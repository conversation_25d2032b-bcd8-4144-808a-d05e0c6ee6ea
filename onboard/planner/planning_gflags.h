#ifndef ONBOARD_PLANNER_PLANNING_GFLAGS_H_
#define ONBOARD_PLANNER_PLANNING_GFLAGS_H_

#include <gflags/gflags.h>

namespace planner {

// If true, output the planning debug information. Note that this is the
// "master" switch and setting this to false will disable all the planning debug
// information, even if when other flags, like --enable_solver_debug, are set
// to true.
DECLARE_bool(enable_planning_debug);

// If true, in the first frame, the planner will use the seed to recover the
// runtime. When set true, the simulation will always run in sim_aligned_mode.
DECLARE_bool(planning_enable_first_frame_seed);

// If true, in all frames, the planner will use the seed to recover the runtime.
// When set true, the simulation will always run in sim_aligned_mode.
DECLARE_bool(planning_enable_all_frame_seed);

// If true, during the warmup stage, the planner will use the seed to recover
// the runtime. When set true, the simulation will always run in
// sim_aligned_mode.
DECLARE_bool(planning_enable_warmup_frame_seed);

// If true, load planner state for the first frame.
// TODO(regisdu), remove this flag. This flag is only for developing, and
// ultimately we'll use seed flag to recover the state.
DECLARE_bool(planning_enable_first_frame_state);

// If true, load planner state for all frames.
// TODO(regisdu), remove this flag. This flag is only for developing, and
// ultimately we'll use seed flag to recover the state.
DECLARE_bool(planning_enable_all_frame_state);

// If true, planner state will be dumped to bag in a set interval.
DECLARE_bool(planning_enable_state_dump);

// If true, load planner state for warmup frames.
// TODO(regisdu), remove this flag. This flag is only for developing, and
// ultimately we'll use seed flag to recover the state.
DECLARE_bool(planning_enable_warmup_frame_state);

// If true, always use byd to load vehicle model.
DECLARE_bool(planning_enable_override_car_type_in_sim);

// If true, the planner will generate route solution json file.
DECLARE_bool(planning_enable_write_route_solution);

// If true, the planner will use gap analysis for lane change. Set it
// if enable gap analysis.
DECLARE_bool(planning_enable_gap_based_lane_change);

// If true, the planner will enable lane boundary violation.
DECLARE_bool(planning_enable_lane_boundary_violation);

// If true, disengagement zone feature will be enabled.
DECLARE_bool(planning_enable_disengagement_zone);

// If true, S-T based traffic light reasoner feature will be enabled.
DECLARE_bool(planning_enable_st_based_traffic_light_reasoner);

// If true, congestion signal will be enabled in lane blockage detector.
DECLARE_bool(planning_enable_congestion_signal);

// If true, only compute overlaps for certain predicted trajectories.
DECLARE_bool(planning_enable_overlap_computation_policy);

// This flag is only supposed to be true for test purpose for now.
// Since the car speed limit is 120 km/h, for non-test cases we limit
// the speed to 60 km/h.
DECLARE_bool(planning_enable_driving_on_freeway);

// If its value is greater than zero, will trigger elc at simulated time.
DECLARE_double(planning_elc_triggered_simulated_time_in_sec);

// Defines the lane change direction of a simulated ELC. 0: either direction, 1:
// left, 2: right.
DECLARE_int32(planning_elc_triggered_simulated_direction);

// When non-negative, overrides the generic urgency score in the lane change
// module.
DECLARE_double(planning_lane_change_override_urgency_score);

// If true, the lane change abort response is enable for the mlc state
// transition.
DECLARE_bool(planning_enable_lane_change_abort_response);

// If the encroachment dist over the cross lane curve is greater is greater than
// the value, will trigger lane change abort automatically. The flag only should
// be only used in the simulator.
DECLARE_double(planning_trigger_lane_change_abort_by_encroachment_in_meter);

// If true, the manual lane change command is enable for the mlc state
// transition.
DECLARE_bool(planning_enable_manual_lane_change_command);

// If true, the lane change finish condition would consider interaction finish
// score.
DECLARE_bool(planning_enable_lane_change_interaction_finish_score);

// If true, invokes the detection of lane change blocking traffic.
DECLARE_bool(planning_enable_near_lane_change_end_blocking_traffic_detector);

// If true, invokes the reroute proposal for consecutive lane change sequence
// end to give up subsequent lane changes early and avoid speed loss.
DECLARE_bool(planning_enable_reroute_proposal_near_trimmed_lane_seq_end);

// If true, invokes the dry_steering_target_angle and angle_rate interface to
// enable the dry steering mpc controller.
DECLARE_bool(planning_enable_dry_steering_controller);

// If true, invokes the reroute proposal for rear end hazard due to EOL during
// lane change abort.
DECLARE_bool(
    planning_enable_reroute_proposal_rear_end_hazard_due_to_eol_during_abort);

// If true, invokes the lane change backup sequence proposal to latch sequence
// during lane change abort.
DECLARE_bool(planning_enable_lane_change_backup_sequence_proposal);

// If true, attempts to address the "LC_BACKUP" proposal with lane sequences
// restored from seed if normal search fails.
DECLARE_bool(planning_enable_addressing_lane_change_backup_proposal_from_seed);

// If true, elective lane change road exit risk avoidance will be enabled.
DECLARE_bool(planning_enable_elective_lane_change_road_exit_risk_avoidance);

// If true, elective lane change U turn risk avoidance will be enabled.
DECLARE_bool(planning_enable_elective_lane_change_u_turn_risk_avoidance);

// If true, elective lane change junction in right side risk avoidance will be
// enabled.
DECLARE_bool(
    planning_enable_elective_lane_change_junction_right_side_risk_avoidance);

// If true, elective lane change neighbor lane stm risk avoidance will be
// enabled.
DECLARE_bool(planning_enable_elective_lane_change_neighbor_lane_stm);

// If true, elective lane change risk directive slow cut-in risk avoidance will
// be enabled.
DECLARE_bool(planning_enable_elc_risk_avoidance_risk_directive_slow_cut_in);

// If true, ELC will get vehicles on lane sequence by occupancy param.
DECLARE_bool(planning_elc_get_vehicles_on_lane_sequence_by_occupancy_param);

// If true, ELC will be triggered considering merge structure.
DECLARE_bool(planning_enable_elc_by_merge_structure);

// If true, ELC will compute remaining distance with the global route on local
// as well.
DECLARE_bool(planning_enable_elc_remaining_distance_with_global_route_on_local);

// If true, the planner will show yield reasoner's conflicting contours with
// conflicting lanes in stream.
DECLARE_bool(planning_display_yield_conflicting_contours);

// If true, the planner will show yield zone contour in stream.
DECLARE_bool(planning_display_yield_zone_contour);

// If true, the planner will show the nudge regions in shape stream.
DECLARE_bool(planning_display_nudge_regions);

// If true, the planner will show inlane nudge corridor in stream.
DECLARE_bool(planning_display_inlane_nudge_corridor);

// If true, the planner will show auxiliary nominal path in stream.
DECLARE_bool(planning_display_auxiliary_nominal_path);

// If true, the planner will show smooth nominal path in stream.
DECLARE_bool(planning_display_ddp_path);

// If true, the planner will show lattice-based nominal path in stream.
DECLARE_bool(planning_display_lattice_nominal_path);

// If true, the planner will show buffer boundary regions in stream.
DECLARE_bool(planning_display_road_buffer);

// If true, the planner will show buffer soft lines in stream.
DECLARE_bool(planning_display_soft_buffer_line);

// If true, the planner will show hard road segments in stream, including
// left_hard_boundary_lines and right_hard_boundary_lines.
DECLARE_bool(planning_display_hard_road_segments);

// If true, the planner will show the optimal gap. This is set to enable
// the gap visualization.
DECLARE_bool(planning_display_gap);

// If true, the planner will show lane change path in stream.
DECLARE_bool(planning_display_ddp_lane_change_path);

// If true, the planner will show the nudge motion boundaries generated from
// nudge motion engine.
DECLARE_bool(planning_display_nudge_motion_boundary);

// If true, the planner will write the waypoint graph to a dot file.
DECLARE_bool(planning_publish_waypoint_graph_dot);

// If true, the planner will add waypoint graph debug info into
// |LaneSequenceGeneratorDebug|.
DECLARE_bool(planning_publish_waypoint_graph_to_debug);

// If true, the planner will publish traffic info map debug.
DECLARE_bool(planning_enable_traffic_info_map_debug);

// If true, the planner will use occlusion map to check if the lane area is not
// occupied.
DECLARE_bool(planner_enable_occlusion_map_for_traffic_info_map);

// If true, the planner will use traffic info map to infer the occlusion area,
// and use traffic flow to infer the temporary vehicle.
DECLARE_bool(planning_enable_traffic_info_map_for_temp_parked);

// If true, the planner will write the regional path graph to a dot file.
DECLARE_bool(planning_publish_regional_path_graph_dot);

// If true, offboard monitor will visualize backup predicted trajectories.
DECLARE_bool(backup_predicted_trajectory_stream);

// If true, the planner will output ST planner debug.
DECLARE_bool(planning_enable_st_planner_debug);

// If true, more debug info would be logged out from the static agent path
// reasoner.
DECLARE_bool(planning_enable_static_agent_path_reasoner_debug);

// If its value is greater than zero, will override all cars' speed limit with
// it.
DECLARE_double(planning_override_car_speed_limit_mps);

// If true, display the drivable space through shape stream.
DECLARE_bool(planning_display_drivable_space);

// Sets if allow to perform forward simulation in lane change crawl.
DECLARE_bool(planning_perform_forward_simulation_in_crawl);

// Sets if state lattice spiral candidates debugging information should be used
// for debug.
DECLARE_bool(planning_enable_lattice_spirals_debug);

// Sets if search based agent intention generator should be used to generate
// the ego intention results.
DECLARE_bool(planning_enable_agent_intention_searcher);

// Sets if we want to enable decoupled maneuvers.
DECLARE_bool(planning_enable_decoupled_maneuvers);

// If true, invoke early lane change reasoning (without executing).
DECLARE_bool(planning_enable_early_lane_change_reasoning);

// If true, invoke early lane change on highway.
DECLARE_bool(planning_enable_early_lane_change_on_highway);

// If true, execute the early lane change.
DECLARE_bool(planning_enable_early_lane_change_execution);

// If true, execute the early lane change for A/B test.
DECLARE_bool(planning_enable_early_lane_change_execution_ab_test);

// If true, enable the lane change creep.
DECLARE_bool(planning_enable_lane_change_creep);

// If true, invoke lane change high risk detector.
DECLARE_bool(planning_enable_lane_change_high_risk_detector);

// If true, invoke lane change forbid ra intervention requirement.
DECLARE_bool(planning_enable_lc_forbid_ra_intervention_requirement);

// If true, use can_reroute flag in route preview for lane change high risk
// detector.
DECLARE_bool(planning_enable_use_can_reroute_flag_for_lc_high_risk);

// If true, use can_reroute flag in route preview for consecutive lane changes.
DECLARE_bool(planning_enable_use_can_reroute_flag_for_cons_lc);

// If true, use can_reroute flag in route preview for lane change stuck.
DECLARE_bool(planning_enable_use_can_reroute_flag_for_lc_blocking_traffic);

// Sets if we want to produce stuck signal.
DECLARE_bool(planning_produce_stuck_signal);

// Sets if we want to enable multi lane sequence candidates in decoupled
// maneuvers.
DECLARE_bool(planning_enable_multi_lane_follow_lane_sequence);

// Sets if we use last path in selection.
DECLARE_bool(planning_enable_last_path);

// Sets if we filter the speed/accel error check when constructing the last
// profile for path reasoning.
DECLARE_bool(planning_enable_path_reasoning_last_speed_profile);

// Sets if we disable exception_handler eb in hybrid mode.
DECLARE_bool(planning_disable_exception_handler_eb_in_hybrid);

// Sets if we enable exception_handler eb on road.
DECLARE_bool(planning_enable_exception_handler_eb_on_road);

// Sets if we enable exception_handler eb in simulation.
DECLARE_bool(planning_enable_exception_handler_eb_in_sim);

// Sets if we disable exception_handler eb in remote assist.
DECLARE_bool(planning_disable_exception_handler_eb_in_RA);

// Sets if we disable exception_handler eb in mrc.
DECLARE_bool(planning_disable_exception_handler_in_MRC);

// Sets if we enable exception_handler swerve in eb.
DECLARE_bool(planning_enable_exception_handler_eb_swerve);

// Sets if we enable ignore ego prediction for veh in exception_handler.
DECLARE_bool(planning_enable_exception_ignore_ego_prediction_veh);

// Sets if we enable ignore ego prediction for VRU in exception_handler.
DECLARE_bool(planning_enable_exception_ignore_ego_prediction_VRU);

// Sets if we enable exception_handler emergency lane keep generation.
DECLARE_bool(planning_enable_exception_handler_emergency_lane_keep);

// Sets if we enable exception_handler emergency lane keep onboard.
DECLARE_bool(planning_enable_exception_handler_elk_onboard);

// Sets if we enable exception_handler tailgater protection generation.
DECLARE_bool(planning_enable_exception_handler_tailgater_protection);

// Sets if we only enable exception_handler tailgater protection speed only
// trajectory generation.
DECLARE_bool(planning_enable_exception_handler_tailgater_protection_speed_only);

// Sets if we enable exception_handler tailgater protection trajectory from ES.
DECLARE_bool(planning_enable_exception_handler_tailgater_protection_from_es);

// Sets if we enable exception_handler tailgater protection swerve onboard.
// False = shadow mode.
DECLARE_bool(
    planning_enable_exception_handler_tailgater_protection_swerve_onboard);

// Sets if we enable exception_handler ebs trajectory from ES.
DECLARE_bool(planning_enable_exception_handler_ebs_from_es);

// Sets if we want to select choosen trajectory index for debug.
DECLARE_int64(planning_exception_handler_elk_select_trajectory_idx);

// Sets if we allow to enable exception_handler es with risk with ttc buffer.
DECLARE_bool(planning_enable_exception_handler_es_allow_risk_buffer);

// Sets if we enable exception_handler emergency lane keep use geometry from the
// provider in exception_handler.
DECLARE_bool(planning_enable_exception_handler_elk_provide_all_geometry);

// Sets if we enable exception_handler use lane keep sequence from eh geometry
// provider as reference.
DECLARE_bool(planning_enable_exception_handler_use_eh_lk_sequence_as_reference);

// Sets if we enable exception handler free space geometry generate corridor.
DECLARE_bool(planning_enable_exception_handler_free_space_geometry_corridor);

// Sets if we enable extended xlane tailgater detection for exception_handler
// ELK.
DECLARE_bool(planning_enable_exception_handler_elk_xlane_tailgater_extend);

// Sets if we enable ELK in multi lane keep sequence scenario for
// exception_handler
DECLARE_bool(planning_enable_exception_handler_elk_in_multi_lane_keep_sequence);

// Sets if we enable exception_handler eb for cz collision.
DECLARE_bool(planning_enable_exception_handler_eb_for_cz_collision);

// Sets if we enable exception_handler eb for curb collision.
DECLARE_bool(planning_enable_exception_handler_eb_for_curb_collision);

// Sets if we enable exception_handler cautious driving.
DECLARE_bool(planning_enable_exception_handler_cautious_driving);

// Sets if we enable exception_handler cautious driving for control drift.
DECLARE_bool(planning_enable_exception_handler_cautious_driving_control_drift);

// Sets if we enable exception_handler cautious driving for remote speed limit.
DECLARE_bool(
    planning_enable_exception_handler_cautious_driving_remote_speed_limit);

// Sets if we enable exception_handler cautious driving for sensor abnormality.
DECLARE_bool(
    planning_enable_exception_handler_cautious_driving_sensor_abnormality);

// Sets if we enable exception_handler cautious driving for eh warning.
DECLARE_bool(planning_enable_exception_handler_cautious_driving_eh_warning);

// Sets if we enable exception_handler eb in control drift.
DECLARE_bool(planning_enable_exception_handler_eb_for_control_drift);

// Sets if we enable exception_handler eb in localization critical drift.
DECLARE_bool(
    planning_enable_exception_handler_eb_for_localization_critical_drift);

// Sets if we enable exception_handler eb for obstacle in sim.
DECLARE_bool(planning_enable_exception_handler_eb_for_obstacle_in_sim);

// Sets if we want to output debug info of object_id.
DECLARE_int64(planning_enable_exception_handler_debug_obj_id);

// Sets if we enable exception_handler eb in shadow mode.
DECLARE_bool(planning_enable_exception_handler_shadow_eb_in_sim);

// Sets if we enable exception_handler eb for early publish.
DECLARE_bool(planning_enable_exception_handler_eb_for_early_publish);

// Sets if we enable exception_handler eb for unknown object.
DECLARE_bool(planning_enable_exception_handler_eb_for_unknown);

// Sets if we enable exception_handler eb for emergency object.
DECLARE_bool(planning_enable_exception_handler_eb_for_emergency_object);

// Sets if we enable exception_handler eb for anomaly detection.
DECLARE_bool(planning_enable_exception_handler_eb_for_anomaly_detection);

// Sets if we enable exception_handler eb for combine_all.
DECLARE_bool(planning_enable_exception_handler_eb_for_combine_all);

// Sets if we enable exception_handler eb for camera.
DECLARE_bool(planning_enable_exception_handler_eb_for_camera);

// Sets if we enable exception_handler taligator risk margin to invoke EB even
// if there is a taligator.
DECLARE_bool(planning_enable_exception_handler_taligator_risk_margin);

// Sets if we enable exception_handler soft brake to avoid tail-end collision.
DECLARE_bool(planning_enable_exception_handler_soft_brake_for_taligator_risk);

// Sets if we enable exception_handler for sustained object.
DECLARE_bool(planning_enable_exception_handler_for_sustained_object);

// Sets if we enable exception_handler for irregular object.
DECLARE_bool(planning_enable_exception_handler_for_irregular_object);

// Sets if we enable exception_handler for unknown animal.
DECLARE_bool(planning_enable_exception_handler_for_unknown_animal);

// Sets if we enable exception_handler eb for vehicle obstacle.
DECLARE_bool(planning_enable_exception_handler_eb_for_vehicle_obstacle);

// Set if we enable exception_handler for merge zone.
DECLARE_bool(planning_enable_exception_handler_in_merge_zone);

// Set if we enable exception_handler elk selection.
DECLARE_bool(planning_enable_exception_handler_elk_selection);

// Set if we enable exception_handler elk selection cost sort .
DECLARE_bool(planning_enable_exception_handler_elk_selection_cost);

// Set if we enable tailgator for unknown fp.
DECLARE_bool(planning_enable_exception_handler_tailgator_for_unknown_fp);

// If true, enable tailgator for dominant object.
DECLARE_bool(planning_enable_eh_tailgator_for_dominant_object);

// If true, enable tailgator for fp construction zone.
DECLARE_bool(planning_enable_eh_tailgator_for_fp_ctzone);

// If true, enable tailgator for all tail collision.
DECLARE_bool(planning_enable_exception_handler_tailgator_for_tail_collision);

// Set if we enable elk trajectory will generate whatever condition.
DECLARE_bool(planning_enable_exception_handler_elk_trajectory_force_generation);

// Set if we enable exception_handler elk target object check .
DECLARE_bool(planning_enable_exception_handler_elk_target_object_check);

// Set if we enable exception_handler target object check .
DECLARE_bool(planning_enable_exception_handler_target_object_check);

// If true, enable control expect extra buffer for exception handler.
DECLARE_bool(planning_enable_control_expect_extra_buffer);

// If true, the selection will dump data-driven feature in ros bag.
DECLARE_bool(planning_enable_selectnet_sample_generation);

// If true, the selection will enable human likeness cost.
DECLARE_bool(planning_enable_selection_human_likeness_cost);

// If true, disable human likeness model when ego slow moving.
DECLARE_bool(planning_disable_selection_human_likeness_in_slow_moving);

// If true, enable human likeness model merge similar candidates.
DECLARE_bool(planning_enable_selection_human_likeness_merge_similar_candidates);

// If true, the selection will enable human likeness cost compare mode.
DECLARE_bool(planning_enable_selection_human_likeness_compare_mode);

// If true, enable lane seq flicker cost in forklane scenario in selection.
DECLARE_bool(planning_enable_lane_seq_flicker_cost_in_forklane);

// If true, the selection will use human likeness embedding model.
DECLARE_bool(planning_enable_human_likeness_embedding_model);

DECLARE_bool(planning_enable_selection_calibrated_road_precedence);

DECLARE_bool(planning_enable_selection_risk_ar_on_non_constraint_bp);

DECLARE_bool(planning_enable_selection_risk_sync_speed_bp_truncation);

DECLARE_bool(planning_enable_selection_risk_unknown_discount);

// If true, we will use a continuous double severity value instead of a concrete
// int severity level to evaluate the collsion severity in selection risk
// solver.
DECLARE_bool(planning_enable_selection_risk_continuous_severity);

DECLARE_bool(planning_enable_selection_multi_bp_risk);

DECLARE_bool(planning_enable_selection_derivative_risk);

DECLARE_bool(planning_enable_selection_worst_case_bp_risk_for_speed_cautious);

DECLARE_bool(planning_enable_selection_tailgater_extra_check);

DECLARE_bool(planning_enable_selection_small_fov_cyc_extra_check);

DECLARE_bool(planning_disable_selection_specialized_risk_against_lc_tail);

DECLARE_bool(planning_enable_calculate_dash_lane_marking_cost);

// If true, enable last lane change point interface
DECLARE_bool(planning_enable_fork_selection_last_lc_point);

// If true, enable use routing_cost_v2 in fork lane scenario selection.
DECLARE_bool(planning_enable_lane_selection_routing_cost_v2);

// If true, enable lane change difficulty interface in selection.
DECLARE_bool(planning_enable_fork_selection_lc_difficulty);

// If true, enable fork sequence diversity prune in high density scenario.
DECLARE_bool(planning_enable_fork_prune_by_agent);

// If true, record rt_event for lane selection scenario.
DECLARE_bool(planning_enable_lane_selection_rt_event);

// Sets if we use warm start speed profile for all candidates including the last
// path candidate.
DECLARE_bool(planning_enable_warm_start_speed_profile_for_all_candidates);

// Sets if we want to enable occlusion grid debug output in decoupled maneuvers.
DECLARE_bool(planning_enable_occlusion_grid_debug_output);

// Sets if we want to enable risk mitigation for occlusion around exit zones.
DECLARE_bool(planning_enable_risk_mitigation_for_occlusion_exit_zone);

// Sets if we want to enable invited yielding for crosswalks at unsignalized
// junction.
DECLARE_bool(
    planning_enable_invited_yield_for_occlusion_crosswalk_unsignalized_junction);

// Sets if we want to generate hallucinated agent for signalized junctions.
DECLARE_bool(planning_enable_hallucinated_agent_for_signalized_junction);

// Sets if we want to enable invited yielding in junction.
DECLARE_bool(planning_enable_invited_yield_for_occlusion_junction);

// Sets if we want to enable invited yielding in u-turn.
DECLARE_bool(planning_enable_invited_yield_for_u_turn);

// Sets if we want to enable occlusion cautious driving strategy for
// higher-precedence crosswalks in non-right-turn scenarios.
DECLARE_bool(planning_enable_occlusion_higher_precedence_crosswalk);

// Sets if we want to enable occlusion cautious driving strategy for
// lower-precedence crosswalks for extreme occlusion.
DECLARE_bool(
    planning_enable_occlusion_lower_precedence_crosswalk_extreme_occlusion);

// Sets if we want to enable occlusion point clustering in the occlusion map.
DECLARE_bool(planning_enable_occlusion_grid_clustering);

// Sets if we want to enable honk reasoner in decoupled maneuvers.
DECLARE_bool(planning_enable_honk_reasoner);

// Sets if should smoother the lane follow center line.
DECLARE_bool(planning_enable_lane_follow_center_line_smoother);

// If true, use cost engine for edge cost estimation in planning.
DECLARE_bool(planning_enable_cost_engine);

// If true, the planner will enable engage maneuver in simulation.
DECLARE_bool(planning_enable_engage_maneuver_sim);

// If true, the planner will execute semantic map inference.
DECLARE_bool(planning_enable_semantic_map);

// If true, the planner will output semantic map debug image to local folder.
DECLARE_bool(planning_enable_semantic_map_output_debug);

// If true, the planner will load routing constraint from topic rather than
// config.
DECLARE_bool(planning_load_routing_constraint_from_topic);

// If true, enable speed optimization in decouple planner.
DECLARE_bool(decouple_planning_enable_speed_opt);

// If true, enable the continuous cost function for proximity speed constraint
// in speed optimization.
DECLARE_bool(planning_enable_continuous_proximity_speed_in_speed_opt);

// If true, enable last profile attraction in speed optimization.
DECLARE_bool(planning_enable_last_profile_attraction_in_speed_opt);

// If true, enable the augmented lagrangian iLqr in the speed optimization.
DECLARE_bool(planning_enable_alilqr_in_speed_opt);

// True if enable Soften StaticRange For Comfort in speed optimization.
DECLARE_bool(planning_enable_soften_static_range_for_comfort);

// True if enable position upper bound attraction in speed optimization.
DECLARE_bool(planning_enable_x_upper_bound_attraction_in_speed_opt);

// True if enable position upper bound attraction in speed optimization.
DECLARE_bool(planning_enable_x_upper_bound_attraction_in_fast_acceleration);

// If true, the planner will show predicted ego bounding boxes in engage
// maneuver.
DECLARE_bool(planning_display_engage_predicted_ego_bounding_box);

// If true, DCHECK no path for decoupled planner.
DECLARE_bool(decoupled_planning_enable_no_path_dcheck);

// If true, the planner will allow to request cells from cloud.
DECLARE_bool(planning_enable_cloud_cell_request);

// If true, generate one sequence for each extra drivable fork lane.
DECLARE_bool(planning_enable_multiple_alternative_lane_follow_sequences);

// If true, we will only pass the ml backup path to the speed pipeline when it
// is available in lane_keep bahavior.
DECLARE_bool(planning_enable_forcing_selecting_ml_path);

// If true, we will add at most 6 ml backup path options.
DECLARE_bool(planning_enable_add_6_ml_path);

// If true, and |planning_enable_forcing_selecting_ml_path| is true, we will
// only pass the ml backup path to the speed pipeline when it is available in
// lane_keep and corss_lane bahavior.
DECLARE_bool(planning_enable_forcing_selecting_ml_path_with_cross_lane);

// If true, we will genereate ml path option without filtering.
DECLARE_bool(planning_disable_ml_path_filter);

// If true, we will enable ML 2.0 in a very large scope, excluding XLN, LC,
// PIPO, MRC etc.
DECLARE_bool(planning_enable_ml_path_2_0);

// If true, we will add another path option for the trajectory selection that
// skips the path search and use ml planner output as the reference line. It is
// ML 2.0.
DECLARE_bool(planning_add_ml_path);

// If true, enable ML 2.0 in all upl turns.
DECLARE_bool(planning_add_ml_path_in_upl_turn);

// If true, passes ML planner output trajectory (i.e. ML 3.0) as an additional
// diversity to selection.
DECLARE_bool(planning_enable_ml_trajectory);

// If true, drop ml path if the extended reference path collides with hard
// boundary.
DECLARE_bool(planning_enable_drop_ml_path_due_to_hb_collision);

// If true, drop ml path when reasoing doesn't provide ignore homotopy.
DECLARE_bool(planning_enable_drop_ml_path_when_no_ignore_homotopy);

// If true, drop ml trajectory when its init state has diverged from planner.
DECLARE_bool(planning_enable_ml_trajectory_init_state_check);

// If true, the similar path options will be dropped before speed pipeline.
DECLARE_bool(planning_enable_path_similarity_deduplicate);

// If true, the long reference path will be trimmed.
DECLARE_bool(planning_enable_reference_path_trimming);

// If true, add a new fallback path option.
DECLARE_bool(planning_enable_fallback_path);

// If true, add a new upl big turn path option.
DECLARE_bool(planning_enable_upl_big_turn_path);

// If true, add a new upl small turn path option.
DECLARE_bool(planning_enable_upl_small_turn_path);

// If true, generate intermediate speed search result into planning debug.
DECLARE_bool(planning_enable_intermediate_speed_search_result_debug);

// If true, generate lane sequences based on cost map.
DECLARE_bool(planning_enable_cost_map_based_lane_sequence_generation);

// If true, cost maps are saved as dot files.
DECLARE_bool(planning_save_cost_map_as_dot_file);

// If true, trigger pull out behavior in decoupled planner.
DECLARE_bool(planning_enable_decoupled_pull_out);

// If true, auto confirm pull out in simulation mode.
DECLARE_bool(planning_pull_out_auto_confirm_in_sim);

// If true, will use post processing logic to update waypoint availability for
// waypoint assist.
DECLARE_bool(planning_should_post_process_waypoint_availability);

// If true, generate ray casting result of semantic corridor into path opt
// debug.
DECLARE_bool(planning_enable_semantic_corridor_ray_casting_result_debug);

// If true, allow lane keep behavior to unstuck by borrowing neighbor lanes.
DECLARE_bool(planning_enable_decoupled_xlane_nudge);

// If true, will enable xlane nudge for dynamic agent v2.
DECLARE_bool(planning_enable_xlane_nudge_dynamic_agent_v2);

// If true, will enable to generate a pure xlane nudge homotopy.
DECLARE_bool(planning_enable_extra_pure_xlane_nudge_homotopy);

// If true, will enable xlane nudge to do a larger lateral movement.
DECLARE_bool(planner_enable_larger_lateral_xlane_nudge);

// If true we could actively reroute when current regional path is not safe to
// execute anymore.
DECLARE_bool(planning_enable_active_reroute_in_regional_path_generation);

// If true, we can produce non gap align and gap align solutions for LC at the
// same time.
DECLARE_bool(
    planning_speed_solver_output_non_gap_align_solution_for_lane_change);

// If true, we can produce extra diversity for lane change gap align.
DECLARE_bool(planning_enable_lane_change_gap_align_diversity);

// If true, we can apply gap align urgency score to all lane change modules.
DECLARE_bool(planning_enable_gap_align_urgency_score_for_lane_change);

// If true, we can produce one more extra diversity.
DECLARE_bool(planning_enable_extra_speed_diversity);

// If true, we can produce risk signal.
DECLARE_bool(planning_enable_risk_signal);

// If true, decoupled reverse driving mode will be invoked.
// TODO(Zekixu): Retire this flag when this feature is ready for release.
DECLARE_bool(planning_enable_decoupled_reverse_driving);

// If true, the trajectory guider will provide path diversity in the turns.
DECLARE_bool(planning_enable_trajectory_guider_diversity);
// If true, the trajectory guider will generate speed limiter.
DECLARE_bool(planning_enable_trajectory_guider_limiter);

// If true, planner will use a profile that tends to maintain the current
// speed and passively reacts to other agents as one of the candidates in
// the conflict resolver.
DECLARE_bool(planning_enable_const_speed_for_conflict_resolver);

// If true, planner will enable scr seach to find a conflict resolver candidate
// that tends to pass the closer unavoidable conflicting object and yield to a
// farther agent.
DECLARE_bool(planning_enable_scr_search_for_conflict_resolver);

// If true, planner will use a profile that drives as fast as possible
// at discomfort 1 as one of the candidates in the conflict resolver.
DECLARE_bool(planning_enable_max_speed_for_conflict_resolver);

// If true, planner will produce emergency brake signal when necessary.
DECLARE_bool(planning_enable_emergency_brake);

// If true, planner will try to brake harder than FULL_STOP but not as
// hard as emergency brake.
DECLARE_bool(planning_enable_extra_full_stop);

// If true, will evaluate the risk using the continuous severity value instead
// of the concrete severity value. Should use with
// planning_enable_new_severity_in_conflict_resolver
DECLARE_bool(planning_enable_continuous_severity_in_conflict_resolver);

// If true, planner speed will search from discomfort -1 to 0.
DECLARE_bool(planning_enable_extra_low_discomfort_speed_search);

// If true, the road speed limiter will break limit speed.
DECLARE_bool(planning_enable_relax_speed_road_limiter);

// If true, the estimated traffic flow data in occluded area from perception
// will be used to estimate congestion information around ego.
DECLARE_bool(planning_enable_integrate_occluded_in_lane_congestion_detector);

// If true, enable the search-based post-process for road boundaries.
DECLARE_bool(planning_enable_road_boundaries_post_process);

// If true, enable the lane sequence generator to relax the constraint for stuck
// avoidance.
DECLARE_bool(planning_enable_relax_lane_change_constraint_to_avoid_stuck);

// If true, enable the lane sequence generator to cross road lane change
// when enable relax lane change constraint to avoid stuck.
DECLARE_bool(planning_enable_cross_road_in_relax_lane_change_constraint);

// If true, enable the lane sequence generator to generate a jump out lane
// sequence for stuck avoidance.
DECLARE_bool(planning_enable_jump_out_to_avoid_stuck);

// If true, compute and use compensated overlap arc lengths
// in the new decoupled architecture.
DECLARE_bool(planning_enable_compensated_overlap_arc_lengths);

// If true, enable steering accel limiter that will apply speed limits
// to reduce high steering acceleration.
DECLARE_bool(planning_enable_steering_accel_limiter);

// If true, shift agent reaction may be assigned in prediction decision maker.
DECLARE_bool(planning_enable_shift_agent_reaction);

// If true, we may allow discomfort for progress in upl.
DECLARE_bool(planning_enable_discomfort_for_progress_in_upl);

// If true, globally enable agent predicted trajectory route association in
// decoupled maneuver.
DECLARE_bool(planning_enable_agent_predicted_trajectory_route_association);

// If true, globally enable agent predicted trajectory route association for
// agents considered by speed pipeline in the last cycle.
DECLARE_bool(
    planning_enable_agent_predicted_trajectory_route_association_considered_by_speed);

// If true, enable debug of old arch maneuvers when using decoupled trajectory.
DECLARE_bool(planning_enable_old_arch_debug_within_decoupled);

// If true, the planning will enable ego stuck estimation when
// the ego being in low speed and AUTO mode.
DECLARE_bool(planning_enable_ego_stuck_estimation);

// If true, max speed barrier from reference profile will be separate to max
// speed barrier from vehicle limits, max speed repeller from reference and
// lateral jerk repeller to penalize lat jerk.
DECLARE_bool(planning_enable_separate_max_speed_barrier);

// If true, lateral accel will be penalized in the speed optimization.
DECLARE_bool(planning_enable_lateral_accel_effort_cost_in_speed_opt);

// If true, enable risky costs in regional path generation.
DECLARE_bool(planning_enable_regional_path_risky_cost);

// If true, enable deciding signal controlled variable lane's drivability.
DECLARE_bool(planning_enable_signal_controlled_variable_lane);

// If true, enable extending regional path from global route.
DECLARE_bool(planning_enable_extended_regional_path);

// If true, enable add extra cost to neighbor lane near stm vehicle.
DECLARE_bool(planning_enable_add_cost_to_stm_vehicle);

// If true, enable speed search in path reasoning.
DECLARE_bool(planning_enable_speed_search_in_path_reasoning);

// If true, populate debug info of speed search in path reasoning.
DECLARE_bool(planning_populate_debug_of_speed_search_in_path_reasoning);

// If true, enable last frame speed cautious when we estimate final profile in
// s-t planner.
DECLARE_bool(planning_enable_last_frame_speed_cautious_in_path_reasoning);

// If true, enable last frame avoid region when we estimate final profile in s-t
// planner.
DECLARE_bool(planning_enable_last_frame_avoid_region_in_path_reasoning);

// If true, enable the tree search for lane change by default.
DECLARE_bool(planning_enable_lane_change_tree_search);

// If true, enable decreasing max speed limit to 50 km/h at night time.
// Otherwise, the max speed limit at night time will be 60 km/h, which is the
// same as day time.
DECLARE_bool(planning_enable_daytime_speed_limit);

// If true, only soft constraint will be added for better_not_drive object.
DECLARE_bool(planning_enable_use_soft_constraint_for_better_not_drive);

// If true, enable filtering occupancy that is far above the ego.
DECLARE_bool(planning_enable_filtering_out_occupancy_above_ego);

// If true, enable the logic of using the ML guidance corridor in uturn.
DECLARE_bool(planning_enable_ml_guidance_corridor_in_uturn);

// If true, enable using soft and strict constraint during lane change.
DECLARE_bool(planning_enable_soft_strict_constraint_during_lane_change);

// If true, enable the usage of nudge info provided by searched path.
DECLARE_bool(planning_enable_search_result_nudge_info);

// If true, enable ObjectOccupancyStateMap usage instead of AgentInLaneState in
// path reasoning.
DECLARE_bool(planning_enable_object_occupancy_state);

// If true, will set waypoint assist unavailable for end of lane sequence
// scenario.
DECLARE_bool(planning_enable_disable_waypoint_assist_usage_for_end_of_laneseq);

// If true, will set light assist detour unavailable for pull out.
DECLARE_bool(planning_disable_light_assist_detour_usage_for_pull_out);

// If true, will set light assist detour unavailable for waypoint assist.
DECLARE_bool(planning_disable_light_assist_detour_usage_for_waypoint_assit);

// If true, will enable waypoint assist recommend replan.
DECLARE_bool(planning_enable_waypoint_assist_recommend_replan);

// If true, add cautious speed limits for junctions.
DECLARE_bool(planning_enable_junction_speed_limits);

// If true, enable backward direction key to trigger unstuck behavior.
DECLARE_bool(planning_enable_backward_direction_key_unstuck_behavior);

// If true, enable free range usage in path search sample arc lengths
// generation.
DECLARE_bool(planning_enable_analyzing_free_range);

// If true, the agent in lane state will not be written into lane sequence info.
DECLARE_bool(planning_deprecate_agent_in_lane_state);

// If true, enable request new lane keep sequence proposal to switch lane
// sequence.
DECLARE_bool(planning_enable_generate_neighbor_lane_sequence_proposal);

// If true, enable pullover to use stop cell in road exit.
DECLARE_bool(planning_enable_road_exit_cell);

// If true, enable data-driven BVP Hint for state lattice search.
DECLARE_bool(planning_enable_data_driven_bvp_hint);

// If true, enable to publish stuck scene request.
DECLARE_bool(planning_enable_publish_stuck_scene_request);

// If true, enable kinematic pruning for state lattice search.
DECLARE_bool(planning_enable_kinematic_pruning);

// If true, enable to restore regional path from seed.
DECLARE_bool(planning_enable_restore_regional_path_from_seed);

// If true, enable to allow global route lanes to be rejected in planner.
DECLARE_bool(planning_enable_allow_global_route_lanes_rejected);

// If true, enable overlap v2.
DECLARE_bool(planning_enable_overlap_v2);

// If true, add repulsions for start-to-move agents when nudging.
DECLARE_bool(planning_enable_start_to_move_repulsion);

// If true, set lateral bound for stm repulsion to prevent HS.
DECLARE_bool(planning_lateral_bound_for_stm_repulsion);

// If true, add repulsions for imaginary agents.
DECLARE_bool(planning_enable_imaginary_agent_repulsion);

// If true, smooth the stm repulsion.
DECLARE_bool(planning_enable_smooth_stm_repulsion);

// If true, add repulsions for rear-end agents.
DECLARE_bool(planning_enable_rear_end_agent_repulsion);

// If true, when adding rear-end repulsions, we consider road precedence by
// curvature info.
DECLARE_bool(planning_enable_curvature_based_road_precedence);

// If true, add repulsion for stuck region during xlane nudge.
DECLARE_bool(planning_enable_xlane_nudge_region_repulsion);

// If true, add repulsion for interested agent during crawl。
DECLARE_bool(planning_enable_crawl_interested_agent_repulsion);

// If true, add repulsion for ped child.
DECLARE_bool(planning_enable_child_ped_repulsion);

// If true, add repulsions for low-likelihood cut-in traj.
DECLARE_bool(planning_enable_multi_traj_cut_in_repulsion);

// If true, add repulsion for faked cut-in traj.
DECLARE_bool(planning_enable_faked_cut_in_traj_repulsion);

// If ture, allow extend boundary for cut-in repulsions.
DECLARE_bool(planning_enable_boundary_extension_for_cut_in_repulsion);

// If true, we compute the ignore_if_nudge_failed for cut-in repulsions more
// aggressively.
DECLARE_bool(planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy);

// if true, enable to add repulsion for oncoming cross left turn agent.
DECLARE_bool(planning_enable_cross_left_turn_agent_repulsion);

// If true, enable to drop current snapshot.
DECLARE_bool(planning_enable_drop_current_snapshot);

// If true, enable to drop current path.
DECLARE_bool(planning_enable_drop_current_path);

// If true, enable path frame drop If the arrival of snapshot input has
// been delayed consecutively for more than 2 frames.
DECLARE_bool(planning_enable_frame_drop_for_consecutive_snapshot_input_delay);

// If true, enable speed frame drop If the arrival of path input has
// been delayed consecutively for more than 2 frames.
DECLARE_bool(planning_enable_frame_drop_for_consecutive_path_input_delay);

// If true, enable to populate stuck signal based on RA instructions.
DECLARE_bool(planning_enable_populate_stuck_signal_based_on_ra_instruction);

// If true, enable gradual regulation of initial state in speed reference
// generator.
DECLARE_bool(planning_enable_gradual_init_state_regulation);

// If true, enable static agent decision branching in tunnel search.
DECLARE_bool(planning_enable_static_agent_decision_branching);

// If true, enable lane encroach nudge for dangerous scene.
DECLARE_bool(planning_enable_lane_encroach_nudge);

// If true, enable lane encroach nudge at high speed.
DECLARE_bool(planning_enable_lane_encroach_nudge_at_high_speed);

// If true, enable the ttc computation used to prevent rear front collision.
DECLARE_bool(planning_enable_ttc_prevent_rear_front_collision);

// IF ture, enable soften prediction blocking sequences for all type of yielding
// crossing agents
DECLARE_bool(planner_enable_soften_all_type_of_yielding_crossing);

// If true, enable narrow oncoming process.
DECLARE_bool(planning_enable_narrow_meeting_process);

// If true, enable strong repulsion in narrow meeting.
DECLARE_bool(planning_enable_strong_repulsion_in_narrow_meeting);

// If true, enable align st buffer in path pipeline.
DECLARE_bool(planning_enable_align_st_buffer_in_path_pipeline);

// If true, enable cyclist overtaking repulsion in WAYPOINT behavior.
DECLARE_bool(planning_enable_waypoint_cyclist_overtaking_repulsion);

// If true, enable the estimation of lateral effort in path reasoning.
DECLARE_bool(planning_enable_path_reasoning_lateral_estimation);

// If true, enable the visual of lateral_estimation in st planner.
DECLARE_bool(planning_enable_lateral_estimation_visual);

// If true, enable generating lane sequences addressing the proposals from lane
// change.
DECLARE_bool(planning_enable_lane_sequence_proposal_for_lane_change);

// If true, enable check reference point incremental along the selected lane
// sequence.
DECLARE_bool(planning_enable_waypoint_assist_check_point_incremental);

// If true, enable ego drive on the opposite lanes.
DECLARE_bool(planning_enable_waypoint_assist_drive_on_opposite_lanes);

// If true, enable pull out auto reversing logic.
DECLARE_bool(planning_enable_pull_out_auto_reversing);

// If true, overwrites planning trajectory with MLPlanner trajectory.
// TODO(jonathanchen): Deprecate it if no backward support issues.
DECLARE_bool(planning_enable_mlplanner_trajectory_evaluation);

// If true, planning will not publish trajectory and only publish trajectory
// result.
DECLARE_bool(planning_disable_planner_trajectory);

// If true, enable to use pullover stop cell from cloud.
DECLARE_bool(planning_use_stop_cell_from_cloud);

// If true, enable update lane congestion reasoner in lane blockage detector.
DECLARE_bool(planning_enable_update_lane_congestion_reasoner);

// If true, enable soft nudge related feature.
DECLARE_bool(planning_enable_soft_nudge);

// If true, enable forward direction key to trigger unstuck behavior.
DECLARE_bool(planning_enable_forward_direction_key_unstuck_behavior);

// If true, the constraints for FoD will be migrated in speed pipeline.
DECLARE_bool(planning_enable_migrate_fod_constraints_in_speed_pipeline);

// If true, enable spatial-temporal state lattice search.
DECLARE_bool(planning_enable_spatial_temporal_state_lattice_search);

// If true, planner will not ignore fod and starts to interact with them.
DECLARE_bool(planning_enable_fod_interaction);
// If true, planner will generate multiple fod intentions to better_not_drive
// objects.
DECLARE_bool(
    planning_provide_multiple_fod_intentions_for_better_not_drive_object);
// If true, planner will process hdmap changed data for pnc map and use these
// data for planning.
DECLARE_bool(planning_enable_hdmap_dynamic_data_update);

// If true, planner will process hdmap changed data for pnc map.
// Whether to use these data for planning is controlled by
// planning_enable_hdmap_dynamic_data_update.
DECLARE_bool(planning_enable_shadow_mode_hdmap_dynamic_data_update);

// If true, enable planner respond to mrc_node's info.
DECLARE_bool(planning_enable_planner_receive_mrc_node_info);

// If true, enable planner respond to mrc request on highway.
DECLARE_bool(planning_enable_planner_respond_mrc_request_on_highway);

// If true, selection uses waiting in traffic reasoning to adjust progress cost.
DECLARE_bool(planning_enable_progress_cost_waiting_in_traffic_reasoning);

// If true, selection uses progress_efficiency_cost to replace original
// progress_immediate & stuck_cost
DECLARE_bool(planning_enable_progress_efficiency_cost);

// If true, forward stuck scene analyzer will reason ctz to unstuck.
DECLARE_bool(planning_enable_process_ctz_in_forward_unstuck);

// If true, the junction lane selection will consider ml planner's
// target lane recommendation.
DECLARE_bool(planning_use_ml_planner_in_junction_lane_selection);

// If true, planner will run lateral clearance generator gpu version.
DECLARE_bool(planning_enable_gpu_lateral_clearance_generator);

// If true, enable gaussian model to calculate collision related probability.
DECLARE_bool(planning_enable_gaussian_model_2d);

// If true, enable planner module recommend replan info to routing if want to
// make up cost map of given direction.
DECLARE_bool(planning_enable_recommend_replan_info_to_routing);

// If true, planner will run u-turn unstuck planner and do dry steering and
// reversing to unstuck. Also the yield extra distance to a potential u-turn
// blockage is reduced.
DECLARE_bool(planning_enable_uturn_unstuck);

// If true, enable Kturn replan after reverse stuck.
DECLARE_bool(planning_enable_kturn_replan_after_reverse_stuck);

// If true, planner enables passing through map change area by RA waypoint.
DECLARE_bool(planning_enable_ra_pass_through_map_change_area);

// If true, planner enables passing through perception CZ by RA waypoint.
DECLARE_bool(planning_enable_ra_pass_through_perception_cz);

// If true, RA enable waypoint exit reasoning about within CZ.
DECLARE_bool(planning_enable_ra_waypoint_exit_reasoning_within_cz);

// If true, planner enables passing through map change area about traffic light.
DECLARE_bool(planning_enable_ra_for_map_change_area_about_traffic_light);

// If true, planner enables RA for perception FP to OPS for confirming
// ignore and unstuck.
DECLARE_bool(planning_enable_ra_for_perception_fp_ignoring);

// If true, planner enables trigger RA request about map change area during
// moving.
DECLARE_bool(planning_enable_ra_request_for_map_change_area_by_routing);

// If true, planner will be in planner-init-state-recovery mode in simulation
DECLARE_bool(planning_enable_sim_planner_init_state_recovery_mode);

// If true, planner will stop processing snapshot seed in
// planner-init-state-recovery mode in simulation.
DECLARE_bool(
    planning_disable_snapshot_seed_in_sim_planner_init_state_recovery_mode);

// If true, planner will stop processing planning seed in
// planner-init-state-recovery mode in simulation.
DECLARE_bool(
    planning_disable_planning_seed_in_sim_planner_init_state_recovery_mode);

// If true, allow relax lane direction when stuck. For example, turn right from
// a straight lane when all right turn lane are blocked.
DECLARE_bool(planning_enable_relax_lane_direction_when_stuck);

// If true, planner will populate recommend replan info for regional path
// request to make up cost map in junction.
DECLARE_bool(planning_enable_recommend_replan_for_regional_path_request);

// If true, the assist stuck detector enable checking yielding parked cars near
// road boundary in the FP queuing rule.
DECLARE_bool(planning_enable_assist_stuck_checking_for_parked_cars_near_hb);

// If true, the assist stuck detector use config for recall orient.
DECLARE_bool(planning_enable_assist_stuck_detector_config_for_recall);

// If true, the assist stuck detector use config according to version type.
DECLARE_bool(planning_enable_assist_stuck_detector_config_by_version_type);

// If true, the assist stuck stationary reasoning considers occlusion.
DECLARE_bool(planning_enable_assist_stuck_occlusion_in_stationary_reasoning);

// If true, the assist stuck queuing reasoning considers current lane traffic
// flow.
DECLARE_bool(planning_enable_assist_stuck_traffic_flow_queuing_reasoning);

// If true, the assist stuck enable yield varying tf in fp yielding rule.
DECLARE_bool(planning_enable_assist_stuck_yield_varying_traffic_flow);

// If true, the assist stuck forcing recall strategy uses default trigger cycle.
DECLARE_bool(planning_enable_assist_stuck_forcing_recall_default_trigger_cycle);

// If true, the assist stuck still working in assist mode, but send fake
// request.
DECLARE_bool(planner_enable_assist_stuck_detector_working_in_assist_mode);

// If true, the assist stuck don't response to FP queuing when FN selection is
// activate.
DECLARE_bool(planning_disable_assist_stuck_fp_queuing_when_fn_selection);

// If true, the assist stuck geometric features would be normalized by the ego's
// state.
DECLARE_bool(planning_enable_assist_stuck_geometric_features_normalization);

// If true, the assist stuck adapt dnn features for inference.
DECLARE_bool(planning_enable_assist_stuck_adapt_dnn_features);

// If true, the assist stuck dumps features' tensor dict for training.
DECLARE_bool(planning_enable_assist_stuck_dumps_tensor_dict);

// If true, stuck detector will enable using neighbor lane density to help
// evaluate traffic density cost.
DECLARE_bool(planning_enable_using_neighbor_lane_to_help_evaluate_density_cost);

// If true, enable enlarge lateral gap for certain object when doing xlane
// nudge.
DECLARE_bool(planning_enable_enlarge_lat_gap_for_xlane_nudge);

// If true, enable enlarge lateral gap for certain object when doing lane
// change.
DECLARE_bool(planning_enable_enlarge_lat_gap_for_lane_change);

// If true, enable generating lane sequence via sampling and selection.
DECLARE_bool(planning_enable_lane_sequence_rollouts);

// If true, enable enlarge lateral gap for certain object when doing pullover.
DECLARE_bool(planning_enable_enlarge_lat_gap_for_pull_over);

// If true, enable planner node trigger replan with desired route info when
// optimal regional path is diverged with global route.
DECLARE_bool(planning_enable_replan_for_desired_route);

// If true, the risk model in selection will populate framewise input and output
// at a specified timestamp in the selection debug proto of sim output bag.
DECLARE_bool(planning_save_risk_model_framewise_io);

// The timestamp at which the framewise input and output for selection
// risk model will be populated to sim output bag.
DECLARE_int64(planning_selection_framewise_timestamp_to_save);

// If true, we skip the ignore homotopy. This would only be used when
// simulation.
DECLARE_bool(path_planning_skip_ignore_homotopy);

// If true, enable cone understanding module to reassign blocking attribute for
// cones.
DECLARE_bool(planning_enable_cone_understanding);

// If true, enable remote assist fp pull over about stationary object
DECLARE_bool(
    planning_enable_remote_assist_fp_pull_over_about_stationary_object);

// If true, selection's human likeness cost shall first sort its trajectories
DECLARE_bool(planning_selection_human_likeness_sort_candidate_trajectories);

// If true, use old stuck feature extractor length, 20, for simulation of remote
// assist auto trigger.
// TODO(fengkaijun): Due to the changes made by CR 3733395 to the stuck feature
// extractor, there is an incompatibility problem, and this gflag needs to be
// used during the simulation. Once the compatibility issue is completely
// resolved, the gflag will be removed.
DECLARE_bool(planning_use_old_stuck_feature_extractor_length_only_in_sim);

// If true, fn_selection strategy will be enabled to trigger RA.
DECLARE_bool(planning_enable_fn_selection_in_stuck_detect);

// If true, the scope of allowing EB for VRU is much larger.
DECLARE_bool(planning_enable_enlarging_scope_of_eb_for_vru);

// If true, enable send waypoint assist request to avoid stuck.
DECLARE_bool(planning_enable_waypoint_assist_request_to_avoid_stuck);

// If true, enable generating in-junction alternative lane change sequence for
// single-connected straight turns.
DECLARE_bool(planning_enable_in_junction_alternative_lane_change_sequence);

// If true, enable selection's longitudinal speed diff cost.
DECLARE_bool(planning_enable_selection_longitudinal_speed_diff_cost);

// If true, enable speed cautious driving reasoner to consider remote speed
// limit.
DECLARE_bool(
    planning_enable_speed_cautious_driving_reasoner_remote_speed_limit);

// If true, path planner can run in parallel with speed planner. default false
DECLARE_bool(planning_path_speed_are_parallel);

// If true, copy the previous finished iteration seed to current seed when a
// frame starts
DECLARE_bool(planning_copy_previous_seed);

// If true, copy the SpeedSeed and SelectionSeed from last frame to current
// frame at the beginning of Speed nodelet start.
DECLARE_bool(planning_copy_speed_seeds);

// If true, all read to LastFinished version of seed will actually read from
// current version. Require 'planning_copy_previous_seed' as true as well.
DECLARE_bool(planning_seed_always_read_from_current);

// If true, checks the last available version and complain when trying to
// read a seed version which has not been written.
DECLARE_bool(planning_seed_check_and_complain_read_before_write);

// If true, bag seed is restored only restored when its timestamp matches the
// target seed.
DECLARE_bool(planning_seed_check_timestamp_before_apply);

// If true, enable the search-based post-process for reasoning boundaries.
DECLARE_bool(planning_enable_reasoning_boundaries_post_process);

// If true, enable populating construction zone as creep around object.
DECLARE_bool(planning_enable_populate_cz_as_creep_around_object);

// If true, enable collect empirical road blockage raw data from world model.
DECLARE_bool(planning_enable_collect_empirical_road_blockage_raw_data);

// If true, enable collect empirical fix point unstuck raw data from world
// model.
DECLARE_bool(planning_enable_collect_empirical_fix_point_unstuck_raw_data);

// If true, enable upload empirical raw data to cloud.
DECLARE_bool(planning_enable_upload_empirical_raw_data);

// If true, enable use new global route selection.
DECLARE_bool(planning_enable_new_global_route_selection);

// If true, enables reading map change area's lane keep lane sequence from
// routing.
DECLARE_bool(planning_enable_map_change_lane_keep);

// If true, enable that pull out can be blocked by bus picking up and dropping
// off.
DECLARE_bool(planning_enable_pull_out_blocked_by_bus_pick_up_drop_off);

// If true, enable planner use creep signals to trigger RA.
DECLARE_bool(planning_enable_creep_connect_ra);

// If true, enable planner creep through multiple obstacles.
DECLARE_bool(planning_enable_creep_through_multiple_obstacles);

// If true, enable eh node and disable eh nodelet
DECLARE_bool(planning_enable_eh_node);

// If true, enable lane sequence rollout generate candidates for blockage
// avoidance.
DECLARE_bool(
    planning_enable_rollout_generate_candidates_for_blockage_avoidance);

// If true, disable static defined tbb thread count.
DECLARE_bool(planning_disable_static_tbb_thread_count);

// If true, enable kinematic jump in guidance searcher.
DECLARE_bool(planning_enable_kinematic_jump_in_guidance_search);

// If true, enable kinematic jump in guidance with upstream hint.
DECLARE_bool(planning_enable_kinematic_jump_in_guidance_with_upstream_hint);

// If true, enable kinematic jump in guidance latch.
DECLARE_bool(planning_enable_latching_jump_in_guidance);

// If true, enable kinematic jump in guidance soft latch.
DECLARE_bool(planning_enable_kinematic_jump_in_soft_latching);

// If true, path module can generate higher curvature path to handel cases
// that Ego need emergency swerve. Only consider ped and cyc.
DECLARE_bool(planning_enable_path_emergency_swerve);

// If true, path module can generate higher curvature path to handel cases
// that Ego need emergency swerve for vehicle.
DECLARE_bool(planning_enable_path_emergency_swerve_for_vehicle);

// If true, enable filtering logic in global object manager.
DECLARE_bool(planning_enable_global_object_manager_filtering);

// If true, path search can expand the drivable space boundary in u-turn
// scenarios.
DECLARE_bool(planning_enable_uturn_drivable_space_expand);

// If true, enable reason reference points to avoid stuck.
DECLARE_bool(planning_enable_reason_reference_points_to_avoid_stuck);

// If true, enable use current lane reasoner.
DECLARE_bool(planning_enable_current_lane_reasoner);

// If ture, enable contours for dynamic objects in predicted_poses.
DECLARE_bool(planner_use_contour_for_dynamic_objects);

// If true, enable the curvature diversity in u-turn.
DECLARE_bool(planning_enable_uturn_curvature_diversity);

// If true, enable remote assist fp pull over about order.
DECLARE_bool(planning_enable_remote_assist_fp_pull_over_about_order);

// If true, enable pull over time out abort due to constant stuck in road for
// yielding traffic.
DECLARE_bool(planning_enable_pull_over_time_out_abort);

// If true, enable creeping through narrow passages by reducing pass RLG under
// the critical value.
DECLARE_bool(planning_enable_creep_narrow_passages);

// If true, enable remote assist fp on turn about temp parked cars.
DECLARE_bool(planning_enable_remote_assist_fp_on_turn_about_temp_parked_car);

// If true, enable configuration form cloud for RA stuck detector.
DECLARE_bool(planning_disable_remote_assist_stuck_cloud_config);

// If true, enable congestion api for RA stuck detector.
DECLARE_bool(planning_enable_congestion_api_in_assist_stuck_detector);

// If true, enable cross road lane change.
DECLARE_bool(planning_enable_cross_road_lane_change);

// If true, enable dense lateral clearance debug. Otherwise, a down-sampling
// factor will applied to the debug recording.
DECLARE_bool(planning_enable_dense_lateral_clearance_debug);

// If true, enable physical boundary clearance debug.
DECLARE_bool(planning_enable_physical_boundary_lateral_clearance_debug);

// If true, enable agent lateral clearance debug.
DECLARE_bool(planning_enable_agent_lateral_clearance_debug);

// If true, enable agent lateral clearance generator debug.
DECLARE_bool(planning_enable_agent_lateral_clearance_generator_debug);

// If true, enable SL nudge corridor costing instead of XY nudge corridor
// costing.
DECLARE_bool(planning_enable_sl_nudge_costing);

// If true, use ego's sampled points for sl nudge costing. If false, use ego's
// disks instead.
DECLARE_bool(planning_use_ego_sampled_points_for_sl_nudge_costing);

// If true, enable dense agent clearance extracted data debug. Otherwise, a
// down-sampling factor will applied to the debug recording.
DECLARE_bool(planning_enable_dense_agent_clearance_extracted_data_debug);

// If true, path module will generate nudge back constraint in homotopies that
// Ego need to yield for some agents ahead.
DECLARE_bool(planning_enable_path_nudge_back);

// If true, selection module shall reject trajectories due to longitudinal limit
// violation.
DECLARE_bool(planning_enable_selection_longitudinal_limit_check);

// If false, enable tbb parallel in simulation
DECLARE_bool(planning_disable_tbb_parallelism);
// If true, planning will deal with the off-road objects.

DECLARE_bool(planning_enable_offroad_objects_filtering);

// If true, enable use route topological info for temp parked.
DECLARE_bool(planning_enable_route_topological_info_for_temp_parked);

// If true, enable use solid line hardening for large vehicle.
DECLARE_bool(planning_enable_solid_line_hardening_for_large_vehicle);

// If true, replace speed profile using human driving poses.
DECLARE_bool(planning_mock_human_speed);

// If true, replace path candidates using human driving poses.
DECLARE_bool(planning_mock_human_path);

// If true, assign the lowest cost among all lane follow sequences to the
// optimal lane follow sequence.
DECLARE_bool(planning_assign_lowest_cost_to_optimal_lane_follow_sequence);

// If true, enable to use cloud cell from bag in simulation.
DECLARE_bool(planning_use_cloud_cell_in_simulation);

// If true, Ego will creep to proceed more urgently in dense traffic.
DECLARE_bool(planning_enable_dense_traffic_creeping);

// If false, core planner will disable sending honk signal to downstream control
// module and will only add honk debug information, which will be used to
// collect human honk data from ops during road testing. Details can be found in
// https://cooper.didichuxing.com/docs2/document/2203687590188 .
DECLARE_bool(planning_disable_honk_shadow_mode_for_core_planner);

// If true, pull over abort trigger will additionally consume output of the
// general selection risk model.
DECLARE_bool(planning_enable_general_risk_model_for_pull_over_abort);

// If true, path reasoning will increase nudge RLG for ped when Ego speed is
// high.
DECLARE_bool(planning_enable_speed_based_rlg_for_ped);

// If true, path reasoning will increase nudge RLG for large vehicle when Ego
// speed is high.
DECLARE_bool(planning_enable_speed_based_rlg_for_large_vehicle);

// If true, tidal flow lane stuck request will be published.
DECLARE_bool(planning_enable_tidal_flow_lane_stuck_request);

// If true, the signed_lateral_gap_to_ego_poses and
// signed_lateral_gap_to_first_ego_pose_boxes field in the overlap
// proto will be saved in the speed solver debug.
DECLARE_bool(planning_enable_signed_lateral_gap_to_ego_poses_in_debug);

// If true, path reasoning will increase nudge RLG for cyclist when Ego speed is
// high.
DECLARE_bool(planning_enable_speed_based_rlg_for_cyclist);

// If both planning_enable_speed_based_rlg_for_cyclist and this flag is true,
// path reasoning will increase nudge RLG for oncoming cyclist when Ego speed is
// high.
DECLARE_bool(planning_enable_speed_based_rlg_for_oncoming_cyclist);

// If true, path generator dump the JukeIntegratedDdpPathGeneratorInput proto
// for path optimization tuner
DECLARE_bool(planning_enable_path_generator_input_dump);

// If true, enable pull over gap generator model.
DECLARE_bool(planning_enable_ml_pull_over_gap_generator);

// If true, ego will plan pull over trajectories towards multiple destinations
// and execute the best available pull over trajectory.
DECLARE_bool(planning_enable_multi_dest_pull_over);

// If true, the ego can probe to the right during the pull over preparation
// stage.
DECLARE_bool(planning_enable_pull_over_jump_in_probe);

// If true, ra is going to use new remote connection signal.
DECLARE_bool(planning_enable_remote_assist_connection);

// If true, when calculating accumulated swerve cost, we will filter extremas
// using different strategies depending on extrema count.
DECLARE_bool(planning_enable_accumulated_swerve_different_extrema);

// If true, the planner will allow to set impo odd type in immediate pull over.
DECLARE_bool(planning_enable_impo_odd_type);

// If true, use max lateral jerk model to generate a comfort priority lane
// change option.
DECLARE_bool(planning_use_comfort_priority_lane_change_option);

// If true, enable the backup lane change option when gap align fails.
DECLARE_bool(planning_enable_backup_lane_change_option);

// If true, auto confirm waypoint assist in simulation mode.
DECLARE_bool(planning_enable_waypoint_assist_auto_confirm_in_simulation);

// If true, enable the dry steering back when stuck in lane keep behavior.
DECLARE_bool(planning_enable_dry_steering_back_for_lane_keep);

// If true, path will try to nudge all unknown objects if possible.
DECLARE_bool(planning_enable_path_reaction_to_all_unknown);

// If true, we allow brake mostly to EFS for FOD subject that is not drivable.
DECLARE_bool(planning_enable_speed_efs_for_non_drivable_unknown);

// If true, the lateral clearance calculation will filter out the out of
// physical boundary agents.
DECLARE_bool(
    planning_enable_lateral_clearance_out_of_physical_boundary_agent_filter);

// If true, enable add backup lane sequence when regional path reroute.
DECLARE_bool(planning_enable_add_backup_lane_sequence_when_reroute);

// If true, will add one ML path when there is short merge in the near front.
DECLARE_bool(planning_enable_add_ml_path_near_short_merge);

// If true, enable tracking based speed cautious driving to avoid squeeze.
DECLARE_bool(planning_enable_tracking_based_cautious_driving_to_avoid_squeeze);

// If true, use new regional path selection method.
DECLARE_bool(planning_enable_new_regional_path_selection);

// If true, we use new dynamic limit for planning to achieve faster brake
// release.
DECLARE_bool(planning_enable_new_dynamic_limit_with_double_brake_j_max);

// If true, path reasoning will enable undercarriaging over an FoD.
DECLARE_bool(planning_enable_undercarriaging_over_fod);

// If true, the search path can violate the physical boundary at the u-turn exit
// with high curvature.
DECLARE_bool(planning_enable_search_violate_physical_boundary);

// If true, enable the oncoming agent repulsion during pullover.
DECLARE_bool(planning_enable_repulsion_for_oncoming_agent_during_pullover);

// If true, path reasoning will only generate a ignore_all homotopy in dense
// crossing vru scenario.
DECLARE_bool(planning_enable_ignore_only_in_dense_crossing_vru_scenario);

// If true, the reference generator will use dynamic look ahead time
// to better track the speed bound while generating reference profile.
DECLARE_bool(planning_enable_dynamic_look_ahead_time_in_ref_generator);

// If true, enables publishing remote warning signal for noticing RA OPS.
DECLARE_bool(planning_enable_remote_warning_signal_publish);

// If true, enables the new remote assist model vnode.
DECLARE_bool(planning_enable_remote_assist_model_vnode);

// If true, the planner will publish slowmoving object detector debug.
DECLARE_bool(planning_enable_slowmoving_object_detector_debug);

// If true, the planner will detect slowmoving objects and generate lane
// sequence to avoid them.
DECLARE_bool(planning_enable_slowmoving_object_avoidance_lane_sequence);

// If true, use interventaion requirement to send ra creep around request.
DECLARE_bool(planning_enable_ra_intervention_requirement_for_creep_around);

// If true, selection risk model will apply risk discount to better-not-drive
// objects.
DECLARE_bool(planning_enable_selection_risk_bnd_discount);

// If true, enable multi-step exploration in the state lattice path search.
DECLARE_bool(planning_enable_multi_step_search);

// If true, use spatial_indexing for object quering.
DECLARE_bool(planning_enable_planner_objects_spatial_indexing);

// If true, selection will consider distance from agent's rear corners to
// ego's edge in risk computation.
DECLARE_bool(planning_selection_enable_min_distance_rear_perturbation);

// If true, if a soft blocking object or better not drive object is able to pass
// by nudge, lane sequence will not consider it as a blockage.
DECLARE_bool(planning_enable_skip_nudgable_soft_blocking_object);

// If true, planner will process map changed data for stuck avoidance.
DECLARE_bool(planning_enable_stuck_avoidance_dynamic_map_data_update);

// If true, enable use solid line hardening for dynamic large vehicle.
DECLARE_bool(planning_enable_solid_line_hardening_for_dynamic_large_vehicle);

// If true, the search path can violate the static agent at the u-turn exit with
// high curvature.
DECLARE_bool(planning_enable_search_violate_static_agent);

// If true, enable backtracking limiting when path search times out.
DECLARE_bool(planning_enable_search_timeout_backtracking_limiting);

// If true, enable planning to reject dangerous new route.
DECLARE_bool(planning_enable_reject_dangerous_new_route);

// If true, enable conflict risk analysis in junction lane preference decision.
DECLARE_bool(planning_enable_junction_lane_preference_conflict_analysis);

// If true, enable obstacle range stability check.
DECLARE_bool(planning_enable_obstacle_range_stability_check);

// If true, enables the use of extended bounding box for irregular shaped
// objects.
DECLARE_bool(planning_enable_extended_bounding_box_for_irregular_objects);

// If true, selection lateral_diff cost will be discounted when ego is slow.
DECLARE_bool(planning_discount_lateral_diff_cost_at_high_speed);

// If true, check if stop cell layer is ready in simulation.
DECLARE_bool(planning_enable_check_stop_cell_layer_in_simulation);

}  // namespace planner

#endif  // ONBOARD_PLANNER_PLANNING_GFLAGS_H_
