#include "planner/world_model/world_model.h"

#include <algorithm>
#include <cstddef>
#include <cstdint>
#include <limits>
#include <memory>
#include <optional>
#include <queue>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include <absl/strings/str_format.h>
#include <boost/filesystem.hpp>
#include <tbb/parallel_for.h>
#include <tbb/task_arena.h>
#include <tbb/tbb.h>

#include "adv_geom/path_curve.h"
#include "control/control_gflags.h"
#include "exception_handler/exception_handler_gflags.h"
#include "exception_handler/exception_handler_main/trajectory_result.h"
#include "exception_handler/utility/common/exception_handler_util.h"
#include "geometry/algorithms/distance.h"
#include "geometry/algorithms/specialization/within_polygon_with_cache.h"
#include "geometry/model/point_2d.h"
#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/hdmap_common.h"
#include "hdmap/lib/relax_lane_marking_util.h"
#include "hdmap/stop_cell/stop_cell.h"
#include "latency/latency_stat.h"
#include "order_protos/order_service.pb.h"
#include "planner/assist/assist_instruction.h"
#include "planner/assist/assist_util.h"
#include "planner/behavior/maneuvers/util/maneuver_common.h"
#include "planner/behavior/util/lane_common/lane_sequence_iterator.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_generator_utility2.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"
#include "planner/planning_gflags.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/lattice_planner/lattice_planner_util.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/current_lane_reasoner/current_lane_candidate.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_generator_util.h"
#include "planner/world_model/pickup_dropoff_zone/stop_cell_info_util.h"
#include "planner/world_model/pickup_dropoff_zone/virtual_pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/planner_object/planner_object_util.h"
#include "planner/world_model/planner_object/planner_objects_history.h"
#include "planner/world_model/regional_map/common_utility.h"
#include "planner/world_model/regional_map/lane_connectivity_utility.h"
#include "planner/world_model/regional_map/regional_map_utility.h"
#include "planner/world_model/regional_map/regional_path_utility.h"
#include "planner/world_model/regional_map/section_preference/reason_preferred_section_sequence.h"
#include "planner/world_model/regional_map/section_preference/section_preference_param.h"
#include "planner/world_model/regional_map/stuck_avoidance/map_change_area_processor_param.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_param.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/snapshot/snapshot_taker_utility.h"
#include "planner/world_model/traffic_participant/predicted_trajectory.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/util/map_debug_utility.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "prediction/common/utility/prediction_utility.h"
#include "prediction_protos/stationary_intention.pb.h"
#include "proto_util/proto_io.h"
#include "routing/engine/routing_edge_filter.h"
#include "routing/node/utils.h"
#include "routing/utility/routing_mode_util.h"
#include "routing/utility/routing_param_util.h"
#include "rt_event/rt_event.h"
#include "stop_cell_protos/stop_cell_data.pb.h"
#include "trace/trace.h"
#include "voy_protos/global_selection.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {

constexpr int kWaypointLanesCacheCapacity = 1500;

// The max distance threshold of ego's distance to order start for pull out
// trigger.
constexpr double kMaxPullOutDistanceFromEgoStartPositionInMeter = 100.0;
// The min distance threshold of ego's distance to order destination for pull
// out trigger.
constexpr double kMinPullOutDistanceToRouteDestinationInMeter = 50.0;

// The pull out termination distance threshold, beyond which the current pull
// out is considered completed and jump out sequence should no longer be picked.
constexpr double kPullOutTerminationDrivingDistanceInMeter = 10.0;

// The max distance to extend the destination successor sections for normal pdz.
constexpr double kMaxDistanceToExtendDestinationSuccessorSectionsInMeter = 30.0;

// The distance threshold to determine whether bus are queueing behind another
// bus near a bus bulb.
constexpr double kDistanceThresholdForQueuingBusInM = 5.0;

// The distance threshold to a bus bulb lane to consider queueing buses.
constexpr double kDistanceThresholdToBusBulbLaneInM = 30.0;

// The key to indicate a specific reason of why the regional sections locator is
// invalid: Failed to update regional sections locator.
constexpr const char* kRegionalSectionsLocatorUpdateFailed =
    "regional_sections_locator_updating_failed";

// The minimum speed in meter per second to exempt these hard timed constraint
// lanes from ego.
constexpr double kMinSpeedForExemptHardConstraintWhenOutOfConstraintMs = 2.0;
// The physics limit of ego harsh brake with {acceleration, min_jerk, max_jerk}
// to avoid entering timed constraint.
constexpr physics::MM1dLimitInfo kHarshBrakeLimitForTimedConstraint{-2.0, -5.0,
                                                                    3.0};
// The maximum distance to exempt these hard timed constraint lanes from ego
// when ego is already in hard constraint.
constexpr double kMaxDistToExemptTimedHardConstraintWhenInConstraintM = 300.0;

// Find the subsequent previous lanes of current lane within horizon and save
// them into lane_ids.
void AddSubsequentLanesWithinHorizon(const pnc_map::Lane* current_lane,
                                     std::unordered_set<int64_t>& lane_ids) {
  const auto next_lanes = current_lane->predecessors();
  if (next_lanes.empty()) {
    return;
  }
  // For the first version, only consider the first next lane since in most
  // cases there is only one next lane and it is usually long enough.
  // TODO(jinghuang1): Add more reasoning logic to decide which subsequent lane
  // to add.
  const pnc_map::Lane* next_lane = next_lanes[0];
  lane_ids.insert(next_lane->id());
  double current_horizon = next_lane->length();
  while (current_horizon < kDistanceThresholdToBusBulbLaneInM) {
    const auto& subsequent_next_lanes = next_lane->predecessors();
    if (subsequent_next_lanes.empty()) {
      break;
    }
    next_lane = subsequent_next_lanes[0];
    current_horizon += next_lane->length();
    lane_ids.insert(next_lane->id());
  }
  return;
}

// Search for buses queuing near bus bulb and update them.
void SearchAndUpdateQueuingBuses(
    const std::vector<std::pair<double, ObjectId>>& same_lane_buses,
    const std::shared_ptr<std::unordered_map<ObjectId, PlannerObject>>&
        planner_object_map_ptr,
    ObjectId last_bus_id_near_bus_bulb) {
  const auto last_bus_near_bus_bulb_iter =
      std::find_if(same_lane_buses.begin(), same_lane_buses.end(),
                   [last_bus_id_near_bus_bulb](const auto& it) {
                     return it.second == last_bus_id_near_bus_bulb;
                   });
  DCHECK(last_bus_near_bus_bulb_iter != same_lane_buses.end());

  // Loop backward from the last bus near bus bulb, to see if there are buses
  // behind it that are queuing.
  for (int i =
           std::distance(same_lane_buses.begin(), last_bus_near_bus_bulb_iter) -
           1;
       i >= 0; --i) {
    const auto current_bus_id = same_lane_buses[i].second;
    const auto front_bus_id = same_lane_buses[i + 1].second;
    PlannerObject& current_bus = planner_object_map_ptr->at(current_bus_id);
    PlannerObject& front_bus = planner_object_map_ptr->at(front_bus_id);

    const double bus_body_length_m =
        (current_bus.length() + front_bus.length()) / 2.0;
    if ((math::geometry::Distance(current_bus.center_2d(),
                                  front_bus.center_2d()) -
         bus_body_length_m) < kDistanceThresholdForQueuingBusInM) {
      current_bus.set_is_bus_queuing_near_bus_bulb(true);
      continue;
    } else {
      // If the current bus is far from its front bus, meaning it is not queuing
      // near bus bulb, no need to continue the loop.
      break;
    }
  }
}

// Gets the lane IDs that we want to exempt it from timed hard constraint to
// avoid emergency break. These lanes who are very near to ego along the last
// section sequence should be exempted.
std::set<hdmap::LaneId> GetExemptTimedHardConstraintLaneIdsWithMaxLength(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const routing::RoutingEdgeFilter& edge_filter,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Lane*>&
        time_hard_constrained_current_lanes,
    const std::vector<const pnc_map::Section*>& last_regional_section_sequence,
    double max_length, bool just_take_first_continue_block) {
  if (current_lane_candidates.empty() ||
      last_regional_section_sequence.empty() || max_length < 0.0 ||
      math::NearZero(max_length)) {
    return {};
  }

  std::set<hdmap::LaneId> exempted_hard_time_constraint_lane_ids;
  // If current lane is already hard constraint in this time, we should
  // exempt all lanes in the same section.
  for (const pnc_map::Lane* current_lane :
       time_hard_constrained_current_lanes) {
    for (const pnc_map::Lane* lane : current_lane->section()->lanes()) {
      exempted_hard_time_constraint_lane_ids.insert(lane->id());
    }
  }

  const std::set<const pnc_map::Section*> allowed_search_sections(
      last_regional_section_sequence.begin(),
      last_regional_section_sequence.end());
  for (const auto& current_lane_candidate : current_lane_candidates) {
    if (current_lane_candidate.lane() == nullptr) {
      continue;
    }
    const std::vector<common::LaneSegment>
        lane_segments_along_section_sequence = BuildPathFromLaneWithMaxLength(
            drivable_lane_reasoner, current_lane_candidate.lane(),
            current_lane_candidate.position_arclength_from_lane_start(),
            /*max_length=*/max_length,
            /*need_reverse_path=*/false, /*allow_undrivable_lane=*/false,
            allowed_search_sections,
            /*IsLeafNode=*/[](const PathSearchNode& current_node) {
              return current_node.current_lane_segment()
                  .lane->successors()
                  .empty();
            });
    if (lane_segments_along_section_sequence.empty()) {
      continue;
    }
    bool is_last_hard_constrained = false;
    for (const auto& lane_segment : lane_segments_along_section_sequence) {
      if (lane_segment.lane == nullptr ||
          lane_segment.lane->id() == current_lane_candidate.lane()->id()) {
        continue;
      }
      if (edge_filter.IsLaneHardConstrained(
              lane_segment.lane->id(), /*only_check_timed_constraint=*/true)) {
        exempted_hard_time_constraint_lane_ids.insert(lane_segment.lane->id());
        is_last_hard_constrained = true;
        continue;
      }
      if (is_last_hard_constrained && just_take_first_continue_block) {
        // The last is hard constrained and the current segment is not, if we
        // just need the first continue block, we should abandon the follow
        // segments.
        break;
      }
    }
  }
  return exempted_hard_time_constraint_lane_ids;
}

// Gets the lane IDs that we want to exempt it from timed hard constraint to
// avoid emergency break. These lanes who are very near to ego along the last
// section sequence should be exempted.
// Note: exempted timed hard constraint lanes should be set before current lane
// update, so |current_lane_candidates| is from last cycle.
std::set<hdmap::LaneId> GetExemptTimedHardConstraintLaneIds(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const routing::RoutingEdgeFilter& edge_filter,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<const pnc_map::Section*>&
        last_regional_section_sequence) {
  if (current_lane_candidates.empty() ||
      last_regional_section_sequence.empty()) {
    return {};
  }

  std::vector<const pnc_map::Lane*> time_constrained_current_lanes;
  for (const lane_candidate::CurrentLaneCandidate& current_lane_candidate :
       current_lane_candidates) {
    if (current_lane_candidate.lane() != nullptr &&
        edge_filter.IsLaneHardConstrained(
            current_lane_candidate.lane()->id(),
            /*only_check_timed_constraint=*/true)) {
      time_constrained_current_lanes.push_back(current_lane_candidate.lane());
    }
  }
  const bool is_ego_already_in_timed_hard_constraint =
      !time_constrained_current_lanes.empty();

  if (!is_ego_already_in_timed_hard_constraint) {
    // Ego is not in timed hard constraint area. It is expect to exempt the hard
    // constraint when the speed is high and ego is very near to the timed hared
    // constraint area.
    if (robot_state_snapshot.speed() <=
        kMinSpeedForExemptHardConstraintWhenOutOfConstraintMs) {
      return {};
    }
    const double harsh_brake_stopping_distance =
        robot_state_snapshot.ComputeStoppingDistance(
            kHarshBrakeLimitForTimedConstraint);
    return GetExemptTimedHardConstraintLaneIdsWithMaxLength(
        drivable_lane_reasoner, edge_filter, current_lane_candidates,
        time_constrained_current_lanes, last_regional_section_sequence,
        harsh_brake_stopping_distance,
        /*just_take_first_continue_block=*/false);
  } else {
    // Ego is already in the timed hard constraint area, we should exempt the
    // first continue hard constraint area along the regional section sequence.
    return GetExemptTimedHardConstraintLaneIdsWithMaxLength(
        drivable_lane_reasoner, edge_filter, current_lane_candidates,
        time_constrained_current_lanes, last_regional_section_sequence,
        kMaxDistToExemptTimedHardConstraintWhenInConstraintM,
        /*just_take_first_continue_block=*/true);
  }
  return {};
}

}  // namespace

WorldModel::WorldModel(
    const pb::WorldModelConfig& config,
    const pnc_map::PncMapService* pnc_map_service,
    const std::shared_ptr<routing::EdgeFilterManager>& edge_filter_manager,
    const std::shared_ptr<GlobalObjectManager>& global_object_manager,
    double robot_max_speed, double regional_path_range)
    : pnc_map_history_buffer_(pnc_map_service),
      world_model_config_(config),
      robot_state_ptr_(MakeRobotState(config.replan_locator_config())),
      traffic_signal_reasoner_(),
      drivable_lane_reasoner_(edge_filter_manager, &traffic_signal_reasoner_),
      regional_sections_locator_(DCHECK_NOTNULL(pnc_map_service),
                                 &drivable_lane_reasoner_, robot_max_speed,
                                 regional_path_range),
      lane_blockage_detector_(PlannerConfigCenter::GetInstance()),
      lane_congestion_detector_(DCHECK_NOTNULL(pnc_map_service)),
      route_model_(pnc_map_service, edge_filter_manager),
      waypoint_lanes_cache_(pnc_map_service->zone_lane_occupancy_map(),
                            edge_filter_manager, drivable_lane_reasoner(),
                            kWaypointLanesCacheCapacity),
      regional_map_generator_(config.regional_map_config(),
                              &drivable_lane_reasoner_, &waypoint_lanes_cache_),
      destruction_background_pool_(std::make_unique<av_comm::ThreadPool>(3)),
      immediate_pull_over_state_manager_(
          DCHECK_NOTNULL(pnc_map_service),
          CHECK_NOTNULL(global_object_manager)->construction_zones(),
          config.immediate_pull_over_config()),
      current_lane_associator_(&drivable_lane_reasoner_),
      current_lane_reasoner_(&drivable_lane_reasoner_),
      previous_action_recorder_(pnc_map_service),
      map_change_area_processor_(CHECK_NOTNULL(pnc_map_service),
                                 &drivable_lane_reasoner_),
      stuck_avoidance_reasoner_(CHECK_NOTNULL(pnc_map_service),
                                &drivable_lane_reasoner_,
                                config.stuck_avoidance_config()),
      changed_lane_structure_reasoner_(CHECK_NOTNULL(pnc_map_service)),
      global_object_manager_(CHECK_NOTNULL(global_object_manager)) {
  if (robot_state_ptr_) {
    pickup_dropoff_zone_generator_ptr_ =
        std::make_unique<PickupDropoffZoneInfoGenerator>(
            robot_state_ptr_->car_model_with_shape().shape_measurement());
    regional_parking_zone_generator_ptr_ =
        std::make_unique<VirtualPickupDropoffZoneInfoGenerator>(
            robot_state_ptr_->car_model_with_shape()
                .shape_measurement()
                .width());
  }
  const pb::PrecomputedStateLatticeSpiralData& pb_precomputed_spiral_data =
      planner::common::LoadPrecomputedStateLatticeSpiralData();

  precomputed_spiral_data_ =
      ConvertProtoBufToSpiralData(pb_precomputed_spiral_data);

  InitAllMLModels();
}

std::unique_ptr<SpeedWorldModel> WorldModel::ExtractToSpeedWorldModel(
    std::unique_ptr<planner::RouteModelSpeedData>& route_model_speed_data) {
  TRACE_EVENT_SCOPE(
      planner, WorldModel_ExtractToSpeedWorldModel,
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(
      LAT_STAT_WorldModel_ExtractToSpeedWorldModel);
  return std::make_unique<SpeedWorldModel>(
      GetLatestJointPncMapService(), std::move(this->latest_snapshot_ptr_),
      std::move(this->agent_list_), std::move(this->tracked_objects_),
      std::move(this->global_object_manager_->mutable_construction_zones()),
      should_ignore_map_change_traffic_lights(),
      std::move(this->global_object_manager_->mutable_construction_zone_map()),
      std::move(this->global_object_manager_->mutable_object_prediction_map()),
      std::move(this->global_object_manager_
                    ->mutable_object_conditional_prediction_map()),
      this->regional_map_, this->replan_route_query_,
      this->order_start_time_info_,
      this->should_generate_pull_out_jump_out_sequence_,
      this->global_object_manager_->is_high_agent_density(),
      this->last_selected_lane_sequence_, this->OrderStartPositionDistance(),
      this->should_planner_respond_mrc_,
      this->IsEgoPositionOnRouteReadyForPullOut(),
      this->should_trigger_pull_out_by_mrc_, this->HasPullOutFinished(),
      *this->robot_state_ptr_, this->traffic_signal_reasoner_,
      this->drivable_lane_reasoner_,
      this->global_object_manager_->planner_object_map_ptr(),
      this->global_object_manager_->planner_objects_history(),
      this->pickup_dropoff_zone_infos_, this->pull_over_request_info_,
      this->map_change_area_handle_param(), this->lane_blockage_detector_,
      this->lane_congestion_detector_, route_model_speed_data,
      this->current_lane_associator_, this->current_lane_reasoner_,
      this->waypoint_assist_tracker_, this->immediate_pull_over_state_manager_,
      this->assist_directive_generator_, this->stuck_avoidance_reasoner_,
      this->assist_task_scheduler_, this->changed_lane_structure_reasoner_);
}

std::unique_ptr<SpeedWorldModel> WorldModel::ExtractToSpeedWorldModel() {
  auto route_model_speed_data = this->route_model_.CreateRouteModelSpeedData();
  return this->ExtractToSpeedWorldModel(route_model_speed_data);
}

void WorldModel::LoadState(const pb::WorldModelState& from_pb) {
  // TODO(reprod-planner): Implement.
  if (from_pb.has_assist_directive_generator()) {
    assist_directive_generator_.LoadState(from_pb.assist_directive_generator());
  }

  if (from_pb.has_regional_sections_locator()) {
    regional_sections_locator_.LoadState(from_pb.regional_sections_locator());
  }

  if (from_pb.has_route_model()) {
    route_model_.LoadState(*pnc_map_service(), from_pb.route_model());
  }

  if (from_pb.has_regional_map_generator()) {
    regional_map_generator_.LoadState(from_pb.regional_map_generator());
  }

  if (from_pb.has_regional_map()) {
    LoadRegionalMapState(from_pb.regional_map(), *pnc_map_service(),
                         &regional_map_);
  }

  if (from_pb.has_immediate_pull_over_state_manager()) {
    immediate_pull_over_state_manager_.LoadState(
        from_pb.immediate_pull_over_state_manager());
  }

  if (from_pb.has_lane_blockage_detector()) {
    lane_blockage_detector_.LoadState(from_pb.lane_blockage_detector());
  }

  if (from_pb.has_drivable_lane_reasoner()) {
    drivable_lane_reasoner_.LoadState(from_pb.drivable_lane_reasoner());
  }

  if (from_pb.has_robot_state()) {
    robot_state_ptr_->LoadState(from_pb.robot_state());
  }

  if (from_pb.has_waypoint_assist_tracker()) {
    waypoint_assist_tracker_.LoadState(from_pb.waypoint_assist_tracker());
  }

  if (from_pb.has_assist_blockage_analyzer()) {
    assist_blockage_analyzer_.LoadState(from_pb.assist_blockage_analyzer());
  }

  if (from_pb.has_waypoint_lanes_cache()) {
    waypoint_lanes_cache_.LoadState(*pnc_map_service()->hdmap(),
                                    from_pb.waypoint_lanes_cache());
  }

  pull_over_reference_point_ =
      math::geometry::Point2d(from_pb.pull_over_reference_point().x(),
                              from_pb.pull_over_reference_point().y());
  cached_routing_dest_point_ =
      math::geometry::Point2d(from_pb.cached_routing_dest_point().x(),
                              from_pb.cached_routing_dest_point().y());

  if (from_pb.has_map_change_area_processor()) {
    map_change_area_processor_.LoadState(*pnc_map_service(),
                                         from_pb.map_change_area_processor());
  }

  if (from_pb.has_violate_lane_marking_singleton()) {
    hdmap::util::ViolateLaneMarkingSingleton::Get().LoadFromPB(
        *pnc_map_service()->hdmap(), from_pb.violate_lane_marking_singleton());
  }

  if (from_pb.has_stuck_avoidance_reasoner()) {
    stuck_avoidance_reasoner_.LoadState(*pnc_map_service(),
                                        from_pb.stuck_avoidance_reasoner());
  }

  if (from_pb.has_changed_lane_structure_reasoner()) {
    changed_lane_structure_reasoner_.LoadState(
        *pnc_map_service(), from_pb.changed_lane_structure_reasoner());
  }

  if (from_pb.has_assist_task_scheduler()) {
    assist_task_scheduler_.LoadState(from_pb.assist_task_scheduler());
  }

  if (from_pb.has_previous_action_recorder()) {
    previous_action_recorder_.LoadState(from_pb.previous_action_recorder());
  }

  // TODO(songjingru): adjust the order of load state, and set joint pnc map
  // service for before loading other state.
  if (from_pb.has_changed_lane_structure_reasoner()) {
    changed_lane_structure_reasoner_.LoadState(
        *pnc_map_service(), from_pb.changed_lane_structure_reasoner());
  }
}

void WorldModel::DumpState(pb::WorldModelState& to_pb) const {
  // TODO(reprod-planner): Implement.
  assist_directive_generator_.DumpState(
      *to_pb.mutable_assist_directive_generator());

  regional_sections_locator_.DumpState(
      *to_pb.mutable_regional_sections_locator());

  route_model_.DumpState(*to_pb.mutable_route_model());

  regional_map_generator_.DumpState(*to_pb.mutable_regional_map_generator());

  DumpRegionalMapState(regional_map_, *to_pb.mutable_regional_map());

  immediate_pull_over_state_manager_.DumpState(
      *to_pb.mutable_immediate_pull_over_state_manager());

  lane_blockage_detector_.DumpState(*to_pb.mutable_lane_blockage_detector());

  drivable_lane_reasoner_.DumpState(*to_pb.mutable_drivable_lane_reasoner());

  robot_state_ptr_->DumpState(*to_pb.mutable_robot_state());

  waypoint_assist_tracker_.DumpState(*to_pb.mutable_waypoint_assist_tracker());

  assist_blockage_analyzer_.DumpState(
      *to_pb.mutable_assist_blockage_analyzer());

  waypoint_lanes_cache_.DumpState(*to_pb.mutable_waypoint_lanes_cache());

  // Set pull_over_reference_point
  auto* pull_over_ref_point_proto = to_pb.mutable_pull_over_reference_point();
  pull_over_ref_point_proto->set_x(pull_over_reference_point_.x());
  pull_over_ref_point_proto->set_y(pull_over_reference_point_.y());

  // Set cached_routing_dest_point
  auto* cached_routing_dest_point_proto =
      to_pb.mutable_cached_routing_dest_point();
  cached_routing_dest_point_proto->set_x(cached_routing_dest_point_.x());
  cached_routing_dest_point_proto->set_y(cached_routing_dest_point_.y());

  map_change_area_processor_.DumpState(
      *to_pb.mutable_map_change_area_processor());

  hdmap::util::ViolateLaneMarkingSingleton::Get().DumpToPB(
      to_pb.mutable_violate_lane_marking_singleton());

  stuck_avoidance_reasoner_.DumpState(
      *to_pb.mutable_stuck_avoidance_reasoner());

  assist_task_scheduler_.DumpState(*to_pb.mutable_assist_task_scheduler());

  previous_action_recorder_.DumpState(
      *to_pb.mutable_previous_action_recorder());

  changed_lane_structure_reasoner_.DumpState(
      *to_pb.mutable_changed_lane_structure_reasoner());
}

bool WorldModel::Update(const pb::WorldModelSeed& previous_world_model_seed,
                        std::unique_ptr<const Snapshot> snapshot_ptr,
                        std::unique_ptr<std::vector<pb::AssistResponse>>
                            remote_assist_responses_ptr,
                        bool has_accepted_new_global_route_lanes,
                        bool is_proposed_route_solution_rejected,
                        pb::PlanningDebug* planning_debug) {
  TRACE_EVENT_SCOPE(
      planner, WorldModel_Update,
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());

  const auto prev_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          PathLastFinishedFrame());

  agent_list_.reset();
  // Normally, the lidar node sends a message every 100ms. Hardware timestamps
  // are saved as hw_time. By detecting the difference in hw_time between two
  // snapshots, we can calculate how many frames were lost.
  frame_drop_count_ = 0;
  if (latest_snapshot_ptr_) {
    const double current_frame_timestamp = static_cast<double>(
        latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());
    const double last_frame_timestamp =
        static_cast<double>(prediction_timestamp());
    if (current_frame_timestamp <= last_frame_timestamp) {
      frame_drop_count_ = 0;
    } else {
      frame_drop_count_ = static_cast<int>(
          std::round((current_frame_timestamp - last_frame_timestamp) / 100.0) -
          1);
    }
  }

  // Get route status update reason.
  pb::RouteStatusUpdateReason route_status_update_reason =
      pb::RouteStatusUpdateReason::kNotUpdateInPlanner;
  if (latest_snapshot_ptr_ == nullptr && snapshot_ptr->route_status_ptr) {
    route_status_update_reason =
        pb::RouteStatusUpdateReason::kRouteStatusChangedFromNullptrToValid;
  } else if (latest_snapshot_ptr_ && latest_snapshot_ptr_->route_status_ptr &&
             snapshot_ptr->route_status_ptr) {
    route_status_update_reason = GetPlannerRouteStatusUpdateReason(
        *(snapshot_ptr->route_status_ptr),
        &(*latest_snapshot_ptr_->route_status_ptr));
  }

  latest_snapshot_ptr_.swap(latest_snapshot_ptr_background_);
  latest_snapshot_ptr_ = std::move(DCHECK_NOTNULL(snapshot_ptr));
  destruction_background_pool_->PushTask([this]() {
    const Snapshot* raw_ptr = latest_snapshot_ptr_background_.release();
    delete raw_ptr;
  });

  pb::WorldModelDebug* world_model_debug =
      planning_debug ? planning_debug->mutable_world_model_debug() : nullptr;

  pnc_map::pb::ChangedElements* changed_elements =
      world_model_debug == nullptr
          ? nullptr
          : world_model_debug->mutable_changed_elements();

  UpdateChangedMapElements(changed_elements);

  const auto& global_init_state = latest_snapshot_ptr_->global_init_state_ptr;
  if (FLAGS_planning_disable_planner_trajectory &&
      !exception_handler::
          FLAGS_exception_handler_enable_only_selected_trajectory &&
      global_init_state != nullptr &&
      global_init_state->last_trajectory().is_ml_planner_trajectory() &&
      global_init_state->last_selected_lane_sequence_size() != 0 &&
      exception_handler::FLAGS_exception_handler_global_selection_mode !=
          "ML_PLANNER") {
    const int64_t current_lane =
        global_init_state->last_selected_lane_sequence(0);
    if (std::any_of(
            previous_world_model_seed.last_selected_lane_sequence().begin(),
            previous_world_model_seed.last_selected_lane_sequence().end(),
            [&](int64_t lane_sequence) {
              return lane_sequence == current_lane;
            })) {
      *Seed::Access<token::WorldModelSeed>::MutableMsg(PathCurrentFrame())
           ->mutable_last_selected_lane_sequence() =
          global_init_state->last_selected_lane_sequence();
    }
  }
  if (FLAGS_planning_disable_planner_trajectory &&
      !exception_handler::
          FLAGS_exception_handler_enable_only_selected_trajectory &&
      exception_handler::FLAGS_exception_handler_global_selection_mode ==
          "HYBRID" &&
      global_init_state != nullptr &&
      global_init_state->last_trajectory().is_ml_planner_trajectory()) {
    Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(PathCurrentFrame())
        ->set_should_latch_lane_change_geom(false);
    Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(PathCurrentFrame())
        ->mutable_selected_waypoint_assist_seed()
        ->set_should_latch_waypoint_assist_geom(false);
  }
  SetLastSelectedLaneSequence(
      previous_world_model_seed.last_selected_lane_sequence());

  UpdateOrderStartTimeInfo();

  // Update robot state.
  UpdateRobotState(
      previous_world_model_seed,
      planning_debug ? planning_debug->mutable_replan_locator_debug() : nullptr,
      world_model_debug == nullptr
          ? nullptr
          : world_model_debug->mutable_robot_state_debug());

  SetCurrentLaneOnLastSelectedLaneSequence();

  // Update should respond mrc.
  UpdateShouldPlannerRespondMRC();

  // Update should_trigger_pull_out_by_mrc_.
  UpdateShouldTriggerPullOutByMrc(previous_world_model_seed);

  // Update global route solution.
  // NOTE: Update global route solution first because section locator, regional
  // path generator, current lane... are going to use it.
  if (!UpdateGlobalRouteSolution(
          has_accepted_new_global_route_lanes,
          is_proposed_route_solution_rejected, route_status_update_reason,
          world_model_debug ? world_model_debug->mutable_global_route_debug()
                                  ->mutable_planning_route_status_debug()
                            : nullptr)) {
    return false;
  }

  // Early return if the route status pointer is null.
  if (latest_snapshot_ptr_->route_status_ptr == nullptr) {
    return false;
  }

  if (!RestoreLastSelectedRegionalPathFromSeed()) {
    LOG(ERROR) << "Failed to restore last selected regional path from seed.";
  }

  // Update drivable lane reasoner.
  UpdateDrivableLaneReasoner();

  // Update the regional sections locator.
  if (!UpdateRegionalSectionsLocator(
          previous_world_model_seed,
          world_model_debug
              ? world_model_debug->mutable_regional_sections_locator_debug()
              : nullptr)) {
    return false;
  }

  // Assist blockage analyzer may ignore obstacle or CZ confirmed by RA ops.
  // TODO(liangxianghui): Refine assist directive generator and use
  // assist_responses() instead of latest_snapshot_ptr_->assist_responses.
  const bool is_mrc_reached =
      mrc_request_ptr() &&
      mrc_request_ptr()->mrm_progress() == mrc::pb::FINISHED;
  assist_blockage_analyzer_.UpdatePerceptionFPObstacleTracker(
      robot_state().IsInAutonomousMode() && !is_mrc_reached,
      latest_snapshot_ptr_->assist_responses,
      world_model_debug
          ? world_model_debug->mutable_assist_blockage_analyzer_debug()
          : nullptr);

  // TODO(DuongLe): deprecated this after move agent_list_ and tracked_objects
  // to GOM
  UpdateAgentList();

  UpdateTrackedObjects(/*max_interested_prediction_timestamp=*/std::nullopt);

  UpdateConstructionZones();

  ResetPullOutJumpOutSeedIfNewMission();

  SetIfShouldGeneratePullOutJumpOutSequence(
      // information has been carried over, and may have been reset by
      // ResetPullOutJumpOutSeedIfNewMission
      Seed::Access<token::PullOutJumpOutSeed>::GetMsg(PathCurrentFrame())
          ->has_entered_jump_out_sequence());

  // TODO(Wenyue): Add proposal from immediate pull over state manager.
  lane_sequence_proposal_info_ = lane_selection::ParseLaneSequenceProposal(
      *GetLatestJointPncMapService(),
      prev_maneuver_seed->lane_sequence_proposal());

  // Update current lane reasoner.
  if (FLAGS_planning_enable_current_lane_reasoner
          ? !UpdateCurrentLaneReasoner(
                previous_world_model_seed,
                prev_maneuver_seed->last_lane_change_finish_timestamp_msec())
          : !UpdateCurrentLaneAssociator(previous_world_model_seed,
                                         *prev_maneuver_seed)) {
    // TODO(Tingran): Figure out what is the correct way to handle empty current
    // lanes.
    LOG(ERROR) << "Empty current lanes when updating current lane reasoner.";
  }

  // Check if |current_lane_candidates_for_regional_path| is valid. If not, post
  // an RT event with the relevant invalid information.
  PostRtEventIfCurrentLaneInvalid(
      current_lane_candidates_for_regional_path(), locator_potential_sections(),
      last_selected_lane_sequence(), pnc_map_service(),
      previous_world_model_seed.has_last_selected_lane_sequence_candidate()
          ? &previous_world_model_seed.last_selected_lane_sequence_candidate()
          : nullptr,
      /*is_current_lane_for_regional_path=*/true,
      robot_state().IsInAutonomousMode(),
      waypoint_assist_tracker().IsWaypointAssistInvoked(), WasReversing(),
      order_info());

  // Update traffic signal reasoner.
  // Note(Wenyue): Update current lane associator before traffic signal
  // reasoner.
  UpdateTrafficSignalReasoner(current_lanes_for_regional_path());

  // Receive the empirical publish data from cloud.
  if (latest_snapshot_ptr_ &&
      latest_snapshot_ptr_->empirical_publish_data_list_ptr) {
    empirical_info_processor_.ReceiveEmpiricalPublishData(
        latest_snapshot_ptr_->empirical_publish_data_list_ptr);
  }

  const auto last_unstuck_seed =
      Seed::Access<token::UnstuckSeed>::GetMsg(PathLastFinishedFrame());
  assist_task_scheduler_.UpdateAssistTaskQueue(
      assist_directive_generator_.unstuck_directives(), mrc_request_ptr(),
      remote_assist_responses_ptr, last_selected_lane_sequence(),
      latest_snapshot_ptr_->assist_responses, robot_state(), *last_unstuck_seed,
      current_lane_associator(), current_lane_reasoner(),
      *global_route_solution(),
      planning_debug ? planning_debug->mutable_behavior_reasoner_debug()
                           ->mutable_assist_debug()
                           ->mutable_assist_task_scheduler_debug()
                     : nullptr);

  assist_directive_generator_.UpdateUnstuckInfo(
      assist_task_scheduler_.assist_task_queue(),
      *global_object_manager_->planner_object_map_ptr(),
      latest_snapshot_ptr_->assist_responses,
      assist_task_scheduler_.remote_assist_signal(), robot_state(),
      current_lane_on_last_selected_lane_sequence(),
      *GetLatestJointPncMapService(),
      latest_snapshot_ptr_->remote_speed_limit_ptr.get(), *last_unstuck_seed,
      Seed::Access<token::LightAssistSeed>::MutableMsg(PathCurrentFrame())
          .get(),
      planning_debug ? planning_debug->mutable_behavior_reasoner_debug()
                           ->mutable_assist_debug()
                           ->mutable_assist_directive_generation_debug()
                     : nullptr);

  const bool has_left_assist_blockage_area =
      !FLAGS_planning_enable_ra_pass_through_map_change_area ||
      assist_blockage_analyzer_.waypoint_cz_tracker().HasLeftBlockageArea(
          robot_state().plan_init_state_snapshot().bounding_box());
  waypoint_assist_tracker_.UpdateWaypointAssistState(
      robot_state(), route_model(), assist_task_scheduler_.assist_task_queue(),
      assist_responses(),
      stuck_avoidance_reasoner()
          .stuck_avoidance_parameter()
          .waypoint_assist_request.waypoint_assist_responses,
      last_selected_lane_sequence(), *GetLatestJointPncMapService(),
      drivable_lane_reasoner(), *global_route_solution(),
      current_lane_associator(), current_lane_reasoner(), *prev_maneuver_seed,
      lane_blockage_detector().road_blockages(), has_left_assist_blockage_area,
      IsImmediatePullOverTriggeredByPlanning(),
      current_lane_associator_.IsCurrentLanesContinuousWhenRA(
          /*is_waypoint_assist_invoked=*/true),
      world_model_debug ? world_model_debug->mutable_waypoint_assist_debug()
                        : nullptr,
      planning_debug ? planning_debug->mutable_behavior_reasoner_debug()
                           ->mutable_lane_sequence_generator()
                     : nullptr);
  waypoint_assist_empirical_info_processor_
      .UpdateWaypointAssistEmpiricalInfoProcessor(
          assist_task_scheduler().assist_task_queue(),
          current_lane_associator(), regional_map(), pose(),
          waypoint_assist_tracker().reference_points(),
          waypoint_assist_tracker().is_routing_unstuck_success(),
          waypoint_assist_tracker().waypoint_assist_phase(),
          waypoint_assist_tracker().is_waypoint_triggered_by_routing());
  const bool is_ego_in_junction =
      !current_lanes_for_regional_path().empty()
          ? DCHECK_NOTNULL(current_lanes_for_regional_path().front())
                ->IsInJunction()
          : false;
  assist_blockage_analyzer_.UpdateTrafficLightMapChangeAreaTracker(
      robot_state().IsInAutonomousMode(), assist_responses(),
      is_ego_in_junction, pose(),
      map_change_area_handle_param()
          .GetStuckTrafficLightChangedAssociateAreas(),
      world_model_debug
          ? world_model_debug->mutable_assist_blockage_analyzer_debug()
          : nullptr);

  assist_blockage_analyzer_.UpdateWaypointCZTracker(pose(),
                                                    waypoint_assist_tracker_);

  // update global_object_manager_ (build planner_objects,
  // construction_zones...)
  global_object_manager_->Update(
      snapshot_timestamp(), *latest_snapshot_ptr_->agent_list_ptr,
      prev_maneuver_seed->selection_seed().ego_offroad_info_seed(),
      assist_blockage_analyzer_.perception_fp_obstacle_tracker(),
      regional_map_.regional_path.distance_to_destination_m, robot_state(),
      *pnc_map_service(), previous_world_model_seed.object_type_tracking_map(),
      regional_sections_locator_.tracked_object_perception_range_lanes(),
      assist_directive_generator().unstuck_directives(),
      GetStuckAssistInstruction(),
      world_model_debug ? world_model_debug->mutable_planner_object_debug()
                        : nullptr);

  // Updates whether planner object is bus queuing near bus bulb.
  UpdateBusQueuingNearBusBulb(robot_state_ptr_->last_nominal_path());

  UpdateConstructionZoneMap();

  // Update replan route query.
  SetReplanRouteQuery(previous_world_model_seed);

  // Update the lane blockages.
  UpdateLaneBlockage(*prev_maneuver_seed,
                     world_model_debug
                         ? world_model_debug->mutable_lane_blockage_debug()
                         : nullptr);

  // Update the lane congestions.
  UpdateLaneCongestion(world_model_debug
                           ? world_model_debug->mutable_lane_congestion_debug()
                           : nullptr);

  // Update lane graph search info.
  UpdateLaneGraphSearchInfo();

  // Update the processor of map change area.
  map_change_area_processor_.Update(
      drivable_lane_reasoner(), last_selected_lane_sequence(),
      robot_state_ptr_->current_state_snapshot(),
      lane_blockage_detector_.lane_blockages(),
      current_lane_candidates_for_regional_path());

  // Collect empirical raw data, which will from vehicle to cloud.
  empirical_info_processor_.CollectEmpiricalRawData(
      lane_blockage_detector().road_blockages(),
      waypoint_assist_empirical_info_processor_
          .fix_point_unstuck_upload_msgs());

  // Update the information of destination successor sections.
  UpdateDestinationSuccessorSectionsInfo(
      world_model_debug
          ? world_model_debug
                ->mutable_destination_successor_section_info_debug()
          : nullptr);

  // Update regional map.
  if (!UpdateRegionalMap(
          previous_world_model_seed, *prev_maneuver_seed,
          world_model_debug
              ? world_model_debug->mutable_regional_map_info_debug()
              : nullptr)) {
    return false;
  }

  // Update waypoint recommend routing replan data. Call it after
  // UpdateRegionalMap.
  UpdateWaypointRecommendRoutingReplan(
      robot_state(), current_lane_candidates_for_regional_path(),
      last_selected_lane_sequence(),
      world_model_debug ? world_model_debug->mutable_waypoint_assist_debug()
                              ->mutable_waypoint_recommend_routing_replan()
                        : nullptr);

  // Update recommend_replan_info and desired_route_request in
  // replan_route_query_ after UpdateRegionalMap and
  // UpdateWaypointRecommendRoutingReplan.
  // [NOTE]: There is no need trigger recommend replan or desired replan for
  // predefined_route.
  if (FLAGS_planning_enable_recommend_replan_info_to_routing &&
      world_model_debug != nullptr &&
      !routing::utils::IsPredefinedRoute(replan_route_query_)) {
    TryToAddRecommendReplanInfo(
        *DCHECK_NOTNULL(global_route_solution()), robot_state(),
        lane_blockage_detector_.road_blockages(), drivable_lane_reasoner_,
        current_lane_associator_, current_lane_reasoner_, regional_map_,
        world_model_debug->waypoint_assist_debug()
            .waypoint_recommend_routing_replan(),
        &replan_route_query_);
  }
  if (FLAGS_planning_enable_replan_for_desired_route &&
      world_model_debug != nullptr &&
      !routing::utils::IsPredefinedRoute(replan_route_query_) &&
      !regional_map_.regional_path.diverge_with_last_selected_ls) {
    TryToAddDesiredRouteRequest(world_model_debug->regional_map_info_debug()
                                    .regional_map()
                                    .regional_path(),
                                &replan_route_query_);
  }

  // Updates preview action recorder. Currently previous actions only used for
  // lane sequence, so just put after updating regional map.
  UpdatePreviousActionRecorder(robot_state(), previous_world_model_seed,
                               *prev_maneuver_seed, GetDrivablePhysicalLanes(),
                               snapshot_timestamp());

  if (world_model_debug) {
    // Update route replan debug.
    world_model_debug->mutable_replan_debug()
        ->mutable_replan_route_query()
        ->CopyFrom(replan_route_query_);

    // NOTE(Tingran): Update current lane debug after updating regional map
    // and current lanes for waypoint graph.
    UpdateCurrentLaneDebug(current_lane_associator_.backup_lanes_debug(),
                           world_model_debug->mutable_current_lane_debug());

    // Update lane congestion debug info.
    UpdateLaneCongestionDebug(
        world_model_debug->mutable_lane_congestion_debug());

    // Update traffic signal reasoner debug info.
    UpdateTrafficSignalReasonerDebug(
        world_model_debug->mutable_traffic_signal_reasoner_debug());

    // Update drivable lane reasoner debug info.
    UpdateDrivableLaneReasonerDebug(
        world_model_debug->mutable_drivable_lane_reasoner_debug());

    // Update map change area processor debug.
    UpdateMapChangeAreaProcessorDebug(
        world_model_debug->mutable_map_change_area_processor_debug());

    // Update changed lane structure reasoner debug.
    UpdateChangedLaneStructureReasonerDebug(
        world_model_debug->mutable_changed_lane_structure_debug());

    // Update debugs for |ViolateLaneMarkingSingleton|.
    UpdateViolateLaneMarkingSingletonDebug(
        world_model_debug->mutable_violate_lane_marking_singleton_debug());

    // Update debugs for |drivable_lane_reasoner|.
    UpdateStuckAvoidanceReasonerDebug(
        world_model_debug->mutable_stuck_avoidance_reasoner_debug());

    UpdateCurrentLaneStateDebug(world_model_debug->mutable_global_route_debug()
                                    ->mutable_current_lane_state_debug());

    UpdatePreviousActionRecorderDebug(
        world_model_debug->mutable_previous_action_recorder_debug());

    // Update debugs for waypoint lane cache.
    waypoint_lanes_cache_.DumpState(
        *world_model_debug->mutable_waypoint_lanes_cache_debug());
  }

  // Update immediate pull over state manager.
  DCHECK(latest_snapshot_ptr_ != nullptr);
  DCHECK(latest_snapshot_ptr_->route_status_ptr != nullptr);
  immediate_pull_over_state_manager_.UpdateImmediatePullOverStateManager(
      *global_route_solution(), *GetLatestJointPncMapService(), regional_map(),
      robot_state(), construction_zones(),
      prev_maneuver_seed->lane_sequence_candidates(),
      *(current_lanes_for_regional_path().front()),
      latest_snapshot_ptr_->route_status_ptr->immediate_pullover_info(),
      should_planner_respond_mrc() ? latest_snapshot_ptr_->mrc_request_ptr
                                   : nullptr,
      pose(), assist_responses(), GetOddInfo(), snapshot_timestamp(),
      previous_world_model_seed.pull_over_progress(),
      world_model_debug ? world_model_debug->mutable_immediate_pull_over_debug()
                        : nullptr);

  UpdateOrderTrackInfo();
  // Update the pickup dropoff zones.
  UpdatePickupDropoffZone(
      world_model_debug ? world_model_debug->mutable_pickup_dropoff_debug()
                        : nullptr,
      world_model_debug ? world_model_debug->mutable_immediate_pull_over_debug()
                        : nullptr);

  // Check the ml trajectory init state with planner.
  if (FLAGS_planning_enable_ml_trajectory_init_state_check &&
      trajectory_guider_output() != nullptr &&
      MLTrajectoryInitStateHasDivergedFromPlanner(
          robot_state(), *trajectory_guider_output())) {
    rt_event::PostRtEvent<
        rt_event::planner::MLTrajectoryInitStateHasDivergedFromPlanner>();
    set_ml_trajectory_init_state_has_diverged_from_planner(true);
  }

  if (world_model_debug) {
    pb::WaypointAssistDebug* assist_debug =
        world_model_debug->mutable_waypoint_assist_debug();
    const std::vector<ConstructionZoneId>& ignored_cz_ids =
        assist_blockage_analyzer_.GetIgnoredCZIds();
    for (const ConstructionZoneId cz_id : ignored_cz_ids) {
      assist_debug->add_ignored_cz_ids(cz_id);
    }
  }
  // Update the snapshot debug.
  if (planning_debug) {
    *planning_debug->mutable_snapshot_debug() =
        latest_snapshot_ptr_->snapshot_debug;
  }

  return true;
}

void WorldModel::UpdateOrderStartTimeInfo() {
  if (!latest_snapshot_ptr_->order_service_ptr) {
    return;
  }
  const int64_t order_start_time_in_seconds =
      latest_snapshot_ptr_->order_service_ptr->order_start_time() / 1000;
  order_start_time_info_ = std::localtime(&order_start_time_in_seconds);
}

void WorldModel::UpdateTrafficSignalReasoner(
    const std::vector<const pnc_map::Lane*>& current_lanes) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateTrafficSignalReasoner);
  traffic_signal_reasoner_.Clear();
  // Update variable lane info.
  traffic_signal_reasoner_.set_current_lanes(current_lanes);
  const auto previous_seed =
      Seed::Access<token::TrafficSignalReasonerSeed>::GetMsg(
          PathLastFinishedFrame());
  const auto new_seed = traffic_signal_reasoner_.Update(
      *GetLatestJointPncMapService(),
      latest_snapshot_ptr_->traffic_light_ptr->tidal_flows(),
      previous_seed.get());
  auto mutable_seed =
      Seed::Access<token::TrafficSignalReasonerSeed>::MutableMsg(
          PathCurrentFrame());
  mutable_seed->CopyFrom(new_seed);
}

void WorldModel::UpdatePreviousActionRecorder(
    const RobotState& robot_state,
    const pb::WorldModelSeed& previous_world_model_seed,
    const pb::DecoupledManeuverSeed& prev_maneuver_seed,
    std::vector<const pnc_map::Lane*> current_cycle_current_lanes,
    int64_t snapshot_timestamp) {
  if (global_route_solution() == nullptr) {
    LOG(ERROR) << "Not update previous action recorder due to null global "
                  "route solution or null last cycle current lane";
    return;
  }

  previous_action_recorder_.Update(
      *GetLatestJointPncMapService(), robot_state, previous_world_model_seed,
      prev_maneuver_seed, global_route_solution()->is_new_trip(),
      current_cycle_current_lanes, snapshot_timestamp);
}

// [NOTE]: Do not clear |mandatory_drivable_lane_segments_| when world_model
// update. If you have to do it, please contact routing team.
void WorldModel::UpdateDrivableLaneReasoner() {
  drivable_lane_reasoner_.set_current_timestamp_ms(
      latest_snapshot_ptr_->pose_timestamp);
  const auto& current_lane_candidates_for_regional_path =
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner().current_lane_candidates_for_regional_path()
          : current_lane_associator()
                .current_lane_candidates_for_regional_path();
  drivable_lane_reasoner_.set_exempted_hard_time_constraint_lane_ids(
      GetExemptTimedHardConstraintLaneIds(
          drivable_lane_reasoner(), route_model_.edge_filter(),
          current_lane_candidates_for_regional_path,
          robot_state().current_state_snapshot(),
          regional_map_.regional_path.sections));
}

void WorldModel::UpdateRobotState(
    const pb::WorldModelSeed& previous_world_model_seed,
    pb::ReplanLocatorDebug* replan_locator_debug,
    pb::RobotStateDebug* robot_state_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateRobotState);
  // If exception handler was activated, we should use last exception handler
  // trajectory.
  // If eh node enabled, get eh trajectory from message; else, get eh
  // trajectory from seed.
  std::optional<pb::Trajectory> last_exception_hanlder_trajectory =
      ::exception_handler::ExceptionHandlerSeedAccess::GetTrajectoryToken();
  const auto& eh_trajectory_ptr_from_eh_node =
      latest_snapshot_ptr_->exception_handler_trajectory_ptr;
  const auto& global_init_state = latest_snapshot_ptr_->global_init_state_ptr;
  // TODO(Planner): Get the executed last trajectory seed from control instead
  // of using the logic flags.
  if (control::FLAGS_control_consume_exceptional_trajectory &&
      last_exception_hanlder_trajectory.has_value() &&
      !FLAGS_planning_enable_eh_node) {
    // If eh node not enabled, get eh trajectory from seed.
    robot_state_ptr_->UpdateTrajectory(*last_exception_hanlder_trajectory);
    LOG(INFO) << "Using last_exception_handler_trajectory, frame "
              << PathCurrentFrame() << " trajectory ts "
              << robot_state_ptr_->last_trajectory().timestamp();
  } else if (control::FLAGS_control_consume_exceptional_trajectory &&
             FLAGS_planning_enable_eh_node &&
             eh_trajectory_ptr_from_eh_node != nullptr &&
             eh_trajectory_ptr_from_eh_node->is_exception_overrided() &&
             eh_trajectory_ptr_from_eh_node->poses_size() > 0) {
    // If eh node enabled, get eh trajectory from message.
    robot_state_ptr_->UpdateTrajectory(*eh_trajectory_ptr_from_eh_node);
    LOG(INFO) << "Using trajectory from eh_trajectory_ptr_from_eh_node, frame "
              << PathCurrentFrame() << " trajectory ts "
              << robot_state_ptr_->last_trajectory().timestamp();
  } else if (!av_comm::InWarmup() &&
             FLAGS_planning_disable_planner_trajectory &&
             global_init_state != nullptr) {
    robot_state_ptr_->UpdateTrajectory(
        ::exception_handler::exception_handler_main::TrajectoryResultInstance::
            GetInstance()
                ->GetLastTrajectoryFromGlobalInitState(*global_init_state));
    LOG(INFO) << "Using trajectory from global init state, frame "
              << PathCurrentFrame() << " trajectory ts "
              << robot_state_ptr_->last_trajectory().timestamp();
  } else {
    robot_state_ptr_->UpdateTrajectory(
        *Seed::Access<token::WorldModel_LastTrajectory>::GetMsg(
            PathLastFinishedFrame()));
    LOG(INFO) << "Using trajectory from normal planning, frame "
              << PathCurrentFrame() << " trajectory ts "
              << robot_state_ptr_->last_trajectory().timestamp();
  }
  robot_state_ptr_->UpdateNominalPath(
      previous_world_model_seed.last_nominal_path());
  robot_state_ptr_->UpdateStationaryColdstartInfo(
      previous_world_model_seed.last_target_steering_rad(),
      previous_world_model_seed.last_arclength_to_nearest_static_fence_m());

  const auto& last_stuck_replan_request_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(PathLastFinishedFrame())
          ->stuck_replan_request_seed();
  robot_state_ptr_->UpdatePoseCanbus(
      pose(), latest_snapshot_ptr_->canbus_ptr,
      latest_snapshot_ptr_->replan_request_ptr, replan_locator_debug,
      robot_state_debug,
      std::make_optional<speed::pb::PulloutReasonerSeed>(
          pull_out_reasoner_seed()),
      std::make_optional<pb::StuckReplanRequestSeed>(
          last_stuck_replan_request_seed),
      previous_world_model_seed.has_dry_steering_target_angle()
          ? std::make_optional<double>(
                previous_world_model_seed.dry_steering_target_angle())
          : std::nullopt);

  robot_state_ptr_->set_is_ego_stuck(
      Seed::Access<token::AssistStuckSeed_IsEgoStuck>::GetValue(
          PathLastFinishedFrame()));
}

void WorldModel::UpdateLaneBlockage(
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed,
    pb::LaneBlockageDebug* lane_blockage_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateLaneBlockage);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(LAT_STAT_WorldModel_UpdateLaneBlockage);
  const auto& optional_unstuck_directives =
      assist_directive_generator().unstuck_directives();
  const AssistInstruction* assist_instruction =
      optional_unstuck_directives.has_value()
          ? &(optional_unstuck_directives->assist_instruction)
          : nullptr;
  const std::optional<pb::IntentionResult> last_lane_keep_intention =
      previous_decoupled_lane_follow_maneuver_seed.selected_behavior_type() ==
              pb::BehaviorType::LANE_KEEP
          ? std::make_optional(
                previous_decoupled_lane_follow_maneuver_seed.last_intent())
          : std::nullopt;
  lane_blockage_detector_.Update(
      *GetLatestJointPncMapService(), robot_state(), occlusion_checker(),
      *global_object_manager_->planner_object_map_ptr(),
      global_object_manager_->construction_zones(),
      regional_map().lane_sequence_searchable_sections,
      drivable_lane_reasoner(), current_lanes_for_regional_path(), pose(),
      traffic_light_detection(), regional_map().extended_sections,
      regional_map().lane_to_junction_info_map, assist_instruction,
      empirical_info_processor_.empirical_info_publish_data(),
      regional_map().regional_path.distance_to_destination_m,
      last_lane_keep_intention, lane_blockage_debug);
  const auto& lane_congestion_reasoner =
      lane_blockage_detector_.lane_congestion_reasoner();
  if (lane_congestion_reasoner.HasCongestedTrafficFlow()) {
    rt_event::PostRtEvent<rt_event::planner::BlockHasCongestedTrafficFlow>("");
  }

  if (!lane_blockage_detector_.cloud_road_blockages().empty()) {
    routing::utility::AddRtEventAsJson<
        rt_event::planner::RoadBlockageReceiveCloudData>(
        order_info(), "receive_cloud_road_blockages",
        lane_blockage_detector_.cloud_road_blockages().size());
  }
}

void WorldModel::UpdateLaneCongestion(
    pb::LaneCongestionDebug* lane_congestion_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateLaneCongestion);
  lane_congestion_detector_.Update(
      drivable_lane_reasoner(), current_lanes_for_regional_path(), pose(),
      tracked_objects(), latest_snapshot_ptr_->lane_occluded_traffic_flow_map,
      lane_congestion_debug);
}

bool WorldModel::UpdateRegionalSectionsLocator(
    const pb::WorldModelSeed& previous_world_model_seed,
    pb::RegionalSectionsLocatorDebug* regional_sections_locator_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateRegionalSectionsLocator);
  const routing::pb::RouteSolution* route_solution_ptr = nullptr;
  int32_t waypoint_count = 0;
  if (global_route_solution()) {
    route_solution_ptr = &(global_route_solution()->route_solution());
    waypoint_count = global_route_solution()->waypoint_count();
  }

  const bool locator_update_success = regional_sections_locator_.Update(
      *GetLatestJointPncMapService(), pose(), waypoint_count,
      robot_state().IsInAutonomousMode(),
      waypoint_assist_tracker().IsWaypointAssistInvoked(), WasReversing(),
      last_selected_lane_sequence(),
      previous_world_model_seed.has_last_selected_lane_sequence_candidate()
          ? &previous_world_model_seed.last_selected_lane_sequence_candidate()
          : nullptr,
      route_solution_ptr, regional_sections_locator_debug);
  if (locator_update_success) {
    // Check if the update results of regional sections locator is valid. If
    // not, post relevant RT events.
    // Notes(yuzehao): In the future, if the rt events of regional sections
    // locator become more and more, we can put all of them into
    // |PostRtEventIfRegionalSectionsLocatorInvalid|. In addition, all invalid
    // reasons about why regional sections locator is invalid will be indicated
    // in |rt_event::planner::RegionalSectionLocatorInvalid| as json string in
    // the future.
    PostRtEventIfRegionalSectionsLocatorInvalid(
        locator_potential_sections(), regional_section_vertices(),
        regional_sections_locator_.potential_sections_source(),
        regional_sections_locator_.is_all_lanes_near_ego_in_section_vertices(),
        robot_state().IsInAutonomousMode(), order_info());
  } else {
    routing::utility::AddRtEventAsJson<
        rt_event::planner::RegionalSectionLocatorInvalid>(
        order_info(), kRegionalSectionsLocatorUpdateFailed, true);
    LOG(ERROR) << "Regional sections locator is invalid: "
               << kRegionalSectionsLocatorUpdateFailed
               << ". Order_info: " << order_info().GetOrderString();
  }
  return locator_update_success;
}

// TODO(DuongLe): deprecated this after move agent_list_ and tracked_objects
// to GOM
void WorldModel::UpdateAgentList() {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateAgentList);
  agent_list_.reset();
  const assist::PerceptionFPObstacleTracker& perception_fp_obstacle_tracker =
      assist_blockage_analyzer_.perception_fp_obstacle_tracker();
  if (perception_fp_obstacle_tracker.HasIgnoredObstacles()) {
    agent_list_ = std::make_optional(*(latest_snapshot_ptr_->agent_list_ptr));
    for (auto iter = agent_list_->mutable_agent_list()->begin();
         iter != agent_list_->mutable_agent_list()->end();) {
      const voy::TrackedObject& object = iter->tracked_object();
      if (perception_fp_obstacle_tracker.ShouldIgnore(object.id(),
                                                      object.object_type())) {
        iter = agent_list_->mutable_agent_list()->erase(iter);
      } else {
        ++iter;
      }
    }
  }
}

// TODO(thaiduongle): move to GOM
void WorldModel::UpdateTrackedObjects(
    const std::optional<int64_t>& max_interested_prediction_timestamp) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateTrackedObjects);
  const int64_t prediction_timestamp =
      max_interested_prediction_timestamp
          ? *max_interested_prediction_timestamp
          : GetMaxInterestedPredictionTimestamp(pose().timestamp());
  // Construct agent list.
  const int agent_list_size = agent_list().agent_list_size();

  tracked_objects_.resize(agent_list_size);

  const int64_t timestamp = this->prediction_timestamp();
  const auto& perception_range_lanes =
      regional_sections_locator_.tracked_object_perception_range_lanes();

  int default_tbb_concurrency = tbb::this_task_arena::max_concurrency();
  int arena_concurrency = static_cast<int>(default_tbb_concurrency * 0.8);
  tbb::task_arena tbb_task_arena(arena_concurrency);
  tbb_task_arena.execute([&] {
    tbb::parallel_for(
        /*first=*/0, /*last=*/agent_list_size,
        [this, timestamp, prediction_timestamp, perception_range_lanes](int i) {
          const prediction::pb::Agent& agent = agent_list().agent_list(i);
          tracked_objects_[i] = TrackedObject(
              timestamp, agent, prediction_timestamp, perception_range_lanes);
        });
  });
}

void WorldModel::UpdateConstructionZones() {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateConstructionZones);
  // Construct construction zone list.
  // TODO(Jun Liu): Use fused construction zone when it is stable.
  auto& construction_zones =
      global_object_manager_->mutable_construction_zones();

  construction_zones.clear();
  construction_zones.reserve(
      latest_snapshot_ptr_->filtered_construction_zones.size());
  const int64_t timestamp =
      latest_snapshot_ptr_->construction_zone_list_ptr->timestamp();
  const auto& perception_range_lanes =
      regional_sections_locator_.construction_zone_perception_range_lanes();
  auto ignored_construction_zones =
      Seed::Access<token::AssistStuckSeed_IgnoredConstructionZones>::MutableMsg(
          PathCurrentFrame());
  const ConstructionZoneId dominant_cz_id =
      Seed::Access<token::AssistStuckSeed>::GetMsg(PathCurrentFrame())
          ->dominant_cz_id();
  for (const auto& construction_zone :
       latest_snapshot_ptr_->filtered_construction_zones) {
    if (assist_blockage_analyzer_.ShouldIgnoreCZ(
            construction_zone, dominant_cz_id,
            ignored_construction_zones.get())) {
      // This cz is confirmed ignorable by a RA Ops.
      continue;
    }
    if (map_change_area_handle_param().ShouldIgnoreTheMapChangeArea(
            construction_zone)) {
      // This cz is ignorable by planning.
      continue;
    }
    construction_zones.emplace_back(timestamp, construction_zone,
                                    perception_range_lanes);
  }
  if (int cz_sz = construction_zones.size()) {
    std::ostringstream cz_record_oss;
    cz_record_oss << latency::PipelineID<
                         latency::PipelineType::PlannerStage1LidarHWTime>()
                         .load()
                  << "," << cz_sz;
    rt_event::PostRtEvent<rt_event::planner::WorldModelConstructionZonesCount>(
        cz_record_oss.str());
  }
}

bool WorldModel::UpdateGlobalRouteSolution(
    bool has_accepted_new_global_route_lanes,
    bool is_proposed_route_solution_rejected,
    pb::RouteStatusUpdateReason route_status_update_reason,
    pb::PlanningRouteStatusDebug* planning_route_status_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateGlobalRouteSolution);
  if (!latest_snapshot_ptr_ || !latest_snapshot_ptr_->route_status_ptr) {
    return false;
  }

  return route_model_.UpdateGlobalRouteSolution(
      latest_snapshot_ptr_->route_status_ptr, *pnc_map_service(),
      has_accepted_new_global_route_lanes, is_proposed_route_solution_rejected,
      route_status_update_reason, planning_route_status_debug);
}

void WorldModel::UpdateGlobalRouteLanesInfo(
    const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
        new_route_lanes) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateGlobalRouteLanesInfo);
  const bool is_global_route_changed = !routing::utils::AreSameGlobalRoutes(
      new_route_lanes, global_route_solution()->route_solution().route_lanes());
  return route_model_.UpdateGlobalRouteLanesInfo(
      new_route_lanes, *pnc_map_service(), is_global_route_changed);
}

bool WorldModel::UpdateCurrentLaneAssociator(
    const pb::WorldModelSeed& previous_world_model_seed,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed) {
  return current_lane_associator_.Update(
      robot_state_ptr_->current_state_snapshot(), pose(),
      *GetLatestJointPncMapService(), route_model_.cost_generator(),
      locator_selected_near_lanes(), locator_potential_sections(),
      locator_extend_potential_sections(), regional_section_vertices(),
      *DCHECK_NOTNULL(global_route_solution()),
      last_selected_regional_path().path_sections,
      last_selected_lane_sequence(),
      previous_world_model_seed.has_last_selected_lane_sequence_candidate()
          ? &previous_world_model_seed.last_selected_lane_sequence_candidate()
          : nullptr,
      regional_map_.lane_to_junction_info_map,
      global_route_solution()->is_new_trip(),
      should_generate_pull_out_jump_out_sequence(),
      waypoint_assist_tracker().IsWaypointAssistInvoked(),
      robot_state().IsInAutonomousMode(),
      stuck_avoidance_reasoner_.stuck_avoidance_parameter(),
      *Seed::Access<token::LaneBlockageDetectorSeed>::GetMsg(
          PathLastFinishedFrame()),
      lane_sequence_proposal_info_,
      previous_decoupled_lane_follow_maneuver_seed,
      waypoint_assist_tracker().has_ego_reached_target_lane_sequence(),
      Seed::Access<token::PullOutJumpOutSeed>::MutableMsg(PathCurrentFrame())
          .get());
}

bool WorldModel::UpdateCurrentLaneReasoner(
    const pb::WorldModelSeed& previous_world_model_seed,
    int64_t last_lane_change_finish_timestamp_msec) {
  return current_lane_reasoner_.UpdateCurrentLaneCandidates(
             pose(), robot_state_ptr_->current_state_snapshot(),
             *GetLatestJointPncMapService(),
             *DCHECK_NOTNULL(global_route_solution()),
             locator_potential_sections(), locator_selected_near_lanes(),
             regional_section_vertices(), last_selected_lane_sequence(),
             last_selected_regional_path().path_sections,
             previous_world_model_seed
                     .has_last_selected_lane_sequence_candidate()
                 ? &previous_world_model_seed
                        .last_selected_lane_sequence_candidate()
                 : nullptr,
             regional_map_.lane_to_junction_info_map,
             stuck_avoidance_reasoner_.stuck_avoidance_parameter(),
             previous_world_model_seed.lane_blockage_detector_seed(),
             lane_sequence_proposal_info_,
             previous_world_model_seed.snapshot_timestamp(),
             last_lane_change_finish_timestamp_msec,
             global_route_solution()->is_new_trip(),
             should_generate_pull_out_jump_out_sequence(),
             Seed::Access<token::PullOutJumpOutSeed>::MutableMsg(
                 PathCurrentFrame())
                 .get()) &&
         current_lane_reasoner_.UpdateCurrentLaneCandidatesForRegionalPath(
             /*should_in_last_selected_lane_sequence=*/
             !last_selected_lane_sequence().empty());
}

void WorldModel::AddFallbackCurrentLaneForRegionalPath(
    const pnc_map::Lane* current_lane,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed) {
  const auto& target_lane_sequence_ids =
      previous_decoupled_lane_follow_maneuver_seed.lane_change_info_seed()
          .lane_change_instance()
          .target_lane_sequence();
  const auto& last_cycle_target_lane_sequence =
      GetLatestJointPncMapService()->GetLaneSequence(std::vector<int64_t>(
          target_lane_sequence_ids.begin(), target_lane_sequence_ids.end()));
  // TODO(Hui Luo): Define the same function for current lane reasoner.
  current_lane_associator_.AddFallbackCurrentLaneForRegionalPath(
      current_lane, robot_state_ptr_->current_state_snapshot(), pose(),
      route_model_.cost_generator(), locator_selected_near_lanes(),
      locator_potential_sections(), locator_extend_potential_sections(),
      *DCHECK_NOTNULL(global_route_solution()), last_selected_lane_sequence(),
      last_cycle_target_lane_sequence);
}

bool WorldModel::UpdateCurrentLanesForWaypointGraph(
    const pb::WorldModelSeed& previous_world_model_seed,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed) {
  if (FLAGS_planning_enable_current_lane_reasoner) {
    if (!current_lane_reasoner_.UpdateCurrentLaneCandidatesForLaneSequence(
            regional_map_.regional_path,
            /*should_in_last_selected_lane_sequence=*/
            !last_selected_lane_sequence().empty())) {
      LOG(ERROR) << "World model fails to update current lanes for "
                    "waypoint graph.";
      return false;
    }
  } else {
    if (!current_lane_associator_.UpdateCurrentLanesForWaypointGraph(
            *GetLatestJointPncMapService(), pose(), regional_map_.regional_path,
            robot_state_ptr_->current_state_snapshot(),
            route_model_.cost_generator(),
            *DCHECK_NOTNULL(global_route_solution()),
            regional_section_vertices(), last_selected_lane_sequence(),
            previous_world_model_seed
                    .has_last_selected_lane_sequence_candidate()
                ? &previous_world_model_seed
                       .last_selected_lane_sequence_candidate()
                : nullptr,
            *Seed::Access<token::PullOutJumpOutSeed>::GetMsg(
                PathCurrentFrame()),  // should have been copied over and
                                      // updated.
            stuck_avoidance_reasoner_.stuck_avoidance_parameter(),
            *Seed::Access<token::LaneBlockageDetectorSeed>::GetMsg(
                PathCurrentFrame()),
            previous_decoupled_lane_follow_maneuver_seed,
            should_generate_pull_out_jump_out_sequence(),
            global_route_solution()->is_new_trip(),
            robot_state().IsInAutonomousMode())) {
      LOG(ERROR) << "World model fails to update current lanes for "
                    "waypoint graph.";
      return false;
    }
  }

  // Check if |current_lanes_for_waypoint_graph| is valid. If not, post an RT
  // event with the relevant invalid information.
  PostRtEventIfCurrLaneForWaypointGraphInvalid(
      current_lanes_for_regional_path(), current_lanes_for_waypoint_graph(),
      locator_potential_sections(), last_selected_lane_sequence(),
      pnc_map_service(),
      previous_world_model_seed.has_last_selected_lane_sequence_candidate()
          ? &previous_world_model_seed.last_selected_lane_sequence_candidate()
          : nullptr,
      robot_state().IsInAutonomousMode(),
      waypoint_assist_tracker().IsWaypointAssistInvoked(), WasReversing(),
      order_info());

  return true;
}

void WorldModel::UpdateLaneGraphSearchInfo() {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateLaneGraphSearchInfo);

  const std::vector<const pnc_map::Lane*>& current_drivable_lanes =
      current_lanes_for_regional_path();
  // Update the current lanes.
  lane_graph_search_info_.clear_current_lanes();
  for (const auto& current_lane : current_drivable_lanes) {
    lane_graph_search_info_.add_current_lanes(current_lane->id());
  }

  // Update the parked car blocked lanes.
  lane_selection::UpdateParkedCarBlockedLanes(lane_blockage_detector_,
                                              &lane_graph_search_info_);

  // Update the slow moving car congested lanes.
  lane_selection::UpdateSlowMovingCongestedLanes(lane_blockage_detector_,
                                                 &lane_graph_search_info_);

  // Update the invalid lane change source lanes.
  lane_selection::UpdateInvalidLaneChangeSourceLanes(
      current_drivable_lanes,
      robot_state_ptr_->current_state_snapshot().front_bumper_position(),
      lane_blockage_detector_, std::map<int64_t, ViolableLaneMarkingRange>(),
      &lane_graph_search_info_);

  // Update waypoint lane cache for these lanes whose attribute of lane marking
  // has changed.
  lane_selection::UpdateViolableLaneMarkingsAssociatedLaneCache(
      *pnc_map_service()->hdmap(), &waypoint_lanes_cache_);

  lane_selection::UpdateChangedElementsAssociatedLaneCache(
      GetLatestJointPncMapService()->GetAllChangedLanes(),
      &waypoint_lanes_cache_);

  // Update waypoint lane cache for these lanes whose drivable attribute has
  // changed.
  waypoint_lanes_cache_.UpdateCacheForMandatoryDrivableLanes(
      drivable_lane_reasoner());
}

bool WorldModel::RestoreLastSelectedRegionalPathFromSeed() {
  return regional_map_generator_.RestoreLastSelectedRegionalPathFromSeed(
      *GetLatestJointPncMapService());
}

bool WorldModel::UpdateRegionalMap(
    const pb::WorldModelSeed& previous_world_model_seed,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed,
    pb::RegionalMapInfoDebug* regional_map_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateRegionalMap);
  const std::vector<lane_candidate::CurrentLaneCandidate>&
      current_drivable_lane_candidates =
          current_lane_candidates_for_regional_path();
  const std::vector<const pnc_map::Lane*>& physical_lanes =
      GetDrivablePhysicalLanes();
  const std::vector<const pnc_map::Lane*>& backup_lanes =
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner_.GetBackupLanes(
                *GetLatestJointPncMapService().get())
          : current_lane_associator_.GetBackupLanes(
                *GetLatestJointPncMapService().get());
  const std::vector<const pnc_map::Lane*>& current_drivable_lanes =
      current_lanes_for_regional_path();
  const RouteModelParam route_model_param =
      RouteModelParam(route_model_.cost_generator(),
                      *DCHECK_NOTNULL(global_route_solution()), order_info());
  const pb::ExtraPlannerSignals::PlannerBehaviorSignal behavior_signal =
      previous_decoupled_lane_follow_maneuver_seed.selected_trajectory()
          .extra_planner_signals()
          .behavior_signal();
  const auto& optional_unstuck_directives =
      assist_directive_generator().unstuck_directives();
  stuck_avoidance_reasoner_.Update(
      *GetLatestJointPncMapService(),
      robot_state_ptr_->current_state_snapshot(),
      *DCHECK_NOTNULL(global_route_solution()),
      lane_blockage_detector().lane_blockages(),
      lane_blockage_detector().road_blockages(),
      lane_blockage_detector().blocked_lane_marking_segments(),
      current_drivable_lane_candidates,
      regional_map().lane_to_junction_info_map,
      regional_map().pose_to_section_and_junction_info_map,
      regional_map().trimmed_sections, traffic_light_detection(),
      /*RA_unstuck_request=*/
      (optional_unstuck_directives.has_value()
           ? std::make_optional(
                 optional_unstuck_directives->expanded_assist_instruction
                     .routing_unstuck_trigger_info)
           : std::nullopt),
      latest_snapshot_ptr_->empirical_publish_data_list_ptr,
      waypoint_assist_tracker_.waypoint_assist_phase(),
      FLAGS_planning_enable_remote_assist_connection
          ? assist_task_scheduler()
                .remote_assist_signal()
                .HasPlannerReceivedTeleassistResponse()
          : assist_directive_generator_.has_started_remote_assist(),
      FLAGS_planning_enable_remote_assist_connection
          ? assist_task_scheduler()
                .remote_assist_signal()
                .HasPlannerReceivedTeleopsConnectedResponse()
          : assist_directive_generator_
                .has_tele_ops_accpeted_remote_assist_req(),
      // Consider it's true for the first frame.
      (last_selected_regional_path().path_lanes.empty()
           ? true
           : last_selected_regional_path().reach_virtual_target),
      WasReversing());
  const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param =
      stuck_avoidance_reasoner_.stuck_avoidance_parameter();
  const lane_selection::SectionPreferenceParam section_preference_param =
      lane_selection::GetPreferredSectionSequenceForRegionalPath(
          *pnc_map_service(), robot_state_ptr_->current_state_snapshot(),
          current_drivable_lanes, *route_model_param.global_route_solution,
          stuck_avoidance_param.section_preference_param,
          Seed::Access<token::RegionalMapGeneratorSeed>::GetMsg(
              PathLastFinishedFrame())
              ->lane_selection_preference_seed()
              .section_preference_seed(),
          destination_successor_sections_for_pullover_,
          regional_map_generator_.regional_path_generator()
              ->last_state_transition(),
          regional_map_generator_.regional_path_generator()
              ->section_preference_param());
  // Disable immutable lane sequence in regional map generation.
  auto lane_sequence_plan_init_state =
      previous_world_model_seed.lane_sequence_plan_init_state();
  lane_sequence_plan_init_state.clear_immutable_lane_sequence();
  lane_sequence_plan_init_state.clear_immutable_waypoint();
  const RegionalMapReturnType regional_map_return_type =
      regional_map_generator_.Generate(
          *GetLatestJointPncMapService(), robot_state(),
          locator_potential_sections(), current_drivable_lane_candidates,
          physical_lanes, backup_lanes, route_model_param,
          RegionalPathQueryParam(
              replan_route_query_, lane_graph_search_info_,
              regional_section_vertices(), lane_blockage_detector_,
              behavior_signal == pb::ExtraPlannerSignals::PULL_OVER
                  ? pb::ManeuverType::PULL_OVER
                  : previous_world_model_seed.last_selected_maneuver_type(),
              lane_sequence_plan_init_state, IsCurrentQueryUsePriorMap(),
              stuck_avoidance_param,
              previous_decoupled_lane_follow_maneuver_seed.selected_trajectory()
                  .extra_planner_signals()
                  .behavior_signal(),
              drivable_lane_reasoner(), section_preference_param,
              /*forbidden_fork_sections_in=*/{},
              previous_decoupled_lane_follow_maneuver_seed
                  .lane_sequence_candidates(),
              lane_blockage_detector_.lane_object_info_map(),
              &(regional_map_.lane_to_junction_info_map),
              previous_decoupled_lane_follow_maneuver_seed.speed_seed()
                  .speed_reasoner_seeds()
                  .pullout_reasoner_seed()
                  .is_triggered(),
              destination_successor_sections_for_pullover_,
              pullover_final_extend_section_percentage_,
              stuck_avoidance_reasoner_
                  .HasAnyPotentialStuckScenariosForRegionalPath()),
          route_model_.GetRouteEvent(), regional_map_,
          lane_sequence_proposal_info_, last_selected_lane_sequence_,
          regional_map_debug);
  if (!regional_map_return_type.regional_map) {
    const std::string error_info =
        "Fail to update regional map due to null regional path. "
        "Keep|regional_map_| from last cycle to avoid world model "
        "update failure.";
    LOG(ERROR) << error_info;
    rt_event::PostRtEvent<rt_event::planner::RegionalMapUpdateFailure>(
        error_info);
    // If last cycle regional map result is latched for a long time, need to
    // trim the regional path behind ego pose.
    lane_selection::TrimRegionalPathBehindEgoPose(
        robot_state_ptr_->current_state_snapshot(),
        &(regional_map_.regional_path));
  } else {
    regional_map_ = *regional_map_return_type.regional_map;
  }

  if (!regional_map_return_type.is_regional_path_updated &&
      current_lanes_for_regional_path().empty()) {
    const std::string error_info =
        "Regional path is not updated, and current lanes for "
        "regional path is empty.";
    LOG(ERROR) << error_info;
    rt_event::PostRtEvent<rt_event::planner::EmptyCurrentLanesForRegionalPath>(
        error_info);
    DCHECK(!regional_map_.regional_path.lanes.empty());
    AddFallbackCurrentLaneForRegionalPath(
        regional_map_.regional_path.lanes.front(),
        previous_decoupled_lane_follow_maneuver_seed);
  }

  // Update current lanes for waypoint graph.
  if (!UpdateCurrentLanesForWaypointGraph(
          previous_world_model_seed,
          previous_decoupled_lane_follow_maneuver_seed)) {
    LOG(ERROR)
        << "World model fails to update current lanes for waypoint graph.";
    return false;
  }

  // Update roads and zones for |regional_map_|
  regional_map_generator_.UpdateRegionalMapRoadAndZone(
      route_model_param, &regional_map_, regional_map_debug);

  // Update regional map related route info. This is done after generate optimal
  // regional path, and features would be used for lane sequence generation.
  const auto& route_lanes =
      route_model_param.global_route_solution->route_solution().route_lanes();
  DCHECK(!route_lanes.empty());

  const auto [actual_destination_percentage, destination_extended_distance] =
      GetActualDestinationPercentage(
          regional_map_, route_model_param.global_route_solution,
          destination_successor_sections_for_pullover_,
          pullover_final_extend_section_percentage_);
  UpdateRegionalMapRouteInfo(
      *GetLatestJointPncMapService(), *route_lanes.rbegin(), &regional_map_,
      route_model_param.global_route_solution->global_route_lanes(),
      current_lane_candidates_for_regional_path(),
      current_lanes_for_waypoint_graph(),
      lane_blockage_detector_.lane_congestion_reasoner(),
      lane_blockage_detector_.lane_object_info_map(),
      trajectory_guider_output(), actual_destination_percentage,
      destination_extended_distance, route_model_.coverage_areas(),
      mutable_drivable_lane_reasoner(), regional_map_debug);

  return true;
}

bool WorldModel::SetReplanRouteQuery(
    const pb::WorldModelSeed& previous_world_model_seed) {
  if (!latest_snapshot_ptr_->route_status_ptr) {
    return false;
  }
  const std::vector<const pnc_map::Lane*>& current_drivable_lanes =
      current_lanes_for_regional_path();
  const RobotStateSnapshot& robot_state_snapshot =
      robot_state_ptr_->current_state_snapshot();
  // For trip starting queries, we get predefined road ids from proposed route
  // query, in case the ride_route_query is still in last cycle (previous trip).
  const pb::RouteEvent& route_event = route_model_.GetRouteEvent();
  const ::google::protobuf::RepeatedField<int64_t>& predefined_road_ids =
      route_event.event_type() == pb::RouteEventType::kTripStarting
          ? latest_snapshot_ptr_->route_status_ptr->proposed_route_query()
                .predefined_route_roads()
          : latest_snapshot_ptr_->route_status_ptr->ride_route_query()
                .predefined_route_roads();
  const double unreachable_lane_cost =
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model_.cost_generator()
              .all_feature_cost_estimates()
              .unreachable_lane_cost_estimates,
          route_model_.cost_generator().use_zero_cost());
  const double non_last_selected_current_lane_cost =
      routing::cost_engine::cost_utils::GetFeatureCostEstimateCost(
          route_model_.cost_generator()
              .all_feature_cost_estimates()
              .non_last_selected_current_lane_cost_estimates,
          route_model_.cost_generator().use_zero_cost());

  const pnc_map::Lane* last_selected_current_lane = nullptr;
  if (previous_world_model_seed.has_last_selected_lane_sequence_candidate()) {
    for (const pnc_map::Lane* current_drivable_lane : current_drivable_lanes) {
      if (!current_drivable_lane) {
        continue;
      }
      if (previous_world_model_seed.last_selected_lane_sequence_candidate()
              .current_lane_id() == current_drivable_lane->id()) {
        last_selected_current_lane = current_drivable_lane;
        break;
      }
    }
  }

  // Note: here followed_path_type is from last cycle in seed.
  replan_route_query_ = lane_selection::GetRouteQuery(
      {robot_state_snapshot.x(), robot_state_snapshot.y()},
      robot_state_snapshot.timestamp(), current_drivable_lanes,
      *DCHECK_NOTNULL(global_route_solution()),
      route_model_.edge_filter().constraint_name(), predefined_road_ids,
      latest_snapshot_ptr_->route_status_ptr->ride_route_query().match_type(),
      route_model_.cost_generator()
          .cost_engine_config()
          .cost_engine_config_info()
          .speed_for_path_point_meters_per_sec(),
      unreachable_lane_cost, non_last_selected_current_lane_cost,
      last_selected_current_lane,
      Seed::Access<token::RouteReplanManagerSeed>::GetMsg(
          PathLastFinishedFrame())
          ->followed_path_type(),
      route_model_.coverage_areas());

  return true;
}

void WorldModel::UpdateCurrentLaneDebug(
    const pb::BackupLanesDebug& backup_lanes_debug_info,
    pb::CurrentLaneDebug* current_lane_debug) const {
  if (!current_lane_debug) {
    return;
  }
  current_lane_debug->Clear();
  // Update debug information for waypoint graph.
  pb::CurrentLaneForWaypointGraphDebug*
      mutable_waypoint_graph_current_lane_debug =
          current_lane_debug
              ->mutable_current_lane_candidates_for_waypoint_graph();
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       current_lanes_for_waypoint_graph()) {
    *mutable_waypoint_graph_current_lane_debug->add_current_lane_candidates() =
        current_lane.ConstructCurrentLaneDebug();
  }
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       current_lanes_for_pull_out_jump_out()) {
    *mutable_waypoint_graph_current_lane_debug
         ->add_pull_out_jump_out_candidates() =
        current_lane.ConstructCurrentLaneDebug();
  }
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       current_lanes_for_stuck_avoidance_jump_out()) {
    *mutable_waypoint_graph_current_lane_debug
         ->add_stuck_avoidance_jump_out_candidates() =
        current_lane.ConstructCurrentLaneDebug();
  }
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       current_lanes_on_fork()) {
    *mutable_waypoint_graph_current_lane_debug->add_current_lanes_on_fork() =
        current_lane.ConstructCurrentLaneDebug();
  }
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       prefix_associated_current_lanes()) {
    *mutable_waypoint_graph_current_lane_debug
         ->add_prefix_associated_candidates() =
        current_lane.ConstructCurrentLaneDebug();
  }

  // Update current lane information for regional path.
  pb::CurrentLaneForRegionalPathGraphDebug*
      mutable_regional_path_graph_current_lane_debug =
          current_lane_debug
              ->mutable_current_lane_candidates_for_regional_path_graph();
  for (const lane_candidate::CurrentLaneCandidate& current_lane :
       current_lane_candidates_for_regional_path()) {
    *mutable_regional_path_graph_current_lane_debug
         ->add_current_lane_candidates() =
        current_lane.ConstructCurrentLaneDebug();
  }

  // Update backup lane debug info.
  current_lane_debug->mutable_backup_lanes_debug()->CopyFrom(
      backup_lanes_debug_info);
}

void WorldModel::UpdateLaneCongestionDebug(
    pb::LaneCongestionDebug* lane_congestion_debug) const {
  if (!lane_congestion_debug) {
    return;
  }
  lane_congestion_debug->clear_lane_congestion_info();
  for (const auto& lane_congestion_info_in_lane : lane_congestion_map()) {
    for (const LaneCongestionInfo& lane_congestion_info :
         lane_congestion_info_in_lane.second) {
      pb::LaneCongestionInfoDebug* lane_congestion_info_debug =
          lane_congestion_debug->add_lane_congestion_info();
      lane_congestion_info_debug->set_lane_id(lane_congestion_info.lane_id);
      lane_congestion_info_debug->set_congestion_prob(
          lane_congestion_info.congestion_prob);
      lane_congestion_info_debug->set_along_lane_start_arclength_m(
          lane_congestion_info.along_lane_start_arclength_m);
      lane_congestion_info_debug->set_along_lane_end_arclength_m(
          lane_congestion_info.along_lane_end_arclength_m);
    }
  }
}

void WorldModel::UpdatePDZForImmediatePullOver() {
  if (!immediate_pull_over_state_manager_.selected_pdz_info().has_value()) {
    immediate_pull_over_state_manager_.set_immediate_pull_over_point(
        std::nullopt);
    pull_over_reference_point_ = math::geometry::Point2d(0.0, 0.0);
    return;
  }

  // Select the pdz from local mode and redirecting mode.
  pickup_dropoff_zone_infos_.push_back(
      immediate_pull_over_state_manager_.selected_pdz_info().value());
  // Update the immediate pull over point by pdz nominal path.
  const math::geometry::PolylineCurve2d& pdz_nominal_path =
      pickup_dropoff_zone_infos_.front().nominal_path;
  const auto adjusted_immediate_pull_over_point = pdz_nominal_path.GetInterp(
      pdz_nominal_path.GetTotalArcLength() - robot_state().GetLength());
  immediate_pull_over_state_manager_.set_immediate_pull_over_point(
      std::make_optional(adjusted_immediate_pull_over_point));
  pull_over_reference_point_ = adjusted_immediate_pull_over_point;
}

void WorldModel::UpdateOrderTrackInfo() {
  const int64_t confirm_id = pull_out_reasoner_seed().confirm_id();
  const bool is_pullout_confirmed =
      pull_out_reasoner_seed().is_confirmed() ||
      std::any_of(
          assist_responses().begin(), assist_responses().end(),
          [&confirm_id](const auto& response) {
            return response.type_case() ==
                       planner::pb::AssistResponse::kPullOutConfirmResponse &&
                   response.pull_out_confirm_response().confirm_id() ==
                       confirm_id;
          });

  if (is_pullout_confirmed && !pull_out_reasoner_seed().is_confirmed()) {
    routing::utility::AddRtEventForPulloutIsConfirmed(
        global_route_solution()->route_solution());
  }
}

void WorldModel::UpdatePickupDropoffZone(
    pb::PickupDropoffDebug* pickup_dropoff_debug,
    pb::ImmediatePullOverDebug* immediate_pull_over_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdatePickupDropoffZone);
  const GlobalRouteSolution* global_route_sol = global_route_solution();
  if (global_route_sol == nullptr) {
    return;
  }

  const auto& destination = global_route_sol->route_solution().destination();
  // If the dest is same as the cached dest and pdz info is not empty,
  // pdz info has been updated, so directly use pdz info in last frame.
  // Otherwise, the cached dest and pdz info is out of sync with the new routing
  // dest or new immediate pull over point, pdz info should be cleared.
  if (!pull_over_request_info_.ShouldUpdatePickupDropoffZoneInfo(
          *global_route_sol, immediate_pull_over_point(),
          immediate_pullover_state(),
          ShouldResponseToTriggeredImmediatePullOver())) {
    pull_over_request_info_.set_is_loopback_route(
        global_route_sol->route_solution().is_loopback_route());
    pull_over_request_info_.set_mission_id(
        global_route_sol->route_solution().mission_id());
    UpdatePickupDropoffZonesDebug(pickup_dropoff_zone_infos_, destination,
                                  pickup_dropoff_debug);
    UpdateVirtualPickupDropoffDebug(
        pickup_dropoff_zone_infos_.front().raw_stop_cells,
        pickup_dropoff_debug);
    return;
  }

  pull_over_request_info_.UpdateRouteInfo(
      *global_route_sol, immediate_pull_over_point(),
      immediate_pull_over_state_manager().immediate_pullover_source(),
      ShouldResponseToTriggeredImmediatePullOver(), match_type(),
      GetOddInfo().odd_info.odd_type());
  pickup_dropoff_zone_infos_.clear();

  // Update pdz if immediate pull over is triggered.
  if (ShouldResponseToTriggeredImmediatePullOver()) {
    UpdatePDZForImmediatePullOver();
    pull_over_request_info_.set_pickup_dropoff_zone_info(
        pickup_dropoff_zone_infos_);
    pull_over_request_info_.set_immediate_pull_over_point(
        immediate_pull_over_point());
    if (!pickup_dropoff_zone_infos_.empty()) {
      pull_over_reference_point_ =
          pull_over_request_info_.pull_over_reference_point();
      immediate_pull_over_state_manager_.UpdateImmediatePullOverDebug(
          immediate_pull_over_debug, snapshot_timestamp());
      UpdatePickupDropoffZonesDebug(pickup_dropoff_zone_infos_, destination,
                                    pickup_dropoff_debug);
      UpdateVirtualPickupDropoffDebug(
          pickup_dropoff_zone_infos_.front().raw_stop_cells,
          pickup_dropoff_debug);
      return;
    }
  }

  const pnc_map::Road* dest_road_ptr =
      GetRoadOfDestination(*global_route_sol, regional_map_);
  if (dest_road_ptr == nullptr) {
    return;
  }

  // Update cached destination point and pdz info when the dest road is in
  // regional map.
  pull_over_reference_point_ =
      pull_over_request_info_.pull_over_reference_point();
  const bool is_updating_pdz_for_new_dest =
      !(math::IsApprox(cached_routing_dest_point_.x(),
                       destination.position().x()) &&
        math::IsApprox(cached_routing_dest_point_.y(),
                       destination.position().y()));
  cached_routing_dest_point_ = pull_over_request_info_.routing_dest_point();

  const bool is_virtual_stop =
      std::any_of(destination.attributes().begin(),
                  destination.attributes().end(), [](const auto& attribute) {
                    return attribute == order::pb::RideStop::kVirtual;
                  });
  // If virtual stop, update pdz info by stop cells.
  if (is_virtual_stop) {
    if (FLAGS_planning_enable_check_stop_cell_layer_in_simulation &&
        av_comm::InSimulation()) {
      DCHECK(pnc_map_service()->hdmap()->IsStopCellLayerReady());
    }

    std::vector<hdmap::StopCellResult> stop_cell_results;
    std::vector<order::pb::CellInfo> cell_infos_from_cloud;
    // Construct stop cell results by cloud info first if stop cell info from
    // cloud is allowed to used and it is not immediate pullover.
    if (FLAGS_planning_use_stop_cell_from_cloud &&
        !immediate_pull_over_point().has_value() &&
        (!av_comm::InSimulation() ||
         FLAGS_planning_use_cloud_cell_in_simulation)) {
      stop_cell_results =
          ConstructStopCellResultsByCloudInfo(destination, *pnc_map_service());
      // If there are stop_cell_results from cloud, update
      // cell_infos_from_cloud.
      if (!stop_cell_results.empty()) {
        cell_infos_from_cloud =
            GetCellInfosFromCloud(destination, immediate_pull_over_point());
      } else {
        pb::PDZGenerationFailureReasonDebug* failure_reason_debug =
            pickup_dropoff_debug->add_failure_reasons();
        failure_reason_debug->set_type(
            pb::PDZGenerationFailureReasonDebug::kNoCloudCells);
      }
    }
    // Get stop cell result by onboard SDK if there is no stop cell result
    // from cloud.
    if (stop_cell_results.empty()) {
      stop_cell_results = GetStopCellResultsFromOnboard(
          pull_over_request_info_.pull_over_reference_point(),
          *pnc_map_service(), *dest_road_ptr,
          /*forbidden_stop_types_for_mrc=*/{},
          pull_over_request_info_.odd_type());
      if (stop_cell_results.empty() ||
          stop_cell_results.front().stop_cells().empty()) {
        pb::PDZGenerationFailureReasonDebug* failure_reason_debug =
            pickup_dropoff_debug->add_failure_reasons();
        failure_reason_debug->set_type(
            pb::PDZGenerationFailureReasonDebug::kNoLocalCells);
        if (!stop_cell_results.empty()) {
          failure_reason_debug->set_error_message(
              stop_cell_results.front().Utf8DebugString());
        }
      }
    }
    UpdateVirtualPickupDropoffDebug(stop_cell_results, pickup_dropoff_debug);

    pickup_dropoff_zone_infos_ = regional_parking_zone_generator_ptr_->Update(
        pull_over_request_info_.routing_dest_point(), pnc_map_service(),
        dest_road_ptr, immediate_pull_over_point(), stop_cell_results,
        cell_infos_from_cloud, pickup_dropoff_debug);
  }

  if (pickup_dropoff_debug != nullptr &&
      !pickup_dropoff_debug->failure_reasons().empty() &&
      is_updating_pdz_for_new_dest) {
    std::string failure_reason_str = "";
    for (const auto& failure_reason : pickup_dropoff_debug->failure_reasons()) {
      failure_reason_str += pb::PDZGenerationFailureReasonDebug::Type_Name(
                                failure_reason.type()) +
                            ";";
    }
    routing::utility::AddRtEventForPDZGenerationFailure(
        global_route_sol->route_solution(), GetOddInfo().odd_info.odd_type(),
        failure_reason_str);
  }

  // If not virtual stop, update pdz info by labelled pdz in Hdmap.
  if (pickup_dropoff_zone_infos_.empty() && !is_virtual_stop) {
    pickup_dropoff_zone_infos_ = pickup_dropoff_zone_generator_ptr_->Update(
        *GetLatestJointPncMapService(), *global_route_sol, dest_road_ptr,
        regional_map().zones, pickup_dropoff_debug);
  }

  pull_over_request_info_.set_pickup_dropoff_zone_info(
      pickup_dropoff_zone_infos_);
  UpdatePickupDropoffZonesDebug(pickup_dropoff_zone_infos_, destination,
                                pickup_dropoff_debug);
}

void WorldModel::UpdateDestinationSuccessorSectionsInfo(
    pb::DestinationSuccessorSectionsInfoDebug* debug) {
  const GlobalRouteSolution* global_route_sol = global_route_solution();
  if (global_route_sol == nullptr) {
    destination_successor_sections_for_pullover_.clear();
    pullover_final_extend_section_percentage_ = 0.0;
    UpdateDestinationSuccessorSectionsInfoDebug(
        destination_successor_sections_for_pullover_,
        pullover_final_extend_section_percentage_, debug);
    return;
  }

  // If immediate pull over is triggered by planning and the route solution
  // hasn't been a loopback route, we latch the results of the last cycle to
  // extend the sections.
  const bool should_latch_last_cycle_result =
      immediate_pull_over_state_manager().IsTriggeredByPlanning() &&
      !global_route_sol->route_solution().is_desired_loopback_route();
  if (should_latch_last_cycle_result) {
    UpdateDestinationSuccessorSectionsInfoDebug(
        destination_successor_sections_for_pullover_,
        pullover_final_extend_section_percentage_, debug);
    return;
  }

  destination_successor_sections_for_pullover_.clear();
  pullover_final_extend_section_percentage_ = 0.0;
  if (pickup_dropoff_zone_infos().empty() ||
      ShouldResponseToTriggeredImmediatePullOver()) {
    return;
  }

  // If planner terminate planning-source or routing-source immediate pullover,
  // which indicates that it has decided to use a loop route, don't calculate
  // destination successor sections info.
  if (immediate_pull_over_state_manager()
          .should_ignore_or_terminate_request() &&
      (immediate_pull_over_state_manager().immediate_pullover_source() ==
           pb::ImmediatePullOverTriggerSource::kByRouting ||
       immediate_pull_over_state_manager().immediate_pullover_source() ==
           pb::ImmediatePullOverTriggerSource::kByPlanning)) {
    return;
  }

  const auto& pdz_info = pickup_dropoff_zone_infos_.front();
  const auto& pdz_nominal_path = pdz_info.extended_nominal_path.has_value()
                                     ? pdz_info.extended_nominal_path.value()
                                     : pdz_info.nominal_path;
  // Calculate the arc length of the maximum extension distance.
  math::geometry::Point2d max_extend_distance_point =
      math::geometry::Point2d(0.0, 0.0);
  if (pdz_info.pickup_dropoff_zone_type == PickupDropoffZoneType::NORMAL) {
    const double max_extend_distance_arclength =
        pdz_nominal_path.GetTotalArcLength() +
        kMaxDistanceToExtendDestinationSuccessorSectionsInMeter;
    max_extend_distance_point =
        pdz_nominal_path.GetInterp(max_extend_distance_arclength);
  } else {
    // Calculate the arclength of raw stop cells end point.
    if (pdz_info.raw_stop_cells.empty() ||
        pdz_info.raw_stop_cells.back().assistant_line().points().empty()) {
      return;
    }
    max_extend_distance_point =
        math::geometry::Convert<math::geometry::Point2d>(
            *(pdz_info.raw_stop_cells.back().assistant_line().points().end() -
              1));
  }

  const auto destination_iter =
      std::find_if(pdz_info.extended_overlapped_lanes.begin(),
                   pdz_info.extended_overlapped_lanes.end(),
                   [&global_route_sol](const pnc_map::Lane* lane) {
                     return lane != nullptr && lane->section() != nullptr &&
                            global_route_sol->IsGlobalDestinationSection(
                                lane->section()->id());
                   });
  if (destination_iter == pdz_info.extended_overlapped_lanes.end()) {
    return;
  }
  // Init the extend section percentage.
  pullover_final_extend_section_percentage_ = 1.0;
  destination_successor_sections_for_pullover_.reserve(
      static_cast<int>(pdz_info.extended_overlapped_lanes.size()));
  for (auto iter = destination_iter;
       iter != pdz_info.extended_overlapped_lanes.end(); ++iter) {
    const pnc_map::Section* extened_section = (*iter)->section();
    if (!destination_successor_sections_for_pullover_.empty() &&
        extened_section->id() ==
            destination_successor_sections_for_pullover_.back()->id()) {
      // The section has already been pushed into extend sections.
      continue;
    }
    destination_successor_sections_for_pullover_.push_back(extened_section);
    // Re calculate the extend section percentage.
    const auto query_info = (*iter)->center_line().GetProximity(
        max_extend_distance_point, math::pb::UseExtensionFlag::kForbid);
    if (query_info.relative_position == math::RelativePosition::kWithIn) {
      pullover_final_extend_section_percentage_ =
          query_info.arc_length / (*iter)->center_line().GetTotalArcLength();
      break;
    }
  }

  // Reset the extend section percentage if destination successor sections is
  // empty.
  if (destination_successor_sections_for_pullover_.empty()) {
    pullover_final_extend_section_percentage_ = 0.0;
  }

  UpdateDestinationSuccessorSectionsInfoDebug(
      destination_successor_sections_for_pullover_,
      pullover_final_extend_section_percentage_, debug);
}

void WorldModel::SetAssociatedRouteForObjectPredictedTrajectories(
    const pb::DecoupledManeuverSeed& prev_maneuver_seed,
    const std::vector<LaneSequenceResult>& lane_sequence_results,
    pb::PlanningDebug* planning_debug) {
  TRACE_EVENT_SCOPE(planner, BP_RouteAssociation);
  const double min_likelihood_for_cautious =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .speed_generator_config()
          .reasoning()
          .prediction_decision_maker()
          .vehicles()
          .min_likelihood_for_cautious();

  const auto prev_agent_map_element_occupancy_seed_ptr =
      Seed::Access<token::AgentMapElementOccupancySeeds>::GetMsg(
          PathLastFinishedFrame());

  const bool is_under_target_scene =
      FLAGS_planning_enable_agent_predicted_trajectory_route_association_considered_by_speed ||
      IsTargetSceneToRunGlobalLaneAssociation(lane_sequence_results);

  auto& object_prediction_map =
      global_object_manager_->mutable_object_prediction_map();

  // Iterate through each trajectory to do route association.
  // NOTE: We currently do global route association only for vehicles.
  for (auto& [object_id, predicted_trajectories] : object_prediction_map) {
    // Skip non-vehicle agents.
    const bool is_vehicle =
        !predicted_trajectories.empty() &&
        predicted_trajectories.front().tracked_object().object_type() ==
            voy::perception::VEHICLE;
    if (!is_vehicle) {
      continue;
    }
    auto iter = std::find_if(
        predicted_trajectories.begin(), predicted_trajectories.end(),
        [](const PredictedTrajectoryWrapper& predicted_trajectory) {
          return predicted_trajectory.is_primary_trajectory();
        });
    DCHECK(iter != predicted_trajectories.end());
    PredictedTrajectoryWrapper& primary_predicted_trajectory = *iter;
    if (!FLAGS_planning_enable_agent_predicted_trajectory_route_association) {
      const bool was_considered_by_speed = WasAgentConsideredInSpeedSeed(
          prev_maneuver_seed.speed_seed(), object_id);
      if (was_considered_by_speed && is_under_target_scene) {
        for (PredictedTrajectoryWrapper& predicted_trajectory :
             predicted_trajectories) {
          if (predicted_trajectory.is_multi_output_trajectory() &&
              (predicted_trajectory.Likelihood() >=
                   min_likelihood_for_cautious ||
               predicted_trajectory.is_primary_trajectory())) {
            route_association::PredictedTrajectoryRouteAssociator
                association_result{
                    predicted_trajectory, *GetLatestJointPncMapService(),
                    *prev_agent_map_element_occupancy_seed_ptr,
                    planning_debug ? planning_debug->mutable_world_model_debug()
                                   : nullptr};
            PopulateRouteAssociationResultInPredictedTrajectoryWrapper(
                association_result, predicted_trajectory);
          }
        }
      } else {
        // Run partial lane association inferred by the first pose.
        route_association::PredictedTrajectoryRouteAssociator
            association_result{
                primary_predicted_trajectory,
                /*sampled_pose_indices=*/{0}, *GetLatestJointPncMapService(),
                *prev_agent_map_element_occupancy_seed_ptr,
                planning_debug ? planning_debug->mutable_world_model_debug()
                               : nullptr};
        PopulateRouteAssociationResultInPredictedTrajectoryWrapper(
            association_result, primary_predicted_trajectory);
      }
    } else {
      for (PredictedTrajectoryWrapper& predicted_trajectory :
           predicted_trajectories) {
        if (predicted_trajectory.is_multi_output_trajectory() &&
            (predicted_trajectory.Likelihood() >= min_likelihood_for_cautious ||
             predicted_trajectory.is_primary_trajectory())) {
          route_association::PredictedTrajectoryRouteAssociator
              association_result{
                  predicted_trajectory, *GetLatestJointPncMapService(),
                  *prev_agent_map_element_occupancy_seed_ptr,
                  planning_debug ? planning_debug->mutable_world_model_debug()
                                 : nullptr};
          PopulateRouteAssociationResultInPredictedTrajectoryWrapper(
              association_result, predicted_trajectory);
        }
      }
    }
  }
}

void WorldModel::UpdateConstructionZoneMap() {
  auto& construction_zone_map =
      global_object_manager_->mutable_construction_zone_map();
  construction_zone_map.clear();
  for (const auto& construction_zone :
       global_object_manager_->construction_zones()) {
    construction_zone_map.emplace(construction_zone.id, &construction_zone);
  }
}

std::vector<const pnc_map::Lane*> WorldModel::GetImmutableLaneSequence() const {
  std::vector<const pnc_map::Lane*> immutable_lane_sequence =
      GetLatestJointPncMapService()->GetLaneSequence(
          last_seed()
              .lane_sequence_plan_init_state()
              .immutable_lane_sequence());
  if (std::any_of(immutable_lane_sequence.begin(),
                  immutable_lane_sequence.end(),
                  [](const pnc_map::Lane* lane) { return lane == nullptr; })) {
    return std::vector<const pnc_map::Lane*>();
  }
  return immutable_lane_sequence;
}

bool WorldModel::SetLastSelectedLaneSequence(
    const ::google::protobuf::RepeatedField<::google::protobuf::int64>&
        last_selected_lane_sequence_lane_ids) {
  last_selected_lane_sequence_ = GetLatestJointPncMapService()->GetLaneSequence(
      last_selected_lane_sequence_lane_ids);
  std::optional<int64_t> not_found_lane_id = std::nullopt;

  for (size_t i = 0; i < last_selected_lane_sequence_.size(); ++i) {
    const pnc_map::Lane* lane = last_selected_lane_sequence_.at(i);
    if (lane == nullptr || lane->section() == nullptr) {
      // Erase nullptr lane to prevent simulation crash.
      last_selected_lane_sequence_.erase(
          last_selected_lane_sequence_.begin() + i,
          last_selected_lane_sequence_.end());
      not_found_lane_id = last_selected_lane_sequence_lane_ids.at(i);
      break;
    }
  }

  const bool valid_last_selected_lane_sequence =
      (not_found_lane_id == std::nullopt);
  if (!valid_last_selected_lane_sequence) {
    LOG(ERROR) << "PncMapService failed to fully construct last selected lane "
                  "sequence. Lane "
               << not_found_lane_id.value() << " not found.";
  }
  return valid_last_selected_lane_sequence;
}

void WorldModel::SetUpEmptyRoute(
    const pnc_map::PncMapService& pnc_map_service) {
  route_model_.SetUpEmptyRoute(pnc_map_service);
}

void WorldModel::ResetPullOutJumpOutSeedIfNewMission() {
  const auto previous_seed =
      Seed::Access<token::PullOutJumpOutSeed>::GetMsg(PathLastFinishedFrame());
  auto mutable_pull_out_jump_out_seed =
      Seed::Access<token::PullOutJumpOutSeed>::MutableMsg(PathCurrentFrame());
  int64_t pull_out_jump_out_seed_mission_id = previous_seed->mission_id();

  if (pull_out_jump_out_seed_mission_id ==
      global_route_solution()->route_solution().mission_id()) {
    if (!FLAGS_planning_copy_previous_seed) {
      // same mission, copy over if not copied as part of whole planning
      // seed
      mutable_pull_out_jump_out_seed->CopyFrom(*previous_seed);
    }
    return;
  }
  // else reset.
  mutable_pull_out_jump_out_seed->set_mission_id(
      global_route_solution()->route_solution().mission_id());
  mutable_pull_out_jump_out_seed->set_has_entered_jump_out_sequence(false);
  mutable_pull_out_jump_out_seed->mutable_last_jump_out_current_lanes()
      ->Clear();
  mutable_pull_out_jump_out_seed->mutable_last_jump_out_lane_sequence()
      ->Clear();
}

std::optional<double> WorldModel::OrderStartPositionDistance() const {
  if (global_route_solution() == nullptr) {
    LOG(WARNING) << "Global route solution is not available.";
    return std::nullopt;
  }
  const math::geometry::Point2d ego_position(
      robot_state().current_state_snapshot().x(),
      robot_state().current_state_snapshot().y());
  return global_route_solution()->GetDistanceFromRideStartPoseToEgo(
      ego_position);
}

bool WorldModel::IsEgoPositionOnRouteReadyForPullOut() const {
  // TODO(Zixuan): Add specific jump out lane sequence generation trigger logic
  // for more rigid control on pull out jump out behavior. For now, pull out in
  // planning and jump out lane generation in routing shares this same logic.
  // TODO(Huoliang): Seed some data to get the same distance to destination with
  // the road test in simulation.
  if (global_route_solution() == nullptr) {
    LOG(WARNING) << "Global route solution is not available.";
    return false;
  }
  const auto& start_position_distance = OrderStartPositionDistance();
  return start_position_distance.has_value() &&
         (start_position_distance.value() <
          kMaxPullOutDistanceFromEgoStartPositionInMeter) &&
         (global_route_solution()->route_solution().total_dist_m() >
          kMinPullOutDistanceToRouteDestinationInMeter) &&
         route_model().is_ego_start_close_to_stop();
}

void WorldModel::SetIfShouldGeneratePullOutJumpOutSequence(
    bool has_entered_jump_out_sequence) {
  const bool is_ego_position_on_route_ready_for_pull_out =
      IsEgoPositionOnRouteReadyForPullOut();
  if (!has_entered_jump_out_sequence &&
      !is_ego_position_on_route_ready_for_pull_out) {
    const std::string debug_info =
        "Ego position is not ready for pull out, and should not "
        "generate jump out sequence.";
    rt_event::PostRtEvent<rt_event::planner::PullOutEgoPositionNotReady>(
        debug_info);
  }
  should_generate_pull_out_jump_out_sequence_ =
      FLAGS_planning_enable_decoupled_pull_out &&
      !has_entered_jump_out_sequence &&
      is_ego_position_on_route_ready_for_pull_out && !HasPullOutFinished();
}

void WorldModel::UpdateShouldTriggerPullOutByMrc(
    const pb::WorldModelSeed& previous_world_model_seed) {
  if (should_planner_respond_mrc_ == false) {
    should_trigger_pull_out_by_mrc_ = false;
    return;
  }

  if (mrc_request_ptr() == nullptr) {
    should_trigger_pull_out_by_mrc_ = false;
    return;
  }

  // In the last cycle, mrm_type should be MRM_HARBOR or MRM_PULLRIGHT, and
  // mrm_progress should be FINISHED.
  if (previous_world_model_seed.mrm_type() != mrc::pb::MrmType::MRM_HARBOR &&
      previous_world_model_seed.mrm_type() != mrc::pb::MrmType::MRM_PULLRIGHT) {
    should_trigger_pull_out_by_mrc_ = false;
    return;
  }
  if (previous_world_model_seed.mrm_progress() !=
      mrc::pb::MrcProgress::FINISHED) {
    should_trigger_pull_out_by_mrc_ = false;
    return;
  }

  should_trigger_pull_out_by_mrc_ =
      mrc_request_ptr()->mrm_progress() == mrc::pb::MrcProgress::NONE;
}

// TODO(liwen): We would move this function to the `pullout_reasoner` later, as
// the operation and logic inside the fuction is mostly related to the pull out
// logic, and use the pull out finish state saved in the pull out seed in other
// code place if needed.
bool WorldModel::HasPullOutFinished() const {
  if (global_route_solution() == nullptr) {
    LOG(WARNING) << "Global route solution is not available.";
    return false;
  }
  auto& prev_pull_out_reasoner_seed = pull_out_reasoner_seed();
  if (global_route_solution()->route_solution().mission_id() !=
      prev_pull_out_reasoner_seed.route_mission_id()) {
    LOG(INFO) << "mission ID changed from "
              << prev_pull_out_reasoner_seed.route_mission_id() << " to "
              << global_route_solution()->route_solution().mission_id();
    // When the new route solution arrives, we should reset the pull out finish
    // state, and here we return false to match the pull out seed update logic
    // in the cycle.
    return false;
  }
  // Set false if pull out should be triggered by MRC.
  if (should_trigger_pull_out_by_mrc_) {
    return false;
  }
  if (prev_pull_out_reasoner_seed.has_pull_out_finished_for_current_route()) {
    // If the "pull out finish" flag in the seed has been set, it means in the
    // current route solution, the pull out has been finished.
    return true;
  }

  const std::optional<double> start_position_distance =
      OrderStartPositionDistance();
  if (!start_position_distance.has_value()) {
    // When the order start position distance is not set, it means that there
    // might be no route solution, the pull out should not be triggered, so we
    // resolve the exception with regarding it as "has not finished".
    LOG(ERROR) << "The start position has no value.";
    CHECK(!IsEgoPositionOnRouteReadyForPullOut());
    return false;
  }

  if (!prev_pull_out_reasoner_seed.is_confirmed()) {
    // Validate ego state before pull out is confirmed and early terminate pull
    // out before confirmation when ego speed is too high or has already
    // diverted from order start position by too far excepting the pull out is
    // triggered by mrc.
    // NOTE: This is a fallback logic typically for handling
    // order destination change while ego is driving mid-trip.
    static constexpr double
        kSpeedLimitForPullOutEarlyFinishBeforeConfirmationInMps =
            2.78;  // 10km/h
    static constexpr double
        kMaxTravelDistForPullOutEarlyFinishBeforeConfirmationInMeter = 20.0;
    return robot_state().plan_init_state_snapshot().speed() >
               kSpeedLimitForPullOutEarlyFinishBeforeConfirmationInMps ||
           (start_position_distance.value() >
                kMaxTravelDistForPullOutEarlyFinishBeforeConfirmationInMeter &&
            prev_pull_out_reasoner_seed.trigger_reason() !=
                speed::pb::PullOutTriggerReason::kMrc);
  }

  CHECK_GE(
      prev_pull_out_reasoner_seed.distance_to_ride_start_pos_at_confirmation(),
      0.0);

  const auto& prev_pull_out_selection_seed = pull_out_selection_seed();
  if (prev_pull_out_reasoner_seed.is_ready_to_go()) {
    // If there has no jump out candidate, pull out can be finished once ego
    // is ready to go. Note: the seed has_pull_out_jump_out_candidate should has
    // value firstly.
    if (prev_pull_out_selection_seed.has_has_pull_out_jump_out_candidate() &&
        !prev_pull_out_selection_seed.has_pull_out_jump_out_candidate()) {
      return true;
    }

    // If pull out unstuck planner is active, pull out should not be
    // finished. The reason is that the unstuck planner relies on the jump out
    // candidates. Early finish pull out may lead to no jump out candidates and
    // unstuck failure.
    const auto unstuck_planner_seed =
        Seed::Access<token::UnstuckPlannerSeed>::GetMsg(
            PathLastFinishedFrame());
    if (unstuck_planner_seed->current_state() !=
            planner::pb::UnstuckPlannerState::IDLE &&
        unstuck_planner_seed->unstuck_scene_type() ==
            planner::pb::UnstuckSceneType::PULLOUT) {
      return false;
    }

    // If there has only left lane change routing advantage when ego dose not
    // pull out from non-vehicle lane and bus bulb, pull out can be finished
    // once ego is ready to go.
    if (prev_pull_out_selection_seed
            .should_finish_pull_out_by_routing_incentive()) {
      return true;
    }
  }

  // Pull out can be finished when ego is 10m far from the pull
  // out start position (ego position at confirmation) when it is not jumping
  // out.
  LOG(INFO) << start_position_distance.value() << " "
            << prev_pull_out_reasoner_seed
                   .distance_to_ride_start_pos_at_confirmation();
  return !prev_pull_out_reasoner_seed.was_pull_out_jump_out() &&
         start_position_distance.value() -
                 prev_pull_out_reasoner_seed
                     .distance_to_ride_start_pos_at_confirmation() >
             kPullOutTerminationDrivingDistanceInMeter;
}

const speed::pb::PulloutReasonerSeed& WorldModel::pull_out_reasoner_seed()
    const {
  return Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
             PathLastFinishedFrame())
      ->speed_seed()
      .speed_reasoner_seeds()
      .pullout_reasoner_seed();
}

const planner::pb::PullOutSelectionSeed& WorldModel::pull_out_selection_seed()
    const {
  return Seed::Access<token::SelectionSeed>::GetMsg(PathLastFinishedFrame())
      ->pull_out_selection_seed();
}

const PredictedTrajectoryWrapper& WorldModel::FindPrimaryTrajectory(
    ObjectId object_id) const {
  const auto& object_prediction_map =
      global_object_manager_->object_prediction_map();

  const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories =
      object_prediction_map.at(object_id);
  const auto& primary_trajectory =
      std::find_if(predicted_trajectories.begin(), predicted_trajectories.end(),
                   [](const PredictedTrajectoryWrapper& predicted_trajectory) {
                     return predicted_trajectory.is_primary_trajectory();
                   });
  DCHECK(primary_trajectory != predicted_trajectories.end());
  return *primary_trajectory;
}

void WorldModel::SetCurrentLaneOnLastSelectedLaneSequence() {
  const math::geometry::Point2d& ego_pose =
      robot_state().current_state_snapshot().front_bumper_position();
  const auto current_lane_iter = std::find_if(
      last_selected_lane_sequence().cbegin(),
      last_selected_lane_sequence().cend(),
      [&ego_pose](const pnc_map::Lane* lane_ptr) {
        return math::geometry::Within(ego_pose, lane_ptr->border());
      });
  if (current_lane_iter == last_selected_lane_sequence().cend()) {
    current_lane_on_last_selected_lane_sequence_ = nullptr;
    return;
  }
  current_lane_on_last_selected_lane_sequence_ = *current_lane_iter;
}

void WorldModel::UpdateTrafficSignalReasonerDebug(
    pb::TrafficSignalReasonerDebug* mutable_traffic_signal_reasoner_debug) {
  if (!mutable_traffic_signal_reasoner_debug) {
    return;
  }

  // Update debug for signal controlled variable lanes.
  pb::SignalControlledVariableLaneDebug* mutable_variable_lane_debug =
      mutable_traffic_signal_reasoner_debug
          ->mutable_signal_controlled_variable_lane_info();
  traffic_signal_reasoner_.UpdateSignalControlledVariableLaneDebug(
      mutable_variable_lane_debug);
}

void WorldModel::UpdateDrivableLaneReasonerDebug(
    pb::DrivableLaneReasonerDebug* mutable_drivable_lane_reasoner_debug) const {
  if (!mutable_drivable_lane_reasoner_debug) {
    return;
  }

  // Update debug for mandatory drivable or undrivable lanes.
  pb::DirvableLaneReasonerState* mutable_mandatory_drivable_info_debug =
      mutable_drivable_lane_reasoner_debug
          ->mutable_mandatory_drivable_reasoner_state();
  drivable_lane_reasoner().DumpState(*mutable_mandatory_drivable_info_debug);
}

void WorldModel::UpdateMapChangeAreaProcessorDebug(
    pb::MapChangeAreaProcessorDebug* mutable_map_change_area_processor_debug) {
  if (mutable_map_change_area_processor_debug == nullptr) {
    return;
  }
  map_change_area_processor_.param().DumpState(
      *mutable_map_change_area_processor_debug
           ->mutable_map_change_area_processor_param_debug());
}

void WorldModel::UpdateChangedLaneStructureReasonerDebug(
    pb::ChangedLaneStructureReasonerState*
        mutable_changed_lane_structure_debug) {
  if (mutable_changed_lane_structure_debug == nullptr) {
    return;
  }
  changed_lane_structure_reasoner_.DumpState(
      *mutable_changed_lane_structure_debug);
}

void WorldModel::UpdateChangedMapElements(
    pnc_map::pb::ChangedElements* changed_elements) {
  changed_lane_structure_reasoner_.Update(
      stuck_avoidance_reasoner_.stuck_avoidance_parameter(),
      global_route_solution() != nullptr
          ? global_route_solution()->global_route_lanes()
          : std::vector<const pnc_map::Lane*>{},
      current_lane_candidates_for_regional_path());

  const std::optional<int64_t> latest_hdmap_changed_data_timestamp =
      (FLAGS_planning_enable_hdmap_dynamic_data_update ||
       FLAGS_planning_enable_shadow_mode_hdmap_dynamic_data_update) &&
              !latest_snapshot_ptr_->hdmap_changed_data_list.empty()
          ? std::make_optional(
                latest_snapshot_ptr_->hdmap_changed_data_list.back()
                    .message->timestamp_ms())
          : std::nullopt;
  const std::optional<int64_t> latest_planner_changed_data_timestamp =
      FLAGS_planning_enable_stuck_avoidance_dynamic_map_data_update
          ? std::make_optional(
                changed_lane_structure_reasoner_.latest_data_timestamp())
          : std::nullopt;

  const int64_t latest_changed_data_timestamp =
      GetLatestJointPncMapService()->GetChangedDataTimestamp();

  // Planner data is invalid, populate hdmap data if it's enabled.
  if (!latest_planner_changed_data_timestamp.has_value() ||
      latest_planner_changed_data_timestamp.value() < 0 ||
      (changed_lane_structure_reasoner_.temp_lanes().empty() &&
       latest_planner_changed_data_timestamp.value() <=
           latest_changed_data_timestamp)) {
    if (FLAGS_planning_enable_hdmap_dynamic_data_update ||
        FLAGS_planning_enable_shadow_mode_hdmap_dynamic_data_update) {
      PopulatePncMapHistoryBuffer(latest_snapshot_ptr_->hdmap_changed_data_list,
                                  changed_elements);
    }
    return;
  }

  // Only planner data is valid, update if it's new.
  if (!latest_hdmap_changed_data_timestamp.has_value() &&
      latest_planner_changed_data_timestamp.has_value() &&
      latest_planner_changed_data_timestamp.value() >
          latest_changed_data_timestamp) {
    const hdmap::ChangedData changed_data =
        changed_lane_structure_reasoner_.PopulateChangedData(
            /*latest_hdmap_changed_data=*/nullptr);
    PopulatePncMapHistoryBuffer(
        {SnapshotData(std::make_shared<const hdmap::ChangedData>(changed_data),
                      std::make_optional(changed_data.timestamp_ms()))},
        changed_elements);
    return;
  }

  // Both are valid data, update if any one is new.
  if (latest_hdmap_changed_data_timestamp.has_value() &&
      latest_planner_changed_data_timestamp.has_value() &&
      (latest_hdmap_changed_data_timestamp.value() >
           latest_changed_data_timestamp ||
       latest_planner_changed_data_timestamp.value() >
           latest_changed_data_timestamp)) {
    std::vector<SnapshotData<const hdmap::ChangedData>> fused_changed_data_list;
    if (latest_snapshot_ptr_->hdmap_changed_data_list.size() > 1) {
      std::copy(latest_snapshot_ptr_->hdmap_changed_data_list.begin(),
                latest_snapshot_ptr_->hdmap_changed_data_list.end() - 1,
                std::back_inserter(fused_changed_data_list));
    }
    const SnapshotData<const hdmap::ChangedData>& latest_hdmap_changed_data =
        latest_snapshot_ptr_->hdmap_changed_data_list.back();
    const hdmap::ChangedData latest_changed_data =
        changed_lane_structure_reasoner_.PopulateChangedData(
            latest_hdmap_changed_data.message.get());
    std::optional<int64_t> publish_timestamp_in_ns =
        latest_hdmap_changed_data.publish_timestamp_in_ns.has_value()
            ? latest_hdmap_changed_data.publish_timestamp_in_ns
            : std::nullopt;
    fused_changed_data_list.emplace_back(
        std::make_shared<const hdmap::ChangedData>(latest_changed_data),
        std::move(publish_timestamp_in_ns));

    PopulatePncMapHistoryBuffer(fused_changed_data_list, changed_elements);
  }
}

void WorldModel::PopulatePncMapHistoryBuffer(
    const std::vector<SnapshotData<const hdmap::ChangedData>>&
        hdmap_changed_data_list,
    pnc_map::pb::ChangedElements* changed_elements) {
  TRACE_EVENT_SCOPE(planner, WorldModel_PopulatePncMapHistoryBuffer);
  const auto& perception_traffic_lights = traffic_light_detection();
  if (hdmap_changed_data_list.empty() &&
      perception_traffic_lights.traffic_lights_size() > 0) {
    // This is the case where lights message arrives before the first
    // ChangedData message.
    hdmap::ChangedData empty_changed_data;
    empty_changed_data.set_timestamp_ms(pnc_map::kTimestampForOfflineMap);
    pnc_map_history_buffer_.EmplaceBackIfNew(
        empty_changed_data, /*publish_timestamp_in_ns=*/std::nullopt,
        perception_traffic_lights,
        changed_lane_structure_reasoner_.temp_lanes(),
        /*generate_effective_hb=*/true);
    pnc_map::PopulateAll(*pnc_map_history_buffer_.GetLatestJointPncMapService(),
                         changed_elements);
    return;
  }
  for (const auto& changed_data : hdmap_changed_data_list) {
    pnc_map_history_buffer_.EmplaceBackIfNew(
        *(changed_data.message), changed_data.publish_timestamp_in_ns,
        perception_traffic_lights,
        changed_lane_structure_reasoner_.temp_lanes(),
        /*generate_effective_hb=*/true);
    pnc_map::PopulateAll(*pnc_map_history_buffer_.GetLatestJointPncMapService(),
                         changed_elements);
  }
}

void WorldModel::UpdateViolateLaneMarkingSingletonDebug(
    hdmap::ViolateLaneMarkings* violate_lane_marking_singleton_debug) {
  if (!violate_lane_marking_singleton_debug) {
    return;
  }
  hdmap::util::ViolateLaneMarkingSingleton::Get().DumpToPB(
      violate_lane_marking_singleton_debug);
}

void WorldModel::UpdateStuckAvoidanceReasonerDebug(
    pb::StuckAvoidanceReasonerState* mutable_stuck_avoidance_reasoner_debug) {
  if (!mutable_stuck_avoidance_reasoner_debug) {
    return;
  }
  stuck_avoidance_reasoner_.DumpState(*mutable_stuck_avoidance_reasoner_debug);
}

std::shared_ptr<const pnc_map::JointPncMapService>
WorldModel::GetLatestJointPncMapService() const {
  return FLAGS_planning_enable_hdmap_dynamic_data_update ||
                 FLAGS_planning_enable_stuck_avoidance_dynamic_map_data_update
             ? pnc_map_history_buffer_.GetLatestJointPncMapService()
             : pnc_map_history_buffer_.static_joint_pnc_map_service();
}

OddInfo WorldModel::GetOddInfo() const {
  OddInfo odd_info;
  if (latest_snapshot_ptr_->route_status_ptr == nullptr) {
    LOG(INFO) << "Unable to return odd info because no valid route status.";
    return odd_info;
  }

  if (latest_snapshot_ptr_->route_status_ptr->has_route_meta_data()) {
    odd_info.map_region =
        latest_snapshot_ptr_->route_status_ptr->route_meta_data().region();
    odd_info.map_geometry_version =
        latest_snapshot_ptr_->route_status_ptr->route_meta_data()
            .geometry_version();
  } else {
    ros::param::param<std::string>("/region", odd_info.map_region, "");
    odd_info.map_geometry_version = pnc_map_service()->hdmap()->GetHdMapVersion(
        hdmap::MapLayer::GEOMETRY_LAYER);
  }

  odd_info.car_id = av_comm::CarId::Get().str();
  odd_info.map_cell_version = "-1";

  if (latest_snapshot_ptr_->route_status_ptr->has_ride_route_query()) {
    odd_info.constraint_name =
        latest_snapshot_ptr_->route_status_ptr->ride_route_query()
            .constraint_name();
    if (latest_snapshot_ptr_->route_status_ptr->ride_route_query()
            .has_odd_info()) {
      odd_info.odd_info =
          latest_snapshot_ptr_->route_status_ptr->ride_route_query().odd_info();
    }

    if (latest_snapshot_ptr_->route_status_ptr->ride_route_query()
            .has_route_operation_info()) {
      const routing::pb::RouteOperationInfo::OperationType operation_type =
          latest_snapshot_ptr_->route_status_ptr->ride_route_query()
              .route_operation_info()
              .operation_type();
      switch (operation_type) {
        case routing::pb::RouteOperationInfo::kUnknown:
          odd_info.constraint_team = ConstraintTeam::kUnKnownTeam;

          break;

        case routing::pb::RouteOperationInfo::kTesting:
          odd_info.constraint_team = ConstraintTeam::kRoadTest;
          break;
        case routing::pb::RouteOperationInfo::kOperation:
          odd_info.constraint_team = ConstraintTeam::kOperation;
          break;
        default:
          break;
      }
    }
  }

  return odd_info;
}

void WorldModel::UpdateCurrentLaneStateDebug(
    pb::CurrentLaneStateDebug* current_lane_state_debug) {
  if (!current_lane_state_debug) {
    return;
  }
  if (robot_state().IsInAutonomousMode()) {
    current_lane_state_debug->set_current_lane_reach_destination_state(
        pb::CurrentLaneStateDebug::NOT_SET);
    return;
  }
  if (std::any_of(
          current_lane_associator().current_lanes_for_regional_path().begin(),
          current_lane_associator().current_lanes_for_regional_path().end(),
          [&](const auto& current_lane) {
            return global_route_solution()->GetCostToDestination(
                       *current_lane,
                       global_route_solution()->waypoint_count()) != nullptr;
          })) {
    current_lane_state_debug->set_current_lane_reach_destination_state(
        pb::CurrentLaneStateDebug::REACHABLE);
  } else {
    current_lane_state_debug->set_current_lane_reach_destination_state(
        pb::CurrentLaneStateDebug::NOT_REACHABLE);
  }
}

void WorldModel::UpdatePreviousActionRecorderDebug(
    pb::PreviousActionRecorderDebug* previous_action_recorder_debug) {
  if (!previous_action_recorder_debug) {
    return;
  }

  previous_action_recorder_.UpdatePreviousActionRecorderDebug(
      previous_action_recorder_debug);
}

void WorldModel::UpdateBusQueuingNearBusBulb(
    const math::geometry::PolylineCurve2d& last_nominal_path) {
  if (last_nominal_path.size() < 2) {
    return;
  }
  // Initiate the lanes of original bus near bus bulb, and buses that are in
  // the same lane as those buses near bus bulb.
  std::unordered_set<int64_t> bus_lane_ids;
  std::vector<std::pair<double, ObjectId>> same_lane_buses;
  std::optional<ObjectId> last_bus_id_near_bus_bulb;
  std::optional<double> last_bus_arclength_near_bus_bulb;
  const auto& planner_object_map_ptr =
      global_object_manager_->planner_object_map_ptr();
  for (const auto& [obj_id, obj] : *planner_object_map_ptr) {
    if (!obj.is_bus_near_bus_bulb()) {
      continue;
    }

    for (const auto lane : obj.associated_lanes()) {
      if (lane->type() == hdmap::Lane_LaneType_BIKE) {
        // There is usually a small bike lane inside bus bulb, no need
        // to consider it.
        continue;
      }
      bus_lane_ids.insert(lane->id());
      AddSubsequentLanesWithinHorizon(lane, bus_lane_ids);
    }
    const double arc_length_on_last_nominal_path =
        last_nominal_path
            .GetProximity(obj.center_2d(), math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    same_lane_buses.push_back(
        std::make_pair(arc_length_on_last_nominal_path, obj_id));
    if (!last_bus_arclength_near_bus_bulb.has_value() ||
        last_bus_arclength_near_bus_bulb.value() >
            arc_length_on_last_nominal_path) {
      last_bus_arclength_near_bus_bulb = arc_length_on_last_nominal_path;
      last_bus_id_near_bus_bulb = obj_id;
    }
  }

  // Obtain buses that are in the same lane as those buses near bus bulb.
  for (const auto& [obj_id, obj] : *planner_object_map_ptr) {
    if (obj.is_bus_near_bus_bulb() || !obj.is_bus()) {
      continue;
    }
    for (const auto lane : obj.associated_lanes()) {
      if (bus_lane_ids.count(lane->id()) > 0) {
        const double arc_length_on_last_nominal_path =
            last_nominal_path
                .GetProximity(obj.center_2d(),
                              math::pb::UseExtensionFlag::kForbid)
                .arc_length;
        same_lane_buses.push_back(
            std::make_pair(arc_length_on_last_nominal_path, obj_id));
        continue;
      }
    }
  }

  if (same_lane_buses.size() < 2 || !last_bus_id_near_bus_bulb.has_value()) {
    return;
  }

  std::sort(
      same_lane_buses.begin(), same_lane_buses.end(),
      [](const auto& it1, const auto& it2) { return it1.first < it2.first; });

  SearchAndUpdateQueuingBuses(same_lane_buses, planner_object_map_ptr,
                              last_bus_id_near_bus_bulb.value());
  return;
}

void WorldModel::UpdateShouldPlannerRespondMRC() {
  if (!FLAGS_planning_enable_planner_receive_mrc_node_info) {
    should_planner_respond_mrc_ = false;
    return;
  }

  should_planner_respond_mrc_ =
      FLAGS_planning_enable_planner_respond_mrc_request_on_highway ||
      !IsEgoOnExpressway(pose(), current_lane_on_last_selected_lane_sequence(),
                         pnc_map_service());
}

}  // namespace planner
