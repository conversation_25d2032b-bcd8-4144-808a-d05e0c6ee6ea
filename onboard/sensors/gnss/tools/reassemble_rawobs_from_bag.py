#!/usr/bin/env python3

"""
Author: <PERSON><PERSON>
Date: 2024-10-23
Description: Reassemble raw obs data from bag files.

How to use:
    1. cd {your_repo} and then do boostrap.
    2. source bazel/scripts/setup.sh
      2.1 If miss RawObs, add "message RawObs {bytes raw_data = 1;}" in gps_imu.proto
    3. bazel build //protobuf_python:*
    4. run script
      4.1(by date) python3.7 reassemble_rawobs_from_bag.py --car 14039 --date 20240912
      4.2(by trip) python3.7 reassemble_rawobs_from_bag.py --trip 14081_20240920_093403

"""


import argparse
import trail_sdk
from trail_sdk.common import config as _config

from voy_tempest import bag
import datetime
import voy_vbag as vbag
import os
import sys
from tqdm import tqdm

TRAIL_CONF = _config.Config(
    app_id=7,
    token="17475d2cbc65f4772125542ef93e90ed",
    trail_region=_config.TrailRegion.CN,
)

MS_PER_DAY = 24 * 60 * 60 * 1000


def get_trips_by_date(car, date):
    st = int(datetime.datetime.strptime(date, "%Y%m%d").timestamp() * 1000)
    et = st + MS_PER_DAY
    return get_trips(car, st, et)


def get_trips(car, st, et):
    client = trail_sdk.Client(TRAIL_CONF)
    params = {
        "car_id": str(car),
        "start_time": st,
        "end_time": et,
        "page": 1,
        "size": 2000,
    }
    ret = client.daypack.trip.query(params=params)
    print(f"Total trip nums: {ret['count']}")
    result = []
    for trip in ret["res"]:
        ready, uploaded = trip["trip_ready"], trip["trip_uploaded"]
        trip_st, trip_et = int(trip["start_time"]), int(trip["end_time"])
        if ready and uploaded:
            result.append(
                (
                    trip["car_id"],
                    trip["trip_id"],
                    trip_st,
                    trip_et,
                    trip_et - trip_st,
                    trip["version"],
                )
            )
        else:
            print(f"Trip {trip['trip_id']} is not ready or not uploaded.")
    print(f"Ready and uploaded trips: {len(result)}")
    print(result)
    return result


def download_raw_obs(trip_to_download):
    for trip_id, (bag_file, st, et) in trip_to_download.items():
        try:
            reader = bag.BagReader(
                trip_id=trip_id,
                start_time=st,
                end_time=et,
                topics=["/raw_obs_data"],
                user_location=bag.UserLocation.CN,
            )
            reader.save_to_bag(bag_file)
        except Exception as e:
            print(f"Trip {trip_id} download error: {e}")
            continue
    print("Download Done")


def reassemble_raw_obs(trip_to_download):
    for trip_id, (bag_file, _, _) in trip_to_download.items():
        if not os.path.exists(bag_file):
            print(f"File {bag_file} does not exist, skipping...")
            continue

        with open(f"output/{trip_id}_raw_obs.txt", "ab") as file:
            for _, msg, _ in tqdm(
                vbag.Bag(bag_file, "r").read_messages(
                    topics=["/raw_obs_data"], raw=False
                )
            ):
                file.write(msg.raw_data)
        os.remove(bag_file)
    print("Reassemble Done")


def get_raw_obs_and_assemble(car_id=None, date=None, trip_id=None):
    if not trip_id and not (car_id and date):
        raise ValueError("Either trip_id or both car_id and date must be provided")

    trip_to_download = {}
    if trip_id:
        car_id, date, _ = trip_id.split("_")

    ret = get_trips_by_date(car_id, date)
    for _, trip_id_ret, st, et, _, _ in ret:
        if trip_id and trip_id_ret != trip_id:
            continue
        trip_to_download[trip_id_ret] = (f"{trip_id_ret}.bag", st, et)

    download_raw_obs(trip_to_download)
    reassemble_raw_obs(trip_to_download)


def main():
    parser = argparse.ArgumentParser(description="Process car and date or trip.")
    group = parser.add_mutually_exclusive_group(required=True)
    group.add_argument("--car", type=str, help="Car ID")
    group.add_argument("--trip", type=str, help="Trip ID in car_id_date_st_str format")
    parser.add_argument(
        "--date", type=str, help="Date in YYYYMMDD format", required="--car" in sys.argv
    )

    args = parser.parse_args()

    if args.trip:
        get_raw_obs_and_assemble(trip_id=args.trip)
    else:
        get_raw_obs_and_assemble(car_id=args.car, date=args.date)


if __name__ == "__main__":
    main()
