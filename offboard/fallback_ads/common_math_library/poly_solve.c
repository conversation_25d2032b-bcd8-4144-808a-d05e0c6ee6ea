#include "common_math_library/poly_solve.h"

#include <math.h>
#include <stdbool.h>
#include <stddef.h>
#include <stdint.h>

#include "common_math_library/common_method.h"

// todo, 20250102, to be implemented, use gsl library replace
// Solves a quadratic equation
// a quadratic equation equal to f(x) = fA x^2 + fB x + fC
bool CML_SolveQuadraticEquation(const float coeff_a, const float coeff_b,
                                const float coeff_c, const float default_root,
                                float root_array[], uint8_t *root_num) {
  bool enable_to_get_roots = true;

  // pointers are valid
  if ((root_array != NULL) && (*root_num >= 2U)) {
    // set default values for no solution
    root_array[0] = default_root;
    root_array[1] = default_root;

    // linear equation with one unknown
    if (fabsf(coeff_a) < CML_EPSILON) {
      // equation is only constant
      if (fabsf(coeff_b) < CML_EPSILON) {
        // the equation has countless roots
        if (fabsf(coeff_c) < CML_EPSILON) {
          root_array[0] = 0.0F;
          *root_num = 1U;
        } else {
          // no solution, default roots have been set
        }
        // linear equation with one unknown
      } else {
        root_array[0] = -coeff_c / coeff_b;
        *root_num = 1U;
      }
      // solve quadratic equation
    } else {
      float discriminant = 0.0F;
      // general solution of the quadratic equation
      discriminant = coeff_b * coeff_b - (4.0F * coeff_a * coeff_c);
      // two roots
      if (discriminant > 0.0F) {
        root_array[0] = (-coeff_b + sqrtf(discriminant)) / (2.0F * coeff_a);
        root_array[1] = (-coeff_b - sqrtf(discriminant)) / (2.0F * coeff_a);
      } else if (fabsf(discriminant) < CML_EPSILON) {
        // one root
        root_array[0] = -coeff_b / (2.0F * coeff_a);
        root_array[1] = root_array[0];
        *root_num = 1U;
      } else {
        // no solution, default roots have been set
        *root_num = 0U;
      }
    }
  } else {
    enable_to_get_roots = false;
  }

  return enable_to_get_roots;
}  // Cml_SolveQuadraticEquation end
