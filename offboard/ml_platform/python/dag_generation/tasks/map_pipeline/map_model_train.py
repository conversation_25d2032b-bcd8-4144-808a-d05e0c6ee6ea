"""map model train"""
import os
from functools import partial

from airflow.utils.task_group import TaskGroup
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator


from perception_training import luban_settings
from orion_cloud.flower.airflow.ops.luban_distributed_task_op import (
    LubanDistributedTaskOperator,
)
from dag_generation.constants import TaskType
from dag_generation.constants import LUBAN_RESOURCE_UUID
from dag_generation.tasks.abstract_task import AbstractTask
from dag_generation.tasks.task_factory import register
from dag_generation.tasks.map_pipeline.checkpoint import Checkpoint
from dag_generation.tasks.map_pipeline.utils import gen_luban_task_conf
from dag_generation.tasks.map_pipeline.utils import gen_distribute_request
from dag_generation.tasks.map_pipeline.utils import on_failure_callback
from dag_generation.tasks.map_pipeline.utils import on_success_callback

from dataloop.utils.luban_utils import LubanTaskOperator

_GROUP_NAME = TaskType.MAP_MODEL_TRAIN


@register(TaskType.MAP_MODEL_TRAIN)
class MapModelTrain(AbstractTask):
    """map model train"""

    def _is_skip(self, **context):
        train = context["dag_run"].conf.get("train")
        if not train:
            raise AirflowSkipException

    def _gen_train_request(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        train = context["dag_run"].conf.get("train")
        cfg_file = train["cfg_file"]
        exp_id = train["exp_id"]
        save_dir = train["save_dir"]

        script_cmd = ["train", cfg_file, exp_id, save_dir]
        return gen_distribute_request(
            user_name,
            script_cmd,
            f"train_ddp_{exp_id}",
            LUBAN_RESOURCE_UUID["H20_8_distributed"],
            branch,
        )

    def _pick_train_ckpt(self, **context):
        train = context["dag_run"].conf.get("train")
        exp_id = train["exp_id"]

        save_dir = train["save_dir"]
        ckpt_path = os.path.join(save_dir, f"ddp_{exp_id}", "checkpoints", "latest.pth")
        # TODO check ckpt path exist
        context["ti"].xcom_push(key="ckpt_path", value=ckpt_path)

    def _gen_visualization_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        train = context["dag_run"].conf.get("train")
        cfg_file = train["cfg_file"]
        ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.pick_train_ckpt", key="ckpt_path"
        )
        save_dir = train["save_dir"]
        exp_id = f"ddp_{train['exp_id']}"

        visual_num = train["visual_num"]

        script_cmd = [
            "run_visualization",
            cfg_file,
            ckpt_path,
            save_dir,
            exp_id,
            visual_num,
        ]
        return gen_luban_task_conf(
            user_name, script_cmd, "visualization", LUBAN_RESOURCE_UUID["H20_8"], branch
        )

    def _gen_eval_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        train = context["dag_run"].conf.get("train")
        ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.pick_train_ckpt", key="ckpt_path"
        )
        # ckpt_path = train["ckpt_path"]
        cfg_file = train["cfg_file"]
        save_dir = train["save_dir"]
        exp_id = f"ddp_{train['exp_id']}"

        script_cmd = ["run_eval", ckpt_path, cfg_file, save_dir, exp_id]
        return gen_luban_task_conf(
            user_name, script_cmd, "eval", LUBAN_RESOURCE_UUID["H20_8"], branch
        )

    def _gen_train_lane_detector_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        train_lane_detector = context["dag_run"].conf.get("train_lane_detector")
        cfg_file = train_lane_detector["cfg_file"]
        save_dir = train_lane_detector["save_dir"]
        exp_id = train_lane_detector["exp_id"]
        main_ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.pick_train_ckpt", key="ckpt_path"
        )

        script_cmd = ["train_lane_detector", cfg_file, save_dir, exp_id, main_ckpt_path]
        return gen_luban_task_conf(
            user_name,
            script_cmd,
            "train_lane_detector",
            LUBAN_RESOURCE_UUID["H20_8"],
            branch,
        )

    def _pick_lane_detector_ckpt(self, **context):
        lane_detector = context["dag_run"].conf.get("train_lane_detector")
        exp_id = lane_detector["exp_id"]
        save_dir = lane_detector["save_dir"]
        ckpt_path = os.path.join(save_dir, f"{exp_id}", "checkpoints", "latest.pth")
        # TODO check ckpt path exist
        context["ti"].xcom_push(key="ckpt_path", value=ckpt_path)

    def _gen_eval_lane_detector_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        train_lane_detector = context["dag_run"].conf.get("train_lane_detector")
        cfg_file = train_lane_detector["cfg_file"]
        save_dir = train_lane_detector["save_dir"]
        exp_id = train_lane_detector["exp_id"]

        ckpt_path = context["ti"].xcom_pull(
            task_ids=f"{_GROUP_NAME}.pick_lane_detector_ckpt", key="ckpt_path"
        )

        # ckpt_path = train_lane_detector["ckpt_path"]

        script_cmd = ["lane_detector_eval", cfg_file, ckpt_path, exp_id, save_dir]
        return gen_luban_task_conf(
            user_name,
            script_cmd,
            "eval_lane_detector",
            LUBAN_RESOURCE_UUID["H20_8"],
            branch,
        )

    def gen_airflow_ops(self, dag, *args, **kwargs):
        with TaskGroup(_GROUP_NAME, dag=dag) as tg:
            is_skip_op = PythonOperator(
                task_id="is_skip",
                dag=dag,
                python_callable=self._is_skip,
                provide_context=True,
            )

            train_op = LubanDistributedTaskOperator(
                project_token=luban_settings.LUBAN_PROJECT_TOKEN,
                project_uuid=luban_settings.LUBAN_PROJECT_UUID,
                caller=luban_settings.LUBAN_DISTRIBUTED_API_CALLER,
                key=luban_settings.LUBAN_DISTRIBUTED_API_KEY,
                task_id="train",
                dag=dag,
                request_generator=self._gen_train_request,
                on_success_callback=partial(
                    on_success_callback, scene="train", is_distribute=True
                ),
                on_failure_callback=partial(on_failure_callback, scene="train"),
            )

            pick_train_ckpt_op = PythonOperator(
                task_id="pick_train_ckpt",
                dag=dag,
                python_callable=self._pick_train_ckpt,
                provide_context=True,
            )

            visualize_op = LubanTaskOperator(
                task_id="visualize",
                dag=dag,
                gen_offline_task_conf=self._gen_visualization_conf,
                on_success_callback=partial(on_success_callback, scene="visualize"),
                on_failure_callback=partial(on_failure_callback, scene="visualize"),
            )

            eval_op = LubanTaskOperator(
                task_id="eval",
                dag=dag,
                gen_offline_task_conf=self._gen_eval_conf,
                on_success_callback=partial(on_success_callback, scene="eval"),
                on_failure_callback=partial(on_failure_callback, scene="eval"),
            )

            train_lane_detector_op = LubanTaskOperator(
                task_id="train_lane_detector",
                dag=dag,
                gen_offline_task_conf=self._gen_train_lane_detector_conf,
                on_success_callback=partial(
                    on_success_callback, scene="train_lane_detector"
                ),
                on_failure_callback=partial(
                    on_failure_callback, scene="train_lane_detector"
                ),
            )

            pick_lane_detector_ckpt_op = PythonOperator(
                task_id="pick_lane_detector_ckpt",
                dag=dag,
                python_callable=self._pick_lane_detector_ckpt,
                provide_context=True,
            )

            eval_lane_detector_op = LubanTaskOperator(
                task_id="lane_detector_eval",
                dag=dag,
                gen_offline_task_conf=self._gen_eval_lane_detector_conf,
                on_success_callback=partial(
                    on_success_callback, scene="eval_lane_detector"
                ),
                on_failure_callback=partial(
                    on_failure_callback, scene="eval_lane_detector"
                ),
            )

            ignore_fail_task = [f"{_GROUP_NAME}.visualize", f"{_GROUP_NAME}.eval"]
            checkpoint_op = Checkpoint(
                _GROUP_NAME, ignore_fail_task=ignore_fail_task
            ).op(dag)

            is_skip_op >> train_op >> pick_train_ckpt_op
            pick_train_ckpt_op >> visualize_op >> checkpoint_op
            pick_train_ckpt_op >> eval_op >> checkpoint_op

            (
                pick_train_ckpt_op
                >> train_lane_detector_op
                >> pick_lane_detector_ckpt_op
                >> eval_lane_detector_op
                >> checkpoint_op
            )

            return tg
