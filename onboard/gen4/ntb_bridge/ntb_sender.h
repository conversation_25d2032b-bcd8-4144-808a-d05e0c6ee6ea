#ifndef ONBOARD_GEN4_NTB_BRIDGE_NTB_SENDER_H_
#define ONBOARD_GEN4_NTB_BRIDGE_NTB_SENDER_H_

#include <cerrno>
#include <cstdint>
#include <cstring>
#include <fcntl.h>
#include <glog/logging.h>
#include <map>
#include <memory>
#include <string>
#include <utility>

#include "common.h"
#include "onboard/common/base/include/base/macros.h"
#include "onboard/common/base/include/base/now.h"
#include "trace/trace.h"
#include "voy_protos/image.pb.h"
#include "voy_protos/video_frame.pb.h"
#include "voy_trace/trace_ntb_bridge.h"

namespace ntb_bridge {

class NtbSender {
 public:
  DISALLOW_COPY(NtbSender);
  using NtbSenderSPtr = std::shared_ptr<NtbSender>;

  static NtbSenderSPtr GetInstance(int dst_host_id) {
    static std::map<int, NtbSenderSPtr> instances;
    static std::mutex mtx;

    std::lock_guard lg(mtx);

    if (instances.count(dst_host_id) == 0) {
      instances.emplace(dst_host_id, new NtbSender(dst_host_id));
    }
    return instances.at(dst_host_id);
  }
  ~NtbSender() { CloseNtbDevice(); }

  template <typename Message>
  bool Send(const std::string& topic_name, const Message& msg,
            int64_t publish_timestamp = base::NowNs()) {
    if (ntb_dev_fd_ < 0) {
      LOG(ERROR) << "Ntb device is not opened, dest_host_id: " << dest_host_id_
                 << ", topic: " << topic_name;
      return false;
    }
    int64_t hw_timestamp = 0;
    TRACE_EVENT_SCOPE(ntb_bridge, SenderSendMsg, topic_name, hw_timestamp,
                      publish_timestamp);
    int ntb_topic_id = TopicToNtbTopicID(topic_name);
    // Extract hardware timestamp from the message
    if constexpr (std::is_same_v<Message, pb::Image>) {
      hw_timestamp = msg.header().timestamp();
    } else if constexpr (std::is_same_v<Message, pb::VideoFrame>) {
      hw_timestamp = msg.timestamp();
    }

    // Alloc NTB tx buf
    constexpr int kNtbTxMemAllocMaxRetryTimes = 10;
    ntb_msg* packet_msg = nullptr;
    for (int i = 1; i <= kNtbTxMemAllocMaxRetryTimes; ++i) {
      errno = 0;
      packet_msg = ntb_tx_mem_alloc(ntb_dev_fd_);
      if (packet_msg != nullptr && errno == 0) {
        break;
      }
      if (errno != NTBE_AGAIN) {
        packet_msg = nullptr;
        break;
      }
      LOG(WARNING) << "No available ntb tx mem, retrying... (" << i << "/"
                   << kNtbTxMemAllocMaxRetryTimes << ")"
                   << "topic: " << topic_name << ", hw_time: " << hw_timestamp
                   << ", pub_time: " << publish_timestamp;
      std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    if (packet_msg == nullptr) {
      LOG(ERROR) << "Failed to alloc ntb tx mem, topic: " << topic_name
                 << ", hw_time: " << hw_timestamp
                 << ", pub_time: " << publish_timestamp;
      return false;
    }

    // Check message size
    const auto msg_size = static_cast<uint32_t>(msg.ByteSizeLong());
    if (const uint32_t max_data_buf_size =
            packet_msg->size - sizeof(NtbMsgHeader);
        msg_size > max_data_buf_size) {
      LOG(ERROR) << "Sending data larger than " << max_data_buf_size
                 << "B is not supported, topic: " << topic_name
                 << ", hw_time: " << hw_timestamp
                 << ", pub_time: " << publish_timestamp;
      return false;
    }

    // Build header and serialize message
    const NtbMsgHeader ntb_msg_header{
        .timestamp = base::NowNs(),
        .hw_timestamp = hw_timestamp,
        .msg_publish_timestamp = publish_timestamp,  // ns
        .topic_id = ntb_topic_id,
        .data_size = msg_size,
    };
    packet_msg->len = kNtbMsgHeaderSize + msg_size;

    void* packet_buf = packet_msg->buf;
    memcpy(packet_buf, &ntb_msg_header, kNtbMsgHeaderSize);
    msg.SerializeWithCachedSizesToArray(static_cast<uint8_t*>(packet_buf) +
                                        kNtbMsgHeaderSize);

    // Send to ntb dev
    int ret = ntb_send(ntb_dev_fd_, packet_msg);
    if (ret < 0) {
      LOG(ERROR) << "Failed to send ntb message"
                 << ", topic: " << topic_name << ", hw_time: " << hw_timestamp
                 << ", pub_time: " << publish_timestamp
                 << ", msg_idx: " << packet_msg->index << ", ret: " << ret;
      return false;
    }
    int hw_to_pub = (publish_timestamp / 1000000) - hw_timestamp;
    int hw_to_send = base::Now() - hw_timestamp;
    LOG_EVERY_N(INFO, 10) << "Send ntb message, topic: " << topic_name
                          << ", size: " << ntb_msg_header.data_size
                          << ", hw_time: " << hw_timestamp
                          << ", hw_to_pub: " << hw_to_pub
                          << ", hw_to_send: " << hw_to_send;
    return true;
  }

 private:
  explicit NtbSender(int dest_host_id)
      : ntb_dev_fd_(-1), dest_host_id_(dest_host_id) {
    OpenNtbDevice();
  }

  bool OpenNtbDevice() {
    ntb_dev_fd_ = ntb_open(static_cast<ntb_host_id>(dest_host_id_), O_RDWR);
    if (ntb_dev_fd_ < 0) {
      LOG(ERROR) << "Failed to open ntb device(" << dest_host_id_ << ")";
      return false;
    }
    return true;
  }

  bool CloseNtbDevice() {
    if (ntb_dev_fd_ >= 0) {
      ntb_close(ntb_dev_fd_);
      ntb_dev_fd_ = -1;
    }
    return true;
  }

  int ntb_dev_fd_{};
  int dest_host_id_{};
};

class NtbSenderWrapper {
 public:
  explicit NtbSenderWrapper(const std::string& topic_name)
      : topic_name(topic_name),
        dest_host_id_(TopicToDestHostID(topic_name)),
        ntb_sender_(NtbSender::GetInstance(dest_host_id_)) {}

  template <typename Message>
  bool Send(const Message& msg, int64_t publish_timestamp = base::NowNs()) {
    return ntb_sender_->Send(topic_name, std::move(msg), publish_timestamp);
  }

 private:
  std::string topic_name;
  int dest_host_id_;
  std::shared_ptr<NtbSender> ntb_sender_;
};

}  // namespace ntb_bridge

#endif  // ONBOARD_GEN4_NTB_BRIDGE_NTB_SENDER_H_
