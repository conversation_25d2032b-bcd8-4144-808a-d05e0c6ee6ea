#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid.h"

#include <queue>
#include <unordered_map>
#include <vector>

#include <tbb/parallel_for.h>

#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace open_space {

namespace {

// The number of next-step grid cell vertices for dp exploration from one grid
// cell vertex, including four on the x & y direction and the other four on the
// diagonal direction.
const int kHeuristicDistDpExplorationNextVerticesNum = 8;

}  // namespace

void Grid::Update(const MotionPlanProblem& motion_plan_problem) {
  TRACE_EVENT_SCOPE(planner, OpenSpacePathSearcher_UpdateGrid);
  DCHECK(motion_plan_problem.IsValid());

  motion_plan_problem_ptr_ = &motion_plan_problem;
  grid_params_ = GridParams(motion_plan_problem.roi,
                            config().xy_grid_resolution_in_meters());

  ClearGridData();
  PopulateGridCellVertices(motion_plan_problem.boundaries,
                           motion_plan_problem.contours,
                           motion_plan_problem.ego_disk_radius);
  PopulateGridCells();
  DCHECK_EQ(motion_plan_problem.target_pose_infos.size(), 1)
      << "Multiple target poses are not currently supported in open space "
         "search!";
  PopulateGridCellVerticesHeuristicDistance(
      motion_plan_problem.target_pose_infos.front().target_pose().position());
}

void Grid::PopulateGridCellVertices(
    const std::unordered_map<OccupancyConstraintID, Boundary>& boundaries,
    const std::unordered_map<OccupancyConstraintID, Contour>& contours,
    double ego_disk_radius) {
  grid_cell_vertices_ = std::vector<GridCellVertex>(
      grid_params_.x_vertices_size * grid_params_.y_vertices_size);
  tbb::parallel_for(
      0, static_cast<int>(grid_cell_vertices_.size()),
      [this, ego_disk_radius, &boundaries, &contours](int vertex_flat_key) {
        // Get GridKey from flat key.
        const GridKey vertex_key =
            GetGridCellVertexKeyFromFlattenedKey(vertex_flat_key);
        // Emplace a new vertex.
        grid_cell_vertices_[vertex_flat_key] =
            GridCellVertex(grid_params_.x_range.start_pos +
                               vertex_key.x_idx * grid_params_.xy_res_m,
                           grid_params_.y_range.start_pos +
                               vertex_key.y_idx * grid_params_.xy_res_m,
                           vertex_key.x_idx, vertex_key.y_idx);
        // Populate distance to forbidden.
        PopulateGridCellVertexOccupancyClearanceInfo(
            boundaries, contours, ego_disk_radius,
            &(grid_cell_vertices_[vertex_flat_key]));
      });
}

void Grid::PopulateGridCellVertexOccupancyClearanceInfo(
    const std::unordered_map<OccupancyConstraintID, Boundary>& boundaries,
    const std::unordered_map<OccupancyConstraintID, Contour>& contours,
    double ego_disk_radius, GridCellVertex* vertex_ptr) {
  // Process obstacle contours for this vertex.
  GridCellVertex& vertex = *DCHECK_NOTNULL(vertex_ptr);

  const auto update_vertex_dominant_clearance_info =
      [&vertex, ego_disk_radius](const OccupancyConstraintID& constraint_id,
                                 double clearance) {
        double& dominant_clearance = vertex.dominant_clearance_info.clearance;
        if (clearance >= dominant_clearance) {
          return;
        }
        dominant_clearance = clearance;
        vertex.dominant_clearance_info.constraint_id = constraint_id;
        vertex.dominant_clearance_info.is_drivable =
            dominant_clearance >= ego_disk_radius;
      };
  const auto update_vertex_clearance_info_map =
      [&vertex, ego_disk_radius](const OccupancyConstraintID& constraint_id,
                                 double clearance) {
        vertex.clearance_info_map.emplace(
            constraint_id,
            OccupancyClearanceInfo{constraint_id,
                                   /*is_drivable=*/clearance >= ego_disk_radius,
                                   clearance});
      };

  vertex.clearance_info_map.reserve(boundaries.size() + contours.size());
  for (const auto& [id, contour] : contours) {
    const double clearance =
        math::geometry::Distance(contour.polygon(), vertex.xy_point);

    if (math::NearZero(clearance)) {
      vertex.is_available = false;
      update_vertex_dominant_clearance_info(contour.constraint_id(),
                                            /*clearance=*/0.0);
      vertex.clearance_info_map.clear();
      break;
    }
    update_vertex_dominant_clearance_info(contour.constraint_id(), clearance);
    update_vertex_clearance_info_map(contour.constraint_id(), clearance);
  }
  // Early return if the vertex is already not drivable.
  if (!vertex.is_available) {
    return;
  }

  // Process open space boundaries for this vertex.
  for (const auto& [id, boundary] : boundaries) {
    const math::ProximityQueryInfo proximity = boundary.GetProximity(
        vertex.xy_point, math::pb::UseExtensionFlag::kForbid);

    if (!boundary.IsOnPassSide(proximity)) {
      vertex.is_available = false;
      update_vertex_dominant_clearance_info(boundary.constraint_id(),
                                            /*clearance=*/0.0);
      vertex.clearance_info_map.clear();
      break;
    }
    update_vertex_dominant_clearance_info(boundary.constraint_id(),
                                          proximity.dist);
    update_vertex_clearance_info_map(boundary.constraint_id(), proximity.dist);
  }
}

void Grid::PopulateGridCellVerticesHeuristicDistance(
    const math::geometry::Point2d& target_pose_pt) {
  std::priority_queue<HeuristicDpPQInfo, std::vector<HeuristicDpPQInfo>>
      open_pq;

  // Local lambda for updating a next vertex's heuristic distance to target
  // given the current vertex.
  const auto get_next_vertex_pq_info =
      [](const math::geometry::Point2d& curr_vertex_pt,
         const GridCellVertex& next_vertex, double curr_vertex_heuristic_dist) {
        if (!next_vertex.is_drivable()) {
          // Vertex not drivable, return pq info with null key.
          return HeuristicDpPQInfo();
        }
        const double one_step_heuristic_dist =
            math::geometry::Distance(curr_vertex_pt, next_vertex.xy_point);
        const double expected_next_vertex_heuristic =
            curr_vertex_heuristic_dist + one_step_heuristic_dist;
        return HeuristicDpPQInfo(next_vertex.key,
                                 expected_next_vertex_heuristic);
      };

  // Initialize the open pq with four corners of the grid cell containing the
  // target pose xy point.
  const GridCell& snapped_target_pose_cell =
      GetGridCell(SnapToGrid(target_pose_pt));
  for (const GridCellVertex* vertex_ptr :
       snapped_target_pose_cell.CornerVertices()) {
    HeuristicDpPQInfo pq_info =
        get_next_vertex_pq_info(target_pose_pt, *DCHECK_NOTNULL(vertex_ptr),
                                /*curr_vertex_heuristic_dist=*/0.0);
    if (!pq_info.key.IsNullKey()) {
      GetMutableGridCellVertex(pq_info.key).heuristic_dist_to_target =
          pq_info.heuristic_dist_to_target;
      open_pq.push(std::move(pq_info));
    }
  }

  // Lambda generating next vertices to be explored from the current vertex.
  const auto generate_next_vertices = [this](const GridKey& curr_vertex_key) {
    std::vector<GridCellVertex*> next_vertices;
    next_vertices.reserve(kHeuristicDistDpExplorationNextVerticesNum);
    for (int x_idx = curr_vertex_key.x_idx - 1;
         x_idx <= curr_vertex_key.x_idx + 1; ++x_idx) {
      if (x_idx < 0 || x_idx >= grid_params().x_vertices_size) {
        // Out of index bound.
        continue;
      }
      for (int y_idx = curr_vertex_key.y_idx - 1;
           y_idx <= curr_vertex_key.y_idx + 1; ++y_idx) {
        if (y_idx < 0 || y_idx >= grid_params().y_vertices_size) {
          // Out of index bound.
          continue;
        }
        GridKey temp_key(x_idx, y_idx);
        if (temp_key == curr_vertex_key) {
          // Skip the current vertex.
          continue;
        }
        const GridCellVertex& temp_next_vertex = GetGridCellVertex(temp_key);
        if (!temp_next_vertex.is_drivable() ||
            temp_next_vertex.is_closed_for_heuristic_dp) {
          continue;
        }
        next_vertices.push_back(&GetMutableGridCellVertex(temp_key));
      }
    }
    return next_vertices;
  };

  while (!open_pq.empty()) {
    GridCellVertex& curr_vertex = GetMutableGridCellVertex(open_pq.top().key);
    open_pq.pop();
    if (curr_vertex.is_closed_for_heuristic_dp) {
      continue;
    }
    curr_vertex.is_closed_for_heuristic_dp = true;

    const std::vector<GridCellVertex*> next_vertices =
        generate_next_vertices(curr_vertex.key);
    std::vector<HeuristicDpPQInfo> pq_infos(next_vertices.size());
    tbb::parallel_for(0, static_cast<int>(next_vertices.size()),
                      [&next_vertices, &curr_vertex, &get_next_vertex_pq_info,
                       &pq_infos](int next_key_idx) {
                        pq_infos[next_key_idx] = get_next_vertex_pq_info(
                            curr_vertex.xy_point,
                            *DCHECK_NOTNULL(next_vertices[next_key_idx]),
                            curr_vertex.heuristic_dist_to_target);
                      });

    DCHECK_EQ(pq_infos.size(), next_vertices.size());
    for (size_t idx = 0; idx < pq_infos.size(); ++idx) {
      if (pq_infos[idx].key.IsNullKey() ||
          pq_infos[idx].heuristic_dist_to_target >=
              next_vertices[idx]->heuristic_dist_to_target) {
        continue;
      }
      next_vertices[idx]->heuristic_dist_to_target =
          pq_infos[idx].heuristic_dist_to_target;
      open_pq.push(pq_infos[idx]);
    }
  }
}

void Grid::PopulateGridCells() {
  grid_cells_ = std::vector<GridCell>(grid_params_.x_cell_size *
                                      grid_params_.y_cell_size);
  tbb::parallel_for(
      0, static_cast<int>(grid_cells_.size()), [this](int flattened_key) {
        const GridKey grid_key = GetGridKeyFromFlattenedKey(flattened_key);
        // Due to the limitation of copy constructor deleted by the unique ptr
        // member in grid cell, firstly declare a cell variable and then move it
        // to optimize performance.
        GridCell new_cell = GridCell(
            /*lower_left=*/&(GetMutableGridCellVertex(
                GridKey(grid_key.x_idx, grid_key.y_idx))),
            /*lower_right=*/
            &(GetMutableGridCellVertex(
                GridKey(grid_key.x_idx + 1, grid_key.y_idx))),
            /*upper_left=*/
            &(GetMutableGridCellVertex(
                GridKey(grid_key.x_idx, grid_key.y_idx + 1))),
            /*upper_right=*/
            &(GetMutableGridCellVertex(
                GridKey(grid_key.x_idx + 1, grid_key.y_idx + 1))),
            config().heading_slot_resolution_in_rad());
        grid_cells_[flattened_key] = std::move(new_cell);
      });
}

GridKey Grid::SnapToGrid(const math::geometry::Point2d& pt) const {
  // Return null key for out-of-bound query.
  if (!IsWithinGridArea(pt)) {
    return GridKey();
  }

  int x_idx = static_cast<int>((pt.x() - grid_params_.x_range.start_pos) /
                               grid_params_.xy_res_m);
  int y_idx = static_cast<int>((pt.y() - grid_params_.y_range.start_pos) /
                               grid_params_.xy_res_m);
  DCHECK_LE(x_idx, grid_params_.x_cell_size);
  DCHECK_LE(y_idx, grid_params_.y_cell_size);

  // The following equality cases happens when the queried x and y steps on
  // the cell x and y range end_pos.
  if (x_idx == grid_params_.x_cell_size) {
    --x_idx;
  }
  if (y_idx == grid_params_.y_cell_size) {
    --y_idx;
  }

  return GridKey(x_idx, y_idx);
}

OpenSpaceNodeKey Grid::GetOpenSpaceNodeKeyFromPathState(
    const PathState& node_path_state) const {
  const GridKey snapped_grid_cell_key = SnapToGrid(node_path_state.position);
  if (snapped_grid_cell_key.IsNullKey()) {
    return {};
  }

  const GridCell& snapped_grid_cell = GetGridCell(
      GridKey{snapped_grid_cell_key.x_idx, snapped_grid_cell_key.y_idx});
  const int heading_interval_idx =
      snapped_grid_cell.GetHeadingIntervalIndex(node_path_state.heading);
  if (heading_interval_idx == kInvalidOpenSpaceGridIndex) {
    return {};
  }

  return OpenSpaceNodeKey(snapped_grid_cell_key.x_idx,
                          snapped_grid_cell_key.y_idx, heading_interval_idx);
}

OpenSpaceNode* Grid::EmplaceOpenSpaceNode(OpenSpaceNode&& new_node) {
  GridCell& snapped_grid_cell =
      GetMutableGridCell(GridKey{new_node.key.x_idx, new_node.key.y_idx});
  const int heading_interval_idx =
      snapped_grid_cell.GetHeadingIntervalIndex(new_node.state.heading);
  if (heading_interval_idx == kInvalidOpenSpaceGridIndex) {
    return nullptr;
  }

  snapped_grid_cell.ConstructHeadingSearchSpaceIfUninitialized();
  OpenSpaceNode& node_for_update =
      DCHECK_NOTNULL(snapped_grid_cell.mutable_heading_search_space())
          ->heading_intervals[heading_interval_idx]
          .searched_node;
  node_for_update = std::move(new_node);
  return &node_for_update;
}

OpenSpaceNode* Grid::GetMutableOpenSpaceNode(const PathState& node_path_state) {
  // Locate GridCell.
  const GridKey snapped_grid_cell_key = SnapToGrid(node_path_state.position);
  if (snapped_grid_cell_key.IsNullKey()) {
    return nullptr;
  }

  // Locate heading interval.
  GridCell& snapped_grid_cell = GetMutableGridCell(snapped_grid_cell_key);
  if (snapped_grid_cell.heading_search_space() == nullptr) {
    return nullptr;
  }
  const int heading_interval_idx =
      snapped_grid_cell.GetHeadingIntervalIndex(node_path_state.heading);
  if (heading_interval_idx == kInvalidOpenSpaceGridIndex) {
    return nullptr;
  }

  HeadingSearchSpaceInterval& heading_space_interval =
      DCHECK_NOTNULL(snapped_grid_cell.mutable_heading_search_space())
          ->heading_intervals[heading_interval_idx];
  return &heading_space_interval.searched_node;
}

OpenSpaceNode* Grid::GetMutableOpenSpaceNode(const OpenSpaceNodeKey& node_key) {
  GridCell& snapped_grid_cell =
      GetMutableGridCell(GridKey{node_key.x_idx, node_key.y_idx});
  if (snapped_grid_cell.mutable_heading_search_space() == nullptr) {
    return nullptr;
  }
  std::vector<HeadingSearchSpaceInterval>& heading_intervals =
      snapped_grid_cell.mutable_heading_search_space()->heading_intervals;
  DCHECK_LT(node_key.heading_idx, heading_intervals.size());
  return node_key.heading_idx < static_cast<int>(heading_intervals.size())
             ? &(heading_intervals[node_key.heading_idx].searched_node)
             : nullptr;
}

const OpenSpaceNode* Grid::GetOpenSpaceNode(
    const OpenSpaceNodeKey& node_key) const {
  const GridCell& snapped_grid_cell =
      GetGridCell(GridKey{node_key.x_idx, node_key.y_idx});
  if (snapped_grid_cell.heading_search_space() == nullptr) {
    return nullptr;
  }
  const std::vector<HeadingSearchSpaceInterval>& heading_intervals =
      snapped_grid_cell.heading_search_space()->heading_intervals;
  DCHECK_LT(node_key.heading_idx, heading_intervals.size());
  return node_key.heading_idx < static_cast<int>(heading_intervals.size())
             ? &(heading_intervals[node_key.heading_idx].searched_node)
             : nullptr;
}

}  // namespace open_space

}  // namespace planner
