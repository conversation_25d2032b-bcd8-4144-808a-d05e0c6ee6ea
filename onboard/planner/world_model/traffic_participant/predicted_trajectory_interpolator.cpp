#include "planner/world_model/traffic_participant/predicted_trajectory_interpolator.h"

#include <cmath>
#include "geometry/model/oriented_box.h"
#include "math/interpolation.h"
#include "math/math_util.h"

namespace planner {
namespace {

// Gets rounded angle value. This function is used for angle interpolation,
// ensuring the abs diff of two consecutive angles less or equal than pi. To
// deal with the discontinuity of the angles crossing -pi to pi.
double GetRoundedAngle(double last_angle, double current_angle) {
  return last_angle + math::AngleDiff(current_angle, last_angle);
}

}  // namespace

PredictedTrajectoryInterpolator::PredictedTrajectoryInterpolator(
    int64_t object_id, const PredictedTrajectory& predicted_trajectory,
    bool include_ignorable_prediction)
    : object_id_(object_id) {
  // Get predicted poses.
  const auto& poses =
      include_ignorable_prediction
          ? predicted_trajectory.GetPoses(/*include_ignorable_prediction=*/true)
          : predicted_trajectory.GetPoses();
  int pose_size = poses.size();
  DCHECK_GT(pose_size, 1);

  // Initialize time, x, y and heading vectors.
  ts_.reserve(pose_size);
  xs_.reserve(pose_size);
  ys_.reserve(pose_size);
  headings_.reserve(pose_size);
  widths_.reserve(pose_size);
  lengths_.reserve(pose_size);

  for (const auto& pose : poses) {
    ts_.push_back(static_cast<double>(pose.timestamp()));
    xs_.push_back(pose.center().x());
    ys_.push_back(pose.center().y());
    widths_.push_back(pose.width());
    lengths_.push_back(pose.length());
    if (headings_.empty()) {
      headings_.push_back(pose.heading());
    } else {
      headings_.push_back(GetRoundedAngle(headings_.back(), pose.heading()));
    }
  }

  t_x_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), xs_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_y_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), ys_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_heading_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), headings_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_width_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), widths_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_length_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), lengths_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  std::tie(start_timestamp_, end_timestamp_) = t_x_->GetRange();
}

PredictedTrajectoryInterpolator::PredictedTrajectoryInterpolator(
    int64_t object_id, const std::vector<PredictedPose>& predicted_poses)
    : object_id_(object_id) {
  const int pose_size = predicted_poses.size();
  DCHECK_GT(pose_size, 1);

  // Initialize time, x, y and heading vectors.
  ts_.reserve(pose_size);
  xs_.reserve(pose_size);
  ys_.reserve(pose_size);
  headings_.reserve(pose_size);
  widths_.reserve(pose_size);
  lengths_.reserve(pose_size);

  for (const auto& pose : predicted_poses) {
    ts_.push_back(static_cast<double>(pose.timestamp()));
    xs_.push_back(pose.center().x());
    ys_.push_back(pose.center().y());
    widths_.push_back(pose.width());
    lengths_.push_back(pose.length());
    if (headings_.empty()) {
      headings_.push_back(pose.heading());
    } else {
      headings_.push_back(GetRoundedAngle(headings_.back(), pose.heading()));
    }
  }

  t_x_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), xs_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_y_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), ys_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_heading_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), headings_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_width_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), widths_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  t_length_ = std::make_unique<math::Interpolator1d>(
      ts_.data(), lengths_.data(), pose_size,
      math::pb::Interpolation1dType::kLinear);
  std::tie(start_timestamp_, end_timestamp_) = t_x_->GetRange();
}

std::optional<math::geometry::OrientedBox2d>
PredictedTrajectoryInterpolator::GetInterpolatedBoundingBox(
    double query_timestamp) const {
  if (query_timestamp < start_timestamp_ || query_timestamp > end_timestamp_) {
    return std::nullopt;
  }
  const double interp_x = t_x_->evaluate(query_timestamp, /*deriv*=*/nullptr,
                                         /*deriv2*=*/nullptr);
  const double interp_y = t_y_->evaluate(query_timestamp, /*deriv*=*/nullptr,
                                         /*deriv2*=*/nullptr);

  const double interp_width =
      t_width_->evaluate(query_timestamp, /*deriv*=*/nullptr,
                         /*deriv2*=*/nullptr);
  const double interp_length =
      t_length_->evaluate(query_timestamp, /*deriv*=*/nullptr,
                          /*deriv2*=*/nullptr);

  const double interp_heading = t_heading_->evaluate(
      query_timestamp, /*deriv*=*/nullptr, /*deriv2*=*/nullptr);

  return math::geometry::OrientedBox2d{
      interp_x, interp_y, interp_length, interp_width,
      math::WrapFromMinusPiToPi(interp_heading)};
}

std::optional<pb::TrajectoryPose>
PredictedTrajectoryInterpolator::GetInterpolatedPose2d(
    int64_t query_timestamp) const {
  const double timestamp_in_d = static_cast<double>(query_timestamp);
  if (timestamp_in_d < start_timestamp_ || timestamp_in_d > end_timestamp_) {
    return std::nullopt;
  }
  pb::TrajectoryPose pose;
  pose.set_timestamp(query_timestamp);
  pose.set_x_pos(t_x_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                /*deriv2*=*/nullptr));
  pose.set_y_pos(t_y_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                /*deriv2*=*/nullptr));
  pose.set_heading(t_heading_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                        /*deriv2*=*/nullptr));
  pose.set_speed(t_speed_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                    /*deriv2*=*/nullptr));
  pose.set_accel(t_accel_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                    /*deriv2*=*/nullptr));
  pose.set_odom(t_odom_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                  /*deriv2*=*/nullptr));
  pose.set_steering_wheel_angle(
      t_steer_wheel_angle_->evaluate(timestamp_in_d, /*deriv*=*/nullptr,
                                     /*deriv2*=*/nullptr));
  return pose;
}

}  // namespace planner
