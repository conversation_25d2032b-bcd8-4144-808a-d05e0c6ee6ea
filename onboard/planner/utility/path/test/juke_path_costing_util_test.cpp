#include "planner/utility/path/juke_path_costing_util.h"

#include <gtest/gtest.h>
#include <utility>

#include "geometry/model/point_2d.h"
#include "math/range.h"
#include "planner/path/path_solver/nominal_path_smoother/semantic_corridor.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"

namespace planner {
namespace path {
namespace {

using StateIndex = path::JukeIntegratedPathFormula::StateIndex;
using ControlIndex = path::JukeIntegratedPathFormula::ControlIndex;
using CostingUtil = path::JukeIntegratedPathFormula::FormulaCostingUtil;
using FeatureParameter = path::JukeIntegratedPathFormula::PbFeatureParameter;
using FeatureType = path::JukeIntegratedPathFormula::PbFeatureType;
using StepwisePrimitives = path::JukeIntegratedPathFormula::StepwisePrimitives;
using WeightAdjustSpec = path::JukeIntegratedPathFormula::PbWeightAdjustSpec;

using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;
using Hamiltonian = CostingUtil::Hamiltonian;
using State = path::JukeIntegratedPathFormula::State;
using Control = path::JukeIntegratedPathFormula::Control;

constexpr int X_DIM = path::JukeIntegratedPathFormula::StateIndex::X_DIM;
[[maybe_unused]] constexpr int U_DIM =
    path::JukeIntegratedPathFormula::ControlIndex::U_DIM;

constexpr double kPathStepInMeter = 0.5;
constexpr double kWheelBase = 2.85;

// Helper function to construct the state.
State CreateState(double x_pos, double y_pos, double theta, double kappa,
                  double eta) {
  State x = State::Zero();
  x[StateIndex::X_POS] = x_pos;
  x[StateIndex::Y_POS] = y_pos;
  x[StateIndex::THETA] = theta;
  x[StateIndex::KAPPA] = kappa;
  x[StateIndex::ETA] = eta;
  return x;
}
}  // namespace
class JukePathCostingUtilTest : public ::testing::Test {
 protected:
  // Returns uniform speed regions given const speed and arclength
  // range and number.
  std::vector<primitive::SpeedReferenceRegionPrimitive> GetUniformSpeedRegions(
      double speed, const math::Range1d& arclength_range, int region_num) {
    std::vector<primitive::SpeedReferenceRegionPrimitive> speed_regions;
    DCHECK_GT(region_num, 0);
    const double arclengh_res =
        (arclength_range.end_pos - arclength_range.start_pos) / region_num;
    speed_regions.reserve(region_num);
    for (int i = 0; i < region_num; ++i) {
      speed_regions.emplace_back(
          math::Range1d(arclength_range.start_pos + arclengh_res * i,
                        arclength_range.start_pos + arclengh_res * (i + 1)),
          speed);
    }
    return speed_regions;
  }

  // Returns a polyline for a straight segment.
  math::geometry::Polyline2d GetPolyline(
      int points_num, const math::geometry::Point2d& start_pt) {
    math::geometry::Polyline2d polyline;
    polyline.reserve(points_num);
    for (int i = 0; i < points_num; ++i) {
      polyline.emplace_back(start_pt.x() + kPathStepInMeter * i, start_pt.y());
    }
    return polyline;
  }

  // Returns speed regions and polyline ptr.
  std::pair<std::vector<primitive::SpeedReferenceRegionPrimitive>,
            math::geometry::PolylineCurve2dProximityCachePtr>
  GenerateSpeedAndPolylinePtr(int speed_region_num, double ego_speed,
                              double start_arc_length, double end_arc_length,
                              double start_x, double start_y) {
    std::vector<primitive::SpeedReferenceRegionPrimitive> speed_regions =
        GetUniformSpeedRegions(ego_speed,
                               math::Range1d(start_arc_length, end_arc_length),
                               speed_region_num);

    math::geometry::Polyline2d polyline = GetPolyline(
        /*points_num=*/50, math::geometry::Point2d(start_x, start_y));

    math::geometry::PolylineCurve2dProximityCachePtr cache_polyline_ptr =
        math::geometry::CreatePolylineCurve2dProximityCachePtr(
            math::geometry::PolylineCurve2d(polyline),
            /*grid_res=*/0.2);

    return std::make_pair(speed_regions, cache_polyline_ptr);
  }
};

TEST_F(JukePathCostingUtilTest, GetReferenceSpeedSquare) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;
  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0,
                        /*theta=*/0.0, /*kappa=*/0.0, /*eta=*/0.0);

  const double ref_speed_sqr = GetReferenceSpeedSquare(
      speed_regions, x, math::Sqr(kEgoSpeed), cache_polyline_ptr.get());

  EXPECT_DOUBLE_EQ(ref_speed_sqr, math::Sqr(kEgoSpeed));
}

TEST_F(JukePathCostingUtilTest, ConstraintKappa) {
  vehicle_model::pb::JukeIntegratedModelLimit limit;
  limit.mutable_kappa_limit()->set_min_val(-0.234);
  limit.mutable_kappa_limit()->set_max_val(0.234);
  path::JukeIntegratedPathFormula::Feature feature;

  State x = CreateState(/*x_pos=*/0.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.25, /*eta=*/0.0);
  ConstraintKappa(x, limit, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 0.029);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, ConstraintEta) {
  vehicle_model::pb::JukeIntegratedModelLimit limit;
  limit.mutable_eta_limit()->set_min_val(-0.15);
  limit.mutable_eta_limit()->set_max_val(0.15);
  path::JukeIntegratedPathFormula::Feature feature;

  State x = CreateState(/*x_pos=*/0.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.0, /*eta=*/0.5);
  ConstraintEta(x, limit, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 0.35);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, ConstraintGamma) {
  constexpr double max_abs_gamma = 0.1;
  path::JukeIntegratedPathFormula::Feature feature;

  Control u = Control::Zero();
  std::vector<double> gamma_values = {0.05, 0.15};
  std::vector<double> expected_vals = {0.0, 0.05};
  std::vector<double> expected_jacob_gammas = {0.0, 1.0};

  for (size_t i = 0; i < gamma_values.size(); ++i) {
    u[ControlIndex::GAMMA] = gamma_values[i];
    [[maybe_unused]] bool result = ConstraintGamma(u, max_abs_gamma, &feature);
    EXPECT_DOUBLE_EQ(feature.components[0].val, expected_vals[i]);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                     expected_jacob_gammas[i]);
  }
}

TEST_F(JukePathCostingUtilTest, ConstraintApproximateLateralAccel) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;
  constexpr double kMaxAbsLateralAccelMpss = 5.0;
  path::JukeIntegratedPathFormula::Feature feature;
  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.0, /*eta=*/0.0);

  std::vector<double> kappa_values = {0.1, -0.5, 0.5};
  std::vector<double> expected_vals = {0.0, -7.5, 7.5};
  std::vector<double> expected_jacob_kappa = {0.0, 25.0, 25.0};

  for (size_t i = 0; i < kappa_values.size(); ++i) {
    x[StateIndex::KAPPA] = kappa_values[i];
    [[maybe_unused]] bool result =
        ConstraintApproximateLateralAccel(x, kEgoSpeed, kMaxAbsLateralAccelMpss,
                                          cache_polyline_ptr.get(), &feature);
    EXPECT_DOUBLE_EQ(feature.components[0].val, expected_vals[i]);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA],
                     expected_jacob_kappa[i]);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                     0.0);
  }
}

TEST_F(JukePathCostingUtilTest, ConstraintApproximateLateralJerk) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;
  constexpr double kMaxAbsLateralJerkMpss = 5.0;

  path::JukeIntegratedPathFormula::Feature feature;
  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.0, /*eta=*/0.0);

  std::vector<double> eta_values = {0.01, -0.2, 0.2};
  std::vector<double> expected_vals = {0.0, -20.0, 20.0};
  std::vector<double> expected_jacob_eta = {0.0, 125.0, 125.0};

  for (size_t i = 0; i < eta_values.size(); ++i) {
    x[StateIndex::ETA] = eta_values[i];
    [[maybe_unused]] bool result =
        ConstraintApproximateLateralJerk(x, kEgoSpeed, kMaxAbsLateralJerkMpss,
                                         cache_polyline_ptr.get(), &feature);
    EXPECT_DOUBLE_EQ(feature.components[0].val, expected_vals[i]);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA],
                     expected_jacob_eta[i]);
    EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                     0.0);
  }
}

TEST_F(JukePathCostingUtilTest, PrimitiveSoftPathAttraction) {
  constexpr double kStartStateX = 15.0;
  constexpr double kStartStateY = 5.0;
  constexpr double kTheta = M_PI_4;
  constexpr int kStep = 30;
  constexpr int kTotalStepSize = 80;

  path::JukeIntegratedPathFormula::Feature feature;

  math::geometry::Point2d front_bumper_pos(kStartStateX, kStartStateY);

  vehicle_model::pb::JukeIntegratedMotionModelParam param;
  param.mutable_measurement()->set_wheel_base(2.5);

  vehicle_model::pb::AxleRectangularMeasurement shape_measurement;
  shape_measurement.set_front_bumper_to_front_axle(1.0);

  std::vector<planner::primitive::WeightAdjustedRegionPrimitive>
      weight_adjusted_regions;
  planner::primitive::WeightAdjustedRegionPrimitive adjusted_region;
  adjusted_region.region = math::Range1d(0.0, 50.0);
  adjusted_region.weight_adjust = 1.0;
  weight_adjusted_regions.push_back(adjusted_region);

  auto cached_poly = math::geometry::CreatePolylineCurve2dProximityCachePtr(
      math::geometry::PolylineCurve2d(
          GetPolyline(50, math::geometry::Point2d(10.0, 0.0))),
      0.2);

  control_util::pb::CostingWeightAdjustSpec weight_adjust_spec;
  weight_adjust_spec.set_type(
      control_util::pb::CostingWeightAdjustSpec::NO_DECAY);
  weight_adjust_spec.mutable_weight_limit()->set_min_val(0.2);
  weight_adjust_spec.mutable_weight_limit()->set_max_val(1.0);

  bool should_enhance_attraction = true;

  PrimitiveSoftPathAttraction(
      front_bumper_pos, kTheta, param, shape_measurement,
      weight_adjusted_regions, kStep, kTotalStepSize, cached_poly.get(),
      weight_adjust_spec, should_enhance_attraction, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 5.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 1.6);
}

TEST_F(JukePathCostingUtilTest, ComputeReferenceHeadingAttraction) {
  constexpr int kStep = 30;
  constexpr int kTotalStepSize = 80;
  constexpr double kHeadingTolerance = 0.2;
  constexpr double kToleranceM = 0.002;

  State x = CreateState(/*x_pos=*/10.0, /*y_pos=*/10.0, /*theta=*/M_PI_4,
                        /*kappa=*/0.1, /*eta=*/0.1);

  path::JukeIntegratedPathFormula::Feature feature;

  std::vector<planner::primitive::WeightAdjustedRegionPrimitive>
      weight_adjusted_regions;
  planner::primitive::WeightAdjustedRegionPrimitive adjusted_region;
  adjusted_region.region = math::Range1d(0.0, 50.0);
  adjusted_region.weight_adjust = 1.0;
  weight_adjusted_regions.push_back(adjusted_region);

  auto cached_poly = math::geometry::CreatePolylineCurve2dProximityCachePtr(
      math::geometry::PolylineCurve2d(
          GetPolyline(50, math::geometry::Point2d(10.0, 0.0))),
      0.2);

  control_util::pb::CostingWeightAdjustSpec weight_adjust_spec;
  weight_adjust_spec.set_type(
      control_util::pb::CostingWeightAdjustSpec::NO_DECAY);
  weight_adjust_spec.mutable_weight_limit()->set_min_val(0.2);
  weight_adjust_spec.mutable_weight_limit()->set_max_val(1.0);

  bool should_enhance_attraction = true;
  ComputeReferenceHeadingAttraction(x, kHeadingTolerance, kStep, kTotalStepSize,
                                    cached_poly.get(), weight_adjust_spec,
                                    should_enhance_attraction, &feature);
  EXPECT_NEAR(feature.components[0].val, 0.5854, kToleranceM);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 1.6);
}

TEST_F(JukePathCostingUtilTest, EffortKappa) {
  path::JukeIntegratedPathFormula::Feature feature;
  State x = CreateState(/*x_pos=*/0.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.25, /*eta=*/0.0);
  EffortKappa(x, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 0.25);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, EffortEta) {
  path::JukeIntegratedPathFormula::Feature feature;
  State x = CreateState(/*x_pos=*/0.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.25, /*eta=*/0.15);
  EffortEta(x, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 0.15);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 1.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, EffortGamma) {
  path::JukeIntegratedPathFormula::Feature feature;
  Control u = Control::Zero();

  u[ControlIndex::GAMMA] = 0.35;
  EffortGamma(u, &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 0.35);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   1.0);
}

TEST_F(JukePathCostingUtilTest, EffortApproximateSteeringRate) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  [[maybe_unused]] constexpr double kEgoSpeedSquare = 625.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;

  path::JukeIntegratedPathFormula::Feature feature;
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.05, /*eta=*/0.2);

  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);
  control_util::pb::CostingWeightAdjustSpec weight_adjust_spec;
  weight_adjust_spec.set_type(
      control_util::pb::
          CostingWeightAdjustSpec_WeightAdjustType_REVERSED_SIGMOID);
  weight_adjust_spec.mutable_weight_limit()->set_min_val(0.0);
  weight_adjust_spec.mutable_weight_limit()->set_max_val(1.0);
  const int step = 30;
  const int total_step_size = 80;
  [[maybe_unused]] const double ref_speed_sqr = GetReferenceSpeedSquare(
      speed_regions, x, math::Sqr(kEgoSpeed), cache_polyline_ptr.get());
  EffortApproximateSteeringRate(x, kWheelBase, kEgoSpeed, step, total_step_size,
                                cache_polyline_ptr.get(), weight_adjust_spec,
                                &feature);

  EXPECT_DOUBLE_EQ(feature.components[0].val, 2.7932789787380021);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA],
                   -2.2236861241219903);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA],
                   13.96639489369001);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
}

TEST_F(JukePathCostingUtilTest, EffortApproximateSteeringAccel) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.0;
  constexpr double kEndArclength = 100.0;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;

  path::JukeIntegratedPathFormula::Feature feature;
  Control u = Control::Zero();
  u[ControlIndex::GAMMA] = 0.35;

  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);
  control_util::pb::CostingWeightAdjustSpec weight_adjust_spec;
  weight_adjust_spec.set_type(
      control_util::pb::
          CostingWeightAdjustSpec_WeightAdjustType_REVERSED_SIGMOID);
  weight_adjust_spec.mutable_weight_limit()->set_min_val(0.0);
  weight_adjust_spec.mutable_weight_limit()->set_max_val(1.0);
  const int step = 30;
  const int total_step_size = 80;
  EffortApproximateSteeringAccel(u, kWheelBase, kEgoSpeed, step,
                                 total_step_size, weight_adjust_spec, &feature);

  EXPECT_DOUBLE_EQ(feature.components[0].val, 24.9375);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::THETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::X_POS], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::Y_POS], 0.0);
}

TEST_F(JukePathCostingUtilTest, EffortApproximateLateralAccel) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;
  [[maybe_unused]] double ego_speed_squared = 25.0;
  path::JukeIntegratedPathFormula::Feature feature;
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.0, /*eta=*/0.0);

  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);

  control_util::pb::CostingWeightAdjustSpec WeightAdjustSpec;
  WeightAdjustSpec.mutable_weight_limit()->set_min_val(0.0);
  WeightAdjustSpec.mutable_weight_limit()->set_max_val(1.0);

  // Assume adjust_type is REVERSED_SIGMOID.
  WeightAdjustSpec.set_type(
      control_util::pb::CostingWeightAdjustSpec::REVERSED_SIGMOID);
  x[StateIndex::KAPPA] = 0.1;
  [[maybe_unused]] bool result =
      EffortApproximateLateralAccel(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                    cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 2.5);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.9998);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 25.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
  x[StateIndex::KAPPA] = 0.2;
  result = EffortApproximateLateralAccel(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                         cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 5.0);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.9998);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 25.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
  // Assume adjust_type is LINEAR_REDUCTION.
  WeightAdjustSpec.set_type(
      control_util::pb::CostingWeightAdjustSpec::LINEAR_REDUCTION);
  x[StateIndex::KAPPA] = -0.1;
  result = EffortApproximateLateralAccel(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                         cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, -2.5);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.99);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 25.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, EffortApproximateLateralJerk) {
  constexpr int kSpeedRegionNum = 10;
  constexpr double kStartArclength = 0.;
  constexpr double kEndArclength = 100.;
  constexpr double kEgoSpeed = 5.0;
  constexpr double kStartStateX = 10.0;
  constexpr double kStartStateY = 0.0;
  [[maybe_unused]] double ego_speed_squared = 25.0;

  path::JukeIntegratedPathFormula::Feature feature;
  State x = CreateState(/*x_pos=*/20.0, /*y_pos=*/0.0, /*theta=*/0.0,
                        /*kappa=*/0.0, /*eta=*/0.0);

  const auto [speed_regions, cache_polyline_ptr] =
      GenerateSpeedAndPolylinePtr(kSpeedRegionNum, kEgoSpeed, kStartArclength,
                                  kEndArclength, kStartStateX, kStartStateY);

  control_util::pb::CostingWeightAdjustSpec WeightAdjustSpec;
  WeightAdjustSpec.mutable_weight_limit()->set_min_val(0.0);
  WeightAdjustSpec.mutable_weight_limit()->set_max_val(1.0);

  //  Assume adjust_type is REVERSED_SIGMOID.
  WeightAdjustSpec.set_type(
      control_util::pb::CostingWeightAdjustSpec::REVERSED_SIGMOID);
  x[StateIndex::ETA] = 0.1;
  [[maybe_unused]] bool result =
      EffortApproximateLateralJerk(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                   cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 12.5);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.9998);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 125.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
  x[StateIndex::ETA] = -0.1;
  result = EffortApproximateLateralJerk(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                        cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, -12.5);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.9998);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 125.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
  // Assume adjust_type is LINEAR_REDUCTION.
  WeightAdjustSpec.set_type(
      control_util::pb::CostingWeightAdjustSpec::LINEAR_REDUCTION);
  x[StateIndex::ETA] = 0.15;
  result = EffortApproximateLateralJerk(x, kEgoSpeed, 1, 100, WeightAdjustSpec,
                                        cache_polyline_ptr.get(), &feature);
  EXPECT_DOUBLE_EQ(feature.components[0].val, 18.75);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 0.99);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::KAPPA], 0.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[StateIndex::ETA], 125.0);
  EXPECT_DOUBLE_EQ(feature.components[0].jacob[X_DIM + ControlIndex::GAMMA],
                   0.0);
}

TEST_F(JukePathCostingUtilTest, ComputeSteeringRateLimit) {
  const auto steering_rate_limt_static = ComputeSteeringRateLimit(
      kWheelBase, /*ego_speed_squared=*/
      math::Sqr(0.05), /*max_abs_steering_rate=*/0.3154159);
  EXPECT_DOUBLE_EQ(steering_rate_limt_static.start_pos, -0.3154159);
  EXPECT_DOUBLE_EQ(steering_rate_limt_static.end_pos, 0.3154159);

  const auto steering_rate_limt_slow =
      ComputeSteeringRateLimit(kWheelBase, /*ego_speed_squared=*/math::Sqr(2.8),
                               /*max_abs_steering_rate=*/0.3154159);
  EXPECT_DOUBLE_EQ(steering_rate_limt_slow.start_pos, -0.3154159);
  EXPECT_DOUBLE_EQ(steering_rate_limt_slow.end_pos, 0.3154159);

  const auto steering_rate_limt_fast =
      ComputeSteeringRateLimit(kWheelBase, /*ego_speed_squared=*/math::Sqr(5.0),
                               /*max_abs_steering_rate=*/0.3154159);
  EXPECT_DOUBLE_EQ(steering_rate_limt_fast.start_pos, -0.228);
  EXPECT_DOUBLE_EQ(steering_rate_limt_fast.end_pos, 0.228);
}

TEST_F(JukePathCostingUtilTest, PathAttractionWithTolerance) {
  path::JukeIntegratedPathFormula::FormulaCostingUtil::Feature feature;
  math::geometry::Point2d front_bumper_pos(10.0, 6.0);
  constexpr double kConsMin = -20.0;
  constexpr double kConsMax = 2.0;
  constexpr int kStep = 10;
  constexpr int kTotalStepSize = 80;
  constexpr double kTheta = 0.524;
  constexpr double kToleranceM = 0.01;

  vehicle_model::pb::JukeIntegratedMotionModelParam param;
  const vehicle_model::pb::AxleRectangularMeasurement shape_measurement =
      vehicle_model::GetEgoAxleRectangularMeasurementByType(
          av_comm::CarType::kTest);

  // Set up the boundary constraints vector that has 2 guidance boundary
  // constraints.
  const auto attraction_poly = test::ReadLine("LINESTRING(8.0 0.0,16.0 8.0)");
  math::geometry::PolylineCurve2dProximityCachePtr attraction_poly_ptr =
      math::geometry::CreatePolylineCurve2dProximityCachePtr(
          attraction_poly,
          planner::primitive::kPolylineDistanceCacheResolutionInMeter);

  feature.components.resize(1);
  // Situation 1, if attraction poly pointer is null, return false.
  EXPECT_FALSE(PathAttractionWithTolerance(
      front_bumper_pos, kTheta, kConsMin, kConsMax, param, shape_measurement,
      kStep, kTotalStepSize, /*weight_adjust=*/1.0,
      /*additional_param=*/std::nullopt, nullptr, &feature.components[0]));

  // Situation 2, if the distance to attraction polyline is within tolerance,
  // don't update feature.
  front_bumper_pos = math::geometry::Point2d(10.0, 3.0);
  EXPECT_TRUE(PathAttractionWithTolerance(
      front_bumper_pos, kTheta, kConsMin, kConsMax, param, shape_measurement,
      kStep, kTotalStepSize,
      /*weight_adjust=*/1.0, /*additional_param=*/std::nullopt,
      attraction_poly_ptr.get(), &feature.components[0]));

  EXPECT_EQ(feature.components.size(), 1);
  EXPECT_NEAR(feature.components[0].val, 0.0, kToleranceM);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 1.0);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::X_POS], 0.0, kToleranceM);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::Y_POS], 0.0, kToleranceM);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::THETA], 0.0, kToleranceM);

  // Situation 2, if the distance to attraction polyline is not within
  // tolerance, update the feature.
  front_bumper_pos = math::geometry::Point2d(10.0, 6.0);
  EXPECT_TRUE(PathAttractionWithTolerance(
      front_bumper_pos, kTheta, kConsMin, kConsMax, param, shape_measurement,
      kStep, kTotalStepSize,
      /*weight_adjust=*/1.0, /*additional_param=*/std::nullopt,
      attraction_poly_ptr.get(), &feature.components[0]));

  EXPECT_EQ(feature.components.size(), 1);
  EXPECT_NEAR(feature.components[0].val, 0.8284, kToleranceM);
  EXPECT_DOUBLE_EQ(feature.components[0].weight_adjust, 1.0);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::X_POS], -0.707107,
              kToleranceM);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::Y_POS], 0.707107,
              kToleranceM);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::THETA], 3.7279,
              kToleranceM);
}

TEST_F(JukePathCostingUtilTest, ConstraintTerminalHeading) {
  constexpr double kToleranceM = 0.01;
  constexpr int kStep = 30;
  constexpr int kTotalStepSize = 80;

  path::JukeIntegratedPathFormula::Feature feature;
  control_util::pb::CostingWeightAdjustSpec WeightAdjustSpec;
  WeightAdjustSpec.mutable_weight_limit()->set_min_val(0.0);
  WeightAdjustSpec.mutable_weight_limit()->set_max_val(1.0);

  //  Assume adjust_type is REVERSED_SIGMOID.
  WeightAdjustSpec.set_type(
      control_util::pb::CostingWeightAdjustSpec::REVERSED_SIGMOID);
  State x = CreateState(/*x_pos=*/5.0, /*y_pos=*/0.0, /*theta=*/1.57,
                        /*kappa=*/0.0, /*eta=*/0.0);
  math::Range<int> index_range = math::Range<int>(25, 35);
  ConstraintTerminalHeading(
      x, /*target_heading=*/2.0,
      /*heading_tolerance=*/0.2,
      /*step=*/kStep,
      /*total_step_size=*/kTotalStepSize, /*weight_adjust=*/5.0,
      /*target_index_range=*/index_range, WeightAdjustSpec, &feature);
  EXPECT_NEAR(feature.components[0].val, -13.178, kToleranceM);
  EXPECT_NEAR(feature.components[0].jacob[StateIndex::THETA], 57.29,
              kToleranceM);
}

}  // namespace path
}  // namespace planner
