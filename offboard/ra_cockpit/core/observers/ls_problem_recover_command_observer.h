#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LS_PROBLEM_RECOVER_COMMAND_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LS_PROBLEM_RECOVER_COMMAND_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class LSProblemRecoverCommandObserver : public IDataObserver {
 public:
  using LSProblemRecoverCommandCallback = std::function<void(
      std::shared_ptr<teleassist::pb::LSProblemRecoverCommand>, DataChannel)>;

  explicit LSProblemRecoverCommandObserver(
      LSProblemRecoverCommandCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  LSProblemRecoverCommandCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LS_PROBLEM_RECOVER_COMMAND_OBSERVER_H_
