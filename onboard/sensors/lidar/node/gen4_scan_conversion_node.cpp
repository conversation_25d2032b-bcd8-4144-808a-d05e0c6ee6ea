#include "gen4_scan_conversion_node.h"

#include <algorithm>
#include <cstring>
#include <list>
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>

#include <glog/logging.h>

#include "av_comm/mode_config.h"
#include "av_comm/onboard_config.h"
#include "lidar/scan_conversion_gflags.h"
#include "lidar/scan_conversion_message.h"
#include "lidar/scan_conversion_message_filter.h"
#include "lidar/scan_slice_util.h"
#include "lidar/sensor_type.h"
#include "rate_limited_log/rate_limited_log.h"

namespace lidar {
namespace gen4_scan_conversion_node {

#define KB(n) ((n)*1024)
#define MB(n) KB((n)*1024)
#define GB(n) MB((n)*1024)

REGISTER_VNODE(Gen4ScanConversionNode)

Gen4ScanConversionNode::Gen4ScanConversionNode()
    : pcl_storaget_type_(voy::PointCloudStorageType::PbArray) {
  if (av_comm::InSimulation() == false ||
      FLAGS_force_using_scan_msg_shm_pool_in_simulation == true) {
    pcl_storaget_type_ = voy::PointCloudStorageType::MemPool;
  }
}
Gen4ScanConversionNode::~Gen4ScanConversionNode() {}

bool Gen4ScanConversionNode::Init() {
  if (!av_comm::IsGen4Platform()) {
    return false;
  }

  if (!config_) {
    config_ = std::make_unique<ScanConversionConfig>();
    config_->use_interpolated_pose =
        GetParamByKey<bool>("use_interpolated_pose");
    config_->use_integrated_pose = GetParamByKey<bool>("use_integrated_pose");
    config_->enable_on_robot_points_filter =
        GetParamByKey<bool>("enable_on_robot_points_filter");
    config_->enable_surrounding_robot_points_filter =
        GetParamByKey<bool>("enable_surrounding_robot_points_filter");
    config_->name = GetParamByKey<std::string>("name");
  }

  msg_pool_ = std::make_shared<lidar::ShmPoolManager>(config_->name, MB(512),
                                                      pcl_storaget_type_);
  msg_pool_->Reset(/*frame_id*/ 0, /*init*/ true);
  if (config_->enable_surrounding_robot_points_filter) {
    surrounding_msg_pool_ = std::make_shared<lidar::ShmPoolManager>(
        config_->name + std::string("_surrounding"), MB(256),
        pcl_storaget_type_);
    surrounding_msg_pool_->Reset(/*frame_id*/ 0, /*init*/ true);
  }

  std::vector<voy::Sensor::SensorType> main_lidar_types, side_lidar_types;
  for (auto& kv : scan_conversion_node::GetLidarTypes()) {
    auto& lidar_type_list = kv.second ? main_lidar_types : side_lidar_types;
    lidar_type_list.push_back(kv.first);
    fault_reporter_list_[kv.first] = std::make_shared<node::FaultReporter>(
        av_comm::component::kScanConversion,
        av_comm::GetLidarComponent(kv.first));
  }

  main_multi_scan_converter_ = std::make_shared<Gen4WholeMultiScanConverter>(
      MultiScanConverterConfig{
          .sensor_types = std::move(main_lidar_types),
          .max_num_scan_unit = 4,
          .use_interpolated_pose = config_->use_interpolated_pose,
          .use_integrated_pose = config_->use_integrated_pose,
          .enable_on_robot_points_filter =
              config_->enable_on_robot_points_filter,
          .enable_surrounding_robot_points_filter =
              config_->enable_surrounding_robot_points_filter},
      /*expected_scans*/ 4, /* timeout_ms */ 50, pcl_storaget_type_, msg_pool_,
      surrounding_msg_pool_, fault_reporter_list_);

  side_multi_scan_converter_ = std::make_shared<WholeMultiScanConverter>(
      MultiScanConverterConfig{
          .sensor_types = side_lidar_types,
          .max_num_scan_unit = 6,
          .use_interpolated_pose = config_->use_interpolated_pose,
          .use_integrated_pose = config_->use_integrated_pose,
          .enable_on_robot_points_filter =
              config_->enable_on_robot_points_filter,
          .enable_surrounding_robot_points_filter =
              config_->enable_surrounding_robot_points_filter},
      /*expected_scans*/ 6, /* timeout_ms */ 50);

  return true;
}

void Gen4ScanConversionNode::Callback(
    std::list<std::shared_ptr<const voy::Pose>>& pose_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_1_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_2_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_3_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_4_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_1_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_2_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_3_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_4_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_5_list,
    std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_6_list) {
  int64_t hw_time = 0;
  TRACE_EVENT_SCOPE(lidar, ScanConversion, "general_scan_conversion",
                    reinterpret_cast<uint64_t&>(hw_time));

#define Type(type, id) voy::Sensor::LIDAR_GEN4_##type##_##id
#define List(type, id) lidar_gen4_##type##_##id##_list
#define EnqueueMain(OP, id) OP(Type(MAIN, id), List(main, id))
#define EnqueueSide(OP, id) OP(Type(SIDE, id), List(side, id))
#define ListOfItems4(OP, X) \
  X(OP, 1);                 \
  X(OP, 2);                 \
  X(OP, 3);                 \
  X(OP, 4);
#define ListOfItems6(OP, X)     \
  ListOfItems4(OP, X) X(OP, 5); \
  X(OP, 6);

  main_multi_scan_converter_->EnqueuePose(pose_list);
  side_multi_scan_converter_->EnqueuePose(pose_list);

  ListOfItems4(main_multi_scan_converter_->EnqueueLidarScan, EnqueueMain);
  ListOfItems6(side_multi_scan_converter_->EnqueueLidarScan, EnqueueSide);

  while (true) {
    msg_pool_->CheckAndReleaseBackupPool(publish_frame_id_);
    if (surrounding_msg_pool_) {
      surrounding_msg_pool_->CheckAndReleaseBackupPool(publish_frame_id_);
    }

    // do main scan convertion
    while (main_multi_scan_converter_->ConvertScan()) {
      // do nothing
    }

    // do side scan convertion
    while (side_multi_scan_converter_->ConvertScan()) {
      // do nothing
    }

    std::optional<lidar::ScanMessage> side_scan_message =
        side_multi_scan_converter_->GetScanMessageManager().GetMergedScan();
    if (!side_scan_message) {
      return;
    }
    std::optional<voy::ScanMessage> main_scan_message =
        main_multi_scan_converter_->GetScanMessageManager().GetMergedScan();
    if (!main_scan_message) {
      // Should We send data for the side lidar?
      RGLOG(ERROR) << "Failed to get merged main scan message.";
      return;
    }

    if (side_scan_message->IsEmpty()) {
      RGLOG(ERROR) << "Side scan message is empty.";
    }

    if (main_scan_message->point_clouds().size() < 1) {
      side_multi_scan_converter_->GetScanMessageManager().ResetAll();
      main_multi_scan_converter_->GetScanMessageManager().ResetAll();
      RGLOG(ERROR) << "Main scan message has no point cloud.";
      return;
    }

    hw_time = GetMainLidarTimestamp(main_scan_message.value());

    {
      // Output timestamp matching statistics information
      DumpMatchedInfo(hw_time,
                      side_scan_message->IsEmpty()
                          ? 0
                          : side_scan_message->PointCloud(0).timestamp);
    }

    {
      // Output timestamp matching information for successful matching, only for
      // debugging purposes
      OutputDebugInfo(side_scan_message, main_scan_message);
    }

    auto shm_holder =
        main_multi_scan_converter_->GetScanMessageManager().GetAllHolder();

    // Merge side point cloud message to main point cloud message
    for (size_t i = 0; i < side_scan_message->PointCloudsSize(); i++) {
      shm_holder.emplace_back(AddPointCloudMessage(
          side_scan_message->PointCloud(i).lidar_type, fault_reporter_list_,
          pcl_storaget_type_, msg_pool_.get(),
          &side_scan_message->PointCloud(i), &(main_scan_message.value())));
    }
    msg_pool_->PostProcessToBePublishedMsg(&(main_scan_message.value()));
    main_scan_message.value().set_main_lidar_timestamp(hw_time);
    GetPublisherByName<voy::ScanMessage>("scan_msg_pub")
        ->Publish(main_scan_message.value());

    // surrounding scan msg
    if (config_->enable_surrounding_robot_points_filter) {
      auto side_surrounding_scan_message =
          side_multi_scan_converter_->GetScanMessageManager().GetMergedScan(
              true);
      if (side_surrounding_scan_message) {
        auto main_surrounding_scan_message =
            main_multi_scan_converter_->GetScanMessageManager().GetMergedScan(
                true);

        auto& message_value = main_surrounding_scan_message.value();
        for (size_t j = 0; j < side_surrounding_scan_message->PointCloudsSize();
             j++) {
          shm_holder.emplace_back(AddPointCloudMessage(
              side_surrounding_scan_message->PointCloud(j).lidar_type,
              fault_reporter_list_, pcl_storaget_type_,
              surrounding_msg_pool_.get(),
              &side_surrounding_scan_message->PointCloud(j), &message_value));
        }
        surrounding_msg_pool_->PostProcessToBePublishedMsg(&message_value);
        message_value.set_main_lidar_timestamp(hw_time);
        GetPublisherByName<voy::ScanMessage>("surrounding_scan_msg_pub")
            ->Publish(message_value);
      }
    }
    side_multi_scan_converter_->GetScanMessageManager().ResetAll();
    main_multi_scan_converter_->GetScanMessageManager().ResetAll();
    publish_frame_id_++;
  }
}

void Gen4ScanConversionNode::DumpMatchedInfo(int64_t main_timestamp,
                                             int64_t side_timestamp) const {
  const int max_time_gap_ms = 50;
  if (std::abs(main_timestamp - side_timestamp) < max_time_gap_ms) {
    LOG_EVERY_N(INFO, 100) << "Matched nearly. within the " << max_time_gap_ms
                           << "ms."
                           << " mt: " << main_timestamp
                           << " st: " << side_timestamp << " ("
                           << side_timestamp - main_timestamp << ")";
  } else {
    LOG_EVERY_N(INFO, 100) << "Matched barely. outside the " << max_time_gap_ms
                           << "ms."
                           << " mt: " << main_timestamp
                           << " st: " << side_timestamp << " ("
                           << side_timestamp - main_timestamp << ")";
  }
}

void Gen4ScanConversionNode::OutputDebugInfo(
    const std::optional<lidar::ScanMessage>& side_scan_message,
    const std::optional<voy::ScanMessage>& main_scan_message) const {
  std::stringstream ss;
  for (size_t i = 0; i < side_scan_message->PointCloudsSize(); i++) {
    if (i != 0) ss << ",";
    ss << static_cast<int>(side_scan_message->PointCloud(i).lidar_type) << " "
       << side_scan_message->PointCloud(i).timestamp;
  }
  for (int i = 0; i < main_scan_message->point_clouds().size(); i++) {
    ss << ","
       << static_cast<int>(main_scan_message->point_clouds(i).lidar_type())
       << " " << main_scan_message->point_clouds(i).end_timestamp();
  }
  LOG(INFO) << ss.str();
}

/////////////////// Gen4MultiScanMessageManager ///////////////////

Gen4MultiScanMessageManager::Gen4MultiScanMessageManager(
    int expected_scans, int timeout_ms,
    voy::PointCloudStorageType pcl_storaget_type,
    std::shared_ptr<lidar::ShmPoolManager> scan_pool,
    std::shared_ptr<lidar::ShmPoolManager> surrounding_pool,
    std::unordered_map<voy::Sensor::SensorType,
                       std::shared_ptr<node::FaultReporter>>
        fault_reporter_list)
    : WholeMultiScanMessageManager(expected_scans, timeout_ms),
      pcl_storaget_type_(pcl_storaget_type),
      scan_pool_(scan_pool),
      surrounding_pool_(surrounding_pool),
      fault_reporter_list_(fault_reporter_list) {}

Gen4MultiScanMessageManager::~Gen4MultiScanMessageManager() {}

PclMsgAdpatorUptr Gen4MultiScanMessageManager::NewElem(
    voy::Sensor::SensorType lidar_type, bool is_surrounding_point) {
  if (is_surrounding_point) {
    return surrounding_pool_->AllocMsgAdaptor(
        lidar_type, fault_reporter_list_[lidar_type].get());
  }
  return scan_pool_->AllocMsgAdaptor(lidar_type,
                                     fault_reporter_list_[lidar_type].get());
}

bool Gen4MultiScanMessageManager::PushBack(PclMsgAdpatorUptr&& adaptor,
                                           bool is_surrounding_point) {
  if (!is_surrounding_point) {
    if (tracker_.IsComplete() || tracker_.IsTimedOut()) {
      ResetAll();
    }
    if (tracker_.AddScan(adaptor->GetLidarType(), adaptor->GetEndTimestamp()) ==
        ScanSyncTracker::Status::EXPIRED) {
      LOG(ERROR) << "Out-of-sync scan, resetting tracker."
                 << adaptor->GetLidarType() << " at timestamp "
                 << adaptor->GetEndTimestamp();
      Reset();
    }
  }

  auto PushBack = [this](lidar::ScanConverter::OutputMessageAdaptor* adaptor,
                         lidar::ShmPoolManager* shm_pool,
                         voy::ScanMessage* pcl_scan_msg) {
    if (pcl_storaget_type_ == voy::PointCloudStorageType::MemPool) {
      auto& voy_pcl_msg =
          reinterpret_cast<voy::PointCloudMessagePoolAdaptor*>(adaptor)
              ->voy_pcl_msg_;
      shm_pool->ShrinkMsg(adaptor);
      pcl_scan_msg->mutable_point_clouds()->Add()->CopyFrom(voy_pcl_msg);
    } else {
      auto& voy_pcl_msg =
          reinterpret_cast<voy::PointCloudMessageArrayAdaptor*>(adaptor)
              ->voy_pcl_msg_;
      pcl_scan_msg->mutable_point_clouds()->Add()->CopyFrom(voy_pcl_msg);
    }
  };

  if (is_surrounding_point) {
    merged_surrounding_scan_.mutable_point_clouds()->Reserve(
        scan_conversion_node::GetLidarTypes().size());
    PushBack(adaptor.get(), surrounding_pool_.get(), &merged_surrounding_scan_);
  } else {
    merged_scan_.mutable_point_clouds()->Reserve(
        scan_conversion_node::GetLidarTypes().size());
    PushBack(adaptor.get(), scan_pool_.get(), &merged_scan_);
  }
  holder_.emplace_back(std::move(adaptor));

  return tracker_.IsComplete();
}

std::optional<voy::ScanMessage> Gen4MultiScanMessageManager::GetMergedScan(
    bool is_surrounding_point) {
  if (tracker_.IsComplete() || tracker_.IsTimedOut()) {
    return is_surrounding_point ? std::move(merged_surrounding_scan_)
                                : std::move(merged_scan_);
  }
  return std::nullopt;
}

void Gen4MultiScanMessageManager::ResetAll() {
  Reset();
  tracker_.ClearState();
}

void Gen4MultiScanMessageManager::Reset() {
  merged_scan_.Clear();
  merged_surrounding_scan_.Clear();
  holder_.clear();
}

std::vector<std::unique_ptr<ScanConverter::OutputMessageAdaptor>>
Gen4MultiScanMessageManager::GetAllHolder() {
  return std::move(holder_);
}
}  // namespace gen4_scan_conversion_node
}  // namespace lidar
