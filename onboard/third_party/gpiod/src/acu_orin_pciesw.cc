/**
 * @file acu_orin_pciesw.cc
 * <AUTHOR>
 * @brief 检测pciesw状态，当前驱动配置固定端口为8,32,36
 * @version 0.1
 * @date 2024-06-26
 *
 * @copyright Copyright (c) 2024
 *
 */

#include "acu_orin_pciesw.h"

#include <stdalign.h>
#include <unistd.h>

#include <algorithm>
#include <atomic>
#include <chrono>
#include <iostream>
#include <sstream>
#include <string>
#include <thread>
#include <unordered_map>
#include <vector>

#include "acu_orin_module.h"
#include "base_tools.hpp"
#include "gpiod_interface.hpp"
#include "serial_port.hpp"
#include "vh_glog.hpp"

namespace vh = voyager::hardware;
enum kPcieSwMrpcStatuEnum {
  kMrpcStatIdel = 0,
  kMrpcStatInprogress = 1,
  kMrpcStatDone = 2,  // ok, 804
  kMrpcStatError = 0xFF,
};

enum kPcieSwMrpcRetEnum {
  kMrpcRetSuccess = 0x00000000,           // Command successful 808
  kMrpcRetMemAllocFail = 0x00000007,      // Memory allocation fails
  kMrpcRetNoThread = 0x00064001,          // No available MRPC handler thread
  kMrpcRetThreadWorking = 0x00064002,     // The handler thread is not idle
  kMrpcRetThreadNotWorking = 0x00064003,  // No thread run for the command
  kMrpcRetSubCmdError = 0x00064004,       // Subcommand is incorrect
  kMrpcRetCmdError = 0x00064005,          // Command is incorrect
  kMrpcRetParaError = 0x00064006,         // Input parameter is incorrect
  kMrpcRetFwError = 0x00064007,           // FW state is incorrect
  kMrpcRetRegistered = 0x0006400F,        // The MRPC command has already been
  kMrpcRetDenialService = 0x00064010,     // MRPC command denial of service
};

static constexpr double kOrinPcieswBwRatio = 1;
static constexpr uint32_t kOrinPcieswUartTimeout = 20;
static constexpr uint32_t kOrinPcieswUartCrcTimeout = 200;

struct __attribute__((packed)) OrinPcieswBwMsg4St {
  uint32_t time0_low_;  // us
  uint32_t time0_high_;
  uint32_t eg0_posted_low_;  // byte
  uint32_t eg0_posted_high_;
  uint32_t eg0_completion_low_;
  uint32_t eg0_completion_high_;
  uint32_t eg0_nonposted_low_;
  uint32_t eg0_nonposted_high_;
  uint32_t in0_posted_low_;  // byte
  uint32_t in0_posted_high_;
  uint32_t in0_completion_low_;
  uint32_t in0_completion_high_;
  uint32_t in0_nonposted_low_;
  uint32_t in0_nonposted_high_;
  uint32_t time1_low_;  // us
  uint32_t time1_high_;
  uint32_t eg1_posted_low_;  // byte
  uint32_t eg1_posted_high_;
  uint32_t eg1_completion_low_;
  uint32_t eg1_completion_high_;
  uint32_t eg1_nonposted_low_;
  uint32_t eg1_nonposted_high_;
  uint32_t in1_posted_low_;  // byte
  uint32_t in1_posted_high_;
  uint32_t in1_completion_low_;
  uint32_t in1_completion_high_;
  uint32_t in1_nonposted_low_;
  uint32_t in1_nonposted_high_;
  uint32_t time2_low_;  // us
  uint32_t time2_high_;
  uint32_t eg2_posted_low_;  // byte
  uint32_t eg2_posted_high_;
  uint32_t eg2_completion_low_;
  uint32_t eg2_completion_high_;
  uint32_t eg2_nonposted_low_;
  uint32_t eg2_nonposted_high_;
  uint32_t in2_posted_low_;  // byte
  uint32_t in2_posted_high_;
  uint32_t in2_completion_low_;
  uint32_t in2_completion_high_;
  uint32_t in2_nonposted_low_;
  uint32_t in2_nonposted_high_;
};
struct __attribute__((packed)) OrinPcieswBwMsg8St {
  uint64_t us0_;         // us
  uint64_t eg0_posted_;  // byte
  uint64_t eg0_completion_;
  uint64_t eg0_nonposted_;
  uint64_t in0_posted_;  // byte
  uint64_t in0_completion_;
  uint64_t in0_nonposted_;
  uint64_t us1_;         // us
  uint64_t eg1_posted_;  // byte
  uint64_t eg1_completion_;
  uint64_t eg1_nonposted_;
  uint64_t in1_posted_;  // byte
  uint64_t in1_completion_;
  uint64_t in1_nonposted_;
  uint64_t us2_;         // us
  uint64_t eg2_posted_;  // byte
  uint64_t eg2_completion_;
  uint64_t eg2_nonposted_;
  uint64_t in2_posted_;  // byte
  uint64_t in2_completion_;
  uint64_t in2_nonposted_;
};
union OrinPcieswBwMsgUn {
  OrinPcieswBwMsg4St msg4_;
  OrinPcieswBwMsg8St msg8_;
  uint32_t var4_[sizeof(OrinPcieswBwMsg4St) / sizeof(uint32_t)];
};

struct OrinPcieswLinkMsgDW1St {
  uint32_t physical_port_id_ : 8;
  uint32_t partition_id_ : 8;
  uint32_t logical_port_id_ : 8;
  uint32_t port_id_ : 4;
  uint32_t stack_id_ : 4;
};
struct OrinPcieswLinkMsgDW2St {
  uint32_t configure_link_width_ : 8;
  uint32_t negotiate_link_width_ : 8;
  uint32_t port_direction_ : 8;
  uint32_t link_rate_ : 7;
  uint32_t link_up_ : 1;
};
struct OrinPcieswLinkMsgDW3St {
  uint32_t ltssm_major_state_ : 8;
  uint32_t ltssm_minor_state_ : 8;
  uint32_t lane_reversal_ : 8;
  uint32_t first_active_lane_ : 4;
  uint32_t reserved_ : 4;
};
struct __attribute__((packed)) OrinPcieswLinkMsgSt {
  OrinPcieswLinkMsgDW1St link1_dw1_;
  OrinPcieswLinkMsgDW2St link1_dw2_;
  OrinPcieswLinkMsgDW3St link1_dw3_;
  OrinPcieswLinkMsgDW1St link2_dw1_;
  OrinPcieswLinkMsgDW2St link2_dw2_;
  OrinPcieswLinkMsgDW3St link2_dw3_;
  OrinPcieswLinkMsgDW1St link3_dw1_;
  OrinPcieswLinkMsgDW2St link3_dw2_;
  OrinPcieswLinkMsgDW3St link3_dw3_;
};
union OrinPcieswLinkMsgUn {
  OrinPcieswLinkMsgSt msg_;
  uint32_t var4_[sizeof(OrinPcieswLinkMsgSt) / sizeof(OrinPcieswLinkMsgDW1St)];
};

union OrinPcieseDevinfoMsgUn {
  vh::OrinPcieswDevinfoSt msg_;
  uint8_t var1[sizeof(vh::OrinPcieswDevinfoSt) / sizeof(uint8_t)];
  uint32_t var4[sizeof(vh::OrinPcieswDevinfoSt) / sizeof(uint32_t)];
};

static const std::string PcieswDebug("[PcieswDebug] ");
#define ORIN_PCIE_GPIO_CHIP "/dev/gpiochip0"
#define ORIN_PCIE_GPIO_LINE 109
#define ORIN_PCIE_GPIO_CONSUMER "orinb_pciesw"
#define ORIN_PCIE_DBG 1

static std::unordered_map<std::string, std::vector<std::string>> pciesw_maps_ =
    {{"temp_read", {"gaswr 0x0 0x2\n", "gaswr 0x800 0x04\n", "gasrd 0x400\n"}},
     {"link_read",
      {"gaswr 0x0 0x1000 0x11\n", "gaswr 0x800 0x1001c\n",
       "gasrd 0x400 0x09\n"}},
     {"bw_read",
      {"gaswr 0x0 0x10c0301 0x1240120\n", "gaswr 0x800 0x10007\n",
       "gasrd 0x400 0x2a\n"}},
     {"dev_id_read", {"gaswr 0x800 0x36\n", "gasrd 0x400 0x02\n"}},
     {"dev_crc_read",
      {
          "gaswr 0x0 0x0\n",
          "gaswr 0x800 0x1002b\n",
          "gasrd 0x804 0x1\n",
          "gasrd 0x808 0x1\n",
          "gasrd 0x440 80\n",
      }},
     {"dev_hversion_read",
      {"gaswr 0x0 0x0\n", "gaswr 0x800 0x36\n", "gasrd 0x400 0x02\n"}}};
bool vh::AcuOrinPciesw::dv_init(void *arg) {
  static_cast<void>(arg);
  static std::atomic<bool> flag(false);
  bool exepected = false;
  if (!flag.compare_exchange_strong(exepected, true)) {
    LOG(ERROR) << PcieswDebug << "dv_init again.\n";
    return 0;
  }
  LOG(WARNING) << PcieswDebug << "dv_init init.\n";
  if (GpiodInterface::gpiod_set_up(ORIN_PCIE_GPIO_CHIP, ORIN_PCIE_GPIO_LINE, 1,
                                   &chip_, &line_,
                                   ORIN_PCIE_GPIO_CONSUMER) < 0) {
    GpiodInterface::gpiod_set_down(chip_, line_);
    return -1;
  }

  if (serial_.open_serial() == false) {
    LOG(ERROR) << PcieswDebug << "open serial error.\n";
    return false;
  }

  return true;
}
void vh::AcuOrinPciesw::dv_deinit() {
  if (chip_ != nullptr && line_ != nullptr) {
    GpiodInterface::gpiod_set_down(chip_, line_);
    chip_ = nullptr;
    line_ = nullptr;
  }
  if (serial_.is_open()) serial_.close_serial();
}
bool vh::AcuOrinPciesw::get_dv_msg(AcuDvOrinBaseInfoUn &info) {
  static const uint8_t invalid_u8 = 0xFF;
  static const uint16_t invalid_u16 = 0xFFFF;
  static const int16_t invalid_s16 = 0xFFFF;

  uint8_t orin_id;
  AcuOrinModule::get_orin_id(&orin_id);
  if (orin_id == kAcuOrinIdA) {
    info.msg_.pciesw_host1_link_ = invalid_u8;
    info.msg_.pciesw_host2_link_ = invalid_u8;
    info.msg_.pciesw_host3_link_ = invalid_u8;
    info.msg_.pciesw_host1_in_bw_ = invalid_u16;
    info.msg_.pciesw_host1_eg_bw_ = invalid_u16;
    info.msg_.pciesw_host2_in_bw_ = invalid_u16;
    info.msg_.pciesw_host2_eg_bw_ = invalid_u16;
    info.msg_.pciesw_host3_in_bw_ = invalid_u16;
    info.msg_.pciesw_host3_eg_bw_ = invalid_u16;
    info.msg_.pciesw_temp_ = invalid_s16;
    return true;
  } else if (orin_id == kAcuOrinIdB) {
    OrinPcieswLinkSt link;
    OrinPcieswBwSt bw;
    int temp;

    if (get_orin_pciesw_temp(&temp) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_temp error.\n";
      goto orin_err;
    }
    if (get_orin_pciesw_link(&link) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_link error.\n";
      goto orin_err;
    }
    if (get_orin_pciesw_bw(&bw) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_bw error.\n";
      goto orin_err;
    }
    info.msg_.pciesw_host1_link_ = static_cast<uint8_t>(link.link0_);
    info.msg_.pciesw_host2_link_ = static_cast<uint8_t>(link.link1_);
    info.msg_.pciesw_host3_link_ = static_cast<uint8_t>(link.link2_);
    info.msg_.pciesw_host1_in_bw_ = static_cast<uint16_t>(bw.in0_bw_);
    info.msg_.pciesw_host1_eg_bw_ = static_cast<uint16_t>(bw.eg0_bw_);
    info.msg_.pciesw_host2_in_bw_ = static_cast<uint16_t>(bw.in1_bw_);
    info.msg_.pciesw_host2_eg_bw_ = static_cast<uint16_t>(bw.eg1_bw_);
    info.msg_.pciesw_host3_in_bw_ = static_cast<uint16_t>(bw.in2_bw_);
    info.msg_.pciesw_host3_eg_bw_ = static_cast<uint16_t>(bw.eg2_bw_);
    info.msg_.pciesw_temp_ = static_cast<int16_t>(temp);
    return true;
  }
orin_err:
  info.msg_.pciesw_host1_link_ = invalid_u8;
  info.msg_.pciesw_host2_link_ = invalid_u8;
  info.msg_.pciesw_host3_link_ = invalid_u8;
  info.msg_.pciesw_host1_in_bw_ = invalid_u16;
  info.msg_.pciesw_host1_eg_bw_ = invalid_u16;
  info.msg_.pciesw_host2_in_bw_ = invalid_u16;
  info.msg_.pciesw_host2_eg_bw_ = invalid_u16;
  info.msg_.pciesw_host3_in_bw_ = invalid_u16;
  info.msg_.pciesw_host3_eg_bw_ = invalid_u16;
  info.msg_.pciesw_temp_ = invalid_s16;
  return false;
}
std::string vh::AcuOrinPciesw::print_dv_msg(const AcuDvOrinBaseInfoUn &info) {
  std::stringstream ss;
  ss << PcieswDebug << "pciesw_host1_link=" << (int)info.msg_.pciesw_host1_link_
     << ", pciesw_host1_in_bw=" << info.msg_.pciesw_host1_in_bw_
     << ", pciesw_host1_eg_bw=" << info.msg_.pciesw_host1_eg_bw_
     << ", pciesw_host2_link=" << (int)info.msg_.pciesw_host2_link_
     << ", pciesw_host2_in_bw=" << info.msg_.pciesw_host2_in_bw_
     << ", pciesw_host2_eg_bw=" << info.msg_.pciesw_host2_eg_bw_
     << ", pciesw_host3_link=" << (int)info.msg_.pciesw_host3_link_
     << ", pciesw_host3_in_bw=" << info.msg_.pciesw_host3_in_bw_
     << ", pciesw_host3_eg_bw=" << info.msg_.pciesw_host3_eg_bw_
     << ", pciesw_temp=" << info.msg_.pciesw_temp_;
  return ss.str();
}
int vh::AcuOrinPciesw::test_dv_msg(int repeat, uint16_t ms) {
  int ret = 0;
  if (!dv_init(nullptr)) return -1;
  do {
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
    AcuDvOrinBaseInfoUn module;
    if (get_dv_msg(module) == false) {
      ret = -1;
      break;
    }
    std::cout << print_dv_msg(module) << "\n";
  } while (--repeat);
  dv_deinit();
  return ret;
}
int vh::AcuOrinPciesw::test_dv_crc_msg() {
  int ret = 0;
  uint8_t orin_id;
  AcuOrinModule::get_orin_id(&orin_id);
  if (orin_id == kAcuOrinIdA) {
    std::cout << PcieswDebug << "pciesw_crc_msgs: true\n";
    return 0;
  }
  if (!dv_init(nullptr)) return -1;
  if (get_orin_pciesw_crc() < 0) {
    ret = -1;
    LOG(ERROR) << PcieswDebug << "orin_pciesw_crc error.\n";
  }
  std::cout << print_dv_crc_msg() << "\n";
  dv_deinit();
  return ret;
}

int vh::AcuOrinPciesw::mt_init(void *arg) {
  static_cast<void>(arg);
  static std::atomic<bool> flag(false);
  bool exepected = false;
  if (!flag.compare_exchange_strong(exepected, true)) return 0;

  if (GpiodInterface::gpiod_set_up(ORIN_PCIE_GPIO_CHIP, ORIN_PCIE_GPIO_LINE, 1,
                                   &chip_, &line_,
                                   ORIN_PCIE_GPIO_CONSUMER) < 0) {
    GpiodInterface::gpiod_set_down(chip_, line_);
    return -1;
  }

  if (serial_.open_serial() == false) return -1;
  return 0;
}
void vh::AcuOrinPciesw::mt_deinit() {
  if (chip_ != nullptr && line_ != nullptr) {
    GpiodInterface::gpiod_set_down(chip_, line_);
    chip_ = nullptr;
    line_ = nullptr;
  }
  if (serial_.is_open()) serial_.close_serial();
}
bool vh::AcuOrinPciesw::get_mt_msg(AcuMtPcieswSt &info) {
  uint8_t orin_id;
  AcuOrinModule::get_orin_id(&orin_id);
  if (orin_id == kAcuOrinIdA) {
    // info.temp_ = INT32_MIN;
    // info.host1_link_ = 0;
    // info.host2_link_ = 0;
    // info.host3_link_ = 0;
    // info.host1_in_bw_ = 0;
    // info.host1_eg_bw_ = 0;
    // info.host2_in_bw_ = 0;
    // info.host2_eg_bw_ = 0;
    // info.host3_in_bw_ = 0;
    // info.host3_eg_bw_ = 0;
    return true;
  } else if (orin_id == kAcuOrinIdB) {
    int32_t temp;
    OrinPcieswLinkSt link;
    OrinPcieswBwSt bw;

    if (get_orin_pciesw_temp(&temp) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_temp error.\n";
      return false;
    }
    if (get_orin_pciesw_link(&link) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_link error.\n";
      return -1;
    }
    if (get_orin_pciesw_bw(&bw) < 0) {
      LOG(ERROR) << PcieswDebug << "orin_pciesw_bw error.\n";
      return -1;
    }
    info.temp_ = temp;
    info.host1_link_ = link.link0_;
    info.host2_link_ = link.link1_;
    info.host3_link_ = link.link2_;
    info.host1_in_bw_ = bw.in0_bw_;
    info.host1_eg_bw_ = bw.eg0_bw_;
    info.host2_in_bw_ = bw.in1_bw_;
    info.host2_eg_bw_ = bw.eg1_bw_;
    info.host3_in_bw_ = bw.in2_bw_;
    info.host3_eg_bw_ = bw.eg2_bw_;
    return true;
  }
  {
    return false;
  }
}
std::string vh::AcuOrinPciesw::print_mt_msg(const AcuMtPcieswSt &info) {
  std::stringstream ss;
  ss << PcieswDebug << "pciesw_temp=" << info.temp_
     << "pciesw_host1_link=" << info.host1_link_
     << "pciesw_host1_in_bw=" << info.host1_in_bw_
     << "pciesw_host1_eg_bw=" << info.host1_eg_bw_
     << "pciesw_host2_link=" << info.host2_link_
     << "pciesw_host2_in_bw=" << info.host2_in_bw_
     << "pciesw_host2_eg_bw=" << info.host2_eg_bw_
     << "pciesw_host3_link=" << info.host3_link_
     << "pciesw_host3_in_bw=" << info.host3_in_bw_
     << "pciesw_host3_eg_bw=" << info.host3_eg_bw_;
  return ss.str();
}
int vh::AcuOrinPciesw::test_mt_msg(int repeat, uint16_t ms) {
  int ret = 0;
  mt_init(nullptr);
  do {
    std::this_thread::sleep_for(std::chrono::milliseconds(ms));
    AcuMtPcieswSt info;
    if (get_mt_msg(info) == false) {
      ret = -1;
      break;
    }
    std::cout << print_mt_msg(info) << "\n";
  } while (--repeat);
  return ret;
}

int vh::AcuOrinPciesw::get_orin_pciesw_temp(void *temp) {
  int *arg = static_cast<int *>(temp);
  if (!serial_.is_open()) return -1;

  auto cmds = pciesw_maps_["temp_read"];
  std::string raw_str;
  std::vector<std::string> sub_strs;
  std::string remain_str;
  for (auto cmd : cmds) {
    raw_str.clear();
    sub_strs.clear();

    if (serial_.write_serial(cmd) < 0) return -1;
    while (true) {
      char arr[1024] = {0};
      int ms = kOrinPcieswUartTimeout;
      ssize_t nread = serial_.read_serial_timeout(arr, sizeof(arr), ms);
      if (nread == 0)
        break;
      else if (nread < 0)
        return -1;
      raw_str += std::string(arr, nread);
      auto len = raw_str.size();
      if (len > 10 && raw_str.at(len - 1) == '>' && raw_str.at(len - 6) == ':')
        break;
    }
    LOG_IF(INFO, (ORIN_PCIE_DBG && (raw_str.size() > 0)))
        << PcieswDebug << "raw_str:" << raw_str << "\n";
    string_split(raw_str, "\n\n", sub_strs, remain_str);
    if (!sub_strs.size() || sub_strs[0].compare(0, 3, "gas")) {
      LOG(WARNING) << PcieswDebug << "temp total_raw:" << raw_str;
      return -1;
    }
  }
  int ret;
  if ((ret = get_orin_pciesw_temp_data(sub_strs, arg)) < 0) {
    LOG(WARNING) << PcieswDebug << "temp total_raw:" << raw_str;
  }
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_temp_data(std::vector<std::string> &strs,
                                                 int *temp) {
  if (strs.size() != 3) return -1;

  int ret = 0;
  int info;
  try {
    info = std::stoul(strs[2], 0, 16) / 100;  // unit 1°C
  } catch (const std::exception &e) {
    LOG(ERROR) << e.what() << '\n';
    ret = -1;
  }
  if (ret == 0) {
    *temp = info;
  }

  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_link(void *link) {
  OrinPcieswLinkSt *arg = static_cast<OrinPcieswLinkSt *>(link);
  if (!serial_.is_open()) return -1;

  auto cmds = pciesw_maps_["link_read"];
  std::string raw_str;
  std::vector<std::string> sub_strs;
  std::string remain_str;
  for (auto cmd : cmds) {
    raw_str.clear();
    sub_strs.clear();

    if (serial_.write_serial(cmd) < 0) return -1;
    while (true) {
      char arr[1024] = {0};
      int ms = kOrinPcieswUartTimeout;
      ssize_t nread = serial_.read_serial_timeout(arr, sizeof(arr), ms);
      if (nread == 0)
        break;
      else if (nread < 0)
        return -1;
      raw_str += std::string(arr, nread);
      auto len = raw_str.size();
      if (len > 10 && raw_str.at(len - 1) == '>' && raw_str.at(len - 6) == ':')
        break;
    }
    LOG_IF(INFO, (ORIN_PCIE_DBG && (raw_str.size() > 0)))
        << PcieswDebug << "raw_str:" << raw_str << "\n";
    string_split(raw_str, "\n\n", sub_strs, remain_str); 
    if (!sub_strs.size() || sub_strs[0].compare(0, 3, "gas")) {
      LOG(WARNING) << PcieswDebug << "link total_raw:" << raw_str;
      return -1;
    }
  }
  int ret;
  if ((ret = get_orin_pciesw_link_data(sub_strs, arg)) < 0) {
    LOG(WARNING) << PcieswDebug << "link total_raw:" << raw_str;
  }
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_link_data(std::vector<std::string> &strs,
                                                 OrinPcieswLinkSt *link) {
  if (strs.size() != 11) return -1;

  int ret = 0;
  OrinPcieswLinkMsgUn msg;
  try {
    for (size_t i = 0; i < (strs.size() - 2); i++)
      msg.var4_[i] = std::stoul(strs[i + 2], 0, 16);
  } catch(const std::exception& e) {
    LOG(ERROR) << e.what() << '\n';
    ret = -1;
  }
  if(ret == 0) {
    link->link0_ = msg.msg_.link1_dw2_.link_up_;
    link->link1_ = msg.msg_.link2_dw2_.link_up_;
    link->link2_ = msg.msg_.link3_dw2_.link_up_;
  }
  
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_bw(void *bw) {
  OrinPcieswBwSt *arg = static_cast<OrinPcieswBwSt *>(bw);
  if (!serial_.is_open()) return -1;

  auto cmds = pciesw_maps_["bw_read"];
  std::string raw_str;
  std::vector<std::string> sub_strs;
  std::string remain_str;
  for (auto cmd : cmds) {
    raw_str.clear();
    sub_strs.clear();

    if (serial_.write_serial(cmd) < 0) return -1;
    while (true) {
      char arr[1024] = {0};
      int ms = kOrinPcieswUartTimeout;
      ssize_t nread = serial_.read_serial_timeout(arr, sizeof(arr), ms);
      if (nread == 0)
        break;
      else if (nread < 0)
        return -1;
      raw_str += std::string(arr, nread);
      auto len = raw_str.size();
      if (len > 10 && raw_str.at(len - 1) == '>' && raw_str.at(len - 6) == ':')
        break;
    }
    LOG_IF(INFO, (ORIN_PCIE_DBG && (raw_str.size() > 0)))
        << PcieswDebug << "raw_str:" << raw_str << "\n";
    string_split(raw_str, "\n\n", sub_strs, remain_str);
    if (!sub_strs.size() || sub_strs[0].compare(0, 3, "gas")) {
      LOG(WARNING) << PcieswDebug << "bw total_raw:" << raw_str;
      return -1;
    } 
  }
  int ret;
  if((ret = get_orin_pciesw_bw_data(sub_strs, arg)) < 0) {
    LOG(WARNING) << PcieswDebug << "bw total_raw:" << raw_str;
  }
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_bw_data(std::vector<std::string> &strs,
                                               OrinPcieswBwSt *bw) {
  if (strs.size() != 44) return -1;
  
  int ret = 0;
  OrinPcieswBwMsgUn msg;
  try {
    for (size_t i = 0; i < (strs.size() - 2); i++) {
      msg.var4_[i] = std::stoul(strs[i + 2], 0, 16);
    }
  } catch(const std::exception& e) {
    LOG(ERROR) << e.what() << '\n';
    ret = -1;
  }
  
  if(ret == 0) {
    int64_t us = msg.msg8_.us0_;
    double eg0_total = msg.msg8_.eg0_posted_ + msg.msg8_.eg0_completion_ +
                      msg.msg8_.eg0_nonposted_;
    double in0_total = msg.msg8_.in0_posted_ + msg.msg8_.in0_completion_ +
                      msg.msg8_.in0_nonposted_;
    double eg1_total = msg.msg8_.eg1_posted_ + msg.msg8_.eg1_completion_ +
                      msg.msg8_.eg1_nonposted_;
    double in1_total = msg.msg8_.in1_posted_ + msg.msg8_.in1_completion_ +
                      msg.msg8_.in1_nonposted_;
    double eg2_total = msg.msg8_.eg2_posted_ + msg.msg8_.eg2_completion_ +
                      msg.msg8_.eg2_nonposted_;
    double in2_total = msg.msg8_.in2_posted_ + msg.msg8_.in2_completion_ +
                      msg.msg8_.in2_nonposted_;
    if (us == 0) {
      bw->eg0_bw_ = 0;
      bw->in0_bw_ = 0;
      bw->eg1_bw_ = 0;
      bw->in1_bw_ = 0;
      bw->eg2_bw_ = 0;
      bw->in2_bw_ = 0;
    } else {
      bw->eg0_bw_ = (eg0_total / us) * kOrinPcieswBwRatio;  // unit 1MB/s
      bw->in0_bw_ = (in0_total / us) * kOrinPcieswBwRatio;
      bw->eg1_bw_ = (eg1_total / us) * kOrinPcieswBwRatio;
      bw->in1_bw_ = (in1_total / us) * kOrinPcieswBwRatio;
      bw->eg2_bw_ = (eg2_total / us) * kOrinPcieswBwRatio;
      bw->in2_bw_ = (in2_total / us) * kOrinPcieswBwRatio;
    }
  }
  return ret;
}

int vh::AcuOrinPciesw::get_orin_pciesw_crc() {
  if (!serial_.is_open()) {
    LOG(ERROR) << PcieswDebug << "orin_pciesw_crc: serial not open\n";
    return -1;
  }

  auto cmds = pciesw_maps_["dev_crc_read"];
  std::string raw_str;
  std::vector<std::string> sub_strs;
  std::string remain_str;
  for (auto cmd : cmds) {
    raw_str.clear();
    sub_strs.clear();

    if (serial_.write_serial(cmd) < 0) return -1;
    while (true) {
      char arr[1024] = {0};
      int ms = kOrinPcieswUartCrcTimeout;
      ssize_t nread = serial_.read_serial_timeout(arr, sizeof(arr), ms);
      if (nread == 0)
        break;
      else if (nread < 0)
        return -1;
      raw_str += std::string(arr, nread);
      auto len = raw_str.size();
      if (len > 10 && raw_str.at(len - 1) == '>' && raw_str.at(len - 6) == ':')
        break;
    }
    string_split(raw_str, "\n\n", sub_strs, remain_str);
    if (!sub_strs.size() || sub_strs[0].compare(0, 3, "gas")) {
      LOG(WARNING) << PcieswDebug << "crc total_raw:" << raw_str;
      return -1;
    } 
  }
  int ret;
  if((ret = get_orin_pciesw_crc_data(sub_strs)) < 0) {
    LOG(WARNING) << PcieswDebug << "crc total_raw:" << raw_str;
  }
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_crc_data(
    std::vector<std::string> &strs) {
  if (strs.size() != 82) return -1;
  crc_.map0_ = strs.at(0 + 2);
  crc_.map1_ = strs.at(8 + 2);
  crc_.key0_ = strs.at(16 + 2);
  crc_.key1_ = strs.at(24 + 2);
  crc_.bl20 = strs.at(32 + 2);
  crc_.bl21 = strs.at(40 + 2);
  crc_.data0_ = strs.at(48 + 2);
  crc_.data1_ = strs.at(56 + 2);
  crc_.image0_ = strs.at(64 + 2);
  crc_.image1_ = strs.at(72 + 2);
  return 0;
}
std::string vh::AcuOrinPciesw::print_dv_crc_msg() {
  std::stringstream ss;
  ss << PcieswDebug << "pciesw_crc_msgs:"
     << ", map0=" << crc_.map0_ << ", map1=" << crc_.map1_
     << ", key0=" << crc_.key0_ << ", key1=" << crc_.key1_
     << ", bl20=" << crc_.bl20 << ", bl21=" << crc_.bl21
     << ", data0=" << crc_.data0_ << ", data1=" << crc_.data1_
     << ", image0=" << crc_.image0_ << ", image1=" << crc_.image1_;
  return ss.str();
}

int vh::AcuOrinPciesw::get_orin_pciesw_hversion(void *hversion) {
  if (!serial_.is_open()) return -1;

  OrinPcieswDevinfoSt *arg = static_cast<OrinPcieswDevinfoSt *>(hversion);
  auto cmds = pciesw_maps_["dev_hversion_read"];
  std::string raw_str;
  std::vector<std::string> sub_strs;
  std::string remain_str;
  for (auto cmd : cmds) {
    raw_str.clear();
    sub_strs.clear();

    if (serial_.write_serial(cmd) < 0) return -1;
    while (true) {
      char arr[1024] = {0};
      int ms = kOrinPcieswUartTimeout;
      ssize_t nread = serial_.read_serial_timeout(arr, sizeof(arr), ms);
      if (nread == 0)
        break;
      else if (nread < 0)
        return -1;
      raw_str += std::string(arr, nread);
      auto len = raw_str.size();
      if (len > 10 && raw_str.at(len - 1) == '>' && raw_str.at(len - 6) == ':')
        break;
    }
    LOG_IF(INFO, (ORIN_PCIE_DBG && (raw_str.size() > 0)))
        << PcieswDebug << "hversion raw_str:" << raw_str << "\n";
    string_split(raw_str, "\n\n", sub_strs, remain_str);
    if (!sub_strs.size() || sub_strs[0].compare(0, 3, "gas")) {
      LOG(ERROR) << PcieswDebug << "hversion total_strs:" << raw_str;
      return -1;
    }
  }
  int ret;
  if ((ret = get_orin_pciesw_hversion_data(sub_strs, arg)) < 0) {
    LOG(ERROR) << PcieswDebug << "hversion total_strs:" << raw_str;
  }
  return ret;
}
int vh::AcuOrinPciesw::get_orin_pciesw_hversion_data(
    std::vector<std::string> &strs, OrinPcieswDevinfoSt *hversion) {
  if (strs.size() != 4) return -1;
  int ret = 0;
  OrinPcieswDevinfoSt tmp;
  try {
    uint32_t value = std::stoul(strs[2], 0, 16);
    tmp.image_phase_ = value & 0xFF;
    tmp.major_hw_rev_ = (value >> 8) & 0x0F;
    tmp.switchtec_generation_ = (value >> 12) & 0x01;
    tmp.chip_id_ = (value >> 16) & 0xFF;
    tmp.fw_ver_ = std::stoul(strs[3], 0, 16);
  } catch (const std::exception &e) {
    LOG(ERROR) << e.what() << '\n';
    ret = -1;
  }
  if (ret == 0) memmove(hversion, &tmp, sizeof(OrinPcieswDevinfoSt));

  return ret;
}
std::string vh::AcuOrinPciesw::get_dv_hversion_str() {
  static std::unordered_map<uint8_t, std::string> image_phase_maps = {
      {1, "BL1"}, {2, "BL2"}, {3, "Main FW"}};
  static std::unordered_map<uint8_t, std::string> switchtec_generation_maps = {
      {0, "Gen4"}, {1, "Gen5"}};
  static std::unordered_map<uint8_t, std::string> major_hw_maps = {
      {0, "Rev B"}, {0x0F, "Rev A"}};

  uint8_t orin_id;
  AcuOrinModule::get_orin_id(&orin_id);
  if (orin_id == kAcuOrinIdA) {
    return "";
  }

  OrinPcieswDevinfoSt hversion;
  memset(&hversion, 0, sizeof(OrinPcieswDevinfoSt));
  if (get_orin_pciesw_hversion(&hversion) < 0) {
    return "";
  }
  std::stringstream ss;
  try {
    ss << "pciesw Image Phase: " << image_phase_maps[hversion.image_phase_]
       << "\n"
       << "pciesw Switchtec Generation: "
       << switchtec_generation_maps[hversion.switchtec_generation_] << "\n"
       << "pciesw Major HW Rev: " << major_hw_maps[hversion.major_hw_rev_]
       << "\n"
       << "pciesw Chip ID: " << hversion.chip_id_ << "\n"
       << "pciesw FW Version: " << hversion.fw_ver_;
  } catch (const std::exception &e) {
    LOG(ERROR) << e.what() << '\n';
    return "";
  }
  return ss.str();
}