#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_CHECK_LANE_CENTER_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_CHECK_LANE_CENTER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"

extern uint16_t CheckLaneCenteringCondition(
    const VSI_VehicleInfo_Struct *vehicle_info_ptr,
    const CSI_LaneInfo_Struct *lane_info_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_CHECK_LANE_CENTER_H_
