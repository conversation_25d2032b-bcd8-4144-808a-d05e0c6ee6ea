#include "prediction/common/ddp_path/prediction_path_costing.h"

#include <vector>

#include <glog/logging.h>

#include <algorithm>
#include "control_util/costing_util.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve_proximity_cache.h"
#include "math/eigen_util.h"
#include "math/math_util.h"
#include "prediction/common/ddp_path/primitive_definition_prediction_path.h"
#include "voy_protos/control_util.pb.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/vehicle_model.pb.h"

namespace prediction {

using StateIndex = PredictionPathFormula::StateIndex;
using ControlIndex = PredictionPathFormula::ControlIndex;
using CostingUtil = PredictionPathFormula::FormulaCostingUtil;
using FeatureParameter = PredictionPathFormula::PbFeatureParameter;

using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;
using Hamiltonian = CostingUtil::Hamiltonian;

constexpr int X_DIM = PredictionPathFormula::StateIndex::X_DIM;

using State = PredictionPathFormula::State;
using Control = PredictionPathFormula::Control;

namespace {

// Computes curvature constraint.
Feature ConstraintKappa(const State& x, double max_abs_kappa) {
  const double kappa = x[StateIndex::KAPPA];
  const double kappa_violation =
      control_util::ComputeViolation(kappa, -max_abs_kappa, max_abs_kappa);

  Feature feature(1);
  feature.components[0].val = kappa_violation;
  feature.components[0].jacob[StateIndex::KAPPA] = 1.0;

  return feature;
}

// Computes curvature rate constraint.
Feature ConstraintEta(const Control& u, double max_abs_eta) {
  const double eta = u[ControlIndex::ETA];
  const double eta_violation =
      control_util::ComputeViolation(eta, -max_abs_eta, max_abs_eta);

  Feature feature(1);
  feature.components[0].val = eta_violation;
  feature.components[0].jacob[X_DIM + ControlIndex::ETA] = 1.0;

  return feature;
}

// Computes curvature effort.
Feature EffortKappa(const State& x) {
  const double kappa = x[StateIndex::KAPPA];

  Feature feature(1);
  feature.components[0].val = kappa;
  feature.components[0].jacob[StateIndex::KAPPA] = 1.0;

  return feature;
}

// Computes curvature rate effort.
Feature EffortEta(const Control& u) {
  const double eta = u[ControlIndex::ETA];

  Feature feature(1);
  feature.components[0].val = eta;
  feature.components[0].jacob[X_DIM + ControlIndex::ETA] = 1.0;

  return feature;
}

// Computes nominal path attraction primitive.
Feature PrimitiveSoftNominalAttraction(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    math::geometry::PolylineCurve2dProximityCache* cached_poly) {
  const double l =
      shape_measurement.length() - shape_measurement.rear_bumper_to_rear_axle();
  const double sin_theta = std::sin(theta);
  const double cos_theta = std::cos(theta);

  math::EVector<2> query_pt;
  query_pt << front_bumper_pos.x(), front_bumper_pos.y();

  double dist = 0.0;
  math::EVector<2> d_dist = math::EVector<2>::Zero();
  cached_poly->Query(query_pt, &dist, &d_dist);

  Feature feature(1);
  feature.components[0].val = dist;
  feature.components[0].jacob[StateIndex::X_POS] = d_dist(0);
  feature.components[0].jacob[StateIndex::Y_POS] = d_dist(1);
  feature.components[0].jacob[StateIndex::THETA] =
      -l * sin_theta * d_dist(0) + l * cos_theta * d_dist(1);

  return feature;
}

// Computes the polyline distance violation given the a circle-based coverage of
// the vehicle.
Feature ComputeFullBodyPolylineViolationFeature(
    const std::vector<vehicle_model::DiskInfo>& ego_disks, double theta,
    math::pb::Side side,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    double side_buffer_m = 0.0) {
  if (cached_poly == nullptr) {
    return Feature();
  }

  Feature feature(ego_disks.size());

  const double sin_theta = std::sin(theta);
  const double cos_theta = std::cos(theta);

  for (size_t i = 0; i < ego_disks.size(); ++i) {
    math::EVector2x1 query_pt;
    query_pt << ego_disks[i].disk.center().x(), ego_disks[i].disk.center().y();
    double dist = 0.0;
    math::EVector2x1 d_dist = math::EVector2x1::Zero();
    cached_poly->Query(query_pt, &dist, &d_dist);

    bool update_jacob(false);
    switch (side) {
      case math::pb::kLeft:
        feature.components[i].val =
            std::max(dist + side_buffer_m + ego_disks[i].disk.radius(), 0.0);
        update_jacob = feature.components[i].val > 0.0;
        break;
      case math::pb::kRight:
        feature.components[i].val =
            std::min(dist - side_buffer_m - ego_disks[i].disk.radius(), 0.0);
        update_jacob = feature.components[i].val < 0.0;
        break;
      default:
        DVLOG(2) << "Unknown nudge side";
        break;
    }

    if (update_jacob) {
      const double l = ego_disks[i].lon_offset;
      const double w = ego_disks[i].lat_offset;
      feature.components[i].jacob[StateIndex::X_POS] = d_dist(0);
      feature.components[i].jacob[StateIndex::Y_POS] = d_dist(1);
      feature.components[i].jacob[StateIndex::THETA] =
          (-l * sin_theta - w * cos_theta) * d_dist(0) +
          (l * cos_theta - w * sin_theta) * d_dist(1);
    }
  }

  return feature;
}

}  // namespace

control_util::pb::PredictionPathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PredictionPathFeatureParameter feature_param,
    const vehicle_model::GeometricModelWithAxleRectangularShape&
        motion_model_with_shape) {
  *feature_param.mutable_shape_measurement() =
      motion_model_with_shape.shape_measurement();
  return feature_param;
}

}  // namespace prediction

namespace control_util {
template <>
prediction::PredictionPathFormula::StepwisePrimitives
prediction::PredictionPathFormula::GetStepwisePrimitives(
    int /*step*/, bool /*is_terminal*/, const State& x, const Control& /*u*/,
    const Primitives& /*primitives*/) const {
  return {.ego_representation = vehicle_model::VehicleRepresentation(
              feature_param_.shape_measurement(), x[StateIndex::X_POS],
              x[StateIndex::Y_POS], x[StateIndex::THETA],
              /*disk_numbers=*/{3})};
}

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kPredictionPath>(
    int step, int total_step_size,
    const prediction::PredictionPathFormula::PbCostingTerm& costing_term,
    const prediction::PredictionPathFormula::PbFeatureParameter& feature_param,
    const prediction::PredictionPathFormula::State& x,
    const prediction::PredictionPathFormula::Control& u,
    const prediction::PredictionPathFormula::StepwisePrimitives&
        stepwise_primitives,
    const prediction::PredictionPathFormula::Primitives& primitives,
    prediction::PredictionPathFormula::Feature* feature) {
  // Nudge corridor buffer distance.
  constexpr double kNudgeBufferDistanceInMeter = 0.1;  // m

  const std::vector<vehicle_model::DiskInfo>& ego_disks =
      stepwise_primitives.ego_representation.disks().at(
          vehicle_model::kDefaultEgoDiskNumber);
  const math::geometry::OrientedBoxWithCache2d& ego_box =
      stepwise_primitives.ego_representation.box();
  bool is_terminal = (step == total_step_size);
  const auto& feature_type = costing_term.feature_type();
  switch (feature_type) {
    case prediction::PredictionPathFormula::PbFeatureType::
        CONSTRAINT_PREDICTION_KAPPA:
      (*feature) =
          prediction::ConstraintKappa(x, feature_param.max_abs_kappa());
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        CONSTRAINT_PREDICTION_ETA:
      (*feature) = is_terminal ? prediction::Feature(1)
                               : prediction::ConstraintEta(
                                     u, feature_param.max_abs_eta());
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        EFFORT_PREDICTION_KAPPA:
      (*feature) = prediction::EffortKappa(x);
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        EFFORT_PREDICTION_ETA:
      (*feature) =
          is_terminal ? prediction::Feature(1) : prediction::EffortEta(u);
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_PREDICTION_LEFT_DRIVING_CORRIDOR:
      (*feature) = prediction::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[prediction::StateIndex::THETA], math::pb::kLeft,
          primitives.driving_corridor.left_boundary.get(),
          kNudgeBufferDistanceInMeter);
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_PREDICTION_RIGHT_DRIVING_CORRIDOR:
      (*feature) = prediction::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[prediction::StateIndex::THETA], math::pb::kRight,
          primitives.driving_corridor.right_boundary.get(),
          kNudgeBufferDistanceInMeter);
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        SOFT_BOUNDARY_VIOLATION_LEFT_CORRIDOR:
      // TODO(liyou): this feature can be upgraded to save computation,
      // such as a wall-like barrier. As long as the object's pose of the
      // trajectory doesn't intersect with this boundary, this feature can
      // be empty.
      (*feature) = prediction::ComputeFullBodyPolylineViolationFeature(
          ego_disks, x[prediction::StateIndex::THETA], math::pb::kLeft,
          primitives.soft_boundary.left_boundary.get());
      break;
    case prediction::PredictionPathFormula::PbFeatureType::
        PRIMITIVE_PREDICTION_NOMINAL_ATTRACTION:
      (*feature) = prediction::PrimitiveSoftNominalAttraction(
          /*front_bumper_pos=*/ego_box.center_segment().end(),
          x[prediction::StateIndex::THETA], feature_param.shape_measurement(),
          primitives.lane_geometry.nominal_path.get());
      break;
    // Not supported yet.
    case prediction::PredictionPathFormula::PbFeatureType::
        SOFT_BOUNDARY_VIOLATION_RIGHT_CORRIDOR:
    default:
      DCHECK(false) << "Unrecognized feature type";
      (*feature) = prediction::Feature(1);
      break;
  }
}

}  // namespace control_util
