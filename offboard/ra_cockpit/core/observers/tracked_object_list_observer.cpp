#include "tracked_object_list_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "base/now.h"
#include "core/type_traits.h"
#include "glog/logging.h"

namespace remote_assist {

TrackedObjectListObserver::TrackedObjectListObserver(
    TrackedObjectListCallback to_list_callback, PoseCallback pose_callback,
    LatencyTraceCallback latency_callback)
    : to_list_callback_(std::move(to_list_callback)),
      pose_callback_(std::move(pose_callback)),
      latency_callback_(std::move(latency_callback)) {}

bool TrackedObjectListObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kTrackedObjectList);
}

std::shared_ptr<google::protobuf::Message> TrackedObjectListObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto obj_list = std::make_shared<voy::TrackedObjectList>();
  if (!obj_list->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error TrackedObjectList message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  // 创建延迟跟踪信息
  auto trace = std::make_shared<LatencyTrace>();
  trace->rcv_time = base::Now();
  trace->net_latency = trace->rcv_time - message.timestamp();
  trace->msg_type = message.message_type();
  if (message.has_trace_route()) {
    trace->trace_route = message.trace_route();
  }

  LOG(INFO) << "TrackedObjectListObserver - Created latency trace: "
            << "msg_type=" << trace->msg_type
            << ", rcv_time=" << trace->rcv_time
            << ", net_latency=" << trace->net_latency;

  voy::Pose pose_msg = obj_list->pose();

  if (pose_callback_) {
    pose_callback_(pose_msg, data_channel);
  }
  if (to_list_callback_) {
    to_list_callback_(obj_list, data_channel);
  }

  // 传递延迟信息
  if (latency_callback_) {
    LOG(INFO) << "TrackedObjectListObserver - Calling latency callback";
    latency_callback_(trace, obj_list, data_channel);
  } else {
    LOG(WARNING)
        << "TrackedObjectListObserver - No latency callback registered";
  }

  return obj_list;
}

}  // namespace remote_assist
