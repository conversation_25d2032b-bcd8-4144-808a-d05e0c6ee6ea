#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_DEBOUNCE_METHOD_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_DEBOUNCE_METHOD_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

extern void CML_SetResetFlipFlop(bool set, bool reset, bool *output);

extern void CML_TurnOnDelay(bool input, float delay_time, float task_cycle_time,
                            float *timer_ptr, bool *output_ptr);

extern void CML_TurnOffDelay(bool input, float delay_time,
                             float task_cycle_time, float *timer, bool *output);

extern void CML_Hysteresis(float input, float lower_threshold,
                           float upper_threshold, bool *output_ptr);

extern void CML_BilateralHysteresis(float input, float min, float max,
                                    float hys, bool *output_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_DEBOUNCE_METHOD_H_
