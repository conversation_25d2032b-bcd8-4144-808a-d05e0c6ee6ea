
#ifndef OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_SIGNAL_DECODE_CAN_SIGNAL_DECODE_H_
#define OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_SIGNAL_DECODE_CAN_SIGNAL_DECODE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

VehicleMode GetVehicleMode(bool vim_module_enable);
bool HoldEmergencySwitch(FallbackMode fallback_mode);
FallbackMode GetFallbackMode(uint8 main_can_counter,
                             FallbackMode main_fallback_mode,
                             uint8 redundant_can_counter,
                             FallbackMode redundant_fallback_mode);
bool FallbackIsActive(VehicleMode vehicle_mode, FallbackMode fallback_mode,
                      bool disable_on_freeway);
bool CollisionDetectionIsValid(uint8 can_counter, uint8 collision_detection);

extern void ConvertVehicleAndAcuDbcSignals(VehicleAndAcuCanSignal *can_signal);

extern void GetAlgorithmRequiredChassisInfo(
    const VehicleAndAcuCanSignal *can_signal,
    VSI_VehicleInfo_Struct *vehicle_info);

extern void GetSensorRequiredChassisInfo(
    const VehicleAndAcuCanSignal *can_signal,
    VSI_VehInfoFor1V1R_Struct *sensor_chassis_info);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_SIGNAL_DECODE_CAN_SIGNAL_DECODE_H_
