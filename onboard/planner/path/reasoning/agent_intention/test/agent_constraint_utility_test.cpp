#include <map>
#include <memory>

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline.h"
#include "google/protobuf/text_format.h"
#include "gtest/gtest.h"

#include "geometry/model/polyline_curve.h"
#include "log_utils/log_macros.h"
#include "math/curve2d.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/path_solver/constraint_manager/repulsion_util.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/path/reasoning/agent_intention/agent_constraint_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state_generator.h"
#include "planner/speed/overlap/test/overlap_lib_test_util.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/test_util/test_util.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "voy_protos/math.pb.h"

namespace planner {

namespace {

// mock ra to front/rear to make computation easier
constexpr double kEgoRaToFrontAppro = 3.0;
constexpr double kEgoRaToRearAppro = 2.0;
constexpr double kEgoCurrentArcLength = 10.0;  // in m
constexpr double kMaxEgoDuration = 10.0;       // in s
constexpr double kConstSpeed = 5.0;            // in m/s
constexpr double kSampleTime = 1.0;            // in s

}  // namespace

class RepulsionMetaManagerTest : public testing::Test {
 protected:
  math::geometry::PolylineCurve2d selected_speed_profile_st_curve_;
  math::geometry::PolylineCurve2d reference_path_;
  adv_geom::PathCurve2d path_curve_;
  int64 plan_init_timestamp_ = 0;
  TypedObjectId dummy_object_id_ =
      TypedObjectId(1, pb::ObjectSourceType::kTrackedObject);

  void SetUp() override {
    std::string reference_path_input =
        "LINESTRING(0.0 0.0,20.0 0,50.0 0,100.0 0.0)";
    reference_path_ = test::ReadLine(reference_path_input);
    path_curve_ = adv_geom::PathCurve2d(reference_path_);
    // construct a constant speed motion
    math::geometry::Polyline2d selected_speed_profile_st_points;
    for (int i = 0; i < kMaxEgoDuration / kSampleTime; ++i) {
      const double current_time = kSampleTime * i;
      selected_speed_profile_st_points.emplace_back(
          current_time, current_time * kConstSpeed + kEgoCurrentArcLength);
    }
    selected_speed_profile_st_curve_ =
        math::geometry::PolylineCurve2d(selected_speed_profile_st_points);
  }

  void TearDown() override {}
};

class TestGenerateAgentConstraint : public ::testing::Test {
 protected:
  void SetUp() override {
    lane_sequence_geometry_.nominal_path =
        planner::test::ReadLine("LINESTRING(0.0 0.0,10.0 0.0)");
    lane_sequence_geometry_.left_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 3.0,10.0 3.0)");
    lane_sequence_geometry_.right_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 -3.0,10.0 -3.0)");
    lane_sequence_geometry_.left_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(0.0 1.0,3.0 1.0)"),
        planner::test::ReadLine("LINESTRING(5.0 2.0,8.0 2.0)")};
    lane_sequence_geometry_.right_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(1.0 -1.0,4.0 -1.0)"),
        planner::test::ReadLine("LINESTRING(6.0 -2.0,9.0 -2.0)")};
    lane_sequence_geometry_.lane_sequence_contour =
        lane_selection::GetLaneBoundaryBorder(
            lane_sequence_geometry_.left_lane_boundary,
            lane_sequence_geometry_.right_lane_boundary,
            lane_sequence_geometry_.nominal_path);

    ego_param_ = planner::test::GetEgoInLaneParams();
    // Static object.
    auto& static_tracked_object = *static_agent_proto_.mutable_tracked_object();
    static_tracked_object.set_id(1);
    static_tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
    math::geometry::Convert(/*obj_polygon=*/
                            math::geometry::Polygon2d(
                                {math::geometry::Point2d(2.0, 1.5),
                                 math::geometry::Point2d(4.0, 1.5),
                                 math::geometry::Point2d(4.0, 2.5),
                                 math::geometry::Point2d(2.0, 2.5)}),
                            *static_tracked_object.mutable_contour());
    static_planner_object_ = std::make_unique<PlannerObject>(
        static_agent_proto_, /*timestamp=*/1,
        TrafficParticipantPose(/*timestamp=*/1, static_tracked_object));
    static_planner_object_->set_is_primary_stationary(true);
    // Dynamic object.
    dynamic_agent_proto_.mutable_tracked_object()->CopyFrom(
        planner::test::ConstructTrackedObjectFromDimension(
            /*obj_id=*/20,
            /*x0=*/1.0,
            /*y0=*/2.0,
            /*width=*/1.0, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
            /*heading=*/0.0));
    predicted_trajectory_wrapper_ =
        std::make_unique<PredictedTrajectoryWrapper>(
            planner::test::ConstructPredictedTrajectoryWrapperForTest(
                dynamic_agent_proto_.tracked_object(), proto_traj_));
    dynamic_planner_object_ = std::make_unique<PlannerObject>(
        dynamic_agent_proto_, /*timestamp=*/1,
        TrafficParticipantPose(/*timestamp=*/1,
                               dynamic_agent_proto_.tracked_object()));
  }

  EgoInLaneParams ego_param_;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  prediction::pb::Agent static_agent_proto_;
  prediction::pb::Agent dynamic_agent_proto_;
  std::unique_ptr<PlannerObject> static_planner_object_ = nullptr;
  std::unique_ptr<PlannerObject> dynamic_planner_object_ = nullptr;
  std::unique_ptr<PredictedTrajectoryWrapper> predicted_trajectory_wrapper_ =
      nullptr;
  prediction::pb::PredictedTrajectory proto_traj_;
  RequiredLateralGap required_lat_gap_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  IntentionResultMetaData static_agent_meta_ = IntentionResultMetaData(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/true, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_RIGHT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 10)},
      /*required_lat_gap=*/required_lat_gap_);
  IntentionResultMetaData dynamic_agent_meta_ = IntentionResultMetaData(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/false, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_RIGHT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 1)},
      /*required_lat_gap=*/required_lat_gap_);
};

TEST_F(TestGenerateAgentConstraint,
       AddCurrentPoseNominalConstraintConstructionZoneCase) {
  IntentionResultMetaData cz_meta(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kConstructionZone),
      /*blocking_state_size=*/20,
      /*is_static=*/true, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_RIGHT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 10)},
      /*required_lat_gap=*/required_lat_gap_);

  ConstructionZoneInLaneState cz_state;
  cz_state.id = 1;

  AddCurrentPoseNominalConstraint(cz_state, cz_meta.lateral_decision,
                                  cz_meta.required_lat_gap, cz_meta);

  ASSERT_EQ(cz_meta.agent_constraints.size(), 1);
  EXPECT_TRUE(cz_meta.agent_constraints[0].snapshots_groups.empty());
}

TEST_F(TestGenerateAgentConstraint,
       AddCurrentPoseNominalConstraintStaticAgentCase) {
  AgentInLaneStates static_agent_state;
  static_agent_state.object_id = 1;

  AddCurrentPoseNominalConstraint(static_agent_state,
                                  static_agent_meta_.lateral_decision,
                                  static_agent_meta_);

  ASSERT_EQ(static_agent_meta_.agent_constraints.size(), 1);
  EXPECT_TRUE(static_agent_meta_.agent_constraints[0].snapshots_groups.empty());
}

TEST_F(TestGenerateAgentConstraint,
       AddCurrentPoseNominalConstraintStaticAgentCaseForObjectOccupancyState) {
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  IntentionResultMetaData static_agent_meta(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/true, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_RIGHT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 10)},
      /*required_lat_gap=*/required_lat_gap);

  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers{
      PredictedTrajectoryWrapper(voy::TrackedObject(),
                                 static_predicted_trajectory,
                                 /*is_primary_trajectory=*/true)};
  ObjectOccupancyState static_object_state = GenerateObjectOccupancyState(
      *static_planner_object_, /*object_information=*/{},
      predicted_trajectory_wrappers, lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  AddCurrentPoseNominalConstraint(static_object_state,
                                  static_agent_meta.lateral_decision,
                                  static_agent_meta);
  ASSERT_EQ(static_agent_meta.agent_constraints.size(), 1);
  EXPECT_TRUE(static_agent_meta.agent_constraints[0].snapshots_groups.empty());
}

TEST_F(RepulsionMetaManagerTest, GetFinalRepulsionDirectionTest) {
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::IGNORE;
    bool add_only_overtaking = true;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_BOTH;
    bool add_only_overtaking = true;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kRight;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_LEFT;
    bool add_only_overtaking = true;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kLeft;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_RIGHT;
    bool add_only_overtaking = true;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::IGNORE;
    bool add_only_overtaking = false;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_BOTH;
    bool add_only_overtaking = false;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_FALSE(final_direction);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_LEFT;
    bool add_only_overtaking = false;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_TRUE(final_direction);
    EXPECT_EQ(final_direction, math::pb::kLeft);
  }
  {
    RepulsionMetaData::RepulsionDirection meta_data_direction =
        RepulsionMetaData::RepulsionDirection::kEither;
    pb::SnapshotIntention::PassState lateral_decision =
        pb::SnapshotIntention::PASS_RIGHT;
    bool add_only_overtaking = false;
    std::optional<math::pb::Side> final_direction =
        RepulsionMetaManager().GetFinalRepulsionDirection(
            meta_data_direction, lateral_decision, add_only_overtaking);

    EXPECT_TRUE(final_direction);
    EXPECT_EQ(final_direction, math::pb::kRight);
  }
}

TEST_F(RepulsionMetaManagerTest, AddRepulsionsFromDirectFrenetPointsDeathTest) {
  {
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kEither,
        /*frenet_boundary_in=*/{path::FrenetPoint(2.0, 3.0)},
        /*add_only_overtaking_in=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::CROSS_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt);
    math::pb::Side final_direction = math::pb::kOn;
    EXPECT_DEATH(RepulsionMetaManager::AddRepulsionsFromFrenetPoints(
                     repulsion_meta_data, final_direction, constraint_manager),
                 "");
  }
}

TEST_F(RepulsionMetaManagerTest, AddRepulsionsFromDirectFrenetPointsTest) {
  {
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kLeft,
        /*frenet_boundary_in=*/
        std::vector<path::FrenetPoint>{path::FrenetPoint(2.0, 3.0),
                                       path::FrenetPoint(3.0, 3.0)},
        /*add_only_overtaking_in=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ratio_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::CROSS_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt);
    RepulsionMetaManager::AddRepulsionsFromFrenetPoints(
        repulsion_meta_data, math::pb::kLeft, constraint_manager);
    EXPECT_EQ(constraint_manager.repulsions().right_frenet_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions().left_frenet_repulsions.size(), 0);
    const auto& repulsion =
        constraint_manager.repulsions().right_frenet_repulsions.front();
    EXPECT_EQ(repulsion.frenet_boundary->size(), 2);
    EXPECT_EQ(repulsion.strength, path::BoundaryStrength::kModerate);
    EXPECT_EQ(repulsion.frenet_boundary->front().s, 2.0);
    EXPECT_EQ(repulsion.frenet_boundary->front().l, 3.0);
  }
}

TEST_F(RepulsionMetaManagerTest, AddRepulsionsFromPolygonAndTrajectoryTest) {
  {
    // test normal leading agent
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(
            {{12.0, 2.0}, {15.0, 2.0}, {32.0, 2.0}}),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10,
            /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions()
                  .left_cartesian_repulsions.front()
                  .strength,
              path::BoundaryStrength::kModerate);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    const auto& info =
        constraint_manager.repulsions().object_repulsions_generating_infos.at(
            std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id));
    EXPECT_EQ(*info.collision_arclength_range_on_polygon_center_path_start_pos,
              0.0);
    EXPECT_EQ(*info.collision_arclength_range_on_polygon_center_path_end_pos,
              4.0);
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { x: 10 y: 6 } points { x: 10 y: 3 } points "
              "{ x: 10 y: 1 } points { x: 18 y: 1 } points { x: 18 y: 3 } "
              "points { x: 18 y: 6 } type: kLinear }");
  }

  {
    // test inside intersection
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(
            {{12.0, 2.0}, {15.0, 2.0}, {32.0, 2.0}}),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10,
            /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    math::geometry::Polyline2d selected_speed_profile_st_points;
    for (int i = 0; i < kMaxEgoDuration / kSampleTime; ++i) {
      const double current_time = kSampleTime * i;
      selected_speed_profile_st_points.emplace_back(
          current_time, current_time * 1.0 + kEgoCurrentArcLength);
    }
    math::geometry::PolylineCurve2d selected_speed_profile_st_curve =
        math::geometry::PolylineCurve2d(selected_speed_profile_st_points);
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions()
                  .left_cartesian_repulsions.front()
                  .strength,
              path::BoundaryStrength::kModerate);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    const auto& info =
        constraint_manager.repulsions().object_repulsions_generating_infos.at(
            std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id));
    EXPECT_EQ(*info.collision_arclength_range_on_polygon_center_path_start_pos,
              0.0);
    EXPECT_EQ(*info.collision_arclength_range_on_polygon_center_path_end_pos,
              12.0);
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { x: 10 y: 6 } points { x: 10 y: 3 } points { "
              "x: 10 y: 1 } points { x: 26 y: 1 } points { x: 26 y: 3 } points "
              "{ x: 26 y: 6 } type: kLinear }");
  }

  {
    // test normal leading agent with lat gap
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(
            {{12.0, 2.0}, {15.0, 2.0}, {32.0, 2.0}}),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10,
            /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    repulsion_meta_data.add_only_overtaking = false;
    repulsion_meta_data.add_only_not_overtaking = false;
    repulsion_meta_data.polygon = math::geometry::Polygon2d(
        {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}});
    // a linear path
    repulsion_meta_data.polygon_center_path = math::geometry::PolylineCurve2d(
        {{12.0, 2.0}, {15.0, 2.0}, {32.0, 2.0}});
    repulsion_meta_data.repulsion_required_lat_gap = 1.0;  // in m
    repulsion_meta_data.strength = path::BoundaryStrength::kModerate;
    repulsion_meta_data.heading = 0.0;  // towards x positive
    repulsion_meta_data.reasoner_id = pb::AgentReasonerId::LEAD_AND_MERGE_AGENT;
    repulsion_meta_data.polygon_center_profile_lower_bound =
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0);
    repulsion_meta_data.polygon_center_profile_upper_bound =
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10, /*dt=*/1.0);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { x: 9 y: 6 } points { x: 9 y: 3 } points { "
              "x: 9 y: 1 } points { x: 9.379999999999999 y: 0.2153981901626788 "
              "} points { x: 10 } points { x: 18 } points { x: "
              "18.784601809837323 y: 0.37999999999999989 } points { x: 19 y: 1 "
              "} points { x: 19 y: 3 } points { x: 19 y: 6 } type: kLinear }");
  }

  {
    // test oncoming agent
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(
            {{32.0, 2.0}, {15.0, 2.0}, {12.0, 2.0}}),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10,
            /*dt=*/1.0),
        /*heading_in=*/M_PI, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions()
                  .left_cartesian_repulsions.front()
                  .strength,
              path::BoundaryStrength::kModerate);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { x: 22.571428571428569 y: 6 } points { x: "
              "22.571428571428569 y: 2.9999999999999996 } points { x: "
              "22.571428571428569 y: 0.99999999999999956 } points { x: 34 y: "
              "0.99999999999999956 } points { x: 34 y: 2.9999999999999996 } "
              "points { x: 34 y: 6 } type: kLinear }");
  }
  {
    // test large polygon
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{0.0, 1.0}, {100.0, 1.0}, {100.0, 3.0}, {0.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(
            {{50.0, 2.0}, {53.0, 2.0}, {70.0, 2.0}}),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateConstantSpeedProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*n_sample=*/10,
            /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    // TODO(Harry): here we extend the reference path to make the following
    // WrapAround work, in future, we should enhance the WrapAround related
    // logic in order to handle agents longer than the reference path
    std::string reference_path_input =
        "LINESTRING(0.0 0.0,20.0 0,50.0 0,100.0 0.0,300.0 0.0)";
    math::geometry::PolylineCurve2d extend_reference_path =
        test::ReadLine(reference_path_input);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, extend_reference_path,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions()
                  .left_cartesian_repulsions.front()
                  .strength,
              path::BoundaryStrength::kModerate);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { y: 1 } points { x: 118 y: 1 } points { x: "
              "118 y: 3 } points { x: 118 y: 6 } type: kLinear }");
  }
  {
    // test agent with empty path
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{10.0, 1.0}, {14.0, 1.0}, {14.0, 3.0}, {10.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions()
                  .left_cartesian_repulsions.front()
                  .strength,
              path::BoundaryStrength::kModerate);
    math::pb::Curve2d proto_curve;
    constraint_manager.repulsions()
        .left_cartesian_repulsions.front()
        .cartesian_boundary->ToProto(&proto_curve);
    // just wrap the polygon it self
    EXPECT_EQ(proto_curve.ShortDebugString(),
              "segments { points { x: 10 y: 6 } points { x: 10 y: 3 } points { "
              "x: 10 y: 1 } points { x: 14 y: 1 } points { x: 14 y: 3 } points "
              "{ x: 14 y: 6 } type: kLinear }");
  }
  {
    // test agent with empty path but far away
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kRight,
        /*polygon_in=*/
        math::geometry::Polygon2d(
            {{50.0, 1.0}, {54.0, 1.0}, {54.0, 3.0}, {50.0, 3.0}}),
        /*polygon_center_path_in=*/
        math::geometry::PolylineCurve2d(),
        /*polygon_center_profile_lower_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*polygon_center_profile_upper_bound_in=*/
        speed::CreateStaticProfileFromTime(
            /*t0=*/0.0, /*x0=*/0.0, /*n_sample=*/10, /*dt=*/1.0),
        /*heading_in=*/0.0, /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt,
        /*path_risk_mitigation_mode_in=*/false);
    auto* object_repulsions_generating_infos =
        constraint_manager.mutable_object_repulsions_generating_infos();
    object_repulsions_generating_infos->emplace(
        std::make_pair(dummy_object_id_, repulsion_meta_data.reasoner_id),
        SingleObjectRepulsionsGeneratingInfo());
    RepulsionMetaManager::AddRepulsionsFromPolygonAndTrajectory(
        repulsion_meta_data, math::pb::kRight, reference_path_,
        selected_speed_profile_st_curve_, kEgoRaToFrontAppro, kEgoRaToRearAppro,
        plan_init_timestamp_, dummy_object_id_, constraint_manager);
    // Expect no repulsions
    EXPECT_GE(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              0);
  }
}

TEST_F(RepulsionMetaManagerTest, AddRepulsionsFromPolylineTest) {
  {
    path::ConstraintManager constraint_manager(path_curve_);
    RepulsionMetaData repulsion_meta_data(
        /*direction_in=*/RepulsionMetaData::RepulsionDirection::kLeft,
        /*polyline_in=*/
        math::geometry::PolylineCurve2d({{1.0, 1.0}, {3.0, 2.0}}),
        /*add_only_overtaking_in=*/false,
        /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false,
        /*strength_in=*/path::BoundaryStrength::kModerate,
        /*strength_ration_in=*/std::nullopt,
        /*repulsion_required_lat_gap_in=*/0.0,
        /*reasoner_id_in=*/pb::AgentReasonerId::LEAD_AND_MERGE_AGENT,
        /*object_id_in=*/std::nullopt, /*trajectory_id_in=*/std::nullopt);
    RepulsionMetaManager::AddRepulsionsFromPolyline(
        repulsion_meta_data, math::pb::kLeft, constraint_manager);
    EXPECT_EQ(constraint_manager.repulsions().right_cartesian_repulsions.size(),
              1);
    EXPECT_EQ(constraint_manager.repulsions().left_cartesian_repulsions.size(),
              0);
    const auto& repulsion =
        constraint_manager.repulsions().right_cartesian_repulsions.front();
    math::pb::Curve2d proto_curve;
    repulsion.cartesian_boundary->ToProto(&proto_curve);
    EXPECT_EQ(
        proto_curve.ShortDebugString(),
        "segments { points { x: 1 y: 1 } points { x: 3 y: 2 } type: kLinear }");
  }
}

TEST_F(TestGenerateAgentConstraint,
       AddNominalNudgeConstraintOnPredictionDynamicAgentCase) {
  AgentInLaneStates dynamic_agent_state;
  dynamic_agent_state.object_id = 1;
  StampedAgentSnapshotInLaneState state_1, state_2;
  state_1.timestamp = 0;
  state_1.inlane_state.inlane_param.along_track_speed_mps = 1.0;
  state_2.timestamp = 2000;
  state_2.inlane_state.inlane_param.along_track_speed_mps = 2.0;
  AgentTrajectoryInLaneStates trajectory_inlane_states;
  trajectory_inlane_states.trajectory_id = 0;
  trajectory_inlane_states.predicted_states.push_back(state_1);
  trajectory_inlane_states.predicted_states.push_back(state_2);
  dynamic_agent_state.predicted_trajectories.insert(
      {trajectory_inlane_states.trajectory_id, trajectory_inlane_states});
  dynamic_agent_state.primary_trajectory_id =
      trajectory_inlane_states.trajectory_id;

  AddMultipleNominalNudgeConstraintOnPrediction(
      dynamic_agent_state, dynamic_agent_meta_.lateral_decision,
      dynamic_agent_meta_.nudge_index_ranges,
      dynamic_agent_meta_.required_lat_gap, dynamic_agent_meta_);

  ASSERT_EQ(dynamic_agent_meta_.agent_constraints.size(), 1);
  const auto agent_constraint = dynamic_agent_meta_.agent_constraints[0];
  ASSERT_EQ(agent_constraint.snapshots_groups.size(), 1);
  ASSERT_EQ(agent_constraint.snapshots_groups[0].size(), 2);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][0].timestamp, 0);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][0].along_track_speed_mps, 1.0);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][1].timestamp, 2000);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][1].along_track_speed_mps, 2.0);
}

TEST_F(
    TestGenerateAgentConstraint,
    AddNominalNudgeConstraintOnPredictionDynamicAgentForObjectOccupancyState) {
  std::vector<PredictedTrajectoryWrapper> predictions = {
      *predicted_trajectory_wrapper_};
  ObjectOccupancyState dynamic_object_state = GenerateObjectOccupancyState(
      *dynamic_planner_object_, /*object_information=*/{}, predictions,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);

  AddMultipleNominalNudgeConstraintOnPrediction(
      dynamic_object_state, dynamic_agent_meta_.lateral_decision,
      dynamic_agent_meta_.nudge_index_ranges,
      dynamic_agent_meta_.required_lat_gap, dynamic_agent_meta_);

  ASSERT_EQ(dynamic_agent_meta_.agent_constraints.size(), 1);
  const auto& agent_constraint = dynamic_agent_meta_.agent_constraints[0];
  ASSERT_EQ(agent_constraint.snapshots_groups.size(), 1);
  ASSERT_EQ(agent_constraint.snapshots_groups[0].size(), 2);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][0].timestamp, 0);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][0].along_track_speed_mps, 1.0);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][1].timestamp, 20);
  EXPECT_EQ(agent_constraint.snapshots_groups[0][1].along_track_speed_mps, 1.0);
}

}  // namespace planner
