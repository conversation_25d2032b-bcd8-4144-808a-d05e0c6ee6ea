#include "planner/ops_warning/ops_warning.h"

#include <algorithm>
#include <string>
#include <unordered_map>

#include "av_comm/mode_config.h"
#include "geometry/model/oriented_box.h"
#include "latency/pipeline_id.h"
#include "planner/ops_warning/ops_warning_utility.h"
#include "planner/planning_gflags.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace ops_warning {

namespace {

// Returns whether the trajectory has extreme steering rate.
bool HasExtremeSteeringRate(const pb::OpsWarningConfig& config,
                            const pb::Trajectory& trajectory,
                            double steering_gear_ratio) {
  for (const auto& pose : trajectory.poses()) {
    if (std::abs(pose.steering_rate()) * steering_gear_ratio >
        config.engage_steering_rate_limit_radps()) {
      return true;
    }
  }
  return false;
}

// Returns the engage speed limit.
double GetEngageSpeedLimit(const pb::OpsWarningConfig& config,
                           const std::string& route_name) {
  // If planning_enable_driving_on_freeway is true, enable engage when ego speed
  // under freeway_engage_speed_limit_mps(80kph).
  if (FLAGS_planning_enable_driving_on_freeway) {
    return config.freeway_engage_speed_limit_mps();
  }

  auto it = std::find_if(config.engage_route_speed_limit_configs().begin(),
                         config.engage_route_speed_limit_configs().end(),
                         [&route_name](const auto& config_item) {
                           return route_name == config_item.route_name();
                         });
  if (it != config.engage_route_speed_limit_configs().end()) {
    return it->speed_limit_mps();
  }

  return config.default_engage_speed_limit_mps();
}

}  // namespace

void OpsWarningGenerator::PublishPlanningFatal(const RobotState& robot_state) {
  fault_reporter_.ToggleFault(
      robot_state.IsInAutonomousMode(),
      ::pb::PlanningFaultCode::PLANNING_DECOUPLED_TRAJECTORY_FAILED,
      "No decoupled trajectory generated");
}

pb::OpsWarning OpsWarningGenerator::GenerateWarning(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const TrajectoryGenerationResult& selected_trajectory_result) {
  TRACE_EVENT_SCOPE(
      planner, OpsWarning_GenerateWarning,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());

  const auto trip_comment_ptr = world_model.trip_comment();
  trip_category_ =
      trip_comment_ptr ? trip_comment_ptr->category() : trip_category_;

  if (!selected_trajectory_result.is_generated) {
    return {};
  }

  ClearPlanningFatal();

  pb::OpsWarningMode state = pb::OpsWarningMode::NORMAL;
  if (world_model.robot_state().IsInAutonomousMode()) {
    // If ego has a collisions with detected objects, there exist a potential
    // collision.
    if (HasPotentialCollisionWithPlannerObjects(
            world_model.robot_state(), world_model.planner_object_map(),
            ops_warning_config_.potential_collision_distance_threshold_m())) {
      state = pb::OpsWarningMode::POTENTIAL_COLLISION;
    }

    // If AV is in auto mode, check whether we need to issue a hard brake /
    // extreme juke / disengagement zone stop.
    if (selected_trajectory_result.evaluation_result.has_extreme_decel) {
      state = pb::OpsWarningMode::POTENTIAL_HARD_BRAKE;
    }

    if (selected_trajectory_result.evaluation_result.has_extreme_juke) {
      state = pb::OpsWarningMode::EXTREME_JUKE;
    }

    ClearAllFaults();
  } else {
    // If AV is in manual mode, check whether we need to issue an engagement
    // warning. If engage maneuver is not enabled(e.g. The ego vehicle is not
    // engagable in data collection trip), do not issue engagement warnings to
    // avoid misleading system error.
    const bool is_ego_engagable =
        !(trip_category_ == voy::TripComment_Category_kGhawar);
    // Engage maneuver is enabled onboard if ego is engagable. In simulation,
    // engage maneuver is disabled default. Users can use
    // FLAGS_planning_enable_engage_maneuver_sim to enable engage maneuver in
    // simulation.
    bool should_enable_engage_warning_from_DHMI =
        is_ego_engagable && (!av_comm::InSimulation() ||
                             (av_comm::InSimulation() &&
                              FLAGS_planning_enable_engage_maneuver_sim));
    if (should_enable_engage_warning_from_DHMI &&
        should_enable_engage_warning_) {
      const auto& current_snapshot =
          world_model.robot_state().current_state_snapshot();
      ValidateEngagementCondition(
          current_snapshot.bounding_box(),
          world_model.global_route_solution()->GetRouteName(),
          behavior_decision, selected_trajectory_result,
          current_snapshot.speed(),
          world_model.robot_state()
              .car_model_with_shape()
              .param()
              .measurement()
              .steering_gear_ratio());
    }
  }

  // If all other states are normal, further checks for potential rear ending.
  if (state == pb::OpsWarningMode::NORMAL) {
    if (CheckRearEnding(world_model.pose(), world_model.planner_object_map(),
                        *selected_trajectory_result.trajectory)) {
      state = pb::OpsWarningMode::POTENTIAL_REAR_ENDING;
    }
  }

  // Set output.
  pb::OpsWarning ops_warning;
  ops_warning.set_timestamp(world_model.pose().timestamp());
  ops_warning.set_warning_mode(state);
  return ops_warning;
}

void OpsWarningGenerator::ClearAllEngagementFault() {
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_DECEL);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_LAT_ACCEL);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_JUKE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::ENGAGE_IN_DISENGAGEMENT_ZONE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_OFF_LANE_CENTER_LINE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_NEAR_ROAD_BOUNDARY);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_WITH_HIGH_SPEED);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_STEERING_RATE);
}

void OpsWarningGenerator::ValidateEngagementCondition(
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const std::string& route_name, const BehaviorDecision& behavior_decision,
    const TrajectoryGenerationResult& selected_trajectory_result,
    double ego_current_speed, double steering_gear_ratio) {
  const auto& trajectory_evaluation_result =
      selected_trajectory_result.evaluation_result;
  fault_reporter_.ToggleFault(trajectory_evaluation_result.has_extreme_decel,
                              ::pb::PlanningFaultCode::EXTREME_DECEL, "");

  fault_reporter_.ToggleFault(
      trajectory_evaluation_result.has_extreme_lat_accel,
      ::pb::PlanningFaultCode::EXTREME_LAT_ACCEL, "");

  fault_reporter_.ToggleFault(trajectory_evaluation_result.has_extreme_juke,
                              ::pb::PlanningFaultCode::EXTREME_JUKE, "");

  const bool is_ego_moving =
      ego_current_speed >
      ops_warning_config_.dynamic_engage_speed_threshold_mps();

  fault_reporter_.ToggleFault(
      is_ego_moving &&
          HasExtremeSteeringRate(ops_warning_config_,
                                 *selected_trajectory_result.trajectory,
                                 steering_gear_ratio),
      ::pb::PlanningFaultCode::EXTREME_STEERING_RATE, "");
  // TODO(hongda): Re-enable this feature in decoupled maneuver.
  // fault_reporter_.ToggleFault(
  //     is_ego_moving && behavior_decision.lane_driving_directives
  //                          ->target_stationary_steering_rad.has_value(),
  //     ::pb::PlanningFaultCode::UNSAFE_ENGAGE_OFF_LANE_CENTER_LINE, "");

  if (behavior_decision.lane_geometry_directive != nullptr) {
    fault_reporter_.ToggleFault(
        IsNearRoadBoundary(*(behavior_decision.lane_geometry_directive),
                           ego_bounding_box),
        ::pb::PlanningFaultCode::UNSAFE_ENGAGE_NEAR_ROAD_BOUNDARY, "");
  }

  fault_reporter_.ToggleFault(
      ego_current_speed > GetEngageSpeedLimit(ops_warning_config_, route_name),
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_WITH_HIGH_SPEED, "");
}

void OpsWarningGenerator::ClearAllFaults() {
  ClearPlanningFatal();
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_DECEL);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_LAT_ACCEL);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_JUKE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::ENGAGE_IN_DISENGAGEMENT_ZONE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_OFF_LANE_CENTER_LINE);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_NEAR_ROAD_BOUNDARY);
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::UNSAFE_ENGAGE_WITH_HIGH_SPEED);
  fault_reporter_.RemoveFault(::pb::PlanningFaultCode::EXTREME_STEERING_RATE);
}

void OpsWarningGenerator::ClearPlanningFatal() {
  fault_reporter_.RemoveFault(
      ::pb::PlanningFaultCode::PLANNING_DECOUPLED_TRAJECTORY_FAILED);
}

bool OpsWarningGenerator::HasPotentialCollisionWithPlannerObjects(
    const RobotState& robot_state,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    double threshold) const {
  const int64_t timestamp = robot_state.current_state_snapshot().timestamp();
  const math::geometry::OrientedBox2d& bounding_box =
      robot_state.current_state_snapshot().bounding_box();

  return std::any_of(
      planner_object_map.begin(), planner_object_map.end(),
      [timestamp, threshold,
       &bounding_box](const auto& current_planner_object_pair) {
        const auto& planner_object = current_planner_object_pair.second;
        if (planner_object
                .predicted_trajectories(/*use_filtered_trajectories=*/true)
                .empty()) {
          return false;
        }
        const std::optional<math::geometry::OrientedBox2d> interpolated_box =
            planner_object.primary_trajectory().GetInterpolatedBoundingBox(
                timestamp, /*use_legacy_logic=*/true);
        return interpolated_box &&
               math::geometry::Distance(bounding_box, *interpolated_box) <=
                   threshold;
      });
}

}  // namespace ops_warning

}  // namespace planner
