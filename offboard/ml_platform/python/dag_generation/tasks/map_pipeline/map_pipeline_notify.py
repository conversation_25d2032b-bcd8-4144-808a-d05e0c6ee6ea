"""map pipeline notify task."""


from airflow.operators.python import Python<PERSON>perator
from airflow.utils.trigger_rule import <PERSON><PERSON><PERSON><PERSON>
from airflow.utils import state as airflow_state

from dag_generation.constants import TaskType
from dag_generation.tasks.abstract_task import AbstractTask
from dag_generation.tasks.task_factory import register
from dataloop.alert.utils import send_dchat_message
from orion_internal.utils import flower_utils


@register(TaskType.MAP_PIPELINE_NOTIFY)
class MapPipelineNotifyTask(AbstractTask):
    """map pipeline notify task."""

    def gen_airflow_ops(self, dag, *args, **kwargs):
        def _notify(**context):
            outputs = []
            all_success = True
            fail_msgs = []
            for ti in context["dag_run"].get_task_instances():
                if ti.task_id.endswith("checkpoint"):
                    stage_name = ti.task_id[: -1 * len("checkpoint") - 1]
                    output = context["ti"].xcom_pull(key="output", task_ids=ti.task_id)
                    if output:
                        outputs.append((stage_name, output))
                    error_info = context["ti"].xcom_pull(
                        key="error_info", task_ids=ti.task_id
                    )
                    if error_info:
                        msgs = "\n".join(error_info.values())
                        fail_msgs.append((stage_name, msgs))

                    if (
                        ti.current_state() == airflow_state.State.FAILED
                        and ti.task_id != context["task_instance"].task_id
                    ):
                        all_success = False
                        break

            title = "Map Auto Pipeline"

            flower_url = flower_utils.get_workflow_url_by_context(context)
            state = "Success" if all_success else "Failed"
            msg = f"Flower: [Link]({flower_url})\n"
            msg += f"State: {state}\n"
            msg += "---\n"

            for out in outputs:
                stage, values = out
                msg += f"**Stage:{stage}**\n"
                for key, val in values.items():
                    if val.startswith("http") or val.startswith("https"):
                        msg += f"{key}: [Link]({val})\n"
                    else:
                        msg += f"{key}: {val}\n"

            for fail_info in fail_msgs:
                task_id, fail_msg = fail_info
                msg += f"**{task_id} Fail:**\n"
                msg += f"{fail_msg}\n"

            username = context["dag_run"].conf.get("user_name")
            send_dchat_message(
                receivers=username, url="", title=title, content=msg, markdown_flag=True
            )

        return PythonOperator(
            task_id="notify",
            dag=dag,
            python_callable=_notify,
            provide_context=True,
            trigger_rule=TriggerRule.ALL_DONE,
        )
