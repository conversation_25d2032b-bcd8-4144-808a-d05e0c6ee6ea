#include "planner/speed/solver/profile_solver.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <iterator>
#include <limits>
#include <string>
#include <utility>
#include <vector>

#include <tbb/parallel_for.h>

#include "latency/latency_stat.h"
#include "math/math_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/discomforts/discomfort_varying_min_range.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/reasoning_input/traffic_rules/speed_limit_traffic_rule.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/solver/optimizer/speed_optimizer.h"
#include "planner/speed/solver/searcher/profile_searcher.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/speed/timemap/time_mapper.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner_protos/speed_decision.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_solver_debug.pb.h"
#include "rt_event/rt_event.h"
#include "strings/stringprintf.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {

namespace {
// Populates the selected discomfort's longitudinal kinematic limits.
void PopulateSelectedDiscomfortLimits(
    const DiscomfortVaryingLimits& discomfort_limits,
    int selected_discomfort_ix,
    pb::LongitudinalKinematicLimitsForDiscomfortLevel*
        longitudinal_limits_debug) {
  DCHECK(longitudinal_limits_debug != nullptr);

  const Limits& limits =
      discomfort_limits.LimitsAtDiscomfortIx(selected_discomfort_ix);

  longitudinal_limits_debug->set_discomfort_level_ix(selected_discomfort_ix);
#define ASSIGN_LIMIT_TO_DEBUG(limit_term)                     \
  longitudinal_limits_debug->mutable_##limit_term()->set_min( \
      limits.limit_term.min);                                 \
  longitudinal_limits_debug->mutable_##limit_term()->set_max( \
      limits.limit_term.max);

  ASSIGN_LIMIT_TO_DEBUG(brake_a);
  ASSIGN_LIMIT_TO_DEBUG(accel_a);
  ASSIGN_LIMIT_TO_DEBUG(brake_j);
  ASSIGN_LIMIT_TO_DEBUG(accel_j);
#undef ASSIGN_LIMIT_TO_DEBUG
}

// Converts the selected reference profile to proto and updates the debug info
// for reference profile if it's not nullptr.
void UpdateSelectedReference(
    const SpeedSearchResult& search_result,
    const Profile& select_reference_profile,
    planner::speed::pb::ReferenceProfile* reference_profile) {
  if (reference_profile == nullptr) {
    return;
  }
  reference_profile->set_discomfort_ix(search_result.selected_discomfort_ix());
  reference_profile->set_discomfort_value(search_result.selected_discomfort());
  *reference_profile->mutable_profile() = ToProto(select_reference_profile);
}

// Update speed limiters and speed bounds for reference profile.
void UpdateSpeedLimitersAndReferenceSpeedDebug(
    const ReferenceGenerator& reference_generator,
    const SpeedSearchResult& search_result,
    planner::speed::pb::SpeedSearchDebug& debug) {
  reference_generator.UpdateSpeedLimitersProto(
      search_result.selected_discomfort(), *debug.mutable_speed_limiters());
  debug.mutable_reference_profile_limits()->set_discomfort_value(
      search_result.selected_discomfort());
  const auto& ref_speed_bounds =
      reference_generator.GetSpeedBoundsAtDiscomfortIx(
          search_result.selected_discomfort_ix());
  const auto& mutable_speed_limits =
      debug.mutable_reference_profile_limits()->mutable_speed_limits();
  mutable_speed_limits->Reserve(ref_speed_bounds.size());
  for (const auto& ref_speed_bound : ref_speed_bounds) {
    mutable_speed_limits->Add(ref_speed_bound);
  }
  debug.mutable_reference_profile_limits()->set_ego_ra_ix(
      reference_generator.reference_profile().init_state_ra_ix());
}

// Converts the search profile to proto and updates the debug info for search.
void UpdateSearchDebug(const SpeedSearchResult& search_result,
                       const ConstraintManager& constraint_manager,
                       const Profile& selected_reference_profile,
                       const ReferenceGenerator& reference_generator,
                       const DiscomfortVaryingLimits& discomfort_limits,
                       const DiscomfortVaryingMinRange& min_range,
                       planner::speed::pb::SpeedSearchDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  *debug->mutable_search_profile() = ToProto(search_result.profile());
  debug->set_search_result(search_result.searcher_result());
  debug->set_conflict_resolving_type(search_result.conflict_resolving_type());
  debug->set_diversity_option(search_result.diversity_option());
  const ConstraintResult& constraint_result =
      search_result.finalized_constraint_result();
  UpdateSelectedReference(search_result, selected_reference_profile,
                          debug->mutable_selected_reference());

  debug->mutable_decisions()->Reserve(constraint_result.decisions.size());
  for (const auto& decision : constraint_result.decisions) {
    debug->add_decisions(decision);
  }

  debug->mutable_constraints()->Reserve(constraint_result.constraints.size());
  for (const auto& constraint : constraint_result.constraints) {
    // NOTE(Tingran): Only populate finalized constraint attributes.
    constraint.ToFinalizedConstraintAttributes(debug->add_constraints());
  }

  UpdateSpeedLimitersAndReferenceSpeedDebug(reference_generator, search_result,
                                            *debug);
  const int dominant_constraint_ix = constraint_result.dominant_constraint_ix;
  debug->set_dominant_constraint_ix(dominant_constraint_ix);
  if (constraint_result.dominant_constraint_ix >= 0) {
    debug->set_dominant_constraint_obj_id(
        constraint_result.constraints.at(dominant_constraint_ix).obj_id);
    debug->set_earliest_brake_time_ix(search_result.earliest_brake_time_ix());
  } else {
    debug->set_dominant_constraint_obj_id(-1);
    debug->set_earliest_brake_time_ix(-1);
  }
  // Populates the selected discomfort's longitudinal kinematic limits.
  PopulateSelectedDiscomfortLimits(
      discomfort_limits, search_result.selected_discomfort_ix(),
      debug->mutable_selected_longitudinal_kinematic_limits());
  // Populates the min range at selected discomfort.
  min_range.PopulateMinRangeDebug(search_result.selected_discomfort(),
                                  debug->mutable_min_range());
  *debug->mutable_global_speed_solver_settings() =
      constraint_manager.global_speed_solver_settings().ToProto();
}

void UpdatePlanInitTimeInSecDebug(double plan_init_time_in_sec,
                                  planner::speed::pb::SpeedSolverDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  debug->set_plan_init_time_in_sec(plan_init_time_in_sec);
}

void UpdateRearAxisShiftDebug(double ra_to_fb_shift, double ra_to_rb_shift,
                              planner::speed::pb::SpeedSolverDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  debug->set_ra_to_fb_shift(ra_to_fb_shift);
  debug->set_ra_to_rb_shift(ra_to_rb_shift);
}

// Adds speed yielding fence info to speed solver debug.
void UpdateSpeedYieldingFencesDebug(const ConstraintResult& constraint_result,
                                    pb::SpeedSolverDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  *debug->mutable_speed_yielding_fences() =
      constraint_result.yielding_fence_list;
}

void ProfileTimeAndSizeCheck(const Profile& profile,
                             const double init_state_time, const size_t steps) {
  DCHECK(math::IsApprox(profile.front().t, init_state_time));
  DCHECK_EQ(profile.size(), steps);
}

}  // namespace

ProfileSolver::ProfileSolver(
    const pb::SpeedGeneratorConfig& config, const double ra_to_fb_shift,
    const double ra_to_rb_shift, const double plan_init_state_time_in_sec,
    const std::array<double, Discomforts::kLevels>& immutable_times)
    : num_steps_(config.for_search().n_horizon()),
      dt_(config.for_search().dt()),
      ra_to_fb_shift_(ra_to_fb_shift),
      ra_to_rb_shift_(ra_to_rb_shift),
      plan_init_state_time_in_sec_(plan_init_state_time_in_sec),
      immutable_times_(immutable_times),
      config_(config) {
#pragma GCC diagnostic push
  // max_required_lateral_gap is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  DCHECK(math::IsApprox(config_.for_search().min_range_max_required_lat_gap(),
                        config_.for_optimizer()
                            .potentials_params()
                            .barrier_params()
                            .max_required_lateral_gap()));
#pragma GCC diagnostic pop
}

bool ProfileSolver::HasNonAllowedConflicts(
    const Profile& profile, const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const DiscomfortVaryingMinRange& min_range,
    const double min_range_required_lat_gap,
    const double selected_discomfort_ix, std::string* debug_str) {
  TimeMapper time_mapper(
      immutable_times_.at(static_cast<int>(selected_discomfort_ix)), num_steps_,
      dt_);
  DCHECK_EQ(constraints.size(), decisions.size());
  // Check if speed profile satisfies all HARD constraints regarding min-ranges.
  for (size_t ix = 0; ix < constraints.size(); ++ix) {
    const Constraint& constraint = constraints[ix];
    const ConstraintSettings& settings = constraint.settings;
    const Decision& decision = decisions[ix];
    if (decision == pb::IGNORE) {
      continue;
    }
    if (constraint.type == ConstraintType::Constraint_Type_NO_BLOCK) {
      // Skip no-block constraint as it is inherently SOFT.
      continue;
    }

    DCHECK(decision != pb::NOT_DECIDED);
    const bool pass = (decision == pb::PASS);
    if (pass && settings.pass_option !=
                    PassYieldOption::ConstraintSettings_PassYieldOption_HARD) {
      continue;
    }
    if (!pass && settings.yield_option !=
                     PassYieldOption::ConstraintSettings_PassYieldOption_HARD) {
      continue;
    }
    if (pass && settings.pass_always_possible) {
      continue;
    }
    // Check whether there is non-allowed conflicts for min-range.
    const double discomfort = constraint.selected_discomfort;
    double safety_buffer = min_range.at(settings, discomfort, pass);
    // Relax the safety buffer with reasoning explicitly set negative buffer.
    const double extra_dist = pass ? settings.pass_extra_distance(discomfort)
                                   : settings.yield_extra_distance(discomfort);
    if (extra_dist < 0.0) {
      safety_buffer += extra_dist;
    }
    const double min_req_lat_gap = settings.allow_ignoring_min_range_lat_gap
                                       ? 0.0
                                       : min_range_required_lat_gap;
    for (const auto& state : constraint.states) {
      if (state.abs_lat_gap > min_req_lat_gap) {
        continue;
      }
      int time_ix = -1;
      if (!time_mapper.MaybeGetTimeIx(pass ? state.start_time : state.end_time,
                                      pass, &time_ix)) {
        continue;
      }
      DCHECK_LE(0, time_ix);
      DCHECK_LT(time_ix, num_steps_);
      double pass_yield_x = 0;
      pass_yield_x = pass ? (state.end_x(discomfort) + safety_buffer)
                          : (state.start_x(discomfort) - safety_buffer);
      if (pass && profile[time_ix].x <= pass_yield_x) {
        if (debug_str != nullptr) {
          *debug_str = absl::StrFormat(
              "Has non-allowed PASS overlap with %s, time_ix %d, profile state "
              "%s, constraint %.3lf",
              constraint.unique_constraint_id, time_ix,
              profile[time_ix].DebugString(), pass_yield_x);
        }
        return true;
      }
      if (!pass && profile[time_ix].x > pass_yield_x) {
        if (debug_str != nullptr) {
          *debug_str = absl::StrFormat(
              "Has non-allowed YIELD overlap with %s, time_ix %d, profile "
              "state %s, constraint %.3lf, "
              "selected discomfort ix %.3lf, constraint discomfort ix %d",
              constraint.unique_constraint_id, time_ix,
              profile[time_ix].DebugString(), pass_yield_x,
              selected_discomfort_ix,
              Discomforts::GetDiscomfortIx(constraint.selected_discomfort));
        }
        return true;
      }
    }
  }
  return false;
}

SpeedResult ProfileSolver::InverseSolve(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const Profile& speed_profile, const ConflictResolverInput& scr_input,
    ConstraintManager&& initial_constraint_manager,
    ReferenceGenerator* reference_generator,
    AgentReactionCalculator* agent_reaction_calculator,
    pb::SpeedSolverDebug* speed_solver_debug) {
  DCHECK(reference_generator != nullptr);
  std::string debug_str;
  DCHECK(reference_generator->UpdatedAndReferenceProfilesAreSet(&debug_str))
      << debug_str;
  DCHECK(agent_reaction_calculator != nullptr);
  // Construct and invoke the speed profile_searcher to do reverse search
  // computation: aka, given the final speed profile, obtain the decisions and
  // other metadata.
  // TODO(tingran): LC is not considered now.
  ProfileSearcher profile_searcher(
      previous_iter_seed.speed_seed().speed_solver_result(),
      config_.for_search(), scr_input, ra_to_fb_shift_, ra_to_rb_shift_,
      plan_init_state_time_in_sec_, immutable_times_,
      /*should_populate_gap_align_guide_seed=*/false,
      /*gap_align_for_lc=*/false);
  DiscomfortVaryingLimits all_limits(config_.for_car_type().limits());
  all_limits.ValidateLimits();
  DiscomfortVaryingMinRange min_range(config_.for_car_type().min_range());

  // Speed searcher debug info for the current ML speed profile.
  speed::pb::SpeedSearchDebug speed_search_debug;
  auto* speed_search_debug_ptr =
      (speed_solver_debug == nullptr) ? nullptr : &speed_search_debug;

  SpeedSearchResult speed_search_result = profile_searcher.InverseSearch(
      speed_profile, all_limits, min_range, initial_constraint_manager,
      reference_generator, agent_reaction_calculator, speed_search_debug_ptr);
  SpeedPipelineMeta speed_pipeline_meta;
  SpeedResult speed_result = SpeedResult(std::move(speed_search_result),
                                         std::move(speed_pipeline_meta));
  // Should be the same as speed_profile.
  const Profile& search_profile = speed_result.searcher_profile();
  const int selected_discomfort_ix = speed_result.selected_discomfort_ix();
  // When the given speed profile has conflicts with constraints, we assign
  // the result as conflict resolving. Thus we treating the profile as if no
  // opt is run.
  Profile complete_search_profile = reference_generator->GetCompleteProfile(
      selected_discomfort_ix, search_profile);
  if (speed_solver_debug != nullptr) {
    const int selected_discomfort_ix = speed_result.selected_discomfort_ix();
    const Profile& selected_reference_profile =
        reference_generator->GetOrGenerateDiscomfortReferenceProfile(
            selected_discomfort_ix, num_steps_, dt_);
    UpdateSearchDebug(speed_result.search_result(), initial_constraint_manager,
                      selected_reference_profile, *reference_generator,
                      all_limits, min_range, speed_search_debug_ptr);
    UpdatePlanInitTimeInSecDebug(plan_init_state_time_in_sec_,
                                 speed_solver_debug);
    UpdateRearAxisShiftDebug(ra_to_fb_shift_, ra_to_rb_shift_,
                             speed_solver_debug);
    UpdateSpeedYieldingFencesDebug(speed_result.constraint_result(),
                                   speed_solver_debug);
    *speed_solver_debug->mutable_search_debug() = std::move(speed_search_debug);
  }

  const pb::SearchResult::Enum searcher_result = speed_result.searcher_result();
  if (searcher_result ==
      ProfileSearcher::SearchResult::SearchResult_Enum_CONFLICT_RESOLVING) {
    speed_result.Finalize(ProfileSolver::Result::SolverResult_Enum_kNoOpt,
                          std::move(complete_search_profile));
  } else {
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        searcher_result ==
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS ||
        searcher_result == ProfileSearcher::SearchResult::
                               SearchResult_Enum_SUCCESS_WITH_GAP_ALIGN);
    // We do not run speed opt on given speed profile.
    speed_result.Finalize(ProfileSolver::Result::SolverResult_Enum_kSuccess,
                          std::move(complete_search_profile));
  }
  if (speed_solver_debug != nullptr) {
    speed_solver_debug->set_solver_result(speed_result.solver_result());
    *speed_solver_debug->mutable_final_profile() =
        ToProto(speed_result.final_profile());
  }
  return speed_result;
}

std::vector<SpeedResult> ProfileSolver::Solve(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const bool should_populate_gap_align_guide_seed,
    const bool gap_align_for_lc, const Profile* warm_start_profile,
    const ConflictResolverInput& scr_input,
    ConstraintManager&& initial_constraint_manager,
    ReferenceGenerator* reference_generator,
    AgentReactionCalculator* agent_reaction_calculator,
    RiskDiversityEvaluator* risk_diversity_evaluator,
    google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverDebug>* debugs) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_ProfileSolver_Solve);
  VOY_LATENCY_STAT_RECORD_PLANNER_S2(
      LAT_STAT_DecoupledForwardManeuver_ProfileSolver_Solve);
  DCHECK(reference_generator != nullptr);
  std::string debug_str;
  DCHECK(reference_generator->UpdatedAndReferenceProfilesAreSet(&debug_str))
      << debug_str;
  DCHECK(agent_reaction_calculator != nullptr);

  // Construct and invoke the speed profile_searcher.
  ProfileSearcher profile_searcher(
      previous_iter_seed.speed_seed().speed_solver_result(),
      config_.for_search(), scr_input, ra_to_fb_shift_, ra_to_rb_shift_,
      plan_init_state_time_in_sec_, immutable_times_,
      should_populate_gap_align_guide_seed, gap_align_for_lc);
  DiscomfortVaryingLimits all_limits(config_.for_car_type().limits());
  all_limits.ValidateLimits();
  DiscomfortVaryingMinRange min_range(config_.for_car_type().min_range());

  // Speed searcher debug info for each speed diversity.
  std::vector<speed::pb::SpeedSearchDebug> speed_search_debugs;
  // Reserve a large number to avoid unnecessary potential reallocation.
  speed_search_debugs.reserve(4);
  auto* speed_search_debugs_ptr =
      (debugs == nullptr) ? nullptr : &speed_search_debugs;
  // Do speed search with stay stop check.
  const std::optional<StayStopCheckInput> stay_stop_check_input =
      std::make_optional(previous_iter_seed.continuous_low_speed_cycle_count());
  std::vector<SpeedSearchResult> search_results = profile_searcher.Search(
      all_limits, min_range,
      /*max_discomfort_for_search=*/Discomforts::kMax,
      initial_constraint_manager, reference_generator,
      agent_reaction_calculator, risk_diversity_evaluator,
      speed_search_debugs_ptr, stay_stop_check_input);
  // Post process and return speed results.
  return GetPostProcessedSpeedResults(
      previous_iter_seed, all_limits, min_range, warm_start_profile,
      std::move(initial_constraint_manager), std::move(search_results),
      std::move(speed_search_debugs), reference_generator, debugs);
}

std::vector<SpeedResult> ProfileSolver::GetPostProcessedSpeedResults(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const DiscomfortVaryingLimits& all_limits,
    const DiscomfortVaryingMinRange& min_range,
    const Profile* warm_start_profile, ConstraintManager&& constraint_manager,
    std::vector<SpeedSearchResult>&& search_results,
    std::vector<speed::pb::SpeedSearchDebug>&& speed_search_debugs,
    ReferenceGenerator* reference_generator,
    google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverDebug>* debugs) {
  TRACE_EVENT_SCOPE(
      planner,
      DecoupledForwardManeuver_ProfileSolver_GetPostProcessedSpeedResults);
  DCHECK(reference_generator != nullptr);
  // Construct the initial solver result and debug container.
  std::vector<SpeedResult> solver_results;
  solver_results.reserve(search_results.size());
  for (size_t idx = 0; idx < search_results.size(); ++idx) {
    SpeedPipelineMeta speed_pipeline_meta;
    solver_results.emplace_back(std::move(search_results[idx]),
                                std::move(speed_pipeline_meta));
    if (debugs != nullptr) {
      DCHECK_LT(idx, speed_search_debugs.size());
      speed::pb::SpeedSolverDebug* single_debug = debugs->Add();
      single_debug->mutable_search_debug()->Swap(&speed_search_debugs[idx]);
    }
  }
  // Parallel running the post-searcher process to finalize the solver results.
  tbb::parallel_for(
      /*first=*/0,
      /*last=*/static_cast<int>(solver_results.size()),
      [this, &solver_results, &constraint_manager, &debugs, &previous_iter_seed,
       &reference_generator, &all_limits, &min_range,
       &warm_start_profile](int idx) {
        SpeedResult& mutable_solver_result = solver_results[idx];
        speed::pb::SpeedSolverDebug* debug =
            (debugs == nullptr) ? nullptr : &debugs->at(idx);
        PostSearchProcess(previous_iter_seed, *reference_generator,
                          constraint_manager, all_limits, min_range,
                          warm_start_profile, &mutable_solver_result, debug);
        if (debug != nullptr) {
          debug->set_solver_result(mutable_solver_result.solver_result());
          *debug->mutable_final_profile() =
              ToProto(mutable_solver_result.final_profile());
        }
      });
  return solver_results;
}

void ProfileSolver::PostSearchProcess(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const ReferenceGenerator& reference_generator,
    const ConstraintManager& constraint_manager,
    const DiscomfortVaryingLimits& all_limits,
    const DiscomfortVaryingMinRange& min_range,
    const Profile* warm_start_profile, SpeedResult* mutable_solver_result,
    speed::pb::SpeedSolverDebug* debug) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_PostSearchProcess);
  const pb::SearchResult::Enum search_result_status =
      mutable_solver_result->searcher_result();
  // The profile from this search result, used for fall-back when opt fails.
  const Profile& search_profile = mutable_solver_result->searcher_profile();
  // The constraint manager to be mutated in the following solver process.
  const int tree_switch_time_ix = mutable_solver_result->tree_switch_time_ix();
  const pb::TreeSearchType::Enum tree_search_type =
      mutable_solver_result->tree_search_type();
  speed::pb::SpeedSearchDebug* search_debug =
      (debug == nullptr) ? nullptr : debug->mutable_search_debug();
  // Searched profile should be consistent and monotonic.
  int state_ix = 0;
  DCHECK(ProfileIsConsistent(search_profile, &state_ix))
      << "state_ix: " << state_ix
      << "this state: " << search_profile[state_ix].DebugString()
      << " \n next state: " << search_profile[state_ix + 1].DebugString();
  DCHECK(IsProfileMonotonic(
      search_profile, config_.for_optimizer().allowed_negative_speed_error(),
      config_.for_optimizer().allowed_move_forward_x_error()));
  // Search should not fail
  CHECK_NE(
      search_result_status,
      ProfileSearcher::SearchResult::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT);
  // Populate debug.
  UpdatePlanInitTimeInSecDebug(plan_init_state_time_in_sec_, debug);
  UpdateRearAxisShiftDebug(ra_to_fb_shift_, ra_to_rb_shift_, debug);
  const int selected_discomfort_ix =
      mutable_solver_result->selected_discomfort_ix();
  const Profile& selected_reference_profile =
      reference_generator.GetDiscomfortReferenceProfile(selected_discomfort_ix,
                                                        num_steps_);
  // TODO(nihar): Maybe pass in yielding fences from debug directly
  // instead of having it as a member varibale in the constraint manager.
  const ConstraintResult& constraint_result =
      mutable_solver_result->constraint_result();
  UpdateSpeedYieldingFencesDebug(constraint_result, debug);
  UpdateSearchDebug(mutable_solver_result->search_result(), constraint_manager,
                    selected_reference_profile, reference_generator, all_limits,
                    min_range, search_debug);
  const Profile& ref_profile =
      reference_generator.GetDiscomfortReferenceProfile(selected_discomfort_ix,
                                                        num_steps_);
  const Profile& min_profile = reference_generator.GetDiscomfortMinProfile(
      selected_discomfort_ix, num_steps_);
  const Limits& evasive_limits = all_limits.EvasiveLimits();

  // TODO(lewisliu): investigate whether we should run speed optimizer if search
  // result is from conflict resolver.
  // Do not run the speed optimizer if search gets into conflict resolving.
  // Note: if search result is CONFLICT_RESOLVING, there should be no speed
  // diversity, i.e. that would be the only applicable solution by speed solver.
  if (search_result_status ==
      ProfileSearcher::SearchResult::SearchResult_Enum_CONFLICT_RESOLVING) {
    std::string debug_string;
    if (!ProfileIsAdmissible(evasive_limits, search_profile, 0,
                             &debug_string)) {
      LOG(ERROR) << "CONFLICT_RESOLVING profile has a state that violates "
                    "kinematic limits: "
                 << debug_string;
    }
    Profile complete_search_profile = reference_generator.GetCompleteProfile(
        selected_discomfort_ix, search_profile);
    mutable_solver_result->Finalize(
        ProfileSolver::Result::SolverResult_Enum_kNoOpt,
        std::move(complete_search_profile));
    return;
  }

  // Return early if speed optimization is disabled.
  if (!FLAGS_decouple_planning_enable_speed_opt) {
    Profile complete_search_profile = reference_generator.GetCompleteProfile(
        selected_discomfort_ix, search_profile);
    mutable_solver_result->Finalize(
        ProfileSolver::Result::SolverResult_Enum_kNoOpt,
        std::move(complete_search_profile));
    return;
  }
  // Verify the search profile does not violate the smallest safety buffer
  // before speed opt.
  std::string debug_str1;
  DCHECK(!HasNonAllowedConflicts(
      search_profile, mutable_solver_result->constraints(),
      mutable_solver_result->decisions(), min_range,
      config_.for_search().min_range_max_required_lat_gap(),
      selected_discomfort_ix, &debug_str1))
      << debug_str1;

  DCHECK(  // NOLINT, FP when checking macro DCHECK
      search_result_status ==
          ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS ||
      search_result_status == ProfileSearcher::SearchResult::
                                  SearchResult_Enum_SUCCESS_WITH_GAP_ALIGN);

  // Get a warm start profile from pose time + immutable time with a
  // size = num_steps_.
  Profile warm_start_profile_from_immutable_t;
  const Limits& limits = all_limits.LimitsAtDiscomfortIx(
      mutable_solver_result->selected_discomfort_ix());
  if (warm_start_profile != nullptr &&
      !math::IsApprox(warm_start_profile->front().t,
                      plan_init_state_time_in_sec_ +
                          immutable_times_.at(selected_discomfort_ix))) {
    MapWarmStartProfileToFirstMutableTime(*warm_start_profile, limits,
                                          selected_discomfort_ix,
                                          warm_start_profile_from_immutable_t);
    warm_start_profile = &warm_start_profile_from_immutable_t;
  }

  // Check if the ego is reversing.
  const planner::pb::ReverseDrivingState_Enum reverse_driving_curr_state =
      previous_iter_seed.reverse_driving_seed().state();
  const bool is_reversing =
      reverse_driving_curr_state == planner::pb::ReverseDrivingState::kAction;

  // If the ego is in the process reversing, or reasoning side set
  // enable_opt_profile_validation, do not perform validity checks on the opt
  // profile.
  // TODO(reasoning): move reversing logic to reasoning side too.
  const bool enable_profile_validation =
      !is_reversing && constraint_manager.global_speed_solver_settings()
                           .enable_opt_profile_validation;

  const double ego_max_speed = traffic_rules::GetEgoMaxSpeedDependOnCar();
  if (debug) {
    debug->set_ego_max_speed(ego_max_speed);
  }

  const std::optional<planner::speed::Profile> last_profile =
      warm_start_profile ? std::make_optional(*warm_start_profile)
                         : std::nullopt;
  const std::optional<StayStopInfo>& stay_stop_info =
      mutable_solver_result->stay_stop_info();
  DCHECK(stay_stop_info.has_value());

  // TODO(yuwang): Since the initial regulation has been removed,
  // speed_immutable_time is no longer necessary. Clean up the code.
  DCHECK_EQ(immutable_times_.at(selected_discomfort_ix), 0.0)
      << "immutable time at discomfort " << selected_discomfort_ix << ": "
      << immutable_times_.at(selected_discomfort_ix);
  const double profile_init_time = plan_init_state_time_in_sec_;
  ProfileTimeAndSizeCheck(search_profile, profile_init_time, num_steps_);
  ProfileTimeAndSizeCheck(ref_profile, profile_init_time, num_steps_);
  ProfileTimeAndSizeCheck(min_profile, profile_init_time, num_steps_);
  if (last_profile.has_value()) {
    ProfileTimeAndSizeCheck(last_profile.value(), profile_init_time,
                            num_steps_);
  }

  planner::speed::SpeedOptInput speed_opt_input(
      config_.for_optimizer(), ref_profile, min_profile, search_profile,
      last_profile, reference_generator.reference_profile().path(),
      mutable_solver_result->constraints(), mutable_solver_result->decisions(),
      mutable_solver_result->gap_align_info_map(), tree_switch_time_ix,
      tree_search_type, limits, min_range,
      config_.for_search().min_range_max_required_lat_gap(),
      mutable_solver_result->selected_discomfort(), dt_, num_steps_,
      ra_to_fb_shift_, ra_to_rb_shift_, ego_max_speed, stay_stop_info.value(),
      constraint_manager.global_speed_solver_settings().attraction_for_progress,
      enable_profile_validation,
      constraint_manager.global_speed_solver_settings().disable_speed_attractor,
      /*check_param=*/true);

  const SpeedOptOutput opt_output = RunSpeedOptimizer(
      speed_opt_input, debug == nullptr ? nullptr : debug->mutable_opt_debug());
  mutable_solver_result->SetSpeedOptResult(opt_output.opt_result);

  // If speed optimization is successfully performed but the result of speed
  // optimization is detected to be invalid, maintain parking via min profile.
  if (opt_output.opt_result == pb::SpeedOptResult_Enum_kNotValid) {
    rt_event::PostRtEvent<rt_event::planner::OptProfileNotValid>();
    // Currently, OptNotValid is triggered during both near-stop deceleration
    // and initial acceleration from stop. If the profile cannot be smoothly
    // executed downstream, the comfort_stopping_profile is output instead of
    // the opt profile, allowing the ego vehicle to come to a smooth stop and
    // avoiding comfort issue caused by unnecessary starting.
    Profile comfort_stopping_profile =
        GenerateComfortStoppingProfile(ref_profile.front(), num_steps_);
    // TODO(speed): consider to deprecate Enum_kStaystopped since the stay
    // stopped profile is optimized and no longer mutually exclusive with other
    // enum types.
    mutable_solver_result->Finalize(
        stay_stop_info->maybe_stay_stop
            ? ProfileSolver::Result::SolverResult_Enum_kStayedStopped
            : ProfileSolver::Result::SolverResult_Enum_kOptProfileNotValid,
        std::move(comfort_stopping_profile), opt_output.is_creep);
    return;
  }

  std::string conflict_string;
  if (ShouldUsePartialOptProfile(opt_output.opt_result, opt_output.opt_profile,
                                 config_.for_optimizer(), config_.for_search(),
                                 min_range, constraint_result,
                                 selected_discomfort_ix,
                                 opt_output.has_lower_cost)) {
    rt_event::PostRtEvent<rt_event::planner::PartialOptResult>();
    Profile complete_optim_profile = reference_generator.GetCompleteProfile(
        selected_discomfort_ix, std::move(opt_output.opt_profile));
    mutable_solver_result->Finalize(
        ProfileSolver::Result::SolverResult_Enum_kPartialOptResult,
        std::move(complete_optim_profile), opt_output.is_creep);
    return;
  }
  bool is_profile_monotonic = IsProfileMonotonic(
      opt_output.opt_profile,
      config_.for_optimizer().allowed_negative_speed_error(),
      config_.for_optimizer().allowed_move_forward_x_error());
  if (!is_profile_monotonic) {
    rt_event::PostRtEvent<rt_event::planner::OptProfileNonMonotonic>();
  }
  if (opt_output.opt_result == pb::SpeedOptResult_Enum_kFail ||
      !is_profile_monotonic) {
    std::string debug_str;
    if (!ProfileIsAdmissible(evasive_limits, search_profile, 0, &debug_str)) {
      rt_event::PostRtEvent<rt_event::planner::OptProfileNotAdmissible>();
      LOG(ERROR) << "Optimizer fails and search result generate a state that "
                    "violates kinematic limits: "
                 << debug_str;
    }
    Profile complete_search_profile = reference_generator.GetCompleteProfile(
        selected_discomfort_ix, search_profile);
    mutable_solver_result->Finalize(
        ProfileSolver::Result::SolverResult_Enum_kNoOpt,
        std::move(complete_search_profile), opt_output.is_creep);
    return;
  }
  // TODO(guanao): enable this when speed opt dynamic limits barrier are strong
  // enough.
  // if (!ProfileIsAdmissible(evasive_limits, *profile, 0, &debug_string)) {
  //   LOG(ERROR) << "Optimizer profile has a state that violates kinematic
  //   limit: " << debug_string;
  // }
  DCHECK(is_profile_monotonic);
  Profile complete_optim_profile = reference_generator.GetCompleteProfile(
      selected_discomfort_ix, std::move(opt_output.opt_profile));
  // TODO(speed): consider to deprecate Enum_kStaystopped since the stay stopped
  // profile is optimized and no longer mutually exclusive with other enum
  // types.
  mutable_solver_result->Finalize(
      stay_stop_info->maybe_stay_stop
          ? ProfileSolver::Result::SolverResult_Enum_kStayedStopped
          : ProfileSolver::Result::SolverResult_Enum_kSuccess,
      std::move(complete_optim_profile), opt_output.is_creep);
}

bool ProfileSolver::ShouldUsePartialOptProfile(
    pb::SpeedOptResult::Enum opt_result, const Profile& optimized_profile,
    const pb::OptimizerConfig& optimizer_config,
    const pb::SearchConfig& search_config,
    const DiscomfortVaryingMinRange& min_range,
    const ConstraintResult& constraint_result, double selected_discomfort_ix,
    bool has_lower_cost) {
  std::string conflict_string;
  if (opt_result != pb::SpeedOptResult_Enum_kFail ||
      optimized_profile.empty() ||
      !IsProfileMonotonic(optimized_profile,
                          optimizer_config.allowed_negative_speed_error(),
                          optimizer_config.allowed_move_forward_x_error()) ||
      HasNonAllowedConflicts(optimized_profile, constraint_result.constraints,
                             constraint_result.decisions, min_range,
                             search_config.min_range_max_required_lat_gap(),
                             selected_discomfort_ix, &conflict_string)) {
    return false;
  }
  return has_lower_cost;
}

void ProfileSolver::MapWarmStartProfileToFirstMutableTime(
    const Profile& warm_start_profile, const Limits& limits,
    double selected_discomfort_ix,
    Profile& warm_start_profile_from_immutable_t) const {
  warm_start_profile_from_immutable_t.reserve(warm_start_profile.size());
  const auto iter = std::lower_bound(
      warm_start_profile.begin(), warm_start_profile.end(),
      plan_init_state_time_in_sec_ +
          immutable_times_.at(static_cast<int>(selected_discomfort_ix)),
      [](const State& state, const double value) { return state.t < value; });
  DCHECK(iter != warm_start_profile.end());
  warm_start_profile_from_immutable_t.insert(
      warm_start_profile_from_immutable_t.end(), iter,
      warm_start_profile.end());
  if (static_cast<int>(warm_start_profile_from_immutable_t.size()) <
      num_steps_) {
    ExtendProfileByZeroJerkOrReleasingBrake(
        num_steps_, dt_, limits, &warm_start_profile_from_immutable_t);
  }
}

}  // namespace speed
}  // namespace planner
