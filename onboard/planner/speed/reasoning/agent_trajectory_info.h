#ifndef ONBOARD_PLANNER_SPEED_REASONING_AGENT_TRAJECTORY_INFO_H_
#define ONBOARD_PLANNER_SPEED_REASONING_AGENT_TRAJECTORY_INFO_H_

#include <limits>
#include <optional>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include <glog/logging.h>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/overlap/ego_lateral_clearance.h"
#include "planner/speed/reasoning/overlap_region_info.h"
#include "planner/speed/reasoning_input/traffic_rules/conflicting_lane_traffic_rule.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"

namespace planner {
namespace speed {

// Struct ObjectTrajectoryId packs the object id and the trajectory id into
// a data structure,  which can be also used as the key of map.
struct ObjectTrajectoryId {
  ObjectTrajectoryId(int64_t object_id_in, int trajectory_id_in)
      : object_id(object_id_in), trajectory_id(trajectory_id_in) {}

  int64_t object_id;
  int trajectory_id;

  bool operator<(const ObjectTrajectoryId& other) const {
    return (object_id == other.object_id)
               ? (trajectory_id < other.trajectory_id)
               : (object_id < other.object_id);
  }

  bool operator==(const ObjectTrajectoryId& other) const {
    return (object_id == other.object_id) &&
           (trajectory_id == other.trajectory_id);
  }

  bool operator!=(const ObjectTrajectoryId& other) const {
    return !operator==(other);
  }

  [[nodiscard]] std::string ToString() const {
    return GetBpUniqueId(object_id, trajectory_id);
  }
};

using OverlapRegionReference = std::reference_wrapper<const pb::OverlapRegion>;

// This struct stores the info about switch time after which the underlying
// agent behavior of the predicted trajectory is uncertain. For example,
// a cyclist who is far away a junction could turn left or go straight. For a
// predicted trajectory turning left, the part entering the junction is
// uncertain until the cyclist starts to turn left. This is used to set risk
// mitigation parameters.
struct SwitchTimeInfo {
  enum class SwitchTimePolicyType {
    kNA = 0,
    kOncomingCross = 1,
  };
  SwitchTimePolicyType policy_type = SwitchTimePolicyType::kNA;
  // In seconds from the planing init time.
  double switch_time = std::numeric_limits<double>::infinity();
  std::string debug_str;

  [[nodiscard]] std::string ToString() const;
};

// AgentTrajectoryInfo contains the information of one predicted_trajectory
// for the planner_object. The predicted_trajectory could have multiple overlap
// regions with Ego's trajectory.
class AgentTrajectoryInfo {
 public:
  // Default ctor only constructs the minimum part of AgentTrajectoryInfo.
  // In the pipeline it's actually generated by GenerateAgentTrajectoryInfo.
  AgentTrajectoryInfo(
      const planner::PredictedTrajectoryWrapper& predicted_trajectory,
      std::vector<OverlapRegionReference>&& overlap_regions,
      ObjectTrajectoryId id);

  const planner::PredictedTrajectoryWrapper& predicted_trajectory() const {
    return predicted_trajectory_;
  }

  const pnc_map::Lane* agent_trajectory_current_lane() const {
    return agent_trajectory_current_lane_;
  }

  const std::vector<OverlapRegionReference>& overlap_regions() const {
    return overlap_regions_;
  }

  const pb::OverlapRegion& overlap_region(int overlap_idx) const {
    return overlap_regions().at(overlap_idx).get();
  }

  const std::vector<OverlapRegionInfo>& overlap_region_infos() const {
    return overlap_region_infos_;
  }

  const OverlapRegionInfo& overlap_region_info(int overlap_idx) const {
    return overlap_region_infos().at(overlap_idx);
  }

  const OverlapRegionInfo& overlap_region_info(
      const pb::OverlapRegion& overlap_region) const;

  const ObjectTrajectoryId& id() const { return id_; }

  double will_see_ego_time() const { return will_see_ego_time_; }

  void set_will_see_ego_time(double will_see_ego_time) {
    will_see_ego_time_ = will_see_ego_time;
  }

  // Returns the trajectory's road priority against ego lane.
  planner::pb::RoadPriorityType::Enum road_priority() const {
    return road_priority_;
  }

  // Returns the trajectory's road precedence against ego lane.
  planner::pb::RoadPrecedence::Enum road_precedence() const {
    return road_precedence_;
  }

  // Set the trajectory's road precedence against ego lane.
  void set_road_precedence(planner::pb::RoadPrecedence::Enum road_precedence) {
    road_precedence_ = road_precedence;
  }

  // Returns the trajectory's road precedence source.
  planner::pb::PrecedenceSource::Enum precedence_source() const {
    return precedence_source_;
  }

  // Set the trajectory's road precedence source.
  void set_precedence_source(
      planner::pb::PrecedenceSource::Enum precedence_source) {
    precedence_source_ = precedence_source;
  }

  // Returns the trajectory's road precedence scene type.
  planner::pb::PrecedenceSceneType::Enum precedence_scene_type() const {
    return precedence_scene_type_;
  }

  // Set the trajectory's road precedence scene type.
  void set_precedence_scene_type(
      planner::pb::PrecedenceSceneType::Enum precedence_scene_type) {
    precedence_scene_type_ = precedence_scene_type;
  }

  // Set debug string.
  void set_debug_str(std::string debug_str) {
    debug_str_ = std::move(debug_str);
  }

  const pb::AgentReactionTrackerDebug& art_debug() const { return art_debug_; }

  pb::AgentReactionTrackerDebug& mutable_art_debug() { return art_debug_; }

  // Sets the trajectory's road priority against ego lane. It is only used for
  // unit test.
  void set_road_priority(planner::pb::RoadPriorityType::Enum road_priority) {
    road_priority_ = road_priority;
  }

  bool is_agent_extra_cautious_on_trajectory() const {
    return is_agent_extra_cautious_on_trajectory_;
  }

  void set_is_agent_extra_cautious_on_trajectory(
      bool is_agent_extra_cautious_on_trajectory) {
    is_agent_extra_cautious_on_trajectory_ =
        is_agent_extra_cautious_on_trajectory;
  }

  bool is_agent_extra_cautious_for_slow_cut_in_in_front() const {
    return is_agent_extra_cautious_for_slow_cut_in_in_front_;
  }

  void set_is_agent_extra_cautious_for_slow_cut_in_in_front(
      bool is_agent_extra_cautious_for_slow_cut_in_in_front) {
    is_agent_extra_cautious_for_slow_cut_in_in_front_ =
        is_agent_extra_cautious_for_slow_cut_in_in_front;
  }

  bool is_agent_exiting_roundabout_from_inner_lane() const {
    return is_agent_exiting_roundabout_from_inner_lane_;
  }

  void set_is_agent_exiting_roundabout_from_inner_lane(
      bool is_agent_exiting_roundabout_from_inner_lane) {
    is_agent_exiting_roundabout_from_inner_lane_ =
        is_agent_exiting_roundabout_from_inner_lane;
  }

  void set_adhoc_agent_associated_route_opt(
      std::vector<route_association::MapElementAndPoseInfo>
          adhoc_agent_associated_route) {
    adhoc_agent_associated_route_opt_ = std::move(adhoc_agent_associated_route);
  }

  const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
  associated_route_opt() const {
    if (predicted_trajectory_.associated_route_opt().has_value()) {
      DCHECK(!adhoc_agent_associated_route_opt_.has_value());
      return predicted_trajectory_.associated_route_opt();
    }
    return adhoc_agent_associated_route_opt_;
  }

  // Returns true if Ego confidently has higher road precedence.
  bool EgoHasHigherRoadPrecedence() const {
    return road_precedence() == planner::pb::RoadPrecedence::LOWER;
  }

  // Returns true if Ego has unknown road precedence.
  bool EgoHasUnknownRoadPrecedence() const {
    return road_precedence() == planner::pb::RoadPrecedence::UNKNOWN;
  }

  // Returns true if Ego has equal road precedence.
  bool EgoHasEqualRoadPrecedence() const {
    return road_precedence() == planner::pb::RoadPrecedence::EQUAL;
  }

  // Returns true if Ego confidently has lower road precedence.
  bool EgoHasLowerRoadPrecedence() const {
    return road_precedence() == planner::pb::RoadPrecedence::HIGHER;
  }

  // Returns true if the OverlapRegion overlaps with certain crosswalk.
  bool HasOverlappedCrosswalk(const pb::OverlapRegion& overlap_region) const {
    return !overlap_region_info(overlap_region)
                .overlapped_crosswalk_infos.empty();
  }

  // Returns associated conflicting lanes that have intersections with ego lane
  // sequence. E.g., in the following illustration, Lane 1 will be returned.
  //                                    |    |
  //                                    |    |  <--- ego lane sequence
  //                                    |    |
  //  ---------------o------------------|----|-----
  //   ^---- Lane 2                     |    |  ^---- Lane 1 (conflicting lane)
  //                                    |    |
  //        ************************************* <--- predicted trajectory
  //  ---------------o------------------|----|-----
  //                                    |    |
  //                                    |    |
  const std::vector<traffic_rules::ConflictingLaneInLaneSequence>&
  associated_conflicting_lanes() const {
    return associated_conflicting_lanes_;
  }

  // Returns the likelihood in range [0.0, 1.0] of the predicted trajectory.
  double likelihood() const { return predicted_trajectory().Likelihood(); }

  // Returns yield intention. Note that the trajectory level yield intention is
  // either same as the agent level yield intention, or unknown yield intention
  // type (which indicates the trajectory scenario is out of scope of training
  // set.)
  const prediction::pb::YieldIntention& yield_intention() const {
    return predicted_trajectory_.yield_intention();
  }

  // Returns true if this trajectory is stationary.
  bool IsStationary() const { return predicted_trajectory().IsStationary(); }

  // Returns true if this trajectory is making turns.
  bool IsTurning() const { return predicted_trajectory().IsTurning(); }

  bool IsTurningRight() const {
    return predicted_trajectory().IsTurningRight();
  }

  // Deprecated method. Instead, you can use
  // |is_merging_from_right_turn_at_junction|.
  bool is_turning_right_inferring_from_associated_lanes() const {
    return is_turning_right_inferring_from_associated_lanes_;
  }

  bool is_merging_from_right_turn_at_junction() const {
    return is_merging_from_right_turn_at_junction_;
  }

  bool IsTurningLeft() const { return predicted_trajectory().IsTurningLeft(); }

  bool IsCompleteReverseDriving() const {
    return predicted_trajectory_.IsCompleteReverseDriving();
  }

  bool IsKTurnDriving() const { return predicted_trajectory_.IsKTurnDriving(); }

  // Returns true if the object is not in extra-cautious scenes, has no or very
  // low yield probability, but not UNKNOWN_YIELD_INTENTION. Note that
  // UNKNOWN_YIELD_INTENTION indicates that the scenario is currently not
  // covered by prediction model.
  bool HasNoYieldIntention() const;

  // Returns true if the agent's predicted trajectory is using crosswalk. If any
  // one of OverlapRegions of the agent's predicted trajectory is using
  // crosswalk, the predicted trajectory is considered as using crosswalk.
  bool is_using_crosswalk() const;

  // Returns true if the predicted trajectory is oncoming with respect to Ego
  // path.
  bool is_oncoming() const { return is_oncoming_; }

  // Returns true if the predicted trajectory is along the same direction with
  // respect to Ego path.
  bool is_same_direction() const { return is_same_direction_; }

  // Returns true if we should consider yield intention signal in agent
  // reactions for this predicted trajectory.
  bool consider_yield_intention() const { return consider_yield_intention_; }

  void set_consider_yield_intention(bool consider_yield_intention) {
    consider_yield_intention_ = consider_yield_intention;
  }

  // In the following two functions, we firstly check whether we could use the
  // yield intention, then compare the values.
  bool IsYieldIntentionUsableAndLowerThan(double threshold) const;
  bool IsYieldIntentionUsableAndHigherThan(double threshold) const;

  // Returns true if any overlap regions are in encroachment or merge-fork
  // regions, and we should add special cautious and conservative logic in
  // reasoning.
  bool has_overlap_region_in_encroachment_regions() const;
  bool has_overlap_region_in_merge_fork_regions() const;

  // Returns true if any overlap regions are in passive merge regions.
  bool has_overlap_region_in_passive_merge_regions() const;

  // Returns true if any overlap regions are in squeeze regions.
  bool has_overlap_region_in_squeeze_regions() const;

  bool has_overlap_region_in_xlane_nudge() const;

  bool is_overtaking_ego() const { return is_overtaking_ego_; }

  void set_is_overtaking_ego(bool is_overtaking_ego) {
    is_overtaking_ego_ = is_overtaking_ego;
  }

  bool is_overtaken_by_ego() const { return is_overtaken_by_ego_; }

  void set_is_overtaken_by_ego(bool is_overtaken_by_ego) {
    is_overtaken_by_ego_ = is_overtaken_by_ego;
  }

  bool is_cutting_in() const { return is_cutting_in_; }

  void set_is_cutting_in(bool is_cutting_in) { is_cutting_in_ = is_cutting_in; }

  // Returns true if the acc agent is cutting out from ego path.
  bool is_cutting_out() const { return is_cutting_out_; }

  void set_is_cutting_out(bool is_cutting_out) {
    is_cutting_out_ = is_cutting_out;
  }

  bool is_exiting_from_exit_zone() const;

  bool is_merging_from_side_road() const { return is_merging_from_side_road_; }

  void set_is_merging_from_side_road(bool is_mergeing_from_side_road) {
    is_merging_from_side_road_ = is_mergeing_from_side_road;
  }

  void set_is_merging_from_right_turn_at_junction(
      bool is_merging_from_right_turn_at_junction) {
    is_merging_from_right_turn_at_junction_ =
        is_merging_from_right_turn_at_junction;
  }

  void set_is_on_u_turn_lane(bool is_on_u_turn_lane) {
    is_on_u_turn_lane_ = is_on_u_turn_lane;
  }

  bool is_on_u_turn_lane() const { return is_on_u_turn_lane_; }

  void set_is_crossing_opposite_road_blocks(
      bool is_crossing_opposite_road_blocks) {
    is_crossing_opposite_road_blocks_ = is_crossing_opposite_road_blocks;
  }

  bool is_crossing_opposite_road_blocks() const {
    return is_crossing_opposite_road_blocks_;
  }
  void set_is_large_vehicle_turning_along_with_ego(
      bool is_large_vehicle_turning_along_with_ego) {
    is_large_vehicle_turning_along_with_ego_ =
        is_large_vehicle_turning_along_with_ego;
  }

  bool is_large_vehicle_turning_along_with_ego() const {
    return is_large_vehicle_turning_along_with_ego_;
  }

  bool is_reversing_trajectory_for_defensive_driving() const {
    return is_reversing_trajectory_for_defensive_driving_;
  }

  void set_is_reversing_trajectory_for_defensive_driving(
      bool is_reversing_trajectory_for_defensive_driving) {
    is_reversing_trajectory_for_defensive_driving_ =
        is_reversing_trajectory_for_defensive_driving;
  }

  bool is_vehicle_turning_from_crossing_to_oncoming_wrt_ego_current_lane_pose()
      const {
    return is_vehicle_turning_from_crossing_to_oncoming_wrt_ego_current_lane_pose_;
  }

  void set_agent_reaction_tracker_result(
      pb::AgentReactionTrackerResult agent_reaction_tracker_result) {
    agent_reaction_tracker_result_ = agent_reaction_tracker_result;
  }
  pb::AgentReactionTrackerResult agent_reaction_tracker_result() const {
    return agent_reaction_tracker_result_;
  }

  void set_yield_probability_with_intention_tracking(
      const std::optional<double>& yield_probability_with_intention_tracking) {
    yield_probability_with_intention_tracking_ =
        yield_probability_with_intention_tracking;
  }

  std::optional<double> yield_probability_with_intention_tracking() const {
    return yield_probability_with_intention_tracking_;
  }

  void set_agent_intention_with_model_fusion(
      const pb::LaneChangeAgentIntentionFusionResult&
          agent_intention_with_model_fusion) {
    agent_intention_with_model_fusion_ = agent_intention_with_model_fusion;
  }

  const pb::LaneChangeAgentIntentionFusionResult&
  agent_intention_with_model_fusion() const {
    return agent_intention_with_model_fusion_;
  }
  // Set the average acceleration in the first 3 seconds on the prediction
  // trajectory.
  void set_predicted_average_acceleration(
      double predicted_average_acceleration) {
    predicted_average_acceleration_ = predicted_average_acceleration;
  }

  double predicted_average_acceleration() const {
    return predicted_average_acceleration_;
  }

  bool is_inside_overtaking() const { return is_inside_overtaking_; }

  void set_is_inside_overtaking(bool is_inside_overtaking) {
    is_inside_overtaking_ = is_inside_overtaking;
  }

  bool is_moving_trajectory_for_start_to_move_agent() const {
    return predicted_trajectory_.MightStartToMoveSoon() &&
           !predicted_trajectory_.is_primary_trajectory();
  }

  bool IsCutBehindTrajectory() const {
    return std::any_of(overlap_region_infos_.begin(),
                       overlap_region_infos_.end(),
                       [](const OverlapRegionInfo& overlap_region_info) {
                         return overlap_region_info.is_cut_behind;
                       });
  }

  const SwitchTimeInfo& switch_time_info() const { return switch_time_info_; }

  pb::AgentTrajectoryInfoDebug ToProto() const;

  // Data memebers.

  const planner::PredictedTrajectoryWrapper& predicted_trajectory_;

  // The current lane of agent trajectory from associated route. Note: Will be
  // nullptr if we do not calculate the associated route for the trajectory.
  const pnc_map::Lane* agent_trajectory_current_lane_ = nullptr;

  // Overlaps between Ego path and the predicted trajectory. In common
  // cases, there is only one overlap but there could be multiple.
  const std::vector<OverlapRegionReference> overlap_regions_;

  // OverlapRegionInfo wraps an overlap region's properties.
  // NOTE(jinhao): overlap_region_infos_ has the same order as the
  // overlap_regions_, so we can index it by region id as well.
  std::vector<OverlapRegionInfo> overlap_region_infos_;

  const ObjectTrajectoryId id_;

  // The trajectory priority is against ego lane and affected by road
  // structures. When |road_priority_| is higher, it means the ego's priority
  // is lower. For pedestrians and cyclists around crosswalks, the road priority
  // from the matched crosswalk is also used to compute the road priority. For
  // pedestrians not around crosswalks, i.e. jaywalkers, the road priority is
  // always LOWER.
  // NOTE(minhanli): Be cautions when use, the |road_priority_| will be
  // deprecated soon, use |road_precedence_against_ego_| instead.
  planner::pb::RoadPriorityType::Enum road_priority_ =
      planner::pb::RoadPriorityType::UNKNOWN;

  // The trajectory precedence against ego lane sequence.
  planner::pb::RoadPrecedence::Enum road_precedence_ =
      planner::pb::RoadPrecedence::UNKNOWN;

  // The reason of being current trajectory precedence against ego lane
  // sequence.
  planner::pb::PrecedenceSource::Enum precedence_source_ =
      planner::pb::PrecedenceSource::None;

  // The scene type of being current trajectory precedence against ego lane
  // sequence.
  planner::pb::PrecedenceSceneType::Enum precedence_scene_type_ =
      planner::pb::PrecedenceSceneType::UnKnown;

  // Debug string.
  std::string debug_str_;

  // Debug info for ART.
  pb::AgentReactionTrackerDebug art_debug_;

  // If the Ego follows the reference trajectory and the agent follows the
  // predicted trajectory, the time w.r.t pose time when the agent will see the
  // ego.
  double will_see_ego_time_ = std::numeric_limits<double>::infinity();  // s

  // The associated conflicting lanes that have intersections with ego lane
  // sequence. This is inferred from the predicted trajectory maneuver's
  // source_lane_id and target_lane_ids.
  std::vector<traffic_rules::ConflictingLaneInLaneSequence>
      associated_conflicting_lanes_;

  // The associated route of agent predicted trajectory computed in the ad-hoc
  // way.
  // NOTE: This member could be |std::nullopt| if the corresponding
  // predicted trajectory never get in called
  // |reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory|, e.g,
  // the corresponding predicted trajectory wrapper already has a global
  // computed result.
  std::optional<std::vector<route_association::MapElementAndPoseInfo>>
      adhoc_agent_associated_route_opt_ = std::nullopt;

  // Whether or not this predicted trajectory is oncoming with respect to Ego
  // path.
  bool is_oncoming_ = false;

  // Whether or not this predicted trajectory is along the same direction with
  // respect to Ego path.
  bool is_same_direction_ = false;

  // When this boolean is true, we consider yield intention.
  // Check IsYieldIntentionUsable for more details
  bool consider_yield_intention_ = false;

  // This is a placeholder. This signal is intended to be set to true when an
  // agent has lower precedence, and watches the surrounding very carefully.
  // In such case, stronger agent reaction with larger FoV (including view from
  // mirrors) is supposed to be given. Targeted scenarios include but not
  // limited to, unprotected slow moving vehicles taking turns/leaving exist
  // zone/leaving bus stop, which are associated with trajectories.
  bool is_agent_extra_cautious_on_trajectory_ = false;
  // TODO(waylon): Consolidate |is_agent_extra_cautious_on_trajectory| and
  // |is_agent_extra_cautious_for_slow_cut_in_in_front| into one.
  bool is_agent_extra_cautious_for_slow_cut_in_in_front_ = false;

  // Whether or not the agent is exiting roundabout from ego's inner lane while
  // ego's in roundabout.
  bool is_agent_exiting_roundabout_from_inner_lane_ = false;

  // Whether or not any overlap region of the trajectory in any Ego encroachment
  // or fork-merge regions.
  bool has_overlap_region_in_encroachment_regions_ = false;
  bool has_overlap_region_in_merge_fork_regions_ = false;

  // Whether or not has overlap region with xlane nudge. True if it overlaps
  // with the path encroachment segment of xlane nudge path.
  bool has_overlap_region_in_xlane_nudge_ = false;

  // Whether the agent is overtaking Ego. This signal should only be meaningful
  // for vehicles and cyclists.
  bool is_overtaking_ego_ = false;

  // Whether the agent is overtaken by Ego. This signal should only be
  // meaningful for vehicles and cyclists.
  bool is_overtaken_by_ego_ = false;

  // Whether the agent is turing right. The quality of the signal is high
  // because it is inferred from associated lanes, rather than relying on a
  // heading difference comparison.
  bool is_turning_right_inferring_from_associated_lanes_ = false;

  // Whether the agent trajectory is cutting in the ego path.
  bool is_cutting_in_ = false;

  // Whether the agent trajectory is cutting out from the ego path.
  bool is_cutting_out_ = false;

  // This class member is true when:
  // 1, the agent is turning right.
  // 2, the agent and ego will pass the same junction and merge to a same
  // successor lane.
  bool is_merging_from_right_turn_at_junction_ = false;

  // This class member is true when:
  // 1, ego has higher road precedence.
  // 2, ego and the agent will merge into same lane, and they have different
  // precessor sections, u turn excluded.
  bool is_merging_from_side_road_ = false;

  // Whether the agent's trajectory is on u turn lane.
  bool is_on_u_turn_lane_ = false;

  // Whether the agent's trajectory is crossing opposite road blocks, e.g.
  // agents u turning out of junction, or crossing oncoming agents exiting from
  // the exit zone.
  bool is_crossing_opposite_road_blocks_ = false;

  // Whether it is a large vehicle turning along with ego in a junction.
  bool is_large_vehicle_turning_along_with_ego_ = false;

  // Whether the trajectory is reversing and need of defensive driving.
  bool is_reversing_trajectory_for_defensive_driving_ = false;

  bool is_vehicle_turning_from_crossing_to_oncoming_wrt_ego_current_lane_pose_ =
      false;

  // The result from agent reaction tracker.
  pb::AgentReactionTrackerResult agent_reaction_tracker_result_ =
      pb::AgentReactionTrackerResult::ART_NONE;

  // The result from yield_probability_with_intention_tracking.
  std::optional<double> yield_probability_with_intention_tracking_;

  // The result from agent_intention_with_model_fusion.
  pb::LaneChangeAgentIntentionFusionResult agent_intention_with_model_fusion_;

  // Indicates whether the agent is overtaking Ego by passing it on the inside
  // lane.
  bool is_inside_overtaking_ = false;

  // The average acceleration in the first 3 seconds on the prediction
  // trajectory.
  double predicted_average_acceleration_ = 0.0;

  // After this switch time, the underlying behaviour of the predicted
  // trajectory is uncertain.
  SwitchTimeInfo switch_time_info_;
};

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_AGENT_TRAJECTORY_INFO_H_
