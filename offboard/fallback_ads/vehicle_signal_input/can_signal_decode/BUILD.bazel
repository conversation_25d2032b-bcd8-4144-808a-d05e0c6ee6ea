package(default_visibility = ["//visibility:public"])

cc_library(
    name = "can_signal_decode",
    srcs = [
        "can_signal_decode.c",
    ],
    hdrs = [
        "can_signal_decode.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "vehicle_signal_input/can_signal_decode/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/vehicle_signal_input/can_timeout_detection",
    ],
)
