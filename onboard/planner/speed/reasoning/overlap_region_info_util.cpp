#include "planner/speed/reasoning/overlap_region_info_util.h"

#include <utility>

#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning/route_association_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/world_context.h"

namespace planner {
namespace speed {

namespace {

bool IsUsingCrosswalkOnOverlap(
    const PlannerObject& planner_object,
    const std::vector<OverlappedCrosswalkInfo>& overlapped_crosswalk_infos) {
  if (planner_object.object_type() != voy::perception::ObjectType::PED &&
      planner_object.object_type() != voy::perception::ObjectType::CYCLIST) {
    // Only cyclists and pedestrians could use crosswalk.
    return false;
  }
  if (overlapped_crosswalk_infos.empty()) {
    // This overlap is not matched with any crosswalk.
    return false;
  }

  // An overlap could overlap with multiple crosswalks.
  return std::any_of(overlapped_crosswalk_infos.begin(),
                     overlapped_crosswalk_infos.end(),
                     [](const OverlappedCrosswalkInfo& overlapped_crosswalk) {
                       return overlapped_crosswalk.is_using_crosswalk;
                     });
}

// Returns trues if the agent wants to cut-behind. Agents except vehicles,
// cyclists, and pedestrians, are not considered and false is returned for them.
bool DoesAgentWantToCutBehind(const WorldContext& world_context,
                              const TrajectoryInfo& ego_trajectory_info,
                              const ReasoningObject& reasoning_object,
                              const pb::OverlapRegion& overlap_region) {
  if (reasoning_object.is_pedestrian_or_cyclist()) {
    return reasoning_util::DoesVRUWantToCutBehindEgo(
        world_context, ego_trajectory_info, reasoning_object, overlap_region);
  }

  if (reasoning_object.is_vehicle()) {
    return reasoning_util::DoesVehicleWantToCutBehindEgo(
        world_context, ego_trajectory_info, reasoning_object, overlap_region);
  }

  return false;
}

bool AreLanesInEgoUnprotectedLeftTurn(
    const TrajectoryInfo& trajectory_info,
    const std::vector<const pnc_map::Lane*>& lanes) {
  const std::vector<traffic_rules::EgoLaneConflictingLanesInfo>&
      traffic_rules_conflicting_lanes =
          trajectory_info.traffic_rules().lane_sequence_conflicting_lanes;
  return std::any_of(
      lanes.begin(), lanes.end(),
      [&traffic_rules_conflicting_lanes](const pnc_map::Lane* lane) {
        return reasoning_util::IsUnprotectedLeftTurn(
            lane, traffic_rules_conflicting_lanes);
      });
}

bool LanesHaveConflictingLanes(const std::vector<const pnc_map::Lane*>& lanes) {
  return std::any_of(lanes.begin(), lanes.end(), [](const pnc_map::Lane* lane) {
    return lane != nullptr && !lane->conflicting_lanes().empty();
  });
}
}  // namespace

OverlapRegionInfo ComputeOverlapRegionInfo(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pb::OverlapRegion& overlap_region) {
  DCHECK_EQ(overlap_region.object_id(), reasoning_object.id());
  OverlapRegionInfo overlap_region_info;
  overlap_region_info.overlapped_crosswalk_infos =
      MatchOverlapRegionWithCrosswalk(
          reasoning_object.planner_object(), predicted_trajectory,
          overlap_region, trajectory_info.traffic_rules().crosswalks);

  overlap_region_info.overlap_region_ix = overlap_region.region_id();
  const PlannerObject& planner_object = reasoning_object.planner_object();
  const pb::ObjectProximityInfo& agent_proximity_info =
      reasoning_object.object_proximity_info();
  overlap_region_info.is_using_crosswalk = IsUsingCrosswalkOnOverlap(
      planner_object, overlap_region_info.overlapped_crosswalk_infos);
  const speed::OverlapRegionEncroachmentInfo overlap_encroachment_info =
      reasoning_util::ComputeOverlapRegionEncroachmentInfo(
          trajectory_info, world_context.robot_state(), predicted_trajectory,
          overlap_region);
  overlap_region_info.encroachment_side =
      overlap_encroachment_info.encroachment_side,
  overlap_region_info.encroachment_segment_opt =
      overlap_encroachment_info.encroachment_segment_opt;
  overlap_region_info.is_encroaching_oncoming_lane =
      overlap_encroachment_info.is_encroaching_oncoming_lane;
  overlap_region_info.overlap_segments_in_wrong_way_encroachment_region =
      overlap_encroachment_info
          .overlap_segments_in_wrong_way_encroachment_region;
  overlap_region_info.is_in_merge_regions =
      reasoning_util::IsOverlapRegionInMergeForkLanes(
          trajectory_info.traffic_rules().merge_fork_lanes, overlap_region,
          /*is_checking_merge=*/true, /*is_active_type=*/true);
  overlap_region_info.is_in_fork_regions =
      reasoning_util::IsOverlapRegionInMergeForkLanes(
          trajectory_info.traffic_rules().merge_fork_lanes, overlap_region,
          /*is_checking_merge=*/false, /*is_active_type=*/true);
  overlap_region_info.is_in_passive_merge_regions =
      reasoning_util::IsOverlapRegionInMergeForkLanes(
          trajectory_info.traffic_rules().passive_merge_fork_lanes,
          overlap_region,
          /*is_checking_merge=*/true, /*is_active_type=*/false);
  overlap_region_info.is_in_xlane_nudge =
      reasoning_util::IsXLaneNudgeInterestedTrajectory(
          trajectory_info, overlap_region_info.is_in_encroachment_regions());
  overlap_region_info.covered_ego_lanes =
      reasoning_util::GetOverlapCoveredEgoLanes(trajectory_info,
                                                overlap_region);
  overlap_region_info.is_in_left_turn =
      reasoning_util::HasSpecifiedTurnTypeInSequence(
          overlap_region_info.covered_ego_lanes, hdmap::Lane::LEFT);
  overlap_region_info.is_in_u_turn =
      reasoning_util::HasSpecifiedTurnTypeInSequence(
          overlap_region_info.covered_ego_lanes, hdmap::Lane::U_TURN);
  overlap_region_info.is_in_unprotected_left_turn =
      AreLanesInEgoUnprotectedLeftTurn(trajectory_info,
                                       overlap_region_info.covered_ego_lanes);
  overlap_region_info.is_cut_in = reasoning_util::IsCutInOverlapRegion(
      trajectory_info, predicted_trajectory, planner_object, overlap_region);
  overlap_region_info.is_cut_out =
      reasoning_util::IsCutOutOverlapRegion(overlap_region);
  overlap_region_info.is_cut_behind = DoesAgentWantToCutBehind(
      world_context, trajectory_info, reasoning_object, overlap_region);
  overlap_region_info.is_crossing_ego_path =
      reasoning_util::IsTrajectoryCrossingEgoPath(overlap_region);
  overlap_region_info.is_in_first_junction =
      reasoning_util::IsOverlapRegionInFirstJunction(trajectory_info,
                                                     overlap_region);
  overlap_region_info.is_crossing_at_right_hook =
      reasoning_util::IsCyclistGoingStraightAtHookRegion(
          trajectory_info, planner_object, predicted_trajectory, overlap_region,
          /*check_right_hook=*/true);
  overlap_region_info.is_crossing_at_left_hook =
      reasoning_util::IsCyclistGoingStraightAtHookRegion(
          trajectory_info, planner_object, predicted_trajectory, overlap_region,
          /*check_right_hook=*/false);
  overlap_region_info.is_in_squeeze_regions =
      reasoning_util::IsOverlapRegionInSqueezeRegions(
          trajectory_info, agent_proximity_info, overlap_region);
  overlap_region_info.is_fully_behind_rear_axle =
      GetPaddedOverlapEnd(overlap_region) +
          world_context.ra_to_trailing_bumper_shift() <
      0.0;
  overlap_region_info.is_fully_behind_rear_axle_allowing_zero_pass_rlg =
      overlap_region_info.is_fully_behind_rear_axle &&
      !trajectory_info.IsReverseDriving() && reasoning_object.IsStationary();

  if (LanesHaveConflictingLanes(overlap_region_info.covered_ego_lanes)) {
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
        predicted_trajectory.associated_route_opt().has_value()
            ? predicted_trajectory.associated_route_opt().value()
            : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                  reasoning_object, predicted_trajectory,
                  *world_context.joint_pnc_map_service(),
                  /*allow_using_for_cyclist=*/true);
    overlap_region_info.includes_conflicting_lanes =
        route_association_util::OverlapCoversEgoAndAgentLaneConflict(
            agent_route, overlap_region_info.covered_ego_lanes);
    // NOTE: this signal also use some members of overlap_region_info, so make
    // sure call this function after covered_ego_lanes,
    // includes_conflicting_lanes, is_in_unprotected_left_turn are computed.
    overlap_region_info.is_scene_vulnerable_to_yield_paradox =
        reasoning_util::IsSceneVulnerableToYieldParadox(
            trajectory_info, reasoning_object, overlap_region,
            overlap_region_info, agent_route);
  }

  return overlap_region_info;
}

}  // namespace speed
}  // namespace planner
