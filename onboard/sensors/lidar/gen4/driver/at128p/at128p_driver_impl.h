#ifndef ONBOARD_SENSORS_LIDAR_GEN4_DRIVER_AT128P_AT128P_DRIVER_IMPL_H_
#define ONBOARD_SENSORS_LIDAR_GEN4_DRIVER_AT128P_AT128P_DRIVER_IMPL_H_

#include <functional>
#include <memory>
#include <string>
#include <thread>  // NOLINT(build/c++11)
#include <tuple>
#include <vector>

#include <boost/asio/io_service.hpp>
#include <boost/make_shared.hpp>
#include <boost/system/error_code.hpp>
#include <pcl/point_cloud.h>
#include <tbb/concurrent_queue.h>

#include "at128p_packet_checker.h"
#include "av_comm/onboard_config.h"
#include "driver/driver_defines.h"
#include "driver/lidar_driver.h"
#include "driver/lidar_event.h"
#include "driver/scan_data_handler.h"
#include "driver/udp_parser/at128p_udp_parser.h"
#include "lidar/point_types.h"
#include "module/udp/udp_receiver.h"
#include "ptc_client.h"

namespace lidar {

// class AT128PDriverImpl

class AT128PDriverImpl {
 public:
  AT128PDriverImpl(
      const std::string &ip_address, const std::string &remote_ip_address,
      const std::string &interface_address, bool is_multicast, int tcp_port,
      int udp_port, LidarOptions lidar_options,
      IScanDataHandler *scan_data_handler,
      const std::function<void(pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t,
                               bool)> &data_callback,
      const std::function<void(LidarEvent, const std::string &)>
          &message_callback,
      boost::asio::io_service *io_service);
  ~AT128PDriverImpl();

 public:
  boost::system::error_code Start();
  boost::system::error_code Stop();
  boost::system::error_code GetData(
      pcl::PointCloud<lidar::PointXYZIR>::Ptr &point_cloud, int64_t &timestamp,
      bool &is_cycle_completed);
  boost::system::error_code GetIntrinsicParameter(
      std::vector<uint8_t> &intrinsic_parameter);
  boost::system::error_code SetReturnType(int return_type);
  boost::system::error_code GetStatus(int &temperature, int &motor,
                                      int &ptp_status);

 public:
  void OnPacket(std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr);
  void OnError(const boost::system::error_code &error_code);

 private:
  void OnData(std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr);
  void OnFaultMessage(std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr);
  pb::LidarScanSector *AllocateScanSector();
  bool IsScanCycleComplete(int scan_angle) const;
  bool IsDiscard(int scan_angle) const;

 private:
  boost::asio::io_service *io_service_;
  std::string ip_address_;
  std::string remote_ip_address_;
  std::string interface_address_;
  bool is_multicast_;
  int tcp_port_;
  int udp_port_;
  int last_angle_{-1};
  bool is_scan_cycle_complete_{false};
  bool is_clockwise_rotation_direction_{true};

  std::vector<uint8_t> intrinsic_parameter_;

  IScanDataHandler *scan_data_handler_;
  std::function<void(pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t, bool)>
      data_callback_;
  std::function<void(LidarEvent, const std::string &)> message_callback_;
  tbb::concurrent_bounded_queue<
      std::tuple<pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t, bool>>
      point_cloud_queue_;
  bool stop_flag_{false};
  std::unique_ptr<std::thread> work_thread_;
  PTCClient ptc_client_;
  AT128PUdpParser at128p_udp_parser_;
  AT128PPacketChecker at128p_packet_checker_;
  uint32_t last_sequence_{0};
};

}  // namespace lidar

#endif  // ONBOARD_SENSORS_LIDAR_GEN4_DRIVER_AT128P_AT128P_DRIVER_IMPL_H_
