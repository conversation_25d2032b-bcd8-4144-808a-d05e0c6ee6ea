load(
    "//bazel:defs.bzl",
    "install",
    "shared_library",
    "voy_add_trace_provider",
)

package(default_visibility = ["//visibility:public"])

voy_add_trace_provider(
    name = "voy_trace_provider_ntb_bridge",
    manifest_file = ":trace_ntb_bridge_manifest.json",
)

cc_library(
    name = "common",
    hdrs = [
        "common.h",
    ],
    deps = [
        ":ntb_driver",
        "//onboard/common/av_comm:voy_common_common",
    ],
)

cc_library(
    name = "ntb_driver",
    srcs = [
        "libntb/ntb_lib.c",
    ],
    hdrs = [
        "libntb/ntb_lib.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=gnu11",
        "-Wno-sign-compare",
    ],
)

cc_library(
    name = "ntb_driver_checker",
    srcs = [
        "ntb_driver_checker.cpp",
    ],
    hdrs = [
        "ntb_driver_checker.h",
    ],
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "ntb_receiver",
    srcs = ["ntb_receiver.cpp"],
    hdrs = [
        "ntb_receiver.h",
    ],
    deps = [
        ":common",
        ":ntb_driver",
        ":voy_trace_provider_ntb_bridge",
        "//onboard/varch",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "ntb_sender",
    hdrs = [
        "ntb_sender.h",
    ],
    deps = [
        ":common",
        ":ntb_driver",
        ":voy_trace_provider_ntb_bridge",
        "//onboard/common/voy_protos:proto_util",
    ],
)

shared_library(
    name = "ntb_bridge_vnode",
    srcs = [
        "ntb_bridge_vnode.cpp",
    ],
    deps = [
        ":ntb_driver",
        ":ntb_receiver",
        "//onboard/varch",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:boost",
        "@voy-sdk//:gflags",
        "@voy-sdk//:glog",
        "@voy-sdk//:ros",
    ],
)

install(
    name = "install_rospkg",
    srcs = [":package.xml"],
    dest = "share/ntb_bridge_gen4",
)

install(
    name = "install_vgraph",
    srcs = ["onboard/gen4/ntb_bridge/vgraph"],
    dest = "share/ntb_bridge_gen4",
)
