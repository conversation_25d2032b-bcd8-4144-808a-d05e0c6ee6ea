#ifndef ONBOARD_SENSORS_LIDAR_SCAN_CONVERT_MULTI_SCAN_MESSAGE_MANAGER_H_
#define ONBOARD_SENSORS_LIDAR_SCAN_CONVERT_MULTI_SCAN_MESSAGE_MANAGER_H_

#include <map>
#include <memory>
#include <utility>
#include <vector>

#include <glog/logging.h>

#include "av_comm/history_buffer.h"
#include "lidar/full_scan_checker.h"
#include "lidar/scan_channel.h"
#include "lidar/scan_converter.h"

namespace lidar {

using PclMsgAdpator = lidar::ScanConverter::OutputMessageAdaptor;
using PclMsgAdpatorUptr = std::unique_ptr<PclMsgAdpator>;

class MultiScanMessageManager {
 public:
  virtual ~MultiScanMessageManager() = 0;
  virtual PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                                    bool is_surrounding_point = false) = 0;
  virtual bool PushBack(PclMsgAdpatorUptr&&,
                        bool is_surrounding_point = false) = 0;
};

/////////////////// WholeMultiScanMessageManager ///////////////////

class WholeMultiScanMessageManager : public MultiScanMessageManager {
 public:
  class ScanSyncTracker {
   public:
    enum class Status { INCOMPLETE, EXPIRED, COMPLETE };

    ScanSyncTracker(int expected_count, int timeout_ms);

    Status AddScan(voy::Sensor::SensorType sensor_type, int64_t timestamp);
    bool IsComplete() const;
    bool IsTimedOut() const;
    void ClearState();

   private:
    void InitIfNeeded();

    const int expected_count_;
    const int timeout_ms_;
    int64_t start_time_{0};
    int64_t min_timestamp_{0};
    std::map<voy::Sensor::SensorType, int64_t> scan_time_map_;
  };

 public:
  explicit WholeMultiScanMessageManager(int expected_scans,
                                        int timeout_ms = 50);
  ~WholeMultiScanMessageManager() override;

  PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                            bool is_surrounding_point = false) override;
  bool PushBack(PclMsgAdpatorUptr&&,
                bool is_surrounding_point = false) override;

  std::optional<ScanMessage> GetMergedScan(bool is_surrounding_point = false);

  void ResetAll();

 protected:
  ScanSyncTracker tracker_;

 private:
  void Reset();

 private:
  ScanMessage merged_scan_;
  ScanMessage merged_surrounding_scan_;
};

/////////////////// LazySliceMultiScanMessageManager ///////////////////

class LazySliceMultiScanMessageManager : public MultiScanMessageManager {
 public:
  LazySliceMultiScanMessageManager();
  ~LazySliceMultiScanMessageManager() override;

  PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                            bool is_surrounding_point = false) override;

  // We should call SetNumScans first before PushBack because of Lazily
  // initialize. With Lazily initialize this |FullScanChecker| here as we don't
  // know the exact number of slices the full scan has been splitted to, until
  // we get the first Lidar scan slice. This brings extra flexibility, so that
  // we can split the full Lidar scan to any number of slices we want and
  // the |FullScanChecker| can easily handle such case.
  bool PushBack(PclMsgAdpatorUptr&&,
                bool is_surrounding_point = false) override;

  std::optional<ScanMessage> GetMergedScan(bool is_surrounding_point = false);

  virtual void SetNumScans(int total_num_scans);

 protected:
  std::unique_ptr<FullScanChecker> full_scan_checker_;
  lidar::PointCloudMessage converted_scan_;
  lidar::PointCloudMessage converted_surrounding_scan_;

 private:
  void Reset();

 private:
  ScanMessage merged_scan_;
  ScanMessage merged_surrounding_scan_;
};

/////////////////// SeparateMultiScanMessageManager ///////////////////

class SeparateMultiScanMessageManager : public MultiScanMessageManager {
 public:
  SeparateMultiScanMessageManager(
      const std::vector<voy::Sensor::SensorType>& sensor_types,
      int buffer_size = 3);
  ~SeparateMultiScanMessageManager() override;

  PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                            bool is_surrounding_point = false) override;
  bool PushBack(PclMsgAdpatorUptr&&,
                bool is_surrounding_point = false) override;

  std::vector<PointCloudMessage> GetPointCloudMessageByTimestamp(
      int64_t scan_timestamp, bool is_surrounding_point = false,
      int max_diff_timestamp = 100);

 private:
  using PointCloudHistoryBuffer = std::map<
      voy::Sensor::SensorType,
      std::unique_ptr<av_comm::HistoryBuffer<int64_t, PointCloudMessage>>>;
  PointCloudHistoryBuffer point_cloud_message_history_buffers_;
  PointCloudHistoryBuffer surrounding_point_cloud_message_history_buffers_;
};

/////////////////// DefaultMultiScanMessageManager ///////////////////

class DefaultMultiScanMessageManager : public MultiScanMessageManager {
 public:
  DefaultMultiScanMessageManager();
  ~DefaultMultiScanMessageManager() override;

  PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                            bool is_surrounding_point = false) override;
  bool PushBack(PclMsgAdpatorUptr&&,
                bool is_surrounding_point = false) override;

  std::map<voy::Sensor::SensorType, PointCloudMessage> GetPcls(
      bool is_surrounding_point = false);

 private:
  using PointCloudMap = std::map<voy::Sensor::SensorType, PointCloudMessage>;
  PointCloudMap point_cloud_msg_map_;
  PointCloudMap surrounding_point_cloud_msg_map_;
};

}  // namespace lidar

#endif  // ONBOARD_SENSORS_LIDAR_SCAN_CONVERT_MULTI_SCAN_MESSAGE_MANAGER_H_
