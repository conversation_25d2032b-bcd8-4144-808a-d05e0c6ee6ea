/**
 * @file gpiod_interface.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-07-03
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __GPIOD_INTERFACE_HPP__
#define __GPIOD_INTERFACE_HPP__

#include <unistd.h>

#include <iostream>
#include <list>

#include "gpiod.h"
namespace voyager {
namespace hardware {

class GpiodInterface {
 public:
  static int gpiod_set_up(const char *chip_path, int offset, int value,
                          struct gpiod_chip **chip, struct gpiod_line **line,
                          const char *consumer) {
    *chip = gpiod_chip_open(chip_path);
    if (!(*chip)) return -1;

    *line = gpiod_chip_get_line(*chip, offset);
    if (!(*line)) goto chip_exit;

    if (gpiod_line_request_output(*line, consumer, 0) < 0) goto chip_exit;

    if (gpiod_line_set_value(*line, value) < 0) goto chip_exit;
    return 0;

  // line_exit:
  //   gpiod_line_release(*line);
  chip_exit:
    gpiod_chip_close(*chip);
    return -1;
  }

  static void gpiod_set_down(struct gpiod_chip *chip, struct gpiod_line *line) {
    if (!chip || !line) return;
    gpiod_line_release(line);
    line = nullptr;
    gpiod_chip_close(chip);
    chip = nullptr;
  }
};

}  // namespace hardware
}  // namespace voyager

#endif