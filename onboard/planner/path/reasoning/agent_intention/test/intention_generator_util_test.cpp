#include "planner/path/reasoning/agent_intention/intention_generator_util.h"

#include <vector>

#include <gtest/gtest.h>

#include <planner_protos/agent_intention.pb.h>
#include <voy_protos/perception_object_type.pb.h>
#include "planner/decoupled_maneuvers/required_lateral_gap/required_lateral_gap_info.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"

namespace planner {

TEST(IntentionGeneratorUtil, StaticObject) {
  const std::vector<math::Range<int>> effective_intention_index_ranges;
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  const IntentionResultMetaData meta_data(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/1,
      /*is_static=*/true, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_LEFT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      effective_intention_index_ranges,
      /*required_lat_gap=*/required_lat_gap);

  const auto intention = GenerateIntention(meta_data);
  EXPECT_EQ(intention.object_id(), 1);
  EXPECT_TRUE(intention.is_static());
  EXPECT_EQ(intention.snapshot_intentions_size(), 1);
  EXPECT_EQ(intention.snapshot_intentions().at(0).pass_state(),
            pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(intention.is_overtaken(), true);
}

TEST(IntentionGeneratorUtil, PassObject) {
  const std::vector<math::Range<int>> effective_intention_index_ranges = {
      {2, 10}};
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  const IntentionResultMetaData meta_data(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/false, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_LEFT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      effective_intention_index_ranges,
      /*required_lat_gap=*/required_lat_gap);
  const auto intention = GenerateIntention(meta_data);
  EXPECT_EQ(intention.object_id(), 1);
  EXPECT_FALSE(intention.is_static());
  EXPECT_EQ(intention.is_overtaken(), true);
  EXPECT_EQ(intention.snapshot_intentions_size(), 3);
  const auto decision_interval0 = intention.snapshot_intentions().at(0);
  EXPECT_EQ(decision_interval0.pass_state(), pb::SnapshotIntention::IGNORE);
  EXPECT_EQ(decision_interval0.start_snapshot_index(), 0);
  EXPECT_EQ(decision_interval0.end_snapshot_index(), 1);

  const auto decision_interval1 = intention.snapshot_intentions().at(1);
  EXPECT_EQ(decision_interval1.pass_state(), pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(decision_interval1.start_snapshot_index(), 2);
  EXPECT_EQ(decision_interval1.end_snapshot_index(), 10);

  const auto decision_interval2 = intention.snapshot_intentions().at(2);
  EXPECT_EQ(decision_interval2.pass_state(), pb::SnapshotIntention::IGNORE);
  EXPECT_EQ(decision_interval2.start_snapshot_index(), 11);
  EXPECT_EQ(decision_interval2.end_snapshot_index(), 19);
}

TEST(IntentionGeneratorUtil, IgnoredObject) {
  const std::vector<math::Range<int>> effective_intention_index_ranges = {
      {2, 10}};
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  const IntentionResultMetaData meta_data(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/false, /*is_overtaken=*/false,
      /*lateral_decision=*/pb::SnapshotIntention::IGNORE,
      /*lon_decision=*/speed::pb::SpeedDecision::NOT_DECIDED,
      effective_intention_index_ranges,
      /*required_lat_gap=*/required_lat_gap);
  const auto intention = GenerateIntention(meta_data);
  EXPECT_EQ(intention.object_id(), 1);
  EXPECT_FALSE(intention.is_static());
  EXPECT_EQ(intention.snapshot_intentions_size(), 1);
  const auto decision_interval0 = intention.snapshot_intentions().at(0);
  EXPECT_EQ(decision_interval0.pass_state(), pb::SnapshotIntention::IGNORE);
  EXPECT_EQ(decision_interval0.start_snapshot_index(), 2);
  EXPECT_EQ(decision_interval0.end_snapshot_index(), 10);
  EXPECT_EQ(intention.is_overtaken(), false);
}

TEST(IntentionGeneratorUtil, PricipledRequiredLateralGapTest) {
  // Build PlannerObject.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  TrafficParticipantPose traffic_participant_pose(/*timestamp=*/101,
                                                  tracked_object);
  PlannerObject planner_object(agent_proto, /*timestamp=*/101,
                               traffic_participant_pose);
  std::unordered_map<int64_t, PlannerObject> planner_object_map;
  planner_object_map.emplace(/*obj_id=*/1, planner_object);

  // Build RequiredLateralGapInfo.
  const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      obj_id_to_required_lat_gaps =
          ComputePrincipledRequiredLateralGapForPlannerObjects(
              planner_object_map, pb::RequiredLateralGapConfig());
  std::unordered_map<ConstructionZoneId, PrincipledRequiredLateralGap>
      cz_id_to_required_lat_gaps;
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  RequiredLateralGapInfo required_lateral_gap_info =
      RequiredLateralGapInfo(std::move(obj_id_to_required_lat_gaps),
                             cz_id_to_required_lat_gaps, required_lat_gap);

  EXPECT_NEAR(
      GetCriticalRequiredLateralGapFromPrincipledRequiredLateralGap<ObjectId>(
          /*obj_id=*/1,
          required_lateral_gap_info.object_id_to_required_lateral_gaps),
      0.332415, 1e-5);
}

TEST(IntentionGeneratorUtil, CanEgoBrakeToYieldTest) {
  EgoInLaneParams ego_params;
  ego_params.front_bumper_arclength = 2.0;
  ego_params.arclength_m = 0.;
  double abs_brake_limit_mpss = 5.0;

  // 1. Sanity Check
  ego_params.speed_mps = 5.0;
  double time = 5.0;
  double agent_speed_mps = 0.0;
  double agent_start_arclength_m = 10.0;
  double agent_full_body_end_arclength_m = 15.0;
  bool is_agent_oncoming = false;
  EXPECT_TRUE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: sanity check failed";
  // 2.1 Oncoming
  ego_params.speed_mps = 5.0;
  time = 5.0;
  agent_speed_mps = 5.0;
  is_agent_oncoming = true;
  agent_start_arclength_m = 0.0;
  agent_full_body_end_arclength_m = 5.0;
  // Oncoming agent with longitudinal overlap. Ego cannot brake to yield the
  // agent.
  EXPECT_FALSE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.1 Oncoming agent with overlap test failed";

  // Oncoming agent with longitudinal overlap but passed by ego, ego could
  // ignore the agent.
  agent_start_arclength_m = -10.0;
  agent_full_body_end_arclength_m = -5.0;
  EXPECT_TRUE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.1 Oncoming agent passed test failed";

  // 2.2 Parallel
  ego_params.speed_mps = 5.0;
  time = 5.0;
  agent_speed_mps = 5.0;
  is_agent_oncoming = false;
  agent_start_arclength_m = 0.0;
  agent_full_body_end_arclength_m = 5.0;
  EXPECT_FALSE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.2 Parallel agent with overlap test failed";

  // 2.2.1 Stop to yield
  ego_params.speed_mps = 5.0;
  time = 5.0;
  agent_speed_mps = 0.0;
  agent_start_arclength_m = 10.0;
  agent_full_body_end_arclength_m = 15.0;
  EXPECT_TRUE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.2.1 (1)ego can stop failed";

  ego_params.speed_mps = 10.0;
  time = 5.0;
  agent_speed_mps = 0.0;
  agent_start_arclength_m = 5.0;
  agent_full_body_end_arclength_m = 10.0;
  EXPECT_FALSE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.2.1 (2)ego can't stop failed";

  // 2.2.2 Can't brake to yield
  ego_params.speed_mps = 10.0;
  time = 1.0;
  agent_speed_mps = 1.0;
  agent_start_arclength_m = 10.0;
  agent_full_body_end_arclength_m = 20.0;
  EXPECT_FALSE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.2.2 ego can't brake failed";

  // 2.2.3 Brake to yield
  ego_params.speed_mps = 10.0;
  time = 1.0;
  agent_speed_mps = 6.0;
  agent_start_arclength_m = 15.0;
  agent_full_body_end_arclength_m = 20.0;
  EXPECT_TRUE(CanEgoBrakeToYieldSnapshot(
      ego_params, agent_speed_mps, agent_start_arclength_m,
      agent_full_body_end_arclength_m, abs_brake_limit_mpss, is_agent_oncoming,
      time))
      << "CanEgoBrakeToYieldTest: 2.2.3 ego can brake failed";
}
}  // namespace planner
