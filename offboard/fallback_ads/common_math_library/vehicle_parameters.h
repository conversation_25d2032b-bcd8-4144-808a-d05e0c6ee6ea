#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_VEHICLE_PARAMETERS_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_VEHICLE_PARAMETERS_H_

#ifdef __cplusplus
extern "C" {
#endif

// get vehicle parameter
extern float GetVehicleMass();
extern float GetVehicleLength();
extern float GetVehicleWidth();
extern float GetVehicleWheelBase();
extern float GetVehicleFrontWheelCorneringStiffness();
extern float GetVehicleRearWheelCorneringStiffness();
extern float GetVehicleFrontAxleToCogDistance();
extern float GetVehicleRearAxleToCogDistance();
extern float GetVehicleRearAxleToFrontBumperDistance();
extern float GetVehicleInertia();
extern float GetVehicleSteerRatio();

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_VEHICLE_PARAMETERS_H_
