#include "planner/path/path_solver/nominal_path_planner.h"

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "hdmap/lib/test/test_util.h"
#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/path_solver/path_input.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/lateral_clearance/cpu_lateral_clearance_generator.h"
#include "planner/world_model/test/world_model_test_utility.h"
#include "pnc_map_service/test/map_test_util.h"
#include "voy_protos/canbus.pb.h"
#include "voy_protos/pose.pb.h"

namespace planner {
namespace path {
namespace {

constexpr double kCurrentX = -4498.062135;
constexpr double kCurrentY = -2587.717336;

using ::google::protobuf::TextFormat;

class NominalPathPlannerTest : public ::testing::Test {
 public:
  NominalPathPlannerTest() = default;

 protected:
  void SetUp() override {
    CHECK(TextFormat::ParseFromString(
        R"pb(
          state_lattice_search_config {
            lattice_space_config {
              station_lon_res_in_meter: 0.5
              min_station_lon_res_in_meter: 0.1
              mesh_block_lon_res_in_meter: 5.0
              min_mesh_block_lon_res_in_meter: 2.0
              lat_res_in_meter: 0.2
              max_lat_horizon_in_meter: 6.0
              speed_profile_timestamp_prediction_buffer_in_ms: 500
              cautious_mesh_block_lon_res_in_meter: 2.5
              merge_back_heading_reference_lateral_acceleration_in_mpss: 0.4
              forbidden_region_look_ahead_time_in_sec: 3.0
              min_forbidden_region_look_ahead_horizon_in_meter: 25.0
              uturn_diverse_heading_deviation_scaling_factor: 8.0
              boundary_strengthen_range_around_uturn_start {
                start_pos: -20.0
                end_pos: 5.0
              }
              boundary_strengthen_buffer_around_uturn_high_curvature_end: 5.0
              hard_lat_repulsion_boundary_buffer_in_meter: 1.5
              soft_lat_repulsion_boundary_buffer_in_meter: 0.5
              lc_heading_diversity_look_ahead_sample_dist: 20.0
              lc_heading_diversity_num: 3
              lc_heading_diversity_sample_buffer: 0.5
              pullover_heading_diversity_dest_backward_buffer_in_meter: 10.0
              pullover_heading_diversity_num: 3
              min_jump_in_heading_diversity_sampling_res: 0.5
              trajectory_state_lattice_config {
                default_timestamp_search_half_range_sec: 1.0
                dynamic_nudge_timestamp_search_half_range_sec: 0.5
                min_timestamp_search_half_range_sec: 0.2
                timestamp_res_sec: 0.1
                default_speed_search_half_range_mps: 2.0
                min_speed_search_half_range_mps: 1.0
                speed_res_mps: 0.5
                default_accel_search_half_range_mpss: 2.0
                accel_res_mpss: 0.5
                jerk_controls_mpsss: [ -3.0, -1.5, -0.75, 0.0, 0.75, 1.5, 3.0 ]
              }
              max_allowed_lattice_sample_curvature: 0.234
              max_lc_range_for_node_upsampling_in_meter: 20.0
              max_search_step: 2
            }
            lattice_costing_config {
              curvature_cost_weight: 3.6
              pinch_cost_weight: 1.2
              look_ahead_forbidden_region_weight: 28.0
              heuristic_cost_weight: 3.0
              path_resolution_in_meter: 0.5
              last_path_peak_weight: 5.0
              last_path_base_weight: 1.0
              critical_dist_to_forbidden_weight: 200.0
              lat_accel_cost_weight: 0.1
              search_start_lateral_jerk_cost_weight: 2.0
              lat_accel_soft_costing_range_in_mpss {
                start_pos: 0.0
                end_pos: 2.75
              }
              in_lane_deviation_lateral_dist_threshold_in_meter: 0.5
              in_lane_devitation_attraction_weight: 6.0
              cross_lane_deviation_attraction_weight: 3.0
              max_allowed_lattice_spiral_curvature: 0.234
              over_max_level_curvature_cost_weight: 20.0
              curvature_soft_costing_range { start_pos: 0.0 end_pos: 0.025 }
              repulsion_boundary_violation_cost_param_set {
                weak_strength_param { weight: 4.0 penalty: 0.0 }
                moderate_strength_param { weight: 4.0 penalty: 0.0 }
                strong_strength_param { weight: 80.0 penalty: 0.0 }
              }
              soft_boundary_violation_cost_param_set {
                weak_strength_param { weight: 4.0 penalty: 0.0 }
                moderate_strength_param { weight: 4.0 penalty: 0.0 }
                strong_strength_param { weight: 8.0 penalty: 5.0 }
              }
              reasoning_boundary_violation_cost_param_set {
                weak_strength_param { weight: 4.0 penalty: 0.0 }
                moderate_strength_param { weight: 4.0 penalty: 0.0 }
                strong_strength_param { weight: 8.0 penalty: 5.0 }
              }
              lc_reasoning_boundary_violation_cost_param_set {
                weak_strength_param { weight: 4.0 penalty: 0.0 }
                moderate_strength_param { weight: 4.0 penalty: 0.0 }
                strong_strength_param { weight: 20.0 penalty: 20.0 }
              }
              stable_last_path_attraction_decay_time_range_in_sec {
                start_pos: 1.0
                end_pos: 4.0
              }
              flicker_last_path_attraction_decay_time_range_in_sec {
                start_pos: 0.5
                end_pos: 1.5
              }
              over_max_level_lat_accel_cost_weight: 5.0
              default_attraction_cost_weight_scale_factor_set {
                weak_strength_scale_factor: 0.5
                moderate_strength_scale_factor: 1.0
              }
              pinch_continuity_cost_weight: 0.6
              search_start_pinch_continuity_cost_weight: 6.0
              strong_attraction_cost_weight_scale_factor_set {
                weak_strength_scale_factor: 5.0
                moderate_strength_scale_factor: 5.0
              }
              moderate_attraction_cost_weight_scale_factor_set {
                weak_strength_scale_factor: 2.0
                moderate_strength_scale_factor: 3.0
              }
              comfort_abs_accel_thresh_mpss: 0.75
              accel_cost_weight: 5.0
              jerk_cost_weight: 0.5
              jerk_continuity_cost_weight: 5.0
              intention_speed_profile_deviation_cost_weight: 0.75
              costing_pt_sample_arc_length_res_in_meter: 0.5
              lat_accel_penalty_time_decay_range_in_sec {
                start_pos: 0.0
                end_pos: 2.0
              }
              lat_accel_penalty_weight_range { start_pos: 2.0 end_pos: 10.0 }
              sample_lat_accel_over_limit_threshold_in_mpss: 3.5
              ego_speed_limit_in_mps: 16.7
              intention_speed_weight_for_lat_accel_penalty: 0.7
              min_spiral_arc_length_sample_interval_in_meter: 0.1
              strong_dist_to_horizon_cost_weight: 6.0
              weak_dist_to_horizon_cost_weight: 3.0
            }
            forbidden_region_info_config {
              forbidden_region_lon_range_buffer_in_meter: 1.0
              dynamic_forbidden_region_lon_front_time_buffer_in_sec: 0.5
              dynamic_forbidden_region_lon_rear_time_buffer_in_sec: 0.25
              default_critical_required_lateral_gap_in_meter: 1.2
              default_comfort_required_lateral_gap_in_meter: 1.4
            }
            kinematic_pruning_config {
              max_lateral_accel_for_kinematic_pruning_in_mpss: 5.36
              min_lateral_accel_for_kinematic_pruning_in_mpss: 1.47
              lateral_proximity_deviation_threshold_in_meter: 0.5
              integration_res_in_meter: 0.5
              max_search_lateral_jerk_in_mpsss: 4.0
              min_search_lateral_jerk_in_mpsss: 1.5
              max_search_pinch_in_radpmm: 0.04
              min_search_pinch_in_radpmm: 0.02
            }
          }
          nominal_path_smoother_config {
            nudge_range_buffer_in_meter: 10.0
            ddp_path_generator_config {
              costing_config {
                costing_base {
                  feature_param {
                    nudge_buffer_distance: 0.75
                    max_lateral_acceleration: 3.0
                    comfort_distance_to_road_boundary: 0.45
                    max_abs_steering_rate: 0.5
                    max_abs_steering_accel: 0.6
                    max_abs_juke: 0.8
                  }
                  costing_terms {
                    feature_type: CONSTRAINT_KAPPA
                    costing_spec {
                      type: BARRIER
                      weight: 1000.0
                      additional_param: 100
                    }
                  }
                  costing_terms {
                    feature_type: CONSTRAINT_ETA
                    costing_spec { type: QUADRATIC weight: 1000.0 }
                  }
                  costing_terms {
                    feature_type: CONSTRAINT_GAMMA
                    costing_spec { type: QUADRATIC weight: 1000.0 }
                  }
                  costing_terms {
                    feature_type: CONSTRAINT_APPROXIMATE_LATERAL_ACCEL
                    costing_spec { type: QUADRATIC weight: 10.0 }
                  }
                  costing_terms {
                    feature_type: EFFORT_APPROXIMATE_LATERAL_ACCEL
                    costing_spec { type: QUADRATIC weight: 2.0 }
                  }
                  costing_terms {
                    feature_type: EFFORT_KAPPA
                    costing_spec { type: QUADRATIC weight: 20.0 }
                  }
                  costing_terms {
                    feature_type: EFFORT_ETA
                    costing_spec { type: QUADRATIC weight: 2000.0 }
                  }
                  costing_terms {
                    feature_type: EFFORT_GAMMA
                    costing_spec { type: QUADRATIC weight: 20000.0 }
                  }
                  costing_terms {
                    feature_type: PRIMITIVE_VIOLATION_LEFT_NUDGE_CORRIDOR
                    costing_spec { type: QUADRATIC weight: 1000.0 }
                  }
                  costing_terms {
                    feature_type: PRIMITIVE_VIOLATION_RIGHT_NUDGE_CORRIDOR
                    costing_spec { type: QUADRATIC weight: 1000.0 }
                  }
                  costing_terms {
                    feature_type: PRIMITIVE_SOFT_NOMINAL_PATH_ATTRACTION
                    costing_spec { type: QUADRATIC weight: 10.0 }
                  }
                }
              }

              solver_config {
                max_mainloop_iteration: 50
                min_absolute_value_reduction: 1e-1
                min_relative_value_reduction: 1e-2
                line_search_shrink_factor: 0.5
                line_search_timeout_threshold: 1e-5
                armijo_factor: 0.5
                cost_reduction_relaxation: 0.5
                max_run_time_in_ms: 100
              }
            }
          }
          min_path_horizon_in_meter: 10.0
          max_path_horizon_in_meter: 135.0)pb",
        &config_));
    map_test_util_.InitPncMapService(kCurrentX, kCurrentY, /*z=*/0,
                                     /*route_name=*/"fremont_route_1",
                                     hdmap::test_util::kFremontData);
    test_speed_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0,
        /*x0=*/0.0, /*v0=*/5.0, /*n_sample=*/80, /*dt=*/0.1);

    pose_.set_x(kCurrentX);
    pose_.set_y(kCurrentY);
    pose_.set_yaw(2.0);

    lane_sequence_ = map_test_util_.pnc_map_service()->GetLaneSequence(
        std::vector<int64_t>{16913, 17049, 16905});

    speed_limits_.accel_a = {-2, 2};
    speed_limits_.brake_a = {-2, 2};
    speed_limits_.accel_j = {-2, 2};
    speed_limits_.brake_j = {-2, 2};
  }

  std::vector<const pnc_map::Lane*> lane_sequence_;
  pb::NominalPathPlannerConfig config_;
  pnc_map::MapTestUtil map_test_util_;
  speed::Profile test_speed_;
  speed::Limits speed_limits_;
  voy::Pose pose_;
};

TEST_F(NominalPathPlannerTest, BasePathPlannerForwardTest) {
  const auto world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const std::vector<ConstructionZoneInLaneState> cz_in_lane_states;
  const AgentInLaneStatesMap agents_in_lane_states_map;

  NominalPathPlanner nominal_path_planner(config_);
  pb::PathPlannerDebug debug;

  const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      obj_id_to_required_lat_gaps =
          ComputePrincipledRequiredLateralGapForPlannerObjects(
              std::unordered_map<int64_t, PlannerObject>(),
              pb::RequiredLateralGapConfig());
  std::unordered_map<ConstructionZoneId, PrincipledRequiredLateralGap>
      cz_id_to_required_lat_gaps;
  const auto hard_boundary_required_lat_gap =
      GetRequiredLateralGapForHardBoundaries();
  auto origin_polyline =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence_, lane_selection::LaneCurveType::kCenterLine)
          .polyline();
  math::geometry::Polyline2d points = {origin_polyline.rbegin(),
                                       origin_polyline.rend()};

  RoadBoundaries road_boundaries;
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 1.0}, {10.0, 1.0}};
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -1.0}, {10.0, -1.0}};
  // soft driving corridor
  auto& left_soft_element = road_boundaries.lane_marking_boundaries
                                .left_boundary_elements.emplace_back();
  left_soft_element.boundary_line =
      math::geometry::PolylineCurve2d(left_boundary_points);
  left_soft_element.boundary_type = BoundaryType::kVirtualSoft;
  left_soft_element.optional_lane_marking_arc_length_range =
      math::Range1d(0.0, 10.0);
  left_soft_element.optional_max_violation_m = 3.0;
  auto& right_soft_element = road_boundaries.lane_marking_boundaries
                                 .right_boundary_elements.emplace_back();
  right_soft_element.boundary_line =
      math::geometry::PolylineCurve2d(right_boundary_points);
  right_soft_element.boundary_type = BoundaryType::kVirtualSoft;
  right_soft_element.optional_lane_marking_arc_length_range =
      math::Range1d(0.0, 10.0);
  right_soft_element.optional_max_violation_m = 3.0;
  // hard driving corridor.
  BoundaryElement left_boundary_element;
  left_boundary_element.boundary_line =
      math::geometry::PolylineCurve2d(left_boundary_points);
  BoundaryElement right_boundary_element;
  right_boundary_element.boundary_line =
      math::geometry::PolylineCurve2d(right_boundary_points);
  road_boundaries.path_search_boundaries.left_boundary_elements.push_back(
      left_boundary_element);
  road_boundaries.path_search_boundaries.right_boundary_elements.push_back(
      right_boundary_element);

  auto reference_path = lane_selection::GetLaneSequenceCurve(
      lane_sequence_, lane_selection::LaneCurveType::kCenterLine);
  const EgoInLaneParams robot_in_lane_param = lane_selection::GetEgoInLaneParam(
      world_model.robot_state(), reference_path, pb::MotionMode::FORWARD);

  PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result.set_homotopy(
      pb::IntentionResult::IGNORE_ALL);
  *path_reasoning_result.intention_result.mutable_selected_speed_profile() =
      speed::ToProto(test_speed_);
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<ConstraintManager>(
          adv_geom::PathCurve2d(reference_path));
  path_reasoning_result.homotopic_constraint_manager->AssignRoadBoundaries(
      std::move(road_boundaries));

  // Generate lateral clearance data.
  std::unique_ptr<lateral_clearance::CpuLateralClearanceGenerator>
      lateral_clearance_generator =
          std::make_unique<lateral_clearance::CpuLateralClearanceGenerator>(
              /*resolution=*/0.05,
              constants::kDefaultMaxRayCastingSearchDistanceInMeter);
  lateral_clearance::LateralClearanceData lateral_clearance_data =
      lateral_clearance_generator->Generate(
          reference_path, math::Range1d(0.0, 1.0),
          std::vector<math::geometry::PolylineCurve2d>(),
          std::vector<math::geometry::PolylineCurve2d>(),
          std::vector<math::geometry::PolylineCurve2d>(),
          hard_boundary_required_lat_gap);
  path_reasoning_result.homotopic_constraint_manager->SetLateralClearanceData(
      std::move(lateral_clearance_data));

  NominalPathPlannerInput input(
      world_model, lane_sequence_, pb::ManeuverType::LANE_FOLLOW,
      reference_path,
      /*path_planning_length_in=*/30,
      /*speed_upper_bound_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*speed_lower_bound_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*slow_down_speed_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*intention_speed_in*/ std::make_shared<speed::Profile>(test_speed_),
      speed::DiscomfortVaryingLimits(std::vector<speed::Limits>(
          speed::Discomforts::kLevels, speed_limits_)),
      path_reasoning_result, agents_in_lane_states_map, cz_in_lane_states,
      /*motion_mode=*/pb::MotionMode::FORWARD,
      /*last_motion_mode=*/pb::MotionMode::FORWARD,
      /*is_pull_out_jump_out=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP,
      /*last_behavior_type=*/pb::BehaviorType::LANE_KEEP,
      /*trajectory_type=*/pb::NOMINAL_TRAJECTORY,
      /*last_intention_homotopy=*/pb::IntentionResult::IGNORE_ALL,
      /*last_selected_path=*/std::nullopt,
      /*should_use_last_selected_path_for_warmstart=*/false,
      /*is_same_lane_sequence_from_last_cycle=*/false,
      /*robot_in_lane_params_in=*/robot_in_lane_param,
      /*reverse_stop_point=*/std::nullopt,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*lane_change_execution_info=*/nullptr,
      /*arclength_ranges_in_junction=*/{},
      /*xlane_nudge_drivable_boundary_extend_dist_in=*/std::nullopt,
      /*current_motion_segment_in=*/std::nullopt,
      /*complete_hard_boundary_lines_in=*/{},
      /*ml_planner_meta_in=*/std::nullopt, /*skip_path_search=*/false);

  const auto result = nominal_path_planner.Plan(
      input, pb::SingleHomotopyPathSeed(), /*cur_cycle_seed=*/nullptr, &debug);

  ASSERT_TRUE(result.has_value());
}

TEST_F(NominalPathPlannerTest, BasePathPlannerBackwardTest) {
  const auto world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);

  const std::vector<ConstructionZoneInLaneState> cz_in_lane_states;
  const AgentInLaneStatesMap agents_in_lane_states_map;

  NominalPathPlanner nominal_path_planner(config_);
  pb::PathPlannerDebug debug;

  const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>
      obj_id_to_required_lat_gaps =
          ComputePrincipledRequiredLateralGapForPlannerObjects(
              std::unordered_map<int64_t, PlannerObject>(),
              pb::RequiredLateralGapConfig());
  std::unordered_map<ConstructionZoneId, PrincipledRequiredLateralGap>
      cz_id_to_required_lat_gaps;
  const auto hard_boundary_required_lat_gap =
      GetRequiredLateralGapForHardBoundaries();

  auto origin_polyline =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence_, lane_selection::LaneCurveType::kCenterLine)
          .polyline();
  math::geometry::Polyline2d points = {origin_polyline.rbegin(),
                                       origin_polyline.rend()};

  RoadBoundaries road_boundaries;
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 1.0}, {10.0, 1.0}};
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -1.0}, {10.0, -1.0}};
  // soft driving corridor
  auto& left_soft_element = road_boundaries.lane_marking_boundaries
                                .left_boundary_elements.emplace_back();
  left_soft_element.boundary_line =
      math::geometry::PolylineCurve2d(left_boundary_points);
  left_soft_element.boundary_type = BoundaryType::kVirtualSoft;
  left_soft_element.optional_lane_marking_arc_length_range =
      math::Range1d(0.0, 10.0);
  left_soft_element.optional_max_violation_m = 3.0;
  auto& right_soft_element = road_boundaries.lane_marking_boundaries
                                 .right_boundary_elements.emplace_back();
  right_soft_element.boundary_line =
      math::geometry::PolylineCurve2d(right_boundary_points);
  right_soft_element.boundary_type = BoundaryType::kVirtualSoft;
  right_soft_element.optional_lane_marking_arc_length_range =
      math::Range1d(0.0, 10.0);
  right_soft_element.optional_max_violation_m = 3.0;
  // hard driving corridor.
  BoundaryElement left_boundary_element;
  left_boundary_element.boundary_line =
      math::geometry::PolylineCurve2d(left_boundary_points);
  BoundaryElement right_boundary_element;
  right_boundary_element.boundary_line =
      math::geometry::PolylineCurve2d(right_boundary_points);
  road_boundaries.path_search_boundaries.left_boundary_elements.push_back(
      left_boundary_element);
  road_boundaries.path_search_boundaries.right_boundary_elements.push_back(
      right_boundary_element);

  const auto reference_path = lane_selection::GetLaneSequenceCurve(
      lane_sequence_, lane_selection::LaneCurveType::kCenterLine);
  const EgoInLaneParams robot_in_lane_param = lane_selection::GetEgoInLaneParam(
      world_model.robot_state(), reference_path, pb::MotionMode::BACKWARD);

  PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result.set_homotopy(
      pb::IntentionResult::IGNORE_ALL);
  *path_reasoning_result.intention_result.mutable_selected_speed_profile() =
      speed::ToProto(test_speed_);
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<ConstraintManager>(
          adv_geom::PathCurve2d(reference_path));
  path_reasoning_result.homotopic_constraint_manager->AssignRoadBoundaries(
      std::move(road_boundaries));

  // Generate lateral clearance data.
  std::unique_ptr<lateral_clearance::CpuLateralClearanceGenerator>
      lateral_clearance_generator =
          std::make_unique<lateral_clearance::CpuLateralClearanceGenerator>(
              /*resolution=*/0.05,
              constants::kDefaultMaxRayCastingSearchDistanceInMeter);
  lateral_clearance::LateralClearanceData lateral_clearance_data =
      lateral_clearance_generator->Generate(
          reference_path, math::Range1d(0.0, 1.0),
          std::vector<math::geometry::PolylineCurve2d>(),
          std::vector<math::geometry::PolylineCurve2d>(),
          std::vector<math::geometry::PolylineCurve2d>(),
          hard_boundary_required_lat_gap);
  path_reasoning_result.homotopic_constraint_manager->SetLateralClearanceData(
      std::move(lateral_clearance_data));

  NominalPathPlannerInput input_reverse(
      world_model, lane_sequence_, pb::ManeuverType::DECOUPLED_FORWARD,
      reference_path,
      /*path_planning_length_in=*/30,
      /*speed_upper_bound_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*slow_down_speed_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*speed_lower_bound_in=*/std::make_shared<speed::Profile>(test_speed_),
      /*intention_speed_in*/ std::make_shared<speed::Profile>(test_speed_),
      speed::DiscomfortVaryingLimits(std::vector<speed::Limits>(
          speed::Discomforts::kLevels, speed_limits_)),
      path_reasoning_result, agents_in_lane_states_map, cz_in_lane_states,
      /*motion_mode=*/pb::MotionMode::BACKWARD,
      /*last_motion_mode=*/pb::MotionMode::BACKWARD,
      /*is_pull_out_jump_out=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP,
      /*last_behavior_type=*/pb::BehaviorType::LANE_KEEP,
      /*trajectory_type=*/pb::ML_PATH,
      /*last_intention_homotopy=*/pb::IntentionResult::IGNORE_ALL,
      /*last_selected_path=*/std::nullopt,
      /*should_use_last_selected_path_for_warmstart=*/false,
      /*is_same_lane_sequence_from_last_cycle=*/false,
      /*robot_in_lane_params_in=*/robot_in_lane_param,
      /*reverse_stop_point=*/std::nullopt,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*lane_change_execution_info=*/nullptr,
      /*arclength_ranges_in_junction=*/{},
      /*xlane_nudge_drivable_boundary_extend_dist_in=*/std::nullopt,
      /*current_motion_segment_in=*/std::nullopt,
      /*complete_hard_boundary_lines_in=*/{},
      /*ml_planner_meta_in=*/std::nullopt, /*skip_path_search=*/false);
  const auto result_reverse =
      nominal_path_planner.Plan(input_reverse, pb::SingleHomotopyPathSeed(),
                                /*cur_cycle_seed=*/nullptr, &debug);

  ASSERT_TRUE(result_reverse.has_value());
}

}  // namespace
}  // namespace path
}  // namespace planner
