#ifndef ONBOARD_SENSORS_LIDAR_NODE_GEN4_SCAN_CONVERSION_NODE_H_
#define ONBOARD_SENSORS_LIDAR_NODE_GEN4_SCAN_CONVERSION_NODE_H_

#include <list>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "lidar/scan_conversion_config.h"
#include "lidar/scan_conversion_message.h"
#include "scan_conversion_node_utils.h"
#include "scan_convert/multi_scan_converter.hpp"
#include "scan_convert/multi_scan_message_manager.h"
#include "varch/vnode/vnode.h"

namespace lidar {
namespace gen4_scan_conversion_node {

class Gen4MultiScanMessageManager : public lidar::WholeMultiScanMessageManager {
 public:
  Gen4MultiScanMessageManager(
      int expected_scans, int timeout_ms,
      voy::PointCloudStorageType pcl_storaget_type,
      std::shared_ptr<ShmPoolManager> scan_pool,
      std::shared_ptr<ShmPoolManager> surrounding_pool,
      std::unordered_map<voy::Sensor::SensorType,
                         std::shared_ptr<node::FaultReporter>>
          fault_reporter_list);
  ~Gen4MultiScanMessageManager() override;

  PclMsgAdpatorUptr NewElem(voy::Sensor::SensorType,
                            bool is_surrounding_point = false) override;
  bool PushBack(PclMsgAdpatorUptr&&,
                bool is_surrounding_point = false) override;
  std::optional<voy::ScanMessage> GetMergedScan(
      bool is_surrounding_point = false);
  void ResetAll();
  std::vector<std::unique_ptr<ScanConverter::OutputMessageAdaptor>>
  GetAllHolder();

 private:
  void Reset();

 private:
  voy::PointCloudStorageType pcl_storaget_type_;

  std::shared_ptr<ShmPoolManager> scan_pool_;
  std::shared_ptr<ShmPoolManager> surrounding_pool_;

  voy::ScanMessage merged_scan_;
  voy::ScanMessage merged_surrounding_scan_;

  std::vector<std::unique_ptr<ScanConverter::OutputMessageAdaptor>> holder_;

  std::unordered_map<voy::Sensor::SensorType,
                     std::shared_ptr<node::FaultReporter>>
      fault_reporter_list_;
};

typedef MultiScanConverter<Gen4MultiScanMessageManager>
    Gen4WholeMultiScanConverter;

using ScanConversionNodeBase = varch::vnode::VNode<
    varch::vnode::PubMessages<voy::ScanMessage, voy::ScanMessage>,
    varch::vnode::SubMessages<voy::Pose, pb::LidarScan, pb::LidarScan,
                              pb::LidarScan, pb::LidarScan, pb::LidarScan,
                              pb::LidarScan, pb::LidarScan, pb::LidarScan,
                              pb::LidarScan, pb::LidarScan>>;

class Gen4ScanConversionNode : public ScanConversionNodeBase {
 public:
  Gen4ScanConversionNode();
  ~Gen4ScanConversionNode();

  bool Init() override;

  void Callback(
      std::list<std::shared_ptr<const voy::Pose>>& pose_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_1_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_2_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_3_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_main_4_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_1_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_2_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_3_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_4_list,
      std::list<std::shared_ptr<const pb::LidarScan>>& lidar_gen4_side_5_list,
      std::list<std::shared_ptr<const pb::LidarScan>>&
          lidar_gen4_sidenode_shm_pool_manager_6_list) override;

 protected:
  std::unique_ptr<ScanConversionConfig> config_;

 private:
  void DumpMatchedInfo(int64_t main_timestamp, int64_t side_timestamp) const;
  void OutputDebugInfo(
      const std::optional<lidar::ScanMessage>& side_scan_message,
      const std::optional<voy::ScanMessage>& main_scan_message) const;

 private:
  std::shared_ptr<Gen4WholeMultiScanConverter> main_multi_scan_converter_;
  std::shared_ptr<WholeMultiScanConverter> side_multi_scan_converter_;

  std::unordered_map<voy::Sensor::SensorType,
                     std::shared_ptr<node::FaultReporter>>
      fault_reporter_list_;

 private:
  std::shared_ptr<ShmPoolManager> msg_pool_;
  std::shared_ptr<ShmPoolManager> surrounding_msg_pool_;

  voy::PointCloudStorageType pcl_storaget_type_;

  uint64_t publish_frame_id_ = 0;
};

}  // namespace gen4_scan_conversion_node
}  // namespace lidar

#endif  // ONBOARD_SENSORS_LIDAR_NODE_GEN4_SCAN_CONVERSION_NODE_H_
