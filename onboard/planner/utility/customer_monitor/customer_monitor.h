#ifndef ONBOARD_PLANNER_UTILITY_CUSTOMER_MONITOR_CUSTOMER_MONITOR_H_
#define ONBOARD_PLANNER_UTILITY_CUSTOMER_MONITOR_CUSTOMER_MONITOR_H_

#include <set>
#include <unordered_map>
#include <utility>
#include <vector>

#include "planner/behavior/types/behavior_decision.h"
#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner_protos/customer_monitor_config.pb.h"
#include "planner_protos/customer_monitor_visual.pb.h"
#include "pnc_map_service/pnc_map_service.h"
#include "voy_protos/traffic_light.pb.h"
#include "voy_trace/trace_planner.h"

namespace planner {

// The Enum for the direction of cyclist relative to ego (for voice warning).
enum class CyclistDirFromEgo {
  IN_EGO_REAR_PROFILE = 0,
  LEFT_TO_EGO_REAR_PROFILE,
  RIGHT_TO_EGO_REAR_PROFILE,
  MOTOR_DIR_TO_EGO_OTHERS
};

struct CyclistVoiceWarningHist {
  CyclistDirFromEgo dir_from_ego;
  int count = 0;
  pb::VoiceWarningType type = pb::VoiceWarningType::NO_VOICE;
};

struct WaypointAssistVisualInfo {
  WaypointAssistVisualInfo(
      pb::AssistPathGenerationStatus::Enum assist_path_generation_status_in,
      bool has_arrived_waypoint_assist_path_end_in,
      pb::PathFailureType path_failure_type_in,
      const std::optional<std::vector<const pnc_map::Lane*>>&
          waypoints_end_feasible_lanes_in)
      : assist_path_generation_status(assist_path_generation_status_in),
        has_arrived_waypoint_assist_path_end(
            has_arrived_waypoint_assist_path_end_in),
        path_failure_type(path_failure_type_in),
        waypoints_end_feasible_lanes(waypoints_end_feasible_lanes_in) {}

  // The path generathed status of waypoint assist maneuver.
  pb::AssistPathGenerationStatus::Enum assist_path_generation_status =
      pb::AssistPathGenerationStatus::kFailed;
  // Indicates whether ego has arrived in the waypoint assist path end.
  bool has_arrived_waypoint_assist_path_end = false;
  // The reason of path generation failure.
  pb::PathFailureType path_failure_type = pb::PathFailureType::PATH_VALID;
  // The lanes which the end of waypoints should be located in.
  // TODO(Xiang): Currently it stores the lane follow sequence lanes, will set
  // it as last successful regional path after we remove the lane follow
  // sequence limitation.
  std::optional<std::vector<const pnc_map::Lane*>> waypoints_end_feasible_lanes;
};

// The information for customer monitor visualization in different maneuver.
struct ManeuverCustomerMonitorVisual {
  // The type of selected maneuver.
  pb::ManeuverType maneuver_type = pb::LANE_FOLLOW;
  // The id of acc agents.
  std::set<int64_t> acc_agent_ids;
  // The id of nudge agents.
  std::set<int64_t> nudge_agent_ids;
  // The id of evade agents.
  std::set<int64_t> evade_agent_ids;
  // The distance to the junction entrance in meters.
  std::optional<double> junction_entrance_distance_m;
  // The distance to the junction exit in meters.
  std::optional<double> junction_exit_distance_m;
  // The traffic light in lane sequence.
  voy::TrafficLight traffic_light_in_lane_sequence;
  // The direction of turn.
  pb::TurnMode turn_mode = pb::TurnMode::STRAIGHT;
  // The distance to the next turn in meters.
  std::optional<double> distance_to_next_turn_m;
  // The arc length of ego car on nominal path.
  double ego_arclength_m = 0.0;
  // The traffic participants with decision.
  std::vector<pb::TrafficParticipant> traffic_participants;
  // The occluded areas.
  std::vector<pb::OccludedAreaDebug> occluded_areas;
  // The max speed limit of ego lane.
  double current_lane_speed_limit_mps = 0.0;
  // The waypoint assist related visualization information.
  std::optional<WaypointAssistVisualInfo> waypoint_assist_visual_info;
  // The flag indicates whether the waypoint assist maneuver is available.
  pb::WaypointAvailability waypoint_availability = pb::NOT_AVAILABLE;
  // The map for static acc agents and its lateral clearance.
  std::unordered_map<std::int64_t, double> static_acc_agent_clearance_map;
};

// Generates customer monitor visualization information of a maneuver.
void GenerateCustomerMonitorVisualForManeuver(
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const speed::TrajectoryInfo& trajectory_info,
    const pb::ManeuverType& maneuver_type,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const voy::TrafficLights& traffic_light_detection,
    ManeuverCustomerMonitorVisual* maneuver_visual);

// Class CustomerMonitorVisualMsgGenerator generates the message for customer
// monitor visualization.
class CustomerMonitorVisualMsgGenerator {
 public:
  explicit CustomerMonitorVisualMsgGenerator(
      const pb::CustomerMonitorConfig& customer_monitor_config)
      : customer_monitor_config_(customer_monitor_config) {}

  // Updates visualization information for customer monitor.
  void UpdateCustomerMonitorVisual(
      const SpeedWorldModel& world_model,
      const BehaviorDecision& behavior_decision,
      const TrajectoryGenerationResult& trajectory_generation_result,
      const ManeuverCustomerMonitorVisual& maneuver_visual,
      const pb::PlanningDebug* planning_debug,
      pb::CustomerMonitorVisual* customer_monitor_visual);

 private:
  // Updates the traffic light attentions.
  void UpdateTrafficLightAttentions(
      const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list,
      const std::vector<const pnc_map::Lane*>& current_lanes,
      const math::geometry::PolylineCurve2d& nominal_path,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Update the brake-stopping over the line attentions.
  void UpdateBrakeStopOverLineAttention(
      bool will_cross_the_stop_line,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Updates the yield and pass attentions.
  void UpdateYieldAndPassAttentions(
      const std::vector<StopLine>& stop_directive,
      const pb::Trajectory& trajectory, double ego_speed,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Updates the nudge attentions.
  void UpdateNudgeAttentions(
      const math::geometry::PolylineCurve2d& nominal_path,
      const pb::Trajectory& trajectory,
      const std::vector<const pnc_map::Lane*>& current_lanes,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Updates the warning information.
  void UpdateWarningInformation(
      const SpeedWorldModel& world_model,
      const BehaviorDecision& behavior_decision,
      const TrajectoryGenerationResult& trajectory_generation_result,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Whether there is yield agent warning in the last cycle. This is used to
  // trigger pass agent warning.
  bool has_yield_warning_last_cycle_ = false;
  // The counter of pass agent warning. This is used to output pass agent
  // warning for some times.
  int pass_agent_warning_count_ = 0;
  // The last yield attention information. This is used for pass agent warning
  // to show which agent is passed.
  pb::YieldAttentionInfo last_yield_attention_info_;

  // Updates the cyclist or bicycle agents(collectively referred to here
  // as cyclist) which should be warning for driver HMI.
  void UpdateWarningNeededCyclistAgents(
      const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
      const RobotState& robot_state,
      pb::WarningCyclistInfo* warning_cyclist_info);

  // Determines if the cyclist needs a voice warning.
  // And updates cyclist_voice_warning_hists_last_cycle BTW.
  pb::VoiceWarningType GetCyclistVoiceWarningType(
      const PlannerObject& planner_object, const RobotStateSnapshot& ego_pose,
      std::vector<std::pair<int64_t, CyclistVoiceWarningHist>>*
          cyclist_voice_warning_hists);

  // Updates the stop lines.
  void UpdateStopLines(
      const math::Curve2d& nominal_curve,
      const math::geometry::PolylineCurve2d& right_lane_boundary,
      const std::vector<StopLine>& stop_directive,
      const WorldModel& world_model,
      pb::CustomerMonitorVisual* customer_monitor_visual);
  void SendReverseDrivingInfo(
      pb::ReverseDrivingAsker trigger_source,
      pb::CopilotReverseDrivingState state,
      pb::CustomerMonitorVisual* customer_monitor_visual);
  // Updates info of reverse driving in decoupled maneuver.
  void UpdateReverseDrivingInfo(
      const SpeedWorldModel& world_model,
      const BehaviorDecision& behavior_decision,
      const math::geometry::PolylineCurve2d& nominal_path,
      pb::CustomerMonitorVisual* customer_monitor_visual);
  // Update whether need warn the oncoming agent.
  void UpdateWhetherOncomingAgent(const SpeedWorldModel& world_model);

  void UpdateRiskAreaMaps(
      const std::vector<const pnc_map::Lane*>& current_lanes,
      const RobotStateSnapshot& ego_pose, pb::RiskAreaMaps* risk_area_maps);

  void SetFutureAreaDangerousLevelForOncomingAgent(
      const pb::RiskAreaMaps risk_area_maps,
      const PlannerObject& planner_object, const RobotStateSnapshot& ego_pose,
      pb::OncomingAgent* oncoming_agent);

  // Updates oncoming agent from rear when pullover.
  void UpdateOncomingAgentWhenReachedDestination(
      const SpeedWorldModel& world_model,
      const std::vector<const pnc_map::Lane*>& current_lanes,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  bool IsEgoInsideUTurn(const RobotStateSnapshot& ego_pose,
                        const std::vector<const pnc_map::Lane*>& current_lanes,
                        const double ego_arclength);

  // Updates customer-triggered immediate pull over for dhmi and phmi.
  void UpdateCustomerTriggeredImmediatePullOverInfo(
      const SpeedWorldModel& world_model,
      const BehaviorDecision& behavior_decision,
      pb::CustomerTriggeredImmediatePullOverInfo*
          customer_triggered_immediate_pullover_info);

  // Updates tidal flow lane related visualization info for ra operation.
  void UpdateTidalFlowLaneVisualizationInfo(
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Cached the total distance the ego will be in offroad u-turn.
  std::optional<double> offroad_uturn_total_distance_in_meter_;

  // Cached the distance the ego already in offroad u-turn.
  double offroad_distance_in_meter_ = 0;

  // Cached PullOverPullOutProgressState in last cycle.
  pb::PullOverPullOutProgressState
      pull_over_pull_out_progress_state_last_cycle_ =
          pb::PullOverPullOutProgressState::NOT_IN_PULLOVER_PULLOUT;

  // Cached decoupled corresponding maneuver type in last cycle.
  [[maybe_unused]] pb::ManeuverType last_maneuver_type_ =
      pb::ManeuverType::LANE_FOLLOW;

  // Cached if pull out success has been sent for one pull out.
  bool has_sent_pull_out_success_ = false;

  // Cached cycle count for contiguous publishing PullOutSuccess progress state.
  int pull_out_success_cycle_count_ = 0;

  // The timer used to accumulate the time can be trusted for nudge warning.
  int64_t nudge_warning_trust_time_ms_ = 0;

  // cached if ego has left nudged.
  bool cache_left_nudge_agent_ = false;

  // cached if ego has right nudged.
  bool cache_right_nudge_agent_ = false;

  // Cached reserve driving unfinished and stationary time in ms.
  int64_t unfinished_time_ms_ = 0;

  // Cached has reverse driving entered.
  [[maybe_unused]] bool has_entered_reverse_driving_ = false;

  // Cached whether reverse driving is preparing.
  bool is_preparing_reverse_driving_ = false;

  // Cached the copilot_assist_id.
  int64_t copilot_assist_id_ = -1;

  // Cached the distance reverse driving is preparing.
  double copilot_reverse_driving_distance_in_meter_ = 0;

  //// Cached whether oncoming agent warning is needed.
  bool is_oncoming_agent_warn_needed_ = false;
  // Cached every cyclist is in rear profile of ego.
  std::vector<std::pair<int64_t, CyclistVoiceWarningHist>>
      cyclist_voice_warning_hists_last_cycle_;

  pb::CustomerMonitorConfig customer_monitor_config_;

  // Indicates whether planner has searched an immediate pull over point when
  // phmi-triggered immediate pull over. Once an immediate pull over point has
  // been searched, the flag will keep true util immediate pull over is
  // cancelled.
  bool have_searched_immediate_pullover_point_ = false;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_UTILITY_CUSTOMER_MONITOR_CUSTOMER_MONITOR_H_
