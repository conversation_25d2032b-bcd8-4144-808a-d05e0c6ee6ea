syntax = "proto3";

package planner.pb;

import "planner_protos/remote_assist.proto";
import "voy_protos/math.proto";
import "voy_protos/trajectory.proto";
import "voy_protos/vehicle_model.proto";

// The order of crosswalk in the controlled lane.
// Next Available ID: 3
enum CrosswalkOrder {
  kUnknownOrderCrosswalk = 0;
  kFirstCrosswalk = 1;
  kSecondCrosswalk = 2;
};

// The right-of-way of ego car compared with other traffic participants.
// Next Available ID: 3
enum RightOfWayType {
  kUnknownRightOfWay = 0;
  kEgoHasRightOfWay = 1;
  kEgoHasNoRightOfWay = 2;
};

// The yield zones' order in a crosswalk along the nominal path.
//       ----------------------
//      /                      \
//     /     -----------        \  --> U-turn scene
//     |    /           \   ^   |
//     |    |           |   | --> nominal path
//     |    |           |   |   |
//  +------------------------------+
//  |  | --> second yield zone  |  |
//  |  |    |           |       |  | --> crosswalk
//  |  |    |           | --> first yield zone
//  +------------------------------+
//     |    |           |       |
//     |    |           |       |
// Next Available ID: 3
enum CrosswalkYieldZoneOrder {
  kUnknownYieldZoneOrder = 0;
  kFirstYieldZone = 1;
  kSecondYieldZone = 2;
}

// The stage of the yield analyzing process at a yield region.
// Each stage can apply its own yield handling strategy.
// Next Available ID: 3
enum YieldRegionStage {
  // At the PriorityCheck stage, the ego will take the most conservative
  // strategy. It can enter this stage when it doesn't block any of the
  // conflicting regions yet.
  kPriorityCheck = 0;
  // At the Waiting stage, the ego will take a less conservative strategy
  // than that taken by the PriorityCheck stage. It happens when the ego can
  // potentially block some conflicting region(s), while not all "yield-to"
  // agents committed in the last stage have passed.
  kWaiting = 1;
  // At the Go stage, the ego will take the most aggressive strategy. It
  // happens when the ego can potentially block some conflicting region(s), and
  // all agents it committed to yield to have passed.
  kGo = 2;
}

// The decision for object of interest in yield/crosswalk reasoners.
// Next Available ID: 4
enum YieldToTrajectoryDecision {
  kUnknownDecision = 0;
  kCautious = 1;
  kYield = 2;
  kIgnore = 3;
};

// The parameters for above decision.
// Next Available ID: 3
message CrosswalkObjectDecisionParam {
  // TODO(xiyi): make it a config instead of a constant in code.
  bool has_lightly_cautious_decay_factor = 1;
  double lightly_cautious_decay_factor = 2;
};

// The maneuver intention type of an object in crosswalk.
// Next Available ID: 3
enum ObjectManeuverIntentionType {
  // Object's intention is to cross crosswalk.
  kCross = 0;
  // Object's intention is to move along road side.
  kAlongRoadSide = 1;
  // Object's intention is vague.
  kUnknown = 2;
};

// The information of the blocked lane.
// Next Available ID: 3
message BlockedLane {
  int64 id = 1;
  double percentage = 2;
}

// The information of the invalid lane change source lane.
// Next Available ID: 6
message LaneChangeFeasibility {
  int64 id = 1;
  bool is_left_lane_change_feasible = 2;
  bool is_right_lane_change_feasible = 3;
  bool is_left_in_violable_ranges = 4;
  bool is_right_in_violable_ranges = 5;
}

// The lane graph search info.
// Next Available ID: 8
message LaneGraphSearchInfo {
  reserved 1, 2, 3;
  repeated BlockedLane blocked_lanes = 6;
  repeated LaneChangeFeasibility invalid_lane_change_source_lanes = 7;
  repeated int64 current_lanes = 4;
  repeated int64 congested_lanes = 5;
}

// Maneuver runnable status.
// Next Available ID: 3
enum ManeuverRunnableStatus {
  // Maneuver execution failed due to unexpected error.
  // In this status maneuver will NOT generate output such as directives
  // and evaluation results.
  kFailed = 0;
  // Maneuver is not in runnable condition.
  // In this status maneuver will NOT generate output such as directives
  // and evaluation results.
  kNotRunnable = 1;
  // Maneuver is runnable. All output including directives and evaluation
  // results should be generated for the maneuver.
  kRunnable = 2;
};

// The traffic light control state.
// Next Available ID: 2
enum TrafficLightControlState {
  kTrafficLightControlStop = 0;
  kTrafficLightControlGo = 1;
}

// Param for traffic light reasoner.
// Next Available ID: 18
message TrafficLightReasonerParam {
  reserved 13 to 14;
  reserved "farthest_driving_arclength_within_red_color",
      "extreme_driving_arclength_within_red_color";

  // The robot's front bumper's arc length on the nominal path.
  double front_bump_arclength = 1;
  // Whether there exist conflicting lanes.
  bool has_conflicting_lane = 2;
  // The nearest conflicting lane's arc length on the nominal path.
  double nearest_conflicting_lane_arclength = 3;
  // The farthest stop point's arc length on the nominal path.
  double farthest_stop_point_arclength = 4;
  // The intersection passing point's arc length on the nominal path.
  double intersection_passing_point_arclength = 5;
  // The robot's comfortable stopping arc length.
  double comfortable_stopping_arclength = 6;
  // The robot's hard stopping arclength.
  double hard_stopping_arclength = 7;
  // The farthest driving arclength before traffic light turns into yellow.
  double farthest_driving_arclength = 8;
  // The farthest driving arclength before traffic light turns into red.
  double extreme_driving_arclength = 9;
  // The remaining time of the green color.
  int64 green_color_remaining_time = 10;
  // The remaining time of the yellow color.
  int64 yellow_color_remaining_time = 11;
  // The farthest conflicting lane's arc length on the nominal path.
  double farthest_conflicting_lane_arclength = 12;
  // Whether there exist high priority conflicting lanes.
  bool has_high_priority_conflicting_lane = 15;
  // The minimum start arclength of high priority conflicting lanes.
  double min_high_priority_conflicting_lane_start_arclength = 16;
  // The remaining time before red color.
  int64 remaining_time_before_red_color = 17;
}

message StopLineType {
  // Next Available ID: 26
  enum Enum {
    reserved 13, 15, 22;
    reserved "SUPER_NUDGE", "SUPER_NUDGE_ABORT", "WAYPOINT_ASSIST_CONFIRM";
    UNKNOWN_TYPE = 0;  // default.
    TRAFFIC_LIGHT = 1;
    CROSSWALK = 2;
    STOP_SIGN = 3;
    YIELD = 4;
    DESTINATION = 5;
    END_OF_LANE_SEQUENCE = 6;
    KEEP_CLEAR = 7;
    LANE_BLOCKAGE = 8;
    PULL_OUT = 9;
    OCCLUSION = 10;
    TARGET_POSE = 11;
    JUKE_SUPPRESSION = 12;
    DISENGAGEMENT_ZONE = 14;
    CONSTRUCTION_ZONE = 16;
    EMERGENCY_BRAKE = 17;
    PULL_OVER_SUCCESS = 18;
    ENGAGE = 19 [ deprecated = true ];
    REVERSE_DRIVING_DESTINATION = 20;
    GEAR_SWITCH = 21;
    ENGAGE_WAITING_STEERING_LATCHED = 23;
    ENGAGE_WAITING_ASSIST = 24;
    INTER_MANEUVER = 25;
  }
}

message SpeedZoneType {
  // Next Available ID: 32
  enum Enum {
    reserved 9, 12, 16;
    reserved "SPEED_ZONE_SUPER_NUDGE";
    SPEED_UNKNOWN = 0;  // default
    SPEED_ZONE_CROSSWALK = 1;
    SPEED_ZONE_TRAFFIC_LIGHT = 2;
    SPEED_ZONE_TURNING = 3;
    SPEED_ZONE_JUNCTION = 4;
    SPEED_ZONE_YIELD = 5;
    SPEED_ZONE_NUDGE = 6;
    SPEED_LIMIT = 7;
    SPEED_ZONE_BUS_STOP = 8;
    SPEED_ZONE_CAUTIOUS_UNKNOWN_OBJECT = 19;
    SPEED_ZONE_CAUTIOUS_PED = 20;
    SPEED_ZONE_CAUTIOUS_LARGE_VEHICLE = 21;
    SPEED_ZONE_CAUTIOUS_OPEN_DOOR = 22;
    SPEED_ZONE_CAUTIOUS_SCENE_MULTIPLE_PARKED_CAR = 23;
    SPEED_ZONE_CAUTIOUS_SCENE_BUS_BULB = 24;
    SPEED_ZONE_CAUTIOUS_SCENE_NON_YIELD_BACKUP_PREDICTION = 25;
    SPEED_ZONE_CAUTIOUS_SCENE_PEDESTRIAN_UNCERTAIN_TRAJECTORY = 26;
    SPEED_ZONE_CAUTIOUS_NUDGE_VEHICLE = 27;
    SPEED_ZONE_OCCLUSION = 10;
    SPEED_ZONE_DEFAULT = 11;
    SPEED_ZONE_CURVATURE = 13;
    SPEED_ZONE_UNCERTAIN_PREDICTION = 14;
    SPEED_ZONE_PULL_OVER = 15;
    SPEED_ZONE_BACKUP_CUT_IN_PREDICTION = 17;
    SPEED_ZONE_SQUEEZE = 18;
    SPEED_ZONE_DESTINATION = 28;
    SPEED_ZONE_ENGAGE = 29;
    SPEED_ZONE_NUDGE_DATA_DRIVEN = 30;
    SPEED_ZONE_SPECIFIC_MANEUVER = 31;
  }
}

// Next Available ID: 2
enum SpeedZoneMode {
  // The hard speed zone mode, with high cost.
  SPEED_ZONE_HARD_MODE = 0;
  // The hard speed zone mode, with low cost.
  SPEED_ZONE_SOFT_MODE = 1;
}

// Next Available ID: 5
message SpeedZoneMetadata {
  // The speed zone type.
  SpeedZoneType.Enum type = 1;
  // The speed zone mode.
  SpeedZoneMode mode = 2;
  // Expected accel for the speed zone.
  double accel = 3;
  // Expected decel for the speed zone.
  double decel = 4;
}

// The type of turn signal.
message TurnSignalType {
  // Next Available ID: 17
  enum Enum {
    reserved 5;
    reserved "kSuperNudge";
    kUnknown = 0;
    // Turn signal triggered by lane change.
    kLaneChange = 1;
    // Turn signal triggered by normal turn.
    kNormalTurn = 2;
    // Turn signal triggered for pull out.
    kPullOut = 3;
    // Turn signal triggered for pull over.
    kPullOver = 4;
    // Turn signal triggered for engage.
    kEngage = 6;
    // Turn signal triggered for waypoint assist.
    kWaypointAssist = 7;
    // Turn signal triggered for cross-lane nudge.
    kXLaneNudge = 8;
    // Turn signal triggered for merging back to origin lane from outside.
    kMergeBack = 9;
    // Turn signal triggered for roundabout.
    kRoundAbout = 10;
    // Turn signal triggered for merge/fork lane.
    kMergeFork = 11 [ deprecated = true ];
    // Turn signal triggered for side/main road or exclusive right turn.
    kMainSide = 12;
    // Turn signal triggered by lane change abort.
    kLaneChangeAbort = 13;
    // Turn signal triggered for merge/fork behaviors in junction scenes.
    kMergeForkInJunction = 14;
    // Turn signal triggered for merge/fork behaviors in non junction scenes.
    kMergeForkInNonJunction = 15;
    // Turn signal triggered by RA light assist.
    kLightAssist = 16;
  }
}

// Phases of the lane change.
// Next Available ID: 5
enum LaneChangePhase {
  // The ego car is not in lane change maneuver.
  kIdle = 0;
  // The ego car is in lane change maneuver but hasn't reached the start point
  // of the lane change corridor.
  kPreparing = 1;
  // The ego car has passed the start point of the lane change corridor and
  // is performing the actual lane change.
  kChanging = 2;
  // The ego car has entered the target lane.
  kFinishing = 3;
  // The ego car has entered the target lane and has aligned with lane center.
  kFinished = 4;
}

// Lane change corridor.
// Next Available ID: 6
message LaneChangeCorridor {
  // Nominal path of a lane change corridor.
  math.pb.Curve2d nominal_path = 1;
  // Left boundary.
  math.pb.Curve2d left_boundary = 2;
  // Right boundary.
  math.pb.Curve2d right_boundary = 3;
  // Whether lane change corridor is valid.
  bool is_valid = 4;
  // Whether the corridor is static for the last chance.
  bool is_static = 5;
}

// The plan initial state type.
message PlanInitType {
  // Next Available ID: 5
  enum Enum {
    COLDSTART = 0;
    // kLatchSteering is a special type of ColdStart while keeping the
    // steering latched.
    LATCH_STEERING = 1 [ deprecated = true ];
    NORMAL_STITCHING = 2;
    LATCH_STATIONARY = 3;
    // DRY_STEERING is a special behavior for the decoupled maneuver. In
    // DRY_STEERING mode, modify the planning initial state's curvature and Eta
    // to get ego steering the wheel-angle without longitudinal motion.
    DRY_STEERING = 4;
  }
}

// The plan init state.
// Next Available ID: 9
message PlanInitState {
  // Next Available ID: 4
  message PlanInitErrorState {
    // Positive when plan init pose is ahead the ego actual pose,
    // negative when plan init pose is behind.
    double longitudinal_error_m = 1;
    // Positive when plan init pose is on the right of ego actual
    // pose, negative when plan init pose is on the left.
    double lateral_error_m = 2;
    // Positive when plan init pose is on the counter-clockwise of ego
    // actual pose, negative when plan init pose is on the clockwise.
    double heading_error_rad = 3;
  }
  // The replanning start time.
  int64 start_time = 1;
  // The replanning start state.
  vehicle_model.pb.CarState state = 2;
  // The plan init state type.
  PlanInitType.Enum type = 3;
  // The immutable trajectory.
  repeated TrajectoryPose immutable_poses = 4;
  // The pose height.
  double z = 5;
  // The pose curvature.
  double curvature = 6;
  // The curvature rate over arclength.
  double pinch = 7;
  optional PlanInitErrorState error_state = 8;
}

// CrosswalkReasonerStopLineMetadata represents the stop line metadata for
// crosswalk reasoner's stopping constraints. NEXT Available ID: 6
message CrosswalkReasonerStopLineMetadata {
  int64 crosswalk_id = 1;
  // The yield zone order along nominal path in a crosswalk.
  CrosswalkYieldZoneOrder yield_zone_order = 2;
  // The id of agent causing this stop line.
  int64 agent_id = 3;
  // The id of trajectory causing this stop line.
  int64 trajectory_id = 4;
  // The preliminary ignore decision issued by the yield reasoner.
  YieldPreliminaryIgnoreDecision preliminary_ignore_decision = 5;
}

// The preliminary yield ignoring decision type.
// Next Available ID: 3
message YieldPreliminaryIgnoreDecision {
  enum Decision {
    // The corresponding constraints should not be ignored.
    SHOULD_NOT_IGNORE = 0;
    // The downstream st-planner can make further decision on whether to
    // ignore the corresponding constraints.
    IGNORE_ALLOWED = 1;
    // The downstream st-planner should ignore the corresponding constraints
    // right away.
    MUST_IGNORE = 2;
  }
  Decision decision = 1;
  enum DecisionReason {
    NOT_APPLIED = 0;
    IS_LARGE_VEHICLE = 1;
    HAS_NO_ENCROACHMENT = 2;
    IS_AGENT_CLOSER_TO_ENCROACHMENT = 3;
    IS_AGENT_TOO_CLOSE = 4;
    HAS_EGO_ENCROACHED_THE_CONFLICTING_LANE = 5;
    IS_AGENT_TOO_FAST = 6;
    AGENT_CAN_NOT_STOP_SMOOTHLY = 7;
    PASS_PRELIMINARY_IGNORING_CHECKS = 8;
    NOT_IN_GO_STAGE = 9;
    AGENT_BEHIND_EGO = 10;
    AGENT_BEHIND_EGO_WITH_UNSAFE_HEADING = 11;
    OVERLAP_WITH_SAFE_HEADING = 12;
    OVERLAP_WITH_UNSAFE_HEADING = 13;
    AGENT_AHEAD_EGO = 14;
    EGO_FAR_TO_SECOND_CROSSWALK = 15;
    EGO_NOT_BEYONG_FIRST_YIELD_ZONE = 16;
  }
  DecisionReason reason = 2;
}

// YieldReasonerStopLineMetadata represents the stop line metadata for
// yield reasoner's stopping constraints. NEXT Available ID: 10
message YieldReasonerStopLineMetadata {
  reserved 6, 7;
  reserved "is_ignore_allowed";
  int64 associated_lane_id = 1;
  // The id of the yielding object.
  int64 yielding_object_id = 2;
  // The id of the associated trajectory.
  int64 trajectory_id = 3;
  // The yield stage at which we generate the stop line.
  YieldRegionStage yield_region_stage = 4;
  // Should st planner consider this stop line while costing or extracting
  // a decision from this constraint
  bool is_effective = 5;
  // If the corresponding conflicting lane is a crossing one.
  bool is_on_crossing_lane = 8;
  // The preliminary ignore decision issued by the yield reasoner.
  YieldPreliminaryIgnoreDecision preliminary_ignore_decision = 9;
}

// TrafficLightReasonerStopLineMetadata represents the stop line metadata for
// traffic light reasoner's stopping constraints. NEXT Available ID: 2
message TrafficLightReasonerStopLineMetadata { int64 controlled_lane_id = 1; }

// The generative metadata for stop line. NEXT Available ID: 4
message StopLineMetadata {
  oneof stop_line_metadata {
    CrosswalkReasonerStopLineMetadata crosswalk_reasoner_stop_line_metadata = 1;
    YieldReasonerStopLineMetadata yield_reasoner_stop_line_metadata = 2;
    TrafficLightReasonerStopLineMetadata
        traffic_light_reasoner_stop_line_metadata = 3;
  }
}

// Define the ID of a lane change instance.
// Next Available ID: 3
message LaneChangeInstanceId {
  int64 src_lane_id = 1;
  int64 dst_lane_id = 2;
}

// Urgency type for a planned lane change instance.
// Next Available ID: 2
enum UrgencyType {
  // No special urgency type.
  kNonUrgencyType = 0;
  // Not suggest to perform early lane change due to special not drivable lane.
  kNotEarly = 1;
}

// Supplimental instance info.
// Next Available ID: 4
message InstanceInfo {
  // Initiator types that request this lane change instance.
  // Next Available ID: 9
  // TODO(wenyue, elliotsong): Some initiators did not define a type yet, like
  // by sepcific type's proposal. Should classify later.
  enum Initiator {
    kUnknownInitiator = 0;
    // Instance's sequence is generated by search, like optimal LF/LC sequence
    // and alternative LF sequence.
    kRouting = 1;
    kEarlyLaneChange = 2;
    kImmediateLaneChange = 3;
    kImmediatePullOver = 4;
    kPreviewRoute = 5;
    kRegionalPath = 6;
    kUnstuckLaneChange = 7;
    kLaneChangeProposal = 8;
  }

  // Urgency level of this lane change.
  // Next Available ID: 3
  enum UrgencyLevel {
    kUnknownUrgencyLevel = 0;
    kHard = 1;
    kSoft = 2;
  }

  Initiator initiator = 1;
  UrgencyLevel urgency_level = 2;
  repeated LaneChangeIncentive incentives = 3;
}

// The possible incentives for a lane change plan on lane sequence.
// Next Avaliable ID: 6
message LaneChangeIncentive {
  // The lane change incentive type.
  // Next Avaliable ID: 12
  enum IncentiveType {
    // No exact incentive found.
    kNone = 0;

    //
    // Proximity-based incentives,
    // which focus on how far the incentives are from the lane change start.
    //
    // This lane change is required to follow the regional path, e.g. a lane
    // change right to take the right turn at the junction.
    kRoute = 1;
    // This lane change is to avoid a hard block.
    kHardBlock = 2;
    // This lane change is to avoid a soft block.
    kSoftBlock = 3;
    // This lane change is to avoid a conflict merge lane.
    kConflictMergeLane = 6;
    // This lane change is to avoid a bus lane.
    kBusLane = 7;
    // This lane change is to avoid a bay style bus bulb.
    kBusBulb = 8;
    // This lane change is to avoid a direct bus bulb.
    kDirectBusBulb = 10;

    //
    // Coverage-based incentives,
    // which focus on how long the ego might drive under the conditions of these
    // incentives.
    //
    // This lane change is to avoid driving long on the leftmost lanes.
    kLeftmostLane = 4;
    // This lane change is to avoid driving long on the rightmost lanes.
    kRightmostLane = 5;

    //
    // Hybrid incentives,
    // which focus both on the distance and the length of the incentive.
    //
    // This lane change is to avoid a congested lane.
    kCongestion = 9;

    //
    // This lane change is to avoid a potential effect cause by a bus.
    kBusPotentialEffect = 11;
  }
  // The lane change urgency level.
  // Next Avaliable ID: 3
  enum UrgencyLevel {
    // This lane change is compulsory, otherwise unstuck or reroute would be
    // required.
    kL0 = 0;
    // This lane change is for better efficiency.
    kL1 = 1;
    // No direct incentive found for this lane change.
    kL2 = 2;
  }
  // The incentive type.
  IncentiveType type = 1;
  // The urgency level.
  UrgencyLevel urgency_level = 2;
  // The distance from the lane change start to the first occurrence of this
  // incentive.
  // Optional for proximity-based incentives.
  optional double dist_from_source = 4;
  // The total length that this incentive occupies on the conterfactual
  // sequence.
  // Optional for coverage-based incentives.
  optional double total_length = 5;

  math.pb.Range range_m = 3 [ deprecated = true ];
}

// Define a lane change instance.
// Next Available ID: 11
message LaneChangeInstance {
  LaneChangeInstanceId id = 1;
  // The source lane sequence which starts from source lane.
  repeated int64 source_lane_sequence = 2;
  // The target lane sequence which starts from target lane.
  repeated int64 target_lane_sequence = 3;
  // The arclength is related to source lane.
  double lc_start_arc_length = 4;
  LaneChangeMode lane_change_direction = 5;
  // Routing suggested urgency type of this lane change instance.
  UrgencyType urgency_type = 6;
  // The target region nominal path.
  math.pb.Curve2d target_region_nominal_path = 7;
  // The target region left boundary.
  math.pb.Curve2d target_region_left_boundary = 8;
  // The target region right boundary.
  math.pb.Curve2d target_region_right_boundary = 9;
  // Info about this lane change instance
  InstanceInfo instance_info = 10;
}

// Detailed info for a lane change request, from global route cost map and
// regional map's topological info.
// Next Available ID: 6
message LaneChangeInstanceDetail {
  // Longest lane change distance for current lane change.
  double valid_lane_change_distance = 1;
  // Total lane change times from source lane to next turn junction.
  int32 total_lane_change_times_to_next_turn_junction = 2;
  // Total lane change times from target lane to next turn junction.
  int32 total_lane_change_target_times_to_next_turn_junction = 5;
  // Total lane change distance from source lane to next turn junction.
  double total_lane_change_distance_to_next_turn_junction = 3;
  // Total junction numbers from source lane to next turn junction.
  int32 junction_num_before_next_turn_junction = 4;
}

// Enum OvertakingRelativeStatus is used for the state machine of finding
// overtaking agent.
// Next Available ID: 4
enum OvertakingRelativeStatus {
  kNotOvertaking = 0;
  kBehind = 1;
  kBeside = 2;
  kAhead = 3;
}

// Data structure for the profile of in lane corridor.
// Next Available ID: 12
message InLaneCorridorProfile {
  // timestamp stores the time for the corresponding corridor. unit is ms.
  int64 timestamp = 1;
  // time_step stores the time index within kNumPlanningSteps for dynamically
  // nudge corridor.
  int64 time_step = 2;
  // smoothed_distance stores the shift distance for in lane corridor. unit is
  // m.
  double smoothed_distance = 3;
  // object_id stores the id of the agent for the corresponding corridor.
  int64 object_id = 4;
  // side indicates the side of the corridor.
  math.pb.Side side = 5;
  // Is an agent that is overtaking from behind and next to ego.
  bool is_overtaking_agent = 6;
  // Is the snapshot in squeeze range.
  bool is_in_squeeze_range = 7;
  // Range of the profile.
  double start_pos_arclength_m = 8;
  double end_pos_arclength_m = 9;
  // The encroachment for the object.
  double lateral_encroachment_m = 10;
  // Cross boundary violation size.
  double lane_boundary_buffer_distance_m = 11;
}

// Direction for immediate lane change.
message ImmediateLaneChangeDirection {
  // Next Available ID: 4
  enum Enum {
    // kEither is deprecated and users must specify direction. Otherwise, regard
    // that users request for both directions.
    kEither = 0 [ deprecated = true ];
    kLeft = 1;
    kRight = 2;
    kBoth = 3;
  }
}

// Indicates the status of waypoint based assist maneuver's path generation.
message AssistPathGenerationStatus {
  // Next Available ID: 3
  enum Enum {
    kNotFinished = 0;
    kFailed = 1;
    kSucceeded = 2;
  }
}

// Indicates the phase of waypoint and light weight based assist maneuver.
message AssistManeuverPhase {
  // Next Available ID: 6
  enum Enum {
    // Planner hasn't entered waypoint based assist maneuver.
    kIdle = 0;
    // Planner is in waypoint based assist maneuver & waiting for waypoints
    // given by ops.
    kWaitingPoints = 1;
    // Planner has received waypoints given by ops but path hasn't been
    // generated.
    kPreparingPath = 2;
    // Planner has generated a path & waiting for ops manually confirm of
    // following the path or not.
    kWaitingConfirm = 3;
    // The path generated by planner has been approved by ops & ego is following
    // the waypoint based path to get out of stuck.
    kFollowingPath = 4;
    // Planner has received exit command from ops and check whether exit
    // waypoint immediately.
    kExitReasoning = 5;
  }
}

// The state of assist task.
message AssistTaskState {
  // Next Available ID: 5
  enum Enum {
    kNone = 0;
    kWaiting = 1;
    kExecuting = 2;
    kAborting = 3;
    kFinished = 4;
  }
}

// The state of traffic light.
// Next Available ID: 8
enum TrafficLightState {
  kUnknownLight = 0;
  kGreenLight = 1;
  kGreenFlashingLight = 2;
  kYellowLight = 3;
  kYellowFlashingLight = 4;
  kRedLight = 5;
  kBrokenLight = 6;
  kOccludedLight = 7;
}

// Next Available ID: 5
message ReverseDrivingInfo {
  // The current state of reverse driving manager.
  ReverseDrivingState.Enum state = 1;
  // The ground point of reverse driving destination for reasoning.
  math.pb.Point2d stop_point = 2;
  // The source of trigger of this reverse driving action.
  ReverseDrivingAsker trigger_source = 3;
  // The guidance curve for reverse
  optional math.pb.Curve2d guidance_curve = 4;
}

// The state of reverse driving function.
message ReverseDrivingState {
  // Next Available ID: 4
  enum Enum {
    // The reverse driving is not triggered (planning is forward or parking).
    kIdle = 0;
    // The reverse driving mode is triggered, then waiting for 'enter' command
    // by ops.
    kPrepare = 1;
    // The reverse driving is action.
    kAction = 2;
    // Ego is Immediately stopping for exit reverse driving.
    kStoppingForExit = 3;
  }
}

// The type of appending a new map element to a sequence of map elements, which
// collectively represent the route of the agent predicted trajectory.
message AgentPredictedTrajectoryRouteAssociationType {
  // Next Available ID: 6
  enum Enum {
    kUnknown = 0;
    // The new element is either the same as the last one in the sequence or its
    // recursive successor.
    kContented = 1;
    // The new element is the brother lane of the last one in the sequence, and
    // it's not in junction.
    kBrotherhood = 2;
    // The new element is under the same section as the last one in the
    // sequence.
    kNeighborhood = 3;
    // The new element is in a succeeding section of the last one in the
    // sequence, but not a successor.
    kSuperNeighborhood = 4;
    // The new element is in junction and it is neighborhood or
    // super-neighborhood of the old element.
    kSoftNeighborhood = 5;
  }
}

message UnstuckDetourInfo {
  enum DetourType {
    // Default value.
    kDetourTypeNone = 0;
    // Unstuck left/right with jumpout.
    kDetourTypeJumpOut = 1;
    // Unstuck left/right with xlane.
    kDetourTypeXLane = 2;
  }

  enum DetourUnavailableReason {
    // No reason.
    kDetourUnavailableNone = 0;
  }
}

message UnstuckDetourAvailableBase {
  bool is_detour_available = 1;
  UnstuckDetourInfo.DetourUnavailableReason unavailable_reason = 2;
}

message UnstuckDetourAvailableState {
  UnstuckDetourAvailableBase available_state_left = 1;
  UnstuckDetourAvailableBase available_state_right = 2;
}

// The command of each remote assist tasks.
// Next Available ID: 8
message AssistTaskCommand {
  optional OperationCommand.Enum waypoint_command = 1;
  optional DirectionKeyCommand.DirectionType direction_type = 2;
  optional AssistFeedback.AssistFeedbackType feedback_type = 3;
  optional StuckSceneResponse stuck_scene_response = 4;
  optional StartRemoteAssistResponse start_remote_assist_response = 5;
  optional RemoteAssistTaskExitResponse remote_assist_task_exit_response = 6;
  optional RemoteAssistConnectionResponse remote_assist_connection_response = 7;
}

// This message displays the original state of different remoet assist task.
// If task state mapped to wrong AssistTaskState, this message helps to find.
// Next Available ID: 4
message AssistTaskOriginalState {
  optional AssistManeuverPhase.Enum waypoint_state = 1;
  optional ReverseDrivingState.Enum reverse_driving_state = 2;
  optional RequestConfirmStatus.Enum request_confirm_status = 3;
}

// Information of assist task scheduler.
// Next Available ID: 8
message RemoteAssistTask {
  AssistTaskCommand task_command = 1;
  AssistTaskOriginalState task_original_state = 2;
  AssistTaskState.Enum task_state = 3;
  int64 task_priority = 4;
  int64 task_timestamp = 5;
  bool should_exit = 6;
  bool should_unstuck = 7;
}

// The state of remote assist signal.
// Next Available ID: 3
message RemoteAssistConnectionSignal {
  // The timestamp that planner received the signal.
  int64 planner_receive_timestamp = 1;
  optional RemoteAssistConnectionResponse remote_assist_connection_response = 2;
}
