#ifndef ONBOARD_PLANNER_UTILITY_TRAJECTORY_MANIPULATION_TRAJECTORY_MANIPULATION_UTIL_H_
#define ONBOARD_PLANNER_UTILITY_TRAJECTORY_MANIPULATION_TRAJECTORY_MANIPULATION_UTIL_H_

#include <tuple>
#include <unordered_map>
#include <vector>

#include "geometry/model/oriented_box.h"
#include "math/range.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/predicted_trajectory_interpolator.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_protos/vehicle_model.pb.h"

namespace planner {

// Struct CollisionSnapshotInfo defines the collision snapshot's information.
// TODO(waylon): Move to collision check utility if possible.
struct CollisionSnapshotInfo {
  CollisionSnapshotInfo(
      int64_t timestamp_in,
      const math::geometry::OrientedBox2d& ego_bounding_box_in,
      const math::geometry::OrientedBox2d& agent_bounding_box_in)
      : timestamp(timestamp_in),
        ego_bounding_box(ego_bounding_box_in),
        agent_bounding_box(agent_bounding_box_in) {}
  // The timestamp that a predicted collision happened.
  int64_t timestamp;
  // Ego bounding box at collision event;
  math::geometry::OrientedBox2d ego_bounding_box;
  // Agent bounding box at collision event;
  math::geometry::OrientedBox2d agent_bounding_box;
};

// Gets the bounding box of the vehicle at a pose in the trajectory. Note that
// the pose of planned trajectory centers at wheelbase center. This function
// return the bounding box centering at the geometric center of the ego's
// contour.
math::geometry::OrientedBox2d GetBoundingBoxFromTrajectoryPose(
    const pb::TrajectoryPose& pose,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement);

// Checks if the ego's bounding box collides with an interpolated predicted
// trajectory.
std::tuple<int64_t, bool> HasCollisionWithPredictedTrajectories(
    int64_t timestamp, const math::geometry::OrientedBox2d& ego_bounding_box,
    const std::vector<PredictedTrajectoryInterpolator>&
        interpolated_trajectories);

// Checks if the trajectory collides with tracked objects and background
// objects.
std::tuple<int64_t, bool> HasCollisionWithObjects(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::Trajectory& trajectory,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const math::Range<int>& evaluation_range);

// Gets the collision snapshot information from one predicted trajectory.
std::optional<CollisionSnapshotInfo> GetCollisionInfoFromPredictedTrajectory(
    const pb::Trajectory& last_trajectory,
    const std::vector<math::geometry::OrientedBox2d>& last_traj_bounding_boxes,
    const PredictedTrajectoryInterpolator& agent_interpolated_trajectory);

}  // namespace planner
#endif  // ONBOARD_PLANNER_UTILITY_TRAJECTORY_MANIPULATION_TRAJECTORY_MANIPULATION_UTIL_H_
