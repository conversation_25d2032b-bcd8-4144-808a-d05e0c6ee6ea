
#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_MAIN_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_MAIN_H_

#ifdef __cplusplus
extern "C" {
#endif

// initialize lateral function output
extern void LatCtrlFct_Init(void);

// lateral signals input
extern void LSI_10ms_Runnable(void);

// vehicle data processing
extern void VDP_60ms_Runnable(void);

// boundary data processing
extern void BDP_60ms_Runnable(void);

// function state assessment
extern void FSA_60ms_Runnable(void);

// lateral control
extern void LCT_10ms_Runnable(void);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_MAIN_H_
