#include "customer_monitor_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

CustomerMonitorObserver::CustomerMonitorObserver(
    CustomerMonitorCallback callback)
    : callback_(std::move(callback)) {}

bool CustomerMonitorObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kCustomerMonitor);
}

std::shared_ptr<google::protobuf::Message> CustomerMonitorObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto cm_visual = std::make_shared<planner::pb::CustomerMonitorVisual>();
  if (!cm_visual->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error CustomerMonitorVisual message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (callback_) {
    callback_(cm_visual, data_channel);
  }
  return cm_visual;
}

}  // namespace remote_assist
