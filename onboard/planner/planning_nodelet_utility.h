#ifndef ONBOARD_PLANNER_PLANNING_NODELET_UTILITY_H_
#define ONBOARD_PLANNER_PLANNING_NODELET_UTILITY_H_

#include <map>
#include <memory>
#include <vector>

#include "planner/behavior/util/lane_sequence/lane_sequence_generator_utility.h"
#include "planner/planning_topics.h"
#include "planner/world_model/snapshot/snapshot.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/customer_monitor_visual.pb.h"
#include "planner_protos/message_metadata.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/trajectory_generator_debug.pb.h"
#include "routing/engine/route_utils.h"
#include "routing/engine/routing_edge_filter.h"
#include "voy_protos/issue_tag_change.pb.h"

namespace planner {
namespace planning_vnode {

// Generates the planning lane sequences proto.
pb::PlanningLaneSequence GeneratePlanningLaneSequence(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const pb::AbnormalSequenceSignal& abnormal_sequence_signal,
    const pb::ExtraPlannerSignals::PlannerBehaviorSignal
        planner_behavior_signal);

std::optional<pb::PlanningLaneSequence> GenerateDowngradePlanningLaneSequence(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const pb::AbnormalSequenceSignal& abnormal_sequence_signal,
    const pb::ExtraPlannerSignals::PlannerBehaviorSignal
        planner_behavior_signal);

// Updates additional planning debug information.
// TODO(tianyugu): shall we remove this function, and blend all these settings
// into where they are used.
void UpdateDebugData(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const SpeedWorldModel& speed_world_model,
    const TrajectoryGenerationResult& selected_result, double cycle_time,
    double execution_time, pb::WaypointAvailability waypoint_availability,
    pb::PlanningDebug* planning_debug);

// Writes the planning configuration into the ros param.
void WritePlanningConfigToRosParam(const pb::PlannerConfig& planner_config);

// Returns true if the engage maneuver should be enabled.
bool ShouldEnableEngageWarning();

// Creates slim route solution with necessary fields.
routing::pb::RouteSolution CreateSlimRouteSolution(
    const routing::pb::RouteSolution& route_solution);

// Returns true if new route lanes and last route lanes are same.
bool AreSameGlobalRoutes(
    const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
        new_route_lanes,
    const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
        last_route_lanes);

// Returns true if should accept new global route directly.
bool ShouldAcceptReplanRouteDirectly(
    const routing::pb::RouteStatus& route_status);

// Returns true if should reject new global route lanes.
bool ShouldRejectNewRouteLanesForReplan(
    const routing::pb::RouteStatus& route_status,
    const planner::RegionalPath& regional_path);

// Updates debug information for routing constraint.
void UpdateRoutingConstraintInfoDebug(
    const routing::pb::ConstraintInfo& constraint_info,
    bool is_from_routing_topic,
    planner::pb::RoutingConstraintInfoDebug* routing_constraint_info_debug);

// Posts rt event for replan route query type.
void PostRTEventForReplanRouteQueryType(
    const routing::pb::RouteQuery& replan_route_query);

// If ego has reached destination, returns the corresponding command purpose if
// route completion should be published and return nullopt if route completion
// shoule not be published.
std::optional<routing::pb::CommandPurpose>
GetCommandPurposeWhenHasReachedDestination(
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    pb::ImmediatePullOverTriggerSource immediate_pull_over_source,
    double dist_to_destination, bool has_reached_destination,
    bool is_immediate_pull_over_triggered_by_mrc);

// Generates planner issue tag change info.
std::optional<voy::IssueTagChangeInfo> GeneratePlannerIssueTagChangeInfo(
    TriggerStateChangeType trigger_state_change_type,
    int64_t current_timestamp);

// Checks if the proposed route solution should be rejected.
bool ShouldRejectProposedRoute(bool is_last_optimal_route_accepted_by_planner,
                               int64_t current_timestamp,
                               int64_t first_receive_timestamp);

bool IsLastOptimalRouteAcceptedByPlanner(
    const pb::WorldModelSeed& previous_world_model_seed,
    const pnc_map::JointPncMapService& joint_pnc_map_service);

bool IsEgoInJunction(const pb::WorldModelSeed& previous_world_model_seed,
                     const pnc_map::PncMapService* pnc_map_service);

using InputMsgTsMap = std::map<PlannerSubTopic, std::optional<int64_t>>;
planner::pb::MessageMetadata ComposeMessageMetadata(
    int64_t message_metadata_timestamp,
    const InputMsgTsMap& input_publish_timestamps_in_ns, bool input_topic_only,
    bool drop_current_snapshot_flag, int64_t message_sequence_number);
}  // namespace planning_vnode
}  // namespace planner

#endif  // ONBOARD_PLANNER_PLANNING_NODELET_UTILITY_H_
