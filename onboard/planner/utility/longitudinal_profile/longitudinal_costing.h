#ifndef ONBOARD_PLANNER_UTILITY_LONGITUDINAL_PROFILE_LONGITUDINAL_COSTING_H_
#define ONBOARD_PLANNER_UTILITY_LONGITUDINAL_PROFILE_LONGITUDINAL_COSTING_H_

#include "control_util/costing_base.h"
#include "planner/utility/primitive/primitive_definition/longitudinal_primitive.h"
#include "vehicle_model/utils.h"

namespace control_util {

// Trait for LongitudinalFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kLongitudinal> {
  using StateControlSpecs = vehicle_model::LongitudinalModel;

  using PbState = vehicle_model::pb::LongitudinalState;
  using PbControl = vehicle_model::pb::LongitudinalControl;

  using PbFeatureType = control_util::pb::LongitudinalFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = control_util::pb::LongitudinalFeatureParameter;
  using PbCostingTerm = control_util::pb::LongitudinalCostingTerm;
  using StepwisePrimitives = std::monostate;

  using PbSolution = planner::pb::LongitudinalSolution;
  using PbWarmstarterDebug = planner::pb::LongitudinalWarmstarterDebug;
  using PbSolverDebug = planner::pb::LongitudinalSolverDebug;
  using PbSolverInterationDebug = planner::pb::LongitudinalSolverIteration;
  using PbWarmstartType = planner::pb::LongitudinalWarmstartType;

  using Primitives = planner::primitive::LongitudinalPrimitives;

  static constexpr int FEATURE_DIM = pb::LongitudinalFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace planner {
namespace longitudinal {

using LongitudinalFormula =
    control_util::Formula<control_util::DdpProblemType::kLongitudinal>;

// LongitudinalFeatureParameter includes parameters defined through motion/shape
// model that are not included in the top level costing .conf files since they
// are vehicle-specific. This helper function merges feature parameter from
// .conf file with motion model to create the full LongitudinalFeatureParameter
// object.
control_util::pb::LongitudinalFeatureParameter MergeWithMotionModelParams(
    control_util::pb::LongitudinalFeatureParameter feature_param,
    const vehicle_model::LongitudinalModelWithAxleRectangularShape&
        motion_model_with_shape);

}  // namespace longitudinal
}  // namespace planner

namespace control_util {

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kLongitudinal>(
    int step, int total_step_size,
    const planner::longitudinal::LongitudinalFormula::PbCostingTerm&
        costing_term,
    const planner::longitudinal::LongitudinalFormula::PbFeatureParameter&
        feature_param,
    const planner::longitudinal::LongitudinalFormula::State& x,
    const planner::longitudinal::LongitudinalFormula::Control& u,
    const planner::longitudinal::LongitudinalFormula::StepwisePrimitives&
        stepwise_primitives,
    const planner::longitudinal::LongitudinalFormula::Primitives& primitives,
    planner::longitudinal::LongitudinalFormula::Feature* feature);

template <>
planner::longitudinal::LongitudinalFormula::StepwisePrimitives
planner::longitudinal::LongitudinalFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;

}  // namespace control_util

#endif  // ONBOARD_PLANNER_UTILITY_LONGITUDINAL_PROFILE_LONGITUDINAL_COSTING_H_
