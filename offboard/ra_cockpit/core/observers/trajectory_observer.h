#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAJECTORY_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAJECTORY_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace remote_assist {

class TrajectoryObserver : public IDataObserver {
 public:
  using TrajectoryCallback = std::function<void(
      std::shared_ptr<planner::pb::Trajectory>, DataChannel)>;

  explicit TrajectoryObserver(TrajectoryCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  TrajectoryCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAJECTORY_OBSERVER_H_
