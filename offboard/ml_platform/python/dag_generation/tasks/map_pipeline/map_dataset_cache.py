"""map dataset cache"""
from functools import partial

from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator
from airflow.utils.task_group import TaskGroup

from dag_generation.constants import TaskType
from dag_generation.constants import LUBAN_RESOURCE_UUID
from dag_generation.tasks.abstract_task import AbstractTask
from dag_generation.tasks.task_factory import register
from dag_generation.tasks.map_pipeline.checkpoint import Checkpoint

from dag_generation.tasks.map_pipeline.utils import gen_luban_task_conf
from dag_generation.tasks.map_pipeline.utils import on_failure_callback
from dag_generation.tasks.map_pipeline.utils import on_success_callback

from dataloop.utils.luban_utils import LubanTaskOperator

_GROUP_NAME = TaskType.MAP_DATASET_CACHE


@register(TaskType.MAP_DATASET_CACHE)
class MapDatasetCacheTask(AbstractTask):
    """map dataset cache"""

    def _is_skip(self, **context):
        dataset_cache = context["dag_run"].conf.get("dataset_cache")
        if not dataset_cache:
            raise AirflowSkipException

    def _gen_generate_cache_conf(self, context):
        user_name = context["dag_run"].conf.get("user_name")
        branch = context["dag_run"].conf.get("branch")
        if not branch:
            branch = "master"

        dataset_cache = context["dag_run"].conf.get("dataset_cache")
        cfg_file = dataset_cache["cfg_file"]

        run_ids = context["ti"].xcom_pull(
            task_ids=f"{TaskType.MAP_DATASET_GENERATION}.build_config", key="run_ids"
        )
        if not run_ids:
            run_ids = dataset_cache["run_ids"]

        dag_run_ids = f'"{" ".join(run_ids)}"'

        script_cmd = [
            "generate_sample_entry_cache",
            cfg_file,
            dag_run_ids,
        ]

        return gen_luban_task_conf(
            user_name,
            script_cmd,
            "generate_cache",
            LUBAN_RESOURCE_UUID["H20_1"],
            branch,
        )

    def gen_airflow_ops(self, dag, *args, **kwargs):
        with TaskGroup(_GROUP_NAME, dag=dag) as tg:
            is_skip_op = PythonOperator(
                task_id="is_skip",
                dag=dag,
                python_callable=self._is_skip,
                provide_context=True,
            )

            generate_cache_op = LubanTaskOperator(
                task_id="generate_cache",
                dag=dag,
                gen_offline_task_conf=self._gen_generate_cache_conf,
                on_success_callback=partial(
                    on_success_callback, scene="generate_cache"
                ),
                on_failure_callback=partial(
                    on_failure_callback, scene="generate_cache"
                ),
            )

            checkpoint_op = Checkpoint(_GROUP_NAME).op(dag)

            is_skip_op >> generate_cache_op >> checkpoint_op
            return tg
