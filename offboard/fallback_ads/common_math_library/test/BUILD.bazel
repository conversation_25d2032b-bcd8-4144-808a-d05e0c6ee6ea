load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "assert_cfg_test",
    srcs = ["assert_cfg_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:assert_cfg",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "can_util_test",
    srcs = ["can_util_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:can_util",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "common_method_test",
    srcs = ["common_method_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "debounce_method_test",
    srcs = ["debounce_method_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "interpolation_test",
    srcs = ["interpolation_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:interpolation",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "linear_quadratic_regulator_test",
    srcs = ["linear_quadratic_regulator_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:linear_quadratic_regulator",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "matrix_test",
    srcs = ["matrix_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:matrix",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "poly_solve_test",
    srcs = ["poly_solve_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:poly_solve",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "sort_test",
    srcs = ["sort_test.cpp"],
    deps = [
        "//offboard/fallback_ads/common_math_library:sort",
        "@voy-sdk//:gtest",
    ],
)
