#include "varch/tools/launcher/vgraph_checker.h"

#include <set>

#include "varch/base/proto.h"
#include "varch/utils//log.h"

namespace varch {
namespace tools {
namespace launcher {

namespace {
const std::set<std::string> vgraph_whitelist = {"main_lidar_node.vgraph"};

bool IsOnWhiteList(const std::string& vgraph_path) {
  auto pos = vgraph_path.find_last_of('/') + 1;
  auto vgraph_name = vgraph_path.substr(pos, vgraph_path.size() - pos);
  return vgraph_whitelist.count(vgraph_name) > 0;
}

}  // namespace

bool VGraphChecker::IsVGraphValid(const std::string& vgraph_path) {
  if (IsOnWhiteList(vgraph_path)) {
    LOG(INFO) << "VGraph [" << vgraph_path << "] is on the whitelist";
    return true;
  }

  auto vforest_config = VGraphParserChecker(vgraph_path);
  if (!IsValid()) {
    return false;
  }

  return NoDuplicatePublisherNameChecker(vforest_config, vgraph_path)
      .NoDuplicatePubTopicNameChecker(vforest_config, vgraph_path)
      .NoDuplicateSubTopicNameChecker(vforest_config, vgraph_path)
      .NoDuplicateVNodeNameChecker(vforest_config, vgraph_path)
      .IsValid();
}

VForestConfig VGraphChecker::VGraphParserChecker(
    const std::string& vgraph_path) {
  VForestConfig vforest_config;
  LOG(INFO) << "Parse configuration file " << vgraph_path;
  if (!base::ReadTextProtoFile(vgraph_path, &vforest_config)) {
    LOG(ERROR) << "Error parsing vgraph file: " << vgraph_path;
    SetIsInValid();
  }
  return vforest_config;
}

bool VGraphChecker::IsValid() { return is_valid_; }
void VGraphChecker::SetIsInValid() { is_valid_ = false; }

VGraphChecker& VGraphChecker::NoDuplicatePublisherNameChecker(
    const VForestConfig& vforest_config, const std::string& vgraph_path) {
  for (const auto& vnode_conf : vforest_config.vgraph().vnode()) {
    std::set<std::string> publisher_names;
    for (const auto& pub : vnode_conf.pub()) {
      if (publisher_names.count(pub.name())) {
        LOG(ERROR) << "Found duplicate publisher name: " << pub.name()
                   << " in VNode: " << vnode_conf.name()
                   << " in vgraph: " << vgraph_path;
        SetIsInValid();
        return *this;
      } else {
        publisher_names.insert(pub.name());
      }
    }
  }
  return *this;
}

VGraphChecker& VGraphChecker::NoDuplicatePubTopicNameChecker(
    const VForestConfig& vforest_config, const std::string& vgraph_path) {
  for (const auto& vnode_conf : vforest_config.vgraph().vnode()) {
    std::set<std::string> pub_topic_names;
    for (const auto& pub : vnode_conf.pub()) {
      if (pub_topic_names.count(pub.topic())) {
        LOG(ERROR) << "Found duplicate publisher topic name: " << pub.topic()
                   << " in VNode: " << vnode_conf.name()
                   << " in vgraph: " << vgraph_path;
        SetIsInValid();
        return *this;
      } else {
        pub_topic_names.insert(pub.topic());
      }
    }
  }
  return *this;
}

VGraphChecker& VGraphChecker::NoDuplicateSubTopicNameChecker(
    const VForestConfig& vforest_config, const std::string& vgraph_path) {
  for (const auto& vnode_conf : vforest_config.vgraph().vnode()) {
    if (!vnode_conf.has_sub()) {
      continue;
    }
    std::set<std::string> sub_topic_names;
    for (const auto& sub : vnode_conf.sub().topic()) {
      if (sub_topic_names.count(sub.topic())) {
        LOG(ERROR) << "Found duplicate subscriber topic name: " << sub.topic()
                   << " in VNode: " << vnode_conf.name()
                   << " in vgraph: " << vgraph_path;
        SetIsInValid();
        return *this;
      } else {
        sub_topic_names.insert(sub.topic());
      }
    }
  }
  return *this;
}

VGraphChecker& VGraphChecker::NoDuplicateVNodeNameChecker(
    const VForestConfig& vforest_config, const std::string& vgraph_path) {
  for (const auto& vnode_conf : vforest_config.vgraph().vnode()) {
    const std::string& vnode_name = vnode_conf.name();
    if (!vnode_name_vgraph_map_.count(vnode_name)) {
      vnode_name_vgraph_map_.emplace(vnode_name, vgraph_path);
      continue;
    }
    LOG(ERROR) << "Found duplicate vnode name: " << vnode_name
               << " in vgraph: " << vnode_name_vgraph_map_[vnode_name]
               << " and in vgraph: " << vgraph_path;
    SetIsInValid();
    return *this;
  }
  return *this;
}

}  // namespace launcher
}  // namespace tools
}  // namespace varch
