#include "planner/speed/reasoning/reasoning_util.h"

#include <cstddef>
#include <functional>
#include <unordered_map>
#include <vector>

#include <gtest/gtest.h>

#include "hdmap/lib/test/test_util.h"
#include "hdmap_protos/lane.pb.h"
#include "math/range.h"
#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/overlap/ego_lateral_clearance.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/agent_trajectory_info_util.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning/route_association_util.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/conflicting_lane_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/junction_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/merge_fork_lane_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/zone_traffic_rule.h"
#include "planner/speed/reasoning_input/util/matching_crosswalk.h"
#include "planner_protos/behavior_reasoner.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/test/map_test_util.h"
#include "prediction_protos/agent.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {
namespace speed {
namespace reasoning_util {

// This is to test the function IsNotFacingEgoVehicle. The function is expected
// to return true is the agent is vehicle, and is it not facing ego.
class IsNotFacingEgoVehicleTest : public ::testing::Test,
                                  public ReasoningTestFixture {
 public:
  IsNotFacingEgoVehicleTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  }
};

TEST_F(IsNotFacingEgoVehicleTest, RightNotFacingEgoTest) {
  SetEgoPose(/*lane_id=*/152793, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/152797, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/152797,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/152797, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_TRUE(
      reasoning_inputs().front().reasoning_object.is_not_facing_ego_vehicle());
}

TEST_F(IsNotFacingEgoVehicleTest, RightFacingEgoTest) {
  SetEgoPose(/*lane_id=*/152761, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/152699, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/152699,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/152699, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(
      reasoning_inputs().front().reasoning_object.is_not_facing_ego_vehicle());
}

TEST_F(IsNotFacingEgoVehicleTest, LeftNotFacingEgoTest) {
  SetEgoPose(/*lane_id=*/166667, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/152703, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/152703,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/152703, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_TRUE(
      reasoning_inputs().front().reasoning_object.is_not_facing_ego_vehicle());
}

TEST_F(IsNotFacingEgoVehicleTest, LeftFacingEgoTest) {
  SetEgoPose(/*lane_id=*/152697, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/152742, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/152742,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/152742, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(
      reasoning_inputs().front().reasoning_object.is_not_facing_ego_vehicle());
}

class IsFullyBehindTest : public ::testing::Test, public ReasoningTestFixture {
 public:
  IsFullyBehindTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

// Description:
// This is to test a agent is fully behind ego and located in a straight lane.
//
// Expected result:
// IsFullyBehind() should return true.
//
// Scene screenshot:
// https://drive.google.com/file/d/1yk-nD_vDiZ4RC8S0ARgvZ8a8J6jxKpyf/view?usp=share_link
TEST_F(IsFullyBehindTest, AgentInStraightDrivingLaneTest) {
  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.1);

  // Create a lane sequence including lane 17049, 16905 and 43411 in Fremont
  // map.
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16915, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16915, /*start_portion=*/0.2,
                                 /*end_lane_id=*/16905, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  // Verify the agent is in unprotected left turn and the relative heading with
  // ego is bigger than pi/6.
  EXPECT_TRUE(object_proximity_info.out_of_path_range());
  EXPECT_GT(world_context().ra_to_trailing_bumper_shift(),
            object_proximity_info.projected_ra_arc_length().end());
  EXPECT_GT(object_proximity_info.abs_lateral_gap(), 2.0);
  EXPECT_LT(object_proximity_info.relative_motion_heading(), M_PI / 6);

  EXPECT_FALSE(IsFullyBehind(reasoning_inputs().front().reasoning_object,
                             world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a agent is fully behind ego and located in an unprotected
// left turn.
//
// Expected result:
// IsFullyBehind() should return true.
//
// Scene screenshot:
// https://drive.google.com/file/d/1vM2vPja5lQnupak_GwLc_tX5z_RcqTIk/view?usp=share_link
TEST_F(IsFullyBehindTest, AgentInUnprotectedLeftTurnTest) {
  SetEgoPose(/*lane_id=*/98241, /*portion=*/0.9);

  // Create a lane sequence including lane 16883, 98241 and 97309 in Fremont
  // map.
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/98241, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/98241, /*start_portion=*/0.2,
                                 /*end_lane_id=*/98241, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  // Verify the agent is in unprotected left turn and the relative heading with
  // ego is bigger than pi/6.
  EXPECT_FALSE(object_proximity_info.out_of_path_range());
  EXPECT_GT(world_context().ra_to_trailing_bumper_shift(),
            object_proximity_info.projected_ra_arc_length().end());
  EXPECT_LT(object_proximity_info.abs_lateral_gap(), 2.0);

  // Verify the output of IsFullyBehind() is true.
  EXPECT_TRUE(IsFullyBehind(reasoning_inputs().front().reasoning_object,
                            world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a agent is fully behind ego and located in an U-turn.
//
// Expected result:
// IsFullyBehind() should return true.
//
// Scene screenshot:
// https://drive.google.com/file/d/1xcy7W_91axJbsTzJp8RAKyMx9JPSxUXE/view?usp=share_link
TEST_F(IsFullyBehindTest, AgentInUTurnTest) {
  SetEgoPose(/*lane_id=*/97371, /*portion=*/0.9);

  // Create a lane sequence including lane 16883, 97371 and 16915 in Fremont
  // map.
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97341, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/97371, /*start_portion=*/0.2,
                                 /*end_lane_id=*/98241, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  // Verify the agent is in U-turn and the relative heading with ego is bigger
  // than pi/6.
  EXPECT_FALSE(object_proximity_info.out_of_path_range());
  EXPECT_GT(world_context().ra_to_trailing_bumper_shift(),
            object_proximity_info.projected_ra_arc_length().end());
  EXPECT_LT(object_proximity_info.abs_lateral_gap(), 2.0);

  // Verify the output of IsFullyBehind() is true.
  EXPECT_TRUE(IsFullyBehind(reasoning_inputs().front().reasoning_object,
                            world_context().ra_to_trailing_bumper_shift()));
}

class IsFarAwayBehindTest : public ::testing::Test,
                            public ReasoningTestFixture {
 public:
  IsFarAwayBehindTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(IsFarAwayBehindTest, DistTest) {
  constexpr int64_t first_agent_id = 1;
  constexpr int64_t second_agent_id = 2;

  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.5, /*speed=*/5.0);
  CreateLaneFollowPath(/*extend_backward=*/true);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Add the first agent who is close to ego.
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/first_agent_id, /*lane_id=*/16905, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16905, /*start_portion=*/0.2,
                                 /*end_lane_id=*/17019, /*end_portion=*/0.8,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent1);
  // Add the second agent whose dist to ego is longer.
  prediction::pb::Agent& agent2 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/second_agent_id, /*lane_id=*/17049, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/17049, /*start_portion=*/0.5,
                                 /*end_lane_id=*/16905, /*end_portion=*/0.6,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/2, agent2);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());

  Update();

  const auto first_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == first_agent_id;
                   });
  DCHECK(first_agent_iter != reasoning_inputs().end());
  EXPECT_FALSE(IsFarAwayBehind(
      first_agent_iter->reasoning_object.object_proximity_info(),
      world_context().ego_speed(),
      world_context().ra_to_trailing_bumper_shift()));
  const auto second_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == second_agent_id;
                   });
  DCHECK(second_agent_iter != reasoning_inputs().end());
  EXPECT_TRUE(IsFarAwayBehind(
      second_agent_iter->reasoning_object.object_proximity_info(),
      world_context().ego_speed(),
      world_context().ra_to_trailing_bumper_shift()));
}

TEST_F(IsFarAwayBehindTest, SpeedDiffTest) {
  constexpr int64_t first_agent_id = 1;
  constexpr int64_t second_agent_id = 2;

  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.5, /*speed=*/5.0);
  CreateLaneFollowPath(/*extend_backward=*/true);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Add the first agent whose speed is higher than ego.
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/first_agent_id, /*lane_id=*/16905, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16905, /*start_portion=*/0.2,
                                 /*end_lane_id=*/17019, /*end_portion=*/0.8,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent1);
  // Add the second agent whose speed is the same to ego.
  prediction::pb::Agent& agent2 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/second_agent_id, /*lane_id=*/16905, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/5.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16905, /*start_portion=*/0.2,
                                 /*end_lane_id=*/17019, /*end_portion=*/0.8,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/2, agent2);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());

  Update();

  const auto first_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == first_agent_id;
                   });
  DCHECK(first_agent_iter != reasoning_inputs().end());
  EXPECT_FALSE(IsFarAwayBehind(
      first_agent_iter->reasoning_object.object_proximity_info(),
      world_context().ego_speed(),
      world_context().ra_to_trailing_bumper_shift()));
  const auto second_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == second_agent_id;
                   });
  DCHECK(second_agent_iter != reasoning_inputs().end());
  EXPECT_TRUE(IsFarAwayBehind(
      second_agent_iter->reasoning_object.object_proximity_info(),
      world_context().ego_speed(),
      world_context().ra_to_trailing_bumper_shift()));
}

class IsLeadingAgentTest : public ::testing::Test, public ReasoningTestFixture {
 public:
  IsLeadingAgentTest() { SetUpSceneMap(hdmap::test_util::SceneType::kMerge); }
};

// https://drive.google.com/file/d/1oiGtnqbkv7kWHKb8trbpcRuCWEDZkyqa/view
TEST_F(IsLeadingAgentTest, LeadAgentInStraightDrivingLaneTest) {
  SetEgoPose(/*lane_id=*/128481, /*portion=*/0.1);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/128481, /*portion=*/0.3,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/5.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/128481,
                                 /*start_portion=*/0.3,
                                 /*end_lane_id=*/128481, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  EXPECT_FALSE(object_proximity_info.out_of_path_range());
  EXPECT_LT(world_context().ra_to_leading_bumper(),
            object_proximity_info.projected_ra_arc_length().start());
  EXPECT_NEAR(object_proximity_info.abs_lateral_gap(), 0.0, 1e-6);
  EXPECT_LT(object_proximity_info.relative_motion_heading(), M_PI / 4);

  EXPECT_TRUE(IsLeadingAgent(trajectory_info(),
                             reasoning_inputs().front().reasoning_object,
                             world_context().ra_to_leading_bumper()));
}

// https://drive.google.com/file/d/1WO4uFiFJcleKDtbRxxTFAtlYWyWYBNbr/view
TEST_F(IsLeadingAgentTest, LeadAgentInTurnLaneTest) {
  SetEgoPose(/*lane_id=*/145246, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/145246, /*portion=*/0.8,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/5.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/145246,
                                 /*start_portion=*/0.8,
                                 /*end_lane_id=*/145246, /*end_portion=*/0.9,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  EXPECT_FALSE(object_proximity_info.out_of_path_range());
  EXPECT_LT(world_context().ra_to_leading_bumper(),
            object_proximity_info.projected_ra_arc_length().start());
  EXPECT_NEAR(object_proximity_info.abs_lateral_gap(), 0.0, 1e-6);
  EXPECT_LT(object_proximity_info.relative_motion_heading(), M_PI / 4);

  EXPECT_TRUE(IsLeadingAgent(trajectory_info(),
                             reasoning_inputs().front().reasoning_object,
                             world_context().ra_to_leading_bumper()));
}

// https://drive.google.com/file/d/1IYCGldS2zC_nvVHAydgSf905eX_t8n6u/view
TEST_F(IsLeadingAgentTest, LaneChangeLeadingInProgress) {
  SetEgoPose(/*lane_id=*/128479, /*portion=*/0.1, /*speed=*/5.0);
  // Construct lane change status and path.
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, /*source_lane_id=*/128479,
      /*target_lane_id=*/128481, /*is_current_homotopy_lane_change=*/true,
      /*leading_object_id=*/1);
  CreateLaneChangePath(/*source_lane_id=*/128479, /*target_lane_id=*/128481);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/128481, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/5.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/128481,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/128481, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_inputs().front().reasoning_object.object_proximity_info();

  EXPECT_GT(world_context().ra_to_leading_bumper(),
            object_proximity_info.projected_ra_arc_length().start());
  EXPECT_GT(object_proximity_info.abs_lateral_gap(), 0.0);
  EXPECT_LT(object_proximity_info.relative_motion_heading(), M_PI / 4);

  EXPECT_FALSE(IsLeadingAgent(trajectory_info(),
                              reasoning_inputs().front().reasoning_object,
                              world_context().ra_to_leading_bumper()));
}

TEST_F(IsLeadingAgentTest, ObjectTypeTest) {
  for (const voy::perception::ObjectType object_type :
       {voy::perception::ObjectType::VEHICLE,
        voy::perception::ObjectType::CYCLIST}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        object_type,
        /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
        /*length=*/4.0, /*width=*/2.0,
        /*velocity=*/0.0, /*height=*/1.8, /*heading_diff_to_lane=*/0.0);

    AddStationaryPredictedTrajectory(
        /*lane_id=*/128481, /*portion=*/0.6,
        /*likelihood=*/0.9, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/0.0);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_TRUE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }

  for (const voy::perception::ObjectType object_type :
       {voy::perception::ObjectType::TRAFFIC_CONE,
        voy::perception::ObjectType::PED,
        voy::perception::ObjectType::BARRIER}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        object_type,
        /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
        /*length=*/4.0, /*width=*/2.0,
        /*velocity=*/0.0, /*height=*/1.8, /*heading_diff_to_lane=*/0.0);

    AddStationaryPredictedTrajectory(
        /*lane_id=*/128481, /*portion=*/0.6,
        /*likelihood=*/0.9, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/0.0);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    // Such object types can not be leading agents.
    EXPECT_FALSE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }
}

TEST_F(IsLeadingAgentTest, HeadingTest) {
  for (double relative_motion_heading : {-M_PI / 6, 0.0, M_PI / 6}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::VEHICLE,
                 /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/5.0, /*height=*/1.8,
                 /*heading_diff_to_lane=*/relative_motion_heading);

    AddStraightPredictedTrajectory(/*start_lane_id=*/128481,
                                   /*start_portion=*/0.6,
                                   /*end_lane_id=*/128481, /*end_portion=*/1.0,
                                   /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_FALSE(reasoning_inputs().front().reasoning_object.IsStationary());
    EXPECT_TRUE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }

  for (double relative_motion_heading : {-M_PI, -M_PI / 2, M_PI / 2, M_PI}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::VEHICLE,
                 /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/5.0, /*height=*/1.8,
                 /*heading_diff_to_lane=*/relative_motion_heading);

    AddStraightPredictedTrajectory(/*start_lane_id=*/128481,
                                   /*start_portion=*/0.6,
                                   /*end_lane_id=*/128481, /*end_portion=*/1.0,
                                   /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_FALSE(reasoning_inputs().front().reasoning_object.IsStationary());
    EXPECT_FALSE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }
}

TEST_F(IsLeadingAgentTest, BoxHeadingTest) {
  for (double relative_box_heading : {-M_PI / 6, 0.0, M_PI / 6}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::VEHICLE,
                 /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/0.0, /*height=*/1.8,
                 /*heading_diff_to_lane=*/relative_box_heading);

    AddStationaryPredictedTrajectory(
        /*lane_id=*/128481, /*portion=*/0.6,
        /*likelihood=*/0.9, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/relative_box_heading);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_TRUE(reasoning_inputs().front().reasoning_object.IsStationary());
    EXPECT_TRUE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }

  for (double relative_box_heading : {-M_PI, -M_PI / 2, M_PI / 2, M_PI}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::VEHICLE,
                 /*id=*/0, /*lane_id=*/128481, /*portion=*/0.6,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/0.0, /*height=*/1.8,
                 /*heading_diff_to_lane=*/relative_box_heading);

    AddStationaryPredictedTrajectory(
        /*lane_id=*/128481, /*portion=*/0.6,
        /*likelihood=*/0.9, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/relative_box_heading);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_TRUE(reasoning_inputs().front().reasoning_object.IsStationary());
    EXPECT_FALSE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }
}

TEST_F(IsLeadingAgentTest, LateralGapTest) {
  for (double lateral_gap : {-1.0, 1.0}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    const double agent_width = 2.0;
    const double lateral_shift =
        std::copysign(world_model().robot_state().GetWidth() / 2.0,
                      lateral_gap) +
        std::copysign(agent_width / 2.0, lateral_gap) + lateral_gap;
    const math::Pose2d shifted_pose = LanePointToPoseWithShift(
        /*lane_id=*/128481, /*portion=*/0.6, /*arclength_shift=*/0.0,
        /*lateral_shift=*/lateral_shift, /*theta_shift=*/0.0);

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        voy::perception::ObjectType::VEHICLE, /*id=*/0, /*pose=*/shifted_pose,
        /*length=*/4.0, /*width=*/agent_width, /*velocity=*/0.0,
        /*height=*/1.8);

    AddStationaryPredictedTrajectory(
        /*pose=*/shifted_pose, /*likelihood=*/0.9, /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    [[maybe_unused]] const pb::ObjectProximityInfo& object_proximity_info =
        reasoning_inputs().front().reasoning_object.object_proximity_info();

    // The object is too far away from Ego path.
    EXPECT_FALSE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }

  for (double lateral_gap : {0.1, 0.5}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    const double agent_width = 2.0;
    const double lateral_shift =
        std::copysign(world_model().robot_state().GetWidth() / 2.0,
                      lateral_gap) +
        std::copysign(agent_width / 2.0, lateral_gap) - lateral_gap;
    const math::Pose2d shifted_pose = LanePointToPoseWithShift(
        /*lane_id=*/128481, /*portion=*/0.6, /*arclength_shift=*/0.0,
        /*lateral_shift=*/lateral_shift, /*theta_shift=*/0.0);

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        voy::perception::ObjectType::VEHICLE, /*id=*/0, /*pose=*/shifted_pose,
        /*length=*/4.0, /*width=*/agent_width, /*velocity=*/0.0,
        /*height=*/1.8);

    AddStationaryPredictedTrajectory(
        /*pose=*/shifted_pose, /*likelihood=*/0.9, /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_TRUE(IsLeadingAgent(
        trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));

    Reset();
  }
}

TEST_F(IsLeadingAgentTest, LongitudinalDistanceTest) {
  for (double lon_shift : {-1.0, 1.0}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    const math::Pose2d shifted_pose = LanePointToPoseWithShift(
        /*lane_id=*/128481, /*portion=*/0.4,
        /*arclength_shift=*/lon_shift,
        /*lateral_shift=*/0.0, /*theta_shift=*/0.0);

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        voy::perception::ObjectType::VEHICLE, /*id=*/0, /*pose=*/shifted_pose,
        /*length=*/4.0, /*width=*/2.0, /*velocity=*/0.0, /*height=*/1.8);

    AddStationaryPredictedTrajectory(
        /*pose=*/shifted_pose, /*likelihood=*/0.9, /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    // The object is not leading, either parallel or behind Ego.
    EXPECT_FALSE(IsLeadingAgent(trajectory_info(),
                                reasoning_inputs().front().reasoning_object,
                                /*min_ra_arc_length=*/4.0));

    Reset();
  }

  for (double lon_shift : {8.0, 10.0}) {
    SetEgoPose(/*lane_id=*/128481, /*portion=*/0.4);
    CreateLaneFollowPath();

    const math::Pose2d shifted_pose = LanePointToPoseWithShift(
        /*lane_id=*/128481, /*portion=*/0.4,
        /*arclength_shift=*/lon_shift,
        /*lateral_shift=*/0.0, /*theta_shift=*/0.0);

    // Contruct prediction::pb::Agent instance.
    prediction::pb::Agent& agent = AddAgent(
        voy::perception::ObjectType::VEHICLE, /*id=*/0, /*pose=*/shifted_pose,
        /*length=*/4.0, /*width=*/2.0, /*velocity=*/0.0, /*height=*/1.8);

    AddStationaryPredictedTrajectory(
        /*pose=*/shifted_pose, /*likelihood=*/0.9, /*traj_id=*/1, agent);

    EXPECT_FALSE(trajectory_info().path().empty());
    EXPECT_EQ(1, agent_list().agent_list().size());

    Update();

    EXPECT_TRUE(IsLeadingAgent(trajectory_info(),
                               reasoning_inputs().front().reasoning_object,
                               /*min_ra_arc_length=*/4.0));

    Reset();
  }
}

TEST_F(IsLeadingAgentTest, DiagonalCrossingTest) {
  // https://drive.google.com/file/d/1LrgC_HX2g04N4KsOcXij6Z2YlDf7ts4n/view?usp=sharing
  // https://drive.google.com/file/d/197q9AQoqITw9SWCJqru1pAw8oanfYRiM/view?usp=sharing
  SetUpRegionMap(hdmap::test_util::kFremontData);
  SetEgoPose(/*lane_id=*/43411, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/17043, /*portion=*/0.9,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/17043,
                                 /*start_portion=*/0.9,
                                 /*end_lane_id=*/17043, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLeadingAgent(
      trajectory_info(), reasoning_inputs().front().reasoning_object, 0.0));
}

TEST(IsSafeWithLeadingAgentTest, TimeToCollisionTest) {
  // Construct voy::TrackedObject instance.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_id(0);

  for (double ego_speed_mps : {1.0, 3.0, 5.0, 7.0}) {
    // Construct pb::ObjectProximityInfo instance.
    pb::ObjectProximityInfo object_proximity_info;
    object_proximity_info.set_relative_motion_heading(0.0);
    object_proximity_info.set_relative_box_heading(0.0);
    object_proximity_info.set_signed_lateral_gap(0.0);
    object_proximity_info.set_abs_lateral_gap(0.0);
    object_proximity_info.set_signed_lateral_speed(0.0);
    object_proximity_info.set_signed_longitudinal_speed(5.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_start(10.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_end(10.0);

    const planner::pb::RequiredLateralGapConfig empty_config;
    const int64_t timestamp = 0;
    // Dummy pose at plan init ts.
    const TrafficParticipantPose pose_at_plan_init_ts(timestamp,
                                                      tracked_object);
    const PlannerObject planner_object(agent_proto, timestamp,
                                       pose_at_plan_init_ts);
    const PrincipledRequiredLateralGap required_lateral_gap =
        planner::ComputePrincipledRequiredLateralGap(planner_object,
                                                     empty_config);
    ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                     required_lateral_gap,
                                     /*object_occupancy_param_ptr=*/nullptr);
    reasoning_object.set_is_leading_agent(true);
    EXPECT_TRUE(IsSafeWithLeadingAgent(reasoning_object, ego_speed_mps,
                                       /*ra_to_front_bumper=*/4.0));
  }

  for (double ego_speed_mps : {9.0, 12.0, 15.0}) {
    // Construct pb::ObjectProximityInfo instance.
    pb::ObjectProximityInfo object_proximity_info;
    object_proximity_info.set_relative_motion_heading(0.0);
    object_proximity_info.set_relative_box_heading(0.0);
    object_proximity_info.set_signed_lateral_gap(0.0);
    object_proximity_info.set_abs_lateral_gap(0.0);
    object_proximity_info.set_signed_lateral_speed(0.0);
    object_proximity_info.set_signed_longitudinal_speed(5.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_start(10.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_end(10.0);

    const planner::pb::RequiredLateralGapConfig empty_config;
    const int64_t timestamp = 0;
    // Dummy pose at plan init ts.
    const TrafficParticipantPose pose_at_plan_init_ts(timestamp,
                                                      tracked_object);
    const PlannerObject planner_object(agent_proto, timestamp,
                                       pose_at_plan_init_ts);
    const PrincipledRequiredLateralGap required_lateral_gap =
        planner::ComputePrincipledRequiredLateralGap(planner_object,
                                                     empty_config);
    ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                     required_lateral_gap,
                                     /*object_occupancy_param_ptr=*/nullptr);
    reasoning_object.set_is_leading_agent(true);
    EXPECT_FALSE(IsSafeWithLeadingAgent(reasoning_object, ego_speed_mps,
                                        /*ra_to_front_bumper=*/4.0));
  }
}

class IsLikelyTailgatingTest : public ::testing::Test,
                               public ReasoningTestFixture {
 public:
  IsLikelyTailgatingTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

// Description:
// This is to test a vehicle that is slightly behind ego and in the same lane
// with ego.
//
// Expected result:
// IsLikelyTailgating() should return true.
//
// Scene screenshot:
// https://drive.google.com/file/d/10vAH5gumflRb0sUCBT-UfZk0F-XCLxtM/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, VehicleSlightlyBehindAndInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/52649, /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_TRUE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a vehicle that is slightly behind ego and in a neighboring
// lane of ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/1EiZB45vNBSuNOPmOlEpT_70aYTrOjxq4/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, VehicleSlightlyBehindAndInNeighboringLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97149, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/97149, /*start_portion=*/0.2,
                                 /*end_lane_id=*/97149, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a vehicle that is ahead of ego and in the same lane with ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/1B_K4DxjQBgzsDBscMDKbB27xAObclX_w/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, VehicleAheadAndInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/52649, /*start_portion=*/0.5,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a cyclist that is slightly behind ego and in the same lane
// with ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/1XI0EwgZXsDj5jDeF_8J4DGjuLWTHRU8A/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, CyclistSlightlyBehindAndInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/52649, /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test an unknown object that is slightly behind ego and in the same
// lane with ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/16VI5fp2R4H3-RTXuTTuo-jXXM99EnZwC/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, UnknownSlightlyBehindAndInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::UNKNOWN,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.4,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/52649, /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a vehicle that is slightly behind ego but laterally far away
// with ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/1USki1VfyvcRlupkIla2OUOBzRWkhwTRb/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, VehicleSlightlyBehindButLaterallyFarTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97151, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/97151, /*start_portion=*/0.2,
                                 /*end_lane_id=*/97151, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a vehicle that is far away behind ego but in the same lane
// with ego.
//
// Expected result:
// IsLikelyTailgating() should return false.
//
// Scene screenshot:
// https://drive.google.com/file/d/1LEg10IRGBWgG-amXQxERDGjHaigBixLV/view?usp=sharing
TEST_F(IsLikelyTailgatingTest, VehicleFarAwayBehindButInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16921, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16921, /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

// Description:
// This is to test a vehicle that is slightly behind ego and following ego with
// a small lateral gap when ego is in a junction. Note the vehicle is not
// necessarily to be in the junction at the same time. In the case when ego is
// in a junction, the lateral gap threshold for a likely tailgating one is
// relaxed compared to that ego is not in a junction. This is to consider that a
// tailgating agent may not follow tightly the path that ego has passed in a
// junction possibly because of the wide turning path of ego and the lack of
// clear lane marking in the junction.
//
// Expected result:
// IsLikelyTailgating() should return true.
//
// Scene screenshot:
// https://drive.google.com/file/d/1hklhUzCB8xT3gOVpA0E6bMDjz-HhElH4/view?usp=share_link
TEST_F(IsLikelyTailgatingTest, VehicleSlightlyBehindWhenEgoInJunctionTest) {
  SetEgoPose(/*lane_id=*/97371, /*portion=*/0.1);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*pose=*/math::Pose2d(-4504.71, -2595.88, -1.06),
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.5);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16883, /*start_portion=*/0.5,
                                 /*end_lane_id=*/16885, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_TRUE(IsLikelyTailgating(
      trajectory_info(), reasoning_inputs().front().reasoning_object,
      world_context().ra_to_trailing_bumper_shift()));
}

class IsEgoPathOvertakingTest : public ::testing::Test,
                                public ReasoningTestFixture {
 public:
  IsEgoPathOvertakingTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(IsEgoPathOvertakingTest, IgnoreAll) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath(/*extend_backward=*/false);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const int64_t kObjectId = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStationaryPredictedTrajectory(/*lane_id=*/16913,
                                       /*portion=*/0.7,
                                       /*likelihood=*/0.9, /*traj_id=*/1,
                                       agent);

  Update();
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  // IGNORE_ALL homotopy and expect false.
  planner::pb::IntentionResult path_intention;
  path_intention.set_homotopy(planner::pb::IntentionResult::IGNORE_ALL);
  EXPECT_FALSE(IsEgoPathOvertaking(input.reasoning_object, path_intention));

  // No intention result for the agent. Expect false.
  path_intention.set_homotopy(planner::pb::IntentionResult::PASS_FROM_LEFT);
  EXPECT_FALSE(IsEgoPathOvertaking(input.reasoning_object, path_intention));

  // Has nudge direction for the agent, but is_overtaken is set to false.
  planner::pb::EgoIntention& ego_intention =
      (*(path_intention.mutable_object_intentions()))[kObjectId];
  ego_intention.set_object_id(kObjectId);
  ego_intention.set_is_overtaken(false);
  planner::pb::SnapshotIntention* snapshot_intention =
      ego_intention.add_snapshot_intentions();
  snapshot_intention->set_pass_state(planner::pb::SnapshotIntention::PASS_LEFT);
  EXPECT_FALSE(IsEgoPathOvertaking(input.reasoning_object, path_intention));
}

TEST_F(IsEgoPathOvertakingTest, NudgeSnapshotIntention) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath(/*extend_backward=*/false);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const int64_t kObjectId = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStationaryPredictedTrajectory(/*lane_id=*/16913,
                                       /*portion=*/0.7,
                                       /*likelihood=*/0.9, /*traj_id=*/1,
                                       agent);

  Update();
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  planner::pb::IntentionResult path_intention;
  path_intention.set_homotopy(planner::pb::IntentionResult::PASS_FROM_LEFT);

  // Has nudge direction for the agent, and is_overtaken is set to true.
  planner::pb::EgoIntention& ego_intention =
      (*(path_intention.mutable_object_intentions()))[kObjectId];
  ego_intention.set_object_id(kObjectId);
  ego_intention.set_is_overtaken(true);
  planner::pb::SnapshotIntention* snapshot_intention =
      ego_intention.add_snapshot_intentions();
  snapshot_intention->set_pass_state(planner::pb::SnapshotIntention::PASS_LEFT);
  EXPECT_TRUE(IsEgoPathOvertaking(input.reasoning_object, path_intention));
}

class IsAgentInNeighboringLaneTest : public ::testing::Test,
                                     public ReasoningTestFixture {
 public:
  IsAgentInNeighboringLaneTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

// Description:
// This is to test if the util function IsAgentInNeighboringLane() could return
// correct results for the agents around ego's current lane.
//
// Expected result:
//   * IsAgentInNeighboringLane() should return true for the first agent.
//   * IsAgentInNeighboringLane() should return true for the second agent.
//   * IsAgentInNeighboringLane() should return false for the third agent.
TEST_F(IsAgentInNeighboringLaneTest, InAndOutOfNeighboringLaneTest) {
  constexpr int64_t first_agent_id = 1;
  constexpr int64_t second_agent_id = 2;
  constexpr int64_t third_agent_id = 3;

  SetUpSceneMap(hdmap::test_util::SceneType::kForklane);

  SetEgoPose(/*lane_id=*/128253, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& first_agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/first_agent_id, /*lane_id=*/128251, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/128251,
      /*start_portion=*/0.1,
      /*end_lane_id=*/128251, /*end_portion=*/1.0,
      /*likelihood=*/0.9, /*traj_id=*/1, first_agent);

  prediction::pb::Agent& second_agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/second_agent_id, /*lane_id=*/128255, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/128255,
      /*start_portion=*/0.1,
      /*end_lane_id=*/128255, /*end_portion=*/1.0,
      /*likelihood=*/0.9, /*traj_id=*/1, second_agent);

  prediction::pb::Agent& third_agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/third_agent_id, /*lane_id=*/128233, /*portion=*/0.8,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/128233,
      /*start_portion=*/0.8,
      /*end_lane_id=*/128251, /*end_portion=*/0.8,
      /*likelihood=*/0.9, /*traj_id=*/1, third_agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(3, agent_list().agent_list().size());

  Update();

  // The first agent is in the left lane of ego, which is in a neighboring lane.
  const auto first_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == first_agent_id;
                   });
  DCHECK(first_agent_iter != reasoning_inputs().end());
  EXPECT_TRUE(IsAgentInNeighboringLane(trajectory_info(),
                                       first_agent_iter->reasoning_object));

  // The second agent is in the right lane of ego, which is in a neighboring
  // lane.
  const auto second_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == second_agent_id;
                   });
  DCHECK(second_agent_iter != reasoning_inputs().end());
  EXPECT_TRUE(IsAgentInNeighboringLane(trajectory_info(),
                                       second_agent_iter->reasoning_object));

  // The third agent is in the left-rear lane of ego, which is not in a
  // neighboring lane.
  const auto third_agent_iter =
      std::find_if(reasoning_inputs().begin(), reasoning_inputs().end(),
                   [](const PredictionDecisionMakerInput& input) {
                     return input.reasoning_object.id() == third_agent_id;
                   });
  DCHECK(third_agent_iter != reasoning_inputs().end());
  EXPECT_FALSE(IsAgentInNeighboringLane(trajectory_info(),
                                        third_agent_iter->reasoning_object));
}

class GetFirstJunctionAheadTest : public ::testing::Test,
                                  public ReasoningTestFixture {
 public:
  GetFirstJunctionAheadTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(GetFirstJunctionAheadTest, HasJunctionTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());

  Update();

  const traffic_rules::JunctionInLaneSequence* first_junction =
      traffic_rules::GetFirstJunctionAhead(
          trajectory_info().traffic_rules().lane_sequence_junctions);
  ASSERT_NE(first_junction, nullptr);
  EXPECT_EQ(first_junction->id, 185);
}

TEST_F(GetFirstJunctionAheadTest, NoJunctionTest) {
  SetEgoPose(/*lane_id=*/43415, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());

  Update();

  const traffic_rules::JunctionInLaneSequence* first_junction =
      traffic_rules::GetFirstJunctionAhead(
          trajectory_info().traffic_rules().lane_sequence_junctions);
  EXPECT_EQ(first_junction, nullptr);
}

class GetFirstCrosswalkAheadTest : public ::testing::Test,
                                   public ReasoningTestFixture {
 public:
  GetFirstCrosswalkAheadTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(GetFirstCrosswalkAheadTest, FoundFirstCrosswalkTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());

  Update();

  const traffic_rules::CrosswalkInLaneSequence* first_crosswalk =
      GetFirstCrosswalkAhead(trajectory_info().traffic_rules().crosswalks);
  ASSERT_NE(first_crosswalk, nullptr);
  EXPECT_EQ(first_crosswalk->crosswalk_ptr->id(), 4223);
}

TEST_F(GetFirstCrosswalkAheadTest, FoundNoCrosswalkTest) {
  SetEgoPose(/*lane_id=*/43415, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());

  Update();

  const traffic_rules::CrosswalkInLaneSequence* first_crosswalk =
      GetFirstCrosswalkAhead(trajectory_info().traffic_rules().crosswalks);
  EXPECT_EQ(first_crosswalk, nullptr);
}

class GetFirstTrafficLightAheadTest : public ::testing::Test,
                                      public ReasoningTestFixture {
 public:
  GetFirstTrafficLightAheadTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }

  // Inits traffic light info.
  void InitTrafficLightInfo(const int left_turn_tl_signal_id,
                            const voy::TrafficLight::Color left_turn_tl_color,
                            const int straight_tl_signal_id,
                            const voy::TrafficLight::Color straight_tl_color,
                            const bool is_flashing = false) {
    traffic_lights_.clear_traffic_lights();
    AddTrafficLight(/*timestamp=*/timestamp(),
                    /*sign_id=*/left_turn_tl_signal_id,
                    /*color=*/left_turn_tl_color,
                    /*type=*/voy::TrafficLight::LEFT,
                    /*countdown_number=*/-1, /*is_flashing=*/is_flashing);
    AddTrafficLight(/*timestamp=*/timestamp(),
                    /*sign_id=*/straight_tl_signal_id,
                    /*color=*/straight_tl_color,
                    /*type=*/voy::TrafficLight::NORMAL,
                    /*countdown_number=*/-1, /*is_flashing=*/is_flashing);
    UpdateWorldModel();
  }
};

TEST_F(GetFirstTrafficLightAheadTest, FoundFirstTrafficLightTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);

  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.5);

  InitTrafficLightInfo(/*left_turn_tl_signal_id=*/3055,
                       /*left_turn_tl_color=*/voy::TrafficLight::RED,
                       /*straight_tl_signal_id=*/3057,
                       /*straight_tl_color=*/voy::TrafficLight::RED,
                       /*is_flashing=*/false);

  UpdateWorldModel();
  CreatePathWithLaneSequence({9023, 9711, 57541},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  Update();

  const traffic_rules::TrafficLightInfo* first_traffic_light =
      GetFirstTrafficLightAhead(
          trajectory_info().traffic_rules().traffic_lights);
  ASSERT_NE(first_traffic_light, nullptr);
  EXPECT_EQ(first_traffic_light->traffic_light_detected()->signal_id(), 3057);
}

TEST_F(GetFirstTrafficLightAheadTest, FoundNoTrafficLightTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kNoSignalJunction);

  SetEgoPose(/*lane_id=*/124221, /*portion=*/0.5);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());

  Update();

  const traffic_rules::TrafficLightInfo* first_traffic_light =
      GetFirstTrafficLightAhead(
          trajectory_info().traffic_rules().traffic_lights);
  EXPECT_EQ(first_traffic_light, nullptr);
}

class GetFirstNonStraightTurnLaneAheadTest : public ::testing::Test,
                                             public ReasoningTestFixture {
 public:
  GetFirstNonStraightTurnLaneAheadTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(GetFirstNonStraightTurnLaneAheadTest, FoundNoNonStraightTurnLane) {
  SetEgoPose(/*lane_id=*/16867, /*portion=*/0.2);
  CreatePathWithLaneSequence(
      /*lane_sequence_id=*/{16867, 108357, 16885});
  EXPECT_FALSE(path().empty());

  Update();

  const pnc_map::Lane* first_non_straight_turn_lane =
      GetFirstNonStraightTurnLaneAhead(
          trajectory_info().lane_sequence_iterator());
  EXPECT_EQ(first_non_straight_turn_lane, nullptr);
}

TEST_F(GetFirstNonStraightTurnLaneAheadTest, FoundFirstNonStraightTurnLane) {
  SetEgoPose(/*lane_id=*/16863, /*portion=*/0.2);
  CreatePathWithLaneSequence(
      /*lane_sequence_id=*/{16863, 16965, 16961});
  EXPECT_FALSE(path().empty());

  Update();

  const pnc_map::Lane* first_non_straight_turn_lane =
      GetFirstNonStraightTurnLaneAhead(
          trajectory_info().lane_sequence_iterator());
  EXPECT_NE(first_non_straight_turn_lane, nullptr);
  EXPECT_EQ(first_non_straight_turn_lane->id(), 16965);
}

class GetFirstExitingRoadAheadLaneInRoundaboutTest
    : public ::testing::Test,
      public ReasoningTestFixture {
 public:
  GetFirstExitingRoadAheadLaneInRoundaboutTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest,
       ExitingRoadInSuccessorTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152704, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152704, 152713, 152707},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  Update();

  const pnc_map::Road* first_exiting_road =
      GetFirstExitingRoadAheadLaneInRoundabout(
          *trajectory_info().lane_sequence_iterator().current_lane());
  ASSERT_NE(first_exiting_road, nullptr);
  EXPECT_EQ(first_exiting_road->id(), 49547);
}

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest,
       CurrentLaneIsExitingRoadTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152782, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152782},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  Update();

  const pnc_map::Road* first_exiting_road =
      GetFirstExitingRoadAheadLaneInRoundabout(
          *trajectory_info().lane_sequence_iterator().current_lane());
  ASSERT_NE(first_exiting_road, nullptr);
  EXPECT_EQ(first_exiting_road->id(), 49547);
}

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest,
       CurrentRoadIsBrotherOfExitingRoadTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152778, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152778},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  Update();

  const pnc_map::Road* first_exiting_road =
      GetFirstExitingRoadAheadLaneInRoundabout(
          *trajectory_info().lane_sequence_iterator().current_lane());
  ASSERT_NE(first_exiting_road, nullptr);
  EXPECT_EQ(first_exiting_road->id(), 49547);
}

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest, NoExitingRoadTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/166543, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({166543},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  Update();

  const pnc_map::Road* first_exiting_road =
      GetFirstExitingRoadAheadLaneInRoundabout(
          *trajectory_info().lane_sequence_iterator().current_lane());
  EXPECT_EQ(first_exiting_road, nullptr);
}

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest,
       AgentExitingRoundaboutFromInnerLane) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152713, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152713, 152707, 152778},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  const int64_t kObjectId = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/152712, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/152712,
                                     /*start_portion=*/0.1,
                                     /*end_lane_id=*/152782,
                                     /*end_portion=*/0.8,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  EXPECT_TRUE(input.agent_trajectory_infos.front()
                  .is_agent_exiting_roundabout_from_inner_lane());
}

TEST_F(GetFirstExitingRoadAheadLaneInRoundaboutTest,
       AgentExitingRoundaboutFromInnerLaneWithEgo) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152714, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152714, 152708, 152781},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  const int64_t kObjectId = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/152712, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/152712,
                                     /*start_portion=*/0.1,
                                     /*end_lane_id=*/152782,
                                     /*end_portion=*/0.8,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  EXPECT_FALSE(input.agent_trajectory_infos.front()
                   .is_agent_exiting_roundabout_from_inner_lane());
}

TEST(ReasoningUtilTest, CalculatePassAgentReactionMinEffectiveDiscomfortTest) {
  AgentReactionParams agent_reaction_params_default_reaction_time;
  ReactionTimes reaction_times_default;
  // Set as default reaction time.
  agent_reaction_params_default_reaction_time.reaction_times =
      reaction_times_default;
  EXPECT_DOUBLE_EQ(CalculatePassAgentReactionMinEffectiveDiscomfort(
                       agent_reaction_params_default_reaction_time),
                   std::numeric_limits<double>::infinity());

  ReactionTimes reaction_times_starting_0;
  reaction_times_starting_0.pass = DiscomfortVarying(-0.1);

  AgentReactionParams agent_reaction_params_starting_0;
  agent_reaction_params_starting_0.reaction_times = reaction_times_starting_0;
  SlowDownParams slow_down_params_starting_0;
  slow_down_params_starting_0.pass.accel = DiscomfortVarying(-2.0);
  agent_reaction_params_starting_0.slow_down_params =
      slow_down_params_starting_0;
  EXPECT_DOUBLE_EQ(CalculatePassAgentReactionMinEffectiveDiscomfort(
                       agent_reaction_params_starting_0),
                   Discomforts::kMin);

  AgentReactionParams agent_reaction_params_starting_05;
  agent_reaction_params_starting_05.reaction_times = reaction_times_starting_0;
  SlowDownParams slow_down_params_starting_05;
  slow_down_params_starting_05.pass.accel =
      DiscomfortVarying({{0.0, 0.0}, {0.25, 0.0}, {0.5, -2.0}, {1.0, -5.0}});
  agent_reaction_params_starting_05.slow_down_params =
      slow_down_params_starting_05;
  EXPECT_DOUBLE_EQ(CalculatePassAgentReactionMinEffectiveDiscomfort(
                       agent_reaction_params_starting_05),
                   0.5);

  AgentReactionParams agent_reaction_params_starting_1;
  agent_reaction_params_starting_1.reaction_times = reaction_times_starting_0;
  SlowDownParams slow_down_params_starting_1;
  slow_down_params_starting_1.pass.accel =
      DiscomfortVarying({{0.0, 0.0}, {0.75, 0.0}, {1.0, -2.0}});
  agent_reaction_params_starting_1.slow_down_params =
      slow_down_params_starting_1;
  EXPECT_DOUBLE_EQ(CalculatePassAgentReactionMinEffectiveDiscomfort(
                       agent_reaction_params_starting_1),
                   1.0);
}

TEST(ComputeCautiousStrengthTest, Strength) {
  constexpr double likelihood = 0.2;
  constexpr double cautious_likelihood_lower_bound = 0.1;
  constexpr double cautious_likelihood_upper_bound = 0.3;

  const double cautious_driving_strength = ComputeCautiousDrivingStrength(
      likelihood, cautious_likelihood_lower_bound,
      cautious_likelihood_upper_bound);

  EXPECT_NEAR(cautious_driving_strength, 0.5, 1e-6);
}

TEST(ReasoningUtilTest, IsOverlapRegionBehindTest) {
  pb::LongitudinalRange strict_range, padded_range;
  pb::OverlapSlice slice;

  {
    strict_range.set_start(-2.0);
    padded_range.set_start(-3.0);
    pb::OverlapRegion overlap_region;
    slice.set_relative_time_in_msec(1000);
    *(slice.mutable_ego_moving_distance_padded()) = padded_range;
    *(slice.mutable_ego_moving_distance_strict()) = strict_range;
    *(overlap_region.add_overlap_slices()) = slice;
    PostProcessOverlapRegionV2(overlap_region);
    EXPECT_EQ(GetPaddedOverlapStart(overlap_region), -3.0);
    EXPECT_TRUE(
        IsPaddedOverlapRegionFullyBehind(overlap_region,
                                         /*ra_arclength_threshold=*/0.0));
    EXPECT_FALSE(
        IsPaddedOverlapRegionFullyBehind(overlap_region,
                                         /*ra_arclength_threshold=*/-3.5));
    EXPECT_TRUE(
        IsStrictOverlapRegionStartingBehind(overlap_region,
                                            /*ra_arclength_threshold=*/0.0));
    EXPECT_FALSE(
        IsStrictOverlapRegionStartingBehind(overlap_region,
                                            /*ra_arclength_threshold=*/-2.5));
  }

  {
    padded_range.set_start(-3.0);
    pb::OverlapRegion overlap_region;
    *(slice.mutable_ego_moving_distance_padded()) = padded_range;
    *(overlap_region.add_overlap_slices()) = slice;
    PostProcessOverlapRegionV2(overlap_region);
    EXPECT_EQ(GetPaddedOverlapStart(overlap_region), -3.0);
    EXPECT_TRUE(
        IsPaddedOverlapRegionFullyBehind(overlap_region,
                                         /*ra_arclength_threshold=*/-2.5));
    EXPECT_FALSE(
        IsPaddedOverlapRegionFullyBehind(overlap_region,
                                         /*ra_arclength_threshold=*/-3.5));
    EXPECT_FALSE(
        IsStrictOverlapRegionStartingBehind(overlap_region,
                                            /*ra_arclength_threshold=*/-2.5));
    EXPECT_FALSE(
        IsStrictOverlapRegionStartingBehind(overlap_region,
                                            /*ra_arclength_threshold=*/-3.5));
  }
}

TEST(ReasoningUtilTest, ReversingAgentIsStrictOverlapRegionStartingBehindTest) {
  pb::LongitudinalRange strict_range, padded_range;
  pb::OverlapSlice slice;
  pb::OverlapRegion overlap_region;
  for (size_t i = 0; i < 5; i++) {
    strict_range.set_start(-i);
    strict_range.set_end(-i + 1);
    padded_range.set_start(-i - 0.5);
    padded_range.set_end(-i + 1.5);
    slice.set_relative_time_in_msec(i * 100);
    *(slice.mutable_ego_moving_distance_padded()) = padded_range;
    *(slice.mutable_ego_moving_distance_strict()) = strict_range;
    *(overlap_region.add_overlap_slices()) = slice;
    PostProcessOverlapRegionV2(overlap_region);
  }
  EXPECT_FALSE(
      IsStrictOverlapRegionStartingBehind(overlap_region,
                                          /*ra_arclength_threshold=*/-4.0));
  EXPECT_TRUE(
      IsStrictOverlapRegionStartingBehind(overlap_region,
                                          /*ra_arclength_threshold=*/1.0));
}

TEST(ReasoningUtilTest, IsStrictCrossingOverlap) {
  // Strict crossing overlap.
  pb::OverlapRegion strict_crossing_overlap_1;
  strict_crossing_overlap_1.add_overlap_slices()->set_signed_lateral_gap(-1.0);
  strict_crossing_overlap_1.add_overlap_slices()->set_signed_lateral_gap(0.0);
  strict_crossing_overlap_1.add_overlap_slices()->set_signed_lateral_gap(1.0);
  EXPECT_TRUE(IsStrictCrossingOverlap(strict_crossing_overlap_1));

  pb::OverlapRegion strict_crossing_overlap_2;
  strict_crossing_overlap_2.add_overlap_slices()->set_signed_lateral_gap(-1.0);
  strict_crossing_overlap_2.add_overlap_slices()->set_signed_lateral_gap(0.0);
  EXPECT_TRUE(IsStrictCrossingOverlap(strict_crossing_overlap_1));

  // Non strict crossing overlap.
  pb::OverlapRegion non_strict_crossing_overlap;
  non_strict_crossing_overlap.add_overlap_slices()->set_signed_lateral_gap(
      -1.0);
  non_strict_crossing_overlap.add_overlap_slices()->set_signed_lateral_gap(0.0);
  EXPECT_TRUE(IsStrictCrossingOverlap(non_strict_crossing_overlap));
}

TEST(ReasoningUtilTest, IsOverlapRegionInMergeForkLanes) {
  constexpr double kOverlapStart = 1.0;
  constexpr double kOverlapEnd = 10.0;
  constexpr double kNonOverlapStart = 100.0;
  constexpr double kNonOverlapEnd = 1000.0;

  // Construct dummy input.
  traffic_rules::MergeForkLaneInLaneSequence dummy_merge_lane(
      math::Range1d(kOverlapStart, kOverlapEnd), /*lane_in=*/nullptr,
      traffic_rules::MergeForkSceneType::kLeftMerge);
  traffic_rules::EgoLaneMergeForkLanesInfo dummy_lane_merge_fork_info(
      {dummy_merge_lane}, /*sorted_fork_lanes_in=*/{}, /*ego_lane_in=*/nullptr);
  const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>
      dummy_lane_merge_fork_infos = {dummy_lane_merge_fork_info};

  pb::OverlapRegion overlap_region1;
  pb::LongitudinalRange* range1 =
      overlap_region1.mutable_ego_moving_distance_padded();
  range1->set_start(kOverlapStart);
  range1->set_end(kOverlapEnd);
  pb::OverlapSlice* slice1 = overlap_region1.add_overlap_slices();
  slice1->set_signed_lateral_gap(-1.0);

  pb::OverlapRegion overlap_region2;
  pb::LongitudinalRange* range2 =
      overlap_region2.mutable_ego_moving_distance_padded();
  range2->set_start(kNonOverlapStart);
  range2->set_end(kNonOverlapEnd);

  EXPECT_TRUE(IsOverlapRegionInMergeForkLanes(dummy_lane_merge_fork_infos,
                                              overlap_region1,
                                              /*is_checking_merge=*/true,
                                              /*is_active_type=*/true));
  EXPECT_FALSE(IsOverlapRegionInMergeForkLanes(dummy_lane_merge_fork_infos,
                                               overlap_region2,
                                               /*is_checking_merge=*/true,
                                               /*is_active_type=*/true));
}

TEST(ReasoningUtilTest, IsOverlapRegionInPassiveMergeForkLanes) {
  constexpr double kOverlapStart = 1.0;
  constexpr double kOverlapEnd = 10.0;
  constexpr double kNonOverlapStart = 100.0;
  constexpr double kNonOverlapEnd = 1000.0;

  // Construct dummy input.
  traffic_rules::MergeForkLaneInLaneSequence dummy_merge_lane(
      math::Range1d(kOverlapStart, kOverlapEnd), /*lane_in=*/nullptr,
      traffic_rules::MergeForkSceneType::kPassiveLeftMerge);
  traffic_rules::EgoLaneMergeForkLanesInfo dummy_lane_merge_fork_info(
      {dummy_merge_lane}, /*sorted_fork_lanes_in=*/{}, /*ego_lane_in=*/nullptr);
  const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>
      dummy_lane_merge_fork_infos = {dummy_lane_merge_fork_info};

  pb::OverlapRegion overlap_region1;
  pb::LongitudinalRange* range1 =
      overlap_region1.mutable_ego_moving_distance_padded();
  range1->set_start(kOverlapStart);
  range1->set_end(kOverlapEnd);
  pb::OverlapSlice* slice1 = overlap_region1.add_overlap_slices();
  slice1->set_signed_lateral_gap(1.0);

  pb::OverlapRegion overlap_region2;
  pb::LongitudinalRange* range2 =
      overlap_region2.mutable_ego_moving_distance_padded();
  range2->set_start(kNonOverlapStart);
  range2->set_end(kNonOverlapEnd);

  EXPECT_TRUE(IsOverlapRegionInMergeForkLanes(dummy_lane_merge_fork_infos,
                                              overlap_region1,
                                              /*is_checking_merge=*/true,
                                              /*is_active_type=*/false));
  EXPECT_FALSE(IsOverlapRegionInMergeForkLanes(dummy_lane_merge_fork_infos,
                                               overlap_region2,
                                               /*is_checking_merge=*/true,
                                               /*is_active_type=*/false));
}

TEST(ReasoningUtilTest, ExtraYieldDistanceForBlockageTest) {
  // Construct voy::TrackedObject instance.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::VEHICLE);
  tracked_object.set_id(18845);
  tracked_object.set_center_x(0.0);
  tracked_object.set_center_y(0.0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_length(2.0);
  tracked_object.set_width(1.0);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(1.0);
  tracked_object.set_velocity(0.0);
  tracked_object.add_attributes(voy::perception::PARKED_CAR);

  // Construct pb::ObjectProximityInfo instance.
  pb::ObjectProximityInfo object_proximity_info;
  object_proximity_info.set_signed_lateral_gap(0.0);
  object_proximity_info.set_signed_lateral_speed(0.0);
  object_proximity_info.set_signed_longitudinal_speed(0.0);
  const planner::pb::RequiredLateralGapConfig empty_config;
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent_proto, timestamp, pose_at_plan_init_ts);
  planner_object.set_is_primary_stationary(true);
  // TODO(mengze): clean up this.
  const PrincipledRequiredLateralGap required_lateral_gap = {
      math::PiecewiseLinearFunction{0.4},  // extra_critical_vs_speed
      math::PiecewiseLinearFunction{0.8},  // comfort_vs_speed
      math::PiecewiseLinearFunction{0.0},  // extra_comfort_vs_curvature
      0.4,
      0.0,
      0.0,
      0.0};

  // Construct ReasoningObject instance.
  const ReasoningObject reasoning_object(
      planner_object, object_proximity_info, required_lateral_gap,
      /*object_occupancy_param_ptr=*/nullptr);

  // Construct RobotState instance.
  const RobotState dummy_robot_state = test_utility::ConstructRobotState(
      /*start_x=*/0.0, /*start_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);

  DiscomfortVarying extra_yield_distance = ComputeYieldExtraDistanceForBlockage(
      reasoning_object, dummy_robot_state,
      /*comfort_yield_extra_distance=*/1.0,
      /*critical_yield_extra_distance=*/0.0);
  EXPECT_NEAR(extra_yield_distance(/*discomfort=*/0.0), 4.21373, 1e-6);
  EXPECT_NEAR(extra_yield_distance(/*discomfort=*/1.0), 3.65633, 1e-6);

  extra_yield_distance = ComputeYieldExtraDistanceForBlockage(
      reasoning_object, dummy_robot_state,
      /*comfort_yield_extra_distance=*/10.0,
      /*critical_yield_extra_distance=*/0.0);
  EXPECT_NEAR(extra_yield_distance(/*discomfort=*/0.0), 10.0, 1e-6);
  EXPECT_NEAR(extra_yield_distance(/*discomfort=*/1.0), 3.65633, 1e-6);
}

class ShouldAllowEmergencyBrakeTest : public ::testing::Test,
                                      public ReasoningTestFixture {
 public:
  ShouldAllowEmergencyBrakeTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

TEST_F(ShouldAllowEmergencyBrakeTest, NearBlockingVehicleTest) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.0, /*speed=*/5.0);
  CreateLaneFollowPath(/*extend_backward=*/true);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/9023, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(/*lane_id=*/9023, /*portion=*/0.2,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  EXPECT_EQ(reasoning_inputs().size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_TRUE(ShouldAllowEmergencyBrake(world_context(), reasoning_object));
}

TEST_F(ShouldAllowEmergencyBrakeTest, FarawayBlockingVehicleTest) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.0, /*speed=*/5.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/9023, /*portion=*/0.9,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(/*lane_id=*/9023, /*portion=*/0.9,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(reasoning_inputs().size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_FALSE(ShouldAllowEmergencyBrake(world_context(), reasoning_object));
}

TEST_F(ShouldAllowEmergencyBrakeTest, NoBlockingObjectTest) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.0, /*speed=*/5.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d agent_pose(/*x_in=*/-8893.85, /*y_in=*/-33537.34,
                                /*yaw_in=*/1.69);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, agent_pose,
               /*length=*/4.0, /*width=*/3.0, /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(agent_pose, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();
  EXPECT_EQ(reasoning_inputs().size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_FALSE(ShouldAllowEmergencyBrake(world_context(), reasoning_object));
}

class ShouldAllowEmergencyBrakeForVRUTest : public ::testing::Test,
                                            public ReasoningTestFixture {
 public:
  ShouldAllowEmergencyBrakeForVRUTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

TEST_F(ShouldAllowEmergencyBrakeForVRUTest, NearCrossingVRUTest) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.2, /*speed=*/10.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/-8894.25, /*y_in=*/-33536.35,
                                /*yaw_in=*/0.35);
  const math::Pose2d end_pose(/*x_in=*/-8881.56, /*y_in=*/-33531.71,
                              /*yaw_in=*/0.35);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/4.0, /*width=*/2.0, /*velocity=*/5.0);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);
  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const std::vector<PredictionDecision>& prediction_decisions =
      wrapped_prediction_decisions().front().prediction_decisions();
  ASSERT_EQ(prediction_decisions.size(), 1);
  const PredictionDecision& prediction_decision = prediction_decisions.front();
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(ShouldAllowEmergencyBrakeForVRU(
      world_context(), agent_policy.reasoning_object(),
      agent_policy.OverlapRegion(), agent_policy.reasoner_in_charge()));
}

TEST_F(ShouldAllowEmergencyBrakeForVRUTest,
       NotAllowEBForCyclistEmergingRecently) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.2, /*speed=*/10.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/-8894.25, /*y_in=*/-33536.35,
                                /*yaw_in=*/1.38);
  const math::Pose2d end_pose(/*x_in=*/-8891.23, /*y_in=*/-33520.49,
                              /*yaw_in=*/1.38);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/4.0, /*width=*/2.0, /*velocity=*/6.0);
  agent.mutable_tracked_object()->set_track_age(200);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);
  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const std::vector<PredictionDecision>& prediction_decisions =
      wrapped_prediction_decisions().front().prediction_decisions();
  ASSERT_EQ(prediction_decisions.size(), 1);
  const PredictionDecision& prediction_decision = prediction_decisions.front();
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_FALSE(ShouldAllowEmergencyBrakeForVRU(
      world_context(), agent_policy.reasoning_object(),
      agent_policy.OverlapRegion(), agent_policy.reasoner_in_charge()));
}

TEST_F(ShouldAllowEmergencyBrakeForVRUTest, AllowEBForMergingCyclist) {
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.2, /*speed=*/10.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/-8894.25, /*y_in=*/-33536.35,
                                /*yaw_in=*/1.38);
  const math::Pose2d end_pose(/*x_in=*/-8891.23, /*y_in=*/-33520.49,
                              /*yaw_in=*/1.38);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/4.0, /*width=*/2.0, /*velocity=*/6.0);
  agent.mutable_tracked_object()->set_track_age(3000);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);
  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const std::vector<PredictionDecision>& prediction_decisions =
      wrapped_prediction_decisions().front().prediction_decisions();
  ASSERT_EQ(prediction_decisions.size(), 1);
  const PredictionDecision& prediction_decision = prediction_decisions.front();
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(ShouldAllowEmergencyBrakeForVRU(
      world_context(), agent_policy.reasoning_object(),
      agent_policy.OverlapRegion(), agent_policy.reasoner_in_charge()));
}

// This class could be used for agent trajectory info and its overlap region
// info test.
class AgentTrajectoryInfoTest : public ::testing::Test,
                                public ReasoningTestFixture {
 public:
  AgentTrajectoryInfoTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

TEST_F(AgentTrajectoryInfoTest, IsCutInOverlapRegionTest) {
  SetEgoPose(/*lane_id=*/9141, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9141, 9157});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9143,
               /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/15.0);

  // Construct agent's predicted trajectory is turning right.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_cut_in);
}

TEST_F(AgentTrajectoryInfoTest, IsCutInOverlapRegionCornerCaseTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/145493, /*portion=*/0.1, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{145493, 145500});

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/145492,
      /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  // Construct agent's predicted trajectory.
  const math::Pose2d agent_init_pos =
      LanePointToPoseWithShift(/*lane_id=*/145492, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0);
  const double yaw_going_straight = agent_init_pos.yaw().value();
  const double yat_cut_in = yaw_going_straight - 0.8;
  const double yat_cut_out = yaw_going_straight + 0.8;
  std::vector<math::Pose2d> waypoints{agent_init_pos,
                                      {28747.54, 39147.62, yaw_going_straight},
                                      {28747.45, 39142.39, yat_cut_in},
                                      {28748.98, 39136.23, yaw_going_straight},
                                      {28752.83, 39130.23, yat_cut_out},
                                      {28755.28, 39125.30, yaw_going_straight}};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_regions().front();
  ASSERT_TRUE(overlap_region.contain_strict_overlap());
  const pb::OverlapSlice& first_slice = overlap_region.overlap_slices(0);
  const pb::OverlapSlice& last_slice =
      overlap_region.overlap_slices(overlap_region.overlap_slices().size() - 1);
  ASSERT_GT(first_slice.signed_lateral_gap() * last_slice.signed_lateral_gap(),
            0);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_cut_in);
}

TEST_F(AgentTrajectoryInfoTest, IsCutInOverlapRegionLeaveEgoPathTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/145493, /*portion=*/0.1, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{145493, 145500});

  const double yaw_going_straight =
      LanePointToPoseWithShift(/*lane_id=*/145492, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)
          .yaw()
          .value();
  const double yat_cut_out = yaw_going_straight + 0.8;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1,
      /*pose=*/{28748.98, 39136.23, yat_cut_out}, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);
  std::vector<math::Pose2d> waypoints{{28748.98, 39136.23, yat_cut_out},
                                      {28752.83, 39130.23, yat_cut_out},
                                      {28755.28, 39125.30, yaw_going_straight}};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const pb::OverlapRegion& overlap_region =
      agent_trajectory_info.overlap_regions().front();
  ASSERT_TRUE(overlap_region.contain_strict_overlap());
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_FALSE(overlap_region_info.is_cut_in);
}

TEST_F(AgentTrajectoryInfoTest, IsNOTCutInOverlapRegionTest) {
  SetEgoPose(/*lane_id=*/9141, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9141, 9157});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9143,
               /*portion=*/0.2, /*length=*/4.0, /*width=*/4.0,
               /*velocity=*/4.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/9143,
                                     /*start_portion=*/0.2,
                                     /*end_lane_id=*/9143,
                                     /*end_portion=*/0.8,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_FALSE(overlap_region_info.is_cut_in);
}

TEST_F(AgentTrajectoryInfoTest, IsTrajectoryCrossingEgoPathTest) {
  SetEgoPose(/*lane_id=*/14051, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{14051, 9143});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9713,
               /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/15.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/9713,
                                     /*start_portion=*/0.2,
                                     /*end_lane_id=*/9713,
                                     /*end_portion=*/0.9,
                                     /*likelihood=*/0.9,
                                     /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_crossing_ego_path);
}

TEST_F(AgentTrajectoryInfoTest, IsAgentMergingFromRightTurnAtJunctionTest) {
  DCHECK(!FLAGS_planning_enable_agent_predicted_trajectory_route_association);
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = true;
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/14051, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence({14051, 9143},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/10533,
      /*portion=*/0.4, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/10533, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/10533, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/3.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/10533, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/3.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.01,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.05,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};

  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();
  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_merging_from_right_turn_at_junction());
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = false;
}

TEST_F(AgentTrajectoryInfoTest, IsAgentMergingFromSideRoadTest) {
  DCHECK(!FLAGS_planning_enable_agent_predicted_trajectory_route_association);
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = true;
  SetUpSceneMap(hdmap::test_util::SceneType::kSideToMainRoad);
  SetEgoPose(/*lane_id=*/175911, /*portion=*/0.0, /*speed=*/10.0);
  CreatePathWithLaneSequence({175911, 175916},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/188793,
      /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  std::vector<math::Pose2d> waypoints;
  for (size_t i = 0; i < 10; ++i) {
    waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/188793,
                                                 /*portion=*/i * 0.1,
                                                 /*arclength_shift=*/0.0,
                                                 /*lateral_shift=*/0.0,
                                                 /*theta_shift=*/0.0));
  }
  waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/175916,
                                               /*portion=*/0,
                                               /*arclength_shift=*/0.0,
                                               /*lateral_shift=*/0.0,
                                               /*theta_shift=*/0.0));
  waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/175916,
                                               /*portion=*/1.0,
                                               /*arclength_shift=*/0.0,
                                               /*lateral_shift=*/0.0,
                                               /*theta_shift=*/0.0));
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_merging_from_side_road());
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = false;
}

TEST_F(AgentTrajectoryInfoTest, IsAgentTrajectoryOnUTurnLaneTest) {
  DCHECK(!FLAGS_planning_enable_agent_predicted_trajectory_route_association);
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = true;
  SetUpSceneMap(hdmap::test_util::SceneType::kUturn);
  SetEgoPose(/*lane_id=*/152936, /*portion=*/0.0, /*speed=*/10.0);
  CreatePathWithLaneSequence({152936, 153024},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/153034,
      /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  std::vector<math::Pose2d> waypoints;
  for (size_t i = 0; i < 10; ++i) {
    waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/153034,
                                                 /*portion=*/i * 0.1,
                                                 /*arclength_shift=*/0.0,
                                                 /*lateral_shift=*/0.0,
                                                 /*theta_shift=*/0.0));
  }
  waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/152942,
                                               /*portion=*/0,
                                               /*arclength_shift=*/0.2,
                                               /*lateral_shift=*/0.0,
                                               /*theta_shift=*/0.0));
  waypoints.push_back(LanePointToPoseWithShift(/*lane_id=*/152942,
                                               /*portion=*/1.0,
                                               /*arclength_shift=*/0.4,
                                               /*lateral_shift=*/0.0,
                                               /*theta_shift=*/0.0));
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);
  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_on_u_turn_lane());
  FLAGS_planning_enable_agent_predicted_trajectory_route_association = false;
}

TEST_F(AgentTrajectoryInfoTest, IsTrajectoryCuttingOutFromEgoPathTest) {
  SetEgoPose(/*lane_id=*/32465, /*portion=*/0.8, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{32465, 14051});

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/32465,
      /*portion=*/0.9, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  // Construct agent's predicted trajectory is turning right.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/1.0,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.2,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_cutting_out());
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_cut_out);
  EXPECT_FALSE(overlap_region_info.is_cut_in);
}

// https://drive.google.com/file/d/1mFE5wW21-pyFegIVY2NKbysdHRS4SM8A/view.
TEST_F(AgentTrajectoryInfoTest, IsTrajectoryCuttingInEgoPathTest) {
  SetEgoPose(/*lane_id=*/11317, /*portion=*/0.6, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{11317, 14045});

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/32465,
      /*portion=*/0.5, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  // Construct agent's predicted trajectory is turning right.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/0.5,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/32465, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/11317, /*portion=*/0.8,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/11317, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_cutting_in());
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_cut_in);
}

TEST_F(AgentTrajectoryInfoTest, IsInsideOvertakingTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/10539, /*portion=*/0.1, /*speed=*/6.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{10539, 9141});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9911,
               /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/15.0);
  // Construct agent's predicted trajectory is inside-overtaking.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/9911, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/10539, /*portion=*/1.0,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/1.0,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_TRUE(agent_trajectory_info.is_inside_overtaking());
}

TEST_F(AgentTrajectoryInfoTest, OverlapCoversEgoAndAgentLaneConflictTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.1);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/13317,
      /*portion=*/0.4, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);
  // Construct agent's predicted trajectory which is crossing oncoming and has
  // two overlaps.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/13317, /*portion=*/0.4,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13317, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9111, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9023, /*portion=*/0.7,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  const std::vector<route_association::MapElementAndPoseInfo> associated_route =
      reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
          reasoning_inputs().front().reasoning_object,
          agent_trajectory_info.predicted_trajectory(),
          *world_context().joint_pnc_map_service(),
          /*allow_using_for_cyclist=*/true);
  EXPECT_FALSE(associated_route.empty());

  const std::vector<OverlapRegionInfo>& overlap_region_infos =
      agent_trajectory_info.overlap_region_infos();
  EXPECT_EQ(overlap_region_infos.size(), 2);

  EXPECT_TRUE(route_association_util::OverlapCoversEgoAndAgentLaneConflict(
      associated_route, overlap_region_infos.front().covered_ego_lanes));
  EXPECT_FALSE(route_association_util::OverlapCoversEgoAndAgentLaneConflict(
      associated_route, overlap_region_infos.back().covered_ego_lanes));
}

TEST_F(AgentTrajectoryInfoTest, PredictedAverageAccelerationTest) {
  constexpr int64_t kLaneId = 128479;
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/kLaneId, /*portion=*/0.0, /*speed=*/10.0);

  CreateLaneFollowPath();

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kLaneId,
      /*start_portion=*/0.6,
      /*end_lane_id=*/kLaneId,
      /*end_portion=*/1.0,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent1, /*accel=*/-0.5);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/6.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kLaneId,
      /*start_portion=*/0.2,
      /*end_lane_id=*/kLaneId,
      /*end_portion=*/0.6,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent2, /*accel=*/-1.2);

  Update();

  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    ASSERT_EQ(reasoning_input.agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;

    const double predicted_avg_accel =
        ComputeAverageAccelerationOnPredictedTrajectory(reasoning_object,
                                                        agent_trajectory_info);
    if (reasoning_object.id() == 1) {
      EXPECT_NEAR(predicted_avg_accel, -0.5, 1e-3);
    } else if (reasoning_object.id() == 2) {
      EXPECT_NEAR(predicted_avg_accel, -1.2, 1e-3);
    }
  }
}

class DoesVRUWantToCutBehindEgoTest : public ::testing::Test,
                                      public ReasoningTestFixture {
 public:
  DoesVRUWantToCutBehindEgoTest() {
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }

 protected:
  void UpdateAgentReactionSeed(int64_t object_id) {
    auto* mutable_seed =
        mutable_previous_iter_seed().mutable_agent_reaction_seeds();

    const auto iter = mutable_seed->agent_info_map().find(object_id);
    if (iter == mutable_seed->agent_info_map().end()) {
      (*mutable_seed->mutable_agent_info_map())[object_id]
          .mutable_agent_saw_ego_interval()
          ->set_agent_first_saw_ego_timestamp(timestamp() - 100);
    }

    (*mutable_seed->mutable_agent_info_map())[object_id]
        .mutable_agent_saw_ego_interval()
        ->set_agent_last_saw_ego_timestamp(timestamp());
  }
};

// https://drive.google.com/file/d/1iZdT5PSJMCM3oaU61LZfyGJl1XqHXa6U/view?usp=drive_link
TEST_F(DoesVRUWantToCutBehindEgoTest, PedCutBehindTest) {
  SetEgoPose(/*x_pos=*/-4495.477318, /*y_pos=*/-2556.508735, /*yaw=*/1.992,
             /*speed=*/5.0, /*accel=*/0.0);
  SetEgoLane(/*current_lane_id=*/52649);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{52649, 52653});

  const math::Pose2d start_pose(/*x_in=*/-4500.020, /*y_in=*/-2555.095,
                                /*yaw_in=*/0.372);
  const math::Pose2d end_pose(/*x_in=*/-4493.310, /*y_in=*/-2552.478,
                              /*yaw_in=*/0.372);
  prediction::pb::Agent& agent = AddAgent(voy::perception::ObjectType::PED,
                                          /*id=*/1, start_pose, /*length=*/1.0,
                                          /*width=*/1.0, /*velocity=*/1.0);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);
  UpdateAgentReactionSeed(agent.tracked_object().id());

  Update();

  ASSERT_EQ(reasoning_inputs().size(), 1);
  const auto& ped_reasoning_input = reasoning_inputs().front();
  ASSERT_EQ(ped_reasoning_input.agent_trajectory_infos.size(), 1);
  const auto& ped_agent_trajectory_info =
      ped_reasoning_input.agent_trajectory_infos.front();
  ASSERT_EQ(ped_agent_trajectory_info.overlap_regions().size(), 1);
  EXPECT_TRUE(ped_reasoning_input.reasoning_object.HasSeenEgo());
  // TODO(speed): Refine the logic for IsStrictOverlapRegionStartingBehind for
  // OverlapV2. In this case the overlap_region's strict overlap starts is
  // behind but the first overlap slice does not.
  EXPECT_TRUE(DoesVRUWantToCutBehindEgo(
      world_context(), trajectory_info(), ped_reasoning_input.reasoning_object,
      ped_agent_trajectory_info.overlap_regions().front().get()));
}

class IsCyclistGoingStraightAtHookRegionTest : public ::testing::Test,
                                               public ReasoningTestFixture {
 public:
  IsCyclistGoingStraightAtHookRegionTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

// https://drive.google.com/file/d/1H9OYOroGsGYLdArwr03MmbuPGVEKf9d5/view?usp=drive_link
TEST_F(IsCyclistGoingStraightAtHookRegionTest, CyclistBehindEgoTest) {
  SetEgoPose(/*lane_id=*/13299, /*portion=*/0.1);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{13299, 10997});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, /*lane_id=*/9633,
               /*portion=*/0.8, /*length=*/3.0, /*width=*/1.0,
               /*velocity=*/5.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/9633, /*start_portion=*/0.8,
                                 /*end_lane_id=*/58083, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  const auto added_planner_object =
      planner_object_map().find(agent.tracked_object().id());
  ASSERT_TRUE(added_planner_object != planner_object_map().end());
  const auto predicted_trajectories =
      object_prediction_map().find(added_planner_object->second.id());
  ASSERT_TRUE(predicted_trajectories != object_prediction_map().end());
  ASSERT_EQ(predicted_trajectories->second.size(), 1);
  EXPECT_EQ(predicted_trajectories->second.front().id(), 1);
  const auto overlaps_for_added_agent =
      object_overlap_regions().find(agent.tracked_object().id());
  ASSERT_TRUE(overlaps_for_added_agent != object_overlap_regions().end());
  ASSERT_FALSE(overlaps_for_added_agent->second.empty());
  EXPECT_TRUE(IsCyclistGoingStraightAtHookRegion(
      trajectory_info(), added_planner_object->second,
      predicted_trajectories->second.front(),
      overlaps_for_added_agent->second.front(), /*check_right_hook=*/true));
}

class EncroachmentUtilTest : public ::testing::Test,
                             public ReasoningTestFixture {
 public:
  EncroachmentUtilTest() { SetUpRegionMap(hdmap::test_util::kFremontData); }
};

TEST_F(EncroachmentUtilTest, ComputeEncroachmentSourceLaneTests) {
  // https://drive.google.com/file/d/1GjjCs0IshPa4illy4LV3V8HImpr-PAHA/view?usp=sharing
  SetEgoLane(/*current_lane_id=*/97149);
  SetEgoPose(/*x_pos=*/-4488.589912, /*y_pos=*/-2563.061381,
             /*yaw=*/-1.07682285, /*speed=*/0.0, /*accel=*/0.0);
  mutable_ego_intention().set_homotopy(
      planner::pb::IntentionResult::PASS_FROM_RIGHT);
  const std::string path_wkt =
      "LINESTRING(-4488.589912 -2563.061381,-4488.598334 "
      "-2563.042910,-4488.679874 -2562.858587,-4488.851033 "
      "-2562.439998,-4489.086452 -2561.777169,-4489.354355 "
      "-2560.860492,-4489.618648 -2559.683638,-4489.850310 "
      "-2558.246109,-4490.047820 -2556.552875,-4490.259386 "
      "-2554.618533,-4490.561744 -2552.502952,-4491.010898 "
      "-2550.306481,-4491.615187 -2548.126494,-4492.330702 "
      "-2546.043159,-4493.082497 -2544.141002,-4493.787139 "
      "-2542.512612,-4494.369032 -2541.243537,-4494.775669 "
      "-2540.386998,-4494.997016 -2539.928871,-4495.076602 "
      "-2539.765101,-4495.088712 -2539.740173)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {97149, 97153, 97157},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());
  Update();
  EXPECT_EQ(trajectory_info().ego_current_lane_relative_state(),
            planner::pb::EgoCurrentLaneRelativeState::FullyOnTheLane);
  const pb::LongitudinalRange not_adjusted_encroachment_range =
      trajectory_info().right_lane_encroachment_info().out_of_lane_segments(0);
  const pnc_map::Lane* encroach_source_lane =
      GetEncroachmentSourceLane(trajectory_info(),
                                {not_adjusted_encroachment_range.start() -
                                     world_context().ra_to_leading_bumper(),
                                 not_adjusted_encroachment_range.end()},
                                /*is_left_encroach=*/false);
  EXPECT_EQ(encroach_source_lane->id(), 97149);
  // TODO(jinhao): Add more tests when ego is on the outside of the lane
  // sequence.
}

TEST(IsObjectOutsidePhysicalBoundaryTest, RightAgentOutPhysicalBoundary) {
  constexpr int64_t timestamp = 1000;
  prediction::pb::Agent agent_proto;
  TrafficParticipantPose traffic_participant_pose(timestamp,
                                                  agent_proto.tracked_object());
  PlannerObject planner_object(agent_proto, timestamp,
                               traffic_participant_pose);
  pb::ObjectProximityInfo object_proximity_info;
  object_proximity_info.set_abs_lateral_gap(2.0);
  object_proximity_info.set_signed_lateral_gap(-2.0);
  object_proximity_info.mutable_projected_ra_arc_length()->set_start(5.0);
  object_proximity_info.mutable_projected_ra_arc_length()->set_end(6.0);
  PrincipledRequiredLateralGap required_lateral_gap;
  ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                   required_lateral_gap,
                                   /*object_occupancy_param_ptr=*/nullptr);
  reasoning_object.set_is_fully_behind(false);

  pb::PhysicalBoundaryProximityInfoPair physical_boundary_proximity_info_pair;
  pb::PhysicalBoundaryProximityInfo* right_proximity =
      physical_boundary_proximity_info_pair.mutable_right();
  pb::PhysicalBoundaryProximityInfo* left_proximity =
      physical_boundary_proximity_info_pair.mutable_left();
  for (int i = 0; i < 20; i++) {
    right_proximity->add_ra_arc_length_on_path(i * 0.5);
    right_proximity->add_abs_distance(1.5);
    left_proximity->add_ra_arc_length_on_path(i * 0.5);
    left_proximity->add_abs_distance(5.0);
  }

  EXPECT_TRUE(IsObjectOutsidePhysicalBoundary(
      reasoning_object, physical_boundary_proximity_info_pair));
}

TEST(IsObjectOutsidePhysicalBoundaryTest, LeftAgentWithinPhysicalBoundary) {
  constexpr int64_t timestamp = 1000;
  prediction::pb::Agent agent_proto;
  TrafficParticipantPose traffic_participant_pose(timestamp,
                                                  agent_proto.tracked_object());
  PlannerObject planner_object(agent_proto, timestamp,
                               traffic_participant_pose);
  pb::ObjectProximityInfo object_proximity_info;
  object_proximity_info.set_abs_lateral_gap(2.0);
  object_proximity_info.set_signed_lateral_gap(2.0);
  object_proximity_info.mutable_projected_ra_arc_length()->set_start(5.0);
  object_proximity_info.mutable_projected_ra_arc_length()->set_end(6.0);
  PrincipledRequiredLateralGap required_lateral_gap;
  ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                   required_lateral_gap,
                                   /*object_occupancy_param_ptr=*/nullptr);
  reasoning_object.set_is_fully_behind(false);

  pb::PhysicalBoundaryProximityInfoPair physical_boundary_proximity_info_pair;
  pb::PhysicalBoundaryProximityInfo* right_proximity =
      physical_boundary_proximity_info_pair.mutable_right();
  pb::PhysicalBoundaryProximityInfo* left_proximity =
      physical_boundary_proximity_info_pair.mutable_left();
  for (int i = 0; i < 20; i++) {
    right_proximity->add_ra_arc_length_on_path(i * 0.5);
    right_proximity->add_abs_distance(1.5);
    left_proximity->add_ra_arc_length_on_path(i * 0.5);
    left_proximity->add_abs_distance(5.0);
  }

  EXPECT_FALSE(IsObjectOutsidePhysicalBoundary(
      reasoning_object, physical_boundary_proximity_info_pair));
}

TEST(ReasoningUtilTest, ComputePinchRegionsBasedOnEgoLateralClearance) {
  constexpr int64_t kTotalSize = 101;
  constexpr double KDeltaLength = 1.0;
  constexpr double KDefaultClearance = 5.0;
  constexpr double KInRangeClearance = 2.0;
  const std::vector<std::vector<double>> left_clearances_ranges = {
      {20.0, 25.0}, {32.0, 35.0}};
  const std::vector<std::vector<double>> right_clearances_ranges = {
      {20.0, 25.0}, {28.0, 35.0}};

  auto in_range_checker =
      [](const std::vector<std::vector<double>>& clearances_ranges,
         const double pos) -> bool {
    return std::find_if(clearances_ranges.begin(), clearances_ranges.end(),
                        [pos](const std::vector<double>& range) {
                          return range[0] <= pos && range[1] >= pos;
                        }) != clearances_ranges.end();
  };

  // Construct ego lateral clearance info.
  std::vector<double> left_clearances(kTotalSize, KDefaultClearance);
  std::vector<double> right_clearances(kTotalSize, KDefaultClearance);
  std::vector<double> moving_distances(kTotalSize, 0.0);
  for (size_t i = 0; i < kTotalSize; ++i) {
    const double current_position = i * KDeltaLength;
    // Set the left clearance.
    if (in_range_checker(left_clearances_ranges, current_position)) {
      left_clearances[i] = KInRangeClearance;
    }
    // Set the right clearance.
    if (in_range_checker(right_clearances_ranges, current_position)) {
      right_clearances[i] = KInRangeClearance;
    }
    // Set the ego moving distance.
    moving_distances[i] = current_position;
  }

  speed::pb::EgoLateralClearanceInfo ego_lateral_clearance;
  *ego_lateral_clearance.mutable_left_clearances() = {left_clearances.begin(),
                                                      left_clearances.end()};
  *ego_lateral_clearance.mutable_right_clearances() = {right_clearances.begin(),
                                                       right_clearances.end()};
  *ego_lateral_clearance.mutable_ego_moving_distances() = {
      moving_distances.begin(), moving_distances.end()};

  ASSERT_EQ(ego_lateral_clearance.left_clearances().size(),
            ego_lateral_clearance.ego_moving_distances().size());
  ASSERT_EQ(ego_lateral_clearance.right_clearances().size(),
            ego_lateral_clearance.ego_moving_distances().size());

  // Configure the computation config.
  speed::pb::PinchRegionComputationConfigForAgent config;
  config.set_min_clearance_to_consider_pinch_region(2.5);
  config.set_min_gap_between_pinch_regions(5.0);

  // Compute the left pinch points.
  std::vector<speed::PinchRegion> left_pinch_regions =
      ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance,
          config.min_clearance_to_consider_pinch_region(),
          config.min_gap_between_pinch_regions(),
          /*is_left=*/true, /*debug=*/nullptr);

  EXPECT_EQ(left_pinch_regions.size(), 2);
  EXPECT_EQ(left_pinch_regions.at(0).start_index, 20);
  EXPECT_EQ(left_pinch_regions.at(0).end_index, 25);
  EXPECT_NEAR(left_pinch_regions.at(0).start_ra_arclength, 20.0, 1e-6);
  EXPECT_NEAR(left_pinch_regions.at(0).end_ra_arclength, 25.0, 1e-6);
  EXPECT_NEAR(left_pinch_regions.at(0).min_clearance, KInRangeClearance, 1e-6);

  EXPECT_EQ(left_pinch_regions.at(1).start_index, 32);
  EXPECT_EQ(left_pinch_regions.at(1).end_index, 35);
  EXPECT_NEAR(left_pinch_regions.at(1).start_ra_arclength, 32.0, 1e-6);
  EXPECT_NEAR(left_pinch_regions.at(1).end_ra_arclength, 35.0, 1e-6);
  EXPECT_NEAR(left_pinch_regions.at(1).min_clearance, KInRangeClearance, 1e-6);

  // Compute the right pinch points.
  std::vector<speed::PinchRegion> right_pinch_regions =
      ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance,
          config.min_clearance_to_consider_pinch_region(),
          config.min_gap_between_pinch_regions(),
          /*is_left=*/false, /*debug=*/nullptr);

  // Verify that the two pinch points are merged into single one because of the
  // min gap between pinch points threshold.
  EXPECT_EQ(right_pinch_regions.size(), 1);
  EXPECT_EQ(right_pinch_regions.at(0).start_index, 20);
  EXPECT_EQ(right_pinch_regions.at(0).end_index, 35);
  EXPECT_NEAR(right_pinch_regions.at(0).start_ra_arclength, 20.0, 1e-6);
  EXPECT_NEAR(right_pinch_regions.at(0).end_ra_arclength, 35.0, 1e-6);
  EXPECT_NEAR(right_pinch_regions.at(0).min_clearance, KInRangeClearance, 1e-6);
}

TEST(ReasoningUtilTest, ComputeIndexRangeInPinchRegionsTest) {
  // Create an overlap region.
  pb::LongitudinalRange strict_range, padded_range;
  pb::OverlapSlice slice;
  pb::OverlapRegion overlap_region;
  for (size_t i = 0; i < 20; i++) {
    strict_range.set_start(i);
    strict_range.set_end(i + 1);
    padded_range.set_start(i - 0.5);
    padded_range.set_end(i + 1.5);
    *(slice.mutable_ego_moving_distance_padded()) = padded_range;
    *(slice.mutable_ego_moving_distance_strict()) = strict_range;
    slice.set_relative_time_in_msec(i * 100);
    *(overlap_region.add_overlap_slices()) = slice;
  }
  PostProcessOverlapRegionV2(overlap_region);

  // Create the pinch regions.
  std::vector<speed::PinchRegion> pinch_regions;
  {
    speed::PinchRegion pinch_region{};
    pinch_region.start_index = 10;
    pinch_region.end_index = 15;
    pinch_region.start_ra_arclength = 10.0;
    pinch_region.end_ra_arclength = 12.5;
    pinch_region.min_clearance = 0.6;
    pinch_regions.emplace_back(std::move(pinch_region));
  }
  {
    speed::PinchRegion pinch_region{};
    pinch_region.start_index = 25;
    pinch_region.end_index = 35;
    pinch_region.start_ra_arclength = 17.5;
    pinch_region.end_ra_arclength = 22.5;
    pinch_region.min_clearance = 0.6;
    pinch_regions.emplace_back(std::move(pinch_region));
  }
  {
    speed::PinchRegion pinch_region{};
    pinch_region.start_index = 50;
    pinch_region.end_index = 60;
    pinch_region.start_ra_arclength = 30.0;
    pinch_region.end_ra_arclength = 35.0;
    pinch_region.min_clearance = 0.6;
    pinch_regions.emplace_back(std::move(pinch_region));
  }

  const std::vector<std::pair<int, int>> index_ranges =
      ComputeIndexRangeInPinchRegions(overlap_region, pinch_regions);

  EXPECT_EQ(index_ranges.size(), 2);
  EXPECT_EQ(index_ranges[0].first, 9);
  EXPECT_EQ(index_ranges[0].second, 14);
  EXPECT_EQ(index_ranges[1].first, 16);
  EXPECT_EQ(index_ranges[1].second, 20);
}

TEST(ReasoningUtilTest, ComputeMinLateralClearanceInPinchRegionTest) {
  constexpr int64_t kTotalSize = 101;
  constexpr double KDeltaLength = 1.0;
  constexpr double KDefaultClearance = 5.0;
  constexpr double KInRangeClearance = 2.0;
  const std::vector<std::vector<double>> left_clearances_ranges = {
      {20.0, 25.0}, {32.0, 35.0}};
  const std::vector<std::vector<double>> right_clearances_ranges = {
      {20.0, 25.0}, {28.0, 35.0}};

  auto in_range_checker =
      [](const std::vector<std::vector<double>>& clearances_ranges,
         const double pos) -> bool {
    return std::find_if(clearances_ranges.begin(), clearances_ranges.end(),
                        [pos](const std::vector<double>& range) {
                          return range[0] <= pos && range[1] >= pos;
                        }) != clearances_ranges.end();
  };

  // Construct ego lateral clearance info.
  std::vector<double> left_clearances(kTotalSize, KDefaultClearance);
  std::vector<double> right_clearances(kTotalSize, KDefaultClearance);
  std::vector<double> moving_distances(kTotalSize, 0.0);
  for (size_t i = 0; i < kTotalSize; ++i) {
    const double current_position = i * KDeltaLength;
    // Set the left clearance.
    if (in_range_checker(left_clearances_ranges, current_position)) {
      left_clearances[i] = KInRangeClearance;
    }
    // Set the right clearance.
    if (in_range_checker(right_clearances_ranges, current_position)) {
      right_clearances[i] = KInRangeClearance;
    }
    // Set the ego moving distance.
    moving_distances[i] = current_position;
  }

  speed::pb::EgoLateralClearanceInfo ego_lateral_clearance;
  *ego_lateral_clearance.mutable_left_clearances() = {left_clearances.begin(),
                                                      left_clearances.end()};
  *ego_lateral_clearance.mutable_right_clearances() = {right_clearances.begin(),
                                                       right_clearances.end()};
  *ego_lateral_clearance.mutable_ego_moving_distances() = {
      moving_distances.begin(), moving_distances.end()};

  ASSERT_EQ(ego_lateral_clearance.left_clearances().size(),
            ego_lateral_clearance.ego_moving_distances().size());
  ASSERT_EQ(ego_lateral_clearance.right_clearances().size(),
            ego_lateral_clearance.ego_moving_distances().size());

  EXPECT_EQ(ComputeMinLateralClearanceInPinchRegion(
                ego_lateral_clearance, /*pinch_region=*/{10.0, 20.0},
                /*pinch_side=*/math::pb::Side::kLeft),
            2.0);
  EXPECT_EQ(ComputeMinLateralClearanceInPinchRegion(
                ego_lateral_clearance, /*pinch_region=*/{26.0, 27.0},
                /*pinch_side=*/math::pb::Side::kRight),
            5.0);
  EXPECT_EQ(ComputeMinLateralClearanceInPinchRegion(
                ego_lateral_clearance, /*pinch_region=*/{26.0, 30.0},
                /*pinch_side=*/math::pb::Side::kOn),
            7.0);
}

TEST(ComputeAgentBelowSpeedThresholdDurationTest,
     AgentBelowSpeedThresholdDuration) {
  constexpr int64_t kBaseTimestamp = 1000;
  constexpr double kIncreaseSpeed = 0.4;
  pb::AgentReactionTrackerInfo agent_reaction_tracker_info;

  for (int i = 0; i < 50; i++) {
    auto* agent_state = agent_reaction_tracker_info.add_agent_state_tracking();
    agent_state->set_timestamp(i * 100 + kBaseTimestamp);
    agent_state->set_velocity((50 - i) * kIncreaseSpeed);
  }

  EXPECT_DOUBLE_EQ(ComputeAgentBelowSpeedThresholdDuration(
                       agent_reaction_tracker_info, /*current_speed=*/0.0,
                       /*low_speed_threshold=*/2.0,
                       /*current_time_stamp=*/5000 + kBaseTimestamp),
                   0.5);
}

class GetAdjustedOverlapRegionFromConstJerkTest : public ::testing::Test,
                                                  public ReasoningTestFixture {
 public:
  GetAdjustedOverlapRegionFromConstJerkTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};
TEST_F(GetAdjustedOverlapRegionFromConstJerkTest, AggressiveCutIn) {
  SetEgoPose(/*lane_id=*/145493, /*portion=*/0.1, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{145493, 145500});
  constexpr double kAgentInitAccelInMpss = -1.0;
  constexpr double kAgentMinAccelInMpss = -5.0;
  constexpr double kAgentEstimatedJerkInMpsss = -1.0;
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/145492,
      /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  // Construct cut in agent's predicted trajectory.
  const math::Pose2d agent_start_pos =
      LanePointToPoseWithShift(/*lane_id=*/145492, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0);
  const double yaw_going_straight = agent_start_pos.yaw().value();
  const double yat_cut_in = yaw_going_straight - 0.8;
  std::vector<math::Pose2d> waypoints{agent_start_pos,
                                      {28744.54, 39129.67, yat_cut_in},
                                      {28758.46, 39086.97, yaw_going_straight}};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const pb::OverlapRegion& original_overlap_region =
      agent_trajectory_info.overlap_regions().front();
  ASSERT_TRUE(original_overlap_region.contain_strict_overlap());
  const double init_speed = agent_trajectory_info.predicted_trajectory()
                                .prediction_first_pose()
                                .speed();
  const pb::OverlapRegion adjusted_overlap_region =
      GetAdjustedOverlapRegionFromConstJerk(
          agent_trajectory_info.predicted_trajectory()
              .predicted_trajectory_proto(),
          original_overlap_region, init_speed, kAgentInitAccelInMpss,
          kAgentMinAccelInMpss, kAgentEstimatedJerkInMpsss);
  // After the adjustment with slow down profile, some overlap slices are
  // unreachable.
  const int num_original_slices =
      original_overlap_region.overlap_slices().size();
  const int num_adjusted_slices =
      adjusted_overlap_region.overlap_slices().size();
  DCHECK_GT(num_original_slices, num_adjusted_slices);
  int adjusted_slice_ix = 0;
  for (int original_slice_ix = 0; original_slice_ix < num_original_slices;
       original_slice_ix++) {
    const pb::OverlapSlice& original_slice =
        original_overlap_region.overlap_slices(original_slice_ix);
    if (!math::NearZero(original_slice.signed_lateral_gap()) ||
        !HasStrictOverlap(original_slice)) {
      continue;
    }
    if (adjusted_slice_ix >= num_adjusted_slices) {
      break;
    }
    const pb::OverlapSlice& adjusted_slice =
        adjusted_overlap_region.overlap_slices(adjusted_slice_ix++);
    DCHECK_LE(original_slice.relative_time_in_sec(),
              adjusted_slice.relative_time_in_sec());
    DCHECK_GE(original_slice.signed_longitudinal_speed(),
              adjusted_slice.signed_longitudinal_speed());
  }
  DCHECK_EQ(original_overlap_region.contain_strict_overlap(),
            adjusted_overlap_region.contain_strict_overlap());
  DCHECK_EQ(original_overlap_region.object_id(),
            adjusted_overlap_region.object_id());
  DCHECK_EQ(original_overlap_region.trajectory_id(),
            adjusted_overlap_region.trajectory_id());
  DCHECK_EQ(original_overlap_region.region_id(),
            adjusted_overlap_region.region_id());
}

TEST(EstimateAgentJerkTest, AgentApproxJerkTest) {
  constexpr int64_t kBaseTimestamp = 1000;
  constexpr double kActualJerk = -1.5;
  constexpr double kDurationToEstimateJerkInSec = 0.5;
  pb::AgentReactionTrackerInfo agent_reaction_tracker_info;
  double current_accel = 1.0;
  // Returns nullopt if the agent tracker info is empty.
  EXPECT_FALSE(EstimateAgentJerk(
                   agent_reaction_tracker_info,
                   /*duration_to_estimate_jerk=*/kDurationToEstimateJerkInSec,
                   current_accel,
                   /*current_time_stamp=*/5000 + kBaseTimestamp)
                   .has_value());
  for (int i = 0; i < 50; i++) {
    auto* agent_state = agent_reaction_tracker_info.add_agent_state_tracking();
    agent_state->set_timestamp(i * 100 + kBaseTimestamp);
    agent_state->set_acceleration(current_accel);
    current_accel += 0.1 * kActualJerk;
  }
  const std::optional<double> estimated_jerk = EstimateAgentJerk(
      agent_reaction_tracker_info,
      /*duration_to_estimate_jerk=*/kDurationToEstimateJerkInSec, current_accel,
      /*current_time_stamp=*/5000 + kBaseTimestamp);

  EXPECT_TRUE(estimated_jerk.has_value());
  EXPECT_NEAR(estimated_jerk.value(), kActualJerk, math::constants::kEpsilon);
}

TEST(GenerateMergeLeaderOrTailgatorInfo, ValidLeaderAndTailgator) {
  constexpr int64_t kLeadAgentId = 1;
  constexpr int64_t kTailAgentId = 2;

  // Construct SpeedResult instance.
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.emplace_back(
      speed::pb::Constraint::SPEED_OBJECT, speed::pb::FenceType::kLead,
      "LeadAgent", absl::StrFormat("constraint-%d", kLeadAgentId), 1);
  constraint_result.constraints.emplace_back(
      speed::pb::Constraint::SPEED_OBJECT, speed::pb::FenceType::kLead,
      "TailAgent", absl::StrFormat("constraint-%d", kTailAgentId), 2);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::PASS);
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  const speed::SpeedResult speed_result(std::move(speed_search_result),
                                        speed::SpeedPipelineMeta());

  // Construct pb::ObjectProximityInfo instance.
  std::map<ObjectId, speed::pb::ObjectProximityInfo> object_proximity_info_map;
  {
    pb::ObjectProximityInfo object_proximity_info;
    object_proximity_info.set_relative_motion_heading(0.0);
    object_proximity_info.set_relative_box_heading(0.0);
    object_proximity_info.set_signed_lateral_gap(0.0);
    object_proximity_info.set_abs_lateral_gap(0.0);
    object_proximity_info.set_signed_lateral_speed(0.0);
    object_proximity_info.set_signed_longitudinal_speed(5.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_start(10.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_end(15.0);
    object_proximity_info_map.emplace(kLeadAgentId,
                                      std::move(object_proximity_info));
  }
  {
    pb::ObjectProximityInfo object_proximity_info;
    object_proximity_info.set_relative_motion_heading(0.0);
    object_proximity_info.set_relative_box_heading(0.0);
    object_proximity_info.set_signed_lateral_gap(0.0);
    object_proximity_info.set_abs_lateral_gap(0.0);
    object_proximity_info.set_signed_lateral_speed(0.0);
    object_proximity_info.set_signed_longitudinal_speed(5.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_start(-10.0);
    object_proximity_info.mutable_projected_ra_arc_length()->set_end(-5.0);
    object_proximity_info_map.emplace(kTailAgentId,
                                      std::move(object_proximity_info));
  }

  // Generate MergeGapCandidates instance.
  google::protobuf::RepeatedField<google::protobuf::int64> merge_gap_candidates;
  merge_gap_candidates.Add(kLeadAgentId);
  merge_gap_candidates.Add(kTailAgentId);

  // Populate merge gap leader and tailgator.
  double pass_or_yield_state_upper_bound = std::numeric_limits<double>::max();
  double pass_or_yield_state_lower_bound =
      std::numeric_limits<double>::lowest();
  ObjectId lead_object_id =
      speed::reasoning_util::PopulateMergeLeaderOrTailgatorInfo(
          speed_result, object_proximity_info_map, merge_gap_candidates,
          /*for_pass=*/false,
          /*pass_or_yield_state_upper_bound=*/
          pass_or_yield_state_upper_bound,
          /*pass_or_yield_state_lower_bound=*/
          pass_or_yield_state_lower_bound);
  EXPECT_TRUE(lead_object_id == kLeadAgentId);

  ObjectId tail_object_id =
      speed::reasoning_util::PopulateMergeLeaderOrTailgatorInfo(
          speed_result, object_proximity_info_map, merge_gap_candidates,
          /*for_pass=*/true,
          /*pass_or_yield_state_upper_bound=*/
          pass_or_yield_state_upper_bound,
          /*pass_or_yield_state_lower_bound=*/
          pass_or_yield_state_lower_bound);
  EXPECT_TRUE(tail_object_id == kTailAgentId);
}

class LaneChangeReasoningUtilTest : public ::testing::Test,
                                    public ReasoningTestFixture {
 public:
  LaneChangeReasoningUtilTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};

TEST_F(LaneChangeReasoningUtilTest,
       ShouldReduceYieldHeadwayDuringLaneChangeTestWithNonLaneChange) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);
  UpdateForLaneChange();
  EXPECT_FALSE(trajectory_info().is_lane_change_in_progress());

  // Construct OverlapRegion.
  pb::OverlapRegion overlap_region;

  // Construct ReasoningObject instance.
  constexpr int64_t timestamp = 1000;
  const prediction::pb::Agent agent_proto;
  const TrafficParticipantPose traffic_participant_pose(
      timestamp, agent_proto.tracked_object());
  const PlannerObject planner_object(agent_proto, timestamp,
                                     traffic_participant_pose);
  const pb::ObjectProximityInfo object_proximity_info;
  const PrincipledRequiredLateralGap required_lateral_gap;
  const ReasoningObject reasoning_object(
      planner_object, object_proximity_info, required_lateral_gap,
      /*object_occupancy_param_ptr=*/nullptr);

  EXPECT_DEATH(  // NOLINT, FP when checking macro EXPECT_DEATH
      ShouldReduceYieldHeadwayDuringLaneChange(
          world_context(), trajectory_info(), reasoning_object, overlap_region),
      "");
}

TEST_F(LaneChangeReasoningUtilTest,
       ShouldReduceYieldHeadwayDuringLaneChangeTestWithWeakInteraction) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);
  UpdateForLaneChange();
  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());

  // Construct OverlapRegion.
  pb::LongitudinalRange strict_range, padded_range;
  pb::OverlapSlice slice;
  strict_range.set_start(20.0);
  strict_range.set_end(24.0);
  padded_range.set_start(19.5);
  padded_range.set_end(23.5);
  pb::OverlapRegion overlap_region;
  slice.set_relative_time_in_msec(2000);
  slice.set_signed_longitudinal_speed(10.0);
  *(slice.mutable_ego_moving_distance_padded()) = padded_range;
  *(slice.mutable_ego_moving_distance_strict()) = strict_range;
  *(overlap_region.add_overlap_slices()) = slice;
  PostProcessOverlapRegionV2(overlap_region);
  EXPECT_TRUE(overlap_region.contain_strict_overlap());
  EXPECT_DOUBLE_EQ(GetStrictOverlapStart(overlap_region), 20.0);
  EXPECT_DOUBLE_EQ(GetPaddedOverlapStart(overlap_region), 19.5);

  // Construct ReasoningObject instance.
  const int64_t timestamp = 1000;
  const int64_t obj_id = 1;
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(obj_id);
  const TrafficParticipantPose traffic_participant_pose(timestamp,
                                                        tracked_object);
  const PlannerObject planner_object(agent_proto, timestamp,
                                     traffic_participant_pose);
  const pb::ObjectProximityInfo object_proximity_info;
  const PrincipledRequiredLateralGap required_lateral_gap;
  ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                   required_lateral_gap,
                                   /*object_occupancy_param_ptr=*/nullptr);
  reasoning_object.set_is_fully_ahead(true);

  CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager_.GetMutableCrossLaneInfo();
  CrossLaneRegionInfo* target_region_info =
      GetRegionInfoPtrByType(cross_lane::pb::CrossLaneRegionType::TARGET,
                             cross_lane_info.region_infos);
  EXPECT_TRUE(target_region_info != nullptr);
  CrossLaneObjectInfoList* target_region_object_info_list =
      &(target_region_info->object_info_list);
  EXPECT_TRUE(target_region_object_info_list != nullptr);
  target_region_object_info_list->reset();
  CrossLaneObjectInfo lead_info;
  lead_info.id = obj_id;
  target_region_object_info_list->Add(obj_id, lead_info);

  EXPECT_TRUE(ShouldReduceYieldHeadwayDuringLaneChange(
      world_context(), trajectory_info(), reasoning_object, overlap_region));
}

TEST_F(LaneChangeReasoningUtilTest,
       ShouldReduceYieldHeadwayDuringLaneChangeTestWithStrongInteraction) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);
  UpdateForLaneChange();
  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());

  // Construct OverlapRegion.
  pb::LongitudinalRange strict_range, padded_range;
  pb::OverlapSlice slice;
  strict_range.set_start(10.0);
  strict_range.set_end(15.0);
  padded_range.set_start(9.5);
  padded_range.set_end(14.5);
  pb::OverlapRegion overlap_region;
  slice.set_relative_time_in_msec(1000);
  slice.set_signed_longitudinal_speed(6.0);
  *(slice.mutable_ego_moving_distance_padded()) = padded_range;
  *(slice.mutable_ego_moving_distance_strict()) = strict_range;
  *(overlap_region.add_overlap_slices()) = slice;
  PostProcessOverlapRegionV2(overlap_region);
  EXPECT_TRUE(overlap_region.contain_strict_overlap());
  EXPECT_DOUBLE_EQ(GetStrictOverlapStart(overlap_region), 10.0);
  EXPECT_DOUBLE_EQ(GetPaddedOverlapStart(overlap_region), 9.5);

  // Construct ReasoningObject instance.
  const int64_t timestamp = 1000;
  const int64_t obj_id = 1;
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(obj_id);
  const TrafficParticipantPose traffic_participant_pose(timestamp,
                                                        tracked_object);
  const PlannerObject planner_object(agent_proto, timestamp,
                                     traffic_participant_pose);
  const pb::ObjectProximityInfo object_proximity_info;
  const PrincipledRequiredLateralGap required_lateral_gap;
  ReasoningObject reasoning_object(planner_object, object_proximity_info,
                                   required_lateral_gap,
                                   /*object_occupancy_param_ptr=*/nullptr);
  reasoning_object.set_is_fully_ahead(true);

  CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager_.GetMutableCrossLaneInfo();
  CrossLaneRegionInfo* target_region_info =
      GetRegionInfoPtrByType(cross_lane::pb::CrossLaneRegionType::TARGET,
                             cross_lane_info.region_infos);
  EXPECT_TRUE(target_region_info != nullptr);
  CrossLaneObjectInfoList* target_region_object_info_list =
      &(target_region_info->object_info_list);
  EXPECT_TRUE(target_region_object_info_list != nullptr);
  target_region_object_info_list->reset();
  CrossLaneObjectInfo lead_info;
  lead_info.id = obj_id;
  target_region_object_info_list->Add(obj_id, lead_info);

  EXPECT_FALSE(ShouldReduceYieldHeadwayDuringLaneChange(
      world_context(), trajectory_info(), reasoning_object, overlap_region));
}

TEST_F(LaneChangeReasoningUtilTest,
       ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentAhead) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.6,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/6.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.6,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_TRUE(reasoning_object.IsFullyAhead());

    ASSERT_EQ(reasoning_input.agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();
    ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);

    std::ostringstream debug_oss;
    const bool should_enable_discomfort_varying_soft_yield_extra_distance =
        ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceDuringLaneChange(
            world_context(), trajectory_info(), reasoning_object,
            agent_trajectory_info, agent_trajectory_info.overlap_region(0),
            debug_oss);
    if (reasoning_object.id() == 1) {
      EXPECT_TRUE(should_enable_discomfort_varying_soft_yield_extra_distance);
    } else if (reasoning_object.id() == 2) {
      EXPECT_FALSE(should_enable_discomfort_varying_soft_yield_extra_distance);
    }
  }
}

TEST_F(LaneChangeReasoningUtilTest,
       ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentNonAhead) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.3, /*speed=*/1.5);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/8.0, /*height=*/1.5, /*heading_diff_to_lane=*/-M_PI_4);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_FALSE(reasoning_object.IsFullyAhead());

    ASSERT_EQ(reasoning_input.agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();
    ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);

    std::ostringstream debug_oss;
    const bool should_enable_discomfort_varying_soft_yield_extra_distance =
        ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceDuringLaneChange(
            world_context(), trajectory_info(), reasoning_object,
            agent_trajectory_info, agent_trajectory_info.overlap_region(0),
            debug_oss);
    if (reasoning_object.id() == 1) {
      EXPECT_TRUE(should_enable_discomfort_varying_soft_yield_extra_distance);
    } else if (reasoning_object.id() == 2) {
      EXPECT_FALSE(should_enable_discomfort_varying_soft_yield_extra_distance);
    }
  }
}

TEST_F(LaneChangeReasoningUtilTest,
       ComputeExtraYieldExtraDistanceForLaneChangeAtLowSpeedTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/5.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kTargetLaneId,
      /*start_portion=*/0.6,
      /*end_lane_id=*/kTargetLaneId,
      /*end_portion=*/1.0,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent1, /*accel=*/-0.5);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/6.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kTargetLaneId,
      /*start_portion=*/0.2,
      /*end_lane_id=*/kTargetLaneId,
      /*end_portion=*/0.6,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent2, /*accel=*/-1.2);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_FALSE(reasoning_object.IsBehindEgoFrontAxle());

    ASSERT_EQ(reasoning_input.agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();

    const double extra_yield_extra_distance =
        ComputeExtraYieldExtraDistanceForLaneChange(
            reasoning_object, agent_trajectory_info,
            world_context().ego_speed());
    EXPECT_NEAR(extra_yield_extra_distance, 0.0, 1e-3);
  }
}

TEST_F(LaneChangeReasoningUtilTest,
       ComputeExtraYieldExtraDistanceForLaneChangeAtHighSpeedTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/10.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kTargetLaneId,
      /*start_portion=*/0.6,
      /*end_lane_id=*/kTargetLaneId,
      /*end_portion=*/1.0,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent1, /*accel=*/-0.5);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/6.0);
  AddStraightPredictedTrajectoryWithConstantAccel(
      /*start_lane_id=*/kTargetLaneId,
      /*start_portion=*/0.2,
      /*end_lane_id=*/kTargetLaneId,
      /*end_portion=*/0.6,
      /*likelihood=*/1.0,
      /*traj_id=*/1, agent2, /*accel=*/-1.2);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_FALSE(reasoning_object.IsBehindEgoFrontAxle());

    ASSERT_EQ(reasoning_input.agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();

    const double extra_yield_extra_distance =
        ComputeExtraYieldExtraDistanceForLaneChange(
            reasoning_object, agent_trajectory_info,
            world_context().ego_speed());
    if (reasoning_object.id() == 1) {
      EXPECT_NEAR(extra_yield_extra_distance, 0.4286, 1e-3);
    } else if (reasoning_object.id() == 2) {
      EXPECT_NEAR(extra_yield_extra_distance, 1.5, 1e-3);
    }
  }
}

TEST_F(LaneChangeReasoningUtilTest,
       ShouldBeCautiousWithLateralMovingAgentDuringLaneChangeTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.6,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0, /*height=*/1.5, /*heading_diff_to_lane=*/-M_PI_4);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());

  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_FALSE(reasoning_object.is_laterally_moving_away());

    const bool should_be_cautious_with_lateral_moving_agent_in_lc =
        ShouldBeCautiousWithLateralMovingAgentDuringLaneChange(
            trajectory_info(), reasoning_object);
    if (reasoning_object.id() == 1) {
      EXPECT_FALSE(should_be_cautious_with_lateral_moving_agent_in_lc);
    } else if (reasoning_object.id() == 2) {
      EXPECT_TRUE(should_be_cautious_with_lateral_moving_agent_in_lc);
    }
  }
}

// Verify that overlap region is correctly computed for reverse driving.
TEST_F(AgentTrajectoryInfoTest, IsTrajectoryCrossingEgoPathTestReverseDriving) {
  SetEgoPose(/*lane_id=*/14051, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{14051, 9143});
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9713,
               /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/15.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/9713,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/9713,
                                 /*end_portion=*/0.9,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();
  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info =
      agent_trajectory_info.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info.is_crossing_ego_path);
  const auto overlap_region_forward =
      agent_trajectory_info.overlap_regions().front();

  // Create the same scene but with motion mode set to BackWard.
  auto test_fixture_reverse_driving = ReasoningTestFixture();
  test_fixture_reverse_driving.SetUpSceneMap(
      hdmap::test_util::SceneType::kJunction);
  test_fixture_reverse_driving.SetEgoPose(/*lane_id=*/14051, /*portion=*/0.2,
                                          /*speed=*/10.0);
  test_fixture_reverse_driving.SetMotionMode(planner::pb::BACKWARD);
  test_fixture_reverse_driving.CreatePathWithLaneSequence(
      /*lane_sequence_id=*/{14051, 9143});
  prediction::pb::Agent& same_agent = test_fixture_reverse_driving.AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9713,
      /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);
  test_fixture_reverse_driving.AddStraightPredictedTrajectory(
      /*start_lane_id=*/9713,
      /*start_portion=*/0.2,
      /*end_lane_id=*/9713,
      /*end_portion=*/0.9,
      /*likelihood=*/0.9,
      /*traj_id=*/1, same_agent);
  test_fixture_reverse_driving.Update();
  ASSERT_FALSE(test_fixture_reverse_driving.reasoning_inputs().empty());
  ASSERT_EQ(test_fixture_reverse_driving.reasoning_inputs()
                .front()
                .agent_trajectory_infos.size(),
            1);
  const AgentTrajectoryInfo& agent_trajectory_info_reverse =
      test_fixture_reverse_driving.reasoning_inputs()
          .front()
          .agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info_reverse.overlap_regions().size(), 1);
  const OverlapRegionInfo& overlap_region_info_reverse =
      agent_trajectory_info_reverse.overlap_region_info(0);
  EXPECT_TRUE(overlap_region_info_reverse.is_crossing_ego_path);
  const auto overlap_region_reverse =
      agent_trajectory_info_reverse.overlap_regions().front();

  // Verify that the overlap regions for forward and reverse driving
  // are offset by an amount ra_to_fb - ra_to_rb.
  const auto bumper_offset_diff =
      rear_axle_to_front_bumper() - rear_axle_to_rear_bumper();
  EXPECT_NEAR(
      GetPaddedOverlapStart(overlap_region_forward),
      GetPaddedOverlapStart(overlap_region_reverse) - bumper_offset_diff, 0.1);
  EXPECT_NEAR(GetPaddedOverlapEnd(overlap_region_forward),
              GetPaddedOverlapEnd(overlap_region_reverse) - bumper_offset_diff,
              0.1);
}

TEST(SegmentOverlapRegionFromLongestHoleTest, MonotonicOverlapRegionTest) {
  constexpr double kAbsLateralGapDeltaInMeter = 0.1;
  constexpr int kOverlapSlicesSize = 20;
  constexpr double kMinAbsLateralGapThresholdInMeter = 0.3;
  pb::OverlapRegion overlap_region;
  for (int i = 0; i < kOverlapSlicesSize; i++) {
    auto* overlap_slice_ptr = overlap_region.add_overlap_slices();
    overlap_slice_ptr->set_signed_lateral_gap(kAbsLateralGapDeltaInMeter * i);
  }

  const std::vector<std::pair<int, int>> overlap_segments =
      SegmentOverlapRegionFromLongestHole(overlap_region,
                                          kMinAbsLateralGapThresholdInMeter);

  ASSERT_EQ(overlap_segments.size(), 1);
  EXPECT_EQ(overlap_segments.front().first, 0);
  EXPECT_EQ(overlap_segments.front().second, kOverlapSlicesSize);
}

TEST(SegmentOverlapRegionFromLongestHoleTest,
     IncreasingAndDecreasingOverlapRegionTest) {
  constexpr double kMinAbsLateralGapThresholdInMeter = 0.3;
  pb::OverlapRegion overlap_region;
  const std::vector<double> overlap_slices_abs_lateral_gap(
      {0.0, 0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.6, 0.5, 0.4, 0.3, 0.2, 0.1});
  for (const double abs_lateral_gap : overlap_slices_abs_lateral_gap) {
    auto* overlap_slice_ptr = overlap_region.add_overlap_slices();
    overlap_slice_ptr->set_signed_lateral_gap(abs_lateral_gap);
  }

  const std::vector<std::pair<int, int>> overlap_segments =
      SegmentOverlapRegionFromLongestHole(overlap_region,
                                          kMinAbsLateralGapThresholdInMeter);

  ASSERT_EQ(overlap_segments.size(), 2);
  EXPECT_EQ(overlap_segments.front().first, 0);
  EXPECT_EQ(overlap_segments.front().second, 7);
  EXPECT_EQ(overlap_segments.back().first, 7);
  EXPECT_EQ(overlap_segments.back().second,
            overlap_slices_abs_lateral_gap.size());
}

TEST(GetPredictionTruncationInfoTest, TestNotIgnoreAndIgnore) {
  constexpr int kNumOfOverlapSlices = 10;
  constexpr ObjectId kObjectId = 1;
  constexpr int kTrajectoryId = 1;
  constexpr int kLastOverlapSliceIndexForConstraintPartiallyIgnore = 5;
  std::map<ObjectId, std::vector<pb::OverlapRegion>>
      object_id_to_overlap_regions_map;
  std::vector<pb::OverlapRegion>& overlap_regions =
      object_id_to_overlap_regions_map[kObjectId];
  overlap_regions.emplace_back();
  pb::OverlapRegion& overlap_region = overlap_regions.back();
  overlap_region.set_region_id(0);
  overlap_region.set_object_id(kObjectId);
  overlap_region.set_trajectory_id(kTrajectoryId);
  for (int i = 0; i < kNumOfOverlapSlices; i++) {
    pb::OverlapSlice* overlap_slice = overlap_region.add_overlap_slices();
    overlap_slice->mutable_ego_moving_distance_padded()->set_start(0.0);
    overlap_slice->mutable_ego_moving_distance_padded()->set_end(1.0);
  }

  std::vector<Constraint> constraints;
  ConstraintCreator constraint_creator(
      /*leading_bumper_offset_from_ra=*/0.0,
      /*trailing_bumper_offset_from_ra=*/0.0, &constraints);
  constraint_creator.AddSpeedConstraintFromOverlapRegion(
      pb::FenceType::kOncoming, pb::ReasonerId::ONCOMING_AGENT,
      /*constraint_id*/ "not-ignore-constraint", overlap_region,
      DiscomfortVarying(1.0), kObjectId,
      GetBpUniqueId(overlap_region.object_id(),
                    overlap_region.trajectory_id()));
  constraint_creator.AddSpeedConstraintFromOverlapRegionSegment(
      pb::FenceType::kOncoming, pb::ReasonerId::ONCOMING_AGENT,
      /*constraint_id=*/"ignore-constraint", overlap_region,
      DiscomfortVarying(1.0), kObjectId,
      GetBpUniqueId(overlap_region.object_id(), overlap_region.trajectory_id()),
      /*use_discomfort_varying_overlap=*/false,
      /*start_idx=*/0,
      /*end_idx=*/kLastOverlapSliceIndexForConstraintPartiallyIgnore);
  ASSERT_EQ(constraints.size(), 2);
  const PredictionTruncationInfo constraint_0_truncation_info =
      GetPredictionTruncationInfo(constraints[0],
                                  object_id_to_overlap_regions_map);
  EXPECT_FALSE(constraint_0_truncation_info.is_truncated);

  const PredictionTruncationInfo constraint_1_truncation_info =
      GetPredictionTruncationInfo(constraints[1],
                                  object_id_to_overlap_regions_map);
  EXPECT_TRUE(constraint_1_truncation_info.is_truncated);
}

}  // namespace reasoning_util
}  // namespace speed
}  // namespace planner
