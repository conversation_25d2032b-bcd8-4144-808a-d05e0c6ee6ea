#include "exception_event_statistics.h"

#include <algorithm>
#include <exception>
#include <glog/logging.h>
#include <string>
#include <unordered_set>

#include "av_comm/car_id.h"
#include "platform_interface/platform_interface.h"
#include "status_machine/common.h"
#include "vehicle_system_level.h"

namespace system_supervisor {
namespace action_factory {
namespace ss_exception_event_statistics {

DEFINE_bool(ss_exception_event_statistics_enable_filter, false,
            "enable filter for some event.");

namespace {
[[maybe_unused]] constexpr char kVersion[] = "/version";
[[maybe_unused]] constexpr char kFaultLevelValuesKeyInRosParams[] =
    "/fault_classification_config_status";
constexpr char kFilterExceptionEventBySystemLevel[] = "010";

const std::set<std::string> kGen4EtCarIds = {
    "10237", "10238", "10239", "10240", "10241", "10242",
    "10243", "10244", "10245", "10246", "10247", "10248"};

uint8_t GetTopThreeBits(uint8_t system_available) {
  return (system_available & 0x0E);
}
}  // namespace

void FaultLevelManifest::GetFaultLevelManifest() {
  std::string fault_level_values{};
  if (!ros::param::get(kFaultLevelValuesKeyInRosParams, fault_level_values)) {
    LOG(ERROR) << "Failed to get [" << kFaultLevelValuesKeyInRosParams
               << "] from ros params.";
    return;
  }
  if (!fault_level_values.empty() &&
      LoadFaultExceptionType(fault_level_values)) {
    LOG(INFO) << "Successfully loaded FaultRecovery.";
  } else {
    LOG(ERROR) << "Failed to load FaultRecovery.";
  }
}

void FaultLevelManifest::CheckFaultClassificationConfigStatus(
    const std::shared_ptr<
        const ::fault_detector::pb::FaultClassificationConfigStatus>&
        fault_classification_config_status) {
  if (fault_classification_config_status->is_set_to_param() &&
      fault_classification_config_status->md5() !=
          fault_classification_config_status_cache_.md5()) {
    LOG(INFO) << "is_set_to_param: "
              << fault_classification_config_status->is_set_to_param()
              << ", content_version: "
              << fault_classification_config_status->content_version()
              << ", md5: " << fault_classification_config_status->md5();

    fault_classification_config_status_cache_ =
        *fault_classification_config_status.get();
    GetFaultLevelManifest();
  }
}

bool FaultLevelManifest::GetFaultExceptionType(
    const std::string& node_name, const pb::Fault& fault,
    fault_detector::pb::FaultExceptionType& fault_exception_type) {
  const auto fault_event_id = GetFaultEventId(node_name, fault);
  auto it = fault_exception_type_map_.find(fault_event_id);
  if (it == fault_exception_type_map_.end()) {
    LOG_EVERY_N(WARNING, 1000)
        << "Find failed for fault_event_id: " << fault_event_id;
    return false;
  }
  fault_exception_type = it->second;
  return true;
}

bool FaultLevelManifest::LoadFaultExceptionType(const std::string& json_str) {
  fault_detector::pb::FaultLevelValues fault_level_values{};
  if (!proto_util::ReadJsonProtoString(json_str, &fault_level_values)) {
    LOG_EVERY_N(ERROR, 500) << "ReadJsonProtoString failed.";
    return false;
  }

  std::string log_info{};
  for (const auto& fault_level : fault_level_values.fault_levels()) {
    for (const auto& fault : fault_level.fault_unit()) {
      auto fault_exception_type =
          fault.fault_level_value().fault_exception_type();
      std::string key = GetFaultEventId(fault);
      log_info += key + ",";
      fault_exception_type_map_.emplace(key, fault_exception_type);
    }
  }
  LOG_IF(INFO, !log_info.empty()) << "fault_exception_type key:\n" << log_info;

  is_loaded_fault_exception_type_ = true;
  LOG(INFO) << "fault_exception_type_map_.size: "
            << fault_exception_type_map_.size();
  return true;
}

std::string FaultLevelManifest::GetFaultEventId(const std::string& node_name,
                                                const pb::Fault& fault) {
  return node_name + "-" + fault.component() + "-" +
         fault.component_instance() + "-" + std::to_string(fault.code());
}

std::string FaultLevelManifest::GetFaultEventId(
    const fault_detector::pb::FaultUnit& fault) {
  return fault.node() + "-" + fault.component() + "-" +
         fault.component_instance() + "-" + std::to_string(fault.code());
}

void ExceptionEventStatistics::Init() {
  GetCarId();
  CheckVehicleRuntimeEnv();
  GetAcuVersion();
}

bool ExceptionEventStatistics::ShouldSkipAddFault(
    const FaultProcessingContext& context) {
  if (!IsTestRuntimeEnv() &&
      context.vehicle_mode == ::voy::VehicleMode::MANUAL) {
    return true;
  }

  if (ExceptionEventFilter(context.system_level)) {
    return true;
  }

  return false;
}

void ExceptionEventStatistics::ProcessActiveFault(
    const FaultProcessingContext& context, const std::string& node_name,
    const ::pb::Fault& fault_pb,
    fault_detector::pb::FaultExceptionType fault_exception_type,
    StatsRes& out_exception_event_statistics) {
  // 1. Check if the fault came from a new node.
  auto iter = exception_event_data_.find(node_name);
  if (iter == exception_event_data_.end()) {  // New node
    iter =
        exception_event_data_
            .insert({node_name,
                     NodeEventData(::action_factory::pb::
                                       SsExceptionEventStatistics::NORMALLY)})
            .first;
  }

  auto& current_node_event_data = iter->second;
  const std::string fault_unit = GenerateFaultUnit(node_name, fault_pb);

  auto fault_info_iter = current_node_event_data.active_faults.find(fault_unit);
  if (fault_info_iter == current_node_event_data.active_faults.end()) {
    // 2. Add new node fault_unit to exception_event_data_.
    if (ShouldSkipAddFault(context)) {
      // Skip add fault info and report event for some reason.
      // e.g.: if vehicle_mode is manual, we should not add new fault info.
      return;
    }
    current_node_event_data.AddFaultInfo(
        fault_unit, exception_event_timestamp_,
        GenerateFaultEventId(node_name, fault_pb), fault_pb,
        fault_exception_type);
  } else {
    // Fault already exist, just update timestamp.
    fault_info_iter->second.UpdateLastDetectedTimestamp(
        exception_event_timestamp_);
    if (!context.is_vehicle_mode_changed_from_manual &&
        (fault_info_iter->second.last_detected_timestamp +
         kEventDebounceDurationMs) > exception_event_timestamp_) {
      // Skip report fault event if the fault has been detected recently.
      return;
    }
  }

  // 3. Report new fault event.
  auto event_stats = SetSsExceptionEventStatistics(
      context.vehicle_mode, context.system_level, node_name,
      current_node_event_data.active_faults[fault_unit],
      vehicle_exception_id_.second, context.af_dispose_time,
      context.fault_types,
      ::action_factory::pb::SsExceptionEventStatistics::SsExceptionEventType::
          SsExceptionEventStatistics_SsExceptionEventType_FAULT_APPEARED);
  out_exception_event_statistics.emplace_back(event_stats);
}

void ExceptionEventStatistics::ProcessDisappearedFaults(
    const FaultProcessingContext& context,
    const std::unordered_set<std::string>& fault_units,
    std::vector<
        std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
        out_exception_event_statistics) {
  for (auto& [node_name, node_event_data] : exception_event_data_) {
    for (auto fault_iter = node_event_data.active_faults.begin();
         fault_iter != node_event_data.active_faults.end();) {
      auto& fault_info = fault_iter->second;
      auto curr_fault_unit =
          GenerateFaultUnit(node_name, fault_info.original_fault_detail);
      // If fault is detectable, just skip processing it.
      if (fault_units.find(curr_fault_unit) != fault_units.end()) {
        ++fault_iter;
        continue;
      }
      fault_info.UpdateDisappearedInfo(exception_event_timestamp_,
                                       vehicle_exception_id_.second,
                                       context.fault_types);
      // If fault is disappeared, report event and remove fault_info.
      if (fault_info.IsFaultTrulyDisappeared(exception_event_timestamp_)) {
        auto event_stats = SetSsExceptionEventStatistics(
            context.vehicle_mode, context.system_level, node_name, fault_info,
            fault_info.vehicle_exception_id_at_first_disappearance,
            context.af_dispose_time,
            fault_info.fault_types_at_first_disappearance,
            ::action_factory::pb::SsExceptionEventStatistics::SsExceptionEventType::
                SsExceptionEventStatistics_SsExceptionEventType_FAULT_DISAPPEARED);
        out_exception_event_statistics.emplace_back(event_stats);
        fault_iter = node_event_data.active_faults.erase(fault_iter);
      } else {
        ++fault_iter;
      }
    }
  }
}

void ExceptionEventStatistics::CheckSystemState(
    const std::shared_ptr<const ::pb::SystemState>& system_state,
    std::vector<
        std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>>&
        exception_event_statistics,
    const voy::VehicleMode& vehicle_mode,
    bool is_vehicle_mode_changed_from_manual) {
  UpdateTimestamp();
  UpdateVehicleExceptionId(system_state->system_level());
  UpdateTripID();
  // Step 1. Collect all fault_exception_type in /system_state
  std::set<fault_detector::pb::FaultExceptionType> system_fault_types{};
  for (const auto& node_health_info : system_state->node_health_infos()) {
    const auto& node_name = node_health_info.node_name();
    for (const auto& fault : node_health_info.health().faults()) {
      fault_detector::pb::FaultExceptionType fault_exception_type{
          fault_detector::pb::FaultExceptionType::NONE};
      if (!fault_level_manifest_->GetFaultExceptionType(node_name, fault,
                                                        fault_exception_type)) {
        // If fault_exception type not found, the fault_exception_type is NONE.
        LOG_EVERY_N(WARNING, 100) << "The fault_exception_type is not found.";
      }
      system_fault_types.emplace(fault_exception_type);
    }
  }

  FaultProcessingContext context(vehicle_mode, system_state->system_level(),
                                 system_state->af_dispose_time(),
                                 is_vehicle_mode_changed_from_manual,
                                 system_fault_types);
  // all fault units, use to check if the fault is appeared or disappeared
  std::unordered_set<std::string> fault_units;
  for (const auto& node_health_info : system_state->node_health_infos()) {
    const auto& node_name = node_health_info.node_name();
    for (const auto& fault : node_health_info.health().faults()) {
      // Step 2. Process active faults from system state.
      fault_detector::pb::FaultExceptionType fault_exception_type{
          fault_detector::pb::FaultExceptionType::NONE};
      fault_level_manifest_->GetFaultExceptionType(node_name, fault,
                                                   fault_exception_type);
      ProcessActiveFault(context, node_name, fault, fault_exception_type,
                         exception_event_statistics);
      fault_units.insert(GenerateFaultUnit(node_name, fault));
    }
  }

  // Step 3: Report disappeared faults and clean exception_event_data_;
  ProcessDisappearedFaults(context, fault_units, exception_event_statistics);
}

void ExceptionEventStatistics::UpdateNodeState(
    const std::shared_ptr<const ::service_restore::pb::RestoreRecord>&
        restore_record) {
  if (restore_record->restart_nodes().empty()) {
    DLOG(INFO) << ">> restart_nodes is empty.";
    return;
  }

  for (auto& event_info : exception_event_data_) {
    auto& node_event_data = event_info.second;
    if (node_event_data.node_state ==
        ::action_factory::pb::SsExceptionEventStatistics::SS_RESTORE) {
      node_event_data.node_state =
          ::action_factory::pb::SsExceptionEventStatistics::NORMALLY;
      LOG(INFO) << "UpdateNodeState [" << event_info.first << "] to NORMALLY";
    }
  }

  for (const auto& restart_node : restore_record->restart_nodes()) {
    auto iter = exception_event_data_.find(restart_node);
    LOG(INFO) << "UpdateNodeState [" << restart_node << "] to SS_RESTORE";
    if (iter != exception_event_data_.end()) {
      iter->second.node_state =
          ::action_factory::pb::SsExceptionEventStatistics::SS_RESTORE;
    } else {
      exception_event_data_[restart_node] = NodeEventData(
          ::action_factory::pb::SsExceptionEventStatistics::SS_RESTORE);
    }
  }
}

void ExceptionEventStatistics::UpdateVehicleExceptionId(
    const std::string& system_level) {
  const auto system_error_risk_level =
      status_machine::ParseSystemLevel(system_level);
  auto& [tmp_system_level, tmp_vehicle_exception_id] = vehicle_exception_id_;
  const auto tmp_system_error_risk_level =
      status_machine::ParseSystemLevel(tmp_system_level);
  if (GetTopThreeBits(system_error_risk_level.system_available) !=
          GetTopThreeBits(tmp_system_error_risk_level.system_available) ||
      system_error_risk_level.business_impact !=
          tmp_system_error_risk_level.business_impact ||
      system_error_risk_level.park_urgency_level !=
          tmp_system_error_risk_level.park_urgency_level) {
    tmp_system_level = system_level;
    tmp_vehicle_exception_id = car_id_ + "-" +
                               std::to_string(exception_event_timestamp_) +
                               "-" + system_level;
    LOG(INFO) << "Update vehicle_exception_id: "
              << vehicle_exception_id_.second;
  }
}

std::string ExceptionEventStatistics::GenerateFaultEventId(
    const std::string& node_name, const pb::Fault& fault) {
  return car_id_ + "-" + std::to_string(exception_event_timestamp_) + "-" +
         GenerateFaultUnit(node_name, fault);
}

std::string ExceptionEventStatistics::GenerateFaultUnit(
    const std::string& node_name, const pb::Fault& fault) {
  return node_name + "-" + fault.component() + "-" +
         fault.component_instance() + "-" + std::to_string(fault.code());
}

std::shared_ptr<::action_factory::pb::SsExceptionEventStatistics>
ExceptionEventStatistics::SetSsExceptionEventStatistics(
    const voy::VehicleMode& vehicle_mode, const std::string& system_level,
    const std::string& node_name, const ActiveFaultInfo& fault_info,
    const std::string& vehicle_exception_id, uint64_t af_dispose_time,
    const std::set<fault_detector::pb::FaultExceptionType>& fault_types,
    const ::action_factory::pb::SsExceptionEventStatistics::
        SsExceptionEventType& event_type) {
  auto exception_event_statistics =
      std::make_shared<::action_factory::pb::SsExceptionEventStatistics>();
  const auto fault = fault_info.original_fault_detail;
  exception_event_statistics->set_fault_event_id(fault_info.fault_event_id);
  exception_event_statistics->set_vehicle_exception_id(vehicle_exception_id);
  // For appeared fault, the first_detected_timestamp is equal to
  // exception_event_timestamp_
  exception_event_statistics->set_fault_event_time(
      fault_info.first_detected_timestamp);
  exception_event_statistics->set_car_id(car_id_);
  exception_event_statistics->set_vehicle_mode(vehicle_mode);
  exception_event_statistics->set_fault_level(fault.fault_level());
  exception_event_statistics->set_fault_type(fault_info.fault_type);
  exception_event_statistics->set_system_level(system_level);
  exception_event_statistics->set_acu_version(acu_version_);
  exception_event_statistics->set_fault_unit(
      GenerateFaultUnit(node_name, fault));
  exception_event_statistics->set_fault_cause(CheckFaultCause(node_name));
  exception_event_statistics->set_fault_appear_time(fault.timestamp());
  exception_event_statistics->set_af_dispose_time(af_dispose_time);
  exception_event_statistics->set_is_major_fault(fault.is_major_fault());
  exception_event_statistics->set_event_type(event_type);
  if (event_type ==
      ::action_factory::pb::
          SsExceptionEventStatistics_SsExceptionEventType_FAULT_DISAPPEARED) {
    exception_event_statistics->set_fault_disappear_time(
        exception_event_timestamp_);
  }
  exception_event_statistics->set_trip_id(curr_trip_id_);

  for (const auto fault_type : fault_types) {
    exception_event_statistics->add_system_fault_type(fault_type);
  }

  const auto system_error_risk_level =
      status_machine::ParseSystemLevel(vehicle_exception_id_.first);
  pb::SsVehicleParkingType::Type vehicle_parking_type{
      ::pb::SsVehicleParkingType::UNKNOWN};
  SsVehicleSystemLevel::GetVehicleParkingType(
      system_error_risk_level.system_available,
      system_error_risk_level.business_impact,
      system_error_risk_level.park_urgency_level, vehicle_parking_type);
  DLOG(WARNING) << "system_available: "
                << static_cast<int>(system_error_risk_level.system_available)
                << ", business_impact: "
                << static_cast<int>(system_error_risk_level.business_impact)
                << ", park_urgency_level: "
                << static_cast<int>(system_error_risk_level.park_urgency_level)
                << ", vehicle_parking_type: "
                << static_cast<int>(vehicle_parking_type);

  exception_event_statistics->set_vehicle_parking_type(vehicle_parking_type);

  UpdateSequenceCounter();
  exception_event_statistics->set_sequence_counter(
      std::to_string(startup_timestamp_) + "_" +
      std::to_string(sequence_counter_));
  return exception_event_statistics;
}

bool ExceptionEventStatistics::ExceptionEventFilter(
    const std::string& system_level) {
  if (FLAGS_ss_exception_event_statistics_enable_filter) {
    if (system_level == kFilterExceptionEventBySystemLevel) {
      return true;
    }
  }
  return false;
}

void ExceptionEventStatistics::CheckVehicleRuntimeEnv() {
  is_test_env_ = PlatformInterface::GetInstance().IsHILEnv();
  is_gen4_et_env_ = kGen4EtCarIds.find(car_id_) != kGen4EtCarIds.end();
  LOG_IF(INFO, is_test_env_ || is_gen4_et_env_)
      << "VehicleRuntimeEnv [HIL: " << is_test_env_
      << ", Gen4-ET: " << is_gen4_et_env_ << "]";
}

bool ExceptionEventStatistics::GetCarId() {
  const auto& car_id = av_comm::CarId::Get();
  car_id_ = car_id.valid() ? car_id.str() : "";
  LOG_IF_EVERY_N(WARNING, car_id_.empty(), 20) << "Car id is invalid.";
  return !car_id_.empty();
}

bool ExceptionEventStatistics::GetAcuVersion() {
  ros::param::get(kVersion, acu_version_);
  LOG_IF_EVERY_N(WARNING, acu_version_.empty(), 20)
      << "acu version is invalid.";
  return !acu_version_.empty();
}

node_state_type ExceptionEventStatistics::CheckFaultCause(
    const std::string& node_name) const {
  auto iter = exception_event_data_.find(node_name);
  if (iter == exception_event_data_.end()) {
    return ::action_factory::pb::SsExceptionEventStatistics::NORMALLY;
  }
  return iter->second.node_state;
}

void ExceptionEventStatistics::UpdateTripID() {
  try {
    std::optional<std::string> trip_id_opt = param_getter_.getTripID(true);
    if (!trip_id_opt.has_value()) {
      LOG_EVERY_N(WARNING, 100)
          << "Failed to get trip_id from param source, keep the old one: "
          << curr_trip_id_;
      return;
    }
    if (trip_id_opt->rfind("/trip_id", 0) == 0 && trip_id_opt->length() > 8) {
      curr_trip_id_ = trip_id_opt->substr(8);
    } else {
      LOG_EVERY_N(WARNING, 100)
          << "Trip ID format is incorrect: " << *trip_id_opt;
    }
  } catch (const std::exception& e) {
    LOG(ERROR) << "Failed to get trip_id and throws exception: " << e.what();
  }
}

}  // namespace ss_exception_event_statistics
}  // namespace action_factory
}  // namespace system_supervisor
