#ifndef ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_SPEED_DATA_H_
#define ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_SPEED_DATA_H_

#include <memory>
#include <optional>
#include <utility>
#include <vector>

#include "mrc_protos/mrc_request.pb.h"
#include "planner/planning_gflags.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner_protos/mrc_immediate_pullover.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "stop_cell_protos/stop_cell_data.pb.h"

namespace planner {

using StopType = hdmap::StopCellEntity::Type;

// TriggerStateChangeType indicates the change type of immediate pull over
// trigger state.
enum class TriggerStateChangeType {
  kNoChange,
  kR<PERSON>eiveTriggerSignal,
  kReceiveCancelSignal,
};

// This class manages the immediate pull over member variables and interface
// functions which are used in speed world model.
class ImmediatePullOverStateManagerSpeedData {
 public:
  ImmediatePullOverStateManagerSpeedData(
      const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
      const std::optional<math::geometry::Point2d>& immediate_pull_over_point,
      int64_t immediate_pullover_triggered_timestamp,
      double accumulated_distance, double initial_dist_to_destination,
      bool should_ignore_or_terminate_request,
      pb::ImmediatePullOverTriggerSource immediate_pullover_source,
      routing::pb::ImmediatePullOverState immediate_pullover_state,
      const std::optional<LightRegionalPath>& redirecting_regional_path,
      bool should_request_immediate_right_lane_change,
      const std::optional<pb::MatchStopCellRequest>& cloud_cell_request,
      mrc::pb::MrmType mrm_type,
      TriggerStateChangeType trigger_state_change_type,
      const std::vector<StopType>& forbidden_stop_types_for_mrm_harbor,
      const std::vector<StopType>& forbidden_stop_types_for_mrm_pullright)
      : immediate_pullover_info_(immediate_pullover_info),
        immediate_pull_over_point_(immediate_pull_over_point),
        immediate_pullover_triggered_timestamp_(
            immediate_pullover_triggered_timestamp),
        accumulated_distance_(accumulated_distance),
        initial_dist_to_destination_(initial_dist_to_destination),
        should_ignore_or_terminate_request_(should_ignore_or_terminate_request),
        immediate_pullover_source_(immediate_pullover_source),
        immediate_pullover_state_(immediate_pullover_state),
        redirecting_regional_path_(redirecting_regional_path),
        should_request_immediate_right_lane_change_(
            should_request_immediate_right_lane_change),
        cloud_cell_request_(cloud_cell_request),
        mrm_type_(mrm_type),
        trigger_state_change_type_(trigger_state_change_type),
        forbidden_stop_types_for_mrm_harbor_(
            forbidden_stop_types_for_mrm_harbor),
        forbidden_stop_types_for_mrm_pullright_(
            forbidden_stop_types_for_mrm_pullright) {}

  // Returns true if immediate pull over is triggered by mrc.
  bool IsImmediatePullOverTriggeredByMRC() const {
    return immediate_pullover_state_ !=
               routing::pb::ImmediatePullOverState::kNotTriggered &&
           (mrm_type_ == mrc::pb::MRM_HARBOR |
            mrm_type_ == mrc::pb::MRM_PULLRIGHT);
  }

  // Returns true if immediate pull over is triggered.
  bool IsImmediatePullOverTriggered() const {
    return immediate_pullover_state_ ==
               routing::pb::ImmediatePullOverState::kTriggered ||
           immediate_pullover_state_ ==
               routing::pb::ImmediatePullOverState::kSuccess;
  }

  // Returns true if planner should respond immediate pull over when it is
  // triggered.
  bool ShouldResponseToTriggeredImmediatePullOver() const {
    return !should_ignore_or_terminate_request_ &&
           IsImmediatePullOverTriggered();
  }

  bool ShouldTriggerImmediateRightLaneChangeForImmediatePullover() const {
    return should_request_immediate_right_lane_change_;
  }

  // Returns cloud cell request.
  const std::optional<pb::MatchStopCellRequest>& cloud_cell_request() const {
    return cloud_cell_request_;
  }

  // Returns true if planning trigger immediate pull over.
  bool IsTriggeredByPlanning() const {
    return IsImmediatePullOverTriggered() &&
           immediate_pullover_source_ ==
               pb::ImmediatePullOverTriggerSource::kByPlanning;
  }

  // Returns true if dhmi or phmi trigger immediate pull over.
  bool IsTriggeredByOrderService() const {
    return IsImmediatePullOverTriggered() &&
           (immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByOrderService ||
            immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByDriverHMI ||
            immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByPassengerHMI);
  }

  // Gets forbidden stop type for mrm harbor or mrc pull right.
  const std::vector<StopType> GetForbiddenStopTypesForMRC() const {
    if (mrm_type_ == mrc::pb::MRM_HARBOR) {
      return forbidden_stop_types_for_mrm_harbor_;
    }
    if (mrm_type_ == mrc::pb::MRM_PULLRIGHT) {
      return forbidden_stop_types_for_mrm_pullright_;
    }
    return std::vector<StopType>{};
  }

  // Returns true if immediate pull over is triggered by route link/route
  // node(include planning-source, routing-source and order_service-source
  // immediate pull over).
  bool IsTriggeredByRouteLink() const {
    return immediate_pullover_info_.immediate_pullover_state() ==
               routing::pb::ImmediatePullOverState::kTriggered ||
           immediate_pullover_info_.immediate_pullover_state() ==
               routing::pb::ImmediatePullOverState::kSuccess ||
           immediate_pullover_info_.immediate_pullover_state() ==
               routing::pb::ImmediatePullOverState::kFailed;
  }

  // Returns immediate pull over info from route_status.
  const routing::pb::ImmediatePullOverInfo& immediate_pullover_info() const {
    return immediate_pullover_info_;
  }

  // Returns immediate pull over source.
  pb::ImmediatePullOverTriggerSource immediate_pullover_source() const {
    return immediate_pullover_source_;
  }

  // Returns immediate pull over state which is decided by mrc node and route
  // status.
  routing::pb::ImmediatePullOverState immediate_pullover_state() const {
    return immediate_pullover_state_;
  }

  // Returns the immediate pull over from immediate pull over info in route
  // status.
  routing::pb::ImmediatePullOverState immediate_pullover_state_from_route_link()
      const {
    return immediate_pullover_info_.immediate_pullover_state();
  }

  // Returns mrc type.
  mrc::pb::MrmType mrm_type() const { return mrm_type_; }

  // Returns the trigger state change type.
  TriggerStateChangeType trigger_state_change_type() const {
    return trigger_state_change_type_;
  }

  // Returns immediate pull over triggered timestamp.
  int64_t immediate_pullover_triggered_timestamp() const {
    return immediate_pullover_triggered_timestamp_;
  }

  // Returns redirecting regional path.
  const std::optional<LightRegionalPath>& redirecting_regional_path() const {
    return redirecting_regional_path_;
  }

  // Returns the accumulated distance when immediate pull over is triggered.
  double accumulated_distance() const { return accumulated_distance_; }

  // Returns the initial_dist_to_destination for planning-source immediate pull
  // over.
  double initial_dist_to_destination() const {
    return initial_dist_to_destination_;
  }

  // Returns immediate pull over point.
  const std::optional<math::geometry::Point2d>& immediate_pull_over_point()
      const {
    return immediate_pull_over_point_;
  }

  // Returns forbidden stop types for mrm harbor.
  const std::vector<StopType>& forbidden_stop_types_for_mrm_harbor() const {
    return forbidden_stop_types_for_mrm_harbor_;
  }

  // Returns forbidden stop types for mrm pullright.
  const std::vector<StopType>& forbidden_stop_types_for_mrm_pullright() const {
    return forbidden_stop_types_for_mrm_pullright_;
  }

  // Returns true if planner should ignore or terminate request from route
  // link.
  bool should_ignore_or_terminate_request() const {
    return should_ignore_or_terminate_request_;
  }

 private:
  // The immediate pull over info from route status.
  routing::pb::ImmediatePullOverInfo immediate_pullover_info_;

  // The immediate pull over point.
  std::optional<math::geometry::Point2d> immediate_pull_over_point_ =
      std::nullopt;

  // The timestamp when immediate pull over is triggered, which is used to
  // determine whether the timeout.
  int64_t immediate_pullover_triggered_timestamp_ = -1;

  // The accumulated distance when immediate pull over is triggered.
  double accumulated_distance_ = 0.0;

  // The distance between ego and destination when planning triggered immediate
  // pull over in the first cycle.
  double initial_dist_to_destination_ = 0.0;

  // Indicates whether the planner needs to reject and terminate the immediate
  // pull over request from the route link.
  bool should_ignore_or_terminate_request_ = false;

  // The immediate pull over source.
  pb::ImmediatePullOverTriggerSource immediate_pullover_source_ =
      pb::ImmediatePullOverTriggerSource::kUnknownSource;

  // The immediate pull over state.
  routing::pb::ImmediatePullOverState immediate_pullover_state_ =
      routing::pb::ImmediatePullOverState::kNotTriggered;

  // Redirecting regional path, use for route redirecting.
  std::optional<LightRegionalPath> redirecting_regional_path_ = std::nullopt;

  // A flag to tell LC to perform immediate right lane change or not.
  bool should_request_immediate_right_lane_change_ = false;

  // Cloud cell request that will be sent to cloud through TeleAssist node.
  std::optional<pb::MatchStopCellRequest> cloud_cell_request_ = std::nullopt;

  // The mrm type.
  mrc::pb::MrmType mrm_type_ = mrc::pb::UNDEFINED;

  // The change type of trigger state.
  TriggerStateChangeType trigger_state_change_type_ =
      TriggerStateChangeType::kNoChange;

  // Forbidden stop types for mrm harbor.
  std::vector<StopType> forbidden_stop_types_for_mrm_harbor_;

  // Forbidden stop types for mrm pull right.
  std::vector<StopType> forbidden_stop_types_for_mrm_pullright_;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_SPEED_DATA_H_
