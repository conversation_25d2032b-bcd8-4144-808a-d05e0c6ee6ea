package(default_visibility = ["//visibility:public"])

cc_library(
    name = "situation_evaluation_decision",
    srcs = [
        "situation_evaluation_decision.c",
    ],
    hdrs = [
        "situation_evaluation_decision.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "long_function/situation_evaluation/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/long_function:long_function_parameter",
        "//offboard/fallback_ads/long_function:long_function_type",
    ],
)
