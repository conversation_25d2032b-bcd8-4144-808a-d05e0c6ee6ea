#include "planner/path/reasoning/agent_intention/reasoner/imaginary_agent_path_reasoner/imaginary_agent_path_reasoner.h"

#include "log_utils/log_macros.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace path {

namespace {

// The max lateral jerk/accel the repulsion should be constrainted in.
constexpr double kMaxLatJerk = 1.0;   // in m/s^3
constexpr double kMaxLatAccel = 1.0;  // in m/s^2

}  // namespace

std::unique_ptr<RepulsionMetaData>
ImaginaryAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& /* blocking_state_data */,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_ImaginaryAgentPathReasoner_PreRepulsionReason);
  DCHECK_EQ(agent_inlane_state.agent_imaginary_actual_type,
            pb::AgentImaginaryActualType::IMAGINARY);
  if (!reusable_last_path.has_value()) {
    debug_str += "no reusable_last_path";
    return nullptr;
  }
  math::geometry::Polyline2d last_path_curve_points;
  for (const auto& pose : reusable_last_path->poses()) {
    last_path_curve_points.emplace_back(pose.x_pos(), pose.y_pos());
  }

  math::geometry::Polyline2d simplified_last_path_curve_points =
      math::geometry::Simplify(std::move(last_path_curve_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_last_path_curve_points.size() < 2) {
    debug_str += "last_path_curve invalid";
    return nullptr;
  }
  const math::geometry::PolylineCurve2d last_path_curve(
      std::move(simplified_last_path_curve_points));
  math::pb::Side last_path_curve_side = last_path_curve.GetSide(
      agent_inlane_state.tracked_state.inlane_param.pose.center_2d());
  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  for (const auto& state : agent_inlane_state.predicted_trajectories
                               .at(agent_inlane_state.primary_trajectory_id)
                               .predicted_states) {
    const auto& pose = state.inlane_state.inlane_param.pose;
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    agent_center_path_points.emplace_back(pose.center_2d());
    profile_upper_bound.emplace_back(math::Ms2Sec(state.timestamp),
                                     state.odom_m,
                                     state.inlane_state.inlane_param.speed_mps,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
  }
  // TODO(Harry): Consider a brake motion for imaginary agent lower bound.
  profile_lower_bound = profile_upper_bound;
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "agent_center_path invalid";
    return nullptr;
  }
  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  // TODO(Harry): kModerate->kWeak and add truncation params.
  return std::make_unique<RepulsionMetaData>(
      last_path_curve_side == math::pb::Side::kLeft
          ? RepulsionMetaData::RepulsionDirection::kRight
          : RepulsionMetaData::RepulsionDirection::kLeft,
      agent_inlane_state.tracked_state.inlane_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound),
      agent_inlane_state.tracked_state.inlane_param.pose.heading(),
      /*add_only_overtaking_in=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      pb::AgentReasonerId::IMAGINARY_AGENT,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      agent_inlane_state.primary_trajectory_id,
      /*path_risk_mitigation_mode_in=*/true, kMaxLatAccel, kMaxLatJerk,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kBestSTRollout, std::nullopt,
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/std::nullopt, []() {
        rt_event::PostRtEvent<
            rt_event::planner::ImaginaryAgentRepulsionAdded>();
      });
}

std::unique_ptr<RepulsionMetaData>
ImaginaryAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& /* blocking_state_data */,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_ImaginaryAgentPathReasoner_PreRepulsionReason);
  DCHECK_EQ(object_occupancy_state.agent_imaginary_actual_type(),
            pb::AgentImaginaryActualType::IMAGINARY);
  if (!reusable_last_path.has_value()) {
    debug_str += "no reusable_last_path";
    return nullptr;
  }
  math::geometry::Polyline2d last_path_curve_points;
  for (const auto& pose : reusable_last_path->poses()) {
    last_path_curve_points.emplace_back(pose.x_pos(), pose.y_pos());
  }

  math::geometry::Polyline2d simplified_last_path_curve_points =
      math::geometry::Simplify(std::move(last_path_curve_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_last_path_curve_points.size() < 2) {
    debug_str += "last_path_curve invalid";
    return nullptr;
  }
  const math::geometry::PolylineCurve2d last_path_curve(
      std::move(simplified_last_path_curve_points));
  math::pb::Side last_path_curve_side =
      last_path_curve.GetSide(object_occupancy_state.pose().center_2d());
  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  for (const auto& state :
       object_occupancy_state.predicted_trajectory_occupancy_states()
           .at(object_occupancy_state.primary_trajectory_id())
           .predicted_states()) {
    const auto& pose = state.pose();
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    agent_center_path_points.emplace_back(pose.center_2d());
    profile_upper_bound.emplace_back(math::Ms2Sec(state.timestamp()),
                                     state.odom_m().value_or(0.0),
                                     state.object_occupancy_param().speed_mps,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
  }
  // TODO(Harry): Consider a brake motion for imaginary agent lower bound.
  profile_lower_bound = profile_upper_bound;
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "agent_center_path invalid";
    return nullptr;
  }
  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  return std::make_unique<RepulsionMetaData>(
      last_path_curve_side == math::pb::Side::kLeft
          ? RepulsionMetaData::RepulsionDirection::kRight
          : RepulsionMetaData::RepulsionDirection::kLeft,
      object_occupancy_state.pose().contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), object_occupancy_state.pose().heading(),
      /*add_only_overtaking_in=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      pb::AgentReasonerId::IMAGINARY_AGENT,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      object_occupancy_state.primary_trajectory_id(),
      /*path_risk_mitigation_mode_in=*/true, kMaxLatAccel, kMaxLatJerk,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kBestSTRollout, std::nullopt,
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/std::nullopt, []() {
        rt_event::PostRtEvent<
            rt_event::planner::ImaginaryAgentRepulsionAdded>();
      });
}

}  // namespace path

}  // namespace planner