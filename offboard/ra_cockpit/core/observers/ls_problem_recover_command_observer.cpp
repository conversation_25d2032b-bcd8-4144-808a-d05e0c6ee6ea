#include "ls_problem_recover_command_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

LSProblemRecoverCommandObserver::LSProblemRecoverCommandObserver(
    LSProblemRecoverCommandCallback callback)
    : callback_(std::move(callback)) {}

bool LSProblemRecoverCommandObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kLSProblemRecoverCommand);
}

std::shared_ptr<google::protobuf::Message>
LSProblemRecoverCommandObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto recover_cmd =
      std::make_shared<teleassist::pb::LSProblemRecoverCommand>();
  if (!recover_cmd->ParseFromString(message.message_data())) {
    LOG(ERROR)
        << "Received error LSProblemRecoverCommand message format, len = "
        << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(recover_cmd, data_channel);
  }
  return recover_cmd;
}

}  // namespace remote_assist
