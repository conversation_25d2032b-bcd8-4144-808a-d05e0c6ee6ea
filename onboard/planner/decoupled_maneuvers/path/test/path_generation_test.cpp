#include "planner/decoupled_maneuvers/path/path_generation.h"

#include <gtest/gtest.h>

#include "planner/global_object_manager/global_object_manager.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
class PathGenerationTest : public testing::Test,
                           public speed::ReasoningTestFixture {
 public:
  PathGenerationTest() {
    SetUpRegionMap(hdmap::test_util::kFremontData);
    SetEgoPose(/*lane_id=*/16865, /*portion=*/0.5);
    CreateLaneFollowPath();
  }
};

TEST_F(PathGenerationTest, BaseTest) {
  FLAGS_planning_enable_upl_big_turn_path = false;
  FLAGS_planning_enable_upl_small_turn_path = false;
  lane_selection::LaneSequenceCandidates candidates(
      /*waypoint_cost_map=*/nullptr);
  candidates.AddLaneSequenceResult(lane_sequence_result_);

  const auto hard_boundary_required_lat_gap =
      GetRequiredLateralGapForHardBoundaries(pb::RequiredLateralGapConfig());
  const RequiredLateralGapInfo required_lateral_gap_info =
      RequiredLateralGapInfo({}, {}, hard_boundary_required_lat_gap);

  pb::DecoupledManeuverDebug debug;
  pb::LaneChangeGeometryMetaData lane_change_geom_meta;
  CrossLaneInfoManager cross_lane_info_manager;
  std::vector<std::vector<LaneSeqRelatedInfo>> forward_lane_sequence_infos;

  GlobalObjectManager global_object_manager(
      PlannerConfigCenter::GetInstance()
          .behavior_reasoner_config()
          .decoupled_forward_maneuver_config()
          .required_lateral_gap_config(),
      PlannerConfigCenter::GetInstance()
          .world_model_config()
          .planner_object_config());

  global_object_manager.required_lateral_gap_info_ =
      std::make_unique<RequiredLateralGapInfo>(required_lateral_gap_info);

  std::unique_ptr<lateral_clearance::LateralClearanceGenerationManager>
      lateral_clearance_generation_manager = std::make_unique<
          lateral_clearance::LateralClearanceGenerationManager>();
  auto result = GeneratePathOptionsForAllBehaviors(
      candidates, pb::DecoupledManeuverSeed(), world_model(),
      lane_change_info(), pull_over_info_, required_lateral_gap_info,
      /*reusable_last_path=*/std::nullopt,
      /*reusable_last_speed_profile=*/std::nullopt, pb::MotionMode::FORWARD,
      global_object_manager, open_space::OpenSpaceInfo(), lane_change_geom_meta,
      cross_lane_info_manager, &forward_lane_sequence_infos,
      &(*lateral_clearance_generation_manager), &debug);
  ASSERT_EQ(result.size(), 2);
  EXPECT_EQ(result[0].trajectory_type, pb::NOMINAL_TRAJECTORY);
  EXPECT_EQ(result[1].trajectory_type, pb::BACKUP_TRAJECTORY);
  std::vector<std::vector<LaneSeqRelatedInfo>> reverse_lane_sequence_infos;
  auto reverse_result = GeneratePathOptionsForAllBehaviors(
      candidates, pb::DecoupledManeuverSeed(), world_model(),
      lane_change_info(), pull_over_info_, required_lateral_gap_info,
      /*reusable_last_path=*/std::nullopt,
      /*reusable_last_speed_profile=*/std::nullopt, pb::MotionMode::BACKWARD,
      global_object_manager, open_space::OpenSpaceInfo(), lane_change_geom_meta,
      cross_lane_info_manager, &reverse_lane_sequence_infos,
      &(*lateral_clearance_generation_manager), &debug);
  ASSERT_EQ(reverse_result.size(), 2);
  EXPECT_EQ(reverse_result[0].trajectory_type, pb::NOMINAL_TRAJECTORY);
  EXPECT_EQ(reverse_result[1].trajectory_type, pb::BACKUP_TRAJECTORY);
}

TEST_F(PathGenerationTest, IsCollideWithCorridorsTest) {
  std::vector<math::geometry::Point2d> left_points = {
      math::geometry::Point2d(-1, 2), math::geometry::Point2d(1, 2)};
  math::geometry::PolylineCurve2d left_boundary(left_points);
  std::vector<math::geometry::Point2d> right_points = {
      math::geometry::Point2d(-1, 1), math::geometry::Point2d(1, 1)};
  math::geometry::PolylineCurve2d right_boundary(right_points);

  std::vector<math::geometry::Point2d> collided_reference_points = {
      math::geometry::Point2d(0, 0), math::geometry::Point2d(0, 1),
      math::geometry::Point2d(0, 3)};
  math::geometry::PolylineCurve2d collided_reference_path(
      collided_reference_points);
  EXPECT_TRUE(
      math::geometry::Intersects(collided_reference_path, left_boundary) ||
      math::geometry::Intersects(collided_reference_path, right_boundary));
  std::vector<math::geometry::Point2d> not_collided_reference_points = {
      math::geometry::Point2d(-1, 1.5), math::geometry::Point2d(0, 1.5),
      math::geometry::Point2d(1, 1.5)};
  math::geometry::PolylineCurve2d not_collided_reference_path(
      not_collided_reference_points);
  EXPECT_FALSE(
      math::geometry::Intersects(not_collided_reference_path, left_boundary) ||
      math::geometry::Intersects(not_collided_reference_path, right_boundary));
}

}  // namespace planner
