{"component_name": "planner", "events": [{"name": "ConservativeAgentSlowDown", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "NoAgentSlowDownSolution", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "NegativeTtcStConflictInScr", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "FullStopFromSpeedConflictResolver", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "ExtraFullStopFromSpeedConflictResolver", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "ScrDecisionChange", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "StayStopTriggered", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PartialOptResult", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "iLqrFail", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "OptProfileNonMonotonic", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "OptProfileNotAdmissible", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "TreeSearchFailDueToMaxIteration", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RiskMitigationTriggered", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "kAdjustNoBlockFailDueToMaxIteration", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "AdjustForConflictExceedsMaxAllowedIterations", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "AdjustProfileForPassByEarliestAccelFromBrakeProfile", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SearchProfileUndershootMinProfile", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "AdjustProfileForPassSuccess", "contact": "tingranyang", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "iLqrTimedOut", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "iLqrConvergeWithHighCost", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PathGenerationGuidanceLoop", "contact": "chengding", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "iLqrNonPositiveDefiniteHessian", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "iLqrLineSearchFail", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "iLqrLineSearchUnexpectedAlpha", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "iLqrIterationCountAlert", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "AddCreepAttractor", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "DynamicRangeAbnormalNormalization", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "OptProfileNotValid", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "YieldAlwaysPossibleConstraintWithRisk", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "LfReceivedRequestFromLc", "contact": "rui<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "LfReceivedRequestFromPullOver", "contact": "rui<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UnknownObjectSpeedOverlap", "contact": "rui<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseContourForCZOverlap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseContourForDynamicObjectOverlap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseContourForStationaryObjectOverlap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseBoundingBoxForDynamicObjectOverlap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseBoundingBoxForStationaryObjectOverlap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UnknownObjectDominantSpeedConstraint", "contact": "rui<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "EmergencyBrake", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "VRUEmergencyBrake", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ConstSpeedFromSpeedConflictResolver", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "MaxSpeedFromSpeedConflictResolver", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "MaxSpeedFromSpeedConflictResolverForCyclist", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "EmergencyBrakeSelectedInSpeedSolver", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ComeToStop", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "Creep", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "CrossingSolidLineWhenUTurn", "contact": "huanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LargeHumanlikenessDifference", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "XLaneNudgeExtendBoundary", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "XLaneNudgeWithLargeLateralMovement", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "XLaneNudgeByBackupStuckSignal", "contact": "tianxi", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false}, {"name": "XLaneNudgeByConsistencySignal", "contact": "tianxi", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false}, {"name": "RepulsionExtendBoundary", "contact": "harry<PERSON>o", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "AddFakeTrajCutInRepulsion", "contact": "harry<PERSON>o", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "AddRearEndRepulsion", "contact": "harry<PERSON>o", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "UseCurvatureBasedRoadPrecedence", "contact": "harry<PERSON>o", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "RaTriggerXLaneNudge", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "TempParkedTriggerXLaneNudge", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "StartToExecuteXLaneNudge", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "FinishXLaneNudgeExecution", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "RequestLKLaneSequenceProposal", "contact": "tianxi", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false}, {"name": "RequestLKProposalForXLaneNudgeAbort", "contact": "tianxi", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false}, {"name": "DetectedTempParkedLaneBlockage", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "LaneBlockageInFrontOfEgo", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "DynamicXLaneNudgeLowEfficiencyAgent", "contact": "tianxi", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "LaneChangeMetadataConstructionError", "contact": "leonard<PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "ZeroLengthConflictingLaneOverlap", "contact": "leonard<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeSignal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeAbortResponseReceived", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeAbortResponseResponded", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeAbortResponseExecuted", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeStateTransition", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeGapValidityChange", "contact": "tingranyang", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerLaneChangeGapObjectChange", "contact": "tingranyang", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerLaneChangeEmergencyOption", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeBackupOption", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeDiscomfortOption", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeNoOption", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeFasterForQueue", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeLeadRiskOn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeLeadRiskOffAtMidDiscomfortLevel", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeCrawl", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeProbing", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeCBPFN", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeCBPFP", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeEgoPassAgentClose", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeIgnoreNudgeAgent", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeCBPUnusable", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeAddBranchingConstraints", "contact": "peng<PERSON><PERSON><PERSON>", "description": "Branching nominal constraints are added in lane change", "expiration": "2025-07-30", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangePotentialParkedCarDetected", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeStaticObjectNotInQueueDetected", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeCreep", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "DisableCreepForNonUrgentLcInDenseTraffic", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneChangeAbortDuringCreep", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "LaneChangeAbortDuringCrawl", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerRiskyLaneChange", "contact": "peng<PERSON><PERSON><PERSON>", "throttle_interval_ms": 4000, "enable_onboard": true, "disable": false}, {"name": "PlannerConsLaneChangeRestrict", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerDifficultLaneChangeForPullover", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerEarlyLaneChangeProposal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeHighRiskDetected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeHighRiskDetectedRegardlessOfReroute", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerNearLaneChangeEndBlockingTrafficDetected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLcSeqProposalRerouteHighRisk", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLcSeqProposalRerouteBlockingTraffic", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLcSeqProposalRerouteNearTrimmedLaneSeqEndOfConsLc", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLcRAInterventionRequirement", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerLcSeqProposalRerouteRearEndHazardDueToEolDuringAbort", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeStateTransitionMonitor", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "PlannerOppositeLaneChangeInstanceMonitor", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeRemainingTimesIncreasedMonitor", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerElectiveLaneChangeRoadExitCutInRiskAvoidance", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeRoadExitCutInRiskAvoidanceAccumulating", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeJunctionInRightSideCutInRiskAvoidance", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeJunctionInRightSideCutInRiskAvoidanceAccumulating", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeNeighborLaneSTMCutInRiskAvoidance", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeNeighborLaneSTMCutInRiskAvoidanceAccumulating", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeUTurnCutInRiskAvoidance", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeUTurnCutInRiskAvoidanceAccumulating", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeRiskDirectiveRiskAvoidance", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeRiskDirectiveRiskAvoidanceAccumulating", "contact": "anwu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerElectiveLaneChangeStateTransition", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerElectiveLaneChangeRiskAvoidanceStateFinish", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GenerateImmediateLaneChangeRequestForElectiveLaneChange", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PlannerElectiveLaneChangeSlowMovingAgent", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "PlannerLaneChangeGapAlignSelectSmallGap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerLaneChangeTargetRegionQueueSignal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerUseUrgencyAgentReaction", "contact": "tingranyang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "HallucinatedAgentDuringUnprotectedLeft", "contact": "pengxu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HallucinatedAgentDuringUnprotectedRight", "contact": "pengxu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HallucinatedAgentDuringUTurn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HallucinatedAgentAroundCrosswalk", "contact": "pengxu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HallucinatedAgentAroundExitZone", "contact": "pengxu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForLowerPrecedenceCrosswalk", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for lower-precedence crosswalk", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForLowerPrecedenceCrosswalkExtremeOcclusion", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for lower-precedence crosswalk", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForHigherPrecedenceCrosswalk", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for higher-precedence crosswalk", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForUnsignalizedIntersection", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for Unsignalized Intersection", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForUnprotectedLeftTurn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for Unprotected Left Turn", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForUnprotectedRightTurn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for Unprotected Right Turn", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForExitZone", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for Exit Zone", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForXLaneNudge", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for X-Lane Nudge", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForUTurn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for U-Turn", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForInvitedYielding", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for invited yielding around crosswalk", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForInvitedYieldingV2", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for invited yielding around crosswalk when ego speed is high", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForInvitedYieldingInJunction", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for invited yielding in junction", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OcclusionCautiousForInvitedYieldingInUTurn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Occlusion speed limit applied for invited yielding in u-turn", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImaginaryAgentRepulsionAdded", "contact": "harry<PERSON>o", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "AggressiveDecelCutInAgent", "contact": "jinh<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "TrafficLightBroken", "contact": "jinh<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "TrafficLightStuckInJunction", "contact": "jinh<PERSON>", "description": "Ego stuck inside the junction due to traffic light", "expiration": "2025-07-31", "throttle_interval_ms": 100000, "enable_onboard": true, "disable": false}, {"name": "RushAtEndOfTrafficLight", "contact": "jinh<PERSON>", "description": "<PERSON><PERSON> rushes to pass the end of the traffic light", "expiration": "2025-07-31", "throttle_interval_ms": 100000, "enable_onboard": true, "disable": false}, {"name": "RemoteAssistForTrafficLight", "contact": "jinh<PERSON>", "description": "The remote assist is invoked for traffic light", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CreepInDenseTraffic", "contact": "jinh<PERSON>", "description": "Ego will be more aggreesive and creep in dense traffic", "expiration": "2025-05-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "DFPBlockingTraffic", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DFPWaitingTooLong", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CreepConstraintAdded", "contact": "jinh<PERSON>", "description": "Creep constraint is added for the agent", "expiration": "2025-05-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ReasonerInChargeCornerCases", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UnstuckForCloseAgentByReducePLG", "contact": "shenqu", "throttle_interval_ms": 5000, "description": "Reset the PLG to current gap for stuck agent.", "expiration": "2025-07-30", "enable_onboard": true, "disable": false}, {"name": "AvoidRushingInUPL", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "expiration": "2025-07-30", "enable_onboard": true, "disable": false}, {"name": "ARTEffective", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ReduceYieldExtraDistForTollGateBarrier", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ARTAgentAccelerating", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ARTEgoHardToPass", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ARTTGapShrinks", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ARTExpectedToBrake", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "EnableARTInLeadAndMergeAgentReasoner", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "description": "Enable ART in specific scenes of L&M reasoner.", "expiration": "2025-07-30", "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "UncertainRiskyCautiousFromProximity", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "UncertainRiskyCautiousFromUTurn", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "UncertainRiskyCautiousFromELA", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "UncertainRiskyCautiousForCyc", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "JunctionArcLengthRangeUnsorted", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "IncreaseYieldExtraTimeForSlowAgentOnPath", "contact": "shenqu", "description": "Increasing yield extra time for slow cut out agent on ego path", "expiration": "2025-07-28", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "RiskyCutInAgentFromLeft", "contact": "shenqu", "description": "Agent cut in from left, 0.0s < TTC < 4.0s", "expiration": "2025-03-31", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "RiskyCutInAgentFromLeftWithTTCFrom2pt5sto4s", "contact": "shenqu", "description": "Agent cut in from left, 2.5s < TTC < 4.0s", "expiration": "2025-03-01", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "HighAgentDensityScenario", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ForkSequencePruned", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalAgent", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalETTC", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_WARNING", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_ERROR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_FATAL", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EB_SWERVE", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CONST_MODEL_SHADOW", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_IGNORE_EGO_PREDICTION_VEH", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_IGNORE_EGO_PREDICTION_VRU", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EB_SWERVE_LEFT_COLLISION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EB_SWERVE_RIGHT_COLLISION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EB_TALIGATOR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_HB_TALIGATOR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_ORIGIN_TALIGATOR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SOFT_BRAKE", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_ORIGIN_XLANE_TALIGATOR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_XLANE_RISK_RECALL_BY_CROSS_LANE_MARKER", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_XLANE_RISK_RECALL_BY_LATERAL_MOTION", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_TRAJECTORY_UNEXIST", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_TRAJECTORY_UNSAFE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_DIR_INCONSISTENT_AGENT", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_DIR_INCONSISTENT", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_FREE_SPACE_FILTER", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_BACKUP_GEOMETRY", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_PROVIDER_GEOMETRY", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_ELK_TRAJECTORY_AVAILABLE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_TP_SPEED_ONLY_UNSAFE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_TP_SWERVE_SHADOW", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_TP_SWERVE_AVAILABLE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_BHV_EBS_DEFAULT_UNSAFE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_EBS_EXTRA_PLAN", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_BHV_EBS_EXTRA_AVAILABLE", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_HANDLE_SIDE_REAR_COLLISION", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_TRRIGER_ES", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_TRRIGER_EB", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_TRRIGER_EB_IN_HYBRID", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_TRRIGER_EH", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_RISK_UNABLE", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_PERCEPTION_FP", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CONTROL_DRIFT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CONTROL_DRIFT_EB", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SPEED_LIMIT_FOR_LOCALIZATION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SPEED_LIMIT_FOR_CONTROL", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SPEED_LIMIT_FOR_REMOTE_SPEED_LIMIT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SPEED_LIMIT_FOR_SENSOR", "contact": "zhanghuanming", "throttle_interval_ms": 3600000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SPEED_LIMIT_FOR_EH_WARNING", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CONSTRUCTION_ZONE_COLLISION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CURB_COLLISION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_CRITICAL_EXCEPTION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_OBSTACLE_EXCEPTION_FOR_TRUCK", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_OBSTACLE_EXCEPTION_FOR_BAR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_OBSTACLE_EXCEPTION_FOR_BAR_ALL", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EARLY_PUBLISH", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EARLY_PUBLISH_NOISE", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EARLY_PUBLISH_CYC_WITHOUT_PERSON", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_SUSTAINED_OBJECT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_IRREGULAR_OBJECT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_UNKNOWN_OBJECT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_UNKNOWN_OBJECT_EARLY_PUBLISH", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_UNKNOWN_OBJECT_IS_ANIMAL", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EMERGENCY_OBJECT", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EMERGENCY_OBJECT_HB", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EMERGENCY_OBJECT_EB", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_EMERGENCY_OBJECT_ORIGIN", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_ANOMALY_DETECTION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_COLLISION_DETECTION", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_ANOMALY_DETECTION_ORIGIN", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_COMBINE_ALL", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_COMBINE_ALL_ORIGIN", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "EH_TTC_FOR_CAMERA", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_FOR_CAMERA_RELAX", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_FOR_VEHICLE_OBSTACLE", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_TTC_FINAL_TALIGATOR", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_ELK_SELECTION_PRUNING_DEFAULT_ID", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_ELK_SELECTION_SELECT_NE_DEFAULT_ID", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalLeadingObjectMissing", "contact": "zhanghuanming", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalHardBoundary", "contact": "zhanghuanming", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalDrift", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RiskSignalDriftCurbCollision", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SpeedExtraDiversity", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EH_STATE_MACHINE_DIFF_WITH_ORIGIN", "contact": "zhanghuanming", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DecoupledForwardNoTrajectory", "contact": "danxie", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PathSmootherUseRelaxedConstraints", "contact": "danxie", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PathSmootherUseWarmStartAsOutput", "contact": "danxie", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PathSmootherLargeNudgeCorridorViolation", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PathCurvatureViolation", "contact": "danxie", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerSpeedHighDiscomfort", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnPED", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnCYC", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnLV", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnBarrier", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnTrafficCone", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnVegetation", "contact": "jun<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnVehicle", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnAnimal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnTricycle", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerDominantConstraintOnLeadingVehicle", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "TriggerUnstuckForUnknownAgents", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "TriggerLaneEncroachNudgeForCrossingAgents", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "TriggerLaneEncroachNudgeForCutinAgents", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathEnlargeLeadAgentBottomBuffer", "contact": "harry<PERSON>o", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathPreventRearFrontalCollision", "contact": "harry<PERSON>o", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathLaneEncroachNudge", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathInterestedLaneEncroachNudge", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathFastSteerLaneEncroachNudge", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerPathIncreaseLvBufferForLaneEncroachNudge", "contact": "sixian<PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RepulsionCrossingNonPrimaryTrajectory", "contact": "wang<PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "IgnoreCrossingAgentNoYieldIntentionWhenEgoCanEB", "contact": "wang<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "EncroachLaneNudgeNecessaryAgentWhenSideSafe", "contact": "wang<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerNarrowMeetingPassageTrigger", "contact": "wang<PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerNarrowMeetingPassageSuccess", "contact": "wang<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerNarrowMeetingPassageByTakeover", "contact": "wang<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerRepulsionCyclistOvertakingTrigger", "contact": "wang<PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PlannerSqueezingSceneWithCyclistOvertaking", "contact": "wang<PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerTrimAdditionalIgnoreHomotopy", "contact": "wang<PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "InLaneNudgeNecessaryAgentWhenSideNotSafe", "contact": "wang<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PlannerHardBrake", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PlannerHardSwerve", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "OccupancyFullyAboveEgo", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "CutOutAgentAhead", "contact": "zhengyu", "description": "Enlarge soft constraint yield extra time for decelerating cut-out agent", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstuctionZoneNearUTurn", "contact": "zhengyu", "description": "Adjust yield params for construction zone near U-turn", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SanitationVehicleAhead", "contact": "zhengyu", "description": "Adjust yield extra distance for sanitation vehicle", "expiration": "2025-03-31", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "KeepLearZoneUnstuck", "contact": "zhengyu", "description": "When traffic flow is moving in front of ego, enable unstuck near keep clear zone", "expiration": "2025-06-30", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ProgressRecoveryActivated", "contact": "chantong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ProgressRecoveryInhibited", "contact": "chantong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutingTriggerWaypointAssist", "contact": "kewenzhong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "FixPointAutoUnstuckUploadMsg", "contact": "kewenzhong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistEndOfLaneSequence", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistEnterExitReasoning", "contact": "kewenzhong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UnexpectedAssistFeedbackType", "contact": "kewenzhong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EOLTriggerCrossAndPullOver", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistEgoInTheOppositeRoad", "contact": "ch<PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistRoadEntirelyBlocked", "contact": "ch<PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistPointNotIncremental", "contact": "ch<PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "ExtendWaypointAsssitStopline", "contact": "ch<PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "DisableLaneChangeForLightAssistDetour", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "EgoNearEndOfLaneSequence", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "RecommendReplanWhenEgoStartRemoteAssist", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "RecommendReplanWhenNearEndOfLaneSequence", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "LightAssistDetourNotAvailableByEndOfLaneSequence", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "LightAssistDetourNotAvailableByLeftNoSpace", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "LightAssistDetourNotAvailableByRightNoSpace", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "LightAssistDetourNotAvailableByUTurn", "contact": "kewenzhong", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistResponseLastCycleId", "contact": "kewenzhong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RegionalMapUpdateFailure", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "EmptyCurrentLanesForRegionalPath", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathDivergeWithLastCycle", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathFailToReachVirtualTarget", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathFailToGenerate", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathLastCycleCandidateMissing", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathGlobalRouteCandidateMissing", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RestoreLastCycleCandidateFromSeedFailed", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RestoreGlobalRouteCandidateFromSeedFailed", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathFallBackTriggered", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathFallBackGetBackupLaneFailed", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathHasLaneChangeStuckRisk", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRerouteAvoidLaneChangeStuckRisk", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRiskyByLateralDistance", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRiskyByHardBoundary", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRiskyWithHarshBrake", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "HonkSignalTriggered", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "HazardLightSignalTriggered", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "TurnSignalTriggered", "contact": "jinhaozhou", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "<PERSON>gger<PERSON><PERSON><PERSON><PERSON>ring<PERSON><PERSON>", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GenerateNudgeBoundaryError", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "DegradedDrivingMode", "contact": "tianxiaowei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DegradedDrivingModeInGlobalObjectManager", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SelectMLPullOverGap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverRequestFromPlanning", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverAtCurrentLaneWithTrailingAgent", "contact": "wang<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RTEventForTestOnly", "contact": "jinh<PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RaUnstuckScenarioTriggered", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceForRaUnstuckGenerated", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": false, "disable": false}, {"name": "RAUnstuckReverseDrivingTriggered", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MissingRedLightSpeedConstraint", "contact": "zhangjiwei", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckModelRequest", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "TriggerNearHardBoundaryFNForAssistStuckDetect", "contact": "fengkaijun", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutePropFailedToFindCurrentLane", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutePropFailToSearchLaneSequence", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutePropFailToSearchRegionalPath", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SequenceForkMergeBack", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqSearchMissLastSelectedLaneSequence", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityMisalignedJunctionLaneSelection", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalChangedRouteAfterReplan", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalMaintainRouteAfterReplan", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteDesiredReplanSucceed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteDesiredReplanFailed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ReplanFollowedLastGlobalPath", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ReplanFollowedLastRegionalPath", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ReplanFollowedNoPath", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteModelEmptyGlobalRouteLanes", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteModelEmptyGlobalNewRouteLanes", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteModelEmptyGlobalCostMap", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SegmentSeqFailToGenerate", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SegmentSeqFailToFindMandatoryLCPoint", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceQualityMonitor", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckForceTerminateByRA", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuck", "contact": "songjingru", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckShouldExecute", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckShouldTerminate", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckGenerateJumpOut", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckGenerateRelaxLC", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SectionLocatorEmptyRegionalSectionVertices", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SectionLocatorEmptyExtiSections", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SectionLocatorEmptyNearLanes", "contact": "huiluo", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PullOutTriggerAutoReverse", "contact": "l<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckTrigger", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckLateStageStuck", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckUntriggerByRearBlockage", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckUntriggerByConflictingBlockage", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckTriggerFailed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckTimeoutReplan", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckReplanAfterReverseStuck", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckConsiderComeToStopVehicleInRoi", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UTurnUnstuckAllBlockagesRemoved", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HardBoundaryDrySteeringTrigger", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityOnNotDrivableFixTimeVariableLane", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityOnNotDrivableSignalVariableLane", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityEgoOnNotDrivableBusLane", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityWithHardBlockObjects", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityWithSoftBlockObjects", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityWithHardConstraint", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqQualityWithUrgentLC", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OriginLaneInconsistentWithPose", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OriginLaneInconsistentWithRegionalPathCurrentLane", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OriginLaneIsHardConstraint", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOutEgoPositionNotReady", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstraintFailedToReadFromCloudForPlanning", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstraintDifferentVersion", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchEmptyFromNearLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchNotInCostMap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchEmptyDrivableLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchEmptyOverlappedLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchNotInLastSequence", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneSearchNotInCurrentSection", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneResEmptyPhysicalLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneResEmptyNormalLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "WorldModelUpdate", "contact": "yuze<PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneResInvalidForRegionalPath", "contact": "yuze<PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "CurrLaneResInvalidForWaypointGraph", "contact": "yuze<PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "HasManyTunnels", "contact": "sixian<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "AllNominalPathGenerationsFailed", "contact": "sixian<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "NudgeHardBlockingDuringMerge", "contact": "sixian<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SpeedSearchRolloutSelected", "contact": "harry<PERSON>o", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "LowConfidenceAgentRepulsionCreated", "contact": "mingshenchen", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ApproachToStartToMoveAgent", "contact": "harry<PERSON>o", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AddRepulsionInIgnoreHomotopy", "contact": "harry<PERSON>o", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "HighDiscomfortForStartToMoveAgent", "contact": "harry<PERSON>o", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SeqSearchWrongNumberOfLCInstances", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "BlockHasCongestedTrafficFlow", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MapChangeMeetMapChangeArea", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RAUnstuckForwardCZScenario", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RAUnstuckForwardTriggered", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "TriggerDriveThroughFoDLogic", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "TriggerUndercarriageOverFoDLogic", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "HandleDrivableFoDInPlanner", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "PopulateOvertakeForStm", "contact": "sixian<PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "NudgeBetterNotDriveFoD", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "EgoTrajectoryOffRoad", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "FilterOutOffRoadObjects", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "HasOffRoadObjects", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "NotIgnoreAnimalsInPath", "contact": "zi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckRelaxLaneDirectionSetParam", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckRelaxLaneDirectionGetLS", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverTriggeredTime", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverCompletedTime", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverCloudCellRequestAndResponse", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SeqFlickerBetweenLFandAlternativeLF", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SeqFlickerBetweenLCandNonLC", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SeqFlickerLCInstancePosition", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SeqFlickerLCInstancePositionDuringLCPrepare", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SnapshotUpdatePlanningRouteStatusReason", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "AbnormalWaypointCountInPlannerNode", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOutCrawlTrigger", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SelectUplBigTurn", "contact": "chengding", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "SelectUplSmallTurn", "contact": "chengding", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "UseMLAttraction", "contact": "chengding", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "SelectMLPath", "contact": "chengding", "throttle_interval_ms": 20000, "enable_onboard": true, "disable": false}, {"name": "MLTrajectoryInitStateHasDivergedFromPlanner", "contact": "chengding", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "DropMLAttractionBecauseIntentionMatch", "contact": "chengding", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "SeqFlickerForBackupSequence", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "ReplanFailTriggerMRC", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AvoidPinchPolicyForCyclist", "contact": "chantong", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RegionalPathHasNegativeCostInAstarSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceHasNegativeCostInAstarSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LanePreferenceForEgoInCongestedOnRouteLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LanePreferenceForEgoInOffRouteLane", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DropMLPathDueToHardBoundaryCollision", "contact": "chengding", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "IgnoreRACZ", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "IgnorePerceptionCZ", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "EnterIgnoreMapChangeTrafficLightCZ", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "ExitIgnoreMapChangeTrafficLightCZ", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "EnterIgnoreAbnormalTrafficLight", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "ExitIgnoreAbnormalTrafficLight", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "AssistIgnoringPerceptionFPObstacle", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNSelectionTriggered", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNForEOLTriggered", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNForEOLWithYieldObj", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNForLaneChangeStuck", "contact": "shaxiang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNVehicleHazardSignal", "contact": "shaxiang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByFNSelection", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByFNHardBoundary", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByFNBreakdownCar", "contact": "fengkaijun", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByFNTempParked", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByDNNModel", "contact": "fengkaijun", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByGBMModel", "contact": "fengkaijun", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByFNRules", "contact": "fengkaijun", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByMapChangeArea", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByMapChangeAreaAboutTrafficLight", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestByRARequirementOfTrafficLight", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "ModelRequestWithMaybePerceptionFPObstacle", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFPPullOverTriggered", "contact": "fengkaijun", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFPPullOverAboutStationaryObjectTriggered", "contact": "fengkaijun", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFPPullOverAboutOrder", "contact": "fengkaijun", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFPOnTurnAboutTempParkedCar", "contact": "fengkaijun", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckDNNInferError", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckScenarioNetInferError", "contact": "fengkaijun", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckScenarioNetInferNanOutput", "contact": "fengkaijun", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "MaybeDropFramesInRAModelVNode", "contact": "fengkaijun", "throttle_interval_ms": 500, "enable_onboard": true, "disable": false}, {"name": "RecordRAModelOutputInPlannerDroppedFrame", "contact": "fengkaijun", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "MLPathReferencePathHugeDiff", "contact": "chengding", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MLPathReferencePathHugeDiffWhenUsingMLAttraction", "contact": "chengding", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MLPathHasHighDiscomfort", "contact": "chengding", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "DropPathOptionDueToPathSimilarity", "contact": "chengding", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "TrimmedReferencePathProjectionIssue", "contact": "chengding", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "S_<PERSON><PERSON>pe_Path_Detected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "Offset_<PERSON><PERSON>ine_Self_Intersects_Detected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "Semantic_Corridor_Invalid_Boundary", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "LAT_WorldModel_Update", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_PlanningNode_LaneSequenceGeneration", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_MainPlanner_PathPlan", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_MainPlanner_SpeedPlan", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_Gap_Between_SpeedCallback_And_SpeedPlan", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_Gap_Between_Speed_Publish", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_PlanningNodelet_Before_Plan", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_PlanningNodelet_Plan_RET", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_PlanningNodelet_Before_Execute", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_PlanningNodelet_After_Execute", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RegionalPathDivergeWithGlobal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RegionalPathDivergeWithGlobalAbnormal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_Before_GenLaneChangeInfo", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_GenerateLaneChangeInfo", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_GeneratePathOptions", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_PathCandidate", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_BackupPath", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_Before_End", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_Path_DDP_Warmstart", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePathPlanner_Path_DDP_Solve", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "Path_LateralClearance_ReferenceEnterHB", "contact": "danxie", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LAT_ExecutePreparation", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "Too_Long_Reference_Path_Detected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LAT_Speed_Before_Gen_Candidate", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_Speed_GenerateSpeedCandidate", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_Speed_DecoupledSelection_Total", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "LAT_Speed_Before_Selection_RET", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "Planner_T<PERSON>_<PERSON>se", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "MapChangeStuckByMapChangeArea", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MapChangeIgnoreMapChangeArea", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MapChangeIgnoreAndPassMapChangeArea", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RA_Intervene_Hazard_Light", "contact": "zekixu", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "SubHdmapChangedData", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DynamicProximityScene", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "AddMapChangedPolygonConstraint", "contact": "jinh<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "HighDiscomfortForMapChangedPolygon", "contact": "jinh<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SlowCutInAgentFromSideRoad", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 2000, "enable_onboard": true, "disable": false}, {"name": "ConstSpeedSoftCutIn", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ProgressEfficiencyCostTrigger", "contact": "hua<PERSON>a", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "LowLikelihoodCautious", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "LowSpeedCutInAgentCautious", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 0, "description": "Reference limit for low-speed cut-in agent", "expiration": "2025-06-30", "enable_onboard": true, "disable": false}, {"name": "PlannerSlightBrake", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "IsEgoOffroad", "contact": "sixian<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "VRUAllowingEBDiff", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UpdateSTPlannerCostsForEmergencySwerve", "contact": "mingshenchen", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "UseNewSTRolloutGeneratingMethod", "contact": "harry<PERSON>o", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "WaypointAssistWrongCommandSequence", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "WrongAssistTaskStateMappingRelation", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RemoteSpeedLimiterActivated", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "IgnoreOutOfPhysicalBoundaryAgent", "contact": "jinghuang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceRolloutNonDefaultSelected", "contact": "wang<PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UsingRedirectingMode", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SelectionViolateLateralJerkLimitCheck", "contact": "wutulin", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "PullOverGapAlignRightOvertakingTailGater", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverDestinationEarlyReject", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverScenarioSelectorFallBack", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverTimeOutAbort", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverSuccessBeforePrepareComplete", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOverRiskModelDisagreement", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "MultiDestinationPullOver", "contact": "liangzixuan", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOutTriggerUnstuckPlanner", "contact": "l<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOutBlockedByBusPickUpDropOff", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "TidalFlowLaneStuckRequested", "contact": "shaxiang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "CZStuckRequestSource", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CZCreepStuckRequested", "contact": "alexma", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "ForwardAssistReceived", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ForwardAssistSuccess", "contact": "alexma", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GenerateEmergencySwerveIntention", "contact": "mingshenchen", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SelectedEmergencySwerveTrajectory", "contact": "mingshenchen", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SelectionSelectedMLTrajectory", "contact": "wutulin", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "SelectionExistHighAndLowDiscomfortDiversities", "contact": "wutulin", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteUnstuckSendWaypointAssistRequest", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckSpecificConfig", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckConfig", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckFNNoBlockTriggered", "contact": "alexma", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckForcingRecallTrigger", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckForcingRecallPreTrigger", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckEgoInLowSpeedForLongTime", "contact": "ch<PERSON><PERSON>", "throttle_interval_ms": 300000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckForcingRecallHit", "contact": "alexma", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckCongestionAPIDiff", "contact": "alexma", "throttle_interval_ms": 30000, "enable_onboard": true, "disable": false}, {"name": "AssistStuckNoSpeedSolverFeature", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "RAUnstuckLightWeightCommandTimeOut", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "WorldModelConstructionZonesCount", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "SelectionEnterForkLaneScenario", "contact": "fengshi", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "SelectionForkLaneScenarioRoutingCostDifferentChoice", "contact": "fengshi", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SelectionForkLaneAddLaneSeqFlickerCauseDecisionChange", "contact": "wutulin", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "SelectionForkLaneRemoveLaneSeqFlickerCauseDecisionChange", "contact": "wutulin", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "SelectionAddPathDiffV2CauseDecisionChange", "contact": "fengshi", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RAInterruptedByManualDriving", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RAInterruptedByCAReverseDriving", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "JunctionCautiousWithEgoSpeed", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "description": "Speed limit for maintaining Ego current speed is applied while passing through junction", "expiration": "2025-06-30", "enable_onboard": true, "disable": false}, {"name": "CrosswalkCreeping", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "description": "Ego creepes around crosswalks with dense VRUs", "expiration": "2025-03-31", "enable_onboard": true, "disable": false}, {"name": "TriggerCrosswalkCreepingConsideringGap", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "description": "Triger Ego creepes around crosswalks with dense VRUs according to gap info.", "expiration": "2025-03-31", "enable_onboard": true, "disable": false}, {"name": "RAInterruptedByMRC", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AddNudgeBackConstraintOnYieldHomotopy", "contact": "jinghuang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRerouteWithReason", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RegionalPathRerouteWithoutReason", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "KTurnPositiveStuckSignal", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "TargetNonDominantStuckObject", "contact": "jinghuang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PositiveTempParkingStuckSignal", "contact": "jinghuang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutingDestinationNotInsideStopCell", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "NoLastTrajUseRefAsAlternative", "contact": "harry<PERSON>o", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UseSpeedBasedRlgForPed", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UseSpeedBasedRlgForCyc", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "UseSpeedBasedRlgForOncomingCyc", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "UseSpeedBasedRlgForLargeVehicle", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "IncreaseRLGForPedInIntersection", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "IncreaseRLGForVehicleWhenEgoTurns", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RegionalPathCrossRoadLaneChange", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "SelectionHumanLikenessCostResultInDifferentChoice", "contact": "fengshi", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ProactiveFrameDrop", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "ConsecutiveDelayTriggeredProactiveFrameDrop", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "UpstreamFrameDrop", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "ForceProactiveFrameDrop", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "GenerateCreepAroundRequest", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "CongestionSignalActivated", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RAUnstuckConnectionFailure", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RAUnstuckConnectionDuplicate", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "BlockHasPotentialBus", "contact": "cojimawang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "BlockBusHasPotentialEffects", "contact": "cojimawang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "BlockLaneChangeDueToBusEffect", "contact": "cojimawang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "EmergencyVehicleAttributesFromPerception", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "description": "Collect special vehicles for 2025 Q2 development", "expiration": "2025-06-30", "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PullOverInLaneJumpInTriggered", "contact": "l<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DecoupledForwardManeuverPath_RunPipeline", "contact": "w<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "InitStateDivergenceInParallelPlanning", "contact": "haimingwang", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "InParallelPathSpeedPlanning", "contact": "haimingwang", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "TargetRiskyBusQueuingNearBusBulb", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "PhysicalBoundaryStuckTriggered", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "RemoteWarningRequestTriggered", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RemoteWarningRequestTriggeredForDebug", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10, "enable_onboard": true, "disable": false}, {"name": "RegionalSectionLocatorInvalid", "contact": "yuze<PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceForRouteChangeBackupFailed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RegionalPathCandidatesAbnormal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PullOutCautiousInBusBulb", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GenerateMLPathInUpl", "contact": "chengding", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "GenerateMLPathNearShortMerge", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "HighCrossingVruDensityScenario", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RegionalPathGeneratorEvents", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "RegionalPathGeneratorAbnormal", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false}, {"name": "ReleaseBrakeFast", "contact": "shi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "ScrSearchFromSpeedConflictResolver", "contact": "shi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "ScrSearchFailureDueToDominantCix", "contact": "shi<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "RAExitTaskAbnormal", "contact": "zhezhu", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RTMReceviedEmpiricalData", "contact": "yuze<PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "TriggerPulloverRepulsionForRightSideOncomingAgent", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "BehaviorTypeChanged", "contact": "haimingwang", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "TrafficQueueRepulsion", "contact": "mingshenchen", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "PhysicalBoundariesRelaxedInPathSolver", "contact": "danxie", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "AgentConstraintRelaxedInPathSolver", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "FrameDropForConsecutiveParallelPlanning", "contact": "haimingwang", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "FailToGeneratePDZ", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "LatticeSearchTerminate", "contact": "w<PERSON><PERSON><PERSON>", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "SelectionHumanLikenessCompareModeDifferentChoice", "contact": "fengshi", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "SelectionHighRiskOverriddenByOtherCost", "contact": "daiya<PERSON><PERSON>i", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "SelectionSelectedRiskyTrajectory", "contact": "daiya<PERSON><PERSON>i", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "PlanningRejectDangerousNewRoute", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 3000, "enable_onboard": true, "disable": false}, {"name": "IrregularShapedAgentRepulsionCreated", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "RequestReplanAfterStuck", "contact": "jinghuang", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "RoadBlockageReceiveCloudData", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 60000, "enable_onboard": true, "disable": false}, {"name": "HighDiscomfortTrackingBasedAvoidSqueezeCautious", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "description": "Checks if avoid squeeze cautious is triggered at high discomfort", "expiration": "2025-09-30", "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "LowDiscomfortTrackingBasedAvoidSqueezeCautious", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "description": "Checks if avoid squeeze cautious is triggered at low discomfort", "expiration": "2025-09-30", "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "LaneSequenceFlicker", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 100, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "BacktrackingLimitedInPathSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "ExtraCautiousForSlowCutInAgent", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "description": " Apply extra cautious logic for slow cut-in agent in front of <PERSON><PERSON>", "expiration": "2025-07-30", "enable_onboard": true, "enable_sim_analysis": true, "disable": false}, {"name": "AddSoftPassConstraintAsPushFence", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Add soft pass constraint in L&M as push fence, scope in merge scene", "expiration": "2025-07-30", "throttle_interval_ms": 1000, "enable_onboard": true, "enable_sim_analysis": true, "disable": false}, {"name": "ResendAssistResponseCommand", "contact": "zhezhu", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "PathReasoningSolidLineEnhancementForLargeVehicle", "contact": "<PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false}, {"name": "KinematicPruningWithHeadingClamped", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SearchPathPassThroughForbidden", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "SoftNudgeStaticAgentTriggered", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "TargetEgoStuckInsideJunction", "contact": "jinghuang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PairLaneInvalidForTemporaryLane", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "DropCandidateContainsTemporaryLaneForEH", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "LaneSequenceContainsTemporaryLane", "contact": "songjingru", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PulloutIsConfirmed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PulloverIsTriggered", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "PulloverProbingIsExecuted", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 5000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}]}