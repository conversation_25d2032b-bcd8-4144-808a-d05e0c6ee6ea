#include "lateral_function/boundary_data_processing/boundary_data_processing.h"

#include <math.h>
#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "lateral_function/lateral_function_parameter.h"

static bool CheckLaneAvailable(uint8_t land_id, float exist_probability,
                               float parse_confidence, float poly_confidence) {
  bool lane_available = false;

  lane_available = (land_id != 0U) && (exist_probability > 0.0F) &&
                   (parse_confidence > 0.0F) && (poly_confidence > 0.0F);

  return lane_available;
}

static bool CheckLaneValid(bool lane_available, float exist_probability) {
  // TODO(lifanjie): Check the lane valid state.
  bool lane_valid = false;

  lane_valid = lane_available && exist_probability > 0.0F;

  return lane_valid;
}

static bool CheckLaneVirtual(bool lane_valid) { return lane_valid; }

static LaneState GetLaneValidState(bool left_lane_is_valid,
                                   bool right_lane_is_valid,
                                   bool left_lane_can_be_virtualized,
                                   bool right_lane_can_be_virtualized) {
  LaneState lane_valid_state = NO_LANE_VALID;

  if (left_lane_is_valid && right_lane_is_valid) {
    lane_valid_state = BOTH_LANES_VALID;
  } else if (left_lane_is_valid) {
    if (right_lane_can_be_virtualized) {
      lane_valid_state = RIGHT_LANE_IS_VIRTUAL;
    } else {
      lane_valid_state = ONLY_LEFT_LANE_VALID;
    }
  } else if (right_lane_is_valid) {
    if (left_lane_can_be_virtualized) {
      lane_valid_state = LEFT_LANE_IS_VIRTUAL;
    } else {
      lane_valid_state = ONLY_RIGHT_LANE_VALID;
    }
  } else {
    lane_valid_state = NO_LANE_VALID;
  }

  return lane_valid_state;
}

static float CalculateVirtualLaneWidth(LaneState lane_state, float left_lane_y0,
                                       float left_lane_heading,
                                       float right_lane_y0,
                                       float right_lane_heading) {
  static float filtered_lane_width = 0.0F;
  static float virtual_lane_width = 0.0F;

  if (lane_state == BOTH_LANES_VALID) {
    float lane_width = fabsf(left_lane_y0 * cosf(left_lane_heading) -
                             right_lane_y0 * cosf(right_lane_heading));
    CML_LowPassFilter(lane_width, &filtered_lane_width, 2.0F,
                      BDP_TASK_CYCLE_TIME);
  } else if (lane_state == LEFT_LANE_IS_VIRTUAL ||
             lane_state == RIGHT_LANE_IS_VIRTUAL) {
    virtual_lane_width = fmaxf(filtered_lane_width, 3.0F);
  } else {
    virtual_lane_width = 0.0F;
  }

  return virtual_lane_width;
}

LaneEquation LaneGeneration(const CSI_LaneInfo_Struct* lane_info_ptr) {
  // right lane can be virtualized
  bool left_lane_available = CheckLaneAvailable(
      lane_info_ptr->CSI_LaneIDLf, lane_info_ptr->CSI_ExistProbLf,
      lane_info_ptr->CSI_ParseConfLf, lane_info_ptr->CSI_PolyfitRmseLf);
  bool left_lane_valid =
      CheckLaneValid(left_lane_available, lane_info_ptr->CSI_ExistProbLf);
  bool right_lane_can_be_virtualized = CheckLaneVirtual(left_lane_valid);

  // left lane can be virtualized
  bool right_lane_available = CheckLaneAvailable(
      lane_info_ptr->CSI_LaneIDRi, lane_info_ptr->CSI_ExistProbRi,
      lane_info_ptr->CSI_ParseConfRi, lane_info_ptr->CSI_PolyfitRmseRi);
  bool right_lane_valid =
      CheckLaneValid(right_lane_available, lane_info_ptr->CSI_ExistProbRi);
  bool left_lane_can_be_virtualized = CheckLaneVirtual(right_lane_valid);

  // lane valid state
  LaneState lane_valid_state = GetLaneValidState(
      left_lane_valid, right_lane_valid, left_lane_can_be_virtualized,
      right_lane_can_be_virtualized);

  // virtual lane width
  float virtual_lane_width = CalculateVirtualLaneWidth(
      lane_valid_state, lane_info_ptr->CSI_PosY0Lf,
      lane_info_ptr->CSI_HeadingLf, lane_info_ptr->CSI_PosY0Ri,
      lane_info_ptr->CSI_HeadingRi);

  // calculate the center lane
  LaneEquation center_lane = {0.0F, 0.0F, 0.0F, 0.0F, 0.0F};
  if (lane_valid_state == BOTH_LANES_VALID) {
    center_lane.y0 =
        0.5F * (lane_info_ptr->CSI_PosY0Lf + lane_info_ptr->CSI_PosY0Ri);
    center_lane.heading =
        0.5F * (lane_info_ptr->CSI_HeadingLf + lane_info_ptr->CSI_HeadingRi);
    center_lane.curvature = 0.5F * (lane_info_ptr->CSI_CurvatureLf +
                                    lane_info_ptr->CSI_CurvatureRi);
    center_lane.curvature_change =
        0.5F * (lane_info_ptr->CSI_CrvRateLf + lane_info_ptr->CSI_CrvRateRi);
    center_lane.length =
        fmax(lane_info_ptr->CSI_PosXEndLf, lane_info_ptr->CSI_PosXEndRi);
  } else if (lane_valid_state == RIGHT_LANE_IS_VIRTUAL) {
    center_lane.y0 = lane_info_ptr->CSI_PosY0Lf - 0.5F * virtual_lane_width;
    center_lane.heading = lane_info_ptr->CSI_HeadingLf;
    center_lane.curvature = lane_info_ptr->CSI_CurvatureLf;
    center_lane.curvature_change = lane_info_ptr->CSI_CrvRateLf;
    center_lane.length = lane_info_ptr->CSI_PosXEndLf;
  } else if (lane_valid_state == LEFT_LANE_IS_VIRTUAL) {
    center_lane.y0 = lane_info_ptr->CSI_PosY0Ri + 0.5F * virtual_lane_width;
    center_lane.heading = lane_info_ptr->CSI_HeadingRi;
    center_lane.curvature = lane_info_ptr->CSI_CurvatureRi;
    center_lane.curvature_change = lane_info_ptr->CSI_CrvRateRi;
    center_lane.length = lane_info_ptr->CSI_PosXEndRi;
  } else {
    // do nothing
  }

  return center_lane;
}
