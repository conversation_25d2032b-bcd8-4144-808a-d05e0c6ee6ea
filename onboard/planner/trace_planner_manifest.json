{"events": [{"type": "duration", "name": "DdpTrajectoryGenerator_SolveWithWarmstart", "suffix": [{"type": "TrajectoryWarmstartType", "name": "trajectory_warmstart_type"}]}, {"type": "duration", "name": "DdpReverseTrajectoryGenerator_SolveWithWarmstart", "suffix": [{"type": "TrajectoryWarmstartType", "name": "trajectory_warmstart_type"}]}, {"type": "duration", "name": "DecoupledTrajectoryGenerator_GenerateTrajectory"}, {"type": "duration", "name": "PlannerObject", "track_task_delay": true}, {"type": "duration", "name": "PlannerObject_OvertakingEvaluator"}, {"type": "duration", "name": "OpsWarning_GenerateWarning", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "OpsWarning_CheckRearEnding"}, {"type": "duration", "name": "OpsWarning_GetOpsWarningMode"}, {"type": "duration", "name": "AccReasoner_GenerateACCDirective"}, {"type": "duration", "name": "TrafficParticipantsReasoner_Reason"}, {"type": "duration", "name": "NudgeDirectiveGenerator_Generate"}, {"type": "duration", "name": "NudgeDirectiveGenerator_UpdateNominalPath"}, {"type": "duration", "name": "NudgeSnapshotHub_Update"}, {"type": "duration", "name": "NudgeDirectiveGenerator_GenerateStaticNudgeDirective"}, {"type": "duration", "name": "NudgeDirectiveGenerator_GenerateDynamicNudgeDirective"}, {"type": "duration", "name": "OcclusionReasoner_Reason"}, {"type": "duration", "name": "CautiousDrivingReasoner_Reason"}, {"type": "duration", "name": "TrafficLightReasoner_Reason"}, {"type": "duration", "name": "AgentIngestor_ComputeAgentsInLaneStates"}, {"type": "duration", "name": "AgentIngestor_IngestAgentsInLaneStates"}, {"type": "duration", "name": "GenerateLaneGeometryDirective"}, {"type": "duration", "name": "OcclusionGrid_BuildMatFromVisibilityPolygon"}, {"type": "duration", "name": "DestinationReasoner_Reason"}, {"type": "duration", "name": "YieldReasoner_Reason"}, {"type": "duration", "name": "YieldAnalyzer_Analyze"}, {"type": "duration", "name": "KeepClearReasoner_Reason"}, {"type": "duration", "name": "StopSignReasoner_Reason"}, {"type": "duration", "name": "CrosswalkReasoner_Reason"}, {"type": "duration", "name": "SpeedLimitReasoner_Reason"}, {"type": "duration", "name": "DisengagementZoneReasoner_Reason"}, {"type": "duration", "name": "ConstructionZoneReasoner_Reason"}, {"type": "duration", "name": "BaseManeuver", "suffix": [{"type": "ManeuverType", "name": "maneuver_type"}]}, {"type": "duration", "name": "DecoupledForwardManeuver", "args": [{"type": "uint64_t", "name": "agent_size"}, {"type": "uint64_t", "name": "path_options_size", "is_lref": true}, {"type": "uint64_t", "name": "backup_path_size", "is_lref": true}, {"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningSeedAccess_StashBagSeed"}, {"type": "duration", "name": "DecoupledForwardManeuver_ExecutePathPlanner", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}, {"type": "uint64_t", "name": "path_options_size", "is_lref": true}, {"type": "uint64_t", "name": "path_backup_size", "is_lref": true}]}, {"type": "duration", "name": "DecoupledForwardManeuver_ExecuteSpeedPlannerAndSelection", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "Speed_SelectBestTrajectory_PostProcess", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "AfterSelection_UpdateExceptionTask", "track_task_delay": true}, {"type": "duration", "name": "AfterSelection_UpdateDecoupledManeuverSeed", "track_task_delay": true}, {"type": "duration", "name": "Decoupled<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "args": [{"type": "uint64_t", "name": "agent_size"}, {"type": "uint64_t", "name": "path_options_size", "is_lref": true}, {"type": "uint64_t", "name": "backup_path_size", "is_lref": true}, {"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledForwardManeuver_RunPathPipeline", "track_task_delay": true, "suffix": [{"type": "string", "name": "homotopy"}], "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledForwardPathPlanner_RunPathPipeline", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledForwardManeuver_GetSpeedProfileUpperBound"}, {"type": "duration", "name": "DecoupledForwardManeuver_Prepare"}, {"type": "duration", "name": "DecoupledForwardPathPlanner_Prepare"}, {"type": "duration", "name": "DecoupledForwardManeuver_GenerateSpeedCandidate", "track_task_delay": true}, {"type": "duration", "name": "ExecuteSpeedPlannerAndSelection_UpdateLaneChangeInfo"}, {"type": "duration", "name": "GenerateBoundaryConstraints"}, {"type": "duration", "name": "GenerateBoundaryConstraints_CreatePolylineCurve2dProximityCachePtr"}, {"type": "duration", "name": "NominalPathPlanner_Plan", "track_task_delay": true}, {"type": "duration", "name": "NominalPathPlanner_GeneratePath", "track_task_delay": true}, {"type": "duration", "name": "TrajectoryInfo_ComputeClosestPhysicalBoundaryProximityInfo"}, {"type": "duration", "name": "DecoupledForwardManeuver_RunSpeedPipeline", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledForwardManeuver_RunSpeedPipeline_Destruction"}, {"type": "duration", "name": "DecoupledForwardManeuver_ProfileSolver_Solve", "track_task_delay": true}, {"type": "duration", "name": "DecoupledForwardManeuver_ProfileSolver_GetPostProcessedSpeedResults"}, {"type": "duration", "name": "DecoupledForwardManeuver_ProfileSearcher_Search"}, {"type": "duration", "name": "DecoupledForwardManeuver_ProfileSearcher_InverseSearch"}, {"type": "duration", "name": "InitialDecisionProvider_GetInitialDecisions"}, {"type": "duration", "name": "AgentReactionCalculator_GetOrCalculateConstraintStates"}, {"type": "duration", "name": "AgentReactionCalculator_GetOrCalculateShiftedOverlap"}, {"type": "duration", "name": "ProfileSearcher_SearchForSolution"}, {"type": "duration", "name": "ProfileSearcher_SearchForSolutionInRiskEvaluatorMode"}, {"type": "duration", "name": "ProfileSearcher_GeneratePerDiscomfortSearchProblem"}, {"type": "duration", "name": "ConstraintTransformer_GenerateTimeMap"}, {"type": "duration", "name": "ConflictResolver_Resolve"}, {"type": "duration", "name": "ConflictResolver_GetCollisionRiskForSingleCandidateUsingRiskModel"}, {"type": "duration", "name": "ProfileSearcher_GenerateSpeedSearchResults"}, {"type": "duration", "name": "ProfileSearcherUtil_FinalizeConstraints"}, {"type": "duration", "name": "ProfileSearcherUtil_PopulateFinalizedConstraintResult"}, {"type": "duration", "name": "ProfileSearcherUtil_AdjustTreeProfileForOneIteration"}, {"type": "duration", "name": "ProfileSearcherUtil_GetLowestFeasibleDiscomfortForConstraint"}, {"type": "duration", "name": "ConstraintManager_UpdateArConstraints"}, {"type": "duration", "name": "ProfileSearcherUtil_UpdateArConstraints"}, {"type": "duration", "name": "DecoupledForwardManeuver_SpeedSearchResolveAllConflictAtDiscomfort"}, {"type": "instant", "name": "ProfileSearcherUtil_AdjustForAllConflict"}, {"type": "instant", "name": "ProfileSearcherUtil_AdjustMainProfileForOneIterationWithNoBlock"}, {"type": "instant", "name": "ProfileSearcher_ResolveAllConflictAtDiscomfort"}, {"type": "instant", "name": "ProfileSearcherUtil_AdjustForConflictExceptNoBlock"}, {"type": "instant", "name": "FindAndPopulateConflict"}, {"type": "instant", "name": "FindFirstConflictForProfile"}, {"type": "duration", "name": "DecoupledForwardManeuver_IterativeLqrSolver_Solve"}, {"type": "duration", "name": "DecoupledForwardManeuver_GeneratePathOptionsForAllBehaviors", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledForwardManeuver_Semantic_Corridor_SetUpBoundaryConstraints"}, {"type": "duration", "name": "DecoupledForwardManeuver_Lateral_Clearance_Generator"}, {"type": "duration", "name": "DecoupledForwardManeuver_PathGenerationGuidanceLoop", "track_task_delay": true}, {"type": "duration", "name": "DecoupledForwardManeuver_RunPathReasoning", "track_task_delay": true}, {"type": "duration", "name": "RunPathReasoning_GenerateLaneSequenceGeometryDebug"}, {"type": "duration", "name": "RunPathReasoning_InitializeMotionEngine"}, {"type": "duration", "name": "RunPathReasoning_AddExitZoneRepulsion"}, {"type": "duration", "name": "RunPathReasoning_AddBusBulbRepulsion"}, {"type": "duration", "name": "RunPathReasoning_CrossAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_ImaginaryAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_LaneChangeAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_LeadAndMergeAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_LowConfidenceAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_PedestrianPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_RearEndAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_ReversingAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_SideAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "RunPathReasoning_StaticAgentPathReasoner_PreRepulsionReason"}, {"type": "duration", "name": "TrajectoryGuiderPathCostEstimator_CreateEstimators"}, {"type": "duration", "name": "RunPathReasoning_AddMlAttraction"}, {"type": "duration", "name": "RunPathReasoning_AddReasoningBoundary"}, {"type": "duration", "name": "RunPathReasoning_PostReasoningProcessing"}, {"type": "duration", "name": "DecoupledForwardManeuver_GeometricGuidanceGen", "suffix": [{"type": "GeometricGuidanceType", "name": "geometric_guidance_type"}]}, {"type": "duration", "name": "GeometricGuidanceGen_Task", "suffix": [{"type": "GeometricGuidanceType", "name": "geometric_guidance_type"}]}, {"type": "duration", "name": "GeometricGuidanceGen_LkGenerateDrivableSpaceCorridor"}, {"type": "duration", "name": "GeometricGuidanceGen_LkGenerateRoadBoundaries"}, {"type": "duration", "name": "GeometricGuidanceGen_LkGenerateLaneSequenceInfo"}, {"type": "duration", "name": "GeometricGuidanceGen_LkPickLaneSequences"}, {"type": "duration", "name": "GeometricGuidanceGen_LkPopulateLaneSequenceInformation"}, {"type": "duration", "name": "GeometricGuidanceGen_LkGetSpeedUpperBound"}, {"type": "duration", "name": "GeometricGuidanceGen_LkGetSpeedUpperBoundSmoothing"}, {"type": "duration", "name": "CopyGeometricGuidanceResult"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeAllOverlapRegions"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeAllProximityInfo"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeAllEgoLateralClearanceInfo"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeOverlapRegionsForObjects"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeOverlapRegionsForHallucinatedObjects"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeOverlapRegionsForCz"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeProximityInfoForObjects"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeProximityInfoForHallucinatedObjects"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputeProximityInfoForCz"}, {"type": "duration", "name": "DecoupledForwardManeuver_PreComputePaddedPathDataForOverlap"}, {"type": "duration", "name": "DecoupledForwardManeuver_PopulateOverlapDebug"}, {"type": "duration", "name": "DecoupledForwardManeuver_PopulateProximityDebug"}, {"type": "duration", "name": "DecoupledSelection_Total", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "DecoupledSelection_HumanLikeness"}, {"type": "duration", "name": "DecoupledSelection_HumanLikenessCreateAgentsInterpolator"}, {"type": "duration", "name": "DecoupledSelection_HumanLikenessGenerateAgentFeature"}, {"type": "duration", "name": "DecoupledSelection_HumanLikenessGenerateCandidateFeature"}, {"type": "duration", "name": "DecoupledSelection_HumanLikenessInference"}, {"type": "duration", "name": "DecoupledSelection_Progress"}, {"type": "duration", "name": "DecoupledSelection_Flicker"}, {"type": "duration", "name": "DecoupledSelection_Risk"}, {"type": "duration", "name": "DecoupledSelection_RiskSignal"}, {"type": "duration", "name": "InvokeUnstuckPlanner_UTurnUnstuckPlanner"}, {"type": "duration", "name": "CheckUTurnUnstuckTriggerCondition"}, {"type": "duration", "name": "BaseManeuver_Reason"}, {"type": "duration", "name": "MainPlanner_Plan", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "MainPlanner_PathPlan", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "MainPlanner_SpeedPlan", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_Plan", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_PathPlan", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_SpeedPlan", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_PublishPlanningResult", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_UpdateDebugData", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_SpeedWorldModelDestruction", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_DecoupledForwardPathPlannerOutputDestruction", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_PublishPlanningResult_debug"}, {"type": "duration", "name": "RemoteAssistModelNodelet_Tick"}, {"type": "duration", "name": "DecoupledLaneSequenceInfo_Update"}, {"type": "duration", "name": "DecoupledLaneSequenceInfo_Update_LC"}, {"type": "duration", "name": "DecoupledLaneSequenceInfo_Update_Reverse"}, {"type": "duration", "name": "LaneSequenceInfoBase_GenerateTrafficRuleAndCzInLaneState"}, {"type": "duration", "name": "LaneSequenceInfoBase_GenerateAgentInLaneStatesMap"}, {"type": "duration", "name": "AgentInLaneStateGenerator_ComputeAgentTrajectoryInLaneStates"}, {"type": "duration", "name": "GenerateObjectOccupancyStateMap", "suffix": [{"type": "AgentImaginaryActualType", "name": "agent_imaginary_actual_type"}]}, {"type": "duration", "name": "Longitudinal_ComputeOptimalWarmstart"}, {"type": "duration", "name": "Longitudinal_GenerateProfile"}, {"type": "duration", "name": "SnapshotNodelet_Tick"}, {"type": "duration", "name": "SnapshotNodelet_Execute", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_Tick", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time", "is_lref": true}]}, {"type": "duration", "name": "PathVNode_Process_Snapshot_And_Drop_Frame", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_DumpPlannerState", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_LoadPlannerState", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_Execute", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "PlanningNodelet_UpdateConstraint"}, {"type": "duration", "name": "BackgroundObject_BackgroundObject"}, {"type": "duration", "name": "TrackedObject_TrackedObject"}, {"type": "duration", "name": "SnapshotTaker_TakeSnapshot"}, {"type": "duration", "name": "WorldModel_UpdateRobotState"}, {"type": "duration", "name": "SemanticContext_EgoLaneFrameAnalyzer"}, {"type": "duration", "name": "SemanticContext_RoadSectionFrameAnalyzer"}, {"type": "duration", "name": "SemanticContext_SideLaneFrameAnalyzer"}, {"type": "duration", "name": "WorldModel_UpdateTrafficSignalReasoner"}, {"type": "duration", "name": "SpeedWorldModel_CreateRouteModelSpeedData"}, {"type": "duration", "name": "WorldModel_Update", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "WorldModel_ExtractToSpeedWorldModel", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "WorldModel_UpdateSeedWithDecoupledManeuver", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "WorldModel_UpdateWaypointAvailability", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "WorldModel_UpdateLightAssistAvailability", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "SnapshotNodelet_UpdateOcclusionChecker"}, {"type": "duration", "name": "WorldModel_UpdateLaneBlockage"}, {"type": "duration", "name": "WorldModel_UpdateLaneBlockageDebug"}, {"type": "duration", "name": "WorldModel_UpdateTrafficInfoMap"}, {"type": "duration", "name": "LaneSlowMovingObjectDetector_UpdateSlowMoving"}, {"type": "duration", "name": "WorldModel_UpdateLaneCongestion"}, {"type": "duration", "name": "WorldModel_UpdateRegionalSectionsLocator"}, {"type": "duration", "name": "WorldModel_UpdateAgentList"}, {"type": "duration", "name": "WorldModel_UpdateTrackedObjects"}, {"type": "duration", "name": "WorldModel_UpdateConstructionZones"}, {"type": "duration", "name": "WorldModel_UpdateGlobalRouteSolution"}, {"type": "duration", "name": "WorldModel_UpdateGlobalRouteLanesInfo"}, {"type": "duration", "name": "WorldModel_UpdateLaneGraphSearchInfo"}, {"type": "duration", "name": "WorldModel_UpdateRegionalMap", "track_task_delay": true}, {"type": "duration", "name": "WorldModel_UpdatePickupDropoffZone"}, {"type": "duration", "name": "WorldModel_UpdateWaypointAssistState"}, {"type": "duration", "name": "WorldModel_PopulatePncMapHistoryBuffer"}, {"type": "duration", "name": "SnapshotNodelet_UpdateSnapshot", "track_task_delay": true}, {"type": "duration", "name": "SnapshotNodelet_UpdateObjects"}, {"type": "duration", "name": "SnapshotNodelet_UpdateTrackedObjectPosesAndAgentHistory"}, {"type": "duration", "name": "SnapshotNodelet_UpdateTrackedObjectPosesAndAgentHistory_NewObj"}, {"type": "duration", "name": "SnapshotNodelet_UpdateTrackedObjectPosesAndAgentHistory_NewHistory"}, {"type": "duration", "name": "SnapshotNodelet_UpdateTrackedObjectPosesAndAgentHistory_ToHistory"}, {"type": "duration", "name": "SnapshotNodelet_UpdateTrackedObjectPosesAndAgentHistory_Clear"}, {"type": "duration", "name": "SnapshotNodelet_UpdateOcclusionMap"}, {"type": "duration", "name": "SnapshotNodelet_UpdateFilteredConstructionZones"}, {"type": "duration", "name": "RegionalMapGenerator_Generate", "track_task_delay": true}, {"type": "duration", "name": "RegionalPathGenerator_Generate", "track_task_delay": true}, {"type": "duration", "name": "RegionalPathGenerator_GetAstarResultForRegionalPath", "track_task_delay": true}, {"type": "duration", "name": "RegionalPathGenerator_ConstructMultipleRegionalPath"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathAlongLastCycle"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathAlongGlobalRoute"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathAlongLastSelectedLS"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathAlongPreferredSections"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathAlongVariableLaneDirection"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathsAvoidForbiddenForkSection"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathsFromSeed"}, {"type": "duration", "name": "RegionalPathGenerator_GetRegionalPathsAlongLaneSequencePrefixes"}, {"type": "duration", "name": "RegionalPathGenerator_GetFallBackRegionalPath"}, {"type": "duration", "name": "RegionalPathGenerator_SavePathDebugInfo"}, {"type": "duration", "name": "RegionalPathGenerator_SelectedOptimalPath"}, {"type": "duration", "name": "PathWarmstarter_ComputeOptimalWarmstart"}, {"type": "duration", "name": "JukeIntegratedPathWarmstarter_ComputeOptimalWarmstart"}, {"type": "duration", "name": "JukeIntegratedPathWarmstarter_ComputeWarmstartWithLastPath"}, {"type": "duration", "name": "JukeIntegratedPathWarmstarter_ComputeWarmstartWithSearchedPath"}, {"type": "duration", "name": "JukeIntegratedPathWarmstarter_ComputeWarmstartWithHighOrderSearchedPath"}, {"type": "duration", "name": "JukeIntegratedPathWarmstarter_ComputeWarmstartWithReference"}, {"type": "duration", "name": "TrajectoryWarmstarter_ComputeOptimalWarmstart"}, {"type": "duration", "name": "STPlanner_GenerateFinalCostedRollout"}, {"type": "duration", "name": "STPlanner_SearchForBestRollout"}, {"type": "duration", "name": "STPlanner_STRolloutGenerator"}, {"type": "duration", "name": "STPlanner_SampleSTRolloutSegment"}, {"type": "duration", "name": "STPlanner_Solve"}, {"type": "duration", "name": "STPlanner_STCostingEngine"}, {"type": "duration", "name": "STPlanner_STDecisionExtractor"}, {"type": "duration", "name": "DdpPathGenerator_GeneratePath"}, {"type": "duration", "name": "PlanningNode_LaneSequenceGeneration", "track_task_delay": true, "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "BP_RouteAssociation"}, {"type": "duration", "name": "BP_RouteAssociation_Sample"}, {"type": "duration", "name": "BP_RouteAssociation_DP"}, {"type": "duration", "name": "BP_RouteAssociation_Post"}, {"type": "duration", "name": "BP_RouteAssociation_Post_Partial"}, {"type": "duration", "name": "BP_RouteAssociation_GetEqually"}, {"type": "duration", "name": "ART_UpdateAgentReactionTrackerSeed"}, {"type": "duration", "name": "LaneSequenceGenerator_GetOptimalLaneSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetOptimalLaneSequenceFromWaypointSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetLaneSequenceWithoutLaneChange"}, {"type": "duration", "name": "LaneSequenceGenerator_GetOptimalLaneFollowSequenceFromWaypointSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetAlternativeLaneFollowSequencesFromWaypointSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetRelaxLaneChangeRestrictionLaneSequenceFromWaypointSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetLaneSequenceFromImmediateLaneChangeSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_GetStuckAvoidanceJumpOutSequenceFromWaypointSequence"}, {"type": "duration", "name": "LaneSequenceGenerator_UpdateLaneGraphSearchInfo"}, {"type": "duration", "name": "BlockageDetector_UpdateLaneObjectInfoMap"}, {"type": "duration", "name": "LaneSequenceGenerator_GetEdgeSequenceCandidates", "track_task_delay": true}, {"type": "duration", "name": "TrafficSignTrafficRule_TrafficSignTrafficRule"}, {"type": "duration", "name": "TrafficLightTrafficRule_TrafficLightTrafficRule"}, {"type": "duration", "name": "CrosswalkTrafficRule_CrosswalkTrafficRule"}, {"type": "duration", "name": "ZoneTrafficRule_ZoneTrafficRule"}, {"type": "duration", "name": "SpeedLimitTrafficRule_SpeedLimitTrafficRule"}, {"type": "duration", "name": "TurnInfoTrafficRule_TurnInfoTrafficRule"}, {"type": "duration", "name": "ConflictingLaneTrafficRule_ConflictingLaneTrafficRule"}, {"type": "duration", "name": "AgentInLaneStateGenerator_ComputeAgentsInLaneStatesForPlannerObjects", "suffix": [{"type": "AgentImaginaryActualType", "name": "agent_imaginary_actual_type"}]}, {"type": "duration", "name": "AgentInLaneStateGenerator_ComputeAgentsInLaneStatesForPlannerObject", "suffix": [{"type": "AgentImaginaryActualType", "name": "agent_imaginary_actual_type"}]}, {"type": "duration", "name": "LaneSequenceInfo_LaneSequenceInfo"}, {"type": "duration", "name": "LaneSequenceGeometry_GenerateRoadCorridor"}, {"type": "duration", "name": "DrivableTunnelSearcher_Search"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_Decide"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_GenerateAgentSTRegion"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_GenerateSTRolloutDecision"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_GenerateOneRollout"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_EstimateLateralPathInfo"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_EvaluateSTRollout"}, {"type": "duration", "name": "SamplingBasedSpeedDecider_GenerateSTRolloutBySpeedSearch"}, {"type": "duration", "name": "AgentIntentionSearcher_Execute", "track_task_delay": true}, {"type": "duration", "name": "AgentIntentionSearcher_RunSpeedSearch"}, {"type": "duration", "name": "AgentIntentionSearcher_GenerateDynamicDecision"}, {"type": "duration", "name": "AgentIntentionSearcher_GenerateBackupIntention"}, {"type": "duration", "name": "AgentIntentionSearcher_RepulsionMetaManager_AppendRepulsions"}, {"type": "duration", "name": "AgentIntentionSearcher_RepulsionMetaManager_AddRepulsionsFromPolyline"}, {"type": "duration", "name": "AgentIntentionSearcher_RepulsionMetaManager_AddRepulsionsFromFrenetPoints"}, {"type": "duration", "name": "AgentIntentionSearcher_RepulsionMetaManager_PathRiskMitigationMode"}, {"type": "duration", "name": "AgentIntentionSearcher_RepulsionMetaManager_FromPolygonAndTrajectory"}, {"type": "duration", "name": "SemanticContext_Update"}, {"type": "duration", "name": "SemanticContext_GenerateObjectReasoningInfoMap"}, {"type": "duration", "name": "SemanticContext_AnalyzeFrame"}, {"type": "duration", "name": "SemanticContext_GenerateNarrowMeetingSceneParams"}, {"type": "duration", "name": "SemanticContext_UpdateImaginaryAgent"}, {"type": "duration", "name": "ReferenceLineSmoother_Solve"}, {"type": "duration", "name": "NominalPathPlanner_Space_Analyze"}, {"type": "duration", "name": "NominalPathPlanner_Space_Search"}, {"type": "duration", "name": "NominalPathSearcher_StateLatticeSearch"}, {"type": "duration", "name": "LatticePathPlanner_MeshGeneration"}, {"type": "duration", "name": "LatticePathPlanner_Search"}, {"type": "duration", "name": "LatticePathPlanner_ResultCollect"}, {"type": "duration", "name": "NominalPath<PERSON>lan<PERSON>_Smoother", "track_task_delay": true}, {"type": "duration", "name": "NominalPath<PERSON><PERSON><PERSON>_<PERSON><PERSON>other_IsSShapePath", "track_task_delay": true}, {"type": "duration", "name": "NominalPathPlanner_Smoother_GenerateWeightAdjustedRegionsForReferenceAttraction", "track_task_delay": true}, {"type": "duration", "name": "DdpCorridorGenerator_GenerateLaneChangeCorridor"}, {"type": "duration", "name": "NominalPathGenerator_LaneChangeAbort"}, {"type": "duration", "name": "NominalPathGenerator_PullOut"}, {"type": "duration", "name": "NominalPathGenerator_PullOver"}, {"type": "duration", "name": "NominalPathGenerator_ENGAGE"}, {"type": "duration", "name": "TrajectoryPrimitiveGenerator_GeneratePrimitives"}, {"type": "duration", "name": "LongitudinalPrimitiveGenerator_GeneratePrimitives"}, {"type": "duration", "name": "OcclusionGrid_OcclusionGrid"}, {"type": "duration", "name": "OcclusionGrid_GetTargetRegionOcclusionMap"}, {"type": "duration", "name": "OcclusionGrid_GetOccludedPositions"}, {"type": "duration", "name": "OcclusionGrid_GetConnectedOccludedPositions"}, {"type": "duration", "name": "OcclusionChecker_Update"}, {"type": "duration", "name": "OcclusionChecker_GetLaneOcclusionInfoVec"}, {"type": "duration", "name": "OcclusionGridFilter_FilterGrid"}, {"type": "duration", "name": "OcclusionChecker_BuildErodedGrids"}, {"type": "duration", "name": "ClearRegionGrid_ClearRegionGrid"}, {"type": "duration", "name": "HallucinatedAgentManager_GenerateHallucinatedAgentList"}, {"type": "duration", "name": "UpdateCustomerMonitorVisual"}, {"type": "duration", "name": "TrajectoryGenerationManager_GenerateTrajectories"}, {"type": "duration", "name": "NaiveOptimizer_GenerateSmoothBrakeProfile"}, {"type": "duration", "name": "TrajectoryGenerationManager_GenTraj", "suffix": [{"type": "TrajectoryProblemType", "name": "trajectory_problem_type"}]}, {"type": "duration", "name": "TrajectoryEvaluator_Evaluate"}, {"type": "duration", "name": "LaneChangeGapCandidatesGenerator_Update"}, {"type": "duration", "name": "PopulatePullOverMetaData"}, {"type": "duration", "name": "LaneChangeGapEvaluator_Evaluate"}, {"type": "duration", "name": "PullOverGapEvaluator_Evaluate"}, {"type": "duration", "name": "PullOverGapCandidatesGenerator_Update"}, {"type": "duration", "name": "MLPullOverGapGenerator"}, {"type": "duration", "name": "OcclusionMapInquirer_Query_CheckOcclusionGPU", "suffix": [{"type": "DeviceName", "name": "device_name"}]}, {"type": "duration", "name": "OcclusionMapInquirer_Query_CheckOcclusionCPU"}, {"type": "duration", "name": "DecoupledForwardManeuver_IntentionGenerator", "track_task_delay": true}, {"type": "duration", "name": "IntentionGenerator_GenerateObjectIntentionInfoMap"}, {"type": "duration", "name": "ComputeOverlapRegion"}, {"type": "duration", "name": "ComputeEgoLateralClearance"}, {"type": "duration", "name": "UpdateEgoLateralClearance", "args": [{"type": "uint64_t", "name": "path_box_size"}]}, {"type": "duration", "name": "WaypointAssistReasoner_Speed"}, {"type": "duration", "name": "TrafficLightReasoner_Speed"}, {"type": "duration", "name": "CrossAgentReasoner_Speed"}, {"type": "duration", "name": "VruReasoner_Speed"}, {"type": "duration", "name": "LeadAndMergeAgentReasoner_Speed"}, {"type": "duration", "name": "CautiousDrivingReasoner_Speed"}, {"type": "duration", "name": "TailgaterReasoner_Speed"}, {"type": "duration", "name": "OcclusionReasoner_Speed"}, {"type": "duration", "name": "PulloutReasoner_Speed"}, {"type": "duration", "name": "PullOverPrepareReasoner_Speed"}, {"type": "duration", "name": "DestinationReasoner_Speed"}, {"type": "duration", "name": "KeepClearReasoner_Speed"}, {"type": "duration", "name": "StopSignReasoner_Speed"}, {"type": "duration", "name": "TurnSignalReasoner_Speed"}, {"type": "duration", "name": "HonkReasoner_Speed"}, {"type": "duration", "name": "HazardLightReasoner_Speed"}, {"type": "duration", "name": "HoldReasoner_Speed"}, {"type": "duration", "name": "ConstructionZoneReasoner_Speed"}, {"type": "duration", "name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_<PERSON>"}, {"type": "duration", "name": "OncomingAgentReasoner_Speed"}, {"type": "duration", "name": "LaneChangePreparationReasoner_Speed"}, {"type": "duration", "name": "UnstuckSpeedReasoner_Speed"}, {"type": "duration", "name": "MrcStopReasoner_Speed"}, {"type": "duration", "name": "TrajectoryInfo_LaneSequenceTrafficRules"}, {"type": "duration", "name": "TrajectoryReasoner_GetTrajectoryIntentionMetaData_Agent"}, {"type": "duration", "name": "TrajectoryReasoner_GetTrajectoryIntentionMetaData_Cz"}, {"type": "duration", "name": "TrajectoryReasoner_GetRepulsionMetaDatas"}, {"type": "duration", "name": "TrafficRules_ConflictingLaneTrafficRule"}, {"type": "duration", "name": "TrafficRules_JunctionTrafficRule"}, {"type": "duration", "name": "TrafficRules_TrafficLightTrafficRule"}, {"type": "duration", "name": "TrafficRules_TurnInfoTrafficRule"}, {"type": "duration", "name": "TrafficRules_TrafficSignTrafficRule"}, {"type": "duration", "name": "TrafficRules_ZoneTrafficRule"}, {"type": "duration", "name": "TrafficRules_LaneInfoTrafficRule"}, {"type": "duration", "name": "PredictionDecisionMakerOutput_MakeDecisions"}, {"type": "duration", "name": "PredictionDecisionMakerInput_Prepare"}, {"type": "duration", "name": "LaneSequenceGenerator_GenerateBaseGraph", "track_task_delay": true}, {"type": "duration", "name": "LaneSequenceGenerator_ComputeBaseCostMap"}, {"type": "duration", "name": "LaneSequenceGenerator_RecreateBaseCostMap"}, {"type": "duration", "name": "LaneSequenceGenerator_GenerateWaypointCostMap", "track_task_delay": true}, {"type": "duration", "name": "LaneSequenceGenerator_PathSearch"}, {"type": "duration", "name": "LaneChangeCommon_GetPreviewRerouteResult"}, {"type": "duration", "name": "LaneChangeCommon_GeneratePreviewedLaneChangeInfo", "track_task_delay": true}, {"type": "duration", "name": "LaneChangeEnvAnalyzer_PopulateLaneChangeEnvInfo"}, {"type": "duration", "name": "LaneChangeEnvAnalyzer_PopulateObjectCollisionInfoMap", "args": [{"type": "uint64_t", "name": "planner_obj_size"}]}, {"type": "duration", "name": "LaneChangeEnvAnalyzer_PopulateLaneChangeRegionInfo", "args": [{"type": "uint64_t", "name": "planner_obj_size"}, {"type": "int", "name": "region_type"}]}, {"type": "duration", "name": "LaneChangeEnvAnalyzer_LaneChangeObjectInfoComputer", "args": [{"type": "uint64_t", "name": "pathsize"}]}, {"type": "duration", "name": "GbmRegressorPrediction_Stuck"}, {"type": "duration", "name": "EgoStuckFeatureExtraction_FetchFeatures"}, {"type": "duration", "name": "RemoteAssistModelNodelet_AdaptDNNFeatures"}, {"type": "duration", "name": "RemoteAssistModelNodelet_TensorDict"}, {"type": "duration", "name": "Assist_StuckModel_TensorDict_DumpToTopic"}, {"type": "duration", "name": "RemoteAssistModelNodelet_InferWithTensorDict"}, {"type": "duration", "name": "RemoteAssistModelNodelet_PublishRAModelOutput"}, {"type": "duration", "name": "RemoteAssistModelNodelet_SpeedWorldModelDestruction", "args": [{"type": "uint64_t", "name": "hw_time"}]}, {"type": "duration", "name": "Assist_StuckFeature_SpeedReasoning"}, {"type": "duration", "name": "Assist_StuckFeature_SpeedSolver_Selection"}, {"type": "duration", "name": "Assist_StuckFeature_Accumulated"}, {"type": "duration", "name": "Assist_StuckModelInference"}, {"type": "duration", "name": "Assist_StuckGBMInfer"}, {"type": "duration", "name": "Assist_StuckDNNInfer"}, {"type": "duration", "name": "RemoteAssistModelNodelet_StuckDNNInfer"}, {"type": "duration", "name": "RemoteAssistModelNodelet_ScenarioNetInfer"}, {"type": "duration", "name": "RemoteWarningDetection_Detect"}, {"type": "duration", "name": "XLaneNudgeDecider_Decide"}, {"type": "duration", "name": "StuckRegionGenerator_GenerateFromLaneBlockage"}, {"type": "duration", "name": "LaneSequenceProposer_Propose"}, {"type": "duration", "name": "ElectiveLaneChangeDecider_ComputeVisibilityInfosForELC"}, {"type": "duration", "name": "ElectiveLaneChangeDecider_Decide"}, {"type": "duration", "name": "LaneChangeInfo_Generate"}, {"type": "duration", "name": "CrossLaneInfo_Generate"}, {"type": "duration", "name": "CrossLaneRegionInfo_Generate"}, {"type": "duration", "name": "CrossLaneCommon_PopulateCrossLaneInfoDebug"}, {"type": "duration", "name": "ElectiveLaneChangeDecider_GetPreviewOnrouteResult"}, {"type": "duration", "name": "ExceptionHandler_TTC", "track_task_delay": true}, {"type": "duration", "name": "ExceptionHandlerForPerception_TTC"}, {"type": "duration", "name": "ExceptionHandlerForPerception_CalculateCollisionWithObstacleNearTruck"}, {"type": "duration", "name": "ExceptionHandlerForPerception_CalculateCollisionWithObstacleFence"}, {"type": "duration", "name": "ExceptionHandlerForPerception_ClusterObstalceInTruck"}, {"type": "duration", "name": "ExceptionHandlerForPerception_GetObstacleFenceCollisionWithEgo"}, {"type": "duration", "name": "ExceptionHandler_CalculateObjectHistory"}, {"type": "duration", "name": "ExceptionH<PERSON>ler_CalculateEgoBrakeTrajectory"}, {"type": "duration", "name": "ExceptionHandler_TrajectoryGeneration_ELK"}, {"type": "duration", "name": "ExceptionHandler_TrajectorySelection_ELK"}, {"type": "duration", "name": "ExceptionHandler_TrajectoryGeneration_TP"}, {"type": "duration", "name": "ExceptionHandler_TrajectorySelection_TP"}, {"type": "duration", "name": "ExceptionHandler_TrajectoryGeneration_EBS"}, {"type": "duration", "name": "ExceptionHandler_TrajectorySelection_EBS"}, {"type": "duration", "name": "ExceptionHandler_GeometryGeneration_History"}, {"type": "duration", "name": "ExceptionHandler_LateralIntervention_Available"}, {"type": "duration", "name": "OpenSpacePathSearcher_UpdateGrid"}, {"type": "duration", "name": "OpenSpacePathSearcher_Search"}, {"type": "duration", "name": "OpenSpacePathSearcher_GenerateRSP"}, {"type": "duration", "name": "DecoupledForwardManeuver_ComputePrincipledRequiredLateralGap"}, {"type": "duration", "name": "DecoupledForwardManeuver_PathForOverlap"}, {"type": "duration", "name": "DecoupledForwardManeuver_RegulateInitialStateAndUpdateImmutableTime"}, {"type": "duration", "name": "DecoupledForwardManeuver_UpdateExcludingCautious"}, {"type": "duration", "name": "DecoupledForwardManeuver_ReasoningDebugAndWorldContextInit"}, {"type": "duration", "name": "DecoupledForwardManeuver_PostSearchProcess"}, {"type": "duration", "name": "DecoupledForwardManeuver_GenerateStatePotentials"}, {"type": "duration", "name": "DecoupledForwardManeuver_GetLqrCost"}, {"type": "duration", "name": "DecoupledForwardManeuver_RunSpeedOptimizer"}, {"type": "duration", "name": "SpecificRangesReferencePathSmooth"}, {"type": "duration", "name": "GlobalObjectManager_Update"}, {"type": "duration", "name": "GlobalObjectManager_UpdatePlannerObjects"}, {"type": "duration", "name": "GetAgentsToIgnoreForWaypoint"}, {"type": "duration", "name": "ComputeObjectsEvaluationResult"}, {"type": "duration", "name": "UpdateOvertakingInfo"}, {"type": "duration", "name": "UpdatePlannerObjectMap"}, {"type": "duration", "name": "UpdateLegacyPredictedPoses"}, {"type": "duration", "name": "UpdatePredictionTrajectoryMap"}, {"type": "duration", "name": "PredictedTrajectoryWrapper_ConstructPredictedTrajectoryWrapper"}, {"type": "duration", "name": "ProcessConditionalPredictions"}, {"type": "duration", "name": "ProcessPredictedTrajectories"}, {"type": "duration", "name": "UpdatePredictionDataForAgent"}, {"type": "duration", "name": "AsyncDestruction_ClearPredictionTrajectoryMap"}, {"type": "duration", "name": "PlannerObject_ConstructPlannerObject"}, {"type": "duration", "name": "PlannerObject_set_predicted_trajectories"}, {"type": "duration", "name": "PredictedTrajectoryWrapper_UpdateLegacyPredictedPoses"}, {"type": "duration", "name": "PlannerObject_GetPerceptionAttributes"}, {"type": "duration", "name": "PlannerObject_GetMotionData"}, {"type": "duration", "name": "PlannerObject_ComputeIrregularContourBuffer"}, {"type": "duration", "name": "SetPlannerObjectAttributes"}, {"type": "duration", "name": "FindPrimaryTrajectory"}, {"type": "duration", "name": "CreatePlannerObject"}, {"type": "duration", "name": "UpdatePlannerObjectMap_ProcessAgent"}, {"name": "SeedCopy", "type": "duration", "ratelimit": {"type": "unlimited"}, "suffix": [{"type": "string", "name": "SeedName"}]}, {"name": "PathSpeedOverlap", "type": "duration", "ratelimit": {"type": "unlimited"}, "args": [{"type": "int64_t", "name": "current_frame"}, {"type": "int64_t", "name": "last_frame"}, {"type": "int64_t", "name": "last_finished_frame"}]}, {"name": "PathSpeedSequential", "type": "duration", "ratelimit": {"type": "unlimited"}, "args": [{"type": "int64_t", "name": "current_frame"}, {"type": "int64_t", "name": "last_frame"}, {"type": "int64_t", "name": "last_finished_frame"}]}, {"type": "duration", "name": "KinematicJumpInGuidanceSearcher_SearchKinematicJumpInGuidance"}, {"type": "duration", "name": "KinematicJumpInGuidanceSearcher_GenerateJumpInMotionPrimitives"}, {"type": "duration", "name": "KinematicJumpInGuidanceSearcher_CalculateJumpInPrimitiveCosts"}, {"type": "duration", "name": "KinematicJumpInGuidanceSearcher_CollectSearchResultFromBestJumpInPrimitive"}, {"type": "duration", "name": "KinematicJumpInGuidanceSearcher_PopulateDebug"}, {"type": "duration", "name": "TargetLineTrafficInfoGenerator_Generate"}, {"type": "duration", "name": "TargetLineTrafficInfo_GetFeasibleRangesFromTargetLineLateralOffset"}, {"type": "duration", "name": "KinematicJumpInGuidanceGenerator_GenerateKinematicJumpInGuidance"}, {"type": "duration", "name": "KinematicJumpInGuidanceGenerator_PopulateJumpInGuidance"}, {"type": "duration", "name": "SnapshotTaker_Cleanup"}, {"type": "duration", "name": "GeneratePullOverCrossLaneInterestedRegion"}, {"type": "duration", "name": "GeneratePullOverCrossLaneInterestedObjects"}, {"type": "duration", "name": "PullOverGapAlignMetaDataGenerator_Generate"}, {"type": "duration", "name": "GeneratePullOverProbeDecision"}], "enums": [{"name": "ManeuverType", "value_names": ["LANE_FOLLOW", "LANE_CHANGE_ABORT", "FALLBACK", "PULL_OUT", "PULL_OVER", "LANE_CHANGE_RIGHT", "LANE_CHANGE_LEFT", "ENGAGE", "REVERSE_DRIVING", "DECOUPLED_FORWARD"], "values": [0, 2, 3, 4, 5, 7, 8, 9, 10, 11]}, {"name": "DecoupledManeuverType", "value_names": ["UNKNOWN", "DECOUPLED_FORWARD"], "values": [0, 1]}, {"name": "GeometricGuidanceType", "value_names": ["UNKNOWN", "LANE_KEEP", "CROSS_LANE", "DECOUPLED_PULL_OVER", "REVERSE", "OPEN_SPACE"], "values": [0, 1, 2, 3, 4, 5]}, {"name": "TrajectoryProblemType", "value_names": ["PLANNED", "JUKE_SUPPR", "JUKE_SUPPR_LONGL", "LAST_EXECUTED", "DECOUPLED"], "values": [0, 1, 2, 3, 4]}, {"name": "TrajectoryWarmstartType", "value_names": ["UNKNOWN", "LAST_PLAN", "LAST_PLAN_LOW_SPEED", "LQR_NOMINAL_ZERO_ACCEL", "LQR_NOMINAL_SMOOTH_STOP", "LQR_NOMINAL_ABRUPT_STOP", "LQR_NOMINAL_STATIONARY_FENCE", "LQR_PP_SMOOTHED_HIGH_ACCEL", "LQR_PP_SMOOTHED_LOW_ACCEL", "LQR_PP_SMOOTHED_ZERO_ACCEL", "LQR_PP_SMOOTHED_LOW_DECEL", "LQR_PP_SMOOTHED_HIGH_DECEL"], "values": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]}, {"name": "DeviceName", "value_names": ["cpu", "gpu_unknown", "gpu_0", "gpu_1", "gpu_2"]}, {"name": "AgentImaginaryActualType", "value_names": ["ACTUAL", "IMAGINARY"], "values": [0, 1]}], "component_name": "planner"}