#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_PARAMETER_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_PARAMETER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

#include "common_math_library/interpolation.h"

#ifndef LGT_CONTROL_TASK_CYCLE_TIME
#define LGT_CONTROL_TASK_CYCLE_TIME (0.01F)
#endif

#ifndef FAS_TASK_CYCLE_TIME
#define FAS_TASK_CYCLE_TIME (0.06F)
#endif

#ifndef CDS_SAFE_DISTANCE_TABLE_ROWS
#define CDS_SAFE_DISTANCE_TABLE_ROWS (7U)
#endif

#ifndef FSC_FREE_STOP_ACC_REQUEST_TABLE_ROWS
#define FSC_FREE_STOP_ACC_REQUEST_TABLE_ROWS (8U)
#endif

#ifndef SED_EMERGENCY_BRAKE_DISTANCE_TABLE_ROWS
#define SED_EMERGENCY_BRAKE_DISTANCE_TABLE_ROWS (9U)
#endif

#ifndef SED_FREE_STOP_BRAKE_DISTANCE_TABLE_ROWS
#define SED_FREE_STOP_BRAKE_DISTANCE_TABLE_ROWS (12U)
#endif

extern bool GetVsiLongSpeedFilterEnable();
extern bool GetVsiLatSpeedFilterEnable();
extern bool GetVsiYawRateFilterEnable();
extern bool GetVsiSteeringWheelAngleFilterEnable();
extern bool GetVsiSteeringWheelAngleRateFilterEnable();
extern float GetCdsMaxStopTime();
extern float GetCdsMaxTimeToCollision();
extern float GetCdsMaxObjectLongDistance();
extern float GetCdsMaxObjectLateralDistance();
extern float GetFscMaxJerk();
extern float GetFscMinJerk();
extern float GetFscEgoVelocityThreshold();
extern float GetFscAccelRequestForStop();
extern float GetFscEgoBrakeResponseTime();
extern float GetSedCollisionProbabilityThreshold();
extern float GetSedTimeToCollisionThreshold();
extern float GetSedEmergencyBrakeEnableDelayTime();
extern float GetSedTimeToReachStopPointThreshold();
extern float GetSedObjectFollowingBrakeDelayTime();
extern float GetSedAccelThreshold();
extern float GetSedLaneIsTooShortDelayTime();
extern const CML_Table1D* GetCdsSafeDistance();
extern const CML_Table1D* GetFscFreeStopAccRequest();
extern const CML_Table1D* GetSedEmergencyBrakeDistance();
extern const CML_Table1D* GetSedFreeStopBrakeDistance();

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_PARAMETER_H_
