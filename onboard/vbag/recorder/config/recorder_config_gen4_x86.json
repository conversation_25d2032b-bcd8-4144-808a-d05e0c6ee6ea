{"bags": [{"bagName": "camera_x86", "compressionType": "UNCOMPRESSED", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/camera_video_frame_101", "/camera_video_frame_102", "/camera_video_frame_103", "/camera_video_frame_104", "/camera_video_frame_105", "/camera_video_frame_106", "/camera_video_frame_107", "/camera_video_frame_tl", "/camera_gen4_video_frame_short_1", "/camera_gen4_video_frame_short_2", "/camera_gen4_video_frame_short_3", "/camera_gen4_video_frame_short_4", "/raw_camera_gen4_infrared_1"], "maxSizeMb": 5}]}, {"bagName": "default_x86", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/radar_scan_1", "/radar_scan_2", "/radar_scan_3", "/radar_scan_4", "/radar_packet_1", "/radar_packet_2", "/radar_packet_3", "/radar_packet_4", "/radar_log_packet", "/converted_gps_imu", "/converted_raw_gps", "/converted_raw_gps_secondary", "/raw_imu_data", "/localization/camera_pose", "/localization/debug", "/localization/visual_odometry_pose", "/localization/fusion_state", "/localization/fusion_pose", "/localization/lidar_pose", "/localization/lidar_coarse_pose", "/localization/map_based_localization_results", "/localization/gnss_pose", "/localization/ins_pose", "/localization/fusion_seed", "/localization/feature_lidar_localizer_seed", "/localization/semantic_localizer_seed", "/localization/lane_localizer_seed", "/localization/instance_localizer_seed", "/localization/yaw_bias_estimator_seed", "/localization/localization_fault", "/v2x/tracked_object_list", "/v2x/traffic_light_detect", "/camera_lane_detect", "/speed_bump_list_detect", "/ss_fallback_canfd_info", "/stop_line_list_detect", "/camera_soiling_detect", "/order_service", "/issue_tag_change", "/issue_record", "/event_handle_info", "/calibration/main_lidar", "/calibration/camera_roll_offset", "/calibration/camera_yaw_pitch_offset", "/calibration/side_lidar_yaw_offset", "/calibration/side_lidar_z_pitch_roll_offset", "/calibration/radar_yaw_offset", "/perception/camera_object_list", "/perception/camera_segmentation", "/perception/lidar_detected_list", "/perception/lidar_detected_map_list", "/perception/lidar_detected_curb_list", "/perception/processed_map_detection_list", "/perception/lidar_object_list", "/perception/radar_clusters", "/perception/occlusion_map", "/planning/fault_detector_response", "/planning/customer_monitor_visual", "/planning/mrc_immediate_pullover", "/vehicle/body_info", "/vehicle/assist_info", "/vehicle/sensor_cleaner_info", "/system_state", "/health/device_can_orin1", "/health/can_inspector_node", "/health/control_node", "/health/fallback_can_decode_node", "/health/fallback_level1_control_node", "/health/fallback_planning", "/health/fallback_level1", "/health/device_gnss_orin1", "/health/ntrip_client_node", "/health/gpu_monitor", "/health/inference_server_node", "/health/laptop_network_monitor", "/health/localization_node", "/health/network_monitor", "/health/ntb_bridge", "/health/onboard_v2x", "/health/order_service_v2_node", "/health/multi_relay", "/health/planner_node", "/health/routing_node", "/health/prediction_node", "/health/device_radar", "/health/regulator_node", "/health/recorder_node", "/health/semantic_mapping_node", "/health/sensing_node", "/health/storage_monitor", "/health/system_control", "/health/tracking_node", "/health/vehicle_node_orin1", "/health/collision_detection_node", "/health/lidar", "/health/main_lidar", "/health/side_lidar", "/health/robotaxi_device_control", "/health/fault_detector", "/health/service_restore", "/health/restore_network_detector", "/health/smart_bridge_node", "/health/onboard_monitor_node", "/health/onboard_monitor_node_orin1", "/health/onboard_monitor_node_orin2", "/request_order_service/trigger_pull_over", "/robotaxi_device_control/device_state_info", "/robotaxi_device_control/oms_identify_info", "/remote/control", "/remote/health", "/multi_relay/onroute_status", "/multi_relay/robotaxi_route_info", "/multi_relay/visual_agent_list", "/teleassist/traffic_light", "/teleassist/status", "/teleassist/zone_list", "/realtime_map/construction_zone_list", "/realtime_map/empirical_publish_data", "/realtime_map/map_change_area_list", "/realtime_map/update_data", "/realtime_map/traffic_light_changes", "/realtime_map/traffic_light_phase", "/realtime_map/traffic_lights", "/system_supervisor/fatal", "/system_supervisor/error", "/system_supervisor/warn", "/system_supervisor/info", "/system_supervisor/metric_list", "/action_factory/camera_self_cleaning_message", "/fallback_planning_trajectory", "/fallback_planning_trajectory_result", "/fallback_level1", "/fallback_level1_control", "/fallback_level1_control/control_debug", "/fallback_level1_control/seed", "/fallback_level1_fault_injection", "/fallback_level2", "/fallback_level2/chassis", "/fallback_level2/camera", "/fallback_level2/radar", "/fallback_mcu_info", "/fallback_anomaly_detection", "/mapping/panel_elements_visualization", "/mapping/map_change_area_list", "/mapping/map_change_info", "/mapping/map_element_change_event", "/mapping/map_element_change", "/mapping/online_map_detection", "/mapping/tracked_map_element_list", "/mapping/changed_map_elements", "/mapping/local_online_map", "/ads_event", "/action_factory/system_supervisor_mrc_triggers", "/ticket_event", "/action_factory/vehicle_parking_status", "/action_factory/vehicle_exception_status", "/action_factory/collision_upstream_faults", "/cloud_config_fetch_request", "/cloud_config_fetch_response", "/system_event", "/restore_record", "/event", "/fallback_event_info", "/fault_inject", "/telemetry/monitor", "/downstream_ticket_event", "/x86_external_disk_free_space", "/hdmap/map_change_preprocess", "/hardware_monitor_record"], "maxSizeMb": 5}]}, {"bagName": "autonomy_x86", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/control", "/control/control_debug", "/control/seed", "/control/corrupted_pose", "/control/replan_request", "/mrc/mrc_debug", "/mrc/mrc_request", "/mrc/mrc_seed", "/pose", "/traffic_light_detect", "/obstacle_list", "/perception/tracked_object_list", "/perception/collision_detection", "/perception/nearby_segmentation_points", "/perception/construction_zone_list", "/perception/construction_zone_change", "/perception/occupancy_model_result_list", "/perception/scene_understanding", "/perception/sensor_abnormal_pub", "/perception/sensor_abnormal_pub_internal", "/perception/lidar_pv_abnormal_output", "/perception/pv_abnormal_response", "/perception/lidar_pv_abnormal_smoothed_pub", "/perception/camera_abnormal_pub", "/prediction/predicted_objects", "/planning/trajectory", "/planning/planning_lane_sequence", "/planning/ops_warning", "/planning/pullout_request", "/planning/stuck_detection_recall_signal", "/planning/remote_warning_signal", "/planning/assist_request", "/planning/assist_response", "/planning/seed", "/planner/state", "/planning/snapshot_seed", "/planning/ttc_detect_result", "/onboard_metrics_warning", "/se_data_acquisition", "/exception_handler/trajectory", "/exception_handler/debug", "/exception_handler/debug_for_perception", "/exception_handler/global_init_state", "/exception_handler/global_selection_state", "/exception_handler/global_selection_seed", "/planning/message_metadata", "/planning/message_metadata_input", "/bag_message_metadata", "/gateway", "/route/request", "/routing/query", "/routing/solution", "/routing/route_command", "/routing/planner_route_command", "/routing/planning_route_state", "/routing/route_status", "/routing/seed", "/routing/state_for_sim", "/ml_planner/ml_trajectories", "/trajectory_guider/guide_trajectory", "/recurring_task/frame", "/recurring_task/frame_agg", "/realtime_map/update_request", "/planning/planning_empirical_raw_data", "/se_ego_liability", "/se_driving_features", "/se_risk_object", "/se_early_warning"], "maxSizeMb": 5}]}, {"bagName": "debug_x86", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/planning/planning_debug", "/shape_stream", "/rosout_agg", "/action_factory/can_communication_record", "/middleware_error", "/routing/routing_debug", "/fallback_planning_debug", "/sensor_metadata"], "maxSizeMb": 5}]}, {"bagName": "lidar_x86", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/lidar_gen4_packets_main_1", "/lidar_gen4_packets_main_2", "/lidar_gen4_packets_main_3", "/lidar_gen4_packets_main_4", "/lidar_gen4_packets_side_1", "/lidar_gen4_packets_side_2", "/lidar_gen4_packets_side_3", "/lidar_gen4_packets_side_4", "/lidar_gen4_packets_side_5", "/lidar_gen4_packets_side_6", "/lidar_gen4_udp_packets_main_1", "/lidar_gen4_udp_packets_main_2", "/lidar_gen4_udp_packets_main_3", "/lidar_gen4_udp_packets_main_4", "/lidar_gen4_udp_packets_side_1", "/lidar_gen4_udp_packets_side_2", "/lidar_gen4_udp_packets_side_3", "/lidar_gen4_udp_packets_side_4", "/lidar_gen4_udp_packets_side_5", "/lidar_gen4_udp_packets_side_6"], "maxSizeMb": 5}]}], "numberCompressionThread": 2, "maxBufferDurationMs": 5000, "maxBufferSizeMb": 10240}