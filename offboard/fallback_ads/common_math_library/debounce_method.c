#include "common_math_library/debounce_method.h"

#include <stdbool.h>

void CML_SetResetFlipFlop(bool set, bool reset, bool *output) {
  if (reset == true) {
    *output = false;
  } else if (set == true) {
    *output = true;
  } else {
    // output unchanged
  }
}

void CML_TurnOnDelay(bool input, float delay_time, float task_cycle_time,
                     float *timer_ptr, bool *output_ptr) {
  // the delay time cannot be less than the task cycle time
  delay_time = delay_time > task_cycle_time ? delay_time : task_cycle_time;

  if (input == true) {
    // timer for turn on
    *timer_ptr = (*timer_ptr) + task_cycle_time;
    if (*timer_ptr >= delay_time || *output_ptr == true) {
      *output_ptr = true;
    } else {
      *output_ptr = false;
    }
  } else {
    *output_ptr = false;
    *timer_ptr = 0.0F;
  }
}

void CML_TurnOffDelay(bool input, float delay_time, float task_cycle_time,
                      float *timer, bool *output) {
  delay_time = delay_time > task_cycle_time ? delay_time : task_cycle_time;
  if (input == false) {
    *timer = *timer + task_cycle_time;
    if (*timer >= delay_time || *output == false) {
      *output = false;
    } else {
      *output = true;
    }
  } else {
    *output = true;
    *timer = 0.0F;
  }
}

void CML_Hysteresis(float input, float lower_threshold, float upper_threshold,
                    bool *output_ptr) {
  if (input > upper_threshold) {
    *output_ptr = true;
  } else if (input < lower_threshold) {
    *output_ptr = false;
  } else {
    // output unchanged
  }
}

void CML_BilateralHysteresis(float input, float min, float max, float hys,
                             bool *output_ptr) {
  if (input < max && input > min) {
    *output_ptr = true;
  } else if (input > max + hys || input < min - hys) {
    *output_ptr = false;
  } else {
    // output unchanged
  }
}
