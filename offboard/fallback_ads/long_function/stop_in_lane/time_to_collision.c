#include "long_function/stop_in_lane/time_to_collision.h"

#include <math.h>
#include <stdint.h>

#include "common_math_library/common_method.h"
#include "common_math_library/poly_solve.h"
#include "long_function/long_function_parameter.h"

// distance after a time with initial distance, velocity and acceleration
static float CalculateDistance(float distance, float velocity,
                               float acceleration, float time) {
  return distance + velocity * time + 0.5F * acceleration * time * time;
}

// calculate the time when ego or object will stop
static float CalculateStopTime(float velocity, float acceleration) {
  float stop_time = 0.0F;

  // no acceleration
  if (fabsf(acceleration) < CML_EPSILON) {
    // no velocity, so vehicle already has stopped
    if (fabsf(velocity) < CML_EPSILON) {
      stop_time = 0.0F;
    } else {
      // velocity exists, but no acceleration, so vehicle will never stop
      stop_time = GetCdsMaxStopTime();
    }
  } else {
    // ego vehicle will stop
    stop_time = -velocity / acceleration;
  }

  // ego vehicle already stops or stop time too long
  if ((stop_time < 0.0F) || (stop_time > GetCdsMaxStopTime())) {
    stop_time = GetCdsMaxStopTime();
  } else {
    // do nothing
  }

  return stop_time;
}

// select minimum positive value in an array
static float SelectMinNonNegativeRoot(const float array[],
                                      uint8_t array_length) {
  float min_value = GetCdsMaxTimeToCollision();

  for (uint8_t i = 0u; i < array_length; ++i) {
    if (array[i] > 0.0F || fabsf(array[i]) < CML_EPSILON) {
      min_value = fminf(min_value, array[i]);
    } else {
      // do nothing
    }
  }

  return min_value;
}

// TTC is calculated without considering possible ego or object stop
static float CalculateTimeToCollisionCore(float relative_dx, float relative_vx,
                                          float relative_ax) {
  float time_to_collision = 0.0F;

  // the collision has already happened
  if (relative_dx < 0.0F) {
    time_to_collision = 0.0F;
  } else {
    // solve equation of motion: 1/2*rel_a*t^2+rel_v*t+rel_s=0
    float time_root_array[2] = {GetCdsMaxTimeToCollision(),
                                GetCdsMaxTimeToCollision()};
    uint8_t time_root_num = 2u;
    CML_SolveQuadraticEquation(0.5F * relative_ax, relative_vx, relative_dx,
                               GetCdsMaxTimeToCollision(), time_root_array,
                               &time_root_num);
    time_to_collision =
        SelectMinNonNegativeRoot(time_root_array, time_root_num);

    // limit time to collision
    if ((time_to_collision < 0.0F) ||
        (time_to_collision > GetCdsMaxTimeToCollision())) {
      time_to_collision = GetCdsMaxTimeToCollision();
    } else {
      // do nothing
    }
  }

  return time_to_collision;
}

float CalculateTimeToCollision(const float ego_dx, const float ego_vx,
                               const float ego_ax, const float obj_dx,
                               const float obj_vx, const float obj_ax) {
  // relative motion between ego vehicle and object
  float relative_dx = obj_dx - ego_dx;
  float relative_vx = obj_vx - ego_vx;
  float relative_ax = obj_ax - ego_ax;

  // get raw time to collision
  float time_to_collision =
      CalculateTimeToCollisionCore(relative_dx, relative_vx, relative_ax);

  // check if ego vehicle or object will stop before collision
  float ego_stop_time = CalculateStopTime(ego_vx, ego_ax);
  float obj_stop_time = CalculateStopTime(obj_vx, obj_ax);

  // ego vehicle stops before collision
  if ((ego_stop_time < obj_stop_time) && (ego_stop_time < time_to_collision)) {
    // oncoming object
    if (obj_vx < 0.0F) {
      // recalculate relative motion for ego vehicle stops before collision
      relative_dx =
          obj_dx - CalculateDistance(ego_dx, ego_vx, ego_ax, ego_stop_time);
      relative_vx = obj_vx;
      relative_ax = obj_ax;
      time_to_collision =
          CalculateTimeToCollisionCore(relative_dx, relative_vx, relative_ax);

      // object stops before collision, there is no risk of collision
      time_to_collision = obj_stop_time < time_to_collision
                              ? GetCdsMaxTimeToCollision()
                              : (time_to_collision);
    } else {
      // ego vehicle and object are moving in the same direction
      // ego vehicle stops before collision, so there is no risk of collision
      time_to_collision = GetCdsMaxTimeToCollision();
    }
  } else if ((obj_stop_time < ego_stop_time) &&
             (obj_stop_time < time_to_collision)) {
    // recalculate relative motion for object stops before collision
    relative_dx =
        CalculateDistance(obj_dx, obj_vx, obj_ax, obj_stop_time) - ego_dx;
    relative_vx = -ego_vx;
    relative_ax = -ego_ax;
    time_to_collision =
        CalculateTimeToCollisionCore(relative_dx, relative_vx, relative_ax);

    // ego vehicle stops before collision, there is no risk of collision
    time_to_collision = ego_stop_time < time_to_collision
                            ? GetCdsMaxTimeToCollision()
                            : (time_to_collision);
  } else {
    // no need considering possible ego or object stop
  }

  return time_to_collision;
}
