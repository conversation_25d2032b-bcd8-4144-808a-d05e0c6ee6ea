package(default_visibility = ["//visibility:public"])

cc_library(
    name = "assert_cfg",
    hdrs = ["assert_cfg.h"],
    copts = ["-x c-header"],
    include_prefix = "common_math_library",
)

cc_library(
    name = "can_util",
    srcs = ["can_util.cpp"],
    hdrs = ["can_util.h"],
    copts = ["-std=c++14"],
    include_prefix = "common_math_library",
)

cc_library(
    name = "common_method",
    srcs = ["common_method.c"],
    hdrs = ["common_method.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
)

cc_library(
    name = "debounce_method",
    srcs = ["debounce_method.c"],
    hdrs = ["debounce_method.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
)

cc_library(
    name = "interpolation",
    srcs = ["interpolation.c"],
    hdrs = ["interpolation.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)

cc_library(
    name = "linear_quadratic_regulator",
    srcs = ["linear_quadratic_regulator.c"],
    hdrs = ["linear_quadratic_regulator.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/common_math_library:matrix",
    ],
)

cc_library(
    name = "look1_iflf_binlcapw",
    srcs = ["look1_iflf_binlcapw.c"],
    hdrs = ["look1_iflf_binlcapw.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library/",
    deps = [
        "//offboard/fallback_ads/common_math_library:rtwtypes",
    ],
)

cc_library(
    name = "matrix",
    srcs = ["matrix.c"],
    hdrs = ["matrix.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/common_math_library:assert_cfg",
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)

cc_library(
    name = "poly_solve",
    srcs = ["poly_solve.c"],
    hdrs = ["poly_solve.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)

cc_library(
    name = "rtwtypes",
    hdrs = ["rtwtypes.h"],
    copts = ["-x c-header"],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/BSW/Bsw/_Common:std_types",
    ],
)

cc_library(
    name = "sort",
    srcs = ["sort.c"],
    hdrs = ["sort.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/common_math_library:assert_cfg",
    ],
)

cc_library(
    name = "vehicle_parameters",
    srcs = ["vehicle_parameters.c"],
    hdrs = ["vehicle_parameters.h"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "common_math_library",
)
