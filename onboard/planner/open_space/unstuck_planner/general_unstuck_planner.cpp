#include "planner/open_space/unstuck_planner/general_unstuck_planner.h"

#include <algorithm>
#include <utility>

#include "math/arc_2d.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_searcher.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

namespace unstuck {

namespace {

// Stuck duration to trigger this unstuck planner.
constexpr int64_t kStuckDurationToTriggerInMSec = 5000;
// The time threshold used to check execution stuck for a motion plan in
// milisecond.
constexpr int64_t kMotionPlanExecutionStuckTimeThresholdInMSec = 1000;
// The time threshold used to check if ego get stuck before reach enough speed
// in milisecond.
constexpr int64_t kMotionPlanExecutionStuckAtStartingTimeThresholdInMSec = 8000;
// The value that indicates no valid stuck start timestamp has been set.
constexpr int64_t kInvalidStuckStartTimestamp = 0;

}  // namespace

pb::UnstuckPlannerState
GeneralUnstuckPlanner::CheckTriggerConditionAndProposeMotionPlan() {
  // If it is in simulation warm up or manual mode, avoid to trigger this
  // unstuck planner.
  if (av_comm::InWarmup() ||
      !world_model().robot_state().IsInAutonomousMode()) {
    return pb::UnstuckPlannerState::IDLE;
  }
  if (!CheckTriggerCondition()) {
    return pb::UnstuckPlannerState::IDLE;
  }

  const std::optional<pb::MotionPlan> motion_plan = GenerateMotionPlan();
  if (!motion_plan.has_value()) {
    return pb::UnstuckPlannerState::IDLE;
  }

  // Update seed.
  mutable_curr_seed().set_unstuck_scene_type(
      pb::UnstuckSceneType::GENERAL_UNSTUCK);
  mutable_motion_plan() = std::move(motion_plan.value());
  return pb::UnstuckPlannerState::EXECUTING;
}

bool GeneralUnstuckPlanner::CheckTriggerCondition() {
  // Check whether ego is fully stopped.
  // TODO(unstuck planner) make it a method of base class.
  const bool is_ego_fully_stopped =
      world_model().robot_state().current_state_snapshot().speed() <
      constants::kDefaultEgoNearStaticSpeedInMps;
  if (!is_ego_fully_stopped ||
      !selected_trajectory_meta_.IsTrajectoryStoppedWithinFirstTwoSecond()) {
    // Reset timer and return false.
    mutable_curr_general_unstuck_planner_seed()->set_stuck_start_timestamp(
        kInvalidStuckStartTimestamp);
    return false;
  }
  // Check if ego has been stopped for enough time.
  if (prev_general_unstuck_planner_seed().stuck_start_timestamp() !=
      kInvalidStuckStartTimestamp) {
    const int64_t current_stuck_duration =
        world_model().snapshot_timestamp() -
        prev_general_unstuck_planner_seed().stuck_start_timestamp();
    if (current_stuck_duration > kStuckDurationToTriggerInMSec) {
      return true;
    }
  } else {
    mutable_curr_general_unstuck_planner_seed()->set_stuck_start_timestamp(
        world_model().snapshot_timestamp());
  }
  return false;
}

std::optional<pb::MotionPlan> GeneralUnstuckPlanner::GenerateMotionPlan() {
  // Calculate target point.
  const math::geometry::PolylineCurve2d& reference_path =
      lane_sequence_geometry().nominal_path;
  const RobotState& robot_state = world_model().robot_state();
  const RobotStateSnapshot& plan_init_snapshot =
      robot_state.plan_init_state_snapshot();
  const double ego_arclength =
      reference_path
          .GetProximity(plan_init_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  // The arclength distance from target to ego.
  static constexpr double kTargetArclengthDistanceInMeter = 10.0;
  const double target_arclength =
      ego_arclength + kTargetArclengthDistanceInMeter;
  if (target_arclength > reference_path.GetTotalArcLength()) {
    return std::nullopt;
  }
  const math::geometry::Point2d& target_point =
      reference_path.GetPointOnArclengthAndLateralOffset(target_arclength, 0);
  const double target_heading = reference_path.GetSegmentHeading(
      reference_path.GetSegmentIndex(ego_arclength));
  debug()->mutable_motion_plan_debug()->mutable_target_pos()->set_x(
      target_point.x());
  debug()->mutable_motion_plan_debug()->mutable_target_pos()->set_y(
      target_point.y());
  debug()->mutable_motion_plan_debug()->set_target_heading(target_heading);

  // Populate motion plan problem.
  motion_plan_problem_manager().SetStartPoseInfoFromRobotState(
      /*initial_motion_mode_guidance=*/open_space::MotionModeGuidance(
          planner::pb::MotionMode::BACKWARD,
          open_space::MotionModeGuidance::GuidanceLevel::ENFORCE));
  motion_plan_problem_manager().AppendTargetPoseInfo(
      target_point.x(), target_point.y(),
      math::NormalizeMinusPiToPi(target_heading),
      /*terminal_motion_mode_guidance=*/std::nullopt,
      /*xy_tolerance_roi=*/std::nullopt,
      /*heading_tolerance_range=*/std::nullopt);
  // TODO(Zixuan): The current implementation is for noop migration of the
  // motion plan API. Finetune this buffer when the general unstuck planner gets
  // in actual use.
  const double ego_length = robot_state.GetLength();
  const double default_motion_plan_roi_buffer = 2.0 * ego_length;
  motion_plan_problem_manager().SetMotionPlanROIFromStartAndTargetPoses(
      /*start_pose_xy_buffer=*/default_motion_plan_roi_buffer,
      /*target_pose_xy_buffer=*/default_motion_plan_roi_buffer);
  motion_plan_problem_manager().PopulateOccupanciesFromStructuredDrivingInputs(
      world_model().planner_object_map(),
      world_model().construction_zone_ptr_map(), lane_sequence_geometry());

  // Construct open-space searcher
  open_space::OpenSpacePathSearcher searcher(
      config().open_space_search_config());

  // Search for result
  const auto& search_result =
      searcher.Search(motion_plan_problem_manager().motion_plan_problem());
  if (!search_result.has_value()) {
    return std::nullopt;
  }

  // Wrap up result into a MotionPlan;
  pb::MotionPlan motion_plan;
  for (const auto& segment : search_result->motion_sequence) {
    pb::MotionSegment motion_segment;
    motion_segment.set_direction(
        segment.IsForward()
            ? pb::MotionSegment::Direction::MotionSegment_Direction_FORWARD
            : pb::MotionSegment::Direction::MotionSegment_Direction_BACKWARD);
    motion_segment.set_init_steering_angle(segment.InitSteeringAngle());

    // Convert PathSegment into polyline curve.
    math::geometry::Polyline2d polyline;
    for (const open_space::PathState& state : segment.path.path_states()) {
      polyline.emplace_back(state.position);
    }
    math::geometry::PolylineCurve2d polyline_curve(std::move(polyline));
    polyline_curve.ToProto(motion_segment.mutable_reference_curve());
    *motion_plan.add_motion_segments() = motion_segment;
  }

  // Record debug
  *(debug()->mutable_motion_plan_debug()->mutable_motion_plan()) = motion_plan;

  return motion_plan;
}

bool GeneralUnstuckPlanner::ShouldAbortMotionPlan() {
  if (world_model().mrc_request_ptr() != nullptr &&
      world_model().mrc_request_ptr()->mrm_progress() == mrc::pb::FINISHED) {
    // Abort unstuck planner if MRC state received.
    mutable_motion_plan().set_abort_reason(pb::MotionPlanAbortReason::IN_MRC);
    return true;
  }
  if (!world_model().robot_state().IsInAutonomousMode()) {
    // Abort the motion plan if EGO is in Manual.
    mutable_motion_plan().set_abort_reason(
        pb::MotionPlanAbortReason::MANUAL_TAKEOVER);
    return true;
  }
  if (!IsMotionPlanStuckAndTimeOut(
          kMotionPlanExecutionStuckTimeThresholdInMSec,
          kMotionPlanExecutionStuckAtStartingTimeThresholdInMSec)) {
    return false;
  }
  if (CheckReplanConditionAndUpdateMotionPlan()) {
    return false;
  }
  mutable_motion_plan().set_abort_reason(
      pb::MotionPlanAbortReason::TIMEOUT_STUCK);
  return true;
}

bool GeneralUnstuckPlanner::CheckReplanConditionAndUpdateMotionPlan() {
  return false;
}

}  // namespace unstuck

}  // namespace planner
