#include "common_math_library/sort.h"

#include <gtest/gtest.h>

TEST(CMLSort, AscendSortTest) {
  float array[] = {3.0F, 1.0F, 2.0F};
  CML_Sort(array, 3, "ascend");
  ASSERT_FLOAT_EQ(array[0], 1.0F);
  ASSERT_FLOAT_EQ(array[1], 2.0F);
  ASSERT_FLOAT_EQ(array[2], 3.0F);
}

TEST(CMLSort, DescendSortTest) {
  float array[] = {3.0F, 1.0F, 2.0F};
  CML_Sort(array, 3, "descend");
  ASSERT_FLOAT_EQ(array[0], 3.0F);
  ASSERT_FLOAT_EQ(array[1], 2.0F);
  ASSERT_FLOAT_EQ(array[2], 1.0F);
}
