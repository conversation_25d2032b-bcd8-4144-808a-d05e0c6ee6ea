syntax = "proto3";

package planner.speed.pb;

import "mrc_protos/mrc_request.proto";
import "planner_protos/agent_reaction.proto";
import "planner_protos/behavior_common_type.proto";
import "planner_protos/behavior_reasoner.proto";
import "planner_protos/exception_handler.proto";
import "planner_protos/lane_change.proto";
import "planner_protos/occlusion_scenario.proto";
import "planner_protos/occlusion_speed_result.proto";
import "planner_protos/overlap.proto";
import "planner_protos/prediction_decision.proto";
import "planner_protos/speed_constraint.proto";
import "planner_protos/speed_discomfort_varying.proto";
import "planner_protos/speed_policy.proto";
import "planner_protos/agent_map_element_occupancy_seed.proto";
import "planner_protos/speed_reasoner.proto";
import "planner_protos/speed_seed.proto";
import "prediction_protos/predicted_trajectory.proto";
import "prediction_protos/yield_intention.proto";
import "voy_protos/math.proto";
import "voy_protos/perception_object_type.proto";
import "voy_protos/point.proto";
import "voy_protos/polygon.proto";
import "voy_protos/traffic_light.proto";
import "voy_protos/trajectory.proto";

// This file contains the debug proto for speed reasoning codes under
// planner/speed/reasoning.

// The debug info for agent slow down strategy.
// Next Available Id: 3
message SlowDownStrategyDebug {
  SlowDownStrategyType slow_down_strategy_type = 1;
  string debug_str = 2;
}

// The debug info for agent shift strategy.
// Next Available Id: 3
message ShiftStrategyDebug {
  ShiftStrategyType shift_strategy_type = 1;
  string debug_str = 2;
}

// The debug info for agent reaction time strategy.
// Next Available Id: 3
message ReactionTimeStrategyDebug {
  ReactionTimeStrategyType reaction_time_strategy_type = 1;
  string debug_str = 2;
}

// The debug info for agent reaction.
// Next Available Id: 4
message AgentReactionDebug {
  SlowDownStrategyDebug slow_down_strategy_debug = 1;
  ShiftStrategyDebug shift_strategy_debug = 3;
  ReactionTimeStrategyDebug reaction_time_strategy_debug = 2;
}

// Next Available Id: 11
message AgentPolicyDebug {
  reserved 3, 4, 7;
  ReasonerId reasoner_in_charge = 1;
  int32 overlap_region_ix = 2;
  double strength = 5;
  AgentReactionDebug agent_reaction_debug = 6;
  string debug_str = 8;
  CautiousDrivingReason cautious_driving_reason = 9;
  GenerativeConstraintType.Enum constraint_type = 10;
}

// The debug info for one predicted trajectory decision.
// Next Available Id: 7
message PredictionDecisionDebug {
  string debug_str = 1;
  Decision decision = 2;
  repeated AgentPolicyDebug agent_policy_debug = 3;
  IgnoreReason.Enum ignore_reason = 4;
  int64 object_id = 5;
  int64 trajectory_id = 6;
}

// prediction_decision_debug contains the debug info for each predicted
// trajectory indicated by object-trajectory-id.
// Next Available Id: 9
message ReasoningDebug {
  repeated PredictionDecisionDebug prediction_decision_debug = 1;
  ReasonersDebug reasoner_debug = 2;
  ReasoningInputDebug input_debug = 3;
  WorldContextDebug world_context = 4;
  TrajectoryInfoDebug trajectory_info = 5;
  PinchRegionsDebug pinch_regions = 6;
  HallucinatedAgentManagerDebug hallucinated_agent_manager_debug = 7;
  UrgencyInfoDebug urgency_info = 8;
}

// Next Available Id: 22
message ReasonersDebug {
  reserved 10;
  reserved "lane_change_preparation_reasoner";
  TrafficLightReasonerDebug traffic_light_reasoner = 1;
  CautiousDrivingReasonerDebug cautious_driving_reasoner = 2;
  OcclusionReasonerDebug occlusion_reasoner = 3;
  CrossAgentReasonerDebug cross_agent_reasoner = 4;
  ConstructionZoneReasonerDebug construction_zone_reasoner = 5;
  DestinationReasonerDebug destination_reasoner = 6;
  KeepClearReasonerDebug keep_clear_reasoner = 7;
  TailgaterReasonerDebug tailgater_reasoner = 8;
  LeadAndMergeReasonerDebug lead_and_merge_reasoner = 9;
  LaneChangeSpeedReasonerDebug lane_change_speed_reasoner = 16;
  TurnSignalReasonerDebug turn_signal_reasoner = 11;
  HoldReasonerDebug hold_reasoner = 12;
  VruReasonerDebug vru_reasoner = 13;
  HonkReasonerDebug honk_reasoner = 14;
  OncomingAgentReasonerDebug oncoming_agent_reasoner = 15;
  HazardLightReasonerDebug hazard_light_reasoner = 17;
  MrcStopReasonerDebug mrc_stop_reasoner = 18;
  PullOverPrepareReasonerDebug pull_over_prepare_reasoner = 19;
  JaywalkerReasonerDebug jaywalker_reasoner = 20;
  StopSignReasonerDebug stop_sign_reasoner = 21;
}

// Next Available Id: 5
message MrcStopReasonerDebug {
  // Condition type for mrc check.
  enum MrcConditionType {
    INVALID = 0;
    LAST_CYCLE = 1;
    URGENCY = 2;
    BACKWARD = 3;
    IN_RANGE = 4;
    STAY_STOPPED = 5;
    REACH_GOAL = 6;
    PULL_OVER_FINISH = 7;
    STUCKED = 8;
  }
  mrc.pb.MrmType mrm_type = 1;
  double brake_distance = 2;
  repeated math.pb.Range valid_ranges = 3;
  MrcConditionType mrc_condition = 4;
}

// Next Available ID: 3
message TrafficLightReasonerDebug {
  repeated TrafficLightInfoDebug traffic_light_info = 1;
  string map_changed_scene_debug_str = 2;
}

// The debug of the single traffic light.
// Next Available ID: 20
message TrafficLightInfoDebug {
  reserved 9, 13, 15;
  int64 traffic_light_id = 1;
  int64 controlled_lane_id = 2;
  voy.TrafficLight.Color color = 3;
  // Whether the current traffic light is flashing or not.
  bool is_flashing = 4;
  // Whether the traffic light uses watch line as stop line.
  bool use_watch_line = 5;
  // Whether the current traffic light is broken.
  bool is_broken = 6;
  // Whether the current traffic light is occluded.
  bool is_occluded = 7;
  // The spatial info of the traffic light crossing.
  TrafficLightSpatialInfo traffic_light_spatial_info = 8;
  // Whether the resulting constraint is a stop constraint.
  bool has_stop_constraint = 10;
  // Whether the resulting constraint is a speed constraint
  bool has_reference_speed_limiter = 11;
  // Whether ego has entered intersection.
  bool ego_has_entered_intersection = 18;
  TrafficLightReferenceSpeedLimiterDebug traffic_light_reference_speed_limiter =
      12;
  // The temporal info of the traffic light.
  planner.speed.pb.TrafficLightTemporalInfo traffic_light_temporal_info_common =
      14;
  // The color of traffic light in previous cycle.
  voy.TrafficLight.Color pre_color = 16 [ deprecated = true ];
  // The string shows some specific information.
  string debug_str = 17;
  // The previous state of the traffic light that different from current one.
  planner.pb.TrafficLightState pre_state = 19;
}

// Deprecate this, instead by TrafficLightTemporalInfo in speed_reasoner.
// The debug for the temporal information of the traffic light.
// Next Available ID: 6
message TrafficLightInfoTemporalDebug {
  math.pb.TimeRange green_temporal_range = 1;
  math.pb.TimeRange green_flashing_temporal_range = 2;
  math.pb.TimeRange yellow_temporal_range = 3;
  math.pb.TimeRange red_temporal_range = 4;
  // Unknown color temporal range of current traffic light.For example, traffic
  // lights are blocked or broken.
  math.pb.TimeRange unknown_temporal_range = 5;
}

// The content of the speed constraint.
// Next Available ID: 8
message TrafficLightReferenceSpeedLimiterDebug {
  reserved 1 to 5;
  reserved "min_comfort_speed", "max_discomfort_speed", "start_ra_arc_length",
      "end_ra_arc_length", "k_min_accl";
  int64 signal_id = 6;
  CautiousDrivingLimiterParameters limiter_parameters = 7;
}

// The debug of the exception handler speed limiter.
// Next Available ID: 5
message ExceptionhandlerSpeedLimiterDebug {
  double min_speed = 1;
  double max_speed = 2;
  double min_accl = 3;
  planner.pb.DegradationHandlerType degradation_handler_type = 4;
}

// The content of all debug info in CautiousDrivingReasoner.
// Next Available Id: 7
message CautiousDrivingReasonerDebug {
  repeated CautiousDrivingAgentDebug agents = 1;
  repeated CautiousDrivingBoundaryDebug boundaries = 2;
  repeated CrosswalkCautiousDrivingDebug crosswalks = 3;
  repeated ExceptionhandlerSpeedLimiterDebug exception_handler = 4;
  repeated TrafficLightReferenceSpeedLimiterDebug traffic_lights = 5;
  repeated ScenarioBasedCautiousDrivingDebug scenarios = 6;
}

// The scenario based debug info in CautiousDrivingReasoner.
// Next Available ID: 3
message ScenarioBasedCautiousDrivingDebug {
  // Next Available ID: 3
  enum ScenarioType {
    NA = 0;
    NARROW_PASSAGE = 1;
    MAP_CHANGE_REGION = 2;
  }
  ScenarioType scenario_type = 1;
  repeated CautiousDrivingLimiterParameters limiter_parameters = 2;
}

// The agent's debug info in CautiousDrivingReasoner.
// Next Available ID: 15
message CautiousDrivingAgentDebug {
  reserved 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12;
  reserved "agent_id", "object_type", "is_add_reference_limit", "min_speed",
      "max_speed", "start_ra_arc_length", "end_ra_arc_length", "k_min_accel",
      "abs_lateral_gap", "comfort_required_lateral_gap",
      "critical_required_lateral_gap", "is_static";
  int64 object_id = 14;
  // The debug info of agent for different cautious scenes.
  repeated CautiousDrivingDebugForScene debug_for_scenes = 15;
  // The debug info of agent's predicted trajectories.
  repeated CautiousDrivingPredictedTrajectoryDebug predicted_trajectory_debugs =
      13;
}

// The parameters used to update the reference limit.
// Next Available ID: 7
message CautiousDrivingLimiterParameters {
  // The min acceleration at which Ego should brake.
  double min_accel = 1;  // m/s^2
  // The speed limit at min discomfort.
  double min_discomfort_speed = 2;  // m/s
  // The speed limit at max discomfort.
  double max_discomfort_speed = 3;  // m/s
  // The arc length range of speed zone.
  double start_ra_arclength = 4;  // m
  double end_ra_arclength = 5;    // m
  // This flag indicates whether the reference profile limit is updated, true
  // for updated.
  bool is_reference_profile_updated = 6;
}

// The debug info of agent for different scenes. One can add a new
// AgentCautiousScene for your interested scene.
// Next Available ID: 5
message CautiousDrivingDebugForScene {
  // Next Available ID: 13
  enum AgentCautiousScene {
    STATIC_PROXIMITY = 0;
    OCCLUSION = 1;
    DYNAMIC_PROXIMITY = 2;
    EXTRA_CAUTIOUS_AGENT = 3 [ deprecated = true ];
    ROUNDABOUT_AGENT = 4;
    HIGH_AGENT_DENSITY = 5;
    TURNING_ALONG_WITH_LARGE_VEHICLE = 6;
    EXIT_ZONE_NEARBY = 7;
    UNCERTAIN_RISKY_AGENT = 8;
    UNSTUCK_WITH_LAT_GAP_LESS_THAN_CRITICAL = 9;
    NEW_DETECT_AGENT = 10;
    BUS_NEAR_BUS_BULB = 11;
    CONST_SPEED_PASSING_JUNCTION = 12;
  }
  AgentCautiousScene cautious_scene = 1;
  // Next Available ID: 17
  enum DynamicProximitySceneRecognition {
    NA = 0;
    HAS_MADE_DECISION = 1;
    NOT_VEHICLE = 2;
    LATERALLY_MOVING_AWAY = 3;
    LOW_LATERAL_SPEED = 4;
    LATERALLY_FAR_AWAY = 5;
    ONCOMING_AGENT = 6;
    BEHIND_EGO = 7;
    FASTER_THAN_EGO = 8;
    WILL_NOT_LONGITUDINALLY_CLOSE = 9;
    WILL_NOT_CLOSE_LANE_BOUNDARY = 10;
    AGENT_ON_EGO_PATH = 11;
    WILL_NOT_LATERALLY_ENCROACH = 12;
    RISKY_CUT_IN = 13;
    NON_REACTIVE_CYC = 14;
    NON_REACTIVE_PED = 15;
    AVOID_SQUEEZE = 16;
  }
  // TODO(waylon): Use |oneof| feature to save memory if we will add other scene
  // recognition info.
  DynamicProximitySceneRecognition dynamic_proximity_scene = 4;
  string debug_str = 2;
  CautiousDrivingLimiterParameters limiter_parameters = 3;
}

// The debug info of reference limits added for physical boundaries
message CautiousDrivingBoundaryDebug {
  math.pb.Side side = 1;
  repeated CautiousDrivingLimiterParameters limits = 2;
}

// The debug info of agent's low likelihood predicted trajectory.
// Next Available ID: 3
message CautiousDrivingPredictedTrajectoryDebug {
  int32 trajectory_id = 1;
  repeated CautiousDrivingOverlapDebug overlaps = 2;
}

// The debug info of OverlapRegion.
// Next Available ID: 12
message CautiousDrivingOverlapDebug {
  reserved 2, 3, 4, 5, 6, 8;
  reserved "min_accel", "min_discomfort_speed", "max_discomfort_speed",
      "start_ra_arclength", "end_ra_arclength", "is_reference_profile_updated";
  int32 overlap_region_id = 1;
  // The strength that Ego applies cautious. The strength larger, the action
  // stronger.
  double strength = 7;
  CautiousDrivingLimiterParameters limiter_parameters = 9;
  CautiousDrivingReason cautious_driving_reason = 10;
  string debug_str = 11;
}

// The debug info of adding cautious driving limiter for crosswalks.
// Next Available ID: 5
message CrosswalkCautiousDrivingDebug {
  int64 crosswalk_id = 1;
  enum CrosswalkCautiousDrivingScene {
    PROXIMITY_VRU = 0;
    STARTING_TO_MOVE_CYCLIST = 1;
  }
  CrosswalkCautiousDrivingScene scene = 2;
  string debug_str = 3;
  CautiousDrivingLimiterParameters limiter_parameters = 4;
}

// The content of all debug info in OcclusionReasoner.
// Next Available Id: 6
message OcclusionReasonerDebug {
  reserved 4;
  reserved "occlusion_grid";
  // occlusion_area_info is to be deprecated.
  OcclusionAreaInfoDebug occlusion_area_info = 2 [ deprecated = true ];
  repeated OcclusionAreaDebug occlusion_areas = 5;
  repeated HallucinatedAgentDebug hallucinated_agents = 1;
  string debug_str = 3;
}

// The debug info of the occlusion area.
message OcclusionAreaInfoDebug {
  repeated voy.Point2d visibility_polygon = 1;
  repeated voy.Point2d visibility_points = 2;
  repeated voy.Point2d occluded_points = 3;
}

// The debug message to show the related information of an occlusion area of
// interest.
// Next Available ID: 15
message OcclusionAreaDebug {
  // Id of the occlusion area.
  int64 id = 1;
  // Indicates the type of the occlusion scenario.
  planner.pb.OcclusionScenario.Enum occlusion_scenario = 2;
  // Indicates the contour of the area of interest.
  repeated voy.Point2d contour = 3;
  // Indicates the occluded points in or around the area.
  repeated voy.Point2d occluded_points = 4;
  // Indicates the width of the occluded point cluster where the hallucinated
  // agent is spawned.
  optional double occlusion_cluster_width = 12;
  // Indicates the length of the occluded point cluster where the hallucinated
  // agent is spawned.
  optional double occlusion_cluster_length = 13;
  // Indicates the contour of the occluded point cluster.
  repeated voy.Point2d occlusion_cluster_contour = 14;
  // Indicates if there is a hallucinated agent created in the area.
  bool has_hallucinated_agent = 5;
  // Indicates the id of the hallucinated agent if existing.
  int64 hallucinated_agent_id = 6;
  // Indicates the id of the agent ahead of the hallucinated agent if existing.
  optional int64 agent_ahead_id = 10;
  // Indicates the road precedence of the occlusion area over the ego lane.
  planner.pb.RoadPrecedence.Enum road_precedence = 7;
  // Indicates the associated ego lane of the occlusion area.
  int64 associated_ego_lane_id = 8;
  // Indicates the associated conflicting lane of the occlusion area.
  optional int64 associated_conflicting_lane_id = 11;
  // Indicates all other debug information.
  string debug_str = 9;
}

// The speed limit info for hallucinated agent in OcclusionReasoner.
// Next Available ID: 7
message OcclusionSpeedLimitDebug {
  double min_comfort_speed = 1;     // m/s
  double max_discomfort_speed = 2;  // m/s
  double start_ra_arc_length = 3;   // m
  double end_ra_arc_length = 4;     // m
  // The min acceleration passed to reference limit, always negative from
  // occlusion reasoner.
  double min_accel = 5;  // m/s/s
  // Debug string.
  string debug_str = 6;
}

// The standoff constraint info for hallucinated agent in OcclusionReasoner.
// Next Available ID: 4
message OcclusionStopPointDebug {
  double ra_arc_length = 1;  // m
  double start_time = 2;     // s
  double end_time = 3;       // s
}

// The risk mitigation constraint info for hallucinated agent in
// OcclusionReasoner. Next Available ID: 4
message OcclusionRiskMitigationDebug {
  double arclength_to_clearance = 1;  // m
  double switch_time = 2;             // s
  double risk_delta_discomfort = 3;
}

// The hallucinated agent debug info in OcclusionReasoner.
// Next Available ID: 17
message HallucinatedAgentDebug {
  reserved 3;
  reserved "OcclusionScenarioType";

  int64 agent_id = 1;
  voy.perception.ObjectType object_type = 2;

  double center_x = 4;  // m
  double center_y = 5;  // m
  double width = 6;     // m
  double length = 7;    // m
  double heading = 8;   // radians
  double speed = 9;     // m/s

  oneof strategy {
    OcclusionStopPointDebug stop_point_debug = 10;
    OcclusionSpeedLimitDebug speed_limit_debug = 11;
    OcclusionRiskMitigationDebug risk_mitigation_debug = 14;
  }

  // Indicates the type of the occlusion scenario.
  planner.pb.OcclusionScenario.Enum occlusion_scenario = 12;

  // Predicted trajectories of the hallucinated agent.
  repeated prediction.pb.PredictedTrajectory predicted_trajectories = 13;

  // Hallucinated agent result.
  optional HallucinatedAgentResult hallucinated_agent_result = 16;

  // Debug string.
  string debug_str = 15;
}

// The content of all debug info in Hallucinated Agent Manager.
// Next Available ID: 5
message HallucinatedAgentManagerDebug {
  string debug_str = 1;
  repeated OcclusionAreaDebug occlusion_areas = 2;
  repeated HallucinatedAgentDebug hallucinated_agents = 3;
  bool will_ego_borrow_opposite_road = 4;
}

// The debug info for CrossAgentReasoner.
// Next Available ID: 2
message CrossAgentReasonerDebug { repeated CrossAgentDebug agents = 1; }

// The agent debug info in CrossAgentReasoner.
// Next Available ID: 3
message CrossAgentDebug {
  int64 object_id = 1;
  repeated CrossAgentTrajectoryDebug agent_trajectories = 2;
}

// The trajectory-level debug info in CrossAgentReasoner.
// Next Available ID: 4
message CrossAgentTrajectoryDebug {
  int32 trajectory_id = 1;
  string debug_str = 2;
  repeated CrossAgentPolicyDebug agent_policies = 3;
}

// The agent policy level debug info in CrossAgentTrajectoryDebug.
// Next Available ID: 4
message CrossAgentPolicyDebug {
  int32 overlap_region_id = 1;
  string policy_debug_str = 2;
  CrossAgentHonkDebug honk_debug = 3;
}

// The honk debug info for CrossAgentReasoner.
// Next Available ID: 3
message CrossAgentHonkDebug {
  enum DisallowedHonkReason {
    NA = 0;
    EGO_SLOW_SPEED = 1;
    EGO_LOW_PRECEDENCE = 2;
    OUT_OF_TEMPORAL_CONTEXT = 3;
    PROXIMITY = 4;
  }
  bool should_trigger = 1;
  DisallowedHonkReason disallowed_reason = 2;
}

// The debug info for DestinationReasoner.
// Next Available ID: 5
message DestinationReasonerDebug {
  double lane_sequence_end_ra_arclength = 1;
  double destination_ra_arclength = 2;
  // If pull over constraint exist, it will dominate over the destination
  // constraint, which invalidates destination_ra_arclength.
  double pull_over_ra_arclength = 4;
  string debug_str = 3;
}

// The debug info for KeepClearReasoner.
// Next Available ID: 2
message KeepClearReasonerDebug {
  repeated KeepClearZoneDebug keep_clear_zones = 1;
  string debug_str = 2;
}

// The keep clear zone debug info in KeepClearReasoner.
// Next Available ID: 3
message KeepClearZoneDebug {
  math.pb.Range zone_ra_arclength = 1;
  string debug_str = 2;
}

// The debug info for ReasoningInput.
// Next Available ID: 2
message ReasoningInputDebug {
  repeated ReasoningObjectDebug reasoning_objects = 1;
}

// The debug info for ReasoningObject.
// Next Available ID: 11
message ReasoningObjectDebug {
  reserved 6;
  int64 object_id = 1;
  voy.perception.ObjectType object_type = 2;
  double has_seen_ego_time = 3;
  ReasoningObjectAttributesDebug attributes = 4;
  repeated AgentTrajectoryInfoDebug trajectory_infos = 5;
  repeated voy.Point2d contour = 7;
  string debug_str = 8;
  DiscomfortVarying required_lateral_gap = 9;
  OverlapComputationPolicy.Type overlap_computation_policy = 10;
}

// The debug info for EgoPinchRegions.
// Next Available ID: 5
message PinchRegionsDebug {
  repeated PinchRegion left_pinch_regions_for_vehicles = 1;
  repeated PinchRegion left_pinch_regions_for_cyclists = 2;
  repeated PinchRegion right_pinch_regions_for_vehicles = 3;
  repeated PinchRegion right_pinch_regions_for_cyclists = 4;
}

// The linked vehicle IDs for a reasoning object if it is a part of trailer
// or tractor of a large vehicle, e.g., semi-truck.
// Next Available ID: 3
message LinkedVehicleIDs {
  int64 head_id = 1;
  int64 tail_id = 2;
}

// This message stores the object-level ART debug info.
// Next Available ID : 10
message ObjectARTDebugInfo {
  reserved 1;
  optional double latest_t_gap = 2;
  optional double latest_distance_gap = 8;
  int64 latest_constraint_timestamp = 3;
  double min_v_in_seed = 4;
  double agent_leads_duration = 5;
  double t_gap_shrinks_duration = 6;
  double near_stationary_duration = 7;
  double t_gap_enlarges_duration = 9;
}

// This message stores the object occupancy param info computed by path module.
// Next Available ID : 5
message ObjectOccupancyParamInfo {
  double max_encroachment = 1;
  double signed_lateral_speed = 2;
  double relative_heading = 3;
  bool is_on_center_line = 4;
}

// The attribute info for ReasoningObject.
// Next Available ID: 56
message ReasoningObjectAttributesDebug {
  reserved 18, 19, 35, 48;
  bool is_leading_vehicle = 1;
  bool is_fully_behind = 2;
  bool is_fully_ahead = 25;
  bool is_behind_ego_front_axle = 20;
  bool is_behind_ego_leading_bumper = 24;
  bool is_far_away_behind = 14;
  bool is_large_vehicle = 3;
  bool is_open_door = 4;
  bool is_stationary = 5;
  bool is_truck = 6;
  bool is_unknown_object_possible_pedestrian = 7 [ deprecated = true ];
  bool is_hallucinated = 8;
  bool is_likely_tailgating = 9;
  bool is_ego_nudging_around = 10 [ deprecated = true ];
  bool was_pass = 11;
  bool was_yield = 12;
  bool is_overtaking = 13;
  bool is_likely_turbo_ped = 15;
  bool is_cyc_no_person = 16;
  bool is_in_junction = 17;
  bool is_blocking_object = 21;
  bool is_ego_path_overtaking = 22;
  bool is_not_facing_ego_vehicle = 23;
  bool is_laterally_moving_away = 26;
  bool is_in_neighboring_lane = 27;
  bool is_slow_moving_vehicle = 28;
  bool is_on_sidewalk = 29;
  bool is_fod_preferable_to_drive_through = 30;
  bool is_outside_physical_boundary = 31;
  LinkedVehicleIDs linked_vehicle_ids = 32;
  bool is_running_pedestrian = 33;
  optional ObjectARTDebugInfo art_debug = 34;
  bool has_complete_reversing_bp = 36;
  bool has_kturn_bp = 37;
  bool is_laterally_encroach_ego_lane_boundary = 38;
  bool is_potential_uturn_blockage = 39;
  ObjectOccupancyParamInfo object_occupancy_debug = 40;
  int64 latest_low_speed_timestamp = 41;
  bool is_bus = 42;
  bool is_bus_near_bus_bulb = 43;
  bool is_crossing_lane_veh = 44;
  bool is_on_ego_path = 45;
  double estimated_jerk = 46;
  double consistent_cross_intention_count = 47;
  bool is_vru_obviously_slow_down = 49;
  double filtered_acceleration = 50;
  // The debug for agent_occupied_map_element_infos_in_seed_ptr, which is
  // computed from Predicted Trajectory Route Associator.
  optional AgentOccupiedMapElementInfosDebug agent_occupied_map_element_infos =
      51;
  bool is_dominant_stuck_object_for_xlane = 52;
  bool is_vehicle_or_cyclist_on_wrong_way_lane = 53;
  bool is_emergency_vehicle_in_task = 54;
  bool is_driving_ahead_in_same_section_along_ego = 55;
}

// Next Available ID: 6
message AgentReactionTrackerDebug {
  reserved 1, 2;
  enum NonReactiveReason {
    NONE = 0;
    AGENT_ACCELERATING = 1;
    T_GAP_SHRINKS = 2;
    EGO_HARD_TO_PASS = 3;
    EXPECTED_TO_BRAKE = 4;
  }
  double strict_overlap_start_time = 3;
  NonReactiveReason nonreactive_reason = 4;
  string debug_str = 5;
}

// The AgentTrajectory info for ReasoningObject.
// Next Available ID: 52
message AgentTrajectoryInfoDebug {
  reserved 5, 6, 35;
  int32 trajectory_id = 1;
  double will_see_ego_time = 2;
  reserved 3, 14;
  reserved "road_priority", "road_precedence_debug_str";
  double likelihood = 4;
  repeated ConflictingLaneDebug conflicting_lanes = 7;
  prediction.pb.YieldIntention yield_intention = 8;
  planner.pb.RoadPrecedence.Enum road_precedence = 9;
  planner.pb.PrecedenceSource.Enum precedence_source = 10;
  planner.pb.PrecedenceSceneType.Enum precedence_scene_type = 21;
  bool is_using_crosswalk = 11;
  bool is_extra_cautious = 12;
  bool is_agent_extra_cautious_for_slow_cut_in_in_front = 50;
  bool is_oncoming = 13;
  bool has_overlap_region_in_encroachment_regions = 15;
  bool has_overlap_region_in_merge_fork_regions = 16;
  bool has_overlap_region_in_passive_merge_regions = 27;
  bool has_overlap_region_in_squeeze_regions = 36;
  bool is_same_direction = 17;
  bool is_overtaking_ego = 18;
  bool is_overtaken_by_ego = 19;
  bool is_exiting_roundabout_from_inner_lane = 20;
  bool consider_yield_intention = 22;
  bool is_turning_right = 23;
  bool is_turning_left = 24;
  bool is_stationary = 25;
  repeated OverlapRegionInfoDebug overlap_region_info_debug = 26;
  bool is_turning_right_infering_from_associated_lanes = 28
      [ deprecated = true ];
  bool is_turning_right_inferring_from_associated_lanes = 29;
  bool is_cutting_in = 39;
  bool is_cutting_out = 30;
  bool is_exiting_from_exit_zone = 31;
  bool is_merging_from_right_turn_at_junction = 32;
  bool is_merging_from_side_road = 33;
  bool is_on_u_turn_lane = 34;
  bool is_crossing_opposite_road_blocks = 48;
  bool is_inside_overtaking = 37;
  AgentReactionTrackerResult agent_reaction_tracker_result = 38;
  AgentReactionTrackerDebug agent_reaction_tracker_debug_info = 43;
  double yield_probability_with_intention_tracking = 40;
  bool is_moving_trajectory_for_start_to_move_agent = 41;
  bool is_large_vehicle_turning_along_with_ego = 42;
  bool is_complete_reversing = 44;
  bool is_kturn = 45;
  double predicted_average_acceleration = 46;
  string switch_time_debug_str = 47;
  LaneChangeAgentIntentionFusionResult agent_intention_with_model_fusion = 49;
  string debug_str = 51;
}

// The OverlapRegionInfo for AgentTrajectoryInfo.
// Next Available ID: 20
message OverlapRegionInfoDebug {
  reserved 4;
  int32 overlap_id = 1;
  bool is_using_crosswalk = 2;
  bool is_in_encroachment_regions = 3;
  bool is_in_xlane_nudge = 5;
  bool is_crossing_at_right_hook = 6;
  bool is_crossing_at_left_hook = 15;
  bool is_in_left_turn = 7;
  bool is_in_merge_regions = 8;
  bool is_in_fork_regions = 9;
  bool is_cut_in = 10;
  bool is_in_passive_merge_regions = 11;
  bool is_crossing_ego_path = 12;
  bool is_in_first_junction = 13;
  bool is_cut_out = 14;
  bool is_in_squeeze_regions = 16;
  bool is_cut_behind = 17;
  bool is_scene_vulnerable_to_yield_paradox = 18;
  bool includes_conflicting_lanes = 19;
}

// The ConflictingLaneInLaneSequence info for AgentTrajectoryInfo.
// Next Available ID: 4
message ConflictingLaneDebug {
  int64 lane_id = 1;
  // The start/end ra arclength of conflicting lane along path.
  double start_ra_arclength = 2;
  double end_ra_arclength = 3;
}

// The debug info for one construction zone.
// Next Available ID: 5
message ConstructionZoneDebug {
  int64 cz_id = 1;
  CautiousDrivingLimiterParameters limiter_parameters = 2;
  // The minimum clearance on both side when passing by the construction zone.
  double min_lateral_clearance = 3;
  string debug_str = 4;
}

// The debug info for ConstructionZoneReasoner.
// Next Available ID: 2
message ConstructionZoneReasonerDebug {
  repeated ConstructionZoneDebug construction_zones = 1;
}

// The debug info for TailgaterReasoner.
// Next Available ID: 2
message TailgaterReasonerDebug { repeated TailgaterAgentDebug agents = 1; }

// The agent debug info in TailgaterReasoner.
// Next Available ID: 4
message TailgaterAgentDebug {
  int64 agent_id = 1;
  voy.perception.ObjectType object_type = 2;
  repeated TailgaterTrajectoryDebug agent_trajectories = 3;
}

// The trajectory-level debug info in TailgaterReasoner.
// Next Available ID: 2
message TailgaterTrajectoryDebug { int32 trajectory_id = 1; }

// The debug info for one lane and the agents occupied the lane.
// Next Available ID: 3
message LaneAgentOccupancyDebug {
  int64 lane_id = 1;
  repeated int64 agent_id = 2;
}

// The debug info for a sequence of the lanes and its occupancy info.
// Next Available ID: 2
message LaneSequenceAgentOccupancyDebug {
  repeated LaneAgentOccupancyDebug lane_agent_occupancy = 1;
}

// The debug info for urgency info.
// Next Available ID: 10
message UrgencyInfoDebug {
  reserved 6;
  reserved "same_direction_non_leading_agent_ids";
  double should_creep = 1;
  bool has_ego_stuck_for_long_time = 2;
  string debug_str = 7;
  repeated planner.pb.UrgencyReason.Enum urgency_reason = 3;
  repeated LaneSequenceAgentOccupancyDebug neighbor_lane_clusters_occupancy = 4;
  repeated LaneSequenceAgentOccupancyDebug merge_lane_clusters_occupancy = 5;
  repeated int64 creeping_crosswalk_ids = 8;
  repeated int64 non_leading_interacting_agent_ids = 9;
}

// The world context debug for reasoning pipeline.
// Next Available ID: 7
message WorldContextDebug {
  reserved 5;
  reserved "urgency_info";
  RobotStateDebug robot_state = 1;
  int64 previous_dominant_risk_object_id = 2;
  int32 previous_dominant_risk_object_duration = 3;
  bool is_pull_out_triggered = 4;
  repeated NarrowPassageInfo narrow_passage_infos = 6;
}

// The robot state info in world context.
// Next Available ID: 10
message RobotStateDebug {
  int64 pose_timestamp = 1;
  double heading = 2;
  bool is_almost_stopped = 3;
  // The time duration in millisecond for which Ego has almost stopped.
  int64 almost_stopped_duration_ms = 4;
  int64 stuck_duration_ms = 9;
  optional int64 will_stop_ts = 8;
  bool has_vehicle_waiting_behind_ego = 5;
  bool has_ego_encroached_left_lane_boundary = 6;
  bool has_ego_encroached_right_lane_boundary = 7;
}

// The debug info for physical boundary proximity
// Next Available ID: 3
message PhysicalBoundaryProximityInfoPairDebug {
  repeated string left = 1;
  repeated string right = 2;
}

// The debug info for lane info in lane sequence
// Next Available ID: 5
message LaneInfoInLaneSequenceDebug {
  int64 lane_id = 1;
  double start_ra_arclength = 2;
  double end_ra_arclength = 3;
  repeated ConflictingLaneDebug conflicting_lanes = 4;
}

// The debug info for the lane sequence.
// Next Available ID: 4
message LaneSequenceDebug {
  repeated int64 lane_id = 1 [ deprecated = true ];
  repeated LaneInfoInLaneSequenceDebug lane_infos = 3;
  int64 current_lane_id = 2;
}

// The debug info for the traffic light of an entrance of a junction. Currently,
// there are only the traffic lights of left-turn, right-turn and straight road.
// Next Available ID: 10
message JunctionEntranceTrafficLights {
  // The left-turn traffic lights.
  repeated int64 left_turn_traffic_signals = 1;
  voy.TrafficLight.Color left_turn_color = 2;
  bool is_left_turn_protected = 3;
  // The straight traffic lights.
  repeated int64 straight_traffic_signals = 4;
  voy.TrafficLight.Color straight_color = 5;
  bool is_straight_protected = 6;
  // The right-turn traffic lights.
  repeated int64 right_turn_traffic_signals = 7;
  voy.TrafficLight.Color right_turn_color = 8;
  bool is_right_turn_protected = 9;
}

// The debug info for the traffic lights of an junction. One junction has four
// entrances. The entrance that Ego is using is called the current entrance. The
// other three entrances are named with respected to the current entrance.
// Next Available ID: 5
message JunctionTrafficLights {
  JunctionEntranceTrafficLights current_entrance = 1;
  JunctionEntranceTrafficLights left_entrance = 2;
  JunctionEntranceTrafficLights right_entrance = 3;
  JunctionEntranceTrafficLights front_entrance = 4;
}

// The debug of the junction traffic rules.
// Next Available ID: 3
message JunctionTrafficRuleDebug {
  int64 junction_id = 1;
  JunctionTrafficLights traffic_lights = 2;
}

// The debug of the crosswalk traffic rules.
// Next Available ID: 10
message CrosswalkTrafficRuleDebug {
  enum InferredTrafficLight {
    kUnknownColor = 0;
    kNoTrafficLight = 1;
    kGreen = 2;
    kRed = 3;
    kYellow = 4;
  }
  int64 crosswalk_id = 1;
  planner.pb.CrosswalkOrderType.Enum crosswalk_order = 2;
  planner.pb.RoadPrecedence.Enum road_precedence = 3;
  voy.TrafficLight.Color associated_pedestrian_traffic_light_color = 4;
  InferredTrafficLight inferred_traffic_light = 5;
  bool has_safety_island = 6;
  double left_range = 7;
  double left_same_direction_range = 8;
  double right_range = 9;
}

// The trajectory info debug for reasoning pipeline.
// Next Available ID: 14
message TrajectoryInfoDebug {
  RobotContextDebug robot_context = 1;
  LaneChangeStatusDebug lane_change_status = 2;
  LaneEncroachmentInfoDebug lane_encroachment_info_debug = 3
      [ deprecated = true ];
  CriticalRegionsDebug critical_regions = 4;
  PhysicalBoundaryProximityInfoPairDebug boundary_proximity_info = 5;
  LaneEncroachmentInfosDebug lane_encroachment_infos_debug = 6;
  LaneSequenceDebug lane_sequence_debug = 7;
  repeated JunctionTrafficRuleDebug junctions_debug = 8;
  repeated CrosswalkTrafficRuleDebug crosswalks_debug = 9;
  MergeGapInfoDebug merge_gap_info_debug = 10;
  repeated planner.pb.PointInfoForPolylineToPolylineGap
      path_to_lane_center_gap = 11;
  planner.pb.TrajectoryType trajectory_type = 12;
  string unique_path_homotopy_id = 13;
}

// The robot context info in trajectory info.
// TODO(speed): "is_in_unprotected_left_turn", "is_in_U_turn" and
// "is_going_straight", "is_in_right_turn" should be mutual exclusion. We could
// use enum class to replace them later.
// Next Available ID: 12
message RobotContextDebug {
  planner.pb.ManeuverType maneuver_type = 1;
  bool is_nudging = 2;
  bool is_in_unprotected_left_turn = 3;
  bool is_in_U_turn = 4;
  double plan_init_ra_arc_length_on_extended_path = 5;
  bool is_going_straight = 6;
  bool is_current_lane_red_light = 7;
  bool is_pull_out_jump_out = 8;
  bool is_in_right_turn = 9;
  bool is_ego_in_red_light_junction = 10;
  bool is_xlane_nudge_overtaking = 11;
  bool is_on_expressway = 12;
}

// Next Available ID: 8
message LaneChangeStatusDebug {
  double turn_signal_duration = 1;
  double distance_to_finish_lc = 2;
  bool is_abortable = 3;
  int64 leading_object_id = 4;
  int64 tailing_object_id = 5;
  planner.pb.LaneChangeMode direction = 6;
  planner.pb.LaneChangeState lane_change_state = 7;
}

// Next Available ID: 5
message LaneEncroachmentInfoDebug {
  math.pb.Side side = 1;
  repeated LongitudinalRange out_of_lane_segments = 2;
  repeated double lateral_encroachment_distance = 3;
  repeated double merge_back_start_ra_arc_length = 4;
}

// The debug info for the status between ego and lane marking when ego is
// straddling on it, including the direction to cross onto the lane marking and
// the maximum distance ego has encroached on both side.
// Next Available ID: 4
message LaneBoundaryCrossingStatusDebug {
  math.pb.Side direction_cross_onto_lane_marking = 1;
  double max_left_encroached_distance = 2;
  double max_right_encroached_distance = 3;
}

// The debug info for the critical encroachment region.
// Next Available ID: 6
message CriticalEncroachmentRangeInfoDebug {
  math.pb.Range ra_arclength_range = 1;
  math.pb.Side side = 2;
  double max_encroached_dist = 3;
  bool encroach_in_junction = 4;
  bool encroach_oncoming_lane = 5;
}

// Next Available ID: 7
message LaneEncroachmentInfosDebug {
  reserved 4;
  reserved "direction_cross_onto_lane_marking";
  planner.pb.EgoCurrentLaneRelativeState.Enum ego_current_lane_relative_state =
      1;
  LaneBoundaryCrossingStatusDebug lane_boundary_crossing_status = 5;
  LaneEncroachmentInfoDebug left_lane_encroachment_info = 2;
  LaneEncroachmentInfoDebug right_lane_encroachment_info = 3;
  repeated CriticalEncroachmentRangeInfoDebug
      critical_encroachment_range_infos = 6;
}

// Next Available ID: 3
message MergeGapInfoDebug {
  int64 lead_object_id = 1;
  int64 tail_object_id = 2;
}

// Next Available ID: 2
message CriticalRegionsDebug {
  repeated CriticalRegionDebug critical_regions = 1;
}

// Next Available ID: 3
message CriticalRegionDebug {
  planner.pb.CriticalRegionType.Enum type = 1;
  math.pb.Range ra_arclength = 2;
}

// The honk debug info for LeadAndMergeReasoner.
// Next Available ID: 4
message LeadAndMergeHonkDebug {
  enum DisallowedHonkReason {
    STATIONARY = 0;
    OUT_OF_TEMPORAL_CONTEXT = 1;
    BLOCKING_PATH = 2;
    BEHIND_EGO = 3;
    EGO_LOW_PRECEDENCE = 4;
    NA = 5;
  }
  bool should_trigger = 1;
  DisallowedHonkReason disallowed_reason = 2;
  // The minimum active discomfort level to trigger honk.
  double min_active_discomfort = 3;
}

// The EB debug info for LeadAndMergeReasoner.
// Next Available ID: 4
message LeadAndMergeEBDebug {
  enum EBReason {
    NA = 0;
    UNMATCHED_AGENT_TYPE = 1;
    BLOCKING_PATH = 2;
    OUT_OF_SPACIAL_CONTEXT = 3;
    COLLIDED_AT_EGO_SIDE = 4;
    RULE_BASED_HIGH_RISK = 5;
    PROXIMITY_REACH_EGO_PATH = 6;
    BEHIND_LEAD_BUMPER_IN_PASSIVE_SCENE = 7;
    LATERALLY_MOVING_AWAY = 8;
  }
  bool should_allow = 1;
  EBReason reason = 2;
  string emergency_brake_debug_str = 3;
}

// Debug info for a single setting adjustment.
// Next Available ID: 4
message SettingAdjustDebug {
  enum Reason {
    NA = 0;
    AGENT_FROM_BEHIND = 1;
    SQUEEZE_REGION = 2;
    LATERAL_MOVING_AGENT = 3;
    EGO_PATH_OVERTAKING = 4;
    SLOW_MOVING_AGENT = 5;
    AGENT_RISK = 6;
    LARGE_VEHICLE = 7;
    EGO_HIGH_PRECEDENCE = 8;
    LC_IN_PROGRESS = 9;
    PASSIVE_INTERACTION_SCENE = 10;
    EGO_AHEAD = 11;
    OVERLAP_IN_MERGE_FORK_LANE = 12;
    OVERLAP_IN_ENCROACHMENT_REGION = 13;
    WAS_PASS_OR_YIELD = 14;
    CYCLIST = 15;
    EGO_IS_OVERTAKEN = 16;
    ROUNDABOUT = 17;
    BLOCKING_AGENT = 18;
    CUT_IN_AGENT = 19;
    OPEN_DOOR_VEHICLE = 20;
    POTENTIAL_UTURN_BLOCKAGE = 21;
    RISK_OBJECT = 22;
    LC_AGENT_CANNOT_SEE_EGO = 23;
    LC_STM_AGENT = 24;
    CROSSING_LANE_AGENT = 25;
    LC_CRAWL = 26;
    EGO_LOW_SPEED = 27;
    AGENT_SLOWER_THAN_EGO = 28;
    AGENT_LATERALLY_FAR_AWAY = 29;
    CUT_OUT_AGENT = 30;
    DENSE_TRAFFIC = 31;
    REDUCE_COMFORT_YIELD_EXTRA_DIST = 32;
    UTURN_UNSTUCK = 33;
    YIELD_ALWAYS_POSSIBLE = 34;
    CREEP = 35;
    LEADING_AGENT = 36;
    DEFAULT = 37;
  }
  // We can use a single string type to explain the adjustment reasons. By
  // leveraging enum reasons in various combinations, we can express extensive
  // and detailed scene information, thereby reducing debug size.
  repeated Reason reasons = 1;
  double original_min_discomfort_value = 2;
  double adjusted_min_discomfort_value = 3;
}

// The debug info for one type constraint setting.
// Next Available ID: 10
message ConstraintSettingDebug {
  enum SettingType {
    NA = 0;
    PASS_LATERAL_GAP = 1;
    PASS_EXTRA_TIME = 2;
    PASS_REQUIRED_LATERAL_GAP = 3;
    SOFT_YIELD_REQUIRED_LATERAL_GAP = 4;
    SOFT_YIELD_EXTRA_DIST = 5;
    YIELD_EXTRA_DIST = 6;
    YIELD_REQUIRED_LATERAL_GAP = 7;
    YIELD_EXTRA_TIME = 8;
    SOFT_YIELD_EXTRA_TIME = 9;
    YIELD_STOP_RANGE = 10;
    SOFT_YIELD_STOP_RANGE = 11;
    MIN_CREEP_DISTANCE = 12;
    SOFT_MIN_CREEP_DISTANCE = 13;
  }

  SettingType setting_type = 1;
  repeated SettingAdjustDebug adjusts = 2;
}

// The debug info for constraint setting options.
// Next Available ID: 15
message ConstraintSettingOptionsDebug {
  reserved 13;
  reserved "should_be_cautious_with_aggressive_decel_cut_in_vehicle";
  enum Reason {
    NA = 0;
    OUT_OF_NEIGHBOUR_LANE = 1;
    HEADING_TOWARD_EGO = 2;
    EGO_HIGH_PRECEDENCE = 3;
    CUT_IN_AGENT = 4;
    HIGH_SPEED_AGENT = 5;
    AGENT_ON_EGO_PATH = 6;
    EGO_AHEAD = 7;
    AGENT_TYPE = 8;
    STATIONARY_AGENT = 9;
    EMPTY_OBJECT_OCCUPANCY_PARAM = 10;
  }

  bool should_be_cautious_with_lateral_moving_agent = 1;
  bool should_try_to_pass_slow_moving_vehicle = 2;
  bool should_be_cautious_in_squeeze_region = 3;
  bool should_be_cautious_in_pinch_region = 4;
  bool should_be_cautious_with_open_door_vehicle = 5;
  bool should_be_cautious_with_vehicle_cannot_see_ego_during_lc = 6;
  bool should_be_cautious_with_encroaching_lane_vehicle = 7;
  bool should_use_discomfort_varying_soft_yield_extra_distance_for_lc = 8;
  string debug_str = 9;
  bool should_be_cautious_with_slow_cut_in_vehicle = 10;
  repeated Reason slow_cut_in_vehicle_reasons = 11;
  bool should_be_cautious_with_lateral_moving_agent_in_lc = 12;
  bool should_be_cautious_with_agent_decelerating_aggressively = 14;
}

// The debug info for LeadAndMergeReasoner.
// Next Available ID: 3
message LeadAndMergeReasonerDebug {
  repeated LeadAndMergeAgentDebug agents = 1;
  ProgressRecoveryDebug progress_recovery = 2;
}

// The agent debug info in LeadAndMergeReasoner.
// Next Available ID: 5
message LeadAndMergeAgentDebug {
  int64 object_id = 1;
  voy.perception.ObjectType object_type = 2;
  string agent_debug_str = 3;
  repeated LeadAndMergeAgentPolicyDebug agent_policies = 4;
}

// The policy-level debug info in LeadAndMergeReasoner.
// Next Available ID: 9
message LeadAndMergeAgentPolicyDebug {
  reserved 3, 5;
  reserved "limiter_parameters", "required_lateral_gap_debug";
  int64 trajectory_id = 1;
  string policy_debug_str = 2;
  LeadAndMergeHonkDebug honk_debug = 4;
  LeadAndMergeEBDebug emergency_brake_debug = 8;
  ConstraintSettingOptionsDebug constraint_setting_options_debug = 7;
  repeated ConstraintSettingDebug settings = 6;
}

// The debug info for progress recovery.
// Next Available ID: 4
message ProgressRecoveryDebug {
  bool has_triggered = 1;
  int64 start_timestamp = 2;
  string debug_str = 3;
}

// The debug info for LaneChangeSpeedReasoner.
// Next Available ID: 6
message LaneChangeSpeedReasonerDebug {
  reserved 1;
  reserved "lateral_search_range";
  repeated int64 interested_objects = 2;
  repeated OverlapRegion gap_align_overlap_regions = 3;
  planner.pb.ReasoningGapInfo optimal_reasoning_gap_info = 5;
  string debug_str = 4;
}

// The debug info for TurnSignalReasoner.
// Next Available ID: 3
message TurnSignalReasonerDebug {
  planner.pb.TurnMode turn_mode = 1;
  string debug_str = 2;
}

// The debug info for HoldReasoner.
// Next Available ID: 6
message HoldReasonerDebug {
  double waypoint_assist_end_arclength = 1;
  double short_path_protection_end_arclength = 3;
  double short_path_protection_threshold = 4;
  double high_curvature_protection_arclength = 5;
  string debug_str = 2;
}

// The debug info for VRUReasoner.
// Next Available ID: 4
message VruReasonerDebug {
  // The crosswalks' id that discomfort for progress has been triggered for
  // them.
  reserved 1, 3;
  reserved "discomfort_for_progress_triggered_crosswalks",
      "creep_decision_debug_str";
  repeated VruReasonerAgentDebug agent_debugs = 2;
}

// The agent-level debug info for VruReasonerDebug.
// Next Available ID: 3
message VruReasonerAgentDebug {
  reserved 2;
  reserved "debug_str";
  int64 object_id = 1;
  repeated VruTrajectoryDebug agent_trajectories = 3;
}

// The trajectory-level debug info in VruReasonerAgentDebug.
// Next Available ID: 4
message VruTrajectoryDebug {
  int32 trajectory_id = 1;
  string debug_str = 2;
  repeated VruAgentPolicyDebug agent_policies = 3;
}

// The agentpolicy-level debug info in VruTrajectoryDebug.
// Next Available ID: 4
message VruAgentPolicyDebug {
  int32 overlap_region_id = 1;
  string policy_debug_str = 2;
  VruHonkDebug honk_debug = 3;
}

// The honk debug info for VruAgentPolicyDebug.
// Next Available ID: 3
message VruHonkDebug {
  // Next Available ID: 7
  enum DisallowedHonkReason {
    NA = 0;
    EGO_SLOW_SPEED = 1;
    EGO_LOW_PRECEDENCE = 2;
    OUT_OF_TEMPORAL_CONTEXT = 3;
    BEHIND_EGO = 4;
    STATIONARY_AGENT = 5;
    OBVIOUSLY_SLOW_DOWN = 6;
  }
  bool should_trigger = 1;
  DisallowedHonkReason disallowed_reason = 2;
}

// The debug info for HonkReasoner.
// Next Available ID: 4
message HonkReasonerDebug {
  string debug_str = 1;
  repeated HonkSceneDebug honk_scenes = 2;
  bool is_in_last_honk_cycle = 3;
}

// The debug info for honk scene.
// Next Available ID: 7
message HonkSceneDebug {
  reserved 1;
  reserved "honk_scene";
  repeated TriggeringHonkAgentDebug triggering_agents = 2;
  string scene_debug_str = 3;
  planner.pb.HonkScene.Enum honk_scene_type = 4;
  bool is_triggered_in_scene = 5;
  int64 triggered_agent_id = 6;
}

// The debug info for honk agent.
// Next Available ID: 4
message TriggeringHonkAgentDebug {
  int64 object_id = 1;
  bool should_trigger = 2;
  string debug_str = 3;
}

// The debug info for HazardLightReasoner.
// Next Available ID: 3
message HazardLightReasonerDebug {
  string debug_str = 1;
  enum HazardLightState {
    UNDEFINED = 0;
    OFF = 1;
    ON_BY_PLANNER = 2;
    ON_BY_RA = 3;
  }
  HazardLightState hazard_light_state = 2;
}

// The debug info for OncomingAgentReasoner.
// Next Available ID: 2
message OncomingAgentReasonerDebug { repeated OncomingAgentDebug agents = 1; }

// The agent debug info in OncomingAgentReasoner.
// Next Available ID: 3
message OncomingAgentDebug {
  int64 object_id = 1;
  repeated OncomingAgentTrajectoryDebug agent_trajectories = 2;
}

// The trajectory-level debug info in OncomingAgentReasoner.
// Next Available ID: 3
message OncomingAgentTrajectoryDebug {
  int32 trajectory_id = 1;
  string debug_str = 2;
}

// The debug info for pull over prepare reasoner.
// Next Available ID: 4
message PullOverPrepareReasonerDebug {
  reserved 1, 2;
  reserved "region", "agents";
  repeated OverlapRegion gap_align_overlap_regions = 3;
}

// The debug info for JaywalkerReasoner.
// Next Available ID: 2
message JaywalkerReasonerDebug {
  repeated JaywalkerAgentDebug agent_debugs = 1;
}

// The agent-level debug info for JaywalkerReasonerDebug.
// Next Available ID: 3
message JaywalkerAgentDebug {
  int64 object_id = 1;
  repeated JaywalkerTrajectoryDebug agent_trajectories = 2;
}

// The trajectory-level debug info in JaywalkerAgentDebug.
// Next Available ID: 4
message JaywalkerTrajectoryDebug {
  int32 trajectory_id = 1;
  string debug_str = 2;
  repeated JaywalkerAgentPolicyDebug agent_policies = 3;
}

// The agentpolicy-level debug info in JaywalkerTrajectoryDebug.
// Next Available ID: 4
message JaywalkerAgentPolicyDebug {
  int32 overlap_region_id = 1;
  string policy_debug_str = 2;
  JaywalkerHonkDebug honk_debug = 3;
}

// The honk debug info for JaywalkerAgentPolicyDebug.
// Next Available ID: 3
message JaywalkerHonkDebug {
  // Next Available ID: 7
  enum DisallowedHonkReason {
    NA = 0;
    EGO_SLOW_SPEED = 1;
    EGO_LOW_PRECEDENCE = 2;
    OUT_OF_TEMPORAL_CONTEXT = 3;
    BEHIND_EGO = 4;
    STATIONARY_AGENT = 5;
    OBVIOUSLY_SLOW_DOWN = 6;
  }
  bool should_trigger = 1;
  DisallowedHonkReason disallowed_reason = 2;
}

// The debug info for StopSignReasoner.
// Next Available ID: 2
message StopSignReasonerDebug { repeated StopSignInfoDebug stop_sign_info = 1; }

// The debug of the single stop sign in lane sequence.
// Next Available ID: 5
message StopSignInfoDebug {
  int64 stop_sign_id = 1;
  int64 controlled_lane_id = 2;
  double stop_until_time_in_sec = 3;
  // The string shows some specific information.
  string debug_str = 4;
}

// The debug for an AgentOccupiedMapElementInfo.
// Next Available ID: 5
message AgentOccupiedMapElementDebug {
  planner.pb.MapElementType.Enum element_type = 1;
  int64 element_id = 2;
  optional double distance_to_border = 3;
  optional double heading_diff = 4;
}

// The debug for agent_map_element_occupancy_seed.AgentOccupiedMapElementInfos.
// Next Available ID: 5
message AgentOccupiedMapElementInfosDebug {
  int64 timestamp = 1;
  repeated AgentOccupiedMapElementDebug occupied_map_elements = 2;
  optional AgentOccupiedMapElementDebug latest_occupied_isolated_lane = 3;
  optional AgentOccupiedMapElementDebug latest_occupied_isolated_zone = 4;
};
