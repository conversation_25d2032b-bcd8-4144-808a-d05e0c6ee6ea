#include "planner/selection/costs/traffic_rule_cost.h"

#include <algorithm>
#include <string>
#include <utility>

#include "absl/strings/str_format.h"
#include "planner/utility/stuck_signal/stuck_detector_utils.h"

namespace planner {
namespace selection {

namespace {
const double kWaitingInTrafficLookAheadDistanceInM = 35.0;
constexpr double kProgressTurnLookAheadDistanceInMeter = 30;
// Field name in cost debug message.
const char kAvoidNudgeReasonFieldName[] = "Traffic_rule:AvoidNudgeReason";
}  // namespace

// Get next left/right/U turn lane within look ahead distance.
// TODO(chengji): Merge with TrajectoryMetaData::HasUTurnAhead
const pnc_map::Lane* GetNextTurnLane(const TrajectoryMetaData& candidate,
                                     double look_ahead_distance) {
  DCHECK(!candidate.trajectory.poses().empty());
  const math::geometry::Point2d ego_position{
      candidate.trajectory.poses().begin()->x_pos(),
      candidate.trajectory.poses().begin()->y_pos()};

  bool is_checking_lane_ahead = false;
  double already_look_ahead_distance_in_m = 0.0;
  for (const pnc_map::Lane* lane : candidate.lane_sequence()) {
    // First try to identify the position of `lane` relative to `current_lane`
    // and update `already_look_ahead_distance_in_m`
    if (is_checking_lane_ahead) {
      already_look_ahead_distance_in_m += lane->length();
    } else if (lane == candidate.current_lane()) {
      is_checking_lane_ahead = true;
      already_look_ahead_distance_in_m =
          lane->length() - lane->GetArclength(ego_position);
    }
    // Skip turn checks if `lane` is behind ego
    if (!is_checking_lane_ahead) {
      continue;
    }
    if ((lane->turn() == hdmap::Lane::LEFT ||
         lane->turn() == hdmap::Lane::RIGHT ||
         lane->turn() == hdmap::Lane::U_TURN) &&
        lane->IsInJunction()) {
      return lane;
    }
    if (already_look_ahead_distance_in_m > look_ahead_distance) {
      return nullptr;
    }
  }
  // All lanes checked. No turns. Max look ahead distance not reached.
  return nullptr;
}

bool IsAvoidNudgeCondition(
    const std::vector<TrajectoryMetaData*>& trajectory_candidates) {
  if (!FLAGS_planning_enable_progress_cost_waiting_in_traffic_reasoning)
    return false;
  for (const auto* candidate : trajectory_candidates) {
    if (candidate->IsCrossLane()) {
      return false;
    }
    if (candidate->IsPullOver()) {
      return false;
    }
  }
  return true;
}

bool IsWaitingInTraffic(const SpeedWorldModel& world_model,
                        TrajectoryMetaData* candidate) {
  DCHECK_NE(candidate, nullptr);
  const auto stuck_parameters = PlannerConfigCenter::GetInstance()
                                    .GetDecoupledForwardManeuverConfig()
                                    .trajectory_selection_config()
                                    .hazardous_state_config()
                                    .stuck_config();

  std::vector<TrafficJamVehicleMetaData> vehicles_on_lane_sequence =
      GenerateVehiclesOnLaneSequence(world_model, *candidate, stuck_parameters);
  const double max_arclength_m = candidate->front_bumper_arclength() +
                                 kWaitingInTrafficLookAheadDistanceInM;

  std::vector<planner::ObjectId> front_vehicle_ids;
  for (const auto& object_meta : vehicles_on_lane_sequence) {
    if (object_meta.arclength_m < max_arclength_m &&
        !object_meta.object->is_blockage_object()) {
      front_vehicle_ids.push_back(object_meta.object->id());
    }
  }

  const int num_front_vehicles = front_vehicle_ids.size();
  candidate->AddCostDebugValue("avoid_nudge: num_front_vehicles",
                               num_front_vehicles);
  const bool waiting =
      num_front_vehicles >= kNumOfVehicleThresholdToWaitInTraffic;
  if (waiting) {
    std::string avoid_nudge_reason = "Waiting for ";
    for (size_t i = 0; i < kNumOfVehicleThresholdToWaitInTraffic; ++i) {
      absl::StrAppendFormat(&avoid_nudge_reason, "%d ", front_vehicle_ids[i]);
    }
    candidate->AddCostDebugMessage(kAvoidNudgeReasonFieldName,
                                   avoid_nudge_reason);
  }
  return waiting;
}

bool MatchAvoidNudgeHomotopy(
    const TrajectoryMetaData& candidate,
    const std::set<BehaviorTypeAndIndex>& avoid_nudge_homotopies) {
  return avoid_nudge_homotopies.count(
             {candidate.behavior_type(), candidate.geometry_guidance_ix()}) > 0;
}

bool IsAgentTurningOrGoingToTurn(
    const PredictedTrajectoryWrapper& leading_agent_trajectory,
    const pnc_map::Lane* next_turn_lane,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  const auto next_turn_lane_iter =
      std::find(lane_sequence.begin(), lane_sequence.end(), next_turn_lane);
  DCHECK(next_turn_lane_iter != lane_sequence.end());
  const auto search_range_end = next_turn_lane_iter + 1;
  // If the agent is not in the junction, latest_isolated_lane is usually the
  // agent's currently occupied lane. Otherwise it's usually the lane before
  // agent entering the junction(i.e, the predecessor lane of the agent's
  // currently occupied lane).
  if (leading_agent_trajectory.latest_isolated_lane_opt().has_value() &&
      leading_agent_trajectory.latest_isolated_lane_opt()->lane_ptr !=
          nullptr) {
    const pnc_map::Lane* leading_agent_latest_isolated_lane =
        leading_agent_trajectory.latest_isolated_lane_opt()->lane_ptr;
    // Returns true if idx(leading_agent_lane) <= idx(next_turn_lane) in
    // lane_sequence
    return std::find(lane_sequence.begin(), search_range_end,
                     leading_agent_latest_isolated_lane) != search_range_end;
  }
  // It's also possible that latest_isolated_lane is not set, but this should
  // rarely happen. Then we want to explicitly compare the agent's occupied
  // lanes.
  for (const route_association::MapElementAndPoseInfo& element :
       leading_agent_trajectory.current_occupied_map_elements()) {
    if (element.lane_ptr == nullptr) {
      continue;
    }
    if (std::find(lane_sequence.begin(), search_range_end, element.lane_ptr) !=
        search_range_end) {
      return true;
    }
  }
  return false;
}

std::set<BehaviorTypeAndIndex> DetectAvoidNudgeHomotopies(
    const std::vector<TrajectoryMetaData*>& trajectory_candidates,
    const SpeedWorldModel& world_model) {
  std::set<BehaviorTypeAndIndex> avoid_nudge_homotopies;
  for (TrajectoryMetaData* candidate : trajectory_candidates) {
    const pnc_map::Lane* next_turn_lane =
        GetNextTurnLane(*candidate, kProgressTurnLookAheadDistanceInMeter);
    if (next_turn_lane != nullptr) {
      const PredictedTrajectoryWrapper* leading_agent_trajectory_ptr =
          common::FindClosestLeadAgentTrajectory(
              world_model.pose(), candidate->lane_sequence(),
              candidate->lane_sequence_geometry().nominal_path,
              world_model.planner_object_map(),
              world_model.object_prediction_map());
      // Currently we assume leading agent is the object to nudge, which is not
      // always true.
      // TODO(chengji): Check object_to_nudge == leading_agent
      if (leading_agent_trajectory_ptr != nullptr) {
        // If the leading agent already finishes turning, then we don't need to
        // avoid nudge.
        if (!IsAgentTurningOrGoingToTurn(*leading_agent_trajectory_ptr,
                                         next_turn_lane,
                                         candidate->lane_sequence())) {
          continue;
        }
        avoid_nudge_homotopies.insert(
            {candidate->behavior_type(), candidate->geometry_guidance_ix()});
        const std::string avoid_nudge_reason = absl::StrFormat(
            "%d: %s turn. lane=%d", leading_agent_trajectory_ptr->object_id(),
            hdmap::Lane::Turn_Name(next_turn_lane->turn()),
            next_turn_lane->id());
        candidate->AddCostDebugMessage(kAvoidNudgeReasonFieldName,
                                       avoid_nudge_reason);
      }
      continue;
    }
    if (IsWaitingInTraffic(world_model, candidate)) {
      avoid_nudge_homotopies.insert(
          {candidate->behavior_type(), candidate->geometry_guidance_ix()});
      continue;
    }
  }
  return avoid_nudge_homotopies;
}

std::vector<double> ComputeTrafficRuleCost(
    const std::vector<TrajectoryMetaData*>& candidates,
    const SpeedWorldModel& world_model) {
  std::vector<double> cost_values(candidates.size(), 0.0);
  std::set<BehaviorTypeAndIndex> avoid_nudge_homotopies =
      IsAvoidNudgeCondition(candidates)
          ? DetectAvoidNudgeHomotopies(candidates, world_model)
          : std::set<BehaviorTypeAndIndex>{};

  for (size_t i = 0; i < candidates.size(); ++i) {
    // Add avoid nudge traffic rule violation cost
    if (MatchAvoidNudgeHomotopy(*candidates[i], avoid_nudge_homotopies) &&
        candidates[i]->homotopy() != planner::pb::IntentionResult::IGNORE_ALL) {
      cost_values[i] += kAvoidNudgeViolationPenalty;
    }

    cost_values[i] = math::Clamp(cost_values[i], 0.0, 1.0);
  }

  return cost_values;
}

}  // namespace selection
}  // namespace planner
