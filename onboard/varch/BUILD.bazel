load("@rules_proto//proto:defs.bzl", "proto_library")
load("@rules_proto_grpc//python:defs.bzl", "python_proto_library")
load(
    "//bazel:defs.bzl",
    "install",
    "python_setup",
    "shared_library",
    "voy_add_rt_event",
    "voy_add_trace_provider",
)

package(default_visibility = ["//visibility:public"])

voy_add_trace_provider(
    name = "voy_trace_provider_varch",
    manifest_file = ":trace_varch_manifest.json",
)

voy_add_rt_event(
    name = "voy_rt_event_varch",
    manifest_file = ":rt_event_varch_manifest.json",
)

python_setup(
    name = "python_setup",
    setup_py = ":setup.py",
    deps = glob(["varchpy/*.py"]),
)

proto_library(
    name = "protos",
    srcs = [
        "protos/determinism/determinism_tracer.proto",
        "protos/vnode/action_distribution.proto",
        "protos/vnode/action_dto.proto",
        "protos/vnode/aligned_mode_checker.proto",
        "protos/vnode/feedback.proto",
        "protos/vnode/frame.proto",
        "protos/vnode/global_config.proto",
        "protos/vnode/message_ack.proto",
        "protos/vnode/msg_type_size_config.proto",
        "protos/vnode/periodic_trigger_dto.proto",
        "protos/vnode/playtime_dto.proto",
        "protos/vnode/raw_type.proto",
        "protos/vnode/smart_bridge_config.proto",
        "protos/vnode/subscriber_conf.proto",
        "protos/vnode/timeout_message.proto",
        "protos/vnode/transport_option.proto",
        "protos/vnode/trigger_policy.proto",
        "protos/vnode/vgraph_conf.proto",
        "protos/vnode/zero_copy_config.proto",
        "protos/vtest/vtest.proto",
    ],
    strip_import_prefix = "/onboard",
    deps = [
        "//onboard/common/voy_protos:voy_options_protos",
    ],
)

python_proto_library(
    name = "protos_python",
    extra_protoc_args = ["--experimental_allow_proto3_optional"],
    protos = [":protos"],
)

genrule(
    name = "gen_git_version_cpp",
    srcs = ["base/git_version.cpp.in"],
    outs = ["git_version.cpp"],
    cmd = "$(execpath base/gen_git_version.sh) $< $@",
    tags = [
        "local",
        "no-cache",
    ],
    tools = ["base/gen_git_version.sh"],
)

shared_library(
    name = "varch_log",
    srcs = [
        "utils/log.cpp",
    ],
    hdrs = [
        "utils/log.h",
        "utils/log_impl/log_impl.h",
    ],
    include_prefix = "varch",
    deps = [
        "@voy-sdk//:boost",
        "@voy-sdk//:gflags",
        "@voy-sdk//:glog",
    ],
)

shared_library(
    name = "varch_base",
    srcs = [
        "base/date.cpp",
        "base/env.cpp",
        "base/file.cpp",
        "base/net.cpp",
        "base/print.cpp",
        "base/proto.cpp",
        "base/split.cpp",
        "base/thread.cpp",
        "base/thread_name.cpp",
        "base/thread_pool.cpp",
        "base/time/clock.cpp",
        "base/time/duration.cpp",
        "base/time/now.cpp",
        "base/time/rate.cpp",
        "base/time/time.cpp",
        "base/timer/timer.cpp",
        "base/timer/timing_wheel.cpp",
        "git_version.cpp",
        "scheduler/config_builder.cpp",
        "scheduler/count_down_latch.cpp",
        "scheduler/detail/context_queue.cpp",
        "scheduler/detail/sched_algo.cpp",
        "scheduler/detail/scheduler.cpp",
        "scheduler/detail/worker.cpp",
        "utils/config/config.cpp",
        "utils/latency/system_latency.cpp",
        "utils/parallel/parallel_impl.cpp",
        "utils/parallel/task.cpp",
    ],
    hdrs = [
        "base/argparse.hpp",
        "base/array_block_priority_queue.h",
        "base/atomic_rw_lock.h",
        "base/concurrent_bitset.h",
        "base/date.h",
        "base/defer.h",
        "base/detail/finally.h",
        "base/env.h",
        "base/exception.h",
        "base/file.h",
        "base/git_version.h",
        "base/ipriority_queue.h",
        "base/macro.h",
        "base/map_block_priority_queue.h",
        "base/memory.h",
        "base/msg_traits.h",
        "base/net.h",
        "base/non_copyable.h",
        "base/print.h",
        "base/proto.h",
        "base/rw_lock_guard.h",
        "base/singleton.h",
        "base/spin_lock.h",
        "base/strings/split.h",
        "base/template_util.h",
        "base/thread.h",
        "base/thread_name.h",
        "base/thread_pool.h",
        "base/time/clock.h",
        "base/time/duration.h",
        "base/time/now.h",
        "base/time/rate.h",
        "base/time/time.h",
        "base/timer/timer.h",
        "base/timer/timer_bucket.h",
        "base/timer/timer_task.h",
        "base/timer/timing_wheel.h",
        "base/tuple_utility.h",
        "scheduler/all.h",
        "scheduler/config_builder.h",
        "scheduler/configuration.h",
        "scheduler/constants.h",
        "scheduler/count_down_latch.h",
        "scheduler/detail/context_queue.h",
        "scheduler/detail/fiber_properties.h",
        "scheduler/detail/metric.h",
        "scheduler/detail/sched_algo.h",
        "scheduler/detail/scheduler.h",
        "scheduler/detail/worker.h",
        "scheduler/fiber/fiber_local.h",
        "scheduler/scheduler.h",
        "scheduler/this_fiber.h",
        "trace.h",
        "utils/config.h",
        "utils/latency/latency_data_base.h",
        "utils/latency/system_latency.h",
        "utils/latency/system_latency_summary.h",
        "utils/name.h",
        "utils/parallel.h",
        "utils/parallel/parallel_impl.h",
        "utils/parallel/task.h",
        "utils/stats.h",
        "utils/trace.h",
    ],
    include_prefix = "varch",
    deps = [
        ":varch_log",
        ":voy_trace_provider_varch",
        "//onboard/common/av_comm:voy_common_common",
        "//protobuf_cpp:protos_cpp",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:boost",
        "@voy-sdk//:glog",
        "@voy-sdk//:yaml-cpp",
    ],
)

shared_library(
    name = "varch_global",
    srcs = [
        "vnode/computational_graph.cpp",
        "vnode/global.cpp",
        "vnode/smart_bridge/smart_bridge_flags.cpp",
        "vnode/system_call.cpp",
    ],
    hdrs = [
        "vnode/computational_graph.h",
        "vnode/detail/constants.h",
        "vnode/global.h",
        "vnode/smart_bridge/smart_bridge_flags.h",
        "vnode/system_call.h",
    ],
    include_prefix = "varch",
    deps = [
        ":varch_base",
        ":varch_log",
        "//onboard/common/shm:voy_shm_base",
        "@voy-sdk//:boost",
        "@voy-sdk//:fmt",
        "@voy-sdk//:ros",
    ],
)

shared_library(
    name = "varch",
    srcs = [
        "init.cpp",
        "vnode/detail/deterministic/activity_context.cpp",
        "vnode/detail/deterministic/activity_service.cpp",
        "vnode/detail/deterministic/event_info.cpp",
        "vnode/detail/deterministic/frame_alignment_checker.cpp",
        "vnode/detail/deterministic/frame_publish_service.cpp",
        "vnode/detail/deterministic/sim_frame_center.cpp",
        "vnode/detail/deterministic/transformator.cpp",
        "vnode/detail/fastdds_log/fastdds_gflags.cpp",
        "vnode/detail/fastdds_log/fastdds_log.cpp",
        "vnode/detail/publisher/message_publish_hijack_hook.cpp",
        "vnode/detail/publisher/message_publish_hook.cpp",
        "vnode/detail/publisher/publisher_base.cpp",
        "vnode/detail/publisher/recorder_publisher_intra.cpp",
        "vnode/detail/replay_test/auxiliary_message_service.cpp",
        "vnode/detail/replay_test/fiber_pool_with_sorted_tasks.cpp",
        "vnode/detail/replay_test/replay_test_scheduler.cpp",
        "vnode/detail/replay_test/replay_test_service.cpp",
        "vnode/detail/shm/atomic_rw_lock.cpp",
        "vnode/detail/shm/memory_block_segment.cpp",
        "vnode/detail/shm/message_meta_info.cpp",
        "vnode/detail/shm/message_meta_segment.cpp",
        "vnode/detail/shm/reader_infos.cpp",
        "vnode/detail/shm/shared_mutex.cpp",
        "vnode/detail/shm/zero_copy_message_checker.cpp",
        "vnode/detail/shm/zero_copy_message_reader.cpp",
        "vnode/detail/shm/zero_copy_message_reader_factory.cpp",
        "vnode/detail/shm/zero_copy_message_writer.cpp",
        "vnode/detail/shm/zero_copy_message_writer_factory.cpp",
        "vnode/detail/subscriber/subscriber_base.cpp",
        "vnode/detail/subscriber/subscriber_option.cpp",
        "vnode/detail/transport/auto_factory.cpp",
        "vnode/detail/transport/inter_factory.cpp",
        "vnode/detail/transport/intra_factory.cpp",
        "vnode/detail/transport/sub_process_pool.cpp",
        "vnode/detail/transport/tcp_factory.cpp",
        "vnode/detail/transport/transport_factory_base.cpp",
        "vnode/detail/transport/udp_factory.cpp",
        "vnode/detail/utils.cpp",
        "vnode/detail/vnode_base.cpp",
        "vnode/detail/vnode_core.cpp",
        "vnode/message_ack/ack_notifier.cpp",
        "vnode/message_ack/ack_receiver.cpp",
        "vnode/message_ack/ack_sender.cpp",
        "vnode/message_store/message_meta_info.cpp",
        "vnode/message_store/message_view.cpp",
        "vnode/message_store/message_view_impl.cpp",
        "vnode/message_util.cpp",
        "vnode/smart_bridge/config.cpp",
    ] + select({
        "//bazel/platforms:is_rigel": [],
        "//conditions:default": [
            "health_reporter/health_reporter.cpp",
            "vnode/detail/transport/pcie_factory.cpp",
        ],
    }),
    hdrs = [
        "init.h",
        "vnode/detail/activity_service_interface.h",
        "vnode/detail/dds_topic_manager.h",
        "vnode/detail/dds_zerocopy_data_for_recorder_struct.h",
        "vnode/detail/dds_zerocopy_data_struct.h",
        "vnode/detail/dds_zerocopy_shared_ptr_struct.h",
        "vnode/detail/default_participant_listener.h",
        "vnode/detail/deterministic/activity_context.h",
        "vnode/detail/deterministic/activity_service.h",
        "vnode/detail/deterministic/entity.h",
        "vnode/detail/deterministic/event_info.h",
        "vnode/detail/deterministic/frame_alignment_checker.h",
        "vnode/detail/deterministic/frame_publish_service.h",
        "vnode/detail/deterministic/sim_frame_center.h",
        "vnode/detail/deterministic/simulation_activity_context.h",
        "vnode/detail/deterministic/transformator.h",
        "vnode/detail/fastdds_log/fastdds_gflags.h",
        "vnode/detail/fastdds_log/fastdds_log.h",
        "vnode/detail/publisher/message_publish_hijack_hook.h",
        "vnode/detail/publisher/message_publish_hook.h",
        "vnode/detail/publisher/publish_params.h",
        "vnode/detail/publisher/publisher.h",
        "vnode/detail/publisher/publisher_base.h",
        "vnode/detail/publisher/publisher_option.h",
        "vnode/detail/publisher/publisher_wrapper.h",
        "vnode/detail/publisher/recorder_publisher_intra.h",
        "vnode/detail/replay_test/auxiliary_message_service.h",
        "vnode/detail/replay_test/distribution_dao.h",
        "vnode/detail/replay_test/fiber_pool.h",
        "vnode/detail/replay_test/fiber_pool_with_sorted_tasks.h",
        "vnode/detail/replay_test/fiber_task.h",
        "vnode/detail/replay_test/fiber_task_interface.h",
        "vnode/detail/replay_test/pooled_shared_work.h",
        "vnode/detail/replay_test/pooled_work_stealing.h",
        "vnode/detail/replay_test/replay_test_scheduler.h",
        "vnode/detail/replay_test/replay_test_service.h",
        "vnode/detail/replay_test/task_queue.h",
        "vnode/detail/replay_test/vnode_task.h",
        "vnode/detail/subscriber/subscriber.h",
        "vnode/detail/subscriber/subscriber_base.h",
        "vnode/detail/subscriber/subscriber_metric_collector.h",
        "vnode/detail/subscriber/subscriber_option.h",
        "vnode/detail/subscriber/subscriber_wrapper.h",
        "vnode/detail/sync_policies/master_slave_policy.h",
        "vnode/detail/sync_policies/master_slave_simulation_policy.h",
        "vnode/detail/sync_policies/periodic_policy.h",
        "vnode/detail/sync_policies/periodic_simulation_policy.h",
        "vnode/detail/sync_policies/policy_base.h",
        "vnode/detail/synchronizer.h",
        "vnode/detail/synchronizer_interface.h",
        "vnode/detail/transport/async_msg_wrapper.h",
        "vnode/detail/transport/auto_factory.h",
        "vnode/detail/transport/common_publisher_processor.h",
        "vnode/detail/transport/inter_factory.h",
        "vnode/detail/transport/intra_factory.h",
        "vnode/detail/transport/sub_process_pool.h",
        "vnode/detail/transport/tcp_factory.h",
        "vnode/detail/transport/transport_factory.h",
        "vnode/detail/transport/transport_factory_base.h",
        "vnode/detail/transport/udp_factory.h",
        "vnode/detail/transport/udp_sub_processor.h",
        "vnode/detail/underlay_message/message_adapter.h",
        "vnode/detail/underlay_message/message_adapter_pub_sub_type.h",
        "vnode/detail/underlay_message/raw_message.h",
        "vnode/detail/underlay_message/zero_copy_handle.h",
        "vnode/detail/utils.h",
        "vnode/detail/vnode_base.h",
        "vnode/detail/vnode_core.h",
        "vnode/emulator_common/constants.h",
        "vnode/emulator_common/far_future_message_manager.h",
        "vnode/emulator_common/helper_functions.h",
        "vnode/emulator_common/message_buffer_manager.h",
        "vnode/include/shm/atomic_rw_lock.h",
        "vnode/include/shm/memory_block_segment.h",
        "vnode/include/shm/message_meta_info.h",
        "vnode/include/shm/message_meta_segment.h",
        "vnode/include/shm/reader_infos.h",
        "vnode/include/shm/segment_name.h",
        "vnode/include/shm/shared_mutex.h",
        "vnode/include/shm/zero_copy_deleter.h",
        "vnode/include/shm/zero_copy_message_checker.h",
        "vnode/include/shm/zero_copy_message_reader.h",
        "vnode/include/shm/zero_copy_message_reader_factory.h",
        "vnode/include/shm/zero_copy_message_writer.h",
        "vnode/include/shm/zero_copy_message_writer_factory.h",
        "vnode/message_ack/ack_notifier.h",
        "vnode/message_ack/ack_receiver.h",
        "vnode/message_ack/ack_sender.h",
        "vnode/message_store/message_event.h",
        "vnode/message_store/message_meta_info.h",
        "vnode/message_store/message_store.h",
        "vnode/message_store/message_tuple.h",
        "vnode/message_store/message_view.h",
        "vnode/message_store/message_view_impl.h",
        "vnode/message_util.h",
        "vnode/pub_messages.h",
        "vnode/smart_bridge/config.h",
        "vnode/sub_messages.h",
        "vnode/sync_subscriber.h",
        "vnode/synchronizer/synchronizer.h",
        "vnode/synchronizer/synchronizer_simulation.h",
        "vnode/synchronizer/timeout_controller.h",
        "vnode/synchronizer/timeout_controller_simulation.h",
        "vnode/trigger_policy/align_constraint.h",
        "vnode/trigger_policy/timeout_controller_interface.h",
        "vnode/trigger_policy/trigger_policy_interface.h",
        "vnode/trigger_policy/xtrigger_with_timeout.h",
        "vnode/trigger_policy/xtrigger_with_timeout_simulation.h",
        "vnode/types.h",
        "vnode/vnode.h",
    ] + select({
        "//bazel/platforms:is_rigel": [],
        "//conditions:default": [
            "health_reporter/health_reporter.h",
            "vnode/detail/transport/pcie_factory.h",
        ],
    }),
    defines = select({
        "//bazel/platforms:is_rigel": [
            "VARCH_NO_VOY_DEPENDENCY",
            "VARCH_NO_EXTEND_FASTDDS_API",
        ],
        "//conditions:default": [],
    }),
    include_prefix = "varch",
    deps = [
        ":varch_base",
        ":varch_global",
        ":varch_log",
        ":voy_rt_event_varch",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/node:voy_memento",
        "//onboard/common/shm:voy_shm_base",
        "//onboard/gen4/ntb_bridge:ntb_driver_checker",
        "//onboard/gen4/ntb_bridge:ntb_sender",
        "@voy-sdk//:boost",
        "@voy-sdk//:fastcdr",
        "@voy-sdk//:fastdds",
        "@voy-sdk//:fmt",
        "@voy-sdk//:foonathan_memory",
        "@voy-sdk//:gflags",
        "@voy-sdk//:glog",
        "@voy-sdk//:tinyxml2",
        "@voy-sdk//:yaml-cpp",
    ] + select({
        "//bazel/platforms:is_rigel": [],
        "//conditions:default": [
            "//onboard/common/node:node_lib",
            "//onboard/common/node:voy_health_reporter",
        ],
    }),
)

install(
    name = "install_varch_config",
    srcs = ["onboard/varch/config/"],
    dest = "etc/varch",
)

install(
    name = "install_rospkg",
    srcs = [":package.xml"],
    dest = "share/varch",
)
