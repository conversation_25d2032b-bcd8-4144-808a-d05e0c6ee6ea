#include "at128p_packet_checker.h"

#include <iostream>
#include <string>
#include <tuple>
#include <utility>

#include <glog/logging.h>

#include "base/now.h"
#include "driver/lidar_event.h"
namespace lidar {
namespace {

// The maximum latency of the packet in microseconds.
// Normal value less than 2ms
constexpr int kLatencyMaxUsWarning = 1 * 50 * 1000;
constexpr int kLatencyMaxUsError = 1 * 210 * 1000;

// The maximum difference of the sequence number.
// Normal valude is 1
constexpr int kDiffMaxSequenceNumWarning = 16;
constexpr int kDiffMaxSequenceNumError = 120;

// The maximum difference of the timestamp in microseconds.
// This is related to the lost packet sequence number.
// Normal valude is 42 us
constexpr int kDiffNormalTimestamp = 42;
constexpr int kDiffMaxTimestampWarning =
    (kDiffMaxSequenceNumWarning + 1) * kDiffNormalTimestamp;
constexpr int kDiffMinTimestampWarning = 15;

constexpr int kDiffMaxTimestampError =
    (kDiffMaxSequenceNumError + 1) * kDiffNormalTimestamp;
constexpr int kDiffMinTimestampError = 10;

// The maximum difference of the angle.
// This is related to the lost packet sequence number.
// The normal value is approximately 2560
constexpr int kDiffNormalAngle = 2560;
constexpr int kDiffMaxAngleWarning =
    (kDiffMaxSequenceNumWarning + 1) * kDiffNormalAngle;
constexpr int kDiffMinAngleWarning = 1000;

constexpr int kDiffMaxAngleError =
    (kDiffMaxSequenceNumError + 1) * kDiffNormalAngle;
constexpr int kDiffMinAngleError = 500;

// The maximum difference of the motor speed.
// Normal valude is 2000
constexpr int kDiffMotorWarning = 50;
constexpr int kDiffMotorError = 200;

// The count of the packet in a period of 100ms.
constexpr int kMinNumberOfReceivedPer100MillsecondsWarning = 1050;
constexpr int kMinNumberOfReceivedPer100MillsecondsError = 900;
constexpr int kMaxNumberOfReceivedPer100MillsecondsWaring = 1350;
constexpr int kMaxNumberOfReceivedPer100MillsecondsError = 1500;

/// Other constants
constexpr int kCountTimeIntervalMillseconds = 99;
constexpr int kMaxNumberLogPerSecond = 10;

template <typename CheckFunc>
void CheckAndAccumulate(CheckFunc check_func, LidarEvent::EventCode &event_code,
                        std::string &error_item) {
  auto sub_result = check_func();
  if (sub_result.first != LidarEvent::EventCode::kNormal) {
    event_code |= sub_result.first;
    error_item += sub_result.second + " ";
  }
}

int GetMaxAngleDiffThreshold(int sequence_diff, bool is_warning) {
  if (sequence_diff < kDiffMaxSequenceNumWarning && is_warning) {
    return kDiffMaxAngleWarning;
  }
  if (sequence_diff < kDiffMaxSequenceNumError && !is_warning) {
    return kDiffMaxAngleError;
  }
  return (sequence_diff + 1) * kDiffNormalAngle;
}

int GetMaxTimestampDiffThreshold(int sequence_diff, bool is_warning) {
  if (sequence_diff < kDiffMaxSequenceNumWarning && is_warning) {
    return kDiffMaxTimestampWarning;
  }
  if (sequence_diff < kDiffMaxSequenceNumError && !is_warning) {
    return kDiffMaxTimestampError;
  }
  return (sequence_diff + 1) * kDiffNormalTimestamp;
}

}  // namespace

AT128PPacketChecker::AT128PPacketChecker(const std::string &ip_address)
    : ip_address_(ip_address) {
  static_cast<void>(kMinNumberOfReceivedPer100MillsecondsError);
  static_cast<void>(kMaxNumberOfReceivedPer100MillsecondsError);
}

void AT128PPacketChecker::CountPacketReceived() {
  int64_t now = base::Now();
  if (last_received_packet_timestamp_us_ == 0) {
    last_received_packet_timestamp_us_ = now;
  }
  if (last_received_packet_timestamp_us_ != 0 &&
      now - last_received_packet_timestamp_us_ >
          kCountTimeIntervalMillseconds) {
    float packet_number =
        received_packet_number_ - last_received_packet_number_;
    if (packet_number > kMaxNumberOfReceivedPer100MillsecondsWaring ||
        packet_number < kMinNumberOfReceivedPer100MillsecondsWarning) {
      LOG(WARNING)
          << "Received "
          << received_packet_number_ - last_received_packet_number_
          << " packets of data within "
          << now - last_received_packet_timestamp_us_ << " now: " << now
          << " last_now: " << last_received_packet_timestamp_us_
          << ", Expected to receive 1250 packets within 100ms. ip_address: "
          << ip_address_;
    }

    last_received_packet_number_ = received_packet_number_;
    last_received_packet_timestamp_us_ = now;
  }
  received_packet_number_++;
}

std::tuple<LidarEvent::EventCode, std::string> AT128PPacketChecker::Check(
    int64_t timestamp_us, uint32_t sequence_number, int angle,
    int16_t motor_speed, bool shutdown_flag, bool is_new_scan) {
  LidarEvent::EventCode event_code = LidarEvent::EventCode::kNormal;
  std::string error_message;
  std::string error_item;

  int64_t cur_time_us = base::WallClockNowUs();

  CheckAndAccumulate([&]() { return CheckLatency(cur_time_us, timestamp_us); },
                     event_code, error_item);
  CheckAndAccumulate(
      [&]() {
        return CheckTimestamp(is_new_scan, timestamp_us,
                              sequence_number - last_sequence_number_);
      },
      event_code, error_item);
  CheckAndAccumulate(
      [&]() {
        return CheckAngle(is_new_scan, angle,
                          sequence_number - last_sequence_number_);
      },
      event_code, error_item);
  CheckAndAccumulate([&]() { return CheckSequenceNumber(sequence_number); },
                     event_code, error_item);
  CheckAndAccumulate([&]() { return CheckMotorSpeed(motor_speed); }, event_code,
                     error_item);
  CheckAndAccumulate([&]() { return CheckShutdownFlag(shutdown_flag); },
                     event_code, error_item);

  if (event_code != LidarEvent::EventCode::kNormal) {
    if ((cur_time_us / 1000) - last_log_timestamp_ms_ > 1000) {
      LOG(INFO) << " The number of logs in this second is "
                << log_count_this_second_ << ". limiting the output to "
                << kMaxNumberLogPerSecond << " at most. " << ip_address_;
      last_log_timestamp_ms_ = cur_time_us / 1000;
      log_count_this_second_ = 0;
    }
    if (log_count_this_second_ <= kMaxNumberLogPerSecond) {
      std::stringstream stream;
      stream << " ip_src: " << ip_address_ << " [" << error_item << "] "
             << " latency: " << cur_time_us - timestamp_us << "(us)"
             << " cur_time_us: " << cur_time_us
             << " timestamp: " << timestamp_us
             << " last_timestamp: " << last_timestamp_us_ << " ("
             << timestamp_us - last_timestamp_us_ << "us) "
             << " seq :" << sequence_number
             << " last_seq: " << last_sequence_number_ << "("
             << sequence_number - last_sequence_number_ << ")"
             << " angle: " << angle << " last_angle_: " << last_angle_ << "("
             << angle - last_angle_ << ")"
             << " motor:" << motor_speed << " last_motor: " << last_motor_speed_
             << "(" << motor_speed - last_motor_speed_ << ")"
             << " shutdown flag: " << static_cast<int>(shutdown_flag)
             << " last_shutdown_flag_: " << last_shutdown_flag_;

      error_message = stream.str();
      LOG(ERROR) << error_message;
    } else {
      // We will temporarily limit the number of errors reported, this is a
      // temporary strategy
      event_code = LidarEvent::EventCode::kNormal;
    }
    log_count_this_second_++;
  }

  last_timestamp_us_ = timestamp_us;
  last_sequence_number_ = sequence_number;
  last_angle_ = angle;
  last_motor_speed_ = motor_speed;
  last_shutdown_flag_ = shutdown_flag;

  return std::make_tuple(event_code, error_message);
}

std::pair<LidarEvent::EventCode, std::string> AT128PPacketChecker::CheckLatency(
    int64_t cur_time_us, int64_t timestamp_us) const {
  if (std::abs(cur_time_us - timestamp_us) > kLatencyMaxUsError) {
    return std::make_pair(LidarEvent::EventCode::kPacketLatencyError,
                          "Latency Error");
  }
  if (std::abs(cur_time_us - timestamp_us) > kLatencyMaxUsWarning) {
    return std::make_pair(LidarEvent::EventCode::kPacketLatencyWarning,
                          "Latency Warning");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

std::pair<LidarEvent::EventCode, std::string>
AT128PPacketChecker::CheckTimestamp(bool is_new_scan, int64_t timestamp_us,
                                    int sequence_diff) const {
  if (is_new_scan) {
    return std::make_pair(LidarEvent::EventCode::kNormal, "");
  }
  if (last_timestamp_us_ != 0 &&
      (std::abs(timestamp_us - last_timestamp_us_) >
           GetMaxTimestampDiffThreshold(sequence_diff, false) ||
       std::abs(timestamp_us - last_timestamp_us_) < kDiffMinTimestampError)) {
    return std::make_pair(LidarEvent::EventCode::kPacketTimestampError,
                          "Timestamp Error");
  }
  if (last_timestamp_us_ != 0 &&
      (std::abs(timestamp_us - last_timestamp_us_) >
           GetMaxTimestampDiffThreshold(sequence_diff, true) ||
       std::abs(timestamp_us - last_timestamp_us_) <
           kDiffMinTimestampWarning)) {
    return std::make_pair(LidarEvent::EventCode::kPacketTimestampWarning,
                          "Timestamp Warning");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

std::pair<LidarEvent::EventCode, std::string> AT128PPacketChecker::CheckAngle(
    bool is_new_angle, int angle, int sequence_diff) const {
  if (is_new_angle) {
    return std::make_pair(LidarEvent::EventCode::kNormal, "");
  }

  if (last_angle_ != 0 &&
      (std::abs(angle - last_angle_) >
           GetMaxAngleDiffThreshold(sequence_diff, false) ||
       std::abs(angle - last_angle_) < kDiffMinAngleError)) {
    return std::make_pair(LidarEvent::EventCode::kPacketAngleError,
                          "Angle Error");
  }

  if (last_angle_ != 0 &&
      (std::abs(angle - last_angle_) >
           GetMaxAngleDiffThreshold(sequence_diff, true) ||
       std::abs(angle - last_angle_) < kDiffMinAngleWarning)) {
    return std::make_pair(LidarEvent::EventCode::kPacketAngleWarning,
                          "Angle Warning");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

std::pair<LidarEvent::EventCode, std::string>
AT128PPacketChecker::CheckSequenceNumber(uint32_t sequence_number) const {
  if (last_sequence_number_ != 0 &&
      std::abs(static_cast<int>(sequence_number - last_sequence_number_)) >
          kDiffMaxSequenceNumError) {
    return std::make_pair(LidarEvent::EventCode::kPacketSequenceError,
                          "Sequence Error");
  }

  if (last_sequence_number_ != 0 &&
      std::abs(static_cast<int>(sequence_number - last_sequence_number_)) >
          kDiffMaxSequenceNumWarning) {
    return std::make_pair(LidarEvent::EventCode::kPacketSequenceWarning,
                          "Sequence Warning");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

std::pair<LidarEvent::EventCode, std::string>
AT128PPacketChecker::CheckMotorSpeed(int16_t motor_speed) const {
  if (last_motor_speed_ != 0 &&
      (std::abs(motor_speed - last_motor_speed_) > kDiffMotorError)) {
    return std::make_pair(LidarEvent::EventCode::kMotorSpeedError,
                          "Motor Error");
  }
  if (last_motor_speed_ != 0 &&
      (std::abs(motor_speed - last_motor_speed_) > kDiffMotorWarning)) {
    return std::make_pair(LidarEvent::EventCode::kMotorSpeedWarning,
                          "Motor Warning");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

std::pair<LidarEvent::EventCode, std::string>
AT128PPacketChecker::CheckShutdownFlag(bool shutdown_flag) const {
  if (shutdown_flag) {
    return std::make_pair(LidarEvent::EventCode::kShutdownFlagError,
                          "Shutdown Flag");
  }
  return std::make_pair(LidarEvent::EventCode::kNormal, "");
}

}  // namespace lidar
