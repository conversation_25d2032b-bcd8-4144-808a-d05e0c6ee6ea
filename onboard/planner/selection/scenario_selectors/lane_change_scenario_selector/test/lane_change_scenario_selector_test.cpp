#include "onboard/planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_scenario_selector.h"

#include <gtest/gtest.h>
#include <memory>
#include <optional>
#include <unordered_map>

#include "glog/logging.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/selection/selection.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "vehicle_model/motion_model.h"

namespace planner {
namespace selection {

class LaneChangeScenarioSelectorTest : public testing::Test,
                                       public speed::ReasoningTestFixture,
                                       public PlanningSeedAccess {
 protected:
  LaneChangeScenarioSelectorTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }

  const planner::pb::DecoupledManeuverSeed& seed() const {
    return *Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
        SpeedCurrentFrame());
  }

  planner::pb::DecoupledManeuverSeed* mutable_seed() {
    return Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(
               SpeedCurrentFrame())
        .get();
  }

  std::shared_ptr<const speed::TrajectoryInfo> ConstructTrajectoryInfo() {
    return std::make_shared<const speed::TrajectoryInfo>(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(), world_model().robot_state(),
        is_pull_out_jump_out_, is_for_ra_unstuck_request_,
        is_lane_sequence_feasible_, lane_sequence_cost_factors_,
        is_waypoint_assist_invoked_, is_waypoint_backup_trajectory_used_,
        is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY,
        planner::pb::PathReasoningSeed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }

  LaneSequenceResult ConstructLaneSequenceResult(
      const int64_t source_lane_id, const int64_t target_lane_id,
      bool is_lane_keep = false, double cost_to_route_destination = 0.0) {
    LaneSequenceResult lane_sequence_result =
        is_lane_keep
            ? test_utility::ConstructLaneSequenceResult(
                  /*lane_ids=*/{source_lane_id},
                  /*lane_change_modes=*/{},
                  /*lane_change_instances=*/{},
                  /*current_lane_id=*/source_lane_id, map_test_util())
            : test_utility::ConstructLaneSequenceResult(
                  /*lane_ids=*/{source_lane_id, target_lane_id},
                  /*lane_change_modes=*/
                  {planner::pb::LaneChangeMode::LEFT_LANE_CHANGE},
                  /*lane_change_instances=*/{{source_lane_id, target_lane_id}},
                  /*current_lane_id=*/source_lane_id, map_test_util());
    lane_sequence_result.cost_to_route_destination = cost_to_route_destination;
    return lane_sequence_result;
  }

  PathGenerationOption GeneratePathOption(
      const LaneSequenceResult& lane_sequence_result,
      bool is_lane_keep = false) {
    lane_selection::DecoupledLaneSequenceInfo lane_seq_info(
        is_lane_keep ? planner::pb::ManeuverType::LANE_FOLLOW
                     : planner::pb::ManeuverType::LANE_CHANGE_LEFT,
        lane_sequence_result, previous_iter_seed_);
    BehaviorRelevantObjectsInfo behavior_relevant_objects_info;
    lane_seq_info.Update(world_model(), behavior_relevant_objects_info);
    PathGenerationOption path_option;
    path_option.geometric_constraint.lane_sequence_info = &lane_seq_info;
    path_option.trajectory_type =
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY;
    path_option.geometric_constraint.behavior_type =
        is_lane_keep ? planner::pb::BehaviorType::LANE_KEEP
                     : planner::pb::BehaviorType::CROSS_LANE;
    path_option.geometric_constraint.lane_sequence_result =
        &lane_sequence_result;
    path_option.path_reasoning_result.intention_result.set_homotopy(
        planner::pb::IntentionResult::IGNORE_ALL);

    return path_option;
  }

  void ConstructCandidate(const PathGenerationOption& path_option) {
    std::optional<TrajectoryMetaData>& optional_candidate =
        (path_option.geometric_constraint.behavior_type ==
         planner::pb::BehaviorType::LANE_KEEP)
            ? lk_candidate_
            : cl_candidate_;
    optional_candidate.emplace(
        path_option,
        /*speed_ix_in=*/0, selection::EgoIntentSignals(),
        /*trajectory_info=*/ConstructTrajectoryInfo(),
        std::make_shared<planner::pb::Path>(planner::pb::Path()),
        /*extended_path_in=*/
        std::make_shared<math::geometry::PolylineCurve2d>(
            math::geometry::PolylineCurve2d()),
        /*proximity_infos=*/
        std::make_shared<std::map<ObjectId, speed::pb::ObjectProximityInfo> >(
            std::map<ObjectId, speed::pb::ObjectProximityInfo>()),
        /*overlap gap=*/
        std::make_shared<
            std::map<ObjectId, std::vector<speed::pb::OverlapRegion> > >(
            std::map<ObjectId, std::vector<speed::pb::OverlapRegion> >()),
        planner::pb::Trajectory(),
        /*speed_result=*/speed::SpeedResult(),
        std::make_shared<planner::pb::SingleHomotopyPathSeed>(
            planner::pb::SingleHomotopyPathSeed()),
        std::make_shared<speed::pb::SpeedSeed>(speed::pb::SpeedSeed()),
        std::make_shared<std::vector<planner::pb::AssistRequest> >(
            std::vector<planner::pb::AssistRequest>()),
        /*speed_discomfort_in=*/0.0,
        /*if_use_trajectory_guider_output_in_path_in=*/false,
        world_model().robot_state().car_model_with_shape().shape_measurement(),
        /*intention_plan_debug_in=*/nullptr);
  }

  void AddTrajectoryCandidate(TrajectoryMetaData& candidate) {
    trajectory_candidates_.push_back(&candidate);
  }

  void CreateLaneChangeScenarioSelector() {
    lc_scenario_selector_.emplace(PlannerConfigCenter::GetInstance()
                                      .GetDecoupledForwardManeuverConfig()
                                      .trajectory_selection_config(),
                                  speed_world_model(), seed(), *mutable_seed());
  }

  const TrajectoryMetaData& cl_candidate() const {
    DCHECK(cl_candidate_.has_value());
    return cl_candidate_.value();
  }

  TrajectoryMetaData& mutable_cl_candidate() {
    DCHECK(cl_candidate_.has_value());
    return cl_candidate_.value();
  }

  const TrajectoryMetaData& lk_candidate() const {
    DCHECK(lk_candidate_.has_value());
    return lk_candidate_.value();
  }

  TrajectoryMetaData& mutable_lk_candidate() {
    DCHECK(lk_candidate_.has_value());
    return lk_candidate_.value();
  }

  LaneChangeScenarioSelector& lc_scenario_selector() {
    DCHECK(lc_scenario_selector_.has_value());
    return lc_scenario_selector_.value();
  }

  std::optional<TrajectoryMetaData> cl_candidate_;
  std::optional<TrajectoryMetaData> lk_candidate_;
  std::vector<TrajectoryMetaData*> trajectory_candidates_;
  std::optional<LaneChangeScenarioSelector> lc_scenario_selector_;
};

TEST_F(LaneChangeScenarioSelectorTest, AdjustRoutingCostByRiskTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/false);
  UpdateForLaneChange();

  // Construct CL candidiate.
  LaneSequenceResult cl_lane_sequence_result = ConstructLaneSequenceResult(
      kSourceLaneId, kTargetLaneId, /*is_lane_keep=*/false,
      /*cost_to_route_destination=*/50.0);
  PathGenerationOption cl_path_option =
      GeneratePathOption(cl_lane_sequence_result, /*is_lane_keep=*/false);
  ConstructCandidate(cl_path_option);
  EXPECT_TRUE(cl_candidate().IsCrossLane());
  AddTrajectoryCandidate(mutable_cl_candidate());

  // Construct LK candidiate.
  LaneSequenceResult lk_lane_sequence_result = ConstructLaneSequenceResult(
      kSourceLaneId, kTargetLaneId, /*is_lane_keep=*/true,
      /*cost_to_route_destination=*/100.0);
  PathGenerationOption lk_path_option =
      GeneratePathOption(lk_lane_sequence_result, /*is_lane_keep=*/true);
  ConstructCandidate(lk_path_option);
  EXPECT_FALSE(lk_candidate().IsCrossLane());
  AddTrajectoryCandidate(mutable_lk_candidate());

  EXPECT_EQ(2, trajectory_candidates_.size());

  ExtractToSpeedWorldModel();

  // Compute basic routing costs.
  CreateLaneChangeScenarioSelector();

  lc_scenario_selector().ComputeAndPopulateRoutingCost(trajectory_candidates_);

  // Populate costs map.
  std::unordered_map<pb::CostType, CostComputerMetadata> costs_map;
  for (const TrajectoryMetaData* candidate : trajectory_candidates_) {
    if (candidate->IsCrossLane()) {
      EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 50.0);
      EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 0.0);
    } else {
      EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 100.0);
      EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 1.0);
    }

    costs_map[pb::CostType::ROUTING].trajectory_costs.push_back(
        candidate->normalized_routing_cost);
    costs_map[pb::CostType::COLLISION_RISK].trajectory_costs.push_back(
        candidate->collision_risk);
  }

  // Adjust collision risk.
  for (TrajectoryMetaData* candidate : trajectory_candidates_) {
    if (candidate->IsCrossLane()) {
      candidate->pose_collision_risk = 0.6;
    } else {
      candidate->pose_collision_risk = 0.1;
    }
  }

  // Test case where lane change is in progress.
  {
    mutable_seed()->set_selected_behavior_type(
        planner::pb::BehaviorType::CROSS_LANE);
    lc_scenario_selector().AdjustRoutingCostByRisk(trajectory_candidates_,
                                                   &costs_map);
    for (const TrajectoryMetaData* candidate : trajectory_candidates_) {
      if (candidate->IsCrossLane()) {
        EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 50.0);
        EXPECT_DOUBLE_EQ(candidate->pose_collision_risk, 0.6);
        EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 0.0);
      } else {
        EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 100.0);
        EXPECT_DOUBLE_EQ(candidate->pose_collision_risk, 0.1);
        EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 1.0);
      }
    }
  }

  // Test case where lane change is not in progress.
  {
    mutable_seed()->set_selected_behavior_type(
        planner::pb::BehaviorType::LANE_KEEP);
    lc_scenario_selector().set_is_blockage_lane_change(true);
    lc_scenario_selector().AdjustRoutingCostByRisk(trajectory_candidates_,
                                                   &costs_map);
    for (const TrajectoryMetaData* candidate : trajectory_candidates_) {
      if (candidate->IsCrossLane()) {
        EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 50.0);
        EXPECT_DOUBLE_EQ(candidate->pose_collision_risk, 0.6);
        EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 2.0);
      } else {
        EXPECT_DOUBLE_EQ(candidate->raw_routing_cost(), 100.0);
        EXPECT_DOUBLE_EQ(candidate->pose_collision_risk, 0.1);
        EXPECT_DOUBLE_EQ(candidate->normalized_routing_cost, 1.0);
      }
    }
  }
}

TEST(LaneChangeSelectionTest, LaneChangeCollisionRiskWindowTest) {
  static constexpr size_t kWindowCapacityForTest = 5;
  LaneChangeCollisionRiskWindow window(kWindowCapacityForTest);

  {
    window.Clear();
    while (!window.IsFull()) {
      window.Add(10.0);
    }
    DCHECK_EQ(window.values_.size(), kWindowCapacityForTest);
  }

  {
    window.Clear();
    while (!window.IsFull()) {
      window.Add(10.0);
    }
    DCHECK_EQ(true,
              window.AllSatisfy([](const double val) { return val < 15.0; }));
    window.Add(20.0);
    DCHECK_EQ(window.values_.size(), kWindowCapacityForTest);
    DCHECK_EQ(false,
              window.AllSatisfy([](const double val) { return val < 15.0; }));
  }
}

}  // namespace selection
}  // namespace planner
