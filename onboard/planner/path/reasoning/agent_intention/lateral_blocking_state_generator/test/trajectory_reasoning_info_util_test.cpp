#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/trajectory_reasoning_info_util.h"

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>
#include <tbb/concurrent_unordered_map.h>

#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state_generator.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state_generator.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/reasoner/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/test_utils/path_reasoning_test_utils.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner_protos/agent_intention.pb.h"

namespace planner {

namespace {

class TrajectoryReasoningInfoUtilTest : public ::testing::Test {
 protected:
  TrajectoryReasoningInfoUtilTest() : planner_objects_history_(20) {}

  void SetUp() override {
    // Set the agent.
    AgentInLaneMetadata meta_data;
    meta_data.features[pb::AgentFeature::IS_STATIONARY] = false;
    meta_data.agent_type = voy::perception::ObjectType::VEHICLE;

    AgentSnapshotBlockingInfo blocking_info;
    blocking_info.is_fully_behind_ego = false;

    const std::vector<math::geometry::Point2d> points = {
        {7.5, 0.5}, {12.5, 0.5}, {12.5, 1}, {7.5, 1}, {7.5, 0.5}};
    const math::geometry::PolygonWithCache2d polygon_bbox(std::move(points),
                                                          /*correct=*/true);
    const math::geometry::Point3d center(10.0, 0.75, 0);
    AgentSnapshotPose pose(center, /*length=*/5.0, /*width=*/0.5,
                           /*height=*/0.0, /*heading=*/0.0, polygon_bbox,
                           /*should_use_contour=*/false);
    // Agent is ahead of the ego.
    AgentSnapshotInLaneParam in_lane_param;
    in_lane_param.pose = pose;
    in_lane_param.cross_track_encroachment_m = 0.0;
    in_lane_param.start_arclength_m = 7.5;
    in_lane_param.end_arclength_m = 12.5;

    agent_sl_boundary_ = std::make_optional<AgentSLBoundary>(in_lane_param);

    AgentSnapshotInLaneState tracked_state;
    tracked_state.inlane_param = in_lane_param;
    tracked_state.blocking_info = blocking_info;

    AgentTrajectoryInLaneStates trajectory_states;
    trajectory_states.trajectory_id = 0;

    agent_.agent_metadata = meta_data;
    agent_.tracked_state = tracked_state;
    agent_.predicted_trajectories.insert(
        {trajectory_states.trajectory_id, trajectory_states});
    agent_.primary_trajectory_id = trajectory_states.trajectory_id;

    ego_param_.arclength_m = 10.0;
    ego_param_.rear_axle_to_rear_bumper_m = 1.0;
    ego_param_.rear_axle_to_front_bumper_m = 2.0;

    const int num_step = 80;
    const speed::Limits limits(
        speed::LimitRange(/*brake_a.min=*/-2.0, /*brake_a.max=*/2.0),
        speed::LimitRange(/*accel_a.min=*/-2.0, /*accel_a.max=*/2.0),
        speed::LimitRange(/*brake_j.min=*/-3.0, /*brake_j.max=*/2.5),
        speed::LimitRange(/*accel_j.min=*/-2.0, /*accel_j.max=*/2.0),
        /*max_v=*/33.33, /*max_brake2accel_j=*/2.5);
    // Initialize lower and upper bound speed.
    lower_bound_speed_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, num_step, /*dt=*/0.1);
    upper_bound_speed_ = speed::CreateConstantAccelProfileFromTime(
        limits, /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*a0=*/1.0, num_step,
        /*dt=*/0.1);
    ego_param_.along_track_speed_mps = lower_bound_speed_.front().v;

    speed::GenerateBrakeProfile(/*brake_first_ix=*/0, num_step, limits,
                                /*dt=*/0.1, &lower_bound_speed_);
    emergency_brake_speed_ = lower_bound_speed_;

    google::protobuf::TextFormat::ParseFromString(
        R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 0.0
        acceleration: 0.0
    )",
        agent_proto_.mutable_tracked_object());
    const int64_t timestamp = 2100;
    const TrafficParticipantPose pose_at_plan_init_ts(
        timestamp, agent_proto_.tracked_object());
    planner_object_ = std::make_unique<PlannerObject>(agent_proto_, timestamp,
                                                      pose_at_plan_init_ts);

    ego_bounding_box_ = math::geometry::OrientedBox2d(
        /*x=*/10.0, /*y=*/0.0,
        /*length=*/ego_param_.rear_axle_to_rear_bumper_m +
            ego_param_.rear_axle_to_front_bumper_m,
        /*width=*/0.5, /*heading=*/0.0);
    reference_line_ = math::geometry::PolylineCurve2d(
        {{0.0, 0.0}, {5.0, 0.0}, {10.0, 0.0}, {20.0, 0.0}, {100.0, 0.0}});

    planning_horizon_range_ = math::Range1d(0, 100);

    auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
    for (size_t i = 1; i <= 20; ++i) {
      auto planner_object_map_ptr =
          std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
      std::unique_ptr<PlannerObject> past_planner_object =
          std::make_unique<PlannerObject>(agent_proto_, 100 * i,
                                          pose_at_plan_init_ts);
      planner_object_map_ptr->emplace(planner_object_->id(),
                                      *past_planner_object);
      planner_objects_history_.Update(planner_object_map_ptr, destruction_pool);
    }
    // Set up physical lateral clearance data.
    lateral_clearance::Clearance left_clearance;
    left_clearance.distance = 3.0;
    lateral_clearance::Clearance right_clearance;
    right_clearance.distance = -2.0;
    lateral_clearance::LateralClearance lateral_clearance{left_clearance,
                                                          right_clearance};

    const math::Range1d effective_reference_range(0.0, 20.0);
    physical_boundary_lateral_clearance_data_ =
        lateral_clearance::LateralClearanceData(
            /*resolution=*/1.0, /*max_abs_lateral_clearance=*/10.0,
            effective_reference_range,
            std::vector<lateral_clearance::LateralClearance>(
                21, lateral_clearance));
  }

  AgentInLaneStates agent_;
  prediction::pb::Agent agent_proto_;
  std::unique_ptr<PlannerObject> planner_object_;
  PlannerObjectsHistory planner_objects_history_;
  std::vector<PredictedTrajectoryWrapper> predictions_;
  tbb::concurrent_unordered_map<int64_t,
                                std::vector<PredictedTrajectoryWrapper>>
      object_predictions_;
  math::geometry::OrientedBox2d ego_bounding_box_;
  RequiredLateralGap required_lat_gap_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  std::optional<AgentSLBoundary> agent_sl_boundary_;
  math::Range1d planning_horizon_range_;
  EgoInLaneParams ego_param_;
  speed::Profile emergency_brake_speed_;
  speed::Profile lower_bound_speed_;
  speed::Profile upper_bound_speed_;
  math::geometry::PolylineCurve2d reference_line_;
  int agent_snapshot_size_ = 20;
  planner::lateral_clearance::LateralClearanceData
      physical_boundary_lateral_clearance_data_;
};

}  // namespace

TEST_F(TrajectoryReasoningInfoUtilTest, SnapshotAnnotationTest) {
  AgentSnapshotInLaneParam agent_inlane_param;
  agent_inlane_param.cross_track_encroachment_m = 0.1;
  agent_inlane_param.start_arclength_m = 7.5;
  agent_inlane_param.end_arclength_m = 12.5;
  agent_inlane_param.along_track_speed_mps = 3.0;
  agent_inlane_param.cross_track_speed_mps = 6.0;
  agent_inlane_param.relative_heading_rad = 1.5;
  agent_inlane_param.left_inlane_clearance_m = 1.2;
  agent_inlane_param.right_inlane_clearance_m = 0.0;
  // The snapshot is encroachment ego lane.
  EXPECT_TRUE(IsInEgoLaneSnapshot(
      required_lat_gap_, agent_inlane_param.cross_track_encroachment_m));
  // The snapshot is crossing not oncoming.
  EXPECT_FALSE(IsOncomingSnapshot(agent_inlane_param.relative_heading_rad));
  // The snapshot is crossing not same direction.
  EXPECT_FALSE(
      IsSameDirectionSnapshot(agent_inlane_param.relative_heading_rad));
  // The snapshot is a normal agent, and has valid arc length range.
  EXPECT_FALSE(
      IsAbnormalInlaneParamSnapshot(agent_inlane_param.start_arclength_m,
                                    agent_inlane_param.end_arclength_m));
  // The snapshot is crossing.
  EXPECT_TRUE(IsCrossingSnapshot(agent_inlane_param));
  // The snapshot is hard blocking.
  EXPECT_TRUE(IsHardBlockingSnapshot(path::test::GetEgoInLaneParams(),
                                     agent_inlane_param, required_lat_gap_));
}

TEST_F(TrajectoryReasoningInfoUtilTest, SnapshotAnnotationTestForOOS) {
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.cross_track_encroachment_m = 0.1;
  object_occupancy_param.snapshot_start_arclength_m = 7.5;
  object_occupancy_param.snapshot_end_arclength_m = 12.5;
  object_occupancy_param.along_track_speed_mps = 3.0;
  object_occupancy_param.cross_track_speed_mps = 6.0;
  object_occupancy_param.relative_heading_rad = 1.5;
  object_occupancy_param.left_boundary_clearance_m = 1.2;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  // The snapshot is encroachment ego lane.
  EXPECT_TRUE(IsInEgoLaneSnapshot(
      required_lat_gap_, object_occupancy_param.cross_track_encroachment_m));
  // The snapshot is crossing not oncoming.
  EXPECT_FALSE(IsOncomingSnapshot(object_occupancy_param.relative_heading_rad));
  // The snapshot is crossing not same direction.
  EXPECT_FALSE(
      IsSameDirectionSnapshot(object_occupancy_param.relative_heading_rad));
  // The snapshot is a normal agent, and has valid arc length range.
  EXPECT_FALSE(IsAbnormalInlaneParamSnapshot(
      object_occupancy_param.snapshot_start_arclength_m,
      object_occupancy_param.snapshot_end_arclength_m));
  // The snapshot is crossing. The function does not have users. So comment it
  // out. EXPECT_TRUE(IsCrossingSnapshot(agent_inlane_param)); The snapshot is
  // hard blocking.
  EXPECT_TRUE(IsHardBlockingSnapshot(path::test::GetEgoInLaneParams(),
                                     object_occupancy_param,
                                     required_lat_gap_));
}

TEST_F(TrajectoryReasoningInfoUtilTest, DynamicBehindInPullOverTest) {
  agent_.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = false;
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 0.5
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);
  agent_.tracked_state.blocking_info.is_fully_behind_ego = true;
  agent_.tracked_state.inlane_param.start_arclength_m = -2;
  agent_.tracked_state.inlane_param.end_arclength_m = 0;
  agent_.tracked_state.inlane_param.full_body_start_arclength_m = -2;
  agent_.tracked_state.inlane_param.full_body_end_arclength_m = 0;

  LateralBlockingStateMetaData nonblocking_object;
  nonblocking_object.global_blocking_state = pb::TrajectoryState::NON_BLOCKING;
  nonblocking_object.trajectory_id = agent_.primary_trajectory_id;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::NON_BLOCKING);
  nonblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);

  // Scenario_1: The agent located on right side of ego lane, lateral and
  // logitudinal distance between agent and ego lane is near.
  agent_.tracked_state.inlane_param.center_line_side = math::pb::kRight;
  agent_.tracked_state.inlane_param.center_line_clearance_m = 1.0;
  agent_.tracked_state.inlane_param.along_track_speed_mps = 8.0;
  auto right_near_result = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::DECOUPLED_PULL_OVER);
  EXPECT_EQ(right_near_result.is_far_away_behind(), false);

  // Scenario_2: The agent located on right side of ego lane, the lateral
  // distance between agent and ego lane is farway.
  agent_.tracked_state.inlane_param.center_line_side = math::pb::kRight;
  agent_.tracked_state.inlane_param.center_line_clearance_m = 6.0;
  auto right_farway_result = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::DECOUPLED_PULL_OVER);

  // Scenario_3: The agent located on left side of ego lane, lateral distance
  // between agent and ego lane is near.
  agent_.tracked_state.inlane_param.center_line_side = math::pb::kLeft;
  agent_.tracked_state.inlane_param.center_line_clearance_m = 1.0;
  auto left_near_result = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::DECOUPLED_PULL_OVER);
  EXPECT_EQ(left_near_result.is_far_away_behind(), true);

  // Scenario_4: The the agent located on right side of ego lane, lateral
  // distance between agent and ego lane is near, but the logitudinal
  // distance between agent and ego is farway.
  agent_.tracked_state.inlane_param.center_line_side = math::pb::kRight;
  agent_.tracked_state.inlane_param.center_line_clearance_m = 1.0;
  agent_.tracked_state.inlane_param.start_arclength_m = -32;
  agent_.tracked_state.inlane_param.end_arclength_m = -30;
  agent_.tracked_state.inlane_param.full_body_start_arclength_m = -32;
  agent_.tracked_state.inlane_param.full_body_end_arclength_m = -30;
  auto right_near_farway_result = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::DECOUPLED_PULL_OVER);
  EXPECT_EQ(right_near_farway_result.is_far_away_behind(), true);
}

TEST_F(TrajectoryReasoningInfoUtilTest, DynamicBehindInPullOverTestForOOS) {
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = -2;
  object_occupancy_param.snapshot_end_arclength_m = 0;
  object_occupancy_param.full_body_start_arclength_m = -2;
  object_occupancy_param.full_body_end_arclength_m = 0;

  // Scenario_1: The agent located on right side of ego lane, lateral and
  // logitudinal distance between agent and ego lane is near.
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.center_line_clearance_m = 1.0;
  object_occupancy_param.along_track_speed_mps = 8.0;
  EXPECT_FALSE(IsFarAwayBehindAgent(
      ego_param_, object_occupancy_param, /*is_fully_behind_ego=*/true,
      /*is_stationary=*/false, /*behavior_type=*/pb::DECOUPLED_PULL_OVER,
      /*pullover_jump_in_type=*/
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN));

  // Scenario_2: The agent located on right side of ego lane, the lateral
  // distance between agent and ego lane is farway.
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.center_line_clearance_m = 6.0;
  EXPECT_TRUE(IsFarAwayBehindAgent(
      ego_param_, object_occupancy_param, /*is_fully_behind_ego=*/true,
      /*is_stationary=*/false, /*behavior_type=*/pb::DECOUPLED_PULL_OVER,
      /*pullover_jump_in_type=*/
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN));

  // Scenario_3: The agent located on left side of ego lane, lateral distance
  // between agent and ego lane is near.
  object_occupancy_param.center_line_side = math::pb::kLeft;
  object_occupancy_param.center_line_clearance_m = 1.0;
  EXPECT_TRUE(IsFarAwayBehindAgent(
      ego_param_, object_occupancy_param, /*is_fully_behind_ego=*/true,
      /*is_stationary=*/false, /*behavior_type=*/pb::DECOUPLED_PULL_OVER,
      /*pullover_jump_in_type=*/
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN));

  // Scenario_4: The the agent located on right side of ego lane, lateral
  // distance between agent and ego lane is near, but the logitudinal
  // distance between agent and ego is farway.
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.center_line_clearance_m = 1.0;
  object_occupancy_param.snapshot_start_arclength_m = -32;
  object_occupancy_param.snapshot_end_arclength_m = -30;
  object_occupancy_param.full_body_start_arclength_m = -32;
  object_occupancy_param.full_body_end_arclength_m = -30;
  EXPECT_TRUE(IsFarAwayBehindAgent(
      ego_param_, object_occupancy_param, /*is_fully_behind_ego=*/true,
      /*is_stationary=*/false, /*behavior_type=*/pb::DECOUPLED_PULL_OVER,
      /*pullover_jump_in_type=*/
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN));
}

// This test validates the tag for oncoming agent.
TEST_F(TrajectoryReasoningInfoUtilTest, OncomingAgentTest) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/100,
                                /*y=*/3,
                                /*yaw=*/-3.14);
  const math::Pose2d end_pose(/*x=*/5, /*y=*/0,
                              /*yaw=*/-3.14);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose, /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 0.5
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);

  const auto in_lane_state =
      CreateAgentInLaneStates(lane_sequence_geometry, agent);

  LateralBlockingStateMetaData oncoming_object_blocking_data;
  oncoming_object_blocking_data.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  oncoming_object_blocking_data.trajectory_id =
      in_lane_state.primary_trajectory_id;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  // Soft blocking for oncoming agent
  oncoming_object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  auto object_result = GenerateAgentReasoningInfo(
      in_lane_state, *planner_object, planner_objects_history_,
      object_predictions_, required_lat_gap_, oncoming_object_blocking_data,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_oncoming());
}

TEST_F(TrajectoryReasoningInfoUtilTest, OncomingAgentTestForOOS) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/100,
                                /*y=*/3,
                                /*yaw=*/-3.14);
  const math::Pose2d end_pose(/*x=*/5, /*y=*/0,
                              /*yaw=*/-3.14);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose, /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  DCHECK_EQ(agent.predicted_trajectories().size(), 1);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  for (const auto& predicted_trajectory : agent.predicted_trajectories()) {
    const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
        agent.tracked_object(), predicted_trajectory,
        /*is_primary_trajectory=*/true);
    predicted_trajectory_wrappers.push_back(
        std::move(predicted_trajectory_wrapper));
  }

  const voy::TrackedObject& tracked_object = agent.tracked_object();
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent, timestamp, pose_at_plan_init_ts);

  const ObjectOccupancyState object_occupancy_state =
      GenerateObjectOccupancyState(
          planner_object, /*object_information=*/{},
          predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary, ego_param_,
          /*motion_mode=*/pb::FORWARD);
  LateralBlockingStateMetaData oncoming_object_blocking_data;
  oncoming_object_blocking_data.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  oncoming_object_blocking_data.trajectory_id =
      object_occupancy_state.primary_trajectory_id();
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  // Soft blocking for oncoming agent
  oncoming_object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  auto object_result = GenerateAgentReasoningInfo(
      object_occupancy_state, planner_objects_history_, required_lat_gap_,
      oncoming_object_blocking_data, /*ls_tl_infos=*/{}, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_oncoming());
}

// This test validates the tag for crossing agent.
TEST_F(TrajectoryReasoningInfoUtilTest, CrossingAgentTest) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/50,
                                /*y=*/50,
                                /*yaw=*/1.57);
  const math::Pose2d end_pose(/*x=*/50, /*y=*/-50,
                              /*yaw=*/1.57);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose, /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 0.5
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);

  auto in_lane_state = CreateAgentInLaneStates(lane_sequence_geometry, agent);
  in_lane_state.predicted_trajectories[in_lane_state.primary_trajectory_id]
      .trajectory_metadata.intention_type =
      pb::AgentTrajectoryIntentionType::CROSS;

  // soft+hard+soft for crossing agent
  LateralBlockingStateMetaData crossing_object_blocking_data;
  crossing_object_blocking_data.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  crossing_object_blocking_data.trajectory_id =
      in_lane_state.primary_trajectory_id;
  pb::LateralBlockingInfo soft_blocking_info;
  soft_blocking_info.set_blocking_state(
      pb::LateralBlockingState::SOFT_BLOCKING);
  pb::LateralBlockingInfo hard_blocking_info;
  hard_blocking_info.set_blocking_state(
      pb::LateralBlockingState::HARD_BLOCKING);
  crossing_object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(40, soft_blocking_info);
  crossing_object_blocking_data.trajectory_blocking_infos.insert(
      crossing_object_blocking_data.trajectory_blocking_infos.end() - 1, 10,
      hard_blocking_info);
  crossing_object_blocking_data.trajectory_blocking_infos.insert(
      crossing_object_blocking_data.trajectory_blocking_infos.end() - 1, 40,
      soft_blocking_info);
  auto object_result = GenerateAgentReasoningInfo(
      in_lane_state, *planner_object, planner_objects_history_,
      object_predictions_, required_lat_gap_, crossing_object_blocking_data,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_crossing());
}

TEST_F(TrajectoryReasoningInfoUtilTest, CrossingAgentTestForOOS) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/50,
                                /*y=*/50,
                                /*yaw=*/1.57);
  const math::Pose2d end_pose(/*x=*/50, /*y=*/-50,
                              /*yaw=*/1.57);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose, /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  DCHECK_EQ(agent.predicted_trajectories().size(), 1);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  for (const auto& predicted_trajectory : agent.predicted_trajectories()) {
    const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
        agent.tracked_object(), predicted_trajectory,
        /*is_primary_trajectory=*/true);
    predicted_trajectory_wrappers.push_back(
        std::move(predicted_trajectory_wrapper));
  }
  const voy::TrackedObject& tracked_object = agent.tracked_object();
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent, timestamp, pose_at_plan_init_ts);

  const ObjectOccupancyState object_occupancy_state =
      GenerateObjectOccupancyState(
          planner_object, /*object_information=*/{},
          predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary, ego_param_,
          /*motion_mode=*/pb::FORWARD);

  // soft+hard+soft for crossing agent
  LateralBlockingStateMetaData crossing_object_blocking_data;
  crossing_object_blocking_data.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  crossing_object_blocking_data.trajectory_id =
      object_occupancy_state.primary_trajectory_id();
  pb::LateralBlockingInfo soft_blocking_info;
  soft_blocking_info.set_blocking_state(
      pb::LateralBlockingState::SOFT_BLOCKING);
  pb::LateralBlockingInfo hard_blocking_info;
  hard_blocking_info.set_blocking_state(
      pb::LateralBlockingState::HARD_BLOCKING);
  crossing_object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(40, soft_blocking_info);
  crossing_object_blocking_data.trajectory_blocking_infos.insert(
      crossing_object_blocking_data.trajectory_blocking_infos.end() - 1, 10,
      hard_blocking_info);
  crossing_object_blocking_data.trajectory_blocking_infos.insert(
      crossing_object_blocking_data.trajectory_blocking_infos.end() - 1, 40,
      soft_blocking_info);
  auto object_result = GenerateAgentReasoningInfo(
      object_occupancy_state, planner_objects_history_, required_lat_gap_,
      crossing_object_blocking_data, /*ls_tl_infos=*/{}, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_crossing());
}

// This test validates the tag for cut-in agent.
TEST_F(TrajectoryReasoningInfoUtilTest, CutIngAgentTest) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/10,
                                /*y=*/3,
                                /*yaw=*/0);
  const math::Pose2d end_pose(/*x=*/100, /*y=*/00,
                              /*yaw=*/0);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose, /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 0.5
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);

  const auto in_lane_state =
      CreateAgentInLaneStates(lane_sequence_geometry, agent);

  LateralBlockingStateMetaData cut_in_object_blocking_object;
  cut_in_object_blocking_object.tracked_blocking_info.set_blocking_state(
      pb::LateralBlockingState::NON_BLOCKING);
  cut_in_object_blocking_object.trajectory_id =
      in_lane_state.primary_trajectory_id;
  cut_in_object_blocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  pb::LateralBlockingInfo non_blocking_info;
  non_blocking_info.set_blocking_state(pb::LateralBlockingState::NON_BLOCKING);
  pb::LateralBlockingInfo soft_blocking_info;
  non_blocking_info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  pb::LateralBlockingInfo hard_blocking_info;
  hard_blocking_info.set_blocking_state(
      pb::LateralBlockingState::HARD_BLOCKING);
  cut_in_object_blocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(10, non_blocking_info);
  cut_in_object_blocking_object.trajectory_blocking_infos.insert(
      cut_in_object_blocking_object.trajectory_blocking_infos.end() - 1, 10,
      soft_blocking_info);
  cut_in_object_blocking_object.trajectory_blocking_infos.insert(
      cut_in_object_blocking_object.trajectory_blocking_infos.end() - 1, 70,
      hard_blocking_info);
  auto object_result = GenerateAgentReasoningInfo(
      in_lane_state, *planner_object, planner_objects_history_,
      object_predictions_, required_lat_gap_, cut_in_object_blocking_object,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_cut_in());
  EXPECT_TRUE(object_result.is_lead_and_merge());
  EXPECT_TRUE(object_result.is_in_current_lane());
}

TEST_F(TrajectoryReasoningInfoUtilTest, CutIngAgentTestForOOS) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  const math::Pose2d start_pose(/*x=*/10,
                                /*y=*/3,
                                /*yaw=*/0);
  const math::Pose2d end_pose(/*x=*/100, /*y=*/00,
                              /*yaw=*/0);
  const auto agent = BuildAgentPrediction(
      voy::perception::VEHICLE, /*id=*/3, start_pose, end_pose,
      /*length=*/5.0,
      /*width=*/2.0, /*velocity=*/1.0, /*start_timestamp=*/100);
  DCHECK_EQ(agent.predicted_trajectories().size(), 1);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  for (const auto& predicted_trajectory : agent.predicted_trajectories()) {
    const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
        agent.tracked_object(), predicted_trajectory,
        /*is_primary_trajectory=*/true);
    predicted_trajectory_wrappers.push_back(
        std::move(predicted_trajectory_wrapper));
  }
  const voy::TrackedObject& tracked_object = agent.tracked_object();
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent, timestamp, pose_at_plan_init_ts);

  const ObjectOccupancyState object_occupancy_state =
      GenerateObjectOccupancyState(
          planner_object, /*object_information=*/{},
          predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary, ego_param_,
          /*motion_mode=*/pb::FORWARD);

  LateralBlockingStateMetaData cut_in_object_blocking_object;
  cut_in_object_blocking_object.tracked_blocking_info.set_blocking_state(
      pb::LateralBlockingState::NON_BLOCKING);
  cut_in_object_blocking_object.trajectory_id =
      object_occupancy_state.primary_trajectory_id();
  cut_in_object_blocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  pb::LateralBlockingInfo non_blocking_info;
  non_blocking_info.set_blocking_state(pb::LateralBlockingState::NON_BLOCKING);
  pb::LateralBlockingInfo soft_blocking_info;
  non_blocking_info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  pb::LateralBlockingInfo hard_blocking_info;
  hard_blocking_info.set_blocking_state(
      pb::LateralBlockingState::HARD_BLOCKING);
  cut_in_object_blocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(10, non_blocking_info);
  cut_in_object_blocking_object.trajectory_blocking_infos.insert(
      cut_in_object_blocking_object.trajectory_blocking_infos.end() - 1, 10,
      soft_blocking_info);
  cut_in_object_blocking_object.trajectory_blocking_infos.insert(
      cut_in_object_blocking_object.trajectory_blocking_infos.end() - 1, 70,
      hard_blocking_info);
  auto object_result = GenerateAgentReasoningInfo(
      object_occupancy_state, planner_objects_history_, required_lat_gap_,
      cut_in_object_blocking_object, /*ls_tl_infos=*/{}, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_cut_in());
  EXPECT_TRUE(object_result.is_lead_and_merge());
  EXPECT_TRUE(object_result.is_in_current_lane());
}

// This test validates the tag for cyclist overtaking ego.
TEST_F(TrajectoryReasoningInfoUtilTest, CyclistOvertakingEgoTest) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  // An agent located on the right side of the lane centerline, it's
  // predicted trajectory is straight, and it's speed is larger than ego.
  const math::Pose2d start_pose(/*x=*/9,
                                /*y=*/-1.5,
                                /*yaw=*/0);
  const math::Pose2d end_pose(/*x=*/99, /*y=*/-1.5,
                              /*yaw=*/0);
  const auto agent = BuildAgentPrediction(
      voy::perception::CYCLIST, /*id=*/3, start_pose, end_pose,
      /*length=*/2.0,
      /*width=*/1.0, /*velocity=*/9.0, /*start_timestamp=*/100);
  const auto in_lane_state =
      CreateAgentInLaneStates(lane_sequence_geometry, agent);

  // Planner object set is_overtaking to false.
  planner_object_->set_is_overtaking(false);

  // Ego speed is less than the speed of the agent.
  ego_param_.speed_mps = 5.0;

  // Soft blocking for agent beside ego.
  LateralBlockingStateMetaData object_blocking_data;
  object_blocking_data.global_blocking_state =
      pb::TrajectoryState::SOFT_BLOCKING;
  object_blocking_data.trajectory_id = in_lane_state.primary_trajectory_id;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);

  auto object_result = GenerateAgentReasoningInfo(
      in_lane_state, *planner_object_, planner_objects_history_,
      object_predictions_, required_lat_gap_, object_blocking_data,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_cyclist_overtaking());
}

TEST_F(TrajectoryReasoningInfoUtilTest, CyclistOvertakingEgoTestForOOS) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  // An agent located on the right side of the lane centerline, it's
  // predicted trajectory is straight, and it's speed is larger than ego.
  const math::Pose2d start_pose(/*x=*/9,
                                /*y=*/-1.5,
                                /*yaw=*/0);
  const math::Pose2d end_pose(/*x=*/99, /*y=*/-1.5,
                              /*yaw=*/0);
  const auto agent = BuildAgentPrediction(
      voy::perception::CYCLIST, /*id=*/3, start_pose, end_pose,
      /*length=*/2.0,
      /*width=*/1.0, /*velocity=*/9.0, /*start_timestamp=*/100);
  DCHECK_EQ(agent.predicted_trajectories().size(), 1);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  for (const auto& predicted_trajectory : agent.predicted_trajectories()) {
    const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
        agent.tracked_object(), predicted_trajectory,
        /*is_primary_trajectory=*/true);
    predicted_trajectory_wrappers.push_back(
        std::move(predicted_trajectory_wrapper));
  }
  const voy::TrackedObject& tracked_object = agent.tracked_object();
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent, timestamp, pose_at_plan_init_ts);
  const ObjectOccupancyState object_occupancy_state =
      GenerateObjectOccupancyState(
          planner_object, /*object_information=*/{},
          predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary, ego_param_,
          /*motion_mode=*/pb::FORWARD);

  // Planner object set is_overtaking to false.
  planner_object_->set_is_overtaking(false);

  // Ego speed is less than the speed of the agent.
  ego_param_.speed_mps = 5.0;

  // Soft blocking for agent beside ego.
  LateralBlockingStateMetaData object_blocking_data;
  object_blocking_data.global_blocking_state =
      pb::TrajectoryState::SOFT_BLOCKING;
  object_blocking_data.trajectory_id =
      object_occupancy_state.primary_trajectory_id();
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  object_blocking_data.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);

  auto object_result = GenerateAgentReasoningInfo(
      object_occupancy_state, planner_objects_history_, required_lat_gap_,
      object_blocking_data, /*ls_tl_infos=*/{}, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, *agent_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_TRUE(object_result.is_cyclist_overtaking());
}

TEST_F(TrajectoryReasoningInfoUtilTest, NotIgnorableUnknownAgentTest) {
  // The agent is unknown type, and close to the ego.
  AgentInLaneStates unknown_agent = agent_;
  unknown_agent.agent_metadata.agent_type =
      voy::perception::ObjectType::UNKNOWN;
  unknown_agent.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = true;
  unknown_agent.agent_metadata.attributes.insert(
      unknown_agent.agent_metadata.attributes.end(), voy::perception::NOT_SET);
  pb::TrajectoryReasoningInfo reasoning_info;
  reasoning_info.set_plan_init_state_distance(10);
  reasoning_info.set_is_stationary(true);
  EXPECT_FALSE(IsIgnorableUnknownAgent(
      unknown_agent, reasoning_info,
      /*has_persistent_motion=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/true));
}

TEST_F(TrajectoryReasoningInfoUtilTest, NotIgnorableUnknownAgentTestForOOS) {
  const auto lane_sequence_geometry = CreateStraightLaneSequenceGeometry();
  // An agent located on the right side of the lane centerline, it's
  // predicted trajectory is straight, and it's speed is larger than ego.
  const math::Pose2d start_pose(/*x=*/9,
                                /*y=*/-1.5,
                                /*yaw=*/0);
  const math::Pose2d end_pose(/*x=*/99, /*y=*/-1.5,
                              /*yaw=*/0);
  const auto agent = BuildAgentPrediction(
      voy::perception::UNKNOWN, /*id=*/3, start_pose, end_pose,
      /*length=*/2.0,
      /*width=*/1.0, /*velocity=*/9.0, /*start_timestamp=*/100);
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  DCHECK_EQ(agent.predicted_trajectories().size(), 1);
  for (const auto& predicted_trajectory : agent.predicted_trajectories()) {
    const PredictedTrajectoryWrapper predicted_trajectory_wrapper(
        agent.tracked_object(), predicted_trajectory,
        /*is_primary_trajectory=*/true);
    predicted_trajectory_wrappers.push_back(
        std::move(predicted_trajectory_wrapper));
  }
  const voy::TrackedObject& tracked_object = agent.tracked_object();
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  PlannerObject planner_object(agent, timestamp, pose_at_plan_init_ts);
  const ObjectOccupancyState object_occupancy_state =
      GenerateObjectOccupancyState(
          planner_object, /*object_information=*/{},
          predicted_trajectory_wrappers, lane_sequence_geometry.nominal_path,
          lane_sequence_geometry.left_lane_boundary,
          lane_sequence_geometry.right_lane_boundary, ego_param_,
          /*motion_mode=*/pb::FORWARD);
  pb::TrajectoryReasoningInfo reasoning_info;
  reasoning_info.set_plan_init_state_distance(10);
  reasoning_info.set_is_stationary(true);
  EXPECT_FALSE(IsIgnorableUnknownAgent(
      object_occupancy_state, reasoning_info,
      /*has_persistent_motion=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/true));
}

TEST_F(TrajectoryReasoningInfoUtilTest, HasPersistentMotionTest) {
  const std::optional<PlannerObjectRecentKinematicInfo>& recent_kinematic_info =
      planner_objects_history_.GetRecentKinematicInfo(*planner_object_, 1000,
                                                      reference_line_);
  EXPECT_TRUE(HasPersistentMotion(recent_kinematic_info,
                                  planner_objects_history_, *planner_object_));
  prediction::pb::Agent recent_agent_proto;
  // Add another abruptly changed velocity
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 3.0
        acceleration: 3.0
    )",
      recent_agent_proto.mutable_tracked_object());
  const int64_t timestamp = 2050;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, recent_agent_proto.tracked_object());
  auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
  auto planner_object_map_ptr =
      std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
  std::unique_ptr<PlannerObject> recent_planner_object =
      std::make_unique<PlannerObject>(recent_agent_proto, timestamp,
                                      pose_at_plan_init_ts);
  planner_objects_history_.Update(planner_object_map_ptr, destruction_pool);
  const std::optional<PlannerObjectRecentKinematicInfo>&
      recent_kinematic_info_updated =
          planner_objects_history_.GetRecentKinematicInfo(
              *recent_planner_object, 1000, reference_line_);
  EXPECT_FALSE(HasPersistentMotion(recent_kinematic_info_updated,
                                   planner_objects_history_,
                                   *recent_planner_object));
}
}  // namespace planner
