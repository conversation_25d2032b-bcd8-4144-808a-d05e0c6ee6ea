/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_CameraSigInput.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <CameraSigInput>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_CAMERASIGINPUT_H
# define RTE_CAMERASIGINPUT_H

//# ifndef RTE_CORE
//#  ifdef RTE_APPLICATION_HEADER_FILE
//#   error Multiple application header files included.
//#  endif
//#  define RTE_APPLICATION_HEADER_FILE
//#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
//#   define RTE_PTR2ARRAYBASETYPE_PASSING
//#  endif
//# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_CameraSigInput_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_FC_191_FC_LineTiStamp (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_HeadingAngle (3277U)
#  define Rte_InitValue_FC_191_FC_Line_01_Id (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_Type (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_Width (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_color (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_dx_End (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_dx_End_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_dx_Start (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_dx_Start_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_dy (0U)
#  define Rte_InitValue_FC_191_FC_Line_01_exist_prob (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_HeadingAngle (3277U)
#  define Rte_InitValue_FC_191_FC_Line_02_Id (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_Type (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_Width (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_color (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_dx_End (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_dx_End_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_dx_Start (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_dx_Start_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_dy (0U)
#  define Rte_InitValue_FC_191_FC_Line_02_exist_prob (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_HeadingAngle (3277U)
#  define Rte_InitValue_FC_191_FC_Line_03_Id (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_Type (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_Width (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_color (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_dx_End (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_dx_End_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_dx_Start (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_dx_Start_std (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_dy (0U)
#  define Rte_InitValue_FC_191_FC_Line_03_exist_prob (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_MeasureType (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_ObstacleFlg (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_ParseConf (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_RMSE (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_curvature_alte (2048U)
#  define Rte_InitValue_FC_191_FTFC_Line_01_curve (12288U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_MeasureType (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_ObstacleFlg (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_ParseConf (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_RMSE (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_curvature_alte (2048U)
#  define Rte_InitValue_FC_191_FTFC_Line_02_curve (12288U)
#  define Rte_InitValue_FC_191_FTFC_Line_03_MeasureType (0U)
#  define Rte_InitValue_FC_191_FTFC_Line_03_curvature_alte (2048U)
#  define Rte_InitValue_FC_191_FTFC_Line_03_curve (12288U)
#  define Rte_InitValue_FC_192_FC_Line_04_HeadingAngle (3277U)
#  define Rte_InitValue_FC_192_FC_Line_04_Id (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_Type (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_Width (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_color (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_dx_End (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_dx_End_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_dx_Start (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_dx_Start_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_dy (0U)
#  define Rte_InitValue_FC_192_FC_Line_04_exist_prob (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_HeadingAngle (3277U)
#  define Rte_InitValue_FC_192_FC_Line_05_Type (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_Width (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_color (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_dx_End (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_dx_End_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_dx_Start (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_dx_Start_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_dy (0U)
#  define Rte_InitValue_FC_192_FC_Line_05_exist_prob (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_HeadingAngle (3277U)
#  define Rte_InitValue_FC_192_FC_Line_06_Type (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_Width (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_color (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_dx_End (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_dx_End_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_dx_Start (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_dx_Start_std (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_dy (0U)
#  define Rte_InitValue_FC_192_FC_Line_06_exist_prob (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_03_ObstacleFlg (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_03_ParseConf (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_03_RMSE (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_MeasureType (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_ObstacleFlg (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_ParseConf (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_RMSE (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_curvature_alte (2048U)
#  define Rte_InitValue_FC_192_FTFC_Line_04_curve (12288U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_MeasureType (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_ObstacleFlg (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_ParseConf (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_RMSE (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_curvature_alte (2048U)
#  define Rte_InitValue_FC_192_FTFC_Line_05_curve (12288U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_MeasureType (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_ObstacleFlg (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_ParseConf (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_RMSE (0U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_curvature_alte (2048U)
#  define Rte_InitValue_FC_192_FTFC_Line_06_curve (12288U)
#  define Rte_InitValue_FC_19D_FC_CIPV_ID (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Ax (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Ay (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_ClassifiedView (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Dx (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Dy (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_ExistProb (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_HeadingAngle (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Height (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_LaneAssignment (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Length (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_MotionType (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Track_Age (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Track_ID (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Type (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Vx (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Vy (0U)
#  define Rte_InitValue_FC_19D_FC_Obj20_Width (0U)
#  define Rte_InitValue_FC_19D_FC_obj20_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19D_FC_obj20_Taillight_Info (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_CenterAngle (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_CornerPoint_x (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_CornerPoint_y (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_DistInLane (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_objCutInFlag (0U)
#  define Rte_InitValue_FC_19D_FTFC_Obj20_objCutInLane (0U)
#  define Rte_InitValue_FC_19D_FrFr_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19D_FrFr_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_FusionedFC_Track_ID (255U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19D_Fr_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19E_FrFr_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19E_LeFr_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_FusionedFC_Track_ID (255U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19E_Le_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID (255U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19F_LeFr_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19F_RiFr_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Class (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Dx (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Dx_Vnce (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Dy (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Dy_Vnce (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_FusionedFC_Track_ID (255U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_HeadingAngle (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Height (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Length (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Vy (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Vy_std (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_Width (0U)
#  define Rte_InitValue_FC_19F_Ri_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_1A0_FrFr_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A0_Fr_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A0_LeFr_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A0_Le_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Ax (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Ay (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Brakelight_Info (FALSE)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_ExistProb (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID (255U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_ObstacleProb (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Taillight_Info (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Track_Age (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Track_ID (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Vx (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_Vx_std (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A0_RiFr_AccOBJ_fusion_Sts (0U)
#  define Rte_InitValue_FC_1A0_Ri_AccOBJ_confi (0U)
#  define Rte_InitValue_FC_1A5_FC_FrontCameraCalibrationStatus (0U)
#  define Rte_InitValue_FC_1A5_FC_LaneChangeStatus (0U)
#  define Rte_InitValue_FC_1A5_FC_RollingCounter (0U)
# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_LineTiStamp() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_LineTiStamp.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Id() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_Id.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_01_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Id() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_Id.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_02_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Id() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_Id.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FC_Line_03_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_01_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_02_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_03_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_03_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_191_FTFC_Line_03_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Id() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_Id.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_04_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_05_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_color() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_color.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_End() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_dx_End.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_End_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_dx_End_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_Start() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_dx_Start.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_Start_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_dx_Start_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_exist_prob() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FC_Line_06_exist_prob.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_03_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_03_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_03_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_04_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_05_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_MeasureType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_MeasureType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_ObstacleFlg() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_ObstacleFlg.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_ParseConf() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_ParseConf.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_RMSE() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_RMSE.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_curvature_alte() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_curvature_alte.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_curve() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_192_FTFC_Line_06_curve.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_CIPV_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_CIPV_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_ClassifiedView() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_ClassifiedView.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_LaneAssignment() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_LaneAssignment.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_MotionType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_MotionType.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Type() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Type.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_Obj20_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_Obj20_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_obj20_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_obj20_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_obj20_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FC_obj20_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_CenterAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_CenterAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_CornerPoint_x() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_CornerPoint_x.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_CornerPoint_y() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_CornerPoint_y.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_DistInLane() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_DistInLane.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_objCutInFlag() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_objCutInFlag.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FTFC_Obj20_objCutInLane() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FTFC_Obj20_objCutInLane.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FrFr_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FrFr_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FrFr_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_FrFr_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19D_Fr_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_FrFr_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_LeFr_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19E_Le_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_LeFr_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_RiFr_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Class() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Class.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Dx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dx_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Dx_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Dy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dy_Vnce() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Dy_Vnce.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_HeadingAngle() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_HeadingAngle.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Height() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Height.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Length() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Length.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vy() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Vy.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vy_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Vy_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Width() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_Width.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_19F_Ri_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_FrFr_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_FrFr_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Fr_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_Fr_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_LeFr_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_LeFr_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Le_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_Le_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Ax() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Ax.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Ay() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Ay.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Brakelight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Brakelight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_ExistProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_ExistProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_ObstacleProb() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_ObstacleProb.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Taillight_Info() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Taillight_Info.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Track_Age() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Track_Age.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Track_ID() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Track_ID.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Vx() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Vx.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Vx_std() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_Vx_std.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_fusion_Sts() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_RiFr_AccOBJ_fusion_Sts.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Ri_AccOBJ_confi() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A0_Ri_AccOBJ_confi.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A5_FC_FrontCameraCalibrationStatus() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A5_FC_FrontCameraCalibrationStatus.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A5_FC_LaneChangeStatus() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A5_FC_LaneChangeStatus.value)


#  define Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A5_FC_RollingCounter() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_CameraSigInput_CameraSigInput_10ms_Runnable.Rte_FC_1A5_FC_RollingCounter.value)


#  define Rte_IWrite_CameraSigInput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value = *(data) \
  )


#  define Rte_IWriteRef_CameraSigInput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value \
  )


#  define Rte_IWrite_CameraSigInput_10ms_Runnable_CSI_ObjectInfo_CSI_ObjectInfo(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_ObjectInfo_CSI_ObjectInfo.value = *(data) \
  )


#  define Rte_IWriteRef_CameraSigInput_10ms_Runnable_CSI_ObjectInfo_CSI_ObjectInfo() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_ObjectInfo_CSI_ObjectInfo.value \
  )


#  define Rte_IWrite_CameraSigInput_Init_CSI_LaneInfo_CSI_LaneInfo(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_CameraSigInput_CameraSigInput_Init.Rte_CSI_LaneInfo_CSI_LaneInfo.value = *(data) \
  )


#  define Rte_IWriteRef_CameraSigInput_Init_CSI_LaneInfo_CSI_LaneInfo() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_CameraSigInput_CameraSigInput_Init.Rte_CSI_LaneInfo_CSI_LaneInfo.value \
  )


#  define Rte_IWrite_CameraSigInput_Init_CSI_ObjectInfo_CSI_ObjectInfo(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_CameraSigInput_CameraSigInput_Init.Rte_CSI_ObjectInfo_CSI_ObjectInfo.value = *(data) \
  )


#  define Rte_IWriteRef_CameraSigInput_Init_CSI_ObjectInfo_CSI_ObjectInfo() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_CameraSigInput_CameraSigInput_Init.Rte_CSI_ObjectInfo_CSI_ObjectInfo.value \
  )


# endif /* !defined(RTE_CORE) */


# define CameraSigInput_START_SEC_CODE
// # include "CameraSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_CameraSigInput_10ms_Runnable CameraSigInput_10ms_Runnable
#  define RTE_RUNNABLE_CameraSigInput_Init CameraSigInput_Init
# endif

FUNC(void, CameraSigInput_CODE) CameraSigInput_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, CameraSigInput_CODE) CameraSigInput_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define CameraSigInput_STOP_SEC_CODE
// # include "CameraSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_CAMERASIGINPUT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
