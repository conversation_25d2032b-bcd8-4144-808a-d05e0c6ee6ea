#ifndef ONBOARD_PLANNER_SPEED_REASONING_INPUT_WORLD_CONTEXT_H_
#define ONBOARD_PLANNER_SPEED_REASONING_INPUT_WORLD_CONTEXT_H_

#include <cstdint>
#include <deque>
#include <limits>
#include <map>
#include <memory>
#include <unordered_map>
#include <utility>
#include <vector>

#include "planner/constants.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/conditional_predicted_trajectory_wrapper.h"
#include "planner/speed/overlap/ego_lateral_clearance.h"
#include "planner/speed/reasoning_input/ego_state_history.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner/world_model/waypoint_assist/waypoint_assist_tracker.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"

namespace planner {
namespace speed {

// Class WorldContext incorporates the environment information that does not
// depend on AV’s maneuver and the information about AV itself.
class WorldContext {
 public:
  WorldContext() = delete;
  // This constructor is only for the unit tests configuration.
  WorldContext(const WorldModel& world_model,
               planner::pb::MotionMode motion_mode);
  WorldContext(const SpeedWorldModel& world_model,
               planner::pb::MotionMode motion_mode);

  [[nodiscard]] const RobotState& robot_state() const { return robot_state_; }

  [[nodiscard]] const std::tm* order_start_time_info() const {
    return order_start_time_info_;
  }

  [[nodiscard]] const pnc_map::PncMapService* pnc_map_service() const {
    return joint_pnc_map_service_->pnc_map_service();
  }

  [[nodiscard]] const std::shared_ptr<const pnc_map::JointPncMapService>&
  joint_pnc_map_service() const {
    return joint_pnc_map_service_;
  }

  // NOTE(Tingran): The construction zones that planner uses are the ones with
  // human labeling, not the ones generated solely by perception algorithms.
  [[nodiscard]] const std::vector<ConstructionZone>& construction_zones()
      const {
    return construction_zones_;
  }

  // Interface to check the occlusion area provided by perception.
  [[nodiscard]] const OcclusionChecker& occlusion_checker() const {
    return occlusion_checker_;
  }

  [[nodiscard]] const std::vector<planner::pb::AssistResponse>&
  assist_responses() const {
    return assist_responses_;
  }

  [[nodiscard]] const std::deque<std::unique_ptr<AssistTaskBase>>&
  assist_task_queue() const {
    return assist_task_queue_;
  }

  [[nodiscard]] const RemoteAssistSignal& remote_assist_signal() const {
    return remote_assist_signal_;
  }

  [[nodiscard]] const std::variant<WaypointAssistTracker,
                                   WaypointAssistTrackerSpeedData>&
  waypoint_assist_tracker() const {
    return waypoint_assist_tracker_;
  }

  // Returns the request from MRC node.
  [[nodiscard]] const mrc::pb::MrcRequest* mrc_request_ptr() const {
    return mrc_request_ptr_;
  }

  // Returns the replan request from control node.
  [[nodiscard]] const planner::pb::ReplanRequest* replan_request_ptr() const {
    return replan_request_ptr_;
  }

  [[nodiscard]] const voy::TrafficLights* traffic_light_detection() const {
    return traffic_light_detection_;
  }

  // The timestamp of current ego pose, which is earlier than the planning
  // initial time.
  [[nodiscard]] int64_t ego_pose_timestamp() const {
    DCHECK_LE(robot_state_.current_state_snapshot().timestamp(),
              robot_state_.plan_init_state_snapshot().timestamp());
    return robot_state_.current_state_snapshot().timestamp();
  }

  // Current ego heading.
  [[nodiscard]] double heading() const {
    return robot_state_.current_state_snapshot().heading();
  }

  // Current ego speed in meter per seconds.
  [[nodiscard]] double ego_speed() const {
    return robot_state_.current_state_snapshot().speed();
  }

  // Current ego acceleration in mpss.
  [[nodiscard]] double ego_acceleration() const {
    return robot_state_.current_state_snapshot().acceleration();
  }

  [[nodiscard]] bool EgoAlmostStopped() const {
    return robot_state().current_state_snapshot().speed() <
           constants::kDefaultEgoNearStaticSpeedInMps;
  }

  // Returns the distance in meters from rear axle to front bumper.
  [[nodiscard]] double ra_to_leading_bumper() const;

  // Returns the distance in meters from rear axle to front axle.
  [[nodiscard]] double ra_to_front_axle() const;

  // Returns the shift in meters from rear axle to rear bumper in ego
  // coordinates. This is a negative value.
  [[nodiscard]] double ra_to_trailing_bumper_shift() const;

  // Returns the distance in meters from Ego to global route destination.
  [[nodiscard]] double distance_to_destination_m() const {
    return distance_to_destination_m_;
  }

  // Returns true if ego is near the current order starting position.
  [[nodiscard]] bool is_ego_position_on_route_ready_for_pull_out() const {
    return is_ego_position_on_route_ready_for_pull_out_;
  }

  // Returns ego's distance to ride start position.
  [[nodiscard]] std::optional<double> distance_to_ride_start_position() const {
    return distance_to_ride_start_position_;
  }

  // Returns true if planner should respond mrc.
  [[nodiscard]] bool should_planner_respond_mrc() const {
    return should_planner_respond_mrc_;
  }

  [[nodiscard]] bool should_trigger_pull_out_by_mrc() const {
    return should_trigger_pull_out_by_mrc_;
  }

  [[nodiscard]] bool has_pull_out_finished() const {
    return has_pull_out_finished_;
  }

  [[nodiscard]] int64_t route_solution_mission_id() const {
    return route_solution_mission_id_;
  }

  // Returns the position of current ego car.
  [[nodiscard]] math::geometry::Point2d current_state_snapshot_position()
      const {
    return {robot_state_.current_state_snapshot().x(),
            robot_state_.current_state_snapshot().y()};
  }

  [[nodiscard]] int64_t ego_almost_stopped_duration_ms() const {
    return ego_almost_stopped_duration_ms_;
  }

  [[nodiscard]] int64_t ego_stuck_duration_ms() const {
    return ego_stuck_duration_ms_;
  }

  [[nodiscard]] bool has_vehicle_waiting_behind_ego() const {
    return has_vehicle_waiting_behind_ego_;
  }

  [[nodiscard]] bool has_ego_encroached_left_lane_boundary() const {
    return has_ego_encroached_left_lane_boundary_;
  }

  [[nodiscard]] bool has_ego_encroached_right_lane_boundary() const {
    return has_ego_encroached_right_lane_boundary_;
  }

  [[nodiscard]] double relax_ego_exceeding_road_speed_limit_factor() const {
    return relax_ego_exceeding_road_speed_limit_factor_;
  }

  [[nodiscard]] std::optional<int64_t> ego_come_to_full_stop_timestamp() const {
    return ego_come_to_full_stop_timestamp_;
  }

  [[nodiscard]] std::optional<double>
  discomfort_to_decay_ego_exceeding_road_speed_limit() const {
    return discomfort_to_decay_ego_exceeding_road_speed_limit_;
  }

  [[nodiscard]] planner::pb::LaneChangeState
  previous_selected_lane_change_state() const {
    return previous_selected_lane_change_state_;
  }

  [[nodiscard]] bool is_high_agent_density() const {
    return is_high_agent_density_;
  }

  [[nodiscard]] bool is_pull_out_triggered() const {
    return is_pull_out_triggered_;
  }

  [[nodiscard]] bool keep_ego_in_static_state_request() const {
    return keep_ego_in_static_state_request_;
  }

  [[nodiscard]] planner::pb::MotionMode motion_mode() const {
    return motion_mode_;
  }

  [[nodiscard]] ObjectId previous_dominant_risk_object_id() const {
    return previous_dominant_risk_object_id_;
  }

  [[nodiscard]] int previous_dominant_risk_object_duration() const {
    return previous_dominant_risk_object_duration_;
  }

  // Returns the remote speed limit from RA.
  [[nodiscard]] const std::optional<voy::planner::RemoteSpeedLimit>&
  remote_speed_limit() const {
    return remote_speed_limit_;
  }

  // Returns if ego should ignore traffic lights for map changed scene after
  // RA's confirm.
  [[nodiscard]] bool should_ignore_map_change_traffic_lights() const {
    return should_ignore_map_change_traffic_lights_;
  }

  // Returns the start ra arclength of the nearest map changed polygon.
  [[nodiscard]] const std::optional<double>&
  nearest_tl_changed_polygon_start_ra_arclength() const {
    return nearest_tl_changed_polygon_start_ra_arclength_;
  }

  [[nodiscard]] const EgoStateHistory& ego_state_history() const {
    return ego_state_history_;
  }

  [[nodiscard]] const std::vector<PickupDropoffZoneInfo>&
  pickup_dropoff_zone_infos() const {
    return pickup_dropoff_zone_infos_;
  }

  [[nodiscard]] const std::vector<hdmap::StopCellEntity::Type>
  forbidden_stop_types_for_mrc() const {
    return forbidden_stop_types_for_mrc_;
  }

  [[nodiscard]] const std::vector<speed::pb::NarrowPassageInfo>&
  narrow_passage_infos() const {
    return narrow_passage_infos_;
  }

  void set_narrow_passage_infos(
      std::vector<speed::pb::NarrowPassageInfo> narrow_passage_infos) {
    narrow_passage_infos_ = std::move(narrow_passage_infos);
  }

  // Updates the duration that Ego has stopped in the light of the history
  // information and current Ego speed.
  void UpdateEgoAlmostStoppedDuration(const pb::SpeedSeed& previous_speed_seed,
                                      pb::WorldContextSeed& current_seed,
                                      pb::WorldContextDebug* debug);

  void UpdateEgoComeToFullStopTimestamp(
      const planner::pb::Trajectory& selected_trajectory,
      pb::WorldContextDebug* debug);

  // Updates factor for relaxing road speed limit.
  void UpdateRelaxSpeedLimitInfo(
      const TrajectoryInfo& trajectory_info,
      const speed::pb::SpeedSeed& previous_speed_seed,
      pb::WorldContextSeed& current_seed, pb::WorldContextDebug* debug);

  // Updates previous selected lane change state.
  void UpdateLaneChangeState(
      const planner::pb::DecoupledManeuverSeed& previous_iter_seed);

  // Updates the signal indicating whether there are vehicles waiting behind
  // Ego.
  void UpdateHasVehicleWaitingBehindEgo(
      const std::unordered_map<ObjectId, PlannerObject>& objects,
      const std::map<ObjectId, speed::pb::ObjectProximityInfo>&
          object_proximity_info,
      pb::WorldContextDebug* debug);

  // Updates the nearest tl changed polygon start ra arclength.
  void UpdateTheNearestTLChangedPolygonStart(
      const std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>&
          cz_proximity_info_map);

  // Updates the signal indicating whether ego has encroached its left and right
  // lane boundary.
  void UpdateHasEgoEncroachedLaneBoundary(
      const LaneSequenceIterator& lane_sequence_iterator,
      pb::WorldContextDebug* debug);

  // Updates the previous_dominant_risk_object_id_ and the result in seed.
  void UpdateDominantRiskObjectId(
      const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
      pb::WorldContextSeed& current_seed);

  // Updates the history of Ego states according to the ART seed.
  void UpdateEgoStateHistory(const planner::speed::pb::AgentReactionTrackerSeed&
                                 previous_iter_art_seed);

  // Currently SpeedWorldModel is extracted from WorldModel. And to avoid
  // inconsistent seed content among path and speed node it does not include any
  // seed info. Thus we assign |is_pull_out_triggered| with an update function.
  // TODO(jinhao): Get the signal from SpeedWorldModel.
  void UpdateIsPullOutTriggered(
      const planner::pb::DecoupledManeuverSeed& previous_iter_seed);

  void PopulateDebug(pb::WorldContextDebug* debug) const;

  // Gets agent conditional predicted yield intention.
  [[nodiscard]] std::optional<double>
  GetAgentConditionalPredictionYieldIntention(int64_t object_id) const;

  // Gets agent conditional predicted yield trajectory pair.
  [[nodiscard]] std::optional<PairedPredictedTrajectoryWrapper>
  GetAgentConditionalPredictionYieldTrajectoryPair(int64_t object_id) const;

 private:
  // Robot state.
  const RobotState& robot_state_;

  // The joint_pnc_map_service provides access to both pnc online and offline
  // map elements, where online elements indicates the changed elements in a
  // dynamic pnc map.
  const std::shared_ptr<const pnc_map::JointPncMapService>
      joint_pnc_map_service_;

  const std::tm* order_start_time_info_;

  // The environment information.
  const OcclusionChecker& occlusion_checker_;
  const std::vector<ConstructionZone>& construction_zones_;

  const std::vector<planner::pb::AssistResponse>& assist_responses_;

  const std::deque<std::unique_ptr<AssistTaskBase>>& assist_task_queue_;

  const RemoteAssistSignal& remote_assist_signal_;
  // Given overload constructors 'WorldContext(const SpeedWorldModel&
  // world_model, ...) and WorldContext(const WorldModel& world_model, ...)', in
  // order to handle differences between similar member variables in overloaded
  // constructors, we used 'std::variant' to hold either type.
  std::variant<WaypointAssistTracker, WaypointAssistTrackerSpeedData>
      waypoint_assist_tracker_;

  // The request from MRC node, owned by latest snapshot of world model.
  const mrc::pb::MrcRequest* const mrc_request_ptr_ = nullptr;
  // The replan request from control node, owned by latest snapshot of world
  // model.
  const planner::pb::ReplanRequest* const replan_request_ptr_ = nullptr;
  // The traffic light detected by the perception module.
  const voy::TrafficLights* const traffic_light_detection_ = nullptr;

  // Distance in meters from Ego to global route destination by querying
  // routing module.
  // TODO(waylon): Use destination point to replace the destination distance.
  double distance_to_destination_m_ = std::numeric_limits<double>::max();
  bool is_ego_position_on_route_ready_for_pull_out_ = false;
  std::optional<double> distance_to_ride_start_position_;
  bool should_planner_respond_mrc_ = false;
  bool should_trigger_pull_out_by_mrc_ = false;
  bool has_pull_out_finished_ = false;
  int64_t route_solution_mission_id_ = -1;

  // The duration in millisecond for which Ego has almost stopped.
  int64_t ego_almost_stopped_duration_ms_ = 0;

  // The duration in milliseconds for which ego has been stuck.
  // Note that this differs from |ego_almost_stopped_duration_ms_| in that it
  // does not include durations when ego was intentionally stopped
  // for specific purposes, such as waiting for a red light or due to PIPO
  // business logic.
  int64_t ego_stuck_duration_ms_ = 0;

  // Whether there are vehicle waiting behind Ego.
  bool has_vehicle_waiting_behind_ego_ = false;

  // The absolute timestamp that ego will stop according to last frame's
  // selected trajectory.
  std::optional<int64_t> ego_come_to_full_stop_timestamp_;

  // Whether any point on ego contour has exceeded current lane left boundary.
  bool has_ego_encroached_left_lane_boundary_ = false;

  // Whether any point on ego contour has exceeded current lane right boundary.
  bool has_ego_encroached_right_lane_boundary_ = false;

  // The motion mode of ego.
  planner::pb::MotionMode motion_mode_;

  // Indicators the strength of hysteresis to smoothly reduce the road speed
  // limit from a larger plan init state speed to the predefined road speed
  // limit. Its range is [0.0, 1.0]. 0.0 means no hysteresis, and 1.0 means the
  // strongest hysteresis. As our intention, it should decay from 1.0 to 0.0
  // gradually in several cycles. By default, we do not add hysteresis for most
  // cases. This is only used for lane change for now.
  double relax_ego_exceeding_road_speed_limit_factor_ = 0.0;

  // Stores the previous discomfort. When the current discomfort is larger than
  // this value, we need to reset road speed limit decay factor. It is
  // std::nullopt if there is no decay strategy.
  // TODO(speed): Research if we can deprecate this field, and only use a
  // boolean to indicate the decay strategy and use the discomfort value from
  // other fields of the seed as a signal to reset decay factor.
  std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit_;

  // The lane change state from previous selected trajectory.
  planner::pb::LaneChangeState previous_selected_lane_change_state_ =
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_NONE;

  // Whether ego is at high agent density scenario.
  bool is_high_agent_density_ = false;

  // Returns the planner's GLOBAL flag to indicate if pull out is triggered.
  // NOTE: Different from the |is_pull_out_jump_out| in TrajectoryInfo, the
  // |is_pull_out_jump_out| is a trajectory level signal that indicates the lane
  // sequence is jump out for pull out.
  bool is_pull_out_triggered_ = false;

  // The request from routing to help planner keep ego in static state when
  // routing changes to idle.
  bool keep_ego_in_static_state_request_ = false;

  // The object_id and the number of iterations that ego was interacting with at
  // high discomfort.
  ObjectId previous_dominant_risk_object_id_ = kInvalidObjectId;

  int previous_dominant_risk_object_duration_ = 0;

  // The signal for ignoring traffic lights for map change area.
  bool should_ignore_map_change_traffic_lights_ = false;

  // The start ra arclength of the nearest tl changed polygon. It is
  // nullptr if there is no tl changed polygon on ego path.
  std::optional<double> nearest_tl_changed_polygon_start_ra_arclength_ =
      std::nullopt;

  // Stores conditional predicted trajectory results for lane change.
  const tbb::concurrent_unordered_map<ObjectId,
                                      ConditionalPredictedTrajectoryWrapper>&
      object_conditional_prediction_map_;

  // The remote speed limit request from RA, owned by latest snapshot of world
  // model.
  const std::optional<voy::planner::RemoteSpeedLimit>& remote_speed_limit_;

  EgoStateHistory ego_state_history_;

  // The pickup dropopff zone used by pull over.
  const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos_;

  // Forbidden stop types for mrm harbor or mrm pullright.
  std::vector<hdmap::StopCellEntity::Type> forbidden_stop_types_for_mrc_;

  // The list of narrow passage infos which are ordered in ascending order by
  // the start position. A narrow passage is defined by a range of ego path with
  // both side clearances being under 0.4m. Two ranges are at least being of
  // ego's length apart. To reduce latency, narrow passages are neither computed
  // when ego speed is over 6km/h, nor for over 30m in terms of ego moving
  // distance.
  std::vector<speed::pb::NarrowPassageInfo> narrow_passage_infos_;
};

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_INPUT_WORLD_CONTEXT_H_
