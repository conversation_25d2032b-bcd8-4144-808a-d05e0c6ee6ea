#include "prediction/common/utility/prediction_utility.h"

#include <algorithm>
#include <deque>
#include <map>
#include <memory>
#include <utility>

#include <glog/logging.h>

#include "geometry/algorithms/area.h"
#include "geometry/algorithms/arithmetic.h"
#include "geometry/algorithms/distance.h"
#include "geometry/algorithms/intersection.h"
#include "geometry/algorithms/intersects.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction/common/definition/prediction_gflag.h"
#include "prediction/common/utility/conflict_util.h"
#include "prediction_protos/trajectory_generation_type.pb.h"
#include "voy_protos/perception_attribute.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace prediction {
namespace utility {
namespace {

// The minimum number of points required to fit ref line.
constexpr int kMinLinePointNum = 2;

// The search radius for map target search when mouse clicked a point.
constexpr double kSearchMapTargetRadius = 0.2;  // meter.

// Max distance for two points that could be considered as "close".
constexpr double kMaxDistanceOfClosePointsForVehAndPed = 0.1;  // meter.
constexpr double kMaxDistanceOfClosePointsForCyc = 0.01;       // meter.

// Max velocity to consider as stationary object for no person cyclist.
constexpr double kMaxStationaryVelocityForCyclistWithoutPerson = 0.8;  // m/s.

// The considered history horizon used to dynamically modify the stationary
// classifier threshold of current frame.
constexpr int kConsiderPreviousFrames = 5;
constexpr double kStationaryVelocityDecayBuffer = 0.05;  // m/s

// Length upper bound for the right sized vehicle tag.
constexpr double kVehicleLengthUpperBound = 5.5;  // m.
// Length lower bound for the right sized vehicle tag.
constexpr double kVehicleLengthLowerBound = 4.0;  // m.
// Width Upper bound for the right sized vehicle tag.
constexpr double kVehicleWidthUpperBound = 2.2;  // m.
// Width lower bound for the right sized vehicle tag.
constexpr double kVehicleWidthLowerBound = 1.6;  // m.

// The maximum valid steering angle for the turning vehicle.
constexpr double kMaxSteeringAngleForVehicle = math::Degree2Radian(30.0);

constexpr double kNearbyRoadQueryRangeForExpressway = 20.0;  // meters.

}  // namespace

::pb::ModelType ObjectTypeToModelType(voy::perception::ObjectType object_type) {
  switch (object_type) {
    case voy::perception::VEHICLE:
      return ::pb::ModelType::VEHICLE_VECTORNET_PREDICTION;
    case voy::perception::CYCLIST:
      return ::pb::ModelType::CYCLIST_VECTORNET_PREDICTION;
    case voy::perception::PED:
      return ::pb::ModelType::PEDESTRIAN_VECTORNET_PREDICTION;
    default:
      DCHECK(false) << "Does not support to get model type for this "
                       " agent type: "
                    << voy::perception::ObjectType_Name(object_type);
      return ::pb::ModelType::UNKNOWN_MODEL_TYPE;
  }
}

// Returns the max number of history states that store in Agent.
int GetMaxNumOfAgentHistoryStates(voy::perception::ObjectType object_type) {
  if (av_comm::InSimulation()) {
    return constants::kDefaultVehicleOutdatedTimeInPoses;
  }

  const int default_num_history = constants::kDefaultAgentOutdatedTimeInPoses;
  switch (object_type) {
    case voy::perception::ObjectType::VEHICLE:
      // TODO(Jian): Proper refactor between onboard configs and this constant
      // for vehicle.
      return constants::kDefaultVehicleOutdatedTimeInPoses;

    case voy::perception::ObjectType::PED:
    case voy::perception::ObjectType::CYCLIST: {
      int max_num_history = std::max(
          {OnboardConfig::Get(utility::ObjectTypeToModelType(object_type))
               .extractor_config()
               .num_vectors_in_agent_feature(),
           OnboardConfig::Get(::pb::ModelType::ML_PLANNER)
               .extractor_config()
               .num_vectors_in_agent_feature(),
           OnboardConfig::Get(::pb::ModelType::JOINT_TRAJECTORY_PREDICTION)
               .extractor_config()
               .num_vectors_in_agent_feature()});
      DCHECK_GE(max_num_history, default_num_history)
          << "Max size of agent history[" << max_num_history
          << "] should be larger than default[" << default_num_history
          << "], maybe forgot to set in config.";
      return max_num_history;
    }
    default:
      return default_num_history;
  }
}

pb::YieldIntention YieldProbabilityToYieldIntentionType(
    float yield_probability, voy::perception::ObjectType object_type) {
  pb::YieldIntention yield_intention;
  if (math::NearZero(yield_probability - constants::kInvalidYieldProbability)) {
    return yield_intention;
  }
  DCHECK_GE(yield_probability, 0.0);
  DCHECK_LE(yield_probability, 1.0);
  const double yield_classifier_threshold =
      object_type == voy::perception::ObjectType::PED
          ? constants::kYieldClassifierThresholdForPed
          : constants::kYieldClassifierThreshold;
  const pb::YieldIntentionType yield_intention_type =
      yield_probability > yield_classifier_threshold
          ? pb::YieldIntentionType::YIELD_TO_EGO_CAR
          : pb::YieldIntentionType::NOT_YIELD_TO_EGO_CAR;
  yield_intention.set_yield_probability(yield_probability);
  yield_intention.set_yield_intention_type(yield_intention_type);
  return yield_intention;
}

double GetYieldOrNonYieldIntentionProb(const PredictedTrajectory& trajectory) {
  const auto& yield_intention = trajectory.yield_intention();
  switch (yield_intention.yield_intention_type()) {
    case pb::YieldIntentionType::YIELD_TO_EGO_CAR:
      return yield_intention.yield_probability();
    case pb::YieldIntentionType::NOT_YIELD_TO_EGO_CAR:
      return 1.0 - yield_intention.yield_probability();
    default:
      return constants::kInvalidYieldProbability;
  }
}

hdmap::Point TrajPoseToHDMapPointIn2D(const planner::pb::TrajectoryPose& pose) {
  hdmap::Point point;
  point.set_x(pose.x_pos());
  point.set_y(pose.y_pos());
  return point;
}

hdmap::Point PoseToHDMapPointIn3D(const voy::Pose& pose) {
  hdmap::Point point;
  point.set_x(pose.x());
  point.set_y(pose.y());
  point.set_z(pose.z());
  return point;
}

bool IsValidAgentProto(const pb::Agent& agent_proto) {
  const auto& predicted_trajectories = agent_proto.predicted_trajectories();
  const int num_output_trajs = std::count_if(
      predicted_trajectories.begin(), predicted_trajectories.end(),
      [](const pb::PredictedTrajectory& traj) {
        return traj.is_output_trajectory();
      });

  bool are_all_trajs_valid = true;
  for (const auto& traj : predicted_trajectories) {
    // Check the consistency between the output trajectories and multi-output
    // trajectories. Once a trajectory is selected as an output trajectory, it
    // should also be selected as a multi-output trajectory.
    if (traj.is_output_trajectory() && !traj.is_multi_output_trajectory()) {
      LOG(ERROR) << voy::perception::ObjectType_Name(
                        agent_proto.tracked_object().object_type())
                 << " " << agent_proto.tracked_object().id()
                 << " has an invalid trajectory " << traj.id()
                 << ", which is an output but not multi-output: "
                 << traj.DebugString();
      are_all_trajs_valid = false;
    }

    // Check start-to-move trajectory.
    if (traj.id() == constants::kVehicleStartToMoveTrajectoryId &&
        traj.is_multi_output_trajectory()) {
      LOG(ERROR) << voy::perception::ObjectType_Name(
                        agent_proto.tracked_object().object_type())
                 << " " << agent_proto.tracked_object().id()
                 << " has an invalid StartToMove trajectory " << traj.id()
                 << ", which is a multi-output.";
      are_all_trajs_valid = false;
    }

    // Check fields of each pose.
    for (const auto& pose : traj.traj_poses()) {
      if (pose.speed() < 0.0 || pose.odom() < 0.0) {
        LOG(ERROR) << voy::perception::ObjectType_Name(
                          agent_proto.tracked_object().object_type())
                   << " " << agent_proto.tracked_object().id()
                   << " has an invalid trajectory " << traj.id()
                   << ", which has negative speed or odom: "
                   << pose.DebugString();
        are_all_trajs_valid = false;
      }
    }

    if (traj.traj_poses_size() != constants::kPredictionHorizonInPoses + 1) {
      LOG(ERROR) << voy::perception::ObjectType_Name(
                        agent_proto.tracked_object().object_type())
                 << " " << agent_proto.tracked_object().id()
                 << " has an invalid trajectory " << traj.id()
                 << ", which has incorrect number of poses ("
                 << " actual " << traj.traj_poses_size() << ", expected "
                 << constants::kPredictionHorizonInPoses + 1 << ")";
      are_all_trajs_valid = false;
    }

    // Check if the pose uncertainty is correctly set, we expect none or all
    // poses in one trajectory have uncertainty field set.
    if (traj.traj_poses(0).has_uncertainty()) {
      if (!std::all_of(
              traj.traj_poses().begin(), traj.traj_poses().end(),
              [](const auto& pose) { return pose.has_uncertainty(); })) {
        LOG(ERROR) << voy::perception::ObjectType_Name(
                          agent_proto.tracked_object().object_type())
                   << " " << agent_proto.tracked_object().id()
                   << " has an invalid trajectory " << traj.id()
                   << ", which does not have pose uncertainty for all poses.";
        are_all_trajs_valid = false;
      }
    }
  }
  const bool is_num_output_valid =
      num_output_trajs > 0 &&
      num_output_trajs <= constants::kMaxOutputTrajectoryCountPerAgent;
  LOG_IF(ERROR, !is_num_output_valid)
      << voy::perception::ObjectType_Name(
             agent_proto.tracked_object().object_type())
      << " " << agent_proto.tracked_object().id()
      << " num trajectories:" << agent_proto.predicted_trajectories_size()
      << " has an invalid num output trajectory:" << num_output_trajs;

  return is_num_output_valid && are_all_trajs_valid;
}

int GetPrimaryTrajectoryIndex(const pb::Agent& agent_proto) {
  const int num_trajectories = agent_proto.predicted_trajectories_size();
  CHECK_GT(num_trajectories, 0)
      << "No predicted trajectories for agent: " << agent_proto.DebugString();

  // NOTE(xiongwei): Old bags before year 2019 have only one trajectory (with
  // no field `is_output_trajectory`), so we need special handling here.
  if (num_trajectories == constants::kMinTrajectorySize) {
    return 0;
  }

  std::vector<int> output_indices;
  for (int i = 0; i < num_trajectories; ++i) {
    if (agent_proto.predicted_trajectories(i).is_output_trajectory()) {
      output_indices.push_back(i);
    }
  }

  CHECK(!output_indices.empty())
      << "No output trajectories for agent: " << agent_proto.DebugString();

  std::sort(
      output_indices.begin(), output_indices.end(),
      [&agent_proto](int i, int j) {
        return agent_proto.predicted_trajectories(i).probability_in_ppm() >
               agent_proto.predicted_trajectories(j).probability_in_ppm();
      });

  return *std::find_if(
      output_indices.begin(), output_indices.end(), [&agent_proto](int i) {
        return agent_proto.predicted_trajectories(i).backup_trajectory_type() ==
               pb::BackupTrajectoryType::NOT_BACKUP;
      });
}

int GetPrimaryIgnoreEgoTrajectoryIndex(const pb::Agent& agent_proto) {
  if (!agent_proto.has_ignore_ego_predictions()) {
    return -1;
  }
  const int num_trajectories = agent_proto.predicted_trajectories_size();
  CHECK_GT(num_trajectories, 0)
      << "No predicted trajectories for agent: " << agent_proto.DebugString();

  int primary_index = -1;
  int max_probability_in_ppm = -1;
  for (int i = 0; i < num_trajectories; ++i) {
    const pb::PredictedTrajectory trajectory =
        agent_proto.predicted_trajectories(i);

    if (trajectory.is_ignore_ego_trajectory()) {
      const int probability_in_ppm = trajectory.probability_in_ppm();
      if (probability_in_ppm > max_probability_in_ppm) {
        primary_index = i;
        max_probability_in_ppm = probability_in_ppm;
      }
    }
  }
  return primary_index;
}

std::vector<int> GetOutputTrajectoryIndexesSortedByProbability(
    const pb::Agent& agent_proto) {
  std::vector<std::pair<const pb::PredictedTrajectory*, int>>
      output_trajectory_and_index_vec;
  for (int index = 0; index < agent_proto.predicted_trajectories_size();
       ++index) {
    const pb::PredictedTrajectory& trajectory =
        agent_proto.predicted_trajectories(index);
    if (trajectory.is_output_trajectory()) {
      output_trajectory_and_index_vec.emplace_back(&trajectory, index);
    }
  }
  DCHECK_GT(output_trajectory_and_index_vec.size(), 0)
      << "There is no output trajectory for "
      << voy::perception::ObjectType_Name(
             agent_proto.tracked_object().object_type())
      << " " << agent_proto.tracked_object().id();
  std::sort(output_trajectory_and_index_vec.begin(),
            output_trajectory_and_index_vec.end(),
            [](const auto& pair_1, const auto& pair_2) {
              // Make sure the output trajectories are sorted in descending
              // order.
              const int not_backup_flag_1 =
                  static_cast<int>(pair_1.first->backup_trajectory_type() ==
                                   pb::BackupTrajectoryType::NOT_BACKUP);
              const int not_backup_flag_2 =
                  static_cast<int>(pair_2.first->backup_trajectory_type() ==
                                   pb::BackupTrajectoryType::NOT_BACKUP);
              if (not_backup_flag_1 != not_backup_flag_2) {
                return not_backup_flag_1 > not_backup_flag_2;
              }
              return pair_1.first->probability_in_ppm() >
                     pair_2.first->probability_in_ppm();
            });
  std::vector<int> ordered_indexes;
  ordered_indexes.reserve(output_trajectory_and_index_vec.size());
  std::transform(output_trajectory_and_index_vec.begin(),
                 output_trajectory_and_index_vec.end(),
                 std::back_inserter(ordered_indexes),
                 [](const auto& pair) { return pair.second; });
  return ordered_indexes;
}

std::vector<int> GetMultiOutputTrajectoryIndexesSortedByProbability(
    const pb::Agent& agent_proto) {
  const int num_trajectories = agent_proto.predicted_trajectories_size();
  CHECK_GT(num_trajectories, 0)
      << "No predicted trajectories for agent: " << agent_proto.DebugString();

  // NOTE(xiongwei): Old bags before year 2019 have only one trajectory (with
  // no field `is_output_trajectory`), so we need special handling here.
  if (num_trajectories == constants::kMinTrajectorySize) {
    return {0};
  }
  std::vector<std::pair<const pb::PredictedTrajectory*, int>>
      output_trajectory_and_index_vec;
  output_trajectory_and_index_vec.reserve(num_trajectories);
  for (int index = 0; index < agent_proto.predicted_trajectories_size();
       ++index) {
    const pb::PredictedTrajectory& trajectory =
        agent_proto.predicted_trajectories(index);
    if (trajectory.is_multi_output_trajectory()) {
      output_trajectory_and_index_vec.emplace_back(&trajectory, index);
    }
  }

  std::sort(output_trajectory_and_index_vec.begin(),
            output_trajectory_and_index_vec.end(),
            [](const auto& pair_1, const auto& pair_2) {
              return pair_1.first->probability_in_ppm() >
                     pair_2.first->probability_in_ppm();
            });
  std::vector<int> ordered_indexes;
  ordered_indexes.reserve(output_trajectory_and_index_vec.size());
  std::transform(output_trajectory_and_index_vec.begin(),
                 output_trajectory_and_index_vec.end(),
                 std::back_inserter(ordered_indexes),
                 [](const auto& pair) { return pair.second; });
  return ordered_indexes;
}

std::vector<planner::pb::TrajectoryPose> GetTrajectoryWithinHorizon(
    const std::vector<planner::pb::TrajectoryPose>& trajectory,
    int64_t max_horizon) {
  DCHECK_GE(max_horizon, 0);
  const int64_t horizon_boundary = trajectory.front().timestamp() + max_horizon;
  int pose_num_within_horizon = 0;
  for (const planner::pb::TrajectoryPose& pose : trajectory) {
    if (pose.timestamp() > horizon_boundary) {
      break;
    }
    ++pose_num_within_horizon;
  }
  return std::vector<planner::pb::TrajectoryPose>(
      trajectory.begin(), trajectory.begin() + pose_num_within_horizon);
}

planner::pb::TrajectoryPose InterpolatePoseFromTrackedObject(
    const voy::TrackedObject& before_obj, const voy::TrackedObject& after_obj,
    double ratio, int64_t timestamp) {
  planner::pb::TrajectoryPose pose;
  pose.set_timestamp(timestamp);
  pose.set_x_pos(math::LinearInterpolate(before_obj.center_x(),
                                         after_obj.center_x(), ratio));
  pose.set_y_pos(math::LinearInterpolate(before_obj.center_y(),
                                         after_obj.center_y(), ratio));
  pose.set_z_pos(math::LinearInterpolate(before_obj.center_z(),
                                         after_obj.center_z(), ratio));
  pose.set_heading(math::LinearInterpolate(before_obj.heading(),
                                           after_obj.heading(), ratio));
  pose.set_speed(math::LinearInterpolate(before_obj.velocity(),
                                         after_obj.velocity(), ratio));
  pose.set_accel(math::LinearInterpolate(before_obj.acceleration(),
                                         after_obj.acceleration(), ratio));
  return pose;
}

planner::pb::TrajectoryPose InterpolatePoseFromAgentStates(
    const AgentState& before_state, const AgentState& after_state, double ratio,
    int64_t timestamp) {
  const voy::TrackedObject& before_obj = before_state.tracked_object();
  const voy::TrackedObject& after_obj = after_state.tracked_object();

  planner::pb::TrajectoryPose pose;
  pose.set_timestamp(timestamp);
  pose.set_x_pos(math::LinearInterpolate(before_obj.center_x(),
                                         after_obj.center_x(), ratio));
  pose.set_y_pos(math::LinearInterpolate(before_obj.center_y(),
                                         after_obj.center_y(), ratio));
  pose.set_z_pos(math::LinearInterpolate(before_obj.center_z(),
                                         after_obj.center_z(), ratio));
  pose.set_heading(math::LinearInterpolate(before_obj.heading(),
                                           after_obj.heading(), ratio));
  pose.set_speed(math::LinearInterpolate(before_obj.velocity(),
                                         after_obj.velocity(), ratio));
  pose.set_accel(math::LinearInterpolate(before_obj.acceleration(),
                                         after_obj.acceleration(), ratio));
  // Reuse |steering_wheel_angle| to store |vehicle_signals| of after state.
  // 1 means left turning signal detected on
  // -1 means right turning signal detected on
  // 0 means no turing signal detected on
  // TODO(Jian): Define a class to replace |planner::pb::TrajectoryPose| to
  // better store fields like |vehicle_signals|.
  if (after_state.vehicle_signals().left_turn_status ==
      VehicleSignalStatus::ON) {
    pose.set_steering_wheel_angle(1);
  } else if (after_state.vehicle_signals().right_turn_status ==
             VehicleSignalStatus::ON) {
    pose.set_steering_wheel_angle(-1);
  }
  return pose;
}

planner::pb::TrajectoryPose InterpolatePoseFromTrackState(
    const voy::TrackState& before_state, const voy::TrackState& after_state,
    int64_t timestamp) {
  planner::pb::TrajectoryPose pose;
  pose.set_timestamp(timestamp);
  const double ratio = math::GetInterpolationRatio(
      before_state.timestamp(), after_state.timestamp(), timestamp);
  pose.set_x_pos(math::LinearInterpolate(before_state.center_x(),
                                         after_state.center_x(), ratio));
  pose.set_y_pos(math::LinearInterpolate(before_state.center_y(),
                                         after_state.center_y(), ratio));
  pose.set_z_pos(math::LinearInterpolate(before_state.center_z(),
                                         after_state.center_z(), ratio));
  pose.set_heading(math::LinearInterpolate(before_state.heading(),
                                           after_state.heading(), ratio));
  pose.set_speed(math::LinearInterpolate(before_state.speed(),
                                         after_state.speed(), ratio));

  return pose;
}

std::vector<math::geometry::Point2d> GetPoint2dFromTrajectoryPose(
    const std::vector<planner::pb::TrajectoryPose>& traj_poses) {
  std::vector<math::geometry::Point2d> points;
  for (const auto& pose : traj_poses) {
    const math::geometry::Point2d cur_point(pose.x_pos(), pose.y_pos());
    // Ignore empty and duplicated points.
    if (points.empty() ||
        math::geometry::Length(math::geometry::Subtract(
            cur_point, points.back())) > math::constants::kEpsilon) {
      points.push_back(cur_point);
    }
  }
  return points;
}

math::geometry::PolylineCurve2d FitTrajectoryCurve(
    const std::vector<planner::pb::TrajectoryPose>& traj_poses) {
  // Get 2d locations of predicted trajectory's poses.
  auto points = GetPoint2dFromTrajectoryPose(traj_poses);
  // Generate ref line if there are enough points. Otherwise return nullptr.
  if (points.size() < kMinLinePointNum) {
    // It happens when an agent is still and its prediction is stop.
    return {};
  }

  return math::geometry::PolylineCurve2d{std::move(points)};
}

int GetPoseNum(int64_t prediction_horizon_in_ms) {
  return prediction_horizon_in_ms / constants::kPoseIntervalInMs + 1;
}

planner::pb::TrajectoryPose ToPlanningTrajectoryPoseWithTimestamp(
    int64_t timestamp, const voy::TrackedObject& tracked_object) {
  planner::pb::TrajectoryPose traj_pose;
  traj_pose.set_timestamp(timestamp);
  traj_pose.set_x_pos(tracked_object.center_x());
  traj_pose.set_y_pos(tracked_object.center_y());
  traj_pose.set_z_pos(tracked_object.center_z());
  traj_pose.set_heading(tracked_object.heading());
  traj_pose.set_speed(tracked_object.velocity());
  return traj_pose;
}

bool IsCloserToEndPointOfLine(const math::geometry::Point2d& query_point,
                              const math::geometry::PolylineCurve2d& line) {
  const double dist_to_line_start_sq =
      math::geometry::ComparableDistance(line.polyline().front(), query_point);
  const double dist_to_line_end_sq =
      math::geometry::ComparableDistance(line.polyline().back(), query_point);
  return dist_to_line_start_sq > dist_to_line_end_sq;
}

bool IsInterestedObjectTypeForPrediction(
    voy::perception::ObjectType object_type) {
  return object_type == voy::perception::ObjectType::VEHICLE ||
         object_type == voy::perception::ObjectType::CYCLIST ||
         object_type == voy::perception::ObjectType::PED ||
         object_type == voy::perception::ObjectType::UNKNOWN;
}

bool IsStationaryObject(
    const voy::TrackedObject& tracked_object, int64_t current_timestamp,
    const av_comm::HistoryBuffer<int64_t, AgentState>& history_states) {
  // The velocity should not be negative.
  CHECK_GE(tracked_object.velocity(), 0);

  switch (tracked_object.object_type()) {
    case voy::perception::VEHICLE:
    case voy::perception::ObjectType::CYCLIST: {
      // Parked/Maybe Parked vehicle will be considered stationary if its
      // velocity less than
      // |constants::kVelocityConsideredAsStationaryParkedVeh|
#pragma GCC diagnostic push
      // voy::perception::Attribute::MAYBE_PARKED_CAR is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
      if (tracked_object.object_type() ==
              voy::perception::ObjectType::VEHICLE &&
          std::any_of(
              tracked_object.attributes().begin(),
              tracked_object.attributes().end(), [](const auto& attribute) {
                return attribute == voy::perception::Attribute::PARKED_CAR ||
                       attribute ==
                           voy::perception::Attribute::MAYBE_PARKED_CAR;
              })) {
        return tracked_object.velocity() <
               constants::kMaxVelocityConsideredAsStationaryParkedVeh;
      }
#pragma GCC diagnostic pop

      return tracked_object.velocity() <
                 constants::kMaxVelocityConsideredAsStationary &&
             std::abs(tracked_object.omega()) <
                 constants::kMaxAngularVelocityConsideredAsStationary;
    }

    case voy::perception::ObjectType::PED: {
      double velocity_consider_as_stationary =
          constants::kMaxVelocityConsideredAsStationary;
      if (!history_states.empty() &&
          current_timestamp != constants::kInvalidTimestamp) {
        int pre_frame_cnt = 0;
        // Make sure the considered previous frame being within the range of
        // (|current_timestamp - 600ms|, current_timestamp] to avoid the
        // tracking missing.
        const int64_t earliest_history_timestamp_to_consider =
            current_timestamp - (kConsiderPreviousFrames + 1) *
                                    constants::kMinTrackingMessageIntervalInMs;
        for (auto it = history_states.rbegin();
             velocity_consider_as_stationary > 0.0 &&
             pre_frame_cnt < kConsiderPreviousFrames &&
             it != history_states.rend() &&
             it->first > earliest_history_timestamp_to_consider;
             ++it, ++pre_frame_cnt) {
          if (it->second.tracked_object().velocity() >
              constants::kMaxVelocityConsideredAsStationary) {
            velocity_consider_as_stationary =
                std::max(0.0, velocity_consider_as_stationary -
                                  kStationaryVelocityDecayBuffer);
          }
        }
      }
      return tracked_object.velocity() <= velocity_consider_as_stationary;
    }

    case voy::perception::ObjectType::UNKNOWN:
      if (std::find(tracked_object.attributes().begin(),
                    tracked_object.attributes().end(),
                    voy::perception::Attribute::CYC_WITHOUT_PERSON_ATTR) !=
          tracked_object.attributes().end()) {
        return tracked_object.velocity() <=
               kMaxStationaryVelocityForCyclistWithoutPerson;
      }

      return tracked_object.velocity() <=
             constants::kMaxVelocityConsideredAsStationary;

    case voy::perception::ObjectType::TRAFFIC_CONE:
    case voy::perception::ObjectType::BARRIER:
      return true;

    default:
      DCHECK(false) << "Unexpected object type for "
                    << tracked_object.DebugString();
      return false;
  }

  DCHECK(false) << "Should not reach here! " << tracked_object.DebugString();
  return false;
}

voy::TrackedObject CreateEgoTrackedObject(const voy::Pose& pose,
                                          int64_t current_timestamp,
                                          double length, double width,
                                          double height,
                                          voy::LightSignal ego_light_signal) {
  voy::TrackedObject robot_obj;
  // Construct robot tracked object by pose.
  robot_obj.mutable_sync_state()->set_timestamp(current_timestamp);
  robot_obj.set_id(constants::kRobotObjId);
  robot_obj.set_object_type(voy::perception::ObjectType::VEHICLE);
  robot_obj.set_center_x(pose.x());
  robot_obj.set_center_y(pose.y());
  robot_obj.set_center_z(pose.z());
  robot_obj.set_box_heading(pose.yaw());
  // TODO(ZhangShuai): Use steer in canbus information and kinematic model to
  // infer ego motion heading.
  robot_obj.set_heading(pose.yaw());
  // Exactly set heading var for a remainder of vectornet feature extraction.
  robot_obj.mutable_sync_state()->set_heading_var(0.0);

  robot_obj.set_omega(pose.vel_yaw());

  robot_obj.set_length(length);
  robot_obj.set_width(width);
  robot_obj.set_height(height);

  const double velocity = std::hypot(pose.vel_x(), pose.vel_y());
  robot_obj.set_velocity(velocity);
  // Exactly set speed var for a remainder of vectornet feature extraction.
  robot_obj.mutable_sync_state()->set_speed_var(0.0);
  robot_obj.set_acceleration(pose.acc_forward());

  // Add light signals for ego tracked object.
  // These light signals will be converted to vehicle_signals in agent_state.
  const std::vector<voy::perception::Attribute> turn_signals =
      ConvertEgoLightSignalToVehicleTurnSignal(ego_light_signal);
  for (const auto turn_signal : turn_signals) {
    robot_obj.add_attributes(turn_signal);
  }

  return robot_obj;
}

bool CanPointProjectToCurveWithoutExtension(
    const math::geometry::Point2d& point,
    const math::geometry::PolylineCurve2d& curve) {
  const math::ProximityQueryInfo proximity =
      curve.GetProximity(point, math::pb::UseExtensionFlag::kForbid);
  // Early return if the proximity point is not start point or end point.
  if (proximity.arc_length > 0.0 &&
      proximity.arc_length < curve.GetTotalArcLength()) {
    return true;
  }

  const math::geometry::Point2d project_direction = {point.x() - proximity.x,
                                                     point.y() - proximity.y};
  const math::geometry::Point2d curve_unit_direction =
      curve.GetInterpDeriv(proximity.arc_length);
  // NOTE(lipei): If a point can be projected to the curve without extension,
  // then its projection direction should be perpendicular to the curve unit
  // direction.
  return math::NearZero(
      math::geometry::DotProduct(project_direction, curve_unit_direction));
}

bool CanPointProjectToCurveWithinStartExtension(
    const math::geometry::Point2d& point,
    const math::geometry::PolylineCurve2d& curve, double extension_length) {
  const math::ProximityQueryInfo proximity =
      curve.GetProximity(point, math::pb::UseExtensionFlag::kAllow);
  return proximity.arc_length > -extension_length &&
         proximity.arc_length < curve.GetTotalArcLength();
}

math::geometry::PolygonWithCache2d MakeBgPolygon2d(
    const voy::TrackedObject& tracked_object) {
  const math::geometry::OrientedBox2d object_box{
      tracked_object.center_x(), tracked_object.center_y(),
      tracked_object.length(), tracked_object.width(),
      tracked_object.heading()};
  return object_box.ToPolygonWithCache2d();
}

bool IsObjectAheadEgoFrontBumper(double arc_dist_ego_center,
                                 double ego_car_length) {
  return arc_dist_ego_center - ego_car_length * 0.5 >= 0.0;
}

bool IsObjectAheadEgoRearBumper(double arc_dist_ego_center,
                                double ego_car_length) {
  return arc_dist_ego_center + ego_car_length * 0.5 >= 0.0;
}

std::vector<int64_t> GetLaneIdsFromClickedPoint(
    const hdmap::HdMap& hdmap, const hdmap::Point& clicked_point) {
  const hdmap::GeoInfo geo_info = hdmap.GetGeoInfo(
      clicked_point, kSearchMapTargetRadius, /*need_closest_lanes=*/true);
  std::vector<int64_t> lane_ids;
  for (const auto& point_to_road : geo_info.roads()) {
    // roads are sorted based on distance.
    if (point_to_road.dist_to_road() >= 0.0) {
      break;
    }
    for (const auto& close_lane : point_to_road.closest_lanes()) {
      if (close_lane.dist_to_lane() >= 0.0) {
        continue;
      }
      lane_ids.push_back(close_lane.lane_id());
    }
  }
  return lane_ids;
}

std::vector<const hdmap::Crosswalk*> GetCrosswalksFromClickedPoint(
    const hdmap::HdMap& hdmap, const hdmap::Point& clicked_point) {
  const std::vector<std::pair<double, const hdmap::Crosswalk*>>
      distance_and_crosswalk_pairs = hdmap.GetCrosswalksWithDistance(
          clicked_point, kSearchMapTargetRadius);
  std::vector<const hdmap::Crosswalk*> crosswalks;
  for (const auto& distance_and_crosswalk_pair : distance_and_crosswalk_pairs) {
    const double distance = distance_and_crosswalk_pair.first;
    if (distance >= 0.0) {
      continue;
    }
    crosswalks.push_back(distance_and_crosswalk_pair.second);
  }
  return crosswalks;
}

std::vector<const hdmap::Zone*> GetExitZonesFromClickedPoint(
    const hdmap::HdMap& hdmap, const hdmap::Point& clicked_point) {
  const std::vector<std::pair<double, const hdmap::Zone*>>
      distance_and_zone_pairs =
          hdmap.GetZonesWithDistance(clicked_point, kSearchMapTargetRadius);
  std::vector<const hdmap::Zone*> exit_zones;
  for (const auto& distance_and_zone_pair : distance_and_zone_pairs) {
    const double distance = distance_and_zone_pair.first;
    const hdmap::Zone* zone = distance_and_zone_pair.second;
    if (distance >= 0.0 || zone->type() != hdmap::Zone::ROAD_EXIT) {
      continue;
    }
    exit_zones.push_back(zone);
  }
  return exit_zones;
}

int64_t GetMapTargetIdFromClickedPoint(
    const hdmap::HdMap& hdmap, const hdmap::Point& clicked_point,
    pb::MapObject::MapObjectType map_target_type) {
  switch (map_target_type) {
    case pb::MapObject::LANE: {
      std::vector<int64_t> lane_ids =
          GetLaneIdsFromClickedPoint(hdmap, clicked_point);
      return lane_ids.size() == 1 ? lane_ids.front() : -1;
    }
    case pb::MapObject::CROSSWALK: {
      std::vector<const hdmap::Crosswalk*> crosswalks =
          GetCrosswalksFromClickedPoint(hdmap, clicked_point);
      return crosswalks.size() == 1 ? crosswalks.front()->id() : -1;
    }
    case pb::MapObject::EXIT_ZONE: {
      std::vector<const hdmap::Zone*> exit_zones =
          GetExitZonesFromClickedPoint(hdmap, clicked_point);
      return exit_zones.size() == 1 ? exit_zones.front()->id() : -1;
    }
    case pb::MapObject::UNKNOWN_TYPE:
      DCHECK(false) << "map target type should not be UNKNOWN_TYPE.";
      return -1;
    default:
      DCHECK(false) << "Unexpected map target type "
                    << pb::MapObject::MapObjectType_Name(map_target_type)
                    << ".";
      return -1;
  }
  return -1;
}

double GetOdometer(const std::vector<planner::pb::TrajectoryPose>& trajectory) {
  double odometer = 0.0;
  for (size_t idx = 1; idx < trajectory.size(); idx++) {
    const planner::pb::TrajectoryPose& prev_pose = trajectory[idx - 1];
    const planner::pb::TrajectoryPose& curr_pose = trajectory[idx];
    odometer += std::hypot(curr_pose.x_pos() - prev_pose.x_pos(),
                           curr_pose.y_pos() - prev_pose.y_pos());
  }
  return odometer;
}

double GetPrimaryTrajectoryOdometer(const pb::Agent& agent) {
  const int primary_trajectory_index = GetPrimaryTrajectoryIndex(agent);
  const pb::PredictedTrajectory& primary_trajectory =
      agent.predicted_trajectories(primary_trajectory_index);
  return GetOdometer({primary_trajectory.traj_poses().begin(),
                      primary_trajectory.traj_poses().end()});
}

int GetMaxCoverageAgentIndex(
    const pb::AgentList& agent_list,
    const math::geometry::PolygonWithCache2d& target_object_area) {
  int found_idx = -1;
  double max_area = -1.0;
  for (int idx = 0; idx < agent_list.agent_list_size(); idx++) {
    const pb::Agent& agent = agent_list.agent_list(idx);
    const math::geometry::PolygonWithCache2d obj_polygon =
        MakeBgPolygon2d(agent.tracked_object());
    // Check for intersection between two Polygons
    if (math::geometry::Intersects(target_object_area, obj_polygon)) {
      std::deque<math::geometry::Polygon2d> intersections;
      math::geometry::Intersection(target_object_area, obj_polygon,
                                   intersections);
      double area = 0;
      for (const auto& intersection : intersections) {
        area += math::geometry::Area(intersection);
      }

      if (area > max_area) {
        found_idx = idx;
        max_area = area;
      }
    }
  }
  return found_idx;
}

double GetArcPositionOfObject(
    const TrackedObjectProximityOnLane& object_proximity_on_lane,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  DCHECK(!lane_sequence.empty());
  DCHECK(object_proximity_on_lane.lane());
  DCHECK_GE(object_proximity_on_lane.lng_lane_position(), 0.0);
  DCHECK(std::any_of(lane_sequence.begin(), lane_sequence.end(),
                     [&object_proximity_on_lane](const pnc_map::Lane* lane) {
                       return lane->id() ==
                              object_proximity_on_lane.lane()->id();
                     }))
      << "Object lane " << object_proximity_on_lane.lane()->id()
      << " is not on the ego lane sequence.";

  double object_arc_position = 0.0;
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (lane->id() == object_proximity_on_lane.lane()->id()) {
      object_arc_position += object_proximity_on_lane.lng_lane_position();
      break;
    }
    object_arc_position += lane->length();
  }
  DCHECK_GE(object_arc_position, 0.0);
  return object_arc_position;
}

bool IsV2xObject(const voy::TrackedObject& tracked_object) {
  return std::any_of(
      tracked_object.attributes().begin(), tracked_object.attributes().end(),
      [](int attr) { return attr == voy::perception::CONVERTED_V2X_OBJECT; });
}

bool IsSustainedTrackedObject(const voy::TrackedObject& tracked_object) {
  return std::any_of(
      tracked_object.attributes().begin(), tracked_object.attributes().end(),
      [](int attr) { return attr == voy::perception::SUSTAINED; });
}

bool IsEarlyPublishedTrackedObject(const voy::TrackedObject& tracked_object) {
  return std::any_of(
      tracked_object.attributes().begin(), tracked_object.attributes().end(),
      [](int attr) { return attr == voy::perception::EARLY_PUBLISHED; });
}

bool IsHighlyUncertainTrackedObject(const voy::TrackedObject& tracked_object) {
  return IsSustainedTrackedObject(tracked_object) ||
         IsEarlyPublishedTrackedObject(tracked_object);
}

bool IsInvalidTrackedObject(const voy::TrackedObject& tracked_object) {
  return IsV2xObject(tracked_object);
}

math::geometry::Polyline2d GetLinestringPoints(
    const math::geometry::Point2d& start_point, double heading, double length,
    bool is_reversed_heading_direction) {
  // If we want to get the distance behind the object, the heading_coefficient
  // should be -1.
  double heading_coefficient = is_reversed_heading_direction ? -1.0 : 1.0;
  const double delta_x = length * std::cos(heading) * heading_coefficient;
  const double delta_y = length * std::sin(heading) * heading_coefficient;
  return {start_point, {start_point.x() + delta_x, start_point.y() + delta_y}};
}

TrajectoryPoseHistoryBuffer GenerateHistoryTrajectory(
    const av_comm::HistoryBuffer<int64_t, const voy::TrackedObject*>&
        tracked_object_history,
    int64_t horizon, int64_t interval) {
  DCHECK_EQ(horizon % interval, 0);
  const int64_t num_poses = horizon / interval + 1;
  TrajectoryPoseHistoryBuffer trajectory(/*size=*/num_poses);

  const int64_t end_timestamp = tracked_object_history.latest_timestamp();
  const int64_t start_timestamp = end_timestamp - horizon;

  for (int64_t cur_timestamp = start_timestamp; cur_timestamp <= end_timestamp;
       cur_timestamp += interval) {
    const auto& before_iter =
        tracked_object_history.FindClosestDataBeforeEqual(cur_timestamp);
    const auto& after_iter =
        tracked_object_history.FindClosestDataEqualAfter(cur_timestamp);
    if (before_iter != tracked_object_history.end() &&
        after_iter != tracked_object_history.end()) {
      const double ratio = math::GetInterpolationRatio(
          before_iter->first, after_iter->first, cur_timestamp);
      planner::pb::TrajectoryPose cur_pose =
          utility::InterpolatePoseFromTrackedObject(
              *before_iter->second, *after_iter->second, ratio, cur_timestamp);

      // Populate acceleration field.
      if (!trajectory.empty()) {
        const planner::pb::TrajectoryPose& prev_pose = trajectory.latest_data();
        const double accel =
            (cur_pose.speed() - prev_pose.speed()) /
            math::Ms2Sec(cur_pose.timestamp() - prev_pose.timestamp());
        cur_pose.set_accel(accel);
      }
      trajectory.EmplaceBack(cur_pose.timestamp(), cur_pose);
    }
  }

  return trajectory;
}

int ProbabilityToPartsPerMillion(double probability) {
  DCHECK_GE(probability, 0.0);
  DCHECK_LE(probability, 1.0);
  return static_cast<int>(probability * constants::kPredictionProbabilitySum);
}

double PartsPerMillionToProbability(int parts_per_million) {
  DCHECK_GE(parts_per_million, 0);
  DCHECK_LE(parts_per_million, constants::kPredictionProbabilitySum);
  return static_cast<double>(parts_per_million) /
         constants::kPredictionProbabilitySum;
}

void ComputeAndSetHeadings(std::vector<planner::pb::TrajectoryPose>* poses,
                           voy::perception::ObjectType object_type,
                           bool is_set_box_heading) {
  DCHECK(poses);
  DCHECK_GE(poses->size(), 2);
  const double max_distance_of_close_points =
      object_type == voy::perception::ObjectType::CYCLIST
          ? kMaxDistanceOfClosePointsForCyc
          : kMaxDistanceOfClosePointsForVehAndPed;
  for (size_t i = 1; i < poses->size(); ++i) {
    // Use central difference to better approximate the derivative.
    // Use backward difference for last pose.
    const int next_index = (i + 1 < poses->size()) ? i + 1 : i;
    const double delta_x =
        (*poses)[next_index].x_pos() - (*poses)[i - 1].x_pos();
    const double delta_y =
        (*poses)[next_index].y_pos() - (*poses)[i - 1].y_pos();
    // In the range [-\pi, \pi] radians
    const double heading = std::atan2(delta_y, delta_x);

    bool use_previouse_heading = false;
    if (math::NearZero(delta_x, max_distance_of_close_points) &&
        math::NearZero(delta_y, max_distance_of_close_points)) {
      // Use heading of previous pose if current pose is very close.
      use_previouse_heading = true;
    } else if (object_type == voy::perception::ObjectType::VEHICLE &&
               is_set_box_heading &&
               std::abs(math::NormalizeMinusPiToPi(
                   heading - (*poses)[i - 1].heading())) > M_PI_2) {
      // Use heading of previous pose if current motion heading flipped.
      use_previouse_heading = true;
    }

    if (use_previouse_heading) {
      (*poses)[i].set_heading((*poses)[i - 1].heading());
    } else {
      (*poses)[i].set_heading(heading);
    }
  }
}

void ComputeAndSetSpeed(std::vector<planner::pb::TrajectoryPose>* poses) {
  DCHECK(poses);
  DCHECK_GE(poses->size(), 2);

  for (size_t i = 1; i < poses->size(); ++i) {
    // Use central difference to better approximate the derivative and use
    // backward difference for last pose.
    const int next_index = i + 1 < poses->size() ? i + 1 : i;
    const double delta_x =
        (*poses)[next_index].x_pos() - (*poses)[i - 1].x_pos();
    const double delta_y =
        (*poses)[next_index].y_pos() - (*poses)[i - 1].y_pos();

    (*poses)[i].set_speed(std::hypot(delta_x, delta_y) /
                          constants::kPoseIntervalInSec / (next_index - i + 1));
  }
}

void ComputeAndSetAccel(std::vector<planner::pb::TrajectoryPose>* poses) {
  DCHECK(poses);
  DCHECK_GE(poses->size(), 2);

  for (size_t i = 1; i < poses->size(); ++i) {
    // Use central difference to better approximate the derivative and use
    // backward difference for last pose.
    const int next_index = (i + 1 < poses->size()) ? i + 1 : i;
    const double delta_v =
        (*poses)[next_index].speed() - (*poses)[i - 1].speed();
    (*poses)[i].set_accel(delta_v / constants::kPoseIntervalInSec /
                          (next_index - i + 1));
  }
}

bool IsAutonomousMiniCar(const voy::TrackedObject& object) {
  const bool is_vehicle =
      object.object_type() == voy::perception::ObjectType::VEHICLE;
  return is_vehicle &&
         std::any_of(object.attributes().begin(), object.attributes().end(),
                     [](const auto& attribute) {
                       return attribute ==
                              voy::perception::Attribute::
                                  VEHICLE_AUTONOMOUS_DELIVERY_MINICAR;
                     });
}

bool IsNormalSizedVehicle(const voy::TrackedObject& object) {
  if (object.object_type() != voy::perception::ObjectType::VEHICLE) {
    return false;
  }

  const double agent_length = object.length();
  const double agent_width = object.width();

  return agent_length >= kVehicleLengthLowerBound &&
         agent_length <= kVehicleLengthUpperBound &&
         agent_width >= kVehicleWidthLowerBound &&
         agent_width <= kVehicleWidthUpperBound;
}

bool IsStationaryTrajectory(
    const std::vector<planner::pb::TrajectoryPose>& traj_poses,
    int end_pose_index) {
  constexpr double kMaxAvgSpeedConsideredAsStationary = 0.15;       // m/s
  constexpr double kMaxMovingDistanceConsideredAsStationary = 1.0;  // m

  DCHECK_GT(traj_poses.size(), end_pose_index)
      << "Trajectory poses are not correctly set.";
  DCHECK_GT(end_pose_index, 0) << "end_pose_index should be greater than 0.";
  const double horizon =
      math::Ms2Sec(end_pose_index * constants::kPoseIntervalInMs);
  const double avg_speed = traj_poses.at(end_pose_index).odom() / horizon;
  if (avg_speed > kMaxAvgSpeedConsideredAsStationary) {
    return false;
  }
  const double moving_distance = std::hypot(
      traj_poses.at(0).x_pos() - traj_poses.at(end_pose_index).x_pos(),
      traj_poses.at(0).y_pos() - traj_poses.at(end_pose_index).y_pos());
  return moving_distance <= kMaxMovingDistanceConsideredAsStationary;
}

bool IsTractor(const voy::TrackedObject& object) {
  const bool is_vehicle =
      object.object_type() == voy::perception::ObjectType::VEHICLE;
  return is_vehicle && object.associated_vehicle_tail_id() !=
                           constants::kInvalidTractorTrailerId;
}

bool IsTrailer(const voy::TrackedObject& object) {
  const bool is_vehicle =
      object.object_type() == voy::perception::ObjectType::VEHICLE;
  return is_vehicle && object.associated_vehicle_head_id() !=
                           constants::kInvalidTractorTrailerId;
}

bool ShouldUseBoxHeadingForCalibration(
    const voy::TrackedObject& tracked_object) {
  if (tracked_object.object_type() == voy::perception::ObjectType::PED) {
    return false;
  }
  const double max_speed_to_apply_box_heading =
      tracked_object.object_type() == voy::perception::ObjectType::VEHICLE &&
              !utility::IsAutonomousMiniCar(tracked_object)
          ? constants::kMaxSpeedForSlowVehicleToApplyBoxHeading
          : constants::kMaxSpeedForSlowCycToApplyBoxHeading;
  return tracked_object.velocity() <= max_speed_to_apply_box_heading;
}

double GetCalibratedHeadingForLowSpeedObject(
    const voy::TrackedObject& tracked_object) {
  return utility::ShouldUseBoxHeadingForCalibration(tracked_object)
             ? tracked_object.box_heading()
             : tracked_object.heading();
}

// Get the angular velocity from trajectory poses.
std::vector<double> GetAngularVelocityFromTrajectoryPoses(
    const std::vector<planner::pb::TrajectoryPose>& trajectory_poses,
    const double agent_angular_velocity) {
  DCHECK_GE(trajectory_poses.size(), 2);
  std::vector<double> angular_velocities;
  angular_velocities.reserve(trajectory_poses.size());
  // The first angular velocity is from tracking.
  angular_velocities.push_back(agent_angular_velocity);
  for (size_t i = 1; i < trajectory_poses.size(); ++i) {
    const double delta_heading = math::NormalizeMinusPiToPi(
        trajectory_poses[i].heading() - trajectory_poses[i - 1].heading());
    angular_velocities.push_back(delta_heading / constants::kPoseIntervalInSec);
  }
  return angular_velocities;
}

double ComputeBoxHeadingViaBicycleModel(
    double velocity_heading, double signed_curvature, double object_length,
    pb::TrajectoryMotionType trajectory_motion_type) {
  // Early return and do not correct for the straight motion.
  if (math::NearZero(signed_curvature)) {
    return velocity_heading;
  }

  const double wheel_base = object_length * constants::kWheelBaseToLengthRatio;
  const double half_wheel_base = 0.5 * wheel_base;

  // Compute the radiuses for the cg (center of gravity) and rear axle (rear
  // tire). It is worthing that the |squared_radius_rear_axle| may be minus
  // since the |radius_cg| is noisy and uncontrollable. A minimum radius of rear
  // axle is used based on the maximum predefined steering angle.
  const double radius_cg = 1.0 / std::abs(signed_curvature);
  const double squared_radius_rear_axle =
      radius_cg * radius_cg - half_wheel_base * half_wheel_base;
  const double min_radius_rear_axle =
      wheel_base / std::tan(kMaxSteeringAngleForVehicle);
  const double radius_rear_axle =
      squared_radius_rear_axle < min_radius_rear_axle * min_radius_rear_axle
          ? min_radius_rear_axle
          : std::sqrt(squared_radius_rear_axle);

  // Compute slip angle and corrected box heading.
  const double slip_angle =
      trajectory_motion_type ==
              pb::TrajectoryMotionType::COMPLETE_REVERSE_DRIVING
          ? -std::atan2(half_wheel_base, radius_rear_axle)
          : std::atan2(half_wheel_base, radius_rear_axle);
  const double box_heading = signed_curvature > 0.0
                                 ? velocity_heading - slip_angle
                                 : velocity_heading + slip_angle;
  return math::NormalizeMinusPiToPi(box_heading);
}

std::vector<double> ComputeMotionHeadings(
    const std::vector<planner::pb::TrajectoryPose>& poses,
    voy::perception::ObjectType object_type) {
  DCHECK(!poses.empty());
  DCHECK_GE(poses.size(), 2);
  const double max_distance_of_close_points =
      object_type == voy::perception::ObjectType::CYCLIST
          ? kMaxDistanceOfClosePointsForCyc
          : kMaxDistanceOfClosePointsForVehAndPed;

  std::vector<double> headings(poses.size());
  // The first heading is from tracking box heading.
  headings[0] = poses[0].heading();
  for (size_t i = 1; i < poses.size(); ++i) {
    const int next_index = (i + 1 < poses.size()) ? i + 1 : i;
    const double delta_x = poses[next_index].x_pos() - poses[i - 1].x_pos();
    const double delta_y = poses[next_index].y_pos() - poses[i - 1].y_pos();

    if (!math::NearZero(delta_x, max_distance_of_close_points) ||
        !math::NearZero(delta_y, max_distance_of_close_points)) {
      headings[i] = std::atan2(delta_y, delta_x);
    } else {
      headings[i] = headings[i - 1];
    }
  }
  return headings;
}

std::vector<std::vector<const pnc_map::Lane*>> GetValidRoutes(
    const std::vector<const pnc_map::Lane*>& target_section_lanes) {
  std::vector<std::vector<const pnc_map::Lane*>> valid_routes;
  valid_routes.reserve(target_section_lanes.size());
  if (target_section_lanes.empty()) {
    return valid_routes;
  }
  for (const auto& target_lane : target_section_lanes) {
    if (target_lane->type() != hdmap::Lane_LaneType_BIKE &&
        target_lane->type() != hdmap::Lane_LaneType_EMERGENCY) {
      valid_routes.push_back({target_lane});
    }
  }
  return valid_routes;
}

double GetTargetLengthForRouteInfer(double current_speed) {
  const double target_length_by_speed =
      current_speed * constants::kTargetTrajectoryTimeInSec;
  return std::max(target_length_by_speed, constants::kMinTargetLaneDistance);
}

// Gets the heading by interpolating based on the known headings and the
// corresponding odometers at the query_odom.
double GetInterpolateHeadingsByOdometer(const std::vector<double>& odoms,
                                        const std::vector<double>& headings,
                                        double query_odom,
                                        double odometer_increase_threshold) {
  DCHECK_EQ(odoms.size(), headings.size());
  DCHECK_GE(odoms.size(), 2);
  DCHECK_GE(query_odom, odoms.front());
  DCHECK_LE(query_odom, odoms.back() + odometer_increase_threshold);

  if (odoms.front() == query_odom) {
    return headings.front();
  }
  if (odoms.back() <= query_odom) {
    return headings.back();
  }
  size_t start_index = 0;
  size_t end_index = 0;
  if (headings.size() == 2) {
    start_index = 0;
    end_index = 1;
  } else {
    // Search the odoms interval that query_odom belongs to.
    const auto end_pose_iter =
        std::lower_bound(odoms.begin(), odoms.end(), query_odom);
    end_index = std::distance(odoms.begin(), end_pose_iter);
    start_index = end_index - 1;
  }
  const double ratio = math::GetInterpolationRatio(
      odoms[start_index], odoms[end_index], query_odom);
  return math::AngleInterpolate(headings[start_index], headings[end_index],
                                ratio);
}

pb::PredictedTrajectory FakeTrajectoryBasedOnVariance(
    const pb::PredictedTrajectory& predicted_traj,
    voy::perception::ObjectType object_type, double lat_std_scale,
    double lng_std_scale) {
  pb::PredictedTrajectory faked_predicted_traj = predicted_traj;
  std::vector<planner::pb::TrajectoryPose> fake_poses;
  fake_poses.reserve(predicted_traj.traj_poses_size());

  for (int i = 0; i < predicted_traj.traj_poses_size(); ++i) {
    planner::pb::TrajectoryPose fake_pose = predicted_traj.traj_poses(i);
    const double pose_heading = fake_pose.heading();
    const double lat_std = lat_std_scale * fake_pose.uncertainty().lat_sd();
    const double lng_std = lng_std_scale * fake_pose.uncertainty().long_sd();

    const double new_x = fake_pose.x_pos() - lat_std * std::sin(pose_heading) +
                         lng_std * std::cos(pose_heading);
    const double new_y = fake_pose.y_pos() + lat_std * std::cos(pose_heading) +
                         lng_std * std::sin(pose_heading);
    fake_pose.set_x_pos(new_x);
    fake_pose.set_y_pos(new_y);
    if (!fake_poses.empty()) {
      fake_pose.set_odom(
          fake_poses.back().odom() +
          std::hypot(fake_pose.x_pos() - fake_poses.back().x_pos(),
                     fake_pose.y_pos() - fake_poses.back().y_pos()));
    }
    // clear fake_pose uncertainty
    fake_pose.mutable_uncertainty()->Clear();
    fake_poses.push_back(std::move(fake_pose));
  }

  ComputeAndSetHeadings(&fake_poses, object_type);
  ComputeAndSetSpeed(&fake_poses);
  ComputeAndSetAccel(&fake_poses);
  *faked_predicted_traj.mutable_traj_poses() = {fake_poses.begin(),
                                                fake_poses.end()};
  return faked_predicted_traj;
}

std::vector<voy::perception::Attribute>
ConvertEgoLightSignalToVehicleTurnSignal(voy::LightSignal ego_light_signal) {
  switch (ego_light_signal) {
    case voy::LightSignal::LIGHT_LEFT:
      return {voy::perception::Attribute::VEHICLE_LEFT_TURN_SIGNAL_ON};
    case voy::LightSignal::LIGHT_RIGHT:
      return {voy::perception::Attribute::VEHICLE_RIGHT_TURN_SIGNAL_ON};
    case voy::LightSignal::LIGHT_HAZARD:
      // Add a VEHICLE_TURN_SIGNAL_OFF to make sure the feature is no-op
      // with/without hazard feature. Because VEHICLE_TURN_SIGNAL_OFF will
      // result to [left_turn_status_OFF, right_turn_status_OFF] in
      // agent_state.cpp. Without it, ego agent with hazard on will result to
      // [left_turn_status_UNKNOW, right_turn_status_UNKNOW].
      return {voy::perception::Attribute::VEHICLE_HAZARD_SIGNAL_ON,
              voy::perception::Attribute::VEHICLE_TURN_SIGNAL_OFF};
    default:
      // Default value for LIGHT_NONE and LIGHT_INVALID.
      return {voy::perception::Attribute::VEHICLE_TURN_SIGNAL_OFF};
  }
}

int GetTrajectoryPoseIndexAtFirstGearChange(
    const std::vector<planner::pb::TrajectoryPose>& trajectory_poses) {
  DCHECK_GE(trajectory_poses.size(), 2);

  auto iter =
      std::adjacent_find(trajectory_poses.begin(), trajectory_poses.end(),
                         [](const planner::pb::TrajectoryPose& prev_pose,
                            const planner::pb::TrajectoryPose& cur_pose) {
                           return prev_pose.gear() != cur_pose.gear();
                         });
  return iter == trajectory_poses.end()
             ? -1
             : std::distance(trajectory_poses.begin(), iter) + 1;
}

bool IsAnimalObject(const voy::TrackedObject& tracked_object) {
  return tracked_object.object_type() == voy::perception::UNKNOWN &&
         std::any_of(tracked_object.attributes().begin(),
                     tracked_object.attributes().end(), [](const auto& attr) {
                       return attr == voy::perception::IS_ANIMAL;
                     });
}

bool IsOnExpressWay(const pnc_map::PredictionMapService& prediction_map_service,
                    const hdmap::Point& point) {
  const std::vector<const pnc_map::Road*> nearby_roads =
      prediction_map_service.GetNearbyRoads(point,
                                            kNearbyRoadQueryRangeForExpressway);
  if (nearby_roads.empty()) {
    return false;
  }
  // Check if any of the nearby roads is an expressway.
  bool is_expressway = std::any_of(
      nearby_roads.begin(), nearby_roads.end(), [](const auto* road) {
        return road->proto().road_class() == hdmap::Road::EXPRESSWAY;
      });
  return is_expressway;
}

}  // namespace utility
}  // namespace prediction
