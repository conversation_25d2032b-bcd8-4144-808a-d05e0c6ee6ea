#ifndef ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_SIGNAL_REASONER_TRAFFIC_SIGNAL_REASONER_H_
#define ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_SIGNAL_REASONER_TRAFFIC_SIGNAL_REASONER_H_

#include <map>
#include <set>
#include <vector>

#include <glog/logging.h>

#include "planner/utility/seed/planning_seed.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"
#include "routing/engine/routing_edge_filter.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {

// Related info about signal controlled variable lane.
struct VariableLaneInfo {
  const pnc_map::Lane* variable_lane = nullptr;
  // Variable lane type. (normal, tidal or variable)
  voy::TidalFlowSign::LaneType lane_type = voy::TidalFlowSign::NORMAL_LANE;
  // Predicted traffic light type from perception.
  voy::TrafficLight::Type predicted_traffic_light_type =
      voy::TrafficLight::UNKNOWN_TYPE;
  // Predicted variable lane's associated turn type. Can be from map labelling
  // or perception.
  std::vector<hdmap::Lane::Turn> drivable_turn_types;
  // Variable lane's current associated passable virtual lanes (variable lane's
  // successor).
  std::vector<const pnc_map::Lane*> passable_virtual_lanes;
  // Variable lane's current associated restricted virtual lanes (variable
  // lane's successor).
  std::vector<const pnc_map::Lane*> restricted_virtual_lanes;
  // Detection result's confidence score.
  double confidence_score = 0.0;
  // Dist from ego to traffic sign. In meter.
  double ego_to_traffic_sign_dist = 0.0;
  // Is this variable lane from seed.
  bool is_from_seed = false;

  bool HasValidResult() const {
    return predicted_traffic_light_type != voy::TrafficLight::UNKNOWN_TYPE;
  }

  bool HasValidResultAndCorrespondingPassableLane() const {
    return (predicted_traffic_light_type != voy::TrafficLight::UNKNOWN_TYPE) &&
           (!passable_virtual_lanes.empty());
  }

  bool HasUnknownResult() const {
    return predicted_traffic_light_type == voy::TrafficLight::UNKNOWN_TYPE;
  }

  bool IsAssociatedLane(const pnc_map::Lane* lane) const {
    if (!lane || !variable_lane) {
      return false;
    }

    if (lane->id() == variable_lane->id()) {
      return true;
    }

    const pnc_map::Lane* local_variable_lane = variable_lane;
    if (!lane->successors().empty()) {
      if (std::any_of(lane->successors().begin(), lane->successors().end(),
                      [local_variable_lane](const pnc_map::Lane* successor) {
                        return (successor &&
                                (successor->id() == local_variable_lane->id()));
                      })) {
        return true;
      }
    }

    if (!lane->predecessors().empty()) {
      if (std::any_of(lane->predecessors().begin(), lane->predecessors().end(),
                      [local_variable_lane](const pnc_map::Lane* predecessor) {
                        return (predecessor && (predecessor->id() ==
                                                local_variable_lane->id()));
                      })) {
        return true;
      }
    }

    return false;
  }

  // Converts variable lane info to proto.
  void ConvertVariableLaneInfoToProto(
      pb::SignalControlledVariableLaneInfo* variable_lane_info_pb) const {
    variable_lane_info_pb->set_lane_id(variable_lane->id());
    variable_lane_info_pb->set_predicted_traffic_light_type(
        predicted_traffic_light_type);
    variable_lane_info_pb->set_ego_to_traffic_sign_dist(
        ego_to_traffic_sign_dist);
    variable_lane_info_pb->set_variable_lane_type(lane_type);
    variable_lane_info_pb->set_confidence_score(confidence_score);
    for (const auto& turn : drivable_turn_types) {
      variable_lane_info_pb->add_analyzed_turn_types(turn);
    }

    for (const pnc_map::Lane* passable_virtual_lane : passable_virtual_lanes) {
      variable_lane_info_pb->add_passable_virtual_lane_ids(
          passable_virtual_lane->id());
    }

    for (const pnc_map::Lane* restricted_virtual_lane :
         restricted_virtual_lanes) {
      variable_lane_info_pb->add_restricted_virtual_lane_ids(
          restricted_virtual_lane->id());
    }
  }

  // Populates variable lane info into debug message.
  void PopulateVariableLaneInfoDebug(
      bool is_from_perception,
      pb::SignalControlledVariableLaneInfoDebug*
          signal_controlled_variable_lane_info_debug) const {
    DCHECK(signal_controlled_variable_lane_info_debug);
    signal_controlled_variable_lane_info_debug->set_is_from_perception(
        is_from_perception);
    ConvertVariableLaneInfoToProto(
        signal_controlled_variable_lane_info_debug
            ->mutable_signal_controlled_variable_lane_info());
  }

  // Populates variable lane info into seed.
  void PopulateVariableLaneInfoSeed(
      pb::SignalControlledVariableLaneSeed*
          signal_controlled_variable_lane_info_seed) const {
    DCHECK(signal_controlled_variable_lane_info_seed);
    signal_controlled_variable_lane_info_seed
        ->set_update_from_perception_result(true);
    ConvertVariableLaneInfoToProto(
        signal_controlled_variable_lane_info_seed
            ->mutable_signal_controlled_variable_lane_info());
  }

  // Constructs VariableLaneInfo struct from pb.
  void ConstructVariableLaneInfoFromPb(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const pb::SignalControlledVariableLaneInfo& variable_lane_info_pb) {
    variable_lane = joint_pnc_map_service.GetLaneSequence(
        {variable_lane_info_pb.lane_id()})[0];
    lane_type = variable_lane_info_pb.variable_lane_type();
    confidence_score = variable_lane_info_pb.confidence_score();
    predicted_traffic_light_type =
        variable_lane_info_pb.predicted_traffic_light_type();
    ego_to_traffic_sign_dist = variable_lane_info_pb.ego_to_traffic_sign_dist();
    is_from_seed = true;
    passable_virtual_lanes = joint_pnc_map_service.GetLaneSequence(
        variable_lane_info_pb.passable_virtual_lane_ids());
    restricted_virtual_lanes = joint_pnc_map_service.GetLaneSequence(
        variable_lane_info_pb.restricted_virtual_lane_ids());
    drivable_turn_types.reserve(
        variable_lane_info_pb.analyzed_turn_types().size());
    for (const auto& turn_type : variable_lane_info_pb.analyzed_turn_types()) {
      drivable_turn_types.push_back(static_cast<hdmap::Lane::Turn>(turn_type));
    }
  }
};

// It collects traffic signal related info from perception, and gets related
// lanes and associated features to planner.
class TrafficSignalReasoner {
 public:
  TrafficSignalReasoner() = default;

  // Updates traffic signal reasoner.
  pb::TrafficSignalReasonerSeed Update(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const ::google::protobuf::RepeatedPtrField< ::voy::TidalFlow>&
          tidal_flows,
      const pb::TrafficSignalReasonerSeed* previous_seed) {
    // Use perception's result (tidal_flows) to update signal controlled
    // variable lane info.
    UpdatePerceptionSignalControlledVariableLanes(joint_pnc_map_service,
                                                  tidal_flows);
    // Merge perception's result and last cycle's seed to get signal controlled
    // variable lane info, this will be used by drivable lane reasoner.
    MergeAndGetPerceptionAndSeedSignalControlledVariableLanes(
        joint_pnc_map_service, previous_seed);
    // Update seed.
    return UpdateTrafficSignalReasonerSeed(previous_seed);
  }

  // Checks if lane is signal controlled variable lane or its successor, and
  // whether it is drivable or not.
  bool IsSignalControlledVariableAndVirtualLaneDrivable(
      const pnc_map::Lane& lane) const;

  void UpdateSignalControlledVariableLaneDebug(
      pb::SignalControlledVariableLaneDebug* mutable_variable_lane_debug);

  // Clears all signal controlled variable lane info.
  void Clear() { signal_controlled_variable_lane_info_.clear(); }

  // Updates current lanes.
  void set_current_lanes(
      const std::vector<const pnc_map::Lane*>& current_lanes) {
    current_lanes_ = current_lanes;
  }

  const std::map<int64_t, VariableLaneInfo>&
  signal_controlled_variable_lane_info_map_with_seed() const {
    return signal_controlled_variable_lane_info_with_seed_message_;
  }

 private:
  // Based on |tidal_flows| and |current_lanes|, to update signal controlled
  // variable lanes and related infos.
  void UpdatePerceptionSignalControlledVariableLanes(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const ::google::protobuf::RepeatedPtrField< ::voy::TidalFlow>&
          tidal_flows);

  // Merges perception's result and last cycle's seed to get signal controlled
  // variable lane info, and this will be used by drivable lane reasoner.
  void MergeAndGetPerceptionAndSeedSignalControlledVariableLanes(
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const pb::TrafficSignalReasonerSeed* previous_seed);

  // Updates a signal controlled variable lane's passable and restricted virtual
  // lanes.
  void UpdatePassableAndRestrictedVirtualLanes(
      const pnc_map::Lane& variable_lane,
      VariableLaneInfo* variable_lane_info) const;

  // Returns a vector of turn directions according to tidal flow's |arrow_type|.
  std::vector<hdmap::Lane::Turn> GetVariableLanePassableTurnTypes(
      voy::TrafficLight::Type arrow_type) const;

  pb::TrafficSignalReasonerSeed UpdateTrafficSignalReasonerSeed(
      const pb::TrafficSignalReasonerSeed* previous_seed);

  // Signal controlled variable lane id and related turn direction info map.
  // This map is built only based on perception's output.
  // key: signal controlled variable lane id.
  // value: variable lane related info.
  std::map<int64_t, VariableLaneInfo> signal_controlled_variable_lane_info_;

  // Signal controlled variable lane id and related turn direction info map.
  // This map is built by merging perception's output and seed.
  // key: signal controlled variable lane id.
  // value: variable lane related info.
  std::map<int64_t, VariableLaneInfo>
      signal_controlled_variable_lane_info_with_seed_message_;

  // Current lanes, synced from world model's current lane associator.
  std::vector<const pnc_map::Lane*> current_lanes_;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_TRAFFIC_SIGNAL_REASONER_TRAFFIC_SIGNAL_REASONER_H_
