#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_cell.h"

#include <vector>

#include <glog/logging.h>
#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "geometry/algorithms/equals.h"

namespace planner {

namespace open_space {

TEST(OpenSpaceGridCellTest, DiscretizedHeadingSearchSpaceTest) {
  const double heading_res_rad = 0.5;
  const DiscretizedHeadingSearchSpace heading_search_space_(
      /*x_idx_in=*/0, /*y_idx_in=*/0, heading_res_rad);

  EXPECT_EQ(heading_search_space_.overall_heading_range.start_pos, -M_PI);
  EXPECT_EQ(heading_search_space_.overall_heading_range.end_pos, M_PI);
  EXPECT_EQ(heading_search_space_.heading_res_rad, heading_res_rad);

  const std::vector<HeadingSearchSpaceInterval>& heading_intervals =
      heading_search_space_.heading_intervals;
  EXPECT_EQ(heading_intervals.size(), 13);
  for (size_t idx = 0; idx < heading_intervals.size(); ++idx) {
    const HeadingSearchSpaceInterval& curr_interval = heading_intervals[idx];
    EXPECT_EQ(curr_interval.interval_idx, idx);

    const math::Range1d& heading_range = curr_interval.heading_range;
    if (idx == 0) {
      EXPECT_EQ(heading_range.start_pos, -M_PI);
    }
    if (idx < heading_intervals.size() - 1) {
      EXPECT_EQ(heading_range.end_pos - heading_range.start_pos,
                heading_res_rad);
    } else {
      EXPECT_EQ(heading_range.end_pos, M_PI);
    }
  }

  EXPECT_EQ(heading_search_space_.GetIntervalIndex(-M_PI), 0);
  EXPECT_EQ(heading_search_space_.GetIntervalIndex(M_PI), 12);
  EXPECT_EQ(heading_search_space_.GetIntervalIndex(-1.0), 4);
  EXPECT_EQ(heading_search_space_.GetIntervalIndex(1.0), 8);
}

TEST(OpenSpaceGridCellTest, GridCellTest) {
  GridCellVertex lower_left(/*x=*/0.0, /*y=*/0.0, /*x_idx=*/0,
                            /*y_idx=*/0);
  lower_left.dominant_clearance_info.clearance = 0.0;
  lower_left.dominant_clearance_info.constraint_id =
      OccupancyConstraintID(OccupancyType::CONTOUR, /*constraint_idx=*/0);

  GridCellVertex lower_right(/*x=*/1.0, /*y=*/0.0, /*x_idx=*/1,
                             /*y_idx=*/0);
  lower_right.dominant_clearance_info.clearance = 1.0;
  lower_right.dominant_clearance_info.constraint_id =
      OccupancyConstraintID(OccupancyType::CONTOUR, /*constraint_idx=*/0);

  GridCellVertex upper_left(/*x=*/0.0, /*y=*/1.0, /*x_idx=*/0,
                            /*y_idx=*/1);
  upper_left.dominant_clearance_info.clearance = 2.0;
  upper_left.dominant_clearance_info.constraint_id =
      OccupancyConstraintID(OccupancyType::CONTOUR, /*constraint_idx=*/0);

  GridCellVertex upper_right(/*x=*/1.0, /*y=*/1.0, /*x_idx=*/1,
                             /*y_idx=*/1);
  upper_right.dominant_clearance_info.clearance = 3.0;
  upper_right.dominant_clearance_info.constraint_id =
      OccupancyConstraintID(OccupancyType::CONTOUR, /*constraint_idx=*/0);

  const GridCell grid_cell(&lower_left, &lower_right, &upper_left, &upper_right,
                           /*heading_res_rad=*/0.5);
  EXPECT_TRUE(math::geometry::Equals(grid_cell.lower_left().xy_point,
                                     math::geometry::Point2d(0.0, 0.0)));
  EXPECT_TRUE(math::geometry::Equals(grid_cell.lower_right().xy_point,
                                     math::geometry::Point2d(1.0, 0.0)));
  EXPECT_TRUE(math::geometry::Equals(grid_cell.upper_left().xy_point,
                                     math::geometry::Point2d(0.0, 1.0)));
  EXPECT_TRUE(math::geometry::Equals(grid_cell.upper_right().xy_point,
                                     math::geometry::Point2d(1.0, 1.0)));

  // Test basic quadratic interpolation interface.
  const std::pair<double, double> quadratic_interp_ratio =
      grid_cell.GetQuadraticInterpolationRatio(/*x=*/0.5, /*y=*/0.5);
  EXPECT_EQ(quadratic_interp_ratio.first, 0.5);
  EXPECT_EQ(quadratic_interp_ratio.second, 0.5);

  // Test dist to forbidden query logic.
  // All vertices available.
  OccupancyClearanceInfo dominant_clearance_info =
      grid_cell.GetDominantOccupancyClearanceInfo(
          math::geometry::Point2d(0.5, 0.5),
          /*drivable_clearance_thresh=*/1.245);
  EXPECT_NEAR(dominant_clearance_info.clearance, 0.707107,
              math::constants::kEpsilon);
  EXPECT_TRUE(!dominant_clearance_info.is_drivable);
  // One of the vertices is not available.
  lower_left.is_available = false;
  dominant_clearance_info = grid_cell.GetDominantOccupancyClearanceInfo(
      math::geometry::Point2d(0.5, 0.5), /*drivable_clearance_thresh=*/1.245);
  EXPECT_EQ(dominant_clearance_info.clearance, 0.0);
  EXPECT_TRUE(!dominant_clearance_info.is_drivable);

  // Test heuristic distance to target query.
  lower_left.heuristic_dist_to_target = kMaxOpenSpaceDistanceInMeter;
  lower_right.heuristic_dist_to_target = 1.0;
  upper_left.heuristic_dist_to_target = 2.0;
  upper_right.heuristic_dist_to_target = 3.0;
  EXPECT_NEAR(
      grid_cell.GetHeuristicDistanceToTarget(math::geometry::Point2d(0.5, 0.5)),
      1.707107, math::constants::kEpsilon);
}

}  // namespace open_space

}  // namespace planner
