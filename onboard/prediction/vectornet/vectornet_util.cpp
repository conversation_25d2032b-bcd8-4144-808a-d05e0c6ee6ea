#include "prediction/vectornet/vectornet_util.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <cstdint>
#include <memory>
#include <string>
#include <utility>

#include <Eigen/src/Core/Matrix.h>
#include <boost/math/statistics/linear_regression.hpp>
#include <fmt/format.h>
#include <glog/logging.h>
#include <gtl/map_util.h>
#include <tbb/parallel_for.h>

#include "base/optional_value_accessor.h"
#include "geometry/algorithms/curvature.h"
#include "geometry/model/point_2d.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/unit_conversion.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction/common/definition/local_coordinates.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction/common/definition/prediction_gflag.h"
#include "prediction/common/utility/config_manager.h"
#include "prediction/common/utility/motion_utility.h"
#include "prediction/common/utility/prediction_utility.h"
#include "prediction/common/utility/registry.h"
#include "prediction/concept/agent/agent.h"
#include "prediction/concept/maneuver/reference_line.h"
#include "prediction/concept/trajectory/predicted_trajectory.h"
#include "prediction/maneuver_proposal/common/maneuver_util.h"
#include "prediction/vectornet/ml_planner_kinematic_calculator.h"
#include "prediction_protos/agent_scenario_type.pb.h"
#include "prediction_protos/trajectory_generation_type.pb.h"
#include "rt_event/rt_event.h"
#include "strings/stringprintf.h"
#include "trace/trace.h"
#include "varch/utils/log.h"
#include "voy_protos/model_config.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_protos/pose.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_prediction.h"
#include "voy_trace/trace_prediction.h"
#include "voystat/stats_prediction.h"

namespace prediction {
namespace vectornet_util {
namespace {

// Skip checking for this dimension if the expected shape is equal to this
// value.
constexpr int kShapeToSkipDimensionCheck = -1;

constexpr double kTimeIntervalInS = math::Ms2Sec(constants::kPoseIntervalInMs);

// Number of floats in each pose's covariance matrix.
constexpr int kCovarianceNumOfFloats = 4;

// Vehicles longer than this length will be treated as large vehicle, and fed
// into the dedicated large-vehicle decoder during prediction vehicle model
// inference.
constexpr double kLengthThresholdForLargeVehicleHead = 8.0;  // m.

// Number of floats in one trajectory.
const int kNumFloatsOfOneTrajectory = constants::kPredictionHorizonInPoses * 2;

// Number of floats in covariances of one trajectory.
const int kNumFloatsOfCovarianceOfOneTrajectory =
    constants::kPredictionHorizonInPoses * kCovarianceNumOfFloats;

// The Z value corresponding to a percentile from
// mean, i.e. p_x = mean + Z * std;
constexpr double kP90Deviation = 1.282;
constexpr double kP10Deviation = -1.282;

// If the distance between the two points' in one predicted trajectory is
// smaller than |kTrajectoryDiscardedDistance|, the latter one should be
// discarded.
constexpr double kTrajectoryDiscardedDistance = 0.1;

// If a predicted trajectory contains more than
// |kNumMinPoseForValidTrajectory| poses, this trajectory can be seen as a
// valid trajectory.
constexpr int kNumMinPoseForValidTrajectory = 2;

// The minimum valid trajectory probability for VRU agents. VRU agents'
// predicted trajectory with lower probability than this threshold would be
// filtered.
constexpr double kMinValidTrajectoryProbabilityForVRU = 0.01;

// The minium heading difference for reverse driving pose.
constexpr double kMinHeadingDiffForReverseDrivingPose =
    math::Degree2Radian(90);  // radian.

constexpr double kMinSpeedForMovingUnknownObject = 0.2;  // m/s.

// The confident probability threshold for start to move.
constexpr double kConfidentProbForStartToMove = 0.9;

// Define a constant for target agent's speed coefficient to scalling
// the longitudinal distance.
constexpr double kSpeedCoefficient = 0.4;  // (m/s)^-1
// Define a constant for stationary target agent to extract the Euclidean
// distance between the target agent and the map object.
constexpr double kStationaryConstant = 1.0;

// The maximum current speed for reverse driving signal to be considered valid.
constexpr double kMaxCurrentSpeedForReverseDriving = 4.0;  // m/s

// Denormalizes to get the original point which is normalized to
// |normalized_point| using origin and heading.
math::geometry::Point2d DenormalizePoint(
    const math::geometry::Point2f& normalized_point,
    const math::geometry::Point2f& origin, float cos_heading,
    float sin_heading) {
  const math::geometry::Point2d rotated_point(
      cos_heading * normalized_point.x() - sin_heading * normalized_point.y(),
      sin_heading * normalized_point.x() + cos_heading * normalized_point.y());

  return math::geometry::Add(rotated_point,
                             math::geometry::Point2d(origin.x(), origin.y()));
}

// Parses positions (x, y) from data between begin and end and sets it to
// trajectory, then sets z, heading and speed depending on positions.
void SetTrajectoryPositions(const float* begin, const Agent& agent,
                            const LocalCoordinates& local_coordinates,
                            PredictedTrajectory* trajectory,
                            ::pb::ModelType model_type) {
  const float* end = begin + kNumFloatsOfOneTrajectory;
  std::vector<planner::pb::TrajectoryPose> poses;
  poses.reserve(constants::kNumOfPredictionTrajectoryPoses);

  // Set x and y.
  planner::pb::TrajectoryPose pose;
  pose.set_x_pos(agent.tracked_object().center_x());
  pose.set_y_pos(agent.tracked_object().center_y());
  poses.push_back(std::move(pose));

  for (int j = 1; j < constants::kNumOfPredictionTrajectoryPoses; ++j) {
    DCHECK_LT(begin + 1, end);
    const math::geometry::Point2f raw_point(*begin, *(begin + 1));
    const math::geometry::Point2d point = DenormalizePoint(
        raw_point, local_coordinates.origin, local_coordinates.cos_heading,
        local_coordinates.sin_heading);
    planner::pb::TrajectoryPose pose;
    pose.set_x_pos(point.x());
    pose.set_y_pos(point.y());
    poses.push_back(std::move(pose));
    begin += 2;
  }
  DCHECK_EQ(begin, end);

  // Sets timestamp, z, heading and speed.
  ComputeAndSetFields(agent.latest_timestamp(), agent.tracked_object(), &poses,
                      true, model_type);

  trajectory->SetTrajectoryPoses(std::move(poses));
}

// Parses predicted box heading from data between begin and end and sets it to
// trajectory pose for large vehicles and overrides their original headings,
// which were derived from trajectory poses.
void OverrideBoxHeadings(const float* box_headings_ptr, int buffer_length,
                         const LocalCoordinates& local_coord,
                         PredictedTrajectory* trajectory) {
  // Note that, the 0th trajectory pose represents the current state, and the
  // predicted box heading overrides starting from the 1st pose.
  DCHECK_EQ(trajectory->num_traj_poses(), buffer_length + 1);
  for (int idx = 0; idx < buffer_length; ++idx) {
    auto& pose = trajectory->mutable_traj_poses(idx + 1);
    const auto rel_box_heading = static_cast<double>(*(box_headings_ptr + idx));
    // Denormalize.
    const double denormal_box_headings =
        (math::NormalizeMinusPiToPi(rel_box_heading + local_coord.heading));
    pose.set_heading(denormal_box_headings);
  }
}

template <typename T>
void OverrideTrajectoryPoseField(
    const float* attribute_ptr, int buffer_length,
    PredictedTrajectory* trajectory,
    void (planner::pb::TrajectoryPose::*setFunc)(T)) {
  DCHECK_EQ(trajectory->num_traj_poses(), buffer_length + 1);
  for (int idx = 0; idx < buffer_length; ++idx) {
    auto& pose = trajectory->mutable_traj_poses(idx + 1);
    (pose.*(setFunc))(static_cast<double>(*(attribute_ptr + idx)));
  }
}

// Parses covariance tensor, denormalizes covariance matrix given the
// original heading and populate trajectory poses.
void SetTrajectoryCovariances(const float* covariance_iter,
                              const LocalCoordinates& local_coord,
                              PredictedTrajectory* trajectory) {
  const int num_poses = trajectory->num_traj_poses();

  Eigen::Matrix2f rotation_matrix(2, 2);
  rotation_matrix << local_coord.cos_heading, -local_coord.sin_heading,
      local_coord.sin_heading, local_coord.cos_heading;

  // TODO(haoxianggao): add tracking variances.
  // First pose is current pose, no variance set.
  auto& pose = trajectory->mutable_traj_poses(0);
  planner::pb::TrajectoryPoseUncertainty* uncertainty =
      pose.mutable_uncertainty();
  uncertainty->set_x_pos_var(0.0);
  uncertainty->set_y_pos_var(0.0);
  uncertainty->set_xy_pos_cov(0.0);

  DCHECK_EQ(kCovarianceNumOfFloats, 4);

  for (int i = 1; i < num_poses; ++i) {
    Eigen::Matrix2f cov(2, 2);
    cov << *covariance_iter, *(covariance_iter + 1), *(covariance_iter + 2),
        *(covariance_iter + 3);
    cov = rotation_matrix * cov * rotation_matrix.transpose();

    auto& pose = trajectory->mutable_traj_poses(i);
    planner::pb::TrajectoryPoseUncertainty* uncertainty =
        pose.mutable_uncertainty();
    uncertainty->set_x_pos_var(cov(0, 0));
    uncertainty->set_y_pos_var(cov(1, 1));
    uncertainty->set_xy_pos_cov(cov(0, 1));
    SetLatLongStandardDeviation(pose.heading(), uncertainty);
    covariance_iter += kCovarianceNumOfFloats;
  }
}

// Converts trajectory/path and speed tensor to CandidateTrajectoryAndSpeed. The
// returned value contains results for multiple routes and for each route it
// contains multiple modal candidate trajectories/paths and corresponding
// speeds.
CandidateTrajectoryAndSpeed GetCandidateTrajectoriesAndSpeed(
    const neural_net::Tensor& trajectory_tensor,
    const neural_net::Tensor& score_tensor,
    const neural_net::Tensor& speed_tensor,
    const neural_net::Tensor* nullable_modal_idx_tensor,
    const neural_net::Tensor* nullable_trajectory_ln_std_tensor,
    const voy::TrackedObject& ego_object,
    const LocalCoordinates& local_coordinates, int num_routes, int num_modals,
    int horizon, bool is_ml_path, ::pb::ModelType model_type) {
  // Output includes x, y.
  constexpr int trajectory_output_dimension = 2;
  constexpr int speed_output_dimension = 2;
  constexpr int trajectory_var_output_dimension = 2;
  // Use is_smart_agent to check whether the model is smart agent or mlplanner
  // section routes.
  const bool is_smart_agent =
      model_type == ::pb::ModelType::SIM_VECTORNET_CONDITIONED;
  if (!is_smart_agent) {
    DCHECK_EQ(trajectory_tensor.num_dims(), 4);
    // dim 0 represents batch size and dim 1 represents num of route candidates.
    DCHECK_EQ(trajectory_tensor.dim_size(0), 1);
    DCHECK_EQ(trajectory_tensor.dim_size(1), num_routes * num_modals);
    DCHECK_EQ(trajectory_tensor.dim_size(2), horizon);
    DCHECK_EQ(trajectory_tensor.dim_size(3), trajectory_output_dimension);
    DCHECK_EQ(score_tensor.dim_size(0), 1);
    DCHECK_EQ(score_tensor.dim_size(1), num_routes * num_modals);
    DCHECK_EQ(speed_tensor.dim_size(0), 1);
    DCHECK_EQ(speed_tensor.dim_size(1), num_routes * num_modals);
    DCHECK_EQ(speed_tensor.dim_size(2), horizon);
    DCHECK_EQ(speed_tensor.dim_size(3), speed_output_dimension);
  }

  // Get tensors' begin and end pointer.
  const float* traj_tensor_begin =
      static_cast<const float*>(trajectory_tensor.data());
  const float* score_tensor_begin =
      static_cast<const float*>(score_tensor.data());
  const float* speed_tensor_begin =
      static_cast<const float*>(speed_tensor.data());

  const float* traj_tensor_iter = traj_tensor_begin;
  const float* score_tensor_iter = score_tensor_begin;
  const float* speed_tensor_iter = speed_tensor_begin;

  const int32_t* modal_idx_tensor_begin = nullptr;
  const int32_t* modal_idx_tensor_iter = nullptr;
  if (nullable_modal_idx_tensor != nullptr) {
    DCHECK_EQ(nullable_modal_idx_tensor->num_dims(), 2);
    DCHECK_EQ(nullable_modal_idx_tensor->dim_size(1), num_routes * num_modals);
    modal_idx_tensor_begin =
        static_cast<const int32_t*>(nullable_modal_idx_tensor->data());
    modal_idx_tensor_iter = modal_idx_tensor_begin;
  }

  const float* traj_var_tensor_begin = nullptr;
  const float* traj_var_tensor_iter = nullptr;
  if (nullable_trajectory_ln_std_tensor != nullptr) {
    DCHECK_EQ(nullable_trajectory_ln_std_tensor->num_dims(), 4);
    DCHECK_EQ(nullable_trajectory_ln_std_tensor->dim_size(0), 1);
    DCHECK_EQ(nullable_trajectory_ln_std_tensor->dim_size(1),
              num_routes * num_modals);
    DCHECK_EQ(nullable_trajectory_ln_std_tensor->dim_size(2), horizon);
    DCHECK_EQ(nullable_trajectory_ln_std_tensor->dim_size(3),
              trajectory_var_output_dimension);
    traj_var_tensor_begin =
        static_cast<const float*>(nullable_trajectory_ln_std_tensor->data());
    traj_var_tensor_iter = traj_var_tensor_begin;
  }

  planner::pb::TrajectoryPose ego_pose;
  ego_pose.set_x_pos(ego_object.center_x());
  ego_pose.set_y_pos(ego_object.center_y());

  CandidateTrajectoryAndSpeed candidates;
  std::vector<std::vector<CandidateTrajectory>> batch_candidate_trajectories;
  std::vector<std::vector<CandidateSpeed>> batch_candidate_speeds;

  const int num_candidates = is_smart_agent ? trajectory_tensor.dim_size(0)
                                            : trajectory_tensor.dim_size(1);
  batch_candidate_trajectories.reserve(num_candidates);
  batch_candidate_speeds.reserve(num_candidates);
  if (!is_smart_agent) {
    DCHECK_EQ(score_tensor.dim_size(1), num_candidates);
    DCHECK_EQ(speed_tensor.dim_size(1), num_candidates);
  }
  for (int i = 0; i < num_candidates; ++i) {
    // Candidate trajectories and speed for single batch.
    std::vector<CandidateTrajectory> candidate_trajectories;
    std::vector<CandidateSpeed> candidate_speeds;
    candidate_trajectories.reserve(num_modals);
    candidate_speeds.reserve(num_modals);
    for (int j = 0; j < num_modals; ++j) {
      CandidateTrajectory candidate_trajectory;
      CandidateSpeed candidate_speed;
      std::vector<planner::pb::TrajectoryPose> poses;
      std::vector<planner::pb::SpeedInfo> speeds;
      std::vector<double> arc_lengths;
      poses.reserve(horizon + 1);
      poses.push_back(ego_pose);
      speeds.reserve(horizon);
      arc_lengths.reserve(horizon);
      for (int k = 0; k < horizon; ++k) {
        const math::geometry::Point2f raw_point(*traj_tensor_iter,
                                                *(traj_tensor_iter + 1));
        traj_tensor_iter += trajectory_output_dimension;

        const double speed_mean = static_cast<double>(*speed_tensor_iter);
        const double speed_log_std =
            static_cast<double>(*(speed_tensor_iter + 1));
        const double speed_std = std::exp(speed_log_std);
        speed_tensor_iter += speed_output_dimension;

        double lng_std = 0.0;
        double lat_std = 0.0;
        if (nullable_trajectory_ln_std_tensor) {
          lng_std = std::exp(static_cast<double>(*traj_var_tensor_iter));
          lat_std = std::exp(static_cast<double>(*(traj_var_tensor_iter + 1)));
          traj_var_tensor_iter += trajectory_var_output_dimension;
        }

        const math::geometry::Point2d point = DenormalizePoint(
            raw_point, local_coordinates.origin, local_coordinates.cos_heading,
            local_coordinates.sin_heading);
        const double distance = math::geometry::Distance(
            point, math::geometry::Point2d{poses.back().x_pos(),
                                           poses.back().y_pos()});
        // Discard points that are too close.
        if (is_ml_path && distance < kTrajectoryDiscardedDistance) {
          continue;
        }
        // Set pose
        planner::pb::TrajectoryPose pose;
        pose.set_x_pos(point.x());
        pose.set_y_pos(point.y());
        pose.mutable_uncertainty()->set_long_sd(lng_std);
        pose.mutable_uncertainty()->set_lat_sd(lat_std);
        poses.push_back(std::move(pose));
        // Set speed info
        arc_lengths.push_back(
            arc_lengths.empty() ? distance : arc_lengths.back() + distance);
        planner::pb::SpeedInfo speed;
        speed.set_mean(speed_mean);
        speed.set_p90_value(speed_mean + kP90Deviation * speed_std);
        speed.set_p10_value(speed_mean + kP10Deviation * speed_std);
        speed.set_arc_length(arc_lengths.back());
        speeds.push_back(std::move(speed));
      }
      const double score = static_cast<double>(*score_tensor_iter);
      ++score_tensor_iter;
      int modal_idx = -1;
      if (nullable_modal_idx_tensor) {
        modal_idx = static_cast<int>(*modal_idx_tensor_iter);
        ++modal_idx_tensor_iter;
      }
      if (poses.size() > kNumMinPoseForValidTrajectory) {
        candidate_trajectory.name =
            fmt::format("{}-{}-{}", is_ml_path ? "path" : "trajectory", i, j);
        candidate_trajectory.poses = std::move(poses);
        candidate_trajectory.probability = score;
        candidate_trajectory.modal_idx = modal_idx;
        candidate_trajectory.route_idx_in_output_tensor = i;
        candidate_trajectory.modal_idx_in_output_tensor = j;
        candidate_speed.speeds = std::move(speeds);
        candidate_trajectories.push_back(std::move(candidate_trajectory));
        candidate_speeds.push_back(std::move(candidate_speed));
      }
    }
    batch_candidate_trajectories.push_back(std::move(candidate_trajectories));
    batch_candidate_speeds.push_back(std::move(candidate_speeds));
  }
  DCHECK_EQ(traj_tensor_iter,
            traj_tensor_begin + GetTensorSize(trajectory_tensor));
  DCHECK_EQ(speed_tensor_iter,
            speed_tensor_begin + GetTensorSize(speed_tensor));
  DCHECK_EQ(score_tensor_iter,
            score_tensor_begin + GetTensorSize(score_tensor));
  if (nullable_modal_idx_tensor) {
    DCHECK_EQ(
        modal_idx_tensor_iter,
        modal_idx_tensor_begin + GetTensorSize(*nullable_modal_idx_tensor));
  }
  if (nullable_trajectory_ln_std_tensor) {
    DCHECK_EQ(traj_var_tensor_iter,
              traj_var_tensor_begin +
                  GetTensorSize(*nullable_trajectory_ln_std_tensor));
  }
  candidates.candidate_trajectories = std::move(batch_candidate_trajectories);
  candidates.candidate_speeds = std::move(batch_candidate_speeds);
  return candidates;
}

// Get candidate routes for section route.
std::vector<planner::pb::RouteCandidate> GetCandidateRoutes(
    const std::vector<RouteFeature>& route_features,
    const neural_net::Tensor& modal_idx_tensor,
    const neural_net::Tensor& traj_probs) {
  std::vector<planner::pb::RouteCandidate> candidate_route;
  const auto* const modal_idx_tensor_begin =
      static_cast<const int32_t*>(modal_idx_tensor.data());
  const int32_t* const modal_idx_tensor_end =
      modal_idx_tensor_begin + GetTensorSize(modal_idx_tensor);

  const int32_t* modal_idx_tensor_iter = modal_idx_tensor_begin;

  const auto* const traj_prob_tensor_begin =
      static_cast<const float*>(traj_probs.data());
  const float* const traj_prob_tensor_end =
      traj_prob_tensor_begin + GetTensorSize(traj_probs);

  const float* traj_prob_tensor_iter = traj_prob_tensor_begin;

  std::vector<planner::pb::RouteCandidate> candidates;
  const int num_routes = modal_idx_tensor.dim_size(1);
  candidates.reserve(num_routes);

  int primary_index = -1;
  double max_score = -1.0;
  for (int i = 0; i < num_routes; ++i) {
    planner::pb::RouteCandidate route;
    const auto route_score = static_cast<double>(*traj_prob_tensor_iter);
    const int route_idx = static_cast<int>(*modal_idx_tensor_iter);
    if (route_score > max_score) {
      primary_index = i;
      max_score = route_score;
    }
    if (static_cast<int>(route_features.size()) > route_idx) {
      *route.mutable_lane_ids() = {
          route_features[route_idx].lane_sequence_ids.begin(),
          route_features[route_idx].lane_sequence_ids.end()};
    }
    route.set_score(route_score);

    ++traj_prob_tensor_iter;
    ++modal_idx_tensor_iter;
    candidates.push_back(std::move(route));
  }
  DCHECK_EQ(traj_prob_tensor_iter, traj_prob_tensor_end);
  DCHECK_EQ(modal_idx_tensor_iter, modal_idx_tensor_end);
  candidate_route = std::move(candidates);
  DCHECK_LE(primary_index, num_routes);
  DCHECK_GE(primary_index, 0);
  auto& primary_route = candidate_route.at(primary_index);
  primary_route.set_is_primary(true);

  return candidate_route;
}

// Returns true if the agent has too many Start-To-Move FP predictions in recent
// |history_horizon_ms|.
bool TooManyStartToMoveFPInHistory(const Agent& agent) {
  // Not FP if agent have not been recalled start-to-move for a long time.
  // This threshold need to be long enough to exclude vehicles in a long queue
  // to pass through a green light.
  const int64_t kMinStartToMoveDurationMs = 10000;  // 10 seconds

  const int64_t current_timestamp = agent.current_timestamp();
  if (agent.observed_stationary_duration_ms() < kMinStartToMoveDurationMs ||
      (agent.first_start_to_move_recall_ts() > 0 &&
       current_timestamp - agent.first_start_to_move_recall_ts() <=
           kMinStartToMoveDurationMs)) {
    return false;
  }

  if (agent.first_start_to_move_recall_ts() > 0 &&
      current_timestamp - agent.first_start_to_move_recall_ts() <=
          kMinStartToMoveDurationMs) {
    return false;
  }

  const int64_t kHistoryToCheckStartToMoveFP = 3000;  // ms
  DCHECK_LE(kHistoryToCheckStartToMoveFP,
            constants::kDefaultVehicleOutdatedTimeInPoses *
                constants::kPoseIntervalInMs);

  const int64_t observation_begin_time =
      current_timestamp - kHistoryToCheckStartToMoveFP;

  const auto& history_states = agent.history_states();
  const auto state_begin_iter =
      history_states.FindClosestDataBeforeEqual(observation_begin_time);
  if (state_begin_iter == history_states.end()) {
    // Cannot determine if it is a FP if there is no enough observations.
    return false;
  }

  int num_pred_to_move = 0;
  int num_stationary_frames = 0;
  int num_total_frames = 0;
  std::vector<double> time_to_move_list;
  std::vector<double> time_list;
  for (auto iter = state_begin_iter; iter != history_states.end(); ++iter) {
    if (iter->second.is_stationary()) {
      num_stationary_frames += 1;
      const AgentState& history_state = iter->second;

      // History intention type might be different from the predicted TTM
      // results of that frame, because we may changed it based on heuristics.
      const pb::StationaryIntention& history_intention =
          iter->second.stationary_intention();
      const bool did_stm_prediction =
          history_state.scenario_type().HasAny(
              {pb::ScenarioType::VEHICLE_EGO_AWARE_STATIONARY,
               pb::ScenarioType::VEHICLE_EGO_AWARE_SLOW}) &&
          history_intention.stationary_intention_type() !=
              pb::StationaryIntentionType::UNKNOWN_STATIONARY_INTENTION;
      const bool is_predicted_stm = IsStartToMove(
          history_intention.stm_prob(), history_intention.time_to_move(),
          history_intention.standard_deviation_of_time_to_move());
      if (did_stm_prediction && is_predicted_stm) {
        num_pred_to_move += 1;
      }
      time_to_move_list.push_back(history_intention.time_to_move());
      time_list.push_back(
          math::Ms2Sec(iter->first - agent.current_timestamp()));
    }
    num_total_frames += 1;
  }

  // In the history buffer most of the frames is stationary (GT stationary).
  // Use the ratio to filter is_stationary noise.
  const float kMinRatioForGTStationary = 0.8;
  const float stationary_ratio =
      static_cast<float>(num_stationary_frames) / num_total_frames;
  if (stationary_ratio < kMinRatioForGTStationary) {
    return false;
  }

  // If ground truth almost stationary, check the predicted frames.
  const float kMinRatioForStartToMoveFP = 0.8;
  const float fp_ratio =
      static_cast<float>(num_pred_to_move) / num_total_frames;

  const bool is_most_pred_fp = fp_ratio > kMinRatioForStartToMoveFP;

  bool has_stm_trend = false;
  if (time_list.size() >= 2) {
    constexpr float kMaxSlopeForSTMTrend = -0.2;
    auto [_, slope] = boost::math::statistics::simple_ordinary_least_squares(
        time_list, time_to_move_list);
    has_stm_trend = slope < kMaxSlopeForSTMTrend;
  }
  return is_most_pred_fp && !has_stm_trend;
}

bool IsTurnSignalOnAtOneSide(const VehicleSignals& vehicle_signals,
                             math::pb::Side side) {
  DCHECK(side == math::pb::kLeft || side == math::pb::kRight);
  const auto& status = side == math::pb::kLeft
                           ? vehicle_signals.left_turn_status
                           : vehicle_signals.right_turn_status;
  return status == VehicleSignalStatus::ON;
}

// Returns true when agent keeps give turning-signal at given side in recent
// frames.
bool KeepTurnSignalAtOneSide(const Agent& agent, math::pb::Side side) {
  // The history horizon to check vehicles' turn-signal.
  const int64_t kHistoryHorizonToCheckMs = 300;

  DCHECK_LE(kHistoryHorizonToCheckMs,
            constants::kDefaultVehicleOutdatedTimeInPoses *
                constants::kPoseIntervalInMs);

  const int64_t current_timestamp = agent.current_timestamp();

  const int64_t observation_begin_time =
      current_timestamp - kHistoryHorizonToCheckMs;

  const auto& history_states = agent.history_states();
  const auto state_begin_iter =
      history_states.FindClosestDataBeforeEqual(observation_begin_time);
  if (state_begin_iter == history_states.end()) {
    // Cannot determine if there is no enough observations.
    return false;
  }

  int num_turn_signal_on = 0;
  int num_total_frames = 0;
  for (auto iter = state_begin_iter; iter != history_states.end(); ++iter) {
    num_turn_signal_on += static_cast<int>(
        IsTurnSignalOnAtOneSide(iter->second.vehicle_signals(), side));
    num_total_frames += 1;
  }
  // Add current frame.
  num_turn_signal_on += static_cast<int>(
      IsTurnSignalOnAtOneSide(agent.current_state().vehicle_signals(), side));
  num_total_frames += 1;

  // In the history buffer most of the frames turn signal is on.
  const float kMinRatioForTurnSignalOn = 0.5F;
  const float turn_signal_on_ratio =
      static_cast<float>(num_turn_signal_on) / num_total_frames;
  return turn_signal_on_ratio > kMinRatioForTurnSignalOn;
}

// Returns true if agent is on right most lane based on its object lane.
bool IsAgentOnRightMostLane(const Agent& agent) {
  const auto& tracked_object = agent.current_state().tracked_object();
  const std::vector<const pnc_map::Lane*>& lanes =
      tracked_object.box_heading() == tracked_object.heading()
          ? agent.object_lanes().normal_lanes
          : agent.object_lanes().inverse_lanes;
  return !lanes.empty() && lanes[0]->IsRightmostVehicleLane();
}

// Returns true when:
// 1) Vehicle keeps give turning-signal at the ego lane sequence side, or
// 2) Vehicle is on the right most lane and keeps give left-turning-signal.
bool KeepTurnSignalAtEgoSide(const SceneSnapshot& scene_snapshot,
                             const Agent& agent) {
  DCHECK_EQ(voy::perception::ObjectType::VEHICLE, agent.object_type());

  const EgoLaneSequence& ego_lane_sequence =
      scene_snapshot.scene_context().ego_lane_sequence();
  if (ego_lane_sequence.lanes.empty()) {
    return false;
  }
  const auto ego_reference_line = scene_snapshot.GetRefLine(
      pnc_map::RefLineInterpolatedRatioType::CENTER_LINE,
      ego_lane_sequence.lanes, pnc_map::LaneDirection::kNormal);
  const math::ProximityQueryInfo agent_proximity =
      ego_reference_line->GetProximity({agent.tracked_object().center_x(),
                                        agent.tracked_object().center_y()},
                                       math::pb::UseExtensionFlag::kAllow);
  switch (agent_proximity.side) {
    case math::pb::kLeft:
      return KeepTurnSignalAtOneSide(agent, IsAgentOnRightMostLane(agent)
                                                ? math::pb::kLeft
                                                : math::pb::kRight);
    case math::pb::kRight:
      return KeepTurnSignalAtOneSide(agent, math::pb::kLeft);
    default:
      return false;
  }
}

pb::TrajectoryGeneratorType GetTrajectoryGeneratorType(
    const ::pb::ModelType model_type) {
  switch (model_type) {
    case ::pb::JOINT_TRAJECTORY_PREDICTION:
      return pb::TrajectoryGeneratorType::JOINT_DL_GENERATOR;

    case ::pb::VEHICLE_VECTORNET_PREDICTION:
      return pb::TrajectoryGeneratorType::VEH_DL_GENERATOR;

    case ::pb::CYCLIST_VECTORNET_PREDICTION:
      return pb::TrajectoryGeneratorType::CYC_DL_GENERATOR;

    case ::pb::PEDESTRIAN_VECTORNET_PREDICTION:
      return pb::TrajectoryGeneratorType::PED_DL_GENERATOR;

    default:
      break;
  }
  DCHECK(false) << "Does not support model type:"
                << ::pb::ModelType_Name(model_type);
  return pb::TrajectoryGeneratorType::UNKNOWN_GENERATOR;
}

// Returns true when agent is in front of ego agent.
bool IsAgentInFrontOfEgo(const Agent& agent, const Agent& ego_agent) {
  const double agent_to_ego_direction = std::atan2(
      agent.tracked_object().center_y() - ego_agent.tracked_object().center_y(),
      agent.tracked_object().center_x() -
          ego_agent.tracked_object().center_x());

  constexpr double kMaxHeadingDiffFromAgentToEgoHeading =
      math::Degree2Radian(100.0);
  const double heading_diff = math::NormalizeMinusPiToPi(
      agent_to_ego_direction - ego_agent.tracked_object().heading());
  return std::abs(heading_diff) < kMaxHeadingDiffFromAgentToEgoHeading;
}

// Return true when agent is in a sector region front of ego or circle radius of
// ego.
bool IsAgentInFrontSectorOfEgo(const Agent& agent, const Agent& ego_agent,
                               const double sector_angle,
                               const double sector_length,
                               const double circle_radius) {
  // Check if the agent is within the sector angle and within the sector length.
  const double distance_to_ego = math::geometry::Distance(
      math::geometry::Point2d{agent.tracked_object().center_x(),
                              agent.tracked_object().center_y()},
      math::geometry::Point2d{ego_agent.tracked_object().center_x(),
                              ego_agent.tracked_object().center_y()});
  const bool is_within_sector_length = distance_to_ego < sector_length;
  // Check if the heading difference is within the sector angle.
  const double agent_to_ego_direction = std::atan2(
      agent.tracked_object().center_y() - ego_agent.tracked_object().center_y(),
      agent.tracked_object().center_x() -
          ego_agent.tracked_object().center_x());
  const double heading_diff = math::NormalizeMinusPiToPi(
      agent_to_ego_direction - ego_agent.tracked_object().box_heading());
  const bool is_within_sector_angle =
      std::abs(heading_diff) < sector_angle / 2.0;

  // Check if the agent is within the circle radius.
  const bool is_within_circle_radius = distance_to_ego < circle_radius;

  return (is_within_sector_length && is_within_sector_angle) ||
         is_within_circle_radius;
}

// Returns the normalized angle difference in range [0, π/2] between two
// headings.
double GetNormalizedAngleDiffZeroToHalfPi(double heading1, double heading2) {
  const double angle_diff = std::abs(math::AngleDiff(heading1, heading2));
  // Normalize angle to [0, π/2] range.
  return std::min(angle_diff, M_PI - angle_diff);
}

// If stationary trajectories take more score than moving trajectories and
// have no risk of interaction with ego, consider the pedestrian as
// |STATIONARY_NOT_TO_MOVE|, else will be |UNKNOWN_STATIONARY_TYPE|.
pb::StationaryIntentionType GetPedStationaryByPredictedTrajectories(
    const VectornetOutput& vectornet_output,
    const voy::TrackedObject& tracked_object,
    const EgoLaneSequence& ego_lane_sequence) {
  constexpr double kMinInLaneClearanceForPed = 2.4;  // m
  // Filter out the pedestrian that is already hard blocking ego lane.
  const bool have_already_hard_blocking_ego_lane = std::any_of(
      ego_lane_sequence.lanes.begin(), ego_lane_sequence.lanes.end(),
      [&tracked_object](const pnc_map::Lane* lane) {
        return !maneuver_util::IsPedHardBlockingLane(
            *lane, kMinInLaneClearanceForPed, tracked_object);
      });
  const bool could_infer_interaction_risk =
      !have_already_hard_blocking_ego_lane &&
      !ego_lane_sequence.has_lane_change && !ego_lane_sequence.lanes.empty();
  double stationary_score = 0.0, start_to_move_score = 0.0;
  double interaction_with_ego_score = 0.0;
  constexpr double kMinInteractionScoreConsiderRisk = 0.3;
  for (const PredictedTrajectory& trajectory : vectornet_output.trajectories) {
    if (utility::IsStationaryTrajectory(trajectory.traj_poses(),
                                        constants::kPredictionHorizonInPoses)) {
      stationary_score += trajectory.score();
    } else {
      start_to_move_score += trajectory.score();
    }
    if (could_infer_interaction_risk) {
      const math::geometry::PolylineCurve2d& lane_sequence_center_line =
          base::CheckAndGetValue(ego_lane_sequence.lane_seq_geometry)
              .center_line;
      if (maneuver_util::IsCrossingLaneCenterline(
              lane_sequence_center_line, trajectory.traj_poses().at(0),
              trajectory.traj_poses().back())) {
        // Judge whether the pedestrian is risk by whether dl predicted
        // trajectories will interactive with ego.
        interaction_with_ego_score += trajectory.score();
      }
    }
  }
  // For risk stationary pedestrian or not stationary pedestrians, use
  // |UNKNOWN_STATIONARY_INTENTION| instead of |STATIONARY_TO_MOVE| in order to
  // use the dl generator to keep prediction continuity for safe.
  // We currently do not add and process |STATIONARY_TO_MOVE| intention for
  // pedestrians.
  return stationary_score >= start_to_move_score &&
                 interaction_with_ego_score <= kMinInteractionScoreConsiderRisk
             ? pb::STATIONARY_NOT_TO_MOVE
             : pb::UNKNOWN_STATIONARY_INTENTION;
}

// Ignores trajectory with less probability.
bool IgnoreTrajWithLessProb(::pb::ModelType model_type, double traj_score) {
  return (model_type == ::pb::ModelType::CYCLIST_VECTORNET_PREDICTION ||
          model_type == ::pb::PEDESTRIAN_VECTORNET_PREDICTION)
             ? traj_score < kMinValidTrajectoryProbabilityForVRU
             : traj_score < FLAGS_min_score_to_keep_vectornet_trajectory;
}

// Post-processes the trajectory pose gear to ensure the gear is reasonable when
// the vehicle is moving slowly, because when vehicle current speed is less than
// 0.3 m/s, prediction model will regard tracked box heading as motion heading.
void PostProcessTrajectoryPoseGear(const Agent& agent,
                                   PredictedTrajectory* trajectory) {
  // Check if the input pointer is not null.
  DCHECK(trajectory != nullptr);
  // The minimum trajectory poses gear change index for slow vehicles. If the
  // vehicle gear change happens after this index or no gear change, we will
  // regard the model has predicted the gear correctly and slow vehicle gear
  // is trustworthy.
  constexpr double kMinTimeForSlowVehiclesGearChangeInSec = 4.0;
  constexpr int kMinGearChangeIndexForSlowVehicles = static_cast<int>(
      kMinTimeForSlowVehiclesGearChangeInSec / constants::kPoseIntervalInSec);
  // Defines the minimum odometer reading for slow-moving vehicles to change
  // gears. If the vehicle travels less than this distance, the gear status will
  // be set as 'unknown'. In the planning view, if the vehicle is moving slowly
  // initially and the odometer reading is less than 0.1 meter, any gear change
  // will be considered as noise.
  constexpr double kMinOdometerForSlowVehiclesGearChangeInMeter = 0.1;
  const int num_poses = trajectory->num_traj_poses();
  DCHECK_EQ(num_poses, constants::kNumOfPredictionTrajectoryPoses);
  if (std::abs(agent.tracked_object().velocity()) <
      constants::kMaxSpeedForSlowVehicleToApplyBoxHeading) {
    int gear_change_index = utility::GetTrajectoryPoseIndexAtFirstGearChange(
        trajectory->traj_poses());
    if (gear_change_index != -1 &&
        gear_change_index < kMinGearChangeIndexForSlowVehicles &&
        trajectory->traj_poses(gear_change_index).odom() <
            kMinOdometerForSlowVehiclesGearChangeInMeter) {
      for (int i = 1; i < gear_change_index; ++i) {
        trajectory->mutable_traj_poses(i).set_gear(
            planner::pb::TrajectoryPose::GEAR_UNKNOWN);
      }
    }
  }
  // Assume that the first pose gear is the same as the second one (the first
  // predicted one).
  trajectory->mutable_traj_poses(0).set_gear(trajectory->traj_poses(1).gear());
}

}  // namespace

bool IsStartToMove(double stm_prob, double ttm_mean, double ttm_sd) {
  // The maximum standard deviation to trust the ttm_mean.
  constexpr double kMaxSdForConfidentPrediction = 3.5;  // seconds
  const bool is_confident = ttm_sd <= kMaxSdForConfidentPrediction;
  // Predict a stationary object is Start-To-Move if it's regressed ttm_mean
  // (in one sd) to reach 0.5m/s is less than this threshold.
  constexpr double kMaxTTMForToMoveAgent = 3.0;  // seconds
  const bool is_to_move_soon = ttm_mean - ttm_sd <= kMaxTTMForToMoveAgent;

  constexpr double kMinSTMProb = 0.5;
  return stm_prob > kMinSTMProb && is_to_move_soon && is_confident;
}

void ComputeAndSetFields(int64_t current_timestamp,
                         const voy::TrackedObject& tracked_object,
                         std::vector<planner::pb::TrajectoryPose>* poses,
                         bool is_set_heading, ::pb::ModelType model_type) {
  DCHECK(poses);
  DCHECK(!poses->empty());
  (*poses)[0].set_timestamp(current_timestamp);
  (*poses)[0].set_z_pos(tracked_object.center_z());

  (*poses)[0].set_speed(tracked_object.velocity());
  (*poses)[0].set_odom(0.0);
  (*poses)[0].set_accel(tracked_object.acceleration());
  if (is_set_heading) {
    bool use_box_heading = false;
    switch (model_type) {
      case ::pb::ModelType::VEHICLE_VECTORNET_PREDICTION:
        use_box_heading =
            tracked_object.velocity() <=
            constants::kMinSpeedForMarginalVehicleToApplyMotionHeading;
        break;
      case ::pb::ModelType::JOINT_TRAJECTORY_PREDICTION:
      case ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE:
        use_box_heading = tracked_object.velocity() <=
                          constants::kMaxSpeedForSlowVehicleToApplyBoxHeading;
        break;
      case ::pb::ModelType::CYCLIST_VECTORNET_PREDICTION:
        use_box_heading = tracked_object.velocity() <=
                          constants::kMaxSpeedForSlowCycToApplyBoxHeading;
        break;
      case ::pb::ModelType::PEDESTRIAN_VECTORNET_PREDICTION:
      default:
        use_box_heading = false;
        break;
    }
    (*poses)[0].set_heading(use_box_heading ? tracked_object.box_heading()
                                            : tracked_object.heading());

    utility::ComputeAndSetHeadings(poses, tracked_object.object_type());
  }

  for (size_t j = 1; j < poses->size(); ++j) {
    (*poses)[j].set_timestamp((*poses)[j - 1].timestamp() +
                              constants::kPoseIntervalInMs);
    (*poses)[j].set_z_pos(tracked_object.center_z());
    // Use central difference to better approximate the derivative.
    // Use backward difference for last pose.
    const int next_index = j + 1 < poses->size() ? j + 1 : j;
    const double delta_x =
        (*poses)[next_index].x_pos() - (*poses)[j - 1].x_pos();
    const double delta_y =
        (*poses)[next_index].y_pos() - (*poses)[j - 1].y_pos();

    (*poses)[j].set_speed(std::hypot(delta_x, delta_y) / kTimeIntervalInS /
                          (next_index - j + 1));
    (*poses)[j].set_odom(
        (*poses)[j - 1].odom() +
        std::hypot((*poses)[j].x_pos() - (*poses)[j - 1].x_pos(),
                   (*poses)[j].y_pos() - (*poses)[j - 1].y_pos()));
  }
  utility::ComputeAndSetAccel(poses);
}

int64_t GetTensorSize(const neural_net::Tensor& tensor) {
  return tensor.num_bytes() / neural_net::DataTypeSize(tensor.data_type());
}

std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>
BuildTensorReferenceMap(
    std::vector<neural_net::Tensor>&& tensors,
    const google::protobuf::RepeatedField<int>& tensor_types) {
  DCHECK_EQ(tensors.size(), tensor_types.size())
      << "Tensor real size:" << tensors.size()
      << " does not match config tensor types size:" << tensor_types.size();

  std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor> tensor_map;
  for (size_t i = 0; i < tensors.size(); ++i) {
    tensor_map.emplace(
        static_cast<pb::ModelConfig::OutputTensorType>(tensor_types[i]),
        std::move(tensors[i]));
  }
  return tensor_map;
}

pb::TrajectoryMotionType InferTrajectoryMotionType(
    PredictedTrajectory& trajectory, const Agent& agent) {
  constexpr double kMinTimeForKTurnDriving = 1.8;  // seconds.
  constexpr int kMinNumForKTurnDriving =
      static_cast<int>(kMinTimeForKTurnDriving / constants::kPoseIntervalInSec);
  constexpr double kMinReverseOdometer = 0.3;        // meters.
  constexpr double kMinReverseOdometerRatio = 0.15;  // ratio.
  constexpr double kFrontPartRatio = 0.5;            // ratio.
  // We define COMPLETE_REVERSE_DRIVING trajectory motion type via following
  // conditions:
  // 1) The predicted trajectory should be non-stationary.
  // 2) The current speed of the agent should be less than 4.0 m/s.
  // 3) All predicted pose should be reverse driving. Thus, trajectory which
  // only contains part of reverse poses is excluded, like K-Turn.

  // We define KTURN_DRIVING trajectory motion type via following
  // conditions:
  // 1) The predicted trajectory should be non-stationary.
  // 2) The current speed of the agent should be less than 4.0 m/s.
  // 3) The predicted trajectory reverse odometer should at least 1.8 seconds.
  // 4) The predicted trajectory reverse odometer should be greater than 0.3
  // meters.
  // 5) The predicted trajectory reverse odometer ratio is greater than 0.15
  // if the reverse driving appear in back part of trajectory.
  if (utility::IsStationaryTrajectory(trajectory.traj_poses(),
                                      constants::kPredictionHorizonInPoses)) {
    return pb::TrajectoryMotionType::NORMAL;
  }
  if (std::abs(agent.tracked_object().velocity()) >
      kMaxCurrentSpeedForReverseDriving) {
    return pb::TrajectoryMotionType::NORMAL;
  }
  // Compute motion headings.
  const std::vector<double> motion_headings = utility::ComputeMotionHeadings(
      trajectory.traj_poses(), agent.tracked_object().object_type());
  DCHECK_EQ(motion_headings.size(), trajectory.traj_poses().size());

  int num_reverse_poses = 0;
  double max_reverse_odometer = 0.0;
  double reverse_odometer = 0.0;
  int reverse_start_index = -1;
  for (size_t i = 1; i < trajectory.traj_poses().size(); ++i) {
    if (std::abs(math::NormalizeMinusPiToPi(trajectory.traj_poses(i).heading() -
                                            motion_headings[i])) >
        kMinHeadingDiffForReverseDrivingPose) {
      num_reverse_poses += 1;
      reverse_odometer +=
          trajectory.traj_poses(i).speed() * constants::kPoseIntervalInSec;
      if (reverse_start_index == -1) {
        reverse_start_index = i;
      }
    } else {
      // When the reverse driving ends, update the max reverse odometer.
      max_reverse_odometer = std::max(max_reverse_odometer, reverse_odometer);
      reverse_odometer = 0.0;
    }
  }
  max_reverse_odometer = std::max(max_reverse_odometer, reverse_odometer);
  const double trajectory_odometer =
      trajectory.traj_poses(constants::kPredictionHorizonInPoses).odom();
  DCHECK(!math::NearZero(trajectory_odometer));
  const double reverse_odom_ratio = max_reverse_odometer / trajectory_odometer;
  if (num_reverse_poses ==
      static_cast<int>(trajectory.traj_poses().size()) - 1) {
    return pb::TrajectoryMotionType::COMPLETE_REVERSE_DRIVING;
  }
  if (num_reverse_poses >= kMinNumForKTurnDriving) {
    // If reverse driving appear in front part of the trajectory.
    if (reverse_start_index <=
            kFrontPartRatio * constants::kPredictionHorizonInPoses &&
        max_reverse_odometer > kMinReverseOdometer) {
      return pb::TrajectoryMotionType::KTURN_DRIVING;
    }
    // If reverse driving appear in back part of the trajectory.
    if (reverse_start_index >
            kFrontPartRatio * constants::kPredictionHorizonInPoses &&
        max_reverse_odometer > kMinReverseOdometer &&
        reverse_odom_ratio > kMinReverseOdometerRatio) {
      return pb::TrajectoryMotionType::KTURN_DRIVING;
    }
  }
  return pb::TrajectoryMotionType::NORMAL;
}

// TODO(Wenkai): Wrap these two utils (InferTrajectoryMotionType and
// InferLargeVehicleTrajectoryMotionType) as a common function to avoid
// rectified predicted trajectory don't have same logic in optimizer.
pb::TrajectoryMotionType InferLargeVehicleTrajectoryMotionType(
    PredictedTrajectory& trajectory, const Agent& agent,
    const Agent& ego_agent) {
  DCHECK_EQ(ego_agent.object_id(), constants::kRobotObjId);
  constexpr double KSectorAngleForLargeVehicleKturn = math::Degree2Radian(90.0);
  constexpr double kSectorLengthForLargeVehicleKturn = 50.0;  // meters.
  constexpr double kCircleRadiusForLargeVehicleKturn = 20.0;  // meters.
  constexpr double kMinHeadingDiffForTargetAgentAndEgo =
      math::Degree2Radian(15.0);  // degrees.
  constexpr double kMinAngleDiffForTargetLaneAndTargetAgent =
      math::Degree2Radian(10.0);                        // degrees.
  constexpr int kMinNumPosesForLargeVehicleKturn = 10;  // poses.
  // Target agent current speed is greater than 4.0 m/s, we regard it as normal.
  // If the agent is not in a sector region front of ego, we regard it as
  // normal.
  // If angle between agent and ego is less than 15 degree, we regard it
  // as normal.
  // If the angle between current lane and agent is less than 10 degree,
  // we regard it as normal.
  if (std::abs(agent.tracked_object().velocity()) >
      kMaxCurrentSpeedForReverseDriving) {
    return pb::TrajectoryMotionType::NORMAL;
  }
  const double angle_diff_to_ego_heading = GetNormalizedAngleDiffZeroToHalfPi(
      agent.tracked_object().box_heading(),
      ego_agent.tracked_object().box_heading());
  if (angle_diff_to_ego_heading < kMinHeadingDiffForTargetAgentAndEgo) {
    return pb::TrajectoryMotionType::NORMAL;
  }
  const std::vector<const pnc_map::Lane*>& object_lanes =
      agent.object_lanes().normal_lanes.empty()
          ? agent.object_lanes().inverse_lanes
          : agent.object_lanes().normal_lanes;
  double target_lane_angle_diff = 0.0;
  if (object_lanes.empty()) {
    // If the agent has no object lane, we regard it satisify this condition.
    target_lane_angle_diff = kMinAngleDiffForTargetLaneAndTargetAgent;
    LOG(ERROR) << "Agent ID: " << agent.object_id()
               << " has no object lane, use default angle diff.";
  } else {
    const pnc_map::Lane& target_lane = *object_lanes.front();
    target_lane_angle_diff = GetNormalizedAngleDiffZeroToHalfPi(
        target_lane.GetLaneDirection(
            math::geometry::Point2d(agent.tracked_object().center_x(),
                                    agent.tracked_object().center_y())),
        agent.tracked_object().box_heading());
  }

  if (target_lane_angle_diff < kMinAngleDiffForTargetLaneAndTargetAgent ||
      !IsAgentInFrontSectorOfEgo(agent, ego_agent,
                                 KSectorAngleForLargeVehicleKturn,
                                 kSectorLengthForLargeVehicleKturn,
                                 kCircleRadiusForLargeVehicleKturn)) {
    return pb::TrajectoryMotionType::NORMAL;
  }
  // Compute motion headings.
  const std::vector<double> motion_headings = utility::ComputeMotionHeadings(
      trajectory.traj_poses(), agent.tracked_object().object_type());
  DCHECK_EQ(motion_headings.size(), trajectory.traj_poses().size());
  int num_reverse_poses = 0;
  for (size_t i = 1; i < trajectory.traj_poses().size(); ++i) {
    if (std::abs(math::NormalizeMinusPiToPi(trajectory.traj_poses(i).heading() -
                                            motion_headings[i])) >
        kMinHeadingDiffForReverseDrivingPose) {
      num_reverse_poses += 1;
    }
  }
  if (num_reverse_poses ==
      static_cast<int>(trajectory.traj_poses().size()) - 1) {
    return pb::TrajectoryMotionType::COMPLETE_REVERSE_DRIVING;
  }
  if (num_reverse_poses >= kMinNumPosesForLargeVehicleKturn) {
    // Post rt event if large vehicle trajectory is a K-turn driving.
    std::string event_msg = strings::StringPrintf(
        "Large vehicle ID: %ld\n"
        "Trajectory score: %.3f\n"
        "Event: Has K-turn signal",
        agent.object_id(), trajectory.score());
    rt_event::PostRtEvent<rt_event::prediction::LargeVehicleHasKturnSignal>(
        event_msg);
    return pb::TrajectoryMotionType::KTURN_DRIVING;
  }
  return pb::TrajectoryMotionType::NORMAL;
}

std::vector<double> ComputeCurvaturesForTrajPoints(
    const AgentState& agent_state,
    const std::vector<planner::pb::TrajectoryPose>& traj_poses) {
  const int num_poses = traj_poses.size();
  DCHECK_GT(num_poses, 2);

  // Smooth the trajectory poses to generate stable curvatures through segment
  // level (1s) cubic polynomial fit and interpolation.
  constexpr int kSmoothSegLength = 10;
  const std::vector<planner::pb::TrajectoryPose>& smooth_traj_poses =
      motion_util::GetSmoothTrajViaPolyInterp(
          traj_poses, polynomial_util::PolynomialType::CUBIC,
          /*segment_length=*/kSmoothSegLength);
  DCHECK_EQ(smooth_traj_poses.size(), num_poses);

  // Compute signed curvature for the first trajectory point.
  const voy::TrackedObject& tracked_object = agent_state.tracked_object();
  const double init_curvature =
      agent_state.is_stationary()
          ? 0.0
          : tracked_object.omega() / tracked_object.velocity();

  // Compute signed menger curvatures for the predicted points.
  std::vector<double> curvatures;
  curvatures.reserve(num_poses);
  curvatures.push_back(init_curvature);
  constexpr int kMaxWinSize = 3;
  for (int i = 1; i < num_poses - 1; ++i) {
    const int wsize = std::min(std::min(i, num_poses - 1 - i), kMaxWinSize);
    const math::geometry::Point2d p1 = {smooth_traj_poses[i - wsize].x_pos(),
                                        smooth_traj_poses[i - wsize].y_pos()};
    const math::geometry::Point2d p2 = {smooth_traj_poses[i].x_pos(),
                                        smooth_traj_poses[i].y_pos()};
    const math::geometry::Point2d p3 = {smooth_traj_poses[i + wsize].x_pos(),
                                        smooth_traj_poses[i + wsize].y_pos()};
    double curvature = math::geometry::MengerCurvature(p1, p2, p3);
    // Use the previous curvature for the current trajectory point if the
    // computed curvature is NaN, which means the three points have duplicates.
    // It is caused by heading flicker.
    if (std::isnan(curvature)) {
      curvature = curvatures.back();
    }
    curvatures.push_back(curvature);
  }
  // Use the previous curvature for the last trajectory point.
  curvatures.push_back(curvatures.back());
  return curvatures;
}

std::vector<std::vector<PredictedTrajectory>> GetPredictedTrajectories(
    const neural_net::Tensor& trajectory_tensor,
    const neural_net::Tensor& score_tensor,
    const float* covariance_tensor_begin,
    const float* box_headings_tensor_begin,
    const float* box_headings_tensor_end, const float* speeds_tensor_begin,
    const float* speeds_tensor_end, const float* accels_tensor_begin,
    const float* accels_tensor_end, const float* steerings_tensor_begin,
    const float* steerings_tensor_end,
    const int32_t* modals_index_int32_tensor_begin,
    const int32_t* modals_index_int32_tensor_end,
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    ::pb::ModelType model_type,
    const google::protobuf::RepeatedPtrField<std::string>& modal_names,
    int num_modals,
    const std::map<int64_t, std::vector<MapObjectKey>>&
        background_map_object_keys,
    int lane_split_factor, int max_num_map_objects_segment) {
  const int num_target_agents = target_agents.size();
  const auto* traj_tensor_begin =
      static_cast<const float*>(trajectory_tensor.data());
  const float* const traj_tensor_end =
      traj_tensor_begin + GetTensorSize(trajectory_tensor);

  const auto* score_tensor_begin =
      static_cast<const float*>(score_tensor.data());
  const float* const score_tensor_end =
      score_tensor_begin + GetTensorSize(score_tensor);
  std::vector<std::vector<PredictedTrajectory>> agent_trajectories(
      num_target_agents);

  // TODO(xiongwei): Refactor this function since it has too many parameters.
  auto trajectory_converter = [&agent_trajectories, covariance_tensor_begin,
                               box_headings_tensor_begin,
                               box_headings_tensor_end, speeds_tensor_begin,
                               speeds_tensor_end, accels_tensor_begin,
                               accels_tensor_end, steerings_tensor_begin,
                               steerings_tensor_end, model_type, num_modals,
                               score_tensor_begin, score_tensor_end,
                               &target_agents, &ego_agent, traj_tensor_begin,
                               traj_tensor_end, &modal_names,
                               modals_index_int32_tensor_begin,
                               modals_index_int32_tensor_end,
                               &background_map_object_keys, lane_split_factor,
                               max_num_map_objects_segment](int agent_idx) {
    const Agent& agent = *target_agents[agent_idx];

    const LocalCoordinates local_coordinates(
        model_type == ::pb::ModelType::JOINT_TRAJECTORY_PREDICTION
            ? target_agents[0]->tracked_object()
            : agent.tracked_object());

    const float* traj_tensor_iter =
        agent_idx * num_modals * kNumFloatsOfOneTrajectory + traj_tensor_begin;
    const float* score_tensor_iter =
        agent_idx * num_modals + score_tensor_begin;
    const int max_score_idx =
        std::max_element(score_tensor_iter, score_tensor_iter + num_modals) -
        score_tensor_iter;

    const float* covariance_tensor_iter = nullptr;
    if (covariance_tensor_begin != nullptr) {
      covariance_tensor_iter =
          covariance_tensor_begin +
          agent_idx * num_modals * kNumFloatsOfCovarianceOfOneTrajectory;
    }

    auto init_tensor_iter = [](const float* tensor_begin,
                               const float* tensor_end, int agent_idx,
                               int num_modals, int num_floats_per_trajectory) {
      DCHECK(tensor_begin && tensor_end);
      return tensor_begin + agent_idx * num_modals * num_floats_per_trajectory;
    };

    const float* box_headings_tensor_iter =
        (box_headings_tensor_begin && box_headings_tensor_end)
            ? init_tensor_iter(box_headings_tensor_begin,
                               box_headings_tensor_end, agent_idx, num_modals,
                               constants::kPredictionHorizonInPoses)
            : nullptr;

    const float* speeds_tensor_iter =
        (speeds_tensor_begin && speeds_tensor_end)
            ? init_tensor_iter(speeds_tensor_begin, speeds_tensor_end,
                               agent_idx, num_modals,
                               constants::kPredictionHorizonInPoses)
            : nullptr;

    const float* accels_tensor_iter =
        (accels_tensor_begin && accels_tensor_end)
            ? init_tensor_iter(accels_tensor_begin, accels_tensor_end,
                               agent_idx, num_modals,
                               constants::kPredictionHorizonInPoses)
            : nullptr;

    const float* steerings_tensor_iter =
        (steerings_tensor_begin && steerings_tensor_end)
            ? init_tensor_iter(steerings_tensor_begin, steerings_tensor_end,
                               agent_idx, num_modals,
                               constants::kPredictionHorizonInPoses)
            : nullptr;

    const int32_t* modals_index_int32_tensor_iter = nullptr;
    if (modals_index_int32_tensor_begin != nullptr) {
      DCHECK(modals_index_int32_tensor_end != nullptr);
      modals_index_int32_tensor_iter =
          modals_index_int32_tensor_begin + agent_idx * num_modals;
    }

    for (int modal_idx = 0; modal_idx < num_modals; ++modal_idx,
             ++score_tensor_iter, ++modals_index_int32_tensor_iter,
             traj_tensor_iter += kNumFloatsOfOneTrajectory,
             box_headings_tensor_iter += constants::kPredictionHorizonInPoses,
             speeds_tensor_iter += constants::kPredictionHorizonInPoses,
             accels_tensor_iter += constants::kPredictionHorizonInPoses,
             steerings_tensor_iter += constants::kPredictionHorizonInPoses,
             covariance_tensor_iter += kNumFloatsOfCovarianceOfOneTrajectory) {
      if (modal_idx != max_score_idx &&
          IgnoreTrajWithLessProb(model_type, *score_tensor_iter)) {
        continue;
      }
      PredictedTrajectory trajectory;
      trajectory.set_trajectory_generator_type(
          GetTrajectoryGeneratorType(model_type));
      trajectory.set_score(*score_tensor_iter);
      if (modal_names.size() == num_modals) {
        trajectory.set_debug_modal_name(modal_names[modal_idx]);
      } else {
        DCHECK_EQ(modal_names.size(), 0)
            << "The size of modal_names (" << modal_names.size()
            << ") must be 0 or equal to num_modals (" << num_modals << ")";
        trajectory.set_debug_modal_name(std::to_string(modal_idx));
      }
      DCHECK_LE(traj_tensor_iter + kNumFloatsOfOneTrajectory, traj_tensor_end);
      DCHECK_LE(score_tensor_iter, score_tensor_end);
      SetTrajectoryPositions(traj_tensor_iter, agent, local_coordinates,
                             &trajectory, model_type);

      if (covariance_tensor_begin != nullptr) {
        DCHECK(covariance_tensor_iter != nullptr);
        DCHECK_GE(covariance_tensor_iter, covariance_tensor_begin);
        SetTrajectoryCovariances(covariance_tensor_iter, local_coordinates,
                                 &trajectory);
      }

      if (box_headings_tensor_begin != nullptr) {
        DCHECK(box_headings_tensor_iter != nullptr);
        DCHECK_GE(box_headings_tensor_iter, box_headings_tensor_begin);
        DCHECK_LE(
            box_headings_tensor_iter + constants::kPredictionHorizonInPoses,
            box_headings_tensor_end);
        OverrideBoxHeadings(box_headings_tensor_iter,
                            constants::kPredictionHorizonInPoses,
                            local_coordinates, &trajectory);
      }

      if (speeds_tensor_begin != nullptr) {
        DCHECK(speeds_tensor_iter != nullptr);
        DCHECK_GE(speeds_tensor_iter, speeds_tensor_begin);
        DCHECK_LE(speeds_tensor_iter + constants::kPredictionHorizonInPoses,
                  speeds_tensor_end);
        OverrideTrajectoryPoseField(
            speeds_tensor_iter, constants::kPredictionHorizonInPoses,
            &trajectory, &planner::pb::TrajectoryPose::set_speed);
      }

      if (accels_tensor_begin != nullptr) {
        DCHECK(accels_tensor_iter != nullptr);
        DCHECK_GE(accels_tensor_iter, accels_tensor_begin);
        DCHECK_LE(accels_tensor_iter + constants::kPredictionHorizonInPoses,
                  accels_tensor_end);
        OverrideTrajectoryPoseField(
            accels_tensor_iter, constants::kPredictionHorizonInPoses,
            &trajectory, &planner::pb::TrajectoryPose::set_accel);
      }

      if (steerings_tensor_begin != nullptr) {
        DCHECK(steerings_tensor_iter != nullptr);
        DCHECK_GE(steerings_tensor_iter, steerings_tensor_begin);
        DCHECK_LE(steerings_tensor_iter + constants::kPredictionHorizonInPoses,
                  steerings_tensor_end);
        OverrideTrajectoryPoseField(
            steerings_tensor_iter, constants::kPredictionHorizonInPoses,
            &trajectory, &planner::pb::TrajectoryPose::set_steering);
      }

      if (modals_index_int32_tensor_begin != nullptr) {
        DCHECK(modals_index_int32_tensor_iter != nullptr);
        DCHECK_GE(modals_index_int32_tensor_iter,
                  modals_index_int32_tensor_begin);
        DCHECK_LE(modals_index_int32_tensor_iter,
                  modals_index_int32_tensor_end);
        trajectory.set_debug_modal_name(
            std::to_string(*modals_index_int32_tensor_iter));
        // TODO(WenkaiYang): Remove this implementation and wrap as a function
        // after int64 is deprecated.
        // Only for vehicle vectornet prediction model, we need to get the
        // background map object keys for target agent.
        if (model_type == ::pb::ModelType::VEHICLE_VECTORNET_PREDICTION) {
          const std::vector<MapObjectKey>& map_object_keys =
              gtl::FindOrDie(background_map_object_keys, agent.object_id());
          DCHECK_LE(map_object_keys.size(),
                    static_cast<std::vector<MapObjectKey>::size_type>(
                        max_num_map_objects_segment));
          const int map_object_key_idx =
              *modals_index_int32_tensor_iter / lane_split_factor;
          // Currently, the unknown modal index will not query corresponding map
          // object id, and use map_object_keys.at(map_object_key_idx) to
          // prevent access violation.
          if (map_object_key_idx < max_num_map_objects_segment) {
            // TODO(WenkaiYang): Will find out the root cause of modal index out
            // of range.
            if (map_object_key_idx >=
                static_cast<int>(map_object_keys.size())) {
              std::string PosesDebugString;
              for (const auto& pose : trajectory.traj_poses()) {
                PosesDebugString += std::string(
                    "x: " + std::to_string(pose.x_pos()) +
                    " y: " + std::to_string(pose.y_pos()) +
                    " box heading: " + std::to_string(pose.heading()) +
                    " speed: " + std::to_string(pose.speed()) + "\n");
              }
              // Post an RT event to indicate the modal index out of range.
              // Will monitor this RT event frequence in road test.
              rt_event::PostRtEvent<
                  rt_event::prediction::VehicleTrajModalIndexOutOfRange>(
                  strings::StringPrintf(
                      "The map_object_key_idx is out of range. "
                      "agent id: %ld current timestamp: %ld "
                      "trajectory modal idx: %d"
                      "trajectory score: %.3f "
                      "map_object_keys size: %zu modal index: %d "
                      "map_object_key_idx: %d max_num_map_objects_segment: %d"
                      "trajectory poses: %s ",
                      agent.object_id(), agent.current_timestamp(), modal_idx,
                      *score_tensor_iter, map_object_keys.size(),
                      *modals_index_int32_tensor_iter, map_object_key_idx,
                      max_num_map_objects_segment, PosesDebugString.c_str()));
              trajectory.set_debug_target_map_object_id(
                  -2);  // Use -2 to indicate the error.
            } else {
              const MapObjectKey& map_object_key =
                  map_object_keys.at(map_object_key_idx);
              trajectory.set_debug_target_map_object_id(
                  map_object_key.map_object_id);
            }
          }
        }
      }
      // We only maintain gear for VEH_DL_GENERATOR since the marginal vehicle
      // model has kinematic constraints.
      if (trajectory.trajectory_generator_type() ==
          pb::TrajectoryGeneratorType::VEH_DL_GENERATOR) {
        SetTrajectoryPoseGear(agent, &trajectory);
        AddRtEventForGearFlickerAndChange(agent, trajectory);
      }
      pb::TrajectoryMotionType trajectory_motion_type =
          pb::TrajectoryMotionType::NORMAL;
      if (agent.tracked_object().length() <
          constants::kLongVehicleLengthThreshold) {
        // Infer trajectory motion type for short vehicles.
        trajectory_motion_type = InferTrajectoryMotionType(trajectory, agent);
      } else {
        // Infer trajectory motion type for large vehicles.
        trajectory_motion_type =
            InferLargeVehicleTrajectoryMotionType(trajectory, agent, ego_agent);
      }
      trajectory.set_trajectory_motion_type(trajectory_motion_type);

      // Compute and set curvatures for the predicted trajectory.
      const std::vector<double> curvatures = ComputeCurvaturesForTrajPoints(
          agent.current_state(), trajectory.traj_poses());
      DCHECK_EQ(curvatures.size(), trajectory.traj_poses().size());
      for (size_t i = 0; i < trajectory.traj_poses().size(); ++i) {
        trajectory.mutable_traj_poses(i).set_curvature(curvatures[i]);
      }

      agent_trajectories[agent_idx].push_back(std::move(trajectory));
    }
  };
  tbb::parallel_for(0, num_target_agents, trajectory_converter);
  return agent_trajectories;
}

void CheckTensorDimensions(const neural_net::Tensor& tensor,
                           const std::vector<int>& expected_dims,
                           const std::string& tensor_name) {
  DCHECK_EQ(tensor.num_dims(), expected_dims.size())
      << "Unexpected ndim for " << tensor_name;

  for (size_t i = 0; i < expected_dims.size(); ++i) {
    if (expected_dims[i] != kShapeToSkipDimensionCheck) {
      DCHECK_EQ(tensor.dim_size(i), expected_dims[i])
          << "Unexpected shape for dimension#" << i << " of tensor "
          << tensor_name;
    }
  }
}

std::map<ObjectId, VectornetOutput> ConvertTensorToCBPLaneChangeResults(
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    int max_num_agents) {
  std::map<ObjectId, VectornetOutput> outputs;
  const int num_target_agents = target_agents.size();
  const std::string& model_name = ::pb::ModelType_Name(
      ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE);
  const neural_net::Tensor& cbp_lane_change_yield_prob =
      output_tensor_map.at(pb::ModelConfig::CBP_LANE_CHANGE_YIELD_PROB);
  CheckTensorDimensions(
      /*tensor=*/cbp_lane_change_yield_prob,
      /*expected_dims=*/
      {num_target_agents, max_num_agents},
      model_name + "." +
          pb::ModelConfig::OutputTensorType_Name(
              pb::ModelConfig::CBP_LANE_CHANGE_YIELD_PROB));

  const neural_net::Tensor& cbp_lane_change_agents_traj =
      output_tensor_map.at(pb::ModelConfig::CBP_LANE_CHANGE_AGENTS_TRAJ);
  CheckTensorDimensions(
      /*tensor=*/cbp_lane_change_agents_traj,
      /*expected_dims=*/
      {num_target_agents, max_num_agents, constants::kPredictionHorizonInPoses,
       2},
      model_name + "." +
          pb::ModelConfig::OutputTensorType_Name(
              pb::ModelConfig::CBP_LANE_CHANGE_AGENTS_TRAJ));

  const neural_net::Tensor& cbp_lane_change_yield_traj =
      output_tensor_map.at(pb::ModelConfig::CBP_LANE_CHANGE_YIELD_TRAJ);
  CheckTensorDimensions(
      /*tensor=*/cbp_lane_change_yield_traj,
      /*expected_dims=*/
      {num_target_agents, max_num_agents, constants::kPredictionHorizonInPoses,
       2},
      model_name + "." +
          pb::ModelConfig::OutputTensorType_Name(
              pb::ModelConfig::CBP_LANE_CHANGE_YIELD_TRAJ));

  const neural_net::Tensor& cbp_lane_change_behind_agents_traj =
      output_tensor_map.at(pb::ModelConfig::CBP_LANE_CHANGE_BEHIND_AGENTS_TRAJ);
  CheckTensorDimensions(
      /*tensor=*/cbp_lane_change_behind_agents_traj,
      /*expected_dims=*/
      {num_target_agents, max_num_agents, constants::kPredictionHorizonInPoses,
       2},
      model_name + "." +
          pb::ModelConfig::OutputTensorType_Name(
              pb::ModelConfig::CBP_LANE_CHANGE_BEHIND_AGENTS_TRAJ));

  const neural_net::Tensor& cbp_lane_change_pass_traj =
      output_tensor_map.at(pb::ModelConfig::CBP_LANE_CHANGE_PASS_TRAJ);
  CheckTensorDimensions(
      /*tensor=*/cbp_lane_change_pass_traj,
      /*expected_dims=*/
      {num_target_agents, max_num_agents, constants::kPredictionHorizonInPoses,
       2},
      model_name + "." +
          pb::ModelConfig::OutputTensorType_Name(
              pb::ModelConfig::CBP_LANE_CHANGE_PASS_TRAJ));

  const auto* const cbp_lane_change_yield_prob_begin =
      static_cast<const float*>(cbp_lane_change_yield_prob.data());
  const auto* const cbp_lane_change_agents_traj_begin =
      static_cast<const float*>(cbp_lane_change_agents_traj.data());
  const auto* const cbp_lane_change_yield_traj_begin =
      static_cast<const float*>(cbp_lane_change_yield_traj.data());
  const auto* const cbp_lane_change_behind_agents_traj_begin =
      static_cast<const float*>(cbp_lane_change_behind_agents_traj.data());
  const auto* const cbp_lane_change_pass_traj_begin =
      static_cast<const float*>(cbp_lane_change_pass_traj.data());

  const int step = max_num_agents * constants::kPredictionHorizonInPoses * 2;

  for (size_t i = 0; i < target_agents.size(); ++i) {
    const Agent& agent = *DCHECK_NOTNULL(target_agents[i]);
    const LocalCoordinates local_coordinates(agent.tracked_object());
    const ObjectId agent_id = agent.object_id();
    VectornetOutput& output = outputs[agent_id];

    int ego_index = GetEgoIndexInBackgroundAgents(agent.object_id());
    output.cbp_lane_change_yield_probability =
        *(cbp_lane_change_yield_prob_begin + i * max_num_agents + ego_index);

    const int offset =
        i * step + ego_index * constants::kPredictionHorizonInPoses * 2;
    SetTrajectoryPositions(
        cbp_lane_change_agents_traj_begin + offset, ego_agent,
        local_coordinates, &output.cbp_lane_change_ego_trajectory,
        ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE);
    SetTrajectoryPositions(
        cbp_lane_change_yield_traj_begin + offset, agent, local_coordinates,
        &output.cbp_lane_change_yield_trajectory,
        ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE);
    SetTrajectoryPositions(
        cbp_lane_change_behind_agents_traj_begin + offset, ego_agent,
        local_coordinates, &output.cbp_lane_change_behind_ego_trajectory,
        ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE);
    SetTrajectoryPositions(
        cbp_lane_change_pass_traj_begin + offset, agent, local_coordinates,
        &output.cbp_lane_change_pass_trajectory,
        ::pb::ModelType::CONDITIONAL_PREDICTION_FOR_LANE_CHANGE);
    output.cbp_lane_change_ego_trajectory.set_trajectory_generator_type(
        pb::TrajectoryGeneratorType::VEH_LANE_CHANGE_CBP_GENERATOR);
    output.cbp_lane_change_ego_trajectory.set_id(
        constants::kCBPLaneChangeEgoTrajectoryId);
    output.cbp_lane_change_yield_trajectory.set_trajectory_generator_type(
        pb::TrajectoryGeneratorType::VEH_LANE_CHANGE_CBP_GENERATOR);
    output.cbp_lane_change_yield_trajectory.set_id(
        constants::kCBPLaneChangeYieldTrajectoryId);
    output.cbp_lane_change_behind_ego_trajectory.set_trajectory_generator_type(
        pb::TrajectoryGeneratorType::VEH_LANE_CHANGE_CBP_GENERATOR);
    output.cbp_lane_change_behind_ego_trajectory.set_id(
        constants::kCBPLaneChangeBehindEgoTrajectoryId);
    output.cbp_lane_change_pass_trajectory.set_trajectory_generator_type(
        pb::TrajectoryGeneratorType::VEH_LANE_CHANGE_CBP_GENERATOR);
    output.cbp_lane_change_pass_trajectory.set_id(
        constants::kCBPLaneChangePassTrajectoryId);
  }
  return outputs;
}

std::map<ObjectId, VectornetOutput> ConvertTensorsToTrajectories(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    const std::map<int64_t, std::vector<MapObjectKey>>&
        background_map_object_keys,
    const std::vector<const Agent*>& target_agents, const Agent& ego_agent,
    ::pb::ModelType model_type, int num_modals,
    const google::protobuf::RepeatedPtrField<std::string>& modal_names,
    int lane_split_factor, int max_num_map_objects_segment,
    const std::vector<bool>* batch_is_ignoring_ego_mask) {
  TRACE_EVENT_SCOPE(prediction,
                    VectornetPredictor_ConvertTensorsToTrajectories);
  // Process trajs and trajs' probs tensor.
  const auto trajectory_tensor_it =
      output_tensor_map.find(pb::ModelConfig::TRAJS);
  const auto score_tensor_it =
      output_tensor_map.find(pb::ModelConfig::TRAJ_PROBS);
  const std::string& model_name = ::pb::ModelType_Name(model_type);

  const int num_target_agents = target_agents.size();

  if (model_type == ::pb::ModelType::JOINT_TRAJECTORY_PREDICTION) {
    // For joint prediction, the first target agent is ego agent and all
    // agents are normalized based on the ego agent's pose.
    DCHECK(!target_agents.empty());
    DCHECK_EQ(target_agents[0]->object_id(), constants::kRobotObjId);

    CheckTensorDimensions(
        /*tensor=*/trajectory_tensor_it->second,
        /*expected_dims=*/
        {1, kShapeToSkipDimensionCheck, num_modals,
         constants::kPredictionHorizonInPoses, 2},
        /*tensor_name=*/model_name + "." +
            pb::ModelConfig::OutputTensorType_Name(pb::ModelConfig::TRAJS));
    DCHECK_LE(num_target_agents, trajectory_tensor_it->second.dim_size(1));

    CheckTensorDimensions(
        /*tensor=*/score_tensor_it->second,
        /*expected_dims=*/{1, kShapeToSkipDimensionCheck, num_modals},
        /*tensor_name=*/model_name + "." +
            pb::ModelConfig::OutputTensorType_Name(
                pb::ModelConfig::TRAJ_PROBS));
    DCHECK_LE(num_target_agents, score_tensor_it->second.dim_size(1));
  } else {
    if (model_type == ::pb::ModelType::CYCLIST_VECTORNET_PREDICTION ||
        model_type == ::pb::ModelType::VEHICLE_VECTORNET_PREDICTION ||
        model_type == ::pb::ModelType::PEDESTRIAN_VECTORNET_PREDICTION) {
      CheckTensorDimensions(
          /*tensor=*/trajectory_tensor_it->second,
          /*expected_dims=*/
          {num_target_agents, num_modals, constants::kPredictionHorizonInPoses,
           2},
          /*tensor_name=*/model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(pb::ModelConfig::TRAJS));
    } else {
      CheckTensorDimensions(
          /*tensor=*/trajectory_tensor_it->second,
          /*expected_dims=*/
          {num_target_agents, num_modals, kNumFloatsOfOneTrajectory},
          /*tensor_name=*/model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(pb::ModelConfig::TRAJS));
    }
    CheckTensorDimensions(/*tensor=*/score_tensor_it->second,
                          /*expected_dims=*/
                          {num_target_agents, num_modals},
                          /*tensor_name=*/model_name + "." +
                              pb::ModelConfig::OutputTensorType_Name(
                                  pb::ModelConfig::TRAJ_PROBS));
  }

  // Parse predicted trajectories.
  std::vector<std::vector<PredictedTrajectory>> agent_trajectories(
      num_target_agents);
  const float* box_headings_tensor_begin = nullptr;
  const float* box_headings_tensor_end = nullptr;
  const float* speeds_tensor_begin = nullptr;
  const float* speeds_tensor_end = nullptr;
  const float* accels_tensor_begin = nullptr;
  const float* accels_tensor_end = nullptr;
  const float* steerings_tensor_begin = nullptr;
  const float* steerings_tensor_end = nullptr;
  const int32_t* modals_index_int32_tensor_begin = nullptr;
  const int32_t* modals_index_int32_tensor_end = nullptr;
  if (trajectory_tensor_it != output_tensor_map.end() &&
      score_tensor_it != output_tensor_map.end()) {
    const float* covariance_buffer_ptr = nullptr;
    const auto covariance_it =
        output_tensor_map.find(pb::ModelConfig::COVARIANCE);
    if (covariance_it != output_tensor_map.end()) {
      covariance_buffer_ptr =
          static_cast<const float*>(covariance_it->second.data());
    }

    // Process box headings tensor.
    const auto box_headings_iter =
        output_tensor_map.find(pb::ModelConfig::BOX_HEADINGS);
    if (box_headings_iter != output_tensor_map.end()) {
      const neural_net::Tensor& box_headings_tensor = box_headings_iter->second;
      CheckTensorDimensions(/*tensor=*/box_headings_tensor,
                            /*expected_dims=*/
                            {static_cast<int>(target_agents.size()), num_modals,
                             constants::kPredictionHorizonInPoses},
                            /*tensor_name=*/
                            model_name + "." +
                                pb::ModelConfig::OutputTensorType_Name(
                                    pb::ModelConfig::BOX_HEADINGS));
      box_headings_tensor_begin =
          static_cast<const float*>(box_headings_tensor.data());
      box_headings_tensor_end =
          box_headings_tensor_begin + GetTensorSize(box_headings_tensor);
    }

    // Process modals index tensor.
    const auto modals_index_iter =
        output_tensor_map.find(pb::ModelConfig::MODALS_INDEX);
    if (modals_index_iter != output_tensor_map.end()) {
      const neural_net::Tensor& modals_index_tensor = modals_index_iter->second;
      CheckTensorDimensions(
          /*tensor=*/modals_index_tensor,
          /*expected_dims=*/
          {static_cast<int>(target_agents.size()), num_modals},
          /*tensor_name=*/
          model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(
                  pb::ModelConfig::MODALS_INDEX));
      DCHECK(modals_index_tensor.data_type() == neural_net::TensorType::kInt32);
      modals_index_int32_tensor_begin =
          static_cast<const int32_t*>(modals_index_tensor.data());
      modals_index_int32_tensor_end =
          modals_index_int32_tensor_begin + GetTensorSize(modals_index_tensor);
    }

    // Process speeds tensor.
    const auto speeds_iter =
        output_tensor_map.find(pb::ModelConfig::TRAJ_SPEEDS);
    if (speeds_iter != output_tensor_map.end()) {
      const neural_net::Tensor& speeds_tensor = speeds_iter->second;
      CheckTensorDimensions(
          /*tensor=*/speeds_tensor,
          /*expected_dims=*/
          {static_cast<int>(target_agents.size()), num_modals,
           constants::kPredictionHorizonInPoses},
          /*tensor_name=*/model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(
                  pb::ModelConfig::TRAJ_SPEEDS));
      speeds_tensor_begin = static_cast<const float*>(speeds_tensor.data());
      speeds_tensor_end = speeds_tensor_begin + GetTensorSize(speeds_tensor);
    }

    // Process accels tensor.
    const auto accels_iter = output_tensor_map.find(pb::ModelConfig::ACCELS);
    if (accels_iter != output_tensor_map.end()) {
      const neural_net::Tensor& accels_tensor = accels_iter->second;
      CheckTensorDimensions(
          /*tensor=*/accels_tensor,
          /*expected_dims=*/
          {static_cast<int>(target_agents.size()), num_modals,
           constants::kPredictionHorizonInPoses},
          /*tensor_name=*/model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(pb::ModelConfig::ACCELS));
      accels_tensor_begin = static_cast<const float*>(accels_tensor.data());
      accels_tensor_end = accels_tensor_begin + GetTensorSize(accels_tensor);
    }

    // Process steerings tensor.
    const auto steerings_iter =
        output_tensor_map.find(pb::ModelConfig::STEERINGS);
    if (steerings_iter != output_tensor_map.end()) {
      const neural_net::Tensor& steerings_tensor = steerings_iter->second;
      CheckTensorDimensions(
          /*tensor=*/steerings_tensor,
          /*expected_dims=*/
          {static_cast<int>(target_agents.size()), num_modals,
           constants::kPredictionHorizonInPoses},
          /*tensor_name=*/model_name + "." +
              pb::ModelConfig::OutputTensorType_Name(
                  pb::ModelConfig::STEERINGS));
      steerings_tensor_begin =
          static_cast<const float*>(steerings_tensor.data());
      steerings_tensor_end =
          steerings_tensor_begin + GetTensorSize(steerings_tensor);
    }

    agent_trajectories = GetPredictedTrajectories(
        trajectory_tensor_it->second, score_tensor_it->second,
        covariance_buffer_ptr, box_headings_tensor_begin,
        box_headings_tensor_end, speeds_tensor_begin, speeds_tensor_end,
        accels_tensor_begin, accels_tensor_end, steerings_tensor_begin,
        steerings_tensor_end, modals_index_int32_tensor_begin,
        modals_index_int32_tensor_end, target_agents, ego_agent, model_type,
        modal_names, num_modals, background_map_object_keys, lane_split_factor,
        max_num_map_objects_segment);
  }

  std::map<ObjectId, VectornetOutput> output;
  DCHECK(!batch_is_ignoring_ego_mask ||
         static_cast<int>(batch_is_ignoring_ego_mask->size()) ==
             num_target_agents);
  for (int i = 0; i < num_target_agents; ++i) {
    auto it =
        output.emplace(target_agents[i]->object_id(), VectornetOutput()).first;
    VectornetOutput& vectorner_output = it->second;
    if (batch_is_ignoring_ego_mask && (*batch_is_ignoring_ego_mask)[i]) {
      DCHECK(vectorner_output.ignore_ego_trajectories.empty());
      vectorner_output.ignore_ego_trajectories =
          std::move(agent_trajectories[i]);
    } else {
      DCHECK(vectorner_output.trajectories.empty());
      vectorner_output.trajectories = std::move(agent_trajectories[i]);
    }
  }
  return output;
}

bool IsCandidateLeadingVehicleOfAgent(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const voy::TrackedObject& candidate_tracked_object,
    const voy::TrackedObject& tailing_tracked_object,
    const pnc_map::PredictionMapService* prediction_map_service,
    bool candidate_is_stationary) {
  // 1) The candidate agent's type must be vehicle.
  if (candidate_tracked_object.object_type() !=
      voy::perception::ObjectType::VEHICLE) {
    return false;
  }

  const std::shared_ptr<const math::geometry::PolylineCurve2d> reference_line =
      prediction_map_service->GetRefLine(
          lane_sequence, pnc_map::RefLineInterpolatedRatioType::CENTER_LINE,
          pnc_map::LaneDirection::kNormal);
  const math::ProximityQueryInfo tailing_agent_proximity =
      reference_line->GetProximity({tailing_tracked_object.center_x(),
                                    tailing_tracked_object.center_y()},
                                   math::pb::UseExtensionFlag::kAllow);
  const math::ProximityQueryInfo candidate_agent_proximity =
      reference_line->GetProximity({candidate_tracked_object.center_x(),
                                    candidate_tracked_object.center_y()},
                                   math::pb::UseExtensionFlag::kAllow);

  // 2) The tailing agent's lateral distance to the reference line should not be
  // too large.
  constexpr double kMinDistanceForLateralGap = 5.0;  // In meters.
  if (tailing_agent_proximity.dist > kMinDistanceForLateralGap) {
    return false;
  }

  // 3) The candidate agent must be ahead of tailing agent.
  // On the reference line.
  const bool is_candidate_agent_ahead_on_reference_line =
      candidate_agent_proximity.arc_length -
          tailing_agent_proximity.arc_length >=
      0.5 *
          (candidate_tracked_object.length() + tailing_tracked_object.length());

  // In agent's direction of motion.
  const double tailing_agent_direction_x =
      std::cos(tailing_tracked_object.heading());
  const double tailing_agent_direction_y =
      std::sin(tailing_tracked_object.heading());
  const double relative_position_x =
      candidate_tracked_object.center_x() - tailing_tracked_object.center_x();
  const double relative_position_y =
      candidate_tracked_object.center_y() - tailing_tracked_object.center_y();

  const double direction_projection =
      relative_position_x * tailing_agent_direction_x +
      relative_position_y * tailing_agent_direction_y;
  const bool is_candidate_agent_ahead_on_heading =
      direction_projection >= 0.5 * (candidate_tracked_object.length() +
                                     tailing_tracked_object.length());

  if (!(is_candidate_agent_ahead_on_reference_line &&
        is_candidate_agent_ahead_on_heading)) {
    return false;
  }

  // 4) The candidate agent must be not far from the centerline.
  // Maximum distance away from center line in meters.
  constexpr double kNearCenterlineThreshold = 2.0;  // In meters.
  const bool is_candidate_agent_near_center_line =
      candidate_agent_proximity.dist <= kNearCenterlineThreshold;
  if (!is_candidate_agent_near_center_line) {
    return false;
  }

  // 5) The relative heading between the lane sequence and candidate.
  // Use box-heading instead of motion-heading for stationary vehicles to
  // avoid heading flipping.
  const double direction_at_proximity_point =
      reference_line->GetInterpTheta(candidate_agent_proximity.arc_length);
  const double candidate_agent_heading =
      candidate_is_stationary ? candidate_tracked_object.box_heading()
                              : candidate_tracked_object.heading();
  const double abs_candidate_agent_heading_diff_with_center_line =
      std::abs(math::NormalizeMinusPiToPi(direction_at_proximity_point -
                                          candidate_agent_heading));
  constexpr double kMinHeadingDiffWithCenterLineInRad =
      M_PI / 18.0;  // 10.0 degree.
  const bool is_candidate_agent_heading_along_lane =
      abs_candidate_agent_heading_diff_with_center_line <=
      kMinHeadingDiffWithCenterLineInRad;

  return is_candidate_agent_heading_along_lane;
}

bool IsForLargeVehicleHead(const Agent& agent) {
  DCHECK(agent.tracked_object().object_type() ==
         voy::perception::ObjectType::VEHICLE);
  return (agent.tracked_object().length() >
          kLengthThresholdForLargeVehicleHead);
}

const Agent* FindLeadingVehicleOfAgent(
    const SceneSnapshot& scene_snapshot,
    const std::vector<const Agent*>& active_agents, const Agent& agent) {
  const std::vector<const pnc_map::Lane*>& ego_lane_sequence =
      scene_snapshot.scene_context().ego_lane_sequence().lanes;
  if (ego_lane_sequence.empty()) {
    return nullptr;
  }
  // Collects candidate leading vehicles which satisfied the constraints.
  std::vector<const Agent*> candidates;
  std::vector<math::geometry::Point2d> candidates_center;
  for (const Agent* active_agent : active_agents) {
    if (IsCandidateLeadingVehicleOfAgent(
            ego_lane_sequence, active_agent->tracked_object(),
            agent.tracked_object(), &scene_snapshot.prediction_map_service(),
            active_agent->current_state().is_stationary())) {
      candidates.emplace_back(active_agent);
      math::geometry::Point2d active_agent_center = {
          active_agent->tracked_object().center_x(),
          active_agent->tracked_object().center_y()};
      candidates_center.emplace_back(active_agent_center);
    }
  }

  if (!candidates.empty()) {
    const math::geometry::Point2d agent_center = {
        agent.tracked_object().center_x(), agent.tracked_object().center_y()};
    const int minDistanceIdx =
        std::min_element(
            candidates_center.begin(), candidates_center.end(),
            [&agent_center](const math::geometry::Point2d& a,
                            const math::geometry::Point2d& b) {
              return math::geometry::ComparableDistance(a, agent_center) <
                     math::geometry::ComparableDistance(b, agent_center);
            }) -
        candidates_center.begin();
    return candidates[minDistanceIdx];
  }
  return nullptr;
}

FlagMessage ShouldSkipTraditionalPrediction(
    const SceneSnapshot& /*scene_snapshot*/, const Agent& agent) {
  if (agent.is_leading_vehicle()) {
    // If leading vehicle is harsh braking, use traditional prediction as
    // fallback strategy.
    if (agent.IsVehicleEstimatedHarshBraking()) {
      return {false, "Leading vehicle of ego is harsh braking"};
    }
  }

  if (IsMovingUnknownObject(agent.tracked_object())) {
    return {false, "moving unknown object"};
  }

  if (std::find(
          agent.potential_anomaly_agent_types().begin(),
          agent.potential_anomaly_agent_types().end(),
          AgentState::AnomalyAgentType::IsAnomalyMovingVehicleInHardBoundary) !=
      agent.potential_anomaly_agent_types().end()) {
    return {false, "anomaly moving vehicle in the hard boundary"};
  }

  if (agent.ScenarioTypes().Has(
          pb::ScenarioType::VEHICLE_POTENTIAL_KTURN_STATIONARY)) {
    return {true, "potential k-turn stationary vehicle"};
  }

  if (agent.current_state().is_stationary()) {
    if (agent.object_type() == voy::perception::ObjectType::CYCLIST &&
        utility::IsEarlyPublishedTrackedObject(agent.tracked_object())) {
      return {true, "early published cyc"};
    }
    return {false, "stationary agent"};
  }

  return {true, ""};
}

FlagMessage ShouldAddVectornetTrajectories(const SceneSnapshot& scene_snapshot,
                                           const Agent& agent) {
  // For pedestrian, not stationary or risk pedestrian also should add
  // vectornet prediction to keep continuity in interactive scenarios.
  if (agent.stationary_intention().stationary_intention_type() !=
          pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE &&
      agent.object_type() == voy::perception::ObjectType::PED) {
    return {true, "not stationary or risk pedestrian"};
  }

  // For vehicles.
  if (agent.ScenarioTypes().Has(
          pb::ScenarioType::VEHICLE_POTENTIAL_KTURN_STATIONARY) ||
      (agent.ScenarioTypes().HasAny(
           {pb::ScenarioType::VEHICLE_EGO_AWARE_STATIONARY,
            pb::ScenarioType::VEHICLE_EGO_AWARE_SLOW}) &&
       agent.stationary_intention().stationary_intention_type() ==
           pb::StationaryIntentionType::STATIONARY_TO_MOVE)) {
    return {true, ""};
  }

  return ShouldSkipTraditionalPrediction(scene_snapshot, agent);
}

MLPlannerOutput ConvertTensorsToMLPlannerOutput(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    const std::vector<RouteFeature>& route_features, const Agent& ego_agent,
    int num_routes, int num_modals, int path_horizon, int traj_horizon,
    ::pb::ModelType model_type) {
  MLPlannerOutput ml_planner_output;

  // Set timestamp.
  const voy::TrackedObject& ego_object = ego_agent.tracked_object();
  ml_planner_output.set_timestamp(ego_object.sync_state().timestamp());
  const LocalCoordinates local_coordinates(ego_object);
  // Get trajectories.
  const neural_net::Tensor& trajectory_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::TRAJS);
  const neural_net::Tensor& trajectory_score_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::TRAJ_PROBS);
  const neural_net::Tensor& even_time_speed_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::TRAJ_SPEEDS);
  const neural_net::Tensor* nullable_trajectory_modal_idx_tensor =
      gtl::FindOrNull(output_tensor_map, pb::ModelConfig::MODALS_INDEX);
  const neural_net::Tensor* nullable_trajectory_ln_std_tensor =
      gtl::FindOrNull(output_tensor_map, pb::ModelConfig::TRAJ_LN_STDS);
  CandidateTrajectoryAndSpeed candidate_trajectories_and_time_even_speeds =
      GetCandidateTrajectoriesAndSpeed(
          trajectory_tensor, trajectory_score_tensor, even_time_speed_tensor,
          nullable_trajectory_modal_idx_tensor,
          nullable_trajectory_ln_std_tensor, ego_object, local_coordinates,
          num_routes, num_modals, traj_horizon,
          /*is_ml_path=*/false, model_type);

  // Get paths.
  const neural_net::Tensor& path_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::PATHS);
  const neural_net::Tensor& path_score_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::PATH_PROBS);

  std::vector<planner::pb::RouteCandidate> candidate_routes;

  if (nullable_trajectory_modal_idx_tensor) {
    DCHECK_EQ(nullable_trajectory_modal_idx_tensor->num_dims(), 2);
    DCHECK_EQ(nullable_trajectory_modal_idx_tensor->dim_size(0), 1);
    DCHECK_EQ(nullable_trajectory_modal_idx_tensor->dim_size(1),
              num_routes * num_modals);
    candidate_routes = GetCandidateRoutes(route_features,
                                          *nullable_trajectory_modal_idx_tensor,
                                          trajectory_score_tensor);
  }

  const neural_net::Tensor& even_space_speed_tensor =
      gtl::FindOrDie(output_tensor_map, pb::ModelConfig::PATH_SPEEDS);
  // TODO(xujia): Refactor GetCandidateTrajectoriesAndSpeed and
  // GetCandidateRoutes to only decode valid results. Currently,
  // num_valid_routes < num_routes = candidates_traj(/route).size().
  CandidateTrajectoryAndSpeed candidate_paths_and_space_even_speeds =
      GetCandidateTrajectoriesAndSpeed(
          path_tensor, path_score_tensor, even_space_speed_tensor,
          nullable_trajectory_modal_idx_tensor,
          /*nullable_trajectory_ln_std_tensor=*/nullptr, ego_object,
          local_coordinates, num_routes, num_modals, path_horizon,
          /*is_ml_path=*/true, model_type);

  // Drop invalid padding route outputs.
  const int num_valid_routes =
      std::max(static_cast<int>(route_features.size()), 1);
  DCHECK_LE(
      num_valid_routes,
      candidate_paths_and_space_even_speeds.candidate_trajectories.size());
  DCHECK_LE(num_valid_routes,
            candidate_paths_and_space_even_speeds.candidate_speeds.size());
  DCHECK_LE(
      num_valid_routes,
      candidate_trajectories_and_time_even_speeds.candidate_speeds.size());
  DCHECK(candidate_routes.size() == 0 ||
         num_valid_routes < static_cast<int>(candidate_routes.size()));

  std::vector<MLPlannerOutputCandidate> candidates;
  candidates.reserve(num_valid_routes);
  for (int i = 0; i < num_valid_routes; ++i) {
    MLPlannerOutputCandidate candidate;
    candidate.trajectories = std::move(
        candidate_trajectories_and_time_even_speeds.candidate_trajectories[i]);
    candidate.paths = std::move(
        candidate_paths_and_space_even_speeds.candidate_trajectories[i]);
    candidate.even_time_speeds = std::move(
        candidate_trajectories_and_time_even_speeds.candidate_speeds[i]);
    candidate.even_space_speeds =
        std::move(candidate_paths_and_space_even_speeds.candidate_speeds[i]);
    if (!candidate_routes.empty()) {
      candidate.lane_sequence_ids = {candidate_routes[i].lane_ids().begin(),
                                     candidate_routes[i].lane_ids().end()};
      candidate.route = std::move(candidate_routes[i]);
    }
    candidates.push_back(std::move(candidate));
  }

  // Parse kinematic signals.
  if (gtl::ContainsKey(output_tensor_map, pb::ModelConfig::JERKS) &&
      gtl::ContainsKey(output_tensor_map, pb::ModelConfig::STEERING_RATES)) {
    ml_planner::ModelBasedKinematicCalculator kinematic_calculator(
        num_routes, num_modals, traj_horizon, &ego_object, &output_tensor_map,
        &local_coordinates);
    for (auto& route_candidate : candidates) {
      for (auto& candidate_trajectory : route_candidate.trajectories) {
        kinematic_calculator.Solve(ego_agent.steering(), &candidate_trajectory);
      }
    }
  }

  if (!route_features.empty()) {
    // TODO(xujia): Delete all the codes related to the turn type.
    ml_planner_output.set_turn(route_features[0].turn);
  }
  ml_planner_output.set_candidates(std::move(candidates));

  const neural_net::Tensor* horn_prob_tensor_ptr =
      gtl::FindOrNull(output_tensor_map, pb::ModelConfig::HORN_PROB);

  if (horn_prob_tensor_ptr) {
    DCHECK(horn_prob_tensor_ptr->data_type() == neural_net::TensorType::kFloat);
    ml_planner_output.set_horn_prob(
        *static_cast<const float*>(horn_prob_tensor_ptr->data()));
  }

  return ml_planner_output;
}

// Gets the attention scores from the tensor if present, otherwise returns an
// empty vector.
const std::vector<double> GetAttentionScoreFromTensor(
    const std::map<pb::ModelConfig::OutputTensorType, neural_net::Tensor>&
        output_tensor_map,
    pb::ModelConfig::OutputTensorType score_name, int num_valid_objects) {
  DCHECK((score_name == pb::ModelConfig::AGENT_ATTN_SCORES) ||
         (score_name == pb::ModelConfig::MAP_ATTN_SCORES));

  std::vector<double> attention_score;
  const neural_net::Tensor* attention_scores_tensor_ptr =
      gtl::FindOrNull(output_tensor_map, score_name);

  if (attention_scores_tensor_ptr) {
    DCHECK(attention_scores_tensor_ptr->data_type() ==
           neural_net::TensorType::kFloat);
    DCHECK_EQ(attention_scores_tensor_ptr->num_dims(), 2);
    DCHECK_EQ(attention_scores_tensor_ptr->dim_size(0), 1);
    DCHECK_LE(num_valid_objects, attention_scores_tensor_ptr->dim_size(1));

    attention_score.reserve(num_valid_objects);
    const auto* tensor_ptr =
        static_cast<const float*>(attention_scores_tensor_ptr->data());

    for (int i = 0; i < num_valid_objects; ++i) {
      attention_score.push_back(static_cast<double>(*tensor_ptr));
      ++tensor_ptr;
    }
  }
  return attention_score;
}

pb::StationaryIntention BuildStationaryIntention(
    const SceneSnapshot& scene_snapshot, const Agent& ego_agent,
    const Agent& agent, const VectornetOutput& vectornet_output) {
  pb::StationaryIntention stationary_intention;

  // Set stationary observation duration by recent agent states.
  stationary_intention.set_observed_stationary_duration_ms(
      agent.observed_stationary_duration_ms());

  // Set first start to move recall timestamp.
  stationary_intention.set_first_start_to_move_recall_ts(
      agent.first_start_to_move_recall_ts());

  // Set whether agent is currently stationary.
  stationary_intention.set_is_currently_stationary(
      agent.current_state().is_stationary());

  if (agent.object_type() == voy::perception::ObjectType::PED) {
    if (agent.current_state().is_stationary()) {
      stationary_intention.set_stationary_intention_type(
          GetPedStationaryByPredictedTrajectories(
              vectornet_output, agent.tracked_object(),
              scene_snapshot.scene_context().ego_lane_sequence()));
    }
    return stationary_intention;
  }

  if (!agent.ScenarioTypes().HasAny(
          {pb::ScenarioType::VEHICLE_EGO_AWARE_STATIONARY,
           pb::ScenarioType::VEHICLE_EGO_AWARE_SLOW}) ||
      vectornet_output.start_to_move_output.ttm_means.empty() ||
      std::any_of(
          agent.tracked_object().attributes().begin(),
          agent.tracked_object().attributes().end(), [](const auto& attribute) {
            return attribute == voy::perception::Attribute::CLUSTER_ONLY_TRACK;
          })) {
    return stationary_intention;
  }

  double prob = vectornet_output.start_to_move_output.stm_prob;
  double mean = vectornet_output.start_to_move_output
                    .ttm_means[constants::kIdxOfTargetSpeedToPublish];
  // TODO(Jian): Change model decoder to make sure no negative means.
  if (mean < 0.0) {
    DLOG(ERROR) << "timestamp:" << agent.current_timestamp()
                << " agent:" << agent.object_id()
                << " wrongly get a negative start-to-move time:" << mean;
    mean = 0.0;
  }
  const double sd =
      vectornet_output.start_to_move_output
          .ttm_standard_deviations[constants::kIdxOfTargetSpeedToPublish];

  // Store raw model prediction results.
  stationary_intention.set_stm_prob(prob);
  stationary_intention.set_time_to_move(mean);
  stationary_intention.set_standard_deviation_of_time_to_move(sd);

  // Vehicle will be set STATIONARY_TO_MOVE when:
  // 1) DL model predicted to-move, or vehicle gives turning-signal at
  //    the ego side in recent frames, and
  // 2) Not a in-front vehicle that accumulated FP to-move predictions in
  //    recent frames.
  const bool is_model_predict_stm = IsStartToMove(prob, mean, sd);
  const bool keep_turn_signal_at_ego_side =
      KeepTurnSignalAtEgoSide(scene_snapshot, agent);
  if (is_model_predict_stm || keep_turn_signal_at_ego_side) {
    if (keep_turn_signal_at_ego_side) {
      // Set rule based fixed TTM mean and sd, to make sure:
      // 1. We can find a best match start-to-move trajectory.
      // 2. The FP-fixing logic works when we check history published stationary
      //    intentions.
      constexpr double kRuleBasedProb = 1.0;
      // 75% samples will be recalled with kRuleBasedMean = 1.4 according to
      // statistic results.
      constexpr double kRuleBasedMean = 1.4;  // seconds
      constexpr double kRuleBasedSd = 0.0;    // seconds
      stationary_intention.set_stm_prob(kRuleBasedProb);
      stationary_intention.set_time_to_move(kRuleBasedMean);
      stationary_intention.set_standard_deviation_of_time_to_move(kRuleBasedSd);
    }
    // We will not publish STATIONARY_TO_MOVE signal as model prediction result,
    // if the agent has following conditions:
    // 1) The agent is in front of ego.
    // 2) The agent stm prob is less than 0.9 or the agent has
    //    VEHICLE_CONSTRUCTION.
    // 3) The agent has too many STM FP in history.

    // The explanation for condition 2:
    // 1) If model give high STM probability, too many STM FP will not be
    // triggered to prevent the STM FN and collision risk like cn14989683.
    // 2) If the agent has VEHICLE_CONSTRUCTION attribute, we will force it to
    // fall into too many STM FP logic. Because the model may give high STM prob
    // to a stationary irregular shape vehicle, and this will lead to ego stuck
    // in a long duration like cn14311191.

    // TODO(Wenkai): Add the comment about too many STM FP conditions after
    // refactor TooManyStartToMoveFPInHistory function.
    // TODO(Wenkai): Refine following logic by using the start-to-move behavior
    // of the leading vehicle to handle corner cases.
    const bool is_in_front_of_ego = IsAgentInFrontOfEgo(agent, ego_agent);
    bool is_fp_stm = false;
    const bool is_vehicle_construction = std::any_of(
        agent.tracked_object().attributes().begin(),
        agent.tracked_object().attributes().end(), [](const auto& attribute) {
          return attribute == voy::perception::Attribute::VEHICLE_CONSTRUCTION;
        });
    if (prob < kConfidentProbForStartToMove || is_vehicle_construction) {
      is_fp_stm = TooManyStartToMoveFPInHistory(agent);
      // Add RT event for too many STM FP.
      // TODO(Wenkai): Add RT event to monitor STM signal with duration larger
      // than kMinStartToMoveDurationMs which may cause ego stuck.
      if (is_fp_stm) {
        rt_event::PostRtEvent<
            rt_event::prediction::TooManyStartToMoveFpInHistory>(
            strings::StringPrintf(
                "timestamp:%ld agent:%ld too many start-to-move FP in history.",
                agent.current_timestamp(), agent.object_id()));
      }
    }
    const bool is_fp_stm_in_front_of_ego = is_fp_stm && is_in_front_of_ego;

    if (!is_fp_stm_in_front_of_ego) {
      stationary_intention.set_stationary_intention_type(
          pb::STATIONARY_TO_MOVE);
      stationary_intention.set_stm_trajectory_id(
          constants::kVehicleStartToMoveTrajectoryId);
    } else {
      // For agent under VEHICLE_EGO_AWARE_SLOW, we should not set NOT_TO_MOVE
      // intention for it.
      if (agent.ScenarioTypes().Has(
              pb::ScenarioType::VEHICLE_EGO_AWARE_STATIONARY)) {
        stationary_intention.set_stationary_intention_type(
            pb::STATIONARY_NOT_TO_MOVE);
      }
    }
  } else {
    // For agents under VEHICLE_EGO_AWARE_STATIONARY and have predicted STM
    // output, we should set STATIONARY_NOT_TO_MOVE.
    if (agent.ScenarioTypes().Has(
            pb::ScenarioType::VEHICLE_EGO_AWARE_STATIONARY)) {
      stationary_intention.set_stationary_intention_type(
          pb::STATIONARY_NOT_TO_MOVE);
    }
  }

  return stationary_intention;
}

void SetLatLongStandardDeviation(
    double heading, planner::pb::TrajectoryPoseUncertainty* uncertainty) {
  // Given x, y covariance matrix and agent's heading, sets lateral and
  // longitudinal standard deviations.
  const Eigen::Matrix2d cov_xy{
      {uncertainty->x_pos_var(), uncertainty->xy_pos_cov()},
      {uncertainty->xy_pos_cov(), uncertainty->y_pos_var()}};
  // Clock-wisely rotates covariance matrix by heading.
  const Eigen::Matrix2d rotation_matrix{
      {std::cos(heading), std::sin(heading)},
      {-std::sin(heading), std::cos(heading)}};

  // After the rotation, the new x axis is longitudinal axis,
  // and the new y axis is lateral axis.
  Eigen::Matrix2d cov_lat_long =
      rotation_matrix * cov_xy * rotation_matrix.transpose();

  // Prevents division by zero.
  cov_lat_long(0, 0) = std::max(cov_lat_long(0, 0), math::constants::kEpsilon);
  cov_lat_long(1, 1) = std::max(cov_lat_long(1, 1), math::constants::kEpsilon);

  // Calculates conditional variance along lateral and longitudinal axes.
  const double cov = cov_lat_long(1, 0);
  const double var_long = cov_lat_long(0, 0) - cov * cov / cov_lat_long(1, 1);
  const double var_lat = cov_lat_long(1, 1) - cov * cov / cov_lat_long(0, 0);
  uncertainty->set_long_sd(std::sqrt(var_long));
  uncertainty->set_lat_sd(std::sqrt(var_lat));
}

void SetTrajectoryPoseGear(const Agent& agent,
                           PredictedTrajectory* trajectory) {
  const int num_poses = trajectory->num_traj_poses();
  // Compute motion headings.
  const std::vector<double> motion_headings = utility::ComputeMotionHeadings(
      trajectory->traj_poses(), agent.tracked_object().object_type());
  DCHECK_EQ(num_poses, motion_headings.size());
  for (int i = 0; i < num_poses; ++i) {
    planner::pb::TrajectoryPose& pose = trajectory->mutable_traj_poses(i);
    const double motion_heading = motion_headings[i];
    const double box_heading = pose.heading();
    if (std::abs(math::NormalizeMinusPiToPi(motion_heading - box_heading)) <
        kMinHeadingDiffForReverseDrivingPose) {
      pose.set_gear(planner::pb::TrajectoryPose::GEAR_DRIVE);
    } else {
      pose.set_gear(planner::pb::TrajectoryPose::GEAR_REVERSE);
    }
  }
  PostProcessTrajectoryPoseGear(agent, trajectory);
}

void AddRtEventForGearFlickerAndChange(const Agent& agent,
                                       const PredictedTrajectory& trajectory) {
  // The index in the trajectory from which to start checking for gear flicker.
  constexpr int kStartPoseIndexToCheckGearFlicker = 2;
  // Define minimum gear changes within a trajectory.
  constexpr int kMinGearChanges = 2;
  DCHECK_GE(trajectory.num_traj_poses(), 2);
  const int num_poses = trajectory.num_traj_poses();
  int num_gear_changes = 0;
  for (int i = 1; i < num_poses; ++i) {
    const auto& pose = trajectory.traj_poses(i);
    const auto& prev_pose = trajectory.traj_poses(i - 1);
    if (pose.gear() != prev_pose.gear()) {
      num_gear_changes += 1;
    }
    // Add RT event for gear flicker, if 3 continuous poses have different
    // gears then add RT event.
    if (i >= kStartPoseIndexToCheckGearFlicker &&
        trajectory.traj_poses(i - 2).gear() != prev_pose.gear() &&
        prev_pose.gear() != pose.gear()) {
      rt_event::PostRtEvent<rt_event::prediction::TrajectoryHasPoseGearFlicker>(
          strings::StringPrintf(
              "timestamp:%ld agent:%ld trajectory score:%f pose index:%d has "
              "gear flicker in trajectory.",
              agent.current_timestamp(), agent.object_id(), trajectory.score(),
              i));
    }
  }
  // Add RT event if gear changes more than twice.
  if (num_gear_changes >= kMinGearChanges) {
    rt_event::PostRtEvent<
        rt_event::prediction::TrajectoryHasMultipleGearChange>(
        strings::StringPrintf(
            "timestamp:%ld agent:%ld trajectory score:%f has %d gear changes.",
            agent.current_timestamp(), agent.object_id(), trajectory.score(),
            num_gear_changes));
  }
}

bool IsMovingUnknownObject(const voy::TrackedObject& tracked_object) {
  if (tracked_object.object_type() != voy::perception::ObjectType::UNKNOWN) {
    return false;
  }
  return tracked_object.velocity() >= kMinSpeedForMovingUnknownObject;
}

// The map object selection design document is in
// https://cooper.didichuxing.com/docs2/document/2203585568698
double CalculateDynamicEllipseDistanceBasedOnSpeed(
    const math::geometry::Point2d& target_agent_position,
    double target_agent_heading, double target_agent_speed,
    const math::geometry::PolylineCurve2d& polyline_curve) {
  // Get the position of map object according to the projection of the target
  // agent.
  const auto& agent_proximity = polyline_curve.GetProximity(
      target_agent_position, math::pb::UseExtensionFlag::kForbid);
  // Transform the position of the object to the target agent's coordinate
  // system, target agent box heading is longitudinal axis.
  const double relative_x = agent_proximity.x - target_agent_position.x();
  const double relative_y = agent_proximity.y - target_agent_position.y();
  const double longitudinal_distance =
      relative_x * std::cos(target_agent_heading) +
      relative_y * std::sin(target_agent_heading);
  const double lateral_distance = -relative_x * std::sin(target_agent_heading) +
                                  relative_y * std::cos(target_agent_heading);
  // Calculate the Ellipse distance between the target agent and the object.
  double ellipse_distance = 0.0;
  if (longitudinal_distance < 0.0) {
    // If the object is located behind the target agent, compute the
    // Euclidean distance between the target agent and the point represented by
    // (longitudinal_distance, lateral_distance).
    ellipse_distance = std::hypot(longitudinal_distance, lateral_distance);
  } else {
    // If the object is in front of the target agent, compute the distance
    // from the target agent to the point (longitudinal_distance,
    // lateral_distance) within an ellipse. The ellipse has an x-radius of
    // (target_agent_speed * kSpeedCoefficient) and a y-radius of 1.
    ellipse_distance =
        std::sqrt(std::pow(longitudinal_distance, 2) /
                      (std::pow(target_agent_speed * kSpeedCoefficient, 2) +
                       kStationaryConstant) +
                  std::pow(lateral_distance, 2));
  }

  return ellipse_distance;
}

// Get the indices of top k elements in vector
const std::vector<int> GetTopKScoresIndices(const std::vector<double>& scores,
                                            int k) {
  if (scores.empty()) {
    return {};
  }

  k = std::min(k, static_cast<int>(scores.size()));

  std::vector<int> indices(scores.size());
  std::iota(indices.begin(), indices.end(), 0);

  std::partial_sort(
      indices.begin(), indices.begin() + k, indices.end(),
      [&scores](int i1, int i2) { return scores[i1] > scores[i2]; });

  // Return the top k indices
  return std::vector<int>(indices.begin(), indices.begin() + k);
}

// Gets a sequence of numbers, starting from a given number, and increments by
// 1, and stops before a specified number.
const std::vector<int> GetRangeIndices(int start, int end) {
  DCHECK(end >= start);

  std::vector<int> indices(end - start);
  std::iota(indices.begin(), indices.end(), start);
  return indices;
}

}  // namespace vectornet_util
}  // namespace prediction
