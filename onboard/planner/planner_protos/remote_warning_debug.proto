syntax = "proto3";

package planner.pb;

import "planner_protos/remote_warning_signal.proto";
import "voy_protos/remote_warning.proto";

// The debug for remote warning signal tracker.
// Next Available ID: 8.
message RemoteWarningSignalTrackerDebug {
  // The task code of the warning tracker.
  voy.remote_assist.TaskCode.Code task_code = 1;
  // In warning statue.
  bool is_in_warning = 2;
  // The last detection.
  bool last_detect_result = 3;
  // The last statue changed seq id.
  int64 last_changed_seq_id = 4;
  // The last in warning timestamp.
  int64 last_in_warning_timestamp = 5;
  // The last request timestamp.
  int64 last_request_timestamp = 6;
  // Enable request.
  bool is_enabled_request = 7;
}

// The debug message about remote warning detector.
// Next Available ID: 10.
message RemoteWarningDetectorDebug {
  reserved 2, 3, 4, 5, 6, 8;
  reserved "in_warning", "last_has_warning_object", "last_changed_seq_id",
      "signal_id", "last_request_timestamp", "tracker_debugs";
  // The seq id of this detector.
  int64 seq_id = 1;
  // The warning signal.
  RemoteWarningSignal signal = 7;
  // Debug messages of warning signal trackers.
  map<int64, RemoteWarningSignalTrackerDebug> trackers = 9;
}
