# proto-file: varch/protos/vnode/vgraph_conf.proto
# proto-message: VForestConfig
vgraph {
  mode: kReality
  scheduler {
    worker_num: 56
    cpu_affinity: kRange
    worker: [
      {
        ids: [0]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [0]
      },
      {
        ids: [1]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [1]
      },
      {
        ids: [2]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [2]
      },
      {
        ids: [3]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [3]
      },
      {
        ids: [4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,52,53,54,55]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,52,53,54,55]
      },
      {
        ids: [47,48,49,50]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [47,48,49,50]
      },
      {
        ids: [51]
        os_sched_policy: kSchedOther
        os_prio: 0
        stealing_from: [51]
      }
    ]
    group: [
      { name: "hdmap_group", workers: "0" },
      { name: "lidar_detection", workers: "1" },
      { name: "bevfusion", workers: "2" },
      { name: "detection_refinement", workers: "3" },

      { name: "camera_subscription_camera_1", workers: "11" },
      { name: "camera_subscription_camera_2", workers: "25" },
      { name: "camera_subscription_camera_3", workers: "27" },
      { name: "camera_subscription_camera_4", workers: "4" },
      { name: "camera_subscription_camera_5", workers: "5" },
      { name: "camera_subscription_camera_6", workers: "6" },
      { name: "camera_subscription_camera_7", workers: "7" },
      { name: "camera_subscription_camera_short_3", workers: "8" },
      { name: "lane_detection", workers: "9" },
      { name: "camera_soiling_detection", workers: "10" },
      { name: "traffic_light_detection", workers: "12" },
      { name: "camera_object_detection_1", workers: "13" },
      { name: "camera_object_detection_4", workers: "14" },
      { name: "camera_object_detection_5", workers: "15" },
      { name: "camera_object_detection_6", workers: "16" },
      { name: "camera_object_detection_237", workers: "17-19" },
      { name: "camera_object_detection_short_3", workers: "20" },
      { name: "radar_cluster", workers: "21" },
      { name: "obstacle_detection", workers: "22" },
      { name: "obstacle_generation", workers: "22" },
      { name: "occlusion_map", workers: "23" },
      { name: "camera_refinement", workers: "24" },
      { name: "occupancy_range_view_sensing", workers: "26" },
      { name: "detection_tracker", workers: "28" },
      { name: "sensing_occupancy_point_cloud_processor", workers: "29" },
      { name: "curb_detection", workers: "30" },
      { name: "obstacle_post_processing", workers: "31" },
      { name: "occupancy_model", workers: "32" },
      { name: "post_processing", workers: "33" },
      { name: "occupancy_post_processing_sensing", workers: "33" },
      { name: "occupancy_grid_sensing", workers: "34" },
      { name: "occupancy_camera_detector", workers: "52"},
      { name: "sensing_occupancy_range_view_detector", workers: "54"},
      { name: "occupancy_camera_sensing", workers: "35" },
      { name: "camera_subscription_camera_infrared_1", workers: "36" },
      { name: "camera_subscription_camera_gen4_infrared_1", workers: "36" },
      { name: "camera_object_detection_infrared_1", workers: "37" },
      { name: "camera_subscription_camera_short_1", workers: "38" },
      { name: "camera_subscription_camera_short_2", workers: "39" },
      { name: "camera_subscription_camera_short_4", workers: "40" },
      { name: "camera_object_detection_short_1", workers: "41" },
      { name: "camera_object_detection_short_2", workers: "42" },
      { name: "camera_object_detection_short_4", workers: "43" },
      { name: "emergency_sensing_nodelet", workers: "44" },
      { name: "lidar_pv_abnormal_detection", workers: "45" },
      { name: "lidar_abnormal_detection", workers: "46" },
      { name: "segmentation_filter", workers: "53" },
      { name: "publish_group", workers: "47-50" },
      { name: "camera_resized_subscription", workers: "51"},
      { name: "camera_subscription_camera_short_gen4", workers: "38-40" },
      # TODO(mengzhaoxin): Check the worker configuration of camera subscription vnodes.
      { name: "camera_subscription_camera_1_crop", workers: "55" }
    ]
  }

  vnode: [
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "SensingPreCreateVNode"
      name: "sensing_pre_create_vnode"
      sub {mode: kCallOnce}
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "HdmapVNode"
      name: "sensing_hdmap"
      sub {
        mode: kPeriodic
        periodic_interval: 10
        topic: [
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/simulation/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "tracker_message_pub"
          topic: "/sensing/hdmap/tracker_message"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "hdmap_pose_pub"
          topic: "/sensing/hdmap/pose"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "vnode_name", value: "sensing" }]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "BaseLayerVNode"
      name: "sensing_base_layer"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/sensing/hdmap/pose"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }]
      }
      params: [{ key: "vnode_name", value: "sensing" }]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "MapTrackerVNode"
      name: "sensing_map_tracker"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/sensing/hdmap/tracker_message"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }]
      }
      params: [{ key: "vnode_name", value: "sensing" }]
      group: "hdmap_group"
      prio: 1
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "DynamicDataVNode"
      name: "sensing_dynamic_data_vnode"
      sub {
        mode: kAnyTrigger
        trigger_num: 2
        topic: [
          {
            topic: "/realtime_map/traffic_light_phase"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/realtime_map/traffic_light_changes" # Temporary traffic light
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }
      params: [
        { key: "vnode_name", value: "sensing" }
      ]
      group: "hdmap_group"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_1" }
      ]

      group: "camera_subscription_camera_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_2" }
      ]

      group: "camera_subscription_camera_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_3" }
      ]

      group: "camera_subscription_camera_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_4" }
      ]

      group: "camera_subscription_camera_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_5"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_105"
            transport_option: { type: kUDP }
            queue_size: 2
          }, {
            topic: "/compressed_camera_telescopic_105"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_105"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_5"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_5" }
      ]

      group: "camera_subscription_camera_5"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_6"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_6"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_6" }
      ]

      group: "camera_subscription_camera_6"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_7"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_7"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_7" }
      ]

      group: "camera_subscription_camera_7"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN3_SHORT_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_3" }
      ]

      group: "camera_subscription_camera_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN4_SHORT_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_3" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_INFRARED_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_infrared_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_infrared_1_placeholder_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_infrared_1_placeholder_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_infrared_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_INFRARED_1" }
      ]

      group: "camera_subscription_camera_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN4_INFRARED_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_infrared_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_gen4_infrared_1_placeholder_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_gen4_infrared_1_placeholder_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_infrared_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_INFRARED_1" }
      ]

      group: "camera_subscription_camera_gen4_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN3_SHORT_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_1" }
      ]

      group: "camera_subscription_camera_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN4_SHORT_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_1" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN3_SHORT_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_2" }
      ]

      group: "camera_subscription_camera_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN4_SHORT_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_2" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN3_SHORT_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_4" }
      ]

      group: "camera_subscription_camera_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_GEN4_SHORT_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_short_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_4" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_1" }
      ]

      group: "camera_subscription_camera_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_102"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_2" }
      ]

      group: "camera_subscription_camera_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_103"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_3" }
      ]

      group: "camera_subscription_camera_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_104"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_4" }
      ]

      group: "camera_subscription_camera_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_5"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_105"
            transport_option: { type: kUDP }
            queue_size: 2
          }, {
            topic: "/compressed_camera_telescopic_105"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_105"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_5"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_5" }
      ]

      group: "camera_subscription_camera_5"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_6"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_106"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_6"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_6" }
      ]

      group: "camera_subscription_camera_6"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_7"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_telescopic_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_107"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_7"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_7" }
      ]

      group: "camera_subscription_camera_7"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN3_SHORT_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_3" }
      ]

      group: "camera_subscription_camera_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN4_SHORT_3"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_3"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_3"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_3" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_INFRARED_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_infrared_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_infrared_1_placeholder_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_infrared_1_placeholder_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_infrared_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_INFRARED_1" }
      ]

      group: "camera_subscription_camera_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN4_INFRARED_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_infrared_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_gen4_infrared_1_placeholder_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_gen4_infrared_1_placeholder_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_infrared_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_INFRARED_1" }
      ]

      group: "camera_subscription_camera_gen4_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN3_SHORT_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_1" }
      ]

      group: "camera_subscription_camera_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN4_SHORT_1"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_1"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_1"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_1" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN3_SHORT_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_2" }
      ]

      group: "camera_subscription_camera_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN4_SHORT_2"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_2"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_2"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_2" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN3_SHORT_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen3_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen3_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen3_video_frame_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_4" }
      ]

      group: "camera_subscription_camera_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSensorExtractorVNode"
      name: "camera_sensor_extractor_vnode_CAMERA_GEN4_SHORT_4"
      sub {
        mode: kAnyTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/raw_camera_gen4_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_gen4_telescopic_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_gen4_video_frame_short_4"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_sensor_extractor_pub_short_4"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_4" }
      ]

      group: "camera_subscription_camera_short_gen4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraResizedSubscriptionVNode"
      name: "sensing_camera_resized_subscription"
      sub {
        mode: kXTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          }
        ]
      }
      pub: [
        {
        name: "perception_camera_resized_pub"
        topic: "/perception_camera_resized_pub"
        transport_option: { type: kUDP }
      }
      ]
      group: "camera_resized_subscription"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraCropSubscriptionVNode"
      name: "sensing_camera_subscription_CAMERA_1_CROP"
      sub {
        mode: kAnyTrigger
        trigger_num: 4
        topic: [
          {
            topic: "/raw_camera_telescopic_101_crop"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/raw_camera_telescopic_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/compressed_camera_telescopic"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/camera_video_frame_101"
            transport_option: { type: kUDP }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          }
        ]
      }

      pub: [{
        name: "pub_perception_camera"
        topic: "/sensing/intra/camera_subscription_1_crop"
        transport_option: { type: kIntraProcess }
      }]

      params: [
        { key: "origin_camera", value: "CAMERA_1" },
        { key: "cropped_camera", value: "CAMERA_1_CROP" }
      ]
      group: "camera_subscription_camera_1_crop"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "LaneDetectionVNode"
      name: "sensing_lane_detection"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
            name: "camera_lane_detect"
            topic: "/camera_lane_detect"
            transport_option: { type: kUDP }
            timeout: 300
        },
        {
            name: "speed_bump_list_detect"
            topic: "/speed_bump_list_detect"
            transport_option: { type: kUDP }
            timeout: 300
        },
        {
            name: "stop_line_list"
            topic: "/stop_line_list_detect"
            transport_option: { type: kUDP }
            timeout: 300
        }
      ]

      group: "lane_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraSoilingDetectionVNode"
      name: "sensing_camera_soiling_detection"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/gateway"
            transport_option: { type: kUDP }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "camera_soiling_detect"
          topic: "/camera_soiling_detect"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_soiling_event"
          topic: "/system_event"
          transport_option: { type: kUDP }
        }
      ]

      group: "camera_soiling_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "Gen3SensingScanConversionProxyVNode"
      name: "sensing_scan_conversion_proxy"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing_scan_conversion/scan_msg"
            transport_option: { type: kUDP }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "sensing_scan_msg_pub"
          topic: "/sensing/intra/scan_conversion"
          transport_option: { type: kIntraProcess }
        }
      ]
      group: "lidar_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "Gen4SensingScanConversionProxyVNode"
      name: "gen4_sensing_scan_conversion_proxy"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/scan_conversion/scan_msg"
            transport_option: { type: kUDP }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "sensing_scan_msg_pub"
          topic: "/sensing/intra/scan_conversion"
          transport_option: { type: kIntraProcess }
        }
      ]
      group: "lidar_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "EmergencySensingVNode"
      name: "emergency_sensing_nodelet"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/lidar_segmentation"
              is_master_trigger : true
            },
            {
              topic: "/perception/intra/occupancy_model_result"
              timeout: 20
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/lidar_segmentation"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/occupancy_model_result"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/lidar_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }
      pub: [
        {
          name: "emergency_sensing_result_pub"
          topic: "/perception/emergency_sensing_result"
          transport_option: { type: kUDP }
        }
      ]
      params: [{ key: "node_name", value: "sensing" }]
      group: "emergency_sensing_nodelet"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "TrafficLightDetectionVNode"
      name: "sensing_traffic_light_detection"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/v2x/traffic_light_detect"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/teleassist/traffic_light"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/realtime_map/traffic_lights"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/camera_soiling_detect"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/perception/intra/panel_objects_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/panel_objects_2"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/panel_objects_3"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/panel_objects_7"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_object_list_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/perception/intra/camera_object_list_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/simulation/pose"
            transport_option: { type: kUDP }
            queue_size: 5
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [{
        name: "traffic_light_detect"
        topic: "/traffic_light_detect"
        transport_option: { type: kUDP }
        timeout: 300
      }]

      group: "traffic_light_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CurbDetectionVNode"
      name: "sensing_curb_detection"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "lidar_detected_curb_list"
          topic: "/perception/lidar_detected_curb_list"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "processed_map_list"
          topic: "/perception/processed_map_detection_list"
          transport_option: { type: kUDP }
          timeout: 400
        }
      ]

      group: "curb_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_1"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_1"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_1"}
      ]
      group: "camera_object_detection_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_1"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_1"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_1"}]
      group: "camera_object_detection_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskBatchVNode"
      name: "camera_multi_task_237"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list_2"
          topic: "/sensing/intra/intra_object_2d_list_2"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "intra_object_2d_list_3"
          topic: "/sensing/intra/intra_object_2d_list_3"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "intra_object_2d_list_7"
          topic: "/sensing/intra/intra_object_2d_list_7"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "intra_panel_objects_2"
          topic: "/perception/intra/panel_objects_2"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "intra_panel_objects_3"
          topic: "/perception/intra/panel_objects_3"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "intra_panel_objects_7"
          topic: "/perception/intra/panel_objects_7"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_2,CAMERA_3,CAMERA_7"},
               { key: "model_group", value: "normal_short" }]
      group: "camera_object_detection_237"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerBatchVNode"
      name: "sensing_camera_object_detection_237"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_2"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/intra_object_2d_list_3"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/intra_object_2d_list_7"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/intra_object_2d_list_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/intra_object_2d_list_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra_2"
          topic: "/perception/intra/camera_object_list_2"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "camera_object_list_intra_3"
          topic: "/perception/intra/camera_object_list_3"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "camera_object_list_intra_7"
          topic: "/perception/intra/camera_object_list_7"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_2,CAMERA_3,CAMERA_7"},
        { key: "model_group", value: "camera237" }
      ]
      group: "camera_object_detection_237"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_4"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_4"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_4"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_4"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_4"}]
      group: "camera_object_detection_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_5"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_5"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_5"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_5"}
      ]
      group: "camera_object_detection_5"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_5"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_5"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_5"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_5"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_5"}]
      group: "camera_object_detection_5"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_6"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_6"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_6"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_6"}
      ]
      group: "camera_object_detection_6"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_6"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_6"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_6"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_6"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_6"}]
      group: "camera_object_detection_6"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_short_3"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_3"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 10
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 40
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_3"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_3"}
      ]
      group: "camera_object_detection_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_short_1"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_1"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 30
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_1"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_1"}
      ]
      group: "camera_object_detection_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_short_2"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_2"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 20
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_2"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_2"}
      ]
      group: "camera_object_detection_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_short_4"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_4"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_4"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN3_SHORT_4"}
      ]
      group: "camera_object_detection_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_gen4_short_3"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_3"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_3"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_3"}
      ]
      group: "camera_object_detection_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_gen4_short_1"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_1"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_1"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_1"}
      ]
      group: "camera_object_detection_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_gen4_short_2"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_2"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_2"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_2"}
      ]
      group: "camera_object_detection_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_gen4_short_4"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_short_4"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 60
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 70
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_short_4"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN4_SHORT_4"}
      ]
      group: "camera_object_detection_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_short_3"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_3"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_3"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_3"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN3_SHORT_3" }]
      group: "camera_object_detection_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_short_1"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_1"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_1"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN3_SHORT_1" }]
      group: "camera_object_detection_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_short_2"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_2"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_2"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_2"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN3_SHORT_2" }]
      group: "camera_object_detection_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_short_4"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_4"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_4"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_4"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN3_SHORT_4" }]
      group: "camera_object_detection_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_gen4_short_3"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_3"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_3"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_3"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN4_SHORT_3" }]
      group: "camera_object_detection_short_3"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_gen4_short_1"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_1"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_1"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN4_SHORT_1" }]
      group: "camera_object_detection_short_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_gen4_short_2"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_2"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_2"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_2"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN4_SHORT_2" }]
      group: "camera_object_detection_short_2"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_gen4_short_4"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_short_4"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 1000
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_short_4"
          transport_option: { type: kIntraProcess }
          timeout: 1000
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_short_4"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN4_SHORT_4" }]
      group: "camera_object_detection_short_4"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_infrared_1"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_infrared_1"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_infrared_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_INFRARED_1"}
      ]
      group: "camera_object_detection_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraObjectRefinerVNode"
      name: "sensing_camera_object_detection_gen4_infrared_1"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/intra_object_2d_list_infrared_1"
              is_master_trigger :  true
            },
            {
              topic: "/sensing/intra/scan_conversion"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/fusion_output"
              timeout: 50
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/intra_object_2d_list_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/lidar_pv_abnormal_output_internal"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          }

        ]
      }

      pub: [
        {
          name: "camera_object_list"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_infrared_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]
      params: [
        { key: "camera", value: "CAMERA_GEN4_INFRARED_1"}
      ]
      group: "camera_object_detection_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_infrared_1"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_infrared_1"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_infrared_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_infrared_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_INFRARED_1" }]
      group: "camera_object_detection_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraMultiTaskVNode"
      name: "camera_multi_task_gen4_infrared_1"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/camera_subscription_infrared_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }

      pub: [
        {
          name: "intra_object_2d_list"
          topic: "/sensing/intra/intra_object_2d_list_infrared_1"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "camera_segmentation"
          topic: "/perception/camera_segmentation"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "intra_camera_segmentation"
          topic: "/perception/intra/camera_segmentation_infrared_1"
          transport_option: { type: kIntraProcess }
          timeout: 350
        },
        {
          name: "intra_panel_objects"
          topic: "/perception/intra/panel_objects_infrared_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera", value: "CAMERA_GEN4_INFRARED_1" }]
      group: "camera_object_detection_infrared_1"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "RadarClusterVNode"
      name: "sensing_radar_cluster"
      sub {
        mode: kPeriodic
        periodic_interval: 100
        topic: [
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 100
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_2"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_3"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_4"
            transport_option: { type: kUDP }
            queue_size: 4
          }
        ]
      }

      pub: [{
        name: "radar_clusters"
        topic: "/perception/radar_clusters"
        transport_option: { type: kUDP }
        timeout: 500
      }]

      group: "radar_cluster"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "ObstacleDetectionVNode"
      name: "sensing_obstacle_detection"
      sub {
        mode: kXTrigger
        trigger_num: 2
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_2"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_3"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_4"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/perception/intra/camera_segmentation_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_2"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_3"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_4"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_5"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_6"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_7"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/occupancy_model_result"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          }
        ]
      }

      pub: [
        {
          name: "obstacle_detection"
          topic: "/sensing/intra/obstacle_detection"
          transport_option: { type: kIntraProcess }
          timeout: 400
        },
        {
          name: "obstacle_list"
          topic: "/obstacle_list"
          transport_option: { type: kUDP }
        },
        {
          name: "obstacle_list_with_type"
          topic: "/sensing/intra/obstacle_detection_with_semantic_type"
          transport_option: { type: kIntraProcess }
        }

      ]

      group: "obstacle_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OcclusionMapVNode"
      name: "sensing_occlusion_map"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/sensing/intra/obstacle_detection"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }]
      }

      pub: [{
        name: "occlusion_map"
        topic: "/sensing/intra/occlusion_map"
        transport_option: { type: kIntraProcess }
      }]

      group: "occlusion_map"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "CameraRefinementVNode"
      name: "sensing_camera_refinement"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/fusion_output"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }
      pub: [
        {
          name: "camera_object_list_pub"
          topic: "/perception/camera_object_list"
          transport_option: { type: kUDP }
          timeout: 350
        },
        {
          name: "camera_object_list_intra"
          topic: "/perception/intra/camera_object_list_2"
          transport_option: { type: kIntraProcess }
          timeout: 350
        }
      ]

      group: "camera_refinement"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "LidarDetectionVNode"
      name: "sensing_lidar_detection"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/sensing/intra/scan_conversion"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }

        ]
      }
      pub: [{
        name: "lidar_detection"
        topic: "/sensing/intra/lidar_detection"
        transport_option: { type: kIntraProcess }
        timeout: 400
      }]

      group: "lidar_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "BEVFusionVNode"
      name: "sensing_bevfusion"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }

        ]
      }
      pub: [
        {
          name: "lidar_detection"
          topic: "/sensing/intra/lidar_detection"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "fusion_output"
          topic: "/sensing/intra/fusion_output"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "lidar_segmentation"
          topic: "/sensing/intra/lidar_segmentation"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "lidar_detected_map_list"
          topic: "/perception/lidar_detected_map_list"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "lidar_1st_detected_list"
          topic: "/perception/lidar_1st_detected_list"
          transport_option: { type: kUDP }
        }
      ]

      group: "bevfusion"
      prio: 11

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "SegmentationFilterVNode"
      name: "sensing_segmentation_filter"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/lidar_segmentation"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          }
        ]
      }
      pub: [
        {
          name: "nearby_lidar_segmentation"
          topic: "/perception/nearby_segmentation_points"
          transport_option: { type: kUDP }
        }
      ]

      group: "segmentation_filter"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "DetectionRefinementVNode"
      name: "sensing_detection_refinement"
      sub {
        mode: kXTrigger
        trigger_num: 2
        topic: [
          {
            topic: "/sensing/intra/lidar_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/occlusion_map"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "lidar_detected_list"
          topic: "/perception/lidar_detected_list"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "detection_refinement"
          topic: "/sensing/intra/detection_refinement"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "detection_refinement"
      prio: 11

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "DetectionTrackerVNode"
      name: "sensing_detection_tracker"
      sub {
        mode: kOneTrigger
        topic: [{
          topic: "/sensing/intra/detection_refinement"
          transport_option: { type: kIntraProcess }
          queue_size: 1
        }]
      }

      pub: [{
        name: "detection_tracker"
        topic: "/sensing/intra/detection_tracker"
        transport_option: { type: kIntraProcess }
      }]

      group: "detection_tracker"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "SensingOccupancyPointCloudProcessorVNode"
      name: "sensing_occupancy_point_cloud_processor"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "occupancy_point_cloud_processor_pub"
          topic: "/perception/sensing/occupancy_point_cloud_processor"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "node_name", value: "sensing" }]
      group: "sensing_occupancy_point_cloud_processor"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "ObstacleGenerationVNode"
      name: "sensing_obstacle_generation"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/radar_conti_430_1"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_2"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_3"
            transport_option: { type: kUDP }
            queue_size: 4
          },
          {
            topic: "/radar_conti_430_4"
            transport_option: { type: kUDP }
            queue_size: 4
          }
        ]
      }

      pub: [
        {
          name: "obstacle_message_pub"
          topic: "/sensing/intra/obstacle_detection"
          transport_option: { type: kIntraProcess }
        }
      ]
      group: "obstacle_generation"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "ObstaclePostProcessingVNode"
      name: "sensing_obstacle_post_processing"
      sub {
        mode: kCustomPolicy
        trigger_attributes: {
          trigger_topics:[
            {
              topic: "/sensing/intra/obstacle_detection"
              is_master_trigger :  true
            },
            {
              topic: "/perception/intra/occupancy_model_result"
              timeout: 20
              align_constraint: { greater_eq: 0 }
            },
            {
              topic: "/sensing/intra/lidar_segmentation"
              timeout: 30
              align_constraint: { greater_eq: 0 }
            }
          ]
        }
        topic: [
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/occupancy_model_result"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/lidar_segmentation"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          }
        ]
      }

      pub: [
        {
          name: "obstacle_message_pub"
          topic: "/sensing/intra/obstacle_detection_with_semantic_type"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "obstacles_pub"
          topic: "/obstacle_list"
          transport_option: { type: kUDP }
        }
      ]
      group: "obstacle_post_processing"
      prio: 11

      enable_message_event_timestamp_record: true
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyModelVNode"
      name: "sensing_occupancy_model"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "occupancy_model_result_pub"
          topic: "/perception/intra/occupancy_model_result"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "occupancy_model"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyGridSensingVNode"
      name: "sensing_occupancy_grid_sensing"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/detection_tracker"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/gateway"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/perception/intra/occupancy_model_result"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/perception/sensing/occupancy_point_cloud_processor"
            transport_option: { type: kIntraProcess }
            queue_size: 4
          },
          {
            topic: "/sensing/intra/obstacle_detection_with_semantic_type"
            transport_option: { type: kIntraProcess }
            queue_size: 5
          },
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/lidar_segmentation"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          }
        ]
      }

      pub: [
        {
          name: "occupancy_grid_result"
          topic: "/perception/intra/occupancy_grid_result"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "occupancy_grid_sensing"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyPostProcessingSensingVNode"
      name: "sensing_occupancy_post_processing_sensing"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/perception/intra/occupancy_grid_result"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/occupancy_range_view_result"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/obstacle_detection_with_semantic_type"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/occupancy_camera_result_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/detection_tracker"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "lidar_object_list"
          topic: "/perception/lidar_object_list"
          transport_option: { type: kUDP }
        }
      ]

      group: "occupancy_post_processing_sensing"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyRangeViewSensingVNode"
      name: "sensing_occupancy_range_view_sensing"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/obstacle_detection_with_semantic_type"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/range_cluster_results"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/detection_tracker"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 15
          },
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "occupancy_range_view_result"
          topic: "/perception/intra/occupancy_range_view_result"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "occupancy_range_view_sensing"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyRangeViewDetectorVNode"
      name: "sensing_occupancy_range_view_detector"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          }
        ]
      }

      pub: [
        {
          name: "range_cluster_results"
          topic: "/perception/intra/range_cluster_results"
          transport_option: { type: kIntraProcess }
        }
      ]

      group: "sensing_occupancy_range_view_detector"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyCameraDetectorVNode"
      name: "occupancy_camera_detector"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/perception/sensing/occupancy_point_cloud_processor"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          }
        ]
      }

      pub: [
        {
          name: "occupancy_camera_detector_output"
          topic: "/sensing/intra/occupancy_camera_detector_output"
          transport_option: { type: kIntraProcess }
        }
      ]
      group: "occupancy_camera_detector"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "OccupancyCameraSensingVNode"
      name: "occupancy_camera_sensing"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/sensing/intra/occupancy_camera_detector_output"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/sensing/occupancy_point_cloud_processor"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/lidar_segmentation"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/detection_tracker"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/single_frame_lidar_abnormal_channel"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "occupancy_camera_result"
          topic: "/sensing/intra/occupancy_camera_result_1"
          transport_option: { type: kIntraProcess }
        }
      ]
      params: [{ key: "camera_type", value: "CAMERA_1" }]
      group: "occupancy_camera_sensing"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "PostProcessingVNode"
      name: "sensing_post_processing"
      sub {
        mode: kXTrigger
        trigger_num: 3
        topic: [
          {
            topic: "/sensing/intra/obstacle_detection_with_semantic_type"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/occlusion_map"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/camera_subscription_1"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_2"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_3"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_4"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_5"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_6"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/sensing/intra/camera_subscription_7"
            transport_option: { type: kIntraProcess }
            queue_size: 2
          },
          {
            topic: "/perception/intra/camera_segmentation_1"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_2"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_3"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_4"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_5"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_6"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/perception/intra/camera_segmentation_7"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/sensing/intra/detection_tracker"
            transport_option: { type: kIntraProcess }
            queue_size: 1
          },
          {
            topic: "/planning/planning_lane_sequence"
            transport_option: { type: kUDP }
            queue_size: 1
          }

        ]
      }

      pub: [
        {
          name: "lidar_object_list"
          topic: "/perception/lidar_object_list"
          transport_option: { type: kUDP }
          timeout: 400
        },
        {
          name: "obstacle_debug"
          topic: "/perception/obstacle_debug"
          transport_option: { type: kUDP }
        },
        {
          name: "segmentation_debug"
          topic: "/perception/segmentation_debug"
          transport_option: { type: kUDP }
        }
      ]

      group: "post_processing"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "SensorExtractorVNode"
      name: "sensor_extractor"
      sub {
        mode: kAnyTrigger,
        trigger_num: 1
        topic: [
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 20
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 10
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_3"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_4"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_5"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_6"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_7"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_short_1"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_short_2"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_short_3"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          },
          {
            topic: "/sensing/intra/camera_sensor_extractor_pub_short_4"
            transport_option: { type: kIntraProcess }
            queue_size: 3
          }
        ]
      }

      group: "sensor_extractor"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "SensingPostCreateVNode"
      name: "sensing_post_create_vnode"
      sub {mode: kCallOnce}
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "LidarPVAbnormalDetectionVNode"
      name: "lidar_perspective_view_abnormal_detection"
      sub {
        mode: kOneTrigger,
        topic: [
          {
            topic: "/lidar_scan_slice_main"
            transport_option: { type: kUDP }
            queue_size: 1
          },
          {
            topic: "/gateway"
            transport_option: { type: kUDP }
            queue_size: 2
          }
        ]
      }

      pub: [
        {
          name: "lidar_pv_abnormal_output_internal"
          topic: "/perception/lidar_pv_abnormal_output_internal"
          transport_option: { type: kIntraProcess }
        },
        {
          name: "lidar_pv_abnormal_output"
          topic: "/perception/lidar_pv_abnormal_output"
          transport_option: { type: kUDP }
        },
        {
          name: "lidar_pv_abnormal_event"
          topic: "/system_event"
          transport_option: { type: kUDP }
        }
      ]
      group: "lidar_pv_abnormal_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libsensing_node.so"
      clazz: "LidarAbnormalDetectionVNode"
      name: "lidar_abnormal_detection"
      sub {
        mode: kPeriodic
        periodic_interval: 1000
        topic: [
          {
            topic: "/sensing/intra/obstacle_detection"
            transport_option: { type: kIntraProcess }
            queue_size: 4
          },
          {
            topic: "/sensing/intra/occlusion_map"
            transport_option: { type: kIntraProcess }
            queue_size: 4
          },
          {
            topic: "/sensing/intra/scan_conversion"
            transport_option: { type: kIntraProcess }
            queue_size: 4
          },
          {
            topic: "/pose"
            transport_option: { type: kUDP }
            queue_size: 10
          },
          {
            topic: "/gateway"
            transport_option: { type: kUDP }
            queue_size: 11
          }
        ]
      }
      pub: [
        {
          name: "lidar_abnormal_output"
          topic: "/perception/lidar_abnormal_output"
          transport_option: { type: kUDP }
        },
        {
          name: "single_frame_lidar_abnormal_channel"
          topic: "/perception/single_frame_lidar_abnormal_channel"
          transport_option: { type: kIntraProcess }
        }
      ]
      group: "lidar_abnormal_detection"
      prio: 11
    },
    {
      library: "${VOY_LIB_DIR}/libhdmap_vnodes.so"
      clazz: "MapChangeUpdaterVNode"
      name: "sensing_map_change_updater"
      sub {
        mode: kOneTrigger
        topic: [
          {
            topic: "/hdmap/map_change_preprocess"
            transport_option: { type: kUDP }
            queue_size: 1
          }
        ]
      }
      params: [
        { key: "vnode_name", value: "sensing" }
      ]
      group: "hdmap_group"
      prio: 11
    }
  ]
}
