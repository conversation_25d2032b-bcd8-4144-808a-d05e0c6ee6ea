#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ACTION_GUIDANCE_REPLY_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ACTION_GUIDANCE_REPLY_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class VehicleActionGuidanceReplyObserver : public IDataObserver {
 public:
  using VehicleActionGuidanceReplyCallback = std::function<void(
      std::shared_ptr<teleassist::pb::VehicleActionGuidanceReply>,
      DataChannel)>;

  explicit VehicleActionGuidanceReplyObserver(
      VehicleActionGuidanceReplyCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  VehicleActionGuidanceReplyCallback vehicle_action_guidance_reply_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ACTION_GUIDANCE_REPLY_OBSERVER_H_
