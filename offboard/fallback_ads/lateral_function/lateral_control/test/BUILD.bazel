load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "lqr_controller_test",
    srcs = ["lqr_controller_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
        "//offboard/fallback_ads/common_math_library:matrix",
        "//offboard/fallback_ads/lateral_function/lateral_control:lqr_controller",
        "@voy-sdk//:gtest",
    ],
)
