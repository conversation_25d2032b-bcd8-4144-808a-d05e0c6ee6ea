#include "planner/selection/selection.h"

#include <optional>

#include <gtest/gtest.h>

#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer.h"
#include "planner/decoupled_maneuvers/path/reasoning/path_reasoning.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_scenario_selector.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"
#include "planner/selection/selection_utils.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/trajectory/util/trajectory_util.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/world_model/test/world_model_test_utility.h"
#include "pnc_map_service/test/map_test_util.h"

namespace planner {

using selection::ComputeEgoCrossLaneTimeInterval;
using selection::ComputeLcCompletionRate;
using selection::ComputeLcGapObsoleteCost;
using selection::ComputeLcSafeYieldDecelerationCost;
using selection::IsCutinToTargetRegionFromTargetNeighborRegion;
using selection::TrajectoryMetaData;
using selection::WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane;

// TODO(Pengfei) : move this file to the lane change scenario folder
class SelectionLaneChangeTest : public testing::Test,
                                public speed::ReasoningTestFixture {
 public:
  SelectionLaneChangeTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
    SetEgoPose(/*lane_id=*/128479, /*portion=*/0.1, /*speed=*/8.0);
    CreateLaneChangePath(/*source_lane_id=*/128479,
                         /*target_lane_id=*/128481);
    SetLaneChangeStatus(
        /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
        /*lane_change_state=*/
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*start_arclength=*/0.0, 128479, 128481,
        /*is_current_homotopy_lane_change=*/false);
    UpdateForLaneChange(/*last_lane_change_point_distance=*/500.0);
    UpdateCrossLaneInfoManager();
  }

  std::shared_ptr<const speed::TrajectoryInfo> ConstructTrajectoryInfo() {
    return std::make_shared<const speed::TrajectoryInfo>(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(), world_model().robot_state(),
        is_pull_out_jump_out_, is_for_ra_unstuck_request_,
        is_lane_sequence_feasible_, lane_sequence_cost_factors_,
        is_waypoint_assist_invoked_, is_waypoint_backup_trajectory_used_,
        is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY,
        planner::pb::PathReasoningSeed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }

  planner::pb::Trajectory ConstructCLTrajectory() {
    const int speed_points_size = path().size();
    const auto speed_profile = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/10.0,
        /*n_sample=*/speed_points_size,
        /*dt=*/constants::kTrajectoryIntervalInSec);
    EXPECT_EQ(speed_profile.size(), speed_points_size);

    planner::pb::Trajectory trajectory;
    CombinePathAndSpeedProfile(
        PlanInitState(
            /*start_time_in=*/0, vehicle_model::CarModel::State::Zero(),
            planner::pb::PlanInitType::COLDSTART,
            /*immutable_poses_in=*/{}, /*z_in=*/0.0, /*curvature=*/0.0,
            /*pinch=*/std::nullopt),
        vehicle_model::JukeIntegratedModelWithAxleRectangularShape(
            vehicle_model::JukeIntegratedModel(
                vehicle_model::JukeIntegratedModel::GetMotionModelParam(),
                /*method=*/math::IntegrationMethod::Taylor_1),
            vehicle_model::GetEgoAxleRectangularMeasurementByType(
                av_comm::CarType::kTest)),
        TurnSignalDirective({planner::pb::TurnMode::RIGHT_TURN,
                             planner::pb::TurnSignalType::kUnknown}),
        /*honk_request=*/false, /*hazard_light_request=*/false,
        /*suppression_status=*/false, path(), speed_profile,
        /*prediction_timestamp=*/0, planner::pb::MotionMode::FORWARD,
        /*has_ego_entered_mrc=*/false, &trajectory);
    EXPECT_LT(0, trajectory.poses_size());
    return trajectory;
  }

  void ConstructCLCandidate() {
    lane_selection::DecoupledLaneSequenceInfo lane_seq_info(
        planner::pb::ManeuverType::LANE_CHANGE_LEFT, lane_sequence_result(),
        previous_iter_seed_);
    path_option_.geometric_constraint.lane_sequence_info = &lane_seq_info;
    path_option_.trajectory_type =
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY;
    path_option_.geometric_constraint.behavior_type =
        planner::pb::BehaviorType::CROSS_LANE;
    path_option_.geometric_constraint.lane_sequence_result =
        &lane_sequence_result();
    candidate_.emplace(
        path_option_, /*speed_ix_in=*/0, selection::EgoIntentSignals(),
        /*trajectory_info=*/ConstructTrajectoryInfo(),
        std::make_shared<planner::pb::Path>(planner::pb::Path()),
        /*extended_path_in=*/
        std::make_shared<math::geometry::PolylineCurve2d>(
            math::geometry::PolylineCurve2d()),
        /*proximity_infos=*/
        std::make_shared<std::map<ObjectId, speed::pb::ObjectProximityInfo>>(
            std::map<ObjectId, speed::pb::ObjectProximityInfo>()),
        /*overlap gap=*/
        std::make_shared<
            std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>>(
            std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>()),
        planner::pb::Trajectory(),
        /*speed_result=*/speed::SpeedResult(),
        std::make_shared<planner::pb::SingleHomotopyPathSeed>(
            planner::pb::SingleHomotopyPathSeed()),
        std::make_shared<speed::pb::SpeedSeed>(speed::pb::SpeedSeed()),
        std::make_shared<std::vector<planner::pb::AssistRequest>>(
            std::vector<planner::pb::AssistRequest>()),
        /*speed_discomfort_in=*/0.0,
        /*if_use_trajectory_guider_output_in_path_in=*/false,
        world_model().robot_state().car_model_with_shape().shape_measurement(),
        /*intention_plan_debug_in=*/nullptr);
  }

  const TrajectoryMetaData& candidate() const {
    DCHECK(candidate_.has_value());
    return candidate_.value();
  }

  TrajectoryMetaData& mutable_candidate() {
    DCHECK(candidate_.has_value());
    return candidate_.value();
  }

 private:
  std::optional<TrajectoryMetaData> candidate_;
  PathGenerationOption path_option_;
};

TEST_F(SelectionLaneChangeTest, ComputeLcCompletionRateInLeftLaneChange) {
  // Generate lane change instance.
  constexpr int64_t kSourceLaneId = 128231;
  constexpr int64_t kTargetLaneId = 128229;
  const pnc_map::Lane* source_lane = map_test_util().GetLane(kSourceLaneId);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util().GetLane(kTargetLaneId);
  ASSERT_NE(target_lane, nullptr);

  const planner::pb::LaneChangeMode direction =
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE;
  LaneChangeInstance lane_change_instance(direction, /*start_arclength=*/0.0,
                                          source_lane, target_lane);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), {source_lane, target_lane});
  lane_change_instance.SetTargetRegionMarkingSequence(
      std::map<int64_t, ViolableLaneMarkingRange>());

  // Test when ego is outside the target boundary.
  {
    SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/0.0,
               /*accel=*/0.0);
    EXPECT_DOUBLE_EQ(0.0, ComputeLcCompletionRate(world_model().robot_state(),
                                                  lane_change_instance));
  }

  // Test when ego is partly crossing the boundary.
  {
    const math::geometry::Point2d& cross_curve_point =
        source_lane->left_marking()->line().GetInterp(/*arc_length=*/5.0);
    const double heading_at_point =
        source_lane->GetLaneDirection(cross_curve_point);
    SetEgoPose(/*x_pos=*/cross_curve_point.x(),
               /*y_pos=*/cross_curve_point.y(),
               /*yaw=*/heading_at_point + M_PI_4, /*speed=*/0.0,
               /*accel=*/0.0);
    const double lc_completion_rate = ComputeLcCompletionRate(
        world_model().robot_state(), lane_change_instance);
    EXPECT_LT(0.0, lc_completion_rate);
    EXPECT_GT(1.0, lc_completion_rate);
  }

  // Test when ego is fully inside the target lane.
  {
    SetEgoPose(/*lane_id=*/kTargetLaneId, /*portion=*/0.0, /*speed=*/0.0,
               /*accel=*/0.0);
    EXPECT_DOUBLE_EQ(1.0, ComputeLcCompletionRate(world_model().robot_state(),
                                                  lane_change_instance));
  }
}

TEST_F(SelectionLaneChangeTest, ComputeLcCompletionRateInRightLaneChange) {
  // Generate lane change instance.
  constexpr int64_t kSourceLaneId = 128229;
  constexpr int64_t kTargetLaneId = 128231;
  const pnc_map::Lane* source_lane = map_test_util().GetLane(kSourceLaneId);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util().GetLane(kTargetLaneId);
  ASSERT_NE(target_lane, nullptr);

  const planner::pb::LaneChangeMode direction =
      planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE;
  LaneChangeInstance lane_change_instance(direction, /*start_arclength=*/0.0,
                                          source_lane, target_lane);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), {source_lane, target_lane});
  lane_change_instance.SetTargetRegionMarkingSequence(
      std::map<int64_t, ViolableLaneMarkingRange>());

  // Test when ego is outside the target boundary.
  {
    SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/0.0,
               /*accel=*/0.0);
    EXPECT_DOUBLE_EQ(0.0, ComputeLcCompletionRate(world_model().robot_state(),
                                                  lane_change_instance));
  }

  // Test when ego is partly crossing the boundary.
  {
    const math::geometry::Point2d& cross_curve_point =
        source_lane->right_marking()->line().GetInterp(/*arc_length=*/5.0);
    const double heading_at_point =
        source_lane->GetLaneDirection(cross_curve_point);
    SetEgoPose(/*x_pos=*/cross_curve_point.x(),
               /*y_pos=*/cross_curve_point.y(),
               /*yaw=*/heading_at_point - M_PI_4, /*speed=*/0.0,
               /*accel=*/0.0);
    const double lc_completion_rate = ComputeLcCompletionRate(
        world_model().robot_state(), lane_change_instance);
    EXPECT_LT(0.0, lc_completion_rate);
    EXPECT_GT(1.0, lc_completion_rate);
  }

  // Test when ego is fully inside the target lane.
  {
    SetEgoPose(/*lane_id=*/kTargetLaneId, /*portion=*/0.0, /*speed=*/0.0,
               /*accel=*/0.0);
    EXPECT_DOUBLE_EQ(1.0, ComputeLcCompletionRate(world_model().robot_state(),
                                                  lane_change_instance));
  }
}

TEST_F(SelectionLaneChangeTest, ComputeLcGapObsoleteCost) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  ConstructCLCandidate();
  cross_lane_info_manager_.GetMutableCrossLaneInfo().urgency_score = 0.0;
  EXPECT_DOUBLE_EQ(
      0.0, candidate().trajectory_info->GetCrossLaneInfo().urgency_score);

  // Test empty lane change guide seed.
  {
    speed::pb::SpeedSeed speed_seed;
    std::ostringstream debug_oss;
    EXPECT_TRUE(speed_seed.lc_guide_seed().empty());
    EXPECT_DOUBLE_EQ(
        0.0, ComputeLcGapObsoleteCost(world_model().robot_state(), speed_seed,
                                      candidate(),
                                      /*lc_completion_rate=*/0.0, debug_oss));
  }

  // Test case where the lane change gap is not obsolete.
  {
    constexpr int64_t kTailObjId = 1;
    std::ostringstream debug_oss;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.add_lc_guide_seed()->set_tail_obj_id(kTailObjId);
    ASSERT_EQ(1, speed_seed.lc_guide_seed_size());

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kTailObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::PASS};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_DOUBLE_EQ(
        0.0, ComputeLcGapObsoleteCost(world_model().robot_state(), speed_seed,
                                      candidate(),
                                      /*lc_completion_rate=*/0.0, debug_oss));
  }

  constexpr double kObsoleteLcGapMaxRisk = 1.5;
  // Test case where the gap is obsolete when lane change is not urgent.
  {
    EXPECT_DOUBLE_EQ(
        0.0, candidate().trajectory_info->GetCrossLaneInfo().urgency_score);

    constexpr int64_t kTailObjId = 1;
    std::ostringstream debug_oss;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.add_lc_guide_seed()->set_tail_obj_id(kTailObjId);
    ASSERT_EQ(1, speed_seed.lc_guide_seed_size());

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kTailObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_DOUBLE_EQ(kObsoleteLcGapMaxRisk,
                     ComputeLcGapObsoleteCost(
                         world_model().robot_state(), speed_seed, candidate(),
                         /*lc_completion_rate=*/0.0, debug_oss));
  }
  // Test case where the gap is obsolete when lane change is urgent.
  {
    SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.9, /*speed=*/8.0);
    SetLaneChangeStatus(
        /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
        /*lc_state=*/
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
        /*is_current_homotopy_lane_change=*/false);
    UpdateForLaneChange();
    ConstructCLCandidate();

    EXPECT_LT(0.0,
              candidate().trajectory_info->GetCrossLaneInfo().urgency_score);
    EXPECT_GT(1.0,
              candidate().trajectory_info->GetCrossLaneInfo().urgency_score);

    constexpr int64_t kTailObjId = 1;
    std::ostringstream debug_oss;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.add_lc_guide_seed()->set_tail_obj_id(kTailObjId);
    ASSERT_EQ(1, speed_seed.lc_guide_seed_size());

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kTailObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;
    const double lc_gap_obsolete_cost = ComputeLcGapObsoleteCost(
        world_model().robot_state(), speed_seed, candidate(),
        /*lc_completion_rate=*/0.0, debug_oss);
    EXPECT_LT(0.0, lc_gap_obsolete_cost);
    EXPECT_GT(kObsoleteLcGapMaxRisk, lc_gap_obsolete_cost);
  }
}

class ComputeLcGapObsoleteCostTest : public testing::TestWithParam<double>,
                                     public speed::ReasoningTestFixture {};
TEST_P(ComputeLcGapObsoleteCostTest, VariousLcCompletionRate) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);
  UpdateForLaneChange();
  UpdateCrossLaneInfoManager();
  cross_lane_info_manager_.GetMutableCrossLaneInfo().urgency_score = 0.0;

  lane_selection::DecoupledLaneSequenceInfo lane_seq_info(
      planner::pb::ManeuverType::LANE_CHANGE_LEFT, lane_sequence_result(),
      previous_iter_seed_);
  PathGenerationOption path_option;
  path_option.geometric_constraint.lane_sequence_info = &lane_seq_info;
  path_option.trajectory_type = planner::pb::TrajectoryType::NOMINAL_TRAJECTORY;
  path_option.geometric_constraint.behavior_type =
      planner::pb::BehaviorType::CROSS_LANE;
  path_option.geometric_constraint.lane_sequence_result =
      &lane_sequence_result();
  TrajectoryMetaData candidate(
      path_option, /*speed_ix_in=*/0, selection::EgoIntentSignals(),
      /*trajectory_info=*/
      std::make_shared<const speed::TrajectoryInfo>(
          unextended_path(), path(), path_evaluation_result(), ego_intention(),
          lane_sequence_iterator_opt_.value(),
          planning_segment_sequence_opt_.value(),
          world_model().traffic_light_detection(), physical_boundaries(),
          *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
          maneuver_type_, behavior_type_, pull_over_info_,
          pull_over_dest_meta_ptr(), cross_lane_info_manager_,
          lane_change_info_, lane_change_status_,
          /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
          reverse_driving_info(), open_space_info(),
          plan_init_ra_arc_length_on_extended_path(),
          world_model().robot_state(), is_pull_out_jump_out_,
          is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
          lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
          is_waypoint_backup_trajectory_used_,
          is_hard_boundary_relaxed_in_path_,
          planner::pb::TrajectoryType::NOMINAL_TRAJECTORY,
          planner::pb::PathReasoningSeed(),
          mutable_current_trajectory_info_seeds(),
          /*unique_path_homotopy_id=*/"",
          /*intention_plan_debug_in=*/nullptr),
      std::make_shared<planner::pb::Path>(planner::pb::Path()),
      /*extended_path_in=*/
      std::make_shared<math::geometry::PolylineCurve2d>(
          math::geometry::PolylineCurve2d()),
      /*proximity_infos=*/
      std::make_shared<std::map<ObjectId, speed::pb::ObjectProximityInfo>>(
          std::map<ObjectId, speed::pb::ObjectProximityInfo>()),
      /*overlap gap=*/
      std::make_shared<
          std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>>(
          std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>()),
      planner::pb::Trajectory(),
      /*speed_result=*/speed::SpeedResult(),
      std::make_shared<planner::pb::SingleHomotopyPathSeed>(
          planner::pb::SingleHomotopyPathSeed()),
      std::make_shared<speed::pb::SpeedSeed>(speed::pb::SpeedSeed()),
      std::make_shared<std::vector<planner::pb::AssistRequest>>(
          std::vector<planner::pb::AssistRequest>()),
      /*speed_discomfort_in=*/0.0,
      /*if_use_trajectory_guider_output_in_path_in=*/false,
      world_model().robot_state().car_model_with_shape().shape_measurement(),
      /*intention_plan_debug_in=*/nullptr);
  EXPECT_DOUBLE_EQ(0.0,
                   candidate.trajectory_info->GetCrossLaneInfo().urgency_score);

  const double lc_completion_rate = GetParam();

  // Test empty lane change guide seed.
  {
    speed::pb::SpeedSeed speed_seed;
    std::ostringstream debug_oss;
    EXPECT_TRUE(speed_seed.lc_guide_seed().empty());
    EXPECT_DOUBLE_EQ(0.0, ComputeLcGapObsoleteCost(
                              world_model().robot_state(), speed_seed,
                              candidate, lc_completion_rate, debug_oss));
  }

  // Test case where the lane change gap is not obsolete.
  {
    constexpr int64_t kTailObjId = 1;
    std::ostringstream debug_oss;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.add_lc_guide_seed()->set_tail_obj_id(kTailObjId);
    ASSERT_EQ(1, speed_seed.lc_guide_seed_size());

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kTailObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::PASS};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    candidate.speed_result = speed_result;

    EXPECT_DOUBLE_EQ(0.0, ComputeLcGapObsoleteCost(
                              world_model().robot_state(), speed_seed,
                              candidate, lc_completion_rate, debug_oss));
  }

  constexpr double kObsoleteLcGapMaxRisk = 1.5;
  // Test case where the lane change gap is obsolete.
  {
    constexpr double kEpsilon = 1e-6;
    constexpr int64_t kTailObjId = 1;
    std::ostringstream debug_oss;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.add_lc_guide_seed()->set_tail_obj_id(kTailObjId);
    ASSERT_EQ(1, speed_seed.lc_guide_seed_size());

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kTailObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    candidate.speed_result = speed_result;

    const double lc_gap_obsolete_cost =
        ComputeLcGapObsoleteCost(world_model().robot_state(), speed_seed,
                                 candidate, lc_completion_rate, debug_oss);

    if (lc_completion_rate < kEpsilon) {
      EXPECT_DOUBLE_EQ(kObsoleteLcGapMaxRisk, lc_gap_obsolete_cost);
    } else if (lc_completion_rate > 1.0 - kEpsilon) {
      EXPECT_DOUBLE_EQ(0.0, lc_gap_obsolete_cost);
    } else {
      EXPECT_LT(0.0, lc_gap_obsolete_cost);
      EXPECT_GT(kObsoleteLcGapMaxRisk, lc_completion_rate);
    }
  }
}

INSTANTIATE_TEST_SUITE_P(ComputeLcGapObsoleteCost, ComputeLcGapObsoleteCostTest,
                         ::testing::Values(0.0, 0.5, 1.0));

TEST_F(SelectionLaneChangeTest, ComputeLcSafeYieldDecelerationCost) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);

  ConstructCLCandidate();

  // Test empty tailgator.
  {
    speed::pb::SpeedSeed speed_seed;
    std::ostringstream debug_oss;
    EXPECT_TRUE(speed_seed.speed_reasoner_seeds()
                    .tailgater_reasoner_seed()
                    .agents_id()
                    .empty());
    EXPECT_DOUBLE_EQ(0.0,
                     ComputeLcSafeYieldDecelerationCost(
                         world_model().robot_state(), candidate(), speed_seed,
                         /*lc_completion_rate=*/0.0, debug_oss));
  }

  constexpr double kExceedLcSafeDecelerationCost = 1.5;
  constexpr int64_t kObjId = 1;

  // Test case where there is large car tailgating.
  {
    planner::pb::TrajectoryPose pose;
    pose.set_accel(-3.0);
    *(mutable_candidate().trajectory.add_poses()) = std::move(pose);
    EXPECT_EQ(1, candidate().trajectory.poses_size());

    CrossLaneInfo& cross_lane_info =
        cross_lane_info_manager_.GetMutableCrossLaneInfo();
    cross_lane_info.safe_yield_acceleration = -1.5;
    speed::pb::SpeedSeed speed_seed;
    speed_seed.mutable_speed_reasoner_seeds()
        ->mutable_tailgater_reasoner_seed()
        ->add_agents_id(kObjId);
    std::ostringstream debug_oss;
    EXPECT_EQ(1, speed_seed.speed_reasoner_seeds()
                     .tailgater_reasoner_seed()
                     .agents_id_size());
    EXPECT_DOUBLE_EQ(kExceedLcSafeDecelerationCost,
                     ComputeLcSafeYieldDecelerationCost(
                         world_model().robot_state(), candidate(), speed_seed,
                         /*lc_completion_rate=*/0.0, debug_oss));
  }

  // TODO(pengfei): Add UT for ComputeLcSafeYieldDecelerationCost for crawl.
}

TEST_F(SelectionLaneChangeTest,
       IsInefficientCandidateDueToPotentialOrTempParkedCars) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/false);

  const std::vector<int64_t> obj_ids{1, 2, 3, 4};
  for (const int64_t obj_id : obj_ids) {
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::VEHICLE,
                 /*id=*/obj_id, /*lane_id=*/kTargetLaneId, /*portion=*/0.1,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        /*lane_id=*/kTargetLaneId, /*portion=*/0.1,
        /*likelihood=*/1.0, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/0.0);
  }

  UpdateForLaneChange(/*last_lane_change_point_distance=*/500.0);
  EXPECT_EQ(4, agent_list().agent_list().size());

  ConstructCLCandidate();
  EXPECT_TRUE(candidate().IsCrossLane());
  ExtractToSpeedWorldModel();

  planner::pb::DecoupledManeuverSeed seed;
  planner::pb::DecoupledManeuverSeed prev_seed;
  selection::LaneChangeScenarioSelector lc_scenario(
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .trajectory_selection_config(),
      speed_world_model(), prev_seed, seed);

  CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager_.GetMutableCrossLaneInfo();
  CrossLaneRegionInfo* target_region_info =
      GetRegionInfoPtrByType(cross_lane::pb::CrossLaneRegionType::TARGET,
                             cross_lane_info.region_infos);
  EXPECT_TRUE(target_region_info != nullptr);
  CrossLaneObjectInfoList* target_region_object_info_list =
      &(target_region_info->object_info_list);
  EXPECT_TRUE(target_region_object_info_list != nullptr);
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(1);
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  PlannerObject planner_object(
      agent_proto, /*object_timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, tracked_object));
  planner_object.set_is_primary_stationary(true);
  std::ostringstream debug_oss;

  // Test case where there is no parked car on the target region.
  {
    target_region_object_info_list->reset();
    EXPECT_FALSE(
        lc_scenario.IsInefficientCandidateDueToPotentialOrTempParkedCars(
            candidate(), debug_oss));
  }

  // Test case where there is pass decision on the parked car.
  {
    constexpr int64_t kObjId = 1;
    CrossLaneObjectInfo lc_obj_info(planner_object);
    lc_obj_info.id = kObjId;
    lc_obj_info.is_parked_car = true;
    target_region_object_info_list->Add(kObjId, lc_obj_info);

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kObjId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::PASS};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_FALSE(
        lc_scenario.IsInefficientCandidateDueToPotentialOrTempParkedCars(
            candidate(), debug_oss));
  }

  // Test case where there is yield decision on the parked car.
  {
    constexpr int64_t kObjId = 2;
    CrossLaneObjectInfo lc_obj_info(planner_object);
    lc_obj_info.id = kObjId;
    lc_obj_info.is_parked_car = true;
    lc_obj_info.object_ego_dist = 100.0;
    target_region_object_info_list->Add(kObjId, lc_obj_info);

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"tail_agent",
                                 /*obj_id=*/kObjId);
    constraint.unique_pred_traj_id = GetBpUniqueId(kObjId, 1);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};

    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);

    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_TRUE(
        lc_scenario.IsInefficientCandidateDueToPotentialOrTempParkedCars(
            candidate(), debug_oss));
  }

  // Test case where there is yield decision on the vehicle with high parked car
  // score.
  {
    constexpr int64_t kObjId = 3;
    CrossLaneObjectInfo lc_obj_info(planner_object);
    lc_obj_info.id = kObjId;
    lc_obj_info.is_likely_parked_car_from_score = true;
    lc_obj_info.is_in_rightmost_lane = true;
    lc_obj_info.object_ego_dist = 100.0;
    cross_lane_info.cross_lane_meta.set_remaining_distance_for_cross_lane(
        200.0);
    cross_lane_info.cross_lane_meta.set_cross_lane_count(1);
    target_region_object_info_list->Add(kObjId, lc_obj_info);

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"lead_agent",
                                 /*obj_id=*/kObjId);
    constraint.unique_pred_traj_id = GetBpUniqueId(kObjId, 1);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};

    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);

    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_TRUE(
        lc_scenario.IsInefficientCandidateDueToPotentialOrTempParkedCars(
            candidate(), debug_oss));
  }

  // Test case where there is yield decision on the temporarily parked car.
  {
    constexpr int64_t kObjId = 4;
    CrossLaneObjectInfo lc_obj_info(planner_object);
    lc_obj_info.id = kObjId;
    lc_obj_info.is_temp_parked_car = true;
    lc_obj_info.is_in_rightmost_lane = true;
    lc_obj_info.object_ego_dist = 100.0;
    cross_lane_info.cross_lane_meta.set_remaining_distance_for_cross_lane(
        200.0);
    cross_lane_info.cross_lane_meta.set_cross_lane_count(1);
    target_region_object_info_list->Add(kObjId, lc_obj_info);

    speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"reansoner",
                                 /*constraint_id=*/"lead_agent",
                                 /*obj_id=*/kObjId);
    constraint.unique_pred_traj_id = GetBpUniqueId(kObjId, 1);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};

    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);

    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_TRUE(
        lc_scenario.IsInefficientCandidateDueToPotentialOrTempParkedCars(
            candidate(), debug_oss));
  }
}

TEST_F(SelectionLaneChangeTest, IsInefficientCandidateDueToConstructionZones) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  ConstructCLCandidate();
  EXPECT_TRUE(candidate().IsCrossLane());
  ExtractToSpeedWorldModel();

  planner::pb::DecoupledManeuverSeed seed;
  planner::pb::DecoupledManeuverSeed prev_seed;
  selection::LaneChangeScenarioSelector lc_scenario(
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .trajectory_selection_config(),
      speed_world_model(), prev_seed, seed);

  std::ostringstream debug_oss;

  // Test case where there is no construction zone on the target region.
  {
    EXPECT_FALSE(lc_scenario.IsInefficientCandidateDueToConstructionZones(
        candidate(), debug_oss));
  }

  const pnc_map::Lane* source_lane = map_test_util().GetLane(kSourceLaneId);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util().GetLane(kTargetLaneId);
  ASSERT_NE(target_lane, nullptr);
  // Generate lane change instance.
  const planner::pb::LaneChangeMode direction =
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE;
  LaneChangeInstance lane_change_instance(direction, /*start_arclength=*/0.0,
                                          source_lane, target_lane);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), {source_lane, target_lane});
  cross_lane_info_manager_.GetMutableCrossLaneInfo().lane_change_instance =
      lane_change_instance;

  // Add construction zone on the target lane.
  constexpr int64_t kCzId = 11;
  std::vector<math::geometry::Point2d> cz_contour;
  for (const auto& point : lane_change_instance.target_region().border()) {
    cz_contour.emplace_back(point.x(), point.y());
  }
  AddConstructionZone(kCzId, cz_contour,
                      /*source=*/voy::ZoneSource::REAL_TIME_MAP_ONLY,
                      /*space_type=*/voy::SpaceType::CONSTRUCTION_ZONE);
  UpdateWorldModel();

  const std::vector<const pnc_map::Lane*>& lane_sequence =
      candidate()
          .trajectory_info->GetCrossLaneInfo()
          .lane_change_instance.target_lane_sequence();
  ASSERT_FALSE(lane_sequence.empty());

  const std::map<int64_t, LaneBlockage>& lane_blockages =
      world_model().lane_blockage_detector().lane_blockages();
  ASSERT_FALSE(lane_blockages.empty());

  const auto& target_lane_blockage = lane_blockages.find(kTargetLaneId);
  ASSERT_NE(target_lane_blockage, lane_blockages.end());

  EXPECT_EQ(1, target_lane_blockage->second.blocking_objects.size());
  const ObjectBlockingInfo& object =
      target_lane_blockage->second.blocking_objects.front();
  EXPECT_TRUE(object.construction_zone() != nullptr);
  EXPECT_TRUE(object.IsConstructionZone());
  EXPECT_TRUE(object.ShouldConsiderAsSoftBlocking() ||
              object.ShouldConsiderAsHardBlocking());

  ExtractToSpeedWorldModel();
  selection::LaneChangeScenarioSelector lc_scenario_add_cz(
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .trajectory_selection_config(),
      speed_world_model(), prev_seed, seed);
  // Test case where there is pass decision on the construction zone on the
  // target region.
  {
    speed::Constraint constraint(
        speed::pb::Constraint::SPEED_OBJECT,
        /*fence_type=*/speed::pb::FenceType::kConstructionZone,
        /*reasoner_id=*/"reansoner",
        /*constraint_id=*/"tail_agent",
        /*obj_id=*/kCzId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::PASS};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_FALSE(
        lc_scenario_add_cz.IsInefficientCandidateDueToConstructionZones(
            candidate(), debug_oss));
  }

  // Test case where there is yield decision on the construction zone on the
  // target region.
  {
    speed::Constraint constraint(
        speed::pb::Constraint::SPEED_OBJECT,
        /*fence_type=*/speed::pb::FenceType::kConstructionZone,
        /*reasoner_id=*/"reansoner",
        /*constraint_id=*/"tail_agent",
        /*obj_id=*/kCzId);
    speed::ConstraintResult constraint_result;
    constraint_result.constraints = {constraint};
    constraint_result.decisions = {speed::pb::SpeedDecision::YIELD};
    speed::SpeedSearchResult speed_search_result(
        speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
        speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
        /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
        /*selected_discomfort_ix=*/-1,
        /*earliest_brake_time_ix=*/-1,
        /*profile=*/speed::Profile(),
        /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
        /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
        speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
        /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
        /*gap_info=*/std::nullopt,
        /*stay_stop_info=*/std::nullopt);
    speed::SpeedResult speed_result(std::move(speed_search_result),
                                    speed::SpeedPipelineMeta());
    mutable_candidate().speed_result = speed_result;

    EXPECT_TRUE(lc_scenario_add_cz.IsInefficientCandidateDueToConstructionZones(
        candidate(), debug_oss));
  }
}

// TODO(pengfei): Implement this test.
TEST_F(SelectionLaneChangeTest, ComputeBlockingTrafficCost) {}

TEST_F(SelectionLaneChangeTest, ComputeEgoCrossLaneTimeInterval) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);
  UpdateForLaneChange();
  ConstructCLCandidate();
  EXPECT_TRUE(candidate().IsCrossLane());

  mutable_candidate().trajectory = ConstructCLTrajectory();
  EXPECT_LE(2, candidate().trajectory.poses_size());

  mutable_candidate().BuildGeoCenteredInterpolatedTrajectory();

  std::ostringstream debug_oss;
  std::optional<double> cross_lane_start_relative_time;
  std::optional<double> cross_lane_end_relative_time;
  bool can_fully_cross_lane = false;
  ComputeEgoCrossLaneTimeInterval(candidate(), cross_lane_start_relative_time,
                                  cross_lane_end_relative_time,
                                  can_fully_cross_lane, debug_oss);
  EXPECT_TRUE(cross_lane_start_relative_time.has_value());
  EXPECT_TRUE(cross_lane_end_relative_time.has_value());
  EXPECT_LE(cross_lane_start_relative_time.value(),
            cross_lane_end_relative_time.value());
  EXPECT_LT(0.0, cross_lane_start_relative_time.value());
  EXPECT_LE(cross_lane_end_relative_time.value(), 8.0);
  EXPECT_TRUE(can_fully_cross_lane);
}

// TODO(pengfei): Implement this test.
TEST_F(SelectionLaneChangeTest, OdomBasedOverlapComputationTest) {}

TEST_F(SelectionLaneChangeTest,
       WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.2, /*speed=*/10.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  // Target neighbor region agent.
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/128483, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/128483,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/128483,
                                 /*end_portion=*/0.4,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  // Target region agent cutin from target neighbor region.
  prediction::pb::Agent& agent2 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/2, /*lane_id=*/128483, /*portion=*/0.6,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/128483,
                                 /*start_portion=*/0.6,
                                 /*end_lane_id=*/128479,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);
  EXPECT_EQ(2, agent_list().agent_list().size());

  UpdateForLaneChange();
  ConstructCLCandidate();
  EXPECT_TRUE(candidate().IsCrossLane());

  mutable_candidate().trajectory = ConstructCLTrajectory();
  EXPECT_LE(2, candidate().trajectory.poses_size());

  mutable_candidate().BuildGeoCenteredInterpolatedTrajectory();

  const CrossLaneObjectInfoList* target_neighbor_region_object_infos =
      cross_lane_info_manager_.GetTargetNeighborRegionObjectInfoListPtr();
  EXPECT_TRUE(target_neighbor_region_object_infos != nullptr);
  EXPECT_EQ(2, target_neighbor_region_object_infos->ids().size());
  const CrossLaneObjectInfo* target_neighbor_object_info1 =
      target_neighbor_region_object_infos->Find(/*object_id=*/1);
  const CrossLaneObjectInfo* target_neighbor_object_info2 =
      target_neighbor_region_object_infos->Find(/*object_id=*/2);
  EXPECT_TRUE(target_neighbor_object_info1 != nullptr);
  EXPECT_TRUE(target_neighbor_object_info2 != nullptr);
  EXPECT_FALSE(target_neighbor_object_info1->overlap_regions().empty());
  EXPECT_FALSE(target_neighbor_object_info2->overlap_regions().empty());

  std::ostringstream debug_oss;
  std::optional<double> cross_lane_start_relative_time;
  std::optional<double> cross_lane_end_relative_time;
  bool can_fully_cross_lane = false;
  ComputeEgoCrossLaneTimeInterval(candidate(), cross_lane_start_relative_time,
                                  cross_lane_end_relative_time,
                                  can_fully_cross_lane, debug_oss);
  EXPECT_TRUE(cross_lane_start_relative_time.has_value());
  EXPECT_TRUE(cross_lane_end_relative_time.has_value());
  EXPECT_LE(cross_lane_start_relative_time.value(),
            cross_lane_end_relative_time.value());
  EXPECT_LT(0.0, cross_lane_start_relative_time.value());
  EXPECT_LE(cross_lane_end_relative_time.value(), 8.0);
  EXPECT_TRUE(can_fully_cross_lane);

  EXPECT_FALSE(WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane(
      candidate(), target_neighbor_object_info1->overlap_regions(),
      cross_lane_start_relative_time.value(),
      cross_lane_end_relative_time.value(), debug_oss));
  EXPECT_TRUE(WillEgoHaveLongitudinalOverlapWithAgentDuringCrossingLane(
      candidate(), target_neighbor_object_info2->overlap_regions(),
      cross_lane_start_relative_time.value(),
      cross_lane_end_relative_time.value(), debug_oss));
}

TEST_F(SelectionLaneChangeTest, IsCutinToTargetRegionFromTargetNeighborRegion) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  // Target neighbor region agent.
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/128483, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/128483,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/128483,
                                 /*end_portion=*/0.4,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  // Target region agent cutin from target neighbor region.
  prediction::pb::Agent& agent2 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/2, /*lane_id=*/128483, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/128483,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/128479,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);
  EXPECT_EQ(2, agent_list().agent_list().size());

  UpdateForLaneChange();
  ConstructCLCandidate();
  EXPECT_TRUE(candidate().IsCrossLane());

  const CrossLaneObjectInfoList* target_neighbor_region_object_infos =
      cross_lane_info_manager_.GetObjectInfoListPtrByRegionType(
          CrossLaneRegionType::TARGET_NEIGHBOR);
  EXPECT_TRUE(target_neighbor_region_object_infos != nullptr);
  EXPECT_EQ(2, target_neighbor_region_object_infos->ids().size());

  // Test case where the agent dose not cut into the target region.
  {
    std::ostringstream debug_oss;
    double cutin_target_region_relative_time;
    double cutin_target_region_ra_arc_length;
    EXPECT_FALSE(IsCutinToTargetRegionFromTargetNeighborRegion(
        cross_lane_info_manager_, /*object_id=*/1,
        cutin_target_region_relative_time, cutin_target_region_ra_arc_length,
        debug_oss));
  }

  // Test case where the agent cuts into target region from the target neighbor
  // region.
  {
    std::ostringstream debug_oss;
    double cutin_target_region_relative_time;
    double cutin_target_region_ra_arc_length;
    EXPECT_TRUE(IsCutinToTargetRegionFromTargetNeighborRegion(
        cross_lane_info_manager_, /*object_id=*/2,
        cutin_target_region_relative_time, cutin_target_region_ra_arc_length,
        debug_oss));
  }
}

// TODO(pengfei): Implement this test.
TEST_F(SelectionLaneChangeTest, IsRiskyAgentDueToFovOnTargetNeighborRegion) {}

}  // namespace planner
