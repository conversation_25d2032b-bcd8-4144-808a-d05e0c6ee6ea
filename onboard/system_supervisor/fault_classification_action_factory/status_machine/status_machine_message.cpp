#include "status_machine_message.h"

#include <glog/logging.h>

#include <atomic>
#include <map>
#include <mutex>   //NOLINT
#include <thread>  //NOLINT
#include <unordered_set>

#if !defined(ONBOARD_ONLY)
#include "sim_protos/fallback_level2_state.pb.h"
#endif

#include "action_factory_protos/action_factory_re_engage.pb.h"
#include "av_comm/car_id.h"
#include "av_comm/components.h"
#include "av_comm/fault_code.h"
#include "av_comm/node_names.h"
#include "av_comm/sensor_util.h"
#include "fallback_level1/utils/odd_utils.h"
#include "platform_interface/platform_interface.h"
#include "platform_interface/ss_gflags.h"
#include "ros/param.h"
#include "status_machine/common.h"
#include "strings/stringprintf.h"
#include "tool_kits/util.h"
#include "voy_protos/canbus.pb.h"
#include "voy_protos/event_handle.pb.h"
#include "voy_protos/health.pb.h"
#include "voy_protos/system_control.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace system_supervisor::action_factory {
namespace status_machine {
namespace {
// This threshold which is a counter of cycles of received gateway topic
// message, is used to check if the communication between acu and mcu is break.
constexpr int kAdsFallbackCommInterruptThreshold = 20;
constexpr int kDelayRemindOpsTakeoverInMs = 1000;
constexpr int kNumberOfAcuAndMcuCommChannel = 2;
constexpr uint8_t kHighOperationalRisk = 0x04;

bool IsHighFrequencyFault(const std::string& node_name,
                          const pb::Health& health) {
  if (node_name != av_comm::node_name::kFaultDetector) {
    return false;
  }
  for (auto fault = health.faults().cbegin(); fault != health.faults().cend();
       ++fault) {
    if (fault->code() == ::pb::FaultDetectorFaultCode::HIGH_FREQUENCY_FAULT) {
      LOG(INFO) << "high frequency fault happens.";
      return true;
    }
  }
  return false;
}
// Obtain the availability of various takeover objects in the system available
void GetAllSystemAvailable(const uint8_t system_available, bool& is_ads_normal,
                           bool& is_software_normal, bool& is_hardware_normal) {
  is_ads_normal = ((system_available & (1 << 1)) != 0x2);
  is_software_normal = ((system_available & (1 << 2)) != 0x4);
  is_hardware_normal = ((system_available & (1 << 3)) != 0x8);
}

int GetFaultRiskValue(const std::string& fault_level) {
  int value = 0;
  std::stringstream string_to_hex(fault_level);
  string_to_hex >> std::hex >> value;
  return (value >> 4) & 0x0f;
}

void IsCollisionFaultMessage(
    const std::string& node_name, const pb::Health& health,
    const std::unordered_map<std::string,
                             std::tuple<uint8_t, uint8_t, uint8_t>>&
        collsion_upstream_map,
    bool& is_collision_detection_risk, bool& is_upstream_risk) {
  if (!PlatformInterface::GetInstance().IsCollisionDetect()) return;
  if (node_name == av_comm::node_name::kCollisionDetection) {
    for (auto fault = health.faults().cbegin(); fault != health.faults().cend();
         ++fault) {
      DLOG(INFO) << "collision risk:"
                 << GetFaultRiskValue(fault->fault_level());
      if (GetFaultRiskValue(fault->fault_level()) >= kHighOperationalRisk) {
        is_collision_detection_risk = true;
        break;
      }
    }
  } else {
    for (auto fault = health.faults().cbegin(); fault != health.faults().cend();
         ++fault) {
      std::string fault_message = node_name + "-" + fault->component() + "-" +
                                  fault->component_instance() + "-" +
                                  std::to_string(fault->code());
      auto iter = collsion_upstream_map.find(fault_message);
      if (iter != collsion_upstream_map.end()) {
        if (std::get<1>(iter->second) >= kHighOperationalRisk) {
          is_upstream_risk = true;
          break;
        }
      }
    }
  }
  DLOG(INFO) << "is_collision_detection_risk is" << is_collision_detection_risk;
}

bool IsRAEBFault(const std::string& node_name, const pb::Health& health) {
  if (node_name != av_comm::node_name::kTeleassist) {
    return false;
  }
  for (auto fault = health.faults().cbegin(); fault != health.faults().cend();
       ++fault) {
    if (fault->code() ==
        ::pb::TeleassistFaultCode::FALLBACK_REMOTE_EMERGENCY_PARKING) {
      LOG(INFO) << "RA EB fault occured.";
      return true;
    }
  }
  return false;
}

}  // namespace

bool IsAnyCameraNeedSelfCleaning(const std::string& node_name,
                                 const pb::Health& health) {
  if (node_name != av_comm::node_name::kTracking) {
    return false;
  }

  static const std::unordered_set<int> kCameraBlindnessFaultCodes = {
      ::pb::SensorAbnormalFaultCode::CAMERA_1_SLIGHT_BLINDNESS,
      ::pb::SensorAbnormalFaultCode::CAMERA_1_SEVERE_BLINDNESS,
      ::pb::SensorAbnormalFaultCode::CAMERA_1_EXTREME_BLINDNESS,
      ::pb::SensorAbnormalFaultCode::CAMERA_2_SLIGHT_BLINDNESS,
      ::pb::SensorAbnormalFaultCode::CAMERA_2_SEVERE_BLINDNESS,
      ::pb::SensorAbnormalFaultCode::CAMERA_2_EXTREME_BLINDNESS};

  const auto& faults = health.faults();
  return std::any_of(faults.begin(), faults.end(), [&](const auto& fault) {
    return fault.component() == av_comm::component::kSensorAbnormal &&
           kCameraBlindnessFaultCodes.count(fault.code());
  });
}

StatusMachineMessage::StatusMachineMessage()
    : StatusMachineMessage(PlatformInterface::GetInstance().GetCarId()) {
#if defined(ONBOARD_ONLY)
  // Start param check thread
  param_check_thread_ =
      std::thread(&StatusMachineMessage::ParamCheckWorker, this);
#endif
}

StatusMachineMessage::StatusMachineMessage(const std::string& car_id)
    : default_ss_event_id_(car_id + "_0"),
      vehicle_mode_in_sim_(::voy::VehicleMode::AUTO_FULL),
      system_status_(SystemStatusType::ADS),
      last_system_status_(SystemStatusType::ADS),
      vehicle_mode_(::voy::VehicleMode::MANUAL),
      last_vehicle_mode_(::voy::VehicleMode::MANUAL),
      prev_status_(StatusBitType::UNINITIALIZATION),
      curr_status_(StatusBitType::INITIALIZATION),
      is_enable_fallback_adas_(
          PlatformInterface::GetInstance().IsEnableFallbackAdas()),
      is_enable_fallback_autonomy_(
          PlatformInterface::GetInstance().IsEnableFallbackAutonomy()),
      is_enable_camera_cleaning_function_(
          PlatformInterface::GetInstance().IsEnableCameraCleaningFunction()) {
  for (size_t i = 0; i < kNumberOfAcuAndMcuCommChannel; i++) {
    AcuMcuCommChecker checker = {.last_rolling_counter = 0,
                                 .comm_interrupt_count = 0};
    acu_mcu_comm_checker_.push_back(checker);
  }
#if defined(ONBOARD_ONLY)
  action_factory_re_engage_publisher_ =
      message_util::CreatePublisher<::action_factory::pb::RE_ENGAGE>(
          av_comm::topic::kReEngage);
#endif
}

StatusMachineMessage::~StatusMachineMessage() {
#if defined(ONBOARD_ONLY)
  stop_param_check_ = true;
  if (param_check_thread_.joinable()) {
    param_check_thread_.join();
  }
#endif
}

#if defined(ONBOARD_ONLY)
void StatusMachineMessage::ParamCheckWorker() {
  std::string driverless_scene;
  if (ros::param::get(kDriverlessSceneKey, driverless_scene)) {
    LOG(INFO) << "driverless_scene is " << driverless_scene;
  }
  while (!stop_param_check_) {
    std::string driverless_scene;
    bool is_driverless = false;

    if (ros::param::get(kDriverlessSceneKey, driverless_scene)) {
      is_driverless = (driverless_scene == kDriverlessSceneValue);
    }
    if (is_driverless_scene_ != is_driverless) {
      LOG(INFO) << "driverless_scene changed to " << driverless_scene;
    }

    {
      std::lock_guard<std::mutex> lock(param_check_mutex_);
      is_driverless_scene_ = is_driverless;
    }

    // Check every 100ms
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
  }
}

bool StatusMachineMessage::GetIsDriverlessScene() {
  bool is_driverless{false};
  {
    std::lock_guard<std::mutex> lock(param_check_mutex_);
    is_driverless = is_driverless_scene_;
  }
  return is_driverless;
}

void StatusMachineMessage::CheckReEngageState() {
  bool is_driverless;
  {
    std::lock_guard<std::mutex> lock(param_check_mutex_);
    is_driverless = is_driverless_scene_;
  }

  if (!is_driverless && is_enable_re_engage_) {
    SetIsEnableReEngage(false);
    LOG(INFO) << "SetIsEnableReEngage to false due to non-driverless scene";
  }
}
#endif

void StatusMachineMessage::UpdateUnmannedMessage() {
  if (!is_recovery_timeout_) {
    is_recovery_timeout_ = is_need_ops_restore_;
  }
  is_door_status_abnormal_ = is_door_opened_;
}

void StatusMachineMessage::UpdateCollsionMessageRequired(
    const pb::SystemState& system_state) {
  voy::perception::CollisionType collision_type =
      system_state.collision_message_required()
          .perception_collision_detection()
          .collision_type();
  if (collision_type == voy::perception::CollisionType::COLLISION ||
      collision_type == voy::perception::CollisionType::MAYBE_COLLISION) {
    SetCollisionMessageRequired(system_state.collision_message_required());
  }
}

SelfDrivingStatus StatusMachineMessage::GetSelfDrivingStatus() {
  if (IsVehicleStationary(collision_message_required_)) {
    return SelfDrivingStatus::STATIONARY;
  } else {
    return SelfDrivingStatus::DRIVING;
  }
}
void StatusMachineMessage::UpdateFallbackAdasStatus() {
  switch (stop_value_) {
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_06):
      system_status_ =
          SystemStatusType::FALLBACK_ADAS_INLANE_STOP;  // inline stop
      break;
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_08):
      system_status_ = SystemStatusType::FALLBACK_ADAS_URGENCY_SLOW;  // -3 EB
      break;
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_10):
      system_status_ = SystemStatusType::FALLBACK_ADAS_URGENCY_QUICK;  // -5EB
      break;
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_13):
      system_status_ =
          SystemStatusType::FALLBACK_ADAS_URGENCY;  // Conditional AEB

      break;
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_14):
      system_status_ =
          SystemStatusType::FALLBACK_ADAS_URGENCY_IMMEDIATELY;  // -10EB
      break;
    default:
      system_status_ = SystemStatusType::FALLBACK_ADAS_INLANE_STOP;
      break;
  }
}

void StatusMachineMessage::UpdateFallbackAutonomyStatus() {
  switch (stop_value_) {
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_06):
      system_status_ = SystemStatusType::FALLBACK_AUTONOMY;
      break;
    default:
      system_status_ = SystemStatusType::FALLBACK_AUTONOMY;
      break;
  }
}

void StatusMachineMessage::InitFaultLevelFromJson(
    const ::action_factory::pb::CollisionUpstreamFaults&
        collision_upstream_faults) {
  for (auto fault_unit : collision_upstream_faults.fault_units()) {
    auto level_tuple = std::make_tuple(fault_unit.system_available(),
                                       fault_unit.business_impact(),
                                       fault_unit.park_urgency_level());
    std::string instance = fault_unit.node() + "-" + fault_unit.component() +
                           "-" + fault_unit.component_instance() + "-" +
                           std::to_string(fault_unit.code());
    collision_upstream_map_.insert({instance, level_tuple});
    DLOG(INFO) << "instance:" << instance;
  }
  LOG(INFO) << "collision_upstream_map_:" << collision_upstream_map_.size();
}

void StatusMachineMessage::UpdateMrcStatus() {
  switch (stop_value_) {
    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_01):
      system_status_ = SystemStatusType::ADS;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_03):
      system_status_ = SystemStatusType::MRM_HARBOR;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_04):
      system_status_ = SystemStatusType::MRM_PULLRIGHT;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_06):
      system_status_ = SystemStatusType::MRM_INLANE;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_08):
      system_status_ = SystemStatusType::MRM_URGENCY_SLOW;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_10):
      system_status_ = SystemStatusType::MRM_URGENCY_QUICK;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_12):
      system_status_ = SystemStatusType::MRM_URGENCY_IMMEDIATELY;
      break;

    case static_cast<int>(fault_detector::pb::ParkUrgencyType::STOP_VALUE_14):
      system_status_ = SystemStatusType::MRM_URGENCY;
      break;

    default:
      system_status_ = SystemStatusType::MRM_PULLRIGHT;
      break;
  }
}
void StatusMachineMessage::ParseGroundSupportCommand(
    const ::voy::VehicleGroundAssist& message) {
  static const std::string kSourceName =
      voy::CommandSource_Name(voy::kGroundAssistApp);
  // Check the command type from the GroundSupportCommand message
  switch (message.command_type()) {
    case voy::KEEP_VEHICLE_STATIONARY: {
      // Set the parking retention flag to true
      ActiveParkingRetention(kSourceName);
      break;
    }
    case voy::RELEASE_VEHICLE_STATIONARY: {
      // Set the parking retention flag to false
      ReleaseParkingRetention(kSourceName);
      break;
    }
    default: {
      // Handle unknown command types
      LOG(WARNING) << "Received unknown ground support command type: "
                   << message.command_type();
      break;
    }
  }
  LOG(WARNING) << "Source of parking retention command: "
               << voy::CommandType_Name(message.command_type());
}
void StatusMachineMessage::ParseMrcRequest(
    const ::mrc::pb::MrcRequest& message) {
  if (canbus_.mode() != ::voy::VehicleMode::AUTO_FULL) {
    is_in_mrm_process_ = false;
    return;
  }
  if (message.mrm_progress() == mrc::pb::MrcProgress::FINISHED) {
    LOG(INFO) << "mrc has parked";
    is_vehicle_parking_completed_ = true;
  } else {
    is_vehicle_parking_completed_ = false;
  }
  if (message.mrm_type() != mrc::pb::MrmType::UNDEFINED) {
    is_in_parking_process_ = true;
    is_in_mrm_process_ = true;
  } else {
    is_in_parking_process_ = false;
    is_in_mrm_process_ = false;
  }
}

void StatusMachineMessage::ParseMessage(const ::pb::SystemState& system_state) {
  DCHECK(system_state.value() == system_state.ads_status())
      << "value  and ads_status  must be the same. Please confirm with the "
         "fault-dector node ";
  ResetMessage();
  SetSystemValue(system_state.value());
  SetLastVehicleMode(GetVehicleMode());
  SetVehicleMode(system_state.vehicle_mode());

  UpdateSystemLevel(system_state.system_level());
  is_collision_detection_risk_ = false;
  is_upstream_risk_ = false;
  is_camera_need_self_cleaning_ = false;
  bool is_ads_available = false;
  bool is_software_available = false;
  bool is_hardware_available = false;
  GetAllSystemAvailable(system_available_, is_ads_available,
                        is_software_available, is_hardware_available);
  bool tmp_is_high_frequency_fault = false;

  for (const auto& node_health_info : system_state.node_health_infos()) {
    const auto& node_name = node_health_info.node_name();
    const pb::Health& health = node_health_info.health();
    if (IsHighFrequencyFault(node_name, health) == true) {
      tmp_is_high_frequency_fault = true;
    }
    if (IsRAEBFault(node_name, health) == true) {
      is_ra_eb_fault_ = true;
    }
    IsCollisionFaultMessage(node_name, health, collision_upstream_map_,
                            is_collision_detection_risk_, is_upstream_risk_);

    // If there is any camera need self cleaning, set the flag to true.
    is_camera_need_self_cleaning_ |=
        IsAnyCameraNeedSelfCleaning(node_name, health);
  }

  SetLastSystemStatus(GetSystemStatus());
  system_status_ = SystemStatusType::ADS;
  DLOG(INFO) << "is_collision_detection_risk_:" << is_collision_detection_risk_;
  DLOG(INFO) << "is_software_available:" << is_software_available;
  DLOG(INFO) << "is_enable_fallback_autonomy_:" << is_enable_fallback_autonomy_;

  if (is_ads_available) {
    if (risk_value_ >= kHighOperationalRisk) {
      UpdateMrcStatus();
    }
  } else if (is_enable_fallback_autonomy_ && is_software_available &&
             (!PlatformInterface::GetInstance().IsCollisionDetect() ||
              !is_collision_detection_risk_)) {
    if (risk_value_ >= kHighOperationalRisk) {
      UpdateFallbackAutonomyStatus();
    }
  } else if (is_enable_fallback_adas_ && is_in_fallback_adas_odd_ &&
             is_hardware_available) {
    if (risk_value_ >= kHighOperationalRisk) {
      UpdateFallbackAdasStatus();
    }
  } else {
    SetSystemStatus(SystemStatusType::MANUAL);
  }

  SetIsHighFrequencyFault(tmp_is_high_frequency_fault);
  ss_last_event_id_ = ss_event_id_;
  ss_event_id_ = system_state.event_info().ss_event_id();
}

void StatusMachineMessage::UpdateFallbackl2VehicleParkingStatus() {
  if (canbus_.mode() != ::voy::VehicleMode::AUTO_FULL &&
      canbus_.mode() != ::voy::VehicleMode::FALLBACK_ADAS_CONTROL &&
      canbus_.mode() != ::voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL) {
    is_vehicle_parking_completed_ = false;
    is_in_parking_process_ = false;
    return;
  }
  if (canbus_.mode() != ::voy::VehicleMode::AUTO_FULL) {
    is_in_parking_process_ = true;
  }
  if (canbus_.mode() == ::voy::VehicleMode::FALLBACK_ADAS_CONTROL) {
    if (fallback_control_status_ == ::voy::DetailedInfo::KEEPING_STOP ||
        fallback_control_status_ ==
            ::voy::DetailedInfo::COLLISION_KEEPING_STOP) {
      LOG(INFO) << "fallbackl2 has parked";
      is_vehicle_parking_completed_ = true;
    } else {
      is_vehicle_parking_completed_ = false;
    }
  }
}

void StatusMachineMessage::UpdateFallbackl1VehicleParkingStatus(
    const ::planner::pb::Trajectory& message) {
  if (canbus_.mode() != ::voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL) {
    return;
  }
  if (message.scene_metadata().maneuver() ==
      ::planner::pb::FALLBACK_STATIONARY) {
    LOG(INFO) << "fallbackl1 has parked";
    is_vehicle_parking_completed_ = true;
  } else {
    is_vehicle_parking_completed_ = false;
  }
}

bool StatusMachineMessage::CheckNetworkStatusOfGroundAssist(
    const std::shared_ptr<const ::pb::SystemState> system_state) {
  for (const auto& health_info : system_state->node_health_infos()) {
    if (health_info.node_name() == av_comm::node_name::kTelemetry) {
      const auto& faults = health_info.health().faults();
      return std::any_of(faults.begin(), faults.end(), [](const auto& fault) {
        return fault.code() == pb::TelemetryFaultCode::CONNECTION_LOST;
      });
    }
  }
  return false;
}

void StatusMachineMessage::CheckIfSsExecParkingRetention(
    const std::shared_ptr<const ::pb::SystemState> system_state) {
  // if parking retention is activated and the current vehicle
  // mode is manual, then release parking retention.
  // if current restore status is CANNOT_RESTART and the network is
  // disconnected, then active parking retention.
  static const std::string kSourceName =
      voy::CommandSource_Name(voy::kSsAutoSource);
  if (IsGroundSupportParkingRetentionActive() &&
      system_state->vehicle_mode() == ::voy::VehicleMode::MANUAL) {
    ReleaseParkingRetention(kSourceName);
  } else if (IsNeedOpsRestore() &&
             CheckNetworkStatusOfGroundAssist(system_state)) {
    ActiveParkingRetention(kSourceName);
  }
}

void StatusMachineMessage::ParseGatewayMessage(
    const ::voy::Canbus& canbus_message) {
  canbus_ = canbus_message;
  const ::voy::DetailedInfo& detailed_info =
      canbus_message.vehicle_detail_info();

  bool temp_is_vcan10_interrupted = IsCommInterruptedWithFallbackAdas(
      detailed_info.fallback_rolling_counter(), 0);
  bool temp_is_vcan4_interrupted = IsCommInterruptedWithFallbackAdas(
      detailed_info.redundant_fb_rolling_counter(), 1);

  if (!temp_is_vcan10_interrupted && !temp_is_vcan4_interrupted) {
    acu_mcu_comm_status_ = AcuMcuCommStatus::COMM_NORMAL;
  } else if (!temp_is_vcan10_interrupted && temp_is_vcan4_interrupted) {
    acu_mcu_comm_status_ = AcuMcuCommStatus::COMM_DEGRADE_VCAN4_BREAK;
  } else if (temp_is_vcan10_interrupted && !temp_is_vcan4_interrupted) {
    acu_mcu_comm_status_ = AcuMcuCommStatus::COMM_DEGRADE_VCAN10_BREAK;
  } else {
    // is_in_fallback_adas_odd_ = false;
    acu_mcu_comm_status_ = AcuMcuCommStatus::COMM_INTERRUPT;
  }

  switch (acu_mcu_comm_status_) {
    case AcuMcuCommStatus::COMM_NORMAL:
      fallback_control_status_ = detailed_info.fallback_control_status();
      break;
    case AcuMcuCommStatus::COMM_DEGRADE_VCAN4_BREAK:
      fallback_control_status_ = detailed_info.fallback_control_status();
      LOG(ERROR) << "Vcan4 comm abnormal.";
      break;
    case AcuMcuCommStatus::COMM_DEGRADE_VCAN10_BREAK:
      fallback_control_status_ = detailed_info.redundant_fb_control_status();
      LOG(ERROR) << "Vcan10 comm abnormal.";
      break;
    case AcuMcuCommStatus::COMM_INTERRUPT:
      LOG(ERROR) << "vcan10 and vcan4 were interupted.";
      break;
    default:
      LOG(ERROR) << "invalid status of acu and mcu communication.";
      break;
  }

  if (acu_mcu_comm_status_ == AcuMcuCommStatus::COMM_INTERRUPT ||
      ((fallback_control_status_ == ::voy::DetailedInfo::ADS_CONTROL ||
        fallback_control_status_ == ::voy::DetailedInfo::DOING_STOP ||
        fallback_control_status_ == ::voy::DetailedInfo::COLLISION_DOING_STOP ||
        fallback_control_status_ == ::voy::DetailedInfo::KEEPING_STOP ||
        fallback_control_status_ ==
            ::voy::DetailedInfo::COLLISION_KEEPING_STOP) &&
       acu_mcu_comm_status_ != AcuMcuCommStatus::COMM_INTERRUPT)) {
    is_in_fallback_adas_odd_ = true;
  } else {
    is_in_fallback_adas_odd_ = false;
    LOG(INFO) << "Now It's out of fallback adas odd, info is: "
              << DetailedInfo_FallBackStatus_Name(fallback_control_status_)
              << ", acu_mcu_comm_status: "
              << static_cast<int>(acu_mcu_comm_status_);
  }
  UpdateFallbackl2VehicleParkingStatus();
  if (fallback_control_status_ == ::voy::DetailedInfo::DOING_STOP ||
      fallback_control_status_ == ::voy::DetailedInfo::COLLISION_DOING_STOP) {
    fallback_ads_control_state_ = FallbackControlState::DOING_STOP;
  } else if (fallback_control_status_ == ::voy::DetailedInfo::KEEPING_STOP ||
             fallback_control_status_ ==
                 ::voy::DetailedInfo::COLLISION_KEEPING_STOP) {
    fallback_ads_control_state_ = FallbackControlState::KEEPING_STOP;
  } else {
    fallback_ads_control_state_ = FallbackControlState::DEFAULT;
  }
  if (detailed_info.fallback_mcu_version() != fallback_mcu_version_) {
    fallback_mcu_version_ = detailed_info.fallback_mcu_version();
  }
}

// Obtain the three levels of the system
void StatusMachineMessage::UpdateSystemLevel(const std::string& system_level) {
  uint16_t value = 0x0;
  std::stringstream string_to_hex(system_level);
  string_to_hex >> std::hex >> value;
  stop_value_ = (uint8_t)(value & 0x0f);
  risk_value_ = (uint8_t)((value >> 4) & 0x0f);
  system_available_ = (uint8_t)(value >> 8);
}

#if defined(ONBOARD_ONLY)

void StatusMachineMessage::ParseVehicleGroundAssistMessage(
    const ::voy::VehicleGroundAssist& vehicle_ground_assist) {
  if (vehicle_ground_assist.command_type() ==
      voy::CommandType::RELEASE_DEFENSE) {
    SetIsKeepDefense(false);
    if (is_enable_re_engage_) {
      LOG(INFO) << "SetIsEnableReEngage false";
    }
    SetIsEnableReEngage(false);
  } else if (vehicle_ground_assist.command_type() ==
             voy::CommandType::KEEP_DEFENSE) {
    SetIsKeepDefense(true);
    // Set ReEngage true while: KEEP_DEFENSE && (odd0 || odd-1 || pre) && auto
    // && driverless.
    if ((odd_type_ == kOdd0TypeValue || odd_type_ == kOdd_1TypeValue ||
         odd_type_ == kOddPreTypeValue) &&
        !GetIsEnableReEngage() && curr_status_ == StatusBitType::AUTO) {
      std::string driverless_scene;
      bool is_driverless;
      {
        std::lock_guard<std::mutex> lock(param_check_mutex_);
        is_driverless = is_driverless_scene_;
      }
      if (is_driverless && driverless_scene == kDriverlessSceneValue &&
          is_keep_defense_) {
        SetIsEnableReEngage(true);
      }
    }
  }
}

void StatusMachineMessage::SetIsEnableReEngage(const bool is_enable_re_engage) {
  if (is_enable_re_engage_ && !is_enable_re_engage) {
    fault_reporter_.RemoveFault(
        pb::ActionFactoryFaultCode::AUTO_DISENGAGE_RE_ENGAGE);
    LOG(INFO) << "Exit Auto DisEngage";
  }
  if (is_enable_re_engage != is_enable_re_engage_) {
    LOG(INFO) << "IsEnableReEngage set to:" << is_enable_re_engage;
  }
  is_enable_re_engage_ = is_enable_re_engage;
}

void StatusMachineMessage::SetIsKeepDefense(const bool is_keep_defense) {
  if (is_keep_defense != is_keep_defense_) {
    LOG(INFO) << "IsKeepDefense set to:" << is_keep_defense;
  }
  is_keep_defense_ = is_keep_defense;
}

void StatusMachineMessage::AutoDisEngage() {
  ::action_factory::pb::RE_ENGAGE re_engage_message;
  re_engage_message.set_timestamp(base::Now());
  re_engage_message.set_is_enable_re_engage(true);
  action_factory_re_engage_publisher_.Publish(re_engage_message);
  fault_reporter_.AddFault(pb::ActionFactoryFaultCode::AUTO_DISENGAGE_RE_ENGAGE,
                           "Enter Auto DisEngage");
}

#endif

void StatusMachineMessage::ParseFallbackPlanningMessage(
    const ::planner::pb::Trajectory& message) {
  UpdateFallbackl1VehicleParkingStatus(message);
  if (message.scene_metadata().maneuver() ==
          ::planner::pb::FALLBACK_REACH_DESTINATION ||
      message.scene_metadata().maneuver() ==
          ::planner::pb::FALLBACK_STATIONARY) {
    fallback_autonomy_control_state_ = FallbackControlState::KEEPING_STOP;
  } else if (message.scene_metadata().maneuver() ==
             ::planner::pb::FALLBACK_IN_MOTION) {
    fallback_autonomy_control_state_ = FallbackControlState::DOING_STOP;
  } else {
    fallback_autonomy_control_state_ = FallbackControlState::DEFAULT;
  }
}

void StatusMachineMessage::ParseRestoreRecordMessage(
    const ::service_restore::pb::RestoreRecord& restore_message) {
  is_need_ops_restore_ = false;
  const ::service_restore::pb::RestoreRecord::RestartStatus& restart_status =
      restore_message.restart_status();
  if (restart_status == ::service_restore::pb::RestoreRecord::CANNOT_RESTART) {
    is_need_ops_restore_ = true;
  }
}

void StatusMachineMessage::ParseVehicleBodyInfoMessage(
    const ::voy::VehicleBodyInfo& vehicle_body_info) {
  // Get message of door status
  is_door_opened_ = false;
  for (const auto& door_open_status : vehicle_body_info.door_opened()) {
    if (door_open_status.second == voy::VehicleBodyInfo::YES) {
      LOG(INFO) << "Door opened: " << door_open_status.first;
      is_door_opened_ = true;
      break;
    }
  }
}

bool StatusMachineMessage::IsCommInterruptedWithFallbackAdas(
    const int32_t rolling_counter, const int index) {
  bool ret = false;
  if (rolling_counter == acu_mcu_comm_checker_.at(index).last_rolling_counter) {
    acu_mcu_comm_checker_.at(index).comm_interrupt_count++;
  } else {
    acu_mcu_comm_checker_.at(index).comm_interrupt_count = 0;
    acu_mcu_comm_checker_.at(index).last_rolling_counter = rolling_counter;
  }

  // If this field remains unchanged within 200s, the communication
  // with the fallback adas is considered interrupted.
  if (acu_mcu_comm_checker_.at(index).comm_interrupt_count >=
      kAdsFallbackCommInterruptThreshold) {
    ret = true;
    // To prevent overflow problems caused by counter accumulation,
    // a reset operation is performed.
    acu_mcu_comm_checker_.at(index).comm_interrupt_count =
        kAdsFallbackCommInterruptThreshold;
  }

  return ret;
}

void StatusMachineMessage::UpdateDelayToRemindOpsTakeoverTimer() {
  if (delay_remind_ops_takeover_timer_ == 0) {
    is_delay_to_remind_ops_takeover_ = false;
    return;
  }

  int interval = base::Now() - delay_remind_ops_takeover_timer_;
  if (interval > kDelayRemindOpsTakeoverInMs) {
    is_delay_to_remind_ops_takeover_ = false;
    delay_remind_ops_takeover_timer_ = 0;
  } else {
    is_delay_to_remind_ops_takeover_ = true;
    LOG(INFO) << "Within 1s of ADS entering Transition mode, there"
                 " will be no prompt to manually take over.";
  }
}

bool StatusMachineMessage::IsUptoLimitTime(int64_t timer, int threshold) {
  DLOG(INFO) << "timer:" << timer;
  DLOG(INFO) << "now:" << base::Now();
  DLOG(INFO) << "threshold:" << base::Now() - timer;
  return timer != 0 && (base::Now() - timer > threshold);
}

bool StatusMachineMessage::IsEventHandleProcessingCompleted(
    ::pb::EventHandleInfo& event_info) {
  if (ss_event_id_ == default_ss_event_id_) {
    is_takeover_success_ = EventHandleState::DEFAULT;
    is_restore_success_ = EventHandleState::DEFAULT;
  }

  if (vehicle_mode_ == ::voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL) {
    if (fallback_autonomy_control_state_ ==
        FallbackControlState::KEEPING_STOP) {
      if (is_takeover_success_ == EventHandleState::DEFAULT)
        is_takeover_success_ = EventHandleState::SUCCESS;
    }
  } else if (vehicle_mode_ == ::voy::VehicleMode::FALLBACK_ADAS_CONTROL) {
    if (fallback_ads_control_state_ == FallbackControlState::KEEPING_STOP) {
      if (is_takeover_success_ == EventHandleState::DEFAULT)
        is_takeover_success_ = EventHandleState::SUCCESS;
    }
  } else if ((vehicle_mode_ == ::voy::VehicleMode::MANUAL ||
              vehicle_mode_ == ::voy::VehicleMode::EMERGENCY) &&
             (last_vehicle_mode_ ==
                  ::voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL ||
              last_vehicle_mode_ ==
                  ::voy::VehicleMode::FALLBACK_ADAS_CONTROL)) {
    if (is_takeover_success_ == EventHandleState::DEFAULT) {
      is_takeover_success_ = EventHandleState::FAIL;
      is_restore_success_ = EventHandleState::FAIL;
    } else {
      is_restore_success_ = EventHandleState::FAIL;
    }
  } else if (vehicle_mode_ == ::voy::VehicleMode::AUTO_FULL &&
             (last_vehicle_mode_ ==
                  ::voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL ||
              last_vehicle_mode_ ==
                  ::voy::VehicleMode::FALLBACK_ADAS_CONTROL)) {
    is_takeover_success_ = EventHandleState::SUCCESS;
    is_restore_success_ = EventHandleState::SUCCESS;
  } else {
    // Keep the value of is_takeover_success_ and is_restore_success_.
  }

  if (is_takeover_success_ != EventHandleState::DEFAULT &&
      is_restore_success_ != EventHandleState::DEFAULT) {
    if (ss_event_id_ == default_ss_event_id_ &&
        ss_last_event_id_ != default_ss_event_id_) {
      event_info.set_ss_event_id(ss_last_event_id_);
    } else {
      event_info.set_ss_event_id(ss_event_id_);
    }
    if (is_takeover_success_ == EventHandleState::SUCCESS) {
      event_info.set_ss_is_takeover_success(pb::EventHandleInfo::SUCCESS);
    } else {
      event_info.set_ss_is_takeover_success(pb::EventHandleInfo::FAIL);
    }
    if (is_restore_success_ == EventHandleState::SUCCESS) {
      event_info.set_ss_is_restore_success(pb::EventHandleInfo::SUCCESS);
    } else {
      event_info.set_ss_is_restore_success(pb::EventHandleInfo::FAIL);
    }
    event_info.set_mcu_version(std::to_string(fallback_mcu_version_));
    event_info.set_timestamp(base::Now());
    return true;
  } else {
    return false;
  }
}

// Set the state of the ADS sended to fallback.
void StatusMachineMessage::SendAdsStateToFallback(bool trigger_fallback_adas) {
  LOG(INFO) << "Fallbackl2 takeover is required";
  if (trigger_fallback_adas) {
    if (!IsStatusTypeInFallbackAdas(system_status_)) {
      UpdateFallbackAdasStatus();
    }
    switch (system_status_) {
      case SystemStatusType::FALLBACK_ADAS_INLANE_STOP:
        StatusMachineMessage::HandleFallbackl2TriggerMethod(
            Fallbackl2TriggerMethod::NORMAL_TRIGGER);
        break;
      case SystemStatusType::FALLBACK_ADAS_URGENCY:
        StatusMachineMessage::HandleFallbackl2TriggerMethod(
            Fallbackl2TriggerMethod::URGENCY);
        break;
      case SystemStatusType::FALLBACK_ADAS_URGENCY_SLOW:
        StatusMachineMessage::HandleFallbackl2TriggerMethod(
            Fallbackl2TriggerMethod::SLOW_URGENCY);
        break;
      case SystemStatusType::FALLBACK_ADAS_URGENCY_QUICK:
        StatusMachineMessage::HandleFallbackl2TriggerMethod(
            Fallbackl2TriggerMethod::QUICK_URGENCY);
        break;
      case SystemStatusType::FALLBACK_ADAS_URGENCY_IMMEDIATELY:
        StatusMachineMessage::HandleFallbackl2TriggerMethod(
            Fallbackl2TriggerMethod::IMMEDIATELY_URGENCY);
        break;
      default:
        LOG(ERROR) << "Unexpected way of Fallbackl2";
        break;
    }
  } else {
    StatusMachineMessage::HandleFallbackl2TriggerMethod(
        Fallbackl2TriggerMethod::NO_TRIGGER);
  }
}

// During a continuous control process of fallbackl2, it can only be upgraded
// and not downgraded
void StatusMachineMessage::HandleFallbackl2TriggerMethod(
    Fallbackl2TriggerMethod trigger_method) {
  switch (trigger_method) {
    case Fallbackl2TriggerMethod::NO_TRIGGER:
      trigger_method_of_fallbackl2_ = Fallbackl2TriggerMethod::NO_TRIGGER;
      LOG(INFO) << "It dont need trigger fallbackl2";
      break;
    case Fallbackl2TriggerMethod::NORMAL_TRIGGER:
      if (current_control_way_of_fallbackl2_ <
          Fallbackl2TriggerMethod::NORMAL_TRIGGER) {
        trigger_method_of_fallbackl2_ = Fallbackl2TriggerMethod::NORMAL_TRIGGER;
        LOG(INFO) << "It need trigger fallbackl2 in normal way";
      }
      break;
    case Fallbackl2TriggerMethod::URGENCY:
      if (current_control_way_of_fallbackl2_ <
          Fallbackl2TriggerMethod::URGENCY) {
        trigger_method_of_fallbackl2_ = Fallbackl2TriggerMethod::URGENCY;
        LOG(INFO) << "It need trigger fallbackl2 in normal urgency";
      }
      break;
    case Fallbackl2TriggerMethod::SLOW_URGENCY:
      if (current_control_way_of_fallbackl2_ <
          Fallbackl2TriggerMethod::SLOW_URGENCY) {
        trigger_method_of_fallbackl2_ = Fallbackl2TriggerMethod::SLOW_URGENCY;
        LOG(INFO) << "It need trigger fallbackl2 in slow urgency";
      }
      break;
    case Fallbackl2TriggerMethod::QUICK_URGENCY:
      if (current_control_way_of_fallbackl2_ <
          Fallbackl2TriggerMethod::QUICK_URGENCY) {
        trigger_method_of_fallbackl2_ = Fallbackl2TriggerMethod::QUICK_URGENCY;
        LOG(INFO) << "It need trigger fallbackl2 in quick urgency";
      }
      break;
    case Fallbackl2TriggerMethod::IMMEDIATELY_URGENCY:
      if (current_control_way_of_fallbackl2_ <
          Fallbackl2TriggerMethod::IMMEDIATELY_URGENCY) {
        trigger_method_of_fallbackl2_ =
            Fallbackl2TriggerMethod::IMMEDIATELY_URGENCY;
        LOG(INFO) << "It need trigger fallbackl2 in immediately urgency";
      }
      break;
    default:
      LOG(ERROR) << "Invalid value of trigger_method_of_fallbackl2_: "
                 << static_cast<int>(trigger_method_of_fallbackl2_);
      break;
  }
  if (trigger_method_of_fallbackl2_ != Fallbackl2TriggerMethod::NO_TRIGGER) {
    current_control_way_of_fallbackl2_ = trigger_method_of_fallbackl2_;
  }
}

Fallbackl2TriggerMethod StatusMachineMessage::GetTriggerMethodOfFallbackl2()
    const {
  return trigger_method_of_fallbackl2_;
}

void StatusMachineMessage::SetCurrentControlWayOfFallbackl2(
    const Fallbackl2TriggerMethod& current_control_way_of_fallbackl2) {
  current_control_way_of_fallbackl2_ = current_control_way_of_fallbackl2;
}

voy::VehicleMode StatusMachineMessage::GetModeInSim() {
  if (is_modify_mode_in_sim_flag_ &&
      (vehicle_mode_ != voy::AUTO_FULL &&
       vehicle_mode_ != voy::FALLBACK_ADAS_CONTROL &&
       vehicle_mode_ != voy::FALLBACK_AUTONOMY_CONTROL)) {
    is_modify_mode_in_sim_flag_ = false;
    LOG(INFO) << "Human actions cause mode changing and reset the variables.";
  }
  if (is_modify_mode_in_sim_flag_)
    return vehicle_mode_in_sim_;
  else
    return vehicle_mode_;
}

bool StatusMachineMessage::IsCollisionDetectorNodeUnavailable(
    const std::string& node_name, const pb::Health& health) {
  (void)node_name;
  (void)health;
  // if (node_name != av_comm::node_name::kCollisionDetection) {
  //   return false;
  // }
  // if (health.faults_size()) {
  //   for (const pb::Fault& fault : health.faults()) {
  //     if (fault.code() < 2000) {
  //       LOG(INFO) << "Collision detector node is unavailable.";
  //       return true;
  //     }
  //   }
  // }
  return false;
}

}  // namespace  status_machine
}  // namespace system_supervisor::action_factory
