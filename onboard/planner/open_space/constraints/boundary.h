#ifndef ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_BOUNDARY_H_
#define ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_BOUNDARY_H_

#include <utility>
#include <vector>

#include "geometry/model/polyline_curve.h"
#include "onboard/planner/open_space/constraints/util.h"

namespace planner {

namespace open_space {

// Class Boundary defines the general polyline type of occupancy that should be
// handled as motion plan constraints by open space motion plan generation.
class Boundary {
 public:
  // Enum class PassSide defines the drivable side of a Boundary.
  enum class PassSide { BOTH, LEFT, RIGHT };

  // Ctor for polyline curve version.
  Boundary(math::geometry::PolylineCurve2d boundary_curve,
           RequiredClearance required_clearance,
           ViolationTolerance violation_tolerance, PassSide pass_side,
           ViolationPenaltyLevel violation_penalty_level, int constraint_idx)
      : constraint_id_(OccupancyType::BOUNDARY, constraint_idx),
        polyline_(std::move(boundary_curve)),
        required_clearance_(std::move(required_clearance)),
        violation_tolerance_(violation_tolerance),
        violation_penalty_level_(violation_penalty_level),
        pass_side_(pass_side) {}

  Boundary(std::vector<math::geometry::Point2d> boundary_pts,
           RequiredClearance required_clearance,
           ViolationTolerance violation_tolerance, PassSide pass_side,
           ViolationPenaltyLevel violation_penalty_level, int constraint_idx)
      : constraint_id_(OccupancyType::BOUNDARY, constraint_idx),
        polyline_(std::move(boundary_pts)),
        required_clearance_(std::move(required_clearance)),
        violation_tolerance_(violation_tolerance),
        violation_penalty_level_(violation_penalty_level),
        pass_side_(pass_side) {}

  // NOTE: This version of GetProximity just naively wraps the base polyline
  // curve GetProximity.
  math::ProximityQueryInfo GetProximity(
      const math::geometry::Point2d& pt,
      math::pb::UseExtensionFlag extension_flag) const {
    DCHECK_GE(polyline_.size(), math::kMinimumInterpolationPointNum);
    return polyline_.GetProximity(pt, extension_flag);
  }

  // Returns true if a queried pt is on the pass side of a boundary.
  bool IsOnPassSide(const math::ProximityQueryInfo& proximity) const {
    if (proximity.relative_position != math::RelativePosition::kWithIn) {
      return true;
    }
    if (pass_side_ == PassSide::LEFT) {
      return proximity.side == math::pb::Side::kLeft;
    }
    if (pass_side_ == PassSide::RIGHT) {
      return proximity.side == math::pb::Side::kRight;
    }
    DCHECK(pass_side_ == PassSide::BOTH);
    return proximity.side != math::pb::Side::kOn;
  }
  bool IsOnPassSide(const math::geometry::Point2d& pt) const {
    return IsOnPassSide(GetProximity(pt, math::pb::UseExtensionFlag::kForbid));
  }

  // Const accessor.
  const OccupancyConstraintID& constraint_id() const { return constraint_id_; }
  const math::geometry::PolylineCurve2d& polyline() const {
    DCHECK_GE(polyline_.size(), math::kMinimumInterpolationPointNum);
    return polyline_;
  }
  const RequiredClearance& required_clearance() const {
    return required_clearance_;
  }
  ViolationTolerance violation_tolerance() const {
    return violation_tolerance_;
  }
  ViolationPenaltyLevel violation_penalty_level() const {
    return violation_penalty_level_;
  }
  PassSide pass_side() const { return pass_side_; }

 private:
  OccupancyConstraintID constraint_id_;
  math::geometry::PolylineCurve2d polyline_;

  //
  // Attributes.
  //
  RequiredClearance required_clearance_;
  ViolationTolerance violation_tolerance_ = ViolationTolerance::FORBID;
  ViolationPenaltyLevel violation_penalty_level_ =
      ViolationPenaltyLevel::CRITICAL;
  // The un-occupied side of the boundary.
  PassSide pass_side_ = PassSide::BOTH;
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_BOUNDARY_H_
