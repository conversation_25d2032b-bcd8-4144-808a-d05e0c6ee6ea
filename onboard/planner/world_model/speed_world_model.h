#ifndef ONBOARD_PLANNER_WORLD_MODEL_SPEED_WORLD_MODEL_H_
#define ONBOARD_PLANNER_WORLD_MODEL_SPEED_WORLD_MODEL_H_

#include <map>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

#include <tbb/concurrent_unordered_map.h>

#include "base/now.h"
#include "planner/assist/assist_availability_analyzer.h"
#include "planner/assist/assist_directive_generator.h"
#include "planner/assist/assist_task_scheduler.h"
#include "planner/behavior/types/behavior_decision.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/conditional_predicted_trajectory_wrapper.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/utility/common/config_io.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner.h"
#include "planner/world_model/current_lane_reasoner/current_lanes_speed_data.h"
#include "planner/world_model/immediate_pull_over/immediate_pull_over_state_manager.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/lane_congestion/lane_congestion_detector.h"
#include "planner/world_model/lane_congestion/lane_congestion_info.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/planner_object/planner_objects_history.h"
#include "planner/world_model/pull_over/pull_over_request_info.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner/world_model/regional_map/stuck_avoidance/changed_lane_structure_reasoner.h"
#include "planner/world_model/regional_map/stuck_avoidance/map_change_area_processor_param.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_reasoner.h"
#include "planner/world_model/regional_sections_locator/regional_sections_locator.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/snapshot/snapshot.h"
#include "planner/world_model/traffic_participant/predicted_trajectory.h"
#include "planner/world_model/traffic_participant/tracked_object.h"
#include "planner/world_model/traffic_signal_reasoner/traffic_signal_reasoner.h"
#include "planner/world_model/waypoint_assist/waypoint_assist_tracker.h"
#include "planner_protos/nominal_path_planner_config.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/state/world_model.pb.h"
#include "planner_protos/world_model_config.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/pnc_map_service.h"

namespace planner {

namespace speed {
class ReasoningTestFixture;
}  // namespace speed
class LaneChangeTestFixture;

// Class WorldModel incorporates all the surrounding information
// around the robot: for example, mapping and perception information.
//
// TODO(Guojing Hu): deprecate this class and rewrite world model with proper
// test coverage.
// This class also manages the seeding data for world model.
class SpeedWorldModel {
 public:
  SpeedWorldModel(
      const std::shared_ptr<const pnc_map::JointPncMapService>&
          joint_pnc_map_service,
      std::unique_ptr<const Snapshot> latest_snapshot_ptr,
      std::optional<prediction::pb::AgentList>&& agent_list,
      std::vector<TrackedObject>&& tracked_objects,
      std::vector<ConstructionZone>&& construction_zones,
      bool should_ignore_map_change_traffic_lights,
      std::unordered_map<ConstructionZoneId, const ConstructionZone*>&&
          construction_zone_ptr_map,
      tbb::concurrent_unordered_map<ObjectId,
                                    std::vector<PredictedTrajectoryWrapper>>&&
          object_prediction_map,
      tbb::concurrent_unordered_map<ObjectId,
                                    ConditionalPredictedTrajectoryWrapper>&&
          object_conditional_prediction_map,
      const RegionalMap& regional_map,
      const routing::pb::RouteQuery& replan_route_query,
      const std::tm* order_start_time_info,
      bool should_generate_pull_out_jump_out_sequence,
      bool is_high_agent_density,
      const std::vector<const pnc_map::Lane*>& last_selected_lane_sequences,
      std::optional<double> order_start_position_distance,
      bool should_planner_respond_mrc,
      bool is_ego_position_on_route_ready_for_pull_out,
      bool should_trigger_pull_out_by_mrc, bool has_pull_out_finished,
      const RobotState& robot_state,
      const TrafficSignalReasoner& traffic_signal_reasoner,
      const DrivableLaneReasoner& drivable_lane_reasoner,
      const std::shared_ptr<std::unordered_map<ObjectId, PlannerObject>>&
          planner_object_map_ptr,
      const PlannerObjectsHistory& planner_objects_history,
      const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos,
      const pull_over::PullOverRequestInfo& pull_over_request_info,
      // TODO(lixangxianghui): Try to enable && for std::move.
      const lane_selection::MapChangeAreaHandleParameter&
          map_change_area_handle_param,
      LaneBlockageDetector& lane_blockage_detector,
      LaneCongestionDetector& lane_congestion_detector,
      std::unique_ptr<planner::RouteModelSpeedData>& route_model_speed_data,
      CurrentLaneAssociator& current_lane_associator,
      CurrentLaneReasoner& current_lane_reasoner,
      WaypointAssistTracker& waypoint_assist_tracker,
      ImmediatePullOverStateManager& immediate_pull_over_state_manager,
      AssistDirectiveGenerator& assist_directive_generator,
      lane_selection::StuckAvoidanceReasoner& stuck_avoidance_reasoner,
      AssistTaskScheduler& assist_task_scheduler,
      lane_selection::ChangedLaneStructureReasoner&
          changed_lane_structure_reasoner);

  // Accessors.
  const voy::Pose& pose() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->pose_ptr;
  }

  int64_t snapshot_timestamp() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->pose_timestamp;
  }

  int64_t prediction_timestamp() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    DCHECK(latest_snapshot_ptr_->agent_list_ptr != nullptr);
    return latest_snapshot_ptr_->agent_list_ptr->timestamp();
  }

  // The return value is a tm structure which represents the corresponding
  // time, expressed for the local timezone. It means planner hasn't received
  // an order start time if return value is nullptr. User can fetch interested
  // time info from related field. e.g. The hour info can be fetched from
  // |order_start_time_info->tm_hour|.
  //
  // More details can be found:
  // /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
  const std::tm* order_start_time_info() const {
    return order_start_time_info_;
  }

  const voy::TrafficLights& traffic_light_detection() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->traffic_light_ptr;
  }

  const RegionalMap& regional_map() const { return regional_map_; }

  const pb::TrajectoryGuiderOutput* trajectory_guider_output() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->trajectory_guider_output_ptr.get();
  }

  const std::shared_ptr<const ::localization::LocalizationFault>&
  localization_fault() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->localization_fault_ptr;
  }

  const std::shared_ptr<const pb::ReplanRequest>& replan_request() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->replan_request_ptr;
  }

  const GlobalRouteSolution* global_route_solution() const {
    return route_model_speed_data_->global_route_solution();
  }

  routing::pb::ImmediatePullOverState immediate_pullover_state() const {
    return immediate_pull_over_state_manager_speed_data_
        .immediate_pullover_state();
  }

  routing::pb::SpecialPlannerResponseRequest special_planner_response_request()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    DCHECK(latest_snapshot_ptr_->route_status_ptr != nullptr);
    return latest_snapshot_ptr_->route_status_ptr
        ->special_planner_response_request();
  }

  bool IsImmediatePullOverTriggeredByMRC() const {
    return immediate_pull_over_state_manager_speed_data_
        .IsImmediatePullOverTriggeredByMRC();
  }

  bool ShouldTriggerImmediateRightLaneChangeForImmediatePullover() const {
    return immediate_pull_over_state_manager_speed_data_
        .ShouldTriggerImmediateRightLaneChangeForImmediatePullover();
  }

  const RouteModelSpeedData& route_model() const {
    return *route_model_speed_data_;
  }

  const routing::pb::RouteQuery& replan_route_query() const {
    return replan_route_query_;
  }

  // DO NOT use this function anymore. Use planner_object_map() and
  // object_prediction_map() instead.
  const std::vector<TrackedObject>& tracked_objects() const {
    return tracked_objects_;
  }

  const prediction::pb::AgentList& agent_list() const {
    if (agent_list_.has_value()) {
      return agent_list_.value();
    }
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *(latest_snapshot_ptr_->agent_list_ptr);
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map()
      const {
    return *planner_object_map_ptr_;
  }

  const PlannerObjectsHistory& planner_objects_history() const {
    return planner_objects_history_;
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
  object_prediction_map() const {
    return object_prediction_map_;
  }

  const tbb::concurrent_unordered_map<ObjectId,
                                      ConditionalPredictedTrajectoryWrapper>&
  object_conditional_prediction_map() const {
    return object_conditional_prediction_map_;
  }

  const av_comm::HistoryBuffer<int64_t, math::geometry::Point2d>& ego_history()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->ego_history_ptr;
  }

  const std::map<int64_t,
                 av_comm::HistoryBuffer<int64_t, math::geometry::Point2d>>&
  agent_history() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->agent_history_ptr;
  }

  const av_comm::HistoryBuffer<int64_t, SnapshotData<const ::pb::Fault>>*
  health_faults_history() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->health_faults_ptr;
  }

  std::shared_ptr<const voy::SemanticMapElements> semantic_map_elements()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->semantic_map_elements_ptr;
  }

  // NOTE(Tingran): The construction zones that planner use are the ones with
  // human labeling, not the ones generately solely by perception algorithms.
  const std::vector<ConstructionZone>& construction_zones() const {
    return construction_zones_;
  }

  // Tests if ego should ignored traffic lights about the map change area.
  bool should_ignore_map_change_traffic_lights() const {
    return should_ignore_map_change_traffic_lights_;
  }

  const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
  construction_zone_ptr_map() const {
    return construction_zone_ptr_map_;
  }

  const RobotState& robot_state() const { return robot_state_; }

  const TrafficSignalReasoner& traffic_signal_reasoner() const {
    return traffic_signal_reasoner_;
  }

  const DrivableLaneReasoner& drivable_lane_reasoner() const {
    return drivable_lane_reasoner_;
  }

  DrivableLaneReasoner* mutable_drivable_lane_reasoner() {
    return &drivable_lane_reasoner_;
  }

  const OcclusionChecker& occlusion_checker() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->occlusion_checker;
  }

  const LaneBlockageDetectorSpeedData& lane_blockage_detector() const {
    return lane_blockage_detector_speed_data_;
  }

  const LaneCongestionDetectorSpeedData& lane_congestion_detector() const {
    return lane_congestion_detector_speed_data_;
  }

  const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos() const {
    return pickup_dropoff_zone_infos_;
  }

  const std::vector<pb::AssistResponse>& assist_responses() const {
    return assist_directive_generator_speed_data_.assist_responses();
  }

  const pnc_map::PncMapService* pnc_map_service() const {
    return joint_pnc_map_service_->pnc_map_service();
  }

  const std::shared_ptr<const pnc_map::JointPncMapService>&
  joint_pnc_map_service() const {
    return joint_pnc_map_service_;
  }

  const CurrentLanesSpeedData& current_lanes_speed_data() const {
    return current_lanes_speed_data_;
  }

  const AssistDirectiveGeneratorSpeedData& assist_directive_generator() const {
    return assist_directive_generator_speed_data_;
  }

  const AssistAvailabilityAnalyzer& assist_availability_analyzer() const {
    return assist_availability_analyzer_;
  }

  const AssistTaskSchedulerSpeedData& assist_task_scheduler() const {
    return assist_task_scheduler_speed_data_;
  }

  // Returns the match type which indicates the route destination is a pick-up
  // destination or a drop-off destination.
  order::pb::MatchType match_type() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->route_status_ptr == nullptr
               ? order::pb::MatchType::UNKNOWN_MATCH
               : latest_snapshot_ptr_->route_status_ptr->ride_route_query()
                     .match_type();
  }

  const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->mrc_request_ptr;
  }

  const std::unique_ptr<const Snapshot>& latest_snapshot_ptr() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_;
  }

  const WaypointAssistTrackerSpeedData& waypoint_assist_tracker() const {
    return waypoint_assist_tracker_speed_data_;
  }

  const pull_over::PullOverRequestInfoSpeedData&
  pull_over_request_info_speed_data() const {
    return pull_over_request_info_speed_data_;
  }

  const ImmediatePullOverStateManagerSpeedData&
  immediate_pull_over_state_manager() const {
    return immediate_pull_over_state_manager_speed_data_;
  }

  const std::optional<math::geometry::Point2d>& immediate_pull_over_point()
      const {
    return immediate_pull_over_state_manager_speed_data_
        .immediate_pull_over_point();
  }

  int64_t immediate_pullover_triggered_timestamp() const {
    return immediate_pull_over_state_manager_speed_data_
        .immediate_pullover_triggered_timestamp();
  }

  const std::optional<LightRegionalPath>& redirecting_regional_path() const {
    return immediate_pull_over_state_manager_speed_data_
        .redirecting_regional_path();
  }

  bool ShouldResponseToTriggeredImmediatePullOver() const {
    return immediate_pull_over_state_manager_speed_data_
        .ShouldResponseToTriggeredImmediatePullOver();
  }

  bool should_generate_pull_out_jump_out_sequence() const {
    return should_generate_pull_out_jump_out_sequence_;
  }

  const std::optional<int64_t>& pull_over_mission_id() const {
    return pull_over_mission_id_;
  }

  // Checks whether too many agents in current scenario.
  bool IsHighAgentDensityScenario() const { return is_high_agent_density_; }

  const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence() const {
    return last_selected_lane_sequences_;
  }

  // The distance ego travels from start position is less than
  // |kMaxPullOutDistanceFromEgoStartPositionInMeter| after receiving an
  // order, we consider ego is near the order start position. And the start
  // position should be close to a PDZ.
  const std::optional<double>& OrderStartPositionDistance() const {
    return order_start_position_distance_;
  }

  // Returns true if ego's position is ready for pull out.
  bool IsEgoPositionOnRouteReadyForPullOut() const {
    return is_ego_position_on_route_ready_for_pull_out_;
  }

  const std::vector<const pnc_map::Lane*>& GetLastSelectedLaneSequence() const {
    return last_selected_lane_sequences_;
  }

  // Returns true if planner should respond mrc.
  bool should_planner_respond_mrc() const {
    return should_planner_respond_mrc_;
  }

  // Returns true if the pull out should be triggered after MRC has finished.
  bool ShouldTriggerPullOutByMrc() const {
    return should_trigger_pull_out_by_mrc_;
  }

  // The pull out is thought finished if the current pull out has been
  // confirmed and executed in lane follow for more than the pull out
  // termination distance threshold.
  bool HasPullOutFinished() const { return has_pull_out_finished_; }

  std::shared_ptr<const voy::TripComment> trip_comment() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->trip_comment_ptr;
  }

  // Accessor for current lane in assist stuck detect.
  const pnc_map::Lane* GetCurrentLaneForAssist() const {
    if (!current_lanes_speed_data_.drivable_physical_nearlane_candidates()
             .empty()) {
      return current_lanes_speed_data_
          .drivable_physical_nearlane_candidates()[0]
          .lane();
    }
    return nullptr;
  }

  // Returns true if remote assist request should be sent in case of stuck,
  // considering MRC (minimal risk condition) status.
  bool ShouldRequestRaUnstuckGivenMrcState() const;

  void UpdateLightAssistAvailability(
      const BehaviorDecision& behavior_decision, const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
      const CurrentLanesSpeedData& current_lanes_speed_data,
      const AssistDirectiveGeneratorSpeedData& assist_directive_generator,
      const pb::LightAssistSeed& light_assist_seed,
      pb::PlanningDebug* planning_debug);

  const lane_selection::MapChangeAreaHandleParameter&
  map_change_area_handle_param() const {
    return map_change_area_handle_param_;
  }

  const lane_selection::StuckAvoidanceReasonerSpeedData&
  stuck_avoidance_reasoner_speed_data() const {
    return stuck_avoidance_reasoner_speed_data_;
  }

  const lane_selection::ChangedLaneStructureReasonerSpeedData&
  changed_lane_structure_reasoner_speed_data() const {
    return changed_lane_structure_reasoner_speed_data_;
  }

 private:
  // The joint_pnc_map_service provides access to both pnc online and offline
  // map elements, where online elements indicates the changed elements in a
  // dynamic pnc map.
  const std::shared_ptr<const pnc_map::JointPncMapService>
      joint_pnc_map_service_;

  // Latest snapshot of input messages.
  std::unique_ptr<const Snapshot> latest_snapshot_ptr_;

  // Agent list for filter or ignore purposes.
  std::optional<prediction::pb::AgentList> agent_list_ = std::nullopt;

  // A vector of tracked objects.
  std::vector<TrackedObject> tracked_objects_;

  // A vector of construction zones.
  std::vector<ConstructionZone> construction_zones_;

  // Ignored construction zones about traffic light of map change area.
  bool should_ignore_map_change_traffic_lights_ = false;

  // A map of the Construction Zone.
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      construction_zone_ptr_map_;

  // A map from the Object ID to the object's all predicted trajectories for
  // decoupled planner.
  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map_;

  tbb::concurrent_unordered_map<ObjectId, ConditionalPredictedTrajectoryWrapper>
      object_conditional_prediction_map_;

  // Member variable to store the start position distance
  std::optional<double> order_start_position_distance_;

  // A struct of regional path and map information.
  // Data used across cycles, which has been stored in state.
  RegionalMap regional_map_;

  // Route query updated each cycle to issue for global routing.
  // It is constructed from current drivable lanes.
  // Query timestamp 0 means no route query available.
  routing::pb::RouteQuery replan_route_query_;

  const std::tm* order_start_time_info_ = nullptr;

  // True if we should generate pull out jump out sequence.
  bool should_generate_pull_out_jump_out_sequence_ = false;

  // True if ego is at high agent density scenario.
  bool is_high_agent_density_ = false;

  // The variable about last selected lane sequence information.
  std::vector<const pnc_map::Lane*> last_selected_lane_sequences_;

  bool should_planner_respond_mrc_ = false;

  bool is_ego_position_on_route_ready_for_pull_out_;

  bool should_trigger_pull_out_by_mrc_ = false;

  bool has_pull_out_finished_;

  // Robot state.
  RobotState robot_state_;

  // It collects traffic signal related info provided by perception.
  TrafficSignalReasoner traffic_signal_reasoner_;

  // It collects these lanes whose attribute of drivable has been changed.
  // For example we set some un-drivable lanes as drivable and set some
  // drivable lanes as un-drivable.
  DrivableLaneReasoner drivable_lane_reasoner_;

  // A map of PlannerObject for decoupled planner.
  std::shared_ptr<std::unordered_map<ObjectId, PlannerObject>>
      planner_object_map_ptr_;

  // History of planner_object_map
  PlannerObjectsHistory planner_objects_history_;

  // The PickupDropoffZone collected from the current route.
  std::vector<PickupDropoffZoneInfo> pickup_dropoff_zone_infos_;

  pull_over::PullOverRequestInfoSpeedData pull_over_request_info_speed_data_;

  // Lane blockage detector.
  LaneBlockageDetectorSpeedData lane_blockage_detector_speed_data_;

  // Lane congestion detector.
  LaneCongestionDetectorSpeedData lane_congestion_detector_speed_data_;

  std::unique_ptr<planner::RouteModelSpeedData> route_model_speed_data_;

  // Determines current lane for different users. e.g. lane_blockage,
  // regional_map, etc.
  CurrentLanesSpeedData current_lanes_speed_data_;

  std::optional<int64_t> pull_over_mission_id_ = std::nullopt;

  // Tracks waypoint assist state.
  // TODO(sixian): move this to global state manager.
  WaypointAssistTrackerSpeedData waypoint_assist_tracker_speed_data_;

  // The state manager of immediate pull over.
  ImmediatePullOverStateManagerSpeedData
      immediate_pull_over_state_manager_speed_data_;

  // It handles the assist command from tele ops and generates assist directives
  // to help ego unstuck.
  AssistDirectiveGeneratorSpeedData assist_directive_generator_speed_data_;

  AssistTaskSchedulerSpeedData assist_task_scheduler_speed_data_;

  // The state manager of assist availability.
  AssistAvailabilityAnalyzer assist_availability_analyzer_;

  // It collects map change area related info.
  lane_selection::MapChangeAreaHandleParameter map_change_area_handle_param_;

  // Stuck avoidance reasoner.
  lane_selection::StuckAvoidanceReasonerSpeedData
      stuck_avoidance_reasoner_speed_data_;

  // It collects the changed lane structure.
  lane_selection::ChangedLaneStructureReasonerSpeedData
      changed_lane_structure_reasoner_speed_data_;

  friend speed::ReasoningTestFixture;
  friend LaneChangeTestFixture;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_SPEED_WORLD_MODEL_H_
