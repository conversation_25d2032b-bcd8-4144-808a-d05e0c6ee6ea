#include "onboard_monitor.h"

#include <cstring>
#include <fcntl.h>
#include <icmp_measure.h>
#include <sys/socket.h>
#include <sys/un.h>
#include <sys/utsname.h>
#include <thread>  // NOLINT
#include <unistd.h>
#include <utility>

#include "av_comm/onboard_config.h"
#ifndef U22_ARM64
#include "mqtt_client.h"
#include "mqtt_handler.h"
#else
#include "pcie_handler.h"
#endif
#include "node/fault_reporter.h"
#include "node/timer.h"
#include "shell_util.h"
#include "timer_manager.h"
#include "timesync.h"

namespace onboard_monitor {
constexpr float kThresholdMs = 4;
namespace {
const std::map<onboard_config::AcuType, std::string> kHostInterfaceMap = {
    {onboard_config::AcuType::GEN4X86, "enp66s0f0"},
    {onboard_config::AcuType::GEN4ORIN1, "eth1"},
    {onboard_config::AcuType::GEN4ORIN2, "eth1"}};
}  // namespace

OnboardMonitor::OnboardMonitor()
    : socket_fd_(-1),
      stop_thread_(false),
      fault_reporter_(node::FaultReporter("onboard_monitor_node")) {
  InitCarId();
  InitCarSn();
}

OnboardMonitor::~OnboardMonitor() {
  stop_thread_ = true;
  if (socket_thread_.joinable()) {
    socket_thread_.join();
  }
#ifndef U22_ARM64
  if (mqtt_thread_.joinable()) {
    mqtt_thread_.join();
  }
#endif
  if (timesync_thread_.joinable()) {
    timesync_thread_.join();
  }
  if (message_handle_thread_.joinable()) {
    message_handle_thread_.join();
  }
  if (socket_fd_ != -1) {
    close(socket_fd_);
  }
}

void OnboardMonitor::Init() {
  hostname_ = GetHostName();
  LOG(INFO) << "Init on host:" << hostname_;
  if (hostname_ == kGen3Cpubox1 || hostname_ == kGen4Cpubox) {
    hardware_status_record_publisher_ = std::make_shared<Publisher>(
        CreatePublisher<monitor::HardwareMonitorData>(
            av_comm::topic::kHardwareMonitorRecord));
    ParseConfig(kMonitorConfigPath);
    std::string socket_path = config_json_["socket_path"];
    LOG(INFO) << "Config socket path:" << socket_path;
    SetupDomainSocket(socket_path);
  }

  if (!av_comm::IsGen3Cpubox1() && !av_comm::IsGen4X86()) {
    hardware_monitor_publisher_ = std::make_shared<Publisher>(
        CreatePublisher<monitor::HardwareMonitorData>(
            av_comm::topic::kHardwareMonitor));
  }
}

void OnboardMonitor::ParseConfig(const std::string& config_path) {
  std::ifstream config_input(config_path);
  config_json_ = nlohmann::json::parse(config_input);
  debug_mode_ = config_json_["debug_mode"];
  LOG(INFO) << "Debug mode:" << static_cast<int>(debug_mode_);
}

void OnboardMonitor::SetupDomainSocket(std::string& path) {
  if (!WaitForSocket(path)) {
    LOG(INFO) << "Timeout waiting for socket at " << path;
  }

  // 创建 Unix 域套接字
  socket_fd_ = socket(AF_UNIX, SOCK_STREAM, 0);
  if (socket_fd_ < 0) {
    LOG(INFO) << "Error creating socket";
  }

  struct sockaddr_un addr {};
  addr.sun_family = AF_UNIX;
  std::strncpy(addr.sun_path, path.c_str(), sizeof(addr.sun_path) - 1);

  // 连接到 socket
  if (connect(socket_fd_, reinterpret_cast<struct sockaddr*>(&addr),
              sizeof(addr)) == -1) {
    LOG(INFO) << "Connection refused at " << path;
    close(socket_fd_);
    socket_fd_ = -1;  // 避免使用无效的 fd
    return;
  }
  LOG(INFO) << "Connected to socket at " << path << ":" << socket_fd_;
}

bool OnboardMonitor::WaitForSocket(std::string& sockPath, int timeout) {
  auto start_time = std::chrono::steady_clock::now();
  while (std::chrono::steady_clock::now() - start_time <
         std::chrono::seconds(timeout)) {
    if (access(sockPath.c_str(), F_OK) == 0) {
      return true;
    }

    LOG(INFO) << "Waiting for socket at " << sockPath << "...";
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
  return false;
}

void OnboardMonitor::SubscribeMessage(const std::string& topic_name) {
  hardware_monitor_subscriber_ = std::make_shared<Subscriber>(
      CreateSubscriber<monitor::HardwareMonitorData>(
          topic_name, -1,
          [this](MonitorDataPtr msg_ptr) {
            MessageCallback(std::move(msg_ptr));
          },
          GetTypeName<monitor::HardwareMonitorData>(), GetTransportType(),
          ProcessorType::kNoExtraThread));
}

void OnboardMonitor::MessageCallback(const MonitorDataPtr hardware_status_ptr) {
  LOG(INFO) << "Push hardware monitor msg into queue";
  msg_queue_.enqueue(hardware_status_ptr);
}

void OnboardMonitor::HandleMessage() {
  google::protobuf::util::JsonPrintOptions options;
  options.always_print_primitive_fields = true;
  options.add_whitespace = false;
  options.preserve_proto_field_names = true;

  while (!stop_thread_) {
    auto hardware_status_msg_ptr = msg_queue_.dequeue();
    if (!hardware_status_msg_ptr) {
      continue;
    }

    ProcessHardwareStatusMsg(hardware_status_msg_ptr);

    std::string json_msg;
    if (!proto_util::WriteJsonProtoString(*hardware_status_msg_ptr, options,
                                          &json_msg)) {
      LOG(ERROR) << "Failed to write json proto string.";
      continue;
    }

    try {
      nlohmann::json json_obj = nlohmann::json::parse(json_msg);
      if (json_obj.contains("hardware_status")) {
        if (json_obj["hardware_status"].empty()) {
          LOG(INFO) << "hardware_status_json is empty, continue";
          continue;
        }
        std::string hardware_status_json =
            json_obj["hardware_status"].dump();  // 提取 `hardware_status` 部分
        LOG(INFO) << "Get Json string:" << hardware_status_json;
        SendJsonData(hardware_status_json);
      } else {
        LOG(ERROR) << "hardware_status field not found in JSON.";
      }
    } catch (const std::exception& e) {
      LOG(ERROR) << "JSON parsing error: " << e.what();
    }
  }
}

void OnboardMonitor::ProcessHardwareStatusMsg(
    std::shared_ptr<const monitor::HardwareMonitorData> hardware_status_ptr) {
  if (hardware_status_ptr->disk_sync_flag()) {
    auto mutable_ptr = std::const_pointer_cast<monitor::HardwareMonitorData>(
        hardware_status_ptr);
    mutable_ptr->set_car_id(GetCarId());
    mutable_ptr->set_car_sn(GetCarSn());
    hardware_status_record_publisher_->Publish(*mutable_ptr);
    LOG(INFO) << "Publish hardware monitor record data";
  }
}

void OnboardMonitor::AddMonitorTask() {
  // Add LDPC Monitor Task
  if (hostname_ == kGen3Cpubox1 || hostname_ == kGen3Cpubox2) {
    AddLDPCMonitorTask();
  }
  // Add Other Monitor Task
}

void OnboardMonitor::SendJsonData(const std::string& json_data) {
  if (socket_fd_ == -1) {
    LOG(INFO) << "Socket is not connected, dropping data." << std::endl;
    return;
  }

  if (send(socket_fd_, json_data.c_str(), json_data.size(), 0) == -1) {
    LOG(INFO) << "Error sending data, closing socket." << std::endl;
    close(socket_fd_);
    socket_fd_ = -1;
    return;
  }

  send(socket_fd_, "\n", 1, 0);  // 发送换行符作为消息分隔符
}

std::string OnboardMonitor::IcmpMeasure(const std::string& target_ip,
                                        bool upload_metrics, bool with_fault) {
  ICMPTimestampRequest icmp_request;
  int r_originate = 0, r_receive = 0, r_transmit = 0, recv_timestamp = 0;
  if (icmp_request.Init() != 0) {
    LOG(ERROR) << "ICMPTimestampRequest Init failed.";
    return "";
  }
  if (icmp_request.SendIcmp(target_ip) != 0) {
    LOG(ERROR) << "SendIcmp failed.";
    return "";
  }
  if (icmp_request.ReceiveIcmp(target_ip, &r_originate, &r_receive, &r_transmit,
                               &recv_timestamp) != 0) {
    LOG(ERROR) << "ReceiveIcmp failed.";
    return "";
  }
  const double offset =
      ((r_receive - r_originate) - (recv_timestamp - r_transmit)) / 2.0;
  const double rtt = recv_timestamp - r_originate;
  if (upload_metrics) {
    std::shared_ptr<monitor::HardwareMonitorData> monitor_data =
        std::make_shared<monitor::HardwareMonitorData>();
    AddHardwareMonitorData(*monitor_data,
                           "timesync_" + target_ip + "_" +
                               av_comm::GetIpFromAcuType(av_comm::GetAcuType()),
                           "offset", offset);
    AddHardwareMonitorData(*monitor_data,
                           "timesync_" + target_ip + "_" +
                               av_comm::GetIpFromAcuType(av_comm::GetAcuType()),
                           "rtt", rtt);
    LOG(INFO) << target_ip << " " << r_originate << " " << r_receive << " "
              << r_transmit << " " << recv_timestamp;
    LOG(INFO) << target_ip << " offset " << offset;
    LOG(INFO) << target_ip << " rtt " << rtt;
    PushMonitorData(monitor_data);
  }
  if (with_fault && rtt <= 1 && std::abs(offset) >= kThresholdMs) {
    return strings::StringPrintf(
        "Diff abs(%.1lf) ms between %s and host %s is greater than %.1f; ",
        offset, GetHostName().c_str(), target_ip.c_str(), kThresholdMs);
  }
  return "";
}
void OnboardMonitor::MeasureTimesyncMetrics() {
  while (true) {
    const auto ts_now_s = base::WallClockNowS();
    const bool upload_metrics = ts_now_s % 10 == 0;
    std::string fault_msg;

    if (av_comm::GetAcuType() == onboard_config::AcuType::GEN4ORIN1) {
      fault_msg += IcmpMeasure("***********23", upload_metrics, false);
      fault_msg += IcmpMeasure("************", upload_metrics, false);
    }
    if (av_comm::GetAcuType() == onboard_config::AcuType::GEN4X86) {
      fault_msg += IcmpMeasure("**************", upload_metrics, true);
    } else {
      fault_msg += IcmpMeasure("***********00", upload_metrics, true);
    }
    fault_msg += IcmpMeasure("***********", upload_metrics, true);

    if (!fault_msg.empty()) {
      fault_reporter_.AddFault(
          pb::OnboardMonitorFaultCode::TIME_NOT_SYNC_WITH_TNU, fault_msg);
      LOG(ERROR) << "Fault: " << fault_msg;
    } else {
      fault_reporter_.RemoveFault(
          pb::OnboardMonitorFaultCode::TIME_NOT_SYNC_WITH_TNU);
    }
    std::shared_ptr<monitor::HardwareMonitorData> monitor_data_single =
        std::make_shared<monitor::HardwareMonitorData>();
    if (phc_->Valid() && av_comm::IsGen4X86()) {
      struct timespec ts {};
      if (phc_->Now(ts)) {
        int64_t hw_ts_ms = ts.tv_sec * 1000 + ts.tv_nsec / 1000000;
        LOG(INFO) << phc_->GetInterfaceName() << " PHC time " << hw_ts_ms;
        AddHardwareMonitorData(*monitor_data_single,
                               "ts_" + phc_->GetInterfaceName(), "interface_ts",
                               hw_ts_ms);
        PushMonitorData(monitor_data_single);
      }
    }
    std::this_thread::sleep_for(std::chrono::seconds(1));
  }
}
void OnboardMonitor::Run(boost::asio::io_service* io_service) {
  AddMonitorTask();

  if (av_comm::IsGen4Platform()) {
#ifndef U22_ARM64
    mqtt_thread_ = std::thread([this]() {
      mqtt::S32gMqttClient(&(this->msg_queue_), mqtt::on_message);
    });
#endif
    phc_ =
        std::make_unique<PhcClock>(kHostInterfaceMap.at(av_comm::GetAcuType()));
    if (!phc_->Init()) {
      LOG(ERROR) << "Failed to initialize PhcClock.";
    }
    timesync_thread_ =
        std::thread(&OnboardMonitor::MeasureTimesyncMetrics, this);
  }

  if (hostname_ == kGen3Cpubox1 || hostname_ == kGen4Cpubox) {
    SubscribeMessage(av_comm::topic::kHardwareMonitor);
    message_handle_thread_ = std::thread(&OnboardMonitor::HandleMessage, this);
  }
  if (!io_service) {
    return;
  }

#ifdef U22_ARM64
  if (av_comm::IsGen4Orin2()) {
    boost::asio::spawn(
        *io_service, [this, io_service](boost::asio::yield_context yield) {
          node::Timer timer(*io_service);
          boost::system::error_code error_code;
          orin_pcie::PcieHandler handler;
          handler.Init();
          while (!error_code) {
            timer.expires_from_now(std::chrono::seconds(10), error_code);
            monitor::HardwareMonitorData monitor_data_single;
            handler.GetMonitorData(monitor_data_single);
            if (monitor_data_single.hardware_status_size() > 0) {
              this->hardware_monitor_publisher_->Publish(monitor_data_single);
            }
            timer.async_wait(yield[error_code]);
          }
        });
  }
#endif
}

std::string OnboardMonitor::GetHostName() {
  struct utsname buf {};
  if (uname(&buf) < 0) {
    return "";
  }
  return buf.nodename;
}

void OnboardMonitor::AddLDPCMonitorTask() {
  TimerManager::GetInstance().Add(std::chrono::seconds(10), [this]() {
    std::string ldpc_error_cnt{"0"};
    ExecShell(kGetLDPCErrosCmd, ldpc_error_cnt);
    LOG(INFO) << "Get LDPC Error cnt:" << ldpc_error_cnt;

    std::shared_ptr<monitor::HardwareMonitorData> monitor_data_single =
        std::make_shared<monitor::HardwareMonitorData>();
    AddHardwareMonitorData(*monitor_data_single, "ldpc", "error",
                           std::stod(ldpc_error_cnt));
    PushMonitorData(monitor_data_single);
  });
}

void OnboardMonitor::InitCarId() {
  car_id_ = av_comm::CarId::Get().str();
  LOG(INFO) << "Get car_id:" << car_id_;
}

void OnboardMonitor::InitCarSn() { LOG(INFO) << "Get car_sn:" << car_sn_; }

std::string OnboardMonitor::GetCarId() { return car_id_; }
std::string OnboardMonitor::GetCarSn() { return car_sn_; }

void OnboardMonitor::PushMonitorData(
    const std::shared_ptr<monitor::HardwareMonitorData>& monitor_data_single) {
  if (av_comm::IsGen3Cpubox2() || av_comm::IsGen4Orin1() ||
      av_comm::IsGen4Orin2()) {
    hardware_monitor_publisher_->Publish(*monitor_data_single);
  } else {
    msg_queue_.enqueue(monitor_data_single);
  }
}
}  // namespace onboard_monitor
