#include "pnc_map_service/pnc_map_service.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <queue>
#include <set>
#include <string>
#include <tuple>
#include <unordered_map>
#include <vector>

#include <glog/logging.h>
#include <tbb/parallel_for_each.h>

#include "av_comm/mode_config.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "gtl/map_util.h"
#include "hdmap/lib/hdmap_util.h"
#include "hdmap/lib/point_util.h"
#include "hdmap_protos/on_road_status.pb.h"
#include "hdmap_protos/pickup_dropoff_zone.pb.h"
#include "planner/planning_gflags.h"
#include "pnc_map_service/map_elements/lift_rod.h"
#include "pnc_map_service/util/crosswalk_utility.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "routing/utility/routing_param_util.h"

namespace pnc_map {
namespace {

// The search range for road geo info around the robot.
[[maybe_unused]] constexpr double kGeoInfoSearchingRange = 100.0;  // m
// The Search range around left lane boundary to get opposite road.
constexpr double kSearchRadiusForGetOppositeRoad = 3.0;  // m
// Use box to represent object polygon, and we only care about the center point
// and the four corners.
constexpr int kNumberOfPolygonPoints = 5;
// The minimum angle difference of the middle point of two roads to be
// considered as opposite roads.
constexpr double kMinAngleDiffForOppositeRoadInRad = M_PI * 0.75;
// The search range for near junctions or zones of the query position point.
constexpr double kMapElementSearchingRadiusInMeter = 10.0;

// Struct RoadVertex stores a road, its succeeding junction and the distance
// from road start to its succeeding junction.
struct RoadVertex {
  explicit RoadVertex(const Road* road_ptr) : road(road_ptr) {}

  RoadVertex(const Road* road_ptr, const Junction* junction_ptr,
             double distance_m)
      : road(road_ptr),
        iteration_nearest_junction(junction_ptr),
        road_start_to_junction_dist_m(distance_m) {}

  const Road* road = nullptr;
  // The nearest next junction at the algorithm's current iteration.
  const Junction* iteration_nearest_junction = nullptr;
  // The distance from |road| start to |iteration_nearest_junction|, in meter.
  double road_start_to_junction_dist_m = std::numeric_limits<double>::max();
  bool closed = false;
  bool visited = false;
};

struct RoadVertexComparator {
  bool operator()(const RoadVertex& lhs, const RoadVertex& rhs) const {
    return lhs.road_start_to_junction_dist_m >
           rhs.road_start_to_junction_dist_m;
  }
};

// Gets hdmap points from a robot's bounding box and z-pose.
//   - points[0] is the center of the bounding box.
//   - points[1..4] are the corners of the bounding box.
[[maybe_unused]] std::vector<hdmap::Point> GetRobotPoints(
    const math::geometry::OrientedBox2d& current_pose_bb, double current_pose_z,
    const std::string& utm_zone) {
  std::vector<hdmap::Point> robot_points;
  robot_points.reserve(kNumberOfPolygonPoints);

  // Insert the center point.
  hdmap::Point center_point;
  center_point.set_x(current_pose_bb.center().x());
  center_point.set_y(current_pose_bb.center().y());
  center_point.set_z(current_pose_z);
  center_point.set_utm_zone(utm_zone);
  robot_points.emplace_back(std::move(center_point));

  // Insert the four corner points.
  for (const auto& corner : current_pose_bb.Corners()) {
    hdmap::Point corner_point;
    corner_point.set_x(corner.x);
    corner_point.set_y(corner.y);
    corner_point.set_z(current_pose_z);
    corner_point.set_utm_zone(utm_zone);
    robot_points.emplace_back(std::move(corner_point));
  }
  return robot_points;
}

// Initializes the |pq| and |road_vertex_map| which contains all junction roads'
// predecessor roads.
void InitializePriorityQueueWithJunctionRoadPredecessors(
    const std::map<int64_t, std::shared_ptr<Road>>& road_map,
    std::priority_queue<RoadVertex, std::vector<RoadVertex>,
                        RoadVertexComparator>* pq,
    std::unordered_map<int64_t, RoadVertex>* road_vertex_map) {
  for (const auto& [_, road] : road_map) {
    road_vertex_map->emplace(road->id(), road.get());
  }

  for (const auto& [_, road] : road_map) {
    if (!road->IsInJunction()) {
      continue;
    }
    for (const auto* predecessor_road : road->predecessors()) {
      RoadVertex& road_vertex = road_vertex_map->at(predecessor_road->id());
      if (road_vertex.visited) {
        continue;
      }
      road_vertex.visited = true;
      road_vertex.road_start_to_junction_dist_m =
          predecessor_road->reference_line().GetTotalArcLength();
      pq->emplace(predecessor_road, road->junction(),
                  road_vertex.road_start_to_junction_dist_m);
    }
  }
}

// Sets the road to nearest succeeding junction relation.
void SetRoadsToNearestSucceedingJunction(
    std::map<int64_t, std::shared_ptr<Road>>* road_map_ptr) {
  std::priority_queue<RoadVertex, std::vector<RoadVertex>, RoadVertexComparator>
      pq;
  std::unordered_map<int64_t, RoadVertex> road_vertex_map;
  InitializePriorityQueueWithJunctionRoadPredecessors(*road_map_ptr, &pq,
                                                      &road_vertex_map);
  while (!pq.empty()) {
    const RoadVertex least_priority_vertex = std::move(pq.top());
    pq.pop();

    RoadVertex& cur_road_vertex =
        road_vertex_map.at(least_priority_vertex.road->id());
    if (cur_road_vertex.closed) {
      continue;
    }
    cur_road_vertex.closed = true;

    // Use the non-const raw Road* for modification.
    Road* cur_road_ptr =
        road_map_ptr->at(least_priority_vertex.road->id()).get();
    cur_road_ptr->set_nearest_junction(
        least_priority_vertex.iteration_nearest_junction);
    cur_road_ptr->set_road_start_to_nearest_junction_dist_m(
        least_priority_vertex.road_start_to_junction_dist_m);

    for (const auto* predecessor_road :
         least_priority_vertex.road->predecessors()) {
      RoadVertex& predecessor_road_vertex =
          road_vertex_map.at(predecessor_road->id());
      if (predecessor_road_vertex.closed) {
        continue;
      }
      const double alternative_distance_m =
          least_priority_vertex.road_start_to_junction_dist_m +
          predecessor_road->reference_line().GetTotalArcLength();

      if (!predecessor_road_vertex.visited ||
          alternative_distance_m <
              predecessor_road_vertex.road_start_to_junction_dist_m) {
        predecessor_road_vertex.road_start_to_junction_dist_m =
            alternative_distance_m;
        predecessor_road_vertex.visited = true;
        pq.emplace(predecessor_road,
                   least_priority_vertex.iteration_nearest_junction,
                   alternative_distance_m);
      }
    }
  }
}
}  // namespace

void PncMapService::Init(std::shared_ptr<const hdmap::HdMap> hdmap,
                         bool generate_effective_hb) {
  generate_effective_hb_ = generate_effective_hb;
  Clear();

  hdmap_ = std::move(hdmap);
  prior_layer_ = hdmap_->GetPriorLayer();
  map_region_ = hdmap_->config().name();
  LOG(INFO) << "loading map for " << map_region_;
  LOG(INFO) << "hdmap geometry version: "
            << hdmap_->GetHdMapVersion(hdmap::MapLayer::GEOMETRY_LAYER);
  zone_lane_occupancy_map_ = hdmap::ZoneLaneOccupancyMap(hdmap_.get());
  zone_lane_occupancy_map_.AddZonesFromHdMap();
  const routing::pb::RoutingParam routing_param =
      routing::utility::GetRoutingParamPerVehicleConfig(map_region());
  tbb::parallel_for(0, 2, [this, &routing_param](int idx) {
    switch (idx) {
      case 0:
        road_feature_map_ = routing::utils::
            GetAllRoadFeaturesAndAddSolidLaneMarkingIdsFromRoads(*hdmap_);
        break;
      case 1:
        precalculated_lane_feature_map_ =
            routing::utils::GetPrecalculatedLaneFeatures(
                *hdmap_, routing_param.vehicle_width_m());
        break;
      default:
        // Handle invalid idx
        break;
    }
  });
  // Step 1: Create Roads (and child objects) in whole map.
  for (const auto& road_proto_pair : *(hdmap_->GetAllRoads())) {
    const hdmap::Road& road_proto = *road_proto_pair.second;
    if (!gtl::ContainsKey(offline_elements_cache_.road_map, road_proto.id())) {
      CreateRoad(road_proto);
    }
  }

  // After the above steps, all Roads and its inner Sections, Lanes and
  // LaneMarkings are created.
  // Create other PNC objects and link relations for each Road.
  for (const auto& kv : offline_elements_cache_.road_map) {
    // Step 2. Create Junction, TrafficSigns and TrafficSignals in each Roads.
    Road* road = kv.second.get();
    const hdmap::Road& road_proto = road->proto();

    // Create parent Junction if any.
    const int64_t junction_id = road_proto.junction_id();
    if (junction_id > 0 &&
        !gtl::ContainsKey(offline_elements_cache_.junction_map, junction_id)) {
      const hdmap::Junction* junction_proto =
          hdmap_->GetJunctionPtrById(junction_id);
      DCHECK(junction_proto != nullptr);
      CreateJunction(*junction_proto);
    }
    // Create TrafficSigns.
    for (const auto& sign_proto : road_proto.signs()) {
      if (!gtl::ContainsKey(offline_elements_cache_.traffic_sign_map,
                            sign_proto.id())) {
        CreateTrafficSign(sign_proto);
      }
    }
    // Create TrafficSignals.
    for (const auto& signal_proto : road_proto.traffic_signals()) {
      if (!gtl::ContainsKey(offline_elements_cache_.traffic_signal_map,
                            signal_proto.id())) {
        CreateTrafficSignal(signal_proto);
      }
    }
    // Create inner Crosswalks. Link stop lines of the Crosswalk with lanes only
    // when lanes have all initialized.
    std::vector<const Crosswalk*> crosswalks;
    crosswalks.reserve(road_proto.crosswalks().size());
    for (const auto& crosswalk_id : road_proto.crosswalks()) {
      Crosswalk* crosswalk = GetCrosswalk(crosswalk_id);
      if (crosswalk == nullptr) {
        const hdmap::Crosswalk* crosswalk_proto =
            hdmap_->GetCrosswalkPtrById(crosswalk_id);
        crosswalk = CreateCrosswalk(*crosswalk_proto);
      }
      crosswalk->add_road(road);
      crosswalks.push_back(DCHECK_NOTNULL(crosswalk));
    }
    road->set_crosswalks(crosswalks);

    // Step 3: Link Road and inner Sections' edges.
    LinkRoadAndInnerSections(road);
  }

  // Try to set opposite road for all roads.
  TrySetOppositeRoadForAllRoads();

  // Try to set neighbor road and road relationship to roundabout after all road
  // relations are created.
  for (const auto& kv : offline_elements_cache_.road_map) {
    SetNeighborRoad(road_feature_map_, kv.second.get());
    SetRoadRelationToRoundabout(kv.second.get());
  }

  // Step 4: Link Lane's edges.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    LinkLane(kv.second.get());
  }
  // Step 5: Link conflicting lanes.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    LinkConflictingLane(kv.second.get());
  }

  // Step 6: Set center line and driving areas for each crosswalk, link
  // crosswalk to traffic signals if it has.
  // NOTE(lipei): We put crosswalk's center line computation here for the
  // following reasons:
  // 1) This computation need to get the crosswalk's associated stop lines or
  // associated roads. But when we construct a crosswalk, we don't know its
  // associated roads and stop lines. So we need to link roads and stop lines
  // first.
  // 2) If we put this part into crosswalk, when user did not call
  // add_road at first or only add some roads not all roads to crosswalk, it may
  // cause unexpected error.
  for (auto& kv : offline_elements_cache_.crosswalk_map) {
    Crosswalk* crosswalk = kv.second.get();
    // Link crosswalk to traffic signals if it has.
    for (const int64_t signal_id : crosswalk->proto().signal_ids()) {
      DCHECK_NOTNULL(GetTrafficSignal(signal_id))
          ->add_associated_crosswalk(crosswalk);
    }

    math::geometry::Polyline2d center_line_string =
        GenerateCenterLineForCrosswalk(*crosswalk);
    // NOTE(Jiajun): Since not all crosswalk's have labeled associated roads or
    // stop lines, we can't guarantee that all crosswalk's center line can be
    // generated. We will notify those crosswalks without calculated center
    // lines.
    if (center_line_string.size() < kMinPointsOfCrosswalkCenterLine) {
      LOG(ERROR) << "Can't generate proper center line for crosswalk: "
                 << crosswalk->id();
      continue;
    }
    crosswalk->set_center_line(
        math::geometry::PolylineCurve2d(std::move(center_line_string)));

    crosswalk->set_driving_areas(GenerateDrivingAreasForCrosswalk(*crosswalk));
  }

  // Step 7: Build Routing Vertex for each lane.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    DCHECK_NOTNULL(kv.second.get()->mutable_routing_vertex())
        ->BuildVertex(*hdmap_, zone_lane_occupancy_map_, road_feature_map_,
                      precalculated_lane_feature_map_,
                      routing_param.vehicle_width_m(), kv.first);
  }

  // Step 8: Set section relationships for a junction.
  for (const auto& kv : offline_elements_cache_.junction_map) {
    const Junction& junction = *DCHECK_NOTNULL(kv.second);
    SetSectionRelationships(junction);
  }

  // Step 9: Set whether close to hard boundary for lane markings.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    const Lane& lane = *DCHECK_NOTNULL(kv.second);
    SetCloseToHardBoundaryForLaneMarkings(lane);
  }

  // Step 10: Associate zones to lanes.
  for (const auto& kv : offline_elements_cache_.road_map) {
    const Road& road = *DCHECK_NOTNULL(kv.second);
    for (const Section* section : road.sections()) {
      for (const Lane* lane : section->lanes()) {
        Lane* mutable_lane =
            gtl::FindOrDie(offline_elements_cache_.lane_map, lane->id()).get();
        mutable_lane->AddZones(road.zone_infos());
      }
    }
  }

  // Step 11: Set adjacent left lane and adjacent right lane of of each lane.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetAdjacentLeftLane(kv.second.get());
    SetAdjacentRightLane(kv.second.get());
  }

  // Step 12: Set Neighbor Lane ConnectionSegments.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetNeighborLaneConnectionSegments(kv.second.get());
  }

  // Step 13:  Set center line for ROAD_EXIT zones.
  // NOTE(boyu): We cannot put this step in road-zone association since there
  // could be multiple roads corresponding to the same exit zone.
  for (const auto& kv : offline_elements_cache_.zone_map) {
    Zone* mutable_zone = kv.second.get();
    if (mutable_zone->type() != hdmap::Zone::ROAD_EXIT) {
      continue;
    }

    auto center_line = GenerateCenterLineForExitZone(*mutable_zone);
    mutable_zone->set_center_line(std::move(center_line));
  }

  // Step 14: Set brothers of each lane.
  // Note: Do this step AFTER lane's successors and predecessors are all set.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetBrothers(kv.second.get());
  }

  // Step 15: Set the roads to nearest succeeding junction relation.
  SetRoadsToNearestSucceedingJunction(&offline_elements_cache_.road_map);
  // Step 16: Set the PickupDropoffZone and connect it with the road.
  // TODO(Xuezhi/Hengtong): implement the GetAllPickupDropOffZones().
  const std::unordered_map<int64_t,
                           std::shared_ptr<const hdmap::PickupDropoffZone>>*
      pickup_dropoff_zones = hdmap_->GetAllPickupDropOffZones();
  // some regions hasn't pdz
  if (pickup_dropoff_zones != nullptr) {
    for (const auto& [id, pickup_dropoff_zones_proto] : *pickup_dropoff_zones) {
      const int64_t road_id = pickup_dropoff_zones_proto->road_id();
      Road* road =
          gtl::FindOrDie(offline_elements_cache_.road_map, road_id).get();
      const auto [iter, is_inserted] =
          offline_elements_cache_.pickup_dropoff_zone_map.emplace(
              id, std::make_shared<PickupDropoffZone>(
                      pickup_dropoff_zones_proto.get(), road));
      road->AppendRelatedPickupDropoffZone(iter->second.get());
    }
  }
  // Step 17: Set crosswalks of each lane.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetCrosswalks(&zone_lane_occupancy_map_, kv.second.get());
  }
  // Step 18: Set truncated preceding lanes and sections for each lane.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetTruncatedPrecedingLanesAndSectionIds(kv.second.get());
  }
  // Step 19: Set related hard boundary infos for each lane.
  tbb::parallel_for_each(
      offline_elements_cache_.lane_map.begin(),
      offline_elements_cache_.lane_map.end(),
      [](const auto& kv) { SetHardBoundaryInfoForLane(kv.second.get()); });
  // Step 20: Set merge fork turn signal for each lane.
  for (const auto& kv : offline_elements_cache_.lane_map) {
    SetMergeForkTurnSignal(kv.second.get());
  }
  // Step 21: Set cross road neighbor lanes.
  for (const auto& kv : offline_elements_cache_.section_map) {
    SetCrossRoadLeftNeighborLane(kv.second.get());
    SetCrossRoadRightNeighborLane(kv.second.get());
  }
  // Step 22: Load all lift rods.
  const auto* all_lift_rods = hdmap_->GetAllLiftRods();
  if (all_lift_rods != nullptr) {
    for (const auto& [id, lift_rod_proto] : *all_lift_rods) {
      CreateLiftRod(lift_rod_proto);
    }
  }
}

std::vector<const Lane*> PncMapService::GetNearLanesWithPose(
    const voy::Pose& pose, double lane_searching_radius_m,
    double max_heading_diff, bool prefer_overlapped_lanes,
    const std::optional<std::vector<const pnc_map::Lane*>>&
        last_selected_lane_sequence) const {
  hdmap::Point point;
  point.set_x(pose.x());
  point.set_y(pose.y());
  point.set_z(pose.z());
  point.set_utm_zone(hdmap_->config().utm_zone());

  const std::vector<const hdmap::Road*> roads =
      hdmap_->GetRoads(point, lane_searching_radius_m);
  std::vector<hdmap::GeoInfo> geo_info_vec;
  geo_info_vec.reserve(roads.size());
  for (const hdmap::Road* road : roads) {
    geo_info_vec.push_back(hdmap_->GetGeoInfoInRoad(
        point, road->id(), /*need_closest_lanes=*/false));
  }

  // Retrieve lanes from roads that contain the point.
  std::vector<const Lane*> lanes;
  std::set<int64_t> lane_ids_set;
  const math::geometry::Point2d point_2d(pose.x(), pose.y());

  if (prefer_overlapped_lanes) {
    AppendLanesFromGeoInfo(point_2d, geo_info_vec, /*search_radius=*/0.f,
                           &lanes, &lane_ids_set);
  }
  if (lanes.empty()) {
    AppendLanesFromGeoInfo(point_2d, geo_info_vec, lane_searching_radius_m,
                           &lanes, &lane_ids_set);
  }
  // Filter the lanes which are not aligned with the ego car.
  return GetFilteredNearLanesByHeading(
      lanes,
      /*robot_center=*/{pose.x(), pose.y()},
      /*robot_heading=*/math::NormalizeMinusPiToPi(pose.yaw()),
      /*max_heading_diff*/ max_heading_diff, last_selected_lane_sequence);
}

std::vector<const Lane*> PncMapService::GetLaneSequence(
    const std::vector<int64_t>& lane_ids) const {
  std::vector<const Lane*> lane_sequence;
  lane_sequence.reserve(lane_ids.size());

  for (const int64_t lane_id : lane_ids) {
    lane_sequence.push_back(GetLane(lane_id));
  }

  return lane_sequence;
}

std::vector<const Lane*> PncMapService::GetLaneSequence(
    const ::google::protobuf::RepeatedField<int64>& lane_ids) const {
  std::vector<const Lane*> lane_sequence;
  lane_sequence.reserve(lane_ids.size());

  for (const int64_t lane_id : lane_ids) {
    lane_sequence.push_back(GetLane(lane_id));
  }

  return lane_sequence;
}

std::vector<const Road*> PncMapService::GetRoadsById(
    const std::vector<int64_t>& road_ids) const {
  std::vector<const Road*> roads;
  roads.reserve(road_ids.size());
  for (const int64_t road_id : road_ids) {
    const Road* road = GetRoad(road_id);
    if (road == nullptr) {
      LOG(ERROR) << "Road does not existed in the map. Id: " << road_id;
      continue;
    }
    roads.push_back(road);
  }

  return roads;
}

std::vector<const Road*> PncMapService::GetRoads(
    const ::google::protobuf::RepeatedField<int64>& road_ids) const {
  std::vector<const Road*> roads;
  roads.reserve(road_ids.size());
  for (const int64_t road_id : road_ids) {
    const Road* road = GetRoad(road_id);
    if (road == nullptr) {
      LOG(ERROR) << "Road does not existed in the map. Id: " << road_id;
      continue;
    }
    roads.push_back(road);
  }

  return roads;
}

std::vector<const Section*> PncMapService::GetSections(
    const std::vector<int64_t>& section_ids) const {
  std::vector<const Section*> sections;
  sections.reserve(section_ids.size());
  for (const int64_t section_id : section_ids) {
    const Section* section = GetSection(section_id);
    if (section == nullptr) {
      LOG(ERROR) << "Section does not existed in the map. Id: " << section_id;
      continue;
    }
    sections.push_back(section);
  }

  return sections;
}

std::vector<const Section*> PncMapService::GetSections(
    const ::google::protobuf::RepeatedField<int64>& section_ids) const {
  std::vector<const Section*> sections;
  sections.reserve(section_ids.size());
  for (const int64_t section_id : section_ids) {
    const Section* section = GetSection(section_id);
    if (section == nullptr) {
      LOG(ERROR) << "Section does not existed in the map. Id: " << section_id;
      continue;
    }
    sections.push_back(section);
  }

  return sections;
}

std::vector<const Zone*> PncMapService::GetZones(
    const std::vector<int64_t>& zone_ids) const {
  std::vector<const Zone*> zones;
  zones.reserve(zone_ids.size());
  for (const int64_t zone_id : zone_ids) {
    const Zone* zone = GetZone(zone_id);
    if (zone == nullptr) {
      LOG(ERROR) << "Zone does not existed in the map. Id: " << zone_id;
      continue;
    }
    zones.push_back(zone);
  }

  return zones;
}

std::unique_ptr<RoadGeoInfo> PncMapService::GetRoadGeoInfo(
    const hdmap::Point& point, const Road& road) const {
  const hdmap::GeoInfo& geo_info_proto =
      hdmap_->GetGeoInfoInRoad(point, road.id(), /*need_closest_lanes=*/true);
  // Check there is only one road in result geo info.
  DCHECK_EQ(geo_info_proto.roads().size(), 1);
  const hdmap::PointToRoad& point_to_road_proto = geo_info_proto.roads(0);
  DCHECK_EQ(point_to_road_proto.road_id(), road.id());

  const int64_t section_id = point_to_road_proto.lane_section_id();
  const Section* section = GetSection(section_id);

  std::vector<const Lane*> lanes;
  for (const auto& close_lane : point_to_road_proto.closest_lanes()) {
    if (close_lane.dist_to_lane() > 0.f) {
      continue;
    }
    Lane* lane = DCHECK_NOTNULL(GetLane(close_lane.lane_id()));
    lanes.push_back(lane);
  }
  return std::make_unique<RoadGeoInfo>(&road, section, lanes,
                                       point_to_road_proto.dist_to_road());
}

bool PncMapService::IsInJunction(const hdmap::Point& point) const {
  const std::vector<const hdmap::Junction*> junctions =
      hdmap_->GetJunctions(point, kMapElementSearchingRadiusInMeter);
  return std::any_of(junctions.begin(), junctions.end(),
                     [&point](const hdmap::Junction* junction) {
                       return hdmap::geometry_util::IsInside2D(
                           point, junction->border());
                     });
}

bool PncMapService::IsInExitZone(const hdmap::Point& point) const {
  const std::vector<const hdmap::Zone*> zones =
      hdmap_->GetZones(point, kMapElementSearchingRadiusInMeter);
  return std::any_of(
      zones.begin(), zones.end(), [&point](const hdmap::Zone* zone) {
        return hdmap::geometry_util::IsInside2D(point, zone->border()) &&
               zone->type() == hdmap::Zone::ROAD_EXIT;
      });
}

bool PncMapService::IsInBusBulb(const hdmap::Point& point) const {
  const std::vector<const hdmap::Zone*> zones =
      hdmap_->GetZones(point, kMapElementSearchingRadiusInMeter);
  return std::any_of(
      zones.begin(), zones.end(), [&point](const hdmap::Zone* zone) {
        return zone->type() == hdmap::Zone::BUS_BULB &&
               hdmap::geometry_util::IsInside2D(point, zone->border());
      });
}

void PncMapService::Clear() { offline_elements_cache_.Clear(); }

Road* PncMapService::CreateRoad(const hdmap::Road& road_proto) {
  const int64_t road_id = road_proto.id();
  DCHECK(!gtl::ContainsKey(offline_elements_cache_.road_map, road_id));

  // Create inner Sections.
  std::vector<Section*> sections;
  sections.reserve(road_proto.lane_sections().size());
  for (const auto& section_proto : road_proto.lane_sections()) {
    Section* section = CreateSection(section_proto);
    sections.push_back(DCHECK_NOTNULL(section));
  }

  // Link Sections' successors (except the tail) and predecessors (except the
  // head).
  for (size_t i = 0; i < sections.size(); ++i) {
    if (i > 0) {
      sections[i]->set_predecessors({sections[i - 1]});
    }
    if (i + 1 < sections.size()) {
      sections[i]->set_successors({sections[i + 1]});
    }
  }

  // Create inner Zones if not in cache.
  std::vector<ZoneInfo> zone_infos;
  zone_infos.reserve(road_proto.zones().size());
  for (const auto& zone_info_proto : road_proto.zones()) {
    const int64_t zone_id = zone_info_proto.zone_id();
    Zone* zone = GetZone(zone_id);
    if (zone == nullptr) {
      const hdmap::Zone* zone_proto = hdmap_->GetZonePtrById(zone_id);
      const auto [iter, is_inserted] = offline_elements_cache_.zone_map.emplace(
          zone_id, std::make_shared<Zone>(zone_proto));
      zone = iter->second.get();
    }
    zone_infos.emplace_back(zone, zone_info_proto.track_start(),
                            zone_info_proto.track_end());
  }
  std::sort(zone_infos.begin(), zone_infos.end(),
            [](const ZoneInfo& info1, const ZoneInfo& info2) {
              return info1.track_start.s() < info2.track_start.s();
            });

  // Create HardBoundary.
  CreateHardBoundary(road_proto.left_boundary());
  CreateHardBoundary(road_proto.right_boundary());
  CreateHardBoundary(road_proto.middle_boundary());

  // Create Road object and insert to cache.
  const auto [iter, is_inserted] = offline_elements_cache_.road_map.emplace(
      road_id, std::make_shared<Road>(
                   &road_proto, offline_elements_cache_.hard_boundary_map,
                   std::move(zone_infos), generate_effective_hb_, &sections));

  // Associate the Road to the Zone.
  Road* road = iter->second.get();
  for (const ZoneInfo& zone_info : road->zone_infos()) {
    Zone* zone = DCHECK_NOTNULL(GetZone(zone_info.id()));
    zone->add_road(road);
    if (zone->type() == hdmap::Zone::ROUNDABOUT &&
        !road->is_associated_with_roundabout()) {
      road->set_is_associated_with_roundabout(true);
    }
  }

  return DCHECK_NOTNULL(GetRoad(road_id));
}

Section* PncMapService::CreateSection(const hdmap::LaneSection& section_proto) {
  const int64_t section_id = section_proto.id();
  DCHECK(!gtl::ContainsKey(offline_elements_cache_.section_map, section_id));

  const LaneMarking* leftmost_marking = nullptr;
  const auto left_most_lane_marking_id =
      section_proto.left_most_lane_marking_id();
  const LaneMarking* rightmost_marking = nullptr;
  const auto right_most_lane_marking_id =
      section_proto.right_most_lane_marking_id();
  // Create LaneMarkings and insert to cache.
  std::vector<const LaneMarking*> lane_markings;
  lane_markings.reserve(section_proto.lane_markings().size());
  for (const auto& lane_marking_proto : section_proto.lane_markings()) {
    const int64_t lane_marking_id = lane_marking_proto.id();
    const auto [iter, is_inserted] =
        offline_elements_cache_.lane_marking_map.emplace(
            lane_marking_id,
            std::make_shared<LaneMarking>(&lane_marking_proto));
    if (is_inserted == false) {
      LOG(ERROR) << "Lane marking id " << lane_marking_id
                 << " appears in more than one section including section "
                 << section_proto.id();
    }
    LaneMarking* lane_marking = iter->second.get();
    lane_markings.push_back(lane_marking);
    if (left_most_lane_marking_id == lane_marking->id()) {
      leftmost_marking = lane_marking;
    }
    if (right_most_lane_marking_id == lane_marking->id()) {
      rightmost_marking = lane_marking;
    }
  }

  // Create Lanes and insert to cache.
  std::vector<Lane*> lanes;
  lanes.reserve(section_proto.lanes().size());
  const auto get_lane_marking =
      [this, &lane_markings](int64_t lane_marking_id) -> const LaneMarking* {
    for (const auto* ln_marking : lane_markings) {
      if (lane_marking_id == ln_marking->id()) {
        return ln_marking;
      }
    }
    return GetLaneMarking(lane_marking_id);
  };
  for (const auto& lane_proto : section_proto.lanes()) {
    const int64_t lane_id = lane_proto.id();
    DCHECK(!gtl::ContainsKey(offline_elements_cache_.lane_map, lane_id));

    const LaneMarking* left_marking =
        get_lane_marking(lane_proto.left_lane_marking_id());
    const LaneMarking* right_marking =
        get_lane_marking(lane_proto.right_lane_marking_id());
    const auto [iter, is_inserted] = offline_elements_cache_.lane_map.emplace(
        lane_id,
        std::make_shared<Lane>(&lane_proto, left_marking, right_marking));
    Lane* lane = iter->second.get();

    // Create PriorLane object if existing in prior path layer.
    if (prior_layer_) {
      const std::vector<const hdmap::PriorLanePath*>& prior_lane_paths =
          prior_layer_->GetPriorLanePath(lane_id);
      if (!prior_lane_paths.empty()) {
        DCHECK_EQ(prior_lane_paths.size(), 1)
            << " Map would only provide at most one prior path for every lane";
        const auto [prior_path_iter, is_inserted] =
            offline_elements_cache_.prior_lane_map.emplace(
                lane_id, std::make_shared<PriorLane>(prior_lane_paths));
        lane->AddPriorLane(DCHECK_NOTNULL(prior_path_iter->second).get());
      }
    }

    lanes.push_back(lane);
  }

  // Link Lane's left and right neighbors based on their order in Section.
  // The first Lane in the Section is the leftmost one.
  // NOTE(wilsonhong): we cannot not use lane proto's left_lane_id and
  // right_lane_id since they are not set properly.
  for (size_t i = 0; i < lanes.size(); ++i) {
    // Link left lane.
    if (i > 0) {
      lanes[i]->set_left_lane(lanes[i - 1]);
    }
    // Link right lane.
    if (i + 1 < lanes.size()) {
      lanes[i]->set_right_lane(lanes[i + 1]);
    }
  }

  // Create Section object and insert to cache.
  if (leftmost_marking == nullptr) {
    leftmost_marking = GetOrCreateLaneMarking(left_most_lane_marking_id);
  }
  if (rightmost_marking == nullptr) {
    rightmost_marking = GetOrCreateLaneMarking(right_most_lane_marking_id);
  }
  const auto [iter, is_inserted] = offline_elements_cache_.section_map.emplace(
      section_id,
      std::make_shared<Section>(&section_proto, lane_markings, leftmost_marking,
                                rightmost_marking, &lanes));
  return iter->second.get();
}

Crosswalk* PncMapService::CreateCrosswalk(
    const hdmap::Crosswalk& crosswalk_proto) {
  const int64_t crosswalk_id = crosswalk_proto.id();
  DCHECK(
      !gtl::ContainsKey(offline_elements_cache_.crosswalk_map, crosswalk_id));
  std::vector<const StopLine*> stop_lines =
      GetOrCreateStopLines(crosswalk_proto.stop_lines());
  DCHECK_EQ(crosswalk_proto.stop_lines().size(), stop_lines.size());
  std::vector<const TrafficSignal*> traffic_signals =
      GetOrCreateTrafficSignals(crosswalk_proto.signal_ids());
  const auto [iter, is_inserted] =
      offline_elements_cache_.crosswalk_map.emplace(
          crosswalk_id,
          std::make_shared<Crosswalk>(&crosswalk_proto, std::move(stop_lines),
                                      std::move(traffic_signals)));
  // TODO(wilsonhong): Consider link Crosswalk to TrafficSign.
  return iter->second.get();
}

Junction* PncMapService::CreateJunction(const hdmap::Junction& junction_proto) {
  const int64_t junction_id = junction_proto.id();
  DCHECK(!gtl::ContainsKey(offline_elements_cache_.junction_map, junction_id));

  std::vector<Road*> roads;
  roads.reserve(junction_proto.road_ids().size());
  for (int64_t road_id : junction_proto.road_ids()) {
    Road* road = GetRoad(road_id);
    if (road == nullptr) {
      LOG(ERROR) << "Junction road does not existed in the map. Id: "
                 << road_id;
      continue;
    }
    roads.push_back(DCHECK_NOTNULL(road));
  }

  const auto [iter, is_inserted] = offline_elements_cache_.junction_map.emplace(
      junction_id, std::make_shared<Junction>(&junction_proto, &roads));
  return iter->second.get();
}

TrafficSign* PncMapService::CreateTrafficSign(const hdmap::Sign& sign_proto) {
  int64_t sign_id = sign_proto.id();
  DCHECK(!gtl::ContainsKey(offline_elements_cache_.traffic_sign_map, sign_id));

  std::vector<const StopLine*> stop_lines =
      GetOrCreateStopLines(sign_proto.stop_lines());
  // Create TrafficSign and insert to cache.
  const auto [iter, is_inserted] =
      offline_elements_cache_.traffic_sign_map.emplace(
          sign_id, std::make_shared<TrafficSign>(&sign_proto, stop_lines));
  TrafficSign* traffic_sign = iter->second.get();
  // Link TrafficSign to Lane.
  for (const StopLine* stop_line : traffic_sign->stop_lines()) {
    const int64_t lane_id = stop_line->lane()->id();
    Lane* lane = GetLane(lane_id);
    // NOTE: It is possible the Lane associated to the TrafficSign is not
    // created, due to not in the global path. If this case, we will skip the
    // linking.
    if (lane != nullptr) {
      lane->add_traffic_sign(traffic_sign);
    }
  }
  return traffic_sign;
}

TrafficSignal* PncMapService::CreateTrafficSignal(
    const hdmap::Signal& signal_proto) {
  const int64_t signal_id = signal_proto.id();
  DCHECK(
      !gtl::ContainsKey(offline_elements_cache_.traffic_signal_map, signal_id));

  std::vector<const StopLine*> stop_lines =
      GetOrCreateStopLines(signal_proto.stop_lines());
  // Create TrafficSignal and insert to cache.
  const auto [iter, is_inserted] =
      offline_elements_cache_.traffic_signal_map.emplace(
          signal_id,
          std::make_shared<TrafficSignal>(&signal_proto, stop_lines));

  TrafficSignal* traffic_signal = iter->second.get();
  // Link TrafficSignal to Lane.
  for (const auto& associate_lane_proto : signal_proto.associate_lanes()) {
    const int64_t lane_id = associate_lane_proto.lane_id();
    Lane* lane = GetLane(lane_id);
    // If associated lane does not exist in cache, we do not need to link
    // signal to the lane since the lane is not covered in global path.
    if (lane != nullptr) {
      lane->add_traffic_signal(traffic_signal, associate_lane_proto.type());
    }
  }
  return traffic_signal;
}

void PncMapService::CreateHardBoundary(
    const hdmap::RoadBoundary& road_boundary) {
  for (const auto& hard_boundary : road_boundary.hard_boundaries()) {
    const int64_t hard_boundary_id = hard_boundary.hard_boundary_id();
    // We might have duplicated hard boundaries in left/right/middle now.
    if (offline_elements_cache_.hard_boundary_map.find(hard_boundary_id) !=
        offline_elements_cache_.hard_boundary_map.end()) {
      continue;
    }

    const hdmap::HardBoundary* hard_boundary_proto =
        hdmap_->GetHardBoundaryPtrById(hard_boundary_id);
    offline_elements_cache_.hard_boundary_map.emplace(
        hard_boundary_id, std::make_shared<HardBoundary>(hard_boundary_proto));
  }
}

std::vector<const TrafficSignal*> PncMapService::GetOrCreateTrafficSignals(
    const ::google::protobuf::RepeatedField<int64>& signal_ids) {
  std::vector<const TrafficSignal*> traffic_signals;
  traffic_signals.reserve(signal_ids.size());
  for (const int64 signal_id : signal_ids) {
    const TrafficSignal* traffic_signal = GetTrafficSignal(signal_id);
    if (traffic_signal != nullptr) {
      traffic_signals.push_back(traffic_signal);
      continue;
    }

    // The road's |traffic_signals| may include all pedestrian lamps of the
    // intersection into which the road enters. But, if the signal does not
    // exist, we create traffic signal directly in order to avoid the change of
    // labeling rule.
    traffic_signal = CreateTrafficSignal(
        *DCHECK_NOTNULL(hdmap_->GetSignalPtrById(signal_id)));
    traffic_signals.push_back(traffic_signal);
  }

  return traffic_signals;
}

std::vector<const StopLine*> PncMapService::GetOrCreateStopLines(
    const ::google::protobuf::RepeatedPtrField<hdmap::StopLine>&
        stop_line_rep_field) {
  std::vector<const StopLine*> stop_lines;
  for (const auto& stop_line_proto : stop_line_rep_field) {
    const int64_t stop_line_id = stop_line_proto.id();
    const StopLine* stop_line = GetStopLine(stop_line_id);
    if (stop_line == nullptr) {
      // StopLine not in the cache, try to create it.
      stop_line = CreateStopLine(stop_line_proto);
    }
    // It is possible that the StopLine cannot be created, so need to check
    // again.
    if (stop_line != nullptr) {
      stop_lines.push_back(stop_line);
    }
  }
  return stop_lines;
}

StopLine* PncMapService::CreateStopLine(
    const hdmap::StopLine& stop_line_proto) {
  const int64_t stop_line_id = stop_line_proto.id();
  DCHECK(
      !gtl::ContainsKey(offline_elements_cache_.stop_line_map, stop_line_id));

  const int64_t lane_id = stop_line_proto.lane_id();
  const Lane* lane = GetLane(lane_id);
  // NOTE: It is possible the associated Lane is not created due to not in
  // global path. In this case, just return nullptr.
  if (lane == nullptr) {
    return nullptr;
  }
  const auto [iter, is_inserted] =
      offline_elements_cache_.stop_line_map.emplace(
          stop_line_id, std::make_shared<StopLine>(&stop_line_proto, lane));
  return iter->second.get();
}

std::shared_ptr<LiftRod> PncMapService::CreateLiftRod(
    const hdmap::LiftRod& rod_proto) {
  const int64_t id = rod_proto.id();
  DCHECK(!gtl::ContainsKey(offline_elements_cache_.lift_rod_map, id));

  const std::vector<const Lane*> lanes = GetLaneSequence(rod_proto.lane_ids());
  const auto [iter, _] = offline_elements_cache_.lift_rod_map.emplace(
      id, std::make_shared<LiftRod>(&rod_proto, lanes));
  for (const auto* lane : lanes) {
    if (lane != nullptr) {
      offline_elements_cache_.lane_to_lift_rod_map[lane->id()].push_back(
          iter->second);
    }
  }
  return iter->second;
}

const LaneMarking* PncMapService::GetOrCreateLaneMarking(
    int64_t lane_marking_id) {
  if (lane_marking_id <= 0) {
    return nullptr;
  }
  const auto* lane_marking = GetLaneMarking(lane_marking_id);
  if (lane_marking != nullptr) {
    return lane_marking;
  }
  const auto* lane_marking_proto =
      hdmap_->GetLaneMarkingPtrById(lane_marking_id);
  if (lane_marking_proto == nullptr) {
    DCHECK(true) << "No proto is found for lane marking id: "
                 << lane_marking_id;
    return nullptr;
  }
  const auto [iter, is_inserted] =
      offline_elements_cache_.lane_marking_map.emplace(
          lane_marking_id, std::make_shared<LaneMarking>(lane_marking_proto));
  return iter->second.get();
}

void PncMapService::TrySetOppositeRoad(Road* road) const {
  // Return if opposite road is not nullptr;
  if (road->opposite_road()) {
    return;
  }
  Road* opposite_road = GetOppositeRoad(road);
  road->set_opposite_road(opposite_road);
  if (opposite_road && !opposite_road->opposite_road()) {
    opposite_road->set_opposite_road(road);
  }
}

void PncMapService::TrySetOppositeRoadForAllRoads() {
  // Try to set opposite road for every road.
  // Determine opposite road for a junction road is more complicated.
  // The logic requires opposite roads information for the junction road
  // successors or predecessors. Therefore, we set opposite road for every
  // non-junction road first.
  for (const auto& [_, road] : offline_elements_cache_.road_map) {
    if (!road->IsInJunction()) {
      TrySetOppositeRoad(road.get());
    }
  }

  // Set opposite road for junction roads.
  for (const auto& [_, road] : offline_elements_cache_.road_map) {
    if (road->IsInJunction()) {
      TrySetOppositeRoad(road.get());
    }
  }
}

void PncMapService::SetNeighborRoad(
    const std::unordered_map</*road_id*/ int64_t, routing::RoadFeature>&
        road_feature_map,
    Road* road) const {
  std::vector<int64_t> same_predecessor_neighbor_road_ids;
  std::vector<int64_t> same_successor_neighbor_road_ids;

  auto it = road_feature_map.find(road->id());
  if (it != road_feature_map.end()) {
    same_predecessor_neighbor_road_ids.reserve(
        it->second.neighbor_roads_with_same_predecessors().size());
    same_successor_neighbor_road_ids.reserve(
        it->second.neighbor_roads_with_same_successors().size());
    for (const hdmap::Road* predecessor_neighbor :
         it->second.neighbor_roads_with_same_predecessors()) {
      same_predecessor_neighbor_road_ids.push_back(predecessor_neighbor->id());
    }

    for (const hdmap::Road* successor_neighbor :
         it->second.neighbor_roads_with_same_successors()) {
      same_successor_neighbor_road_ids.push_back(successor_neighbor->id());
    }
  }

  road->set_neighbor_roads(GetRoadsById(same_predecessor_neighbor_road_ids),
                           GetRoadsById(same_successor_neighbor_road_ids));
}

std::vector<const Lane*> PncMapService::GetOverlapLanesInRoad(
    const std::vector<hdmap::Point>& points, int64_t road_id) const {
  std::set<int64_t> lane_ids_set;
  // Retrieve lanes from roads that contain any of the point.
  for (const auto& point : points) {
    const hdmap::GeoInfo& geo_info =
        hdmap_->GetGeoInfoInRoad(point, road_id, /*need_closest_lanes=*/true);
    for (const auto& point_to_road : geo_info.roads()) {
      for (const auto& close_lane : point_to_road.closest_lanes()) {
        if (close_lane.dist_to_lane() > 0.f) {
          continue;
        }
        lane_ids_set.insert(close_lane.lane_id());
      }
    }
  }
  std::vector<const Lane*> lanes;
  for (const int64_t lane_id : lane_ids_set) {
    const Lane* lane = DCHECK_NOTNULL(GetLane(lane_id));
    lanes.push_back(lane);
  }
  return lanes;
}

std::vector<const Road*> PncMapService::GetNearRoads(double radius) const {
  std::set<int64_t> road_ids_set;
  std::vector<const hdmap::Road*> near_road_protos =
      hdmap_->GetRoadsNear(radius);
  for (const hdmap::Road* road_proto : near_road_protos) {
    road_ids_set.insert(road_proto->id());
  }
  std::vector<const Road*> roads;
  for (const int64_t road_id : road_ids_set) {
    const Road* road = GetRoad(road_id);
    if (road != nullptr) {
      roads.push_back(road);
    }
  }
  return roads;
}

bool PncMapService::IsOppositeRoadPair(const Road* current_road,
                                       const Road* near_road) const {
  DCHECK(current_road && near_road);
  DCHECK(current_road->IsInJunction());

  if (near_road->predecessors().empty() || near_road->successors().empty()) {
    return false;
  }
  const Road* successor_road = current_road->successors().front();
  const Road* predecessor_road = current_road->predecessors().front();
  const Road* successor_opposite_road = successor_road->opposite_road();
  const Road* predecessor_opposite_road = predecessor_road->opposite_road();
  if (!successor_opposite_road || !predecessor_opposite_road) {
    return false;
  }

  // Return true if and only if one of successors is opposite road to current
  // road's predecessor and one of predecessors is opposite road to current
  // road's successor.
  return std::any_of(near_road->successors().begin(),
                     near_road->successors().end(),
                     [id = predecessor_opposite_road->id()](const Road* road) {
                       return road->id() == id && !road->IsInJunction();
                     }) &&
         std::any_of(near_road->predecessors().begin(),
                     near_road->predecessors().end(),
                     [id = successor_opposite_road->id()](const Road* road) {
                       return road->id() == id && !road->IsInJunction();
                     });
}

Road* PncMapService::GetOppositeRoad(const Road* road) const {
  DCHECK(road);
  // Return nullptr for the invalid road in the junction.
  if (road->IsInJunction() && !IsApplicableJunctionRoad(road)) {
    return nullptr;
  }

  // First get the middle point of the left boundary of the road.
  const auto& left_soft_boundary = road->left_boundary().soft_boundary_line;
  if (left_soft_boundary.empty()) {
    return nullptr;
  }
  const double arclength = left_soft_boundary.GetTotalArcLength();
  const math::geometry::Point2d middle_point =
      left_soft_boundary.GetInterp(arclength * 0.5);
  const hdmap::Point hdmap_middle_point = hdmap::point_util::BuildHdmapPoint(
      middle_point.x(), middle_point.y(), /*z=*/0.0);
  const double middle_point_theta =
      left_soft_boundary.GetInterpTheta(arclength * 0.5);
  const std::vector<const hdmap::Road*> near_roads =
      hdmap_->GetRoads(hdmap_middle_point, kSearchRadiusForGetOppositeRoad);
  Road* opposite_road = nullptr;
  double nearest_dist = std::numeric_limits<double>::max();
  // Return the first road found near the middle point of left boundary that
  // isn't the given road itself.
  for (const auto* near_road_proto : near_roads) {
    if (near_road_proto->id() == road->id()) {
      continue;
    }
    Road* near_road = GetRoad(near_road_proto->id());
    // Project the road left boundary middle point onto the near road left
    // boundary, and get the theta. Only consider as opposite road if the
    // angle difference is greater than a threshold.
    const auto& near_road_left_soft_boundary =
        near_road->left_boundary().soft_boundary_line;
    const auto proximity = near_road_left_soft_boundary.GetProximity(
        {middle_point.x(), middle_point.y()},
        math::pb::UseExtensionFlag::kForbid);
    const double near_road_middle_point_theta =
        near_road_left_soft_boundary.GetInterpTheta(proximity.arc_length);
    const double road_distance = math::geometry::ComparableDistance(
        middle_point, math::geometry::Point2d{proximity.x, proximity.y});
    // The opposite road should has similar curvature with current road.
    // Moreover, the successors/predecessors of junction road should be
    // opposite road of the current road's predecessors/successors.
    if (road_distance > nearest_dist ||
        (road->IsInJunction() && !IsOppositeRoadPair(road, near_road)) ||
        std::abs(
            math::AngleDiff(middle_point_theta, near_road_middle_point_theta)) <
            kMinAngleDiffForOppositeRoadInRad) {
      continue;
    }
    nearest_dist = road_distance;
    opposite_road = near_road;
  }
  // If no opposite road found, return nullptr.
  return opposite_road;
}

void PncMapService::AppendLanesFromGeoInfo(
    const math::geometry::Point2d& point,
    const std::vector<hdmap::GeoInfo>& geo_info_vec, double search_radius,
    std::vector<const pnc_map::Lane*>* lanes,
    std::set<int64_t>* lane_ids_set) const {
  const double search_radius_square = search_radius * search_radius;
  for (const auto& geo_info : geo_info_vec) {
    for (const auto& point_to_road : geo_info.roads()) {
      const Road* road = GetRoad(point_to_road.road_id());
      for (const Section* section : DCHECK_NOTNULL(road)->sections()) {
        const double comparable_distance_to_section =
            math::geometry::ComparableDistance(
                point, DCHECK_NOTNULL(section)->border());
        if (comparable_distance_to_section > search_radius_square) {
          continue;
        }
        for (const Lane* lane : section->lanes()) {
          if (lane == nullptr) {
            continue;
          }
          const double comparable_distance_to_lane =
              math::geometry::ComparableDistance(
                  point, DCHECK_NOTNULL(lane)->border());
          if (comparable_distance_to_lane > search_radius_square) {
            continue;
          }
          const auto it = lane_ids_set->insert(lane->id());
          if (it.second) {
            lanes->push_back(lane);
          }
        }
      }
    }
  }
}

}  // namespace pnc_map
