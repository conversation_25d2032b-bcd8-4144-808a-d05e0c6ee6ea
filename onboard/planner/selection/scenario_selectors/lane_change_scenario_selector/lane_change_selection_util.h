#ifndef ONBOARD_PLANNER_SELECTION_SCENARIO_SELECTORS_LANE_CHANGE_SCENARIO_SELECTOR_LANE_CHANGE_SELECTION_UTIL_H_
#define ONBOARD_PLANNER_SELECTION_SCENARIO_SELECTORS_LANE_CHANGE_SCENARIO_SELECTOR_LANE_CHANGE_SELECTION_UTIL_H_

#include <map>
#include <optional>
#include <sstream>
#include <string>

#include "planner/constants.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/world_model/speed_world_model.h"

namespace planner {
namespace selection {
/**
 * This file contains utility functions for lane change selection.
 */

// Computes the state at time t during a two-phase piecewise jerk motion:
// - phase 1: release brake or gas with the input jerk until the acceleration
// reaches zero.
// - phase 2: keep constant velocity moving.
// NOTE: The input jerk here should be absolute value and non-zero.
std::optional<speed::State>
ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
    const speed::State& init_state, double t, double jerk);

// Computes the time tolerance in advance for the lane change gap being seen
// almost aligned.
double ComputeLaneChangeGapAlignedTolerance(
    const CrossLaneObjectInfo* gap_object_info, bool is_pass_gap,
    double ego_speed, double urgency_score, bool is_consecutive_lc,
    std::ostringstream& debug_oss);

// Computes if the lane change gap is ready by considering the relative position
// and speed of ego and the agent.
bool IsLaneChangeGapReadyByRelativeStates(
    const SpeedWorldModel& world_model,
    const speed::TrajectoryInfo& trajectory_info,
    const math::geometry::PolylineCurve2d& extended_path,
    const speed::pb::SpeedSeed& speed_seed,
    const CrossLaneObjectInfo* gap_object_info, bool is_pass_gap,
    double future_t, std::ostringstream& debug_oss);

// Computes if the gap for lane change is ready, which means the gap has almost
// been aligned during lane keep.
bool IsLaneChangeGapReady(const SpeedWorldModel& world_model,
                          const speed::TrajectoryInfo& trajectory_info,
                          const math::geometry::PolylineCurve2d& extended_path,
                          const speed::pb::SpeedSeed& speed_seed,
                          double urgency_score, double gap_aligned_time,
                          bool is_pass_gap, std::ostringstream& debug_oss);

// Computes the lane change gap info. Returns true if there is a valid gap.
bool GetLaneChangeGapInfo(const speed::pb::SpeedSeed& speed_seed,
                          bool& is_pass_gap, double& gap_aligned_time);

// Returns true if there is not much risk on the cross lane candidate.
bool IsCrossLaneSafe(const TrajectoryMetaData& candidate,
                     std::ostringstream& debug_oss);

// Computes flicker cost for cross lane candidates to control the cross lane
// timing.
void ComputeCrossLaneFlickerCost(const SpeedWorldModel& world_model,
                                 const planner::pb::DecoupledManeuverSeed& seed,
                                 TrajectoryMetaData* candidate,
                                 bool is_lane_change_ready);

// Adjusts path diff and lateral cost for lane change candidates according to
// the last path latching status.
double CalculateCrossLaneDiffCompensationByLastPath(
    const std::vector<TrajectoryMetaData*>& candidates,
    int64_t consecutive_last_cl_cycles);

// Updates selected lane change gap info.
void UpdateSelectedLaneChangeGapInfo(
    const std::map<std::string, TrajectoryMetaData*>& candidates,
    const planner::pb::SelectedLaneChangeGapInfo& prev_selected_gap_info,
    planner::pb::SelectedLaneChangeGapInfo* selected_gap_info);

// Returns true if ego is on a roundabout.
bool IsLaneInRoundabout(const pnc_map::Lane* current_lane);

// Gets the reroute increasing distance from preview route result. If cannot
// reroute but ego is on the road through a roundabout, this result would be an
// estimated value.
std::optional<double> GetRerouteIncreasingDistance(
    const std::optional<lane_selection::PreviewRouteResult>&
        preview_route_result,
    const pnc_map::Lane* current_lane);

// Gets the urgency score of current lane change considering the route. Returns
// nullopt if there isn't hard lane change end or there is no reroute option.
std::optional<double> GetLaneChangeRouteUrgencyScore(
    const CrossLaneInfo& cross_lane_info, const pnc_map::Lane* current_lane,
    bool& can_reroute, std::ostringstream& debug_oss);

// Returns true when we should apply a large reward for lane change candidates,
// such as lane change crawl or creep.
bool ShouldGiveRewardToLaneChange(const speed::TrajectoryInfo& trajectory_info);

bool IsLaneChangeDueToBlockage(const CrossLaneInfo& cross_lane_info);

// Returns true when the lane change speed discomfort is high and there may be
// risk to execute the lane change.
bool IsRiskyLaneChangeAtHighDiscomfort(
    double speed_discomfort, double ego_speed,
    double comfortable_lc_half_duration, double ego_ra_to_rb,
    const CrossLaneRegionInfo* target_region_info_ptr,
    std::ostringstream& debug_oss);

// Returns true when the lane change is not urgent and the speed discomfort is
// high.
bool IsHighDiscomfortLaneChangeUnderLowUrgency(
    const CrossLaneInfo& cross_lane_info, double speed_discomfort,
    double ego_speed, double ego_ra_to_rb, std::ostringstream& debug_oss);

// Returns true when the lane change is triggered by progress elc (exclude elc
// triggered by bus since we don't want to follow a bus entering the bus
// station) and the speed discomfort is greater than 0.0.
bool IsRiskyELC(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const planner::pb::LaneChangeSignalSourceType&
        lane_change_signal_source_type,
    int64_t elc_triggered_object_id, double speed_discomfort);

// Returns true when the lane change is in a fork and the speed discomfort is
// equal or higher than 0.75.
bool IsRiskyLaneChangeInFork(bool is_current_lane_change_in_fork,
                             double speed_discomfort);

// Returns true when ego is on highway and the lane change is not urgent and the
// CL speed discomfort is greater than 0.0.
bool IsRiskyLaneChangeOnHighway(const CrossLaneInfo& cross_lane_info,
                                double speed_discomfort);

/**
 * Target neighbor risk related util functions.
 */
// Returns true if the cut-in object can't be several meters ahead of ego when
// reaching the lateral time-to-collision, which means the agent is close to ego
// and is not much faster, and it may collide with ego if ego keeps doing lane
// change.
// NOTE: Only handles cut-in agents here.
bool IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
    const RobotState& robot_state,
    const CrossLaneInfoManager& cross_lane_info_manager, int64_t object_id,
    std::ostringstream& debug_oss);

// Computes lane change completion rate, which will be associated with costs
// adjustment.
double ComputeLcCompletionRate(const RobotState& ego,
                               const LaneChangeInstance& lane_change_instance);

// Returns if ego has shown lane change intention by judging if any of the
// following is true:
// - Ego heads toward the target lane.
// - Ego has changed some heading from its first pose.
// - Ego's bbox intersects with target lane or its successors' borders.
bool EgoHasShownLcIntention(
    const vehicle_model::pb::AxleRectangularMeasurement& ego_shape,
    const planner::pb::TrajectoryPose& ego_pose,
    const lane_selection::LaneSequenceGeometry* lf_lane_seq_geometry,
    const pnc_map::Lane& target_lane, const TrajectoryMetaData& candidate);

// True if an obsolete gap is used for lane change.
bool IsLcGapObsolete(const speed::pb::SpeedSeed& speed_seed,
                     const TrajectoryMetaData& candidate,
                     std::ostringstream& debug_oss);

// Returns true when there is a yield decision on the object.
bool HasYieldDecision(const speed::SpeedResult& speed_result, int64_t object_id,
                      const std::string& unique_primary_traj_id = "");
bool HasYieldDecision(
    const speed::SpeedResult& speed_result, int64_t object_id,
    const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories,
    bool only_check_primary_decision = false);

// Compute the min or max acceleration in the first few seconds (max_time_range)
// on the trajectory. Default to compute the min acceleration.
double GetMinOrMaxAccelerationInTimeRange(
    const planner::pb::Trajectory& trajectory,
    double max_time_range = constants::kTrajectoryHorizonInSec,
    bool update_min = true);

void UpdateCrossLaneSeed(
    const std::map<std::string, TrajectoryMetaData*>& candidates,
    const TrajectoryMetaData* selected_trajectory,
    const planner::pb::SelectionSeed& prev_selection_seed,
    bool enable_lane_change_scenario,
    planner::pb::SelectionSeed* selection_seed);

bool IsExcessiveBlockageAvoidanceLc(
    const CrossLaneInfo& cross_lane_info,
    const planner::pb::LaneChangeInfoSeed& previous_lane_change_info_seed,
    std::ostringstream& debug_oss);

}  // namespace selection
}  // namespace planner

#endif  // ONBOARD_PLANNER_SELECTION_SCENARIO_SELECTORS_LANE_CHANGE_SCENARIO_SELECTOR_LANE_CHANGE_SELECTION_UTIL_H_
