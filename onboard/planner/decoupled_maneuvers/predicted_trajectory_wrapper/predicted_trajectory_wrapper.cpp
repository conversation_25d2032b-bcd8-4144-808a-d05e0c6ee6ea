#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"

#include <algorithm>
#include <numeric>
#include <unordered_set>

#include "math/math_util.h"
#include "planner/world_model/traffic_participant/predicted_trajectory_interpolator.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "prediction/common/definition/prediction_constants.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {

// The min heading difference between the headings of the first and last
// predicted poses of the predicted trajectory.
constexpr double kTurningTrajectoryMinHeadingDiffInRad = M_PI / 6.0;
// The maximum time difference for a pose in predicted trajectory to be
// used without interpolation. This is used to speed up the function
// without compromising too much accuracy.
constexpr int kMaxTimeDiffToSkipInterpolationInMs = 10;

constexpr int64 kMaxPredictionAgentRank = 5;
constexpr double kMinTrajectoryProbability = 0.1;  // 10%
constexpr int kMinCertainPredictedTrajectoryLengthInMs = 2000;
constexpr int kMinCertainPosesNum = kMinCertainPredictedTrajectoryLengthInMs /
                                    prediction::constants::kPoseIntervalInMs;

// Returns if all poses in predicted trajectory proto have the same pose as the
// tracked object.
bool TrajHasSamePoses(
    const voy::TrackedObject& tracked_object_proto,
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto) {
  return std::all_of(predicted_trajectory_proto.traj_poses().begin(),
                     predicted_trajectory_proto.traj_poses().end(),
                     [&tracked_object_proto](const auto& traj_pose) {
                       return HasSamePose(tracked_object_proto, traj_pose);
                     });
}

// this logic is ported from
// PredictedTrajectory::GetPredictedPosesForStaticObject
void GetPredictedPosesForStaticObject(
    const voy::TrackedObject& tracked_object,
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
    std::vector<PredictedPose>* predicted_poses) {
  if (predicted_trajectory_proto.traj_poses().empty()) {
    return;
  }
  DCHECK(predicted_poses != nullptr);
  predicted_poses->clear();
  predicted_poses->reserve(2);

  const auto& traj_id = predicted_trajectory_proto.id();
  const auto& first_pose = predicted_trajectory_proto.traj_poses(0);
  const auto& last_pose = predicted_trajectory_proto.traj_poses(
      predicted_trajectory_proto.traj_poses_size() - 1);

  predicted_poses->emplace_back(tracked_object, first_pose, traj_id,
                                /*odom=*/0.0, /*is_stationary=*/true);
  predicted_poses->emplace_back(tracked_object, last_pose, traj_id,
                                /*odom=*/0.0, /*is_stationary=*/true);
}

// this logic is ported from
// PredictedTrajectory::GetPredictedPosesForMovingObject
void GetPredictedPosesForMovingObject(
    const voy::TrackedObject& tracked_object,
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
    bool is_stationary,
    const std::optional<int64_t>& max_interested_prediction_timestamp,
    std::vector<PredictedPose>* predicted_poses) {
  if (predicted_trajectory_proto.traj_poses().empty()) {
    return;
  }

  DCHECK(predicted_poses != nullptr);
  predicted_poses->clear();

  const auto& traj_id = predicted_trajectory_proto.id();
  const int uncertain_idx =
      predicted_trajectory_proto.uncertain_trajectory_start_pose_idx();
  const auto& traj_poses = predicted_trajectory_proto.traj_poses();
  const size_t pose_count = traj_poses.size();

  // If the uncertain index is invalid or not set, treat all poses as certain.
  const size_t uncertain_pose_start_idx =
      (!is_stationary && uncertain_idx > 0 &&
       uncertain_idx < static_cast<int>(pose_count))
          ? static_cast<size_t>(uncertain_idx)
          : pose_count;

  // If the max interested prediction timestamp is not set, we assume all poses
  // are interested.
  // Otherwise, we find the first pose that has a timestamp greater than the
  // max interested prediction timestamp.
  // If the max interested prediction timestamp is set, we only consider poses
  // that have a timestamp less than or equal to the max interested prediction
  // timestamp.
  const int64_t max_prediction_timestamp =
      max_interested_prediction_timestamp.value_or(
          traj_poses[pose_count - 1].timestamp());

  const auto iter = std::find_if(
      traj_poses.begin(), traj_poses.end(),
      [max_prediction_timestamp](const planner::pb::TrajectoryPose& pose) {
        return pose.timestamp() > max_prediction_timestamp;
      });

  const int num_pose =
      std::min(static_cast<int>(pose_count),
               static_cast<int>(std::distance(traj_poses.begin(), iter)));
  if (num_pose == 0) {
    return;
  }

  int non_ignorable_poses_length = num_pose;
  int extended_poses_length = 0;

  if (tracked_object.object_type() == voy::perception::PED &&
      uncertain_pose_start_idx < static_cast<size_t>(num_pose)) {
    non_ignorable_poses_length = static_cast<int>(uncertain_pose_start_idx);
    extended_poses_length =
        std::max(0, kMinCertainPosesNum - non_ignorable_poses_length);
  }

  predicted_poses->reserve(non_ignorable_poses_length + extended_poses_length);
  double accumulated_odom = 0.0;

  const auto& first_pose = traj_poses[0];
  predicted_poses->emplace_back(tracked_object, first_pose, traj_id,
                                accumulated_odom,
                                /*is_stationary=*/false);

  for (int i = 1; i < non_ignorable_poses_length; ++i) {
    const auto& prev = traj_poses[i - 1];
    const auto& curr = traj_poses[i];

    accumulated_odom += math::FastHypot(curr.x_pos() - prev.x_pos(),
                                        curr.y_pos() - prev.y_pos());
    predicted_poses->emplace_back(tracked_object, curr, traj_id,
                                  accumulated_odom,
                                  /*is_stationary=*/false);
  }

  if (!predicted_poses->empty() && extended_poses_length > 0) {
    PredictedPose pose_to_extend = predicted_poses->back();

    for (int i = 0; i < extended_poses_length; ++i) {
      pose_to_extend.set_timestamp(pose_to_extend.timestamp() +
                                   prediction::constants::kPoseIntervalInMs);
      predicted_poses->push_back(pose_to_extend);
    }
  }
}

// Ported logic from
// onboard/planner/world_model/traffic_participant/tracked_object.cpp:186
// https://kunpeng.xiaojukeji.com/view/revision/3197967
bool ShouldIgnorePredictedTrajectory(
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
    std::optional<int64> prediction_agent_rank) {
  // Ignore if rank is not provided or exceeds the maximum allowed value
  if (!prediction_agent_rank.has_value() ||
      *prediction_agent_rank > kMaxPredictionAgentRank) {
    return true;
  }

  // Ignore if it's not a multi-output trajectory
  if (!predicted_trajectory_proto.is_multi_output_trajectory()) {
    return true;
  }

  // Ignore if the probability is below the minimum threshold
  return predicted_trajectory_proto.probability_in_ppm() * 1e-6 <
         kMinTrajectoryProbability;
}
}  // namespace

std::vector<int> ComputeSamplingIndexForPredictedTrajectory(
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const int sampled_size, const double min_required_odom_progress_in_meter) {
  std::vector<int> sampled_indices;
  DCHECK(!predicted_trajectory.empty());
  DCHECK_LT(1, sampled_size);

  const int sampling_interval =
      static_cast<int>(std::floor(predicted_trajectory.size() / sampled_size));
  DCHECK_LT(0, sampling_interval);

  sampled_indices.reserve(sampled_size + 1);

  for (int idx = 0; idx < predicted_trajectory.size();
       idx += sampling_interval) {
    if (!sampled_indices.empty()) {
      const double odom_progress_in_meter =
          predicted_trajectory.pose(idx).odom() -
          predicted_trajectory.pose(sampled_indices.back()).odom();
      if (odom_progress_in_meter < min_required_odom_progress_in_meter)
        continue;
    }
    sampled_indices.push_back(idx);
  }
  return sampled_indices;
}

PredictedTrajectoryWrapper::PredictedTrajectoryWrapper(
    const prediction::pb::Agent& agent,
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
    bool is_primary_trajectory,
    const std::optional<int64_t>& max_interested_prediction_timestamp)
    : PredictedTrajectoryWrapper(
          agent.tracked_object(), predicted_trajectory_proto,
          is_primary_trajectory, agent.stationary_intention()) {
  should_ignore_ = ShouldIgnorePredictedTrajectory(
      predicted_trajectory_proto,
      agent.importance_rank_to_ego() != 0
          ? std::make_optional(agent.importance_rank_to_ego())
          : std::nullopt);

  // Update legacy predicted poses only if this trajectory is relevant
  if (!should_ignore_ || is_primary_trajectory) {
    UpdateLegacyPredictedPoses(max_interested_prediction_timestamp);
  }
}

PredictedTrajectoryWrapper::PredictedTrajectoryWrapper(
    const voy::TrackedObject& tracked_object_proto,
    const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
    bool is_primary_trajectory,
    prediction::pb::StationaryIntention object_stationary_intention)
    : predicted_trajectory_proto_(predicted_trajectory_proto),
      tracked_object_proto_(tracked_object_proto),
      object_id_(tracked_object_proto.id()),
      is_primary_trajectory_(is_primary_trajectory),
      object_stationary_intention_(std::move(object_stationary_intention)),
      trajectory_motion_type_(
          predicted_trajectory_proto.trajectory_motion_type()) {
  TRACE_EVENT_SCOPE(
      planner, PredictedTrajectoryWrapper_ConstructPredictedTrajectoryWrapper);

  if (HasStationaryNotToMoveIntention()) {
    DCHECK(TrajHasSamePoses(tracked_object_proto, predicted_trajectory_proto));
    is_stationary_ = true;
  } else {
    is_stationary_ =
        TrajHasSamePoses(tracked_object_proto, predicted_trajectory_proto);
  }
  IsStationary() ? UpdatePredictedPosesForStaticObject(tracked_object_proto)
                 : UpdatePredictedPosesForDynamicObject(tracked_object_proto);

  prediction_attributes_.clear();
  prediction_attributes_.reserve(
      predicted_trajectory_proto_.maneuver().attributes_size());
  for (const auto& attribute :
       predicted_trajectory_proto_.maneuver().attributes()) {
    prediction_attributes_.insert(
        static_cast<prediction::pb::Maneuver::Attribute>(attribute));
  }

  DCHECK_LT(0, predicted_trajectory_proto_.id())
      << "Trajectory id not populated. Trajectory id must be positive. "
         "Decoupled arch must re-simulate the prediction node.";
}

PredictedTrajectoryWrapper::PredictedTrajectoryWrapper(
    const pb::PredictedTrajectoryWrapper& predicted_trajectory_wrapper_proto)
    : PredictedTrajectoryWrapper(
          predicted_trajectory_wrapper_proto.tracked_object(),
          predicted_trajectory_wrapper_proto.predicted_trajectory(),
          predicted_trajectory_wrapper_proto.is_primary_trajectory(),
          predicted_trajectory_wrapper_proto.object_stationary_intention()) {}

// Simplify the computation for static objects.
void PredictedTrajectoryWrapper::UpdatePredictedPosesForStaticObject(
    const voy::TrackedObject& tracked_object_proto) {
  if (predicted_trajectory_proto_.traj_poses().empty()) {
    return;
  }

  const pb::TrajectoryPose& first_traj_pose_proto =
      predicted_trajectory_proto_.traj_poses(0);
  const pb::TrajectoryPose& last_traj_pose_proto =
      predicted_trajectory_proto_.traj_poses(
          predicted_trajectory_proto_.traj_poses_size() - 1);
  predicted_poses_.reserve(predicted_trajectory_proto_.traj_poses().size());
  for (int ii = 0; ii < predicted_trajectory_proto_.traj_poses().size(); ++ii) {
    const pb::TrajectoryPose& traj_pose_proto =
        ii < predicted_trajectory_proto_.traj_poses().size() * 0.5
            ? first_traj_pose_proto
            : last_traj_pose_proto;

    predicted_poses_.emplace_back(tracked_object_proto, traj_pose_proto,
                                  predicted_trajectory_proto_.id(),
                                  /*odom=*/0.0, /*is_stationary=*/true);
  }
}

void PredictedTrajectoryWrapper::UpdatePredictedPosesForDynamicObject(
    const voy::TrackedObject& tracked_object_proto) {
  double accumulated_odom = 0.0;
  predicted_poses_.reserve(predicted_trajectory_proto_.traj_poses().size());
  for (int ii = 0; ii < predicted_trajectory_proto_.traj_poses().size(); ++ii) {
    const pb::TrajectoryPose& traj_pose_proto =
        predicted_trajectory_proto_.traj_poses(ii);
    if (ii > 0) {
      accumulated_odom += math::FastHypot(
          traj_pose_proto.x_pos() -
              predicted_trajectory_proto_.traj_poses(ii - 1).x_pos(),
          traj_pose_proto.y_pos() -
              predicted_trajectory_proto_.traj_poses(ii - 1).y_pos());
    }

    predicted_poses_.emplace_back(tracked_object_proto, traj_pose_proto,
                                  predicted_trajectory_proto_.id(),
                                  accumulated_odom, /*is_stationary=*/false);
  }
}

bool PredictedTrajectoryWrapper::IsTurning() const {
  // If the agent was turning, the absolute difference between headings of start
  // and end poses of predicted trajectory would be large. Though it is small
  // at the end of turning, it's a meaningful signal to check whether a
  // predicted trajectory is turning.
  return IsTurningLeft() || IsTurningRight();
}

bool PredictedTrajectoryWrapper::IsTurningLeft() const {
  const double heading_diff = math::AngleDiff(
      predicted_poses().back().heading(), predicted_poses().front().heading());
  // If the agent was turning left, the difference between headings of end
  // and start poses of predicted trajectory would be large. Though it is small
  // at the end of turning, it's a meaningful signal to check whether a
  // predicted trajectory is turning.
  return heading_diff > kTurningTrajectoryMinHeadingDiffInRad;
}

bool PredictedTrajectoryWrapper::IsTurningRight() const {
  const double heading_diff = math::AngleDiff(
      predicted_poses().back().heading(), predicted_poses().front().heading());
  // If the agent was turning, the absolute difference between headings of end
  // and start poses of predicted trajectory would be negative. Though it is
  // small at the end of turning, it's a meaningful signal to check whether a
  // predicted trajectory is turning.
  return heading_diff < -kTurningTrajectoryMinHeadingDiffInRad;
}

bool PredictedTrajectoryWrapper::is_valid_query_timestamp(
    const int64_t query_timestamp) const {
  DCHECK_GE(size(), 0);
  return query_timestamp > pose(0).timestamp() &&
         query_timestamp < pose(size() - 1).timestamp();
}

planner::pb::TrajectoryPose PredictedTrajectoryWrapper::ComputePoseAtTimestamp(
    int64_t query_timestamp) const {
  DCHECK_GE(query_timestamp, pose(0).timestamp());
  DCHECK_LE(query_timestamp, pose(size() - 1).timestamp());
  // If the predicted trajectory is stationary, use the first pose with modified
  // timestamp.
  if (IsStationary()) {
    planner::pb::TrajectoryPose computed_pose(pose(0));
    computed_pose.set_timestamp(query_timestamp);
    return computed_pose;
  }

  // If one of the poses in the predicted trajectory is within 10 ms of
  // query_timestamp, use that pose without interpolation.
  for (int i = 0; i < size(); ++i) {
    const planner::pb::TrajectoryPose& traj_pose = pose(i);
    const int time_diff_ms = traj_pose.timestamp() - query_timestamp;
    if (time_diff_ms > kMaxTimeDiffToSkipInterpolationInMs) {
      break;
    }
    if (time_diff_ms >= -kMaxTimeDiffToSkipInterpolationInMs) {
      planner::pb::TrajectoryPose computed_pose(traj_pose);
      computed_pose.set_timestamp(query_timestamp);
      return computed_pose;
    }
  }

  // Note: since PredictedTrajectoryInterpolator is not copyable, this is a
  // workaround but not optimal solution. We should add Interpolator1d for
  // x,y, heading, speed, accel, and odom... to avoid creating a new
  // PredictedTrajectoryInterpolator every time we call this function.
  const PredictedTrajectoryInterpolator predicted_trajectory_interpolator(
      poses());
  const std::optional<pb::TrajectoryPose> interpolated_pose =
      predicted_trajectory_interpolator.GetInterpolatedPose2d(query_timestamp);
  DCHECK(interpolated_pose.has_value());
  planner::pb::TrajectoryPose computed_pose;
  computed_pose.set_timestamp(query_timestamp);
  computed_pose.set_x_pos(interpolated_pose->x_pos());
  computed_pose.set_y_pos(interpolated_pose->y_pos());
  computed_pose.set_heading(interpolated_pose->heading());
  computed_pose.set_speed(interpolated_pose->speed());
  computed_pose.set_accel(interpolated_pose->accel());
  return computed_pose;
}

double PredictedTrajectoryWrapper::GetTrajectoryMinimumSpeed() const {
  DCHECK(!poses().empty());
  const planner::pb::TrajectoryPose& min_speed_pose =
      *std::min_element(poses().begin(), poses().end(),
                        [](const planner::pb::TrajectoryPose& first_pose,
                           const planner::pb::TrajectoryPose& second_pose) {
                          return first_pose.speed() < second_pose.speed();
                        });
  return min_speed_pose.speed();
}

double PredictedTrajectoryWrapper::GetTrajectoryMaximumSpeed() const {
  DCHECK(!poses().empty());
  const planner::pb::TrajectoryPose& max_speed_pose =
      *std::max_element(poses().begin(), poses().end(),
                        [](const planner::pb::TrajectoryPose& first_pose,
                           const planner::pb::TrajectoryPose& second_pose) {
                          return first_pose.speed() < second_pose.speed();
                        });
  return max_speed_pose.speed();
}

double PredictedTrajectoryWrapper::GetTrajectoryAverageSpeed() const {
  DCHECK(!poses().empty());
  const double accumulate_speed =
      std::accumulate(poses().begin(), poses().end(), /*init=*/0.0,
                      [](double sum, const planner::pb::TrajectoryPose& pose) {
                        return sum + pose.speed();
                      });
  return accumulate_speed / poses().size();
}

pb::PredictedTrajectoryWrapper PredictedTrajectoryWrapper::ToProto() const {
  pb::PredictedTrajectoryWrapper predicted_trajectory_wrapper_proto;

  predicted_trajectory_wrapper_proto.mutable_tracked_object()->CopyFrom(
      tracked_object());
  predicted_trajectory_wrapper_proto.mutable_predicted_trajectory()->CopyFrom(
      predicted_trajectory_proto());
  predicted_trajectory_wrapper_proto.set_is_primary_trajectory(
      is_primary_trajectory());
  predicted_trajectory_wrapper_proto.mutable_object_stationary_intention()
      ->CopyFrom(StationaryIntention());
  return predicted_trajectory_wrapper_proto;
}

void PredictedTrajectoryWrapper::UpdateLegacyPredictedPoses(
    const std::optional<int64_t>& max_interested_prediction_timestamp) {
  TRACE_EVENT_SCOPE(planner,
                    PredictedTrajectoryWrapper_UpdateLegacyPredictedPoses);

  if (!legacy_predicted_poses_.empty()) {
    // If legacy predicted poses are already computed, no need to recompute.
    return;
  }

  bool is_primary_stationary =
      object_stationary_intention_.stationary_intention_type() ==
      prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE;

  if (is_primary_stationary) {
    GetPredictedPosesForStaticObject(tracked_object_proto_,
                                     predicted_trajectory_proto_,
                                     &legacy_predicted_poses_);
  } else {
    GetPredictedPosesForMovingObject(
        tracked_object_proto_, predicted_trajectory_proto_,
        is_primary_stationary, max_interested_prediction_timestamp,
        &legacy_predicted_poses_);
  }
}

std::optional<math::geometry::OrientedBox2d>
PredictedTrajectoryWrapper::GetInterpolatedBoundingBox(
    double query_timestamp, bool use_legacy_logic) const {
  const PredictedTrajectoryInterpolator predicted_trajectory_interpolator(
      object_id(), predicted_poses(use_legacy_logic));
  return predicted_trajectory_interpolator.GetInterpolatedBoundingBox(
      query_timestamp);
}
}  // namespace planner
