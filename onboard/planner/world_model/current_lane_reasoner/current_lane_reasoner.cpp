#include "planner/world_model/current_lane_reasoner/current_lane_reasoner.h"

#include <algorithm>
#include <limits>
#include <set>

#include "planner/behavior/util/lane_sequence/lane_sequence_generator_utility2.h"
#include "planner/constants.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner_utility.h"

namespace planner {
namespace {
// Calculates near lane candidate.
lane_candidate::NearLaneCandidate CalculateNearLaneCandidate(
    const pnc_map::Lane* lane,
    const DrivableLaneReasoner* drivable_lane_reasoner,
    const math::geometry::Point2d& rear_axle_center,
    const RobotStateSnapshot& robot_state_snapshot,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Section*>&
        last_selected_section_sequence) {
  CHECK(lane != nullptr);

  const auto proximity = lane->center_line().GetProximity(
      rear_axle_center, math::pb::UseExtensionFlag::kForbid);
  const double lane_heading =
      lane->center_line().GetInterpTheta(proximity.arc_length);
  // Lane heading is in [-M_PI, M_PI], robot state heading is in [0, 2 *
  // M_PI]. The heading diff should be in [0, M_PI].
  const double heading_diff = std::abs(math::AngleDiff(
      math::WrapFromMinusPiToPi(robot_state_snapshot.heading()), lane_heading));
  const double position_to_lane_center_dist_in_m = proximity.dist;
  const double position_arclength_from_lane_start = proximity.arc_length;
  CHECK_GT(lane->length(), 0.0);
  const double position_percentage_along_lane =
      position_arclength_from_lane_start / lane->length();
  const bool is_drivable = drivable_lane_reasoner->IsLaneRobotDrivable(
      lane, /*check_local_hold=*/true,
      /*check_edge_filter=*/false);
  const bool is_overlapped = math::geometry::Intersects(
      robot_state_snapshot.bounding_box(), lane->border());
  const bool is_physical =
      is_overlapped ? math::geometry::Within(rear_axle_center, lane->border())
                    : false;
  const bool is_in_cost_map =
      global_route_solution.GetCostToDestination(
          *lane, global_route_solution.waypoint_count()) != nullptr;
  const bool is_selected_by_locator_near_lanes = std::any_of(
      locator_selected_near_lanes.begin(), locator_selected_near_lanes.end(),
      [lane](const pnc_map::Lane* selected_lane) {
        return lane->id() == selected_lane->id();
      });
  const int64_t section_id = lane->section()->id();
  const bool is_in_regional_section_vertices = std::any_of(
      regional_section_vertices.begin(), regional_section_vertices.end(),
      [section_id](const pnc_map::SectionVertex& section_vertex) {
        return section_id == section_vertex.section->id();
      });
  const bool is_in_last_selected_lane_sequence = std::any_of(
      last_selected_lane_sequence.begin(), last_selected_lane_sequence.end(),
      [lane](const pnc_map::Lane* selected_lane) {
        return lane->id() == selected_lane->id();
      });
  const bool is_in_last_selected_section_sequence =
      std::any_of(last_selected_section_sequence.begin(),
                  last_selected_section_sequence.end(),
                  [section_id](const pnc_map::Section* selected_section) {
                    return section_id == selected_section->id();
                  });
  // TODO(yuzehao): |is_in_locator_potential_sections|,
  // |is_in_extend_potential_sections| and |is_in_last_target_lane_sequence|
  // will be set when we begin to use current lane reasoner instead of
  // associator.
  return lane_candidate::NearLaneCandidate(
      lane, heading_diff, position_to_lane_center_dist_in_m,
      position_arclength_from_lane_start, position_percentage_along_lane,
      is_drivable, is_physical, is_overlapped, is_in_cost_map,
      is_selected_by_locator_near_lanes,
      /*is_in_locator_potential_sections=*/false,
      /*is_in_extend_potential_sections=*/false,
      is_in_regional_section_vertices, is_in_last_selected_lane_sequence,
      /*is_in_last_target_lane_sequence=*/false,
      is_in_last_selected_section_sequence);
}

// Adds near lane candidate if not exists. Return true if add successfully.
bool AddNearLaneCandidate(
    const lane_candidate::NearLaneCandidate* near_lane_candidate,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        near_lane_candidates) {
  if (near_lane_candidate == nullptr) {
    return false;
  }

  const bool is_exist = std::any_of(
      near_lane_candidates->begin(), near_lane_candidates->end(),
      [near_lane_candidate](
          const lane_candidate::NearLaneCandidate* candidate) {
        return near_lane_candidate->lane()->id() == candidate->lane()->id();
      });

  if (!is_exist) {
    near_lane_candidates->push_back(near_lane_candidate);
    return true;
  }

  return false;
}

// Adds near lane candidate from pool. Return true if find the lane in pool.
bool AddNearLaneCandidateFromPool(
    const pnc_map::Lane* lane,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        near_lane_candidates) {
  if (lane == nullptr) {
    return false;
  }

  const auto it = std::find_if(
      near_lane_candidates_pool.begin(), near_lane_candidates_pool.end(),
      [lane](const lane_candidate::NearLaneCandidate* candidate) {
        return lane->id() == candidate->lane()->id();
      });
  if (it == near_lane_candidates_pool.end()) {
    return false;
  }

  AddNearLaneCandidate(*it, near_lane_candidates);
  return true;
}

// Gets the closest near lane candidate. May return nullptr if the
// |near_lane_candidates| is empty.
const lane_candidate::NearLaneCandidate* GetClosestNearLaneCandidate(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates) {
  const lane_candidate::NearLaneCandidate* closest_candidate = nullptr;

  if (near_lane_candidates.empty()) {
    return closest_candidate;
  }

  for (const lane_candidate::NearLaneCandidate* candidate :
       near_lane_candidates) {
    if (candidate == nullptr) {
      continue;
    }
    if (closest_candidate == nullptr ||
        candidate->position_to_lane_center_dist_in_m() <
            closest_candidate->position_to_lane_center_dist_in_m()) {
      closest_candidate = candidate;
    }
  }

  return closest_candidate;
}

// Gets proposal near lane candidates pool.
std::vector<const lane_candidate::NearLaneCandidate*>
GetProposalNearLaneCandidatesPool(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const std::vector<lane_candidate::NearLaneCandidate>&
        all_near_lane_candidates) {
  std::vector<const lane_candidate::NearLaneCandidate*>
      proposal_near_lane_candidates_pool;
  proposal_near_lane_candidates_pool.reserve(all_near_lane_candidates.size() +
                                             1);

  for (const lane_candidate::NearLaneCandidate* normal_near_lane_candidate :
       normal_near_lane_candidates) {
    // Indicate that we can find the candidate from the same section with
    // |normal_near_lane_candidate| in |all_near_lane_candidates|.
    bool found_match = false;
    for (const lane_candidate::NearLaneCandidate& candidate :
         all_near_lane_candidates) {
      if (candidate.lane()->section()->id() !=
              normal_near_lane_candidate->lane()->section()->id() ||
          !candidate.is_drivable()) {
        continue;
      }
      AddNearLaneCandidate(&candidate, &proposal_near_lane_candidates_pool);
      found_match = true;
    }

    // Make sure that |normal_near_lane_candidate| is in the proposal candidates
    // pool.
    if (!found_match) {
      AddNearLaneCandidate(normal_near_lane_candidate,
                           &proposal_near_lane_candidates_pool);
    }
  }

  return proposal_near_lane_candidates_pool;
}

// Returns true if ego has entered pull out jump out sequence.
bool HasEnteredPullOutJumpOutSequence(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const ::google::protobuf::RepeatedField<int64_t>&
        last_jump_out_lane_sequence) {
  if (physical_lanes.empty()) {
    return false;
  }

  for (const pnc_map::Lane* physical_lane : physical_lanes) {
    if (std::any_of(last_jump_out_lane_sequence.begin(),
                    last_jump_out_lane_sequence.end(),
                    [&physical_lane](const int64_t lane_id) {
                      return physical_lane->id() == lane_id;
                    })) {
      return true;
    }
  }

  return false;
}

// Returns true if ego is in solid lane marking range before a junction.
// |dist_to_solid_lane_marking_before_junction| is the distance from current
// lane's start to the solid lane marking. |ego_arc_length| is the ego's
// arc_length in current lane.
bool IsInSolidLaneMarkingRangeBeforeJunction(
    const pnc_map::Lane* lane,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        lane_to_junction_info_map,
    double ego_arc_length, bool to_left) {
  if (!lane || lane_to_junction_info_map.empty()) {
    return false;
  }

  const auto it = lane_to_junction_info_map.find(lane->id());
  if (it == lane_to_junction_info_map.end()) {
    return false;
  }

  const double dist_to_solid_lane_marking_before_junction =
      to_left ? it->second.dist_to_left_solid_lane_marking_before_junction()
              : it->second.dist_to_right_solid_lane_marking_before_junction();
  if (dist_to_solid_lane_marking_before_junction ==
      std::numeric_limits<double>::max()) {
    return false;
  }

  return dist_to_solid_lane_marking_before_junction <= ego_arc_length;
}

// Returns true if ego has entered jump out sequence or we should not generate
// jump out sequence any more in this trip.
bool ShouldTerminatePullOutJumpOutSequence(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const GlobalRouteSolution& global_route_solution,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    const pb::PullOutJumpOutSeed& pull_out_jump_out_seed, bool is_new_trip,
    bool is_selecting_pull_out_jump_out_sequence,
    bool should_generate_pull_out_jump_out_sequence) {
  const ::google::protobuf::RepeatedField<int64_t>&
      last_jump_out_lane_sequence =
          pull_out_jump_out_seed.last_jump_out_lane_sequence();
  // Ego has entered pull out jump out sequence in this cycle.
  if (HasEnteredPullOutJumpOutSequence(physical_lanes,
                                       last_jump_out_lane_sequence)) {
    return true;
  }

  // Ego's position is not ready for pull out. We should make sure that ego has
  // valid start position because planning node may not receive new route
  // solution for a new trip.
  if (!should_generate_pull_out_jump_out_sequence &&
      global_route_solution.IsEgoStartFromValidPosition()) {
    return true;
  }

  // Ego does not select the jump out sequence and is in the solid lane marking
  // range. This is to avoid ego crossing solid lane marking using pull out jump
  // out sequence before a junction. Note that if it is a new trip, we should
  // not check the last lane to junction info map.
  if (!is_new_trip && !is_selecting_pull_out_jump_out_sequence &&
      std::any_of(
          normal_near_lane_candidates.begin(),
          normal_near_lane_candidates.end(),
          [&last_jump_out_lane_sequence, &last_lane_to_junction_info_map](
              const lane_candidate::NearLaneCandidate* candidate) {
            // The |normal_near_lane_candidates| may include the JO current
            // lane.
            return std::none_of(last_jump_out_lane_sequence.begin(),
                                last_jump_out_lane_sequence.end(),
                                [candidate](const int64_t lane_id) {
                                  return lane_id == candidate->lane()->id();
                                }) &&
                   IsInSolidLaneMarkingRangeBeforeJunction(
                       candidate->lane(), last_lane_to_junction_info_map,
                       candidate->position_arclength_from_lane_start(),
                       /*to_left=*/true);
          })) {
    return true;
  }

  return false;
}

// Returns true if we should add pull out jump out lanes. And we decide to
// terminate generation of pull out jump out sequence.
bool ShouldAddPullOutJumpOutLanes(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const GlobalRouteSolution& global_route_solution,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    bool is_new_trip, bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed) {
  // Set has_entered_jump_out_sequence to true when we should terminate
  // generation of pull out jump out sequence.
  // TODO(Huoliang): Update PullOutJumpOutSeed at the end of planning cycle.
  if (!mutable_pull_out_jump_out_seed->has_entered_jump_out_sequence() &&
      ShouldTerminatePullOutJumpOutSequence(
          physical_lanes, normal_near_lane_candidates, global_route_solution,
          last_lane_to_junction_info_map, *mutable_pull_out_jump_out_seed,
          is_new_trip,
          lane_selection::IsInPullOutJumpOut(
              last_selected_lane_sequence_candidate),
          should_generate_pull_out_jump_out_sequence)) {
    mutable_pull_out_jump_out_seed->set_has_entered_jump_out_sequence(true);
  }

  // If physical_lanes is empty, it means that ego starts from non-drivable
  // lanes and we should not add any jump out current lanes.
  // Note(Huoliang): Here we also check last cycle's pull out JO current lanes
  // are empty because ego may come across empty area even if it starts from
  // drivable lane, such as some bus bulbs.
  if (physical_lanes.empty() &&
      mutable_pull_out_jump_out_seed->last_jump_out_current_lanes().empty()) {
    return false;
  }

  // Note(Huoliang): |should_generate_pull_out_jump_out_sequence| is the flag
  // in world model to tell that ego position meet the requirement in a trip
  // to generate pull out jump out sequence. |has_entered_jump_out_sequence|
  // is the flag to tell if ego has entered the JO sequence. When updating
  // current lanes, we know that ego has entered JO sequence before regional
  // path and lane sequence, and we should not add JO current lane in this
  // cycle. And for old bags, which do not have valid ride start pose, the
  // |has_entered_jump_out_sequence| will not have the chance to be true from
  // false.
  return should_generate_pull_out_jump_out_sequence &&
         !mutable_pull_out_jump_out_seed->has_entered_jump_out_sequence();
}

// Returns true if we should add stuck avoidance jump out lanes.
bool ShouldAddStuckAvoidanceJumpOutLanes(
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    bool should_generate_pull_out_jump_out_sequence,
    bool has_entered_pull_out_jump_out_sequence) {
  // Return false if ego is in pull out stage.
  if (should_generate_pull_out_jump_out_sequence &&
      !has_entered_pull_out_jump_out_sequence) {
    return false;
  }

  // Return false if ego is not in stuck avoidance mode.
  if (stuck_avoidance_param.stuck_lane_segments.empty() &&
      stuck_avoidance_param.jump_out_param.staged_relax_lane_segments_sequence()
          .empty()) {
    return false;
  }

  // Return false if we do not need to add stuck avoidance lane sequence.
  if (!stuck_avoidance_param.jump_out_param.use_jump_out_sequence()) {
    return false;
  }

  // Return false if we are waiting for confirming the scenario and do not
  // decide to execute for now.
  if (!stuck_avoidance_param.should_execute) {
    return false;
  }

  return true;
}

// Selects pull out jump out lane from |section|. Most of time we find the
// second drivable lane on the right. But if this lane is fork or merge lane and
// ego's pose is in it, we find another. We use the function often in the first
// cycle of pull out.
const pnc_map::Lane* SelectPullOutJumpOutLaneFromSection(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const pnc_map::Section* section,
    const DrivableLaneReasoner* drivable_lane_reasoner) {
  const pnc_map::Lane* rightmost_drivable_lane =
      lane_selection::GetRightMostDrivableLane(section, drivable_lane_reasoner);
  if (rightmost_drivable_lane == nullptr) {
    return nullptr;
  }

  // Is ego's pose is not in rightmost drivable lane, we do not need to add jump
  // out lane.
  if (!lane_selection::IsPhysicalLane(rightmost_drivable_lane,
                                      physical_lanes)) {
    return nullptr;
  }

  return lane_selection::GetNonPhysicalRightMostDrivableLane(
      physical_lanes, section, drivable_lane_reasoner);
}

// Selects pull out jump out lanes from |near_lane_candidates_pool|.
std::vector<const lane_candidate::NearLaneCandidate*>
SelectPullOutJumpOutCandidates(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    const DrivableLaneReasoner* drivable_lane_reasoner,
    const ::google::protobuf::RepeatedField<int64_t>&
        last_jump_out_lane_sequence) {
  std::vector<const lane_candidate::NearLaneCandidate*>
      pull_out_jump_out_candidates;
  pull_out_jump_out_candidates.reserve(near_lane_candidates_pool.size());

  // Find jump out lanes from last jump out lane sequence and make sure it is
  // the left lane of physical lane.
  if (!last_jump_out_lane_sequence.empty()) {
    for (const lane_candidate::NearLaneCandidate* candidate :
         near_lane_candidates_pool) {
      const int64_t lane_id = candidate->lane()->id();
      if (std::any_of(last_jump_out_lane_sequence.begin(),
                      last_jump_out_lane_sequence.end(),
                      [lane_id](const int64_t sequence_lane_id) {
                        return sequence_lane_id == lane_id;
                      }) &&
          std::any_of(physical_lanes.begin(), physical_lanes.end(),
                      [lane_id](const pnc_map::Lane* physical_lane) {
                        return physical_lane->left_lane() != nullptr &&
                               physical_lane->left_lane()->id() == lane_id;
                      })) {
        pull_out_jump_out_candidates.push_back(candidate);
      }
    }
  }

  // Find current lanes from section if last_jump_out_lane_sequence is empty or
  // we can not find jump out lane from it.
  if (pull_out_jump_out_candidates.empty()) {
    const std::vector<const pnc_map::Section*> physical_sections =
        GetSectionsFromLanes(physical_lanes, /*prevent_loop=*/false);
    for (const pnc_map::Section* physical_section : physical_sections) {
      const pnc_map::Lane* jump_out_lane_from_section =
          SelectPullOutJumpOutLaneFromSection(physical_lanes, physical_section,
                                              drivable_lane_reasoner);
      AddNearLaneCandidateFromPool(jump_out_lane_from_section,
                                   near_lane_candidates_pool,
                                   &pull_out_jump_out_candidates);
    }
  }

  return pull_out_jump_out_candidates;
}

// Returns the directed relax lane segments where ego is in.
lane_selection::DirectedRelaxLaneSegments GetDirectedRelaxLaneSegments(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        overlapped_near_lane_candidates,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param) {
  lane_selection::DirectedRelaxLaneSegments directed_relax_lane_segments;

  const auto& jump_out_param = stuck_avoidance_param.jump_out_param;
#pragma GCC diagnostic push
  // relax_direction is deprecated.
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  if (jump_out_param.relax_direction() !=
      hdmap::RelaxDirection::kInvalidDirection) {
    // It commonly happens in simulation for an old bag.
    common::ConvertPBToLaneSegments(
        joint_pnc_map_service, stuck_avoidance_param.stuck_lane_segments,
        &directed_relax_lane_segments.lane_segments);
    directed_relax_lane_segments.relax_direction =
        jump_out_param.relax_direction();
    return directed_relax_lane_segments;
  }
#pragma GCC diagnostic pop

  const auto& staged_relax_lane_segments_sequence =
      jump_out_param.staged_relax_lane_segments_sequence();
  for (const auto& directed_segments : staged_relax_lane_segments_sequence) {
    std::vector<common::LaneSegment> lane_segments;
    common::ConvertPBToLaneSegments(joint_pnc_map_service,
                                    directed_segments.lane_segments(),
                                    &lane_segments);
    bool is_in_lane_segment = false;
    for (const auto& segment : lane_segments) {
      if (std::any_of(overlapped_near_lane_candidates.begin(),
                      overlapped_near_lane_candidates.end(),
                      [&segment](const lane_candidate::NearLaneCandidate*
                                     overlapped_candidate) {
                        return segment.lane->id() ==
                                   overlapped_candidate->lane()->id() &&
                               math::IsInRange(
                                   overlapped_candidate
                                       ->position_arclength_from_lane_start(),
                                   segment.arc_length_range_m.start_pos(),
                                   segment.arc_length_range_m.end_pos());
                      })) {
        is_in_lane_segment = true;
        break;
      }
    }

    if (is_in_lane_segment) {
      // If ego is in the lane segments of one stage, we will only jump
      // out from the lane segments to the specific direction and ignore
      // other stages.
      directed_relax_lane_segments.lane_segments = lane_segments;
      directed_relax_lane_segments.relax_direction =
          directed_segments.relax_direction();
      return directed_relax_lane_segments;
    }
  }

  return directed_relax_lane_segments;
}

// Selects stuck avoidance jump out lane from section.
const pnc_map::Lane* SelectStuckAvoidanceJumpOutLaneFromSection(
    const pnc_map::Lane* stuck_lane,
    const std::vector<const pnc_map::Lane*>& physical_lanes, bool to_left) {
  const pnc_map::Lane* jump_out_lane = to_left
                                           ? stuck_lane->adjacent_left_lane()
                                           : stuck_lane->adjacent_right_lane();
  if (jump_out_lane == nullptr) {
    const pnc_map::Lane* left_or_right_lane =
        to_left ? stuck_lane->left_lane() : stuck_lane->right_lane();
    if (left_or_right_lane == nullptr) {
      return nullptr;
    }

    if (stuck_lane->IsForkLane() &&
        stuck_lane->IsBrother(
            *left_or_right_lane,
            /*relation_type=*/pnc_map::BrotherLane::RelationType::kDiverge,
            /*is_drivable_only=*/true)) {
      jump_out_lane = left_or_right_lane;
    }
  }

  if (jump_out_lane == nullptr ||
      lane_selection::IsPhysicalLane(jump_out_lane, physical_lanes)) {
    return nullptr;
  }

  return jump_out_lane;
}

// Selects stuck avoidance jump out lanes from |near_lane_candidates_pool|.
std::vector<const lane_candidate::NearLaneCandidate*>
SelectStuckAvoidanceJumpOutCandidates(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        overlapped_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed) {
  std::vector<const lane_candidate::NearLaneCandidate*>
      stuck_avoidance_jump_out_candidates;
  stuck_avoidance_jump_out_candidates.reserve(near_lane_candidates_pool.size());

  const lane_selection::DirectedRelaxLaneSegments directed_relax_lane_segments =
      GetDirectedRelaxLaneSegments(overlapped_near_lane_candidates,
                                   joint_pnc_map_service,
                                   stuck_avoidance_param);
  if (directed_relax_lane_segments.relax_direction ==
      hdmap::RelaxDirection::kInvalidDirection) {
    return stuck_avoidance_jump_out_candidates;
  }

  const std::vector<common::LaneSegment>& stuck_lane_segments =
      directed_relax_lane_segments.lane_segments;
  const auto& blocked_marking_segments =
      lane_blockage_detector_seed.blocked_lane_marking_seed();
  // TODO(Huoliang): Move the hb_check & lm_check to eval module. There is no
  // need to individually check for stuck avoidance jump out lane.
  const bool need_left =
      (directed_relax_lane_segments.relax_direction ==
           hdmap::RelaxDirection::kToLeft ||
       directed_relax_lane_segments.relax_direction ==
           hdmap::RelaxDirection::kToBoth) &&
      !lane_selection::HasHardBoundaryInSegmentSequence(joint_pnc_map_service,
                                                        stuck_lane_segments,
                                                        /*to_left=*/true) &&
      !common::HasBlockedLaneMarkingInSegmentSequence(
          blocked_marking_segments, stuck_lane_segments, /*to_left=*/true);
  const bool need_right =
      (directed_relax_lane_segments.relax_direction ==
           hdmap::RelaxDirection::kToRight ||
       directed_relax_lane_segments.relax_direction ==
           hdmap::RelaxDirection::kToBoth) &&
      !lane_selection::HasHardBoundaryInSegmentSequence(joint_pnc_map_service,
                                                        stuck_lane_segments,
                                                        /*to_left=*/false) &&
      !common::HasBlockedLaneMarkingInSegmentSequence(
          blocked_marking_segments, stuck_lane_segments, /*to_left=*/false);

  std::set<int64_t> visited_lane_ids;
  for (const auto& stuck_lane_segment : stuck_lane_segments) {
    const pnc_map::Lane* stuck_lane = stuck_lane_segment.lane;
    if (stuck_lane == nullptr) {
      continue;
    }

    const int64_t lane_id = stuck_lane->id();
    if (!visited_lane_ids.insert(lane_id).second) {
      continue;
    }

    if (std::none_of(
            normal_near_lane_candidates.begin(),
            normal_near_lane_candidates.end(),
            [lane_id](const lane_candidate::NearLaneCandidate* candidate) {
              return candidate->lane()->id() == lane_id;
            })) {
      continue;
    }

    // Find jump out lane from current section.
    if (need_left) {
      const pnc_map::Lane* left_jump_out_lane =
          SelectStuckAvoidanceJumpOutLaneFromSection(stuck_lane, physical_lanes,
                                                     /*to_left=*/true);
      AddNearLaneCandidateFromPool(left_jump_out_lane,
                                   near_lane_candidates_pool,
                                   &stuck_avoidance_jump_out_candidates);
    }
    if (need_right) {
      const pnc_map::Lane* right_jump_out_lane =
          SelectStuckAvoidanceJumpOutLaneFromSection(stuck_lane, physical_lanes,
                                                     /*to_left=*/false);
      AddNearLaneCandidateFromPool(right_jump_out_lane,
                                   near_lane_candidates_pool,
                                   &stuck_avoidance_jump_out_candidates);
    }
    // TODO(Huoliang): Move the logic to find jump out lane from left section.
    // The lane may be not in current pool.
  }

  return stuck_avoidance_jump_out_candidates;
}

// Adds prefix near lane candidates and updates lane prefix info map.
void AddPrefixNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates,
    const lane_candidate::PrefixInfo& prefix_info,
    std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>*
        lane_prefix_info_map,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        proposal_near_lane_candidates) {
  for (const lane_candidate::NearLaneCandidate* candidate :
       near_lane_candidates) {
    AddNearLaneCandidate(candidate, proposal_near_lane_candidates);
    const int64_t lane_id = candidate->lane()->id();
    const auto it = lane_prefix_info_map->find(lane_id);
    if (it == lane_prefix_info_map->end()) {
      lane_prefix_info_map->emplace(
          lane_id, std::vector<lane_candidate::PrefixInfo>({prefix_info}));
      continue;
    }

    std::vector<lane_candidate::PrefixInfo>& current_prefix_infos = it->second;
    if (std::none_of(
            current_prefix_infos.begin(), current_prefix_infos.end(),
            [&prefix_info](
                const lane_candidate::PrefixInfo& current_prefix_info) {
              return prefix_info.id == current_prefix_info.id &&
                     prefix_info.source == current_prefix_info.source;
            })) {
      current_prefix_infos.push_back(prefix_info);
    }
  }
}

// Adds pull out or stuck avoidance jump out near lane candidates to
// |proposal_near_lane_candidates|.
void AddJumpOutNearLaneCandidates(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        overlapped_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner* drivable_lane_reasoner,
    const GlobalRouteSolution& global_route_solution,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    bool is_new_trip, bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed,
    std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>*
        lane_prefix_info_map,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        proposal_near_lane_candidates) {
  if (ShouldAddPullOutJumpOutLanes(
          physical_lanes, normal_near_lane_candidates, global_route_solution,
          last_selected_lane_sequence_candidate, last_lane_to_junction_info_map,
          is_new_trip, should_generate_pull_out_jump_out_sequence,
          mutable_pull_out_jump_out_seed)) {
    const std::vector<const lane_candidate::NearLaneCandidate*>
        pull_out_jump_out_candidates = SelectPullOutJumpOutCandidates(
            physical_lanes, near_lane_candidates_pool, drivable_lane_reasoner,
            mutable_pull_out_jump_out_seed->last_jump_out_lane_sequence());
    AddPrefixNearLaneCandidates(
        pull_out_jump_out_candidates,
        lane_candidate::PrefixInfo(pb::LaneSequencePrefix::LK_PULLOUT_JO),
        lane_prefix_info_map, proposal_near_lane_candidates);
  }

  if (ShouldAddStuckAvoidanceJumpOutLanes(
          stuck_avoidance_param, should_generate_pull_out_jump_out_sequence,
          mutable_pull_out_jump_out_seed->has_entered_jump_out_sequence())) {
    const std::vector<const lane_candidate::NearLaneCandidate*>
        stuck_avoidance_jump_out_candidates =
            SelectStuckAvoidanceJumpOutCandidates(
                physical_lanes, normal_near_lane_candidates,
                overlapped_near_lane_candidates, near_lane_candidates_pool,
                joint_pnc_map_service, stuck_avoidance_param,
                lane_blockage_detector_seed);
    AddPrefixNearLaneCandidates(
        stuck_avoidance_jump_out_candidates,
        lane_candidate::PrefixInfo(
            pb::LaneSequencePrefix::LK_STUCK_AVOIDANCE_JO),
        lane_prefix_info_map, proposal_near_lane_candidates);
  }
}

// Adds fork near lane candidates to |proposal_near_lane_candidates|.
void AddForkNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        overlapped_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>*
        lane_prefix_info_map,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        proposal_near_lane_candidates) {
  std::vector<const lane_candidate::NearLaneCandidate*>
      fork_near_lane_candidates;
  fork_near_lane_candidates.reserve(near_lane_candidates_pool.size());

  // We only add overlapped near lane candidates in fork.
  for (const lane_candidate::NearLaneCandidate* overlapped_candidate :
       overlapped_near_lane_candidates) {
    if (!overlapped_candidate->is_on_fork()) {
      continue;
    }
    AddNearLaneCandidateFromPool(overlapped_candidate->lane(),
                                 near_lane_candidates_pool,
                                 &fork_near_lane_candidates);
  }

  AddPrefixNearLaneCandidates(
      fork_near_lane_candidates,
      lane_candidate::PrefixInfo(pb::LaneSequencePrefix::LK_FORK),
      lane_prefix_info_map, proposal_near_lane_candidates);
}

// Adds proposal near lane candidates.
void AddProposalNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates_pool,
    const lane_selection::LaneSequenceProposalInfo& lane_sequence_proposal_info,
    std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>*
        lane_prefix_info_map,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        proposal_near_lane_candidates) {
  const std::vector<lane_selection::LaneSequencePrefixInfo>
      lane_sequence_prefix_infos = GetAllLaneSequencePrefixes(
          lane_sequence_proposal_info.lane_seq_latch_signal,
          FLAGS_planning_enable_lane_sequence_proposal_for_lane_change
              ? lane_sequence_proposal_info.lane_change_latch_signal
              : std::nullopt);

  std::vector<const lane_candidate::NearLaneCandidate*> matched_candidates;
  matched_candidates.reserve(near_lane_candidates_pool.size());
  for (const lane_selection::LaneSequencePrefixInfo& prefix :
       lane_sequence_prefix_infos) {
    matched_candidates.clear();
    for (const lane_candidate::NearLaneCandidate* candidate :
         near_lane_candidates_pool) {
      if (!FindLatchedSegmentIndexForLane(prefix.latched_segments,
                                          candidate->lane()->id())) {
        continue;
      }
      AddNearLaneCandidate(candidate, &matched_candidates);
    }
    if (matched_candidates.empty()) {
      continue;
    }
    const lane_candidate::NearLaneCandidate* closest_candidate_ptr =
        GetClosestNearLaneCandidate(matched_candidates);
    if (closest_candidate_ptr != nullptr) {
      AddPrefixNearLaneCandidates(
          {closest_candidate_ptr},
          lane_candidate::PrefixInfo(prefix.id, prefix.source),
          lane_prefix_info_map, proposal_near_lane_candidates);
    }
  }
}

// Returns true if any hard boundary is between the given lanes at corresponding
// arc length
// Note(Huoliang): This method is only meaningful for nearby lanes to check if
// there is any hard boundary between them. In current lane associator, the
// given lanes should be physical lane and logic lane.
bool HasHardBoundaryBetweenNearbyLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane* lane_a, const pnc_map::Lane* lane_b,
    double arc_length_a, double arc_length_b) {
  CHECK(lane_a);
  CHECK(lane_b);
  const int64_t section_id_a = lane_a->section()->id();
  const int64_t section_id_b = lane_b->section()->id();
  // Return false if the lanes in the same section and there is no middle hard
  // boundary in the road.
  if (section_id_a == section_id_b) {
    const auto road = joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Road>(
        lane_a->section()->road()->id(),
        pnc_map::ElementQueryType::kOnlineFirst);
    if (!road->online() &&
        road->middle_boundary().hard_boundary_lines.empty()) {
      // Road has no dynamic hard boundaries and no middle_boundary.
      // There will be no hard boundary between lanes in the same section.
      return false;
    }
  }

  // Return false if the lanes are in the successive sections.
  if (lane_a->section()->IsSuccessor(section_id_b) ||
      lane_b->section()->IsSuccessor(section_id_a)) {
    return false;
  }

  // Return true if any hard boundary line:
  // 1. has valid range at |hard_boundary_arc_length| and
  // 2. the point in center line at |center_line_arc_length| can project to it
  // with the side same as |check_left_for_hard_boundary|.
  auto is_different_side =
      [](const math::geometry::PolylineCurve2d& center_line,
         const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos,
         double center_line_arc_length, double hard_boundary_arc_length,
         bool check_left_for_hard_boundary) {
        const math::geometry::Point2d& center_line_point =
            center_line.GetInterp(center_line_arc_length);
        return std::any_of(
            hard_boundary_infos.begin(), hard_boundary_infos.end(),
            [&center_line_point, hard_boundary_arc_length,
             check_left_for_hard_boundary](
                const pnc_map::HardBoundaryInfo& hard_boundary_info) {
              if (!math::IsInRange(
                      hard_boundary_arc_length,
                      hard_boundary_info.range_on_center_line.start_pos,
                      hard_boundary_info.range_on_center_line.end_pos)) {
                return false;
              }
              const math::ProximityQueryInfo proximity =
                  hard_boundary_info.hard_boundary_line->GetProximity(
                      center_line_point, math::pb::UseExtensionFlag::kForbid);
              // The hard boundary is beyond the position of
              // |center_line_point|.
              if (proximity.relative_position !=
                  math::RelativePosition::kWithIn) {
                return false;
              }
              // This should not happen because lane's center line will not be
              // on a hard boundary.
              if (proximity.side == math::pb::kOn) {
                return true;
              }
              return check_left_for_hard_boundary
                         ? proximity.side == math::pb::kLeft
                         : proximity.side == math::pb::kRight;
            });
      };

  // Return true if the lanes are separated by hard boundary.
  auto is_separated_by_hard_boundary =
      [&is_different_side, arc_length_a, arc_length_b](
          const pnc_map::Lane* lane_a_for_hard_boundary,
          const pnc_map::Lane* lane_b_for_hard_boundary, bool check_left_a) {
        const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos_a =
            check_left_a ? lane_a_for_hard_boundary->left_hard_boundary_info()
                         : lane_a_for_hard_boundary->right_hard_boundary_info();
        const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos_b =
            check_left_a ? lane_b_for_hard_boundary->right_hard_boundary_info()
                         : lane_b_for_hard_boundary->left_hard_boundary_info();
        if (hard_boundary_infos_a.empty() || hard_boundary_infos_b.empty()) {
          return false;
        }

        const bool has_valid_hard_boundary_id =
            hard_boundary_infos_a.front().has_valid_hard_boundary_id();
        if (!has_valid_hard_boundary_id) {
          return is_different_side(lane_b_for_hard_boundary->center_line(),
                                   hard_boundary_infos_a, arc_length_b,
                                   arc_length_a, check_left_a) ||
                 is_different_side(lane_a_for_hard_boundary->center_line(),
                                   hard_boundary_infos_b, arc_length_a,
                                   arc_length_b, !check_left_a);
        }

        for (const pnc_map::HardBoundaryInfo& hard_boundary_info_a :
             hard_boundary_infos_a) {
          if (!math::IsInRange(
                  arc_length_a,
                  hard_boundary_info_a.range_on_center_line.start_pos,
                  hard_boundary_info_a.range_on_center_line.end_pos)) {
            continue;
          }
          for (const pnc_map::HardBoundaryInfo& hard_boundary_info_b :
               hard_boundary_infos_b) {
            if (!math::IsInRange(
                    arc_length_b,
                    hard_boundary_info_b.range_on_center_line.start_pos,
                    hard_boundary_info_b.range_on_center_line.end_pos)) {
              continue;
            }
            if (hard_boundary_info_a.id == hard_boundary_info_b.id) {
              return true;
            }
          }
        }

        return false;
      };

  const auto lane_a_for_hard_boundary =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Lane>(
          lane_a->id(), pnc_map::ElementQueryType::kOnlineFirst);
  const auto lane_b_for_hard_boundary =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Lane>(
          lane_b->id(), pnc_map::ElementQueryType::kOnlineFirst);
  return is_separated_by_hard_boundary(lane_a_for_hard_boundary.get(),
                                       lane_b_for_hard_boundary.get(),
                                       /*check_left_a=*/true) ||
         is_separated_by_hard_boundary(lane_a_for_hard_boundary.get(),
                                       lane_b_for_hard_boundary.get(),
                                       /*check_left_a=*/false);
}

// Returns true if the candidate is lane change source lane.
bool IsLaneChangeSourceLane(
    const lane_candidate::NearLaneCandidate* near_lane_candidate,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate) {
  if (near_lane_candidate == nullptr ||
      last_selected_lane_sequence_candidate == nullptr) {
    return false;
  }

  if (!lane_selection::IsLaneChangeType(
          last_selected_lane_sequence_candidate->lane_sequence_type())) {
    return false;
  }

  const auto& lane_change_instances =
      last_selected_lane_sequence_candidate->lane_change_instances();
  if (lane_change_instances.empty()) {
    return false;
  }

  // We only consider the first lane change instance.
  if ((*lane_change_instances.begin()).id().src_lane_id() ==
      near_lane_candidate->lane()->id()) {
    return true;
  }

  return false;
}

// Gets near lane candidates that are not lane change source lane if lane change
// finishes.
void FilterOutFinishedLaneChangeSourceLane(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        source_near_lane_candidates,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    int64_t last_snapshot_timestamp,
    int64_t last_lane_change_finish_timestamp_msec,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        target_near_lane_candidates) {
  for (const lane_candidate::NearLaneCandidate* source_candidate :
       source_near_lane_candidates) {
    if (last_snapshot_timestamp == last_lane_change_finish_timestamp_msec &&
        IsLaneChangeSourceLane(source_candidate,
                               last_selected_lane_sequence_candidate)) {
      continue;
    }

    AddNearLaneCandidate(source_candidate, target_near_lane_candidates);
  }
}

// Gets near lane candidates that have no hard boundary between physical lanes.
void EvalHardBoundary(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        physical_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        source_near_lane_candidates,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        target_near_lane_candidates) {
  for (const lane_candidate::NearLaneCandidate* source_candidate :
       source_near_lane_candidates) {
    // Check the non-physical candidates.
    if (std::all_of(
            physical_near_lane_candidates.begin(),
            physical_near_lane_candidates.end(),
            [source_candidate](
                const lane_candidate::NearLaneCandidate* physical_candidate) {
              return physical_candidate->lane()->id() !=
                     source_candidate->lane()->id();
            }) &&
        std::any_of(
            physical_near_lane_candidates.begin(),
            physical_near_lane_candidates.end(),
            [&joint_pnc_map_service, source_candidate](
                const lane_candidate::NearLaneCandidate* physical_candidate) {
              return HasHardBoundaryBetweenNearbyLanes(
                  joint_pnc_map_service, physical_candidate->lane(),
                  source_candidate->lane(),
                  physical_candidate->position_arclength_from_lane_start(),
                  source_candidate->position_arclength_from_lane_start());
            })) {
      continue;
    }

    AddNearLaneCandidate(source_candidate, target_near_lane_candidates);
  }
}

// Evaluates the near lane candidates which meet the requirements.
void Eval(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        physical_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        source_near_lane_candidates,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    int64_t last_snapshot_timestamp,
    int64_t last_lane_change_finish_timestamp_msec,
    std::vector<const lane_candidate::NearLaneCandidate*>*
        target_near_lane_candidates) {
  // Evaluate to filter lane change source lane when LC finishes.
  std::vector<const lane_candidate::NearLaneCandidate*>
      eval_LC_near_lane_candidates;
  eval_LC_near_lane_candidates.reserve(source_near_lane_candidates.size());
  FilterOutFinishedLaneChangeSourceLane(
      source_near_lane_candidates, last_selected_lane_sequence_candidate,
      last_snapshot_timestamp, last_lane_change_finish_timestamp_msec,
      &eval_LC_near_lane_candidates);

  // Evaluate that there is no hard boundary between the candidate and ego.
  EvalHardBoundary(physical_near_lane_candidates, eval_LC_near_lane_candidates,
                   joint_pnc_map_service, target_near_lane_candidates);
}

// Gets successor removed near lane candidates.
// For any pair of candidates, if they are successor and predecessor, do not
// consider the successor. For example, if there are three candidates A, B and
// C, if A->B->C, only return A. If A->C, and B->C, return both A and B. If A->B
// and C is B's neighbor, return both A and C.
std::vector<const lane_candidate::NearLaneCandidate*>
GetSuccessorRemovedNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates) {
  std::vector<const lane_candidate::NearLaneCandidate*> result;
  result.reserve(near_lane_candidates.size());

  std::set<int64_t> removed_lane_ids;
  for (const lane_candidate::NearLaneCandidate* candidate_a :
       near_lane_candidates) {
    for (const lane_candidate::NearLaneCandidate* candidate_b :
         near_lane_candidates) {
      if (candidate_a->lane()->id() == candidate_b->lane()->id()) {
        continue;
      }
      if (candidate_a->lane()->IsSuccessor(*candidate_b->lane())) {
        removed_lane_ids.insert(candidate_b->lane()->id());
      }
    }
  }

  for (const lane_candidate::NearLaneCandidate* candidate :
       near_lane_candidates) {
    if (removed_lane_ids.count(candidate->lane()->id()) > 0) {
      continue;
    }
    result.push_back(candidate);
  }

  return result;
}
}  // namespace

CurrentLaneReasoner::CurrentLaneReasoner(
    const DrivableLaneReasoner* drivable_lane_reasoner)
    : drivable_lane_reasoner_(DCHECK_NOTNULL(drivable_lane_reasoner)) {}

bool CurrentLaneReasoner::UpdateCurrentLaneCandidates(
    const voy::Pose& pose, const RobotStateSnapshot& robot_state_snapshot,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Section*>& last_selected_section_sequence,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const lane_selection::LaneSequenceProposalInfo& lane_sequence_proposal_info,
    int64_t last_snapshot_timestamp,
    int64_t last_lane_change_finish_timestamp_msec, bool is_new_trip,
    bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed) {
  if (!UpdateNearLaneCandidates(
          pose, robot_state_snapshot, joint_pnc_map_service,
          global_route_solution, locator_potential_sections,
          locator_selected_near_lanes, regional_section_vertices,
          last_selected_lane_sequence, last_selected_section_sequence)) {
    LOG(ERROR) << "Failed to update current lane candidates for regional path "
                  "because of empty near lanes.";
    return false;
  }

  const std::vector<const lane_candidate::NearLaneCandidate*>
      normal_near_lane_candidates = GetNormalNearLaneCandidates(
          robot_state_snapshot, global_route_solution,
          locator_selected_near_lanes, regional_section_vertices,
          last_selected_lane_sequence, last_selected_section_sequence);

  // Should make sure that we have normal near lane candidates.
  if (normal_near_lane_candidates.empty()) {
    LOG(ERROR) << "Failed to update current lane candidates for regional path "
                  "because of empty normal near lane candidates.";
    return false;
  }

  // The key is lane id and the value is prefix infos.
  std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>
      lane_prefix_info_map;
  const math::geometry::Point2d ego_position(robot_state_snapshot.x(),
                                             robot_state_snapshot.y());
  const std::vector<const lane_candidate::NearLaneCandidate*>
      proposal_near_lane_candidates = GetProposalNearLaneCandidates(
          normal_near_lane_candidates, joint_pnc_map_service,
          global_route_solution, last_selected_lane_sequence_candidate,
          last_lane_to_junction_info_map, stuck_avoidance_param,
          lane_blockage_detector_seed, lane_sequence_proposal_info, is_new_trip,
          should_generate_pull_out_jump_out_sequence,
          mutable_pull_out_jump_out_seed, &lane_prefix_info_map);

  const std::vector<const lane_candidate::NearLaneCandidate*>
      final_near_lane_candidates = EvalAndGetFinalNearLaneCandidates(
          normal_near_lane_candidates, proposal_near_lane_candidates,
          joint_pnc_map_service, last_selected_lane_sequence_candidate,
          last_snapshot_timestamp, last_lane_change_finish_timestamp_msec);

  if (final_near_lane_candidates.empty()) {
    LOG(ERROR) << "Failed to find appropriate near lane candidates after "
                  "evaluation, use backup lanes.";
    const std::vector<const lane_candidate::NearLaneCandidate*>
        backup_near_lane_candidates =
            GetBackupNearLaneCandidates(joint_pnc_map_service);
    if (backup_near_lane_candidates.empty()) {
      LOG(ERROR) << "Failed to find backup near lane candidates, ego position "
                    "is invalid! The position is: "
                 << rear_axle_center_.x() << ", " << rear_axle_center_.y()
                 << ".";
      return false;
    }

    AddCurrentLaneCandidates(backup_near_lane_candidates, lane_prefix_info_map,
                             ego_position);
    return true;
  }

  AddCurrentLaneCandidates(final_near_lane_candidates, lane_prefix_info_map,
                           ego_position);
  return true;
}

bool CurrentLaneReasoner::UpdateCurrentLaneCandidatesForRegionalPath(
    bool should_in_last_selected_lane_sequence) {
  for (const lane_candidate::CurrentLaneCandidate& candidate :
       current_lane_candidates_) {
    if (candidate.is_proposal() || !should_in_last_selected_lane_sequence ||
        candidate.is_in_last_selected_lane_sequence()) {
      current_lane_candidates_for_regional_path_.push_back(candidate);
    }
  }
  if (current_lane_candidates_for_regional_path_.empty()) {
    LOG(ERROR) << "Failed to update curent lane candidates for regional path "
                  "from last selected, use physical drivable lanes.";
    for (const lane_candidate::CurrentLaneCandidate& candidate :
         current_lane_candidates_) {
      if (candidate.is_physical() && candidate.is_physical()) {
        current_lane_candidates_for_regional_path_.push_back(candidate);
      }
    }
  }
  return !current_lane_candidates_for_regional_path_.empty();
}

bool CurrentLaneReasoner::UpdateCurrentLaneCandidatesForLaneSequence(
    const RegionalPath& regional_path,
    bool should_in_last_selected_lane_sequence) {
  DCHECK(!regional_path.sections.empty());
  const pnc_map::Section* current_section =
      DCHECK_NOTNULL(regional_path.sections.front());
  for (const lane_candidate::CurrentLaneCandidate& candidate :
       current_lane_candidates_) {
    if (candidate.lane()->section()->id() != current_section->id()) {
      continue;
    }
    // Note(Huoliang): Here we do not use is_on_fork field because this vector
    // is used to generate alternative lane sequence by fork.
    if (candidate.lane()->IsForkLane()) {
      current_lane_candidates_on_fork_.push_back(candidate);
    }

    if (candidate.is_downstream_proposal()) {
      current_lane_candidates_for_downstream_proposal_.push_back(candidate);
    }

    if (candidate.is_in_last_selected_lane_sequence()) {
      current_lanes_on_last_selected_lane_sequence_.push_back(candidate);
    }

    if (candidate.is_pull_out_jump_out()) {
      current_lane_candidates_for_pull_out_jump_out_.push_back(candidate);
      continue;
    }

    if (candidate.is_stuck_avoidance_jump_out()) {
      current_lane_candidates_for_stuck_avoidance_jump_out_.push_back(
          candidate);
      continue;
    }

    // The added closest physical candidate is not used to generate optimal lane
    // sequence.
    if (candidate.is_proposal() || !should_in_last_selected_lane_sequence ||
        candidate.is_in_last_selected_lane_sequence()) {
      current_lane_candidates_for_optimal_lane_sequence_.push_back(candidate);
      continue;
    }

    current_lane_candidates_for_backup_lane_sequence_.push_back(candidate);
  }
  if (current_lane_candidates_for_optimal_lane_sequence_.empty()) {
    LOG(ERROR) << "Failed to get curent lane candidates for lane sequence "
                  "from last selected, use backup lanes.";
    current_lane_candidates_for_optimal_lane_sequence_ =
        current_lane_candidates_for_backup_lane_sequence_;
  }
  return !current_lane_candidates_for_optimal_lane_sequence_.empty();
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::GetPhysicalNearLaneCandidates(
    bool need_drivable_lane) const {
  lane_candidate::LaneSelectionParam lane_selection_param;
  lane_selection_param.is_drivable = need_drivable_lane;
  lane_selection_param.is_physical = true;

  return SelectNearLaneCandidates(lane_selection_param);
}

std::vector<const pnc_map::Lane*> CurrentLaneReasoner::GetPhysicalLanes(
    bool need_drivable_lane) const {
  return lane_selection::GetLanesFromNearLaneCandidates(
      GetPhysicalNearLaneCandidates(need_drivable_lane));
}

std::vector<const pnc_map::Lane*> CurrentLaneReasoner::GetBackupLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service) const {
  return lane_selection::GetLanesFromNearLaneCandidates(
      GetBackupNearLaneCandidates(joint_pnc_map_service));
}

const std::vector<lane_candidate::CurrentLaneCandidate>&
CurrentLaneReasoner::GetCrossLaneNudgeCurrentLanes(
    pb::ManeuverType last_selected_maneuver_type,
    bool latch_current_lane) const {
  static std::vector<lane_candidate::CurrentLaneCandidate> empty_current_lanes;
  if (!FLAGS_planning_enable_decoupled_maneuvers ||
      last_selected_maneuver_type != pb::ManeuverType::LANE_FOLLOW ||
      !latch_current_lane) {
    return empty_current_lanes;
  }

  return current_lanes_on_last_selected_lane_sequence();
}

std::vector<const pnc_map::Lane*>
CurrentLaneReasoner::current_lanes_for_regional_path() const {
  return lane_selection::GetLanesFromCurrentLaneCandidates(
      current_lane_candidates_for_regional_path_);
}

bool CurrentLaneReasoner::UpdateNearLaneCandidates(
    const voy::Pose& pose, const RobotStateSnapshot& robot_state_snapshot,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Section*>&
        last_selected_section_sequence) {
  near_lane_candidates_.clear();

  const std::vector<const pnc_map::Lane*> near_lanes =
      joint_pnc_map_service.pnc_map_service()->GetNearLanesWithPose(
          pose, constants::kLaneSearchingRadiusInMeter,
          /*max_heading_diff=*/M_PI_2,
          /*prefer_overlapped_lanes=*/false);
  // TODO(Huoliang): Add rt-event after current lane reasoner is ready.
  if (near_lanes.empty()) {
    LOG(ERROR)
        << "There is no lane near ego pose when updating near lane candidates.";
    return false;
  }

  near_lane_candidates_.reserve(near_lanes.size());
  rear_axle_center_ = math::geometry::Point2d(pose.x(), pose.y());
  for (const pnc_map::Lane* lane : near_lanes) {
    if (lane == nullptr) {
      continue;
    }
    // Note(Huoliang): We filter all lanes that are not in locator potential
    // sections to further avoid the opposite lanes. But in some cases, we may
    // lose some directions because of lack of cost map that locator potential
    // sections base on. It means the potential sections are logical, not
    // physical from the hdmap. At this time, if ops wants to choose the
    // direction not in potential sections manually, we will not output near
    // lanes in this section unless regional section vertices update. So for
    // now, we rely on regional sections locator to reopen these sections to
    // handle this. Here we can just filter with potential sections.
    if (std::none_of(locator_potential_sections.begin(),
                     locator_potential_sections.end(),
                     [lane](const pnc_map::Section* potential_section) {
                       return lane->section()->id() == potential_section->id();
                     })) {
      continue;
    }

    near_lane_candidates_.push_back(CalculateNearLaneCandidate(
        lane, drivable_lane_reasoner_, rear_axle_center_, robot_state_snapshot,
        global_route_solution, locator_selected_near_lanes,
        regional_section_vertices, last_selected_lane_sequence,
        last_selected_section_sequence));
  }

  return near_lane_candidates_.empty() ? false : true;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::SelectNearLaneCandidates(
    const lane_candidate::LaneSelectionParam& lane_selection_param) const {
  std::vector<const lane_candidate::NearLaneCandidate*> near_lane_candidates;
  near_lane_candidates.reserve(near_lane_candidates_.size());

  for (const lane_candidate::NearLaneCandidate& candidate :
       near_lane_candidates_) {
    if (lane_selection_param.ShouldSelect(candidate)) {
      near_lane_candidates.push_back(&candidate);
    }
  }

  return near_lane_candidates;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::GetNormalNearLaneCandidates(
    const RobotStateSnapshot& robot_state_snapshot,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Section*>&
        last_selected_section_sequence) {
  std::vector<const lane_candidate::NearLaneCandidate*>
      normal_near_lane_candidates;
  normal_near_lane_candidates.reserve(near_lane_candidates_.size() + 1);

  lane_candidate::LaneSelectionParam lane_selection_param;

  if (!last_selected_lane_sequence.empty()) {
    // Try to find lane which is physical and in last selected lane sequence.
    // TODO(Huoliang): Solve the situation that last lane sequence has LC edge
    // in fork. Here we may return all physical lanes in fork.
    lane_selection_param.is_physical = true;
    lane_selection_param.is_in_last_selected_lane_sequence = true;
    normal_near_lane_candidates =
        SelectNearLaneCandidates(lane_selection_param);
    if (!normal_near_lane_candidates.empty()) {
      return normal_near_lane_candidates;
    }

    // If we reach here, it means that we can not find physical lane in last
    // selected lane sequence. It means that ego leaves lane
    // sequence(nudge/pullout/pullover/...). We expect that any maneuver making
    // ego leaving lane sequence gives a proposal, but for now, we need to
    // consider these cases, like normal nudge. Try to find closest physical
    // lane.
    lane_selection_param.Clear();
    lane_selection_param.is_physical = true;
    lane_selection_param.is_in_regional_section_vertices = true;
    lane_selection_param.is_in_last_selected_section_sequence =
        !last_selected_section_sequence.empty();
    const lane_candidate::NearLaneCandidate* closest_physical_candidate =
        GetClosestNearLaneCandidate(
            SelectNearLaneCandidates(lane_selection_param));
    if (closest_physical_candidate != nullptr) {
      AddNearLaneCandidate(closest_physical_candidate,
                           &normal_near_lane_candidates);
    }

    // Try to find closest lane in last selected lane sequence.
    lane_selection_param.Clear();
    lane_selection_param.is_in_last_selected_lane_sequence = true;
    const lane_candidate::NearLaneCandidate* closest_candidate_ptr =
        GetClosestNearLaneCandidate(
            SelectNearLaneCandidates(lane_selection_param));
    if (closest_candidate_ptr == nullptr) {
      // Note(Huoliang): We find some cases that ego is far away from lane
      // sequence when nudging and we can not find current lane in near lane
      // candidates. In this case, we add the closest lane from last selected
      // lane sequence.
      const pnc_map::Lane* closest_lane_in_last_selected_lane_sequence =
          pnc_map::GetClosestLaneInRange(
              last_selected_lane_sequence, rear_axle_center_,
              constants::kLaneSearchingRadiusInMeter * 1.5);
      if (closest_lane_in_last_selected_lane_sequence != nullptr) {
        closest_candidate_in_last_selected_lane_sequence_ =
            CalculateNearLaneCandidate(
                closest_lane_in_last_selected_lane_sequence,
                drivable_lane_reasoner_, rear_axle_center_,
                robot_state_snapshot, global_route_solution,
                locator_selected_near_lanes, regional_section_vertices,
                last_selected_lane_sequence, last_selected_section_sequence);
        AddNearLaneCandidate(&closest_candidate_in_last_selected_lane_sequence_,
                             &normal_near_lane_candidates);
      }
    } else {
      closest_candidate_in_last_selected_lane_sequence_ =
          *closest_candidate_ptr;
      AddNearLaneCandidate(&closest_candidate_in_last_selected_lane_sequence_,
                           &normal_near_lane_candidates);
    }
  } else {
    lane_selection_param.is_in_cost_map = true;
    lane_selection_param.is_selected_by_locator_near_lanes = true;
    lane_selection_param.is_in_last_selected_section_sequence =
        !last_selected_section_sequence.empty();
    normal_near_lane_candidates =
        SelectNearLaneCandidates(lane_selection_param);
  }

  if (normal_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    lane_selection_param.is_in_regional_section_vertices = true;
    const lane_candidate::NearLaneCandidate* closest_candidate_ptr =
        GetClosestNearLaneCandidate(
            SelectNearLaneCandidates(lane_selection_param));
    if (closest_candidate_ptr != nullptr) {
      AddNearLaneCandidate(closest_candidate_ptr, &normal_near_lane_candidates);
    }
  }

  return normal_near_lane_candidates;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::GetProposalNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const lane_selection::LaneSequenceProposalInfo& lane_sequence_proposal_info,
    bool is_new_trip, bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed,
    std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>*
        lane_prefix_info_map) const {
  std::vector<const lane_candidate::NearLaneCandidate*>
      proposal_near_lane_candidates;
  proposal_near_lane_candidates.reserve(near_lane_candidates_.size() + 1);

  lane_candidate::LaneSelectionParam lane_selection_param;
  lane_selection_param.is_overlapped = true;
  const std::vector<const lane_candidate::NearLaneCandidate*>
      overlapped_near_lane_candidates =
          SelectNearLaneCandidates(lane_selection_param);
  const std::vector<const lane_candidate::NearLaneCandidate*>
      near_lane_candidates_pool = GetProposalNearLaneCandidatesPool(
          normal_near_lane_candidates, near_lane_candidates_);

  // Add self-owned prefixes. Now we have JOs and fork.
  AddJumpOutNearLaneCandidates(
      GetPhysicalLanes(/*need_drivable_lane=*/true),
      normal_near_lane_candidates, overlapped_near_lane_candidates,
      near_lane_candidates_pool, joint_pnc_map_service, drivable_lane_reasoner_,
      global_route_solution, last_selected_lane_sequence_candidate,
      last_lane_to_junction_info_map, stuck_avoidance_param,
      lane_blockage_detector_seed, is_new_trip,
      should_generate_pull_out_jump_out_sequence,
      mutable_pull_out_jump_out_seed, lane_prefix_info_map,
      &proposal_near_lane_candidates);

  AddForkNearLaneCandidates(overlapped_near_lane_candidates,
                            near_lane_candidates_pool, lane_prefix_info_map,
                            &proposal_near_lane_candidates);

  // Add downstream prefixes.
  AddProposalNearLaneCandidates(
      near_lane_candidates_pool, lane_sequence_proposal_info,
      lane_prefix_info_map, &proposal_near_lane_candidates);

  return proposal_near_lane_candidates;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::EvalAndGetFinalNearLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        normal_near_lane_candidates,
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        proposal_near_lane_candidates,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    int64_t last_snapshot_timestamp,
    int64_t last_lane_change_finish_timestamp_msec) const {
  std::vector<const lane_candidate::NearLaneCandidate*>
      final_near_lane_candidates;
  final_near_lane_candidates.reserve(normal_near_lane_candidates.size() +
                                     proposal_near_lane_candidates.size());

  const std::vector<const lane_candidate::NearLaneCandidate*>
      physical_near_lane_candidates =
          GetPhysicalNearLaneCandidates(/*need_drivable_lane=*/false);
  Eval(physical_near_lane_candidates, normal_near_lane_candidates,
       joint_pnc_map_service, last_selected_lane_sequence_candidate,
       last_snapshot_timestamp, last_lane_change_finish_timestamp_msec,
       &final_near_lane_candidates);
  Eval(physical_near_lane_candidates, proposal_near_lane_candidates,
       joint_pnc_map_service, last_selected_lane_sequence_candidate,
       last_snapshot_timestamp, last_lane_change_finish_timestamp_msec,
       &final_near_lane_candidates);

  return final_near_lane_candidates;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneReasoner::GetBackupNearLaneCandidates(
    const pnc_map::JointPncMapService& joint_pnc_map_service) const {
  std::vector<const lane_candidate::NearLaneCandidate*>
      backup_near_lane_candidates;
  backup_near_lane_candidates.reserve(near_lane_candidates_.size());

  lane_candidate::LaneSelectionParam lane_selection_param;
  const std::vector<const lane_candidate::NearLaneCandidate*>
      physical_near_lane_candidates =
          GetPhysicalNearLaneCandidates(/*need_drivable_lane=*/false);

  // Note(Huoliang): The selection logic below is mainly issue driven.
  // 1. Prefer physical lanes on last selected lane sequence for stability.
  lane_selection_param.is_physical = true;
  lane_selection_param.is_in_last_selected_lane_sequence = true;
  EvalHardBoundary(physical_near_lane_candidates,
                   SelectNearLaneCandidates(lane_selection_param),
                   joint_pnc_map_service, &backup_near_lane_candidates);

  // 2. Still try to select lanes in last selected lane sequence, but relax the
  // distance to lane and limit the heading diff.
  if (backup_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    lane_selection_param.max_heading_diff = M_PI_4;
    lane_selection_param.max_position_to_lane_center_dist_in_m =
        constants::kLaneSearchingRadiusInMeter / 2.0;
    lane_selection_param.is_in_last_selected_lane_sequence = true;
    EvalHardBoundary(physical_near_lane_candidates,
                     SelectNearLaneCandidates(lane_selection_param),
                     joint_pnc_map_service, &backup_near_lane_candidates);
  }

  // 3. We can not find appropriate lanes from last selected lane sequence.
  // Try to find lanes according to ego pose. Prefer physical lanes, but should
  // be more restricted to heading diff.
  if (backup_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    lane_selection_param.max_heading_diff = M_PI_2 / 3.0;
    lane_selection_param.is_physical = true;
    EvalHardBoundary(physical_near_lane_candidates,
                     SelectNearLaneCandidates(lane_selection_param),
                     joint_pnc_map_service, &backup_near_lane_candidates);
  }

  // 4. Go on to relax the distance and heading diff.
  if (backup_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    lane_selection_param.max_heading_diff = M_PI_4;
    lane_selection_param.max_position_to_lane_center_dist_in_m =
        constants::kLaneSearchingRadiusInMeter / 2.0;
    EvalHardBoundary(physical_near_lane_candidates,
                     SelectNearLaneCandidates(lane_selection_param),
                     joint_pnc_map_service, &backup_near_lane_candidates);
  }

  // 5. Go on to relax the distance.
  if (backup_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    lane_selection_param.max_heading_diff = M_PI_4;
    EvalHardBoundary(physical_near_lane_candidates,
                     SelectNearLaneCandidates(lane_selection_param),
                     joint_pnc_map_service, &backup_near_lane_candidates);
  }

  // 6. Relax all the restriction.
  if (backup_near_lane_candidates.empty()) {
    lane_selection_param.Clear();
    EvalHardBoundary(physical_near_lane_candidates,
                     SelectNearLaneCandidates(lane_selection_param),
                     joint_pnc_map_service, &backup_near_lane_candidates);
  }

  return backup_near_lane_candidates;
}

void CurrentLaneReasoner::AddCurrentLaneCandidates(
    const std::vector<const lane_candidate::NearLaneCandidate*>&
        near_lane_candidates,
    const std::map<int64_t, std::vector<lane_candidate::PrefixInfo>>&
        lane_prefix_info_map,
    const math::geometry::Point2d& ego_position) {
  current_lane_candidates_.clear();
  current_lane_candidates_for_regional_path_.clear();
  current_lane_candidates_for_optimal_lane_sequence_.clear();
  current_lane_candidates_for_backup_lane_sequence_.clear();
  current_lanes_on_last_selected_lane_sequence_.clear();
  current_lane_candidates_for_pull_out_jump_out_.clear();
  current_lane_candidates_for_stuck_avoidance_jump_out_.clear();
  current_lane_candidates_on_fork_.clear();
  current_lane_candidates_for_downstream_proposal_.clear();

  // TODO(Huoliang): Find a better way to remove lanes having
  // predecessor-successor relationship. Need to consider the relative_position
  // of the projection point.
  const std::vector<const lane_candidate::NearLaneCandidate*>
      filtered_near_lane_candidates =
          GetSuccessorRemovedNearLaneCandidates(near_lane_candidates);

  const std::vector<lane_candidate::PrefixInfo> empty_prefix_infos = {};
  for (const lane_candidate::NearLaneCandidate* candidate :
       filtered_near_lane_candidates) {
    const pnc_map::Lane* lane = candidate->lane();
    routing::pb::LaneCandidate lane_candidate;
    lane_candidate.set_lane_id(lane->id());
    const math::ProximityQueryInfo proximity = lane->center_line().GetProximity(
        ego_position, math::pb::UseExtensionFlag::kForbid);
    const auto it = lane_prefix_info_map.find(candidate->lane()->id());
    const std::vector<lane_candidate::PrefixInfo>& prefix_infos =
        it != lane_prefix_info_map.end() ? it->second : empty_prefix_infos;
    // TODO(Huoliang): Build a more perfect cost system.
    const double cost =
        current_lane_cost_estimator_.GetCost(*candidate, prefix_infos);
    lane_candidate.set_candidate_cost(cost);
    lane_candidate.mutable_position()->set_x(ego_position.x());
    lane_candidate.mutable_position()->set_y(ego_position.y());
    const double percentage = proximity.arc_length / lane->length();
    lane_candidate.set_percentage_along_lane(percentage);
    current_lane_candidates_.emplace_back(*candidate, prefix_infos, cost,
                                          std::move(lane_candidate));
  }

  std::sort(current_lane_candidates_.begin(), current_lane_candidates_.end());
}

}  // namespace planner
