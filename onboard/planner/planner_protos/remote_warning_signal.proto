syntax = "proto3";

package planner.pb;

import "voy_protos/perception_object_type.proto";
import "voy_protos/point.proto";
import "voy_protos/remote_warning.proto";

// The warning represents that there are one or several traffic rule officers
// nearby ego. Next Available ID: 2
message PoliceOfficerWarning {
  // The object id of the traffic police officers. The presence of `repeated
  // addressed the scenario of multiple police officers.
  repeated int64 police_officer_ids = 1;
}

// The warning represents that there are one or several emergency vehicles
// nearby ego. Next Available ID: 2
message EmergencyVehicleWarning {
  // The object id of the emergency vehicles. The presence of `repeated
  // addressed the scenario of multiple emergency vehicles.
  repeated int64 emergency_vehicle_ids = 1;
}

// The warning object meta.
// Next Available ID: 4
message WarningObjectMeta {
  // The object's id.
  int64 object_id = 1;
  // The object's type.
  voy.perception.ObjectType type = 2;
  // The object's contour.
  repeated voy.Point2d contour = 3;
}

// |RemoteWarningSignal| is applicable for ADS reporting risk risk event of road
// test in real time. Which is published from planner speed vnode through the
// topic '/planning/remote_warning_signal'.
// The demand related prd can be referred:
// https://cooper.didichuxing.com/docs2/document/2203949711294
// Next Available ID: 11
message RemoteWarningSignal {
  reserved 4;
  reserved "should_discard_previous_warning";
  // The unique id of the signal.
  int64 id = 1;
  // The published timestamp.
  int64 timestamp = 2;
  // The sequence number identifying the message of the planning cycle.
  int64 sequence_number = 3;
  // The meta information of the remote warning task.
  repeated voy.remote_assist.RemoteWarningTaskMeta task_meta = 7;

  // The choice of optional versus oneof ensures compatibility with multi-type
  // warning scenarios.
  optional PoliceOfficerWarning police_officer_warning = 5;
  optional EmergencyVehicleWarning emergency_vehicle_warning = 6;
  // Warning objects from exception handler.
  repeated WarningObjectMeta eh_warning_objects = 8;
  // Warning objects from SE conflict warnings.
  repeated WarningObjectMeta se_warning_objects = 9;
  // Warning objects from perception collision detection.
  repeated WarningObjectMeta collision_objects = 10;
}

// The visual information for remote assist warning.
// Next Available ID: 6
message RemoteWarningVisual {
  // Id list of police officers.
  repeated int64 police_officer_ids = 1;
  // Id list of emergency vehicles.
  repeated int64 emergency_vehicle_ids = 2;
  // Warning objects from exception handler.
  // TODO(lxh): Refactors as ids if EH warning objects merge to perception.
  repeated WarningObjectMeta eh_warning_objects = 3;
  // Warning objects ids from SE conflict warnings.
  repeated int64 se_warning_object_ids = 4;
  // Warning objects ids from perception collision detection.
  repeated int64 collision_object_ids = 5;
}

// The detection result of remote warning detector.
// Next Available ID: 4
message RemoteWarningDetection {
  // The remote warning signal.
  RemoteWarningSignal remote_warning_signal = 1;
  // Whether to seed remote warning request.
  bool trigger_request = 2;
  // The visual information for remote assist warning.
  RemoteWarningVisual remote_warning_visual = 3;
}
