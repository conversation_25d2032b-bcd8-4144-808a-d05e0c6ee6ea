
#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_CONTROL_LQR_CONTROLLER_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_CONTROL_LQR_CONTROLLER_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/matrix.h"
#include "lateral_function/lateral_function_type.h"

typedef struct {
  float distance_error;
  float distance_error_rate;
  float heading_error;
  float heading_error_rate;
} LateralError;

extern void UpdateSystemMatrix(float ego_velocity, CML_Matrix *matrix_ad_ptr);

extern void UpdateControlMatrix(CML_Matrix *matrix_bd_ptr);

extern LateralError ComputeLateralErrors(float center_lane_y0,
                                         float center_lane_heading);

extern void UpdateStateMatrix(LateralError lateral_error,
                              CML_Matrix *matrix_state_ptr);

extern void UpdateControlMatrix(CML_Matrix *matrix_bd_ptr);

extern void ComputeStateCostMatrix(float ego_velocity, float q_cost_ratio,
                                   CML_Matrix *matrix_q_ptr);

extern float ComputeFeedback(const CML_Matrix *matrix_k_ptr,
                             const CML_Matrix *matrix_state_ptr);

extern float ComputeFeedForward(float k3, float ego_velocity,
                                float ref_curvature);

extern float LimitSteeringAngle(float ego_velocity, uint16_t system_state,
                                float front_wheel_steer_angle,
                                float *max_steer_angle_ptr,
                                float *max_steer_angle_rate_ptr);

extern float LqrController(const VSI_VehicleInfo_Struct *vehicle_info_ptr,
                           const BDP_FilteredLane_Struct *center_lane_ptr,
                           const FSA_SystemState_Struct *system_state_ptr,
                           LqrControllerDebug *lqr_debug_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_CONTROL_LQR_CONTROLLER_H_
