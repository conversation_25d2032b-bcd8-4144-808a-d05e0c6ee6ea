#ifndef ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_PARAMS_H_
#define ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_PARAMS_H_

#include "geometry/model/point_2d.h"
#include "math/range.h"
#include "onboard/planner/open_space/constraints/motion_plan_roi.h"

namespace planner {

namespace open_space {

// Struct GridParams defines the set of parameters related to the basic
// dimensions of the open space grid.
struct GridParams {
  GridParams() = default;
  GridParams(const MotionPlanROI& motion_plan_roi, double xy_res_m_in);

  // The range the its length in meters of open space grid on the x dimension,
  // from starting x coordinate to ending x coordinate.
  double x_range_length = 0.0;
  math::Range1d x_range{};
  // The number of x coordinate samples that can be sampled within this x range
  // given the resolution.
  int x_vertices_size = 0;
  // The number of x intervals between neighboring x samples, equal to
  // x_vertices_size - 1.
  int x_cell_size = 0;

  // The range the its length in meters of open space grid on the y dimension,
  // from starting y coordinate to ending y coordinate.
  double y_range_length = 0.0;
  math::Range1d y_range{};
  // The number of y coordinate samples that can be sampled within this y range
  // given the resolution.
  int y_vertices_size = 0;
  // The number of y intervals between neighboring y samples, equal to
  // y_vertices_size - 1.
  int y_cell_size = 0;

  // The resolution used to sample within the x and y range.
  double xy_res_m = 0.0;
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_PARAMS_H_
