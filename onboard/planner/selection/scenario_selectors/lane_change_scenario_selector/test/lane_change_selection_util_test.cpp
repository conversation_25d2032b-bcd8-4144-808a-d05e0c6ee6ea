#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"

#include <cmath>
#include <gtest/gtest.h>

#include "math/constants.h"
#include "math/interpolation.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"

namespace planner {
namespace selection {

class ComputeLaneChangeGapAlignedToleranceTest
    : public testing::TestWithParam<double> {};

INSTANTIATE_TEST_SUITE_P(ComputeLaneChangeGapAlignedToleranceParamsTest,
                         ComputeLaneChangeGapAlignedToleranceTest,
                         ::testing::Values(0.0, 2.0, 3.0, 4.0, 6.0, 7.0));

TEST_P(ComputeLaneChangeGapAlignedToleranceTest,
       ComputeLaneChangeGapAlignedToleranceTestWithVariousUrgencyScore) {
  constexpr double kLcGapAlignedTimeMaxToleranceForStaticObjectInSec = 3.0;
  constexpr double kLcGapAlignedTimeMaxToleranceInSec = 1.5;
  constexpr double kLcGapAlignedTimeMinToleranceInSec = 0.5;
  constexpr double kLcGapAlignedMinEgoSpeedInMps = 3.0;
  constexpr double kLcGapAlignedMaxEgoSpeedInMps = 6.0;

  std::ostringstream debug_oss;
  // Construct a planner object.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(1);
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  PlannerObject planner_object(
      agent_proto, /*object_timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, tracked_object));

  // Test cases for stationary gap object.
  {
    planner_object.set_is_primary_stationary(true);
    planner_object.set_has_stationary_to_move_intention(false);
    CrossLaneObjectInfo object_info(planner_object);
    EXPECT_DOUBLE_EQ(
        ComputeLaneChangeGapAlignedTolerance(
            &object_info, /*is_pass_gap=*/true, /*ego_speed=*/10.0,
            /*urgency_score=*/0.0, /*is_consecutive_lc=*/false, debug_oss),
        kLcGapAlignedTimeMaxToleranceForStaticObjectInSec);
    EXPECT_DOUBLE_EQ(
        ComputeLaneChangeGapAlignedTolerance(
            &object_info, /*is_pass_gap=*/true, /*ego_speed=*/10.0,
            /*urgency_score=*/0.0, /*is_consecutive_lc=*/true, debug_oss),
        kLcGapAlignedTimeMaxToleranceForStaticObjectInSec);

    planner_object.set_is_primary_stationary(true);
    planner_object.set_has_stationary_to_move_intention(true);
    CrossLaneObjectInfo object_info1(planner_object);
    EXPECT_DOUBLE_EQ(
        ComputeLaneChangeGapAlignedTolerance(
            &object_info1, /*is_pass_gap=*/true, /*ego_speed=*/10.0,
            /*urgency_score=*/0.0, /*is_consecutive_lc=*/false, debug_oss),
        kLcGapAlignedTimeMinToleranceInSec);
    EXPECT_DOUBLE_EQ(
        ComputeLaneChangeGapAlignedTolerance(
            &object_info1, /*is_pass_gap=*/true, /*ego_speed=*/10.0,
            /*urgency_score=*/0.0, /*is_consecutive_lc=*/true, debug_oss),
        kLcGapAlignedTimeMaxToleranceForStaticObjectInSec);
  }

  const double ego_speed = GetParam();

  // Test case with no urgency.
  {
    const double urgency_score = 0.0;

    planner_object.set_is_primary_stationary(false);
    planner_object.set_has_stationary_to_move_intention(false);
    CrossLaneObjectInfo object_info(planner_object);
    const double gap_aligned_time_tolerance =
        ComputeLaneChangeGapAlignedTolerance(
            &object_info, /*is_pass_gap=*/false, ego_speed, urgency_score,
            /*is_consecutive_lc=*/false, debug_oss);
    if (ego_speed < kLcGapAlignedMinEgoSpeedInMps + math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMaxToleranceInSec);
    } else if (ego_speed >
               kLcGapAlignedMaxEgoSpeedInMps - math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMinToleranceInSec);
    } else {
      EXPECT_GT(gap_aligned_time_tolerance, kLcGapAlignedTimeMinToleranceInSec);
      EXPECT_LT(gap_aligned_time_tolerance, kLcGapAlignedTimeMaxToleranceInSec);
    }
  }

  // Test case with medium urgency.
  {
    const double urgency_score = 0.5;
    planner_object.set_is_primary_stationary(false);
    planner_object.set_has_stationary_to_move_intention(false);
    CrossLaneObjectInfo object_info(planner_object);
    const double gap_aligned_time_tolerance =
        ComputeLaneChangeGapAlignedTolerance(
            &object_info, /*is_pass_gap=*/false, ego_speed, urgency_score,
            /*is_consecutive_lc=*/false, debug_oss);
    const double min_speed_threshold = math::GetLinearInterpolatedY(
        kLcGapAlignedTimeMinToleranceInSec, kLcGapAlignedTimeMaxToleranceInSec,
        kLcGapAlignedMaxEgoSpeedInMps, kLcGapAlignedMinEgoSpeedInMps,
        kLcGapAlignedTimeMaxToleranceInSec / 1.5);
    const double max_speed_threshold = math::GetLinearInterpolatedY(
        kLcGapAlignedTimeMinToleranceInSec, kLcGapAlignedTimeMaxToleranceInSec,
        kLcGapAlignedMaxEgoSpeedInMps, kLcGapAlignedMinEgoSpeedInMps,
        kLcGapAlignedTimeMinToleranceInSec);
    if (ego_speed < min_speed_threshold + math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMaxToleranceInSec);
    } else if (ego_speed > max_speed_threshold - math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMinToleranceInSec * 1.5);
    } else {
      EXPECT_GT(gap_aligned_time_tolerance, kLcGapAlignedTimeMinToleranceInSec);
      EXPECT_LT(gap_aligned_time_tolerance, kLcGapAlignedTimeMaxToleranceInSec);
      EXPECT_LT(gap_aligned_time_tolerance,
                kLcGapAlignedTimeMinToleranceInSec * 1.5);
    }
  }

  // Test case with high urgency.
  {
    const double urgency_score = 1.0;
    planner_object.set_is_primary_stationary(false);
    planner_object.set_has_stationary_to_move_intention(false);
    CrossLaneObjectInfo object_info(planner_object);
    const double gap_aligned_time_tolerance =
        ComputeLaneChangeGapAlignedTolerance(
            &object_info, /*is_pass_gap=*/false, ego_speed, urgency_score,
            /*is_consecutive_lc=*/false, debug_oss);
    const double min_speed_threshold = math::GetLinearInterpolatedY(
        kLcGapAlignedTimeMinToleranceInSec, kLcGapAlignedTimeMaxToleranceInSec,
        kLcGapAlignedMaxEgoSpeedInMps, kLcGapAlignedMinEgoSpeedInMps,
        kLcGapAlignedTimeMaxToleranceInSec / 2.0);
    const double max_speed_threshold = math::GetLinearInterpolatedY(
        kLcGapAlignedTimeMinToleranceInSec, kLcGapAlignedTimeMaxToleranceInSec,
        kLcGapAlignedMaxEgoSpeedInMps, kLcGapAlignedMinEgoSpeedInMps,
        kLcGapAlignedTimeMinToleranceInSec);
    if (ego_speed < min_speed_threshold + math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMaxToleranceInSec);
    } else if (ego_speed > max_speed_threshold - math::constants::kEpsilon) {
      EXPECT_DOUBLE_EQ(gap_aligned_time_tolerance,
                       kLcGapAlignedTimeMinToleranceInSec * 2.0);
    } else {
      EXPECT_GT(gap_aligned_time_tolerance, kLcGapAlignedTimeMinToleranceInSec);
      EXPECT_LT(gap_aligned_time_tolerance, kLcGapAlignedTimeMaxToleranceInSec);
      EXPECT_LT(gap_aligned_time_tolerance,
                kLcGapAlignedTimeMinToleranceInSec * 2.0);
    }
  }
}

class IsLaneChangeGapReadyTest : public testing::Test,
                                 public speed::ReasoningTestFixture {
 public:
  IsLaneChangeGapReadyTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};

TEST_F(IsLaneChangeGapReadyTest,
       IsLaneChangeGapNotReadyByRelativeStatesWithYieldGap) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.2, /*speed=*/8.0,
             /*accel=*/0.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  constexpr int64_t kLeadObjId = 1;
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/kLeadObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.4,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);
  EXPECT_EQ(1, agent_list().agent_list().size());

  UpdateForLaneChange();
  ExtractToSpeedWorldModel();
  EXPECT_EQ(1, speed_world_model().object_prediction_map().size());

  speed::pb::SpeedSeed speed_seed;
  speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
      speed_seed.add_lc_guide_seed();
  lc_guide_seed->set_lead_obj_id(kLeadObjId);
  lc_guide_seed->set_is_pass(false);
  lc_guide_seed->set_gap_aligned_time_ix(0);
  EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info()
          .cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  EXPECT_NE(target_region_object_info_list, nullptr);
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(kLeadObjId);
  EXPECT_NE(object_info, nullptr);

  std::ostringstream debug_oss;
  const double gap_aligned_time_tolerance = 1.0;
  EXPECT_FALSE(IsLaneChangeGapReadyByRelativeStates(
      speed_world_model(), trajectory_info(),
      trajectory_info().path().polyline_curve(), speed_seed, object_info,
      /*is_pass_gap=*/false, gap_aligned_time_tolerance, debug_oss));
}

TEST_F(IsLaneChangeGapReadyTest,
       IsLaneChangeGapReadyByRelativeStatesWithYieldGap) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/6.0,
             /*accel=*/0.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  constexpr int64_t kLeadObjId = 1;
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/kLeadObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);
  EXPECT_EQ(1, agent_list().agent_list().size());

  UpdateForLaneChange();
  ExtractToSpeedWorldModel();
  EXPECT_EQ(1, speed_world_model().object_prediction_map().size());

  speed::pb::SpeedSeed speed_seed;
  speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
      speed_seed.add_lc_guide_seed();
  lc_guide_seed->set_lead_obj_id(kLeadObjId);
  lc_guide_seed->set_is_pass(false);
  lc_guide_seed->set_gap_aligned_time_ix(0);
  EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info()
          .cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  EXPECT_NE(target_region_object_info_list, nullptr);
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(kLeadObjId);
  EXPECT_NE(object_info, nullptr);

  std::ostringstream debug_oss;
  const double gap_aligned_time_tolerance = 1.0;
  EXPECT_TRUE(IsLaneChangeGapReadyByRelativeStates(
      speed_world_model(), trajectory_info(),
      trajectory_info().path().polyline_curve(), speed_seed, object_info,
      /*is_pass_gap=*/false, gap_aligned_time_tolerance, debug_oss));
}

TEST_F(IsLaneChangeGapReadyTest,
       IsLaneChangeGapNotReadyByRelativeStatesWithPassGap) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0,
             /*accel=*/0.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  constexpr int64_t kTailObjId = 1;
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/kTailObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.8,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);
  EXPECT_EQ(1, agent_list().agent_list().size());

  UpdateForLaneChange();
  ExtractToSpeedWorldModel();
  EXPECT_EQ(1, speed_world_model().object_prediction_map().size());

  speed::pb::SpeedSeed speed_seed;
  speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
      speed_seed.add_lc_guide_seed();
  lc_guide_seed->set_tail_obj_id(kTailObjId);
  lc_guide_seed->set_is_pass(true);
  lc_guide_seed->set_gap_aligned_time_ix(0);
  EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info()
          .cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  EXPECT_NE(target_region_object_info_list, nullptr);
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(kTailObjId);
  EXPECT_NE(object_info, nullptr);

  std::ostringstream debug_oss;
  const double gap_aligned_time_tolerance = 1.0;
  EXPECT_FALSE(IsLaneChangeGapReadyByRelativeStates(
      speed_world_model(), trajectory_info(),
      trajectory_info().path().polyline_curve(), speed_seed, object_info,
      /*is_pass_gap=*/true, gap_aligned_time_tolerance, debug_oss));
}

TEST_F(IsLaneChangeGapReadyTest,
       IsLaneChangeGapReadyByRelativeStatesWithPassGap) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/10.0,
             /*accel=*/0.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  constexpr int64_t kTailObjId = 1;
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/kTailObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/8.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.6,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);
  EXPECT_EQ(1, agent_list().agent_list().size());

  UpdateForLaneChange();
  ExtractToSpeedWorldModel();
  EXPECT_EQ(1, speed_world_model().object_prediction_map().size());

  speed::pb::SpeedSeed speed_seed;
  speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
      speed_seed.add_lc_guide_seed();
  lc_guide_seed->set_tail_obj_id(kTailObjId);
  lc_guide_seed->set_is_pass(true);
  lc_guide_seed->set_gap_aligned_time_ix(0);
  EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info()
          .cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  EXPECT_NE(target_region_object_info_list, nullptr);
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(kTailObjId);
  EXPECT_NE(object_info, nullptr);

  std::ostringstream debug_oss;
  const double gap_aligned_time_tolerance = 1.0;
  EXPECT_TRUE(IsLaneChangeGapReadyByRelativeStates(
      speed_world_model(), trajectory_info(),
      trajectory_info().path().polyline_curve(), speed_seed, object_info,
      /*is_pass_gap=*/true, gap_aligned_time_tolerance, debug_oss));
}

TEST_F(IsLaneChangeGapReadyTest,
       IsLaneChangeGapReadyByRelativeStatesTestWithGapObjectType) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0,
             /*accel=*/0.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  // Test cyclist.
  {
    constexpr int64_t kTailObjId = 1;
    prediction::pb::Agent& agent1 =
        AddAgent(voy::perception::ObjectType::CYCLIST,
                 /*id=*/kTailObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.2,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/10.0);
    AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                   /*start_portion=*/0.1,
                                   /*end_lane_id=*/kTargetLaneId,
                                   /*end_portion=*/0.8,
                                   /*likelihood=*/1.0,
                                   /*traj_id=*/1, agent1);
    EXPECT_EQ(1, agent_list().agent_list().size());

    UpdateForLaneChange();
    ExtractToSpeedWorldModel();
    EXPECT_EQ(1, speed_world_model().object_prediction_map().size());

    speed::pb::SpeedSeed speed_seed;
    speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
        speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(kTailObjId);
    lc_guide_seed->set_is_pass(true);
    lc_guide_seed->set_gap_aligned_time_ix(0);
    EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

    const CrossLaneObjectInfoList* target_region_object_info_list =
        trajectory_info()
            .cross_lane_info_manager()
            .GetTargetRegionObjectInfoListPtr();
    EXPECT_NE(target_region_object_info_list, nullptr);
    const CrossLaneObjectInfo* object_info =
        target_region_object_info_list->Find(kTailObjId);
    EXPECT_NE(object_info, nullptr);

    std::ostringstream debug_oss;
    const double gap_aligned_time_tolerance = 1.0;
    EXPECT_FALSE(IsLaneChangeGapReadyByRelativeStates(
        speed_world_model(), trajectory_info(),
        trajectory_info().path().polyline_curve(), speed_seed, object_info,
        /*is_pass_gap=*/true, gap_aligned_time_tolerance, debug_oss));
  }

  // Test traffic cone.
  {
    constexpr int64_t kTailObjId = 2;
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::TRAFFIC_CONE,
                 /*id=*/kTailObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.6,
                 /*length=*/0.5, /*width=*/0.5,
                 /*velocity=*/0.0);
    AddStationaryPredictedTrajectory(
        /*lane_id=*/kTargetLaneId, /*portion=*/0.6,
        /*likelihood=*/1.0, /*traj_id=*/1, agent,
        /*bbox_heading_diff_to_lane=*/0.0);

    EXPECT_EQ(2, agent_list().agent_list().size());

    UpdateForLaneChange();
    ExtractToSpeedWorldModel();
    EXPECT_EQ(2, speed_world_model().object_prediction_map().size());

    speed::pb::SpeedSeed speed_seed;
    speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
        speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(kTailObjId);
    lc_guide_seed->set_is_pass(true);
    lc_guide_seed->set_gap_aligned_time_ix(0);
    EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

    const CrossLaneObjectInfoList* target_region_object_info_list =
        trajectory_info()
            .cross_lane_info_manager()
            .GetTargetRegionObjectInfoListPtr();
    EXPECT_NE(target_region_object_info_list, nullptr);
    const CrossLaneObjectInfo* object_info =
        target_region_object_info_list->Find(kTailObjId);
    EXPECT_NE(object_info, nullptr);

    std::ostringstream debug_oss;
    const double gap_aligned_time_tolerance = 1.0;
    EXPECT_TRUE(IsLaneChangeGapReadyByRelativeStates(
        speed_world_model(), trajectory_info(),
        trajectory_info().path().polyline_curve(), speed_seed, object_info,
        /*is_pass_gap=*/true, gap_aligned_time_tolerance, debug_oss));
  }

  // Test pedestrian.
  {
    constexpr int64_t kTailObjId = 3;
    prediction::pb::Agent& agent =
        AddAgent(voy::perception::ObjectType::PED,
                 /*id=*/kTailObjId, /*lane_id=*/kTargetLaneId, /*portion=*/0.6,
                 /*length=*/1.0, /*width=*/1.0,
                 /*velocity=*/0.5);
    AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                   /*start_portion=*/0.6,
                                   /*end_lane_id=*/kTargetLaneId,
                                   /*end_portion=*/0.8,
                                   /*likelihood=*/1.0,
                                   /*traj_id=*/1, agent);

    EXPECT_EQ(3, agent_list().agent_list().size());

    UpdateForLaneChange();
    ExtractToSpeedWorldModel();
    EXPECT_EQ(3, speed_world_model().object_prediction_map().size());

    speed::pb::SpeedSeed speed_seed;
    speed::pb::SpeedSolverLcGuideSeed* lc_guide_seed =
        speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(kTailObjId);
    lc_guide_seed->set_is_pass(true);
    lc_guide_seed->set_gap_aligned_time_ix(0);
    EXPECT_EQ(1, speed_seed.lc_guide_seed_size());

    const CrossLaneObjectInfoList* target_region_object_info_list =
        trajectory_info()
            .cross_lane_info_manager()
            .GetTargetRegionObjectInfoListPtr();
    EXPECT_NE(target_region_object_info_list, nullptr);
    const CrossLaneObjectInfo* object_info =
        target_region_object_info_list->Find(kTailObjId);
    EXPECT_NE(object_info, nullptr);

    std::ostringstream debug_oss;
    const double gap_aligned_time_tolerance = 1.0;
    EXPECT_TRUE(IsLaneChangeGapReadyByRelativeStates(
        speed_world_model(), trajectory_info(),
        trajectory_info().path().polyline_curve(), speed_seed, object_info,
        /*is_pass_gap=*/true, gap_aligned_time_tolerance, debug_oss));
  }
}

class ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkTest
    : public testing::Test {};

TEST_F(
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkTest,
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkWithInvalidInput) {
  // Test invalid input jerk value.
  {
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/0.0, /*v_in=*/1.0, /*a_in=*/0.0,
                                  /*j_in=*/0.0);
    EXPECT_FALSE(
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, /*future_t=*/0.1, /*jerk=*/-1.0)
            .has_value());

    EXPECT_FALSE(
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, /*future_t=*/0.1, /*jerk=*/0.0)
            .has_value());
  }

  // Test invalid init state.
  {
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/0.0, /*v_in=*/-1.0, /*a_in=*/0.0,
                                  /*j_in=*/0.0);
    EXPECT_FALSE(
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, /*future_t=*/0.1, /*jerk=*/1.0)
            .has_value());
  }
}

TEST_F(
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkTest,
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkWithPhase1Only) {
  // Test case of releasing gas.
  {
    const double future_t = 0.5;
    const double init_x = 50.0;
    const double speed = 10.0;
    const double accel = 1.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_DOUBLE_EQ(future_state->t, future_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel - jerk_value * future_t);
    EXPECT_DOUBLE_EQ(
        future_state->v,
        speed + accel * future_t - 0.5 * jerk_value * future_t * future_t);
    EXPECT_DOUBLE_EQ(
        future_state->x,
        init_x + speed * future_t + 0.5 * accel * future_t * future_t -
            1.0 / 6.0 * jerk_value * future_t * future_t * future_t);
  }

  // Test case of releasing brake with non-zero velocity at last, and zero speed
  // status is unreachable even with a larger horizon.
  {
    const double future_t = 0.5;
    const double init_x = 50.0;
    const double speed = 10.0;
    const double accel = -1.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_DOUBLE_EQ(future_state->t, future_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel + jerk_value * future_t);
    EXPECT_DOUBLE_EQ(
        future_state->v,
        speed + accel * future_t + 0.5 * jerk_value * future_t * future_t);
    EXPECT_DOUBLE_EQ(
        future_state->x,
        init_x + speed * future_t + 0.5 * accel * future_t * future_t +
            1.0 / 6.0 * jerk_value * future_t * future_t * future_t);
  }

  // Test case of releasing brake with non-zero velocity at last, and zero speed
  // status is reachable with a larger horizon.
  {
    const double future_t = 0.5;
    const double init_x = 50.0;
    const double speed = 1.0;
    const double accel = -2.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_DOUBLE_EQ(future_state->t, future_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel + jerk_value * future_t);
    EXPECT_DOUBLE_EQ(
        future_state->v,
        speed + accel * future_t + 0.5 * jerk_value * future_t * future_t);
    EXPECT_GT(future_state->v, 0.0);
    EXPECT_DOUBLE_EQ(
        future_state->x,
        init_x + speed * future_t + 0.5 * accel * future_t * future_t +
            1.0 / 6.0 * jerk_value * future_t * future_t * future_t);
  }

  // Test case of releasing brake with zero velocity at last, and zero speed
  // status is reachable within the given horizon.
  {
    const double future_t = 0.6;
    const double init_x = 50.0;
    const double speed = 1.0;
    const double accel = -2.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_LT(future_state->t, future_t);
    EXPECT_LT(future_state->a, accel + jerk_value * future_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_GT(future_state->v, speed + accel * future_t +
                                   0.5 * jerk_value * future_t * future_t);
    EXPECT_GT(future_state->x,
              init_x + speed * future_t + 0.5 * accel * future_t * future_t +
                  1.0 / 6.0 * jerk_value * future_t * future_t * future_t);

    const double zero_speed_t =
        -accel / jerk_value -
        std::sqrt(accel * accel - 2.0 * jerk_value * speed) / jerk_value;
    EXPECT_GT(zero_speed_t, 0.0);
    EXPECT_DOUBLE_EQ(future_state->t, zero_speed_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel + jerk_value * zero_speed_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_NEAR(future_state->v,
                speed + accel * zero_speed_t +
                    0.5 * jerk_value * zero_speed_t * zero_speed_t,
                math::constants::kEpsilon);
    EXPECT_DOUBLE_EQ(future_state->x,
                     init_x + speed * zero_speed_t +
                         0.5 * accel * zero_speed_t * zero_speed_t +
                         1.0 / 6.0 * jerk_value * zero_speed_t * zero_speed_t *
                             zero_speed_t);
  }
}

TEST_F(
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkTest,
    ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerkWith2Phases) {
  // Test case of releasing gas.
  {
    const double future_t = 2.0;
    const double init_x = 50.0;
    const double speed = 10.0;
    const double accel = 1.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_DOUBLE_EQ(future_state->t, future_t);
    const double mid_t = accel / jerk_value;
    EXPECT_DOUBLE_EQ(future_state->a, 0.0);
    EXPECT_DOUBLE_EQ(future_state->v,
                     speed + accel * mid_t - 0.5 * jerk_value * mid_t * mid_t);
    EXPECT_DOUBLE_EQ(future_state->x,
                     init_x + speed * mid_t + 0.5 * accel * mid_t * mid_t -
                         1.0 / 6.0 * jerk_value * mid_t * mid_t * mid_t +
                         future_state->v * (future_t - mid_t));
  }

  // Test case of releasing brake with non-zero velocity at last, and zero speed
  // status is unreachable even with a larger horizon.
  {
    const double future_t = 2.0;
    const double init_x = 50.0;
    const double speed = 10.0;
    const double accel = -1.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_DOUBLE_EQ(future_state->t, future_t);
    const double mid_t = -accel / jerk_value;
    EXPECT_DOUBLE_EQ(future_state->a, 0.0);
    EXPECT_DOUBLE_EQ(future_state->v,
                     speed + accel * mid_t + 0.5 * jerk_value * mid_t * mid_t);
    EXPECT_DOUBLE_EQ(future_state->x,
                     init_x + speed * mid_t + 0.5 * accel * mid_t * mid_t +
                         1.0 / 6.0 * jerk_value * mid_t * mid_t * mid_t +
                         future_state->v * (future_t - mid_t));
  }

  // Test case of releasing brake with zero velocity at last, and zero speed
  // status is reachable within the given horizon.
  {
    const double future_t = 1.5;
    const double init_x = 50.0;
    const double speed = 1.0;
    const double accel = -2.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    EXPECT_LT(future_state->t, future_t);
    EXPECT_LT(future_state->a, accel + jerk_value * future_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_GT(future_state->v, speed + accel * future_t +
                                   0.5 * jerk_value * future_t * future_t);
    EXPECT_GT(future_state->x,
              init_x + speed * future_t + 0.5 * accel * future_t * future_t +
                  1.0 / 6.0 * jerk_value * future_t * future_t * future_t);

    const double zero_speed_t =
        -accel / jerk_value -
        std::sqrt(accel * accel - 2.0 * jerk_value * speed) / jerk_value;
    EXPECT_GT(zero_speed_t, 0.0);
    EXPECT_DOUBLE_EQ(future_state->t, zero_speed_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel + jerk_value * zero_speed_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_NEAR(future_state->v,
                speed + accel * zero_speed_t +
                    0.5 * jerk_value * zero_speed_t * zero_speed_t,
                math::constants::kEpsilon);
    EXPECT_DOUBLE_EQ(future_state->x,
                     init_x + speed * zero_speed_t +
                         0.5 * accel * zero_speed_t * zero_speed_t +
                         1.0 / 6.0 * jerk_value * zero_speed_t * zero_speed_t *
                             zero_speed_t);
  }

  // Test case of releasing brake with zero velocity at last, and zero
  // acceleration status is reachable within the given horizon if reversing
  // motion is enabled.
  {
    const double future_t = 3.0;
    const double init_x = 50.0;
    const double speed = 1.0;
    const double accel = -2.0;
    const double jerk_value = 1.0;
    const speed::State init_state(/*t_in=*/0.0,
                                  /*x_in=*/init_x, /*v_in=*/speed,
                                  /*a_in=*/accel,
                                  /*j_in=*/0.0);
    const std::optional<speed::State> future_state =
        ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
            init_state, future_t, jerk_value);
    EXPECT_TRUE(future_state.has_value());
    const double mid_t = -accel / jerk_value;
    EXPECT_LT(future_state->t, mid_t);
    EXPECT_LT(future_state->a, accel + jerk_value * mid_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_GT(future_state->v,
              speed + accel * mid_t + 0.5 * jerk_value * mid_t * mid_t);
    EXPECT_GT(future_state->x,
              init_x + speed * mid_t + 0.5 * accel * mid_t * mid_t +
                  1.0 / 6.0 * jerk_value * mid_t * mid_t * mid_t);

    const double zero_speed_t =
        mid_t -
        std::sqrt(accel * accel - 2.0 * jerk_value * speed) / jerk_value;
    EXPECT_GT(zero_speed_t, 0.0);
    EXPECT_DOUBLE_EQ(future_state->t, zero_speed_t);
    EXPECT_DOUBLE_EQ(future_state->a, accel + jerk_value * zero_speed_t);
    EXPECT_NEAR(future_state->v, 0.0, math::constants::kEpsilon);
    EXPECT_NEAR(future_state->v,
                speed + accel * zero_speed_t +
                    0.5 * jerk_value * zero_speed_t * zero_speed_t,
                math::constants::kEpsilon);
    EXPECT_DOUBLE_EQ(future_state->x,
                     init_x + speed * zero_speed_t +
                         0.5 * accel * zero_speed_t * zero_speed_t +
                         1.0 / 6.0 * jerk_value * zero_speed_t * zero_speed_t *
                             zero_speed_t);
  }
}

// TODO(pengfei): Implement this test.
TEST(LaneChangeSelectionUtilTest, IsHighDiscomfortLaneChangeUnderLowUrgency) {}

class TargetNeighborRiskTest : public testing::Test,
                               public speed::ReasoningTestFixture {
 public:
  TargetNeighborRiskTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};

TEST_F(TargetNeighborRiskTest,
       IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegionTest) {
  constexpr int64_t kSourceLaneId = 128481;
  constexpr int64_t kTargetLaneId = 128479;
  constexpr int64_t kTargetNeighborLaneId = 128483;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/8.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lc_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/0.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  // Risky target region agent cutin from target neighbor region.
  prediction::pb::Agent& agent1 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/kTargetNeighborLaneId, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/6.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetNeighborLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.4,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);

  // Non-risky target region agent cutin from target neighbor region.
  prediction::pb::Agent& agent2 =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/2, /*lane_id=*/kTargetNeighborLaneId, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetNeighborLaneId,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);
  EXPECT_EQ(2, agent_list().agent_list().size());

  UpdateForLaneChange();

  const CrossLaneInfoManager& cross_lane_info_manager =
      trajectory_info().cross_lane_info_manager();

  const CrossLaneObjectInfoList* target_neighbor_region_object_infos =
      cross_lane_info_manager.GetTargetNeighborRegionObjectInfoListPtr();
  EXPECT_TRUE(target_neighbor_region_object_infos != nullptr);
  EXPECT_EQ(2, target_neighbor_region_object_infos->ids().size());

  const CrossLaneObjectInfoList* target_region_object_infos =
      cross_lane_info_manager.GetTargetRegionObjectInfoListPtr();
  EXPECT_TRUE(target_region_object_infos != nullptr);
  EXPECT_EQ(2, target_region_object_infos->ids().size());

  std::ostringstream debug_oss;
  EXPECT_TRUE(IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
      world_model().robot_state(), cross_lane_info_manager, /*object_id=*/1,
      debug_oss));

  EXPECT_FALSE(IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
      world_model().robot_state(), cross_lane_info_manager, /*object_id=*/2,
      debug_oss));
}

}  // namespace selection
}  // namespace planner
