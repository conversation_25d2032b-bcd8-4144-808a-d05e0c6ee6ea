syntax = "proto3";

package planner.pb;

import "google/protobuf/wrappers.proto";

import "hdmap_protos/lane.proto";
import "hdmap_protos/relax_lane_marking_info.proto";
import "mrc_protos/mrc_request.proto";
import "planner_protos/behavior_reasoner.proto";
import "planner_protos/common.proto";
import "planner_protos/lane_blockage.proto";
import "planner_protos/lane_selection_preference.proto";
import "planner_protos/map_change_area_processor.proto";
import "planner_protos/predicted_trajectory_route_association.proto";
import "planner_protos/regional_map.proto";
import "planner_protos/relax_lane_sequence_parameter.proto";
import "planner_protos/route_event.proto";
import "planner_protos/remote_assist.proto";
import "planner_protos/speed_seed.proto";
import "planner_protos/planning_seed.proto";
import "planner_protos/immediate_pull_over.proto";
import "stop_cell_protos/stop_cell_data.proto";
import "planner_protos/subscribed_message_info.proto";
import "pnc_map_protos/map_debug.proto";
import "routing_protos/route_query.proto";
import "routing_protos/route_solution.proto";
import "routing_protos/route_status.proto";
import "voy_protos/math.proto";
import "voy_protos/point.proto";
import "voy_protos/polygon.proto";
import "voy_protos/pose.proto";

// The curve to search occlusion for super nudge.
// NEXT Available ID: 2
message OcclusionSearchCurve { repeated voy.Point2d points = 1; }

// Debug info for a pull over target line.
// NEXT Available ID: 4
message PullOverTargetLineDebug {
  repeated voy.Point2d line_segment = 1;
  int64 front_object_id = 2;
  int64 rear_object_id = 3;
}

// Debug info for stop cells in regional pullover.
// NEXT Available ID: 7
message StopCellDebug {
  int64 stop_cell_id = 1;
  string stop_cell_type = 2;
  bool is_no_parking_cell = 3;
  repeated voy.Point2d right_boundary = 4;
  repeated voy.Point2d border = 5;
  repeated string no_parking_attrs = 6;
}

// Debug info for a pickup dropoff zone.
// NEXT Available ID: 9
message PickupDropoffZoneDebug {
  int64 zone_id = 1;
  repeated voy.Point2d reference_line = 2;
  double distance_to_destination = 3 [ deprecated = true ];
  repeated PullOverTargetLineDebug pull_over_target_lines = 4
      [ deprecated = true ];
  repeated voy.Point2d left_boundary = 5;
  repeated voy.Point2d right_boundary = 6;
  repeated voy.Point2d border = 7;
  bool is_virtual_stop = 8;
}

// Cell candidate and scores.
// Next Available ID: 27
message CellCandidateAndScore {
  // If it is selected.
  bool is_selected = 1;
  // Cell direction info.
  RouteDirectionInfo route_direction_info = 2;

  // Score for route.
  // Score of detour.
  double detour_score = 11;
  // Lane change times / distance to desired direction. Note this score would
  // not be used to calculate final score.
  double inverse_avg_lane_change_dist_score = 12;
  // Lane change to desired direction's score.
  double lane_change_to_direction_score = 13;
  // Environment's difficulty score.
  double environment_difficulty_score = 14;
  // Lane change to rightmost lane's score.
  double lane_change_to_rightmost_lane_score = 15;
  // Sum of routing related score (11 - 15).
  double route_score = 16;

  // Score for pull over.
  double pdz_length_score = 21;
  double cell_type_score = 22;
  double cell_parking_info_score = 23;
  double total_cell_score = 24;
  double pdz_position_score = 25;

  // Candidate pickup dropoff zone.
  PickupDropoffZoneDebug pickup_dropoff_zone_info = 26;
}

// Cell candidates and its routing and pullover evaluating scores list.
// Next Available ID: 2
message CellCandidatesAndScores {
  repeated CellCandidateAndScore cell_candidates_and_scores = 1;
}

// Local hdmap cell request and respond candidates.
// Next Available ID: 4
message CellRequestAndCandidate {
  // Message that queries cells.
  hdmap.ImmPullOverStopCellQueryInfo immediate_pull_over_query = 1;
  hdmap.StopCellQueryInfo cell_query = 2;
  // Respond cell candidates.
  hdmap.DirectionalCellResultGroup cell_candidate = 3;
}

// Cloud cell request and respond candidates.
// Next Available ID: 5
message CloudCellRequestAndCandidate {
  CloudCellRequest cloud_cell_request = 1 [ deprecated = true ];
  CloudCellResponse cloud_cell_response = 2 [ deprecated = true ];
  MatchStopCellRequest match_stop_cell_request = 3;
  MatchStopCellResponse match_stop_cell_response = 4;
}

// Cell request and candidate debug, could from hdmap or from cloud.
// Next Available ID: 4
message CellRequestAndCandidateDebug {
  CellRequestAndCandidate hdmap_cell_request_and_candidate = 1;
  CloudCellRequestAndCandidate cloud_cell_request_and_candidate = 2;
  CellCandidatesAndScores cell_candidates_and_scores_debug = 3;
}

// Debug info for redirecting mode immediate pull over.
// Next Available ID: 5
message RedirectingImmediatePullOverDebug {
  // Since stage could change multipe times, here record all stages.
  repeated ImmediatePullOverStage.stage stages = 1;
  // Cell request, respond candidates and final select result.
  CellRequestAndCandidateDebug cell_request_and_candidate_debug = 2;
  bool is_using_cloud_cells = 3;
  bool is_deviate_original_route = 4;
}

// Debug info for immediate pull over info.
// Next Available ID: 14
message ImmediatePullOverDebug {
  routing.pb.ImmediatePullOverState immediate_pullover_state = 1;
  voy.Point2d immediate_pull_over_point = 2;
  int64 triggered_timestamp = 3;
  int64 triggered_duration_time = 4;
  double accumulated_distance = 5;
  double initial_dist_to_destination = 6;
  ImmediatePullOverTriggerSource trigger_source = 7;
  mrc.pb.MrmType mrm_type = 8;
  bool use_redirecting_mode = 9;
  RedirectingImmediatePullOverDebug redirecting_mode_debug = 10;
  bool should_ignore_or_terminate_request = 11;
  bool should_request_immediate_right_lane_change = 12;
  bool should_request_restricted_left_lane_change = 13;
}

// Debug info for failure reason of PDZ generation.
// Next ID to use: 3
message PDZGenerationFailureReasonDebug {
  // Failed reasons.
  // NEXT Available ID: 10
  enum Type {
    kUnknownType = 0;
    kNoCloudCells = 1;                // No point cell match info from cloud.
    kNoLocalCells = 2;                // No stop cell result from SDK.
    kNoStopCellResult = 3;            // No stop cell result in pdz generator.
    kInvalidProjectionIndex = 4;      // Projection index is invalid.
    kUnknownStopCellType = 5;         // Dest cell has unknown stop cell type.
    kNoEnoughParkableCells = 6;       // Do not have enough selected stop cells.
    kInvalidExtendedNominalPath = 7;  // Invalid extended nominal path.
    kInvalidNominalPath = 8;          // Invalid nominal path.
    kInvalidPDZ = 9;                  // PDZ validation failed.
    kInvalidDestRoad = 10;            // Invalid dest road.
  }
  Type type = 1;
  string error_message = 2;
}

// Debug info for all the pickup dropoff zones.
// NEXT Available ID: 5
message PickupDropoffDebug {
  repeated PickupDropoffZoneDebug pickup_dropoff_zones = 1;
  voy.Point2d routing_destination_point = 2;
  repeated StopCellDebug stop_cells = 3;
  repeated PDZGenerationFailureReasonDebug failure_reasons = 4;
}

// Debug info for temp-parked queue recognization.
// Next Available ID: 10
message TempParkedQueueRecognitionDebug {
  repeated int64 queue_object_ids = 1;
  int64 last_lane_id = 2;
  double queue_end_lane_arclength = 3;
  bool is_ended_due_to_occlusion = 4;
  string no_occlusion_reason = 7;
  TempParkedFeatures features = 5;
  // The kFree ranges ahead of the queue. Its positions are based on the lane
  // sequence.
  repeated math.pb.Range free_ranges = 6;
  // The tolerance waiting time duration for queueing.
  int64 max_waiting_duration = 8;
  bool is_all_stationary = 9;
}

// Next Available ID: 4
message TempParkedDebug {
  repeated TempParkedQueueRecognitionDebug temp_parked_debug = 1;
  repeated TempParkedTimeRecorder start_temp_parked_timestamps = 2;
  // Records traffic queues in the last cycles.
  repeated AgentTrafficQueues last_agent_traffic_queues = 3;
}

// The lane object blocking information.
// NEXT Available ID: 19
message LaneObjectBlockingDebug {
  reserved 4, 5, 6, 8;
  reserved "confirmation_status", "start_range", "end_range", "avoiding_modes";
  int64 lane_id = 1;
  int64 object_id = 2;
  BlockingType blocking_type = 3;
  repeated BlockingAttribute blocking_attributes = 7;
  .google.protobuf.DoubleValue congestion_prob = 9;
  double left_clearance = 10;
  double right_clearance = 11;
  math.pb.Range lateral_range = 12;
  math.pb.Range longitudinal_range = 13;
  int64 static_duration = 14;
  BlockageSemanticFeatures semantic_features = 15;
  repeated RoutingBlockingAttribute blocking_attributes_for_routing = 16;
  bool is_always_moving = 17;
  bool is_never_seen_moved = 18;
}

// The debug for these blocked lane marking segments.
// NEXT Available ID: 3
message BlockedLaneMarkingSegmentDebug {
  // The blocked lane marking segment.
  pb.LaneMarkingSegment blocked_lane_marking_segment = 1;
  // The associated tracked objects.
  repeated int64 associated_object_ids = 2;
}

// The debug info for road blockage group.
// NEXT Available ID: 3
message RoadBlockageGroupDebug {
  int64 road_id = 1;
  // The road blockage group info.
  RoadBlockageGroup road_blockage_group = 2;
}

// The debug info for in lane cones.
// NEXT Available ID: 5
message InLaneConesDebug {
  // Object id for in lane cones.
  int64 cone_id = 1;
  // The blocking range along the center line of the lane.
  math.pb.Range along_lane_arclength_m = 2;
  // The clearance to the left lane marking.
  double left_clearance = 3;
  // The clearance to the right lane marking.
  double right_clearance = 4;
}

// The debug info for cone understanding.
// NEXT Available ID: 13
message LaneConesDebug {
  int64 lane_id = 1;
  // Object id for cones near left lane marking.
  repeated int64 cones_near_left_lane_marking = 2;
  // Object id for cones near right lane marking.
  repeated int64 cones_near_right_lane_marking = 3;
  // Object id for in lane cones.
  repeated int64 cones_in_lane = 4 [ deprecated = true ];
  // Object id for cones near left hard boundary or left side of road.
  repeated int64 cones_near_left_hard_boundary = 5;
  // Object id for cones near right hard boundary or right side of road.
  repeated int64 cones_near_right_hard_boundary = 6;
  // The start percentage of forbidden region.
  double forbidden_region_start_percentage = 7;
  // The end percentage of forbidden region.
  double forbidden_region_end_percentage = 8;
  // Whether the lane is partially blocked.
  bool is_partially_side_blocked = 9;
  // Whether the lane is left side blocked.
  bool is_left_side_blocked = 10;
  // Whether the lane is right side blocked.
  bool is_right_side_blocked = 11;
  // Object id, range and clearance for in lane cones.
  repeated InLaneConesDebug cones_info_in_lane = 12;
}

// The debug info for final output of lane congestion.
// NEXT Available ID: 5
message LaneCongestionMapDebug {
  int64 lane_id = 1;
  // The congestion score that updates in each frame.
  double congestion_score = 2;
  // Whether it is static congestion.
  bool is_static_congested = 3;
  // The congestion type and range for given lane.
  repeated CongestionSegmentInfoDebug congestion_segment = 4;
}

// The debug info for final output of lane object info map.
// Next Available ID: 3
message LaneObjectInfoDebug {
  int64 lane_id = 1;
  // Next Available ID: 4
  message ObstacleRangeInfo {
    // The confidence score of the obstacle range.
    double score = 1;
    // Whether the obstacle range is hard blocking.
    bool is_hard_blocking = 2;
    // The blocking range.
    math.pb.Range blocking_range = 3;
  }
  repeated ObstacleRangeInfo range_info = 2;
}

// The debug info for pull over reference destination to detect negligible
// blockages behind destination.
// NEXT Available ID: 3
message PullOverReferenceDebug {
  // The arclength of pull over reference destination point.
  double reference_destination_arclength = 1;
  repeated int64 associated_sections_behind_destination = 2;
}

// All of the potential lane blockage info.
// NEXT Available ID: 11
message LaneBlockageDebug {
  repeated LaneObjectBlockingDebug lane_object_blocking_info = 1;
  repeated BlockedLaneMarkingSegmentDebug blocked_lane_marking_info = 2;
  TempParkedDebug temp_parked_debug = 3;
  // TODO(chengruoyu): Deprecate lane_segment_sequence_congestion.
  repeated LaneSegmentSequenceCongestionDebug lane_segment_sequence_congestion =
      4;
  repeated RoadBlockageGroupDebug road_blockage_info = 5;
  TrafficInfoMap traffic_info_map_debug = 6;
  repeated LaneConesDebug cone_understanding_info = 7;
  repeated LaneCongestionMapDebug lane_congestion_map_info = 8;
  PullOverReferenceDebug pull_over_reference_debug = 9;
  LaneSlowMovingDetectorDebug lane_slowmoving_detector = 10;
}

// Indicates where the locator potential sections come from.
// NEXT Available ID: 4
enum PotentialSectionsSource {
  kUnknownPotentialSectionsSource = 0;
  // The potential sections are updated from last selected lane sequence.
  kFromLastLaneSequence = 1;
  // The potential sections are updated from regional section vertices.
  kFromSectionVertices = 2;
  // The potential sections are updated from near lanes around ego pose.
  kFromEgoPose = 3;
}

// Debug information for regional sections locator.
// NEXT Available ID: 5
message RegionalSectionsLocatorDebug {
  repeated int64 near_lane_ids = 1;
  repeated int64 potential_section_ids = 2;
  repeated int64 extend_potential_section_ids = 3;
  PotentialSectionsSource potential_section_source = 4;
}

// Factors of a current lane candidate for costing.
// NEXT Available ID: 15
message CurrentLaneCandidate {
  int64 lane_id = 1;
  double candidate_cost = 2;
  double heading_diff = 3;
  bool is_in_cost_map = 4;
  bool is_on_last_selected_lane_sequence = 5;
  bool is_on_last_target_lane_sequence = 14;
  bool is_pose_in_lane = 6;
  bool is_selected_by_locator_near_lanes = 10;
  bool is_in_locator_potential_sections = 11;
  bool is_in_extend_potential_sections = 12;
  bool is_temp_lane = 13;
  double pose_to_lane_center_dist_in_m = 7;
  double pose_percentage_along_lane = 8;
  double pose_arclength_from_lane_start = 9;
}

// Current lane candidates for waypint graph.
// NEXT Available ID: 7
message CurrentLaneForWaypointGraphDebug {
  repeated CurrentLaneCandidate current_lane_candidates = 1;
  repeated CurrentLaneCandidate current_lanes_on_last_selected_lane_sequence =
      2;
  repeated CurrentLaneCandidate pull_out_jump_out_candidates = 3;
  repeated CurrentLaneCandidate current_lanes_on_fork = 4;
  repeated CurrentLaneCandidate stuck_avoidance_jump_out_candidates = 5;
  repeated CurrentLaneCandidate prefix_associated_candidates = 6;
}

// Current lane candidates for regional path graph.
// NEXT Available ID: 2
message CurrentLaneForRegionalPathGraphDebug {
  repeated CurrentLaneCandidate current_lane_candidates = 1;
}

// The reason why backup lanes are applied.
// NEXT Available ID: 5
enum ApplyBackupLaneReason {
  NOT_APPLIED = 0;
  CURRENT_LANE_CANDIDATES_EMPTY = 1;
  MANUAL_MODE_AND_BACKUP_LANE_NOT_IN_COST_MAP = 2;
  HAS_HARD_BOUNDARY_WITH_PHYSICAL_LANES = 3;
  DRIVE_INTO_CURRENT_LANE_SUCCESSORS = 4;
}

// Debug information for if and why backup lanes are applied in current
// lane associator.
// NEXT Available ID: 4
message BackupLanesDebug {
  repeated int64 backup_lanes = 1;
  repeated CurrentLaneCandidate original_current_lane_candidates = 2;
  ApplyBackupLaneReason apply_backup_lane_reason = 3;
}

// Debug information for current lane associator.
// NEXT Available ID: 6
message CurrentLaneDebug {
  repeated CurrentLaneCandidate current_lane_candidates = 1
      [ deprecated = true ];
  repeated CurrentLaneCandidate regional_path_current_lane_candidates = 2
      [ deprecated = true ];
  CurrentLaneForWaypointGraphDebug current_lane_candidates_for_waypoint_graph =
      3;
  CurrentLaneForRegionalPathGraphDebug
      current_lane_candidates_for_regional_path_graph = 4;
  BackupLanesDebug backup_lanes_debug = 5;
}

// Congestion info in one segment of one lane
// NEXT Available ID: 5
message LaneCongestionInfoDebug {
  int64 lane_id = 1;
  double congestion_prob = 2;
  // Congestion start arclength on the center line of the lane.
  double along_lane_start_arclength_m = 3;
  // Congestion end arclength on the center line of the lane.
  double along_lane_end_arclength_m = 4;
}

// Traffic flow info in one segment of one lane
// NEXT Available ID: 6
message LaneTrafficFlowDebug {
  int64 lane_id = 1;
  double speed = 2;
  int32 object_cnt = 3;
  int32 slow_moving_cnt = 4;
  double congestion_prob = 5;
}

// Congestion type of lane segment in lane segment sequence.
// kStaticCongested means traffic flow speed is lower than 1.39m/s and congested
// range or congested vehicle num is satisfied some threshhold.
// kDynamicCongested means traffic flow speed is lower than 6.94m/s and
// congested range or congested vehicle num is satisfied some threshhold.
// kNotCongested means traffic flow not congested.
// NEXT Available ID: 4
enum CongestionType {
  kCongested = 0 [ deprecated = true ];
  kNotCongested = 1;
  kStaticCongested = 2;
  kDynamicCongested = 3;
}

// NEXT Available ID: 4
enum CongestionDataSource {
  kUnknownCongestionDataSource = 0;
  // The congestion information is from perception data.
  kFromPerception = 1;
  // The congestion information is not from perception data
  // and it is from reasoning.
  kFromReasoning = 2;
  // The congestion information is not from perception and it is
  // from empirical data.
  kFromEmpirical = 3;
}

// Lane segment info in a lane segment sequence.
// NEXT Available ID: 3
message LaneSegmentInfoDebug {
  LaneSegment lane_segment = 1;
  math.pb.Range along_lane_sequence_range_m = 2;
}

// Congestion object info in a lane segment sequence.
// NEXT Available ID: 3
message CongestionObjectInfoDebug {
  int64 object_id = 1;
  math.pb.Range along_lane_sequence_range_m = 2;
}

// Grouped congestion object info in a lane segment sequence.
// NEXT Available ID: 6
message GroupedCongestionObjectDebug {
  repeated int64 object_ids = 1;
  math.pb.Range along_lane_sequence_range_m = 2;
  double average_speed = 3;
  bool is_congested = 4;
  CongestionType congestion_type = 5;
}

// Congestion segment info in a lane or lane segment sequence.
// NEXT Available ID: 5
message CongestionSegmentInfoDebug {
  CongestionType congestion_type = 1;
  math.pb.Range along_lane_sequence_range_m = 2;
  CongestionDataSource data_source = 3;
  // It indicate whether the range is in scope of perception.
  bool is_in_perception_scope = 4;
}

// Lane segment congestion info in a lane.
// NEXT Available ID: 3
message LaneSegmentCongestionInfoDebug {
  int64 lane_id = 1;
  repeated CongestionSegmentInfoDebug congestion_segment_info = 2;
}

// Lane segment sequence traffic flow info.
// NEXT Available ID: 7
message LaneSegmentSequenceTrafficFlowDebug {
  repeated LaneSegmentInfoDebug lane_segment_info = 1;
  repeated CongestionObjectInfoDebug congestion_object_info = 2;
  repeated GroupedCongestionObjectDebug grouped_congestion_object = 3;
  repeated math.pb.Range congested_segment_range = 4 [ deprecated = true ];
  repeated LaneSegmentCongestionInfoDebug lane_segment_congestion_info = 5;
  repeated CongestionSegmentInfoDebug congested_segment_range_info = 6;
}

// Debug infomation for a lane segment sequnce's congestion info.
// NEXT Available ID: 3
message LaneSegmentSequenceCongestionDebug {
  int64 lane_segment_sequence_id = 1;
  LaneSegmentSequenceTrafficFlowDebug lane_segment_sequence_traffic_flow = 2;
}

// Debug infomation for all lanes' congestion info
// NEXT Available ID: 4
message LaneCongestionDebug {
  repeated LaneCongestionInfoDebug lane_congestion_info = 1;
  repeated LaneTrafficFlowDebug lane_traffic_flow = 2;
  repeated LaneTrafficFlowDebug raw_lane_traffic_flow = 3;
}

// Waypoint edge info with cost.
// NEXT Available ID: 5
message WaypointEdgeWithCost {
  int64 from_id = 1;
  int64 to_id = 2;
  double cost = 3;
  bool is_block = 4;
}

// Waypoint vertext.
// NEXT Available ID: 5
message WaypointVertex {
  int64 id = 1;
  voy.Point2d point = 2;
  bool is_key_point = 3;
  // Cost to destination.
  double cost = 4;
}

// The waypoint graph debug.
// Next Available ID: 3
message WaypointGraphDebug {
  repeated WaypointVertex vertexes = 1;
  repeated WaypointEdgeWithCost edges = 2;
}

// Simplified region path information.
// NEXT Available ID: 24
message SimpleRegionalPathDebug {
  repeated WaypointEdgeWithCost costs_on_current_graph = 1;
  RegionalPathCostFactors route_cost_factors = 2;
  double risky_cost = 3;
  bool is_selected = 4;
  double flicker_cost = 5;
  double progress_cost = 6;
  double total_cost = 7;
  bool diverge_with_last_cycle = 8;
  bool is_edge_sequence_blocked = 9;
  // A boolean to indicate whether the path is diverged compared to the global
  // route. If we accept the new global route directly before do Plan(), this
  // global route means the new one; otherwise, this global route means the last
  // one.
  bool diverge_with_global_route = 10;
  // A boolean to indicate whether the path is diverged with tracking_route.
  bool diverge_with_tracking_route = 22;
  // A boolean to indicate whether the path is diverged with last selected lane
  // sequence.
  bool diverge_with_last_selected_ls = 20;
  // A boolean to indicate whether the path is following loop back route.
  bool is_follow_loop_back_route = 19;
  // A boolean to indicate whether the path is diverged compared to new global
  // route. This global route always means the new global route.
  bool diverge_with_new_global_route = 12;
  // A boolean to indicate whether the path is diverged compared to preferred
  // sections.
  bool diverge_with_preferred_sections = 13;
  // A boolean to indicate whether the regional path is diverged compared to
  // variable's passable lane direction.
  bool diverge_with_variable_lane_passable_direction = 16 [ deprecated = true ];
  bool is_cross_road = 11;
  // A boolean to indicate whether the path reaches the target.
  bool reach_virtual_target = 14;
  // A boolean to indicate whether the path has risk enter constraint.
  bool has_risk_enter_constraint = 21;
  // A boolean to indicate whether ego is connectable with the path.
  bool is_ego_connectable = 15;
  // A boolean to indicate whether the regional path is along variable lane
  // direction.
  bool is_along_variable_lane_passable_direction = 17;
  // A boolean to indicate whether ego is in signal controlled variable lane.
  bool is_ego_in_signal_controlled_variable_lane = 18 [ deprecated = true ];
  // The generation source of regional path.
  RegionalPathSource source = 23;
}

// Next Available ID: 19
enum RegionalPathRerouteReason {
  // <1>Due to end_of_lane_sequence, trigger out_of_stuck.
  kGetRidOfStuck = 0;
  // <2>Last direction not exist on current cycle.
  // Due to HardBlock increasing cost too much, last direction is missed.
  kLastDirecMissDueToHardBlock = 1;
  // Ego car is near solid lane marking, LC edge is not allowed.
  kLastDirecMissNearSolidLaneMarking = 2;
  // Unkonwn state of last direction missing.
  kUnknownLastDirecMiss = 3;
  // Unknown state one of current direction come up suddenly.
  kUnknownNewDirecFindOne = 4;
  // <3>Last direction exist on current cycle.
  // Last direction has continuous_lane_change on current cycle.
  kLastDirecFindContinuousLC = 5;
  // Last direction has lane_change_to_congestion on current cycle.
  kLastDirecFindCongestionLC = 6;
  // Last direction has diverge with global route on curretn cycle.
  kLastDirecFindDivergeWithGlobal = 7;
  // Last direction has hard_block on current cycle.
  kLastDirecFindHardBlock = 8;
  // <3.1>Current direction both exist on current and last cycle.
  // Current direction has continuous_lane_change on last cycle.
  kCurrDirecMissContinuousLC = 9;
  // Current direction has lane_change_to_congestion on last cycle.
  kCurrDirecMissCongestionLC = 10;
  // Current direction has diverge with global route on last cycle.
  kCurrDirecMissDivergeWithGlobal = 11;
  // Current direction has hard_block on last cycle.
  kCurrDirecMissHardBlock = 12;
  // Unknown state of less risky cost.
  kUnknownLessRiskyCost = 13;
  // Unknown state of less progress cost.
  kUnknownLessProgressCost = 14;
  // Unknown state of less total cost.
  kUnknownLessTotalCost = 15;
  // <3.2>Current direction not exist on last cycle.
  // New direction has less risky cost.
  kNewDirecWithLessRiskyCost = 16;
  // New direction has less progress cost.
  kNewDirecWithLessProgressCost = 17;
  // Unknown state two of current direction come up suddenly.
  kUnknownNewDirecFindTwo = 18;
}

// Regional path preview information.
// NEXT Available ID: 6
message RegionalPathPreview {
  bool has_abnormal_lateral_distance = 1;
  bool has_intersection_with_left_road_boundary = 2;
  bool has_intersection_with_right_road_boundary = 3;
  bool has_intersection_with_middle_road_boundary = 4;
  // Points along the reginal path preview ployline curve.
  repeated voy.Point2d points = 5;
}

// NEXT Available ID: 6
message EgoToExitNodeCost {
  // Id of exit node.
  int64 exit_node_id = 1;
  // Total cost from ego to regional path exit node.
  double cost = 2;
  // Total estimate travel timefrom ego to regional path exit node.
  double time_s = 3;
  // Total distance from ego to regional path exit node.
  double distance_m = 4;
  // The astar path of regional path.
  repeated int64 astar_path = 5;
}

// NEXT Available ID: 5
enum TrackingRouteType {
  // The tracking route is not determined.
  kUnknownRoute = 0;
  // The tracking route is global route.
  kGlobalRoute = 1;
  // The tracking route is along last optimal regional path.
  kLastOptimalRP = 2;
  // The tracking route is a new regional path diverged from global route or
  // last optimal regional path.
  kNewRegionalPath = 3;
  // The tracking route is along last selected lane sequence.
  kLastSelectedLS = 4;
}

// NEXT Available ID: 2
message TrackingRoute { TrackingRouteType type = 1; }

// NEXT Available ID: 8
enum RegionalPathStateType {
  // The state is not set for unknown tracking_route or old trip bag in
  // simulation.
  kEmptyOrMissed = 0;
  // The global route is in changing state.
  kGlobalRouteChange = 1;
  // The tracking_route is applied with ego behavior and converged with
  // global_route.
  kTrackingGlobal = 2;
  // The tracking_route is not applied with ego behavior and diverged with
  // global_route.
  kProposingNewRP = 4;
  // The tracking_route is applied with ego behavior and diverged with
  // global_route.
  kActiveRerouted = 5;
  // The tracking_route is not applied with ego behavior and converged with
  // global_route.
  kProposingGlobal = 6;
  // The tracking_route is to be determined by regional path cost selection.
  kToBeDetermined = 7;
}

// NEXT Available ID: 4
message RegionalPathState {
  RegionalPathStateType type = 1;
  TrackingRoute tracking_route = 2;
  string trace_info = 3;
}

// Stores regional path state transitions in one frame.
// NEXT Available ID: 4
message RegionalPathStateTransition {
  // Base on last_state considering planner selection.
  RegionalPathState start_state = 1;
  // Based on start_tate considering global route change info and passable info.
  RegionalPathState interim_state = 2;
  // Based on interim_state considering regional path selection.
  RegionalPathState end_state = 3;
}

// Regional path information.
// NEXT Available ID: 49
message RegionalPathDebug {
  double distance_to_destination_m = 1;
  double time_to_destination_s = 2;
  repeated int64 path_lane_ids = 3;
  repeated int64 path_section_ids = 4;
  repeated int64 path_current_lane_ids = 5;
  int64 path_route_timestamp = 6;
  repeated voy.Polygon2d lane_borders = 7;
  repeated voy.Point2d lane_start_points = 8;
  // Angle of center_line's first point. It is used for displaying lane id.
  repeated double lane_start_point_angles = 9;
  // Cost for current pose to destination.
  double cost_to_destination = 10;
  double last_cycle_cost_to_destination = 11;
  // Current waypoint edge sequence with costs on current graph.
  repeated WaypointEdgeWithCost current_costs_on_current_graph = 12;
  // Last cycle waypoint edge sequence with costs on currnet graph.
  repeated WaypointEdgeWithCost last_cycle_costs_on_current_graph = 13;
  // The length of regional path section sequence, and the end point of
  // the length is the end of last section.
  double length = 42;
  // Itemized factors to describe this regional path.
  RegionalPathCostFactors route_cost_factors = 14;
  // If there is a regional path flicker, we will recalculate it using last
  // cycle immutable lane sequence. But we will also preserve the original path
  // with flicker and waypoint edge costs on current graph.
  // If there is not flicker, it is expected to be empty.
  repeated WaypointEdgeWithCost original_path_costs_on_current_graph = 15
      [ deprecated = true ];
  // If there is a regional path flicker, we will recalculate it using last
  // cycle immutable lane sequence. But we will also preserve the original path
  // with flicker and waypoint edge costs on current graph.
  // If there is not flicker, the field of |costs_on_current_graph| is expected
  // to be empty.
  SimpleRegionalPathDebug original_path = 17 [ deprecated = true ];
  // If the original regional path is risky, it will be dropped and a new one
  // will be computed. But a the original one will be record here. If the
  // original regional path is not risky, the field |costs_on_current_graph| is
  // expected to be empty.
  SimpleRegionalPathDebug original_risky_path = 16 [ deprecated = true ];
  // Whether can this regional path reach the destination.
  bool reach_virtual_target = 18;
  // A boolean to indicate whether the path has risk enter constraint.
  bool has_risk_enter_constraint = 45;
  // Whether ego is connectable with this regional path.
  bool is_ego_connectable = 29;
  // Waypoint graph for optimal waypoint sequence. It will only be set in
  // simulation.
  WaypointGraphDebug waypoint_graph_info = 19;
  // Blockage ranges we used when searching regioanl path.
  WaypointLaneSearchInfoDebug waypoint_lane_search_info_debug = 20;
  // Simplified debug info of all multiple regional path candidates.
  repeated SimpleRegionalPathDebug multiple_regional_path_candidates = 21;
  // Simplified debug info of all aborted regional path candidates.
  repeated SimpleRegionalPathDebug aborted_regional_path_candidates = 30;
  // A boolean to indicate whether the path is diverged compared to last cycle.
  bool diverge_with_last_cycle = 22;
  // When |diverge_with_last_cycle| is true, record the type of reroute.
  repeated RegionalPathRerouteReason regional_path_reroute_reason = 23
      [ deprecated = true ];
  // A boolean to indicate whether the path is diverged compared to the global
  // route. If we accept the new global route directly before do Plan(), this
  // global route means the new one; otherwise, this global route means the last
  // one.
  bool diverge_with_global_route = 24;
  // A boolean to indicate whether the path is diverged with tracking_route.
  bool diverge_with_tracking_route = 46;
  // A boolean to indicate whether the path is diverged with last selected lane
  // sequence.
  bool diverge_with_last_selected_ls = 43;
  // A boolean to indicate whether the path is following loop back route.
  bool is_follow_loop_back_route = 39;
  // A boolean to indicate whether the path is diverged compared to new global
  // route. This global route always means the new global route.
  bool diverge_with_new_global_route = 27;
  // A boolean to indicate whether the regional path is diverged compared to
  // preferred sections.
  bool diverge_with_preferred_sections = 28;
  // A boolean to indicate whether the regional path is diverged compared to
  // variable's passable lane direction.
  bool diverge_with_variable_lane_passable_direction = 31 [ deprecated = true ];
  // A boolean to indicate whether the current edge sequence is blocked.
  bool is_edge_sequence_blocked = 25;
  // A boolean to indicate whether the regional path is restored from seed.
  bool is_restored_from_seed = 26;
  // A boolean to indicate whether the regional path is along variable lane
  // direction.
  bool is_along_variable_lane_passable_direction = 32;
  // A boolean to indicate whether ego is in signal controlled variable lane.
  bool is_ego_in_signal_controlled_variable_lane = 37 [ deprecated = true ];
  // A boolean to indicate whether the path is fall back to avoid risk.
  bool is_fall_back = 33;
  // A boolean to indicate whether the path has risk of stuck due to continuous
  // lane changes or lane change to congestion.
  bool has_lane_change_stuck_risk = 34;
  // A boolean to indicate whether the path is cross road lane change.
  bool is_cross_road = 40;
  // A boolean to indicate whether the path has risk of harsh brake due to end
  // of lane sequence.
  bool has_harsh_brake_risk = 41;
  // A boolean to indicate whether the path contains any sections that all lanes
  // are out of cost map.
  bool has_no_cost_map_sections = 47;
  // A list of the path previews for risky regional path candidates.
  repeated RegionalPathPreview path_previews = 35;
  // Cost info from ego to exit node.
  EgoToExitNodeCost ego_to_exit_node_cost = 36;
  // The reroute scenarios of regional path when optimal regioanl path is
  // diverged with global route or last cycle.
  RerouteRequest reroute_request = 38;
  // Stores regional path state transitions and tracking route.
  RegionalPathStateTransition state_transition = 44;
  // The generation source of regional path.
  RegionalPathSource source = 48;
}

// Next Available ID: 8
enum KeyRouteSectionType {
  kNotKeySection = 0;
  // |kInJunction| is for the section in junction defined by hdmap.
  kInJunction = 1;
  // |kUturnGap| is for the section that all lanes are uturn and virtual but
  // not in junction defined by hdmap.
  kUturnGap = 2;
  // |kDestination| is for considering the section of route destination as
  // enter section which can enter a special virtual key route section.
  kDestination = 3;
  // |kForkSection| is for the section which is not in junction, and has fork
  // lane but has not merge lane.
  kForkSection = 4 [ deprecated = true ];
  // |kRightTurnOnly| is for the section which is not in junction, and is right
  // turn only section.
  kRightTurnOnly = 5;
  // |kInJunctionToEnterRoundabout| is for the section which is in junction, and
  // is to enter a roundabout. We do not regard it as a turn junction.
  kInJunctionToEnterRoundabout = 6;
  // |kInJunctionToLeaveRoundabout| is for the section which is in junction, and
  // is to leave a roundabout. We regard it as a turn junction.
  kInJunctionToLeaveRoundabout = 7;
}

// Next Available ID: 12
// Here |junction| in each field name represents Key Route Section.
// Eg: field |next_junction_id| represents next key section id.
message PoseToSectionAndJunctionInfo {
  int64 section_id = 1;
  int64 next_junction_id = 2;
  double pose_to_section_end_distance_m = 3;
  double pose_to_junction_distance_m = 4;
  int64 next_enter_section_id = 5;
  repeated int64 on_route_enter_section_lane_ids = 6;
  int64 next_turn_junction_id = 7;
  double pose_to_turn_junction_distance_m = 8;
  int64 next_enter_section_id_for_turn_junction = 9;
  KeyRouteSectionType next_junction_type = 10;
  KeyRouteSectionType next_turn_junction_type = 11;
}

// Side of route for a lane. In the enter section before a key section, if the
// route is going straight through the key section, then the going straight
// lanes are kOnRoute. Left turn lanes are kLeft, and Right turn lanes are
// kRight. If the route is going left through the key section, then except the
// left turn lanes are kOnRoute, all other lanes are kRight because they to
// right to the left turn lanes. Similar logic when the route is turning right
// through the key section, all other lanes are kLeft except the right turn
// lanes which is kOnRoute.
// Next Available ID: 4
enum SideOfRoute {
  kNone = 0;
  kOnRoute = 1;
  kLeft = 2;
  kRight = 3;
}

// Next Available ID: 32
// TODO(Kevinwangxiang): Some fields can be merged, and the logic of calculation
// is complex, we can have some tiny refactor.
message LaneToNextJunctionInfo {
  int64 lane_id = 1;
  bool reach_to_dead_end = 2;
  repeated int64 reversed_path_to_enter_section = 3;
  double remaining_forward_distance_m = 5;
  SideOfRoute side_of_route = 6;
  // The distance to next key section from lane start.
  double distance_to_junction_m = 4;
  // The minimum lane change times to pass next key section.
  int32 lane_change_times_before_junction = 7;
  // Whether has conflict merge lanes to next key section from a on route lane.
  // [Note]: In this field, we only care the conflict merge lanes which are not
  // in junction.
  bool has_conflict_merge_lanes_before_junction = 10;
  // Whether has fork lanes to next key section from a on route lane.
  bool has_fork_lanes_before_junction = 11;
  // Whether has diverged section to next key section.
  bool has_diverged_section_before_junction = 12;
  // Whether has hard blockage to next key section.
  bool has_hard_blockage_before_junction = 26;
  // The distance from lane start to left solid lane marking near key section.
  double dist_to_left_solid_lane_marking_before_junction = 13;
  // The distance from lane start to right solid lane marking near key section.
  double dist_to_right_solid_lane_marking_before_junction = 14;
  // The distance from lane start to the nearest conflict merge lanes in front
  // before key section.
  double dist_to_nearest_conflict_merge_lanes_before_junction = 20;
  // The distance from lane start to the nearest fork lanes in front before key
  // section.
  double dist_to_nearest_fork_lanes_before_junction = 21;
  // The distance from lane start to the nearest diverge section in front before
  // key section.
  double dist_to_nearest_diverged_section_before_junction = 22;
  // The distance from lane start to the nearest hard blockage in front before
  // key section.
  double dist_to_nearest_hard_blockage_before_junction = 28;
  // The distance from lane start to the farthest fork lanes in front before key
  // section.
  double dist_to_farthest_fork_lanes_before_junction = 30;
  // The Distance to next turn key section from lane start.
  double distance_to_turn_junction_m = 9;
  // The minimum lane change times to pass next turn key section.
  int32 lane_change_times_before_turn_junction = 8;
  // Whether has conflict merge lanes to next turn key section from a on route
  // lane.
  // [Note]: In this field, we only care the conflict merge lanes which are not
  // in junction.
  bool has_conflict_merge_lanes_before_turn_junction = 15;
  // Whether has fork lanes to next turn key section from a on route lane.
  bool has_fork_lanes_before_turn_junction = 16;
  // Whether has diverged section to next turn key section.
  bool has_diverged_section_before_turn_junction = 17;
  // Whether has hard blockage to next turn key section.
  bool has_hard_blockage_before_turn_junction = 27;
  // The distance from lane start to left solid lane marking near turn key
  // section.
  double dist_to_left_solid_lane_marking_before_turn_junction = 18;
  // The distance from lane start to right solid lane marking near turn key
  // section.
  double dist_to_right_solid_lane_marking_before_turn_junction = 19;
  // The distance from lane start to the nearest conflict merge lanes in front
  // before turn key section.
  double dist_to_nearest_conflict_merge_lanes_before_turn_junction = 23;
  // The distance from lane start to the nearest fork lanes in front before turn
  // key section.
  double dist_to_nearest_fork_lanes_before_turn_junction = 24;
  // The distance from lane start to the nearest diverge section in front before
  // turn key section.
  double dist_to_nearest_diverged_section_before_turn_junction = 25;
  // The distance from lane start to the nearest hard blockage in front before
  // turn key section.
  double dist_to_nearest_hard_blockage_before_turn_junction = 29;
  // The distance from lane start to the farthest fork lanes in front before
  // turn key section.
  double dist_to_farthest_fork_lanes_before_turn_junction = 31;
}

// Next Available ID: 17
// The debug information of candidate junction lanes.
message JunctionLaneSelectionInfo {
  // The candidate id.
  int64 id = 1;

  // Attributes.
  // A boolean indicates if this lane is set as the preferred lane.
  bool is_preferred = 11;
  // A boolean indicates if this lane is reachable from ego current lanes
  // without lane changes.
  bool is_on_route_from_ego = 2;
  // The enter/exit index diff of the lane.
  int32 index_diff = 3;
  // The heading diff between the junction lane and its predecessor enter
  // lane, in degree [0, 180].
  double enter_heading_diff_in_deg = 4;
  // The heading diff between the junction lane and its successor exit
  // lane, in degree [0, 180].
  double exit_heading_diff_in_deg = 5;
  // The ml planner's recommendation score; the higher the score, the more
  // likely this lane will be selected from human driver's perspective.
  double ml_planner_score = 6;
  // The in-junction alternative lane change target lane id for the
  // single-connected straight turn lane.
  // If no target lane available, it is set to 0.
  int64 alternative_lane_change_target_lane_id = 12;
  // Returns true if this junction lane might conflict with another junction
  // lane from the same enter section.
  // Currently the cases include:
  // 1. A left/u-turn, with any straight turn on its left;
  // 2. A straight turn, with any left/u-turn on its right.
  bool is_internal_conflict_turn = 13;
  // The agents from these enter lanes might have conflicts with the candidate
  // junction lane, due to potential competence for exit lanes on the same side.
  repeated int64 competitive_enter_lane_ids = 15;
  // If this junction lane does not follow guidance lines.
  bool is_violating_guidance_line = 16;

  // Costs.
  // The cost related to index diff.
  double index_diff_cost = 7;
  // The cost related to heading diff.
  double heading_diff_cost = 8;
  // The human-likeliness cost based ml planner's output.
  double ml_planner_cost = 9;
  // The cost related to risks.
  double risk_cost = 14;
  // The overall cost of the candidate.
  double final_cost = 10;
}

// Regional map information.
// NEXT Available ID: 27
message RegionalMapDebug {
  RegionalPathDebug regional_path = 1;
  SectionSequenceState section_sequence_state = 2;
  repeated int64 current_lane_ids = 3 [ deprecated = true ];
  repeated int64 road_ids = 4;
  repeated int64 zone_ids = 5;
  repeated int64 trimmed_section_ids = 6;
  // Note: Equivalent to regional map's lane_sequence_searchable_sections' ids.
  repeated int64 lane_change_section_ids = 7;
  repeated int64 lane_follow_section_ids = 8 [ deprecated = true ];
  // Based on lane_change_sections (lane_sequence_searchable_sections), extend
  // sections to global route's first turn junction.
  repeated int64 extended_section_ids = 21;
  // The detail information for the extened section sequence. It is
  // corresponding with |extended_section_ids|.
  repeated ElementVisualizeInfo extended_sections = 23;
  repeated int64 crosswalk_ids = 9;
  double max_distance_to_exit_section_m = 10;
  map<int64, PoseToSectionAndJunctionInfo>
      pose_to_section_and_junction_info_map = 11;
  map<int64, LaneToNextJunctionInfo> lane_to_junction_info_map = 12;
  // Broken traffic light information. It contains a list of land ids who are
  // associated with broken traffic light and the update timestamp.
  routing.pb.BrokenTrafficLightsInfo broken_traffic_lights_info = 13;
  // The parameter for stuck avoidance.
  StuckAvoidanceParameter stuck_avoidance_parameter = 14;
  repeated SectionVertex regional_section_vertices = 15;
  // All regional section vertices including exits.
  repeated SectionVertex regional_exit_section_vertices = 26;
  // The LaneSelectionPreference stores params for lane preference.
  LaneSelectionPreference lane_selection_preference = 16;
  // The preferred section sequence for regional path.
  PreferredSectionSequenceInfo preferred_section_sequence = 17;
  // The parameter to relax the lane marking segments.
  RelaxLaneChangeForBlockageOnGlobalParameter
      relax_lane_change_for_global_param = 18;
  repeated JunctionLaneSelectionInfo junction_lane_selection_infos = 19;
  repeated int64 expected_replan_lane_ids = 22;
  // A boolean indicate whether should add backup lane follow sequence to
  // planner selection.
  bool should_add_backup_lf_sequence_to_selection = 25;

  //
  // Deprecated fields.
  //
  reserved 24;
  reserved "should_add_backup_ls_to_selection";
}

// Regional map debug info.
// NEXT Available ID: 2
message RegionalMapInfoDebug { RegionalMapDebug regional_map = 1; }

// Debug info for Avoidance range.
// NEXT Available ID: 5
message AvoidanceRangeDebug {
  int64 start_lane_id = 1;
  double start_arclength = 2;
  int64 end_lane_id = 3;
  double end_arclength = 4;
}

// Debug info for violable lane marking ranges.
// NEXT Available ID: 3
message ViolableLaneMarkingRangeDebug {
  int64 lane_marking_id = 1;
  math.pb.Range violable_range = 2;
}

// Debug info for stuck avoidance guidance info.
// NEXT Available ID: 4
message StuckAvoidanceGuidanceDebug {
  AvoidanceRangeDebug avoidance_range_debug = 1;
  repeated ViolableLaneMarkingRangeDebug violable_lane_marking_ranges_debug = 2;
  bool is_solid_line_crossable = 3;
}

// Debug info for pull out confirmation state.
// NEXT Available ID: 14
message PullOutConfirmDebug {
  speed.pb.PulloutReasonerSeed seed_debug = 1;
  double order_start_position_distance = 2;
  bool is_ego_position_on_route_ready_for_pull_out = 3;
  voy.Pose ride_start_pose = 4;
  double destination_distance = 5;
  // True if the route status is not from replanning querey.
  bool is_ride_query = 6;
  bool is_ego_start_within_drivable_lane = 7 [ deprecated = true ];
  bool is_ego_start_close_to_stop = 8;
  repeated int64 arrived_road_ids_from_ride_start = 9;
  PullOutJumpOutSeed pull_out_jump_out_seed = 10;
  bool has_pull_out_jump_out_current_lanes = 11;
  bool has_drivable_physical_lanes = 12;
  bool should_generate_pull_out_jump_out_sequence = 13;
}

// Debug info for compensated pose.
// NEXT Available ID: 4
message CompensatedPose {
  double center_z = 1;
  double height = 2;
  repeated voy.Point2d contour = 3;
}

// The debug message for one PlannerObject class.
message PerPlannerObjectDebug {
  // The value of importance_rank_to_ego from the prediction output.
  int64 prediction_agent_rank = 1;
}

// The debug message for offroad objects.
message OffRoadObjectDebug {
  repeated int64 ignored_offroad_objects = 1;
  int64 last_off_road_path_timestamp = 2;
}

// Debug info for planner objects in the world model.
// NEXT Available ID: 10
message PlannerObjectDebug {
  int64 perception_object_timestamp_ms = 1;
  int64 plan_init_timestamp_ms = 2;
  int64 perception_object_plan_init_lag_time_ms = 3;
  repeated CompensatedPose compensated_poses = 4;
  map<int64, PerPlannerObjectDebug> per_planner_obj_debug = 5;
  bool is_high_agent_density = 6;
  bool is_high_crossing_vru_density = 9;
  OffRoadObjectDebug off_road_object_debug = 7;
  repeated int64 ignored_by_fpobstacle_tracker_objects = 8;
}

// The reason of path generation failure.
// Next Available ID: 13
enum PathFailureType {
  PATH_VALID = 0;
  POINTS_NOT_INCREMENTAL = 1;
  NOT_END_IN_LANE_FOLLOW_SEQUENCE = 2;
  GENERATE_NOMINAL_PATH_FAILED = 3;
  PATH_VIOLATE_LEFT_HARD_BOUNDARY = 4;
  PATH_VIOLATE_RIGHT_HARD_BOUNDARY = 5;
  POINTS_SMALL_SIZE = 6;
  POINTS_VALID_SIZE_SMALL = 7;
  END_POINT_NOT_IN_DRAWABLE_AREA = 8;
  INVALID_LANE_SEQUENCE = 9;
  POINT_INVALID_BY_ROUTING = 10;
  POINTS_FARAWAY_FORM_EGO = 11;
  INVALID_DRAWABLE_AREA = 12;
}

// Indicates waypoint assist status.
// Next Available ID: 3
enum WaypointAvailability {
  // Signal indicates planner is not allowed to enter waypoint assist.
  NOT_AVAILABLE = 0;
  // Signal indicates planner can enter waypoint assist.
  AVAILABLE = 1;
  // Signal indicates planner had already entered waypoint assist.
  ALREADY_IN_WAYPOINT = 2;
}

// Indicates reason when waypoint assist is not available.
// Next Available ID: 5
enum ReasonWaypointAssistNotAvailable {
  // Default value.
  NOT_SET = 0;
  // Signal indicates ego is not stationary.
  EGO_NOT_STATIONARY = 1;
  // Signal indicates the scenario in which need to acc the static agent in ego
  // lane.
  STATIC_ACC_OBJECT = 2;
  // Signal indicates the scenario of end_of_lane_sequence.
  END_OF_LANE_SEQUENCE = 3;
  // Signal indicates the scenario that ego is close to hard boundary.
  CLOSE_TO_HARD_BOUNDARY = 4;
}

// Waypoint trigger routing replan info
// Next Available ID: 4
message WaypointRecommendRoutingReplan {
  // Status
  bool is_triggered = 1;
  // Next Available ID: 5
  enum ReasonForNotRecommend {
    NOT_SET = 0;
    FLAG_NOT_SET = 1;
    EGO_NOT_STATIONARY = 2;
    HAS_NOT_STARTED_REMOTE_ASSIST = 3;
    LANE_SEQUENCE_NORMAL = 4;
  }
  ReasonForNotRecommend reason_for_not_recommend = 2;
  repeated int64 recommend_lane_ids = 3;
}

// Info of borrowing opposite lanes.
// Next Available ID: 5
message WaypointBorrowOppositeLanes {
  bool is_ego_in_oppsite_road = 1;
  bool is_road_entirely_blocked = 2;
  bool is_last_waypoint_in_opposite_lanes = 3;
  repeated int64 opposite_lane_ids = 4;
}

// The debug for waypoint assist.
// Next Available ID: 22
message WaypointAssistDebug {
  reserved 11;
  reserved "exit_by_reverse_driving";

  AssistManeuverPhase.Enum assist_maneuver_phase = 1;
  bool has_received_waypoints = 2;
  AssistPathGenerationStatus.Enum path_generation_status = 3;
  bool has_arrived_path_end = 4;
  int64 enter_way_point_time_ms = 5;
  int64 exit_way_point_time_ms = 6;
  PathFailureType path_failure_type = 7;
  string current_command = 8;
  string current_command_sender = 17;
  int64 command_count_in_one_cycle = 9;
  WaypointAvailability waypoint_availability = 10;
  // TODO(ZHE): Removes assist_responses_size after finding root cause.
  int64 assist_responses_size = 12;
  bool is_reverse_driving_active = 13;
  // Indicates reason when the waypoint assist is not available.
  ReasonWaypointAssistNotAvailable reason_waypoint_assist_not_available = 14;
  // Indicates lane sequence proposal has been responded.
  bool has_lane_sequence_proposal_responded = 15;
  // The ids of ignored cz decided by waypoint assist blockage analyzer.
  repeated int64 ignored_cz_ids = 16;
  // Indicates waypoint recommend routing replan lane sequence info.
  WaypointRecommendRoutingReplan waypoint_recommend_routing_replan = 18;
  // Indicates waypoint borrow opposite lanes info.
  WaypointBorrowOppositeLanes waypoint_borrow_opposite_lanes = 19;
  // Indicates start stuck time when waypoint assist is in kExitReasoning.
  int64 start_stuck_time_in_exit_reasoning_ms = 20;
  // Indicates whether ego has reached the waypoint assist lane sequence when
  // return to kIdle phase.
  EgoReachTargetLaneSequenceState.HasReached
      has_ego_reached_target_lane_sequence = 21;
}

// The debug for light assist.
// Next Available ID: 2
message LightAssistDebug {
  // Indicates reason when the light assist is not available.
  LightAssistAvailability light_assist_availability = 1;
}

// The debug for agent's mutiple trajectroy route associations.
// Next Available ID: 2
message AgentPredictedTrajectoryRoutesDebug {
  map<int64, MultiPredictedTrajectoryRouteAssociationDebug>
      agent_predicted_trajectory_routes = 1;
}

// Next Available ID: 3
message SignalControlledVariableLaneInfoDebug {
  SignalControlledVariableLaneInfo signal_controlled_variable_lane_info = 1;
  // If the info is from perception. If not, it is from seed.
  bool is_from_perception = 2;
}

// The debug for signal controlled variable lane.
// Next Available ID: 3
message SignalControlledVariableLaneDebug {
  reserved 1;
  reserved "variable_lane_infos";
  // Variable lane info from perception's result.
  repeated SignalControlledVariableLaneInfoDebug
      perception_variable_lane_infos = 2;
  // Variable lane info merged between perception's result and seed, which would
  // be used by drivable lane reasoner.
  repeated SignalControlledVariableLaneInfoDebug merged_variable_lane_infos = 3;
}

// The debug for traffic signal reasoner.
// Next Available ID: 2
message TrafficSignalReasonerDebug {
  SignalControlledVariableLaneDebug signal_controlled_variable_lane_info = 1;
}

// The debug for lanes mandatory drivable or undrivable info.
// Next Available ID: 3
message MandatoryDrivableInfoDebug {
  // The lane segments that are undrivable and set as drivable.
  repeated pb.LaneSegment mandatory_drivable_lane_segments = 1;
  // The lane segments that are drivable and set as undrivable.
  repeated pb.LaneSegment mandatory_undrivable_lane_segments = 2;
}

// Debug message for drivable lane reasoner.
// Next Available ID: 3
message DrivableLaneReasonerDebug {
  MandatoryDrivableInfoDebug mandatory_drivable_info = 1 [ deprecated = true ];
  DirvableLaneReasonerState mandatory_drivable_reasoner_state = 2;
}

// Reason why planner snapshot nodelet updates route status.
// Next Available ID: 9
enum RouteStatusUpdateReason {
  // Not update for planner node.
  kNotUpdateInPlanner = 0;
  // Update because first get valid route status, usually for first time get
  // route status from routing node.
  kRouteStatusChangedFromNullptrToValid = 1;
  // Update because global route changed.
  kGlobalRouteChanged = 2;
  // Update becasue waypoint info changed.
  kRouteWaypointInfoChanged = 3;
  // Update becasue RTM's broken traffic light updated.
  kRTMBrokenTrafficLightUpdated = 4;
  // Update because immeidate pull over info changed.
  kImmediatePullOverInfoChanged = 5;
  // Update because immediate pull over state changes to kSuccess.
  kImmediatePullOverSuccess = 6;
  // Update because special planner response request changed.
  kSpecialPlannerResponseRequestChanged = 7;
  // Update because special planner response request changed to let ego keep in
  // static state.
  kSpecialPlannerResponseRequestChangedToKeepStatic = 8;
}

// Slim version of RouteStatus, here only save important route status feature
// for planner node.
// Next Available ID: 14
message PlanningRouteStatusDebug {
  // Indicates the distance and time are valid, user of those should check this
  // flag.
  bool is_valid = 1;
  // Latest proposed route solution to planning.
  routing.pb.RouteSolution proposed_route_solution = 2;
  // Current used route solution which is accepted by planning. If it is same as
  // proposed route solution, we will not fill this field.
  optional routing.pb.RouteSolution current_route_solution = 3;
  // Distance to destination in meters.
  double dist_to_dest_m = 4;
  // Time to destination in seconds.
  double time_to_dest_s = 5;
  // Distance to destination in meters. It will keep coincident with local
  // route.
  double dist_to_dest_in_local_m = 6;
  // Time to destination in seconds. It will keep coincident with local route.
  double time_to_dest_in_local_s = 7;
  // Info that used for immediate pull over, includes its state, parking
  // location and source of request.
  routing.pb.ImmediatePullOverInfo immediate_pullover_info = 8;
  // Reason why update route status.
  RouteStatusUpdateReason update_reason = 9;
  // Routing status's special planner response request.
  // Note if special request is kKeepEgoInStaticState, three possible states:
  // 1. Last cycle: other request, curr cycle: kKeepEgoInStaticState request:
  // Replace proposed and current route solution from last cycle.
  // 2. Last cycle: kKeepEgoInStaticState request, curr cycle:
  // kKeepEgoInStaticState request: Not update route solution. (Still use not
  // empty solution)
  // 3. Last cycle: kKeepEgoInStaticState request, curr cycle: other request:
  // Normal update route solution.
  routing.pb.SpecialPlannerResponseRequest special_planner_response_request =
      10;
  // The times of global route has looped back.
  int64 loopback_times = 11;
  // Whether current route solution is a new trip.
  bool is_new_trip = 12;
  // Whether current route solution is changed if mission_id changed, road
  // sequence changed.
  bool is_global_route_changed = 13;
}

// Indicates the current lane state in the global route, such as whether ego can
// reach destination from current lane.
// Next Available ID: 2
message CurrentLaneStateDebug {
  enum State {
    // NEXT Available ID: 3
    NOT_SET = 0;
    REACHABLE = 1;
    NOT_REACHABLE = 2;
  }
  State current_lane_reach_destination_state = 1;
}

// Globla route related info that used by planner node. May include route
// status, route solution, etc.
// Next Available ID: 3
message GlobalRouteDebug {
  PlanningRouteStatusDebug planning_route_status_debug = 1;
  CurrentLaneStateDebug current_lane_state_debug = 2;
}

// Debug for map change area processor.
// Next Available ID: 2
message MapChangeAreaProcessorDebug {
  MapChangeAreaHandleParameter map_change_area_processor_param_debug = 1;
}

// Debug for assist traffic light unstuck tracker for map change area.
// Next Available ID: 10
message AssistTrafficLightUnstuckTrackerDebug {
  reserved 4;
  reserved "accumulated_s_left";
  // If the ego in unstuck mode about traffic light map change area.
  bool in_unstuck = 1;
  // The last state of in junction.
  bool last_in_junction = 2;
  // The last state of within traffic light map change area.
  bool last_within_ignored_areas = 3;
  // The accumulate movement since left map change area in meter.
  double accumulated_odom_left = 7;
  // The accumulate movement since the ego starts to move in meter.
  double accumulated_odom = 8;
  // The timestamp since in unstuck state, from pose in ms.
  int64 timestamp_in_unstuck = 5;
  // The enum of reason about exit unstuck mode.
  // Next Available ID: 8
  enum ExitReason {
    kNone = 0;
    kOpsCommand = 1;
    kNotInAuto = 2;
    kExitJunction = 3;
    kExitMapChangeAreaFarEnough = 4;
    kTimeout = 5;
    kMovedFarEnough = 6;
    kExitAssist = 7;
  }
  // The exit reason.
  ExitReason exit_reason = 6;
  // The enum of unstuck scene.
  // Next Available ID: 3
  enum UnstuckScene {
    kUnknown = 0;
    kTrafficLightMapChange = 1;
    kAbnormalTrafficLight = 2;
  }
  UnstuckScene unstuck_scene = 9;
}

// Assist ignore obstacle tracker for stuck scence about perception
// FP.
// Next Available ID: 2
message AssistIgnoreObstacleTrackerDebug {
  // Ignored obstacle commands.
  repeated IgnoreObstacleCommand ignored_obstacles = 1;
}

// Debug for assist blockage analyzer.
// Next Available ID: 3
message AssistBlockageAnalyzer {
  // Debug about traffic light map change area tracker.
  AssistTrafficLightUnstuckTrackerDebug traffic_light_tracker_debug = 1;
  // Debug about ignored obstacle tracker for perception FP.
  AssistIgnoreObstacleTrackerDebug ignore_obstacle_tracker_debug = 2;
}

// Debug for route replan manager.
// Next Available ID: 3
message ReplanManagerDebug {
  routing.pb.QueryFollowedPathType followed_path_type = 1;
  repeated string effective_triggers = 2;
}

// Debug for routing replan.
// Next Available ID: 3
message ReplanDebug {
  // Replan route query is generated in world model and may be used as planner
  // route commend in current frame.
  routing.pb.RouteQuery replan_route_query = 1;
  // Replan manager debug is generated during getting followed path type which
  // is used to determine whether replan or not in next frame.
  ReplanManagerDebug replan_manager_debug = 2;
}

// Debug for destination successor sections info.
// Next Available ID: 3
message DestinationSuccessorSectionsInfoDebug {
  repeated int64 extended_section_ids = 1;
  double pullover_final_extend_section_percentage = 2;
}

// A pb to record completed lane change info.
// Next Available ID: 4
message CompletedLaneChangeInfo {
  // Timestamp when lane change completed, in msec.
  int64 completed_timestamp = 1;
  // Completed lane change instance.
  LaneChangeInstance completed_LC_instance = 2;
  // Accumulated distance since |completed_timestamp| in meter.
  double accumulated_distance = 3;
}

// A pb to record passed junction info.
// Next Available ID: 4
message PassedJunctionInfo {
  // Timestamp when ego exits junction, in msec.
  int64 passed_timestamp = 1;
  // Exited junction info.
  TurnInfo passed_turn_info = 2;
  // Accumulated distance since |completed_timestamp| in meter.
  double accumulated_distance = 3;
}

// Debug for previoud action recorder.
// Next Available ID: 3
message PreviousActionRecorderDebug {
  repeated CompletedLaneChangeInfo completed_LC_infos = 1;
  repeated PassedJunctionInfo passed_junction_infos = 2;
}

// Debug for the robot state.
// Next Avaliable ID: 2
// TODO(planner): Add more debug infos if needed.
message RobotStateDebug { int64 ego_start_stationary_timestamp = 1; }

// Debug info for world model.
// NEXT Available ID: 32
message WorldModelDebug {
  reserved 1, 21;
  reserved "super_nudge_debug", "assist_traffic_light_unstuck_tracker_debug";

  PickupDropoffDebug pickup_dropoff_debug = 2;
  LaneBlockageDebug lane_blockage_debug = 3;
  // Records the message timestamp of the snapshot.
  repeated SubscribedMessageInfo subscribed_message_infos = 4;
  RegionalMapInfoDebug regional_map_info_debug = 5;
  StuckAvoidanceGuidanceDebug stuck_avoidance_guidance_debug = 6;
  RegionalSectionsLocatorDebug regional_sections_locator_debug = 30;
  CurrentLaneDebug current_lane_debug = 7;
  LaneCongestionDebug lane_congestion_debug = 8;
  PullOutConfirmDebug pull_out_confirm_debug = 9;
  PlannerObjectDebug planner_object_debug = 10;
  WaypointAssistDebug waypoint_assist_debug = 11;
  LightAssistDebug light_assist_debug = 18;
  AgentPredictedTrajectoryRoutesDebug agent_prediction_route_debug = 12;
  ImmediatePullOverDebug immediate_pull_over_debug = 13;
  MandatoryDrivableInfoDebug mandatory_drivable_lane_info_debug = 14
      [ deprecated = true ];
  GlobalRouteDebug global_route_debug = 15;
  DrivableLaneReasonerDebug drivable_lane_reasoner_debug = 16;
  TrafficSignalReasonerDebug traffic_signal_reasoner_debug = 17;
  MapChangeAreaProcessorDebug map_change_area_processor_debug = 19;
  hdmap.ViolateLaneMarkings violate_lane_marking_singleton_debug = 20;
  // The debug message for pnc map changed elements.
  pnc_map.pb.ChangedElements changed_elements = 22;
  StuckAvoidanceReasonerState stuck_avoidance_reasoner_debug = 23;
  AssistBlockageAnalyzer assist_blockage_analyzer_debug = 24;
  ReplanDebug replan_debug = 25;
  WaypointLanesCacheState waypoint_lanes_cache_debug = 26;
  DestinationSuccessorSectionsInfoDebug
      destination_successor_section_info_debug = 27;
  PreviousActionRecorderDebug previous_action_recorder_debug = 28;
  RobotStateDebug robot_state_debug = 29;
  ChangedLaneStructureReasonerState changed_lane_structure_debug = 31;
}
