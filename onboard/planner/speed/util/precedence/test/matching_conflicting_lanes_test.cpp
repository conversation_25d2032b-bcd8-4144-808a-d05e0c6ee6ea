#include "planner/speed/util/precedence/matching_conflicting_lanes.h"

#include <vector>

#include <gtest/gtest.h>

#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner_protos/overlap.pb.h"
#include "prediction_protos/agent.pb.h"

namespace planner {
namespace speed {
namespace util {

class MatchingConflictingLanesFixtureTest : public ::testing::Test,
                                            public ReasoningTestFixture {
 public:
  MatchingConflictingLanesFixtureTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(MatchingConflictingLanesFixtureTest, UnmatchConflictingLanTest) {
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.8);
  CreateLaneFollowPath(/*extend_backward=*/false);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE,
      /*id=*/1, math::Pose2d(/*x_in=*/0.0, /*y_in=*/0.0, /*yaw_in=*/0.0),
      /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/10.0);
  // Construct predicted trajectory.
  prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddPredictedTrajectory(agent);
  predicted_trajectory.set_id(1);
  prediction::pb::Maneuver* maneuver = predicted_trajectory.mutable_maneuver();
  maneuver->set_source_lane_id(1);
  maneuver->add_target_lane_ids(1);
  maneuver->add_target_lane_ids(2);

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp(),
                                                    agent.tracked_object());
  std::optional<traffic_rules::ConflictingLaneInLaneSequence>
      ego_associated_conflicting_lane =
          GetConflictingLaneBetweenPredictedTrajectoryAndEgoLane(
              planner::PlannerObject(agent, timestamp(), pose_at_plan_init_ts),
              planner::PredictedTrajectoryWrapper(agent.tracked_object(),
                                                  predicted_trajectory),
              trajectory_info()
                  .traffic_rules()
                  .lane_sequence_conflicting_lanes.front());

  // The predicted trajectory does NOT have any associated conflicting lane with
  // ego lane sequence as Ego conflicting lanes are empty.
  EXPECT_FALSE(ego_associated_conflicting_lane.has_value());
}

TEST_F(MatchingConflictingLanesFixtureTest,
       MatchConflictingLaneTestWithTargetLanes) {
  constexpr int64_t kSourceLaneId = 97551;
  constexpr int64_t kEgoConflictingLaneId = 108353;

  SetEgoPose(/*lane_id=*/17049, /*portion=*/0.1);
  CreateLaneFollowPath(/*extend_backward=*/false);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE,
      /*id=*/1, math::Pose2d(/*x_in=*/0.0, /*y_in=*/0.0, /*yaw_in=*/0.0),
      /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/10.0);
  // Construct predicted trajectory.
  prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddPredictedTrajectory(agent);
  predicted_trajectory.set_id(1);

  prediction::pb::Maneuver* maneuver = predicted_trajectory.mutable_maneuver();
  maneuver->set_source_lane_id(kSourceLaneId);
  maneuver->add_target_lane_ids(kSourceLaneId);
  maneuver->add_target_lane_ids(kEgoConflictingLaneId);

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp(),
                                                    agent.tracked_object());
  std::optional<traffic_rules::ConflictingLaneInLaneSequence>
      associated_conflicting_lane =
          GetConflictingLaneBetweenPredictedTrajectoryAndEgoLane(
              planner::PlannerObject(agent, timestamp(), pose_at_plan_init_ts),
              planner::PredictedTrajectoryWrapper(agent.tracked_object(),
                                                  predicted_trajectory),
              trajectory_info()
                  .traffic_rules()
                  .lane_sequence_conflicting_lanes.front());

  // The predicted trajectory has an associated conflicting lane with
  // ego lane sequence.
  DCHECK(associated_conflicting_lane.has_value());
  EXPECT_EQ(associated_conflicting_lane.value().conflicting_lane().id(),
            kEgoConflictingLaneId);
}

TEST_F(MatchingConflictingLanesFixtureTest,
       MatchConflictingLaneTestWithoutTargetLanes) {
  [[maybe_unused]] constexpr int64_t kEgoConflictingLaneId = 97505;

  SetEgoPose(/*lane_id=*/17049, /*portion=*/0.1);
  CreateLaneFollowPath(/*extend_backward=*/false);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE,
      /*id=*/1, math::Pose2d(/*x_in=*/-4511, /*y_in=*/-2586, /*yaw_in=*/0.0),
      /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/10.0);
  // Construct predicted trajectory.
  prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddPredictedTrajectory(agent);
  predicted_trajectory.set_id(1);

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp(),
                                                    agent.tracked_object());
  std::optional<traffic_rules::ConflictingLaneInLaneSequence>
      associated_conflicting_lane =
          GetConflictingLaneBetweenPredictedTrajectoryAndEgoLane(
              planner::PlannerObject(agent, timestamp(), pose_at_plan_init_ts),
              planner::PredictedTrajectoryWrapper(agent.tracked_object(),
                                                  predicted_trajectory),
              trajectory_info()
                  .traffic_rules()
                  .lane_sequence_conflicting_lanes.front());

  // The predicted trajectory has an associated conflicting lane with
  // ego lane sequence by rules.
  EXPECT_TRUE(!associated_conflicting_lane.has_value());
  // TODO(waylon): Enable it when we will add predicted trajectories for
  // constructed agent.
  /*
EXPECT_EQ(associated_conflicting_lane.value().conflicting_lane.id(),
      kEgoConflictingLaneId);*/
}

TEST_F(MatchingConflictingLanesFixtureTest,
       GetConflictingLanesBetweenPredictedTrajectoryAndEgoLaneSequence) {
  constexpr int64_t kSourceLaneId = 97551;
  constexpr int64_t kEgoConflictingLaneId = 108353;

  SetEgoPose(/*lane_id=*/17049, /*portion=*/0.1);
  CreateLaneFollowPath(/*extend_backward=*/false);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE,
      /*id=*/1, math::Pose2d(/*x_in=*/0.0, /*y_in=*/0.0, /*yaw_in=*/0.0),
      /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/10.0);
  // Construct predicted trajectory.
  prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddPredictedTrajectory(agent);
  predicted_trajectory.set_id(1);
  prediction::pb::Maneuver* maneuver = predicted_trajectory.mutable_maneuver();
  maneuver->set_source_lane_id(kSourceLaneId);
  maneuver->add_target_lane_ids(kSourceLaneId);
  maneuver->add_target_lane_ids(kEgoConflictingLaneId);

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp(),
                                                    agent.tracked_object());
  std::vector<traffic_rules::ConflictingLaneInLaneSequence>
      associated_conflicting_lanes =
          GetConflictingLanesBetweenPredictedTrajectoryAndEgoLaneSequence(
              planner::PlannerObject(agent, timestamp(), pose_at_plan_init_ts),
              planner::PredictedTrajectoryWrapper(agent.tracked_object(),
                                                  predicted_trajectory),
              trajectory_info()
                  .traffic_rules()
                  .lane_sequence_conflicting_lanes);

  // The predicted trajectory has an associated conflicting lane with
  // ego lane sequence.
  EXPECT_FALSE(associated_conflicting_lanes.empty());
  EXPECT_EQ(associated_conflicting_lanes.size(), 1);
  EXPECT_EQ(associated_conflicting_lanes.front().conflicting_lane().id(),
            kEgoConflictingLaneId);
}

TEST_F(MatchingConflictingLanesFixtureTest,
       GetConflictingLanesForLongPredictedTrajectory) {
  constexpr int64_t kEgoConflictingLaneId = 108205;
  SetEgoPose(/*lane_id=*/3859, /*portion=*/0.1, /*speed=*/10.0);
  CreatePathWithLaneSequence({3859, 108349},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  ObjectId object_id = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, object_id,
               /*lane_id=*/108201, /*portion=*/0.8,
               /*length=*/2.5, /*width=*/1.5,
               /*velocity=*/10.0);
  prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/108201,
                                     /*start_portion=*/0.8,
                                     /*end_lane_id=*/108133,
                                     /*end_portion=*/0.2,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp(),
                                                    agent.tracked_object());
  std::vector<traffic_rules::ConflictingLaneInLaneSequence>
      associated_conflicting_lanes =
          GetConflictingLanesBetweenPredictedTrajectoryAndEgoLaneSequence(
              planner::PlannerObject(agent, timestamp(), pose_at_plan_init_ts),
              planner::PredictedTrajectoryWrapper(agent.tracked_object(),
                                                  predicted_trajectory),
              trajectory_info()
                  .traffic_rules()
                  .lane_sequence_conflicting_lanes);
  // The predicted trajectory has an associated conflicting lane with
  // ego lane sequence.
  EXPECT_FALSE(associated_conflicting_lanes.empty());
  EXPECT_EQ(associated_conflicting_lanes.size(), 1);
  EXPECT_EQ(associated_conflicting_lanes.front().conflicting_lane().id(),
            kEgoConflictingLaneId);
}

}  // namespace util
}  // namespace speed
}  // namespace planner
