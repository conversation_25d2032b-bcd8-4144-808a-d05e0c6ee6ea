#include "planner/speed/solver/searcher/profile_searcher_util.h"

#include <algorithm>
#include <limits>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>

#include <tbb/parallel_for.h>

#include "planner/speed/agent_reaction/agent_reaction_calculator/agent_reaction_calculator.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/solver/util/gap_align_search_util.h"
#include "rt_event/rt_event.h"
#include "strings/stringprintf.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {
namespace {
// The max number of tree search iteration to avoid infinite loop.
constexpr int kMaxTreeSearchIterationNum = 20;
// The max number of calling FindFirstConflictForProfile to resolve conflicts on
// a certain branch of profile. This is to avoid infinity loop and planner
// timeout.
constexpr int kMaxNumOfConfictAdjustment = 200;
// A default invalid number to show for data fields that are invalid or not
// explicitly set in intermediate search debug.
constexpr double kInvalidNum = -999;
// In a binary search, the diff of the selected discomfort and the optimal
// selected discomfort is less than this value.
constexpr double kDiscomfortResolution = 0.1;
// The maximum allowed search times to find discomfort for a constraint.
constexpr int kMaxIterationToSearchDiscomfort = 1000;
// This value is used in the calculation whether the initial accel
// can achieve the discomfort varying limits within this tolerance.
constexpr double kInitialAccelOverLimitsToleranceInMpss = 0.1;
// The shortest time duration when ego is almost static to consider triggering
// stay stopped logic.
constexpr double kStaticSpeedDurationToConsiderStayStoppedInSecond = 1.0;
// The minimum average speed of the lead agent for ego to early exit the stay
// stop logic.
constexpr double kLeadAgentSpeedToFollowInMps = 1.0;
// The threshold to early exit stay-stopped for one constraint, considering
// the constraint's yield entries (that require ego stay-stopped) will be no
// more blocking. It should be an approximate value corresponding to the control
// look-ahead time.
constexpr double kEarlyExitStayStoppedThresholdInSec = 1.5;
// Plan init state speed cannot be larger than this value when stay stopped.
// Otherwise, there might be risks by ignoring pass decision.
constexpr double kMaxAllowedSpeedToIgnoreConflictedConstraintWhenStayStopInMps =
    0.5;
// Returns yield distance buffer to calculate distance for stay stopped logic.
double GetYieldBuffer(const Constraint& constraint,
                      const DiscomfortVaryingMinRange& min_range,
                      double discomfort) {
  return constraint.settings.yield_extra_distance(discomfort) +
         min_range.at(constraint.settings, discomfort,
                      /*is_pass=*/false);
}

// Returns true if the |state| is inside the no block constraint and has a speed
// less than no_block_min_speed.
bool ShouldConsiderAsBlockingNoBlockRegion(const State& state,
                                           double no_block_min_speed,
                                           double no_block_start_time,
                                           double no_block_start_x,
                                           double no_block_end_x) {
  return (state.t > no_block_start_time) && (state.x > no_block_start_x) &&
         (state.x < no_block_end_x) && (state.v < no_block_min_speed);
}

// Returns true if any yield entry is inside a no block constraint after
// |enter_no_block_time| which is w.r.t ego first state time (planning initial
// state time).
bool YieldEntryExistsInsideNoBlockAfterTime(
    double no_block_constraint_start_x, double no_block_constraint_end_x,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions, const TimeMap& time_map,
    double enter_no_block_time) {
  int enter_no_block_time_ix = -1;

  // NOTE(Tingran): When enter_no_block_time is earlier or equal to plan init
  // state time, we regard that there is not yield entry after ego enters the no
  // block, which is not true. This may result in decision change when ego is at
  // the start pos of a no block constraint. Revisit the logic here.
  if (!time_map.time_mapper().MaybeGetTimeIx(
          enter_no_block_time, /*is_pass=*/false, &enter_no_block_time_ix)) {
    return false;
  }

  // Return true if it finds a yield constraint after entering the no block
  // constraint.
  for (auto iter = time_map.yield_entries().upper_bound(enter_no_block_time_ix);
       iter != time_map.yield_entries().end(); ++iter) {
    const std::vector<TimeMap::Entry>& entries = iter->second;
    if (std::any_of(entries.begin(), entries.end(),
                    [&constraints, &decisions, no_block_constraint_start_x,
                     no_block_constraint_end_x](const TimeMap::Entry& entry) {
                      return constraints.at(entry.constraint_ix).type !=
                                 pb::Constraint::NO_BLOCK &&
                             decisions.at(entry.constraint_ix) ==
                                 pb::SpeedDecision::YIELD &&
                             entry.start_or_end_x >
                                 no_block_constraint_start_x &&
                             entry.start_or_end_x < no_block_constraint_end_x;
                    })) {
      return true;
    }
  }
  // Not found.
  return false;
}

// Populates all data fields into |iterative_profile_debug|.
void PopulateIterativeProfileDebug(
    const bool adjust_for_tree_branch, const Profile& result_profile,
    const int64_t conflict_constraint_ix,
    const std::string& conflict_constraint_id, const Decision conflict_decision,
    const double t_conflict, const double x_conflict, const double t_brake,
    const double x_resolve, const pb::IterativeProfile::AdjustType adjust_type,
    const std::vector<Decision>& curr_decisions,
    const int64_t tree_switch_time_ix,
    pb::IterativeProfile& iterative_profile_debug) {
  iterative_profile_debug.set_adjust_for_tree_branch(adjust_for_tree_branch);
  *iterative_profile_debug.mutable_result_profile() = ToProto(result_profile);
  iterative_profile_debug.set_conflict_constraint_ix(conflict_constraint_ix);
  iterative_profile_debug.set_conflict_constraint_id(conflict_constraint_id);
  iterative_profile_debug.set_conflict_decision(conflict_decision);
  iterative_profile_debug.set_t_conflict(t_conflict);
  iterative_profile_debug.set_x_conflict(x_conflict);
  iterative_profile_debug.set_t_brake(t_brake);
  iterative_profile_debug.set_x_resolve(x_resolve);
  iterative_profile_debug.set_adjust_type(adjust_type);
  iterative_profile_debug.set_tree_switch_time_ix(tree_switch_time_ix);
  for (const auto& decision : curr_decisions) {
    iterative_profile_debug.mutable_curr_decisions()->Add(decision);
  }
}

void ResolveNotDecidedDecisionToPass(
    const Profile& profile, const bool adjust_for_tree_branch,
    std::vector<Decision>& decisions,
    pb::PerDiscomfortDebug* intermediate_result_debug) {
  // Resolve all not-decided decisions to pass.
  for (pb::SpeedDecision& decision : decisions) {
    if (decision == Decision::NOT_DECIDED) {
      decision = Decision::PASS;
    }
  }
  if (intermediate_result_debug != nullptr) {
    const int size = intermediate_result_debug->iterative_profiles_size();
    DCHECK_NE(size, 0);
    const pb::IterativeProfile& iterative_profile =
        intermediate_result_debug->iterative_profiles(size - 1);
    const int conflict_constraint_ix =
        iterative_profile.conflict_constraint_ix();
    const std::string conflict_constraint_id =
        iterative_profile.conflict_constraint_id();
    const Decision conflict_decision = iterative_profile.conflict_decision();
    // Add one iterative profile to resolve not decided decisions.
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        adjust_for_tree_branch, profile, conflict_constraint_ix,
        conflict_constraint_id, conflict_decision,
        /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
        /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
        /*adjust_type=*/pb::IterativeProfile::kResolveNotDecided, decisions,
        /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
  }
}

// Updates |profile| and |decisions| when other_earliest_brake_time_ix is before
// tree_switch_time_ix. Only update |decisions| whose corresponding decisions
// were updated in the last iterative search.
// |profile| and |decisions| are both I&O params.
void ConsolidateTreeSearchSolutionBeforeSwitchTime(
    const std::vector<Constraint>& constraints, const Profile& other_profile,
    const std::vector<Decision>& other_decisions,
    const std::vector<int>& other_updated_decision_indices,
    const int other_earliest_brake_time_ix, const bool adjusted_for_pass,
    const int last_tree_switch_time_ix, const int tree_switch_time_ix,
    const double planning_time_horizon_in_second,
    const bool adjust_for_tree_branch, Profile& profile,
    std::vector<Decision>& decisions,
    pb::PerDiscomfortDebug* intermediate_result_debug) {
  if (adjusted_for_pass) {
    // If we adjust the profile for pass conflict, the profile must have been
    // adjusted from time ix 0, so we need to copy the whole profile from
    // adjustment first time ix 0.
    profile = other_profile;
  }
  if (other_earliest_brake_time_ix >= tree_switch_time_ix) {
    if (last_tree_switch_time_ix != tree_switch_time_ix) {
      DCHECK_GT(tree_switch_time_ix, last_tree_switch_time_ix);
      // If tree switch time ix is changed, we need to update profile.
      // Ideally we just want to copy the profile from tree_switch_time_ix. We
      // copy the whole to simplify the logic. The adjustment of profile later
      // should grow towards reference from tree_switch_time_ix.

      // This should and MUST happen in main profile search. The reason is that
      // when switch time changes, we have to re-adjust main profiles from
      // tree profile so that the main profile earliest brake time ix can be
      // correctly populated. Note that we should NOT update the
      // decisions because decision change after switch time will not affect its
      // decision in the main profile search, even if switch time was updated.
      profile = other_profile;
      if (intermediate_result_debug != nullptr) {
        // Add one iterative profile to resolve not decided decisions.
        pb::IterativeProfile* iterative_profile_debug =
            intermediate_result_debug->add_iterative_profiles();
        PopulateIterativeProfileDebug(
            adjust_for_tree_branch, profile,
            /*conflict_constraint_ix=*/static_cast<int>(kInvalidNum),
            /*conflict_constraint_id=*/"NA",
            /*conflict_decision=*/Decision::NOT_DECIDED,
            /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
            /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
            /*adjust_type=*/
            pb::IterativeProfile::kUpdateProfileForNewSwitchTime, decisions,
            tree_switch_time_ix, *iterative_profile_debug);
      }
    }
    return;
  }
  DCHECK_LT(other_earliest_brake_time_ix, tree_switch_time_ix);
  // Sync profile and decisions that were updated in the last iterative search.
  // Ideally we just want to copy the profile from tree_switch_time_ix. We
  // copy the whole to simplify the logic.
  profile = other_profile;

  // Update decisions.
  for (const int decision_ix : other_updated_decision_indices) {
    DCHECK_LT(decision_ix, decisions.size());
    DCHECK_GE(decision_ix, 0);
    if (IsValidRiskMitigationConstraint(constraints[decision_ix],
                                        planning_time_horizon_in_second)) {
      // Do not consolidate RM constraint's decision because we want to keep
      // IGNORE in main decisions for all RM constraint. If a dominant
      // constraint is a Risk Mitigation constraint, its risk decision will be
      // YIELD while its main decision remains IGNORE.
      continue;
    }
    decisions[decision_ix] = other_decisions[decision_ix];
  }
  if (intermediate_result_debug != nullptr) {
    // Add one iterative profile to resolve not decided decisions.
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        adjust_for_tree_branch, profile,
        /*conflict_constraint_ix=*/static_cast<int>(kInvalidNum),
        /*conflict_constraint_id=*/"NA",
        /*conflict_decision=*/Decision::NOT_DECIDED,
        /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
        /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
        /*adjust_type=*/
        pb::IterativeProfile::kUpdateProfileForBrakeBeforeSwitchTime, decisions,
        tree_switch_time_ix, *iterative_profile_debug);
  }
}

// Gets adjust type from the conflict constraint's decision.
pb::IterativeProfile::AdjustType GetAdjustTypeFromDecision(
    const Decision conflict_decision) {
  switch (conflict_decision) {
    case Decision::NOT_DECIDED:
      return pb::IterativeProfile::kNotDecidedToYield;
    case Decision::PASS:
      return pb::IterativeProfile::kPassToIgnore;
    case Decision::YIELD:
      return pb::IterativeProfile::kBrakeForYield;
    case Decision::IGNORE:
      return pb::IterativeProfile::kStayIgnore;
    default:
      break;
  }
  return pb::IterativeProfile::kUnknown;
}

// Calculates the closest fence postion to ego based on the
// yield distance of standoffs and selected discomfort.
double GetFencePositionForStandOffs(const Standoffs& standoffs,
                                    const double selected_discomfort,
                                    const double rear_axle_to_front_bumper_m) {
  double fence_pos_m = std::numeric_limits<double>::max();
  for (const auto& standoff : standoffs) {
    fence_pos_m = std::min(fence_pos_m, standoff.yield_x(selected_discomfort));
  }
  // Return the fence position from front bumper.
  return FLAGS_planning_enable_overlap_v2
             ? fence_pos_m
             : fence_pos_m - rear_axle_to_front_bumper_m;
}

// Returns a unique key for the lead and tail object that form the
// search_solution's gap_info.
std::string GetGapKey(const SearchSolution& search_solution) {
  DCHECK(search_solution.gap_info.has_value());
  const int tail_obj_c_ix = search_solution.gap_info.value().tail_obj_c_ix;
  const int lead_obj_c_ix = search_solution.gap_info.value().lead_obj_c_ix;
  return absl::StrFormat("%d,%d", tail_obj_c_ix, lead_obj_c_ix);
}

// Returns an std::set of unique gap keys from |search_solutions|. Note that
// the size of the set may be smaller than |search_solutions| size as gaps may
// be repetitive.
// TODO(Tingran): Make these check-unique-gap functions into hash-based instead
// of string-based in the future.
std::set<std::string> GetUniqueGapKeys(
    const std::vector<SearchSolution>& search_solutions) {
  std::set<std::string> unique_gap_keys;
  for (const auto& search_solution : search_solutions) {
    unique_gap_keys.insert(GetGapKey(search_solution));
  }
  return unique_gap_keys;
}

// Returns true if the gap in |curr_search_solution| in new to the gaps in
// |prev_search_solutions|.
bool IsNewGap(const SearchSolution& curr_search_solution,
              const std::vector<SearchSolution>& prev_search_solutions) {
  const auto unique_gap_keys = GetUniqueGapKeys(prev_search_solutions);
  const auto curr_gap_key = GetGapKey(curr_search_solution);
  return unique_gap_keys.find(curr_gap_key) == unique_gap_keys.end();
}

// Returns true if we should try to adjust profile for pass using the method of
// finding earliest time to accelerate from a full brake profile.
// Three conditions should be satisfied:
// 1. There is a prev_yield_conflict. It means that there was something making
// us brake thus here is a potential over yielding.
// 2. The earliest brake time ix is not 0. It means that there is potential room
// for us to adjust.
// 3. The current pass conflict happens after the previous yield conflict. This
// means that we should find the earliest time to accelerate to get the most
// greedy profile to pass the current pass conflict as long as it can still
// yield to the prev yield conflict. Refer to the design doc:
// https://docs.google.com/document/d/1LO3uuJcuuy_P9S4RYB-fQs1Aljnn4gORYtPkiYHQCD0/edit#heading=h.po6zqm7nnpdr
bool ShouldTryEarliestAccelFromBrakeProfile(
    const Conflict& prev_yield_conflict, const Conflict& curr_pass_conflict,
    const ProfileBrakeMeta& profile_brake_meta) {
  return prev_yield_conflict.constraint_ix >= 0 &&
         profile_brake_meta.earliest_brake_time_ix > 0 &&
         curr_pass_conflict.time_ix > prev_yield_conflict.time_ix;
}

// Updates acceleration/brake related info from profile brake meta into the
// variables in search solution.
void UpdateAccelBrakeInfo(
    const int profile_brake_meta_earliest_brake_time_ix,
    const int profile_brake_meta_earliest_brake_constraint_ix,
    const int profile_brake_meta_earliest_accel_time_ix_for_adjust_for_pass,
    int& earliest_brake_time_ix, int& earliest_brake_cix,
    int& earliest_accel_time_ix_for_adjust_for_pass) {
  if (math::UpdateMin(profile_brake_meta_earliest_brake_time_ix,
                      earliest_brake_time_ix)) {
    earliest_brake_cix = profile_brake_meta_earliest_brake_constraint_ix;
  }
  DCHECK_GE(profile_brake_meta_earliest_accel_time_ix_for_adjust_for_pass, 0);
  math::UpdateMin(profile_brake_meta_earliest_accel_time_ix_for_adjust_for_pass,
                  earliest_accel_time_ix_for_adjust_for_pass);
}

void UpdatePerDiscomfortDebug(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const int earliest_brake_cix, const int earliest_brake_time_ix,
    const int earliest_accel_time_ix_for_adjust_for_pass,
    const FailReasonConstraint& current_fail_info, const bool succeed,
    pb::PerDiscomfortDebug* new_discomfort_debug) {
  if (new_discomfort_debug != nullptr) {
    if (FLAGS_planning_enable_intermediate_speed_search_result_debug) {
      *(new_discomfort_debug->mutable_time_map()) =
          per_discomfort_problem.time_map.ToProto();
      for (const Decision& decision :
           per_discomfort_problem.per_discomfort_initial_decisions) {
        new_discomfort_debug->mutable_initial_decisions()->Add(decision);
      }
    }
    new_discomfort_debug->set_discomfort_idx(
        per_discomfort_problem.discomfort_ix);
    new_discomfort_debug->set_discomfort_level(
        per_discomfort_problem.discomfort);
    if (succeed) {
      new_discomfort_debug->set_dominant_constraint_ix(earliest_brake_cix);
      new_discomfort_debug->set_earliest_brake_time_ix(earliest_brake_time_ix);
      new_discomfort_debug->set_earliest_accel_time_ix_for_adjust_for_pass(
          earliest_accel_time_ix_for_adjust_for_pass);
    } else {
      new_discomfort_debug->set_dominant_constraint_ix(
          current_fail_info.constraint_ix);
      new_discomfort_debug->set_earliest_brake_time_ix(-1);
      new_discomfort_debug->set_earliest_accel_time_ix_for_adjust_for_pass(-1);
    }
    new_discomfort_debug->set_success(succeed);
    new_discomfort_debug->set_discomfort_increase_source(
        current_fail_info.reason);
  }
}

void PopulateRiskMitigationDebug(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const SearchSolution& per_discomfort_search_solution, const double dt,
    const int num_steps, pb::RiskMitigationDebug* risk_mitigation_debug) {
  const double risk_discomfort = GetRiskMitigationDiscomfort(
      per_discomfort_problem.discomfort, per_discomfort_problem.constraints,
      /*planning_time_horizon_in_second=*/dt * num_steps);
  risk_mitigation_debug->set_risk_discomfort(risk_discomfort);
  risk_mitigation_debug->set_risk_switch_time_ix(
      per_discomfort_search_solution.tree_switch_time_ix);
  risk_mitigation_debug->set_risk_mitigation_active(
      per_discomfort_search_solution.risk_mitigation_active);

  // Generate min and max reachable profiles.
  // NOTE(Tingran): Here is a difference between risk_ref_profile and
  // call AccelerateTowardsReferenceProfileWithProfileLimits to accel.
  // Risk ref profile is generated purely using elevated discomfort from the
  // first state, while accelerating with profile limits only use elevated
  // discomfort after switch time. Same for the min profile. Therefore we need
  // to generate the "reachable" profiles for debug which matches the logic that
  // we use in risk search.

  // Initialize min speed profile with ref profile.
  Profile min_speed_profile = per_discomfort_problem.ref_profile;
  // Generate brake profile by braking at the first state using profile limits.
  GenerateBrakeProfileWithProfileLimits(
      /*brake_first_ix=*/0, /*brake_end_ix=*/num_steps,
      per_discomfort_problem.profile_limits, dt, /*for_risk_search=*/true,
      &min_speed_profile);

  // Initialize max speed profile with ref profile.
  Profile max_speed_profile = per_discomfort_problem.ref_profile;
  // Fix the profile before tree switch time ix, and accelerate after it.
  AccelerateTowardsReferenceProfileWithProfileLimits(
      base::CheckAndGetValue(per_discomfort_problem.risk_sampled_ref_profile),
      per_discomfort_problem.profile_limits,
      per_discomfort_search_solution.tree_switch_time_ix, dt,
      /*for_risk_search=*/true, num_steps,
      /*max_v=*/std::nullopt, max_speed_profile);

  // Add profiles to debug.
  *risk_mitigation_debug->mutable_risk_max_speed_profile() =
      ToProto(max_speed_profile);
  *risk_mitigation_debug->mutable_risk_profile() =
      ToProto(per_discomfort_search_solution.tree_profile);
  *risk_mitigation_debug->mutable_risk_min_speed_profile() =
      ToProto(min_speed_profile);
  risk_mitigation_debug->mutable_risk_decisions()->Reserve(
      per_discomfort_search_solution.tree_decisions.size());
  for (const Decision decision :
       per_discomfort_search_solution.tree_decisions) {
    risk_mitigation_debug->add_risk_decisions(decision);
  }
  *risk_mitigation_debug->mutable_risk_time_map() =
      base::CheckAndGetValue(per_discomfort_problem.risk_time_map).ToProto();
}

// Returns the number of gap align constraints that have YIELD decisions.
int GetYieldGapAlignDecisionCount(
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const std::vector<Decision>& decisions) {
  int count = 0;
  for (const auto& [c_ix, _] : gap_align_info_map) {
    if (decisions[c_ix] == Decision::YIELD) {
      ++count;
    }
  }
  return count;
}

// Returns true if the requested gap diversity has been satisfied in the
// |prev_search_solutions|. Populates the |slowest_solution_ix| which has the
// most yield decisions for gap align constraints.
// |need_slower_gap| is true if we are checking if the slower gap diversity
// request has been satisfied. False if we are checking faster gap diversity
// request.
bool GapDiversityIsSatisfied(
    const bool need_slower_gap,
    const std::vector<SearchSolution>& prev_search_solutions,
    const GapDiversitySettings& gap_diversity_settings,
    int& slowest_solution_ix) {
  const SearchSolution& first_search_solution = prev_search_solutions.front();
  std::set<std::string> unique_gap_keys;
  unique_gap_keys.insert(GetGapKey(first_search_solution));
  const int default_gap_yield_decision_num = GetYieldGapAlignDecisionCount(
      first_search_solution.per_discomfort_gap_align_info_map,
      first_search_solution.decisions);
  // Check if we should search for faster/slower gap.
  int slowest_yield_decision_num = default_gap_yield_decision_num;
  for (size_t i = 1; i < prev_search_solutions.size(); ++i) {
    const SearchSolution& search_solution = prev_search_solutions[i];
    const int curr_gap_yield_decision_num = GetYieldGapAlignDecisionCount(
        search_solution.per_discomfort_gap_align_info_map,
        search_solution.decisions);
    if (need_slower_gap
            ? (curr_gap_yield_decision_num <= default_gap_yield_decision_num)
            : (curr_gap_yield_decision_num >= default_gap_yield_decision_num)) {
      // If it does not have more / less yield decisions for gap align
      // constraints, continue;
      continue;
    }
    const std::string gap_key = GetGapKey(search_solution);
    // NOTE: We may be checking a solution accepted by progress or
    // faster gap option. So it is not guaranteed to be a unique gap even if it
    // has different amount of yield decisions.
    if (unique_gap_keys.find(gap_key) != unique_gap_keys.end()) {
      // If the gap is not a new one, continue;
      continue;
    }
    unique_gap_keys.insert(gap_key);
    if (need_slower_gap) {
      // When checking for slower gaps, update |slowest_solution_ix| if the new
      // gap has more yield decisions.
      // NOTE: We may be checking a solution accepted by progress or
      // faster gap option. So it is not guaranteed that the higher discomfort
      // solution with a unique gap must have
      // more yield decisions.
      if (math::UpdateMax(curr_gap_yield_decision_num,
                          slowest_yield_decision_num)) {
        slowest_solution_ix = i;
      }
    }
  }
  const int extra_gap_amount =
      need_slower_gap
          ? gap_diversity_settings.extra_gap_amount_slower_than_default_gap
          : gap_diversity_settings.extra_gap_amount_faster_than_default_gap;
  return static_cast<int>(unique_gap_keys.size()) >= extra_gap_amount + 1;
}

double GetFeasibleDiscomfortLowerBound(
    const std::vector<Constraint>& constraints, const int c_ix,
    const double min_discomfort, const double global_discomfort_for_progress) {
  const bool has_creep_constraint = std::any_of(
      constraints.begin(), constraints.end(), [](const Constraint& constraint) {
        return constraint.settings.is_creep;
      });
  if (!has_creep_constraint) {
    return min_discomfort;
  }

  const Constraint& constraint = constraints[c_ix];
  const double per_constraint_discomfort_for_progress =
      constraint.settings.is_creep ? constraint.settings.discomfort_for_progress
                                   : min_discomfort;
  return std::max({min_discomfort, per_constraint_discomfort_for_progress,
                   global_discomfort_for_progress});
}

double GetFinalizedDiscomfortForARConstraint(
    const double lower_bound, const double profile_discomfort,
    const pb::TreeSearchType::Enum tree_search_type, const Profile& profile,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const std::vector<Constraint>& constraint_vec,
    const std::vector<Decision>& final_decision_vec,
    const DiscomfortVaryingMinRange& min_range,
    const AgentReactionCalculator& agent_reaction_calculator,
    const ConstraintTransformer& constraint_transformer) {
  if (lower_bound >= profile_discomfort) {
    return profile_discomfort;
  }
  int discomfort_ix = Discomforts::GetDiscomfortIx(profile_discomfort) - 1;
  for (; discomfort_ix >= Discomforts::GetDiscomfortIx(lower_bound);
       --discomfort_ix) {
    const TimeMap time_map = constraint_transformer.GenerateTimeMapWithArCache(
        constraint_vec, final_decision_vec, min_range, profile_discomfort,
        Discomforts::GetDiscomfort(discomfort_ix), agent_reaction_calculator);
    Conflict unused_conflict;
    const bool has_conflict = FindFirstConflictForProfile(
        tree_search_type, time_map, profile, constraint_vec, final_decision_vec,
        /*search_for_conflict_start_ix=*/0, gap_align_info_map,
        /*tree_switch_time_ix=*/time_map.steps(),
        /*adjust_for_tree_branch=*/false, &unused_conflict);
    if (has_conflict) {
      break;
    }
  }
  return Discomforts::GetDiscomfort(discomfort_ix + 1);
}

// Returns true and populates |finalize_discomfort| if we need special
// handlings for constraint finalization. Currently there are two conditions:
// 1. A soft constraint that violates with the ML profile in the inverse search.
// 2. A constraint with AR in the inverse search.
bool ShouldSpeciallyFinalizeDiscomfortInInverseSearch(
    const double lower_bound, const pb::TreeSearchType::Enum tree_search_type,
    const Profile& profile,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const bool is_pass, const std::vector<Constraint>& constraint_vec,
    const std::vector<Decision>& final_decision_vec,
    const DiscomfortVaryingMinRange& min_range, const double profile_discomfort,
    const AgentReactionCalculator& agent_reaction_calculator,
    const ConstraintTransformer& constraint_transformer,
    const InitialDecisionProvider& initial_decision_provider,
    double& finalize_discomfort) {
  DCHECK_EQ(constraint_vec.size(), 1);
  DCHECK_EQ(final_decision_vec.size(), 1);
  const Constraint& constraint = constraint_vec[0];
  // SOFT constraint.
  if ((is_pass ? constraint.settings.pass_option
               : constraint.settings.yield_option) ==
      PassYieldOption::ConstraintSettings_PassYieldOption_SOFT) {
    const TimeMap time_map = constraint_transformer.GenerateTimeMapWithArCache(
        constraint_vec, final_decision_vec, min_range, profile_discomfort,
        agent_reaction_calculator);
    Conflict unused_conflict;
    if (FindFirstConflictForProfile(
            tree_search_type, time_map, profile, constraint_vec,
            final_decision_vec,
            /*search_for_conflict_start_ix=*/0, gap_align_info_map,
            /*tree_switch_time_ix=*/time_map.steps(),
            /*adjust_for_tree_branch=*/false, &unused_conflict)) {
      // SOFT with violation in inverse search. Need special discomfort
      // finalization.
      finalize_discomfort = Discomforts::GetDiscomfort(
          initial_decision_provider
              .GetDiscomfortIxForInitialProfileAdjustedConstraint(
                  constraint.settings, is_pass));
      return true;
    }
  }

  // Need to calculate AR for this constraint.
  if (!agent_reaction_calculator.NoNeedToCalculateAr(
          constraint.settings, Discomforts::kMax, is_pass)) {
    finalize_discomfort = GetFinalizedDiscomfortForARConstraint(
        lower_bound, profile_discomfort, tree_search_type, profile,
        gap_align_info_map, constraint_vec, final_decision_vec, min_range,
        agent_reaction_calculator, constraint_transformer);
    return true;
  }

  return false;
}

// Returns true if we basic check is satisfied for adjusting constraint
// discomfort.
bool SatisfyBasicCheckForAdjustingDiscomfort(
    const bool for_inverse_search, const Constraint& constraint,
    const Decision& decision, const int c_ix, const int dominant_constraint_ix,
    const double profile_discomfort, const double t_horizon) {
  if (math::NearZero(profile_discomfort - Discomforts::kMin)) {
    // Discomfort is already the lowest.
    return false;
  }

  if (!for_inverse_search && c_ix == dominant_constraint_ix) {
    // Do not further adjust discomfort for dominant constraint.
    // TODO(Tingran): Find motivating cases to fine tune the logic.
    return false;
  }

  if (decision != Decision::YIELD && decision != Decision::PASS) {
    // If decision is neither yield nor pass, it must be ignore.
    DCHECK(decision == Decision::IGNORE);
    // Discomfort does not matter if the constraint is ignored.
    return false;
  }
  if (constraint.type == ConstraintType::Constraint_Type_NO_BLOCK) {
    // Do not adjust discomfort for no block constraint.
    // TODO(Tingran): we can keep this for now, but for no-block, if we pass, it
    // should be profile discomfort. if we yield, we can still reduce
    // discomfort.
    return false;
  }

  if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
    // TODO(Tingran): to make gap_align v0 simple, Do not adjust for gap_align
    // now.
    return false;
  }

  if (IsValidRiskMitigationConstraint(constraint, t_horizon)) {
    // We only do optimization for regular constraints and RM constraints are
    // ignored. If it is dominant, and also means its decision can not be
    // adjusted.
    return false;
  }

  return true;
}

Profile PopulateStopProfile(const State& search_profile_first_state,
                            int num_steps, const Limits& limits, double dt) {
  Profile stopping_profile;
  stopping_profile.reserve(num_steps);
  // Initialize the stopping profile.
  stopping_profile.push_back(search_profile_first_state);
  GenerateBrakeProfile(/*brake_first_ix=*/0, /*brake_end_ix=*/80, limits, dt,
                       &stopping_profile);
  return stopping_profile;
}

// Returns true if according to the search profile, ego is coming to stop.
// Checks the last 1s profile states at planning horizon, returns true if the
// average speed is less than kSpeedSolverStaticSpeedInMps.
// TODO(Tingran): Remove this function if stay stop speed and coming to stop has
// the same speed threshold because then it will no longer return true there. If
// so, we will rely on optimizer to naturally handle the trajectory from motion
// to a full stop. Stay stop should only judge if ego should start to move from
// fully static state.
bool IsComingToStopProfile(const Profile& search_profile) {
  constexpr int kTotalProfileStatesToCheck = 10;
  const int profile_size = search_profile.size();
  DCHECK(!search_profile.empty()) << "Search profile cannot be empty.";
  if (search_profile.front().v < constants::kSpeedSolverStaticSpeedInMps) {
    // Ego is already at very low speed. So we regard it as static.
    return false;
  }

  double average_speed = 0.0;
  const int check_state_size =
      std::min(profile_size, kTotalProfileStatesToCheck);
  for (int i = 1; i <= check_state_size; ++i) {
    average_speed += search_profile[profile_size - i].v;
  }
  average_speed /= check_state_size;
  return average_speed < constants::kSpeedSolverStaticSpeedInMps;

  // TODO(Tingran): Return true when ego triggers stay stop signal at the first
  // few cycles.
}

// A small struct to store the stay-stopped information of a constraint for
// local usage.
struct ShouldStayStoppedConstraintInfo {
  size_t constraint_ix = 0;
  double time_should_exit_stay_stopped = -std::numeric_limits<double>::max();
  double closest_fb_dist_to_constraint = std::numeric_limits<double>::max();
};

// Iterates through all states of one constraint, and compares the yield entries
// to the required distance of min creep (including buffers). Returns the time
// when the ego should exit stay stopped w.r.t. this constraint (i.e. after the
// time, ego does not need to stay stopped to any further entries of the
// constraint). Returns a large negative value if no entry of the constraint
// requires ego to stay stopped. Also updates |closest_ra_dist_to_speed_obj| if
// the constraint is from a speed object.
double GetTimeShouldExitStayStopped(const Constraint& constraint,
                                    const double req_min_creep_dist,
                                    double& closest_ra_dist_to_speed_obj) {
  const double selected_discomfort = constraint.selected_discomfort;
  const double yield_req_lat_gap =
      constraint.settings.yield_required_lat_gap(selected_discomfort);
  const bool is_speed_obj = IsSpeedObjectConstraint(constraint);
  double time_should_exit_stay_stopped = -std::numeric_limits<double>::max();
  for (size_t ix = 0; ix < constraint.states.size(); ++ix) {
    const ConstraintState& state = constraint.states[ix];
    if (state.abs_lat_gap > yield_req_lat_gap || state.end_time < 0.0) {
      continue;
    }
    const double state_x = state.start_x(selected_discomfort);
    // Ego should stay-stopped until this time.
    if (state_x < req_min_creep_dist) {
      time_should_exit_stay_stopped = state.end_time;
      if (is_speed_obj) {
        // Note: the closest_ra_dist_speed_obj would not impact whether ego
        // should stay-stopped or not. It only impacts the extended_stopping_x
        // calculation in PopulateStayStopInfo() if ego is_coming_to_stop.
        math::UpdateMin(state_x, closest_ra_dist_to_speed_obj);
      }
    }
  }
  return time_should_exit_stay_stopped;
}

// Checks if ego should quick-start to a certain should-stay-stopped constraint.
bool ShouldQuickStartToOneConstraint(const Constraint& constraint,
                                     const double time_should_exit_stay_stopped,
                                     std::string& stay_stop_debug_str) {
  absl::StrAppendFormat(&stay_stop_debug_str, "Constraint ID: %s,",
                        constraint.unique_constraint_id);
  if (!IsSpeedObjectConstraint(constraint)) {
    absl::StrAppend(&stay_stop_debug_str,
                    "Not speed object. Not quick-start-to-follow.\n");
    return false;
  }
  // For one constraint, we can quick-start if the time to exit stay-stopped
  // w.r.t. the constraint is already close (aka. the blocking yield entries
  // will disappear soon).
  if (time_should_exit_stay_stopped < kEarlyExitStayStoppedThresholdInSec) {
    absl::StrAppendFormat(
        &stay_stop_debug_str,
        "Should exit stay-stopped after %.2f sec. Early quick-start.\n",
        time_should_exit_stay_stopped);
    return true;
  }
  absl::StrAppendFormat(&stay_stop_debug_str,
                        "Should exit stay-stopped after %.2fs. ",
                        time_should_exit_stay_stopped);

  // For the remaining cases, check if reasoning explicitly set
  // quick-start-to-follow for the constraint.
  if (constraint.fence_type != pb::FenceType::kLead) {
    // If the closest yield constraint's fence type is not kLead, we should not
    // speed up to catch.
    absl::StrAppend(&stay_stop_debug_str,
                    "Fence_type not kLead. Not quick-start-to-follow.\n");
    return false;
  }
  if (!constraint.settings.quick_start_to_follow) {
    absl::StrAppend(&stay_stop_debug_str,
                    "Leader but explicitly not quick-start-to-follow.\n");
    return false;
  }
  // TODO(Tingran): Improve this heuristic according to future motivation cases.
  double lead_agent_speed = 0.0;
  const int state_size = constraint.states.size();
  // Get the average |lead_agent_speed|.
  for (int i = 0; i < state_size; ++i) {
    // Assuming lead object is continuous so we do not check if abs_lat_gap <
    // required lateral gap.
    const ConstraintState& state = constraint.states[i];
    lead_agent_speed += state.signed_longitudinal_speed;
  }
  DCHECK_NE(state_size, 0);
  lead_agent_speed /= state_size;
  const bool is_lead_agent_fast_start_up =
      lead_agent_speed >= kLeadAgentSpeedToFollowInMps;
  absl::StrAppendFormat(&stay_stop_debug_str,
                        "avg lead_agent_speed: %.2f, %s\n", lead_agent_speed,
                        is_lead_agent_fast_start_up
                            ? "Quick-start-to-follow.\n"
                            : "Not quick-start-to-follow.\n");

  return is_lead_agent_fast_start_up;
}

void PopulateStayStopInfo(const Profile& search_profile,
                          const Profile& stopping_profile,
                          const DiscomfortVaryingMinRange& min_range,
                          const Constraint& constraint,
                          const double closest_fb_dist_to_constraint,
                          const double ra_to_fb_shift, bool& is_coming_to_stop,
                          double& desired_stopping_x) {
  is_coming_to_stop = IsComingToStopProfile(search_profile);
  // Ego is static, we need to attract to the stopping profile's last
  // state.
  if (is_coming_to_stop) {
    // When ego is coming to stop, we need the ego to stop a bit closer to
    // the closest agent by reducing the min creep distance. So when the
    // lead agent starts to creep a bit or the perception bounding box
    // flickers longitudinally a bit, there is a distance buffer for ego to
    // continue staying stop.
    const double min_creep_distance = constraint.settings.min_creep_distance;
    const double selected_discomfort = constraint.selected_discomfort;
    const double min_creep_plus_yield_buffer_dist =
        FLAGS_planning_enable_overlap_v2
            ? min_creep_distance +
                  GetYieldBuffer(constraint, min_range, selected_discomfort)
            : ra_to_fb_shift + min_creep_distance +
                  GetYieldBuffer(constraint, min_range, selected_discomfort);
    constexpr double kComingToStopNegativeBufferInMeter = -1.0;
    const double extended_stopping_x =
        closest_fb_dist_to_constraint -
        std::max(min_creep_plus_yield_buffer_dist - min_creep_distance,
                 min_creep_plus_yield_buffer_dist +
                     kComingToStopNegativeBufferInMeter);
    desired_stopping_x =
        std::max(stopping_profile.back().x, extended_stopping_x);
  } else {
    desired_stopping_x = stopping_profile.back().x;
  }
}

// Updates the constraints' decisions from pass to ignore if min profile
// violates the pass time map entries. This function should be called only when
// stay stop is true.
void UpdateMinProfileViolatedPassDecisions(
    const int discomfort_ix, pb::TreeSearchType::Enum tree_search_type,
    const TimeMap& time_map, const std::vector<Constraint>& constraints,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int tree_switch_time_ix,
    const ReferenceGenerator& reference_generator,
    std::vector<Decision>& finalized_decisions) {
  const Profile& min_profile = reference_generator.GetDiscomfortMinProfile(
      discomfort_ix, time_map.steps());
  const double plan_init_state_speed = min_profile.front().v;
  DCHECK_LE(plan_init_state_speed,
            kMaxAllowedSpeedToIgnoreConflictedConstraintWhenStayStopInMps);
  Conflict conflict;
  while (FindFirstConflictForProfile(
      tree_search_type, time_map, min_profile, constraints, finalized_decisions,
      /*search_for_conflict_start_ix=*/0, gap_align_info_map,
      tree_switch_time_ix, /*adjust_for_tree_branch=*/false, &conflict)) {
    const int c_ix = conflict.constraint_ix;
    DCHECK_GE(c_ix, 0);
    DCHECK_LT(c_ix, constraints.size());
    if (finalized_decisions[c_ix] == Decision::PASS) {
      // Update pass decision to ignore for stay stop.
      finalized_decisions[c_ix] = Decision::IGNORE;
    } else {
      DCHECK(finalized_decisions[c_ix] == Decision::YIELD);
      // NOTE(Tingran): Theoretically speaking we should only have pass
      // conflict. But we found in some extreme corner case that the search
      // profile can be lower than min profile so that the conflict is yield.
      // Here we add rt event for this issue for future investigation.
      rt_event::PostRtEvent<
          rt_event::planner::SearchProfileUndershootMinProfile>();
      // Early return to avoid infinite loop.
      return;
    }
  }
}

}  // namespace

bool ShouldAdjustDiscomfort(
    const bool for_inverse_search, const Constraint& constraint,
    const Decision decision, const int c_ix, const int dominant_constraint_ix,
    const double profile_discomfort, const double t_horizon,
    const AgentReactionCalculator& agent_reaction_calculator) {
  if (!SatisfyBasicCheckForAdjustingDiscomfort(
          for_inverse_search, constraint, decision, c_ix,
          dominant_constraint_ix, profile_discomfort, t_horizon)) {
    return false;
  }

  // For remaining constraints, if they are without AR, we always finalize them.
  // If they are with AR, we do not finalize them in regular search because the
  // search was done in ascending order of discomfort. But we always finalize
  // them in reverse search because the search was only done at max discomfort
  // so it may overestimate the discomfort.
  // TODO(Tingran): Revisit this logic later because if speed/gap diversity is
  // enabled, we may still want to finalize its discomfort to lower discomfort
  // level.
  return for_inverse_search ? true
                            : agent_reaction_calculator.NoNeedToCalculateAr(
                                  constraint.settings, profile_discomfort,
                                  /*is_pass=*/decision == Decision::PASS);
}

// Returns true if |diversity_options| has |diversity_option|.
bool HasDiversityOption(
    const std::vector<pb::SpeedDiversityOption>& diversity_options,
    pb::SpeedDiversityOption diversity_option) {
  return std::find(diversity_options.begin(), diversity_options.end(),
                   diversity_option) != diversity_options.end();
}

bool ProfileSearcherUtil::LowerDownProfileToYield(
    const Conflict& conflict, const Profile& sampled_ref_profile,
    const ProfileLimits& profile_limits, const bool for_risk_search,
    int conflict_constraint_ix, Profile& profile,
    ProfileBrakeMeta& profile_brake_meta, int& search_for_conflict_start_ix,
    FailReasonConstraint& fail_info, double& x_resolve) const {
  // For a yield conflict, adjust the profile down to yield.
  int brake_first_ix = 0;
  // Try to brake to avoid the conflict.
  // NOTE(Tingran): We must allow adjust profile from start_search_ix = 0.
  // Otherwise, we may fail to adjust because for example the main profile
  // state at the tree switch time ix is already higher than the yield
  // entry. So main profile is valid but we cannot adjust the tree profile
  // if we do not allow brake earlier. It is okay to adjust the profile
  // before the switch time because it has not converged and will need to
  // run another iteration for the main profile as well.
  const bool feasible = BrakeForYieldConflict(
      conflict, profile_limits, /*start_search_ix=*/0, dt_, for_risk_search,
      &profile, &brake_first_ix, &x_resolve);
  if (!feasible) {
    // We can not brake any earlier to avoid this conflict.
    profile_brake_meta.earliest_brake_time_ix = -1;
    profile_brake_meta.earliest_brake_constraint_ix = conflict_constraint_ix;
    fail_info.constraint_ix = conflict_constraint_ix;
    fail_info.reason =
        pb::PerDiscomfortDebug_DiscomfortIncreaseSource_kCannotBrake;
    return false;
  }

  if (brake_first_ix < profile_brake_meta.earliest_brake_time_ix) {
    profile_brake_meta.earliest_brake_time_ix = brake_first_ix;
    profile_brake_meta.earliest_brake_constraint_ix = conflict_constraint_ix;
  }

  search_for_conflict_start_ix = brake_first_ix;
  AccelerateTowardsReferenceProfileWithProfileLimits(
      sampled_ref_profile, profile_limits, conflict.time_ix, dt_,
      for_risk_search, num_steps_,
      /*max_v=*/std::nullopt, profile);
  return true;
}

bool ProfileSearcherUtil::AdjustForYieldConflict(
    const bool adjust_for_tree_branch, const Conflict& conflict,
    const Constraint& conflict_constraint,
    const std::vector<Decision>& decisions, const Profile& sampled_ref_profile,
    const ProfileLimits& profile_limits, const bool for_risk_search,
    Profile& profile, ProfileBrakeMeta& profile_brake_meta,
    int& search_for_conflict_start_ix, FailReasonConstraint& fail_info,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  // Write the latest yield conflict into profile_brake_meta.
  profile_brake_meta.prev_yield_conflict = conflict;
  const int conflict_constraint_ix = conflict.constraint_ix;
  const Decision conflict_decision = decisions[conflict_constraint_ix];
  // Adjust the profile by taking the brake to avoid conflict with the yield
  // constraint.
  double x_resolve = kInvalidNum;
  const bool can_adjust = LowerDownProfileToYield(
      conflict, sampled_ref_profile, profile_limits,
      /*for_risk_search=*/for_risk_search, conflict_constraint_ix, profile,
      profile_brake_meta, search_for_conflict_start_ix, fail_info, x_resolve);
  if (intermediate_result_debug != nullptr) {
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        adjust_for_tree_branch, profile, conflict_constraint_ix,
        conflict_constraint.unique_constraint_id, conflict_decision,
        conflict.time_ix, conflict.pos,
        profile_brake_meta.earliest_brake_time_ix, x_resolve,
        GetAdjustTypeFromDecision(conflict_decision), decisions,
        /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
  }
  return can_adjust;
}

bool ProfileSearcherUtil::AdjustDecisionToNoPass(
    const std::vector<Constraint>& constraints,
    const int conflict_constraint_ix, const double discomfort,
    std::vector<Decision>& decisions, int& search_for_conflict_start_ix,
    ProfileBrakeMeta& profile_brake_meta,
    FailReasonConstraint& fail_info) const {
  // Pass in profile_brake_meta.updated_decision_indices in order to be used
  // for updating the other main/tree search constraints' decisions.
  if (!SetNoPassDecision(constraints, conflict_constraint_ix, discomfort,
                         &decisions,
                         &profile_brake_meta.updated_decision_indices)) {
    // We fail to set no pass decision. We will consider this as a
    // failure.
    // The possible cause is that we cannot set no pass to no pass ignore
    // constraint which is
    // 1. a no yield constraint with NA yield_option in constraint settings;
    // 2. a gap align constraint with pass as current decision;
    // 3. a yield-impossible with current discomfort larger than
    // yield_if_possible_below_discomfort.
    profile_brake_meta.earliest_brake_time_ix = -1;
    profile_brake_meta.earliest_brake_constraint_ix = conflict_constraint_ix;
    fail_info.constraint_ix = conflict_constraint_ix;
    fail_info.reason =
        pb::PerDiscomfortDebug_DiscomfortIncreaseSource_kSetNoPassFailure;
    return false;
  }
  // After updating decision to no pass, the conflict may occur at the very
  // beginning of planning horizon. So we need to update it to 0.
  search_for_conflict_start_ix = 0;
  return true;
}

bool ProfileSearcherUtil::AdjustProfileForPass(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const pb::TreeSearchType::Enum tree_search_type, const TimeMap& time_map,
    const Profile& sampled_ref_profile, const ProfileLimits& profile_limits,
    const Conflict& prev_yield_conflict, const Conflict& curr_pass_conflict,
    const bool for_risk_search,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int tree_switch_time_ix, const bool adjust_for_tree_branch,
    Profile& profile, ProfileBrakeMeta& profile_brake_meta,
    int& search_for_conflict_start_ix,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  if (ShouldTryEarliestAccelFromBrakeProfile(
          prev_yield_conflict, curr_pass_conflict, profile_brake_meta)) {
    return AdjustProfileForPassByEarliestAccelFromBrakeProfile(
        constraints, decisions, tree_search_type, time_map, sampled_ref_profile,
        profile_limits, prev_yield_conflict, curr_pass_conflict,
        for_risk_search, gap_align_info_map, tree_switch_time_ix,
        adjust_for_tree_branch, profile, profile_brake_meta,
        search_for_conflict_start_ix, intermediate_result_debug);
  }

  // TODO(Tingran): Try other adjusting methods with different pass-yield
  // conflict patterns. e.g. The previous yield conflict is after the current
  // pass conflict.
  return false;
}

bool ProfileSearcherUtil::AdjustProfileForPassByEarliestAccelFromBrakeProfile(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const pb::TreeSearchType::Enum tree_search_type, const TimeMap& time_map,
    const Profile& sampled_ref_profile, const ProfileLimits& profile_limits,
    const Conflict& prev_yield_conflict, const Conflict& curr_pass_conflict,
    const bool for_risk_search,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int tree_switch_time_ix, const bool adjust_for_tree_branch,
    Profile& profile, ProfileBrakeMeta& profile_brake_meta,
    int& search_for_conflict_start_ix,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  rt_event::PostRtEvent<
      rt_event::planner::AdjustProfileForPassByEarliestAccelFromBrakeProfile>();
  // Copy to a local profile to try adjusting.
  Profile mutable_profile = profile;

  // 1. Firstly generate a brake profile.
  GenerateBrakeProfileWithProfileLimits(
      /*brake_first_ix=*/0, profile.size() - 1, profile_limits, dt_,
      for_risk_search, &mutable_profile);

  // 2. Try to find the earliest time to accelerate toward the previous yield
  // conflict while the profile still has no conflict at the original conflict.
  if (!EarliestAccelWhileAvoidYieldConflict(
          prev_yield_conflict, profile_limits, dt_, for_risk_search,
          sampled_ref_profile, num_steps_, mutable_profile,
          profile_brake_meta.earliest_accel_time_ix_for_adjust_for_pass)) {
    // Fail to get a decel-first-then-accel-ASAP profile.
    return false;
  }

  // 3. Check if the profile can pass the current pass conflict after the
  // adjustment.
  Conflict conflict_after_adjust;
  const bool has_conflict_after_adjust = FindFirstConflictForProfile(
      tree_search_type, time_map, mutable_profile, constraints, decisions,
      /*search_for_conflict_start_ix=*/0, gap_align_info_map,
      tree_switch_time_ix, adjust_for_tree_branch, &conflict_after_adjust);
  const int conflict_constraint_ix = curr_pass_conflict.constraint_ix;
  if (has_conflict_after_adjust &&
      (conflict_after_adjust.time_ix <= prev_yield_conflict.time_ix ||
       conflict_after_adjust.constraint_ix ==
           curr_pass_conflict.constraint_ix)) {
    // If we have a new conflict before previous yield conflict, or the current
    // pass conflict is not fully resolved. We consider it as failed to adjust
    // for the pass conflict.
    // Set the index to profile size (currently 80) to indicate that the adjust
    // for pass is triggered, but it is not successful.
    profile_brake_meta.earliest_accel_time_ix_for_adjust_for_pass =
        profile.size();
    if (intermediate_result_debug != nullptr) {
      pb::IterativeProfile* iterative_profile_debug =
          intermediate_result_debug->add_iterative_profiles();
      PopulateIterativeProfileDebug(
          adjust_for_tree_branch, mutable_profile, conflict_constraint_ix,
          constraints[conflict_constraint_ix].unique_constraint_id,
          decisions[conflict_constraint_ix], curr_pass_conflict.time_ix,
          curr_pass_conflict.pos,
          /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
          pb::IterativeProfile::
              kFailToAdjustProfileForPassByEarliestAccelFromBrakeProfile,
          decisions,
          /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
    }
    return false;
  }

  // Success.
  profile = mutable_profile;
  profile_brake_meta.adjusted_for_pass = true;
  // Since we are adjusting profile before the original earliest brake time ix,
  // we need to reset search_for_conflict_start_ix.
  search_for_conflict_start_ix = 0;
  if (intermediate_result_debug != nullptr) {
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        adjust_for_tree_branch, profile, conflict_constraint_ix,
        constraints[conflict_constraint_ix].unique_constraint_id,
        decisions[conflict_constraint_ix], curr_pass_conflict.time_ix,
        curr_pass_conflict.pos,
        /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
        pb::IterativeProfile::
            kAdjustProfileForPassByEarliestAccelFromBrakeProfile,
        decisions,
        /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
  }
  return true;
}

bool ProfileSearcherUtil::AdjustForPassConflict(
    const pb::TreeSearchType::Enum tree_search_type,
    const bool adjust_for_tree_branch, const TimeMap& time_map,
    const Profile& sampled_ref_profile, const ProfileLimits& profile_limits,
    const Conflict& curr_pass_conflict, const bool for_risk_search,
    const std::vector<Constraint>& constraints, const double discomfort,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const int tree_switch_time_ix,
    const Conflict& other_branch_prev_yield_conflict, Profile& profile,
    std::vector<Decision>& decisions, int& search_for_conflict_start_ix,
    ProfileBrakeMeta& profile_brake_meta, FailReasonConstraint& fail_info,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  const Conflict& prev_yield_conflict =
      profile_brake_meta.prev_yield_conflict.constraint_ix >= 0
          ? profile_brake_meta.prev_yield_conflict
          : other_branch_prev_yield_conflict;
  // 1. Try to adjust profile to resolve the pass conflict.
  if (AdjustProfileForPass(constraints, decisions, tree_search_type, time_map,
                           sampled_ref_profile, profile_limits,
                           prev_yield_conflict, curr_pass_conflict,
                           for_risk_search, gap_align_info_map,
                           tree_switch_time_ix, adjust_for_tree_branch, profile,
                           profile_brake_meta, search_for_conflict_start_ix,
                           intermediate_result_debug)) {
    rt_event::PostRtEvent<rt_event::planner::AdjustProfileForPassSuccess>();
    return true;
  }

  // 2. Adjusting profile is infeasible, so we instead adjust the decision.
  // If the decision is NOT_DECIDED, we adjust it to
  // YIELD. If the decision is PASS, we adjust it to IGNORE if we allow
  // pass-ignore.
  const int conflict_constraint_ix = curr_pass_conflict.constraint_ix;
  const Decision conflict_decision = decisions[conflict_constraint_ix];
  const bool adjust_success = AdjustDecisionToNoPass(
      constraints, conflict_constraint_ix, discomfort, decisions,
      search_for_conflict_start_ix, profile_brake_meta, fail_info);
  if (intermediate_result_debug != nullptr) {
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        adjust_for_tree_branch, profile, conflict_constraint_ix,
        constraints[conflict_constraint_ix].unique_constraint_id,
        conflict_decision, curr_pass_conflict.time_ix, curr_pass_conflict.pos,
        profile_brake_meta.earliest_brake_time_ix, /*x_resolve=*/kInvalidNum,
        GetAdjustTypeFromDecision(conflict_decision), decisions,
        /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
  }
  return adjust_success;
}

bool ProfileSearcherUtil::UpdateNoBlockDecisions(
    const std::vector<Constraint>& constraints, const Profile& profile,
    const bool adjust_for_tree_branch, const TimeMap& time_map,
    const double discomfort, std::vector<Decision>* decisions,
    std::vector<int>* updated_decision_indices,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  DCHECK_EQ(constraints.size(), decisions->size());
  bool any_no_block_decision_updated = false;
  double slow_v_duration = 0.0;
  const std::vector<Decision> old_decisions = *decisions;
  int first_decision_updated_no_block_c_ix = -1;
  for (size_t ix = 0; ix < constraints.size(); ix++) {
    if (constraints[ix].type != ConstraintType::Constraint_Type_NO_BLOCK) {
      continue;
    }
    DCHECK(!IsMustPassConstraintAtDiscomfort(constraints[ix], discomfort));
    if ((*decisions)[ix] == Decision::YIELD ||
        (*decisions)[ix] == Decision::IGNORE) {
      continue;
    }
    // Check if we can pass the no-block constraint with the profile.
    if (CanPassNoBlock(profile, time_map, constraints[ix], constraints,
                       *decisions, &slow_v_duration)) {
      continue;
    }
    any_no_block_decision_updated = true;
    // We can not pass, change to yield decision.
    int new_yield_c_ix = -1;
    SetNoPassDecision(constraints, ix, discomfort, decisions,
                      updated_decision_indices, &new_yield_c_ix);
    // If any new yield decision was made, we need to start from beginning
    // again.
    if (new_yield_c_ix >= 0) {
      if (first_decision_updated_no_block_c_ix == -1) {
        first_decision_updated_no_block_c_ix = new_yield_c_ix;
      }
      ix = 0;
    }
  }
  if (intermediate_result_debug != nullptr) {
    intermediate_result_debug->set_no_block_need_update(
        any_no_block_decision_updated);
    intermediate_result_debug->set_slow_v_duration_in_no_block(slow_v_duration);
    if (any_no_block_decision_updated) {
      DCHECK_GE(first_decision_updated_no_block_c_ix, 0);
      DCHECK_LT(first_decision_updated_no_block_c_ix, constraints.size());
      auto* iterative_profile_debug =
          intermediate_result_debug->add_iterative_profiles();
      PopulateIterativeProfileDebug(
          adjust_for_tree_branch, profile, first_decision_updated_no_block_c_ix,
          constraints[first_decision_updated_no_block_c_ix]
              .unique_constraint_id,
          old_decisions[first_decision_updated_no_block_c_ix],
          /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
          /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
          GetAdjustTypeFromDecision(
              old_decisions[first_decision_updated_no_block_c_ix]),
          *decisions,
          /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
    }
  }
  return any_no_block_decision_updated;
}

bool ProfileSearcherUtil::CanPassNoBlock(
    const Profile& profile, const TimeMap& time_map,
    const Constraint& no_block_constraint,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions, double* slow_v_duration) const {
  DCHECK(no_block_constraint.type == ConstraintType::Constraint_Type_NO_BLOCK);
  DCHECK_EQ(no_block_constraint.states.size(), 1);
  DCHECK_EQ(no_block_constraint.states.front().end_time,
            std::numeric_limits<double>::infinity());
  DCHECK(!profile.empty());
  DCHECK_GT(no_block_constraint.settings.no_block_max_duration, 0.0);
  DCHECK(slow_v_duration != nullptr);
  const ConstraintState& constraint_state = no_block_constraint.states.front();
  const double start_time = constraint_state.start_time;
  // TODO(nihar): No block states are currently not discomfort varying.
  // If we make them discomfort varying then pass in the current discomfort
  // here and use it.
  DCHECK(!constraint_state.start_x.VaryWithDiscomfort())
      << constraint_state.start_x.DebugString();
  const double start_x =
      constraint_state.start_x(Discomforts::kMin) -
      (FLAGS_planning_enable_overlap_v2 ? 0.0 : ra_to_fb_shift_);
  DCHECK(!constraint_state.end_x.VaryWithDiscomfort())
      << constraint_state.end_x.DebugString();
  const double end_x =
      constraint_state.end_x(Discomforts::kMin) +
      (FLAGS_planning_enable_overlap_v2 ? 0.0 : ra_to_rb_shift_);
  // Early returns:
  // If profile is too short to reach the no-block start time, we assume we can
  // pass the no-block later.
  if (profile.back().t - plan_init_state_time_in_sec_ <= start_time) {
    return true;
  }
  // Profile is below the no-block constraint.
  if (profile.back().x <= start_x) {
    return false;
  }

  // Return true if either of the conditions is satisfied:
  // 1. The duration of any continuous blocking states is less than
  // no_block_max_duration, and the last state of the profile is not
  // a blocking state. The definition of a blocking state is defined in
  // ShouldConsiderAsBlockingNoBlockRegion.
  // 2. There is no yield entry we yield to in the no block region
  // at or after the time we enter the no-block.
  // Here is the logic revisit:
  // https://docs.google.com/document/d/1_oD16eVh-a0MQY9ksN0kHq4cfA_B5cjbE8M5HTvFcXo/edit#
  int max_continuous_time_steps_in_nb = 0;
  int curr_continuous_time_steps_in_nb = 0;
  std::optional<double> enter_no_block_time = std::nullopt;
  for (const auto& state : profile) {
    if (ShouldConsiderAsBlockingNoBlockRegion(
            state, no_block_constraint.settings.no_block_min_speed,
            start_time + plan_init_state_time_in_sec_, start_x, end_x)) {
      curr_continuous_time_steps_in_nb++;
      max_continuous_time_steps_in_nb = std::max(
          max_continuous_time_steps_in_nb, curr_continuous_time_steps_in_nb);
      if (enter_no_block_time == std::nullopt) {
        enter_no_block_time = state.t - plan_init_state_time_in_sec_;
      }
      continue;
    }
    curr_continuous_time_steps_in_nb = 0;
  }

  *slow_v_duration = max_continuous_time_steps_in_nb * dt_;
  // The profile can pass the no block constraint without entering it.
  if (!enter_no_block_time.has_value()) {
    return true;
  }

  // If the last state of the profile is considered as blocking,
  // we assume that it may block the no block constraint for longer so we
  // should yield to the no block constraint. This is a conservative strategy to
  // compensate for the fact that our planning horizon is limited.
  if (curr_continuous_time_steps_in_nb != 0) {
    return false;
  }

  if (!YieldEntryExistsInsideNoBlockAfterTime(start_x, end_x, constraints,
                                              decisions, time_map,
                                              *enter_no_block_time)) {
    // If there is no yield entry inside the no block after ego enters the
    // no block, We should be able to pass regardless of ego speed.
    return true;
  }

  return *slow_v_duration <= no_block_constraint.settings.no_block_max_duration;
}

void ProfileSearcherUtil::PopulateConstraintSelectedDiscomfort(
    const pb::TreeSearchType::Enum tree_search_type, const bool for_scr,
    const bool for_inverse_search, const Profile& profile,
    const DiscomfortVaryingMinRange& min_range,
    const std::vector<Decision>& initial_decisions,
    const ConstraintTransformer& constraint_transformer,
    const InitialDecisionProvider& initial_decision_provider,
    const AgentReactionCalculator& agent_reaction_calculator,
    const int dominant_constraint_ix, const double global_discomfort,
    const double global_discomfort_for_progress,
    const std::vector<Decision>& finalized_decisions,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    std::vector<Constraint>& finalized_constraints) const {
  TRACE_EVENT_SCOPE(
      planner, ProfileSearcherUtil_GetLowestFeasibleDiscomfortForConstraint);
  DCHECK_EQ(finalized_constraints.size(), initial_decisions.size());
  DCHECK_EQ(finalized_constraints.size(), finalized_decisions.size());
  // Find each constraint's lowest feasible discomfort in parallel by
  // reducing from global discomfort to its lowest feasible discomfort at
  // which the final profile at global discomfort still satisfies the
  // constraint. Note all the parallel_for capture are const except for
  // 'finalized_constraints'.
  // TODO(Tingran): Cache the calculated time maps at a certain discomfort
  // value in the constraint transformer for reuse.
  tbb::parallel_for(
      /*first=*/0, /*last=*/static_cast<int>(finalized_constraints.size()),
      [&for_inverse_search, &global_discomfort_for_progress,
       &finalized_constraints, &finalized_decisions, &initial_decisions,
       &profile, &min_range, tree_search_type, dominant_constraint_ix,
       global_discomfort, &gap_align_info_map, &constraint_transformer,
       &initial_decision_provider, &agent_reaction_calculator, for_scr,
       this](int c_ix) {
        // Do not need to get per-constraint lowest discomfort if search
        // result is from SCR as SCR would not go through the optimizer.
        // TODO(tienan): can revisit later if we have the need to pass some
        // SCR profiles through the optimizer.
        finalized_constraints[c_ix].selected_discomfort =
            for_scr ? global_discomfort
                    : GetLowestFeasibleDiscomfortForConstraint(
                          for_inverse_search, global_discomfort_for_progress,
                          tree_search_type, finalized_constraints,
                          finalized_decisions, initial_decisions, profile,
                          min_range, c_ix, dominant_constraint_ix,
                          global_discomfort, gap_align_info_map,
                          constraint_transformer, initial_decision_provider,
                          agent_reaction_calculator);
      });
}

ConstraintResult ProfileSearcherUtil::PopulateFinalizedConstraintResult(
    const bool maybe_stay_stop, const pb::TreeSearchType::Enum tree_search_type,
    const int tree_switch_time_ix, const bool for_scr,
    const DiscomfortVaryingMinRange& min_range,
    const std::vector<Decision>& initial_decisions,
    const InitialDecisionProvider& initial_decision_provider,
    const ConstraintTransformer& constraint_transformer,
    const AgentReactionCalculator& agent_reaction_calculator,
    const ReferenceGenerator& reference_generator,
    const std::optional<TimeMap>& time_map,
    const std::optional<std::vector<Decision>>& tree_decisions,
    const int dominant_constraint_ix, const double global_discomfort,
    std::vector<Constraint> finalized_constraints,
    std::vector<Decision> finalized_decisions,
    absl::flat_hash_map<int, GapAlignInfo> gap_align_info_map,
    pb::InitialDecisionProviderDebug* idp_debug,
    pb::TimeMap* time_map_debug) const {
  TRACE_EVENT_SCOPE(planner,
                    ProfileSearcherUtil_PopulateFinalizedConstraintResult);
  DCHECK_EQ(finalized_constraints.size(), initial_decisions.size());
  DCHECK_EQ(finalized_constraints.size(), finalized_decisions.size());
  if (time_map_debug != nullptr) {
    *time_map_debug = constraint_transformer.GenerateTimeMapForDebugWithArCache(
        finalized_constraints, initial_decisions, finalized_decisions,
        min_range, global_discomfort, for_scr, agent_reaction_calculator);
  }
  const int discomfort_ix = Discomforts::GetDiscomfortIx(global_discomfort);
  if (idp_debug != nullptr) {
    *idp_debug = initial_decision_provider.PopulateDebug(
        finalized_constraints, initial_decisions,
        Discomforts::GetDiscomfortIx(global_discomfort),
        Discomforts::GetDiscomfortIx(GetRiskMitigationDiscomfort(
            global_discomfort, finalized_constraints,
            constraint_transformer.t_horizon())));
  }
  UpdateArConstraints(agent_reaction_calculator, finalized_decisions,
                      finalized_constraints);
  planner::speed::pb::FenceList fence_list = PopulateYieldFences(
      finalized_constraints, finalized_decisions, ra_to_fb_shift_);
  if (maybe_stay_stop) {
    DCHECK(time_map.has_value());
    UpdateMinProfileViolatedPassDecisions(
        discomfort_ix, tree_search_type, time_map.value(),
        finalized_constraints, gap_align_info_map, tree_switch_time_ix,
        reference_generator, finalized_decisions);
  }
  ConstraintResult constraint_result(
      {.constraints = std::move(finalized_constraints),
       .decisions = std::move(finalized_decisions),
       .tree_decisions = tree_decisions,
       .gap_align_info_map = std::move(gap_align_info_map),
       .yielding_fence_list = std::move(fence_list),
       .dominant_constraint_ix = dominant_constraint_ix});
  return constraint_result;
}

double ProfileSearcherUtil::GetLowestFeasibleDiscomfortForConstraint(
    const bool for_inverse_search, const double global_discomfort_for_progress,
    const pb::TreeSearchType::Enum tree_search_type,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& final_decisions,
    const std::vector<Decision>& initial_decisions, const Profile& profile,
    const DiscomfortVaryingMinRange& min_range, int c_ix,
    int dominant_constraint_ix, double profile_discomfort,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const ConstraintTransformer& constraint_transformer,
    const InitialDecisionProvider& initial_decision_provider,
    const AgentReactionCalculator& agent_reaction_calculator) const {
  DCHECK_EQ(constraints.size(), final_decisions.size());
  DCHECK_EQ(initial_decisions.size(), final_decisions.size());
  const Constraint& constraint = constraints[c_ix];
  const Decision final_decision = final_decisions[c_ix];
  const bool is_pass = final_decision == Decision::PASS;
  const std::vector<Constraint> constraint_vec = {constraint};
  const std::vector<Decision> initial_decision_vec = {initial_decisions[c_ix]};
  const std::vector<Decision> final_decision_vec = {final_decision};
  const double min_discomfort =
      FLAGS_planning_enable_extra_low_discomfort_speed_search
          ? Discomforts::kMin
          : Discomforts::kMid;
  // As a constraint is pass_always_possible we will always be ready to pass at
  // lowest discomfort.
  if (is_pass && constraint.settings.pass_always_possible) {
    return min_discomfort;
  }
  if (!ShouldAdjustDiscomfort(for_inverse_search, constraint, final_decision,
                              c_ix, dominant_constraint_ix, profile_discomfort,
                              constraint_transformer.t_horizon(),
                              agent_reaction_calculator)) {
    return profile_discomfort;
  }
  // Use binary search to find lowest discomfort for this constraint which
  // does not violate the profile.
  // We should increase the lower bound if global discomfort for progress is
  // set.
  // NOTE(Tingran): Currently we will not finalize gap align or risk mitigation
  // constraint. In the future if we want to finalize them, we should use a
  // valid tree_switch_time_ix to find conflict in the following binary search
  // process instead of a dummy value (horizon).
  DCHECK(!IsValidRiskMitigationConstraint(constraint,
                                          constraint_transformer.t_horizon()));
  DCHECK(constraint.type != pb::Constraint_Type_GAP_ALIGN);
  double lower_bound = GetFeasibleDiscomfortLowerBound(
      constraints, c_ix, min_discomfort, global_discomfort_for_progress);
  if (for_inverse_search) {
    DCHECK(math::NearZero(profile_discomfort - Discomforts::kMax));
    double finalized_discomfort = Discomforts::kMax;
    if (ShouldSpeciallyFinalizeDiscomfortInInverseSearch(
            lower_bound, tree_search_type, profile, gap_align_info_map, is_pass,
            constraint_vec, final_decision_vec, min_range, profile_discomfort,
            agent_reaction_calculator, constraint_transformer,
            initial_decision_provider, finalized_discomfort)) {
      return finalized_discomfort;
    }
  }
  double upper_bound = profile_discomfort;
  // Add a counter to elicit error if dead loop due to logic error.
  int count = 0;
  while (lower_bound + kDiscomfortResolution < upper_bound) {
    DCHECK_LT(count, kMaxIterationToSearchDiscomfort)
        << "Run out of search times before finding lowest feasible discomfort "
           "for constraint: "
        << constraint.unique_constraint_id;
    count++;
    const double mid_discomfort = (lower_bound + upper_bound) * 0.5;
    // Get a time map with the immutable time corresponding to
    // profile_discomfort and entries corresponding to constraint discomfort.
    const TimeMap time_map = constraint_transformer.GenerateTimeMapWithArCache(
        constraint_vec, final_decision_vec, min_range, profile_discomfort,
        mid_discomfort, agent_reaction_calculator);
    Conflict unused_conflict;
    const bool has_conflict = FindFirstConflictForProfile(
        tree_search_type, time_map, profile, constraint_vec, final_decision_vec,
        /*search_for_conflict_start_ix=*/0, gap_align_info_map,
        /*tree_switch_time_ix=*/time_map.steps(),
        /*adjust_for_tree_branch=*/false, &unused_conflict);
    if (has_conflict) {
      // Has a conflict, update lower bound.
      lower_bound = mid_discomfort;
    } else {
      // Has no conflict, update upper bound. Now upper_bound is the lowest
      // feasible discomfort.
      upper_bound = mid_discomfort;
    }
  }
  // As we set a search resolution above to terminate this binary search loop,
  // using upper bound is always a slightly conservative estimate within
  // resolution.
  return upper_bound;
}

void ProfileSearcherUtil::UpdateArConstraints(
    const AgentReactionCalculator& agent_reaction_calculator,
    const std::vector<Decision>& decisions,
    std::vector<Constraint>& mutable_constraints) const {
  TRACE_EVENT_SCOPE(planner, ProfileSearcherUtil_UpdateArConstraints);
  for (size_t i = 0; i < mutable_constraints.size(); ++i) {
    // No update if not a speed object.
    if (mutable_constraints[i].type != pb::Constraint::SPEED_OBJECT &&
        mutable_constraints[i].type != pb::Constraint::GAP_ALIGN) {
      continue;
    }
    // No update if ignore.
    DCHECK(decisions[i] != Decision::NOT_DECIDED);
    if (decisions[i] == Decision::IGNORE) {
      continue;
    }
    // No update if no agent reaction or it is a constraint that does not need
    // to calculate AR, E.g., passing a pass_always_possible constraint.
    if (agent_reaction_calculator.NoNeedToCalculateAr(
            mutable_constraints[i].settings,
            mutable_constraints[i].selected_discomfort,
            /*is_pass=*/(decisions[i] == Decision::PASS))) {
      continue;
    }
    // Update the constraint states with AR.
    mutable_constraints[i].states =
        agent_reaction_calculator.GetCachedConstraintStates(
            mutable_constraints[i], mutable_constraints[i].selected_discomfort,
            /*is_pass=*/(decisions[i] == Decision::PASS));
    // Update the adjusted prediction with AR.
    mutable_constraints[i].adjusted_prediction =
        agent_reaction_calculator.InterpolateArTrajectory(
            mutable_constraints[i], mutable_constraints[i].selected_discomfort,
            /*is_pass=*/(decisions[i] == Decision::PASS));
  }
}

planner::speed::pb::FenceList ProfileSearcherUtil::PopulateYieldFences(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions,
    const double rear_axle_to_front_bumper_m) const {
  planner::speed::pb::FenceList yielding_fence_list;
  yielding_fence_list.mutable_fence_list()->Reserve(constraints.size());
  // TODO(nihar): Check if we need to subtract the rear axle
  // to front bumper distance.
  yielding_fence_list.set_rear_axle_to_front_bumper_m(
      rear_axle_to_front_bumper_m);
  DCHECK_EQ(constraints.size(), decisions.size());
  for (size_t i = 0; i < constraints.size(); ++i) {
    DCHECK(constraints[i].type != pb::Constraint::NA)
        << "Constraint type not defined";
    const auto selected_discomfort = constraints[i].selected_discomfort;
    // Only populate fence data if we are yielding to the constraint.
    if (decisions[i] != Decision::YIELD) {
      continue;
    }
    // Do not add fence if constraint states are empty.
    // TODO(nihar): Replace this with a DCHECK once we make
    // sure that constraints with empty states cannot be created.
    if (constraints[i].states.empty()) {
      continue;
    }
    planner::speed::pb::Fence* fence = nullptr;
    // Calculate the front bumper to fence distance based on the
    // constraint type. If constraint is of type AVOID_REGION, NO_BLOCK
    // or STOP_POINT, use the first state to calculate the distance. If
    // the constraint is of type SHIFT_SPEED_OBJECT or SPEED_OBJECT, use
    // use the first state that is within the required lateral gap to
    // calculate the distance.
    // Yielding fence only displays the reasoning context, so that is why
    // min-range is not included.
    switch (constraints[i].type) {
      case pb::Constraint::AVOID_REGION:
      case pb::Constraint::NO_BLOCK:
      case pb::Constraint::STOP_POINT: {
        fence = yielding_fence_list.add_fence_list();
        const auto front_bumper_to_fence_m =
            FLAGS_planning_enable_overlap_v2
                ? constraints[i].states.front().start_x(selected_discomfort) -
                      constraints[i].settings.yield_extra_distance(
                          selected_discomfort)
                : (constraints[i].states.front().start_x(selected_discomfort) -
                   constraints[i].settings.yield_extra_distance(
                       selected_discomfort) -
                   rear_axle_to_front_bumper_m);
        // TODO(nihar): See if you need to add DCHECK_GT here.
        // Set the closer fence position between the one calculated
        // from constraint states and the one from standoffs.
        fence->set_front_bumper_to_fence_m(std::min(
            front_bumper_to_fence_m,
            GetFencePositionForStandOffs(constraints[i].standoffs,
                                         selected_discomfort,
                                         rear_axle_to_front_bumper_m)));
        break;
      }
      case pb::Constraint::SHIFT_SPEED_OBJECT:
      case pb::Constraint::SPEED_OBJECT: {
        const auto yield_required_lat_gap_m =
            constraints[i].settings.yield_required_lat_gap(selected_discomfort);
        // Find the closest state to ego within the required lateral gap.
        const ConstraintState* closest_state_to_ego_within_req_lat_gap =
            nullptr;
        double min_distance_to_ego = std::numeric_limits<double>::max();
        for (const auto& state : constraints[i].states) {
          if (state.abs_lat_gap <= yield_required_lat_gap_m &&
              state.start_time >= 0.0 &&
              state.start_x(selected_discomfort) < min_distance_to_ego) {
            closest_state_to_ego_within_req_lat_gap = &state;
            min_distance_to_ego = state.start_x(selected_discomfort);
          }
        }
        if (closest_state_to_ego_within_req_lat_gap != nullptr) {
          fence = yielding_fence_list.add_fence_list();
          const auto front_bumper_to_fence_m =
              FLAGS_planning_enable_overlap_v2
                  ? closest_state_to_ego_within_req_lat_gap->start_x(
                        selected_discomfort) -
                        constraints[i].settings.yield_extra_distance(
                            selected_discomfort)
                  : (closest_state_to_ego_within_req_lat_gap->start_x(
                         selected_discomfort) -
                     constraints[i].settings.yield_extra_distance(
                         selected_discomfort) -
                     rear_axle_to_front_bumper_m);
          // TODO(nihar): See if you need to add DCHECK_GT here.
          // Set the closer fence position between the one calculated
          // from constraint states and the one from standoffs.
          fence->set_front_bumper_to_fence_m(std::min(
              front_bumper_to_fence_m,
              GetFencePositionForStandOffs(constraints[i].standoffs,
                                           selected_discomfort,
                                           rear_axle_to_front_bumper_m)));
        }
        break;
      }
      default:
        break;
    }
    if (fence != nullptr) {
      fence->set_constraint_id(constraints[i].unique_constraint_id);
      fence->set_type(constraints[i].fence_type);
      fence->set_obj_id(constraints[i].obj_id);
    }
  }
  return yielding_fence_list;
}

bool ProfileSearcherUtil::AdjustForAllConflictByType(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    BaseTreeSearchConvergenceMeta* base_tree_search_convergence_meta,
    pb::PerDiscomfortDebug* intermediate_result_debug,
    SearchSolution& search_solution) const {
  DCHECK(base_tree_search_convergence_meta != nullptr);

  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  const pb::TreeSearchType::Enum tree_search_type =
      search_solution.tree_search_type;
  int& tree_switch_time_ix = search_solution.tree_switch_time_ix;
  int& earliest_brake_time_ix = search_solution.earliest_brake_time_ix;
  int& earliest_brake_cix = search_solution.earliest_brake_cix;
  int& earliest_accel_time_ix_for_adjust_for_pass =
      search_solution.earliest_accel_time_ix_for_adjust_for_pass;
  std::vector<Decision>& decisions = search_solution.decisions;
  std::vector<Decision>& tree_decisions = search_solution.tree_decisions;
  Profile& profile = search_solution.profile;
  Profile& tree_profile = search_solution.tree_profile;
  DCHECK_EQ(decisions.size(), constraints.size());
  DCHECK_EQ(profile.size(), num_steps_);
  DCHECK_EQ(tree_profile.size(), profile.size());
  const bool enable_tree_search =
      tree_search_type != pb::TreeSearchType_Enum_kNone;
  // Continuously adjust the profile and accommodate no-block constraints and
  // terminate until no new yield decision is made for any no-block
  // constraint. We need to separate no-block from other constraints as the
  // criterion to pass a no-block constraint is not simply PASS/YIELD to a given
  // position, but we also need to consider how much time and speed we go in a
  // no-block region.
  earliest_brake_time_ix = profile.size();
  earliest_brake_cix = -1;
  // Initialize to INT_MAX to indicate that adjust for pass is not triggered.
  earliest_accel_time_ix_for_adjust_for_pass = std::numeric_limits<int>::max();
  tree_switch_time_ix = base_tree_search_convergence_meta->GetSwitchTimeIndex(
      enable_tree_search, constraints);
  int last_tree_switch_time_ix = tree_switch_time_ix;
  // Count the number of iteration to avoid infinite loop.
  int iteration_count = 0;
  if (intermediate_result_debug != nullptr) {
    // Add one iterative profile to record the first profile (ref profile)
    // before adjusting for conflicts.
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        /*adjust_for_tree_branch=*/false, profile,
        /*conflict_constraint_ix=*/static_cast<int>(kInvalidNum),
        /*conflict_constraint_id=*/"NA",
        /*conflict_decision=*/Decision::NOT_DECIDED,
        /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
        /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
        /*adjust_type=*/pb::IterativeProfile::kInitiateFromRefProfile,
        decisions,
        /*tree_switch_time_ix=*/-1, *iterative_profile_debug);
  }
  while (true) {
    if (iteration_count > kMaxTreeSearchIterationNum) {
      // Return false if it exceeds the max tree search iteration for
      // converge.
      // TODO(Tingran): Add a unit test if this case happens.
      DCHECK(enable_tree_search);
      rt_event::PostRtEvent<
          rt_event::planner::TreeSearchFailDueToMaxIteration>();
      search_solution.current_fail_info.reason = pb::
          PerDiscomfortDebug_DiscomfortIncreaseSource_kTreeSearchFailDueToMaxIteration;  // NOLINT
      LOG(ERROR)
          << "Tree search fails to converge due to reaching max iteration.";
      return false;
    }
    // Increment |iteration_count|.
    ++iteration_count;
    // 1. Adjust main profile.

    // Construct main profile brake meta.
    ProfileBrakeMeta main_profile_brake_meta(
        /*earliest_brake_time_ix_in=*/profile.size(),
        /*earliest_brake_constraint_ix_in=*/-1, constraints.size());
    const bool success = AdjustMainProfileForOneIterationWithNoBlock(
        per_discomfort_problem,
        base_tree_search_convergence_meta->profile_brake_meta(),
        last_tree_switch_time_ix, main_profile_brake_meta,
        intermediate_result_debug, search_solution);
    if (!success) {
      return false;
    }
    DCHECK(success) << "Run tree search when main search iteration fails.";
    if (!IsValidTreeSwitchTimeIx(tree_switch_time_ix, num_steps_)) {
      // Do not run tree search if tree switch time is invalid.
      // Here the nominal search has succeeded.
      break;
    }

    // 2. Run tree search to adjust tree profile.
    last_tree_switch_time_ix = tree_switch_time_ix;
    base_tree_search_convergence_meta->Prepare(tree_profile.size(),
                                               constraints.size());
    AdjustTreeProfileForOneIteration(
        per_discomfort_problem, main_profile_brake_meta,
        base_tree_search_convergence_meta, search_solution,
        intermediate_result_debug);
    if (!base_tree_search_convergence_meta->can_adjust_tree_profile()) {
      return false;
    }
    if (base_tree_search_convergence_meta->has_converged()) {
      // Converged. Break.
      break;
    }
    // Tree search has not converged. Continue.
  }

  // Search has succeeded.
  // Update main decisions.
  ResolveNotDecidedDecisionToPass(profile, /*adjust_for_tree_branch=*/false,
                                  decisions, intermediate_result_debug);

  if (IsValidTreeSwitchTimeIx(tree_switch_time_ix, num_steps_)) {
    // After tree search, the two profiles should be identical until after
    // tree switch time ix. No need to check last state's j because by
    // definition we allow divergence starting from |tree_switch_time_ix|,
    // namely different j.
    // NOTE(Tingran): By checking states, actually we do not need to check j, as
    // j is not part of the state, state meaning, x,v,a, j is control which is
    // changable.
    const Profile& tree_profile = search_solution.tree_profile;
    DCHECK(AreProfilesIdenticalUntilIndex(profile, tree_profile,
                                          tree_switch_time_ix,
                                          /*check_last_state_j=*/false))
        << absl::StrFormat(
               "tree_switch_time_ix: %d.\nMain profile: %s\n, tree "
               "profile: %s",
               tree_switch_time_ix, ProfileDebugString(profile).c_str(),
               ProfileDebugString(tree_profile).c_str());
    const TimeMap& time_map = per_discomfort_problem.time_map;
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map =
        per_discomfort_problem.per_discomfort_gap_align_info_map;
    Conflict conflict;
    DCHECK(!FindFirstConflictForProfile(
        tree_search_type, time_map, profile, constraints, decisions,
        /*search_for_conflict_start_ix=*/0, gap_align_info_map,
        tree_switch_time_ix,
        /*adjust_for_tree_branch=*/false, &conflict))
        << "conflict.time_ix: " << conflict.time_ix
        << ", switch time: " << tree_switch_time_ix
        << ", conflict.pos: " << conflict.pos
        << "main profile state x: " << profile[conflict.time_ix].x
        << ", pass: " << conflict.pass;

    // Use |risk_time_map| for adjusting tree branch if it has value. Otherwise,
    // use the same time map for main search.
    const TimeMap& tree_branch_time_map =
        per_discomfort_problem.risk_time_map.has_value()
            ? per_discomfort_problem.risk_time_map.value()
            : per_discomfort_problem.time_map;
    if (per_discomfort_problem.risk_time_map.has_value()) {
      DCHECK(tree_search_type == pb::TreeSearchType_Enum_kRiskMitigation);
    }
    DCHECK(!FindFirstConflictForProfile(
        tree_search_type, tree_branch_time_map, tree_profile, constraints,
        tree_decisions,
        /*search_for_conflict_start_ix=*/0, gap_align_info_map,
        tree_switch_time_ix,
        /*adjust_for_tree_branch=*/true, &conflict))
        << "conflict.time_ix: " << conflict.time_ix
        << ", switch time: " << tree_switch_time_ix
        << ", conflict.pos: " << conflict.pos
        << "tree profile state x: " << tree_profile[conflict.time_ix].x
        << ", pass: " << conflict.pass;

    if (search_solution.risk_mitigation_active) {
      // If risk mitigation is active, the earliest_brake_time_ix must be before
      // tree_switch_time_ix.
      DCHECK_LT(earliest_brake_time_ix, tree_switch_time_ix);
    }
  }

  return true;
}

bool ProfileSearcherUtil::AdjustMainProfileForOneIterationWithNoBlock(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const ProfileBrakeMeta& tree_profile_brake_meta,
    const int last_tree_switch_time_ix,
    ProfileBrakeMeta& main_profile_brake_meta,
    pb::PerDiscomfortDebug* intermediate_result_debug,
    SearchSolution& search_solution) const {
  TRACE_EVENT_INSTANT(
      planner, ProfileSearcherUtil_AdjustMainProfileForOneIterationWithNoBlock);
  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  const TimeMap& time_map = per_discomfort_problem.time_map;
  const double discomfort = per_discomfort_problem.discomfort;
  Profile& profile = search_solution.profile;
  std::vector<Decision>& decisions = search_solution.decisions;
  const int tree_switch_time_ix = search_solution.tree_switch_time_ix;
  const std::vector<Decision>& tree_decisions = search_solution.tree_decisions;
  int& earliest_brake_time_ix = search_solution.earliest_brake_time_ix;
  int& earliest_brake_cix = search_solution.earliest_brake_cix;
  int& earliest_accel_time_ix_for_adjust_for_pass =
      search_solution.earliest_accel_time_ix_for_adjust_for_pass;
  DCHECK_EQ(decisions.size(), constraints.size());
  DCHECK_EQ(profile.size(), num_steps_);
  if (IsValidTreeSwitchTimeIx(tree_switch_time_ix, num_steps_)) {
    // Sync search result from last iterative tree search.
    ConsolidateTreeSearchSolutionBeforeSwitchTime(
        constraints, search_solution.tree_profile, tree_decisions,
        tree_profile_brake_meta.updated_decision_indices,
        tree_profile_brake_meta.earliest_brake_time_ix,
        tree_profile_brake_meta.adjusted_for_pass, last_tree_switch_time_ix,
        tree_switch_time_ix, dt_ * num_steps_,
        /*adjust_for_tree_branch=*/false, profile, decisions,
        intermediate_result_debug);
    // Accel toward reference profile from tree_switch_time_ix.
    AccelerateTowardsReferenceProfileWithProfileLimits(
        per_discomfort_problem.sampled_ref_profile,
        per_discomfort_problem.profile_limits, tree_switch_time_ix, dt_,
        /*for_risk_search=*/false, num_steps_,
        /*max_v=*/std::nullopt, profile);
    if (intermediate_result_debug != nullptr) {
      // Add one iterative profile to resolve not decided decisions.
      pb::IterativeProfile* iterative_profile_debug =
          intermediate_result_debug->add_iterative_profiles();
      PopulateIterativeProfileDebug(
          /*adjust_for_tree_branch=*/false, profile,
          /*conflict_constraint_ix=*/static_cast<int>(kInvalidNum),
          /*conflict_constraint_id=*/"NA",
          /*conflict_decision=*/Decision::NOT_DECIDED,
          /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
          /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
          /*adjust_type=*/pb::IterativeProfile::kAccelTowardReferenceProfile,
          decisions, tree_switch_time_ix, *iterative_profile_debug);
    }
  }

  int iteration = -1;
  // The max number of no block constraint adjustment to avoid infinite loop.
  const int max_allowed_iteration = std::count_if(
      constraints.begin(), constraints.end(), [](const Constraint& constraint) {
        return constraint.type == pb::Constraint::NO_BLOCK;
      });
  while (true) {
    if (iteration > max_allowed_iteration) {
      rt_event::PostRtEvent<
          rt_event::planner::kAdjustNoBlockFailDueToMaxIteration>();
      search_solution.current_fail_info.reason = pb::
          PerDiscomfortDebug_DiscomfortIncreaseSource_kAdjustNoBlockFailDueToMaxIteration;  // NOLINT
      LOG(ERROR) << "Adjusting no block constraints fails to converge due to "
                    "reaching max iteration.";
      return false;
    }
    ++iteration;
    // Adjust profile for all constraints.
    // Adjust the main profile.
    const bool can_adjust_main_profile = AdjustForConflictExceptNoBlock(
        per_discomfort_problem,
        /*adjust_for_tree_branch=*/false,
        tree_profile_brake_meta.prev_yield_conflict, main_profile_brake_meta,
        intermediate_result_debug, search_solution);
    if (!can_adjust_main_profile) {
      earliest_brake_time_ix = -1;
      earliest_brake_cix = main_profile_brake_meta.earliest_brake_constraint_ix;
      // If search fails, update |earliest_accel_time_ix_for_adjust_for_pass| to
      // -1.
      earliest_accel_time_ix_for_adjust_for_pass = -1;
      return false;
    }
    // Update main profile adjustment meta.
    UpdateAccelBrakeInfo(
        main_profile_brake_meta.earliest_brake_time_ix,
        main_profile_brake_meta.earliest_brake_constraint_ix,
        main_profile_brake_meta.earliest_accel_time_ix_for_adjust_for_pass,
        earliest_brake_time_ix, earliest_brake_cix,
        earliest_accel_time_ix_for_adjust_for_pass);
    // Accommodate no-block constraints.
    const bool any_no_block_decision_updated = UpdateNoBlockDecisions(
        constraints, profile, /*adjust_for_tree_branch=*/false, time_map,
        discomfort, &decisions,
        &main_profile_brake_meta.updated_decision_indices,
        intermediate_result_debug);
    if (!any_no_block_decision_updated) {
      break;
    }
    // We made a yield decision for a no-block constraint, need to re-adjust
    // the profile to avoid yield conflicts.
  }
  return true;
}

void ProfileSearcherUtil::AdjustTreeProfileForOneIteration(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const ProfileBrakeMeta& main_profile_brake_meta,
    BaseTreeSearchConvergenceMeta* base_tree_search_convergence_meta,
    SearchSolution& search_solution,
    pb::PerDiscomfortDebug* intermediate_result_debug) const {
  TRACE_EVENT_SCOPE(planner,
                    ProfileSearcherUtil_AdjustTreeProfileForOneIteration);
  DCHECK(base_tree_search_convergence_meta != nullptr);
  // Set alias for search solution data fields.
  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  const bool for_risk_search =
      HasAnyValidRiskMitigationConstraint(constraints, num_steps_ * dt_);
  if (!for_risk_search) {
    const double switch_time =
        per_discomfort_problem.profile_limits.switch_time();
    const int n_steps = per_discomfort_problem.time_map.steps();
    const double dt = per_discomfort_problem.time_map.dt();
    const double time_horizon = n_steps * dt;
    DCHECK_GE(switch_time, time_horizon) << absl::StrFormat(
        "If not for risk mitigation search, profile limits switch time should "
        "NOT be less than planning horizon, %.6f vs %.6f",
        switch_time, time_horizon);
  }
  Profile& tree_profile = search_solution.tree_profile;
  DCHECK_EQ(tree_profile.size(), num_steps_);
  std::vector<Decision>& tree_decisions = search_solution.tree_decisions;
  DCHECK_EQ(tree_decisions.size(), constraints.size());
  int& tree_switch_time_ix = search_solution.tree_switch_time_ix;
  DCHECK(IsValidTreeSwitchTimeIx(tree_switch_time_ix, num_steps_))
      << "Invalid tree switch time when doing tree search.";
  int& earliest_brake_time_ix = search_solution.earliest_brake_time_ix;
  int& earliest_brake_cix = search_solution.earliest_brake_cix;
  int& earliest_accel_time_ix_for_adjust_for_pass =
      search_solution.earliest_accel_time_ix_for_adjust_for_pass;
  // Set alias for tree profile brake meta data fields.
  ProfileBrakeMeta& tree_profile_brake_meta =
      base_tree_search_convergence_meta->mutable_profile_brake_meta();

  const Profile& sampled_ref_profile =
      for_risk_search &&
              per_discomfort_problem.risk_sampled_ref_profile.has_value()
          ? per_discomfort_problem.risk_sampled_ref_profile.value()
          : per_discomfort_problem.sampled_ref_profile;

  // Consolidate main profile search solution before switch time.
  // NOTE(Tingran): Use tree_switch_time_ix as last_tree_switch_time_ix in
  // order to avoid syncing tree profile again if main earliest brake profile
  // is at or after tree switch time.
  ConsolidateTreeSearchSolutionBeforeSwitchTime(
      constraints, search_solution.profile, search_solution.decisions,
      main_profile_brake_meta.updated_decision_indices,
      main_profile_brake_meta.earliest_brake_time_ix,
      main_profile_brake_meta.adjusted_for_pass,
      /*last_tree_switch_time_ix=*/tree_switch_time_ix, tree_switch_time_ix,
      dt_ * num_steps_, /*adjust_for_tree_branch=*/true, tree_profile,
      tree_decisions, intermediate_result_debug);
  // Fix the profile before switch_time. Then accel towards reference profile
  // afterwards.
  AccelerateTowardsReferenceProfileWithProfileLimits(
      sampled_ref_profile, per_discomfort_problem.profile_limits,
      tree_switch_time_ix, dt_,
      /*for_risk_search=*/for_risk_search, num_steps_,
      /*max_v=*/std::nullopt, tree_profile);
  if (intermediate_result_debug != nullptr) {
    // Add one iterative profile to resolve not decided decisions.
    pb::IterativeProfile* iterative_profile_debug =
        intermediate_result_debug->add_iterative_profiles();
    PopulateIterativeProfileDebug(
        /*adjust_for_tree_branch=*/true, tree_profile,
        /*conflict_constraint_ix=*/static_cast<int>(kInvalidNum),
        /*conflict_constraint_id=*/"NA",
        /*conflict_decision=*/Decision::NOT_DECIDED,
        /*t_conflict=*/kInvalidNum, /*x_conflict=*/kInvalidNum,
        /*t_brake=*/kInvalidNum, /*x_resolve=*/kInvalidNum,
        /*adjust_type=*/pb::IterativeProfile::kAccelTowardReferenceProfile,
        tree_decisions, tree_switch_time_ix, *iterative_profile_debug);
  }

  base_tree_search_convergence_meta->set_can_adjust_tree_profile(
      AdjustForConflictExceptNoBlock(
          per_discomfort_problem,
          /*adjust_for_tree_branch=*/true,
          main_profile_brake_meta.prev_yield_conflict, tree_profile_brake_meta,
          intermediate_result_debug, search_solution));

  if (!base_tree_search_convergence_meta->can_adjust_tree_profile()) {
    earliest_brake_time_ix = -1;
    earliest_brake_cix = tree_profile_brake_meta.earliest_brake_constraint_ix;
    // If search fails, update |earliest_accel_time_ix_for_adjust_for_pass| to
    // -1.
    earliest_accel_time_ix_for_adjust_for_pass = -1;
    return;
  }

  // TODO(Tingran): Figure out whether we should adjust for no block for tree
  // profile as well when there are motivation cases.
  if (base_tree_search_convergence_meta->CheckConvergence(
          per_discomfort_problem, tree_profile, tree_decisions,
          tree_profile_brake_meta.adjusted_for_pass, tree_switch_time_ix)) {
    base_tree_search_convergence_meta->set_has_converged();
    // If converged, update tree_decisions.
    ResolveNotDecidedDecisionToPass(tree_profile,
                                    /*adjust_for_tree_branch=*/true,
                                    tree_decisions, intermediate_result_debug);
    return;
  }
  base_tree_search_convergence_meta->Update(
      /*has_converged=*/false);
  base_tree_search_convergence_meta->UpdateSwitchTimeIndex(
      /*enable_tree_search=*/search_solution.tree_search_type !=
          pb::TreeSearchType_Enum_kNone,
      constraints, tree_switch_time_ix);
  // Update global |earliest_brake_time_ix|, |earliest_brake_cix| and
  // |earliest_accel_time_ix_for_adjust_for_pass| only if
  // |tree_profile_brake_meta.earliest_brake_time_ix| is before
  // |tree_switch_time_ix|.
  // NOTE(Tingran): We should use the updated |tree_switch_time_ix| to
  // compare. We may directly update the global value because
  // tree_switch_time_ix will monotonically increase. Namely it will not go
  // back and cause the main search to ignore this adjustment. We will sync
  // tree and main profile and decision outside this function.
  if (tree_profile_brake_meta.earliest_brake_time_ix < tree_switch_time_ix) {
    UpdateAccelBrakeInfo(
        tree_profile_brake_meta.earliest_brake_time_ix,
        tree_profile_brake_meta.earliest_brake_constraint_ix,
        tree_profile_brake_meta.earliest_accel_time_ix_for_adjust_for_pass,
        earliest_brake_time_ix, earliest_brake_cix,
        earliest_accel_time_ix_for_adjust_for_pass);
  }
}

bool ProfileSearcherUtil::AdjustForConflictExceptNoBlock(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const bool adjust_for_tree_branch,
    const Conflict& other_branch_prev_yield_conflict,
    ProfileBrakeMeta& profile_brake_meta,
    pb::PerDiscomfortDebug* intermediate_result_debug,
    SearchSolution& search_solution) const {
  TRACE_EVENT_INSTANT(planner,
                      ProfileSearcherUtil_AdjustForConflictExceptNoBlock);
  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  const double discomfort = per_discomfort_problem.discomfort;
  const ProfileLimits& profile_limits = per_discomfort_problem.profile_limits;
  const std::optional<Profile>& risk_sampled_ref_profile =
      per_discomfort_problem.risk_sampled_ref_profile;
  const std::optional<TimeMap>& risk_time_map =
      per_discomfort_problem.risk_time_map;

  // Use |risk_time_map| for adjusting tree branch if it has value. Otherwise,
  // use the same time map for main search.
  const TimeMap& time_map = adjust_for_tree_branch && risk_time_map.has_value()
                                ? risk_time_map.value()
                                : per_discomfort_problem.time_map;
  // Use |risk_sampled_ref_profile| for adjusting tree branch if it has value.
  // Otherwise, use the same sampled_ref_profile for main search.
  const Profile& sampled_ref_profile =
      adjust_for_tree_branch && risk_sampled_ref_profile.has_value()
          ? risk_sampled_ref_profile.value()
          : per_discomfort_problem.sampled_ref_profile;
  const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map =
      per_discomfort_problem.per_discomfort_gap_align_info_map;
  // We will consider conflict from the very beginning of planning horizon.
  int search_for_conflict_start_ix = 0;
  std::vector<Decision>* decisions = adjust_for_tree_branch
                                         ? &search_solution.tree_decisions
                                         : &search_solution.decisions;
  Profile* profile = adjust_for_tree_branch ? &search_solution.tree_profile
                                            : &search_solution.profile;
  FailReasonConstraint* fail_info = &search_solution.current_fail_info;
  const int tree_switch_time_ix = search_solution.tree_switch_time_ix;
  DCHECK(decisions != nullptr);
  DCHECK(profile != nullptr);
  DCHECK_GE(profile->size(), num_steps_);
  DCHECK(fail_info != nullptr);
  DCHECK_EQ(constraints.size(), decisions->size());
  const bool for_risk_search =
      HasAnyValidRiskMitigationConstraint(constraints, num_steps_ * dt_) &&
      adjust_for_tree_branch;
  const pb::TreeSearchType::Enum tree_search_type =
      search_solution.tree_search_type;
  // Continuously find the earliest conflict between the profile and the
  // time-map, and resolve the conflicts by either updating decisions or
  // adjusting the profile. Terminate until we can not find any conflict
  // between the profile and the time-map.
  Conflict conflict;
  int adjust_count = 0;
  while (FindFirstConflictForProfile(
      tree_search_type, time_map, *profile, constraints, *decisions,
      search_for_conflict_start_ix, gap_align_info_map, tree_switch_time_ix,
      adjust_for_tree_branch, &conflict)) {
    if (adjust_count > kMaxNumOfConfictAdjustment) {
      LOG(ERROR) << "Fail to adjust within max allowed number of iterations.";
      rt_event::PostRtEvent<
          rt_event::planner::AdjustForConflictExceedsMaxAllowedIterations>();
      return false;
    }
    ++adjust_count;
    const int conflict_constraint_ix = conflict.constraint_ix;
    const Decision conflict_decision = (*decisions)[conflict_constraint_ix];
    const Constraint& conflict_constraint = constraints[conflict_constraint_ix];
    bool can_adjust = false;
    if (conflict_decision == Decision::YIELD) {
      can_adjust = AdjustForYieldConflict(
          adjust_for_tree_branch, conflict, conflict_constraint, (*decisions),
          sampled_ref_profile, profile_limits,
          /*for_risk_search=*/for_risk_search, *profile, profile_brake_meta,
          search_for_conflict_start_ix, *fail_info, intermediate_result_debug);
    } else {
      // Decision is pass or not_decided.
      can_adjust = AdjustForPassConflict(
          tree_search_type, adjust_for_tree_branch, time_map,
          sampled_ref_profile, profile_limits, conflict, for_risk_search,
          constraints, discomfort, gap_align_info_map, tree_switch_time_ix,
          other_branch_prev_yield_conflict, *profile, *decisions,
          search_for_conflict_start_ix, profile_brake_meta, *fail_info,
          intermediate_result_debug);
    }
    if (!can_adjust) {
      return false;
    }
  }
  if (intermediate_result_debug != nullptr) {
    DCHECK(!intermediate_result_debug->iterative_profiles().empty());
  }
  return true;
}

bool ProfileSearcherUtil::AnyConstraintUpdatedToIgnore(
    const std::vector<Constraint>& constraints, ConstraintType constraint_type,
    const std::vector<Decision>& initial_decisions,
    const std::vector<Decision>& result_decisions) const {
  DCHECK_EQ(constraints.size(), initial_decisions.size());
  DCHECK_EQ(constraints.size(), result_decisions.size());
  for (size_t i = 0; i < constraints.size(); ++i) {
    if (constraints[i].type != constraint_type) {
      continue;
    }
    if (initial_decisions[i] != Decision::IGNORE &&
        result_decisions[i] == Decision::IGNORE) {
      return true;
    }
  }
  return false;
}

bool ProfileSearcherUtil::IsDiscomfortForProgressSatisfied(
    const std::vector<Constraint>& primitive_constraints,
    const SearchSolution& search_solution, const double curr_discomfort,
    const double global_discomfort_for_progress) const {
  const std::vector<Decision>& decisions = search_solution.decisions;
  // Check if we are still yielding to any constraint that expects
  // ego to look for pass with discomfort for progress.
  for (size_t cix = 0; cix < primitive_constraints.size(); ++cix) {
    if (decisions[cix] != Decision::YIELD) {
      continue;
    }
    const double targeted_discomfort =
        std::max(primitive_constraints[cix].settings.discomfort_for_progress,
                 global_discomfort_for_progress);
    if (targeted_discomfort >= curr_discomfort) {
      return false;
    }
  }
  return true;
}

bool ProfileSearcherUtil::IsProgressForKinematicSatisfied(
    const double curr_discomfort, const double global_discomfort_for_progress,
    const bool progress_for_kinematic) const {
  // If we just want to elevate discomfort for kinematic progress (E.g, more
  // accel ), we do not care about constraint decisions.
  if (progress_for_kinematic &&
      global_discomfort_for_progress >= curr_discomfort) {
    return false;
  }
  return true;
}

bool ProfileSearcherUtil::AdjustForAllConflict(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    double immutable_time, pb::PerDiscomfortDebug* intermediate_result_debug,
    SearchSolution& search_solution) const {
  TRACE_EVENT_INSTANT(planner, ProfileSearcherUtil_AdjustForAllConflict);
  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  switch (search_solution.tree_search_type) {
    case pb::TreeSearchType_Enum_kNone: {
      NoneTreeSearchConvergenceMeta none_tree_search_convergence_meta(
          immutable_time, dt_, num_steps_,
          /*earliest_brake_time_ix_in=*/num_steps_,
          /*earliest_brake_constraint_ix_in=*/-1, constraints.size());
      return AdjustForAllConflictByType(
          per_discomfort_problem, &none_tree_search_convergence_meta,
          intermediate_result_debug, search_solution);
    }
    case pb::TreeSearchType_Enum_kLaneChangeGapAlign: {
      DCHECK(per_discomfort_problem.estimated_half_cross_lane_duration_in_second
                 .has_value());
      GapAlignTreeSearchConvergenceMeta gap_align_tree_search_convergence_meta(
          immutable_time, dt_, num_steps_,
          /*earliest_brake_time_ix_in=*/num_steps_,
          /*earliest_brake_constraint_ix_in=*/-1, constraints.size(),
          per_discomfort_problem.estimated_half_cross_lane_duration_in_second
              .value());
      return AdjustForAllConflictByType(
          per_discomfort_problem, &gap_align_tree_search_convergence_meta,
          intermediate_result_debug, search_solution);
    }
    case pb::TreeSearchType_Enum_kRiskMitigation: {
      rt_event::PostRtEvent<rt_event::planner::RiskMitigationTriggered>();
      RiskMitigationTreeSearchConvergenceMeta
          risk_mitigation_tree_search_convergence_meta(
              immutable_time, dt_, num_steps_,
              /*earliest_brake_time_ix_in=*/num_steps_,
              /*earliest_brake_constraint_ix_in=*/-1, constraints.size());
      const bool success = AdjustForAllConflictByType(
          per_discomfort_problem, &risk_mitigation_tree_search_convergence_meta,
          intermediate_result_debug, search_solution);
      if (success) {
        // Update if risk mitigation is active.
        search_solution.risk_mitigation_active =
            risk_mitigation_tree_search_convergence_meta
                .risk_mitigation_active();
      }
      return success;
    }
    default:
      DCHECK(false) << "Unknown tree search type.";
  }
  return false;
}

bool ProfileSearcherUtil::ShouldSearchForDiscomfortForProgressInRegularMode(
    const std::vector<Constraint>& primitive_constraints,
    const SearchSolution& search_solution, const double curr_discomfort,
    const double global_discomfort_for_progress,
    const bool for_yield_homotopy) const {
  if (for_yield_homotopy) {
    return false;
  }

  return !IsDiscomfortForProgressSatisfied(primitive_constraints,
                                           search_solution, curr_discomfort,
                                           global_discomfort_for_progress);
}

bool ProfileSearcherUtil::ShouldSearchForProgressForKinematicInRegularMode(
    const double curr_discomfort, const double global_discomfort_for_progress,
    const bool progress_for_kinematic, const bool for_yield_homotopy) const {
  if (for_yield_homotopy) {
    return false;
  }

  return !IsProgressForKinematicSatisfied(
      curr_discomfort, global_discomfort_for_progress, progress_for_kinematic);
}

bool ProfileSearcherUtil::SearchForSolutionAtDiscomfort(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const std::vector<pb::SpeedDiversityOption>& diversity_options,
    const double immutable_time_at_discomfort,
    ReferenceGenerator* reference_generator,
    SearchSolution& per_discomfort_search_solution,
    pb::SpeedSearchDebug* shared_speed_search_debug,
    pb::PerDiscomfortDebug* new_discomfort_debug) const {
  DCHECK(reference_generator != nullptr);
  DCHECK(!diversity_options.empty());
  const int discomfort_ix = per_discomfort_problem.discomfort_ix;
  const double discomfort = per_discomfort_problem.discomfort;
  const bool for_gap_align = per_discomfort_problem.for_gap_align;
  const std::vector<Constraint>& constraints =
      per_discomfort_problem.constraints;
  const pb::TreeSearchType::Enum tree_search_type = GetTreeSearchType(
      constraints,
      /*planning_time_horizon_in_second=*/dt_ * num_steps_, for_gap_align);
  // TODO(tienan): we can transfer the ownership of PerdiscomfortSearchProblem
  // into this module, as it is not used elsewhere after. So the decisions,
  // profiles, etc. can be moved instead of copied into the SearchSolution.
  PrepareSearchSolutionAtDiscomfort(
      tree_search_type, discomfort_ix, discomfort,
      per_discomfort_problem.per_discomfort_initial_decisions,
      per_discomfort_problem.per_discomfort_risk_initial_decisions,
      per_discomfort_problem.ref_profile, per_discomfort_problem.time_map,
      per_discomfort_search_solution);

  pb::TimeMap* time_map_debug = nullptr;
  if (shared_speed_search_debug != nullptr) {
    time_map_debug = shared_speed_search_debug->mutable_time_map();
  }

  // TODO(nihar): Revise if we still need to regulate the initial profile.
  // Check if the initial state is qualified at this discomfort level.
  const Limits& limits_before_switch_time =
      per_discomfort_problem.profile_limits.GetLimitsBeforeSwitchTime();
  const Profile& initial_profile_at_discomfort =
      reference_generator->GetInitialProfileAtDiscomfortIx(discomfort_ix);
  if (!initial_profile_at_discomfort.empty() &&
      discomfort_ix < Discomforts::kLevels - 1) {
    // Get initial accel and the limits for current discomfort level.
    const double init_ra_state_accel = initial_profile_at_discomfort.front().a;

    const LimitRange& limit_accel_at_discomfort =
        init_ra_state_accel > 0.0 ? limits_before_switch_time.accel_a
                                  : limits_before_switch_time.brake_a;
    if (!limit_accel_at_discomfort.IsInRange(
            init_ra_state_accel, kInitialAccelOverLimitsToleranceInMpss)) {
      if (new_discomfort_debug != nullptr) {
        new_discomfort_debug->set_discomfort_idx(discomfort_ix);
        new_discomfort_debug->set_discomfort_level(discomfort);
        new_discomfort_debug->set_dominant_constraint_ix(-1);
        new_discomfort_debug->set_success(false);
        new_discomfort_debug->set_discomfort_increase_source(
            pb::PerDiscomfortDebug_DiscomfortIncreaseSource_kInitialStateOutOfDiscomfortLimits);  // NOLINT
      }
      return false;
    }
  }

  // Check if the min speed limit set by the speed limiters is achievable
  // by ego at current discomfort level. If not, we raise the
  // discomfort and continue to next iteration. The rationale behind this
  // is that we want to achieve the speed limit set by the limiters
  // regardless if other constraints are present or not. This will mostly
  // be triggered only at turns when ego has to slow down to be within the
  // given lateral acceleration limits. Even if there is an agent in front
  // of us, rather than relying on the agent to increase our discomfort
  // it is better to increase the discomfort here since we anyways intend to
  // make a turn safely where the ego stays within the given lateral
  // acceleration limit. We skip this check at max discomfort since
  // that is the best we can do.
  // TODO(nihar): Use brake limits here instead of accel limits.

  // We use regular discomfort limits, because we assume elevated discomfort is
  // higher, thus it can always brake more.
  if (!reference_generator->IsLimiterMinVAchievable(
          discomfort_ix,
          per_discomfort_problem.profile_limits.GetLimitsBeforeSwitchTime()
              .accel_a.min) &&
      discomfort_ix < Discomforts::kLevels - 1) {
    // TODO(nihar): Add some indicator in debug to indicate that
    // discomfort was raised to achieve min speed limit.
    if (new_discomfort_debug != nullptr) {
      new_discomfort_debug->set_discomfort_idx(discomfort_ix);
      new_discomfort_debug->set_discomfort_level(discomfort);
      new_discomfort_debug->set_dominant_constraint_ix(-1);
      new_discomfort_debug->set_success(false);
      new_discomfort_debug->set_discomfort_increase_source(
          pb::PerDiscomfortDebug_DiscomfortIncreaseSource_kCannotReachMinV);
    }
    return false;
  }
  // Adjust the profile to resolve all conflicts at this discomfort level.
  if (!ResolveAllConflictAtDiscomfort(per_discomfort_problem,
                                      immutable_time_at_discomfort,
                                      per_discomfort_search_solution,
                                      time_map_debug, new_discomfort_debug)) {
    return false;
  }

  // After resolving all conflicts, per_discomfort_search_solution must be a
  // successful solution. Note that now gap align constraint is by default no
  // pass ignore. Therefore, if there is a conflict, it will be caught during
  // the search process and continue to search for the next discomfort level.
  // In the future, if we want to log info for gap align constraint which is
  // ignored with permission, we may add here if needed.
  DCHECK(per_discomfort_search_solution.success);

  // Add additional info into the successful solution.
  if (shared_speed_search_debug != nullptr) {
    per_discomfort_search_solution.speed_search_debug =
        std::make_optional<pb::SpeedSearchDebug>(*shared_speed_search_debug);
    if (per_discomfort_search_solution.tree_search_type ==
        pb::TreeSearchType_Enum_kRiskMitigation) {
      auto* risk_mitigation_debug =
          per_discomfort_search_solution.speed_search_debug
              ->mutable_risk_mitigation_debug();
      PopulateRiskMitigationDebug(per_discomfort_problem,
                                  per_discomfort_search_solution, dt_,
                                  num_steps_, risk_mitigation_debug);
    }
  }
  per_discomfort_search_solution.searcher_result =
      for_gap_align ? pb::SearchResult_Enum_SUCCESS_WITH_GAP_ALIGN
                    : pb::SearchResult_Enum_SUCCESS;
  per_discomfort_search_solution.diversity_options = diversity_options;
  if (for_gap_align) {
    const bool is_gap_align_tree_search =
        per_discomfort_search_solution.tree_search_type ==
        pb::TreeSearchType_Enum_kLaneChangeGapAlign;
    const Profile& speed_profile =
        is_gap_align_tree_search ? per_discomfort_search_solution.tree_profile
                                 : per_discomfort_search_solution.profile;
    const std::vector<Decision>& speed_decisions =
        is_gap_align_tree_search ? per_discomfort_search_solution.tree_decisions
                                 : per_discomfort_search_solution.decisions;
    // Extracts gap info from a speed profile and constraint states at selected
    // discomfort.
    per_discomfort_search_solution.per_discomfort_gap_align_info_map =
        per_discomfort_problem.per_discomfort_gap_align_info_map;
    const GapAlignType type = GetGapAlignType(constraints);
    per_discomfort_search_solution.gap_info = ExtractGapInfo(
        /*for_seed_population=*/true, type, per_discomfort_problem.discomfort,
        /*offset_from_ra=*/0.0, per_discomfort_problem.ref_profile.back().x,
        speed_profile, per_discomfort_problem.constraints, speed_decisions,
        per_discomfort_problem.time_map,
        per_discomfort_problem.per_discomfort_gap_align_info_map,
        per_discomfort_problem.agent_reaction_calculator);
  }
  return true;
}

bool ProfileSearcherUtil::ResolveAllConflictAtDiscomfort(
    const PerDiscomfortSearchProblem& per_discomfort_problem,
    const double immutable_time_at_discomfort, SearchSolution& search_solution,
    pb::TimeMap* time_map_debug,
    pb::PerDiscomfortDebug* new_discomfort_debug) const {
  TRACE_EVENT_SCOPE(
      planner,
      DecoupledForwardManeuver_SpeedSearchResolveAllConflictAtDiscomfort);
  TRACE_EVENT_INSTANT(planner, ProfileSearcher_ResolveAllConflictAtDiscomfort);
  pb::PerDiscomfortDebug* local_discomfort_debug_ptr =
      FLAGS_planning_enable_intermediate_speed_search_result_debug
          ? new_discomfort_debug
          : nullptr;
  search_solution.success =
      AdjustForAllConflict(per_discomfort_problem, immutable_time_at_discomfort,
                           local_discomfort_debug_ptr, search_solution);
  DCHECK(math::IsApprox(
      search_solution.profile.front().t,
      plan_init_state_time_in_sec_ + immutable_time_at_discomfort));
  if (!search_solution.success &&
      search_solution.current_fail_info.reason ==
          DiscomfortIncreaseSource::
              PerDiscomfortDebug_DiscomfortIncreaseSource_kCannotBrake) {
    DCHECK_EQ(search_solution.earliest_brake_cix,
              search_solution.current_fail_info.constraint_ix);
  }
  // TODO(Nihar): Reduce unnecessary copies of time map for each cycle.
  if (time_map_debug != nullptr) {
    *time_map_debug = per_discomfort_problem.time_map.ToProto();
  }
  UpdatePerDiscomfortDebug(
      per_discomfort_problem, search_solution.earliest_brake_cix,
      search_solution.earliest_brake_time_ix,
      search_solution.earliest_accel_time_ix_for_adjust_for_pass,
      search_solution.current_fail_info, search_solution.success,
      new_discomfort_debug);
  return search_solution.success;
}

std::optional<pb::SpeedDiversityOption>
ProfileSearcherUtil::ShouldSearchForGapDiversityInRegularMode(
    const bool for_yield_homotopy,
    const std::vector<SearchSolution>& prev_search_solutions,
    const double curr_discomfort,
    const GapDiversitySettings& gap_diversity_settings,
    std::vector<int>& constraint_indices_to_explore_yield) const {
  // Check the first solution's discomfort.
  const SearchSolution& first_search_solution = prev_search_solutions.front();
  if (curr_discomfort - first_search_solution.discomfort >
      gap_diversity_settings.delta_discomfort_for_gap_diversity) {
    // Current solution's discomfort is larger than max allowed delta
    // discomfort, do not continue to search.
    return std::nullopt;
  }

  int slowest_solution_ix = 0;
  if (GapDiversityIsSatisfied(
          /*need_slower_gap=*/for_yield_homotopy, prev_search_solutions,
          gap_diversity_settings, slowest_solution_ix)) {
    return std::nullopt;
  }

  // For searching greedy solution.
  if (!for_yield_homotopy) {
    // When the searcher is exploring greedy solution and faster gap
    // homotopy is NOT satisfied, we should explore a faster gap.
    // |slowest_solution_ix| is NOT used for faster gap.
    return std::make_optional(pb::FASTER_GAP_FOR_GAP_DIVERSITY);
  }
  // For searching less greedy solution.
  DCHECK(for_yield_homotopy);

  // When searcher is exploring less greedy solution and slower gap
  // homotopy is NOT satisfied, we should explore a slower gap.
  // Explore yield solution for the tail obj that has a PASS decision in the
  // slowest solution.
  const SearchSolution& slowest_search_solution =
      prev_search_solutions[slowest_solution_ix];
  DCHECK(slowest_search_solution.gap_info.has_value());
  const int tail_obj_c_ix = slowest_search_solution.gap_info->tail_obj_c_ix;
  // If there is no tail object for the slowest search solution, we cannot
  // explore yield homotopy.
  if (tail_obj_c_ix < 0) {
    return std::nullopt;
  }
  DCHECK(slowest_search_solution.decisions[tail_obj_c_ix] == Decision::PASS)
      << tail_obj_c_ix;
  // Populate |constraint_indices_to_explore_yield| to set no pass.
  constraint_indices_to_explore_yield.emplace_back(tail_obj_c_ix);
  return std::make_optional(pb::SLOWER_GAP_FOR_GAP_DIVERSITY);
}

std::vector<pb::SpeedDiversityOption>
ProfileSearcherUtil::ShouldSearchAtDiscomfortInRegularMode(
    const std::vector<Constraint>& primitive_constraints,
    const std::vector<SearchSolution>& prev_search_solutions,
    const bool for_gap_align, const double global_discomfort_for_progress,
    const bool progress_for_kinematic,
    const GapDiversitySettings& gap_diversity_settings,
    const int curr_discomfort_ix, const bool for_yield_homotopy,
    std::vector<int>& constraint_indices_to_explore_yield) const {
  std::vector<pb::SpeedDiversityOption> diversity_options;
  // If no solution was found, add the default LOWEST_DISCOMFORT
  // diversity option, and then return.
  if (prev_search_solutions.empty()) {
    if (for_yield_homotopy) {
      // If |prev_search_solutions| is empty and for_yield_homotopy is true, it
      // must be that we have no solution at the given discomfort level. Thus,
      // we do not need to search this discomfort again.
      return diversity_options;
    }
    diversity_options.push_back(pb::LOWEST_DISCOMFORT);
    return diversity_options;
  }

  if (ShouldSearchForDiscomfortForProgressInRegularMode(
          primitive_constraints, prev_search_solutions.back(),
          Discomforts::GetDiscomfort(curr_discomfort_ix),
          global_discomfort_for_progress, for_yield_homotopy)) {
    diversity_options.push_back(pb::DISCOMFORT_FOR_PROGRESS);
  }

  if (ShouldSearchForProgressForKinematicInRegularMode(
          Discomforts::GetDiscomfort(curr_discomfort_ix),
          global_discomfort_for_progress, progress_for_kinematic,
          for_yield_homotopy)) {
    diversity_options.push_back(pb::PROGRESS_FOR_KINEMATIC);
  }

  if (for_gap_align) {
    // For gap align related diversity options.
    const std::optional<pb::SpeedDiversityOption> gap_diversity_option =
        ShouldSearchForGapDiversityInRegularMode(
            for_yield_homotopy, prev_search_solutions,
            Discomforts::GetDiscomfort(curr_discomfort_ix),
            gap_diversity_settings, constraint_indices_to_explore_yield);
    if (gap_diversity_option.has_value()) {
      diversity_options.push_back(gap_diversity_option.value());
    }
  }

  return diversity_options;
}

bool ProfileSearcherUtil::ShouldAcceptCurrSolutionInRegularMode(
    const std::vector<SearchSolution>& prev_search_solutions,
    const std::vector<pb::SpeedDiversityOption>& diversity_options,
    const SearchSolution& curr_search_solution,
    pb::SpeedSearchDebug* curr_solution_speed_search_debug) const {
  // The solution is always interesting if we do not have any solution yet.
  if (prev_search_solutions.empty()) {
    DCHECK_EQ(1, diversity_options.size());
    DCHECK(HasDiversityOption(diversity_options, pb::LOWEST_DISCOMFORT));
    return true;
  }

  if (HasDiversityOption(diversity_options, pb::PROGRESS_FOR_KINEMATIC)) {
    // We need to have a solution at |global_discomfort_for_progress| regardless
    // of constraint decisions. We will always keep the solution.
    return true;
  }

  if (HasDiversityOption(diversity_options, pb::DISCOMFORT_FOR_PROGRESS)) {
    // If we have a solution for discomfort_for_progress, accept it if it has a
    // different decision compared to the previous solution's.
    bool decision_changed = false;
    speed::pb::DiscomfortForProgressDebug::ProblemDiffDebug problem_diff_debug;
    // Check decision changes between the current solution and the previous
    // solution.
    const SearchSolution& prev_search_solution = prev_search_solutions.back();
    for (size_t i = 0; i < curr_search_solution.decisions.size(); ++i) {
      // TODO(Tingran): Revisit here if we want to check per constraint
      // decision change instead of accepting a solution due to any random
      // decision change.
      if (curr_search_solution.decisions[i] !=
          prev_search_solution.decisions[i]) {
        decision_changed = true;
        if (curr_solution_speed_search_debug != nullptr) {
          problem_diff_debug.add_decision_changed_constraint_ix(i);
          problem_diff_debug.add_prev_decision(
              prev_search_solution.decisions[i]);
          problem_diff_debug.add_curr_decision(
              curr_search_solution.decisions[i]);
        }
      }
    }

    if (decision_changed) {
      if (curr_solution_speed_search_debug != nullptr) {
        // initialize the problem_diff_debug.
        problem_diff_debug.set_prev_problem_discomfort(
            prev_search_solution.discomfort);
        problem_diff_debug.set_curr_problem_discomfort(
            curr_search_solution.discomfort);
        curr_solution_speed_search_debug->mutable_speed_diversity_debug()
            ->mutable_discomfort_for_progress_debug()
            ->add_problem_diff_debug()
            ->Swap(&problem_diff_debug);
      }
      return true;
    }
  }

  // Check for gap diversity.
  const bool has_slower_gap_diversity =
      HasDiversityOption(diversity_options, pb::SLOWER_GAP_FOR_GAP_DIVERSITY);
  const bool has_faster_gap_diversity =
      HasDiversityOption(diversity_options, pb::FASTER_GAP_FOR_GAP_DIVERSITY);
  DCHECK(!(has_slower_gap_diversity && has_faster_gap_diversity))
      << "Impossible to has both faster and slower gap diversity option";
  if (has_faster_gap_diversity || has_slower_gap_diversity) {
    if (!IsNewGap(curr_search_solution, prev_search_solutions)) {
      // Do not accept if it is NOT a new gap.
      return false;
    }
    const SearchSolution& first_search_solution = prev_search_solutions.front();
    const int default_gap_yield_decision_num = GetYieldGapAlignDecisionCount(
        first_search_solution.per_discomfort_gap_align_info_map,
        first_search_solution.decisions);
    const int curr_gap_yield_decision_num = GetYieldGapAlignDecisionCount(
        curr_search_solution.per_discomfort_gap_align_info_map,
        curr_search_solution.decisions);
    // Check if the new gap meets the requirements
    if ((has_faster_gap_diversity &&
         curr_gap_yield_decision_num < default_gap_yield_decision_num) ||
        (has_slower_gap_diversity &&
         curr_gap_yield_decision_num > default_gap_yield_decision_num)) {
      return true;
    }
  }
  return false;
}

std::vector<SearchSolution> ProfileSearcherUtil::PruneSolutionCandidates(
    const bool gap_align_for_lc,
    const GapDiversitySettings& gap_diversity_settings,
    const bool enable_extra_diversity_by_risk_evaluator,
    std::vector<SearchSolution>&& non_gap_align_search_solutions,
    std::vector<SearchSolution>&& gap_align_search_solutions) const {
  std::vector<SearchSolution> selected_solutions;
  selected_solutions.reserve(non_gap_align_search_solutions.size() +
                             gap_align_search_solutions.size());
  // In risk evaluator mode.
  if (enable_extra_diversity_by_risk_evaluator) {
    // Take all gap_align solutions if it exits, otherwise take all
    // non_gap_align solutions.
    if (gap_align_search_solutions.empty()) {
      DCHECK_GE(non_gap_align_search_solutions.size(), 1);
      for (auto& solution : non_gap_align_search_solutions) {
        selected_solutions.push_back(std::move(solution));
      }
    } else {
      for (auto& solution : gap_align_search_solutions) {
        selected_solutions.push_back(std::move(solution));
      }
    }
    DCHECK_GE(selected_solutions.size(), 1);
    return selected_solutions;
  }

  // In regular mode.

  // For non gap align solution, take the last search solution which is the most
  // progressive.
  DCHECK_GE(non_gap_align_search_solutions.size(), 1);
  // TODO(Tingran): When exclusive type is used, we will remove the dependency
  // of |gap_align_for_lc| as an input.
  const bool should_output_both_types_for_lc =
      gap_align_for_lc &&
      FLAGS_planning_speed_solver_output_non_gap_align_solution_for_lane_change;
  if (gap_align_search_solutions.empty() || should_output_both_types_for_lc) {
    selected_solutions.push_back(
        std::move(non_gap_align_search_solutions.back()));
    DCHECK_GE(selected_solutions.size(), 1);
  }
  if (gap_align_search_solutions.empty()) {
    return selected_solutions;
  }

  // For gap align solutions.
  DCHECK_GE(gap_align_search_solutions.size(), 1);
  if (!IsGapDiversityInvokedForRegularMode(gap_diversity_settings)) {
    // No gap diversity. So it is simply greedy, discomfort for progress, or
    // progress for kinematic solution.
    selected_solutions.push_back(std::move(gap_align_search_solutions.back()));
    DCHECK((selected_solutions.size() == 1) || should_output_both_types_for_lc);
    return selected_solutions;
  }
  for (auto& solution : gap_align_search_solutions) {
    selected_solutions.push_back(std::move(solution));
  }

  return selected_solutions;
}

bool ProfileSearcherUtil::MaybeStayStop(
    const int continuous_low_speed_cycle_count,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions, const Profile& search_profile,
    const Limits& limits, const DiscomfortVaryingMinRange& min_range,
    bool& is_coming_to_stop, double& desired_stopping_x,
    std::string& stay_stop_debug_str) const {
  // NOTE(Zekixu): early return when reverse driving.
  // TODO(Tingran): We still want to consider this when reverse driving.
  if (Seed::Access<token::ReverseDrivingBehaviorSeed>::GetMsg(
          SpeedCurrentFrame())
          ->state() == planner::pb::ReverseDrivingState::kAction) {
    absl::StrAppend(&stay_stop_debug_str, "Reverse.\nNo.\n");
    return false;
  }

  // NOTE(Jiakai): Early return when EGO is executing an unstuck motion plan.
  const planner::pb::UnstuckPlannerState unstuck_planner_curr_state =
      Seed::Access<token::UnstuckPlannerSeed>::GetMsg(SpeedLastFinishedFrame())
          ->current_state();
  const bool is_unstuck_planner_currently_invoked =
      unstuck_planner_curr_state != planner::pb::UnstuckPlannerState::IDLE;
  if (is_unstuck_planner_currently_invoked) {
    absl::StrAppend(&stay_stop_debug_str, "Unstuck motion plan.\nNo.\n");
    return false;
  }

  const State& search_profile_first_state = search_profile.front();
  // Check if we are almost stopped. If not, return false early if:
  // 1. Ego speed is NOT very close to being static (> 0.1m/s).
  // 2. continuous_low_speed_cycle_count is NOT large enough.
  // TODO(Tingran): we will consider in future if using 0.0 is good enough.
  // TODO(control): use the speed threshold that control uses to latch stopping
  // state.
  if (search_profile_first_state.v > constants::kSpeedSolverStaticSpeedInMps ||
      (continuous_low_speed_cycle_count * dt_ <
       kStaticSpeedDurationToConsiderStayStoppedInSecond)) {
    absl::StrAppendFormat(&stay_stop_debug_str,
                          "First state v: %.6f, low_speed_cycle: %d.\nNo.\n",
                          search_profile_first_state.v,
                          continuous_low_speed_cycle_count);
    return false;
  }

  // Find all should-stay-stopped constraints by distance conditions.
  // See details in:
  // https://docs.google.com/document/d/1CC66EIYT83KQH0qfNiFLJR9SzHKfP0bfbyluyg4_AAM/edit
  // https://docs.google.com/document/d/1IpADW-M2LL0PoVu67GFfYxwgobbnhASser5ZsznTdvA/edit
  // See the sketch in:
  // https://drive.google.com/file/d/1NJLD-uj-Yyq_9v-PGHX7TYCj4vl4ZFIE/view?usp=share_link
  std::vector<ShouldStayStoppedConstraintInfo>
      should_stay_stopped_constraint_infos;
  should_stay_stopped_constraint_infos.reserve(constraints.size());
  stay_stop_debug_str +=
      strings::StringPrintf("Should-Stay-Stopped Constraints:\n");
  for (size_t ix = 0; ix < constraints.size(); ++ix) {
    const Constraint& constraint = constraints[ix];
    // Do not consider the constraint for stay-stopped if we do not yield to it,
    // or it is a GAP_ALIGN constraint.
    if (decisions[ix] != Decision::YIELD ||
        constraint.type == pb::Constraint::GAP_ALIGN) {
      continue;
    }
    const double min_creep_distance = constraint.settings.min_creep_distance;
    // 1. For all constraint which sets the min_creep_distance to non zero.
    // If the distance from ego front bumper to any of its yield time map entry
    // is closer than min_creep_distance, it is a should-stay-stopped
    // constraint.
    // TODO(tienan): merge the shared part between condition 1 and 2 to shared
    // a utility function.
    if (!math::NearZero(min_creep_distance)) {
      const double selected_discomfort = constraint.selected_discomfort;
      const double min_creep_plus_yield_buffer_dist =
          FLAGS_planning_enable_overlap_v2
              ? min_creep_distance +
                    GetYieldBuffer(constraint, min_range, selected_discomfort)
              : ra_to_fb_shift_ + min_creep_distance +
                    GetYieldBuffer(constraint, min_range, selected_discomfort);
      double closest_fb_dist_to_speed_constraint =
          std::numeric_limits<double>::infinity();
      const double time_should_exit_stay_stopped = GetTimeShouldExitStayStopped(
          constraint, min_creep_plus_yield_buffer_dist,
          closest_fb_dist_to_speed_constraint);
      // It is a should-stay-stopped constraint that ego should consider if
      // any future yield entries of it requires ego to stay stopped.
      if (time_should_exit_stay_stopped >= 0.0) {
        std::string fb_dist_str =
            std::isinf(closest_fb_dist_to_speed_constraint)
                ? "inf"
                : absl::StrFormat("%.2f", closest_fb_dist_to_speed_constraint);
        absl::StrAppendFormat(&stay_stop_debug_str,
                              "Min-creep Constraint ID: %s, "
                              "min_creep_plus_yield_buffer_dist: %.2f, "
                              "closest_fb_dist_to_speed_constraint: %s.\n",
                              constraint.unique_constraint_id,
                              min_creep_plus_yield_buffer_dist, fb_dist_str);
        should_stay_stopped_constraint_infos.push_back(
            {ix, time_should_exit_stay_stopped,
             closest_fb_dist_to_speed_constraint});
      }
    }
    // 2. For all constraint which sets the always_stay_stopped to true. We
    // check the minimum value to the largest buffer for the constraint, aka the
    // buffer at discomfort 0.0.
    // TODO(tienan): check if Condition 1 and 2 should be mutually exclusive.
    if (constraint.settings.always_stay_stopped) {
      // NOTE: It is by design to consider yield_stop_range here for stay
      // stopped logic. As speed optimizer has a SOFT stop_range cost, so the
      // final profile, at extreme case, can be be smaller than search profile
      // by stop_range amount. we add this to the equation to reflect the
      // extreme scenario.
      const double yield_stop_range_plus_buffer =
          FLAGS_planning_enable_overlap_v2
              ? constraint.settings.yield_stop_range(0.0) + kPositionError +
                    GetYieldBuffer(constraint, min_range,
                                   /*discomfort=*/0.0)
              : ra_to_fb_shift_ + constraint.settings.yield_stop_range(0.0) +
                    kPositionError +
                    GetYieldBuffer(constraint, min_range,
                                   /*discomfort=*/0.0);
      double closest_fb_dist_to_speed_constraint =
          std::numeric_limits<double>::infinity();
      const double time_should_exit_stay_stopped =
          GetTimeShouldExitStayStopped(constraint, yield_stop_range_plus_buffer,
                                       closest_fb_dist_to_speed_constraint);
      if (time_should_exit_stay_stopped >= 0.0) {
        std::string fb_dist_str =
            std::isinf(closest_fb_dist_to_speed_constraint)
                ? "inf"
                : absl::StrFormat("%.2f", closest_fb_dist_to_speed_constraint);

        absl::StrAppendFormat(&stay_stop_debug_str,
                              "Always-stay-stop Constraint ID: %s, "
                              "yield_stop_range_plus_buffer: %.2f, "
                              "closest_fb_dist_to_speed_constraint: %s.\n",
                              constraint.unique_constraint_id,
                              yield_stop_range_plus_buffer, fb_dist_str);
        should_stay_stopped_constraint_infos.push_back(
            {ix, time_should_exit_stay_stopped,
             closest_fb_dist_to_speed_constraint});
      }
    }
  }

  // Iterate all should-stay-stopped constraints. If any of them does not
  // request quick-start, return true to stay-stopped.
  absl::StrAppend(&stay_stop_debug_str,
                  "\nQuick-Start-To-Follow Constraints:\n");
  for (const ShouldStayStoppedConstraintInfo& info :
       should_stay_stopped_constraint_infos) {
    const Constraint& constraint = constraints[info.constraint_ix];
    DCHECK_EQ(decisions[info.constraint_ix], Decision::YIELD);
    if (!ShouldQuickStartToOneConstraint(constraint,
                                         info.time_should_exit_stay_stopped,
                                         stay_stop_debug_str)) {
      const Profile stopping_profile = PopulateStopProfile(
          search_profile_first_state, num_steps_, limits, dt_);
      PopulateStayStopInfo(search_profile, stopping_profile, min_range,
                           constraint, info.closest_fb_dist_to_constraint,
                           ra_to_fb_shift_, is_coming_to_stop,
                           desired_stopping_x);
      return true;
    }
  }
  return false;
}

void ProfileSearcherUtil::PrepareSearchSolutionAtDiscomfort(
    const pb::TreeSearchType::Enum tree_search_type, const int discomfort_ix,
    const double discomfort,
    const std::vector<Decision>& per_discomfort_initial_decisions,
    const std::optional<std::vector<Decision>>&
        per_discomfort_risk_initial_decisions,
    const Profile& ref_profile, const TimeMap& per_discomfort_time_map,
    SearchSolution& search_solution) const {
  search_solution.per_discomfort_initial_decisions =
      per_discomfort_initial_decisions;
  search_solution.per_discomfort_risk_initial_decisions =
      per_discomfort_risk_initial_decisions;

  search_solution.success = false;
  search_solution.discomfort_ix = discomfort_ix;
  search_solution.discomfort = discomfort;

  search_solution.decisions = per_discomfort_initial_decisions;
  search_solution.tree_decisions =
      per_discomfort_risk_initial_decisions.has_value()
          ? per_discomfort_risk_initial_decisions.value()
          : per_discomfort_initial_decisions;

  search_solution.profile = ref_profile;
  // NOTE(Tingran): We must copy ref profile at regular discomfort instead of
  // risk ref profile at elevated discomfort. Otherwise, it may be higher than
  // what it could actually reach.
  search_solution.tree_profile = ref_profile;

  search_solution.time_map.CopyEntriesFrom(per_discomfort_time_map);
  search_solution.tree_search_type = tree_search_type;
}

}  // namespace speed
}  // namespace planner
