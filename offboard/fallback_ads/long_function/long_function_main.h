#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_MAIN_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_MAIN_H_

#include <stdbool.h>
#include <stdint.h>

#ifdef __cplusplus
extern "C" {
#endif

// longitudinal control function
extern void LongitudinalControlFunction_Init(void);

// vehicle signal process
extern void VehicleSignalProcess_10ms_Runnable();

// object pre-selection
extern void ObjectPreSelection_10ms_Runnable();

// free stop control
extern void FreeStopControl_10ms_Runnable();

// collision detection system
extern void CollisionDetectionSystem_10ms_Runnable();

// situation evaluation decision
extern void SituationEvaluationDecision_10ms_Runnable();

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_LONG_FUNCTION_MAIN_H_
