#include "onboard/planner/path/reasoning/agent_intention/reasoner/pull_over_path_reasoner/pull_over_path_reasoner.h"

#include <utility>

#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"

namespace planner {
namespace path {

namespace {

// The longitudinal buffer when adding a repulsion to the large static agent.
constexpr double kLongitudinalRepulsionBufferLargeAgentInMeter = 5.0;

// The longitudinal buffer when adding a repulsion to the static agent.
constexpr double kLongitudinalRepulsionBufferInMeter = 3.0;

// The brake limit to determine whether we think Ego cannot longitudinally yield
// hence need to nudge to avoid pedestrian in front during pullover.
constexpr double kLeadingDynamicPedestrianPullOverBrakeThresholdInMpss = -1.0;

// The brake limit to determine whether we think Ego cannot longitudinally yield
// hence need to nudge to avoid <PERSON><PERSON><PERSON> in front during pullover.
constexpr double kLeadingDynamicCyclistPullOverBrakeThresholdInMpss = -1.5;

// The brake limit to determine whether we think E<PERSON> cannot longitudinally yield
// hence need to nudge to avoid agent in front during pullover.
constexpr double kLeadingDynamicAgentPullOverBrakeThresholdInMpss = -3.0;

bool ShouldNudgeAgent(
    const AgentInLaneStates& agent_inlane_state,
    const EgoInLaneParams& ego_param,
    const LateralBlockingStateMetaData& blocking_state_meta_data) {
  auto agent_type = agent_inlane_state.agent_metadata.agent_type;
  double brake_threshold_in_mpss = 0.0;
  if (agent_type == voy::perception::PED) {
    brake_threshold_in_mpss =
        kLeadingDynamicPedestrianPullOverBrakeThresholdInMpss;
  } else if (agent_type == voy::perception::CYCLIST) {
    brake_threshold_in_mpss =
        kLeadingDynamicCyclistPullOverBrakeThresholdInMpss;
  } else {
    brake_threshold_in_mpss = kLeadingDynamicAgentPullOverBrakeThresholdInMpss;
  }
  return ShouldNudgeAgentDuringPullOver(ego_param, agent_inlane_state,
                                        blocking_state_meta_data,
                                        /*brake_limit_mpss=*/
                                        brake_threshold_in_mpss);
}

bool ShouldNudgeAgent(
    const ObjectOccupancyState& object_occupancy_state,
    const EgoInLaneParams& ego_param,
    const LateralBlockingStateMetaData& blocking_state_meta_data) {
  auto agent_type = object_occupancy_state.object_type();
  double brake_threshold_in_mpss = 0.0;
  if (agent_type == voy::perception::PED) {
    brake_threshold_in_mpss =
        kLeadingDynamicPedestrianPullOverBrakeThresholdInMpss;
  } else if (agent_type == voy::perception::CYCLIST) {
    brake_threshold_in_mpss =
        kLeadingDynamicCyclistPullOverBrakeThresholdInMpss;
  } else {
    brake_threshold_in_mpss = kLeadingDynamicAgentPullOverBrakeThresholdInMpss;
  }
  return ShouldNudgeAgentDuringPullOver(ego_param, object_occupancy_state,
                                        blocking_state_meta_data,
                                        /*brake_limit_mpss=*/
                                        brake_threshold_in_mpss);
}

}  // namespace

// Adds repulsion when Ego is pulling over.
std::unique_ptr<RepulsionMetaData> PullOverPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& /* reusable_last_path */,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  return PreRepulsionReasonInternal(
      blocking_state_data.reasoning_info, agent_inlane_state.object_id,
      agent_inlane_state.tracked_state.inlane_param.pose.contour().polygon(),
      agent_inlane_state.tracked_state.inlane_param.pose.heading(),
      agent_inlane_state.tracked_state.inlane_param.full_body_start_arclength_m,
      agent_inlane_state.tracked_state.inlane_param.full_body_end_arclength_m,
      agent_inlane_state.tracked_state.inlane_param
          .full_body_end_lateral_distance_m,
      robot_state_snapshot.heading(), repulsion_required_lat_gap, debug_str);
}

// Adds repulsion when Ego is pulling over.
std::unique_ptr<RepulsionMetaData> PullOverPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& /* reusable_last_path */,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  return PreRepulsionReasonInternal(
      blocking_state_data.reasoning_info, object_occupancy_state.object_id(),
      object_occupancy_state.pose().contour().polygon(),
      object_occupancy_state.pose().contour_heading(),
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_start_arclength_m,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_end_arclength_m,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_end_lateral_distance_m,
      robot_state_snapshot.heading(), repulsion_required_lat_gap, debug_str);
}

// Add repulsion to the pullover rear obstalce. For regular agents, the
// repulsion is formulated by two points (a segment) parallel to the reference
// curve that covers the buffered longitudinal range and laterally buffered from
// the agent left edge with the repulsion required lat gap. For large agents,
// create a bounding box based on the polygon and the box heading is always
// along with EGO. The repulsions is formulated by a lateral offset line
// starting from a front-left corner to a fix distance along box heading.
std::unique_ptr<RepulsionMetaData>
PullOverPathReasoner::PreRepulsionReasonInternal(
    const pb::TrajectoryReasoningInfo& reasoning_info, int64_t object_id,
    const math::geometry::Polygon2d& agent_contour, const double agent_heading,
    const double agent_start_arclength_m, const double agent_end_arclength_m,
    const double agent_end_lateral_distance_m, const double ego_heading,
    const double repulsion_required_lat_gap, std::string& debug_str) const {
  if (reasoning_info.oriented_side_to_ego_centerline() !=
      math::pb::Side::kRight) {
    debug_str +=
        "Agent is not oriented to EGO's centerline. Skip the generation of "
        "repulsion";
    // Only add this repulsion to the right-side pullover rear obstacle
    return nullptr;
  }

  if (reasoning_info.is_large_vehicle()) {
    debug_str += "adding repulsion for large vehicle\n";
    // For large agents(bus, truck), add cartesian repulsion for accurate shape
    // and stronger strength.
    const double heading_diff =
        std::abs(math::AngleDiff(agent_heading, ego_heading));
    if (heading_diff > M_PI_4 && heading_diff < (M_PI - M_PI_4)) {
      // The agent's heading is not same or opposite to EGO's heading. Skip the
      // generation of repulsion
      debug_str +=
          "Agent's heading is not same or opposite to EGO's "
          "heading. ";
      return nullptr;
    }

    const double box_heading =
        heading_diff < M_PI_4 ? agent_heading : agent_heading + M_PI;
    const math::geometry::OrientedBox2d agent_box(agent_contour, box_heading);
    math::geometry::Point2d polyline_start = agent_box.FrontLeftPoint();
    math::geometry::Point2d polyline_end = agent_box.FrontLeftPoint();
    const math::geometry::Point2d lon_offset_vec = math::geometry::Multiply(
        agent_box.length_unit(), kLongitudinalRepulsionBufferLargeAgentInMeter);
    const math::geometry::Point2d lat_offset_vec = math::geometry::Multiply(
        agent_box.width_unit(), repulsion_required_lat_gap);
    math::geometry::AssignmentAdd(polyline_start, lat_offset_vec);
    math::geometry::AssignmentAdd(polyline_end, lat_offset_vec);
    math::geometry::AssignmentAdd(polyline_end, lon_offset_vec);

    return std::make_unique<RepulsionMetaData>(
        RepulsionMetaData::RepulsionDirection::kLeft,
        math::geometry::PolylineCurve2d({polyline_start, polyline_end}),
        /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false, path::BoundaryStrength::kModerate,
        /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
        reasoner_id_,
        ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
        /*trajectory_id_in=*/std::nullopt);
  }
  // For regular agents(sedans), add frenet repulsion direcetly.
  debug_str += "adding repulsion for regular vehicle\n";
  std::vector<FrenetPoint> points;
  points.reserve(2);
  points.emplace_back(agent_start_arclength_m, agent_end_lateral_distance_m +
                                                   repulsion_required_lat_gap);
  points.emplace_back(
      agent_end_arclength_m + kLongitudinalRepulsionBufferInMeter,
      agent_end_lateral_distance_m + repulsion_required_lat_gap);
  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kLeft, std::move(points),
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/std::nullopt);
}

bool PullOverPathReasoner::ShouldInvokePreReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const AgentInLaneStates& agent_inlane_state, pb::BehaviorType behavior_type,
    bool /*is_stationary*/) const {
  auto agent_type = agent_inlane_state.agent_metadata.agent_type;
  if (behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type_ ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    if (agent_type == voy::perception::ObjectType::PED ||
        agent_type == voy::perception::ObjectType::CYCLIST ||
        blocking_state_meta_data.reasoning_info.is_lead_and_merge()) {
      bool should_nudge = ShouldNudgeAgent(agent_inlane_state, ego_param_,
                                           blocking_state_meta_data);
      const bool has_soft_blocking =
          (blocking_state_meta_data.blocking_sequence_type ==
               pb::BlockingSequenceType::kPartialBlocking ||
           blocking_state_meta_data.blocking_sequence_type ==
               pb::BlockingSequenceType::kMixedBlocking);
      if (!should_nudge || has_soft_blocking) {
        // Only do special pullover PreReason when should nudge is false or
        // there is soft blocking. Otherwise rely on general pre reason logic in
        // other reasoners.
        return true;
      }
    }
  }
  return false;
}

bool PullOverPathReasoner::ShouldInvokePreReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const ObjectOccupancyState& object_occupancy_state,
    pb::BehaviorType behavior_type, bool /*is_stationary*/) const {
  auto agent_type = object_occupancy_state.object_type();
  if (behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type_ ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    if (agent_type == voy::perception::ObjectType::PED ||
        agent_type == voy::perception::ObjectType::CYCLIST ||
        blocking_state_meta_data.reasoning_info.is_lead_and_merge()) {
      bool should_nudge = ShouldNudgeAgent(object_occupancy_state, ego_param_,
                                           blocking_state_meta_data);
      const bool has_soft_blocking =
          (blocking_state_meta_data.blocking_sequence_type ==
               pb::BlockingSequenceType::kPartialBlocking ||
           blocking_state_meta_data.blocking_sequence_type ==
               pb::BlockingSequenceType::kMixedBlocking);
      if (!should_nudge || has_soft_blocking) {
        // Only do special pullover PreReason when should nudge is false or
        // there is soft blocking. Otherwise rely on general pre reason logic in
        // other reasoners.
        return true;
      }
    }
  }
  return false;
}

bool PullOverPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /*object_sl_boundary*/,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  const bool should_nudge =
      ShouldNudgeAgent(agent_inlane_state, ego_param_, blocking_state_data);
  if (!should_nudge) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    return true;
  }
  const bool has_soft_blocking =
      (blocking_state_data.blocking_sequence_type ==
           pb::BlockingSequenceType::kPartialBlocking ||
       blocking_state_data.blocking_sequence_type ==
           pb::BlockingSequenceType::kMixedBlocking);
  DCHECK(has_soft_blocking);
  // For pullover nudge behavior, if the object originally has
  // soft blocking, set the blocking sequence in trajectory intention data
  // to be HARD_BLOCKING instead of SOFT_BLOCKING so that we will
  // longitudinally yield for it.
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  std::vector<pb::BlockingSequence> blocking_sequences =
      (blocking_state_data.blocking_sequence_type ==
       pb::BlockingSequenceType::kPartialBlocking)
          ? std::vector<pb::BlockingSequence>{GetStartEndBlockingSequence(
                blocking_state_data.blocking_sequences,
                /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING)}
          : GetMixedBlockingSequences(blocking_state_data.blocking_sequences);
  std::for_each(
      blocking_sequences.begin(), blocking_sequences.end(),
      [this](auto& sequence) {
        // TODO(Harry): Only for lead and merge?
        *sequence.mutable_buffer_config() = st_buffer_config_;
        sequence.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
      });
  trajectory_intention_data.blocking_sequences = std::move(blocking_sequences);
  return true;
}

bool PullOverPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /*object_sl_boundary*/,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  const bool should_nudge =
      ShouldNudgeAgent(object_occupancy_state, ego_param_, blocking_state_data);
  if (!should_nudge) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    return true;
  }
  const bool has_soft_blocking =
      (blocking_state_data.blocking_sequence_type ==
           pb::BlockingSequenceType::kPartialBlocking ||
       blocking_state_data.blocking_sequence_type ==
           pb::BlockingSequenceType::kMixedBlocking);
  DCHECK(has_soft_blocking);
  // For pullover nudge behavior, if the object originally has
  // soft blocking, set the blocking sequence in trajectory intention data
  // to be HARD_BLOCKING instead of SOFT_BLOCKING so that we will
  // longitudinally yield for it.
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  std::vector<pb::BlockingSequence> blocking_sequences =
      (blocking_state_data.blocking_sequence_type ==
       pb::BlockingSequenceType::kPartialBlocking)
          ? std::vector<pb::BlockingSequence>{GetStartEndBlockingSequence(
                blocking_state_data.blocking_sequences,
                /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING)}
          : GetMixedBlockingSequences(blocking_state_data.blocking_sequences);
  std::for_each(
      blocking_sequences.begin(), blocking_sequences.end(),
      [this](auto& sequence) {
        *sequence.mutable_buffer_config() = st_buffer_config_;
        sequence.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
      });
  // TODO(Harry): Only for lead and merge?
  trajectory_intention_data.blocking_sequences = std::move(blocking_sequences);
  return true;
}

}  // namespace path
}  // namespace planner
