#include "planner/path/reasoning/agent_intention/reasoner/static_agent_path_reasoner/static_agent_path_reasoner.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "base/optional_value_accessor.h"
#include "geometry/algorithms/arithmetic.h"
#include "geometry/algorithms/intersection_area.h"
#include "geometry/algorithms/simplify.h"
#include "geometry/algorithms/specialization/distance_point2d.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline.h"
#include "geometry/model/polyline_curve.h"
#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/math_util.h"
#include "math/piecewise_linear_function.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/scenario_identifier_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/physics/motion_model_2d.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "prediction_protos/stationary_intention.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

// The agent's assumed decel after it notices ego.
constexpr double kAgentDecelAfterNoticingEgo = -3.0;  // in m/s^2
// The agent's fov angle range & distance, like the following picture
// illustrates.
/*
\               /
 \  30m        /
  \           /
   \         /
    \       /)30
     ┌─────┐
     │     │
     │     │
     │     │
     │     │
     │     │
     └─────┘
*/
const double kAgentFovAngle = math::Degree2Radian(30.0);
constexpr double kAgentFovDistance = 30.0;  // in m
// The area ratio threshold of ego entering agent's fov where agent could notice
// ego.
constexpr double kIntersectionAreaRatioThresholdToNoticeEgo = 0.3;
// The maximum time duration to consider start-to-move traj.
constexpr int64 kMaxTimeStampToConsiderStartToMoveTraj = 4000;  // in ms

constexpr double kSamplingTime = 0.1;        // in s
constexpr double kCreepingAcc = 0.2;         // in m/s^2
constexpr double kFullStartAcc = 1.0;        // in m/s^2
constexpr double kJerkUpperBound = 2.0;      // in m/s^3
constexpr double kDistanceUpperBound = 5.0;  // in m
constexpr double kExtraRepulsionLatBufferForNotFollowLaneAgents = 0.5;  // in m
constexpr double kMinAccelForLosingPatienceAgent = 0.5;  // in m/s^2

// If agent was starting up within kCreepingCheckTimeThreshold, we would still
// assign it a start-to-move traj by considering its average jerk.
constexpr double kCreepingCheckTimeThreshold = 2.0;  // in s
// The longitudinal buffer when adding a repulsion to the static agent.
constexpr double kLongitudinalRepulsionBufferInMeter = 3.0;

// The threshold of the height of the object to be considered as a high object.
// We prefer not to undercarriage over a high object.
constexpr double kDrivableHeightThresholdForFODInM = 0.15;
// The threshold of the width of the object to be considered as a wide object.
// We prefer not to drive through a wide object.
constexpr double kDrivableWidthThresholdForFODInM = 1.1;
// The threshold of the width of the object. We prefer not to undercarriage over
// the object if its width is beyond this value.
constexpr double kUndercarriageOverWidthThresholdInM = 0.5;
// The threshold of the ego speed to be considered as the high speed.
constexpr double kHighSpeedThresholdInMps = 8.33;
// The following three parameters are used to check whether a static fod is safe
// to nudge. The threshold of the ego speed to do a safe nudge.
constexpr double kSafeNudgeLateralAccelThresholdInMpss = 3.0;
// The required lateral gap to the fod.
constexpr double kRequiredLateralGapForNudgingFodInM = 0.2;
// The extra lateral buffer to undercarriage over an FOD. Mainly the distance
// from the wheel to the bbox.
constexpr double kExtraLateralBufferForUndercarriageOverFodInM = 0.25;
// The maximum allowed longitudinal deceleration and lateral acceleration/jerk
// limits for different discomfort levels.
constexpr int kNumDiscomfortLevels = 3;
// Note: The deceleration values are defined positive for binary search, but
// please to make sure that the deceleration should be negative.
const std::vector<double> max_allowed_longitudinal_decels{2.0, 3.0, 5.0};
const std::vector<double> max_allowed_lateral_accels{1.0, 2.0, 3.0};
const std::vector<double> max_allowed_lateral_jerks{1.0, 2.0, 3.0};

// The max time shrink ratio when considering start-to-move traj's uncertainty.
constexpr double kMaxTimeShrinkRatio = 0.5;
// The comfort lateral repulsion buffer for xlane nudge.
constexpr double kLateralComfortableXLaneNudgeBufferInMeter = 1.0;

// If the current traj is more conservative than the previous one, the smoothed
// result should equal to 0.3 * previous + 0.7 * current.
constexpr double kSmoothRatioWhenMoreConservative = 0.7;

// Maximum lateral accel and jerk used for bus in bus bulb repulsion.
constexpr double kMaxLateralAccelForBusInBusBulbRepulsionInMpss = 1.0;
constexpr double kMaxLateralJerkForBusInBusBulbRepulsionInMpss = 1.0;

// Adds brake motion along brake heading and extends the corresponding path.
void AddBrakeMotion(double brake_heading, speed::Profile& profile_upper_bound,
                    math::geometry::Polyline2d& agent_center_path_points) {
  DCHECK(!profile_upper_bound.empty());
  DCHECK(!agent_center_path_points.empty());
  const double v_before_decel = profile_upper_bound.back().v;
  if (v_before_decel < math::constants::kEpsilon) {
    return;
  }
  const double stop_duration = v_before_decel / -kAgentDecelAfterNoticingEgo;
  const double stop_distance = v_before_decel * stop_duration * 0.5;
  if (math::NearZero(stop_distance)) {
    return;
  }
  profile_upper_bound.emplace_back(speed::State(
      profile_upper_bound.back().t + stop_duration,
      profile_upper_bound.back().x + stop_distance, 0.0, 0.0, 0.0));
  math::geometry::Point2d new_point = math::geometry::Add(
      agent_center_path_points.back(),
      math::geometry::Multiply(
          math::geometry::FromHeadingToUnit2D<math::geometry::Point2d>(
              brake_heading),
          stop_distance));
  agent_center_path_points.push_back(std::move(new_point));
}

// Creates slow acc traj poses along heading of init_pose.
std::vector<pb::TrajectoryPose> CreateSlowAccTrajPoses(
    const pb::TrajectoryPose& init_pose) {
  pb::TrajectoryPose modified_init_pose = init_pose;
  modified_init_pose.set_accel(kCreepingAcc);
  return physics::CreateLinearTrajectoryInConstAccel(
      kCreepingAcc, init_pose.timestamp(), init_pose.speed(),
      init_pose.heading(), init_pose.x_pos(), init_pose.y_pos(),
      init_pose.odom(),
      /*speed_lower_bound=*/-std::numeric_limits<double>::infinity(),
      /*speed_upper_bound=*/std::numeric_limits<double>::infinity(),
      kDistanceUpperBound, math::Ms2Sec(kMaxTimeStampToConsiderStartToMoveTraj),
      kSamplingTime);
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageJerk(
    std::optional<double> average_jerk, double init_timestamp,
    double acceleration, double speed, double heading, double pos_x,
    double pos_y) {
  if (!average_jerk.has_value() || *average_jerk < math::constants::kEpsilon) {
    return {};
  }
  return physics::CreateTrajectoryInConstJerk(
      std::min(kJerkUpperBound, *average_jerk),
      static_cast<int64_t>(init_timestamp), acceleration, speed, heading, pos_x,
      pos_y, /*init_odom=*/0.0,
      /*acceleration_lower_bound=*/-std::numeric_limits<double>::infinity(),
      kFullStartAcc, kDistanceUpperBound,
      math::Ms2Sec(kMaxTimeStampToConsiderStartToMoveTraj), kSamplingTime);
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageJerk(
    std::optional<double> average_jerk, double init_timestamp,
    const AgentSnapshotInLaneParam& agent_snapshot_in_lane_param) {
  return CreateTrajByRecentAverageJerk(
      average_jerk, init_timestamp,
      agent_snapshot_in_lane_param.acceleration_mpss,
      agent_snapshot_in_lane_param.speed_mps,
      agent_snapshot_in_lane_param.pose.heading(),
      agent_snapshot_in_lane_param.pose.center_x(),
      agent_snapshot_in_lane_param.pose.center_y());
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageJerk(
    std::optional<double> average_jerk, double init_timestamp,
    const ObjectOccupancyParam& object_occupancy_param,
    const TrafficParticipantPose& pose) {
  return CreateTrajByRecentAverageJerk(
      average_jerk, init_timestamp, object_occupancy_param.acceleration_mpss,
      object_occupancy_param.speed_mps, pose.heading(), pose.center_2d().x(),
      pose.center_2d().y());
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageAccel(
    std::optional<double> average_accel, double init_timestamp, double speed,
    double heading, double pos_x, double pos_y) {
  if (!average_accel.has_value() ||
      *average_accel < math::constants::kEpsilon) {
    return {};
  }
  return physics::CreateLinearTrajectoryInConstAccel(
      std::min(*average_accel, kFullStartAcc),
      static_cast<int64_t>(init_timestamp), speed, heading, pos_x, pos_y,
      /*init_odom=*/0.0,
      /*speed_lower_bound=*/-std::numeric_limits<double>::infinity(),
      /*speed_upper_bound=*/std::numeric_limits<double>::infinity(),
      kDistanceUpperBound, math::Ms2Sec(kMaxTimeStampToConsiderStartToMoveTraj),
      kSamplingTime);
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageAccel(
    std::optional<double> average_accel, double init_timestamp,
    const AgentSnapshotInLaneParam& agent_snapshot_in_lane_param) {
  return CreateTrajByRecentAverageAccel(
      average_accel, init_timestamp, agent_snapshot_in_lane_param.speed_mps,
      agent_snapshot_in_lane_param.pose.heading(),
      agent_snapshot_in_lane_param.pose.center_x(),
      agent_snapshot_in_lane_param.pose.center_y());
}

std::vector<pb::TrajectoryPose> CreateTrajByRecentAverageAccel(
    std::optional<double> average_accel, double init_timestamp,
    const ObjectOccupancyParam& object_occupancy_param,
    const TrafficParticipantPose& pose) {
  return CreateTrajByRecentAverageAccel(
      average_accel, init_timestamp, object_occupancy_param.speed_mps,
      pose.heading(), pose.center_2d().x(), pose.center_2d().y());
}

// Gets the possible accel for a losing patience agent. Nullopt if the agent
// doesn't need to be taken into consideration.
std::optional<double> GetAccelForLosingPatienceAgent(
    const voy::perception::ObjectType& agent_type,
    int64 last_stationary_timestamp, int64 init_timestamp,
    std::optional<double> average_accel) {
  if (agent_type != voy::perception::ObjectType::VEHICLE) {
    return std::nullopt;
  }
  if (last_stationary_timestamp + math::Sec2Ms(kCreepingCheckTimeThreshold) <
      init_timestamp) {
    return std::nullopt;
  }
  const double accel_for_losing_patience_agent =
      average_accel.has_value()
          ? std::max(kMinAccelForLosingPatienceAgent, *average_accel)
          : kMinAccelForLosingPatienceAgent;
  return accel_for_losing_patience_agent;
}

std::vector<pb::TrajectoryPose> GetStartToMoveTrajectoryPoses(
    const prediction::pb::PredictedTrajectory* start_to_move_trajectory,
    const prediction::pb::StationaryIntention& stationary_intention,
    voy::perception::ObjectType agent_type,
    const AgentSnapshotInLaneParam& agent_snapshot_in_lane_param,
    std::optional<double> average_accel, std::optional<double> average_jerk,
    double last_stationary_timestamp, double init_timestamp,
    bool has_agent_lost_patience, std::string& debug_str) {
  // If agent loses patience, we directly use its average accel to construct a
  // linear motion to meet the situation that the agent becomes aggressive.
  if (has_agent_lost_patience) {
    std::optional<double> accel_for_losing_patience_agent =
        GetAccelForLosingPatienceAgent(
            agent_type, static_cast<int64_t>(last_stationary_timestamp),
            static_cast<int64_t>(init_timestamp), average_accel);
    debug_str += "create lost patience traj\n";
    return CreateTrajByRecentAverageAccel(accel_for_losing_patience_agent,
                                          init_timestamp,
                                          agent_snapshot_in_lane_param);
  }
  // Construct start to move traj by analyzing recent jerk/accel if no
  // start-to-move signal from prediction can refer to.
  if (start_to_move_trajectory == nullptr) {
    debug_str +=
        "no stm traj from prediction, creating stm traj by planner...\n";
    // Only assign start to move traj for vehicles.
    if (agent_type != voy::perception::ObjectType::VEHICLE) {
      debug_str += "not vehicle\n";
      return {};
    }
    if (last_stationary_timestamp + math::Sec2Ms(kCreepingCheckTimeThreshold) <
        init_timestamp) {
      debug_str += "not stationary or creeping\n";
      return {};
    }
    constexpr double kMaxHeadingDiffToAssignStartToMove =
        math::Degree2Radian(30.0);
    // We don't want to add start to move traj for agents that is almost
    // crossing us.
    if (std::abs(agent_snapshot_in_lane_param.relative_heading_rad) >
        kMaxHeadingDiffToAssignStartToMove) {
      debug_str += "high heading misalignment\n";
      return {};
    }
    // Assign start to move traj by recent jerk.
    std::vector<pb::TrajectoryPose> traj_poses_by_recent_average_jerk =
        CreateTrajByRecentAverageJerk(average_jerk, init_timestamp,
                                      agent_snapshot_in_lane_param);
    // Assign start to move traj by recent accel.
    std::vector<pb::TrajectoryPose> traj_poses_by_recent_average_accel =
        CreateTrajByRecentAverageAccel(average_accel, init_timestamp,
                                       agent_snapshot_in_lane_param);
    if (traj_poses_by_recent_average_accel.empty() &&
        traj_poses_by_recent_average_jerk.empty()) {
      debug_str += "no recent average accel/jerk can refer to\n";
      return {};
    }
    if (traj_poses_by_recent_average_accel.empty()) {
      return traj_poses_by_recent_average_jerk;
    }
    if (traj_poses_by_recent_average_jerk.empty()) {
      return traj_poses_by_recent_average_accel;
    }
    // Return the longer traj.
    return traj_poses_by_recent_average_jerk.back().odom() <
                   traj_poses_by_recent_average_accel.back().odom()
               ? traj_poses_by_recent_average_accel
               : traj_poses_by_recent_average_jerk;
  }
  // Construct start to move traj by prediction output.
  if (start_to_move_trajectory->traj_poses().empty()) {
    return {};
  }
  DCHECK_EQ(stationary_intention.stationary_intention_type(),
            prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE);
  // Shift start-to-move traj by considering 1 standard deviation time to move.
  prediction::pb::PredictedTrajectory shifted_start_to_move_trajectory;
  const double time_ratio =
      std::abs(stationary_intention.time_to_move()) < math::constants::kEpsilon
          ? 1.0
          : std::max(
                kMaxTimeShrinkRatio,
                (stationary_intention.time_to_move() -
                 stationary_intention.standard_deviation_of_time_to_move()) /
                    stationary_intention.time_to_move());
  const int64_t first_timestamp =
      start_to_move_trajectory->traj_poses(0).timestamp();
  const int64_t last_timestamp =
      start_to_move_trajectory->traj_poses().rbegin()->timestamp();
  for (const auto& pose : start_to_move_trajectory->traj_poses()) {
    auto* shifted_pose = shifted_start_to_move_trajectory.add_traj_poses();
    *shifted_pose = pose;
    shifted_pose->set_timestamp(
        first_timestamp +
        math::Sec2Ms(math::Ms2Sec(pose.timestamp() - first_timestamp) *
                     time_ratio));
  }
  // Append poses with const speed and linear motion to fullfill its duration.
  planner::pb::TrajectoryPose last_pose_after_shifted =
      *shifted_start_to_move_trajectory.traj_poses().rbegin();
  const int64 have_not_durated_timestamp =
      last_timestamp - last_pose_after_shifted.timestamp();
  last_pose_after_shifted.set_accel(0.0);
  const std::vector<pb::TrajectoryPose> tail_poses =
      physics::CreateTrajectoryInConstSpeed(
          last_pose_after_shifted.speed(), last_pose_after_shifted.timestamp(),
          last_pose_after_shifted.heading(), last_pose_after_shifted.x_pos(),
          last_pose_after_shifted.y_pos(), last_pose_after_shifted.odom(),
          have_not_durated_timestamp,
          last_pose_after_shifted.odom() + kDistanceUpperBound, kSamplingTime);
  for (size_t i = 1; i < tail_poses.size(); ++i) {
    auto* add_pose = shifted_start_to_move_trajectory.add_traj_poses();
    *add_pose = tail_poses[i];
  }

  const int64_t conservative_check_timestamp = static_cast<int64_t>(
      init_timestamp + kMaxTimeStampToConsiderStartToMoveTraj);
  const auto lower_it = std::lower_bound(
      shifted_start_to_move_trajectory.traj_poses().begin(),
      shifted_start_to_move_trajectory.traj_poses().end(),
      conservative_check_timestamp,
      [](const planner::pb::TrajectoryPose& traj_pose, int64_t timestamp) {
        return traj_pose.timestamp() < timestamp;
      });
  const double min_odom_to_use_prediction =
      0.5 * kCreepingAcc *
      math::Sqr(math::Ms2Sec(kMaxTimeStampToConsiderStartToMoveTraj));
  bool is_prediction_too_conservative =
      lower_it != shifted_start_to_move_trajectory.traj_poses().end() &&
      lower_it->odom() < min_odom_to_use_prediction;
  if (is_prediction_too_conservative) {
    return CreateSlowAccTrajPoses(
        shifted_start_to_move_trajectory.traj_poses(0));
  }
  return {shifted_start_to_move_trajectory.traj_poses().begin(),
          shifted_start_to_move_trajectory.traj_poses().end()};
}

std::vector<pb::TrajectoryPose> GetStartToMoveTrajectoryPoses(
    const prediction::pb::PredictedTrajectory* start_to_move_trajectory,
    const prediction::pb::StationaryIntention& stationary_intention,
    voy::perception::ObjectType agent_type,
    const ObjectOccupancyParam& object_occupancy_param,
    const TrafficParticipantPose& object_pose,
    const std::optional<double>& average_accel,
    const std::optional<double>& average_jerk, double last_stationary_timestamp,
    double init_timestamp, bool has_agent_lost_patience,
    std::string& debug_str) {
  // If agent loses patience, we directly use its average accel to construct a
  // linear motion to meet the situation that the agent becomes aggressive.
  if (has_agent_lost_patience) {
    std::optional<double> accel_for_losing_patience_agent =
        GetAccelForLosingPatienceAgent(
            agent_type, static_cast<int64_t>(last_stationary_timestamp),
            static_cast<int64_t>(init_timestamp), average_accel);
    debug_str += "create lost patience traj\n";
    return CreateTrajByRecentAverageAccel(accel_for_losing_patience_agent,
                                          init_timestamp,
                                          object_occupancy_param, object_pose);
  }
  // Construct start to move traj by analyzing recent jerk/accel if no
  // start-to-move signal from prediction can refer to.
  if (start_to_move_trajectory == nullptr) {
    debug_str +=
        "no stm traj from prediction, creating stm traj by planner...\n";
    // Only assign start to move traj for vehicles.
    if (agent_type != voy::perception::ObjectType::VEHICLE) {
      debug_str += "not vehicle\n";
      return {};
    }
    if (last_stationary_timestamp + math::Sec2Ms(kCreepingCheckTimeThreshold) <
        init_timestamp) {
      debug_str += "not stationary or creeping\n";
      return {};
    }
    constexpr double kMaxHeadingDiffToAssignStartToMove =
        math::Degree2Radian(30.0);
    // We don't want to add start to move traj for agents that is almost
    // crossing us.
    if (std::abs(object_occupancy_param.relative_heading_rad) >
        kMaxHeadingDiffToAssignStartToMove) {
      debug_str += "high heading misalignment\n";
      return {};
    }
    // Assign start to move traj by recent jerk.
    std::vector<pb::TrajectoryPose> traj_poses_by_recent_average_jerk =
        CreateTrajByRecentAverageJerk(average_jerk, init_timestamp,
                                      object_occupancy_param, object_pose);
    // Assign start to move traj by recent accel.
    std::vector<pb::TrajectoryPose> traj_poses_by_recent_average_accel =
        CreateTrajByRecentAverageAccel(average_accel, init_timestamp,
                                       object_occupancy_param, object_pose);
    if (traj_poses_by_recent_average_accel.empty() &&
        traj_poses_by_recent_average_jerk.empty()) {
      debug_str += "no recent average accel/jerk can refer to\n";
      return {};
    }
    if (traj_poses_by_recent_average_accel.empty()) {
      return traj_poses_by_recent_average_jerk;
    }
    if (traj_poses_by_recent_average_jerk.empty()) {
      return traj_poses_by_recent_average_accel;
    }
    // Return the longer traj.
    return traj_poses_by_recent_average_jerk.back().odom() <
                   traj_poses_by_recent_average_accel.back().odom()
               ? traj_poses_by_recent_average_accel
               : traj_poses_by_recent_average_jerk;
  }
  // Construct start to move traj by prediction output.
  if (start_to_move_trajectory->traj_poses().empty()) {
    return {};
  }
  // Shift start-to-move traj by considering 1 standard deviation time to move.
  prediction::pb::PredictedTrajectory shifted_start_to_move_trajectory;
  // If start to move trajectory is not empty, the stationary intention type
  // should be STATIONARY_TO_MOVE.
  DCHECK_EQ(stationary_intention.stationary_intention_type(),
            prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE);
  const double time_ratio =
      std::abs(stationary_intention.time_to_move()) < math::constants::kEpsilon
          ? 1.0
          : std::max(
                kMaxTimeShrinkRatio,
                (stationary_intention.time_to_move() -
                 stationary_intention.standard_deviation_of_time_to_move()) /
                    stationary_intention.time_to_move());
  const int64_t first_timestamp =
      start_to_move_trajectory->traj_poses(0).timestamp();
  const int64_t last_timestamp =
      start_to_move_trajectory->traj_poses().rbegin()->timestamp();
  for (const auto& pose : start_to_move_trajectory->traj_poses()) {
    auto* shifted_pose = shifted_start_to_move_trajectory.add_traj_poses();
    *shifted_pose = pose;
    shifted_pose->set_timestamp(
        first_timestamp +
        math::Sec2Ms(math::Ms2Sec(pose.timestamp() - first_timestamp) *
                     time_ratio));
  }
  // Append poses with const speed and linear motion to fullfill its duration.
  planner::pb::TrajectoryPose last_pose_after_shifted =
      *shifted_start_to_move_trajectory.traj_poses().rbegin();
  const int64 have_not_durated_timestamp =
      last_timestamp - last_pose_after_shifted.timestamp();
  last_pose_after_shifted.set_accel(0.0);
  const std::vector<pb::TrajectoryPose> tail_poses =
      physics::CreateTrajectoryInConstSpeed(
          last_pose_after_shifted.speed(), last_pose_after_shifted.timestamp(),
          last_pose_after_shifted.heading(), last_pose_after_shifted.x_pos(),
          last_pose_after_shifted.y_pos(), last_pose_after_shifted.odom(),
          have_not_durated_timestamp,
          last_pose_after_shifted.odom() + kDistanceUpperBound, kSamplingTime);
  for (size_t i = 1; i < tail_poses.size(); ++i) {
    auto* add_pose = shifted_start_to_move_trajectory.add_traj_poses();
    *add_pose = tail_poses[i];
  }

  const int64_t conservative_check_timestamp = static_cast<int64_t>(
      init_timestamp + kMaxTimeStampToConsiderStartToMoveTraj);
  const auto lower_it = std::lower_bound(
      shifted_start_to_move_trajectory.traj_poses().begin(),
      shifted_start_to_move_trajectory.traj_poses().end(),
      conservative_check_timestamp,
      [](const planner::pb::TrajectoryPose& traj_pose, int64_t timestamp) {
        return traj_pose.timestamp() < timestamp;
      });
  const double min_odom_to_use_prediction =
      0.5 * kCreepingAcc *
      math::Sqr(math::Ms2Sec(kMaxTimeStampToConsiderStartToMoveTraj));
  bool is_prediction_too_conservative =
      lower_it != shifted_start_to_move_trajectory.traj_poses().end() &&
      lower_it->odom() < min_odom_to_use_prediction;
  if (is_prediction_too_conservative) {
    return CreateSlowAccTrajPoses(
        shifted_start_to_move_trajectory.traj_poses(0));
  }
  return {shifted_start_to_move_trajectory.traj_poses().begin(),
          shifted_start_to_move_trajectory.traj_poses().end()};
}

// Adds left side repulsion in Frenet coordinate for stationary agent with
// additional longitudinal buffer.
//
//
//      ----------------------------------------------  Left Lane Marking
//    -----
//   | ego | ->
//    -----
//    -----           --------------  -------------       <- left repulsion
//                    ｜   -----   |
//     ---------------｜  | veh |  |----------------- Right Lane Marking
//                    ｜   -----   |  <- Nudge constraint
std::unique_ptr<RepulsionMetaData> MaybeAddLongitudinalBuffer(
    const pb::TrajectoryReasoningInfo& reasoning_info, int64_t object_id,
    const double agent_end_arclength_m,
    const double agent_end_lateral_distance_m,
    const double repulsion_required_lat_gap) {
  if (!reasoning_info.is_stationary() ||
      !reasoning_info.is_vehicle_current_heading_along_nominal_path() ||
      reasoning_info.oriented_side_to_ego_centerline() !=
          math::pb::Side::kRight) {
    // If agent is not stationary, heading is not roughly along nominal path
    // (either same direction or opposite direction), or agent is not on the
    // right side of ego, return early as we will not add left side repulsion in
    // this case.
    return nullptr;
  }

  std::vector<FrenetPoint> points;
  points.reserve(2);
  points.emplace_back(agent_end_arclength_m, agent_end_lateral_distance_m +
                                                 repulsion_required_lat_gap);
  points.emplace_back(
      agent_end_arclength_m + kLongitudinalRepulsionBufferInMeter,
      agent_end_lateral_distance_m + repulsion_required_lat_gap);

  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kLeft, std::move(points),
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/true,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      pb::AgentReasonerId::STATIC_AGENT,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/std::nullopt);
}

// Add repulsion for the target lane agent during crawl.We create a bounding box
// based on the polygon and the box heading is always along with EGO. The
// repulsions is formulated by a lateral offset line starting from a the front
// corner to a fix distance along box heading. Expand reasoning boundary is
// allowed here.
std::unique_ptr<RepulsionMetaData> AddLaneChangeCrawlTargetLaneAgentRepusion(
    const pb::TrajectoryReasoningInfo& reasoning_info, const int64_t object_id,
    const math::geometry::Polygon2d& agent_contour, const double agent_heading,
    const double ego_heading, const double ego_width,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const double repulsion_required_lat_gap, std::string& debug_str) {
  if (!lane_change_execution_info.has_value()) {
    debug_str += " no lane change execution info ";
    return nullptr;
  }
  const planner::pb::LaneChangeMode lane_change_direction =
      lane_change_execution_info->lane_change_direction;

  if (!(lane_change_direction ==
            planner::pb::LaneChangeMode::LEFT_LANE_CHANGE &&
        reasoning_info.oriented_side_to_ego_centerline() ==
            math::pb::Side::kLeft) &&
      !(lane_change_direction ==
            planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE &&
        reasoning_info.oriented_side_to_ego_centerline() ==
            math::pb::Side::kRight)) {
    debug_str += " crawl direction is not the same as agent path side ";

    return nullptr;
  }
  const double heading_diff =
      std::abs(math::AngleDiff(agent_heading, ego_heading));
  // If the agent is static ,the agent's heading is unstable, it may along with
  // the box heading or opposite to the box heading. During lane change, the
  // agent's heading diff with agent's box heading always < M_PI_2, if not, the
  // box heading may be opposite to the agent's heading.
  const double box_heading =
      heading_diff < M_PI_2 ? agent_heading : agent_heading + M_PI;
  const double lane_change_sign =
      lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? -1.0
          : 1.0;
  const math::geometry::OrientedBox2d agent_box(agent_contour, box_heading);
  math::geometry::Point2d polyline_start =
      lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? agent_box.FrontRightPoint()
          : agent_box.FrontLeftPoint();
  math::geometry::Point2d polyline_end = polyline_start;
  // The repulsions is formulated by a lateral offset line starting from a the
  // front corner to a fix distance along box heading.
  const math::geometry::Point2d lon_offset_vec = math::geometry::Multiply(
      agent_box.length_unit(), kLongitudinalRepulsionBufferInMeter);
  const math::geometry::Point2d lat_offset_vec = math::geometry::Multiply(
      agent_box.width_unit(), repulsion_required_lat_gap * lane_change_sign);
  math::geometry::AssignmentAdd(polyline_start, lat_offset_vec);
  math::geometry::AssignmentAdd(polyline_end, lat_offset_vec);
  math::geometry::AssignmentAdd(polyline_end, lon_offset_vec);
  const bool is_repulsion_direction_left =
      lane_change_direction == planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE &&
      reasoning_info.oriented_side_to_ego_centerline() ==
          math::pb::Side::kRight;
  return std::make_unique<RepulsionMetaData>(
      is_repulsion_direction_left
          ? RepulsionMetaData::RepulsionDirection::kLeft
          : RepulsionMetaData::RepulsionDirection::kRight,
      math::geometry::PolylineCurve2d({polyline_start, polyline_end}),
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      pb::AgentReasonerId::STATIC_AGENT,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/std::nullopt,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in = */ std::nullopt,
      /*extend_boundary_level_in= */
      RepulsionMetaData::ExtendBoundaryLevel::kVirtualHard,
      /*clearance_in= */ std::make_optional(ego_width),
      /*max_extend_boundary_distance_in= */ std::make_optional(ego_width),
      /*lateral_bound_in=*/std::nullopt,
      /*post_handler_in= */ []() {});
}

// Adds bi-directional repulsion to make the ego's path solution within the two
// repulsion polylines.
// params:
// @@start_s: starting longitudinal position of the repulsion center
// @@end_s: ending longitudinal position of the repulsion center
// @@left/right lateral_offset: respective lateral offset from the repulsion
// center.
// --------------------------------------------------------------------------
//
//           _______________left_repulsion_____________________
//                |
//                | left_lateral_offset
//                |
//   start_s______|__________repulsion_center__________________end_s
//                |
//                | right_lateral_offset
//                |
//         _______|_________right_repulsion___________________
//---------------------------------------------------------------------------
void AddBiDirectionalRepulsions(
    const double start_s, const double end_s, const double left_lateral_offset,
    const double right_lateral_offset,
    const path::BoundaryStrength repulsion_strength,
    const pb::ObjectSourceType::Enum source_type, const int64_t object_id,
    std::vector<RepulsionMetaData>& repulsion_meta_data_vec) {
  std::vector<FrenetPoint> left_repulsion_points, right_repulsion_points;
  left_repulsion_points.reserve(2);
  right_repulsion_points.reserve(2);
  left_repulsion_points.emplace_back(start_s, right_lateral_offset);
  left_repulsion_points.emplace_back(end_s, right_lateral_offset);
  right_repulsion_points.emplace_back(start_s, left_lateral_offset);
  right_repulsion_points.emplace_back(end_s, left_lateral_offset);
  const bool add_only_not_overtaking =
      FLAGS_planning_provide_multiple_fod_intentions_for_better_not_drive_object;
  // repulsion_required_lateral_gap is set to 0 to align in the corridor.
  // TODO(Ziyue): Might want to extend lane marking boundaries.
  repulsion_meta_data_vec.emplace_back(
      RepulsionMetaData::RepulsionDirection::kLeft,
      std::move(left_repulsion_points),
      /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/add_only_not_overtaking,
      /*ignore_if_nudge_yield_in=*/false, repulsion_strength,
      /*strength_ratio_in=*/std::nullopt, /*repulsion_required_lat_gap=*/0,
      pb::AgentReasonerId::STATIC_AGENT,
      ToProto(TypedObjectId(object_id, source_type)),
      /*trajectory_id_in=*/std::nullopt,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt);
  repulsion_meta_data_vec.emplace_back(
      RepulsionMetaData::RepulsionDirection::kRight,
      std::move(right_repulsion_points),
      /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/add_only_not_overtaking,
      /*ignore_if_nudge_yield_in=*/false, repulsion_strength,
      /*strength_ratio_in=*/std::nullopt, /*repulsion_required_lat_gap=*/0,
      pb::AgentReasonerId::STATIC_AGENT,
      ToProto(TypedObjectId(object_id, source_type)),
      /*trajectory_id_in=*/std::nullopt,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt);
}

// Gets the width and length of the FOD given the object contour.
// TODO(Ziyue): Move this function to an util file
template <typename ObjectPose>
inline double CalculateObjectWidthByContour(const ObjectPose& pose) {
  math::geometry::OrientedBox<math::geometry::Point2d> object_bbox(
      pose.contour().polygon(), pose.heading());

  return object_bbox.width();
}

// If the object is drivable, try to find a decelerate and drive through
// solution. The return value of this function is the discomfort level index. A
// higher value means more discomfort and dangerous to pass.
std::optional<int> GetDriveThroughSolutionDiscomfortIndex(
    const EgoInLaneParams& ego_params, bool is_drivable_fod,
    const double distance_to_ego_m) {
  if (is_drivable_fod) {
    // Calculate the required deceleration to reduce to the speed that is
    // considered safe to pass the object. Assume to use a constant
    // deceleration.
    double deceleration = -(std::pow(kHighSpeedThresholdInMps, 2) -
                            std::pow(ego_params.speed_mps, 2)) /
                          (2 * distance_to_ego_m);
    return std::lower_bound(max_allowed_longitudinal_decels.begin(),
                            max_allowed_longitudinal_decels.end(),
                            deceleration) -
           max_allowed_longitudinal_decels.begin();
  }
  return std::nullopt;
}

inline bool CanEgoYieldAtCurrentSpeed(const EgoInLaneParams& ego_params,
                                      const double distance_to_ego_m) {
  double deceleration =
      std::pow(ego_params.speed_mps, 2) / (2 * distance_to_ego_m);
  return distance_to_ego_m > 0 &&
         deceleration < max_allowed_longitudinal_decels.back();
}

// Generate a nudge intention for FOD if a comfortable nudge path can be found.
template <typename ObjectOccupancyParam>
int GetNudgeSolutionDiscomfortIndex(
    const path::NudgeMotionChecker& nudge_motion_checker,
    const ObjectOccupancyParam& object_occupancy_param,
    const RequiredLateralGap& required_lateral_gaps,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info) {
  // TODO(Ziyue): Define these values based on the motion data.
  for (size_t level = 0; level < kNumDiscomfortLevels; ++level) {
    double max_lateral_jerk = max_allowed_lateral_jerks[level];
    double max_lateral_accel = max_allowed_lateral_accels[level];
    // Whether can nudge from left.
    bool can_nudge_from_left =
        traffic_rule_reasoning_info.can_ego_drive_on_left_lane() &&
        nudge_motion_checker.IsObjectNudgable(
            object_occupancy_param, required_lateral_gaps, max_lateral_jerk,
            max_lateral_accel, /*is_nudge_from_left=*/true,
            /*speed_profile_idx=*/0);
    // Whether can nudge from right.
    bool can_nudge_from_right =
        traffic_rule_reasoning_info.can_ego_drive_on_right_lane() &&
        nudge_motion_checker.IsObjectNudgable(
            object_occupancy_param, required_lateral_gaps, max_lateral_jerk,
            max_lateral_accel, /*is_nudge_from_left=*/false,
            /*speed_profile_idx=*/0);
    // If cannot nudge from either side, should ignore it.
    if (can_nudge_from_left || can_nudge_from_right) {
      return level;
    }
  }
  return kNumDiscomfortLevels;
}

// Gets minimum lateral motion to either nudge or undercarriage over an object.
// The value should be both non-negative. The safe drivable region is outside of
// the targeted_center_line_dist_left/right because we want to nudge the object.
// --------------------------------------------------------------------------
//              Drivable Region
//    - - - - - - - - - - - - - - - - - -| - - - - - - - - -
//   _______________left_bbox____________|
//                                       | targeted_center_line_dist_left
//                                       |    (signed)
//   ________________lane_center_line___ |____________
//                                       |
//                                       |
//   _______________right_bbox___________| targeted_center_line_dist_right
//                                       |    (signed)
//    - - - - - - - - - - - - - - - - - -| - - - - - - - - -
//              Drivable Region
//---------------------------------------------------------------------------
inline std::pair<double, double> GetLateralMotionPairForNudge(
    const double ego_center_line_dist,
    const double targeted_center_line_dist_left,
    const double targeted_center_line_dist_right) {
  const double left_min_lateral_motion =
      targeted_center_line_dist_left - ego_center_line_dist;
  const double right_min_lateral_motion =
      ego_center_line_dist - targeted_center_line_dist_right;
  return std::make_pair(std::max(0.0, left_min_lateral_motion),
                        std::max(0.0, right_min_lateral_motion));
}

// Gets minimum lateral motion to undercarriage over an object. The safe
// drivable region is in between the targeted_center_line_dist_left/right
// because we want to undercarriage over the object..
// --------------------------------------------------------------------------
//    - - - - - - - - - - - - - - - - - -| - - - - - - - - - - - - -
//   _______________left_bbox____________|
//                                       | targeted_center_line_dist_left
//                                       |    (signed)
//   ________________lane_center_line___ |_
//                                       |    Drivable Region
//                                       |
//   _______________right_bbox___________| targeted_center_line_dist_right
//                                       |    (signed)
//    - - - - - - - - - - - - - - - - - -| - - - - - - - - - - - - -
//---------------------------------------------------------------------------
inline double GetLateralMotionForUndercarriageOver(
    const double ego_center_line_dist,
    const double targeted_center_line_dist_left,
    const double targeted_center_line_dist_right) {
  DCHECK_LE(targeted_center_line_dist_right, targeted_center_line_dist_left);
  if (ego_center_line_dist >= targeted_center_line_dist_right &&
      ego_center_line_dist <= targeted_center_line_dist_left) {
    return 0.0;
  }
  if (ego_center_line_dist < targeted_center_line_dist_right) {
    return targeted_center_line_dist_right - ego_center_line_dist;
  }
  return targeted_center_line_dist_left - ego_center_line_dist;
}

// Whether we should consider nudging a FoD.
// TODO(Ziyue): This is a short-term function. The long-term one is
// GetNudgeSolutionDiscomfortIndex() When the nudge checker is ready to use,
// this function will be deprecated.
inline bool IsFoDSafeToNudge(
    const std::pair<double, double>& min_lateral_accels_to_nudge,
    const bool can_ego_drive_on_left_lane,
    const bool can_ego_drive_on_right_lane, const bool can_in_lane_nudge,
    const bool is_xlane_nudge) {
  bool can_nudge_left =
      min_lateral_accels_to_nudge.first <=
          kSafeNudgeLateralAccelThresholdInMpss &&
      (can_ego_drive_on_left_lane || can_in_lane_nudge || is_xlane_nudge);
  bool can_nudge_right =
      min_lateral_accels_to_nudge.second <=
          kSafeNudgeLateralAccelThresholdInMpss &&
      (can_ego_drive_on_right_lane || can_in_lane_nudge || is_xlane_nudge);
  return can_nudge_left || can_nudge_right;
}

// Determines whether the path reasoner should undercarriage over the FOD.
// We compare the lateral acceleration of nudging or undercarriaging over the
// object, and choose the behavior whichever has a smaller lateral acceleration.
// Note min_lateral_acceleration can be as big as
// std::numeric_limits<double>::max() for both nudge and undercarriage over. In
// this case, the function output is false, and we should neither nudge nor
// undercarriage over it, the desired behavior is ignore yield.
bool ShouldUndercarriageOverFOD(
    const EgoInLaneParams& ego_params,
    const NudgeMotionChecker& nudge_motion_checker,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const std::pair<double, double>& min_lateral_accels_to_nudge,
    const double desired_s, const double object_width,
    const double object_center_line_dist, const double lane_width) {
  DCHECK_LE(object_width, kUndercarriageOverWidthThresholdInM);
  // This value is based on the traffic_rule_reasoning_info setting and the
  // object's width. Testing on this value now. This value need to be revisited
  // because this config can be vulnerable.
  static constexpr double kMinRequiredDistToHardBoundary = 1.5;
  auto is_too_close_to_hard_boundary =
      [](const double dist_to_lane_boundary,
         const double lane_marking_to_hard_boundary_dist) {
        return lane_marking_to_hard_boundary_dist + dist_to_lane_boundary <
               kMinRequiredDistToHardBoundary;
      };
  // If the object is too close to the hard boundary, undercarriaging it will
  // become dangerous and at the same time, the attraction won't be obvious
  // because the hard boundary has a much higher penalty weight.
  if (is_too_close_to_hard_boundary(
          lane_width / 2 - (object_center_line_dist + object_width / 2),
          traffic_rule_reasoning_info
              .min_lane_marking_distance_to_left_hard_boundary()) ||
      is_too_close_to_hard_boundary(
          lane_width / 2 + (object_center_line_dist - object_width / 2),
          traffic_rule_reasoning_info
              .min_lane_marking_distance_to_right_hard_boundary())) {
    return false;
  }
  // The targeted_center_line_dist calculation are based on the method in this
  // doc
  // https://docs.google.com/document/d/16bA70SAo2RN-B2rx1BPSDkJAYL1F0dppyWzbA9vWzV8/edit?tab=t.0
  // The targeted_center_line_dist_left/right is the targeted signed distance
  // (positive for left and negative for right) of the ego's center to the lane
  // center line. The targeted position is where the gap between the ego's bbox
  // and the object's bbox is greater than the threshold, meaning the ego's
  // wheel is within a safety distance to the object.
  const double targeted_center_line_dist_left =
      object_center_line_dist + 0.5 * ego_params.width_m - 0.5 * object_width -
      kExtraLateralBufferForUndercarriageOverFodInM;
  const double targeted_center_line_dist_right =
      object_center_line_dist - (0.5 * ego_params.width_m - 0.5 * object_width -
                                 kExtraLateralBufferForUndercarriageOverFodInM);
  const double ego_center_line_dist =
      ego_params.relative_lateral_distance_to_line_center;
  const double lateral_motion_for_undercarriage_over =
      GetLateralMotionForUndercarriageOver(ego_center_line_dist,
                                           targeted_center_line_dist_left,
                                           targeted_center_line_dist_right);
  const double min_lateral_accels_to_undercarriage_over =
      nudge_motion_checker
          .CalculateDesiredLateralAccelerationWithinKinematicLimit(
              desired_s,
              ego_center_line_dist + lateral_motion_for_undercarriage_over,
              /*speed_at_s=*/std::nullopt,
              /*enable_steering_rate_limit=*/true);

  // If the lateral acceleration of undercarriage over is less than nudge,
  // or if the lateral motion of undercarriage over is close to 0, we prefer to
  // undercarriage over.
  return std::abs(lateral_motion_for_undercarriage_over) <=
             math::constants::kEpsilon ||
         std::min(min_lateral_accels_to_nudge.first,
                  min_lateral_accels_to_nudge.second) -
                 min_lateral_accels_to_undercarriage_over >
             0;
}

// Decides whether to nudge, decelerate pass or completely ignore the object
// based on the dimension of the object and the comfort/safety of those
// behaviors. Note in this function, we only set intention type to IGNORE if the
// preferred behavior is decelerate pass or completely ignore. We will not set
// the intention type to MIXED when the preferred behavior is nudge because the
// default value is MIXED.
void GenerateNudgeOrDriveThroughBehaviorIfPossible(
    const AgentInLaneStates& agent_inlane_state,
    const EgoInLaneParams& ego_params,
    const path::NudgeMotionChecker& nudge_motion_checker,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    bool is_object_drivable_fod, bool is_better_not_drive_fod,
    double distance_to_ego_m,
    const pb::TrajectoryState::TrajectoryBlockingState& blocking_state,
    const ScenarioIdentifierResult& scenario_identify_result_,
    TrajectoryIntentionMetaData& trajectory_intention_data) {
  if (is_object_drivable_fod) {
    rt_event::PostRtEvent<rt_event::planner::HandleDrivableFoDInPlanner>();
  }
  const auto& agent_in_lane_param =
      agent_inlane_state.tracked_state.inlane_param;
  const auto& object_pose = agent_in_lane_param.pose;
  const double object_width = CalculateObjectWidthByContour(object_pose);
  const double object_height = object_pose.height();
  const double object_center_line_dist =
      (agent_in_lane_param.dist_to_center_line_range.end_pos +
       agent_in_lane_param.dist_to_center_line_range.start_pos) /
      2;
  const double targeted_center_line_dist_left =
      object_center_line_dist + 0.5 * object_width + 0.5 * ego_params.width_m +
      kRequiredLateralGapForNudgingFodInM;
  const double targeted_center_line_dist_right =
      object_center_line_dist - (0.5 * object_width + 0.5 * ego_params.width_m +
                                 kRequiredLateralGapForNudgingFodInM);
  const double ego_center_line_dist =
      ego_params.relative_lateral_distance_to_line_center;
  const auto [left_nudge_min_lateral_motion, right_nudge_min_lateral_motion] =
      GetLateralMotionPairForNudge(ego_center_line_dist,
                                   targeted_center_line_dist_left,
                                   targeted_center_line_dist_right);
  const std::pair<double, double> min_lateral_accels_to_nudge = std::make_pair(
      nudge_motion_checker
          .CalculateDesiredLateralAccelerationWithinKinematicLimit(
              agent_in_lane_param.full_body_start_arclength_m,
              ego_center_line_dist + left_nudge_min_lateral_motion,
              /*speed_at_s=*/std::nullopt,
              /*enable_steering_rate_limit=*/true),
      nudge_motion_checker
          .CalculateDesiredLateralAccelerationWithinKinematicLimit(
              agent_in_lane_param.full_body_start_arclength_m,
              ego_center_line_dist - right_nudge_min_lateral_motion,
              /*speed_at_s=*/std::nullopt,
              /*enable_steering_rate_limit=*/true));
  bool is_fod_safe_to_nudge = IsFoDSafeToNudge(
      min_lateral_accels_to_nudge,
      traffic_rule_reasoning_info.can_ego_drive_on_left_lane(),
      traffic_rule_reasoning_info.can_ego_drive_on_right_lane(),
      blocking_state != pb::TrajectoryState::HARD_BLOCKING,
      scenario_identify_result_.scenario_type ==
          pb::ScenarioRecognition::XLANE_NUDGE);
  if (is_better_not_drive_fod &&
      FLAGS_planning_enable_undercarriaging_over_fod) {
    // Provide both undercarriage_over and nudge options.
    if (FLAGS_planning_provide_multiple_fod_intentions_for_better_not_drive_object) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::UNDERCARRIAGE_OVER);
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      return;
    }
    if (object_height <= kDrivableHeightThresholdForFODInM &&
        object_width <= kUndercarriageOverWidthThresholdInM &&
        ShouldUndercarriageOverFOD(
            ego_params, nudge_motion_checker, traffic_rule_reasoning_info,
            min_lateral_accels_to_nudge,
            agent_in_lane_param.full_body_start_arclength_m, object_width,
            object_center_line_dist, agent_in_lane_param.min_lane_width)) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::UNDERCARRIAGE_OVER);
      rt_event::PostRtEvent<
          rt_event::planner::TriggerUndercarriageOverFoDLogic>();
      return;
    }
    // If the calculated nudge acceleration is within safety level, then prefer
    // nudge. Otherwise, prefer ignore and yield.
    if (std::min(min_lateral_accels_to_nudge.first,
                 min_lateral_accels_to_nudge.second) <
        std::numeric_limits<double>::max()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
      rt_event::PostRtEvent<rt_event::planner::NudgeBetterNotDriveFoD>();
      return;
    }
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    trajectory_intention_data.fod_intentions.insert(pb::IntentionResult::YIELD);
    return;
  }
  if (object_width < kDrivableWidthThresholdForFODInM) {
    // Small FODs. Prefer passing through to nudging. If the object is drivable,
    // we will not try to find a nudge solution.
    if (is_object_drivable_fod) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::DRIVE_THROUGH);
      rt_event::PostRtEvent<rt_event::planner::TriggerDriveThroughFoDLogic>();
      return;
    }
    // TODO(Ziyue): Currently use a hard-coded threshold value to check
    // nudgable. Use nudge checker when it's ready for use.
    if (is_fod_safe_to_nudge ||
        !CanEgoYieldAtCurrentSpeed(ego_params, distance_to_ego_m)) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
    } else {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::YIELD);
    }
  } else {
    // This is considered as a large FOD. It is wide but not too high to pass
    // through. For these objects, we prefer nudging to passing through.
    // Comparing the ego speed with a safe to nudge speed, if the ego speed is
    // safe to nudge, we will generate a nudge intention. Otherwise, we will
    // generate a pass or yield intention.
    if (is_fod_safe_to_nudge ||
        !CanEgoYieldAtCurrentSpeed(ego_params, distance_to_ego_m)) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
    } else if (GetDriveThroughSolutionDiscomfortIndex(
                   ego_params, is_object_drivable_fod, distance_to_ego_m)
                   .has_value()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::DRIVE_THROUGH);
      rt_event::PostRtEvent<rt_event::planner::TriggerDriveThroughFoDLogic>();
    } else {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::YIELD);
    }
  }
}

void GenerateNudgeOrDriveThroughBehaviorIfPossible(
    const ObjectOccupancyState& object_occupancy_state,
    const EgoInLaneParams& ego_params,
    const path::NudgeMotionChecker& nudge_motion_checker,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    bool is_object_drivable_fod, bool is_better_not_drive_fod,
    double distance_to_ego_m,
    const pb::TrajectoryState::TrajectoryBlockingState& blocking_state,
    const ScenarioIdentifierResult& scenario_identify_result_,
    TrajectoryIntentionMetaData& trajectory_intention_data) {
  const auto& object_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const auto& object_pose = object_occupancy_state.pose();
  const double object_width = CalculateObjectWidthByContour(object_pose);
  const double object_height = object_pose.height();
  const double object_center_line_dist =
      (object_occupancy_param.dist_to_center_line_range.end_pos +
       object_occupancy_param.dist_to_center_line_range.start_pos) /
      2;
  const double targeted_center_line_dist_left =
      object_center_line_dist + 0.5 * object_width + 0.5 * ego_params.width_m +
      kRequiredLateralGapForNudgingFodInM;
  const double targeted_center_line_dist_right =
      object_center_line_dist - (0.5 * object_width + 0.5 * ego_params.width_m +
                                 kRequiredLateralGapForNudgingFodInM);
  const double ego_center_line_dist =
      ego_params.relative_lateral_distance_to_line_center;
  const auto [left_nudge_min_lateral_motion, right_nudge_min_lateral_motion] =
      GetLateralMotionPairForNudge(ego_center_line_dist,
                                   targeted_center_line_dist_left,
                                   targeted_center_line_dist_right);
  const std::pair<double, double> min_lateral_accels_to_nudge = std::make_pair(
      nudge_motion_checker
          .CalculateDesiredLateralAccelerationWithinKinematicLimit(
              object_occupancy_param.full_body_start_arclength_m,
              ego_center_line_dist + left_nudge_min_lateral_motion,
              /*speed_at_s=*/std::nullopt,
              /*enable_steering_rate_limit=*/true),
      nudge_motion_checker
          .CalculateDesiredLateralAccelerationWithinKinematicLimit(
              object_occupancy_param.full_body_start_arclength_m,
              ego_center_line_dist - right_nudge_min_lateral_motion,
              /*speed_at_s=*/std::nullopt,
              /*enable_steering_rate_limit=*/true));
  bool is_fod_safe_to_nudge = IsFoDSafeToNudge(
      min_lateral_accels_to_nudge,
      traffic_rule_reasoning_info.can_ego_drive_on_left_lane(),
      traffic_rule_reasoning_info.can_ego_drive_on_right_lane(),
      blocking_state != pb::TrajectoryState::HARD_BLOCKING,
      scenario_identify_result_.scenario_type ==
          pb::ScenarioRecognition::XLANE_NUDGE);
  if (is_better_not_drive_fod &&
      FLAGS_planning_enable_undercarriaging_over_fod) {
    // Provide both undercarriage_over and nudge options.
    if (FLAGS_planning_provide_multiple_fod_intentions_for_better_not_drive_object) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::UNDERCARRIAGE_OVER);
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      return;
    }
    if (object_height < kDrivableHeightThresholdForFODInM &&
        object_width < kUndercarriageOverWidthThresholdInM &&
        ShouldUndercarriageOverFOD(
            ego_params, nudge_motion_checker, traffic_rule_reasoning_info,
            min_lateral_accels_to_nudge,
            object_occupancy_param.full_body_start_arclength_m, object_width,
            object_center_line_dist,
            object_occupancy_param.min_corridor_width_m)) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::UNDERCARRIAGE_OVER);
      rt_event::PostRtEvent<
          rt_event::planner::TriggerUndercarriageOverFoDLogic>();
      return;
    }
    // If the calculated nudge acceleration is within safety level, then prefer
    // nudge. Otherwise, prefer ignore and yield.
    if (std::min(min_lateral_accels_to_nudge.first,
                 min_lateral_accels_to_nudge.second) <
        std::numeric_limits<double>::max()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
      rt_event::PostRtEvent<rt_event::planner::NudgeBetterNotDriveFoD>();
      return;
    }
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    trajectory_intention_data.fod_intentions.insert(pb::IntentionResult::YIELD);
    return;
  }
  if (object_width < kDrivableWidthThresholdForFODInM) {
    // Small FODs. Prefer passing through to nudging. If the object is drivable,
    // we will not try to find a nudge solution.
    if (is_object_drivable_fod) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::DRIVE_THROUGH);
      rt_event::PostRtEvent<rt_event::planner::TriggerDriveThroughFoDLogic>();
      return;
    }
    // TODO(Ziyue): Currently use a hard-coded threshold value to check
    // nudgable. Use nudge checker when it's ready for use.
    if (is_fod_safe_to_nudge ||
        !CanEgoYieldAtCurrentSpeed(ego_params, distance_to_ego_m)) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
    } else {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::YIELD);
    }
  } else {
    // This is considered as a large FOD. It is wide but not too high to pass
    // through. For these objects, we prefer nudging to passing through.
    // Comparing the ego speed with a safe to nudge speed, if the ego speed is
    // safe to nudge, we will generate a nudge intention. Otherwise, we will
    // generate a pass or yield intention.
    if (is_fod_safe_to_nudge ||
        !CanEgoYieldAtCurrentSpeed(ego_params, distance_to_ego_m)) {
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::NUDGE);
    } else if (GetDriveThroughSolutionDiscomfortIndex(
                   ego_params, is_object_drivable_fod, distance_to_ego_m)
                   .has_value()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::DRIVE_THROUGH);
      rt_event::PostRtEvent<rt_event::planner::TriggerDriveThroughFoDLogic>();
    } else {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      trajectory_intention_data.fod_intentions.insert(
          pb::IntentionResult::YIELD);
    }
  }
}

// Adds repulsion for the stuck region of the xlane nudge if the region polygon
// is valid.
std::unique_ptr<RepulsionMetaData> AddRepulsionForXLaneNudgeRegion(
    const XLaneNudgeScene& xlane_nudge_scene, TypedObjectId object_id,
    RepulsionMetaData::RepulsionDirection repulsion_direction,
    pb::AgentReasonerId reasoner_id, int64_t timestamp) {
  DCHECK(repulsion_direction != RepulsionMetaData::RepulsionDirection::kEither);
  static constexpr double kDefaultPolygonSimplifyThresholdInMeter = 0.5;
  const math::geometry::Polygon2d region_polygon =
      math::geometry::Simplify(xlane_nudge_scene.stuck_region_contour,
                               kDefaultPolygonSimplifyThresholdInMeter,
                               /*assert_no_self_intersection=*/false);
  if (!math::geometry::IsValid(region_polygon)) {
    return nullptr;
  }

  speed::Profile static_speed_profile_bound;
  static_speed_profile_bound.emplace_back(math::Ms2Sec(timestamp), /*x_in=*/0.0,
                                          /*v_in=*/0.0, /*a_in=*/0.0,
                                          /*j_in=*/0.0);
  static_speed_profile_bound.emplace_back(
      math::Ms2Sec(timestamp) + constants::kTrajectoryHorizonInSec,
      /*x_in=*/0.0, /*v_in=*/0.0, /*a_in=*/0.0, /*j_in=*/0.0);
  return std::make_unique<RepulsionMetaData>(
      repulsion_direction, std::move(region_polygon),
      math::geometry::PolylineCurve2d(), static_speed_profile_bound,
      static_speed_profile_bound, /*heading_in=*/0.0,
      /*add_only_overtaking_in=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      kLateralComfortableXLaneNudgeBufferInMeter, reasoner_id,
      ToProto(object_id), /*trajectory_id_in=*/std::nullopt,
      /*path_risk_mitigation_mode_in=*/false);
}

void EvaluateIntetionDuringPullOver(
    const LateralBlockingStateMetaData& blocking_state_data,
    const PullOverStatusMetaData& pull_over_status_meta,
    double agent_arclength_m, bool is_cz,
    TrajectoryIntentionMetaData& trajectory_intention_data) {
  const double pull_over_destination_arclength_m =
      pull_over_status_meta.pullover_reference_path
          .GetProximity(pull_over_status_meta.pull_over_destination,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (agent_arclength_m > pull_over_destination_arclength_m) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    return;
  }

  const bool should_pass =
      is_cz ? (blocking_state_data.reasoning_info.tracked_side_to_ego() !=
               math::pb::kLeft)
            : (blocking_state_data.reasoning_info.is_blockage_object() &&
               blocking_state_data.reasoning_info.tracked_side_to_ego() !=
                   math::pb::kLeft &&
               !blocking_state_data.reasoning_info.contains_stm_prediction());
  if (should_pass) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
  }
  return;
}

}  // namespace

bool StaticAgentPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& object_sl_boundary,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  // Ignore the behind rear_bumper but not side agent.
  if (blocking_state_data.reasoning_info.is_ignorable_behind()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore ignorable behind agent; ");
    return true;
  }
  if (blocking_state_data.reasoning_info.is_stationary_out_of_range_object()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of range agent; ");
    return true;
  }
  const math::geometry::PolygonWithCache2d& contour =
      agent_inlane_state.tracked_state.inlane_param.pose.contour();
  const bool has_overlap_with_driving_corridor = HasOverlapWithDrivableCorridor(
      drivable_space_corridor_.driving_lateral_corridor,
      drivable_space_corridor_sl_boundary_, drivable_space_corridor_contour_,
      object_sl_boundary, contour,
      trajectory_intention_data.required_lat_gap_for_pass_state
          .comfort_required_lateral_gap);
  if (!has_overlap_with_driving_corridor) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of corridor agent; ");
    return true;
  }
  if (scenario_identify_result_.scenario_type ==
          pb::ScenarioRecognition::AVOID_NUDGE &&
      base::CheckAndGetValue(scenario_identify_result_.avoid_nudge_scene) ==
          pb::AvoidNudgeSceneType::IsWaitingForTrafficRule &&
      agent_inlane_state.agent_metadata.agent_type ==
          voy::perception::ObjectType::VEHICLE) {
    trajectory_intention_data.intention_type =
        FLAGS_planning_enable_static_agent_decision_branching
            ? pb::TrajectoryState::YIELD
            : pb::TrajectoryState::IGNORE;
    return true;
  }
  if (FLAGS_planning_enable_solid_line_hardening_for_large_vehicle &&
      !FLAGS_planning_enable_solid_line_hardening_for_dynamic_large_vehicle) {
    if (ShouldStayInLane(blocking_state_data,
                         agent_inlane_state.tracked_state.inlane_param
                             .full_body_start_arclength_m,
                         agent_inlane_state.tracked_state.inlane_param
                             .full_body_end_arclength_m)) {
      AdjustLaneBoundaryImutability(
          agent_inlane_state.tracked_state.inlane_param.min_lane_width);
    }
  }
  // Apply the business logic for unknown objects
  if (FLAGS_planning_enable_fod_interaction &&
      agent_inlane_state.agent_metadata.agent_type ==
          voy::perception::UNKNOWN &&
      has_overlap_with_driving_corridor &&
      behavior_type_ != pb::BehaviorType::REVERSE) {
    rt_event::PostRtEvent<rt_event::planner::TriggerFoDLogic>();
    // Determine whether the object should be considered for nudge or drive
    // through it. If true, design the nudge or drive through behavior given the
    // dimension of the object and the ego speed and distance to the object.
    GenerateNudgeOrDriveThroughBehaviorIfPossible(
        agent_inlane_state, ego_param_, nudge_motion_checker(),
        traffic_rule_reasoning_info_,
        blocking_state_data.reasoning_info.is_drivable_fod(),
        blocking_state_data.reasoning_info.is_better_not_drive_fod(),
        blocking_state_data.reasoning_info.plan_init_state_distance(),
        blocking_state_data.global_blocking_state, scenario_identify_result_,
        trajectory_intention_data);
    return true;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::LANE_KEEP &&
      lane_keep_behavior_type_ != pb::LK_NA) {
    if (blocking_state_data.reasoning_info.tracked_side_to_ego() !=
            math::pb::kOn ||
        blocking_state_data.backup_intention_meta_data.has_value()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
    }
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::CROSS_LANE) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    DCHECK(pull_over_status_meta_.has_value());
    EvaluateIntetionDuringPullOver(blocking_state_data,
                                   pull_over_status_meta_.value(),
                                   agent_inlane_state.tracked_state.inlane_param
                                       .full_body_start_arclength_m,
                                   /*is_cz=*/false, trajectory_intention_data);
  }
  trajectory_intention_data.fod_intentions.insert(
      pb::IntentionResult::NOT_APPLICABLE);
  return true;
}

bool StaticAgentPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& object_sl_boundary,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  // Ignore the behind rear_bumper but not side agent.
  if (blocking_state_data.reasoning_info.is_ignorable_behind()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore ignorable behind agent; ");
    return true;
  }
  if (blocking_state_data.reasoning_info.is_stationary_out_of_range_object()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of range agent; ");
    return true;
  }
  const math::geometry::PolygonWithCache2d& contour =
      object_occupancy_state.pose().contour();
  const bool has_overlap_with_driving_corridor = HasOverlapWithDrivableCorridor(
      drivable_space_corridor_.driving_lateral_corridor,
      drivable_space_corridor_sl_boundary_, drivable_space_corridor_contour_,
      object_sl_boundary, contour,
      trajectory_intention_data.required_lat_gap_for_pass_state
          .comfort_required_lateral_gap);
  if (!has_overlap_with_driving_corridor) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of corridor agent; ");
    return true;
  }
  if (scenario_identify_result_.scenario_type ==
          pb::ScenarioRecognition::AVOID_NUDGE &&
      base::CheckAndGetValue(scenario_identify_result_.avoid_nudge_scene) ==
          pb::AvoidNudgeSceneType::IsWaitingForTrafficRule &&
      object_occupancy_state.planner_object().is_vehicle()) {
    trajectory_intention_data.intention_type =
        FLAGS_planning_enable_static_agent_decision_branching
            ? pb::TrajectoryState::YIELD
            : pb::TrajectoryState::IGNORE;
    return true;
  }
  if (FLAGS_planning_enable_solid_line_hardening_for_large_vehicle &&
      !FLAGS_planning_enable_solid_line_hardening_for_dynamic_large_vehicle) {
    if (ShouldStayInLane(blocking_state_data,
                         object_occupancy_state.current_snapshot_info()
                             .object_occupancy_param()
                             .snapshot_start_arclength_m,
                         object_occupancy_state.current_snapshot_info()
                             .object_occupancy_param()
                             .snapshot_start_arclength_m)) {
      AdjustLaneBoundaryImutability(
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .min_corridor_width_m);
    }
  }
  // Apply the business logic for unknown objects
  if (FLAGS_planning_enable_fod_interaction &&
      object_occupancy_state.object_type() == voy::perception::UNKNOWN &&
      has_overlap_with_driving_corridor &&
      behavior_type_ != pb::BehaviorType::REVERSE) {
    rt_event::PostRtEvent<rt_event::planner::TriggerFoDLogic>();
    // Determine whether the object should be considered for nudge or drive
    // through it. If true, design the nudge or drive through behavior given the
    // dimension of the object and the ego speed and distance to the object.
    GenerateNudgeOrDriveThroughBehaviorIfPossible(
        object_occupancy_state, ego_param_, nudge_motion_checker(),
        traffic_rule_reasoning_info_,
        blocking_state_data.reasoning_info.is_drivable_fod(),
        blocking_state_data.reasoning_info.is_better_not_drive_fod(),
        blocking_state_data.reasoning_info.plan_init_state_distance(),
        blocking_state_data.global_blocking_state, scenario_identify_result_,
        trajectory_intention_data);
    return true;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::LANE_KEEP &&
      lane_keep_behavior_type_ != pb::LK_NA) {
    if (blocking_state_data.reasoning_info.tracked_side_to_ego() !=
            math::pb::kOn ||
        blocking_state_data.backup_intention_meta_data.has_value()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
    }
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::CROSS_LANE) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    DCHECK(pull_over_status_meta_.has_value());
    EvaluateIntetionDuringPullOver(
        blocking_state_data, pull_over_status_meta_.value(),
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_start_arclength_m,
        /*is_cz=*/false, trajectory_intention_data);
  }
  trajectory_intention_data.fod_intentions.insert(
      pb::IntentionResult::NOT_APPLICABLE);
  return true;
}

bool StaticAgentPathReasoner::PreReason(
    const ConstructionZoneInLaneState& construction_zone_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& object_sl_boundary,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  // Ignore the behind rear_bumper but not side agent.
  if (blocking_state_data.reasoning_info.is_ignorable_behind()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore ignorable behind CZ; ");
    return true;
  }
  if (blocking_state_data.reasoning_info.is_stationary_out_of_range_object()) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of range CZ; ");
    return true;
  }

  // https://cooper.didichuxing.com/docs2/document/2203169009402
  // Skip the cz is compress over with hard blocking.
  /// @todo (yongbing): revisited here for map changed area.
  if (construction_zone_state.is_planner_enterable_cz) {
    if (construction_zone_state.center_line_side == math::pb::kOn) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      absl::StrAppend(&trajectory_intention_data.debug_str,
                      "Ignore planner enterable CZ on center line; ");
      return true;
    } else {
      trajectory_intention_data.required_lat_gap_for_pass_state
          .critical_required_lateral_gap = 0.1;
    }
  }

  const math::geometry::PolygonWithCache2d& contour =
      construction_zone_state.construction_zone_contour;
  if (!HasOverlapWithDrivableCorridor(
          drivable_space_corridor_.driving_lateral_corridor,
          drivable_space_corridor_sl_boundary_,
          drivable_space_corridor_contour_, object_sl_boundary, contour,
          trajectory_intention_data.required_lat_gap_for_pass_state
              .comfort_required_lateral_gap)) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&trajectory_intention_data.debug_str,
                    "Ignore out of corridor CZ; ");
    return true;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::LANE_KEEP &&
      lane_keep_behavior_type_ != pb::LK_NA) {
    if (blocking_state_data.reasoning_info.tracked_side_to_ego() !=
            math::pb::kOn ||
        blocking_state_data.backup_intention_meta_data.has_value()) {
      trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
    }
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::CROSS_LANE) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
  }
  if (FLAGS_planning_enable_static_agent_decision_branching &&
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    DCHECK(pull_over_status_meta_.has_value());
    EvaluateIntetionDuringPullOver(blocking_state_data,
                                   pull_over_status_meta_.value(),
                                   construction_zone_state.start_arclength_m,
                                   /*is_cz=*/true, trajectory_intention_data);
  }
  return true;
}

std::vector<RepulsionMetaData> StaticAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
    /* trajectory_blocking_state_data_map */,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    double repulsion_required_lat_gap,
    pb::PathReasonerSeedPerAgent& current_path_reasoner_seed_per_agent,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const std::set<pb::IntentionResult::FoDIntention>& fod_intentions,
    bool& need_to_be_handled_by_pull_over_reasoner,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_StaticAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_data_vec;
  const bool is_ego_pullover_in_bus_bulb =
      (is_pullover_destination_in_bus_bulb_.has_value() &&
       is_pullover_destination_in_bus_bulb_.value() == true);
  const auto& agent_in_lane_param =
      agent_inlane_state.tracked_state.inlane_param;

  if (traffic_rule_reasoning_info_.is_ego_lane_near_bus_bulb() &&
      blocking_state_data.reasoning_info.is_risky_bus_in_bus_bulb() &&
      !is_ego_pullover_in_bus_bulb) {
    debug_str += "adding repulsion for bus bulb...\n";
    // Add bus bulb repulsion if needed.
    // ----------------------------------------------------------------------
    //
    //
    //
    // -------------------------------------------------------------- ref path
    //                    | half_lane_width_m - ego_width_m
    //             ________________________________   <- Added repulsion for
    //              region of bus in bus bulb
    //             |<5m>|                |  <10m>  |
    // -----------------------------------------------------------------------
    //          \         _______________                          /
    //           \       |     BUS       |           BUS BULB     /
    //            \      |_______________|                       /
    //             \____________________________________________/
    //
    //
    const double agent_start_arclength_m =
        agent_in_lane_param.full_body_start_arclength_m;
    const double agent_end_arclength_m =
        agent_in_lane_param.full_body_end_arclength_m;

    // 5.0m before and 10.0m after the bus in s direction for some heads up.
    // Offset in l would be half_lane_width - ego_width to try
    // to offset Ego to exploit the left side room in the lane as much ad
    // possible.
    const double lateral_offset_m =
        traffic_rule_reasoning_info_.ego_lane_avg_width_m() / 2.0 -
        robot_state_snapshot.bounding_box().width();

    std::vector<FrenetPoint> repulsion_points;
    repulsion_points.reserve(2);
    repulsion_points.emplace_back(agent_start_arclength_m - 5.0,
                                  lateral_offset_m);
    repulsion_points.emplace_back(agent_end_arclength_m + 10.0,
                                  lateral_offset_m);

    const path::BoundaryStrength repulsion_strength =
        traffic_rule_reasoning_info_.is_left_side_lane_safe()
            ? path::BoundaryStrength::kModerate
            : path::BoundaryStrength::kWeak;
    repulsion_meta_data_vec.emplace_back(
        RepulsionMetaData::RepulsionDirection::kLeft,
        std::move(repulsion_points),
        /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false, repulsion_strength,
        /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
        pb::AgentReasonerId::STATIC_AGENT,
        ToProto(TypedObjectId(agent_inlane_state.object_id,
                              pb::ObjectSourceType::kTrackedObject)),
        /*trajectory_id_in=*/std::nullopt,
        /*max_abs_lateral_accel_in=*/
        kMaxLateralAccelForBusInBusBulbRepulsionInMpss,
        /*max_abs_lateral_juke_in=*/
        kMaxLateralJerkForBusInBusBulbRepulsionInMpss);
  }

  // Add repulsion for driving through an FoD.
  if (FLAGS_planning_enable_undercarriaging_over_fod &&
      HasFodIntention(fod_intentions,
                      pb::IntentionResult::UNDERCARRIAGE_OVER)) {
    const double agent_start_arclength_m =
        agent_in_lane_param.full_body_start_arclength_m;
    const double agent_end_arclength_m =
        agent_in_lane_param.full_body_end_arclength_m;
    // The lateral offset should be using the contour of the object.
    // We should add repulsions on both sides to make ego drive through the
    // object. To avoid repulsion violation, the distance between these two
    // repulsions should be greater than the ego's width.
    const double repulsion_offset_FOD_m = ego_param_.width_m / 2;
    const double object_center_lateral_position =
        (agent_in_lane_param.dist_to_center_line_range.end_pos +
         agent_in_lane_param.dist_to_center_line_range.start_pos) /
        2;

    const double left_lateral_offset_m =
        object_center_lateral_position + repulsion_offset_FOD_m;
    const double right_lateral_offset_m =
        object_center_lateral_position - repulsion_offset_FOD_m;
    const double ego_length = robot_state_snapshot.car_model_with_shape()
                                  .shape_measurement()
                                  .length();
    AddBiDirectionalRepulsions(
        /*start_s=*/agent_start_arclength_m - ego_length,
        /*end_s=*/agent_end_arclength_m + ego_length, left_lateral_offset_m,
        right_lateral_offset_m,
        /*repulsion_strength=*/path::BoundaryStrength::kModerate,
        /*source_type=*/pb::ObjectSourceType::kTrackedObject,
        agent_inlane_state.object_id, repulsion_meta_data_vec);
    absl::StrAppend(&debug_str,
                    "Add repulsions for FOD: ", agent_inlane_state.object_id);
  }

  std::vector<pb::TrajectoryPose> start_to_move_trajectory_poses =
      GetStartToMoveTrajectoryPoses(
          agent_inlane_state.agent_metadata.start_to_move_trajectory,
          agent_inlane_state.agent_metadata.stationary_intention,
          agent_inlane_state.agent_metadata.agent_type,
          agent_inlane_state.tracked_state.inlane_param,
          blocking_state_data.reasoning_info.recent_average_accel(),
          blocking_state_data.reasoning_info.recent_average_jerk(),
          blocking_state_data.reasoning_info.last_stationary_timestamp(),
          robot_state_snapshot.timestamp(),
          blocking_state_data.reasoning_info.has_agent_lost_patience(),
          debug_str);
  if (FLAGS_planning_enable_crawl_interested_agent_repulsion &&
      blocking_state_data.reasoning_info
          .is_lane_change_crawl_interested_agent()) {
    debug_str += "adding crawl target lane agent repulsion...\n";
    const std::unique_ptr<RepulsionMetaData>
        lane_change_crawl_target_lane_agent_repulsion =
            AddLaneChangeCrawlTargetLaneAgentRepusion(
                blocking_state_data.reasoning_info,
                agent_inlane_state.object_id,
                agent_inlane_state.tracked_state.inlane_param.pose.contour()
                    .polygon(),
                agent_inlane_state.tracked_state.inlane_param.pose.heading(),
                robot_state_snapshot.heading(), ego_param_.width_m,
                lane_change_execution_info, repulsion_required_lat_gap,
                debug_str);
    if (lane_change_crawl_target_lane_agent_repulsion != nullptr) {
      repulsion_meta_data_vec.push_back(
          *lane_change_crawl_target_lane_agent_repulsion);
    }
    return repulsion_meta_data_vec;
  }
  const speed::Profile& refer_speed_profile =
      reusable_last_speed_profile.value_or(upper_bound_speed_);
  if (!reusable_last_speed_profile.has_value()) {
    debug_str +=
        "no reusable_last_speed_profile, use upper bound speed instead\n";
  }
  std::vector<pb::TrajectoryPose> smoothed_traj;

  std::string string_object_id =
      TypedObjectId(agent_inlane_state.object_id,
                    pb::ObjectSourceType::kTrackedObject)
          .ToString();
  auto seed_it =
      last_path_reasoner_seed_.path_reasoner_seed_map().find(string_object_id);
  if (FLAGS_planning_enable_smooth_stm_repulsion &&
      agent_inlane_state.agent_metadata.prediction_agent_rank.has_value() &&
      *agent_inlane_state.agent_metadata.prediction_agent_rank <= 5 &&
      seed_it != last_path_reasoner_seed_.path_reasoner_seed_map().end()) {
    smoothed_traj = GetSmoothedTrajectoryPoses(
        std::vector<pb::TrajectoryPose>(
            seed_it->second.static_agent_path_reasoner_seed()
                .last_stm_trajectory_poses()
                .begin(),
            seed_it->second.static_agent_path_reasoner_seed()
                .last_stm_trajectory_poses()
                .end()),
        start_to_move_trajectory_poses,
        agent_inlane_state.tracked_state.inlane_param.pose.center_2d(),
        robot_state_snapshot.timestamp());
  } else {
    smoothed_traj = std::move(start_to_move_trajectory_poses);
  }
  // Populate to the current seed.
  for (const auto& smoothed_traj_pose : smoothed_traj) {
    auto* add_pose = current_path_reasoner_seed_per_agent
                         .mutable_static_agent_path_reasoner_seed()
                         ->add_last_stm_trajectory_poses();
    *add_pose = smoothed_traj_pose;
  }
  // No moving traj.
  if (smoothed_traj.empty()) {
    debug_str += "empty stm traj\n";
    if (blocking_state_data.reasoning_info.is_pull_over_rear_obstacle()) {
      debug_str += "need to be handled by pull over reasoner...\n";
      need_to_be_handled_by_pull_over_reasoner = true;
      return repulsion_meta_data_vec;
    }
    // Add region repulsion if this object is the first xlane nudge object in
    // the recorded list. NOTE: It might be not the nearest object.
    if (FLAGS_planning_enable_xlane_nudge_region_repulsion &&
        IsFirstXLaneNudgeObject(
            TypedObjectId(agent_inlane_state.object_id,
                          pb::ObjectSourceType::kTrackedObject))) {
      debug_str += "add xlane nudge object repulsion\n";
      DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
      DCHECK(scenario_identify_result_.xlane_nudge_scene.value().type !=
             pb::XLaneNudgeSceneType::kCrossBoth);
      const RepulsionMetaData::RepulsionDirection repulsion_direction =
          scenario_identify_result_.xlane_nudge_scene.value().type ==
                  pb::XLaneNudgeSceneType::kCrossLeft
              ? RepulsionMetaData::RepulsionDirection::kLeft
              : RepulsionMetaData::RepulsionDirection::kRight;
      const std::unique_ptr<RepulsionMetaData> xlane_nudge_region_repulsion =
          AddRepulsionForXLaneNudgeRegion(
              scenario_identify_result_.xlane_nudge_scene.value(),
              TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject),
              repulsion_direction, reasoner_id_,
              robot_state_snapshot.timestamp());
      if (xlane_nudge_region_repulsion != nullptr) {
        repulsion_meta_data_vec.push_back(*xlane_nudge_region_repulsion);
      }
      return repulsion_meta_data_vec;
    }

    debug_str += "adding longitudinal buffer repulsion...\n";
    const std::unique_ptr<RepulsionMetaData> longitudinal_buffer_repulsion =
        MaybeAddLongitudinalBuffer(blocking_state_data.reasoning_info,
                                   agent_inlane_state.object_id,
                                   agent_inlane_state.tracked_state.inlane_param
                                       .full_body_end_arclength_m,
                                   agent_inlane_state.tracked_state.inlane_param
                                       .full_body_end_lateral_distance_m,
                                   repulsion_required_lat_gap);
    if (longitudinal_buffer_repulsion != nullptr) {
      repulsion_meta_data_vec.push_back(*longitudinal_buffer_repulsion);
    }
    return repulsion_meta_data_vec;
  }

  std::vector<double> ref_speed_profile_func_ts;
  std::vector<double> refer_speed_profile_func_xs;
  ref_speed_profile_func_ts.reserve(refer_speed_profile.size());
  refer_speed_profile_func_xs.reserve(refer_speed_profile.size());
  for (const auto& state : refer_speed_profile) {
    ref_speed_profile_func_ts.push_back(state.t);
    refer_speed_profile_func_xs.push_back(state.x);
  }

  auto optional_refer_path_curve = GetLastPathCurveOrRefPathCurve(
      reusable_last_path, truncated_reference_path_, debug_str);
  if (!optional_refer_path_curve.has_value()) {
    debug_str += "fail to get refer_path_curve\n";
    return repulsion_meta_data_vec;
  }
  math::geometry::PolylineCurve2d refer_path_curve =
      std::move(*optional_refer_path_curve);

  const math::PiecewiseLinearFunction refer_speed_profile_x_at_t(
      std::move(ref_speed_profile_func_ts),
      std::move(refer_speed_profile_func_xs));

  speed::Profile profile_upper_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool should_add_brake_motion = false;
  double brake_heading = 0.0;

  auto handle_traj_pose = [&](const pb::TrajectoryPose& traj_pose) {
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            math::geometry::Point2d(traj_pose.x_pos(), traj_pose.y_pos()),
            agent_center_path_points.back()))) {
      return true;
    }
    if (traj_pose.timestamp() - smoothed_traj.front().timestamp() >
        kMaxTimeStampToConsiderStartToMoveTraj) {
      return false;
    }
    agent_center_path_points.emplace_back(traj_pose.x_pos(), traj_pose.y_pos());
    profile_upper_bound.push_back(
        speed::State(math::Ms2Sec(traj_pose.timestamp()), traj_pose.odom(),
                     traj_pose.speed(), traj_pose.accel(), traj_pose.jerk()));
    const auto& agent_original_pose =
        agent_inlane_state.tracked_state.inlane_param.pose;
    if (!blocking_state_data.reasoning_info.has_agent_lost_patience() &&
        CanAgentNoticeEgo(robot_state_snapshot, agent_original_pose.contour(),
                          agent_original_pose.center_2d(),
                          agent_original_pose.heading(), traj_pose,
                          refer_path_curve, refer_speed_profile_x_at_t)) {
      should_add_brake_motion = true;
      brake_heading = traj_pose.heading();
      return false;
    }
    return true;
  };

  for (const auto& traj_pose : smoothed_traj) {
    if (!handle_traj_pose(traj_pose)) {
      break;
    }
  }

  if (should_add_brake_motion) {
    AddBrakeMotion(brake_heading, profile_upper_bound,
                   agent_center_path_points);
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(
          std::move(agent_center_path_points),
          /*max_distance=*/math::constants::kDefaultSimplifyThreshold,
          /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "agent_center_path invalid";
    return repulsion_meta_data_vec;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    debug_str += "agent_center_path too short";
    return repulsion_meta_data_vec;
  }

  // Form a lower bound profile.
  speed::Profile profile_lower_bound;
  for (const auto& state : profile_upper_bound) {
    profile_lower_bound.emplace_back(state.t, /*x_in=*/0.0, /*v_in=*/0.0,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
  }

  RepulsionMetaData::RepulsionDirection repulsion_direction =
      GetRepulsionDirection(
          refer_path_curve,
          agent_inlane_state.tracked_state.inlane_param.pose.center_2d());
  const double half_lane_width_m =
      traffic_rule_reasoning_info_.ego_lane_avg_width_m() / 2;
  const double lateral_bound =
      FLAGS_planning_lateral_bound_for_stm_repulsion ? half_lane_width_m : 0.0;
  bool is_risky =
      IsTargetDirectionRisky(repulsion_direction, frame_analyzer_results);
  const double ego_width = ego_param_.width_m;
  repulsion_meta_data_vec.emplace_back(
      repulsion_direction,
      agent_inlane_state.tracked_state.inlane_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound),
      agent_inlane_state.tracked_state.inlane_param.pose.heading(),
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      repulsion_required_lat_gap +
          (blocking_state_data.reasoning_info
                       .is_pose_follow_associated_lane() ||
                   !blocking_state_data.reasoning_info
                        .is_agent_toward_reference_line()
               ? 0.0
               : kExtraRepulsionLatBufferForNotFollowLaneAgents),
      reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/std::nullopt,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft
          ? lateral_bound
          : -lateral_bound,
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      is_risky ? RepulsionMetaData::ExtendBoundaryLevel::kNA
               : RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      is_risky ? std::nullopt : std::make_optional(ego_width),
      is_risky ? std::nullopt : std::make_optional(ego_width / 2.0));
  return repulsion_meta_data_vec;
}

std::vector<RepulsionMetaData> StaticAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    double repulsion_required_lat_gap,
    pb::PathReasonerSeedPerAgent& current_path_reasoner_seed_per_agent,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const std::set<pb::IntentionResult::FoDIntention>& fod_intentions,
    bool& need_to_be_handled_by_pull_over_reasoner,
    std::string& debug_str) const {
  (void)current_path_reasoner_seed_per_agent;
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_StaticAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_data_vec;
  const ObjectOccupancyParam& object_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const bool is_ego_pullover_in_bus_bulb =
      (is_pullover_destination_in_bus_bulb_.has_value() &&
       is_pullover_destination_in_bus_bulb_.value() == true);
  if (traffic_rule_reasoning_info_.is_ego_lane_near_bus_bulb() &&
      blocking_state_data.reasoning_info.is_risky_bus_in_bus_bulb() &&
      !is_ego_pullover_in_bus_bulb) {
    debug_str += "adding repulsion for bus bulb...\n";
    // Add bus bulb repulsion if needed.
    // ----------------------------------------------------------------------
    //
    //
    //
    // -------------------------------------------------------------- ref path
    //                    | half_lane_width_m - ego_width_m
    //             ________________________________   <- Added repulsion for
    //              region of bus in bus bulb
    //             |<5m>|                |  <10m>  |
    // -----------------------------------------------------------------------
    //          \         _______________                          /
    //           \       |     BUS       |           BUS BULB     /
    //            \      |_______________|                       /
    //             \____________________________________________/
    //
    //
    const double agent_start_arclength_m =
        object_occupancy_param.full_body_start_arclength_m;
    const double agent_end_arclength_m =
        object_occupancy_param.full_body_end_arclength_m;

    // 5.0m before and 10.0m after the bus in s direction for some heads up.
    // Offset in l would be half_lane_width - ego_width to try
    // to offset Ego to exploit the left side room in the lane as much ad
    // possible.
    const double lateral_offset_m =
        traffic_rule_reasoning_info_.ego_lane_avg_width_m() / 2.0 -
        robot_state_snapshot.bounding_box().width();

    std::vector<FrenetPoint> repulsion_points;
    repulsion_points.reserve(2);
    repulsion_points.emplace_back(agent_start_arclength_m - 5.0,
                                  lateral_offset_m);
    repulsion_points.emplace_back(agent_end_arclength_m + 10.0,
                                  lateral_offset_m);

    const path::BoundaryStrength repulsion_strength =
        traffic_rule_reasoning_info_.is_left_side_lane_safe()
            ? path::BoundaryStrength::kModerate
            : path::BoundaryStrength::kWeak;

    repulsion_meta_data_vec.emplace_back(
        RepulsionMetaData::RepulsionDirection::kLeft,
        std::move(repulsion_points),
        /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false, repulsion_strength,
        /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
        pb::AgentReasonerId::STATIC_AGENT,
        ToProto(TypedObjectId(object_occupancy_state.object_id(),
                              pb::ObjectSourceType::kTrackedObject)),
        /*trajectory_id_in=*/std::nullopt,
        /*max_abs_lateral_accel_in=*/
        kMaxLateralAccelForBusInBusBulbRepulsionInMpss,
        /*max_abs_lateral_juke_in=*/
        kMaxLateralJerkForBusInBusBulbRepulsionInMpss);
  }

  // Add repulsion for driving through an FoD.
  if (FLAGS_planning_enable_undercarriaging_over_fod &&
      HasFodIntention(fod_intentions,
                      pb::IntentionResult::UNDERCARRIAGE_OVER)) {
    const double agent_start_arclength_m =
        object_occupancy_param.full_body_start_arclength_m;
    const double agent_end_arclength_m =
        object_occupancy_param.full_body_end_arclength_m;
    // The lateral offset should be using the contour of the object.
    // We should add repulsions on both sides to make ego drive through the
    // object.
    const double repulsion_offset_FOD_m = ego_param_.width_m / 2;
    const double object_center_lateral_position =
        (object_occupancy_param.dist_to_center_line_range.end_pos +
         object_occupancy_param.dist_to_center_line_range.start_pos) /
        2;

    const double left_lateral_offset_m =
        object_center_lateral_position + repulsion_offset_FOD_m;
    const double right_lateral_offset_m =
        object_center_lateral_position - repulsion_offset_FOD_m;
    const double ego_length = robot_state_snapshot.car_model_with_shape()
                                  .shape_measurement()
                                  .length();
    AddBiDirectionalRepulsions(
        /*start_s=*/agent_start_arclength_m - ego_length,
        /*end_s=*/agent_end_arclength_m + ego_length, left_lateral_offset_m,
        right_lateral_offset_m,
        /*repulsion_strength=*/path::BoundaryStrength::kModerate,
        /*source_type=*/pb::ObjectSourceType::kTrackedObject,
        object_occupancy_state.object_id(), repulsion_meta_data_vec);
    absl::StrAppend(&debug_str, "Add repulsions for FOD: ",
                    object_occupancy_state.object_id());
  }

  std::vector<pb::TrajectoryPose> start_to_move_trajectory_poses =
      GetStartToMoveTrajectoryPoses(
          object_occupancy_state.start_to_move_trajectory(),
          object_occupancy_state.stationary_intention(),
          object_occupancy_state.object_type(),
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param(),
          object_occupancy_state.pose(),
          blocking_state_data.reasoning_info.recent_average_accel(),
          blocking_state_data.reasoning_info.recent_average_jerk(),
          blocking_state_data.reasoning_info.last_stationary_timestamp(),
          robot_state_snapshot.timestamp(),
          blocking_state_data.reasoning_info.has_agent_lost_patience(),
          debug_str);

  if (FLAGS_planning_enable_crawl_interested_agent_repulsion &&
      blocking_state_data.reasoning_info
          .is_lane_change_crawl_interested_agent()) {
    debug_str += "adding crawl target lane agent repulsion...\n";
    const std::unique_ptr<RepulsionMetaData>
        lane_change_crawl_target_lane_agent_repulsion =
            AddLaneChangeCrawlTargetLaneAgentRepusion(
                blocking_state_data.reasoning_info,
                object_occupancy_state.object_id(),
                object_occupancy_state.pose().contour().polygon(),
                object_occupancy_state.pose().contour_heading(),
                robot_state_snapshot.heading(), ego_param_.width_m,
                lane_change_execution_info, repulsion_required_lat_gap,
                debug_str);
    if (lane_change_crawl_target_lane_agent_repulsion != nullptr) {
      repulsion_meta_data_vec.push_back(
          *lane_change_crawl_target_lane_agent_repulsion);
    }
    return repulsion_meta_data_vec;
  }
  const speed::Profile& refer_speed_profile =
      reusable_last_speed_profile.value_or(upper_bound_speed_);
  if (!reusable_last_speed_profile.has_value()) {
    debug_str +=
        "no reusable_last_speed_profile, use upper bound speed instead\n";
  }
  // No moving traj.
  if (start_to_move_trajectory_poses.empty()) {
    debug_str += "empty stm traj\n";
    if (blocking_state_data.reasoning_info.is_pull_over_rear_obstacle()) {
      debug_str += "need to be handled by pull over reasoner...\n";
      need_to_be_handled_by_pull_over_reasoner = true;
      return repulsion_meta_data_vec;
    }
    // Add region repulsion if this object is the first xlane nudge object in
    // the recorded list. NOTE: It might be not the nearest object.
    if (FLAGS_planning_enable_xlane_nudge_region_repulsion &&
        IsFirstXLaneNudgeObject(
            TypedObjectId(object_occupancy_state.object_id(),
                          pb::ObjectSourceType::kTrackedObject))) {
      debug_str += "add xlane nudge object repulsion\n";
      DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
      DCHECK(scenario_identify_result_.xlane_nudge_scene.value().type !=
             pb::XLaneNudgeSceneType::kCrossBoth);
      const RepulsionMetaData::RepulsionDirection repulsion_direction =
          scenario_identify_result_.xlane_nudge_scene.value().type ==
                  pb::XLaneNudgeSceneType::kCrossLeft
              ? RepulsionMetaData::RepulsionDirection::kLeft
              : RepulsionMetaData::RepulsionDirection::kRight;
      const std::unique_ptr<RepulsionMetaData> xlane_nudge_region_repulsion =
          AddRepulsionForXLaneNudgeRegion(
              scenario_identify_result_.xlane_nudge_scene.value(),
              TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject),
              repulsion_direction, reasoner_id_,
              robot_state_snapshot.timestamp());
      if (xlane_nudge_region_repulsion != nullptr) {
        repulsion_meta_data_vec.push_back(*xlane_nudge_region_repulsion);
      }
      return repulsion_meta_data_vec;
    }

    debug_str += "adding longitudinal buffer repulsion...\n";
    const std::unique_ptr<RepulsionMetaData> longitudinal_buffer_repulsion =
        MaybeAddLongitudinalBuffer(
            blocking_state_data.reasoning_info,
            object_occupancy_state.object_id(),
            object_occupancy_state.current_snapshot_info()
                .object_occupancy_param()
                .full_body_end_arclength_m,
            object_occupancy_state.current_snapshot_info()
                .object_occupancy_param()
                .full_body_end_lateral_distance_m,
            repulsion_required_lat_gap);
    if (longitudinal_buffer_repulsion != nullptr) {
      repulsion_meta_data_vec.push_back(*longitudinal_buffer_repulsion);
    }
    return repulsion_meta_data_vec;
  }
  std::vector<double> ref_speed_profile_func_ts;
  std::vector<double> refer_speed_profile_func_xs;
  ref_speed_profile_func_ts.reserve(refer_speed_profile.size());
  refer_speed_profile_func_xs.reserve(refer_speed_profile.size());
  for (const auto& state : refer_speed_profile) {
    ref_speed_profile_func_ts.push_back(state.t);
    refer_speed_profile_func_xs.push_back(state.x);
  }

  auto optional_refer_path_curve = GetLastPathCurveOrRefPathCurve(
      reusable_last_path, truncated_reference_path_, debug_str);
  if (!optional_refer_path_curve.has_value()) {
    debug_str += "fail to get refer_path_curve\n";
    return repulsion_meta_data_vec;
  }
  math::geometry::PolylineCurve2d refer_path_curve =
      std::move(*optional_refer_path_curve);

  const math::PiecewiseLinearFunction refer_speed_profile_x_at_t(
      std::move(ref_speed_profile_func_ts),
      std::move(refer_speed_profile_func_xs));

  speed::Profile profile_upper_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool should_add_brake_motion = false;
  double brake_heading = 0.0;

  auto handle_traj_pose = [&](const pb::TrajectoryPose& traj_pose) {
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            math::geometry::Point2d(traj_pose.x_pos(), traj_pose.y_pos()),
            agent_center_path_points.back()))) {
      return true;
    }
    if (traj_pose.timestamp() -
            start_to_move_trajectory_poses.front().timestamp() >
        kMaxTimeStampToConsiderStartToMoveTraj) {
      return false;
    }
    agent_center_path_points.emplace_back(traj_pose.x_pos(), traj_pose.y_pos());
    profile_upper_bound.push_back(
        speed::State(math::Ms2Sec(traj_pose.timestamp()), traj_pose.odom(),
                     traj_pose.speed(), traj_pose.accel(), traj_pose.jerk()));
    if (!blocking_state_data.reasoning_info.has_agent_lost_patience() &&
        CanAgentNoticeEgo(robot_state_snapshot,
                          object_occupancy_state.pose().contour(),
                          object_occupancy_state.pose().center_2d(),
                          object_occupancy_state.pose().heading(), traj_pose,
                          refer_path_curve, refer_speed_profile_x_at_t)) {
      should_add_brake_motion = true;
      brake_heading = traj_pose.heading();
      return false;
    }
    return true;
  };

  for (const auto& traj_pose : start_to_move_trajectory_poses) {
    if (!handle_traj_pose(traj_pose)) {
      break;
    }
  }

  if (should_add_brake_motion) {
    AddBrakeMotion(brake_heading, profile_upper_bound,
                   agent_center_path_points);
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points));
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "agent_center_path invalid";
    return repulsion_meta_data_vec;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    debug_str += "agent_center_path too short";
    return repulsion_meta_data_vec;
  }

  // Form a lower bound profile.
  speed::Profile profile_lower_bound;
  for (const auto& state : profile_upper_bound) {
    profile_lower_bound.emplace_back(state.t, /*x_in=*/0.0, /*v_in=*/0.0,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
  }

  RepulsionMetaData::RepulsionDirection repulsion_direction =
      GetRepulsionDirection(refer_path_curve,
                            object_occupancy_state.pose().center_2d());
  const double half_lane_width_m =
      traffic_rule_reasoning_info_.ego_lane_avg_width_m() / 2;
  const double lateral_bound =
      FLAGS_planning_lateral_bound_for_stm_repulsion ? half_lane_width_m : 0.0;
  bool is_risky =
      IsTargetDirectionRisky(repulsion_direction, frame_analyzer_results);
  const double ego_width = ego_param_.width_m;
  repulsion_meta_data_vec.emplace_back(
      repulsion_direction, object_occupancy_state.pose().contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), object_occupancy_state.pose().heading(),
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      repulsion_required_lat_gap +
          (blocking_state_data.reasoning_info
                       .is_pose_follow_associated_lane() ||
                   !blocking_state_data.reasoning_info
                        .is_agent_toward_reference_line()
               ? 0.0
               : kExtraRepulsionLatBufferForNotFollowLaneAgents),
      reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/std::nullopt,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft
          ? lateral_bound
          : -lateral_bound,
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      is_risky ? RepulsionMetaData::ExtendBoundaryLevel::kNA
               : RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      is_risky ? std::nullopt : std::make_optional(ego_width),
      is_risky ? std::nullopt : std::make_optional(ego_width / 2.0));
  return repulsion_meta_data_vec;
}

std::unique_ptr<RepulsionMetaData> StaticAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const ConstructionZoneInLaneState& cz_inlane_state) const {
  // Add region repulsion if this object is the first xlane nudge object in the
  // recorded list. NOTE: It might be not the nearest object.
  if (FLAGS_planning_enable_xlane_nudge_region_repulsion &&
      IsFirstXLaneNudgeObject(TypedObjectId(
          cz_inlane_state.id, pb::ObjectSourceType::kConstructionZone))) {
    DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
    DCHECK(scenario_identify_result_.xlane_nudge_scene.value().type !=
           pb::XLaneNudgeSceneType::kCrossBoth);
    const RepulsionMetaData::RepulsionDirection repulsion_direction =
        scenario_identify_result_.xlane_nudge_scene.value().type ==
                pb::XLaneNudgeSceneType::kCrossLeft
            ? RepulsionMetaData::RepulsionDirection::kLeft
            : RepulsionMetaData::RepulsionDirection::kRight;
    return AddRepulsionForXLaneNudgeRegion(
        scenario_identify_result_.xlane_nudge_scene.value(),
        TypedObjectId(cz_inlane_state.id,
                      pb::ObjectSourceType::kConstructionZone),
        repulsion_direction, reasoner_id_, robot_state_snapshot.timestamp());
  }
  return nullptr;
}

bool StaticAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  if (blocking_state_data.reasoning_info
          .is_lane_change_ignore_agent_in_post_reason()) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "lane change post reason ignore; ");
    return true;
  }
  // is_overtaken is true only for stationary static agent that:
  // 1. not potentially reversing, AND
  // 2. doesn't STM, OR STM but heading is not towards ego reference line
  intention_result.is_overtaken =
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      blocking_state_data.reasoning_info.is_stationary() &&
      !blocking_state_data.reasoning_info.is_potentially_reversing() &&
      (!blocking_state_data.reasoning_info.contains_stm_prediction() ||
       !blocking_state_data.reasoning_info.is_agent_toward_reference_line());

  if (intention_result.is_overtaken &&
      blocking_state_data.reasoning_info.contains_stm_prediction()) {
    rt_event::PostRtEvent<rt_event::planner::PopulateOvertakeForStm>();
  }

  // Get max speed from start_s and end_s of agent.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, agent_inlane_state, ego_param_.arclength_m,
      buffer_before_agent_meters);

  AdjustSameDirectionReqLatGapInPostReason(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_,
      agent_inlane_state.tracked_state.inlane_param.speed_mps,
      /*ego_speed_near_agent=*/ego_speed_near_agent,
      intention_result.lateral_decision,
      agent_inlane_state.agent_metadata.agent_type,
      intention_result.required_lat_gap, intention_result.is_risky_agent,
      &(intention_result.debug_str));

  // Sometimes ped intends to cross the lane but treated as static, for example
  // cn8017064. Currently if we increase RLG, Ego will not decelerate enough
  // so will be risky if ped does not yield.
  // Need revisit this kind of scenarios in the future.
  if (agent_inlane_state.agent_metadata.agent_type == voy::perception::PED &&
      !blocking_state_data.reasoning_info.is_crossing()) {
    const double buffer_before_agent_meters =
        ego_param_.rear_axle_to_front_bumper_m;
    std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
        reusable_last_speed_profile_, agent_inlane_state,
        ego_param_.arclength_m, buffer_before_agent_meters);
    MaybeAdjustPedLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
        /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
        agent_inlane_state.agent_metadata.agent_type,
        intention_result.required_lat_gap, &(intention_result.debug_str));
  }

  if (agent_inlane_state.agent_metadata.agent_type ==
      voy::perception::CYCLIST) {
    const double buffer_before_agent_meters =
        ego_param_.rear_axle_to_front_bumper_m;
    std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
        reusable_last_speed_profile_, agent_inlane_state,
        ego_param_.arclength_m, buffer_before_agent_meters);
    MaybeAdjustCycLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_,
        agent_inlane_state.tracked_state.inlane_param.along_track_speed_mps,
        active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
        intention_result.lateral_decision,
        blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
        agent_inlane_state.agent_metadata.agent_type,
        intention_result.required_lat_gap, &(intention_result.debug_str));
  }

  if (lane_keep_behavior_type_ == pb::LK_PULL_OUT &&
      agent_inlane_state.tracked_state.inlane_param.center_line_side ==
          math::pb::kRight) {
    intention_result.required_lat_gap.critical_required_lateral_gap += 0.2;
    intention_result.required_lat_gap.comfort_required_lateral_gap += 0.2;
    absl::StrAppend(
        &intention_result.debug_str,
        "Increase req lat gap for pull out right side agent by 0.2; ");
  }
  // Adjust the lane boundary imutability.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_start_arclength_m,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  const auto& fod_intentions = trajectory_intention_data.fod_intentions;
  AddFoDIntentionToIntentionResultMetadata(fod_intentions, intention_result);
  AddCurrentPoseNominalConstraint(
      agent_inlane_state, intention_result.lateral_decision, intention_result);
  return true;
}

bool StaticAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;

  if (blocking_state_data.reasoning_info
          .is_lane_change_ignore_agent_in_post_reason()) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "lane change post reason ignore; ");
    return true;
  }
  // is_overtaken is true only for stationary static agent that:
  // 1. not potentially reversing, AND
  // 2. doesn't STM, OR STM but heading is not towards ego reference line
  intention_result.is_overtaken =
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      blocking_state_data.reasoning_info.is_stationary() &&
      !blocking_state_data.reasoning_info.is_potentially_reversing() &&
      (!blocking_state_data.reasoning_info.contains_stm_prediction() ||
       !blocking_state_data.reasoning_info.is_agent_toward_reference_line());

  if (intention_result.is_overtaken &&
      blocking_state_data.reasoning_info.contains_stm_prediction()) {
    rt_event::PostRtEvent<rt_event::planner::PopulateOvertakeForStm>();
  }

  // Get max speed from start_s and end_s of agent.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, object_occupancy_state,
      ego_param_.arclength_m, buffer_before_agent_meters);

  AdjustSameDirectionReqLatGapInPostReason(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .speed_mps,
      /*ego_speed_near_agent=*/ego_speed_near_agent,
      intention_result.lateral_decision, object_occupancy_state.object_type(),
      intention_result.required_lat_gap, intention_result.is_risky_agent,
      &(intention_result.debug_str));

  // Sometimes ped intends to cross the lane but treated as static, for example
  // cn8017064. Currently if we increase RLG, Ego will not decelerate enough
  // so will be risky if ped does not yield.
  // Need revisit this kind of scenarios in the future.
  if (object_occupancy_state.object_type() == voy::perception::PED &&
      !blocking_state_data.reasoning_info.is_crossing()) {
    const double buffer_before_agent_meters =
        ego_param_.rear_axle_to_front_bumper_m;
    std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
        reusable_last_speed_profile_, object_occupancy_state,
        ego_param_.arclength_m, buffer_before_agent_meters);
    MaybeAdjustPedLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
        /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
        object_occupancy_state.object_type(), intention_result.required_lat_gap,
        &(intention_result.debug_str));
  }

  if (object_occupancy_state.object_type() == voy::perception::CYCLIST) {
    const double buffer_before_agent_meters =
        ego_param_.rear_axle_to_front_bumper_m;
    std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
        reusable_last_speed_profile_, object_occupancy_state,
        ego_param_.arclength_m, buffer_before_agent_meters);
    MaybeAdjustCycLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_,
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .along_track_speed_mps,
        active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
        intention_result.lateral_decision,
        blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
        object_occupancy_state.object_type(), intention_result.required_lat_gap,
        &(intention_result.debug_str));
  }

  if (lane_keep_behavior_type_ == pb::LK_PULL_OUT &&
      object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .center_line_side == math::pb::kRight) {
    intention_result.required_lat_gap.critical_required_lateral_gap += 0.2;
    intention_result.required_lat_gap.comfort_required_lateral_gap += 0.2;
    absl::StrAppend(
        &intention_result.debug_str,
        "Increase req lat gap for pull out right side agent by 0.2; ");
  }
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_start_arclength_m,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  const auto& fod_intentions = trajectory_intention_data.fod_intentions;
  AddFoDIntentionToIntentionResultMetadata(fod_intentions, intention_result);
  AddCurrentPoseNominalConstraint(object_occupancy_state,
                                  intention_result.lateral_decision,
                                  intention_result);
  return true;
}

bool StaticAgentPathReasoner::PostReason(
    const ConstructionZoneInLaneState& construction_zone_state,
    const LateralBlockingStateMetaData& /* blocking_state_data */,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  AddCurrentPoseNominalConstraint(
      construction_zone_state, intention_result.lateral_decision,
      intention_result.required_lat_gap, intention_result);
  return true;
}

bool StaticAgentPathReasoner::CanAgentNoticeEgo(
    const RobotStateSnapshot& robot_state_snapshot,
    const math::geometry::PolygonWithCache2d& agent_original_contour,
    const math::geometry::Point2d& agent_original_center,
    double agent_original_heading,
    const planner::pb::TrajectoryPose& agent_pose,
    const math::geometry::PolylineCurve2d& refer_path_curve,
    const math::PiecewiseLinearFunction& last_speed_profile_x_at_t) const {
  // TODO(Harry): UT & moving this function to a common utils.
  const double t_in_sec = math::Ms2Sec(agent_pose.timestamp());
  const double odom = last_speed_profile_x_at_t(
      math::Clamp(t_in_sec, last_speed_profile_x_at_t.start_x(),
                  last_speed_profile_x_at_t.end_x()));
  const auto& [point, direction] =
      refer_path_curve.GetInterpPointAndDeriv(odom);
  const math::geometry::Polygon2d ego_after_moving(
      math::geometry::MovePointsToNewCenterAndHeading(
          robot_state_snapshot.bounding_box().CornerPoints(),
          robot_state_snapshot.rear_axle_position(), point,
          robot_state_snapshot.heading(),
          math::geometry::FromVector2DToHeading(direction)));
  const math::geometry::Polygon2d agent_after_moving(
      math::geometry::MovePointsToNewCenterAndHeading(
          agent_original_contour.points(), agent_original_center,
          {agent_pose.x_pos(), agent_pose.y_pos()}, agent_original_heading,
          agent_pose.heading()));
  const math::geometry::OrientedBox2d agent_bounding_box(agent_after_moving,
                                                         agent_pose.heading());

  const math::geometry::Point2d front_left_to_right_unit =
      math::geometry::GetUnitDirection(agent_bounding_box.FrontLeftPoint(),
                                       agent_bounding_box.FrontRightPoint());
  const math::geometry::Polygon2d agent_fov_polygon = {
      agent_bounding_box.FrontLeftPoint(), agent_bounding_box.FrontRightPoint(),
      math::geometry::Add(agent_bounding_box.FrontRightPoint(),
                          math::geometry::Multiply(
                              math::geometry::RotateVector2D(
                                  front_left_to_right_unit, kAgentFovAngle),
                              kAgentFovDistance)),
      math::geometry::Add(
          agent_bounding_box.FrontLeftPoint(),
          math::geometry::Multiply(
              math::geometry::RotateVector2D(front_left_to_right_unit,
                                             M_PI - kAgentFovAngle),
              kAgentFovDistance))};
  if (FLAGS_planning_enable_static_agent_path_reasoner_debug) {
    LOG(ERROR) << DUMP_TO_STREAM(agent_fov_polygon) << "\n"
               << DUMP_TO_STREAM(ego_after_moving) << "\n"
               << DUMP_TO_STREAM(agent_after_moving);
  }
  const double intersection_area =
      math::geometry::IntersectionArea(agent_fov_polygon, ego_after_moving);
  return intersection_area / robot_state_snapshot.bounding_box().Area() >
         kIntersectionAreaRatioThresholdToNoticeEgo;
}

bool StaticAgentPathReasoner::IsFirstXLaneNudgeObject(
    const TypedObjectId& typed_id) const {
  if (scenario_identify_result_.scenario_type !=
      pb::ScenarioRecognition::XLANE_NUDGE) {
    return false;
  }

  DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
  DCHECK(scenario_identify_result_.xlane_nudge_scene.value().type !=
         pb::XLaneNudgeSceneType::kCrossBoth);
  const std::vector<TypedObjectId>& inlane_nudge_objects =
      scenario_identify_result_.xlane_nudge_scene.value().type ==
              pb::XLaneNudgeSceneType::kCrossLeft
          ? scenario_identify_result_.inlane_nudge_objects_info
                .pass_left_object_ids
          : scenario_identify_result_.inlane_nudge_objects_info
                .pass_right_object_ids;
  if (inlane_nudge_objects.empty()) {
    return false;
  }

  // Inlane_nudge_objects is currently unsorted because the order is not
  // crucial. We simply aim to bind the repulsion with one object in the stuck
  // region and ensure that the region's repulsion is not added twice.
  return typed_id == inlane_nudge_objects.front();
}

std::vector<pb::TrajectoryPose>
StaticAgentPathReasoner::GetSmoothedTrajectoryPoses(
    const std::vector<pb::TrajectoryPose>& last_traj,
    const std::vector<pb::TrajectoryPose>& current_traj,
    const math::geometry::Point2d& agent_center, int64_t timestamp) const {
  if (last_traj.empty()) {
    return current_traj;
  }
  if (!current_traj.empty() &&
      current_traj.back().odom() > last_traj.back().odom()) {
    return current_traj;
  }
  double smooth_ratio = kSmoothRatioWhenMoreConservative;

  std::vector<pb::TrajectoryPose> smoothed_traj;
  smoothed_traj.reserve(current_traj.size());
  auto [last_path, last_profile] =
      physics::FromTrajectoryPosesToPathAndProfile(last_traj);
  if (last_profile.empty() || last_path.empty()) {
    return current_traj;
  }
  auto last_profile_func = speed::FromSpeedProfileToPiecewiseLinearFunction(
      last_profile, /*time_offset=*/0.0,
      /*arclength_offset=*/0.0, math::pb::kAllow);
  auto emplace_new_pose =
      [&](const pb::TrajectoryPose& current_pose,
          const math::geometry::PolylineCurve2d& last_path) {
        pb::TrajectoryPose new_pose;
        new_pose.CopyFrom(current_pose);
        double t = math::Ms2Sec(current_pose.timestamp());
        double last_odom = last_profile_func(t);
        auto last_point = last_path.GetInterp(last_odom);
        new_pose.set_x_pos(smooth_ratio * last_point.x() +
                           (1.0 - smooth_ratio) * current_pose.x_pos());
        new_pose.set_y_pos(smooth_ratio * last_point.y() +
                           (1.0 - smooth_ratio) * current_pose.y_pos());
        if (!smoothed_traj.empty()) {
          new_pose.set_odom(
              smoothed_traj.back().odom() +
              math::geometry::Distance(
                  math::geometry::Point2d(smoothed_traj.back().x_pos(),
                                          smoothed_traj.back().y_pos()),
                  math::geometry::Point2d(new_pose.x_pos(), new_pose.y_pos())));
        }
        smoothed_traj.push_back(std::move(new_pose));
      };
  if (current_traj.empty()) {
    // Handle current is static.
    pb::TrajectoryPose pose_start;
    pose_start.set_x_pos(agent_center.x());
    pose_start.set_y_pos(agent_center.y());
    pose_start.set_timestamp(timestamp);
    pose_start.set_odom(0.0);
    pb::TrajectoryPose pose_end;
    pose_end.CopyFrom(pose_start);
    pose_end.set_timestamp(pose_start.timestamp() + math::Sec2Ms(8.0));
    for (const auto& current_pose : {pose_start, pose_end}) {
      emplace_new_pose(current_pose, last_path);
    }
  } else {
    for (const auto& current_pose : current_traj) {
      emplace_new_pose(current_pose, last_path);
    }
  }
  return smoothed_traj;
}

}  // namespace path
}  // namespace planner
