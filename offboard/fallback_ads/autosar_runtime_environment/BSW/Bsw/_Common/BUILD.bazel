package(default_visibility = ["//visibility:public"])

cc_library(
    name = "compiler_cfg",
    hdrs = ["Compiler_Cfg.h"],
    copts = ["-x c-header"],
    include_prefix = "BSW/Bsw/_Common",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:os_compiler_cfg",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_compiler_cfg",
    ],
)

cc_library(
    name = "compiler",
    hdrs = ["Compiler.h"],
    copts = ["-x c-header"],
    include_prefix = "BSW/Bsw/_Common",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/BSW/Bsw/_Common:compiler_cfg",
    ],
)

cc_library(
    name = "platform_types",
    hdrs = ["Platform_Types.h"],
    copts = ["-x c-header"],
    include_prefix = "BSW/Bsw/_Common",
)

cc_library(
    name = "std_types",
    hdrs = ["Std_Types.h"],
    copts = ["-x c-header"],
    include_prefix = "BSW/Bsw/_Common",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/BSW/Bsw/_Common:compiler",
        "//offboard/fallback_ads/autosar_runtime_environment/BSW/Bsw/_Common:platform_types",
    ],
)
