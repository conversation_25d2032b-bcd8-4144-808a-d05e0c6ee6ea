package(default_visibility = ["//visibility:public"])

cc_library(
    name = "ops_warning_utility",
    srcs = [
        "ops_warning_utility.cpp",
    ],
    hdrs = [
        "ops_warning_utility.h",
    ],
    include_prefix = "planner/ops_warning",
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/math",
        "//onboard/common/stats:voy_stats_client",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/behavior:directive",
        "//onboard/planner/behavior/reasoners/util",
        "//onboard/planner/ops_warning/st_agent",
        "//onboard/planner/ops_warning/st_agent:st_agent_utility",
        "//onboard/planner/world_model",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "ops_warning",
    srcs = [
        "ops_warning.cpp",
    ],
    hdrs = [
        "ops_warning.h",
    ],
    include_prefix = "planner/ops_warning",
    deps = [
        ":ops_warning_utility",
        "//onboard/common/base:voy_base",
        "//onboard/common/latency",
        "//onboard/common/math",
        "//onboard/common/node:node_lib",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/decoupled_maneuvers/predicted_trajectory_wrapper",
        "//onboard/planner/world_model",
        "//onboard/planner/world_model:robot_state",
        "//protobuf_cpp:protos_cpp",
    ],
)
