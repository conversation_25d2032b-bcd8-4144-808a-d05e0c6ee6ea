#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ORDER_SERVICE_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ORDER_SERVICE_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "order_protos/order_service.pb.h"

namespace remote_assist {

class OrderServiceObserver : public IDataObserver {
 public:
  using OrderServiceCallback = std::function<void(
      std::shared_ptr<order::pb::OrderService>, DataChannel)>;

  explicit OrderServiceObserver(OrderServiceCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  OrderServiceCallback order_service_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ORDER_SERVICE_OBSERVER_H_
