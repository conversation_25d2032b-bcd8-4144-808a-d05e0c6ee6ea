#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/frame_analyzer_util.h"

#include <gtest/gtest.h>

#include "planner/path/reasoning/agent_intention/semantic_context/test/test_utility.h"

namespace planner {
namespace path {

class BaseFrameAnalyzerTest : public ::testing::Test {
 protected:
  void SetUp() override {
    // Set lane sequence geometry.
    lane_sequence_geometry_.nominal_path =
        planner::test::ReadLine("LINESTRING(0.0 0.0,10.0 0.0)");
    lane_sequence_geometry_.left_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 1.0,10.0 1.0)");
    lane_sequence_geometry_.right_lane_boundary =
        planner::test::ReadLine("LINESTRING(0.0 -1.0,10.0 -1.0)");
    lane_sequence_geometry_.left_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(0.0 3.0,3.0 3.0)"),
        planner::test::ReadLine("LINESTRING(5.0 3.0,8.0 3.0)")};
    lane_sequence_geometry_.right_hard_boundary_lines = {
        planner::test::ReadLine("LINESTRING(1.0 -3.0,4.0 -3.0)"),
        planner::test::ReadLine("LINESTRING(6.0 -3.0,9.0 -3.0)")};
    lane_sequence_geometry_.lane_sequence_contour =
        lane_selection::GetLaneBoundaryBorder(
            lane_sequence_geometry_.left_lane_boundary,
            lane_sequence_geometry_.right_lane_boundary,
            lane_sequence_geometry_.nominal_path);

    planning_horizon_range_ = {
        lane_sequence_geometry_.nominal_path.GetStartPoint().x(),
        lane_sequence_geometry_.nominal_path.GetEndPoint().x()};

    // Set ego param.
    ego_param_.arclength_m = 2.0;
    ego_param_.width_m = 1.0;
    ego_param_.front_bumper_arclength = 3.0;
    ego_param_.rear_axle_to_rear_bumper_m = 0.5;
    ego_param_.rear_axle_to_front_bumper_m = 1.0;
  }

  EgoInLaneParams ego_param_;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  math::Range1d planning_horizon_range_;

  AgentInLaneStatesMap agent_state_map_;
  std::vector<ConstructionZoneInLaneState> construction_zones_state_;

  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map_;
};

/*              _
---------------|_|----------------   left boundary 1.0m
               cz
    ___     __     ___     ___
~~~|___|~~~|__|~~~|___|~~~|___|~~~   reference_line 0.0m
   obj1    ego    obj2    obj3

---------------------------------- right boundary -1.0m
*/
TEST_F(BaseFrameAnalyzerTest, DummyTest) {
  agent_state_map_.clear();
  construction_zones_state_.clear();
  planner::test::ConstructConstructionZoneInLaneState(
      ego_param_, lane_sequence_geometry_, /*cz_id=*/1,
      /*cz_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(3.0, 0.8),
                                 math::geometry::Point2d(5.0, 0.8),
                                 math::geometry::Point2d(5.0, 1.2),
                                 math::geometry::Point2d(3.0, 1.2)}),
      &construction_zones_state_);
  planner::test::ConstructDynamicAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/1,
      /*x0=*/0.0, /*y0=*/0.0,
      /*width=*/1.0, /*speed=*/1.0, agent_state_map_);
  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/2,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(4.0, -0.5),
                                 math::geometry::Point2d(5.0, -0.5),
                                 math::geometry::Point2d(5.0, 0.5),
                                 math::geometry::Point2d(4.0, 0.5)}),
      agent_state_map_);
  planner::test::ConstructStaticAgentInLaneState(
      lane_sequence_geometry_, /*obj_id=*/3,
      /*obj_polygon=*/
      math::geometry::Polygon2d({math::geometry::Point2d(7.0, -0.5),
                                 math::geometry::Point2d(8.0, -0.5),
                                 math::geometry::Point2d(8.0, 0.5),
                                 math::geometry::Point2d(7.0, 0.5)}),
      agent_state_map_);

  object_reasoning_info_map_ = test::GenerateObjectReasoningInfoMap(
      agent_state_map_, construction_zones_state_,
      lane_sequence_geometry_.nominal_path);

  const pb::FrameAnalysis::FrameType frame_type = pb::FrameAnalysis::IN_LANE;
  std::vector<TypedObjectId> object_ids =
      GetInterestedObjectsInFrame(object_reasoning_info_map_, frame_type);
  const FrameLaneBlockageInfo lane_blockage_info =
      CalculateLaneBlockageInfoInFrame(
          /*lane_blockages=*/std::map<int64_t, LaneBlockage>{},
          object_reasoning_info_map_,
          GetPotentialStuckObjectsInFrame(object_reasoning_info_map_,
                                          object_ids, frame_type));
  const FrameTrafficFlowInfo traffic_flow_info =
      CalculateTrafficFlowInfoInFrame(
          ego_param_, object_reasoning_info_map_,
          GetTrafficFlowAgentsInFrame(
              object_reasoning_info_map_, object_ids,
              /*lane_blockage_info=*/FrameLaneBlockageInfo{}, frame_type),
          frame_type);

  EXPECT_EQ(object_ids.size(), 4);
  EXPECT_EQ(lane_blockage_info.potential_stuck_object_ids.size(), 4);
  EXPECT_TRUE(lane_blockage_info.blockage_object_ids.empty());
  EXPECT_EQ(traffic_flow_info.traffic_flow_ids.size(), 3);
  EXPECT_EQ(traffic_flow_info.traffic_flow_queue.size(), 3);
  EXPECT_EQ(traffic_flow_info.slow_moving_vehicle_ids.size(), 3);
  constexpr double tolerance_m = 1e-3;
  // dynamic agent's speed = 1.0m/s;
  EXPECT_NEAR(traffic_flow_info.max_speed, 1.0, tolerance_m);
  EXPECT_NEAR(traffic_flow_info.avg_speed, 1.0 / 3.0, tolerance_m);
  EXPECT_NEAR(traffic_flow_info.min_speed, 0.0, tolerance_m);
  // distance between obj2 and obj3 = 2.0m
  EXPECT_NEAR(traffic_flow_info.min_diff_arclength_between_agents, 2.0,
              tolerance_m);
  // dynamic agent's length = 2.0m, arclength_m - rear_axle_to_rear_bumper_m
  // = 1.5 ego_distance_to_nearby_vehicle = 1.5 - (0.0 + 2.0 / 2.0) = 0.5
  EXPECT_NEAR(traffic_flow_info.ego_distance_to_nearby_vehicle, 0.5,
              tolerance_m);

  // Generate the object reasoning info map from the object occupancy states.
  // Construct a static object occupancy state.
  ObjectOccupancyStateMap object_occupancy_state_map;
  FLAGS_planning_enable_object_occupancy_state = true;
  // Construct a dynamic object occupancy state.
  prediction::pb::Agent dynamic_agent_proto;
  dynamic_agent_proto.mutable_tracked_object()->CopyFrom(
      planner::test::ConstructTrackedObjectFromDimension(
          /*obj_id=*/10,
          /*x0=*/0.0, /*y0=*/0.0,
          /*width=*/1.2, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
          /*heading=*/0.0));
  prediction::pb::PredictedTrajectory proto_traj;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers_1;
  predicted_trajectory_wrappers_1.push_back(
      planner::test::ConstructPredictedTrajectoryWrapperForTest(
          dynamic_agent_proto.tracked_object(), proto_traj));
  PlannerObject dynamic_planner_object(
      dynamic_agent_proto, /*timestamp=*/0,
      TrafficParticipantPose(/*timestamp=*/0,
                             dynamic_agent_proto.tracked_object()));
  dynamic_planner_object.set_is_primary_stationary(false);
  ObjectOccupancyState dynamic_object_state = GenerateObjectOccupancyState(
      dynamic_planner_object, /*object_information=*/{},
      predicted_trajectory_wrappers_1, lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);
  // Construct 2 static objects.
  prediction::pb::Agent static_agent_1_proto;
  auto& static_tracked_object_1 =
      *static_agent_1_proto.mutable_tracked_object();
  static_tracked_object_1.set_id(2);
  static_tracked_object_1.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  static_tracked_object_1.set_object_type(voy::perception::ObjectType::VEHICLE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(4.0, -0.5),
                               math::geometry::Point2d(5.0, -0.5),
                               math::geometry::Point2d(5.0, 0.5),
                               math::geometry::Point2d(4.0, 0.5)}),
                          *static_tracked_object_1.mutable_contour());
  PlannerObject static_planner_object_1(
      static_agent_1_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_1));
  static_planner_object_1.set_is_primary_stationary(true);
  prediction::pb::PredictedTrajectory static_predicted_trajectory;
  static_predicted_trajectory.set_id(1);
  static_predicted_trajectory.add_traj_poses();
  std::vector<PredictedTrajectoryWrapper>
      static_predicted_trajectory_wrappers_1;
  static_predicted_trajectory_wrappers_1.emplace_back(
      voy::TrackedObject(), static_predicted_trajectory,
      /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state_1 = GenerateObjectOccupancyState(
      static_planner_object_1, /*object_information=*/{},
      static_predicted_trajectory_wrappers_1,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);

  prediction::pb::Agent static_agent_2_proto;
  auto& static_tracked_object_2 =
      *static_agent_2_proto.mutable_tracked_object();
  static_tracked_object_2.set_id(3);
  static_tracked_object_2.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  static_tracked_object_2.set_object_type(voy::perception::ObjectType::VEHICLE);
  math::geometry::Convert(/*obj_polygon=*/
                          math::geometry::Polygon2d(
                              {math::geometry::Point2d(7.0, -0.5),
                               math::geometry::Point2d(8.0, -0.5),
                               math::geometry::Point2d(8.0, 0.5),
                               math::geometry::Point2d(7.0, 0.5)}),
                          *static_tracked_object_2.mutable_contour());
  PlannerObject static_planner_object_2(
      static_agent_2_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, static_tracked_object_2));
  static_planner_object_2.set_is_primary_stationary(true);
  static_predicted_trajectory.set_id(1);
  std::vector<PredictedTrajectoryWrapper>
      static_predicted_trajectory_wrappers_2;
  static_predicted_trajectory_wrappers_2.emplace_back(
      voy::TrackedObject(), static_predicted_trajectory,
      /*is_primary_trajectory=*/true);
  ObjectOccupancyState static_object_state_2 = GenerateObjectOccupancyState(
      static_planner_object_2, /*object_information=*/{},
      static_predicted_trajectory_wrappers_2,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.left_lane_boundary,
      lane_sequence_geometry_.right_lane_boundary, ego_param_,
      pb::MotionMode::FORWARD);

  object_occupancy_state_map.emplace(
      static_object_state_1.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_1));
  object_occupancy_state_map.emplace(
      static_object_state_2.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(static_object_state_2));
  object_occupancy_state_map.emplace(
      dynamic_object_state.planner_object().tracked_object().id(),
      std::make_unique<ObjectOccupancyState>(dynamic_object_state));

  auto object_reasoning_info_map_2 =
      test::GenerateObjectReasoningInfoMapFromOccupancyState(
          object_occupancy_state_map, construction_zones_state_,
          lane_sequence_geometry_.nominal_path, ego_param_);
  object_ids.clear();
  object_ids =
      GetInterestedObjectsInFrame(object_reasoning_info_map_2, frame_type);
  const FrameLaneBlockageInfo lane_blockage_info_2 =
      CalculateLaneBlockageInfoInFrame(
          /*lane_blockages=*/std::map<int64_t, LaneBlockage>{},
          object_reasoning_info_map_2,
          GetPotentialStuckObjectsInFrame(object_reasoning_info_map_2,
                                          object_ids, frame_type));
  const FrameTrafficFlowInfo traffic_flow_info_2 =
      CalculateTrafficFlowInfoInFrame(
          ego_param_, object_reasoning_info_map_2,
          GetTrafficFlowAgentsInFrame(
              object_reasoning_info_map_2, object_ids,
              /*lane_blockage_info=*/FrameLaneBlockageInfo{}, frame_type),
          frame_type);

  EXPECT_EQ(object_ids.size(), 4);
  EXPECT_EQ(lane_blockage_info_2.potential_stuck_object_ids.size(), 4);
  EXPECT_TRUE(lane_blockage_info_2.blockage_object_ids.empty());
  EXPECT_EQ(traffic_flow_info_2.traffic_flow_ids.size(), 3);
  EXPECT_EQ(traffic_flow_info_2.traffic_flow_queue.size(), 3);
  EXPECT_EQ(traffic_flow_info_2.slow_moving_vehicle_ids.size(), 3);
  // dynamic agent's speed = 1.0m/s;
  EXPECT_NEAR(traffic_flow_info_2.max_speed, 1.0, tolerance_m);
  EXPECT_NEAR(traffic_flow_info_2.avg_speed, 1.0 / 3.0, tolerance_m);
  EXPECT_NEAR(traffic_flow_info_2.min_speed, 0.0, tolerance_m);
  // distance between obj2 and obj3 = 2.0m
  EXPECT_NEAR(traffic_flow_info_2.min_diff_arclength_between_agents, 2.0,
              tolerance_m);
  // dynamic agent's length = 2.0m, arclength_m - rear_axle_to_rear_bumper_m
  // = 1.5 ego_distance_to_nearby_vehicle = 1.5 - (0.0 + 2.0 / 2.0) = 0.5
  EXPECT_NEAR(traffic_flow_info_2.ego_distance_to_nearby_vehicle, 0.5,
              tolerance_m);
}

}  // namespace path
}  // namespace planner
