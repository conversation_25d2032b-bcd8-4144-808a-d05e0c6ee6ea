{"component_name": "routing", "events": [{"name": "SameOriginAndDestImmediatePullover", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRoutingFailedForFirstRideQuery", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "GlobalRoutingFailedForChangingMissionRideQuery", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}, {"name": "GlobalRoutingFailedForReplanQuery", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalNewRouteLanesIsRejected", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalLoopRoute", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalThroughConstructionZonesRoute", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalDistToDestinationDeviation", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalTimeToDestinationDeviation", "contact": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalETAAndEDA", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteFollowCloudRoute", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteRejectInfeasibleCloudRoute", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OrdSrvPlannerCompletionMismatchMissionId", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OrdSrvPlannerCompletionMismatchMissionIdWithImmPullover", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OrdSrvOrderCompletionMismatchMissionId", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverTriggered", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverCancelled", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverSuccess", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverFail", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstraintFailedToReadFromCloudForRouting", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "AbnormalOrdSwitchToDropOffWhenStaticAndPullOver", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CompleteRouteForStaticSameOriginAndDestRoute", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CompleteRouteForSameOriginAndDestImmediatePullover", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalHasNegativeCostInAstarSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GlobalHasEndLessWhileLoopWhenConstructPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GlobalFindALoopInAstarSearchResult", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GlobalConsecutiveReplanFailedForRoutingGraphNotConnected", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false}, {"name": "GlobalReSearchSucceedAfterUnknownReasonFailure", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarHasEmptyOpenQueueInWarmStart", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarResearchSucceedAfterFailedWarmStart", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarResearchSucceedAfterLoopInAstarResult", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarResearchSucceedAfterIllegalBackTrackPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalLanePointLaneHasIllegalFirstLanePoint", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalLanePointLaneHasWrongLastKeyPointIndex", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalLanePointLaneHasIllegalLanePointIndex", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRecommendReplanHasZeroTargetRoads", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRecommendReplanHasNoConnectableSourceRoads", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarHasValidRecommendReplan", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarFindNullPtrWhenConstructPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarFindNullPtrWhenExecuteSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarResearchSucceedForNullPtrInAstarSearch", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarResearchSucceedForNullPtrInBackTrackPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalAstarExceedsTimeThreshold", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverTerminateByRouting", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalPathDivergeWithRegionalPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalPathConvergeWithRegionalPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteStatusManagerHasIllegalRejectingPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "QueryManagerHasNotPreparedForReplan", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RouteSolutionHasNoDestinationLaneCandidateInPath", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ImmediatePullOverCancelledTime", "contact": "wa<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteCloudRouteBeyondHdmap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteOriginNotOnCloudRoute", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteCloudRouteNotInboardCostMap", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRejectCloudForCZ", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRejectCloudForHardConstrained", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteHasNoLaneCandidatesForOrigin", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "CloudRouteLastLaneNotInDestLaneCandidates", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRecommendReplanSucceed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRecommendReplanFailed", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstraintFailedToCacheEdgeFilter", "contact": "cojimawang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "ConstraintFailedToHitEdgeFilterCache", "contact": "cojimawang", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRoutingSucceedForRideQuery", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRoutingSucceedForReplanQuery", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRoutingFailedWithUnknownReason", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "OrderRoutingStateTrack", "contact": "cyrushe", "throttle_interval_ms": 0, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteChanged", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteHasContinuousLC", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "GlobalRouteRejectContinuousLC", "contact": "huiluo", "throttle_interval_ms": 10000, "enable_onboard": true, "disable": false}, {"name": "RoutingSuccessForNewRoute", "contact": "<PERSON><PERSON><PERSON><PERSON>", "throttle_interval_ms": 1000, "enable_onboard": true, "disable": false, "enable_sim_analysis": true}]}