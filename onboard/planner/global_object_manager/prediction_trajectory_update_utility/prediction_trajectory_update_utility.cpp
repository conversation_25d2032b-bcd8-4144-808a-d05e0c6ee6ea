#include "planner/global_object_manager/prediction_trajectory_update_utility/prediction_trajectory_update_utility.h"

#include <algorithm>
#include <unordered_set>
#include <utility>

#include <tbb/parallel_for.h>
#include <tbb/task_arena.h>

#include "planner/constants.h"
#include "planner/utility/seed/planning_seed.h"
#include "prediction/common/utility/prediction_utility.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace utility {
namespace {
// Limit the number of TBB threads to avoid excessive competition for kernel
// time slices.
constexpr int kNumThreadsForPlannerObject = 10;

// Run parallel if agent count exceeds this threshold for better performance
constexpr int kPlannerObjectsParallelProcessThreshold = 32;

constexpr int64_t kMaxInterestedPredictionTimeBufferInMSec = 500;

// Logic ported from:
// onboard/planner/world_model/world_model_utility.cpp:GetMaxInterestedPredictionTimestamp
// Originally used for constructing PredictedTrajectory in TrackedObject.
// Preserved here to support computation of
// PredictedTrajectoryWrapper::legacy_predicted_poses_.
int64_t GetMaxInterestedPredictionTimestamp(int64_t snapshot_timestamp) {
  return snapshot_timestamp + constants::kStitchingImmutableTimeInMSec +
         constants::kTrajectoryHorizonInMSec +
         kMaxInterestedPredictionTimeBufferInMSec;
}

// Helper to Check if Agent is Stationary
bool IsAgentStationary(const prediction::pb::Agent& agent) {
  return agent.stationary_intention().stationary_intention_type() ==
         prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE;
}

// Helper to Determine if a Trajectory is CBP-generated
bool IsCBPTrajectory(const prediction::pb::PredictedTrajectory& trajectory) {
  return trajectory.trajectory_generator_type() ==
         prediction::pb::TrajectoryGeneratorType::VEH_LANE_CHANGE_CBP_GENERATOR;
}

// Processes a Single Agent's Conditional Predicted Trajectories
void ProcessConditionalPredictions(
    const prediction::pb::Agent& agent,
    TbbConditionalPredictionTrajectoryMap* object_conditional_prediction_map) {
  TRACE_EVENT_SCOPE(planner, ProcessConditionalPredictions);

  DCHECK(object_conditional_prediction_map != nullptr);

  const auto& cbp_target_trajectories = agent.conditional_trajectories()
                                            .yield_cbp_trajs_pair()
                                            .target_trajectories();
  if (IsAgentStationary(agent) || cbp_target_trajectories.empty()) {
    return;
  }

  auto& new_conditional_bps =
      (*object_conditional_prediction_map)[agent.tracked_object().id()];

  for (const auto& cbp : cbp_target_trajectories) {
    // Only populate predictions with type
    // VEH_LANE_CHANGE_CBP_GENERATOR, because others will have
    // trajectory_id = 0.
    if (IsCBPTrajectory(cbp)) {
      new_conditional_bps.yield_cbp_trajs_pair.target_trajectories.emplace_back(
          agent.tracked_object(), cbp, /*is_primary=*/false,
          agent.stationary_intention());
    }
  }
  new_conditional_bps.yield_cbp_trajs_pair.ego_trajectories.emplace_back(
      agent.conditional_trajectories().yield_cbp_trajs_pair().ego_trajectory());
}

// Processes a Single Agent's Predicted Trajectories
void ProcessPredictedTrajectories(
    const prediction::pb::Agent& agent,
    int64_t max_interested_prediction_timestamp,
    TbbPredictionTrajectoryMap* object_prediction_map) {
  TRACE_EVENT_SCOPE(planner, ProcessPredictedTrajectories);

  DCHECK(object_prediction_map != nullptr);

  auto& new_bps = (*object_prediction_map)[agent.tracked_object().id()];
  const int primary_index =
      prediction::utility::GetPrimaryTrajectoryIndex(agent);
  const auto& primary_trajectory = agent.predicted_trajectories(primary_index);

  for (const auto& trajectory : agent.predicted_trajectories()) {
    // DecoupledManeuver currently only considers predictions from
    // multi-output, excluding agents with the 'STATIONARY_TO_MOVE'
    // intention.
    if (!trajectory.is_multi_output_trajectory() &&
        agent.stationary_intention().stationary_intention_type() !=
            prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE) {
      continue;
    }

    bool is_primary = (primary_trajectory.id() == trajectory.id());
    new_bps.emplace_back(agent, trajectory, is_primary,
                         max_interested_prediction_timestamp);
  }
}

// Update Prediction Data a Single Agent (Combines Both Prediction Processing
// Steps)
void UpdatePredictionDataForAgent(
    const prediction::pb::Agent& agent,
    int64_t max_interested_prediction_timestamp,
    TbbConditionalPredictionTrajectoryMap* object_conditional_prediction_map,
    TbbPredictionTrajectoryMap* object_prediction_map) {
  TRACE_EVENT_SCOPE(planner, UpdatePredictionDataForAgent);

  DCHECK(object_conditional_prediction_map != nullptr);
  DCHECK(object_prediction_map != nullptr);

  ProcessConditionalPredictions(agent, object_conditional_prediction_map);
  ProcessPredictedTrajectories(agent, max_interested_prediction_timestamp,
                               object_prediction_map);
}

}  // namespace

void UpdatePredictionTrajectoryMap(
    int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
    const std::unordered_set<TypedObjectId>& irrelevant_object_ids,
    TbbPredictionTrajectoryMap* object_prediction_map,
    TbbConditionalPredictionTrajectoryMap* object_conditional_prediction_map,
    av_comm::ThreadPool* destruction_background_pool) {
  TRACE_EVENT_SCOPE(planner, UpdatePredictionTrajectoryMap);

  DCHECK(object_prediction_map != nullptr);
  DCHECK(object_conditional_prediction_map != nullptr);
  DCHECK(destruction_background_pool != nullptr);

  // Clearing object_prediction_map may be expensive due to memory held.
  // To avoid blocking the main thread, we move its content into a background
  // task and clear it asynchronously via destruction_background_pool.
  destruction_background_pool->PushTask(
      [moved_map = std::move(*object_prediction_map)]() mutable {
        TRACE_EVENT_SCOPE(planner,
                          AsyncDestruction_ClearPredictionTrajectoryMap);
        moved_map.clear();
      });

  const auto agent_list_size = static_cast<int>(agent_list.agent_list_size());

  const auto max_interested_prediction_timestamp =
      GetMaxInterestedPredictionTimestamp(snapshot_timestamp);

  if (agent_list_size > kPlannerObjectsParallelProcessThreshold) {
    // Execute parallel processing using TBB's task arena
    tbb::task_arena limited_arena(kNumThreadsForPlannerObject);
    limited_arena.execute([&,
                           thread_owner = ThreadOwnerMarker::CurrentOwner()] {
      tbb::parallel_for(0, agent_list_size, [&, thread_owner](int idx) {
        PLANNER_MARK_THREAD_OWNER(thread_owner);

        const auto& agent = agent_list.agent_list(idx);
        TypedObjectId typed_object_id(agent.tracked_object().id(),
                                      pb::ObjectSourceType::kTrackedObject);

        if (irrelevant_object_ids.find(typed_object_id) !=
            irrelevant_object_ids.end()) {
          return;
        }

        UpdatePredictionDataForAgent(agent, max_interested_prediction_timestamp,
                                     object_conditional_prediction_map,
                                     object_prediction_map);
      });
    });
  } else {
    for (const auto& agent : agent_list.agent_list()) {
      TypedObjectId typed_object_id(agent.tracked_object().id(),
                                    pb::ObjectSourceType::kTrackedObject);
      // Skip if object is irrelevant
      if (irrelevant_object_ids.find(typed_object_id) !=
          irrelevant_object_ids.end()) {
        continue;
      }

      UpdatePredictionDataForAgent(agent, max_interested_prediction_timestamp,
                                   object_conditional_prediction_map,
                                   object_prediction_map);
    }
  }
}
}  // namespace utility
}  // namespace planner
