#include "planner/world_model/traffic_signal_reasoner/traffic_signal_reasoner.h"

#include <algorithm>
#include <functional>

#include "planner/planning_gflags.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"

namespace planner {

namespace {
// Returns true if a lane is possibly variable lane's successor.
bool IsLanePossibleVariableLaneSuccessor(const pnc_map::Lane& lane) {
  return lane.type() == hdmap::Lane::LaneType::Lane_LaneType_VIRTUAL &&
         lane.predecessors().size() == 1;
}

// Returns true if a lane is variable lane or variable lane's successor.
bool IsLaneOrItsPredecessorVariableLane(const pnc_map::Lane& lane,
                                        int64_t variable_lane_id) {
  // If a lane does not have predecessor, we only check lane itself.
  if (lane.predecessors().empty()) {
    return lane.id() == variable_lane_id;
  }

  return IsLanePossibleVariableLaneSuccessor(lane)
             ? lane.predecessors()[0]->id() == variable_lane_id
             : lane.id() == variable_lane_id;
}

bool IsAnyCurrentLaneOrItsPredecessorVariableLane(
    const std::vector<const pnc_map::Lane*>& current_lanes,
    int64_t variable_lane_id) {
  return std::any_of(current_lanes.begin(), current_lanes.end(),
                     [variable_lane_id](const pnc_map::Lane* current_lane) {
                       return (IsLaneOrItsPredecessorVariableLane(
                           *current_lane, variable_lane_id));
                     });
}

bool AreAllCurrentLaneNotRelatedToVariableLane(
    const std::vector<const pnc_map::Lane*>& current_lanes,
    int64_t variable_lane_id) {
  return std::all_of(current_lanes.begin(), current_lanes.end(),
                     [variable_lane_id](const pnc_map::Lane* current_lane) {
                       return (!IsLaneOrItsPredecessorVariableLane(
                           *current_lane, variable_lane_id));
                     });
}

}  // namespace

// Cases:
// 1) no predicted signal controlled variable lane:
// 1.1. return true is lane or lane's predecessor is map labelled signal
// controlled variable lane.
// 1.2. return false for other cases.
// 2) has predicted signal controlled variable lane:
// 2.1. if lane is predicted as variable lane, return true.
// 2.2. lane is not variable lane or variable lane's successor, return true.
// 2.3. lane is variable lane's successor, return false if it's restricted;
// otherwise, return true.
bool TrafficSignalReasoner::IsSignalControlledVariableAndVirtualLaneDrivable(
    const pnc_map::Lane& lane) const {
  // If no predicted signal controlled variable lane, and |lane| or |lane|'s
  // predecessor is labelled as signal controlled variable, we consider it as
  // not drivable. Otherwise consider as drivable.
  if (signal_controlled_variable_lane_info_with_seed_message_.empty()) {
    if (lane.routing_vertex().is_signal_controlled_variable_lane()) {
      return false;
    }

    if (IsLanePossibleVariableLaneSuccessor(lane)) {
      const pnc_map::Lane* predecessor = lane.predecessors()[0];
      if (predecessor->routing_vertex().is_signal_controlled_variable_lane()) {
        return false;
      }
    }

    return true;
  }

  // For other virtual lanes:
  // 1. If its predecessor is not in map, return true.
  // 2. If its predecessor is in map, and it is one of restricted virtual lanes,
  // return false. Otherwise, return false.
  if (!IsLanePossibleVariableLaneSuccessor(lane)) {
    return true;
  }

  const auto& predecessor_iter =
      signal_controlled_variable_lane_info_with_seed_message_.find(
          lane.predecessors()[0]->id());
  if (predecessor_iter ==
      signal_controlled_variable_lane_info_with_seed_message_.end()) {
    return true;
  }

  // If predicted signal controlled variable lane does not output any valid sign
  // type, we treat its successor as not drivable as well.
  if (predecessor_iter->second.predicted_traffic_light_type ==
      voy::TrafficLight::UNKNOWN_TYPE) {
    return false;
  }

  const bool is_restricted =
      std::any_of(predecessor_iter->second.restricted_virtual_lanes.begin(),
                  predecessor_iter->second.restricted_virtual_lanes.end(),
                  [&lane](const pnc_map::Lane* restrict_lane) {
                    return restrict_lane->id() == lane.id();
                  });
  return !is_restricted;
}

void TrafficSignalReasoner::UpdatePassableAndRestrictedVirtualLanes(
    const pnc_map::Lane& variable_lane,
    VariableLaneInfo* variable_lane_info) const {
  const std::vector<hdmap::Lane::Turn>& drivable_turn_types =
      variable_lane_info->drivable_turn_types;
  if (drivable_turn_types.empty()) {
    return;
  }
  variable_lane_info->passable_virtual_lanes.reserve(
      variable_lane.successors().size());
  variable_lane_info->restricted_virtual_lanes.reserve(
      variable_lane.successors().size());

  for (const pnc_map::Lane* successor : variable_lane.successors()) {
    // Variable lane's affect lane is always its in-junction successor. So if a
    // successor is not in junction or not uturn, we will skip checking its
    // passable feature.
    if (!(successor->IsInJunction() ||
          successor->routing_vertex().is_uturn())) {
      continue;
    }

    const bool is_turn_drivable =
        std::any_of(drivable_turn_types.begin(), drivable_turn_types.end(),
                    [successor](hdmap::Lane::Turn passable_turn_type) {
                      return successor->turn() == passable_turn_type;
                    });

    if (is_turn_drivable) {
      variable_lane_info->passable_virtual_lanes.push_back(successor);
    } else {
      variable_lane_info->restricted_virtual_lanes.push_back(successor);
    }
  }
}

std::vector<hdmap::Lane::Turn>
TrafficSignalReasoner::GetVariableLanePassableTurnTypes(
    voy::TrafficLight::Type arrow_type) const {
  switch (arrow_type) {
    case voy::TrafficLight::UNKNOWN_TYPE:
      return {};
    case voy::TrafficLight::TIDAL_FLOW_LEFT:
      return {hdmap::Lane_Turn_LEFT};
    case voy::TrafficLight::TIDAL_FLOW_STRAIGHT:
      return {hdmap::Lane_Turn_STRAIGHT};
    case voy::TrafficLight::TIDAL_FLOW_RIGHT:
      return {hdmap::Lane_Turn_RIGHT};
    case voy::TrafficLight::TIDAL_FLOW_LEFT_AND_STRAIGHT:
      return {hdmap::Lane_Turn_LEFT, hdmap::Lane_Turn_STRAIGHT};
    case voy::TrafficLight::TIDAL_FLOW_RIGHT_AND_STRAIGHT:
      return {hdmap::Lane_Turn_RIGHT, hdmap::Lane_Turn_STRAIGHT};
    case voy::TrafficLight::TIDAL_FLOW_NO_ENTRY:
      return {};
    case voy::TrafficLight::TIDAL_FLOW_U_TURN:
      return {hdmap::Lane_Turn_U_TURN};
    case voy::TrafficLight::TIDAL_FLOW_LEFT_AND_U_TURN:
      return {hdmap::Lane_Turn_U_TURN, hdmap::Lane_Turn_LEFT};
    default:
      CHECK(false) << "no valid traffic light type: " << arrow_type;
      return {};
  }
}

void TrafficSignalReasoner::UpdatePerceptionSignalControlledVariableLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const ::google::protobuf::RepeatedPtrField<::voy::TidalFlow>& tidal_flows) {
  signal_controlled_variable_lane_info_.clear();

  if (tidal_flows.empty()) {
    return;
  }

  std::vector<const ::voy::TidalFlow*> tidal_flow_vec;
  tidal_flow_vec.reserve(tidal_flows.size());
  for (const auto& tidal_flow : tidal_flows) {
    tidal_flow_vec.push_back(&tidal_flow);
  }

  std::sort(
      tidal_flow_vec.begin(), tidal_flow_vec.end(),
      [](const ::voy::TidalFlow* lhs, const ::voy::TidalFlow* rhs) {
        if (lhs->dist_to_traffic_sign() >= 0 &&
            rhs->dist_to_traffic_sign() >= 0) {
          return lhs->dist_to_traffic_sign() < rhs->dist_to_traffic_sign();
        } else if (lhs->dist_to_traffic_sign() < 0 &&
                   rhs->dist_to_traffic_sign() < 0) {
          return lhs->dist_to_traffic_sign() > rhs->dist_to_traffic_sign();
        } else {
          // For a positive dist and a negative dist, the positive one
          // should be in front of the negative one.
          return lhs->dist_to_traffic_sign() > rhs->dist_to_traffic_sign();
        }
      });

  for (const ::voy::TidalFlow* tidal_flow : tidal_flow_vec) {
    for (const auto& tidal_flow_sign : tidal_flow->tidal_flow_signs()) {
      if (tidal_flow_sign.lane_type() == voy::TidalFlowSign::NORMAL_LANE) {
        continue;
      }

      const int64_t variable_lane_id = tidal_flow_sign.associated_lane_id();
      auto iter = signal_controlled_variable_lane_info_.find(variable_lane_id);

      // If variable lane has already added to map, we should ignore update map
      // info for following conditions:
      // 1. Mew sign type is unknown(no valid detection result).
      // 2. New sign type is identical to local sign type.
      // 3. New sign type is different from local one, local one is not unknown,
      // and new sign type's distance to ego is larger than local one.
      if (iter != signal_controlled_variable_lane_info_.end()) {
        const auto& local_tidal_flow_sign = iter->second;
        if (tidal_flow_sign.sign_type() == voy::TrafficLight::UNKNOWN_TYPE ||
            local_tidal_flow_sign.predicted_traffic_light_type ==
                tidal_flow_sign.sign_type()) {
          continue;
        }

        if ((local_tidal_flow_sign.predicted_traffic_light_type !=
             voy::TrafficLight::UNKNOWN_TYPE) &&
            (local_tidal_flow_sign.predicted_traffic_light_type !=
             tidal_flow_sign.sign_type())) {
          continue;
        }
      }

      // Cases we should create a new variable info and insert to map.
      // 1. A variable lane has not added to map yet.
      // 2. Local sign type is unknown.
      // 3. New sign type and local's are different, but new sign type's
      // distance to ego is shorter than local. (We assume closer the distance,
      // higher perception's accuracy.)
      const pnc_map::Lane* variable_lane =
          joint_pnc_map_service.GetLaneSequence({variable_lane_id})[0];
      VariableLaneInfo variable_lane_info;
      variable_lane_info.variable_lane = variable_lane;
      variable_lane_info.lane_type = tidal_flow_sign.lane_type();
      variable_lane_info.confidence_score = tidal_flow_sign.score();
      variable_lane_info.predicted_traffic_light_type =
          tidal_flow_sign.sign_type();
      variable_lane_info.ego_to_traffic_sign_dist =
          tidal_flow->dist_to_traffic_sign();
      variable_lane_info.is_from_seed = false;
      variable_lane_info.drivable_turn_types =
          GetVariableLanePassableTurnTypes(tidal_flow_sign.sign_type());
      UpdatePassableAndRestrictedVirtualLanes(*variable_lane,
                                              &variable_lane_info);

      signal_controlled_variable_lane_info_[variable_lane_id] =
          variable_lane_info;
    }
  }
}

void TrafficSignalReasoner::
    MergeAndGetPerceptionAndSeedSignalControlledVariableLanes(
        const pnc_map::JointPncMapService& joint_pnc_map_service,
        const pb::TrafficSignalReasonerSeed* previous_seed) {
  signal_controlled_variable_lane_info_with_seed_message_.clear();

  // 1) Firstly copy perception's variable lane info map.
  signal_controlled_variable_lane_info_with_seed_message_ =
      signal_controlled_variable_lane_info_;

  // 2) Iterate seed to overwrite perception's result accordingly.
  // 2.1) If seed is empty, directly return.
  if (previous_seed->variable_lane_info_seed().empty()) {
    return;
  }

  for (const auto& seed_lane : previous_seed->variable_lane_info_seed()) {
    const pb::SignalControlledVariableLaneInfo& seed_lane_info =
        seed_lane.signal_controlled_variable_lane_info();
    auto iter = signal_controlled_variable_lane_info_with_seed_message_.find(
        seed_lane_info.lane_id());
    if (iter != signal_controlled_variable_lane_info_with_seed_message_.end()) {
      // 2.2) If a lane has valid value from perception, not overwrite.
      if (iter->second.HasValidResult()) {
        continue;
      }

      // 2.3) If map has labelled this lane but perception does not recognize
      // valid sign direction (unknown case), and seed has valid result, then
      // use seed to overwrite it.
      if (iter->second.HasUnknownResult() &&
          seed_lane_info.predicted_traffic_light_type() !=
              voy::TrafficLight::UNKNOWN_TYPE) {
        LOG(INFO) << "For variable lane " << seed_lane_info.lane_id()
                  << ", perception's result is unknown, so use seed's result.";
        VariableLaneInfo info;
        info.ConstructVariableLaneInfoFromPb(joint_pnc_map_service,
                                             seed_lane_info);
        signal_controlled_variable_lane_info_with_seed_message_
            [seed_lane_info.lane_id()] = info;
        continue;
      }
    } else {
      // 2.4) If no result from perception, we will overwrite it if ego is on
      // variable lane or its successor. (Currently when ego enters junction,
      // perception will not publish detection result)
      if (IsAnyCurrentLaneOrItsPredecessorVariableLane(
              current_lanes_, seed_lane_info.lane_id())) {
        LOG(INFO)
            << "For variable lane " << seed_lane_info.lane_id()
            << ", perception does not publish result, so use seed's result.";

        VariableLaneInfo info;
        info.ConstructVariableLaneInfoFromPb(joint_pnc_map_service,
                                             seed_lane_info);
        signal_controlled_variable_lane_info_with_seed_message_
            [seed_lane_info.lane_id()] = info;
      }
    }
  }
}

pb::TrafficSignalReasonerSeed
TrafficSignalReasoner::UpdateTrafficSignalReasonerSeed(
    const pb::TrafficSignalReasonerSeed* previous_seed) {
  pb::TrafficSignalReasonerSeed new_seed;
  // If no data in seed yet, just sync from perception.
  if (previous_seed->variable_lane_info_seed().empty()) {
    // If no data from perception, current scene is not related to signal
    // controlled variable lane, just return.
    if (signal_controlled_variable_lane_info_.empty()) {
      return new_seed;
    }

    for (const auto& [variable_lane_id, variable_lane_info] :
         signal_controlled_variable_lane_info_) {
      variable_lane_info.PopulateVariableLaneInfoSeed(
          new_seed.add_variable_lane_info_seed());
    }
    return new_seed;
  }

  // Seed already held valid data.
  for (const auto& seed_lane : previous_seed->variable_lane_info_seed()) {
    const pb::SignalControlledVariableLaneInfo& seed_lane_info =
        seed_lane.signal_controlled_variable_lane_info();
    auto iter =
        signal_controlled_variable_lane_info_.find(seed_lane_info.lane_id());
    // For a lane in seed but not in perception's data. If current lane is
    // variable lane or its successor, we should keep seed's data; otherwise
    // keep cleanup this lane from seed.
    if (iter == signal_controlled_variable_lane_info_.end()) {
      if (AreAllCurrentLaneNotRelatedToVariableLane(current_lanes_,
                                                    seed_lane_info.lane_id())) {
        continue;
      }
      auto* signal_controlled_variable_lane_info_seed =
          new_seed.add_variable_lane_info_seed();
      signal_controlled_variable_lane_info_seed
          ->mutable_signal_controlled_variable_lane_info()
          ->CopyFrom(seed_lane_info);
      signal_controlled_variable_lane_info_seed
          ->set_update_from_perception_result(false);
      continue;
    }

    // If can find seed's lane in perception data and it's valid, just replace
    // with current cycle's result.
    if (iter->second.HasValidResultAndCorrespondingPassableLane()) {
      iter->second.PopulateVariableLaneInfoSeed(
          new_seed.add_variable_lane_info_seed());
      continue;
    }

    // If data is not valid, we should keep seed's data.
    if (iter->second.HasUnknownResult()) {
      auto* signal_controlled_variable_lane_info_seed =
          new_seed.add_variable_lane_info_seed();
      signal_controlled_variable_lane_info_seed
          ->mutable_signal_controlled_variable_lane_info()
          ->CopyFrom(seed_lane_info);
      signal_controlled_variable_lane_info_seed
          ->set_update_from_perception_result(false);
    }
  }

  return new_seed;
}

void TrafficSignalReasoner::UpdateSignalControlledVariableLaneDebug(
    pb::SignalControlledVariableLaneDebug* mutable_variable_lane_debug) {
  // Update variable lane info debug for perception published variable lane.
  for (auto iter = signal_controlled_variable_lane_info_.begin();
       iter != signal_controlled_variable_lane_info_.end(); iter++) {
    pb::SignalControlledVariableLaneInfoDebug*
        signal_controlled_variable_lane_info_debug =
            mutable_variable_lane_debug->add_perception_variable_lane_infos();
    iter->second.PopulateVariableLaneInfoDebug(
        /*is_from_perception=*/true,
        signal_controlled_variable_lane_info_debug);
  }

  // Update variable lane info debug for perception and seed merged variable
  // lane, which will be used by drivable lane reasoner.
  for (auto iter =
           signal_controlled_variable_lane_info_with_seed_message_.begin();
       iter != signal_controlled_variable_lane_info_with_seed_message_.end();
       iter++) {
    pb::SignalControlledVariableLaneInfoDebug*
        signal_controlled_variable_lane_info_debug =
            mutable_variable_lane_debug->add_merged_variable_lane_infos();
    iter->second.PopulateVariableLaneInfoDebug(
        /*is_from_perception=*/false,
        signal_controlled_variable_lane_info_debug);
  }
}

}  // namespace planner
