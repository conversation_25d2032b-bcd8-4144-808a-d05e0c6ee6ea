#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LIGHT_ASSIST_RESPONSE_REPLY_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LIGHT_ASSIST_RESPONSE_REPLY_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class LightAssistResponseReplyObserver : public IDataObserver {
 public:
  using LightAssistResponseReplyCallback = std::function<void(
      std::shared_ptr<teleassist::pb::LightAssistResponseReply>, DataChannel)>;

  explicit LightAssistResponseReplyObserver(
      LightAssistResponseReplyCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  LightAssistResponseReplyCallback light_assist_response_reply_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_LIGHT_ASSIST_RESPONSE_REPLY_OBSERVER_H_
