#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_COMMON_INFOS_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_COMMON_INFOS_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class VehicleCommonInfosObserver : public IDataObserver {
 public:
  using VehicleCommonInfosCallback = std::function<void(
      std::shared_ptr<teleassist::pb::VehicleCommonInfos>, DataChannel)>;

  explicit VehicleCommonInfosObserver(VehicleCommonInfosCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  VehicleCommonInfosCallback vehicle_common_infos_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_COMMON_INFOS_OBSERVER_H_
