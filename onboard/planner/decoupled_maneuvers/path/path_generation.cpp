#include "planner/decoupled_maneuvers/path/path_generation.h"

#include <algorithm>
#include <functional>
#include <limits>
#include <memory>
#include <numeric>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include <tbb/parallel_for.h>

#include "base/wall_clock_elapsed_timer.h"
#include "latency/pipeline_id.h"
#include "math/range.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_type_check_utility.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_external_utils.h"
#include "planner/decoupled_maneuvers/geometric_guidance/geometric_guidance_generator_util.h"
#include "planner/decoupled_maneuvers/geometric_guidance/lane_change_geometric_guidance_generator.h"
#include "planner/decoupled_maneuvers/geometric_guidance/lane_keep_geometric_guidance_generator.h"
#include "planner/decoupled_maneuvers/geometric_guidance/open_space_geometric_guidance_generator.h"
#include "planner/decoupled_maneuvers/geometric_guidance/pull_over_geometric_guidance_generator.h"
#include "planner/decoupled_maneuvers/geometric_guidance/reverse_driving_geometric_guidance_generator.h"
#include "planner/decoupled_maneuvers/last_trajectory/last_trajectory_util.h"
#include "planner/decoupled_maneuvers/path/motion_engine/path_motion_engine.h"
#include "planner/decoupled_maneuvers/path/reasoning/path_generation_util.h"
#include "planner/decoupled_maneuvers/path/reasoning/path_reasoning.h"
#include "planner/decoupled_maneuvers/path/reasoning/trajectory_guider_reasoning.h"
#include "planner/global_object_manager/global_object_manager.h"
#include "planner/machine_learning/ml_planner_meta.h"
#include "planner/path/path_solver/path_input.h"
#include "planner/path/reasoning/agent_intention/intention_generator_util.h"
#include "planner/planning_gflags.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner_protos/speed_seed.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace {

using IntentionDiversities = std::vector<PathGenerationOption>;
using GuidanceDiversities = std::vector<IntentionDiversities>;

// Add 1 Backup Path + 1 Last Path + 1 ML Backup Path + 1 Narrow Oncoming Path
// as extra path options.
constexpr int kExtraNonMLPathOptionsNumber = 4;
// Add at most 6 ML Path as extra path options.
constexpr int kMLPathOptionsNumber = 6;
// The minmum ml path that can be used to generate a ml path option.
constexpr double kMinmumMLPathLength = 15.0;
// The minmum average distance between ml path and reference path that we think
// they have huge diff in meters.
constexpr double kMLPathReferencePathHugeDistance = 5.0;
// The minimum stationary agent percentage allowed to enable ml path.
constexpr double kMinStationaryAgentPercentageToEnableMLPath = 0.5;
// The maximum number of total nominal path options allowed to enable the ml
// path.
constexpr int kMaxNumPathOptionsToEnableMLPath = 3;
// The minimum probability allowed to enable the ml path candidate.
constexpr double kMinMLPathProbability = 0.09;
// The default path length for BACKUP_STOP_TRAJECTORY
constexpr double kBackupReferencePathLength = 80.0;
// The distance between each point on ml/reference path, which we will use these
// points to calculate the max path difference.
constexpr double kDistanceOfEachPoint = 1.0;
// The min distance from ego to the upl turn for enabling ml path 2.0.
constexpr double kMinUplDistanceFromEgoMeters = 30.0;
// When check if there is short merge in front of Ego, we only consider merge
// lane that is shorter than this length.
constexpr double kShortMergeLaneMaxLengthMeters = 10.0;
// When check if there is short merge in front of Ego, we only consider merge
// lane that is closer than this length.
constexpr double kMinMergeDistanceFromEgoMeters = 40.0;
// We will drop the ml trajectory if it is has a collision with hard boundary
// before this seconds.
constexpr double kMLTrajectoryHardBoundaryCollisionPositionInSec = 5.0;
// The number of ml trajectory points .
constexpr int kNumMLTrajectoryPoints = 91;
// The P999 running time for path generation guidance loop.
constexpr double kPathGenerationGuidanceLoopRunningTimeInMs = 28.0;
// The max angle that we want to add a big turn path option in upl lanes.
constexpr double kMaxAngleEnableBigTurn = 60.0;

// Information related to the correpsonding ML planner candidate used for
// planning.
struct MlPathInfo {
  // The polyline extracted from ML trajectory.
  math::geometry::PolylineCurve2d ml_path;
  // Pointer to the corresponding candidate from ML planner output message.
  const pb::CandidatesOutput::Candidate* candidate_ptr;
};

// Checks if the ml trajectory has a intersection with the complete (no trimmed)
// hard boundaries before kMLTrajectoryHardBoundaryCollisionPositionInSec
// seconds.
bool CheckMLTrajectoryHardBoundaryIntersection(
    const MlPathInfo& ml_path_info,
    const std::vector<math::geometry::PolylineCurve2d>& boundary_lines) {
  const auto& ml_candidate = *ml_path_info.candidate_ptr;
  DCHECK_EQ(ml_candidate.states().size(), kNumMLTrajectoryPoints);
  const size_t point_index =
      static_cast<size_t>(kMLTrajectoryHardBoundaryCollisionPositionInSec /
                          /*time_space=*/0.1);
  DCHECK_LT(point_index, kNumMLTrajectoryPoints);

  // Build the ml trajectory before
  // kMLTrajectoryHardBoundaryCollisionPositionInSec seconds.
  std::vector<math::geometry::Point2d> state_points;
  state_points.reserve(ml_candidate.states().size());
  for (size_t i = 0; i <= point_index; ++i) {
    const math::geometry::Point2d point(ml_candidate.states(i).x_global(),
                                        ml_candidate.states(i).y_global());
    if (state_points.empty() ||
        !math::NearZero(math::geometry::Length(
            math::geometry::Subtract(point, state_points.back())))) {
      state_points.push_back(point);
    }
  }
  if (state_points.size() < 2) {
    return false;
  }
  const math::geometry::PolylineCurve2d ml_path(state_points);

  // Check the intersection.
  for (const auto& line : boundary_lines) {
    if (math::geometry::Intersects(ml_path, line)) {
      rt_event::PostRtEvent<
          rt_event::planner::DropMLPathDueToHardBoundaryCollision>();
      return true;
    }
  }
  return false;
}

bool NeedToDropMLPath(const MlPathInfo& ml_path_info,
                      const GeometricGuidanceResult& geometric_constraint,
                      const double ml_path_probability,
                      const bool is_ml_backup_path,
                      const int ml_candidate_index,
                      pb::LaneSequenceInfoAndIntentPlanDebug& debug) {
  const math::geometry::PolylineCurve2d& ml_path = ml_path_info.ml_path;
  if (ml_path.GetTotalArcLength() < kMinmumMLPathLength) {
    if (!is_ml_backup_path) {
      debug.add_filter_ml_candidate_reason(
          absl::StrCat("filter_ml_candidate_", ml_candidate_index,
                       "_because_length_shorter_than_", kMinmumMLPathLength));
    }
    return true;
  }

  // Drops ml path with low probability.
  if (ml_path_probability < kMinMLPathProbability) {
    if (!is_ml_backup_path) {
      debug.add_filter_ml_candidate_reason(absl::StrCat(
          "filter_ml_candidate_", ml_candidate_index,
          "_because_model_probability_smaller_than_", kMinMLPathProbability));
    }
    return true;
  }

  // If we skip path search, we don't want the ml trajectory has a collision
  // with the hard boundaries.
  if (!is_ml_backup_path &&
      FLAGS_planning_enable_drop_ml_path_due_to_hb_collision) {
    if (CheckMLTrajectoryHardBoundaryIntersection(
            ml_path_info, geometric_constraint.lane_sequence_geometry
                              .complete_hard_boundary_lines)) {
      debug.add_filter_ml_candidate_reason(absl::StrCat(
          "filter_ml_candidate_", ml_candidate_index,
          "_because_of_hard_boundary_collision_before_",
          kMLTrajectoryHardBoundaryCollisionPositionInSec, "_seconds"));
      return true;
    }
  }

  // Drops ml path with a huge distance to the route, excludes the ml backup
  // path. Compares the distance between ml path and reference path by each
  // equidistant point.
  if (!is_ml_backup_path) {
    std::vector<double> ml_arc_lengths;
    std::vector<double> ref_arc_lengths;
    const double ego_arc_length =
        geometric_constraint.lane_sequence_info->robot_in_lane_param()
            .arclength_m;
    const double ref_after_ego_length =
        geometric_constraint.reference_path.GetTotalArcLength() -
        ego_arc_length;
    const double ml_path_length = ml_path.GetTotalArcLength();
    const double shorter_length =
        std::min(ref_after_ego_length, ml_path_length);
    for (double i = 0.0; i < shorter_length; i += kDistanceOfEachPoint) {
      ml_arc_lengths.push_back(i);
      ref_arc_lengths.push_back(i + ego_arc_length);
    }
    std::vector<math::geometry::Point2d> ml_interp_points =
        adv_geom::PathCurve2d(ml_path).GetInterpPoint(ml_arc_lengths);
    std::vector<math::geometry::Point2d> ref_interp_points =
        geometric_constraint.reference_path.GetInterpPoint(ref_arc_lengths);
    DCHECK_EQ(ml_interp_points.size(), ref_interp_points.size());

    for (int i = 0; i < static_cast<int>(ml_interp_points.size()); ++i) {
      if (math::geometry::Distance(ml_interp_points[i], ref_interp_points[i]) >
          kMLPathReferencePathHugeDistance) {
        rt_event::PostRtEvent<rt_event::planner::MLPathReferencePathHugeDiff>();
        debug.add_filter_ml_candidate_reason(
            absl::StrCat("filter_ml_candidate_", ml_candidate_index,
                         "_because_route_distance_larger_than_",
                         kMLPathReferencePathHugeDistance));
        return true;
      }
    }
  }

  return false;
}

// Computes the stationary agent percentage among all nudge agents.
double ComputeNudgeStationaryAgentPercentage(
    const WorldModel& world_model,
    const std::vector<PathGenerationOption>& path_generation_options) {
  std::unordered_set<ObjectId> agents_to_nudge;
  std::unordered_set<ObjectId> stationary_agents_to_nudge;
  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map =
      world_model.planner_object_map();
  // Iterate through path generation options to identify agents to nudge.
  for (const auto& path_generation_option : path_generation_options) {
    const std::vector<AgentConstraint>& agent_constraints =
        path_generation_option.path_reasoning_result.agent_constraints;
    for (const auto& constraint : agent_constraints) {
      const ObjectId object_id = constraint.object_id;
      // Check if the agent is not already marked for nudging.
      if (agents_to_nudge.find(object_id) == agents_to_nudge.end()) {
        agents_to_nudge.insert(object_id);
        // Check if the agent is stationary.
        if (const auto it = planner_object_map.find(object_id);
            it != planner_object_map.end() && it->second.is_stationary()) {
          stationary_agents_to_nudge.insert(object_id);
        }
      }
    }
  }
  double nudge_static_agent_percentage = 0.0;
  if (agents_to_nudge.size() > 0) {
    nudge_static_agent_percentage =
        static_cast<double>(stationary_agents_to_nudge.size()) /
        agents_to_nudge.size();
  }
  return nudge_static_agent_percentage;
}

bool IsEgoNearUTurn(const GeometricGuidanceResult& geometric_constraint) {
  if (geometric_constraint.lane_sequence_info == nullptr) {
    return false;
  }

  const math::Range1d ego_arclength_range_with_padding = {
      geometric_constraint.lane_sequence_info->robot_in_lane_param()
              .ego_range.start_pos -
          15.0,
      geometric_constraint.lane_sequence_info->robot_in_lane_param()
              .ego_range.end_pos +
          15.0};
  if (!math::IsValidRange(ego_arclength_range_with_padding)) {
    return false;
  }

  for (const auto& uturn_range : geometric_constraint.lane_sequence_geometry
                                     .arclength_ranges_of_uturn_lane) {
    if (math::IsValidRange(uturn_range) &&
        math::AreRangesOverlapping(uturn_range,
                                   ego_arclength_range_with_padding)) {
      return true;
    }
  }

  return false;
}

bool ShouldEnableAddingMLPath(
    const WorldModel& world_model, const pb::MotionMode motion_mode,
    const pull_over::PullOverInfo& pull_over_info,
    const std::vector<PathGenerationOption>& options,
    const GeometricGuidanceResult& geometric_constraint,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug) {
  if (!FLAGS_planning_add_ml_path) {
    debug.set_disable_ml_path_reason("ml_path_flag_off");
    return false;
  }

  const bool is_ego_near_uturn = IsEgoNearUTurn(geometric_constraint);

  // Enable ml path 2.0 in all scope except BACKWARD, PIPO, MRC, LC, FreeWay,
  // XLN, U_TURN.
  if (FLAGS_planning_enable_ml_path_2_0) {
    // Disbale in backward mode.
    if (motion_mode == pb::MotionMode::BACKWARD) {
      debug.set_disable_ml_path_reason("ml_path_in_backward_mode");
      return false;
    }

    // Disable in U-turn.
    if (is_ego_near_uturn) {
      return false;
    }

    // Disable in pull in / pull out.
    if (geometric_constraint.lane_sequence_info->lane_sequence_result()
            .IsPullOutJumpOut() ||
        pull_over_info.status().should_trigger_pull_over()) {
      debug.set_disable_ml_path_reason("ml_path_in_pull_in_pull_out");
      return false;
    }

    // Disable in MRC.
    if (world_model.mrc_request_ptr() != nullptr &&
        world_model.mrc_request_ptr()->mrm_type() !=
            mrc::pb::MrmType::UNDEFINED) {
      debug.set_disable_ml_path_reason("ml_path_in_pull_in_mrc");
      return false;
    }

    // Only enable in lane keep.
    if (geometric_constraint.behavior_type != pb::BehaviorType::LANE_KEEP) {
      debug.set_disable_ml_path_reason("ml_path_not_in_lane_keep");
      return false;
    }

    // Disable in high speed (free way).
    if (FLAGS_planning_enable_driving_on_freeway) {
      debug.set_disable_ml_path_reason("ml_path_in_freeway");
      return false;
    }

    // Disable in xlane nuge.
    const bool have_xlane_left_homotopy = std::any_of(
        options.begin(), options.end(), [](const auto& path_option) {
          return path_option.path_reasoning_result.intention_result
                     .homotopy() == pb::IntentionResult::XLANE_PASS_FROM_LEFT;
        });
    const bool have_xlane_right_homotopy = std::any_of(
        options.begin(), options.end(), [](const auto& path_option) {
          return path_option.path_reasoning_result.intention_result
                     .homotopy() == pb::IntentionResult::XLANE_PASS_FROM_RIGHT;
        });
    if (have_xlane_left_homotopy &&
        !geometric_constraint.semantic_context_result
             ->traffic_rule_reasoning_info.is_left_side_lane_safe()) {
      debug.set_disable_ml_path_reason(
          "ml_path_in_left_side_unsafe_xlane_nudge");
      return false;
    }
    if (have_xlane_right_homotopy &&
        !geometric_constraint.semantic_context_result
             ->traffic_rule_reasoning_info.is_right_side_lane_safe()) {
      debug.set_disable_ml_path_reason(
          "ml_path_in_right_side_unsafe_xlane_nudge");
      return false;
    }

    return true;
  }

  // ONLY FOR TESTING.
  if (FLAGS_planning_disable_ml_path_filter) {
    DCHECK(FLAGS_planning_enable_forcing_selecting_ml_path);
    return true;
  }

  // We only enable ml path 2.0 in LANE_KEEP.
  if (geometric_constraint.behavior_type != pb::BehaviorType::LANE_KEEP &&
      (!FLAGS_planning_enable_forcing_selecting_ml_path_with_cross_lane ||
       geometric_constraint.behavior_type != pb::BehaviorType::CROSS_LANE)) {
    return false;
  }

  // Disable ml path 2.0 in pull over progress.
  if (pull_over_info.status().should_trigger_pull_over()) {
    return false;
  }

  // ML 2.0 for short merge.
  const std::function<bool(
      const speed::traffic_rules::MergeForkLaneInLaneSequence&)>
      is_valid_merge_lane =
          [](const speed::traffic_rules::MergeForkLaneInLaneSequence&
                 merge_lane) {
            const double merge_lane_length =
                merge_lane.overlap_ra_range.end_pos -
                merge_lane.overlap_ra_range.start_pos;
            return merge_lane_length >= 0.0 &&
                   merge_lane_length <= kShortMergeLaneMaxLengthMeters;
          };
  // TODO(mingshenchen): now this will also be true when merge is behind Ego,
  // may need revisit this logic.
  const bool is_ego_near_merge =
      geometric_constraint.lane_sequence_info != nullptr &&
      IsEgoNearMerge(
          geometric_constraint.lane_sequence_info->lane_sequence_iterator(),
          geometric_constraint.lane_sequence_info->lane_sequence_geometry()
              .nominal_path,
          geometric_constraint.lane_sequence_info->robot_in_lane_param(),
          is_valid_merge_lane, kMinMergeDistanceFromEgoMeters);
  // Check if Ego is near uturn.
  if (is_ego_near_merge && !is_ego_near_uturn) {
    // Always populate RT event even if flag is off, so we can gather examples.
    rt_event::PostRtEvent<rt_event::planner::GenerateMLPathNearShortMerge>();
    if (FLAGS_planning_enable_add_ml_path_near_short_merge) {
      return true;
    }
  }

  // Enable ml path 2.0 in upl turn with all scope.
  if (FLAGS_planning_add_ml_path_in_upl_turn &&
      path::GetUplLane(world_model, previous_iter_seed,
                       *geometric_constraint.lane_sequence_info,
                       geometric_constraint.reference_path,
                       kMinUplDistanceFromEgoMeters) != nullptr) {
    rt_event::PostRtEvent<rt_event::planner::GenerateMLPathInUpl>();
    return true;
  }

  // Disable ml path 2.0 if we have no ignore homotopy.
  const bool have_ignore_homotopy =
      std::any_of(options.begin(), options.end(), [](const auto& path_option) {
        return path_option.path_reasoning_result.intention_result.homotopy() ==
               pb::IntentionResult::IGNORE_ALL;
      });
  if (FLAGS_planning_enable_drop_ml_path_when_no_ignore_homotopy &&
      !have_ignore_homotopy) {
    return false;
  }

  // Disable ml path 2.0 when high discomfort.
  const double last_cycle_discomfort = previous_iter_seed.speed_seed()
                                           .speed_solver_result()
                                           .selected_discomfort();
  if (last_cycle_discomfort > constants::kMaxDiscomfortAllowUsingMLPath) {
    return false;
  }

  // Disable ml path 2.0 when complicated traffic condition.
  if (ComputeNudgeStationaryAgentPercentage(world_model, options) <
          kMinStationaryAgentPercentageToEnableMLPath ||
      static_cast<int>(options.size()) > kMaxNumPathOptionsToEnableMLPath) {
    return false;
  }

  // Disable ml path 2.0 when,
  // [1] There is an open door vehicle.
  for (const auto& [object_id, object_reasoning_info] :
       geometric_constraint.semantic_context_result
           ->object_reasoning_info_map) {
    if (object_reasoning_info.blocking_state_data()
            .reasoning_info.is_open_door_vehicle()) {
      return false;
    }
  }
  // [2] Doing xlane nudge with unsafe side vehicles.
  const bool have_xlane_left_homotopy =
      std::any_of(options.begin(), options.end(), [](const auto& path_option) {
        return path_option.path_reasoning_result.intention_result.homotopy() ==
               pb::IntentionResult::XLANE_PASS_FROM_LEFT;
      });
  const bool have_xlane_right_homotopy =
      std::any_of(options.begin(), options.end(), [](const auto& path_option) {
        return path_option.path_reasoning_result.intention_result.homotopy() ==
               pb::IntentionResult::XLANE_PASS_FROM_RIGHT;
      });
  if (have_xlane_left_homotopy &&
      !geometric_constraint.semantic_context_result->traffic_rule_reasoning_info
           .is_left_side_lane_safe()) {
    return false;
  }
  if (have_xlane_right_homotopy &&
      !geometric_constraint.semantic_context_result->traffic_rule_reasoning_info
           .is_right_side_lane_safe()) {
    return false;
  }

  return true;
}

bool ShouldEnableAddingMLTrajectory(
    const bool ml_trajectory_init_state_has_diverged_from_planner,
    const pb::BehaviorType behavior_type,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug) {
  if (!FLAGS_planning_enable_ml_trajectory) {
    debug.set_disable_ml_trajectory_reason("ml_trajectory_flag_off");
    return false;
  }

  // We drop ml trajectory if its init state has diverged from planner.
  if (ml_trajectory_init_state_has_diverged_from_planner) {
    debug.set_disable_ml_trajectory_reason(
        "ml_trajectory_init_state_has_diverged_from_planner");
    return false;
  }

  // We enable ml path 3.0 in both LANE_KEEP & CROSS_LANE
  if (behavior_type != pb::BehaviorType::LANE_KEEP &&
      behavior_type != pb::BehaviorType::CROSS_LANE) {
    debug.set_disable_ml_trajectory_reason(
        "ml_trajectory_not_in_lane_keep_or_lane_change");
    return false;
  }

  return true;
}

// Adds last path option to options vector if needed and supported. During the
// generation process, a reference PathGenerationOption is passed in to provide
// the necessary information for us to know if last path is needed.
void MaybeAddLastPathOption(
    const pb::BehaviorType behavior_type,
    const std::optional<path::PathCandidate>& reusable_last_path,
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const PathGenerationOption& reference_generation_option,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug,
    std::vector<PathGenerationOption>* options) {
  if (!FLAGS_planning_enable_last_path ||
      !CanReuseLastPath(world_model, previous_iter_seed,
                        reference_generation_option.geometric_constraint
                            .lane_sequence_result->lane_sequence,
                        behavior_type)) {
    return;
  }
  auto* intention_plan_debug_ptr = debug.add_single_intention_plan_debug();
  path::PathReasoningResult path_reasoning_result;
  // TODO(Harry): Check whether we should add last path reasoner seed to
  // path_reasoning_result here.
  path_reasoning_result.intention_result = previous_iter_seed.last_intent();
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *reference_generation_option.geometric_constraint.constraint_manager);

  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(
          reference_generation_option.geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = &debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = pb::LAST_TRAJECTORY,
      .need_to_copy_extra_ml_trajectory = false,
      .optional_last_path = reusable_last_path,
      .behavior_relevant_objects_info =
          reference_generation_option.behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt};
  option.geometric_constraint.path_reasoning_seed =
      previous_iter_seed.path_reasoning_seed();
  // Add agent yield intention seed.
  option.geometric_constraint.path_reasoning_seed.clear_object_intention_seed();
  option.geometric_constraint.path_reasoning_seed
      .mutable_object_intention_seed()
      ->mutable_yield_object_map()
      ->insert(
          reference_generation_option.geometric_constraint.path_reasoning_seed
              .object_intention_seed()
              .yield_object_map()
              .begin(),
          reference_generation_option.geometric_constraint.path_reasoning_seed
              .object_intention_seed()
              .yield_object_map()
              .end());
  // Add narrow meeting seed.
  option.geometric_constraint.path_reasoning_seed.clear_narrow_meeting_seed();
  auto* mutable_narrow_meeting_seed =
      option.geometric_constraint.path_reasoning_seed
          .mutable_narrow_meeting_seed();
  const auto& narrow_meeting_seed =
      reference_generation_option.geometric_constraint.path_reasoning_seed
          .narrow_meeting_seed();
  mutable_narrow_meeting_seed->mutable_oncoming_vehicle_id()->CopyFrom(
      narrow_meeting_seed.oncoming_vehicle_id());
  mutable_narrow_meeting_seed->set_is_narrow_meeting_in_progress(
      narrow_meeting_seed.is_narrow_meeting_in_progress());
  mutable_narrow_meeting_seed->set_latest_trigger_timestamp_ms(
      narrow_meeting_seed.latest_trigger_timestamp_ms());

  options->push_back(std::move(option));
}

// Adds a upl path option to provide a bigger or smaller turn in upl junctions.
void AddUplPathOption(
    const WorldModel& world_model,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const PathGenerationOption& reference_generation_option,
    const pnc_map::Lane* upl_lane, const double enter_bezier_length_rate,
    const double exit_bezeir_length_rate,
    const PathGenerationOption::UplPathAnnotation upl_path_type,
    lateral_clearance::BaseLateralClearanceGenerator*
        lateral_clearance_generator,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug,
    std::vector<PathGenerationOption>* options) {
  std::vector<math::geometry::Point2d> upl_points =
      GenerateAuxiliaryCurveForLeftTurn(upl_lane, enter_bezier_length_rate,
                                        exit_bezeir_length_rate);
  if (upl_points.empty()) {
    return;
  }

  // Only enable the upl big turn if the a <= 50 degrees.
  //  <--- - - -
  //  exit.     |
  //       .    |
  //        .   |
  //         . a|
  //          . ^
  //           .| enter
  //            .
  const auto& start_p = upl_lane->center_line().GetStartPoint();
  const auto& end_p = upl_lane->center_line().GetEndPoint();
  const double heading_start_to_end =
      std::atan2(end_p.y() - start_p.y(), end_p.x() - start_p.x());
  const double junction_angle = std::fabs(math::Radian2Degree(math::AngleDiff(
      heading_start_to_end, upl_lane->GetLaneDirection(start_p))));
  if (junction_angle > kMaxAngleEnableBigTurn) {
    return;
  }

  // Build path option.
  auto* intention_plan_debug_ptr = debug.add_single_intention_plan_debug();
  pb::IntentionResult ignore_all_intention;
  ignore_all_intention.set_homotopy(pb::IntentionResult::IGNORE_ALL);
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result = ignore_all_intention;
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *reference_generation_option.geometric_constraint.constraint_manager);
  path_reasoning_result.path_reasoner_seed =
      reference_generation_option.path_reasoning_result.path_reasoner_seed;

  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(
          reference_generation_option.geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = &debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = pb::NOMINAL_TRAJECTORY,
      .upl_path_type = upl_path_type,
      .need_to_copy_extra_ml_trajectory = false,
      .optional_last_path = std::nullopt,
      .behavior_relevant_objects_info =
          reference_generation_option.behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt,
      .skip_path_search = true};
  *option.path_reasoning_result.intention_result
       .mutable_selected_speed_profile() =
      reference_generation_option.path_reasoning_result.intention_result
          .selected_speed_profile();

  std::vector<math::geometry::Point2d> concat_points;
  // Concat the reference path points.
  for (const auto* lane :
       reference_generation_option.geometric_constraint.lane_sequence_info
           ->lane_sequence_iterator()
           .lane_sequence()) {
    // Add the upl turn's first point as the start point, if the ego is in the
    // upl turn and the lane sequence starts from the upl lane.
    if (concat_points.empty() && lane->id() == upl_lane->id()) {
      concat_points.push_back(
          upl_lane->predecessors()[0]->center_line().points().back());
    }
    if (lane->id() != upl_lane->id()) {
      for (const auto& point : lane->center_line().points()) {
        if (concat_points.empty() ||
            !math::NearZero(math::geometry::Length(
                math::geometry::Subtract(point, concat_points.back())))) {
          concat_points.push_back(point);
        }
      }
    } else {
      for (const auto& point : upl_points) {
        if (concat_points.empty() ||
            !math::NearZero(math::geometry::Length(
                math::geometry::Subtract(point, concat_points.back())))) {
          concat_points.push_back(point);
        }
      }
    }
  }
  option.geometric_constraint.reference_path =
      adv_geom::PathCurve2d(math::geometry::PolylineCurve2d(concat_points));
  const math::ProximityQueryInfo ego_proximity_info =
      option.geometric_constraint.reference_path.GetProximity(
          world_model.robot_state()
              .current_state_snapshot()
              .front_bumper_position(),
          math::pb::UseExtensionFlag::kForbid);
  math::Range1d planning_horizon_range = math::Range1d(
      ego_proximity_info.arc_length,
      ego_proximity_info.arc_length + option.geometric_constraint.path_length);
  planning_horizon_range = GetExtendedPlanningHorizonRange(
      option.geometric_constraint.reference_path, planning_horizon_range,
      kDrivableCorridorExtendBufferInMeter);
  // Update the lateral clearance due to new reference path.
  lateral_clearance::LateralClearanceData lateral_clearance_data;
  DCHECK(lateral_clearance_generator != nullptr);
  lateral_clearance_data = lateral_clearance_generator->Generate(
      option.geometric_constraint.reference_path, planning_horizon_range,
      option.geometric_constraint.lane_sequence_geometry
          .left_hard_boundary_lines,
      option.geometric_constraint.lane_sequence_geometry
          .right_hard_boundary_lines,
      option.geometric_constraint.lane_sequence_geometry
          .middle_hard_boundary_lines,
      required_lateral_gap_info.hard_boundary_required_lat_gap);
  option.path_reasoning_result.homotopic_constraint_manager
      ->SetLateralClearanceData(std::move(lateral_clearance_data));
  options->push_back(std::move(option));
}

// Adds upl path options include bigger/smaller turn in upl junctions.
void MaybeAddUplPathOptions(
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const PathGenerationOption& reference_generation_option,
    lateral_clearance::BaseLateralClearanceGenerator*
        lateral_clearance_generator,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug,
    std::vector<PathGenerationOption>* options) {
  // Get the upl lane.
  auto* upl_lane = path::GetUplLane(
      world_model, previous_iter_seed,
      *reference_generation_option.geometric_constraint.lane_sequence_info,
      reference_generation_option.geometric_constraint.reference_path,
      kMinUplDistanceFromEgoMeters);
  if (upl_lane == nullptr) {
    return;
  }

  if (FLAGS_planning_enable_upl_big_turn_path) {
    AddUplPathOption(world_model, required_lateral_gap_info,
                     reference_generation_option, upl_lane,
                     /*enter_bezier_length_rate=*/0.5,
                     /*exit_bezeir_length_rate=*/0.2,
                     PathGenerationOption::UplPathAnnotation::BIG_TURN,
                     lateral_clearance_generator, debug, options);
  }
  if (FLAGS_planning_enable_upl_small_turn_path) {
    AddUplPathOption(world_model, required_lateral_gap_info,
                     reference_generation_option, upl_lane,
                     /*enter_bezier_length_rate=*/-0.1,
                     /*exit_bezeir_length_rate=*/-0.1,
                     PathGenerationOption::UplPathAnnotation::SMALL_TURN,
                     lateral_clearance_generator, debug, options);
  }
}

// Returns a backup PathGenerationOption (with IGNORE_ALL intent) for the
// corresponding sample PathGenerationOption. Backup option will skip path
// search and provide reference path directly to smoother for path generation.
PathGenerationOption GetBackupPathOption(
    const PathGenerationOption& reference_generation_option,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug) {
  auto* intention_plan_debug_ptr = debug.add_single_intention_plan_debug();
  pb::IntentionResult ignore_all_intention;
  ignore_all_intention.set_homotopy(pb::IntentionResult::IGNORE_ALL);
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result = ignore_all_intention;
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *reference_generation_option.geometric_constraint.constraint_manager);
  path_reasoning_result.path_reasoner_seed =
      reference_generation_option.path_reasoning_result.path_reasoner_seed;

  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(
          reference_generation_option.geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = &debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = pb::BACKUP_TRAJECTORY,
      .need_to_copy_extra_ml_trajectory = false,
      .optional_last_path = std::nullopt,
      .behavior_relevant_objects_info =
          reference_generation_option.behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt,
      .skip_path_search = true};
  return option;
}

// Returns a fallback PathGenerationOption (with IGNORE_ALL intent) which is
// enabled when all others failed.
PathGenerationOption GetFallbackPathOption(
    const WorldModel& world_model,
    const PathGenerationOption& reference_generation_option,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug) {
  auto* intention_plan_debug_ptr = debug.add_single_intention_plan_debug();
  pb::IntentionResult ignore_all_intention;
  ignore_all_intention.set_homotopy(pb::IntentionResult::IGNORE_ALL);
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result = ignore_all_intention;
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *reference_generation_option.geometric_constraint.constraint_manager);

  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(
          reference_generation_option.geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = &debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = pb::BACKUP_STOP_TRAJECTORY,
      .need_to_copy_extra_ml_trajectory = false,
      .optional_last_path = std::nullopt,
      .behavior_relevant_objects_info =
          reference_generation_option.behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt,
      .skip_path_search = true};

  const auto& robot_state = world_model.robot_state().current_state_snapshot();
  std::vector<math::geometry::Point2d> state_points;
  state_points.emplace_back(robot_state.x(), robot_state.y());
  state_points.emplace_back(
      robot_state.x() +
          kBackupReferencePathLength * math::FastCos(robot_state.heading()),
      robot_state.y() +
          kBackupReferencePathLength * math::FastSin(robot_state.heading()));
  option.geometric_constraint.reference_path =
      adv_geom::PathCurve2d(math::geometry::PolylineCurve2d(state_points));
  return option;
}

void AddFodIntentionMapToMLPathOption(
    const PlannerObjectMap& planner_object_map,
    const pb::IntentionResult& reference_intention_result,
    pb::IntentionResult& intention_result) {
  for (const auto& [object_id, object_intention_info] :
       reference_intention_result.fod_intention_map()) {
    if (object_intention_info == pb::IntentionResult::NOT_APPLICABLE) {
      continue;
    }
    const auto planner_object_iter = planner_object_map.find(object_id);
    if (planner_object_iter == planner_object_map.end()) {
      continue;
    }
    const PlannerObject& planner_object = planner_object_iter->second;
    if (planner_object.is_object_drivable()) {
      intention_result.mutable_fod_intention_map()->emplace(
          object_id, pb::IntentionResult::DRIVE_THROUGH);
    } else if (planner_object.is_better_not_drive()) {
      intention_result.mutable_fod_intention_map()->emplace(
          object_id, pb::IntentionResult::UNDERCARRIAGE_OVER);
    }
  }
}
// Create a ml path option, which changes the reference path using ml path and
// also updates the lateral clearance generator.
// TODO(chengding): Pass MlPathInfo into this function.
void AddMLPathOption(const WorldModel& world_model,
                     const RequiredLateralGapInfo& required_lateral_gap_info,
                     const PathGenerationOption& reference_generation_option,
                     const math::geometry::PolylineCurve2d& ml_path,
                     const pb::CandidatesOutput::Candidate* ml_candidate,
                     const pb::TrajectoryType trajectory_type,
                     const bool need_to_copy_extra_ml_trajectory,
                     lateral_clearance::BaseLateralClearanceGenerator*
                         lateral_clearance_generator,
                     pb::LaneSequenceInfoAndIntentPlanDebug& debug,
                     std::vector<PathGenerationOption>* options) {
  // Create a new path option.
  auto* intention_plan_debug_ptr = debug.add_single_intention_plan_debug();
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *reference_generation_option.geometric_constraint.constraint_manager);

  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(
          reference_generation_option.geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = &debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = trajectory_type,
      .need_to_copy_extra_ml_trajectory = need_to_copy_extra_ml_trajectory,
      .optional_last_path = std::nullopt,
      .behavior_relevant_objects_info =
          reference_generation_option.behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt,
      .skip_path_search = true};

  // Update the reference path to the ml path.
  option.geometric_constraint.reference_path = adv_geom::PathCurve2d(ml_path);
  option.geometric_constraint.path_length = ml_path.GetTotalArcLength();
  const math::ProximityQueryInfo ego_proximity_info =
      option.geometric_constraint.reference_path.GetProximity(
          world_model.robot_state()
              .current_state_snapshot()
              .front_bumper_position(),
          math::pb::UseExtensionFlag::kForbid);
  math::Range1d planning_horizon_range = math::Range1d(
      ego_proximity_info.arc_length,
      ego_proximity_info.arc_length + option.geometric_constraint.path_length);
  planning_horizon_range = GetExtendedPlanningHorizonRange(
      option.geometric_constraint.reference_path, planning_horizon_range,
      kDrivableCorridorExtendBufferInMeter);
  // Update the lateral clearance due to new reference path.
  lateral_clearance::LateralClearanceData lateral_clearance_data;
  DCHECK(lateral_clearance_generator != nullptr);
  lateral_clearance_data = lateral_clearance_generator->Generate(
      option.geometric_constraint.reference_path, planning_horizon_range,
      option.geometric_constraint.lane_sequence_geometry
          .left_hard_boundary_lines,
      option.geometric_constraint.lane_sequence_geometry
          .right_hard_boundary_lines,
      option.geometric_constraint.lane_sequence_geometry
          .middle_hard_boundary_lines,
      required_lateral_gap_info.hard_boundary_required_lat_gap);
  option.path_reasoning_result.homotopic_constraint_manager
      ->SetLateralClearanceData(std::move(lateral_clearance_data));

  // Disable lane marking boundary when using ml path for a better path
  // flexibility.
  // TODO(chengding): Consider neighbor lane agents situations as well as solid
  // lane marking.
  option.path_reasoning_result.homotopic_constraint_manager
      ->ClearLaneMarkingBoundaries();
  option.path_reasoning_result.homotopic_constraint_manager
      ->ClearReasoningBoundaries();

  option.ml_planner_meta = MlPlannerMeta{.ml_trajectory = pb::Trajectory(),
                                         .candidate_ptr = ml_candidate};
  auto& intention_result = option.path_reasoning_result.intention_result;
  const auto& planner_object_map_ptr =
      DCHECK_NOTNULL(DCHECK_NOTNULL(world_model.global_object_manager_ptr())
                         ->planner_object_map_ptr());
  AddFodIntentionMapToMLPathOption(
      *planner_object_map_ptr,
      reference_generation_option.path_reasoning_result.intention_result,
      intention_result);
  speed::Profile speed_profile;
  for (const auto& ml_state : ml_candidate->states()) {
    const auto& ml_pose = ml_state.trajectory_pose();
    speed_profile.emplace_back(ml_pose.timestamp(), ml_pose.odom(),
                               ml_pose.speed(), ml_pose.accel(),
                               ml_pose.jerk());
  }
  *intention_result.mutable_selected_speed_profile() =
      speed::ToProto(speed_profile);

  options->push_back(std::move(option));
}

// Returns a ml backup PathGenerationOption (with IGNORE_ALL intent) for the
// corresponding sample PathGenerationOption. ML Backup path option will skip
// path search and provide ml path directly to smoother for path generation.
void MaybeAddMLBackupPathOptions(
    const WorldModel& world_model,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const PathGenerationOption& reference_generation_option,
    lateral_clearance::BaseLateralClearanceGenerator*
        lateral_clearance_generator,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug,
    std::vector<PathGenerationOption>* options) {
  if (reference_generation_option.geometric_constraint.behavior_type !=
          pb::BehaviorType::LANE_KEEP ||
      world_model.trajectory_guider_output() == nullptr) {
    return;
  }

  for (int i = 0;
       i <
       world_model.trajectory_guider_output()->path_output().candidates_size();
       ++i) {
    const auto& ml_path =
        world_model.trajectory_guider_output()->path_output().candidates(i);
    // Drop ml path if there is no lane sequence matching the nominal path.
    if (!IfLaneSequenceMatched(
            ml_path.lane_sequence_lane_ids(),
            reference_generation_option.geometric_constraint.lane_sequence_info
                ->lane_sequence_iterator()
                .lane_sequence())) {
      continue;
    }

    // Convert the path states to a polyline.
    DCHECK_GT(ml_path.states().size(), 1);
    std::vector<math::geometry::Point2d> state_points;
    state_points.reserve(ml_path.states().size());
    for (auto state : ml_path.states()) {
      state_points.emplace_back(state.x_global(), state.y_global());
    }
    math::geometry::PolylineCurve2d ml_path_polyline(state_points);
    MlPathInfo ml_path_info = {.ml_path = ml_path_polyline,
                               .candidate_ptr = &ml_path};

    // The debug info is not valid for ml backup path.
    if (NeedToDropMLPath(
            ml_path_info, reference_generation_option.geometric_constraint,
            ml_path.probability(), /*is_ml_backup_path=*/true, i, debug)) {
      continue;
    }

    AddMLPathOption(world_model, required_lateral_gap_info,
                    reference_generation_option, ml_path_polyline, &ml_path,
                    pb::ML_BACKUP_TRAJECTORY,
                    /*need_to_copy_extra_ml_trajectory=*/false,
                    lateral_clearance_generator, debug, options);
    return;
  }
}

// Adds ML path/trajectory option to options vector if available.
// ML_PATH option is the ML integration 2.0.
// ML_TRAJECTORY option is the ML integration 3.0.
void MaybeAddMLPathOptions(
    const WorldModel& world_model,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const PathGenerationOption& reference_generation_option,
    const bool enable_ml_path, const bool enable_ml_trajectory,
    lateral_clearance::BaseLateralClearanceGenerator*
        lateral_clearance_generator,
    pb::LaneSequenceInfoAndIntentPlanDebug& debug,
    std::vector<PathGenerationOption>* options) {
  if (world_model.trajectory_guider_output() == nullptr) {
    return;
  }

  // Add at most kMLPathOptionsNumber ml path options. The ml path candidates
  // are sorted by probability.
  std::vector<MlPathInfo> ml_candidates;
  ml_candidates.reserve(kMLPathOptionsNumber);
  for (int i = 0; i < world_model.trajectory_guider_output()
                          ->trajectory_output()
                          .candidates_size();
       ++i) {
    const auto& ml_path =
        world_model.trajectory_guider_output()->trajectory_output().candidates(
            i);
    // Drop ml path if there is no lane sequence matching the nominal path.
    if (!IfLaneSequenceMatched(
            ml_path.lane_sequence_lane_ids(),
            reference_generation_option.geometric_constraint.lane_sequence_info
                ->lane_sequence_iterator()
                .lane_sequence()) ||
        ml_candidates.size() >= kMLPathOptionsNumber) {
      debug.add_filter_ml_candidate_reason(
          absl::StrCat("filter_ml_candidate_", i,
                       "_because_ml_target_lane_sequence_unmatched"));
      continue;
    }

    // Convert the path to a polyline.
    DCHECK_GT(ml_path.states().size(), 1);
    std::vector<math::geometry::Point2d> state_points;
    state_points.reserve(ml_path.states().size());
    for (auto state : ml_path.states()) {
      const math::geometry::Point2d point(state.x_global(), state.y_global());
      if (state_points.empty() ||
          !math::NearZero(math::geometry::Length(
              math::geometry::Subtract(point, state_points.back())))) {
        state_points.push_back(point);
      }
    }
    if (state_points.size() < 2) {
      continue;
    }
    math::geometry::PolylineCurve2d ml_path_polyline(state_points);
    MlPathInfo ml_path_info = {.ml_path = ml_path_polyline,
                               .candidate_ptr = &ml_path};

    // We can only disable the ml path filter when testing ml planner model
    // quality, while the FLAGS_planning_enable_forcing_selecting_ml_path should
    // be true at the same time.
    if (FLAGS_planning_disable_ml_path_filter) {
      DCHECK(FLAGS_planning_enable_forcing_selecting_ml_path);
    } else {
      if (NeedToDropMLPath(ml_path_info,
                           reference_generation_option.geometric_constraint,
                           ml_path.probability(),
                           /*is_ml_backup_path=*/false, i, debug)) {
        continue;
      }
    }

    ml_candidates.push_back(std::move(ml_path_info));
    // If FLAGS_planning_enable_add_6_ml_path is not enabled, we will only add
    // at most 1 ml path option.
    if (!FLAGS_planning_enable_add_6_ml_path) {
      break;
    }
  }
  for (const auto& ml_candidate : ml_candidates) {
    if (enable_ml_path && enable_ml_trajectory) {
      AddMLPathOption(world_model, required_lateral_gap_info,
                      reference_generation_option, ml_candidate.ml_path,
                      ml_candidate.candidate_ptr, pb::ML_PATH,
                      /*need_to_copy_extra_ml_trajectory=*/true,
                      lateral_clearance_generator, debug, options);
    } else if (enable_ml_path) {
      AddMLPathOption(world_model, required_lateral_gap_info,
                      reference_generation_option, ml_candidate.ml_path,
                      ml_candidate.candidate_ptr, pb::ML_PATH,
                      /*need_to_copy_extra_ml_trajectory=*/false,
                      lateral_clearance_generator, debug, options);
    } else if (enable_ml_trajectory) {
      AddMLPathOption(world_model, required_lateral_gap_info,
                      reference_generation_option, ml_candidate.ml_path,
                      ml_candidate.candidate_ptr, pb::ML_TRAJECTORY,
                      /*need_to_copy_extra_ml_trajectory=*/false,
                      lateral_clearance_generator, debug, options);
    }
  }
}

// Flattens all the options as well as the debug messages (if they are
// requested).
std::vector<PathGenerationOption> CombinePathOptionsAndDebugs(
    std::vector<GuidanceDiversities>& behavior_diversities) {
  std::vector<PathGenerationOption> option_results;
  const int total_options = std::accumulate(
      behavior_diversities.begin(), behavior_diversities.end(), 0,
      [](int num_options, const GuidanceDiversities& guidance_diversities) {
        return std::accumulate(
                   guidance_diversities.begin(), guidance_diversities.end(), 0,
                   [](int num_options,
                      const IntentionDiversities& intention_diversities) {
                     return num_options + intention_diversities.size();
                   }) +
               num_options;
      });
  option_results.reserve(total_options);
  for (size_t behavior_index = 0; behavior_index < behavior_diversities.size();
       ++behavior_index) {
    // Behavior diversities, i.e. lane keep, jump out, pull over, etc.
    for (size_t guidance_index = 0;
         guidance_index < behavior_diversities[behavior_index].size();
         ++guidance_index) {
      // Guidance diversities, e.g. multiple lane sequences in fork lanes, or
      // different cross lane directions.
      for (size_t intent_index = 0;
           intent_index <
           behavior_diversities[behavior_index][guidance_index].size();
           ++intent_index) {
        // Intention diversities under different homotopy.
        option_results.push_back(
            std::move(behavior_diversities[behavior_index][guidance_index]
                                          [intent_index]));
        option_results.back().guidance_index = guidance_index;
        option_results.back().intent_index = intent_index;
        option_results.back().unique_path_homotopy_id =
            PopulatePathHomotopyUniqueId(
                option_results.back().trajectory_type,
                option_results.back().geometric_constraint.behavior_type,
                option_results.back()
                    .path_reasoning_result.intention_result.homotopy(),
                guidance_index, intent_index);

        if (option_results.back().intention_plan_debug != nullptr) {
          option_results.back()
              .intention_plan_debug->set_dropped_due_to_path_similarity(false);
          option_results.back().intention_plan_debug->set_trajectory_type(
              option_results.back().trajectory_type);
        }
      }
    }
  }
  DCHECK(!option_results.empty())
      << "No path generation options available at all";
  return option_results;
}

// Populates the list of agent ids that path reasoning will ignore from LC guide
// seed.
void GetAgentsToIgnore(
    const google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>&
        lc_guide_seeds,
    const std::vector<int64_t>& ignore_agent_ids_during_lane_change,
    std::unordered_set<int64_t>& ignore_agent_ids) {
  // TODO(LC): support multi-side lane change.
  if (lc_guide_seeds.size() != 1) return;

  for (const int64_t agent_ignore : ignore_agent_ids_during_lane_change) {
    ignore_agent_ids.insert(agent_ignore);
  }
}

// Populates the list of agent ids that path reasoning will ignore for waypoint.
void GetAgentsToIgnoreForWaypoint(
    const math::geometry::PolylineCurve2d& reference_path,
    const math::geometry::Point2d& front_bumper_position,
    const GlobalObjectManager& global_object_manager,
    std::unordered_set<int64_t>& ignore_agent_ids) {
  TRACE_EVENT_SCOPE(planner, GetAgentsToIgnoreForWaypoint);

  const double ego_front_bumper_position_arclength =
      reference_path
          .GetProximity(front_bumper_position,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  // collect all stationary vehicle  objects on path
  auto is_stationary_vehicle = [](const PlannerObject& object) {
    return object.is_vehicle() && object.is_stationary();
  };

  const auto objects_on_path =
      global_object_manager.GetPlannerObjectsIntersectWithPolyline(
          reference_path, is_stationary_vehicle);

  if (objects_on_path.empty()) {
    return;
  }

  for (const auto* object : objects_on_path) {
    const math::geometry::Point2d object_box_direction(
        std::cos(object->box_heading()), std::sin(object->box_heading()));

    const math::geometry::Point2d object_box_rear_center = math::geometry::Add(
        object->center_2d(), math::geometry::Multiply(object_box_direction,
                                                      -0.5 * object->length()));

    const double object_arc_length =
        reference_path
            .GetProximity(object_box_rear_center,
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;

    if (object_arc_length > ego_front_bumper_position_arclength) {
      ignore_agent_ids.insert(object->id());
    }
  }
}

// TODO(liwen): Consider add pull out confirmation check as a sufficient
// condition together with the `IsEgoPositionOnRouteReadyForPullOut`
// check. The reason is, if the pull out is confirmed nearly 100m from the
// route start, it might be broken by lane change due to this condition, as it
// is still during the pull out actually, while the distance to the route
// start might larger than the threshold (100m).
bool IsDuringPullOverOrOut(const WorldModel& world_model,
                           pb::PullOverProgress pull_over_progress) {
  return (world_model.IsEgoPositionOnRouteReadyForPullOut() &&
          !world_model.HasPullOutFinished()) ||
         pull_over_progress != planner::pb::PullOverProgress::kNotTriggered;
}

// Returns a PathGenerationOption (with IGNORE_ALL intent) for the open-space
// behavior type. This option will skip path search and provide reference path
// directly to smoother for path generation.
// NOTE(Jiakai): Currently, the open-space path option is generated by this
// special method like the backup path option. Later, we may consider using the
// standard path reasoning pipeline with decent configuration.
PathGenerationOption GetOpenSpacePathOption(
    const GeometricGuidanceResult& geometric_constraint,
    const BehaviorRelevantObjectsInfo& behavior_relevant_objects_info,
    pb::LaneSequenceInfoAndIntentPlanDebug* debug) {
  auto* intention_plan_debug_ptr = debug->add_single_intention_plan_debug();
  pb::IntentionResult ignore_all_intention;
  ignore_all_intention.set_homotopy(pb::IntentionResult::IGNORE_ALL);
  path::PathReasoningResult path_reasoning_result;
  path_reasoning_result.intention_result = ignore_all_intention;
  path_reasoning_result.homotopic_constraint_manager =
      std::make_unique<path::ConstraintManager>(
          *geometric_constraint.constraint_manager);
  PathGenerationOption option = {
      .geometric_constraint = CopyGeometricGuidanceResult(geometric_constraint),
      .path_reasoning_result = std::move(path_reasoning_result),
      .lane_sequence_debug = debug,
      .intention_plan_debug = intention_plan_debug_ptr,
      .trajectory_type = pb::NOMINAL_TRAJECTORY,
      .need_to_copy_extra_ml_trajectory = false,
      .optional_last_path = std::nullopt,
      .behavior_relevant_objects_info = behavior_relevant_objects_info,
      .ml_planner_meta = std::nullopt,
      .skip_path_search = true};

  // Record lane sequence info debug.
  auto* lane_sequence_geometry_debug =
      debug->mutable_lane_sequence_info()->mutable_lane_sequence_geometry();
  GenerateLaneSequenceGeometryDebug(geometric_constraint.lane_sequence_geometry,
                                    lane_sequence_geometry_debug);
  return option;
}

}  // namespace

std::vector<PathGenerationOption> GeneratePathOptionsForAllBehaviors(
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const WorldModel& world_model, const LaneChangeInfo& lane_change_info,
    const pull_over::PullOverInfo& pull_over_info,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const std::optional<path::PathCandidate>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const pb::MotionMode motion_mode,
    const GlobalObjectManager& global_object_manager,
    const open_space::OpenSpaceInfo& open_space_info,
    pb::LaneChangeGeometryMetaData& current_lane_change_geom_meta,
    CrossLaneInfoManager& cross_lane_info_manager,
    std::vector<std::vector<LaneSeqRelatedInfo>>* lane_sequence_infos,
    lateral_clearance::LateralClearanceGenerationManager*
        lateral_clearance_generation_manager,
    pb::DecoupledManeuverDebug* debug) {
  TRACE_EVENT_SCOPE(
      planner, DecoupledForwardManeuver_GeneratePathOptionsForAllBehaviors,
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());
  DCHECK(lane_sequence_infos != nullptr);
  DCHECK(lane_sequence_infos->empty());

  // TODO(sixian): Change the below to state check and prep.
  std::vector<pb::BehaviorType> enabled_types({});

  if (motion_mode != pb::MotionMode::BACKWARD) {
    enabled_types.push_back(pb::BehaviorType::LANE_KEEP);
  }
  if (open_space_info.IsActive()) {
    // NOTE(Jiakai): Enable lane-keep behavior as well when running forward
    // open-space behavior. The open-space selector will select the proper
    // trajectory.
    enabled_types.push_back(pb::BehaviorType::OPEN_SPACE);
  } else {
    // If motion mode is FORWARD or PARKED.
    if (motion_mode != pb::MotionMode::BACKWARD) {
      // TODO(howardgao): move to lane change info for state management.
      // Do not generate cross lane homotopy.
      if (IsDuringPullOverOrOut(world_model,
                                pull_over_info.status().pull_over_progress())) {
        LOG(INFO) << "[lane change][path]Don't generate CL path during "
                     "pullover or out.";
      } else if (lane_change_info.signal_source_type ==
                 pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed) {
        LOG(INFO) << "[lane change][path]Don't generate CL path if signal is "
                     "from seed.";
      } else {
        enabled_types.push_back(pb::BehaviorType::CROSS_LANE);
      }

      enabled_types.push_back(pb::BehaviorType::DECOUPLED_PULL_OVER);
    } else if (motion_mode == pb::MotionMode::BACKWARD) {
      enabled_types.push_back(pb::BehaviorType::REVERSE);
    }
  }
  lane_sequence_infos->reserve(enabled_types.size());
  for (size_t i = 0; i < enabled_types.size(); ++i) {
    lane_sequence_infos->push_back({});
  }

  // Initialize the cross lane info vector.
  std::vector<CrossLaneInfo> cross_lane_infos;
  cross_lane_infos.reserve(enabled_types.size());
  for (size_t i = 0; i < enabled_types.size(); ++i) {
    cross_lane_infos.emplace_back();
  }

  // TODO(hongda): Move the path motion engine to the world_model.
  const path::PathMotionEngine path_motion_engine =
      path::PathMotionEngine::GeneratePathMotionEngine(
          world_model.robot_state().plan_init_state_snapshot());

  // It will cover all the rolled out path diversities.
  std::vector<GuidanceDiversities> path_generation_options;
  path_generation_options.reserve(enabled_types.size());
  for (size_t i = 0; i < enabled_types.size(); ++i) {
    path_generation_options.push_back({});
  }

  auto base_behavior_relevant_objects_info =
      global_object_manager.base_behavior_relevant_objects_info();

  // Create a mutex that will protect debug, as we don't know how many lane
  // sequences will be selected for generation eventually.
  std::mutex debug_mutex;

  tbb::parallel_for(
      0, static_cast<int>(enabled_types.size()),
      [&lane_sequence_candidates, &world_model, &lane_change_info,
       &pull_over_info, &open_space_info, &enabled_types,
       &path_generation_options, &required_lateral_gap_info,
       &reusable_last_path, &reusable_last_speed_profile, &previous_iter_seed,
       &current_lane_change_geom_meta, &path_motion_engine,
       &base_behavior_relevant_objects_info, &debug_mutex,
       &lateral_clearance_generation_manager, motion_mode, &cross_lane_infos,
       lane_sequence_infos, debug,
       th_owner = ThreadOwnerMarker::CurrentOwner()](int behavior_index) {
        PLANNER_MARK_THREAD_OWNER(th_owner);
        auto behavior_based_objects_info = base_behavior_relevant_objects_info;
        // Calls geometric guidance generator for guidance gen.
        const pb::BehaviorType type = enabled_types[behavior_index];

        // Generate cross lane info for the corresponding behavior type.
        cross_lane_infos[behavior_index] = GenerateCrossLaneInfo(
            type, world_model, std::cref(lane_change_info), previous_iter_seed,
            debug);

        std::vector<LaneSeqRelatedInfo>* infos =
            &(*lane_sequence_infos)[behavior_index];
        std::unordered_set<int64_t> ignore_agent_ids;
        std::vector<GeometricGuidanceResult> guidances;
        std::unique_ptr<LaneKeepGeometricGuidanceGenerator>
            lane_keep_generator = nullptr;
        std::unique_ptr<LaneChangeGeometricGuidanceGenerator>
            lane_change_generator = nullptr;
        std::unique_ptr<PullOverGeometricGuidanceGenerator>
            pull_over_generator = nullptr;
        std::unique_ptr<ReverseDrivingGeometricGuidanceGenerator>
            reversing_driving_generator = nullptr;
        std::unique_ptr<OpenSpaceGeometricGuidanceGenerator>
            open_space_generator = nullptr;
        switch (type) {
          case pb::BehaviorType::LANE_KEEP: {
            lane_keep_generator =
                std::make_unique<LaneKeepGeometricGuidanceGenerator>(
                    lane_sequence_candidates, world_model, lane_change_info,
                    required_lateral_gap_info, previous_iter_seed,
                    PlannerConfigCenter::GetInstance(), pull_over_info, infos);
            guidances = lane_keep_generator->Generate(
                debug_mutex, lateral_clearance_generation_manager,
                behavior_based_objects_info, debug);
            // Ignore agents for waypoint.
            if (world_model.waypoint_assist_tracker().waypoint_assist_phase() ==
                    planner::pb::AssistManeuverPhase::kFollowingPath &&
                !guidances.empty()) {
              GetAgentsToIgnoreForWaypoint(
                  guidances.front().lane_sequence_geometry.nominal_path,
                  world_model.robot_state()
                      .current_state_snapshot()
                      .front_bumper_position(),
                  *world_model.global_object_manager_ptr(), ignore_agent_ids);
            }
            break;
          }
          case pb::BehaviorType::CROSS_LANE: {
            lane_change_generator =
                std::make_unique<LaneChangeGeometricGuidanceGenerator>(
                    lane_sequence_candidates, world_model, lane_change_info,
                    required_lateral_gap_info, previous_iter_seed,
                    PlannerConfigCenter::GetInstance(), infos);
            guidances = lane_change_generator->Generate(
                debug_mutex, lateral_clearance_generation_manager,
                behavior_based_objects_info, debug);
            if (lane_change_generator->lane_change_geom_meta().has_value()) {
              // We store lane change metadata to decoupled maneuver seed since
              // gap align starts to run, but do not latch this data until
              // LC is firstly selected by selection.
              current_lane_change_geom_meta =
                  lane_change_generator->lane_change_geom_meta()->ToProto();
              GetAgentsToIgnore(previous_iter_seed.speed_seed().lc_guide_seed(),
                                lane_change_generator->lane_change_geom_meta()
                                    ->ignore_agent_ids_during_lane_change,
                                ignore_agent_ids);
            }
            break;
          }
          case pb::BehaviorType::DECOUPLED_PULL_OVER: {
            // TODO(Zixuan, Liwen): Further refactor the following logic to
            // support multi-geometry-guidance generation given multiple pull
            // over destinations.
            pull_over_generator =
                std::make_unique<PullOverGeometricGuidanceGenerator>(
                    lane_sequence_candidates, world_model, lane_change_info,
                    required_lateral_gap_info, previous_iter_seed,
                    PlannerConfigCenter::GetInstance(), pull_over_info, infos);
            guidances = pull_over_generator->Generate(
                debug_mutex, lateral_clearance_generation_manager,
                behavior_based_objects_info, debug);
            break;
          }
          case pb::BehaviorType::REVERSE: {
            reversing_driving_generator =
                std::make_unique<ReverseDrivingGeometricGuidanceGenerator>(
                    lane_sequence_candidates, world_model,
                    required_lateral_gap_info, previous_iter_seed,
                    PlannerConfigCenter::GetInstance(), infos);
            guidances = reversing_driving_generator->Generate(
                debug_mutex, lateral_clearance_generation_manager,
                behavior_based_objects_info, debug);
            break;
          }
          case pb::BehaviorType::OPEN_SPACE: {
            open_space_generator =
                std::make_unique<OpenSpaceGeometricGuidanceGenerator>(
                    lane_sequence_candidates, world_model,
                    required_lateral_gap_info, previous_iter_seed,
                    PlannerConfigCenter::GetInstance(), open_space_info,
                    motion_mode, infos);
            guidances = open_space_generator->Generate(
                debug_mutex, lateral_clearance_generation_manager,
                behavior_based_objects_info, debug);
            break;
          }
          default: {
            CHECK(false) << "You shall not pass into this forbidden region";
            break;
          }
        }
        if (guidances.empty()) {
          // It is allowed for one guidance generator to not generate any
          // guidances.
          return;
        }
        for (size_t i = 0; i < guidances.size(); ++i) {
          path_generation_options[behavior_index].push_back({});
        }
        if (type == pb::BehaviorType::OPEN_SPACE) {
          // No need to run path reasoning. Only add an ignore-all homotopy for
          // open-space behavior.
          DCHECK_EQ(guidances.size(), 1);
          path_generation_options[behavior_index][0].push_back(
              GetOpenSpacePathOption(guidances.at(0),
                                     behavior_based_objects_info,
                                     guidances.at(0).debug));
          return;
        }
        // Runs path reasoning for all the guidances generated.
        tbb::parallel_for(
            0, static_cast<int>(guidances.size()),
            [&path_generation_options, &world_model, &pull_over_info,
             &guidances, &type, &required_lateral_gap_info, &previous_iter_seed,
             &reusable_last_path, &reusable_last_speed_profile,
             &ignore_agent_ids, &path_motion_engine,
             &behavior_based_objects_info,
             &lateral_clearance_generation_manager, motion_mode,
             behavior_index](int guidance_index) {
              TRACE_EVENT_SCOPE(
                  planner, DecoupledForwardManeuver_PathGenerationGuidanceLoop);
              base::WallClockElapsedTimer execute_timer;
              auto intention_based_objects_info = behavior_based_objects_info;
              auto& updated_need_to_be_considered_planner_objects =
                  intention_based_objects_info.mutable_relevant_objects();
              if (FLAGS_planning_enable_object_occupancy_state) {
                UpdateObjectInLaneParamInfo(
                    guidances[guidance_index]
                        .lane_sequence_info->object_occupancy_state_map(),
                    guidances[guidance_index].behavior_type,
                    updated_need_to_be_considered_planner_objects);
              } else {
                UpdateObjectInLaneParamInfo(
                    guidances[guidance_index]
                        .lane_sequence_info->tracked_object_in_lane_param_map(),
                    guidances[guidance_index].behavior_type,
                    updated_need_to_be_considered_planner_objects);
              }
              // NOTE: We need to make sure that there are NO common ignore
              // agent ids shared across multiple path generation options. The
              // following implementation will always use pull over ignored
              // agent ids for pull over path generation option.
              // TODO(path reasoning): Consider making ignored agent ids to be a
              // field in GeometricGuidanceResult.
              path_generation_options[behavior_index][guidance_index] =
                  RunPathReasoning(
                      world_model, required_lateral_gap_info, guidance_index,
                      kExtraNonMLPathOptionsNumber + kMLPathOptionsNumber,
                      previous_iter_seed,
                      reusable_last_path.has_value()
                          ? std::make_optional(reusable_last_path->path)
                          : std::nullopt,
                      reusable_last_speed_profile,
                      guidances[guidance_index]
                                  .pull_over_reasoning_input
                                  .destination_meta_ptr == nullptr
                          ? ignore_agent_ids
                          : guidances[guidance_index]
                                .pull_over_reasoning_input.destination_meta_ptr
                                ->ignored_object_ids,
                      path_motion_engine.left_right_steering_path_motion_map(),
                      intention_based_objects_info, guidances[guidance_index],
                      guidances[guidance_index].debug);
              if (path_generation_options[behavior_index][guidance_index]
                      .empty()) {
                LOG(ERROR) << "No input path generation option after reasoning";
                return;
              }
              // kExtraNonMLPathOptionsNumber must be modified whenever we have
              // new extra path options.
              const int max_path_options_capacity =
                  path_generation_options[behavior_index][guidance_index]
                      .capacity();
              // For backup path generation and last path generation, some
              // geometric information is needed (e.g. road boundary info, etc).
              // We use the last available path generation option for reference
              // when generating them.
              const PathGenerationOption& reference_generation_option =
                  path_generation_options[behavior_index][guidance_index]
                      .back();
              // Get the lateral clearance generator by lane sequence index.
              DCHECK(lateral_clearance_generation_manager != nullptr);
              auto* lateral_clearance_generator =
                  lateral_clearance_generation_manager->GetGenerator(
                      reference_generation_option.geometric_constraint
                          .behavior_type,
                      guidance_index);
              // Adds ML path/trajectory generation option.
              const bool enable_ml_path = ShouldEnableAddingMLPath(
                  world_model, motion_mode, pull_over_info,
                  path_generation_options[behavior_index][guidance_index],
                  reference_generation_option.geometric_constraint,
                  previous_iter_seed, *guidances[guidance_index].debug);
              const bool enable_ml_trajectory = ShouldEnableAddingMLTrajectory(
                  world_model
                      .ml_trajectory_init_state_has_diverged_from_planner(),
                  reference_generation_option.geometric_constraint
                      .behavior_type,
                  *guidances[guidance_index].debug);
              if (enable_ml_path || enable_ml_trajectory) {
                MaybeAddMLPathOptions(
                    world_model, required_lateral_gap_info,
                    reference_generation_option, enable_ml_path,
                    enable_ml_trajectory, lateral_clearance_generator,
                    *guidances[guidance_index].debug,
                    &(path_generation_options[behavior_index][guidance_index]));
              }
              if (FLAGS_planning_enable_upl_big_turn_path ||
                  FLAGS_planning_enable_upl_small_turn_path) {
                MaybeAddUplPathOptions(
                    world_model, previous_iter_seed, required_lateral_gap_info,
                    reference_generation_option, lateral_clearance_generator,
                    *guidances[guidance_index].debug,
                    &(path_generation_options[behavior_index][guidance_index]));
              }
              // Adds backup path generation option.
              path_generation_options[behavior_index][guidance_index].push_back(
                  GetBackupPathOption(reference_generation_option,
                                      *guidances[guidance_index].debug));
              // Adds fallback path generation option.
              if (FLAGS_planning_enable_fallback_path) {
                path_generation_options[behavior_index][guidance_index]
                    .push_back(GetFallbackPathOption(
                        world_model, reference_generation_option,
                        *guidances[guidance_index].debug));
              }
              // Adds ml backup path generation option.
              MaybeAddMLBackupPathOptions(
                  world_model, required_lateral_gap_info,
                  reference_generation_option, lateral_clearance_generator,
                  *guidances[guidance_index].debug,
                  &(path_generation_options[behavior_index][guidance_index]));
              // Adds last path trajectory generation option.
              MaybeAddLastPathOption(
                  type, reusable_last_path, world_model, previous_iter_seed,
                  reference_generation_option, *guidances[guidance_index].debug,
                  &(path_generation_options[behavior_index][guidance_index]));
              // Make sure there is no vector resizing during adding the extra
              // path options.
              DCHECK_EQ(path_generation_options[behavior_index][guidance_index]
                            .capacity(),
                        max_path_options_capacity);
              // Record unexpected latency.
              if (execute_timer.GetElapsedTimeInMs() >
                  kPathGenerationGuidanceLoopRunningTimeInMs) {
                rt_event::PostRtEvent<
                    rt_event::planner::PathGenerationGuidanceLoop>();
              }
            });  // End for loop guidances
      });        // End for loop enabled_types

  cross_lane_info_manager = CrossLaneInfoManager(cross_lane_infos);

  return CombinePathOptionsAndDebugs(path_generation_options);
}

}  // namespace planner
