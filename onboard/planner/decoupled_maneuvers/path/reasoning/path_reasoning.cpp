#include "planner/decoupled_maneuvers/path/reasoning/path_reasoning.h"

#include <algorithm>
#include <cstdint>
#include <glog/logging.h>
#include <map>
#include <memory>
#include <optional>
#include <utility>

#include <tbb/tbb.h>

#include "geometry/model/polyline_curve.h"
#include "latency/latency_stat.h"
#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_type_check_utility.h"
#include "planner/decoupled_maneuvers/geometric_guidance/geometric_guidance_generator_util.h"
#include "planner/decoupled_maneuvers/path/motion_engine/nudge_motion_checker.h"
#include "planner/decoupled_maneuvers/path/reasoning/boundary_reasoning.h"
#include "planner/decoupled_maneuvers/path/reasoning/trajectory_guider_reasoning.h"
#include "planner/path/path_solver/constraint_manager/road_boundaries.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/path/reasoning/agent_intention/intention_generator.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/planning_gflags.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {

// Arclength threshold between ego pose and u-turn starting point under which
// will we avoid reducing physical boundary req lat gap. Since u-turn is
// special, ego might get close to the physical boundary, which will cause
// stuck.
constexpr double kArcLengthThresholdForCloseToUTurnInMeter = 10.0;

// Deviation of in lane corridor boundary w.r.t center line.
constexpr double kInLaneDeviationInMeter = 1.2;

// We add in lane boundary before junction using this arc length range.
constexpr double kInLaneBoundaryRangeBeforeJuntion = 20.0;

// Obtains the first exit zone along lane sequence in front of ego.
const lane_selection::ZoneInLaneSequence* GetFirstExitZone(
    const lane_selection::LaneSequenceTrafficRules& lane_sequence_traffic_rules,
    const double ego_arclength_m) {
  for (const auto& zone : lane_sequence_traffic_rules.zone_in_lane_vector()) {
    if (zone.start_arclength < ego_arclength_m) {
      continue;
    }
    if (zone.zone_ptr->type() != hdmap::Zone::ROAD_EXIT) {
      continue;
    }
    return &zone;
  }
  return nullptr;
}

// Check whether in-lane reasoning boundary need to be appended.
bool ShouldAppendInLaneReasoningBoundary(
    const GeometricGuidanceResult& geometric_guidance,
    const pb::IntentionResult::IntentionHomotopy intention_homotopy) {
  const bool is_pull_out_jump_out =
      geometric_guidance.lane_sequence_info->lane_sequence_result()
          .IsPullOutJumpOut();
  const pb::BehaviorType behavior_type = geometric_guidance.behavior_type;
  return behavior_type == pb::BehaviorType::LANE_KEEP &&
         intention_homotopy == pb::IntentionResult::IGNORE_ALL &&
         !is_pull_out_jump_out;
}

bool ShouldAppendLaneChangeProbingAttractionInfo(
    const GeometricGuidanceResult& geometric_guidance,
    const pb::IntentionResult::IntentionHomotopy intention_homotopy) {
  const bool is_pull_out_jump_out =
      geometric_guidance.lane_sequence_info->lane_sequence_result()
          .IsPullOutJumpOut();
  const pb::BehaviorType behavior_type = geometric_guidance.behavior_type;
  const bool no_unidirectional_nudge =
      (intention_homotopy == pb::IntentionResult::IGNORE_ALL) ||
      (intention_homotopy == pb::IntentionResult::PASS_THROUGH);
  return behavior_type == pb::BehaviorType::LANE_KEEP &&
         no_unidirectional_nudge && !is_pull_out_jump_out;
}

// Returns true if the dynamic agent's snapshots groups violate the attraction
// polyline within the longitudinal attraction range.
bool DoesAgentViolateProbeAttractionGeom(
    const RobotState& robot_state, const AgentConstraint& agent_constraint,
    const pull_over::ProbeDecision& probe_decision) {
  const double vehicle_length = robot_state.GetLength();
  const double half_vehicle_width = 0.5 * robot_state.GetWidth();
  DCHECK(probe_decision.attraction_polyline.has_value() &&
         probe_decision.attraction_range.has_value());

  const auto does_snapshot_contour_interfere_probing =
      [&agent_constraint, &probe_decision, half_vehicle_width, vehicle_length](
          const math::geometry::PolygonWithCache2d& snapshot_contour) {
        const math::geometry::PolylineCurve2d& attraction_polyline =
            probe_decision.attraction_polyline.value();
        const math::Range1d& attraction_range =
            probe_decision.attraction_range.value();
        const double buffered_attraction_range_start_pos =
            std::max(0.0, attraction_range.start_pos - vehicle_length);
        const double buffered_attraction_range_end_pos =
            std::min(attraction_polyline.GetTotalArcLength(),
                     attraction_range.end_pos + vehicle_length);
        const math::geometry::FrenetAxisAlignedBox2d contour_sl_box(
            attraction_polyline, snapshot_contour.points());
        const double buffered_comfort_required_lateral_gap =
            agent_constraint.required_lateral_gap.comfort_required_lateral_gap +
            half_vehicle_width;
        // If negative, the violation is checked from the right side of the
        // probing attraction geometry.
        return contour_sl_box.l_range.end_pos >=
                   -buffered_comfort_required_lateral_gap &&
               (math::IsInRange(contour_sl_box.s_range.start_pos,
                                buffered_attraction_range_start_pos,
                                buffered_attraction_range_end_pos) ||
                math::IsInRange(contour_sl_box.s_range.end_pos,
                                buffered_attraction_range_start_pos,
                                buffered_attraction_range_end_pos));
      };

  for (const std::vector<ObjectSnapshot>& snapshots_group :
       agent_constraint.snapshots_groups) {
    for (const ObjectSnapshot& snapshot : snapshots_group) {
      if (does_snapshot_contour_interfere_probing(snapshot.contour)) {
        return true;
      }
    }
  }
  return false;
}

// Returns true if no dynamic agent exists that requires left-side passing and
// whose snapshot groups violate the attraction polyline within the longitudinal
// attraction range.
bool ShouldAppendPullOverProbingAttractionInfo(
    const RobotState& robot_state,
    const GeometricGuidanceResult& geometric_guidance,
    const path::PathReasoningResult& path_reasoning_result) {
  const pull_over::ProbeDecision* probe_decision_ptr =
      geometric_guidance.pull_over_reasoning_input.probe_decision_ptr;
  if (probe_decision_ptr == nullptr || !probe_decision_ptr->should_probe) {
    return false;
  }
  // Append probing attraction info only for BehaviorType::LANE_KEEP.
  if (geometric_guidance.behavior_type != pb::BehaviorType::LANE_KEEP) {
    return false;
  }
  const pb::IntentionResult::IntentionHomotopy& homotopy =
      path_reasoning_result.intention_result.homotopy();
  if (homotopy == pb::IntentionResult::PASS_FROM_LEFT ||
      homotopy == pb::IntentionResult::PASS_THROUGH ||
      homotopy == pb::IntentionResult::XLANE_PASS_FROM_LEFT) {
    for (const AgentConstraint& agent_constraint :
         path_reasoning_result.agent_constraints) {
      if (agent_constraint.type != AgentConstraint::Type::kDynamicAgent ||
          agent_constraint.pass_state != pb::SnapshotIntention::PASS_LEFT) {
        continue;
      }
      if (DoesAgentViolateProbeAttractionGeom(robot_state, agent_constraint,
                                              *probe_decision_ptr)) {
        return false;
      }
    }
  }
  return true;
}

// Appends attraction constraints for pull over probing.
void AppendPullOverProbingAttractionInfo(
    const pull_over::ProbeDecision& probe_decision,
    path::ConstraintManager* constraint_manager_ptr) {
  const std::optional<math::geometry::PolylineCurve2d>& attraction_polyline =
      probe_decision.attraction_polyline;
  const std::optional<math::Range1d>& attraction_range =
      probe_decision.attraction_range;
  DCHECK(attraction_polyline.has_value() && attraction_range.has_value());
  std::vector<path::AttractionRange> attraction_ranges;
  // The number of pull over probe attraction segments on attraction polyline.
  static constexpr int kNumOfPullOverProbeAttractionSegments = 2;
  // The left tolerance buffer of attraction segment for pull over probe.
  static constexpr double kPullOverProbeAttractionLeftToleranceInMeter = 0.0;
  // The right tolerance buffer of attraction segment for pull over probe.
  static constexpr double kPullOverProbeAttractionRightToleranceInMeter = 0.5;

  // Split the attraction range into weak and normal strength (strength in probe
  // decision) segments if the attraction length is beyond the distance at which
  // we expect to maintain weak attraction strength.
  attraction_ranges.reserve(kNumOfPullOverProbeAttractionSegments);
  const double attraction_length =
      attraction_range->end_pos - attraction_range->start_pos;
  DCHECK_GE(attraction_length, 0.0);
  // The distance ahead of the ego where weak-strength probing is maintained.
  static constexpr double kNearEgoWeakStrengthProbingDistanceInMeter = 6.0;
  const math::Range1d lateral_range(
      -kPullOverProbeAttractionRightToleranceInMeter,
      kPullOverProbeAttractionLeftToleranceInMeter);

  // Maintain weak-strength probing within the distance of
  // kNearEgoWeakStrengthProbingDistanceInMeter ahead to prevent harsh swerving
  // from sudden attraction strength transitions.
  if (attraction_length > kNearEgoWeakStrengthProbingDistanceInMeter) {
    const double attraction_split_pos =
        attraction_range->start_pos +
        kNearEgoWeakStrengthProbingDistanceInMeter;
    attraction_ranges.emplace_back(
        lateral_range,
        math::Range1d(attraction_range->start_pos, attraction_split_pos),
        path::AttractionStrength::kWeak);
    attraction_ranges.emplace_back(
        lateral_range,
        math::Range1d(attraction_split_pos, attraction_range->end_pos),
        probe_decision.attraction_strength);
  } else {
    attraction_ranges.emplace_back(lateral_range, attraction_range.value(),
                                   path::AttractionStrength::kWeak);
  }
  DCHECK_NOTNULL(constraint_manager_ptr)
      ->AddAttraction(attraction_polyline.value(), attraction_ranges,
                      path::AttractionSource::kPullOverProbing);
}

// Appends attraction constraints for lane change probing.
void AppendLaneChangeProbingAttractionInfo(
    const LaneChangeProbingInfo& lane_change_probe_info,
    path::ConstraintManager* constraint_manager_ptr) {
  std::vector<path::AttractionRange> attraction_ranges;
  attraction_ranges.reserve(1);
  attraction_ranges.emplace_back(
      /*lateral_range=*/math::Range1d(
          lane_change_probe_info.start_lateral_range,
          lane_change_probe_info.end_lateral_range), /*active_arclength_range=*/
      math::Range1d(lane_change_probe_info.attraction_start_arc_length,
                    lane_change_probe_info.attraction_end_arc_length),
      /*strength=*/path::AttractionStrength::kModerate);
  DCHECK_NOTNULL(constraint_manager_ptr)
      ->AddReferenceAttraction(
          attraction_ranges,
          /*attraction_source=*/path::AttractionSource::kLcProbing);
}

// Adds in lane boundary in junction to keep ego driving in lane.
void AddInLaneBoundary(
    const double ego_full_body_start_arclength,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    math::pb::Side side, path::ConstraintManager& constraint_manager) {
  BoundaryElement boundary_element;
  boundary_element.boundary_type = path::BoundaryType::kPureVirtual;
  // TODO(huanhui): further polish this deviation distance based on environment.
  boundary_element.optional_max_pure_virtual_deviation_m =
      kInLaneDeviationInMeter;
  // Find the nearest end arclength for the lane section that is in junction.
  const auto& ranges = lane_sequence_geometry.arclength_ranges_in_junction;
  DCHECK(std::is_sorted(ranges.cbegin(), ranges.cend(),
                        [](const auto& lhs, const auto& rhs) -> bool {
                          return lhs.start_pos < rhs.start_pos;
                        }));
  // TODO(path): Use lower_bound() instead since the ranges are sorted.
  const auto it =
      std::find_if(ranges.begin(), ranges.end(),
                   [&ego_full_body_start_arclength](const auto& range) {
                     return range.end_pos >= ego_full_body_start_arclength;
                   });
  if (it == ranges.end()) {
    return;
  }

  boundary_element.boundary_range = *it;
  boundary_element.boundary_range.start_pos =
      std::max(0.0, it->start_pos - kInLaneBoundaryRangeBeforeJuntion);
  constraint_manager.AddReasoningBoundaryElement(std::move(boundary_element),
                                                 side);
}

void AppendInLaneReasoningBoundary(
    const RobotStateSnapshot& robot_state_snapshot,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    path::ConstraintManager& constraint_manager) {
  voy::TrackedObject ego_object;
  ego_object.set_center_x(robot_state_snapshot.bounding_box().center().x());
  ego_object.set_center_y(robot_state_snapshot.bounding_box().center().y());
  ego_object.set_length(robot_state_snapshot.bounding_box().length());
  ego_object.set_width(robot_state_snapshot.bounding_box().width());
  ego_object.set_heading(robot_state_snapshot.bounding_box().heading());
  const AgentSnapshotInLaneParam ego_inlane_param =
      lane_selection::ComputeAgentSnapshotInLaneParam(
          lane_sequence_geometry,
          /*pose=*/TrafficParticipantPose(/*timestamp=*/1, ego_object));

  AddInLaneBoundary(ego_inlane_param.full_body_end_arclength_m,
                    lane_sequence_geometry, math::pb::Side::kLeft,
                    constraint_manager);
  AddInLaneBoundary(ego_inlane_param.full_body_end_arclength_m,
                    lane_sequence_geometry, math::pb::Side::kRight,
                    constraint_manager);
}

math::geometry::PolylineCurve2d GenerateRepulsionBoundaryBasedOnLaneMarking(
    const EgoInLaneParams& ego_param,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const pb::SnapshotIntention::PassState lateral_decision,
    const path::FeasibleRegion& feasible_region) {
  const math::geometry::PolylineCurve2d& feasible_region_boundary =
      lateral_decision == pb::SnapshotIntention::PASS_LEFT
          ? feasible_region.restricted_right_boundary
          : feasible_region.restricted_left_boundary;
  if (FLAGS_planning_enable_strong_repulsion_in_narrow_meeting) {
    // Strong repulsion may cause the ego vehicle to excessively squeeze against
    // a stationary agent on one side, resulting in the ego vehicle getting
    // too close to the stationary agent and getting stuck. This can be
    // mitigated by leaving a space of half the ego length at the starting
    // position of the repulsion.
    const double half_ego_length =
        0.5 * (ego_param.rear_axle_to_rear_bumper_m +
               ego_param.rear_axle_to_front_bumper_m);
    const double feasible_region_length =
        feasible_region.longitudinal_range.end_pos -
        feasible_region.longitudinal_range.start_pos;
    if (feasible_region.longitudinal_range.start_pos >
            ego_param.front_bumper_arclength &&
        feasible_region_length > half_ego_length) {
      const size_t new_start_index =
          feasible_region_boundary.GetSegmentIndex(half_ego_length);
      auto temp_polyline_points = feasible_region_boundary.points();
      temp_polyline_points.erase(
          temp_polyline_points.begin(),
          temp_polyline_points.begin() + new_start_index);
      return math::geometry::PolylineCurve2d(std::move(temp_polyline_points));
    }
    return feasible_region_boundary;
  }

  const math::geometry::PolylineCurve2d& lane_marking_line =
      lateral_decision == pb::SnapshotIntention::PASS_LEFT
          ? lane_sequence_geometry.right_lane_boundary
          : lane_sequence_geometry.left_lane_boundary;

  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;
  const math::geometry::Point2d start_point =
      nominal_path.GetInterp(feasible_region.longitudinal_range.start_pos);
  const math::geometry::Point2d end_point =
      nominal_path.GetInterp(feasible_region.longitudinal_range.end_pos);
  const math::ProximityQueryInfo start_proximity =
      lane_marking_line.GetProximity(start_point,
                                     math::pb::UseExtensionFlag::kAllow);
  const math::ProximityQueryInfo end_proximity = lane_marking_line.GetProximity(
      end_point, math::pb::UseExtensionFlag::kAllow);
  // The number of sample points should not less than  2.
  const double interval =
      std::min((end_proximity.arc_length - start_proximity.arc_length) / 2,
               /*default_interval=*/0.5);
  const std::vector<math::geometry::Point2d> points_in_range =
      lane_marking_line.GetSampledPoints(start_proximity.arc_length,
                                         end_proximity.arc_length, interval,
                                         math::pb::UseExtensionFlag::kForbid);
  // Assumes that the lane lines are uniformly linear.
  const math::ProximityQueryInfo front_proximity = nominal_path.GetProximity(
      points_in_range.front(), math::pb::UseExtensionFlag::kForbid);
  const math::ProximityQueryInfo back_proximity = nominal_path.GetProximity(
      points_in_range.back(), math::pb::UseExtensionFlag::kForbid);
  const double signed_dist_lane_marking_to_curve =
      front_proximity.dist < back_proximity.dist ? front_proximity.signed_dist
                                                 : back_proximity.signed_dist;
  const double signed_feasible_region_dist_to_curve =
      lateral_decision == pb::SnapshotIntention::PASS_LEFT
          ? feasible_region.lateral_range.start_pos
          : feasible_region.lateral_range.end_pos;

  // Returns the most restrictive boundary as repulsion polyline based on lane
  // marking and feasible region boundary.
  DCHECK_GT(points_in_range.size(), 1);
  const bool should_return_lane_marking =
      lateral_decision == pb::SnapshotIntention::PASS_RIGHT
          ? signed_dist_lane_marking_to_curve <
                signed_feasible_region_dist_to_curve
          : signed_dist_lane_marking_to_curve >
                signed_feasible_region_dist_to_curve;

  return should_return_lane_marking
             ? math::geometry::PolylineCurve2d(points_in_range)
             : feasible_region_boundary;
}

// Returns the number of overtaken objects.
int GetOvertakenObjectCount(
    const std::map<TypedObjectId, path::ObjectReasoningInfo>&
        object_reasoning_info_map,
    const pb::IntentionResult& intention_result,
    const TypedObjectId& interested_object_id) {
  int count = 0;

  auto count_overtaken =
      [](const google::protobuf::Map<int64_t, pb::EgoIntention>& intentions) {
        return std::count_if(
            intentions.begin(), intentions.end(), [](const auto& pair) {
              return pair.second.is_overtaken() && pair.second.is_static();
            });
      };

  count += count_overtaken(intention_result.object_intentions());
  count += count_overtaken(intention_result.construction_zone_intentions());

  // For the almost stationary interested object, we increase the count if it is
  // overtaken.
  const auto object_reasoning_info_iter =
      object_reasoning_info_map.find(interested_object_id);
  const bool is_almost_stationary =
      object_reasoning_info_iter != object_reasoning_info_map.end() &&
      object_reasoning_info_iter->second.IsAlmostStationaryVehicle();
  if (is_almost_stationary) {
    const auto intention_iter =
        intention_result.object_intentions().find(interested_object_id.id);
    if (intention_iter != intention_result.object_intentions().end() &&
        intention_iter->second.is_overtaken()) {
      constexpr int kMaxDefaultOvertakenObjectsCount = 1000;
      count += kMaxDefaultOvertakenObjectsCount;
    }
  }

  return count;
}

std::optional<path::PathReasoningResult> FindOptimalPathReasoningResult(
    const std::map<TypedObjectId, path::ObjectReasoningInfo>&
        object_reasoning_info_map,
    const std::vector<path::PathReasoningResult>& path_reasoning_results,
    const TypedObjectId& interested_object_id) {
  // Only for XLane nudge.
  auto is_valid = [](const path::PathReasoningResult& result) {
    return result.intention_result.homotopy() ==
               pb::IntentionResult::XLANE_PASS_FROM_LEFT ||
           result.intention_result.homotopy() ==
               pb::IntentionResult::XLANE_PASS_FROM_RIGHT;
  };

  std::vector<path::PathReasoningResult> valid_results;
  valid_results.reserve(path_reasoning_results.size());
  std::copy_if(path_reasoning_results.begin(), path_reasoning_results.end(),
               std::back_inserter(valid_results), is_valid);

  if (valid_results.empty()) {
    return std::nullopt;
  }

  // Returns the result with the most overtaken objects.
  return *std::max_element(
      valid_results.begin(), valid_results.end(),
      [&object_reasoning_info_map, &interested_object_id](
          const path::PathReasoningResult& lhs,
          const path::PathReasoningResult& rhs) {
        return GetOvertakenObjectCount(object_reasoning_info_map,
                                       lhs.intention_result,
                                       interested_object_id) <
               GetOvertakenObjectCount(object_reasoning_info_map,
                                       rhs.intention_result,
                                       interested_object_id);
      });
}

void RemoveNudgeAndRepulsionForOncomingAgentAndObject(
    const path::NarrowMeetingSceneParams& narrow_meeting_params,
    const std::map<TypedObjectId, path::ObjectReasoningInfo>&
        object_reasoning_info_map,
    path::PathReasoningResult& path_reasoning_result) {
  // 1.1 Removes nudge constraints.
  const double end_arclength_of_optimal_feasible_region =
      narrow_meeting_params
          .feasible_regions[narrow_meeting_params.optimal_feasible_region_index]
          .longitudinal_range.end_pos;
  std::vector<int64_t> removed_ids_for_agent_constraint;
  removed_ids_for_agent_constraint.reserve(
      path_reasoning_result.agent_constraints.size());
  path_reasoning_result.agent_constraints.erase(
      std::remove_if(
          path_reasoning_result.agent_constraints.begin(),
          path_reasoning_result.agent_constraints.end(),
          [&narrow_meeting_params, &removed_ids_for_agent_constraint,
           &object_reasoning_info_map,
           end_arclength_of_optimal_feasible_region](
              const AgentConstraint& agent_constraint) {
            const auto& oncoming_vehicle_id =
                narrow_meeting_params.oncoming_vehicle_id;
            const auto it = object_reasoning_info_map.find(oncoming_vehicle_id);
            const bool is_almost_stationary =
                it != object_reasoning_info_map.end() &&
                it->second.IsAlmostStationaryVehicle();
            const bool is_oncoming_vehicle =
                agent_constraint.object_id == oncoming_vehicle_id.id;
            const bool is_after_region =
                agent_constraint.tracked_snapshot.start_arclength_m >=
                end_arclength_of_optimal_feasible_region;
            const bool should_remove =
                (is_oncoming_vehicle && !is_almost_stationary) ||
                is_after_region;
            if (should_remove) {
              removed_ids_for_agent_constraint.push_back(
                  agent_constraint.object_id);
            }
            return should_remove;
          }),
      path_reasoning_result.agent_constraints.end());
  // 1.2 Removes the corresponding intention decision debug.
  auto* mutable_intentions =
      path_reasoning_result.intention_result.mutable_object_intentions();
  for (int64_t removed_id : removed_ids_for_agent_constraint) {
    mutable_intentions->erase(removed_id);
  }

  // 2.1 Removes repulsion constraints.
  // TODO(wanghao): 2.2 Removes related repulsion constraints debug info.
  const std::map<std::pair<TypedObjectId, pb::AgentReasonerId>,
                 SingleObjectRepulsionsGeneratingInfo>&
      single_object_repulsions =
          path_reasoning_result.homotopic_constraint_manager->repulsions()
              .object_repulsions_generating_infos;
  std::vector<TypedObjectId> removed_ids_for_repulsion;
  removed_ids_for_repulsion.reserve(single_object_repulsions.size());
  for (const auto& single_object_repulsion : single_object_repulsions) {
    const TypedObjectId& object_id = single_object_repulsion.first.first;
    bool should_remove =
        object_id == narrow_meeting_params.oncoming_vehicle_id ||
        (object_reasoning_info_map.find(object_id) !=
             object_reasoning_info_map.end() &&
         object_reasoning_info_map.at(object_id).full_body_start_arclength() >
             end_arclength_of_optimal_feasible_region);
    if (should_remove) {
      removed_ids_for_repulsion.push_back(object_id);
    }
  }
  for (const TypedObjectId& removed_id : removed_ids_for_repulsion) {
    path_reasoning_result.homotopic_constraint_manager->RemoveRepulsionForAgent(
        removed_id);
  }
}

std::vector<path::FrenetPoint> MaybeCreateTrafficQueueRepulsionPoints(
    const path::FrameAnalyzerResult& frame_analyzer_result) {
  const auto& frame_type = frame_analyzer_result.frame_type;
  DCHECK(frame_type == pb::FrameAnalysis::LEFT_LANE ||
         frame_type == pb::FrameAnalysis::RIGHT_LANE);

  const auto& traffic_queue_data =
      frame_analyzer_result.traffic_flow_info.near_static_traffic_queue_data;
  const double lateral_buffer_meters =
      frame_type == pb::FrameAnalysis::LEFT_LANE ? -0.5 : 0.5;

  std::vector<path::FrenetPoint> result;
  // Not a valid traffic queue.
  if (traffic_queue_data.end_s() - traffic_queue_data.start_s() <
      math::constants::kEpsilon) {
    return result;
  }

  result.reserve(2);
  result.emplace_back(traffic_queue_data.start_s(),
                      traffic_queue_data.l() + lateral_buffer_meters);
  result.emplace_back(traffic_queue_data.end_s(),
                      traffic_queue_data.l() + lateral_buffer_meters);
  return result;
}
}  // namespace

void GenerateLaneSequenceGeometryDebug(
    const lane_selection::LaneSequenceGeometry& lane_geometry,
    pb::LaneSequenceGeometryDebug* lane_geometry_debug) {
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_GenerateLaneSequenceGeometryDebug);
  math::geometry::Convert(lane_geometry.nominal_path.polyline(),
                          *lane_geometry_debug->mutable_nominal_path());
  math::geometry::Convert(lane_geometry.left_lane_boundary,
                          *lane_geometry_debug->mutable_left_lane_boundary());
  math::geometry::Convert(lane_geometry.right_lane_boundary,
                          *lane_geometry_debug->mutable_right_lane_boundary());
  math::geometry::Convert(
      lane_geometry.left_soft_road_boundary,
      *lane_geometry_debug->mutable_left_soft_road_boundary());
  math::geometry::Convert(
      lane_geometry.right_soft_road_boundary,
      *lane_geometry_debug->mutable_right_soft_road_boundary());

  for (const auto& left_hard_road_boundary_line :
       lane_geometry.left_hard_boundary_lines) {
    pb::HardRoadBoundaryLine* hard_boundary_line =
        lane_geometry_debug->add_left_hard_road_boundary_lines();
    math::geometry::Convert(left_hard_road_boundary_line,
                            *hard_boundary_line->mutable_points());
  }
  for (const auto& right_hard_road_boundary_line :
       lane_geometry.right_hard_boundary_lines) {
    pb::HardRoadBoundaryLine* hard_boundary_line =
        lane_geometry_debug->add_right_hard_road_boundary_lines();
    math::geometry::Convert(right_hard_road_boundary_line,
                            *hard_boundary_line->mutable_points());
  }
}

std::vector<PathGenerationOption> RunPathReasoning(
    const WorldModel& world_model,
    const RequiredLateralGapInfo& /* required_lateral_gap_info */,
    const int /* guidance_index */, const int extra_path_options_number,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const std::unordered_set<int64_t>& ignore_agent_ids,
    const path::LeftRightSteeringPathMotionMap& steering_path_motion_map,
    const BehaviorRelevantObjectsInfo& behavior_relevant_objects_info,
    const GeometricGuidanceResult& guidance,
    pb::LaneSequenceInfoAndIntentPlanDebug* debug) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_RunPathReasoning);
  DCHECK(!guidance.speed_upper_bound.empty())
      << "RunPathReasoning: empty speed upper bound.";
  // Note(yongbing): guidance.lane_sequence_geometry and
  // lane_sequence_info->lane_sequence_geometry() may be different. This is just
  // to show the lane sequence geometry received by intention.
  pb::LaneSequenceGeometryDebug* lane_sequence_geometry_debug = nullptr;
  if (debug != nullptr) {
    lane_sequence_geometry_debug =
        debug->mutable_lane_sequence_info()->mutable_lane_sequence_geometry();
  }
  GenerateLaneSequenceGeometryDebug(guidance.lane_sequence_geometry,
                                    lane_sequence_geometry_debug);

  path::NudgeMotionChecker nudge_motion_checker(
      guidance.lane_sequence_info->lane_sequence_geometry().nominal_path,
      world_model.robot_state().plan_init_state_snapshot());
  // TODO(hongda): Currently the ego motion only handles the forward behavior,
  // add reverse motion sampling for the Reverse behavior.
  if (guidance.behavior_type != pb::BehaviorType::REVERSE) {
    TRACE_EVENT_SCOPE(planner, RunPathReasoning_InitializeMotionEngine);
    auto task1 = [&]() {
      for (auto& [combined_motion_index, left_steering_path_boundary] :
           steering_path_motion_map.left_steering_path_boundaries) {
        nudge_motion_checker.UpdateSLNudgeBoundary(combined_motion_index,
                                                   left_steering_path_boundary,
                                                   /*is_steering_left=*/true);
      }
    };
    auto task2 = [&]() {
      for (auto& [combined_motion_index, right_steering_path_boundary] :
           steering_path_motion_map.right_steering_path_boundaries) {
        nudge_motion_checker.UpdateSLNudgeBoundary(combined_motion_index,
                                                   right_steering_path_boundary,
                                                   /*is_steering_left=*/false);
      }
    };
    tbb::parallel_invoke(task1, task2);
  }

  const double avg_abs_ref_path_curvature = CalculateAvgAbsRefPathCurvature(
      guidance.reference_path,
      guidance.lane_sequence_info->robot_in_lane_param()
          .front_bumper_arclength);

  std::optional<path::PullOverStatusMetaData> optional_pullover_status_meta;
  const pull_over::DestinationMetaData* pull_over_destination_meta_ptr =
      guidance.pull_over_reasoning_input.destination_meta_ptr;
  if (pull_over_destination_meta_ptr != nullptr) {
    optional_pullover_status_meta =
        std::make_optional(path::PullOverStatusMetaData{
            pull_over_destination_meta_ptr->destination_pt(),
            pull_over_destination_meta_ptr->jump_in_guidance,
            pull_over_destination_meta_ptr->is_in_bus_bulb,
            pull_over_destination_meta_ptr->reference_path()});
  }

  IntentionGenerator intention_generator(guidance.behavior_type,
                                         guidance.lane_keep_behavior_type);

  auto path_reasoning_results = intention_generator.Generate(
      *guidance.lane_sequence_info,
      world_model.robot_state().plan_init_state_snapshot(),
      guidance.speed_lower_bound, guidance.speed_upper_bound,
      *guidance.semantic_context_result, guidance.drivable_space_corridor,
      reusable_last_path, reusable_last_speed_profile,
      optional_pullover_status_meta, guidance.reverse_driving_destination,
      world_model.trajectory_guider_output(), ignore_agent_ids,
      guidance.lane_change_execution_info, previous_iter_seed.speed_seed(),
      previous_iter_seed.path_reasoner_seed(), &nudge_motion_checker,
      world_model.global_object_manager_ptr()->is_high_crossing_vru_density(),
      *DCHECK_NOTNULL(guidance.constraint_manager),
      world_model.pnc_map_service()->hdmap()->config().name(),
      debug == nullptr ? nullptr : debug->mutable_intention_generator_debug());

  std::vector<PathGenerationOption> path_generation_options;
  path_generation_options.reserve(path_reasoning_results.size() +
                                  extra_path_options_number);
  if (debug != nullptr) {
    debug->mutable_single_intention_plan_debug()->Reserve(
        path_reasoning_results.size() + extra_path_options_number);
  }

  const bool is_ego_close_to_u_turn =
      DistanceToTurnLane(
          world_model.pose(),
          guidance.lane_sequence_info->lane_sequence_iterator().lane_sequence(),
          hdmap::Lane::U_TURN) < kArcLengthThresholdForCloseToUTurnInMeter;

  if (guidance.lane_keep_behavior_type == pb::LK_DEFAULT) {
    DCHECK(guidance.behavior_type == pb::LANE_KEEP);
  }
  // TODO(sixian): Better organize how we would like to add non-agent
  // constraint.
  std::vector<path::FrenetPoint> exit_zone_repulsion_points;
  const pnc_map::Lane* current_lane_ptr =
      (*guidance.lane_sequence_info->lane_sequence_iterator().current_lane());
  DCHECK(current_lane_ptr != nullptr);
  // Pull out could be close to exit zone, so we will not add exit zone
  // repulsion during pull out.
  const bool is_pullout_ready_to_go = previous_iter_seed.speed_seed()
                                          .speed_reasoner_seeds()
                                          .pullout_reasoner_seed()
                                          .is_ready_to_go();
  if (guidance.behavior_type == pb::LANE_KEEP && !is_pullout_ready_to_go &&
      current_lane_ptr->IsRightmostVehicleLane() &&
      !current_lane_ptr->IsLeftmostDrivableLane()) {
    TRACE_EVENT_SCOPE(planner, RunPathReasoning_AddExitZoneRepulsion);
    // If ego is on the rightmost lane and not on the leftmost lane in the
    // road, we would like to add some minor repulsion to help ego drive to the
    // left slightly for exit zone on ego's right。
    const auto* first_exit_zone =
        GetFirstExitZone(guidance.lane_sequence_info->traffic_rules(),
                         guidance.lane_sequence_info->robot_in_lane_param()
                             .front_bumper_arclength);
    if (first_exit_zone != nullptr) {
      // ----------------------------------------------------------------------
      //
      //
      //
      // -------------------------------------------------------------- ref path
      //                    | 0.9m
      //                   ______   <- Added repulsion for exit zone cautious
      //                   |<5m>|
      // ------------------------           -----------------------------------
      //                        |   Exit    |
      //                        |   Zone    |
      //                        |           |
      exit_zone_repulsion_points.reserve(2);
      // 5m in s before the exit zone for some heads up
      // -0.9m in l (shift to the left by ~15cm) as a defensive strategy for
      // exit zone
      exit_zone_repulsion_points.emplace_back(
          first_exit_zone->start_arclength - 5.0, -0.9);
      exit_zone_repulsion_points.emplace_back(first_exit_zone->start_arclength,
                                              -0.9);
    }
  }

  std::vector<path::FrenetPoint> left_traffic_repulsion_points =
      MaybeCreateTrafficQueueRepulsionPoints(
          guidance.semantic_context_result
              ->frame_analyzer_results[pb::FrameAnalysis::LEFT_LANE]);
  std::vector<path::FrenetPoint> right_traffic_repulsion_points =
      MaybeCreateTrafficQueueRepulsionPoints(
          guidance.semantic_context_result
              ->frame_analyzer_results[pb::FrameAnalysis::RIGHT_LANE]);

  // Adds a path options for narrow meeting scenario.
  if (FLAGS_planning_enable_narrow_meeting_process) {
    const path::NarrowMeetingSceneParams& params =
        guidance.semantic_context_result->narrow_meeting_params;
    if (params.trigger_narrow_meeting &&
        params.optimal_feasible_region_index != -1) {
      // Returns the xlane nudge with the highest number of overtaken objects.
      const auto& object_reasoning_info_map =
          guidance.semantic_context_result->object_reasoning_info_map;
      auto optimal_result = FindOptimalPathReasoningResult(
          object_reasoning_info_map, path_reasoning_results,
          params.oncoming_vehicle_id);
      if (optimal_result.has_value()) {
        DCHECK(params.lateral_decision == pb::SnapshotIntention::PASS_LEFT ||
               params.lateral_decision == pb::SnapshotIntention::PASS_RIGHT);
        // Removes the nudge and repulsion constraint for key oncoming agent and
        // the object ahead of the optimal feasible region in the optimal path
        // reasoning result, which benifits the ego vehicle to drill into the
        // feasible region.
        RemoveNudgeAndRepulsionForOncomingAgentAndObject(
            params, guidance.semantic_context_result->object_reasoning_info_map,
            *optimal_result);

        // Generates a repulsion boundary that pushes ego vehicle into the ego
        // lane while leaving lateral space for the oncoming agent.
        const math::geometry::PolylineCurve2d boundary =
            GenerateRepulsionBoundaryBasedOnLaneMarking(
                guidance.lane_sequence_info->robot_in_lane_param(),
                guidance.lane_sequence_geometry, params.lateral_decision,
                params.feasible_regions[params.optimal_feasible_region_index]);
        const bool is_left_boundary =
            params.lateral_decision == pb::SnapshotIntention::PASS_RIGHT;
        optimal_result->homotopic_constraint_manager->AddRepulsion(
            boundary,
            FLAGS_planning_enable_strong_repulsion_in_narrow_meeting
                ? path::BoundaryStrength::kStrong
                : path::BoundaryStrength::kModerate,
            /*is_left=*/is_left_boundary, /*boundary_source=*/"narrow_meeting",
            ToProto(params.oncoming_vehicle_id),
            /*trajectory_id=*/std::nullopt,
            /*strength_weight_ratio=*/1.0,
            /*repulsion_weight_decay_info=*/
            std::nullopt,
            /*extend_boundary_level=*/
            RepulsionMetaData::ExtendBoundaryLevel::kNA,
            /*clearance=*/std::nullopt,
            /*max_extend_boundary_distance=*/std::nullopt,
            /*lateral_bound=*/std::nullopt);
        optimal_result->is_duplicate = false;
        path_generation_options.push_back(PathGenerationOption{
            .geometric_constraint = CopyGeometricGuidanceResult(guidance),
            .path_reasoning_result = *optimal_result,
            .lane_sequence_debug = debug,
            .intention_plan_debug =
                debug ? debug->add_single_intention_plan_debug() : nullptr,
            .need_to_copy_extra_ml_trajectory = false,
            .behavior_relevant_objects_info =
                std::move(behavior_relevant_objects_info),
            .ml_planner_meta = std::nullopt});
      }
    }
  }

  {
    TRACE_EVENT_SCOPE(planner, RunPathReasoning_PostReasoningProcessing);
    VOY_LATENCY_STAT_RECORD_PLANNER_S1(
        LAT_STAT_RunPathReasoning_PostReasoningProcessing);
    for (size_t i = 0; i < path_reasoning_results.size(); ++i) {
      if (path_reasoning_results[i].is_duplicate) {
        continue;
      }
      pb::IntentionPlanDebug* intention_plan_debug_ptr = nullptr;
      if (debug != nullptr) {
        intention_plan_debug_ptr = debug->add_single_intention_plan_debug();
      }
      // TODO(yongbing, guanao): build up a boundary reasoning pipeline. For
      // every homotopy, the boundary attributes might be different based on
      // specific scenerio and environment. Add the following DCHECK after new
      // api is stable: BoundaryReason(guidance.road_boundaries);
      path_generation_options.push_back(PathGenerationOption{
          .geometric_constraint = CopyGeometricGuidanceResult(guidance),
          .path_reasoning_result = std::move(path_reasoning_results[i]),
          .lane_sequence_debug = debug,
          .intention_plan_debug = intention_plan_debug_ptr,
          .need_to_copy_extra_ml_trajectory = false,
          .behavior_relevant_objects_info =
              std::move(behavior_relevant_objects_info),
          .ml_planner_meta = std::nullopt});

      // Updates physical boundary required lateral gap.
      auto& path_reasoning_result =
          path_generation_options.back().path_reasoning_result;
      auto& guidance_used = path_generation_options.back().geometric_constraint;
      if (guidance_used.behavior_type != pb::DECOUPLED_PULL_OVER &&
          !is_ego_close_to_u_turn) {
        // Use physical principle lateral gap to calculate required lateral gap
        // based on Ego current speed. Only apply this when it is not pull over
        // maneuver for now.
        UpdatePhysicalBoundaryRequiredLateralGap(
            avg_abs_ref_path_curvature,
            guidance_used.lane_sequence_info->robot_in_lane_param().speed_mps,
            *(path_reasoning_result.homotopic_constraint_manager
                  ->mutable_lateral_clearance_data()));
      }

      // Updates ML Path attraction
      if (ShouldEnableMLAttraction(
              world_model.robot_state().plan_init_state_snapshot().speed(),
              guidance, *path_reasoning_result.homotopic_constraint_manager)) {
        TRACE_EVENT_SCOPE(planner, RunPathReasoning_AddMlAttraction);
        DCHECK(path_reasoning_result.homotopic_constraint_manager != nullptr);
        // Select the best match ml path.
        const std::optional<math::geometry::PolylineCurve2d> ml_path =
            SelectMLPath(world_model.trajectory_guider_output(),
                         guidance.lane_sequence_info->lane_sequence_iterator()
                             .lane_sequence(),
                         path_reasoning_result.agent_constraints,
                         intention_plan_debug_ptr);
        // Append ml attraction.
        if (ml_path.has_value() &&
            AppendMLGuidanceAttraction(
                guidance.lane_sequence_info->lane_sequence_iterator()
                    .lane_sequence(),
                world_model.pose(), guidance.lane_sequence_geometry, *ml_path,
                *path_reasoning_result.homotopic_constraint_manager)) {
          // Add ml path debug information and rt event.
          rt_event::PostRtEvent<rt_event::planner::UseMLAttraction>();
          if (intention_plan_debug_ptr != nullptr) {
            ml_path->ToProto(
                intention_plan_debug_ptr->mutable_path_planner_debug()
                    ->mutable_path_smoother_debug()
                    ->mutable_trajectory_guider_output());
            intention_plan_debug_ptr->mutable_path_planner_debug()
                ->set_if_use_trajectory_guider_output(true);
          }
        }
      }

      const pb::IntentionResult::IntentionHomotopy homotopy =
          path_reasoning_result.intention_result.homotopy();
      if ((!exit_zone_repulsion_points.empty())) {
        path_reasoning_result.homotopic_constraint_manager->AddRepulsion(
            exit_zone_repulsion_points, path::BoundaryStrength::kModerate,
            /*max_abs_lateral_accel=*/std::nullopt,
            /*max_abs_lateral_juke=*/std::nullopt,
            /*is_left=*/false, "Exit Zone",
            /*object_id=*/std::nullopt, /*trajectory_id=*/std::nullopt);
      }

      if ((!left_traffic_repulsion_points.empty())) {
        path_reasoning_result.homotopic_constraint_manager->AddRepulsion(
            left_traffic_repulsion_points, path::BoundaryStrength::kWeak,
            /*max_abs_lateral_accel=*/std::nullopt,
            /*max_abs_lateral_juke=*/std::nullopt,
            /*is_left=*/true, "Left Traffic Queue",
            /*object_id=*/std::nullopt, /*trajectory_id=*/std::nullopt);
        rt_event::PostRtEvent<rt_event::planner::TrafficQueueRepulsion>();
      }
      if ((!right_traffic_repulsion_points.empty())) {
        path_reasoning_result.homotopic_constraint_manager->AddRepulsion(
            right_traffic_repulsion_points, path::BoundaryStrength::kWeak,
            /*max_abs_lateral_accel=*/std::nullopt,
            /*max_abs_lateral_juke=*/std::nullopt,
            /*is_left=*/false, "Right Traffic Queue",
            /*object_id=*/std::nullopt, /*trajectory_id=*/std::nullopt);
        rt_event::PostRtEvent<rt_event::planner::TrafficQueueRepulsion>();
      }
      if (ShouldAppendPullOverProbingAttractionInfo(
              world_model.robot_state(), guidance, path_reasoning_result)) {
        path_generation_options.back().is_pull_over_probing_executed = true;
        DCHECK_NE(guidance.pull_over_reasoning_input.probe_decision_ptr,
                  nullptr);
        AppendPullOverProbingAttractionInfo(
            *guidance.pull_over_reasoning_input.probe_decision_ptr,
            path_reasoning_result.homotopic_constraint_manager.get());
        rt_event::PostRtEvent<rt_event::planner::PulloverProbingIsExecuted>();
      }
      if (ShouldAppendLaneChangeProbingAttractionInfo(guidance, homotopy) &&
          guidance.lane_change_probing_info.has_value()) {
        AppendLaneChangeProbingAttractionInfo(
            guidance.lane_change_probing_info.value(),
            path_reasoning_result.homotopic_constraint_manager.get());
      }
      if (ShouldAppendInLaneReasoningBoundary(guidance_used, homotopy)) {
        TRACE_EVENT_SCOPE(planner, RunPathReasoning_AddReasoningBoundary);
        // Append in_lane reasoning boundary to non-pullout-jump-out, lane_keep
        // behavior with ignore_all homotopy. This boundary is mainly to help
        // EGO stay around the center-line of the lane.
        AppendInLaneReasoningBoundary(
            world_model.robot_state().plan_init_state_snapshot(),
            guidance_used.lane_sequence_geometry,
            *path_reasoning_result.homotopic_constraint_manager);
      }
    }
  }

  return path_generation_options;
}

}  // namespace planner
