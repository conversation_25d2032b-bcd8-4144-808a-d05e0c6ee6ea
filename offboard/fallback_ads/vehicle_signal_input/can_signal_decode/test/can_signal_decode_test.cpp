#include "vehicle_signal_input/can_signal_decode/can_signal_decode.h"

#include <gtest/gtest.h>

#include "_out/Appl/GenData/Components/Rte_VehSigInput_Type.h"
#include "offboard/fallback_ads/fallback_simulation.h"
#include "vehicle_signal_input/can_timeout_detection/can_timeout_detection.h"
#include "vehicle_signal_input/vehicle_signal_input_parameter.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

static Rte_tsMainTask_Core0_10ms CreateChassisInput() {
  Rte_tsMainTask_Core0_10ms rte_main_task_core0_10ms_test = {};

  auto& chassis_input = rte_main_task_core0_10ms_test.Rte_RB
                            .Rte_VehSigInput_VehSigInput_10ms_Runnable;

  chassis_input.Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe.value
      .PrimVehSpdGroupSafeNom = 15.0F / 0.01F;  // 15.0 m/s

  chassis_input.Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe.value
      .PrimVehSpdGroupSafeNomQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr03_SG_PrimVLatSafe.value.PrimVLatSafeNom =
      0.9F / 0.001F;  // 0.9 m/s

  chassis_input.Rte_SSMMid3CanFr11_SG_PrimALgtDataRawSafe.value
      .PrimALgtDataRawSafeNom = (1.0F + 16.384F) / 0.001F;  // 1.0 m/s^2

  chassis_input.Rte_SSMMid3CanFr11_SG_PrimALgtDataRawSafe.value
      .PrimALgtDataRawSafeNomQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe.value
      .PrimALatDataRawSafeNom = (0.8F + 16.384F) / 0.001F;  // 0.8 m/s^2

  chassis_input.Rte_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe.value
      .PrimALatDataRawSafeNomQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr03_SG_PrimYawRateSafe.value
      .PrimYawRateSafeNom = 0.2F / 0.0002F;  // 0.1 rad/s

  chassis_input.Rte_VCU1Mid3CanFr08_SG_AgDataRawSafe.value.YawRate1 =
      0.1F / 0.000244140625F;  // 0.1 rad/s

  chassis_input.Rte_VCU1Mid3CanFr08_SG_AgDataRawSafe.value.YawRate1Qf1 =
      Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe.value
      .AdPrimWhlAgEstimdGroupSafeWhlAg = (0.5F + 0.85F) / 5.249E-5F;  // 0.5 rad

  chassis_input.Rte_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe.value
      .AdPrimWhlAgEstimdGroupSafeWhlAgRate =
      (0.4F + 0.7F) / 5.0E-5F;  // 0.4 rad/s

  chassis_input.Rte_VCU1Mid3CanFr09_SG_SteerWhlSnsr.value.SteerWhlAgSafe =
      1.0F / 0.0009765625F;  // 1.0 rad

  chassis_input.Rte_VCU1Mid3CanFr09_SG_SteerWhlSnsr.value.SteerWhlAgSpdSafe =
      1.2F / 0.0078125F;  // 1.2 rad/s

  chassis_input.Rte_VCU1Mid3CanFr09_SG_SteerWhlSnsr.value.SteerWhlSnsrQf =
      Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value
      .PrimWhlAgSpdFrntSafeLe = 1.1F / 0.0078125F;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value
      .PrimWhlAgSpdFrntSafeLeQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value
      .PrimWhlAgSpdFrntSafeRi = 1.0F / 0.0078125F;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value
      .PrimWhlAgSpdFrntSafeRiQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe.value
      .PrimWhlAgSpdReSafeLe = 0.9F / 0.0078125F;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe.value
      .PrimWhlAgSpdReSafeLeQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe.value
      .PrimWhlAgSpdReSafeRi = 0.8F / 0.0078125F;

  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe.value
      .PrimWhlAgSpdReSafeRiQf = Cx3_GenQf1_AccurData;

  chassis_input.Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe.value
      .PrimVehSpdGroupSafeMovDir = Cx3_MovDir1_Fwd;

  chassis_input.Rte_VCU1Mid3CanFr36_SG_WhlLockSts.value.WhlLockStsLockSts =
      Cx1_WhlLockSts1_AllWhlsReld;

  chassis_input.Rte_VCU1Mid3CanFr03_SG_PrpsnTqDir.value.PrpsnTqDirAct =
      Cx1_PrpsnDirAct1_Fwd;

  chassis_input.Rte_VCU1Mid3CanFr14_IndcrTurnSts1WdSts.value =
      Cx1_IndcrSts1_LeOn;

  chassis_input.Rte_VCU1Mid3CanFr13_AdActvnOkFromVehDyn.value = Cx1_OnOff1_On;

  chassis_input.Rte_VCU1Mid3CanFr30_SG_AutnmsDrvModMngtExtSafe.value
      .AutnmsDrvModMngtExtSafeAutnmsDrvModSts1 = Cx1_ActvSt_Actv;

  // vcan10, acu to  mcu
  chassis_input.Rte_AcuFbAdsStatus_AcuFbReserved.value = 2U;
  chassis_input.Rte_AcuFbAdsStatus_AcuFbRollingCounter.value = 3U;

  // vcan4, acu to mcu, redundant communication
  chassis_input.Rte_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A.value
      .AdNomALgtReqGroupSafeCntr_A = 4U;
  chassis_input.Rte_VIMMid3CanFr07_ACU_SG_AutnmsDrvStReq_A.value
      .AutnmsDrvStReqCounter_A = 2U;

  // collision detection result from EH
  chassis_input.Rte_AcuFbAdsStatus_ReqResleaseFbControl.value = 2U;

  // check can timeout
  chassis_input.Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe.value
      .PrimVehSpdGroupSafeCntr = 5U;

  // vehicle mid3 ssm to mcu counter0
  chassis_input.Rte_SSMMid3CanFr11_SG_PrimALgtDataRawSafe.value
      .PrimALgtDataRawSafeCntr = 6U;

  // vehicle mid3 vcu to mcu counter0
  chassis_input.Rte_VCU1Mid3CanFr08_SG_AgDataRawSafe.value.AgDataRawSafeCntr =
      7U;

  // vehicle mid3 vcu to mcu counter1
  chassis_input.Rte_VCU1Mid3CanFr02_SG_BrkTqMinReq.value
      .BrkTqMinReqBrkTqMinReqCntr = 8U;

  // vehicle mid5 ssm to mcu counter0
  chassis_input.Rte_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe.value
      .AdPrimWhlAgEstimdGroupSafeCntr = 9U;

  // vehicle mid5 ssm to mcu counter1
  chassis_input.Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe.value
      .PrimWhlAgSpdFrntSafeCntr = 10U;

  // vehicle mid6 ssm to mcu counter0
  chassis_input.Rte_SSMBMid6CanFdFr04_SG_BrkDegradedRdnt.value
      .BrkDegradedRdntCntr = 11U;

  // vehicle mid6 ssm to mcu counter1
  chassis_input.Rte_SSMBMid6CanFdFr01_SG_AdSecSteerStsSafeGroup.value
      .AdSecSteerStsSafeGroupCntr = 10U;

  // acu mid3 ssm to mcu counter0
  // chassis_input.Rte_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A.value
  //     .AdNomALgtReqGroupSafeCntr_A = 9U;

  // acu mid3 vim to mcu counter1
  chassis_input.Rte_VIMMid3CanFr15_ACU_SG_AdPrimALgtLimReqGroupSafe_A.value
      .AdPrimALgtLimReqGroupSafeCntr_A = 8U;

  // acu mid5 vim to mcu counter0
  chassis_input.Rte_VIMMid5CanFdFr02_ACU_SG_AdPrimPose_A.value
      .AdPrimPoseCntr_A = 7U;

  // acu mid5 vim to mcu counter1
  chassis_input.Rte_VIMMid5CanFdFr12_ACU_SG_AdPrimWhlAgReqGroupSafe_A.value
      .AdPrimWhlAgReqGroupSafeCntr_A = 6U;

  // acu mid6 vim to mcu counter0
  chassis_input.Rte_VIMBMid6CanFdFr28_ACU_SG_AdSecALgtLimReqGroupSafe_A.value
      .AdSecALgtLimReqGroupSafeCntr_A = 5U;

  // acu mid6 vim to mcu counter1
  chassis_input.Rte_VIMBMid6CanFdFr14_ACU_SG_AdSecWhlAgReqGroupSafe_A.value
      .AdSecWhlAgReqGroupSafeCntr_A = 4U;

  return rte_main_task_core0_10ms_test;
}

TEST(VehicleChassisInfoMapping, VehicleChassisInfoMappingTest) {
  Rte_MainTask_Core0_10ms = CreateChassisInput();
  VehicleAndAcuCanSignal can_signal = {};
  ConvertVehicleAndAcuDbcSignals(&can_signal);

  ASSERT_NEAR(can_signal.PrimVehSpdGroupSafeNom, 15.0F, 0.01F);
  ASSERT_TRUE(can_signal.PrimVehSpdGroupSafeNomQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimVLatSafeNom, 0.9F, 0.01F);
  ASSERT_NEAR(can_signal.PrimALgtDataRawSafeNom, 1.0F, 0.001F);
  ASSERT_TRUE(can_signal.PrimALgtDataRawSafeNomQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimALatDataRawSafeNom, 0.8F, 0.001F);
  ASSERT_TRUE(can_signal.PrimALatDataRawSafeNomQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimYawRateSafeNom, 0.2F, 0.0002F);
  ASSERT_NEAR(can_signal.YawRate1, 0.1F, 0.000244140625F);
  ASSERT_TRUE(can_signal.YawRate1Qf1 == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.AdPrimWhlAgEstimdGroupSafeWhlAg, 0.5F, 5.249E-5F);
  ASSERT_NEAR(can_signal.AdPrimWhlAgEstimdGroupSafeWhlAgRate, 0.4F, 5.0E-5F);
  ASSERT_NEAR(can_signal.SteerWhlAgSafe, 1.0F, 0.0009765625F);
  ASSERT_NEAR(can_signal.SteerWhlAgSpdSafe, 1.2F, 0.0078125F);
  ASSERT_TRUE(can_signal.SteerWhlSnsrQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimWhlAgSpdFrntSafeLe, 1.1F, 0.0078125F);
  ASSERT_TRUE(can_signal.PrimWhlAgSpdFrntSafeLeQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimWhlAgSpdFrntSafeRi, 1.0F, 0.0078125F);
  ASSERT_TRUE(can_signal.PrimWhlAgSpdFrntSafeRiQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimWhlAgSpdReSafeLe, 0.9F, 0.0078125F);
  ASSERT_TRUE(can_signal.PrimWhlAgSpdReSafeLeQf == Cx3_GenQf1_AccurData);
  ASSERT_NEAR(can_signal.PrimWhlAgSpdReSafeRi, 0.8F, 0.0078125F);
  ASSERT_TRUE(can_signal.PrimWhlAgSpdReSafeRiQf == Cx3_GenQf1_AccurData);
  ASSERT_TRUE(can_signal.PrimVehSpdGroupSafeMovDir == Cx3_MovDir1_Fwd);
  ASSERT_TRUE(can_signal.WhlLockStsLockSts == Cx1_WhlLockSts1_AllWhlsReld);
  ASSERT_TRUE(can_signal.PrpsnTqDirAct == Cx1_PrpsnDirAct1_Fwd);
  ASSERT_TRUE(can_signal.IndcrTurnSts1WdSts == Cx1_IndcrSts1_LeOn);
  ASSERT_TRUE(can_signal.AdActvnOkFromVehDyn == Cx1_OnOff1_On);
  ASSERT_TRUE(can_signal.AutnmsDrvModMngtExtSafeAutnmsDrvModSts1 ==
              Cx1_ActvSt_Actv);
  ASSERT_EQ(can_signal.AcuFbReserved, 2U);
  ASSERT_EQ(can_signal.AcuFbRollingCounter, 3U);
  ASSERT_EQ(can_signal.AdNomALgtReqGroupSafeCntr_A, 4U);
  ASSERT_EQ(can_signal.AutnmsDrvStReqCounter_A, 2U);
  ASSERT_EQ(can_signal.ReqResleaseFbControl, 2U);
  ASSERT_EQ(can_signal.PrimVehSpdGroupSafeCntr, 5U);
  ASSERT_EQ(can_signal.PrimALgtDataRawSafeCntr, 6U);
  ASSERT_EQ(can_signal.AgDataRawSafeCntr, 7U);
  ASSERT_EQ(can_signal.BrkTqMinReqBrkTqMinReqCntr, 8U);
  ASSERT_EQ(can_signal.AdPrimWhlAgEstimdGroupSafeCntr, 9U);
  ASSERT_EQ(can_signal.PrimWhlAgSpdFrntSafeCntr, 10U);
  ASSERT_EQ(can_signal.BrkDegradedRdntCntr, 11U);
  ASSERT_EQ(can_signal.AdSecSteerStsSafeGroupCntr, 10U);
  ASSERT_EQ(can_signal.AdPrimALgtLimReqGroupSafeCntr_A, 8U);
  ASSERT_EQ(can_signal.AdPrimPoseCntr_A, 7U);
  ASSERT_EQ(can_signal.AdPrimWhlAgReqGroupSafeCntr_A, 6U);
  ASSERT_EQ(can_signal.AdSecALgtLimReqGroupSafeCntr_A, 5U);
  ASSERT_EQ(can_signal.AdSecWhlAgReqGroupSafeCntr_A, 4U);
}

TEST(GetVehicleMode, GetVehicleModeTest) {
  ASSERT_TRUE(GetVehicleMode(TRUE) == AUTO_FULL);
  ASSERT_TRUE(GetVehicleMode(FALSE) == MANUAL);
}

TEST(HoldEmergencySwitch, HoldEmergencySwitchTest) {
  ASSERT_FALSE(HoldEmergencySwitch(NO_FAULT));
  ASSERT_TRUE(HoldEmergencySwitch(EMERGENCY_SWITCH_ON));
  // delay time is too long, calculation accuracy error
  // 59972 / (600 / 0.01) = 0.99953
  for (int i = 0; i < 59972; ++i) {
    ASSERT_TRUE(HoldEmergencySwitch(NO_FAULT));
  }
  ASSERT_FALSE(HoldEmergencySwitch(NO_FAULT));
}

TEST(GetFallbackMode, GetFallbackModeTest) {
  // can communication is ok
  for (int i = 0; i < 1000; ++i) {
    ASSERT_TRUE(GetFallbackMode(i % 256, SYSTEM_ERROR, i % 256,
                                SYSTEM_ERROR_EB) == SYSTEM_ERROR);
  }

  // main can timeout is currently being detected, redundant can is ok
  for (int i = 0; i < 51; ++i) {
    ASSERT_TRUE(GetFallbackMode(0, SYSTEM_ERROR, i % 256, SYSTEM_ERROR_EB) ==
                SYSTEM_ERROR);
  }
  // main can timeout, redundant can is ok
  ASSERT_TRUE(GetFallbackMode(0, SYSTEM_ERROR, 51, SYSTEM_ERROR_EB) ==
              SYSTEM_ERROR_EB);

  // redundant can timeout is currently being detected, main can timeout
  for (int i = 0; i < 51; ++i) {
    ASSERT_TRUE(GetFallbackMode(0, SYSTEM_ERROR, 0, SYSTEM_ERROR_EB) ==
                SYSTEM_ERROR_EB);
  }
  // main can timeout, redundant can timeout
  ASSERT_TRUE(GetFallbackMode(0, SYSTEM_ERROR_EB, 0, SYSTEM_ERROR_EB) ==
              SYSTEM_ERROR);
  // main can is ok, redundant can timeout
  ASSERT_TRUE(GetFallbackMode(1, SYSTEM_ERROR_EB, 0, SYSTEM_ERROR_EB) ==
              SYSTEM_ERROR_EB);
  // can communication is ok, emergency switch is on
  ASSERT_TRUE(GetFallbackMode(2, EMERGENCY_SWITCH_ON, 1, EMERGENCY_SWITCH_ON) ==
              EMERGENCY_SWITCH_ON);
}

TEST(FallbackIsActive, FallbackIsActiveTest) {
  EXPECT_FALSE(FallbackIsActive(MANUAL, SYSTEM_ERROR, false));
  EXPECT_FALSE(FallbackIsActive(AUTO_FULL, NO_FAULT, false));
  EXPECT_TRUE(FallbackIsActive(AUTO_FULL, SYSTEM_ERROR, false));
  EXPECT_FALSE(FallbackIsActive(AUTO_FULL, SYSTEM_ERROR, true));

  for (int i = 0; i < 15; ++i) {
    EXPECT_TRUE(FallbackIsActive(MANUAL, SYSTEM_ERROR, false));
  }
  EXPECT_FALSE(FallbackIsActive(MANUAL, SYSTEM_ERROR, false));
}

TEST(CollisionDetectionIsValid, CollisionDetectionIsValidTest) {
  ASSERT_TRUE(CollisionDetectionIsValid(0, 0x01));
  ASSERT_FALSE(CollisionDetectionIsValid(1, 0x04));
  for (int i = 0; i < 51; ++i) {
    ASSERT_TRUE(CollisionDetectionIsValid(0, 0x01));
  }
  ASSERT_FALSE(CollisionDetectionIsValid(0, 0x01));
}

TEST(GetAlgorithmRequiredChassisInfo, GetAlgorithmRequiredChassisInfoTest) {
  Rte_MainTask_Core0_10ms = CreateChassisInput();
  VehicleAndAcuCanSignal can_signal = {};
  VSI_VehicleInfo_Struct vehicle_info;
  // clear, especially the rear emergency stop switch
  for (int i = 0; i < 60000; ++i) {
    GetAlgorithmRequiredChassisInfo(&can_signal, &vehicle_info);
  }
  ConvertVehicleAndAcuDbcSignals(&can_signal);
  GetAlgorithmRequiredChassisInfo(&can_signal, &vehicle_info);

  ASSERT_NEAR(vehicle_info.VSI_LongitudinalVelocity, 15.0F, 0.01F);
  ASSERT_NEAR(vehicle_info.VSI_LateralVelocity, 0.9F, 0.01F);
  ASSERT_NEAR(vehicle_info.VSI_LongitudinalAcceleration, 1.0F, 0.001F);
  ASSERT_NEAR(vehicle_info.VSI_LateralAcceleration, 0.8F, 0.001F);
  ASSERT_NEAR(vehicle_info.VSI_FrontWheelSteeringAngle, 0.5F, 5.249E-5F);
  ASSERT_NEAR(vehicle_info.VSI_FrontWheelAngularVelocity, 0.4F, 5.0E-5F);
  ASSERT_NEAR(vehicle_info.VSI_SteeringWheelAngle, 1.0F, 0.0009765625F);
  ASSERT_NEAR(vehicle_info.VSI_SteeringAngularVelocity, 1.2F, 0.0078125F);
  ASSERT_TRUE(vehicle_info.VSI_TurnSignalStatus == Cx1_IndcrSts1_LeOn);
  ASSERT_TRUE(vehicle_info.VSI_MovementDirection == Cx3_MovDir1_Fwd);

  if (GetVsiChooseYawRateFromIMU()) {
    ASSERT_NEAR(vehicle_info.VSI_YawRate, 0.1F, 0.000244140625F);
  } else {
    ASSERT_NEAR(vehicle_info.VSI_YawRate, 0.2F, 0.0002F);
  }

  ASSERT_TRUE(vehicle_info.VSI_VehicleAutofullReady);
  ASSERT_EQ((VehicleMode)vehicle_info.VSI_VehicleMode, AUTO_FULL);
  ASSERT_EQ((FallbackMode)vehicle_info.VSI_FallbackMode, SYSTEM_ERROR);
  ASSERT_FALSE(vehicle_info.VSI_ForwardCollisionWarning);
  ASSERT_TRUE(vehicle_info.VSI_RearCollisionWarning);
}

TEST(GetSensorRequiredChassisInfo, GetSensorRequiredChassisInfoTest) {
  Rte_MainTask_Core0_10ms = CreateChassisInput();
  VehicleAndAcuCanSignal can_signal = {};
  VSI_VehInfoFor1V1R_Struct sensor_chassis_info;
  VSI_VehicleInfo_Struct vehicle_info;
  // clear, especially the rear emergency stop switch
  for (int i = 0; i < 60000; ++i) {
    GetAlgorithmRequiredChassisInfo(&can_signal, &vehicle_info);
  }
  ConvertVehicleAndAcuDbcSignals(&can_signal);
  GetSensorRequiredChassisInfo(&can_signal, &sensor_chassis_info);

  ASSERT_NEAR(sensor_chassis_info.VSI_PrimWhlAgSpdFrntSafeLe, 1.1F, 0.0078125F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimWhlAgSpdFrntSafeLeQf ==
              Cx3_GenQf1_AccurData);
  ASSERT_NEAR(sensor_chassis_info.VSI_PrimWhlAgSpdFrntSafeRi, 1.0F, 0.0078125F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimWhlAgSpdFrntSafeRiQf ==
              Cx3_GenQf1_AccurData);
  ASSERT_NEAR(sensor_chassis_info.VSI_PrimWhlAgSpdReSafeLe, 0.9F, 0.0078125F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimWhlAgSpdReSafeLeQf ==
              Cx3_GenQf1_AccurData);
  ASSERT_NEAR(sensor_chassis_info.VSI_PrimWhlAgSpdReSafeRi, 0.8F, 0.0078125F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimWhlAgSpdReSafeRiQf ==
              Cx3_GenQf1_AccurData);

  ASSERT_NEAR(sensor_chassis_info.VSI_PrimVehSpdGroupSafeNom, 15.0F, 0.01F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimVehSpdGroupSafeNomQf ==
              Cx3_GenQf1_AccurData);

  ASSERT_NEAR(sensor_chassis_info.VSI_PrimALgtDataRawSafeNom, 1.0F, 0.001F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimALgtDataRawSafeNomQf ==
              Cx3_GenQf1_AccurData);

  ASSERT_NEAR(sensor_chassis_info.VSI_PrimALatDataRawSafeNom, 0.8F, 0.001F);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrimALatDataRawSafeNomQf ==
              Cx3_GenQf1_AccurData);

  ASSERT_NEAR(sensor_chassis_info.VSI_YawRate1, 0.1F, 0.000244140625F);
  ASSERT_TRUE(sensor_chassis_info.VSI_YawRate1Qf1 == Cx3_GenQf1_AccurData);

  ASSERT_NEAR(sensor_chassis_info.VSI_SteerWhlAgSafe, 1.0F, 0.0009765625F);
  ASSERT_NEAR(sensor_chassis_info.VSI_SteerWhlAgSpdSafe, 1.2F, 0.0078125F);
  ASSERT_TRUE(sensor_chassis_info.VSI_SteerWhlSnsrQf == Cx3_GenQf1_AccurData);

  ASSERT_TRUE(sensor_chassis_info.VSI_WhlLockStsLockSts ==
              Cx1_WhlLockSts1_AllWhlsReld);
  ASSERT_TRUE(sensor_chassis_info.VSI_PrpsnTqDirAct == Cx1_PrpsnDirAct1_Fwd);
}
