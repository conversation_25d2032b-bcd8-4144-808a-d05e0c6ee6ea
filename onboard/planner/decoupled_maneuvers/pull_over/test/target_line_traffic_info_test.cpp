#include "planner/decoupled_maneuvers/pull_over/target_line_traffic_info.h"
#include <glog/logging.h>
#include <gtest/gtest.h>

#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_helper.h"
#include "planner/world_model/speed_world_model.h"
#include "planner/world_model/test/world_model_test_utility.h"
#include "pnc_map_service/pnc_map_service.h"
#include "pnc_map_service/test/map_test_util.h"

namespace planner {

namespace pull_over {

namespace {

constexpr double kCurrentX = -4498.062135;
constexpr double kCurrentY = -2587.717336;
constexpr double kTargetLineTotalArcLengthInMeter = 30.0;
constexpr double kJumpInStartArcLengthInMeter = 10.0;
constexpr double kJumpInEndArcLengthInMeter = 20.0;

// Initializes and returns a DestinationInitialDecision based on nominal path
// geometry.
DestinationInitialDecision InitializeDestinationInitialDecision(
    const math::geometry::PolylineCurve2d& nominal_path) {
  DestinationInitialDecision::JumpInGeometry jump_in_geom;
  jump_in_geom.target_line =
      math::geometry::PolylineCurve2d(nominal_path.GetSkeletonInRange(
          nominal_path.GetTotalArcLength() - kTargetLineTotalArcLengthInMeter,
          nominal_path.GetTotalArcLength()));
  jump_in_geom.jump_in_range =
      math::Range1d(kJumpInStartArcLengthInMeter, kJumpInEndArcLengthInMeter);
  jump_in_geom.jump_in_reference_seg = math::geometry::SegmentWithCache2d(
      jump_in_geom.target_line.GetInterp(kJumpInStartArcLengthInMeter),
      jump_in_geom.target_line.GetInterp(
          0.5 * (kJumpInStartArcLengthInMeter + kJumpInEndArcLengthInMeter)));

  DestinationInitialDecision dest_initial_decision;
  dest_initial_decision.reference_dest = nominal_path.GetEndPoint();
  dest_initial_decision.jump_in_geom =
      std::make_optional(std::move(jump_in_geom));
  return dest_initial_decision;
}

// Returns a PlannerObject centered at the specified pull over target line arc
// length, with input length and width.
PlannerObject GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
    const math::geometry::PolylineCurve2d& target_line,
    double obj_center_arc_length, double obj_length, double obj_width,
    ObjectId id) {
  prediction::pb::Agent agent_proto;
  auto& obj_proto = *agent_proto.mutable_tracked_object();

  obj_proto.set_id(id);
  obj_proto.set_object_type(voy::perception::ObjectType::VEHICLE);
  obj_proto.set_width(obj_width);
  obj_proto.set_length(obj_length);

  const math::geometry::Point2d obj_center =
      target_line.GetInterp(obj_center_arc_length);
  obj_proto.set_center_x(obj_center.x());
  obj_proto.set_center_y(obj_center.y());
  obj_proto.set_heading(target_line.GetInterpTheta(obj_center_arc_length));

  TrafficParticipantPose traffic_pose(/*timestamp=*/0, obj_proto);
  PlannerObject planner_obj(agent_proto, /*object_timestamp=*/0, traffic_pose);
  planner_obj.set_is_stationary(true);
  return planner_obj;
}

}  // namespace

class TagetLineTrafficInfoTest : public ::testing::Test {
 public:
  virtual void SetUp() {
    pose_.set_x(kCurrentX);
    pose_.set_y(kCurrentY);
    pose_.set_yaw(2.0);
    map_test_util_.InitPncMapService(kCurrentX, kCurrentY, /*z=*/0,
                                     /*route_name=*/"fremont_route_1",
                                     hdmap::test_util::kFremontData);
  }

 protected:
  voy::Pose pose_;
  pnc_map::MapTestUtil map_test_util_;
};

TEST_F(TagetLineTrafficInfoTest, EmptyTargetLineSnapshots) {
  WorldModel dummy_world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult({16913, 17049, 16905}, {}, {},
                                                16913, map_test_util_);
  const lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *dummy_world_model.GetLatestJointPncMapService(),
          lane_sequence_result.lane_sequence);
  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;

  const DestinationInitialDecision dest_initial_decision =
      InitializeDestinationInitialDecision(nominal_path);
  TargetLineTrafficInfo target_line_traffic_info(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);

  const std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges.empty());
  EXPECT_EQ(feasible_ranges.size(), 1);
  EXPECT_DOUBLE_EQ(feasible_ranges.front().start_arc_length(), -2.0);
  EXPECT_DOUBLE_EQ(feasible_ranges.front().end_arc_length(), 42.0);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
}

TEST_F(TagetLineTrafficInfoTest, SingleSnapshot) {
  WorldModel dummy_world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult({16913, 17049, 16905}, {}, {},
                                                16913, map_test_util_);
  const lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *dummy_world_model.GetLatestJointPncMapService(),
          lane_sequence_result.lane_sequence);
  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;

  const DestinationInitialDecision dest_initial_decision =
      InitializeDestinationInitialDecision(nominal_path);

  // Case 1: Append a single stationary planner object overlapping with the
  // starting edge of the traffic info longitudinal ROI.
  // -------- TargetLineSnapshotInfo
  // |  *----|------------------------------------*  Target line
  // --------
  TargetLineTrafficInfo target_line_traffic_info_1(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);
  const PlannerObject planner_obj_1 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info_1.ref_target_line(),
          /*center_arc_length=*/-4.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/1);
  target_line_traffic_info_1.AppendSnapshotInfo(planner_obj_1,
                                                /*is_in_target_line_roi=*/true);

  const std::vector<TargetLineFeasibleRange> feasible_ranges_1 =
      target_line_traffic_info_1.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges_1.empty());
  EXPECT_EQ(feasible_ranges_1.size(), 1);
  EXPECT_NEAR(feasible_ranges_1.front().start_arc_length(), 1.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges_1.front().end_arc_length(), 42.0,
              math::constants::kEpsilon);
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_1.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_1.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  const std::vector<const TargetLineSnapshotInfo*>& trailing_snapshots_ptrs =
      *feasible_ranges_1.front().GetSnapshotsByCategory(
          TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary);
  EXPECT_EQ(trailing_snapshots_ptrs.size(), 1);
  EXPECT_EQ(DCHECK_NOTNULL(trailing_snapshots_ptrs.front())->typed_obj_id().id,
            1);

  // Case 2: Append a single stationary planner object overlapping with the
  // ending edge of the traffic info longitudinal ROI.
  //                    TargetLineSnapshotInfo --------
  //    *-------------------------------------|---*    |  Target line
  //                                           --------
  TargetLineTrafficInfo target_line_traffic_info_2(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);
  const PlannerObject planner_obj_2 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info_2.ref_target_line(),
          /*center_arc_length=*/44.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/2);
  target_line_traffic_info_2.AppendSnapshotInfo(planner_obj_2,
                                                /*is_in_target_line_roi=*/true);

  const std::vector<TargetLineFeasibleRange> feasible_ranges_2 =
      target_line_traffic_info_2.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges_2.empty());
  EXPECT_EQ(feasible_ranges_2.size(), 1);
  EXPECT_NEAR(feasible_ranges_2.front().start_arc_length(), -2.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges_2.front().end_arc_length(), 39.0,
              math::constants::kEpsilon);

  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_2.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_2.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  const std::vector<const TargetLineSnapshotInfo*>& leading_snapshots_ptrs =
      *feasible_ranges_2.front().GetSnapshotsByCategory(
          TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary);
  EXPECT_EQ(leading_snapshots_ptrs.size(), 1);
  EXPECT_EQ(DCHECK_NOTNULL(leading_snapshots_ptrs.front())->typed_obj_id().id,
            2);

  // Case 3: Append a single stationary planner object in the middle of the
  // traffic info longitudinal ROI.
  // TargetLineSnapshotInfo --------
  //    *------------------|--------|-----------*  Target line
  //                        --------
  TargetLineTrafficInfo target_line_traffic_info_3(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);
  const PlannerObject planner_obj_3 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info_3.ref_target_line(),
          /*center_arc_length=*/15.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/3);
  target_line_traffic_info_3.AppendSnapshotInfo(planner_obj_3,
                                                /*is_in_target_line_roi=*/true);

  std::vector<TargetLineFeasibleRange> feasible_ranges_3 =
      target_line_traffic_info_3.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges_3.empty());
  EXPECT_EQ(feasible_ranges_3.size(), 2);
  EXPECT_NEAR(feasible_ranges_3.front().start_arc_length(), -2.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges_3.front().end_arc_length(), 10.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges_3.back().start_arc_length(), 20.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges_3.back().end_arc_length(), 42.0,
              math::constants::kEpsilon);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_3.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_3.back().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.back().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_EQ(
      feasible_ranges_3.front()
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
          ->size(),
      1);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges_3.front()
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
              ->front())
          ->typed_obj_id()
          .id,
      3);
  EXPECT_EQ(
      feasible_ranges_3.back()
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary)
          ->size(),
      1);
  EXPECT_EQ(DCHECK_NOTNULL(feasible_ranges_3.back()
                               .GetSnapshotsByCategory(
                                   TargetLineFeasibleRange::SnapshotCategory::
                                       kTrailingStationary)
                               ->front())
                ->typed_obj_id()
                .id,
            3);

  //   *--------------------------------------------------*
  // TargetLineSnapshotInfo --------
  //                       |        |
  //                        --------
  feasible_ranges_3 =
      target_line_traffic_info_3.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/4.0);
  ASSERT_TRUE(!feasible_ranges_3.empty());
  EXPECT_EQ(feasible_ranges_3.size(), 1);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().start_arc_length(), -2.0);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().end_arc_length(), 42.0);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeftStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kRightStationary))
          ->empty());

  feasible_ranges_3 =
      target_line_traffic_info_3.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/2.3);
  ASSERT_TRUE(!feasible_ranges_3.empty());
  EXPECT_EQ(feasible_ranges_3.size(), 1);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().start_arc_length(), -2.0);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().end_arc_length(), 42.0);
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_3.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  EXPECT_EQ(
      feasible_ranges_3.front()
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary)
          ->size(),
      1);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeftStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kRightStationary))
          ->empty());
  EXPECT_EQ(DCHECK_NOTNULL(feasible_ranges_3.front()
                               .GetSnapshotsByCategory(
                                   TargetLineFeasibleRange::SnapshotCategory::
                                       kTrailingStationary)
                               ->front())
                ->typed_obj_id()
                .id,
            3);

  feasible_ranges_3 =
      target_line_traffic_info_3.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/-3.0);
  ASSERT_TRUE(!feasible_ranges_3.empty());
  EXPECT_EQ(feasible_ranges_3.size(), 1);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().start_arc_length(), -2.0);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().end_arc_length(), 42.0);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeftStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kRightStationary))
          ->empty());

  feasible_ranges_3 =
      target_line_traffic_info_3.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/-2.3);
  ASSERT_TRUE(!feasible_ranges_3.empty());
  EXPECT_EQ(feasible_ranges_3.size(), 1);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().start_arc_length(), -2.0);
  EXPECT_DOUBLE_EQ(feasible_ranges_3.front().end_arc_length(), 42.0);
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges_3.front().GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kRightStationary))
          ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges_3.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeftStationary))
           ->empty());
  EXPECT_EQ(feasible_ranges_3.front()
                .GetSnapshotsByCategory(
                    TargetLineFeasibleRange::SnapshotCategory::kLeftStationary)
                ->size(),
            1);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges_3.front()
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeftStationary)
              ->front())
          ->typed_obj_id()
          .id,
      3);
}

TEST_F(TagetLineTrafficInfoTest, LeadingAndTrailing) {
  WorldModel dummy_world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult({16913, 17049, 16905}, {}, {},
                                                16913, map_test_util_);
  const lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *dummy_world_model.GetLatestJointPncMapService(),
          lane_sequence_result.lane_sequence);
  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;

  const DestinationInitialDecision dest_initial_decision =
      InitializeDestinationInitialDecision(nominal_path);

  //             TargetLineSnapshotInfos
  //         -------                --------
  //    *---|-------|--------------|--------|----*  Target line
  //         -------                --------
  TargetLineTrafficInfo target_line_traffic_info(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);

  const PlannerObject planner_obj_1 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/10.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/1);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_1,
                                              /*is_in_target_line_roi=*/true);
  const PlannerObject planner_obj_2 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/26.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/2);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_2,
                                              /*is_in_target_line_roi=*/true);

  const std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges.empty());
  ASSERT_EQ(feasible_ranges.size(), 3);
  EXPECT_NEAR(feasible_ranges[0].start_arc_length(), -2.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[0].end_arc_length(), 5.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[1].start_arc_length(), 15.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[1].end_arc_length(), 21.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[2].start_arc_length(), 31.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[2].end_arc_length(), 42.0,
              math::constants::kEpsilon);

  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges[0].GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[0].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  EXPECT_EQ(
      feasible_ranges[0]
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
          ->size(),
      2);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges[0]
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
              ->front())
          ->typed_obj_id()
          .id,
      1);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges[0]
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
              ->back())
          ->typed_obj_id()
          .id,
      2);

  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[1].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[1].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  const std::vector<const TargetLineSnapshotInfo*>& trailing_snapshots_ptrs_1 =
      *feasible_ranges[1].GetSnapshotsByCategory(
          TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary);
  EXPECT_EQ(trailing_snapshots_ptrs_1.size(), 1);
  EXPECT_EQ(
      DCHECK_NOTNULL(trailing_snapshots_ptrs_1.front())->typed_obj_id().id, 1);
  EXPECT_EQ(
      feasible_ranges[1]
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
          ->size(),
      1);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges[1]
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
              ->front())
          ->typed_obj_id()
          .id,
      2);

  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[2].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges[2].GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_EQ(
      feasible_ranges[2]
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary)
          ->size(),
      2);
  EXPECT_EQ(DCHECK_NOTNULL(feasible_ranges[2]
                               .GetSnapshotsByCategory(
                                   TargetLineFeasibleRange::SnapshotCategory::
                                       kTrailingStationary)
                               ->front())
                ->typed_obj_id()
                .id,
            2);
  EXPECT_EQ(DCHECK_NOTNULL(feasible_ranges[2]
                               .GetSnapshotsByCategory(
                                   TargetLineFeasibleRange::SnapshotCategory::
                                       kTrailingStationary)
                               ->back())
                ->typed_obj_id()
                .id,
            1);
}

TEST_F(TagetLineTrafficInfoTest, DoubleLeadingAndTrailing) {
  WorldModel dummy_world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult({16913, 17049, 16905}, {}, {},
                                                16913, map_test_util_);
  const lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *dummy_world_model.GetLatestJointPncMapService(),
          lane_sequence_result.lane_sequence);
  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;

  const DestinationInitialDecision dest_initial_decision =
      InitializeDestinationInitialDecision(nominal_path);

  //               TargetLineSnapshotInfos
  //                  -------  --------
  //    *------------|-------||--------|------------*  Target line
  //                  -------  --------
  TargetLineTrafficInfo target_line_traffic_info(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);

  const PlannerObject planner_obj_1 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/16.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/1);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_1,
                                              /*is_in_target_line_roi=*/true);
  const PlannerObject planner_obj_2 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/20.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/2);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_2,
                                              /*is_in_target_line_roi=*/true);

  const std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges.empty());
  ASSERT_EQ(feasible_ranges.size(), 2);
  EXPECT_NEAR(feasible_ranges[0].start_arc_length(), -2.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[0].end_arc_length(), 11.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[1].start_arc_length(), 25.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges[1].end_arc_length(), 42.0,
              math::constants::kEpsilon);

  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges[0].GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
          ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[0].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  EXPECT_EQ(
      feasible_ranges[0]
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
          ->size(),
      2);

  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges[1].GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  EXPECT_TRUE(
      DCHECK_NOTNULL(
          feasible_ranges[1].GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
          ->empty());
  EXPECT_EQ(
      feasible_ranges[1]
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary)
          ->size(),
      2);
}

TEST_F(TagetLineTrafficInfoTest, SnapshotsAtTwoEnds) {
  WorldModel dummy_world_model = CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      pose_, voy::Canbus(), std::vector<prediction::pb::Agent>(),
      map_test_util_.pnc_map_service(), /*traffic_lights=*/nullptr,
      map_test_util_.route_solution(), /*trajectory_guider_output=*/nullptr);
  const LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult({16913, 17049, 16905}, {}, {},
                                                16913, map_test_util_);
  const lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      lane_selection::GetLaneSequenceGeometryForLaneKeep(
          *dummy_world_model.GetLatestJointPncMapService(),
          lane_sequence_result.lane_sequence);
  const math::geometry::PolylineCurve2d& nominal_path =
      lane_sequence_geometry.nominal_path;

  const DestinationInitialDecision dest_initial_decision =
      InitializeDestinationInitialDecision(nominal_path);

  //             TargetLineSnapshotInfos
  //    -------                      --------
  //   |   *---|--------------------|----*   |  Target line
  //    -------                      --------
  TargetLineTrafficInfo target_line_traffic_info(
      dummy_world_model.robot_state().plan_init_state_snapshot(),
      dest_initial_decision);

  const PlannerObject planner_obj_1 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/7.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/1);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_1,
                                              /*is_in_target_line_roi=*/true);
  const PlannerObject planner_obj_2 =
      GenerateTestPlannerObjCenteredOnLaneSeqNominalPath(
          target_line_traffic_info.ref_target_line(),
          /*center_arc_length=*/36.0, /*obj_length=*/4.0, /*obj_width=*/2.0,
          /*id=*/2);
  target_line_traffic_info.AppendSnapshotInfo(planner_obj_2,
                                              /*is_in_target_line_roi=*/true);

  const std::vector<TargetLineFeasibleRange> feasible_ranges =
      target_line_traffic_info.GetFeasiblelRangesFromTargetLineLateralOffset(
          /*lateral_offset=*/0.0);
  ASSERT_TRUE(!feasible_ranges.empty());
  EXPECT_EQ(feasible_ranges.size(), 1);
  EXPECT_NEAR(feasible_ranges.front().start_arc_length(), 12.0,
              math::constants::kEpsilon);
  EXPECT_NEAR(feasible_ranges.front().end_arc_length(), 31.0,
              math::constants::kEpsilon);

  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary))
           ->empty());
  ASSERT_TRUE(
      !DCHECK_NOTNULL(
           feasible_ranges.front().GetSnapshotsByCategory(
               TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary))
           ->empty());
  EXPECT_EQ(
      feasible_ranges.front()
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kTrailingStationary)
          ->size(),
      1);
  EXPECT_EQ(
      feasible_ranges.front()
          .GetSnapshotsByCategory(
              TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
          ->size(),
      1);
  EXPECT_EQ(DCHECK_NOTNULL(feasible_ranges.front()
                               .GetSnapshotsByCategory(
                                   TargetLineFeasibleRange::SnapshotCategory::
                                       kTrailingStationary)
                               ->front())
                ->typed_obj_id()
                .id,
            1);
  EXPECT_EQ(
      DCHECK_NOTNULL(
          feasible_ranges.front()
              .GetSnapshotsByCategory(
                  TargetLineFeasibleRange::SnapshotCategory::kLeadingStationary)
              ->front())
          ->typed_obj_id()
          .id,
      2);
}

}  // namespace pull_over

}  // namespace planner
