syntax = "proto3";

package prediction.pb;

// The tracking object attribute type.
// Next id: 22
enum ObjectAttributeType {
  PARKED_CAR = 0;
  IS_STATIONARY = 1;
  IS_OCCLUDED = 2;
  SUSTAINED = 3;
  EARLY_PUBLISHED = 4;
  CYC_WITHOUT_PERSON_ATTR = 5;
  // Camera detection related attributes.
  SPECIAL_PERSON_NORMAL = 6;
  SPECIAL_PERSON_CHILDREN = 7;
  SPECIAL_PERSON_POLICEMAN = 8;
  SPECIAL_PERSON_CONSTRUCTION_WORKER = 9;
  SPECIAL_PERSON_SANITATION_WORKER = 10;
  // Tractor and trailer.
  TRACTOR = 11;
  TRAILER = 12;
  IS_DRIVABLE = 13;
  VEHICLE_AUTONOMOUS_DELIVERY_MINICAR = 14;
  // Special vehicle.
  VEHICLE_CONSTRUCTION = 15;
  HAS_EXACT_SHAPE = 16;
  VEHICLE_AMBULANCE = 17;
  VEHICLE_BUS = 21;
  // EGO related attributes.
  IS_EGO = 18;
  // Fallen status for cyclist and pedestrian.
  IS_FALLEN = 19;
  BETTER_NOT_DRIVE = 20;
}
