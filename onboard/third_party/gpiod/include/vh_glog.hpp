/**
 * @file vh_glog.hpp
 * <AUTHOR>
 * @brief 
 * @version 0.1
 * @date 2024-06-24
 * 
 * @copyright Copyright (c) 2024
 * 
 */

#ifndef __VH_GLOG_HPP__
#define __VH_GLOG_HPP__

#include <atomic>
#include <sys/stat.h>

#include "gc_callback.hpp"
#include "glog/logging.h"
namespace voyager {
namespace hardware {

inline void coredump_opt(const char *data, int size) {
  static std::atomic_bool flag(true);

  bool expect = true, desire = false;
  if (flag.compare_exchange_strong(expect, desire) == true) {
    GcCbClass::get_ins().cb_run();
    LOG(ERROR) << "---------------coredump---------------" << std::endl;
  }
  LOG(ERROR) << std::string(data, data + size);
}

class VhGlog {
 public:
  static VhGlog& get_ins() {
    static VhGlog ins;
    return ins;
  }
  static inline int create_dir(const std::string &path) {
  struct stat info;
  if (stat(path.c_str(), &info) < 0) {
    if (mkdir(path.c_str(), 0755) < 0) {
      return -1;
    }
  }
  return 0;
}
  bool init(char *argv, std::string level = "INFO",
            std::string dir = "./vh_log") {
    static std::atomic<bool> flag(true);
    bool expect = true;
    if(flag.compare_exchange_strong(expect, false) == false)
      return false;
    
    create_dir(dir.c_str());
    google::InitGoogleLogging(argv);
    if (level == "INFO" || level == "info")
      FLAGS_minloglevel = google::INFO;  // 日志级别
    else if (level == "WARNING" || level == "warning")
      FLAGS_minloglevel = google::WARNING;  // 日志级别
    else if (level == "ERROR" || level == "error")
      FLAGS_minloglevel = google::ERROR;  // 日志级别
    else
      FLAGS_minloglevel = google::FATAL;  // 日志级别

    FLAGS_log_dir = dir;                     // 日志目录
    FLAGS_max_log_size = 100;                // 最大日志大小
    FLAGS_stop_logging_if_full_disk = true;  // 磁盘满时停止输出
    FLAGS_stderrthreshold = google::FATAL;  // 此级别以上的日志输出到终端
    // FLAGS_logtostderr = true; //日志只输出到stderr
    // FLAGS_alsologtostderr = true; //同时输出到stderr
    // FLAGS_colorlogtostderr = true; //stderr显示响应颜色
    FLAGS_logbufsecs = 0;  // 缓冲日志输出，默认30秒，改为立即输出
    google::EnableLogCleaner(2);  // 保持n天日志

    google::InstallFailureSignalHandler();  // 捕捉错误信号
    google::InstallFailureWriter(coredump_opt);

    return true;
  }
  void deinit() { google::ShutdownGoogleLogging(); }
 private:
  VhGlog() = default;
  VhGlog(const VhGlog&) = delete;
  VhGlog& operator=(const VhGlog&) = delete;
  virtual ~VhGlog() = default;
};
}  // namespace hardware
}  // namespace voyager

#endif
