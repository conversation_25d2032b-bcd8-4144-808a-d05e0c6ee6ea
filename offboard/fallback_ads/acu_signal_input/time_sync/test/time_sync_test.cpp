#include "acu_signal_input/time_sync/time_sync.h"

#include <gtest/gtest.h>

#include <cstdint>
#include <cstring>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "offboard/fallback_ads/fallback_simulation.h"

namespace fallback_adas {
namespace acu_signal_input {

TEST(GetCurrentTimestamp, GetCurrentTimestampTest) {
  auto& raw_pose_input =
      Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms;

  // ref_timestamp_ms = 0x19618d955d8.
  uint64_t ref_timestamp_ms = 1744173618648 % 1000000000L;
  raw_pose_input.Rte_AcuPose_GnssPoseReserve1.value =
      ref_timestamp_ms % 1000000;

  for (int i = 0; i < 12; ++i) {
    // Set the counter value
    uint32_t timestamp = GetCurrentTimestamp(
        i, ref_timestamp_ms, raw_pose_input.Rte_AcuPose_GnssPoseReserve1.value);

    // Verify the timestamp
    if (i < 11) {
      EXPECT_EQ((i + 1) * 10, timestamp);
    } else {
      EXPECT_EQ(static_cast<uint32_t>(ref_timestamp_ms) + 10, timestamp);
    }
  }
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
