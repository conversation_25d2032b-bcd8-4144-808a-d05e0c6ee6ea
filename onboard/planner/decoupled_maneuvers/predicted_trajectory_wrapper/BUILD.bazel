package(default_visibility = ["//visibility:public"])

cc_library(
    name = "predicted_trajectory_wrapper",
    srcs = [
        "predicted_trajectory_wrapper.cpp",
    ],
    hdrs = [
        "conditional_predicted_trajectory_wrapper.h",
        "predicted_trajectory_route_association_common.h",
        "predicted_trajectory_wrapper.h",
    ],
    include_prefix = "planner/decoupled_maneuvers/predicted_trajectory_wrapper/",
    deps = [
        "//onboard/common/math",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/world_model/traffic_participant",
        "//onboard/pnc_map_service:map_elements",
        "//onboard/prediction:voy_prediction_common",
        "//onboard/third_party/strings",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:glog",
    ],
)
