package(default_visibility = ["//visibility:public"])

cc_library(
    name = "trajectory_decode",
    srcs = [
        "trajectory_decode.cpp",
    ],
    hdrs = [
        "trajectory_decode.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-unused-parameter",
    ],
    include_prefix = "acu_signal_input/trajectory_decode/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_acu_sig_input",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_acu_sig_input_type",
        "//offboard/fallback_ads/common_math_library:assert_cfg",
        "//offboard/fallback_ads/common_math_library:can_util",
        "//offboard/fallback_ads/common_math_library:common_method",
    ],
)
