#include "node.h"
#include <boost/asio/spawn.hpp>
#include <boost/filesystem.hpp>
#include <boost/filesystem/operations.hpp>
#include <boost/filesystem/path.hpp>
#include <boost/system/detail/errc.hpp>
#include <boost/system/detail/error_code.hpp>
#include <boost/system/errc.hpp>
#include <hdmap_protos/hdmap_config.pb.h>
#include <iostream>
#include <memory>
#include <ros/time.h>
#include <string>
#include <utility>
#include <vector>

#include <boost/system/error_code.hpp>
#include <glog/logging.h>
#include "av_comm/car_id.h"
#include "av_comm/onboard_config.h"
#include "bag_compressor.h"
#include "bag_uploader.h"
#include "base/base_dir.h"
#include "base/now.h"
#include "file_syncer.h"
#include "gift_util.h"
#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/util.h"
#include "node/timer.h"
#include "realtime_uploader/common/types.hpp"
#include "trigger_handler.h"

namespace realtime_uploader {

boost::system::error_code RealtimeUploaderNode::Go(
    boost::asio::io_service* io_service) {
  trigger_handler_ = std::make_unique<TriggerHandler>(
      /*compressing_path=*/kCompressingPath,
      /*uploading_path=*/kUploadingPath,
      /*trace_trigger_dir_path=*/kTraceTriggerDirPath,
      /*version_type=*/version_type_,
      /*version=*/curr_version_,
      /*car_id=*/car_id_,
      /*region=*/curr_region_,
      /*trigger_skip_type=*/bag_realtime_upload_control_,
      /*acu_type=*/acu_type_);

  bag_compressor_ = std::make_unique<BagCompressor>(
      /*handle_path=*/kCompressingPath, /*uploading_path=*/kUploadingPath);

  bag_uploader_ = std::make_unique<BagUploader>(
      /*handled_path=*/kUploadingPath,
      /*map_version=*/curr_map_version_, /*region=*/curr_region_,
      /*runtime_env*/ runtime_env_);

  file_syncer_ = std::make_unique<FileSyncer>(
      /*handled_path=*/kUploadingPath, /*target_host_name_=*/kCPUBOX1IP,
      /*target_path=*/kUploadingPath,
      /*limitedBandwidth=*/kScpLimitedBandwidthBps);

  auto error_code = trigger_handler_->Init();
  if (error_code) {
    LOG(ERROR) << "Init bag_handler error " << error_code.message();
    return error_code;
  }
  // We must start trigger_handler.
  boost::asio::spawn(
      *io_service, [io_service, this](boost::asio::yield_context yield) {
        auto timer = std::make_unique<node::Timer>(*io_service);
        boost::system::error_code error_code;
        while (!error_code) {
          timer->expires_from_now(std::chrono::milliseconds(100), error_code);
          trigger_handler_->UpdateTriggers();
          timer->async_wait(yield[error_code]);
        }
        io_service->stop();
      });
  boost::asio::spawn(
      *io_service, [io_service, this](boost::asio::yield_context yield) {
        auto timer = std::make_unique<node::Timer>(*io_service);
        boost::system::error_code error_code;
        while (!error_code) {
          timer->expires_from_now(std::chrono::milliseconds(5000), error_code);
          trigger_handler_->DoFileOperations();
          timer->async_wait(yield[error_code]);
        }
        io_service->stop();
      });
  if (av_comm::IsGen3Cpubox1()) {
    // We must HandleExternalDiskFiles at cpubox1.
    boost::asio::spawn(*io_service, [io_service,
                                     this](boost::asio::yield_context yield) {
      auto timer = std::make_unique<node::Timer>(*io_service);
      boost::system::error_code error_code;
      while (!error_code) {
        timer->expires_from_now(std::chrono::milliseconds(1000), error_code);
        trigger_handler_->HandleExternalDiskFiles();
        timer->async_wait(yield[error_code]);
      }
      io_service->stop();
    });
    // For cpubox1 and cpubox2, we need to do sync tasks with cloud.
    boost::asio::spawn(*io_service, [io_service,
                                     this](boost::asio::yield_context yield) {
      auto timer = std::make_unique<node::Timer>(*io_service);
      boost::system::error_code error_code;
      while (!error_code) {
        // 30s sync once
        timer->expires_from_now(std::chrono::milliseconds(30000), error_code);
        trigger_handler_->SyncTriggersWithCloud();
        timer->async_wait(yield[error_code]);
      }
      io_service->stop();
    });
    // For only cpubox1, we do file upload.
    boost::asio::spawn(*io_service, [io_service,
                                     this](boost::asio::yield_context yield) {
      auto timer = std::make_unique<node::Timer>(*io_service);
      boost::system::error_code error_code;
      while (!error_code) {
        timer->expires_from_now(std::chrono::milliseconds(5000), error_code);
        bag_uploader_->DoUpload();
        timer->async_wait(yield[error_code]);
      }
      io_service->stop();
    });

    // For cpubox2 camera moved to cpubox1 feature, we need to do compress
    // camera bags.
    boost::asio::spawn(
        *io_service, [io_service, this](boost::asio::yield_context yield) {
          auto timer = std::make_unique<node::Timer>(*io_service);
          boost::system::error_code error_code;
          while (!error_code) {
            timer->expires_from_now(std::chrono::milliseconds(700), error_code);
            bag_compressor_->ProcessCameraBag();
            timer->async_wait(yield[error_code]);
          }
          io_service->stop();
        });
  }
  if (av_comm::IsGen3Cpubox2()) {
    // For cpubox1 and cpubox2, we need to do sync tasks with cloud.
    boost::asio::spawn(*io_service, [io_service,
                                     this](boost::asio::yield_context yield) {
      auto timer = std::make_unique<node::Timer>(*io_service);
      boost::system::error_code error_code;
      while (!error_code) {
        // 31s sync once
        timer->expires_from_now(std::chrono::milliseconds(31000), error_code);
        trigger_handler_->SyncTriggersWithCloud();
        timer->async_wait(yield[error_code]);
      }
      io_service->stop();
    });
    // For only cpubox2, we need to do scp to cpubox1.
    boost::asio::spawn(*io_service, [io_service,
                                     this](boost::asio::yield_context yield) {
      auto timer = std::make_unique<node::Timer>(*io_service);
      boost::system::error_code error_code;
      while (!error_code) {
        timer->expires_from_now(std::chrono::milliseconds(5000), error_code);
        file_syncer_->DoSync();
        timer->async_wait(yield[error_code]);
      }
      io_service->stop();
    });
  }

  return boost::system::error_code();
}

}  // namespace realtime_uploader
