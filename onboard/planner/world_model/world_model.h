#ifndef ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_H_
#define ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_H_

#include <map>
#include <memory>
#include <optional>
#include <string>
#include <unordered_map>
#include <vector>

#include <tbb/concurrent_unordered_map.h>

#include "base/optional_value_accessor.h"
#include "exception_handler/utility/seed/exception_handler_seed.h"
#include "localization_protos/localization.pb.h"
#include "mrc_protos/mrc_request.pb.h"
#include "perception/sensing/lidar/occlusion_map/occlusion_map.h"
#include "planner/assist/assist_blockage_analyzer.h"
#include "planner/assist/assist_directive_generator.h"
#include "planner/assist/assist_instruction.h"
#include "planner/assist/assist_task_definition/stuck_scene_request_task.h"
#include "planner/assist/assist_task_scheduler.h"
#include "planner/assist/assist_util.h"
#include "planner/behavior/types/behavior_decision.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/conditional_predicted_trajectory_wrapper.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/global_object_manager/global_object_manager.h"
#include "planner/utility/common/config_io.h"
#include "planner/utility/lattice_planner/cubic_polynomial_spiral.h"
#include "planner/utility/occlusion/occlusion_checker.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/utility/state/planner_internal_state_interface.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner_utility.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/empirical_info/empirical_info_processor.h"
#include "planner/world_model/immediate_pull_over/immediate_pull_over_state_manager.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/lane_congestion/lane_congestion_detector.h"
#include "planner/world_model/lane_congestion/lane_congestion_info.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/pickup_dropoff_zone/virtual_pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/previous_action_recorder/previous_action_recorder.h"
#include "planner/world_model/pull_over/pull_over_request_info.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner/world_model/regional_map/regional_map_generator.h"
#include "planner/world_model/regional_map/stuck_avoidance/changed_lane_structure_reasoner.h"
#include "planner/world_model/regional_map/stuck_avoidance/map_change_area_processor.h"
#include "planner/world_model/regional_map/stuck_avoidance/map_change_area_processor_param.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_reasoner.h"
#include "planner/world_model/regional_sections_locator/regional_sections_locator.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/route_model/route_model.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/snapshot/snapshot.h"
#include "planner/world_model/speed_world_model.h"
#include "planner/world_model/traffic_participant/tracked_object.h"
#include "planner/world_model/traffic_signal_reasoner/traffic_signal_reasoner.h"
#include "planner/world_model/waypoint_assist/waypoint_assist_empirical_info_processor.h"
#include "planner/world_model/waypoint_assist/waypoint_assist_tracker.h"
#include "planner/world_model/waypoint_search/waypoint_lanes_cache.h"
#include "planner/world_model/world_model_utility.h"
#include "planner_protos/nominal_path_planner_config.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/state/world_model.pb.h"
#include "planner_protos/world_model_config.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/pnc_map_history_buffer.h"
#include "pnc_map_service/pnc_map_service.h"
#include "routing/engine/edge_filter_manager.h"
#include "routing_protos/route_query.pb.h"

namespace planner {

namespace speed {
class ReasoningTestFixture;
}  // namespace speed
class LaneChangeTestFixture;

// Class WorldModel incorporates all the surrounding information
// around the robot: for example, mapping and perception information.
//
// TODO(Guojing Hu): deprecate this class and rewrite world model with proper
// test coverage.
// This class also manages the seeding data for world model.
class WorldModel : public PlanningSeedAccess,
                   public PlannerInternalStateInterface<pb::WorldModelState>,
                   public ::exception_handler::ExceptionHandlerSeedAccess {
 public:
  WorldModel(
      const pb::WorldModelConfig& config,
      const pnc_map::PncMapService* pnc_map_service,
      const std::shared_ptr<routing::EdgeFilterManager>& edge_filter_manager,
      const std::shared_ptr<GlobalObjectManager>& global_object_manager,
      double robot_max_speed = common::LoadPlannerCarMotionModelParam()
                                   .limit()
                                   .speed_limit()
                                   .max_val(),
      double regional_path_range = constants::kRegionalPathRangeInMeter);

  std::unique_ptr<SpeedWorldModel> ExtractToSpeedWorldModel(
      std::unique_ptr<RouteModelSpeedData>& route_model_speed_data);
  std::unique_ptr<SpeedWorldModel> ExtractToSpeedWorldModel();

  void LoadState(const pb::WorldModelState& from_pb) final;

  void DumpState(pb::WorldModelState& to_pb) const final;

  // Updates world model using latest snapshot.
  bool Update(const pb::WorldModelSeed& previous_world_model_seed,
              std::unique_ptr<const Snapshot> snapshot_ptr,
              std::unique_ptr<std::vector<pb::AssistResponse>>
                  remote_assist_responses_ptr,
              bool has_accepted_new_global_route_lanes,
              bool is_proposed_route_solution_rejected,
              pb::PlanningDebug* planning_debug);

  // Accessors.
  const voy::Pose& pose() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->pose_ptr;
  }

  int64_t snapshot_timestamp() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->pose_timestamp;
  }

  int64_t prediction_timestamp() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    DCHECK(latest_snapshot_ptr_->agent_list_ptr != nullptr);
    return latest_snapshot_ptr_->agent_list_ptr->timestamp();
  }

  // The return value is a tm structure which represents the corresponding
  // time, expressed for the local timezone. It means planner hasn't received
  // an order start time if return value is nullptr. User can fetch interested
  // time info from related field. e.g. The hour info can be fetched from
  // |order_start_time_info->tm_hour|.
  //
  // More details can be found:
  // /usr/include/x86_64-linux-gnu/bits/types/struct_tm.h
  const std::tm* order_start_time_info() const {
    return order_start_time_info_;
  }

  const voy::TrafficLights& traffic_light_detection() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->traffic_light_ptr;
  }

  const RegionalSectionsLocator& regional_sections_locator() const {
    return regional_sections_locator_;
  }

  const RegionalMap& regional_map() const { return regional_map_; }

  const LightRegionalPath& last_selected_regional_path() const {
    return regional_map_generator_.last_selected_regional_path();
  }

  const pb::LaneGraphSearchInfo& lane_graph_search_info() const {
    return lane_graph_search_info_;
  }

  const pb::TrajectoryGuiderOutput* trajectory_guider_output() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->trajectory_guider_output_ptr.get();
  }

  const std::shared_ptr<const ::localization::LocalizationFault>&
  localization_fault() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->localization_fault_ptr;
  }

  const std::shared_ptr<const pb::ReplanRequest>& replan_request() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->replan_request_ptr;
  }

  const GlobalRouteSolution* global_route_solution() const {
    return route_model_.global_route_solution();
  }

  const routing::utility::OrderInfo& order_info() const {
    return route_model_.order_info();
  }

  routing::pb::ImmediatePullOverState immediate_pullover_state() const {
    return immediate_pull_over_state_manager_.immediate_pullover_state();
  }

  routing::pb::SpecialPlannerResponseRequest special_planner_response_request()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    DCHECK(latest_snapshot_ptr_->route_status_ptr != nullptr);
    return latest_snapshot_ptr_->route_status_ptr
        ->special_planner_response_request();
  }

  bool IsImmediatePullOverTriggeredByMRC() const {
    return immediate_pull_over_state_manager_
        .IsImmediatePullOverTriggeredByMRC();
  }

  bool IsImmediatePullOverTriggeredByPlanning() const {
    return immediate_pull_over_state_manager_.IsTriggeredByPlanning();
  }

  bool IsCurrentQueryUsePriorMap() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    DCHECK(latest_snapshot_ptr_->route_status_ptr != nullptr);
    return latest_snapshot_ptr_->route_status_ptr->current_route_query()
        .use_query_timestamp_as_start_time();
  }

  const RouteModel& route_model() const { return route_model_; }
  RouteModel& mutable_route_model() { return route_model_; }

  const routing::pb::RouteQuery& replan_route_query() const {
    return replan_route_query_;
  }

  const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence() const {
    return last_selected_lane_sequence_;
  }

  const pnc_map::Lane* current_lane_on_last_selected_lane_sequence() const {
    return current_lane_on_last_selected_lane_sequence_;
  }

  // DO NOT use this function anymore. Use planner_object_map() and
  // object_prediction_map() instead.
  const std::vector<TrackedObject>& tracked_objects() const {
    return tracked_objects_;
  }

  // Fetch agent list from prediction. Recommend to use this method instead of
  // latest_snapshot_ptr_->agent_list_ptr, because this method with filter
  // objects, like ones confirmed by RA OPS.
  const prediction::pb::AgentList& agent_list() const {
    if (agent_list_.has_value()) {
      return agent_list_.value();
    }
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *(latest_snapshot_ptr_->agent_list_ptr);
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  std::shared_ptr<GlobalObjectManager>& mutable_global_object_manager_ptr() {
    DCHECK(global_object_manager_ != nullptr);
    return global_object_manager_;
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  const std::shared_ptr<GlobalObjectManager>& global_object_manager_ptr()
      const {
    DCHECK(global_object_manager_ != nullptr);
    return global_object_manager_;
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map()
      const {
    return *global_object_manager_->planner_object_map_ptr();
  }

  const PlannerObjectsHistory& planner_objects_history() const {
    return global_object_manager_->planner_objects_history();
  }

  // Use this instead of tracked_objects() in the decoupled arch.
  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
  object_prediction_map() const {
    return global_object_manager_->object_prediction_map();
  }

  const tbb::concurrent_unordered_map<ObjectId,
                                      ConditionalPredictedTrajectoryWrapper>&
  object_conditional_prediction_map() const {
    return global_object_manager_->object_conditional_prediction_map();
  }

  const av_comm::HistoryBuffer<int64_t, math::geometry::Point2d>& ego_history()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->ego_history_ptr;
  }

  const std::map<int64_t,
                 av_comm::HistoryBuffer<int64_t, math::geometry::Point2d>>&
  agent_history() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->agent_history_ptr;
  }

  const av_comm::HistoryBuffer<int64_t, SnapshotData<const ::pb::Fault>>*
  health_faults_history() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->health_faults_ptr;
  }

  const std::shared_ptr<const voy::SemanticMapElements>& semantic_map_elements()
      const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->semantic_map_elements_ptr;
  }

  const perception::OcclusionMap& occlusion_map() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return *latest_snapshot_ptr_->occlusion_map_ptr;
  }

  // NOTE(Tingran): The construction zones that planner use are the ones with
  // human labeling, not the ones generately solely by perception algorithms.
  const std::vector<ConstructionZone>& construction_zones() const {
    return global_object_manager_->construction_zones();
  }

  // Tests if ego should ignored traffic lights about the map change area.
  bool should_ignore_map_change_traffic_lights() const {
    return assist_blockage_analyzer_.traffic_light_map_change_area_tracker()
        .ShouldIgnoreTrafficLight();
  }

  const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
  construction_zone_ptr_map() const {
    return global_object_manager_->construction_zone_map();
  }

  const std::map<int64_t, std::vector<LaneCongestionInfo>>&
  lane_congestion_map() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->lane_congestion_map;
  }

  const RobotState& robot_state() const { return *robot_state_ptr_; }

  const OcclusionChecker& occlusion_checker() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->occlusion_checker;
  }

  const LaneBlockageDetector& lane_blockage_detector() const {
    return lane_blockage_detector_;
  }

  const LaneCongestionDetector& lane_congestion_detector() const {
    return lane_congestion_detector_;
  }

  const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos() const {
    return pickup_dropoff_zone_infos_;
  }

  const std::vector<pb::AssistResponse>& assist_responses() const {
    return assist_directive_generator_.assist_responses();
  }

  // Not owned by this class.
  const pnc_map::PncMapService* pnc_map_service() const {
    return pnc_map_history_buffer_.pnc_map_service();
  }

  // If FLAGS_planning_enable_hdmap_dynamic_data_update is true, returns the
  // latest joint pnc map service. Otherwise returns
  // static_joint_pnc_map_service.
  // TODO(xiaodong) Rename to GetEffectiveJointPncMapService.
  std::shared_ptr<const pnc_map::JointPncMapService>
  GetLatestJointPncMapService() const;

  const TrafficSignalReasoner& traffic_signal_reasoner() const {
    return traffic_signal_reasoner_;
  }

  const CurrentLaneAssociator& current_lane_associator() const {
    return current_lane_associator_;
  }

  const CurrentLaneReasoner& current_lane_reasoner() const {
    return current_lane_reasoner_;
  }

  std::vector<const lane_candidate::NearLaneCandidate*>
  GetDrivablePhysicalNearLaneCandidates() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_.GetPhysicalNearLaneCandidates(
                     /*need_drivable_lane=*/true)
               : current_lane_associator_
                     .GetDrivablePhysicalNearLaneCandidates();
  }

  std::vector<const pnc_map::Lane*> GetDrivablePhysicalLanes() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_.GetPhysicalLanes(
                     /*need_drivable_lane=*/true)
               : current_lane_associator_.GetDrivablePhysicalLanes();
  }

  const std::vector<const pnc_map::Lane*> current_lanes_for_regional_path()
      const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? lane_selection::GetLanesFromCurrentLaneCandidates(
                     current_lane_reasoner_
                         .current_lane_candidates_for_regional_path())
               : current_lane_associator_.current_lanes_for_regional_path();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  current_lane_candidates_for_regional_path() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_
                     .current_lane_candidates_for_regional_path()
               : current_lane_associator_
                     .current_lane_candidates_for_regional_path();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  current_lanes_for_waypoint_graph() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_
                     .current_lane_candidates_for_optimal_lane_sequence()
               : current_lane_associator_.current_lanes_for_waypoint_graph();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  current_lanes_for_pull_out_jump_out() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_
                     .current_lane_candidates_for_pull_out_jump_out()
               : current_lane_associator_.current_lanes_for_pull_out_jump_out();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  current_lanes_for_stuck_avoidance_jump_out() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_
                     .current_lane_candidates_for_stuck_avoidance_jump_out()
               : current_lane_associator_
                     .current_lanes_for_stuck_avoidance_jump_out();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  current_lanes_on_fork() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_.current_lane_candidates_on_fork()
               : current_lane_associator_.current_lanes_on_fork();
  }

  const std::vector<lane_candidate::CurrentLaneCandidate>&
  prefix_associated_current_lanes() const {
    return FLAGS_planning_enable_current_lane_reasoner
               ? current_lane_reasoner_
                     .current_lane_candidates_for_downstream_proposal()
               : current_lane_associator_.prefix_associated_current_lanes();
  }

  const std::shared_ptr<const pb::FaultDetectorResponse>&
  fault_detector_response_ptr() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->fault_detector_response_ptr;
  }

  WaypointLanesCache* mutable_waypoint_lanes_cache_ptr() {
    return &waypoint_lanes_cache_;
  }

  const DrivableLaneReasoner& drivable_lane_reasoner() const {
    return drivable_lane_reasoner_;
  }

  const lane_selection::LaneSequenceProposalInfo& lane_sequence_proposal_info()
      const {
    return lane_sequence_proposal_info_;
  }

  DrivableLaneReasoner* mutable_drivable_lane_reasoner() {
    return &drivable_lane_reasoner_;
  }

  const AssistDirectiveGenerator& assist_directive_generator() const {
    return assist_directive_generator_;
  }

  const AssistTaskScheduler& assist_task_scheduler() const {
    return assist_task_scheduler_;
  }

  const PreviousActionRecorder& previous_action_recorder() const {
    return previous_action_recorder_;
  }

  const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->mrc_request_ptr;
  }

  const AssistInstruction* GetStuckAssistInstruction() const {
    if (!assist_task_scheduler().assist_task_queue().empty() &&
        IsStuckSceneResponseCommand(assist_task_scheduler()
                                        .assist_task_queue()
                                        .front()
                                        ->assist_response())) {
      StuckSceneRequestTask* stuck_task_ptr =
          dynamic_cast<StuckSceneRequestTask*>(
              assist_task_scheduler().assist_task_queue().front().get());
      if (DCHECK_NOTNULL(stuck_task_ptr)->IsStuckSceneRequestConfirmedYes() &&
          assist_directive_generator().unstuck_directives().has_value()) {
        return &base::CheckAndGetValue(
                    assist_directive_generator().unstuck_directives())
                    .assist_instruction;
      }
    }
    return nullptr;
  }

  void UpdateFrameView(const FrameView& new_view) {
    frame_view_ = new_view;
    last_finished_seed_ =
        Seed::Access<token::WorldModelSeed>::GetMsg(PathLastFinishedFrame());
  }

  // Returns const seed.
  const pb::WorldModelSeed& last_seed() const { return *last_finished_seed_; }

  // Returns decoupled_lane_follow_maneuver_seed's latch_current_lane.
  bool latch_current_lane() const {
    return Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
               PathLastFinishedFrame())
        ->latch_current_lane();
  }

  int frame_drop_count() const { return frame_drop_count_; }

  // Returns the reference destination point of the pull over.
  const math::geometry::Point2d& pull_over_reference_point() const {
    return pull_over_reference_point_;
  }

  const lane_selection::MapChangeAreaHandleParameter&
  map_change_area_handle_param() const {
    return map_change_area_processor_.param();
  }

  const lane_selection::EmpiricalInfoProcessor& empirical_info_processor()
      const {
    return empirical_info_processor_;
  }

  std::vector<const pnc_map::Lane*> GetImmutableLaneSequence() const;

  // Sets up empty route (mainly for unit test).
  void SetUpEmptyRoute(const pnc_map::PncMapService& pnc_map_service);

  // Sets associate route for predicted trajectories if they fall into target
  // scopes.
  void SetAssociatedRouteForObjectPredictedTrajectories(
      const pb::DecoupledManeuverSeed& prev_maneuver_seed,
      const std::vector<LaneSequenceResult>& lane_sequence_results,
      pb::PlanningDebug* planning_debug);

  const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes() const {
    return regional_sections_locator_.locator_selected_near_lanes();
  }

  const std::vector<const pnc_map::Section*>& locator_potential_sections()
      const {
    return regional_sections_locator_.potential_sections();
  }

  const std::vector<const pnc_map::Section*>&
  locator_extend_potential_sections() const {
    return regional_sections_locator_.extend_potential_sections();
  }

  const std::vector<pnc_map::SectionVertex>& regional_section_vertices() const {
    return regional_sections_locator_.regional_section_vertices();
  }

  const std::unique_ptr<const Snapshot>& latest_snapshot_ptr() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_;
  }

  const WaypointAssistTracker& waypoint_assist_tracker() const {
    return waypoint_assist_tracker_;
  }

  const assist::AssistBlockageAnalyzer& assist_blockage_analyzer() const {
    return assist_blockage_analyzer_;
  }

  const pull_over::PullOverRequestInfo& pull_over_request_info() const {
    return pull_over_request_info_;
  }

  const ImmediatePullOverStateManager& immediate_pull_over_state_manager()
      const {
    return immediate_pull_over_state_manager_;
  }

  const std::optional<math::geometry::Point2d>& immediate_pull_over_point()
      const {
    return immediate_pull_over_state_manager_.immediate_pull_over_point();
  }

  int64_t immediate_pullover_triggered_timestamp() const {
    return immediate_pull_over_state_manager_
        .immediate_pullover_triggered_timestamp();
  }

  const std::optional<LightRegionalPath>& redirecting_regional_path() const {
    return immediate_pull_over_state_manager_.redirecting_regional_path();
  }

  bool should_planner_respond_mrc() const {
    return should_planner_respond_mrc_;
  }

  bool should_trigger_pull_out_by_mrc() const {
    return should_trigger_pull_out_by_mrc_;
  }

  // Returns true if immediate pullover is in progress.
  bool ShouldResponseToTriggeredImmediatePullOver() const {
    return immediate_pull_over_state_manager_
        .ShouldResponseToTriggeredImmediatePullOver();
  }

  // Returns true if immediate pullover is in progress and should ask LC to
  // perform immediate right lane change.
  bool ShouldTriggerImmediateRightLaneChangeForImmediatePullover() const {
    return immediate_pull_over_state_manager_
        .ShouldTriggerImmediateRightLaneChangeForImmediatePullover();
  }

  const PrecomputedStateLatticeSpiralData& precomputed_spiral_data() const {
    return precomputed_spiral_data_;
  }

  const lane_selection::StuckAvoidanceReasoner& stuck_avoidance_reasoner()
      const {
    return stuck_avoidance_reasoner_;
  }

  bool should_generate_pull_out_jump_out_sequence() const {
    return should_generate_pull_out_jump_out_sequence_;
  }

  const std::vector<const pnc_map::Section*>&
  destination_successor_sections_for_pullover() const {
    return destination_successor_sections_for_pullover_;
  }

  double pullover_final_extend_section_percentage() const {
    return pullover_final_extend_section_percentage_;
  }

  // Returns the match type which indicates the route destination is a pick-up
  // destination or a drop-off destination.
  order::pb::MatchType match_type() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->route_status_ptr == nullptr
               ? order::pb::MatchType::UNKNOWN_MATCH
               : latest_snapshot_ptr_->route_status_ptr->ride_route_query()
                     .match_type();
  }

  // The distance ego travels from start position is less than
  // |kMaxPullOutDistanceFromEgoStartPositionInMeter| after receiving an
  // order, we consider ego is near the order start position. And the start
  // position should be close to a PDZ.
  std::optional<double> OrderStartPositionDistance() const;

  // Returns true if ego's position is ready for pull out.
  bool IsEgoPositionOnRouteReadyForPullOut() const;

  // Accessor for last-cycle pull out reasoner seed.
  const speed::pb::PulloutReasonerSeed& pull_out_reasoner_seed() const;
  // Accessor for last-cycle pull out selection seed.
  const planner::pb::PullOutSelectionSeed& pull_out_selection_seed() const;

  // Checks whether too many agents in current scenario.
  bool IsHighAgentDensityScenario() const {
    return global_object_manager_->is_high_agent_density();
  }

  // Updates the flag should_planner_respond_mrc_.
  void UpdateShouldPlannerRespondMRC();

  // Updates the flag should_trigger_pull_out_by_mrc_ based on the last and the
  // curent cycle states.
  void UpdateShouldTriggerPullOutByMrc(
      const pb::WorldModelSeed& previous_world_model_seed);

  // The pull out is thought finished if the current pull out has been
  // confirmed and executed in lane follow for more than the pull out
  // termination distance threshold.
  bool HasPullOutFinished() const;

  // Updates global route lanes info.
  // Note: This would be called after calculating regional path and lane
  // sequences, so majorly for next cycle's planning.
  void UpdateGlobalRouteLanesInfo(
      const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
          new_route_lanes);

  const std::shared_ptr<const voy::TripComment>& trip_comment() const {
    DCHECK(latest_snapshot_ptr_ != nullptr);
    return latest_snapshot_ptr_->trip_comment_ptr;
  }

  void set_ml_trajectory_init_state_has_diverged_from_planner(
      bool has_diverged) {
    ml_trajectory_init_state_has_diverged_from_planner_ = has_diverged;
  }

  bool ml_trajectory_init_state_has_diverged_from_planner() const {
    return ml_trajectory_init_state_has_diverged_from_planner_;
  }

 private:
  // NOTE: the default constructor is used only for unit testing.
  WorldModel() = delete;

  // Updates traffic signal reasoner.
  void UpdateTrafficSignalReasoner(
      const std::vector<const pnc_map::Lane*>& current_lanes);

  // Updates previous action recorder.
  void UpdatePreviousActionRecorder(
      const RobotState& robot_state,
      const pb::WorldModelSeed& previous_world_model_seed,
      const pb::DecoupledManeuverSeed& prev_maneuver_seed,
      std::vector<const pnc_map::Lane*> current_cycle_current_lanes,
      int64_t snapshot_timestamp);

  // Updates drivable lane reasoner.
  void UpdateDrivableLaneReasoner();

  // Updates robot state.
  void UpdateRobotState(const pb::WorldModelSeed& previous_world_model_seed,
                        pb::ReplanLocatorDebug* replan_locator_debug,
                        pb::RobotStateDebug* robot_state_debug);

  // Updates lane blockage.
  void UpdateLaneBlockage(const pb::DecoupledManeuverSeed&
                              previous_decoupled_lane_follow_maneuver_seed,
                          pb::LaneBlockageDebug* lane_blockage_debug);

  // Updates lane congestion.
  void UpdateLaneCongestion(pb::LaneCongestionDebug* lane_congestion_debug);

  // Updates regional sections locator.
  bool UpdateRegionalSectionsLocator(
      const pb::WorldModelSeed& previous_world_model_seed,
      pb::RegionalSectionsLocatorDebug* regional_sections_locator_debug);

  // Update agent list.
  void UpdateAgentList();

  // Updates tracked objects.
  void UpdateTrackedObjects(
      const std::optional<int64_t>& max_interested_prediction_timestamp);

  // Updates construction zones.
  void UpdateConstructionZones();

  // Updates global route solution.
  bool UpdateGlobalRouteSolution(
      bool has_accepted_new_global_route_lanes,
      bool is_proposed_route_solution_rejected,
      pb::RouteStatusUpdateReason route_status_update_reason,
      pb::PlanningRouteStatusDebug* planning_route_status_debug);

  // Restores last selected regional path from seed.
  bool RestoreLastSelectedRegionalPathFromSeed();

  // Restores last selected lane sequence from seed.
  bool SetLastSelectedLaneSequence(
      const ::google::protobuf::RepeatedField<::google::protobuf::int64>&
          last_selected_lane_sequence_lane_ids);

  // Sets current_lane_on_last_selected_lane_sequence_.
  // Calls this method right after calling SetLastSelectedLaneSequence().
  void SetCurrentLaneOnLastSelectedLaneSequence();

  // Updates current lane associator.
  bool UpdateCurrentLaneAssociator(
      const pb::WorldModelSeed& previous_world_model_seed,
      const pb::DecoupledManeuverSeed&
          previous_decoupled_lane_follow_maneuver_seed);

  // Updates current lane reasoner.
  bool UpdateCurrentLaneReasoner(
      const pb::WorldModelSeed& previous_world_model_seed,
      int64_t last_lane_change_finish_timestamp_msec);

  // Updates lane_graph_search_info_, and part of waypoint_lanes_cache_.
  void UpdateLaneGraphSearchInfo();

  // Updates regional map.
  bool UpdateRegionalMap(const pb::WorldModelSeed& previous_world_model_seed,
                         const pb::DecoupledManeuverSeed&
                             previous_decoupled_lane_follow_maneuver_seed,
                         pb::RegionalMapInfoDebug* regional_map_debug);

  // Adds the given |current_lane| to current lanes for regional path.
  // Note(Hui Luo): This function can only be used as the fallback strategy,
  // when current lanes for regional path is empty, and avoid lane sequence
  // update failure.
  void AddFallbackCurrentLaneForRegionalPath(
      const pnc_map::Lane* current_lane,
      const pb::DecoupledManeuverSeed&
          previous_decoupled_lane_follow_maneuver_seed);

  // Updates current lanes for waypoint graph.
  bool UpdateCurrentLanesForWaypointGraph(
      const pb::WorldModelSeed& previous_world_model_seed,
      const pb::DecoupledManeuverSeed&
          previous_decoupled_lane_follow_maneuver_seed);

  // Returns true if a route query for issue replan is updated.
  bool SetReplanRouteQuery(const pb::WorldModelSeed& previous_world_model_seed);

  // Updates current lane candidate debug info.
  void UpdateCurrentLaneDebug(
      const pb::BackupLanesDebug& backup_lanes_debug_info,
      pb::CurrentLaneDebug* current_lane_debug) const;

  // Updates pickup dropoff zone.
  void UpdatePickupDropoffZone(
      pb::PickupDropoffDebug* pickup_dropoff_debug,
      pb::ImmediatePullOverDebug* immediate_pull_over_debug);

  // Updates order track info.
  void UpdateOrderTrackInfo();

  // Updates the information of destination successor sections for pull over.
  void UpdateDestinationSuccessorSectionsInfo(
      pb::DestinationSuccessorSectionsInfoDebug* debug);

  // Updates pickup dropoff zone for immediate pull over.
  void UpdatePDZForImmediatePullOver();

  // Updates the construction zone to unordered map for quick access.
  void UpdateConstructionZoneMap();

  void UpdateLaneCongestionDebug(
      pb::LaneCongestionDebug* lane_congestion_debug) const;

  // Updates order start time info.
  void UpdateOrderStartTimeInfo();

  // Resets pull out jump out seed when it is a new trip.
  void ResetPullOutJumpOutSeedIfNewMission();

  // Sets true if we should generate pull out jump out sequence;
  void SetIfShouldGeneratePullOutJumpOutSequence(
      bool has_entered_jump_out_sequence);

  const PredictedTrajectoryWrapper& FindPrimaryTrajectory(
      ObjectId object_id) const;

  // Updates the debug for traffic signal reasoner.
  void UpdateTrafficSignalReasonerDebug(
      pb::TrafficSignalReasonerDebug* mutable_traffic_signal_reasoner_debug);

  // Updates the debug for drivable lane reasoner.
  void UpdateDrivableLaneReasonerDebug(
      pb::DrivableLaneReasonerDebug* mutable_drivable_lane_reasoner_debug)
      const;

  // Updates the debug for map change area processor.
  void UpdateMapChangeAreaProcessorDebug(
      pb::MapChangeAreaProcessorDebug* mutable_map_change_area_processor_debug);

  // Updates the debug for changed lane structure reasoner.
  void UpdateChangedLaneStructureReasonerDebug(
      pb::ChangedLaneStructureReasonerState*
          mutable_changed_lane_structure_debug);

  // Update the changed map elements and populate into pnc map history buffer.
  void UpdateChangedMapElements(pnc_map::pb::ChangedElements* changed_elements);

  // Populated pnc map history buffer with input |hdmap_changed_data_list|.
  void PopulatePncMapHistoryBuffer(
      const std::vector<SnapshotData<const hdmap::ChangedData>>&
          hdmap_changed_data_list,
      pnc_map::pb::ChangedElements* changed_elements);

  // Updates the debug for |ViolateLaneMarkingSingleton|.
  static void UpdateViolateLaneMarkingSingletonDebug(
      hdmap::ViolateLaneMarkings* violate_lane_marking_singleton_debug);

  // Updates the debug for |stuck_avoidance_reasoner|.
  void UpdateStuckAvoidanceReasonerDebug(
      pb::StuckAvoidanceReasonerState* mutable_stuck_avoidance_reasoner_debug);

  void UpdateCurrentLaneStateDebug(
      pb::CurrentLaneStateDebug* current_lane_state_debug);

  // Updates the debug for |previous_action_recorder|.
  void UpdatePreviousActionRecorderDebug(
      pb::PreviousActionRecorderDebug* previous_action_recorder_debug);

  // Returns odd info.
  OddInfo GetOddInfo() const;

  // Update bus objects that are queuing near bus bulb.
  void UpdateBusQueuingNearBusBulb(
      const math::geometry::PolylineCurve2d& last_nominal_path);

  // Holds PncMapService and recent map changes.
  pnc_map::PncMapHistoryBuffer pnc_map_history_buffer_;

  // Not owned by this class.
  const pb::WorldModelConfig& world_model_config_;

  // Latest snapshot of input messages.
  std::unique_ptr<const Snapshot> latest_snapshot_ptr_;

  // Background object_prediction_map_ for destruction.
  std::unique_ptr<const Snapshot> latest_snapshot_ptr_background_;

  // Robot state.
  std::unique_ptr<RobotState> robot_state_ptr_;

  // It collects traffic signal related info provided by perception.
  TrafficSignalReasoner traffic_signal_reasoner_;

  // It collects these lanes whose attribute of drivable has been changed.
  // For example we set some un-drivable lanes as drivable and set some
  // drivable lanes as un-drivable.
  DrivableLaneReasoner drivable_lane_reasoner_;

  // Regional sections locator.
  // Data used across cycles, which has been stored in state.
  RegionalSectionsLocator regional_sections_locator_;

  // Lane blockage detector.
  LaneBlockageDetector lane_blockage_detector_;

  // Lane congestion detector.
  LaneCongestionDetector lane_congestion_detector_;

  // The route model holds route information such as route solution, route
  // events.
  // Data used across cycles, which has been stored in state.
  RouteModel route_model_;

  // The waypoint lanes cache.
  WaypointLanesCache waypoint_lanes_cache_;

  // The lane graph search info.
  pb::LaneGraphSearchInfo lane_graph_search_info_;

  // Data used across cycles, which has been stored in state.
  RegionalMapGenerator regional_map_generator_;

  // A struct of regional path and map information.
  // Data used across cycles, which has been stored in state.
  RegionalMap regional_map_;

  // Converted from seed().last_selected_lane_sequence() (replace id with
  // pnc_map::Lane*). Does this with world model update().
  // last_selected_lane_sequence_ will be empty if lane lookup from id failed.
  // LIFE: cache data, can be cleared at beginning of each cycle.
  std::vector<const pnc_map::Lane*> last_selected_lane_sequence_;

  // Ego current lane on seed().last_selected_lane_sequence based on current
  // front bumper position.
  const pnc_map::Lane* current_lane_on_last_selected_lane_sequence_ = nullptr;

  // Agent list for filter or ignore purposes.
  std::optional<prediction::pb::AgentList> agent_list_ = std::nullopt;

  // A vector of tracked objects.
  std::vector<TrackedObject> tracked_objects_;

  // Route query updated each cycle to issue for global routing.
  // It is constructed from current drivable lanes.
  // Query timestamp 0 means no route query available.
  routing::pb::RouteQuery replan_route_query_;

  // Pickup and dropoff zone generator for regional parking.
  std::unique_ptr<VirtualPickupDropoffZoneInfoGenerator>
      regional_parking_zone_generator_ptr_;

  // Pickup and dropoff zone generator. Note the generator depends on validity
  // of robot_state_ptr_ which might not be available in constructor, so
  // making it a pointer as well.
  std::unique_ptr<PickupDropoffZoneInfoGenerator>
      pickup_dropoff_zone_generator_ptr_;

  // The PickupDropoffZone collected from the current route.
  std::vector<PickupDropoffZoneInfo> pickup_dropoff_zone_infos_;

  // Expanded successors of the destination sections in a certain range for
  // pullover.
  // Note: It is expected to start with destination section.
  std::vector<const pnc_map::Section*>
      destination_successor_sections_for_pullover_;
  // The percentage that the last section needs to extend.
  double pullover_final_extend_section_percentage_ = 1.0;

  // The pull over reference point is routing dest point or immediate pull over
  // point. It is used to check if pdz info needs to be updated in current
  // cycle. And it is used to check whether pull over can be triggered, and
  // generate pull over gap and destination.
  math::geometry::Point2d pull_over_reference_point_ =
      math::geometry::Point2d(0.0, 0.0);

  // The routing dest point of last cycle.
  math::geometry::Point2d cached_routing_dest_point_ =
      math::geometry::Point2d(0.0, 0.0);

  // Used to run destruction in parallel.
  std::unique_ptr<av_comm::ThreadPool> destruction_background_pool_;

  // Tracks waypoint assist state.
  // TODO(sixian): move this to global state manager.
  WaypointAssistTracker waypoint_assist_tracker_;

  // Analysis blockages for remote assist.
  assist::AssistBlockageAnalyzer assist_blockage_analyzer_;

  // The state manager of immediate pull over.
  ImmediatePullOverStateManager immediate_pull_over_state_manager_;

  // It collects request info for executing pull over.
  pull_over::PullOverRequestInfo pull_over_request_info_;

  // Determines current lane for different users. e.g. lane_blockage,
  // regional_map, etc.
  CurrentLaneAssociator current_lane_associator_;

  // Determines current lane for different users. e.g. lane_blockage,
  // regional_map, etc.
  CurrentLaneReasoner current_lane_reasoner_;

  // Records previous ego actions.
  PreviousActionRecorder previous_action_recorder_;

  std::tm* order_start_time_info_ = nullptr;

  // True if we should generate pull out jump out sequence.
  bool should_generate_pull_out_jump_out_sequence_ = false;

  // It handles the assist command from tele ops and generates assist directives
  // to help ego unstuck.
  AssistDirectiveGenerator assist_directive_generator_;

  // It schedules the assist command from tele ops and generates
  //  assist task to assist directives.
  AssistTaskScheduler assist_task_scheduler_;

  // Variable to store the count of dropped frames.
  int frame_drop_count_ = 0;

  // The information about lane sequence proposal.
  lane_selection::LaneSequenceProposalInfo lane_sequence_proposal_info_;

  PrecomputedStateLatticeSpiralData precomputed_spiral_data_;

  // It collects map change area related info.
  lane_selection::MapChangeAreaProcessor map_change_area_processor_;

  // It collects the empirical raw data from vehicle to cloud, and also receives
  // the processed empirical publish data from cloud to vehicle.
  lane_selection::EmpiricalInfoProcessor empirical_info_processor_;

  lane_selection::StuckAvoidanceReasoner stuck_avoidance_reasoner_;

  lane_selection::ChangedLaneStructureReasoner changed_lane_structure_reasoner_;

  WaypointAssistEmpiricalInfoProcessor
      waypoint_assist_empirical_info_processor_;

  // True if planner should respond mrc.
  bool should_planner_respond_mrc_ = false;

  // True if should trigger pull out after mrc with type of MRM_PULLRIGHT or
  // MRM_HARBOR.
  bool should_trigger_pull_out_by_mrc_ = false;

  FrameView frame_view_;
  std::shared_ptr<const pb::WorldModelSeed> last_finished_seed_;

  // Shared pointer to the GlobalObjectManager instance, responsible for
  // managing and maintaining global objects within the planning system. It
  // tracks and updates relevant planner objects, predicted trajectories,
  // construction zones. Provides interfaces for accessing, and update these
  // objects efficiently.
  std::shared_ptr<GlobalObjectManager> global_object_manager_;

  // The ml trajectory init state is diverged from planner.
  bool ml_trajectory_init_state_has_diverged_from_planner_ = false;

  friend WorldModel CreateDummyWorldModel(
      const pb::WorldModelConfig& config, const voy::Pose& pose,
      const voy::Canbus& canbus,
      const std::vector<::prediction::pb::Agent>& predicted_agents,
      const pnc_map::PncMapService* pnc_map_service,
      const voy::TrafficLights* traffic_light,
      const std::optional<routing::pb::RouteSolution>& route_solution_in,
      std::shared_ptr<const pb::TrajectoryGuiderOutput>
          trajectory_guider_output,
      const std::vector<voy::ConstructionZone>& construction_zones,
      const std::string& constraint_name,
      const std::optional<mrc::pb::MrcRequest>& mrc_request,
      const std::optional<const std::vector<pb::AssistResponse>>&
          assist_responses);

  friend speed::ReasoningTestFixture;
  friend LaneChangeTestFixture;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_H_
