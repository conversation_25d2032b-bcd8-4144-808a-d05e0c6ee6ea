{"bags": [{"bagName": "camera_orin4", "compressionType": "UNCOMPRESSED", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/camera_video_frame_102", "/camera_video_frame_103", "/camera_video_frame_104", "/camera_video_frame_105", "/camera_video_frame_106", "/camera_video_frame_107"], "maxSizeMb": 5}]}, {"bagName": "default_orin4", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/radar_scan_1", "/radar_scan_2", "/radar_scan_3", "/radar_scan_4", "/planning/fault_detector_response", "/planning/customer_monitor_visual", "/planning/mrc_immediate_pullover", "/health/can_inspector_node", "/health/cpubox2_custom_recorder_node", "/health/cpubox2_system_control", "/health/cpubox2_recorder_node", "/health/recorder_node_orin4", "/health/device_radar", "/health/planner_node", "/health/routing_node", "/health/prediction_node", "/health/xavier4_aurix_driver", "/health/xavier4_custom_recorder_node", "/health/xavier3_system_control", "/health/xavier4_system_control", "/health/gmsl_camera_xavier3", "/health/gmsl_camera_xavier4", "/health/cpubox2_ntb_bridge", "/health/cpubox2_gpu_monitor", "/health/inference_server_node", "/health/fallback_can_decode_node", "/health/fallback_level1_control_node", "/health/fallback_planning", "/health/fallback_level1", "/health/fallback_miira", "/health/restore_network_detector", "/health/teleassist", "/health/mrc_node", "/health/exception_handler_node", "/fallback_level1", "/fallback_level1_control", "/fallback_level1_control/control_debug", "/fallback_level1_control/seed", "/fallback_level1_fault_injection", "/fallback_level2", "/fallback_level2/chassis", "/fallback_level2/camera", "/fallback_level2/radar", "/fallback_anomaly_detection", "/health/realtime_map_node", "/teleassist/traffic_light", "/teleassist/status", "/teleassist/zone_list", "/teleassist/net_info", "/teleassist/delay_info", "/teleassist/degradation_info", "/fallback_planning_trajectory", "/fallback_planning_trajectory_result", "/cpubox2_external_disk_free_space", "/camera/h264_encode_signal"], "maxSizeMb": 5}]}, {"bagName": "autonomy_orin4", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/bag_message_metadata", "/prediction/predicted_objects", "/planning/trajectory", "/planning/planning_lane_sequence", "/planning/ops_warning", "/planning/pullout_request", "/planning/stuck_detection_recall_signal", "/planning/remote_warning_signal", "/planning/assist_request", "/planning/assist_response", "/planning/remote_speed_limit", "/planning/seed", "/planner/state", "/planning/snapshot_seed", "/trajectory_guider/guide_trajectory", "/planning/ttc_detect_result", "/exception_handler/debug", "/exception_handler/debug_for_perception", "/exception_handler/global_init_state", "/exception_handler/global_selection_state", "/exception_handler/global_selection_seed", "/exception_handler/trajectory", "/planning/message_metadata", "/planning/message_metadata_input", "/mrc/mrc_debug", "/mrc/mrc_request", "/mrc/mrc_seed", "/planning/planning_empirical_raw_data", "/planning/trajectory_result"], "maxSizeMb": 5}]}, {"bagName": "debug_orin4", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/planning/planning_debug", "/routing/routing_debug", "/fallback_planning_debug"], "maxSizeMb": 5}]}, {"bagName": "lidar_orin4", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/lidar_gen4_packets_main_1", "/lidar_gen4_packets_main_2", "/lidar_gen4_packets_main_3", "/lidar_gen4_packets_main_4", "/lidar_gen4_udp_packets_main_1", "/lidar_gen4_udp_packets_main_2", "/lidar_gen4_udp_packets_main_3", "/lidar_gen4_udp_packets_main_4"], "maxSizeMb": 5}]}], "numberCompressionThread": 2, "maxBufferDurationMs": 5000, "maxBufferSizeMb": 4096}