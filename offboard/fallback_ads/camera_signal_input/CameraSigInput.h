/*
 * File: CameraSigInput.h
 *
 * Code generated for Simulink model 'CameraSigInput'.
 *
 * Model version                  : 1.22
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Thu Dec 12 16:01:01 2024
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_CameraSigInput_h_
#define RTW_HEADER_CameraSigInput_h_
#ifndef CameraSigInput_COMMON_INCLUDES_
#define CameraSigInput_COMMON_INCLUDES_
#include "common_math_library/rtwtypes.h"
#include "_out/Appl/GenData/Components/Rte_CameraSigInput.h"
#endif                                 /* CameraSigInput_COMMON_INCLUDES_ */

#include "CameraSigInput_types.h"

/* Macros for accessing real-time model data structure */

/* Parameters (default storage) */
struct P_CameraSigInput_T_ {
  CSI_LaneInfo_Struct CSI_LaneInfo_Y0; /* Computed Parameter: CSI_LaneInfo_Y0
                                        * Referenced by: '<S1>/CSI_LaneInfo'
                                        */
  CSI_ObjectInfo_Struct CSI_ObjectInfo_Y0;/* Computed Parameter: CSI_ObjectInfo_Y0
                                           * Referenced by: '<S1>/CSI_ObjectInfo'
                                           */
  float32 Constant_Value;              /* Computed Parameter: Constant_Value
                                        * Referenced by: '<S4>/Constant'
                                        */
  float32 Constant1_Value;             /* Computed Parameter: Constant1_Value
                                        * Referenced by: '<S4>/Constant1'
                                        */
};

/* Block parameters (default storage) */
extern P_CameraSigInput_T CameraSigInput_P;

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern uint8 CSI_CalibrationStatus;    /* '<S4>/Signal Conversion' */

/* - */
extern uint8 CSI_ColorTypeLf;          /* '<S4>/Signal Conversion10' */
extern uint8 CSI_ColorTypeLfAdj;       /* '<S4>/Signal Conversion39' */
extern uint8 CSI_ColorTypeLfRe;        /* '<S4>/Signal Conversion49' */
extern uint8 CSI_ColorTypeRi;          /* '<S4>/Signal Conversion27' */
extern uint8 CSI_ColorTypeRiAdj;       /* '<S4>/Signal Conversion57' */
extern uint8 CSI_ColorTypeRiRe;        /* '<S4>/Signal Conversion87' */
extern float32 CSI_CrvRateLf;          /* '<S4>/Signal Conversion11' */
extern float32 CSI_CrvRateLfAdj;       /* '<S4>/Signal Conversion40' */
extern float32 CSI_CrvRateLfRe;        /* '<S4>/Signal Conversion37' */
extern float32 CSI_CrvRateRi;          /* '<S4>/Signal Conversion28' */
extern float32 CSI_CrvRateRiAdj;       /* '<S4>/Signal Conversion58' */
extern float32 CSI_CrvRateRiRe;        /* '<S4>/Signal Conversion86' */
extern float32 CSI_CurvatureLf;        /* '<S4>/Signal Conversion9' */
extern float32 CSI_CurvatureLfAdj;     /* '<S4>/Signal Conversion73' */
extern float32 CSI_CurvatureLfRe;      /* '<S4>/Signal Conversion38' */
extern float32 CSI_CurvatureRi;        /* '<S4>/Signal Conversion26' */
extern float32 CSI_CurvatureRiAdj;     /* '<S4>/Signal Conversion56' */
extern float32 CSI_CurvatureRiRe;      /* '<S4>/Signal Conversion84' */
extern float32 CSI_ExistProbLf;        /* '<S4>/Signal Conversion6' */
extern float32 CSI_ExistProbLfAdj;     /* '<S4>/Signal Conversion70' */
extern float32 CSI_ExistProbLfRe;      /* '<S4>/Signal Conversion102' */
extern float32 CSI_ExistProbRi;        /* '<S4>/Signal Conversion23' */
extern float32 CSI_ExistProbRiAdj;     /* '<S4>/Signal Conversion53' */
extern float32 CSI_ExistProbRiRe;      /* '<S4>/Signal Conversion82' */
extern uint8 CSI_FC_CIPV_ID;           /* '<S8>/Convert178' */
extern uint8 CSI_FC_FrontCameraCalibrationStatus;/* '<S12>/Convert176' */

/* - */
extern uint8 CSI_FC_LaneChangeStatus;  /* '<S12>/Convert177' */

/* - */
extern float32 CSI_FC_LineTiStamp;     /* '<S6>/Convert34' */
extern float32 CSI_FC_Line_01_HeadingAngle;/* '<S6>/Convert8' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_01_Id;        /* '<S6>/Convert9' */

/* Left Line of Host Lane */
extern uint8 CSI_FC_Line_01_Type;      /* '<S6>/Convert10' */
extern float32 CSI_FC_Line_01_Width;   /* '<S6>/Convert11' */
extern uint8 CSI_FC_Line_01_color;     /* '<S6>/Convert1' */
extern float32 CSI_FC_Line_01_dx_End;  /* '<S6>/Convert2' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_01_dx_End_std;/* '<S6>/Convert3' */
extern float32 CSI_FC_Line_01_dx_Start;/* '<S6>/Convert4' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_01_dx_Start_std;/* '<S6>/Convert5' */
extern float32 CSI_FC_Line_01_dy;      /* '<S6>/Convert6' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_FC_Line_01_exist_prob;/* '<S6>/Convert7' */
extern float32 CSI_FC_Line_02_HeadingAngle;/* '<S6>/Convert19' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_02_Id;        /* '<S6>/Convert20' */

/* Right Line of Host Lane */
extern uint8 CSI_FC_Line_02_Type;      /* '<S6>/Convert21' */
extern float32 CSI_FC_Line_02_Width;   /* '<S6>/Convert22' */
extern uint8 CSI_FC_Line_02_color;     /* '<S6>/Convert12' */
extern float32 CSI_FC_Line_02_dx_End;  /* '<S6>/Convert13' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_02_dx_End_std;/* '<S6>/Convert14' */
extern float32 CSI_FC_Line_02_dx_Start;/* '<S6>/Convert15' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_02_dx_Start_std;/* '<S6>/Convert16' */
extern float32 CSI_FC_Line_02_dy;      /* '<S6>/Convert17' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
extern float32 CSI_FC_Line_02_exist_prob;/* '<S6>/Convert18' */
extern float32 CSI_FC_Line_03_HeadingAngle;/* '<S6>/Convert30' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_03_Id;        /* '<S6>/Convert31' */

/* Left Line of Host Lane */
extern uint8 CSI_FC_Line_03_Type;      /* '<S6>/Convert48' */
extern float32 CSI_FC_Line_03_Width;   /* '<S6>/Convert49' */
extern uint8 CSI_FC_Line_03_color;     /* '<S6>/Convert23' */
extern float32 CSI_FC_Line_03_dx_End;  /* '<S6>/Convert24' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_03_dx_End_std;/* '<S6>/Convert25' */
extern float32 CSI_FC_Line_03_dx_Start;/* '<S6>/Convert26' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_03_dx_Start_std;/* '<S6>/Convert27' */
extern float32 CSI_FC_Line_03_dy;      /* '<S6>/Convert28' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_FC_Line_03_exist_prob;/* '<S6>/Convert29' */
extern float32 CSI_FC_Line_04_HeadingAngle;/* '<S7>/Convert34' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_04_Id;        /* '<S7>/Convert35' */

/* Left Line of Host Lane */
extern uint8 CSI_FC_Line_04_Type;      /* '<S7>/Convert36' */
extern float32 CSI_FC_Line_04_Width;   /* '<S7>/Convert37' */
extern uint8 CSI_FC_Line_04_color;     /* '<S7>/Convert17' */
extern float32 CSI_FC_Line_04_dx_End;  /* '<S7>/Convert18' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_04_dx_End_std;/* '<S7>/Convert19' */
extern float32 CSI_FC_Line_04_dx_Start;/* '<S7>/Convert20' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_04_dx_Start_std;/* '<S7>/Convert21' */
extern float32 CSI_FC_Line_04_dy;      /* '<S7>/Convert22' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_FC_Line_04_exist_prob;/* '<S7>/Convert31' */
extern float32 CSI_FC_Line_05_HeadingAngle;/* '<S7>/Convert8' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_05_Type;      /* '<S7>/Convert15' */
extern float32 CSI_FC_Line_05_Width;   /* '<S7>/Convert16' */
extern uint8 CSI_FC_Line_05_color;     /* '<S7>/Convert1' */
extern float32 CSI_FC_Line_05_dx_End;  /* '<S7>/Convert2' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_05_dx_End_std;/* '<S7>/Convert3' */
extern float32 CSI_FC_Line_05_dx_Start;/* '<S7>/Convert4' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_05_dx_Start_std;/* '<S7>/Convert5' */
extern float32 CSI_FC_Line_05_dy;      /* '<S7>/Convert6' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_FC_Line_05_exist_prob;/* '<S7>/Convert7' */
extern float32 CSI_FC_Line_06_HeadingAngle;/* '<S7>/Convert30' */

/* Conterclolkwise is Positive */
extern uint8 CSI_FC_Line_06_Type;      /* '<S7>/Convert48' */
extern float32 CSI_FC_Line_06_Width;   /* '<S7>/Convert49' */
extern uint8 CSI_FC_Line_06_color;     /* '<S7>/Convert23' */
extern float32 CSI_FC_Line_06_dx_End;  /* '<S7>/Convert24' */

/* End of Effective visual range */
extern float32 CSI_FC_Line_06_dx_End_std;/* '<S7>/Convert25' */
extern float32 CSI_FC_Line_06_dx_Start;/* '<S7>/Convert26' */

/* Start of Effective visual range */
extern float32 CSI_FC_Line_06_dx_Start_std;/* '<S7>/Convert27' */
extern float32 CSI_FC_Line_06_dy;      /* '<S7>/Convert28' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_FC_Line_06_exist_prob;/* '<S7>/Convert29' */
extern uint8 CSI_FTFC_Line_01_MeasureType;/* '<S6>/Convert37' */
extern uint8 CSI_FTFC_Line_01_ObstacleFlg;/* '<S6>/Convert38' */
extern uint8 CSI_FTFC_Line_01_ParseConf;/* '<S6>/Convert39' */
extern uint8 CSI_FTFC_Line_01_RMSE;    /* '<S6>/Convert40' */
extern float32 CSI_FTFC_Line_01_curvature_alte;/* '<S6>/Convert35' */
extern float32 CSI_FTFC_Line_01_curve; /* '<S6>/Convert36' */
extern uint8 CSI_FTFC_Line_02_MeasureType;/* '<S6>/Convert43' */
extern uint8 CSI_FTFC_Line_02_ObstacleFlg;/* '<S6>/Convert44' */
extern uint8 CSI_FTFC_Line_02_ParseConf;/* '<S6>/Convert45' */
extern uint8 CSI_FTFC_Line_02_RMSE;    /* '<S6>/Convert46' */
extern float32 CSI_FTFC_Line_02_curvature_alte;/* '<S6>/Convert41' */
extern float32 CSI_FTFC_Line_02_curve; /* '<S6>/Convert42' */
extern uint8 CSI_FTFC_Line_03_MeasureType;/* '<S6>/Convert32' */
extern uint8 CSI_FTFC_Line_03_ObstacleFlg;/* '<S7>/Convert50' */
extern uint8 CSI_FTFC_Line_03_ParseConf;/* '<S7>/Convert51' */
extern uint8 CSI_FTFC_Line_03_RMSE;    /* '<S7>/Convert52' */
extern float32 CSI_FTFC_Line_03_curvature_alte;/* '<S6>/Convert33' */
extern float32 CSI_FTFC_Line_03_curve; /* '<S6>/Convert47' */
extern uint8 CSI_FTFC_Line_04_MeasureType;/* '<S7>/Convert43' */
extern uint8 CSI_FTFC_Line_04_ObstacleFlg;/* '<S7>/Convert38' */
extern uint8 CSI_FTFC_Line_04_ParseConf;/* '<S7>/Convert39' */
extern uint8 CSI_FTFC_Line_04_RMSE;    /* '<S7>/Convert40' */
extern float32 CSI_FTFC_Line_04_curvature_alte;/* '<S7>/Convert41' */
extern float32 CSI_FTFC_Line_04_curve; /* '<S7>/Convert42' */
extern uint8 CSI_FTFC_Line_05_MeasureType;/* '<S7>/Convert9' */
extern uint8 CSI_FTFC_Line_05_ObstacleFlg;/* '<S7>/Convert11' */
extern uint8 CSI_FTFC_Line_05_ParseConf;/* '<S7>/Convert12' */
extern uint8 CSI_FTFC_Line_05_RMSE;    /* '<S7>/Convert13' */
extern float32 CSI_FTFC_Line_05_curvature_alte;/* '<S7>/Convert10' */
extern float32 CSI_FTFC_Line_05_curve; /* '<S7>/Convert14' */
extern uint8 CSI_FTFC_Line_06_MeasureType;/* '<S7>/Convert32' */
extern uint8 CSI_FTFC_Line_06_ObstacleFlg;/* '<S7>/Convert44' */
extern uint8 CSI_FTFC_Line_06_ParseConf;/* '<S7>/Convert45' */
extern uint8 CSI_FTFC_Line_06_RMSE;    /* '<S7>/Convert46' */
extern float32 CSI_FTFC_Line_06_curvature_alte;/* '<S7>/Convert33' */
extern float32 CSI_FTFC_Line_06_curve; /* '<S7>/Convert47' */
extern float32 CSI_FrFr_AccOBJ_Ax;     /* '<S9>/Convert118' */
extern float32 CSI_FrFr_AccOBJ_Ay;     /* '<S9>/Convert119' */
extern uint8 CSI_FrFr_AccOBJ_Brakelight_Info;/* '<S9>/Convert120' */
extern uint8 CSI_FrFr_AccOBJ_Class;    /* '<S8>/Convert116' */
extern float32 CSI_FrFr_AccOBJ_Dx;     /* '<S9>/Convert121' */
extern float32 CSI_FrFr_AccOBJ_Dx_Vnce;/* '<S9>/Convert215' */
extern float32 CSI_FrFr_AccOBJ_Dy;     /* '<S9>/Convert122' */
extern float32 CSI_FrFr_AccOBJ_Dy_Vnce;/* '<S9>/Convert216' */
extern float32 CSI_FrFr_AccOBJ_ExistProb;/* '<S9>/Convert123' */
extern uint8 CSI_FrFr_AccOBJ_FusionedFC_Track_ID;/* '<S9>/Convert218' */

/* It is the track ID of FC that fusioned with FR ACC FrFr target */
extern float32 CSI_FrFr_AccOBJ_HeadingAngle;/* '<S9>/Convert124' */
extern float32 CSI_FrFr_AccOBJ_Height; /* '<S9>/Convert219' */
extern float32 CSI_FrFr_AccOBJ_Length; /* '<S9>/Convert220' */
extern float32 CSI_FrFr_AccOBJ_ObstacleProb;/* '<S9>/Convert125' */
extern uint8 CSI_FrFr_AccOBJ_Taillight_Info;/* '<S9>/Convert221' */
extern uint8 CSI_FrFr_AccOBJ_Track_Age;/* '<S9>/Convert222' */
extern uint8 CSI_FrFr_AccOBJ_Track_ID; /* '<S9>/Convert223' */
extern float32 CSI_FrFr_AccOBJ_Vx;     /* '<S9>/Convert126' */
extern float32 CSI_FrFr_AccOBJ_Vx_std; /* '<S9>/Convert224' */
extern float32 CSI_FrFr_AccOBJ_Vy;     /* '<S9>/Convert127' */
extern float32 CSI_FrFr_AccOBJ_Vy_std; /* '<S9>/Convert225' */
extern float32 CSI_FrFr_AccOBJ_Width;  /* '<S8>/Convert117' */
extern uint8 CSI_FrFr_AccOBJ_confi;    /* '<S11>/Convert265' */
extern uint8 CSI_FrFr_AccOBJ_fusion_Sts;/* '<S9>/Convert217' */
extern float32 CSI_Fr_AccOBJ_Ax;       /* '<S8>/Convert102' */
extern float32 CSI_Fr_AccOBJ_Ay;       /* '<S8>/Convert103' */
extern uint8 CSI_Fr_AccOBJ_Brakelight_Info;/* '<S8>/Convert104' */
extern uint8 CSI_Fr_AccOBJ_Class;      /* '<S8>/Convert105' */
extern float32 CSI_Fr_AccOBJ_Dx;       /* '<S8>/Convert106' */
extern float32 CSI_Fr_AccOBJ_Dx_Vnce;  /* '<S8>/Convert200' */
extern float32 CSI_Fr_AccOBJ_Dy;       /* '<S8>/Convert107' */
extern float32 CSI_Fr_AccOBJ_Dy_Vnce;  /* '<S8>/Convert201' */
extern float32 CSI_Fr_AccOBJ_ExistProb;/* '<S8>/Convert108' */
extern uint8 CSI_Fr_AccOBJ_FusionedFC_Track_ID;/* '<S8>/Convert203' */

/* It is the track ID of FC that fusioned with FR ACC Fr target */
extern float32 CSI_Fr_AccOBJ_HeadingAngle;/* '<S8>/Convert109' */
extern float32 CSI_Fr_AccOBJ_Height;   /* '<S8>/Convert110' */
extern float32 CSI_Fr_AccOBJ_Length;   /* '<S8>/Convert111' */
extern float32 CSI_Fr_AccOBJ_ObstacleProb;/* '<S8>/Convert112' */
extern uint8 CSI_Fr_AccOBJ_Taillight_Info;/* '<S8>/Convert221' */
extern uint8 CSI_Fr_AccOBJ_Track_Age;  /* '<S8>/Convert205' */
extern uint8 CSI_Fr_AccOBJ_Track_ID;   /* '<S8>/Convert206' */
extern float32 CSI_Fr_AccOBJ_Vx;       /* '<S8>/Convert113' */
extern float32 CSI_Fr_AccOBJ_Vx_std;   /* '<S8>/Convert207' */
extern float32 CSI_Fr_AccOBJ_Vy;       /* '<S8>/Convert114' */
extern float32 CSI_Fr_AccOBJ_Vy_std;   /* '<S8>/Convert208' */
extern float32 CSI_Fr_AccOBJ_Width;    /* '<S8>/Convert115' */
extern uint8 CSI_Fr_AccOBJ_confi;      /* '<S11>/Convert264' */
extern uint8 CSI_Fr_AccOBJ_fusion_Sts; /* '<S8>/Convert202' */
extern float32 CSI_HeadingAngleLf;     /* '<S4>/Signal Conversion8' */

/* Conterclolkwise is Positive */
extern float32 CSI_HeadingAngleLfAdj;  /* '<S4>/Signal Conversion72' */

/* Conterclolkwise is Positive */
extern float32 CSI_HeadingAngleLfRe;   /* '<S4>/Signal Conversion104' */

/* Conterclolkwise is Positive */
extern float32 CSI_HeadingAngleRi;     /* '<S4>/Signal Conversion25' */

/* Conterclolkwise is Positive */
extern float32 CSI_HeadingAngleRiAdj;  /* '<S4>/Signal Conversion55' */

/* Conterclolkwise is Positive */
extern float32 CSI_HeadingAngleRiRe;   /* '<S4>/Signal Conversion85' */

/* Conterclolkwise is Positive */
extern uint8 CSI_LaneChangeStatus;     /* '<S4>/Signal Conversion1' */

/* - */
extern uint8 CSI_LaneIDLf;             /* '<S4>/Signal Conversion4' */

/* Left Line of Host Lane */
extern uint8 CSI_LaneIDLfAdj;          /* '<S4>/Signal Conversion68' */

/* Left Line of Host Lane */
extern uint8 CSI_LaneIDRi;             /* '<S4>/Signal Conversion20' */

/* Right Line of Host Lane */
extern uint8 CSI_LaneIDRiAdj;          /* '<S4>/Signal Conversion50' */

/* Right Line of Host Lane */
extern float32 CSI_LaneTimeStamp;      /* '<S4>/Signal Conversion2' */
extern float32 CSI_LeFr_AccOBJ_Ax;     /* '<S10>/Convert144' */
extern float32 CSI_LeFr_AccOBJ_Ay;     /* '<S10>/Convert145' */
extern uint8 CSI_LeFr_AccOBJ_Brakelight_Info;/* '<S10>/Convert146' */
extern uint8 CSI_LeFr_AccOBJ_Class;    /* '<S9>/Convert140' */
extern float32 CSI_LeFr_AccOBJ_Dx;     /* '<S10>/Convert147' */
extern float32 CSI_LeFr_AccOBJ_Dx_Vnce;/* '<S10>/Convert240' */
extern float32 CSI_LeFr_AccOBJ_Dy;     /* '<S9>/Convert141' */
extern float32 CSI_LeFr_AccOBJ_Dy_Vnce;/* '<S9>/Convert237' */
extern float32 CSI_LeFr_AccOBJ_ExistProb;/* '<S10>/Convert148' */
extern uint8 CSI_LeFr_AccOBJ_FusionedFC_Track_ID;/* '<S10>/Convert242' */

/* It is the track ID of FC that fusioned with FR ACC LeFr target */
extern float32 CSI_LeFr_AccOBJ_HeadingAngle;/* '<S9>/Convert142' */
extern float32 CSI_LeFr_AccOBJ_Height; /* '<S9>/Convert238' */
extern float32 CSI_LeFr_AccOBJ_Length; /* '<S9>/Convert239' */
extern float32 CSI_LeFr_AccOBJ_ObstacleProb;/* '<S10>/Convert149' */
extern uint8 CSI_LeFr_AccOBJ_Taillight_Info;/* '<S10>/Convert243' */
extern uint8 CSI_LeFr_AccOBJ_Track_Age;/* '<S10>/Convert244' */
extern uint8 CSI_LeFr_AccOBJ_Track_ID; /* '<S10>/Convert245' */
extern float32 CSI_LeFr_AccOBJ_Vx;     /* '<S10>/Convert150' */
extern float32 CSI_LeFr_AccOBJ_Vx_std; /* '<S10>/Convert246' */
extern float32 CSI_LeFr_AccOBJ_Vy;     /* '<S10>/Convert151' */
extern float32 CSI_LeFr_AccOBJ_Vy_std; /* '<S10>/Convert247' */
extern float32 CSI_LeFr_AccOBJ_Width;  /* '<S9>/Convert143' */
extern uint8 CSI_LeFr_AccOBJ_confi;    /* '<S11>/Convert267' */
extern uint8 CSI_LeFr_AccOBJ_fusion_Sts;/* '<S10>/Convert241' */
extern float32 CSI_Le_AccOBJ_Ax;       /* '<S9>/Convert128' */
extern float32 CSI_Le_AccOBJ_Ay;       /* '<S9>/Convert129' */
extern uint8 CSI_Le_AccOBJ_Brakelight_Info;/* '<S9>/Convert130' */
extern uint8 CSI_Le_AccOBJ_Class;      /* '<S9>/Convert131' */
extern float32 CSI_Le_AccOBJ_Dx;       /* '<S9>/Convert132' */
extern float32 CSI_Le_AccOBJ_Dx_Vnce;  /* '<S9>/Convert226' */
extern float32 CSI_Le_AccOBJ_Dy;       /* '<S9>/Convert133' */
extern float32 CSI_Le_AccOBJ_Dy_Vnce;  /* '<S9>/Convert227' */
extern float32 CSI_Le_AccOBJ_ExistProb;/* '<S9>/Convert134' */
extern uint8 CSI_Le_AccOBJ_FusionedFC_Track_ID;/* '<S9>/Convert229' */

/* It is the track ID of FC that fusioned with FR ACC Le target */
extern float32 CSI_Le_AccOBJ_HeadingAngle;/* '<S9>/Convert135' */
extern float32 CSI_Le_AccOBJ_Height;   /* '<S9>/Convert230' */
extern float32 CSI_Le_AccOBJ_Length;   /* '<S9>/Convert231' */
extern float32 CSI_Le_AccOBJ_ObstacleProb;/* '<S9>/Convert136' */
extern uint8 CSI_Le_AccOBJ_Taillight_Info;/* '<S9>/Convert232' */
extern uint8 CSI_Le_AccOBJ_Track_Age;  /* '<S9>/Convert233' */
extern uint8 CSI_Le_AccOBJ_Track_ID;   /* '<S9>/Convert234' */
extern float32 CSI_Le_AccOBJ_Vx;       /* '<S9>/Convert137' */
extern float32 CSI_Le_AccOBJ_Vx_std;   /* '<S9>/Convert235' */
extern float32 CSI_Le_AccOBJ_Vy;       /* '<S9>/Convert138' */
extern float32 CSI_Le_AccOBJ_Vy_std;   /* '<S9>/Convert236' */
extern float32 CSI_Le_AccOBJ_Width;    /* '<S9>/Convert139' */
extern uint8 CSI_Le_AccOBJ_confi;      /* '<S11>/Convert266' */
extern uint8 CSI_Le_AccOBJ_fusion_Sts; /* '<S9>/Convert228' */
extern float32 CSI_MakerWidthLf;       /* '<S4>/Signal Conversion12' */
extern float32 CSI_MakerWidthLfAdj;    /* '<S4>/Signal Conversion41' */
extern float32 CSI_MakerWidthLfRe;     /* '<S4>/Signal Conversion74' */
extern float32 CSI_MakerWidthRi;       /* '<S4>/Signal Conversion29' */
extern float32 CSI_MakerWidthRiAdj;    /* '<S4>/Signal Conversion59' */
extern float32 CSI_MakerWidthRiRe;     /* '<S4>/Signal Conversion88' */
extern uint8 CSI_MarkerTypeLf;         /* '<S4>/Signal Conversion3' */
extern uint8 CSI_MarkerTypeLfAdj;      /* '<S4>/Signal Conversion60' */
extern uint8 CSI_MarkerTypeLfRe;       /* '<S4>/Signal Conversion96' */
extern uint8 CSI_MarkerTypeRi;         /* '<S4>/Signal Conversion22' */
extern uint8 CSI_MarkerTypeRiAdj;      /* '<S4>/Signal Conversion52' */
extern uint8 CSI_MarkerTypeRiRe;       /* '<S4>/Signal Conversion80' */
extern uint8 CSI_MeasureTypeLf;        /* '<S4>/Signal Conversion5' */
extern uint8 CSI_MeasureTypeLfAdj;     /* '<S4>/Signal Conversion69' */
extern uint8 CSI_MeasureTypeLfRe;      /* '<S4>/Signal Conversion101' */
extern uint8 CSI_MeasureTypeRi;        /* '<S4>/Signal Conversion21' */
extern uint8 CSI_MeasureTypeRiAdj;     /* '<S4>/Signal Conversion51' */
extern uint8 CSI_MeasureTypeRiRe;      /* '<S4>/Signal Conversion81' */
extern uint8 CSI_ObstacleFlgLf;        /* '<S4>/Signal Conversion18' */
extern uint8 CSI_ObstacleFlgLfAdj;     /* '<S4>/Signal Conversion47' */
extern uint8 CSI_ObstacleFlgLfRe;      /* '<S4>/Signal Conversion91' */
extern uint8 CSI_ObstacleFlgRi;        /* '<S4>/Signal Conversion36' */
extern uint8 CSI_ObstacleFlgRiAdj;     /* '<S4>/Signal Conversion67' */
extern uint8 CSI_ObstacleFlgRiRe;      /* '<S4>/Signal Conversion93' */
extern uint8 CSI_ParseConfLf;          /* '<S4>/Signal Conversion17' */
extern uint8 CSI_ParseConfLfAdj;       /* '<S4>/Signal Conversion46' */
extern uint8 CSI_ParseConfLfRe;        /* '<S4>/Signal Conversion99' */
extern uint8 CSI_ParseConfRi;          /* '<S4>/Signal Conversion34' */
extern uint8 CSI_ParseConfRiAdj;       /* '<S4>/Signal Conversion65' */
extern uint8 CSI_ParseConfRiRe;        /* '<S4>/Signal Conversion94' */
extern uint8 CSI_PolyfitRmseLf;        /* '<S4>/Signal Conversion19' */
extern uint8 CSI_PolyfitRmseLfAdj;     /* '<S4>/Signal Conversion48' */
extern uint8 CSI_PolyfitRmseLfRe;      /* '<S4>/Signal Conversion100' */
extern uint8 CSI_PolyfitRmseRi;        /* '<S4>/Signal Conversion35' */
extern uint8 CSI_PolyfitRmseRiAdj;     /* '<S4>/Signal Conversion66' */
extern uint8 CSI_PolyfitRmseRiRe;      /* '<S4>/Signal Conversion78' */
extern float32 CSI_PosXEndLf;          /* '<S4>/Signal Conversion15' */

/* End of Effective visual range */
extern float32 CSI_PosXEndLfAdj;       /* '<S4>/Signal Conversion44' */

/* End of Effective visual range */
extern float32 CSI_PosXEndLfRe;        /* '<S4>/Signal Conversion76' */

/* End of Effective visual range */
extern float32 CSI_PosXEndRi;          /* '<S4>/Signal Conversion32' */

/* End of Effective visual range */
extern float32 CSI_PosXEndRiAdj;       /* '<S4>/Signal Conversion63' */

/* End of Effective visual range */
extern float32 CSI_PosXEndRiRe;        /* '<S4>/Signal Conversion90' */

/* End of Effective visual range */
extern float32 CSI_PosXEndStdLf;       /* '<S4>/Signal Conversion16' */
extern float32 CSI_PosXEndStdLfAdj;    /* '<S4>/Signal Conversion45' */
extern float32 CSI_PosXEndStdLfRe;     /* '<S4>/Signal Conversion77' */
extern float32 CSI_PosXEndStdRi;       /* '<S4>/Signal Conversion33' */
extern float32 CSI_PosXEndStdRiAdj;    /* '<S4>/Signal Conversion64' */
extern float32 CSI_PosXEndStdRiRe;     /* '<S4>/Signal Conversion95' */
extern float32 CSI_PosXStartLf;        /* '<S4>/Signal Conversion13' */
extern float32 CSI_PosXStartLfAdj;     /* '<S4>/Signal Conversion42' */
extern float32 CSI_PosXStartLfRe;      /* '<S4>/Signal Conversion75' */
extern float32 CSI_PosXStartRi;        /* '<S4>/Signal Conversion30' */

/* Start of Effective visual range */
extern float32 CSI_PosXStartRiAdj;     /* '<S4>/Signal Conversion61' */

/* Start of Effective visual range */
extern float32 CSI_PosXStartRiRe;      /* '<S4>/Signal Conversion92' */

/* Start of Effective visual range */
extern float32 CSI_PosY0Lf;            /* '<S4>/Signal Conversion7' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_PosY0LfAdj;         /* '<S4>/Signal Conversion71' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_PosY0LfRe;          /* '<S4>/Signal Conversion103' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
extern float32 CSI_PosY0Ri;            /* '<S4>/Signal Conversion24' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
extern float32 CSI_PosY0RiAdj;         /* '<S4>/Signal Conversion54' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
extern float32 CSI_PosY0RiRe;          /* '<S4>/Signal Conversion83' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
extern float32 CSI_PosYStartStdLf;     /* '<S4>/Signal Conversion14' */

/* Start of Effective visual range */
extern float32 CSI_PosYStartStdLfAdj;  /* '<S4>/Signal Conversion43' */

/* Start of Effective visual range */
extern float32 CSI_PosYStartStdLfRe;   /* '<S4>/Signal Conversion79' */

/* Start of Effective visual range */
extern float32 CSI_PosYStartStdRi;     /* '<S4>/Signal Conversion31' */
extern float32 CSI_PosYStartStdRiAdj;  /* '<S4>/Signal Conversion62' */
extern float32 CSI_PosYStartStdRiRe;   /* '<S4>/Signal Conversion89' */
extern float32 CSI_RiFr_AccOBJ_Ax;     /* '<S11>/Convert170' */
extern float32 CSI_RiFr_AccOBJ_Ay;     /* '<S11>/Convert171' */
extern uint8 CSI_RiFr_AccOBJ_Brakelight_Info;/* '<S11>/Convert172' */
extern uint8 CSI_RiFr_AccOBJ_Class;    /* '<S10>/Convert164' */
extern float32 CSI_RiFr_AccOBJ_Dx;     /* '<S10>/Convert165' */
extern float32 CSI_RiFr_AccOBJ_Dx_Vnce;/* '<S10>/Convert259' */
extern float32 CSI_RiFr_AccOBJ_Dy;     /* '<S10>/Convert166' */
extern float32 CSI_RiFr_AccOBJ_Dy_Vnce;/* '<S10>/Convert260' */
extern float32 CSI_RiFr_AccOBJ_ExistProb;/* '<S11>/Convert173' */
extern uint8 CSI_RiFr_AccOBJ_FusionedFC_Track_ID;/* '<S11>/Convert271' */

/* It is the track ID of FC that fusioned with FR ACC RiFr target */
extern float32 CSI_RiFr_AccOBJ_HeadingAngle;/* '<S10>/Convert167' */
extern float32 CSI_RiFr_AccOBJ_Height; /* '<S10>/Convert261' */
extern float32 CSI_RiFr_AccOBJ_Length; /* '<S10>/Convert262' */
extern float32 CSI_RiFr_AccOBJ_ObstacleProb;/* '<S11>/Convert174' */
extern uint8 CSI_RiFr_AccOBJ_Taillight_Info;/* '<S11>/Convert272' */
extern uint8 CSI_RiFr_AccOBJ_Track_Age;/* '<S11>/Convert273' */
extern uint8 CSI_RiFr_AccOBJ_Track_ID; /* '<S11>/Convert274' */
extern float32 CSI_RiFr_AccOBJ_Vx;     /* '<S11>/Convert175' */
extern float32 CSI_RiFr_AccOBJ_Vx_std; /* '<S11>/Convert275' */
extern float32 CSI_RiFr_AccOBJ_Vy;     /* '<S10>/Convert168' */
extern float32 CSI_RiFr_AccOBJ_Vy_std; /* '<S10>/Convert263' */
extern float32 CSI_RiFr_AccOBJ_Width;  /* '<S10>/Convert169' */
extern uint8 CSI_RiFr_AccOBJ_confi;    /* '<S11>/Convert269' */
extern uint8 CSI_RiFr_AccOBJ_fusion_Sts;/* '<S11>/Convert270' */
extern float32 CSI_Ri_AccOBJ_Ax;       /* '<S10>/Convert152' */
extern float32 CSI_Ri_AccOBJ_Ay;       /* '<S10>/Convert153' */
extern uint8 CSI_Ri_AccOBJ_Brakelight_Info;/* '<S10>/Convert154' */
extern uint8 CSI_Ri_AccOBJ_Class;      /* '<S10>/Convert155' */
extern float32 CSI_Ri_AccOBJ_Dx;       /* '<S10>/Convert156' */
extern float32 CSI_Ri_AccOBJ_Dx_Vnce;  /* '<S10>/Convert248' */
extern float32 CSI_Ri_AccOBJ_Dy;       /* '<S10>/Convert157' */
extern float32 CSI_Ri_AccOBJ_Dy_Vnce;  /* '<S10>/Convert249' */
extern float32 CSI_Ri_AccOBJ_ExistProb;/* '<S10>/Convert158' */
extern uint8 CSI_Ri_AccOBJ_FusionedFC_Track_ID;/* '<S10>/Convert251' */

/* It is the track ID of FC that fusioned with FR ACC Ri target */
extern float32 CSI_Ri_AccOBJ_HeadingAngle;/* '<S10>/Convert159' */
extern float32 CSI_Ri_AccOBJ_Height;   /* '<S10>/Convert252' */
extern float32 CSI_Ri_AccOBJ_Length;   /* '<S10>/Convert253' */
extern float32 CSI_Ri_AccOBJ_ObstacleProb;/* '<S10>/Convert160' */
extern uint8 CSI_Ri_AccOBJ_Taillight_Info;/* '<S10>/Convert254' */
extern uint8 CSI_Ri_AccOBJ_Track_Age;  /* '<S10>/Convert255' */
extern uint8 CSI_Ri_AccOBJ_Track_ID;   /* '<S10>/Convert256' */
extern float32 CSI_Ri_AccOBJ_Vx;       /* '<S10>/Convert161' */
extern float32 CSI_Ri_AccOBJ_Vx_std;   /* '<S10>/Convert257' */
extern float32 CSI_Ri_AccOBJ_Vy;       /* '<S10>/Convert162' */
extern float32 CSI_Ri_AccOBJ_Vy_std;   /* '<S10>/Convert258' */
extern float32 CSI_Ri_AccOBJ_Width;    /* '<S10>/Convert163' */
extern uint8 CSI_Ri_AccOBJ_confi;      /* '<S11>/Convert268' */
extern uint8 CSI_Ri_AccOBJ_fusion_Sts; /* '<S10>/Convert250' */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'CameraSigInput'
 * '<S1>'   : 'CameraSigInput/CameraSigInput_10ms'
 * '<S2>'   : 'CameraSigInput/CameraSigInput_Init'
 * '<S3>'   : 'CameraSigInput/CameraSigInput_10ms/CameraMessages'
 * '<S4>'   : 'CameraSigInput/CameraSigInput_10ms/LaneInfo'
 * '<S5>'   : 'CameraSigInput/CameraSigInput_10ms/ObjectInfo'
 * '<S6>'   : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_191'
 * '<S7>'   : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_192'
 * '<S8>'   : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_19D'
 * '<S9>'   : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_19E'
 * '<S10>'  : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_19F'
 * '<S11>'  : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_1A0'
 * '<S12>'  : 'CameraSigInput/CameraSigInput_10ms/CameraMessages/FC_1A5'
 */
#endif                                 /* RTW_HEADER_CameraSigInput_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
