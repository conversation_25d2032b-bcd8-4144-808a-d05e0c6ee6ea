#include "planner/planning_nodelet.h"

#include <deque>
#include <memory>
#include <numeric>
#include <string>
#include <tuple>
#include <utility>
#include <variant>
#include <vector>

#include <boost/range/combine.hpp>
#include <boost/system/detail/error_code.hpp>
#include <tbb/tbb.h>

#include "av_comm/mode_config.h"
#include "av_comm/topics.h"
#include "base/now.h"
#include "base/wall_clock_elapsed_timer.h"
#include "control/control_gflags.h"
#include "exception_handler/exception_handler_main/trajectory_result.h"
#include "hdmap/nodelet/hdmap_nodelet.h"
#include "latency/pipeline_id.h"
#include "math/curve2d.h"
#include "math/unit_conversion.h"
#include "planner/behavior/main_planner.h"
#include "planner/behavior/types/behavior_decision.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/decoupled_base_utility.h"
#include "planner/edge_filter.h"
#include "planner/latency_record_macro.h"
#include "planner/ops_warning/ops_warning.h"
#include "planner/planner_debug_wrapper.h"
#include "planner/planner_health/backup_trajectory_generator.h"
#include "planner/planning_gflags.h"
#include "planner/planning_nodelet_utility.h"
#include "planner/planning_proactive_drop_message_manager.h"
#include "planner/planning_result.h"
#include "planner/planning_service.h"
#include "planner/planning_topics.h"
#include "planner/route_replan/route_replan_manager.h"
#include "planner/utility/common/config_io.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/utility/state/planner_state_util.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner/world_model/snapshot/snapshot.h"
#include "planner/world_model/world_model.h"
#include "planner/world_model/world_model_utility.h"
#include "planner_protos/customer_monitor_visual.pb.h"
#include "planner_protos/message_metadata.pb.h"
#include "planner_protos/ops_warning.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/state/planner.pb.h"
#include "planner_protos/trajectory_generator_debug.pb.h"
#include "planner_protos/trajectory_guider_output.pb.h"
#include "pnc_map_service/pnc_map_service.h"
#include "prediction_protos/agent.pb.h"
#include "routing/engine/engine_flags.h"
#include "routing/utility/rt_event_util.h"
#include "routing_protos/planning_route_state.pb.h"
#include "routing_protos/route_command.pb.h"
#include "routing_protos/route_confirmation.pb.h"
#include "routing_protos/route_solution.pb.h"
#include "stats/stats.h"
#include "trace/trace.h"
#include "varch/vnode/system_call.h"
#include "varch/vnode/vnode.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"
#include "voystat/stats_planner.h"

namespace planner {
namespace planning_vnode {
namespace {  // const for path planning
using StateIndex = vehicle_model::CarModel::StateIndex;
// A name that is unique so that we will update constraint for edge filter on
// receiving next route status.
constexpr char kConstraintNameToForceUpdate[] = "kShouldUpdate";

// A key to monitor the updating frequency of world model updating.
constexpr char kWorldModelUpdateSuccess[] = "world_model_update_success";

// Threshold values for changes in the planning initial state's steering wheel
// angle, speed, and acceleration between consecutive frames during parallel
// path and speed planning. If any of these differences exceed the specified
// thresholds, a frame drop is triggered, and planning falls back to serialized
// path-speed execution.
constexpr double kFrameDropSteeringWheelAngleDeltaThresholdForParallelPlanning =
    2.0;
constexpr double kFrameDropSpeedDeltaThresholdForParallelPlanning = 0.5;
constexpr double kFrameDropAccelerationDeltaThresholdForParallelPlanning = 0.2;
// Threshold for consecutive parallel planning cycles before triggering fallback
// to serialized planning.
constexpr int64_t kFrameDropForConsecutiveParallelPlanningCount = 20;
}  // namespace

namespace {  // const for speed planning
// Duration threshold to determine planner fail to call fallback system.
constexpr double kNoTrajTimerThresholdInMs = 400;

// The velocity threshold that we can safely use the backup deceleration
// trajectory to stop the car, otherwise we should pop out fault code and let
// the fallback team takeover.
constexpr double kBackupTrajectorySafeVelocityThresholdInMpS = 5.0;
}  // namespace

PlanningPathVNode::PlanningPathVNode()
    : planner_config_center_(PlannerConfigCenter::GetInstance()),
      pnc_map_service_(PlanningService::GetInstance()->GetPnCMapService()),
      edge_filter_manager_(GetEdgeFilterManager()),
      global_object_manager_(std::make_shared<GlobalObjectManager>(
          planner_config_center_.behavior_reasoner_config()
              .decoupled_forward_maneuver_config()
              .required_lateral_gap_config(),
          planner_config_center_.world_model_config().planner_object_config())),
      world_model_(planner_config_center_.world_model_config(),
                   pnc_map_service_.get(), edge_filter_manager_,
                   global_object_manager_),
      main_path_planner_(planner_config_center_.behavior_reasoner_config(),
                         world_model_.mutable_waypoint_lanes_cache_ptr()),
      update_route_status_debug_pool_(
          std::make_unique<av_comm::ThreadPool>(1)) {
  // TODO(cojimawang): Construct EdgeFilterManager explicitly and Notify if
  // initialization fails
  WritePlanningConfigToRosParam(planner_config_center_.planner_config());
  // Enforce gflag relation:
  // FLAGS_planning_seed_always_read_from_current can be enabled only if
  // FLAGS_planning_copy_previous_seed is true
  if (FLAGS_planning_seed_always_read_from_current) {
    CHECK(FLAGS_planning_copy_previous_seed)
        << " FLAGS_planning_seed_always_read_from_current require "
           "FLAGS_planning_copy_previous_seed to be true";
  }
}

void PlanningPathVNode::LoadPlannerState(const pb::PlannerState& proto) {
  if (current_route_timestamp_ != proto.current_route_timestamp()) {
    current_route_timestamp_ = proto.current_route_timestamp();
    if (!FLAGS_planning_load_routing_constraint_from_topic) {
      // If no need to update edge filter, for example, using the default edge
      // filter, it will skip the update process of edge filter in
      // |UpdateInUseEdgeFilter|.
      UpdateInUseEdgeFilter(
          proto.route_command().route_query().constraint_name());
    } else {
      // Set constraint name of edge filter as force update, and constraint
      // definitions will be updated in next cycle.
      edge_filter_manager_->GetInUsedEdgeFilter()->set_constraint_name(
          kConstraintNameToForceUpdate);
    }
  }

  need_publish_route_status_ = proto.need_publish_route_status();
  last_route_acceptance_state_ = proto.last_route_acceptance_state();

  if (proto.has_world_model()) {
    world_model_.LoadState(proto.world_model());
  }

  if (proto.has_behavior_reasoner()) {
    main_path_planner_.LoadState(proto.behavior_reasoner());
  }
}

void PlanningPathVNode::DumpPlannerState(int64_t snapshot_timestamp,
                                         pb::PlannerState& proto) {
  proto.set_timestamp(snapshot_timestamp);
  if (snapshot_timestamp == 0) {
    LOG(WARNING) << "No snapshot available in world model.";
  }
  proto.set_current_route_timestamp(current_route_timestamp_);
  proto.set_need_publish_route_status(need_publish_route_status_);
  proto.set_last_route_acceptance_state(last_route_acceptance_state_);

  world_model_.DumpState(*proto.mutable_world_model());
  main_path_planner_.DumpState(*proto.mutable_behavior_reasoner());
}

void PlanningPathVNode::PublishRouteState(
    const routing::pb::RouteStatus& route_status, int64_t current_timestamp,
    bool is_last_optimal_route_accepted_by_planner, bool is_ego_in_junction,
    bool* is_proposed_route_solution_rejected) {
  const routing::pb::RouteSolution& proposed_route_solution =
      route_status.proposed_route_solution();
  const routing::pb::RouteSolution& current_route_solution =
      route_status.current_route_solution();

  // For already rejected proposed route solution.
  if (proposed_route_solution.route_timestamp() == rejected_route_timestamp_) {
    *is_proposed_route_solution_rejected = true;
    UpdateRoutingConstraint(route_status, /*is_new_route_solution=*/false,
                            *is_proposed_route_solution_rejected);
    return;
  }

  // For not rejected proposed route solution.
  const bool is_new_route_solution =
      current_route_timestamp_ != proposed_route_solution.route_timestamp();
  if (is_new_route_solution) {
    need_publish_route_status_ = true;
  }
  UpdateRoutingConstraint(route_status, is_new_route_solution,
                          *is_proposed_route_solution_rejected);
  current_route_timestamp_ = proposed_route_solution.route_timestamp();
  if (proposed_route_solution.mission_id() ==
      current_route_solution.mission_id()) {
    // For replan route query or rollback to current route solution after
    // proposed new route is rejected.
    // Publish route acceptance before update world model for 2 cases:
    // 1. When we do not enable to allow global route lanes to be rejected.
    // 2. When we enable to allow global route lanes to be rejected and should
    //    accept directly, eg: new route.
    if (is_new_route_solution &&
        (!FLAGS_planning_enable_allow_global_route_lanes_rejected ||
         ShouldAcceptReplanRouteDirectly(route_status))) {
      PublishRouteAcceptance(proposed_route_solution);
      accepted_route_timestamp_ = proposed_route_solution.route_timestamp();
      need_publish_route_status_ = false;
    }
    return;
  }

  // For non-replan query, we should always accept all route lanes, so just set
  // need_publish_route_status_ as false.
  need_publish_route_status_ = false;

  // For new route not proposed in ontrip state, accept new route directly.
  if (accepted_route_timestamp_ != proposed_route_solution.route_timestamp() &&
      route_status.mission_state() !=
          routing::pb::MissionState::kOntripProposing) {
    PublishRouteAcceptance(proposed_route_solution);
    accepted_route_timestamp_ = proposed_route_solution.route_timestamp();
    return;
  }

  // For new route proposed in ontrip state, wait for planning result.
  if (is_new_route_solution) {
    first_receive_timestamp_ = current_timestamp;
    return;
  }

  // For new route and response to proposed route solution is not decided.
  if (accepted_route_timestamp_ != proposed_route_solution.route_timestamp() &&
      is_last_optimal_route_accepted_by_planner) {
    // Accept new route if planner selects new route. Do not accept alreay
    // accpeted route.
    PublishRouteAcceptance(proposed_route_solution);
    accepted_route_timestamp_ = proposed_route_solution.route_timestamp();
  } else if (ShouldRejectProposedRoute(
                 is_last_optimal_route_accepted_by_planner, current_timestamp,
                 first_receive_timestamp_)) {
    // Add rt event for planning reject dangerous new route.
    routing::utility::AddRtEventForPlanningRejectNewRoute(
        proposed_route_solution, current_timestamp, is_ego_in_junction);

    if (FLAGS_planning_enable_reject_dangerous_new_route) {
      PublishRouteRejection(proposed_route_solution,
                            /*only_reject_route_lanes=*/false);
      *is_proposed_route_solution_rejected = true;
      rejected_route_timestamp_ = proposed_route_solution.route_timestamp();
      UpdateRoutingConstraint(route_status, /*is_new_route_solution=*/true,
                              *is_proposed_route_solution_rejected);
    } else {
      // If not planning_enable_reject_dangerous_new_route, only add rt-event
      // for planning reject, then accept the new route.
      PublishRouteAcceptance(proposed_route_solution);
      accepted_route_timestamp_ = proposed_route_solution.route_timestamp();
    }
  }
}

void PlanningPathVNode::UpdateRoutingConstraint(
    const routing::pb::RouteStatus& route_status, bool is_new_route_solution,
    bool is_proposed_route_solution_rejected) {
  TRACE_EVENT_SCOPE(planner, PlanningNodelet_UpdateConstraint);
  std::string debug_info;
  if (is_new_route_solution) {
    // Update routing edge filter with constraint name in proposed route
    // query.
    if (!FLAGS_planning_load_routing_constraint_from_topic) {
      const auto& route_constraint_name =
          is_proposed_route_solution_rejected
              ? route_status.current_route_query().constraint_name()
              : route_status.proposed_route_query().constraint_name();
      UpdateInUseEdgeFilter(route_constraint_name);
    } else {
      // Set constraint name of edge filter as force update, and constraint
      // definitions will be updated in next cycle. Because routing constraint
      // is updated into route status after the proposed route is accepted by
      // planner when calling RouteStatusManager::UpdateCurrentRouteStatus().
      // So theoretically speaking, we should not update constraint at this
      // step. Otherwise, we may use the constraint which is about to be
      // obsolete at the next cycle.
      edge_filter_manager_->GetInUsedEdgeFilter()->set_constraint_name(
          kConstraintNameToForceUpdate);
    }
  } else if (FLAGS_planning_load_routing_constraint_from_topic) {
    // Update the constraint from the topics. This is
    // only for use cases when loading constraint from config is unavailable.
    routing::route_utils::UpdateConstraintInEdgeFilter(
        route_status.current_route_query().constraint_name(),
        *pnc_map_service_->hdmap(), route_status.constraint_info(),
        edge_filter_manager_->GetInUsedEdgeFilter().get());
  }

  edge_filter_manager_->GetInUsedEdgeFilter()
      ->UpdateEffectiveMinutesFromMidnight(*pnc_map_service_->hdmap(),
                                           /*notify_observers=*/true);
}

void PlanningPathVNode::PublishRouteStateAndUpdateRouteLanesForReplan(
    const routing::pb::RouteStatus& route_status) {
  if (FLAGS_planning_enable_allow_global_route_lanes_rejected &&
      need_publish_route_status_) {
    const routing::pb::RouteSolution& route_solution =
        route_status.proposed_route_solution();
    if (ShouldRejectNewRouteLanesForReplan(
            route_status, world_model_.regional_map().regional_path)) {
      PublishRouteRejection(route_solution, /*only_reject_route_lanes=*/true);
      rejected_route_timestamp_ = route_solution.route_timestamp();
    } else {
      PublishRouteAcceptance(route_solution);
      accepted_route_timestamp_ = route_solution.route_timestamp();
      world_model_.UpdateGlobalRouteLanesInfo(route_solution.route_lanes());
    }
    need_publish_route_status_ = false;
  }
}

PathResult PlanningPathVNode::PathPlan(
    std::unique_ptr<const Snapshot> snapshot_ptr,
    std::unique_ptr<std::vector<pb::AssistResponse>>
        remote_assist_responses_ptr,
    bool is_proposed_route_solution_rejected, pb::PlanningDebug* planning_debug,
    pb::BehaviorReasonersDebug* behavior_reasoner_debug) {
  TRACE_EVENT_SCOPE(
      planner, PlanningNodelet_PathPlan,
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());

  DCHECK(snapshot_ptr != nullptr);
  latency_timer_.RestartTimer();
  PlanningResult planning_result;
  ManeuverCustomerMonitorVisual maneuver_visual;

  // If consecutive parallel planning exceeds threshold, fallback to serialized
  // planning to reset init state.
  // If the previous frame of data has already been proactively discarded,
  // no frame loss judgment will be made.
  if (planner::FLAGS_planning_path_speed_are_parallel) {
    const auto frame_view = GetPathFrameView();
    if (!IsSpecialFrameId(frame_view.last_frame) &&
        !IsSpecialFrameId(frame_view.last_upstream_frame)) {
      if (frame_view.last_frame != frame_view.last_upstream_frame) {
        rt_event::PostRtEvent<rt_event::planner::InParallelPathSpeedPlanning>(
            fmt::format("{}:{}:{}", frame_view.current_frame,
                        frame_view.last_frame, frame_view.last_upstream_frame));
        consecutive_parallel_planning_count_++;
        if (ProactiveDropMessageManager::GetInstance()
                .CanDropFrameWithConsecutiveGuard(
                    latency::PipelineID<
                        latency::PipelineType::PlannerStage1LidarHWTime>()
                        .load(),
                    consecutive_parallel_planning_count_ >=
                        kFrameDropForConsecutiveParallelPlanningCount,
                    true)) {
          rt_event::PostRtEvent<
              rt_event::planner::FrameDropForConsecutiveParallelPlanning>(
              fmt::format("{}:{}:{},{}", frame_view.current_frame,
                          frame_view.last_frame, frame_view.last_upstream_frame,
                          latency::PipelineID<
                              latency::PipelineType::PlannerStage1LidarHWTime>()
                              .load()));
          LOG(INFO) << "consecutive_parallel_planning_count = "
                    << consecutive_parallel_planning_count_ << " hits "
                    << kFrameDropForConsecutiveParallelPlanningCount << ", "
                    << fmt::format("{}:{}:{}", frame_view.current_frame,
                                   frame_view.last_frame,
                                   frame_view.last_upstream_frame);
          return PathResult(last_route_acceptance_state_, true);
        }
      } else {
        consecutive_parallel_planning_count_ = 0;
      }
    }
  }

  const pb::WorldModelSeed& world_model_seed = world_model_.last_seed();
  const auto prev_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          PathLastFinishedFrame());

  // Update the snapshot.
  const bool world_model_update_success = world_model_.Update(
      world_model_seed, std::move(snapshot_ptr),
      std::move(remote_assist_responses_ptr), !need_publish_route_status_,
      is_proposed_route_solution_rejected, planning_debug);
  // Monitor the updating frequency of world model. This is to calculate the
  // proportion of current lane invalidtion events.
  routing::utility::AddRtEventAsJson<rt_event::planner::WorldModelUpdate>(
      kWorldModelUpdateSuccess, world_model_update_success ? 1 : 0);
  if (!world_model_update_success) {
    LATENCY_COST_RECORD_STAGE1(LAT_WorldModel_Update, latency_timer_);
    STATS_COUNTER2(planner, planning_nodelet, update_snapshot_failure, 1);
    const auto& route_status_ptr =
        world_model_.latest_snapshot_ptr() != nullptr
            ? world_model_.latest_snapshot_ptr()->route_status_ptr
            : nullptr;
    const auto* global_route_solution_ptr =
        world_model_.global_route_solution();
    pb::PlanningRouteStatusDebug* planning_route_status_debug =
        planning_debug != nullptr ? planning_debug->mutable_world_model_debug()
                                        ->mutable_global_route_debug()
                                        ->mutable_planning_route_status_debug()
                                  : nullptr;
    if (route_status_ptr != nullptr && global_route_solution_ptr != nullptr &&
        planning_route_status_debug != nullptr) {
      UpdatePlanningRouteStatusDebug(
          *route_status_ptr, global_route_solution_ptr->route_solution(),
          global_route_solution_ptr->loopback_times(),
          global_route_solution_ptr->is_global_route_changed(),
          planning_route_status_debug);
    }
    auto speed_world_model = world_model_.ExtractToSpeedWorldModel();
    DCHECK(speed_world_model);
    return PathResult(std::move(speed_world_model),
                      last_route_acceptance_state_);
  }
  LATENCY_COST_RECORD_STAGE1(LAT_WorldModel_Update, latency_timer_);

  // Check if the divergence condition for the initial state's steering wheel
  // angle, speed, and acceleration between consecutive frames during parallel
  // path and speed planning is met. If any difference exceeds the specified
  // threshold, a frame drop is triggered and execution reverts to serialized
  // path-speed planning.

  // If the previous frame of data has already been proactively discarded,
  // no frame loss judgment will be made.
  if (planner::FLAGS_planning_path_speed_are_parallel) {
    const auto frame_view = GetPathFrameView();
    auto* path_frame_view_debug =
        planning_debug != nullptr
            ? planning_debug->mutable_path_frame_view_debug()
            : nullptr;
    if (!IsSpecialFrameId(frame_view.last_frame) &&
        !IsSpecialFrameId(frame_view.last_upstream_frame)) {
      if (frame_view.last_frame != frame_view.last_upstream_frame) {
        const auto divergence_infos = GetInitStateDivergenceInfos(
            world_model_.robot_state().plan_init_state(),
            world_model_.robot_state().juke_integrated_model_with_shape());
        if (ProactiveDropMessageManager::GetInstance()
                .CanDropFrameWithConsecutiveGuard(
                    latency::PipelineID<
                        latency::PipelineType::PlannerStage1LidarHWTime>()
                        .load(),
                    !divergence_infos.empty(), true)) {
          path_frame_view_debug->set_abort_path_frame_until_last_speed_finish(
              true);
          std::string divergence_str;
          for (const auto& info : divergence_infos) {
            auto* new_info =
                path_frame_view_debug->add_init_state_divergence_info();
            new_info->CopyFrom(info);
            if (!divergence_str.empty()) {
              divergence_str += ", ";
            }
            divergence_str += fmt::format(
                "{} ({}|{}|{}|{}|{})",
                InitStateDivergenceInfo_DivergenceReason_Name(info.reason()),
                info.first_delta(), info.second_delta(), info.first_value(),
                info.second_value(), info.third_value());
          }
          rt_event::PostRtEvent<
              rt_event::planner::InitStateDivergenceInParallelPlanning>(
              fmt::format("{}:{}:{},{}", frame_view.current_frame,
                          frame_view.last_frame, frame_view.last_upstream_frame,
                          divergence_str));
          LOG(INFO) << "Divergence detected: " << divergence_str;
          return PathResult(last_route_acceptance_state_, true);
        }
      } else {
        // Clearing planning_three_cycle_history_ ensures that init_state
        // divergence–induced frame drops occur only during consecutive
        // path-speed parallel executions.
        planning_three_cycle_history_.clear();
      }
    }
  }

  tbb::task_group route_model_task_group;

  std::unique_ptr<planner::RouteModelSpeedData> route_model_speed_data;
  route_model_task_group.run([this, &route_model_speed_data]() {
    TRACE_EVENT_SCOPE(planner, SpeedWorldModel_CreateRouteModelSpeedData);
    route_model_speed_data =
        world_model_.mutable_route_model().CreateRouteModelSpeedData();
  });

  const auto& route_status_ptr =
      world_model_.latest_snapshot_ptr() != nullptr
          ? world_model_.latest_snapshot_ptr()->route_status_ptr
          : nullptr;
  const auto* global_route_solution_ptr = world_model_.global_route_solution();
  pb::PlanningRouteStatusDebug* planning_route_status_debug =
      planning_debug != nullptr ? planning_debug->mutable_world_model_debug()
                                      ->mutable_global_route_debug()
                                      ->mutable_planning_route_status_debug()
                                : nullptr;
  std::future<void> update_route_status_debug_task;
  if (route_status_ptr != nullptr && global_route_solution_ptr != nullptr &&
      planning_route_status_debug != nullptr) {
    auto update_debug_task = [&route_status_ptr, global_route_solution_ptr,
                              planning_route_status_debug]() {
      UpdatePlanningRouteStatusDebug(
          *route_status_ptr, global_route_solution_ptr->route_solution(),
          global_route_solution_ptr->loopback_times(),
          global_route_solution_ptr->is_global_route_changed(),
          planning_route_status_debug);
    };

    update_route_status_debug_task =
        update_route_status_debug_pool_->PushTask(update_debug_task);
  }

  if (prev_maneuver_seed->has_selected_trajectory()) {
    DCHECK(!prev_maneuver_seed->selected_trajectory().poses().empty());
  }

  // Generate the lane sequence candidates.
  lane_selection::LaneSequenceCandidates lane_sequence_candidates =
      main_path_planner_.GenerateLaneSequenceCandidates(
          world_model_, *prev_maneuver_seed, behavior_reasoner_debug);
  LATENCY_COST_RECORD_STAGE1(LAT_PlanningNode_LaneSequenceGeneration,
                             latency_timer_);
  // Get the turn signal state.
  TurnSignalState turn_signal_state = turn_signal_indicator_.GetTurnSignalState(
      world_model_.snapshot_timestamp());
  // Create DecoupledManeuverInput
  DecoupledManeuverInput input = {world_model_, lane_sequence_candidates,
                                  turn_signal_state};
  ManeuverReturnType decoupled_maneuver_return;

  global_object_manager_->UpdateBaseBehaviorRelevantObjectsInfo();

  world_model_.SetAssociatedRouteForObjectPredictedTrajectories(
      *prev_maneuver_seed, lane_sequence_candidates.lane_sequence_results(),
      planning_debug);

  // Run path pipeline.
  DecoupledForwardPathPlannerOutput path_planner_output =
      main_path_planner_.PathPlan(
          input, *prev_maneuver_seed, *global_object_manager_,
          &decoupled_maneuver_return, behavior_reasoner_debug);
  if (update_route_status_debug_task.valid()) {
    update_route_status_debug_task.wait();
  }

  LATENCY_COST_RECORD_STAGE1(LAT_MainPlanner_PathPlan, latency_timer_);

  route_model_task_group.wait();
  auto speed_world_model =
      world_model_.ExtractToSpeedWorldModel(route_model_speed_data);
  DCHECK(speed_world_model);
  main_path_planner_.ReleaseSharedResource();
  return PathResult(
      std::move(path_planner_output), std::move(speed_world_model),
      std::move(lane_sequence_candidates), std::move(turn_signal_state),
      std::move(decoupled_maneuver_return), last_route_acceptance_state_);
}

//////////////////////////////////////////////////
// SpeedPlan
//////////////////////////////////////////////////
PlanningResult PlanningSpeedVNode::SpeedPlan(
    int64_t path_planning_start_time, PathResult& path_result,
    const std::shared_ptr<const pb::AssistStuckModelOutput>&
        assist_stuck_model_output_sptr,
    const assist::RemoteWarningInputs& remote_warning_inputs,
    assist::AssistModelInput* assist_stuck_model_input,
    pb::PlanningDebug* planning_debug,
    pb::BehaviorReasonersDebug* behavior_reasoner_debug,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  TRACE_EVENT_SCOPE(
      planner, PlanningNodelet_SpeedPlan,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());

  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->UpdateDegradationHandler(
              path_result.speed_world_model_data->localization_fault(),
              path_result.speed_world_model_data->replan_request(),
              path_result.speed_world_model_data->latest_snapshot_ptr()
                  ->remote_speed_limit_ptr,
              path_result.speed_world_model_data->latest_snapshot_ptr()
                  ->vehicle_exception_status_ptr,
              path_result.speed_world_model_data->pose().timestamp());
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_immutable_poses(
              path_result.speed_world_model_data->robot_state()
                  .plan_init_state()
                  .immutable_poses);
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_construction_zones(
              path_result.speed_world_model_data->construction_zones());
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_autonomy_control_mode(av_comm::IsAutonomyControlMode(
              Seed::Access<token::WorldModelSeed>::GetMsg(
                  SpeedLastFinishedFrame())
                  ->last_vehicle_mode()));
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_mrc_request(
              path_result.speed_world_model_data->mrc_request_ptr());

  const auto previous_decoupled_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          SpeedLastFinishedFrame());

  const auto& latest_snapshot_ptr =
      path_result.speed_world_model_data->latest_snapshot_ptr();
  if (latest_snapshot_ptr && latest_snapshot_ptr->route_status_ptr) {
    const routing::pb::RouteSolution& proposed_route_solution =
        latest_snapshot_ptr->route_status_ptr->proposed_route_solution();
    auto route_solution_timestamp = proposed_route_solution.route_timestamp();
    if (current_route_timestamp_ != route_solution_timestamp) {
      current_route_timestamp_ = route_solution_timestamp;
    }
  }

  if (path_result.speed_world_model_data->global_route_solution()
          ->is_new_trip()) {
    has_published_route_completion_ = false;
    has_published_immediate_pullover_request_ = false;
  }

  PlanningResult planning_result;
  ManeuverCustomerMonitorVisual maneuver_visual;
  // From here, we avoid using world_model_ class, in order to mimic nodelet
  // separation configuration.
  const BehaviorDecision behavior_decision = main_speed_planner_.SpeedPlan(
      *path_result.speed_world_model_data,
      path_result.lane_sequence_candidates.value(),
      path_result.turn_signal_state.value(), *previous_decoupled_maneuver_seed,
      path_result.path_planner_output.value(), assist_stuck_model_output_sptr,
      remote_warning_inputs, &path_result.decoupled_maneuver_return.value(),
      &maneuver_visual, assist_stuck_model_input, behavior_reasoner_debug);

  LATENCY_COST_RECORD_STAGE2(LAT_MainPlanner_SpeedPlan, latency_timer_);
  // Get trajectory result from decoupled maneuver.
  // Note: If this selection logic is changed, also update the
  // selection logic in UpdateCustomerMonitorVisual() where the
  // decoupled path and decoupled fences are added to the proto.
  if (!behavior_decision.decoupled_lf_trajectory_result.has_value()) {
    // Note: If no output from decoupled arch. Try generate a safe full-stop
    // trajectory when ego speed is low or send a fault code to directly trigger
    // the fallback system when current speed is not safe.
    rt_event::PostRtEvent<rt_event::planner::DecoupledForwardNoTrajectory>();
    auto* exception_debug = planning_debug->mutable_exception_reason_debug();
    exception_debug->set_exception_reason(
        planner::pb::ExceptionReasonDebug::DECOUPLED_TRAJECTORY_NO_VALUE);
    exception_debug->set_exception_message(
        "No valid trajectory could be generated from the decoupled manuver.");
    if (FLAGS_decoupled_planning_enable_no_path_dcheck) {
      DCHECK(false) << "No trajectory result from decoupled maneuver.";
    }
    HandlePlanningNodeletException(
        path_result.speed_world_model_data->robot_state(),
        path_result.speed_world_model_data->prediction_timestamp(),
        path_result.speed_world_model_data->mrc_request_ptr(),
        path_result.speed_world_model_data->should_planner_respond_mrc(),
        &planning_result, customer_monitor_visual);
    return planning_result;
  }

  no_traj_timer_.RestartTimer();

  const TrajectoryGenerationResult trajectory_generation_result =
      behavior_decision.decoupled_lf_trajectory_result.value();

  // Since world_model_seed is updated after decoupled_maneuver_seed, we need to
  // use the current frame's (iteration n) seed to update world_model_seed.
  const auto current_decoupled_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedCurrentFrame());
  auto mutable_worldmodel_seed =
      Seed::Access<token::WorldModelSeed>::MutableMsg(SpeedCurrentFrame());
  // Update world model seed with planning result.
  UpdateSeedWithDecoupledManeuver(
      *current_decoupled_maneuver_seed,
      path_result.speed_world_model_data->robot_state().vehicle_mode(),
      behavior_decision, *trajectory_generation_result.trajectory,
      path_result.speed_world_model_data->mrc_request_ptr(),
      path_result.speed_world_model_data->snapshot_timestamp(),
      mutable_worldmodel_seed.get());

  ///////////////////////////////
  // Assemble planning result. //
  ///////////////////////////////

  // Generate ops warning.
  planning_result.ops_warning = ops_warning_generator_.GenerateWarning(
      *path_result.speed_world_model_data, behavior_decision,
      trajectory_generation_result);

  // Assign the selected trajectory to planing result.
  planning_result.trajectory = trajectory_generation_result.trajectory;

  // Update the assist requests.
  planning_result.assist_requests = behavior_decision.requests;

  // Update the remote warning signal.
  if (behavior_decision.remote_warning_detection &&
      behavior_decision.remote_warning_detection->trigger_request()) {
    planning_result.remote_warning_signal =
        behavior_decision.remote_warning_detection->remote_warning_signal();
  }

  // Update the destination flag.
  planning_result.has_reached_destination =
      behavior_decision.has_reached_destination;

  // Update the immediate pull over trigger flag.
  planning_result.should_trigger_immediate_pull_over =
      behavior_decision.should_trigger_immediate_pull_over;

  // Update planning lane sequence data.
  const pb::AbnormalSequenceSignal abnormal_sequence_signal =
      GetAbnormalSequenceSignal(
          path_result.speed_world_model_data->global_route_solution(),
          behavior_decision.should_add_route_replan_fail_fault_code);
  planning_result.planning_lane_sequence = GeneratePlanningLaneSequence(
      *path_result.speed_world_model_data, behavior_decision,
      abnormal_sequence_signal,
      planning_result.trajectory->extra_planner_signals().behavior_signal());

  planning_result
      .downgrade_planning_lane_sequence = GenerateDowngradePlanningLaneSequence(
      *path_result.speed_world_model_data, behavior_decision,
      abnormal_sequence_signal,
      planning_result.trajectory->extra_planner_signals().behavior_signal());

  // Add replan query if there is a valid type about global route replan.
  const routing::pb::RouteQuery& replan_route_query =
      path_result.speed_world_model_data->replan_route_query();
  routing::pb::QueryFollowedPathType replan_followed_path_type =
      routing::pb::NONE_FOLLOWED_PATH;
  // When should_trigger_immediate_pull_over is true and planning has not
  // published immediate pullover request, planning needs to send a request to
  // routing that trigger immediate pull over.
  need_publish_immediate_pullover_request_ =
      planning_result.should_trigger_immediate_pull_over &&
      !has_published_immediate_pullover_request_;
  // When should_trigger_immediate_pull_over is false and the immediate pull
  // over has been triggered by planning in last cycle , planning needs to send
  // a request to routing that cancel immediate pull over.
  need_publish_cancel_immediate_pullover_request_ =
      !planning_result.should_trigger_immediate_pull_over &&
      path_result.speed_world_model_data->immediate_pull_over_state_manager()
          .IsTriggeredByPlanning();

  if (replan_route_query.followed_path_type() !=
      routing::pb::QueryFollowedPathType::NONE_REPLAN) {
    planning_result.replan_query = replan_route_query;
    PostRTEventForReplanRouteQueryType(replan_route_query);
    replan_followed_path_type = routing::pb::QueryFollowedPathType::NONE_REPLAN;
  } else {
    const bool last_replanned_route_lanes_rejected =
        (path_result.last_route_acceptance_state ==
         routing::pb::RouteState::kPlanningRejectedRouteLanes) ||
        (path_result.last_route_acceptance_state ==
         routing::pb::RouteState::kPlanningRejected);
    replan_followed_path_type =
        route_replan_manager_.GetReplanQueryFollowedPathType(
            *path_result.speed_world_model_data, behavior_decision,
            last_replanned_route_lanes_rejected,
            need_publish_immediate_pullover_request_,
            need_publish_cancel_immediate_pullover_request_,
            planning_result.trajectory->extra_planner_signals()
                .behavior_signal(),
            planning_debug == nullptr
                ? nullptr
                : planning_debug->mutable_world_model_debug()
                      ->mutable_replan_debug()
                      ->mutable_replan_manager_debug());
  }
  // Note: Here is set for next cycle's query.
  UpdateReplanFollowedPathType(
      replan_followed_path_type,
      Seed::Access<token::RouteReplanManagerSeed>::MutableMsg(
          SpeedCurrentFrame())
          .get());

  // Update waypoint assist availability.
  UpdateWaypointAvailability(
      *current_decoupled_maneuver_seed,
      behavior_decision.decoupled_yielding_fence_list,
      path_result.speed_world_model_data->robot_state(),
      path_result.speed_world_model_data->planner_object_map(),
      *DCHECK_NOTNULL(
          path_result.speed_world_model_data->joint_pnc_map_service()),
      path_result.speed_world_model_data->last_selected_lane_sequence(),
      planning_debug);

  // Indicates whether light assist detour is available.
  path_result.speed_world_model_data->UpdateLightAssistAvailability(
      behavior_decision, path_result.speed_world_model_data->robot_state(),
      path_result.speed_world_model_data->last_selected_lane_sequence(),
      path_result.speed_world_model_data->current_lanes_speed_data(),
      path_result.speed_world_model_data->assist_directive_generator(),
      *Seed::Access<token::LightAssistSeed>::GetMsg(SpeedCurrentFrame()),
      planning_debug);

  ///////////////////////////////////////
  // Assemble planning monitor visual. //
  ///////////////////////////////////////
  customer_monitor_visual_msg_generator_.UpdateCustomerMonitorVisual(
      *path_result.speed_world_model_data, behavior_decision,
      trajectory_generation_result, maneuver_visual, planning_debug,
      customer_monitor_visual);

  ///////////////////////////////////////////
  // Assemble (additional) planning debug. //
  ///////////////////////////////////////////
  int64_t now = base::NowNs();
  int64_t total_planning_execution_time_ms =
      (now - path_planning_start_time) / 1'000'000;
  if (planning_debug != nullptr) {
    UpdateDebugData(
        *current_decoupled_maneuver_seed, *path_result.speed_world_model_data,
        trajectory_generation_result, cycle_timer_.GetElapsedTimeInMs(),
        total_planning_execution_time_ms,
        customer_monitor_visual->waypoint_availability(), planning_debug);
  }

  UpdatePlannerExecutionTime(total_planning_execution_time_ms,
                             mutable_worldmodel_seed.get());

  cycle_timer_.RestartTimer();

  // Destruction of path_planner_output in async.
  async_destruction_pool_->PushTask(
      [path_planner_output_background_ptr =
           std::make_unique<DecoupledForwardPathPlannerOutput>(
               std::move(path_result.path_planner_output.value()))]() mutable {
        TRACE_EVENT_SCOPE(
            planner,
            PlanningNodelet_DecoupledForwardPathPlannerOutputDestruction,
            latency::PipelineID<
                latency::PipelineType::PlannerStage2LidarHWTime>());
        path_planner_output_background_ptr.reset();
      });
  LATENCY_COST_RECORD_STAGE2(LAT_Gap_Between_Speed_Publish, latency_timer_);
  return planning_result;
}

void PlanningPathVNode::Callback(
    std::list<std::shared_ptr<const SnapshotMessage>>& snapshot_list,
    std::list<std::shared_ptr<const pb::PlanningSeed>>& planning_seed_list,
    std::list<std::shared_ptr<const pb::SpeedCompletionSignal>>&
        planning_speed_completion_list,
    std::list<std::shared_ptr<const SnapshotRAMessage>>& snapshot_ra_list,
    std::list<std::shared_ptr<const pb::PlannerState>>& planner_state_list) {
// The tbb of the planner will affect the simulation determinism,
// so we have decided to turn off the tbb of the planner in the simulation.
// To minimize the impact on onboard, we have added ONBOARD_ONLY macro
#ifndef ONBOARD_ONLY
  int max_concurrency = -1;
  if (av_comm::InSimulation() && FLAGS_planning_disable_tbb_parallelism) {
    max_concurrency = 1;
  }
  tbb::task_arena arena(max_concurrency);
  arena.execute([&] {
#endif
    PLANNER_MARK_THREAD_OWNER_AS_PATH();
    uint64_t hw_time = 0;
    TRACE_EVENT_SCOPE(planner, PlanningNodelet_Tick,
                      reinterpret_cast<uint64&>(hw_time));
    latency_timer_.RestartTimer();
    base::WallClockElapsedTimer exec_timer;
    InputMsgTsMap input_publish_timestamps_in_ns;
    int64_t path_planning_start_time = base::NowNs();
    callback_start_time_ns_ = GetCurrentFrameSnapshot().start_timestamp_ns();
    int64_t message_metadata_timestamp = callback_start_time_ns_ / 1'000'000;
    // Read world snapshot data.
    CHECK(!snapshot_list.empty());
    std::unique_ptr<const Snapshot> snapshot(std::move(
        const_cast<std::unique_ptr<Snapshot>&>(**snapshot_list.back())));

    std::unique_ptr<std::vector<pb::AssistResponse>>
        remote_assist_responses_ptr = nullptr;
    if (!snapshot_ra_list.empty()) {
      LOG(INFO) << "Planning_nodelete: snapshot_ra_list size is: "
                << snapshot_ra_list.size();
      remote_assist_responses_ptr =
          std::make_unique<std::vector<pb::AssistResponse>>(
              std::vector<pb::AssistResponse>{});
      for (const auto& snapshot_ra : snapshot_ra_list) {
        if (snapshot_ra && *snapshot_ra && **snapshot_ra) {
          remote_assist_responses_ptr->insert(
              remote_assist_responses_ptr->end(),
              std::move_iterator<std::vector<pb::AssistResponse>::iterator>(
                  (**snapshot_ra)->begin()),
              std::move_iterator<std::vector<pb::AssistResponse>::iterator>(
                  (**snapshot_ra)->end()));
        }
      }
      LOG(INFO) << "Planning_nodelete: remote_assist_responses_ptr size is: "
                << remote_assist_responses_ptr->size();
    }

    if (!planning_seed_list.empty()) {
      const std::vector<int64_t>& pub_times = GetConsumedMessagePubTimeInfo(
          GetPlannerTopicName(PlannerSubTopic::bag_planning_seed));
      input_publish_timestamps_in_ns[PlannerSubTopic::bag_planning_seed] =
          pub_times.empty() ? std::nullopt
                            : std::make_optional(pub_times.back());

      StashBagSeed(planning_seed_list);
    }

    if (!planning_speed_completion_list.empty()) {
      auto speed_pub_times = GetConsumedMessagePubTimeInfo(GetPlannerTopicName(
          PlannerSubTopic::planning_intra_speed_completion_signal));
      CHECK(planning_speed_completion_list.size() == speed_pub_times.size());
      for (const auto& [completion_msg, last_speed_completion_time] :
           boost::combine(planning_speed_completion_list, speed_pub_times)) {
        DCHECK(completion_msg);
        FrameId last_completed_speed_frame_id = completion_msg->frame_id();
        int64_t last_frame_total_latency_ms = 0;
        if (last_completed_speed_frame_id != kInvalidFrameId) {
          // this is a real frame finish signal. (not the empty snapshot or
          // initial signal)

          // mark the seed committed.
          // We choose to mark committed here instead of when speed end. This is
          // to allow the committed state being tracked correctly in sim aligned
          // mode. Here the completion message would be given
          auto ret = MarkFrameCommitted(last_completed_speed_frame_id,
                                        "Path::Callback");
          if (!ret.ok()) {
            if (ret.code() == absl::StatusCode::kPermissionDenied) {
              LOG(INFO) << "Frame " << last_completed_speed_frame_id
                        << " already commited by Speed.";
            } else {
              LOG(ERROR) << "Commit frame " << last_completed_speed_frame_id
                         << " from path failed: " << ret.message();
            }
          }

          // Calculate latency of path + speed in last frame.
          if (completion_msg->path_start_ts_ns() != 0) {
            last_frame_total_latency_ms = (last_speed_completion_time -
                                           completion_msg->path_start_ts_ns()) /
                                          1'000'000;
            ProactiveDropMessageManager::GetInstance()
                .SavePathSpeedVnodeExecutionTime(last_frame_total_latency_ms);
          }
        }
        // debug print, to be deleted later
        LOG(INFO)
            << "Path tick start. Got speed completion signal with frame id "
            << last_completed_speed_frame_id << ", frame took "
            << last_frame_total_latency_ms << " ms";
      }
    } else {
      // debug print, to be deleted later.
      LOG(WARNING) << "Path tick start without speed completion.";
    }
    int64_t snapshot_pub_time = 0;
    bool is_empty_snapshot = false;
    if (snapshot) {
      hw_time = static_cast<uint64_t>(snapshot->agent_list_ptr->timestamp());
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>() =
          hw_time;
      ProactiveDropMessageManager::GetInstance().UpdateUpstreamFrameDropStatus(
          hw_time);
      TRACE_EVENT_SCOPE(planner, PathVNode_Process_Snapshot_And_Drop_Frame,
                        latency::PipelineID<
                            latency::PipelineType::PlannerStage1LidarHWTime>());
      message_metadata_timestamp = snapshot->pose_timestamp;
      LOG(INFO) << "hw_time=" << hw_time << ", message_metadata_timestamp="
                << message_metadata_timestamp;

      // Since snapshot is not empty, the following value is always valid.
      snapshot_pub_time =
          GetConsumedMessagePubTimeInfo(
              GetPlannerTopicName(PlannerSubTopic::planning_snapshot))
              .back();
      const int64_t snapshot_publish_timestamp_ms =
          math::Ns2Ms(snapshot_pub_time);

      if (FLAGS_planning_enable_drop_current_snapshot) {
        bool should_drop = false;
        const char* drop_reason = "invalid";
        // speed and path concurrent execution
        if (planner::FLAGS_planning_path_speed_are_parallel) {
          if (ProactiveDropMessageManager::GetInstance().CanDropCurrentSnapshot(
                  snapshot_publish_timestamp_ms)) {
            should_drop = true;
            drop_reason = "M";  // M stands for Msg_delay
          }
        } else {  // speed and path serial execution
          if (ProactiveDropMessageManager::GetInstance()
                  .CanDropCurrentSnapshotNotSplittingVnode(
                      snapshot_publish_timestamp_ms,
                      math::Ns2Ms(callback_start_time_ns_))) {
            should_drop = true;
            drop_reason = "M";
          }
        }

        auto num_inflight_frame = GetNumInflightFrames();
        if (ProactiveDropMessageManager::GetInstance()
                .CanDropFrameWithConsecutiveGuard(hw_time,
                                                  (num_inflight_frame >= 2))) {
          should_drop = true;
          drop_reason = "F";  // F stands for Frame-limit
          LOG(INFO) << "Dropping frame due to num inflight frame= "
                    << num_inflight_frame;
        }

        if (av_comm::InSimulation() && av_comm::InSimAlignedMode()) {
          auto bag_value =
              GetRecordedAuxData(varch::protos::vnode::Frame::FRAME_DROPPED);
          if (bag_value.has_value() &&
              std::holds_alternative<bool>(bag_value.value())) {
            bool recorded_frame_drop_decision =
                std::get<bool>(bag_value.value());
            LOG(INFO) << "Restore frame drop decision = "
                      << recorded_frame_drop_decision << " for frame "
                      << message_metadata_timestamp;
            should_drop = recorded_frame_drop_decision;
            drop_reason = "R";  // R stand for replay.
          }
        }

        if (should_drop) {
          RecordAuxData(varch::protos::vnode::Frame::FRAME_DROPPED, true);
          // msg_delay abbreviated as M
          ProactiveDropSnapshotPostProcess(
              message_metadata_timestamp, snapshot_pub_time,
              input_publish_timestamps_in_ns, true, path_planning_start_time,
              base::NowNs(), drop_reason);
          return;
        }
      }
      std::ostringstream pose_record_oss;
      pose_record_oss << hw_time << "," << snapshot->pose_ptr->x() << ","
                      << snapshot->pose_ptr->y();
      rt_event::PostRtEvent<rt_event::planner::Planner_Tick_Pose>(
          pose_record_oss.str());
      // Copy required information for message_metadata from snapshot because it
      // will be moved during Execute() later.
      input_publish_timestamps_in_ns =
          snapshot->message_publish_timestamps_in_ns;
      // some code uses timestamp from pose_ptr, make sure they are same
      CHECK(message_metadata_timestamp == snapshot->pose_ptr->timestamp());
    } else {
      is_empty_snapshot = true;
    }

    // Publish message_metadata_input.
    PublishInputMessageMetadata(message_metadata_timestamp,
                                input_publish_timestamps_in_ns,
                                /*drop_current_snapshot_flag=*/false);

    LATENCY_COST_RECORD_STAGE1(LAT_PlanningNodelet_Before_Execute,
                               latency_timer_);

    // Execute the planner.
    // The latency_timer_ will be reset at the end of ExecutePath. The
    // LAT_PlanningNodelet_Plan_RET means the return cost of ExecutePath.
    auto path_planning_result = ExecutePath(
        std::move(snapshot), std::move(remote_assist_responses_ptr));
    // Next the second stage is going to execute, set the on the fly hw_time.
    // This should be execute inside Plan Vnode. It will override the previous
    // value and will be read and apply to stage2 as its hw_time.
    latency::PipelineID<latency::PipelineType::PlannerOnTheFlyLidarHWTime>() =
        latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>()
            .load();
    LATENCY_COST_RECORD_STAGE1(LAT_PlanningNodelet_Plan_RET, latency_timer_);
    // TODO(WuDing): This timer should be reset at the begining of speed vnode
    // when spliting to 2 vnode. And also the timer should be a private data
    // for the speed vnode at that time.
    latency_timer_.RestartTimer();
    // NOTE: This exec_timer means nothing now.
    STATS_HISTOGRAM(planner, planning_nodelet, runtime,
                    exec_timer.GetElapsedTimeInMs());
    if (path_planning_result.path_result.path_aborted) {
      auto ret = MarkFrameAborted(PathCurrentFrame());
      CHECK(ret.ok());
      LOG(INFO) << "Aborting path planning for frame " << PathCurrentFrame();
      // algo_logic abbreviated as A
      ProactiveDropSnapshotPostProcess(
          message_metadata_timestamp, snapshot_pub_time,
          input_publish_timestamps_in_ns, true, path_planning_start_time,
          base::NowNs(), "A");
      return;
    }

    // dump/restore planner state when necessary
    auto [state_loaded, loaded_state_sptr, state_dumped, dumped_state_uptr] =
        LoadAndDumpStates(planner_state_list, message_metadata_timestamp,
                          &input_publish_timestamps_in_ns);

    int64_t path_planning_publish_speed_time = base::NowNs();
    InvokeSpeedVNode(is_empty_snapshot,                          //
                     message_metadata_timestamp,                 //
                     snapshot_pub_time,                          //
                     path_planning_start_time,                   //
                     path_planning_publish_speed_time,           //
                     std::move(path_planning_result),            //
                     std::move(input_publish_timestamps_in_ns),  //
                     state_loaded,                               //
                     std::move(loaded_state_sptr),               //
                     state_dumped,                               //
                     std::move(dumped_state_uptr));

    // Increment the sequence number.
    ++output_message_sequence_number_;
    // NOTE: This contains the duration of data publish
    LATENCY_COST_RECORD_STAGE1(LAT_PlanningNodelet_After_Execute,
                               latency_timer_);
    if (planner::FLAGS_planning_path_speed_are_parallel) {
      // This data used for speed vnode,using atomic variables for data
      // competition protection.
      ProactiveDropMessageManager::GetInstance().SetPathPublishInterval(
          math::Ns2Ms(path_planning_publish_speed_time));
      // This data used for path vnode only,there is no risk of data
      // competition.
      const int64_t path_vnode_execute_time_ms = math::Ns2Ms(
          path_planning_publish_speed_time - path_planning_start_time);
      ProactiveDropMessageManager::GetInstance().SavePathVnodeExecutionTime(
          path_vnode_execute_time_ms);
    }
#ifndef ONBOARD_ONLY
  });
#endif
}

void PlanningPathVNode::ProactiveDropSnapshotPostProcess(
    int64_t message_metadata_timestamp, int64_t snapshot_pub_time,
    const InputMsgTsMap& input_publish_timestamps_ns,
    bool drop_current_snapshot_flag, int64_t path_planning_start_time_ns,
    int64_t path_planning_publish_speed_time_ns,
    const std::string& drop_reason) {
  LOG(INFO) << "Dropping frame " << message_metadata_timestamp
            << " proactively";
  std::string payload;
  payload.reserve(32);
  // Path abbreviated as P
  payload.append("P");
  payload.append(",");
  payload.append(drop_reason);
  payload.append(",");
  payload.append(std::to_string(
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>()
          .load()));
  rt_event::PostRtEvent<rt_event::planner::ProactiveFrameDrop>(payload);
  // Publish message_metadata_input.
  PublishInputMessageMetadata(message_metadata_timestamp,
                              input_publish_timestamps_ns,
                              drop_current_snapshot_flag);
  // invoke speed vnode to publish speed_completed signal so that the
  // next frame can start
  InvokeSpeedVNode(true, message_metadata_timestamp, snapshot_pub_time,
                   // all other parameters are going to be ignored
                   path_planning_start_time_ns,
                   path_planning_publish_speed_time_ns, {}, {}, false, {},
                   false, {});
}

std::tuple<bool,                                     // state loaded
           std::shared_ptr<const pb::PlannerState>,  // loaded state
           bool,                                     // state dumped
           std::unique_ptr<pb::PlannerState>>        // partially dumped state
PlanningPathVNode::LoadAndDumpStates(
    std::list<std::shared_ptr<const pb::PlannerState>>& planner_state_list,
    int64_t snapshot_timestamp,  //
    InputMsgTsMap* input_msg_ts_map) {
  bool state_loaded = false;
  bool state_dumped = false;
  std::unique_ptr<pb::PlannerState> dumped_state_uptr;
  // Process the planner state.
  // Populate bag planner state's publish timestamp.
  if (!planner_state_list.empty()) {
    const std::vector<int64_t>& pub_times = GetConsumedMessagePubTimeInfo(
        GetPlannerTopicName(PlannerSubTopic::bag_planner_state));
    (*input_msg_ts_map)[PlannerSubTopic::bag_planner_state] =
        pub_times.empty() ? std::nullopt : std::make_optional(pub_times.back());
  }
  // If planner state has not been restored yet and should be restored, we'll
  // skip dumping planner state since the state is uninitialized.
  bool skip_dumping_planner_state =
      (!first_planner_state_loaded_ &&
       ShouldLoadPlannerState(first_planner_state_loaded_));

  // Dump the planner state to bag. Dumps before loading to better debug if
  // the state has been restored correctly.
  if (!skip_dumping_planner_state &&
      ShouldDumpPlannerState(output_message_sequence_number_)) {
    state_dumped = true;
    TRACE_EVENT_SCOPE(
        planner, PlanningNodelet_DumpPlannerState,
        latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());
    dumped_state_uptr = std::make_unique<pb::PlannerState>();
    DumpPlannerState(snapshot_timestamp, *dumped_state_uptr);
    base::SetSequenceNumberIfPossible(*dumped_state_uptr,
                                      output_message_sequence_number_);
    // Message will continue to be filled up in Speed and published there.
  }
  // Load the planner state from bag.
  std::shared_ptr<const pb::PlannerState> loaded_state_sptr;
  if (!planner_state_list.empty() &&
      ShouldLoadPlannerState(first_planner_state_loaded_)) {
    state_loaded = true;
    TRACE_EVENT_SCOPE(
        planner, PlanningNodelet_LoadPlannerState,
        latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>());
    // Load the states that belong to Path. States belong to Speed will be
    // loaded there.
    loaded_state_sptr = planner_state_list.back();
    LoadPlannerState(*loaded_state_sptr);
    first_planner_state_loaded_ = true;
    LOG(INFO) << "Loaded planning state in path with timestamp "
              << loaded_state_sptr->timestamp() << ", seq "
              << loaded_state_sptr->sequence_number();
  }
  return std::make_tuple(state_loaded, std::move(loaded_state_sptr),
                         state_dumped, std::move(dumped_state_uptr));
}

void PlanningPathVNode::InvokeSpeedVNode(
    bool snapshot_dropped,                                      //
    int64_t message_metadata_timestamp,                         //
    int64_t snapshot_pub_time,                                  //
    int64_t path_planning_start_time,                           //
    int64_t path_planning_publish_speed_time,                   //
    PathPlanningResult&& result,                                //
    InputMsgTsMap&& input_msg_ts_map,                           //
    bool state_loaded,                                          //
    std::shared_ptr<const pb::PlannerState> loaded_state_sptr,  //
    bool state_dumped,                                          //
    std::unique_ptr<pb::PlannerState> dumped_state_uptr) {
  auto speed_parameters = std::make_shared<SpeedVNodeParameters>(
      snapshot_dropped,                 //
      output_message_sequence_number_,  //
      message_metadata_timestamp,       //
      snapshot_pub_time,                //
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>()
          .load(),                       //
      path_planning_start_time,          //
      path_planning_publish_speed_time,  //
      std::move(result),                 //
      std::move(input_msg_ts_map),       //
      state_loaded,                      //
      std::move(loaded_state_sptr),      //
      state_dumped,                      //
      std::move(dumped_state_uptr),      //
      GetPathFrameView());

  GetPublisherByName<std::shared_ptr<SpeedVNodeParameters>>(
      GetPublisherName(PlannerPubTopic::planning_intra_speed_planner_param))
      ->Publish(std::move(speed_parameters));
}

PathPlanningResult PlanningPathVNode::ExecutePath(
    std::unique_ptr<const Snapshot> snapshot,
    std::unique_ptr<std::vector<pb::AssistResponse>>
        remote_assist_responses_ptr) {
  if (snapshot == nullptr) {
    LOG(ERROR) << "Empty world snapshot";
    return {PathResult(nullptr, last_route_acceptance_state_), nullptr,
            SnapshotStatus::NoSnapshot};
  }
  // check it's a new frame that is more recent than last.
  CHECK(GetPathFrameView().current_frame < snapshot->pose_timestamp)
      << GetPathFrameView().current_frame << ": " << snapshot->pose_timestamp;
  // create a new seed version. update the static FrameView that is accessible
  // by all component in Path.
  UpdatePathFrameView(/* frame_id */ snapshot->pose_timestamp,
                      /* time_stamp */ snapshot->pose_timestamp);

  MaybeRestoreOutputMessageSequenceNumber(PathCurrentFrame(), PathLastFrame(),
                                          &output_message_sequence_number_);

  base::SetSequenceNumberIfPossible(
      *PlanningSeedAccess::MutablePlanningSeed(PathCurrentFrame()),
      output_message_sequence_number_);

  // update FrameView inside world_model, so that it can get/cache the seed it
  // needs
  world_model_.UpdateFrameView(GetPathFrameView());

  auto planner_debug_wrapper = std::make_unique<PlannerDebugWrapper>();
  pb::PlanningDebug* planning_debug = planner_debug_wrapper->planning_debug;
  DCHECK(planning_debug != nullptr);

  // Update path frame view in debug.
  auto* path_frame_view_debug = planning_debug->mutable_path_frame_view_debug();
  const auto frame_view = GetPathFrameView();
  path_frame_view_debug->set_current_frame(frame_view.current_frame);
  path_frame_view_debug->set_previous_frame(frame_view.last_frame);
  path_frame_view_debug->set_last_finished_frame(
      frame_view.last_upstream_frame);
  path_frame_view_debug->set_is_path_speed_processed_in_parallel(
      frame_view.last_frame != frame_view.last_upstream_frame);
  path_frame_view_debug->set_abort_path_frame_until_last_speed_finish(false);

  auto* behavior_reasoner_debug =
      planning_debug->mutable_behavior_reasoner_debug();
  behavior_reasoner_debug->add_decoupled_maneuvers();
  auto* world_model_debug = planning_debug->mutable_world_model_debug();
  *(world_model_debug->mutable_subscribed_message_infos()) =
      snapshot->subscribed_message_infos;

  const routing::pb::RouteStatus* route_status =
      snapshot->route_status_ptr.get();
  bool is_proposed_route_solution_rejected = false;
  // Publish route state that whole route solution should be accepted
  // directly.
  if (route_status != nullptr) {
    const bool is_last_optimal_route_accepted_by_planner =
        IsLastOptimalRouteAcceptedByPlanner(
            world_model_.last_seed(),
            *DCHECK_NOTNULL(world_model_.GetLatestJointPncMapService()));
    const bool is_ego_in_junction =
        IsEgoInJunction(world_model_.last_seed(), pnc_map_service_.get());
    PublishRouteState(*route_status, snapshot->pose_timestamp,
                      is_last_optimal_route_accepted_by_planner,
                      is_ego_in_junction, &is_proposed_route_solution_rejected);
  }
  UpdateRoutingConstraintInfoDebug(
      CHECK_NOTNULL(edge_filter_manager_->GetInUsedEdgeFilter())
          ->constraint_info(),
      /*is_from_routing_topic=*/
      FLAGS_planning_load_routing_constraint_from_topic,
      planning_debug->mutable_routing_constraint_info_debug());

  LATENCY_COST_RECORD_STAGE1(LAT_PlanningNodelet_Before_Plan, latency_timer_);
  PathResult path_result =
      PathPlan(std::move(snapshot), std::move(remote_assist_responses_ptr),
               is_proposed_route_solution_rejected,
               FLAGS_enable_planning_debug ? planning_debug : nullptr,
               behavior_reasoner_debug);

  if (route_status != nullptr &&
      route_status->proposed_route_solution().mission_id() ==
          route_status->current_route_solution().mission_id()) {
    PublishRouteStateAndUpdateRouteLanesForReplan(*route_status);
  }

  PublishEmpiricalRawData();

  return {std::move(path_result), std::move(planner_debug_wrapper),
          SnapshotStatus::HasSnapshot};
}

// This function is called only during path-speed parallel execution. The
// condition 'if (!infos.empty())' indicates an init_state divergence. Then we
// erase the latest entry and drop one frame. If the next frame is serialized,
// we clear planning_three_cycle_history_. However, if latency is still high and
// the planning is still parallel, this function will be triggered again. In
// that case, a new init_state should replace the latest one to properly detect
// oscillations across three cycles of init_state (the previous one has been
// dropped, thus no need to record).
std::vector<pb::InitStateDivergenceInfo>
PlanningPathVNode::GetInitStateDivergenceInfos(
    const PlanInitState& init_state,
    const vehicle_model::JukeIntegratedModelWithAxleRectangularShape&
        model_with_shape) {
  // Extract current values.
  const double current_steering_wheel_angle_in_degree = math::Radian2Degree(
      init_state.state[StateIndex::DELTA] *
      model_with_shape.param().measurement().steering_gear_ratio());
  const double current_speed = init_state.state[StateIndex::SPEED];
  const double current_acceleration = init_state.state[StateIndex::ACCEL];
  const pb::BehaviorType current_behavior =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          PathLastFinishedFrame())
          ->selected_behavior_type();
  const pb::IntentionResult::IntentionHomotopy current_intention =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          PathLastFinishedFrame())
          ->last_intent()
          .homotopy();

  // Create a new cycle record.
  PlanningCycleHistory current_cycle{current_steering_wheel_angle_in_degree,
                                     current_speed, current_acceleration,
                                     current_behavior, current_intention};
  const int64_t now = base::Now();
  // Add the current cycle to the history.
  planning_three_cycle_history_.EmplaceBack(now, current_cycle);
  if (planning_three_cycle_history_.size() > 3) {
    planning_three_cycle_history_.EraseEarliest();
  }

  // Wait until we have three complete cycles.
  if (planning_three_cycle_history_.size() < 3) {
    return std::vector<pb::InitStateDivergenceInfo>();
  }

  const auto& planning_cycle_0 = planning_three_cycle_history_[0].second;
  const auto& planning_cycle_1 = planning_three_cycle_history_[1].second;
  const auto& planning_cycle_2 = planning_three_cycle_history_[2].second;
  // Check the steering angle condition.
  const double diff1_steering = planning_cycle_1.steering_wheel_angle -
                                planning_cycle_0.steering_wheel_angle;
  const double diff2_steering = planning_cycle_2.steering_wheel_angle -
                                planning_cycle_1.steering_wheel_angle;
  const bool steering_wheel_angle_oscillate =
      (std::abs(diff1_steering) >
           kFrameDropSteeringWheelAngleDeltaThresholdForParallelPlanning &&
       std::abs(diff2_steering) >
           kFrameDropSteeringWheelAngleDeltaThresholdForParallelPlanning &&
       diff1_steering * diff2_steering < 0);

  std::vector<pb::InitStateDivergenceInfo> infos;
  // If steering wheel angle oscillation exceeds 2 degrees, and the behavior for
  // three consecutive cycles is lane keep with equal and allowable intentions,
  // record this as a divergence event.
  if (steering_wheel_angle_oscillate) {
    pb::InitStateDivergenceInfo info;
    info.set_reason(pb::InitStateDivergenceInfo::DIVERGENCE_STEERING);
    info.set_first_delta(diff1_steering);
    info.set_second_delta(diff2_steering);
    info.set_first_value(planning_cycle_0.steering_wheel_angle);
    info.set_second_value(planning_cycle_1.steering_wheel_angle);
    info.set_third_value(planning_cycle_2.steering_wheel_angle);
    infos.push_back(info);
  }

  // If speed oscillation exceeds 0.5m/s, record this as a divergence event.
  const double diff1_speed = planning_cycle_1.speed - planning_cycle_0.speed;
  const double diff2_speed = planning_cycle_2.speed - planning_cycle_1.speed;
  const bool speed_condition =
      (std::abs(diff1_speed) >
           kFrameDropSpeedDeltaThresholdForParallelPlanning &&
       std::abs(diff2_speed) >
           kFrameDropSpeedDeltaThresholdForParallelPlanning &&
       diff1_speed * diff2_speed < 0);
  if (speed_condition) {
    pb::InitStateDivergenceInfo info;
    info.set_reason(pb::InitStateDivergenceInfo::DIVERGENCE_SPEED);
    info.set_first_delta(diff1_speed);
    info.set_second_delta(diff2_speed);
    info.set_first_value(planning_cycle_0.speed);
    info.set_second_value(planning_cycle_1.speed);
    info.set_third_value(planning_cycle_2.speed);
    infos.push_back(info);
  }

  // If acceleration oscillation exceeds 0.5m/s^2, record this as a divergence
  // event.
  const double diff1_acceleration =
      planning_cycle_1.acceleration - planning_cycle_0.acceleration;
  const double diff2_acceleration =
      planning_cycle_2.acceleration - planning_cycle_1.acceleration;
  const bool acceleration_condition =
      (std::abs(diff1_acceleration) >
           kFrameDropAccelerationDeltaThresholdForParallelPlanning &&
       std::abs(diff2_acceleration) >
           kFrameDropAccelerationDeltaThresholdForParallelPlanning &&
       diff1_acceleration * diff2_acceleration < 0);
  if (acceleration_condition) {
    pb::InitStateDivergenceInfo info;
    info.set_reason(pb::InitStateDivergenceInfo::DIVERGENCE_ACCELERATION);
    info.set_first_delta(diff1_acceleration);
    info.set_second_delta(diff2_acceleration);
    info.set_first_value(planning_cycle_0.acceleration);
    info.set_second_value(planning_cycle_1.acceleration);
    info.set_third_value(planning_cycle_2.acceleration);
    infos.push_back(info);
  }

  // We erase the latest in case of high latency. When init_state divergence is
  // detected, the system drops one frame to force path-speed serialization.
  // However, if latency is significantly large, dropping one frame may not be
  // sufficient to serialize the system, a second frame may need to be dropped.
  // By erasing the latest init_state info, we ensure that a new init_state can
  // replace it, allowing the divergence detection logic to function correctly.
  if (!infos.empty()) {
    planning_three_cycle_history_.EraseLatest();
  }

  return infos;
}

void PlanningSpeedVNode::ExecuteSpeed(
    PathPlanningResult& path_planning_result,
    int64_t message_metadata_timestamp, int64_t path_planning_start_time,
    const std::shared_ptr<const pb::AssistStuckModelOutput>&
        assist_stuck_model_output_sptr,
    const assist::RemoteWarningInputs& remote_warning_inputs,
    InputMsgTsMap* input_timestamps_map, bool load_planner_state,
    const pb::PlannerState* planner_state_in, bool dump_planner_state,
    pb::PlannerState* planner_state_out) {
  latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>() =
      latency::PipelineID<latency::PipelineType::PlannerOnTheFlyLidarHWTime>()
          .load();

  // In parallel path-speed planning mode, check whether the behavior type in
  // the (n-1) frame
  // matches the one selected in the (n-2) frame. If a behavior type change is
  // detected, mark the current (n) frame accordingly and trigger a frame drop
  // to fallback to serialized path-speed planning. Placing this check here
  // avoids wasting the speed_node’s execution time in the current (n) frame.
  if (planner::FLAGS_planning_path_speed_are_parallel) {
    const auto frame_view = GetSpeedFrameView();
    // Only run the parallel‐planning fallback check when both frames are
    // valid (i.e. not the special “first” or “second” frame) and the last
    // frame differs from the last finished frame. That difference indicates
    // that path and speed planning are currently overlapping.
    if (!IsSpecialFrameId(frame_view.last_frame) &&
        !IsSpecialFrameId(frame_view.last_upstream_frame) &&
        (frame_view.last_frame != frame_view.last_upstream_frame)) {
      if (ProactiveDropMessageManager::GetInstance()
              .CanDropFrameWithConsecutiveGuard(
                  latency::PipelineID<
                      latency::PipelineType::PlannerStage2LidarHWTime>()
                      .load(),
                  Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                      SpeedLastFrame())
                          ->selected_behavior_type() !=
                      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                          SpeedLastFinishedFrame())
                          ->selected_behavior_type(),
                  true)) {
        rt_event::PostRtEvent<rt_event::planner::BehaviorTypeChanged>(
            fmt::format("{}:{}:{} - {} vs {},{}", frame_view.current_frame,
                        frame_view.last_frame, frame_view.last_upstream_frame,
                        pb::BehaviorType_Name(
                            Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                                SpeedLastFrame())
                                ->selected_behavior_type()),
                        pb::BehaviorType_Name(
                            Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                                SpeedLastFinishedFrame())
                                ->selected_behavior_type()),
                        latency::PipelineID<
                            latency::PipelineType::PlannerStage2LidarHWTime>()
                            .load()));

        auto ret = MarkFrameAborted(SpeedCurrentFrame());
        CHECK(ret.ok());
        LOG(INFO) << "Aborting speed planning for frame " << SpeedCurrentFrame()
                  << ", Dropping frame " << message_metadata_timestamp
                  << " proactively";
        ProactiveDropPathPostProcess("A");
        PublishFullMessageMetadata(message_metadata_timestamp,
                                   *input_timestamps_map,
                                   /*drop_current_snapshot_flag=*/true);
        return;
      }
    }
  }

  if (path_planning_result.snapshot_status == SnapshotStatus::HasSnapshot) {
    google::protobuf::Arena arena;
    auto* customer_monitor_visual =
        google::protobuf::Arena::CreateMessage<pb::CustomerMonitorVisual>(
            &arena);
    DCHECK(path_planning_result.path_result.speed_world_model_data);

    PlanningResult planning_result;
    std::unique_ptr<assist::AssistModelInput> assist_model_input =
        FLAGS_planning_enable_remote_assist_model_vnode
            ? std::make_unique<assist::AssistModelInput>()
            : nullptr;
    pb::PlanningDebug* planning_debug =
        path_planning_result.planner_debug_wrapper->planning_debug;
    DCHECK(planning_debug != nullptr);
    if (path_planning_result.path_result.path_planner_output.has_value()) {
      auto* behavior_reasoner_debug =
          planning_debug->mutable_behavior_reasoner_debug();
      LATENCY_COST_RECORD_STAGE2(LAT_Gap_Between_SpeedCallback_And_SpeedPlan,
                                 latency_timer_);
      planning_result =
          SpeedPlan(path_planning_start_time, path_planning_result.path_result,
                    assist_stuck_model_output_sptr, remote_warning_inputs,
                    assist_model_input ? assist_model_input.get() : nullptr,
                    FLAGS_enable_planning_debug ? planning_debug : nullptr,
                    behavior_reasoner_debug, customer_monitor_visual);
    } else {
      // If world model update failed. world_model data is not moved to
      // speed_world_model yet. Try to generate a safe full-stop trajectory when
      // ego speed is low or send a fault code to directly trigger the
      // fallback system when current speed is not safe.
      auto* exception_debug = planning_debug->mutable_exception_reason_debug();
      exception_debug->set_exception_reason(
          planner::pb::ExceptionReasonDebug::WORLD_MODEL_UPDATE_FAILURE);
      exception_debug->set_exception_message("Failed to update world model.");
      HandlePlanningNodeletException(
          path_planning_result.path_result.speed_world_model_data
              ->robot_state(),
          path_planning_result.path_result.speed_world_model_data
              ->prediction_timestamp(),
          path_planning_result.path_result.speed_world_model_data
              ->mrc_request_ptr(),
          path_planning_result.path_result.speed_world_model_data
              ->should_planner_respond_mrc(),
          &planning_result, customer_monitor_visual);
      // If world model update failed, the validate engagement condition
      // warning will be retained continuously, it makes a world_model update
      // problem a SS problem. We need clear engagement warning to correctly
      // classify problems and avoid Health ERROR-level fault.
      ops_warning_generator_.ClearAllEngagementFault();
    }

    const std::optional<voy::IssueTagChangeInfo> issue_tag_change_info =
        GeneratePlannerIssueTagChangeInfo(
            path_planning_result.path_result.speed_world_model_data
                ->immediate_pull_over_state_manager()
                .trigger_state_change_type(),
            path_planning_result.path_result.speed_world_model_data
                ->snapshot_timestamp());
    PublishPlannerIssueTagChangeInfo(issue_tag_change_info);

    PublishPlanningResult(
        std::move(path_planning_result.path_result.speed_world_model_data),
        std::move(path_planning_result.path_result.decoupled_maneuver_return),
        std::move(path_planning_result.planner_debug_wrapper), &planning_result,
        customer_monitor_visual, std::move(assist_model_input));
  }

  // Dump the planner state to bag. Dumps before loading to better debug if
  // the state has been restored correctly.
  if (dump_planner_state) {
    TRACE_EVENT_SCOPE(
        planner, PlanningNodelet_DumpPlannerState,
        latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());
    CHECK(planner_state_out != nullptr);
    DumpPlannerState(*planner_state_out);
    base::SetSequenceNumberIfPossible(*planner_state_out,
                                      output_message_sequence_number_);
    GetPublisherByName<pb::PlannerState>(
        GetPublisherName(PlannerPubTopic::planner_state))
        ->PublishSeedAsync(std::move(*planner_state_out));
  }
  // Load the planner state from bag.
  if (load_planner_state) {
    CHECK(planner_state_in != nullptr);
    // should only load state once.
    TRACE_EVENT_SCOPE(
        planner, PlanningNodelet_LoadPlannerState,
        latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());
    LoadPlannerState(*planner_state_in);
    planner_state_loaded_cnt_++;
    LOG(INFO) << "Loaded planning state in Speed with timestamp "
              << planner_state_in->timestamp() << ", seq "
              << planner_state_in->sequence_number();
  }

  // Publish message_metadata.
  PublishFullMessageMetadata(message_metadata_timestamp, *input_timestamps_map,
                             /*drop_current_snapshot_flag=*/false);
}

void PlanningSpeedVNode::PublishPlanningResult(
    std::unique_ptr<SpeedWorldModel> speed_world_model_data,
    std::optional<ManeuverReturnType> decoupled_maneuver_return,
    std::shared_ptr<PlannerDebugWrapper> planner_debug_wrapper,
    PlanningResult* planning_result,
    pb::CustomerMonitorVisual* customer_monitor_visual,
    std::shared_ptr<assist::AssistModelInput> assist_model_input) {
  TRACE_EVENT_SCOPE(
      planner, PlanningNodelet_PublishPlanningResult,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());
  DCHECK(planning_result);

  std::unique_ptr<pb::TrajectoryResult> trajectory_result =
      std::make_unique<pb::TrajectoryResult>();
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_timestamp(latency::PipelineID<
                          latency::PipelineType::PlannerStage2LidarHWTime>());
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->set_planner_trajectory(*(planning_result->trajectory));
  ::exception_handler::exception_handler_main::TrajectoryResultInstance::
      GetInstance()
          ->swap_trajectory_result(trajectory_result);
  GetPublisherByName<pb::TrajectoryResult>(
      GetPublisherName(PlannerPubTopic::planning_trajectory_result))
      ->Publish(*trajectory_result);

  // If enable global selection, planner node will not publish trajectory
  // directly. Global selection nodelet will publish trajectory after selection.
  if (!FLAGS_planning_disable_planner_trajectory) {
    base::SetSequenceNumberIfPossible(*planning_result->trajectory,
                                      output_message_sequence_number_);
    GetPublisherByName<pb::Trajectory>(
        GetPublisherName(PlannerPubTopic::planning_trajectory))
        ->Publish(*(planning_result->trajectory));
  }

  base::SetSequenceNumberIfPossible(planning_result->ops_warning,
                                    output_message_sequence_number_);
  GetPublisherByName<pb::OpsWarning>(
      GetPublisherName(PlannerPubTopic::planning_ops_warning))
      ->Publish(planning_result->ops_warning);

  if (FLAGS_planning_enable_selectnet_sample_generation) {
    pb::BehaviorReasonersDebug& behavior_reasoner_debug =
        *planner_debug_wrapper->planning_debug
             ->mutable_behavior_reasoner_debug();
    if (!behavior_reasoner_debug.decoupled_maneuvers().empty()) {
      pb::TrajectorySelectionDebug& selection_debug =
          *behavior_reasoner_debug.mutable_decoupled_maneuvers(0)
               ->mutable_trajectory_selection_debug();
      pb::SelectnetSample& selectnet_sample =
          *selection_debug.mutable_feature_sample();
      base::SetSequenceNumberIfPossible(selectnet_sample,
                                        output_message_sequence_number_);
      GetPublisherByName<pb::SelectnetSample>(
          GetPublisherName(PlannerPubTopic::planning_selectnet_sample))
          ->Publish(std::move(selectnet_sample));
      selection_debug.clear_feature_sample();
    }
  }

  if (FLAGS_enable_planning_debug) {
    base::SetSequenceNumberIfPossible(*planner_debug_wrapper->planning_debug,
                                      output_message_sequence_number_);
    GetPublisherByName<std::shared_ptr<PlannerDebugWrapper>>(
        GetPublisherName(
            PlannerPubTopic::planning_intra_planning_debug_wrapper))
        ->Publish(planner_debug_wrapper);
  }

  if (ShouldDumpPlanningSeed()) {
    // sequence number already set in path.
    // use regular publish instead of PublishSeed since this seed is to be
    // consumed by _next_ frame.
    GetPublisherByName<pb::PlanningSeed>(
        GetPublisherName(PlannerPubTopic::planning_seed))
        ->Publish(PlanningSeedAccess::PlanningSeed(SpeedCurrentFrame()));
  }

  base::SetSequenceNumberIfPossible(*customer_monitor_visual,
                                    output_message_sequence_number_);
  GetPublisherByName<pb::CustomerMonitorVisual>(
      GetPublisherName(PlannerPubTopic::planning_customer_monitor_visual))
      ->Publish(*customer_monitor_visual);

  base::SetSequenceNumberIfPossible(
      planning_result->downgrade_planning_lane_sequence.has_value()
          ? planning_result->downgrade_planning_lane_sequence.value()
          : planning_result->planning_lane_sequence,
      output_message_sequence_number_);
  GetPublisherByName<pb::PlanningLaneSequence>(
      GetPublisherName(PlannerPubTopic::planning_planning_lane_sequence))
      ->Publish(planning_result->downgrade_planning_lane_sequence.has_value()
                    ? planning_result->downgrade_planning_lane_sequence.value()
                    : planning_result->planning_lane_sequence);

  for (auto& request : planning_result->assist_requests) {
    if (request.type_case() == pb::AssistRequest::kPullOutConfirmRequest) {
      base::SetSequenceNumberIfPossible(request,
                                        output_message_sequence_number_);
      GetPublisherByName<pb::AssistRequest>(
          GetPublisherName(PlannerPubTopic::planning_pullout_request))
          ->PublishAsync(std::move(request));
    } else if (request.type_case() ==
               pb::AssistRequest::kMatchStopCellRequest) {
      base::SetSequenceNumberIfPossible(request,
                                        output_message_sequence_number_);
      GetPublisherByName<pb::AssistRequest>(
          GetPublisherName(PlannerPubTopic::planning_cloud_cell_request))
          ->PublishAsync(std::move(request));
    } else if (request.type_case() ==
               pb::AssistRequest::kStuckDetectionRecallSignal) {
      base::SetSequenceNumberIfPossible(request,
                                        output_message_sequence_number_);
      GetPublisherByName<pb::AssistRequest>(
          GetPublisherName(
              PlannerPubTopic::planning_stuck_detection_recall_signal))
          ->PublishAsync(std::move(request));
    } else {
      base::SetSequenceNumberIfPossible(request,
                                        output_message_sequence_number_);
      GetPublisherByName<pb::AssistRequest>(
          GetPublisherName(PlannerPubTopic::planning_assist_request))
          ->PublishAsync(std::move(request));
    }
  }

  if (FLAGS_planning_enable_remote_warning_signal_publish &&
      planning_result->remote_warning_signal.has_value()) {
    base::SetSequenceNumberIfPossible(*planning_result->remote_warning_signal,
                                      output_message_sequence_number_);
    GetPublisherByName<pb::RemoteWarningSignal>(
        GetPublisherName(PlannerPubTopic::remote_warning_signal))
        ->PublishAsync(*planning_result->remote_warning_signal);
    LOG(INFO) << "Published a remote warning signal: "
              << planning_result->remote_warning_signal->DebugString();
  }

  // Indicate that ego has reached destination.
  const routing::pb::RouteStatus* route_status =
      speed_world_model_data->latest_snapshot_ptr()->route_status_ptr.get();
  if (planning_result->has_reached_destination) {
    const std::optional<routing::pb::CommandPurpose> optional_command_purpose =
        GetCommandPurposeWhenHasReachedDestination(
            speed_world_model_data->mrc_request_ptr(),
            speed_world_model_data->immediate_pull_over_state_manager()
                .immediate_pullover_source(),
            speed_world_model_data->regional_map()
                .regional_path.distance_to_destination_m,
            planning_result->has_reached_destination,
            speed_world_model_data->IsImmediatePullOverTriggeredByMRC());
    if (optional_command_purpose.has_value() &&
        !has_published_route_completion_ && route_status &&
        route_status->has_ride_route_query() &&
        route_status->has_current_route_solution() &&
        current_route_timestamp_ ==
            route_status->current_route_solution().route_timestamp()) {
      route_command_->set_command_type(
          routing::pb::CommandType::ROUTE_COMPLETION);
      route_command_->set_command_purpose(optional_command_purpose.value());
      route_command_->set_completion_mission_id(
          planning_result->trajectory->extra_planner_signals()
                      .behavior_signal() == pb::ExtraPlannerSignals::PULL_OVER
              ? speed_world_model_data->pull_over_request_info_speed_data()
                    .mission_id()
              : route_status->proposed_route_solution().mission_id());
      *route_command_->mutable_route_query() = route_status->ride_route_query();
      base::SetSequenceNumberIfPossible(*route_command_,
                                        output_message_sequence_number_);
      route_command_->set_immediate_pullover_request_type(
          routing::pb::kNotRequest);
      route_command_->set_immediate_pullover_source(routing::pb::kNoneSource);
      GetPublisherByName<routing::pb::RouteCommand>(
          GetPublisherName(PlannerPubTopic::routing_planner_route_command))
          ->PublishAsync(route_command_);

      has_published_route_completion_ = true;
      has_published_immediate_pullover_request_ = false;
    }
    goto end_async_free;
  }

  if (planning_result->replan_query) {
    route_command_->set_command_type(
        routing::pb::CommandType::ROUTE_REPLANNING_QUERY);
    *route_command_->mutable_route_query() = *planning_result->replan_query;
    base::SetSequenceNumberIfPossible(*route_command_,
                                      output_message_sequence_number_);

    // If last cycle's replan query decides to publish request of triggering
    // immediate pull over, we will send trigger command in current cycle, set
    // the loop route and replan purpose in route query.
    if (planning_result->replan_query->followed_path_type() ==
        routing::pb::QueryFollowedPathType::
            NONE_FOLLOWED_PATH_AND_TRIGGER_IMMEDIATE_PULL_OVER) {
      LOG(INFO) << "Send forward retry immediate pull over request.";
      route_command_->set_immediate_pullover_request_type(
          routing::pb::kImmediatePullOver);
      route_command_->set_immediate_pullover_source(routing::pb::kPlanning);
      route_command_->mutable_route_query()->set_is_loop_route(true);
      route_command_->mutable_route_query()->set_replan_purpose(
          routing::pb::ReplanPurpose::DESIRED_REPLAN_FOR_LOOP_ROUTE);
      has_published_immediate_pullover_request_ = true;

    } else if (planning_result->replan_query->followed_path_type() ==
               routing::pb::QueryFollowedPathType::
                   NONE_FOLLOWED_PATH_AND_CANCEL_IMMEDIATE_PULL_OVER) {
      // If last cycle's replan query decides to publish request of cancelling
      // immediate pull over, we will send cancel command in current cycle.
      route_command_->set_immediate_pullover_request_type(routing::pb::kCancel);
      route_command_->set_immediate_pullover_source(routing::pb::kPlanning);
      has_published_immediate_pullover_request_ = false;

    } else {
      // Indicate that planning needs regular replan query.
      route_command_->set_immediate_pullover_request_type(
          routing::pb::kNotRequest);
      route_command_->set_immediate_pullover_source(routing::pb::kNoneSource);
      if (speed_world_model_data->immediate_pull_over_state_manager()
              .IsTriggeredByPlanning()) {
        route_command_->mutable_route_query()->set_is_loop_route(true);
        route_command_->mutable_route_query()->set_replan_purpose(
            routing::pb::ReplanPurpose::DESIRED_REPLAN_FOR_LOOP_ROUTE);
        LOG(INFO) << "Set loop route when planning trigger immediate pull over "
                     "request.";
      }
      has_published_immediate_pullover_request_ = false;
    }

    GetPublisherByName<routing::pb::RouteCommand>(
        GetPublisherName(PlannerPubTopic::routing_planner_route_command))
        ->PublishAsync(route_command_);
  }

  // In `PlanningSpeedVNode::ExecuteSpeed`, we use
  // FLAGS_planning_enable_remote_assist_model_vnode to control the share
  // ptr assist_model_input is nullptr.
  // 1. If FLAGS_planning_enable_remote_assist_model_vnode is true,
  //   - share ptr assist_model_input is not nullptr,
  //   - we will move the unique_ptr `speed_world_model_data` to the unique_ptr
  //     `assist_model_input->speed_world_model`.
  //   - So the origin unique_ptr `speed_world_model_data` will be nullptr and
  //     not need to be destroyed in `PlanningSpeedVNode`.
  //   - Then the unique_ptr `assist_model_input` will be published to
  //     `RemoteAssistModelVNode` and destroyed in this vnode.
  // 2. If FLAGS_planning_enable_remote_assist_model_vnode is false,
  //   - share ptr assist_model_input is nullptr,
  //   - we will skip moving the unique_ptr `speed_world_model_data` to the
  //     unique_ptr `assist_model_input->speed_world_model`
  //   - and will not publish anything to `RemoteAssistModelVNode`.
  //   - The origin unique_ptr `speed_world_model_data` will be destroyed here.
  if (assist_model_input && assist_model_input->extract_feature_on_cycle) {
    assist_model_input->previous_timestamp = base::Now();
    assist_model_input->speed_world_model = std::move(speed_world_model_data);
    assist_model_input->maneuver_return = std::move(decoupled_maneuver_return);
    GetPublisherByName<assist::AssistModelInput::SPtr>(
        GetPublisherName(PlannerPubTopic::remote_assist_model_input))
        ->PublishAsync(std::move(assist_model_input));
  }

end_async_free:
  // Destruction of speed_world_model in async.
  if (speed_world_model_data) {
    async_destruction_pool_->PushTask(
        [speed_world_model_background_ptr =
             std::move(speed_world_model_data)]() mutable {
          TRACE_EVENT_SCOPE(
              planner, PlanningNodelet_SpeedWorldModelDestruction,
              latency::PipelineID<
                  latency::PipelineType::PlannerStage2LidarHWTime>());
          speed_world_model_background_ptr.reset();
        });
  }
}

void PlanningPathVNode::PublishRouteAcceptance(
    const routing::pb::RouteSolution& route_solution) {
  routing::pb::PlanningRouteState route_state;
  *route_state.mutable_proposed_route_solution() =
      CreateSlimRouteSolution(route_solution);
  // Update current_route_solution using same route solution.
  *route_state.mutable_current_route_solution() =
      CreateSlimRouteSolution(route_solution);
  base::SetSequenceNumberIfPossible(route_state,
                                    output_message_sequence_number_);
  route_state.set_route_state(routing::pb::RouteState::kPlanningAccepted);
  last_route_acceptance_state_ = route_state.route_state();
  GetPublisherByName<routing::pb::PlanningRouteState>(
      GetPublisherName(PlannerPubTopic::routing_planning_route_state))
      ->PublishAsync(std::move(route_state));
}

void PlanningPathVNode::PublishRouteRejection(
    const routing::pb::RouteSolution& route_solution,
    bool only_reject_route_lanes) {
  routing::pb::PlanningRouteState route_state;
  *route_state.mutable_proposed_route_solution() =
      CreateSlimRouteSolution(route_solution);
  if (only_reject_route_lanes) {
    route_state.mutable_proposed_route_solution()
        ->mutable_route_lanes()
        ->CopyFrom(route_solution.route_lanes());
    route_state.set_route_state(
        routing::pb::RouteState::kPlanningRejectedRouteLanes);
  } else {
    route_state.set_route_state(routing::pb::RouteState::kPlanningRejected);
  }
  last_route_acceptance_state_ = route_state.route_state();
  // Update current_route_solution using empty route solution.
  const routing::pb::RouteSolution empty_route_solution;
  *route_state.mutable_current_route_solution() =
      CreateSlimRouteSolution(empty_route_solution);
  base::SetSequenceNumberIfPossible(route_state,
                                    output_message_sequence_number_);
  GetPublisherByName<routing::pb::PlanningRouteState>(
      GetPublisherName(PlannerPubTopic::routing_planning_route_state))
      ->PublishAsync(std::move(route_state));
}

void PlanningPathVNode::PublishEmpiricalRawData() {
  const auto& empirical_info_raw_data =
      world_model_.empirical_info_processor().empirical_info_raw_data();
  if (FLAGS_planning_enable_upload_empirical_raw_data &&
      empirical_info_raw_data.ByteSizeLong() > 0) {
    GetPublisherByName<hdmap::EmpiricalInfoRawDataList>(
        GetPublisherName(PlannerPubTopic::planning_empirical_raw_data))
        ->Publish(empirical_info_raw_data);
  }
  return;
}

void PlanningSpeedVNode::HandlePlanningNodeletException(
    const RobotState& robot_state, int64_t prediction_timestamp,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    bool should_planner_respond_mrc, PlanningResult* planning_result,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  DCHECK(planning_result != nullptr);
  const double no_traj_elapsed_ms = no_traj_timer_.GetElapsedTimeInMs();
  const bool should_call_fallback =
      no_traj_elapsed_ms > kNoTrajTimerThresholdInMs;
  const bool should_respond_mrc =
      should_planner_respond_mrc && mrc_request_ptr != nullptr &&
      mrc_request_ptr->mrm_type() != mrc::pb::UNDEFINED;
  // Generate backup full-stop trajectory when ego speed is low, in Manual
  // driving mode, or fails to execute mrc request.
  if (!robot_state.IsInAutonomousMode() ||
      robot_state.plan_init_state_snapshot().speed() <
          kBackupTrajectorySafeVelocityThresholdInMpS ||
      should_respond_mrc) {
    pb::Trajectory backup_trajectory = planner::GenerateBackupTrajectory(
        robot_state, prediction_timestamp, should_respond_mrc);
    if (should_respond_mrc &&
        backup_trajectory.intention().motion() == pb::PARKED) {
      customer_monitor_visual->set_has_reached_mrc(true);
      *customer_monitor_visual->mutable_mrc_request() = *mrc_request_ptr;
      customer_monitor_visual->mutable_mrc_info()->set_mrc_status(
          pb::kEnterMRCSuccess);
    }
    UpdateLastTrajectorySeed(
        backup_trajectory,
        Seed::Access<token::WorldModel_LastTrajectory>::MutableMsg(
            SpeedCurrentFrame())
            .get());
    planning_result->trajectory =
        std::make_shared<pb::Trajectory>(std::move(backup_trajectory));
  } else {
    // Call fallback to takeover when ego speed is not safe, aka, faster than
    // 5m/s.
    if (should_call_fallback) {
      ops_warning_generator_.PublishPlanningFatal(robot_state);
    }
  }
}

void PlanningPathVNode::PublishInputMessageMetadata(
    int64_t message_metadata_timestamp,
    const InputMsgTsMap& input_publish_timestamps,
    bool drop_current_snapshot_flag) {
  // Publish.
  auto publisher = GetPublisherByName<pb::MessageMetadata>(
      GetPublisherName(PlannerPubTopic::planning_message_metadata_input));
  DCHECK(publisher);
  publisher->PublishAsync(ComposeMessageMetadata(
      message_metadata_timestamp, input_publish_timestamps,
      /* input_topic_only = */ true, drop_current_snapshot_flag,
      output_message_sequence_number_));
}

/****************************
Speed Nodelet
*/

PlanningSpeedVNode::PlanningSpeedVNode()
    : planner_config_center_(PlannerConfigCenter::GetInstance()),
      ops_warning_generator_(planner_config_center_.ops_warning_config(),
                             ShouldEnableEngageWarning()),
      customer_monitor_visual_msg_generator_(
          planner_config_center_.customer_monitor_config()),
      main_speed_planner_(planner_config_center_.behavior_reasoner_config()),
      route_command_(std::make_shared<routing::pb::RouteCommand>()),
      async_destruction_pool_(std::make_unique<av_comm::ThreadPool>(1)) {
  route_command_->set_command_type(
      routing::pb::CommandType::ROUTE_REPLANNING_QUERY);
}

void PlanningSpeedVNode::Callback(
    std::list<std::shared_ptr<const SpeedVNodeParameters::SPtr>>&
        speed_vnode_parameter_list,
    std::list<std::shared_ptr<const pb::AssistStuckModelOutput>>&
        assist_stuck_model_output_list,
    std::list<std::shared_ptr<const pb::PlanningSeed>>& bag_seed_list,
    std::list<std::shared_ptr<const pb::ExceptionHandlerForPerceptionDebug>>&
        eh_for_perception_debug_list,
    std::list<std::shared_ptr<const ::pb::SystemState>>& system_state_list,
    std::list<std::shared_ptr<const ::onboard_metrics::pb::EarlyWarning>>&
        early_warning_list,
    std::list<std::shared_ptr<const ::voy::perception::CollisionDetection>>&
        collision_detection_list,
    std::list<std::shared_ptr<const ::voy::perception::SensorAbnormality>>&
        sensor_abnormality_list,
    std::list<std::shared_ptr<const ::voy::perception::SensorAbnormality>>&
        camera_abnormality_list) {
// The tbb of the planner will affect the simulation determinism,
// so we have decided to turn off the tbb of the planner in the simulation.
// To minimize the impact on onboard, we have added ONBOARD_ONLY macro
#ifndef ONBOARD_ONLY
  int max_concurrency = -1;
  if (av_comm::InSimulation() && FLAGS_planning_disable_tbb_parallelism) {
    max_concurrency = 1;
  }
  tbb::task_arena arena(max_concurrency);
  arena.execute([&] {
#endif
    PLANNER_MARK_THREAD_OWNER_AS_SPEED();
    base::WallClockElapsedTimer exec_timer;
    latency_timer_.RestartTimer();
    if (speed_vnode_parameter_list.empty()) return;
    if (speed_vnode_parameter_list.size() > 1) {
      LOG(WARNING)
          << "Got multiple inputs from path, only the last one is going "
             "to be processed.";
    }
    int64_t speed_planning_start_time = base::NowNs();
    const SpeedVNodeParameters::SPtr param_ptr =
        *speed_vnode_parameter_list.back();
    CHECK(param_ptr);

    output_message_sequence_number_ = param_ptr->output_message_sequence_number;
    pb::SpeedCompletionSignal completion_signal;
    completion_signal.set_path_start_ts_ns(param_ptr->path_planning_start_time);
    completion_signal.set_speed_start_ts_ns(speed_planning_start_time);
    DCHECK_LE(completion_signal.path_start_ts_ns(),
              completion_signal.speed_start_ts_ns());

    // Get the latest remote assist model inference results.
    if (FLAGS_planning_enable_remote_assist_model_vnode) {
      if (latest_assist_stuck_model_output_uptr_ != nullptr) {
        // Previously planner was actively dropping frames, and in the same
        // frame also subscribed to valid latest model outputs. So
        // latest_assist_stuck_model_output_uptr_ retains the latest model
        // output at the time of the dropped frame, waiting to be used by the
        // `AssistStuckSceneDetector`.
        rt_event::PostRtEvent<
            rt_event::planner::RecordRAModelOutputInPlannerDroppedFrame>();
      }
      if (!assist_stuck_model_output_list.empty()) {
        // Save the latest model output subscribed by the planner's current
        // frame in 'latest_assist_stuck_model_output_uptr_'.
        // If latest_assist_stuck_model_output_uptr_ has value, it will be
        // updated to the latest model output value.
        latest_assist_stuck_model_output_uptr_ =
            std::make_unique<const pb::AssistStuckModelOutput>(
                *assist_stuck_model_output_list.back());
      }
    }

    // commit last version, apply bag seed if necessary.
    HandleSeed(bag_seed_list, param_ptr->path_frame_view);

    // In the case that snapshot is dropped, nothing should be done except
    // publish the speed completion signal
    if (!param_ptr->snapshot_dropped) {
      FrameId current_frame_ts =
          param_ptr->path_planning_result.path_result.speed_world_model_data
              ->snapshot_timestamp();
      CHECK(param_ptr->message_metadata_timestamp == current_frame_ts)
          << param_ptr->message_metadata_timestamp << ", " << current_frame_ts;
      CHECK(current_frame_ts == param_ptr->path_frame_view.current_frame);
      UpdateSpeedFrameView(param_ptr->path_frame_view);
      completion_signal.set_frame_id(SpeedCurrentFrame());

      // Dropping speed frame is currently disabled.
      bool drop_speed_frame =
          planner::FLAGS_planning_path_speed_are_parallel &&
          (FLAGS_planning_enable_drop_current_path) &&
          (ProactiveDropMessageManager::GetInstance().CanDropCurrentPath(
              math::Ns2Ms(param_ptr->path_planning_pub_speed_time)));

      if (av_comm::InSimulation() && av_comm::InSimAlignedMode()) {
        auto bag_value =
            GetRecordedAuxData(varch::protos::vnode::Frame::FRAME_DROPPED);
        if (bag_value.has_value() &&
            std::holds_alternative<bool>(bag_value.value())) {
          bool recorded_frame_drop_decision = std::get<bool>(bag_value.value());
          LOG(INFO) << "Restore frame drop decision = "
                    << recorded_frame_drop_decision << " for speed frame "
                    << param_ptr->message_metadata_timestamp;
          drop_speed_frame = recorded_frame_drop_decision;
        }
      }

      if (drop_speed_frame) {
        // msg_delay abbreviated as M
        ProactiveDropPathPostProcess("M");
        RecordAuxData(varch::protos::vnode::Frame::FRAME_DROPPED, true);
        completion_signal.set_frame_id(kInvalidFrameId);
      } else {
        assist::RemoteWarningInputs remote_warning_inputs(
            eh_for_perception_debug_list, system_state_list, early_warning_list,
            collision_detection_list, sensor_abnormality_list,
            camera_abnormality_list);
        ExecuteSpeed(param_ptr->path_planning_result,                    //
                     param_ptr->message_metadata_timestamp,              //
                     param_ptr->path_planning_start_time,                //
                     std::move(latest_assist_stuck_model_output_uptr_),  //
                     remote_warning_inputs,                              //
                     &param_ptr->input_timestamps_map,                   //
                     param_ptr->state_loaded,                            //
                     param_ptr->loaded_state.get(),                      //
                     param_ptr->state_dumped,                            //
                     param_ptr->dumped_state.get());

        STATS_HISTOGRAM(planner, planning_speed_nodelet, runtime,
                        exec_timer.GetElapsedTimeInMs());
        // Calculate latency of PlanningSpeedVNode,Only used when speed and path
        // are parallel.
        if (planner::FLAGS_planning_path_speed_are_parallel) {
          int64_t speed_vnode_execute_timestamp_ms =
              math::Ns2Ms(base::NowNs() - speed_planning_start_time);
          ProactiveDropMessageManager::GetInstance()
              .SaveSpeedVnodeExecutionTime(speed_vnode_execute_timestamp_ms);
        }

        // mark the completing of a frame for seed access.
        // if there's no snapshot, practically no state change happened in Speed
        // and Path, and no new seed version was created by Path.
        // [[maybe_unused]] auto ret = MarkFrameCommitted(SpeedCurrentFrame());
        // DCHECK(ret.ok()) << ret.message();
      }
    } else {
      completion_signal.set_frame_id(kInvalidFrameId);
    }

    // publish the speed completion signal so that path can start in a
    // serialized fashion.
    GetPublisherByName<pb::SpeedCompletionSignal>(
        GetPublisherName(
            PlannerPubTopic::planning_intra_speed_completion_signal))
        ->PublishAsync(completion_signal);
    LOG(INFO) << "Published completion signal for frame id "
              << param_ptr->message_metadata_timestamp;

    // Print out the summary of timing for this frame.
    auto param_pub_time =
        GetConsumedMessagePubTimeInfo(
            GetPlannerTopicName(
                PlannerSubTopic::planning_intra_speed_planner_param))
            .back();
    auto speed_planning_finish_time = base::NowNs();
    // clang-format off
    LOG(INFO) << fmt::format(
        "Frame {} path wait {} latency {}, speed wait {} latency {}, "
        "total {}, frame drop {}, ts:({},{},{},{},{})",
        param_ptr->message_metadata_timestamp,
        math::Ns2Ms(param_ptr->path_planning_start_time - param_ptr->snapshot_pub_time),
        math::Ns2Ms(param_ptr->path_planning_pub_speed_time - param_ptr->path_planning_start_time),
        math::Ns2Ms(speed_planning_start_time - param_pub_time),
        math::Ns2Ms(speed_planning_finish_time - speed_planning_start_time),
        math::Ns2Ms(speed_planning_finish_time - param_ptr->snapshot_pub_time),
        param_ptr->snapshot_dropped,
        math::Ns2Ms(param_ptr->snapshot_pub_time),
        math::Ns2Ms(param_ptr->path_planning_start_time),
        math::Ns2Ms(param_ptr->path_planning_pub_speed_time),
        math::Ns2Ms(speed_planning_start_time),
        math::Ns2Ms(speed_planning_finish_time));
    // clang-format on

#ifndef ONBOARD_ONLY
  });
#endif
}

void PlanningSpeedVNode::HandleSeed(
    std::list<std::shared_ptr<const pb::PlanningSeed>>& bag_seed_list,
    const FrameView& path_frame_view) {
  (void)path_frame_view;
  if (!bag_seed_list.empty() && ShouldLoadPlanningSeed()) {
    StashBagSeed(bag_seed_list);
  }
  // this function is called before updating the Speed frame view, so
  // current_frame is actually previous frame.
  auto last_speed_frame = GetSpeedFrameView().current_frame;
  if (!IsSpecialFrameId(last_speed_frame)) {
    // the frame must have been committed.
    auto ret = MarkFrameCommitted(last_speed_frame);
    if (!ret.ok()) {
      if (ret.code() == absl::StatusCode::kPermissionDenied) {
        LOG(INFO) << "Frame " << last_speed_frame
                  << " already commited in Path";
      } else {
        LOG(ERROR) << "Commit frame " << last_speed_frame
                   << " from speed failed, err " << ret.message();
      }
    }
  }
}

void PlanningSpeedVNode::LoadPlannerState(const pb::PlannerState& proto) {
  current_route_timestamp_ = proto.current_route_timestamp();
  has_published_route_completion_ = proto.has_published_route_completion();
  has_published_immediate_pullover_request_ =
      proto.has_published_immediate_pullover_request();
  need_publish_immediate_pullover_request_ =
      proto.need_publish_immediate_pullover_request();
  need_publish_cancel_immediate_pullover_request_ =
      proto.need_publish_cancel_immediate_pullover_request();

  if (proto.has_route_command()) {
    route_command_ =
        std::make_shared<routing::pb::RouteCommand>(proto.route_command());
  }

  if (proto.has_behavior_reasoner()) {
    main_speed_planner_.LoadState(proto.behavior_reasoner());
  }
}

void PlanningSpeedVNode::DumpPlannerState(pb::PlannerState& proto) {
  proto.set_has_published_route_completion(has_published_route_completion_);
  if (route_command_) {
    proto.mutable_route_command()->CopyFrom(*route_command_);
  }
  proto.set_has_published_immediate_pullover_request(
      has_published_immediate_pullover_request_);
  proto.set_need_publish_immediate_pullover_request(
      need_publish_immediate_pullover_request_);
  proto.set_need_publish_cancel_immediate_pullover_request(
      need_publish_cancel_immediate_pullover_request_);

  main_speed_planner_.DumpState(*proto.mutable_behavior_reasoner());
}

void PlanningSpeedVNode::PublishFullMessageMetadata(
    int64_t message_metadata_timestamp,
    const InputMsgTsMap& input_publish_timestamps_in_ns,
    bool drop_current_snapshot_flag) {
  // Publish.
  GetPublisherByName<pb::MessageMetadata>(
      GetPublisherName(PlannerPubTopic::planning_message_metadata))
      ->PublishAsync(ComposeMessageMetadata(
          message_metadata_timestamp, input_publish_timestamps_in_ns, false,
          drop_current_snapshot_flag, output_message_sequence_number_));
}

void PlanningSpeedVNode::ProactiveDropPathPostProcess(
    const std::string& drop_reason) {
  std::string payload;
  payload.reserve(32);
  // Speed abbreviated as S
  payload.append("S");
  payload.append(",");
  payload.append(drop_reason);
  payload.append(",");
  payload.append(std::to_string(
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>()
          .load()));
  rt_event::PostRtEvent<rt_event::planner::ProactiveFrameDrop>(payload);
}

pb::AbnormalSequenceSignal PlanningSpeedVNode::GetAbnormalSequenceSignal(
    const GlobalRouteSolution* global_route_solution,
    bool should_add_route_replan_fail_fault_code) {
  pb::AbnormalSequenceSignal abnormal_sequence_signal;
  // Add replan fail fault code if routing node sends signal or get similar
  // state from seed.
  if (has_send_route_replan_signal_ ||
      should_add_route_replan_fail_fault_code) {
    // Remove replan fail fault code if enters a new trip.
    // TODO(Wenyue): remove fault code if routing node successfully calculates
    // new route (may not be a new trip).
    if (global_route_solution && global_route_solution->is_new_trip()) {
      has_send_route_replan_signal_ = false;
      return abnormal_sequence_signal;
    }

    abnormal_sequence_signal.set_continuous_replan_fail(true);
  }

  return abnormal_sequence_signal;
}

void PlanningSpeedVNode::PublishPlannerIssueTagChangeInfo(
    const std::optional<voy::IssueTagChangeInfo>& issue_tag_change_info) {
  if (!issue_tag_change_info.has_value()) {
    return;
  }

  GetPublisherByName<voy::IssueTagChangeInfo>("planner_issue_tag_change_pub")
      ->Publish(issue_tag_change_info.value());
}

REGISTER_VNODE(PlanningPathVNode)
REGISTER_VNODE(PlanningSpeedVNode)
}  // namespace planning_vnode
}  // namespace planner
