
#include "lateral_function/lateral_function_main.h"

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Components/Rte_LatCtrlFct.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "common_math_library/debounce_method.h"
#include "common_math_library/vehicle_parameters.h"
#include "lateral_function/boundary_data_processing/boundary_data_processing.h"
#include "lateral_function/lane_center/check_lane_center.h"
#include "lateral_function/lateral_control/lqr_controller.h"
#include "lateral_function/lateral_function_type.h"

static CSI_LaneInfo_Struct *ReadLaneInfoFromCamera() {
  return Rte_IRead_BDP_60ms_Runnable_CSI_LaneInfo_CSI_LaneInfo();
}

static VSI_VehicleInfo_Struct *ReadVehicleInfoFromVehicle() {
  return Rte_IRead_FSA_60ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo();
}

static BDP_FilteredLane_Struct *ReadBoundaryDataProcessing() {
  return Rte_IrvIRead_LCT_10ms_Runnable_BDP_FilteredLane();
}

static FSA_SystemState_Struct *ReadFunctionStateAssessment() {
  return Rte_IrvIRead_LCT_10ms_Runnable_FSA_SystemState();
}

static void WriteBoundaryDataProcessing(LaneEquation center_lane) {
  BDP_FilteredLane_Struct filtered_lane = {0};

  filtered_lane.BDP_PosY0Cntr = center_lane.y0;
  filtered_lane.BDP_HeadingAngleCntr = center_lane.heading;
  filtered_lane.BDP_CurvatureCntr = center_lane.curvature;
  filtered_lane.BDP_CurvatureRateCntr = center_lane.curvature_change;
  filtered_lane.BDP_ValidLengthCntr = center_lane.length;

  Rte_IrvIWrite_BDP_60ms_Runnable_BDP_FilteredLane(&filtered_lane);
}

static void WriteFunctionStateAssessment(
    uint16_t lane_centering_invalid_bitfield) {
  FSA_SystemState_Struct system_state = {lane_centering_invalid_bitfield};

  Rte_IrvIWrite_FSA_60ms_Runnable_FSA_SystemState(&system_state);
}

static void WriteLateralControl(float steer_wheel_angle,
                                const BDP_FilteredLane_Struct *center_lane_ptr,
                                const FSA_SystemState_Struct *system_state_ptr,
                                const LqrControllerDebug *lqr_debug_ptr) {
  LAT_CtrlCmd_Struct lat_ctrl_cmd = {0};

  lat_ctrl_cmd.LAT_FrontWheelSteerAngle = steer_wheel_angle;

  // BDP debug info
  lat_ctrl_cmd.BDP_PosY0Lf = center_lane_ptr->BDP_PosY0Lf;
  lat_ctrl_cmd.BDP_HeadingAngleLf = center_lane_ptr->BDP_HeadingAngleLf;
  lat_ctrl_cmd.BDP_CurvatureLf = center_lane_ptr->BDP_CurvatureLf;
  lat_ctrl_cmd.BDP_CurvatureRateLf = center_lane_ptr->BDP_CurvatureRateLf;
  lat_ctrl_cmd.BDP_ValidLengthLf = center_lane_ptr->BDP_ValidLengthLf;
  lat_ctrl_cmd.BDP_PosY0Ri = center_lane_ptr->BDP_PosY0Ri;
  lat_ctrl_cmd.BDP_HeadingAngleRi = center_lane_ptr->BDP_HeadingAngleRi;
  lat_ctrl_cmd.BDP_CurvatureRi = center_lane_ptr->BDP_CurvatureRi;
  lat_ctrl_cmd.BDP_CurvatureRateRi = center_lane_ptr->BDP_CurvatureRateRi;
  lat_ctrl_cmd.BDP_ValidLengthRi = center_lane_ptr->BDP_ValidLengthRi;
  lat_ctrl_cmd.BDP_PosY0Cntr = center_lane_ptr->BDP_PosY0Cntr;
  lat_ctrl_cmd.BDP_HeadingAngleCntr = center_lane_ptr->BDP_HeadingAngleCntr;
  lat_ctrl_cmd.BDP_CurvatureCntr = center_lane_ptr->BDP_CurvatureCntr;
  lat_ctrl_cmd.BDP_CurvatureRateCntr = center_lane_ptr->BDP_CurvatureRateCntr;
  lat_ctrl_cmd.BDP_ValidLengthCntr = center_lane_ptr->BDP_ValidLengthCntr;
  lat_ctrl_cmd.VLP_LaneValidState = center_lane_ptr->VLP_LaneValidState;
  lat_ctrl_cmd.VLP_MeasureLaneWidth = center_lane_ptr->VLP_MeasureLaneWidth;

  // FSA debug info
  lat_ctrl_cmd.LAT_SystemStateOut = system_state_ptr->FSA_SystemStateOut;

  // LCT debug info
  lat_ctrl_cmd.LCT_ErrorDistY = lqr_debug_ptr->distance_error;
  lat_ctrl_cmd.LCT_ErrHeadingAngle = lqr_debug_ptr->heading_error;
  lat_ctrl_cmd.LCT_DistYErrorWeight = lqr_debug_ptr->distance_error_weight;
  lat_ctrl_cmd.LCT_HeadingErrorWeight = lqr_debug_ptr->heading_error_weight;
  lat_ctrl_cmd.LCT_IterationNum = lqr_debug_ptr->num_iteration;
  lat_ctrl_cmd.LCT_IterationError = lqr_debug_ptr->diff_iteration;
  lat_ctrl_cmd.LCT_SteeringAngleByLQR =
      CML_RadToDeg(lqr_debug_ptr->feedback_steer_wheel_angle);
  lat_ctrl_cmd.LCT_FeedforwardSteeringAngle =
      CML_RadToDeg(lqr_debug_ptr->feedforward_steer_wheel_angle);
  lat_ctrl_cmd.LCT_RawSteeringAngle =
      CML_RadToDeg(lqr_debug_ptr->steer_wheel_angle);
  lat_ctrl_cmd.LCT_LimitSteeringAngle =
      CML_RadToDeg(lqr_debug_ptr->limit_steer_wheel_angle);
  lat_ctrl_cmd.LCT_MaxSteeringAngleRate =
      lqr_debug_ptr->max_steer_wheel_angle_rate;
  lat_ctrl_cmd.LCT_MaxSteeringAngle = lqr_debug_ptr->max_steer_wheel_angle;

  Rte_IWrite_LCT_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd(&lat_ctrl_cmd);
}

void LatCtrlFct_Init(void) {
  LSI_LatSigInput_Struct lsi_lat_sig_input_init = {0};
  Rte_IrvIWrite_LatCtrlFct_Init_LSI_LatSigInput(&lsi_lat_sig_input_init);

  VDP_VehicleState_Struct vdp_vehicle_state_init = {0};
  Rte_IrvIWrite_LatCtrlFct_Init_VDP_VehicleState(&vdp_vehicle_state_init);

  BDP_FilteredLane_Struct bdp_filtered_lane_init = {0};
  Rte_IrvIWrite_LatCtrlFct_Init_BDP_FilteredLane(&bdp_filtered_lane_init);

  FSA_SystemState_Struct fsa_system_state_init = {0};
  Rte_IrvIWrite_LatCtrlFct_Init_FSA_SystemState(&fsa_system_state_init);

  LAT_CtrlCmd_Struct lat_ctrl_cmd_init = {0};
  Rte_IWrite_LatCtrlFct_Init_LAT_CtrlCmd_LAT_CtrlCmd(&lat_ctrl_cmd_init);
}

// todo, 20250110, will delete this module in the future
void LSI_10ms_Runnable(void) {
  LSI_LatSigInput_Struct lat_sig_input = {0};
  Rte_IrvIWrite_LSI_10ms_Runnable_LSI_LatSigInput(&lat_sig_input);
}

// todo, 20250122, will delete this module in the future
void VDP_60ms_Runnable(void) {
  VDP_VehicleState_Struct vehicle_state = {0};
  Rte_IrvIWrite_VDP_60ms_Runnable_VDP_VehicleState(&vehicle_state);
}

void BDP_60ms_Runnable(void) {
  // get camera lane info from RTE
  const CSI_LaneInfo_Struct *lane_info_ptr = ReadLaneInfoFromCamera();

  // generate center lane
  LaneEquation center_lane = LaneGeneration(lane_info_ptr);

  // write boundary data processing to RTE
  WriteBoundaryDataProcessing(center_lane);
}

void FSA_60ms_Runnable(void) {
  // get vehicle info and camera lane info from RTE
  const VSI_VehicleInfo_Struct *vehicle_info_ptr = ReadVehicleInfoFromVehicle();
  const CSI_LaneInfo_Struct *lane_info_ptr = ReadLaneInfoFromCamera();

  // check lane centering condition
  uint16_t lane_centering_invalid_bitfield =
      CheckLaneCenteringCondition(vehicle_info_ptr, lane_info_ptr);

  // write system state to RTE
  WriteFunctionStateAssessment(lane_centering_invalid_bitfield);
}

void LCT_10ms_Runnable(void) {
  // get vehicle info and camera lane info from RTE
  const VSI_VehicleInfo_Struct *vehicle_info_ptr = ReadVehicleInfoFromVehicle();
  const BDP_FilteredLane_Struct *center_lane_ptr = ReadBoundaryDataProcessing();
  const FSA_SystemState_Struct *system_state_ptr =
      ReadFunctionStateAssessment();

  // lateral control by lqr controller
  LqrControllerDebug lqr_debug = {0};
  float steer_wheel_angle = LqrController(vehicle_info_ptr, center_lane_ptr,
                                          system_state_ptr, &lqr_debug);

  // write lateral control and debug info to RTE
  WriteLateralControl(steer_wheel_angle, center_lane_ptr, system_state_ptr,
                      &lqr_debug);
}
