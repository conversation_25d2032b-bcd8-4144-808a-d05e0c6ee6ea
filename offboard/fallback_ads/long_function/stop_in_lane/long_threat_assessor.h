#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_LONG_THREAT_ASSESSOR_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_LONG_THREAT_ASSESSOR_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "_out/Appl/GenData/Rte_Type.h"

extern void LongThreatAssessor(
    const VSP_VehicleSignal_Struct* vehicle_signal_ptr,
    const OPS_ObjectList_Struct* ops_object_list_ptr,
    CDS_CollisionDetection_Struct* collision_detection_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_LONG_THREAT_ASSESSOR_H_
