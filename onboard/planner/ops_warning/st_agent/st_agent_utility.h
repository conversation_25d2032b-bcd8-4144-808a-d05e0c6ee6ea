#ifndef ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_ST_AGENT_UTILITY_H_
#define ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_ST_AGENT_UTILITY_H_

#include <vector>

#include "geometry/model/oriented_box.h"
#include "geometry/model/polyline_curve.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
namespace ops_warning {

// STAgentParam includes necessary parameters for building STAgent
struct STAgentParam {
  STAgentParam(const pb::Trajectory& agent_trajectory, double agent_width,
               double agent_length)
      : trajectory(agent_trajectory),
        width(agent_width),
        length(agent_length) {}
  pb::Trajectory trajectory;
  // The width of agent.
  double width = 0.0;
  // The length of agent.
  double length = 0.0;
};

struct OdomSampleInfo {
  OdomSampleInfo(int64_t time, double acc_value)
      : end_time(time), accel(acc_value) {}
  // The end time of current acceleration.
  int64_t end_time = 0;
  // The value of acceleration.
  double accel = 0.0;
};

// STInfo includes the space info at corresponding time.
struct STInfo {
  STInfo(const math::geometry::OrientedBox2d& box, int64_t timestamp)
      : space(box), time(timestamp) {}
  math::geometry::OrientedBox2d space;
  int64_t time = 0;
};

// Transforms the predicted trajectory to STAgentParam.
STAgentParam TransformSTAgentParam(
    const PredictedTrajectoryWrapper& predicted_trajectory);

// Samples the odoms by fixed accelerations.
std::vector<double> GetSampledOdomsByConstAcc(
    double speed, const std::vector<OdomSampleInfo>& acc_time_pair_vec,
    int64_t resolution_ms, double odom_offset);

std::vector<pb::TrajectoryPose> InterpolatePosesByOdom(
    const math::geometry::PolylineCurve2d& trajectory_curve,
    const std::vector<double>& sampled_odoms);

// Transforms the trajectory pose to box.
void TransformTrajectoryPoseToBox(
    const STAgentParam& param,
    const std::vector<pb::TrajectoryPose>& trajectory_poses,
    std::vector<STInfo>* st_info_vec_ptr);

}  // namespace ops_warning
}  // namespace planner
#endif  // ONBOARD_PLANNER_OPS_WARNING_ST_AGENT_ST_AGENT_UTILITY_H_
