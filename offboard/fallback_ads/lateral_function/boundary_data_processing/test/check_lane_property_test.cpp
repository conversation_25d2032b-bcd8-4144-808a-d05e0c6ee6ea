#include "lateral_function/boundary_data_processing/check_lane_property.h"

#include <gtest/gtest.h>

TEST(CheckLaneRadius, CheckLaneRadiusTest) {
  float left_lane_curvature = 0.01F;
  float right_lane_curvature = 0.01F;

  // the curvature radius is too small
  ASSERT_FALSE(CheckLaneRadius(left_lane_curvature, right_lane_curvature));

  // the curvature radius is valid
  left_lane_curvature = 0.0039F;
  right_lane_curvature = 0.0039F;
  ASSERT_TRUE(CheckLaneRadius(left_lane_curvature, right_lane_curvature));
}

TEST(CheckLaneLength, CheckLaneLengthTest) {
  float left_lane_length = 10.0F;
  float right_lane_length = 10.0F;

  ASSERT_FALSE(CheckLaneLength(left_lane_length, right_lane_length));

  left_lane_length = 15.1F;
  right_lane_length = 15.1F;
  ASSERT_TRUE(CheckLaneLength(left_lane_length, right_lane_length));
}

TEST(<PERSON><PERSON><PERSON>Width, CheckLaneWidthTest) {
  float left_lane_y0 = 1.75F;
  float left_lane_heading = -0.01F;
  float right_lane_y0 = -1.75F;
  float right_lane_heading = 0.01F;

  ASSERT_TRUE(CheckLaneWidth(left_lane_y0, left_lane_heading, right_lane_y0,
                             right_lane_heading));

  left_lane_y0 = 2.75F;
  right_lane_y0 = -2.75F;
  ASSERT_FALSE(CheckLaneWidth(left_lane_y0, left_lane_heading, right_lane_y0,
                              right_lane_heading));
}

TEST(CheckDistanceToLane, CheckDistanceToLaneTest) {
  float left_lane_y0 = 1.75F;
  float right_lane_y0 = -1.75F;

  ASSERT_TRUE(CheckDistanceToLane(left_lane_y0, right_lane_y0));

  left_lane_y0 = 0.75F;
  right_lane_y0 = -2.75F;
  ASSERT_FALSE(CheckDistanceToLane(left_lane_y0, right_lane_y0));
}

TEST(CheckLaneInCaptureRange, CheckLaneInCaptureRangeTest) {
  float left_lane_y0 = 1.75F;
  float left_lane_heading = 0.01F;
  float right_lane_y0 = -1.76F;
  float right_lane_heading = 0.01F;

  ASSERT_TRUE(CheckLaneInCaptureRange(left_lane_y0, left_lane_heading,
                                      right_lane_y0, right_lane_heading));

  right_lane_y0 = -1.74F;
  ASSERT_FALSE(CheckLaneInCaptureRange(left_lane_y0, left_lane_heading,
                                       right_lane_y0, right_lane_heading));
}

TEST(CheckLaneMerge, CheckLaneMergeTest) {
  // issue cn15600271, timestamp: 1735277488888
  CSI_LaneInfo_Struct lane_info = {};
  lane_info.CSI_PosY0Lf = 1.78125F;
  lane_info.CSI_HeadingLf = -0.0238F;
  lane_info.CSI_CurvatureLf = 0.000095F * 2.0F;
  lane_info.CSI_CrvRateLf = 0.00000712F * 6.0F;
  lane_info.CSI_PosXEndLf = 33.25F;
  lane_info.CSI_PosY0Ri = -1.546875F;
  lane_info.CSI_HeadingRi = -0.035F;
  lane_info.CSI_CurvatureRi = 0.00359F * 2.0F;
  lane_info.CSI_CrvRateRi = 0.0000084F * 6.0F;
  lane_info.CSI_PosXEndRi = 34.5F;

  ASSERT_TRUE(CheckLaneMerge(&lane_info));
}
