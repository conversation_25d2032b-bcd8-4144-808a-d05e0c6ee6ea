#include "planner/utility/trajectory_manipulation/trajectory_manipulation_util.h"

#include <tuple>
#include <unordered_map>
#include <vector>

#include "geometry/model/oriented_box.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"

namespace planner {
namespace {

// The spatial dilation buffer for generating the bounding box from pose.
constexpr double kDilationBufferInMeter = 0.1;

}  // namespace

math::geometry::OrientedBox2d GetBoundingBoxFromTrajectoryPose(
    const pb::TrajectoryPose& pose,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement) {
  const double wheelbase_center_to_geometric_center =
      0.5 * (shape_measurement.front_bumper_to_front_axle() -
             shape_measurement.rear_bumper_to_rear_axle());
  const double length = shape_measurement.length();
  const double width = shape_measurement.width();

  const double heading = pose.heading();
  const double x =
      pose.x_pos() + (cos(heading) * wheelbase_center_to_geometric_center);
  const double y =
      pose.y_pos() + (sin(heading) * wheelbase_center_to_geometric_center);
  const math::geometry::OrientedBox2d contour_box(
      x, y, length + (kDilationBufferInMeter * 2.0),
      width + (kDilationBufferInMeter * 2.0), heading);

  return contour_box;
}

std::optional<CollisionSnapshotInfo> GetCollisionInfoFromPredictedTrajectory(
    const pb::Trajectory& last_trajectory,
    const std::vector<math::geometry::OrientedBox2d>& last_traj_bounding_boxes,
    const PredictedTrajectoryInterpolator& agent_interpolated_trajectory) {
  const int poses_size = last_trajectory.poses_size();
  for (int i = 0; i < poses_size; ++i) {
    const int64_t query_timestamp = last_trajectory.poses(i).timestamp();
    const std::optional<math::geometry::OrientedBox2d> agent_predicted_box =
        agent_interpolated_trajectory.GetInterpolatedBoundingBox(
            static_cast<double>(query_timestamp));
    const math::geometry::OrientedBox2d& ego_bounding_box =
        last_traj_bounding_boxes.at(i);
    if (agent_predicted_box &&
        ego_bounding_box.Intersect(*agent_predicted_box)) {
      // Return the first collision snapshot parameters.
      return std::make_optional(CollisionSnapshotInfo(
          query_timestamp, ego_bounding_box, agent_predicted_box.value()));
    }
  }

  return std::nullopt;
}

std::tuple<int64_t, bool> HasCollisionWithPredictedTrajectories(
    int64_t timestamp, const math::geometry::OrientedBox2d& ego_bounding_box,
    const std::vector<const PredictedTrajectoryWrapper*>&
        predicted_trajectory_wrapper_collector) {
  const double query_timestamp = static_cast<double>(timestamp);

  for (const auto& predicted_trajectory_wrapper :
       predicted_trajectory_wrapper_collector) {
    const std::optional<math::geometry::OrientedBox2d> predicted_box =
        predicted_trajectory_wrapper->GetInterpolatedBoundingBox(
            query_timestamp);
    if (predicted_box && ego_bounding_box.Intersect(*predicted_box)) {
      return {predicted_trajectory_wrapper->object_id(), true};
    }
  }
  return {-1, false};
}

std::tuple<int64_t, bool> HasCollisionWithObjects(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::Trajectory& trajectory,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const math::Range<int>& evaluation_range) {
  // Generate interpolated predicted pose for every predicted trajectory for
  // each tracked object.
  std::vector<const PredictedTrajectoryWrapper*>
      predicted_trajectory_wrapper_collector;
  for (const auto& [_, planner_object] : planner_object_map) {
    for (const auto* predicted_trajectory :
         planner_object.predicted_trajectories(
             /*use_filtered_trajectories=*/true)) {
      predicted_trajectory_wrapper_collector.emplace_back(predicted_trajectory);
    }
  }

  std::tuple<int64_t, bool> collision_event = {-1, false};

  for (int i = evaluation_range.start_pos; i < evaluation_range.end_pos; ++i) {
    const auto& pose = trajectory.poses(i);
    const math::geometry::OrientedBox2d ego_box =
        GetBoundingBoxFromTrajectoryPose(pose, shape_measurement);
    collision_event = HasCollisionWithPredictedTrajectories(
        pose.timestamp(), ego_box, predicted_trajectory_wrapper_collector);
    if (std::get<1>(collision_event)) {
      return collision_event;
    }
  }
  return {-1, false};
}

}  // namespace planner
