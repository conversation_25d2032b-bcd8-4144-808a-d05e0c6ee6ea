#include "planner/speed/util/precedence/road_precedence_util.h"

#include <algorithm>
#include <iterator>
#include <optional>
#include <unordered_set>

#include <absl/strings/str_cat.h>

#include "geometry/algorithms/distance.h"
#include "geometry/algorithms/intersects.h"
#include "hdmap_protos/lane.pb.h"
#include "hdmap_protos/signal.pb.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"
#include "planner/planning_gflags.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/reasoner/traffic_light_reasoner/traffic_light_reasoner_util.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning/route_association_util.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_info.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_traffic_rule.h"
#include "planner/speed/util/precedence/matching_conflicting_lanes.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/map_elements/zone.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace util {
using RoadPrecedence = planner::pb::RoadPrecedence;
using PrecedenceSource = planner::pb::PrecedenceSource;
using PrecedenceSceneType = planner::pb::PrecedenceSceneType;
using EncroachmentSceneType = planner::pb::EncroachmentSceneType;
namespace {

// The maximum relative heading between exit zone and agent for an agent to be
// considered as exiting from a zone.
constexpr double kMaxHeadingDiffForRoadExitVehiclesInRad = M_PI / 3.0;
// The maximum squared distance between exit zone and agent for an agent to be
// considered as exiting from a zone.
constexpr double kMaxSquaredDistanceForRoadExitVehiclesInSquaredMeter =
    9.0;  // 3.0 * 3.0 m
// The maximum abs lateral gap between ego and agent for an agent to be
// considered as parallel straight through a junction.
[[maybe_unused]] constexpr double kMaxAbsLateralGapForParallelStraightInMeter =
    2.0;
// The maximum relative heading between ego and agent for an agent to be
// considered as parallel straight through a junction.
[[maybe_unused]] constexpr double kMaxHeadingDiffForForParallelStraightInRad =
    M_PI / 6.0;
// Minimum direction diff for a lower right of way merge.
constexpr double kMinDirectionDiffForMerging = math::Degree2Radian(5.0);
constexpr int kPoseSamplingInterval = 18;
// The maximum number of candidate overlapped lanes to examine.
constexpr size_t kMaxNumberOfLanesForCandidateOverlapOfInterest = 3UL;
// The threshold to determine oncoming agents in XL-nudge scenarios.
constexpr double kXLNudgePrecedenceOncomingThresholdInRad = M_PI * 2 / 3;
// The absolute tilt angle difference threshold for precedence inference of
// straight merging in junction.
constexpr double kTiltAngleDiffThresholdForStraightMergeInferenceInRad =
    math::Degree2Radian(2.5);
// The threshold not to consider encroachment while ego is stepping on lane
// marking and nudge back to lane.
constexpr double kSteppingMarkingEncroachmentDistThresholdInMeter = 1.0;

struct ZoneOfInterest {
  const pnc_map::Zone* zone_ptr = nullptr;
  const bool is_ego_in_the_zone = false;
};

// Returns true if there is a yield sign associated with the lane.
bool HasYieldSign(const pnc_map::Lane* lane,
                  const std::vector<traffic_rules::TrafficSignInLaneSequence>&
                      traffic_signs) {
  return std::any_of(
      traffic_signs.begin(), traffic_signs.end(),
      [&lane](const traffic_rules::TrafficSignInLaneSequence& traffic_sign) {
        return lane->id() == traffic_sign.controlled_lane->id() &&
               traffic_sign.traffic_sign_ptr->type() ==
                   pnc_map::TrafficSignType::kYieldToVehicle;
      });
}

// If the overlap region has intersection with a lane in terms of arclength, we
// deem the lane as OverlappedLanes.
// NOTE: Currently we only consider first three overlapped lanes as candidates
// for further examination to save computation load.
std::vector<const pnc_map::Lane*> GetCandidateOverlappedLanes(
    const TrajectoryInfo& ego_trajectory_info,
    const std::vector<std::pair<int64_t, math::Range<double>>>&
        ego_lane_sequence_id_ra_arclength_ranges,
    const std::vector<std::reference_wrapper<const pb::OverlapRegion>>&
        overlap_regions,
    const double ra_to_front_bumper_shift,
    const double ra_to_rear_bumper_shift) {
  std::vector<const pnc_map::Lane*> candidate_overlapped_lanes_of_interest;
  if (overlap_regions.empty()) {
    return candidate_overlapped_lanes_of_interest;
  }

  [[maybe_unused]] const pnc_map::Lane* current_ego_lane =
      *ego_trajectory_info.lane_sequence_iterator().current_lane();

  for (int idx = 0; idx < ego_trajectory_info.lane_sequence_iterator().size();
       ++idx) {
    const pnc_map::Lane* lane =
        ego_trajectory_info.lane_sequence_iterator().lane_sequence()[idx];
    // Only consider lanes at present or in the future.
    if (!ego_trajectory_info.lane_sequence_iterator().IsOrAfterCurrentLane(
            *lane)) {
      continue;
    }
    DCHECK(lane->id() == ego_lane_sequence_id_ra_arclength_ranges[idx].first)
        << "Lane ID differs.";
    const math::Range<double>& lane_ra_arclength_range =
        ego_lane_sequence_id_ra_arclength_ranges[idx].second;

    // Iterate through all overlap regions to check if any of them overlaps with
    // the lane. Store the lane if yes.
    const auto matched_overlap_region_iter = std::find_if(
        overlap_regions.begin(), overlap_regions.end(),
        [&lane_ra_arclength_range, ra_to_front_bumper_shift,
         ra_to_rear_bumper_shift](const pb::OverlapRegion& overlap_region) {
          const double overlap_start_arclength =
              GetPaddedOverlapStart(overlap_region) + ra_to_front_bumper_shift;
          const double overlap_end_arclength =
              GetPaddedOverlapEnd(overlap_region) + ra_to_rear_bumper_shift;
          if (overlap_start_arclength < 0.0 && overlap_end_arclength < 0.0) {
            return false;
          }
          return std::max(overlap_start_arclength,
                          lane_ra_arclength_range.start_pos) <=
                 std::min(overlap_end_arclength,
                          lane_ra_arclength_range.end_pos);
        });
    // Skip if no matched.
    if (matched_overlap_region_iter == overlap_regions.end()) {
      continue;
    }
    // Restrict the number of lanes to examine for reducing computation load, as
    // the first few overlapped lanes matter most.
    if (candidate_overlapped_lanes_of_interest.size() <
        kMaxNumberOfLanesForCandidateOverlapOfInterest) {
      candidate_overlapped_lanes_of_interest.push_back(lane);
    } else {
      break;
    }
  }
  return candidate_overlapped_lanes_of_interest;
}

// Util function to check if a lane is zip merging.
bool IsZipMergingLane(const pnc_map::Lane* lane) {
  if (lane->IsInJunction()) {
    return false;
  }
  const std::vector<pnc_map::BrotherLane>& brother_lanes =
      lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge);
  const bool has_zip_merging_brother = std::any_of(
      brother_lanes.begin(), brother_lanes.end(),
      [lane](const pnc_map::BrotherLane& brother) {
        return reasoning_util::HasSamePredecessorSection(brother.lane(), lane);
      });
  return has_zip_merging_brother;
}

// Util to find the ego's first overlapped lane with agent trajectory associated
// lane sequence.
const pnc_map::Lane* FindEgoFirstOverlappedLaneWithAgent(
    const std::vector<const pnc_map::Lane*>&
        candidate_overlapped_lanes_of_interest,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_lanes) {
  for (const pnc_map::Lane* ego_lane : candidate_overlapped_lanes_of_interest) {
    for (const route_association::MapElementAndPoseInfo& agent_lane :
         agent_lanes) {
      if (agent_lane.lane_ptr == nullptr) {
        continue;
      }
      // Same lane.
      if (ego_lane->id() == agent_lane.element_id) {
        return ego_lane;
      }
      // Conflicting lane.
      const bool is_conflicting = std::any_of(
          ego_lane->conflicting_lanes().begin(),
          ego_lane->conflicting_lanes().end(),
          [&agent_lane](const pnc_map::ConflictingLane& conflicting_lane) {
            return conflicting_lane.lane()->id() == agent_lane.element_id;
          });
      if (is_conflicting) {
        return ego_lane;
      }
      // Merging lane.
      const std::vector<pnc_map::BrotherLane>& brothers =
          ego_lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge);
      const bool is_merging =
          std::any_of(brothers.begin(), brothers.end(),
                      [&agent_lane](const pnc_map::BrotherLane& brother) {
                        return brother.lane()->id() == agent_lane.element_id &&
                               !brother.lane()->IsInJunction();
                      });
      if (is_merging) {
        return ego_lane;
      }
    }
  }
  return nullptr;
}

// Find the overlapped lane of interest in ego's candidate overlapped lanes.
// Note: We currently only examining candidates when there are any merging
// lanes, otherwise return the first overlapped lane as the one of interest.
// TODO(minhanli): Generalize the logic to more generic scenarios when lane
// association is broadly used.
const pnc_map::Lane* GetOverlappedLaneOfInterest(
    const ReasoningObject& reasoning_object,
    const std::vector<const pnc_map::Lane*>&
        candidate_overlapped_lanes_of_interest,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  const pnc_map::Lane* lane_of_interest = nullptr;
  // Check if any of candidates is zip merging lane.
  const bool has_zip_merging_overlapped_lane =
      std::any_of(candidate_overlapped_lanes_of_interest.begin(),
                  candidate_overlapped_lanes_of_interest.end(),
                  [](const pnc_map::Lane* candidate_overlapped_lane) {
                    return IsZipMergingLane(candidate_overlapped_lane);
                  });
  if (has_zip_merging_overlapped_lane &&
      reasoning_object.is_vehicle_or_cyclist()) {
    const pnc_map::Lane* lane_overlapped_with_agent = nullptr;

    const std::vector<route_association::MapElementAndPoseInfo>&
        agent_lane_ids =
            agent_associated_route_opt.has_value()
                ? agent_associated_route_opt.value()
                : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                      reasoning_object, predicted_trajectory,
                      joint_pnc_map_service,
                      /*allow_using_for_cyclist=*/true);
    lane_overlapped_with_agent = FindEgoFirstOverlappedLaneWithAgent(
        candidate_overlapped_lanes_of_interest, agent_lane_ids);

    lane_of_interest = lane_overlapped_with_agent != nullptr
                           ? lane_overlapped_with_agent
                           : candidate_overlapped_lanes_of_interest.front();
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Lane association. "));
  } else {
    lane_of_interest = candidate_overlapped_lanes_of_interest.front();
  }
  return lane_of_interest;
}

// Computes absolute tile angle between given entrance and exit lanes. The angle
// is formed by the tangent vector of entrance lane's end point and the vector
// from entrance lane's end point to the exit lane's start point.
double ComputeTiltAngleBetweenEntranceAndExitLanes(
    const pnc_map::Lane* entrance_lane, const pnc_map::Lane* exit_lane) {
  const math::geometry::Point2d entrance_exit_direction =
      math::geometry::Subtract(exit_lane->center_line().GetStartPoint(),
                               entrance_lane->center_line().GetEndPoint());
  const double entrance_exit_angle =
      std::atan2(entrance_exit_direction.y(), entrance_exit_direction.x());
  const double entrance_end_heading_angle = entrance_lane->GetLaneDirection(
      entrance_lane->center_line().GetEndPoint());
  const double abs_tilt_angle = std::abs(
      math::AngleDiff(entrance_exit_angle, entrance_end_heading_angle));
  return abs_tilt_angle;
}

// Gets a precedence result by additional inference in case the original result
// purely based on map labeling does not align with the social norm of
// human-driving.
// NOTE: So far we only do inference for straight merge in junction where the
// original road precedence of the agent is lower.
RoadPrecedenceResult GetInferredPrecedenceResult(
    const RoadPrecedenceResult& original_result,
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  const planner::pb::PrecedenceInferenceSource::Enum inference_source =
      GetPrecedenceInferenceSource(conflicting_ego_lane, trajectory_info,
                                   reasoning_object, agent_route,
                                   joint_pnc_map_service, debug_string_ptr);
  if (inference_source ==
      planner::pb::PrecedenceInferenceSource::StraightMergeInJunction) {
    const InferredPrecedenceInfo inferred_precedence_info =
        GetInferredPrecedenceInfoForStraightMergeInJunction(
            conflicting_ego_lane, trajectory_info, reasoning_object,
            agent_route, joint_pnc_map_service, debug_string_ptr);
    if (inferred_precedence_info
            .agent_entrance_lane_for_straight_merge_in_junction != nullptr) {
      const double ego_abs_tilt_angle =
          ComputeTiltAngleBetweenEntranceAndExitLanes(
              inferred_precedence_info
                  .ego_entrance_lane_for_straight_merge_in_junction,
              inferred_precedence_info
                  .ego_exit_lane_for_straight_merge_in_junction);
      const double agent_abs_tilt_angle =
          ComputeTiltAngleBetweenEntranceAndExitLanes(
              inferred_precedence_info
                  .agent_entrance_lane_for_straight_merge_in_junction,
              inferred_precedence_info
                  .ego_exit_lane_for_straight_merge_in_junction);
      const double tilt_angle_diff = ego_abs_tilt_angle - agent_abs_tilt_angle;
      if (debug_string_ptr != nullptr) {
        absl::StrAppend(
            debug_string_ptr,
            strings::StringPrintf("ET :%4.2f, AT :%4.2f", ego_abs_tilt_angle,
                                  agent_abs_tilt_angle));
      }
      RoadPrecedenceResult inferred_result = original_result;
      inferred_result.inferred_precedence_info = inferred_precedence_info;
      if (tilt_angle_diff >
          kTiltAngleDiffThresholdForStraightMergeInferenceInRad) {
        inferred_result.road_precedence = RoadPrecedence::HIGHER;
      } else if (tilt_angle_diff > 0.0) {
        inferred_result.road_precedence = RoadPrecedence::EQUAL;
      } else {
        inferred_result.road_precedence = RoadPrecedence::LOWER;
      }
      return inferred_result;
    }
  }
  return original_result;
}

// Examines if the agent goes through a lane of interest in junction.
// NOTE: This function demands a high computation load, call it only
// when really necessary. Currently it is used in unprotected turning and
// junction without any traffic signs. It may also fail for lane change cases.
// To speed up searching for junction with traffic light, we sometimes only
// care about part of conflicting lanes that ego has to yield to. Thus, an input
// argument |has_to_be_yielding_lane| indicates whether to restrict examination
// to those lanes.
bool IsAgentAlongLaneOfInterestInJunction(
    const pnc_map::Lane* lane,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    bool has_to_be_yielding_lane,
    const std::unordered_set<hdmap::Lane::Turn>& turn_type_of_interest) {
  DCHECK(lane->IsInJunction()) << "The queried lane is not in junction";
  // Find all lanes in junction to which the queried |lane| needs to yield.
  std::vector<const pnc_map::Lane*> conflicting_lanes_of_interest;
  conflicting_lanes_of_interest.reserve(lane->conflicting_lanes().size());
  for (const pnc_map::ConflictingLane& conflicting_lane :
       lane->conflicting_lanes()) {
    if (turn_type_of_interest.find(
            DCHECK_NOTNULL(conflicting_lane.lane())->turn()) !=
        turn_type_of_interest.end()) {
      if (has_to_be_yielding_lane && conflicting_lane.hdmap_conflict_type() !=
                                         hdmap::ConflictLane::YIELD_LANE) {
        continue;
      }
      conflicting_lanes_of_interest.push_back(conflicting_lane.lane());
    }
  }
  // Down sampling the agent trajectory poses.
  const std::vector<int> sampling_indices =
      ComputeSamplingIndexForPredictedTrajectory(
          predicted_trajectory,
          static_cast<int>(
              std::ceil(predicted_trajectory.size() / kPoseSamplingInterval)),
          /*min_required_odom_progress_in_meter=*/0.0);

  std::vector<TrafficParticipantPose> downsampled_trajectory_poses;
  downsampled_trajectory_poses.reserve(sampling_indices.size());
  for (int idx : sampling_indices) {
    downsampled_trajectory_poses.push_back(TrafficParticipantPose(
        predicted_trajectory.tracked_object(), predicted_trajectory.pose(idx)));
  }

  return std::any_of(conflicting_lanes_of_interest.begin(),
                     conflicting_lanes_of_interest.end(),
                     [&downsampled_trajectory_poses](
                         const pnc_map::Lane* conflicting_lane_of_interest) {
                       return util::IsTrajectoryPosesAlongLane(
                           downsampled_trajectory_poses,
                           conflicting_lane_of_interest);
                     });
}

// Check if agent is along with ego in right turn with no traffic light
bool IsAgentAboutToRightTurnAlongWithEgo(
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  // Check whether agent has same entrance with ego
  if (reasoning_object.agent_occupied_map_element_infos_in_seed_ptr() !=
          nullptr &&
      reasoning_object.agent_occupied_map_element_infos_in_seed_ptr()
          ->has_latest_occupied_isolated_lane()) {
    // If the agent is already in the junction, find the agent entrance lane
    // from the seed.
    std::vector<const pnc_map::Lane*> agent_lane_sequence =
        joint_pnc_map_service.GetLaneSequence(
            {reasoning_object.agent_occupied_map_element_infos_in_seed_ptr()
                 ->latest_occupied_isolated_lane()
                 .element_id()});
    if (!agent_lane_sequence.empty() &&
        !conflicting_ego_lane->predecessors().empty()) {
      const pnc_map::Lane* agent_entrance_lane = agent_lane_sequence.front();
      if ((agent_entrance_lane != nullptr) &&
          (conflicting_ego_lane->predecessors().front() != nullptr) &&
          (conflicting_ego_lane->predecessors().front()->section()->id() ==
           agent_entrance_lane->section()->id())) {
        absl::StrAppend(
            debug_string_ptr,
            strings::StringPrintf("agent has same entrance with ego, "));
        return true;
      }
    }
  }
  // Check whether agent has same associated route with ego
  const traffic_rules::JunctionInLaneSequence* first_junction_ahead_ptr =
      GetFirstJunctionAhead(
          trajectory_info.traffic_rules().lane_sequence_junctions);
  const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
      predicted_trajectory.associated_route_opt().has_value()
          ? predicted_trajectory.associated_route_opt().value()
          : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                reasoning_object, predicted_trajectory, joint_pnc_map_service,
                /*allow_using_for_cyclist=*/true);
  if ((first_junction_ahead_ptr != nullptr) &&
      route_association_util::IsAgentTurningAlongWithEgoInJunction(
          first_junction_ahead_ptr->ego_lane_in_junction_ptr, agent_route)) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("same associated route, "));
    return true;
  }
  return false;
}

// Check if we can treat a traffic light along ego's lane sequence the same way
// as if it was green. Return true if (1) current color is green; (2) current
// color is not green but ego rear axle has passed the point of no return.
bool IsTrafficLightColorConditionallyConsideredAsGreen(
    const pnc_map::Lane* ego_lane,
    const traffic_rules::TrafficLightInfo& traffic_signal,
    const TrajectoryInfo& ego_trajectory_info, std::string* debug_string_ptr) {
  if (traffic_signal.is_green()) {
    return true;
  }

  // Check if the ego has entered the junction or not.
  const bool ego_has_entered_junction_of_interest =
      ego_trajectory_info.IsEgoInJunction() &&
      (*ego_trajectory_info.lane_sequence_iterator().current_lane())->id() ==
          ego_lane->id();

  // TODO(minhanli): fix this bug
  const bool is_using_watch_line =
      traffic_signal.watch_line_ra_arclength().has_value() &&
      ego_has_entered_junction_of_interest;

  const pb::TrafficLightSpatialInfo& traffic_light_spatial_info =
      reasoner::GetTrafficLightSpatialInfo(
          traffic_signal,
          ego_trajectory_info.traffic_rules().lane_sequence_conflicting_lanes,
          ego_trajectory_info.traffic_rules().crosswalks, is_using_watch_line);

  if (traffic_light_spatial_info.no_return_lane_ra_arclength() < 0.0) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("NoReturn. "));
    return true;
  }
  return false;
}

bool IsAgentTrajectoryLaneKeepWhenEgoEncroaching(
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& candidate_encroached_lanes,
    const bool consider_virtual_as_lane_keep = false) {
  if (candidate_encroached_lanes.empty()) {
    return false;
  }
  // So far, there will be only one candidate if any.
  DCHECK_EQ(candidate_encroached_lanes.size(), 1);
  const pnc_map::Lane* encroached_lane = candidate_encroached_lanes.front();
  // Get agent's route and check if it drives through the lane being encroached.
  const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
      agent_associated_route_opt.has_value()
          ? agent_associated_route_opt.value()
          : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                reasoning_object, predicted_trajectory, joint_pnc_map_service,
                /*allow_using_for_cyclist=*/true);

  return route_association_util::IsAgentDoingLaneKeepUntilTargetLane(
      encroached_lane, agent_route, consider_virtual_as_lane_keep);
}

bool IsEgoSteppingOnMarkingNotConsideredCrossingLane(
    const OverlapRegionInfo& overlap_region_info,
    const TrajectoryInfo& trajectory_info, const pnc_map::Lane* ego_lane) {
  // Early return |false| if ego is not stepping on lane marking or the overlap
  // region encroachment side is not same as the crossing direction.
  if (trajectory_info.direction_cross_onto_lane_marking() == math::pb::kOn ||
      overlap_region_info.encroachment_side !=
          trajectory_info.direction_cross_onto_lane_marking()) {
    return false;
  }
  if (!overlap_region_info.encroachment_segment_opt.has_value()) {
    return false;
  }
  // Early return |false| if the distance by which ego encroaches back to the
  // lane is greater than the lane width, indicating a multiple-lane
  // encroachment.
  const pb::PathOutOfLaneInfo* in_lane_encroachment_info_ptr =
      trajectory_info.in_lane_encroachment_info_ptr();
  if (in_lane_encroachment_info_ptr != nullptr &&
      !in_lane_encroachment_info_ptr->lateral_encroachment_distance().empty() &&
      in_lane_encroachment_info_ptr->lateral_encroachment_distance().at(0) >
          ego_lane->avg_sampled_width()) {
    return false;
  }
  // Early return |true| if there is no encroachment out of the lane, indicating
  // ego won't go out of the lane.
  const pb::PathOutOfLaneInfo* out_lane_encroachment_info_ptr =
      trajectory_info.out_lane_encroachment_info_ptr();
  if (out_lane_encroachment_info_ptr == nullptr ||
      out_lane_encroachment_info_ptr->lateral_encroachment_distance().empty()) {
    DCHECK(out_lane_encroachment_info_ptr != nullptr);
    return true;
  }
  // TODO(minhanli): Refine the logic by checking if the queried
  // lateral_encroachment_distance occurs ahead of the overlap region.
  return reasoning_util::GetEgoTrajectoryMaxOutLaneDistSoFarBeforeArclength(
             trajectory_info,
             overlap_region_info.encroachment_segment_opt.value().start_pos) <
         kSteppingMarkingEncroachmentDistThresholdInMeter;
}

// Get the encroachment scene type, which determines whether we want assign
// |PrecedenceSource::InEgoEncroachment| as the primary precedence scene and get
// the final precedence. If the encroachment scene type is
// |EgoEncroachingOnly|, we directly assign it to
// |PrecedenceSource::InEgoEncroachment|. If it is |EgoAgentBothEncroaching| or
// |EgoSteppingOnMarkingNotCrossingLane|, other primary road-structure related
// precedence scenes will handle it to get the road-structure related precedence
// result, and we will correct the precedence to |EQUAL| when necessary.
// Otherwise, directly using the precedence result obtained from the
// road-structure related precedence scenes. We currently only consider three
// following scenes as |EgoEncroachingOnly|. (1) Ego's doing XL nudge into the
// opposite lane; (2) Ego's doing in-lane nudge but encroaching the opposite
// lane, and oncoming overlap region falls into the same side of encroachment;
// (3) Ego nudges into other lane and the agent behind ego front bumper is on
// the same side of encroachment; (4) Ego does LC while agent is doing LK.
EncroachmentSceneType::Enum GetEncroachmentSceneType(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<std::reference_wrapper<const pb::OverlapRegion>>&
        overlap_regions,
    const OverlapRegionInfo& first_overlap_region_info,
    const pnc_map::Lane* lane,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (first_overlap_region_info.is_in_encroachment_regions()) {
    DCHECK(!overlap_regions.empty());
    const bool is_left_encroach =
        first_overlap_region_info.encroachment_side == math::pb::Side::kLeft;
    const bool is_agent_on_the_same_side_of_encroachment =
        reasoning_object.out_of_extended_path_range()
            ? (is_left_encroach ? overlap_regions.front()
                                          .get()
                                          .overlap_slices()
                                          .begin()
                                          ->signed_lateral_gap() > 0.0
                                : overlap_regions.front()
                                          .get()
                                          .overlap_slices()
                                          .begin()
                                          ->signed_lateral_gap() < 0.0)
            : (is_left_encroach
                   ? object_proximity_info.signed_lateral_gap() > 0.0
                   : object_proximity_info.signed_lateral_gap() < 0.0);

    const bool is_ego_stepping_on_marking_not_crossing_lane =
        IsEgoSteppingOnMarkingNotConsideredCrossingLane(
            first_overlap_region_info, trajectory_info, lane);
    if (is_agent_on_the_same_side_of_encroachment &&
        (reasoning_object.IsBehindEgoLeadingBumper() ||
         (!reasoning_object.IsFullyAhead() &&
          reasoning_object.is_driving_ahead_in_same_section_along_ego()))) {
      if (is_ego_stepping_on_marking_not_crossing_lane) {
        absl::StrAppend(debug_string_ptr, "Stepping Marking.");
        return EncroachmentSceneType::EgoSteppingOnMarkingNotCrossingLane;
      }
      // We currently only consider both agent and ego are encroaching into the
      // same region under the scoped scenes: 1. Ego's lane at which the
      // encroachment occurs is of a non-virtual type. 2. Agent's lane
      // association has a result and is not going through any virtual lanes.
      const bool is_target_scene_considering_both_encroaching =
          (lane->turn() == hdmap::Lane_Turn_STRAIGHT ||
           lane->type() == hdmap::Lane_LaneType_REGULAR) &&
          !lane->IsInJunction();
      const bool is_not_veh_nor_having_la_result =
          !agent_associated_route_opt.has_value() &&
          !reasoning_object.is_vehicle();
      if (!is_target_scene_considering_both_encroaching ||
          is_not_veh_nor_having_la_result) {
        absl::StrAppend(debug_string_ptr, "Not Both Encroach Scene.");
        return EncroachmentSceneType::EgoEncroachingOnly;
      }
      const std::vector<const pnc_map::Lane*> candidate_encroached_lanes =
          reasoning_util::GetCandidateLanesEncroachedByEgo(
              trajectory_info, first_overlap_region_info);
      // Unable to find the encroached lanes, we will not handle it in short
      // term in case of FN.
      if (candidate_encroached_lanes.empty()) {
        absl::StrAppend(debug_string_ptr, "Empty Encroached Lane.");
        return EncroachmentSceneType::EgoEncroachingOnly;
      }
      const bool is_left_marking_all_virtual =
          lane->type() == hdmap::Lane_LaneType_VIRTUAL &&
          std::all_of(lane->left_lane_marking_info().begin(),
                      lane->left_lane_marking_info().end(),
                      [](const pnc_map::LaneMarkingSegmentInfo& info) {
                        return info.type ==
                               hdmap::LaneMarking::Attribute::VIRTUAL;
                      });
      const bool is_right_marking_all_virtual =
          lane->type() == hdmap::Lane_LaneType_VIRTUAL &&
          std::all_of(lane->right_lane_marking_info().begin(),
                      lane->right_lane_marking_info().end(),
                      [](const pnc_map::LaneMarkingSegmentInfo& info) {
                        return info.type ==
                               hdmap::LaneMarking::Attribute::VIRTUAL;
                      });
      if (is_left_marking_all_virtual || is_right_marking_all_virtual) {
        absl::StrAppend(debug_string_ptr, "All Vir Marking.");
        return EncroachmentSceneType::EgoEncroachingOnly;
      }
      const bool are_ego_agent_both_encroaching =
          !IsAgentTrajectoryLaneKeepWhenEgoEncroaching(
              reasoning_object, predicted_trajectory,
              agent_associated_route_opt, joint_pnc_map_service,
              candidate_encroached_lanes,
              /*consider_virtual_as_lane_keep=*/true);
      if (are_ego_agent_both_encroaching) {
        return EncroachmentSceneType::EgoAgentBothEncroaching;
      }
      return EncroachmentSceneType::EgoEncroachingOnly;
    }

    const bool is_xlane_nudge_encroaching_opposite_way =
        lane->IsLeftmostDrivableLane() &&
        trajectory_info.ego_intention().homotopy() ==
            planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT;
    if (is_xlane_nudge_encroaching_opposite_way) {
      return EncroachmentSceneType::EgoEncroachingOnly;
    }

    bool is_oncoming_overlap_region = true;
    for (const OverlapRegionReference& overlap_region : overlap_regions) {
      if (std::any_of(overlap_region.get().overlap_slices().begin(),
                      overlap_region.get().overlap_slices().end(),
                      [](const pb::OverlapSlice& overlap_slice) {
                        return overlap_slice.motion_type() !=
                               pb::OverlapMotionType::OVERLAP_MOTION_ONCOMING;
                      })) {
        is_oncoming_overlap_region = false;
        break;
      }
    }

    const bool is_inlane_nudge_encroaching_opposite_way =
        lane->IsLeftmostDrivableLane() && is_oncoming_overlap_region &&
        (is_agent_on_the_same_side_of_encroachment ||
         (object_proximity_info.signed_lateral_gap() == 0.0 &&
          first_overlap_region_info.encroachment_side ==
              math::pb::Side::kLeft));
    if (is_inlane_nudge_encroaching_opposite_way) {
      return EncroachmentSceneType::EgoEncroachingOnly;
    }

    if (!is_agent_on_the_same_side_of_encroachment &&
        object_proximity_info.signed_lateral_gap() == 0.0) {
      if (is_ego_stepping_on_marking_not_crossing_lane) {
        absl::StrAppend(debug_string_ptr, "Stepping Marking.");
        return EncroachmentSceneType::EgoSteppingOnMarkingNotCrossingLane;
      }
      const std::vector<const pnc_map::Lane*> candidate_encroached_lanes =
          reasoning_util::GetCandidateLanesEncroachedByEgo(
              trajectory_info, first_overlap_region_info);
      return IsAgentTrajectoryLaneKeepWhenEgoEncroaching(
                 reasoning_object, predicted_trajectory,
                 agent_associated_route_opt, joint_pnc_map_service,
                 candidate_encroached_lanes)
                 ? EncroachmentSceneType::EgoEncroachingOnly
                 : EncroachmentSceneType::EgoNotEncroaching;
    }
    if (is_agent_on_the_same_side_of_encroachment &&
        !reasoning_object.IsBehindEgoLeadingBumper() &&
        is_ego_stepping_on_marking_not_crossing_lane) {
      absl::StrAppend(debug_string_ptr, "Stepping Marking Ahead.");
      return EncroachmentSceneType::EgoSteppingOnMarkingNotCrossingLane;
    }
  }
  return EncroachmentSceneType::EgoNotEncroaching;
}

// Determine the precedence under the encroachment scene. We assign agents the
// higher precedence blindly except for the XL nudging into the opposite lane.
// Specifically for the latter case, we assign higher for oncoming agents, equal
// for agents doing the XL nudge with ego, and higher for other same direction
// agents staying on the ego's source lane (not doing XL nudge).
// TODO(minhanli): Refine the logic using lane association results.
RoadPrecedenceResult GetPrecedenceForEncroachment(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<std::reference_wrapper<const pb::OverlapRegion>>&
        overlap_regions,
    const OverlapRegionInfo& first_overlap_region_info,
    const pnc_map::Lane* lane, std::string* debug_string_ptr) {
  (void)reasoning_object;
  const bool is_xlane_nudge_encroaching_opposite_way =
      lane->IsLeftmostDrivableLane() &&
      trajectory_info.ego_intention().homotopy() ==
          planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT;
  if (is_xlane_nudge_encroaching_opposite_way) {
    bool is_oncoming_agent = true;
    for (const OverlapRegionReference& overlap_region : overlap_regions) {
      if (std::any_of(overlap_region.get().overlap_slices().begin(),
                      overlap_region.get().overlap_slices().end(),
                      [](const pb::OverlapSlice& overlap_slice) {
                        return std::abs(overlap_slice.relative_heading()) <
                               kXLNudgePrecedenceOncomingThresholdInRad;
                      })) {
        is_oncoming_agent = false;
        break;
      }
    }
    // Oncoming agents have higher precedence anyway.
    if (is_oncoming_agent) {
      absl::StrAppend(debug_string_ptr, "XL-opposite oncoming.");
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::EgoEncroachment);
    }
    // If the agent keeps driving on the ego's source lane, i.e., not x-lane
    // nudging, it has higher precedence. The encroachment side is right for
    // such scene.
    if (first_overlap_region_info.encroachment_side == math::pb::Side::kRight) {
      absl::StrAppend(debug_string_ptr, "XL-opposite same right.");
      if (IsEgoSteppingOnMarkingNotConsideredCrossingLane(
              first_overlap_region_info, trajectory_info, lane)) {
        absl::StrAppend(debug_string_ptr, "Stepping Marking.");
        return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                    PrecedenceSource::EgoEncroachment);
      }
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::EgoEncroachment);
    }
    // Otherwise, the agent is x-lane nudging together with ego, should be
    // equal.
    absl::StrAppend(debug_string_ptr, "XL-opposite same left.");
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::EgoEncroachment);
  }
  return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                              PrecedenceSource::EgoEncroachment);
}

// NOTE: As we so far do not have the agent's lane sequence association, there
// are cases not being able to get accurately categorized when multiple
// scenarios exist simultaneously (e.g., GuangZhou, HuangPu, Zone 56462). A
// temporary solution is to go over scenarios by the descending order of
// how much it matters, which are, in sequence, junction, zone, merging section,
// regular road lane change, etc.
PrecedenceSceneType::Enum GetPrecedenceSceneType(
    const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object, const pnc_map::Lane* lane,
    const pnc_map::Zone* zone_ptr,
    const EncroachmentSceneType::Enum encroachment_scene) {
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (!reasoning_object.is_vehicle_or_cyclist()) {
    return PrecedenceSceneType::UnKnown;
  }

  const bool is_agent_on_left_side =
      object_proximity_info.signed_lateral_gap() > 0;

  // Check if ego's pulling out or pulling over.
  if (ego_trajectory_info.is_pull_out_jump_out() ||
      (ego_trajectory_info.is_pull_over_jump_in_trajectory() &&
       !is_agent_on_left_side)) {
    return PrecedenceSceneType::InPullOverPullOut;
  }

  if (encroachment_scene == EncroachmentSceneType::EgoEncroachingOnly) {
    return PrecedenceSceneType::InEgoEncroachment;
  }

  // Check if in junction, excluding the exclusive right turn as it is not a
  // typical junction.
  if (lane->IsInJunction() && lane->section()->road()->side_road_type() !=
                                  hdmap::Road::EXCLUSIVE_RIGHT_TURN) {
    return PrecedenceSceneType::InJunction;
  }

  // Check if on temp lane.
  if (lane->is_temp_lane()) {
    return PrecedenceSceneType::InTempLane;
  }

  // Check if in zone of interest.
  if (zone_ptr != nullptr) {
    if (zone_ptr->type() == hdmap::Zone::ROAD_EXIT) {
      return PrecedenceSceneType::InExitZone;
    }
    return PrecedenceSceneType::UnKnown;
  }
  // Check if in merging section.
  const std::vector<const pnc_map::Lane*>& lanes_in_section =
      lane->section()->lanes();
  const auto iter = std::find_if(
      lanes_in_section.begin(), lanes_in_section.end(),
      [](const pnc_map::Lane* lane_in_section) {
        return !lane_in_section
                    ->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge)
                    .empty();
      });
  if (iter != lanes_in_section.end()) {
    return PrecedenceSceneType::InMergeSection;
  }

  // Check if in regular or standalone virtual lanes. Due to the characteristic
  // of these lanes, vehicles are supposed to enter them via either lane follow
  // from a predecessor lane or lane change. It enables us to determine the
  // precedence by just examining ego's lane change status.

  // Standalone virtual lanes are those having no conflicting or merging brother
  // lanes.
  const bool is_standalone_virtual_lane =
      lane->conflicting_lanes().empty() &&
      lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge).empty();

  if (lane->type() == hdmap::Lane_LaneType_REGULAR ||
      is_standalone_virtual_lane) {
    return PrecedenceSceneType::InRegularOrStandAloneVirtualLane;
  }

  return PrecedenceSceneType::UnKnown;
}

RoadPrecedenceResult GetPrecedenceForGreenLight(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info,
    const traffic_rules::TrafficLightInfo& traffic_signal,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::reference_wrapper<const pb::OverlapRegion> overlap_region,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  if (traffic_signal.associate_traffic_light().type ==
      hdmap::AssociateLane::PROTECTED_LANE) {
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
        agent_associated_route_opt.has_value()
            ? agent_associated_route_opt.value()
            : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                  reasoning_object, predicted_trajectory, joint_pnc_map_service,
                  /*allow_using_for_cyclist=*/true);
    if (route_association_util::IsAgentStraightThroughJunctionInSameDirection(
            ego_trajectory_info, agent_route)) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Lane association. "));

      const std::vector<pnc_map::BrotherLane>& brothers =
          ego_lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge);
      // TODO(speed): Replace it with a unified signal under
      // |EgoLaneMergeForkLanesInfo|.
      const bool is_zip_merge_in_junction = std::any_of(
          brothers.begin(), brothers.end(),
          [ego_lane](const pnc_map::BrotherLane& brother) {
            return brother.lane()->section()->id() == ego_lane->section()->id();
          });

      const RoadPrecedenceResult original_result(
          is_zip_merge_in_junction ? RoadPrecedence::EQUAL
                                   : RoadPrecedence::LOWER,
          PrecedenceSource::KnownTrafficLight);
      return GetInferredPrecedenceResult(
          original_result, ego_lane, ego_trajectory_info, reasoning_object,
          agent_route, joint_pnc_map_service, debug_string_ptr);
    }

    // Check if ego is going from side to main road or vice versa.
    const pnc_map::Road* ego_road = ego_lane->section()->road();
    if (ego_road->side_road_type() == hdmap::Road::MAIN_ROAD_TO_SIDE_ROAD ||
        ego_road->side_road_type() == hdmap::Road::SIDE_ROAD_TO_MAIN_ROAD) {
      if (debug_string_ptr != nullptr)
        absl::StrAppend(debug_string_ptr, " (S.Road) ");
      return route_association_util::
                     IsEgoCrossingMainSideRoadAndConflictingWithAgent(
                         ego_lane, agent_route)
                 ? RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                        PrecedenceSource::KnownTrafficLight)
                 : RoadPrecedenceResult(RoadPrecedence::LOWER,
                                        PrecedenceSource::KnownTrafficLight);
    }

    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::KnownTrafficLight);
  }
  // Unprotected left turn yields to the agent going straight through.
  // TODO(minhanli): To address lane association failure, we use agent's turning
  // and overlap region info to ensure it will not return incorrect low
  // precedence for agent in UPL. It is a temporary hacky solution. Need to
  // rewrite association logic to fundamentally solve the problem.
  const bool is_agent_oncoming_straight =
      !predicted_trajectory.IsTurning() &&
      !reasoning_util::IsSameDirectionOverlapRegion(overlap_region);
  if (ego_lane->turn() == hdmap::Lane_Turn_LEFT) {
    if (IsAgentAlongLaneOfInterestInJunction(
            ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/true,
            std::unordered_set<hdmap::Lane::Turn>{hdmap::Lane_Turn_STRAIGHT}) ||
        is_agent_oncoming_straight) {
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::KnownTrafficLight);
    }
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::KnownTrafficLight);
  }
  // Unprotected right turn yields to the agent going straight
  // through and turning left.
  if (ego_lane->turn() == hdmap::Lane_Turn_RIGHT) {
    if (IsAgentAlongLaneOfInterestInJunction(
            ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/true,
            std::unordered_set<hdmap::Lane::Turn>{hdmap::Lane_Turn_LEFT,
                                                  hdmap::Lane_Turn_STRAIGHT})) {
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::KnownTrafficLight);
    }
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::KnownTrafficLight);
  }
  // Unprotected u-turn yields to the agent going straight
  // through and turning right, impossible conflicting with left turns.
  if (ego_lane->turn() == hdmap::Lane_Turn_U_TURN) {
    if (IsAgentAlongLaneOfInterestInJunction(
            ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/true,
            std::unordered_set<hdmap::Lane::Turn>{hdmap::Lane_Turn_STRAIGHT,
                                                  hdmap::Lane_Turn_RIGHT})) {
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::KnownTrafficLight);
    }
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::KnownTrafficLight);
  }
  // Not considered if ego lane turn type is not labeled as one of three above.
  return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                              PrecedenceSource::KnownTrafficLight);
}

RoadPrecedenceResult GetPrecedenceForTrafficLight(
    const pnc_map::Lane* ego_lane,
    const traffic_rules::TrafficLightInfo& traffic_signal,
    const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::reference_wrapper<const pb::OverlapRegion> overlap_region,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  absl::StrAppend(debug_string_ptr, strings::StringPrintf("With TL. "));
  switch (traffic_signal.state()) {
    case planner::pb::TrafficLightState::kGreenLight:
    case planner::pb::TrafficLightState::kGreenFlashingLight: {
      return GetPrecedenceForGreenLight(
          ego_lane, ego_trajectory_info, traffic_signal, reasoning_object,
          predicted_trajectory, overlap_region, agent_associated_route_opt,
          joint_pnc_map_service, debug_string_ptr);
    }
    case planner::pb::TrafficLightState::kYellowLight: {
      if (IsTrafficLightColorConditionallyConsideredAsGreen(
              ego_lane, traffic_signal, ego_trajectory_info,
              debug_string_ptr)) {
        return GetPrecedenceForGreenLight(
            ego_lane, ego_trajectory_info, traffic_signal, reasoning_object,
            predicted_trajectory, overlap_region, agent_associated_route_opt,
            joint_pnc_map_service, debug_string_ptr);
      }
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::KnownTrafficLight);
    }
    case planner::pb::TrafficLightState::kRedLight:
      if (IsTrafficLightColorConditionallyConsideredAsGreen(
              ego_lane, traffic_signal, ego_trajectory_info,
              debug_string_ptr)) {
        return GetPrecedenceForGreenLight(
            ego_lane, ego_trajectory_info, traffic_signal, reasoning_object,
            predicted_trajectory, overlap_region, agent_associated_route_opt,
            joint_pnc_map_service, debug_string_ptr);
      }
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::KnownTrafficLight);
    case planner::pb::TrafficLightState::kUnknownLight: {
      if (IsTrafficLightColorConditionallyConsideredAsGreen(
              ego_lane, traffic_signal, ego_trajectory_info,
              debug_string_ptr)) {
        return GetPrecedenceForGreenLight(
            ego_lane, ego_trajectory_info, traffic_signal, reasoning_object,
            predicted_trajectory, overlap_region, agent_associated_route_opt,
            joint_pnc_map_service, debug_string_ptr);
      }
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Unknown color. "));
      return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                  PrecedenceSource::UnKnownTrafficLight);
    }
    case planner::pb::TrafficLightState::kYellowFlashingLight: {
      DCHECK(false) << "Incorrectly handle yellow flashing";
      return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                  PrecedenceSource::UnKnownTrafficLight);
    }
    default:
      return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                  PrecedenceSource::UnKnownTrafficLight);
  }
}

// Coarse checking yield or stop sign in junction to get precedence.
// TODO(minhanli): Fine checking yield or stop sign for agent's lane to cover
// more cases.
std::optional<RoadPrecedenceResult> MaybeGetPrecedenceFromTrafficSign(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info) {
  if (HasYieldSign(ego_lane,
                   ego_trajectory_info.traffic_rules().traffic_signs)) {
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::YieldSign);
  }

  bool all_conflicting_lanes_controlled_by_stop = false;
  if (!ego_lane->conflicting_lanes().empty()) {
    all_conflicting_lanes_controlled_by_stop =
        std::all_of(ego_lane->conflicting_lanes().begin(),
                    ego_lane->conflicting_lanes().end(),
                    [](const pnc_map::ConflictingLane& conflicting_lane) {
                      return traffic_rules::IsControlledByStopSign(
                          DCHECK_NOTNULL(conflicting_lane.lane()));
                    });
  }
  if (all_conflicting_lanes_controlled_by_stop) {
    if (traffic_rules::IsControlledByStopSign(ego_lane)) {
      return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                  PrecedenceSource::StopSign);
    }
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::StopSign);
  }
  return std::nullopt;
}

RoadPrecedenceResult GetPrecedenceForNoTrafficLightWhenEgoIsStraight(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
      agent_associated_route_opt.has_value()
          ? agent_associated_route_opt.value()
          : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                reasoning_object, predicted_trajectory, joint_pnc_map_service,
                /*allow_using_for_cyclist=*/true);
  if (route_association_util::IsAgentStraightThroughJunctionInSameDirection(
          ego_trajectory_info, agent_route)) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Lane association. "));
    const RoadPrecedenceResult original_result(RoadPrecedence::LOWER,
                                               PrecedenceSource::NoTrafficSign);
    return GetInferredPrecedenceResult(
        original_result, ego_lane, ego_trajectory_info, reasoning_object,
        agent_route, joint_pnc_map_service, debug_string_ptr);
  }
  // Check if ego is going from side to main road or vice versa.
  const pnc_map::Road* ego_road = ego_lane->section()->road();
  if (ego_road->side_road_type() == hdmap::Road::MAIN_ROAD_TO_SIDE_ROAD ||
      ego_road->side_road_type() == hdmap::Road::SIDE_ROAD_TO_MAIN_ROAD) {
    if (debug_string_ptr != nullptr)
      absl::StrAppend(debug_string_ptr, " (S.Road) ");
    return route_association_util::
                   IsEgoCrossingMainSideRoadAndConflictingWithAgent(ego_lane,
                                                                    agent_route)
               ? RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                      PrecedenceSource::NoTrafficSign)
               : RoadPrecedenceResult(RoadPrecedence::LOWER,
                                      PrecedenceSource::NoTrafficSign);
  }
  if (IsAgentAlongLaneOfInterestInJunction(
          ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/false,
          std::unordered_set<hdmap::Lane::Turn>{hdmap::Lane_Turn_STRAIGHT})) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Both Thru. "));
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::NoTrafficSign);
  }
  return RoadPrecedenceResult(RoadPrecedence::LOWER,
                              PrecedenceSource::NoTrafficSign);
}

RoadPrecedenceResult GetPrecedenceForNoTrafficLight(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  std::optional<RoadPrecedenceResult> maybe_road_precedence =
      MaybeGetPrecedenceFromTrafficSign(ego_lane, ego_trajectory_info);
  if (maybe_road_precedence.has_value()) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("With TS. "));
    return maybe_road_precedence.value();
  }
  absl::StrAppend(debug_string_ptr, strings::StringPrintf("Without TS. "));
  // If there is no any yield or stop signs, get precedence by complying with
  // the following rules. (1) Turn yields to straight through. (2) Right turn
  // yields to left turn. (3) U-turn yields to all.
  // TODO(minhanli): add addtional rule that if both straight through or turn,
  // yield to the one coming from right.
  switch (ego_lane->turn()) {
    case hdmap::Lane_Turn_STRAIGHT:
      return GetPrecedenceForNoTrafficLightWhenEgoIsStraight(
          ego_lane, ego_trajectory_info, reasoning_object, predicted_trajectory,
          agent_associated_route_opt, joint_pnc_map_service, debug_string_ptr);
    case hdmap::Lane_Turn_RIGHT:
      if (IsAgentAlongLaneOfInterestInJunction(
              ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/false,
              std::unordered_set<hdmap::Lane::Turn>{hdmap::Lane_Turn_U_TURN})) {
        return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                    PrecedenceSource::NoTrafficSign);
      }
      if (IsAgentAboutToRightTurnAlongWithEgo(
              ego_lane, ego_trajectory_info, reasoning_object,
              predicted_trajectory, joint_pnc_map_service, debug_string_ptr)) {
        return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                    PrecedenceSource::NoTrafficSign);
      }
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::NoTrafficSign);
    case hdmap::Lane_Turn_LEFT:
      if (IsAgentAlongLaneOfInterestInJunction(
              ego_lane, predicted_trajectory, /*has_to_be_yielding_lane=*/false,
              std::unordered_set<hdmap::Lane::Turn>{
                  hdmap::Lane_Turn_STRAIGHT})) {
        return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                    PrecedenceSource::NoTrafficSign);
      }
      return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                  PrecedenceSource::NoTrafficSign);
    case hdmap::Lane_Turn_U_TURN:
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::NoTrafficSign);
    default:
      return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                  PrecedenceSource::NoTrafficSign);
  }
}

bool DoesEgoLaneChangeToLane(const pnc_map::Lane* lane,
                             const TrajectoryInfo& ego_trajectory_info) {
  return ego_trajectory_info.lane_change_status().direction !=
             planner::pb::LaneChangeMode::NONE_LANE_CHANGE &&
         ego_trajectory_info.lane_change_status()
                 .lane_change_instance.target_lane()
                 .id() == lane->id();
}

// Util function to search zone of interest in the ego lane sequence, whose
// ra arclength overlaps with that of the agent. It greedily sets the
// |zone_of_interest| to the found zone, if any, and returns.
ZoneOfInterest SearchZoneOfInterest(
    const TrajectoryInfo& ego_trajectory_info,
    const pb::LongitudinalRange& ra_arc_length_range,
    const std::unordered_set<hdmap::Zone::ZoneType>& zone_types_of_interest) {
  for (const traffic_rules::ZoneInLaneSequence& zone :
       ego_trajectory_info.traffic_rules().zones) {
    if (zone_types_of_interest.find(DCHECK_NOTNULL(zone.zone_ptr)->type()) ==
        zone_types_of_interest.end()) {
      continue;
    }
    // Skip for zones that are entirely behind ego.
    if (zone.end_ra_arclength <= 0.0) {
      continue;
    }
    if (zone.start_ra_arclength == zone.end_ra_arclength) {
      // Not valid zone, skip.
      continue;
    }
    // Early return if ego's in the zone.
    const bool is_ego_in_the_zone = math::geometry::Intersects(
        ego_trajectory_info.path().GetInterpPoint(
            ego_trajectory_info.plan_init_ra_arc_length_on_extended_path()),
        zone.zone_ptr->border());
    if (is_ego_in_the_zone) {
      return {zone.zone_ptr, is_ego_in_the_zone};
    }

    // If the project is a point, in such case, cannot use function
    // AreRangesOverlapping.
    if (ra_arc_length_range.start() == ra_arc_length_range.end()) {
      if (math::IsInRange(ra_arc_length_range.start(), zone.start_ra_arclength,
                          zone.end_ra_arclength)) {
        return {zone.zone_ptr, is_ego_in_the_zone};
      }
    } else if (math::AreRangesOverlapping(
                   math::Range1d(ra_arc_length_range.start(),
                                 ra_arc_length_range.end()),
                   math::Range1d(zone.start_ra_arclength,
                                 zone.end_ra_arclength))) {
      // Return if the agent ra arclength and zone ra arclength overlaps.
      return {zone.zone_ptr, is_ego_in_the_zone};
    }
  }
  return ZoneOfInterest();
}

// Check if the agent is around zones of interest, greedily return the zone
// by order of exit zone, bus bulb, if any.
// NOTE: This is temporary solution to identify whether the agent is exiting
// from an exit zone or bus bulb due to lack of lane association. An agent
// exiting from a road exit must have crossing heading with ego.
const pnc_map::Zone* GetNearbyZoneOfInterest(
    const TrajectoryInfo& ego_trajectory_info,
    const pb::ObjectProximityInfo& object_proximity_info,
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  const ZoneOfInterest& exit_zone_of_interest = SearchZoneOfInterest(
      ego_trajectory_info, object_proximity_info.projected_ra_arc_length(),
      std::unordered_set<hdmap::Zone::ZoneType>{hdmap::Zone::ROAD_EXIT});
  if (exit_zone_of_interest.zone_ptr != nullptr) {
    DCHECK(exit_zone_of_interest.zone_ptr->center_line() != nullptr)
        << "No center line for exit zone.";
    const double zone_heading =
        exit_zone_of_interest.zone_ptr->center_line()->GetInterpTheta(
            exit_zone_of_interest.zone_ptr->center_line()->GetTotalArcLength());
    // Zone heading is from road end pointing towards zone end, plus pi to flip
    // the direction of zone heading to cover agent exiting cases.
    const bool is_heading_considered_exiting_zone =
        std::abs(math::AngleDiff(
            zone_heading + M_PI,
            predicted_trajectory.prediction_first_pose().heading())) <=
        kMaxHeadingDiffForRoadExitVehiclesInRad;
    const bool is_squared_distance_considered_exiting_zone =
        math::geometry::ComparableDistance(
            predicted_trajectory.prediction_first_pose().center_2d(),
            exit_zone_of_interest.zone_ptr->border()) <=
        kMaxSquaredDistanceForRoadExitVehiclesInSquaredMeter;
    if (is_heading_considered_exiting_zone &&
        is_squared_distance_considered_exiting_zone) {
      return exit_zone_of_interest.zone_ptr;
    }
  }
  return nullptr;
}

RoadPrecedenceResult GetPrecedenceFromEgoLaneNearbyZone(
    const pnc_map::Lane* lane, const TrajectoryInfo& ego_trajectory_info,
    const pnc_map::Zone* near_zone, std::string* debug_string_ptr) {
  absl::StrAppend(debug_string_ptr,
                  strings::StringPrintf("Zone id :%ld. ", near_zone->id()));
  planner::pb::PrecedenceSource::Enum precedence_source =
      PrecedenceSource::None;
  if (near_zone->type() == hdmap::Zone::ROAD_EXIT) {
    precedence_source = PrecedenceSource::ExitZone;
  } else {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Zone type out of scope. "));
    return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                PrecedenceSource::UnKnownScenarioCategory);
  }
  // Check if ego's in the zone. Early return |RoadPrecedence::HIGHER| for the
  // agent if yes.
  const bool is_ego_in_the_zone = math::geometry::Intersects(
      ego_trajectory_info.path().GetInterpPoint(
          ego_trajectory_info.plan_init_ra_arc_length_on_extended_path()),
      near_zone->border());
  if (is_ego_in_the_zone) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Ego in zone. "));
    return RoadPrecedenceResult(RoadPrecedence::HIGHER, precedence_source);
  }

  // Check if ego comes to the lane by lane change. Early return
  // |RoadPrecedence::HIGHER| for the agent if yes.
  if (DoesEgoLaneChangeToLane(lane, ego_trajectory_info)) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Ego lane change. "));
    return RoadPrecedenceResult(RoadPrecedence::HIGHER, precedence_source);
  }
  // Ego goes on through lane without lane change, return
  // |RoadPrecedence::LOWER|
  return RoadPrecedenceResult(RoadPrecedence::LOWER, precedence_source);
}

// TODO(minhanli): Add logic to check if the agent is already in the junction
// before its traffic light goes red, in which agent has higher precedence
// regardless of ego's traffic light color.
RoadPrecedenceResult GetPrecedenceFromEgoLaneInJunction(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const std::reference_wrapper<const pb::OverlapRegion> overlap_region,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  // Handle precedence in roundabout separately.
  if (ego_lane->section()->road()->is_associated_with_roundabout() ||
      ego_lane->section()->road()->is_exiting_roundabout()) {
    if (ego_lane->section()->road()->is_through_roundabout()) {
      // Ego has absolute higher precedence when ego lane is through roundabout.
      return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                  PrecedenceSource::Roundabout);
    }

    const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
        agent_associated_route_opt.has_value()
            ? agent_associated_route_opt.value()
            : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                  reasoning_object, predicted_trajectory, joint_pnc_map_service,
                  /*allow_using_for_cyclist=*/true);

    if (ego_lane->section()->road()->is_entering_roundabout()) {
      // Assign equal precedence for traffic entering the roundabout along with
      // ego, otherwise higher precedence.
      absl::StrAppend(debug_string_ptr, strings::StringPrintf("enRA. "));
      return route_association_util::IsAgentEnteringRoundaboutAlongWithEgo(
                 ego_lane, agent_route)
                 ? RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                        PrecedenceSource::Roundabout)
                 : RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                        PrecedenceSource::Roundabout);
    }
    // Assign equal precedence for traffic exiting the roundabout along with
    // ego, higher precedence for traffic through roundabout, otherwise lower
    // precedence (traffic entering roundabout).
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("exRA. "));
    if (route_association_util::IsAgentExitingRoundaboutAlongWithEgo(
            ego_lane, agent_route)) {
      return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                  PrecedenceSource::Roundabout);
    }
    if (route_association_util::IsAgentThroughRoundabout(agent_route)) {
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::Roundabout);
    }
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::Roundabout);
  }

  const traffic_rules::TrafficLightInfo* maybe_traffic_light_info_ptr =
      traffic_rules::MaybeGetTrafficLightInfoPtrByLaneId(
          ego_trajectory_info.traffic_rules().traffic_lights, ego_lane->id());

  // Case of junction with traffic light.
  if (maybe_traffic_light_info_ptr != nullptr) {
    // Flashing yellow is treated as if it was not controlled by light.
    // TODO(minhanli): Integrate flashing light logic into junction w/o sign.
    if (maybe_traffic_light_info_ptr->is_yellow_flashing(/*sustained=*/true)) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Flashing Yellow. "));
      if (ego_lane->turn() != hdmap::Lane_Turn_STRAIGHT) {
        return GetPrecedenceForNoTrafficLight(
            ego_lane, ego_trajectory_info, reasoning_object,
            predicted_trajectory, agent_associated_route_opt,
            joint_pnc_map_service, debug_string_ptr);
      }
      return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                  PrecedenceSource::NoTrafficSign);
    }

    return GetPrecedenceForTrafficLight(
        ego_lane, *maybe_traffic_light_info_ptr, ego_trajectory_info,
        reasoning_object, predicted_trajectory, overlap_region,
        agent_associated_route_opt, joint_pnc_map_service, debug_string_ptr);
  }
  // Case of junction without traffic light.
  return GetPrecedenceForNoTrafficLight(
      ego_lane, ego_trajectory_info, reasoning_object, predicted_trajectory,
      agent_associated_route_opt, joint_pnc_map_service, debug_string_ptr);
}

// Determine right of way between two brother lanes by examining (1) turning
// type and associated traffic light if any, straight lane has the right of way
// if another is turning, unless the turning is protected by a green traffic
// light; (2) if both stragight, compare brother lanes' heading diff against the
// connected successor lane, the lane with the smallest heading diff has the
// right of way, or equal if their difference is smaller than a threshold.
std::optional<RoadPrecedenceResult> MaybeGetPrecedenceBetweenBrotherLanes(
    const ReasoningObject& reasoning_object,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane* ego_lane_with_brother_lanes,
    const pnc_map::BrotherLane& agent_brother_lane,
    std::string* debug_string_ptr) {
  // Check turning type and traffic light.
  const bool is_only_ego_lane_turning =
      ego_lane_with_brother_lanes->turn() != hdmap::Lane_Turn_STRAIGHT &&
      agent_brother_lane.lane()->turn() == hdmap::Lane_Turn_STRAIGHT;
  const bool is_only_brother_lane_turning =
      ego_lane_with_brother_lanes->turn() == hdmap::Lane_Turn_STRAIGHT &&
      agent_brother_lane.lane()->turn() != hdmap::Lane_Turn_STRAIGHT;

  // TODO(minhanli): There are cases where turn merging outside of a junction is
  // protected by TL, add logic to handle it to avoid over yielding.
  if (is_only_ego_lane_turning) {
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::MergingSection);
  }

  if (is_only_brother_lane_turning) {
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::MergingSection);
  }

  // Determine precedence directly from side/main road type. This should give
  // the same precedence as that given by the following "Checking heading diff"
  // part, but is faster because of no calculation when side/main road type is
  // available from the hdmap.
  const pnc_map::Road* ego_road =
      ego_lane_with_brother_lanes->section()->road();
  // Ego is merging, agent has higher precedence:
  if (ego_road->side_road_type() == hdmap::Road::MAIN_ROAD_TO_SIDE_ROAD ||
      ego_road->side_road_type() == hdmap::Road::SIDE_ROAD_TO_MAIN_ROAD) {
    if (debug_string_ptr != nullptr)
      absl::StrAppend(debug_string_ptr, " (S.Road) ");
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::MergingSection);
  }
  // Ego is driving straight, agent has lower precedence:
  if (ego_road->side_road_type() == hdmap::Road::MAIN_ROAD ||
      ego_road->side_road_type() == hdmap::Road::SIDE_ROAD) {
    if (debug_string_ptr != nullptr)
      absl::StrAppend(debug_string_ptr, " (S.Road) ");
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::MergingSection);
  }
  // Check if ego is coming from a exclusive right turn.
  const bool ego_lane_is_considered_exclusive_right_turn_merging =
      reasoning_object.object_proximity_info().signed_lateral_gap() > 0.0 &&
      std::any_of(ego_lane_with_brother_lanes->predecessors().begin(),
                  ego_lane_with_brother_lanes->predecessors().end(),
                  [](const pnc_map::Lane* predecessor_lane) {
                    return predecessor_lane->section()
                        ->road()
                        ->IsExtendedExclusiveRightTurnRoad();
                  });
  if (ego_lane_is_considered_exclusive_right_turn_merging) {
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route =
        agent_associated_route_opt.has_value()
            ? agent_associated_route_opt.value()
            : reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                  reasoning_object, predicted_trajectory, joint_pnc_map_service,
                  /*allow_using_for_cyclist=*/true);
    const bool is_agent_through_exclusive_right_turn =
        route_association_util::IsAgentTrajectoryGoingThroughExclusiveRightTurn(
            agent_route);
    // If agent is not turning with ego, early returns HIGHER.
    if (!is_agent_through_exclusive_right_turn) {
      if (debug_string_ptr != nullptr)
        absl::StrAppend(debug_string_ptr, " (Exclusive R. Turn) ");
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::MergingSection);
    }
    if (debug_string_ptr != nullptr)
      absl::StrAppend(debug_string_ptr, " (Exclusive R. Turn Together) ");
  }
  // Check agent lane's lane marking.
  const math::ProximityQueryInfo agent_lane_start_point_proximity =
      ego_lane_with_brother_lanes->center_line().GetProximity(
          agent_brother_lane.lane()->center_line().GetStartPoint(),
          math::pb::UseExtensionFlag::kForbid);
  const bool is_agent_lane_of_no_virtual_left_marking = std::none_of(
      agent_brother_lane.lane()->left_marking()->attributes().begin(),
      agent_brother_lane.lane()->left_marking()->attributes().end(),
      [](const hdmap::LaneMarking::Attribute& attribute) {
        return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
      });
  const bool is_agent_lane_of_no_virtual_right_marking = std::none_of(
      agent_brother_lane.lane()->right_marking()->attributes().begin(),
      agent_brother_lane.lane()->right_marking()->attributes().end(),
      [](const hdmap::LaneMarking::Attribute& attribute) {
        return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
      });
  if ((agent_lane_start_point_proximity.side == math::pb::kRight &&
       is_agent_lane_of_no_virtual_left_marking) ||
      (agent_lane_start_point_proximity.side == math::pb::kLeft &&
       is_agent_lane_of_no_virtual_right_marking)) {
    absl::StrAppendFormat(debug_string_ptr, "Lane Marking. ");
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::MergingSection);
  }

  // Check heading diff.
  const double lane_diff_with_connected_lane =
      pnc_map::GetAbsDirectionDiffOfConnectedLanes(
          *ego_lane_with_brother_lanes, *agent_brother_lane.connected_lane());
  const double brother_lane_diff_with_connected_lane =
      pnc_map::GetAbsDirectionDiffOfConnectedLanes(
          *agent_brother_lane.lane(), *agent_brother_lane.connected_lane());
  const double direction_compare_result =
      lane_diff_with_connected_lane - brother_lane_diff_with_connected_lane;
  if (direction_compare_result > kMinDirectionDiffForMerging) {
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::MergingSection);
  }
  if (direction_compare_result < -kMinDirectionDiffForMerging) {
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::MergingSection);
  }
  return std::nullopt;
}

std::vector<const pnc_map::Lane*> GetLanesOfInterestTypesInSection(
    const pnc_map::Section* section_ptr,
    const std::unordered_set<hdmap::Lane_LaneType>& types_of_interest,
    bool need_successors) {
  std::vector<const pnc_map::Lane*> preceding_regular_lanes;
  for (const pnc_map::Lane* lane_ptr : section_ptr->lanes()) {
    if (types_of_interest.find(lane_ptr->type()) != types_of_interest.end()) {
      if (need_successors && lane_ptr->successors().empty()) {
        continue;
      }
      preceding_regular_lanes.push_back(lane_ptr);
    }
  }
  return preceding_regular_lanes;
}

RoadPrecedenceResult GetPrecedenceByLaneIndexForZipMerge(
    const int64_t prev_lane_index, const int64_t next_lane_index,
    const pb::ObjectProximityInfo& object_proximity_info,
    const bool is_left_aligned, std::string* debug_string_ptr) {
  if (prev_lane_index < next_lane_index) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("IdxNotCovered."));
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::MergingSection);
  }
  if (prev_lane_index > next_lane_index) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Side."));
    // Further check the agent side to determine precedence for cases like left
    // aligned 3 to 1 merge, where ego takes the middle lane. Agent is lower if
    // it is on ego's right whereas agent higher if it on ego's left.
    const bool is_agent_on_lower_side =
        is_left_aligned ? object_proximity_info.signed_lateral_gap() < 0.0
                        : object_proximity_info.signed_lateral_gap() > 0.0;
    return is_agent_on_lower_side
               ? RoadPrecedenceResult(RoadPrecedence::LOWER,
                                      PrecedenceSource::MergingSection)
               : RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                      PrecedenceSource::MergingSection);
  }
  absl::StrAppend(debug_string_ptr, strings::StringPrintf("Thru."));
  return RoadPrecedenceResult(RoadPrecedence::LOWER,
                              PrecedenceSource::MergingSection);
}

// NOTE: This is a temporary solution for differentiating precedence in zip
// merge before completion of relevant turn info labeling in hdmap.
// TODO(minhanli): Revisit the logic after hdmap get updated.
RoadPrecedenceResult GetPrecedenceForZipMerge(
    const pnc_map::Lane* ego_lane,
    const pb::ObjectProximityInfo& object_proximity_info,
    std::string* debug_string_ptr) {
  // Lambda function to get lane order index in the section, the leftmost lane
  // is of index 1.
  auto GetLaneIndexInSection =
      [](const pnc_map::Lane* queried_lane_ptr,
         const std::vector<const pnc_map::Lane*>& lanes) {
        const auto lane_iter = std::find_if(
            lanes.begin(), lanes.end(),
            [&queried_lane_ptr](const pnc_map::Lane* regular_lane) {
              return regular_lane->id() == queried_lane_ptr->id();
            });
        if (lane_iter != lanes.end()) {
          return std::distance(lanes.begin(), lane_iter);
        }
        return -1L;
      };
  // Get predecessor and successor regular lane section. This works for most
  // cases as zip merge usually has one predecessor and successor regular lane
  // section.
  const pnc_map::Lane* previous_lane_ptr = ego_lane->predecessors().front();
  const pnc_map::Lane* next_lane_ptr = ego_lane->successors().front();
  // Preceding lanes have to have successors avoid using nullptr for the
  // following angle examination.
  const std::vector<const pnc_map::Lane*> prev_regular_bus_lanes =
      GetLanesOfInterestTypesInSection(
          previous_lane_ptr->section(),
          {hdmap::Lane_LaneType::Lane_LaneType_REGULAR,
           hdmap::Lane_LaneType::Lane_LaneType_BUS},
          /*need_successors=*/true);
  const std::vector<const pnc_map::Lane*> next_regular_bus_lanes =
      GetLanesOfInterestTypesInSection(
          next_lane_ptr->section(),
          {hdmap::Lane_LaneType::Lane_LaneType_REGULAR,
           hdmap::Lane_LaneType::Lane_LaneType_BUS},
          /*need_successors=*/false);

  const std::vector<const pnc_map::Lane*> current_virtual_bus_lanes =
      GetLanesOfInterestTypesInSection(
          ego_lane->section(),
          {hdmap::Lane_LaneType::Lane_LaneType_VIRTUAL,
           hdmap::Lane_LaneType::Lane_LaneType_BUS},
          /*need_successors=*/true);

  if (prev_regular_bus_lanes.empty() || next_regular_bus_lanes.empty() ||
      prev_regular_bus_lanes.size() <= next_regular_bus_lanes.size()) {
    const bool ego_has_physical_lane_markings =
        !pnc_map::HasAnyVirtualLaneMarkingBySide(ego_lane, math::pb::kLeft) &&
        !pnc_map::HasAnyVirtualLaneMarkingBySide(ego_lane, math::pb::kRight);
    if (ego_has_physical_lane_markings) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Zip. Lane Marking. "));
      return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                  PrecedenceSource::MergingSection);
    }
    if (ego_lane->merge_fork_turn_signal() ==
            planner::pb::TurnMode::LEFT_TURN ||
        ego_lane->merge_fork_turn_signal() ==
            planner::pb::TurnMode::RIGHT_TURN) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Zip. TurnType."));
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::MergingSection);
    }
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Zip. SizeNotCovered."));
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::MergingSection);
  }
  const int64_t prev_lane_num = prev_regular_bus_lanes.size();
  const int64_t next_lane_num = next_regular_bus_lanes.size();
  const int64_t prev_lane_index =
      GetLaneIndexInSection(previous_lane_ptr, prev_regular_bus_lanes);
  const int64_t next_lane_index =
      GetLaneIndexInSection(next_lane_ptr, next_regular_bus_lanes);

  if (prev_lane_index <= -1L || prev_lane_index >= prev_lane_num ||
      next_lane_index <= -1L || next_lane_index >= next_lane_num) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Zip. IdxNotCovered."));
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::MergingSection);
  }

  // TODO(minhanli): Verify the generalizaion of the following angle check rule
  // for zip merge. Check angle off between the leftmost lane in merging section
  // and its regular predecessor.
  const double left_most_lane_angle_diff =
      pnc_map::GetAbsDirectionDiffOfConnectedLanes(
          *prev_regular_bus_lanes.front(),
          *prev_regular_bus_lanes.front()->successors().front());
  // Check angle off between the rightmost lane in merging section and its
  // regular predecessor.
  const double right_most_lane_angle_diff =
      pnc_map::GetAbsDirectionDiffOfConnectedLanes(
          *prev_regular_bus_lanes.back(),
          *prev_regular_bus_lanes.back()->successors().front());
  // If we cannot tell alignment type by lane turn signal info and angle off, we
  // give it equal, otherwise check turn signal info followed by angle off.
  if (left_most_lane_angle_diff < kMinDirectionDiffForMerging &&
      right_most_lane_angle_diff < kMinDirectionDiffForMerging &&
      ego_lane->merge_fork_turn_signal() != planner::pb::TurnMode::RIGHT_TURN &&
      ego_lane->merge_fork_turn_signal() != planner::pb::TurnMode::LEFT_TURN &&
      current_virtual_bus_lanes.back()->merge_fork_turn_signal() !=
          planner::pb::TurnMode::LEFT_TURN &&
      current_virtual_bus_lanes.front()->merge_fork_turn_signal() !=
          planner::pb::TurnMode::RIGHT_TURN) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Zip. NoAln."));
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::MergingSection);
  }
  if (ego_lane->merge_fork_turn_signal() == planner::pb::TurnMode::LEFT_TURN) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Zip. L."));
    // Left aligned merging.
    return GetPrecedenceByLaneIndexForZipMerge(
        prev_lane_index, next_lane_index, object_proximity_info,
        /*is_left_aligned=*/true, debug_string_ptr);
  }
  if (ego_lane->merge_fork_turn_signal() == planner::pb::TurnMode::RIGHT_TURN) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Zip. R."));
    // Right aligned merging, reverse index to adapt to an universal processing
    // logic.
    return GetPrecedenceByLaneIndexForZipMerge(
        prev_lane_num - 1 - prev_lane_index,
        next_lane_num - 1 - next_lane_index, object_proximity_info,
        /*is_left_aligned=*/false, debug_string_ptr);
  }
  if (left_most_lane_angle_diff < right_most_lane_angle_diff ||
      current_virtual_bus_lanes.back()->merge_fork_turn_signal() ==
          planner::pb::TurnMode::LEFT_TURN) {
    absl::StrAppend(debug_string_ptr, strings::StringPrintf("Zip. L."));
    // Left aligned merging.
    return GetPrecedenceByLaneIndexForZipMerge(
        prev_lane_index, next_lane_index, object_proximity_info,
        /*is_left_aligned=*/true, debug_string_ptr);
  }
  absl::StrAppend(debug_string_ptr, strings::StringPrintf("Zip. R."));
  // Right aligned merging, reverse index to adapt to an universal processing
  // logic.
  return GetPrecedenceByLaneIndexForZipMerge(
      prev_lane_num - 1 - prev_lane_index, next_lane_num - 1 - next_lane_index,
      object_proximity_info,
      /*is_left_aligned=*/false, debug_string_ptr);
}

// Check wheather the lane section to which the input |lane| belongs
// contains brother lanes of type |BrotherLane::RelationType::kMerge| like
// the illustration shown below. The section consists of A, B, and C. The
// reason why we care about the whole section rather than just a single
// lane is that currently some CN maps do not connect virtual lanes
// between A and C. But, in practice, we often observe agents merge into
// ego's lane (e.g., A) from C directly. So we need to take this case into
// account and treat it together with the case of merging from C to B. If
// we find the |lane| is in the such a section, we further examime if it
// belongs to a zip merging where A, B and C share the same predecessor
// section. Zip merging needs a special treatment.
// Otherwise, C is a turning lane originating from a different section
// than A and B. If ego is going through A or B, return
// |RoadPrecedence::LOWER| for the agent. Or if ego is going through C,
// return |RoadPrecedence::HIGHER| for the agent. We calculate heading
// difference between lane sequences to determine whether ego is turning
// or not. Say, ego's going through B to E. We compare the heading diff
// between B and E to that of C and E. If the former diff is smaller than
// the latter, we deem ego is straight through and vice versa.
//    |------|------|
//    |      |      |
//    |      |      |
//    |   D  |   E  |
//    |      |      |
//    |------|------|
//    |      |\     |\
//    |      | \    | \
//    |      |  \   |  \
//    |   A  | B \  | C \
//    |      |    \ |    \
//    |----- |-----\|-----\
// TODO(minhanli): Request map team to connect all possible virtual lanes
// in merging section to avoid checking lanes without brothers, such as A,
// and simplify the logic.
RoadPrecedenceResult GetPrecedenceFromEgoLaneInMergingSection(
    const pnc_map::Lane* ego_lane, const TrajectoryInfo& ego_trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  // Check if ego comes to the lane by lane change. Early return
  // |RoadPrecedence::HIGHER| for the agent if yes.
  if (ego_trajectory_info.lane_change_status().direction !=
          planner::pb::LaneChangeMode::NONE_LANE_CHANGE &&
      ego_trajectory_info.lane_change_status()
              .lane_change_instance.target_lane()
              .id() == ego_lane->id()) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Ego lane change. "));
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::MergingSection);
  }
  const pnc_map::Lane* lane_with_brother_lanes = nullptr;

  const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
      ego_lane_merge_fork_lanes_info =
          ego_trajectory_info.traffic_rules().merge_fork_lanes;
  const bool ego_lane_has_merge_lanes = std::any_of(
      ego_lane_merge_fork_lanes_info.begin(),
      ego_lane_merge_fork_lanes_info.end(),
      [&ego_lane](const traffic_rules::EgoLaneMergeForkLanesInfo&
                      ego_lane_merge_fork_lane) {
        return ego_lane_merge_fork_lane.ego_lane->id() == ego_lane->id() &&
               !ego_lane_merge_fork_lane.sorted_merge_lanes.empty();
      });
  if (ego_lane_has_merge_lanes) {
    lane_with_brother_lanes = ego_lane;
  } else {
    const std::vector<const pnc_map::Lane*>& lanes_in_section =
        ego_lane->section()->lanes();
    const auto iter = std::find_if(
        lanes_in_section.begin(), lanes_in_section.end(),
        [](const pnc_map::Lane* lane_in_section) {
          return !lane_in_section
                      ->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge)
                      .empty();
        });
    if (iter != lanes_in_section.end()) {
      lane_with_brother_lanes = *iter;
    }
  }
  // If the queried lane does not have brother lanes, agent does merging by
  // crossing lanes. Early return |RoadPrecedence::LOWER|.
  if (lane_with_brother_lanes->id() != ego_lane->id()) {
    absl::StrAppend(debug_string_ptr,
                    strings::StringPrintf("Agent lane crossing. "));
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::MergingSection);
  }

  for (const pnc_map::BrotherLane& brother_lane :
       lane_with_brother_lanes->GetBrothers(
           pnc_map::BrotherLane::RelationType::kMerge)) {
    // Check if they are zip merging.
    if (reasoning_util::HasSamePredecessorSection(lane_with_brother_lanes,
                                                  brother_lane.lane())) {
      return GetPrecedenceForZipMerge(
          ego_lane, reasoning_object.object_proximity_info(), debug_string_ptr);
    }
    // Determine precedence by road structure differences between brother lanes.
    std::optional<RoadPrecedenceResult> maybe_road_precedence =
        MaybeGetPrecedenceBetweenBrotherLanes(
            reasoning_object, agent_associated_route_opt, predicted_trajectory,
            joint_pnc_map_service, lane_with_brother_lanes, brother_lane,
            debug_string_ptr);
    if (maybe_road_precedence.has_value()) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Heading diff or turn type. "));
      return maybe_road_precedence.value();
    }
  }
  // Could not differentiate heading diff between lanes given the
  // threshold,return |RoadPrecedence::EQUAL|.
  absl::StrAppend(debug_string_ptr,
                  strings::StringPrintf("No heading diff or turn type. "));
  return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                              PrecedenceSource::MergingSection);
}

// NOTE: This method does a quick check on current positions of ego
// and agent to see if they are already in the overlapped lane. For virtual
// lanes, we are able to do a fast inference on road precedence
// without further looking at other rules if either or both of ego and agent are
// in the lane. It follows four simple rules: (1) ego is already in the lane
// whereas agent is not, early return |RoadPrecedence::LOWER| for agent; (2)
// agent is already in the lane whereas ego is not, early return
// |RoadPrecedence::HIGHER| for agent; (3) if both are in the lane, early return
// |RoadPrecedence::EQUAL|; (4) if none of them is in the lane, return
// |std::nullopt| and fallback to additional rules to infer the
// precedence.
std::optional<RoadPrecedenceResult>
MaybeGetPrecedenceFromEgoAgentCurrentPosition(
    const pnc_map::Lane* overlapped_lane,
    const TrajectoryInfo& ego_trajectory_info,
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  // It's only applicable to regular lanes or standalone virtual lanes as
  // additional traffic regulations are otherwise enforced. Early return
  // std::nullopt for lanes being of types not regular, indicating the
  // precedence is not determined by this method.
  const bool is_standalone_virtual_lane =
      overlapped_lane->conflicting_lanes().empty() &&
      overlapped_lane->GetBrothers(pnc_map::BrotherLane::RelationType::kMerge)
          .empty();
  if (overlapped_lane->type() != hdmap::Lane_LaneType_REGULAR &&
      !is_standalone_virtual_lane) {
    return std::nullopt;
  }
  const bool is_ego_in_the_lane = math::geometry::Intersects(
      ego_trajectory_info.path().GetInterpPoint(
          ego_trajectory_info.plan_init_ra_arc_length_on_extended_path()),
      overlapped_lane->border());

  const bool is_agent_in_the_lane = math::geometry::Intersects(
      predicted_trajectory.prediction_first_pose().center_2d(),
      overlapped_lane->border());
  // Ego is already in the lane whereas agent is not, agent has lower
  // precedence.
  if (is_ego_in_the_lane && !is_agent_in_the_lane) {
    return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                PrecedenceSource::AlreadyInLane);
  }
  // Agent is already in the lane whereas ego is not, agent has higher
  // precedence.
  if (!is_ego_in_the_lane && is_agent_in_the_lane) {
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::AlreadyInLane);
  }
  // Both ego and agent are in the lane, equal precedence.
  if (is_ego_in_the_lane && is_agent_in_the_lane) {
    return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                PrecedenceSource::AlreadyInLane);
  }
  return std::nullopt;
}

// Return the road precedence of a predicted agent trajectory aganist ego
// computed from legacy road priority.
RoadPrecedenceResult ConversionFromRoadPriority(
    const planner::pb::RoadPriorityType::Enum road_priority) {
  switch (road_priority) {
    case planner::pb::RoadPriorityType::HIGHER:
      return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                  PrecedenceSource::UseRoadPririty);
    case planner::pb::RoadPriorityType::LOWER:
      return RoadPrecedenceResult(RoadPrecedence::LOWER,
                                  PrecedenceSource::UseRoadPririty);
    case planner::pb::RoadPriorityType::EQUAL:
      return RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                  PrecedenceSource::UseRoadPririty);
    case planner::pb::RoadPriorityType::UNKNOWN:
      return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                  PrecedenceSource::UseRoadPririty);
    default:
      break;
  }
  return RoadPrecedenceResult(RoadPrecedence ::UNKNOWN,
                              PrecedenceSource::UseRoadPririty);
}

// NOTE: This is a temporary util function to select a
// |RoadPrecedenceResult| with the highest road precedence against ego between
// computed and road priority converted results, given we still rely on the road
// priority for fallbacks. It will be ultimately deprecated when road precedence
// fully covers what current road priority has done.
const RoadPrecedenceResult& HighestRoadPrecedence(
    const RoadPrecedenceResult& precedence_1,
    const RoadPrecedenceResult& precedence_2) {
  if (precedence_1.road_precedence == RoadPrecedence::UNKNOWN) {
    return precedence_2;
  }
  if (precedence_2.road_precedence == RoadPrecedence::UNKNOWN) {
    return precedence_1;
  }
  switch (precedence_1.road_precedence) {
    case RoadPrecedence::HIGHER:
      return precedence_1;

    case RoadPrecedence::EQUAL:
      if (precedence_2.road_precedence == RoadPrecedence::HIGHER) {
        return precedence_2;
      }
      return precedence_1;

    case RoadPrecedence::LOWER:
      return precedence_2.road_precedence == RoadPrecedence::LOWER
                 ? precedence_1
                 : precedence_2;

    default:
      DCHECK(false) << "unexpected road precedence: "
                    << RoadPrecedence::Enum_Name(precedence_1.road_precedence);
      return precedence_2;
  }
}

}  // namespace

// TODO(minhanli): Add logic to check if the agent is a leading vehicle, if yes,
// early return |RoadPrecedence::HIGHER|.
RoadPrecedenceResult GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
    const TrajectoryInfo& ego_trajectory_info,
    const std::vector<std::pair<int64_t, math::Range<double>>>&
        ego_lane_sequence_id_ra_arclength_ranges,
    const std::vector<std::reference_wrapper<const pb::OverlapRegion>>&
        overlap_regions,
    const std::vector<OverlapRegionInfo>& overlap_region_infos,
    const ReasoningObject& reasoning_object,
    const PredictedTrajectoryWrapper& predicted_trajectory,
    const planner::pb::RoadPriorityType::Enum road_priority,
    const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
        agent_associated_route_opt,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const double ra_to_front_bumper_shift, const double ra_to_rear_bumper_shift,
    std::string* debug_string_ptr) {
  DCHECK(debug_string_ptr != nullptr);
  absl::StrAppend(debug_string_ptr, " \n road precedence debug: ");

  const bool is_overlap_regions_from_the_same_agent = std::none_of(
      overlap_regions.begin(), overlap_regions.end(),
      [&predicted_trajectory](const pb::OverlapRegion& overlap_region) {
        return overlap_region.object_id() != predicted_trajectory.object_id();
      });
  DCHECK(is_overlap_regions_from_the_same_agent)
      << "Overlap regions come from a different object than the predicted "
         "trajectory.";

  // Early return if agent with EmergencyVehicle attribute in task execution
  // period (lights flashing), and we will set high precedence for them. Refer
  // doc https://cooper.didichuxing.com/docs2/document/2204455542911.
  if (reasoning_object.IsEmergencyVehicleInTask()) {
    rt_event::PostRtEvent<
        rt_event::planner::EmergencyVehicleAttributesFromPerception>(
        std::to_string(reasoning_object.id()));
    return {RoadPrecedence::HIGHER,
            PrecedenceSource::PrecedenceSource::EmergencyVehicle};
  }

  // [Note] This is a temporary safety fix for map change region with temp
  // lanes.
  // TODO(path&speed): Refactor this with a more principled solution.
  // Early return if ego is/will soon be on a temp lane without conflicting
  // lanes, set high precedence for agents to ensure safe behavior. Since temp
  // lane might not be available for lane association, we will do the early
  // return here.
  constexpr double kTempLaneArcLengthThresholdInMeter = 10.0;
  for (const auto& lane :
       ego_trajectory_info.traffic_rules().lane_sequence_lanes) {
    if (lane.start_ra_arclength > kTempLaneArcLengthThresholdInMeter) {
      // If the lane is far away from ego, we will not check following lanes.
      break;
    }
    if (!lane.lane->is_temp_lane() || !lane.lane->conflicting_lanes().empty()) {
      continue;
    }
    return RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                PrecedenceSource::TempLane);
  }

  // Early return |RoadPrecedence::UNKNOWN| road precedence if there is no
  // overlap between ego lane sequence and agent predicted trajectory.
  if (predicted_trajectory.empty()) {
    return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                PrecedenceSource::NoOverlapWithEgo);
  }
  RoadPrecedenceResult road_precedence_result(RoadPrecedence::UNKNOWN,
                                              PrecedenceSource::None);

  DCHECK(static_cast<int>(ego_lane_sequence_id_ra_arclength_ranges.size()) ==
         ego_trajectory_info.lane_sequence_iterator().size())
      << "Lane sequence ra arclength ranges size differs from lane sequence "
         "iterator size.";
  const std::vector<const pnc_map::Lane*>
      candidate_overlapped_lanes_of_interest = GetCandidateOverlappedLanes(
          ego_trajectory_info, ego_lane_sequence_id_ra_arclength_ranges,
          overlap_regions, ra_to_front_bumper_shift, ra_to_rear_bumper_shift);
  if (candidate_overlapped_lanes_of_interest.empty()) {
    return RoadPrecedenceResult(RoadPrecedence::UNKNOWN,
                                PrecedenceSource::NoOverlapWithEgo);
  }

  const pnc_map::Lane* overlapped_lane_of_interest =
      GetOverlappedLaneOfInterest(
          reasoning_object, candidate_overlapped_lanes_of_interest,
          predicted_trajectory, agent_associated_route_opt,
          joint_pnc_map_service, debug_string_ptr);

  absl::StrAppend(debug_string_ptr,
                  strings::StringPrintf("Overlapped lane id :%ld. ",
                                        overlapped_lane_of_interest->id()));
  const auto first_overlap_region_iter =
      std::min_element(overlap_regions.begin(), overlap_regions.end(),
                       [](const pb::OverlapRegion& overlap_region_1,
                          const pb::OverlapRegion& overlap_region_2) {
                         return GetPaddedOverlapStart(overlap_region_1) <
                                GetPaddedOverlapStart(overlap_region_2);
                       });
  const OverlapRegionInfo& first_overlap_region_info = overlap_region_infos.at(
      std::distance(overlap_regions.begin(), first_overlap_region_iter));

  const pnc_map::Zone* zone_ptr = GetNearbyZoneOfInterest(
      ego_trajectory_info, reasoning_object.object_proximity_info(),
      predicted_trajectory);

  const EncroachmentSceneType::Enum encroachment_scene =
      GetEncroachmentSceneType(
          ego_trajectory_info, reasoning_object, overlap_regions,
          first_overlap_region_info, overlapped_lane_of_interest,
          predicted_trajectory, agent_associated_route_opt,
          joint_pnc_map_service, DCHECK_NOTNULL(debug_string_ptr));

  const PrecedenceSceneType::Enum precedence_scene_type =
      GetPrecedenceSceneType(ego_trajectory_info, reasoning_object,
                             overlapped_lane_of_interest, zone_ptr,
                             encroachment_scene);

  switch (precedence_scene_type) {
    case PrecedenceSceneType::InJunction:
      road_precedence_result = GetPrecedenceFromEgoLaneInJunction(
          overlapped_lane_of_interest, ego_trajectory_info, reasoning_object,
          predicted_trajectory, *first_overlap_region_iter,
          agent_associated_route_opt, joint_pnc_map_service,
          DCHECK_NOTNULL(debug_string_ptr));
      break;
    case PrecedenceSceneType::InExitZone:
      road_precedence_result = GetPrecedenceFromEgoLaneNearbyZone(
          overlapped_lane_of_interest, ego_trajectory_info,
          DCHECK_NOTNULL(zone_ptr), DCHECK_NOTNULL(debug_string_ptr));
      break;
    case PrecedenceSceneType::InStopbyZone:
      road_precedence_result = GetPrecedenceFromEgoLaneNearbyZone(
          overlapped_lane_of_interest, ego_trajectory_info,
          DCHECK_NOTNULL(zone_ptr), DCHECK_NOTNULL(debug_string_ptr));
      break;
    case PrecedenceSceneType::InMergeSection:
      road_precedence_result = GetPrecedenceFromEgoLaneInMergingSection(
          overlapped_lane_of_interest, ego_trajectory_info, reasoning_object,
          agent_associated_route_opt, predicted_trajectory,
          joint_pnc_map_service, DCHECK_NOTNULL(debug_string_ptr));
      break;
    case PrecedenceSceneType::InRegularOrStandAloneVirtualLane: {
      // Fast examining current postion for both of ego and agent, and early
      // return if the road precedence can be determined by current positions.
      std::optional<RoadPrecedenceResult> maybe_road_precedence_result =
          MaybeGetPrecedenceFromEgoAgentCurrentPosition(
              overlapped_lane_of_interest, ego_trajectory_info,
              predicted_trajectory);
      road_precedence_result =
          maybe_road_precedence_result.has_value()
              ? maybe_road_precedence_result.value()
              : (DoesEgoLaneChangeToLane(overlapped_lane_of_interest,
                                         ego_trajectory_info)
                     ? RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                            PrecedenceSource::LaneChange)
                     : RoadPrecedenceResult(RoadPrecedence::LOWER,
                                            PrecedenceSource::LaneChange));
      break;
    }
    case PrecedenceSceneType::InEgoEncroachment: {
      // TODO(waylon): This is a short term solution to compute road
      // precedence in nudge/super nudge scenes. It could be deleted when road
      // precedence computation has improved in nudge/super nudge scenes.
      road_precedence_result = GetPrecedenceForEncroachment(
          ego_trajectory_info, reasoning_object, overlap_regions,
          first_overlap_region_info, overlapped_lane_of_interest,
          debug_string_ptr);
      break;
    }
    case PrecedenceSceneType::InPullOverPullOut: {
      road_precedence_result =
          encroachment_scene == EncroachmentSceneType::EgoAgentBothEncroaching
              ? RoadPrecedenceResult(RoadPrecedence::EQUAL,
                                     PrecedenceSource::PullOverPullOut)
              : RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                     PrecedenceSource::PullOverPullOut);
      break;
    }
    case PrecedenceSceneType::InTempLane: {
      road_precedence_result = RoadPrecedenceResult(RoadPrecedence::HIGHER,
                                                    PrecedenceSource::TempLane);
      break;
    }
    case PrecedenceSceneType::UnKnown:
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Scenario is not covered. "));
      road_precedence_result = RoadPrecedenceResult(
          RoadPrecedence::UNKNOWN, PrecedenceSource::UnKnownScenarioCategory);
      break;
    default:
      break;
  }

  // Further check the precedence when both agent and ego are encroaching and
  // take the highest precedence from encroachment-based and road-structure
  // based results to return except for the LaneChange (which is
  // already being checked beforehand).
  // TODO(minhan): Refactor the logic and integrate the
  // |PrecedenceSource::LaneChange| into |PrecedenceSource::InEgoEncroachment|.
  if (encroachment_scene == EncroachmentSceneType::EgoAgentBothEncroaching) {
    if (road_precedence_result.road_precedence != RoadPrecedence::HIGHER) {
      road_precedence_result = {
          RoadPrecedence::EQUAL,
          PrecedenceSource::PrecedenceSource::EgoEncroachment};
      absl::StrAppend(debug_string_ptr, "Corrected Equal.");
    } else if (road_precedence_result.precedence_source ==
               PrecedenceSource::LaneChange) {
      road_precedence_result = {RoadPrecedence::EQUAL,
                                PrecedenceSource::PrecedenceSource::LaneChange};
      absl::StrAppend(debug_string_ptr, "Corrected Equal.");
    }
  } else if (encroachment_scene ==
             EncroachmentSceneType::EgoSteppingOnMarkingNotCrossingLane) {
    // Only correct the precedence to EQUAL when the agent already occupied the
    // lane without any other road-structure-wise conflicts, aka. under
    // |PrecedenceSource::AlreadyInLane|.
    if (road_precedence_result.precedence_source ==
            PrecedenceSource::AlreadyInLane &&
        road_precedence_result.road_precedence == RoadPrecedence::HIGHER) {
      road_precedence_result = {
          RoadPrecedence::EQUAL,
          PrecedenceSource::PrecedenceSource::AlreadyInLane};
      absl::StrAppend(debug_string_ptr, "Corrected Equal.");
    }
  }

  if (road_precedence_result.road_precedence == RoadPrecedence::UNKNOWN) {
    road_precedence_result = util::ConversionFromRoadPriority(road_priority);
  }
  // NOTE: We currently do not tackle cases where there are peds or
  // cyclists crossing crosswalks, thus selecting the highest precedence
  // between computed and converted results.
  // TODO(minhanli): Add logic to handle ped and cyclist.
  if (reasoning_object.is_pedestrian_or_cyclist()) {
    road_precedence_result =
        HighestRoadPrecedence(road_precedence_result,
                              util::ConversionFromRoadPriority(road_priority));
  }
  road_precedence_result.precedence_scene_type = precedence_scene_type;
  return road_precedence_result;
}

planner::pb::PrecedenceInferenceSource::Enum GetPrecedenceInferenceSource(
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  // NOTE: We currently only do inference for straight merge in junction,
  // excluding roundabouts.
  if (conflicting_ego_lane->IsInJunction() &&
      !conflicting_ego_lane->section()
           ->road()
           ->is_associated_with_roundabout() &&
      !agent_route.empty()) {
    const bool is_ego_straight =
        conflicting_ego_lane->turn() == hdmap::Lane_Turn_STRAIGHT ||
        conflicting_ego_lane->turn() == hdmap::Lane_Turn_STRAIGHT_RIGHT ||
        conflicting_ego_lane->turn() == hdmap::Lane_Turn_LEFT_STRAIGHT;
    if (!is_ego_straight) {
      return planner::pb::PrecedenceInferenceSource::NoInference;
    }
    // Only do precedence inference if the lane marking is pure virtual.
    const bool is_ego_lane_of_virtual_left_marking = std::any_of(
        conflicting_ego_lane->left_marking()->attributes().begin(),
        conflicting_ego_lane->left_marking()->attributes().end(),
        [](const hdmap::LaneMarking::Attribute& attribute) {
          return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
        });
    const bool is_ego_lane_of_virtual_right_marking = std::any_of(
        conflicting_ego_lane->right_marking()->attributes().begin(),
        conflicting_ego_lane->right_marking()->attributes().end(),
        [](const hdmap::LaneMarking::Attribute& attribute) {
          return attribute.type() == hdmap::LaneMarking::Attribute::VIRTUAL;
        });
    if ((reasoning_object.object_proximity_info().signed_lateral_gap() > 0.0 &&
         !is_ego_lane_of_virtual_left_marking) ||
        (reasoning_object.object_proximity_info().signed_lateral_gap() < 0.0 &&
         !is_ego_lane_of_virtual_right_marking)) {
      if (debug_string_ptr != nullptr) {
        absl::StrAppend(debug_string_ptr, strings::StringPrintf("Vir Mark. "));
      }
      return planner::pb::PrecedenceInferenceSource::NoInference;
    }
    const auto iter_ego_exit_lane =
        std::find_if(trajectory_info.lane_sequence_iterator().current_lane(),
                     trajectory_info.lane_sequence_iterator().end(),
                     [conflicting_ego_lane](const pnc_map::Lane* lane) {
                       return conflicting_ego_lane->IsSuccessor(*lane);
                     });
    // Not knowing where the ego is coming from or about to exit the junction,
    // cannot do further inference.
    if (iter_ego_exit_lane == trajectory_info.lane_sequence_iterator().end() ||
        conflicting_ego_lane->predecessors().empty()) {
      if (debug_string_ptr != nullptr) {
        absl::StrAppend(debug_string_ptr,
                        strings::StringPrintf("No Junc. Exit. "));
      }
      return planner::pb::PrecedenceInferenceSource::NoInference;
    }
    const int64_t junction_id =
        conflicting_ego_lane->section()->road()->junction()->id();
    const auto iter_agent_first_lane_in_junction = std::find_if(
        agent_route.begin(), agent_route.end(),
        [junction_id](const route_association::MapElementAndPoseInfo& element) {
          return element.lane_ptr != nullptr &&
                 element.lane_ptr->section()->road()->junction() != nullptr &&
                 element.lane_ptr->section()->road()->junction()->id() ==
                     junction_id;
        });
    const pnc_map::Lane* agent_first_lane_in_junction =
        iter_agent_first_lane_in_junction != agent_route.end()
            ? joint_pnc_map_service
                  .GetLaneSequence(
                      {iter_agent_first_lane_in_junction->element_id})
                  .front()
            : nullptr;
    // When agent and ego are both going straight through a junction, do
    // additional precedence inference.
    return (agent_first_lane_in_junction != nullptr &&
            agent_first_lane_in_junction->section()->id() ==
                conflicting_ego_lane->section()->id())
               ? planner::pb::PrecedenceInferenceSource::StraightMergeInJunction
               : planner::pb::PrecedenceInferenceSource::NoInference;
  }
  return planner::pb::PrecedenceInferenceSource::NoInference;
}

InferredPrecedenceInfo GetInferredPrecedenceInfoForStraightMergeInJunction(
    const pnc_map::Lane* conflicting_ego_lane,
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    std::string* debug_string_ptr) {
  const auto iter_ego_exit_lane =
      std::find_if(trajectory_info.lane_sequence_iterator().current_lane(),
                   trajectory_info.lane_sequence_iterator().end(),
                   [conflicting_ego_lane](const pnc_map::Lane* lane) {
                     return conflicting_ego_lane->IsSuccessor(*lane);
                   });
  DCHECK(iter_ego_exit_lane != trajectory_info.lane_sequence_iterator().end());

  const int64_t junction_id =
      conflicting_ego_lane->section()->road()->junction()->id();
  // Find the last lane ahead of the junction in agent's route.
  const auto iter_agent_entrance_lane = std::find_if(
      agent_route.rbegin(), agent_route.rend(),
      [junction_id](const route_association::MapElementAndPoseInfo& element) {
        return element.lane_ptr != nullptr &&
               !element.lane_ptr->successors().empty() &&
               element.lane_ptr->successors()
                       .front()
                       ->section()
                       ->road()
                       ->junction() != nullptr &&
               element.lane_ptr->successors()
                       .front()
                       ->section()
                       ->road()
                       ->junction()
                       ->id() == junction_id;
      });

  const pnc_map::Lane* agent_entrance_lane = nullptr;
  if (iter_agent_entrance_lane != agent_route.rend()) {
    agent_entrance_lane =
        joint_pnc_map_service
            .GetLaneSequence({iter_agent_entrance_lane->element_id})
            .front();
  } else if (reasoning_object.agent_occupied_map_element_infos_in_seed_ptr() !=
                 nullptr &&
             reasoning_object.agent_occupied_map_element_infos_in_seed_ptr()
                 ->has_latest_occupied_isolated_lane()) {
    // If the agent is already in the junction, find the agent entrance lane
    // from the seed.
    agent_entrance_lane =
        joint_pnc_map_service
            .GetLaneSequence(
                {reasoning_object
                     .agent_occupied_map_element_infos_in_seed_ptr()
                     ->latest_occupied_isolated_lane()
                     .element_id()})
            .front();
  }
  if (agent_entrance_lane == nullptr) {
    if (debug_string_ptr != nullptr) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("No Agent Entrance Lane. "));
    }
    const InferredPrecedenceInfo inferred_precedence_info{
        /*inference_source=*/planner::pb::PrecedenceInferenceSource::
            StraightMergeInJunction,
        /*ego_exit_lane_for_straight_merge_in_junction=*/*iter_ego_exit_lane,
        /*ego_entrance_lane_for_straight_merge_in_junction=*/
        conflicting_ego_lane->predecessors().front(),
        /*agent_entrance_lane_for_straight_merge_in_junction=*/
        nullptr};
    return inferred_precedence_info;
  }

  // Exclude cases where agents go straight by taking the turning lanes,
  // violating traffic rules.
  const bool is_agent_straight =
      agent_entrance_lane->turn() == hdmap::Lane_Turn_STRAIGHT ||
      agent_entrance_lane->turn() == hdmap::Lane_Turn_STRAIGHT_RIGHT ||
      agent_entrance_lane->turn() == hdmap::Lane_Turn_LEFT_STRAIGHT;
  const bool are_agent_and_ego_from_same_section =
      is_agent_straight &&
      conflicting_ego_lane->predecessors().front()->section()->id() ==
          agent_entrance_lane->section()->id();
  if (!are_agent_and_ego_from_same_section) {
    if (debug_string_ptr != nullptr) {
      absl::StrAppend(debug_string_ptr,
                      strings::StringPrintf("Not from same section. "));
    }
    const InferredPrecedenceInfo inferred_precedence_info{
        /*inference_source=*/planner::pb::PrecedenceInferenceSource::
            StraightMergeInJunction,
        /*ego_exit_lane_for_straight_merge_in_junction=*/*iter_ego_exit_lane,
        /*ego_entrance_lane_for_straight_merge_in_junction=*/
        conflicting_ego_lane->predecessors().front(),
        /*agent_entrance_lane_for_straight_merge_in_junction=*/
        nullptr};
    return inferred_precedence_info;
  }

  const InferredPrecedenceInfo inferred_precedence_info{
      /*inference_source=*/planner::pb::PrecedenceInferenceSource::
          StraightMergeInJunction,
      /*ego_exit_lane_for_straight_merge_in_junction=*/*iter_ego_exit_lane,
      /*ego_entrance_lane_for_straight_merge_in_junction=*/
      conflicting_ego_lane->predecessors().front(),
      /*agent_entrance_lane_for_straight_merge_in_junction=*/
      agent_entrance_lane};
  return inferred_precedence_info;
}

}  // namespace util
}  // namespace speed
}  // namespace planner
