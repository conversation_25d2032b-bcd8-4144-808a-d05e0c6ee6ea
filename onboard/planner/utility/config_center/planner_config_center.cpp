#include "planner/utility/config_center/planner_config_center.h"

#include <unordered_map>
#include <unordered_set>

#include "av_comm/car_id.h"
#include "base/base_dir.h"
#include "planner/assist/util/assist_config_util.h"
#include "planner/speed/config_util/speed_config_util.h"
#include "planner_protos/planner_config.pb.h"
#include "planner_protos/road_speed_limiter_patch.pb.h"
#include "proto_util/proto_io.h"

namespace planner {
namespace {
// Decides loading which version of configs according to code version.
const boost::filesystem::path SetConfigFileFolderByVersion() {
  boost::filesystem::path config_file_folder(
      base::GetBaseDirPath(base::BaseDir::kConfigDir));
  config_file_folder /= "planner";

  pb::ConfigVersion config_version;
  CHECK(proto_util::ReadTextProtoFile(
      (config_file_folder / "version.conf").string(), &config_version));
  switch (config_version.version()) {
    case pb::Version::kMaster:
      config_file_folder /= "master";
      return config_file_folder;
    default:
      DCHECK(false) << "Unexpected configure version. ";
      return {};
  }
}

std::string GetManeuverConfigNameByType(pb::ManeuverType maneuver_type) {
  switch (maneuver_type) {
    case pb::ManeuverType::LANE_FOLLOW:
      return "lane_follow_maneuver_overlay.conf";
    case pb::ManeuverType::LANE_CHANGE_LEFT:
    case pb::ManeuverType::LANE_CHANGE_RIGHT:
      return "lane_change_maneuver_overlay.conf";
    case pb::ManeuverType::LANE_CHANGE_ABORT:
      return "lane_change_abort_maneuver_overlay.conf";
    case pb::ManeuverType::FALLBACK:
      return "fallback_maneuver_overlay.conf";
    case pb::ManeuverType::PULL_OUT:
      return "pull_out_maneuver_overlay.conf";
    case pb::ManeuverType::ENGAGE:
      return "engage_maneuver_overlay.conf";
    case pb::ManeuverType::PULL_OVER:
      return "pull_over_maneuver_overlay.conf";
    case pb::ManeuverType::REVERSE_DRIVING:
      return "reverse_driving_maneuver_overlay.conf";
    default:
      DCHECK(false) << "Unexpected maneuver type. ";
      return {};
  }
}

// Loads the base costing configuration and overlays different costing modes for
// the path smoother module.
void LoadPathSmootherCostModeConfig(
    const boost::filesystem::path& cost_mode_conf_folder,
    pb::PathCostingConfig* mutable_costing_config) {
  // load config helper.
  auto load_config = [&cost_mode_conf_folder, &mutable_costing_config](
                         const pb::CostModeType cost_mode,
                         const std::string& conf_file) {
    auto* mutable_costing_overrides =
        mutable_costing_config->mutable_costing_overrides();
    pb::PathCostingModeConfig config;
    CHECK(proto_util::ReadTextProtoFile(
        (cost_mode_conf_folder / conf_file).string(), &config));
    (*mutable_costing_overrides)[pb::CostModeType_Name(cost_mode)] = config;
  };

  CHECK(proto_util::ReadTextProtoFile(
      (cost_mode_conf_folder / "default_cost_mode.conf").string(),
      mutable_costing_config->mutable_costing_base()));

  // NOTE(path solver): Only allow two additional cost modes in addition to
  // default mode.
  std::unordered_map<pb::CostModeType, std::string> cost_mode_config_map;
  cost_mode_config_map.emplace(pb::CostModeType::LOW_SPEED_MODE,
                               "low_speed_cost_mode.conf");
  cost_mode_config_map.emplace(pb::CostModeType::HIGH_SPEED_MODE,
                               "high_speed_cost_mode.conf");
  cost_mode_config_map.emplace(pb::CostModeType::EMERGENCY_SWERVE_MODE,
                               "emergency_swerve_cost_mode.conf");
  cost_mode_config_map.emplace(pb::CostModeType::ML_PATH_MODE,
                               "ml_path_cost_mode.conf");
  // Load corresponding config according to cost mode.
  for (const auto& [cost_mode, conf_file] : cost_mode_config_map) {
    load_config(cost_mode, conf_file);
  }
}

std::string GetPathConfigNameByType(pb::ManeuverType maneuver_type) {
  switch (maneuver_type) {
    case pb::ManeuverType::LANE_CHANGE_LEFT:
    case pb::ManeuverType::LANE_CHANGE_RIGHT:
      return "lane_change_path_generator.conf";
    case pb::ManeuverType::LANE_CHANGE_ABORT:
      return "lane_change_abort_path_generator.conf";
    case pb::ManeuverType::PULL_OUT:
      return "pull_out_path_generator.conf";
    case pb::ManeuverType::PULL_OVER:
      return "pull_over_path_generator.conf";
    case pb::ManeuverType::LANE_FOLLOW:
    case pb::ManeuverType::FALLBACK:
    case pb::ManeuverType::ENGAGE:
    case pb::ManeuverType::REVERSE_DRIVING:
      return {};
    default:
      DCHECK(false) << "Unexpected maneuver type. ";
      return {};
  }
}

// Ensure unique lane ids in the input config file for target speed limit patch.
bool EnsureUniqueLaneIds(
    const speed::pb::TargetSpeedLimitPatch& speed_limit_patch,
    int64_t& duplicate_id) {
  std::unordered_set<int64_t> unique_lane_ids;
  for (const auto& target_speed_limit_for_lanes :
       speed_limit_patch.target_speed_limit_for_lanes()) {
    for (const auto id : target_speed_limit_for_lanes.lane_ids()) {
      if (unique_lane_ids.find(id) == unique_lane_ids.end()) {
        unique_lane_ids.insert(id);
      } else {
        duplicate_id = id;
        return false;
      }
    }
  }
  return true;
}

void LoadAgentIntentionConfigs(
    const boost::filesystem::path& agent_intention_conf_folder,
    google::protobuf::Map<std::string, pb::AgentIntentionConfig>*
        mutable_agent_intention_configs) {
  // Overlay helper.
  auto OverlayAgentIntentionConfig =
      [&agent_intention_conf_folder, &mutable_agent_intention_configs](
          const std::string& key, const std::string& conf_file,
          const pb::AgentIntentionConfig& overlay_on) {
        pb::AgentIntentionConfig config = overlay_on;
        CHECK(proto_util::MergeTextProtoFile(
            (agent_intention_conf_folder / conf_file).string(), &config));
        (*mutable_agent_intention_configs)[key] = config;
      };

  // Common agent intention config.
  pb::AgentIntentionConfig common_config;
  CHECK(proto_util::ReadTextProtoFile(
      (agent_intention_conf_folder / "common.conf").string(), &common_config));

  // Lane keep config overlay on common.
  // TODO(liangxianghui): Move lane keep specific config into the conf file.
  OverlayAgentIntentionConfig(
      /*key=*/pb::BehaviorType_Name(pb::BehaviorType::LANE_KEEP),
      /*conf_file=*/"lane_keep_overlay.conf",
      /*overlay_on=*/common_config);

  // Cross lane config overlay on common.
  OverlayAgentIntentionConfig(
      /*key=*/pb::BehaviorType_Name(pb::BehaviorType::CROSS_LANE),
      /*conf_file=*/"cross_lane_overlay.conf",
      /*overlay_on=*/common_config);

  // Pull over config overlay on common.
  OverlayAgentIntentionConfig(
      /*key=*/pb::BehaviorType_Name(pb::BehaviorType::DECOUPLED_PULL_OVER),
      /*conf_file=*/"pull_over_overlay.conf",
      /*overlay_on=*/common_config);

  // Reverse config overlay on common.
  OverlayAgentIntentionConfig(
      /*key=*/pb::BehaviorType_Name(pb::BehaviorType::REVERSE),
      /*conf_file=*/"reverse_overlay.conf",
      /*overlay_on=*/common_config);
  // Open Space config overlay on common.
  OverlayAgentIntentionConfig(
      /*key=*/pb::BehaviorType_Name(pb::BehaviorType::OPEN_SPACE),
      /*conf_file=*/"open_space_overlay.conf",
      /*overlay_on=*/common_config);
}

}  // namespace

PlannerConfigCenter::PlannerConfigCenter()
    : config_file_folder_(SetConfigFileFolderByVersion()) {
  GenerateWorldModelConfig("world_model_config");

  GenerateBehaviorReasonerConfig("behavior_reasoner_config");

  GenerateTrajectoryConfig("trajectory_config");

  GenerateOpsWarningConfig("ops_warning_config");

  GenerateCustomerMonitorConfig("customer_monitor_config");

  GenerateExceptionHandlerConfig("exception_handler_config");
}

void PlannerConfigCenter::GenerateWorldModelConfig(
    const std::string& sub_folder) {
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "world_model.conf"),
      planner_config_.mutable_world_model_config()));
}

void PlannerConfigCenter::GenerateBehaviorReasonerConfig(
    const std::string& sub_folder) {
  pb::BehaviorReasonerConfig* behavior_reasoner_config_ptr =
      planner_config_.mutable_behavior_reasoner_config();
  // Load LaneSequenceGeneratorConfig which doesn't need choose.
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "lane_sequence_generator.conf"),
      behavior_reasoner_config_ptr->mutable_lane_sequence_generator_config()));

  CHECK(proto_util::MergeTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "invoked_maneuvers.conf"),
      behavior_reasoner_config_ptr));

  // Load ManeuverRequestFilterConfig which doesn't need choose.
  CHECK(proto_util::MergeTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "maneuver_request_filter.conf"),
      behavior_reasoner_config_ptr));

  // Load ManeuverConfig which need load all maneuver configs.
  pb::ManeuverConfig base_maneuver_config;
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "maneuver_common.conf"),
      &base_maneuver_config));

  // Load occlusion speed override config.
  const std::string config_file =
      av_comm::CarId::Get().region() == av_comm::CarRegion::kUS
          ? "occlusion_speed_override_us.conf"
          : "occlusion_speed_override_cn.conf";
  proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, config_file),
      base_maneuver_config.mutable_reasoner_configs()
          ->mutable_occlusion_reasoner_config()
          ->mutable_occlusion_speed_override_config());

  for (int maneuver_type_index = 0;
       maneuver_type_index <
       behavior_reasoner_config_ptr->invoked_maneuvers_size();
       ++maneuver_type_index) {
    const pb::ManeuverType maneuver_type =
        behavior_reasoner_config_ptr->invoked_maneuvers(maneuver_type_index);

    pb::ManeuverConfig specific_maneuver_config = base_maneuver_config;
    const std::string maneuver_config_name =
        GetManeuverConfigNameByType(maneuver_type);
    if (!maneuver_config_name.empty()) {
      proto_util::MergeTextProtoFile(
          GetConfigFileAbsolutePath(sub_folder, maneuver_config_name),
          &specific_maneuver_config);
    }

    const std::string path_config_name = GetPathConfigNameByType(maneuver_type);
    if (!path_config_name.empty()) {
      proto_util::ReadTextProtoFile(
          GetConfigFileAbsolutePath(sub_folder, path_config_name),
          specific_maneuver_config.mutable_path_generator_config());
    }

    (*(behavior_reasoner_config_ptr->mutable_maneuver_configs()))
        [pb::ManeuverType_Name(maneuver_type)] = specific_maneuver_config;
  }

  // After all old maneuvers are disabled, we still need to load config from
  // LANE_FOLLOW maneuver for decoupled maneuver (used in
  // LoadPlannerCarMotionModelParam).
  // TODO(dan) [old arch]: Cleanup the legacy maneuver configs.
  if (behavior_reasoner_config_ptr->maneuver_configs().find(
          pb::ManeuverType_Name(pb::ManeuverType::LANE_FOLLOW)) ==
      behavior_reasoner_config_ptr->maneuver_configs().end()) {
    const pb::ManeuverType maneuver_type = pb::ManeuverType::LANE_FOLLOW;

    pb::ManeuverConfig specific_maneuver_config = base_maneuver_config;
    const std::string maneuver_config_name =
        GetManeuverConfigNameByType(maneuver_type);
    if (!maneuver_config_name.empty()) {
      proto_util::MergeTextProtoFile(
          GetConfigFileAbsolutePath(sub_folder, maneuver_config_name),
          &specific_maneuver_config);
    }

    const std::string path_config_name = GetPathConfigNameByType(maneuver_type);
    if (!path_config_name.empty()) {
      proto_util::ReadTextProtoFile(
          GetConfigFileAbsolutePath(sub_folder, path_config_name),
          specific_maneuver_config.mutable_path_generator_config());
    }

    (*(behavior_reasoner_config_ptr->mutable_maneuver_configs()))
        [pb::ManeuverType_Name(maneuver_type)] = specific_maneuver_config;
  }

  // Load DecoupledForwardManeuverConfig.
  pb::DecoupledForwardManeuverConfig* decoupled_forward_maneuver_config =
      behavior_reasoner_config_ptr->mutable_decoupled_forward_maneuver_config();
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "decoupled_forward_maneuver.conf"),
      decoupled_forward_maneuver_config));
  // The car-type based speed config for decoupled maneuver.
  *(decoupled_forward_maneuver_config->mutable_speed_generator_config()
        ->mutable_for_car_type()) = speed::LoadCarTypeSpeedConfigAuto();
  // Overlay on agent intention config.
  LoadAgentIntentionConfigs(
      config_file_folder_ / sub_folder / "agent_intention_configs",
      decoupled_forward_maneuver_config->mutable_agent_intention_configs());

  // Load costing config for path smoother.
  LoadPathSmootherCostModeConfig(
      config_file_folder_ / sub_folder / "nominal_path_cost_base_config",
      decoupled_forward_maneuver_config->mutable_nominal_path_planner_config()
          ->mutable_nominal_path_smoother_config()
          ->mutable_ddp_path_generator_config()
          ->mutable_costing_config());

  // Overlay on map patch.
  proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "road_speed_limiter_patch.conf"),
      decoupled_forward_maneuver_config->mutable_speed_generator_config()
          ->mutable_reference()
          ->mutable_road_speed_limiter()
          ->mutable_target_speed_limit_patch());
  // Ensure the lane id in config file is unique
  int64_t duplicate_id = -1;
  DCHECK(EnsureUniqueLaneIds(
      decoupled_forward_maneuver_config->speed_generator_config()
          .reference()
          .road_speed_limiter()
          .target_speed_limit_patch(),
      duplicate_id))
      << "Duplicate lane id in the road_speed_limiter_patch.conf with id: "
      << duplicate_id;

  // Load remote assist config.
  CHECK(proto_util::MergeTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "remote_assist.conf"),
      behavior_reasoner_config_ptr));
  // Patch specific config for assist stuck.
  assist::PatchOnAssistStuckConfig(
      (config_file_folder_ / sub_folder / "assist_stuck").string(),
      behavior_reasoner_config_ptr->mutable_remote_assist_config()
          ->mutable_assist_stuck_scene_detector_config());

  // Load remote warning config.
  CHECK(proto_util::MergeTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "remote_warning.conf"),
      behavior_reasoner_config_ptr->mutable_remote_assist_config()));
  // Patch specific config for remote warning.
  assist::PatchOnRemoteWarningConfig(
      behavior_reasoner_config_ptr->mutable_remote_assist_config()
          ->mutable_remote_warning_detector_config());

  // TODO(liangxianghui): Execute expected config check here.
}

void PlannerConfigCenter::GenerateTrajectoryConfig(
    const std::string& sub_folder) {
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "last_trajectory.conf"),
      planner_config_.mutable_trajectory_config()
          ->mutable_last_trajectory_config()));

  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "trajectory_generator.conf"),
      planner_config_.mutable_trajectory_config()
          ->mutable_trajectory_generator_config()));

  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder,
                                "decoupled_trajectory_generator.conf"),
      planner_config_.mutable_trajectory_config()
          ->mutable_decoupled_trajectory_generator_config()));

  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "trajectory_evaluation.conf"),
      planner_config_.mutable_trajectory_config()
          ->mutable_trajectory_evaluation_config()));

  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder,
                                "reverse_trajectory_generator.conf"),
      planner_config_.mutable_trajectory_config()
          ->mutable_reverse_trajectory_generator_config()));
}

void PlannerConfigCenter::GenerateOpsWarningConfig(
    const std::string& sub_folder) {
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "ops_warning.conf"),
      planner_config_.mutable_ops_warning_config()));
}

void PlannerConfigCenter::GenerateCustomerMonitorConfig(
    const std::string& sub_folder) {
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "customer_monitor.conf"),
      planner_config_.mutable_customer_monitor_config()));
}

void PlannerConfigCenter::GenerateExceptionHandlerConfig(
    const std::string& sub_folder) {
  CHECK(proto_util::ReadTextProtoFile(
      GetConfigFileAbsolutePath(sub_folder, "exception_handler.conf"),
      planner_config_.mutable_exception_handler_config()));
}

}  // namespace planner
