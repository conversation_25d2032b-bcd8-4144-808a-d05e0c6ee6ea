#include "planner/planning_topics.h"

#include <map>
#include <set>

#include <glog/logging.h>

namespace planner {

namespace {
// clang-format off
// A map from a pub topic to its publisher's name.
const std::map<PlannerPubTopic, std::string> pub_topic_publisher_names = {
    // SnapshotVNode publishers.
    {PlannerPubTopic::planning_snapshot,                      "snapshot_pub"},
    {PlannerPubTopic::planning_intra_snapshot_ra,             "snapshot_ra_pub"},
    {PlannerPubTopic::planning_snapshot_seed,                 "snapshot_seed_pub"},

    // PlanningPathVNode publishers.
    {PlannerPubTopic::routing_planning_route_state,           "routing_route_state_pub"},
    {PlannerPubTopic::planning_intra_speed_planner_param,     "planning_speed_parameters"},

    // PlanningSpeedVNode publishers.
    {PlannerPubTopic::planning_ops_warning,                   "planning_ops_warning_pub"},
    {PlannerPubTopic::planning_intra_planning_debug_wrapper,  "planning_debug_wrapper_pub"},
    {PlannerPubTopic::planning_seed,                          "planning_planning_seed_pub"},
    {PlannerPubTopic::planner_state,                          "planning_planner_state_pub"},
    {PlannerPubTopic::planning_planning_lane_sequence,        "planning_planning_lane_sequence_pub"},
    {PlannerPubTopic::planning_customer_monitor_visual,       "planning_customer_monitor_visual_pub"},
    {PlannerPubTopic::planning_pullout_request,               "planning_pullout_request_pub"},
    {PlannerPubTopic::planning_cloud_cell_request,            "planning_cloud_cell_request_pub"},
    {PlannerPubTopic::planning_stuck_detection_recall_signal, "planning_stuck_detection_recall_signal_pub"},
    {PlannerPubTopic::remote_warning_signal,                  "remote_warning_signal_pub"},
    {PlannerPubTopic::planning_assist_request,                "planning_assist_request_pub"},
    {PlannerPubTopic::routing_planner_route_command,          "routing_route_command_pub"},
    {PlannerPubTopic::planning_trajectory,                    "planning_trajectory_pub"},
    {PlannerPubTopic::planning_selectnet_sample,              "selectnet_sample_pub"},
    {PlannerPubTopic::planning_trajectory_result,             "trajectory_result_pub"},
    {PlannerPubTopic::planning_message_metadata,              "planning_message_metadata_pub"},
    {PlannerPubTopic::planning_message_metadata_input,        "planning_message_metadata_input_pub"},
    {PlannerPubTopic::planning_intra_speed_completion_signal, "planning_intra_speed_completion_signal"},
    {PlannerPubTopic::planning_empirical_raw_data, "planning_empirical_raw_data_pub"},
    {PlannerPubTopic::remote_assist_model_input,              "remote_assist_model_input_pub"},

    // PublishVNode publishers.
    {PlannerPubTopic::planning_planning_debug,                "planning_planning_debug_pub"},

    // ExceptionHandlerVNode publishers.
    {PlannerPubTopic::planning_exception_handler_seed,        "exception_handler_seed_pub"},
    {PlannerPubTopic::planning_ttc_detect_result,             "ttc_detect_result_pub"},
    {PlannerPubTopic::exception_handler_trajectory,           "exception_handler_trajectory_pub"},
    {PlannerPubTopic::exception_handler_for_perception_debug, "exception_handler_for_perception_debug_pub"},

    // ML Planner publishers.
    {PlannerPubTopic::ml_planner_close_loop_assist_trajectory,"ml_planner_close_loop_assist_trajectory_pub"},

    // RemoteAssistModelVNode publishers.
    {PlannerPubTopic::remote_assist_model_output,             "remote_assist_model_output_pub"},
};


// A map from a pub topic to its name.
const std::map<PlannerPubTopic, std::string> pub_topic_names = {
    // SnapshotVNode published topics.
    {PlannerPubTopic::planning_snapshot,                      "/planning/snapshot"},
    {PlannerPubTopic::planning_intra_snapshot_ra,             "/planning/intra/snapshot_ra"},
    {PlannerPubTopic::planning_snapshot_seed,                 "/planning/snapshot_seed"},

    // PlanningPathVNode published topics.
    {PlannerPubTopic::routing_planning_route_state,           "/routing/planning_route_state"},
    {PlannerPubTopic::planning_intra_speed_planner_param,     "/planning/intra/speed_planner_params"},
    {PlannerPubTopic::planning_message_metadata_input,        "/planning/message_metadata_input"},
    {PlannerPubTopic::planning_empirical_raw_data,            "/planning/planning_empirical_raw_data"},

    // PlanningSpeedVNode published topics.
    {PlannerPubTopic::planning_ops_warning,                   "/planning/ops_warning"},
    {PlannerPubTopic::planning_intra_planning_debug_wrapper,  "/planning/intra/planning_debug_wrapper"},
    {PlannerPubTopic::planning_seed,                          "/planning/seed"},
    {PlannerPubTopic::planner_state,                          "/planner/state"},
    {PlannerPubTopic::planning_planning_lane_sequence,        "/planning/planning_lane_sequence"},
    {PlannerPubTopic::planning_customer_monitor_visual,       "/planning/customer_monitor_visual"},
    {PlannerPubTopic::planning_pullout_request,               "/planning/pullout_request"},
    {PlannerPubTopic::planning_cloud_cell_request,            "/planning/cloud_cell_request"},
    {PlannerPubTopic::planning_stuck_detection_recall_signal, "/planning/stuck_detection_recall_signal"},
    {PlannerPubTopic::remote_warning_signal,                  "/planning/remote_warning_signal"},
    {PlannerPubTopic::planning_assist_request,                "/planning/assist_request"},
    {PlannerPubTopic::routing_planner_route_command,          "/routing/planner_route_command"},
    {PlannerPubTopic::planning_trajectory,                    "/planning/trajectory"},
    {PlannerPubTopic::planning_selectnet_sample,              "/planning/selectnet_sample"},
    {PlannerPubTopic::planning_trajectory_result,             "/planning/trajectory_result"},
    {PlannerPubTopic::planning_message_metadata,              "/planning/message_metadata"},
    {PlannerPubTopic::planning_intra_speed_completion_signal, "/planning/intra/speed_completion_signal"},
    {PlannerPubTopic::remote_assist_model_input,              "/planning/intra/remote_assist_model_input"},

    // PublishVNode published topics.
    {PlannerPubTopic::planning_planning_debug,                "/planning/planning_debug"},

    // ExceptionHandlerVNode published topics.
    {PlannerPubTopic::planning_exception_handler_seed,        "/planning/exception_handler_seed"},
    {PlannerPubTopic::planning_ttc_detect_result,             "/planning/ttc_detect_result"},
    {PlannerPubTopic::exception_handler_trajectory,           "/exception_handler/trajectory"},
    {PlannerPubTopic::exception_handler_for_perception_debug,           "/exception_handler/debug_for_perception"},

    // ML Planner published topics.
    {PlannerPubTopic::ml_planner_close_loop_assist_trajectory,"/planning/ml_planner_close_loop_assist_trajectory"},

    // RemoteAssistModelVNode published topics.
    {PlannerPubTopic::remote_assist_model_output,             "/planning/intra/remote_assist_model_output"},
};

// Pub topics to skip alignment.
const std::set<PlannerPubTopic> pub_topics_to_skip_alignment = {
    // Intra-process topics should be skipped because they are not published to
    // bag.
    PlannerPubTopic::planning_snapshot,
    PlannerPubTopic::planning_intra_snapshot_ra,
    PlannerPubTopic::planning_intra_speed_planner_param,
    PlannerPubTopic::planning_intra_planning_debug_wrapper,
    PlannerPubTopic::planning_intra_speed_completion_signal,
    PlannerPubTopic::remote_assist_model_input,
    PlannerPubTopic::remote_assist_model_output,

    // Exception handler topics should be skipped because exception handler runs
    // at different frequency.
    PlannerPubTopic::planning_exception_handler_seed,
    PlannerPubTopic::planning_ttc_detect_result,
    PlannerPubTopic::exception_handler_trajectory,
    PlannerPubTopic::exception_handler_for_perception_debug};

// A map from a subscribed topic to its name.
const std::map<PlannerSubTopic, std::string> sub_topic_names = {
    // SnapshotVNode subscribed topics.
    {PlannerSubTopic::prediction_predicted_objects,           "/prediction/predicted_objects"},
    {PlannerSubTopic::bag_planning_snapshot_seed,             "/bag/planning/snapshot_seed"},
    {PlannerSubTopic::pose,                                   "/pose"},
    {PlannerSubTopic::simulation_pose,                        "/simulation/pose"},
    {PlannerSubTopic::traffic_light_detect,                   "/traffic_light_detect"},
    {PlannerSubTopic::gateway,                                "/gateway"},
    {PlannerSubTopic::simulation_vehicle,                     "/simulation/vehicle"},
    {PlannerSubTopic::obstacle_list,                          "/obstacle_list"},
    {PlannerSubTopic::perception_construction_zone_list,      "/perception/construction_zone_list"},
    {PlannerSubTopic::routing_route_status,                   "/routing/route_status"},
    {PlannerSubTopic::planning_assist_response,               "/planning/assist_response"},
    {PlannerSubTopic::planning_fault_detector_response,       "/planning/fault_detector_response"},
    {PlannerSubTopic::trajectory_guider_guide_trajectory,     "/trajectory_guider/guide_trajectory"},
    {PlannerSubTopic::order_service,                          "/order_service"},
    {PlannerSubTopic::trip_comment,                           "/trip_comment"},
    {PlannerSubTopic::perception_lidar_detected_curb_list,    "/perception/lidar_detected_curb_list"},
    {PlannerSubTopic::health_localization_node,               "/health/localization_node"},
    {PlannerSubTopic::planning_mrc_immediate_pullover,        "/planning/mrc_immediate_pullover"},
    {PlannerSubTopic::control_replan_request,                 "/control/replan_request"},
    {PlannerSubTopic::localization_localization_fault,        "/localization/localization_fault"},
    {PlannerSubTopic::mrc_request,                            "/mrc/mrc_request"},
    {PlannerSubTopic::planning_remote_speed_limit,            "/planning/remote_speed_limit"},
    {PlannerSubTopic::vehicle_exception_status,      "/action_factory/vehicle_exception_status"},
    {PlannerSubTopic::empirical_publish_data_list,            "/realtime_map/empirical_publish_data"},
    {PlannerSubTopic::global_init_state,                      "/exception_handler/global_init_state"},
    {PlannerSubTopic::exception_handler_trajectory,           "/exception_handler/trajectory"},

    // PlanningPathVNode subscribed topics.
    {PlannerSubTopic::planning_snapshot,                      "/planning/snapshot"},
    {PlannerSubTopic::planning_intra_snapshot_ra,             "/planning/intra/snapshot_ra"},
    {PlannerSubTopic::bag_planning_seed,                      "/bag/planning/seed"},
    {PlannerSubTopic::bag_planner_state,                      "/bag/planner/state"},
    {PlannerSubTopic::map_change_area_list,                   "/mapping/map_change_area_list"},
    {PlannerSubTopic::planning_intra_speed_completion_signal, "/planning/intra/speed_completion_signal"},


    // PlanningSpeedVNode subscribed topics.
    {PlannerSubTopic::planning_intra_speed_planner_param,     "/planning/intra/speed_planner_params"},
    {PlannerSubTopic::remote_assist_model_output,             "/planning/intra/remote_assist_model_output"},
    {PlannerSubTopic::exception_handler_for_perception_debug, "/exception_handler/debug_for_perception"},
    {PlannerSubTopic::system_state,                           "/system_state"},
    {PlannerSubTopic::se_early_warning,                       "/se_early_warning"},
    {PlannerSubTopic::perception_collision_detection,         "/perception/collision_detection"},
    {PlannerSubTopic::perception_sensor_abnormal_pub,         "/perception/sensor_abnormal_pub"},
    {PlannerSubTopic::perception_camera_abnormal_pub,         "/perception/camera_abnormal_pub"},

    // PublishVNode subscribed topics.
    {PlannerSubTopic::planning_intra_planning_debug_wrapper,  "/planning/intra/planning_debug_wrapper"},

    // ExceptionHandlerVNode subscribed topics.
    {PlannerSubTopic::planning_trajectory_result,             "/planning/trajectory_result"},
    {PlannerSubTopic::bag_planning_exception_handler_seed,    "/bag/planning/exception_handler_seed"},
    {PlannerSubTopic::hdmap_changed_data,                     "/hdmap/map_change_preprocess"},

    // RemoteAssistModelVNode subscribed topics.
    {PlannerSubTopic::remote_assist_model_input,              "/planning/intra/remote_assist_model_input"},
};
// clang-format on

// Sub topics to skip alignment.
const std::set<PlannerSubTopic> sub_topics_to_skip_alignment = {
    // Intra-process topics should be skipped because they are not published to
    // bag.
    PlannerSubTopic::planning_snapshot,
    PlannerSubTopic::planning_intra_snapshot_ra,
    PlannerSubTopic::planning_intra_speed_planner_param,
    PlannerSubTopic::planning_intra_speed_completion_signal,
    PlannerSubTopic::planning_intra_planning_debug_wrapper,
    PlannerSubTopic::remote_assist_model_input,
    PlannerSubTopic::remote_assist_model_output};
}  // namespace

std::string GetPublisherName(const PlannerPubTopic& pub_topic) {
  const auto iter = pub_topic_publisher_names.find(pub_topic);
  if (iter == pub_topic_publisher_names.end()) {
    DCHECK(false) << "Cannot find pub_topic's publisher name.";
  }
  return iter->second;
}

std::string GetPlannerTopicName(const PlannerPubTopic& pub_topic) {
  const auto iter = pub_topic_names.find(pub_topic);
  if (iter == pub_topic_names.end()) {
    DCHECK(false) << "Cannot find pub_topic's name.";
  }
  return iter->second;
}

std::string GetPlannerTopicName(const PlannerSubTopic& sub_topic) {
  const auto iter = sub_topic_names.find(sub_topic);
  if (iter == sub_topic_names.end()) {
    DCHECK(false) << "Cannot find sub_topic's name.";
  }
  return iter->second;
}

std::vector<PlannerPubTopic> GetAllPubTopics() {
  std::vector<PlannerPubTopic> pub_topics;
  pub_topics.reserve(pub_topic_names.size());
  for (const auto& pair : pub_topic_names) {
    pub_topics.push_back(pair.first);
  }
  return pub_topics;
}

std::vector<PlannerSubTopic> GetAllSubTopics() {
  std::vector<PlannerSubTopic> sub_topics;
  sub_topics.reserve(sub_topic_names.size());
  for (const auto& pair : sub_topic_names) {
    sub_topics.push_back(pair.first);
  }
  return sub_topics;
}

bool ShouldSkipAlignment(const PlannerPubTopic& pub_topic) {
  return pub_topics_to_skip_alignment.find(pub_topic) !=
         pub_topics_to_skip_alignment.end();
}

bool ShouldSkipAlignment(const PlannerSubTopic& sub_topic) {
  return sub_topics_to_skip_alignment.find(sub_topic) !=
         sub_topics_to_skip_alignment.end();
}

}  // namespace planner
