#include "degradation_info_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

DegradationInfoObserver::DegradationInfoObserver(
    DegradationInfoCallback callback)
    : degradation_info_callback_(std::move(callback)) {}

bool DegradationInfoObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kDegradationInfo);
}

std::shared_ptr<google::protobuf::Message> DegradationInfoObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto degradation_info = std::make_shared<ra_cockpit::pb::DegradationInfo>();
  if (!degradation_info->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error DegradationInfo message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (degradation_info_callback_) {
    degradation_info_callback_(degradation_info, data_channel);
  }
  return degradation_info;
}

}  // namespace remote_assist
