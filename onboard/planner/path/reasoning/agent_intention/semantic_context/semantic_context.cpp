#include "planner/path/reasoning/agent_intention/semantic_context/semantic_context.h"

#include <algorithm>
#include <cstdint>
#include <limits>
#include <map>
#include <memory>
#include <unordered_map>
#include <utility>
#include <vector>

#include <tbb/parallel_for.h>

#include "geometry/model/polyline_curve.h"
#include "math/range.h"
#include "planner/assist/assist_instruction.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state_generator.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/behavior/util/traffic_rules/lane_marking_traffic_rule.h"
#include "planner/behavior/util/traffic_rules/traffic_light_info.h"
#include "planner/decoupled_maneuvers/path/reasoning/boundary_reasoning.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/control_error.h"
#include "planner/path/reasoning/agent_intention/agent_intention_estimation/agent_intention_estimation.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/intention_generator_util.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state_generator.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/ego_lane_frame_analyzer.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/road_section_frame_analyzer.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/side_lane_frame_analyzer.h"
#include "planner/path/reasoning/agent_intention/semantic_context/narrow_meeting/narrow_meeting_processor.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/object_reasoning_info.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/traffic_rule_reasoning_info.h"
#include "planner/path/reasoning/agent_intention/semantic_context/semantic_context_result.h"
#include "planner/path/reasoning/agent_intention/semantic_context/special_agent_intention/special_agent_intention_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/reasoning_input/traffic_rules/merge_fork_lane_common.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "trace/trace.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

// Constants for junction distance thresholds
constexpr double kMinJunctionDistanceThresholdInMeter = 100.0;
constexpr double kJunctionTimeGapThresholdInSec = 8.0;
constexpr double kMergeLaneConsiderationRangeInM = 30.0;

void PopulateSemanticContextDebug(
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const std::vector<FrameAnalyzerResult>& frame_analyzer_results,
    const ScenarioIdentifierResult& scenario_identify_result,
    const NarrowMeetingSceneParams& narrow_meeting_params,
    pb::SemanticContextDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  // traffic_rule_reasoning_info
  *debug->mutable_traffic_rule_reasoning_info() = traffic_rule_reasoning_info;
  // frame_analyzer_results
  for (const auto& result : frame_analyzer_results) {
    *debug->add_frame_analyze_results() = PopulateFrameAnalyzerDebug(result);
  }
  // scenario_identify_result
  *debug->mutable_scenario_identify_result() =
      PopulateScenarioIdentifierDebug(scenario_identify_result);
  // narrow_meeting_debug
  *debug->mutable_narrow_meeting_debug() =
      PopulateNarrowMeetingDebugInfo(narrow_meeting_params);
}

// Return true if the ego vehicle is near but not in a junction.
bool IsEgoNearJunction(
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const RobotStateSnapshot& ego_state_snapshot) {
  auto& current_lane = *DCHECK_NOTNULL(
      *(lane_sequence_info.lane_sequence_iterator().current_lane()));
  const pnc_map::Road& road = *DCHECK_NOTNULL(current_lane.section()->road());
  double ego_dist_to_junction = std::numeric_limits<double>::max();
  if (road.IsInJunction()) {
    return false;
  } else {
    ego_dist_to_junction =
        road.road_start_to_nearest_junction_dist_m() -
        road.reference_line()
            .GetProximity({ego_state_snapshot.x(), ego_state_snapshot.y()},
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
  }
  return ego_dist_to_junction <
         std::max(kMinJunctionDistanceThresholdInMeter,
                  ego_state_snapshot.speed() * kJunctionTimeGapThresholdInSec);
}
}  // namespace

SemanticContextResult SemanticContext::Update(
    const WorldModel& world_model,
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const lateral_clearance::LateralClearanceData& lateral_clearance_data,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile,
    const math::Range1d& planning_horizon_range,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const LaneChangeInfo& lane_change_info, bool is_ego_in_lane,
    bool should_reduce_gap_for_pass_state,
    pb::PathReasoningSeed& path_reasoning_seed,
    pb::SemanticContextDebug* debug) const {
  TRACE_EVENT_SCOPE(planner, SemanticContext_Update);
  SemanticContextResult result;
  // 1. Check should trigger unstuck logic for unknown objects.
  pb::UnstuckModeSeed unstuck_mode_seed;
  if (ShouldTriggerUnstuckForUnknownObjects(
          world_model.planner_object_map(), previous_iter_seed.selection_seed(),
          previous_iter_seed.path_seed(), world_model.snapshot_timestamp(),
          world_model.robot_state().current_state_snapshot().speed())) {
    unstuck_mode_seed.add_unstuck_modes(
        pb::UnstuckMode::UNSTUCK_UNKNOWN_OBJECTS);
    rt_event::PostRtEvent<rt_event::planner::TriggerUnstuckForUnknownAgents>();
  }
  result.unstuck_mode_seed = unstuck_mode_seed;

  // 2. Generate traffic rules needed for agent reasoning.
  const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule =
      nullptr;
  bool is_ego_near_merge = false;
  bool is_ego_near_junction = false;
  if (behavior_type_ == pb::LANE_KEEP &&
      (lane_keep_behavior_type_ == pb::LK_DEFAULT ||
       lane_keep_behavior_type_ == pb::LK_LEFT_XLANE ||
       lane_keep_behavior_type_ == pb::LK_RIGHT_XLANE)) {
    lane_marking_traffic_rule =
        &lane_sequence_info.traffic_rules().lane_marking_traffic_rule();
    // Consider all merge lanes.
    const std::function<bool(
        const speed::traffic_rules::MergeForkLaneInLaneSequence&)>
        is_valid_merge_lane =
            [](const speed::traffic_rules::MergeForkLaneInLaneSequence&
                   merge_lane) {
              (void)merge_lane;
              return true;
            };
    is_ego_near_merge =
        IsEgoNearMerge(lane_sequence_info.lane_sequence_iterator(),
                       lane_sequence_info.lane_sequence_geometry().nominal_path,
                       lane_sequence_info.robot_in_lane_param(),
                       is_valid_merge_lane, kMergeLaneConsiderationRangeInM);
    is_ego_near_junction = IsEgoNearJunction(
        lane_sequence_info, world_model.robot_state().current_state_snapshot());
  }

  // 3. Generate agent reasoning info.
  if (FLAGS_planning_enable_object_occupancy_state) {
    result.object_reasoning_info_map = GenerateObjectReasoningInfoMap(
        lane_sequence_info.object_occupancy_state_map(),
        world_model.planner_objects_history(),
        lane_sequence_info.construction_zones_inlane_state(),
        required_lateral_gap_info,
        world_model.robot_state().plan_init_state_snapshot(),
        lane_sequence_info.robot_in_lane_param(), emergency_brake_speed_profile,
        lower_bound_speed_profile, upper_bound_speed_profile,
        previous_iter_seed.last_intent(),
        previous_iter_seed.path_reasoning_seed(),
        previous_iter_seed.pull_over_info_seed(), unstuck_mode_seed,
        planning_horizon_range,
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        pull_over_destination_meta_ptr,
        lane_sequence_info.traffic_rules().traffic_light_in_lane_vector(),
        lane_change_execution_info, lane_change_info, lane_marking_traffic_rule,
        lateral_clearance_data,
        *DCHECK_NOTNULL(
            *(lane_sequence_info.lane_sequence_iterator().current_lane())),
        should_reduce_gap_for_pass_state, is_ego_near_merge,
        path_reasoning_seed,
        /*use_tbb=*/true);
    result.imaginary_object_reasoning_info_map = GenerateObjectReasoningInfoMap(
        imaginary_object_occupancy_state_map(),
        world_model.planner_objects_history(),
        /*construction_zone_states=*/{},
        imaginary_objects_required_lateral_gap_info(),
        world_model.robot_state().plan_init_state_snapshot(),
        lane_sequence_info.robot_in_lane_param(), emergency_brake_speed_profile,
        lower_bound_speed_profile, upper_bound_speed_profile,
        previous_iter_seed.last_intent(),
        previous_iter_seed.path_reasoning_seed(),
        previous_iter_seed.pull_over_info_seed(), unstuck_mode_seed,
        planning_horizon_range,
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        pull_over_destination_meta_ptr,
        lane_sequence_info.traffic_rules().traffic_light_in_lane_vector(),
        lane_change_execution_info, lane_change_info, lane_marking_traffic_rule,
        lateral_clearance_data,
        *DCHECK_NOTNULL(
            *(lane_sequence_info.lane_sequence_iterator().current_lane())),
        should_reduce_gap_for_pass_state, is_ego_near_merge,
        path_reasoning_seed,
        /*use_tbb=*/false);
  } else {
    result.object_reasoning_info_map = GenerateObjectReasoningInfoMap(
        lane_sequence_info.tracked_object_in_lane_param_map(),
        required_lateral_gap_info, world_model.planner_object_map(),
        world_model.planner_objects_history(),
        world_model.object_prediction_map(),
        lane_sequence_info.construction_zones_inlane_state(),
        world_model.robot_state().plan_init_state_snapshot(),
        lane_sequence_info.robot_in_lane_param(), emergency_brake_speed_profile,
        lower_bound_speed_profile, upper_bound_speed_profile,
        previous_iter_seed.last_intent(),
        previous_iter_seed.path_reasoning_seed(),
        previous_iter_seed.pull_over_info_seed(), unstuck_mode_seed,
        planning_horizon_range,
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        pull_over_destination_meta_ptr,
        lane_sequence_info.traffic_rules().traffic_light_in_lane_vector(),
        lane_change_execution_info, lane_change_info, lane_marking_traffic_rule,
        lateral_clearance_data,
        *DCHECK_NOTNULL(
            *(lane_sequence_info.lane_sequence_iterator().current_lane())),
        should_reduce_gap_for_pass_state, is_ego_near_merge,
        path_reasoning_seed,
        /*use_tbb=*/true);
    // Generate object reasoning info for imaginary objects. Note(Harry):
    // Currently, we only have imaginary agent, no cz is imaginary.
    result.imaginary_object_reasoning_info_map = GenerateObjectReasoningInfoMap(
        imaginary_tracked_object_in_lane_param_map(),
        imaginary_objects_required_lateral_gap_info(),
        imaginary_planner_object_map(), world_model.planner_objects_history(),
        prediction_map_for_imaginary_planner_object(),
        /*construction_zone_states=*/{},
        world_model.robot_state().plan_init_state_snapshot(),
        lane_sequence_info.robot_in_lane_param(), emergency_brake_speed_profile,
        lower_bound_speed_profile, upper_bound_speed_profile,
        previous_iter_seed.last_intent(),
        previous_iter_seed.path_reasoning_seed(),
        previous_iter_seed.pull_over_info_seed(), unstuck_mode_seed,
        planning_horizon_range,
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        pull_over_destination_meta_ptr,
        lane_sequence_info.traffic_rules().traffic_light_in_lane_vector(),
        lane_change_execution_info, lane_change_info, lane_marking_traffic_rule,
        lateral_clearance_data,
        *DCHECK_NOTNULL(
            *(lane_sequence_info.lane_sequence_iterator().current_lane())),
        should_reduce_gap_for_pass_state, is_ego_near_merge,
        path_reasoning_seed,
        /*use_tbb=*/false);
  }

  // 4. Generate frame_analyzer_results.
  result.frame_analyzer_results = AnalyzeFrame(
      world_model, upper_bound_speed_profile,
      lane_sequence_info.lane_sequence_result(),
      lane_sequence_info.lane_sequence_geometry(),
      lane_sequence_info.robot_in_lane_param(),
      lane_sequence_info.traffic_rules(), planning_horizon_range,
      result.object_reasoning_info_map, world_model.GetStuckAssistInstruction(),
      lateral_clearance_data, previous_iter_seed, &path_reasoning_seed, debug);

  // 5. Generate traffic rule reasoning info.
  TrafficRuleReasoningInfo traffic_rules(
      lane_sequence_info.lane_sequence_iterator(),
      lane_sequence_info.lane_sequence_geometry(),
      lane_sequence_info.traffic_rules());
  result.traffic_rule_reasoning_info = traffic_rules.Generate(
      lane_sequence_info.robot_in_lane_param(), lateral_clearance_data,
      result.frame_analyzer_results, is_ego_near_merge, is_ego_near_junction,
      is_ego_in_lane);
  // 6. Generate scenario_identify_result.
  result.scenario_identify_result = CategorizeScenario(
      result.object_reasoning_info_map,
      lane_sequence_info.robot_in_lane_param(), result.frame_analyzer_results,
      result.traffic_rule_reasoning_info, &path_reasoning_seed);

  // 7. Generate narrow oncoming result params.
  result.narrow_meeting_params = GenerateNarrowMeetingSceneParams(
      result.scenario_identify_result.scenario_type,
      lane_sequence_info.lane_sequence_geometry(), planning_horizon_range,
      world_model.robot_state().plan_init_state_snapshot(),
      lane_sequence_info.robot_in_lane_param(),
      result.object_reasoning_info_map,
      lane_sequence_info.lane_sequence_result().current_lane,
      lower_bound_speed_profile,
      previous_iter_seed.path_reasoning_seed().narrow_meeting_seed(),
      lateral_clearance_data, world_model.robot_state().IsInAutonomousMode(),
      path_reasoning_seed.mutable_narrow_meeting_seed());
  result.lane_change_info = lane_change_info;
  // 8. populate debug info.
  PopulateSemanticContextDebug(
      result.traffic_rule_reasoning_info, result.frame_analyzer_results,
      result.scenario_identify_result, result.narrow_meeting_params, debug);
  return result;
}

std::map<TypedObjectId, ObjectReasoningInfo>
SemanticContext::GenerateObjectReasoningInfoMap(
    const AgentInLaneStatesMap& agent_state_map,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const std::unordered_map<ObjectId, PlannerObject>& planner_objects,
    const PlannerObjectsHistory& planner_objects_history,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>& object_predictions,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
    const RobotStateSnapshot& robot_state_snapshot,
    const EgoInLaneParams& ego_param,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile,
    const pb::IntentionResult& last_intent_seed,
    const pb::PathReasoningSeed& last_path_reasoning_seed,
    const pb::PullOverInfoSeed& last_pull_over_info_seed,
    const pb::UnstuckModeSeed& unstuck_mode_seed,
    const math::Range1d& planning_horizon_range,
    const math::geometry::PolylineCurve2d& reference_line,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const std::vector<lane_selection::TrafficLightInfoReference>&
        lane_sequence_tl_infos,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const LaneChangeInfo& lane_change_info,
    const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
    const planner::lateral_clearance::LateralClearanceData&
        lateral_clearance_data,
    const pnc_map::Lane& current_lane, bool should_reduce_gap_for_pass_state,
    bool is_ego_near_merge, pb::PathReasoningSeed& path_reasoning_seed,
    bool use_tbb) const {
  TRACE_EVENT_SCOPE(planner, SemanticContext_GenerateObjectReasoningInfoMap);
  using ObjectReasoingInfoMapType =
      std::map<TypedObjectId, ObjectReasoningInfo>;
  ObjectReasoingInfoMapType object_reasoning_info_map;

  const size_t total_cpu_numbers = common::CPUCoresNumberOfPlanner();
  const auto tbb_thread_num = static_cast<size_t>(
      total_cpu_numbers * common::kHighCoresRatioOfThreadsPool);
  tbb::task_arena tbb_task_arena(tbb_thread_num);
  std::vector<ObjectReasoingInfoMapType> sub_object_reasoning_info_maps;
  sub_object_reasoning_info_maps.resize(tbb_thread_num);

  std::unordered_set<int64_t> pull_over_gap_align_trailing_agent_ids;
  for (const int64_t agent_id :
       last_pull_over_info_seed.gap_align_trailing_agent_ids()) {
    pull_over_gap_align_trailing_agent_ids.insert(agent_id);
  }

  auto GenerateAgentRelatedBlockingStateMetaTask =
      [&blocking_state_generator = blocking_state_generator_, &agent_state_map,
       &planner_objects, &required_lateral_gap_info, &last_intent_seed,
       &last_path_reasoning_seed, &path_reasoning_seed, &unstuck_mode_seed,
       &planner_objects_history, &object_predictions, &lane_sequence_tl_infos,
       &emergency_brake_speed_profile, &lower_bound_speed_profile,
       &upper_bound_speed_profile, &ego_param, &robot_state_snapshot,
       &planning_horizon_range, &reference_line, &lane_change_execution_info,
       &lane_change_info, &sub_object_reasoning_info_maps,
       &lateral_clearance_data, &current_lane,
       &pull_over_gap_align_trailing_agent_ids,
       should_reduce_gap_for_pass_state, lane_marking_traffic_rule,
       pull_over_destination_meta_ptr, use_tbb, is_ego_near_merge](int k) {
        // If use_tbb == false, only host thread take this path, worker_index =
        // 0.
        size_t worker_index =
            use_tbb ? tbb::this_task_arena::current_thread_index() : 0;
        auto agent_state_map_begin = agent_state_map.begin();
        auto agent_id_and_agent_state_iter =
            std::next(agent_state_map_begin, k);
        const auto& agent_id = agent_id_and_agent_state_iter->first;
        const auto& agent_state = agent_id_and_agent_state_iter->second;
        const auto& planner_object = gtl::FindOrDie(planner_objects, agent_id);
        const auto gap_iter =
            required_lateral_gap_info.obj_id_to_required_lat_gaps.find(
                agent_id);
        RequiredLateralGap required_lat_gap =
            gap_iter !=
                    required_lateral_gap_info.obj_id_to_required_lat_gaps.end()
                ? gap_iter->second
                : RequiredLateralGap(0.4, 0.8);
        RequiredLateralGap required_lat_gap_for_pass_state = required_lat_gap;
        required_lat_gap_for_pass_state.critical_required_lateral_gap =
            should_reduce_gap_for_pass_state
                ? planner::control_error::
                      GetLateralControlErrorSmallCurvatureInDefaultRange()
                : GetCriticalRequiredLateralGapFromPrincipledRequiredLateralGap<
                      ObjectId>(agent_id,
                                required_lateral_gap_info
                                    .object_id_to_required_lateral_gaps);
        AgentSLBoundary agent_sl_boundary(
            agent_state->tracked_state.inlane_param);
        // Gets last intention from previous seed.
        const auto intention_iter =
            last_intent_seed.object_intentions().find(agent_id);
        const bool was_nudge_intention =
            (intention_iter != last_intent_seed.object_intentions().end()) &&
            std::any_of(intention_iter->second.snapshot_intentions().begin(),
                        intention_iter->second.snapshot_intentions().end(),
                        [](const auto& snapshot_intention) {
                          return snapshot_intention.pass_state() !=
                                 pb::SnapshotIntention::IGNORE;
                        });
        const pb::StuckRegion& stuck_region =
            path_reasoning_seed.xlane_nudge_seed().target_stuck_region();
        const bool is_xlane_nudge_object =
            std::any_of(stuck_region.stuck_objects().begin(),
                        stuck_region.stuck_objects().end(),
                        [&agent_id](const pb::StuckObjectInfo& object) {
                          return object.object_id() == agent_id &&
                                 object.object_type() ==
                                     pb::ObjectSourceType::kTrackedObject;
                        });
        bool should_trigger_unstuck_for_unknown_objects = std::any_of(
            unstuck_mode_seed.unstuck_modes().begin(),
            unstuck_mode_seed.unstuck_modes().end(),
            [](const auto& unstuck_mode) {
              return unstuck_mode == pb::UnstuckMode::UNSTUCK_UNKNOWN_OBJECTS;
            });
        bool is_pull_over_gap_align_trailing_agent =
            pull_over_gap_align_trailing_agent_ids.find(agent_id) !=
            pull_over_gap_align_trailing_agent_ids.end();
        std::unordered_map<int, LateralBlockingStateMetaData>
            trajectory_blocking_state_data_map;
        // Pre-bucketing
        trajectory_blocking_state_data_map.reserve(
            agent_state->predicted_trajectories.size());
        // Generate blocking state meta data.
        for (const auto& [trajectory_id, _] :
             agent_state->predicted_trajectories) {
          trajectory_blocking_state_data_map.emplace(
              trajectory_id,
              blocking_state_generator.GenerateBlockingStateMetaData(
                  *agent_state, planner_object, planner_objects_history,
                  object_predictions, trajectory_id,
                  required_lat_gap_for_pass_state, lane_sequence_tl_infos,
                  emergency_brake_speed_profile, lower_bound_speed_profile,
                  upper_bound_speed_profile, ego_param,
                  robot_state_snapshot.bounding_box(), planning_horizon_range,
                  agent_sl_boundary, was_nudge_intention, is_xlane_nudge_object,
                  should_trigger_unstuck_for_unknown_objects, reference_line,
                  pull_over_destination_meta_ptr, lane_change_execution_info,
                  lane_change_info, lane_marking_traffic_rule,
                  lateral_clearance_data, current_lane, is_ego_near_merge,
                  is_pull_over_gap_align_trailing_agent));
        }
        LateralBlockingStateMetaData primary_blocking_state_meta_data =
            trajectory_blocking_state_data_map[agent_state
                                                   ->primary_trajectory_id];
        // Estimates agent yield probability.
        const std::optional<planner::pb::YieldIntentionData> yield_data =
            EstimateAgentYieldIntention(ego_param, *agent_state,
                                        primary_blocking_state_meta_data,
                                        last_path_reasoning_seed);

        ObjectReasoningInfo object_info(
            *agent_state, std::move(required_lat_gap),
            std::move(required_lat_gap_for_pass_state), yield_data,
            std::move(agent_sl_boundary),
            std::move(primary_blocking_state_meta_data),
            std::move(trajectory_blocking_state_data_map));

        sub_object_reasoning_info_maps[worker_index].emplace(
            object_info.object_id(), std::move(object_info));
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(agent_state_map.size()),
                        GenerateAgentRelatedBlockingStateMetaTask);
    });
  } else {
    for (size_t k = 0; k < agent_state_map.size(); ++k) {
      GenerateAgentRelatedBlockingStateMetaTask(k);
    }
  }

  // Stores current agent yield probability in the seed.
  for (const auto& sub_map : sub_object_reasoning_info_maps) {
    for (const auto& [object_id, object_info] : sub_map) {
      if (object_info.yield_intention_data().has_value()) {
        (*path_reasoning_seed.mutable_object_intention_seed()
              ->mutable_yield_object_map())[object_id.id] =
            object_info.yield_intention_data().value();
      }
    }
  }

  double avg_abs_ref_path_curvature = 0.0;
  const double ego_current_speed_mps = ego_param.speed_mps;
  if (std::any_of(construction_zone_states.begin(),
                  construction_zone_states.end(),
                  [](const auto& cz) { return cz.is_map_labeled_cz; })) {
    // Right now there are some latency concern for the below function, so we
    // will hide it behind flag.
    avg_abs_ref_path_curvature = CalculateAvgAbsRefPathCurvature(
        reference_line, ego_param.front_bumper_arclength);
  }

  // TODO(yongbing): Clean up.
  auto GenerateCzRelatedBlockingStateMetaTask =
      [&blocking_state_generator = blocking_state_generator_,
       &required_lateral_gap_info, &last_intent_seed,
       &emergency_brake_speed_profile, &lower_bound_speed_profile,
       &upper_bound_speed_profile, &ego_param, &robot_state_snapshot,
       &construction_zone_states, planning_horizon_range,
       &sub_object_reasoning_info_maps, should_reduce_gap_for_pass_state,
       avg_abs_ref_path_curvature, ego_current_speed_mps,
       pull_over_destination_meta_ptr, use_tbb](int k) {
        size_t worker_index =
            use_tbb ? tbb::this_task_arena::current_thread_index() : 0;
        const auto& cz_state = construction_zone_states[k];
        const auto& cz_id_to_required_lat_gaps =
            required_lateral_gap_info.cz_id_to_required_lat_gaps;
        const auto& cz_id_to_plg_iter =
            required_lateral_gap_info.cz_id_to_required_lateral_gaps.find(
                cz_state.id);
        const auto gap_iter = cz_id_to_required_lat_gaps.find(cz_state.id);
        RequiredLateralGap required_lat_gap =
            gap_iter !=
                    required_lateral_gap_info.cz_id_to_required_lat_gaps.end()
                ? gap_iter->second
                : RequiredLateralGap(0.4, 0.8);
        if (cz_state.is_map_labeled_cz) {
          required_lat_gap = CalculatePhysicalBoundaryRequiredLateralGap(
              cz_id_to_plg_iter->second, avg_abs_ref_path_curvature,
              ego_current_speed_mps);
        }

        // Set the CZ critical gap used in path reasoning to be 0.0 to reach
        // maximum capability of nudge generation.
        RequiredLateralGap required_lat_gap_for_pass_state = required_lat_gap;
        required_lat_gap_for_pass_state.critical_required_lateral_gap =
            should_reduce_gap_for_pass_state
                ? 0.0
                : GetCriticalRequiredLateralGapFromPrincipledRequiredLateralGap<
                      ConstructionZoneId>(
                      cz_state.id,
                      required_lateral_gap_info.cz_id_to_required_lateral_gaps);

        AgentSLBoundary cz_sl_boundary(cz_state);
        const auto intention_iter =
            last_intent_seed.construction_zone_intentions().find(cz_state.id);
        const bool was_nudge_intention =
            (intention_iter !=
             last_intent_seed.construction_zone_intentions().end()) &&
            std::any_of(intention_iter->second.snapshot_intentions().begin(),
                        intention_iter->second.snapshot_intentions().end(),
                        [](const auto& snapshot_intention) {
                          return snapshot_intention.pass_state() !=
                                 pb::SnapshotIntention::IGNORE;
                        });

        LateralBlockingStateMetaData blocking_state_data =
            blocking_state_generator
                .GenerateConstructionZoneBlockingStateMetaData(
                    cz_state, required_lat_gap_for_pass_state,
                    emergency_brake_speed_profile, lower_bound_speed_profile,
                    upper_bound_speed_profile, ego_param,
                    robot_state_snapshot.bounding_box(), planning_horizon_range,
                    cz_sl_boundary, pull_over_destination_meta_ptr,
                    was_nudge_intention);
        ObjectReasoningInfo object_info(
            cz_state, std::move(required_lat_gap),
            std::move(required_lat_gap_for_pass_state),
            std::move(cz_sl_boundary), std::move(blocking_state_data));

        sub_object_reasoning_info_maps[worker_index].emplace(
            object_info.object_id(), std::move(object_info));
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(construction_zone_states.size()),
                        GenerateCzRelatedBlockingStateMetaTask);
    });
  } else {
    for (size_t k = 0; k < construction_zone_states.size(); ++k) {
      GenerateCzRelatedBlockingStateMetaTask(k);
    }
  }

  // Map merge
  for (auto&& sub_object_reasoning_info_map : sub_object_reasoning_info_maps) {
    object_reasoning_info_map.merge(std::move(sub_object_reasoning_info_map));
  }

  return object_reasoning_info_map;
}

std::map<TypedObjectId, ObjectReasoningInfo>
SemanticContext::GenerateObjectReasoningInfoMap(
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const PlannerObjectsHistory& planner_objects_history,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
    const RequiredLateralGapInfo& required_lateral_gap_info,
    const RobotStateSnapshot& robot_state_snapshot,
    const EgoInLaneParams& ego_param,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile,
    const pb::IntentionResult& last_intent_seed,
    const pb::PathReasoningSeed& last_path_reasoning_seed,
    const pb::PullOverInfoSeed& last_pull_over_info_seed,
    const pb::UnstuckModeSeed& unstuck_mode_seed,
    const math::Range1d& planning_horizon_range,
    const math::geometry::PolylineCurve2d& reference_line,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const std::vector<lane_selection::TrafficLightInfoReference>&
        lane_sequence_tl_infos,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const LaneChangeInfo& lane_change_info,
    const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
    const planner::lateral_clearance::LateralClearanceData&
        lateral_clearance_data,
    const pnc_map::Lane& current_lane, bool should_reduce_gap_for_pass_state,
    bool is_ego_near_merge, pb::PathReasoningSeed& path_reasoning_seed,
    bool use_tbb) const {
  TRACE_EVENT_SCOPE(planner, SemanticContext_GenerateObjectReasoningInfoMap);
  using ObjectReasoingInfoMapType =
      std::map<TypedObjectId, ObjectReasoningInfo>;
  ObjectReasoingInfoMapType object_reasoning_info_map;

  // This number should be slightly less than total cores.
  const size_t total_cpu_numbers = common::CPUCoresNumberOfPlanner();
  const auto tbb_thread_num = static_cast<size_t>(
      total_cpu_numbers * common::kHighCoresRatioOfThreadsPool);
  tbb::task_arena tbb_task_arena(tbb_thread_num);
  std::vector<ObjectReasoingInfoMapType> sub_object_reasoning_info_maps;
  sub_object_reasoning_info_maps.resize(tbb_thread_num);

  std::unordered_set<int64_t> pull_over_gap_align_trailing_agent_ids;
  for (const auto& agent_id :
       last_pull_over_info_seed.gap_align_trailing_agent_ids()) {
    pull_over_gap_align_trailing_agent_ids.insert(agent_id);
  }

  auto GenerateOccupancyRelatedBlockingStateMetaTask =
      [&blocking_state_generator = blocking_state_generator_,
       &object_occupancy_state_map, &required_lateral_gap_info,
       &last_intent_seed, &last_path_reasoning_seed, &path_reasoning_seed,
       &unstuck_mode_seed, &planner_objects_history, &lane_sequence_tl_infos,
       &emergency_brake_speed_profile, &lower_bound_speed_profile,
       &upper_bound_speed_profile, &ego_param, &robot_state_snapshot,
       &planning_horizon_range, &reference_line, &lane_change_execution_info,
       &lane_change_info, &sub_object_reasoning_info_maps,
       &lateral_clearance_data, &current_lane, should_reduce_gap_for_pass_state,
       &pull_over_gap_align_trailing_agent_ids, lane_marking_traffic_rule,
       pull_over_destination_meta_ptr, use_tbb, is_ego_near_merge](int k) {
        // If use_tbb == false, only host thread take this path, worker_index =
        // 0.
        size_t worker_index =
            use_tbb ? tbb::this_task_arena::current_thread_index() : 0;
        auto object_id_and_object_map_begin =
            object_occupancy_state_map.begin();
        auto object_id_and_object_occupancy_state_ptr =
            std::next(object_id_and_object_map_begin, k);
        const int64 object_id = object_id_and_object_occupancy_state_ptr->first;
        const std::unique_ptr<planner::ObjectOccupancyState>&
            object_occupancy_state_ptr =
                object_id_and_object_occupancy_state_ptr->second;

        RequiredLateralGap required_lat_gap = gtl::FindOrDie(
            required_lateral_gap_info.obj_id_to_required_lat_gaps, object_id);
        RequiredLateralGap required_lat_gap_for_pass_state = required_lat_gap;
        required_lat_gap_for_pass_state.critical_required_lateral_gap =
            should_reduce_gap_for_pass_state
                ? planner::control_error::
                      GetLateralControlErrorSmallCurvatureInDefaultRange()
                : GetCriticalRequiredLateralGapFromPrincipledRequiredLateralGap<
                      ObjectId>(object_id,
                                required_lateral_gap_info
                                    .object_id_to_required_lateral_gaps);
        AgentSLBoundary agent_sl_boundary(
            object_occupancy_state_ptr->current_snapshot_info()
                .object_occupancy_param());
        // Gets last intention from previous seed.
        const auto intention_iter =
            last_intent_seed.object_intentions().find(object_id);
        const bool was_nudge_intention =
            (intention_iter != last_intent_seed.object_intentions().end()) &&
            std::any_of(intention_iter->second.snapshot_intentions().begin(),
                        intention_iter->second.snapshot_intentions().end(),
                        [](const auto& snapshot_intention) {
                          return snapshot_intention.pass_state() !=
                                 pb::SnapshotIntention::IGNORE;
                        });
        const pb::StuckRegion& stuck_region =
            path_reasoning_seed.xlane_nudge_seed().target_stuck_region();
        const bool is_xlane_nudge_object =
            std::any_of(stuck_region.stuck_objects().begin(),
                        stuck_region.stuck_objects().end(),
                        [&object_id](const pb::StuckObjectInfo& object) {
                          return object.object_id() == object_id &&
                                 object.object_type() ==
                                     pb::ObjectSourceType::kTrackedObject;
                        });

        bool should_trigger_unstuck_for_unknown_objects = std::any_of(
            unstuck_mode_seed.unstuck_modes().begin(),
            unstuck_mode_seed.unstuck_modes().end(),
            [](const auto& unstuck_mode) {
              return unstuck_mode == pb::UnstuckMode::UNSTUCK_UNKNOWN_OBJECTS;
            });
        bool is_pull_over_gap_align_trailing_agent =
            pull_over_gap_align_trailing_agent_ids.find(object_id) !=
            pull_over_gap_align_trailing_agent_ids.end();
        std::unordered_map<int, LateralBlockingStateMetaData>
            trajectory_blocking_state_data_map;
        trajectory_blocking_state_data_map.reserve(
            object_occupancy_state_ptr->predicted_trajectory_occupancy_states()
                .size());
        for (const auto& [trajectory_id, _] :
             object_occupancy_state_ptr
                 ->predicted_trajectory_occupancy_states()) {
          trajectory_blocking_state_data_map.emplace(
              trajectory_id,
              blocking_state_generator.GenerateBlockingStateMetaData(
                  *object_occupancy_state_ptr, planner_objects_history,
                  trajectory_id, required_lat_gap_for_pass_state,
                  lane_sequence_tl_infos, emergency_brake_speed_profile,
                  lower_bound_speed_profile, upper_bound_speed_profile,
                  ego_param, robot_state_snapshot.bounding_box(),
                  planning_horizon_range, agent_sl_boundary,
                  was_nudge_intention, is_xlane_nudge_object,
                  should_trigger_unstuck_for_unknown_objects, reference_line,
                  pull_over_destination_meta_ptr, lane_change_execution_info,
                  lane_change_info, lane_marking_traffic_rule,
                  lateral_clearance_data, current_lane, is_ego_near_merge,
                  is_pull_over_gap_align_trailing_agent));
        }

        LateralBlockingStateMetaData primary_blocking_state_meta_data =
            trajectory_blocking_state_data_map[object_occupancy_state_ptr
                                                   ->primary_trajectory_id()];
        // Estimates agent yield probability.
        const std::optional<planner::pb::YieldIntentionData> yield_data =
            EstimateAgentYieldIntention(ego_param, *object_occupancy_state_ptr,
                                        primary_blocking_state_meta_data,
                                        last_path_reasoning_seed);

        ObjectReasoningInfo object_info(
            *object_occupancy_state_ptr, std::move(required_lat_gap),
            std::move(required_lat_gap_for_pass_state), yield_data,
            std::move(agent_sl_boundary),
            std::move(primary_blocking_state_meta_data),
            std::move(trajectory_blocking_state_data_map));

        sub_object_reasoning_info_maps[worker_index].emplace(
            object_info.object_id(), std::move(object_info));
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(object_occupancy_state_map.size()),
                        GenerateOccupancyRelatedBlockingStateMetaTask);
    });
  } else {
    for (size_t k = 0; k < object_occupancy_state_map.size(); ++k) {
      GenerateOccupancyRelatedBlockingStateMetaTask(k);
    }
  }

  // Stores current agent yield probability in the seed.
  for (const auto& sub_map : sub_object_reasoning_info_maps) {
    for (const auto& [object_id, object_info] : sub_map) {
      if (object_info.yield_intention_data().has_value()) {
        (*path_reasoning_seed.mutable_object_intention_seed()
              ->mutable_yield_object_map())[object_id.id] =
            object_info.yield_intention_data().value();
      }
    }
  }

  double avg_abs_ref_path_curvature = 0.0;
  const double ego_current_speed_mps = ego_param.speed_mps;
  if (std::any_of(construction_zone_states.begin(),
                  construction_zone_states.end(),
                  [](const auto& cz) { return cz.is_map_labeled_cz; })) {
    // Right now there are some latency concern for the below function, so we
    // will hide it behind flag.
    avg_abs_ref_path_curvature = CalculateAvgAbsRefPathCurvature(
        reference_line, ego_param.front_bumper_arclength);
  }

  // Generate construction zone related objects info map.
  auto GenerateCzRelatedBlockingStateMetaTask =
      [&blocking_state_generator = blocking_state_generator_,
       &required_lateral_gap_info, &last_intent_seed,
       &emergency_brake_speed_profile, &lower_bound_speed_profile,
       &upper_bound_speed_profile, &ego_param, &robot_state_snapshot,
       &construction_zone_states, planning_horizon_range,
       &sub_object_reasoning_info_maps, should_reduce_gap_for_pass_state,
       avg_abs_ref_path_curvature, ego_current_speed_mps,
       pull_over_destination_meta_ptr, use_tbb](int k) {
        size_t worker_index =
            use_tbb ? tbb::this_task_arena::current_thread_index() : 0;
        const auto& cz_state = construction_zone_states[k];
        const auto& cz_id_to_required_lat_gaps =
            required_lateral_gap_info.cz_id_to_required_lat_gaps;
        const auto& cz_id_to_plg_iter =
            required_lateral_gap_info.cz_id_to_required_lateral_gaps.find(
                cz_state.id);
        const auto gap_iter = cz_id_to_required_lat_gaps.find(cz_state.id);
        RequiredLateralGap required_lat_gap =
            gap_iter !=
                    required_lateral_gap_info.cz_id_to_required_lat_gaps.end()
                ? gap_iter->second
                : RequiredLateralGap(0.4, 0.8);
        if (cz_state.is_map_labeled_cz) {
          required_lat_gap = CalculatePhysicalBoundaryRequiredLateralGap(
              cz_id_to_plg_iter->second, avg_abs_ref_path_curvature,
              ego_current_speed_mps);
        }
        RequiredLateralGap required_lat_gap_for_pass_state = required_lat_gap;
        required_lat_gap_for_pass_state.critical_required_lateral_gap =
            should_reduce_gap_for_pass_state
                ? 0.0
                : GetCriticalRequiredLateralGapFromPrincipledRequiredLateralGap<
                      ConstructionZoneId>(
                      cz_state.id,
                      required_lateral_gap_info.cz_id_to_required_lateral_gaps);
        AgentSLBoundary cz_sl_boundary(cz_state);
        const auto intention_iter =
            last_intent_seed.construction_zone_intentions().find(cz_state.id);
        const bool was_nudge_intention =
            (intention_iter !=
             last_intent_seed.construction_zone_intentions().end()) &&
            std::any_of(intention_iter->second.snapshot_intentions().begin(),
                        intention_iter->second.snapshot_intentions().end(),
                        [](const auto& snapshot_intention) {
                          return snapshot_intention.pass_state() !=
                                 pb::SnapshotIntention::IGNORE;
                        });

        LateralBlockingStateMetaData blocking_state_data =
            blocking_state_generator
                .GenerateConstructionZoneBlockingStateMetaData(
                    cz_state, required_lat_gap_for_pass_state,
                    emergency_brake_speed_profile, lower_bound_speed_profile,
                    upper_bound_speed_profile, ego_param,
                    robot_state_snapshot.bounding_box(), planning_horizon_range,
                    cz_sl_boundary, pull_over_destination_meta_ptr,
                    was_nudge_intention);
        ObjectReasoningInfo object_info(
            cz_state, std::move(required_lat_gap),
            std::move(required_lat_gap_for_pass_state),
            std::move(cz_sl_boundary), std::move(blocking_state_data));

        sub_object_reasoning_info_maps[worker_index].emplace(
            object_info.object_id(), std::move(object_info));
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(construction_zone_states.size()),
                        GenerateCzRelatedBlockingStateMetaTask);
    });
  } else {
    for (size_t k = 0; k < construction_zone_states.size(); ++k) {
      GenerateCzRelatedBlockingStateMetaTask(k);
    }
  }

  // Map merge
  for (auto&& sub_object_reasoning_info_map : sub_object_reasoning_info_maps) {
    object_reasoning_info_map.merge(std::move(sub_object_reasoning_info_map));
  }

  return object_reasoning_info_map;
}

std::vector<FrameAnalyzerResult> SemanticContext::AnalyzeFrame(
    const WorldModel& world_model, const speed::Profile& speed_upper_bound,
    const LaneSequenceResult& lane_sequence_result,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const EgoInLaneParams& ego_param,
    const lane_selection::LaneSequenceTrafficRules& traffic_rules,
    const math::Range1d& planning_horizon_range,
    const std::map<TypedObjectId, ObjectReasoningInfo>&
        object_reasoning_info_map,
    const AssistInstruction* assist_instruction,
    const lateral_clearance::LateralClearanceData& lateral_clearance_data,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    pb::PathReasoningSeed* path_reasoning_seed,
    pb::SemanticContextDebug* debug) const {
  TRACE_EVENT_SCOPE(planner, SemanticContext_AnalyzeFrame);
  // Update all the scene detectors and get the related hazardous scene
  // detection results.
  std::vector<FrameAnalyzerResult> frame_analyzer_results(
      pb::FrameAnalysis::FrameType_ARRAYSIZE);
  // TODO(zixuan): we can not support pullover because of
  // planning_horizon_range is not correspond to lane_sequence_geometry.
  // TODO(xuzhe): we can not support reverse driving because of lane sequence
  // is not reversed.
  // TODO(Jiakai): We cannot support open-space for now.
  if (behavior_type_ == pb::BehaviorType::DECOUPLED_PULL_OVER ||
      behavior_type_ == pb::BehaviorType::REVERSE ||
      behavior_type_ == pb::BehaviorType::OPEN_SPACE) {
    frame_analyzer_results[pb::FrameAnalysis::IN_LANE].frame_type =
        pb::FrameAnalysis::IN_LANE;
    frame_analyzer_results[pb::FrameAnalysis::LEFT_LANE].frame_type =
        pb::FrameAnalysis::LEFT_LANE;
    frame_analyzer_results[pb::FrameAnalysis::RIGHT_LANE].frame_type =
        pb::FrameAnalysis::RIGHT_LANE;
    frame_analyzer_results[pb::FrameAnalysis::ROAD_SECTION].frame_type =
        pb::FrameAnalysis::ROAD_SECTION;
    return frame_analyzer_results;
  }

  // Note(yongbing): we only support lane follow and lane change with semantic
  // context.
  const bool is_lane_keep_lane_sequence =
      (behavior_type_ == pb::BehaviorType::LANE_KEEP) &&
      (lane_keep_behavior_type_ == pb::LaneKeepBehaviorType::LK_DEFAULT ||
       lane_keep_behavior_type_ == pb::LaneKeepBehaviorType::LK_LEFT_XLANE ||
       lane_keep_behavior_type_ == pb::LaneKeepBehaviorType::LK_RIGHT_XLANE);
  if (is_lane_keep_lane_sequence) {
    tbb::parallel_for(
        0, pb::FrameAnalysis::FrameType_ARRAYSIZE,
        [&frame_analyzer_results, &world_model, &speed_upper_bound,
         &lane_sequence_result, &lane_sequence_geometry, &ego_param,
         &traffic_rules, &planning_horizon_range, &object_reasoning_info_map,
         &assist_instruction, &lateral_clearance_data, &previous_iter_seed,
         &path_reasoning_seed, &debug, this](int idx) {
          const auto frame_type =
              static_cast<pb::FrameAnalysis::FrameType>(idx);
          switch (frame_type) {
            case pb::FrameAnalysis::IN_LANE:
              frame_analyzer_results[idx] =
                  EgoLaneFrameAnalyzer(behavior_type_, agent_intention_config_)
                      .Analyze(
                          world_model, speed_upper_bound, candidates_,
                          *DCHECK_NOTNULL(lane_sequence_result.current_lane),
                          lane_sequence_result.lane_sequence,
                          lane_sequence_geometry, traffic_rules,
                          planning_horizon_range, ego_param,
                          object_reasoning_info_map, assist_instruction,
                          lateral_clearance_data, previous_iter_seed,
                          path_reasoning_seed,
                          debug == nullptr
                              ? nullptr
                              : debug->mutable_ego_lane_frame_debug());
              return;
            case pb::FrameAnalysis::ROAD_SECTION:
              frame_analyzer_results[idx] =
                  RoadSectionFrameAnalyzer(behavior_type_,
                                           lane_keep_behavior_type_)
                      .Analyze(
                          lane_sequence_geometry, planning_horizon_range,
                          world_model.robot_state().plan_init_state_snapshot(),
                          ego_param,
                          world_model.lane_blockage_detector()
                              .stationary_duration_map(),
                          object_reasoning_info_map, *path_reasoning_seed,
                          assist_instruction, lateral_clearance_data,
                          previous_iter_seed.path_reasoning_seed()
                              .narrow_meeting_seed());
              return;
            case pb::FrameAnalysis::LEFT_LANE:
            case pb::FrameAnalysis::RIGHT_LANE:
              frame_analyzer_results[idx] = SideLaneFrameAnalyzer().Analyze(
                  ego_param,
                  world_model.lane_blockage_detector().lane_blockages(),
                  object_reasoning_info_map, frame_type);
              return;
            default:
              DCHECK(false) << "Unsupportable frame type : "
                            << pb::FrameAnalysis::FrameType_Name(frame_type);
              return;
          }
        });
    DCHECK(frame_analyzer_results[pb::FrameAnalysis::IN_LANE].frame_type ==
           pb::FrameAnalysis::IN_LANE);
    DCHECK(frame_analyzer_results[pb::FrameAnalysis::LEFT_LANE].frame_type ==
           pb::FrameAnalysis::LEFT_LANE);
    DCHECK(frame_analyzer_results[pb::FrameAnalysis::RIGHT_LANE].frame_type ==
           pb::FrameAnalysis::RIGHT_LANE);
    DCHECK(frame_analyzer_results[pb::FrameAnalysis::ROAD_SECTION].frame_type ==
           pb::FrameAnalysis::ROAD_SECTION);
  } else {
    // Only consider the inlane nudge objects for other maneuvers.
    frame_analyzer_results[pb::FrameAnalysis::IN_LANE] =
        EgoLaneFrameAnalyzer(behavior_type_, agent_intention_config_)
            .Analyze(ego_param, traffic_rules, object_reasoning_info_map);
    // Empty for other frames.
    frame_analyzer_results[pb::FrameAnalysis::LEFT_LANE].frame_type =
        pb::FrameAnalysis::LEFT_LANE;
    frame_analyzer_results[pb::FrameAnalysis::RIGHT_LANE].frame_type =
        pb::FrameAnalysis::RIGHT_LANE;
    frame_analyzer_results[pb::FrameAnalysis::ROAD_SECTION].frame_type =
        pb::FrameAnalysis::ROAD_SECTION;
  }

  return frame_analyzer_results;
}

ScenarioIdentifierResult SemanticContext::CategorizeScenario(
    const std::map<TypedObjectId, ObjectReasoningInfo>&
        object_reasoning_info_map,
    const EgoInLaneParams& ego_param,
    const std::vector<FrameAnalyzerResult>& frame_analyzer_results,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    pb::PathReasoningSeed* path_reasoning_seed) const {
  DCHECK_NE(path_reasoning_seed, nullptr);
  const ScenarioIdentifierResult xlane_scenario_result =
      xlane_nudge_identifier_.Identify(
          object_reasoning_info_map, ego_param, frame_analyzer_results,
          traffic_rule_reasoning_info, path_reasoning_seed);
  if (xlane_scenario_result.scenario_type ==
      pb::ScenarioRecognition::XLANE_NUDGE) {
    DCHECK(xlane_scenario_result.xlane_nudge_scene.has_value());
    return xlane_scenario_result;
  }

  const ScenarioIdentifierResult acc_scenario_result =
      avoid_nudge_identifier_.Identify(object_reasoning_info_map, ego_param,
                                       frame_analyzer_results,
                                       traffic_rule_reasoning_info);
  if (acc_scenario_result.scenario_type ==
      pb::ScenarioRecognition::AVOID_NUDGE) {
    DCHECK(acc_scenario_result.avoid_nudge_scene.has_value());
    return acc_scenario_result;
  }

  return inlane_nudge_identifier_.Identify(object_reasoning_info_map, ego_param,
                                           frame_analyzer_results,
                                           traffic_rule_reasoning_info);
}

NarrowMeetingSceneParams SemanticContext::GenerateNarrowMeetingSceneParams(
    pb::ScenarioRecognition::ScenarioType scenario_type,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const math::Range1d& planning_horizon_range,
    const RobotStateSnapshot& robot_state_snapshot,
    const EgoInLaneParams& ego_param,
    const std::map<TypedObjectId, ObjectReasoningInfo>&
        object_reasoning_info_map,
    const pnc_map::Lane* current_lane, const speed::Profile& speed_lower_bound,
    const pb::NarrowMeetingSeed& last_seed,
    const lateral_clearance::LateralClearanceData& lateral_clearance_data,
    bool is_in_autonomous_mode, pb::NarrowMeetingSeed* mutable_seed) const {
  DCHECK(current_lane);
  TRACE_EVENT_SCOPE(planner, SemanticContext_GenerateNarrowMeetingSceneParams);
  return NarrowMeetingProcess(
      behavior_type_, scenario_type, robot_state_snapshot, ego_param,
      object_reasoning_info_map, current_lane, lane_sequence_geometry,
      speed_lower_bound, planning_horizon_range, lateral_clearance_data,
      last_seed, /*is_attraction_necessary=*/false, is_in_autonomous_mode,
      mutable_seed);
}

void SemanticContext::UpdateImaginaryAgent(
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    pb::MotionMode motion_mode) {
  TRACE_EVENT_SCOPE(planner, SemanticContext_UpdateImaginaryAgent);
  // Generate imaginary tracked objects and their prediction trajectory in
  // lane states.
  // 1. Generate hallucinated agent in lane states. Note(zhihaoruan): the
  // will_ego_borrow_opposite_road is only meaningful when the
  // hallucinated_agent_manager is inside the speed pipeline and dealing with
  // XLANE homotopy. As a result, it's safe for us to set it as a default
  // value of FALSE.
  imaginary_agent_in_lane_state_generator_ =
      std::make_unique<lane_selection::AgentInLaneStateGenerator>();
  imaginary_planner_object_map_.clear();
  hallucinate_agent_manager_ =
      std::make_unique<speed::HallucinatedAgentManager>(
          speed::HallucinatedAgentManager(
              world_model.pose(), world_model.planner_object_map(),
              world_model.object_prediction_map(),
              world_model.occlusion_checker(),
              lane_sequence_info.lane_sequence_geometry().nominal_path,
              lane_sequence_info.lane_sequence_iterator(),
              world_model.traffic_light_detection(),
              previous_decoupled_maneuver_seed, world_model.robot_state(),
              *world_model.GetLatestJointPncMapService(),
              /*will_ego_borrow_opposite_road=*/false, motion_mode,
              world_model.snapshot_timestamp(), /*filter_crosswalk=*/true,
              /*debug_proto_ptr=*/nullptr));
  if (!FLAGS_planning_deprecate_agent_in_lane_state) {
    imaginary_agent_in_lane_state_generator_->Update(
        lane_sequence_info.lane_sequence_geometry(),
        lane_sequence_info.robot_in_lane_param(),
        lane_sequence_info.traffic_rules(),
        hallucinate_agent_manager_->hallucinated_planner_object_map(),
        motion_mode, pb::AgentImaginaryActualType::IMAGINARY);
    std::unordered_map<TypedObjectId, ObjectInformation> relevant_object_map;
    imaginary_agent_in_lane_state_generator_
        ->ComputeAgentTrajectoryInLaneStates(
            hallucinate_agent_manager_->hallucinated_planner_object_map(),
            relevant_object_map, motion_mode);
  }
  imaginary_planner_object_map_ =
      hallucinate_agent_manager_->hallucinated_planner_object_map();
  for (const auto& pair :
       hallucinate_agent_manager_->hallucinated_object_prediction_map()) {
    prediction_map_for_imaginary_planner_object_.insert(pair);
  }
  imaginary_objects_required_lateral_gap_info_ =
      std::make_unique<RequiredLateralGapInfo>(RequiredLateralGapInfo(
          ComputePrincipledRequiredLateralGapForPlannerObjects(
              imaginary_planner_object_map_,
              PlannerConfigCenter::GetInstance()
                  .GetDecoupledForwardManeuverConfig()
                  .required_lateral_gap_config()),
          {}, GetRequiredLateralGapForHardBoundaries()));
  // Generate imaginary object occupancy state map when the flag is on.
  if (FLAGS_planning_enable_object_occupancy_state) {
    auto plan_init_object_info_map = GeneratePlanInitTsObjectInfoMap(
        imaginary_planner_object_map(),
        lane_sequence_info.robot_in_lane_param(),
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        lane_sequence_info.lane_sequence_geometry().left_lane_boundary,
        lane_sequence_info.lane_sequence_geometry().right_lane_boundary,
        motion_mode);
    std::unordered_map<TypedObjectId, ObjectInformation> relevant_object_map;
    imaginary_object_occupancy_state_map_ = GenerateObjectOccupancyStateMap(
        imaginary_planner_object_map(),
        prediction_map_for_imaginary_planner_object(),
        lane_sequence_info.lane_sequence_geometry().nominal_path,
        lane_sequence_info.lane_sequence_geometry().left_lane_boundary,
        lane_sequence_info.lane_sequence_geometry().right_lane_boundary,
        lane_sequence_info.robot_in_lane_param(), relevant_object_map,
        std::move(plan_init_object_info_map), motion_mode,
        pb::AgentImaginaryActualType::IMAGINARY);
  }
}

}  // namespace path
}  // namespace planner
