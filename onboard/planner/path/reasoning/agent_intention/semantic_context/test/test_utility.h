#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_TEST_TEST_UTILITY_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_TEST_TEST_UTILITY_H_

#include <algorithm>
#include <limits>
#include <map>
#include <memory>
#include <unordered_map>
#include <utility>
#include <vector>

#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/lateral_blocking_state_generator.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/object_reasoning_info.h"
#include "planner/path/reasoning/agent_intention/test_utils/path_reasoning_test_utils.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/world_model/world_model.h"

namespace planner {
namespace path {

namespace test {

// Inspect boundary line as expected.
void CheckBoundaryLine(const math::geometry::PolylineCurve2d& boundary_line,
                       const math::geometry::PolylineCurve2d& expected_line,
                       double tolerance_m = 1e-2) {
  ASSERT_EQ(boundary_line.size(), expected_line.size());
  for (size_t i = 0; i < expected_line.size(); i++) {
    EXPECT_NEAR(boundary_line.polyline()[i].x(),
                expected_line.polyline()[i].x(), tolerance_m);
    EXPECT_NEAR(boundary_line.polyline()[i].y(),
                expected_line.polyline()[i].y(), tolerance_m);
  }
}

// Returns lateral blocking state generator config for frame analyzer.
pb::LateralBlockingStateGeneratorConfig
GetLateralBlockingStateGeneratorConfig() {
  pb::LateralBlockingStateGeneratorConfig config;
  config.set_max_intention_horizon_ms(8000);
  config.set_nudge_intention_longitudinal_buffer_m(3.0);
  config.set_min_clearance_as_soft_blocking_m(0.0);
  config.set_use_inlane_based_blocking_state(false);
  return config;
}

prediction::pb::Agent GetAgentProto() {
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(/*object_id=*/1);
  tracked_object.set_center_x(0.0);
  tracked_object.set_center_y(0.0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_width(1.0);
  tracked_object.set_length(2.0);
  tracked_object.set_height(2.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_omega(0.0);
  tracked_object.set_velocity(1.0);
  return agent_proto;
}

// Returns a planner object.
PlannerObject GetPlannerObject(const prediction::pb::Agent& agent_proto) {
  return PlannerObject(
      agent_proto, /*timestamp=*/0,
      /*pose_at_plan_init_ts=*/
      TrafficParticipantPose(/*timestamp=*/0, agent_proto.tracked_object()));
}

// Returns upper_bound_speed.
speed::Profile GetSpeedUpperBound(int num_step = 50) {
  // Initialize upper bound speed.
  speed::State init_state(/*t=*/0.0, /*x=*/0.0, /*v=*/2.0, /*a=*/0.0,
                          /*j=*/0.0);
  speed::Profile upper_bound_speed;
  upper_bound_speed.push_back(init_state);
  for (int i = 0; i < num_step; i++) {
    upper_bound_speed.push_back(upper_bound_speed.back().Move(0.1));
  }
  return upper_bound_speed;
}

// Returns lower_bound_speed.
speed::Profile GetSpeedLowerBound(int num_step = 50) {
  // Initialize lower bound speed.
  speed::Profile lower_bound_speed = speed::CreateConstantSpeedProfileFromTime(
      /*t0=*/0.0, /*x0=*/0.0, /*v0=*/0.1, num_step, /*dt=*/0.1);
  speed::Limits limits(
      speed::LimitRange(/*brake_a.min=*/-2.0, /*brake_a.max=*/2.0),
      speed::LimitRange(/*accel_a.min=*/-2.0, /*accel_a.max=*/2.0),
      speed::LimitRange(/*brake_j.min=*/-3.0, /*brake_j.max=*/2.5),
      speed::LimitRange(/*accel_j.min=*/-2.0, /*accel_j.max=*/2.0),
      /*max_v=*/33.33, /*max_brake2accel_j=*/2.5);
  speed::GenerateBrakeProfile(0, num_step, limits, 0.1, &lower_bound_speed);
  return lower_bound_speed;
}

// Returns the ObjectReasoningInfo of each object(agent and construction
// zone).
std::map<TypedObjectId, ObjectReasoningInfo> GenerateObjectReasoningInfoMap(
    const AgentInLaneStatesMap& agent_state_map,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
    const math::geometry::PolylineCurve2d& reference_line) {
  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map;
  const RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.0,
                         /*comfort_required_lateral_gap=*/0.0);
  const auto agent_proto = GetAgentProto();
  PlannerObject planner_object = GetPlannerObject(agent_proto);
  const std::vector<PredictedTrajectoryWrapper> object_predictions;
  const math::geometry::OrientedBox2d ego_bounding_box(
      /*x=*/10.0, /*y=*/0.0, /*length=*/5.0, /*width=*/0.5, /*heading=*/0.0);
  const speed::Profile lower_bound_speed = GetSpeedLowerBound();
  const speed::Profile upper_bound_speed = GetSpeedUpperBound();
  // This can be a problem because the ego params are defined inside this
  // function. If the unit tests have different ego params, they will not be
  // correctly used.
  const EgoInLaneParams ego_param = planner::test::GetEgoInLaneParams();
  auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
  PlannerObjectsHistory planner_objects_history(2);
  auto planner_object_map_ptr =
      std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
  planner_object_map_ptr->emplace(planner_object.id(), planner_object);
  planner_objects_history.Update(planner_object_map_ptr, destruction_pool);
  tbb::concurrent_unordered_map<int64_t,
                                std::vector<PredictedTrajectoryWrapper>>
      object_predictions_;
  // Set up physical lateral clearance data.
  lateral_clearance::Clearance left_clearance;
  left_clearance.distance = 3.0;
  lateral_clearance::Clearance right_clearance;
  right_clearance.distance = -2.0;
  lateral_clearance::LateralClearance lateral_clearance{left_clearance,
                                                        right_clearance};

  const math::Range1d effective_reference_range(0.0, 20.0);
  planner::lateral_clearance::LateralClearanceData
      physical_boundary_lateral_clearance_data =
          lateral_clearance::LateralClearanceData(
              /*resolution=*/1.0, /*max_abs_lateral_clearance=*/10.0,
              effective_reference_range,
              std::vector<lateral_clearance::LateralClearance>(
                  21, lateral_clearance));
  LateralBlockingStateGenerator blocking_state_generator(
      /*config=*/GetLateralBlockingStateGeneratorConfig(),
      /*behavior_type=*/pb::LANE_KEEP);
  for (const auto& [agent_id, agent_state] : agent_state_map) {
    AgentSLBoundary object_sl_boundary(agent_state->tracked_state.inlane_param);
    planner_object.set_is_primary_stationary(
        agent_state->agent_metadata.features[pb::AgentFeature::IS_STATIONARY]);
    LateralBlockingStateMetaData blocking_state_data =
        blocking_state_generator.GenerateBlockingStateMetaData(
            *agent_state, planner_object, planner_objects_history,
            object_predictions_, agent_state->primary_trajectory_id,
            required_lat_gap,
            /*ls_tl_infos=*/{},
            /*emergency_brake_speed_profile=*/lower_bound_speed,
            /*lower_bound_speed_profile=*/lower_bound_speed, upper_bound_speed,
            ego_param, ego_bounding_box, math::Range1d(0.0, 100.0),
            object_sl_boundary,
            /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
            /*should_trigger_unstuck_for_unknown_objects=*/false,
            reference_line,
            /*pull_over_destination_meta_ptr=*/nullptr,
            /*lane_change_execution_info=*/std::nullopt,
            /*lane_change_info=*/{},
            /*lane_marking_traffic_rule=*/nullptr,
            physical_boundary_lateral_clearance_data,
            /*current_lane=*/
            planner::test::GenerateLane(CreateStraightLaneSequenceGeometry()),
            /*is_ego_near_merge=*/false,
            /*is_pull_over_gap_align_trailing_agent=*/false);
    std::unordered_map<int, LateralBlockingStateMetaData>
        trajectory_blocking_state_data_map = {
            {agent_state->primary_trajectory_id, blocking_state_data}};
    ObjectReasoningInfo object_info(
        *agent_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        /*yield_intention_data=*/std::nullopt, std::move(object_sl_boundary),
        std::move(blocking_state_data),
        std::move(trajectory_blocking_state_data_map));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  for (const auto& cz_state : construction_zone_states) {
    AgentSLBoundary object_sl_boundary(cz_state);
    LateralBlockingStateMetaData blocking_state_data =
        blocking_state_generator.GenerateConstructionZoneBlockingStateMetaData(
            cz_state, required_lat_gap,
            /*emergency_brake_speed_profile=*/lower_bound_speed,
            /*lower_bound_speed_profile=*/lower_bound_speed, upper_bound_speed,
            ego_param, ego_bounding_box, math::Range1d(0.0, 100.0),
            object_sl_boundary,
            /*pull_over_destination_meta_ptr=*/nullptr,
            /*was_nudge_intention=*/false);
    ObjectReasoningInfo object_info(
        cz_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        std::move(object_sl_boundary), std::move(blocking_state_data));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  return object_reasoning_info_map;
}

std::map<TypedObjectId, ObjectReasoningInfo>
GenerateObjectReasoningInfoMapFromOccupancyState(
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
    const math::geometry::PolylineCurve2d& reference_line,
    const EgoInLaneParams& ego_param) {
  std::map<TypedObjectId, ObjectReasoningInfo> object_reasoning_info_map;
  const RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.0,
                         /*comfort_required_lateral_gap=*/0.0);
  const math::geometry::OrientedBox2d ego_bounding_box(
      /*x=*/10.0, /*y=*/0.0, /*length=*/5.0, /*width=*/0.5, /*heading=*/0.0);
  const speed::Profile lower_bound_speed = GetSpeedLowerBound();
  const speed::Profile upper_bound_speed = GetSpeedUpperBound();
  const auto agent_proto = GetAgentProto();
  const PlannerObject planner_object = GetPlannerObject(agent_proto);
  auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
  PlannerObjectsHistory planner_objects_history(2);
  auto planner_object_map_ptr =
      std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
  planner_object_map_ptr->emplace(planner_object.id(), planner_object);
  planner_objects_history.Update(planner_object_map_ptr, destruction_pool);

  // Set up physical lateral clearance data.
  lateral_clearance::Clearance left_clearance;
  left_clearance.distance = 3.0;
  lateral_clearance::Clearance right_clearance;
  right_clearance.distance = -2.0;
  lateral_clearance::LateralClearance lateral_clearance{left_clearance,
                                                        right_clearance};

  const math::Range1d effective_reference_range(0.0, 20.0);
  planner::lateral_clearance::LateralClearanceData
      physical_boundary_lateral_clearance_data =
          lateral_clearance::LateralClearanceData(
              /*resolution=*/1.0, /*max_abs_lateral_clearance=*/10.0,
              effective_reference_range,
              std::vector<lateral_clearance::LateralClearance>(
                  21, lateral_clearance));

  LateralBlockingStateGenerator blocking_state_generator(
      /*config=*/GetLateralBlockingStateGeneratorConfig(),
      /*behavior_type=*/pb::LANE_KEEP);
  for (const auto& [agent_id, object_occupancy_state] :
       object_occupancy_state_map) {
    AgentSLBoundary object_sl_boundary(
        object_occupancy_state->current_snapshot_info()
            .object_occupancy_param());
    LateralBlockingStateMetaData blocking_state_data =
        blocking_state_generator.GenerateBlockingStateMetaData(
            *object_occupancy_state, planner_objects_history,
            object_occupancy_state->primary_trajectory_id(), required_lat_gap,
            /*ls_tl_infos=*/{},
            /*emergency_brake_speed_profile=*/lower_bound_speed,
            /*lower_bound_speed_profile=*/lower_bound_speed, upper_bound_speed,
            ego_param, ego_bounding_box, math::Range1d(0.0, 100.0),
            object_sl_boundary,
            /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
            /*should_trigger_unstuck_for_unknown_objects=*/false,
            reference_line,
            /*pull_over_destination_meta_ptr=*/nullptr,
            /*lane_change_execution_info=*/std::nullopt,
            /*lane_change_info=*/{},
            /*lane_marking_traffic_rule=*/nullptr,
            physical_boundary_lateral_clearance_data,
            /*current_lane=*/
            planner::test::GenerateLane(CreateStraightLaneSequenceGeometry()),
            /*is_ego_near_merge=*/false,
            /*is_pull_over_gap_align_trailing_agent=*/false);
    std::unordered_map<int, LateralBlockingStateMetaData>
        trajectory_blocking_state_data_map = {
            {object_occupancy_state->primary_trajectory_id(),
             blocking_state_data}};
    ObjectReasoningInfo object_info(
        *object_occupancy_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        /*yield_intention_data=*/std::nullopt, std::move(object_sl_boundary),
        std::move(blocking_state_data),
        std::move(trajectory_blocking_state_data_map));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  for (const auto& cz_state : construction_zone_states) {
    AgentSLBoundary object_sl_boundary(cz_state);
    LateralBlockingStateMetaData blocking_state_data =
        blocking_state_generator.GenerateConstructionZoneBlockingStateMetaData(
            cz_state, required_lat_gap,
            /*emergency_brake_speed_profile=*/lower_bound_speed,
            /*lower_bound_speed_profile=*/lower_bound_speed, upper_bound_speed,
            ego_param, ego_bounding_box, math::Range1d(0.0, 100.0),
            object_sl_boundary,
            /*pull_over_destination_meta_ptr=*/nullptr,
            /*was_nudge_intention=*/false);
    ObjectReasoningInfo object_info(
        cz_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        std::move(object_sl_boundary), std::move(blocking_state_data));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  return object_reasoning_info_map;
}

}  // namespace test

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_TEST_TEST_UTILITY_H_
