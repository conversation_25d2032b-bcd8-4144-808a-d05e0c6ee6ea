#include "planner/world_model/waypoint_assist/waypoint_assist_tracker.h"

#include <algorithm>
#include <limits>
#include <map>
#include <optional>
#include <set>
#include <string>
#include <unordered_set>

#include "base/now.h"
#include "geometry/algorithms/specialization/distance_point2d.h"
#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/planning_service.h"
#include "planner_protos/behavior_reasoner.pb.h"

#include "planner/behavior/maneuvers/util/maneuver_common.h"
#include "planner/behavior/util/lane_sequence/waypoint_assist_lane_sequence_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/utility/common/common_utility.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {
// The distance buffer for evaluating whether ego arrives at the waypoint assist
// destination.
constexpr double kWaypointAssistDestinationBufferInMeter = 2.0;
// The Additional buffer for hard corridor boundary.
constexpr double kAdditionalHardBoundaryBufferInMeter = 0.5;
// Recommend min size of point to be simplify and smooth.
constexpr int KMinSizeOfPointToSimplifyAndSmooth = 4;
// The max arclength to back trace.
constexpr double kMaxBackTraceArclength = 50.0;
// The maximum distance requirement between drawing points
constexpr double kMaxDistanceBetweenDrawingPoints = 10.0;
// The time threshold for ego return to kIdle from kExitReasoning when stuck.
constexpr int64_t kTimeThresholdReturnIdleFromExitReasoningMs = 5000;
// Ignore the lane which's start point to ego exceeds the maximum threshold.
constexpr double kMaxDistBetweenStartAndEgo = 100.0;  // m
// The maximum distance to search drawable lane.
constexpr double kMaxDistSearchDrawableLane = 100.0;  // m

// Check whether the given reference_curve and hard_boundary_line Intersect.
bool CheckIntersect(
    const math::geometry::Polyline<math::geometry::Point2d>& reference_curve,
    const math::geometry::PolylineCurve2d& hard_boundary_line,
    std::vector<math::geometry::Point2d>& intersected_points) {
  math::geometry::Intersection(reference_curve, hard_boundary_line,
                               intersected_points);
  return !intersected_points.empty();
}

// Check whether reference curve and hard boundary (both
// left and right hard boundary should be checked) intersect.
pb::PathFailureType IsReferenceCurveIntersectWithHardBoundary(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const math::geometry::Polyline<math::geometry::Point2d>& reference_curve,
    const pnc_map::Lane* current_lane) {
  std::vector<math::geometry::Point2d> intersected_points;
  const auto current_road =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Road>(
          current_lane->section()->road()->id(),
          pnc_map::ElementQueryType::kOnlineFirst);
  for (const auto& hard_boundary_line :
       current_road->left_boundary().hard_boundary_lines) {
    if (CheckIntersect(reference_curve, hard_boundary_line.hard_boundary,
                       intersected_points)) {
      return pb::PathFailureType::PATH_VIOLATE_LEFT_HARD_BOUNDARY;
    }
  }
  for (const auto& hard_boundary_line :
       current_road->right_boundary().hard_boundary_lines) {
    if (CheckIntersect(reference_curve, hard_boundary_line.hard_boundary,
                       intersected_points)) {
      return pb::PathFailureType::PATH_VIOLATE_RIGHT_HARD_BOUNDARY;
    }
  }
  // Get successor road hard boundary of current road. Because hard boundary in
  // lane_sequence_geometry may miss the hard boundary of lanes where reference
  // points located in.
  const std::vector<const pnc_map::Road*> successor_roads =
      current_lane->section()->road()->successors();
  for (const auto* road_ptr : successor_roads) {
    const auto successor_road =
        joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Road>(
            road_ptr->id(), pnc_map::ElementQueryType::kOnlineFirst);
    for (const auto& hard_boundary_line :
         successor_road->left_boundary().hard_boundary_lines) {
      if (CheckIntersect(reference_curve, hard_boundary_line.hard_boundary,
                         intersected_points)) {
        return pb::PathFailureType::PATH_VIOLATE_LEFT_HARD_BOUNDARY;
      }
    }
    for (const auto& hard_boundary_line :
         successor_road->right_boundary().hard_boundary_lines) {
      if (CheckIntersect(reference_curve, hard_boundary_line.hard_boundary,
                         intersected_points)) {
        return pb::PathFailureType::PATH_VIOLATE_RIGHT_HARD_BOUNDARY;
      }
    }
  }
  return pb::PathFailureType::PATH_VALID;
}

// Calculate the maximum distance between the ego and the polyline in the
// vertical direction of the polyline
std::optional<double> CalculateMaxDistance(
    const math::geometry::Polygon<math::geometry::Point2d>& rectangle,
    const math::geometry::PolylineCurve2d& polyline) {
  DCHECK_GT(polyline.size(), 0);
  std::vector<double> vertex(rectangle.size(), INT_MAX);
  double maxDistance = 0.0;
  for (size_t i = 0; i < rectangle.size(); ++i) {
    const auto& dist_forbid_exten =
        polyline.GetProximity(rectangle[i], math::pb::UseExtensionFlag::kForbid)
            .dist;
    vertex[i] = std::min(vertex[i], dist_forbid_exten);
    maxDistance = std::max(maxDistance, vertex[i]);
  }
  return std::make_optional(maxDistance + kAdditionalHardBoundaryBufferInMeter);
}

// Calculate lanes where reference points located in, will keep only one if two
// lane located in the same road.
std::optional<std::vector<const pnc_map::Lane*>> GetLanesForReferencePoints(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const ::google::protobuf::RepeatedPtrField<::voy::Point2d>&
        reference_points) {
  math::geometry::Polyline2d raw_polyline;
  raw_polyline.reserve(reference_points.size());
  for (const auto& reference_point : reference_points) {
    raw_polyline.emplace_back(reference_point.x(), reference_point.y());
  }
  if (raw_polyline.size() >= KMinSizeOfPointToSimplifyAndSmooth) {
    raw_polyline = math::geometry::Simplify(
        raw_polyline, constants::KWayPointsSmoothingParameters,
        /*assert_no_self_intersection=*/false);
  }
  std::vector<int64_t> lane_sequence_id;
  for (const auto& point : raw_polyline) {
    const auto& point_lanes = hdmap::util::GetLanesByPoint(
        *joint_pnc_map_service.pnc_map_service()->hdmap(), point.x(),
        point.y());
    for (const auto& lane : point_lanes) {
      if (lane != nullptr) {
        auto iter = std::find_if(
            lane_sequence_id.begin(), lane_sequence_id.end(),
            [&lane](const auto& lane_id) { return lane->id() == lane_id; });
        if (iter == lane_sequence_id.end()) {
          lane_sequence_id.push_back(lane->id());
        }
      }
    }
  }
  std::vector<const pnc_map::Lane*> lane_sequence =
      joint_pnc_map_service.GetLaneSequence(lane_sequence_id);
  std::vector<const pnc_map::Lane*> filter_lanes;
  std::set<const pnc_map::Road*> roads;
  for (const auto& lane : lane_sequence) {
    DCHECK(lane != nullptr);
    const auto& road = lane->section()->road();
    if (roads.find(road) == roads.end()) {
      roads.insert(road);
      filter_lanes.push_back(lane);
    }
  }
  return std::make_optional(filter_lanes);
}

// Simplify the reference point to make the correspond polyline smooth.
math::geometry::Polyline2d TrimReferencePoints(
    const ::google::protobuf::RepeatedPtrField<::voy::Point2d>&
        reference_points) {
  DCHECK_GE(reference_points.size(), math::kMinimumInterpolationPointNum);

  math::geometry::Polyline2d raw_polyline;
  for (int i = 0; i < reference_points.size(); ++i) {
    const ::voy::Point2d& raw_point = reference_points.Get(i);
    raw_polyline.emplace_back(raw_point.x(), raw_point.y());
  }
  math::geometry::Polyline2d trimmed_polyline = math::geometry::Simplify(
      raw_polyline, constants::KWayPointsSmoothingParameters,
      /*assert_no_self_intersection=*/false);

  DCHECK_GE(trimmed_polyline.size(), math::kMinimumInterpolationPointNum);
  return trimmed_polyline;
}

// Validate the waypoint assist's reference points.
pb::PathFailureType ValidateWaypointAssistReferencePoint(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::unordered_set<const pnc_map::Lane*>& drawable_lane_sequence,
    const std::unordered_set<const pnc_map::Lane*>&
        extended_drawable_lane_sequence,
    const math::geometry::Polyline2d& trimmed_polyline) {
  // 0.Check if trimmed_polyline size is too small.
  if (trimmed_polyline.size() < math::kMinimumInterpolationPointNum) {
    return pb::PathFailureType::POINTS_VALID_SIZE_SMALL;
  }
  // 1. Check if there is nullptr in lane sequence.
  const bool nullptr_in_lane_sequence = std::any_of(
      lane_sequence.begin(), lane_sequence.end(),
      [](const pnc_map::Lane* lane_ptr) { return lane_ptr == nullptr; });
  if (nullptr_in_lane_sequence || lane_sequence.empty()) {
    return pb::PathFailureType::INVALID_LANE_SEQUENCE;
  }
  // 2.Check if the reference end point in the drawable lane border.
  bool nullptr_in_drawable_lane_sequence = std::any_of(
      drawable_lane_sequence.begin(), drawable_lane_sequence.end(),
      [](const pnc_map::Lane* lane_ptr) { return lane_ptr == nullptr; });
  if (drawable_lane_sequence.empty() || nullptr_in_drawable_lane_sequence) {
    return pb::PathFailureType::INVALID_DRAWABLE_AREA;
  }
  const math::geometry::Point2d& end_point = trimmed_polyline.back();
  bool end_point_in_lane_border = std::any_of(
      drawable_lane_sequence.begin(), drawable_lane_sequence.end(),
      [&end_point](const pnc_map::Lane* lane_ptr) {
        return math::geometry::Within(end_point, lane_ptr->border());
      });
  end_point_in_lane_border |= std::any_of(
      extended_drawable_lane_sequence.begin(),
      extended_drawable_lane_sequence.end(),
      [&end_point](const pnc_map::Lane* lane_ptr) {
        return math::geometry::Within(end_point, lane_ptr->border());
      });
  if (!end_point_in_lane_border) {
    return pb::PathFailureType::END_POINT_NOT_IN_DRAWABLE_AREA;
  }
  return pb::PathFailureType::PATH_VALID;
}

std::optional<pb::WaypointBasedAssistCommand> SearchWaypointAssistCommand(
    const std::deque<std::unique_ptr<AssistTaskBase>>& assist_task_queue,
    const std::vector<pb::AssistResponse>& waypoint_responses_from_routing) {
  if (!assist_task_queue.empty() && assist_task_queue.front()->task_state() ==
                                        pb::AssistTaskState::kWaiting) {
    if (assist_task_queue.front()->assist_response().type_case() ==
        pb::AssistResponse::kWaypointBasedAssistCommand) {
      return std::make_optional(assist_task_queue.front()
                                    ->assist_response()
                                    .waypoint_based_assist_command());
    }

    if (assist_task_queue.front()->assist_response().type_case() ==
        pb::AssistResponse::kRemoteAssistTaskExitResponse) {
      pb::AssistResponse response;
      response.set_is_unidirectional(true);
      response.mutable_waypoint_based_assist_command()->set_timestamp(
          assist_task_queue.front()->task_timestamp());
      response.mutable_waypoint_based_assist_command()->set_operation_command(
          pb::OperationCommand::kExitWayPointAssistMode);
      return std::make_optional(response.waypoint_based_assist_command());
    }
  }

  if (!waypoint_responses_from_routing.empty()) {
    return std::make_optional(waypoint_responses_from_routing.front()
                                  .waypoint_based_assist_command());
  }

  return std::nullopt;
}

// TODO(Zhe): Needs to distinguish manual reverse driving and
// auto reverse driving, when reverse driving provides relavent function.
bool IsReverseDrivingActive(const std::vector<pb::AssistResponse>& responses) {
  return std::any_of(responses.cbegin(), responses.cend(),
                     [](const pb::AssistResponse& response) {
                       return response.type_case() ==
                              pb::AssistResponse::kReverseDrivingResponse;
                     });
}

bool HasArrivedAtWaypointAssistPathEnd(
    const RobotState& robot_state,
    const math::geometry::Point2d& waypoint_assist_path_end,
    const std::optional<pb::WaypointAssistSeed> waypoint_assist_seed) {
  DCHECK(waypoint_assist_seed.has_value());
  const math::geometry::PolylineCurve2d nominal_path(
      waypoint_assist_seed->reference_curve());
  const double front_bumper_arclength =
      nominal_path
          .GetProximity(
              robot_state.current_state_snapshot().front_bumper_position(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double waypoint_assist_path_end_arclength =
      nominal_path
          .GetProximity(waypoint_assist_path_end,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  return waypoint_assist_path_end_arclength - front_bumper_arclength <
         kWaypointAssistDestinationBufferInMeter;
}

// Checks whether ego's current road has been entirely blocked.
bool IsRoadEntirelyBlocked(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages) {
  std::unordered_set<int64_t> checked_road_ids;
  for (const auto& lane : lane_sequence) {
    if (!lane || !lane->section() || !lane->section()->road()) {
      continue;
    }

    const int64_t road_id = lane->section()->road()->id();
    if (checked_road_ids.find(road_id) != checked_road_ids.end()) {
      continue;
    }
    checked_road_ids.insert(road_id);

    const auto& road_blockage_iter = road_blockages.find(road_id);
    if (road_blockage_iter != road_blockages.end()) {
      for (const auto& road_blockage_group : road_blockage_iter->second) {
        const auto& types = road_blockage_group.road_blockage_group.types();
        // The current lane'road is entirely blocked.
        if (std::any_of(types.begin(), types.end(), [](const auto& type) {
              return type == pb::RoadBlockageGroupType::kEntireBlocked;
            })) {
          rt_event::PostRtEvent<
              rt_event::planner::WaypointAssistRoadEntirelyBlocked>();
          return true;
        }
      }
    }
  }
  return false;
}

const std::optional<pb::WaypointAssistSeed> GetWaypointAssistSeed(
    const pb::DecoupledManeuverSeed& prev_maneuver_seed) {
  if (prev_maneuver_seed.has_selected_waypoint_assist_seed()) {
    return std::make_optional<pb::WaypointAssistSeed>(
        prev_maneuver_seed.selected_waypoint_assist_seed());
  }

  return std::nullopt;
}

// Generates the lane sequence debug data.
void ConstructLaneSequenceDebugForWaypointAssist(
    const std::unordered_set<const pnc_map::Lane*>& drawable_lane_sequence,
    const std::unordered_set<const pnc_map::Lane*>&
        extended_drawable_lane_sequence,
    const std::shared_ptr<const LaneSequenceResult>
        lane_sequence_result_for_waypoint_assist,
    pb::LaneSequenceGeneratorDebug* lane_sequence_generator_debug) {
  // Update the debug for the drawable lane sequence.
  lane_sequence_generator_debug->mutable_waypoint_assist_drawable_area_debug()
      ->clear_lane_sequence();
  lane_sequence_generator_debug->mutable_waypoint_assist_drawable_area_debug()
      ->mutable_lane_sequence()
      ->Reserve(drawable_lane_sequence.size());
  for (const auto& lane : drawable_lane_sequence) {
    pb::LaneInfo* lane_info =
        lane_sequence_generator_debug
            ->mutable_waypoint_assist_drawable_area_debug()
            ->add_lane_sequence();
    lane_info->set_lane_id(lane->id());
    // Update the boarder.
    math::geometry::Convert(lane->border(), *lane_info->mutable_lane_border());
  }

  // Update the debug for the extended drawable lane sequence.
  lane_sequence_generator_debug
      ->mutable_waypoint_assist_extended_drawable_area_debug()
      ->clear_lane_sequence();
  lane_sequence_generator_debug
      ->mutable_waypoint_assist_extended_drawable_area_debug()
      ->mutable_lane_sequence()
      ->Reserve(extended_drawable_lane_sequence.size());
  for (const auto& lane : extended_drawable_lane_sequence) {
    pb::LaneInfo* lane_info =
        lane_sequence_generator_debug
            ->mutable_waypoint_assist_extended_drawable_area_debug()
            ->add_lane_sequence();
    lane_info->set_lane_id(lane->id());
    // Update the boarder.
    math::geometry::Convert(lane->border(), *lane_info->mutable_lane_border());
  }

  if (lane_sequence_result_for_waypoint_assist) {
    lane_sequence_generator_debug->clear_lane_sequence_for_end_of_lane_seq();
    lane_sequence_generator_debug->mutable_lane_sequence_for_end_of_lane_seq()
        ->Reserve(
            lane_sequence_result_for_waypoint_assist->lane_sequence.size());
    for (const auto& lane :
         lane_sequence_result_for_waypoint_assist->lane_sequence) {
      pb::LaneInfo* lane_info = lane_sequence_generator_debug
                                    ->add_lane_sequence_for_end_of_lane_seq();
      lane_info->set_lane_id(lane->id());
      // Update the boarder.
      math::geometry::Convert(lane->border(),
                              *lane_info->mutable_lane_border());
    }
  }
}

// Whether ego has reach drawable lane sequence and there is only one direction
// of lanes at ego location.
bool IsEgoAtOnlyDirectionLanes(
    const std::unordered_set<const pnc_map::Lane*>& lane_sequence,
    const RobotState& robot_state) {
  std::unordered_set<hdmap::Lane::Turn> lane_turn_ego_locate;
  for (const auto* lane : lane_sequence) {
    if (math::geometry::Within(
            robot_state.current_state_snapshot().rear_axle_position(),
            lane->border())) {
      lane_turn_ego_locate.insert(lane->turn());
    }
  }
  // Equal 1 means there is only one direction lanes.
  return lane_turn_ego_locate.size() == 1;
}

// Gets near lanes with pose.
// TODO(songjingru): check hard boundary info between near lanes and physical
// lane.
std::vector<const pnc_map::Lane*> GetNearLanes(
    const pnc_map::PncMapService* pnc_map_service,
    const RobotStateSnapshot& current_state_snapshot,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence) {
  voy::Pose voy_pose;
  voy_pose.set_x(current_state_snapshot.x());
  voy_pose.set_y(current_state_snapshot.y());
  voy_pose.set_z(current_state_snapshot.z());
  voy_pose.set_yaw(current_state_snapshot.heading());
  return pnc_map_service->GetNearLanesWithPose(
      voy_pose, constants::kLaneSearchingRadiusInMeter,
      /*max_heading_diff=*/M_PI_2,
      /*prefer_overlapped_lanes=*/false, last_selected_lane_sequence);
}

// Indicates whether ego is in the turn right lane.
bool IsTurnRightScene(const CurrentLaneAssociator& current_lane_associator,
                      const CurrentLaneReasoner& current_lane_reasoner) {
  const auto& current_lanes =
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner.current_lanes_for_regional_path()
          : current_lane_associator.current_lanes_for_regional_path();
  return std::any_of(current_lanes.begin(), current_lanes.end(),
                     [](const pnc_map::Lane* lane) {
                       return lane->turn() ==
                              hdmap::Lane::Turn::Lane_Turn_RIGHT;
                     });
}
}  // namespace

void WaypointAssistTracker::UpdateCurrentLaneForWaypointAssist(
    const RobotState& robot_state) {
  if (!lane_sequence_result_for_waypoint_assist_) {
    return;
  }
  const math::geometry::PolylineCurve2d nominal_path =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence_result_for_waypoint_assist_->lane_sequence,
          lane_selection::LaneCurveType::kCenterLine);

  std::vector<const pnc_map::Lane*> trimmed_lane_sequence;
  const double ego_arclength =
      nominal_path
          .GetProximity(
              robot_state.current_state_snapshot().rear_axle_position(),
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  for (const auto& lane :
       lane_sequence_result_for_waypoint_assist_->lane_sequence) {
    const double cur_lane_start_arclenght =
        nominal_path
            .GetProximity(lane->center_line().GetStartPoint(),
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    const double cur_lane_end_arclenght =
        nominal_path
            .GetProximity(lane->center_line().GetEndPoint(),
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    if (cur_lane_end_arclenght >= ego_arclength &&
        cur_lane_start_arclenght < ego_arclength) {
      lane_sequence_result_for_waypoint_assist_->current_lane = lane;
      return;
    }
  }
}

void WaypointAssistTracker::LoadState(
    const pb::WaypointAssistTrackerState& from_pb) {
  waypoint_assist_phase_ = from_pb.waypoint_assist_phase();
  last_waypoint_assist_phase_ = from_pb.last_waypoint_assist_phase();
  has_ego_reached_target_lane_sequence_ =
      from_pb.has_ego_reached_target_lane_sequence();

  if (from_pb.has_waypoint_assist_end_point()) {
    const auto& pb_point = from_pb.waypoint_assist_end_point();
    voy::Point2d point;
    point.set_x(pb_point.x());
    point.set_y(pb_point.y());
    waypoint_assist_end_point_ = point;
  } else {
    waypoint_assist_end_point_ = std::nullopt;
  }

  if (from_pb.has_hard_boundary_buffer_in_meter()) {
    hard_boundary_buffer_in_meter_ = from_pb.hard_boundary_buffer_in_meter();
  } else {
    hard_boundary_buffer_in_meter_ = std::nullopt;
  }

  std::shared_ptr<const pnc_map::PncMapService> pnc_map_service =
      PlanningService::GetInstance()->GetPnCMapService();
  DCHECK(pnc_map_service);
  // Load |waypoint_lane_sequence_result_| related state.
  if (from_pb.has_waypoint_lane_sequence_result()) {
    if (!lane_sequence_result_for_waypoint_assist_.get()) {
      lane_sequence_result_for_waypoint_assist_ =
          std::make_shared<LaneSequenceResult>();
    }
    lane_sequence_result_for_waypoint_assist_->lane_sequence =
        pnc_map_service->GetLaneSequence(
            from_pb.waypoint_lane_sequence_result().lane_sequence());
    lane_sequence_result_for_waypoint_assist_->untrimmed_lane_sequence =
        pnc_map_service->GetLaneSequence(
            from_pb.waypoint_lane_sequence_result().untrimmed_lane_sequence());
    lane_sequence_result_for_waypoint_assist_->current_lane =
        lane_sequence_result_for_waypoint_assist_->lane_sequence.front();
    lane_sequence_result_for_waypoint_assist_->lane_sequence_type =
        from_pb.waypoint_lane_sequence_result().lane_sequence_type();
  } else {
    lane_sequence_result_for_waypoint_assist_ = nullptr;
  }

  // Load |reference_point_located_lane_| related state.
  if (from_pb.has_reference_point_located_lane()) {
    reference_point_located_lane_ =
        std::make_optional<std::vector<const pnc_map::Lane*>>(
            pnc_map_service->GetLaneSequence(
                from_pb.reference_point_located_lane().lane_sequence()));
  } else {
    reference_point_located_lane_ = std::nullopt;
  }

  // Load |drawable_lane_sequence_| related state.
  drawable_lane_sequence_.clear();
  if (from_pb.has_drawable_lane_sequence()) {
    std::vector<const pnc_map::Lane*> drawable_lane_sequence(
        pnc_map_service->GetLaneSequence(
            from_pb.drawable_lane_sequence().lane_sequence()));
    drawable_lane_sequence_.insert(drawable_lane_sequence.cbegin(),
                                   drawable_lane_sequence.cend());
  }

  // Load |extended_drawable_lane_sequence_| related state.
  extended_drawable_lane_sequence_.clear();
  if (from_pb.has_extended_drawable_lane_sequence()) {
    std::vector<const pnc_map::Lane*> extended_drawable_lane_sequence(
        pnc_map_service->GetLaneSequence(
            from_pb.extended_drawable_lane_sequence().lane_sequence()));
    extended_drawable_lane_sequence_.insert(
        extended_drawable_lane_sequence.cbegin(),
        extended_drawable_lane_sequence.cend());
  }
}

void WaypointAssistTracker::DumpState(
    pb::WaypointAssistTrackerState& to_pb) const {
  to_pb.set_waypoint_assist_phase(waypoint_assist_phase_);
  to_pb.set_last_waypoint_assist_phase(last_waypoint_assist_phase_);
  to_pb.set_has_ego_reached_target_lane_sequence(
      has_ego_reached_target_lane_sequence_);

  if (waypoint_assist_end_point_.has_value()) {
    auto* point = to_pb.mutable_waypoint_assist_end_point();
    point->set_x(waypoint_assist_end_point_->x());
    point->set_y(waypoint_assist_end_point_->y());
  }

  if (hard_boundary_buffer_in_meter_.has_value()) {
    to_pb.set_hard_boundary_buffer_in_meter(
        hard_boundary_buffer_in_meter_.value());
  }

  if (lane_sequence_result_for_waypoint_assist_) {
    auto* waypoint_lane_sequence_result =
        to_pb.mutable_waypoint_lane_sequence_result();
    for (const pnc_map::Lane* lane :
         lane_sequence_result_for_waypoint_assist_->lane_sequence) {
      waypoint_lane_sequence_result->add_lane_sequence(lane->id());
    }
    for (const pnc_map::Lane* lane :
         lane_sequence_result_for_waypoint_assist_->untrimmed_lane_sequence) {
      waypoint_lane_sequence_result->add_untrimmed_lane_sequence(lane->id());
    }
    if (lane_sequence_result_for_waypoint_assist_->current_lane) {
      waypoint_lane_sequence_result->set_current_lane_id(
          lane_sequence_result_for_waypoint_assist_->current_lane->id());
    }
    waypoint_lane_sequence_result->set_lane_sequence_type(
        lane_sequence_result_for_waypoint_assist_->lane_sequence_type);
  }

  if (reference_point_located_lane_.has_value()) {
    auto* reference_point_located_lane =
        to_pb.mutable_reference_point_located_lane();
    for (const pnc_map::Lane* lane : *reference_point_located_lane_) {
      reference_point_located_lane->add_lane_sequence(lane->id());
    }
  }

  if (!drawable_lane_sequence_.empty()) {
    auto* drawable_lane_sequence = to_pb.mutable_drawable_lane_sequence();
    for (const pnc_map::Lane* lane : drawable_lane_sequence_) {
      drawable_lane_sequence->add_lane_sequence(lane->id());
    }
  }

  if (!extended_drawable_lane_sequence_.empty()) {
    auto* extended_drawable_lane_sequence =
        to_pb.mutable_extended_drawable_lane_sequence();
    for (const pnc_map::Lane* lane : extended_drawable_lane_sequence_) {
      extended_drawable_lane_sequence->add_lane_sequence(lane->id());
    }
  }
}

void WaypointAssistTracker::UpdateDebug(
    const std::vector<pb::AssistResponse>& responses,
    pb::LaneSequenceGeneratorDebug* lane_sequence_generator_debug,
    pb::WaypointAssistDebug* waypoint_assist_debug) {
  if (waypoint_assist_debug == nullptr) {
    return;
  }
  const std::string waypoint_command_string =
      current_waypoint_assist_command_.has_value()
          ? planner::pb::OperationCommand_Enum_Name(
                current_waypoint_assist_command_->operation_command())
          : "No Waypoint Assist Command";
  const std::string current_waypoint_command_sender_string =
      current_waypoint_assist_command_.has_value()
          ? planner::pb::Sender_Enum_Name(
                current_waypoint_assist_command_->sender())
          : "No Waypoint Assist Command sender";
  waypoint_assist_debug->set_assist_maneuver_phase(waypoint_assist_phase_);
  waypoint_assist_debug->set_has_received_waypoints(
      waypoint_assist_end_point_.has_value());
  waypoint_assist_debug->set_assist_responses_size(responses.size());
  waypoint_assist_debug->set_current_command(waypoint_command_string);
  waypoint_assist_debug->set_current_command_sender(
      current_waypoint_command_sender_string);
  const int waypoint_assist_command_in_one_cycle =
      std::count_if(responses.cbegin(), responses.cend(),
                    [](const pb::AssistResponse& response) {
                      return response.type_case() ==
                             pb::AssistResponse::kWaypointBasedAssistCommand;
                    });
  waypoint_assist_debug->set_command_count_in_one_cycle(
      waypoint_assist_command_in_one_cycle);

  if (!drawable_lane_sequence_.empty() &&
      lane_sequence_generator_debug != nullptr) {
    ConstructLaneSequenceDebugForWaypointAssist(
        drawable_lane_sequence_, extended_drawable_lane_sequence_,
        lane_sequence_result_for_waypoint_assist_,
        lane_sequence_generator_debug);
  }

  // TODO(Xiang): Set path failure info & failure reason properly.
  switch (waypoint_assist_phase_) {
    case pb::AssistManeuverPhase::kIdle:
    case pb::AssistManeuverPhase::kWaitingPoints:
      waypoint_assist_debug->set_path_generation_status(
          pb::AssistPathGenerationStatus::kNotFinished);
      waypoint_assist_debug->set_path_failure_type(
          pb::PathFailureType::PATH_VALID);
      break;
    case pb::AssistManeuverPhase::kPreparingPath:
      if (path_failure_type_ != pb::PathFailureType::PATH_VALID) {
        waypoint_assist_debug->set_path_generation_status(
            pb::AssistPathGenerationStatus::kFailed);
        waypoint_assist_debug->set_path_failure_type(path_failure_type_);
      } else {
        waypoint_assist_debug->set_path_generation_status(
            pb::AssistPathGenerationStatus::kSucceeded);
        waypoint_assist_debug->set_path_failure_type(
            pb::PathFailureType::PATH_VALID);
      }
      waypoint_assist_debug->mutable_waypoint_borrow_opposite_lanes()
          ->set_is_ego_in_oppsite_road(is_ego_in_oppsite_road_);
      waypoint_assist_debug->mutable_waypoint_borrow_opposite_lanes()
          ->set_is_road_entirely_blocked(is_road_entirely_blocked_);
      waypoint_assist_debug->mutable_waypoint_borrow_opposite_lanes()
          ->set_is_last_waypoint_in_opposite_lanes(
              is_last_waypoint_in_opposite_lanes_);
      for (const auto* lane : opposite_lane_sequence_) {
        waypoint_assist_debug->mutable_waypoint_borrow_opposite_lanes()
            ->add_opposite_lane_ids(lane->id());
      }
      break;
    case pb::AssistManeuverPhase::kWaitingConfirm:
    case pb::AssistManeuverPhase::kFollowingPath:
    case pb::AssistManeuverPhase::kExitReasoning:
      waypoint_assist_debug->set_path_generation_status(
          pb::AssistPathGenerationStatus::kSucceeded);
      waypoint_assist_debug->set_path_failure_type(
          pb::PathFailureType::PATH_VALID);
      waypoint_assist_debug->set_start_stuck_time_in_exit_reasoning_ms(
          start_stuck_time_in_kExitReasoning_ms_);
      break;
    default:
      DCHECK(false) << "Unexpected waypoint assist phase!";
  }

  waypoint_assist_debug->set_has_arrived_path_end(
      has_arrived_at_waypoint_assist_path_end_);
  waypoint_assist_debug->set_has_lane_sequence_proposal_responded(
      has_lane_sequence_proposal_responded_);
  waypoint_assist_debug->set_has_ego_reached_target_lane_sequence(
      has_ego_reached_target_lane_sequence_);
}

void WaypointAssistTracker::UpdateDrawableLaneSequence(
    const RobotState& robot_state,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const GlobalRouteSolution& global_route_solution,
    const RouteModel& route_model,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    bool is_immediate_pull_over_triggered_by_planning) {
  // Only update the drawable lane sequence when the waypoint assist phase is
  // kWaitingPoints.
  if (waypoint_assist_phase_ != pb::AssistManeuverPhase::kWaitingPoints) {
    return;
  }
  ResetDrawableLaneSequence();

  const std::vector<const pnc_map::Lane*>& current_lanes =
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner.current_lanes_for_regional_path()
          : current_lane_associator.current_lanes_for_regional_path();
  const std::vector<const pnc_map::Lane*>& start_lanes = current_lanes;
  const math::geometry::Point2d ego_position(
      robot_state.current_state_snapshot().x(),
      robot_state.current_state_snapshot().y());
  const std::map<int64_t, routing::pb::CostToDestination> lane_cost_map =
      global_route_solution.GetNearbyGlobalCostMap(
          drivable_lane_reasoner, route_model.edge_filter(), ego_position,
          start_lanes, current_lanes,
          /*ego_timestamp=*/robot_state.current_state_snapshot().timestamp(),
          is_immediate_pull_over_triggered_by_planning,
          kMaxDistSearchDrawableLane);
  if (!lane_cost_map.empty()) {
    for (const auto& [lane_id, _] : lane_cost_map) {
      const std::vector<const pnc_map::Lane*> cost_map_lanes =
          joint_pnc_map_service.GetLaneSequence({lane_id});
      if (!cost_map_lanes.empty() && cost_map_lanes.front() != nullptr) {
        drawable_lane_sequence_.emplace(cost_map_lanes.front());
      }
    }
  }

  // Update the extended drawable lane sequence.
  is_road_entirely_blocked_ =
      IsRoadEntirelyBlocked(lane_sequence, road_blockages);
  if (is_road_entirely_blocked_) {
    const std::vector<const pnc_map::Lane*>& near_lanes =
        GetNearLanes(joint_pnc_map_service.pnc_map_service(),
                     robot_state.current_state_snapshot(), lane_sequence);
    std::vector<const pnc_map::Lane*> extended_start_lanes;
    for (const auto* lane : near_lanes) {
      if (std::none_of(current_lanes.begin(), current_lanes.end(),
                       [&lane](const pnc_map::Lane* current_lane) {
                         return lane->id() == current_lane->id();
                       })) {
        extended_start_lanes.emplace_back(lane);
      }
    }
    const std::map<int64_t, routing::pb::CostToDestination>
        extended_lane_cost_map = global_route_solution.GetNearbyGlobalCostMap(
            drivable_lane_reasoner, route_model.edge_filter(), ego_position,
            extended_start_lanes, current_lanes,
            /*ego_timestamp=*/robot_state.current_state_snapshot().timestamp(),
            is_immediate_pull_over_triggered_by_planning,
            kMaxDistSearchDrawableLane);
    if (!extended_lane_cost_map.empty()) {
      for (const auto& [lane_id, _] : extended_lane_cost_map) {
        const std::vector<const pnc_map::Lane*> cost_map_lanes =
            joint_pnc_map_service.GetLaneSequence({lane_id});
        if (!cost_map_lanes.empty() && cost_map_lanes.front() != nullptr &&
            drawable_lane_sequence_.find(cost_map_lanes.front()) ==
                drawable_lane_sequence_.end()) {
          extended_drawable_lane_sequence_.emplace(cost_map_lanes.front());
        }
      }
    }
  }

  if (!FLAGS_planning_enable_waypoint_assist_drive_on_opposite_lanes) {
    return;
  }

  // Update the drawable lane sequence if ego is in the opposite road, or
  // current road have been blocked.
  const auto& current_lanes_for_regional_path =
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner.current_lanes_for_regional_path()
          : current_lane_associator.current_lanes_for_regional_path();
  is_ego_in_oppsite_road_ = common::IsEgoInTheOppositeRoad(
      robot_state.current_state_snapshot().rear_axle_position(),
      current_lanes_for_regional_path);

  if (is_ego_in_oppsite_road_ || is_road_entirely_blocked_) {
    // Add the leftmost lane of the opposite lane to the drawable area.
    opposite_lane_sequence_.clear();
    const auto& raw_opposite_lane_sequence =
        GetOppositeLeftMostLanes(robot_state, lane_sequence);
    for (const auto* candidate : raw_opposite_lane_sequence) {
      if (candidate == nullptr) {
        continue;
      }
      drawable_lane_sequence_.emplace(candidate);
      opposite_lane_sequence_.push_back(candidate);
    }

    // If ego is in the opposite road, also add the current lane to the
    // drawable area, prevent ego is not on the opposite leftmost lane.
    const auto& current_ego_pos =
        robot_state.current_state_snapshot().rear_axle_position();
    if (is_ego_in_oppsite_road_ &&
        !std::any_of(opposite_lane_sequence_.begin(),
                     opposite_lane_sequence_.end(),
                     [current_ego_pos](const auto& opposite_lane) {
                       return math::geometry::Within(current_ego_pos,
                                                     opposite_lane->border());
                     })) {
      // Get ego current lane IDs without duplicates.
      const auto& point_lanes = hdmap::util::GetLanesByPoint(
          *joint_pnc_map_service.pnc_map_service()->hdmap(),
          current_ego_pos.x(), current_ego_pos.y());
      std::unordered_set<int64_t> lane_sequence_id;
      for (const auto& lane : point_lanes) {
        lane_sequence_id.insert(DCHECK_NOTNULL(lane)->id());
      }
      // Convert the lane type from hdmap to pnc_map based on the ids.
      const std::vector<const pnc_map::Lane*>& ego_current_lanes =
          joint_pnc_map_service.GetLaneSequence(std::vector<int64_t>(
              lane_sequence_id.begin(), lane_sequence_id.end()));
      DCHECK_GT(ego_current_lanes.size(), 0);

      // Include the preceding lanes if less than 50m to drive.
      for (const auto* current_lane : ego_current_lanes) {
        if (current_lane == nullptr) {
          continue;
        }
        drawable_lane_sequence_.emplace(current_lane);
        opposite_lane_sequence_.push_back(current_lane);
        const double drive_arc_length =
            current_lane->GetArclength(current_ego_pos);
        if (drive_arc_length < kMaxBackTraceArclength) {
          continue;
        }
        std::for_each(DCHECK_NOTNULL(current_lane)->predecessors().begin(),
                      current_lane->predecessors().end(), [&](const auto lane) {
                        if (!lane->IsInJunction()) {
                          drawable_lane_sequence_.emplace(lane);
                          opposite_lane_sequence_.push_back(lane);
                        }
                      });
      }
    }
  }
}

void WaypointAssistTracker::UpdateRoutingTriggerWaypointAssistUnstuckState() {
  is_routing_unstuck_success_ = std::nullopt;
  if (current_waypoint_assist_command_ == std::nullopt) {
    return;
  }
  if (is_waypoint_triggered_by_routing_) {
    if (current_waypoint_assist_command_->operation_command() ==
        pb::OperationCommand::kFollowPath) {
      rt_event::PostRtEvent<rt_event::planner::RoutingTriggerWaypointAssist>(
          "Reference point accepted by teleops");
    } else if (current_waypoint_assist_command_->operation_command() ==
                   pb::OperationCommand::kReplanPath ||
               current_waypoint_assist_command_->operation_command() ==
                   pb::OperationCommand::kExitWayPointAssistMode) {
      is_routing_unstuck_success_ = false;
    }
  }
  is_waypoint_triggered_by_routing_ =
      current_waypoint_assist_command_->sender() == pb::Sender::kRouting;
}

void WaypointAssistTracker::UpdateIsEgoReachTargetLaneSequence(
    const math::geometry::OrientedBox2d& ego_bbox) {
  has_ego_reached_target_lane_sequence_ =
      pb::EgoReachTargetLaneSequenceState::NOT_DEFINED;
  if (lane_sequence_result_for_waypoint_assist_ == nullptr) {
    return;
  }
  const auto& target_lane_sequence =
      lane_sequence_result_for_waypoint_assist_->lane_sequence;
  // If ego center point has reached the target lane sequence, set the state to
  // REACH.
  if (std::any_of(target_lane_sequence.begin(), target_lane_sequence.end(),
                  [&](const auto* lane) {
                    return math::geometry::Within(ego_bbox.center(),
                                                  lane->border());
                  })) {
    has_ego_reached_target_lane_sequence_ =
        pb::EgoReachTargetLaneSequenceState::REACHED;
    // If ego has reached the target lane sequence, but any corner of the
    // vehicle is not in the target lane sequence, set the state to
    // NOT_REACH_IN_LANE_CHANGE.
    for (const auto& corner_point : ego_bbox.CornerPoints()) {
      if (!std::all_of(target_lane_sequence.begin(), target_lane_sequence.end(),
                       [&](const auto* lane) {
                         return math::geometry::Within(corner_point,
                                                       lane->border());
                       })) {
        has_ego_reached_target_lane_sequence_ =
            pb::EgoReachTargetLaneSequenceState::NOT_REACHED_IN_LANE_CHANGE;
      }
    }
  } else {
    // If ego center point is not in the target lane sequence, set the state to
    // NOT_REACH.
    has_ego_reached_target_lane_sequence_ =
        pb::EgoReachTargetLaneSequenceState::NOT_REACHED;
    return;
  }
}

void WaypointAssistTracker::UpdateWaypointAssistState(
    const RobotState& robot_state, const RouteModel& route_model,
    const std::deque<std::unique_ptr<AssistTaskBase>>& assist_task_queue,
    const std::vector<pb::AssistResponse>& responses,
    const std::vector<pb::AssistResponse>& waypoint_responses_from_routing,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const GlobalRouteSolution& global_route_solution,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const pb::DecoupledManeuverSeed& prev_maneuver_seed,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const bool has_left_assist_blockage_area,
    bool is_immediate_pull_over_triggered_by_planning,
    const std::optional<bool>& is_current_lanes_continuous,
    pb::WaypointAssistDebug* waypoint_assist_debug,
    pb::LaneSequenceGeneratorDebug* lane_sequence_generator_debug) {
  TRACE_EVENT_SCOPE(planner, WorldModel_UpdateWaypointAssistState);

  const bool is_near_stationary = IsEgoCarNearStationary(robot_state);
  const std::optional<pb::WaypointAssistSeed> waypoint_assist_seed =
      GetWaypointAssistSeed(prev_maneuver_seed);

  bool waypoint_assist_has_path =
      (waypoint_assist_seed.has_value() &&
       waypoint_assist_seed->waypoint_assist_phase() !=
           pb::AssistManeuverPhase::kIdle &&
       waypoint_assist_seed->waypoint_assist_phase() !=
           pb::AssistManeuverPhase::kWaitingPoints);
  // waypoint_assist_has_path means it should have a valid reference curve
  DCHECK(!waypoint_assist_has_path ||
         waypoint_assist_seed->reference_curve().segments_size() > 0);

  last_waypoint_assist_phase_ = waypoint_assist_phase_;

  current_waypoint_assist_command_ = SearchWaypointAssistCommand(
      assist_task_queue, waypoint_responses_from_routing);

  UpdateRoutingTriggerWaypointAssistUnstuckState();

  if (waypoint_assist_end_point_.has_value() && waypoint_assist_has_path) {
    has_arrived_at_waypoint_assist_path_end_ =
        HasArrivedAtWaypointAssistPathEnd(
            robot_state,
            {waypoint_assist_end_point_->x(), waypoint_assist_end_point_->y()},
            waypoint_assist_seed);
  }

  if (waypoint_assist_has_path) {
    has_lane_sequence_proposal_responded_ =
        waypoint_assist_seed->has_responded_lane_sequence_proposal();
    // TODO(Zhuzhe, Xiaobo): Refactor work needed to unify FSM transition logic
    // and properly notify GENERATE_NOMINAL_PATH_FAILED in T1 cycle to tele ops.
    if (waypoint_assist_seed->prepare_path_fail()) {
      if (path_failure_type_ !=
          pb::PathFailureType::GENERATE_NOMINAL_PATH_FAILED) {
        path_failure_type_ = pb::PathFailureType::GENERATE_NOMINAL_PATH_FAILED;
        LOG(INFO) << "[GGG]GENERATE_NOMINAL_PATH_FAILED";
        UpdateDebug(responses, lane_sequence_generator_debug,
                    waypoint_assist_debug);
        return;
      }
    }
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning &&
      is_near_stationary) {
    if (start_stuck_time_in_kExitReasoning_ms_ < 0) {
      start_stuck_time_in_kExitReasoning_ms_ = base::Now();
    }
  } else {
    start_stuck_time_in_kExitReasoning_ms_ = -1;
  }

  const bool is_reverse_driving_active = IsReverseDrivingActive(responses);

  if (IsReadyForIdlePhase(
          current_waypoint_assist_command_, current_lane_associator,
          current_lane_reasoner, robot_state, prev_maneuver_seed,
          is_reverse_driving_active, is_near_stationary,
          has_left_assist_blockage_area, is_current_lanes_continuous)) {
    UpdateIsEgoReachTargetLaneSequence(
        robot_state.plan_init_state_snapshot().bounding_box());
    ResetToIdlePhase();
  } else if (IsReadyForWaitingPointsPhase(current_waypoint_assist_command_,
                                          is_near_stationary)) {
    // TODO(Xiang): clear related info & add related input for
    // IsReadyForWaitingPointsPhase().
    ResetToWaitingPointsPhase();
    is_turn_right_scene_ =
        IsTurnRightScene(current_lane_associator, current_lane_reasoner);
  } else if (IsReadyForPreparingPathPhase(current_waypoint_assist_command_,
                                          is_near_stationary)) {
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kPreparingPath;
    DCHECK(current_waypoint_assist_command_.has_value());
    reference_points_.CopyFrom(
        current_waypoint_assist_command_->reference_points());
    DCHECK_NE(reference_points_.size(), 0);
    // Check whether reference point is valid.
    path_failure_type_ = DoPreparingPath(
        robot_state, joint_pnc_map_service, global_route_solution,
        current_lane_associator, current_lane_reasoner, lane_sequence,
        drivable_lane_reasoner, reference_points_);
    if (path_failure_type_ != pb::PathFailureType::PATH_VALID) {
      if (is_waypoint_triggered_by_routing_) {
        path_failure_type_ = pb::PathFailureType::POINT_INVALID_BY_ROUTING;
        LOG(INFO) << "Point generated by routing is invalid";
      }
    }
  } else if (IsReadyForWaitingConfirmPhase(is_near_stationary)) {
    // TODO(Xiang): add related input for IsReadyForWaitingConfirmPhase().
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kWaitingConfirm;
    ResetHasArrivedAtWaypointAssistPathEnd();
    if (hard_boundary_buffer_in_meter_ == std::nullopt &&
        waypoint_assist_seed.has_value()) {
      const math::geometry::PolylineCurve2d nominal_path(
          waypoint_assist_seed->reference_curve());
      if (!nominal_path.empty()) {
        hard_boundary_buffer_in_meter_ = CalculateMaxDistance(
            robot_state.current_state_snapshot().bounding_box().polygon(),
            nominal_path);
      }
    }
  } else if (IsReadyForFollowingPathPhase(current_waypoint_assist_command_,
                                          is_near_stationary)) {
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kFollowingPath;
    UpdateCurrentLaneForWaypointAssist(robot_state);
  } else if (IsReadyForExitReasoningPhase(current_waypoint_assist_command_)) {
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kExitReasoning;
    UpdateCurrentLaneForWaypointAssist(robot_state);
    // Add cloud buried point if enter kExitReasoning mode.
    rt_event::PostRtEvent<
        rt_event::planner::WaypointAssistEnterExitReasoning>();
  }
  UpdateDrawableLaneSequence(robot_state, drivable_lane_reasoner,
                             joint_pnc_map_service, lane_sequence,
                             current_lane_associator, current_lane_reasoner,
                             global_route_solution, route_model, road_blockages,
                             is_immediate_pull_over_triggered_by_planning);
  UpdateDebug(responses, lane_sequence_generator_debug, waypoint_assist_debug);
}

double WaypointAssistTracker::BackTrackFromEndPointLane(
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const math::geometry::Point2d ego_pos, const pnc_map::Lane* start_lane,
    const pnc_map::Lane* end_point_located_lane, int back_arclength,
    double& min_dist_to_prefix, std::vector<const pnc_map::Lane*>& result) {
  // 1. Backtracks up to 50 meters.
  double dist_to_prefix = std::numeric_limits<double>::max();
  if (back_arclength > kMaxBackTraceArclength) {
    dist_to_prefix =
        start_lane->center_line()
            .GetProximity(ego_pos, math::pb::UseExtensionFlag::kForbid)
            .dist;
    if (dist_to_prefix < min_dist_to_prefix + math::constants::kEpsilon) {
      min_dist_to_prefix = dist_to_prefix;
      result.clear();
    }
    return dist_to_prefix;
  }

  // 2. Backtracks to the lane where ego is located or the same section.
  for (const auto& current_lane :
       FLAGS_planning_enable_current_lane_reasoner
           ? current_lane_reasoner.current_lanes_for_regional_path()
           : current_lane_associator.current_lanes_for_regional_path()) {
    if (start_lane->id() == current_lane->id()) {
      is_ego_in_waypoint_lane_sequence_ = true;
      dist_to_prefix = 0;
    } else if (start_lane->section()->id() == current_lane->section()->id()) {
      is_ego_in_waypoint_lane_sequence_ = true;
      dist_to_prefix =
          start_lane->center_line()
              .GetProximity(ego_pos, math::pb::UseExtensionFlag::kForbid)
              .dist;
    }
    if (dist_to_prefix < min_dist_to_prefix + math::constants::kEpsilon) {
      min_dist_to_prefix = dist_to_prefix;
      result.clear();
      return dist_to_prefix;
    }
  }
  if (start_lane->id() == end_point_located_lane->id()) {
    // If start_lane is the lane where end waypoint located in.
    back_arclength += static_cast<int>(start_lane->GetArclength(
        {waypoint_assist_end_point_->x(), waypoint_assist_end_point_->y()}));
  } else {
    back_arclength += static_cast<int>(start_lane->length());
  }

  // 3. Continue back track.
  const pnc_map::Lane* selected_lane_ptr = nullptr;
  for (const auto& predecessor : start_lane->predecessors()) {
    dist_to_prefix = BackTrackFromEndPointLane(
        current_lane_associator, current_lane_reasoner, ego_pos, predecessor,
        end_point_located_lane, back_arclength, min_dist_to_prefix, result);
    if (dist_to_prefix == min_dist_to_prefix) {
      selected_lane_ptr = predecessor;
    }
  }
  if (selected_lane_ptr != nullptr) {
    result.emplace_back(selected_lane_ptr);
    dist_to_prefix = min_dist_to_prefix;
  }
  return dist_to_prefix;
}

bool WaypointAssistTracker::IsReadyForIdlePhase(
    const std::optional<pb::WaypointBasedAssistCommand>& command,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const RobotState& robot_state,
    const pb::DecoupledManeuverSeed& prev_maneuver_seed,
    const bool is_reverse_driving_active, const bool is_near_stationary,
    const bool has_left_assist_blockage_area,
    const std::optional<bool>& is_current_lanes_continuous) const {
  // If not in autonomous mode and not in simulation, return kIdle.
  if (!robot_state.IsInAutonomousMode() && !av_comm::InSimulation()) {
    return true;
  }

  // Keep kIdle phase when not receiving related commands.
  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kIdle) {
    return is_near_stationary &&
           (!command.has_value() ||
            command->operation_command() !=
                pb::OperationCommand::kEnterWayPointAssistMode);
  }

  if (is_reverse_driving_active && is_near_stationary) {
    return true;
  }

  if (last_waypoint_assist_phase_ == pb::AssistManeuverPhase::kPreparingPath &&
      path_failure_type_ == pb::PathFailureType::POINT_INVALID_BY_ROUTING) {
    return true;
  }

  if (waypoint_assist_phase_ != pb::AssistManeuverPhase::kFollowingPath &&
      waypoint_assist_phase_ != pb::AssistManeuverPhase::kExitReasoning &&
      command.has_value() &&
      command->operation_command() ==
          pb::OperationCommand::kExitWayPointAssistMode) {
    return true;
  }

  // Don't enter kExitReasoning when ego is stationary when in kFollowingPath.
  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
      command.has_value() &&
      command->operation_command() ==
          pb::OperationCommand::kExitWayPointAssistMode &&
      is_near_stationary) {
    return true;
  }

  // Return kIdle if ego is stationary and the last waypoint is in the opposite
  // lanes when in kExitReasoning or ops exits in kFollowingPath.
  if (((waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
        command.has_value() &&
        command->operation_command() ==
            pb::OperationCommand::kExitWayPointAssistMode) ||
       waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning) &&
      is_near_stationary && is_last_waypoint_in_opposite_lanes_) {
    return true;
  }

  // Judge whether ego should return to kIdle or return to kExitReasoning.
  if (((waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
        command.has_value() &&
        command->operation_command() ==
            pb::OperationCommand::kExitWayPointAssistMode) ||
       waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning) &&
      !lane_sequence_from_end_point_.empty()) {
    // 1. Don't enter kExitReasoning when ego is in pull out maneuver.
    const bool is_pull_out_triggered = prev_maneuver_seed.speed_seed()
                                           .speed_reasoner_seeds()
                                           .pullout_reasoner_seed()
                                           .is_triggered();
    if (is_pull_out_triggered) {
      return true;
    }

    // 2. Ego should return to kIdle phase from kExitReasoning when has been
    // stucked for kTimeThresholdReturnIdleFromExitReasoningMs.
    if (start_stuck_time_in_kExitReasoning_ms_ > 0 &&
        base::Now() - start_stuck_time_in_kExitReasoning_ms_ >
            kTimeThresholdReturnIdleFromExitReasoningMs) {
      return true;
    }

    // 3. Exit kExitReasoning when ego has reached the drawable lane sequence
    // and these is no overlapping lane in ego position.
    bool is_ego_in_exitable_lane =
        IsEgoAtOnlyDirectionLanes(drawable_lane_sequence_, robot_state);

    // 4. Ego must reach the same direction lane sequence as the end point
    // located lane before return to idle phase.
    const auto& end_point_located_lane = lane_sequence_from_end_point_.front();
    if (is_ego_in_exitable_lane) {
      const auto& current_lanes =
          FLAGS_planning_enable_current_lane_reasoner
              ? current_lane_reasoner.current_lanes_for_regional_path()
              : current_lane_associator.current_lanes_for_regional_path();
      is_ego_in_exitable_lane = std::any_of(
          current_lanes.begin(), current_lanes.end(), [&](const auto& lane) {
            return lane->turn() == end_point_located_lane->turn() ||
                   lane->turn() == hdmap::Lane_Turn_U_TURN ||
                   lane->turn() == hdmap::Lane_Turn_RIGHT;
          });
      for (const auto& current_lane : current_lanes) {
        if (is_ego_in_exitable_lane) {
          break;
        }
        for (const auto& lane :
             lane_sequence_result_for_waypoint_assist_->lane_sequence) {
          if (current_lane->id() == lane->id()) {
            is_ego_in_exitable_lane = true;
            break;
          }
        }
      }
    }

    // Still latch if the current lanes are discontinuous.
    // 1. If the 'is_current_lanes_continuous' signal is nullopt, it means that
    // in the current frame, it is no need to concern if current lane is
    // continuous. For example, in current lane associator, the signal
    // 'is_waypoint_assist_invoked' is false.
    // 2. If 'is_current_lanes_continuous' has value and the value is false, it
    // means that current lanes haven't go back to the potential sections whcih
    // is generated from last selected RA proposed lane sequence. So in this
    // case it is not ready to idle the phase.
    if (is_current_lanes_continuous.has_value() &&
        !is_current_lanes_continuous.value()) {
      return false;
    }

    if ((has_lane_sequence_proposal_responded_ || is_turn_right_scene_) &&
        is_ego_in_exitable_lane && has_left_assist_blockage_area) {
      return true;
    }
  }

  return false;
}

bool WaypointAssistTracker::IsReadyForWaitingPointsPhase(
    const std::optional<pb::WaypointBasedAssistCommand>& command,
    bool is_near_stationary) const {
  if (!is_near_stationary) {
    return false;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
      has_arrived_at_waypoint_assist_path_end_) {
    return true;
  }

  if (last_waypoint_assist_phase_ == pb::AssistManeuverPhase::kPreparingPath &&
      path_failure_type_ != pb::PathFailureType::PATH_VALID) {
    return true;
  }

  if (!command.has_value()) {
    return false;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kIdle &&
      command->operation_command() ==
          pb::OperationCommand::kEnterWayPointAssistMode) {
    return true;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning &&
      command->operation_command() ==
          pb::OperationCommand::kEnterWayPointAssistMode) {
    return true;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kWaitingConfirm &&
      command->operation_command() == pb::OperationCommand::kReplanPath) {
    return true;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
      command->operation_command() == pb::OperationCommand::kReplanPath) {
    return true;
  }

  if (waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning &&
      command->operation_command() == pb::OperationCommand::kReplanPath) {
    return true;
  }

  return false;
}

bool WaypointAssistTracker::IsReadyForPreparingPathPhase(
    const std::optional<pb::WaypointBasedAssistCommand>& command,
    bool is_near_stationary) const {
  if (!is_near_stationary || !command.has_value()) {
    return false;
  }

  return waypoint_assist_phase_ == pb::AssistManeuverPhase::kWaitingPoints &&
         command->operation_command() ==
             pb::OperationCommand::kPrepareReferencePoints;
}

bool WaypointAssistTracker::IsReadyForWaitingConfirmPhase(
    bool is_near_stationary) const {
  if (!is_near_stationary) {
    return false;
  }
  return waypoint_assist_phase_ == pb::AssistManeuverPhase::kPreparingPath;
}

bool WaypointAssistTracker::IsReadyForFollowingPathPhase(
    const std::optional<pb::WaypointBasedAssistCommand>& command,
    bool is_near_stationary) const {
  if (!is_near_stationary) {
    return false;
  }
  if (av_comm::InSimulation() &&
      FLAGS_planning_enable_waypoint_assist_auto_confirm_in_simulation) {
    return waypoint_assist_phase_ == pb::AssistManeuverPhase::kWaitingConfirm;
  }

  return waypoint_assist_phase_ == pb::AssistManeuverPhase::kWaitingConfirm &&
         command.has_value() &&
         command->operation_command() == pb::OperationCommand::kFollowPath;
}

bool WaypointAssistTracker::IsReadyForExitReasoningPhase(
    const std::optional<pb::WaypointBasedAssistCommand>& command) const {
  if (command.has_value() &&
      waypoint_assist_phase_ == pb::AssistManeuverPhase::kFollowingPath &&
      command->operation_command() ==
          pb::OperationCommand::kExitWayPointAssistMode) {
    return true;
  }

  return false;
}

// Check if points projection are incremental along the lane sequence.
bool WaypointAssistTracker::ValidPointProjectionIncremental(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const math::geometry::Point2d& front_bumper_position) {
  const math::geometry::PolylineCurve2d nominal_path_lane_follow =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine,
          /*should_use_prior_path=*/false,
          /*need_center_line_smooth=*/false);
  double cur_arclength = nominal_path_lane_follow
                             .GetProximity(front_bumper_position,
                                           math::pb::UseExtensionFlag::kForbid)
                             .arc_length;

  if (is_waypoint_triggered_by_routing_) {
    trimmed_reference_point_.erase(
        std::remove_if(
            trimmed_reference_point_.begin(), trimmed_reference_point_.end(),
            [&](const math::geometry::Point2d& point) {
              return cur_arclength >=
                     nominal_path_lane_follow
                         .GetProximity({point.x(), point.y()},
                                       math::pb::UseExtensionFlag::kAllow)
                         .arc_length;
            }),
        trimmed_reference_point_.end());
  }
  for (const auto& point : trimmed_reference_point_) {
    const math::ProximityQueryInfo current_point_projection =
        nominal_path_lane_follow.GetProximity(
            {point.x(), point.y()}, math::pb::UseExtensionFlag::kAllow);
    if (cur_arclength >= current_point_projection.arc_length) {
      return false;
    }
    cur_arclength = current_point_projection.arc_length;
  }
  return true;
}

std::vector<const pnc_map::Lane*>
WaypointAssistTracker::GetOppositeLeftMostLanes(
    const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  // Lane sequence empty.
  std::vector<const pnc_map::Lane*> opposite_left_most_lanes;
  if (lane_sequence.empty()) {
    return opposite_left_most_lanes;
  }

  // Get lane sequence's center line
  const math::geometry::PolylineCurve2d lane_sequence_center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine,
          /*should_use_prior_path=*/false,
          /*need_center_line_smooth=*/false);

  // Retrieve ego's current position and arclength.
  const math::geometry::Point2d& current_ego_pos =
      robot_state.current_state_snapshot().rear_axle_position();
  const double current_ego_pos_arclength =
      lane_sequence_center_line
          .GetProximity(current_ego_pos, math::pb::UseExtensionFlag::kAllow)
          .arc_length;

  std::unordered_set<int64_t> checked_opposite_road_ids;
  for (const auto* lane : lane_sequence) {
    // Skip if the lane's start point is far or end point is behind ego.
    const auto& lane_start_point = lane->center_line().GetStartPoint();
    const auto& lane_end_point = lane->center_line().GetEndPoint();
    if (lane_sequence_center_line
                .GetProximity(lane_end_point,
                              math::pb::UseExtensionFlag::kForbid)
                .arc_length < current_ego_pos_arclength ||
        lane_sequence_center_line
                    .GetProximity(lane_start_point,
                                  math::pb::UseExtensionFlag::kForbid)
                    .arc_length -
                current_ego_pos_arclength >
            kMaxDistBetweenStartAndEgo) {
      continue;
    }

    // Get opposite road's sections
    if (!lane || !lane->section() || !lane->section()->road() ||
        !lane->section()->road()->opposite_road() ||
        lane->section()->road()->opposite_road()->sections().empty()) {
      continue;
    }
    // Avoid processing the same opposite road.
    const auto& opposite_road = lane->section()->road()->opposite_road();
    if (checked_opposite_road_ids.find(opposite_road->id()) !=
        checked_opposite_road_ids.end()) {
      continue;
    }
    checked_opposite_road_ids.insert(opposite_road->id());

    const auto& opposite_sections = opposite_road->sections();
    for (const auto* const opposite_section : opposite_sections) {
      DCHECK(opposite_section != nullptr);
      // Get opposite left most lane.
      const auto& opposite_section_leftmost_lane =
          opposite_section->GetLeftMostDrivableLane();

      // Skip if opposite lane's end point is far or start point is behind ego.
      const auto& opposite_lane_start_point =
          opposite_section_leftmost_lane->center_line().GetStartPoint();
      const auto& opposite_lane_end_point =
          opposite_section_leftmost_lane->center_line().GetEndPoint();
      if (lane_sequence_center_line
                  .GetProximity(opposite_lane_start_point,
                                math::pb::UseExtensionFlag::kAllow)
                  .arc_length < current_ego_pos_arclength ||
          lane_sequence_center_line
                      .GetProximity(opposite_lane_end_point,
                                    math::pb::UseExtensionFlag::kAllow)
                      .arc_length -
                  current_ego_pos_arclength >
              kMaxDistBetweenStartAndEgo) {
        continue;
      }

      opposite_left_most_lanes.push_back(
          DCHECK_NOTNULL(opposite_section_leftmost_lane));
    }
  }
  return opposite_left_most_lanes;
}

std::vector<const pnc_map::Lane*>
WaypointAssistTracker::FindBestMatchLaneSequence(
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const math::geometry::Point2d ego_pos,
    std::vector<int64_t>& end_point_lane_ids) {
  std::vector<const pnc_map::Lane*> back_trace_result;
  double min_dist_to_prefix = std::numeric_limits<double>::max() - 1;
  std::vector<const pnc_map::Lane*> end_point_lanes =
      joint_pnc_map_service.GetLaneSequence(end_point_lane_ids);
  end_point_lane_ids.clear();
  const pnc_map::Lane* base_match_start_lane = nullptr;
  for (const auto& start_lane : end_point_lanes) {
    if (start_lane == nullptr) {
      continue;
    }
    double dist_to_prefix = BackTrackFromEndPointLane(
        current_lane_associator, current_lane_reasoner, ego_pos, start_lane,
        start_lane, 0, min_dist_to_prefix, back_trace_result);
    if (dist_to_prefix == min_dist_to_prefix) {
      base_match_start_lane = start_lane;
    }
  }
  end_point_lane_ids.emplace_back(DCHECK_NOTNULL(base_match_start_lane)->id());
  return back_trace_result;
}

pb::PathFailureType WaypointAssistTracker::DoPreparingPath(
    const RobotState& robot_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const ::google::protobuf::RepeatedPtrField<::voy::Point2d>&
        reference_points) {
  // Check if the reference_points size is too small.
  if (reference_points.size() < math::kMinimumInterpolationPointNum) {
    return pb::PathFailureType::POINTS_SMALL_SIZE;
  }
  trimmed_reference_point_ = TrimReferencePoints(reference_points);
  path_failure_type_ = ValidateWaypointAssistReferencePoint(
      lane_sequence, drawable_lane_sequence_, extended_drawable_lane_sequence_,
      trimmed_reference_point_);
  if (path_failure_type_ != pb::PathFailureType::PATH_VALID) {
    return path_failure_type_;
  }
  waypoint_assist_end_point_ =
      std::make_optional(reference_points[reference_points.size() - 1]);
  std::vector<int64_t> end_point_lane_ids;
  for (const auto& lane : drawable_lane_sequence_) {
    if (math::geometry::Within(waypoint_assist_end_point_.value(),
                               lane->border())) {
      end_point_lane_ids.push_back(lane->id());
    }
  }
  for (const auto& lane : extended_drawable_lane_sequence_) {
    if (math::geometry::Within(waypoint_assist_end_point_.value(),
                               lane->border())) {
      end_point_lane_ids.push_back(lane->id());
    }
  }
  std::vector<const pnc_map::Lane*> best_match_lane_sequence =
      FindBestMatchLaneSequence(
          current_lane_associator, current_lane_reasoner, joint_pnc_map_service,
          robot_state.current_state_snapshot().rear_axle_position(),
          end_point_lane_ids);

  // Extend waypoint endpoint stopline.
  waypoint_assist_arclength_ =
      math::geometry::PolylineCurve2d(trimmed_reference_point_)
          .GetTotalArcLength();

  // Check if ego is in the opposite road or road has been entirely blocked.
  is_last_waypoint_in_opposite_lanes_ = false;
  if (is_ego_in_oppsite_road_ || is_road_entirely_blocked_) {
    is_last_waypoint_in_opposite_lanes_ =
        waypoint_assist_end_point_.has_value() &&
        std::any_of(
            opposite_lane_sequence_.begin(), opposite_lane_sequence_.end(),
            [this](const pnc_map::Lane* lane_sequence) {
              return math::geometry::Within(waypoint_assist_end_point_.value(),
                                            lane_sequence->border());
            });
  }

  // Generate lane sequence from last waypoint.
  if (!is_last_waypoint_in_opposite_lanes_) {
    const std::vector<
        std::vector<const pnc_map::Lane*>>& lane_sequences_for_waypoint_assist =
        lane_selection::GetLaneSequencesForWaypointAssist(
            global_route_solution, joint_pnc_map_service,
            drivable_lane_reasoner,
            math::geometry::Point2d(waypoint_assist_end_point_->x(),
                                    waypoint_assist_end_point_->y()),
            end_point_lane_ids,
            /*ego_timestamp=*/robot_state.current_state_snapshot().timestamp());
    for (const auto& lane_sequence : lane_sequences_for_waypoint_assist) {
      if (lane_sequence.empty()) {
        continue;
      }
      lane_sequence_from_end_point_ = lane_sequence;
      // Backtrace.
      lane_sequence_result_for_waypoint_assist_ =
          std::make_shared<LaneSequenceResult>();
      best_match_lane_sequence.insert(best_match_lane_sequence.end(),
                                      lane_sequence.begin(),
                                      lane_sequence.end());
      lane_sequence_result_for_waypoint_assist_->lane_sequence =
          best_match_lane_sequence;
      lane_sequence_result_for_waypoint_assist_->untrimmed_lane_sequence =
          best_match_lane_sequence;
      lane_sequence_result_for_waypoint_assist_->current_lane =
          best_match_lane_sequence.front();
      lane_sequence_result_for_waypoint_assist_->lane_sequence_type =
          pb::LaneSequenceCandidate::LANE_FOLLOW;
      UpdateCurrentLaneForWaypointAssist(robot_state);
      break;
    }

    if (!lane_sequence_result_for_waypoint_assist_) {
      return pb::PathFailureType::NOT_END_IN_LANE_FOLLOW_SEQUENCE;
    } else if (FLAGS_planning_enable_waypoint_assist_check_point_incremental &&
               !ValidPointProjectionIncremental(
                   lane_sequence_result_for_waypoint_assist_->lane_sequence,
                   robot_state.current_state_snapshot()
                       .front_bumper_position())) {
      return pb::PathFailureType::POINTS_NOT_INCREMENTAL;
    } else if (!ValidPointProjectionIncremental(
                   lane_sequence_result_for_waypoint_assist_->lane_sequence,
                   robot_state.current_state_snapshot()
                       .front_bumper_position())) {
      rt_event::PostRtEvent<
          rt_event::planner::WaypointAssistPointNotIncremental>();
    }
  }

  // Check if the reference_points size is too small.
  if (trimmed_reference_point_.size() < math::kMinimumInterpolationPointNum) {
    return pb::PathFailureType::POINTS_SMALL_SIZE;
  }
  // Check if the first point is faraway from ego position.
  if (math::geometry::Distance(
          trimmed_reference_point_.front(),
          robot_state.current_state_snapshot().front_bumper_position()) >
      kMaxDistanceBetweenDrawingPoints) {
    return pb::PathFailureType::POINTS_FARAWAY_FORM_EGO;
  }
  if (!current_lane_associator.GetDrivablePhysicalNearLaneCandidates()
           .empty()) {
    pb::PathFailureType path_failure_type_hb =
        IsReferenceCurveIntersectWithHardBoundary(
            joint_pnc_map_service, trimmed_reference_point_,
            current_lane_associator.GetDrivablePhysicalNearLaneCandidates()[0]
                ->lane());
    if (path_failure_type_hb != pb::PathFailureType::PATH_VALID) {
      return path_failure_type_hb;
    }
  }
  reference_point_located_lane_ =
      GetLanesForReferencePoints(joint_pnc_map_service, reference_points);
  return pb::PathFailureType::PATH_VALID;
}
}  // namespace planner
