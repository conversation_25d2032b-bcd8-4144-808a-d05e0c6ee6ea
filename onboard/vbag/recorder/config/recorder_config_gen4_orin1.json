{"bags": [{"bagName": "camera_orin1", "compressionType": "UNCOMPRESSED", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/camera_gen4_video_frame_short_1", "/camera_gen4_video_frame_short_2", "/camera_gen4_video_frame_short_3", "/camera_gen4_video_frame_short_4", "/camera_video_frame_101", "/camera_video_frame_tl", "/raw_camera_gen4_infrared_1"], "maxSizeMb": 5}]}, {"bagName": "default_orin1", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/converted_gps_imu", "/converted_raw_gps", "/converted_raw_gps_secondary", "/raw_imu_data", "/localization/camera_pose", "/localization/visual_odometry_pose", "/localization/fusion_state", "/localization/fusion_pose", "/localization/lidar_pose", "/localization/lidar_coarse_pose", "/localization/map_based_localization_results", "/localization/gnss_pose", "/localization/ins_pose", "/localization/fusion_seed", "/localization/feature_lidar_localizer_seed", "/localization/semantic_localizer_seed", "/localization/lane_localizer_seed", "/localization/local_odometry_pose", "/localization/instance_localizer_seed", "/localization/yaw_bias_estimator_seed", "/localization/localization_fault", "/v2x/tracked_object_list", "/v2x/traffic_light_detect", "/v2x/obu_status", "/camera_lane_detect", "/speed_bump_list_detect", "/ss_fallback_canfd_info", "/stop_line_list_detect", "/camera_soiling_detect", "/order_service", "/issue_record", "/event_handle_info", "/calibration/main_lidar", "/calibration/camera_roll_offset", "/calibration/camera_yaw_pitch_offset", "/calibration/side_lidar_yaw_offset", "/calibration/side_lidar_z_pitch_roll_offset", "/calibration/radar_yaw_offset", "/perception/camera_object_list", "/perception/camera_segmentation", "/perception/lidar_detected_list", "/perception/lidar_detected_map_list", "/perception/lidar_detected_curb_list", "/perception/processed_map_detection_list", "/perception/lidar_object_list", "/perception/radar_clusters", "/perception/occlusion_map", "/vehicle/body_info", "/vehicle/assist_info", "/system_state", "/health/action_factory", "/health/device_can_orin1", "/health/control_node", "/health/cpubox1_custom_recorder_node", "/health/gmsl_camera_xavier1", "/health/gmsl_camera_xavier2", "/health/gmsl_infrared_camera", "/health/device_gnss_orin1", "/health/ntrip_client_node", "/health/gpu_monitor", "/health/infrared_camera", "/health/laptop_network_monitor", "/health/localization_node", "/health/network_monitor", "/health/ntb_bridge", "/health/onboard_v2x", "/health/order_service_v2_node", "/health/multi_relay", "/health/regulator_node", "/health/recorder_node", "/health/recorder_node_orin1", "/health/recorder_node_orin2", "/health/recorder_node_orin4", "/health/sensing_node", "/health/storage_monitor", "/health/system_control", "/health/tracking_node", "/health/trajectory_guider_node", "/health/vehicle_node_orin1", "/health/collision_detection_node", "/health/lidar", "/health/main_lidar", "/health/side_lidar", "/health/service_restore", "/health/sys_agent", "/health/robotaxi_device_control", "/health/telemetry_node", "/health/xavier1_custom_recorder_node", "/health/xavier1_network_monitor", "/health/xavier2_custom_recorder_node", "/health/xavier2_network_monitor", "/health/xavier2_ntb_bridge", "/health/xavier1_system_control", "/health/xavier2_system_control", "/health/fault_detector", "/request_order_service/trigger_pull_over", "/health/xavier2_aurix_driver", "/robotaxi_device_control/device_state_info", "/robotaxi_device_control/oms_identify_info", "/remote/control", "/remote/health", "/multi_relay/onroute_status", "/multi_relay/robotaxi_route_info", "/multi_relay/visual_agent_list", "/realtime_map/construction_zone_list", "/realtime_map/map_change_area_list", "/realtime_map/update_data", "/realtime_map/traffic_lights", "/realtime_map/traffic_light_phase", "/realtime_map/empirical_publish_data", "/system_supervisor/fatal", "/system_supervisor/error", "/system_supervisor/warn", "/system_supervisor/info", "/system_supervisor/metric_list", "/ads_event", "/fault_inject", "/action_factory/camera_self_cleaning_message", "/action_factory/system_supervisor_mrc_triggers", "/ticket_event", "/system_event", "/action_factory/vehicle_parking_status", "/action_factory/collision_upstream_faults", "/mapping/panel_elements_visualization", "/mapping/map_change_area_list", "/mapping/map_change_info", "/mapping/map_element_change_event", "/mapping/map_element_change", "/mapping/online_map_detection", "/mapping/tracked_map_element_list", "/mapping/changed_map_elements", "/mapping/local_online_map", "/restore_record", "/cpubox1_external_disk_free_space", "/event", "/fallback_event_info", "/onboard_metrics_warning", "/se_data_acquisition", "/telemetry/monitor", "/network_info", "/geolocation_indicator", "/vehicle/sensor_cleaner_info", "/downstream_ticket_event", "/shape_stream", "/rosout_agg", "/action_factory/can_communication_record", "/middleware_error", "/hdmap/map_change_preprocess"], "maxSizeMb": 5}]}, {"bagName": "autonomy_orin1", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/control", "/control/control_debug", "/control/seed", "/control/corrupted_pose", "/control/replan_request", "/pose", "/traffic_light_detect", "/obstacle_list", "/perception/tracked_object_list", "/perception/collision_detection", "/perception/construction_zone_list", "/perception/construction_zone_change", "/perception/occupancy_model_result_list", "/perception/scene_understanding", "/perception/sensor_abnormal_pub", "/perception/sensor_abnormal_pub_internal", "/perception/lidar_pv_abnormal_output", "/perception/pv_abnormal_response", "/gateway", "/fallback_mcu_info", "/route/request", "/routing/query", "/routing/solution", "/routing/route_command", "/routing/planner_route_command", "/routing/planning_route_state", "/routing/route_status", "/routing/seed", "/routing/state_for_sim", "/recurring_task/frame", "/recurring_task/frame_agg", "/realtime_map/update_request"], "maxSizeMb": 5}]}, {"bagName": "lidar_orin1", "compressionType": "ZSTD", "maxDurationS": 120, "bagType": "NORMAL", "topicGroups": [{"priority": 1, "topics": ["/lidar_gen4_packets_side_1", "/lidar_gen4_packets_side_2", "/lidar_gen4_packets_side_3", "/lidar_gen4_packets_side_4", "/lidar_gen4_packets_side_5", "/lidar_gen4_packets_side_6", "/lidar_gen4_udp_packets_side_1", "/lidar_gen4_udp_packets_side_2", "/lidar_gen4_udp_packets_side_3", "/lidar_gen4_udp_packets_side_4", "/lidar_gen4_udp_packets_side_5", "/lidar_gen4_udp_packets_side_6"], "maxSizeMb": 5}]}], "numberCompressionThread": 2, "maxBufferDurationMs": 5000, "maxBufferSizeMb": 4096}