#include "restore_record_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

RestoreRecordObserver::RestoreRecordObserver(RestoreRecordCallback callback)
    : callback_(std::move(callback)) {}

bool RestoreRecordObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kRestoreRecord);
}

std::shared_ptr<google::protobuf::Message> RestoreRecordObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto msg = std::make_shared<service_restore::pb::RestoreRecord>();
  if (!msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error RestoreRecord message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (callback_) {
    callback_(msg, data_channel);
  }
  return msg;
}

}  // namespace remote_assist
