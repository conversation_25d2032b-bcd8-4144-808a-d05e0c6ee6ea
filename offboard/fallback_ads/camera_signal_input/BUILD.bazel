package(default_visibility = ["//visibility:public"])

cc_library(
    name = "camera_sig_input",
    srcs = [
        "CameraSigInput.c",
        "CameraSigInput_data.c",
    ],
    hdrs = [
        "CameraSigInput.h",
        "CameraSigInput_private.h",
        "CameraSigInput_types.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    # include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_camera_sig_input",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:rtwtypes",
    ],
)
