#include "planner/global_object_manager/prediction_trajectory_update_utility/prediction_trajectory_update_utility.h"

#include <glog/logging.h>
#include <gtest/gtest.h>

#include "planner/global_object_manager/objects_evaluation_utility/objects_evaluation_utility.h"
#include "planner/global_object_manager/test/global_object_manager_test_utility_fixture.h"

namespace planner {
namespace {
bool IsAgentStationary(const prediction::pb::Agent& agent) {
  return agent.stationary_intention().stationary_intention_type() ==
         prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE;
}

bool HasCBPTrajectory(const prediction::pb::Agent& agent) {
  return agent.conditional_trajectories()
                 .yield_cbp_trajs_pair()
                 .target_trajectories_size() > 0 &&
         !IsAgentStationary(agent);
}

}  // namespace
class PredictionTrajectoryUpdateUtilityTest
    : public ::testing::Test,
      public assist::GlobalObjectManagerTestUtilityFixture {};

TEST_F(PredictionTrajectoryUpdateUtilityTest,
       UpdatePredictionTrajectoryMapWithoutIrrelevantObjects) {
  std::unordered_set<TypedObjectId> irrelevant_object_ids;
  TbbPredictionTrajectoryMap object_prediction_map;
  TbbConditionalPredictionTrajectoryMap object_conditional_prediction_map;
  utility::UpdatePredictionTrajectoryMap(
      snapshot_timestamp_, agent_list_, irrelevant_object_ids,
      &object_prediction_map, &object_conditional_prediction_map,
      destruction_background_pool_.get());

  // all object in agent_list should be in object_prediction_map
  EXPECT_EQ(agent_list_.agent_list_size(), object_prediction_map.size());

  for (const auto& agent : agent_list_.agent_list()) {
    ObjectId id = agent.tracked_object().id();
    auto it = object_prediction_map.find(id);

    // all object in agent_list should be in object_prediction_map
    EXPECT_NE(it, object_prediction_map.end());

    const auto it_conditional = object_conditional_prediction_map.find(id);

    if (HasCBPTrajectory(agent)) {
      EXPECT_NE(it_conditional, object_conditional_prediction_map.end());
      EXPECT_FALSE(it_conditional->second.yield_cbp_trajs_pair
                       .target_trajectories.empty());
    } else {
      EXPECT_EQ(it_conditional, object_conditional_prediction_map.end());
    }
  }
}

TEST_F(PredictionTrajectoryUpdateUtilityTest,
       UpdatePredictionTrajectoryMapWithIrrelevantObjects) {
  std::unordered_set<TypedObjectId> irrelevant_object_ids;
  std::unordered_set<TypedObjectId> stationary_object_ids;
  pb::PlannerObjectDebug planner_debug;
  bool has_off_road_objects{false};
  bool has_path_recently_go_offroad{false};

  utility::AddIgnoreObjectIdsToIrrelevantObjectIds(
      agent_list_, perception_fp_obstacle_tracker_, &irrelevant_object_ids,
      &planner_debug);

  utility::GetStationaryObjectIds(
      agent_list_,
      planner_object_config_.pedestrian_near_stationary_speed_in_mps(),
      planner_object_config_.default_agent_near_stationary_speed_in_mps(),
      &stationary_object_ids);

  utility::AddOffroadObjectIdsToIrrelevantObjectIds(
      snapshot_timestamp_, agent_list_, ego_offroad_info_seed_,
      stationary_object_ids, distance_to_destination_m_, &has_off_road_objects,
      &has_path_recently_go_offroad, &irrelevant_object_ids, &planner_debug);

  TbbPredictionTrajectoryMap object_prediction_map;
  TbbConditionalPredictionTrajectoryMap object_conditional_prediction_map;
  utility::UpdatePredictionTrajectoryMap(
      snapshot_timestamp_, agent_list_, irrelevant_object_ids,
      &object_prediction_map, &object_conditional_prediction_map,
      destruction_background_pool_.get());

  EXPECT_EQ(agent_list_.agent_list_size() - irrelevant_object_ids.size(),
            object_prediction_map.size());

  for (const auto& agent : agent_list_.agent_list()) {
    const auto& id = agent.tracked_object().id();
    const auto it = object_prediction_map.find(id);

    // if object is irrelevant, it should not in object_prediction_map
    if (irrelevant_object_ids.find({agent.tracked_object().id(),
                                    pb::ObjectSourceType::kTrackedObject}) !=
        irrelevant_object_ids.end()) {
      EXPECT_EQ(it, object_prediction_map.end());
      continue;
    }

    EXPECT_NE(it, object_prediction_map.end());

    const auto it_conditional = object_conditional_prediction_map.find(id);

    if (HasCBPTrajectory(agent)) {
      EXPECT_NE(it_conditional, object_conditional_prediction_map.end());
      EXPECT_FALSE(it_conditional->second.yield_cbp_trajs_pair
                       .target_trajectories.empty());
    } else {
      EXPECT_EQ(it_conditional, object_conditional_prediction_map.end());
    }
  }
}

}  // namespace planner
