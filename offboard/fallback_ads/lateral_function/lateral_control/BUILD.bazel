package(default_visibility = ["//visibility:public"])

cc_library(
    name = "lqr_controller",
    srcs = [
        "lqr_controller.c",
    ],
    hdrs = [
        "lqr_controller.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/lateral_control/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:interpolation",
        "//offboard/fallback_ads/common_math_library:linear_quadratic_regulator",
        "//offboard/fallback_ads/common_math_library:matrix",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
        "//offboard/fallback_ads/lateral_function:lateral_function_type",
    ],
)
