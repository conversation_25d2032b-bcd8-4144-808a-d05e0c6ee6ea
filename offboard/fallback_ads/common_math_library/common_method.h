#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_COMMON_METHOD_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_COMMON_METHOD_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>
#include <stdint.h>

#define CML_EPSILON ((float)1e-6F)
#define CML_PI ((float)3.141592653F)

#define CML_Sign(x) (((x) < 0) ? (-(1.0)) : (1.0))
#define CML_IsNonZero(x) ((x) > CML_EPSILON || (x) < -CML_EPSILON)
#define CML_SetBit(uData, uBit) (uData |= (0x01 << (uBit)))
#define CML_ClearBit(uData, uBit) (uData &= (~(0x01 << (uBit))))
#define CML_SecondsToMs(seconds) ((seconds)*1000.0F)
#define CML_RadToDeg(rad) ((rad)*180.0F / CML_PI)

extern float CML_SafeDivision(float dividend, float divisor);

extern float CML_Limit(float input, float lower_limit, float upper_limit);

extern void CML_RateLimit(float input, float lower_rate_limit,
                          float upper_rate_limit, float task_cycle_time,
                          float *last_cycle_output);

extern void CML_LowPassFilter(float input, float *output, float filter_time,
                              float task_cycle_time);
#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_COMMON_METHOD_H_
