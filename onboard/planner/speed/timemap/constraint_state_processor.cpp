#include "planner/speed/timemap/constraint_state_processor.h"

#include <algorithm>

#include "math/math_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/discomforts/discomforts.h"

namespace planner {
namespace speed {

namespace {
// This function returns the lateral gap value based on the required lateral
// gap, min range max required lateral gap, and the two user flags. The main
// logic is:
// (1) by default (only_consider_min_range_lat_gap = false &&
// only_consider_strict = false), we use required_lat_gap,
// The remaining two conditions 2 and 3 are mutually exclusive (guarded by
// DCHECK):
// (2) only_consider_strict = true, we use 0.0;
// (3) only_consider_min_range_lat_gap = true, we use min_range_lat_gap, where
// its value is determined by: 3.1) if (allow_reducing_min_range_lat_gap ==
// false), returns min_range_max_required_lat_gap; 3.2) if
// (allow_reducing_min_range_lat_gap == true), returns the smaller one between
// the input min_range_max_required_lat_gap and req_lat_gap.
double DecideLateralGap(const double required_lat_gap,
                        const double min_range_max_required_lat_gap,
                        const bool only_consider_strict,
                        const bool allow_reducing_min_range_lat_gap,
                        const bool only_consider_min_range_lat_gap) {
  // If not explicitly allow, req_lat_gap should be always larger than
  // min_range_max_lat_gap.
  if (!allow_reducing_min_range_lat_gap) {
    DCHECK_LE(min_range_max_required_lat_gap, required_lat_gap)
        << "Do not allow req lat gap to be smaller than min range max "
           "required lat gap without explicit set.";
  }
  // Actual lateral_gap deciding logic.
  if (!only_consider_strict && !only_consider_min_range_lat_gap) {
    return required_lat_gap;
  }
  if (only_consider_strict) {
    DCHECK(!only_consider_min_range_lat_gap);
    return 0.0;
  }
  DCHECK(only_consider_min_range_lat_gap);
  return std::min(required_lat_gap, min_range_max_required_lat_gap);
}
}  // namespace

void ConstraintStateProcessor::ProcessStates(
    const std::vector<ConstraintState>& states,
    const ConstraintSettings& settings,
    const DiscomfortVaryingMinRange& min_range, double discomfort, bool pass,
    bool ignore_reasoning_context, bool only_consider_min_lat_gap,
    bool only_consider_strict,
    const std::function<void(double, double)>& process_constraint_state) const {
  const double required_lat_gap =
      pass ? settings.pass_required_lat_gap(discomfort)
           : settings.yield_required_lat_gap(discomfort);
  const double lat_gap = DecideLateralGap(
      required_lat_gap, min_range_max_required_lat_gap_, only_consider_strict,
      settings.allow_ignoring_min_range_lat_gap, only_consider_min_lat_gap);
  for (const auto& state : states) {
    if (state.abs_lat_gap > lat_gap) {
      continue;
    }
    const double time = pass ? GetPassTime(state, settings, discomfort,
                                           ignore_reasoning_context)
                             : GetYieldTime(state, settings, discomfort,
                                            ignore_reasoning_context);
    const double x =
        pass ? GetPassX(state, settings, min_range, discomfort,
                        only_consider_strict, ignore_reasoning_context)
             : GetYieldX(state, settings, min_range, discomfort,
                         only_consider_strict, ignore_reasoning_context);
    process_constraint_state(time, x);
  }
}

void ConstraintStateProcessor::ProcessStatesForDynamicRange(
    const std::vector<ConstraintState>& states,
    const ConstraintSettings& settings,
    const DiscomfortVaryingMinRange& min_range, double discomfort,
    double immutable_time, bool pass,
    const std::function<void(double, double, double)>& process_constraint_state)
    const {
  (void)immutable_time;
  const double required_lat_gap =
      pass ? settings.pass_required_lat_gap(discomfort)
           : settings.yield_required_lat_gap(discomfort);

  if (!settings.allow_ignoring_min_range_lat_gap) {
    DCHECK_LE(min_range_max_required_lat_gap_, required_lat_gap)
        << "Do not allow req lat gap to be smaller than min range max required "
           "lat gap without explicit set.";
  }

  for (const auto& state : states) {
    if (state.abs_lat_gap > required_lat_gap) {
      continue;
    }

    const double time = pass ? GetPassTime(state, settings, discomfort,
                                           /*ignore_reasoning_context=*/false)
                             : GetYieldTime(state, settings, discomfort,
                                            /*ignore_reasoning_context=*/false);
    const double x = pass ? GetPassX(state, settings, min_range, discomfort,
                                     /*use_strict_x=*/false,
                                     /*ignore_reasoning_context=*/false)
                          : GetYieldX(state, settings, min_range, discomfort,
                                      /*use_strict_x=*/false,
                                      /*ignore_reasoning_context=*/false);
    // Need signed_longitudinal_speed.
    process_constraint_state(time, x, state.signed_longitudinal_speed);
  }
}

void ConstraintStateProcessor::ProcessStateOfSingleStateConstraint(
    const ConstraintState& state, const ConstraintSettings& settings,
    const DiscomfortVaryingMinRange& min_range, double discomfort, bool pass,
    bool ignore_reasoning_context, bool only_consider_min_lat_gap,
    const std::function<void(double, double, double)>& process_constraint_state)
    const {
  const double required_lat_gap =
      pass ? settings.pass_required_lat_gap(discomfort)
           : settings.yield_required_lat_gap(discomfort);

  const double lat_gap = DecideLateralGap(
      required_lat_gap, min_range_max_required_lat_gap_,
      /*only_consider_strict=*/false, settings.allow_ignoring_min_range_lat_gap,
      only_consider_min_lat_gap);

  if (state.abs_lat_gap > lat_gap) {
    return;
  }

  const double start_time =
      pass ? GetPassTime(state, settings, discomfort, ignore_reasoning_context,
                         /*use_start_time=*/true)
           : GetYieldTime(state, settings, discomfort, ignore_reasoning_context,
                          /*use_start_time=*/true);
  const double end_time =
      pass ? GetPassTime(state, settings, discomfort, ignore_reasoning_context,
                         /*use_start_time=*/false)
           : GetYieldTime(state, settings, discomfort, ignore_reasoning_context,
                          /*use_start_time=*/false);
  const double x =
      pass ? GetPassX(state, settings, min_range, discomfort,
                      /*use_strict_x=*/false, ignore_reasoning_context)
           : GetYieldX(state, settings, min_range, discomfort,
                       /*use_strict_x=*/false, ignore_reasoning_context);
  process_constraint_state(start_time, end_time, x);
}

void ConstraintStateProcessor::ProcessStandoffs(
    const Standoffs& standoffs, double discomfort,
    const std::function<void(double, double)>& process_standoff) const {
  for (const auto& standoff : standoffs) {
    // TODO(shiying): can we increase the sampling interval to improve
    // timing and not sacrifice accuracy?
    // Bound end time to end of time horizon.
    const double t_horizon = steps_ * dt_;
    const double start_time = math::Clamp(standoff.start_time, 0.0, t_horizon);
    const double end_time = math::Clamp(standoff.end_time, 0.0, t_horizon);
    DCHECK_LE(start_time, end_time) << t_horizon;
    const int n_samples =
        math::CeilToIntWithTol((end_time - start_time) * inv_dt_);
    for (int ix = 0; ix < n_samples; ++ix) {
      const double standoff_t = std::min(end_time, ix * dt_ + start_time);
      // TODO(shiying): do we need min-range here?
      const double standoff_x = standoff.yield_x(discomfort);
      process_standoff(standoff_t, standoff_x);
    }
  }
}

double ConstraintStateProcessor::GetPassTime(const ConstraintState& state,
                                             const ConstraintSettings& settings,
                                             double discomfort,
                                             bool ignore_reasoning_context,
                                             bool use_start_time) const {
  return (use_start_time ? state.start_time : state.end_time) -
         (ignore_reasoning_context ? 0.0
                                   : settings.pass_extra_time(discomfort));
}

double ConstraintStateProcessor::GetPassX(
    const ConstraintState& state, const ConstraintSettings& settings,
    const DiscomfortVaryingMinRange& min_range, double discomfort,
    bool use_strict_x, bool ignore_reasoning_context) const {
  double end_x = state.end_x(discomfort);
  if (use_strict_x) {
    // We should only use strict x when the it is a strict state.
    DCHECK(math::NearZero(state.abs_lat_gap));
    end_x = state.strict_end_x;
  }
  const double reasoning_buffer =
      ignore_reasoning_context ? 0.0 : settings.pass_extra_distance(discomfort);
  return end_x + reasoning_buffer +
         GetMinRangeForConstraintState(settings, state, min_range, discomfort,
                                       min_range_max_required_lat_gap_,
                                       /*is_pass=*/true);
}

double ConstraintStateProcessor::GetYieldTime(
    const ConstraintState& state, const ConstraintSettings& settings,
    double discomfort, bool ignore_reasoning_context,
    bool use_start_time) const {
  return (use_start_time ? state.start_time : state.end_time) +
         (ignore_reasoning_context ? 0.0
                                   : settings.yield_extra_time(discomfort));
}

double ConstraintStateProcessor::GetYieldX(
    const ConstraintState& state, const ConstraintSettings& settings,
    const DiscomfortVaryingMinRange& min_range, double discomfort,
    bool use_strict_x, bool ignore_reasoning_context) const {
  double start_x = state.start_x(discomfort);
  if (use_strict_x) {
    // We should only use strict x when the it is a strict state.
    DCHECK(math::NearZero(state.abs_lat_gap));
    start_x = state.strict_start_x;
  }
  const double reasoning_buffer =
      ignore_reasoning_context ? 0.0
                               : settings.yield_extra_distance(discomfort);
  return start_x - reasoning_buffer -
         GetMinRangeForConstraintState(settings, state, min_range, discomfort,
                                       min_range_max_required_lat_gap_,
                                       /*is_pass=*/false);
}

}  // namespace speed
}  // namespace planner
