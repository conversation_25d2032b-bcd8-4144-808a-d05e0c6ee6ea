#ifndef ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_UTILITY_H_
#define ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_UTILITY_H_

#include <map>
#include <memory>
#include <optional>
#include <set>
#include <unordered_map>
#include <vector>

#include "planner/behavior/types/behavior_decision.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/predicted_trajectory_route_association.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/utility/common/config_io.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_generator.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner_protos/nominal_path_planner_config.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/state/world_model.pb.h"
#include "planner_protos/world_model_config.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/pnc_map_service.h"
#include "routing_protos/route_query.pb.h"

namespace planner {

std::unique_ptr<RobotState> MakeRobotState(
    const pb::ReplanLocatorConfig& config);

// Updates the debug information.
void UpdatePickupDropoffZonesDebug(
    const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos,
    const routing::pb::RoutePoint& destination, pb::PickupDropoffDebug* debug);

// Updates the debug info of virtual pickup dropoff.
void UpdateVirtualPickupDropoffDebug(
    const std::vector<hdmap::StopCellEntity>& raw_stop_cells,
    pb::PickupDropoffDebug* debug);

// Updates the debug info of virtual pickup dropoff by stop cell result.
void UpdateVirtualPickupDropoffDebug(
    const std::vector<hdmap::StopCellResult>& stop_cell_results,
    pb::PickupDropoffDebug* debug);

// Updates pull out jump out seed's last jump out current lane and lane sequence
// with |lane_sequence_candidates|.
void UpdateLastPullOutJumpOutLaneSequenceInfo(
    const pb::LaneSequenceCandidates& lane_sequence_candidates,
    pb::PullOutJumpOutSeed* pull_out_jump_out_seed);

// Gets the road where the destination point located.
const pnc_map::Road* GetRoadOfDestination(
    const GlobalRouteSolution& global_route_solution,
    const RegionalMap& regional_map);

// Populate route associated related field in corresponding predicted trajectory
// wrapper, including |associated_route_|,
// |latest_exit_zone_covering_first_pose_opt_|, and |latest_isolated_lane_opt_|.
void PopulateRouteAssociationResultInPredictedTrajectoryWrapper(
    const route_association::PredictedTrajectoryRouteAssociator&
        association_result,
    PredictedTrajectoryWrapper& predicted_trajectory);

// Gets the maximum predicted time that is of interest based on the planning
// immutable region and horizon.
int64_t GetMaxInterestedPredictionTimestamp(int64_t current_pose_time);

// Util function to determine if an agent was considered by speed solver in last
// cycle, which is used to determine whether to include the agent for global
// route association.
bool WasAgentConsideredInSpeedSeed(
    const planner::speed::pb::SpeedSeed& speed_seed, const ObjectId& obj_id);

bool IsTargetSceneToRunGlobalLaneAssociation(
    const std::vector<LaneSequenceResult>& lane_sequence_results);

// BFS to search drivable lanes in next junctions.
google::protobuf::RepeatedField<int64_t> GetDrivableLaneIdsInNextJunctions(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pnc_map::Section* current_section,
    const std::set<int64_t>& blocked_section_ids,
    double percentage_along_current_section);

bool ShouldUpdateRecommendReplan(
    const routing::pb::RouteSolution& route_solution,
    const std::vector<routing::pb::RecommendReplanInfo>&
        recommend_replan_infos);

void UpdateWaypointRecommendRoutingReplan(
    const RobotState& robot_state,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::WaypointRecommendRoutingReplan* waypoint_recommend_routing_replan);

void UpdateRecommendReplanInfoByWaypointAssist(
    const pb::WaypointRecommendRoutingReplan& waypoint_recommend_routing_replan,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos);

// Updates recommend replan infos by road blockage if it blocks current
// direction.
void UpdateRecommendReplanInfosByRoadBlockage(
    const RobotState& robot_state,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const GlobalRouteSolution& global_route_solution,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos);

// Updates recommend replan infos by regional path if it want to make up cost
// map for some directions.
void UpdateRecommendReplanInfosByRegionalPath(
    const RegionalMap& regional_map,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos);

void TryToAddRecommendReplanInfo(
    const GlobalRouteSolution& global_route_solution,
    const RobotState& robot_state,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const RegionalMap& regional_map,
    const pb::WaypointRecommendRoutingReplan& waypoint_recommend_routing_replan,
    routing::pb::RouteQuery* replan_route_query);

void TryToAddDesiredRouteRequest(const pb::RegionalPathDebug& regional_path_pb,
                                 routing::pb::RouteQuery* replan_route_query);

// Here we use the function to set waypoint not available when
// 1.if in the scenario where obj are close to ego and speed reasoning give a
// stop fence.
// 2.if ego is approaching end of lane sequence.
// 3.If ego bbox is close to road hard boundary.
void UpdateWaypointAvailability(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::PlanningDebug* planning_debug);

void ReasonIfWaypointAssistNotAvailableByHardBoundary(
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state, pb::PlanningDebug* planning_debug);

void ReasonIfWaypointAssistNotAvailableByEndOfLaneSequence(
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    pb::PlanningDebug* planning_debug);

void ReasonIfWaypointAssistNotAvailableByStaticObjects(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::PlanningDebug* planning_debug);

// Accessor for current lane in assist stuck detect.
const pnc_map::Lane* GetCurrentLane(
    const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence);

void UpdatePullOutConfirmDebug(
    const pb::PullOutJumpOutSeed& pull_out_jump_out_seed,
    const speed::pb::PulloutReasonerSeed& pullout_reasoner_seed,
    const std::optional<double>& start_position_distance,
    bool is_ego_position_on_route_ready_for_pull_out,
    const GlobalRouteSolution* global_route_solution,
    bool is_ego_start_close_to_stop,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lanes_for_pull_out_jump_out,
    const std::vector<const pnc_map::Lane*>& drivable_physical_lanes,
    bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutConfirmDebug* pull_out_confirm_debug);

// Updates PlanningRouteStatusDebug.
// Assumes planning_route_status_debug update_reason is set already.
void UpdatePlanningRouteStatusDebug(
    const routing::pb::RouteStatus& route_status,
    const routing::pb::RouteSolution& route_solution, int64_t loopback_times,
    bool is_global_route_changed,
    pb::PlanningRouteStatusDebug* planning_route_status_debug);

// Updates world model seed if we select a decoupled trajectory.
void UpdateSeedWithDecoupledManeuver(
    const pb::DecoupledManeuverSeed& decoupled_maneuver_seed,
    const voy::VehicleMode& vehicle_mode,
    const BehaviorDecision& behavior_decision, const pb::Trajectory& trajectory,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    int64_t snapshot_timestamp, pb::WorldModelSeed* mutable_world_model_seed);

void UpdatePlannerExecutionTime(double execute_time,
                                pb::WorldModelSeed* mutable_world_model_seed);

void UpdateLastTrajectorySeed(const pb::Trajectory& trajectory,
                              pb::Trajectory* mutable_trajectory);

void UpdateReplanFollowedPathType(
    const routing::pb::QueryFollowedPathType& replan_followed_path_type,
    pb::RouteReplanManagerSeed* mutable_replan_manager_seed);

// Updates destination successor sections info debug.
void UpdateDestinationSuccessorSectionsInfoDebug(
    const std::vector<const pnc_map::Section*>&
        destination_successor_sections_for_pullover,
    double pullover_final_extend_section_percentage,
    pb::DestinationSuccessorSectionsInfoDebug* debug);

// Inits all ml models when initializing WorldModel to reduce runtime latency.
void InitAllMLModels();

// Posts RT events and logs error messages if regional section vertices is
// invalid.
// Notes(yuzehao): In the future, if the rt events of regional sections
// locator become more and more, we can put all of them into
// |PostRtEventIfRegionalSectionsLocatorInvalid|. In addition, all invalid
// reasons about why regional sections locator is invalid will be indicated in
// |rt_event::planner::RegionalSectionLocatorInvalid| as json string in the
// future.
void PostRtEventIfRegionalSectionsLocatorInvalid(
    const std::vector<const pnc_map::Section*>& potential_sections,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    pb::PotentialSectionsSource potential_sections_source,
    bool is_all_lanes_near_ego_in_section_vertices, bool is_in_auto_mode,
    const routing::utility::OrderInfo& order_info);

// Posts RT events and logs error messages if the current lane updating result
// is invalid.
void PostRtEventIfCurrentLaneInvalid(
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pnc_map::PncMapService* pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    bool is_current_lane_for_regional_path, bool is_in_auto_mode,
    bool is_waypoint_assist_invoked, bool was_reversing,
    const routing::utility::OrderInfo& order_info);

// Posts RT events and logs error messages if the current lane for waypoint
// graph updating result is invalid.
void PostRtEventIfCurrLaneForWaypointGraphInvalid(
    const std::vector<const pnc_map::Lane*>& current_lane_for_regional_path,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_for_waypoint_graph,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pnc_map::PncMapService* pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    bool is_in_auto_mode, bool is_waypoint_assist_invoked, bool was_reversing,
    const routing::utility::OrderInfo& order_info);

// Returns true if ML trajectory init state has diverged from that of planner.
bool MLTrajectoryInitStateHasDivergedFromPlanner(
    const RobotState& robot_state,
    const pb::TrajectoryGuiderOutput& trajectory_guider_output);

// Returns true if ego is on the EXPRESSWAY.
bool IsEgoOnExpressway(const ::voy::Pose& ego_pose,
                       const pnc_map::Lane* current_lane_from_last_cycle,
                       const pnc_map::PncMapService* pnc_map_service);

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_WORLD_MODEL_UTILITY_H_
