#include "planner/speed/reasoning/agent_trajectory_info.h"

#include <algorithm>
#include <unordered_map>
#include <utility>
#include <vector>

#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_solver_debug.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "rt_event/rt_event.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_protos/traffic_light.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace {
// Gets the current lane of the agent trajectory from the associated route.
const pnc_map::Lane* GetAgentTrajectoryCurrentLane(
    const planner::PredictedTrajectoryWrapper& predicted_trajectory) {
  const auto& optional_associated_route =
      predicted_trajectory.associated_route_opt();
  if (!optional_associated_route.has_value()) {
    return nullptr;
  }
  const std::vector<route_association::MapElementAndPoseInfo>&
      associated_route = optional_associated_route.value();
  if (associated_route.empty()) {
    // Returns nullptr if the predicted trajectory is not associated to a route.
    return nullptr;
  }

  // If the first element is not a lane, will return nullptr.
  return associated_route.front().lane_ptr;
}
}  // namespace

std::string SwitchTimeInfo::ToString() const {
  if (policy_type == SwitchTimePolicyType::kNA) {
    return absl::StrCat(debug_str, " NA; ");
  }

  return absl::StrCat(debug_str, absl::StrFormat(" T %0.3f;", switch_time));
}

AgentTrajectoryInfo::AgentTrajectoryInfo(
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    std::vector<OverlapRegionReference>&& overlap_regions,
    ObjectTrajectoryId id)
    : predicted_trajectory_(predicted_trajectory),
      agent_trajectory_current_lane_(
          GetAgentTrajectoryCurrentLane(predicted_trajectory)),
      overlap_regions_(std::move(overlap_regions)),
      id_(id) {}

const OverlapRegionInfo& AgentTrajectoryInfo::overlap_region_info(
    const pb::OverlapRegion& overlap_region) const {
  const auto& overlap_iter = std::find_if(
      overlap_regions().begin(), overlap_regions().end(),
      [&overlap_region](const OverlapRegionReference& overlap) {
        return overlap.get().region_id() == overlap_region.region_id();
      });
  return overlap_region_infos().at(
      std::distance(overlap_regions().begin(), overlap_iter));
}

bool AgentTrajectoryInfo::is_using_crosswalk() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_using_crosswalk;
                     });
}

bool AgentTrajectoryInfo::has_overlap_region_in_encroachment_regions() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_encroachment_regions();
                     });
}

bool AgentTrajectoryInfo::has_overlap_region_in_merge_fork_regions() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_merge_regions ||
                              overlap_region_info.is_in_fork_regions;
                     });
}

bool AgentTrajectoryInfo::has_overlap_region_in_passive_merge_regions() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_passive_merge_regions;
                     });
}

bool AgentTrajectoryInfo::has_overlap_region_in_squeeze_regions() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_squeeze_regions;
                     });
}

bool AgentTrajectoryInfo::has_overlap_region_in_xlane_nudge() const {
  return std::any_of(overlap_region_infos().begin(),
                     overlap_region_infos().end(),
                     [](const OverlapRegionInfo& overlap_region_info) {
                       return overlap_region_info.is_in_xlane_nudge;
                     });
}

bool AgentTrajectoryInfo::IsYieldIntentionUsableAndLowerThan(
    double threshold) const {
  return consider_yield_intention() &&
         yield_intention().yield_probability() < threshold;
}

bool AgentTrajectoryInfo::IsYieldIntentionUsableAndHigherThan(
    double threshold) const {
  return consider_yield_intention() &&
         yield_intention().yield_probability() > threshold;
}

bool AgentTrajectoryInfo::is_exiting_from_exit_zone() const {
  return predicted_trajectory_.latest_exit_zone_covering_first_pose_opt()
      .has_value();
}

pb::AgentTrajectoryInfoDebug AgentTrajectoryInfo::ToProto() const {
  pb::AgentTrajectoryInfoDebug trajectory_info_debug;
  trajectory_info_debug.set_trajectory_id(id_.trajectory_id);
  trajectory_info_debug.set_road_precedence(road_precedence());
  trajectory_info_debug.set_precedence_source(precedence_source());
  trajectory_info_debug.set_precedence_scene_type(precedence_scene_type());
  trajectory_info_debug.set_will_see_ego_time(will_see_ego_time_);
  trajectory_info_debug.set_agent_reaction_tracker_result(
      agent_reaction_tracker_result());
  const auto& optional_optional_yield_probability =
      yield_probability_with_intention_tracking();
  if (optional_optional_yield_probability.has_value()) {
    trajectory_info_debug.set_yield_probability_with_intention_tracking(
        optional_optional_yield_probability.value());
  }
  trajectory_info_debug.set_likelihood(likelihood());
  trajectory_info_debug.set_is_using_crosswalk(is_using_crosswalk());
  trajectory_info_debug.set_is_extra_cautious(
      is_agent_extra_cautious_on_trajectory());
  trajectory_info_debug.set_is_agent_extra_cautious_for_slow_cut_in_in_front(
      is_agent_extra_cautious_for_slow_cut_in_in_front());
  trajectory_info_debug.set_is_oncoming(is_oncoming_);
  trajectory_info_debug.set_is_same_direction(is_same_direction_);
  trajectory_info_debug.set_consider_yield_intention(consider_yield_intention_);
  trajectory_info_debug.set_is_exiting_roundabout_from_inner_lane(
      is_agent_exiting_roundabout_from_inner_lane());
  trajectory_info_debug.set_has_overlap_region_in_encroachment_regions(
      has_overlap_region_in_encroachment_regions());
  trajectory_info_debug.set_has_overlap_region_in_merge_fork_regions(
      has_overlap_region_in_merge_fork_regions());
  trajectory_info_debug.set_has_overlap_region_in_passive_merge_regions(
      has_overlap_region_in_passive_merge_regions());
  trajectory_info_debug.set_has_overlap_region_in_squeeze_regions(
      has_overlap_region_in_squeeze_regions());
  trajectory_info_debug.set_is_overtaking_ego(is_overtaking_ego());
  trajectory_info_debug.set_is_overtaken_by_ego(is_overtaken_by_ego());
  trajectory_info_debug.set_is_turning_right(IsTurningRight());
  trajectory_info_debug.set_is_turning_left(IsTurningLeft());
  trajectory_info_debug.set_is_stationary(IsStationary());
  trajectory_info_debug.set_is_turning_right_inferring_from_associated_lanes(
      is_turning_right_inferring_from_associated_lanes());
  trajectory_info_debug.set_is_cutting_in(is_cutting_in());
  trajectory_info_debug.set_is_cutting_out(is_cutting_out());
  trajectory_info_debug.set_is_exiting_from_exit_zone(
      is_exiting_from_exit_zone());
  trajectory_info_debug.set_is_merging_from_right_turn_at_junction(
      is_merging_from_right_turn_at_junction());
  trajectory_info_debug.set_is_merging_from_side_road(
      is_merging_from_side_road());
  trajectory_info_debug.set_is_on_u_turn_lane(is_on_u_turn_lane());
  trajectory_info_debug.set_is_crossing_opposite_road_blocks(
      is_crossing_opposite_road_blocks());
  trajectory_info_debug.set_is_inside_overtaking(is_inside_overtaking());
  trajectory_info_debug.set_is_moving_trajectory_for_start_to_move_agent(
      is_moving_trajectory_for_start_to_move_agent());
  trajectory_info_debug.set_is_complete_reversing(IsCompleteReverseDriving());
  trajectory_info_debug.set_is_kturn(IsKTurnDriving());
  trajectory_info_debug.set_is_large_vehicle_turning_along_with_ego(
      is_large_vehicle_turning_along_with_ego());
  trajectory_info_debug.set_predicted_average_acceleration(
      predicted_average_acceleration());
  trajectory_info_debug.set_switch_time_debug_str(
      switch_time_info().ToString());
  trajectory_info_debug.set_debug_str(debug_str_);
  *(trajectory_info_debug.mutable_agent_intention_with_model_fusion()) =
      agent_intention_with_model_fusion();
  *(trajectory_info_debug.mutable_agent_reaction_tracker_debug_info()) =
      art_debug();

  for (const auto& conflicting_lane : associated_conflicting_lanes_) {
    pb::ConflictingLaneDebug* conflicting_lane_debug =
        trajectory_info_debug.add_conflicting_lanes();
    conflicting_lane_debug->set_lane_id(
        conflicting_lane.conflicting_lane().id());
    conflicting_lane_debug->set_start_ra_arclength(
        conflicting_lane.conflicting_range_along_track.start_pos);
    conflicting_lane_debug->set_end_ra_arclength(
        conflicting_lane.conflicting_range_along_track.end_pos);
  }

  prediction::pb::YieldIntention yield_intention_debug;
  yield_intention_debug.set_yield_intention_type(
      predicted_trajectory().yield_intention().yield_intention_type());
  yield_intention_debug.set_yield_probability(
      predicted_trajectory().yield_intention().yield_probability());
  *(trajectory_info_debug.mutable_yield_intention()) =
      std::move(yield_intention_debug);

  for (const auto& overlap_region_info : overlap_region_infos_) {
    *(trajectory_info_debug.add_overlap_region_info_debug()) =
        overlap_region_info.ToProto();
  }

  return trajectory_info_debug;
}

}  // namespace speed
}  // namespace planner
