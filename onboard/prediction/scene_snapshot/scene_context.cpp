#include "prediction/scene_snapshot/scene_context.h"

#include <algorithm>
#include <utility>
#include <vector>

#include "latency/latency_stat.h"
#include "perception/sensing/camera/traffic_light_detection/reason_color_utils.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction/common/definition/prediction_gflag.h"
#include "prediction/common/utility/prediction_utility.h"
#include "prediction/scene_snapshot/ego_lane_sequence_util.h"
#include "prediction/scene_snapshot/traffic_light_infer.h"
#include "trace/trace.h"
#include "voy_protos/construction_zones.pb.h"
#include "voy_trace/trace_prediction.h"
#include "voystat/stats_prediction.h"

namespace prediction {
namespace {

constexpr int kHistoryBufferExpireTime = 1000;  // ms.

constexpr double kNearbyLaneQueryRange = 150.0;               // meters.
constexpr double kNearbyLaneQueryRangeForExpressway = 300.0;  // meters.
constexpr double kNearbyMedianStripAndJunctionZoneQueryRange =
    100.0;                                                   // meters.
template <typename T>
std::shared_ptr<const T> FindClosestDataWithinOrNull(
    const av_comm::HistoryBuffer<int64_t, std::shared_ptr<const T>>& queue,
    int64_t timestamp) {
  const auto iter =
      queue.FindClosestDataWithin(timestamp, kHistoryBufferExpireTime);
  return iter == queue.end() ? nullptr : iter->second;
}

SceneContext::MapObjectToTrafficLightState GetTrafficLightStateMap(
    const pnc_map::PredictionMapService& prediction_map_service,
    av_comm::CarRegion car_region, int64_t snapshot_timestamp,
    const SceneContext::TrafficLightHistoryBuffer& traffic_lights_queue,
    const SceneContext::EgoLaneSequenceHistoryBuffer& ego_lane_sequence_queue) {
  const std::shared_ptr<const planner::pb::PlanningLaneSequence>
      raw_ego_lane_sequence = FindClosestDataWithinOrNull(
          ego_lane_sequence_queue, snapshot_timestamp);
  const std::shared_ptr<const voy::TrafficLights> traffic_light_message =
      FindClosestDataWithinOrNull(traffic_lights_queue, snapshot_timestamp);

  if (traffic_light_message != nullptr) {
    // NOTE(yuejianyong): here we need change the traffic light message when do
    // simulation. Because
    // 1 the message may not contain signals in all enter direction.
    // 2 need infer traffic lights' color by mutual exclusion.
    // So when simulation, we will call the inference supported by perception
    // to get traffic light with inferred message.
    const voy::TrafficLights& traffic_light_with_infer_message =
        av_comm::InSimulation()
            ? perception::camera::GetTrafficLightMessageWithInfer(
                  prediction_map_service.hdmap(), *traffic_light_message)
            : *traffic_light_message;

    // TODO(carltianxing): Try to use ego_lane_sequence.
    return InferTrafficLight(traffic_light_with_infer_message,
                             raw_ego_lane_sequence.get(), car_region,
                             prediction_map_service);
  }

  return {};
}

SceneContext::MapObjectToTrafficLightState GetTrafficLightStateMapInHistory(
    const SceneContext::TrafficLightStateHistoryBuffer*
        map_object_to_tl_state_queue,
    int64_t target_timestamp) {
  if (map_object_to_tl_state_queue == nullptr ||
      map_object_to_tl_state_queue->empty()) {
    return {};
  }
  const auto iter = map_object_to_tl_state_queue->FindClosestDataWithin(
      target_timestamp, kHistoryBufferExpireTime);
  return iter == map_object_to_tl_state_queue->end()
             ? SceneContext::MapObjectToTrafficLightState{}
             : iter->second;
}

std::vector<voy::TrackedObject> GetTrafficConesAndBarriers(
    const std::shared_ptr<const voy::TrackedObjectList>& tracked_objects) {
  std::vector<voy::TrackedObject> traffic_cones;
  if (tracked_objects == nullptr) {
    return traffic_cones;
  }
  for (size_t i = 0;
       i < static_cast<size_t>(tracked_objects->tracked_objects_size()); ++i) {
    const voy::TrackedObject& tracked_object =
        tracked_objects->tracked_objects(i);
    if (tracked_object.object_type() ==
            voy::perception::ObjectType::TRAFFIC_CONE ||
        tracked_object.object_type() == voy::perception::ObjectType::BARRIER) {
      traffic_cones.push_back(tracked_object);
    }
  }
  return traffic_cones;
}

}  // namespace

SceneContext::SceneContext(
    const pnc_map::PredictionMapService& prediction_map_service,
    av_comm::CarRegion car_region, int64_t timestamp, voy::Pose ego_pose,
    const SceneContext::TrafficLightHistoryBuffer& traffic_lights_queue,
    const SceneContext::EgoLaneSequenceHistoryBuffer& ego_lane_sequence_queue,
    const SceneContext::ConstructionZoneHistoryBuffer& construction_zones_queue,
    std::shared_ptr<const control::pb::Control> control_message,
    std::shared_ptr<const voy::TrackedObjectList> tracked_objects,
    std::shared_ptr<ObstacleSegmentationResult> obstacle_segmentation_result,
    const TrafficLightStateHistoryBuffer* map_object_to_tl_state_queue,
    int64_t point_cloud_timestamp)
    : obstacle_segmentation_result_ptr_(
          std::move(obstacle_segmentation_result)),
      ego_pose_(std::move(ego_pose)),
      timestamp_(timestamp),
      ego_control_message_(std::move(control_message)) {
  TRACE_EVENT_SCOPE(prediction, SceneContext_SceneContext);
  VOY_LATENCY_STAT_RECORD_PREDICTION(LAT_STAT_SceneContext_SceneContext);

  // TODO(Boyu): log ego pose is used here for map elements query.
  // We may need to revisit the logic when sim pose is needed.
  const hdmap::Point ego_point = utility::PoseToHDMapPointIn3D(ego_pose_);
  // Use different query range for expressway.
  double nearby_lane_query_range = kNearbyLaneQueryRange;  // meters.
  if (utility::IsOnExpressWay(prediction_map_service, ego_point)) {
    nearby_lane_query_range = kNearbyLaneQueryRangeForExpressway;
  }
  nearby_lanes_ =
      prediction_map_service.GetNearbyLanes(ego_point, nearby_lane_query_range);
  nearby_crosswalks_ = prediction_map_service.GetNearbyCrosswalks(
      ego_point, nearby_lane_query_range);
  nearby_exit_zones_ = prediction_map_service.GetNearZones(
      ego_point, nearby_lane_query_range, {hdmap::Zone::ROAD_EXIT});
  nearby_median_strip_zones_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::MEDIAN_STRIP});
  nearby_bus_stop_zones_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::BUS_STOP, hdmap::Zone::BUS_BULB,
       hdmap::Zone::DIRECT_BUS_BULB});
  nearby_fences_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::FENCE});
  nearby_junctions_ = prediction_map_service.GetNearbyJunctions(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange);
  nearby_roads_ =
      prediction_map_service.GetNearbyRoads(ego_point, nearby_lane_query_range);
  nearby_sidewalks_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::SIDEWALK});
  nearby_roundabouts_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::ROUNDABOUT});
  nearby_map_construction_zones_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::CONSTRUCTION});
  nearby_diversion_zones_ = prediction_map_service.GetNearZones(
      ego_point, kNearbyMedianStripAndJunctionZoneQueryRange,
      {hdmap::Zone::DIVERSION_LINE});
  nearby_traffic_cones_and_barriers_ =
      GetTrafficConesAndBarriers(tracked_objects);
  nearby_curbs_ = prediction_map_service.GetNearbyCurbs(
      ego_pose_, kNearbyMedianStripAndJunctionZoneQueryRange);
  const std::shared_ptr<const planner::pb::PlanningLaneSequence>
      raw_ego_lane_sequence =
          FindClosestDataWithinOrNull(ego_lane_sequence_queue, timestamp);
  if (raw_ego_lane_sequence) {
    ego_lane_sequence_ =
        GetEgoLaneSequence(*raw_ego_lane_sequence, prediction_map_service);
  } else {
    DLOG(WARNING) << "Cannot get ego lane sequence from message buffer at "
                  << timestamp;
  }

  raw_construction_zone_list_ =
      FindClosestDataWithinOrNull(construction_zones_queue, timestamp);

  if (raw_construction_zone_list_ == nullptr) {
    DLOG(WARNING) << "Cannot get construction zone list from message buffer at "
                  << timestamp;
  } else {
    for (const voy::ConstructionZone& cz :
         raw_construction_zone_list_->construction_zones_real_time_map()) {
      // Only uses zones from perception and real time map.
      if (cz.zone_source() == voy::ZoneSource::ONBOARD_PERCEPTION_ONLY ||
          cz.zone_source() == voy::ZoneSource::REAL_TIME_MAP_ONLY ||
          cz.zone_source() == voy::ZoneSource::FUSED) {
        DCHECK_NE(cz.space_type(), voy::SpaceType::MAP_CHANGE_AREA)
            << cz.DebugString();
        construction_zones_.push_back(&cz);
      }
    }
  }

  // Construct traffic light.
  map_object_to_tl_state_ =
      GetTrafficLightStateMap(prediction_map_service, car_region, timestamp,
                              traffic_lights_queue, ego_lane_sequence_queue);

  map_object_to_tl_state_history_.reserve(
      constants::kTrafficLightHistoryItemSize);
  for (int i = constants::kTrafficLightHistoryItemSize; i > 0; --i) {
    const int64_t target_timestamp =
        timestamp - i * constants::kTrafficLightHistoryIntervalInMs;
    map_object_to_tl_state_history_.push_back(GetTrafficLightStateMapInHistory(
        map_object_to_tl_state_queue, target_timestamp));
  }

  if (point_cloud_timestamp != constants::kInvalidTimestamp) {
    observed_latency_from_tracking_to_lidar_ =
        point_cloud_timestamp - timestamp;
    STATS_HISTOGRAM(prediction, scene_snapshot,
                    observed_latency_from_tracking_to_lidar,
                    observed_latency_from_tracking_to_lidar_);
  }
}

}  // namespace prediction
