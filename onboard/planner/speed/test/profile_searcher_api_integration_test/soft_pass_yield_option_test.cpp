#include "profile_searcher_test_fixture.h"

#include <glog/logging.h>
#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/reference/reference_generator.h"
#include "planner/speed/solver/searcher/profile_searcher.h"
#include "planner/speed/test_util/test_util.h"

namespace planner {
namespace speed {
// Testing constraints with soft pass or yield options.
class SoftPassYieldOptionTest : public ProfileSearcherTestFixture,
                                public ::testing::Test {
 public:
  SoftPassYieldOptionTest()
      : ProfileSearcherTestFixture(/*pose_time=*/10.0,
                                   /*control_immutable_time=*/0.0) {}
};

// Test the case when we have a hard to yield constraint, making it SOFT makes
// it easier to yield with the desired yield_min_a.
// Original constraint:
// https://drive.google.com/file/d/1jLm2d6syOPhERUiGF_Pv5lnZab0lOr9t/view?usp=sharing
// SOFT:https://drive.google.com/file/d/1k7VP7czSFxtl6-aSwCOs_duOpT9E8cnk/view?usp=sharing
TEST_F(SoftPassYieldOptionTest, SearchSoftYieldConstraint) {
  // Add an avoid region constraint that we can not yield if it is not SOFT.
  constexpr double kStartTime = 3.0;
  constexpr double kEndTime = 4.0;
  constexpr double kStartX = 0.0;
  mutable_constraint_manager().constraint_creator().AddStopPoint(
      kStartTime, kEndTime, kStartX,
      /*fence_type=*/pb::FenceType::kCrossWalkVRU, pb::ReasonerId::STOP_SIGN,
      "xwalk-pass-stop");
  std::vector<SpeedSearchResult> results = RunSearcher();
  EXPECT_EQ(results.size(), 1);
  const auto& result = results[0];
  const auto& constraint_result = result.finalized_constraint_result();
  ASSERT_EQ(
      result.searcher_result(),
      ProfileSearcher::SearchResult::SearchResult_Enum_CONFLICT_RESOLVING);
  EXPECT_EQ(result.selected_discomfort(), 1.0);
  EXPECT_EQ(constraint_result.decisions.front(), Decision::YIELD);
  EXPECT_EQ(constraint_result.dominant_constraint_ix, 0);

  // Make the constraint soft and we should be able to yield now.
  const std::optional<DiscomfortVaryingLimits>& ref_all_limits = all_limits();
  DCHECK(ref_all_limits.has_value());
  ClearConstraintManager();
  mutable_constraint_manager()
      .constraint_creator()
      .AddStopPoint(kStartTime, kEndTime, kStartX,
                    /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                    pb::ReasonerId::STOP_SIGN, "xwalk-pass-stop")
      .set_soft_yield_with_min_a(ref_all_limits->ComfortLimits().brake_a.min);
  // Verify the speed search succeeds.
  std::vector<SpeedSearchResult> results2 = RunSearcher();
  EXPECT_EQ(results2.size(), 1);
  const auto& result2 = results2[0];
  const auto& constraint_result2 = result2.finalized_constraint_result();
  const auto& profile2 = result2.profile();
  ASSERT_EQ(result2.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result2.selected_discomfort(), 0.0);
  EXPECT_EQ(constraint_result2.decisions.front(), Decision::YIELD);
  EXPECT_EQ(constraint_result2.dominant_constraint_ix, 0);
  // Verify profile stays below the constraint.
  const auto& constraint = constraint_result2.constraints.front();
  for (const auto& state : profile2) {
    if (kStartTime + pose_time() <= state.t &&
        state.t <= kEndTime + pose_time() +
                       constraint.settings.yield_extra_distance(0.0)) {
      const double constraint_state_thr =
          constraint.states.front().start_x(0.0);
      EXPECT_LT(state.x, constraint_state_thr -
                             constraint.settings.yield_extra_distance(0.0));
    }
  }
}

TEST_F(SoftPassYieldOptionTest, DiscomfortVaryingSoftBuffers) {
  constexpr double kStartTime = 3.0;
  constexpr double kEndTime = 10.0;
  constexpr double kStartX = 0.0;
  constexpr double kDiscomfort = 0.5;
  constexpr double kDiscomfortYieldBuffer = 5.0;
  constexpr double kHighDiscomfort = 1.0;
  const int discomfort_ix = Discomforts::GetDiscomfortIx(kDiscomfort);
  const std::optional<DiscomfortVaryingLimits>& ref_all_limits = all_limits();
  DCHECK(ref_all_limits.has_value());
  mutable_constraint_manager()
      .constraint_creator()
      .AddStopPoint(kStartTime, kEndTime, kStartX,
                    /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                    pb::ReasonerId::STOP_SIGN, "soft-xwalk-stop")
      .set_soft_yield_with_min_a(
          ref_all_limits->LimitsAtDiscomfortIx(discomfort_ix).brake_a.min)
      .set_yield_extra_distance(
          {/*discomforts=*/{0.0, kDiscomfort, kHighDiscomfort},
           /*vals=*/{kDiscomfortYieldBuffer, kDiscomfortYieldBuffer, 0.0}});
  std::vector<SpeedSearchResult> results = RunSearcher();
  EXPECT_EQ(results.size(), 1);
  const auto& result = results[0];
  const auto& constraint_result = result.finalized_constraint_result();
  ASSERT_EQ(result.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  // If SOFT is the only constraint, searcher should find a solution exactly
  // at the discomfort level corresponding to the set yield_min_a.
  EXPECT_EQ(result.selected_discomfort(), kDiscomfort);
  EXPECT_EQ(constraint_result.decisions.front(), Decision::YIELD);
  const auto& profile_at_low_discomfort = result.profile();

  // If the global discomfort is raised by other constraints. We should be
  // able to progress more, given the soft constraint has a reduced yield
  // buffer at higher discomfort.
  mutable_constraint_manager()
      .constraint_creator()
      .AddStopPoint(kStartTime, kEndTime, kStartX,
                    /*fence_type=*/pb::FenceType::kCrossWalkVRU,
                    pb::ReasonerId::STOP_SIGN, "hard-xwalk-stop")
      .set_if_possible_below_discomfort(kHighDiscomfort);
  std::vector<SpeedSearchResult> results2 = RunSearcher();
  EXPECT_EQ(results2.size(), 1);
  const auto& result2 = results2[0];
  const auto& constraint_result2 = result2.finalized_constraint_result();
  ASSERT_EQ(result2.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result2.selected_discomfort(), kHighDiscomfort);
  EXPECT_EQ(constraint_result2.decisions.front(), Decision::YIELD);
  const auto& profile_at_high_discomfort = result2.profile();
  // The high discomfort profile should progress more by the buffer
  // diff between 0.5 and 1.0.
  const double kBrakeProfileNumericalDiffTolerance = 1.5;
  EXPECT_GE(profile_at_high_discomfort.back().x,
            profile_at_low_discomfort.back().x + kDiscomfortYieldBuffer -
                kBrakeProfileNumericalDiffTolerance);
}

// See
// https://drive.google.com/file/d/1FWncDF5rRQhh2Hq_66icFNN2hefJvugV/view
TEST_F(SoftPassYieldOptionTest, SearchSoftPassConstraint) {
  UpdateReferenceGenerator(/*ego_speed=*/10.0, /*init_accel=*/0.0,
                           /*min_speed=*/10.0,
                           /*max_speed=*/15.0, /*control_immutable_time=*/0.0);
  // Add an avoid region constraint that we can only pass with discomfort 1.0 if
  // it is HARD.
  constexpr double kStartTime = 3.0;
  constexpr double kEndTime = 4.0;
  constexpr double kStartX = 0.0;
  constexpr double kEndX = 35.0;
  mutable_constraint_manager().constraint_creator().AddAvoidRegion(
      kStartTime, kEndTime, kStartX, kEndX, pb::FenceType::kLead,
      pb::ReasonerId::TRAFFIC_LIGHT, "mock-tl");
  std::vector<SpeedSearchResult> results = RunSearcher();
  EXPECT_EQ(results.size(), 1);
  const auto& result = results[0];
  const auto& constraint_result = result.finalized_constraint_result();
  ASSERT_EQ(result.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result.selected_discomfort(), 1.0);
  EXPECT_EQ(constraint_result.decisions.front(), Decision::PASS);

  // Make the constraint soft-pass then we should be able to pass in a lower
  // discomfort.
  const std::optional<DiscomfortVaryingLimits>& ref_all_limits = all_limits();
  DCHECK(ref_all_limits.has_value());
  ClearConstraintManager();
  mutable_constraint_manager()
      .constraint_creator()
      .AddAvoidRegion(kStartTime, kEndTime, kStartX, kEndX,
                      pb::FenceType::kLead, pb::ReasonerId::TRAFFIC_LIGHT,
                      "mock-tl")
      .set_soft_pass_with_max_a(ref_all_limits->ComfortLimits().accel_a.max);
  // Verify the speed search succeeds with lower discomfort.
  std::vector<SpeedSearchResult> results2 = RunSearcher();
  EXPECT_EQ(results2.size(), 1);
  const auto& result2 = results2[0];
  const auto& constraint_result2 = result2.finalized_constraint_result();
  const auto& profile2 = result2.profile();
  ASSERT_EQ(result2.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result2.selected_discomfort(), 0.0);
  EXPECT_EQ(constraint_result2.decisions.front(), Decision::PASS);
  // Verify profile stays above the constraint.
  const auto& constraint = constraint_result2.constraints.front();
  for (const auto& state : profile2) {
    if (kStartTime + pose_time() -
                constraint.settings.pass_extra_distance(0.0) <=
            state.t &&
        state.t <= kEndTime + pose_time()) {
      const double constraint_state_thr = constraint.states.front().end_x(0.0);
      EXPECT_GT(state.x, constraint_state_thr -
                             constraint.settings.pass_extra_distance(0.0));
    }
  }
}

// See
// https://drive.google.com/file/d/15oao-iNmFbS4JQgjsqMtR25Q9OpeuHRO/view
TEST_F(SoftPassYieldOptionTest, SoftPassTailGaterToRaiseDiscomfort) {
  UpdateReferenceGenerator(/*ego_speed=*/10.0, /*init_accel=*/0.0,
                           /*min_speed=*/10.0,
                           /*max_speed=*/15.0);
  // Add a fast-running tailgater constraint that we can only pass at
  // discomfort 1.
  constraint_creator().AddSpeedConstraintFromConstSpeed(
      /*start_time=*/1.0, /*ra_start_pos=*/0.0, /*longitudinal_speed=*/15.0,
      /*dt=*/0.1,
      /*num_states=*/70, /*agent_length=*/3.5, pb::FenceType::kMerge,
      pb::ReasonerId::TAILGATER,
      /*constraint_id=*/"tailgater-agent-const-speed",
      /*obj_id=*/1);
  std::vector<SpeedSearchResult> results = RunSearcher();
  EXPECT_EQ(results.size(), 1);
  const auto& result = results[0];
  const auto& constraint_result = result.finalized_constraint_result();
  ASSERT_EQ(result.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result.selected_discomfort(), 1.0);
  EXPECT_EQ(constraint_result.decisions.front(), Decision::PASS);

  // Make the constraint soft-pass then we should be able to pass in a lower
  // discomfort.
  const std::optional<DiscomfortVaryingLimits>& ref_all_limits = all_limits();
  DCHECK(ref_all_limits.has_value());
  ClearConstraintManager();
  constraint_creator()
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/1.0, /*ra_start_pos=*/0.0, /*longitudinal_speed=*/15.0,
          /*dt=*/0.1,
          /*num_states=*/70, /*agent_length=*/3.5, pb::FenceType::kMerge,
          pb::ReasonerId::TAILGATER,
          /*constraint_id=*/"tailgater-agent-const-speed",
          /*obj_id=*/1)
      .set_soft_pass_with_max_a(
          ref_all_limits->LimitsAtDiscomfortIx(2).accel_a.max);
  // Verify the speed search succeeds with lower discomfort.
  std::vector<SpeedSearchResult> results2 = RunSearcher();
  EXPECT_EQ(results2.size(), 1);
  const auto& result2 = results2[0];
  const auto& constraint_result2 = result2.finalized_constraint_result();
  ASSERT_EQ(result2.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result2.selected_discomfort(), 0.25);
  EXPECT_EQ(constraint_result2.decisions.front(), Decision::PASS);
}

// See
// https://drive.google.com/file/d/1sblfkTYXGhIDRjhrGLAbJmRy6EIV6tEI/view
TEST_F(SoftPassYieldOptionTest,
       SoftPassToRaiseDiscomfortThenIgnoreAtHighDiscomfort) {
  UpdateReferenceGenerator(/*ego_speed=*/10.0, /*init_accel=*/0.0,
                           /*min_speed=*/10.0,
                           /*max_speed=*/15.0);

  ////// CONDITION 1 //////
  // Add a fast-running tailgater constraint that we can only pass at
  // discomfort 1.
  constraint_creator().AddSpeedConstraintFromConstSpeed(
      /*start_time=*/1.0, /*ra_start_pos=*/0.0, /*longitudinal_speed=*/15.0,
      /*dt=*/0.1,
      /*num_states=*/70, /*agent_length=*/3.5, pb::FenceType::kMerge,
      pb::ReasonerId::TAILGATER,
      /*constraint_id=*/"tailgater-agent-const-speed",
      /*obj_id=*/1);
  // Add a stop point ahead that won't allow us to pass.
  mutable_constraint_manager().constraint_creator().AddStopPoint(
      /*start_time=*/6.0, /*end_time=*/8.0, /*ra_stop_x=*/80.0,
      pb::FenceType::kLead, pb::ReasonerId::TRAFFIC_LIGHT, "mock-tl");

  // Search should have no solution and goes to conflict resolving.
  std::vector<SpeedSearchResult> results = RunSearcher();
  EXPECT_EQ(results.size(), 1);
  const auto& result = results[0];
  ASSERT_EQ(
      result.searcher_result(),
      ProfileSearcher::SearchResult::SearchResult_Enum_CONFLICT_RESOLVING);
  EXPECT_EQ(result.selected_discomfort(), 1.0);

  ////// CONDITION 2 //////
  // Make the tailgater a soft-pass then we should be able to pass in discomfort
  // 0.25.
  const std::optional<DiscomfortVaryingLimits>& ref_all_limits = all_limits();
  DCHECK(ref_all_limits.has_value());
  ClearConstraintManager();
  constraint_creator()
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/1.0, /*ra_start_pos=*/0.0, /*longitudinal_speed=*/15.0,
          /*dt=*/0.1,
          /*num_states=*/70, /*agent_length=*/3.5, pb::FenceType::kMerge,
          pb::ReasonerId::TAILGATER,
          /*constraint_id=*/"tailgater-agent-const-speed",
          /*obj_id=*/1)
      .set_soft_pass_with_max_a(
          ref_all_limits->LimitsAtDiscomfortIx(2).accel_a.max)
      .set_no_yield()
      .set_allow_pass_ignore(false);
  // But the stop point ahead still won't allow us to pass.
  mutable_constraint_manager().constraint_creator().AddStopPoint(
      /*start_time=*/6.0, /*end_time=*/8.0, /*ra_stop_x=*/80.0,
      pb::FenceType::kLead, pb::ReasonerId::TRAFFIC_LIGHT, "mock-tl");

  // Search should have no solution, as we won't allow to ignore the
  // tailgater even with soft-pass, and it still conflicts with the TL.
  std::vector<SpeedSearchResult> results2 = RunSearcher();
  EXPECT_EQ(results2.size(), 1);
  const auto& result2 = results2[0];
  ASSERT_EQ(
      result2.searcher_result(),
      ProfileSearcher::SearchResult::SearchResult_Enum_CONFLICT_RESOLVING);
  EXPECT_EQ(result2.selected_discomfort(), 1.0);

  ////// CONDITION 3 //////
  // Make the tailgater a soft-pass then we should be able to pass in discomfort
  // 0.25, and we will start to ignore it above 0.5.
  ClearConstraintManager();
  constraint_creator()
      .AddSpeedConstraintFromConstSpeed(
          /*start_time=*/1.0, /*ra_start_pos=*/0.0, /*longitudinal_speed=*/15.0,
          /*dt=*/0.1,
          /*num_states=*/70, /*agent_length=*/3.5, pb::FenceType::kMerge,
          pb::ReasonerId::TAILGATER,
          /*constraint_id=*/"tailgater-agent-const-speed",
          /*obj_id=*/1)
      .set_soft_pass_with_max_a(
          ref_all_limits->LimitsAtDiscomfortIx(1).accel_a.max)
      .set_no_yield()
      .set_allow_pass_ignore(false)
      .set_if_possible_below_discomfort(0.5);
  // The TL still won't allow us to pass.
  mutable_constraint_manager().constraint_creator().AddStopPoint(
      /*start_time=*/6.0, /*end_time=*/8.0, /*ra_stop_x=*/80.0,
      pb::FenceType::kLead, pb::ReasonerId::TRAFFIC_LIGHT, "mock-tl");

  // Search should have solution at discomfort 0.5, which IGNORE the tailgater,
  // and YIELD to the stop point.
  std::vector<SpeedSearchResult> results3 = RunSearcher();
  EXPECT_EQ(results3.size(), 1);
  const auto& result3 = results3[0];
  const auto& constraint_result3 = result3.finalized_constraint_result();
  ASSERT_EQ(result3.searcher_result(),
            ProfileSearcher::SearchResult::SearchResult_Enum_SUCCESS);
  EXPECT_EQ(result3.selected_discomfort(), 0.5);
  EXPECT_EQ(constraint_result3.decisions.front(), Decision::IGNORE);
  EXPECT_EQ(constraint_result3.decisions.back(), Decision::YIELD);
}

}  // namespace speed
}  // namespace planner
