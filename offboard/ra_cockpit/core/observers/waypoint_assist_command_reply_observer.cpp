#include "waypoint_assist_command_reply_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

WaypointAssistCommandObserver::WaypointAssistCommandObserver(
    WaypointAssistCommandCallback callback)
    : waypoint_assist_command_callback_(std::move(callback)) {}

bool WaypointAssistCommandObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kWaypointAssistCommandReply);
}

std::shared_ptr<google::protobuf::Message>
WaypointAssistCommandObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  std::shared_ptr<teleassist::pb::WaypointAssistCommandReply>
      waypoint_assist_command_reply =
          std::make_shared<teleassist::pb::WaypointAssistCommandReply>();
  if (!waypoint_assist_command_reply->ParseFromString(message.message_data())) {
    LOG(ERROR)
        << "Received error WaypointAssistCommandReply message format, len = "
        << message.message_data().length();
    return nullptr;
  }

  if (waypoint_assist_command_callback_) {
    waypoint_assist_command_callback_(waypoint_assist_command_reply,
                                      data_channel);
  }
  return waypoint_assist_command_reply;
}

}  // namespace remote_assist
