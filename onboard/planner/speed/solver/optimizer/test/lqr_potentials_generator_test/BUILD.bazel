load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "lqr_potentials_generator_test",
    srcs = [
        "constraint_barrier_and_repeller.cpp",
        "following_attractor_test.cpp",
        "kinetics_barrier_and_effort_test.cpp",
        "potentials_generator_test_inst.cpp",
        "potentials_prune_test.cpp",
        "profile_attractor_test.cpp",
        "proximity_speed_test.cpp",
        "utils_test.cpp",
    ],
    deps = [
        "//onboard/common/adv_geom",
        "//onboard/common/math",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner/speed/constraint",
        "//onboard/planner/speed/constraint:constraint_creator",
        "//onboard/planner/speed/discomforts",
        "//onboard/planner/speed/overlap",
        "//onboard/planner/speed/profile",
        "//onboard/planner/speed/solver/optimizer:solver_optimizer",
        "//onboard/planner/speed/solver/optimizer/test:speed_opt_test_fixture",
        "//onboard/planner/speed/solver/searcher:simple_profile_searcher",
        "//onboard/planner/speed/solver/util:risk_mitigation_util",
        "//onboard/planner/speed/test_util:speed_test_util",
        "//onboard/planner/utility/config_center",
        "//protobuf_cpp:protos_cpp",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:absl-container",
        "@voy-sdk//:glog",
        "@voy-sdk//:gmock",
        "@voy-sdk//:gtest",
    ],
)
