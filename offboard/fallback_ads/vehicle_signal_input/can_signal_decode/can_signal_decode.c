
#include "vehicle_signal_input/can_signal_decode/can_signal_decode.h"

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Components/Rte_VehSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/debounce_method.h"
#include "vehicle_signal_input/can_timeout_detection/can_timeout_detection.h"
#include "vehicle_signal_input/vehicle_signal_input_parameter.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

VehicleMode GetVehicleMode(bool vim_module_enable) {
  VehicleMode vehicle_mode = MANUAL;
  // vehicle mode only available in autofull and manual
  // different from the mode on ads canbus
  if (vim_module_enable == TRUE) {
    vehicle_mode = AUTO_FULL;
  } else if (vim_module_enable == false) {
    vehicle_mode = MANUAL;
  } else {
    vehicle_mode = TRANSITION;
  }

  return vehicle_mode;
}

bool HoldEmergencySwitch(FallbackMode fallback_mode) {
  static float emergency_switch_on_timer = 0.0F;
  static bool emergency_switch_on = false;
  CML_TurnOffDelay((fallback_mode == EMERGENCY_SWITCH_ON),
                   GetVsiEmergencySwitchOnHoldTime(), VSI_TASK_CYCLE_TIME,
                   &emergency_switch_on_timer, &emergency_switch_on);
  return emergency_switch_on;
}

FallbackMode GetFallbackMode(uint8 main_can_counter,
                             FallbackMode main_fallback_mode,
                             uint8 redundant_can_counter,
                             FallbackMode redundant_fallback_mode) {
  FallbackMode fallback_mode = NO_FAULT;

  bool main_can_timeout_enable = CheckMainCanTimeout(main_can_counter);
  bool redundant_can_timeout_enable =
      CheckRedundantCanTimeout(redundant_can_counter);

  // choose fallback mode input
  if (main_can_timeout_enable == false) {
    fallback_mode = main_fallback_mode;
  } else if (redundant_can_timeout_enable == false) {
    fallback_mode = redundant_fallback_mode;
  } else {
    // activated by CAN timeout
    fallback_mode = SYSTEM_ERROR;
  }

  // when the emergency switch is switched from on to off
  // Keep it on for a period of time
  fallback_mode =
      HoldEmergencySwitch(fallback_mode) ? EMERGENCY_SWITCH_ON : fallback_mode;

  return fallback_mode;
}

bool FallbackIsActive(VehicleMode vehicle_mode, FallbackMode fallback_mode,
                      bool disable_on_freeway) {
  static float timer = 0.0F;
  static bool fallback_is_triggered = false;
  bool fallback_is_active = false;

  if (GetVsiFallbackActiveSwitchOn()) {
    fallback_is_active = true;
  } else {
    // To turn off the hazard warning lights,
    // the Fallback takeover time is delayed.
    CML_TurnOffDelay((vehicle_mode == AUTO_FULL) && (fallback_mode != NO_FAULT),
                     GetVsiFallbackTakeoverDelayTime(), VSI_TASK_CYCLE_TIME,
                     &timer, &fallback_is_triggered);
    fallback_is_active = fallback_is_triggered && (disable_on_freeway == false);
  }

  return fallback_is_active;
}

bool CollisionDetectionIsValid(uint8 can_counter, uint8 collision_detection) {
  static uint8 last_can_counter = 0;
  static float can_timeout_timer = 0.0;
  static bool can_timeout_enable = false;

  CML_TurnOnDelay((can_counter == last_can_counter),
                  GetVsiCollisionDetectionTimeoutThreshold(),
                  VSI_TASK_CYCLE_TIME, &can_timeout_timer, &can_timeout_enable);
  last_can_counter = can_counter;

  // can timeout or not receive any information from EH
  return (can_timeout_enable == false && (collision_detection & 0x04) == 0U);
}

void ConvertVehicleAndAcuDbcSignals(VehicleAndAcuCanSignal *can_signal) {
  can_signal->PrimVehSpdGroupSafeNom =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe())  // NOLINT
                  ->PrimVehSpdGroupSafeNom *
              0.01F);

  can_signal->PrimVehSpdGroupSafeNomQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe())
          ->PrimVehSpdGroupSafeNomQf;

  can_signal->PrimVLatSafeNom =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimVLatSafe())  // NOLINT
                  ->PrimVLatSafeNom *
              0.001F);

  can_signal->PrimALgtDataRawSafeNomQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr11_SG_PrimALgtDataRawSafe())
          ->PrimALgtDataRawSafeNomQf;

  can_signal->PrimALgtDataRawSafeNom =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr11_SG_PrimALgtDataRawSafe())  // NOLINT
                      ->PrimALgtDataRawSafeNom *
                  0.001F -
              16.384F);

  can_signal->PrimALatDataRawSafeNom =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe())  // NOLINT
                      ->PrimALatDataRawSafeNom *
                  0.001F -
              16.384F);

  can_signal->PrimALatDataRawSafeNomQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe())
          ->PrimALatDataRawSafeNomQf;

  can_signal->PrimYawRateSafeNom =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_PrimYawRateSafe())  // NOLINT
                  ->PrimYawRateSafeNom *
              0.0002F);

  can_signal->YawRate1 =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr08_SG_AgDataRawSafe())  // NOLINT
                  ->YawRate1 *
              0.000244140625F);

  can_signal->YawRate1Qf1 =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr08_SG_AgDataRawSafe())
          ->YawRate1Qf1;

  can_signal->AdPrimWhlAgEstimdGroupSafeWhlAg =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe())  // NOLINT
                      ->AdPrimWhlAgEstimdGroupSafeWhlAg *
                  5.249E-5F -
              0.85F);

  can_signal->AdPrimWhlAgEstimdGroupSafeWhlAgRate =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe())  // NOLINT
                      ->AdPrimWhlAgEstimdGroupSafeWhlAgRate *
                  5.0E-5F -
              0.7F);

  can_signal->SteerWhlAgSafe =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_SG_SteerWhlSnsr())  // NOLINT
                  ->SteerWhlAgSafe *
              0.0009765625F);

  can_signal->SteerWhlAgSpdSafe =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_SG_SteerWhlSnsr())  // NOLINT
                  ->SteerWhlAgSpdSafe *
              0.0078125F);

  can_signal->SteerWhlSnsrQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr09_SG_SteerWhlSnsr())
          ->SteerWhlSnsrQf;

  can_signal->PrimWhlAgSpdFrntSafeLe =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe())  // NOLINT
                  ->PrimWhlAgSpdFrntSafeLe *
              0.0078125F);

  can_signal->PrimWhlAgSpdFrntSafeLeQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe())
          ->PrimWhlAgSpdFrntSafeLeQf;

  can_signal->PrimWhlAgSpdFrntSafeRi =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe())  // NOLINT
                  ->PrimWhlAgSpdFrntSafeRi *
              0.0078125F);

  can_signal->PrimWhlAgSpdFrntSafeRiQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe())
          ->PrimWhlAgSpdFrntSafeRiQf;

  can_signal->PrimWhlAgSpdReSafeLe =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe())  // NOLINT
                  ->PrimWhlAgSpdReSafeLe *
              0.0078125F);

  can_signal->PrimWhlAgSpdReSafeLeQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe())
          ->PrimWhlAgSpdReSafeLeQf;

  can_signal->PrimWhlAgSpdReSafeRi =
      (float)((Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe())  // NOLINT
                  ->PrimWhlAgSpdReSafeRi *
              0.0078125F);

  can_signal->PrimWhlAgSpdReSafeRiQf =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe())
          ->PrimWhlAgSpdReSafeRiQf;

  can_signal->PrimVehSpdGroupSafeMovDir =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe())
          ->PrimVehSpdGroupSafeMovDir;

  can_signal->WhlLockStsLockSts =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr36_SG_WhlLockSts())
          ->WhlLockStsLockSts;

  can_signal->PrpsnTqDirAct =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr03_SG_PrpsnTqDir())
          ->PrpsnTqDirAct;

  can_signal->IndcrTurnSts1WdSts =
      Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr14_IndcrTurnSts1WdSts();

  can_signal->AdActvnOkFromVehDyn =
      Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr13_AdActvnOkFromVehDyn();

  can_signal->AutnmsDrvModMngtExtSafeAutnmsDrvModSts1 =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr30_SG_AutnmsDrvModMngtExtSafe())
          ->AutnmsDrvModMngtExtSafeAutnmsDrvModSts1;

  // vcan10, acu to  mcu
  can_signal->AcuFbReserved =
      Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbReserved();
  can_signal->AcuFbRollingCounter =
      Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_AcuFbRollingCounter();

  // vcan4, acu to mcu, redundant communication
  can_signal->AdNomALgtReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A())
          ->AdNomALgtReqGroupSafeCntr_A;
  can_signal->AutnmsDrvStReqCounter_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr07_ACU_SG_AutnmsDrvStReq_A())
          ->AutnmsDrvStReqCounter_A;

  // collision detection result from EH
  can_signal->ReqResleaseFbControl =
      Rte_IRead_VehSigInput_10ms_Runnable_AcuFbAdsStatus_ReqResleaseFbControl();

  // check can timeout
  can_signal->PrimVehSpdGroupSafeCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe())
          ->PrimVehSpdGroupSafeCntr;

  // vehicle mid3 ssm to mcu counter0
  can_signal->PrimALgtDataRawSafeCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid3CanFr11_SG_PrimALgtDataRawSafe())
          ->PrimALgtDataRawSafeCntr;

  // vehicle mid3 vcu to mcu counter0
  can_signal->AgDataRawSafeCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr08_SG_AgDataRawSafe())
          ->AgDataRawSafeCntr;

  // vehicle mid3 vcu to mcu counter1
  can_signal->BrkTqMinReqBrkTqMinReqCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_VCU1Mid3CanFr02_SG_BrkTqMinReq())
          ->BrkTqMinReqBrkTqMinReqCntr;

  // vehicle mid5 ssm to mcu counter0
  can_signal->AdPrimWhlAgEstimdGroupSafeCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe())
          ->AdPrimWhlAgEstimdGroupSafeCntr;

  // vehicle mid5 ssm to mcu counter1
  can_signal->PrimWhlAgSpdFrntSafeCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe())
          ->PrimWhlAgSpdFrntSafeCntr;

  // vehicle mid6 ssm to mcu counter0
  can_signal->BrkDegradedRdntCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr04_SG_BrkDegradedRdnt())
          ->BrkDegradedRdntCntr;

  // vehicle mid6 ssm to mcu counter1
  can_signal->AdSecSteerStsSafeGroupCntr =
      (Rte_IRead_VehSigInput_10ms_Runnable_SSMBMid6CanFdFr01_SG_AdSecSteerStsSafeGroup())
          ->AdSecSteerStsSafeGroupCntr;

  // acu mid3 ssm to mcu counter0
  can_signal->AdNomALgtReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A())
          ->AdNomALgtReqGroupSafeCntr_A;

  // acu mid3 vim to mcu counter1
  can_signal->AdPrimALgtLimReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid3CanFr15_ACU_SG_AdPrimALgtLimReqGroupSafe_A())
          ->AdPrimALgtLimReqGroupSafeCntr_A;

  // acu mid5 vim to mcu counter0
  can_signal->AdPrimPoseCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid5CanFdFr02_ACU_SG_AdPrimPose_A())
          ->AdPrimPoseCntr_A;

  // acu mid5 vim to mcu counter1
  can_signal->AdPrimWhlAgReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMMid5CanFdFr12_ACU_SG_AdPrimWhlAgReqGroupSafe_A())
          ->AdPrimWhlAgReqGroupSafeCntr_A;

  // acu mid6 vim to mcu counter0
  can_signal->AdSecALgtLimReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMBMid6CanFdFr28_ACU_SG_AdSecALgtLimReqGroupSafe_A())
          ->AdSecALgtLimReqGroupSafeCntr_A;

  // acu mid6 vim to mcu counter1
  can_signal->AdSecWhlAgReqGroupSafeCntr_A =
      (Rte_IRead_VehSigInput_10ms_Runnable_VIMBMid6CanFdFr14_ACU_SG_AdSecWhlAgReqGroupSafe_A())
          ->AdSecWhlAgReqGroupSafeCntr_A;
}

void GetAlgorithmRequiredChassisInfo(const VehicleAndAcuCanSignal *can_signal,
                                     VSI_VehicleInfo_Struct *vehicle_info) {
  // signal mapping
  vehicle_info->VSI_LongitudinalVelocity = can_signal->PrimVehSpdGroupSafeNom;

  vehicle_info->VSI_LateralVelocity = can_signal->PrimVLatSafeNom;

  vehicle_info->VSI_LongitudinalAcceleration =
      can_signal->PrimALgtDataRawSafeNom;

  vehicle_info->VSI_LateralAcceleration = can_signal->PrimALatDataRawSafeNom;

  vehicle_info->VSI_FrontWheelSteeringAngle =
      can_signal->AdPrimWhlAgEstimdGroupSafeWhlAg;

  vehicle_info->VSI_FrontWheelAngularVelocity =
      can_signal->AdPrimWhlAgEstimdGroupSafeWhlAgRate;

  vehicle_info->VSI_SteeringWheelAngle = can_signal->SteerWhlAgSafe;

  vehicle_info->VSI_SteeringAngularVelocity = can_signal->SteerWhlAgSpdSafe;

  vehicle_info->VSI_TurnSignalStatus = can_signal->IndcrTurnSts1WdSts;

  vehicle_info->VSI_MovementDirection = can_signal->PrimVehSpdGroupSafeMovDir;

  // choose yaw rate
  vehicle_info->VSI_YawRate = GetVsiChooseYawRateFromIMU()
                                  ? can_signal->YawRate1
                                  : can_signal->PrimYawRateSafeNom;

  // vehicle autofull ready
  vehicle_info->VSI_VehicleAutofullReady =
      can_signal->AdActvnOkFromVehDyn == Cx1_OnOff1_On;

  // vehicle mode
  bool vim_module_enable =
      (can_signal->AutnmsDrvModMngtExtSafeAutnmsDrvModSts1 == 1);
  vehicle_info->VSI_VehicleMode = (uint8)GetVehicleMode(vim_module_enable);

  // fallback mode and fallback is active
  // vcan10, acu to  mcu
  FallbackMode main_fallback_mode = (FallbackMode)can_signal->AcuFbReserved;
  uint8 main_can_counter = can_signal->AcuFbRollingCounter;

  // vcan4, redundant communication
  FallbackMode redundant_fallback_mode =
      (FallbackMode)can_signal->AutnmsDrvStReqCounter_A;
  uint8 redundant_can_counter = can_signal->AdNomALgtReqGroupSafeCntr_A;

  vehicle_info->VSI_FallbackMode =
      (uint8)GetFallbackMode(main_can_counter, main_fallback_mode,
                             redundant_can_counter, redundant_fallback_mode);

  // fallback is active
  // disable_on_freeway is true, indicating that Fallback is not activated.
  bool disable_on_freeway = can_signal->ReqResleaseFbControl & 0x08;
  vehicle_info->VSI_FallbackActive = FallbackIsActive(
      (VehicleMode)vehicle_info->VSI_VehicleMode,
      (FallbackMode)vehicle_info->VSI_FallbackMode, disable_on_freeway);

  // collision detection result from eh
  uint8 collision_detection = can_signal->ReqResleaseFbControl;
  if (CollisionDetectionIsValid(main_can_counter, collision_detection)) {
    vehicle_info->VSI_ForwardCollisionWarning =
        ((collision_detection & 0x01) != 0U);
    vehicle_info->VSI_RearCollisionWarning =
        ((collision_detection & 0x02) != 0U);
  } else {
    vehicle_info->VSI_ForwardCollisionWarning = false;
    vehicle_info->VSI_RearCollisionWarning = false;
  }
}

void GetSensorRequiredChassisInfo(
    const VehicleAndAcuCanSignal *can_signal,
    VSI_VehInfoFor1V1R_Struct *sensor_chassis_info) {
  sensor_chassis_info->VSI_PrimWhlAgSpdFrntSafeLe =
      can_signal->PrimWhlAgSpdFrntSafeLe;

  sensor_chassis_info->VSI_PrimWhlAgSpdFrntSafeLeQf =
      can_signal->PrimWhlAgSpdFrntSafeLeQf;

  sensor_chassis_info->VSI_PrimWhlAgSpdFrntSafeRi =
      can_signal->PrimWhlAgSpdFrntSafeRi;

  sensor_chassis_info->VSI_PrimWhlAgSpdFrntSafeRiQf =
      (uint8)can_signal->PrimWhlAgSpdFrntSafeRiQf;

  sensor_chassis_info->VSI_PrimWhlAgSpdReSafeLe =
      can_signal->PrimWhlAgSpdReSafeLe;

  sensor_chassis_info->VSI_PrimWhlAgSpdReSafeLeQf =
      (uint8)can_signal->PrimWhlAgSpdReSafeLeQf;

  sensor_chassis_info->VSI_PrimWhlAgSpdReSafeRi =
      can_signal->PrimWhlAgSpdReSafeRi;

  sensor_chassis_info->VSI_PrimWhlAgSpdReSafeRiQf =
      (uint8)can_signal->PrimWhlAgSpdReSafeRiQf;

  sensor_chassis_info->VSI_PrimALatDataRawSafeNom =
      can_signal->PrimALatDataRawSafeNom;

  sensor_chassis_info->VSI_PrimALatDataRawSafeNomQf =
      (uint8)can_signal->PrimALatDataRawSafeNomQf;

  sensor_chassis_info->VSI_YawRate1 = can_signal->YawRate1;

  sensor_chassis_info->VSI_YawRate1Qf1 = (uint8)can_signal->YawRate1Qf1;

  sensor_chassis_info->VSI_PrimALgtDataRawSafeNom =
      can_signal->PrimALgtDataRawSafeNom;

  sensor_chassis_info->VSI_PrimALgtDataRawSafeNomQf =
      (uint8)can_signal->PrimALgtDataRawSafeNomQf;

  sensor_chassis_info->VSI_SteerWhlAgSafe = can_signal->SteerWhlAgSafe;

  sensor_chassis_info->VSI_SteerWhlAgSpdSafe = can_signal->SteerWhlAgSpdSafe;

  sensor_chassis_info->VSI_SteerWhlSnsrQf = (uint8)can_signal->SteerWhlSnsrQf;

  sensor_chassis_info->VSI_PrimVehSpdGroupSafeNom =
      can_signal->PrimVehSpdGroupSafeNom;

  sensor_chassis_info->VSI_PrimVehSpdGroupSafeNomQf =
      (uint8)can_signal->PrimVehSpdGroupSafeNomQf;

  sensor_chassis_info->VSI_WhlLockStsLockSts = can_signal->WhlLockStsLockSts;

  sensor_chassis_info->VSI_PrpsnTqDirAct = can_signal->PrpsnTqDirAct;
}
