syntax = "proto3";

package prediction.pb;

// The map object.
// Next id: 3
message MapObject {
  // The map object types.
  // Next id: 38
  enum MapObjectType {
    UNKNOWN_TYPE = 0;
    MISSING = 1;
    // Basic map object types, used in feature extraction config.
    LANE = 21;
    LANE_MARKING = 22;
    LANE_SECTION = 23;
    CROSSWALK = 9;
    EXIT_ZONE = 10;
    MEDIAN_STRIP = 16;
    JUNCTION = 17;
    ROAD = 24;
    ROAD_HARD_BOUNDARY = 18;
    CONSTRUCTION_ZONE = 19;
    BUS_STOP_ZONE = 25;
    FENCE = 20;
    SIDEWALK = 28;
    ROUNDABOUT = 29;
    MAP_CONSTRUCTION_ZONE = 30;
    CROSSWALK_CENTERLINE = 26;
    // TODO(shuaiwu): Deprecate |LANE_WITHOUT_VIRTUAL| after the land of 25Q3
    // cyclist model.
    LANE_WITHOUT_VIRTUAL = 27;
    // NOTE: All lanes except virtual lanes in junction.
    // Please note that non-junction virtual lanes are included in this type.
    LANE_WITHOUT_VIRTUAL_IN_JUNCTION = 35;
    TRAFFIC_CONE = 31;
    BARRIER = 32;
    CURB = 33;
    DIVERSION_ZONE = 34;
    LANE_TRAFFIC_SIGNAL_STOP_LINE = 36;
    LANE_WATCH_LINE = 37;
    // Lane center-line types.
    LANE_CENTER_LINE_VIRTUAL = 2;
    LANE_CENTER_LINE_REGULAR = 3;
    LANE_CENTER_LINE_BUS = 4;
    LANE_CENTER_LINE_BIKE = 5;
    LANE_CENTER_LINE_WAITING = 6;
    LANE_CENTER_LINE_CENTER_LEFT_TURN = 7;
    LANE_CENTER_LINE_EMERGENCY = 8;
    // Lane marking types.
    LANE_MARKING_VIRTUAL = 11;
    LANE_MARKING_BROKEN = 12;
    LANE_MARKING_SOLID = 13;
    LANE_MARKING_DOUBLE_BROKEN = 14;
    LANE_MARKING_DOUBLE_SOLID = 15;
  }
  MapObjectType type = 1;
  int64 id = 2;
}

enum HardBoundaryType {
  IS_VEH_BOUNDARY = 0;
  IS_PED_BOUNDARY = 1;
  IS_CYC_BOUNDARY = 2;
}
