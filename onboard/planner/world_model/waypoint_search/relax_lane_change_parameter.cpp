#include "planner/world_model/waypoint_search/relax_lane_change_parameter.h"

#include <algorithm>
#include <glog/logging.h>
#include <optional>

#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/hdmap_util.h"
#include "planner/constants.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/waypoint_search/waypoint.h"
#include "planner_protos/relax_lane_sequence_parameter.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/pnc_object.h"

namespace planner {
namespace lane_selection {

namespace {

// Gets these constraint types can be relaxed for a source.
std::set<pb::RelaxLaneChangeConstrainType> GetRelaxedConstraintTypesForSource(
    pb::RelaxLaneChangeParamSource source) {
  switch (source) {
    case (pb::RelaxLaneChangeParamSource::kStuckAvoidance): {
      std::set<pb::RelaxLaneChangeConstrainType> relaxed_types = {
          pb::RelaxLaneChangeConstrainType::kBlockage,
          pb::RelaxLaneChangeConstrainType::kImmutable};
      if (FLAGS_planning_enable_cross_road_in_relax_lane_change_constraint) {
        relaxed_types.insert(pb::RelaxLaneChangeConstrainType::kNeighborRoad);
      }
      return relaxed_types;
    }
    case (pb::RelaxLaneChangeParamSource::kCA):
      return {pb::RelaxLaneChangeConstrainType::kSolidLaneMarking};
    default:
      return {};
  }
}

bool HasHardBoundaryInLaneChangeMinDistance(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const Waypoint& waypoint, bool to_left, double lane_change_min_distance) {
  const double remaining_distance =
      lane_change_min_distance - waypoint.GetRemainingArcLength();
  // If can add an lane change edge involving 2 lanes.
  if (remaining_distance < 0) {
    const math::Range1d range(
        waypoint.GetArcLength(),
        waypoint.GetArcLength() + lane_change_min_distance);
    if (joint_pnc_map_service.IsLaneSegmentParallelToHardBoundary(
            &waypoint.parent_lane(), range, to_left)) {
      return true;
    }
  } else {
    const math::Range1d curr_range(waypoint.GetArcLength(),
                                   waypoint.parent_lane().length());
    if (joint_pnc_map_service.IsLaneSegmentParallelToHardBoundary(
            &waypoint.parent_lane(), curr_range, to_left)) {
      return true;
    }
    // If should across section lane change, also check hard boundary
    // on successor.
    for (const pnc_map::Lane* successor : waypoint.parent_lane().successors()) {
      if (!drivable_lane_reasoner.IsLaneRobotDrivable(
              successor, /*check_local_hold=*/true,
              /*check_edge_filter=*/false)) {
        continue;
      }
      if (successor->length() > remaining_distance) {
        const math::Range1d succ_range(0.0, remaining_distance);
        if (joint_pnc_map_service.IsLaneSegmentParallelToHardBoundary(
                successor, succ_range, to_left)) {
          return true;
        }
      }
    }
  }
  return false;
}

// Gets the lane follow distance of edge.
double GetEdgeLaneFollowDistance(const std::vector<Waypoint>& waypoints) {
  double length_m = 0.0;
  int prev_point_idx = -1;
  for (size_t i = 0; i < waypoints.size(); ++i) {
    const Waypoint& waypoint = waypoints.at(i);
    if (waypoint.id() == constants::kSourceVirtualVertexId ||
        waypoint.id() == constants::kTargetVirtualVertexId) {
      continue;
    }
    if (prev_point_idx >= 0) {
      const Waypoint& prev_point = waypoints.at(prev_point_idx);
      const pnc_map::Lane& prev_lane = prev_point.parent_lane();
      const pnc_map::Lane& curr_lane = waypoint.parent_lane();
      if (prev_lane.id() == curr_lane.id()) {
        length_m += prev_lane.length() * (waypoint.percentage_along_lane() -
                                          prev_point.percentage_along_lane());
      } else if (prev_lane.IsSuccessor(curr_lane)) {
        // Two lanes have a predecessor and successor relation.
        length_m +=
            (1.0 - prev_point.percentage_along_lane()) * prev_lane.length();
        length_m += waypoint.percentage_along_lane() * curr_lane.length();
      } else {
        // Different lanes and not lane follow, early return.
        break;
      }
    }
    prev_point_idx = i;
  }
  return length_m;
}
}  // namespace

void WaypointRelaxEdgeParameters::Clear() {
  waypoint_relax_lane_change_map_.clear();
  allow_immediate_stop_source_waypoint_ids_.clear();
  violable_lane_marking_ranges_ = std::nullopt;
  cross_road_lane_change_map_.clear();
  relaxed_lane_marking_info_map_.clear();
  relax_scenario_types_.clear();
  relax_lane_direction_lane_follow_info_.Clear();
  allow_to_lane_change_in_junction_no_from_ego_ = false;
}

void WaypointRelaxEdgeParameters::AddWaypointRelaxLaneChangeInfo(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const Waypoint& waypoint, hdmap::RelaxDirection direction_in,
    pb::RelaxLaneChangeParamSource source,
    const std::optional<double>& required_distance) {
  if (direction_in == hdmap::RelaxDirection::kInvalidDirection) {
    // If the direction is invalid, do nothing.
    return;
  }
  auto waypoint_itr = waypoint_relax_lane_change_map_.emplace(
      waypoint.id(),
      std::map<hdmap::RelaxDirection, RelaxLaneChangeParameter>());
  std::map<hdmap::RelaxDirection, RelaxLaneChangeParameter>&
      waypoint_relax_info = waypoint_itr.first->second;
  std::vector<hdmap::RelaxDirection> directions;
  directions.reserve(2);
  if (direction_in == hdmap::RelaxDirection::kToBoth) {
    directions.push_back(hdmap::RelaxDirection::kToLeft);
    directions.push_back(hdmap::RelaxDirection::kToRight);
  } else {
    directions.push_back(direction_in);
  }
  for (const auto& direction : directions) {
    const bool to_left = (direction == hdmap::RelaxDirection::kToLeft);
    // Check if there is hard boundary in lane change min distance when
    // no lane change neighbor lane in current section.
    // Only check it when enable cross road lane change.
    if (FLAGS_planning_enable_cross_road_in_relax_lane_change_constraint &&
        pnc_map::GetLaneChangeNeighborLane(waypoint.parent_lane(), to_left) ==
            nullptr) {
      const double lane_change_min_distance =
          waypoint.is_ego_pose() ? kMinimumDistanceForLaneChangeAtPoseInMeter
                                 : kMinimumDistanceForLaneChangeInMeter;
      if (HasHardBoundaryInLaneChangeMinDistance(
              joint_pnc_map_service, drivable_lane_reasoner, waypoint, to_left,
              lane_change_min_distance)) {
        continue;
      }
    }
    auto direction_relax_itr =
        waypoint_relax_info.emplace(direction, RelaxLaneChangeParameter());
    auto& direction_relax_info = direction_relax_itr.first->second;
    direction_relax_info.sources.insert(source);
    const auto constraint_types = GetRelaxedConstraintTypesForSource(source);
    direction_relax_info.relaxed_constraint_types.insert(
        constraint_types.begin(), constraint_types.end());
    if (required_distance.has_value()) {
      if (direction_relax_info.mini_required_distance.has_value()) {
        direction_relax_info.mini_required_distance = std::min(
            direction_relax_info.mini_required_distance, required_distance);
      } else {
        direction_relax_info.mini_required_distance = required_distance;
      }
    }
  }
}

void WaypointRelaxEdgeParameters::UpdateCrossRoadLaneChangeMap(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const std::vector<pb::RelaxLaneChangeParam>& relax_lane_change_params) {
  // Update |source_to_cross_road_neighbor_map_| by stuck avoidance parameters.
  for (const auto& param : relax_lane_change_params) {
    if (param.region_type() == pb::RelaxLaneChangeParam_RegionType_kAtEgoPose) {
      for (const pnc_map::Lane* current_lane : current_lanes) {
        AddCrossRoadLaneChangeLanes(joint_pnc_map_service, current_lane, param);
      }
    } else if (param.region_type() ==
               pb::RelaxLaneChangeParam_RegionType_kLaneSegment) {
      const std::vector<const pnc_map::Lane*> lanes =
          joint_pnc_map_service.GetLaneSequence(
              {param.relax_lane_segment().lane_id()});
      if (!lanes.empty()) {
        AddCrossRoadLaneChangeLanes(joint_pnc_map_service, lanes.front(),
                                    param);
      }
    }
  }
}

bool WaypointRelaxEdgeParameters::IsLaneChangeForStuckAvoidance(
    const WaypointEdge& edge, bool check_soft_blockage) const {
  if (relax_scenario_types_.empty()) {
    return false;
  }
  if (!edge.IsLaneChangeEdge()) {
    // We don't relax edge feasibility check for above cases.
    return false;
  }

  const auto& waypoints = edge.waypoints();
  DCHECK_GT(waypoints.size(), 1);

  const bool change_to_left = edge.IsLaneChangeEdge(/*to_left=*/true);
  if (IsLaneChangeEdgeRelaxedBySource(
          waypoints.front(), change_to_left,
          pb::RelaxLaneChangeParamSource::kStuckAvoidance)) {
    const double dist_to_go_straight = GetEdgeLaneFollowDistance(waypoints);
    const double min_straight_dist_for_lane_change =
        waypoints.front().is_ego_pose()
            ? kMinimumDistanceForLaneChangeAtPoseInMeter -
                  kMaximumLengthForCloseLaneMarkingInMeter
            : kMinimumDistanceForLaneChangeInMeter -
                  kMaximumLengthForCloseLaneMarkingInMeter;
    if (dist_to_go_straight < min_straight_dist_for_lane_change) {
      return true;
    }
  }

  const pnc_map::Lane& from_lane = waypoints.front().parent_lane();

  bool is_on_source_lane = true;
  // Check lane marking and blockages.
  for (size_t idx = 0; idx < waypoints.size(); ++idx) {
    // Check the last point only when it's the first point on target lane.
    if (idx == waypoints.size() - 1 && !is_on_source_lane) {
      break;
    }
    const auto& point = waypoints[idx];
    is_on_source_lane = point.lane_id() == from_lane.id() ||
                        from_lane.IsSuccessor(point.parent_lane());
    // The direction of crossed lane marking relative to waypoint.
    const bool search_direction =
        is_on_source_lane ? change_to_left : !change_to_left;
    const pnc_map::LaneMarking* lane_marking =
        search_direction ? DCHECK_NOTNULL(point.parent_lane().left_marking())
                         : DCHECK_NOTNULL(point.parent_lane().right_marking());
    const hdmap::LaneMarking::Attribute::Type attribute_type =
        lane_marking->GetAttributeType(point.GetArcLength());
    // In any of following situations, we consider a lane change edge is for
    // stuck avoidance.
    // 1) waypoint on source lane is blocked.
    // 2) waypoint except last one is within solid lane marking which is set as
    // violable.
    if ((is_on_source_lane &&
         !point.IsViableKeyPoint(search_direction, /*check_lane_marking=*/true,
                                 /*check_lane_blockage=*/true,
                                 check_soft_blockage)) ||
        (IsViolableLaneMarkingBySource(
             lane_marking->id(), point.GetArcLength(), change_to_left,
             hdmap::ViolateLaneMarkingDataSource::kFromRelaxLaneSequence) &&
         !hdmap::util::IsLaneChangeableAttributeType(attribute_type,
                                                     change_to_left))) {
      return true;
    }
  }
  return false;
}

const RelaxLaneChangeParameter*
WaypointRelaxEdgeParameters::GetEdgeRelaxLaneChangeParameter(
    const Waypoint& waypoint, bool to_left) const {
  const auto& waypoint_relax_info_itr =
      waypoint_relax_lane_change_map_.find(waypoint.id());
  if (waypoint_relax_info_itr == waypoint_relax_lane_change_map_.end()) {
    return nullptr;
  }
  const auto direction = to_left ? hdmap::RelaxDirection::kToLeft
                                 : hdmap::RelaxDirection::kToRight;
  const auto& direction_relax_info_itr =
      waypoint_relax_info_itr->second.find(direction);
  if (direction_relax_info_itr == waypoint_relax_info_itr->second.end()) {
    return nullptr;
  }
  return &direction_relax_info_itr->second;
}

const RelaxLaneChangeParameter*
WaypointRelaxEdgeParameters::GetEdgeRelaxLaneChangeParameter(
    const WaypointEdge& edge) const {
  if (!edge.IsLaneChangeEdge()) {
    return nullptr;
  }
  return GetEdgeRelaxLaneChangeParameter(
      edge.waypoints().front(),
      /*to_left=*/edge.IsLaneChangeEdge(/*to_left=*/true));
}

bool WaypointRelaxEdgeParameters::IsLaneChangeEdgeRelaxedBySource(
    const Waypoint& waypoint, bool to_left,
    pb::RelaxLaneChangeParamSource source) const {
  const auto& waypoint_relax_info_itr =
      waypoint_relax_lane_change_map_.find(waypoint.id());
  if (waypoint_relax_info_itr == waypoint_relax_lane_change_map_.end()) {
    return false;
  }
  const auto direction = to_left ? hdmap::RelaxDirection::kToLeft
                                 : hdmap::RelaxDirection::kToRight;
  const auto& direction_relax_info_itr =
      waypoint_relax_info_itr->second.find(direction);
  if (direction_relax_info_itr == waypoint_relax_info_itr->second.end()) {
    return false;
  }
  const auto& relaxed_types =
      direction_relax_info_itr->second.relaxed_constraint_types;
  const std::set<pb::RelaxLaneChangeConstrainType> needed_relax_types =
      GetRelaxedConstraintTypesForSource(source);
  return !needed_relax_types.empty() &&
         std::all_of(needed_relax_types.begin(), needed_relax_types.end(),
                     [relaxed_types](pb::RelaxLaneChangeConstrainType type) {
                       return relaxed_types.find(type) != relaxed_types.end();
                     });
}

bool WaypointRelaxEdgeParameters::IsLaneChangeEdgeRelaxedBySource(
    const WaypointEdge& edge, pb::RelaxLaneChangeParamSource source) const {
  return edge.IsLaneChangeEdge() &&
         IsLaneChangeEdgeRelaxedBySource(
             edge.waypoints().front(),
             /*to_left=*/edge.IsLaneChangeEdge(/*to_left=*/true), source);
}

bool WaypointRelaxEdgeParameters::IsLaneChangeEdgeRelaxedByConstraintType(
    const Waypoint& waypoint, bool to_left,
    pb::RelaxLaneChangeConstrainType type) const {
  const auto& waypoint_relax_info_itr =
      waypoint_relax_lane_change_map_.find(waypoint.id());
  if (waypoint_relax_info_itr == waypoint_relax_lane_change_map_.end()) {
    return false;
  }
  const auto direction = to_left ? hdmap::RelaxDirection::kToLeft
                                 : hdmap::RelaxDirection::kToRight;
  const auto& direction_relax_info_itr =
      waypoint_relax_info_itr->second.find(direction);
  if (direction_relax_info_itr == waypoint_relax_info_itr->second.end()) {
    return false;
  }
  const auto& relax_parameter = direction_relax_info_itr->second;
  return relax_parameter.relaxed_constraint_types.find(type) !=
         relax_parameter.relaxed_constraint_types.end();
}

bool WaypointRelaxEdgeParameters::IsLaneChangeEdgeRelaxedByConstraintType(
    const WaypointEdge& edge, pb::RelaxLaneChangeConstrainType type) const {
  return edge.IsLaneChangeEdge() &&
         IsLaneChangeEdgeRelaxedByConstraintType(
             edge.waypoints().front(),
             /*to_left=*/edge.IsLaneChangeEdge(/*to_left=*/true), type);
}

void WaypointRelaxEdgeParameters::AddCrossRoadLaneChangeLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane* curr_lane,
    const pb::RelaxLaneChangeParam& relax_lane_change_param) {
  if (curr_lane == nullptr) {
    return;
  }
  const bool need_left =
      (relax_lane_change_param.direction() == hdmap::RelaxDirection::kToLeft ||
       relax_lane_change_param.direction() == hdmap::RelaxDirection::kToBoth);
  const bool need_right =
      (relax_lane_change_param.direction() == hdmap::RelaxDirection::kToRight ||
       relax_lane_change_param.direction() == hdmap::RelaxDirection::kToBoth);
  if (!need_left && !need_right) {
    return;
  }
  if (IsSourceLaneForCrossRoadLaneChange(curr_lane->id())) {
    return;
  }
  pnc_map::CrossRoadLaneChangeLanes cross_road_lane_change_lanes;
  cross_road_lane_change_lanes.source_lane = curr_lane;
  if (need_left && !curr_lane->left_lane()) {
    // Obtain neighbor lane from neighbor road, with same successor.
    const std::optional<const hdmap::Lane*> neighbor_lane_raw =
        hdmap::util::GetLaneChangeLaneFromNeighborRoad(
            *joint_pnc_map_service.pnc_map_service()->hdmap(),
            curr_lane->routing_vertex().neighbor_roads_with_same_successors(),
            curr_lane->id(), /*to_left=*/true);
    if (neighbor_lane_raw.has_value()) {
      const std::vector<const pnc_map::Lane*> neighbor_lanes =
          joint_pnc_map_service.GetLaneSequence(
              {neighbor_lane_raw.value()->id()});
      if (!neighbor_lanes.empty()) {
        cross_road_lane_change_lanes.neighbor_road_left_lane =
            neighbor_lanes.front();
      }
    }
  }
  if (need_right && !curr_lane->right_lane()) {
    const std::optional<const hdmap::Lane*> neighbor_lane_raw =
        hdmap::util::GetLaneChangeLaneFromNeighborRoad(
            *joint_pnc_map_service.pnc_map_service()->hdmap(),
            curr_lane->routing_vertex().neighbor_roads_with_same_successors(),
            curr_lane->id(), /*to_left=*/false);
    if (neighbor_lane_raw.has_value()) {
      const std::vector<const pnc_map::Lane*> neighbor_lanes =
          joint_pnc_map_service.GetLaneSequence(
              {neighbor_lane_raw.value()->id()});
      if (!neighbor_lanes.empty()) {
        cross_road_lane_change_lanes.neighbor_road_right_lane =
            neighbor_lanes.front();
      }
    }
  }
  if (cross_road_lane_change_lanes.neighbor_road_left_lane != nullptr ||
      cross_road_lane_change_lanes.neighbor_road_right_lane != nullptr) {
    cross_road_lane_change_map_.emplace(curr_lane->id(),
                                        cross_road_lane_change_lanes);
  }
}

bool WaypointRelaxEdgeParameters::IsSourceLaneForCrossRoadLaneChange(
    int64_t lane_id) const {
  return IsSourceLaneForCrossRoadLaneChange(lane_id, /*to_left=*/true) ||
         IsSourceLaneForCrossRoadLaneChange(lane_id, /*to_left=*/false);
}

bool WaypointRelaxEdgeParameters::IsSourceLaneForCrossRoadLaneChange(
    int64_t lane_id, bool to_left) const {
  const auto iter = cross_road_lane_change_map_.find(lane_id);
  if (iter == cross_road_lane_change_map_.end()) {
    return false;
  }
  if (to_left) {
    return iter->second.neighbor_road_left_lane != nullptr;
  }
  return iter->second.neighbor_road_right_lane != nullptr;
}

const pnc_map::Lane*
WaypointRelaxEdgeParameters::GetLaneChangeLaneFromNeighborRoad(
    int64_t lane_id, bool to_left) const {
  if (!IsSourceLaneForCrossRoadLaneChange(lane_id, to_left)) {
    return nullptr;
  }
  // If has this key, return corresponding value.
  const pnc_map::CrossRoadLaneChangeLanes& cross_road_lane_change_lanes =
      cross_road_lane_change_map_.at(lane_id);
  return to_left ? cross_road_lane_change_lanes.neighbor_road_left_lane
                 : cross_road_lane_change_lanes.neighbor_road_right_lane;
}

void WaypointRelaxEdgeParameters::AddAllowImmediateStopSourceWaypoints(
    const std::vector<const Waypoint*>& waypoints) {
  if (waypoints.empty()) {
    return;
  }
  for (const Waypoint* waypoint : waypoints) {
    if (waypoint) {
      allow_immediate_stop_source_waypoint_ids_.insert(waypoint->id());
    }
  }
}

bool WaypointRelaxEdgeParameters::CanLaneFollowEdgeStopAtNextWaypoint(
    const Waypoint& waypoint) const {
  return allow_immediate_stop_source_waypoint_ids_.find(waypoint.id()) !=
         allow_immediate_stop_source_waypoint_ids_.end();
}

bool WaypointRelaxEdgeParameters::IsViolableLaneMarkingBySource(
    hdmap::LaneMarkingId lane_marking_id, double arc_length,
    bool is_left_lane_change,
    hdmap::ViolateLaneMarkingDataSource source) const {
  if (relaxed_lane_marking_info_map_.empty()) {
    return false;
  }
  const auto iter = relaxed_lane_marking_info_map_.find(lane_marking_id);
  if (iter == relaxed_lane_marking_info_map_.end()) {
    // The lane marking is not relaxed.
    return false;
  }
  const hdmap::util::RelaxedLaneMarkingInfo& relax_info = iter->second;
  return relax_info.IsRelaxedAtArcLength(
      /*is_left_lane_change=*/is_left_lane_change, arc_length, source);
}

}  // namespace lane_selection
}  // namespace planner
