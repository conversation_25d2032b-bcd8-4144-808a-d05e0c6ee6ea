load(
    "//bazel:defs.bzl",
    "install",
    "voy_add_trace_provider",
    "voy_cc_rostest",
    "voy_cc_test",
    "voy_gen_candbc_proto",
)

package(default_visibility = ["//visibility:public"])

voy_add_trace_provider(
    name = "voy_trace_provider_gnss",
    manifest_file = ":trace_gnss_manifest.json",
)

voy_gen_candbc_proto(
    name = "candbc_protos",
    dbc_files = [
        "data/ins570d_20241025.dbc",
        "data/tnu_20250113.dbc",
    ],
)

cc_library(
    name = "utils",
    srcs = ["src/utils/mount_point_utils.cpp"],
    hdrs = [
        "src/utils/event_publisher.h",
        "src/utils/mount_point_utils.h",
        "src/utils/time_conversion.h",
    ],
    strip_include_prefix = "src/",
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/hdmap:lib",
    ],
)

cc_library(
    name = "core",
    srcs = [
        "src/core/abnormal_checker.cpp",
        "src/core/can_receiver.cpp",
        "src/core/gnss_gflags.cpp",
        "src/core/gnss_receiver_base.cpp",
        "src/core/ins570d_receiver.cpp",
        "src/core/ionosphere_client.cpp",
        "src/core/novatel_parser.cpp",
        "src/core/novatel_receiver.cpp",
        "src/core/tnu_receiver.cpp",
    ],
    hdrs = [
        "src/core/abnormal_checker.h",
        "src/core/can_receiver.h",
        "src/core/gnss_gflags.h",
        "src/core/gnss_receiver_base.h",
        "src/core/ins570d_receiver.h",
        "src/core/ionosphere_client.h",
        "src/core/novatel_messages.h",
        "src/core/novatel_parser.h",
        "src/core/novatel_receiver.h",
        "src/core/tnu_receiver.h",
    ],
    copts = ["-Wno-write-strings"],
    strip_include_prefix = "src/",
    deps = [
        ":utils",
        ":voy_trace_provider_gnss",
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/can",
        "//onboard/common/http:voy_http_client",
        "//onboard/common/node",
        "//onboard/sensors/onboard_monitor:onboard_monitor_add_data",
        "//onboard/sensors/sensor_publisher",
        "//onboard/third_party/coordinate_converter",
        "//onboard/third_party/json",
    ],
)

cc_binary(
    name = "gnss_driver_node",
    srcs = [
        "src/gnss_driver_node.cpp",
        "src/gnss_driver_node.h",
        "src/main.cpp",
    ],
    data = [":candbc_protos"],
    deps = [
        ":core",
        ":utils",
        "//onboard/common/node",
        "@voy-sdk//:ros",
    ],
)

install(
    name = "install_targets",
    srcs = [
        ":gnss_driver_node",
        ":tools/download_firmware.sh",
        ":tools/mock_pose.py",
    ],
    dest = "lib/gnss_driver",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

install(
    name = "install_config",
    srcs = ["onboard/sensors/gnss/config/"],
    dest = "etc/gnss_driver",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

install(
    name = "install_candbc",
    srcs = [":candbc_protos"],
    dest = "share/gnss_driver/candbc",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

install(
    name = "install_rospkg",
    srcs = [":package.xml"],
    dest = "share/gnss_driver",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": ["@platforms//:incompatible"],
        "//conditions:default": [],
    }),
)

voy_cc_test(
    name = "novatel_parser_test",
    srcs = ["tests/novatel_parser_test.cpp"],
    copts = ["-Ionboard/sensors/gnss/src/core"],
    deps = [
        ":core",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_rostest(
    name = "ins570d_receiver_test",
    srcs = ["tests/ins570d_receiver_test.cpp"],
    copts = ["-Ionboard/sensors/gnss/src/core"],
    data = [":candbc_protos"],
    launch_file = "tests/ins570d_receiver_test.test",
    deps = [
        ":core",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "ionosphere_client_test",
    srcs = ["tests/ionosphere_client_test.cpp"],
    copts = ["-Ionboard/sensors/gnss/src/core"],
    deps = [
        ":core",
        "@voy-sdk//:gmock",
        "@voy-sdk//:gtest",
    ],
)
