#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_TEST_TEST_UTILITY_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_TEST_TEST_UTILITY_H_

#include <algorithm>
#include <limits>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "av_comm/params.h"
#include "planner/behavior/util/agent_state/agent_blocking_state_generator.h"
#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/agent_state/agent_pass_info_generation.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state_generator.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_helper.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/intention_generator_util.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state_generator.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/object_reasoning_info.h"
#include "planner/planning_gflags.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner/world_model/traffic_participant/tracked_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/construction_zone_inlane_state.pb.h"
#include "pnc_map_service/map_elements/lane_marking.h"
#include "pnc_map_service/test/test_util.h"
#include "vehicle_model/motion_model.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

namespace test {

constexpr int kNumObjectForTest = 4;

// Read LINESTRING and return a math::geometry::PolylineCurve2d line_curve.
math::geometry::PolylineCurve2d ReadLine(const std::string& input) {
  boost::geometry::model::linestring<math::geometry::Point2d> raw_path;
  boost::geometry::read_wkt(input, raw_path);
  return math::geometry::PolylineCurve2d(raw_path);
}

// Get ego in-lane params.
EgoInLaneParams GetEgoInLaneParams() {
  EgoInLaneParams ego_param;
  ego_param.plan_start_timestamp_ms = 0;
  ego_param.speed_mps = 1.0;
  ego_param.accleration_mpss = 0.0;
  ego_param.min_accel_limit = -2.0;
  ego_param.max_accel_limit = 2.0;
  ego_param.min_jerk_limit = -2.0;
  ego_param.max_jerk_limit = 2.0;
  ego_param.max_speed_limit = 33.3;

  ego_param.arclength_m = 0.0;
  ego_param.width_m = 1.0;
  ego_param.front_bumper_arclength = 1.0;
  ego_param.rear_axle_to_rear_bumper_m = 0.5;
  ego_param.rear_axle_to_front_bumper_m = 1.0;
  ego_param.min_turn_radius = 4.0;
  return ego_param;
}

// Get Robot State Snapshot.
RobotStateSnapshot GenerateRobotStateSnapshot(
    const math::geometry::Point2d& front_pose, const double speed = 0.0) {
  // Generate pose.
  // length: 4.9536
  const double rear_bumper_to_rear_axle = 1.0946;
  // rear_bumper_to_rear_axle: 1.0946
  // width: 1.864
  // geometry_center_to_rear_axle: 1.3822
  const double wheel_base = 2.85;
  const double rear_bumper_position =
      rear_bumper_to_rear_axle + wheel_base / 2.0;
  voy::Pose current_pose;
  current_pose.set_x(front_pose.x() + rear_bumper_position);
  current_pose.set_y(front_pose.y());
  current_pose.set_vel_y(speed);
  // Generate parameter.
  const vehicle_model::CarModelWithAxleRectangularShape car_model_with_shape(
      vehicle_model::CarModel(
          vehicle_model::CarModel::GetMotionModelParam(av_comm::CarType::kTest),
          /*method=*/math::IntegrationMethod::RK_2),
      vehicle_model::GetEgoAxleRectangularMeasurementByType(
          av_comm::CarType::kTest));

  // Generate pose snapshot.
  return RobotStateSnapshot(current_pose, 0.0, car_model_with_shape);
}

// Construct a LaneSequenceGeometry with reference_path and lane_corridor.
lane_selection::LaneSequenceGeometry ConstructLaneSequenceGeometry(
    const math::geometry::PolylineCurve2d& reference_path,
    const planner::DrivingCorridor& lane_corridor) {
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  lane_sequence_geometry.nominal_path =
      lane_selection::NominalPath(reference_path);
  lane_sequence_geometry.left_lane_boundary = lane_corridor.left_boundary;
  lane_sequence_geometry.right_lane_boundary = lane_corridor.right_boundary;
  lane_sequence_geometry.lane_sequence_contour =
      lane_selection::GetLaneBoundaryBorder(lane_corridor.left_boundary,
                                            lane_corridor.right_boundary,
                                            reference_path);
  return lane_sequence_geometry;
}

// Returns a pnc_map::Lane with lane_sequence_geometry.
pnc_map::Lane GenerateLane(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const std::vector<hdmap::LaneMarking::Attribute::Type>& attr_types =
        {hdmap::LaneMarking::Attribute::SOLID},
    const hdmap::Lane::LaneType& lane_type = hdmap::Lane::REGULAR,
    const hdmap::Lane::Turn& turn = hdmap::Lane::UNKNOWN_TURN) {
  const auto get_polyline_points =
      [](const math::geometry::PolylineCurve2d& line) {
        std::vector<std::pair<double, double>> points;
        points.reserve(line.points().size());
        for (const auto& point : line.points()) {
          points.push_back({point.x(), point.y()});
        }
        return points;
      };
  hdmap::LaneMarking* left_lane_marking_proto = new hdmap::LaneMarking();
  hdmap::LaneMarking* right_lane_marking_proto = new hdmap::LaneMarking();
  left_lane_marking_proto->set_id(101);
  right_lane_marking_proto->set_id(102);
  *left_lane_marking_proto->mutable_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.left_lane_boundary));
  *right_lane_marking_proto->mutable_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.right_lane_boundary));

  const size_t num_of_types = attr_types.size();
  DCHECK_GT(num_of_types, 0);

  int left_idx = 0;
  const int left_step_size =
      left_lane_marking_proto->line().points().size() / num_of_types;
  for (int i = 0; i < left_lane_marking_proto->line().points().size(); ++i) {
    if (left_idx * left_step_size <= i) {
      // Reach the end of a lane marking segment, store an attribute.
      hdmap::LaneMarking::Attribute* left_attr =
          left_lane_marking_proto->add_attrs();
      left_attr->set_type(attr_types[left_idx]);
      *(left_attr->mutable_track()->mutable_point()) =
          left_lane_marking_proto->line().points(i);
      // Determine the index for the left boundary type.
      left_idx += 1;
    }
  }

  int right_idx = 0;
  const int right_step_size =
      right_lane_marking_proto->line().points().size() / num_of_types;
  for (int i = 0; i < right_lane_marking_proto->line().points().size(); ++i) {
    if (right_idx * right_step_size <= i) {
      // Reach the end of a lane marking segment, store an attribute.
      hdmap::LaneMarking::Attribute* right_attr =
          right_lane_marking_proto->add_attrs();
      right_attr->set_type(attr_types[right_idx]);
      *(right_attr->mutable_track()->mutable_point()) =
          right_lane_marking_proto->line().points(i);
      // Determine the index for the right boundary type.
      right_idx += 1;
    }
  }
  pnc_map::LaneMarking* left_lane_marking =
      new pnc_map::LaneMarking(left_lane_marking_proto);
  pnc_map::LaneMarking* right_lane_marking =
      new pnc_map::LaneMarking(right_lane_marking_proto);

  hdmap::Lane* lane_proto = new hdmap::Lane();
  lane_proto->set_type(lane_type);
  lane_proto->set_turn(turn);
  lane_proto->set_id(1);
  lane_proto->set_left_lane_marking_id(101);
  lane_proto->set_right_lane_marking_id(102);
  *lane_proto->mutable_assistant_line() = pnc_map::CreatePolylineProto(
      get_polyline_points(lane_sequence_geometry.nominal_path));

  return pnc_map::Lane(lane_proto, left_lane_marking, right_lane_marking);
}

// Generate Lane Marking Traffic Rule with specified lane sequence and lane
// marking type. Left and right lane marking id are set arbitrarily.
lane_selection::LaneMarkingTrafficRule GenerateLaneMarkingTrafficRule(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const std::vector<hdmap::LaneMarking::Attribute::Type>& attr_types = {
        hdmap::LaneMarking::Attribute::SOLID}) {
  return lane_selection::LaneMarkingTrafficRule(
      GenerateLane(lane_sequence_geometry, attr_types));
}

// Construct a ConstructionZone.
void ConstructConstructionZoneInLaneState(
    const EgoInLaneParams& ego_param,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    int64_t cz_id, const math::geometry::Polygon2d& cz_polygon,
    std::vector<ConstructionZoneInLaneState>* construction_zones_state) {
  // Construct a tracked_object.
  voy::ConstructionZone cz_proto;
  cz_proto.set_id(cz_id);
  math::geometry::Convert(cz_polygon, *cz_proto.mutable_zone());
  const ConstructionZone construction_zone(
      /*timestamp=*/1, cz_proto,
      /*perception_range_lanes=*/std::vector<const pnc_map::Lane*>{});
  ConstructionZoneInLaneStateGenerator generator;
  const std::optional<ConstructionZoneInLaneState>
      construction_zone_inlane_state_optional = generator.Generate(
          construction_zone, lane_sequence_geometry,
          GenerateLaneMarkingTrafficRule(lane_sequence_geometry),
          /*ego_width=*/ego_param.width_m);
  if (construction_zone_inlane_state_optional) {
    construction_zones_state->emplace_back(
        construction_zone_inlane_state_optional.value());
  }
}

// Construct a static tracked object.
void ConstructStaticAgentInLaneState(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    int64_t agent_id, const math::geometry::Polygon2d& agent_polygon,
    AgentInLaneStatesMap& agent_state_map) {
  // Construct a tracked_object.
  voy::TrackedObject tracked_object;
  tracked_object.set_id(agent_id);
  tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(agent_polygon, *tracked_object.mutable_contour());

  agent_state_map[agent_id] = std::make_unique<AgentInLaneStates>();
  std::unique_ptr<AgentInLaneStates>& agent_in_lane_state =
      agent_state_map[agent_id];
  agent_in_lane_state->object_id = agent_id;
  agent_in_lane_state->agent_metadata
      .features[pb::AgentFeature::IS_STATIONARY] = true;
  agent_in_lane_state->agent_metadata.agent_type =
      voy::perception::ObjectType::VEHICLE;
  agent_in_lane_state->tracked_state.inlane_param =
      lane_selection::ComputeAgentSnapshotInLaneParam(
          lane_sequence_geometry,
          /*pose=*/TrafficParticipantPose(/*timestamp=*/1, tracked_object));
  agent_in_lane_state->tracked_state.blocking_info =
      lane_selection::AgentBlockingStateGenerator::Generate(
          /*ego_inlane_param=*/GetEgoInLaneParams(),
          agent_in_lane_state->tracked_state.inlane_param,
          /*agent_metadata=*/AgentInLaneMetadata{},
          /*blocking_computation_params=*/BlockingComputationParams{},
          pb::MotionMode::FORWARD);
  agent_in_lane_state->tracked_state.pass_info =
      lane_selection::ComputeAgentSnapshotPassInfo(
          agent_in_lane_state->tracked_state.inlane_param,
          GenerateLaneMarkingTrafficRule(lane_sequence_geometry),
          /*agent_metadata=*/AgentInLaneMetadata{},
          /*is_cyc_no_person=*/false);

  // Add predicted_trajectories for constructed static agent
  const int traj_id = 1;
  const int64_t timestamp = 0;
  const int64_t duration = math::Sec2Ms(5.0);
  ::prediction::pb::PredictedTrajectory proto_traj;
  proto_traj.set_is_output_trajectory(true);
  proto_traj.set_probability_in_ppm(1000000);
  // Static agent only has two states in predicted_trajectory
  for (int64_t delta_t = 0; delta_t <= duration; delta_t += duration) {
    pb::TrajectoryPose* proto_traj_pose = proto_traj.add_traj_poses();
    proto_traj_pose->set_timestamp(timestamp + delta_t);
    const double heading = tracked_object.heading();
    proto_traj_pose->set_x_pos(tracked_object.center_x());
    proto_traj_pose->set_y_pos(tracked_object.center_y());
    proto_traj_pose->set_z_pos(tracked_object.center_z());
    proto_traj_pose->set_heading(heading);
    proto_traj_pose->set_speed(tracked_object.velocity());
    // Set default value.
    proto_traj_pose->set_accel(0.0);
  }
  const PredictedTrajectory trajectory(tracked_object, proto_traj, traj_id,
                                       /*max_interested_prediction_timestamp=*/
                                       std::numeric_limits<int64_t>::max(),
                                       /*is_stationary=*/false);

  const EgoInLaneParams ego_inlane_param = GetEgoInLaneParams();
  std::vector<StampedAgentSnapshotInLaneState> agent_inlane_params;
  const lane_selection::LaneMarkingTrafficRule lane_marking_traffic_rule =
      GenerateLaneMarkingTrafficRule(lane_sequence_geometry);
  for (const auto& pose : trajectory.GetPoses()) {
    StampedAgentSnapshotInLaneState predicted_state;
    predicted_state.timestamp = pose.timestamp();
    predicted_state.inlane_state.inlane_param =
        lane_selection::ComputeAgentSnapshotInLaneParam(lane_sequence_geometry,
                                                        pose);
    predicted_state.inlane_state.blocking_info =
        lane_selection::AgentBlockingStateGenerator::Generate(
            ego_inlane_param, predicted_state.inlane_state.inlane_param,
            /*agent_metadata=*/AgentInLaneMetadata{},
            /*blocking_computation_params=*/BlockingComputationParams{},
            pb::MotionMode::FORWARD);
    predicted_state.inlane_state.pass_info =
        lane_selection::ComputeAgentSnapshotPassInfo(
            predicted_state.inlane_state.inlane_param,
            lane_marking_traffic_rule,
            /*agent_metadata=*/AgentInLaneMetadata{},
            /*is_cyc_no_person=*/false);
    agent_inlane_params.push_back(std::move(predicted_state));
  }

  AgentTrajectoryInLaneStates agent_traj;
  agent_traj.trajectory_id = traj_id;
  agent_traj.predicted_states = std::move(agent_inlane_params);
  agent_in_lane_state->primary_trajectory_id = traj_id;
  agent_in_lane_state->predicted_trajectories =
      std::unordered_map<int64_t, AgentTrajectoryInLaneStates>{
          {traj_id, std::move(agent_traj)}};
}

// Construct a tracked dynamic object.
void ConstructDynamicAgentInLaneState(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    int64_t object_id, double x0, double y0, double width, double velocity,
    AgentInLaneStatesMap& agent_state_map) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(object_id);
  tracked_object.set_center_x(x0);
  tracked_object.set_center_y(y0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_width(width);
  tracked_object.set_length(2.0);
  tracked_object.set_height(2.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_omega(0.0);
  tracked_object.set_velocity(velocity);

  // Construct a predicted trajectory.
  const int traj_id = 1;
  const int64_t timestamp = 0;
  const int64_t duration = math::Sec2Ms(5.0);
  ::prediction::pb::PredictedTrajectory proto_traj;
  proto_traj.set_is_output_trajectory(true);
  proto_traj.set_probability_in_ppm(1000000);
  for (int64_t delta_t = 0; delta_t <= duration;
       delta_t += constants::kTrajectoryPoseResolutionInMSec) {
    pb::TrajectoryPose* proto_traj_pose = proto_traj.add_traj_poses();
    proto_traj_pose->set_timestamp(timestamp + delta_t);
    const double dist = tracked_object.velocity() * math::Ms2Sec(delta_t);
    const double heading = tracked_object.heading();
    const double dist_x = dist * cos(heading);
    const double dist_y = dist * sin(heading);
    proto_traj_pose->set_x_pos(tracked_object.center_x() + dist_x);
    proto_traj_pose->set_y_pos(tracked_object.center_y() + dist_y);
    proto_traj_pose->set_z_pos(tracked_object.center_z());
    proto_traj_pose->set_heading(heading);
    proto_traj_pose->set_speed(tracked_object.velocity());
    // Set default value.
    proto_traj_pose->set_accel(0.0);
  }
  const PredictedTrajectory trajectory(tracked_object, proto_traj, traj_id,
                                       /*max_interested_prediction_timestamp=*/
                                       std::numeric_limits<int64_t>::max(),
                                       /*is_stationary=*/false);
  const lane_selection::LaneMarkingTrafficRule lane_marking_traffic_rule =
      GenerateLaneMarkingTrafficRule(lane_sequence_geometry);
  const EgoInLaneParams ego_inlane_param = GetEgoInLaneParams();
  std::vector<StampedAgentSnapshotInLaneState> agent_inlane_params;
  for (const auto& pose : trajectory.GetPoses()) {
    StampedAgentSnapshotInLaneState predicted_state;
    predicted_state.timestamp = pose.timestamp();
    predicted_state.inlane_state.inlane_param =
        lane_selection::ComputeAgentSnapshotInLaneParam(lane_sequence_geometry,
                                                        pose);
    predicted_state.inlane_state.blocking_info =
        lane_selection::AgentBlockingStateGenerator::Generate(
            ego_inlane_param, predicted_state.inlane_state.inlane_param,
            /*agent_metadata=*/AgentInLaneMetadata{},
            /*blocking_computation_params=*/BlockingComputationParams{},
            pb::MotionMode::FORWARD);
    predicted_state.inlane_state.pass_info =
        lane_selection::ComputeAgentSnapshotPassInfo(
            predicted_state.inlane_state.inlane_param,
            lane_marking_traffic_rule,
            /*agent_metadata=*/AgentInLaneMetadata{},
            /*is_cyc_no_person=*/false);
    agent_inlane_params.push_back(std::move(predicted_state));
  }
  AgentTrajectoryInLaneStates agent_traj;
  agent_traj.trajectory_id = traj_id;
  agent_traj.predicted_states = std::move(agent_inlane_params);

  agent_state_map[object_id] = std::make_unique<AgentInLaneStates>();
  std::unique_ptr<AgentInLaneStates>& agent_in_lane_state =
      agent_state_map[object_id];
  agent_in_lane_state->object_id = object_id;
  agent_in_lane_state->agent_metadata
      .features[pb::AgentFeature::IS_STATIONARY] = false;
  agent_in_lane_state->agent_metadata.agent_type =
      voy::perception::ObjectType::VEHICLE;
  agent_in_lane_state->tracked_state.inlane_param =
      lane_selection::ComputeAgentSnapshotInLaneParam(
          lane_sequence_geometry,
          /*pose=*/TrafficParticipantPose(/*timestamp=*/1, tracked_object));
  agent_in_lane_state->tracked_state.blocking_info =
      lane_selection::AgentBlockingStateGenerator::Generate(
          ego_inlane_param, agent_in_lane_state->tracked_state.inlane_param,
          /*agent_metadata=*/AgentInLaneMetadata{},
          /*blocking_computation_params=*/BlockingComputationParams{},
          pb::MotionMode::FORWARD);
  agent_in_lane_state->tracked_state.pass_info =
      lane_selection::ComputeAgentSnapshotPassInfo(
          agent_in_lane_state->tracked_state.inlane_param,
          lane_marking_traffic_rule,
          /*agent_metadata=*/AgentInLaneMetadata{},
          /*is_cyc_no_person=*/false);
  agent_in_lane_state->primary_trajectory_id = traj_id;
  agent_in_lane_state->predicted_trajectories =
      std::unordered_map<int64_t, AgentTrajectoryInLaneStates>{
          {traj_id, std::move(agent_traj)}};
}

// Calculates the ObjectReasoningInfo of each object for unit test.
std::map<TypedObjectId, path::ObjectReasoningInfo>
GenerateObjectReasoningInfoMap(
    const AgentInLaneStatesMap& agent_state_map,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states) {
  std::map<TypedObjectId, path::ObjectReasoningInfo> object_reasoning_info_map;
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  for (const auto& [agent_id, agent_state] : agent_state_map) {
    LateralBlockingStateMetaData blocking_state_data;
    if (!agent_state->agent_metadata
             .features[pb::AgentFeature::IS_STATIONARY]) {
      DCHECK(!agent_state->predicted_trajectories.empty());
      DCHECK(!agent_state->predicted_trajectories
                  .at(agent_state->primary_trajectory_id)
                  .predicted_states.empty());
      pb::BlockingSequence sequence;
      sequence.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
      sequence.mutable_range()->set_start_index(0);
      sequence.mutable_range()->set_end_index(
          agent_state->predicted_trajectories
              .at(agent_state->primary_trajectory_id)
              .predicted_states.size() -
          1);
      blocking_state_data.blocking_sequences =
          std::vector<pb::BlockingSequence>{sequence};
    }
    blocking_state_data.reasoning_info.set_is_stationary(
        agent_state->agent_metadata.features[pb::AgentFeature::IS_STATIONARY]);
    blocking_state_data.reasoning_info.set_is_in_current_lane(true);
    blocking_state_data.reasoning_info.set_tracked_side_to_ego(math::pb::kOn);
    std::unordered_map<int, LateralBlockingStateMetaData>
        trajectory_blocking_state_data_map{
            {blocking_state_data.trajectory_id, blocking_state_data}};
    path::ObjectReasoningInfo object_info(
        *agent_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        /*yield_intention_data=*/std::nullopt,
        AgentSLBoundary(agent_state->tracked_state.inlane_param),
        std::move(blocking_state_data),
        std::move(trajectory_blocking_state_data_map));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  for (const auto& cz_state : construction_zone_states) {
    LateralBlockingStateMetaData blocking_state_data;
    blocking_state_data.reasoning_info.set_is_stationary(true);
    blocking_state_data.reasoning_info.set_is_in_current_lane(true);
    blocking_state_data.reasoning_info.set_tracked_side_to_ego(math::pb::kOn);
    path::ObjectReasoningInfo object_info(
        cz_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        AgentSLBoundary(cz_state), std::move(blocking_state_data));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  return object_reasoning_info_map;
}

std::map<TypedObjectId, path::ObjectReasoningInfo>
GenerateObjectReasoningInfoMapFromObjectOccupancyState(
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const std::vector<ConstructionZoneInLaneState>& construction_zone_states) {
  std::map<TypedObjectId, path::ObjectReasoningInfo> object_reasoning_info_map;
  RequiredLateralGap required_lat_gap =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  for (const auto& [agent_id, agent_state] : object_occupancy_state_map) {
    LateralBlockingStateMetaData blocking_state_data;
    if (!agent_state->is_primary_stationary()) {
      DCHECK(!agent_state->predicted_trajectory_occupancy_states().empty());
      DCHECK(!agent_state->predicted_trajectory_occupancy_states()
                  .at(agent_state->primary_trajectory_id())
                  .predicted_states()
                  .empty());
      pb::BlockingSequence sequence;
      sequence.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
      sequence.mutable_range()->set_start_index(0);
      sequence.mutable_range()->set_end_index(
          agent_state->predicted_trajectory_occupancy_states()
              .at(agent_state->primary_trajectory_id())
              .predicted_states()
              .size() -
          1);
      blocking_state_data.blocking_sequences =
          std::vector<pb::BlockingSequence>{sequence};
    }
    blocking_state_data.reasoning_info.set_is_stationary(
        agent_state->is_primary_stationary());
    blocking_state_data.reasoning_info.set_is_in_current_lane(true);
    blocking_state_data.reasoning_info.set_tracked_side_to_ego(math::pb::kOn);
    std::unordered_map<int, LateralBlockingStateMetaData>
        trajectory_blocking_state_data_map{
            {blocking_state_data.trajectory_id, blocking_state_data}};
    path::ObjectReasoningInfo object_info(
        *agent_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        /*yield_intention_data=*/std::nullopt,
        AgentSLBoundary(
            agent_state->current_snapshot_info().object_occupancy_param()),
        std::move(blocking_state_data),
        std::move(trajectory_blocking_state_data_map));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  for (const auto& cz_state : construction_zone_states) {
    LateralBlockingStateMetaData blocking_state_data;
    blocking_state_data.reasoning_info.set_is_stationary(true);
    blocking_state_data.reasoning_info.set_is_in_current_lane(true);
    blocking_state_data.reasoning_info.set_tracked_side_to_ego(math::pb::kOn);
    path::ObjectReasoningInfo object_info(
        cz_state, required_lat_gap,
        /*required_lat_gap_pass_state=*/required_lat_gap,
        AgentSLBoundary(cz_state), std::move(blocking_state_data));
    object_reasoning_info_map.emplace(object_info.object_id(),
                                      std::move(object_info));
  }
  return object_reasoning_info_map;
}

// Calculates the ObjectIntentionInfo of each object for unit test.
std::map<TypedObjectId, ObjectIntentionInfo> GenerateObjectIntentionInfoMap(
    const std::map<TypedObjectId, path::ObjectReasoningInfo>&
        object_reasoning_info_map) {
  std::map<TypedObjectId, ObjectIntentionInfo> object_intention_info_map;
  for (const auto& [object_id, object_reasoning_info] :
       object_reasoning_info_map) {
    TrajectoryIntentionMetaData trajectory_intention_data;
    trajectory_intention_data.is_static = object_reasoning_info.is_stationary();
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    if (!trajectory_intention_data.is_static) {
      trajectory_intention_data.blocking_sequences =
          object_reasoning_info.blocking_sequences();
    }
    trajectory_intention_data.fod_intentions.insert(
        pb::IntentionResult::DRIVE_THROUGH);
    trajectory_intention_data.fod_intentions.insert(pb::IntentionResult::NUDGE);
    std::vector<RepulsionMetaData> repulsion_meta_datas;
    std::unordered_map<pb::AgentReasonerId, std::string>
        repulsion_reasoning_debugs;
    ObjectIntentionInfo object_intention_info(
        object_reasoning_info, std::move(trajectory_intention_data),
        /*expected_intention=*/ExpectedIntentionMetaData{},
        std::move(repulsion_meta_datas), std::move(repulsion_reasoning_debugs));
    object_intention_info_map.emplace(object_id,
                                      std::move(object_intention_info));
  }
  return object_intention_info_map;
}

voy::TrackedObject ConstructTrackedObjectFromPolygon(
    int object_id, const math::geometry::Polygon2d& object_polygon) {
  voy::TrackedObject tracked_object;
  tracked_object.set_id(object_id);
  tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(object_polygon, *tracked_object.mutable_contour());
  return tracked_object;
}

voy::TrackedObject ConstructTrackedObjectFromDimension(
    int object_id, double x0, double y0, double width, double length,
    double height, double velocity, double heading) {
  voy::TrackedObject tracked_object;
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_id(object_id);
  tracked_object.set_center_x(x0);
  tracked_object.set_center_y(y0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_width(width);
  tracked_object.set_length(length);
  tracked_object.set_height(height);
  tracked_object.set_heading(heading);
  tracked_object.set_omega(0.0);
  tracked_object.set_velocity(velocity);
  return tracked_object;
}

PredictedTrajectoryWrapper ConstructPredictedTrajectoryWrapperForTest(
    const voy::TrackedObject& tracked_object,
    prediction::pb::PredictedTrajectory& proto_traj) {
  // Construct a predicted trajectory.
  const int64_t timestamp = 0;
  const int64_t duration = math::Sec2Ms(5.0);
  proto_traj.set_id(tracked_object.id());
  proto_traj.set_is_output_trajectory(true);
  proto_traj.set_probability_in_ppm(1000000);
  for (int64_t delta_t = 0; delta_t <= duration;
       delta_t += constants::kTrajectoryPoseResolutionInMSec) {
    pb::TrajectoryPose* proto_traj_pose = proto_traj.add_traj_poses();
    proto_traj_pose->set_timestamp(timestamp + delta_t);
    const double dist = tracked_object.velocity() * math::Ms2Sec(delta_t);
    const double heading = tracked_object.heading();
    const double dist_x = dist * cos(heading);
    const double dist_y = dist * sin(heading);
    proto_traj_pose->set_x_pos(tracked_object.center_x() + dist_x);
    proto_traj_pose->set_y_pos(tracked_object.center_y() + dist_y);
    proto_traj_pose->set_z_pos(tracked_object.center_z());
    proto_traj_pose->set_heading(heading);
    proto_traj_pose->set_speed(tracked_object.velocity());
    // Set default value.
    proto_traj_pose->set_accel(0.0);
  }
  return PredictedTrajectoryWrapper(tracked_object, proto_traj,
                                    /*is_primary_trajectory=*/true);
}

// Construct an object occupancy state for test. This
// function will create planner_object, predicted_trajectory_proto, and
// object_occupancy_state. Besides, it will store all the above values to the
// corresponding vectors. The caller side should provide these vectors to store
// these values. Important: Please reserve the vectors' size before calling this
// function to prevent undefined behavior caused by resizing.
void ConstructObjectOccupancyStateForTest(
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const EgoInLaneParams& ego_params, const voy::TrackedObject& tracked_object,
    std::vector<PlannerObject>& planner_object_list,
    std::vector<prediction::pb::PredictedTrajectory>&
        predicted_trajectory_protos,
    std::vector<std::vector<PredictedTrajectoryWrapper>>&
        predicted_trajectory_wrappers,
    ObjectOccupancyStateMap& object_occupancy_state_map) {
  bool is_stationary = tracked_object.velocity() == 0.0;
  prediction::pb::PredictedTrajectory proto_traj;
  proto_traj.set_id(1);
  predicted_trajectory_protos.push_back(proto_traj);
  PredictedTrajectoryWrapper predicted_trajectory_wrapper =
      is_stationary ? PredictedTrajectoryWrapper(tracked_object, proto_traj,
                                                 /*is_primary_trajectory=*/true)
                    : ConstructPredictedTrajectoryWrapperForTest(
                          tracked_object, predicted_trajectory_protos.back());
  predicted_trajectory_wrappers.push_back({predicted_trajectory_wrapper});
  prediction::pb::Agent agent_proto;
  agent_proto.mutable_tracked_object()->CopyFrom(tracked_object);

  PlannerObject planner_object(
      agent_proto, /*timestamp=*/0,
      TrafficParticipantPose(/*timestamp=*/0, agent_proto.tracked_object()));
  planner_object.set_is_primary_stationary(is_stationary);
  planner_object_list.push_back(planner_object);
  ObjectInformation object_information;
  ObjectOccupancyState object_occupancy_state = GenerateObjectOccupancyState(
      planner_object_list.back(), object_information,
      predicted_trajectory_wrappers.back(), lane_sequence_geometry.nominal_path,
      lane_sequence_geometry.left_lane_boundary,
      lane_sequence_geometry.right_lane_boundary, ego_params,
      pb::MotionMode::FORWARD);
  object_occupancy_state_map.emplace(
      planner_object.id(),
      std::make_unique<ObjectOccupancyState>(object_occupancy_state));
}

bool IsEqualVelocity(std::optional<math::geometry::Point2d> velocity_1,
                     std::optional<math::geometry::Point2d> velocity_2) {
  if (!velocity_1.has_value() && !velocity_2.has_value()) {
    return true;
  } else if (velocity_1.has_value() && !velocity_2.has_value()) {
    return false;
  } else if (velocity_2.has_value() && !velocity_1.has_value()) {
    return false;
  } else {
    return math::IsApprox(velocity_1->x(), velocity_2->x()) &&
           math::IsApprox(velocity_1->y(), velocity_2->y());
  }
}

bool IsAgentInLaneParamEquivalentToObjectOccupancyParam(
    const AgentSnapshotInLaneParam& agent_inlane_param,
    const ObjectOccupancyParam& object_occupancy_state_param) {
  if (std::fabs(object_occupancy_state_param.snapshot_start_arclength_m -
                agent_inlane_param.start_arclength_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different snapshot_start_arclength_m: agent_inlane_param = "
               << agent_inlane_param.start_arclength_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.snapshot_start_arclength_m;
    return false;
  }

  if (std::fabs(object_occupancy_state_param.snapshot_end_arclength_m -
                agent_inlane_param.end_arclength_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different snapshot_end_arclength_m: agent_inlane_param = "
               << agent_inlane_param.end_arclength_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.snapshot_end_arclength_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.full_body_start_arclength_m -
                agent_inlane_param.full_body_start_arclength_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different full_body_start_arclength_m: agent_inlane_param = "
               << agent_inlane_param.full_body_start_arclength_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.full_body_start_arclength_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.full_body_end_arclength_m -
                agent_inlane_param.full_body_end_arclength_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different full_body_end_arclength_m: agent_inlane_param = "
               << agent_inlane_param.full_body_end_arclength_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.full_body_end_arclength_m;
    return false;
  }
  if (std::fabs(
          object_occupancy_state_param.full_body_start_lateral_distance_m -
          agent_inlane_param.full_body_start_lateral_distance_m) >
      math::constants::kEpsilon) {
    LOG(ERROR)
        << "Different full_body_start_lateral_distance_m: agent_inlane_param "
           "= "
        << agent_inlane_param.full_body_start_lateral_distance_m
        << " , object_occupancy_state = "
        << object_occupancy_state_param.full_body_start_lateral_distance_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.full_body_end_lateral_distance_m -
                agent_inlane_param.full_body_end_lateral_distance_m) >
      math::constants::kEpsilon) {
    LOG(ERROR)
        << "Different full_body_end_lateral_distance_m: agent_inlane_param = "
        << agent_inlane_param.full_body_end_lateral_distance_m
        << " , object_occupancy_state = "
        << object_occupancy_state_param.full_body_end_lateral_distance_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.left_boundary_clearance_m -
                agent_inlane_param.left_inlane_clearance_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different left_boundary_clearance_m: agent_inlane_param = "
               << agent_inlane_param.left_inlane_clearance_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.left_boundary_clearance_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.right_boundary_clearance_m -
                agent_inlane_param.right_inlane_clearance_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different right_boundary_clearance_m: agent_inlane_param = "
               << agent_inlane_param.right_inlane_clearance_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.right_boundary_clearance_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.center_line_clearance_m -
                agent_inlane_param.center_line_clearance_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different center_line_clearance_m: agent_inlane_param = "
               << agent_inlane_param.center_line_clearance_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.center_line_clearance_m;
    return false;
  }
  if (object_occupancy_state_param.center_line_side !=
      agent_inlane_param.center_line_side) {
    LOG(ERROR) << "Different center_line_side: agent_inlane_param = "
               << agent_inlane_param.center_line_side
               << " , object_occupancy_state = "
               << object_occupancy_state_param.center_line_side;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.cross_track_encroachment_m -
                agent_inlane_param.cross_track_encroachment_m) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different cross_track_encroachment_m: agent_inlane_param = "
               << agent_inlane_param.cross_track_encroachment_m
               << " , object_occupancy_state = "
               << object_occupancy_state_param.cross_track_encroachment_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.relative_heading_rad -
                agent_inlane_param.relative_heading_rad) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different relative_heading_rad: agent_inlane_param = "
               << agent_inlane_param.relative_heading_rad
               << " , object_occupancy_state = "
               << object_occupancy_state_param.relative_heading_rad;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.along_track_speed_mps -
                agent_inlane_param.along_track_speed_mps) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different along_track_speed_mps: agent_inlane_param = "
               << agent_inlane_param.along_track_speed_mps
               << " , object_occupancy_state = "
               << object_occupancy_state_param.along_track_speed_mps;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.cross_track_speed_mps -
                agent_inlane_param.cross_track_speed_mps) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different cross_track_speed_mps: agent_inlane_param = "
               << agent_inlane_param.cross_track_speed_mps
               << " , object_occupancy_state = "
               << object_occupancy_state_param.cross_track_speed_mps;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.max_left_violation -
                agent_inlane_param.max_left_violation) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different max_left_violation: agent_inlane_param = "
               << agent_inlane_param.max_left_violation
               << " , object_occupancy_state = "
               << object_occupancy_state_param.max_left_violation;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.max_right_violation -
                agent_inlane_param.max_right_violation) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different max_right_violation: agent_inlane_param = "
               << agent_inlane_param.max_right_violation
               << " , object_occupancy_state = "
               << object_occupancy_state_param.max_right_violation;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.min_corridor_width_m -
                agent_inlane_param.min_lane_width) >
      math::constants::kEpsilon) {
    LOG(ERROR) << "Different min_corridor_width_m: agent_inlane_param = "
               << agent_inlane_param.min_lane_width
               << " , object_occupancy_state = "
               << object_occupancy_state_param.min_corridor_width_m;
    return false;
  }
  if (std::fabs(object_occupancy_state_param.speed_mps -
                agent_inlane_param.speed_mps) > math::constants::kEpsilon) {
    LOG(ERROR) << "Different sped_mps: agent_inlane_param = "
               << agent_inlane_param.speed_mps << " , object_occupancy_state = "
               << object_occupancy_state_param.speed_mps;
    return false;
  }
  if (!IsEqualVelocity(object_occupancy_state_param.velocity,
                       agent_inlane_param.velocity)) {
    LOG(ERROR) << "Different velocity composition";
    return false;
  }
  if (object_occupancy_state_param.is_fully_within_corridor !=
      agent_inlane_param.is_fully_in_lane) {
    LOG(ERROR) << "Different is_fully_within_corridor: agent_inlane_param = "
               << agent_inlane_param.is_fully_in_lane
               << " , object_occupancy_state = "
               << object_occupancy_state_param.is_fully_within_corridor;
    return false;
  }
  if (std::fabs(
          object_occupancy_state_param.dist_to_center_line_range.start_pos -
          agent_inlane_param.dist_to_center_line_range.start_pos) >
          math::constants::kEpsilon ||
      std::fabs(object_occupancy_state_param.dist_to_center_line_range.end_pos -
                agent_inlane_param.dist_to_center_line_range.end_pos) >
          math::constants::kEpsilon) {
    LOG(ERROR)
        << "Different dist_to_center_line_range: agent_inlane_param = {"
        << agent_inlane_param.dist_to_center_line_range.start_pos << ", "
        << agent_inlane_param.dist_to_center_line_range.end_pos << "}"
        << " , object_occupancy_state = {"
        << object_occupancy_state_param.dist_to_center_line_range.start_pos
        << "," << object_occupancy_state_param.dist_to_center_line_range.end_pos
        << "}";
    return false;
  }
  return true;
}

bool IsAgentInLaneStateMapEquivalentToObjectOccupancyStateMap(
    const AgentInLaneStatesMap& agent_state_map,
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const std::vector<int64_t>& object_ids) {
  if (agent_state_map.size() != object_occupancy_state_map.size()) {
    LOG(ERROR) << "Maps have different sizes: agent_state_map_size() = "
               << agent_state_map.size()
               << " , object_occupancy_state_map_size() = "
               << object_occupancy_state_map.size();
    return false;
  }
  for (int64_t object_id : object_ids) {
    const auto agent_state_it = agent_state_map.find(object_id);
    const auto object_occupancy_state_it =
        object_occupancy_state_map.find(object_id);
    if (agent_state_it == agent_state_map.end() ||
        object_occupancy_state_it == object_occupancy_state_map.end()) {
      LOG(ERROR) << "Invalid object id: " << object_id;
      return false;
    }

    const auto& agent_inlane_state_param =
        agent_state_it->second->tracked_state.inlane_param;
    const auto& object_occupancy_state_param =
        object_occupancy_state_it->second->current_snapshot_info()
            .object_occupancy_param();
    if (!IsAgentInLaneParamEquivalentToObjectOccupancyParam(
            agent_inlane_state_param, object_occupancy_state_param)) {
      LOG(ERROR) << "AgentInLaneParam and ObjectOccupancyParam are different "
                    "for object id: "
                 << object_id;
      return false;
    }
  }
  return true;
}

}  // namespace test

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_TEST_TEST_UTILITY_H_
