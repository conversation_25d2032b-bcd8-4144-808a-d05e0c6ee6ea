load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "situation_evaluation_decision_test",
    srcs = ["situation_evaluation_decision_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-float-conversion",
    ],
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_long_function",
        "//offboard/fallback_ads/common_math_library:can_util",
        "//offboard/fallback_ads/long_function/situation_evaluation:situation_evaluation_decision",
        "@voy-sdk//:gtest",
    ],
)
