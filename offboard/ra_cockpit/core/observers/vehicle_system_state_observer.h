#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_SYSTEM_STATE_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_SYSTEM_STATE_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class VehicleSystemStateObserver : public IDataObserver {
 public:
  using VehicleSystemStateCallback = std::function<void(
      std::shared_ptr<teleassist::pb::VehicleSystemState>, DataChannel)>;

  explicit VehicleSystemStateObserver(VehicleSystemStateCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

  void CheckIfTeleassistAbnormal(const bool is_teleassist_abnormal);

 private:
  VehicleSystemStateCallback vehicle_system_state_callback_;
  bool is_teleassist_abnormal_{false};
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_SYSTEM_STATE_OBSERVER_H_
