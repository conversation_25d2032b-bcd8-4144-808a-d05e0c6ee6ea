#ifndef ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_H_
#define ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_H_

#include <string>
#include <unordered_map>

#include "node/fault_reporter.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/speed_world_model.h"
#include "planner_protos/ops_warning.pb.h"
#include "planner_protos/ops_warning_config.pb.h"

namespace planner {
namespace ops_warning {

class OpsWarningGenerator {
 public:
  OpsWarningGenerator(const pb::OpsWarningConfig& ops_warning_config,
                      bool should_enable_engage_warning)
      : ops_warning_config_(ops_warning_config),
        should_enable_engage_warning_(should_enable_engage_warning) {}

  // GenerateWarning generates warnings for operators. The warnings include:
  // 1. Hard brake warning;
  // 2. Rear end warning.
  // 3. Juke suppression warning.
  // 4. Engagement warning.
  // 5. Disengagement zone stopping warning.
  pb::OpsWarning GenerateWarning(
      const SpeedWorldModel& world_model,
      const BehaviorDecision& behavior_decision,
      const TrajectoryGenerationResult& selected_trajectory_result);

  // Publishes the planning fatal, that the system supervisor can takeover.
  void PublishPlanningFatal(const RobotState& robot_state);

  // Clears all the engagement fault.
  void ClearAllEngagementFault();

 private:
  // Checks if vehicle is ready to engage.
  void ValidateEngagementCondition(
      const math::geometry::OrientedBox2d& ego_bounding_box,
      const std::string& route_name, const BehaviorDecision& behavior_decision,
      const TrajectoryGenerationResult& selected_trajectory_result,
      double ego_current_speed, double steering_gear_ratio);

  // Returns whether the ego car has a collision with the planner objects.
  bool HasPotentialCollisionWithPlannerObjects(
      const RobotState& robot_state,
      const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
      double threshold) const;

  // Clears all faults in auto mode.
  void ClearAllFaults();

  // Clears the planning fatals.
  void ClearPlanningFatal();

  node::FaultReporter fault_reporter_ =
      node::FaultReporter(av_comm::component::kPlanning);

  pb::OpsWarningConfig ops_warning_config_;

  voy::TripComment_Category trip_category_ = voy::TripComment_Category_kOthers;
  // Whether the engage maneuver should be enabled.
  const bool should_enable_engage_warning_ = true;
};

}  // namespace ops_warning

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPS_WARNING_OPS_WARNING_H_
