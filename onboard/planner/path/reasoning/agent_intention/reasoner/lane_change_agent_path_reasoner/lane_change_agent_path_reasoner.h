#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_H_

#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/speed/agent_reaction/agent_reaction_tracker/agent_intention_tracker.h"
#include "planner_protos/agent_intention_config.pb.h"

namespace planner {
namespace path {

// LaneChangeAgentReasoner reasons about the agent in the target lane during
// lane change.
class LaneChangeAgentPathReasoner : public AbstractReasoner {
 public:
  explicit LaneChangeAgentPathReasoner(const EgoContextInfo& ego_context_info);
  virtual ~LaneChangeAgentPathReasoner() = default;

  // Copied from lead and merge agent path reasoner, but removing logic
  // unrelated to lane change.
  bool PreReason(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  // Copied from lead and merge agent path reasoner, but removing logic
  // unrelated to lane change.
  bool PreReason(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentInLaneStates& agent_inlane_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)agent_inlane_state;
    (void)is_stationary;
    (void)behavior_type;
    return IsAgentHandledByLaneChangeNudge(blocking_state_meta_data);
  }

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const ObjectOccupancyState& object_occupancy_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final {
    (void)object_occupancy_state;
    (void)is_stationary;
    (void)behavior_type;
    return IsAgentHandledByLaneChangeNudge(blocking_state_meta_data);
  }

  bool ShouldInvokePreRepulsionReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
      std::string& debug_str) const final;

  bool ShouldInvokePostReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary) const final {
    (void)agent_type;
    (void)is_stationary;
    (void)behavior_type;
    return IsAgentHandledByLaneChangeNudge(blocking_state_meta_data);
  }

  // New logic.
  std::vector<RepulsionMetaData> PreRepulsionReason(
      const RobotStateSnapshot& robot_state_snapshot,
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::unordered_map<int, LateralBlockingStateMetaData>&
      /*trajectory_blocking_state_data_map*/,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      double repulsion_required_lat_gap,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      std::string& debug_str) const;

  std::vector<RepulsionMetaData> PreRepulsionReason(
      const RobotStateSnapshot& robot_state_snapshot,
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      double repulsion_required_lat_gap,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      std::string& debug_str) const;

  // Copied from lead and merge agent path reasoner.
  bool PostReason(const AgentInLaneStates& agent_inlane_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  // Copied from lead and merge agent path reasoner.
  bool PostReason(const ObjectOccupancyState& object_occupancy_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

 protected:
  // Copied from lead and merge agent path reasoner.
  bool PreReasonForMixedBlockingSequences(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  // Copied from lead and merge agent path reasoner.
  bool PreReasonForMixedBlockingSequences(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  // Copied from lead and merge agent path reasoner.
  bool PreReasonForHardBlockingSequences(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  // Copied from lead and merge agent path reasoner.
  bool PreReasonForHardBlockingSequences(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  // TODO(Judychen): Add UT.
  std::vector<RepulsionMetaData> AddRepulsionForLaneChange(
      const int64_t object_id, const int64_t trajectory_id,
      const bool is_lane_change_ignore_agent,
      const double agent_relative_lateral_speed, const double object_heading,
      const math::geometry::Polygon2d& polygon,
      const math::geometry::Point2d& center_2d,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const double repulsion_required_lat_gap, const double ego_width,
      const speed::Profile& profile_upper_bound,
      const speed::Profile& yield_upper_bound,

      const math::geometry::Polyline2d& agent_center_path_points,
      std::string& debug_str) const;

  // TODO(Judychen): Add UT.
  bool GenerateLastPathCurve(
      const int64_t object_id,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      math::geometry::PolylineCurve2d& last_path_curve,
      std::string& debug_str) const;

  // TODO(Judychen): Add UT.
  std::optional<double> GetAgentLastYieldProbability(
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const int64_t object_id, std::string& debug_str) const;

 private:
  void FetchInfoFromStampedAgentSnapshotInLaneState(
      const StampedAgentSnapshotInLaneState& state, int64_t& timestamp,
      double& v, double& a, std::optional<double>& odom_m,
      math::geometry::Point2d& center) const;

  void FetchInfoFromObjectStampedSnapshotInfo(
      const ObjectStampedSnapshotInfo& state, int64_t& timestamp, double& v,
      double& a, std::optional<double>& odom_m,
      math::geometry::Point2d& center) const;

  bool IsAgentHandledByLaneChangeNudge(
      const LateralBlockingStateMetaData& blocking_state_meta_data) const;

  bool IsAgentHandledByLaneChangeRepulsion(
      const voy::perception::ObjectType agent_type,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const pb::BehaviorType behavior_type, const bool is_statinoary) const;

  bool AdjustNudgeRequiredLatGapForLaneChangePathReasoner(
      const int64_t object_id,
      const pb::TrajectoryReasoningInfo& reasoning_info,
      const pb::SnapshotIntention::PassState intention_algorithm_pass_side,
      double agent_speed_mps, RequiredLateralGap& required_lat_gap,
      std::string* debug_str) const;

  pb::ObstacleSTBufferConfig conservative_buffer_config_;
};

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_LANE_CHANGE_AGENT_PATH_REASONER_H_