package(default_visibility = ["//visibility:public"])

cc_library(
    name = "can_timeout_detection",
    srcs = [
        "can_timeout_detection.c",
    ],
    hdrs = [
        "can_timeout_detection.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
        "-Wno-unused-parameter",
        "-Wno-sign-compare",
    ],
    include_prefix = "vehicle_signal_input/can_timeout_detection/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
        "//offboard/fallback_ads/common_math_library:assert_cfg",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_parameter",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_type",
    ],
)
