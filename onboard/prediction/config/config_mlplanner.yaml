features:
  agent:
    - type: <PERSON>ND_POINT_COORDINATE
      dimension: 2
    - type: AGENT_SIZE
      dimension: 3
    - type: AGENT_HEADING
      dimension: 1
    - type: AGENT_SPEED
      dimension: 1
    - type: TURN_RATE
      dimension: 1
    - type: AGENT_BOX_HEADING
      dimension: 1
    - type: AGENT_ACCEL
      dimension: 1
    - type: AGENT_TYPE
      dimension: 1
    - type: AGENT_END_RELATIVE_TIMESTAMP
      dimension: 1
    - type: VEHICLE_SIGNAL_LIGHTS_STATUS
      dimension: 4
    - type: AGENT_ATTRIBUTES
      dimension: 1
  map:
    - type: START_POINT_COORDINATE
      dimension: 2
    - type: END_POINT_COORDINATE
      dimension: 2
    - type: MAP_OBJECT_TYPE
      dimension: 1
    - type: MAP_OBJECT_TRAFFIC_LIGHT_COLOR
      dimension: 1
    - type: MAP_OBJECT_TRAFFIC_LIGHT_COUNTDOWN_NUMBER
      dimension: 1
    - type: MAP_OBJECT_TRAFFIC_LIGHT_IS_FLASHING
      dimension: 1
    - type: MAP_OBJECT_TRAFFIC_LIGHT_COLOR_HISTORY
      dimension: 3
    - type: LANE_LEFT_MARKING_TYPE
      dimension: 1
    - type: LANE_RIGHT_MARKING_TYPE
      dimension: 1
  route:
    - type: START_POINT_COORDINATE
      dimension: 2
    - type: END_POINT_COORDINATE
      dimension: 2
    - type: SPEED_LIMIT
      dimension: 1
    - type: MAP_OBJECT_TYPE
      dimension: 1
extractor_config:
  # Agent feature config.
  max_num_agents: 33
  num_vectors_in_agent_feature: 10
  # Map object feature config.
  max_num_map_objects_segment: 192
  num_vectors_in_map_object_feature: 20
  map_object_types:
    - LANE
    - CROSSWALK
    - EXIT_ZONE
    - MEDIAN_STRIP
    - ROAD_HARD_BOUNDARY
    - CONSTRUCTION_ZONE
    - FENCE
    - DIVERSION_ZONE
    # TODO(yorkchen): uncomment this line with model trained on 25Q3 dataset.
    # - LANE_TRAFFIC_SIGNAL_STOP_LINE
    # - LANE_WATCH_LINE
  # Route feature config.
  num_vectors_in_route_feature: 80
  max_num_routes: 40
  # Priority is given to ensuring that the top-k most important objects
  # will be the background objects, then select background objects according
  # to their distance to ego car.
  max_num_importance_rank_agent: 16
  use_dense_map_sampling: true
  extract_accurate_lane_marking_type: true
  extract_middle_hard_boundary: true
  mask_light_status_for_target_agent: true
  extract_accurate_traffic_light_feature: true
  # -------------------------------- Onboard Post Processing Config --------------------------------
  # Set occluded traffic light to red to avoid running light signals.
  set_occluded_traffic_light_color: true
  # Ignore drivable unknown to avoid over reaction.
  excluded_background_agent_tags:
    - IS_DRIVABLE_UNKNOWN
model_config:
  model_type: ML_PLANNER
  # The shape of model output.
  num_routes: 41
  num_modals: 1
  ego_path_horizon: 80
  prediction_horizon: 90
  # Enum defined in prediction_protos/prediction_config.proto.
  # The number and order of it must be consistent with model outputs.
  output_tensor_types:
    - TRAJS
    - TRAJ_PROBS
    - PATHS
    - PATH_PROBS
    - TRAJ_SPEEDS
    - PATH_SPEEDS
    - MODALS_INDEX
    - BOX_HEADINGS
    - ACCELS
    - STEERINGS
    - JERKS
    - STEERING_RATES
    - LATERAL_JERKS
    - TRAJ_LN_STDS
    - MAP_ATTN_SCORES
    - AGENT_ATTN_SCORES
