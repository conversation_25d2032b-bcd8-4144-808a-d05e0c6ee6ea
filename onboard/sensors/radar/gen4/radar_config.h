#ifndef ONBOARD_SENSORS_RADAR_GEN4_RADAR_CONFIG_H_
#define ONBOARD_SENSORS_RADAR_GEN4_RADAR_CONFIG_H_

#include <glog/logging.h>
#include <map>
#include <string>
#include <utility>
#include "av_comm/topics.h"

namespace radar {

enum class RadarConfigItem {
  IP_ADDRESS,
  MULTICAST_ADDRESS,
  SCAN_TOPIC,
  PACKET_TOPIC,
  HEALTH_COMPONENT,
};

enum class PacketType {
  RECV = 0,
  SEND,
};

struct RadarConfig {
  std::string ip_address;
  std::string multicast_address;
  std::string scan_topic;
  std::string packet_topic;
  std::string health_component;
};

template <RadarConfigItem item>
static const std::string& GetRadarConfig(const std::string& facing) {
  static const std::map<std::string, RadarConfig> configs = {
      {"front",
       {"************", "***********", av_comm::topic::kRadarScan1,
        av_comm::topic::kRadarPacket1, av_comm::component::kDeviceRadar1}},
      {"right",
       {"************", "***********", av_comm::topic::kRadarScan2,
        av_comm::topic::kRadarPacket2, av_comm::component::kDeviceRadar2}},
      {"rear",
       {"************", "***********", av_comm::topic::kRadarScan3,
        av_comm::topic::kRadarPacket3, av_comm::component::kDeviceRadar3}},
      {"left",
       {"************", "***********", av_comm::topic::kRadarScan4,
        av_comm::topic::kRadarPacket4, av_comm::component::kDeviceRadar4}}};

  auto it = configs.find(facing);
  CHECK(it != configs.end()) << "Invalid facing direction: " << facing;

  const RadarConfig& config = it->second;
  if constexpr (item == RadarConfigItem::IP_ADDRESS) {
    return config.ip_address;
  } else if constexpr (item == RadarConfigItem::MULTICAST_ADDRESS) {
    return config.multicast_address;
  } else if constexpr (item == RadarConfigItem::SCAN_TOPIC) {
    return config.scan_topic;
  } else if constexpr (item == RadarConfigItem::PACKET_TOPIC) {
    return config.packet_topic;
  } else if constexpr (item == RadarConfigItem::HEALTH_COMPONENT) {
    return config.health_component;
  } else {
    static const std::string empty;
    return empty;
  }
}

}  // namespace radar

#endif  // ONBOARD_SENSORS_RADAR_GEN4_RADAR_CONFIG_H_
