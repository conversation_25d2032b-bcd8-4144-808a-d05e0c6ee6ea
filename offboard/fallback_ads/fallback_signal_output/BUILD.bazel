package(default_visibility = ["//visibility:public"])

cc_library(
    name = "fallback_signal_output",
    srcs = [
        "FallbackSigOutput.c",
        "FallbackSigOutput_data.c",
    ],
    hdrs = [
        "FallbackSigOutput.h",
        "FallbackSigOutput_private.h",
        "FallbackSigOutput_types.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    # include_prefix = "common_math_library",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_fallback_sig_output",
        "//offboard/fallback_ads/common_math_library:look1_iflf_binlcapw",
        "//offboard/fallback_ads/common_math_library:rtwtypes",
        "//offboard/fallback_ads/fallback_signal_output/checksum",
    ],
)
