#include "planner/speed/reasoning/prediction_decision_maker.h"

#include <algorithm>
#include <cmath>
#include <utility>
#include <vector>

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "math/pose_2d.h"
#include "planner/planning_gflags.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/util/matching_crosswalk.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/prediction_decision.pb.h"
#include "planner_protos/speed_constraint.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "prediction_protos/agent.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {
namespace speed {

class PredictionDecisionMakerTest : public ::testing::Test,
                                    public ReasoningTestFixture {
 public:
  PredictionDecisionMakerTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

class PredictionDecisionMakerJunctionTest : public ::testing::Test,
                                            public ReasoningTestFixture {
 public:
  PredictionDecisionMakerJunctionTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  }
};

TEST_F(PredictionDecisionMakerJunctionTest, ConsiderCutInCyclistTest) {
  // TODO(reasoning): add a screenshot
  SetEgoPose(/*lane_id=*/53073, /*portion=*/0.15);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST,
               /*id=*/1, /*lane_id=*/53073, /*portion=*/0.2,
               /*length=*/2.0, /*width=*/0.5,
               /*velocity=*/2.0);

  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/53073, /*portion=*/0.2,
                               /*arclength_shift=*/1.5,
                               /*lateral_shift=*/-1.8,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/53073, /*portion=*/0.2,
                               /*arclength_shift=*/2.0,
                               /*lateral_shift=*/-1.8,
                               /*theta_shift=*/M_PI / 12),
      LanePointToPoseWithShift(/*lane_id=*/53073, /*portion=*/0.2,
                               /*arclength_shift=*/2.0,
                               /*lateral_shift=*/1.0,
                               /*theta_shift=*/M_PI / 2)};
  std::vector<int> waypoint_indices{0, 20, kPredictedTrajectoryStep};

  AddMultiPointPredictedTrajectory(waypoints, waypoint_indices,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());

  EXPECT_NEAR(3.414,
              object_proximity_infos().at(1).projected_ra_arc_length().start(),
              1e-3);
  EXPECT_NEAR(-0.453599, GetPaddedOverlapStart(overlap_regions().front()),
              1e-3);
  EXPECT_NEAR(0.8, GetStrictOverlapStart(overlap_regions().front()), 1e-3);

  EXPECT_EQ(1, wrapped_prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerJunctionTest,
       DoNotAggregateCutInWithNoCutInTest) {
  // TODO(reasoning): add a screenshot
  SetEgoPose(/*lane_id=*/9075, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/9073, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/4.0,
               /*velocity=*/2.0);

  // Primary going straight prediction.
  AddStraightPredictedTrajectory(/*start_lane_id=*/9073, /*start_portion=*/0.2,
                                 /*end_lane_id=*/9073, /*end_portion=*/1.0,
                                 /*likelihood=*/0.5, /*traj_id=*/1, agent);

  // Low likelihood going straight prediction.
  AddStraightPredictedTrajectory(/*start_lane_id=*/9073, /*start_portion=*/0.2,
                                 /*end_lane_id=*/9073, /*end_portion=*/1.0,
                                 /*likelihood=*/0.15, /*traj_id=*/2, agent);

  // Low likelihood cut-in prediction.
  AddStraightPredictedTrajectory(/*start_lane_id=*/9073, /*start_portion=*/0.2,
                                 /*end_lane_id=*/9075, /*end_portion=*/1.0,
                                 /*likelihood=*/0.2, /*traj_id=*/3, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/9073, /*start_portion=*/0.2,
                                 /*end_lane_id=*/9075, /*end_portion=*/1.0,
                                 /*likelihood=*/0.15, /*traj_id=*/4, agent);

  Update();

  EXPECT_EQ(
      4, wrapped_prediction_decisions().front().prediction_decisions().size());
  for (const PredictionDecision& decision :
       wrapped_prediction_decisions().front().prediction_decisions()) {
    switch (decision.trajectory_id()) {
      case 1:
        EXPECT_TRUE(decision.Nominal());
        break;
      case 2:
        EXPECT_TRUE(decision.CautiousDriving());
        break;
      case 3:
        EXPECT_TRUE(decision.Nominal());
        break;
      case 4:
        EXPECT_TRUE(decision.Ignore());
        EXPECT_EQ(decision.ignore_reason(),
                  pb::IgnoreReason::TRAJECTORY_AGGREGATION);
        break;
      default:
        break;
    }
  }
}

TEST_F(PredictionDecisionMakerTest, IgnoreLowLikelihoodTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/2, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  {
    const PredictionDecision& decision =
        GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

    EXPECT_TRUE(decision.agent_policies().empty());
    EXPECT_TRUE(decision.Ignore());
    EXPECT_EQ(pb::IgnoreReason::LOW_LIKELIHOOD, decision.ignore_reason());
  }
  {
    const PredictionDecision& decision =
        GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/2);

    EXPECT_TRUE(decision.Nominal());
  }
}

TEST_F(PredictionDecisionMakerTest, ConsiderHighLikelihoodTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
}

TEST_F(PredictionDecisionMakerTest, DISABLED_ConsiderLowLikelihoodTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.2, /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.CautiousDriving());
}

TEST_F(PredictionDecisionMakerTest, ReasonerInChargeCrossAgentReasonerTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.2);
  CreateLaneFollowPath();
  int obj_id = 1;
  for (const voy::perception::ObjectType object_type :
       {voy::perception::ObjectType::VEHICLE,
        voy::perception::ObjectType::UNKNOWN,
        voy::perception::ObjectType::TRAFFIC_CONE,
        voy::perception::ObjectType::BARRIER}) {
    prediction::pb::Agent& agent =
        AddAgent(object_type,
                 /*id=*/obj_id, /*lane_id=*/97573, /*portion=*/0.7,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/10.0);
    ++obj_id;
    AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                   /*start_portion=*/0.1,
                                   /*end_lane_id=*/97573, /*end_portion=*/1.0,
                                   /*likelihood=*/1.0, /*traj_id=*/1, agent);
  }
  Update();

  EXPECT_EQ(4, wrapped_prediction_decisions().size());
  for (const PredictionDecisionMakerOutput& wrapped_decision :
       wrapped_prediction_decisions()) {
    EXPECT_EQ(1, wrapped_decision.prediction_decisions().size());
    const PredictionDecision& decision =
        wrapped_decision.prediction_decisions().front();

    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);

    const AgentPolicy& agent_policy = decision.agent_policies().front();
    EXPECT_EQ(pb::ReasonerId::CROSS_AGENT, agent_policy.reasoner_in_charge());
  }
}

TEST_F(PredictionDecisionMakerTest,
       ReasonerInChargeLeadAndMergeAgentReasonerTest) {
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2, /*speed=*/10.0);
  CreateLaneFollowPath();

  int obj_id = 1;
  for (const voy::perception::ObjectType object_type :
       {voy::perception::ObjectType::VEHICLE,
        voy::perception::ObjectType::CYCLIST}) {
    prediction::pb::Agent& agent =
        AddAgent(object_type,
                 /*id=*/obj_id, /*lane_id=*/16913, /*portion=*/0.7,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/10.0);
    ++obj_id;
    AddStraightPredictedTrajectory(/*start_lane_id=*/16913,
                                   /*start_portion=*/0.7,
                                   /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                   /*likelihood=*/1.0, /*traj_id=*/1, agent);
  }
  Update();

  EXPECT_EQ(2, wrapped_prediction_decisions().size());
  for (const PredictionDecisionMakerOutput& wrapped_decision :
       wrapped_prediction_decisions()) {
    EXPECT_EQ(1, wrapped_decision.prediction_decisions().size());
    const PredictionDecision& decision =
        wrapped_decision.prediction_decisions().front();

    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);

    const AgentPolicy& agent_policy = decision.agent_policies().front();
    EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE,
              agent_policy.reasoner_in_charge());
  }
}

TEST_F(PredictionDecisionMakerTest, ReasonerInChargeVRUReasonerTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.5);
  CreateLaneFollowPath();

  int obj_id = 1;
  for (const voy::perception::ObjectType object_type :
       {voy::perception::ObjectType::PED,
        voy::perception::ObjectType::CYCLIST}) {
    prediction::pb::Agent& agent =
        AddAgent(object_type,
                 /*id=*/obj_id, /*lane_id=*/108357, /*portion=*/0.05,
                 /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/10.0);
    ++obj_id;
    AddStraightPredictedTrajectory(/*start_lane_id=*/108357,
                                   /*start_portion=*/0.05,
                                   /*end_lane_id=*/16965, /*end_portion=*/0.05,
                                   /*likelihood=*/1.0, /*traj_id=*/1, agent);
  }
  Update();

  EXPECT_EQ(2, wrapped_prediction_decisions().size());
  for (const PredictionDecisionMakerOutput& wrapped_decision :
       wrapped_prediction_decisions()) {
    EXPECT_EQ(1, wrapped_decision.prediction_decisions().size());
    const PredictionDecision& decision =
        wrapped_decision.prediction_decisions().front();

    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);

    const AgentPolicy& agent_policy = decision.agent_policies().front();
    EXPECT_EQ(pb::ReasonerId::VRU, agent_policy.reasoner_in_charge());
  }
}

TEST_F(PredictionDecisionMakerTest, ReasonerInChargeJaywalkerReasonerTest) {
  // https://drive.google.com/file/d/1b8rlyPstpn8mU21a_VRlGvzPc2644J7G/view?usp=sharing
  SetEgoPose(/*lane_id=*/43415, /*portion=*/0.0);
  CreateLaneFollowPath();

  const math::Pose2d start_pose(/*x_in=*/-4519.4006691, /*y_in=*/-2564.197967,
                                /*yaw_in=*/0.482156);
  const math::Pose2d end_pose(/*x_in=*/-4514.170338, /*y_in=*/-2561.460648,
                              /*yaw_in=*/0.482156);
  prediction::pb::Agent& agent = AddAgent(voy::perception::ObjectType::PED,
                                          /*id=*/1, start_pose, /*length=*/0.7,
                                          /*width=*/0.7, /*velocity=*/1.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);
  Update();

  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(1, wrapped_prediction_decisions()[0].prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(decision.agent_policies().size(), 1);

  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::JAYWALKER, agent_policy.reasoner_in_charge());
}

TEST_F(PredictionDecisionMakerTest, ReasonerInChargeArbitraryAgentTest) {
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16913,
                                 /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  pb::OverlapRegion& overlap_region =
      mutable_object_overlap_regions().begin()->second.front();
  overlap_region.set_motion_type(pb::OverlapMotionType::OVERLAP_MOTION_UNKNOWN);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(decision.agent_policies().size(), 1);

  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::CROSS_AGENT, agent_policy.reasoner_in_charge());
}

// This test is to test the case in which the prediction decision made for the
// overlap is to call Tailgater Reasoner.
//
// Case: An agent of VEHICLE type is following Ego in the same lane and has an
// overlap that is fully behind Ego.
// https://drive.google.com/file/d/1HTfZc3KK6e6cCRMghnGlpllKZomYxPPF/view?usp=sharing
TEST_F(PredictionDecisionMakerTest,
       ReasonerInChargeTailgaterReasonerForVehicleFollowingInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/52649,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  pb::OverlapRegion& overlap_region =
      mutable_object_overlap_regions().begin()->second.front();
  overlap_region.set_motion_type(
      pb::OverlapMotionType::OVERLAP_MOTION_SAME_DIRECTION);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(decision.agent_policies().size(), 1);

  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::TAILGATER, agent_policy.reasoner_in_charge());
}

// This test is to test the case in which the prediction decision made for the
// overlap is IGNORE with the reason of UNREALISTIC_OVERLAP.
//
// Case: An agent of CYCLIST type is following Ego in the same lane and has an
// overlap that is fully behind Ego.
// https://drive.google.com/file/d/1uBnqtYHwJjUbtL88laATPdcp5fay3679/view?usp=sharing
TEST_F(PredictionDecisionMakerTest,
       IgnoreUnrealisticOverlapForCyclistFollowingInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/52649,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  pb::OverlapRegion& overlap_region =
      mutable_object_overlap_regions().begin()->second.front();
  overlap_region.set_motion_type(
      pb::OverlapMotionType::OVERLAP_MOTION_SAME_DIRECTION);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Ignore());
  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

// This test is to test the case in which the agents' overlaps are ignored for
// the reason of UNREALISTIC_OVERLAP.
//
// Case: An agent of UNKNOWN that is following Ego in the same lane and has an
// overlap that is fully behind Ego.
// https://drive.google.com/file/d/14sCo2aO9CpuYyYO3y5QO0ujdkgu_2dzQ/view?usp=sharing
TEST_F(PredictionDecisionMakerTest,
       IgnoreUnrealisticOverlapForUnknownFollowingInSameLaneTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::UNKNOWN,
               /*id=*/1, /*lane_id=*/52649, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/52649,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  pb::OverlapRegion& overlap_region =
      mutable_object_overlap_regions().begin()->second.front();
  overlap_region.set_motion_type(
      pb::OverlapMotionType::OVERLAP_MOTION_SAME_DIRECTION);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Ignore());
  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

// This test is to test the case in which the prediction decision made for the
// overlap is IGNORE with the reason of UNREALISTIC_OVERLAP.
//
// Case: An agent of VEHICLE type is changing to Ego's lane from a neighboring
// lane and has an overlap that is fully behind Ego (i.e. cutting in behind).
// https://drive.google.com/file/d/1s07-pFkPu23mZfl87XRYPa9CPOkJg92Y/view?usp=sharing
TEST_F(PredictionDecisionMakerTest,
       IgnoreUnrealisticOverlapForVehicleCutInBehindTest) {
  SetEgoPose(/*lane_id=*/52649, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97149, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97149,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/52649, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  pb::OverlapRegion& overlap_region =
      mutable_object_overlap_regions().begin()->second.front();
  overlap_region.set_motion_type(
      pb::OverlapMotionType::OVERLAP_MOTION_SAME_DIRECTION);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Ignore());
  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

// This test is to test the case in which the prediction decision made for the
// agent is IGNORE with the reason of UNREALISTIC_OVERLAP.
//
// Case: An agent of VEHICLE type is far behind Ego in the same lane and has an
// overlap that is fully behind.
// https://drive.google.com/file/d/1e0ezYIqebn9nmWD2uBLwkxlllR9gWqCU/view?usp=sharing
TEST_F(PredictionDecisionMakerTest,
       IgnoreUnrealisticOverlapForVehicleFarAwayBehindHasOverlapTest) {
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16915, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16915, /*start_portion=*/0.2,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_NEAR(world_context().ra_to_trailing_bumper_shift(), -1.0946, 1e-3);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_TRUE(decision.Ignore());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

TEST_F(PredictionDecisionMakerTest,
       IgnoreUnrealisticOverlapForOnComingVehicleCutBehindFromLeftTest) {
  // https://cooper.didichuxing.com/shares/pTC9cWUNOZ72
  SetEgoPoseWoYaw(/*x_pos=*/-4501.19, /*y_pos=*/-2581.09,
                  /*lane_id=*/17049, /*speed=*/3.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*pose=*/{-4506.83, -2579.84, -0.809073}, /*length=*/6.0,
               /*width=*/1.9, /*velocity=*/1.5, /*height=*/1.8);

  std::vector<math::Pose2d> waypoints{
      {-4506.83, -2579.84, -0.809073}, {-4505.12, -2580.87, -0.551551},
      {-4503.07, -2581.34, -0.259403}, {-4500.78, -2581.29, -0.0067731},
      {-4498.45, -2580.82, 0.17683},   {-4496.1, -2580.07, 0.295453},
      {-4493.79, -2579.02, 0.431509}};
  AddMultiPointPredictedTrajectory(waypoints, /*likelihood=*/0.9, /*traj_id=*/1,
                                   agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_TRUE(decision.Ignore());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

TEST_F(
    PredictionDecisionMakerTest,
    DoNotIgnoreAgentCurrentPoseStrictlyOverlappedWithEgoBBoxEndInFrontOfRearAxle) {
  // https://cooper.didichuxing.com/shares/fb63ZQeKbKl6
  SetEgoPoseWoYaw(/*x_pos=*/-4501.44, /*y_pos=*/-2579.82,
                  /*lane_id=*/17049, /*speed=*/3.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*pose=*/{-4503.86, -2578.82, 4.78125}, /*length=*/10.0,
               /*width=*/3.0, /*velocity=*/0.0, /*height=*/1.8);

  AddStraightPredictedTrajectory(/*start_pose=*/{-4503.86, -2578.82, 4.78125},
                                 /*end_pose=*/{-4503.26, -2586.54, 4.78125},
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_FALSE(decision.agent_policies().empty());
  EXPECT_FALSE(decision.Ignore());
}

TEST_F(
    PredictionDecisionMakerTest,
    DoNotIgnoreAgentCurrentPoseStrictlyOverlappedWithEgoBBoxStartInFrontOfFrontAxle) {
  // https://cooper.didichuxing.com/shares/Y0gTtTCfsCSo
  SetEgoPoseWoYaw(/*x_pos=*/-4501.44, /*y_pos=*/-2579.82,
                  /*lane_id=*/17049, /*speed=*/3.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*pose=*/{-4503.76, -2577.10, 4.78125}, /*length=*/1.2,
               /*width=*/2.0, /*velocity=*/0.0, /*height=*/1.8);

  AddStraightPredictedTrajectory(/*start_pose=*/{-4503.76, -2577.10, 4.78125},
                                 /*end_pose=*/{-4503.26, -2586.54, 4.78125},
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_FALSE(decision.agent_policies().empty());
  EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest,
       IgnoreAgentCurrentPoseStrictlyOverlappedWithEgoBBoxStartBehindRearAxle) {
  // https://cooper.didichuxing.com/shares/PeRdrBmByPEL
  SetEgoPoseWoYaw(/*x_pos=*/-4501.44, /*y_pos=*/-2579.82,
                  /*lane_id=*/17049, /*speed=*/3.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*pose=*/{-4501.54, -2583.09, 5.93462}, /*length=*/10.0,
               /*width=*/3.0, /*velocity=*/0.0, /*height=*/1.8);

  AddStraightPredictedTrajectory(/*start_pose=*/{-4501.54, -2583.09, 5.93462},
                                 /*end_pose=*/{-4501.64, -2583.19, 5.93462},
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_TRUE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnoreOutOfExtendPathRangeTest) {
  SetEgoPose(/*lane_id=*/16867, /*portion=*/0.2);
  // TODO(waylon): The extended path is short.
  CreateLaneFollowPath(/*extend_backward=*/false);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16879, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16879, /*start_portion=*/0.2,
                                 /*end_lane_id=*/16867, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_TRUE(decision.Ignore());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnoreFullyBehindForOtherLanesTest) {
  // https://drive.google.com/file/d/1FL71V3fG9b7r3ty1oM9xBaspAcAEyr9M/view?usp=sharing
  SetEgoPose(/*lane_id=*/16965, /*portion=*/0.4);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97505, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/97505,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/97505, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_NEAR(world_context().ra_to_trailing_bumper_shift(), -1.0946, 1e-3);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnoreFullyBehindDuringNudgingTest) {
  // https://drive.google.com/file/d/1e0ezYIqebn9nmWD2uBLwkxlllR9gWqCU/view?usp=sharing
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.7);

  mutable_ego_intention().set_homotopy(
      planner::pb::IntentionResult::PASS_FROM_RIGHT);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16915, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16915, /*start_portion=*/0.2,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_NEAR(world_context().ra_to_trailing_bumper_shift(), -1.0946, 1e-3);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  [[maybe_unused]] const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  // TODO(Reasoning): Temporarily disable.
  // EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest,
       DoNotIgnoreFullyBehindCyclistAroundCrosswalk) {
  SetEgoPose(/*lane_id=*/16867, /*portion=*/0.7);
  CreatePathWithLaneSequence({16879, 16867, 3861, 3859});
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Add a cyclist.
  const math::Pose2d cyclist_start_pose(
      /*x_in=*/-4520.069768, /*y_in=*/-2567.961619, /*yaw_in=*/-1.15323);
  const math::Pose2d cyclist_end_pose(
      /*x_in=*/-4512.741585, /*y_in=*/-2584.479748, /*yaw_in=*/-1.15323);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose, /*length=*/2.0, /*width=*/1.0,
               /*velocity=*/5.0);
  AddStraightPredictedTrajectory(cyclist_start_pose, cyclist_end_pose,
                                 /*likelihood=*/0.9, /*traj_id=*/1, cyclist);
  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_NEAR(world_context().ra_to_trailing_bumper_shift(), -1.0946, 1e-3);
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  ASSERT_EQ(decision.agent_policies().size(), 2);
  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(agent_policy.reasoning_object().object_type(),
            voy::perception::ObjectType::CYCLIST);
  EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE, agent_policy.reasoner_in_charge());
  EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest,
       DoNotIgnoreFullyBehindPedestrianAroundCrosswalk) {
  // https://drive.google.com/file/d/1RhCATUL0sro4XpyKqjtA6q5psg-SLuaE/view?usp=sharing
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.2);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Add a pedestrian.
  prediction::pb::Agent& pedestrian =
      AddAgent(voy::perception::ObjectType::PED,
               /*id=*/1, /*lane_id=*/16867, /*portion=*/0.1,
               /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/1.5);
  AddStraightPredictedTrajectory(/*start_lane_id=*/16867, /*start_portion=*/0.1,
                                 /*end_lane_id=*/16887, /*end_portion=*/0.1,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, pedestrian);
  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_NEAR(world_context().ra_to_trailing_bumper_shift(), -1.0946, 1e-3);
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  ASSERT_EQ(decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(agent_policy.reasoning_object().object_type(),
            voy::perception::ObjectType::PED);
  EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest, IgnoreNoOverlap) {
  // https://drive.google.com/file/d/10PlCjnKKQHtxKPNejJ4ZSqZJKAhuEUt_/view?usp=sharing
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16915, /*portion=*/0.8,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/16915,
                                     /*start_portion=*/0.8,
                                     /*end_lane_id=*/16915, /*end_portion=*/1.0,
                                     /*likelihood=*/1.0,
                                     /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());
  EXPECT_TRUE(overlap_regions().empty());

  Update();
  // One agent.
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  // Zero prediction decision
  EXPECT_TRUE(
      wrapped_prediction_decisions().front().prediction_decisions().empty());
}

TEST_F(PredictionDecisionMakerTest, MergeAgentReaction) {
  // https://drive.google.com/file/d/1VDdSwHCUXCYQVRSGdqG1rp9bIoChjSby/view?usp=sharing
  // https://drive.google.com/file/d/197q9AQoqITw9SWCJqru1pAw8oanfYRiM/view?usp=sharing
  SetEgoPose(/*lane_id=*/16905, /*portion=*/0.8);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/17043, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/17043,
                                     /*start_portion=*/0.0,
                                     /*end_lane_id=*/17043, /*end_portion=*/1.0,
                                     /*likelihood=*/1.0,
                                     /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  // One agent.
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(decision.Nominal());

  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE, policy.reasoner_in_charge());
  EXPECT_FALSE(policy.reasoning_object().is_leading_agent());
  EXPECT_TRUE(policy.HasSlowDown());
}

// This test ensures no agent reaction shall be applied for leading agents.
TEST_F(PredictionDecisionMakerTest, LeadingAgentNoReaction) {
  // https://drive.google.com/file/d/1LrgC_HX2g04N4KsOcXij6Z2YlDf7ts4n/view?usp=sharing
  // https://drive.google.com/file/d/197q9AQoqITw9SWCJqru1pAw8oanfYRiM/view?usp=sharing
  SetEgoPose(/*lane_id=*/43411, /*portion=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/43411, /*portion=*/0.6,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStraightPredictedTrajectory(/*start_lane_id=*/43411,
                                     /*start_portion=*/0.6,
                                     /*end_lane_id=*/43411, /*end_portion=*/0.9,
                                     /*likelihood=*/1.0,
                                     /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  // One agent.
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(decision.Nominal());

  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE, policy.reasoner_in_charge());
  // Leading agent has no reaction.
  EXPECT_TRUE(policy.reasoning_object().is_leading_agent());
  EXPECT_FALSE(policy.HasSlowDown());
}

TEST_F(PredictionDecisionMakerTest, NonjunctionUTurnCautiousTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kUturn);
  SetEgoPose(/*lane_id=*/152970, /*portion=*/0.5);
  CreateLaneFollowPath();

  const math::Pose2d start_pose(/*x_in=*/50682.8276, /*y_in=*/14443.8401,
                                /*yaw_in=*/-0.4);
  const math::Pose2d end_pose(/*x_in=*/50683.4904, /*y_in=*/14439.4641,
                              /*yaw_in=*/0.0);
  prediction::pb::Agent& agent = AddAgent(voy::perception::ObjectType::VEHICLE,
                                          /*id=*/1, start_pose,
                                          /*length=*/4.0, /*width=*/2.0,
                                          /*velocity=*/2.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);
  AddAgentSeenEgoTimeToSeed(
      /*object_id=*/1, /*timestamp=*/timestamp() - 1000);
  Update();

  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(1, wrapped_prediction_decisions()[0].prediction_decisions().size());
  const PredictionDecision& decision =
      wrapped_prediction_decisions()[0].prediction_decisions().front();

  EXPECT_TRUE(decision.Nominal());
  EXPECT_GT(decision.agent_policies().size(), 1);

  const AgentPolicy& agent_cautious_policy = decision.agent_policies().back();
  EXPECT_EQ(pb::ReasonerId::CAUTIOUS_DRIVING,
            agent_cautious_policy.reasoner_in_charge());
  EXPECT_EQ(pb::CautiousDrivingReason::NONREACTIVE_AGENT,
            agent_cautious_policy.cautious_driving_reason());
}

// https://drive.google.com/file/d/1GbE3TT9K4CzK4FnrU07xiKlzfcz_r-uF/view?usp=sharing
TEST_F(PredictionDecisionMakerTest, DISABLED_JunctionCautiousTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97505, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0, /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectort =
      AddStraightPredictedTrajectory(/*start_lane_id=*/97505,
                                     /*start_portion=*/0.1,
                                     /*end_lane_id=*/97505, /*end_portion=*/0.9,
                                     /*likelihood=*/0.2, /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.CautiousDriving());
  ASSERT_EQ(1, decision.agent_policies().size());
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::CAUTIOUS_DRIVING, policy.reasoner_in_charge());
  EXPECT_NEAR(policy.strength(), 0.5, 1e-6);
}

// https://drive.google.com/file/d/1uatAnEqwXKC1FUdeKNrCh6Cp9sy3wdQb/view?usp=sharing
TEST_F(PredictionDecisionMakerTest, DISABLED_CrosswalkCautiousTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d ped_start_pose(/*x_in=*/-4517.616859,
                                    /*y_in=*/-2577.720447,
                                    /*yaw_in=*/0.41666);
  const math::Pose2d ped_end_pose(/*x_in=*/-4509.888315, /*y_in=*/-2574.300005,
                                  /*yaw_in=*/0.41666);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::PED, /*id=*/1, ped_start_pose,
               /*length=*/0.7, /*width=*/0.7, /*velocity=*/1.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectort =
      AddStraightPredictedTrajectory(ped_start_pose, ped_end_pose,
                                     /*likelihood=*/0.2, /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.CautiousDriving());
  ASSERT_EQ(1, decision.agent_policies().size());
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::CAUTIOUS_DRIVING, policy.reasoner_in_charge());
  EXPECT_NEAR(policy.strength(), 0.5, 1e-6);

  const AgentTrajectoryInfo& agent_trajectory_info =
      policy.agent_trajectory_info();
  const std::vector<pb::OverlapRegion>& overlap_regions_result =
      overlap_regions();
  ASSERT_EQ(overlap_regions_result.size(), 1);
  ASSERT_TRUE(
      agent_trajectory_info.HasOverlappedCrosswalk(overlap_regions_result[0]));
  const std::vector<OverlappedCrosswalkInfo>& overlapped_crosswalks =
      agent_trajectory_info.overlap_region_info(overlap_regions_result[0])
          .overlapped_crosswalk_infos;
  EXPECT_FALSE(overlapped_crosswalks.empty());
  ASSERT_EQ(overlapped_crosswalks.size(), 1);
  EXPECT_EQ(overlapped_crosswalks[0].crosswalk.crosswalk_ptr->id(), 4223);
  EXPECT_EQ(overlapped_crosswalks[0].yield_zone_idx, 0);
  EXPECT_EQ(overlapped_crosswalks[0].motion_type,
            OverlapMotionTypeWithRespectToCrosswalk::kCrossingIn);
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnoreFullyBehindDuringMerging) {
  // https://drive.google.com/file/d/1ozDIUK1CmfZRCybgnhul8EBZ_JmUkqRV/view?usp=sharing
  SetEgoPose(/*lane_id=*/108315, /*portion=*/0.4);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/108313, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/108313,
                                 /*start_portion=*/0.0,
                                 /*end_lane_id=*/108319, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  Update();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(decision.agent_policies().size(), 1);
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(policy.reasoner_in_charge(), pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(policy.HasSlowDown());
}

TEST_F(PredictionDecisionMakerTest,
       AssignStationaryTrajectoryToLeadAndMergeReasonerTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath(/*extend_backward=*/false);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const int64_t kObjectId = 1;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStationaryPredictedTrajectory(/*lane_id=*/16913,
                                       /*portion=*/0.7,
                                       /*likelihood=*/0.9, /*traj_id=*/1,
                                       agent);

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(1, decision.agent_policies().size());
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_TRUE(policy.reasoning_object().is_leading_agent());
  EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE, policy.reasoner_in_charge());
}

TEST_F(PredictionDecisionMakerTest,
       AssignNudgingStationaryTrajectoryToCrossAgentReasonerTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath(/*extend_backward=*/false);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const int64_t kObjectId = 1;
  constexpr double kBboxHeadingDiffToLane = M_PI / 2;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, kObjectId,
               /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0, /*height=*/1.5,
               /*heading_diff_to_lane=*/kBboxHeadingDiffToLane);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory =
      AddStationaryPredictedTrajectory(
          /*lane_id=*/16913,
          /*portion=*/0.7,
          /*likelihood=*/0.9, /*traj_id=*/1, agent,
          /*bbox_heading_diff_to_lane=*/kBboxHeadingDiffToLane);

  mutable_ego_intention().set_homotopy(
      planner::pb::IntentionResult::PASS_FROM_LEFT);
  planner::pb::EgoIntention& object_intention =
      (*mutable_ego_intention().mutable_object_intentions())[kObjectId];
  object_intention.set_object_id(kObjectId);
  object_intention.set_is_overtaken(true);
  planner::pb::SnapshotIntention* nudge_snapshot_intention =
      object_intention.add_snapshot_intentions();
  nudge_snapshot_intention->set_pass_state(
      planner::pb::SnapshotIntention::PASS_LEFT);

  Update();

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(1, decision.agent_policies().size());
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_TRUE(policy.reasoning_object().is_ego_path_overtaking());
  EXPECT_FALSE(policy.reasoning_object().is_leading_agent());
  EXPECT_EQ(pb::ReasonerId::CROSS_AGENT, policy.reasoner_in_charge());
}

// https://drive.google.com/file/d/1uatAnEqwXKC1FUdeKNrCh6Cp9sy3wdQb/view?usp=sharing
TEST_F(PredictionDecisionMakerTest, AllocatePedAroundCrosswalkToVruReasoner) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.1);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d ped_start_pose(/*x_in=*/-4517.616859,
                                    /*y_in=*/-2577.720447,
                                    /*yaw_in=*/0.41666);
  const math::Pose2d ped_end_pose(/*x_in=*/-4509.888315, /*y_in=*/-2574.300005,
                                  /*yaw_in=*/0.41666);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::PED, /*id=*/1, ped_start_pose,
               /*length=*/0.7, /*width=*/0.7, /*velocity=*/1.0);

  prediction::pb::YieldIntention yield_intention;
  yield_intention.set_yield_probability(0.6);
  yield_intention.set_yield_intention_type(::prediction::pb::YIELD_TO_EGO_CAR);
  *(agent.mutable_yield_intention()) = std::move(yield_intention);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectort =
      AddStraightPredictedTrajectory(ped_start_pose, ped_end_pose,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(decision.agent_policies().size(), 1);
  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(policy.reasoner_in_charge(), pb::ReasonerId::VRU);
  EXPECT_TRUE(policy.HasSlowDown());

  const AgentTrajectoryInfo& agent_trajectory_info =
      policy.agent_trajectory_info();
  const std::vector<pb::OverlapRegion>& overlap_regions_result =
      overlap_regions();
  ASSERT_EQ(overlap_regions_result.size(), 1);
  ASSERT_TRUE(
      agent_trajectory_info.HasOverlappedCrosswalk(overlap_regions_result[0]));
  const std::vector<OverlappedCrosswalkInfo>& overlapped_crosswalks =
      agent_trajectory_info.overlap_region_info(overlap_regions_result[0])
          .overlapped_crosswalk_infos;
  EXPECT_FALSE(overlapped_crosswalks.empty());
  ASSERT_EQ(overlapped_crosswalks.size(), 1);
  EXPECT_EQ(overlapped_crosswalks[0].crosswalk.crosswalk_ptr->id(), 4223);
  EXPECT_EQ(overlapped_crosswalks[0].yield_zone_idx, 0);
  EXPECT_EQ(overlapped_crosswalks[0].motion_type,
            OverlapMotionTypeWithRespectToCrosswalk::kCrossingIn);
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnorePedNearCrosswalkEgoStationary) {
  // https://drive.google.com/file/d/13EkCez6_cO8h8X43YVhJ7Z7FS47JBk8I/view?usp=sharing
  SetEgoPose(/*lane_id=*/16865, /*portion=*/1.0);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d ped_start_pose(/*x_in=*/-4517.616859,
                                    /*y_in=*/-2577.720447,
                                    /*yaw_in=*/0.41666);
  const math::Pose2d ped_end_pose(/*x_in=*/-4509.888315, /*y_in=*/-2574.300005,
                                  /*yaw_in=*/0.41666);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::PED, /*id=*/1, ped_start_pose,
               /*length=*/0.7, /*width=*/0.7, /*velocity=*/1.0);

  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectort =
      AddStraightPredictedTrajectory(ped_start_pose, ped_end_pose,
                                     /*likelihood=*/0.9, /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(decision.agent_policies().size(), 1);
  EXPECT_TRUE(decision.agent_policies().front().yield_always_possible());
}

// https://drive.google.com/file/d/1XfZ9-QuOduZve2QCfNc1NDWss9tpFu17/view?usp=sharing
TEST_F(PredictionDecisionMakerTest, ShouldIgnoreTrajectoryForTailgaterTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.8);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16865, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16865,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/16865, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  ASSERT_EQ(decision.agent_policies().size(), 1);
}

// https://drive.google.com/file/d/1M-3YUTU3fkH2k8iTggsgXAqZuHi-E7sH/view?usp=share_link
TEST_F(PredictionDecisionMakerTest, UnknownObjectTest) {
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.2);
  CreateLaneFollowPath();
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/-4511.09, /*y_in=*/-2570.75,
                                /*yaw_in=*/-1.15);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::UNKNOWN,
               /*id=*/1, start_pose, /*length=*/4.0, /*width=*/0.2,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(start_pose, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
}

TEST_F(PredictionDecisionMakerTest, ConsiderPrimaryTrajectoryTest) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16913, /*portion=*/0.2);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16913, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16913, /*start_portion=*/0.7,
                                 /*end_lane_id=*/16913, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(object_prediction_map().at(1).front().is_primary_trajectory());
  EXPECT_TRUE(decision.Nominal());
}

// This test ensures no agent reaction will be applied for invalid object type.
TEST_F(PredictionDecisionMakerTest,
       ShouldConsiderAgentReactionInvalidObjectTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);

  SetEgoPose(/*lane_id=*/14051, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& slow_agent =
      AddAgent(voy::perception::ObjectType::TRAFFIC_CONE,
               /*id=*/1, /*lane_id=*/10533, /*portion=*/0.6,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);
  AddStraightPredictedTrajectory(
      /*start_lane_id=*/10533,
      /*start_portion=*/0.6,
      /*end_lane_id=*/10533, /*end_portion=*/1.0,
      /*likelihood=*/0.9, /*traj_id=*/1, slow_agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_TRUE(trajectory_info().IsEgoInRightMostVehicleLane());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(1, reasoning_inputs().size());

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(decision.Nominal());

  const AgentPolicy& policy = decision.agent_policies().back();
  EXPECT_EQ(pb::ReasonerId::CROSS_AGENT, policy.reasoner_in_charge());
  EXPECT_FALSE(policy.HasSlowDown());
}

// In this test, the vehicle is reversing and the overlap_region's motion type
// is CROSSING, NOT OVERCOMING.
TEST_F(PredictionDecisionMakerTest, ShouldConsiderReversingVehicle) {
  SetEgoPose(/*lane_id=*/3863, /*portion=*/0.5);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{3863, 16879, 16867});
  EXPECT_FALSE(path().empty());
  // Add a reversing vehicle whose trajectory will roll over ego
  prediction::pb::Agent& reversing_agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1,
      /*lane_id*/ 108229, /*portion=*/0.9, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/2.0);
  std::vector<math::Pose2d> waypoints{
      LanePointToPose(/*lane_id=*/108229, /*portion=*/0.9),
      LanePointToPose(/*lane_id=*/3863, /*portion=*/0.9),
      LanePointToPose(/*lane_id=*/3863, /*portion=*/0.2)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1,
                                   reversing_agent);
  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(overlap_regions().front().motion_type(),
            pb::OVERLAP_MOTION_CROSSING);
  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
}

TEST_F(PredictionDecisionMakerTest, CyclistOvertakingEgo) {
  SetEgoPose(/*lane_id=*/3863, /*portion=*/0.9);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{3863, 16879, 16867});
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4528.443019, /*y_in=*/-2547.413396, /*yaw_in=*/-1.141758);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4518.589030, /*y_in=*/-2568.954143, /*yaw_in=*/-1.141758);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/3.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory_1 =
      AddStraightPredictedTrajectory(
          cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
          /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  // Add a cyclist overtaking Ego who is before Ego and has been overtaking Ego.
  const math::Pose2d cyclist_start_pose_before_ego(
      /*x_in=*/-4522.584547, /*y_in=*/-2560.239096, /*yaw_in=*/-1.141758);
  const math::Pose2d cyclist_end_pose_before_ego(
      /*x_in=*/-4515.646174, /*y_in=*/-2575.315185, /*yaw_in=*/-1.141758);
  prediction::pb::Agent& agent_before_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/2,
               cyclist_start_pose_before_ego, /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/3.0);
  [[maybe_unused]] prediction::pb::PredictedTrajectory& predicted_trajectory_2 =
      AddStraightPredictedTrajectory(
          cyclist_start_pose_before_ego, cyclist_end_pose_before_ego,
          /*likelihood=*/0.9, /*traj_id=*/1, agent_before_ego);

  EXPECT_EQ(2, agent_list().agent_list().size());

  Update();
  ASSERT_EQ(2, wrapped_prediction_decisions().size());

  for (const PredictionDecisionMakerOutput& output :
       wrapped_prediction_decisions()) {
    ASSERT_EQ(1, output.prediction_decisions().size());
    const PredictionDecision& prediction_decision =
        output.prediction_decisions().front();
    EXPECT_TRUE(
        prediction_decision.agent_trajectory_info().is_overtaking_ego());
    if (output.reasoning_object().id() == 1) {
      // Agent behind Ego.
      EXPECT_TRUE(prediction_decision.Ignore());
    } else {
      // Agent before Ego.
      EXPECT_TRUE(prediction_decision.Nominal());
      ASSERT_EQ(1, prediction_decision.agent_policies().size());
      const AgentPolicy& agent_policy =
          prediction_decision.agent_policies().front();
      EXPECT_TRUE(agent_policy.yield_always_possible());
    }
  }
}

// This test ensures that gap align constraint is not added for large vehicles
// on Ego path.
// https://drive.google.com/file/d/1ycN4JrHqDdhLaydmC9ElTKcKi7QLeAID/view?usp=share_link
TEST_F(PredictionDecisionMakerTest, NotAddGapAlignForLargeVehicleOnEgoPath) {
  SetEgoPose(/*lane_id=*/3875, /*portion=*/0.2);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{3875, 97143, 97149});

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d strict_agent_start_pose(
      /*x_in=*/-4483.103425, /*y_in=*/-2575.188853, /*yaw_in=*/1.997443);
  const math::Pose2d strict_agent_end_pose(
      /*x_in=*/-4502.003821, /*y_in=*/-2533.610146, /*yaw_in=*/1.997443);
  prediction::pb::Agent& strict_agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               strict_agent_start_pose, /*length=*/10.0, /*width=*/3.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(strict_agent_start_pose, strict_agent_end_pose,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, strict_agent);

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(1, reasoning_inputs().size());
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(std::none_of(decision.agent_policies().begin(),
                           decision.agent_policies().end(),
                           [](const AgentPolicy& agent_policy) {
                             return agent_policy.generative_constraint_type() ==
                                    pb::GenerativeConstraintType::GAP_ALIGN;
                           }));
}

// This test ensures that gap align constraint is not added for large vehicles
// when Ego changes lane.
// https://drive.google.com/file/d/1-FCZ9LTvUWA9gfdkJ5og-CeEZ4s5fdZx/view?usp=share_link
TEST_F(PredictionDecisionMakerTest,
       NotAddGapAlignForLargeVehicleWhenEgoChangesLane) {
  SetEgoPose(/*lane_id=*/3875, /*portion=*/0.2);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION,
      /*start_arclength=*/0.0, /*source_lane_id=*/97149,
      /*target_lane_id=*/52649, /*is_current_homotopy_lane_change=*/false);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{3875, 97143, 97149});

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d large_vehicle_start_pose(
      /*x_in=*/-4484.368000, /*y_in=*/-2565.370508, /*yaw_in=*/1.997443);
  const math::Pose2d large_vehicle_end_pose(
      /*x_in=*/-4499.818881, /*y_in=*/-2531.654047, /*yaw_in=*/1.997443);
  prediction::pb::Agent& large_vehicle =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               large_vehicle_start_pose, /*length=*/10.0, /*width=*/3.0,
               /*velocity=*/10.0, /*height=*/2.5);
  AddStraightPredictedTrajectory(large_vehicle_start_pose,
                                 large_vehicle_end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, large_vehicle);

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_FALSE(overlap_regions().front().contain_strict_overlap());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(1, reasoning_inputs().size());
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(std::all_of(decision.agent_policies().begin(),
                          decision.agent_policies().end(),
                          [](const AgentPolicy& agent_policy) {
                            return agent_policy.reasoner_in_charge() ==
                                   pb::ReasonerId::LEAD_AND_MERGE;
                          }));
  EXPECT_TRUE(std::none_of(decision.agent_policies().begin(),
                           decision.agent_policies().end(),
                           [](const AgentPolicy& agent_policy) {
                             return agent_policy.generative_constraint_type() ==
                                    pb::GenerativeConstraintType::GAP_ALIGN;
                           }));
}

TEST_F(PredictionDecisionMakerTest,
       NotAddGapAlignForLargeVehicleLowLikelihoodTrajectory) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129379, /*portion=*/0.0,
             /*speed=*/10.0);
  CreateLaneFollowPath();

  const math::Pose2d start_pose(/*x_in=*/26892.59, /*y_in=*/39958.67,
                                /*yaw_in=*/-0.15);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, start_pose,
               /*length=*/10.0, /*width=*/2.0,
               /*velocity=*/8.0, /*height=*/2.7);
  EXPECT_EQ(1, agent_list().agent_list().size());
  const math::Pose2d end_pose(/*x_in=*/26920.77, /*y_in=*/39953.56,
                              /*yaw_in=*/-0.15);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.5,
                                 /*traj_id=*/1, agent);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.2,
                                 /*traj_id=*/2, agent);

  Update();

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(1, reasoning_inputs().size());
  EXPECT_EQ(
      2, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      wrapped_prediction_decisions().front().prediction_decisions().back();

  EXPECT_TRUE(decision.CautiousDriving());

  EXPECT_EQ(1, decision.agent_trajectory_info().overlap_regions().size());
  EXPECT_TRUE(std::none_of(decision.agent_policies().begin(),
                           decision.agent_policies().end(),
                           [](const AgentPolicy& agent_policy) {
                             return agent_policy.generative_constraint_type() ==
                                    pb::GenerativeConstraintType::GAP_ALIGN;
                           }));
}

// https://drive.google.com/file/d/1KEsmUB4mlrxzduFfLaixCYgcPtZ_Xrxc/view?usp=share_link
TEST_F(PredictionDecisionMakerTest,
       NotIgnoreCyclistOvertakingEgoWhenXLaneNudge) {
  FLAGS_planning_enable_decoupled_xlane_nudge = true;
  SetEgoPose(/*lane_id=*/16919, /*portion=*/0.1, /*speed=*/5.0, /*accel=*/1.0);
  const std::string path_wkt =
      "LINESTRING(-4489.0705987138754 -2579.6165268199729,"
      "-4488.8592359199147 -2580.0696556861126,"
      "-4488.181973021824 -2581.4071971243361,"
      "-4487.6273550649403 -2582.238268012261,"
      "-4486.6334442600755 -2583.3600670463334,"
      "-4485.9254456215403 -2584.0662594232003,"
      "-4484.8894420292863 -2585.1506242352557,"
      "-4483.9474063223406 -2586.3170809909138,"
      "-4483.3784280070004 -2587.1392368774445,"
      "-4482.5925483928149 -2588.4166458709624,"
      "-4482.0993168712112 -2589.2865104543603,"
      "-4481.6232718421006 -2590.1659104035834,"
      "-4481.1635101560159 -2591.0539279587033,"
      "-4480.5165558683339 -2592.4070617330362,"
      "-4479.9571854580445 -2593.7983373374886,"
      "-4479.8000906452044 -2594.27294680675,"
      "-4479.5455546395888 -2595.2395413255585,"
      "-4479.3897532770525 -2596.2266173676153,"
      "-4479.3561139053445 -2597.2250438455267,"
      "-4479.4659289104829 -2598.2176546611017,"
      "-4479.7324245735217 -2599.1798943720487,"
      "-4480.1503927107688 -2600.0867858468605,"
      "-4480.410132007376 -2600.5136926249847)";

  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_is_boundary_extended(true);
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT);
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
#pragma GCC diagnostic push
  // DECOUPLED_LANE_FOLLOW is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {16919, 16995},
      planner::pb::ManeuverType::DECOUPLED_LANE_FOLLOW);
#pragma GCC diagnostic pop

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4487.743316, /*y_in=*/-2575.759922, /*yaw_in=*/-1.141809);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4478.878670, /*y_in=*/-2595.474559, /*yaw_in=*/-1.141809);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.agent_trajectory_info().is_overtaking_ego());
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.reasoner_in_charge() ==
              pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.generative_constraint_type() ==
              pb::GenerativeConstraintType::YIELD_STRICT_WITH_SOFT);
}

// https://drive.google.com/file/d/1EVeaBunh5z73GL5LABjU6SWz6l4o6Tsk/view?usp=share_link
TEST_F(PredictionDecisionMakerTest,
       NotIgnoreCyclistOvertakingEgoWhenLaneChange) {
  SetEgoLane(/*current_lane_id=*/52649);
  SetEgoPose(/*x_pos=*/-4495.31, /*y_pos=*/-2554.42, /*yaw=*/1.810967,
             /*speed=*/5.0, /*accel=*/1.0);
  const std::string path_wkt =
      "LINESTRING(-4491.16 -2566.7,-4491.36 -2566.25,-4491.57 -2565.8,"
      "-4491.78 -2565.34,-4491.98 -2564.88,-4492.18 -2564.42,-4492.37 -2563.96,"
      "-4492.56 -2563.5,-4492.75 -2563.04,-4492.93 -2562.57,-4493.1 -2562.1,"
      "-4493.27 -2561.63,-4493.43 -2561.16,-4493.59 -2560.68,-4493.74 -2560.21,"
      "-4493.89 -2559.73,-4494.04 -2559.25,-4494.18 -2558.77,-4494.31 -2558.29,"
      "-4494.44 -2557.81,-4494.57 -2557.32,-4494.7 -2556.84,-4494.82 -2556.36,"
      "-4494.95 -2555.87,-4495.07 -2555.39,-4495.19 -2554.9,-4495.31 -2554.42,"
      "-4495.43 -2553.93,-4495.55 -2553.44,-4495.66 -2552.96,-4495.78 -2552.47,"
      "-4495.9 -2551.99,-4496.02 -2551.5,-4496.14 -2551.02,-4496.27 -2550.53,"
      "-4496.39 -2550.05,-4496.52 -2549.56,-4496.64 -2549.08,-4496.77 -2548.6,"
      "-4496.91 -2548.11,-4497.04 -2547.63,-4497.17 -2547.15,-4497.31 -2546.67,"
      "-4497.45 -2546.19,-4497.59 -2545.71,-4497.74 -2545.23,-4497.88 -2544.75,"
      "-4498.03 -2544.28,-4498.19 -2543.8,-4498.34 -2543.33,-4498.5 -2542.85,"
      "-4498.65 -2542.38,-4498.81 -2541.9,-4498.98 -2541.43,-4499.14 -2540.96,"
      "-4499.31 -2540.49,-4499.48 -2540.02,-4499.65 -2539.55,-4499.82 -2539.08,"
      "-4500 -2538.61,-4500.17 -2538.14,-4500.35 -2537.67,-4500.53 -2537.21,"
      "-4500.72 -2536.74,-4500.9 -2536.28,-4501.08 -2535.81,-4501.27 -2535.35,"
      "-4501.46 -2534.89,-4501.65 -2534.42,-4501.84 -2533.96,-4502.03 -2533.5,"
      "-4502.23 -2533.04,-4502.42 -2532.58,-4502.62 -2532.12,-4502.81 -2531.66,"
      "-4503.01 -2531.2,-4503.21 -2530.74,-4503.41 -2530.28,-4503.61 -2529.82,"
      "-4503.81 -2529.37,-4504.01 -2528.91,-4504.21 -2528.45,-4504.42 -2527.99,"
      "-4504.62 -2527.54,-4504.83 -2527.08,-4505.03 -2526.62,-4505.24 -2526.17,"
      "-4505.44 -2525.71,-4505.65 -2525.26,-4505.86 -2524.8,-4506.06 -2524.35,"
      "-4506.27 -2523.89,-4506.48 -2523.44,-4506.69 -2522.98,-4506.9 -2522.53,"
      "-4507.1 -2522.08,-4507.31 -2521.62,-4507.52 -2521.17,-4507.73 -2520.71,"
      "-4507.94 -2520.26,-4508.15 -2519.81,-4508.36 -2519.35,-4508.57 -2518.9,"
      "-4508.78 -2518.44,-4508.99 -2517.99,-4509.2 -2517.54,-4509.41 -2517.08,"
      "-4509.63 -2516.63,-4509.84 -2516.18,-4510.05 -2515.72,-4510.26 -2515.27,"
      "-4510.47 -2514.82,-4510.68 -2514.36,-4510.89 -2513.91,-4511.1 -2513.46,"
      "-4511.32 -2513.01,-4511.53 -2512.55,-4511.74 -2512.1,-4511.95 -2511.65,"
      "-4512.16 -2511.19,-4512.37 -2510.74)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION,
      /*start_arclength=*/0.0, /*source_lane_id=*/52649,
      /*target_lane_id=*/97149, /*is_current_homotopy_lane_change=*/true);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {52649, 97149},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4488.175705, /*y_in=*/-2565.043777, /*yaw_in=*/2.011691);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4511.605328, /*y_in=*/-2515.738327, /*yaw_in=*/2.011691);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.agent_trajectory_info().is_overtaking_ego());
  EXPECT_TRUE(prediction_decision.Nominal());
  EXPECT_TRUE(trajectory_info().lane_encroachment_info().side() ==
              math::pb::kRight);
  EXPECT_GT(
      trajectory_info().lane_encroachment_info().out_of_lane_segments().size(),
      0);
  EXPECT_EQ(trajectory_info().critical_encroachment_range_infos().size(), 1);
  EXPECT_TRUE(
      trajectory_info().critical_encroachment_range_infos().front().side ==
      math::pb::kRight);
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .has_overlap_region_in_encroachment_regions());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.reasoner_in_charge() ==
              pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.generative_constraint_type() ==
              pb::GenerativeConstraintType::YIELD_STRICT_WITH_SOFT);
}

// https://drive.google.com/file/d/1juD4VO9hJdyZp8XEPROffMir_Bk05YFV/view?usp=share_link
TEST_F(PredictionDecisionMakerTest,
       NotIgnoreCyclistOvertakingEgoWhenPullOutJumpOut) {
  SetEgoLane(/*current_lane_id=*/97149);
  SetEgoPose(/*x_pos=*/-4495.671392, /*y_pos=*/-2539.430073,
             /*yaw=*/2.06745, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(-4495.671392 -2539.430073,"
      "-4496.657021 -2537.689847,"
      "-4497.679066 -2535.970766,"
      "-4498.740855 -2534.275955,"
      "-4499.844059 -2532.607809,"
      "-4500.985509 -2530.965574,"
      "-4502.150332 -2529.339792,"
      "-4503.309296 -2527.709842,"
      "-4504.439275 -2526.059690,"
      "-4505.543448 -2524.392129,"
      "-4506.643524 -2522.721853,"
      "-4507.765029 -2521.065933)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  SetIsPullOutJumpOut(true);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {97149, 97153, 97157},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4494.240501, /*y_in=*/-2546.959077, /*yaw_in=*/1.999105);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4513.117137, /*y_in=*/-2505.615229, /*yaw_in=*/1.999105);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/0.7, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  EXPECT_TRUE(trajectory_info().lane_encroachment_info().side() ==
              math::pb::kLeft);
  EXPECT_GT(
      trajectory_info().lane_encroachment_info().out_of_lane_segments().size(),
      0);
  EXPECT_EQ(trajectory_info().critical_encroachment_range_infos().size(), 1);
  EXPECT_TRUE(
      trajectory_info().critical_encroachment_range_infos().back().side ==
      math::pb::kLeft);

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.agent_trajectory_info().is_overtaking_ego());
  EXPECT_TRUE(prediction_decision.Nominal());
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .has_overlap_region_in_encroachment_regions());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.reasoner_in_charge() ==
              pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest,
       NotIgnoreCyclistOvertakingEgoWhenPullOutTriggered) {
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166837);
  SetEgoPose(/*x_pos=*/49373.28, /*y_pos=*/13556.08,
             /*yaw=*/3.12093, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49373.28 13556.08,49371.20 13554.43,49368.81 "
      "13554.89,49364.73 13554.99,49361.29 13555.67,49356.82 13554.29,49353.73 "
      "13553.30)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  // TODO(jinhao): Refine the unit test for the LK homotopy when pull out is
  // triggered.
  SetIsPullOutJumpOut(false);
  SetIsPullOutTriggered(true);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {166837, 166842, 166852},
      planner::pb::ManeuverType::PULL_OUT);
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose_0(/*x_in=*/49378.04, /*y_in=*/13555.72,
                                  /*yaw_in=*/3.12093);
  const math::Pose2d end_pose_0(/*x_in=*/49361.59, /*y_in=*/13556.06,
                                /*yaw_in=*/3.12093);
  prediction::pb::Agent& cyclist_overtaking_closely =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose_0,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);

  AddStraightPredictedTrajectory(start_pose_0, end_pose_0, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist_overtaking_closely);
  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  // TODO(jinhao): Fix the encroachment FN in such cases.
  //   EXPECT_TRUE(prediction_decision.agent_trajectory_info()
  //                   .has_overlap_region_in_encroachment_regions());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.reasoner_in_charge() ==
              pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

// https://drive.google.com/file/d/13sNxTOH63lxMnerBHpXatlxMnNZExwbN/view?usp=share_link
TEST_F(PredictionDecisionMakerTest,
       NotIgnoreCyclistOvertakingEgoWhenCrossLanePullOver) {
  SetEgoLane(/*current_lane_id=*/97149);
  SetEgoPose(/*x_pos=*/-4488.589912, /*y_pos=*/-2563.061381,
             /*yaw=*/2.06745, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(-4488.589912 -2563.061381,-4488.598334 "
      "-2563.042910,-4488.679874 -2562.858587,-4488.851033 "
      "-2562.439998,-4489.086452 -2561.777169,-4489.354355 "
      "-2560.860492,-4489.618648 -2559.683638,-4489.850310 "
      "-2558.246109,-4490.047820 -2556.552875,-4490.259386 "
      "-2554.618533,-4490.561744 -2552.502952,-4491.010898 "
      "-2550.306481,-4491.615187 -2548.126494,-4492.330702 "
      "-2546.043159,-4493.082497 -2544.141002,-4493.787139 "
      "-2542.512612,-4494.369032 -2541.243537,-4494.775669 "
      "-2540.386998,-4494.997016 -2539.928871,-4495.076602 "
      "-2539.765101,-4495.088712 -2539.740173)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {97149, 97153},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);
  SetBehaviorType(planner::pb::BehaviorType::DECOUPLED_PULL_OVER);
  SetPullOverDestination(/*portion=*/1.0, /*is_pull_over_jump_in=*/true);
  SetPullOverStatus(planner::pull_over::PullOverStatus(
      /*should_trigger_pull_over=*/true,
      /*execution_state=*/planner::pb::PullOverExecutionState::EXECUTE,
      /*pull_over_progress=*/planner::pb::PullOverProgress::kStarted,
      /*is_in_abort_recovery=*/false));

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4483.743482, /*y_in=*/-2567.018108, /*yaw_in=*/1.999105);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4502.567736, /*y_in=*/-2526.019626, /*yaw_in=*/1.999105);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/1.5, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  EXPECT_TRUE(trajectory_info().lane_encroachment_info().side() ==
              math::pb::kRight);
  EXPECT_GT(
      trajectory_info().lane_encroachment_info().out_of_lane_segments().size(),
      0);

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.agent_trajectory_info().is_overtaking_ego());
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .has_overlap_region_in_encroachment_regions());
  EXPECT_EQ(
      prediction_decision.agent_trajectory_info().overlap_regions().size(), 1);
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .overlap_region_info(0)
                  .is_in_encroachment_regions());
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.reasoner_in_charge() ==
              pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest, ReasonerInChargeHigherPrecedenceMergeTest) {
  // https://drive.google.com/file/d/1TqpB8NHmlb-DEAltS_scILKNBfs9zCz8/view?usp=sharing
  SetEgoPose(/*lane_id=*/97429, /*portion=*/0.3);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97505, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97505,
                                 /*start_portion=*/0.5,
                                 /*end_lane_id=*/97505, /*end_portion=*/1.0,
                                 /*likelihood=*/1.0, /*traj_id=*/1, agent);

  Update();
  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, reasoning_inputs().size());
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  EXPECT_TRUE(trajectory_info().IsInRightTurn());
  for (const pb::OverlapRegion& overlap_region : overlap_regions()) {
    EXPECT_TRUE(reasoning_util::IsEgoMergingToHigherPriorityConflictingLane(
        input.reasoning_object, trajectory_info(),
        input.agent_trajectory_infos.front(), overlap_region));
  }

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(decision.agent_policies().size(), 1);

  for (const AgentPolicy& agent_policy : decision.agent_policies()) {
    EXPECT_EQ(pb::ReasonerId::CROSS_AGENT, agent_policy.reasoner_in_charge());
  }
}

TEST_F(PredictionDecisionMakerTest, AggregateLowLikelihoodIgnoredTrajectories) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.2);
  CreateLaneFollowPath();
  const int64_t object_id = 956;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/956, /*lane_id=*/97573, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.09, /*traj_id=*/1, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.05, /*traj_id=*/2, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.3, /*traj_id=*/3, agent);
  Update();

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      3, wrapped_prediction_decisions().front().prediction_decisions().size());
  // Trajectory 1 and 2 are aggregated, and trajectory 1 is the representative
  // trajectory.
  {
    // The aggregated trajectory has a cautious decision.
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 1));
    EXPECT_TRUE(decision.CautiousDriving());
    EXPECT_EQ(decision.agent_policies().size(), 1);
  }
  {
    // The trajectory is aggregated with the first one.
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 2));
    EXPECT_TRUE(decision.Ignore());
    EXPECT_TRUE(decision.agent_policies().empty());
    EXPECT_EQ(pb::IgnoreReason::TRAJECTORY_AGGREGATION,
              decision.ignore_reason());
  }
  {
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 3));
    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);
  }
}

TEST_F(PredictionDecisionMakerTest, AggregateCautiousTrajectories) {
  // https://drive.google.com/file/d/1LaLwzrvfZ2HuilN_m6IQ8p9DZRkGLPe0/view
  SetEgoPose(/*lane_id=*/16865, /*portion=*/0.2);
  CreateLaneFollowPath();
  const int64_t object_id = 956;
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/956, /*lane_id=*/97573, /*portion=*/0.7,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.26, /*traj_id=*/1, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.24, /*traj_id=*/2, agent);
  AddStraightPredictedTrajectory(/*start_lane_id=*/97573,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97573,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.5, /*traj_id=*/3, agent);
  Update();

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      3, wrapped_prediction_decisions().front().prediction_decisions().size());
  // Trajectory 1 and 2 are aggregated, and trajectory 1 is the representative
  // trajectory.
  {
    // The aggregated trajectory has a nominal decision.
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 1));

    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);
  }
  {
    // The trajectory is aggregated with the first one.
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 2));
    EXPECT_TRUE(decision.Ignore());
    EXPECT_TRUE(decision.agent_policies().empty());
    EXPECT_EQ(pb::IgnoreReason::TRAJECTORY_AGGREGATION,
              decision.ignore_reason());
  }
  {
    const PredictionDecision& decision =
        prediction_decision(ObjectTrajectoryId(object_id, 3));

    EXPECT_TRUE(decision.Nominal());
    EXPECT_EQ(decision.agent_policies().size(), 1);
  }
}

// This test ensures that, if with very low yield probability, agent reaction
// will not be applied for extra cautious agents.
TEST_F(PredictionDecisionMakerTest,
       ShouldConsiderAgentReactionExtraCautiousAgentLMReasonerTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);

  SetEgoPose(/*lane_id=*/14051, /*portion=*/0.7);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& slow_agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/10533, /*portion=*/0.6,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/2.0);

  // Set very low yield probability.
  prediction::pb::YieldIntention yield_intention;
  yield_intention.set_yield_probability(0.01);
  yield_intention.set_yield_intention_type(
      ::prediction::pb::NOT_YIELD_TO_EGO_CAR);
  *(slow_agent.mutable_yield_intention()) = std::move(yield_intention);

  AddStraightPredictedTrajectory(
      /*start_lane_id=*/10533,
      /*start_portion=*/0.6,
      /*end_lane_id=*/10533, /*end_portion=*/1.0,
      /*likelihood=*/0.9, /*traj_id=*/1, slow_agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_TRUE(trajectory_info().IsEgoInRightMostVehicleLane());
  EXPECT_EQ(1, agent_list().agent_list().size());

  traffic_lights_.clear_traffic_lights();
  AddTrafficLight(/*timestamp=*/timestamp(), /*sign_id=*/3033,
                  /*color=*/voy::TrafficLight_Color_GREEN,
                  /*type=*/voy::TrafficLight_Type_NORMAL);
  UpdateWorldModel();

  Update();

  EXPECT_EQ(1, overlap_regions().size());
  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(1, reasoning_inputs().size());

  const PredictionDecisionMakerInput& slow_object_input =
      reasoning_inputs().back();
  const ReasoningObject& slow_reasoning_object =
      slow_object_input.reasoning_object;
  EXPECT_EQ(slow_reasoning_object.id(), 1);
  EXPECT_EQ(slow_object_input.agent_trajectory_infos.size(), 1);
  EXPECT_FALSE(slow_reasoning_object.is_leading_agent());

  const AgentTrajectoryInfo& slow_agent_trajectory_info =
      slow_object_input.agent_trajectory_infos.front();

  EXPECT_TRUE(slow_agent_trajectory_info.EgoHasHigherRoadPrecedence());
  EXPECT_TRUE(slow_agent_trajectory_info.precedence_source() ==
              planner::pb::PrecedenceSource::Enum::
                  PrecedenceSource_Enum_KnownTrafficLight);
  EXPECT_FALSE(slow_agent_trajectory_info.has_overlap_region_in_xlane_nudge());
  EXPECT_TRUE(slow_agent_trajectory_info.consider_yield_intention());
  EXPECT_FALSE(
      slow_agent_trajectory_info.is_agent_extra_cautious_on_trajectory());

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  // 3-1 policy for lead and merge reasoner.
  EXPECT_EQ(1, decision.agent_policies().size());
  EXPECT_TRUE(decision.Nominal());

  const AgentPolicy& policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::LEAD_AND_MERGE, policy.reasoner_in_charge());
  EXPECT_FALSE(policy.HasSlowDown());
}

TEST_F(PredictionDecisionMakerTest, UPLIgnoreCrossAgentOverlapTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);

  SetEgoPose(/*lane_id=*/10523, /*portion=*/0.6, /*speed=*/2.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/14051, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/14051, /*start_portion=*/0.2,
                                 /*end_lane_id=*/14051, /*end_portion=*/1.0,
                                 /*likelihood=*/0.0, /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, object_proximity_infos().size());
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  const AgentTrajectoryInfo& agent_trajectory_info =
      input.agent_trajectory_infos.front();

  EXPECT_TRUE(agent_trajectory_info.overlap_region_info(0).is_in_left_turn);
  EXPECT_TRUE(reasoning_util::IsStrictOverlapRegionStartingBehind(
      agent_trajectory_info.overlap_region(0),
      world_context().ra_to_front_axle()));

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_EQ(0, decision.agent_policies().size());
  EXPECT_TRUE(decision.Ignore());
}

// This tests that when the object is ahead but the ObjectProximityInfo falsely
// thinks the agent is fully behind and we should not ignore the overlap.
TEST_F(PredictionDecisionMakerJunctionTest,
       ShouldNotIgnoreOverlapBasedOnProximityTest) {
  SetEgoPose(/*lane_id=*/10523, /*portion=*/0.6, /*speed=*/0.0);
  CreateLaneFollowPath();

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST,
               /*id=*/1, /*lane_id=*/10523, /*portion=*/0.8,
               /*length=*/2.0, /*width=*/0.8,
               /*velocity=*/0.0);

  AddStationaryPredictedTrajectory(/*lane_id=*/10523,
                                   /*portion=*/0.8,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();
  EXPECT_EQ(1, object_proximity_infos().size());

  // This is for cases that extended path is buggy and we have very wrong
  // projection of proximity.
  mutable_object_proximity_infos()[1]
      .mutable_projected_ra_arc_length()
      ->set_start(-10.0);
  mutable_object_proximity_infos()[1]
      .mutable_projected_ra_arc_length()
      ->set_end(-10.0);

  UpdateWrappedPredictionDecisions();
  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  const ReasoningObject& reasoning_object = input.reasoning_object;

  EXPECT_TRUE(reasoning_object.is_behind_ego_rear_axle());

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_FALSE(decision.Ignore());
}

TEST_F(PredictionDecisionMakerTest, ShouldNotIgnoreLowLikelihoodOverlapTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);

  SetEgoPose(/*lane_id=*/9141, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9141, 9157});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9143,
               /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/15.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/9143, /*start_portion=*/0.3,
                                 /*end_lane_id=*/9143, /*end_portion=*/0.9,
                                 /*likelihood=*/0.4, /*traj_id=*/1, agent);
  // Construct agent's predicted trajectories.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.3,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9143, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/9141, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.2, /*traj_id=*/2, agent);

  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  const PredictionDecisionMakerInput& input = reasoning_inputs().front();
  EXPECT_EQ(1, input.agent_trajectory_infos.size());
  const pb::OverlapRegion& overlap_region =
      input.agent_trajectory_infos.front().overlap_region(0);
  // The minimum relative time to ignore agents in front for cautious decison.
  constexpr int64_t kMinRelativeTimeToIgnoreAgentInCautiousDecisionInMSec =
      5000;
  EXPECT_GT(overlap_region.start_padded_relative_time_in_msec(),
            kMinRelativeTimeToIgnoreAgentInCautiousDecisionInMSec);

  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/2);

  EXPECT_TRUE(decision.CautiousDriving());
  EXPECT_EQ(1, decision.agent_policies().size());
}

TEST_F(PredictionDecisionMakerTest, ShouldNotIgnoreOutOfExtendPathVehicleTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152700, /*portion=*/0.1,
             /*speed=*/10.0);
  CreatePathWithLaneSequence({152700, 152693},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  int64_t object_id = 1;
  for (auto object_tpye :
       {voy::perception::ObjectType::VEHICLE, voy::perception::ObjectType::PED,
        voy::perception::ObjectType::CYCLIST,
        voy::perception::ObjectType::UNKNOWN}) {
    prediction::pb::Agent& agent =
        AddAgent(object_tpye, /*id=*/object_id, /*lane_id=*/152700,
                 /*portion=*/0.3, /*length=*/4.0, /*width=*/2.0,
                 /*velocity=*/1.0);
    AddStraightPredictedTrajectory(/*start_lane_id=*/152700,
                                   /*start_portion=*/0.3,
                                   /*end_lane_id=*/152700, /*end_portion=*/0.9,
                                   /*likelihood=*/0.4, /*traj_id=*/1, agent);
    object_id++;
  }
  EXPECT_EQ(4, agent_list().agent_list().size());

  Update();

  for (int id = 1; id < 5; id++) {
    speed::pb::ObjectProximityInfo& mutable_object_proximity_info =
        mutable_object_proximity_infos().at(id);
    mutable_object_proximity_info.set_out_of_path_range(true);
    EXPECT_TRUE(object_proximity_infos().at(id).out_of_path_range());
  }
  UpdateWrappedPredictionDecisions();

  for (int id = 1; id < 5; id++) {
    const PredictionDecision& decision =
        GetPredictionDecision(/*object_id=*/id, /*trajectory_id=*/1);
    EXPECT_TRUE(decision.Nominal());
    EXPECT_LT(0, decision.agent_policies().size());
  }
}

TEST_F(PredictionDecisionMakerTest,
       DoNotIgnorePartiallyOutOfExtendPathRangeTest) {
  // https://drive.google.com/file/d/1Grdfo6ujZ7nLzg9rYD9VoqSDoxXJQvrp/view?usp=sharing
  SetEgoPose(/*lane_id=*/16885, /*portion=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{16867, 108357, 16885},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16867, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/16867, /*start_portion=*/1.0,
                                 /*end_lane_id=*/16885, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_FALSE(object_proximity_infos().at(1).out_of_path_range());

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(decision.Nominal());
  EXPECT_EQ(decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy = decision.agent_policies().front();
  EXPECT_EQ(pb::ReasonerId::TAILGATER, agent_policy.reasoner_in_charge());
}

// https://cooper.didichuxing.com/shares/tqlEQJuTgfFp
TEST_F(PredictionDecisionMakerTest, DoNotIgnoreCyclistRightHookWhenPullOver) {
  SetEgoLane(/*current_lane_id=*/97149);
  SetEgoPose(/*x_pos=*/-4493.60, /*y_pos=*/-2552.06,
             /*yaw=*/2.06745, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(-4493.60 -2552.06,-4495.72 "
      "-2547.37,-4496.35 -2542.78,-4496.06 "
      "-2538.09,-4497.65 -2534.08)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {97149, 97153},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);
  SetBehaviorType(planner::pb::BehaviorType::DECOUPLED_PULL_OVER);
  SetPullOverDestination(/*portion=*/1.0, /*is_pull_over_jump_in=*/true);
  SetPullOverStatus(planner::pull_over::PullOverStatus(
      /*should_trigger_pull_over=*/true,
      /*execution_state=*/planner::pb::PullOverExecutionState::EXECUTE,
      /*pull_over_progress=*/planner::pb::PullOverProgress::kStarted,
      /*is_in_abort_recovery=*/false));

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4490.88, /*y_in=*/-2555.15, /*yaw_in=*/1.999105);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4498.99, /*y_in=*/-2537.94, /*yaw_in=*/1.999105);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/1.5, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_EQ(
      prediction_decision.agent_trajectory_info().overlap_regions().size(), 1);
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .overlap_region_info(0)
                  .is_in_encroachment_regions());
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .overlap_region_info(0)
                  .is_crossing_at_right_hook);
  EXPECT_TRUE(prediction_decision.Nominal());
  EXPECT_GE(prediction_decision.agent_policies().size(), 1);
}

// https://cooper.didichuxing.com/shares/2k0o6IvgIxUs
TEST_F(PredictionDecisionMakerTest, DoNotIgnoreCyclistLeftHookWhenPullOut) {
  SetEgoLane(/*current_lane_id=*/97149);
  SetEgoPose(/*x_pos=*/-4490.21, /*y_pos=*/-2550.09,
             /*yaw=*/2.06745, /*speed=*/0.0, /*accel=*/0.0);
  SetIsPullOutJumpOut(true);
  const std::string path_wkt =
      "LINESTRING(-4490.21 -2550.09,-4492.45 "
      "-2545.75,-4493.78 -2543.57,-4497.76 "
      "-2541.06,-4501.15 -2537.02)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {97149, 97153},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Add a cyclist overtaking Ego who is behind Ego.
  const math::Pose2d cyclist_start_pose_behind_ego(
      /*x_in=*/-4489.60, /*y_in=*/-2554.49, /*yaw_in=*/1.999105);
  const math::Pose2d cyclist_end_pose_behind_ego(
      /*x_in=*/-4498.37, /*y_in=*/-2535.53, /*yaw_in=*/1.999105);
  prediction::pb::Agent& agent_behind_ego =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1,
               cyclist_start_pose_behind_ego, /*length=*/1.5, /*width=*/0.7,
               /*velocity=*/7.0);
  AddStraightPredictedTrajectory(
      cyclist_start_pose_behind_ego, cyclist_end_pose_behind_ego,
      /*likelihood=*/0.9, /*traj_id=*/1, agent_behind_ego);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_EQ(
      prediction_decision.agent_trajectory_info().overlap_regions().size(), 1);
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .overlap_region_info(0)
                  .is_in_encroachment_regions());
  EXPECT_TRUE(prediction_decision.agent_trajectory_info()
                  .overlap_region_info(0)
                  .is_crossing_at_left_hook);
  EXPECT_TRUE(prediction_decision.Nominal());
  EXPECT_GE(prediction_decision.agent_policies().size(), 1);
}

TEST_F(PredictionDecisionMakerTest, StartToMoveAgentTest) {
  SetEgoPose(/*lane_id=*/16885, /*portion=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{16867, 108357, 16885},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d pose = LanePointToPose(/*lane_id=*/16885, /*portion=*/0.5);

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, pose,
               /*length=*/4.0, /*width=*/2.0, /*velocity=*/0.0);

  // Construct one stationary trajectory and one possible moving trajectory.
  AddStationaryPredictedTrajectory(/*pose=*/pose,
                                   /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      AddStartToMovePredictedTrajectory(/*start_lane_id=*/16885,
                                        /*start_portion=*/0.5,
                                        /*end_lane_id=*/16885,
                                        /*end_portion=*/1.0,
                                        /*likelihood=*/0.4,
                                        /*traj_id=*/2, agent);
  added_predicted_trajectory.set_is_multi_output_trajectory(true);

  ::prediction::pb::StationaryIntention* stationary_intention =
      agent.mutable_stationary_intention();
  stationary_intention->set_stationary_intention_type(
      prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE);
  stationary_intention->set_time_to_move(2.5);
  stationary_intention->set_standard_deviation_of_time_to_move(1.0);

  Update();

  EXPECT_EQ(2, object_prediction_map().at(1).size());

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_FALSE(object_proximity_infos().at(1).out_of_path_range());

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      2, wrapped_prediction_decisions().front().prediction_decisions().size());
  const PredictionDecision& primary_decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(primary_decision.Nominal());
  EXPECT_EQ(primary_decision.agent_policies().size(), 1);

  const PredictionDecision& start_to_move_decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/2);
  EXPECT_TRUE(start_to_move_decision.Nominal());
  EXPECT_EQ(start_to_move_decision.agent_policies().size(), 1);
}

TEST_F(PredictionDecisionMakerTest, StartToMoveAgentWithNonMultiOutputTest) {
  SetEgoPose(/*lane_id=*/16885, /*portion=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{16867, 108357, 16885},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/16885, /*portion=*/0.5,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/0.0);
  prediction::pb::StationaryIntention stationary_intention;
  stationary_intention.set_stationary_intention_type(
      prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE);
  stationary_intention.set_time_to_move(2.5);
  stationary_intention.set_standard_deviation_of_time_to_move(1.0);
  *(agent.mutable_stationary_intention()) = stationary_intention;

  // Construct one stationary trajectory and one non_multi_output possible
  // moving trajectory.
  AddStraightPredictedTrajectory(/*start_lane_id=*/16885, /*start_portion=*/0.5,
                                 /*end_lane_id=*/16885, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      AddStartToMovePredictedTrajectory(/*start_lane_id=*/16885,
                                        /*start_portion=*/0.5,
                                        /*end_lane_id=*/16885,
                                        /*end_portion=*/1.0,
                                        /*likelihood=*/0.4,
                                        /*traj_id=*/2, agent);
  added_predicted_trajectory.set_is_multi_output_trajectory(false);

  Update();

  EXPECT_EQ(2, object_prediction_map().at(1).size());

  EXPECT_EQ(1, object_proximity_infos().size());
  EXPECT_FALSE(object_proximity_infos().at(1).out_of_path_range());

  EXPECT_EQ(1, wrapped_prediction_decisions().size());
  EXPECT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());
  const PredictionDecision& primary_decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);
  EXPECT_TRUE(primary_decision.Nominal());
  EXPECT_EQ(primary_decision.agent_policies().size(), 1);
}

TEST_F(PredictionDecisionMakerTest, DoNotIgnoreYieldAlwaysPossibleAgent) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/10539, /*portion=*/0.4);
  CreateLaneFollowPath();
  const math::Pose2d start_pose(/*x_in=*/-8908.23, /*y_in=*/-33482.18,
                                /*yaw_in=*/3.12093);
  const math::Pose2d end_pose(/*x_in=*/-8913.84, /*y_in=*/-33469.84,
                              /*yaw_in=*/3.12093);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();

  EXPECT_EQ(
      prediction_decision.agent_trajectory_info().overlap_regions().size(), 2);
  EXPECT_FALSE(wrapped_prediction_decisions()
                   .front()
                   .reasoning_object()
                   .is_behind_ego_rear_axle());
  EXPECT_TRUE(prediction_decision.Nominal());
  EXPECT_GE(prediction_decision.agent_policies().size(), 1);
}

TEST_F(PredictionDecisionMakerTest,
       IgnoreYieldAlwaysPossibleAgentBehindRearAxle) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/10539, /*portion=*/0.4);
  CreateLaneFollowPath();
  const math::Pose2d start_pose(/*x_in=*/-8909.38, /*y_in=*/-33485.93,
                                /*yaw_in=*/0.92396);
  const math::Pose2d end_pose(/*x_in=*/-8901.79, /*y_in=*/-33475.88,
                              /*yaw_in=*/0.92396);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  ASSERT_EQ(
      wrapped_prediction_decisions().front().prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();

  EXPECT_EQ(
      prediction_decision.agent_trajectory_info().overlap_regions().size(), 1);
  EXPECT_TRUE(wrapped_prediction_decisions()
                  .front()
                  .reasoning_object()
                  .is_behind_ego_rear_axle());
  EXPECT_TRUE(prediction_decision.Ignore());
  EXPECT_EQ(prediction_decision.ignore_reason(),
            pb::IgnoreReason::UNREALISTIC_OVERLAP);
}

TEST_F(PredictionDecisionMakerTest,
       SetYieldAlwaysPossibleForCyclistCloseToEgoAroundCrosswalkTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/1.0, /*speed=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8932.12, /*y_in=*/-33481.08,
                                /*yaw_in=*/-1.53);
  const math::Pose2d end_pose(/*x_in=*/-8932.41, /*y_in=*/-33490.31,
                              /*yaw_in=*/-1.53);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_EQ(agent_policy.reasoner_in_charge(), pb::ReasonerId::VRU);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest,
       SetYieldAlwaysPossibleForJaywalkerCloseToEgoTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPoseWoYaw(/*x_pos=*/-8912.74, /*y_pos=*/-33478.47, /*lane_id=*/9099,
                  /*speed=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9099, 10993});

  const math::Pose2d start_pose(/*x_in=*/-8913.62, /*y_in=*/-33474.75,
                                /*yaw_in=*/-1.29);
  const math::Pose2d end_pose(/*x_in=*/-8910.50, /*y_in=*/-33485.72,
                              /*yaw_in=*/-1.29);
  prediction::pb::Agent& jaywalker =
      AddAgent(voy::perception::ObjectType::PED, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, jaywalker);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_EQ(agent_policy.reasoner_in_charge(), pb::ReasonerId::JAYWALKER);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest, SetYieldAlwaysPossibleForPullOutTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49376.83, /*y_pos=*/13557.06,
             /*yaw=*/1.5, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49376.83 13557.06,49372.99 13556.63,49369.23 "
      "13555.81,49365.24 13554.76,49359.86 13553.71,49352.77 13552.14)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  SetIsPullOutJumpOut(true);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841},
      planner::pb::ManeuverType::PULL_OUT);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/49376.98, /*y_in=*/13556.15,
                                /*yaw_in=*/0.01);
  const math::Pose2d end_pose(/*x_in=*/49361.59, /*y_in=*/13556.06,
                              /*yaw_in=*/0.01);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);
  Update();
  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_GE(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();

  EXPECT_EQ(agent_policy.reasoner_in_charge(), pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest, SetYieldAlwaysPossibleForPullOverTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49397.81, /*y_pos=*/13552.45,
             /*yaw=*/0.01, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49397.81 13552.45,49393.34 13552.20,49390.63 "
      "13553.59,49388.10 13554.41,49385.61 13555.64,49382.65 13556.72,49378.80 "
      "13557.22)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841});
  SetBehaviorType(planner::pb::BehaviorType::DECOUPLED_PULL_OVER);
  SetPullOverDestination(/*portion=*/1.0, /*is_pull_over_jump_in=*/true);
  SetPullOverStatus(planner::pull_over::PullOverStatus(
      /*should_trigger_pull_over=*/true,
      /*execution_state=*/planner::pb::PullOverExecutionState::EXECUTE,
      /*pull_over_progress=*/planner::pb::PullOverProgress::kStarted,
      /*is_in_abort_recovery=*/false));

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/49401.49, /*y_in=*/13553.23,
                                /*yaw_in=*/0.01);
  const math::Pose2d end_pose(/*x_in=*/49382.82, /*y_in=*/13553.53,
                              /*yaw_in=*/0.01);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);
  Update();
  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_GE(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();

  EXPECT_EQ(agent_policy.reasoner_in_charge(), pb::ReasonerId::LEAD_AND_MERGE);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

TEST_F(PredictionDecisionMakerTest, SetYieldAlwaysPossibleForCrossAgentTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/10539, /*portion=*/0.1);
  CreateLaneFollowPath();

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/103021, /*portion=*/0.2,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/103021,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/103021,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();
  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_GE(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();

  EXPECT_EQ(agent_policy.reasoner_in_charge(), pb::ReasonerId::CROSS_AGENT);
  EXPECT_TRUE(agent_policy.yield_always_possible());
}

// All settings are same as the above test
// |SetYieldAlwaysPossibleForCyclistCloseToEgoAroundCrosswalkTest|, expect the
// Ego speed. Ego speed is 10 m/s in this test, while it's zero in the above
// test.
TEST_F(PredictionDecisionMakerTest,
       IgnoreCyclistCloseToEgoAroundCrosswalkTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/1.0, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8932.12, /*y_in=*/-33481.08,
                                /*yaw_in=*/-1.53);
  const math::Pose2d end_pose(/*x_in=*/-8932.41, /*y_in=*/-33490.31,
                              /*yaw_in=*/-1.53);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Ignore());
  EXPECT_EQ(prediction_decision.ignore_reason(),
            pb::IgnoreReason::UNREALISTIC_OVERLAP);
}

TEST_F(PredictionDecisionMakerTest, DisableARForRunningPedestrian) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8913.652, /*y_in=*/-33472.779,
                                /*yaw_in=*/-1.325);
  const math::Pose2d end_pose(/*x_in=*/-8907.902, /*y_in=*/-33495.699,
                              /*yaw_in=*/-1.325);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::PED, /*id*/ 1, start_pose,
               /*length=*/1.0, /*width=*/1.0, /*velocity=*/2.3);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Nominal());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  EXPECT_FALSE(
      prediction_decision.agent_policies().front().HasAnyAgentReaction());
}

TEST_F(PredictionDecisionMakerTest, IgnoreLowConfidentObjectTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/0.5, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8913.652, /*y_in=*/-33472.779,
                                /*yaw_in=*/-1.325);
  const math::Pose2d end_pose(/*x_in=*/-8907.902, /*y_in=*/-33495.699,
                              /*yaw_in=*/-1.325);
  prediction::pb::Agent& ped =
      AddAgent(voy::perception::ObjectType::PED, /*id*/ 1, start_pose,
               /*length=*/1.0, /*width=*/1.0, /*velocity=*/2.3);
  ped.mutable_tracked_object()->add_attributes(
      voy::perception::Attribute::EARLY_PUBLISHED);

  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, ped);

  Update();

  ASSERT_EQ(wrapped_prediction_decisions().size(), 1);
  const PredictionDecisionMakerOutput& one_agent_decision =
      wrapped_prediction_decisions().front();
  EXPECT_EQ(one_agent_decision.reasoning_object().id(), 1);
  ASSERT_EQ(one_agent_decision.prediction_decisions().size(), 1);
  const PredictionDecision& prediction_decision =
      one_agent_decision.prediction_decisions().front();
  EXPECT_TRUE(prediction_decision.Ignore());
  EXPECT_EQ(prediction_decision.ignore_reason(),
            pb::IgnoreReason::LOW_CONFIDENCE_OBJECT);
}

// This test verifies that static objects with strict overlap behind the rear
// axle are ignored when the ego path is straight.
TEST_F(PredictionDecisionMakerTest,
       IgnoreStaticObjectBehindRearAxleOnStraightPathTest) {
  // Set up a straight path using a straight lane sequence
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49397.81, /*y_pos=*/13552.45,
             /*yaw=*/0.01, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49397.81 13552.45,49393.34 13552.45,49390.63 "
      "13552.45)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841});
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());
  // Add a stationary vehicle behind ego
  const math::Pose2d start_pose(/*x_pos=*/49400.21, /*y_pos=*/13552.45,
                                /*yaw_in=*/0.01);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, start_pose, /*length=*/4.0, /*width=*/0.2,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(start_pose, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();
  EXPECT_GE(overlap_regions().size(), 1);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_TRUE(decision.Ignore());
  EXPECT_TRUE(decision.agent_policies().empty());
  EXPECT_EQ(pb::IgnoreReason::UNREALISTIC_OVERLAP, decision.ignore_reason());
}

// This test verifies that static objects with strict overlap behind the rear
// axle are not ignored when the ego path is not straight.
TEST_F(PredictionDecisionMakerTest,
       DoNotIgnoreStaticObjectBehindRearAxleOnCurvedPathTest) {
  // Set up a straight path using a straight lane sequence
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49397.81, /*y_pos=*/13552.45,
             /*yaw=*/0.01, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49397.81 13552.45,49397.51 13552.15, 49397.21 "
      "13551.85, 49396.91, 13551.55)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841});
  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());
  // Add a stationary vehicle behind ego
  const math::Pose2d start_pose(/*x_pos=*/49400.21, /*y_pos=*/13552.45,
                                /*yaw_in=*/0.01);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, start_pose, /*length=*/4.0, /*width=*/0.2,
               /*velocity=*/0.0);
  AddStationaryPredictedTrajectory(start_pose, /*likelihood=*/0.9,
                                   /*traj_id=*/1, agent);
  Update();
  EXPECT_GE(overlap_regions().size(), 1);

  UpdateWrappedPredictionDecisions();
  const PredictionDecision& decision =
      GetPredictionDecision(/*object_id=*/1, /*trajectory_id=*/1);

  EXPECT_FALSE(decision.Ignore());
}

}  // namespace speed

}  // namespace planner
