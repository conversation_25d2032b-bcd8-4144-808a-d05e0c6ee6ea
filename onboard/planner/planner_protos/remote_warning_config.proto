syntax = "proto3";

package planner.pb;

import "voy_protos/remote_warning.proto";

// The config for remote warning signal tracker.
// Next Available ID: 6
message WarningSignalTrackerConfig {
  // The task meta about the warning signal.
  voy.remote_assist.RemoteWarningTaskMeta task_meta = 1;
  // The threshold about triggering warning signal.
  int64 trigger_cycle_cnt = 2;
  // The threshold about aborting warning signal.
  int64 abort_cycle_cnt = 3;
  // Enabled in remote warning signal request.
  bool is_enabled_request = 4;
  // Enabled aborting signal.
  bool is_enabled_abort = 5;
}

// The config for remote warning detector.
// Next Available ID: 2
message RemoteWarningDetectorConfig {
  // Configs for remote warning signal trackers.
  repeated WarningSignalTrackerConfig warning_tracker_configs = 1;
}