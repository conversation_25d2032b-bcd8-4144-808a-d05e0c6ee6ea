#include <stdio.h>
#include <stdint.h>
#include <stdbool.h>
#include <errno.h>
#include <sys/types.h>
#include <stdatomic.h>
#include <unistd.h>
#include <stdlib.h>
#include <fcntl.h>
#include <string.h>
#include <limits.h>
#include <sys/ioctl.h>
#include <sys/mman.h>

#include "ntb_lib.h"

/*******************************************************************************
 *  align with kernel start
 ******************************************************************************/
#define Q_STATUS_OK 		(0)
#define Q_STATUS_EMPTY 		(-1)
#define Q_STATUS_FULL 		(-2)
#define Q_STATUS_INVALID 	(-3)
#define Q_STATUS_INDEX_INVALID 	(-4)

#define NTB_INMW_DIR_MAP_OFFSET	0

#define NTB_MAX_QUEUE_LEN 	64
#define NTB_DEV_CNT 	3

#define NTB_DEV_ORIN1_ORIN2 	0
#define NTB_DEV_ORIN1_X86 	1
#define NTB_DEV_ORIN2_ORIN1 	0
#define NTB_DEV_ORIN2_X86 	1
#define NTB_DEV_X86_ORIN1 	0
#define NTB_DEV_X86_ORIN2 	1

#define NTB_IOCTL_MAGIC 'k'
#define NTB_IOCTL_TRIGGER_SEND _IOW(NTB_IOCTL_MAGIC, 1, struct ntb_msg)
#define NTB_IOCTL_GET_MAP_INFO _IOR(NTB_IOCTL_MAGIC, 2, struct ioctl_map_info)
#define NTB_IOCTL_GET_TX_INDEX _IOR(NTB_IOCTL_MAGIC, 3, u64)
#define NTB_IOCTL_RELEASE_RX_INDEX _IOW(NTB_IOCTL_MAGIC, 4, u64)
#define NTB_IOCTL_GET_RX_INDEX _IOR(NTB_IOCTL_MAGIC, 5, u64)

enum ntb_queue_idx {
	Q_RX,
	Q_TX,
	Q_NR
};

struct ioctl_map_info {
	u64 phy;
	u32 len;
	u32 chunk_size;
	u32 in_mw_len;
	u16 q_index;
	bool is_dir_map;
	/* make size of struct aligned to 8 bytes */
	u8 pad;
};

struct ntb_queue {
	u64 head;
	u64 tail;
	/* maximum of indexes in queue */
	u64 depth;
	u64 entry[NTB_MAX_QUEUE_LEN];
	/* protect multi-process queue operations */
	u32 lock;
	/* make size of struct aligned to 8 bytes */
	unsigned pad;
};

struct map_entry {
	u64 kernel_addr;
	u64 dma_addr;
	u64 phy_addr;
	u64 index;
	u64 len;
};

struct ntb_queue_info {
	struct ntb_queue work_q;
	struct ntb_queue free_q;
	u64 depth;
	struct map_entry entry[NTB_MAX_QUEUE_LEN];
};

struct ntb_user_qinfo {
	struct ntb_queue_info *q;
	struct ntb_msg msg[NTB_MAX_QUEUE_LEN];
};

struct ntb_device {
	struct ntb_user_qinfo user_q[Q_NR];
	int fd;
	int open_mode;
	u32 qinfo_len;
	u32 chunk_size;
	u32 in_mw_len;
	bool is_dir_map[Q_NR];
	char device_path[PATH_MAX];
};

/*******************************************************************************
 *  align with kernel end
 ******************************************************************************/

/**
 * Map table storing relationships between src_id, dst_id and ntb_dev_id.
 */
static const struct ntb_dev_match_table {
	enum ntb_host_id src;
	enum ntb_host_id dst;
	int dev_id;
} g_valid_match_table[] = {
	{
		.src = NTB_ORIN1,
		.dst = NTB_ORIN2,
		.dev_id = NTB_DEV_ORIN1_ORIN2
	},
	{
		.src = NTB_ORIN1,
		.dst = NTB_X86,
		.dev_id = NTB_DEV_ORIN1_X86
	},
	{
		.src = NTB_ORIN2,
		.dst = NTB_ORIN1,
		.dev_id = NTB_DEV_ORIN2_ORIN1
	},
	{
		.src = NTB_ORIN2,
		.dst = NTB_X86,
		.dev_id = NTB_DEV_ORIN2_X86
	},
	{
		.src = NTB_X86,
		.dst = NTB_ORIN1,
		.dev_id = NTB_DEV_X86_ORIN1
	},
	{
		.src = NTB_X86,
		.dst = NTB_ORIN2,
		.dev_id = NTB_DEV_X86_ORIN2
	}
};

static struct ntb_dev_ctl {
	atomic_int status;
	struct ntb_device *dev;
} ntb_dev_table[NTB_DEV_CNT] = {
	{ATOMIC_VAR_INIT(0), NULL},
	{ATOMIC_VAR_INIT(0), NULL},
	{ATOMIC_VAR_INIT(0), NULL}
};

/**
 * Get the src ntb dev id for local host by hostname
 *
 * Returns: A valid dev_id on sucess, -EINVAL on error.
 */
static int get_src_dev()
{
	char name[HOST_NAME_MAX] = {0};
	int len;
	int src_id;

	len = gethostname(name, HOST_NAME_MAX);
	if (len < 0) {
		fprintf(stderr, "Get hostname err, %d %s\n",errno,
			strerror(errno));
		return -EINVAL;
	}

	if (!strcmp(name, "gen4x86")) {
		src_id = NTB_X86;
	} else if (!strcmp(name, "gen4orin1")) {
		src_id = NTB_ORIN1;
	} else if (!strcmp(name, "gen4orin2")) {
		src_id = NTB_ORIN2;
	} else {
		printf("Hostname %s is invalid.\n", name);
		return -EINVAL;
	}

	printf("local hostname %s, src_id: %d\n", name, src_id);

	return src_id;
}

/**
 * Get device id by local and target id.
 *
 * @dst_id: target id
 * @dev_id: the pointer to device id
 *
 * Returns: 0 on success, -EINVAL on error.
 */
static inline int get_dev_id(int dst_id, int *dev_id)
{
	int len, i, src_id;

	/* get local id by hostname */
	src_id = get_src_dev();
	if (src_id == -EINVAL) {
		fprintf(stderr, "Get ntb host id fail!\n");
		return -EINVAL;
	} else if (src_id == dst_id) {
		fprintf(stderr, "ntb dst_id cannot be equal to src_id!\n");
		return -EINVAL;
	}

	/* get dev_id which connect src_id and dst_id */
	len = sizeof(g_valid_match_table) / sizeof(g_valid_match_table[0]);
	for (i = 0; i < len; i++) {
		if (src_id == g_valid_match_table[i].src &&
		    dst_id == g_valid_match_table[i].dst) {
			*dev_id = g_valid_match_table[i].dev_id;
			return 0;
		}
	}

	fprintf(stderr, "Invalid src_id:dst_id pair, src_id: %d, dst_id: %d\n",
		src_id, dst_id);
	return -EINVAL;
}

static inline bool is_valid_dev_id(int dev_id)
{
	int val;

	if (dev_id < 0 || dev_id >= NTB_DEV_CNT) {
		fprintf(stderr, "ntb dev_id %d is invalid!\n", dev_id);
		return false;
	}

	val = atomic_load(&ntb_dev_table[dev_id].status);
	if (!val) {
		fprintf(stderr, "ntb dev_id %d is not opened!\n", dev_id);
		return false;
	}

	return true;
}

static inline bool is_valid_ntb_msg(struct ntb_msg *const msg)
{
	if (msg == NULL) {
		fprintf(stderr, "ntb msg pointer is NULL!\n");
		return false;
	}
	if (msg->len > msg->size || !msg->len) {
		fprintf(stderr, "ntb msg size %u is invalid!\n", msg->len);
		return false;
	}

	return true;
}

static int ntb_init_queue_single(struct ntb_device *dev, int q_index)
{
	struct ioctl_map_info map_info = {0};
	struct ntb_queue_info *q;
	void *user;
	int i, j, ret;
	char *p;

	map_info.q_index = q_index;
	ret = ioctl(dev->fd, NTB_IOCTL_GET_MAP_INFO, &map_info);
	if (ret) {
		fprintf(stderr, "ioctl get ntb map info error! %d %s\n", errno,
			strerror(errno));
		return ret;
	}

	dev->is_dir_map[q_index] = map_info.is_dir_map;
	dev->chunk_size = map_info.chunk_size;
	dev->in_mw_len = map_info.in_mw_len;
	dev->qinfo_len = map_info.len;
	printf("ntb: q_index %d phy 0x%lx len %d\n", map_info.q_index,
		map_info.phy, map_info.len);

	q = mmap(NULL, dev->qinfo_len, PROT_READ|PROT_WRITE, MAP_SHARED,
		 dev->fd, map_info.phy);
	if (q == MAP_FAILED) {
		fprintf(stderr, "ntb_queue_info map failed, %d %s\n", errno,
			strerror(errno));
		return -EFAULT;
	}

	dev->user_q[q_index].q = q;

	if (dev->is_dir_map[q_index]) {
		user = mmap(NULL, dev->in_mw_len, PROT_READ|PROT_WRITE,
			    MAP_SHARED, dev->fd, NTB_INMW_DIR_MAP_OFFSET);
		if (user == MAP_FAILED) {
			fprintf(stderr, "ntb in_mw map failed, %d %s\n", errno,
				strerror(errno));
			goto map_failed;
		}
		p = (char *)user;
		for (i = 0; i < q->depth; i++) {
			/* The first chunk is used to store meta, so not map */
			p += dev->chunk_size;
			dev->user_q[q_index].msg[i].buf = (void *)p;
			dev->user_q[q_index].msg[i].size = dev->chunk_size;
		}
	} else {
		for(i = 0; i < q->depth; i++) {
			user = mmap(NULL, dev->chunk_size, PROT_READ|PROT_WRITE,
				    MAP_SHARED, dev->fd, q->entry[i].phy_addr);
			if (user == MAP_FAILED) {
				fprintf(stderr, "ntb ring %d map failed\n", i);
				goto map_failed;
			}
			dev->user_q[q_index].msg[i].buf = (void *)user;
			dev->user_q[q_index].msg[i].size = dev->chunk_size;
		}
	}

	return 0;

map_failed:
	if (!dev->is_dir_map[q_index]) {
		for (j = 0; j < i; j++) {
			munmap((void*)dev->user_q[q_index].msg[i].buf,
				dev->chunk_size);
			dev->user_q[q_index].msg[i].buf = NULL;
		}
	}
	dev->user_q[q_index].q = NULL;
	munmap(q, dev->qinfo_len);

	return -EFAULT;
}

static void ntb_deinit_queue_single(struct ntb_device *dev, int q_index)
{
	struct ntb_user_qinfo *user_q = &dev->user_q[q_index];
	struct ntb_queue_info *q = user_q->q;
	char *buf, *start_inmw;
	int i;

	if (dev->is_dir_map[q_index]) {
		buf = user_q->msg[0].buf;
		if (buf) {
			start_inmw = buf - dev->chunk_size;
			munmap((void *)start_inmw, dev->in_mw_len);
			for (i = 0; i < q->depth; i++)
				user_q->msg[i].buf = NULL;
		}
	} else {
		for(i = 0; i < q->depth; i++) {
			if (!user_q->msg[i].buf)
				continue;
			munmap((void*)user_q->msg[i].buf, dev->chunk_size);
			user_q->msg[i].buf = NULL;
		}
	}

	munmap(q, dev->qinfo_len);
	user_q->q = NULL;
}

static inline int ntb_init_queue(struct ntb_device *dev, mode_t mode)
{
	int ret;

	switch (mode) {
	case O_RDONLY:
		ret = ntb_init_queue_single(dev, Q_RX);
		if (ret) {
			fprintf(stderr, "failed to init rx queue\n");
			return ret;
		}
		break;
	case O_WRONLY:
		ret = ntb_init_queue_single(dev, Q_TX);
		if (ret) {
			fprintf(stderr, "failed to init tx queue\n");
			return ret;
		}
		break;
	case O_RDWR:
		ret = ntb_init_queue_single(dev, Q_RX);
		if (ret) {
			fprintf(stderr, "failed to init rx queue\n");
			return ret;
		}
		ret = ntb_init_queue_single(dev, Q_TX);
		if (ret) {
			ntb_deinit_queue_single(dev, Q_RX);
			fprintf(stderr, "failed to init tx queue\n");
			return ret;
		}
		break;
	default:
		fprintf(stderr, "mode %d is invalid!\n", mode);
		return -EINVAL;
	}

	dev->open_mode = mode;

	return 0;
}

static void ntb_deinit_queue(struct ntb_device *dev)
{
	switch (dev->open_mode) {
	case O_RDONLY:
		ntb_deinit_queue_single(dev, Q_RX);
		break;
	case O_WRONLY:
		ntb_deinit_queue_single(dev, Q_TX);
		break;
	case O_RDWR:
		ntb_deinit_queue_single(dev, Q_RX);
		ntb_deinit_queue_single(dev, Q_TX);
		break;
	default:
		fprintf(stderr, "mode %d is invalid!\n", dev->open_mode);
		return;
	}
}

int ntb_open(enum ntb_host_id dst_id, mode_t mode)
{
	char device_path[PATH_MAX] = { 0 };
	struct ntb_device *dev;
	int dev_id, ret, i, j, dev_len;
	int zero = 0;

	if (get_dev_id(dst_id, &dev_id))
		return -EINVAL;

	snprintf(device_path, sizeof(device_path), "/dev/ntb_dev%d", dev_id);
	if (!atomic_compare_exchange_strong(&ntb_dev_table[dev_id].status,
					    &zero, 1)) {
		fprintf(stderr, "%s has be already opened\n", device_path);
		return -EBUSY;
	}

	dev_len = sizeof(struct ntb_device);
	dev = (struct ntb_device *)malloc(dev_len);
	if (dev == NULL) {
		fprintf(stderr, "ntb meta_info alloc fail!\n");
		ret = -ENOMEM;
		goto clean_status;
	}
	memset((char *)dev, 0, dev_len);

	strncpy(dev->device_path, device_path, sizeof(dev->device_path));
	/*
	 * init each msg index, kernel need to get map_entry index to
	 * be used by msg->index.
	 */
	for (i = 0; i < NTB_MAX_QUEUE_LEN; i++) {
		for (j = 0; j < Q_NR; j++)
			dev->user_q[j].msg[i].index = i;
	}

	ret = open(device_path, O_RDWR);
	if (ret < 0) {
		fprintf(stderr, "ntb device %s open fail!\n", device_path);
		goto free_dev;
	}
	dev->fd = ret;

	ret = ntb_init_queue(dev, mode);
	if (ret) {
		fprintf(stderr, "%s init queue fail!\n", device_path);
		goto close_fd;
	}

	printf("open %s success\n", dev->device_path);
	ntb_dev_table[dev_id].dev = dev;
	return dev_id;

close_fd:
	close(dev->fd);
free_dev:
	free(dev);
clean_status:
	atomic_store(&ntb_dev_table[dev_id].status, 0);
	return ret;
}

void ntb_close(int dev_id)
{
	int one = 1;
	struct ntb_device *dev;

	if (dev_id < 0 || dev_id >= NTB_DEV_CNT) {
		fprintf(stderr, "ntb dev_id %d is invalid!\n", dev_id);
		return;
	}

	if (!atomic_compare_exchange_strong(&ntb_dev_table[dev_id].status,
					    &one, 0)) {
		fprintf(stderr, "ntb dev %d does not be opened\n", dev_id);
		return;
	}

	dev = ntb_dev_table[dev_id].dev;
	ntb_deinit_queue(dev);

	close(dev->fd);
	printf("close %s success\n", dev->device_path);
	free(dev);
	ntb_dev_table[dev_id].dev = NULL;
}

/**
 * ntb_tx_mem_alloc - alloc a new buffer to send data
 *
 * @dev_id: device id
 *
 * Returns:
 *   NULL on error or buffer unavailable, please check the errno,
 *   if errno is NTBE_AGAIN, please try again later.
 *
 *   A valid pointer to a ntb_msg structure;
 */
struct ntb_msg *ntb_tx_mem_alloc(int dev_id)
{
	struct ntb_device *dev;
	struct ntb_msg *msg = NULL;
	s64 index, left = 0, right;

	if (!is_valid_dev_id(dev_id)) {
		fprintf(stderr, "Parameters of %s are invalid!\n", __func__);
		errno = NTBE_ERR;
		return NULL;
	}

	dev = ntb_dev_table[dev_id].dev;
	right = dev->user_q[Q_TX].q->depth;

	if (ioctl(dev->fd, NTB_IOCTL_GET_TX_INDEX, &index)) {
		fprintf(stderr, "ntb ioctl get tx mem error, %d %s\n", errno,
			strerror(errno));
		errno = NTBE_ERR;
		return NULL;
	}

	/* if no available queue entry, try again later */
	if (index == Q_STATUS_EMPTY) {
		errno = NTBE_AGAIN;
		return NULL;
	}

	if (index < left || index >= right) {
		errno = NTBE_ERR;
		return NULL;
	}

	msg = &dev->user_q[Q_TX].msg[index];
	/* restore msg len to default zero, user can change it. */
	msg->len = 0;

	return msg;
}

int ntb_send(int dev_id, struct ntb_msg *const msg)
{
	struct ntb_device *dev;
	s64 index, left = 0, right;
	int ret;

	if (!is_valid_dev_id(dev_id) || !is_valid_ntb_msg(msg)) {
		fprintf(stderr, "Parameters of %s are invalid!\n", __func__);
		return -EINVAL;
	}

	dev = ntb_dev_table[dev_id].dev;
	right = dev->user_q[Q_TX].q->depth;

	index = msg->index;
	if (index < left || index >= right) {
		fprintf(stderr, "ntb send msg index %ld is invalid\n", index);
		return -EINVAL;
	}

	ret = ioctl(dev->fd, NTB_IOCTL_TRIGGER_SEND, msg);
	if (ret) {
		fprintf(stderr, "ntb ioctl send error, %d %s\n", errno,
			strerror(errno));
		return ret;
	}

	return 0;
}

struct ntb_msg *ntb_recv(int dev_id)
{
	struct ntb_msg *msg = NULL;
	s64 index = Q_STATUS_EMPTY, left = 0, right;
	int ret;
	struct ntb_device *dev;

	if (!is_valid_dev_id(dev_id)) {
		fprintf(stderr, "Parameters of %s are invalid!\n", __func__);
		return NULL;
	}

	dev = ntb_dev_table[dev_id].dev;
	right = dev->user_q[Q_RX].q->depth;

	while (index == Q_STATUS_EMPTY) {
		ret = ioctl(dev->fd, NTB_IOCTL_GET_RX_INDEX, &index);
		if (ret) {
			fprintf(stderr, "ntb ioctl get rx index error, %d %s\n",
				errno, strerror(errno));
			return NULL;
		}
	}

	if (index < left || index >= right) {
		fprintf(stderr, "ntb recv index %ld is invalid!\n", index);
		return NULL;
	}
	msg = &dev->user_q[Q_RX].msg[index];
	/* set msg len equal to rx buffer len, which is set by sender */
	msg->len = dev->user_q[Q_RX].q->entry[index].len;

	return msg;
}

int ntb_rx_mem_free(int dev_id, struct ntb_msg *const msg)
{
	s64 index, left = 0, right;
	int ret;
	struct ntb_device *dev;

	if (!is_valid_dev_id(dev_id) || !is_valid_ntb_msg(msg)) {
		fprintf(stderr, "Parameters of %s are invalid!\n", __func__);
		return -EINVAL;
	}

	dev = ntb_dev_table[dev_id].dev;
	right = dev->user_q[Q_RX].q->depth;

	index = msg->index;
	if (index < left || index >= right) {
		fprintf(stderr, "ntb recv index %ld is invalid!\n", index);
		return -EINVAL;
	}
	/* Reset msg len to default zero */
	msg->len = 0;

	ret = ioctl(dev->fd, NTB_IOCTL_RELEASE_RX_INDEX, &msg->index);
	if (ret)
		fprintf(stderr, "Release ntb rx index fail! %d %s\n", errno,
			strerror(errno));
	return ret;
}