#include "planner/path/reasoning/agent_intention/reasoner/dynamic_overtake_path_reasoner/dynamic_overtake_path_reasoner.h"

#include <algorithm>
#include <memory>
#include <optional>
#include <string>
#include <utility>
#include <vector>

#include "geometry/model/polygon_with_cache.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/sequence_blocking_state_computer.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/scenario_identifier_util.h"
#include "planner/planning_gflags.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "voy_protos/telemetry.pb.h"

namespace planner {
namespace path {

namespace {
// The default spatial buffer for the dynamic cyclist which ego is xlane nudging
// in meter.
constexpr double kXLaneNudgeDynamicAgentSpatialBufferForYieldInMeter = 20.0;
constexpr double kXLaneNudgeDynamicAgentSpatialBufferForPassInMeter = 10.0;
constexpr double kMinTemporalBufferInSecond = 0.3;
constexpr double kMaxTemporalBufferInSecond = 5.0;
// The default comfortable lateral acceleration for dynamic overtaking.
constexpr double kComfortLateralAccelerationForEgoInMpss = 0.3;
// The minimum required clearance to start nudging in meters.
constexpr double kMinRequiredClearanceToStartNudgeInM = 0.5;
// The minimum length to add repulsion in meters.
constexpr double kMinLengthToAddRepulsion = 2.0;

// Returns true if current scenario is xlane nudge and the object is the target
// dynamic nudge object.
bool IsDynamicOvertakeTargetAgent(
    const ScenarioIdentifierResult& scenario_identify_result,
    const TypedObjectId& object_id, pb::BehaviorType behavior_type) {
  if (behavior_type != pb::BehaviorType::LANE_KEEP ||
      scenario_identify_result.scenario_type !=
          pb::ScenarioRecognition::XLANE_NUDGE) {
    return false;
  }

  DCHECK(scenario_identify_result.xlane_nudge_scene.has_value());
  // Only invoke pre reason for dynamic nudge at first.
  if (!scenario_identify_result.xlane_nudge_scene->is_overtake_dynamic_agent) {
    return false;
  }

  const pb::XLaneNudgeSceneType scene_type =
      scenario_identify_result.xlane_nudge_scene->type;
  DCHECK(scene_type == pb::XLaneNudgeSceneType::kCrossLeft ||
         scene_type == pb::XLaneNudgeSceneType::kCrossRight);
  const std::vector<TypedObjectId>& nudge_object_ids =
      scene_type == pb::XLaneNudgeSceneType::kCrossLeft
          ? scenario_identify_result.inlane_nudge_objects_info
                .pass_left_object_ids
          : scenario_identify_result.inlane_nudge_objects_info
                .pass_right_object_ids;
  return std::any_of(nudge_object_ids.begin(), nudge_object_ids.end(),
                     [&object_id](const TypedObjectId& typed_object_id) {
                       return object_id == typed_object_id;
                     });
}
}  // namespace

DynamicOvertakePathReasoner::DynamicOvertakePathReasoner(
    const EgoContextInfo& ego_context_info)
    : AbstractReasoner(ego_context_info, pb::AgentReasonerId::DYNAMIC_OVERTAKE),
      sequence_blocking_state_computer_(
          drivable_space_corridor_, ego_context_info.nominal_path,
          drivable_space_corridor_contour_, ego_param_,
          /*lateral_clearance_buffer*/
          kComfortRequiredLateralGapForOvertakeInMeter) {}

bool DynamicOvertakePathReasoner::ShouldInvokePreReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const AgentInLaneStates& agent_inlane_state,
    pb::BehaviorType /*behavior_type*/, bool /*is_stationary*/) const {
  const TypedObjectId object_id(agent_inlane_state.object_id,
                                pb::ObjectSourceType::kTrackedObject);
  return FLAGS_planning_enable_xlane_nudge_dynamic_agent_v2 &&
         !blocking_state_meta_data.reasoning_info.is_oncoming() &&
         IsDynamicOvertakeTargetAgent(scenario_identify_result_, object_id,
                                      behavior_type_);
}

bool DynamicOvertakePathReasoner::ShouldInvokePreReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const ObjectOccupancyState& object_occupancy_state,
    pb::BehaviorType /*behavior_type*/, bool /*is_stationary*/) const {
  const TypedObjectId object_id(object_occupancy_state.object_id(),
                                pb::ObjectSourceType::kTrackedObject);
  return FLAGS_planning_enable_xlane_nudge_dynamic_agent_v2 &&
         !blocking_state_meta_data.reasoning_info.is_oncoming() &&
         IsDynamicOvertakeTargetAgent(scenario_identify_result_, object_id,
                                      behavior_type_);
}

bool DynamicOvertakePathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /*object_sl_boundary*/,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // Record current reasoner id.
  trajectory_intention_data.reasoner_id = reasoner_id_;

  // Recompute agent inlane state based on the extended drivable space.
  const AgentBlockingSequenceInfo recomputed_blocking_sequence_info =
      sequence_blocking_state_computer_.Compute(agent_inlane_state,
                                                blocking_state_data);

  // Update blocking sequence, sequence blocking type, and the s-t region buffer
  // for the effective range of the agent trajectory.
  PreReasoningForBlockingSequence(recomputed_blocking_sequence_info,
                                  trajectory_intention_data);

  return true;
}

bool DynamicOvertakePathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /*object_sl_boundary*/,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // Record current reasoner id.
  trajectory_intention_data.reasoner_id = reasoner_id_;

  // Recompute agent occupancy state based on the extended drivable space.
  const AgentBlockingSequenceInfo recomputed_blocking_sequence_info =
      sequence_blocking_state_computer_.Compute(object_occupancy_state,
                                                blocking_state_data);

  // Update blocking sequence, sequence blocking type, and the s-t region buffer
  // for the effective range of the agent trajectory.
  PreReasoningForBlockingSequence(recomputed_blocking_sequence_info,
                                  trajectory_intention_data);

  return true;
}

void DynamicOvertakePathReasoner::PreReasoningForBlockingSequence(
    const AgentBlockingSequenceInfo& recomputed_blocking_sequence_info,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // Set the agent trajectory intention type.
  switch (recomputed_blocking_sequence_info.type) {
    case pb::BlockingSequenceType::kNonBlocking:
      trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
      break;
    case pb::BlockingSequenceType::kPartialBlocking:
      trajectory_intention_data.intention_type = pb::TrajectoryState::PASS;
      break;
    case pb::BlockingSequenceType::kCompleteBlocking:
    case pb::BlockingSequenceType::kMixedBlocking:
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      break;
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
      break;
  }

  // Set the agent sequence blocking type and blocking sequences.
  trajectory_intention_data.blocking_sequence_type =
      recomputed_blocking_sequence_info.type;
  trajectory_intention_data.blocking_sequences =
      recomputed_blocking_sequence_info.blocking_sequences;

  // Enlarge s-t region buffer for target agent. The top, bottom, left and right
  // refer to the position in the ST sampling plot.
  // TODO(PathReasoning): consider to rename those fields to
  // 'spatial_pass_buffer_m', 'spatial_yield_buffer_m',
  // 'temporal_pass_buffer_s', 'temporal_yield_buffer_s' in 25Q2 Fix-it.
  const double start_overtake_time_buffer_s =
      math::NearZero(ego_param_.speed_mps)
          ? kMinTemporalBufferInSecond
          : kXLaneNudgeDynamicAgentSpatialBufferForYieldInMeter /
                ego_param_.speed_mps;
  const double end_overtake_time_buffer_s =
      math::NearZero(ego_param_.speed_mps)
          ? kMinTemporalBufferInSecond
          : kXLaneNudgeDynamicAgentSpatialBufferForPassInMeter /
                ego_param_.speed_mps;
  for (auto& blocking_sequence : trajectory_intention_data.blocking_sequences) {
    pb::ObstacleSTBufferConfig* buffer_config =
        blocking_sequence.mutable_buffer_config();
    buffer_config->set_spatial_bottom_buffer_m(
        kXLaneNudgeDynamicAgentSpatialBufferForYieldInMeter);
    buffer_config->set_spatial_top_buffer_m(
        kXLaneNudgeDynamicAgentSpatialBufferForPassInMeter);
    buffer_config->set_temporal_left_buffer_s(
        math::Clamp(start_overtake_time_buffer_s, kMinTemporalBufferInSecond,
                    kMaxTemporalBufferInSecond));
    buffer_config->set_temporal_right_buffer_s(
        math::Clamp(end_overtake_time_buffer_s, kMinTemporalBufferInSecond,
                    kMaxTemporalBufferInSecond));
  }
}

bool DynamicOvertakePathReasoner::ShouldInvokePreRepulsionReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    voy::perception::ObjectType /*agent_type*/, pb::BehaviorType behavior_type,
    bool /*is_stationary*/,
    pb::AgentImaginaryActualType /*imaginary_actual_type*/,
    std::string& /*debug_str*/) const {
  const TypedObjectId object_id(blocking_state_meta_data.object_id,
                                pb::ObjectSourceType::kTrackedObject);
  return FLAGS_planning_enable_xlane_nudge_dynamic_agent_v2 &&
         !blocking_state_meta_data.reasoning_info.is_oncoming() &&
         IsDynamicOvertakeTargetAgent(scenario_identify_result_, object_id,
                                      behavior_type);
}

std::unique_ptr<RepulsionMetaData>
DynamicOvertakePathReasoner::PreRepulsionReason(
    const AgentInLaneStates& agent_inlane_state,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    const LateralBlockingStateMetaData& blocking_state_data,
    std::string& debug_str) const {
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
  const std::optional<XLaneNudgeScene>& xlane_nudge_scene =
      scenario_identify_result_.xlane_nudge_scene;
  DCHECK(xlane_nudge_scene->is_overtake_dynamic_agent);
  DCHECK(xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft ||
         xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossRight);
  // Ignore behind agent.
  if (traffic_rule_reasoning_info_.is_ego_in_lane() &&
      IsIgnorableBehindAgent(blocking_state_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    absl::StrAppend(&debug_str, "Ignorable behind agent");
    return nullptr;
  }

  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  if (trajectory_intention_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "have no effective snapshots");
    return nullptr;
  }

  // Get repulsion direction based on the cross side.
  const RepulsionMetaData::RepulsionDirection repulsion_direction =
      xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft
          ? RepulsionMetaData::RepulsionDirection::kLeft
          : RepulsionMetaData::RepulsionDirection::kRight;
  const pb::SnapshotIntention::PassState pass_decision =
      xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft
          ? pb::SnapshotIntention::PASS_LEFT
          : pb::SnapshotIntention::PASS_RIGHT;

  // Estimate the comfort nudge range based on the soft blocking range of the
  // trajectory.
  DCHECK(!agent_inlane_state.predicted_trajectories.empty());
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  DCHECK(!predicted_states.empty());
  const math::Range<int> estimated_nudge_range = EstimateComfortNudgeRange(
      trajectory_intention_data.blocking_sequences, predicted_states,
      blocking_state_data.effective_intention_index_ranges, pass_decision);
  absl::StrAppend(&debug_str, "estimated nudge range: [",
                  estimated_nudge_range.start_pos, ", ",
                  estimated_nudge_range.end_pos, "],");
  if (!math::IsValidRange(estimated_nudge_range)) {
    absl::StrAppend(&debug_str, "Invalid estimated nudge range,");
    return nullptr;
  }

  // Construct the agent trajectory for repulsion.
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(
        state.inlane_state.inlane_param.pose.center_2d());
  }

  for (int index = estimated_nudge_range.start_pos;
       index <= estimated_nudge_range.end_pos; ++index) {
    speed_profile.emplace_back(
        math::Ms2Sec(predicted_states[index].timestamp),
        predicted_states[index].odom_m,
        predicted_states[index].inlane_state.inlane_param.along_track_speed_mps,
        /*a_in=*/0.0, /*j_in=*/0.0);
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return nullptr;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return nullptr;
  }

  // Add dynamic overtake repulsion.
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  return std::make_unique<RepulsionMetaData>(
      repulsion_direction, tracked_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile, tracked_param.pose.heading(),
      /*add_only_overtaking=*/true,
      /*ignore_if_nudge_failed_in=*/true,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/1.0,
      /*repulsion_required_lat_gap=*/
      kComfortRequiredLateralGapForOvertakeInMeter, reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      agent_inlane_state.primary_trajectory_id,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kCustomized,
      /*customized_guide_profile_in=*/upper_bound_speed_,
      RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      /*clearance_in=*/std::make_optional(ego_param_.width_m),
      /*max_extend_boundary_distance_in=*/
      std::make_optional(ego_param_.width_m),
      /*st_buffer_in=*/
      trajectory_intention_data.blocking_sequences.front().buffer_config());
}

bool DynamicOvertakePathReasoner::ShouldInvokePostReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    voy::perception::ObjectType /*agent_type*/, pb::BehaviorType behavior_type,
    bool /*is_stationary*/) const {
  const TypedObjectId object_id(blocking_state_meta_data.object_id,
                                pb::ObjectSourceType::kTrackedObject);
  return FLAGS_planning_enable_xlane_nudge_dynamic_agent_v2 &&
         !blocking_state_meta_data.reasoning_info.is_oncoming() &&
         IsDynamicOvertakeTargetAgent(scenario_identify_result_, object_id,
                                      behavior_type);
}

bool DynamicOvertakePathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.longitudinal_decision ==
          speed::pb::SpeedDecision::PASS) {
    AdjustIntentionResultForPassDecision(agent_inlane_state,
                                         trajectory_intention_data,
                                         intention_result.lateral_decision,
                                         blocking_state_data, intention_result);
  }
  intention_result.is_overtaken =
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.longitudinal_decision == speed::pb::SpeedDecision::PASS;
  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

void DynamicOvertakePathReasoner::AdjustIntentionResultForPassDecision(
    const AgentInLaneStates& agent_inlane_state,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    const LateralBlockingStateMetaData& blocking_state_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.nudge_index_ranges.empty());
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  // Increase required lateral gap.
  math::UpdateMax(
      kComfortRequiredLateralGapForOvertakeInMeter,
      intention_result.required_lat_gap.comfort_required_lateral_gap);

  DCHECK(!agent_inlane_state.predicted_trajectories.empty());
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  DCHECK(!predicted_states.empty());

  // Adjust the start pos of the nudge range so that ego path can be early
  // pushed to the side and ego can gain more FOV and do acceleration for
  // overtake.
  absl::StrAppend(&intention_result.debug_str, "Original start nudge pos: ",
                  intention_result.nudge_index_ranges.front().start_pos);
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      trajectory_intention_data.blocking_sequences;
  const int start_nudge_idx = GetFirstNudgeSnapshotIndex(
      intention_result.nudge_index_ranges.front().start_pos,
      tracked_pass_decision, blocking_sequences,
      blocking_state_data.effective_intention_index_ranges, predicted_states);
  const int end_nudge_idx = intention_result.nudge_index_ranges.back().end_pos;
  if (start_nudge_idx !=
          intention_result.nudge_index_ranges.front().start_pos ||
      end_nudge_idx != intention_result.nudge_index_ranges.back().end_pos) {
    absl::StrAppend(&intention_result.debug_str, "Adjusted to ",
                    start_nudge_idx);
  }
  intention_result.nudge_index_ranges.clear();
  intention_result.nudge_index_ranges.emplace_back(start_nudge_idx,
                                                   end_nudge_idx);
}

int DynamicOvertakePathReasoner::GetFirstNudgeSnapshotIndex(
    int original_start_pos, pb::SnapshotIntention::PassState pass_decision,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const std::vector<math::Range<int>>& effective_ranges,
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states)
    const {
  const double left_side_desired_l_extra_offset =
      ego_param_.width_m * 0.5 + kComfortRequiredLateralGapForOvertakeInMeter;
  const double right_side_desired_l_extra_offset =
      ego_param_.width_m * 0.5 - kComfortRequiredLateralGapForOvertakeInMeter;
  int nudge_idx = original_start_pos;
  for (; nudge_idx > 0; --nudge_idx) {
    const auto& snapshot_param =
        predicted_states[nudge_idx].inlane_state.inlane_param;
    const double desired_l =
        pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? (snapshot_param.full_body_end_lateral_distance_m +
               left_side_desired_l_extra_offset)
            : (snapshot_param.full_body_start_lateral_distance_m -
               right_side_desired_l_extra_offset);
    // Return previous nudge idx if it's not comfortable to nudge this snapshot.
    if (nudge_motion_checker()
            .CalculateDesiredLateralAccelerationWithinKinematicLimit(
                /*desired_s=*/snapshot_param.start_arclength_m, desired_l) >
        kComfortLateralAccelerationForEgoInMpss) {
      return nudge_idx + 1;
    }

    // Return previous nudge idx if it's hard blocking which is better to yield.
    if (IsHardBlockingSnapshot(blocking_sequences, predicted_states[nudge_idx],
                               effective_ranges, nudge_idx)) {
      return nudge_idx + 1;
    }
  }
  return nudge_idx;
}

bool DynamicOvertakePathReasoner::IsHardBlockingSnapshot(
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const StampedAgentSnapshotInLaneState& snapshot_param,
    const std::vector<math::Range<int>>& effective_ranges, int index) const {
  DCHECK(!effective_ranges.empty());
  // Check if it's in any recomputed blocking sequences.
  const auto sequence_iter =
      std::find_if(blocking_sequences.begin(), blocking_sequences.end(),
                   [&index](const pb::BlockingSequence& seq) {
                     return math::IsInRange(index, seq.range().start_index(),
                                            seq.range().end_index());
                   });
  if (sequence_iter != blocking_sequences.end()) {
    return sequence_iter->blocking_state() ==
           pb::LateralBlockingState::HARD_BLOCKING;
  }

  // If not find the corresponding sequence, this snapshot could be
  // non-blocking, or it's out of the effective range.
  // If it's in effective range, it's should be non-blocking and return false
  // directly.
  if (math::IsInRange(index, effective_ranges.front().start_pos,
                      effective_ranges.back().end_pos)) {
    return false;
  }

  // If it's original blocking state is not hard blocking, return true;
  if (snapshot_param.inlane_state.blocking_info.blocking_state !=
      pb::AgentSnapshotBlockingState::HARD_BLOCKING) {
    return false;
  }

  // Otherwise, recompute its blocking state in the drivable space.
  const math::geometry::PolygonWithCache2d& snapshot_polygon =
      snapshot_param.inlane_state.inlane_param.pose.contour();
  return pb::LateralBlockingState::HARD_BLOCKING ==
         sequence_blocking_state_computer_
             .GetClearanceBasedBlockingStateForSnapshot(snapshot_polygon);
}

bool DynamicOvertakePathReasoner::IsHardBlockingSnapshot(
    const ObjectOccupancyState& object_occupancy_state,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::LateralBlockingState::BlockingState
        current_snapshot_blocking_state,
    const std::vector<math::Range<int>>& effective_ranges, int index) const {
  DCHECK(!effective_ranges.empty());
  // Check if it's in any recomputed blocking sequences.
  const auto sequence_iter =
      std::find_if(blocking_sequences.begin(), blocking_sequences.end(),
                   [&index](const pb::BlockingSequence& seq) {
                     return math::IsInRange(index, seq.range().start_index(),
                                            seq.range().end_index());
                   });
  if (sequence_iter != blocking_sequences.end()) {
    return sequence_iter->blocking_state() ==
           pb::LateralBlockingState::HARD_BLOCKING;
  }

  // If not find the corresponding sequence, this snapshot could be
  // non-blocking, or it's out of the effective range.
  // If it's in effective range, it's should be non-blocking and return false
  // directly.
  if (math::IsInRange(index, effective_ranges.front().start_pos,
                      effective_ranges.back().end_pos)) {
    return false;
  }

  // If it's original blocking state is not hard blocking, return true;
  if (current_snapshot_blocking_state !=
      pb::LateralBlockingState::HARD_BLOCKING) {
    return false;
  }

  // Otherwise, recompute its blocking state in the drivable space.
  const math::geometry::PolygonWithCache2d& snapshot_polygon =
      object_occupancy_state.pose().contour();
  return pb::LateralBlockingState::HARD_BLOCKING ==
         sequence_blocking_state_computer_
             .GetClearanceBasedBlockingStateForSnapshot(snapshot_polygon);
}

math::Range<int> DynamicOvertakePathReasoner::EstimateComfortNudgeRange(
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const std::vector<math::Range<int>>& effective_ranges,
    pb::SnapshotIntention::PassState pass_decision) const {
  // Find the first soft blocking sequence at first.
  auto first_soft_block_seq_iter = std::find_if(
      blocking_sequences.begin(), blocking_sequences.end(),
      [](const auto& seq) {
        return seq.blocking_state() == pb::LateralBlockingState::SOFT_BLOCKING;
      });
  if (first_soft_block_seq_iter == blocking_sequences.end()) {
    return {0, 0};
  }

  // Estimate the first comfortable nudge snapshot.
  const int estimated_earliest_nudge_start_pos = GetFirstNudgeSnapshotIndex(
      first_soft_block_seq_iter->range().start_index(), pass_decision,
      blocking_sequences, effective_ranges, predicted_states);

  // Start from the first nudge snapshot, find the last soft blocking snapshot
  // before any hard blocking sequence.
  int estimate_last_nudge_pos = estimated_earliest_nudge_start_pos;
  while (first_soft_block_seq_iter != blocking_sequences.end()) {
    if (first_soft_block_seq_iter->blocking_state() ==
        pb::LateralBlockingState::HARD_BLOCKING) {
      break;
    }
    math::UpdateMax(first_soft_block_seq_iter->range().end_index(),
                    estimate_last_nudge_pos);
    ++first_soft_block_seq_iter;
  }

  return {estimated_earliest_nudge_start_pos, estimate_last_nudge_pos};
}

bool DynamicOvertakePathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.longitudinal_decision ==
          speed::pb::SpeedDecision::PASS) {
    AdjustIntentionResultForPassDecision(object_occupancy_state,
                                         trajectory_intention_data,
                                         intention_result.lateral_decision,
                                         blocking_state_data, intention_result);
  }
  intention_result.is_overtaken =
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.longitudinal_decision == speed::pb::SpeedDecision::PASS;
  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

void DynamicOvertakePathReasoner::AdjustIntentionResultForPassDecision(
    const ObjectOccupancyState& object_occupancy_state,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    const LateralBlockingStateMetaData& blocking_state_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.nudge_index_ranges.empty());
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  // Increase required lateral gap.
  math::UpdateMax(
      kComfortRequiredLateralGapForOvertakeInMeter,
      intention_result.required_lat_gap.comfort_required_lateral_gap);

  DCHECK(
      !object_occupancy_state.predicted_trajectory_occupancy_states().empty());
  const ObjectTrajectoryStates* primary_trajectory = nullptr;
  for (const auto& [id, trajectory] :
       object_occupancy_state.predicted_trajectory_occupancy_states()) {
    if (id == object_occupancy_state.primary_trajectory_id()) {
      primary_trajectory = &trajectory;
      break;
    }
  }
  DCHECK(primary_trajectory != nullptr);

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      primary_trajectory->predicted_states();
  DCHECK(!predicted_states.empty());

  // Adjust the start pos of the nudge range so that ego path can be early
  // pushed to the side and ego can gain more FOV and do acceleration for
  // overtake.
  absl::StrAppend(&intention_result.debug_str, "Original start nudge pos: ",
                  intention_result.nudge_index_ranges.front().start_pos);
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      trajectory_intention_data.blocking_sequences;
  const pb::LateralBlockingState::BlockingState
      current_snapshot_blocking_state =
          blocking_state_data.tracked_blocking_info.blocking_state();
  const int start_nudge_idx = GetFirstNudgeSnapshotIndex(
      intention_result.nudge_index_ranges.front().start_pos,
      tracked_pass_decision, object_occupancy_state, blocking_sequences,
      blocking_state_data.effective_intention_index_ranges, predicted_states,
      current_snapshot_blocking_state);
  const int end_nudge_idx = intention_result.nudge_index_ranges.back().end_pos;
  if (start_nudge_idx !=
          intention_result.nudge_index_ranges.front().start_pos ||
      end_nudge_idx != intention_result.nudge_index_ranges.back().end_pos) {
    absl::StrAppend(&intention_result.debug_str, "Adjusted to ",
                    start_nudge_idx);
  }
  intention_result.nudge_index_ranges.clear();
  intention_result.nudge_index_ranges.emplace_back(start_nudge_idx,
                                                   end_nudge_idx);
}

math::Range<int> DynamicOvertakePathReasoner::EstimateComfortNudgeRange(
    const ObjectOccupancyState& object_occupancy_state,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const std::vector<math::Range<int>>& effective_ranges,
    pb::SnapshotIntention::PassState pass_decision,
    pb::LateralBlockingState::BlockingState current_snapshot_blocking_state)
    const {
  // Find the first soft blocking sequence at first.
  auto first_soft_block_seq_iter = std::find_if(
      blocking_sequences.begin(), blocking_sequences.end(),
      [](const auto& seq) {
        return seq.blocking_state() == pb::LateralBlockingState::SOFT_BLOCKING;
      });
  if (first_soft_block_seq_iter == blocking_sequences.end()) {
    return {0, 0};
  }

  // Estimate the first comfortable nudge snapshot.
  const int estimated_earliest_nudge_start_pos = GetFirstNudgeSnapshotIndex(
      first_soft_block_seq_iter->range().start_index(), pass_decision,
      object_occupancy_state, blocking_sequences, effective_ranges,
      predicted_states, current_snapshot_blocking_state);

  // Start from the first nudge snapshot, find the last soft blocking snapshot
  // before any hard blocking sequence.
  int estimate_last_nudge_pos = estimated_earliest_nudge_start_pos;
  while (first_soft_block_seq_iter != blocking_sequences.end()) {
    if (first_soft_block_seq_iter->blocking_state() ==
        pb::LateralBlockingState::HARD_BLOCKING) {
      break;
    }
    math::UpdateMax(first_soft_block_seq_iter->range().end_index(),
                    estimate_last_nudge_pos);
    ++first_soft_block_seq_iter;
  }

  return {estimated_earliest_nudge_start_pos, estimate_last_nudge_pos};
}

int DynamicOvertakePathReasoner::GetFirstNudgeSnapshotIndex(
    int original_start_pos, pb::SnapshotIntention::PassState pass_decision,
    const ObjectOccupancyState& object_occupancy_state,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const std::vector<math::Range<int>>& effective_ranges,
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    pb::LateralBlockingState::BlockingState current_snapshot_blocking_state)
    const {
  // Backward searches for a snapshot which is not hard blocking so that ego can
  // take action earlier, which helps to gain more FOV and accelerate during the
  // dynamic overtaking.
  for (int index = original_start_pos - 1; index >= 0; --index) {
    if (index >= static_cast<int>(predicted_states.size())) {
      continue;
    }
    const auto& snapshot = predicted_states[index];
    const double clearance_m =
        pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? snapshot.object_occupancy_param().left_boundary_clearance_m
            : snapshot.object_occupancy_param().right_boundary_clearance_m;
    if (clearance_m < kMinRequiredClearanceToStartNudgeInM) {
      continue;
    }
    if (IsHardBlockingSnapshot(object_occupancy_state, blocking_sequences,
                               current_snapshot_blocking_state,
                               effective_ranges, index)) {
      continue;
    }
    // This snapshot is valid.
    return index;
  }
  return original_start_pos;
}

std::unique_ptr<RepulsionMetaData>
DynamicOvertakePathReasoner::PreRepulsionReason(
    const ObjectOccupancyState& object_occupancy_state,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    const LateralBlockingStateMetaData& blocking_state_data,
    std::string& debug_str) const {
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  DCHECK(scenario_identify_result_.xlane_nudge_scene.has_value());
  const std::optional<XLaneNudgeScene>& xlane_nudge_scene =
      scenario_identify_result_.xlane_nudge_scene;
  DCHECK(xlane_nudge_scene->is_overtake_dynamic_agent);
  DCHECK(xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft ||
         xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossRight);
  // Ignore behind agent.
  if (traffic_rule_reasoning_info_.is_ego_in_lane() &&
      IsIgnorableBehindAgent(blocking_state_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    absl::StrAppend(&debug_str, "Ignorable behind agent");
    return nullptr;
  }

  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  if (trajectory_intention_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "have no effective snapshots");
    return nullptr;
  }

  // Get repulsion direction based on the cross side.
  const RepulsionMetaData::RepulsionDirection repulsion_direction =
      xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft
          ? RepulsionMetaData::RepulsionDirection::kLeft
          : RepulsionMetaData::RepulsionDirection::kRight;
  const pb::SnapshotIntention::PassState pass_decision =
      xlane_nudge_scene->type == pb::XLaneNudgeSceneType::kCrossLeft
          ? pb::SnapshotIntention::PASS_LEFT
          : pb::SnapshotIntention::PASS_RIGHT;

  const pb::LateralBlockingState::BlockingState
      current_snapshot_blocking_state =
          blocking_state_data.tracked_blocking_info.blocking_state();

  // Estimate the comfort nudge range based on the soft blocking range of the
  // trajectory.
  DCHECK(
      !object_occupancy_state.predicted_trajectory_occupancy_states().empty());
  const auto& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  DCHECK(!predicted_states.empty());
  const math::Range<int> estimated_nudge_range = EstimateComfortNudgeRange(
      object_occupancy_state, trajectory_intention_data.blocking_sequences,
      predicted_states, blocking_state_data.effective_intention_index_ranges,
      pass_decision, current_snapshot_blocking_state);
  absl::StrAppend(&debug_str, "estimated nudge range: [",
                  estimated_nudge_range.start_pos, ", ",
                  estimated_nudge_range.end_pos, "],");
  if (!math::IsValidRange(estimated_nudge_range)) {
    absl::StrAppend(&debug_str, "Invalid estimated nudge range,");
    return nullptr;
  }

  // Construct the agent trajectory for repulsion.
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(state.pose().center_2d());
  }
  for (int i = estimated_nudge_range.start_pos;
       i <= estimated_nudge_range.end_pos; ++i) {
    const auto& state = predicted_states[i];
    speed_profile.emplace_back(
        math::Ms2Sec(state.timestamp()), state.odom_m().value_or(0.0),
        state.object_occupancy_param().along_track_speed_mps,
        /*a_in=*/0.0, /*j_in=*/0.0);
  }
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return nullptr;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return nullptr;
  }

  const auto& tracked_param = object_occupancy_state.current_snapshot_info();
  return std::make_unique<RepulsionMetaData>(
      repulsion_direction, tracked_param.pose().contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile, tracked_param.pose().heading(),
      /*add_only_overtaking=*/true,
      /*ignore_if_nudge_failed_in=*/true,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/1.0,
      /*repulsion_required_lat_gap=*/
      kComfortRequiredLateralGapForOvertakeInMeter, reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      object_occupancy_state.primary_trajectory_id(),
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kCustomized,
      /*customized_guide_profile_in=*/upper_bound_speed_,
      RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      /*clearance_in=*/std::make_optional(ego_param_.width_m),
      /*max_extend_boundary_distance_in=*/
      std::make_optional(ego_param_.width_m),
      /*st_buffer_in=*/
      trajectory_intention_data.blocking_sequences.front().buffer_config());
}

}  // namespace path
}  // namespace planner
