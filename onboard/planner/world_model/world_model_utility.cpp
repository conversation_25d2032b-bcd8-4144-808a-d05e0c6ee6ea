#include "planner/world_model/world_model_utility.h"

#include <algorithm>
#include <cstdint>
#include <limits>
#include <memory>
#include <optional>
#include <queue>
#include <set>
#include <string>
#include <utility>

#include <absl/strings/str_format.h>
#include <boost/filesystem.hpp>

#include "adv_geom/path_curve.h"
#include "control/control_gflags.h"
#include "geometry/algorithms/distance.h"
#include "geometry/algorithms/specialization/within_polygon_with_cache.h"
#include "geometry/model/point_2d.h"
#include "hdmap/lib/hdmap_common.h"
#include "hdmap/stop_cell/stop_cell.h"
#include "inference/inference.h"
#include "latency/pipeline_id.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_generator_utility2.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/traffic_participant/predicted_trajectory.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction_protos/stationary_intention.pb.h"
#include "proto_util/proto_io.h"
#include "rt_event/rt_event.h"
#include "stop_cell_protos/stop_cell_data.pb.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {
using StateIndex = vehicle_model::CarModel::StateIndex;
}

constexpr int64_t kMaxInterestedPredictionTimeBufferInMSec = 500;
// The lower bound of the fence's front_bumper_to_fence, used in
// UpdateWaypointAvailability.
constexpr double kThresholdFrontBumperToFenceForWaypointAssistInMeter = -8.0;
// The distance threshold of end of lanesequence fence's
// front_bumper_to_fence, used in UpdateWaypointAvailability.
constexpr double kThresholdEndOfLaneSequenceForWaypointAssistInMeter = 10.0;
// The distance threshold between static obj and ego,
// used in UpdateWaypointAvailability.
constexpr double kThresholdStaticACCObjectForWaypointAssistInMeter = 2.0;
// The distance threshold between static obj and ego when object laterally not
// fully occupied, used in UpdateWaypointAvailability.
constexpr double kDistanceThresholdWhenLateralNotFullyOccupiedInMeter = 1.5;
// The max distance in meter for searching junction.
constexpr double kMaxDistForSearchingJunctionInMeter = 150.0;
// The Speed in Mps to judge if ego stationary.
constexpr double kEgoNearStaticSpeedInMps = 0.5;

// The key to indicate a specific reason of why the regional sections locator is
// invalid: Section vertices missed all potential sections.
constexpr const char* kSectionVerticesMissingAllPotentialSections =
    "section_vertices_missing_all_potential_sections";
// Indicate the reason why the regional sections locator is invalid: Not all
// lanes in 10m range near the ego are in regional section vertices.
constexpr const char* kLanesIn10mRangeNotAllInSectionVertices =
    "section_vertices_not_covering_all_lanes_in_10m_range";
// Indicate the reason why the regional sections locator is invalid: The
// potential sections were not updated from last selected lane sequence.
constexpr const char* kPotentialSectionsNotFromLastLaneSequence =
    "potential_sections_not_from_last_lane_sequence";

// Below are keys indicating the specific types of why the current lane
// candidate is invalid. These keys will be used when posting RT events for
// current lane invalidation.
constexpr const char* kCurrentLaneEmptyResult = "current_lane_is_empty";
constexpr const char* kCurrentLaneNotInLocatorPotentialSections =
    "current_lane_not_in_locator_potential_sections";
constexpr const char* kCurrentLaneNotInLastLaneSequence =
    "current_lane_not_in_last_lane_sequence";
constexpr const char* kCurrentLaneNotInLastSectionSequence =
    "current_lane_not_in_last_section_sequence";
constexpr const char* kCurrentLaneFlickerToPredecessorSection =
    "current_lane_flicker_to_predecessor_section";
constexpr const char* kCurrentLaneFlickerToNeighborLane =
    "current_lane_flicker_to_neighbor_lanes_when_lane_follow";
constexpr const char* kHasWaypointGraphCurrLaneNotInRegionalPathCurrLanes =
    "has_waypoint_graph_curr_lane_not_in_regional_path_curr_lanes";

std::unique_ptr<RobotState> MakeRobotState(
    const pb::ReplanLocatorConfig& config) {
  if (av_comm::CarId::Get().region() == av_comm::CarRegion::kNone) {
    LOG(WARNING) << "car region is not available.";
    return nullptr;
  }
  return std::make_unique<RobotState>(config);
}

void UpdatePickupDropoffZonesDebug(
    const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos,
    const routing::pb::RoutePoint& destination, pb::PickupDropoffDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  // Set routing destination.
  voy::Point2d* routing_destination_point =
      debug->mutable_routing_destination_point();
  routing_destination_point->set_x(destination.position().x());
  routing_destination_point->set_y(destination.position().y());

  if (pickup_dropoff_zone_infos.empty()) {
    return;
  }
  debug->mutable_pickup_dropoff_zones()->Reserve(
      pickup_dropoff_zone_infos.size());
  for (const auto& pdz : pickup_dropoff_zone_infos) {
    pb::PickupDropoffZoneDebug* pdz_debug = debug->add_pickup_dropoff_zones();
    pdz_debug->set_zone_id(pdz.id);
    // Set left boundary.
    if (pdz.left_boundary) {
      pdz_debug->mutable_left_boundary()->Reserve(
          pdz.left_boundary->points().size());
      for (const auto& pt : pdz.left_boundary->points()) {
        voy::Point2d* p_debug = pdz_debug->add_left_boundary();
        p_debug->set_x(pt.x());
        p_debug->set_y(pt.y());
      }
    }
    // Set right boundary.
    pdz_debug->mutable_right_boundary()->Reserve(
        pdz.right_boundary.points().size());
    for (const auto& pt : pdz.right_boundary.points()) {
      voy::Point2d* p_debug = pdz_debug->add_right_boundary();
      p_debug->set_x(pt.x());
      p_debug->set_y(pt.y());
    }
    // Set nominal path.
    pdz_debug->mutable_reference_line()->Reserve(
        pdz.nominal_path.points().size());
    for (const auto& pt : pdz.nominal_path.points()) {
      voy::Point2d* p_debug = pdz_debug->add_reference_line();
      p_debug->set_x(pt.x());
      p_debug->set_y(pt.y());
    }
    // Set border.
    if (pdz.border) {
      pdz_debug->mutable_border()->Reserve(pdz.border->points().size());
      for (const auto& pt : pdz.border->points()) {
        voy::Point2d* p_debug = pdz_debug->add_border();
        p_debug->set_x(pt.x());
        p_debug->set_y(pt.y());
      }
    }
    // Set pdz type.
    pdz_debug->set_is_virtual_stop(pdz.pickup_dropoff_zone_type ==
                                   PickupDropoffZoneType::VIRTUAL);
  }
}

void UpdateVirtualPickupDropoffDebug(
    const std::vector<hdmap::StopCellEntity>& raw_stop_cells,
    pb::PickupDropoffDebug* debug) {
  // Update virtual pickup dropoff debug
  debug->mutable_stop_cells()->Reserve(raw_stop_cells.size());
  for (const auto& stop_cell : raw_stop_cells) {
    pb::StopCellDebug* stop_cell_debug = debug->add_stop_cells();
    stop_cell_debug->set_stop_cell_id(stop_cell.id());
    stop_cell_debug->set_stop_cell_type(
        hdmap::StopCellEntity::Type_Name(stop_cell.type()).c_str());
    stop_cell_debug->set_is_no_parking_cell(stop_cell.parking_info().type() ==
                                            hdmap::CellParkingInfo_Type_FORBID);
    // Set no parking attributes.
    for (const auto& no_parking_attr : stop_cell.parking_info().attrs()) {
      stop_cell_debug->add_no_parking_attrs(
          hdmap::CellParkingInfo::ParkingAttr_Name(no_parking_attr).c_str());
    }
    // Set stop cell right boundary.
    stop_cell_debug->mutable_right_boundary()->Reserve(
        stop_cell.right_boundary().points().size());
    for (const auto& pt : stop_cell.right_boundary().points()) {
      voy::Point2d* pt_debug = stop_cell_debug->add_right_boundary();
      pt_debug->set_x(pt.x());
      pt_debug->set_y(pt.y());
    }
    // Set stop cell border.
    for (const auto& loop : stop_cell.border().loops()) {
      for (const auto& pt : loop.points()) {
        voy::Point2d* pt_debug = stop_cell_debug->add_border();
        pt_debug->set_x(pt.x());
        pt_debug->set_y(pt.y());
      }
      if (!loop.points().empty()) {
        voy::Point2d* pt_debug = stop_cell_debug->add_border();
        pt_debug->set_x(loop.points().begin()->x());
        pt_debug->set_y(loop.points().begin()->y());
      }
    }
  }
}

void UpdateVirtualPickupDropoffDebug(
    const std::vector<hdmap::StopCellResult>& stop_cell_results,
    pb::PickupDropoffDebug* debug) {
  if (stop_cell_results.empty()) {
    return;
  }

  std::vector<hdmap::StopCellEntity> raw_stop_cells;
  std::copy(stop_cell_results.front().stop_cells().begin(),
            stop_cell_results.front().stop_cells().end(),
            std::back_inserter(raw_stop_cells));
  UpdateVirtualPickupDropoffDebug(raw_stop_cells, debug);
}

void UpdateLastPullOutJumpOutLaneSequenceInfo(
    const pb::LaneSequenceCandidates& lane_sequence_candidates,
    pb::PullOutJumpOutSeed* pull_out_jump_out_seed) {
  pull_out_jump_out_seed->mutable_last_jump_out_lane_sequence()->Clear();
  // We label all Lane follow sequences as jump out when ego pulls out from
  // non-drivable lane, in this case we save the optimal lane follow sequence
  // as last jump out sequence.
  for (const pb::LaneSequenceCandidate& lane_sequence_candidate :
       lane_sequence_candidates.lane_sequence_candidates()) {
    if (lane_selection::IsPullOutJumpOutLaneSequence(lane_sequence_candidate) &&
        lane_sequence_candidate.lane_sequence_type() ==
            pb::LaneSequenceCandidate::LANE_FOLLOW) {
      for (const int64_t lane_id : lane_sequence_candidate.lane_sequence()) {
        pull_out_jump_out_seed->add_last_jump_out_lane_sequence(lane_id);
      }
      break;
    }
  }
  if (!pull_out_jump_out_seed->last_jump_out_lane_sequence().empty()) {
    return;
  }
  // If reach here, it means that ego starts from drivable lane and has only one
  // alternative lane follow type jump out lane sequence.
  for (const pb::LaneSequenceCandidate& lane_sequence_candidate :
       lane_sequence_candidates.lane_sequence_candidates()) {
    if (lane_selection::IsPullOutJumpOutLaneSequence(lane_sequence_candidate)) {
      for (const int64_t lane_id : lane_sequence_candidate.lane_sequence()) {
        pull_out_jump_out_seed->add_last_jump_out_lane_sequence(lane_id);
      }
      pull_out_jump_out_seed->add_last_jump_out_current_lanes(
          lane_sequence_candidate.current_lane_id());
      break;
    }
  }
}

const pnc_map::Road* GetRoadOfDestination(
    const GlobalRouteSolution& global_route_solution,
    const RegionalMap& regional_map) {
  if (global_route_solution.road_ids().empty()) {
    return nullptr;
  }

  // Return nullptr if the dest road is not one of the regional roads.
  const auto dest_road_id = global_route_solution.road_ids().back();
  for (const auto* regional_road : regional_map.roads) {
    if (regional_road != nullptr && dest_road_id == regional_road->id()) {
      return regional_road;
    }
  }

  return nullptr;
}

void PopulateRouteAssociationResultInPredictedTrajectoryWrapper(
    const route_association::PredictedTrajectoryRouteAssociator&
        association_result,
    PredictedTrajectoryWrapper& predicted_trajectory) {
  DCHECK_EQ(association_result.object_id(), predicted_trajectory.object_id());
  DCHECK_EQ(association_result.trajectory_id(), predicted_trajectory.id());
  const auto& optional_partial_route_inferred_by_the_first_pose_opt =
      association_result.partial_route_inferred_by_the_first_pose_opt();
  if (!optional_partial_route_inferred_by_the_first_pose_opt.has_value()) {
    predicted_trajectory.set_associated_route(
        association_result.most_likely_route());
  }

  // Note(zhihaoruan): Use
  // PredictedTrajectoryRouteAssociator.raw_equally_optimal_first_map_elements_
  // for now. If the map element is virtual (i.e. in junction), agent's
  // center point may not necessarily strictly within the polygon of the
  // element, as we may extend the query range.
  predicted_trajectory.set_current_occupied_map_elements(
      association_result.raw_equally_optimal_first_map_elements());

  const route_association::MapElementAndPoseInfo*
      first_occupied_map_element_ptr =
          optional_partial_route_inferred_by_the_first_pose_opt.has_value()
              ? &optional_partial_route_inferred_by_the_first_pose_opt.value()
          : !association_result.most_likely_route().empty()
              ? &association_result.most_likely_route().front()
              : nullptr;

  // If the first associated map element is a zone, use it to set
  // |latest_exit_zone_covering_first_pose_opt_| in
  // |PredictedTrajectoryWrapper|. Otherwise, use seed info instead.
  if (first_occupied_map_element_ptr != nullptr &&
      first_occupied_map_element_ptr->type == pb::MapElementType::ZONE) {
    predicted_trajectory.set_latest_exit_zone_covering_first_pose_opt(
        *first_occupied_map_element_ptr);
  } else if (association_result.latest_exit_zone_info_in_seed_opt()
                 .has_value()) {
    predicted_trajectory.set_latest_exit_zone_covering_first_pose_opt(
        association_result.latest_exit_zone_info_in_seed_opt());
  }
  // If the first associated map element is an isolated lane, use it to set
  // |latest_isolated_lane_opt_| in |PredictedTrajectoryWrapper|. Otherwise, use
  // seed info instead.
  if (first_occupied_map_element_ptr != nullptr &&
      first_occupied_map_element_ptr->type == pb::MapElementType::LANE &&
      (first_occupied_map_element_ptr->lane_ptr->type() ==
           hdmap::Lane_LaneType::Lane_LaneType_REGULAR ||
       first_occupied_map_element_ptr->lane_ptr->type() ==
           hdmap::Lane_LaneType::Lane_LaneType_BUS)) {
    predicted_trajectory.set_latest_isolated_lane_opt(
        *first_occupied_map_element_ptr);
  } else if (association_result.latest_isolated_lane_info_in_seed_opt()
                 .has_value()) {
    predicted_trajectory.set_latest_isolated_lane_opt(
        association_result.latest_isolated_lane_info_in_seed_opt());
  }
}

int64_t GetMaxInterestedPredictionTimestamp(int64_t current_pose_time) {
  return current_pose_time + constants::kStitchingImmutableTimeInMSec +
         constants::kTrajectoryHorizonInMSec +

         kMaxInterestedPredictionTimeBufferInMSec;
}

bool WasAgentConsideredInSpeedSeed(
    const planner::speed::pb::SpeedSeed& speed_seed, const ObjectId& obj_id) {
  for (const auto& result : speed_seed.constraint_results()) {
    if (result.has_constraint() && result.constraint().obj_id() == obj_id) {
      return true;
    }
  }
  if (!speed_seed.has_speed_limiters_seed()) {
    return false;
  }
  for (const auto& limiter : speed_seed.speed_limiters_seed()
                                 .last_cautious_speed_limiter_per_object()) {
    if (limiter.object_id() == obj_id) {
      return true;
    }
  }
  return false;
}

bool IsTargetSceneToRunGlobalLaneAssociation(
    const std::vector<LaneSequenceResult>& lane_sequence_results) {
  for (const LaneSequenceResult& lane_sequence_result : lane_sequence_results) {
    const pnc_map::Lane* current_lane = lane_sequence_result.current_lane;
    if (current_lane == nullptr) {
      continue;
    }
    const std::vector<const pnc_map::Lane*>& lane_sequence =
        lane_sequence_result.lane_sequence;
    const auto iter = std::find_if(lane_sequence.cbegin(), lane_sequence.cend(),
                                   [current_lane](const pnc_map::Lane* lane) {
                                     return lane->id() == current_lane->id();
                                   });
    if (iter == lane_sequence.cend()) {
      continue;
    }
    // For now, we only consider the current and the next lanes.
    std::vector<const pnc_map::Lane*> target_lanes{current_lane};
    if (iter + 1 != lane_sequence.cend()) {
      target_lanes.push_back(*(iter + 1));
    }
    const bool has_any_straight_lane_in_junction =
        std::any_of(target_lanes.begin(), target_lanes.end(),
                    [](const pnc_map::Lane* lane) {
                      return lane->IsInJunction() &&
                             lane->turn() == hdmap::Lane_Turn_STRAIGHT;
                    });
    if (has_any_straight_lane_in_junction) {
      return true;
    }
  }
  return false;
}

std::set<int64_t> GetDrivableLaneIdsInNextJunctions(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  if (current_lane_candidates.empty()) {
    return {};
  }
  const lane_candidate::CurrentLaneCandidate& current_lane_candidate =
      current_lane_candidates.front();
  const pnc_map::Section* current_section =
      current_lane_candidate.lane()->section();
  const double percentage_along_current_section =
      current_lane_candidate.position_percentage_along_lane();
  std::set<int64_t> drivable_lane_ids;
  std::set<int64_t> visited_section_ids;
  // The key is section and the value is the distance to ego.
  std::queue<std::pair<const pnc_map::Section*, double>> queue;
  queue.push(std::make_pair(current_section, 0.0));
  while (!queue.empty()) {
    const pnc_map::Section* section = queue.front().first;
    const double current_distance = queue.front().second;
    queue.pop();
    if (current_distance > kMaxDistForSearchingJunctionInMeter) {
      continue;
    }
    visited_section_ids.insert(section->id());
    if (section->IsInJunction()) {
      for (const pnc_map::Lane* lane : section->lanes()) {
        if (drivable_lane_reasoner.IsLaneRobotDrivable(
                lane, /*check_local_hold=*/true, /*check_edge_filter=*/true)) {
          drivable_lane_ids.insert(lane->id());
        }
      }
      continue;
    }
    const double section_length =
        section->id() == current_section->id()
            ? percentage_along_current_section * section->GetLength()
            : section->GetLength();
    for (const pnc_map::Section* successor : section->successors()) {
      if (visited_section_ids.count(successor->id()) > 0) {
        continue;
      }
      queue.push(std::make_pair(successor, current_distance + section_length));
    }
  }

  return drivable_lane_ids;
}

bool IsRiskyRoadBlockageOnRoute(
    const GlobalRouteSolution& global_route_solution,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  if (global_route_solution.global_route_section_ids_sequence().empty() ||
      road_blockages.empty() || current_lane_candidates.empty()) {
    return false;
  }

  const lane_candidate::CurrentLaneCandidate& current_lane_candidate =
      current_lane_candidates.front();
  const pnc_map::Section* current_section =
      current_lane_candidate.lane()->section();

  // Indicate if we have found current section in global route sections.
  // TODO(Huoliang): Consider the situation that global route loops back.
  bool found_match = false;
  for (const auto section_id :
       global_route_solution.global_route_section_ids_sequence()) {
    if (section_id == current_section->id()) {
      found_match = true;
    }
    if (!found_match) {
      continue;
    }

    // Check if there is risky road blockage on global route.
    for (const auto& [_, road_blockage] : road_blockages) {
      for (const RoadBlockageGroup& road_blockage_group : road_blockage) {
        const pb::RoadBlockageGroup& road_blockage_group_pb =
            road_blockage_group.road_blockage_group;
        if (road_blockage_group_pb.section_id() != section_id) {
          continue;
        }
        // The road blockage group is behind ego.
        if (current_section->id() == section_id &&
            (current_lane_candidate.position_percentage_along_lane() >
             road_blockage_group_pb.percentage_along_lane_range().end_pos())) {
          continue;
        }
        // Check if road blockage group has valid blocked types: like entire
        // blocked, straight blocked and so on.
        if (road_blockage_group_pb.types().empty()) {
          continue;
        }
        // Return true if any risky road blockage has been found on route.
        return true;
      }
    }
  }

  // Has not found any risky road blockage on route.
  return false;
}

bool IsEgoCarNearStationary(const RobotState& robot_state) {
  return robot_state.IsStationary() ||
         std::abs(robot_state.current_state_snapshot().speed()) <
             constants::kDefaultEgoNearStaticSpeedInMps;
}

bool ShouldUpdateRecommendReplan(
    const routing::pb::RouteSolution& route_solution,
    const std::vector<routing::pb::RecommendReplanInfo>&
        recommend_replan_infos) {
  const auto& astar_search_debug = route_solution.astar_search_debug();
  std::set<int64_t> curr_recommend_lane_ids;
  for (const auto& recommend_lane_info : recommend_replan_infos) {
    for (const auto& lane_id :
         recommend_lane_info.expected_drivable_lane_ids()) {
      curr_recommend_lane_ids.insert(lane_id);
    }
  }

  // Return true if curr_recommend_lane_ids is not same as last query's
  // recommend_lane_ids.
  if (static_cast<int>(curr_recommend_lane_ids.size()) !=
      astar_search_debug.recommend_drivable_lane_ids().size()) {
    return true;
  }
  for (const auto lane_id : astar_search_debug.recommend_drivable_lane_ids()) {
    if (curr_recommend_lane_ids.find(lane_id) ==
        curr_recommend_lane_ids.end()) {
      return true;
    }
  }

  // Return false if given recommend replan infos has been dealt with.
  return false;
}

void UpdateWaypointRecommendRoutingReplan(
    const RobotState& robot_state,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::WaypointRecommendRoutingReplan* waypoint_recommend_routing_replan) {
  if (waypoint_recommend_routing_replan == nullptr) {
    LOG(ERROR) << "waypoint_recommend_routing_replan is nullptr!";
    return;
  }

  // Ego must be stationary when recommend replan.
  if (!IsEgoCarNearStationary(robot_state)) {
    waypoint_recommend_routing_replan->set_is_triggered(false);
    waypoint_recommend_routing_replan->set_reason_for_not_recommend(
        pb::WaypointRecommendRoutingReplan::EGO_NOT_STATIONARY);
    return;
  }

  rt_event::PostRtEvent<
      rt_event::planner::RecommendReplanWhenEgoStartRemoteAssist>();
  if (!last_selected_lane_sequence.empty()) {
    const math::geometry::PolylineCurve2d nominal_path_lane_sequence =
        lane_selection::GetLaneSequenceCurve(
            last_selected_lane_sequence,
            lane_selection::LaneCurveType::kCenterLine,
            /*should_use_prior_path=*/false,
            /*need_smooth=*/false);

    if (!common::IsEgoNearEndOfLaneSequence(
            nominal_path_lane_sequence,
            robot_state.current_state_snapshot().front_bumper_position()) &&
        !common::IsEgoInShortLaneSequence(nominal_path_lane_sequence)) {
      waypoint_recommend_routing_replan->set_is_triggered(false);
      waypoint_recommend_routing_replan->set_reason_for_not_recommend(
          pb::WaypointRecommendRoutingReplan::LANE_SEQUENCE_NORMAL);
      return;
    }
  }
  rt_event::PostRtEvent<
      rt_event::planner::RecommendReplanWhenNearEndOfLaneSequence>();

  std::set<const pnc_map::Section*> sections;
  std::set<int64_t> expected_drivable_lane_ids;
  for (const auto& current_lane_candidate : current_lane_candidates) {
    sections.insert(current_lane_candidate.lane()->section());
  }
  for (const auto& section : sections) {
    for (const auto& lane : section->lanes()) {
      expected_drivable_lane_ids.insert(lane->id());
      for (const auto& successor_lane : lane->successors()) {
        expected_drivable_lane_ids.insert(successor_lane->id());
      }
    }
  }
  for (const auto& lane_id : expected_drivable_lane_ids) {
    waypoint_recommend_routing_replan->add_recommend_lane_ids(lane_id);
  }
  waypoint_recommend_routing_replan->set_is_triggered(true);
  waypoint_recommend_routing_replan->set_reason_for_not_recommend(
      pb::WaypointRecommendRoutingReplan::NOT_SET);
}

void UpdateRecommendReplanInfoByWaypointAssist(
    const pb::WaypointRecommendRoutingReplan& waypoint_recommend_routing_replan,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos) {
  const auto& recommend_lane_ids =
      waypoint_recommend_routing_replan.recommend_lane_ids();
  if (recommend_lane_ids.empty()) {
    return;
  }
  routing::pb::RecommendReplanInfo recommend_replan_info;
  recommend_replan_info.set_recommend_replan_type(
      routing::pb::RecommendReplanType::WAYPOINT_ASSIST);
  for (const auto& lane_id : recommend_lane_ids) {
    recommend_replan_info.add_expected_drivable_lane_ids(lane_id);
  }
  recommend_replan_infos->emplace_back(recommend_replan_info);
}

void UpdateRecommendReplanInfosByRoadBlockage(
    const RobotState& robot_state,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const GlobalRouteSolution& global_route_solution,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos) {
  DCHECK(recommend_replan_infos);
  if (current_lane_candidates.empty() || recommend_replan_infos == nullptr) {
    LOG(ERROR) << "Input current_lane_candidates or recommend_replan_infos are "
                  "illegal!";
    return;
  }
  if (road_blockages.empty()) {
    return;
  }

  // TODO(Junfeng): Move this limit out.
  // Ego must be stationary when recommend replan.
  if (!IsEgoCarNearStationary(robot_state)) {
    return;
  }

  // There must be risky road blockage on route.
  if (!IsRiskyRoadBlockageOnRoute(global_route_solution, road_blockages,
                                  current_lane_candidates)) {
    return;
  }

  // Add expected drivable lanes in next junction.
  std::set<int64_t> expected_drivable_lane_ids =
      GetDrivableLaneIdsInNextJunctions(drivable_lane_reasoner,
                                        current_lane_candidates);

  // Add expected drivable lanes from expected_sections.
  // 1. drivable.
  // 2. not in cost map.
  std::vector<const pnc_map::Section*> expected_sections;
  for (const auto& current_lane_candidate : current_lane_candidates) {
    expected_sections.push_back(current_lane_candidate.lane()->section());
    for (const pnc_map::Section* section :
         expected_sections.back()->successors()) {
      expected_sections.push_back(section);
    }
  }
  for (const pnc_map::Section* section : expected_sections) {
    for (const pnc_map::Lane* lane : section->lanes()) {
      if (drivable_lane_reasoner.IsLaneRobotDrivable(
              lane, /*check_local_hold=*/true,
              /*check_edge_filter=*/true) &&
          global_route_solution.GetCostToDestination(
              *lane, global_route_solution.waypoint_count()) == nullptr) {
        expected_drivable_lane_ids.insert(lane->id());
      }
    }
  }

  if (expected_drivable_lane_ids.empty()) {
    return;
  }

  routing::pb::RecommendReplanInfo recommend_replan_info;
  recommend_replan_info.set_recommend_replan_type(
      routing::pb::RecommendReplanType::AVOID_ROAD_BLOCKAGE);
  for (const auto lane_id : expected_drivable_lane_ids) {
    recommend_replan_info.add_expected_drivable_lane_ids(lane_id);
  }

  recommend_replan_infos->push_back(std::move(recommend_replan_info));
}

void UpdateRecommendReplanInfosByRegionalPath(
    const RegionalMap& regional_map,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    std::vector<routing::pb::RecommendReplanInfo>* recommend_replan_infos) {
  DCHECK(recommend_replan_infos);
  if (recommend_replan_infos == nullptr) {
    LOG(ERROR) << "Input recommend_replan_infos is nullptr!";
    return;
  }
  if (regional_map.expected_replan_lane_ids.empty()) {
    return;
  }
  routing::pb::RecommendReplanInfo recommend_replan_info_for_regional_path;
  if (!FLAGS_planning_enable_recommend_replan_for_regional_path_request) {
    // True if ego is in the before entering roundabout lane.
    bool is_ego_before_entering_roundabout = false;
    for (const pnc_map::Lane* curr_lane : current_lanes) {
      if (!curr_lane ||
          curr_lane->section()->road()->is_entering_roundabout()) {
        continue;
      }
      const pnc_map::Road* curr_road = curr_lane->section()->road();
      for (const pnc_map::Road* succ_road : curr_road->successors()) {
        if (succ_road->is_entering_roundabout()) {
          is_ego_before_entering_roundabout = true;
          break;
        }
      }
    }
    if (!is_ego_before_entering_roundabout) {
      return;
    }
    recommend_replan_info_for_regional_path.set_recommend_replan_type(
        routing::pb::RecommendReplanType::REGIONAL_PATH_REQUEST_FOR_ROUNDABOUT);
    for (int64_t lane_id : regional_map.expected_replan_lane_ids) {
      recommend_replan_info_for_regional_path.add_expected_drivable_lane_ids(
          lane_id);
    }
    recommend_replan_infos->push_back(recommend_replan_info_for_regional_path);
  }
  if (FLAGS_planning_enable_recommend_replan_for_regional_path_request) {
    recommend_replan_info_for_regional_path.set_recommend_replan_type(
        routing::pb::RecommendReplanType::REGIONAL_PATH_REQUEST);
    for (int64_t lane_id : regional_map.expected_replan_lane_ids) {
      recommend_replan_info_for_regional_path.add_expected_drivable_lane_ids(
          lane_id);
    }
    recommend_replan_infos->push_back(recommend_replan_info_for_regional_path);
  }
}

void TryToAddRecommendReplanInfo(
    const GlobalRouteSolution& global_route_solution,
    const RobotState& robot_state,
    const std::map<int64_t, std::vector<RoadBlockageGroup>>& road_blockages,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const CurrentLaneAssociator& current_lane_associator,
    const CurrentLaneReasoner& current_lane_reasoner,
    const RegionalMap& regional_map,
    const pb::WaypointRecommendRoutingReplan& waypoint_recommend_routing_replan,
    routing::pb::RouteQuery* replan_route_query) {
  std::vector<routing::pb::RecommendReplanInfo> recommend_replan_infos;
  UpdateRecommendReplanInfoByWaypointAssist(waypoint_recommend_routing_replan,
                                            &recommend_replan_infos);
  UpdateRecommendReplanInfosByRoadBlockage(
      robot_state, drivable_lane_reasoner, global_route_solution,
      road_blockages,
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner.current_lane_candidates_for_regional_path()
          : current_lane_associator.current_lane_candidates_for_regional_path(),
      &recommend_replan_infos);
  UpdateRecommendReplanInfosByRegionalPath(
      regional_map,
      FLAGS_planning_enable_current_lane_reasoner
          ? current_lane_reasoner.current_lanes_for_regional_path()
          : current_lane_associator.current_lanes_for_regional_path(),
      &recommend_replan_infos);
  if (ShouldUpdateRecommendReplan(global_route_solution.route_solution(),
                                  recommend_replan_infos)) {
    // Update recommend replan infos from planner to routing.
    replan_route_query->clear_recommend_replan_infos();
    for (const auto& recommend_replan_info : recommend_replan_infos) {
      if (recommend_replan_info.recommend_replan_type() ==
          routing::pb::RecommendReplanType::NON_RECOMMEND_REPLAN) {
        LOG(ERROR) << "Recommend replan type is not valid!";
        continue;
      }
      routing::pb::RecommendReplanInfo* recommend_replan_info_in_pb =
          replan_route_query->add_recommend_replan_infos();
      recommend_replan_info_in_pb->CopyFrom(recommend_replan_info);
    }
  }
}

void TryToAddDesiredRouteRequest(const pb::RegionalPathDebug& regional_path_pb,
                                 routing::pb::RouteQuery* replan_route_query) {
  // Return if regional path is not diverged with global route.
  if (!regional_path_pb.diverge_with_global_route()) {
    return;
  }

  // Return if regional path is cross road.
  // TODO(taojunfeng): remove this filter after global route is able to cross
  // road lane change.
  if (regional_path_pb.is_cross_road()) {
    return;
  }

  // Return if global route is passable and has no valid prefix proposal.
  const auto& global_route_passable_info =
      regional_path_pb.reroute_request().global_route_passable_info();
  if (global_route_passable_info.is_passable()) {
    LOG(ERROR) << "Regional path has unreasonable reroute!";
    return;
  }

  if (!regional_path_pb.has_ego_to_exit_node_cost()) {
    LOG(ERROR) << "Regional path debug info has no ego_to_exit_node_cost.";
    return;
  }
  if (math::NearZero(regional_path_pb.ego_to_exit_node_cost().distance_m())) {
    LOG(ERROR) << "Regional path length is near zero.";
    return;
  }

  const pb::EgoToExitNodeCost& ego_to_exit_node_cost_pb =
      regional_path_pb.ego_to_exit_node_cost();
  if (ego_to_exit_node_cost_pb.astar_path().empty()) {
    LOG(ERROR) << "Regional path has empty astar path.";
    return;
  }

  routing::pb::DesiredRouteRequest* desired_route_request =
      replan_route_query->mutable_desired_route_request();
  desired_route_request->set_exit_node_id(
      ego_to_exit_node_cost_pb.exit_node_id());
  desired_route_request->set_cost_to_exit_node(ego_to_exit_node_cost_pb.cost());
  desired_route_request->set_time_to_exit_node_s(
      ego_to_exit_node_cost_pb.time_s());
  desired_route_request->set_distance_to_exit_node_m(
      ego_to_exit_node_cost_pb.distance_m());
  desired_route_request->mutable_astar_path()->CopyFrom(
      ego_to_exit_node_cost_pb.astar_path());

  desired_route_request->set_diverge_with_new_global_route(
      regional_path_pb.diverge_with_new_global_route());
  desired_route_request->mutable_path_lane_ids()->CopyFrom(
      regional_path_pb.path_lane_ids());
  desired_route_request->mutable_path_section_ids()->CopyFrom(
      regional_path_pb.path_section_ids());
  desired_route_request->mutable_route_cost_factors()->CopyFrom(
      regional_path_pb.route_cost_factors().cost_factors_base());
  desired_route_request->set_cost_to_destination(
      regional_path_pb.cost_to_destination());
  desired_route_request->set_distance_to_destination_m(
      regional_path_pb.distance_to_destination_m());
  desired_route_request->set_time_to_destination_s(
      regional_path_pb.time_to_destination_s());
  // Populate the route change factors from planner.
  if (global_route_passable_info.has_road_blockage_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kAvoidRoadBlockage);
  }
  if (global_route_passable_info.has_lane_change_stuck_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kAvoidLCStuck);
  }
  if (global_route_passable_info.has_variable_lane_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kComplyVariableLane);
  }
  if (global_route_passable_info.has_safety_fallback_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kSafetyFallBack);
  }
  if (global_route_passable_info.has_end_of_ls_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kAvoidEOL);
  }
  if (global_route_passable_info.has_section_preference_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kMatchSectionPreference);
  }
  if (global_route_passable_info.has_proposal_reroute_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kMatchProposalReroute);
  }
  if (global_route_passable_info.has_risky_route_change_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kAvoidRiskyRouteChange);
  }
  if (global_route_passable_info.has_constraint_scenario()) {
    desired_route_request->add_active_reroute_factors(
        routing::pb::PlannerActiveRerouteFactors::kAvoidConstraint);
  }
  // Set replan route query as desired replan type.
  replan_route_query->set_replan_purpose(
      routing::pb::ReplanPurpose::DESIRED_REPLAN_FOR_NEW_ROUTE);
}

void UpdateWaypointAvailability(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::PlanningDebug* planning_debug) {
  TRACE_EVENT_SCOPE(
      planner, WorldModel_UpdateWaypointAvailability,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());

  if (!planning_debug) {
    return;
  }

  auto* waypoint_assist_debug = planning_debug->mutable_world_model_debug()
                                    ->mutable_waypoint_assist_debug();
  waypoint_assist_debug->set_waypoint_availability(
      pb::WaypointAvailability::AVAILABLE);
  waypoint_assist_debug->set_reason_waypoint_assist_not_available(
      pb::ReasonWaypointAssistNotAvailable::NOT_SET);
  // judge if ego stationary.
  if (!(robot_state.IsStationary() ||
        std::abs(robot_state.current_state_snapshot().speed()) <
            kEgoNearStaticSpeedInMps)) {
    waypoint_assist_debug->set_waypoint_availability(
        pb::WaypointAvailability::NOT_AVAILABLE);
    waypoint_assist_debug->set_reason_waypoint_assist_not_available(
        pb::ReasonWaypointAssistNotAvailable::EGO_NOT_STATIONARY);
    return;
  }
  if (!FLAGS_planning_should_post_process_waypoint_availability) {
    return;
  }
  ReasonIfWaypointAssistNotAvailableByStaticObjects(
      previous_decoupled_maneuver_seed, decoupled_yielding_fence_list_in,
      robot_state, planner_object_map, last_selected_lane_sequence,
      planning_debug);
  ReasonIfWaypointAssistNotAvailableByEndOfLaneSequence(
      decoupled_yielding_fence_list_in, robot_state, joint_pnc_map_service,
      planning_debug);
  ReasonIfWaypointAssistNotAvailableByHardBoundary(
      decoupled_yielding_fence_list_in, robot_state, planning_debug);
}

void ReasonIfWaypointAssistNotAvailableByStaticObjects(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    pb::PlanningDebug* planning_debug) {
  // static acc for decoupled waypoint assist.
  // This is for the scenario that speed reasoning has already set stop fence
  // for static object and ego can not unstuck.
  // Here we use stop fence, dist between ego and object, occupied lane,
  // lateral\longitudinal relationship between object and ego, to make the
  // decision.
  // Avoid override waypoint_availability when NOT_AVAILABLE has been set.
  if (!planning_debug) {
    return;
  }

  if (planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .waypoint_availability() ==
          pb::WaypointAvailability::NOT_AVAILABLE &&
      planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .reason_waypoint_assist_not_available() !=
          pb::ReasonWaypointAssistNotAvailable::NOT_SET) {
    return;
  }

  if (previous_decoupled_maneuver_seed.selected_behavior_type() ==
      pb::BehaviorType::CROSS_LANE) {
    return;
  }

  if (decoupled_yielding_fence_list_in.has_value()) {
    for (const auto& fence :
         decoupled_yielding_fence_list_in.value().fence_list()) {
      // Judge if exist a fence just located in ego border, behind ego front
      // bumper.
      if (fence.front_bumper_to_fence_m() < 0 &&
          fence.front_bumper_to_fence_m() >
              kThresholdFrontBumperToFenceForWaypointAssistInMeter) {
        auto acc_agent_iter = planner_object_map.find(fence.obj_id());
        // Skip if not tracked in planner object map.
        if (acc_agent_iter == planner_object_map.end()) {
          continue;
        }
        // Skip if agent is moving.
        if (!acc_agent_iter->second.is_stationary()) {
          continue;
        }
        const pnc_map::Lane* current_lane =
            GetCurrentLane(robot_state, last_selected_lane_sequence);
        if (current_lane != nullptr) {
          const math::ProximityQueryInfo ego_proximity_info =
              current_lane->center_line().GetProximity(
                  robot_state.current_state_snapshot().front_bumper_position(),
                  math::pb::UseExtensionFlag::kAllow);
          const math::ProximityQueryInfo obj_proximity_info =
              current_lane->center_line().GetProximity(
                  acc_agent_iter->second.center_2d(),
                  math::pb::UseExtensionFlag::kAllow);
          // Skip if object center not in current lane border .
          if (!math::geometry::Within(acc_agent_iter->second.center_2d(),
                                      current_lane->border())) {
            continue;
          }
          // Skip if object center arclength behind ego front bumper for half of
          // agent length.
          if (obj_proximity_info.arc_length +
                  0.5 * acc_agent_iter->second.length() <
              ego_proximity_info.arc_length) {
            continue;
          }
          // Calculate the lateral encroachment to
          // judge if space sufficient to unstuck.
          // TODO(Xiaobo): Dive deep to cover more CAN-UNSTUCK scenarios,
          // eg. use NudgeMotionChecker.
          const double lane_width =
              current_lane->GetWidthAtPoint(acc_agent_iter->second.center_2d());
          // Skip if not lateral fully blocked and longitudinal dist can pass.
          // Compare object center's proximity_info.dist and 1/4 lane width, to
          // judge if object not blocked straightly.
          if (acc_agent_iter->second.l2_distance_to_ego_m() >
                  kDistanceThresholdWhenLateralNotFullyOccupiedInMeter &&
              obj_proximity_info.dist > 0.25 * lane_width) {
            continue;
          }
          // calculate l2_distance between obj && ego's bbox.
          if (acc_agent_iter->second.l2_distance_to_ego_m() <
              kThresholdStaticACCObjectForWaypointAssistInMeter) {
            auto* waypoint_assist_debug =
                planning_debug->mutable_world_model_debug()
                    ->mutable_waypoint_assist_debug();
            waypoint_assist_debug->set_waypoint_availability(
                pb::WaypointAvailability::NOT_AVAILABLE);
            waypoint_assist_debug->set_reason_waypoint_assist_not_available(
                pb::ReasonWaypointAssistNotAvailable::STATIC_ACC_OBJECT);
            return;
          }
        }
      }
    }
  }
}

void ReasonIfWaypointAssistNotAvailableByEndOfLaneSequence(
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list_in,
    const RobotState& robot_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    pb::PlanningDebug* planning_debug) {
  // for end_of_lane_sequence in non lane change lane sequence.
  if (!FLAGS_planning_enable_disable_waypoint_assist_usage_for_end_of_laneseq) {
    return;
  }

  if (!planning_debug) {
    return;
  }
  // Avoid override waypoint_availability when NOT_AVAILABLE has been set.
  if (planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .waypoint_availability() ==
          pb::WaypointAvailability::NOT_AVAILABLE &&
      planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .reason_waypoint_assist_not_available() !=
          pb::ReasonWaypointAssistNotAvailable::NOT_SET) {
    return;
  }
  std::vector<int64_t> non_lane_change_lane_ids;
  if (planning_debug) {
    for (const auto& lane : planning_debug->behavior_reasoner_debug()
                                .lane_sequence_generator()
                                .non_lane_change_lane_sequence()
                                .lane_sequence()) {
      non_lane_change_lane_ids.push_back(lane.lane_id());
    }
  }
  const auto& non_lane_change_lane_sequence =
      joint_pnc_map_service.GetLaneSequence(non_lane_change_lane_ids);
  if (decoupled_yielding_fence_list_in.has_value()) {
    if (decoupled_yielding_fence_list_in.value().fence_list().empty()) {
      return;
    }
    double end_of_lane_sequence_dist = 0;
    bool non_lane_change_lane_sequence_available = false;
    if (!non_lane_change_lane_sequence.empty()) {
      const ::math::geometry::Point2d& lane_sequence_end_point =
          non_lane_change_lane_sequence.back()->center_line().GetEndPoint();
      const math::geometry::PolylineCurve2d
          nominal_path_non_lane_change_lane_sequence =
              non_lane_change_lane_sequence.back()->center_line();
      const double ego_arclength =
          nominal_path_non_lane_change_lane_sequence
              .GetProximity(math::geometry::Point2d(
                                robot_state.current_state_snapshot().x(),
                                robot_state.current_state_snapshot().y()),
                            math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      const double end_arclength =
          nominal_path_non_lane_change_lane_sequence
              .GetProximity(lane_sequence_end_point,
                            math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      end_of_lane_sequence_dist = end_arclength - ego_arclength;
      non_lane_change_lane_sequence_available = true;
    }
    const auto end_of_lane_sequence_fence_iter = std::find_if(
        decoupled_yielding_fence_list_in.value().fence_list().begin(),
        decoupled_yielding_fence_list_in.value().fence_list().end(),
        [&](const auto& fence) {
          return fence.constraint_id() == "end_of_lane_sequence";
        });
    if (end_of_lane_sequence_fence_iter ==
        decoupled_yielding_fence_list_in.value().fence_list().end()) {
      return;
    }
    const double nearest_non_lane_change_sequence_end_dist =
        non_lane_change_lane_sequence_available
            ? end_of_lane_sequence_dist
            : end_of_lane_sequence_fence_iter->front_bumper_to_fence_m();
    if (nearest_non_lane_change_sequence_end_dist <
        kThresholdEndOfLaneSequenceForWaypointAssistInMeter) {
      auto* waypoint_assist_debug = planning_debug->mutable_world_model_debug()
                                        ->mutable_waypoint_assist_debug();
      waypoint_assist_debug->set_waypoint_availability(
          pb::WaypointAvailability::NOT_AVAILABLE);
      waypoint_assist_debug->set_reason_waypoint_assist_not_available(
          pb::ReasonWaypointAssistNotAvailable::END_OF_LANE_SEQUENCE);
    }
  }
}

void ReasonIfWaypointAssistNotAvailableByHardBoundary(
    const std::optional<
        speed::pb::FenceList>& /* decoupled_yielding_fence_list_in */,
    const RobotState& robot_state, pb::PlanningDebug* planning_debug) {
  // check if too close to hard boundary using
  // cautious_driving_reasoner.boundaries in planning_debug.
  if (!planning_debug) {
    return;
  }

  if (planning_debug->behavior_reasoner_debug().decoupled_maneuvers().empty()) {
    LOG(ERROR) << "decoupled_maneuver_debug not in planning_debug";
    return;
  }
  // Avoid override waypoint_availability when NOT_AVAILABLE has been set.
  if (planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .waypoint_availability() ==
          pb::WaypointAvailability::NOT_AVAILABLE &&
      planning_debug->world_model_debug()
              .waypoint_assist_debug()
              .reason_waypoint_assist_not_available() !=
          pb::ReasonWaypointAssistNotAvailable::NOT_SET) {
    return;
  }
  const auto& decoupled_maneuver_debug =
      planning_debug->behavior_reasoner_debug().decoupled_maneuvers(0);
  // find the selected identifier for the trajectory.
  std::string selected_label;
  bool is_close_to_hard_boundary = false;
  const auto& traj_meta_debugs =
      decoupled_maneuver_debug.trajectory_selection_debug()
          .trajectory_meta_debugs();
  for (const auto& traj_meta : traj_meta_debugs) {
    if (traj_meta.is_selected()) {
      selected_label = traj_meta.label();
      break;
    }
  }
  if (selected_label.empty()) {
    LOG(ERROR) << "selected_label not found:" << selected_label;
    return;
  }
  // find the selected identifier's reasoning info in speed_generator_debug.
  for (const auto& single_lane_sequence_plan_debug :
       decoupled_maneuver_debug.single_lane_sequence_plan_debug()) {
    for (const auto& single_intention_debug :
         single_lane_sequence_plan_debug.single_intention_plan_debug()) {
      if (single_intention_debug.candidate_identifier() == selected_label) {
        // check if close to boundary using boundary info in cautious reasoner.
        const auto& boundary_info =
            single_intention_debug.speed_generator_debug()
                .speed_reasoning_debug()
                .reasoner_debug()
                .cautious_driving_reasoner()
                .boundaries();
        for (const auto& boundary : boundary_info) {
          if (boundary.limits().empty()) {
            continue;
          }
          const double ego_len = robot_state.GetLength();
          is_close_to_hard_boundary =
              std::any_of(boundary.limits().begin(), boundary.limits().end(),
                          [ego_len](const auto& limit_params) {
                            return limit_params.min_discomfort_speed() <
                                       math::constants::kEpsilon &&
                                   limit_params.end_ra_arclength() < ego_len;
                          });
          if (is_close_to_hard_boundary) {
            break;
          }
        }
        break;
      }
    }
  }
  if (is_close_to_hard_boundary) {
    auto* waypoint_assist_debug = planning_debug->mutable_world_model_debug()
                                      ->mutable_waypoint_assist_debug();
    waypoint_assist_debug->set_waypoint_availability(
        pb::WaypointAvailability::NOT_AVAILABLE);
    waypoint_assist_debug->set_reason_waypoint_assist_not_available(
        pb::ReasonWaypointAssistNotAvailable::CLOSE_TO_HARD_BOUNDARY);
  }
}

// Accessor for current lane in assist stuck detect.
const pnc_map::Lane* GetCurrentLane(
    const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence) {
  const math::geometry::Point2d& ego_pose =
      robot_state.current_state_snapshot().front_bumper_position();
  const auto current_lane_iter = std::find_if(
      last_selected_lane_sequence.cbegin(), last_selected_lane_sequence.cend(),
      [&ego_pose](const pnc_map::Lane* lane_ptr) {
        return math::geometry::Within(ego_pose, lane_ptr->border());
      });
  if (current_lane_iter == last_selected_lane_sequence.cend()) {
    return nullptr;
  }
  return *current_lane_iter;
}

void UpdatePullOutConfirmDebug(
    const pb::PullOutJumpOutSeed& pull_out_jump_out_seed,
    const speed::pb::PulloutReasonerSeed& pullout_reasoner_seed,
    const std::optional<double>& start_position_distance,
    bool is_ego_position_on_route_ready_for_pull_out,
    const GlobalRouteSolution* global_route_solution,
    bool is_ego_start_close_to_stop,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lanes_for_pull_out_jump_out,
    const std::vector<const pnc_map::Lane*>& drivable_physical_lanes,
    bool should_generate_pull_out_jump_out_sequence,
    pb::PullOutConfirmDebug* pull_out_confirm_debug) {
  if (pull_out_confirm_debug == nullptr) {
    return;
  }
  pull_out_confirm_debug->mutable_seed_debug()->CopyFrom(pullout_reasoner_seed);
  if (start_position_distance.has_value()) {
    pull_out_confirm_debug->set_order_start_position_distance(
        start_position_distance.value());
  } else {
    pull_out_confirm_debug->set_order_start_position_distance(
        std::numeric_limits<double>::max());
  }
  pull_out_confirm_debug->set_is_ego_position_on_route_ready_for_pull_out(
      is_ego_position_on_route_ready_for_pull_out);
  *pull_out_confirm_debug->mutable_ride_start_pose() =
      global_route_solution->ride_start_pose();
  pull_out_confirm_debug->set_destination_distance(
      global_route_solution->route_solution().total_dist_m());
  pull_out_confirm_debug->set_is_ride_query(
      !DCHECK_NOTNULL(global_route_solution)->is_from_replan_query());
  pull_out_confirm_debug->set_is_ego_start_close_to_stop(
      is_ego_start_close_to_stop);
  google::protobuf::RepeatedField<int64_t>* arrived_road_ids =
      pull_out_confirm_debug->mutable_arrived_road_ids_from_ride_start();
  for (const pnc_map::Road* road :
       global_route_solution->arrived_roads_from_ride_start()) {
    arrived_road_ids->Add(road->id());
  }
  pull_out_confirm_debug->mutable_pull_out_jump_out_seed()->CopyFrom(
      pull_out_jump_out_seed);
  pull_out_confirm_debug->set_has_pull_out_jump_out_current_lanes(
      !current_lanes_for_pull_out_jump_out.empty());
  pull_out_confirm_debug->set_has_drivable_physical_lanes(
      !drivable_physical_lanes.empty());
  pull_out_confirm_debug->set_should_generate_pull_out_jump_out_sequence(
      should_generate_pull_out_jump_out_sequence);
}

void UpdateSeedWithDecoupledManeuver(
    const pb::DecoupledManeuverSeed& decoupled_maneuver_seed,
    const voy::VehicleMode& vehicle_mode,
    const BehaviorDecision& behavior_decision, const pb::Trajectory& trajectory,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    int64_t snapshot_timestamp, pb::WorldModelSeed* mutable_world_model_seed) {
  TRACE_EVENT_SCOPE(
      planner, WorldModel_UpdateSeedWithDecoupledManeuver,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());

  UpdateLastTrajectorySeed(
      trajectory, Seed::Access<token::WorldModel_LastTrajectory>::MutableMsg(
                      SpeedCurrentFrame())
                      .get());
  mutable_world_model_seed->set_last_selected_maneuver_type(
      behavior_decision.maneuver_type);
  // Update last path curve2d, copy from the decoupled planner selected
  // trajectory's extended_path.
  mutable_world_model_seed->mutable_last_nominal_path()
      ->mutable_segments()
      ->Clear();
  mutable_world_model_seed->mutable_last_nominal_path()->CopyFrom(
      decoupled_maneuver_seed.extended_path());

  // Update last selected lane sequence from decoupled maneuver seed.
  mutable_world_model_seed->mutable_last_selected_lane_sequence()->CopyFrom(
      decoupled_maneuver_seed.selected_lane_sequence());

  const auto& lane_sequence_candidates =
      decoupled_maneuver_seed.lane_sequence_candidates();
  if (lane_sequence_candidates.selected_lane_seq_idx() <
      lane_sequence_candidates.lane_sequence_candidates_size()) {
    mutable_world_model_seed->mutable_last_selected_lane_sequence_candidate()
        ->CopyFrom(lane_sequence_candidates.lane_sequence_candidates().at(
            lane_sequence_candidates.selected_lane_seq_idx()));
  }
  // Update last pull out jump out current lane and lane sequence.
  UpdateLastPullOutJumpOutLaneSequenceInfo(
      behavior_decision.lane_sequence_candidates,
      Seed::Access<token::PullOutJumpOutSeed>::MutableMsg(SpeedCurrentFrame())
          .get());
  mutable_world_model_seed->mutable_lane_sequence_plan_init_state()->CopyFrom(
      decoupled_maneuver_seed.lane_sequence_plan_init_state());
  // Control needs those two fields.
  mutable_world_model_seed->set_last_target_steering_rad(
      trajectory.auxiliary_trajectory_directives()
          .target_stationary_steering_rad());
  mutable_world_model_seed->set_last_arclength_to_nearest_static_fence_m(
      trajectory.auxiliary_trajectory_directives()
          .arclength_to_nearest_static_fence_m());
  if (behavior_decision.dry_steering_target_angle) {
    mutable_world_model_seed->set_dry_steering_target_angle(
        behavior_decision.dry_steering_target_angle.value());
  } else {
    mutable_world_model_seed->clear_dry_steering_target_angle();
  }
  mutable_world_model_seed->set_last_vehicle_mode(vehicle_mode);
  mutable_world_model_seed->set_request_immediate_pull_over(
      behavior_decision.should_trigger_immediate_pull_over);
  mutable_world_model_seed->set_pull_over_progress(
      behavior_decision.pull_over_status_info.has_value()
          ? behavior_decision.pull_over_status_info->pull_over_progress
          : pb::PullOverProgress::kNotTriggered);
  mutable_world_model_seed->set_should_extend_lane_seq_behind_routing_dest(
      behavior_decision.pull_over_status_info.has_value()
          ? behavior_decision.pull_over_status_info
                ->should_extend_lane_seq_behind_routing_dest
          : false);
  mutable_world_model_seed->set_snapshot_timestamp(snapshot_timestamp);
  mutable_world_model_seed->set_mrm_progress(
      mrc_request_ptr != nullptr ? mrc_request_ptr->mrm_progress()
                                 : mrc::pb::MrcProgress::NONE);
  mutable_world_model_seed->set_mrm_type(mrc_request_ptr != nullptr
                                             ? mrc_request_ptr->mrm_type()
                                             : mrc::pb::MrmType::UNDEFINED);
}

void UpdatePlannerExecutionTime(double execute_time,
                                pb::WorldModelSeed* mutable_world_model_seed) {
  mutable_world_model_seed->set_plan_execute_time_ms(execute_time);
}

void UpdateLastTrajectorySeed(const pb::Trajectory& trajectory,
                              pb::Trajectory* mutable_trajectory) {
  mutable_trajectory->CopyFrom(trajectory);
}

void UpdateReplanFollowedPathType(
    const routing::pb::QueryFollowedPathType& replan_followed_path_type,
    pb::RouteReplanManagerSeed* mutable_replan_manager_seed) {
  mutable_replan_manager_seed->set_followed_path_type(
      replan_followed_path_type);
}

void UpdatePlanningRouteStatusDebug(
    const routing::pb::RouteStatus& route_status,
    const routing::pb::RouteSolution& route_solution, int64_t loopback_times,
    bool is_global_route_changed,
    pb::PlanningRouteStatusDebug* planning_route_status_debug) {
  if (!planning_route_status_debug) {
    return;
  }
  const pb::RouteStatusUpdateReason update_reason =
      planning_route_status_debug->update_reason();

  planning_route_status_debug->Clear();
  planning_route_status_debug->set_is_valid(route_status.is_valid());
  *planning_route_status_debug->mutable_proposed_route_solution() =
      route_solution;
  // Clear cost map in proposed route solution to save memory.
  planning_route_status_debug->mutable_proposed_route_solution()
      ->clear_cost_map();
  // Fill current route solution debug if current and proposed route solution
  // are different.
  if (route_status.proposed_route_solution().route_timestamp() !=
          route_status.current_route_solution().route_timestamp() &&
      update_reason != pb::kSpecialPlannerResponseRequestChangedToKeepStatic &&
      update_reason != pb::kImmediatePullOverSuccess) {
    *planning_route_status_debug->mutable_current_route_solution() =
        route_status.current_route_solution();
    // Clear cost map in current route solution to save memory.
    planning_route_status_debug->mutable_current_route_solution()
        ->clear_cost_map();
  }
  planning_route_status_debug->set_dist_to_dest_m(
      route_status.dist_to_dest_m());
  planning_route_status_debug->set_time_to_dest_s(
      route_status.time_to_dest_s());
  planning_route_status_debug->set_dist_to_dest_in_local_m(
      route_status.dist_to_dest_in_local_m());
  planning_route_status_debug->set_time_to_dest_in_local_s(
      route_status.time_to_dest_in_local_s());
  *planning_route_status_debug->mutable_immediate_pullover_info() =
      route_status.immediate_pullover_info();
  planning_route_status_debug->set_update_reason(update_reason);
  planning_route_status_debug->set_special_planner_response_request(
      route_status.special_planner_response_request());
  planning_route_status_debug->set_loopback_times(loopback_times);
  planning_route_status_debug->set_is_global_route_changed(
      is_global_route_changed);
}

void UpdateDestinationSuccessorSectionsInfoDebug(
    const std::vector<const pnc_map::Section*>&
        destination_successor_sections_for_pullover,
    double pullover_final_extend_section_percentage,
    pb::DestinationSuccessorSectionsInfoDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  for (const pnc_map::Section* section :
       destination_successor_sections_for_pullover) {
    debug->add_extended_section_ids(section->id());
  }
  debug->set_pullover_final_extend_section_percentage(
      pullover_final_extend_section_percentage);
}

void InitAllMLModels() {
  // Init ml pull over model.
  if (FLAGS_planning_enable_ml_pull_over_gap_generator) {
    CHECK(inference::Inference::Init({::pb::PULL_OVER_GAP_GENERATOR, {}}))
        << "Init ml pull over gap model failed.";
  }
}

void PostRtEventIfRegionalSectionsLocatorInvalid(
    const std::vector<const pnc_map::Section*>& potential_sections,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    pb::PotentialSectionsSource potential_sections_source,
    bool is_all_lanes_near_ego_in_section_vertices, bool is_in_auto_mode,
    const routing::utility::OrderInfo& order_info) {
  if (!is_in_auto_mode) {
    return;
  }

  const std::string invalid_info_head =
      "Regional sections locator is invalid: ";
  if (regional_section_vertices.empty()) {
    const std::string err_info =
        "Fail to update regional section vertices in RegionalSectionsLocator!";
    rt_event::PostRtEvent<
        rt_event::planner::SectionLocatorEmptyRegionalSectionVertices>(
        err_info);
    LOG(ERROR) << invalid_info_head << err_info;
    return;
  }

  if (std::all_of(regional_section_vertices.begin(),
                  regional_section_vertices.end(),
                  [](const pnc_map::SectionVertex& section_vertex) {
                    return !section_vertex.is_exit;
                  })) {
    const std::string err_info =
        "Fail to get any exit section in RegionalSectionsLocator!";
    rt_event::PostRtEvent<rt_event::planner::SectionLocatorEmptyExtiSections>(
        err_info);
    LOG(ERROR) << invalid_info_head << err_info;
  }

  // Initialize a map to track the invalidation reasons for invalid sections
  // locator.
  std::map<std::string, bool> rt_event_info_map;
  // Check if section vertices contain any potential sections.
  bool has_potential_sections_in_section_vertices = false;
  for (const auto* section : potential_sections) {
    DCHECK(section != nullptr);
    if (std::any_of(regional_section_vertices.begin(),
                    regional_section_vertices.end(),
                    [section](const auto& section_vertex) {
                      return DCHECK_NOTNULL(section_vertex.section)->id() ==
                             section->id();
                    })) {
      has_potential_sections_in_section_vertices = true;
      break;
    }
  }
  if (!has_potential_sections_in_section_vertices) {
    rt_event_info_map.try_emplace(kSectionVerticesMissingAllPotentialSections,
                                  true);
    LOG(ERROR) << invalid_info_head
               << kSectionVerticesMissingAllPotentialSections;
  }

  // Check if the potential sections were updated from last selected lane
  // sequence.
  if (potential_sections_source != pb::kFromLastLaneSequence) {
    rt_event_info_map.try_emplace(kPotentialSectionsNotFromLastLaneSequence,
                                  true);
    LOG(ERROR) << invalid_info_head
               << kPotentialSectionsNotFromLastLaneSequence;
  }

  // Check if all lanes near ego pose are in section vertices.
  if (!is_all_lanes_near_ego_in_section_vertices) {
    rt_event_info_map.try_emplace(kLanesIn10mRangeNotAllInSectionVertices,
                                  true);
    LOG(ERROR) << invalid_info_head << kLanesIn10mRangeNotAllInSectionVertices;
  }

  // If any invalidation condition is met, post an RT event with the relevant
  // invalid information.
  if (!rt_event_info_map.empty()) {
    // Notes(yuzehao): Provisionally, we only add the order info json into rt
    // event which is a new addition. For already existing rt event, we don't
    // add order info.
    routing::utility::AddRtEventAsJsonFromMap<
        rt_event::planner::RegionalSectionLocatorInvalid>(order_info,
                                                          rt_event_info_map);
    LOG(ERROR)
        << "Got invalid results from regional sections locator. Order_info: "
        << order_info.GetOrderString();
  }
}

void PostRtEventIfCurrentLaneInvalid(
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pnc_map::PncMapService* pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    bool is_current_lane_for_regional_path, bool is_in_auto_mode,
    bool is_waypoint_assist_invoked, bool was_reversing,
    const routing::utility::OrderInfo& order_info) {
  if (!is_in_auto_mode) {
    return;
  }

  // Add a suffix for every specific event reason if the ego is in some special
  // cases.
  std::string suffix;
  if (is_waypoint_assist_invoked) {
    suffix += "_RA";
  }
  if (was_reversing) {
    suffix += "_reverse";
  }

  if (current_lane_candidates.empty()) {
    const std::string invalid_info_head =
        is_current_lane_for_regional_path
            ? "Current lane for regional path is invalid: "
            : "Current lane for waypoint graph is invalid: ";
    if (is_current_lane_for_regional_path) {
      routing::utility::AddRtEventAsJson<
          rt_event::planner::CurrLaneResInvalidForRegionalPath>(
          order_info, kCurrentLaneEmptyResult + suffix, true);
    } else {
      routing::utility::AddRtEventAsJson<
          rt_event::planner::CurrLaneResInvalidForWaypointGraph>(
          order_info, kCurrentLaneEmptyResult + suffix, true);
    }
    LOG(ERROR) << invalid_info_head << kCurrentLaneEmptyResult + suffix
               << ". Order_info: " << order_info.GetOrderString();
    return;
  }

  if (pnc_map_service == nullptr ||
      last_selected_lane_sequence_candidate == nullptr) {
    return;
  }

  // Lanes with the jump-out type `STUCK_AVOIDANCE_JUMP_OUT` may lie outside the
  // last selected section sequence, it is a special lane type. Since we only
  // consider the validation of normal current lanes, lanes with
  // `STUCK_AVOIDANCE_JUMP_OUT` are filtered out.
  // TODO(yuzehao): After optimizing the continuity of the current lane, this
  // filtering step can be removed.
  std::vector<const lane_candidate::CurrentLaneCandidate*>
      currnet_lanes_without_sutck_avoidance_JO_lane;
  currnet_lanes_without_sutck_avoidance_JO_lane.reserve(
      current_lane_candidates.size());
  for (const auto& candidate : current_lane_candidates) {
    if (candidate.IsStuckAvoidanceJumpOut()) {
      continue;
    }
    currnet_lanes_without_sutck_avoidance_JO_lane.push_back(&candidate);
  }
  if (currnet_lanes_without_sutck_avoidance_JO_lane.empty()) {
    return;
  }

  // Initialize a map to track the invalidation reasons for the current lane.
  std::map<std::string, bool> rt_event_info_map;
  const std::string invalid_info_head =
      is_current_lane_for_regional_path
          ? "Current lane for regional path is invalid: "
          : "Current lane for waypoint graph is invalid: ";

  // Check if there is any current lane candidate outside the potential sections
  // selected in regional sections locator.
  if (!locator_potential_sections.empty()) {
    for (const auto* candidate :
         currnet_lanes_without_sutck_avoidance_JO_lane) {
      const bool is_in_locator_potential_sections = std::any_of(
          locator_potential_sections.begin(), locator_potential_sections.end(),
          [&candidate](const pnc_map::Section* section) {
            return DCHECK_NOTNULL(candidate->lane()->section())->id() ==
                   DCHECK_NOTNULL(section)->id();
          });
      if (!is_in_locator_potential_sections) {
        rt_event_info_map.try_emplace(
            kCurrentLaneNotInLocatorPotentialSections + suffix, true);
        LOG(ERROR) << invalid_info_head
                   << kCurrentLaneNotInLocatorPotentialSections + suffix;
        break;
      }
    }
  }

  // Check if there is any current lane candidate outside the last selected lane
  // sequence when the last selected lane sequence is in lane follow and not in
  // jump out.
  if (lane_selection::IsLaneFollowType(
          last_selected_lane_sequence_candidate->lane_sequence_type()) &&
      !lane_selection::IsJumpOutLaneSequence(
          *last_selected_lane_sequence_candidate)) {
    std::string JO_suffix;
    bool has_lanes_outside_last_selected_lane_sequence = std::any_of(
        currnet_lanes_without_sutck_avoidance_JO_lane.begin(),
        currnet_lanes_without_sutck_avoidance_JO_lane.end(),
        [&JO_suffix](const lane_candidate::CurrentLaneCandidate* candidate) {
          // Do not return true if it is a fork lane.
          const bool is_outside_last_lane_sequence =
              !candidate->is_in_last_selected_lane_sequence() &&
              !DCHECK_NOTNULL(candidate->lane())->IsForkLane();
          if (is_outside_last_lane_sequence && JO_suffix.empty() &&
              candidate->IsPullOutJumpOut()) {
            JO_suffix = "_JO";
          }
          return is_outside_last_lane_sequence;
        });
    if (has_lanes_outside_last_selected_lane_sequence) {
      rt_event_info_map.try_emplace(
          kCurrentLaneNotInLastLaneSequence + suffix + JO_suffix, true);
      LOG(ERROR) << invalid_info_head
                 << kCurrentLaneNotInLastLaneSequence + suffix + JO_suffix;
    }
  }

  // Check if there is any current lane candidate outside the last selected
  // section sequence.
  if (!last_selected_lane_sequence.empty()) {
    const auto last_selected_section_sequence =
        GetSectionsFromLanes(last_selected_lane_sequence,
                             /*prevent_loop =*/true);
    for (const auto* candidate :
         currnet_lanes_without_sutck_avoidance_JO_lane) {
      const bool is_in_last_section_sequence = std::any_of(
          last_selected_section_sequence.begin(),
          last_selected_section_sequence.end(),
          [&candidate](const pnc_map::Section* section) {
            return DCHECK_NOTNULL(candidate->lane()->section())->id() ==
                   DCHECK_NOTNULL(section)->id();
          });
      if (!is_in_last_section_sequence) {
        rt_event_info_map.try_emplace(
            kCurrentLaneNotInLastSectionSequence + suffix, true);
        LOG(ERROR) << invalid_info_head
                   << kCurrentLaneNotInLastSectionSequence + suffix;
        break;
      }
    }
  }

  const auto* last_selected_current_lane = pnc_map_service->GetLaneById(
      last_selected_lane_sequence_candidate->current_lane_id());
  if (last_selected_current_lane != nullptr) {
    // Check if current lane candidates are flickering to the predecessor
    // section (or successor section when ego is reversing) of the last selected
    // current lane.
    const bool is_lanes_flickered_to_predecessor_section = std::any_of(
        currnet_lanes_without_sutck_avoidance_JO_lane.begin(),
        currnet_lanes_without_sutck_avoidance_JO_lane.end(),
        [last_selected_current_lane,
         was_reversing](const lane_candidate::CurrentLaneCandidate* candidate) {
          const auto* current_section =
              DCHECK_NOTNULL(candidate->lane()->section());
          const auto* last_section =
              DCHECK_NOTNULL(last_selected_current_lane->section());
          return was_reversing
                     ? last_section->IsSuccessor(current_section->id())
                     : current_section->IsSuccessor(last_section->id());
        });
    if (is_lanes_flickered_to_predecessor_section) {
      rt_event_info_map.try_emplace(
          kCurrentLaneFlickerToPredecessorSection + suffix, true);
      LOG(ERROR) << invalid_info_head
                 << kCurrentLaneFlickerToPredecessorSection + suffix;
    }

    // If the last selected current lane is in lane-follow mode and not in
    // jump-out mode, check if current lane candidates are flickering to the
    // neighbor of the last selected current lane.
    if (lane_selection::IsLaneFollowType(
            last_selected_lane_sequence_candidate->lane_sequence_type()) &&
        !lane_selection::IsJumpOutLaneSequence(
            *last_selected_lane_sequence_candidate)) {
      std::string JO_suffix;
      const bool is_lanes_flickered_to_neighbor_lane = std::any_of(
          currnet_lanes_without_sutck_avoidance_JO_lane.begin(),
          currnet_lanes_without_sutck_avoidance_JO_lane.end(),
          [last_selected_current_lane,
           &JO_suffix](const lane_candidate::CurrentLaneCandidate* candidate) {
            // Do not return true if it is a fork lane.
            const bool is_lane_flickered_to_neighbor =
                DCHECK_NOTNULL(candidate->lane())
                    ->IsNeighbor(*last_selected_current_lane) &&
                !candidate->lane()->IsForkLane();
            if (is_lane_flickered_to_neighbor && JO_suffix.empty() &&
                candidate->IsPullOutJumpOut()) {
              JO_suffix = "_JO";
            }
            return is_lane_flickered_to_neighbor;
          });
      if (is_lanes_flickered_to_neighbor_lane) {
        rt_event_info_map.try_emplace(
            kCurrentLaneFlickerToNeighborLane + suffix + JO_suffix, true);
        LOG(ERROR) << invalid_info_head
                   << kCurrentLaneFlickerToNeighborLane + suffix + JO_suffix;
      }
    }
  }

  // If any invalidation condition is met, post an RT event with the relevant
  // invalid information.
  if (!rt_event_info_map.empty()) {
    if (is_current_lane_for_regional_path) {
      routing::utility::AddRtEventAsJsonFromMap<
          rt_event::planner::CurrLaneResInvalidForRegionalPath>(
          order_info, rt_event_info_map);
    } else {
      routing::utility::AddRtEventAsJsonFromMap<
          rt_event::planner::CurrLaneResInvalidForWaypointGraph>(
          order_info, rt_event_info_map);
    }
    LOG(ERROR)
        << "Got invalid results from current lane associator, order_info: "
        << order_info.GetOrderString();
  }
}

void PostRtEventIfCurrLaneForWaypointGraphInvalid(
    const std::vector<const pnc_map::Lane*>& current_lane_for_regional_path,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_for_waypoint_graph,
    const std::vector<const pnc_map::Section*>& locator_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pnc_map::PncMapService* pnc_map_service,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    bool is_in_auto_mode, bool is_waypoint_assist_invoked, bool was_reversing,
    const routing::utility::OrderInfo& order_info) {
  if (!is_in_auto_mode) {
    return;
  }

  // Add a suffix for every specific event reason if the ego is in some special
  // cases.
  std::string suffix;
  if (is_waypoint_assist_invoked) {
    suffix += "_RA";
  }
  if (was_reversing) {
    suffix += "_reverse";
  }

  if (!current_lane_for_regional_path.empty() &&
      !current_lane_for_waypoint_graph.empty()) {
    const bool is_subset_of_regional_path_current_lanes =
        std::all_of(current_lane_for_waypoint_graph.begin(),
                    current_lane_for_waypoint_graph.end(),
                    [&current_lane_for_regional_path](
                        const lane_candidate::CurrentLaneCandidate&
                            waypoint_graph_candidate) {
                      const int64_t waypoint_graph_lane_id =
                          DCHECK_NOTNULL(waypoint_graph_candidate.lane())->id();
                      return std::any_of(
                          current_lane_for_regional_path.begin(),
                          current_lane_for_regional_path.end(),
                          [&waypoint_graph_lane_id](
                              const pnc_map::Lane* regional_path_lane) {
                            return DCHECK_NOTNULL(regional_path_lane)->id() ==
                                   waypoint_graph_lane_id;
                          });
                    });
    if (!is_subset_of_regional_path_current_lanes) {
      routing::utility::AddRtEventAsJson<
          rt_event::planner::CurrLaneResInvalidForWaypointGraph>(
          order_info,
          kHasWaypointGraphCurrLaneNotInRegionalPathCurrLanes + suffix, true);
      LOG(ERROR) << "Current lane for waypoint graph is invalid: "
                 << kHasWaypointGraphCurrLaneNotInRegionalPathCurrLanes + suffix
                 << ". Order_info: " << order_info.GetOrderString();
    }
  }

  PostRtEventIfCurrentLaneInvalid(
      current_lane_for_waypoint_graph, locator_potential_sections,
      last_selected_lane_sequence, pnc_map_service,
      last_selected_lane_sequence_candidate,
      /*is_current_lane_for_regional_path=*/false, is_in_auto_mode,
      is_waypoint_assist_invoked, was_reversing, order_info);
}

bool MLTrajectoryInitStateHasDivergedFromPlanner(
    const RobotState& robot_state,
    const pb::TrajectoryGuiderOutput& trajectory_guider_output) {
  // Get timestamp.
  const int64_t plan_init_state_time_ms =
      robot_state.plan_init_state_snapshot().timestamp();
  if (trajectory_guider_output.trajectory_output().candidates_size() == 0 ||
      trajectory_guider_output.trajectory_output()
              .candidates(0)
              .states_size() == 0) {
    return true;
  }
  const auto& states =
      trajectory_guider_output.trajectory_output().candidates(0).states();
  const auto right_state_iter = std::find_if(
      states.begin(), states.end(),
      [&plan_init_state_time_ms](const pb::State& state) {
        return state.trajectory_pose().timestamp() > plan_init_state_time_ms;
      });
  // If the ml trajectory is too old, we put it as a diverged frame.
  if (right_state_iter == states.end()) {
    return true;
  }
  // The ml init state timestamp could be larger than planner init state.
  const auto left_state_iter = right_state_iter == states.begin()
                                   ? right_state_iter
                                   : right_state_iter - 1;
  const auto& left_pose = left_state_iter->trajectory_pose();
  const auto& right_pose = right_state_iter->trajectory_pose();
  // Ratio should be in 0 and 1.
  const double interpolation_ratio = math::GetInterpolationRatio(
      left_pose.timestamp(), right_pose.timestamp(), plan_init_state_time_ms);
  DCHECK_LE(interpolation_ratio, 1.0);
  DCHECK_GE(interpolation_ratio, 0.0);

  const PlanInitState& current_plan_init_state = robot_state.plan_init_state();
  // Check speed diff.
  const double init_speed = current_plan_init_state.state[StateIndex::SPEED];
  const double ml_speed = math::LinearInterpolate(
      left_pose.speed(), right_pose.speed(), interpolation_ratio);
  if (std::abs(init_speed - ml_speed) >
      constants::kMLTrajectoryFrameDropSpeedDeltaThresholdInMps) {
    return true;
  }
  // Check accel diff.
  const double init_accel = current_plan_init_state.state[StateIndex::ACCEL];
  const double ml_accel = math::LinearInterpolate(
      left_pose.accel(), right_pose.accel(), interpolation_ratio);
  if (std::abs(init_accel - ml_accel) >
      constants::kMLTrajectoryFrameDropAccelerationDeltaThreshold) {
    return true;
  }
  // Check steering diff.
  const double steering_gear_ratio =
      robot_state.juke_integrated_model_with_shape()
          .param()
          .measurement()
          .steering_gear_ratio();
  const double ml_steering = math::LinearInterpolate(
      left_pose.steering(), right_pose.steering(), interpolation_ratio);
  const double init_steering = current_plan_init_state.state[StateIndex::DELTA];
  if (math::Radian2Degree(std::abs(init_steering - ml_steering) *
                          steering_gear_ratio) >
      constants::
          kMLTrajectoryFrameDropSteeringWheelAngleDeltaThresholdInDegree) {
    return true;
  }

  return false;
}

bool IsEgoOnExpressway(const ::voy::Pose& ego_pose,
                       const pnc_map::Lane* current_lane_from_last_cycle,
                       const pnc_map::PncMapService* pnc_map_service) {
  if (current_lane_from_last_cycle != nullptr) {
    return current_lane_from_last_cycle->section()
               ->road()
               ->proto()
               .road_class() == hdmap::Road::EXPRESSWAY;
  }

  if (pnc_map_service == nullptr) {
    return false;
  }

  const std::vector<const pnc_map::Lane*> near_lanes =
      pnc_map_service->GetNearLanesWithPose(
          ego_pose, constants::kLaneSearchingRadiusInMeter,
          /*max_heading_diff=*/M_PI_2,
          /*prefer_overlapped_lanes=*/true);
  return std::any_of(
      near_lanes.begin(), near_lanes.end(), [](const auto* lane) {
        return lane && lane->section() && lane->section()->road() &&
               lane->section()->road()->proto().road_class() ==
                   hdmap::Road::EXPRESSWAY;
      });
}

}  // namespace planner
