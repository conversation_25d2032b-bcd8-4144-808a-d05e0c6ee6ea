#include <gtest/gtest.h>

#include "common_math_library/debounce_method.h"

TEST(CMLSetResetFlipFlop, CMLSetResetFlipFlopTest) {
  bool set = true;
  bool reset = false;
  bool output = false;

  // case 1: set is true, reset is false, last output is false
  // output is true
  set = true;
  reset = false;
  output = false;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_TRUE(output);

  // case 2: set is true, reset is false, last output is true
  // output is true
  set = true;
  reset = false;
  output = true;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_TRUE(output);

  // case 3: set is false, reset is false, last output is true
  // output is false
  set = false;
  reset = false;
  output = true;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_TRUE(output);

  // case 4: set is false, reset is false, last output is false
  // output is false
  set = false;
  reset = false;
  output = false;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_FALSE(output);

  // case 5: set is true, reset is true, last output is false
  // output is false
  set = true;
  reset = true;
  output = false;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_FALSE(output);

  // case 6: set is true, reset is true, last output is true
  // output is false
  set = true;
  reset = true;
  output = true;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_FALSE(output);

  // case 7: set is true, reset is true, last output is false
  // output is false
  set = true;
  reset = true;
  output = false;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_FALSE(output);

  // case 8: set is false, reset is true, last output is false
  // output is false
  set = false;
  reset = true;
  output = false;
  CML_SetResetFlipFlop(set, reset, &output);
  ASSERT_FALSE(output);
}

TEST(CMLTurnOnDelay, CMLTurnOnDelayTest) {
  bool input = true;
  float delay_time = 1.0F;
  float task_cycle_time = 0.01F;
  float timer = 0.0F;
  bool output = false;

  // case 1: input is true, last output is false, not reaching the required
  // delay time, so output is false
  for (int i = 0; i < 10; ++i) {
    CML_TurnOnDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, 0.1F, task_cycle_time * 0.1F);
  ASSERT_FALSE(output);

  // case 2: input is true, last output is false, just reaching the required
  // delay time, so output is true
  timer = 0.0F;
  output = false;
  for (int i = 0; i < static_cast<int>(delay_time / task_cycle_time); ++i) {
    CML_TurnOnDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, delay_time, task_cycle_time * 0.1F);
  CML_TurnOnDelay(input, delay_time, task_cycle_time, &timer, &output);
  ASSERT_TRUE(output);

  // case 3: input is true, last output is false, reaching the required
  // delay time, so output is false
  timer = 0.0F;
  output = false;
  for (int i = 0; i < 1000; ++i) {
    CML_TurnOnDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, 10.0F, task_cycle_time * 0.1F);
  ASSERT_TRUE(output);

  // case 4: input is false, last output is false, so output is always false
  input = false;
  timer = 0.0F;
  output = false;
  for (int i = 0; i < 1000; ++i) {
    CML_TurnOnDelay(input, delay_time, task_cycle_time, &timer, &output);
    ASSERT_NEAR(timer, 0.0F, task_cycle_time * 0.1F);
    ASSERT_FALSE(output);
  }
}

TEST(CMLTurnOffDelay, CMLTurnOffDelayTest) {
  bool input = false;
  float delay_time = 1.0F;
  float task_cycle_time = 0.01F;
  float timer = 0.0F;
  bool output = true;

  // case 1: input is false, last output is true, not reaching the required
  // delay time, so output is true
  for (int i = 0; i < 10; ++i) {
    CML_TurnOffDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, 0.1F, task_cycle_time * 0.1F);
  ASSERT_TRUE(output);

  // case 2: input is false, last output is true, just reaching the required
  // delay time, so output is false
  timer = 0.0F;
  output = true;
  for (int i = 0; i < static_cast<int>(delay_time / task_cycle_time); ++i) {
    CML_TurnOffDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, delay_time, task_cycle_time * 0.1F);
  CML_TurnOffDelay(input, delay_time, task_cycle_time, &timer, &output);
  ASSERT_FALSE(output);

  // case 3: input is false, last output is true, reaching the required
  // delay time, so output is false
  timer = 0.0F;
  output = true;
  for (int i = 0; i < 1000; ++i) {
    CML_TurnOffDelay(input, delay_time, task_cycle_time, &timer, &output);
  }
  ASSERT_NEAR(timer, 10.0F, task_cycle_time * 0.1F);
  ASSERT_FALSE(output);

  // case 4: input is true, last output is true, so output is always true
  input = true;
  timer = 0.0F;
  output = true;
  for (int i = 0; i < 1000; ++i) {
    CML_TurnOffDelay(input, delay_time, task_cycle_time, &timer, &output);
    ASSERT_NEAR(timer, 0.0F, task_cycle_time * 0.1F);
    ASSERT_TRUE(output);
  }
}

TEST(CMLHysteresis, CMLHysteresisTest) {
  float input = -2.0F;
  float lower_threshold = -1.0F;
  float upper_threshold = 1.0F;
  bool output = false;

  // case 1: input is below lower threshold, last output is false
  // output is false
  input = -2.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = false;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_FALSE(output);

  // case 2: input is below lower threshold, last output is true
  // output is false
  input = -2.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = true;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_FALSE(output);

  // case 3: input is above upper threshold, last output is false
  // output is true
  input = 2.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = false;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_TRUE(output);

  // case 4: input is above upper threshold, last output is true
  // output is true
  input = 2.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = true;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_TRUE(output);

  // case 5: input is between lower and upper threshold, last output is false
  // output is false
  input = 0.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = false;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_FALSE(output);

  // case 6: input is between lower and upper threshold, last output is true
  // output is true
  input = 0.0F;
  lower_threshold = -1.0F;
  upper_threshold = 1.0F;
  output = true;
  CML_Hysteresis(input, lower_threshold, upper_threshold, &output);
  ASSERT_TRUE(output);
}

TEST(CMLBilateralHysteresis, CMLBilateralHysteresisTest) {
  float input = -2.0F;
  float min = -1.0F;
  float max = 1.0F;
  float hys = 0.5F;
  bool output = false;

  // case 1: input is below min - hys, last output is false
  // output is false
  input = -2.0F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = false;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_FALSE(output);

  // case 2: input is below min - hys, last output is true
  // output is false
  input = -2.0F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = true;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_FALSE(output);

  // case 3: input is between min - hys and min, last output is false
  // output is false
  input = -1.5F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = false;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_FALSE(output);

  // case 4: input is between min - hys and min, last output is true
  // output is true
  input = -1.4F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = true;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_TRUE(output);

  // case 5: input is between min and max, last output is false
  // output is true
  input = 0.0F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = false;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_TRUE(output);

  // case 6: input is between min and max, last output is true
  // output is true
  input = 0.0F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = true;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_TRUE(output);

  // case 7: input is between max and max + hys, last output is false
  // output is false
  input = 1.4F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = false;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_FALSE(output);

  // case 8: input is between max and max + hys, last output is true
  // output is true
  input = 1.4F;
  min = -1.0F;
  max = 1.0F;
  hys = 0.5F;
  output = true;
  CML_BilateralHysteresis(input, min, max, hys, &output);
  ASSERT_TRUE(output);
}
