/*
 * File: CameraSigInput_data.c
 *
 * Code generated for Simulink model 'CameraSigInput'.
 *
 * Model version                  : 1.22
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Thu Dec 12 16:01:01 2024
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "CameraSigInput.h"
#include "CameraSigInput_private.h"

/* Block parameters (default storage) */
P_CameraSigInput_T CameraSigInput_P = {
  /* Computed Parameter: CSI_LaneInfo_Y0
   * Referenced by: '<S1>/CSI_LaneInfo'
   */
  {
    0U,                                /* CSI_CalibrationStatus */
    0U,                                /* CSI_LaneChangeStatus */
    0.0F,                              /* CSI_LaneTimeStamp */
    0U,                                /* CSI_MarkerTypeLf */
    0U,                                /* CSI_LaneIDLf */
    0U,                                /* CSI_MeasureTypeLf */
    0.0F,                              /* CSI_ExistProbLf */
    0.0F,                              /* CSI_PosY0Lf */
    0.0F,                              /* CSI_HeadingLf */
    0.0F,                              /* CSI_CurvatureLf */
    0U,                                /* CSI_ColorTypeLf */
    0.0F,                              /* CSI_CrvRateLf */
    0.0F,                              /* CSI_MakerWidthLf */
    0.0F,                              /* CSI_PosXStartLf */
    0.0F,                              /* CSI_PosYStartStdLf */
    0.0F,                              /* CSI_PosXEndLf */
    0.0F,                              /* CSI_PosXEndStdLf */
    0U,                                /* CSI_ParseConfLf */
    0U,                                /* CSI_ObstacleFlgLf */
    0U,                                /* CSI_PolyfitRmseLf */
    0U,                                /* CSI_LaneIDRi */
    0U,                                /* CSI_MeasureTypeRi */
    0U,                                /* CSI_MarkerTypeRi */
    0.0F,                              /* CSI_ExistProbRi */
    0.0F,                              /* CSI_PosY0Ri */
    0.0F,                              /* CSI_HeadingAngleRi */
    0.0F,                              /* CSI_CurvatureRi */
    0U,                                /* CSI_ColorTypeRi */
    0.0F,                              /* CSI_CrvRateRi */
    0.0F,                              /* CSI_MakerWidthRi */
    0.0F,                              /* CSI_PosXStartRi */
    0.0F,                              /* CSI_PosYStartStdRi */
    0.0F,                              /* CSI_PosXEndRi */
    0.0F,                              /* CSI_PosXEndStdRi */
    0U,                                /* CSI_ParseConfRi */
    0U,                                /* CSI_PolyfitRmseRi */
    0U,                                /* CSI_ObstacleFlgRi */
    0U,                                /* CSI_LaneIDLfAdj */
    0U,                                /* CSI_MeasureTypeLfAdj */
    0U,                                /* CSI_MarkerTypeLfAdj */
    0.0F,                              /* CSI_ExistProbLfAdj */
    0.0F,                              /* CSI_PosY0LfAdj */
    0.0F,                              /* CSI_HeadingLfAdj */
    0.0F,                              /* CSI_CurvatureLfAdj */
    0.0F,                              /* CSI_CrvRateLfAdj */
    0U,                                /* CSI_ColorTypeLfAdj */
    0.0F,                              /* CSI_MakerWidthLfAdj */
    0.0F,                              /* CSI_PosXStartLfAdj */
    0.0F,                              /* CSI_PosYStartStdLfAdj */
    0.0F,                              /* CSI_PosXEndLfAdj */
    0.0F,                              /* CSI_PosXEndStdLfAdj */
    0U,                                /* CSI_ObstacleFlgLfAdj */
    0U,                                /* CSI_PolyfitRmseLfAdj */
    0U,                                /* CSI_ParseConfLfAdj */
    0U,                                /* CSI_LaneIDRiAdj */
    0U,                                /* CSI_MeasureTypeRiAdj */
    0U,                                /* CSI_MarkerTypeRiAdj */
    0.0F,                              /* CSI_ExistProbRiAdj */
    0.0F,                              /* CSI_PosY0RiAdj */
    0.0F,                              /* CSI_HeadingAngleRiAdj */
    0.0F,                              /* CSI_CurvatureRiAdj */
    0.0F,                              /* CSI_CrvRateRiAdj */
    0U,                                /* CSI_ColorTypeRiAdj */
    0.0F,                              /* CSI_MakerWidthRiAdj */
    0.0F,                              /* CSI_PosXStartRiAdj */
    0.0F,                              /* CSI_PosYStartStdRiAdj */
    0.0F,                              /* CSI_PosXEndRiAdj */
    0.0F,                              /* CSI_PosXEndStdRiAdj */
    0U,                                /* CSI_ParseConfRiAdj */
    0U,                                /* CSI_ObstacleFlgRiAdj */
    0U,                                /* CSI_PolyfitRmseRiAdj */
    0U,                                /* CSI_ParseConfLfRe */
    0U,                                /* CSI_PolyfitRmseLfRe */
    0U,                                /* CSI_ObstacleFlgLfRe */
    0U,                                /* CSI_MeasureTypeLfRe */
    0U,                                /* CSI_MarkerTypeLfRe */
    0.0F,                              /* CSI_ExistProbLfRe */
    0.0F,                              /* CSI_PosY0LfRe */
    0.0F,                              /* CSI_HeadingLfRe */
    0.0F,                              /* CSI_CurvatureLfRe */
    0.0F,                              /* CSI_CrvRateLfRe */
    0U,                                /* CSI_ColorTypeLfRe */
    0.0F,                              /* CSI_MakerWidthLfRe */
    0.0F,                              /* CSI_PosXStartLfRe */
    0.0F,                              /* CSI_PosYStartStdLfRe */
    0.0F,                              /* CSI_PosXEndLfRe */
    0.0F,                              /* CSI_PosXEndStdLfRe */
    0U,                                /* CSI_ParseConfRiRe */
    0U,                                /* CSI_PolyfitRmseRiRe */
    0U,                                /* CSI_ObstacleFlgRiRe */
    0U,                                /* CSI_MarkerTypeRiRe */
    0U,                                /* CSI_MeasureTypeRiRe */
    0.0F,                              /* CSI_ExistProbRiRe */
    0.0F,                              /* CSI_PosY0RiRe */
    0.0F,                              /* CSI_HeadingAngleRiRe */
    0.0F,                              /* CSI_CurvatureRiRe */
    0.0F,                              /* CSI_CrvRateRiRe */
    0U,                                /* CSI_ColorTypeRiRe */
    0.0F,                              /* CSI_MakerWidthRiRe */
    0.0F,                              /* CSI_PosXStartRiRe */
    0.0F,                              /* CSI_PosYStartStdRiRe */
    0.0F,                              /* CSI_PosXEndRiRe */
    0.0F                               /* CSI_PosXEndStdRiRe */
  },

  /* Computed Parameter: CSI_ObjectInfo_Y0
   * Referenced by: '<S1>/CSI_ObjectInfo'
   */
  {
    0.0F,                              /* CSI_Ri_AccOBJ_Width */
    0.0F,                              /* CSI_Ri_AccOBJ_Vy */
    0.0F,                              /* CSI_Ri_AccOBJ_Vx */
    0.0F,                              /* CSI_Ri_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_Ri_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_Ri_AccOBJ_ExistProb */
    0.0F,                              /* CSI_Ri_AccOBJ_Dy */
    0.0F,                              /* CSI_Ri_AccOBJ_Dx */
    0U,                                /* CSI_Ri_AccOBJ_Class */
    0U,                                /* CSI_Ri_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_Ri_AccOBJ_Ay */
    0.0F,                              /* CSI_Ri_AccOBJ_Ax */
    0.0F,                              /* CSI_RiFr_AccOBJ_Width */
    0.0F,                              /* CSI_RiFr_AccOBJ_Vy */
    0.0F,                              /* CSI_RiFr_AccOBJ_Vx */
    0.0F,                              /* CSI_RiFr_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_RiFr_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_RiFr_AccOBJ_ExistProb */
    0.0F,                              /* CSI_RiFr_AccOBJ_Dy */
    0.0F,                              /* CSI_RiFr_AccOBJ_Dx */
    0U,                                /* CSI_RiFr_AccOBJ_Class */
    0U,                                /* CSI_RiFr_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_RiFr_AccOBJ_Ay */
    0.0F,                              /* CSI_RiFr_AccOBJ_Ax */
    0.0F,                              /* CSI_Le_AccOBJ_Width */
    0.0F,                              /* CSI_Le_AccOBJ_Vy */
    0.0F,                              /* CSI_Le_AccOBJ_Vx */
    0.0F,                              /* CSI_Le_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_Le_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_Le_AccOBJ_ExistProb */
    0.0F,                              /* CSI_Le_AccOBJ_Dy */
    0.0F,                              /* CSI_Le_AccOBJ_Dx */
    0U,                                /* CSI_Le_AccOBJ_Class */
    0U,                                /* CSI_Le_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_Le_AccOBJ_Ay */
    0.0F,                              /* CSI_Le_AccOBJ_Ax */
    0.0F,                              /* CSI_LeFr_AccOBJ_Width */
    0.0F,                              /* CSI_LeFr_AccOBJ_Vy */
    0.0F,                              /* CSI_LeFr_AccOBJ_Vx */
    0.0F,                              /* CSI_LeFr_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_LeFr_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_LeFr_AccOBJ_ExistProb */
    0.0F,                              /* CSI_LeFr_AccOBJ_Dy */
    0.0F,                              /* CSI_LeFr_AccOBJ_Dx */
    0U,                                /* CSI_LeFr_AccOBJ_Class */
    0U,                                /* CSI_LeFr_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_LeFr_AccOBJ_Ay */
    0.0F,                              /* CSI_LeFr_AccOBJ_Ax */
    0.0F,                              /* CSI_Fr_AccOBJ_Width */
    0.0F,                              /* CSI_Fr_AccOBJ_Vy */
    0.0F,                              /* CSI_Fr_AccOBJ_Vx */
    0.0F,                              /* CSI_Fr_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_Fr_AccOBJ_Length */
    0.0F,                              /* CSI_Fr_AccOBJ_Height */
    0.0F,                              /* CSI_Fr_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_Fr_AccOBJ_ExistProb */
    0.0F,                              /* CSI_Fr_AccOBJ_Dy */
    0.0F,                              /* CSI_Fr_AccOBJ_Dx */
    0U,                                /* CSI_Fr_AccOBJ_Class */
    0U,                                /* CSI_Fr_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_Fr_AccOBJ_Ay */
    0.0F,                              /* CSI_Fr_AccOBJ_Ax */
    0.0F,                              /* CSI_FrFr_AccOBJ_Width */
    0.0F,                              /* CSI_FrFr_AccOBJ_Vy */
    0.0F,                              /* CSI_FrFr_AccOBJ_Vx */
    0.0F,                              /* CSI_FrFr_AccOBJ_ObstacleProb */
    0.0F,                              /* CSI_FrFr_AccOBJ_HeadingAngle */
    0.0F,                              /* CSI_FrFr_AccOBJ_ExistProb */
    0.0F,                              /* CSI_FrFr_AccOBJ_Dy */
    0.0F,                              /* CSI_FrFr_AccOBJ_Dx */
    0U,                                /* CSI_FrFr_AccOBJ_Class */
    0U,                                /* CSI_FrFr_AccOBJ_Brakelight_Info */
    0.0F,                              /* CSI_FrFr_AccOBJ_Ay */
    0.0F                               /* CSI_FrFr_AccOBJ_Ax */
  },

  /* Computed Parameter: Constant_Value
   * Referenced by: '<S4>/Constant'
   */
  2.0F,

  /* Computed Parameter: Constant1_Value
   * Referenced by: '<S4>/Constant1'
   */
  6.0F
};

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
