#include "common_math_library/interpolation.h"

#include <stdint.h>

#include "common_math_library/common_method.h"

float CML_Interpolation1D(float x, const CML_Table1D table[],
                          uint8_t table_rows) {
  float result = 0.0F;
  // if the x-position is smallest sampling point
  // return the smallest sampling point y-value
  if (x <= table[0].x) {
    result = table[0].y;
  } else if (x >= table[table_rows - 1].x) {
    result = table[table_rows - 1].y;
  } else {
    // index of the nearest sampling point to the right
    uint8_t right_index = 1;
    while (table[right_index].x < x) {
      right_index++;
    }
    float dx = table[right_index].x - table[right_index - 1].x;

    float weight = 0.0;
    if (dx > CML_EPSILON) {
      weight = (x - table[right_index - 1].x) / (dx);
    } else {
      weight = 0.0;
    }

    result = ((table[right_index - 1].y) * (1.0F - weight)) +
             ((table[right_index].y) * weight);
  }
  return result;
}
