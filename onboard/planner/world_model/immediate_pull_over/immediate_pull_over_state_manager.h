#ifndef ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_H_
#define ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_H_

#include <memory>
#include <vector>

#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/state/planner_internal_state_interface.h"
#include "planner/world_model/immediate_pull_over/immediate_pull_over_point_searcher.h"
#include "planner/world_model/immediate_pull_over/immediate_pull_over_state_manager_speed_data.h"
#include "planner/world_model/immediate_pull_over/stage/stage_manager.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner_protos/lane_sequence_proposal.pb.h"
#include "planner_protos/mrc_immediate_pullover.pb.h"
#include "planner_protos/state/world_model.pb.h"

namespace planner {

// This class manages immediate pull over internal states, include immediate
// pull over point, immediate pull over state, triggered timestamp and debug
// info.
class ImmediatePullOverStateManager
    : public PlanningSeedAccess,
      public PlannerInternalStateInterface<
          pb::ImmediatePullOverStateManagerState> {
 public:
  ImmediatePullOverStateManager(
      const pnc_map::PncMapService* pnc_map_service,
      const std::vector<ConstructionZone>& construction_zones,
      const pb::ImmediatePullOverConfig& immediate_pull_over_config)
      : stage_manager_(DCHECK_NOTNULL(pnc_map_service), construction_zones) {
    forbidden_stop_types_for_mrm_harbor_.reserve(/*size=*/12);
    forbidden_stop_types_for_mrm_pullright_.reserve(/*size=*/6);
    for (const auto& forbidden_stop_type :
         immediate_pull_over_config.mrm_harbor_config()
             .forbidden_stop_types()) {
      forbidden_stop_types_for_mrm_harbor_.push_back(
          StopType(forbidden_stop_type));
    }
    for (const auto& forbidden_stop_type :
         immediate_pull_over_config.mrm_pullright_config()
             .forbidden_stop_types()) {
      forbidden_stop_types_for_mrm_pullright_.push_back(
          StopType(forbidden_stop_type));
    }
  }

  void LoadState(const pb::ImmediatePullOverStateManagerState& from_pb) final;

  void DumpState(pb::ImmediatePullOverStateManagerState& to_pb) const final;

  // Returns immediate pull over info from route_status.
  const routing::pb::ImmediatePullOverInfo& immediate_pullover_info() const {
    return immediate_pullover_info_;
  }

  // Sets the immediate pull over point.
  void set_immediate_pull_over_point(
      const std::optional<math::geometry::Point2d>& immediate_pull_over_point) {
    immediate_pull_over_point_ = immediate_pull_over_point;
  }

  // Returns immediate pull over point.
  const std::optional<math::geometry::Point2d>& immediate_pull_over_point()
      const {
    return immediate_pull_over_point_;
  }

  // Returns immediate pull over triggered timestamp.
  int64_t immediate_pullover_triggered_timestamp() const {
    return immediate_pullover_triggered_timestamp_;
  }

  // The pdz info is selected between local mode and redirecting mode.
  const std::optional<PickupDropoffZoneInfo>& selected_pdz_info() const {
    return selected_pdz_info_;
  }

  // Returns the accumulated distance when immediate pull over is triggered.
  double accumulated_distance() const { return accumulated_distance_; }

  // Returns the initial_dist_to_destination for planning-source immediate pull
  // over.
  double initial_dist_to_destination() const {
    return initial_dist_to_destination_;
  }

  // Returns immediate pull over source.
  pb::ImmediatePullOverTriggerSource immediate_pullover_source() const {
    return immediate_pullover_source_;
  }

  // Returns immediate pull over state.
  routing::pb::ImmediatePullOverState immediate_pullover_state() const {
    return immediate_pullover_state_;
  }

  // Returns immediate pull over type.
  mrc::pb::MrmType mrm_type() const { return mrm_type_; }

  // Returns the trigger state change type.
  TriggerStateChangeType trigger_state_change_type() const {
    return trigger_state_change_type_;
  }

  const stage::StageManager& stage_manager() const { return stage_manager_; }

  // Returns true if immediate pull over is using redirecting mode.
  bool is_using_redirecting_mode() const { return is_using_redirecting_mode_; }

  // Returns true if immediate pull over is triggered by mrc.
  bool IsImmediatePullOverTriggeredByMRC() const {
    return immediate_pullover_state_ !=
               routing::pb::ImmediatePullOverState::kNotTriggered &&
           (mrm_type_ == mrc::pb::MRM_HARBOR ||
            mrm_type_ == mrc::pb::MRM_PULLRIGHT);
  }

  // Returns true if immediate pull over is triggered.
  bool IsImmediatePullOverTriggered() const {
    return immediate_pullover_state_ ==
               routing::pb::ImmediatePullOverState::kTriggered ||
           immediate_pullover_state_ ==
               routing::pb::ImmediatePullOverState::kSuccess;
  }

  // Returns true if planner should respond immediate pull over when it is
  // triggered.
  bool ShouldResponseToTriggeredImmediatePullOver() const {
    return !should_ignore_or_terminate_request_ &&
           IsImmediatePullOverTriggered();
  }

  // Returns true if should trigger immediate right lane change due to immediate
  // pullover is triggered.
  bool ShouldTriggerImmediateRightLaneChangeForImmediatePullover() const {
    return should_request_immediate_right_lane_change_;
  }

  // Returns true if should restrict left lane change due to immediate pullover
  // is triggered.
  bool ShouldRequestRestrictedLeftLaneChange() const {
    return should_request_restricted_left_lane_change_;
  }

  // Returns true if planning trigger immediate pull over.
  bool IsTriggeredByPlanning() const {
    return IsImmediatePullOverTriggered() &&
           immediate_pullover_source_ ==
               pb::ImmediatePullOverTriggerSource::kByPlanning;
  }

  // Returns true if dhmi or phmi trigger immediate pull over.
  bool IsTriggeredByOrderService() const {
    return IsImmediatePullOverTriggered() &&
           (immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByOrderService ||
            immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByDriverHMI ||
            immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByPassengerHMI);
  }

  // Gets forbidden stop type for mrm harbor or mrc pull right.
  std::vector<StopType> GetForbiddenStopTypesForMRC() const {
    if (mrm_type_ == mrc::pb::MRM_HARBOR) {
      return forbidden_stop_types_for_mrm_harbor_;
    }
    if (mrm_type_ == mrc::pb::MRM_PULLRIGHT) {
      return forbidden_stop_types_for_mrm_pullright_;
    }
    return {};
  }

  // Updates immediate pull over state manager, include candidate immediate
  // pull over points, immediate pull over state, triggered timestamp and
  // debug info.
  void UpdateImmediatePullOverStateManager(
      const GlobalRouteSolution& global_route_solution,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<ConstructionZone>& construction_zones,
      const pb::LaneSequenceCandidates& last_candidates,
      const pnc_map::Lane& current_lane,
      const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
      const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request,
      const voy::Pose& current_pose,
      const std::vector<pb::AssistResponse>& assist_responses,
      const OddInfo& odd_info, int64_t current_timestamp,
      pb::PullOverProgress pull_over_progress,
      pb::ImmediatePullOverDebug* immediate_pull_over_debug);

  // Updates immediate pull over debug.
  void UpdateImmediatePullOverDebug(
      pb::ImmediatePullOverDebug* immediate_pull_over_debug,
      int64_t current_timestamp);

  ImmediatePullOverStateManagerSpeedData
  CreateImmediatePullOverStateManagerSpeedData() {
    return ImmediatePullOverStateManagerSpeedData(
        immediate_pullover_info_, immediate_pull_over_point_,
        immediate_pullover_triggered_timestamp_, accumulated_distance_,
        initial_dist_to_destination_, should_ignore_or_terminate_request_,
        immediate_pullover_source_, immediate_pullover_state_,
        stage_manager_.redirecting_regional_path(),
        should_request_immediate_right_lane_change_,
        stage_manager_.cell_manager().cloud_cell_request(), mrm_type_,
        trigger_state_change_type_, forbidden_stop_types_for_mrm_harbor_,
        forbidden_stop_types_for_mrm_pullright_);
  }

  const std::optional<LightRegionalPath>& redirecting_regional_path() const {
    return stage_manager_.redirecting_regional_path();
  }

  // Returns cloud cell request.
  const std::optional<pb::MatchStopCellRequest>& cloud_cell_request() const {
    return stage_manager().cell_manager().cloud_cell_request();
  }

  // Returns true if planner should ignore or terminate request from route
  // link.
  bool should_ignore_or_terminate_request() const {
    return should_ignore_or_terminate_request_;
  }

 private:
  // Resets all the data member.
  void ResetImmediatePullOverStateManager(
      pb::ImmediatePullOverDebug* immediate_pull_over_debug);

  // Selects the pdz between local mode and redirecting mode.
  void SelectPDZBetweenLocalModeAndRedirectingMode(
      const RobotState& robot_state);

  // Updates the immediate pull over pdz, including by local mode and
  // redirecting mode.
  void UpdateImmediatePullOverPDZ(
      const GlobalRouteSolution& global_route_solution,
      const pnc_map::PncMapService* pnc_map_service,
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<ConstructionZone>& construction_zones,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
      const std::vector<pb::AssistResponse>& assist_responses,
      const OddInfo& odd_info, int64_t current_timestamp,
      pb::PullOverProgress pull_over_progress,
      pb::ImmediatePullOverDebug* immediate_pull_over_debug);

  // Updates immediate pull over state manager in local mode, which samples the
  // immediate pull over points by lane sequence.
  void UpdateImmediatePullOverStateManagerInLocalMode(
      const GlobalRouteSolution& global_route_solution,
      const pnc_map::PncMapService* pnc_map_service,
      const std::vector<PickupDropoffZoneInfo>& pickup_dropoff_zone_infos,
      const RobotState& robot_state,
      const std::vector<ConstructionZone>& construction_zones,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
      int64_t current_timestamp, pb::PullOverProgress pull_over_progress);

  // Generates pdz and run immediate pull over by redirecting mode, which allow
  // route redirecting and collect cell before actual pull over.
  void GeneratePDZAndRunImmediatePullOverByRedirectingMode(
      const pnc_map::PncMapService* pnc_map_service,
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
      const OddInfo& odd_info,
      const std::vector<pb::AssistResponse>& assist_responses,
      bool is_current_pdz_valid,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Updates immediate pull over state by routing and mrc state machine.
  void UpdateImmediatePullOverState(
      const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
      const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request);

  // Updates immediate pull over source and mrm type.
  void UpdateImmediatePullOverSourceAndMRMType(
      const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
      const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request);

  // Updates should ignore or terminate request from route link.
  void UpdateShouldRefuseAndTerminateRequest(
      const RegionalMap& regional_map, const RobotState& robot_state,
      const routing::pb::RoutePoint& destination,
      pb::PullOverProgress pull_over_progress);

  // Generates pdz by local mode, which samples candidate immediate pull over
  // points on the lane sequence and then generate the pdz by query cell.
  void GenerateImmediatePullOverPDZByLocalMode(
      const GlobalRouteSolution& global_route_solution,
      const pnc_map::PncMapService* pnc_map_service,
      const RobotState& robot_state,
      const std::vector<ConstructionZone>& construction_zones,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
      const OddInfo& odd_info);

  // Updates lane change request.
  void UpdateLaneChangeRequest(
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane);

  // Updates immediate pull over triggered timestamp.
  void UpdateImmediatePullOverTriggeredTimestamp(const voy::Pose& current_pose,
                                                 int64_t current_timestamp);

  // Updates the accumulate distance when immediate pull over is triggered.
  void UpdateAccumulatedDistance(const RobotState& robot_state,
                                 const routing::pb::RoutePoint& destination,
                                 const voy::Pose& current_pose,
                                 int64_t current_timestamp);

  // Records rt-event when immediate pull over success.
  void RecordImmediatePullOverSuccessRTEvent(
      const std::optional<PickupDropoffZoneInfo>& pickup_dropoff_zone_info,
      const voy::Pose& current_pose, int64_t current_timestamp);

  // Handles immediate pull over signal.
  void HandleImmediatePullOverSignal(
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Handles cell collecting signal, triggers cloud or local cell finding.
  void HandleCellCollectingSignal(
      const pnc_map::PncMapService* pnc_map_service,
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const voy::Pose& current_pose, const OddInfo& odd_info,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Handles cloud cell message's subscription.
  void HandleCloudCellSubscription(
      const pnc_map::PncMapService* pnc_map_service,
      const RegionalMap& regional_map, const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const voy::Pose& current_pose,
      const std::vector<pb::AssistResponse>& assist_responses,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Handles route directing signal.
  void HandleRouteDirectingSignal(
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const RegionalMap& regional_map,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Handles immediate right lane change signal.
  void HandleImmediateRightLCSignal(
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const RobotState& robot_state,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Handles pull over state.
  void HandlePullOverSignal(
      const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
      const pnc_map::Lane& current_lane, const RobotState& robot_state,
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Resets redirecting mode stage manager to idle stage when
  // ShouldTryUsingRedirectingMode is false.
  void ResetRedirectingModeStageManagerToIdleStage(
      pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug);

  // Returns true if redirecting mode should be used.
  bool ShouldTryUsingRedirectingMode() const;

  // Updates route direction info to seed.
  void UpdateRouteDirectionInfoToSeed();

  // Updates dynamic pull over request to seed.
  void UpdateDynamicPullOverRequestInfoSeed();

  // Returns true should set IMPO odd type when querying cell.
  bool ShouldUseImpoOddType() const;

  // The immediate pull over info from route status.
  routing::pb::ImmediatePullOverInfo immediate_pullover_info_;

  // The immediate pull over point.
  std::optional<math::geometry::Point2d> immediate_pull_over_point_ =
      std::nullopt;

  // The timestamp when immediate pull over is triggered, which is used to
  // determine whether the timeout.
  int64_t immediate_pullover_triggered_timestamp_ = -1;

  // The last timestamp which is used to calculate the accumulated distance.
  int64_t last_timestamp_ = -1;

  // The accumulated distance when immediate pull over is triggered.
  double accumulated_distance_ = 0.0;

  // The distance between ego and destination when planning triggered
  // immediate pull over in the first cycle.
  double initial_dist_to_destination_ = 0.0;

  // The flag indicates whether is using redirecting_mode.
  bool is_using_redirecting_mode_ = false;

  // Indicate whether the planner needs to reject and terminate the immediate
  // pull over request from the route link.
  bool should_ignore_or_terminate_request_ = false;

  // The immediate pull over source.
  pb::ImmediatePullOverTriggerSource immediate_pullover_source_ =
      pb::ImmediatePullOverTriggerSource::kUnknownSource;

  // The immediate pull over state.
  routing::pb::ImmediatePullOverState immediate_pullover_state_ =
      routing::pb::ImmediatePullOverState::kNotTriggered;

  // The mrm type.
  mrc::pb::MrmType mrm_type_ = mrc::pb::UNDEFINED;

  // The change info of trigger state.
  TriggerStateChangeType trigger_state_change_type_ =
      TriggerStateChangeType::kNoChange;

  // The pdz info generated by local mode.
  std::optional<PickupDropoffZoneInfo> pdz_info_from_local_mode_ = std::nullopt;

  // The pdz info selected between local mode and redirecting mode.
  std::optional<PickupDropoffZoneInfo> selected_pdz_info_ = std::nullopt;

  // Forbidden stop types for mrm harbor.
  std::vector<StopType> forbidden_stop_types_for_mrm_harbor_;

  // Forbidden stop types for mrm pull right.
  std::vector<StopType> forbidden_stop_types_for_mrm_pullright_;

  // Manage immediate pullover's stage.
  stage::StageManager stage_manager_;

  // Indicates should request immediate right lane change when immediate pull
  // over is triggered.
  bool should_request_immediate_right_lane_change_ = false;

  // Indicates should request restrict left lane change when immediate pull over
  // is triggered.
  bool should_request_restricted_left_lane_change_ = false;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_STATE_MANAGER_H_
