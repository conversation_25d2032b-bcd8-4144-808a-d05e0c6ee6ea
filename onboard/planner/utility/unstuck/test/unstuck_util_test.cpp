#include "planner/utility/unstuck/unstuck_util.h"

#include <vector>

#include <gtest/gtest.h>

#include "geometry/model/point_2d.h"
#include "geometry/model/polygon.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "pnc_map_service/test/map_test_util.h"

namespace planner {
namespace {
constexpr int64_t kCurrentLaneId = 16919;

pnc_map::MapTestUtil ConstructPncMapTestUtil() {
  const double current_map_x = -4489.0834444744978;
  const double current_map_y = -2579.6149766549934;
  pnc_map::MapTestUtil map_test_util;
  map_test_util.InitPncMapService(current_map_x, current_map_y, 0.0,
                                  /*route_name=*/"fremont_route_3",
                                  hdmap::test_util::TestData::kFremontData);
  return map_test_util;
}

// Shifts the polygon along the given direction vector whose origin is (0, 0);
math::geometry::PolygonWithCache2d ShiftPolygon(
    const math::geometry::PolygonWithCache2d& polygon,
    const math::geometry::Point2d& direction) {
  std::vector<math::geometry::Point2d> points;
  points.reserve(polygon.points().size());
  for (const auto& pt : polygon.points()) {
    points.emplace_back(pt.x() + direction.x(), pt.y() + direction.y());
  }
  return math::geometry::PolygonWithCache2d(std::move(points));
}
}  // namespace

TEST(UnstuckUtilTest, GetUnstuckRequiredBoundaryViolationTest) {
  const pnc_map::MapTestUtil map_test_util = ConstructPncMapTestUtil();
  const std::vector<const pnc_map::Lane*> lane_sequence =
      test_utility::ConstructLaneSequence({kCurrentLaneId}, map_test_util);
  // An object contour whose points is stored in clock-counter wise direction,
  // and its first point is on the left lane marking and the third point is on
  // the right lane marking.
  const math::geometry::PolygonWithCache2d contour_1(
      {{-4483.4757034389768, -2587.2475492600352},
       {-4487.4928962852891, -2582.9402634958392},
       {-4489.2474632649682, -2583.7205780600198},
       {-4485.3094666529532, -2587.7351298608287}});
  const math::geometry::PolylineCurve2d left_boundary =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kLeftBoundary);
  const math::geometry::PolylineCurve2d right_boundary =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kRightBoundary);
  const double contour_1_left_violation = GetUnstuckRequiredBoundaryViolation(
      left_boundary, contour_1, /*ego_width=*/2.0, /*is_left=*/true,
      /*required_lateral_gap=*/2.0);
  const double contour_1_right_violation = GetUnstuckRequiredBoundaryViolation(
      right_boundary, contour_1, /*ego_width=*/2.0, /*is_left=*/false,
      /*required_lateral_gap=*/2.0);

  EXPECT_NEAR(contour_1_left_violation, 4.0, 1e-1);
  EXPECT_NEAR(contour_1_right_violation, 4.0, 1e-1);

  // Get the shift normalized direction vector by rotate the left lane marking
  // derivative 90 degree clock wise.
  const math::geometry::PolylineCurve2d left_lane_marking =
      DCHECK_NOTNULL(lane_sequence.front()->left_marking())->line();
  const math::geometry::Point2d derivative = left_lane_marking.GetInterpDeriv(
      left_lane_marking.GetTotalArcLength() * 0.5);
  const math::geometry::Point2d shift_direction = {derivative.y(),
                                                   -derivative.x()};

  // Left shift polygon with 1.5m.
  constexpr double kLeftShiftDistInMeter = -1.5;
  const math::geometry::Point2d left_shift_direction = {
      kLeftShiftDistInMeter * shift_direction.x(),
      kLeftShiftDistInMeter * shift_direction.y()};
  const math::geometry::PolygonWithCache2d contour_2 =
      ShiftPolygon(contour_1, left_shift_direction);
  const double contour_2_left_violation = GetUnstuckRequiredBoundaryViolation(
      left_boundary, contour_2, /*ego_width=*/2.0, /*is_left=*/true,
      /*required_lateral_gap=*/2.0);
  const double contour_2_right_violation = GetUnstuckRequiredBoundaryViolation(
      right_boundary, contour_2, /*ego_width=*/2.0, /*is_left=*/false,
      /*required_lateral_gap=*/2.0);

  EXPECT_NEAR(contour_2_left_violation, 5.5, 1e-1);
  EXPECT_NEAR(contour_2_right_violation, 2.5, 1e-1);

  // Right shift polygon with 4.1m.
  constexpr double kRightShiftDistInMeter = 4.1;
  const math::geometry::Point2d right_shift_direction = {
      kRightShiftDistInMeter * shift_direction.x(),
      kRightShiftDistInMeter * shift_direction.y()};
  const math::geometry::PolygonWithCache2d contour_3 =
      ShiftPolygon(contour_1, right_shift_direction);
  const double contour_3_left_violation = GetUnstuckRequiredBoundaryViolation(
      left_boundary, contour_3, /*ego_width=*/2.0, /*is_left=*/true,
      /*required_lateral_gap=*/2.0);
  const double contour_3_right_violation = GetUnstuckRequiredBoundaryViolation(
      right_boundary, contour_3, /*ego_width=*/2.0, /*is_left=*/false,
      /*required_lateral_gap=*/2.0);

  EXPECT_NEAR(contour_3_left_violation, 0.0, 1e-1);
  EXPECT_NEAR(contour_3_right_violation, 8.1, 1e-1);
}

TEST(UnstuckUtilTest, GetYieldDistanceForStuckingObjectTest) {
  // Test for the static blockage.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::VEHICLE);
  tracked_object.set_id(18845);
  tracked_object.set_center_x(0.0);
  tracked_object.set_center_y(0.0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_length(2.0);
  tracked_object.set_width(1.0);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(1.0);
  tracked_object.set_velocity(0.0);
  tracked_object.add_attributes(voy::perception::PARKED_CAR);

  const int64_t timestamp = 0;

  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);

  const RobotState dummy_robot_state = test_utility::ConstructRobotState(
      /*start_x=*/0.0, /*start_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);

  PlannerObject blockage_object =
      PlannerObject(agent_proto, timestamp, pose_at_plan_init_ts);
  blockage_object.set_is_primary_stationary(true);
  const double yield_distance_for_blockage =
      GetYieldDistanceForStuckingObject(blockage_object, dummy_robot_state,
                                        /*required_lateral_gap=*/0.8);
  EXPECT_GT(yield_distance_for_blockage, 4.0);
}

TEST(UnstuckUtilTest, GetUnstuckTargetSteeringAngle) {
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::VEHICLE);
  tracked_object.set_id(18845);
  tracked_object.set_center_x(8.5);
  tracked_object.set_center_y(3.0);
  tracked_object.set_center_z(0.0);
  tracked_object.set_length(2.0);
  tracked_object.set_width(1.0);
  tracked_object.set_height(1.0);
  tracked_object.set_heading(0.0);
  tracked_object.set_velocity(0.0);

  const int64_t timestamp = 0;

  // Dummy pose at plan init ts.
  TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  const PlannerObject left_side_blocking_object =
      PlannerObject(agent_proto, timestamp, pose_at_plan_init_ts);

  const RobotState dummy_robot_state = test_utility::ConstructRobotState(
      /*start_x=*/0.0, /*start_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);
  const std::optional<double> dry_steering_left_target_angel =
      GetUnstuckTargetSteeringAngle(dummy_robot_state,
                                    left_side_blocking_object.contour(),
                                    /*nudge_from_left=*/true);
  EXPECT_TRUE(dry_steering_left_target_angel.has_value());
  EXPECT_GT(dry_steering_left_target_angel.value(), 0.5);

  tracked_object.set_center_y(-3.0);
  pose_at_plan_init_ts = TrafficParticipantPose(timestamp, tracked_object);
  const PlannerObject right_side_blocking_object =
      PlannerObject(agent_proto, timestamp, pose_at_plan_init_ts);
  const std::optional<double> dry_steering_right_target_angel =
      GetUnstuckTargetSteeringAngle(dummy_robot_state,
                                    right_side_blocking_object.contour(),
                                    /*nudge_from_left=*/false);
  EXPECT_TRUE(dry_steering_left_target_angel.has_value());
  EXPECT_LT(dry_steering_right_target_angel.value(), -0.5);

  tracked_object.set_center_x(4.0);
  pose_at_plan_init_ts = TrafficParticipantPose(timestamp, tracked_object);
  const PlannerObject complete_blocking_object =
      PlannerObject(agent_proto, timestamp, pose_at_plan_init_ts);
  const std::optional<double> valid_left_dry_angel =
      GetUnstuckTargetSteeringAngle(dummy_robot_state,
                                    complete_blocking_object.contour(),
                                    /*nudge_from_left=*/true);
  const std::optional<double> invalid_right_dry_angel =
      GetUnstuckTargetSteeringAngle(dummy_robot_state,
                                    complete_blocking_object.contour(),
                                    /*nudge_from_left=*/false);
  EXPECT_TRUE(valid_left_dry_angel.has_value());
  EXPECT_FALSE(invalid_right_dry_angel.has_value());
}

TEST(UnstuckUtilTest, GetUnstuckTargetSteeringAngleFromPoint) {
  // Create a dummy robot state for testing.
  const RobotState dummy_robot_state = test_utility::ConstructRobotState(
      /*start_x=*/0.0, /*start_y=*/0.0, /*vel_x=*/0.0, /*vel_y=*/0.0);

  // Check if the robot can drive towards the forward target point.
  const math::geometry::Point2d straight_forward_target_point{5.0, 0.0};
  const std::optional<double> dry_steering_straight_forward_angel =
      GetUnstuckTargetSteeringAngleFromPoint(dummy_robot_state,
                                             straight_forward_target_point,
                                             /*nudge_from_left=*/true);
  EXPECT_TRUE(dry_steering_straight_forward_angel.has_value());
  EXPECT_NEAR(dry_steering_straight_forward_angel.value(), 0.0, 1e-3);

  // Check if the robot can drive towards the left target point.
  const math::geometry::Point2d left_target_point{5.0, 1.0};
  const std::optional<double> dry_steering_left_angel =
      GetUnstuckTargetSteeringAngleFromPoint(dummy_robot_state,
                                             left_target_point,
                                             /*nudge_from_left=*/true);
  EXPECT_TRUE(dry_steering_left_angel.has_value());
  EXPECT_GT(dry_steering_left_angel.value(), 0.2);

  // Check if the robot can drive towards the right target point.
  const math::geometry::Point2d right_target_point{7.0, -2.0};
  const std::optional<double> dry_steering_right_angel =
      GetUnstuckTargetSteeringAngleFromPoint(dummy_robot_state,
                                             right_target_point,
                                             /*nudge_from_left=*/false);
  EXPECT_TRUE(dry_steering_right_angel.has_value());
  EXPECT_LT(dry_steering_right_angel.value(), -0.2);

  // Check if the target point is invalid and the function returns no value.
  const math::geometry::Point2d out_of_scope_target_point{3.0, -5.0};
  const std::optional<double> dry_steering_out_of_scope_angel =
      GetUnstuckTargetSteeringAngleFromPoint(dummy_robot_state,
                                             out_of_scope_target_point,
                                             /*nudge_from_left=*/false);
  EXPECT_FALSE(dry_steering_out_of_scope_angel.has_value());
}
}  // namespace planner
