#include "long_function/stop_in_lane/avoidance_deceleration.h"

#include <math.h>
#include <stdint.h>

#include "common_math_library/common_method.h"

// To avoid a collision, acceleration need to be calculated
float CalculateAvoidanceDeceleration(const float ego_dx, const float ego_vx,
                                     const float ego_ax, const float obj_dx,
                                     const float obj_vx, const float obj_ax,
                                     uint8_t *state) {
  const float relative_vx = obj_vx - ego_vx;
  const float relative_dx = fmaxf(obj_dx - ego_dx, CD_MIN_LONG_DISTANCE);
  float deceleration = 0.0;
  float obj_brake_distance = 0.0;
  float ego_brake_distance = 0.0;

  (void)ego_ax;  // Suppress unused variable warning

  // collision has happened
  if (relative_dx <= CD_MIN_LONG_DISTANCE) {
    deceleration = CD_MAX_NEC_LONG_DECEL * 0.5;
    *state = 1;
    // ego is standing, no need deceleration
  } else if (fabsf(ego_vx) < CD_FLOAT_ALMOST_ZERO) {
    deceleration = 0.0;
    *state = 2;
    // object is accelerating away from the ego, no collision risk
  } else if ((obj_ax >= 0.0) && (relative_vx > 0.0)) {
    deceleration = 0.0;
    *state = 3;
    // object is oncoming
  } else if (obj_vx < 0.0) {
    // unable to avoid collision
    if (obj_ax <= 0.0) {
      deceleration = CD_MAX_NEC_LONG_DECEL;
      *state = 4;
      // oncoming object is braking
    } else if (obj_ax > 0.0) {
      obj_brake_distance = 0.5 * obj_vx * obj_vx / obj_ax;
      ego_brake_distance = relative_dx - obj_brake_distance;
      // unable to avoid collision
      if (ego_brake_distance <= 0.0) {
        deceleration = CD_MAX_NEC_LONG_DECEL;
        *state = 5;
      } else {
        deceleration = -0.5 * ego_vx * ego_vx / ego_brake_distance;
        *state = 6;
      }
    }
    // object and ego are driving in the same direction
  } else if (obj_vx >= 0.0) {
    // the time when the object and ego speed are equal
    float vx_equal_time = 0.0;
    // situation 7
    if (relative_vx < 0.0 && obj_ax > 0.0) {
      vx_equal_time = -2 * relative_dx / relative_vx;
      deceleration = (relative_vx + obj_ax * vx_equal_time) / vx_equal_time;
      *state = 7;
      // situation 8
    } else if (relative_vx > 0.0 && obj_ax < 0.0) {
      obj_brake_distance = -0.5 * obj_vx * obj_vx / obj_ax;
      ego_brake_distance = relative_dx + obj_brake_distance;
      deceleration = -0.5 * ego_vx * ego_vx / ego_brake_distance;
      *state = 8;
    } else if (relative_vx < 0.0 && obj_ax < 0.0) {
      const float obj_stop_time = -obj_vx / obj_ax;
      vx_equal_time = -2 * relative_dx / relative_vx;
      // same calculation method as situation 7
      if (vx_equal_time < obj_stop_time) {
        deceleration = (relative_vx + obj_ax * vx_equal_time) / vx_equal_time;
        *state = 9;
        // same calculation method as situation 8
      } else {
        obj_brake_distance = -0.5 * obj_vx * obj_vx / obj_ax;
        ego_brake_distance = relative_dx + obj_brake_distance;
        deceleration = -0.5 * ego_vx * ego_vx / ego_brake_distance;
        *state = 10;
      }
    }
  } else {
    // unknown
    *state = 11;
  }
  deceleration = CML_Limit(deceleration, CD_MAX_NEC_LONG_DECEL, 0.0F);

  return deceleration;
}  // CalculateAvoidanceDeceleration end

float CalculateAvoidanceDecelerationWithJerk(
    const float ego_dx, const float ego_vx, const float ego_ax,
    const float obj_dx, const float obj_vx, const float obj_ax, uint8_t *state,
    uint8_t *iteration_number) {
  float deceleration = 0.0F;
  float deceleration_last = 0.0F;
  float deceleration_diff = 0.0F;
  float reach_acc_time = 0.0F;
  float ego_distance = 0.0F;
  float obj_distance = 0.0F;
  float jerk = -3.0F;

  *iteration_number = 0U;
  while (*iteration_number <= 5) {
    *iteration_number = *iteration_number + 1;
    deceleration = CalculateAvoidanceDeceleration(ego_dx + ego_distance, ego_vx,
                                                  ego_ax, obj_dx + obj_distance,
                                                  obj_vx, obj_ax, state);
    deceleration_diff = fabsf(deceleration - deceleration_last);
    deceleration_last = deceleration;

    if ((deceleration == CD_MAX_NEC_LONG_DECEL) || (deceleration >= 0.0) ||
        (deceleration >= ego_ax) || (deceleration_diff <= 0.5)) {
      // No need to consider the distance for reaching the deceleration
      break;
    } else {
      reach_acc_time = (deceleration - ego_ax) / jerk;
      ego_distance =
          ego_vx * reach_acc_time +
          1.0F / 2.0F * ego_ax * reach_acc_time * reach_acc_time +
          1.0F / 6.0F * jerk * reach_acc_time * reach_acc_time * reach_acc_time;
      obj_distance = obj_vx * reach_acc_time +
                     1.0F / 2.0F * obj_ax * reach_acc_time * reach_acc_time;
      if ((ego_distance + obj_distance) < 1.0F) {
        // No need to consider the distance for reaching the deceleration
        break;
      }
    }
  }
  return deceleration;
}
