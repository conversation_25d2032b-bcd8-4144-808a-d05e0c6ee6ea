#include "planner/speed/agent_reaction/agent_reaction_seed_manager.h"

#include <cmath>

#include <gtest/gtest.h>

#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/planning_gflags.h"
#include "planner/speed/agent_reaction/agent_reaction_tracker/agent_intention_tracker.h"
#include "planner/speed/agent_reaction/agent_reaction_tracker/agent_reaction_tracker.h"

namespace planner {
namespace speed {
namespace {
// Generates a dummy robot state at given timestamp.
RobotState GenerateEgoState(int64_t curr_timestamp) {
  ::planner::pb::ReplanLocatorConfig config;
  RobotState dummy_robot_state(config, av_comm::CarType::kLincoln,
                               av_comm::CarRegion::kUS);

  // In order to set current timestamp, we need to set current snapshot ptr,
  // which is done in function UpdatePoseCanbus.
  voy::Pose ego_init_pose;
  // Set current timestamp
  ego_init_pose.set_timestamp(curr_timestamp);
  ego_init_pose.set_x(0.0);
  ego_init_pose.set_y(0.0);
  voy::Canbus canbus;
  const std::shared_ptr<const voy::Canbus> canbus_ptr =
      std::make_shared<const voy::Canbus>(canbus);
  ::planner::pb::ReplanLocatorDebug replan_locator_debug;
  dummy_robot_state.UpdatePoseCanbus(ego_init_pose, canbus_ptr,
                                     /*replan_request=*/nullptr,
                                     &replan_locator_debug);
  return dummy_robot_state;
}
}  // namespace

// Tests inserting and retrieving agent see ego time record in seed.
TEST(AgentReactionTimeTrackerTest, RecordInsertionRemoval) {
  auto agent_reaction_seeds = std::make_shared<speed::pb::AgentReactionSeeds>();
  prediction::pb::AgentList agent_list1;

  ObjectId object_id_1 = 1;
  prediction::pb::Agent* agent1 = agent_list1.add_agent_list();
  voy::TrackedObject* tracked_object1 = agent1->mutable_tracked_object();
  tracked_object1->set_id(object_id_1);
  tracked_object1->set_center_x(0);
  tracked_object1->set_center_y(1);
  tracked_object1->set_heading(-M_PI_2);

  ObjectId object_id_2 = 2;
  prediction::pb::Agent* agent2 = agent_list1.add_agent_list();
  voy::TrackedObject* tracked_object2 = agent2->mutable_tracked_object();
  tracked_object2->set_id(object_id_2);
  tracked_object2->set_center_x(0);
  tracked_object2->set_center_y(-1);
  tracked_object2->set_heading(M_PI_2);

  int64_t timestamp1 = 100;
  const RobotState& dummy_robot_state1 = GenerateEgoState(timestamp1);
  EXPECT_EQ(GetNumTrackedAgents(*agent_reaction_seeds), 0);
  UpdateAgentReactionSeeds(dummy_robot_state1, agent_list1, {},
                           agent_reaction_seeds);
  // Agent 1 and agent 2 can both see ego at timestamp1, hence are both
  // inserted into seed.
  EXPECT_EQ(GetNumTrackedAgents(*agent_reaction_seeds), 2);
  EXPECT_EQ(GetAgentFirstSawEgoTimestamp(object_id_1, *agent_reaction_seeds),
            timestamp1);
  EXPECT_EQ(GetAgentLastSawEgoTimestamp(object_id_1, *agent_reaction_seeds),
            timestamp1);
  EXPECT_EQ(GetAgentFirstSawEgoTimestamp(object_id_2, *agent_reaction_seeds),
            timestamp1);
  EXPECT_EQ(GetAgentLastSawEgoTimestamp(object_id_2, *agent_reaction_seeds),
            timestamp1);

  int64_t timestamp2 = 200;
  const RobotState& dummy_robot_state2 = GenerateEgoState(timestamp2);
  // Agent 1 and agent 2 can still be seen at timestamp2. Their last saw ego
  // time should be updated.
  auto agent_reaction_seeds2 =
      std::make_shared<speed::pb::AgentReactionSeeds>();
  if (fLB::FLAGS_planning_copy_previous_seed) {
    agent_reaction_seeds2->CopyFrom(*agent_reaction_seeds);
  }
  UpdateAgentReactionSeeds(dummy_robot_state2, agent_list1,
                           agent_reaction_seeds, agent_reaction_seeds2);
  EXPECT_EQ(GetNumTrackedAgents(*agent_reaction_seeds2), 2);
  EXPECT_EQ(GetAgentFirstSawEgoTimestamp(object_id_1, *agent_reaction_seeds2),
            timestamp1);
  EXPECT_EQ(GetAgentLastSawEgoTimestamp(object_id_1, *agent_reaction_seeds2),
            timestamp2);
  EXPECT_EQ(GetAgentFirstSawEgoTimestamp(object_id_2, *agent_reaction_seeds2),
            timestamp1);
  EXPECT_EQ(GetAgentLastSawEgoTimestamp(object_id_2, *agent_reaction_seeds2),
            timestamp2);

  int64_t timestamp3 = 30000;
  const RobotState& dummy_robot_state3 = GenerateEgoState(timestamp3);
  prediction::pb::AgentList agent_list2;
  ObjectId object_id_3 = 3;
  prediction::pb::Agent* agent3 = agent_list2.add_agent_list();
  voy::TrackedObject* tracked_object3 = agent3->mutable_tracked_object();
  tracked_object3->set_id(object_id_3);
  tracked_object3->set_center_x(0);
  tracked_object3->set_center_y(1);
  tracked_object3->set_heading(-M_PI_2);
  // Agent 1 and agent 2 have not been seen for over 20s, and should have been
  // removed from seed. Agent 3 can see ego at current timestamp, hence is
  // inserted into seed.
  auto agent_reaction_seeds3 =
      std::make_shared<speed::pb::AgentReactionSeeds>();
  if (fLB::FLAGS_planning_copy_previous_seed) {
    agent_reaction_seeds3->CopyFrom(*agent_reaction_seeds2);
  }
  UpdateAgentReactionSeeds(dummy_robot_state3, agent_list2,
                           agent_reaction_seeds2, agent_reaction_seeds3);
  EXPECT_EQ(GetNumTrackedAgents(*agent_reaction_seeds3), 1);
  EXPECT_EQ(GetAgentFirstSawEgoTimestamp(object_id_3, *agent_reaction_seeds3),
            timestamp3);
  EXPECT_EQ(GetAgentLastSawEgoTimestamp(object_id_3, *agent_reaction_seeds3),
            timestamp3);
}

TEST(AgentReactionTimeTrackerTest, AgentHasSeenEgoTime) {
  speed::pb::AgentReactionSeeds agent_reaction_seeds;
  // Construct two agents, agent 4 can see ego, agent 5 cannot see ego.

  ObjectId object_id_4 = 4;
  prediction::pb::AgentList agent_list1;
  prediction::pb::Agent* agent4 = agent_list1.add_agent_list();
  voy::TrackedObject* tracked_object4 = agent4->mutable_tracked_object();
  tracked_object4->set_id(object_id_4);
  tracked_object4->set_center_x(1);
  tracked_object4->set_center_y(2);
  tracked_object4->set_heading(-M_PI_2);

  ObjectId object_id_5 = 5;
  prediction::pb::Agent* agent5 = agent_list1.add_agent_list();
  voy::TrackedObject* tracked_object5 = agent5->mutable_tracked_object();
  tracked_object5->set_id(object_id_5);
  tracked_object5->set_center_x(1);
  tracked_object5->set_center_y(0);
  tracked_object5->set_heading(-M_PI_2);

  int64_t agent_bp_initial_time = 100;
  int64_t first_see_ego_timestamp = 0;
  const math::Pose2d ego_pose(/*x_in=*/1.0, /*y_in=*/1.0, /*yaw_in=*/0.0);
  // Agent 4 will be added to seed, agent 5 will not be added to seed.
  AddAgentToAgentReactionSeeds({{1.0, 1.0}, {1.0, 1.0}, {1.0, 1.0}, {1.0, 1.0}},
                               first_see_ego_timestamp, agent_list1,
                               &agent_reaction_seeds);
  EXPECT_EQ(AgentHasSeenEgoTime(object_id_4, agent_bp_initial_time,
                                agent_reaction_seeds),
            -0.1);
  EXPECT_EQ(AgentHasSeenEgoTime(object_id_5, agent_bp_initial_time,
                                agent_reaction_seeds),
            std::numeric_limits<double>::infinity());
}

TEST(AgentReactionTrackerTest, AgentAndEgoVelocityTracking) {
  prediction::pb::AgentList agent_list;
  speed::pb::AgentReactionTrackerSeed mutable_seed;
  auto& agent_reaction_tracker_info_map =
      (*mutable_seed.mutable_agent_reaction_tracker_info_map());
  // Construct 3 agents, the first one always has interaction with ego(has
  // constraint), the second one never has interaction with ego, the third one
  // has interaction for a while then no interaction.
  ObjectId object_id_1 = 1;
  prediction::pb::Agent* agent1 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object1 = agent1->mutable_tracked_object();
  tracked_object1->set_id(object_id_1);
  tracked_object1->set_object_type(voy::perception::ObjectType::VEHICLE);

  ObjectId object_id_2 = 2;
  prediction::pb::Agent* agent2 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object2 = agent2->mutable_tracked_object();
  tracked_object2->set_id(object_id_2);

  ObjectId object_id_3 = 3;
  [[maybe_unused]] prediction::pb::Agent* agent3 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object3 = agent2->mutable_tracked_object();
  tracked_object3->set_id(object_id_3);

  for (int64_t i = 0; i < 60; ++i) {
    int64_t ts = i * 100;
    double agent1_velocity = i * 0.2;
    tracked_object1->set_velocity(agent1_velocity);
    double agent2_velocity = i * 0.4;
    tracked_object2->set_velocity(agent2_velocity);
    double agent3_velocity = i * 0.6;
    tracked_object3->set_velocity(agent3_velocity);
    RobotState robot_state = GenerateEgoState(ts);
    agent_reaction_tracker_info_map[object_id_1]
        .set_last_timestamp_constraint_added(ts);
    agent_reaction_tracker_info_map[object_id_3]
        .set_last_timestamp_constraint_added(ts);
    UpdateEgoAgentVelocityInAgentReactionTrackerSeed(robot_state, agent_list,
                                                     &mutable_seed);
  }

  EXPECT_TRUE(agent_reaction_tracker_info_map.find(object_id_1) !=
              agent_reaction_tracker_info_map.end());
  EXPECT_TRUE(agent_reaction_tracker_info_map.find(object_id_3) !=
              agent_reaction_tracker_info_map.end());
  const auto& agent_state_tracking =
      agent_reaction_tracker_info_map.at(object_id_1).agent_state_tracking();
  EXPECT_EQ(agent_state_tracking.size(), 51);
  EXPECT_EQ(agent_state_tracking[29].timestamp(), 3800);
  EXPECT_NEAR(agent_state_tracking[29].velocity(), 7.6, 1e-3);
  EXPECT_EQ(agent_reaction_tracker_info_map.at(object_id_1)
                .last_timestamp_constraint_added(),
            5900);

  const auto& ego_state_at_timestamp_map =
      mutable_seed.ego_state_at_timestamp_map();
  EXPECT_EQ(ego_state_at_timestamp_map.size(), 51);
  EXPECT_TRUE(ego_state_at_timestamp_map.find(2900) !=
              ego_state_at_timestamp_map.end());
  EXPECT_EQ(ego_state_at_timestamp_map.at(2900).acceleration(), 0.);
  EXPECT_EQ(ego_state_at_timestamp_map.at(2900).speed(), 0.);

  for (int64_t i = 0; i < 60; ++i) {
    int64_t ts = (i + 60) * 100;
    double agent1_velocity = i * 0.2;
    tracked_object1->set_velocity(agent1_velocity);
    double agent2_velocity = i * 0.4;
    tracked_object2->set_velocity(agent2_velocity);
    double agent3_velocity = i * 0.6;
    tracked_object3->set_velocity(agent3_velocity);
    RobotState robot_state = GenerateEgoState(ts);
    agent_reaction_tracker_info_map[object_id_1]
        .set_last_timestamp_constraint_added(ts);
    UpdateEgoAgentVelocityInAgentReactionTrackerSeed(robot_state, agent_list,
                                                     &mutable_seed);
  }
  EXPECT_EQ(agent_reaction_tracker_info_map.size(), 1);
  EXPECT_TRUE(agent_reaction_tracker_info_map.find(object_id_1) !=
              agent_reaction_tracker_info_map.end());
  EXPECT_FALSE(agent_reaction_tracker_info_map.find(object_id_3) !=
               agent_reaction_tracker_info_map.end());
}

TEST(AgentReactionTrackerTest, AgentConflictIntoTracking) {
  speed::Profile ego_profile;
  constexpr double kRaToFb = 4.0;
  constexpr double kRaToRb = 1.0;
  ego_profile.push_back(planner::speed::State(0.0, 0.0, 2.0, 0.0, 0.0));
  ego_profile.push_back(planner::speed::State(1.0, 2.0, 2.0, 0.0, 0.0));
  ego_profile.push_back(planner::speed::State(2.0, 4.0, 2.0, 0.0, 0.0));
  ego_profile.push_back(planner::speed::State(3.0, 6.0, 2.0, 0.0, 0.0));

  prediction::pb::AgentList agent_list;
  speed::ConstraintResult constraint_results;
  std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>
      object_overlap_region_map;
  std::map<ObjectId, speed::pb::ObjectProximityInfo> object_proximity_info_map;

  int64_t pred_1 = 1;
  ObjectId object_id_1 = 1;

  prediction::pb::Agent* agent1 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object1 = agent1->mutable_tracked_object();
  tracked_object1->set_id(object_id_1);
  tracked_object1->set_object_type(voy::perception::ObjectType::VEHICLE);

  const std::string unique_id_1 = GetBpUniqueId(object_id_1, pred_1);
  constraint_results.constraints.push_back(
      Constraint(pb::Constraint::SPEED_OBJECT, speed::pb::FenceType::kCross,
                 "cross", "", object_id_1, unique_id_1));
  constraint_results.decisions.push_back(pb::SpeedDecision::PASS);
  pb::OverlapRegion overlap_region_1;
  pb::OverlapSlice overlap_slice_1;
  overlap_slice_1.set_relative_time_in_msec(1000);
  overlap_slice_1.mutable_ego_moving_distance_strict()->set_start(2.0 -
                                                                  kRaToFb);
  overlap_slice_1.mutable_ego_moving_distance_strict()->set_end(5.0 + kRaToRb);
  *(overlap_region_1.add_overlap_slices()) = std::move(overlap_slice_1);
  overlap_region_1.set_start_strict_relative_time_in_msec(1000);
  overlap_region_1.set_start_strict_relative_time_in_sec(1.0);
  overlap_region_1.set_contain_strict_overlap(true);
  overlap_region_1.set_trajectory_id(pred_1);
  object_overlap_region_map[object_id_1].push_back(std::move(overlap_region_1));
  // update proximity info
  speed::pb::ObjectProximityInfo proximity_info_1;
  proximity_info_1.set_signed_lateral_gap(2.0);
  proximity_info_1.set_signed_lateral_speed(2.0);
  object_proximity_info_map[object_id_1] = proximity_info_1;

  ObjectId object_id_2 = 2;
  prediction::pb::Agent* agent2 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object2 = agent2->mutable_tracked_object();
  tracked_object2->set_id(object_id_2);
  const std::string unique_id_2 = GetBpUniqueId(object_id_2, pred_1);
  constraint_results.constraints.push_back(
      Constraint(pb::Constraint::SPEED_OBJECT, speed::pb::FenceType::kCross,
                 "cross", "", object_id_2, unique_id_2));
  constraint_results.decisions.push_back(pb::SpeedDecision::YIELD);
  pb::OverlapRegion overlap_region_2;
  pb::OverlapSlice overlap_slice_2;
  overlap_slice_2.set_relative_time_in_msec(2000);
  overlap_slice_2.mutable_ego_moving_distance_strict()->set_start(5.0 -
                                                                  kRaToFb);
  overlap_slice_2.mutable_ego_moving_distance_strict()->set_end(7.0 + kRaToRb);
  *(overlap_region_2.add_overlap_slices()) = std::move(overlap_slice_2);
  overlap_region_2.set_start_strict_relative_time_in_msec(2000);
  overlap_region_2.set_start_strict_relative_time_in_sec(2.0);
  overlap_region_2.set_contain_strict_overlap(true);
  overlap_region_2.set_trajectory_id(pred_1);
  object_overlap_region_map[object_id_2].push_back(std::move(overlap_region_2));
  // update proximity info
  speed::pb::ObjectProximityInfo proximity_info_2;
  proximity_info_2.set_signed_lateral_gap(2.0);
  proximity_info_2.set_signed_lateral_speed(2.0);
  object_proximity_info_map[object_id_2] = proximity_info_2;

  speed::pb::AgentReactionTrackerSeed mutable_seed;

  UpdateAgentConflictInfoInAgentReactionTrackerSeed(
      agent_list, ego_profile, constraint_results, object_overlap_region_map,
      object_proximity_info_map,
      /*curr_timestamp=*/0, kRaToFb, &mutable_seed);

  const auto& agent_reaction_tracker_info_map =
      mutable_seed.agent_reaction_tracker_info_map();
  EXPECT_EQ(agent_reaction_tracker_info_map.size(), 2);
  EXPECT_TRUE(agent_reaction_tracker_info_map.find(object_id_1) !=
              agent_reaction_tracker_info_map.end());
  EXPECT_TRUE(agent_reaction_tracker_info_map.find(object_id_2) !=
              agent_reaction_tracker_info_map.end());

  const auto& agent_conflict_info_tracking_1 =
      agent_reaction_tracker_info_map.at(object_id_1)
          .agent_conflict_info_tracking();
  const auto& agent_conflict_info_tracking_2 =
      agent_reaction_tracker_info_map.at(object_id_2)
          .agent_conflict_info_tracking();
  EXPECT_EQ(agent_conflict_info_tracking_1.size(), 1);
  EXPECT_EQ(agent_conflict_info_tracking_2.size(), 1);
  EXPECT_EQ(agent_conflict_info_tracking_1.begin()->t_gap(), 0);
  EXPECT_EQ(agent_conflict_info_tracking_1.begin()->distance_gap(), -4);
  EXPECT_EQ(agent_conflict_info_tracking_2.begin()->t_gap(), -0.5);
  EXPECT_EQ(agent_conflict_info_tracking_2.begin()->distance_gap(), -3);
}

TEST(AgentIntentionTrackerTest,
     ComputeAgentYieldProbabilityWithIntentionTracking) {
  prediction::pb::AgentList agent_list;
  ::planner::pb::DecoupledManeuverSeed previous_iter_seed;
  ObjectId object_id_1 = 1;
  prediction::pb::Agent* agent1 = agent_list.add_agent_list();
  voy::TrackedObject* tracked_object1 = agent1->mutable_tracked_object();
  tracked_object1->set_id(object_id_1);
  tracked_object1->set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object1->set_velocity(4.0);
  tracked_object1->set_center_x(-8772.61);
  tracked_object1->set_center_y(-33423.89);
  previous_iter_seed.mutable_speed_seed()
      ->add_constraint_results()
      ->mutable_constraint()
      ->set_obj_id(object_id_1);

  speed::pb::AgentReactionTrackerSeed mutable_seed;
  const math::Pose2d ego_pose(/*x_in=*/-8797.37, /*y_in=*/-33434.34,
                              /*yaw_in=*/0.0);
  // Construct a reasoning object to test agent yield probability with intention
  // tracking.
  pb::ObjectProximityInfo object_proximity_info;
  const planner::pb::RequiredLateralGapConfig empty_config;
  const TrafficParticipantPose pose_at_plan_init_ts(0, *tracked_object1);
  const PlannerObject planner_object1(*agent1, 0, pose_at_plan_init_ts);
  const PrincipledRequiredLateralGap required_lateral_gap =
      planner::ComputePrincipledRequiredLateralGap(planner_object1,
                                                   empty_config);
  const ReasoningObject reasoning_object1(
      planner_object1, object_proximity_info, required_lateral_gap,
      /*object_occupancy_param_ptr=*/nullptr);
  *previous_iter_seed.mutable_agent_reaction_tracker_seed() = mutable_seed;
  AgentIntentionTracker agent_intention_tracker;
  std::unordered_set<int64_t> interactive_agent_ids;
  interactive_agent_ids.insert(object_id_1);
  std::string debug_str;
  // Test with different yield intention values.
  EXPECT_EQ(
      0.4,
      agent_intention_tracker.ComputeAgentYieldProbabilityWithIntentionTracking(
          interactive_agent_ids,
          /*ego_vel = */ 5.0, /*ra_to_rear_bumper_shift = */ 1.0,
          /* plan_init_timestamp = */ 53545,
          /* cbp_yield_intention = */ 0.8, /* lane_change_info = */ {},
          reasoning_object1, object_proximity_info, previous_iter_seed,
          debug_str));
  EXPECT_NEAR(
      0.4,
      agent_intention_tracker.ComputeAgentYieldProbabilityWithIntentionTracking(
          interactive_agent_ids,
          /*ego_vel = */ 5.0, /*ra_to_rear_bumper_shift = */ 1.0,
          /* plan_init_timestamp = */ 53545,
          /* cbp_yield_intention = */ 0.2, /* lane_change_info = */ {},
          reasoning_object1, object_proximity_info, previous_iter_seed,
          debug_str),
      1e-2);
}

}  // namespace speed
}  // namespace planner
