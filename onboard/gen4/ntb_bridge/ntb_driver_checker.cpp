#include "ntb_driver_checker.h"

#include <algorithm>
#include <fstream>
#include <glog/logging.h>
#include <sstream>
#include <string>
#include <unistd.h>
#include <unordered_set>

#include "av_comm/onboard_config.h"

namespace ntb_bridge {

bool IsGen4NtbDriverLoaded() {
  if (!av_comm::IsGen4Platform()) {
    LOG(ERROR) << "This function is only applicable for Gen4 platform.";
    return false;
  }
  std::unordered_set<std::string> modules_required = {
      "switchtec", "ntb", "switchtec_ntb", "ntb_shm"};

  // 1. Check if ntb modules exists in /proc/modules
  std::ifstream modules_file("/proc/modules");
  if (!modules_file.is_open()) {
    LOG(ERROR) << "Failed to open /proc/modules";
    return false;
  }
  std::string line;
  while (std::getline(modules_file, line)) {
    std::stringstream ss(line);
    std::string module_name;
    ss >> module_name;
    if (modules_required.count(module_name)) {
      modules_required.erase(module_name);
    }
  }
  if (!modules_required.empty()) {
    for (const auto& mod : modules_required) {
      LOG(ERROR) << "Required ntb modules not loaded: " << mod;
    }
    return false;
  }

  // 2. Check if ntb devices exists
  std::array<const char*, 2> devices_required = {"/dev/ntb_dev0",
                                                 "/dev/ntb_dev1"};
  bool devices_all_existed = std::all_of(
      devices_required.begin(), devices_required.end(), [](const char* device) {
        if (access(device, F_OK) != 0) {
          LOG(ERROR) << "Required ntb device not found: " << device;
          return false;
        }
        return true;
      });
  if (!devices_all_existed) {
    LOG(ERROR) << "Not all required ntb devices are present.";
    return false;
  }
  LOG(INFO) << "All required NTB modules and devices are present.";
  return true;
}

}  // namespace ntb_bridge
