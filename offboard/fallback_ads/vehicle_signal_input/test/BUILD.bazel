load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "vehicle_signal_input_main_test",
    srcs = ["vehicle_signal_input_main_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-float-conversion",
    ],
    deps = [
        "//offboard/fallback_ads:fallback_l2_simulation",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_main",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_parameter",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_type",
        "@voy-sdk//:gtest",
    ],
)
