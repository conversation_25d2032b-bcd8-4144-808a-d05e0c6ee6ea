#ifndef ONBOARD_ROUTING_UTILITY_RT_EVENT_UTIL_H_
#define ONBOARD_ROUTING_UTILITY_RT_EVENT_UTIL_H_

#include <map>
#include <optional>
#include <set>
#include <string>
#include <utility>

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include <json/json.hpp>

#include "routing_protos/planning_route_state.pb.h"
#include "routing_protos/route_status.pb.h"
#include "routing_protos/state/state_manager.pb.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_routing.h"

namespace routing {
namespace utility {
using Json = nlohmann::json;

// A struct indicating the order information of the route.
struct OrderInfo {
  OrderInfo() = default;
  OrderInfo(const pb::RouteQuery& route_query,
            std::optional<pb::CommandType> command_type,
            std::optional<pb::CommandPurpose> command_purpose)
      : order_id(route_query.order_id()),
        command_type(std::move(command_type)),
        command_purpose(std::move(command_purpose)),
        match_type(route_query.match_type()),
        operation_type(route_query.route_operation_info().operation_type()),
        mission_id(route_query.mission_id()),
        query_timestamp(route_query.query_timestamp()) {}

  // Converts the order info to a string for rt event posting or log printing.
  std::string GetOrderString() const;

  // Checks if order info is valid and available.
  bool IsValid() const;

  void Clear();

  std::string order_id = std::string();
  std::optional<pb::CommandType> command_type = std::nullopt;
  std::optional<pb::CommandPurpose> command_purpose = std::nullopt;
  order::pb::MatchType match_type = order::pb::MatchType();
  pb::RouteOperationInfo::OperationType operation_type =
      pb::RouteOperationInfo::OperationType();
  int64_t mission_id = -1;
  int64_t query_timestamp = -1;
};

template <typename T>
void AddKeyValuePairsToJson(Json& j, const std::string& key, const T& value) {
  j[key] = value;
}

template <typename T, typename... Args>
void AddKeyValuePairsToJson(Json& j, const std::string& key, const T& value,
                            const std::string& nextKey, Args&&... args) {
  j[key] = value;
  AddKeyValuePairsToJson(j, nextKey, std::forward<Args>(args)...);
}

// Adds RT-Event and record detail information in the |event_info| in json
// format.
template <typename Event, typename... Args>
void AddRtEventAsJson(const std::string& key1, Args&&... args) {
  Json json;
  AddKeyValuePairsToJson(json, key1, std::forward<Args>(args)...);
  rt_event::PostRtEvent<Event>(json.dump());
}

template <typename Event>
void AddRtEventAsJson(const OrderInfo& order_info) {
  AddRtEventAsJson<Event>("order_info", order_info.GetOrderString());
}

template <typename Event, typename... Args>
void AddRtEventAsJson(const OrderInfo& order_info, const std::string& key1,
                      Args&&... args) {
  AddRtEventAsJson<Event>("order_info", order_info.GetOrderString(), key1,
                          std::forward<Args>(args)...);
}

// With an order info string, adds RT-Event and records detailed information
// from a dynamic map into |event_info| in json format.
template <typename Event, typename Value>
void AddRtEventAsJsonFromMap(const std::optional<OrderInfo>& order_info,
                             const std::map<std::string, Value>& rt_event_map) {
  DCHECK(!rt_event_map.empty());
  if (rt_event_map.empty()) {
    return;
  }
  Json json;
  if (order_info.has_value()) {
    json["order_info"] = order_info->GetOrderString();
  }
  for (const auto& [key, value] : rt_event_map) {
    json[key] = value;
  }
  rt_event::PostRtEvent<Event>(json.dump());
}

// Adds rt event if find a endless while loop when constructing path.
void AddRtEventForAstarHasEndLessLoopWhenConstructPath(
    int64_t backtrack_edge_nums, int64_t cost_map_size);

// Adds rt event if find a loop in astar search result.
void AddRtEventForAstarFindALoopWhenConstructPath(int64_t node_id);

// Adds rt event if find a nullptr when constructing path.
void AddRtEventForAstarFindNullPtrWhenConstructPath();

// Adds rt event if find a nullptr when executing astar search.
void AddRtEventForAstarFindNullPtrWhenExecuteSearch();

// Adds rt event if astar has empty queue in warm start.
void AddRtEventForAstarHasEmptyQueueInWarmStart(
    int64_t node_ids_reset_to_open_status_size);

// Adds rt event if negative cost is detected in global astar.
void AddRtEventForGlobalAstarHasNegativeCost(int64_t from_id, int64_t to_id,
                                             double cost,
                                             const std::string& edge_type);

// Adds rt_event if astar search in cold start successfully after last routing
// failed in warm start.
void AddRtEventForRoutingResearchAfterFailedWarmStartSearch(bool found_path);

// Adds rt_event if astar search in cold start successfully after last routing
// failed for finding a loop in astar result.
void AddRtEventForRoutingResearchWithLoopInResult(bool found_path);

// Adds rt_event if astar search in cold start successfully after last routing
// failed for finding a nullptr in astar search.
void AddRtEventForRoutingResearchWithNullPtrInAstarSearch(bool found_path);

// Adds rt_event if astar search in cold start successfully after last routing
// failed for finding a nullptr in astar search.
void AddRtEventForRoutingResearchWithNullPtrInBackTrackPath(bool found_path);

// Adds rt_event if astar search in cold start successfully after last routing
// failed for illegal backtrack path.
void AddRtEventForRoutingResearchWithIllegalBackTrackPath(bool found_path);

// Adds rt_event if astar search again successfully after last routing failure
// with unknown reason.
void AddRtEventForRoutingResearchWithUnknownReasonFailure(bool found_path);

// Adds rt event if no source roads connectable with target roads.
void AddRtEventForRecommendReplanHasZeroConnectableSourceRoads(
    const std::set<int64_t>& source_road_ids,
    const std::set<int64_t>& target_road_ids);

// Adds rt event if astar has valid recommend replan.
void AddRtEventForAstarHasValidRecommendReplan();

// Adds rt event if the number of target_road_ids is zero.
void AddRtEventForRecommendReplanHasZeroTargetRoads();

// Adds rt event and logs the elapsed time of the a star search, if it exceeds
// the threshold.
void AddRtEventForAstarExceedsTimeThreshold(
    double warm_astar_max_run_time_ms, double cold_astar_max_run_time_ms,
    double elapsed_time, bool is_warmstart, bool found_path,
    bool has_searched_all_recommend_lanes);

// Adds rt event and logs if failed to read constraint from cloud for routing.
void AddRtEventForFailedToReadConstraintFromCloudForRouting();

// Adds rt event if global path diverge with regional path after replan.
void AddRtEventForGlobalPathDivergeWithRegionalPath();

// Adds rt event if global path converge with regional path after replan.
void AddRtEventForGlobalPathConvergeWithRegionalPath();

// Adds rt event if route status manager has illegal rejecting path.
void AddRtEventForRouteStatusManagerHasIllegalRejectingPath();

// Adds rt event if query manager has not prepared for replan query.
void AddRtEventForQueryManagerHasNotPreparedForReplan();

// Adds rt event if route solution has no destination lane candidate in path.
void AddRtEventForRouteSolutionHasNoDestinationLaneCandidateInPath();

// Adds rt event if find no valid lane candidates for origin point.
void AddRtEventForGlobalRouteHasNoLaneCandidatesForOrigin(
    const voy::Point2d& point);

// Adds rt event if origin is inconsistent with ego pose.
void AddRtEventForOriginLaneInconsistentWithPose(int64_t road_id);

// Adds rt event if recommend replan infos are not empty.
void AddRtEventForRecommendReplan(bool success);

// Adds rt event for routing search results.
void AddRtEventForRoutingSearch(bool success, bool is_replan,
                                bool is_unknown_failure);

// Adds rt event for planning reject dangerous new route.
void AddRtEventForPlanningRejectNewRoute(
    const pb::RouteSolution& route_solution, int64_t timestamp,
    bool is_in_junction);

// Adds rt event for routing command.
void AddRtEventForRouteCommand(const std::optional<pb::RouteQuery>& route_query,
                               pb::CommandType command_type,
                               pb::CommandPurpose command_purpose);

// Adds rt event for routing confirmation.
void AddRtEventForRouteConfirmation(const pb::RouteSolution& route_solution,
                                    pb::RouteState route_state);

// Adds rt event for eda and eta.
void AddRtEventForEDAAndETA(const routing::pb::RouteStatus& route_status);

// Adds rt event for PDZ generation failure.
void AddRtEventForPDZGenerationFailure(const pb::RouteSolution& route_solution,
                                       const ::pb::OddInfo::OddType& odd_type,
                                       const std::string& failure_reason);

// Adds rt event for same origin and dest route.
void AddRtEventForSameOriginAndDest(const pb::RouteQuery& route_query,
                                    const voy::Point2d& origin_pose,
                                    const voy::Point2d& destination_pose);

// Adds rt event for static same origin and dest.
void AddRtEventForStaticSameOriginAndDest(const pb::RouteQuery& route_query);

// Adds rt event for immediate pullover by same origin and dest.
void AddRtEventForSameOriginAndDestImmediatePullover(
    const pb::RouteQuery& route_query);

// Adds rt event for routing failed for first ride query.
void AddRtEventForRoutingFailedForFirstRideQuery(
    const pb::RouteQuery& route_query, const std::string& error_msg,
    const std::string& failed_reason);

// Adds rt event for routing failed for replan query.
void AddRtEventForRoutingFailedForReplanQuery(const pb::RouteQuery& route_query,
                                              const std::string& error_msg,
                                              const std::string& failed_reason);

// Adds rt event for routing failed for changing mission ride query.
void AddRtEventForRoutingFailedForChangingMissionRideQuery(
    const pb::RouteQuery& route_query, const std::string& error_msg,
    const std::string& failed_reason);

// Adds rt event for routing success for new route.
void AddRtEventForRoutingSuccessForNewRoute(
    const pb::RouteQuery& route_query,
    const pb::CommandPurpose& command_purpose);

// Adds rt event for pullout confirmed.
void AddRtEventForPulloutIsConfirmed(const pb::RouteSolution& route_solution);

// Adds rt event for pullover triggered.
void AddRtEventForPulloverIsTriggered(const pb::RouteSolution& route_solution);

}  // namespace utility
}  // namespace routing

#endif  // ONBOARD_ROUTING_UTILITY_RT_EVENT_UTIL_H_
