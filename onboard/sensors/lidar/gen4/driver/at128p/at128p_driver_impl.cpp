#include "at128p_driver_impl.h"

#include <iomanip>
#include <map>
#include <utility>
#include <vector>

#include <boost/asio/io_context.hpp>
#include <glog/logging.h>
#include <tbb/concurrent_queue.h>

#include "at128p_packet_checker.h"
#include "base/now.h"
#include "driver/driver_defines.h"
#include "driver/lidar_event.h"
#include "driver/udp_parser/at128p_corrections.h"
#include "driver/udp_parser/at128p_udp_protocol.h"
#include "fault_message.h"
#include "module/udp/udp_receiver.h"
#include "moodycamel_queue/readerwritercircularbuffer.h"
#include "rate_limited_log/rate_limited_log.h"
#include "trace/trace.h"
#include "voy_protos/lidar_scan.pb.h"
#include "voy_trace/trace_gen4_lidar.h"

namespace lidar {
namespace {

constexpr int kDataPacketSize = 1118;
constexpr int kFaultMessagePacketSize = 99;
// constexpr int kLogReportPacketSize = 273;

// FOV Minimum Angle range minimum value
constexpr int kAT128PFOVMinimumAngleRangeMinimumAngle = 28;
constexpr int kAT128PFOVMinimumAngleRangeMaximumAngle = 31;

// FOV Maximum Angle range Maximum value
constexpr int kAT128PMaximumAngleRangeMinimumAngle = 152;
constexpr int kAT128PMaximumAngleRangeMaximumAngle = 155;
constexpr int kAT128PMaxPacketCountInOnePeriod = 1250;

constexpr int kAT128PAngleRatio = 256 * 100;

constexpr int kAT128PMaxPointsNum = 128 * 2;

// guess:
// clockwise rotation direction 16, counteclockwise rotation direction 17
constexpr int kAT128PClockwiseRotationDirection = 16;
constexpr int kAT128PCounterclockwiseRotationDirection = 17;

std::string uint32_to_ipv4string(uint32_t ip) {
  std::stringstream ss;
  ss << ((ip >> 24) & 0xFF) << '.' << ((ip >> 16) & 0xFF) << '.'
     << ((ip >> 8) & 0xFF) << '.' << (ip & 0xFF);
  return ss.str();
}

void PrintFaultMessageInHex(
    const std::shared_ptr<std::vector<uint8_t>> &packet_buffer_ptr) {
  if (packet_buffer_ptr && !packet_buffer_ptr->empty()) {
    std::stringstream hex_stream;
    hex_stream << std::hex << std::setfill('0');
    for (const auto &byte : *packet_buffer_ptr) {
      hex_stream << std::setw(2) << static_cast<int>(byte);
    }
    LOG(ERROR) << "Fault message: " << hex_stream.str();
  } else {
    LOG(WARNING) << "Packet buffer is empty or null";
  }
}

int64_t Debug_ParseTimeStamp(const uint8_t utc[6], uint32_t timestamp) {
  int64_t unix_microsecond = 0;
  const int64_t kMicrosecondRatio = 1e6;
  if (utc[0] != 0) {
    struct tm t;
    t.tm_year = utc[0];
    if (t.tm_year >= 200) {
      t.tm_year -= 100;
    }
    t.tm_mon = utc[1] - 1;
    t.tm_mday = utc[2];
    t.tm_hour = utc[3];
    t.tm_min = utc[4];
    t.tm_sec = utc[5];
    t.tm_isdst = 0;

    unix_microsecond = static_cast<int64_t>(timegm(&t)) * kMicrosecondRatio +
                       static_cast<int64_t>(timestamp);
  } else {
    const uint32_t utc_time_big =
        *reinterpret_cast<const uint32_t *>(&utc[0] + 2);

    unix_microsecond =
        (((utc_time_big >> 24) & 0xff) | ((utc_time_big >> 8) & 0xff00) |
         ((utc_time_big << 8) & 0xff0000) | ((utc_time_big << 24))) *
            kMicrosecondRatio +
        static_cast<int64_t>(timestamp);
  }

  return unix_microsecond;
}

void Debug_ParsePacket(const std::vector<uint8_t> &packet_buffer,
                       int64_t &timestamp_microsecond,
                       uint32_t &sequence_number) {
  CHECK(!packet_buffer.empty());
  const AT128PDataPacket *data_packet =
      reinterpret_cast<const AT128PDataPacket *>(packet_buffer.data());

  if (data_packet->packet_header.pkt_start_flag[0] != 0xEE ||
      data_packet->packet_header.pkt_start_flag[1] != 0xFF) {
    LOG(ERROR) << "Error start of packet. "
               << static_cast<int>(data_packet->packet_header.pkt_start_flag[0])
               << " "
               << static_cast<int>(
                      data_packet->packet_header.pkt_start_flag[1]);
    return;
  }
  timestamp_microsecond = Debug_ParseTimeStamp(
      data_packet->packet_tail.utc, data_packet->packet_tail.timestamp);
  sequence_number = static_cast<int>(data_packet->packet_tail.sequence_number);
}

}  // namespace

AT128PDriverImpl::AT128PDriverImpl(
    const std::string &ip_address, const std::string &remote_ip_address,
    const std::string &interface_address, bool is_multicast, int tcp_port,
    int udp_port, LidarOptions lidar_options,
    IScanDataHandler *scan_data_handler,
    const std::function<void(pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t,
                             bool)> &data_callback,
    const std::function<void(LidarEvent, const std::string &)>
        &message_callback,
    boost::asio::io_service *io_service)
    : io_service_(io_service),
      ip_address_(ip_address),
      remote_ip_address_(remote_ip_address),
      interface_address_(interface_address),
      is_multicast_(is_multicast),
      tcp_port_(tcp_port),
      udp_port_(udp_port),
      last_angle_(-1),
      scan_data_handler_(scan_data_handler),
      data_callback_(data_callback),
      message_callback_(message_callback),
      ptc_client_(io_service, ip_address, tcp_port),
      at128p_udp_parser_(lidar_options),
      at128p_packet_checker_(ip_address) {
  (void)kAT128PFOVMinimumAngleRangeMinimumAngle;
  (void)kAT128PMaximumAngleRangeMaximumAngle;
  (void)kAT128PClockwiseRotationDirection;
  (void)kAT128PMaxPacketCountInOnePeriod;

  LOG(INFO) << " AT128PDriverImpl constrcutor called. "
            << " ip_address: " << ip_address
            << " remote_ip_address: " << remote_ip_address_
            << " interface_address: " << interface_address_
            << " tcp_port: " << tcp_port_ << " udp_port: " << udp_port_
            << " options min_distance: " << lidar_options.max_range
            << " options max_distance: " << lidar_options.min_range;
}

AT128PDriverImpl::~AT128PDriverImpl() { Stop(); }

boost::system::error_code AT128PDriverImpl::Start() {
  boost::system::error_code error_code = ptc_client_.Connect();
  if (error_code) {
    LOG(ERROR) << "Failed to connect lidar. ip: " << ip_address_
               << ". error: " << error_code.message();
    return error_code;
  }

  std::vector<uint8_t> result;
  error_code = ptc_client_.GetLidarCalibration(result);
  if (error_code) {
    LOG(ERROR) << "Failed to get calibration. ip: " << ip_address_
               << ". error: " << error_code.message();
    return error_code;
  }
  LOG(INFO) << "Successfully get calibration. ip: " << ip_address_;

  std::unique_ptr<AT128PCorrections> at128p_corrections =
      std::make_unique<AT128PCorrections>();
  if (!at128p_corrections->Parse(reinterpret_cast<char *>(result.data()))) {
    LOG(ERROR) << "Failed to parse corrections. ip: " << ip_address_;
    return boost::system::errc::make_error_code(
        boost::system::errc::bad_message);
  }
  LOG(INFO) << "Successfully parse corrections. ip: " << ip_address_;

  intrinsic_parameter_ = std::move(result);
  at128p_udp_parser_.set_intrinsic_config(std::move(at128p_corrections));

  auto array_to_string = [](uint8_t *data, int len) -> std::string {
    if (data == nullptr || len < 1 || 256 < len) {
      return "";
    }
    std::vector<char> str;
    str.resize(len + 1);
    strncpy(&str[0], (const char *)data, len);
    str.emplace_back('\0');
    std::string ret_str(str.data());
    return ret_str;
  };

  PTCConfigInfo config_info;
  error_code = ptc_client_.GetConfigInfo(config_info);
  if (error_code) {
    LOG(ERROR) << "Failed to get config info. ip: " << ip_address_
               << ". error: " << error_code.message();
    return error_code;
  }

  LOG(INFO) << "Lidar config info: " << uint32_to_ipv4string(config_info.ipaddr)
            << ", motor_type_status: "
            << static_cast<int>(config_info.motor_type_status);

  if (config_info.motor_type_status ==
      kAT128PCounterclockwiseRotationDirection) {
    is_clockwise_rotation_direction_ = false;
  }

  PtcGetInventoryInfo inventory_info;
  error_code = ptc_client_.GetInventoryInfo(inventory_info);
  if (error_code) {
    LOG(ERROR) << "Failed to get inventory info. ip: " << ip_address_
               << ". error: " << error_code.message();
    return error_code;
  }

  LOG(INFO) << "Lidar inventory info: "
            << " ip: " << ip_address_ << " sn: "
            << array_to_string(inventory_info.sn, sizeof(inventory_info.sn))
            << " sw_ver: "
            << array_to_string(inventory_info.sw_ver,
                               sizeof(inventory_info.sw_ver))
            << " hw_ver: "
            << array_to_string(inventory_info.hw_ver,
                               sizeof(inventory_info.hw_ver))
            << " control_fw_ver: "
            << array_to_string(inventory_info.control_fw_ver,
                               sizeof(inventory_info.control_fw_ver))
            << " fpga_para_ver: "
            << array_to_string(inventory_info.fpga_para_ver,
                               sizeof(inventory_info.fpga_para_ver))
            << " fpga_cfg_ver: "
            << array_to_string(inventory_info.fpga_cfg_ver,
                               sizeof(inventory_info.fpga_cfg_ver));

  std::shared_ptr<CircularQueue> packet_queue_ptr =
      std::make_shared<CircularQueue>(1024 * 4);
  point_cloud_queue_.set_capacity(1260 * 8);
  auto on_packet_callback =
      [this,
       packet_queue_ptr](std::shared_ptr<std::vector<uint8_t>> packet_buffer) {
        if (packet_buffer->size() == kDataPacketSize) {
          int64_t timestamp;
          uint32_t sequence;
          Debug_ParsePacket(*packet_buffer, timestamp, sequence);
          if (std::abs(static_cast<int>(sequence) -
                       static_cast<int>(last_sequence_)) > 16) {
            RGLOG(WARNING) << "Packet lost, last:" << last_sequence_
                           << ",s:" << sequence
                           << ",d:" << sequence - last_sequence_
                           << ",t:" << timestamp;
          }
          last_sequence_ = sequence;
        }

        while (!packet_queue_ptr->try_enqueue(packet_buffer)) {
          RGLOG(WARNING) << "The udp queue is full. " << udp_port_ << ","
                         << packet_queue_ptr->size_approx();
          std::shared_ptr<std::vector<uint8_t>> drop_packet;
          packet_queue_ptr->try_dequeue(drop_packet);
        }
      };

  work_thread_ = std::make_unique<std::thread>([this,
                                                packet_queue_ptr]() -> void {
    while (!this->stop_flag_) {
      try {
        std::shared_ptr<std::vector<uint8_t>> packet_buffer;
        if (packet_queue_ptr->wait_dequeue_timed(packet_buffer, 30 * 1000)) {
          int pending_packet_count =
              static_cast<int>(packet_queue_ptr->size_approx());
          if (pending_packet_count > 200) {
            RGLOG(WARNING) << "The udp queue pending. " << udp_port_ << ","
                           << pending_packet_count;
          }
          this->OnPacket(packet_buffer);
        }
      } catch (const std::exception &e) {
        LOG(ERROR) << "Exception caught in receive thread. " << e.what();
      }
    }
  });
  return ReadSpawn(
      io_service_, udp_port_, 100, remote_ip_address_, is_multicast_,
      interface_address_, on_packet_callback,
      std::bind(&AT128PDriverImpl::OnError, this, std::placeholders::_1));
}

boost::system::error_code AT128PDriverImpl::Stop() {
  // TODO(peytonwangpeng): Implement specific functions
  if (!stop_flag_) {
    stop_flag_ = true;
    work_thread_->join();
  }
  return boost::system::error_code();
}

boost::system::error_code AT128PDriverImpl::GetData(
    pcl::PointCloud<lidar::PointXYZIR>::Ptr &point_cloud, int64_t &timestamp,
    bool &is_cycle_completed) {
  std::tuple<pcl::PointCloud<lidar::PointXYZIR>::Ptr, int64_t, bool> tuple;
  if (!point_cloud_queue_.try_pop(tuple)) {
    return boost::system::errc::make_error_code(
        boost::system::errc::no_message_available);
  }

  point_cloud = std::get<0>(tuple);
  timestamp = std::get<1>(tuple);
  is_cycle_completed = std::get<2>(tuple);
  return boost::system::error_code();
}

boost::system::error_code AT128PDriverImpl::GetIntrinsicParameter(
    std::vector<uint8_t> &intrinsic_parameter) {
  intrinsic_parameter = intrinsic_parameter_;
  return boost::system::error_code();
}

boost::system::error_code AT128PDriverImpl::SetReturnType(
    int /* return_type */) {
  // TODO(peytonwangpeng): Implement specific functions
  return boost::system::error_code();
}

boost::system::error_code AT128PDriverImpl::GetStatus(int & /* temperature */,
                                                      int & /* motor */,
                                                      int & /* ptp_status */) {
  // TODO(peytonwangpeng): Implement specific functions
  PTCLidarStatus ptc_lidar_status;
  boost::system::error_code error_code =
      ptc_client_.GetLidarStatus(ptc_lidar_status);
  if (error_code) {
    LOG(ERROR) << "Failed to get lidar status. ip: " << ip_address_
               << ". error: " << error_code.message();
    return error_code;
  }

  auto get_status_string = [](int status) {
    const std::map<int, std::string> status_map = {
        {0, "Free run"}, {1, "Tracking"}, {2, "Locked"}, {3, "Frozen"}};

    if (status_map.find(status) != status_map.end()) {
      return status_map.at(status);
    } else {
      return std::string{"Unknown status"};
    }
  };
  // PTP status 0 — Free run 1 — Tracking 2 — Locked 3 — Frozen
  LOG_EVERY_N(INFO, 5) << "Lidar status: "
                       << "[" << ip_address_ << "]"
                       << " Temperature: "
                       << ptc_lidar_status.temperature[2] / 100
                       << " Motor Speed: " << ptc_lidar_status.motor_speed
                       << " PTP Status: "
                       << static_cast<int>(ptc_lidar_status.ptp_status) << " - "
                       << get_status_string(ptc_lidar_status.ptp_status);

  PTCPTPStatus ptc_ptp_status;
  error_code = ptc_client_.GetPTPStatus(ptc_ptp_status);
  if (error_code) {
    LOG(ERROR) << "Failed to get ptp status. error: " << error_code.message();
    return error_code;
  }
  LOG_EVERY_N(INFO, 5) << "PTP status: "
                       << "[" << ip_address_ << "]"
                       << " PTP master_offset: " << ptc_ptp_status.master_offset
                       << "ns"
                       << " mean_offset: " << ptc_ptp_status.mean_offset << "ns"
                       << " ptp_state: " << ptc_ptp_status.ptp_state
                       << " elapsed_millisec: "
                       << ptc_ptp_status.elapsed_millisec << "ms";
  return error_code;
}

void AT128PDriverImpl::OnPacket(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  if (packet_buffer_ptr->empty()) {
    LOG(ERROR) << "Incorrect packet. size:0 ip: " << ip_address_;
    return;
  }
  if (packet_buffer_ptr->size() == kFaultMessagePacketSize) {
    OnFaultMessage(std::move(packet_buffer_ptr));
  } else if (packet_buffer_ptr->size() == kDataPacketSize) {
    OnData(std::move(packet_buffer_ptr));
  } else {
    LOG_EVERY_N(WARNING, 100) << "Ignore the message. ip: " << ip_address_
                              << ". size:" << packet_buffer_ptr->size();
  }
}

void AT128PDriverImpl::OnError(const boost::system::error_code &error_code) {
  RGLOG(ERROR) << "Received data transmission error. ip: " << ip_address_
               << ". error: " << error_code.message();
  if (message_callback_) {
    // TODO(peytonwangpeng): Implement specific functions
    message_callback_({LidarEvent::EventCode::kUdpReceivedTimeoutError,
                       LidarEvent::State::kOccur},
                      "LidarEvent::DataReceiveTimeout");
  }
}

// TODO(peytonwangpeng): This function needs to be optimized
void AT128PDriverImpl::OnData(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  TRACE_EVENT_SCOPE(gen4_lidar, ProcessData, udp_port_);

  if (packet_buffer_ptr->data()[0] != 0xEE ||
      packet_buffer_ptr->data()[1] != 0xFF) {
    LOG(ERROR) << "Incorrect AT128 data header. ip: " << ip_address_;
    return;
  }

  at128p_packet_checker_.CountPacketReceived();

  int64_t timestamp;
  uint32_t sequence;
  int scan_angle;
  int16_t motor_speed;
  int8_t shutdown_flag;
  at128p_udp_parser_.ParsePacketSummary(*packet_buffer_ptr, timestamp, sequence,
                                        scan_angle, motor_speed, shutdown_flag);

  std::string error_message;
  LidarEvent::EventCode event_code;
  std::tie(event_code, error_message) = at128p_packet_checker_.Check(
      timestamp, sequence, scan_angle, motor_speed, shutdown_flag,
      is_clockwise_rotation_direction_ ? last_angle_ > scan_angle
                                       : last_angle_ < scan_angle);
  if (event_code != LidarEvent::EventCode::kNormal) {
    if (message_callback_) {
      message_callback_({event_code, LidarEvent::State::kOccur}, error_message);
    }
  }

  if (IsDiscard(scan_angle)) {
    VLOG(2) << "Discard frame. angle: " << float(scan_angle) / kAT128PAngleRatio
            << ". ip: " << ip_address_;
    last_angle_ = scan_angle;
    return;
  }

  DCHECK(scan_data_handler_);
  pb::LidarScanSector *lidar_scan_sector = AllocateScanSector();

  at128p_udp_parser_.ParseUdpData(*packet_buffer_ptr, lidar_scan_sector);
  lidar_scan_sector->set_timestamp(static_cast<int64_t>(timestamp / 1000));

  if (lidar_scan_sector->positions_size() == 0) {
    LOG_EVERY_N(WARNING, 1000)
        << "The valid point cloud data in the packet is empty.[1/1000]";
  }
  scan_data_handler_->OnUdpPacket(*packet_buffer_ptr);

  is_scan_cycle_complete_ = IsScanCycleComplete(scan_angle);
  last_angle_ = scan_angle;

  if (is_scan_cycle_complete_ && scan_data_handler_) {
    scan_data_handler_->OnScanCycleComplete();
  }
}

void AT128PDriverImpl::OnFaultMessage(
    std::shared_ptr<std::vector<uint8_t>> packet_buffer_ptr) {
  if (packet_buffer_ptr->data()[0] != 0xCD ||
      packet_buffer_ptr->data()[1] != 0xDC) {
    LOG(ERROR) << "Incorrect data header. This is not fault message. ip: "
               << ip_address_;
    return;
  }

  auto [event_code, fault_message] = CheckFaultMessage(packet_buffer_ptr);
  if (event_code == LidarEvent::EventCode::kNormal) {
    return;
  }

  LOG(ERROR) << "Received fault message. ip: " << ip_address_
             << " event_code: " << std::bitset<32>(static_cast<int>(event_code))
             << ". message: " << fault_message;

  if (message_callback_) {
    message_callback_({event_code, LidarEvent::State::kOccur}, fault_message);
  }
  PrintFaultMessageInHex(packet_buffer_ptr);
}

pb::LidarScanSector *AT128PDriverImpl::AllocateScanSector() {
  pb::LidarScanSector *lidar_scan_sector =
      scan_data_handler_->AllocateScanSector(pb::LidarScan::DUAL);
  DCHECK(lidar_scan_sector != nullptr);
  lidar_scan_sector->mutable_positions()->Reserve(kAT128PMaxPointsNum * 3);
  lidar_scan_sector->mutable_intensities()->reserve(kAT128PMaxPointsNum);
  lidar_scan_sector->mutable_ring_numbers()->reserve(kAT128PMaxPointsNum);
  lidar_scan_sector->mutable_confidences()->reserve(kAT128PMaxPointsNum);
  return lidar_scan_sector;
}

bool AT128PDriverImpl::IsScanCycleComplete(int scan_angle) const {
  if (last_angle_ < 0) {
    LOG_EVERY_N(WARNING, 100)
        << "The last angle is invalid. last_angle_ is "
           "reset to -1. [1/100]. ip: "
        << ip_address_ << ". scan_angle: " << scan_angle << " " << last_angle_;
  }

  if (is_clockwise_rotation_direction_) {
    // When the radar is scanned clockwise, the cycle judgment boundary is
    if (scan_angle > kAT128PMaximumAngleRangeMinimumAngle * kAT128PAngleRatio)
      return true;
    return (scan_angle < last_angle_) &&
           (last_angle_ <=
            kAT128PMaximumAngleRangeMinimumAngle * kAT128PAngleRatio);
  } else {
    // When the radar is scanning counterclockwise, the periodic judgment
    // boundary is
    if (scan_angle <
        kAT128PFOVMinimumAngleRangeMaximumAngle * kAT128PAngleRatio)
      return true;
    return (scan_angle > last_angle_) &&
           (last_angle_ >=
            kAT128PFOVMinimumAngleRangeMaximumAngle * kAT128PAngleRatio);
  }
}

bool AT128PDriverImpl::IsDiscard(int scan_angle) const {
  if (is_clockwise_rotation_direction_) {
    // Clockwise (viewed from above)
    return (scan_angle >
            kAT128PMaximumAngleRangeMinimumAngle * kAT128PAngleRatio) &&
           is_scan_cycle_complete_;
  } else {
    // Counterclockwise (viewed from above)
    return (scan_angle <
            kAT128PFOVMinimumAngleRangeMaximumAngle * kAT128PAngleRatio) &&
           is_scan_cycle_complete_;
  }
}

}  // namespace lidar
