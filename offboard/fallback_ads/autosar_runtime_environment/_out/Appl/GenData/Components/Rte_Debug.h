/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Debug.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <Debug>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_DEBUG_H
# define RTE_DEBUG_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
// #   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_Debug_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"

# ifndef RTE_CORE

#  define RTE_START_SEC_CODE
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/* RTE Helper-Functions */
FUNC(void, RTE_CODE) Rte_MemCpy(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num);
FUNC(void, RTE_CODE) Rte_MemCpy32(P2VAR(void, AUTOMATIC, RTE_APPL_VAR) destination, P2CONST(void, AUTOMATIC, RTE_APPL_DATA) source, uint32_least num);

#  define RTE_STOP_SEC_CODE
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# endif /* !defined(RTE_CORE) */


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_FallbackControlDebugInfo_ControlErrorHeading (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlErrorLateral (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlErrorLongitudinal (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlErrorVelocity (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve0 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve1 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve2 (FALSE)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve3 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve4 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve5 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve6 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve7 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve8 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_ControlReserve9 (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidDistanceDComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidDistanceIComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidDistancePComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidDistanceTotal (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidHeadingDComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidHeadingIComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidHeadingPComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatPidHeadingTotal (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatSteeringFeeback (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LatSteeringFeedforward (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongAccelerationFeeback (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongAccelerationFeedforward (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidDistanceDComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidDistanceIComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidDistancePComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidDistanceTotal (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidSpeedDComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidSpeedIComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidSpeedPComponent (0)
#  define Rte_InitValue_FallbackControlDebugInfo_LongPidSpeedTotal (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugCanbusState (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugIsStationary (FALSE)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugPositionX (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugPositionXStd (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugPositionY (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugPositionYStd (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved0 (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved1 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved2 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved3 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved4 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved5 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved6 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved7 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugReserved8 (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugSideSlipAngle (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugSpeed (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugSpeedStd (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugStateType (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugVehicleState (0U)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugYaw (0)
#  define Rte_InitValue_FallbackPoseDebug_PoseDebugYawStd (0U)
# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_60ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_60ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_Debug_60ms_ControlDebug_ControlDebug() \
  (&Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_ControlDebug_ControlDebug.value)


#  define Rte_IRead_Debug_60ms_EstimationDebug_EstimationDebug() \
  (&Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_EstimationDebug_EstimationDebug.value)


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorHeading(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorHeading.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlErrorHeading() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorHeading.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorLateral(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorLateral.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlErrorLateral() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorLateral.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorLongitudinal(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorLongitudinal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlErrorLongitudinal() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorLongitudinal.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlErrorVelocity(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorVelocity.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlErrorVelocity() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlErrorVelocity.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve0(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve0.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve0() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve0.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve1(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve1.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve1() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve1.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve2(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve2.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve2() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve2.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve3(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve3.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve3() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve3.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve4(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve4.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve4() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve4.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve5(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve5.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve5() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve5.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve6(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve6.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve6() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve6.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve7(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve7.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve7() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve7.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve8(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve8.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve8() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve8.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlReserve9(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve9.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlReserve9() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlReserve9.value \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(data) \
  RteIWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(data)
#  else
#   define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(data) \
  RteIWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(*(data))
#  endif
#  define RteIWrite_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp(data) \
  ( \
    Rte_MemCpy(Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlTimeStamp.value, data, sizeof(ControlTimeStamp)) \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp() \
  (&((*RteIWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp()))[0])
#  else
#   define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp() \
  RteIWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp()
#  endif
#  define RteIWriteRef_Debug_60ms_FallbackControlDebugInfo_ControlTimeStamp() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_ControlTimeStamp.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceDComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceDComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceDComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceIComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceIComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceIComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistancePComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistancePComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidDistancePComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistancePComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceTotal(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidDistanceTotal() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidDistanceTotal.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingDComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingDComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingDComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingIComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingIComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingIComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingPComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingPComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingPComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingPComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingTotal(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatPidHeadingTotal() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatPidHeadingTotal.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeeback(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatSteeringFeeback.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeeback() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatSteeringFeeback.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeedforward(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatSteeringFeedforward.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LatSteeringFeedforward() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LatSteeringFeedforward.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeeback(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongAccelerationFeeback.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeeback() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongAccelerationFeeback.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeedforward(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongAccelerationFeedforward.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongAccelerationFeedforward() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongAccelerationFeedforward.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceDComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceDComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceDComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceIComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceIComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceIComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistancePComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistancePComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidDistancePComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistancePComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceTotal(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidDistanceTotal() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidDistanceTotal.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedDComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedDComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedDComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedIComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedIComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedIComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedPComponent(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedPComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedPComponent() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedPComponent.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedTotal(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackControlDebugInfo_LongPidSpeedTotal() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackControlDebugInfo_LongPidSpeedTotal.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugCanbusState(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugCanbusState.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugCanbusState() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugCanbusState.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugIsStationary(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugIsStationary.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugIsStationary() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugIsStationary.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionX(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionX.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugPositionX() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionX.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionXStd(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionXStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugPositionXStd() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionXStd.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionY(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionY.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugPositionY() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionY.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugPositionYStd(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionYStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugPositionYStd() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugPositionYStd.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved0(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved0.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved0() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved0.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved1(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved1.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved1() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved1.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved2(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved2.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved2() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved2.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved3(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved3.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved3() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved3.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved4(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved4.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved4() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved4.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved5(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved5.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved5() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved5.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved6(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved6.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved6() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved6.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved7(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved7.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved7() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved7.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugReserved8(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved8.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugReserved8() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugReserved8.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSideSlipAngle(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSideSlipAngle.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugSideSlipAngle() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSideSlipAngle.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSpeed(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSpeed.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugSpeed() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSpeed.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugSpeedStd(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSpeedStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugSpeedStd() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugSpeedStd.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugStateType(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugStateType.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugStateType() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugStateType.value \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  RteIWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(data)
#  else
#   define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  RteIWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(*(data))
#  endif
#  define RteIWrite_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  ( \
    Rte_MemCpy(Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugTimeStamp.value, data, sizeof(PoseDebugTimeStamp)) \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp() \
  (&((*RteIWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp()))[0])
#  else
#   define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp() \
  RteIWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp()
#  endif
#  define RteIWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugTimeStamp() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugTimeStamp.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugVehicleState(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugVehicleState.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugVehicleState() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugVehicleState.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugYaw(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugYaw.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugYaw() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugYaw.value \
  )


#  define Rte_IWrite_Debug_60ms_FallbackPoseDebug_PoseDebugYawStd(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugYawStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_60ms_FallbackPoseDebug_PoseDebugYawStd() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms.Rte_FallbackPoseDebug_PoseDebugYawStd.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlErrorHeading(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorHeading.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlErrorHeading() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorHeading.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlErrorLateral(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorLateral.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlErrorLateral() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorLateral.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlErrorLongitudinal(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorLongitudinal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlErrorLongitudinal() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorLongitudinal.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlErrorVelocity(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorVelocity.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlErrorVelocity() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlErrorVelocity.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve0(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve0.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve0() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve0.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve1(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve1.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve1() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve1.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve2(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve2.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve2() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve2.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve3(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve3.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve3() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve3.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve4(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve4.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve4() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve4.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve5(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve5.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve5() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve5.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve6(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve6.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve6() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve6.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve7(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve7.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve7() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve7.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve8(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve8.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve8() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve8.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlReserve9(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve9.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlReserve9() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlReserve9.value \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp(data) \
  RteIWrite_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp(data)
#  else
#   define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp(data) \
  RteIWrite_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp(*(data))
#  endif
#  define RteIWrite_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp(data) \
  ( \
    Rte_MemCpy(Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlTimeStamp.value, data, sizeof(ControlTimeStamp)) \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp() \
  (&((*RteIWriteRef_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp()))[0])
#  else
#   define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp() \
  RteIWriteRef_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp()
#  endif
#  define RteIWriteRef_Debug_Init_FallbackControlDebugInfo_ControlTimeStamp() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_ControlTimeStamp.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidDistanceDComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidDistanceDComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceDComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidDistanceIComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidDistanceIComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceIComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidDistancePComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistancePComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidDistancePComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistancePComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidDistanceTotal(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidDistanceTotal() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidDistanceTotal.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidHeadingDComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidHeadingDComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingDComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidHeadingIComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidHeadingIComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingIComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidHeadingPComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingPComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidHeadingPComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingPComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatPidHeadingTotal(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatPidHeadingTotal() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatPidHeadingTotal.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatSteeringFeeback(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatSteeringFeeback.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatSteeringFeeback() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatSteeringFeeback.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LatSteeringFeedforward(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatSteeringFeedforward.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LatSteeringFeedforward() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LatSteeringFeedforward.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongAccelerationFeeback(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongAccelerationFeeback.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongAccelerationFeeback() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongAccelerationFeeback.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongAccelerationFeedforward(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongAccelerationFeedforward.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongAccelerationFeedforward() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongAccelerationFeedforward.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidDistanceDComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidDistanceDComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceDComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidDistanceIComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidDistanceIComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceIComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidDistancePComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistancePComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidDistancePComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistancePComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidDistanceTotal(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidDistanceTotal() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidDistanceTotal.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidSpeedDComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedDComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidSpeedDComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedDComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidSpeedIComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedIComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidSpeedIComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedIComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidSpeedPComponent(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedPComponent.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidSpeedPComponent() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedPComponent.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackControlDebugInfo_LongPidSpeedTotal(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedTotal.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackControlDebugInfo_LongPidSpeedTotal() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackControlDebugInfo_LongPidSpeedTotal.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugCanbusState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugCanbusState.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugCanbusState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugCanbusState.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugIsStationary(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugIsStationary.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugIsStationary() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugIsStationary.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugPositionX(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionX.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugPositionX() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionX.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugPositionXStd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionXStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugPositionXStd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionXStd.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugPositionY(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionY.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugPositionY() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionY.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugPositionYStd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionYStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugPositionYStd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugPositionYStd.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved0(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved0.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved0() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved0.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved1(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved1.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved1() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved1.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved2(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved2.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved2() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved2.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved3(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved3.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved3() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved3.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved4(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved4.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved4() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved4.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved5(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved5.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved5() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved5.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved6(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved6.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved6() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved6.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved7(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved7.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved7() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved7.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugReserved8(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved8.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugReserved8() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugReserved8.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugSideSlipAngle(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSideSlipAngle.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugSideSlipAngle() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSideSlipAngle.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugSpeed(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSpeed.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugSpeed() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSpeed.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugSpeedStd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSpeedStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugSpeedStd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugSpeedStd.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugStateType(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugStateType.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugStateType() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugStateType.value \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  RteIWrite_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp(data)
#  else
#   define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  RteIWrite_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp(*(data))
#  endif
#  define RteIWrite_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp(data) \
  ( \
    Rte_MemCpy(Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugTimeStamp.value, data, sizeof(PoseDebugTimeStamp)) \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp() \
  (&((*RteIWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp()))[0])
#  else
#   define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp() \
  RteIWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp()
#  endif
#  define RteIWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugTimeStamp() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugTimeStamp.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugVehicleState(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugVehicleState.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugVehicleState() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugVehicleState.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugYaw(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugYaw.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugYaw() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugYaw.value \
  )


#  define Rte_IWrite_Debug_Init_FallbackPoseDebug_PoseDebugYawStd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugYawStd.value = (data) \
  )


#  define Rte_IWriteRef_Debug_Init_FallbackPoseDebug_PoseDebugYawStd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Debug_Debug_Init.Rte_FallbackPoseDebug_PoseDebugYawStd.value \
  )


# endif /* !defined(RTE_CORE) */


# define Debug_START_SEC_CODE
// # include "Debug_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_Debug_60ms Debug_60ms
#  define RTE_RUNNABLE_Debug_Init Debug_Init
# endif

FUNC(void, Debug_CODE) Debug_60ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, Debug_CODE) Debug_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define Debug_STOP_SEC_CODE
// # include "Debug_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_DEBUG_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
