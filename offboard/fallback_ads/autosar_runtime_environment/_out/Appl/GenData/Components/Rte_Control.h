/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Control.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <Control>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_CONTROL_H
# define RTE_CONTROL_H

# ifndef RTE_CORE
// #  ifdef RTE_APPLICATION_HEADER_FILE
// #   error Multiple application header files included.
// #  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_Control_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_Control_10ms_AcuControl_AcuControl() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_AcuControl_AcuControl.value)


#  define Rte_IRead_Control_10ms_GnssPose_GnssPose() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_GnssPose_GnssPose.value)


#  define Rte_IRead_Control_10ms_LocalizationPose_LocalizationPose() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_LocalizationPose_LocalizationPose.value)


#  define Rte_IRead_Control_10ms_Timestamp_Timestamp() \
  (Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_Timestamp_Timestamp.value)


#  define Rte_IRead_Control_10ms_Trajectory_Trajectory() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_Trajectory_Trajectory.value)


#  define Rte_IRead_Control_10ms_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value)


#  define Rte_IWrite_Control_10ms_ControlCommand_ControlCommand(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_Control_ControlCommand_ControlCommand.value = *(data) \
  )


#  define Rte_IWriteRef_Control_10ms_ControlCommand_ControlCommand() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_Control_ControlCommand_ControlCommand.value \
  )


#  define Rte_IWrite_Control_10ms_ControlDebug_ControlDebug(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_ControlDebug_ControlDebug.value = *(data) \
  )


#  define Rte_IWriteRef_Control_10ms_ControlDebug_ControlDebug() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_ControlDebug_ControlDebug.value \
  )


#  define Rte_IWrite_Control_10ms_EstimationDebug_EstimationDebug(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_EstimationDebug_EstimationDebug.value = *(data) \
  )


#  define Rte_IWriteRef_Control_10ms_EstimationDebug_EstimationDebug() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_RB.Rte_Control_Control_10ms.Rte_EstimationDebug_EstimationDebug.value \
  )


#  define Rte_IWrite_Control_Init_ControlCommand_ControlCommand(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_ControlCommand_ControlCommand.value = *(data) \
  )


#  define Rte_IWriteRef_Control_Init_ControlCommand_ControlCommand() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_ControlCommand_ControlCommand.value \
  )


#  define Rte_IWrite_Control_Init_ControlDebug_ControlDebug(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_ControlDebug_ControlDebug.value = *(data) \
  )


#  define Rte_IWriteRef_Control_Init_ControlDebug_ControlDebug() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_ControlDebug_ControlDebug.value \
  )


#  define Rte_IWrite_Control_Init_EstimationDebug_EstimationDebug(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_EstimationDebug_EstimationDebug.value = *(data) \
  )


#  define Rte_IWriteRef_Control_Init_EstimationDebug_EstimationDebug() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_Control_Control_Init.Rte_EstimationDebug_EstimationDebug.value \
  )


# endif /* !defined(RTE_CORE) */


# define Control_START_SEC_CODE
// # include "Control_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_Control_10ms Control_10ms
#  define RTE_RUNNABLE_Control_Init Control_Init
# endif

FUNC(void, Control_CODE) Control_10ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, Control_CODE) Control_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define Control_STOP_SEC_CODE
// # include "Control_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_CONTROL_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
