#
# Autogenerated by Thrift Compiler (0.11.0)
#
# DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
#
#  options string: py
#
# pylint: disable=all
# flake8: noqa

from thrift.Thrift import TType, TMessageType, TF<PERSON>zenDict, TException, TApplicationException
from thrift.protocol.TProtocol import TProtocolException
from thrift.TRecursive import fix_spec

import sys

from thrift.transport import TTransport
all_structs = []


class Error(object):
    LOCSVR_COUNTRY_ID_NOT_FOUND = -1
    LOCSVR_RET_OK = 0
    CLIENT_INVALID_COORD = 1
    CLIENT_INVALID_ARGUMENT = 2
    SERVER_ERROR = 3
    LOCSVR_RET_COORD_SIZE_EXCEED_LIMIT = 4
    LOCSVR_RET_COORD_SIZE_ZERO = 5
    LOCSVR_RET_INVALID_COORD_TYPE = 6
    LOCSVR_RET_UID_SIZE_EXCEED_LIMIT = 7
    LOCSVR_RET_UID_SIZE_ZERO = 8
    LOCSVR_RET_INVALID_TOKEN = 9
    LOCSVR_RET_EXCEED_RATE_LIMIT = 10
    LOCSVR_RET_IP_INVALID = 11

    _VALUES_TO_NAMES = {
        -1: "LOCSVR_COUNTRY_ID_NOT_FOUND",
        0: "LOCSVR_RET_OK",
        1: "CLIENT_INVALID_COORD",
        2: "CLIENT_INVALID_ARGUMENT",
        3: "SERVER_ERROR",
        4: "LOCSVR_RET_COORD_SIZE_EXCEED_LIMIT",
        5: "LOCSVR_RET_COORD_SIZE_ZERO",
        6: "LOCSVR_RET_INVALID_COORD_TYPE",
        7: "LOCSVR_RET_UID_SIZE_EXCEED_LIMIT",
        8: "LOCSVR_RET_UID_SIZE_ZERO",
        9: "LOCSVR_RET_INVALID_TOKEN",
        10: "LOCSVR_RET_EXCEED_RATE_LIMIT",
        11: "LOCSVR_RET_IP_INVALID",
    }

    _NAMES_TO_VALUES = {
        "LOCSVR_COUNTRY_ID_NOT_FOUND": -1,
        "LOCSVR_RET_OK": 0,
        "CLIENT_INVALID_COORD": 1,
        "CLIENT_INVALID_ARGUMENT": 2,
        "SERVER_ERROR": 3,
        "LOCSVR_RET_COORD_SIZE_EXCEED_LIMIT": 4,
        "LOCSVR_RET_COORD_SIZE_ZERO": 5,
        "LOCSVR_RET_INVALID_COORD_TYPE": 6,
        "LOCSVR_RET_UID_SIZE_EXCEED_LIMIT": 7,
        "LOCSVR_RET_UID_SIZE_ZERO": 8,
        "LOCSVR_RET_INVALID_TOKEN": 9,
        "LOCSVR_RET_EXCEED_RATE_LIMIT": 10,
        "LOCSVR_RET_IP_INVALID": 11,
    }


class CoordType(object):
    BAIDU = 1
    SOSOGCJ = 2
    WGS84 = 3
    NONE = 4

    _VALUES_TO_NAMES = {
        1: "BAIDU",
        2: "SOSOGCJ",
        3: "WGS84",
        4: "NONE",
    }

    _NAMES_TO_VALUES = {
        "BAIDU": 1,
        "SOSOGCJ": 2,
        "WGS84": 3,
        "NONE": 4,
    }


class ExtraFieldsSpec(object):
    EXTRA_FIELDS_GPS_INFO = 0
    EXTRA_FIELDS_RT_STATUS = 1
    EXTRA_FIELDS_LISTEN_STATUS = 2
    EXTRA_FIELDS_ANGLE = 3
    EXTRA_FIELDS_GRID_ID = 4
    EXTRA_FIELDS_UTC_OFFSET = 5
    EXTRA_FIELDS_CHINA_APP_OFFSET = 6
    EXTRA_FIELDS_CHINA_RT_OFFSET = 7
    EXTRA_LOCATION_UTC_OFFSET = 8
    EXTRA_OS_UTC_OFFSET = 9

    _VALUES_TO_NAMES = {
        0: "EXTRA_FIELDS_GPS_INFO",
        1: "EXTRA_FIELDS_RT_STATUS",
        2: "EXTRA_FIELDS_LISTEN_STATUS",
        3: "EXTRA_FIELDS_ANGLE",
        4: "EXTRA_FIELDS_GRID_ID",
        5: "EXTRA_FIELDS_UTC_OFFSET",
        6: "EXTRA_FIELDS_CHINA_APP_OFFSET",
        7: "EXTRA_FIELDS_CHINA_RT_OFFSET",
        8: "EXTRA_LOCATION_UTC_OFFSET",
        9: "EXTRA_OS_UTC_OFFSET",
    }

    _NAMES_TO_VALUES = {
        "EXTRA_FIELDS_GPS_INFO": 0,
        "EXTRA_FIELDS_RT_STATUS": 1,
        "EXTRA_FIELDS_LISTEN_STATUS": 2,
        "EXTRA_FIELDS_ANGLE": 3,
        "EXTRA_FIELDS_GRID_ID": 4,
        "EXTRA_FIELDS_UTC_OFFSET": 5,
        "EXTRA_FIELDS_CHINA_APP_OFFSET": 6,
        "EXTRA_FIELDS_CHINA_RT_OFFSET": 7,
        "EXTRA_LOCATION_UTC_OFFSET": 8,
        "EXTRA_OS_UTC_OFFSET": 9,
    }


class LANGUAGE_TYPE(object):
    LNG_LOCAL = 0
    LNG_SIMP_CHINESE = 1
    LNG_US_ENGLISH = 2
    LNG_TRAD_CHINESE = 3

    _VALUES_TO_NAMES = {
        0: "LNG_LOCAL",
        1: "LNG_SIMP_CHINESE",
        2: "LNG_US_ENGLISH",
        3: "LNG_TRAD_CHINESE",
    }

    _NAMES_TO_VALUES = {
        "LNG_LOCAL": 0,
        "LNG_SIMP_CHINESE": 1,
        "LNG_US_ENGLISH": 2,
        "LNG_TRAD_CHINESE": 3,
    }


class Area_RET_TYPE(object):
    AREA_ADMIN_SIMPLE = 1
    AREA_ADMIN_DETAIL = 2

    _VALUES_TO_NAMES = {
        1: "AREA_ADMIN_SIMPLE",
        2: "AREA_ADMIN_DETAIL",
    }

    _NAMES_TO_VALUES = {
        "AREA_ADMIN_SIMPLE": 1,
        "AREA_ADMIN_DETAIL": 2,
    }


class TimeZone(object):
    """
    Attributes:
     - utc_offset
     - china_app_offset
     - china_rt_offset
     - location_utc_offset
     - os_utc_offset
     - name
    """


    def __init__(self, utc_offset=None, china_app_offset=None, china_rt_offset=None, location_utc_offset=None, os_utc_offset=None, name=None,):
        self.utc_offset = utc_offset
        self.china_app_offset = china_app_offset
        self.china_rt_offset = china_rt_offset
        self.location_utc_offset = location_utc_offset
        self.os_utc_offset = os_utc_offset
        self.name = name

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.utc_offset = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.china_app_offset = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.china_rt_offset = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I32:
                    self.location_utc_offset = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I32:
                    self.os_utc_offset = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.name = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('TimeZone')
        if self.utc_offset is not None:
            oprot.writeFieldBegin('utc_offset', TType.I32, 1)
            oprot.writeI32(self.utc_offset)
            oprot.writeFieldEnd()
        if self.china_app_offset is not None:
            oprot.writeFieldBegin('china_app_offset', TType.I32, 2)
            oprot.writeI32(self.china_app_offset)
            oprot.writeFieldEnd()
        if self.china_rt_offset is not None:
            oprot.writeFieldBegin('china_rt_offset', TType.I32, 3)
            oprot.writeI32(self.china_rt_offset)
            oprot.writeFieldEnd()
        if self.location_utc_offset is not None:
            oprot.writeFieldBegin('location_utc_offset', TType.I32, 4)
            oprot.writeI32(self.location_utc_offset)
            oprot.writeFieldEnd()
        if self.os_utc_offset is not None:
            oprot.writeFieldBegin('os_utc_offset', TType.I32, 5)
            oprot.writeI32(self.os_utc_offset)
            oprot.writeFieldEnd()
        if self.name is not None:
            oprot.writeFieldBegin('name', TType.STRING, 6)
            oprot.writeString(self.name.encode('utf-8') if sys.version_info[0] == 2 else self.name)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class Gpsinfo(object):
    """
    Attributes:
     - accuracy
     - direction
     - speed
     - angle
    """


    def __init__(self, accuracy=None, direction=None, speed=None, angle=None,):
        self.accuracy = accuracy
        self.direction = direction
        self.speed = speed
        self.angle = angle

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.DOUBLE:
                    self.accuracy = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.DOUBLE:
                    self.direction = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.DOUBLE:
                    self.speed = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.DOUBLE:
                    self.angle = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Gpsinfo')
        if self.accuracy is not None:
            oprot.writeFieldBegin('accuracy', TType.DOUBLE, 1)
            oprot.writeDouble(self.accuracy)
            oprot.writeFieldEnd()
        if self.direction is not None:
            oprot.writeFieldBegin('direction', TType.DOUBLE, 2)
            oprot.writeDouble(self.direction)
            oprot.writeFieldEnd()
        if self.speed is not None:
            oprot.writeFieldBegin('speed', TType.DOUBLE, 3)
            oprot.writeDouble(self.speed)
            oprot.writeFieldEnd()
        if self.angle is not None:
            oprot.writeFieldBegin('angle', TType.DOUBLE, 4)
            oprot.writeDouble(self.angle)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        if self.accuracy is None:
            raise TProtocolException(message='Required field accuracy is unset!')
        if self.direction is None:
            raise TProtocolException(message='Required field direction is unset!')
        if self.speed is None:
            raise TProtocolException(message='Required field speed is unset!')
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class Coordinate(object):
    """
    Attributes:
     - lng
     - lat
     - org_coord_type
     - timestamp
     - gps_info
     - rt_status
     - listen_status
     - angle
     - grid_id
     - time_zone
    """


    def __init__(self, lng=None, lat=None, org_coord_type=None, timestamp=None, gps_info=None, rt_status=None, listen_status=None, angle=None, grid_id=None, time_zone=None,):
        self.lng = lng
        self.lat = lat
        self.org_coord_type = org_coord_type
        self.timestamp = timestamp
        self.gps_info = gps_info
        self.rt_status = rt_status
        self.listen_status = listen_status
        self.angle = angle
        self.grid_id = grid_id
        self.time_zone = time_zone

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.DOUBLE:
                    self.lng = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.DOUBLE:
                    self.lat = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.org_coord_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I64:
                    self.timestamp = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.gps_info = Gpsinfo()
                    self.gps_info.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.I32:
                    self.rt_status = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I32:
                    self.listen_status = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.DOUBLE:
                    self.angle = iprot.readDouble()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRING:
                    self.grid_id = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRUCT:
                    self.time_zone = TimeZone()
                    self.time_zone.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Coordinate')
        if self.lng is not None:
            oprot.writeFieldBegin('lng', TType.DOUBLE, 1)
            oprot.writeDouble(self.lng)
            oprot.writeFieldEnd()
        if self.lat is not None:
            oprot.writeFieldBegin('lat', TType.DOUBLE, 2)
            oprot.writeDouble(self.lat)
            oprot.writeFieldEnd()
        if self.org_coord_type is not None:
            oprot.writeFieldBegin('org_coord_type', TType.I32, 3)
            oprot.writeI32(self.org_coord_type)
            oprot.writeFieldEnd()
        if self.timestamp is not None:
            oprot.writeFieldBegin('timestamp', TType.I64, 4)
            oprot.writeI64(self.timestamp)
            oprot.writeFieldEnd()
        if self.gps_info is not None:
            oprot.writeFieldBegin('gps_info', TType.STRUCT, 5)
            self.gps_info.write(oprot)
            oprot.writeFieldEnd()
        if self.rt_status is not None:
            oprot.writeFieldBegin('rt_status', TType.I32, 6)
            oprot.writeI32(self.rt_status)
            oprot.writeFieldEnd()
        if self.listen_status is not None:
            oprot.writeFieldBegin('listen_status', TType.I32, 7)
            oprot.writeI32(self.listen_status)
            oprot.writeFieldEnd()
        if self.angle is not None:
            oprot.writeFieldBegin('angle', TType.DOUBLE, 8)
            oprot.writeDouble(self.angle)
            oprot.writeFieldEnd()
        if self.grid_id is not None:
            oprot.writeFieldBegin('grid_id', TType.STRING, 9)
            oprot.writeString(self.grid_id.encode('utf-8') if sys.version_info[0] == 2 else self.grid_id)
            oprot.writeFieldEnd()
        if self.time_zone is not None:
            oprot.writeFieldBegin('time_zone', TType.STRUCT, 10)
            self.time_zone.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        if self.lng is None:
            raise TProtocolException(message='Required field lng is unset!')
        if self.lat is None:
            raise TProtocolException(message='Required field lat is unset!')
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class Cityinfo(object):
    """
    Attributes:
     - cityid
     - city_desc
     - countyid
     - county_desc
     - countryid
     - country_desc
     - provinceid
     - province_desc
     - country_code
     - district_code
     - time_zone
     - canonical_country_code
    """


    def __init__(self, cityid=None, city_desc=None, countyid=None, county_desc=None, countryid=None, country_desc=None, provinceid=None, province_desc=None, country_code=None, district_code=None, time_zone=None, canonical_country_code=None,):
        self.cityid = cityid
        self.city_desc = city_desc
        self.countyid = countyid
        self.county_desc = county_desc
        self.countryid = countryid
        self.country_desc = country_desc
        self.provinceid = provinceid
        self.province_desc = province_desc
        self.country_code = country_code
        self.district_code = district_code
        self.time_zone = time_zone
        self.canonical_country_code = canonical_country_code

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.cityid = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.city_desc = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.I32:
                    self.countyid = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.STRING:
                    self.county_desc = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.I32:
                    self.countryid = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRING:
                    self.country_desc = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 7:
                if ftype == TType.I32:
                    self.provinceid = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 8:
                if ftype == TType.STRING:
                    self.province_desc = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 9:
                if ftype == TType.STRING:
                    self.country_code = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 10:
                if ftype == TType.STRING:
                    self.district_code = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 11:
                if ftype == TType.STRUCT:
                    self.time_zone = TimeZone()
                    self.time_zone.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 12:
                if ftype == TType.STRING:
                    self.canonical_country_code = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Cityinfo')
        if self.cityid is not None:
            oprot.writeFieldBegin('cityid', TType.I32, 1)
            oprot.writeI32(self.cityid)
            oprot.writeFieldEnd()
        if self.city_desc is not None:
            oprot.writeFieldBegin('city_desc', TType.STRING, 2)
            oprot.writeString(self.city_desc.encode('utf-8') if sys.version_info[0] == 2 else self.city_desc)
            oprot.writeFieldEnd()
        if self.countyid is not None:
            oprot.writeFieldBegin('countyid', TType.I32, 3)
            oprot.writeI32(self.countyid)
            oprot.writeFieldEnd()
        if self.county_desc is not None:
            oprot.writeFieldBegin('county_desc', TType.STRING, 4)
            oprot.writeString(self.county_desc.encode('utf-8') if sys.version_info[0] == 2 else self.county_desc)
            oprot.writeFieldEnd()
        if self.countryid is not None:
            oprot.writeFieldBegin('countryid', TType.I32, 5)
            oprot.writeI32(self.countryid)
            oprot.writeFieldEnd()
        if self.country_desc is not None:
            oprot.writeFieldBegin('country_desc', TType.STRING, 6)
            oprot.writeString(self.country_desc.encode('utf-8') if sys.version_info[0] == 2 else self.country_desc)
            oprot.writeFieldEnd()
        if self.provinceid is not None:
            oprot.writeFieldBegin('provinceid', TType.I32, 7)
            oprot.writeI32(self.provinceid)
            oprot.writeFieldEnd()
        if self.province_desc is not None:
            oprot.writeFieldBegin('province_desc', TType.STRING, 8)
            oprot.writeString(self.province_desc.encode('utf-8') if sys.version_info[0] == 2 else self.province_desc)
            oprot.writeFieldEnd()
        if self.country_code is not None:
            oprot.writeFieldBegin('country_code', TType.STRING, 9)
            oprot.writeString(self.country_code.encode('utf-8') if sys.version_info[0] == 2 else self.country_code)
            oprot.writeFieldEnd()
        if self.district_code is not None:
            oprot.writeFieldBegin('district_code', TType.STRING, 10)
            oprot.writeString(self.district_code.encode('utf-8') if sys.version_info[0] == 2 else self.district_code)
            oprot.writeFieldEnd()
        if self.time_zone is not None:
            oprot.writeFieldBegin('time_zone', TType.STRUCT, 11)
            self.time_zone.write(oprot)
            oprot.writeFieldEnd()
        if self.canonical_country_code is not None:
            oprot.writeFieldBegin('canonical_country_code', TType.STRING, 12)
            oprot.writeString(self.canonical_country_code.encode('utf-8') if sys.version_info[0] == 2 else self.canonical_country_code)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        if self.cityid is None:
            raise TProtocolException(message='Required field cityid is unset!')
        if self.city_desc is None:
            raise TProtocolException(message='Required field city_desc is unset!')
        if self.countyid is None:
            raise TProtocolException(message='Required field countyid is unset!')
        if self.county_desc is None:
            raise TProtocolException(message='Required field county_desc is unset!')
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class Filter(object):
    """
    Attributes:
     - extra_fields_spec
     - timeout
    """


    def __init__(self, extra_fields_spec=None, timeout=None,):
        self.extra_fields_spec = extra_fields_spec
        self.timeout = timeout

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.extra_fields_spec = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.timeout = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('Filter')
        if self.extra_fields_spec is not None:
            oprot.writeFieldBegin('extra_fields_spec', TType.I64, 1)
            oprot.writeI64(self.extra_fields_spec)
            oprot.writeFieldEnd()
        if self.timeout is not None:
            oprot.writeFieldBegin('timeout', TType.I32, 2)
            oprot.writeI32(self.timeout)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class AreaFilter(object):
    """
    Attributes:
     - area_type
     - lan_type
    """


    def __init__(self, area_type=None, lan_type=None,):
        self.area_type = area_type
        self.lan_type = lan_type

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.area_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.I32:
                    self.lan_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('AreaFilter')
        if self.area_type is not None:
            oprot.writeFieldBegin('area_type', TType.I32, 1)
            oprot.writeI32(self.area_type)
            oprot.writeFieldEnd()
        if self.lan_type is not None:
            oprot.writeFieldBegin('lan_type', TType.I32, 2)
            oprot.writeI32(self.lan_type)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class MultiCoordRequest(object):
    """
    Attributes:
     - log_id
     - token
     - coords
     - req_coord_type
     - area_filter
     - coord_filter
    """


    def __init__(self, log_id=None, token=None, coords=None, req_coord_type=None, area_filter=None, coord_filter=None,):
        self.log_id = log_id
        self.token = token
        self.coords = coords
        self.req_coord_type = req_coord_type
        self.area_filter = area_filter
        self.coord_filter = coord_filter

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I64:
                    self.log_id = iprot.readI64()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.token = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.LIST:
                    self.coords = []
                    (_etype3, _size0) = iprot.readListBegin()
                    for _i4 in range(_size0):
                        _elem5 = Coordinate()
                        _elem5.read(iprot)
                        self.coords.append(_elem5)
                    iprot.readListEnd()
                else:
                    iprot.skip(ftype)
            elif fid == 4:
                if ftype == TType.I32:
                    self.req_coord_type = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 5:
                if ftype == TType.STRUCT:
                    self.area_filter = AreaFilter()
                    self.area_filter.read(iprot)
                else:
                    iprot.skip(ftype)
            elif fid == 6:
                if ftype == TType.STRUCT:
                    self.coord_filter = Filter()
                    self.coord_filter.read(iprot)
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('MultiCoordRequest')
        if self.log_id is not None:
            oprot.writeFieldBegin('log_id', TType.I64, 1)
            oprot.writeI64(self.log_id)
            oprot.writeFieldEnd()
        if self.token is not None:
            oprot.writeFieldBegin('token', TType.STRING, 2)
            oprot.writeString(self.token.encode('utf-8') if sys.version_info[0] == 2 else self.token)
            oprot.writeFieldEnd()
        if self.coords is not None:
            oprot.writeFieldBegin('coords', TType.LIST, 3)
            oprot.writeListBegin(TType.STRUCT, len(self.coords))
            for iter6 in self.coords:
                iter6.write(oprot)
            oprot.writeListEnd()
            oprot.writeFieldEnd()
        if self.req_coord_type is not None:
            oprot.writeFieldBegin('req_coord_type', TType.I32, 4)
            oprot.writeI32(self.req_coord_type)
            oprot.writeFieldEnd()
        if self.area_filter is not None:
            oprot.writeFieldBegin('area_filter', TType.STRUCT, 5)
            self.area_filter.write(oprot)
            oprot.writeFieldEnd()
        if self.coord_filter is not None:
            oprot.writeFieldBegin('coord_filter', TType.STRUCT, 6)
            self.coord_filter.write(oprot)
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        if self.log_id is None:
            raise TProtocolException(message='Required field log_id is unset!')
        if self.token is None:
            raise TProtocolException(message='Required field token is unset!')
        if self.coords is None:
            raise TProtocolException(message='Required field coords is unset!')
        if self.req_coord_type is None:
            raise TProtocolException(message='Required field req_coord_type is unset!')
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)


class MultiAreaInfoResponse(object):
    """
    Attributes:
     - ret_code
     - ret_msg
     - city_infos
    """


    def __init__(self, ret_code=None, ret_msg=None, city_infos=None,):
        self.ret_code = ret_code
        self.ret_msg = ret_msg
        self.city_infos = city_infos

    def read(self, iprot):
        if iprot._fast_decode is not None and isinstance(iprot.trans, TTransport.CReadableTransport) and self.thrift_spec is not None:
            iprot._fast_decode(self, iprot, [self.__class__, self.thrift_spec])
            return
        iprot.readStructBegin()
        while True:
            (fname, ftype, fid) = iprot.readFieldBegin()
            if ftype == TType.STOP:
                break
            if fid == 1:
                if ftype == TType.I32:
                    self.ret_code = iprot.readI32()
                else:
                    iprot.skip(ftype)
            elif fid == 2:
                if ftype == TType.STRING:
                    self.ret_msg = iprot.readString().decode('utf-8') if sys.version_info[0] == 2 else iprot.readString()
                else:
                    iprot.skip(ftype)
            elif fid == 3:
                if ftype == TType.MAP:
                    self.city_infos = {}
                    (_ktype8, _vtype9, _size7) = iprot.readMapBegin()
                    for _i11 in range(_size7):
                        _key12 = iprot.readI64()
                        _val13 = Cityinfo()
                        _val13.read(iprot)
                        self.city_infos[_key12] = _val13
                    iprot.readMapEnd()
                else:
                    iprot.skip(ftype)
            else:
                iprot.skip(ftype)
            iprot.readFieldEnd()
        iprot.readStructEnd()

    def write(self, oprot):
        if oprot._fast_encode is not None and self.thrift_spec is not None:
            oprot.trans.write(oprot._fast_encode(self, [self.__class__, self.thrift_spec]))
            return
        oprot.writeStructBegin('MultiAreaInfoResponse')
        if self.ret_code is not None:
            oprot.writeFieldBegin('ret_code', TType.I32, 1)
            oprot.writeI32(self.ret_code)
            oprot.writeFieldEnd()
        if self.ret_msg is not None:
            oprot.writeFieldBegin('ret_msg', TType.STRING, 2)
            oprot.writeString(self.ret_msg.encode('utf-8') if sys.version_info[0] == 2 else self.ret_msg)
            oprot.writeFieldEnd()
        if self.city_infos is not None:
            oprot.writeFieldBegin('city_infos', TType.MAP, 3)
            oprot.writeMapBegin(TType.I64, TType.STRUCT, len(self.city_infos))
            for kiter14, viter15 in self.city_infos.items():
                oprot.writeI64(kiter14)
                viter15.write(oprot)
            oprot.writeMapEnd()
            oprot.writeFieldEnd()
        oprot.writeFieldStop()
        oprot.writeStructEnd()

    def validate(self):
        if self.ret_code is None:
            raise TProtocolException(message='Required field ret_code is unset!')
        if self.ret_msg is None:
            raise TProtocolException(message='Required field ret_msg is unset!')
        if self.city_infos is None:
            raise TProtocolException(message='Required field city_infos is unset!')
        return

    def __repr__(self):
        L = ['%s=%r' % (key, value)
             for key, value in self.__dict__.items()]
        return '%s(%s)' % (self.__class__.__name__, ', '.join(L))

    def __eq__(self, other):
        return isinstance(other, self.__class__) and self.__dict__ == other.__dict__

    def __ne__(self, other):
        return not (self == other)
all_structs.append(TimeZone)
TimeZone.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'utc_offset', None, None, ),  # 1
    (2, TType.I32, 'china_app_offset', None, None, ),  # 2
    (3, TType.I32, 'china_rt_offset', None, None, ),  # 3
    (4, TType.I32, 'location_utc_offset', None, None, ),  # 4
    (5, TType.I32, 'os_utc_offset', None, None, ),  # 5
    (6, TType.STRING, 'name', 'UTF8', None, ),  # 6
)
all_structs.append(Gpsinfo)
Gpsinfo.thrift_spec = (
    None,  # 0
    (1, TType.DOUBLE, 'accuracy', None, None, ),  # 1
    (2, TType.DOUBLE, 'direction', None, None, ),  # 2
    (3, TType.DOUBLE, 'speed', None, None, ),  # 3
    (4, TType.DOUBLE, 'angle', None, None, ),  # 4
)
all_structs.append(Coordinate)
Coordinate.thrift_spec = (
    None,  # 0
    (1, TType.DOUBLE, 'lng', None, None, ),  # 1
    (2, TType.DOUBLE, 'lat', None, None, ),  # 2
    (3, TType.I32, 'org_coord_type', None, None, ),  # 3
    (4, TType.I64, 'timestamp', None, None, ),  # 4
    (5, TType.STRUCT, 'gps_info', [Gpsinfo, None], None, ),  # 5
    (6, TType.I32, 'rt_status', None, None, ),  # 6
    (7, TType.I32, 'listen_status', None, None, ),  # 7
    (8, TType.DOUBLE, 'angle', None, None, ),  # 8
    (9, TType.STRING, 'grid_id', 'UTF8', None, ),  # 9
    (10, TType.STRUCT, 'time_zone', [TimeZone, None], None, ),  # 10
)
all_structs.append(Cityinfo)
Cityinfo.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'cityid', None, None, ),  # 1
    (2, TType.STRING, 'city_desc', 'UTF8', None, ),  # 2
    (3, TType.I32, 'countyid', None, None, ),  # 3
    (4, TType.STRING, 'county_desc', 'UTF8', None, ),  # 4
    (5, TType.I32, 'countryid', None, None, ),  # 5
    (6, TType.STRING, 'country_desc', 'UTF8', None, ),  # 6
    (7, TType.I32, 'provinceid', None, None, ),  # 7
    (8, TType.STRING, 'province_desc', 'UTF8', None, ),  # 8
    (9, TType.STRING, 'country_code', 'UTF8', None, ),  # 9
    (10, TType.STRING, 'district_code', 'UTF8', None, ),  # 10
    (11, TType.STRUCT, 'time_zone', [TimeZone, None], None, ),  # 11
    (12, TType.STRING, 'canonical_country_code', 'UTF8', None, ),  # 12
)
all_structs.append(Filter)
Filter.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'extra_fields_spec', None, None, ),  # 1
    (2, TType.I32, 'timeout', None, None, ),  # 2
)
all_structs.append(AreaFilter)
AreaFilter.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'area_type', None, None, ),  # 1
    (2, TType.I32, 'lan_type', None, None, ),  # 2
)
all_structs.append(MultiCoordRequest)
MultiCoordRequest.thrift_spec = (
    None,  # 0
    (1, TType.I64, 'log_id', None, None, ),  # 1
    (2, TType.STRING, 'token', 'UTF8', None, ),  # 2
    (3, TType.LIST, 'coords', (TType.STRUCT, [Coordinate, None], False), None, ),  # 3
    (4, TType.I32, 'req_coord_type', None, None, ),  # 4
    (5, TType.STRUCT, 'area_filter', [AreaFilter, None], None, ),  # 5
    (6, TType.STRUCT, 'coord_filter', [Filter, None], None, ),  # 6
)
all_structs.append(MultiAreaInfoResponse)
MultiAreaInfoResponse.thrift_spec = (
    None,  # 0
    (1, TType.I32, 'ret_code', None, None, ),  # 1
    (2, TType.STRING, 'ret_msg', 'UTF8', None, ),  # 2
    (3, TType.MAP, 'city_infos', (TType.I64, None, TType.STRUCT, [Cityinfo, None], False), None, ),  # 3
)
fix_spec(all_structs)
del all_structs
