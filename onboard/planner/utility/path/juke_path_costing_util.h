#ifndef ONBOARD_PLANNER_UTILITY_PATH_JUKE_PATH_COSTING_UTIL_H_
#define ONBOARD_PLANNER_UTILITY_PATH_JUKE_PATH_COSTING_UTIL_H_

#include <algorithm>
#include <utility>
#include <vector>

#include "control_util/costing_base.h"
#include "math/math_util.h"
#include "planner/path/path_solver/nominal_path_smoother/semantic_corridor.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/utility/path/path_kinematics_util.h"
#include "planner/utility/primitive/primitive_definition/decoupled_path_primitive.h"
#include "vehicle_model/utils.h"
#include "voy_protos/control_util.pb.h"

namespace control_util {
// Trait for JukeIntegratedPathFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kJukeIntegratedPath> {
  using StateControlSpecs = vehicle_model::JukeIntegratedModel;

  using PbState = vehicle_model::pb::JukeIntegratedState;
  using PbControl = vehicle_model::pb::JukeIntegratedControl;

  using PbFeatureType = control_util::pb::PathFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = control_util::pb::PathFeatureParameter;
  using PbCostingTerm = control_util::pb::PathCostingTerm;
  struct StepwisePrimitives {
    vehicle_model::VehicleRepresentation ego_representation;
  };

  using PbSolution = planner::pb::JukeIntegratedPathSolution;
  using PbWarmstarterDebug = planner::pb::JukeIntegratedPathWarmstarterDebug;
  using PbSolverDebug = planner::pb::JukeIntegratedPathSolverDebug;
  using PbSolverInterationDebug =
      planner::pb::JukeIntegratedPathSolverIteration;
  using PbWarmstartType = planner::pb::PathWarmstartType;

  using Primitives = planner::primitive::DecoupledPathPrimitives;

  static constexpr int FEATURE_DIM = pb::PathFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace planner {
namespace path {
using JukeIntegratedPathFormula =
    control_util::Formula<control_util::DdpProblemType::kJukeIntegratedPath>;

using State = planner::path::JukeIntegratedPathFormula::State;
using Control = planner::path::JukeIntegratedPathFormula::Control;
using CostingUtil =
    planner::path::JukeIntegratedPathFormula::FormulaCostingUtil;
using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;

// The type of clearance. kPositive (kNegative) means that ego should maintain a
// positive (negative) clearance from a given geometry.
enum class ClearanceType {
  kPositive = 0,
  kNegative,
};

// TODO(huanhui): Remove this function since it's not been used anymore.
// Gets reference speed square value for a given point.
double GetReferenceSpeedSquare(
    const std::vector<planner::primitive::SpeedReferenceRegionPrimitive>&
        speed_regions,
    const State& x, double ego_speed_squared,
    math::geometry::PolylineCurve2dProximityCache* cached_poly);

// Computes curvature constraint.
bool ConstraintKappa(const State& x,
                     const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                     Feature* feature);

//  Computes augmented lagrangian curvature constraint.
bool ALConstraintKappa(const State& x,
                       const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                       Feature* feature);

// Computes curvature rate constraint.
bool ConstraintEta(const State& x,
                   const vehicle_model::pb::JukeIntegratedModelLimit& limit,
                   Feature* feature);

// Computes curvature 2nd order derivative constraint.
bool ConstraintGamma(const Control& u, double max_abs_gamma, Feature* feature);

// Computes approximate lateral accel violation.
bool ConstraintApproximateLateralAccel(
    const State& x, double speed_reference, double max_abs_lateral_accel_mpss,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature);

// Computes approximate lateral jerk violation.
bool ConstraintApproximateLateralJerk(
    const State& x, double speed_reference, double max_abs_lateral_jerk_mpss,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature);

// Since we need to adjust the terminal heading in reverse driving/pull over, we
// add a terminal heading cost.
void ConstraintTerminalHeading(
    const State& x, double target_heading, double heading_tolerance, int step,
    int total_step_size, double weight_adjust,
    math::Range<int> target_index_range,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature);

// Computes nominal path attraction primitive.
void PrimitiveSoftPathAttraction(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<planner::primitive::WeightAdjustedRegionPrimitive>&
        weight_adjusted_regions,
    int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction, Feature* feature);

// Computes reference heading attraction cost.
void ComputeReferenceHeadingAttraction(
    const State& x, double heading_tolerance, int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction, Feature* feature);

// Computes reference curvature attraction cost.
void ComputeReferenceCurvatureAttraction(
    const planner::path::JukeIntegratedPathFormula::State& x,
    double curvature_tolerance, int step, int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const adv_geom::PathCurve2d& attraction_path_curve,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    bool should_enhance_attraction,
    JukeIntegratedPathFormula::FormulaCostingUtil::Feature* feature);

// Computes curvature effort.
void EffortKappa(const State& x, Feature* feature);

// Computes curvature rate(pinch) effort. i.e., derivative of curvature w.r.t
// delta-s.
void EffortEta(const State& x, Feature* feature);

// Computes curvature 2nd order derivative(juke) effort. i.e., 2nd order
// derivative of curvature w.r.t delta-s.
void EffortGamma(const Control& u, Feature* feature);

// Computes cost amount out of steering rate effort/norm relevant.
void EffortApproximateSteeringRate(
    const State& x, double wheel_base, double speed_reference, int step,
    int total_step_size,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature);

void EffortApproximateSteeringAccel(
    const Control& u, double wheel_base, double speed_reference, int step,
    int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    Feature* feature);

// Computes approximate lateral acceleration effort.
bool EffortApproximateLateralAccel(
    const State& x, double speed_reference, int step, int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature);

// Computes approximate lateral jerk effort.
bool EffortApproximateLateralJerk(
    const State& x, double speed_reference, int step, int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature);

// Returns corresponding steering rate that control can accept based on
// experienced model, see Yanbo's doc:
// https://docs.google.com/document/d/1scZTpWVnGGWL12SwhiPmxcoej3lAe0eCk_ONNZxVNHA/edit
math::Range1d ComputeSteeringRateLimit(double wheel_base,
                                       double ego_speed_squared,
                                       double max_abs_steering_rate);

// Adding hard cost to guarantee approximate steering rate limit based on ego
// speed.
void ConstraintApproximateSteeringRate(
    const State& x, double ego_speed_squared, double speed_reference,
    double wheel_base,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature);

// Computes path attraction primitive with tolerance.
bool PathAttractionWithTolerance(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const double cons_min, const double cons_max,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    int step, int total_step_size, double weight_adjust,
    const std::optional<double>& additional_param,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    JukeIntegratedPathFormula::FormulaCostingUtil::FeatureLet* component);

// Computes corresponding violation cost of steering accel that control can
// accept based on experienced model.
void ConstraintApproximateSteeringAccel(const Control& u,
                                        double speed_reference,
                                        double wheel_base,
                                        double max_abs_steering_accel,
                                        Feature* feature);

// Computes corresponding augmented lagrangian violation cost of steering accel
// that control can accept based on experienced model.
void ALConstraintApproximateSteeringAccel(const Control& u,
                                          double speed_reference,
                                          double wheel_base,
                                          double max_abs_steering_accel,
                                          Feature* feature);

// Computes attraction cost.
void ComputeAttractionSegmentCost(
    const math::geometry::Point2d& front_bumper_pos, double theta,
    const path_problem::Attractions& attractions,
    const primitive::PathManeuverPrimitive& maneuver_primitive,
    const JukeIntegratedPathFormula::PbWeightAdjustSpec& weight_adjust_spec,
    const vehicle_model::pb::JukeIntegratedMotionModelParam& param,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    int step, int total_step_size, Feature* feature);
}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_UTILITY_PATH_JUKE_PATH_COSTING_UTIL_H_
