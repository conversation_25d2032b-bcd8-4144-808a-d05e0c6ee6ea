load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "selection_test_fixture",
    srcs = ["selection_test_fixture.cpp"],
    hdrs = ["selection_test_fixture.h"],
    include_prefix = "planner/selection/test",
    deps = [
        "//onboard/planner/behavior/util/agent_state:agent_in_lane_state",
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/selection:trajectory_meta",
        "//onboard/planner/selection/scenario_selectors:progress_efficiency_util",
        "//onboard/planner/speed/reasoning/test:reasoning_test_fixture",
        "//onboard/planner/speed/test_util:speed_test_util",
        "//onboard/planner/trajectory/util",
    ],
)

voy_cc_test(
    name = "progress_efficiency_util_test",
    srcs = [
        "progress_efficiency_util_test.cpp",
    ],
    deps = [
        ":selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "selection_test",
    srcs = [
        "selection_test.cpp",
    ],
    deps = [
        ":selection_test_fixture",
        "//onboard/planner/selection",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "risk_test",
    srcs = [
        "risk_test.cpp",
    ],
    deps = [
        "//onboard/planner/selection/risk:agent_reaction_fusion",
        "//onboard/planner/selection/risk:risk_reasoner",
        "//onboard/planner/selection/risk:risk_solver",
        "//onboard/planner/speed/constraint:constraint_creator",
        "//onboard/pnc_map_service:test_util",
    ],
)

voy_cc_test(
    name = "risk_severity_test",
    srcs = [
        "risk_severity_test.cpp",
    ],
    deps = [
        "//onboard/common/collision:collision_severity_evaluator",
        "//onboard/planner:voy_planner_gflags",
        "//onboard/planner/selection/risk:risk_solver",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gflags",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "selection_proto_test",
    srcs = [
        "selection_proto_test.cpp",
    ],
    deps = [
        ":selection_test_fixture",
        "//onboard/planner/selection:selection_utils",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "selection_utils_test",
    srcs = [
        "selection_utils_test.cpp",
    ],
    deps = [
        "//onboard/planner/selection:selection_utils",
        "//onboard/planner/speed/reasoning/test:reasoning_test_fixture",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "lane_selection_scenario_selector_test",
    srcs = [
        "lane_selection_scenario_selector_test.cpp",
    ],
    deps = [
        ":selection_test_fixture",
        "//onboard/planner/selection",
        "//onboard/planner/selection/scenario_selectors:lane_selection_scenario_selector",
        "@voy-sdk//:gtest",
    ],
)
