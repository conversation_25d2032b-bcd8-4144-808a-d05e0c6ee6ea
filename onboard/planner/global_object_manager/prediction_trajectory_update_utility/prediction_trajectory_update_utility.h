#ifndef ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_PREDICTION_TRAJECTORY_UPDATE_UTILITY_PREDICTION_TRAJECTORY_UPDATE_UTILITY_H_
#define ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_PREDICTION_TRAJECTORY_UPDATE_UTILITY_PREDICTION_TRAJECTORY_UPDATE_UTILITY_H_

#include <unordered_set>

#include "av_comm/thread_pool.h"
#include "planner/global_object_manager/common_types.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "prediction_protos/agent.pb.h"

namespace planner {
namespace utility {
void UpdatePredictionTrajectoryMap(
    int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
    const std::unordered_set<TypedObjectId>& irrelevant_object_ids,
    TbbPredictionTrajectoryMap* object_prediction_map,
    TbbConditionalPredictionTrajectoryMap* object_conditional_prediction_map,
    av_comm::ThreadPool* destruction_background_pool);
}  // namespace utility
}  // namespace planner
#endif  // ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_PREDICTION_TRAJECTORY_UPDATE_UTILITY_PREDICTION_TRAJECTORY_UPDATE_UTILITY_H_
