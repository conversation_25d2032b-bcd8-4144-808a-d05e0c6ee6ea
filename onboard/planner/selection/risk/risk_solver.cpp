#include "planner/selection/risk/risk_solver.h"

#include <algorithm>
#include <tbb/parallel_for.h>
#include <tbb/tbb.h>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/container/flat_hash_map.h"
#include "common/collision/collision_position.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/selection/risk/agent_reaction_fusion.h"
#include "planner/selection/risk/risk_utils.h"
#include "planner/utility/common/position_estimate_error.h"
#include "planner/utility/common/risk_model.h"

namespace planner {
namespace selection {
namespace {

using math::geometry::Box2dCornerType;
using math::geometry::Box2dEdgeType;

// The value should be slightly larger than human driver reaction time but not
// too large to cause over-reaction, a value in [2., 3.] is about right.
constexpr double kRequiredSafeYieldReactTimeInRegularSpeed = 2.25;  // s
// The value is adjusted 1s down from regular speed react time and still kept
// above 1s.
constexpr double kRequiredSafeYieldReactTimeInLowSpeed = 1.25;  // s
// Low and regular speed thresholds used in required safe yield time
// computation.
constexpr double kSafeYieldReactLowSpeedInMSec = 2.0;
constexpr double kSafeYieldReactRegularSpeedInMSec = 4.0;
// The weight ensures a maximum risk (2.5) recalled if a strict collision is
// detected at 1.8s (risk >= prob(collision) * weight@1.8 * 0.33 = 0.5 * 15.4 *
// 0.33 = 2.54.).
constexpr double kUnknownObjectRiskDiscount = 0.33;

// Max collision risk value we allow for better-not-drive object.
constexpr double kMaxBetterNotDriveObjectRisk = 1.3;
// Three levels of weight discount for better-not-drive(BND) object, which is
// defined as those objects that we can run over through vehicle chassis.
// Weight discount if ego path doesn't intersect with better-not-drive(BND)
// object.
constexpr double kBndRiskDiscountNonOverlap = 0.1;
// Weight discount if ego under-chassis-over better-not-drive(BND) object
// without hitting tires.
constexpr double kBndRiskDiscountUnderChassis = 0.2;
// Weight discount if ego tire-over better-not-drive(BND) object.
constexpr double kBndRiskDiscountTireOver = 0.5;
// Tire dimension of Gen3 vehicle: 275/45/R20.
constexpr double kEgoTireWidthInMeter = 0.275;

// Risk weight for special 999 prediction.
constexpr double kSpecialBpRiskWeight = 0.3;
constexpr double kMinCheckHorizonForSpeedIgnoreBpInSec = 2.5;
constexpr double kMaxCheckHorizonForSpeedIgnoreBpInSec = 4.5;

constexpr int kPerturbCycleForLcLead = 15;
constexpr int kMaxPoseIxToCheckPerturbFullCycleForLcLead = 30;
constexpr int kMinPoseIxToIgnorePerturbForLcLead = 60;
constexpr int kPerturbCycleForLcTail = 22;
constexpr int kMaxPoseIxToCheckPerturbFullCycleForLcTail = 30;
constexpr int kMinPoseIxToIgnorePerturbForLcTail = 60;
constexpr double kMinLateralSearchRangeInM = 6.0;        // m
constexpr double kMinLongitudinalSearchRangeInM = 12.0;  // m
// The ratio of hard brake level to speed under stress condition. It's intended
// to be a simple effective approximation of worst scenario.
constexpr double kAdversarialBrakeToSpeedRatio = 0.25;  // 1/s
constexpr double kAdversarialBrakeLowerBoundInMpss = -3.5;
constexpr double kAdversarialBrakeUpperBoundInMpss = -1.5;
constexpr double kAdversarialBrakeMaxSpeedInMps = 11.11;  // 40kph
constexpr double kLongSafetyDistBufferToLeadAgentInMeter = 3.0;
constexpr double kLongSafetyTimeBufferToLeadAgentInSec = 0.7;
constexpr double kLeadAgentInteractionCheckDurationInSec = 1.0;

// The speed threshold below which we deem as near stationary.
constexpr double kSpeedCriteriaForNearStaticAgent = 1.0;  // m/s
// Severity hyperparameters for generalized UKF policy
// Severity = pow(kGenericSeverityFactor, SeverityLevel)
constexpr double kGenericSeverityFactor = 1.5;
// Distance over uncertainty ratio that's deemed as significant enough to check
// time-to-collision in horizon before. As a prior, we think 3 is required to
// avoid FP and make it stricter to 2.6 based on empirical issues. The lower the
// ration is required, the higher precision is.
constexpr double kSignifDistOverSdRatioThresForTtcEstimate = 2.6;

constexpr double kLongitudinalSdToSpeedRatioUpperBound = 0.5;
constexpr double kGenericLongitudinalSdUpperBound = 1.0;
constexpr double kBackwardLongitudinalSdUpperBound = 0.55;
constexpr double kPosewiseRiskPrintThreshold = 0.005;
constexpr double kPosewiseDistancePrintThreshold = 1.25;  // m
constexpr double kMaxRatioOfDerivativeRiskToProbRisk = 0.25;
constexpr double kMaxDerivativeRiskAtLowProbRisk = 0.5;

struct InitialPoseRiskInfo {
  PositionErrorInfo ego_localization_error{};
  PositionErrorInfo agent_tracking_error{};
  bool has_been_set = false;
  std::string debug{};
};

// This struct contains env info (additional to input in generic risk API) for
// considering extra uncertainty for LC behavior, e.g. whether to apply and
// strength of application of speiclize perturbation of lead/tail agent.
struct LaneChangeExtraUncertaintyContext {};

struct LaneChangePoseRisk {
  double ttc_in_sec = std::numeric_limits<double>::infinity();
  double risk = 0.0;
};

// Collision risk info of a planned trajectory against one agent prediction.
// TODO(jieruan): consolidate it with RiskConstraintEvalResult.
struct BpRiskInfo {
  double risk = 0.0;
  // Risk cost resulted from collision probability.
  double probability_risk = 0.0;
  // Risk cost resulted from derivative of collision probability, i.e. potential
  // collision probability increase in adverse event.
  double derivative_risk = 0.0;
  double collision_prob = 0.0;
  double static_risk = 0.0;
  double lc_risk = 0.0;
  double min_dist_over_sd = std::numeric_limits<double>::max();
  // Ego-agent distance where minimum distance-to-uncertainty ratio is attained.
  double dist_at_min_ratio = 0.0;
  // Agent uncertainty where minimum distance-to-uncertainty ratio is attained.
  double sd_at_min_ratio = std::numeric_limits<double>::infinity();
  // Spatial relation signal of the Better-Not-Drive (BND) object's contour
  // relative to the ego vehicle's frame bounding box edge.
  BndRelativeLocation bnd_pos_to_ego_frame_signal =
      BndRelativeLocation::UNKNOWN;

  // Time to collide in sec.
  double ttc = std::numeric_limits<double>::max();
  // Prob(TTC < t) for t in [0s, 8s] based on raw prediction.
  math::PiecewiseLinearFunction ttc_cdf;
  // Updated severity level.
  collision::CollisionSeverity collision_severity;
  // If there is no collison, collision_position.type is
  // UNKNOWN_COLLISION_POSITION
  collision::CollisionPosition collision_position;
  // Time-to-collision against lane change lead/tail agent by specilized
  // longitudinal perturbation for them.
  double lc_specialized_ttc_in_sec = std::numeric_limits<double>::infinity();
  std::string debug_info = {};
};

bool IsAgentMovable(voy::perception::ObjectType agent_type) {
  return agent_type == voy::perception::ObjectType::VEHICLE ||
         agent_type == voy::perception::ObjectType::PED ||
         agent_type == voy::perception::ObjectType::CYCLIST ||
         agent_type == voy::perception::ObjectType::TRICYCLE;
}

const PrincipledRequiredLateralGap* MaybeGetStaticRequiredLateralGap(
    const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
        required_lat_gap_map,
    const PredictedTrajectoryWrapper& agent_bp) {
  if (!IsAgentMovable(agent_bp.tracked_object().object_type())) return nullptr;

  // Check if we should restrict to vehicle first.
  const bool near_static =
      std::all_of(agent_bp.begin(), agent_bp.end(),
                  [=](const planner::pb::TrajectoryPose& pose) {
                    return pose.speed() < kSpeedCriteriaForNearStaticAgent;
                  });
  const auto iter = required_lat_gap_map.find(agent_bp.object_id());
  // Disable for fixing simulation failures in scenarios 2928327, 2928087.
  // TODO(jieruan): Investigate why agent can be IsStationary() yet not
  // near_static?
  // DCHECK(near_static || !agent_bp.IsStationary());
  DCHECK(iter != required_lat_gap_map.end());
  return near_static ? &(iter->second) : nullptr;
}

bool IsLcLeadAgent(const ObjectId& agent_id,
                   const speed::pb::SpeedSeed& speed_seed) {
  return !speed_seed.lc_guide_seed().empty() &&
         speed_seed.lc_guide_seed(0).lead_obj_id() == agent_id;
}

bool IsLcTailAgent(const ObjectId& agent_id,
                   const speed::pb::SpeedSeed& speed_seed) {
  return !speed_seed.lc_guide_seed().empty() &&
         speed_seed.lc_guide_seed(0).tail_obj_id() == agent_id;
}

std::pair<const PredictedTrajectoryWrapper*, const PredictedTrajectoryWrapper*>
GetLcLeadTailAgentBp(const speed::pb::SpeedSeed& speed_seed,
                     const PredictedTrajectoryWrapper& agent_bp) {
  const PredictedTrajectoryWrapper* lead_agent_bp =
      IsLcLeadAgent(agent_bp.object_id(), speed_seed) ? &agent_bp : nullptr;
  const PredictedTrajectoryWrapper* tail_agent_bp =
      IsLcTailAgent(agent_bp.object_id(), speed_seed) ? &agent_bp : nullptr;
  return {lead_agent_bp, tail_agent_bp};
}

PositionErrorInfo GetControlErrorInfo(
    double ego_heading, double relative_time_in_sec_from_plan_init) {
  return {ego_heading,
          position_estimate_error::GetControlLateralErrorSd(
              relative_time_in_sec_from_plan_init),
          position_estimate_error::GetControlLongitudinalErrorSd(
              relative_time_in_sec_from_plan_init)};
}

PositionErrorInfo GetLocalizationErrorInfo(double ego_heading,
                                           double ego_speed) {
  return {
      ego_heading,
      position_estimate_error::GetLocalizationLateralErrorSd(ego_speed),
      position_estimate_error::GetLocalizationLongitudinalErrorSd(ego_speed)};
}

PositionErrorInfo GetTrackingErrorInfo(const TrafficParticipantPose& agent_pose,
                                       const TrafficParticipantPose& ego_pose) {
  const double lateral_error_sd =
      position_estimate_error::GetTrackingLateralErrorSd(agent_pose, ego_pose);
  return {agent_pose.heading(), lateral_error_sd,
          lateral_error_sd *
              position_estimate_error::kLongErrorSdToLatErrorSdRatio};
}

// Estimate time-to-collision under stress condition of lead agent braking hard.
// Specifically it checks time to first collision from the input pose, assuming
// lead agent starts hard brake from there and ego maintain planned trajectory.
std::optional<double> ComputeTtcToLcLeadWithLongitudinalPerturbation(
    int agent_pose_ix, const PredictedTrajectoryWrapper& agent_bp,
    const RiskEvalTarget& candidate, std::string& debug) {
  const voy::TrackedObject& agent_object = agent_bp.tracked_object();
  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>&
      agent_pb_poses = agent_bp.predicted_trajectory_proto().traj_poses();
  DCHECK_LT(agent_pose_ix, agent_pb_poses.size());
  const int perturb_cycle = static_cast<int>(std::floor(
      math::GetLinearInterpolatedY(kMaxPoseIxToCheckPerturbFullCycleForLcLead,
                                   kMinPoseIxToIgnorePerturbForLcLead,
                                   kPerturbCycleForLcLead, 0, agent_pose_ix)));
  if (perturb_cycle <= 0) {
    return std::nullopt;
  }

  const planner::pb::TrajectoryPose& agent_pb_pose =
      agent_pb_poses.at(agent_pose_ix);
  const double agent_speed = std::max(agent_pb_pose.speed(), 0.0);
  const double adversary_brake =
      std::max(-agent_speed * kAdversarialBrakeToSpeedRatio,
               math::GetLinearInterpolatedY(0.0, kAdversarialBrakeMaxSpeedInMps,
                                            kAdversarialBrakeLowerBoundInMpss,
                                            kAdversarialBrakeUpperBoundInMpss,
                                            agent_speed));
  const int end_pose_ix =
      std::min(perturb_cycle + agent_pose_ix, agent_pb_poses.size());
  std::vector<int> perturbed_agent_pose_indices;
  perturbed_agent_pose_indices.reserve(end_pose_ix - agent_pose_ix);
  // TODO(jieruan): change to use an std::ostringstream for efficiency.
  debug += absl::StrFormat(
      "agent_pose_ix = %i, perturbed_agent_pose_indices: \n", agent_pose_ix);
  for (int i = agent_pose_ix, perturbed_ix = i; i < end_pose_ix; ++i) {
    const double dt = (i - agent_pose_ix) * constants::kTrajectoryIntervalInSec;
    const double perturbed_l = agent_pb_pose.odom() + agent_speed * dt +
                               0.5 * adversary_brake * dt * dt;
    while (perturbed_ix + 1 < end_pose_ix &&
           perturbed_l >= agent_pb_poses.at(perturbed_ix + 1).odom()) {
      ++perturbed_ix;
    }
    perturbed_agent_pose_indices.push_back(perturbed_ix);
    debug += absl::StrFormat("%i,", perturbed_ix);
  }
  debug += "\n";
  for (int i = agent_pose_ix; i < end_pose_ix; ++i) {
    const int64_t pose_ts = agent_pb_poses.at(i).timestamp();
    const std::optional<planner::pb::TrajectoryPose>& ego_pb_pose =
        candidate.geo_centered_traj->GetInterpolatedPose2d(pose_ts);
    if (!ego_pb_pose.has_value()) continue;

    DCHECK_LT(i - agent_pose_ix, perturbed_agent_pose_indices.size());
    const planner::pb::TrajectoryPose& perturbed_agent_pose =
        agent_pb_poses.at(perturbed_agent_pose_indices[i - agent_pose_ix]);
    const math::geometry::OrientedBox2d ego_bbox =
        GetPoseBBox(TrafficParticipantPose(candidate.ego_shape, *ego_pb_pose));
    const math::geometry::OrientedBox2d agent_bbox =
        GetPoseBBox(TrafficParticipantPose(agent_object, perturbed_agent_pose));
    if (math::geometry::Intersects(ego_bbox, agent_bbox)) {
      return (i - agent_pose_ix) * constants::kTrajectoryIntervalInSec;
    }
  }
  return std::nullopt;
}

// Estimate time-to-collision under stress condition of ego braking hard.
// Specifically it checks time to first collision from the input pose, assuming
// ego starts hard brake from there and tail agent maintain prediction.
std::optional<double> ComputeTtcToLcTailWithLongitudinalPerturbation(
    const planner::pb::TrajectoryPose& ego_pose,
    const RiskEvalTarget& candidate, int agent_pose_ix,
    const PredictedTrajectoryWrapper& agent_bp, std::string& debug) {
  std::optional<planner::pb::TrajectoryPose> opt_perturbed_next_ego_pose =
      candidate.geo_centered_traj->GetInterpolatedPose2d(
          ego_pose.timestamp() + constants::kPlanningCycleTimeInMSec);
  if (!opt_perturbed_next_ego_pose.has_value()) return std::nullopt;

  const CrossLaneInfoManager& cross_lane_info_manager =
      candidate.trajectory_info->cross_lane_info_manager();
  const CrossLaneInfo& cross_lane_info =
      cross_lane_info_manager.GetCrossLaneInfo();
  // TODO(pengfei): Maybe also consider the lead agent on the current lane.
  if (!cross_lane_info.gap_info.is_valid() ||
      !cross_lane_info.gap_info.has_lead_obj_id()) {
    return std::nullopt;
  }

  const CrossLaneObjectInfo* lead_object_info =
      DCHECK_NOTNULL(cross_lane_info_manager.GetTargetRegionObjectInfoListPtr())
          ->Find(cross_lane_info.gap_info.lead_obj_id());
  if (lead_object_info == nullptr || lead_object_info->is_leaving_region) {
    return std::nullopt;
  }

  planner::pb::TrajectoryPose perturbed_curr_ego_pose = ego_pose;
  planner::pb::TrajectoryPose perturbed_next_ego_pose =
      *opt_perturbed_next_ego_pose;
  // Here ego is assumed to do a const-accel motion and the lead agent is
  // assumed to move with const-speed. We calculate the potential brake ego
  // will need to reach the same speed as the lead agent and also to keep the
  // distance and time buffer to the lead agent for safety in the near future
  // (1s).
  const double ego_speed = std::max(ego_pose.speed(), 0.0);
  const double lead_agent_speed = lead_object_info->planner_object().speed();
  const double lead_agent_dist =
      lead_object_info->object_ego_dist +
      lead_agent_speed * agent_pose_ix * constants::kTrajectoryIntervalInSec +
      lead_agent_speed * kLeadAgentInteractionCheckDurationInSec -
      lead_object_info->planner_object().length() * 0.5 - ego_pose.odom() -
      candidate.ego_shape.length() * 0.5 -
      kLongSafetyDistBufferToLeadAgentInMeter -
      kLongSafetyTimeBufferToLeadAgentInSec * ego_speed;
  // When ego can't maintain the safety buffer to the lead agent at the end of
  // the interaction check duration, we assume the potential brake to be
  // infinite and will use the default adversary brake in the following.
  const double ego_potential_brake =
      lead_agent_dist < math::constants::kEpsilon
          ? -std::numeric_limits<double>::infinity()
          : std::min(0.5 *
                         (lead_agent_speed * lead_agent_speed -
                          ego_speed * ego_speed) /
                         std::max(lead_agent_dist, math::constants::kEpsilon),
                     0.0);
  const double adversary_brake =
      std::max(-ego_speed * kAdversarialBrakeToSpeedRatio, ego_potential_brake);
  const voy::TrackedObject& agent_object = agent_bp.tracked_object();
  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>&
      agent_pb_poses = agent_bp.predicted_trajectory_proto().traj_poses();
  DCHECK_LT(agent_pose_ix, agent_pb_poses.size());
  const int perturb_cycle = static_cast<int>(std::floor(
      math::GetLinearInterpolatedY(kMaxPoseIxToCheckPerturbFullCycleForLcTail,
                                   kMinPoseIxToIgnorePerturbForLcTail,
                                   kPerturbCycleForLcTail, 0, agent_pose_ix)));
  if (perturb_cycle <= 0 || adversary_brake > -math::constants::kEpsilon) {
    return std::nullopt;
  }

  const int end_pose_ix =
      std::min(perturb_cycle + agent_pose_ix, agent_pb_poses.size());
  for (int i = agent_pose_ix; i < end_pose_ix; ++i) {
    const double dt = (i - agent_pose_ix) * constants::kTrajectoryIntervalInSec;
    const double perturbed_ego_l =
        ego_pose.odom() + ego_speed * dt + 0.5 * adversary_brake * dt * dt;
    while (perturbed_next_ego_pose.odom() < perturbed_ego_l) {
      const int64_t next_ts = perturbed_next_ego_pose.timestamp() +
                              constants::kPlanningCycleTimeInMSec;
      const std::optional<planner::pb::TrajectoryPose>& new_next_pose =
          candidate.geo_centered_traj->GetInterpolatedPose2d(next_ts);
      if (!new_next_pose.has_value()) {
        debug += absl::StrFormat(
            "no new next pose for agent_ts = %i, next_ts = %i\n",
            agent_pb_poses.at(i).timestamp(), next_ts);
        return std::nullopt;
      }

      perturbed_curr_ego_pose = perturbed_next_ego_pose;
      perturbed_next_ego_pose = new_next_pose.value();
    }
    const math::geometry::OrientedBox2d ego_bbox = GetPoseBBox(
        TrafficParticipantPose(candidate.ego_shape, perturbed_curr_ego_pose));
    const math::geometry::OrientedBox2d agent_bbox =
        GetPoseBBox(TrafficParticipantPose(agent_object, agent_pb_poses.at(i)));
    if (math::geometry::Intersects(ego_bbox, agent_bbox)) {
      return (i - agent_pose_ix) * constants::kTrajectoryIntervalInSec;
    }
  }
  return std::nullopt;
}

double ConvertTtcToRisk(double ttc) {
  constexpr int kLen = 9;
  constexpr std::array<double, kLen> inter_ttcs{0,   1.0, 2.0, 3.0, 4.0,
                                                5.0, 6.0, 7.0, 8.0};
  constexpr std::array<double, kLen> inter_risks{1.0,  0.95, 0.85, 0.7, 0.5,
                                                 0.35, 0.2,  0.1,  0.05};
  int right_ix = 1;
  while (right_ix + 1 < kLen && ttc > inter_ttcs[right_ix]) {
    ++right_ix;
  }
  DCHECK(right_ix > 0 && right_ix < kLen);
  const double risk = math::GetLinearInterpolatedY(
      inter_ttcs[right_ix - 1], inter_ttcs[right_ix], inter_risks[right_ix - 1],
      inter_risks[right_ix], ttc);
  DCHECK_GE(risk, 0.0);
  return risk;
}

LaneChangePoseRisk ComputePoseRiskForLaneChange(
    const PredictedTrajectoryWrapper* lead_agent_bp,
    const PredictedTrajectoryWrapper* tail_agent_bp,
    double time_after_plan_init_in_sec, int agent_pose_ix,
    const planner::pb::TrajectoryPose& ego_pose,
    const RiskEvalTarget& candidate, std::string& debug_info) {
  std::optional<double> ttc = std::nullopt;
  if (lead_agent_bp != nullptr) {
    ttc = ComputeTtcToLcLeadWithLongitudinalPerturbation(
        agent_pose_ix, *lead_agent_bp, candidate, debug_info);
  } else if (
      tail_agent_bp != nullptr &&
      !FLAGS_planning_disable_selection_specialized_risk_against_lc_tail) {
    ttc = ComputeTtcToLcTailWithLongitudinalPerturbation(
        ego_pose, candidate, agent_pose_ix, *tail_agent_bp, debug_info);
  }
  if (ttc.has_value()) {
    debug_info += absl::StrFormat("lc_ttc = %.2f.\n", ttc.value());
    return {
        .ttc_in_sec = time_after_plan_init_in_sec + ttc.value(),
        .risk = ConvertTtcToRisk(time_after_plan_init_in_sec + ttc.value())};
  }
  return {};
}

double ComputePoseRiskForStaticAgent(
    const PrincipledRequiredLateralGap* required_lat_gap,
    double time_after_plan_init_in_sec,
    const TrafficParticipantPose& agent_pose,
    const planner::pb::TrajectoryPose& ego_pose,
    const vehicle_model::pb::AxleRectangularMeasurement& ego_shape,
    std::string* debug_info) {
  if (required_lat_gap == nullptr) return 0.0;

  const double floor_ego_speed = std::max(ego_pose.speed(), 0.0);
  // TODO(selection): use curvature of traj at agent's arc when merging risk.
  const double critical_gap = required_lat_gap->GetCriticalRequiredLateralGap(
      floor_ego_speed, ego_pose.curvature());
  const double comofort_gap = required_lat_gap->GetComfortRequiredLateralGap(
      floor_ego_speed, ego_pose.curvature());
  DCHECK_LE(agent_pose.speed(), kSpeedCriteriaForNearStaticAgent);
  // The smoother is to make static risk transition continuously to risk based
  // on pred sd and prevent flicker when agent starts to move or vice versa.
  const double smoother = math::GetLinearInterpolatedY(
      0.0, kSpeedCriteriaForNearStaticAgent, 1.0, 0.0, agent_pose.speed());
  const double uncertainty_discount =
      ConvertTtcToRisk(time_after_plan_init_in_sec);
  const math::geometry::OrientedBox2d ego_bbox(
      {ego_pose.x_pos(), ego_pose.y_pos()}, ego_shape.length(),
      ego_shape.width(), ego_pose.heading());
  const double distance =
      math::geometry::Distance(agent_pose.contour(), ego_bbox);
  const double static_risk = math::GetLinearInterpolatedY(
      critical_gap, math::LinearInterpolate(critical_gap, comofort_gap, 0.6),
      1.25, 0.0, distance);
  if (debug_info) {
    *debug_info += absl::StrFormat(
        "crit_gap = %.2f, comf_gap = %.2f, dist = %.2f, raw_risk = %.2f, "
        "smoother = %.2f, time_dsc = %.2f, final_risk = %.2f\n",
        critical_gap, comofort_gap, distance, static_risk, smoother,
        uncertainty_discount, static_risk * smoother * uncertainty_discount);
  }
  return static_risk * smoother * uncertainty_discount;
}

std::string GetCollisionDirectionName(PotentialCollisionDirection direction) {
  switch (direction) {
    case PotentialCollisionDirection::kLateral: {
      return "Lateral";
    }
    case PotentialCollisionDirection::kLongitude: {
      return "Longitude";
    }
    case PotentialCollisionDirection::kAlongMinDist: {
      return "AlongMinDist";
    }
    case PotentialCollisionDirection::kUnknown: {
      return "Unknown";
    }
    default: {
      LOG(FATAL) << "Invalid PotentialCollisionDirection";
    }
  }
}

std::string FormatDistanceInfo(
    const PotentialCollisionDistanceInfo& distance_info) {
  return absl::StrFormat(
      "{direction = %s, dist = %.2f, object_to_ego_angle=%.2f, sd = %.2f, "
      "ratio = %.2f}",
      GetCollisionDirectionName(distance_info.Direction()),
      distance_info.Dist(), distance_info.ObjectToEgoAngle(),
      distance_info.Sd(), distance_info.DistOverSd());
}

std::string FindAndFormatDistanceInfo(
    const std::vector<PotentialCollisionDistanceInfo>&
        collision_distance_info_vector,
    PotentialCollisionDirection direction) {
  const auto distance_info_iter = std::find_if(
      collision_distance_info_vector.begin(),
      collision_distance_info_vector.end(),
      [direction](const PotentialCollisionDistanceInfo& distance_info) {
        return distance_info.Direction() == direction;
      });
  if (distance_info_iter == collision_distance_info_vector.end()) {
    return "Not used";
  }
  return FormatDistanceInfo(*distance_info_iter);
}

std::string GetPoseRiskDebugString(
    double time_after_plan_init_in_sec,
    const std::vector<PotentialCollisionDistanceInfo>&
        collision_distance_info_vector,
    double added_collision_prob, double added_collision_risk,
    double derivative_risk, double dist_at_min_ratio, double floored_sd,
    const PositionErrorInfo& ego_localization_error,
    const PositionErrorInfo& ego_control_error,
    const PositionErrorInfo& agent_tracking_error,
    const PositionErrorInfo& agent_prediction_error) {
  const std::string lat_dist_info = FindAndFormatDistanceInfo(
      collision_distance_info_vector, PotentialCollisionDirection::kLateral);
  const std::string long_dist_info = FindAndFormatDistanceInfo(
      collision_distance_info_vector, PotentialCollisionDirection::kLongitude);
  const std::string cor_dist_info =
      FindAndFormatDistanceInfo(collision_distance_info_vector,
                                PotentialCollisionDirection::kAlongMinDist);

  return absl::StrFormat(
      "ts = %.2f, lat_dist_info = %s, long_dist_info = %s, cor_dist_info = %s\n"
      "added_prob = %.5f, added_risk = %.5f, deriv_risk = %.5f, ratio = %.2f "
      "[dist = %.2f, sd = %.2f]\n"
      "local_lat = %.2f, local_lon = %.2f, ctrl_lat = %.2f, ctrl_lon = %.2f\n"
      "track_lat = %.3f, track_lon = %.2f, pred_lat = %.2f, pred_lon = %.2f\n",
      time_after_plan_init_in_sec, lat_dist_info, long_dist_info, cor_dist_info,
      added_collision_prob, added_collision_risk, derivative_risk,
      dist_at_min_ratio / floored_sd, dist_at_min_ratio, floored_sd,
      ego_localization_error.lateral_error_sd(),
      ego_localization_error.longitudinal_error_sd(),
      ego_control_error.lateral_error_sd(),
      ego_control_error.longitudinal_error_sd(),
      agent_tracking_error.lateral_error_sd(),
      agent_tracking_error.longitudinal_error_sd(),
      agent_prediction_error.lateral_error_sd(),
      agent_prediction_error.longitudinal_error_sd());
}

double GetRiskConstraintWeight(
    const WorstCasePredictionRiskConstraint& worst_case_bp_constraint,
    const RiskEvalTarget& candidate) {
  constexpr double kMaxWorstCaseBpRiskWeight = 0.3;
  const TrafficParticipantPose ego_init_pose(candidate.ego_shape,
                                             candidate.trajectory.poses(0));
  const double ego_see_agent_angle = std::abs(math::Radian2Degree(
      AngleOfAgentSee(worst_case_bp_constraint.object().pose_at_plan_init_ts(),
                      ego_init_pose)));
  return kMaxWorstCaseBpRiskWeight *
         math::GetLinearInterpolatedY(kWorstCaseBpRiskEgoMildAttentionFov,
                                      kWorstCaseBpRiskEgoFullAttentionFov, 0.0,
                                      1.0, ego_see_agent_angle);
}

double GetCappedSdAtMinRatio(
    double long_sd_bound, const PositionErrorInfo& total_distance_error,
    const PotentialCollisionDistanceInfo& most_likely_collision_distance_info) {
  constexpr double kDerivRiskMaxLateralSd = 0.5;
  constexpr double kInverseSqrt2 = 0.70710;

  const double raw_sd = std::sqrt(total_distance_error.GetProjectedErrorVar(
      most_likely_collision_distance_info.ObjectToEgoAngle()));
  const PotentialCollisionDirection direction =
      most_likely_collision_distance_info.Direction();
  if (direction == PotentialCollisionDirection::kLateral) {
    return std::min(raw_sd, kDerivRiskMaxLateralSd);
  } else if (direction == PotentialCollisionDirection::kLongitude) {
    return std::min(raw_sd, long_sd_bound);
  } else if (direction == PotentialCollisionDirection::kAlongMinDist) {
    return std::min(raw_sd,
                    kInverseSqrt2 * (kDerivRiskMaxLateralSd + long_sd_bound));
  }
  return raw_sd;
}

double GetPosewiseDerivativeRisk(double time_after_plan_init_in_sec,
                                 double dist_at_min_ratio,
                                 double sd_at_min_ratio) {
  constexpr double kCheckHorizonInSec = 4.0;
  // kGamma = delta_distance / sqrt(2 * pi), kBeta = delta_sd / sqrt(2 * pi),
  // delta_distance = 0.025m, delta_sd = 0.025m, 1/sqrt(2 * pi) = 0.3989.
  constexpr double kGamma = 0.025 * 0.3989;
  constexpr double kBeta = 0.025 * 0.3989;
  if (time_after_plan_init_in_sec > kCheckHorizonInSec) return 0.0;

  const double floored_sd =
      std::max(sd_at_min_ratio, math::constants::kEpsilon);
  const double floored_ratio =
      std::max(dist_at_min_ratio / floored_sd,
               // Critical point of derivative risk w.r.t. distance, to ensure
               // added derivative risk monotonic on distance.
               2 / (kGamma / kBeta + std::sqrt(math::Sqr(kGamma / kBeta) + 4)));
  const double derivative_risk = std::exp(-0.5 * math::Sqr(floored_ratio)) *
                                 (kGamma + kBeta * floored_ratio) / floored_sd;
  const double time_weight = GetWeightOnTtc(time_after_plan_init_in_sec);
  const double next_time_weight = GetWeightOnTtc(
      time_after_plan_init_in_sec + constants::kTrajectoryIntervalInSec);
  return (time_weight - next_time_weight) * derivative_risk;
}

}  // namespace

planner::pb::TrajectoryPose GetMostLikelyCollisionAgentPose(
    const PoseRiskInfo& pose_risk_info_at_ttc) {
  const PotentialCollisionDistanceInfo& distance_info =
      pose_risk_info_at_ttc.collision_distance_info;
  DCHECK(!std::isinf(distance_info.Dist()));
  planner::pb::TrajectoryPose collision_agent_pose =
      pose_risk_info_at_ttc.agent_pose;
  if (math::NearZero(distance_info.Dist())) {
    return collision_agent_pose;
  }
  const double x_shift = (distance_info.Dist() + kSmallDistanceInMeter) *
                         std::cos(distance_info.ObjectToEgoAngle());
  const double y_shift = (distance_info.Dist() + kSmallDistanceInMeter) *
                         std::sin(distance_info.ObjectToEgoAngle());
  collision_agent_pose.set_x_pos(collision_agent_pose.x_pos() + x_shift);
  collision_agent_pose.set_y_pos(collision_agent_pose.y_pos() + y_shift);
  return collision_agent_pose;
}

CollisionFrameInfo ComputeTtcCollisionInfo(
    const PoseRiskInfo& pose_risk_info_at_ttc, const voy::TrackedObject& agent,
    const vehicle_model::pb::AxleRectangularMeasurement& ego_shape,
    const planner::pb::MotionMode motion_mode) {
  const double dist_to_collision_point =
      pose_risk_info_at_ttc.collision_distance_info.Dist();
  // Too far to collide
  if (std::isinf(dist_to_collision_point)) {
    return {.severity = collision::CollisionSeverity::NoCollision()};
  }
  // Already has intersection if Dist() == 0. No need to make a copy
  const planner::pb::TrajectoryPose& most_probable_collision_agent_pose =
      math::NearZero(dist_to_collision_point)
          ? pose_risk_info_at_ttc.agent_pose
          : GetMostLikelyCollisionAgentPose(pose_risk_info_at_ttc);
  const voy::TrackedObject pred_tracked_object =
      common::GetTrackedObjectWithPredictedPose(
          agent, most_probable_collision_agent_pose);
  CollisionFrameInfo collision_info =
      ComputePoseCollisionInfo(pose_risk_info_at_ttc.ego_pose, ego_shape,
                               motion_mode, pred_tracked_object);
  DCHECK(collision_info.severity.severity_level !=
         voy::perception::CollisionDetection::NO_COLLISION_LEVEL);
  return collision_info;
}

// See
// https://docs.google.com/document/d/10mqJrXrkIQauYgIpEqJRZQqjKxOdXIUEUtqVAKXxKGE/edit?tab=t.0#heading=h.2hnhnj40rr8l
double ComputeTtcSeverityScore(const collision::CollisionSeverity& severity) {
  if (severity.severity_level ==
      voy::perception::CollisionDetection::NO_COLLISION_LEVEL) {
    return 1.0;
  }
  if (severity.severity_level ==
      voy::perception::CollisionDetection::NOT_GIVEN) {
    LOG(WARNING) << "Severity level not given";
    return 1.0;
  }

  // Now we use severity = 1.5^max(0, severity_level - S1)
  // The original designed formula is 2^severity_level. However, there are some
  // problems in our current implementation:
  // 1. The severity level is a concrete value, which makes the risk cost
  //    unstable when the speed is near some thresholds. So we temporarily
  //    make S1 severity no-op to decrease the impact of feature.
  // 2. There are some prediction FPs and yield prob FPs where is main
  //    prediction trajectory of the agent is quite dangerous. The risk cost
  //    will be amplified in these cases, and our ADV behavior will be too
  //    sensitive if we use the original formula.
  // We will scale up the severity score when some of the following features are
  // implemented:
  // - Continuous severity level (Done)
  // - Multi-BP agent risk computing
  // - AR fusion model upgrade
  double continuous_severity_value =
      FLAGS_planning_enable_selection_risk_continuous_severity
          ? severity.continuous_severity_value
          : severity.severity_level - voy::perception::CollisionDetection::S0;
  const double severity_exponent = std::max(0.0, continuous_severity_value - 1);
  return std::pow(kGenericSeverityFactor, severity_exponent);
}

PositionErrorInfo ComputeDistanceTotalErrorSd(
    double agent_heading, const PositionErrorInfo& ego_localization_error,
    const PositionErrorInfo& ego_control_error,
    const PositionErrorInfo& agent_tracking_error,
    const PositionErrorInfo& agent_prediction_error) {
  const double agent_normal = agent_heading + M_PI_2;
  return {
      agent_heading,
      std::sqrt(math::Sqr(agent_prediction_error.lateral_error_sd()) +
                agent_tracking_error.GetProjectedErrorVar(agent_normal) +
                ego_control_error.GetProjectedErrorVar(agent_normal) +
                ego_localization_error.GetProjectedErrorVar(agent_normal)),
      std::sqrt(math::Sqr(agent_prediction_error.longitudinal_error_sd()) +
                agent_tracking_error.GetProjectedErrorVar(agent_heading) +
                ego_control_error.GetProjectedErrorVar(agent_heading) +
                ego_localization_error.GetProjectedErrorVar(agent_heading))};
}

std::unique_ptr<prediction::pb::PredictedTrajectory> GetArBpWithUncertainty(
    bool is_ar_bp, const prediction::pb::PredictedTrajectory* pb_ar_bp,
    const PredictedTrajectoryWrapper& raw_bp) {
  if (pb_ar_bp == nullptr || !is_ar_bp) return {};

  prediction::pb::PredictedTrajectory prepared_pb_ar_bp = *pb_ar_bp;
  prepared_pb_ar_bp.set_id(raw_bp.id());
  for (int i = 0, upper_ix = 1; i < prepared_pb_ar_bp.traj_poses_size(); ++i) {
    planner::pb::TrajectoryPose& ar_traj_pose =
        *prepared_pb_ar_bp.mutable_traj_poses(i);
    while (upper_ix + 1 < raw_bp.size() &&
           raw_bp.pose(upper_ix).odom() < ar_traj_pose.odom()) {
      ++upper_ix;
    }
    DCHECK(math::IsInsideRange(upper_ix, 0, raw_bp.size()));
    const planner::pb::TrajectoryPose& upper_pose = raw_bp.pose(upper_ix);
    const planner::pb::TrajectoryPose& lower_pose = raw_bp.pose(upper_ix - 1);
    planner::pb::TrajectoryPoseUncertainty& uncertainty =
        *ar_traj_pose.mutable_uncertainty();
    uncertainty.set_lat_sd(math::GetLinearInterpolatedY(
        lower_pose.odom(), upper_pose.odom(), lower_pose.uncertainty().lat_sd(),
        upper_pose.uncertainty().lat_sd(), ar_traj_pose.odom()));
    uncertainty.set_long_sd(math::GetLinearInterpolatedY(
        lower_pose.odom(), upper_pose.odom(),
        lower_pose.uncertainty().long_sd(), upper_pose.uncertainty().long_sd(),
        ar_traj_pose.odom()));
  }
  return std::make_unique<prediction::pb::PredictedTrajectory>(
      std::move(prepared_pb_ar_bp));
}

const PoseRiskInfo* GetPoseRiskWithMaxLikelihoodEstOfTtc(
    const std::vector<PoseRiskInfo>& poses_risk) {
  auto added_prob_comp = [](const PoseRiskInfo& p1, const PoseRiskInfo& p2) {
    return p1.added_collision_prob < p2.added_collision_prob;
  };
  // Pose with max collision prob increment in whole planning horizon.
  const auto max_added_collision_prob_pose_iter =
      std::max_element(poses_risk.begin(), poses_risk.end(), added_prob_comp);
  if (max_added_collision_prob_pose_iter == poses_risk.end() ||
      math::NearZero(
          max_added_collision_prob_pose_iter->added_collision_prob)) {
    return nullptr;
  }

  // Try to identify any collision prob peak w/ valley before overall max
  // increment pose found above.
  const auto non_trivial_prob_iter =
      std::find_if(poses_risk.begin(), max_added_collision_prob_pose_iter,
                   [](const PoseRiskInfo& p) {
                     return p.collision_distance_info.DistOverSd() <
                            kSignifDistOverSdRatioThresForTtcEstimate;
                   });
  const auto no_added_collision_prob_iter = std::find_if(
      non_trivial_prob_iter, max_added_collision_prob_pose_iter,
      [](const auto& p) { return math::NearZero(p.added_collision_prob); });
  const auto max_added_prob_pose_before_plateau = std::max_element(
      poses_risk.begin(), no_added_collision_prob_iter, added_prob_comp);
  // A collision prob peak w/ valley means collision reach above some threshold
  // but then no collision prob increment for a while.
  const bool has_non_trivial_early_peak =
      (no_added_collision_prob_iter != max_added_collision_prob_pose_iter);

  // By strict statistics definition, MLE of TTC is pose with maximum added
  // collision probability max_added_collision_prob_pose_iter; But in practice,
  // our true goal is to approximate human perception of TTC better, and in some
  // corner cases (left/right turn parallel), prediction can get closer to ego,
  // part away then get closer again: for this case, early probability peak is a
  // better approximation of human perception.
  return (has_non_trivial_early_peak) ? &(*max_added_prob_pose_before_plateau)
                                      : &(*max_added_collision_prob_pose_iter);
}

double ComputeSignedLateralDistance(double search_range,
                                    const TrafficParticipantPose& agent_pose,
                                    const TrafficParticipantPose& ego_pose) {
  const bool further_than_search_range =
      // if CenterDist^2 > 2*(HalfEgoDiagLength^2 + HalfAgentDiagLength^2) >
      // (HalfEgoDiagLength + HalfAgentDiagLength)^2, then two bbox won't
      // intersect, distance is beyond search range and exit early.
      math::geometry::ComparableDistance(agent_pose.center_2d(),
                                         ego_pose.center_2d()) >
      0.5 * (math::Sqr(agent_pose.length()) +
             math::Sqr(agent_pose.width() + 2 * search_range) +
             math::Sqr(ego_pose.length()) + math::Sqr(ego_pose.width()));
  if (further_than_search_range) return std::numeric_limits<double>::infinity();

  const math::geometry::OrientedBox2d agent_bbox = GetPoseBBox(agent_pose);
  const math::geometry::OrientedBox2d ego_bbox = GetPoseBBox(ego_pose);
  if (math::geometry::Intersects(agent_bbox, ego_bbox)) return 0.0;

  double signed_lateral_distance = std::numeric_limits<double>::infinity();
  const math::geometry::OrientedBox2d extended_agent_bbox(
      agent_pose.center_2d(), agent_pose.length(),
      agent_pose.width() + 2 * search_range, agent_pose.heading());
  // TODO(selection): fix the naming issue from CR 4252795.
  const math::geometry::SegmentWithCache2d agent_right_dir(
      agent_pose.center_2d(), agent_pose.heading() + M_PI_2, 1.0);
  const auto result_updater = [&](const math::geometry::Point2d& point) {
    const double proj_length = agent_right_dir.GetProjectLength(point);
    const double point_lat_dist_to_agent =
        std::max(std::abs(proj_length) - agent_pose.width() * 0.5, 0.0);
    if (point_lat_dist_to_agent < std::abs(signed_lateral_distance)) {
      signed_lateral_distance =
          math::SignNum(proj_length) * point_lat_dist_to_agent;
    }
  };

  const auto intersection =
      math::geometry::Intersection<std::vector<math::geometry::Point2d>>(
          extended_agent_bbox, ego_bbox);
  for_each(intersection.begin(), intersection.end(), result_updater);
  // Need additional loop as boost intersection seems to have a bug
  // https://github.com/boostorg/geometry/issues/1182.
  for (const math::geometry::Point2d& corner : ego_bbox) {
    if (math::geometry::CoveredBy(corner, extended_agent_bbox)) {
      result_updater(corner);
    }
  }
  return signed_lateral_distance;
}

double ComputeSignedLongitudinalDistance(
    double search_range, const TrafficParticipantPose& agent_pose,
    const TrafficParticipantPose& ego_pose) {
  const bool further_than_search_range =
      // see further_than_search_range in ComputeSignedLateralDistance().
      math::geometry::ComparableDistance(agent_pose.center_2d(),
                                         ego_pose.center_2d()) >
      0.5 * (math::Sqr(agent_pose.width()) +
             math::Sqr(agent_pose.length() + 2 * search_range) +
             math::Sqr(ego_pose.length()) + math::Sqr(ego_pose.width()));
  if (further_than_search_range) return std::numeric_limits<double>::infinity();

  const math::geometry::OrientedBox2d agent_bbox = GetPoseBBox(agent_pose);
  const math::geometry::OrientedBox2d ego_bbox = GetPoseBBox(ego_pose);
  if (math::geometry::Intersects(agent_bbox, ego_bbox)) return 0.0;

  double signed_longitudinal_distance = std::numeric_limits<double>::infinity();
  const math::geometry::OrientedBox2d extended_agent_bbox(
      agent_pose.center_2d(), agent_pose.length() + 2 * search_range,
      agent_pose.width(), agent_pose.heading());
  const math::geometry::SegmentWithCache2d agent_head_dir(
      agent_pose.center_2d(), agent_pose.heading(), 1.0);
  const auto result_updater = [&](const math::geometry::Point2d& point) {
    const double proj_length = agent_head_dir.GetProjectLength(point);
    const double point_long_dist_to_agent =
        std::max(std::abs(proj_length) - agent_pose.length() * 0.5, 0.0);
    if (point_long_dist_to_agent < std::abs(signed_longitudinal_distance)) {
      signed_longitudinal_distance =
          math::SignNum(proj_length) * point_long_dist_to_agent;
    }
  };

  const auto intersection =
      math::geometry::Intersection<std::vector<math::geometry::Point2d>>(
          extended_agent_bbox, ego_bbox);
  for_each(intersection.begin(), intersection.end(), result_updater);
  // Need additional loop as boost intersection seems to have a bug
  // https://github.com/boostorg/geometry/issues/1182.
  for (const math::geometry::Point2d& corner : ego_bbox) {
    if (math::geometry::CoveredBy(corner, extended_agent_bbox)) {
      result_updater(corner);
    }
  }
  return signed_longitudinal_distance;
}

void UpdateInitialPoseRiskInfo(const TrafficParticipantPose& ego_pose,
                               const TrafficParticipantPose& agent_pose,
                               InitialPoseRiskInfo* init_pose_risk_info) {
  if (init_pose_risk_info == nullptr || init_pose_risk_info->has_been_set) {
    return;
  }

  init_pose_risk_info->ego_localization_error =
      GetLocalizationErrorInfo(ego_pose.heading(), ego_pose.speed());
  init_pose_risk_info->agent_tracking_error =
      GetTrackingErrorInfo(agent_pose, ego_pose);
  init_pose_risk_info->has_been_set = true;
}

BndRelativeLocation GetBndPosToVehicleFrameSignal(
    const TrafficParticipantPose& ego_pose,
    const TrafficParticipantPose& bnd_object_pose) {
  const math::geometry::OrientedBox2d ego_bbox = GetPoseBBox(ego_pose);
  const math::geometry::PolygonWithCache2d& bnd_object_contour =
      bnd_object_pose.contour();
  // Compute tire region bbox.
  const double dist_ego_center_to_tire_center =
      (ego_pose.width() - kEgoTireWidthInMeter) / 2;
  const double x_shift =
      dist_ego_center_to_tire_center * std::cos(ego_pose.heading() + M_PI_2);
  const double y_shift =
      dist_ego_center_to_tire_center * std::sin(ego_pose.heading() + M_PI_2);
  const auto compute_tire_region_bbox = [&](double x_offset, double y_offset) {
    return math::geometry::OrientedBox2d(
        {ego_pose.center_2d().x() + x_offset,
         ego_pose.center_2d().y() + y_offset},
        ego_pose.length(), kEgoTireWidthInMeter, ego_pose.heading());
  };
  const auto ego_right_tire_region_bbox =
      compute_tire_region_bbox(x_shift, y_shift);
  const auto ego_left_tire_region_bbox =
      compute_tire_region_bbox(-x_shift, -y_shift);

  if (!math::geometry::Intersects(ego_bbox, bnd_object_contour))
    return BndRelativeLocation::OUTSIDE_EGO_BBOX;
  if (math::geometry::Intersects(ego_right_tire_region_bbox,
                                 bnd_object_contour) ||
      math::geometry::Intersects(ego_left_tire_region_bbox,
                                 bnd_object_contour)) {
    return BndRelativeLocation::OVERLAP_TIRE_REGION;
  }
  return BndRelativeLocation::UNDER_CHASSIS;
}

void UpdateBndPosToVehicleFrameSignal(
    const BndRelativeLocation bnd_pos_to_ego_frame_signal,
    BndRelativeLocation& updated_value) {
  // Not innitialized, update anyway.
  const bool is_not_initialized = updated_value == BndRelativeLocation::UNKNOWN;
  // new_signal is more risky, either new_signal is under-chassis and old_signal
  // is outside-ego-bbox, or new_signal is overlap-tire-region and old_sginal is
  // not.
  const bool is_new_signal_more_risky =
      (bnd_pos_to_ego_frame_signal == BndRelativeLocation::UNDER_CHASSIS &&
       updated_value == BndRelativeLocation::OUTSIDE_EGO_BBOX) ||
      (bnd_pos_to_ego_frame_signal == BndRelativeLocation::OVERLAP_TIRE_REGION);
  if (is_not_initialized || is_new_signal_more_risky) {
    updated_value = bnd_pos_to_ego_frame_signal;
  }
}

double GetBetterNotDriveObjectRiskDiscount(
    const BndRelativeLocation bnd_pos_to_ego_frame_signal) {
  DCHECK(kBndRiskDiscountNonOverlap < kBndRiskDiscountUnderChassis);
  DCHECK(kBndRiskDiscountUnderChassis < kBndRiskDiscountTireOver);
  if (bnd_pos_to_ego_frame_signal == BndRelativeLocation::UNKNOWN) return 1.0;
  if (bnd_pos_to_ego_frame_signal == BndRelativeLocation::OUTSIDE_EGO_BBOX) {
    return kBndRiskDiscountNonOverlap;
  }
  if (bnd_pos_to_ego_frame_signal == BndRelativeLocation::OVERLAP_TIRE_REGION) {
    return kBndRiskDiscountTireOver;
  }
  return kBndRiskDiscountUnderChassis;
}

BpRiskInfo ComputeCollisionRiskOnAgentBp(
    const RiskEvalContext& risk_eval_context, const RiskEvalTarget& candidate,
    const PlannerObject& planner_object,
    const PredictedTrajectoryWrapper& agent_raw_bp,
    const prediction::pb::PredictedTrajectory* constraint_ar_bp,
    double considered_horizon_cap, bool is_ar_bp,
    const LaneChangeExtraUncertaintyContext&) {
  if (is_ar_bp && constraint_ar_bp == nullptr) return {};

  const voy::TrackedObject& agent_object = planner_object.tracked_object();
  std::unique_ptr<prediction::pb::PredictedTrajectory> ar_bp =
      GetArBpWithUncertainty(is_ar_bp, constraint_ar_bp, agent_raw_bp);
  const PredictedTrajectoryWrapper& agent_bp =
      is_ar_bp ? PredictedTrajectoryWrapper(agent_object, *ar_bp)
               : agent_raw_bp;
  DCHECK_EQ(agent_raw_bp.id(), agent_bp.id());
  DCHECK(!candidate.trajectory.poses().empty());
  const int64_t plan_init = candidate.trajectory.poses(0).timestamp();
  const PrincipledRequiredLateralGap* static_agent_required_lat_gap = nullptr;
  const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
      required_lat_gap_map = risk_eval_context.object_required_lat_gap_map;
  if (!IsUnstuckScenario(risk_eval_context.scenario_type) && !is_ar_bp) {
    static_agent_required_lat_gap =
        MaybeGetStaticRequiredLateralGap(required_lat_gap_map, agent_bp);
  }
  auto [lead_agent_bp, tail_agent_bp] =
      GetLcLeadTailAgentBp(risk_eval_context.seed.speed_seed(), agent_bp);
  const auto lat_buffer_iter = required_lat_gap_map.find(agent_bp.object_id());
  DCHECK(lat_buffer_iter != required_lat_gap_map.end());
  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>&
      agent_pb_poses = agent_bp.predicted_trajectory_proto().traj_poses();
  BpRiskInfo risk_on_bp;
  std::vector<PoseRiskInfo> poses_risk;
  poses_risk.reserve(agent_pb_poses.size());
  InitialPoseRiskInfo init_pose_risk_info{};
  // Reuse ego_pose in loop so that we can reduce the
  // overhead of constructing TrafficParticipantPose.
  // Need to set heading, speed, center_2d, length and width.
  TrafficParticipantPose ego_pose;
  ego_pose.set_type(voy::perception::ObjectType::VEHICLE);
  ego_pose.set_width(candidate.ego_shape.width());
  ego_pose.set_length(candidate.ego_shape.length());
  std::vector<double> ttc_times{0.0};
  std::vector<double> ttc_cumulative_probs{0.0};
  for (int i = 0; i < agent_pb_poses.size(); ++i) {
    const planner::pb::TrajectoryPose& agent_pb_pose = agent_pb_poses.at(i);
    const int64_t pose_ts = agent_pb_pose.timestamp();
    const std::optional<planner::pb::TrajectoryPose>& ego_pb_pose =
        candidate.geo_centered_traj->GetInterpolatedPose2d(pose_ts);
    if (!ego_pb_pose.has_value()) continue;

    const double time_after_plan_init_in_sec =
        math::Ms2Sec(pose_ts - plan_init);
    if (time_after_plan_init_in_sec > considered_horizon_cap) break;

    ego_pose.set_heading(ego_pb_pose->heading());
    ego_pose.set_speed(ego_pb_pose->speed());
    ego_pose.set_center_2d(ego_pb_pose->x_pos(), ego_pb_pose->y_pos());
    const TrafficParticipantPose agent_pose(agent_object, agent_pb_pose);
    UpdateInitialPoseRiskInfo(ego_pose, agent_pose, &init_pose_risk_info);
    const PositionErrorInfo ego_control_error =
        GetControlErrorInfo(ego_pose.heading(), time_after_plan_init_in_sec);
    const PositionErrorInfo agent_prediction_error(agent_pose.heading(),
                                                   agent_pb_pose.uncertainty());
    DCHECK(init_pose_risk_info.has_been_set);
    PositionErrorInfo total_distance_error = ComputeDistanceTotalErrorSd(
        agent_pose.heading(), init_pose_risk_info.ego_localization_error,
        ego_control_error, init_pose_risk_info.agent_tracking_error,
        agent_prediction_error);
    PositionErrorInfo uncapped_total_distance_error = total_distance_error;
    const double lat_sd_upper_bound =
        lat_buffer_iter->second.GetCriticalRequiredLateralGap(
            std::max(ego_pose.speed(), 0.0), ego_pb_pose->curvature());
    // TODO(jieruan): check if max should be min.
    const double long_sd_upper_bound =
        std::max({agent_pb_pose.speed() * kLongitudinalSdToSpeedRatioUpperBound,
                  kGenericLongitudinalSdUpperBound});
    total_distance_error.ClampErrorSd(lat_sd_upper_bound, long_sd_upper_bound);

    std::vector<PotentialCollisionDistanceInfo>
        potential_collision_distance_info_candidates;

    // Always consider lateral collision possibility.
    const double signed_lat_dist_to_ego = ComputeSignedLateralDistance(
        kMinLateralSearchRangeInM, agent_pose, ego_pose);
    // When signed_lat_dist_to_ego > 0, ego is on right side of agent, so agent
    // is on left side. Rad = agent.heading() + M_PI_2
    const double lat_agent_to_ego_angle_in_rad =
        agent_pose.heading() + M_PI_2 * math::SignNum(signed_lat_dist_to_ego);
    const PotentialCollisionDistanceInfo lat_collision_distance_info(
        PotentialCollisionDirection::kLateral, std::abs(signed_lat_dist_to_ego),
        lat_agent_to_ego_angle_in_rad, total_distance_error.lateral_error_sd());
    potential_collision_distance_info_candidates.push_back(
        std::move(lat_collision_distance_info));

    // Consider longitudinal collision possibility.
    const double signed_long_dist_to_ego = ComputeSignedLongitudinalDistance(
        kMinLongitudinalSearchRangeInM, agent_pose, ego_pose);
    const double capped_long_error_sd = std::min(
        total_distance_error.longitudinal_error_sd(),
        signed_long_dist_to_ego > 0 ? long_sd_upper_bound
                                    : kBackwardLongitudinalSdUpperBound);
    const double long_agent_to_ego_angle_in_rad =
        signed_long_dist_to_ego > 0 ? agent_pose.heading()
                                    : agent_pose.heading() + M_PI;
    const PotentialCollisionDistanceInfo long_collision_distance_info(
        PotentialCollisionDirection::kLongitude,
        std::abs(signed_long_dist_to_ego), long_agent_to_ego_angle_in_rad,
        capped_long_error_sd);
    potential_collision_distance_info_candidates.push_back(
        std::move(long_collision_distance_info));

    // Consider min_dist direction collision possibility.
    const PotentialCollisionDistanceInfo min_dist_collision_distance_info =
        ComputeAgentCornerToEgoFrontSideDistanceInfo(
            agent_pose, ego_pose, total_distance_error, lat_sd_upper_bound);
    potential_collision_distance_info_candidates.push_back(
        std::move(min_dist_collision_distance_info));

    const PotentialCollisionDistanceInfo& most_likely_collision_distance_info =
        *std::min_element(potential_collision_distance_info_candidates.begin(),
                          potential_collision_distance_info_candidates.end(),
                          [](const PotentialCollisionDistanceInfo& a,
                             const PotentialCollisionDistanceInfo& b) -> bool {
                            return a.DistOverSd() < b.DistOverSd();
                          });
    const double min_dist_over_sd =
        most_likely_collision_distance_info.DistOverSd();

    double added_collision_prob = 0.0;
    if (min_dist_over_sd < risk_on_bp.min_dist_over_sd) {
      risk_on_bp.min_dist_over_sd = min_dist_over_sd;
      risk_on_bp.dist_at_min_ratio = most_likely_collision_distance_info.Dist();
      risk_on_bp.sd_at_min_ratio = GetCappedSdAtMinRatio(
          signed_long_dist_to_ego > 0 ? long_sd_upper_bound
                                      : kBackwardLongitudinalSdUpperBound,
          uncapped_total_distance_error, most_likely_collision_distance_info);
      added_collision_prob =
          1.0 - NormalCdf(min_dist_over_sd) - risk_on_bp.collision_prob;
    }
    DCHECK_GE(added_collision_prob, 0.0);
    risk_on_bp.collision_prob += added_collision_prob;
    ttc_times.push_back(time_after_plan_init_in_sec);
    ttc_cumulative_probs.push_back(risk_on_bp.collision_prob);
    const double added_collision_risk =
        GetWeightOnTtc(time_after_plan_init_in_sec) * added_collision_prob;
    risk_on_bp.probability_risk += added_collision_risk;
    double pose_derivative_risk = 0.0;
    if (FLAGS_planning_enable_selection_derivative_risk) {
      pose_derivative_risk = GetPosewiseDerivativeRisk(
          time_after_plan_init_in_sec, risk_on_bp.dist_at_min_ratio,
          risk_on_bp.sd_at_min_ratio);
      risk_on_bp.derivative_risk += pose_derivative_risk;
    }

    // If the agent is better-not-drive object, compute the spatial relation
    // signal relative to the vehicle frame.
    if (FLAGS_planning_enable_selection_risk_bnd_discount &&
        planner_object.is_better_not_drive() && !is_ar_bp &&
        risk_on_bp.bnd_pos_to_ego_frame_signal !=
            BndRelativeLocation::OVERLAP_TIRE_REGION) {
      // TODO(Selection & Speed): Compute this through overlap in Speed
      // and get this signal directly here.
      BndRelativeLocation bnd_pos_to_ego_frame_signal =
          GetBndPosToVehicleFrameSignal(ego_pose, agent_pose);
      UpdateBndPosToVehicleFrameSignal(bnd_pos_to_ego_frame_signal,
                                       risk_on_bp.bnd_pos_to_ego_frame_signal);
    }

    PoseRiskInfo pose_risk_info{
        .time_after_plan_init_in_sec = time_after_plan_init_in_sec,
        .added_collision_prob = added_collision_prob,
        .ego_pose = std::move(*ego_pb_pose),
        .agent_pose = std::move(agent_pb_pose),
        .collision_distance_info =
            std::move(most_likely_collision_distance_info),
        .debug_info = GetPoseRiskDebugString(
            time_after_plan_init_in_sec,
            potential_collision_distance_info_candidates, added_collision_prob,
            added_collision_risk, pose_derivative_risk,
            risk_on_bp.dist_at_min_ratio, risk_on_bp.sd_at_min_ratio,
            init_pose_risk_info.ego_localization_error, ego_control_error,
            init_pose_risk_info.agent_tracking_error, agent_prediction_error)};

    // Special risks
    const double static_pose_risk = ComputePoseRiskForStaticAgent(
        static_agent_required_lat_gap,
        pose_risk_info.time_after_plan_init_in_sec, agent_pose, *ego_pb_pose,
        candidate.ego_shape, &(pose_risk_info.debug_info));
    math::UpdateMax(static_pose_risk, risk_on_bp.static_risk);
    const LaneChangePoseRisk lc_pose_risk = ComputePoseRiskForLaneChange(
        lead_agent_bp, tail_agent_bp,
        pose_risk_info.time_after_plan_init_in_sec, i, *ego_pb_pose, candidate,
        pose_risk_info.debug_info);
    math::UpdateMax(lc_pose_risk.risk, risk_on_bp.lc_risk);
    math::UpdateMin(lc_pose_risk.ttc_in_sec,
                    risk_on_bp.lc_specialized_ttc_in_sec);
    if (std::max({added_collision_risk, static_pose_risk, lc_pose_risk.risk,
                  pose_derivative_risk}) > kPosewiseRiskPrintThreshold ||
        std::min(std::abs(signed_lat_dist_to_ego),
                 std::abs(signed_long_dist_to_ego)) <
            kPosewiseDistancePrintThreshold) {
      risk_on_bp.debug_info += pose_risk_info.debug_info + "\n";
    }
    poses_risk.push_back(std::move(pose_risk_info));
  }
  const PoseRiskInfo* ttc_risk_info =
      GetPoseRiskWithMaxLikelihoodEstOfTtc(poses_risk);
  CollisionFrameInfo ttc_collision_info;
  double severity_score = 1.0;
  if (ttc_risk_info == nullptr) {
    risk_on_bp.ttc = std::numeric_limits<double>::infinity();
  } else {
    risk_on_bp.ttc = ttc_risk_info->time_after_plan_init_in_sec;
    ttc_collision_info = ComputeTtcCollisionInfo(
        *ttc_risk_info, agent_object, candidate.ego_shape,
        candidate.trajectory_info->motion_mode());
    severity_score = ComputeTtcSeverityScore(ttc_collision_info.severity);
  }
  math::UpdateMin(std::max(risk_on_bp.probability_risk *
                               kMaxRatioOfDerivativeRiskToProbRisk,
                           kMaxDerivativeRiskAtLowProbRisk),
                  risk_on_bp.derivative_risk);
  risk_on_bp.risk = std::max(risk_on_bp.lc_risk,
                             severity_score * (risk_on_bp.probability_risk +
                                               risk_on_bp.derivative_risk));
  if (!is_ar_bp) {
    risk_on_bp.ttc_cdf =
        math::PiecewiseLinearFunction(ttc_times, ttc_cumulative_probs);
    risk_on_bp.collision_severity = std::move(ttc_collision_info.severity);
    risk_on_bp.collision_position =
        std::move(ttc_collision_info.collision_position);
  }
  risk_on_bp.debug_info =
      absl::StrFormat(
          "risk = %.2f [generic = %.2f, static = %.2f, lc = %.2f, is_ar = %i, "
          "static_on = %i, ttc = %.2f, severity_level = %s, "
          "continuous_severity_value = %.2f, severity_score = %.2f, "
          "total_prob_risk = %.2f, total_deri_risk = %.2f].\n%s\n",
          risk_on_bp.risk, risk_on_bp.probability_risk, risk_on_bp.static_risk,
          risk_on_bp.lc_risk, is_ar_bp,
          static_agent_required_lat_gap != nullptr,
          std::min(risk_on_bp.ttc, planner::constants::kTrajectoryHorizonInSec),
          ttc_collision_info.severity.severity_level_name(),
          risk_on_bp.collision_severity.continuous_severity_value,
          ttc_collision_info.severity.continuous_severity_value,
          risk_on_bp.probability_risk, risk_on_bp.derivative_risk,
          init_pose_risk_info.debug) +
      risk_on_bp.debug_info;
  return risk_on_bp;
}

std::optional<math::geometry::Segment2d>
ComputeAgentCornersToEgoFrontSideDistVector(
    const TrafficParticipantPose& agent_pose,
    const TrafficParticipantPose& ego_pose) {
  std::optional<math::geometry::Segment2d> dist_vector{};
  double min_dist_sqr = std::numeric_limits<double>::infinity();
  const math::geometry::OrientedBox2d agent_bbox = GetPoseBBox(agent_pose);
  const math::geometry::OrientedBox2d ego_bbox = GetPoseBBox(ego_pose);
  if (math::geometry::Intersects(agent_bbox, ego_bbox)) return dist_vector;

  const math::geometry::SegmentWithCache2d ego_rear(
      ego_bbox.GetEdge(Box2dEdgeType::kRear));
  for (const Box2dCornerType corner_type :
       {Box2dCornerType::kFrontLeft, Box2dCornerType::kFrontRight,
        Box2dCornerType::kRearLeft, Box2dCornerType::kRearRight}) {
    if (!FLAGS_planning_selection_enable_min_distance_rear_perturbation &&
        (corner_type == Box2dCornerType::kRearLeft ||
         corner_type == Box2dCornerType::kRearRight)) {
      continue;
    }
    const math::geometry::Point2d agent_corner_pt =
        agent_bbox.CornerPoint(corner_type);
    // kLeft means before ego rear bumper.
    if (ego_rear.CheckSide(agent_corner_pt) != math::pb::Side::kLeft) continue;

    for (const Box2dEdgeType edge_type :
         {Box2dEdgeType::kFront, Box2dEdgeType::kLeft, Box2dEdgeType::kRight}) {
      const math::geometry::SegmentWithCache2d ego_edge(
          ego_bbox.GetEdge(edge_type));
      if (!ego_edge.WithIn(agent_corner_pt)) continue;

      const math::geometry::Point2d project_pt =
          ego_edge.GetClosestPoint(agent_corner_pt);
      const double dist_sqr =
          math::geometry::ComparableDistance(agent_corner_pt, project_pt);
      if (dist_sqr < min_dist_sqr) {
        min_dist_sqr = dist_sqr;
        dist_vector = math::geometry::Segment2d(agent_corner_pt, project_pt);
      }
    }
  }
  return dist_vector;
}

// TODO(jieruan): add handling of bbox intersects case, it's not handled
// currently because the dist vector is not specified and doesn't affect usage.
PotentialCollisionDistanceInfo ComputeAgentCornerToEgoFrontSideDistanceInfo(
    const TrafficParticipantPose& agent_pose,
    const TrafficParticipantPose& ego_pose, const PositionErrorInfo& error_info,
    double error_sd_upper_bound) {
  constexpr double kMaxCornerSideDistanceInM = 2.5;
  const bool further_than_search_range =
      math::geometry::ComparableDistance(agent_pose.center_2d(),
                                         ego_pose.center_2d()) >
      math::Sqr(kMaxCornerSideDistanceInM +
                0.5 * (agent_pose.length() + agent_pose.width() +
                       ego_pose.length() + ego_pose.width()));
  // Quick exit to avoid latency overhead.
  if (further_than_search_range)
    return PotentialCollisionDistanceInfo::WithInfiniteDistance(
        PotentialCollisionDirection::kAlongMinDist);

  const std::optional<math::geometry::Segment2d> corner_to_side_dist_vec =
      ComputeAgentCornersToEgoFrontSideDistVector(agent_pose, ego_pose);
  if (!corner_to_side_dist_vec.has_value()) {
    return PotentialCollisionDistanceInfo::WithInfiniteDistance(
        PotentialCollisionDirection::kAlongMinDist);
  }

  const math::geometry::Point2d seg_beg = corner_to_side_dist_vec->start();
  const math::geometry::Point2d seg_end = corner_to_side_dist_vec->end();
  const double distance = math::geometry::Distance(seg_beg, seg_end);
  const double heading =
      std::atan2(seg_end.y() - seg_beg.y(), seg_end.x() - seg_beg.x());
  const double projected_error_sd =
      math::Clamp(std::sqrt(error_info.GetProjectedErrorVar(heading)),
                  math::constants::kEpsilon, error_sd_upper_bound);
  return PotentialCollisionDistanceInfo(
      PotentialCollisionDirection::kAlongMinDist, distance, heading,
      projected_error_sd);
}

RiskCheckHorizonInfo ComputeRiskCheckHorizonForSpeedPartiallyIgnoredBp(
    const PredictedTrajectoryWrapper& raw_bp,
    std::optional<double> speed_check_horizon,
    const RiskEvalTarget& candidate) {
  if (!FLAGS_planning_enable_selection_risk_sync_speed_bp_truncation) return {};

  const bool is_cyc = (raw_bp.tracked_object().object_type() ==
                       voy::perception::ObjectType::CYCLIST);
  DCHECK(candidate.scenario_type.has_value());
  const bool is_in_lane_keep_scenario =
      (candidate.scenario_type.value() ==
       planner::pb::SelectionScenarioType::SCENARIO_NORMAL) ||
      (candidate.scenario_type.value() ==
       planner::pb::SelectionScenarioType::SCENARIO_FORK_SELECTION);
  if (!is_cyc || !is_in_lane_keep_scenario ||
      !speed_check_horizon.has_value()) {
    return {};
  }

  // Compute distance from agent prediction to ego's lane sequence center line.
  const std::vector<const pnc_map::Lane*>& ego_lane_seq =
      DCHECK_NOTNULL(candidate.lane_seq_res)->lane_sequence;
  const math::geometry::Point2d agent_center(
      raw_bp.tracked_object().center_x(), raw_bp.tracked_object().center_y());
  size_t proj_lane_ix = ego_lane_seq.size();
  for (size_t i = 0; i < ego_lane_seq.size(); ++i) {
    const math::ProximityQueryInfo proximity =
        DCHECK_NOTNULL(ego_lane_seq[i])
            ->center_line()
            .GetProximity(agent_center, math::pb::UseExtensionFlag::kAllow);
    if (proximity.relative_position == math::RelativePosition::kWithIn) {
      proj_lane_ix = i;
      break;
    }
  }
  if (proj_lane_ix >= ego_lane_seq.size()) return {};

  double agent_bp_dist_to_ego_route = std::numeric_limits<double>::max();
  for (const planner::pb::TrajectoryPose& agent_pose : raw_bp) {
    math::geometry::OrientedBox2d agent_bbox =
        GetPoseBBox(raw_bp.tracked_object(), agent_pose);
    for (size_t i = 0; i <= proj_lane_ix; ++i) {
      const double dist_to_lane = math::geometry::Distance(
          agent_bbox, DCHECK_NOTNULL(ego_lane_seq[i])->center_line());
      math::UpdateMin(dist_to_lane, agent_bp_dist_to_ego_route);
    }
    if (math::NearZero(agent_bp_dist_to_ego_route)) break;
  }

  // Risk check horizon decreases as agent BP distance to ego route decreases,
  // as it's harder to determine which side agent might go and better to stay
  // put, particularly for agent crossing from one side to the other. It's
  // guaranteed to be larger than speed_check_horizon.
  const double risk_check_horizon = math::GetLinearInterpolatedY(
      0.0, candidate.ego_shape.width() * 0.5,
      std::max(*speed_check_horizon, kMinCheckHorizonForSpeedIgnoreBpInSec),
      std::max(*speed_check_horizon, kMaxCheckHorizonForSpeedIgnoreBpInSec),
      agent_bp_dist_to_ego_route);
  return {.risk_check_horizon = risk_check_horizon,
          .speed_check_horizon = *speed_check_horizon,
          .agent_to_lane_dist = agent_bp_dist_to_ego_route};
}

// Returns overall risk of a planned trajectory in the scene by aggregating all
// risk constraints' evaluation result.
RiskEvalResult AggregateRiskConstraintEvalResults(
    const RiskEvalContext& risk_eval_context,
    std::vector<RiskConstraintEvalResult>&& constraint_eval_results) {
  // Sort to ensure deterministic as addition order can affect numeric results.
  std::vector<RiskConstraintEvalResult*> sorted_eval_results{};
  sorted_eval_results.reserve(constraint_eval_results.size());
  for (RiskConstraintEvalResult& result : constraint_eval_results) {
    sorted_eval_results.push_back(&result);
  }
  std::stable_sort(
      sorted_eval_results.begin(), sorted_eval_results.end(),
      [](const RiskConstraintEvalResult* l, const RiskConstraintEvalResult* r) {
        return (l->object_id != r->object_id)
                   ? (l->object_id < r->object_id)
                   : (l->prediction_id < r->prediction_id);
      });
  // TODO(jieruan): consider remove aggregation over agent directly aggregate
  // over constraint.
  absl::flat_hash_map<ObjectId, AgentRiskInfo> agent_risk_info_map{};
  for (size_t i = 0; i < sorted_eval_results.size(); ++i) {
    RiskConstraintEvalResult& eval_result =
        *DCHECK_NOTNULL(sorted_eval_results[i]);
    ObjectId object_id = eval_result.object_id;
    agent_risk_info_map[object_id].agent_id = object_id;
    if (eval_result.type == RiskConstraintType::REGULAR_PREDICTION) {
      agent_risk_info_map[object_id].prediction_prob_sum +=
          eval_result.prediction_prob;
    }
    agent_risk_info_map[object_id].constraints_eval_results.push_back(
        std::move(eval_result));
  }

  RiskEvalResult risk_eval_result{};
  double statistical_risk = 0.0;
  double worst_case_risk = 0.0;
  for (auto& [object_id, agent_risk_info] : agent_risk_info_map) {
    std::string agent_debug_summary{};
    std::string agent_debug_detail{};
    for (RiskConstraintEvalResult& eval_result :
         agent_risk_info.constraints_eval_results) {
      if (eval_result.type == RiskConstraintType::REGULAR_PREDICTION) {
        const double normalized_bp_prob =
            eval_result.prediction_prob /
            std::max(math::constants::kEpsilon,
                     agent_risk_info.prediction_prob_sum);
        if (eval_result.is_multi_output_bp) {
          agent_risk_info.statistical_regular_risk +=
              eval_result.risk * normalized_bp_prob;
        } else {
          const double special_bp_weight =
              risk_eval_context.should_use_multi_bp_risk ? kSpecialBpRiskWeight
                                                         : 1.0;
          math::UpdateMax(eval_result.risk * special_bp_weight,
                          agent_risk_info.statistical_special_risk);
        }
        agent_debug_summary += absl::StrFormat(
            "risk info on pred-%d: risk = %.2f [prob = %.2f, risk_by_prob = "
            "%.2f, scaler = %.2f]\n%s\n",
            eval_result.prediction_id, eval_result.risk * normalized_bp_prob,
            normalized_bp_prob, eval_result.probability_based_risk,
            eval_result.context_risk_scaler,
            std::move(eval_result.summarized_debug));
        agent_debug_detail += absl::StrFormat(
            "Risk detail on pred-%d:\n%s\n", eval_result.prediction_id,
            std::move(eval_result.detailed_debug));
      } else if (eval_result.type ==
                 RiskConstraintType::WORST_CASE_PREDICTION) {
        math::UpdateMax(eval_result.risk, agent_risk_info.worst_case_risk);
        agent_debug_summary +=
            absl::StrFormat("risk info on pred-%d: risk = %.2f.\n%s\n",
                            eval_result.prediction_id, eval_result.risk,
                            std::move(eval_result.summarized_debug));
        agent_debug_detail += absl::StrFormat(
            "Risk detail on pred-%d:\n%s\n", eval_result.prediction_id,
            std::move(eval_result.detailed_debug));
      }
    }
    agent_risk_info.statistical_risk =
        std::max(agent_risk_info.statistical_regular_risk,
                 agent_risk_info.statistical_special_risk);
    math::UpdateMax(agent_risk_info.statistical_risk, statistical_risk);
    math::UpdateMax(agent_risk_info.worst_case_risk, worst_case_risk);
    risk_eval_result.agent_debug_map[object_id] = absl::StrFormat(
        "Risk = %.2f [stat_risk = %.2f, worst_risk = %.2f]. "
        "\n\nDebug Summary:\n%s \nDebug Detail:\n%s\n",
        agent_risk_info.statistical_risk + agent_risk_info.worst_case_risk,
        agent_risk_info.statistical_risk, agent_risk_info.worst_case_risk,
        std::move(agent_debug_summary), std::move(agent_debug_detail));
    risk_eval_result.agents_risk_info.push_back(std::move(agent_risk_info));
  }
  std::vector<AgentRiskInfo>& agents_risk_info =
      risk_eval_result.agents_risk_info;
  std::stable_sort(
      agents_risk_info.begin(), agents_risk_info.end(),
      [](const AgentRiskInfo& risk_info1, const AgentRiskInfo& risk_info2) {
        return risk_info1.statistical_risk > risk_info2.statistical_risk;
      });
  agents_risk_info.resize(std::min(RiskEvalResult::kMaxNumTopRiskAgentsToSave,
                                   static_cast<int>(agents_risk_info.size())));
  if (!agents_risk_info.empty()) {
    risk_eval_result.max_risk_agent = agents_risk_info.front();
    std::vector<RiskConstraintEvalResult>& eval_results =
        risk_eval_result.max_risk_agent.constraints_eval_results;
    std::stable_sort(eval_results.begin(), eval_results.end(),
                     [](const auto& res1, const auto& res2) {
                       return res1.risk > res2.risk;
                     });
    risk_eval_result.max_risk_agent.debug_info =
        risk_eval_result.agent_debug_map[agents_risk_info.front().agent_id];
  }
  risk_eval_result.collision_risk = statistical_risk + worst_case_risk;
  risk_eval_result.overall_debug = absl::StrFormat(
      "max_risk_agent_id = %i, raw_risk = %.2f\n%s\n",
      risk_eval_result.max_risk_agent.agent_id, risk_eval_result.collision_risk,
      risk_eval_result.max_risk_agent.debug_info);
  return risk_eval_result;
}

// Returns collision risk of a regular prediction-based risk constraint.
RiskConstraintEvalResult EvaluatePredictionRiskConstraint(
    const PredictionRiskConstraint& prediction_constraint,
    const RiskEvalTarget& candidate, const RiskEvalContext& risk_eval_context,
    const RiskArFusionModel& ar_fusion_model,
    const LaneChangeExtraUncertaintyContext& lc_extra_uncertainty_context) {
  const PlannerObject& planner_object = prediction_constraint.object();
  const PredictedTrajectoryWrapper& raw_bp = prediction_constraint.prediction();
  const prediction::pb::PredictedTrajectory* ar_bp =
      prediction_constraint.ar_prediction();
  const RiskCheckHorizonInfo risk_check_horizon_info =
      ComputeRiskCheckHorizonForSpeedPartiallyIgnoredBp(
          raw_bp, prediction_constraint.speed_check_horizon(), candidate);
  const BpRiskInfo risk_on_raw_bp = ComputeCollisionRiskOnAgentBp(
      risk_eval_context, candidate, planner_object, raw_bp, ar_bp,
      risk_check_horizon_info.risk_check_horizon,
      /*is_ar_bp=*/false, lc_extra_uncertainty_context);
  const BpRiskInfo risk_on_ar_bp = ComputeCollisionRiskOnAgentBp(
      risk_eval_context, candidate, planner_object, raw_bp, ar_bp,
      risk_check_horizon_info.risk_check_horizon,
      /*is_ar_bp=*/true, lc_extra_uncertainty_context);
  const AgentYieldInfo agent_yield_info = ar_fusion_model.ComputeAgentYieldInfo(
      candidate, raw_bp, ar_bp,
      std::min(risk_on_raw_bp.ttc, constants::kTrajectoryHorizonInSec),
      prediction_constraint.liability_info().calibrated_agent_precedence);
  DCHECK(!candidate.trajectory.poses().empty());
  const double ego_init_speed = candidate.trajectory.poses(0).speed();
  const double required_ego_react_time = math::GetLinearInterpolatedY(
      kSafeYieldReactLowSpeedInMSec, kSafeYieldReactRegularSpeedInMSec,
      kRequiredSafeYieldReactTimeInLowSpeed,
      kRequiredSafeYieldReactTimeInRegularSpeed, ego_init_speed);
  const bool consider_ego_cant_yield =
      ar_bp != nullptr && candidate.consider_speed_change &&
      agent_yield_info.speed_agent_road_precedence ==
          planner::pb::RoadPrecedence::LOWER &&
      risk_on_raw_bp.ttc > required_ego_react_time;
  const double risk_adjust_on_raw_risk =
      (1 - agent_yield_info.yield_prob) *
      (consider_ego_cant_yield ? agent_yield_info.prob_ego_cant_yield : 1.0);
  const double probability_based_risk =
      std::max((risk_on_raw_bp.risk * risk_adjust_on_raw_risk +
                risk_on_ar_bp.risk * agent_yield_info.yield_prob) *
                   agent_yield_info.fidelity_discount,
               // static agent cannot react, thus risk is not discounted.
               risk_on_raw_bp.static_risk *
                   (raw_bp.IsStationary() ? 1.0 : risk_adjust_on_raw_risk));
  const bool discount_unknown_risk =
      FLAGS_planning_enable_selection_risk_unknown_discount &&
      (prediction_constraint.object().object_type() ==
       voy::perception::ObjectType::UNKNOWN);
  const bool discount_bnd_risk =
      FLAGS_planning_enable_selection_risk_bnd_discount &&
      prediction_constraint.object().is_better_not_drive();
  const double bnd_risk_scaler =
      discount_bnd_risk ? GetBetterNotDriveObjectRiskDiscount(
                              risk_on_raw_bp.bnd_pos_to_ego_frame_signal)
                        : 1.0;
  const double context_risk_scaler =
      (discount_unknown_risk ? kUnknownObjectRiskDiscount : 1.0) *
      bnd_risk_scaler *
      prediction_constraint.liability_info().risk_scaler_by_liability;
  return {
      .type = prediction_constraint.type(),
      .object_id = prediction_constraint.object().id(),
      .prediction_id = raw_bp.id(),
      .is_multi_output_bp = raw_bp.is_multi_output_trajectory(),
      .prediction_prob = raw_bp.Likelihood(),
      .risk =
          std::min(probability_based_risk * context_risk_scaler,
                   discount_bnd_risk ? kMaxBetterNotDriveObjectRisk
                                     : std::numeric_limits<double>::infinity()),
      .probability_based_risk = probability_based_risk,
      .context_risk_scaler = context_risk_scaler,
      .ttc_in_sec = risk_on_raw_bp.ttc,
      .ttc_cumulative_distribution = risk_on_raw_bp.ttc_cdf,
      .severity_level = risk_on_raw_bp.collision_severity.severity_level,
      .collision_position = risk_on_raw_bp.collision_position,
      .yield_probability = agent_yield_info.yield_prob,
      .lc_specialized_risk = risk_on_raw_bp.lc_risk,
      .lc_specialized_ttc_in_sec = risk_on_raw_bp.lc_specialized_ttc_in_sec,
      .summarized_debug = absl::StrFormat(
          "risk = %.2f, raw_risk = %.2f, ar_risk = %.2f, yield_prob = "
          "%.2f, scaler = %.2f, bnd_risk_scaler = %.2f.\nagent_road = %s, "
          "consider_cant_brake = %i, required_react_time = %.2f, check_horizon "
          "= %.2f[speed = %.2f, agent_to_lane = %.2f].\n",
          probability_based_risk * context_risk_scaler, risk_on_raw_bp.risk,
          risk_on_ar_bp.risk, agent_yield_info.yield_prob, context_risk_scaler,
          bnd_risk_scaler,
          planner::pb::RoadPrecedence::Enum_Name(
              agent_yield_info.agent_road_precedence),
          consider_ego_cant_yield, required_ego_react_time,
          risk_check_horizon_info.risk_check_horizon,
          risk_check_horizon_info.speed_check_horizon,
          risk_check_horizon_info.agent_to_lane_dist),
      .detailed_debug =
          absl::StrFormat("liability debug:\n%s \nagent yield debug: \n%s "
                          "\nraw_bp_risk.debug_info: "
                          "\n%s \nar_bp_risk.debug_info: \n%s\n",
                          prediction_constraint.liability_info().debug,
                          agent_yield_info.debug_info,
                          risk_on_raw_bp.debug_info, risk_on_ar_bp.debug_info)};
}

// Returns collision risk of worst-case risk constraint.
RiskConstraintEvalResult EvaluateWorstCasePredictionRiskConstraint(
    const WorstCasePredictionRiskConstraint& worst_case_bp_constraint,
    const RiskEvalTarget& candidate, const RiskEvalContext& risk_eval_context) {
  const PlannerObject& planner_object = worst_case_bp_constraint.object();
  const PredictedTrajectoryWrapper& worst_case_bp =
      worst_case_bp_constraint.worst_case_prediction();
  const BpRiskInfo risk_on_worst_case_bp = ComputeCollisionRiskOnAgentBp(
      risk_eval_context, candidate, planner_object, worst_case_bp,
      /*ar_bp=*/nullptr,
      /*considered_horizon_cap=*/std::numeric_limits<double>::infinity(),
      /*is_ar_bp=*/false, LaneChangeExtraUncertaintyContext{});
  const bool discount_unknown_risk =
      FLAGS_planning_enable_selection_risk_unknown_discount &&
      (worst_case_bp_constraint.object().object_type() ==
       voy::perception::ObjectType::UNKNOWN);
  const bool discount_bnd_risk =
      FLAGS_planning_enable_selection_risk_bnd_discount &&
      worst_case_bp_constraint.object().is_better_not_drive();
  const double context_risk_scaler =
      (discount_unknown_risk ? kUnknownObjectRiskDiscount : 1.0) *
      (discount_bnd_risk
           ? GetBetterNotDriveObjectRiskDiscount(
                 risk_on_worst_case_bp.bnd_pos_to_ego_frame_signal)
           : 1.0) *
      worst_case_bp_constraint.liability_info().risk_scaler_by_liability;
  const double constraint_weight =
      GetRiskConstraintWeight(worst_case_bp_constraint, candidate);
  return {
      .type = worst_case_bp_constraint.type(),
      .object_id = worst_case_bp_constraint.object().id(),
      .prediction_id = worst_case_bp.id(),
      .is_multi_output_bp = worst_case_bp.is_multi_output_trajectory(),
      .prediction_prob = worst_case_bp.Likelihood(),
      .risk =
          std::min(risk_on_worst_case_bp.risk * constraint_weight,
                   discount_bnd_risk ? kMaxBetterNotDriveObjectRisk
                                     : std::numeric_limits<double>::infinity()),
      .probability_based_risk = risk_on_worst_case_bp.risk,
      .context_risk_scaler = context_risk_scaler,
      .ttc_in_sec = risk_on_worst_case_bp.ttc,
      .ttc_cumulative_distribution = risk_on_worst_case_bp.ttc_cdf,
      .severity_level = risk_on_worst_case_bp.collision_severity.severity_level,
      .collision_position = risk_on_worst_case_bp.collision_position,
      .yield_probability = 0.0,
      .lc_specialized_risk = risk_on_worst_case_bp.lc_risk,
      .lc_specialized_ttc_in_sec =
          risk_on_worst_case_bp.lc_specialized_ttc_in_sec,
      .summarized_debug =
          absl::StrFormat("worst_case_bp_risk = %.2f, weight = %.2f.\n",
                          risk_on_worst_case_bp.risk, constraint_weight),
      .detailed_debug = absl::StrFormat("raw_bp_risk.debug_info: \n%s \n",
                                        risk_on_worst_case_bp.debug_info)};
}

RiskEvalResult SolveRisk(const RiskEvalContext& risk_eval_context,
                         const RiskConstraintSet& risk_constraint_set,
                         const RiskEvalTarget& candidate) {
  const std::vector<std::unique_ptr<RiskConstraint>>& risk_constraints =
      risk_constraint_set.constraints;
  std::vector<RiskConstraintEvalResult> risk_constraint_eval_results(
      risk_constraints.size());
  const RiskArFusionModel ar_fusion_model(
      risk_eval_context.seed, risk_eval_context.object_required_lat_gap_map);
  tbb::parallel_for(0, static_cast<int>(risk_constraints.size()), [&](int i) {
    switch (risk_constraints[i]->type()) {
      case RiskConstraintType::REGULAR_PREDICTION:
        risk_constraint_eval_results[i] = EvaluatePredictionRiskConstraint(
            dynamic_cast<const PredictionRiskConstraint&>(*risk_constraints[i]),
            candidate, risk_eval_context, ar_fusion_model,
            LaneChangeExtraUncertaintyContext{});
        break;
      case RiskConstraintType::WORST_CASE_PREDICTION:
        risk_constraint_eval_results[i] =
            EvaluateWorstCasePredictionRiskConstraint(
                dynamic_cast<const WorstCasePredictionRiskConstraint&>(
                    *risk_constraints[i]),
                candidate, risk_eval_context);
        break;
      default:
        DCHECK(false) << "Unknown constraint type.";
    }
  });
  return AggregateRiskConstraintEvalResults(
      risk_eval_context, std::move(risk_constraint_eval_results));
}

}  // namespace selection
}  // namespace planner
