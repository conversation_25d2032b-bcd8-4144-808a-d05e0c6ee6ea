#include "traffic_lights_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

TrafficLightsObserver::TrafficLightsObserver(TrafficLightsCallback callback)
    : callback_(std::move(callback)) {}

bool TrafficLightsObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kTrafficLightsMessage);
}

std::shared_ptr<google::protobuf::Message> TrafficLightsObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto traffic_lights = std::make_shared<voy::TrafficLights>();
  if (!traffic_lights->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error TrafficLights message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(traffic_lights, data_channel);
  }
  return traffic_lights;
}

}  // namespace remote_assist
