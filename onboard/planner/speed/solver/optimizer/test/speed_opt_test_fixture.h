#ifndef ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_TEST_SPEED_OPT_TEST_FIXTURE_H_
#define ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_TEST_SPEED_OPT_TEST_FIXTURE_H_

#include <limits>
#include <optional>
#include <string>
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#include <gtest/gtest.h>

#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/constraint/constraint_mutator.h"
#include "planner/speed/constraint/decision_defs.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/solver/optimizer/lqr_cost_model.h"
#include "planner/speed/solver/optimizer/lqr_potentials_generator.h"
#include "planner/speed/solver/optimizer/potentials.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/test_util/test_util.h"

namespace planner {
namespace speed {

// TODO(speed): add more unit tests for better coverage.
class GflagGuard {
 public:
  GflagGuard();
  ~GflagGuard();

  // Gflags have a global effect. If we change the value of a flag in a certain
  // test case but forget to restore it, then when testing other test cases, the
  // value will no longer be the default value but the changed value. Therefore,
  // we add a protection mechanism for gflags to record all gflags before
  // running and automatically restore them after the test runs.
 private:
  std::unordered_map<std::string, std::string> saved_flags_;
};

class PrivateMemberAccessorForUT {
 public:
  static auto GetAttractorMembers(const Attractor& attractor) {
    return std::tie(attractor.x0_, attractor.dx_,
                    attractor.normalization_factor_sqr_, attractor.gain_);
  }

  static auto GetSingleSideAttractorMembers(
      const SingleSidedAttractor& single_side_attractor) {
    return std::tie(single_side_attractor.sign_,
                    single_side_attractor.attractor_.x0_,
                    single_side_attractor.attractor_.dx_,
                    single_side_attractor.attractor_.normalization_factor_sqr_,
                    single_side_attractor.attractor_.gain_);
  }

  static auto GetAsymmetricalAttractorMembers(
      const AsymmetricalAttractor& asymmetrical_attractor,
      const bool positive) {
    if (positive) {
      return std::tie(
          asymmetrical_attractor.positive_attractor_.attractor_.x0_,
          asymmetrical_attractor.positive_attractor_.attractor_.dx_,
          asymmetrical_attractor.positive_attractor_.attractor_
              .normalization_factor_sqr_,
          asymmetrical_attractor.positive_attractor_.attractor_.gain_);
    }
    return std::tie(
        asymmetrical_attractor.negative_attractor_.attractor_.x0_,
        asymmetrical_attractor.negative_attractor_.attractor_.dx_,
        asymmetrical_attractor.negative_attractor_.attractor_
            .normalization_factor_sqr_,
        asymmetrical_attractor.negative_attractor_.attractor_.gain_);
  }

  static auto GetLateralAccelEffortMembers(
      const LateralAccelEffort& lateral_accel_effort) {
    return std::tie(lateral_accel_effort.path_, lateral_accel_effort.gain_);
  }

  static auto GetLateralJerkRepellerMembers(
      const LateralJerkRepeller& lateral_jerk_repeller) {
    return std::tie(lateral_jerk_repeller.path_,
                    lateral_jerk_repeller.max_lat_jerk_,
                    lateral_jerk_repeller.gain_);
  }

  static auto GetProximitySpeedMembers(const ProximitySpeed& proximity_speed) {
    return std::tie(proximity_speed.sign_, proximity_speed.max_v_,
                    proximity_speed.min_v_, proximity_speed.max_x_,
                    proximity_speed.min_x_, proximity_speed.gain_);
  }

  static LqrPotentials& GetMutableLqrPotentials(
      LqrPotentialsGenerator& lqr_potentials_generator) {
    return lqr_potentials_generator.lqr_potentials_;
  }

  static const std::vector<double>& GetYieldRepellerLowerBound(
      const LqrPotentialsGenerator& lqr_potentials_generator) {
    return lqr_potentials_generator.yield_repeller_lower_bound_;
  }

  static void PruneStatePotentials(
      LqrPotentialsGenerator& lqr_potentials_generator) {
    lqr_potentials_generator.PruneStatePotentials();
  }

  static DynamicRange ConstructDynamicRange(const double gain, const bool sign,
                                            const double ego_decel,
                                            const double other_x,
                                            const double other_stop_x,
                                            const double nominal_max_diff) {
    return DynamicRange(gain, sign, ego_decel, other_x, other_stop_x,
                        nominal_max_diff);
  }

  static double CalcDynamicRangeStopX(const double x, const double v,
                                      const double decel) {
    return DynamicRange::CalcStopX(x, v, decel);
  }

  static double CalcDynamicRangeNominalMaxDiff(const bool sign,
                                               const double ego_v_bound,
                                               const double ego_decel,
                                               const double other_v,
                                               const double other_decel,
                                               const double buffer_dist) {
    return DynamicRange::CalcNominalMaxDiff(sign, ego_v_bound, ego_decel,
                                            other_v, other_decel, buffer_dist);
  }
};

using FlagWithValue = std::pair<std::string, bool>;
using FlagCombination = std::vector<FlagWithValue>;
using FlagTestCases = std::vector<FlagCombination>;
class PotentialsGeneratorTestWithFlags
    : public virtual testing::TestWithParam<FlagCombination> {
 public:
  void SetUp() override {
    gflag_guard_ = new GflagGuard();
    SetGflagsWithGivenParams();
  }

  void TearDown() override { delete gflag_guard_; }

  // Set the gflags according to the test case input params.
  static void SetGflagsWithGivenParams();

  // Generate all combinations of boolean flags.
  static FlagTestCases GenerateAllCombinations(
      const std::vector<std::string>& flags);

  // Generate the name of the current test case, append the bool value (T/F) to
  // the flag name, for example, planning_enable_alilqr_in_speed_opt_T.
  static std::string GenerateTestCaseName(
      const testing::TestParamInfo<ParamType>& info);

 private:
  GflagGuard* gflag_guard_;
};

// A class that stores default input parameters. It provides an interface to
// conveniently modify parameters, thus setting different OptInput for various
// unit tests.
class OptInputBuilder {
  static constexpr double kSpeed = 20.0;

 public:
  // Builds the OptInput.
  SpeedOptInput build();

  // Avoid the influence of other potentials when testing cases with only
  // partial potentials enabled.
  // Note: These interfaces can only be used in lqr_test and ilqr_test, and must
  // not be used in potentials_test. For potentials testing, local_params should
  // be used directly.
  // TODO(yuwang): Encapsulate these interfaces into a new class, such as
  // InputBuilderForIlqrTest, to avoid using these interfaces in potential's
  // test.
  OptInputBuilder& set_empty_potential_params();
  OptInputBuilder& set_state_barrier_params();
  OptInputBuilder& set_state_repeller_params();
  OptInputBuilder& set_jerk_effort_params();
  OptInputBuilder& set_following_attractor_params();
  OptInputBuilder& set_lateral_accel_effort_params();
  OptInputBuilder& set_lateral_jerk_repeller_params();
  OptInputBuilder& set_proximity_speed_params();
  // TODO(yuwang): This is a temporary interface for compatibility with old unit
  // tests, and future adjustments to test cases should be made to avoid using
  // this interface.
  OptInputBuilder& set_proximity_speed_params(double gain);

  // TODO(yuwang): Currently, for quick adaptation, we temporarily write
  // specific values to potential_params. Later, we will refactor it to avoid
  // set the whole potentials_params.
  OptInputBuilder& set_potentials_params(
      const pb::PotentialParams& potential_params);

  // Modify opt related settings.
  OptInputBuilder& disable_speed_attract();
  OptInputBuilder& enable_fast_accel();
  OptInputBuilder& enable_stay_stopped(double desired_stopping_x_in);

  // Update profiles.
  OptInputBuilder& set_ref_profile(Profile profile);
  OptInputBuilder& set_search_profile(Profile profile);
  OptInputBuilder& set_min_profile(Profile profile);
  // TODO(yuwang): Set the search profile to the default value of the last
  // profile and only adjust last profile when changes are needed.
  OptInputBuilder& set_last_profile_as_search_profile();
  OptInputBuilder& update_ref_profile_with_const_jerk(double v_in, double a_in,
                                                      double j_in, double jerk);
  OptInputBuilder& update_search_profile_with_const_jerk(double v_in,
                                                         double a_in,
                                                         double j_in,
                                                         double jerk);
  OptInputBuilder& update_last_profile_with_const_jerk(double v_in, double a_in,
                                                       double j_in,
                                                       double jerk);
  OptInputBuilder& update_min_profile_by_braking_from_ref(int brake_first_ix,
                                                          int brake_end_ix);

  // Update path.
  OptInputBuilder& update_path_with_const_curvature(double x_end, double y_end,
                                                    double curvature);
  OptInputBuilder& update_path_with_const_pinch(double x_end, double y_end,
                                                double curvature_end,
                                                double pinch);

  // Modify the discomfort value.
  OptInputBuilder& set_discomfort(double discomfort_in);

  // Modify ego max speed.
  OptInputBuilder& set_ego_max_speed(double ego_max_speed_in);

  // Add constraints.
  ConstraintMutator add_const_speed_constraint(
      double t_start, double x_start, double v_const, int num_states,
      Decision decision, double selected_discomfort = 0.5);

  ConstraintMutator add_avoid_region(double t_start, double t_end,
                                     double x_start, double x_end,
                                     Decision decision,
                                     double selected_discomfort = 0.5);

  ConstraintMutator add_const_speed_gap_align_constraint(
      double t_start, double x_start, double v_const, int num_states,
      Decision decision, int active_time_ix, double selected_discomfort = 0.5);

  ConstraintMutator add_const_speed_proximity_speed_constraint(
      int state_ix_start, double x_start, double v_const, double lateral_gap,
      int num_states, pb::OverlapRegion& overlap_region,
      double agent_length = ra_to_fb_shift_ - ra_to_rb_shift_,
      double selected_discomfort = 0.5);

  // Mutable accessors.
  std::optional<Profile>& mutable_last_profile() { return last_profile_; }

 private:
  // Read from the local OptimizerConf. Use the latest local parameters as
  // default value to run unit tests, ensuring that when local parameters
  // change, the unit tests are also updated accordingly.
  pb::OptimizerConfig conf_ = PlannerConfigCenter::GetInstance()
                                  .GetDecoupledForwardManeuverConfig()
                                  .speed_generator_config()
                                  .for_optimizer();
  // Record local_potential_params for assigning values specifically to these
  // potentials' parameters when testing with only partial potentials enabled.
  pb::PotentialParams local_potential_params_ = conf_.potentials_params();

  Limits limits_ = Limits(/*brake_a=*/LimitRange(-2.0, 2.0),
                          /*accel_a=*/LimitRange(-2.0, 2.0),
                          /*brake_j=*/LimitRange(-3.0, 3.0),
                          /*accel_j=*/LimitRange(-2.5, 3.5),
                          /*max_v=*/std::numeric_limits<double>::infinity(),
                          /*max_brake2accel_j=*/3.0);
  DiscomfortVaryingMinRange min_range_ =
      DiscomfortVaryingMinRange(PlannerConfigCenter::GetInstance()
                                    .GetDecoupledForwardManeuverConfig()
                                    .speed_generator_config()
                                    .for_car_type()
                                    .min_range());
  double min_range_max_required_lat_gap_ = 0.2;
  double dt_ = 0.1;
  int num_steps_ = 80;
  constexpr static double ra_to_fb_shift_ = 3.5;
  constexpr static double ra_to_rb_shift_ = 1.5;
  double ego_max_speed_ = 1000.0;

  // Profiles
  Profile ref_profile_ = CreateProfileFromStateWithJerk(
      limits_,
      State({/*t_in=*/0.0, /*x_in=*/0.0, /*v_in=*/kSpeed,
             /*a_in=*/0.0, /*j_in=*/0.0}),
      num_steps_, /*jerk=*/0.0, dt_);
  Profile min_profile_ = CreateProfileFromStateWithJerk(
      limits_,
      State({/*t_in=*/0.0, /*x_in=*/0.0, /*v_in=*/0.0,
             /*a_in=*/0.0, /*j_in=*/0.0}),
      num_steps_, /*jerk=*/0.0, dt_);
  Profile search_profile_ = ref_profile_;
  std::optional<Profile> last_profile_ = std::nullopt;

  // Opt settings
  StayStopInfo stay_stopped_info_ =
      StayStopInfo(/*maybe_stay_stop_in=*/false, /*desired_stopping_x_in=*/0.0);
  bool enable_fast_acceleration_ = false;
  bool enable_profile_validation_ = true;
  bool disable_speed_attractor_ = false;

  // Tree search results
  int tree_switch_time_ix_ = 0;
  pb::TreeSearchType::Enum tree_search_type_ = pb::TreeSearchType::kNone;

  adv_geom::Path2dWithJuke path_ = adv_geom::Path2dWithJuke(
      /*points=*/{{0.0, 0.0}, {0, 200.0}},
      /*headings=*/{0.0, 0.0}, /*curvatures=*/{0.0, 0.0},
      /*pinches=*/{0.0, 0.0}, /*jukes*/ {0.0, 0.0});
  std::vector<Constraint> constraints_ = {};
  std::vector<Decision> decisions_ = {};
  absl::flat_hash_map<int, GapAlignInfo> gap_align_info_map_ =
      absl::flat_hash_map<int, GapAlignInfo>();
  double discomfort_ = 0.5;

  ConstraintCreator constraint_creator_ =
      ConstraintCreator(ra_to_fb_shift_, ra_to_rb_shift_, &constraints_);
};

// Verify whether a potential is empty or nullopt.
template <typename Potential>
void VerifyPotentialIsEmpty(
    const SpeedOptInput& opt_input,
    const Potential(LqrPotentials::StatePotentials::*member)) {
  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  bool type_is_included = false;
  for (int state_ix = 0; state_ix < num_steps; state_ix++) {
    const auto& container = (lqr_potentials.state_potentials[state_ix].*member);
    if constexpr (std::is_same_v<
                      Potential,
                      std::optional<typename Potential::value_type>>) {
      EXPECT_FALSE(container.has_value());
      type_is_included = true;
      continue;
    }

    if constexpr (std::is_same_v<Potential,
                                 std::vector<typename Potential::value_type>>) {
      EXPECT_TRUE(container.empty());
      type_is_included = true;
      continue;
    }

    if constexpr (std::is_same_v<
                      Potential,
                      tbb::concurrent_vector<typename Potential::value_type>>) {
      EXPECT_TRUE(container.empty());
      type_is_included = true;
      continue;
    }
  }
  EXPECT_TRUE(type_is_included);
}

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_SOLVER_OPTIMIZER_TEST_SPEED_OPT_TEST_FIXTURE_H_
