load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "can_timeout_detection_test",
    srcs = ["can_timeout_detection_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-float-conversion",
        "-Wno-missing-field-initializers",
        "-Wno-sign-compare",
    ],
    deps = [
        "//offboard/fallback_ads:fallback_l2_simulation",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_parameter",
        "//offboard/fallback_ads/vehicle_signal_input:vehicle_signal_input_type",
        "//offboard/fallback_ads/vehicle_signal_input/can_timeout_detection",
        "@voy-sdk//:gtest",
    ],
)
