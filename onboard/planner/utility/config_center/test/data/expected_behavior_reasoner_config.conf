lane_sequence_generator_config {
  lane_sequence_backward_trim_distance_in_meter: 50
  backward_lane_sequence_minimum_distance_in_meter: 25
  low_ego_speed_for_lane_sequence_divergenece_in_mps: 1
  closest_dist_to_ego_lane_end_latch_fork_lane_selection_in_meter: 10
  desired_lane_sequence_length_without_lane_change_in_meter: 150
  max_distance_from_ego_to_overtaking_object_in_meter: 50
  max_dist_to_junction_for_lane_change_penalty_in_meter: 200
  distance_threshold_to_extend_lane_follow_sequence_in_meter: 100
  minimum_committed_lane_distance_in_meter: 30
  estimate_number_of_nearby_keypoints: 30
  waypoint_graph_dot_max_edge_count: 50
}
maneuver_request_filter_configs {
  acceptable_maneuvers: LANE_FOLLOW
}
maneuver_request_filter_configs {
  selected_maneuver: LANE_CHANGE_LEFT
  acceptable_maneuvers: LANE_CHANGE_LEFT
}
maneuver_request_filter_configs {
  selected_maneuver: LANE_CHANGE_RIGHT
  acceptable_maneuvers: LANE_CHANGE_RIGHT
}
maneuver_request_filter_configs {
  selected_maneuver: LANE_CHANGE_ABORT
  acceptable_maneuvers: LANE_CHANGE_ABORT
}
maneuver_request_filter_configs {
  selected_maneuver: PULL_OUT
  acceptable_maneuvers: PULL_OUT
}
maneuver_request_filter_configs {
  selected_maneuver: PULL_OVER
  acceptable_maneuvers: PULL_OVER
}
maneuver_request_filter_configs {
  selected_maneuver: ENGAGE
  acceptable_maneuvers: ENGAGE
}
maneuver_request_filter_configs {
  selected_maneuver: REVERSE_DRIVING
  acceptable_maneuvers: REVERSE_DRIVING
}
remote_assist_config {
  forward_unstuck_config {
    supported_object_types: TRAFFIC_CONE
    supported_object_types: BARRIER
    supported_object_types: CYC_WITHOUT_PERSON
  }
  remote_warning_detector_config {
    warning_tracker_configs: [
        {
            task_meta: {
                task_code: POLICE_DETECTED
                type_1: TRAFFIC_RULE
                type_2: TRAFFIC_RULE_HARD
                type_3: POLICE_DETECTED
                source: WARNING
            }
            trigger_cycle_cnt: 30
            abort_cycle_cnt: 15
            is_enabled_request: false
            is_enabled_abort: true
        },
        {
            task_meta: {
                task_code: EMERGENCY_VEHICLE_DETECTED
                type_1: STUCK
                type_2: SPECIAL_STUCK
                type_3: EMERGENCY_VEHICLE_DETECTED
                source: WARNING
            }
            trigger_cycle_cnt: 30
            abort_cycle_cnt: 15
            is_enabled_request: false
            is_enabled_abort: true
        },
        {
            task_meta: {
                task_code: WARNING_GHOST_PEAK
                type_1: HAZARD_CONFLICT
                type_2: VRU_CONFLICT
                type_3: WARNING_GHOST_PEAK
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_NON_CRUSHABLE_OBSTACLE_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: OBSTACLE_CONFLICT
                type_3: WARNING_NON_CRUSHABLE_OBSTACLE_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_VRU_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VRU_CONFLICT
                type_3: WARNING_VRU_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_PULL_IN_LANE_CHANGE_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_PULL_IN_LANE_CHANGE_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_PULLOVER_LANE_CHANGE_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_PULLOVER_LANE_CHANGE_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_OTHER_CUT_IN_REAR_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_OTHER_CUT_IN_REAR_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_OTHER_CUT_IN_SIDE_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_OTHER_CUT_IN_SIDE_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_EGO_CUT_IN_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_EGO_CUT_IN_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_EGO_EB_REAR_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_EGO_EB_REAR_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_EGO_UTURN_REAR_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_EGO_UTURN_REAR_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_EGO_REVERSE_FRONT_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_EGO_REVERSE_FRONT_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_EGO_INTERSECTION_UTURN_REAR_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_EGO_INTERSECTION_UTURN_REAR_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: WARNING_BIG_VEHICLE_PULL_OUT_CUT_IN_CONFLICT
                type_1: HAZARD_CONFLICT
                type_2: VEH_CONFLICT
                type_3: WARNING_BIG_VEHICLE_PULL_OUT_CUT_IN_CONFLICT
                source: WARNING
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_OTHER
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_OTHER
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_NARROW_SPACE
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_NARROW_SPACE
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_TRAFFIC_LIGHT_BROKEN
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_TRAFFIC_LIGHT_BROKEN
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_TRAFFIC_LIGHT_CHANGED
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_TRAFFIC_LIGHT_CHANGED
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_ARROW_MARK_CHANGED
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_ARROW_MARK_CHANGED
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        },
        {
            task_meta: {
                task_code: STUCK_ROAD_LINE_CHANGED
                type_1: STUCK
                type_2: NORMAL_STUCK
                type_3: STUCK_ROAD_LINE_CHANGED
                source: UNSTUCK
            }
            trigger_cycle_cnt: 0
            abort_cycle_cnt: 15
            is_enabled_request: false
        }
    ]
  }
}
