#ifndef OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TRAJECTORY_DECODE_TRAJECTORY_DECODE_H_
#define OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TRAJECTORY_DECODE_TRAJECTORY_DECODE_H_

#include <cstddef>

#include "_out/Appl/GenData/Rte_Type.h"

namespace fallback_adas {
namespace acu_signal_input {

#define RteTrajectoryPose(message, signal_prefix, signal) \
  Rte_IRead_TrajectoryInput_60ms_##message##_##signal_prefix##signal()

#define TrajectoryPoseDecode(pose, index, message, signal_prefix)         \
  do {                                                                    \
    pose[index].x_pos =                                                   \
        RteTrajectoryPose(message, signal_prefix, PositionX) * 0.001F;    \
    pose[index].y_pos =                                                   \
        RteTrajectoryPose(message, signal_prefix, PositionY) * 0.001F;    \
    pose[index].heading =                                                 \
        RteTrajectoryPose(message, signal_prefix, Heading) * 2e-4F;       \
    pose[index].steering =                                                \
        RteTrajectoryPose(message, signal_prefix, Steering) * 4e-4F;      \
    pose[index].speed =                                                   \
        RteTrajectoryPose(message, signal_prefix, Speed) * 0.01F;         \
    pose[index].accel =                                                   \
        RteTrajectoryPose(message, signal_prefix, Acceleration) * 0.005F; \
    pose[index].curvature =                                               \
        RteTrajectoryPose(message, signal_prefix, Curvature) * 1e-4F;     \
  } while (0)

void TrajectoryDecode(TrajectoryInput* trajectory_ptr);

void AcuControlDecode(AcuControl* acu_control_ptr);

}  // namespace acu_signal_input
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TRAJECTORY_DECODE_TRAJECTORY_DECODE_H_
