#include "lateral_function/lane_center/lane_departure.h"

#include <math.h>
#include <stdbool.h>

#include "common_math_library/common_method.h"
#include "common_math_library/vehicle_parameters.h"
#include "lateral_function/lateral_function_parameter.h"

bool CheckLeftLaneDeparture(bool left_lane_exist, float left_lane_y0,
                            float left_lane_heading, float ego_long_velocity) {
  float relative_distance =
      left_lane_exist ? (left_lane_y0 - 0.5F * GetVehicleWidth()) : 10.0F;
  float relative_velocity =
      left_lane_exist ? (ego_long_velocity * sinf(left_lane_heading)) : 1e-6F;

  float time_to_lane_crossing = 0.0F;
  if (relative_distance > 0.0F && left_lane_heading < 0.0F) {
    time_to_lane_crossing =
        fabsf(CML_SafeDivision(relative_distance, relative_velocity));
  } else {
    time_to_lane_crossing = 40.0F;
  }

  return (relative_velocity < -GetLatMaxLateralVelocityTowardsLane() ||
          time_to_lane_crossing < GetLatMaxTimeToLaneCrossing());
}

bool CheckRightLaneDeparture(bool right_lane_exist, float right_lane_y0,
                             float right_lane_heading,
                             float ego_long_velocity) {
  float relative_distance =
      right_lane_exist ? (right_lane_y0 + 0.5F * GetVehicleWidth()) : -10.0F;
  float relative_velocity =
      right_lane_exist ? (ego_long_velocity * sinf(right_lane_heading)) : 1e-6F;

  float time_to_lane_crossing = 0.0F;
  if (relative_distance < 0.0F && right_lane_heading > 0.0F) {
    time_to_lane_crossing =
        fabsf(CML_SafeDivision(relative_distance, relative_velocity));
  } else {
    time_to_lane_crossing = 40.0F;
  }

  return (relative_velocity > GetLatMaxLateralVelocityTowardsLane() ||
          time_to_lane_crossing < GetLatMaxTimeToLaneCrossing());
}
