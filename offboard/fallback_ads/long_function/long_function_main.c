#include "long_function/long_function_main.h"

#include <math.h>
#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Components/Rte_LgtCtrlFct.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "common_math_library/vehicle_parameters.h"
#include "long_function/long_function_parameter.h"
#include "long_function/long_function_type.h"
#include "long_function/situation_evaluation/situation_evaluation_decision.h"
#include "long_function/stop_in_lane/avoidance_deceleration.h"
#include "long_function/stop_in_lane/free_stop_control.h"
#include "long_function/stop_in_lane/long_threat_assessor.h"
#include "long_function/stop_in_lane/time_to_collision.h"

static VSI_VehicleInfo_Struct* ReadVehicleInfo(void) {
  return (
      Rte_IRead_VehicleSignalProcess_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo());
}

static CSI_ObjectInfo_Struct* ReadObjectInfo(void) {
  return (
      Rte_IRead_ObjectPreSelection_10ms_Runnable_CSI_ObjectInfo_CSI_ObjectInfo());
}

static CSI_LaneInfo_Struct* ReadLaneInfo(void) {
  return (Rte_IRead_FreeStopControl_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo());
}

static VSP_VehicleSignal_Struct* ReadVspVehicleSignal(void) {
  return (
      Rte_IrvIRead_CollisionDetectionSystem_10ms_Runnable_VSP_VehicleSignal());
}

static FSC_FreeStopControl_Struct* ReadFreeStopControl(void) {
  return (
      Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_FSC_FreeStopControl());
}

static OPS_ObjectList_Struct* ReadOpsObjectList(void) {
  return (Rte_IrvIRead_CollisionDetectionSystem_10ms_Runnable_OPS_ObjectList());
}

static CDS_CollisionDetection_Struct* ReadCollisionDetectionSystem(void) {
  return (
      Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_CDS_CollisionDetection());
}

static void WriteVehicleSignalProcess(
    VSP_VehicleSignal_Struct* vsp_vehicle_signal) {
  Rte_IrvIWrite_VehicleSignalProcess_10ms_Runnable_VSP_VehicleSignal(
      vsp_vehicle_signal);
}

static void WriteObjectPreSelection(OPS_ObjectList_Struct* ops_object_list) {
  Rte_IrvIWrite_ObjectPreSelection_10ms_Runnable_OPS_ObjectList(
      ops_object_list);
}

void WriteFreeStopControl(float free_stop_accel_request,
                          float accel_request_by_lane_length,
                          float cruise_control_accel_request,
                          bool cruise_control_enable) {
  FSC_FreeStopControl_Struct fsc_free_stop_control = {0};

  fsc_free_stop_control.FSC_FreeStopAccRequest = free_stop_accel_request;
  fsc_free_stop_control.FSC_AccRequestByLaneLength =
      accel_request_by_lane_length;
  fsc_free_stop_control.FSC_CruiseAccRequest = cruise_control_accel_request;
  fsc_free_stop_control.FSC_LowSpeedCruiseEnable = cruise_control_enable;

  Rte_IrvIWrite_FreeStopControl_10ms_Runnable_FSC_FreeStopControl(
      &fsc_free_stop_control);
}

void WriteCollisionDetection(
    CDS_CollisionDetection_Struct collision_detection) {
  Rte_IrvIWrite_CollisionDetectionSystem_10ms_Runnable_CDS_CollisionDetection(
      &collision_detection);
}

static void WriteSituationEvaluationDecision(
    LongSystemState system_state, float accel_request, float jerk_request,
    VSP_VehicleSignal_Struct* vehicle_signal_ptr,
    FSC_FreeStopControl_Struct* free_stop_control_ptr,
    CDS_CollisionDetection_Struct* collision_detection_ptr) {
  LGT_CtrlCmd_Struct long_control_command = {0};
  long_control_command.LGT_SystemState = system_state;
  long_control_command.LGT_AccRequest = accel_request;
  long_control_command.LGT_JerkRequest = jerk_request;
  long_control_command.VSP_VehicleSignal = *vehicle_signal_ptr;
  long_control_command.OPS_ObjectList =
      *Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_OPS_ObjectList();
  long_control_command.CDS_CollisionDetection = *collision_detection_ptr;
  long_control_command.FSC_FreeStopControl = *free_stop_control_ptr;

  Rte_IWrite_SituationEvaluationDecision_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd(
      &long_control_command);
}

static void FrontObjectPropertyMapping(
    const CSI_ObjectInfo_Struct* object_info_ptr,
    OPS_ObjectList_Struct* front_object) {
  // signal mapping
  front_object->OPS_MainObjectWidth = object_info_ptr->CSI_Fr_AccOBJ_Width;
  front_object->OPS_MainObjectHeadingAngle =
      object_info_ptr->CSI_Fr_AccOBJ_HeadingAngle;
  front_object->OPS_MainObjectDx = object_info_ptr->CSI_Fr_AccOBJ_Dx;
  front_object->OPS_MainObjectDy = object_info_ptr->CSI_Fr_AccOBJ_Dy;
  front_object->OPS_MainObjectVx = object_info_ptr->CSI_Fr_AccOBJ_Vx;
  front_object->OPS_MainObjectVy = object_info_ptr->CSI_Fr_AccOBJ_Vy;
  front_object->OPS_MainObjectAx = object_info_ptr->CSI_Fr_AccOBJ_Ax;
  front_object->OPS_MainObjectAy = object_info_ptr->CSI_Fr_AccOBJ_Ay;
}

void LongitudinalControlFunction_Init(void) {
  CDS_CollisionDetection_Struct cds_collision_detection_init = {0};
  Rte_IrvIWrite_LongitudinalControlFunction_Init_CDS_CollisionDetection(
      &cds_collision_detection_init);

  FSC_FreeStopControl_Struct fsc_free_stop_control_init = {0};
  Rte_IrvIWrite_LongitudinalControlFunction_Init_FSC_FreeStopControl(
      &fsc_free_stop_control_init);

  OPS_ObjectList_Struct ops_object_list_init = {0};
  Rte_IrvIWrite_LongitudinalControlFunction_Init_OPS_ObjectList(
      &ops_object_list_init);

  VSP_VehicleSignal_Struct vsp_vehicle_signal_init = {0};
  Rte_IrvIWrite_LongitudinalControlFunction_Init_VSP_VehicleSignal(
      &vsp_vehicle_signal_init);

  LGT_CtrlCmd_Struct lgt_control_command_init = {0};
  Rte_IWrite_LongitudinalControlFunction_Init_LGT_CtrlCmd_LGT_CtrlCmd(
      &lgt_control_command_init);
}

void VehicleSignalProcess_10ms_Runnable() {
  static float filtered_long_velocity = 0.0F;
  static float filtered_lat_velocity = 0.0F;
  static float filtered_yaw_rate = 0.0F;
  static float filtered_steering_wheel_angle = 0.0F;
  static float filtered_steering_wheel_angle_rate = 0.0F;
  VSP_VehicleSignal_Struct vsp_vehicle_signal = {0};
  VSI_VehicleInfo_Struct* vehicle_info_ptr = ReadVehicleInfo();

  vsp_vehicle_signal.VSP_FrontWheelAngle =
      vehicle_info_ptr->VSI_FrontWheelSteeringAngle;

  // vehicle longitudinal speed low pass filter
  float long_velocity = vehicle_info_ptr->VSI_LongitudinalVelocity;
  if (GetVsiLongSpeedFilterEnable()) {
    CML_LowPassFilter(long_velocity, &filtered_long_velocity, 0.28F,
                      LGT_CONTROL_TASK_CYCLE_TIME);
  } else {
    filtered_long_velocity = long_velocity;
  }
  vsp_vehicle_signal.VSP_LgtVehSpdFild = filtered_long_velocity;

  // vehicle lateral speed low pass filter
  float lat_velocity = vehicle_info_ptr->VSI_LateralVelocity;
  if (GetVsiLatSpeedFilterEnable()) {
    CML_LowPassFilter(lat_velocity, &filtered_lat_velocity, 0.28F,
                      LGT_CONTROL_TASK_CYCLE_TIME);
  } else {
    filtered_lat_velocity = lat_velocity;
  }
  vsp_vehicle_signal.VSP_LatVehSpdFild = filtered_lat_velocity;

  // vehicle yaw rate low pass filter
  float yaw_rate = vehicle_info_ptr->VSI_YawRate;
  if (GetVsiYawRateFilterEnable()) {
    CML_LowPassFilter(yaw_rate, &filtered_yaw_rate, 0.28F,
                      LGT_CONTROL_TASK_CYCLE_TIME);
  } else {
    filtered_yaw_rate = yaw_rate;
  }
  vsp_vehicle_signal.VSP_YawRateFild = filtered_yaw_rate;

  // vehicle steering wheel angle low pass filter
  float steering_wheel_angle = vehicle_info_ptr->VSI_SteeringWheelAngle;
  if (GetVsiSteeringWheelAngleFilterEnable()) {
    CML_LowPassFilter(steering_wheel_angle, &filtered_steering_wheel_angle,
                      0.28F, LGT_CONTROL_TASK_CYCLE_TIME);
  } else {
    filtered_steering_wheel_angle = steering_wheel_angle;
  }
  vsp_vehicle_signal.VSP_StrWhlAngleFild = filtered_steering_wheel_angle;

  // vehicle steering wheel angle rate low pass filter
  float steering_wheel_angle_rate =
      vehicle_info_ptr->VSI_SteeringAngularVelocity;
  if (GetVsiSteeringWheelAngleRateFilterEnable()) {
    CML_LowPassFilter(steering_wheel_angle_rate,
                      &filtered_steering_wheel_angle_rate, 0.28F,
                      LGT_CONTROL_TASK_CYCLE_TIME);
  } else {
    filtered_steering_wheel_angle_rate = steering_wheel_angle_rate;
  }
  vsp_vehicle_signal.VSP_StrWhlAngleRateFild =
      filtered_steering_wheel_angle_rate;

  // todo: butter worth filter
  vsp_vehicle_signal.VSP_LgtVehAccFild =
      vehicle_info_ptr->VSI_LongitudinalAcceleration;
  vsp_vehicle_signal.VSP_LatVehAccFild =
      vehicle_info_ptr->VSI_LateralAcceleration;

  // write result to autosar rte
  WriteVehicleSignalProcess(&vsp_vehicle_signal);
}

void ObjectPreSelection_10ms_Runnable() {
  OPS_ObjectList_Struct front_object = {0};
  CSI_ObjectInfo_Struct* object_info_ptr = ReadObjectInfo();
  // signal mapping
  FrontObjectPropertyMapping(object_info_ptr, &front_object);

  // check object valid
  float exist_probability = object_info_ptr->CSI_Fr_AccOBJ_ExistProb;
  front_object.OPS_MainObjectValid = (exist_probability > 0.0F);

  // object motion offset
  // object Butterworth filter
  // todo, 20250102, to be implemented

  // write result to autosar rte
  WriteObjectPreSelection(&front_object);
}

void FreeStopControl_10ms_Runnable() {
  // read signals from autosar rte
  VSP_VehicleSignal_Struct* vehicle_signal_ptr = ReadVspVehicleSignal();
  CSI_LaneInfo_Struct* lane_info_ptr = ReadLaneInfo();

  // deceleration request for free stop control
  float free_stop_accel_request =
      CalculateFreeStopDeceleration(vehicle_signal_ptr->VSP_LgtVehSpdFild);

  // stop before ego lane end
  LaneInfo left_lane_info = {.lane_id = lane_info_ptr->CSI_LaneIDLf,
                             .dx_end = lane_info_ptr->CSI_PosXEndLf};
  LaneInfo right_lane_info = {.lane_id = lane_info_ptr->CSI_LaneIDRi,
                              .dx_end = lane_info_ptr->CSI_PosXEndRi};
  float accel_request_by_lane_length = CalculateAccelRequestByLaneLength(
      vehicle_signal_ptr->VSP_LgtVehSpdFild, left_lane_info, right_lane_info);

  // cruise control, just for test
  // todo, 20250102, to be implemented
  float cruise_control_accel_request = 0.0F;
  bool cruise_control_enable = false;

  // write result to autosar rte
  WriteFreeStopControl(free_stop_accel_request, accel_request_by_lane_length,
                       cruise_control_accel_request, cruise_control_enable);
}

void CollisionDetectionSystem_10ms_Runnable() {
  // read signals from autosar rte
  VSP_VehicleSignal_Struct* vehicle_signal_ptr = ReadVspVehicleSignal();
  OPS_ObjectList_Struct* ops_object_list_ptr = ReadOpsObjectList();

  // follow the object to brake
  CDS_CollisionDetection_Struct collision_detection = {0};
  LongThreatAssessor(vehicle_signal_ptr, ops_object_list_ptr,
                     &collision_detection);

  // write result to autosar rte
  WriteCollisionDetection(collision_detection);
}

void SituationEvaluationDecision_10ms_Runnable() {
  // read signals from autosar rte
  VSI_VehicleInfo_Struct* vehicle_info_ptr = ReadVehicleInfo();
  CSI_LaneInfo_Struct* lane_info_ptr = ReadLaneInfo();
  VSP_VehicleSignal_Struct* vehicle_signal_ptr = ReadVspVehicleSignal();
  CDS_CollisionDetection_Struct* collision_detection_ptr =
      ReadCollisionDetectionSystem();
  FSC_FreeStopControl_Struct* free_stop_control_ptr = ReadFreeStopControl();

  // update system state
  float accel_request = 0.0F;
  float jerk_request = 0.0F;
  LongSystemState system_state = SituationEvaluationDecision(
      vehicle_info_ptr, lane_info_ptr, vehicle_signal_ptr,
      free_stop_control_ptr, collision_detection_ptr, &accel_request,
      &jerk_request);

  // write result to autosar rte
  WriteSituationEvaluationDecision(system_state, accel_request, jerk_request,
                                   vehicle_signal_ptr, free_stop_control_ptr,
                                   collision_detection_ptr);
}
