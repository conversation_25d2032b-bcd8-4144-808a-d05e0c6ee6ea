#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_searcher.h"

#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_search_result.h"

namespace planner {

namespace open_space {

std::optional<OpenSpacePathSearchResult> OpenSpacePathSearcher::Search(
    const MotionPlanProblem& motion_plan_problem) {
  grid_.Update(motion_plan_problem);
  return hybrid_a_star_search_.Search();
}

}  // namespace open_space

}  // namespace planner
