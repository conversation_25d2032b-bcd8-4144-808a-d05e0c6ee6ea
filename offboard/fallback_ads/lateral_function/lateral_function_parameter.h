
#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_PARAMETER_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_PARAMETER_H_

#ifdef __cplusplus
extern "C" {
#endif

#ifndef LCT_TASK_CYCLE_TIME
#define LCT_TASK_CYCLE_TIME (0.01F)
#endif

#ifndef BDP_TASK_CYCLE_TIME
#define BDP_TASK_CYCLE_TIME (0.06F)
#endif

#ifndef FSA_TASK_CYCLE_TIME
#define FSA_TASK_CYCLE_TIME (0.06F)
#endif

#include "common_math_library/interpolation.h"

#ifndef LAT_GAIN_FOR_Q11_STATE_COST_TABLE_ROWS
#define LAT_GAIN_FOR_Q11_STATE_COST_TABLE_ROWS (5U)
#endif

#ifndef LAT_GAIN_FOR_Q33_STATE_COST_TABLE_ROWS
#define LAT_GAIN_FOR_Q33_STATE_COST_TABLE_ROWS (5U)
#endif

#ifndef LAT_MAX_WHEEL_ANGLE_TABLE_ROWS
#define LAT_MAX_WHEEL_ANGLE_TABLE_ROWS (10U)
#endif

#ifndef LAT_MAX_WHEEL_ANGLE_RATE_TABLE_ROWS
#define LAT_MAX_WHEEL_ANGLE_RATE_TABLE_ROWS (10U)
#endif

#ifndef SCALE_GAIN_FOR_Q_COST_RATIO_TABLE_ROWS
#define SCALE_GAIN_FOR_Q_COST_RATIO_TABLE_ROWS (5U)
#endif

extern float GetLatMinLaneWidth();
extern float GetLatMaxLaneWidth();
extern float GetLatLaneWidthHys();
extern float GetLatMinDistanceToLane();
extern float GetLatMinDistanceToLaneHys();
extern float GetLatMinLaneRadius();
extern float GetLatMinLaneRadiusHys();
extern float GetLatMaxLaneHeading();
extern float GetLatMinLaneLength();
extern float GetLatMinLaneLengthHys();
extern float GetLatMaxLateralVelocityTowardsLane();
extern float GetLatMaxTimeToLaneCrossing();
extern float GetLatLaneWidthForLaneMerge();
extern float GetLatControlCost();
extern float GetLatLqrTolerance();
extern uint16_t GetLatLqrMaxNumIteration();
extern const CML_Table1D* GetLatGainForQ11StateSpaceTable();
extern const CML_Table1D* GetLatGainForQ33StateSpaceTable();
extern const CML_Table1D* GetLatMaxWheelAngleTable();
extern const CML_Table1D* GetLatMaxWheelAngleRateTable();
extern const CML_Table1D* GetScaleGainForQCostRatioTable();

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LATERAL_FUNCTION_PARAMETER_H_
