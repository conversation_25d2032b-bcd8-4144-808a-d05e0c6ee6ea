#include "planner/open_space/unstuck_planner/uturn_unstuck_planner.h"

#include <algorithm>
#include <memory>
#include <utility>
#include <vector>

#include "math/arc_2d.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

namespace unstuck {

namespace {

// The execution progress anchor point for replan/abort logic.
constexpr double kLowExecutionProgressOnMotionSegment = 0.2;
constexpr double kMidExecutionProgressOnMotionSegment = 0.5;
constexpr double kHighExecutionProgressOnMotionSegment = 0.8;
// The execution stuck timeout threshold for replan/abort logic.
constexpr int64_t kLowMotionSegmentExecutionTimeoutThresholdInMSec = 10000;
constexpr int64_t kMidMotionSegmentExecutionTimeoutThresholdInMSec = 4000;
constexpr int64_t kHighMotionSegmentExecutionTimeoutThresholdInMSec = 2000;
// The additional time threshold used to check if ego get stuck before reaching
// enough speed in milisecond.
constexpr int64_t
    kMotionPlanAdditionalExecutionStuckAtStartingTimeThresholdInMSec = 2000;
// The minimum percentage of uturn progress to trigger u-turn unstuck. If EGO
// does not enter U-turn lanes or is at the early stage, u-turn unstuck should
// not be triggered.
constexpr double kUTurnMinProgressPercentage = 0.34;
// The minimum heading deviation of EGO from the u-turn end heading. If the
// deviation is below this threshold, EGO probably almost finished the u-turn.
constexpr double kMinHeadingDeviationFromUTurnEndInRad = M_PI / 6.0;
// The maximum heading deviation of EGO from the u-turn end heading. If the
// deviation is above this threshold, EGO is probably at the early stage of
// u-turn.
constexpr double kMaxHeadingDeviationFromUTurnEndInRad = M_PI_2 * 5.0 / 4.0;
// The minimum heading deviation of EGO for a late stage stuck from the u-turn
// end heading.
constexpr double kMinHeadingDeviationFromUTurnEndForLateStageStuckInRad =
    -M_PI / 18.0;
// The cool down time to avoid trigger u-turn unstuck for multiple times within
// a short time period, in milliseconds.
constexpr int64_t kUTurnUnstuckCoolDownTimeInMSec = 60000;
// The fallback steering angle factor for replan dry-steering.
constexpr double kFallbackReplanDrySteeringAngleFactor = 0.7;

// Computes the percentage of motion segment that has been executed.
double ComputeMotionSegmentExecutionProgress(
    const math::geometry::Point2d& ego_position,
    const pb::MotionSegment& current_motion_segment) {
  if (!current_motion_segment.has_reference_curve()) {
    return 0.0;
  }
  const math::geometry::PolylineCurve2d reference_curve(
      current_motion_segment.reference_curve());
  DCHECK(!reference_curve.empty());
  const double ego_arclength =
      reference_curve
          .GetProximity(ego_position, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  DCHECK_GT(reference_curve.GetTotalArcLength(), 0.0);
  return ego_arclength / reference_curve.GetTotalArcLength();
}

// Computes the stuck timeout threshold to replan or abort.
int64_t ComputeExecutionStuckTimeoutThreshold(
    double motion_segment_execution_progress) {
  // The stuck timeout threshold is a piecewise step function of the execution
  // progress.
  if (motion_segment_execution_progress <
      kLowExecutionProgressOnMotionSegment) {
    return kLowMotionSegmentExecutionTimeoutThresholdInMSec;
  } else if (motion_segment_execution_progress <
             kMidExecutionProgressOnMotionSegment) {
    return kMidMotionSegmentExecutionTimeoutThresholdInMSec;
  } else if (motion_segment_execution_progress <
             kHighExecutionProgressOnMotionSegment) {
    return kHighMotionSegmentExecutionTimeoutThresholdInMSec;
  }
  return 0;
}

// Generates trimmed stuck path when EGO triggers k-turn.
math::geometry::PolylineCurve2d GenerateTrimmedStuckPath(
    const planner::selection::TrajectoryMetaData& selected_trajectory_meta) {
  constexpr double kTrimmedStuckPathLengthInMeter = 7.0;
  const std::shared_ptr<const planner::pb::Path>& stuck_path =
      selected_trajectory_meta.path;
  std::vector<math::geometry::Point2d> trimmed_path_points;
  const size_t number_of_points =
      static_cast<size_t>(std::floor(
          (kTrimmedStuckPathLengthInMeter + math::constants::kEpsilon) /
          constants::kPathIntervalInMeter)) +
      1;
  trimmed_path_points.reserve(number_of_points);
  for (const auto& path_pose : DCHECK_NOTNULL(stuck_path)->poses()) {
    if (path_pose.odom() >
        kTrimmedStuckPathLengthInMeter + math::constants::kEpsilon) {
      break;
    }
    trimmed_path_points.emplace_back(path_pose.x_pos(), path_pose.y_pos());
  }
  return math::geometry::PolylineCurve2d(trimmed_path_points);
}

}  // namespace

pb::UnstuckPlannerState
UTurnUnstuckPlanner::CheckTriggerConditionAndProposeMotionPlan() {
  const std::optional<UTurnUnstuckInfo> uturn_unstuck_info =
      CheckTriggerCondition();
  if (!uturn_unstuck_info.has_value()) {
    // U-turn unstuck is not supposed to trigger under the current circumstance.
    return pb::UnstuckPlannerState::IDLE;
  }

  const std::optional<pb::MotionPlan> motion_plan =
      ProposeMotionPlan(uturn_unstuck_info.value());
  if (!motion_plan.has_value()) {
    // Failed to generate an unstuck motion plan.
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kFailedToGenerateMotionPlan);
    return pb::UnstuckPlannerState::IDLE;
  }

  // Record debug and rt event.
  uturn_unstuck_trigger_debug_->set_untriggered_reason(
      pb::UTurnUnstuckTriggerDebug::kTriggered);
  rt_event::PostRtEvent<rt_event::planner::UTurnUnstuckTrigger>();
  if (uturn_unstuck_info->is_late_stage_stuck) {
    rt_event::PostRtEvent<rt_event::planner::UTurnUnstuckLateStageStuck>();
  }

  // Update the triggering seed.
  mutable_curr_uturn_unstuck_trigger_seed()->set_last_trigger_lane_id(
      DCHECK_NOTNULL(uturn_unstuck_info->lane_info.uturn_end_lane())->id());
  mutable_curr_uturn_unstuck_trigger_seed()->set_last_trigger_timestamp(
      world_model().snapshot_timestamp());
  const math::geometry::PolylineCurve2d trimmed_stuck_path =
      GenerateTrimmedStuckPath(selected_trajectory_meta_);
  trimmed_stuck_path.ToProto(
      mutable_curr_uturn_unstuck_trigger_seed()->mutable_trimmed_stuck_path());
  for (const auto& blockage : uturn_unstuck_info->blockage_info) {
    if (blockage.non_uturn_unstuck_blockage_reason !=
        pb::UTurnUnstuckBlockageDebug::kIsUTurnUnstuckBlockage) {
      // Only record the blockage info that triggers k-turn.
      continue;
    }
    auto* pb_blockage = mutable_curr_uturn_unstuck_trigger_seed()
                            ->add_unstuck_unstuck_blockage();
    pb_blockage->set_blockage_type(blockage.blockage_type);
    if (blockage.object_id.has_value()) {
      *pb_blockage->mutable_blockage_object_id() =
          ToProto(blockage.object_id.value());
    }
  }

  // Update the unstuck planner seed.
  mutable_curr_seed().set_unstuck_scene_type(pb::UnstuckSceneType::UTURN);
  mutable_motion_plan() = std::move(motion_plan.value());
  return pb::UnstuckPlannerState::EXECUTING;
}

std::optional<UTurnUnstuckInfo> UTurnUnstuckPlanner::CheckTriggerCondition() {
  TRACE_EVENT_SCOPE(planner, CheckUTurnUnstuckTriggerCondition);
  if (!FLAGS_planning_enable_uturn_unstuck) {
    return std::nullopt;
  }
  if (!world_model().robot_state().IsInAutonomousMode()) {
    // EGO is not in autonomous mode and is not under the control of planner, no
    // need to trigger u-turn unstuck.
    return std::nullopt;
  }

  // Step 1: Check whether EGO is fully stopped.
  const bool is_ego_fully_stopped =
      world_model().robot_state().current_state_snapshot().speed() <
      constants::kDefaultEgoNearStaticSpeedInMps;
  if (!is_ego_fully_stopped ||
      !selected_trajectory_meta_.IsTrajectoryStopped()) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kNotStopped);
    return std::nullopt;
  }

  // Step 2: Check whether EGO is in lane change.
  // NOTE(Jiakai): When EGO is doing a lane change, the lane sequence is not
  // continuous and will break the assumption in the follow geometry compution
  // of the u-turn lane.
  if (selected_trajectory_meta_.IsCrossLane()) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kInLaneChange);
    return std::nullopt;
  }

  // Step 3: Generate lane info.
  const std::optional<UTurnUnstuckLaneInfo> uturn_unstuck_lane_info =
      GenerateUTurnUnstuckLaneInfo(
          selected_trajectory_meta_,
          world_model().robot_state().current_state_snapshot());
  if (!uturn_unstuck_lane_info.has_value()) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kNotOnUTurnLane);
    return std::nullopt;
  }
  for (const auto* lane : uturn_unstuck_lane_info->uturn_lane_sequence) {
    uturn_unstuck_trigger_debug_->mutable_u_turn_lane_seq()->Add(lane->id());
  }

  // Check heading diff with uturn lane.
  const double ego_heading =
      world_model().robot_state().current_state_snapshot().heading();
  const double heading_diff = math::AngleDiff(
      uturn_unstuck_lane_info->uturn_end_lane_heading(), ego_heading);
  uturn_unstuck_trigger_debug_->set_heading_diff_to_u_turn_end(heading_diff);

  bool is_late_stage_stuck = false;
  if (heading_diff < kMinHeadingDeviationFromUTurnEndInRad ||
      heading_diff > kMaxHeadingDeviationFromUTurnEndInRad) {
    if (heading_diff < kMinHeadingDeviationFromUTurnEndInRad &&
        heading_diff > kMinHeadingDeviationFromUTurnEndForLateStageStuckInRad) {
      // Late stage u-turn stuck. EGO's heading is almost aligned with u-turn
      // lane.
      is_late_stage_stuck = true;
    } else {
      uturn_unstuck_trigger_debug_->set_untriggered_reason(
          pb::UTurnUnstuckTriggerDebug::kImproperUTurnHeadingAlignment);
      return std::nullopt;
    }
  }

  // Step 4: Generate blockage info.
  std::vector<UTurnUnstuckBlockageInfo> potential_uturn_unstuck_blockage_info;
  // EGO's heading is almost aligned with u-turn lane in late stage. In this
  // situation, we don't need to consider the right hard boundary which is not
  // in speed constraints as uturn blockage.
  const bool has_uturn_unstuck_blockage = GenerateUTurnUnstuckBlockageInfo(
      world_model(), selected_trajectory_meta_,
      /*should_ignore_hard_boundary=*/is_late_stage_stuck,
      &potential_uturn_unstuck_blockage_info);
  // Record all potential blockage infos to debug.
  for (const UTurnUnstuckBlockageInfo& blockage_info :
       potential_uturn_unstuck_blockage_info) {
    pb::UTurnUnstuckBlockageDebug* pb_uturn_unstuck_blockage_debug =
        uturn_unstuck_trigger_debug_->add_uturn_unstuck_blockage_debug();
    blockage_info.ToDebugProto(pb_uturn_unstuck_blockage_debug);
  }

  if (!has_uturn_unstuck_blockage) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kNotStuckByPersistentObjectOrBlockage);
    return std::nullopt;
  }

  const math::geometry::PolylineCurve2d& uturn_lane_center_line =
      uturn_unstuck_lane_info->uturn_lane_center_line;

  // Check the u-turn progress
  const math::ProximityQueryInfo rear_axle_proximity =
      uturn_lane_center_line.GetProximity(world_model()
                                              .robot_state()
                                              .current_state_snapshot()
                                              .rear_axle_position(),
                                          math::pb::UseExtensionFlag::kForbid);
  const math::ProximityQueryInfo rear_bumper_proximity =
      uturn_lane_center_line.GetProximity(world_model()
                                              .robot_state()
                                              .current_state_snapshot()
                                              .rear_bumper_position(),
                                          math::pb::UseExtensionFlag::kForbid);
  const math::ProximityQueryInfo front_bumper_proximity =
      uturn_lane_center_line.GetProximity(world_model()
                                              .robot_state()
                                              .current_state_snapshot()
                                              .front_bumper_position(),
                                          math::pb::UseExtensionFlag::kForbid);
  // Uturn progress is calculated by average arc length of rear axle,
  // rear bumper and front bumper to eliminate the error from inconsistencies
  // in uturn lane length.
  const double average_arc_length =
      (rear_axle_proximity.arc_length + rear_bumper_proximity.arc_length +
       front_bumper_proximity.arc_length) /
      3;
  const double uturn_progress =
      average_arc_length / uturn_lane_center_line.GetTotalArcLength();
  uturn_unstuck_trigger_debug_->set_u_turn_progress(uturn_progress);
  if (uturn_progress < kUTurnMinProgressPercentage) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kImproperUTurnProgress);
    return std::nullopt;
  }

  // Step 5: Check scoping conditions.
  // NOTE(Jiakai): These checks are placed close to the end to provide an
  // accurate rt-event.
  if (HasConflictingDynamicBlockage(
          world_model().planner_object_map(),
          uturn_unstuck_lane_info->conflicting_lanes(),
          uturn_unstuck_trigger_debug_)) {
    uturn_unstuck_trigger_debug_->set_untriggered_reason(
        pb::UTurnUnstuckTriggerDebug::kHasConflictingBlockage);
    rt_event::PostRtEvent<
        rt_event::planner::UTurnUnstuckUntriggerByConflictingBlockage>();
    return std::nullopt;
  }

  // Step 6: Check whether the u-turn unstuck has been triggered for the same
  // lane within the cool down time.
  const int64_t uturn_end_lane_id =
      uturn_unstuck_lane_info->uturn_end_lane()->id();
  const int64_t current_timestamp = world_model().snapshot_timestamp();
  if (prev_uturn_unstuck_trigger_seed().has_last_trigger_lane_id()) {
    const int64_t last_trigger_lane_id =
        prev_uturn_unstuck_trigger_seed().last_trigger_lane_id();
    const int64_t last_trigger_timestamp =
        prev_uturn_unstuck_trigger_seed().last_trigger_timestamp();
    if (uturn_end_lane_id == last_trigger_lane_id &&
        current_timestamp - last_trigger_timestamp <
            kUTurnUnstuckCoolDownTimeInMSec) {
      // U-turn unstuck has been triggered for same lane recently. Do NOT
      // trigger again.
      uturn_unstuck_trigger_debug_->set_untriggered_reason(
          pb::UTurnUnstuckTriggerDebug::kTriggeredRecently);
      return std::nullopt;
    }
  }

  // Protection logic for simulation.
  if (av_comm::InWarmup()) {
    DCHECK(false) << "U-Turn unstuck scenario is detected in the phase of "
                     "simulation warm-up, which may result in DCHECK failures "
                     "in unstuck planner state transition, since EGO is not "
                     "under control of planner. Please update the warm-up time "
                     "of this scenario to make it prior to the frame of stuck.";
  }

  return UTurnUnstuckInfo(std::move(uturn_unstuck_lane_info.value()),
                          std::move(potential_uturn_unstuck_blockage_info),
                          is_late_stage_stuck);
}

std::optional<pb::MotionPlan> UTurnUnstuckPlanner::ProposeMotionPlan(
    const UTurnUnstuckInfo& uturn_unstuck_info) {
  // Generate ROI.
  const MotionPlanGenerationRoi motion_plan_roi = GeneratRoi(
      world_model(), lane_sequence_geometry(), uturn_unstuck_info.lane_info,
      uturn_unstuck_motion_plan_debug_);

  const auto& robot_state = world_model().robot_state();
  const MotionPlanTargetInfo motion_plan_target_info =
      GenerateMotionPlanTargetInfo(uturn_unstuck_info.lane_info,
                                   motion_plan_roi,
                                   robot_state.car_model_with_shape());

  UTurnUnstuckMotionPlanGenerator uturn_unstuck_motion_plan_generator(
      robot_state.car_model_with_shape());

  const pb::UTurnUnstuckMotionPlanType motion_plan_type =
      uturn_unstuck_info.is_late_stage_stuck
          ? pb::UTurnUnstuckMotionPlanType::kLateStage
          : pb::UTurnUnstuckMotionPlanType::kNormal;
  mutable_curr_uturn_unstuck_trigger_seed()->set_motion_plan_type(
      motion_plan_type);
  return uturn_unstuck_motion_plan_generator.Generate(
      motion_plan_type, robot_state, motion_plan_roi, motion_plan_target_info,
      /*ego_reversed_distance=*/std::nullopt, uturn_unstuck_motion_plan_debug_);
}

bool UTurnUnstuckPlanner::ShouldAbortMotionPlan() {
  if (world_model().mrc_request_ptr() != nullptr &&
      world_model().mrc_request_ptr()->mrm_progress() == mrc::pb::FINISHED) {
    // Abort unstuck planner if MRC state received.
    mutable_motion_plan().set_abort_reason(pb::MotionPlanAbortReason::IN_MRC);
    return true;
  }
  if (!world_model().robot_state().IsInAutonomousMode()) {
    // Abort the motion plan if EGO is in Manual.
    mutable_motion_plan().set_abort_reason(
        pb::MotionPlanAbortReason::MANUAL_TAKEOVER);
    return true;
  }

  if (prev_seed().current_state() != pb::UnstuckPlannerState::IDLE &&
      prev_uturn_unstuck_trigger_seed().motion_plan_type() !=
          pb::UTurnUnstuckMotionPlanType::kReplanForwardDrySteering &&
      AreAllBlockagesRemoved(prev_uturn_unstuck_trigger_seed(),
                             world_model())) {
    rt_event::PostRtEvent<rt_event::planner::UTurnUnstuckAllBlockagesRemoved>();
    // All of the blockages are removed.
    if (HasEgoFullyStopped()) {
      // EGO is stopped. Directly try to replan.
      if (CheckReplanConditionAndUpdateMotionPlan()) {
        // Replan and turn steering wheel to the left to unstuck.
        return false;
      }
    } else {
      // EGO is not stopped. Abort the current motion plan first.
      mutable_motion_plan().set_abort_reason(
          pb::MotionPlanAbortReason::OUTDATED_NEED_REPLAN);
      return true;
    }
  }

  // TODO(Jiakai): Improve the replan and abort logic by consideration of speed
  // decision.
  const double motion_segment_execution_progress =
      ComputeMotionSegmentExecutionProgress(world_model()
                                                .robot_state()
                                                .current_state_snapshot()
                                                .rear_axle_position(),
                                            current_motion_segment());

  const int64_t execution_stuck_timeout_threshold =
      ComputeExecutionStuckTimeoutThreshold(motion_segment_execution_progress);

  if (!IsMotionPlanStuckAndTimeOut(
          execution_stuck_timeout_threshold,
          execution_stuck_timeout_threshold +
              kMotionPlanAdditionalExecutionStuckAtStartingTimeThresholdInMSec)) {
    return false;
  }

  rt_event::PostRtEvent<rt_event::planner::UTurnUnstuckTimeoutReplan>();
  if (CheckReplanConditionAndUpdateMotionPlan()) {
    return false;
  }
  mutable_motion_plan().set_abort_reason(
      pb::MotionPlanAbortReason::TIMEOUT_STUCK);
  return true;
}

bool UTurnUnstuckPlanner::CheckScenarioSpecificEarlyTerminationCondition() {
  // Only do early termination for late-stage stuck, since late-stage the motion
  // plan might not be accurte enough and have excessive dry-steering.
  return prev_uturn_unstuck_trigger_seed().motion_plan_type() ==
         pb::UTurnUnstuckMotionPlanType::kLateStage;
}

bool UTurnUnstuckPlanner::CheckReplanConditionAndUpdateMotionPlan() {
  // Generate unstuck lane info.
  const std::optional<UTurnUnstuckLaneInfo> uturn_unstuck_lane_info =
      GenerateUTurnUnstuckLaneInfo(prev_uturn_unstuck_trigger_seed(),
                                   world_model());
  if (!uturn_unstuck_lane_info.has_value()) {
    return false;
  }

  for (const auto* lane : uturn_unstuck_lane_info->uturn_lane_sequence) {
    uturn_unstuck_trigger_debug_->mutable_u_turn_lane_seq()->Add(lane->id());
  }

  // Generate ROI.
  const MotionPlanGenerationRoi motion_plan_roi = GeneratRoi(
      world_model(), lane_sequence_geometry(), uturn_unstuck_lane_info.value(),
      uturn_unstuck_motion_plan_debug_);
  const MotionPlanTargetInfo motion_plan_target_info =
      GenerateMotionPlanTargetInfo(
          uturn_unstuck_lane_info.value(), motion_plan_roi,
          world_model().robot_state().car_model_with_shape());

  UTurnUnstuckMotionPlanGenerator uturn_unstuck_motion_plan_generator(
      world_model().robot_state().car_model_with_shape());

  // First check whether the forward dry steering replan method can generate a
  // motion plan successfully.
  std::optional<pb::MotionPlan> motion_plan =
      uturn_unstuck_motion_plan_generator.Generate(
          pb::UTurnUnstuckMotionPlanType::kReplanForwardDrySteering,
          world_model().robot_state(), motion_plan_roi, motion_plan_target_info,
          /*ego_reversed_distance=*/std::nullopt,
          uturn_unstuck_motion_plan_debug_);
  if (motion_plan.has_value()) {
    mutable_curr_uturn_unstuck_trigger_seed()->set_motion_plan_type(
        pb::UTurnUnstuckMotionPlanType::kReplanForwardDrySteering);
  } else {
    rt_event::PostRtEvent<
        rt_event::planner::UTurnUnstuckReplanAfterReverseStuck>();
    // Failed to generate forward dry steering replan motion plan.
    if (FLAGS_planning_enable_kturn_replan_after_reverse_stuck) {
      // If the forward dry steering replan method fails to generate a motion
      // plan, try the FRF replan method.
      const math::geometry::PolylineCurve2d reference_curve(
          current_motion_segment().reference_curve());
      DCHECK(!reference_curve.empty());
      const double ego_reversed_distance =
          reference_curve
              .GetProximity(world_model()
                                .robot_state()
                                .current_state_snapshot()
                                .rear_axle_position(),
                            math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      motion_plan = uturn_unstuck_motion_plan_generator.Generate(
          pb::UTurnUnstuckMotionPlanType::kReplanAfterReverseStuck,
          world_model().robot_state(), motion_plan_roi, motion_plan_target_info,
          ego_reversed_distance, uturn_unstuck_motion_plan_debug_);
      if (motion_plan.has_value()) {
        mutable_curr_uturn_unstuck_trigger_seed()->set_motion_plan_type(
            pb::UTurnUnstuckMotionPlanType::kReplanAfterReverseStuck);
      }
    }
    if (!motion_plan.has_value()) {
      // If both of the above replan methods fail to generate a replan motion
      // plan, fall back to the default forward dry steering motion plan.
      motion_plan.emplace();
      pb::MotionSegment* motion_segment =
          motion_plan.value().add_motion_segments();
      motion_segment->set_init_steering_angle(
          kFallbackReplanDrySteeringAngleFactor * world_model()
                                                      .robot_state()
                                                      .car_model_with_shape()
                                                      .param()
                                                      .limit()
                                                      .steering_limit()
                                                      .max_val());
    }
  }
  mutable_motion_plan().Clear();
  mutable_motion_plan() = std::move(motion_plan.value());
  return true;
}

bool UTurnUnstuckPlanner::ReplanAfterAbort() {
  if (motion_plan().abort_reason() !=
      pb::MotionPlanAbortReason::OUTDATED_NEED_REPLAN) {
    return false;
  }
  return CheckReplanConditionAndUpdateMotionPlan();
}

}  // namespace unstuck

}  // namespace planner
