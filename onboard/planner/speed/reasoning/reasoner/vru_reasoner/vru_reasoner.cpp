#include "planner/speed/reasoning/reasoner/vru_reasoner/vru_reasoner.h"

#include <algorithm>
#include <cstdint>
#include <iterator>
#include <limits>
#include <queue>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>

#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/planning_gflags.h"
#include "planner/speed/agent_reaction/agent_reaction_param_def.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/constraint/constraint_mutator.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/prediction_decision_maker.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/ego_state_history.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/crosswalk_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_info.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_rule_utility.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/urgency_info.h"
#include "planner/speed/reasoning_input/util/creep_gap_util.h"
#include "planner/speed/reasoning_input/util/matching_crosswalk.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "planner_protos/speed_yielding_fence.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {

// If a cyclist has speed below this threshold, we think that it's slow enough.
constexpr double kSpeedThresholdOfSlowCyclistInMps = 0.7;
// The required lateral gap for cyclists that are slow.
constexpr double kComfortLateralGapForSlowCyclistInMeter = 0.6;
// The min acceleration for soft constraints.
constexpr double kSoftYieldMinAccelInMpss = -2.0;

// Extra yield extra time buffer for consistent yield object.
constexpr double kYieldExtraTimeForConsistentYieldObjectInSec = 0.3;
// Extra yield extra distance buffer for consistent yield object.
constexpr double kYieldExtraDistanceForConsistentYieldObjectInMeter = 0.3;

// Obvious accel threshold for VRU cyclist, below this threshold will consider
// as obviously slow down
constexpr double kObviousSlowDownAccelForVRUCyclistInMpss = -2.0;
// Obvious accel threshold for VRU pedestrian, below this threshold will
// consider as obviously slow down
constexpr double kObviousSlowDownAccelForVRUPedestrianInMpss = -1.5;
constexpr double kComfortPassExtraTimeAdjustmentInSec = 0.25;
constexpr double kMinCreepDistanceAsCreepInMeter = 0.5;
constexpr double kYieldExtraDistanceForSafetyDuringCreepingInMeter = 0.5;

// Sets emergency brake for a constraint according to specific contexts. Returns
// true if emergency brake is set.
bool MaybeSetEmergencyBrake(const WorldContext& world_context,
                            const ReasoningObject& reasoning_object,
                            const pb::OverlapRegion& overlap_region,
                            ConstraintMutator& constraint_mutator) {
  if (reasoning_util::ShouldAllowEmergencyBrake(world_context,
                                                reasoning_object) ||
      reasoning_util::ShouldAllowEmergencyBrakeForVRU(
          world_context, reasoning_object, overlap_region,
          /*reasoner_in_charge=*/pb::ReasonerId::VRU)) {
    constraint_mutator.set_allow_emergency_brake();
    return true;
  }

  return false;
}

bool ShouldAllowMaxSpeed(const WorldContext& world_context,
                         const ReasoningObject& reasoning_object,
                         const bool is_in_creep_duration) {
  return !is_in_creep_duration && reasoning_util::ShouldAllowMaxSpeedForCyclist(
                                      world_context, reasoning_object);
}

// Returns true if the required lateral gap is reduced.
bool MaybeReducePassRequiredLateralGapForSlowCyclistAtCrosswalk(
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    ConstraintMutator& constraint_mutator) {
  if (reasoning_object.is_cyclist() &&
      reasoning_object.speed() < kSpeedThresholdOfSlowCyclistInMps &&
      (!reasoning_util::IsStrictCrossingOverlap(overlap_region))) {
    constraint_mutator.set_pass_required_lat_gap(
        {kComfortLateralGapForSlowCyclistInMeter,
         reasoning_object.CriticalRequiredLateralGap()});
    return true;
  }

  return false;
}

// Adjust pass required lateral gap only.
void AdjustPassRequiredLateralGap(const TrajectoryInfo& trajectory_info,
                                  const AgentPolicy& agent_policy,
                                  const bool is_in_creep_duration,
                                  ConstraintMutator& constraint_mutator) {
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  // As Ego should creep, the predicted trajectories of VRUs are uncertain, so
  // zero pass RLG is used.
  if (is_in_creep_duration) {
    constraint_mutator.set_pass_required_lat_gap(DiscomfortVarying(0.0));
    return;
  }
  // The path can only keep a critical required lateral gap to agent that Ego is
  // overtaking. In order to have same decision as path, the required lateral
  // gap is reduced.
  if (reasoning_object.is_ego_path_overtaking()) {
    constraint_mutator.set_pass_required_lat_gap(
        reasoning_util::ComputeComfortRequiredLateralGapBaseOnOverlapRegion(
            trajectory_info, reasoning_object, overlap_region));
    return;
  }

  // Cyclists waiting around crosswalks could stay closely to Ego lane, so we
  // should reduce required lateral for them.
  MaybeReducePassRequiredLateralGapForSlowCyclistAtCrosswalk(
      reasoning_object, overlap_region, constraint_mutator);

  if (agent_policy.overlap_region_info().is_cut_behind) {
    constraint_mutator.set_pass_required_lat_gap(DiscomfortVarying(0.0));
  }
}

// Returns the pass extra time according to specific context.
// TODO(hezhihang): There are many over-yielding issues. The logic of computing
// pass extra time should be tuned finely.
DiscomfortVarying ComputePassExtraTime(const ReasoningObject& reasoning_object,
                                       const pb::VruReasonerConfig& config,
                                       const bool does_vru_want_to_cut_behind,
                                       const bool is_in_creep_duration) {
  // As Ego should creep, the predicted trajectories of VRUs are uncertain, so
  // zero pass extra time is used.
  if (is_in_creep_duration) {
    return DiscomfortVarying(0.0);
  }

  const double comfort_pass_extra_time =
      config.pass_extra_time_in_sec().comfort();
  const double critical_pass_extra_time =
      config.pass_extra_time_in_sec().critical();

  // Adjust pass extra time based on ego decision.
  const double comfort_pass_extra_time_adjustment =
      reasoning_object.was_pass()
          ? -kComfortPassExtraTimeAdjustmentInSec
          : (reasoning_object.was_yield() ? kComfortPassExtraTimeAdjustmentInSec
                                          : 0.0);

  // If Ego has yielded the VRU, the buffers should not be adjusted in
  // order to avoid decision flicker, because it could make Ego to pass the
  // pedestrian due to reduce pass extra time.
  if (does_vru_want_to_cut_behind && !reasoning_object.was_yield()) {
    // The vru could cross Ego lane immediately after Ego passes, Ego
    // pass extra time is controlled completely by the vru. Thus, the
    // pass extra time at discomfort 1.0 is set to zero.
    // TODO(hezhihang): Consider to reduce pass required lateral gap like the
    // way of handling jaywalker.
    return DiscomfortVarying(
        {{0.0, comfort_pass_extra_time + comfort_pass_extra_time_adjustment},
         {0.5, critical_pass_extra_time},
         {1.0, 0.0}});
  }

  return DiscomfortVarying(
      comfort_pass_extra_time + comfort_pass_extra_time_adjustment,
      critical_pass_extra_time);
}

// Returns the yield extra time according to specific context.
DiscomfortVarying ComputeYieldExtraTime(const WorldContext& world_context,
                                        const AgentPolicy& agent_policy,
                                        const pb::VruReasonerConfig& config,
                                        const bool is_in_creep_duration) {
  constexpr double kYieldExtraTimeForSafetyDuringCreepingInSecond = 0.3;
  if (is_in_creep_duration) {
    return DiscomfortVarying(kYieldExtraTimeForSafetyDuringCreepingInSecond);
  }

  if (agent_policy.yield_always_possible()) {
    const double extra_yield_extra_time_for_yield_always_possible =
        reasoning_util::ComputeExtraYieldExtraTimeForYieldAlwaysPossible(
            world_context, agent_policy.OverlapRegion(),
            config.yield_extra_distance_in_meter().comfort());
    return DiscomfortVarying(config.yield_extra_time_in_sec().comfort() +
                             extra_yield_extra_time_for_yield_always_possible);
  }

  // If agent is consistent yield object, leave more yield buffer for safety.
  if (reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
          world_context, agent_policy.reasoning_object())) {
    return DiscomfortVarying(kYieldExtraTimeForConsistentYieldObjectInSec);
  }
  return DiscomfortVarying(0.0);
}

// Returns the yield extra distance according to specific context.
DiscomfortVarying ComputeYieldExtraDistance(
    const WorldContext& world_context, const AgentPolicy& agent_policy,
    const double comfort_yield_extra_distance,
    const double critical_yield_extra_distance, const bool is_in_creep_duration,
    std::string* debug_str_ptr) {
  if (is_in_creep_duration) {
    return DiscomfortVarying(kYieldExtraDistanceForSafetyDuringCreepingInMeter);
  }

  if (agent_policy.yield_always_possible()) {
    return DiscomfortVarying(comfort_yield_extra_distance);
  }

  // If agent is consistent yield object, leave more yield buffer for safety.
  if (reasoning_util::ShouldAddExtraBufferForConsistentYieldObject(
          world_context, agent_policy.reasoning_object())) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, " Domin risk obj, add yield dist;");
    }
    // To prevent yield paradox, extra yield buffer is only added at discomfort
    // levels of 0.75 and 1.0. Other discomfort levels maintain the same yield
    // extra distance as before.
    double intermediate_yield_extra_distance =
        (comfort_yield_extra_distance + critical_yield_extra_distance) * 0.5;
    double updated_critical_yield_extra_distance =
        critical_yield_extra_distance +
        kYieldExtraDistanceForConsistentYieldObjectInMeter;
    DCHECK_GT(intermediate_yield_extra_distance,
              updated_critical_yield_extra_distance);
    return DiscomfortVarying({{0.0, comfort_yield_extra_distance},
                              {0.5, intermediate_yield_extra_distance},
                              {1.0, updated_critical_yield_extra_distance}});
  }
  return DiscomfortVarying(comfort_yield_extra_distance,
                           critical_yield_extra_distance);
}

bool ShouldActivateHonkForVRUScenario(const WorldContext& world_context,
                                      const AgentPolicy& agent_policy,
                                      pb::VruHonkDebug* honk_debug) {
  constexpr int64_t kMinEgoSpeedToHonkToVRUInMps = 6.0;
  if (world_context.ego_speed() < kMinEgoSpeedToHonkToVRUInMps) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(pb::VruHonkDebug::EGO_SLOW_SPEED);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  const bool is_ego_lower_precedence =
      agent_policy.agent_trajectory_info().EgoHasLowerRoadPrecedence();
  if (is_ego_lower_precedence) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(pb::VruHonkDebug::EGO_LOW_PRECEDENCE);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  constexpr int64_t kMinActiveTimeForHonkSignalInMSec = 1000;
  constexpr int64_t kMaxActiveTimeForHonkSignalInMSec = 5000;
  const bool should_trigger_honk_in_temporal_context =
      agent_policy.OverlapRegion().contain_strict_overlap() &&
      math::IsInRange(
          agent_policy.OverlapRegion().start_strict_relative_time_in_msec(),
          kMinActiveTimeForHonkSignalInMSec, kMaxActiveTimeForHonkSignalInMSec);
  if (!should_trigger_honk_in_temporal_context) {
    if (honk_debug != nullptr) {
      honk_debug->set_disallowed_reason(
          pb::VruHonkDebug::OUT_OF_TEMPORAL_CONTEXT);
      honk_debug->set_should_trigger(false);
    }
    return false;
  }

  const bool is_agent_behind_ego_front_axle =
      agent_policy.reasoning_object()
          .object_proximity_info()
          .projected_ra_arc_length()
          .end() < world_context.ra_to_front_axle();
  if (is_agent_behind_ego_front_axle) {
    honk_debug->set_disallowed_reason(pb::VruHonkDebug::BEHIND_EGO);
    honk_debug->set_should_trigger(false);
    return false;
  }

  if (agent_policy.reasoning_object().IsStationary() &&
      !agent_policy.reasoning_object().has_limited_tracking_frames()) {
    honk_debug->set_disallowed_reason(pb::VruHonkDebug::STATIONARY_AGENT);
    honk_debug->set_should_trigger(false);
    return false;
  }

  if ((agent_policy.reasoning_object().is_cyclist() &&
       agent_policy.reasoning_object().acceleration() <
           kObviousSlowDownAccelForVRUCyclistInMpss) ||
      (agent_policy.reasoning_object().is_pedestrian() &&
       agent_policy.reasoning_object().acceleration() <
           kObviousSlowDownAccelForVRUPedestrianInMpss)) {
    honk_debug->set_disallowed_reason(pb::VruHonkDebug::OBVIOUSLY_SLOW_DOWN);
    honk_debug->set_should_trigger(false);
    return false;
  }

  if (honk_debug != nullptr) {
    honk_debug->set_disallowed_reason(pb::VruHonkDebug::NA);
    honk_debug->set_should_trigger(true);
  }
  return true;
}

// Returns true if Ego is turning right and the associated straight lane of this
// right-turn lane has read light.
// TODO(hezhihang): Move it to reasoning_util.
bool IsEgoUPRWithRedLightStraightLane(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const voy::TrafficLights& traffic_light_detection,
    const pnc_map::Lane& lane_intersecting_crosswalk) {
  if (lane_intersecting_crosswalk.type() != hdmap::Lane::VIRTUAL ||
      lane_intersecting_crosswalk.turn() != hdmap::Lane::RIGHT) {
    return false;
  }
  if (!traffic_rules::GetAssociatedTrafficSignals(joint_pnc_map_service,
                                                  lane_intersecting_crosswalk)
           .empty()) {
    // The right-turn lane is controlled by traffic signal.
    return false;
  }

  const voy::TrafficLight* straight_or_left_turn_traffic_light_ptr =
      traffic_rules::ComputeTrafficLightForUnprotectedRightTurn(
          joint_pnc_map_service, traffic_light_detection,
          lane_intersecting_crosswalk);
  if (straight_or_left_turn_traffic_light_ptr == nullptr) {
    return false;
  }

  if (straight_or_left_turn_traffic_light_ptr->color() !=
      voy::TrafficLight::RED) {
    return false;
  }

  return true;
}

// Returns slow down params as Ego creeps for safety constraint.
// TODO(hezhihang): move it into |nominal_pedestrian_slow_down_strategy.cpp|.
SlowDownParams ComputePassSlowDownParamsForYieldStrictConstraintAsCreep(
    const WorldContext& world_context, const AgentPolicy& agent_policy,
    const traffic_rules::CrosswalkInLaneSequence& crosswalk) {
  constexpr double kPedComfortAccelAsEgoCreepInMpss = -0.5;
  constexpr double kPedUncomfortAccelAsEgoCreepInMpss = -1.5;
  constexpr double kPedCriticalAccelAsEgoCreepInMpss = -3.0;
  constexpr double kCycComfortAccelAsEgoCreepInMpss = -1.0;
  constexpr double kCycUncomfortAccelAsEgoCreepInMpss = -2.0;
  // This critical acceleration is only used to very limited scope, i.e. the end
  // ego moving distance of the first strict overlap slice is less than the Ego
  // front bumper which is an very obvious cut-behind scene.
  constexpr double kCycCriticalAccelAsEgoCreepInMpss = -4.0;
  SlowDownParams slow_down_params;

  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const double comfort_accel = reasoning_object.is_pedestrian()
                                   ? kPedComfortAccelAsEgoCreepInMpss
                                   : kCycComfortAccelAsEgoCreepInMpss;
  const double uncomfort_accel = reasoning_object.is_pedestrian()
                                     ? kPedUncomfortAccelAsEgoCreepInMpss
                                     : kCycUncomfortAccelAsEgoCreepInMpss;

  double highest_discomfort_acceleration = uncomfort_accel;
  DCHECK(overlap_region.contain_strict_overlap());
  if (agent_policy.overlap_region_info().is_cut_behind) {
    highest_discomfort_acceleration = reasoning_object.is_pedestrian()
                                          ? kPedCriticalAccelAsEgoCreepInMpss
                                          : kCycCriticalAccelAsEgoCreepInMpss;
  }

  // TODO(hezhihang): update the config to avoid magical number.
  slow_down_params.pass.accel =
      DiscomfortVarying({{0.0, comfort_accel},
                         {0.5, uncomfort_accel},
                         {1.0, highest_discomfort_acceleration}});

  const pb::ObjectProximityInfo& object_proximity_info =
      agent_policy.reasoning_object().object_proximity_info();
  if (object_proximity_info.signed_lateral_gap() > 0.0 &&
      crosswalk.IsGreenLight() &&
      crosswalk.IsWithinCrosswalkLateralRange(
          object_proximity_info.signed_lateral_gap(),
          world_context.robot_state().GetWidth() * 0.5) &&
      IsEgoUPRWithRedLightStraightLane(
          *(world_context.joint_pnc_map_service()),
          *(world_context.traffic_light_detection()),
          *(crosswalk.lanes_intersecting_crosswalk.front()))) {
    slow_down_params.pass.max_speed_reduction =
        DiscomfortVarying({{0.0, reasoning_object.speed() * 0.5},
                           {0.25, reasoning_object.speed() * 0.5},
                           {0.5, reasoning_object.speed() * 0.7},
                           {1.0, std::numeric_limits<double>::infinity()}});
  } else {
    slow_down_params.pass.max_speed_reduction =
        DiscomfortVarying(std::numeric_limits<double>::infinity());
  }

  return slow_down_params;
}

// Returns true if Ego has crept an enough long distance to show intention.
bool DoesEgoCreepForLongEnoughDistance(const WorldContext& world_context) {
  constexpr int64_t kConsideredTimeDurationInMs = 1000;
  constexpr double kMinCreepDistanceToShowIntentionInMeter = 0.5;
  const auto& ego_states = world_context.ego_state_history().ego_states();
  double ego_moving_distance = 0.0;
  for (auto state_iter = ego_states.rbegin(); state_iter != ego_states.rend();
       state_iter++) {
    if (world_context.ego_pose_timestamp() - state_iter->timestamp >
        kConsideredTimeDurationInMs) {
      break;
    }

    const auto last_state_iter = std::next(state_iter);
    if (last_state_iter != ego_states.rend()) {
      const double average_v =
          (state_iter->speed + last_state_iter->speed) * 0.5;
      ego_moving_distance +=
          average_v *
          math::Ms2Sec(state_iter->timestamp - last_state_iter->timestamp);
    }
  }

  return ego_moving_distance > kMinCreepDistanceToShowIntentionInMeter;
}

// Returns true if VRUs show yield intention after Ego has crept an enough long
// distance to show intention.
bool DoesVRUWantToYieldToEgoAfterEgoCreepAnLongEnoughDistance(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const traffic_rules::CrosswalkInLaneSequence& crosswalk,
    const bool does_ego_creep_long_distance, std::string* debug_str_ptr) {
  constexpr double kLateralRangeToCheckVRUIntentionInMeter = 3.0;
  constexpr double kExtraLateralRangeAroundCrosswalkInMeter = 1.0;
  constexpr double kMaxLongitudinalArclengthToCheckVRUIntentionInMeter = 10.0;
  constexpr double kMinLongitudinalArclengthToCheckVRUIntentionInMeter = -10.0;
  constexpr double kPedLowSpeedInMps = 0.5;
  constexpr double kCycLowSpeedInMps = 0.8;
  constexpr double kPedSpeedReductionAsYieldingToEgoInMps = 0.4;
  constexpr double kCycSpeedReductionAsYieldingToEgoInMps = 0.7;
  constexpr int64_t kMinDecelerationDurationInMs = 1000;

  if (!does_ego_creep_long_distance) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "not creep an enough long dist; ");
    }
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double lateral_range =
      kLateralRangeToCheckVRUIntentionInMeter +
      (crosswalk.IsWithinCrosswalkLateralRange(
           reasoning_object.object_proximity_info().abs_lateral_gap(),
           /*half_ego_width=*/world_context.robot_state().GetWidth() * 0.5)
           ? kExtraLateralRangeAroundCrosswalkInMeter
           : 0.0);
  if (object_proximity_info.abs_lateral_gap() > lateral_range ||
      object_proximity_info.projected_ra_arc_length().start() >
          kMaxLongitudinalArclengthToCheckVRUIntentionInMeter ||
      object_proximity_info.projected_ra_arc_length().end() <
          kMinLongitudinalArclengthToCheckVRUIntentionInMeter) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "not in lat or long range; ");
    }
    return true;
  }

  // VRU has slowed down and keeps a low speed.
  // TODO(hezhihang): Unify it and the logic in
  // |nominal_pedestrian_reaction_time_strategy.cpp| after we moving the
  // function |ComputeReactionTimesForSafetyConstraint| into this file.
  const bool is_low_speed = reasoning_object.is_cyclist()
                                ? reasoning_object.speed() < kCycLowSpeedInMps
                                : reasoning_object.speed() < kPedLowSpeedInMps;
  if (is_low_speed) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "vru low speed; ");
    }
    return true;
  }

  const pb::AgentReactionTrackerInfo* agent_art_info_ptr =
      reasoning_object.art_object_info().has_value()
          ? reasoning_object.art_object_info()->agent_art_seed_ptr
          : nullptr;
  if (agent_art_info_ptr == nullptr) {
    return true;
  }

  const int64_t current_timestamp = world_context.ego_pose_timestamp();
  const auto agent_states = agent_art_info_ptr->agent_state_tracking();
  int64_t deceleration_start_timestamp = current_timestamp;
  double min_speed = std::numeric_limits<double>::infinity();
  double start_speed = std::numeric_limits<double>::infinity();
  for (auto agent_state_iter = agent_states.rbegin();
       agent_state_iter != agent_states.rend(); agent_state_iter++) {
    if (agent_state_iter->acceleration() > 0.0) {
      break;
    }

    math::UpdateMin(agent_state_iter->velocity(), min_speed);
    start_speed = agent_state_iter->velocity();
    deceleration_start_timestamp = agent_state_iter->timestamp();
  }

  if (std::isinf(start_speed)) {
    // Not decelerate at current timestamp.
    return false;
  }

  const double speed_reduction = reasoning_object.is_pedestrian()
                                     ? kPedSpeedReductionAsYieldingToEgoInMps
                                     : kCycSpeedReductionAsYieldingToEgoInMps;
  DCHECK_LE(min_speed, start_speed);
  if (current_timestamp - deceleration_start_timestamp >
          kMinDecelerationDurationInMs &&
      start_speed - min_speed > speed_reduction) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "vru decelerates; ");
    }
    return true;
  }

  return false;
}

// Computes pass reaction time as Ego creep for nominal constraint.
// TODO(hezhihang): Move this function into the file
// |nominal_pedestrian_reaction_time_strategy.cpp|.
ReactionTimes ComputePassReactionTimesForYieldStrictConstraintAsCreep(
    const WorldContext& world_context, const AgentPolicy& agent_policy,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const bool does_ego_creep_long_distance, std::string* debug_str_ptr) {
  ReactionTimes reaction_times;
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();

  const traffic_rules::CrosswalkInLaneSequence& crosswalk =
      crosswalk_used_by_agent.crosswalk;
  const int yield_zone_idx = crosswalk_used_by_agent.yield_zone_idx;
  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "\n[Creep Reaction Time] ");
  }

  DCHECK(overlap_region.contain_strict_overlap());
  // If Ego has crept for a while, we should observe VRUs' behavior VRU yielded
  // to Ego.
  if (DoesVRUWantToYieldToEgoAfterEgoCreepAnLongEnoughDistance(
          world_context, agent_policy.reasoning_object(), crosswalk,
          does_ego_creep_long_distance, debug_str_ptr)) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "set reaction time to 0.0; ");
    }

    reaction_times.pass = DiscomfortVarying(0.0);

    return reaction_times;
  }

  if (agent_policy.overlap_region_info().is_cut_behind) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, "cut-behind, set reaction time to 0.0; ");
    }

    reaction_times.pass = DiscomfortVarying(0.0);

    return reaction_times;
  }

  // Ego does not creep enough long duration, so we should assume VRU could
  // react to Ego so that Ego can start up.
  const PredictedTrajectoryWrapper& predicted_trajectory =
      agent_policy.agent_trajectory_info().predicted_trajectory();
  double start_strict_state_relative_time = 0.0;
  if (overlap_region.contain_strict_overlap()) {
    const int64_t start_strict_state_timestamp =
        predicted_trajectory
            .predicted_poses()[overlap_region.first_strict_agent_state_ix()]
            .timestamp();
    start_strict_state_relative_time =
        math::Ms2Sec(start_strict_state_timestamp -
                     predicted_trajectory.prediction_first_pose().timestamp());
  }

  // VRUs do not start to decelerate when they are far away Ego even if they
  // want to yield to Ego. They will delay the deceleration time until they can
  // slow down smoothly. Therefore, the following method use the time VRUs enter
  // ego path  minus certain buffer as reaction time.
  // The relative time that VRUs start to decelerate before they enter the Ego
  // path if they want to yield to Ego.
  constexpr double kComfortDecelerationTimeBeforeEnteringEgoPathInSecond = 2.0;
  constexpr double kCriticalDecelerationTimeBeforeEnteringEgoPathInSecond = 3.0;
  constexpr double kExtraDelayOfComfortDecelerationTimeInSecond = 0.4;
  constexpr double kComfortMinReactionTimeInSecond = 2.5;
  constexpr double kCriticalMinReactionTimeInSecond = 2.2;
  double comfort_deceleration_time_before_entering_ego_path =
      kComfortDecelerationTimeBeforeEnteringEgoPathInSecond;
  if (crosswalk.IsWithinCrosswalkLateralRange(
          object_proximity_info.signed_lateral_gap(),
          /*half_ego_width=*/world_context.robot_state().GetWidth() * 0.5) &&
      DoesRangeOverlapWithCrosswalk(
          crosswalk.yield_zone_infos[yield_zone_idx].ra_arclength_range,
          object_proximity_info.projected_ra_arc_length())) {
    comfort_deceleration_time_before_entering_ego_path -=
        kExtraDelayOfComfortDecelerationTimeInSecond;

    if (crosswalk.IsGreenLight()) {
      comfort_deceleration_time_before_entering_ego_path -=
          kExtraDelayOfComfortDecelerationTimeInSecond;
    }

    if (agent_policy.reasoning_object().is_pedestrian()) {
      comfort_deceleration_time_before_entering_ego_path -=
          kExtraDelayOfComfortDecelerationTimeInSecond;
    }
  }

  const double comfort_reaction_time =
      std::max(kComfortMinReactionTimeInSecond,
               start_strict_state_relative_time -
                   comfort_deceleration_time_before_entering_ego_path);
  const double critical_reaction_time =
      std::max(kCriticalMinReactionTimeInSecond,
               start_strict_state_relative_time -
                   kCriticalDecelerationTimeBeforeEnteringEgoPathInSecond);
  reaction_times.pass =
      DiscomfortVarying(comfort_reaction_time, critical_reaction_time);

  return reaction_times;
}

// Returns true if we should set YAP, i.e. set_hard_yield_with_min_accel, as Ego
// creeps in order to avoid yield paradox and unnecessary hard brake.
bool ShouldSetYieldAlwaysPossibleAsCreep(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region) {
  constexpr double kMinTimeEnteringEgoPathToAllowYAPForPedInSeconds = 1.0;
  constexpr double kMinTimeEnteringEgoPathToAllowYAPForPCycInSeconds = 3.0;
  constexpr double kMaxEgoSpeedAllowYAPInMps = 1.5;
  const double min_time_entering_ego_path =
      reasoning_object.is_pedestrian()
          ? kMinTimeEnteringEgoPathToAllowYAPForPedInSeconds
          : kMinTimeEnteringEgoPathToAllowYAPForPCycInSeconds;
  return world_context.ego_speed() < kMaxEgoSpeedAllowYAPInMps &&
         overlap_region.contain_strict_overlap() &&
         overlap_region.start_strict_relative_time_in_sec() >
             min_time_entering_ego_path;
}

// Returns the overlap region ids that required lateral gap varying overlap
// should be used.
std::unordered_set<std::string> SelectOverlapRegionToUseRLGVaryingOverlap(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerOutput>& agent_decisions) {
  constexpr uint64_t kMaxNumberOfOverlapUsingRLGVaryingState = 5;
  std::unordered_set<std::string> result;
  // When Ego has stopped, accurate overlap region is not very important.
  if (world_context.EgoAlmostStopped()) {
    return result;
  }

  const double ego_speed = world_context.ego_speed();
  // We could added multiple agent policy for one overlap region, so this set is
  // used to avoid duplication.
  std::unordered_set<std::string> overlap_region_ids;
  // An priority queue whose top element has maximum absolution time gap.
  std::priority_queue<std::pair<double, std::string>>
      overlap_region_ids_with_abs_time_gap;
  for (const PredictionDecisionMakerOutput& one_agent : agent_decisions) {
    for (const PredictionDecision& one_predicted_trajectory :
         one_agent.prediction_decisions()) {
      for (const AgentPolicy& agent_policy :
           one_predicted_trajectory.agent_policies()) {
        if (agent_policy.reasoner_in_charge() != pb::ReasonerId::VRU) {
          continue;
        }

        const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
        const std::string& overlap_region_id =
            GetOverlapRegionUniqueId(overlap_region);
        if (!overlap_region_ids.insert(overlap_region_id).second) {
          // This overlap region has been checked.
          continue;
        }

        const double ego_moving_distance =
            GetPaddedOverlapStart(overlap_region);
        const double agent_arriving_time =
            overlap_region.start_padded_relative_time_in_sec();
        const double abs_time_gap =
            std::abs(ego_moving_distance / ego_speed - agent_arriving_time);

        if (overlap_region_ids_with_abs_time_gap.size() <
            kMaxNumberOfOverlapUsingRLGVaryingState) {
          overlap_region_ids_with_abs_time_gap.emplace(abs_time_gap,
                                                       overlap_region_id);
        } else {
          std::pair<double, std::string> overlap_region_id_with_abs_time_gap(
              abs_time_gap, overlap_region_id);
          if (overlap_region_id_with_abs_time_gap <
              overlap_region_ids_with_abs_time_gap.top()) {
            overlap_region_ids_with_abs_time_gap.pop();
            overlap_region_ids_with_abs_time_gap.push(
                std::move(overlap_region_id_with_abs_time_gap));
          }
        }
      }
    }
  }

  DCHECK_LE(overlap_region_ids_with_abs_time_gap.size(),
            kMaxNumberOfOverlapUsingRLGVaryingState);
  while (!overlap_region_ids_with_abs_time_gap.empty()) {
    result.insert(std::move(overlap_region_ids_with_abs_time_gap.top().second));
    overlap_region_ids_with_abs_time_gap.pop();
  }

  DCHECK_LE(result.size(), kMaxNumberOfOverlapUsingRLGVaryingState);
  return result;
}

// Adds yield strict constraint for the yield-strict-with-soft constraint.
bool AddStrictPartOfYieldStrictWithSoftConstraint(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const pb::VruReasonerConfig& config, const std::string& constraint_group_id,
    const std::unordered_set<std::string>&
        overlap_regions_using_rlg_varying_state,
    const bool is_in_creep_duration, const bool does_ego_creep_long_distance,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::VruAgentPolicyDebug* agent_policy_debug) {
  std::string* debug_str_ptr =
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr;
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const std::string& overlap_region_id =
      GetOverlapRegionUniqueId(overlap_region);
  const std::string yield_strict_constraint_id =
      absl::StrCat(overlap_region_id, "-yield-strict");
  ConstraintMutator yield_strict_mutator =
      agent_policy.AddYieldStrictConstraint(
          pb::FenceType::kCrossWalkVRU, reasoning_object.required_lateral_gap(),
          yield_strict_constraint_id, constraint_creator_ptr,
          /*allow_zero_pass_rlg=*/false,
          /*use_rlg_varying_overlap=*/
          overlap_regions_using_rlg_varying_state.count(overlap_region_id) > 0);
  // NOTE: We set the required lateral gap to zero, which violates the
  // requirement that "system error should be less than required lateral gap" in
  // the speed pipeline.
  yield_strict_mutator.set_allow_ignoring_min_range_lat_gap();
  yield_strict_mutator.set_group_id(constraint_group_id);

  // PASS settings.
  const bool does_vru_want_to_cut_behind =
      agent_policy.overlap_region_info().is_cut_behind;

  AdjustPassRequiredLateralGap(trajectory_info, agent_policy,
                               is_in_creep_duration, yield_strict_mutator);
  yield_strict_mutator.set_pass_extra_time(
      ComputePassExtraTime(reasoning_object, config,
                           does_vru_want_to_cut_behind, is_in_creep_duration));

  // YIELD settings, different from the soft constraint.
  // During creep, we could set negative yield extra distance to avoid yield
  // paradox.
  yield_strict_mutator.set_yield_extra_distance(
      ComputeYieldExtraDistance(
          world_context, agent_policy,
          config.yield_extra_distance_for_yield_strict_in_meter().comfort(),
          config.yield_extra_distance_for_yield_strict_in_meter().critical(),
          is_in_creep_duration, debug_str_ptr),
      /*allow_negative_value=*/is_in_creep_duration);
  yield_strict_mutator.set_yield_extra_time(ComputeYieldExtraTime(
      world_context, agent_policy, config, is_in_creep_duration));

  // TODO(hezhihang): Refine the logic of whether using YAP as creep.
  if (is_in_creep_duration) {
    yield_strict_mutator.set_discomfort_for_progress(/*discomfort=*/0.25);
    yield_strict_mutator.set_pass_agent_reaction(
        ComputePassReactionTimesForYieldStrictConstraintAsCreep(
            world_context, agent_policy, crosswalk_used_by_agent,
            does_ego_creep_long_distance, debug_str_ptr),
        ComputePassSlowDownParamsForYieldStrictConstraintAsCreep(
            world_context, agent_policy, crosswalk_used_by_agent.crosswalk));
    yield_strict_mutator.set_min_creep_distance(
        kMinCreepDistanceAsCreepInMeter);
    if (ShouldSetYieldAlwaysPossibleAsCreep(world_context, reasoning_object,
                                            overlap_region)) {
      yield_strict_mutator.set_hard_yield_with_min_accel(
          kSoftYieldMinAccelInMpss);
      yield_strict_mutator.set_yield_agent_reaction(ReactionTimes(),
                                                    SlowDownParams());
    }
  } else if (agent_policy.yield_always_possible()) {
    yield_strict_mutator.set_hard_yield_with_min_accel(
        kSoftYieldMinAccelInMpss);
    yield_strict_mutator.set_yield_agent_reaction(ReactionTimes(),
                                                  SlowDownParams());
  }

  MaybeSetEmergencyBrake(world_context, reasoning_object, overlap_region,
                         yield_strict_mutator);

  if (ShouldAllowMaxSpeed(world_context, reasoning_object,
                          is_in_creep_duration)) {
    yield_strict_mutator.set_allow_max_speed();
  }

  if (ShouldActivateHonkForVRUScenario(
          world_context, agent_policy,
          agent_policy_debug != nullptr
              ? agent_policy_debug->mutable_honk_debug()
              : nullptr)) {
    // The minimum active discomfort level for honk signal in yield scenario.
    constexpr double kMinActiveDiscomfortForHonkSignal = 0.5;
    constexpr int64_t kHonkingHornTimeIntervalInMSec = 3000;  // ms
    if (honk_request_ptr != nullptr) {
      honk_request_ptr->add_honk_constraint(
          yield_strict_constraint_id, kMinActiveDiscomfortForHonkSignal,
          kHonkingHornTimeIntervalInMSec,
          ::planner::pb::HonkScene::VRU_CONSTRAINT);
    }
  }

  return true;
}

// Returns true if the yield extra distance should be adjusted based on the
// crosswalk matched with the agent.
bool ShouldComputeYieldLocationBaseOnCrosswalk(
    const TrajectoryInfo& trajectory_info,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const double overlap_min_arclength, const double crosswalk_yield_line,
    const double ego_front_bumper_ra_arclength, std::string* debug_str_ptr) {
  if (crosswalk_used_by_agent.motion_type ==
          OverlapMotionTypeWithRespectToCrosswalk::kAlongRoadSide ||
      crosswalk_used_by_agent.motion_type ==
          OverlapMotionTypeWithRespectToCrosswalk::kCrossingOut) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr,
                      " vru walk along road side or cross out,"
                      " no yield extra distance adjustment.");
    }
    return false;
  }

  if (ego_front_bumper_ra_arclength > crosswalk_yield_line) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr,
                      " Ego beyond crosswalk yield line,"
                      " no yield extra distance adjustment.");
    }
    return false;
  }

  if (overlap_min_arclength <= crosswalk_yield_line) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr,
                      " agent before crosswalk,"
                      " no yield extra distance adjustment.");
    }
    return false;
  }

  const std::vector<const pnc_map::Lane*>& lanes_intersecting_crosswalk =
      crosswalk_used_by_agent.crosswalk.lanes_intersecting_crosswalk;
  DCHECK(!lanes_intersecting_crosswalk.empty());
  const traffic_rules::TrafficLightInfo* controlled_ego_lane_traffic_light =
      traffic_rules::MaybeGetTrafficLightInfoPtrByLaneId(
          trajectory_info.traffic_rules().traffic_lights,
          lanes_intersecting_crosswalk.front()->id());
  if (controlled_ego_lane_traffic_light != nullptr &&
      controlled_ego_lane_traffic_light->is_green(/*is_sustain=*/true)) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, " Ego lane green; ");
    }
    return false;
  }

  return true;
}

// Returns true if Ego should speed up quickly once this agent passes during
// creep.
bool IsVRUEgoShouldCutBehindDuringCreep(
    const UrgencyInfo& urgency_info,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const pb::OverlapRegion& overlap_region) {
  constexpr int64_t kSuitableGapMaxStartTimeInMs = 2000;
  constexpr int64_t kSuitableGapMinLengthInMs = 700;
  const CreepTimeGapInfo& crosswalk_gap_info =
      urgency_info.GetCrosswalkTimeGapInfo(
          crosswalk_used_by_agent.crosswalk.crosswalk_ptr->id());
  if (std::none_of(crosswalk_gap_info.gaps().begin(),
                   crosswalk_gap_info.gaps().end(), [](const auto& gap) {
                     return gap.first < kSuitableGapMaxStartTimeInMs &&
                            gap.second - gap.first > kSuitableGapMinLengthInMs;
                   })) {
    return false;
  }

  return overlap_region.contain_strict_overlap() &&
         overlap_region.end_strict_relative_time_in_msec() <
             kSuitableGapMaxStartTimeInMs;
}

// Returns the yield extra distance for soft constraints. The yield extra
// distance could be adjusted base on yield line of crosswalk.
DiscomfortVarying ComputeSoftConstraintYieldExtraDistance(
    const WorldContext& world_context, const UrgencyInfo& urgency_info,
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const pb::VruReasonerConfig& config,
    const double ego_front_bumper_ra_arclength, const bool is_in_creep_duration,
    std::string* debug_str_ptr) {
  // TODO(hezhihang): This is only for keep cr noop. We should adjust the yield
  // extra distance to make better yield behavior.
  if (is_in_creep_duration) {
    if (IsEgoUPRWithRedLightStraightLane(
            *(world_context.joint_pnc_map_service()),
            *(world_context.traffic_light_detection()),
            *(crosswalk_used_by_agent.crosswalk.lanes_intersecting_crosswalk
                  .front())) &&
        !IsVRUEgoShouldCutBehindDuringCreep(
            urgency_info, crosswalk_used_by_agent, overlap_region)) {
      return DiscomfortVarying(
          config.yield_extra_distance_in_meter().comfort());
    }

    return DiscomfortVarying(kYieldExtraDistanceForSafetyDuringCreepingInMeter);
  }

  const int matched_yield_zone_idx = crosswalk_used_by_agent.yield_zone_idx;
  const traffic_rules::YieldZoneInfo& yield_zone_info =
      crosswalk_used_by_agent.crosswalk
          .yield_zone_infos[matched_yield_zone_idx];
  const double crosswalk_yield_line =
      traffic_rules::GetCrosswalkYieldLine(yield_zone_info.yield_line);
  const double overlap_yield_min_arclength =
      overlap_region.contain_strict_overlap()
          ? GetStrictOverlapStart(overlap_region)
          : GetPaddedOverlapStart(overlap_region);
  const double overlap_yield_min_arclength_with_offset =
      overlap_yield_min_arclength + ego_front_bumper_ra_arclength;
  if (ShouldComputeYieldLocationBaseOnCrosswalk(
          trajectory_info, crosswalk_used_by_agent,
          overlap_yield_min_arclength_with_offset, crosswalk_yield_line,
          ego_front_bumper_ra_arclength, debug_str_ptr)) {
    DCHECK_GE(overlap_yield_min_arclength_with_offset, crosswalk_yield_line);
    return DiscomfortVarying(overlap_yield_min_arclength_with_offset -
                             crosswalk_yield_line);
  }

  return DiscomfortVarying(config.yield_extra_distance_in_meter().comfort());
}

// Adds soft constraint for the nominal-with-soft constraint.
bool AddSoftPartOfYieldStrictWithSoftConstraint(
    const WorldContext& world_context, const UrgencyInfo& urgency_info,
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const pb::VruReasonerConfig& config, const std::string& constraint_group_id,
    const bool is_in_creep_duration, ConstraintCreator* constraint_creator_ptr,
    std::string* debug_str_ptr) {
  constexpr double kSoftConstraintYieldExtraTimeAsCreepInSecond = 0.3;
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const std::string soft_constraint_id =
      absl::StrCat(GetOverlapRegionUniqueId(overlap_region), "-soft");
  ConstraintMutator soft_mutator = agent_policy.AddConstraint(
      pb::FenceType::kCrossWalkVRU, reasoning_object.required_lateral_gap(),
      soft_constraint_id, constraint_creator_ptr);
  soft_mutator.set_soft_yield_with_min_a(kSoftYieldMinAccelInMpss);
  soft_mutator.set_group_id(constraint_group_id);

  // The soft constraint is set as pass_alway_possible and is grouped with the
  // nominal constraint, so if the nominal constraint has PASS decision, the
  // soft constraint will have PASS decision also.
  soft_mutator.set_pass_always_possible();

  // YIELD settings, different from the grouped strict constraint.
  if (is_in_creep_duration) {
    // TODO(hezhihang): This is only for keep cr noop. We should adjust the
    // yield extra distance to make better yield behavior.
    soft_mutator.set_yield_required_lat_gap(DiscomfortVarying(0.0));
    soft_mutator.set_allow_ignoring_min_range_lat_gap();
  } else {
    soft_mutator.set_yield_required_lat_gap(
        DiscomfortVarying(reasoning_object.ComfortRequiredLateralGap()));
  }
  soft_mutator.set_yield_extra_distance(ComputeSoftConstraintYieldExtraDistance(
      world_context, urgency_info, trajectory_info, overlap_region,
      crosswalk_used_by_agent, config, world_context.ra_to_leading_bumper(),
      is_in_creep_duration, debug_str_ptr));
  soft_mutator.set_yield_extra_time(DiscomfortVarying(
      (is_in_creep_duration ? kSoftConstraintYieldExtraTimeAsCreepInSecond
                            : config.yield_extra_time_in_sec().comfort())));
  // SOFT yield does not support agent reaction
  soft_mutator.set_yield_agent_reaction(ReactionTimes(), SlowDownParams());

  if (is_in_creep_duration) {
    soft_mutator.set_min_creep_distance(kMinCreepDistanceAsCreepInMeter);
  }

  return true;
}

// Adds two constraints, yield strict constraint and soft constraint. The yield
// strict constraint has zero yield required lateral. The soft constraint has
// much larger yield required lateral gap to make Ego slow down.
bool AddYieldStrictWithSoftConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    const pb::VruReasonerConfig& config,
    const OverlappedCrosswalkInfo& crosswalk_used_by_agent,
    const std::unordered_set<std::string>&
        overlap_regions_using_rlg_varying_state,
    const bool does_ego_creep_long_distance,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::VruAgentPolicyDebug* agent_policy_debug) {
  bool constraint_added = false;
  const std::string constraint_group_id =
      absl::StrCat(GetOverlapRegionUniqueId(agent_policy.OverlapRegion()),
                   "-strict&soft-yield");

  const bool is_in_creep_duration =
      urgency_info.ShouldProceedUrgentlyAroundCrosswalk(
          crosswalk_used_by_agent.crosswalk.crosswalk_ptr->id());
  constraint_added |= AddStrictPartOfYieldStrictWithSoftConstraint(
      world_context, trajectory_info, agent_policy, crosswalk_used_by_agent,
      config, constraint_group_id, overlap_regions_using_rlg_varying_state,
      /*is_in_creep_duration=*/is_in_creep_duration,
      /*does_ego_creep_long_distance=*/does_ego_creep_long_distance,
      constraint_creator_ptr, honk_request_ptr, agent_policy_debug);
  constraint_added |= AddSoftPartOfYieldStrictWithSoftConstraint(
      world_context, urgency_info, trajectory_info, agent_policy,
      crosswalk_used_by_agent, config, constraint_group_id,
      is_in_creep_duration, constraint_creator_ptr,
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr);

  return constraint_added;
}

// Adds constraints when discomfort for progress has been triggered. Returns
// true if any constraint is added.
bool AddCreepingConstraintForProgress(
    const WorldContext& world_context, const AgentPolicy& agent_policy,
    const pb::VruReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr) {
  constexpr double kEgoCreepingSpeedInMps = 1.0;
  constexpr double kEgoCreepingSpeedForVRUsBehindEgoFrontBumperInMps = 3.0;
  constexpr double kMinAccelerationDuringCreepingInMpss = -1.0;
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();

  const std::string constraint_id =
      GetOverlapRegionUniqueId(agent_policy.OverlapRegion());
  // Sets parameters of the soft constraint for creeping around crosswalk with
  // dense VRUs.
  const bool start_behind_front_axle =
      reasoning_util::IsStrictOverlapRegionStartingBehind(
          overlap_region, world_context.ra_to_front_axle() -
                              world_context.ra_to_leading_bumper());
  const double ego_creeping_speed =
      start_behind_front_axle
          ? kEgoCreepingSpeedForVRUsBehindEgoFrontBumperInMps
          : kEgoCreepingSpeedInMps;
  ConstraintMutator creeping_constraint_mutator =
      agent_policy.AddConstraintForCreeping(
          pb::FenceType::kCrossWalkVRU, reasoning_object.required_lateral_gap(),
          absl::StrCat(constraint_id, "-crosswalk-creep"), ego_creeping_speed,
          /*yield_min_a=*/kMinAccelerationDuringCreepingInMpss,
          /*use_new_creep_api=*/true, constraint_creator_ptr);
  creeping_constraint_mutator.set_pass_required_lat_gap(
      DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap()));
  creeping_constraint_mutator.set_pass_extra_time(
      DiscomfortVarying(config.pass_extra_time_in_sec().critical()));
  creeping_constraint_mutator.set_yield_extra_time(
      DiscomfortVarying(config.yield_extra_time_in_sec().critical()));
  creeping_constraint_mutator.set_yield_extra_distance(
      DiscomfortVarying(config.yield_extra_distance_in_meter().comfort()));

  rt_event::PostRtEvent<rt_event::planner::CrosswalkCreeping>();

  return true;
}

}  // namespace

bool VruReasoner::Reason(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info,
    const std::vector<PredictionDecisionMakerOutput>& agent_decisions,
    const pb::VruReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    speed::pb::ReasoningDebug* debug_proto_ptr) {
  pb::VruReasonerDebug* vru_reasoner_debug_ptr =
      debug_proto_ptr
          ? debug_proto_ptr->mutable_reasoner_debug()->mutable_vru_reasoner()
          : nullptr;

  bool constraint_added = false;

  const bool does_ego_creep_long_distance =
      urgency_info.HasUrgencyReason(
          planner::pb::UrgencyReason::CrosswalkWithDenseVRU) &&
      DoesEgoCreepForLongEnoughDistance(world_context);

  const std::unordered_set<std::string>
      overlap_regions_using_rlg_varying_state =
          SelectOverlapRegionToUseRLGVaryingOverlap(world_context,
                                                    agent_decisions);

  for (const PredictionDecisionMakerOutput& agent_decision : agent_decisions) {
    if (!agent_decision.HasAnyAgentPolicyHandledByGivenReasoner(id())) {
      continue;
    }

    // The agent-level debug info.
    pb::VruReasonerAgentDebug* agent_debug =
        vru_reasoner_debug_ptr != nullptr
            ? vru_reasoner_debug_ptr->add_agent_debugs()
            : nullptr;
    if (agent_debug != nullptr) {
      agent_debug->set_object_id(agent_decision.reasoning_object().id());
    }

    for (const PredictionDecision& prediction_decision :
         agent_decision.prediction_decisions()) {
      // The trajectory-level debug info.
      pb::VruTrajectoryDebug* trajectory_debug =
          agent_debug != nullptr ? agent_debug->add_agent_trajectories()
                                 : nullptr;
      if (trajectory_debug != nullptr) {
        trajectory_debug->set_trajectory_id(
            prediction_decision.trajectory_id());
      }

      constraint_added |= AddConstraintForPrediction(
          world_context, trajectory_info, urgency_info, prediction_decision,
          config, overlap_regions_using_rlg_varying_state,
          does_ego_creep_long_distance, constraint_creator_ptr,
          honk_request_ptr, trajectory_debug);
    }
  }

  return constraint_added;
}

bool VruReasoner::AddConstraintForPrediction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info,
    const PredictionDecision& prediction_decision,
    const pb::VruReasonerConfig& config,
    const std::unordered_set<std::string>&
        overlap_regions_using_rlg_varying_state,
    const bool does_ego_creep_long_distance,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::VruTrajectoryDebug* trajectory_debug) {
  if (prediction_decision.Ignore()) {
    DCHECK(prediction_decision.agent_policies().empty());
    return false;
  }

  bool constraint_added = false;
  for (const AgentPolicy& agent_policy : prediction_decision.agent_policies()) {
    // The agentpolicy-level debug info.
    pb::VruAgentPolicyDebug* agent_policy_debug =
        trajectory_debug != nullptr ? trajectory_debug->add_agent_policies()
                                    : nullptr;

    constraint_added |= AddConstraintForPolicy(
        world_context, trajectory_info, urgency_info, agent_policy, config,
        overlap_regions_using_rlg_varying_state, does_ego_creep_long_distance,
        constraint_creator_ptr, honk_request_ptr, agent_policy_debug);
  }
  return constraint_added;
}

bool VruReasoner::AddConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const UrgencyInfo& urgency_info, const AgentPolicy& agent_policy,
    const pb::VruReasonerConfig& config,
    const std::unordered_set<std::string>&
        overlap_regions_using_rlg_varying_state,
    const bool does_ego_creep_long_distance,
    ConstraintCreator* constraint_creator_ptr, HonkRequest* honk_request_ptr,
    pb::VruAgentPolicyDebug* agent_policy_debug) {
  if (agent_policy.reasoner_in_charge() != pb::ReasonerId::VRU) {
    return false;
  }

  if (agent_policy_debug != nullptr) {
    agent_policy_debug->set_overlap_region_id(
        agent_policy.OverlapRegion().region_id());
  }
  std::string* debug_str_ptr =
      agent_policy_debug != nullptr
          ? agent_policy_debug->mutable_policy_debug_str()
          : nullptr;

  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const std::string constraint_id = GetOverlapRegionUniqueId(overlap_region);
  if (debug_str_ptr != nullptr) {
    absl::StrAppend(debug_str_ptr, "\n", constraint_id, ":");
  }
  // For objects out of extended path range, VRUReasoner will add a basic
  // nominal constraint as fallback.
  if (reasoning_object.out_of_extended_path_range()) {
    if (debug_str_ptr != nullptr) {
      absl::StrAppend(debug_str_ptr, " out of extened path range.");
    }
    ConstraintMutator constraint_mutator = agent_policy.AddConstraint(
        pb::FenceType::kCrossWalkVRU, reasoning_object.required_lateral_gap(),
        constraint_id, constraint_creator_ptr);
    MaybeSetEmergencyBrake(world_context, reasoning_object, overlap_region,
                           constraint_mutator);

    return true;
  }

  bool constraint_added = false;
  // One overlap region could be matched with multiple crosswalks. We want to
  // only add one constraint for a overlap region, so the nearest matched
  // crosswalk is used computed some parameters related to crosswalk, like
  // standoff.
  const OverlappedCrosswalkInfo* crosswalk_used_by_agent =
      agent_policy.overlap_region_info().GetNearestMatchedCrosswalk(
          world_context.ra_to_trailing_bumper_shift());
  if (crosswalk_used_by_agent == nullptr) {
    DCHECK(false) << "Must have a matched crosswalk.";
    return constraint_added;
  }

  if (agent_policy.generative_constraint_type() ==
      pb::GenerativeConstraintType::CREEP) {
    constraint_added = AddCreepingConstraintForProgress(
        world_context, agent_policy, config, constraint_creator_ptr);
  } else {
    DCHECK(agent_policy.generative_constraint_type() ==
           pb::GenerativeConstraintType::YIELD_STRICT_WITH_SOFT);
    constraint_added = AddYieldStrictWithSoftConstraintForPolicy(
        world_context, trajectory_info, urgency_info, agent_policy, config,
        *crosswalk_used_by_agent, overlap_regions_using_rlg_varying_state,
        does_ego_creep_long_distance, constraint_creator_ptr, honk_request_ptr,
        agent_policy_debug);
  }

  return constraint_added;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
