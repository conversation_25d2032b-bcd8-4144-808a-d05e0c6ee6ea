/**
 * @file acu_orin_pciesw.h
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-26
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __ACU_ORIN_PCIESW_H__
#define __ACU_ORIN_PCIESW_H__
#include <stdalign.h>
#include <stdint.h>

#include <string>
#include <unordered_map>
#include <vector>

#include "acu_dv_protocal.hpp"
#include "acu_mt_protocal.hpp"
#include "gpiod_interface.hpp"
#include "serial_port.hpp"

namespace voyager {
namespace hardware {
struct OrinPcieswBwSt {
  float eg0_bw_;
  float in0_bw_;
  float eg1_bw_;
  float in1_bw_;
  float eg2_bw_;
  float in2_bw_;
};
struct OrinPcieswLinkSt {
  uint32_t link0_;
  uint32_t link1_;
  uint32_t link2_;
};
struct OrinPcieswDevinfoSt {
  uint8_t image_phase_; // 1:BL1, 2:BL2, 3:Main Fw
  uint8_t major_hw_rev_; // 0:Rev B, 1:Rev A
  uint8_t switchtec_generation_; // 0:Gen 4, 1:Gen 5
  uint16_t chip_id_;
  uint32_t fw_ver_;
};
struct OrinPcieswCrcSt {
  std::string map0_;
  std::string map1_;
  std::string key0_;
  std::string key1_;
  std::string bl20;
  std::string bl21;
  std::string data0_;
  std::string data1_;
  std::string image0_;
  std::string image1_;
};

class AcuOrinPciesw {
 public:
  //("/dev/ttyTHS1", 230400, 0, 0, 0, false);
  AcuOrinPciesw(const SerialCfgSt &cfg) : serial_(cfg) {}
  AcuOrinPciesw(const AcuOrinPciesw &) = delete;
  AcuOrinPciesw &operator=(const AcuOrinPciesw &) = delete;
  virtual ~AcuOrinPciesw() {}

  bool dv_init(void *arg);
  void dv_deinit();
  bool get_dv_msg(AcuDvOrinBaseInfoUn &info);
  std::string print_dv_msg(const AcuDvOrinBaseInfoUn &info);
  int test_dv_msg(int repeat = 10, uint16_t ms = 1000);
  std::string print_dv_crc_msg();
  std::string get_dv_hversion_str();
  int test_dv_crc_msg();

  int mt_init(void *arg);
  void mt_deinit();
  bool get_mt_msg(AcuMtPcieswSt &info);
  std::string print_mt_msg(const AcuMtPcieswSt &info);
  int test_mt_msg(int repeat = 10, uint16_t ms = 1000);

 private:
  int get_orin_pciesw_temp(void *temp);
  int get_orin_pciesw_link(void *link);
  int get_orin_pciesw_bw(void *bw);
  int get_orin_pciesw_hversion(void *hversion);
  // int get_orin_pciesw_dev(void *devinfo);
  int get_orin_pciesw_crc();
  int get_orin_pciesw_temp_data(std::vector<std::string> &strs, int *temp);
  int get_orin_pciesw_link_data(std::vector<std::string> &strs,
                                OrinPcieswLinkSt *link);
  int get_orin_pciesw_bw_data(std::vector<std::string> &strs,
                              OrinPcieswBwSt *bw);
  int get_orin_pciesw_crc_data(std::vector<std::string> &strs);
  int get_orin_pciesw_hversion_data(std::vector<std::string> &strs,
                                    OrinPcieswDevinfoSt *hversion);

 private:
  SerialPort serial_;
  struct gpiod_chip *chip_ = nullptr;
  struct gpiod_line *line_ = nullptr;
  OrinPcieswCrcSt crc_;
  // static std::unordered_map<std::string, std::vector<std::string>>
  // pciesw_maps_;
};
}  // namespace hardware
}  // namespace voyager

#endif
