"""map dataset generation"""
import copy
from datetime import <PERSON><PERSON><PERSON>
import json

from airflow.exceptions import AirflowFailException
from airflow.exceptions import AirflowSkipException
from airflow.operators.python import PythonOperator
from airflow.sensors.python import PythonSensor
from airflow.utils.task_group import TaskGroup
from airflow.operators.trigger_dagrun import Tri<PERSON><PERSON>agRunOperator
from airflow.models.dagrun import Dag<PERSON>un, Dag<PERSON>unState

from dag_generation.constants import TaskType
from dag_generation.tasks.abstract_task import AbstractTask
from dag_generation.tasks.task_factory import register
from dag_generation.tasks.map_pipeline.checkpoint import Checkpoint


_GROUP_NAME = TaskType.MAP_DATASET_GENERATION.value
_DEFAULT_PARAMS = {
    "binary_id": "LKGR",
    "max_trip_segs_for_mapping": "2",
    "s3_dataset_key_root": "online_map",
    "filter_out_unsynced_samples": "false",
    "allow_unfinished_labeling_task": "false",
    "monolinize_dual_line_fence": "true",
    "upload_sensor_data": "true",
    "overwrite_sensor_data": "false",
    "test_flag": "false",
    "skip_dg_mapping_process": "true",
}
_MAP_DATASET_GENERATOR_DAG_ID = "online_map_dataset_generator"


@register(TaskType.MAP_DATASET_GENERATION)
class MapDatasetGenerationTask(AbstractTask):
    """map dataset generation"""

    def _is_skip(self, **context):
        dataset = context["dag_run"].conf.get("dataset")
        if not dataset:
            raise AirflowSkipException

    def _build_config(self, **context):
        user_name = context["dag_run"].conf.get("user_name")
        dataset = context["dag_run"].conf.get("dataset")
        batch_name = dataset["batch_name"]
        run_ids = dataset["run_ids"]
        configs = []
        for run_id in run_ids:
            conf = copy.deepcopy(_DEFAULT_PARAMS)
            conf["online_map_ltp_dag_run_id"] = run_id
            conf["batch_name"] = batch_name
            conf["username"] = user_name
            configs.append(conf)

        context["ti"].xcom_push(key="config", value=configs)
        context["ti"].xcom_push(key="run_ids", value=run_ids)

        output = {"run_ids": json.dumps(run_ids)}
        context["ti"].xcom_push(key="output", value=output)
        return configs

    def _wait_pipeline(self, **context):
        all_done = True
        run_ids = context["ti"].xcom_pull(
            key="trigger_run_id", task_ids=f"{_GROUP_NAME}.trigger_pipeline"
        )
        for run_id in run_ids:
            dag_runs = DagRun.find(
                dag_id=_MAP_DATASET_GENERATOR_DAG_ID, run_id=str(run_id)
            )
            if dag_runs and dag_runs[0].state == DagRunState.SUCCESS:
                continue
            elif dag_runs and dag_runs[0].state == DagRunState.FAILED:
                failures = f"dataset generation failed. run_id:{run_id}"
                context["ti"].xcom_push(key="failures", value=failures)
                raise AirflowFailException(failures)
            else:
                all_done = False
                break
        return all_done

    def gen_airflow_ops(self, dag, *args, **kwargs):
        with TaskGroup(_GROUP_NAME, dag=dag) as tg:
            is_skip_op = PythonOperator(
                task_id="is_skip",
                dag=dag,
                python_callable=self._is_skip,
                provide_context=True,
            )
            build_config_op = PythonOperator(
                task_id="build_config",
                dag=dag,
                python_callable=self._build_config,
                provide_context=True,
            )

            tirgger_pipeline = TriggerDagRunOperator.partial(
                task_id="trigger_pipeline",
                dag=dag,
                trigger_dag_id=_MAP_DATASET_GENERATOR_DAG_ID,
            ).expand(conf=build_config_op.output)

            wait_pipeline = PythonSensor(
                task_id="wait_pipeline",
                dag=dag,
                python_callable=self._wait_pipeline,
                poke_interval=300,
                mode="reschedule",
                timeout=3600 * 24 * 2,
                retry_delay=timedelta(seconds=60),
            )

            checkpoint_op = Checkpoint(_GROUP_NAME).op(dag)
            (
                is_skip_op
                >> build_config_op
                >> tirgger_pipeline
                >> wait_pipeline
                >> checkpoint_op
            )
            return tg
