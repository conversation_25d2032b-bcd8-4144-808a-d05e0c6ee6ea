#include "routing/node/state/state_manager.h"

#include <memory>
#include <optional>
#include <queue>
#include <set>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include <absl/strings/str_format.h>
#include <glog/logging.h>
#include "gtl/map_util.h"

#include "av_comm/mode_config.h"
#include "base/optional_value_accessor.h"
#include "geometry/algorithms/distance.h"
#include "hdmap/lib/geometry_util.h"
#include "hdmap/lib/point_util.h"
#include "proto_util/proto_compare.h"
#include "routing/engine/engine_utils.h"
#include "routing/node/defines.h"
#include "routing/node/routing_flags.h"
#include "routing/node/state/abnormal_idle_state.h"
#include "routing/node/state/cancelling_state.h"
#include "routing/node/state/completing_state.h"
#include "routing/node/state/idle_state.h"
#include "routing/node/state/ontrip_proposing_state.h"
#include "routing/node/state/ontrip_pullover_state.h"
#include "routing/node/state/ontrip_state.h"
#include "routing/node/state/proposed_static_state.h"
#include "routing/node/state/proposing_state.h"
#include "routing/node/state/state.h"
#include "routing/node/state/utils.h"
#include "routing/node/utils.h"
#include "routing/utility/hdmap_route_util.h"
#include "routing/utility/routing_param_util.h"
#include "routing/utility/rt_event_util.h"
#include "routing_protos/routing_debug.pb.h"
#include "routing_protos/state/state.pb.h"

namespace routing {
namespace state {
namespace {
constexpr int64_t kRoutingSeedPublishInterval = 500;       // In milliseconds.
constexpr int64_t kRouteStatusPublishInterval = 500;       // In milliseconds.
constexpr double kMaxSameDirectionRoadHeadingDiff = 0.52;  // unit: rad.
constexpr double kMinSameDirectionRoadHeadingDiff = 5.76;  // unit: rad.
// Max linear distance between origin and dest to consider they are "close".
constexpr double kMaxLinearDistanceForCloseOriginAndDestinationInMeter = 100.0;
constexpr double
    kMaxLinearDistanceSquareForCloseOriginAndDestinationInSquareMeter = 10000.0;
// Max accumulated arc length distance to trigger immediate pullover
constexpr double kMaxArcLengthToTriggerImmediatePullOverInMeter = 120.0;
// When triggered immediate (by any source), max distance to cancel if planner
// does not response.
constexpr double kMaxDistanceToTerminateImmediatePullOverInMeter = 100.0;
// When check if pose in within a cell, use this buffer to check.
constexpr double kPoseBufferToCheckIfPoseInCell = 0.1;
// The distance to destination threshold to publish route completion
// confirmation, and let order service complete current trip.
constexpr double kDistToDestThresholdToPublishRouteCompletionInMeter = 50.0;
// The max search distance to find related road.
constexpr int64_t kMaxSearchDistanceToFindRelatedRoadInMeter = 100.0;

// Returns true if two road's heading diff smaller than 30 degree.
bool IsRoadHeadingsApproximatelyOnSameDirection(
    const hdmap::HdMap& hdmap, const hdmap::Point& origin_pose,
    const hdmap::Point& destination_pose, int64_t origin_road_id,
    int64_t destination_road_id) {
  double origin_road_heading = 0.0;
  if (!hdmap.GetRoadHeading(origin_road_id, origin_pose,
                            &origin_road_heading)) {
    return false;
  }
  double destination_road_heading = 0.0;
  if (!hdmap.GetRoadHeading(destination_road_id, destination_pose,
                            &destination_road_heading)) {
    return false;
  }

  const double road_heading_diff =
      std::fabs(origin_road_heading - destination_road_heading);

  return road_heading_diff <= kMaxSameDirectionRoadHeadingDiff ||
         road_heading_diff >= kMinSameDirectionRoadHeadingDiff;
}

bool IsPoseInStopCell(const hdmap::HdMap& hdmap, double pose_x, double pose_y) {
  hdmap::StopCellQueryInfo stop_cell_query_info;
  const std::vector<const hdmap::StopCellEntity*> stop_cell_entities =
      hdmap.QueryStopCellEntitiesByRange(
          /*range_bottom_left_point_x=*/(pose_x -
                                         kPoseBufferToCheckIfPoseInCell),
          /*range_bottom_left_point_y=*/
          (pose_y - kPoseBufferToCheckIfPoseInCell),
          /*range_top_right_x=*/(pose_x + kPoseBufferToCheckIfPoseInCell),
          /*range_top_right_y=*/(pose_y + kPoseBufferToCheckIfPoseInCell),
          stop_cell_query_info);
  if (stop_cell_entities.empty()) {
    return false;
  }

  return std::any_of(stop_cell_entities.begin(), stop_cell_entities.end(),
                     [](const auto stop_cell) {
                       return stop_cell->parking_info().type() ==
                              hdmap::CellParkingInfo::ALLOW;
                     });
}

// Checks if immediate pullover related info changes (includes state and
// pullover point). If any changes, we should populate status to order service.
bool IsImmediatePullOverChanged(
    const pb::ImmediatePullOverInfo& current_immediate_pullover_info,
    const pb::ImmediatePullOverInfo& new_immediate_pullover_info) {
  const bool is_state_change_to_fail =
      new_immediate_pullover_info.immediate_pullover_state() ==
          pb::ImmediatePullOverState::kFailed &&
      current_immediate_pullover_info.immediate_pullover_state() !=
          pb::ImmediatePullOverState::kFailed;
  if (is_state_change_to_fail) {
    return true;
  }

  bool is_immediate_pullover_point_changed = false;
  if (current_immediate_pullover_info.has_immediate_pullover_point() &&
      new_immediate_pullover_info.has_immediate_pullover_point()) {
    is_immediate_pullover_point_changed = !proto_util::Equals(
        new_immediate_pullover_info.immediate_pullover_point(),
        current_immediate_pullover_info.immediate_pullover_point());
  } else {
    // Immediate pull over point is changed if one route status has point and
    // the other does not have.
    is_immediate_pullover_point_changed =
        current_immediate_pullover_info.has_immediate_pullover_point() ||
        new_immediate_pullover_info.has_immediate_pullover_point();
  }
  return is_immediate_pullover_point_changed;
}

// Checks if can reach origin road from dest road within max search distance.
bool CanReachOriginWithinMaxSearchDistance(const voy::Point2d& origin,
                                           const voy::Point2d& destination,
                                           int64_t origin_road_id,
                                           int64_t dest_road_id,
                                           int64_t max_search_distance,
                                           const hdmap::HdMap& hdmap) {
  // Breadth first search along all successors until the first origin road gets
  // found.
  std::queue<std::pair</*road_id=*/int64_t, /*searched_distance=*/double>>
      queue_of_roads;
  std::unordered_set<int64_t> visited_road_ids;
  queue_of_roads.push({/*road_id=*/dest_road_id, /*searched_distance=*/0.0});
  visited_road_ids.insert(dest_road_id);
  while (!queue_of_roads.empty()) {
    const auto road_with_search_distance = queue_of_roads.front();
    const hdmap::Road* road =
        hdmap.GetRoadPtrById(road_with_search_distance.first);
    queue_of_roads.pop();
    if (road == nullptr) {
      continue;
    }

    const hdmap::Polyline& road_reference_line = road->reference_line();
    if (road_reference_line.points().empty()) {
      continue;
    }

    // Calculate distance from dest position to origin position along road
    // reference curve.
    // 1. Calculate current road length. If dest road is found, current road
    // length is distance from origin position to road end.
    double curr_road_length =
        road->id() == dest_road_id
            ? hdmap::geometry_util::DistanceToEnd2D(
                  hdmap::point_util::BuildHdmapPoint(
                      destination.x(), destination.y(), /*z=*/0.0),
                  road_reference_line)
            : road_reference_line.length();
    // 2. If current road is the origin road, minus distance from origin
    // position to road end.
    if (road->id() == origin_road_id) {
      curr_road_length -= hdmap::geometry_util::DistanceToEnd2D(
          hdmap::point_util::BuildHdmapPoint(origin.x(), origin.y(), /*z=*/0.0),
          road_reference_line);
    }
    // 3. Get the total searched length.
    const double total_length =
        road_with_search_distance.second + curr_road_length;

    // Returns true if origin road is found and total length is smaller than max
    // search distance.
    if (road->id() == origin_road_id && total_length < max_search_distance) {
      return true;
    }
    if (total_length > max_search_distance) {
      continue;
    }

    for (const auto succeeding_road_id : road->successors()) {
      // Skip visited roads.
      if (visited_road_ids.find(succeeding_road_id) != visited_road_ids.end()) {
        continue;
      }
      const auto insert_result = visited_road_ids.insert(succeeding_road_id);
      if (!insert_result.second) {
        continue;
      }
      queue_of_roads.push({/*road_id=*/succeeding_road_id,
                           /*searched_distance=*/total_length});
    }
  }
  return false;
}

// For first item, returns true if origin and destination are close to each
// other (linear distance < 100) and locate on same direction. As a result,
// origin ahead of destination, or destination ahead of origin are both
// possible. For second item, returns true if ego position (origin) is in cell
// or on rightmost lane.
std::pair</*is_origin_and_dest_close_and_on_same_direction=*/bool,
          /*is_ego_in_cell=*/bool>
AreOriginAndDestCloseAndOnSameDirection(const pb::RoutePoint& origin,
                                        const pb::RoutePoint& destination,
                                        RoutingMap* routing_map) {
  // 1. If linear distance between origin and destination exceeds 100m, we
  // should not trigger immediate pullover.
  if (math::geometry::ComparableDistance(origin.position(),
                                         destination.position()) >
      kMaxLinearDistanceSquareForCloseOriginAndDestinationInSquareMeter) {
    return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
            /*is_ego_in_cell=*/false};
  }

  // 2. Find origin and destination's lane candidates. If no candidates or
  // origin is in junction or not rightmost lane or not in cell, return false.
  pb::RoutePoint local_origin = origin;
  if (local_origin.lane_candidates().empty()) {
    utility::ValidateAndFillLaneCandidatesForOrigin(
        /*speed_for_path_point_meter_per_sec=*/16.67,  // won't be used.
        routing_map, &local_origin);
  }

  if (local_origin.lane_candidates().empty()) {
    return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
            /*is_ego_in_cell=*/false};
  }

  const hdmap::LaneInfo& origin_lane_info = routing_map->hdmap().GetLaneInfo(
      local_origin.lane_candidates()[0].lane_id());

  if (origin_lane_info.lane->is_in_junction()) {
    return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
            /*is_ego_in_cell=*/false};
  }

  pb::RoutePoint local_destination = destination;
  if (local_destination.lane_candidates().empty()) {
    utility::ValidateAndFillLaneCandidatesForDestination(
        /*expanded_target_lane_cost=*/0.0,              // won't be used.
        /*rightmost_expanded_target_lane_cost=*/0.0,    // won't be used.
        /*speed_for_path_point_meters_per_sec=*/16.67,  // won't be used.
        routing_map, &local_destination);
  }
  if (local_destination.lane_candidates().empty()) {
    return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
            /*is_ego_in_cell=*/false};
  }
  const hdmap::LaneInfo& destination_lane_info =
      routing_map->hdmap().GetLaneInfo(
          local_destination.lane_candidates()[0].lane_id());

  const bool is_pose_in_cell =
      IsPoseInStopCell(routing_map->hdmap(), local_origin.position().x(),
                       local_origin.position().y());

  LOG(INFO) << "For same origin and dest, is pose in cell: " << is_pose_in_cell;

  // 3. Check if can reach the origin road from dest road within max search
  // distance.
  if (!CanReachOriginWithinMaxSearchDistance(
          origin.position(), destination.position(), origin_lane_info.road_id,
          destination_lane_info.road_id,
          kMaxSearchDistanceToFindRelatedRoadInMeter, routing_map->hdmap())) {
    return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
            /*is_ego_in_cell=*/is_pose_in_cell};
  }

  // 4. Check if destination and origin locate on opposite side of road. If
  // locate on opposite side, return false.
  const auto& origin_pt = hdmap::point_util::BuildHdmapPoint(
      local_origin.position().x(), local_origin.position().y());
  const auto& destination_pt = hdmap::point_util::BuildHdmapPoint(
      local_destination.position().x(), local_destination.position().y());
  if (IsRoadHeadingsApproximatelyOnSameDirection(
          routing_map->hdmap(), origin_pt, destination_pt,
          origin_lane_info.road_id, destination_lane_info.road_id)) {
    LOG(INFO) << "For same origin and dest, road heading approximately on same "
                 "direction.";
    return {/*is_origin_and_dest_close_and_on_same_direction=*/true,
            /*is_ego_in_cell=*/is_pose_in_cell};
  }

  return {/*is_origin_and_dest_close_and_on_same_direction=*/false,
          /*is_ego_in_cell=*/is_pose_in_cell};
}

// TODO(Junfeng): Add proposing_route_info_ to state.
// Gets an instance of state from a proto.
std::unique_ptr<State> RecoverStateFromPB(const pb::StateState& state_pb) {
  const pb::MissionState mission_state = state_pb.state();
  switch (mission_state) {
    case pb::MissionState::kIdle: {
      return std::make_unique<IdleState>();
    }
    case pb::MissionState::kAbnormalIdle: {
      return std::make_unique<AbnormalIdleState>();
    }
    case pb::MissionState::kProposing: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR) << "The route info is expected for proposing state, but get "
                      "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      std::unique_ptr<ProposingState> state =
          std::make_unique<ProposingState>(route_info);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kOntrip: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR) << "The route info is expected for ontrip state, but get "
                      "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      auto state = std::make_unique<OnTripState>(route_info, std::nullopt);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kOntripProposing: {
      if (!state_pb.has_route_info() || !state_pb.has_proposed_route_info()) {
        LOG(ERROR) << "The route info and proposed route info are expected for "
                      "ontrip proposing state, but get a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      RouteInfo proposed_route_info;
      proposed_route_info.LoadState(state_pb.proposed_route_info());
      auto state = std::make_unique<OnTripProposingState>(route_info,
                                                          proposed_route_info);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kOntripPullover: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR)
            << "The route info is expected for ontrip-pullover state, but get "
               "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      auto state =
          std::make_unique<OntripPulloverState>(route_info, std::nullopt);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kProposedStatic: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR)
            << "The route info is expected for proposed-static state, but get "
               "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      auto state = std::make_unique<ProposedStaticState>(route_info);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kCancelling: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR)
            << "The route info is expected for cancelling state, but get "
               "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      auto state = std::make_unique<CancellingState>(route_info);
      state->LoadState(state_pb);
      return state;
    }
    case pb::MissionState::kCompleting: {
      if (!state_pb.has_route_info()) {
        LOG(ERROR)
            << "The route info is expected for completing state, but get "
               "a null one.";
        return nullptr;
      }
      RouteInfo route_info;
      route_info.LoadState(state_pb.route_info());
      auto state = std::make_unique<CompletingState>(route_info);
      state->LoadState(state_pb);
      return state;
    }
    default:
      LOG(ERROR) << "Load mission state error, unkown state, state="
                 << state_pb.state();
      return nullptr;
  }
}

// Checks if ego is on trip.
inline bool IsOnTrip(pb::MissionState state) {
  return state == pb::MissionState::kOntrip ||
         state == pb::MissionState::kProposedStatic ||
         state == pb::MissionState::kOntripPullover ||
         state == pb::MissionState::kOntripProposing;
}

}  // namespace

StateManager::StateManager(RoutingEngine* routing_engine,
                           RoutingMap* routing_map,
                           const EgoCarStatus* ego_car_status,
                           RoutePublisher* route_publisher, bool publish_seed)
    : state_(std::make_unique<IdleState>()),
      routing_engine_(routing_engine),
      routing_map_(routing_map),
      ego_car_status_(DCHECK_NOTNULL(ego_car_status)),
      route_publisher_(DCHECK_NOTNULL(route_publisher)),
      query_manager_(
          std::make_unique<QueryManager>(routing_engine_, ego_car_status_)),
      publish_seed_(publish_seed) {
  const pb::ConstraintDefinitions& constraint_definitions =
      routing_engine_->routing_map()->edge_filter()->constraint_definitions();
  route_status_manager_ = std::make_unique<RouteStatusManager>(
      routing_engine_, ego_car_status, constraint_definitions);
  routing_seed_.mutable_constraint_definitions()->CopyFrom(
      constraint_definitions);
}

void StateManager::DumpState(pb::StateManagerState* to_pb) const {
  if (!to_pb) {
    return;
  }
  if (state_) {
    state_->DumpState(to_pb->mutable_state());
  }
  if (query_manager_) {
    query_manager_->DumpState(to_pb->mutable_query_manager());
  }
  if (route_status_manager_) {
    route_status_manager_->DumpState(to_pb->mutable_route_status_manager());
  }

  to_pb->set_publish_seed(publish_seed_);
  to_pb->set_last_seed_publish_time(last_seed_publish_time_);
  to_pb->mutable_routing_seed()->CopyFrom(routing_seed_);

  to_pb->set_last_route_status_publish_time(last_route_status_publish_time_);
  to_pb->set_should_publish_route_status(should_publish_route_status_);

  if (distance_to_ride_start_when_start_immediate_pullover_.has_value()) {
    to_pb->set_distance_to_ride_start_when_start_immediate_pullover(
        distance_to_ride_start_when_start_immediate_pullover_.value());
  }

  to_pb->set_performed_a_route_search(performed_a_route_search_);
  to_pb->set_complete_route_for_static_same_origin_and_dest_route(
      complete_route_for_static_same_origin_and_dest_route_);
}

void StateManager::LoadState(const pb::StateManagerState& from_pb) {
  state_ = RecoverStateFromPB(from_pb.state());
  if (query_manager_) {
    query_manager_->LoadState(from_pb.query_manager());
  }
  if (route_status_manager_) {
    route_status_manager_->LoadState(from_pb.route_status_manager());
  }

  publish_seed_ = from_pb.publish_seed();
  last_seed_publish_time_ = from_pb.last_seed_publish_time();
  routing_seed_.CopyFrom(from_pb.routing_seed());
  last_route_status_publish_time_ = from_pb.last_route_status_publish_time();
  should_publish_route_status_ = from_pb.should_publish_route_status();
  if (from_pb.has_distance_to_ride_start_when_start_immediate_pullover()) {
    distance_to_ride_start_when_start_immediate_pullover_ =
        from_pb.distance_to_ride_start_when_start_immediate_pullover();
  }

  performed_a_route_search_ = from_pb.performed_a_route_search();
  complete_route_for_static_same_origin_and_dest_route_ =
      from_pb.complete_route_for_static_same_origin_and_dest_route();
}

void StateManager::ChangeState(std::unique_ptr<State> state) {
  state_ = std::move(state);
  LOG(INFO) << "Current state: " << pb::MissionState_Name(state_->state());
  UpdateInternalStateInRouteStatus();
}

void StateManager::OnPoseReceived(const voy::Pose& pose) {
  route_status_manager_->UpdatePose(pose);
  if (state_) {
    state_->OnPoseReceived(pose, this);
  }
  if (IsItTimeToPublishAnotherRouteStatus()) {
    should_publish_route_status_ = true;
  }
  UpdateRoutingSeed(pose);
}

void StateManager::OnRouteQueryReceived(
    const pb::RouteQuery& route_query, pb::SourceOfQuery source_of_query,
    pb::CommandPurpose command_purpose,
    const std::optional<pb::RouteInfo>& cloud_route_info) {
  if (!state::utils::ValidateQuery(route_query, *query_manager_,
                                   *ego_car_status_,
                                   rejected_route_query_mission_id_, this)) {
    return;
  }
  LOG(INFO) << node_utils::GetRouteMessageLogString(
      "Routing StateManager received a not duplicated route query",
      route_query);

  if (!node_utils::IsReplanningQuery(route_query)) {
    utility::AddRtEventForRouteCommand(
        route_query, pb::CommandType::ROUTE_RIDE_QUERY, command_purpose);
  }

  // If order service changes query from PICKUP to DROPOFF, and ego is during
  // pullover, we should not calculate route.
  if (state_->state() == pb::MissionState::kOntripPullover) {
    if (command_purpose == pb::CommandPurpose::kSwitchToDropoffRoute) {
      UpdateSpecialPlannerResponseRequest(pb::kCompletePullOverSooner);
      LOG(INFO) << node_utils::GetRouteMessageLogString(
          "In Ontrip-Pullover state, query changes from PICKUP to DROPOFF, and "
          "we did not calculate route",
          route_query);
      return;
    }

    if (command_purpose ==
        pb::CommandPurpose::kPassengerChangesDestinationDuringDropOff) {
      LOG(INFO) << node_utils::GetRouteMessageLogString(
          "In Ontrip-Pullover state, passenger changes destination during "
          "dropoff route, just reject order's query",
          route_query);
      RejectOrderRouteQuery(route_query);
      return;
    }
  }

  if (node_utils::IsReplanningQuery(route_query)) {
    // Replanning query should be always during the trip.
    // 1. In simulation, when we segment the trip, it is likely to have
    // replanning route before routing seed OR route command, we should ignore
    // it and seed will recover the query.
    if (!IsOnTrip(state_->state())) {
      LOG(WARNING)
          << "Replanning query is ignored, since it is received before "
             "seed and route query.";
      return;
    }
    // 2. If it is currently waiting for planning route acceptance, skip
    // this replanning query. It will continue processing the next replanning
    // query.
    if (state_->state() == pb::MissionState::kOntripProposing ||
        static_cast<OnTripState*>(state_.get())->proposing_route_info() ||
        static_cast<OntripPulloverState*>(state_.get())
            ->proposing_route_info() ||
        static_cast<ProposedStaticState*>(state_.get())
            ->proposing_route_info()) {
      LOG(WARNING)
          << "If it is currently waiting for planning route acceptance,"
             " this replanning query is skipped.";
      return;
    }
    // 3. If query manager is not ready, skip this replanning query.
    if (!query_manager_->IsReadyForReplanningQuery(route_query)) {
      LOG(WARNING) << node_utils::GetRouteMessageLogString(
          "Routing received a replan query but QueryManager not prepare",
          route_query);
      utility::AddRtEventForRouteStatusManagerHasIllegalRejectingPath();
      return;
    }
  }

  pb::RouteQuery local_route_query = route_query;
  if (!local_route_query.has_routing_param()) {
    *local_route_query.mutable_routing_param() =
        routing_engine_->routing_map()->default_routing_param();
  }

  if (FLAGS_sim_enable_dynamic_rerouting) {
    local_route_query.set_is_wide_cost_map(true);
    local_route_query.clear_predefined_route_roads();
  }

  // TODO(Wenyue): Changes to use pb::CommandPurpose::kSwitchToDropoffRoute.
  const bool switch_to_drop_off_route =
      query_manager_->SwitchToDropOffRoute(local_route_query);

  // Note: After calling this, query manager update local held route_ride_query
  // or replan_query.
  const RouteQueryInfo route_query_info = query_manager_->HandleRouteQuery(
      local_route_query, source_of_query, command_purpose, cloud_route_info,
      route_status_manager_.get());
  LOG(INFO) << node_utils::GetRouteMessageLogString(
                   "Successfully obtained the route query info",
                   route_query_info)
            << ", route from "
            << (route_query_info.IsRideQuery() ? "ride query"
                                               : "replanning query");

  bool should_trigger_immediate_pullover = false;
  bool is_ego_static_and_in_cell_or_rightmost_lane = false;
  // We check if trigger immediate pullover when:
  // 1. Query is not predefined route.
  // 2. Query has filled origin and destination.
  // 3. Query is pick up route.
  if (ego_car_status_->IsEgoCarReady() &&
      local_route_query.predefined_route_roads().empty() &&
      route_query_info.IsRideQuery() &&
      route_query_info.original_ride_route_query.query_type() ==
          pb::RouteQueryType::ORIGIN_AND_DESTINATION &&
      route_query_info.original_ride_route_query.match_type() ==
          order::pb::MatchType::PICKUP_POINT) {
    const std::pair</*is_origin_and_dest_close_and_on_same_direction=*/bool,
                    /*is_ego_in_cell=*/bool>
        origin_and_dest_check_result = AreOriginAndDestCloseAndOnSameDirection(
            route_query_info.original_ride_route_query.origin(),
            route_query_info.original_ride_route_query.destination(),
            routing_engine_->routing_map());
    const bool is_ego_static = ego_car_status_->IsEgoInStaticState();
    LOG(INFO) << "Ego is static: " << is_ego_static;
    is_ego_static_and_in_cell_or_rightmost_lane =
        origin_and_dest_check_result.second && is_ego_static;

    if (origin_and_dest_check_result.first) {
      pb::RouteSolution route_solution_with_max_search_distance =
          FLAGS_enable_multiple_global_routes
              ? CHECK_NOTNULL(routing_engine_)
                    ->FindRoute(base::CheckAndGetValue(
                                    route_query_info.current_route_query),
                                kMaxArcLengthToTriggerImmediatePullOverInMeter)
              : CHECK_NOTNULL(routing_engine_)
                    ->FindOptimalRoute(
                        base::CheckAndGetValue(
                            route_query_info.current_route_query),
                        /*edge_filter_param=*/std::nullopt,
                        kMaxArcLengthToTriggerImmediatePullOverInMeter);
      // When astar search path failed, clear infeasible cost map.
      if (!route_solution_with_max_search_distance.success()) {
        route_solution_with_max_search_distance.clear_cost_map();
      }
      // If fail to find a path, or able to find a path, but the path's total
      // distance is larger than 120, then this is a loop route and we should
      // trigger immediate pullover.
      if (!route_solution_with_max_search_distance.success() ||
          (route_solution_with_max_search_distance.success() &&
           route_solution_with_max_search_distance.total_dist_m() >
               kMaxLinearDistanceForCloseOriginAndDestinationInMeter)) {
        const auto origin_pose =
            route_query_info.original_ride_route_query.origin().position();
        const auto destination_pose =
            route_query_info.original_ride_route_query.destination().position();
        LOG(INFO)
            << "Receives a query that should trigger immediate pullover "
               "because query's origin just exceeds destination. origin is: ("
            << origin_pose.x() << ", " << origin_pose.y()
            << "), destination is: (" << destination_pose.x() << ", "
            << destination_pose.y() << ").";

        utility::AddRtEventForSameOriginAndDest(route_query, origin_pose,
                                                destination_pose);
        if (FLAGS_enable_same_origin_and_dest_immediate_pullover) {
          LOG(INFO) << "Trigger same origin and dest immediate pullover.";
          should_trigger_immediate_pullover = true;
        } else {
          LOG(INFO) << "Not trigger same origin and dest immediate pullover.";
        }
      }
    }
  }

  // Add rt-event if ops switches to DROP_OFF route when ego is static and
  // during pullover.
  if (switch_to_drop_off_route && ego_car_status_->IsEgoInStaticState() &&
      ego_car_status_->IsEgoInPullOver()) {
    rt_event::PostRtEvent<
        rt_event::routing::AbnormalOrdSwitchToDropOffWhenStaticAndPullOver>(
        "Switches to DROPOFF from PICKUP when ego is static and during "
        "pullover.");
  }

  // For pick up static same origin and destination immediate pull over case,
  // directly send route completion even if route is fail to calculate.
  if (should_trigger_immediate_pullover &&
      is_ego_static_and_in_cell_or_rightmost_lane &&
      command_purpose == pb::CommandPurpose::kNewRouteForPickUp) {
    complete_route_for_static_same_origin_and_dest_route_ = true;
    utility::AddRtEventForStaticSameOriginAndDest(route_query);

    // Note here we add both |route_query| and |route_query_info| as input,
    // since normal completion command does not have |route_query_info|.
    state_->OnRouteCompletionReceived(
        route_query, route_query_info, route_query.mission_id(),
        RouteCompletionSource::kRouting,
        pb::CommandPurpose::kRoutingSkippedRouteCompletion,
        /*is_during_immediate_pullover=*/false, this);
    return;
  }

  const bool query_has_valid_route_solution =
      state_->OnQueryReceived(route_query_info, this);
  // Add rt event for valid solution of new route.
  if (query_has_valid_route_solution &&
      !node_utils::IsReplanningQuery(route_query)) {
    utility::AddRtEventForRoutingSuccessForNewRoute(route_query,
                                                    command_purpose);
  }
  // Only trigger immediate pullover if query can calculate route solution.
  if (should_trigger_immediate_pullover && query_has_valid_route_solution) {
    OnImmediatePullOverRequestReceived(
        pb::ImmediatePullOverRequestType::kImmediatePullOver,
        pb::ImmediatePullOverSource::kRouting,
        /*receive_new_ride_route_query=*/false);
  }
  performed_a_route_search_ = true;
}

void StateManager::OnRouteRequestReceived(
    const hdmap::RouteRequest& route_request,
    pb::SourceOfQuery source_of_query) {
  if (query_manager_->IsDuplicateRouteRequest(route_request)) {
    return;
  }

  const pb::RouteQuery route_query = routing::utility::ConvertToRouteQuery(
      route_request, /*constraint_name=*/"");
  return OnRouteQueryReceived(
      route_query, source_of_query,
      /*command_purpose=*/pb::CommandPurpose::kOtherPurpose);
}

void StateManager::OnRouteCancellationReceived(
    const std::optional<pb::RouteQuery>& route_query) {
  utility::AddRtEventForRouteCommand(route_query,
                                     pb::CommandType::ROUTE_CANCELLATION,
                                     pb::CommandPurpose::kOtherPurpose);
  state_->OnRouteCancellationReceived(route_query, this);
}

void StateManager::OnRouteCompletionReceived(
    const std::optional<pb::RouteQuery>& route_query,
    const std::optional<RouteQueryInfo>& route_query_info,
    const std::optional<int64_t>& completion_mission_id,
    const RouteCompletionSource completion_source,
    pb::CommandPurpose command_purpose) {
  const pb::ImmediatePullOverInfo& immediate_pullover_info =
      route_status_manager_->current_route_status().immediate_pullover_info();
  const bool is_during_immediate_pullover =
      immediate_pullover_info.immediate_pullover_state() ==
      pb::ImmediatePullOverState::kTriggered;
  utility::AddRtEventForRouteCommand(
      route_query, pb::CommandType::ROUTE_COMPLETION, command_purpose);

  if (immediate_pullover_info.immediate_pullover_source() ==
          pb::ImmediatePullOverSource::kRouting &&
      route_query != std::nullopt) {
    utility::AddRtEventForSameOriginAndDestImmediatePullover(
        route_query.value());
  }
  state_->OnRouteCompletionReceived(
      route_query, route_query_info, completion_mission_id, completion_source,
      command_purpose, is_during_immediate_pullover, this);
}

// TODO(Wenyue): Fill in the logic after receive message from order node.
void StateManager::OnPulloutConfirmReceived() {
  state_->OnPulloutConfirmReceived(this);
}

void StateManager::OnPlanningLaneSequenceReceived(
    const planner::pb::PlanningLaneSequence& lane_sequence) {
  if (route_status_manager_) {
    const std::optional<pb::RoutePoint> route_point =
        ego_car_status_->GetEgoOrigin();
    const int32_t waypoint_count =
        route_status_manager_->current_route_status().waypoint_count();
    const pb::ImmediatePullOverInfo& current_immediate_pullover_info =
        route_status_manager_->current_route_status().immediate_pullover_info();
    if (route_point && route_status_manager_->UpdateLaneSequence(
                           lane_sequence, *route_point)) {
      const pb::ImmediatePullOverInfo& new_immediate_pullover_info =
          route_status_manager_->current_route_status()
              .immediate_pullover_info();
      // If immediate pull over state or point changes, we force to publish
      // route status.
      const bool is_immediate_pull_over_changed = IsImmediatePullOverChanged(
          current_immediate_pullover_info, new_immediate_pullover_info);
      const bool force_publish =
          route_status_manager_->current_route_status().waypoint_count() !=
              waypoint_count ||
          is_immediate_pull_over_changed;
      if (force_publish || IsItTimeToPublishAnotherRouteStatus()) {
        should_publish_route_status_ = true;
      }
      state_->OnLaneSequenceReceived(lane_sequence, this);
    }
  }
}

void StateManager::OnPlanningRouteStateReceived(
    const pb::PlanningRouteState& planning_route_state) {
  state_->OnPlanningRouteStateReceived(planning_route_state, this);
}

void StateManager::OnRoutingSeedReceived(const pb::RoutingSeed& routing_seed) {
  // Cancel current route before processing seed query.
  InternalCancelTrip();

  RouteQueryInfo route_query_info = query_manager_->LoadFromSeed(routing_seed);
  if (!route_query_info.current_route_query) {
    return;
  }

  if (FLAGS_sim_enable_dynamic_rerouting) {
    route_query_info.current_route_query->set_is_wide_cost_map(true);
  }

  state_->OnQueryReceived(route_query_info, this);
  performed_a_route_search_ = true;
}

// TODO(Wenyue): current immediate pullover has multiple sources, and we have
// not set priority yet.
void StateManager::OnImmediatePullOverRequestReceived(
    const pb::ImmediatePullOverRequestType request_type,
    const pb::ImmediatePullOverSource immediate_pull_over_source,
    bool receive_new_ride_route_query) {
  const pb::ImmediatePullOverInfo& immediate_pullover_info =
      route_status_manager_->current_route_status().immediate_pullover_info();
  const pb::ImmediatePullOverState current_state =
      immediate_pullover_info.immediate_pullover_state();
  const pb::ImmediatePullOverSource current_source =
      immediate_pullover_info.immediate_pullover_source();
  const pb::ImmediatePullOverState current_planner_state =
      immediate_pullover_info.planner_immediate_pullover_state();

  std::stringstream debug_info;
  switch (request_type) {
    case pb::ImmediatePullOverRequestType::kImmediatePullOver:
      debug_info << "Received trigger immediate pullover request, source is: "
                 << immediate_pull_over_source;
      rt_event::PostRtEvent<rt_event::routing::ImmediatePullOverTriggered>(
          debug_info.str());
      LOG(INFO) << debug_info.str();
      // When changes source, currently we do not recalculate accumulate
      // distance.
      if (current_state == pb::ImmediatePullOverState::kTriggered &&
          immediate_pull_over_source != current_source) {
        LOG(INFO) << "Received another request when immediate is triggered "
                     "from other source, current source is "
                  << current_source
                  << ", and new source is: " << immediate_pull_over_source;
      } else {
        distance_to_ride_start_when_start_immediate_pullover_ =
            (ego_car_status_ != nullptr)
                ? ego_car_status_->GetEgoPassedDistanceFromRideStart()
                : std::nullopt;
      }
      route_status_manager_->UpdateImmediatePullOverStateToTriggered(
          immediate_pull_over_source);
      return;

    case pb::ImmediatePullOverRequestType::kCancel:
      debug_info << "Received cancel immediate pullover request, source is: "
                 << immediate_pull_over_source;
      rt_event::PostRtEvent<rt_event::routing::ImmediatePullOverCancelledTime>(
          debug_info.str());
      LOG(INFO) << debug_info.str();
      if (current_state != pb::ImmediatePullOverState::kTriggered) {
        LOG(INFO) << "Received a Cancel request when immediate is not "
                     "triggered, whose source is: "
                  << immediate_pull_over_source;
      }
      route_status_manager_->ResetImmediatePullOverInfo();
      distance_to_ride_start_when_start_immediate_pullover_ = std::nullopt;
      return;

    case pb::ImmediatePullOverRequestType::kNotRequest:
      if (current_state == pb::ImmediatePullOverState::kTriggered) {
        // Add additional terminate logic when routing node is triggered but
        // planner node does not response:
        // 1. If currently is during same origin and dest immediate pullover, we
        // receives new ride route query.
        // 2. After routing triggered, the accumulate distance is larger than
        // 100m.
        std::optional<double> passed_distance_from_ride_start = std::nullopt;
        if (ego_car_status_ != nullptr &&
            distance_to_ride_start_when_start_immediate_pullover_) {
          passed_distance_from_ride_start =
              ego_car_status_->GetEgoPassedDistanceFromRideStart();
        }
        const double immediate_pullover_lasting_distance =
            (distance_to_ride_start_when_start_immediate_pullover_ &&
             passed_distance_from_ride_start.has_value())
                ? passed_distance_from_ride_start.value() -
                      distance_to_ride_start_when_start_immediate_pullover_
                          .value()
                : 0.0;
        const bool receive_ride_query_during_routing_triggered_imm_pullover =
            receive_new_ride_route_query &&
            current_source == pb::ImmediatePullOverSource::kRouting &&
            !math::NearZero(immediate_pullover_lasting_distance);
        const bool exceed_not_response_distance =
            immediate_pullover_lasting_distance >
            kMaxDistanceToTerminateImmediatePullOverInMeter;

        if ((current_planner_state ==
             pb::ImmediatePullOverState::kNotTriggered) &&
            (receive_ride_query_during_routing_triggered_imm_pullover ||
             exceed_not_response_distance)) {
          debug_info << "Routing trigger cancel immediate pullover when "
                        "planner not response, ";
          if (receive_ride_query_during_routing_triggered_imm_pullover) {
            debug_info << "order sends new query. ";
          }
          if (exceed_not_response_distance) {
            debug_info << "exceeds max distance. ";
          }
          rt_event::PostRtEvent<
              rt_event::routing::ImmediatePullOverCancelledTime>(
              debug_info.str());
          LOG(INFO) << debug_info.str();
          route_status_manager_->ResetImmediatePullOverInfo();
          distance_to_ride_start_when_start_immediate_pullover_ = std::nullopt;
        }
        return;
      }
      // If currently is not in immediate pullover process, always reset.
      route_status_manager_->ResetImmediatePullOverInfo();
      return;

    default:
      LOG(ERROR) << "unknown request type: " << request_type;
      return;
  }
}

RouteInfo StateManager::GetRouteInfo() {
  const pb::MissionState state = state_->state();
  if (state == pb::MissionState::kOntrip) {
    const OnTripState* ontrip_state =
        dynamic_cast<const OnTripState*>(state_.get());
    return ontrip_state->route_info();
  }

  if (state == pb::MissionState::kOntripProposing) {
    const OnTripState* ontrip_proposing_state =
        dynamic_cast<const OnTripProposingState*>(state_.get());
    return ontrip_proposing_state->route_info();
  }

  if (state == pb::MissionState::kProposedStatic) {
    const ProposedStaticState* proposed_static_state =
        dynamic_cast<const ProposedStaticState*>(state_.get());
    return proposed_static_state->route_info();
  }

  if (state == pb::MissionState::kOntripPullover) {
    const OntripPulloverState* ontrip_pullover_state =
        dynamic_cast<const OntripPulloverState*>(state_.get());
    return ontrip_pullover_state->route_info();
  }

  // Should not enter here.
  CHECK(false);
  RouteInfo empty_route_info;
  return empty_route_info;
}

void StateManager::UpdateRoutingSeed(const voy::Pose& pose) {
  if (!publish_seed_ || !IsOnTrip(state_->state()) ||
      pose.timestamp() - last_seed_publish_time_ <
          kRoutingSeedPublishInterval) {
    return;
  }

  last_seed_publish_time_ = pose.timestamp();
  const RouteInfo& route_info = GetRouteInfo();
  const pb::RouteStatus& current_route_status =
      route_status_manager_->current_route_status();
  const int32_t waypoint_count = current_route_status.waypoint_count();
  if (routing_seed_.timestamp() != route_info.route_query_info.timestamp ||
      routing_seed_.waypoint_count() != waypoint_count) {
    routing_seed_.set_timestamp(route_info.route_query_info.timestamp);
    *routing_seed_.mutable_ride_route_query() =
        route_info.route_query_info.original_ride_route_query;
    *routing_seed_.mutable_current_route_query() =
        base::CheckAndGetValue(route_info.route_query_info.current_route_query);
    routing_seed_.set_waypoint_count(waypoint_count);
    routing_seed_.set_has_waypoint_count(true);
    routing_seed_.mutable_constraint_definitions()->CopyFrom(
        routing_engine_->routing_map()
            ->edge_filter()
            ->constraint_definitions());
    routing_seed_.mutable_arrived_section_ids_from_current_route_point()
        ->CopyFrom(current_route_status
                       .arrived_section_ids_from_current_route_point());
    routing_seed_.mutable_ride_start_pose()->CopyFrom(
        current_route_status.ride_start_pose());
    routing_seed_.mutable_arrived_road_ids_from_ride_start()->CopyFrom(
        current_route_status.arrived_road_ids_from_ride_start());
  }

  route_publisher_->PublishRoutingSeed(routing_seed_);
  // NOTE(Tingran): Publish route status when updating routing seed to make
  // routing node compatible for aligned mode to use routing seed for
  // initialization. For more background: Routing node publishes routing seed
  // and route status during onboard, but may not be at the same frame in the
  // before. During simulation, routing node subscribes routing seed from bag,
  // and initialize the internal state, and publish a route status right away to
  // planner node. If at this frame, planner did not receive a route status
  // during onboard, then the published route status will be disregarded at
  // sim_aligned_mode. Then this will be a problem because routing and planner
  // nodes need interaction to communicate. If one message is ignored,
  // especially during proposing a route, they will not work properly.
  should_publish_route_status_ = true;
}

void StateManager::PublishRouteStatus() {
  const pb::RouteStatus& route_status =
      route_status_manager_->current_route_status();
  const int64_t current_timestamp = route_status.current_pose().timestamp();
  route_publisher_->PublishRouteStatus(route_status);
  last_route_status_publish_time_ = current_timestamp;
  should_publish_route_status_ = false;

  // If load constraint failed, add fault code.
  // Note: constraint_info.constraint_error_msg() will be empty if success to
  // load file.
  const pb::ConstraintInfo& constraint_info = route_status.constraint_info();
  std::string constraint_error_msg =
      (FLAGS_enable_get_constraints_from_cloud &&
       constraint_info.constraint_name().length() != 0 &&
       !constraint_info.is_from_cloud() &&
       routing_engine_->routing_map()->hdmap().IsRegionWithPlatformData())
          ? "Failed to load constraint settings from hdmap data file. " +
                constraint_info.constraint_error_msg()
          : constraint_info.constraint_error_msg();
  const bool is_for_driverless_mode = false;
  if (!constraint_error_msg.empty()) {
    constraint_error_msg +=
        " major version: " + constraint_info.major_version();
    constraint_error_msg +=
        " minor version: " + constraint_info.minor_version();
    if (is_for_driverless_mode) {
      AddFaultCode(::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE_RED_LIGHT,
                   constraint_error_msg);
    } else {
      AddFaultCode(::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE,
                   constraint_error_msg);
    }
  } else {
    if (is_for_driverless_mode) {
      ClearFaultCodes(
          {::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE_RED_LIGHT});
    } else {
      ClearFaultCodes({::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE});
    }
  }

  // If loaded constraint is empty or is not consistent with route query
  // constraint type, add fault for invalid constraint.
  if (constraint_info.constraint_definitions()
          .constraint_definitions()
          .empty() ||
      constraint_info.constraint_name() !=
          route_status.current_route_query().constraint_name()) {
    if (is_for_driverless_mode) {
      AddFaultCode(::pb::RoutingFaultCode::INVALID_CONSTRAINT_RED_LIGHT,
                   constraint_error_msg);
    } else {
      AddFaultCode(::pb::RoutingFaultCode::INVALID_CONSTRAINT,
                   constraint_error_msg);
    }
  } else {
    if (is_for_driverless_mode) {
      ClearFaultCodes({::pb::RoutingFaultCode::INVALID_CONSTRAINT_RED_LIGHT});
    } else {
      ClearFaultCodes({::pb::RoutingFaultCode::INVALID_CONSTRAINT});
    }
  }
}

void StateManager::PublishRoutingDebug(double node_cycle_time) {
  if (routing_debug_manager_.IsRoutingDebugEmpty()) {
    return;
  }
  // When route query handle infos not empty, publish routing debug.
  routing_debug_manager_.SetNodeCycleTime(node_cycle_time);
  const auto& pose_and_lane = ego_car_status_->GetCurrentPoseAndLane();
  if (pose_and_lane.has_value()) {
    routing_debug_manager_.SetCurrentPose(pose_and_lane->pose);
  }
  route_publisher_->PublishRoutingDebug(
      routing_debug_manager_.current_routing_debug());
  routing_debug_manager_.Clear();
}

void StateManager::PublishRouteConfirmation(
    const pb::RouteSolution& route_solution, pb::RouteState route_state,
    bool send_fault_code, const std::string& error_msg) {
  route_publisher_->PublishRouteConfirmation(route_solution, route_state);
  route_status_manager_->SetRouteState(route_state);

  if (!send_fault_code) {
    return;
  }

  switch (route_state) {
    case pb::RouteState::kRoutingFailed:
      AddFaultCode(::pb::RoutingFaultCode::ROUTE_CALCULATION_FAILURE_LOW_LEVEL,
                   error_msg);
      break;
    case pb::RouteState::kPlanningRejected:
    case pb::RouteState::kPlanningRejectedRouteLanes:
      AddFaultCode(::pb::RoutingFaultCode::ROUTE_REJECTED_BY_PLANNER,
                   error_msg);
      break;
    case pb::RouteState::kRoutingFailedForOperation:
    case pb::RouteState::kRouteCancelFailed:
    case pb::RouteState::kRouteCompleteFailed:
    case pb::RouteState::kRoutingSuccess:
    case pb::RouteState::kPlanningAccepted:
    case pb::RouteState::kRouteCancelled:
    case pb::RouteState::kRouteCompleted:
    case pb::RouteState::kPullOverCompleted:
    case pb::RouteState::kRejectOrderRouteCommand:
      break;
    case pb::RouteState::kUnknownRouteState:
    case pb::RouteState::RouteState_INT_MIN_SENTINEL_DO_NOT_USE_:
    case pb::RouteState::RouteState_INT_MAX_SENTINEL_DO_NOT_USE_:
      CHECK(false) << "Wrong route_state: " << route_state;
  }
}

void StateManager::AcceptRoute(const RouteInfo& route_info) {
  ClearFaultCodes({::pb::RoutingFaultCode::ROUTE_REJECTED_BY_PLANNER,
                   ::pb::RoutingFaultCode::ROUTE_PROPOSING_TIME_OUT,
                   ::pb::RoutingFaultCode::UNEXPECTED_PLANNING_ROUTE_STATE,
                   ::pb::RoutingFaultCode::REPLANNING_ROUTE_REJECTED,
                   ::pb::RoutingFaultCode::PARTIAL_ROUTE_REJECTED});
  LOG(INFO) << "Proposed route is accepted by planning.";

  // Update current route status.
  route_status_manager_->UpdateCurrentRouteStatus(route_info,
                                                  /*is_path_rejected=*/false);
  should_publish_route_status_ = true;
  // Reset rejected_route_query_mission_id_ once a new route is accepted.
  rejected_route_query_mission_id_ = -1;

  // Set constraint info to ros param.
  const pb::ConstraintInfo& constraint_info = route_info.constraint_info;
  ros::param::set("/major_version", constraint_info.major_version());
  ros::param::set("/minor_version", constraint_info.minor_version());
  ros::param::set("/constraint_type", constraint_info.constraint_name());
}

void StateManager::RejectRoute(const RouteInfo& route_info) {
  ClearFaultCodes({::pb::RoutingFaultCode::ROUTE_REJECTED_BY_PLANNER,
                   ::pb::RoutingFaultCode::ROUTE_PROPOSING_TIME_OUT,
                   ::pb::RoutingFaultCode::UNEXPECTED_PLANNING_ROUTE_STATE,
                   ::pb::RoutingFaultCode::PARTIAL_ROUTE_REJECTED});
  LOG(INFO) << "Proposed route is rejected by planning, use last global path, "
            << "but still update route's other information.";

  // Update current route status.
  route_status_manager_->UpdateCurrentRouteStatus(route_info,
                                                  /*is_path_rejected=*/true);
  should_publish_route_status_ = true;
}

// TODO(Junfeng): If replan only for updating cost map, we can skip a_star
// seach and directly propose route solution with new trimmed cost map. we only
// propose new route when handle ride query or replan for changing route lanes.
void StateManager::ProposeRoute(const RouteInfo& route_info,
                                pb::ProposingReason proposing_reason) {
  DCHECK_GE(route_info.route_query_info.waypoint_count, 0);
  if (route_info.route_solution.success()) {
    ClearAllFaultCodes();
  } else if (!route_status_manager_->IsPreparedToProposeCostMap(
                 route_info, proposing_reason)) {
    return;
  }

  // Routing node will propose route to planner node even if replan failed. When
  // route solution is not successful in replan mode, we will try to creat a new
  // proposed route solution with last and failed route solution.
  route_status_manager_->UpdateProposedRouteSolution(
      route_info, proposing_reason,
      routing_debug_manager_.ShouldPlannerTriggerMRC());
  should_publish_route_status_ = true;
}

void StateManager::CompleteRoute(
    const RouteInfo& route_info, pb::CommandPurpose command_purpose,
    const std::optional<int64_t>& completion_mission_id) {
  ClearFaultCodes({::pb::RoutingFaultCode::ROUTE_COMPLETION_FAILURE});
  int64_t overwrite_mission_id = -1;
  if (command_purpose == pb::CommandPurpose::kRoutingSkippedRouteCompletion &&
      completion_mission_id.has_value()) {
    overwrite_mission_id = completion_mission_id.value();
  }
  // Don't update time so that planner won't update the empty route in planner.
  const RouteInfo empty_route_info = utils::MakeEmptyRouteInfo(
      route_info, /*update_timestamp=*/false, overwrite_mission_id);
  route_status_manager_->UpdateProposedRouteSolution(
      empty_route_info, pb::ProposingReason::kCompletion,
      /*should_planner_trigger_mrc=*/false);
  route_status_manager_->UpdateImmediatePullOverStateToSuccess();
  route_status_manager_->UpdateCurrentRouteStatus(empty_route_info,
                                                  /*is_path_rejected=*/false);
  should_publish_route_status_ = true;

  const pb::RouteState route_state = GetCompletionRouteState(command_purpose);
  PublishRouteConfirmation(empty_route_info.route_solution, route_state,
                           /*send_fault_code=*/false,
                           /*error_msg=*/"");
}

void StateManager::CancelRoute(const RouteInfo& route_info) {
  ClearFaultCodes({::pb::RoutingFaultCode::ROUTE_CANCELLATION_FAILURE});
  const RouteInfo empty_route_info = utils::MakeEmptyRouteInfo(
      route_info, /*update_timestamp=*/false, /*overwrite_mission_id=*/-1);
  route_status_manager_->UpdateProposedRouteSolution(
      empty_route_info, pb::ProposingReason::kCancellation,
      /*should_planner_trigger_mrc=*/false);
  route_status_manager_->UpdateCurrentRouteStatus(empty_route_info,
                                                  /*is_path_rejected=*/false);
  should_publish_route_status_ = true;

  PublishRouteConfirmation(empty_route_info.route_solution,
                           pb::RouteState::kRouteCancelled,
                           /*send_fault_code=*/false,
                           /*error_msg=*/"");
}

void StateManager::RejectOrderRouteQuery(const pb::RouteQuery& route_query) {
  // Since for rejection, we did not calculate for route solution, so we use
  // |route_query| to create a route solution to publish reject confirmation.
  const pb::RouteSolution reject_route_solution =
      utils::CreateRejectRouteSolution(route_query);
  PublishRouteConfirmation(reject_route_solution,
                           pb::RouteState::kRejectOrderRouteCommand,
                           /*send_fault_code=*/false,
                           /*error_msg=*/"");
}

RouteInfo StateManager::SyncRouteQueryInfoToRouteInfo(
    const RouteQueryInfo& route_query_info) {
  RouteInfo route_info;
  route_info.route_query_info = route_query_info;
  LOG(INFO) << "Set waypoint count be 0.";
  route_info.route_query_info.waypoint_count = 0;

  // Since for idle state, we do not have route solution, so just fill incoming
  // route query.
  return route_info;
}

void StateManager::ClearFaultCodes(
    const std::vector<::pb::RoutingFaultCode::Enum>& fault_codes) {
  for (const auto& code : fault_codes) {
    fault_reporter_.RemoveFault(code);
  }
}

void StateManager::ClearAllFaultCodes() {
  ClearFaultCodes(
      {::pb::RoutingFaultCode::ROUTE_CALCULATION_FAILURE,
       ::pb::RoutingFaultCode::ROUTE_REJECTED_BY_PLANNER,
       ::pb::RoutingFaultCode::ROUTE_CANCELLATION_FAILURE,
       ::pb::RoutingFaultCode::ROUTE_COMPLETION_FAILURE,
       ::pb::RoutingFaultCode::ROUTE_PROPOSING_TIME_OUT,
       ::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE_RED_LIGHT,
       ::pb::RoutingFaultCode::INVALID_CONSTRAINT_RED_LIGHT,
       ::pb::RoutingFaultCode::EGO_POSE_IS_NOT_READY,
       ::pb::RoutingFaultCode::UNEXPECTED_PLANNING_ROUTE_STATE,
       ::pb::RoutingFaultCode::REPLANNING_ROUTE_REJECTED,
       ::pb::RoutingFaultCode::PARTIAL_ROUTE_REJECTED,
       ::pb::RoutingFaultCode::LOAD_CONSTRAINT_FAILURE,
       ::pb::RoutingFaultCode::INVALID_CONSTRAINT_RED_LIGHT,
       ::pb::RoutingFaultCode::ROUTE_CALCULATION_FAILURE_LOW_LEVEL});
}

void StateManager::AddFaultCode(::pb::RoutingFaultCode::Enum fault_code,
                                const std::string& error_msg) {
  if (fault_code == ::pb::RoutingFaultCode::ROUTE_CALCULATION_FAILURE ||
      fault_code == ::pb::RoutingFaultCode::EGO_POSE_IS_NOT_READY ||
      fault_code ==
          ::pb::RoutingFaultCode::ROUTE_CALCULATION_FAILURE_LOW_LEVEL) {
    route_status_manager_->AppendRoutingFailureReason(error_msg);
  }
  LOG(ERROR) << error_msg;
  fault_reporter_.AddFault(fault_code, error_msg);
}

void StateManager::UpdateSpecialPlannerResponseRequest(
    pb::SpecialPlannerResponseRequest special_request) {
  route_status_manager_->UpdateSpecialPlannerResponseRequest(special_request);
}

void StateManager::InternalCancelTrip() {
  LOG(INFO) << "Internally cancel current trip to idle state";
  ChangeState(std::make_unique<IdleState>());
}

void StateManager::UpdateInternalStateInRouteStatus() {
  route_status_manager_->SetMissionState(state_->state());
  should_publish_route_status_ = true;
}

bool StateManager::IsItTimeToPublishAnotherRouteStatus() const {
  const pb::RouteStatus& route_status =
      route_status_manager_->current_route_status();
  const int64_t current_timestamp = route_status.current_pose().timestamp();
  return (current_timestamp - last_route_status_publish_time_) >=
         kRouteStatusPublishInterval;
}

pb::RouteState StateManager::GetCompletionRouteState(
    pb::CommandPurpose command_purpose) const {
  if (command_purpose !=
          pb::CommandPurpose::kPlannerCompletesDHMIImmediatePullOverRoute &&
      command_purpose !=
          pb::CommandPurpose::kPlannerCompletesPHMIImmediatePullOverRoute) {
    return pb::RouteState::kRouteCompleted;
  }

  const std::optional<double> dist_to_route_dest =
      ego_car_status_->GetDistToRouteDestination();
  if (!dist_to_route_dest.has_value()) {
    LOG(ERROR) << "No valid lane sequence, but return regular route complete "
                  "signal.";
    return pb::RouteState::kRouteCompleted;
  }

  return dist_to_route_dest.value() <
                 kDistToDestThresholdToPublishRouteCompletionInMeter
             ? pb::RouteState::kRouteCompleted
             : pb::RouteState::kPullOverCompleted;
}

}  // namespace state
}  // namespace routing
