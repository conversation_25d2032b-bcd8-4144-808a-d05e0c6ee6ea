#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CANBUS_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CANBUS_OBSERVER_H_

#include <functional>
#include <memory>
#include <set>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/canbus.pb.h"

namespace remote_assist {

constexpr int kMsgTimeoutMs = 13000;

class CanbusObserver : public IDataObserver {
 public:
  using CanbusCallback =
      std::function<void(std::shared_ptr<voy::Canbus>, DataChannel)>;

  explicit CanbusObserver(CanbusCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

  void CheckMsgTimeout();

  void SetIsManual(const std::shared_ptr<voy::Canbus>& canbus);

 private:
  CanbusCallback callback_;
  const std::set<voy::VehicleMode> kManualModeSet;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_CANBUS_OBSERVER_H_
