#include "planner/planning_gflags.h"

namespace planner {

DEFINE_bool(enable_planning_debug, true,
            "If true, enable planning debug proto.");

DEFINE_bool(planning_enable_first_frame_seed, false,
            "If true, enable planning seed mode for the first frame.");

DEFINE_bool(planning_enable_all_frame_seed, false,
            "If true, enable planning seed mode for all frames.");

DEFINE_bool(planning_enable_warmup_frame_seed, false,
            "If true, enable planning seed mode for warmup frames.");

DEFINE_bool(planning_enable_first_frame_state, false,
            "If true, load planner state for the first frame.");

DEFINE_bool(planning_enable_state_dump, true,
            "If true, planner state will be dumped to bag.");

DEFINE_bool(planning_enable_all_frame_state, false,
            "If true, load planner state for all frames.");

DEFINE_bool(planning_enable_warmup_frame_state, false,
            "If true, load planner state for warmup frames.");

DEFINE_bool(planning_enable_override_car_type_in_sim, false,
            "Set car type used by planner to BYD for simulation purpose.");

DEFINE_bool(planning_enable_write_route_solution, false,
            "If true, generate route solution json file.");

DEFINE_bool(planning_enable_gap_based_lane_change, true,
            "If true, use gap analysis for lane change.");

DEFINE_bool(planning_enable_lane_boundary_violation, true,
            "If true, enable lane boundary violation.");

DEFINE_bool(planning_enable_disengagement_zone, true,
            "If true, disengagement zone feature will be enabled.");

DEFINE_bool(
    planning_enable_st_based_traffic_light_reasoner, true,
    "If true, S-T based traffic light reasoner feature will be enabled.");

DEFINE_bool(
    planning_enable_congestion_signal, false,
    "If true, congestion signal will be enabled in lane blockage detector.");

DEFINE_bool(planning_enable_driving_on_freeway, false,
            "If true, do not limit max speed to 60 km/h and do not limit max "
            "speed at night.");

DEFINE_bool(
    planning_enable_elective_lane_change_road_exit_risk_avoidance, true,
    "If true, elective lane change road exit risk avoidance will be enabled.");

DEFINE_bool(
    planning_enable_elective_lane_change_u_turn_risk_avoidance, true,
    "If true, elective lane change U turn risk avoidance will be enabled.");

DEFINE_bool(
    planning_enable_elective_lane_change_junction_right_side_risk_avoidance,
    true,
    "If true, elective lane change junction in right side risk avoidance will "
    "be enabled.");

DEFINE_bool(
    planning_enable_elective_lane_change_neighbor_lane_stm, true,
    "If true, elective lane change neighbor lane stm risk avoidance will "
    "be enabled.");

DEFINE_bool(
    planning_enable_elc_risk_avoidance_risk_directive_slow_cut_in, true,
    "If true, elective lane change risk directive slow cut-in risk avoidance "
    "will be enabled.");

DEFINE_double(
    planning_elc_triggered_simulated_time_in_sec, -1.0,
    "If its value is greater than zero, will trigger elc at simulated time.");

DEFINE_int32(planning_elc_triggered_simulated_direction, 0,
             "Defines the lane change direction of a simulated ELC. 0: either "
             "direction, 1: left, 2: right.");

DEFINE_double(planning_lane_change_override_urgency_score, -1.0,
              "When non-negative, overrides the generic urgency score in the "
              "lane change module.");

DEFINE_bool(planning_enable_lane_change_abort_response, true,
            "If true, the lane change abort response is enable for the mlc "
            "state transition.");

DEFINE_double(planning_trigger_lane_change_abort_by_encroachment_in_meter,
              10000.0,
              "If the encroachment dist over the cross lane curve is greater "
              "than the value, will trigger lane change abort automatically.");

DEFINE_bool(
    planning_enable_overlap_computation_policy, true,
    "If true, only compute overlaps for certain predicted trajectories.");

DEFINE_bool(planning_enable_manual_lane_change_command, true,
            "If true, the manual lane change command is enable for the mlc "
            "state transition.");

DEFINE_bool(planning_enable_lane_change_interaction_finish_score, true,
            "If true, the lane change finish condition would consider "
            "interaction finish score.");

DEFINE_bool(planning_enable_near_lane_change_end_blocking_traffic_detector,
            true,
            "If true, invokes the detection of lane change blocking traffic.");

DEFINE_bool(planning_enable_reroute_proposal_near_trimmed_lane_seq_end, true,
            "If true, invokes the reroute proposal for consecutive lane change "
            "sequence end to give up subsequent lane changes early and avoid "
            "speed loss.");

DEFINE_bool(planning_enable_dry_steering_controller, true,
            "If true, invokes the dry_steering_target_angle and angle_rate "
            "interface to enable the dry steering mpc controller.");

DEFINE_bool(
    planning_enable_reroute_proposal_rear_end_hazard_due_to_eol_during_abort,
    true,
    "If true, invokes the reroute proposal for rear end hazard due to EOL "
    "during lane change abort.");

DEFINE_bool(planning_enable_lane_change_backup_sequence_proposal, true,
            "If true, invokes the lane change backup sequence proposal to "
            "latch sequence during lane change abort.");

DEFINE_bool(planning_enable_addressing_lane_change_backup_proposal_from_seed,
            true,
            "If true, attempts to address the LC_BACKUP proposal with lane "
            "sequences restored from seed if normal search fails.");

DEFINE_bool(
    planning_elc_get_vehicles_on_lane_sequence_by_occupancy_param, false,
    "If true, elc will get vehicles on lane sequence by occupancy param.");

DEFINE_bool(planning_enable_elc_by_merge_structure, true,
            "If true, elc will be triggered considering merge structure.");

DEFINE_bool(planning_enable_elc_remaining_distance_with_global_route_on_local,
            true,
            "If true, ELC will compute remaining distance with the global "
            "route on local as well.");

DEFINE_bool(planning_display_yield_conflicting_contours, false,
            "If true, show yield reasoner's conflicting contours in stream.");

DEFINE_bool(planning_display_yield_zone_contour, true,
            "If true, show yield zone contour in stream.");

DEFINE_bool(planning_display_nudge_regions, false,
            "If true, show nudge regions in stream.");

DEFINE_bool(planning_display_inlane_nudge_corridor, false,
            "If true, show inlane nudge corridor in stream.");

DEFINE_bool(planning_display_auxiliary_nominal_path, false,
            "If true, show auxiliary path in stream.");

DEFINE_bool(planning_display_ddp_path, false,
            "If true, show smooth nominal path in stream.");

DEFINE_bool(planning_display_lattice_nominal_path, false,
            "If true, show lattice-based nominal path in stream.");

DEFINE_bool(planning_display_road_buffer, false,
            "If true, show buffer boundary regions in stream.");

DEFINE_bool(planning_display_soft_buffer_line, false,
            "If true, show buffer soft lines in stream.");

DEFINE_bool(planning_display_hard_road_segments, false,
            "If true, show hard road segments in stream.");

DEFINE_bool(planning_display_gap, false, "If true, show the optimal gap.");

DEFINE_bool(planning_display_ddp_lane_change_path, false,
            "If true, show lane change path in stream.");

DEFINE_bool(planning_display_nudge_motion_boundary, false,
            "If true, show the nudge motion boundaries in stream.");

DEFINE_bool(planning_publish_waypoint_graph_dot, false,
            "If true, generate waypoint graph dot file");

DEFINE_bool(planning_publish_waypoint_graph_to_debug, false,
            "If true, add waypoint graph debug info into "
            "|LaneSequenceGeneratorDebug|.");

DEFINE_bool(planning_enable_traffic_info_map_debug, false,
            "If true, will store traffic info map debug");

DEFINE_bool(planner_enable_occlusion_map_for_traffic_info_map, false,
            "If true, the planner will use occlusion map to check if the lane "
            "area is not occupied.");

DEFINE_bool(planning_enable_traffic_info_map_for_temp_parked, true,
            "If true, the planner will use traffic info map to infer the "
            "occlusion area, and use traffic flow to infer the temporary "
            "vehicle.");

DEFINE_bool(
    planning_publish_regional_path_graph_dot, false,
    "If true, the planner will write the regional path graph to a dot file.");

DEFINE_bool(backup_predicted_trajectory_stream, false,
            "Show backup predicted trajectory in stream");

DEFINE_bool(planning_enable_st_planner_debug, false,
            "If true, the planner will output ST planner debug");

DEFINE_bool(planning_enable_static_agent_path_reasoner_debug, false,
            "If true, more debug info would be logged out from the static "
            "agent path reasoner.");

DEFINE_double(
    planning_override_car_speed_limit_mps, -1.0,
    "If its value is greater than zero, will override all cars' speed "
    "limit with it.");

DEFINE_bool(planning_display_drivable_space, false,
            "If true, display the drivable space through shape stream");

DEFINE_bool(planning_perform_forward_simulation_in_crawl, false,
            "If true, forward simulation will be performed "
            "in lane change crawl.");

DEFINE_bool(
    planning_enable_lattice_spirals_debug, false,
    "If true, enable the lattice spiral candidates' debugging information.");

DEFINE_bool(
    planning_enable_agent_intention_searcher, true,
    "If true, the search based agent intention generator should be used to "
    "generate the ego intention results.");

DEFINE_bool(planning_enable_decoupled_maneuvers, true,
            "Sets if we want to enable decoupled maneuvers.");

DEFINE_bool(planning_enable_early_lane_change_reasoning, true,
            "If true, invoke early lane change reasoning (without executing).");

DEFINE_bool(planning_enable_early_lane_change_on_highway, false,
            "If true, invoke early lane change on highway.");

DEFINE_bool(planning_enable_early_lane_change_execution, true,
            "If true, execute the early lane change.");

DEFINE_bool(planning_enable_early_lane_change_execution_ab_test, false,
            "If true, execute the early lane change for A/B test.");

DEFINE_bool(planning_enable_lane_change_creep, true,
            "If true, enable the lane change creep.");

DEFINE_bool(planning_enable_lane_change_high_risk_detector, true,
            "If true, invoke the lane change high risk detector.");

DEFINE_bool(planning_enable_lc_forbid_ra_intervention_requirement, true,
            "If true, invoke lane change forbid ra intervention requirement.");

DEFINE_bool(planning_enable_use_can_reroute_flag_for_lc_high_risk, true,
            "If true, use can_reroute flag in route preview for lane change "
            "high risk detector.");

DEFINE_bool(planning_enable_use_can_reroute_flag_for_cons_lc, false,
            "If true, use can_reroute flag in route preview for consecutive "
            "lane changes.");

DEFINE_bool(
    planning_enable_use_can_reroute_flag_for_lc_blocking_traffic, true,
    "If true, use can_reroute flag in route preview for lane change stuck.");

DEFINE_bool(planning_enable_multi_lane_follow_lane_sequence, true,
            "Sets if we want to enable multi lane sequence candidates in the "
            "decoupled lane follow maneuvers.");

DEFINE_bool(planning_produce_stuck_signal, true,
            "Sets if we want to produce stuck signal. ");

DEFINE_bool(planning_enable_last_path, true,
            "Sets if we use last path in selection. ");

DEFINE_bool(planning_enable_path_reasoning_last_speed_profile, true,
            "Sets if we filter the speed/accel error check when constructing "
            "the last profile for path reasoning.");

DEFINE_bool(planning_disable_exception_handler_eb_in_hybrid, true,
            "Sets if we disable exception_handler eb in hybrid mode.");

DEFINE_bool(planning_enable_exception_handler_eb_on_road, true,
            "Sets if we enable exception_handler eb on road.");

DEFINE_bool(planning_enable_exception_handler_eb_in_sim, true,
            "Sets if we enable exception_handler eb in simulation.");

DEFINE_bool(planning_disable_exception_handler_eb_in_RA, true,
            "Sets if we disable exception_handler eb in remote assist.");

DEFINE_bool(planning_disable_exception_handler_in_MRC, true,
            "Sets if we disable exception_handler eb in mrc.");

DEFINE_bool(planning_enable_exception_handler_eb_swerve, true,
            "Sets if we enable exception_handler swerve in eb.");

DEFINE_bool(
    planning_enable_exception_ignore_ego_prediction_veh, true,
    "Sets if we enable ignore ego prediction for veh in exception_handler.");

DEFINE_bool(
    planning_enable_exception_ignore_ego_prediction_VRU, true,
    "Sets if we enable ignore ego prediction for VRU in exception_handler.");

DEFINE_bool(planning_enable_exception_handler_emergency_lane_keep, true,
            "Sets if we enable exception_handler emergency lane keep.");

DEFINE_bool(planning_enable_exception_handler_elk_onboard, true,
            "Sets if we enable exception_handler emergency lane keep onboard.");

DEFINE_bool(planning_enable_exception_handler_tailgater_protection, true,
            "Sets if we enable exception_handler tailgater protection.");

DEFINE_bool(planning_enable_exception_handler_tailgater_protection_speed_only,
            false,
            "Sets if we only enable exception_handler tailgater protection "
            "speed only trajectory generation.");

DEFINE_bool(planning_enable_exception_handler_tailgater_protection_from_es,
            true,
            "Sets if we enable exception_handler tailgater protection "
            "trajectory from ES.");

DEFINE_bool(
    planning_enable_exception_handler_tailgater_protection_swerve_onboard, true,
    "Sets if we enable exception_handler tailgater protection swerve "
    "onboard. False = shadow mode.");

DEFINE_bool(planning_enable_exception_handler_ebs_from_es, true,
            "Sets if we enable exception_handler ebs trajectory from ES.");

DEFINE_int64(planning_exception_handler_elk_select_trajectory_idx, -1,
             "Sets if we want to select choosen trajectory index for debug.");

DEFINE_bool(
    planning_enable_exception_handler_es_allow_risk_buffer, false,
    "Sets if we allow to enable exception_handler es with risk ttc buffer.");

DEFINE_bool(planning_enable_exception_handler_elk_provide_all_geometry, true,
            "Sets if we enable exception_handler emergency lane keep use "
            "geometry from the provider in exception_handler.");

DEFINE_bool(planning_enable_exception_handler_use_eh_lk_sequence_as_reference,
            false,
            "Sets if we enable exception_handler use lane keep sequence from "
            "eh geometry provider as reference.");

DEFINE_bool(planning_enable_exception_handler_free_space_geometry_corridor,
            false,
            "Sets if we enable exception handler free space geometry generate "
            "corridor.");

DEFINE_bool(planning_enable_exception_handler_elk_xlane_tailgater_extend, true,
            "Sets if we enable extended xlane tailgater detection for "
            "exception_handler ELK.");

DEFINE_bool(planning_enable_exception_handler_elk_in_multi_lane_keep_sequence,
            true,
            "Sets if we enable ELK in multi lane keep sequence scenario for "
            "exception_handler");

DEFINE_bool(planning_enable_exception_handler_eb_for_cz_collision, false,
            "Sets if we enable exception_handler eb for cz collision.");

DEFINE_bool(planning_enable_exception_handler_eb_for_curb_collision, true,
            "Sets if we enable exception_handler eb for curb collision.");

DEFINE_bool(planning_enable_exception_handler_cautious_driving, true,
            "Sets if we enable exception_handler cautious driving.");

DEFINE_bool(
    planning_enable_exception_handler_cautious_driving_control_drift, true,
    "Sets if we enable exception_handler cautious driving for control drift.");

DEFINE_bool(
    planning_enable_exception_handler_cautious_driving_remote_speed_limit,
    false,
    "Sets if we enable exception_handler cautious driving for remote speed "
    "limit.");

DEFINE_bool(
    planning_enable_exception_handler_cautious_driving_sensor_abnormality, true,
    "Sets if we enable exception_handler cautious driving for sensor "
    "abnormality.");

DEFINE_bool(
    planning_enable_exception_handler_cautious_driving_eh_warning, false,
    "Sets if we enable exception_handler cautious driving for eh warning.");

DEFINE_bool(planning_enable_exception_handler_eb_for_control_drift, true,
            "Sets if we enable exception_handler eb in control drift.");

DEFINE_bool(
    planning_enable_exception_handler_eb_for_localization_critical_drift, true,
    "Sets if we enable exception_handler eb in localization critical drift.");

DEFINE_bool(planning_enable_exception_handler_eb_for_obstacle_in_sim, true,
            "Sets if we enable exception_handler eb for obstacle in sim.");

DEFINE_int64(planning_enable_exception_handler_debug_obj_id, 0,
             "Sets if we want to output debug info of object_id");

DEFINE_bool(planning_enable_exception_handler_shadow_eb_in_sim, false,
            "Sets if we enable exception_handler eb in shadow mode.");

DEFINE_bool(planning_enable_exception_handler_eb_for_early_publish, true,
            "Sets if we enable exception_handler eb for early publish.");

DEFINE_bool(planning_enable_exception_handler_eb_for_unknown, true,
            "Sets if we enable exception_handler eb for unknown object.");

DEFINE_bool(planning_enable_exception_handler_eb_for_emergency_object, true,
            "Sets if we enable exception_handler eb for emergency object.");

DEFINE_bool(planning_enable_exception_handler_eb_for_anomaly_detection, false,
            "Sets if we enable exception_handler eb for anomaly detection.");

DEFINE_bool(planning_enable_exception_handler_eb_for_combine_all, true,
            "Sets if we enable exception_handler eb for combine all.");

DEFINE_bool(planning_enable_exception_handler_eb_for_camera, true,
            "Sets if we enable exception_handler eb for camera.");

DEFINE_bool(planning_enable_exception_handler_taligator_risk_margin, true,
            "Sets if we enable exception_handler taligator risk margin to "
            "invoke EB even if there is a taligator.");

DEFINE_bool(planning_enable_exception_handler_soft_brake_for_taligator_risk,
            true,
            "Sets if we enable exception_handler soft brake to avoid tail-end "
            "collision.");

DEFINE_bool(planning_enable_exception_handler_for_sustained_object, true,
            "Sets if we enable exception_handler for sustained object.");

DEFINE_bool(planning_enable_exception_handler_for_irregular_object, true,
            "Sets if we enable exception_handler for irregular object.");

DEFINE_bool(planning_enable_exception_handler_for_unknown_animal, true,
            "Sets if we enable exception_handler for unknown animal.");

DEFINE_bool(planning_enable_exception_handler_eb_for_vehicle_obstacle, true,
            "Sets if we enable exception_handler eb for vehicle obstacle.");

DEFINE_bool(planning_enable_exception_handler_in_merge_zone, true,
            "Set if we enable exception_handler in merge zone.");

DEFINE_bool(planning_enable_exception_handler_elk_selection, true,
            "Set if we enable exception_handler elk selection.");

DEFINE_bool(planning_enable_exception_handler_elk_selection_cost, true,
            "Set if we enable exception_handler elk selection cost.");

DEFINE_bool(planning_enable_exception_handler_elk_target_object_check, true,
            "Set if we enable exception_handler elk target object check.");

DEFINE_bool(planning_enable_exception_handler_target_object_check, true,
            "Set if we enable exception_handler target object check.");

DEFINE_bool(planning_enable_exception_handler_tailgator_for_unknown_fp, true,
            "Set if we enable tailgator for unknown fp.");

DEFINE_bool(planning_enable_exception_handler_tailgator_for_tail_collision,
            true, "If true, enable tailgator for all tail collision.");

DEFINE_bool(planning_enable_exception_handler_elk_trajectory_force_generation,
            false,
            "If true, elk trajectory will generated whatever condition.");

DEFINE_bool(planning_enable_eh_tailgator_for_dominant_object, true,
            "If true, enable tailgator for dominant object.");

DEFINE_bool(planning_enable_eh_tailgator_for_fp_ctzone, true,
            "If true, enable tailgator for fp construction zone.");

DEFINE_bool(
    planning_enable_control_expect_extra_buffer, true,
    "If true, enable control expect extra buffer for exception handler.");

DEFINE_bool(planning_enable_selectnet_sample_generation, false,
            "True if we'd generate training samples for selection ML model");

DEFINE_bool(planning_enable_human_likeness_embedding_model, true,
            "True if we use human likeness with prediction embedding model as "
            "selection ML model");

DEFINE_bool(planning_enable_selection_human_likeness_cost, true,
            "Sets if we dump enable human likeness cost in selection. ");

DEFINE_bool(planning_disable_selection_human_likeness_in_slow_moving, true,
            "True if disable human likeness model when ego slow moving.");

DEFINE_bool(planning_enable_selection_human_likeness_merge_similar_candidates,
            false,
            "True if enable human likeness model merge similar candidates.");

DEFINE_bool(planning_enable_selection_human_likeness_compare_mode, true,
            "True if enable human likeness model compare mode.");

DEFINE_bool(
    planning_enable_lane_seq_flicker_cost_in_forklane, false,
    "True if enable lane seq flicker cost in forklane scenario in selection.");

DEFINE_bool(
    planning_enable_selection_risk_unknown_discount, true,
    "True if selection risk model down weight risk against unknown objects.");

DEFINE_bool(
    planning_enable_selection_risk_continuous_severity, false,
    " If true, we will use a continuous double severity value instead of a "
    "concrete int severity level to evaluate the collsion severity in "
    "selection risk solver.");

DEFINE_bool(planning_enable_selection_multi_bp_risk, true,
            "True if selection risk model considers multiple predictions.");

DEFINE_bool(planning_enable_selection_derivative_risk, true,
            "True if selection risk model adds derivative of collision "
            "probability in risk cost.");

DEFINE_bool(planning_enable_selection_worst_case_bp_risk_for_speed_cautious,
            false,
            "True if selection risk model considers worst-case "
            "prediction-based risk against speed cautious agents.");

DEFINE_bool(planning_enable_selection_tailgater_extra_check, true,
            "True if selection risk model does extra check to decide whether "
            "to consider a tailgater.");

DEFINE_bool(planning_enable_selection_small_fov_cyc_extra_check, true,
            "True if selection risk model does extra check to decide whether "
            "to consider a cyclist in a small FoV.");

DEFINE_bool(planning_disable_selection_specialized_risk_against_lc_tail, true,
            "True if selection risk model disable the specialized risk against "
            "LC tail agent.");

DEFINE_bool(planning_enable_calculate_dash_lane_marking_cost, false,
            "True if enable calculate dash lane marking cost.");

DEFINE_bool(
    planning_enable_selection_calibrated_road_precedence, true,
    "If true, selection will recalibrate road precedence in risk model.");

DEFINE_bool(planning_enable_selection_risk_ar_on_non_constraint_bp, false,
            "True if selection risk model considers agent reaction against "
            "predictions not in speed regular constraints.");

DEFINE_bool(planning_enable_selection_risk_sync_speed_bp_truncation, true,
            "True if selection risk model will reduce risk check horizon of "
            "prediction when Speed does such truncation.");

DEFINE_bool(planning_enable_fork_selection_last_lc_point, true,
            "True if we used last lane change point interface in selection.");

DEFINE_bool(planning_enable_lane_selection_routing_cost_v2, true,
            "True if we use routing_cost_v2 in fork lane scenario selection.");

DEFINE_bool(planning_enable_fork_selection_lc_difficulty, true,
            "True if we used lane change difficulty in selection.");

DEFINE_bool(
    planning_enable_fork_prune_by_agent, true,
    "True if we prune fork sequence diversity in high density scenario.");

DEFINE_bool(planning_enable_lane_selection_rt_event, true,
            "True if we want to record rt_event for lane selection scenario.");

DEFINE_bool(planning_enable_warm_start_speed_profile_for_all_candidates, true,
            "Sets if we use warm start speed profile for all candidates.");

DEFINE_bool(
    decouple_planning_enable_speed_opt, true,
    "True if we want to enable speed optimization in decoupled maneuvers");

DEFINE_bool(planning_enable_continuous_proximity_speed_in_speed_opt, true,
            "True if enable continuous cost function for proximity speed "
            "constraint in speed optimization");

DEFINE_bool(
    planning_enable_last_profile_attraction_in_speed_opt, true,
    "True if we want to enable last profile attraction in speed optimization");

DEFINE_bool(
    planning_enable_alilqr_in_speed_opt, true,
    "True if enable augmented la iLqr lagrangian in speed optimization");

DEFINE_bool(
    planning_enable_soften_static_range_for_comfort, true,
    "True if enable Soften StaticRange For Comfort in speed optimization");

DEFINE_bool(
    planning_enable_x_upper_bound_attraction_in_speed_opt, true,
    "True if enable position upper bound attraction in speed optimization");

DEFINE_bool(
    planning_enable_x_upper_bound_attraction_in_fast_acceleration, true,
    "True if enable position upper bound attraction in fast acceleration");

DEFINE_bool(planning_enable_occlusion_grid_debug_output, false,
            "Sets if we want to enable occlusion grid debug output in "
            "decoupled maneuvers.");

DEFINE_bool(planning_enable_risk_mitigation_for_occlusion_exit_zone, false,
            "Sets if we want to enable risk mitigation for occlusion around "
            "exit zones.");

DEFINE_bool(
    planning_enable_invited_yield_for_occlusion_crosswalk_unsignalized_junction,
    true,
    "Sets if we want to enable invited yielding for crosswalks at unsignalized "
    "junction.");

DEFINE_bool(
    planning_enable_hallucinated_agent_for_signalized_junction, true,
    "Sets if we want to generate hallucinated agent for signalized junctions.");

DEFINE_bool(planning_enable_invited_yield_for_occlusion_junction, true,
            "Sets if we want to enable invited yielding in junction.");

DEFINE_bool(planning_enable_invited_yield_for_u_turn, true,
            "Sets if we want to enable invited yielding in u-turn.");

DEFINE_bool(planning_enable_occlusion_higher_precedence_crosswalk, true,
            "Sets if we want to enable occlusion cautious driving strategy for "
            "higher-precedence crosswalks in non-right-turn scenarios.");

DEFINE_bool(
    planning_enable_occlusion_lower_precedence_crosswalk_extreme_occlusion,
    true,
    "// Sets if we want to enable occlusion cautious driving strategy for "
    "lower-precedence crosswalks for extreme occlusion.");

DEFINE_bool(planning_enable_occlusion_grid_clustering, true,
            "Sets if we want to enable occlusion point clustering in the "
            "occlusion map.");

DEFINE_bool(planning_enable_honk_reasoner, true,
            "Sets if we want to enable honk reasoner in decoupled maneuvers.");

DEFINE_bool(
    planning_enable_lane_follow_center_line_smoother, false,
    "If true, will smooth the center line for lane follow lane sequence");

DEFINE_bool(planning_enable_cost_engine, true,
            "If true, use cost engine for edge cost estimation.");

DEFINE_bool(planning_enable_engage_maneuver_sim, false,
            "If true, the planner will enable engage maneuver in simulation.");

DEFINE_bool(planning_enable_semantic_map, false,
            "If true, the planner will execute semantic map inference.");

DEFINE_bool(planning_enable_semantic_map_output_debug, false,
            "If true, the planner will output semantic map debug image to "
            "local folder.");

DEFINE_bool(planning_load_routing_constraint_from_topic, false,
            "If true, the planner will load routing constraint from topic "
            "rather than config.");

DEFINE_bool(planning_display_engage_predicted_ego_bounding_box, false,
            "If true, the planner will show predicted ego bounding boxes in "
            "engage maneuver.");

DEFINE_bool(decoupled_planning_enable_no_path_dcheck, true,
            "If true, DCHECK no path for decoupled planner.");

DEFINE_bool(planning_enable_cloud_cell_request, true,
            "If true, the planner will allow to request cell from cloud.");

DEFINE_bool(
    planning_enable_multiple_alternative_lane_follow_sequences, false,
    "If true, generate one sequence for each extra drivable fork lane.");

DEFINE_bool(planning_enable_forcing_selecting_ml_path, false,
            "If true, we will only pass the ml backup path to the speed "
            "pipeline when it is available.");

DEFINE_bool(planning_enable_add_6_ml_path, false,
            "If true, we will add at most 6 ml backup path options.");

DEFINE_bool(
    planning_enable_forcing_selecting_ml_path_with_cross_lane, false,
    "If true, and |planning_enable_forcing_selecting_ml_path| is true, we will "
    "only pass the ml backup path to the speed pipeline when it is available "
    "in lane_keep and corss_lane bahavior.");

DEFINE_bool(planning_disable_ml_path_filter, false,
            "If true, we will genereate ml path option without filtering.");

DEFINE_bool(planning_enable_ml_path_2_0, true,
            "If true, we will enable ML 2.0 in a very large scope, excluding "
            "XLN, LC, PIPO, MRC etc.");

DEFINE_bool(
    planning_add_ml_path, true,
    "If true, we will add another path option for the trajectory selection "
    "that skips the path search and use ml planner output as the reference "
    "line. It is ML 2.0.");

DEFINE_bool(planning_add_ml_path_in_upl_turn, false,
            "If true, enable ML 2.0 in all upl turns.");

DEFINE_bool(planning_enable_ml_trajectory, false,
            "If true, passes ML planner output trajectory (i.e. ML 3.0) as an "
            "additional diversity to selection.");

DEFINE_bool(planning_enable_drop_ml_path_due_to_hb_collision, true,
            "If true, drop ml path if the extended reference path collides "
            "with hard boundary.");

DEFINE_bool(
    planning_enable_drop_ml_path_when_no_ignore_homotopy, false,
    "If true, drop ml path when reasoing doesn't provide ignore homotopy.");

DEFINE_bool(planning_enable_ml_trajectory_init_state_check, false,
            "If true, drop ml trajectory when its init state has diverged from "
            "planner.");

DEFINE_bool(
    planning_enable_path_similarity_deduplicate, true,
    "If true, the similar path options will be dropped before speed pipeline.");

DEFINE_bool(planning_enable_reference_path_trimming, true,
            "If true, the long reference path will be trimmed.");

DEFINE_bool(planning_enable_fallback_path, false,
            "If true, add a new fallback path option.");

DEFINE_bool(planning_enable_upl_big_turn_path, true,
            "If true, add a new upl big turn path option.");

DEFINE_bool(planning_enable_upl_small_turn_path, false,
            "If true, add a new upl small turn path option.");

DEFINE_bool(planning_enable_intermediate_speed_search_result_debug, false,
            "If true,"
            "generate intermediate speed search result into planning debug");

DEFINE_bool(planning_enable_cost_map_based_lane_sequence_generation, true,
            "If true, generate lane sequences based on cost map.");

DEFINE_bool(planning_save_cost_map_as_dot_file, false,
            "If true, cost maps are saved as dot files.");

DEFINE_bool(planning_enable_decoupled_pull_out, true,
            "If true, trigger pull out behavior in decoupled planner.");

DEFINE_bool(planning_pull_out_auto_confirm_in_sim, true,
            "If true, auto confirm pull out in simulation mode.");

DEFINE_bool(planning_should_post_process_waypoint_availability, true,
            "If true, will use post processing logic to update waypoint "
            "availability for waypoint assist.");

DEFINE_bool(planning_enable_semantic_corridor_ray_casting_result_debug, false,
            "If true, generate ray casting result of semantic corridor into "
            "path opt debug.");

DEFINE_bool(planning_enable_decoupled_xlane_nudge, true,
            "If true, ego will be allowed to execute cross lane nudge by "
            "borrowing neighbor lanes in lane keep behavior.");

DEFINE_bool(planning_enable_xlane_nudge_dynamic_agent_v2, true,
            "If true, will enable xlane nudge dynamic agent v2.");

DEFINE_bool(planning_enable_extra_pure_xlane_nudge_homotopy, true,
            "If true, will enable to generate a pure xlane nudge homotopy.");

DEFINE_bool(planner_enable_larger_lateral_xlane_nudge, true,
            "If true, will enable larger lateral xlane nudge.");

DEFINE_bool(planning_enable_active_reroute_in_regional_path_generation, false,
            "If true, enable to active reroute when current regional path is "
            "not safe.");

DEFINE_bool(planning_speed_solver_output_non_gap_align_solution_for_lane_change,
            true,
            "If true, we can allow speed solver to output both non gap align "
            "and gap align solutions.");

DEFINE_bool(
    planning_enable_lane_change_gap_align_diversity, true,
    "If true, we can produce extra diversity for lane change gap align.");

DEFINE_bool(planning_enable_gap_align_urgency_score_for_lane_change, true,
            "If true, we can apply gap align urgency score to all lane change "
            "modules.");

DEFINE_bool(planning_enable_extra_speed_diversity, true,
            " If true, we can produce one more extra diversity.");

DEFINE_bool(planning_enable_risk_signal, true,
            " If true, we can produce risk signal in selection.");

DEFINE_bool(planning_enable_decoupled_reverse_driving, true,
            "If true, decoupled reverse driving mode will be invoked.");

DEFINE_bool(
    planning_enable_trajectory_guider_diversity, false,
    "If true, the trajectory guider will provide path diversity in the turns.");

DEFINE_bool(planning_enable_trajectory_guider_limiter, false,
            "If true, the trajectory guider will generate speed limiter.");

DEFINE_bool(
    planning_enable_const_speed_for_conflict_resolver, true,
    "If true, planner will use a profile that tends to maintain the current "
    "speed and passively reacts to other agents as one of the candidates in "
    "the conflict resolver.");

DEFINE_bool(
    planning_enable_scr_search_for_conflict_resolver, true,
    "If true, planner will enable scr seach to find a conflict "
    "resolver candidate that tends to pass the closer unavoidable conflicting "
    "object and yield to a farther agent.");

DEFINE_bool(
    planning_enable_max_speed_for_conflict_resolver, true,
    "If true, planner will use a profile that drives as fast as possible "
    "at discomfort 1 as one of the candidates in the conflict resolver.");

DEFINE_bool(planning_enable_emergency_brake, true,
            "If true, planner will produce emergency brake signal when "
            "necessary and pass it down to control.");

DEFINE_bool(planning_enable_extra_full_stop, true,
            "If true, planner will try to brake harder than FULL_STOP but "
            "not as hard as emergency brake.");

DEFINE_bool(planning_enable_continuous_severity_in_conflict_resolver, false,
            "If true, will evaluate the risk using the continuous severity "
            "value instead "
            "of the concrete severity value. Should use with "
            "planning_enable_new_severity_in_conflict_resolver");

DEFINE_bool(
    planning_enable_extra_low_discomfort_speed_search, false,
    "If true, planner speed searcher will search from discomfort -1 to 0.");

DEFINE_bool(planning_enable_relax_speed_road_limiter, true,
            "If true, the road speed limiter will break limit speed.");

DEFINE_bool(
    planning_enable_integrate_occluded_in_lane_congestion_detector, false,
    "If true, the estimated traffic flow data in occluded area from perception "
    "will be used to estimate congestion information around ego");

DEFINE_bool(
    planning_enable_road_boundaries_post_process, true,
    "If true, enable the search-based post-process for road boundaries.");

DEFINE_bool(planning_enable_relax_lane_change_constraint_to_avoid_stuck, true,
            "If true, enable the lane sequence generator to relax the "
            "constraint for stuck avoidance");

DEFINE_bool(planning_enable_cross_road_in_relax_lane_change_constraint, true,
            "If true, enable the lane sequence generator to cross road lane"
            "change when enable relax lane change constraint to avoid stuck.");

DEFINE_bool(planning_enable_jump_out_to_avoid_stuck, true,
            "If true, enable the lane sequence generator to generate a jump "
            "out lane sequence for stuck avoidance.");

DEFINE_bool(planning_enable_compensated_overlap_arc_lengths, true,
            "If true, compute and use compensated overlap arc lengths"
            "in the new decoupled architecture.");

DEFINE_bool(
    planning_enable_steering_accel_limiter, false,
    "If true, enable steering accel limiter that will apply speed limits"
    "to reduce high steering acceleration.");

DEFINE_bool(planning_enable_shift_agent_reaction, false,
            "If true, shift agent reaction may be assigned in prediction "
            "decision maker.");

DEFINE_bool(planning_enable_discomfort_for_progress_in_upl, true,
            "If true, allow discomfort for proress in upl when necessary.");

DEFINE_bool(planning_enable_agent_predicted_trajectory_route_association, false,
            "If true, globally enable agent predicted trajectory route "
            "association in world model.");

DEFINE_bool(
    planning_enable_agent_predicted_trajectory_route_association_considered_by_speed,
    true,
    "If true, globally enable agent predicted trajectory route association for "
    "agents considered by speed pipeline in the last cycle.");

DEFINE_bool(
    planning_enable_old_arch_debug_within_decoupled, false,
    "If true, enable debug of old arch maneuvers when executing decoupled "
    "trajectory.");

DEFINE_bool(planning_enable_ego_stuck_estimation, true,
            "If true, the planning will enable ego stuck estimation "
            "when the ego being in low speed and AUTO mode.");

DEFINE_bool(
    planning_enable_separate_max_speed_barrier, true,
    "If true, max speed barrier from reference profile will be separate to max "
    "speed barrier from vehicle limits, max speed repeller from reference and "
    "lateral jerk repeller to penalize lat jerk.");

DEFINE_bool(
    planning_enable_lateral_accel_effort_cost_in_speed_opt, false,
    "If true, lateral accel will be penalized in the speed optimization.");

DEFINE_bool(planning_enable_regional_path_risky_cost, false,
            "If true, enable risky costs in regional path generation.");

DEFINE_bool(
    planning_enable_signal_controlled_variable_lane, true,
    "If true, enable deciding signal controlled variable lane's drivability.");

DEFINE_bool(planning_enable_extended_regional_path, true,
            "If true, enable extending regional path from global route.");

DEFINE_bool(
    planning_enable_add_cost_to_stm_vehicle, true,
    "If true, enable add extra cost to neighbor lane near stm vehicle.");

DEFINE_bool(planning_enable_speed_search_in_path_reasoning, true,
            "If true, enable speed search in path reasoning");

DEFINE_bool(planning_populate_debug_of_speed_search_in_path_reasoning, false,
            "If true, populate debug info of speed search in path reasoning");

DEFINE_bool(planning_enable_last_frame_speed_cautious_in_path_reasoning, false,
            "If true, enable the last frame speed cautious when we estimate "
            "the final speed profile in s-t planner");

DEFINE_bool(planning_enable_last_frame_avoid_region_in_path_reasoning, false,
            "If true, enable last frame avoid region when we estimate final "
            "profile in s-t planner.");

DEFINE_bool(planning_enable_lane_change_tree_search, true,
            "If true, enable the tree search for lane change by default.");

DEFINE_bool(
    planning_enable_daytime_speed_limit, true,
    "If true, enable decreasing max speed limit to 50 km/h at night time. "
    "Otherwise, the max speed limit at night time will be 60 km/h, which is "
    "the same as day time.");

DEFINE_bool(planning_enable_filtering_out_occupancy_above_ego, false,
            "If true, enable filtering occupancy that is far above the ego.");

DEFINE_bool(
    planning_enable_use_soft_constraint_for_better_not_drive, true,
    "If true, only soft constraint will be added for better_not_drive object.");

DEFINE_bool(
    planning_enable_ml_guidance_corridor_in_uturn, false,
    "If true, enable the logic of using the ML guidance corridor in uturn.");

DEFINE_bool(
    planning_enable_soft_strict_constraint_during_lane_change, true,
    "If true, enable using soft and strict constraint during lane change.");

DEFINE_bool(
    planning_enable_search_result_nudge_info, false,
    "If true, enable the usage of nudge info provided by searched path.");

DEFINE_bool(planning_enable_object_occupancy_state, false,
            "If true, enable ObjectOccupancyStateMap usage instead of "
            "AgentInLaneState in path reasoning.");
DEFINE_bool(
    planning_enable_disable_waypoint_assist_usage_for_end_of_laneseq, false,
    "If true, will set waypoint assist unavailable for end of lane sequence "
    "scenario.");

DEFINE_bool(planning_disable_light_assist_detour_usage_for_pull_out, true,
            "If true, will set light assist detour unavailable for pull out "
            "scenario.");

DEFINE_bool(planning_disable_light_assist_detour_usage_for_waypoint_assit, true,
            "If true, will set light assist detour unavailable for waypoint "
            "assist scenario.");

DEFINE_bool(planning_enable_waypoint_assist_recommend_replan, true,
            "If true, will enable waypoint assist recommend replan.");

DEFINE_bool(planning_enable_junction_speed_limits, true,
            "If true, add cautious speed limits for junctions.");

DEFINE_bool(
    planning_enable_backward_direction_key_unstuck_behavior, true,
    "If true, enable backward direction key to trigger unstuck behavior.");

DEFINE_bool(planning_enable_analyzing_free_range, true,
            "If true, enable free range usage in path search sample arc "
            "lengths generation.");

DEFINE_bool(planning_deprecate_agent_in_lane_state, false,
            "If true, the agent in lane state will not be written into lane "
            "sequence info.");

DEFINE_bool(planning_enable_generate_neighbor_lane_sequence_proposal, true,
            "If true, allow planner to request new lane keep sequence proposal "
            "to switch lane sequence.");

DEFINE_bool(planning_enable_road_exit_cell, true,
            "If true, enable pullover to use stop cell in road exit.");

DEFINE_bool(planning_enable_data_driven_bvp_hint, true,
            "If true, enable data-driven BVP Hint for state lattice search.");

DEFINE_bool(planning_enable_publish_stuck_scene_request, true,
            "If true, enable to publish stuck scene request.");

DEFINE_bool(planning_enable_kinematic_pruning, true,
            "If true, enable kinematic pruning for state lattice search.");

DEFINE_bool(planning_enable_restore_regional_path_from_seed, true,
            "If true, enable to restore regional path from seed.");

DEFINE_bool(planning_enable_allow_global_route_lanes_rejected, true,
            "If true, enable to allow global route lanes to be rejected.");

DEFINE_bool(planning_enable_overlap_v2, true, "if true, enable overlap v2.");

DEFINE_bool(
    planning_enable_start_to_move_repulsion, true,
    "if true, enable to add repulsion for start-to-move agents when nudging");

DEFINE_bool(planning_lateral_bound_for_stm_repulsion, true,
            "If true, set lateral bound for stm repulsion to prevent HS");

DEFINE_bool(planning_enable_imaginary_agent_repulsion, true,
            "if true, enable to add repulsion for imaginary agents");

DEFINE_bool(planning_enable_smooth_stm_repulsion, true,
            "If true, enable smooth the stm repulsion");

DEFINE_bool(planning_enable_rear_end_agent_repulsion, true,
            "if true, enable to add repulsion for rear-end agents.");

DEFINE_bool(planning_enable_curvature_based_road_precedence, true,
            "If true, when adding rear-end repulsions, we consider road "
            "precedence by curvature info.");

DEFINE_bool(
    planning_enable_xlane_nudge_region_repulsion, false,
    "if true, enable to add repulsion for stuck region during xlane nudge");

DEFINE_bool(
    planning_enable_crawl_interested_agent_repulsion, true,
    "if true, enable to add repulsion for interested agent during crawl");

DEFINE_bool(planning_enable_child_ped_repulsion, false,
            "if true, add repulsion for ped child");

DEFINE_bool(planning_enable_multi_traj_cut_in_repulsion, true,
            "if true, enable to add repulsion for low-likelihood cut-in traj");

DEFINE_bool(planning_enable_faked_cut_in_traj_repulsion, true,
            "If true, add repulsions for low-likelihood cut-in traj.");

DEFINE_bool(planning_enable_boundary_extension_for_cut_in_repulsion, true,
            "if ture, allow extend boundary for cut-in repulsions.");

DEFINE_bool(planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy, true,
            "If true, we compute the ignore_if_nudge_failed for cut-in "
            "repulsions more aggressively.");

DEFINE_bool(
    planning_enable_cross_left_turn_agent_repulsion, true,
    "if true, enable to add repulsion for oncoming cross left turn agent.");

DEFINE_bool(planning_enable_drop_current_snapshot, true,
            "If true, enable to drop current snapshot.");

DEFINE_bool(planning_enable_drop_current_path, true,
            "If true, enable to drop current path.");

DEFINE_bool(planning_enable_frame_drop_for_consecutive_snapshot_input_delay,
            false, "If true, enable to drop consecutive snapshot delay.");

DEFINE_bool(planning_enable_frame_drop_for_consecutive_path_input_delay, false,
            "If true, enable to drop consecutive path delay.");
DEFINE_bool(
    planning_enable_populate_stuck_signal_based_on_ra_instruction, false,
    "If true, enable to populate stuck signal based on ra instruction.");

DEFINE_bool(planning_enable_gradual_init_state_regulation, false,
            "If true, enable gradual regulation of initial state in speed "
            "reference generator.");

DEFINE_bool(
    planning_enable_static_agent_decision_branching, true,
    "If true, enable static agent decision branching in tunnel search.");

DEFINE_bool(planning_enable_lane_encroach_nudge, true,
            "If true, enable lane encroach nudge for dangerous scene.");

DEFINE_bool(planning_enable_lane_encroach_nudge_at_high_speed, true,
            "If true, enable lane encroach nudge at high speed.");

DEFINE_bool(planning_enable_ttc_prevent_rear_front_collision, true,
            "If true, enable the ttc computation used to prevent rear front "
            "collision.");

DEFINE_bool(planner_enable_soften_all_type_of_yielding_crossing, false,
            "If true, enable soften prediction for all type of yielding "
            "crossing agent.");

DEFINE_bool(planning_enable_narrow_meeting_process, true,
            "If true, enable narrow oncoming process.");

DEFINE_bool(planning_enable_strong_repulsion_in_narrow_meeting, true,
            "If true, enable strong repulsion in narrow meeting.");

DEFINE_bool(planning_enable_align_st_buffer_in_path_pipeline, false,
            "If true, enable align st buffer in path pipeline.");

DEFINE_bool(
    planning_enable_waypoint_cyclist_overtaking_repulsion, false,
    "If true, enable cyclist overtaking repulsion in WAYPOINT behavior.");

DEFINE_bool(
    planning_enable_path_reasoning_lateral_estimation, false,
    "If true, enable the estimation of lateral effort in path reasoning.");

DEFINE_bool(planning_enable_lateral_estimation_visual, false,
            "If true, enable the visual of lateral_estimation in st planner.");

DEFINE_bool(planning_enable_lane_sequence_proposal_for_lane_change, true,
            "If true, enable generating lane sequences addressing the "
            "proposals from lane change.");

DEFINE_bool(planning_enable_waypoint_assist_check_point_incremental, false,
            "If true, enable check reference point incremental along the "
            "selected lane sequence.");

DEFINE_bool(planning_enable_waypoint_assist_drive_on_opposite_lanes, true,
            "If true, enable ego drive on the opposite lanes.");

DEFINE_bool(planning_enable_pull_out_auto_reversing, true,
            "If true, enable pull out auto reversing logic.");

DEFINE_bool(
    planning_enable_mlplanner_trajectory_evaluation, false,
    "If true, use MLPlanner trajectory and ignore planning trajectory in "
    "Planner module. Only take effect in offboard simulation.");

DEFINE_bool(planning_disable_planner_trajectory, false,
            "If true, planning will not publish trajectory and only publish "
            "trajectory result.");

DEFINE_bool(planning_use_stop_cell_from_cloud, true,
            "If true, enable to use pullover stop cell from cloud.");

DEFINE_bool(planning_enable_update_lane_congestion_reasoner, true,
            "If true, enable update lane congestion reasoner.");

DEFINE_bool(planning_enable_soft_nudge, true,
            "If true, enable soft nudge related feature.");

DEFINE_bool(
    planning_enable_forward_direction_key_unstuck_behavior, true,
    "If true, enable forward direction key to trigger unstuck behavior.");

DEFINE_bool(
    planning_enable_migrate_fod_constraints_in_speed_pipeline, true,
    "If true, the constraints for FoD will be migrated in speed pipeline.");

DEFINE_bool(planning_enable_spatial_temporal_state_lattice_search, false,
            "If true, enable spatial-temporal state lattice search.");

DEFINE_bool(
    planning_enable_fod_interaction, true,
    "If true, planner will not ignore fod and starts to interact with them.");
DEFINE_bool(
    planning_provide_multiple_fod_intentions_for_better_not_drive_object, false,
    "If true, planner will generate multiple fod intentions to better not "
    "drive objects");
DEFINE_bool(planning_enable_hdmap_dynamic_data_update, false,
            "If true, planner will process hdmap changed data for pnc map.");

DEFINE_bool(planning_enable_shadow_mode_hdmap_dynamic_data_update, false,
            "If true, planner will process hdmap changed data for pnc map and "
            "may not use the data for planning.");

DEFINE_bool(planning_enable_planner_receive_mrc_node_info, true,
            "If true, enable planner respond to mrc_node's info.");

DEFINE_bool(planning_enable_planner_respond_mrc_request_on_highway, false,
            "If true, enable planner respond to mrc request on highway.");

DEFINE_bool(planning_enable_progress_cost_waiting_in_traffic_reasoning, true,
            "If true, selection uses waiting in traffic reasoning to adjust "
            "progress cost.");

DEFINE_bool(
    planning_enable_progress_efficiency_cost, false,
    "If true, selection uses progress_efficiency_cost to replace original "
    "progress_immediate & stuck_cost.");

DEFINE_bool(
    planning_enable_process_ctz_in_forward_unstuck, true,
    " If true, forward stuck scene analyzer will reason ctz to unstuck.");

DEFINE_bool(planning_use_ml_planner_in_junction_lane_selection, true,
            "If true, the junction lane selection will consider ml planner's "
            "target lane recommendation.");

DEFINE_bool(
    planning_enable_gpu_lateral_clearance_generator, false,
    "If true, planner will run gpu version lateral clearance generator.");

DEFINE_bool(planning_enable_gaussian_model_2d, false,
            "If true, enable gaussian model to calculate collision related "
            "probability.");

DEFINE_bool(planning_enable_recommend_replan_info_to_routing, true,
            "If true, enable planner module recommend replan info for routing "
            "to make up cost map.");

DEFINE_bool(planning_enable_uturn_unstuck, true,
            "If true, planner will run u-turn unstuck planner and do dry "
            "steering and reversing to unstuck.");

DEFINE_bool(
    planning_enable_kturn_replan_after_reverse_stuck, false,
    "If true, enable Kturn replan after reverse stuck motion plan generation.");

DEFINE_bool(
    planning_enable_ra_pass_through_map_change_area, true,
    "If true, planner enables passing through map change area by RA waypoint.");

DEFINE_bool(
    planning_enable_ra_pass_through_perception_cz, true,
    "If true, planner enables passing through perception CZ by RA waypoint.");

DEFINE_bool(planning_enable_ra_waypoint_exit_reasoning_within_cz, false,
            "If true, RA enable waypoint exit reasoning about within CZ.");

DEFINE_bool(planning_enable_ra_for_map_change_area_about_traffic_light, true,
            "If true, planner enables passing through map change area about "
            "traffic light.");

DEFINE_bool(planning_enable_ra_for_perception_fp_ignoring, true,
            "If true, planner enables RA for perception FP to OPS for "
            "confirming ignore and unstuck.");

DEFINE_bool(planning_enable_ra_request_for_map_change_area_by_routing, true,
            "If true, planner enables trigger RA request about map change area "
            "during moving.");

DEFINE_bool(planning_enable_sim_planner_init_state_recovery_mode, false,
            "If true, planner will be in planner-init-state-recovery mode in "
            "simulation.");

DEFINE_bool(
    planning_disable_snapshot_seed_in_sim_planner_init_state_recovery_mode,
    false,
    "If true, planner will stop processing snapshot seed in "
    "planner-init-state-recovery mode in simulation.");

DEFINE_bool(
    planning_disable_planning_seed_in_sim_planner_init_state_recovery_mode,
    false,
    "If true, planner will stop processing planning seed in "
    "planner-init-state-recovery mode in simulation.");

DEFINE_bool(planning_enable_relax_lane_direction_when_stuck, true,
            "If true, allow relax lane direction when stuck. For example, turn "
            "right from a straight lane when all right turn lane are blocked.");

DEFINE_bool(planning_enable_recommend_replan_for_regional_path_request, true,
            "If true, planner will enable trigger recommend replan for "
            "regional path to make up cost map in junction.");

DEFINE_bool(planning_enable_assist_stuck_checking_for_parked_cars_near_hb, true,
            "If true, the assist stuck detector enable checking yielding parked"
            " cars near road boundary in the FP queuing rule.");

DEFINE_bool(planning_enable_assist_stuck_detector_config_for_recall, false,
            "If true, the assist stuck detector use config for recall orient.");

DEFINE_bool(
    planning_enable_assist_stuck_detector_config_by_version_type, true,
    "If true, the assist stuck detector use config according to version type.");

DEFINE_bool(planning_enable_assist_stuck_forcing_recall_default_trigger_cycle,
            false,
            "If true, the assist stuck forcing recall strategy uses default "
            "trigger cycle.");

DEFINE_bool(planner_enable_assist_stuck_detector_working_in_assist_mode, true,
            "If true, the assist stuck still working in assist mode, but send "
            "fake request.");

DEFINE_bool(planning_disable_assist_stuck_fp_queuing_when_fn_selection, true,
            "If true, the assist stuck don't response to FP queuing when FN "
            "selection is activate.");

DEFINE_bool(
    planning_enable_assist_stuck_occlusion_in_stationary_reasoning, false,
    "If true, the assist stuck stationary reasoning considers occlusion.");

DEFINE_bool(planning_enable_assist_stuck_traffic_flow_queuing_reasoning, false,
            "If true, the assist stuck queuing reasoning considers current "
            "lane traffic flow.");

DEFINE_bool(
    planning_enable_assist_stuck_yield_varying_traffic_flow, true,
    "If true, the assist stuck enable yield varying tf in fp yielding rule.");

DEFINE_bool(planning_enable_assist_stuck_geometric_features_normalization, true,
            "If true, the assist stuck geometric features would be normalized "
            "by the ego's state.");

DEFINE_bool(planning_enable_assist_stuck_adapt_dnn_features, true,
            "If true, the assist stuck adapt dnn features for inference.");

DEFINE_bool(
    planning_enable_assist_stuck_dumps_tensor_dict, true,
    "If true, the assist stuck dumps features' tensor dict for training.");

DEFINE_bool(planning_enable_using_neighbor_lane_to_help_evaluate_density_cost,
            true,
            "If true, the stuck detector will enable using neighbor lane "
            "traffic density"
            "to help evaluate density cost.");

DEFINE_bool(planning_enable_enlarge_lat_gap_for_xlane_nudge, false,
            "If true, enable enlarge lateral gap for certain object when doing "
            "xlane nudge.");

DEFINE_bool(planning_enable_enlarge_lat_gap_for_lane_change, true,
            "If true, enable enlarge lateral gap for certain object when doing "
            "lane change.");

DEFINE_bool(
    planning_enable_lane_sequence_rollouts, true,
    "If true, enable generating lane sequence via sampling and selection");

DEFINE_bool(planning_enable_enlarge_lat_gap_for_pull_over, false,
            "If true, enable enlarge lateral gap for certain object when doing "
            "pull over.");

DEFINE_bool(planning_enable_replan_for_desired_route, true,
            "If true, enable planner node trigger replan with desired route "
            "info when optimal regional path is diverged with global route.");

DEFINE_bool(
    planning_save_risk_model_framewise_io, false,
    "If true, the risk model in selection will populate framewise input and "
    "output at a specified timestamp in the selection debug proto of "
    "sim output bag.");

DEFINE_int64(
    planning_selection_framewise_timestamp_to_save, 0,
    "The timestamp at which the framewise input and output for selection "
    "risk model will be populated to sim output bag.");

DEFINE_bool(path_planning_skip_ignore_homotopy, false,
            "If true, we skip the ignore homotopy. This would only be "
            "used when simulation.");

DEFINE_bool(planning_enable_cone_understanding, true,
            "If true, enable cone understanding module.");

// TODO(fengkaijun): Remove this flag after the logic has been land in release.
DEFINE_bool(
    planning_enable_remote_assist_fp_pull_over_about_stationary_object, true,
    "If true, enable remote assist fp pull over about stationary object");

// TODO(wutulin): Remove this flag after the logic has been land in release.
DEFINE_bool(planning_selection_human_likeness_sort_candidate_trajectories,
            false,
            "If true, selection's human likeness cost shall first sort its "
            "trajectories.");

DEFINE_bool(
    planning_use_old_stuck_feature_extractor_length_only_in_sim, false,
    "If true, use old stuck feature extractor length, 20, for simulation "
    "of remote assist auto trigger.");

DEFINE_bool(planning_enable_fn_selection_in_stuck_detect, true,
            "If true, fn_selection strategy will be enabled to trigger RA.");

DEFINE_bool(planning_enable_enlarging_scope_of_eb_for_vru, false,
            "If true, the scope of allowing EB is larger for VRU.");

DEFINE_bool(planning_enable_waypoint_assist_request_to_avoid_stuck, true,
            "If true, enable send waypoint assist request to avoid stuck.");

DEFINE_bool(planning_enable_in_junction_alternative_lane_change_sequence, false,
            "If true, enable generating in-junction alternative lane change "
            "sequence for single-connected straight turns.");

DEFINE_bool(planning_enable_selection_longitudinal_speed_diff_cost, true,
            "If true, enable selection's longitudinal speed diff cost.");

DEFINE_bool(
    planning_enable_speed_cautious_driving_reasoner_remote_speed_limit, true,
    "If true, enable speed cautious driving reasoner to consider remote speed "
    "limit.");

DEFINE_bool(planning_path_speed_are_parallel, true,
            "If true, path planner can run in parallel with speed planner.");

DEFINE_bool(planning_copy_previous_seed, true,
            "If true, copy the previous finished iteration seed to current "
            "seed when a frame starts");

DEFINE_bool(planning_copy_speed_seeds, false,
            "If true, copy the SpeedSeed and SelectionSeed from last frame to "
            "current frame at the beginning of Speed nodelet start.");

DEFINE_bool(planning_seed_always_read_from_current, true,
            "If true, all read to LastFinished version of seed will actually "
            "read from current version.");

DEFINE_bool(planning_seed_check_and_complain_read_before_write, false,
            "If true, checks the last available version and complain when "
            "trying to read a seed version which has not been written.");

DEFINE_bool(planning_seed_check_timestamp_before_apply, false,
            "If true, checks the timestamp before apply the planning seed from "
            "the bag, if the timestamp does not match, the bag seed won't "
            "overwrite the in-memory seed.");

DEFINE_bool(
    planning_enable_reasoning_boundaries_post_process, true,
    "If true, enable the search-based post-process for reasoning boundaries.");

DEFINE_bool(
    planning_enable_populate_cz_as_creep_around_object, true,
    "If true, enable populating construction zone as creep around object.");

DEFINE_bool(planning_enable_collect_empirical_road_blockage_raw_data, true,
            "If true, enable collect empirical road blockage raw data from "
            "world model.");

DEFINE_bool(planning_enable_collect_empirical_fix_point_unstuck_raw_data, true,
            "If true, enable collect empirical fix point unstuck raw data from "
            "world model.");

DEFINE_bool(planning_enable_upload_empirical_raw_data, true,
            "If true, enable upload empirical raw data to cloud.");

DEFINE_bool(planning_enable_new_global_route_selection, true,
            "If true, enable planner use new global route selection method.");

DEFINE_bool(planning_enable_map_change_lane_keep, false,
            "If true, enables reading map change area's lane keep lane "
            "sequence from routing.");

DEFINE_bool(planning_enable_pull_out_blocked_by_bus_pick_up_drop_off, true,
            "If true, enable that pull out can be blocked by bus picking up "
            "and dropping off.");

DEFINE_bool(planning_enable_creep_connect_ra, true,
            "If true, enable planner use creep signals to trigger RA.");

DEFINE_bool(planning_enable_creep_through_multiple_obstacles, true,
            "If true, enable planner use creep signals to trigger RA.");

DEFINE_bool(planning_enable_eh_node, true,
            "If true, enable eh node and disable eh nodelet.");

DEFINE_bool(planning_enable_rollout_generate_candidates_for_blockage_avoidance,
            true,
            "If true, enable lane sequence rollout generate candidates for "
            "blockage avoidance.");

DEFINE_bool(planning_disable_static_tbb_thread_count, false,
            "If true, disable static defined tbb thread count.");

DEFINE_bool(planning_enable_kinematic_jump_in_guidance_search, true,
            "If true, enable planner search for kinematic jump in guidance in "
            "pull over.");

DEFINE_bool(planning_enable_kinematic_jump_in_guidance_with_upstream_hint, true,
            "If true, enable planner generate jump in guidance with upstream "
            "hint, and analysis target line traffic info during pull over.");

DEFINE_bool(planning_enable_latching_jump_in_guidance, true,
            "If true, enable planner latch jump in guidance in pull over "
            "during execution.");

DEFINE_bool(planning_enable_kinematic_jump_in_soft_latching, true,
            "If true, enable planner softly latch jump in guidance in pull "
            "over during execution.");

DEFINE_bool(planning_enable_path_emergency_swerve, true,
            "If true, path module can generate higher curvature path to handel "
            "cases that Ego need emergency swerve. Only consider ped and cyc.");

DEFINE_bool(planning_enable_path_emergency_swerve_for_vehicle, false,
            "If true, path module can generate higher curvature path to handel "
            "cases that Ego need emergency swerve for vehicle.");

DEFINE_bool(planning_enable_global_object_manager_filtering, true,
            "If true, enable filtering logic in global object manager.");

DEFINE_bool(planning_enable_uturn_drivable_space_expand, true,
            "If true, path search can expand the drivable space boundary in "
            "u-turn scenarios.");

DEFINE_bool(planning_enable_reason_reference_points_to_avoid_stuck, true,
            "If true, enable reason reference points to avoid stuck.");

DEFINE_bool(planning_enable_current_lane_reasoner, false,
            "If true, enable planner use current lane reasoner.");

DEFINE_bool(planner_use_contour_for_dynamic_objects, false,
            "If ture, enable contours for dynamic objects in predicted_poses");

DEFINE_bool(planning_enable_uturn_curvature_diversity, true,
            "If true, enable the curvature diversity in u-turn.");

DEFINE_bool(planning_enable_remote_assist_fp_pull_over_about_order, true,
            "If true, enable remote assist fp pull over about order.");

DEFINE_bool(planning_enable_pull_over_time_out_abort, false,
            "If true, enable pull over time out abort due to constant stuck in "
            "road for yielding traffic.");

DEFINE_bool(planning_enable_creep_narrow_passages, true,
            "If true, enable creeping through narrow passages by reducing pass "
            "RLG under the critical value.");

DEFINE_bool(planning_enable_remote_assist_fp_on_turn_about_temp_parked_car,
            true,
            "If true, enable remote assist fp on turn about temp parked cars.");

DEFINE_bool(planning_disable_remote_assist_stuck_cloud_config, true,
            "If true, enable configuration form cloud for RA stuck detector.");

DEFINE_bool(planning_enable_congestion_api_in_assist_stuck_detector, false,
            "If true, enable congestion api for RA stuck detector.");

DEFINE_bool(planning_enable_cross_road_lane_change, true,
            "If true, enable cross road lane change.");

DEFINE_bool(planning_enable_dense_lateral_clearance_debug, false,
            "If true, enable dense lateral clearance debug. Otherwise, a "
            "down-sampling factor will applied to the debug recording.");

DEFINE_bool(planning_enable_physical_boundary_lateral_clearance_debug, true,
            "If true, enable physical boundary clearance debug..");

DEFINE_bool(planning_enable_agent_lateral_clearance_debug, false,
            "If true, enable agent lateral clearance debug.");

DEFINE_bool(planning_enable_agent_lateral_clearance_generator_debug, false,
            "If true, enable agent lateral clearance generator debug.");

DEFINE_bool(planning_enable_sl_nudge_costing, true,
            "If true, enable SL nudge corridor costing instead of XY nudge "
            "corridor costing.");

DEFINE_bool(planning_use_ego_sampled_points_for_sl_nudge_costing, false,
            "If true, use ego's sampled points for sl nudge costing. If false, "
            "use ego's disks instead.");
DEFINE_bool(
    planning_enable_dense_agent_clearance_extracted_data_debug, false,
    "If true, enable dense agent clearance extracted data debug. Otherwise, a "
    "down-sampling factor will applied to the debug recording.");

DEFINE_bool(planning_enable_path_nudge_back, true,
            "If true, path module can generate nudge back constraints for "
            "agent that need to be yield.");

DEFINE_bool(planning_enable_selection_longitudinal_limit_check, false,
            "If true, selection module shall reject trajectories due to "
            "longitudinal limit violation.");

// If false, enable tbb parallel in simulation
DEFINE_bool(planning_disable_tbb_parallelism, false,
            "If false, enable tbb parallel in simulation");

DEFINE_bool(planning_enable_offroad_objects_filtering, false,
            "If true, planning will deal with the off-road objects.");

DEFINE_bool(planning_enable_route_topological_info_for_temp_parked, true,
            "If true, enable use route topological info for temp parked.");

DEFINE_bool(planning_enable_solid_line_hardening_for_large_vehicle, true,
            "If true, enable use solid line hardening for large vehicle.");

DEFINE_bool(planning_mock_human_speed, false,
            "Whether to use human driving speed as profile");

DEFINE_bool(planning_mock_human_path, false,
            "Whether to use human driving path for candidates");

DEFINE_bool(
    planning_assign_lowest_cost_to_optimal_lane_follow_sequence, false,
    "If true, assign the lowest cost among all lane follow sequences to "
    "the optimal lane follow sequence.");

DEFINE_bool(planning_use_cloud_cell_in_simulation, false,
            "If true, enable to use cloud cell from bag in simulation.");

DEFINE_bool(
    planning_enable_dense_traffic_creeping, true,
    "If true, Ego will creep to proceed more urgently in dense traffic.");

DEFINE_bool(
    planning_disable_honk_shadow_mode_for_core_planner, true,
    "If false, core planner will disable sending honk signal to downstream "
    "control module and will only add honk debug information.");

DEFINE_bool(planning_enable_general_risk_model_for_pull_over_abort, false,
            "If true, pull over abort trigger will additionally consume output "
            "of the general selection risk model.");

DEFINE_bool(planning_enable_speed_based_rlg_for_ped, true,
            "If true, path reasoning will increase nudge RLG for ped when Ego "
            "speed is high.");

DEFINE_bool(planning_enable_speed_based_rlg_for_large_vehicle, true,
            "If true, path reasoning will increase nudge RLG for large vehicle "
            "when Ego speed is high.");

DEFINE_bool(planning_enable_tidal_flow_lane_stuck_request, true,
            "If true, tidal flow lane stuck request will be published.");

DEFINE_bool(planning_enable_signed_lateral_gap_to_ego_poses_in_debug, false,
            "If true, the signed_lateral_gap_to_ego_poses and "
            "signed_lateral_gap_to_first_ego_pose_boxes field in the overlap "
            "proto will be saved in the speed solver debug.");

DEFINE_bool(
    planning_enable_speed_based_rlg_for_cyclist, true,
    "If true, path reasoning will increase nudge RLG for cyclist when Ego "
    "speed is high.");

DEFINE_bool(planning_enable_speed_based_rlg_for_oncoming_cyclist, true,
            "If both planning_enable_speed_based_rlg_for_cyclist and this flag "
            "is true, path reasoning will increase nudge RLG for oncoming "
            "cyclist when Ego speed is high.");

DEFINE_bool(planning_enable_path_generator_input_dump, false,
            "If true, path smoother dump the JukeIntegratedDdpPathGenerator "
            "input proto for path optimization tuner");

DEFINE_bool(planning_enable_ml_pull_over_gap_generator, true,
            "If true, enable ml pull over gap generator model.");

DEFINE_bool(
    planning_enable_multi_dest_pull_over, true,
    "If true, ego will plan pull over trajectories towards multiple "
    "destinations and execute the best available pull over trajectory.");

DEFINE_bool(planning_enable_pull_over_jump_in_probe, true,
            "If true, the ego can probe to the right during the pull over "
            "preparation stage.");

DEFINE_bool(planning_enable_remote_assist_connection, true,
            "If true, ra is going to use new remote connection signal.");

DEFINE_bool(planning_enable_accumulated_swerve_different_extrema, true,
            "If true, when calculating accumulated swerve cost, we will filter "
            "extremas using different strategies depending on extrema count.");

DEFINE_bool(planning_enable_impo_odd_type, true,
            "If true, the planner will allow to set impo odd type in immediate "
            "pull over.");

DEFINE_bool(
    planning_use_comfort_priority_lane_change_option, true,
    "If true, use max lateral jerk model to generate a comfort priority lane "
    "change option.");

DEFINE_bool(
    planning_enable_backup_lane_change_option, true,
    "If true, enable the backup lane change option when gap align fails.");

DEFINE_bool(planning_enable_waypoint_assist_auto_confirm_in_simulation, false,
            "If true, auto confirm waypoint assist in simulation mode.");

DEFINE_bool(
    planning_enable_dry_steering_back_for_lane_keep, true,
    "If true, enable the dry steering back when stuck in lane keep behavior.");

DEFINE_bool(planning_enable_path_reaction_to_all_unknown, false,
            "If true, path will try to nudge all unknown objects if possible.");

DEFINE_bool(
    planning_enable_lateral_clearance_out_of_physical_boundary_agent_filter,
    true,
    "If true, the lateral clearance calculation will filter out the out of "
    "physical boundary agents.");

DEFINE_bool(
    planning_enable_add_backup_lane_sequence_when_reroute, true,
    "If true, enable add backup lane sequence when regional path reroute.");

DEFINE_bool(planning_enable_add_ml_path_near_short_merge, true,
            "If true, will add one ML path when there is short merge in the "
            "near front.");

DEFINE_bool(
    planning_enable_tracking_based_cautious_driving_to_avoid_squeeze, false,
    "If true, enable tracking based speed cautious driving to avoid squeeze.");

DEFINE_bool(planning_enable_speed_efs_for_non_drivable_unknown, true,
            "If true, we allow braking mostly to EFS for non-drivable unknown "
            "subjects");

DEFINE_bool(planning_enable_new_regional_path_selection, true,
            "If true, use new regional path selection method.");

DEFINE_bool(planning_enable_new_dynamic_limit_with_double_brake_j_max, true,
            "If true, will use new xc90 dynamic limit for planning to achieve "
            "faster brake.");

DEFINE_bool(planning_enable_undercarriaging_over_fod, true,
            "If true, path reasoning will enable undercarriaging over an FoD.");

DEFINE_bool(planning_enable_search_violate_physical_boundary, true,
            "If true, the search path can violate the physical boundary at the "
            "u-turn exit with high curvature.");

DEFINE_bool(planning_enable_repulsion_for_oncoming_agent_during_pullover, true,
            "If true, path reasoning will add repulsion for oncoming agent "
            "during pullover.");

DEFINE_bool(
    planning_enable_ignore_only_in_dense_crossing_vru_scenario, true,
    "If true, path reasoning will only generate a ignore_all homotopy in dense "
    "crossing vru scenario.");

DEFINE_bool(
    planning_enable_dynamic_look_ahead_time_in_ref_generator, true,
    "If true, the reference generator will use dynamic look ahead time to "
    "better track the speed bound while generating reference profile.");

DEFINE_bool(
    planning_enable_remote_warning_signal_publish, false,
    "If true, enables publishing remote warning signal for noticing RA OPS.");

DEFINE_bool(planning_enable_remote_assist_model_vnode, true,
            "If true, enables the new remote assist model vnode.");

DEFINE_bool(planning_enable_slowmoving_object_detector_debug, true,
            "If true, will store slowmoving object detector debug.");

DEFINE_bool(planning_enable_slowmoving_object_avoidance_lane_sequence, true,
            "If true, the planner will detect slowmoving objects and generate "
            "lane sequence to avoid them.");

DEFINE_bool(planning_enable_ra_intervention_requirement_for_creep_around, true,
            "If true, unstuck handler will send ra creep around request in a "
            "intervention requirement form.");

DEFINE_bool(planning_enable_selection_risk_bnd_discount, true,
            "True if selection risk model down weight risk against "
            "better-not-drive objects.");

DEFINE_bool(planning_enable_multi_step_search, false,
            "If true, enable multi-step exploration in the state lattice path "
            "search.");

DEFINE_bool(planning_enable_planner_objects_spatial_indexing, false,
            "If true, use spatial_indexing for object quering");

DEFINE_bool(planning_selection_enable_min_distance_rear_perturbation, true,
            "If true, selection will consider distance from agent's rear "
            "corners to ego's edge in risk computation.");

DEFINE_bool(
    planning_enable_skip_nudgable_soft_blocking_object, true,
    "If true, if a soft blocking object or better not drive object is able to "
    "pass by nudge, lane sequence will not consider it as a blockage.");

DEFINE_bool(
    planning_enable_stuck_avoidance_dynamic_map_data_update, true,
    "If true, planner will process map changed data for stuck avoidance.");

DEFINE_bool(
    planning_enable_solid_line_hardening_for_dynamic_large_vehicle, false,
    "If true, enable use solid line hardening for dynamic large vehicle.");

DEFINE_bool(planning_enable_search_violate_static_agent, false,
            "If true, the search path can violate the static agent at the "
            "u-turn exit with high curvature.");

DEFINE_bool(planning_enable_search_timeout_backtracking_limiting, true,
            "If true, enable backtracking limiting when path search times out");

DEFINE_bool(planning_enable_reject_dangerous_new_route, false,
            "If true, enable planning to reject dangerous new route.");

DEFINE_bool(planning_enable_junction_lane_preference_conflict_analysis, true,
            "If true, enable conflict risk analysis in junction lane "
            "preference decision.");

DEFINE_bool(planning_enable_obstacle_range_stability_check, true,
            "If true, enable obstacle range stability check.");

DEFINE_bool(planning_enable_extended_bounding_box_for_irregular_objects, true,
            "If true, enables the use of extended bounding box for irregular "
            "shaped objects.");

DEFINE_bool(planning_discount_lateral_diff_cost_at_high_speed, false,
            "If true, selection lateral_diff cost will be discounted when ego "
            "is slow.");

DEFINE_bool(planning_enable_check_stop_cell_layer_in_simulation, false,
            "If true, check if stop cell layer is ready in simulation.");

}  // namespace planner
