#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/lateral_blocking_state_generator.h"

#include <memory>

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "av_comm/thread_pool.h"
#include "geometry/model/polyline_curve.h"
#include "math/enum.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state_generator.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/test_utils/path_reasoning_test_utils.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/planner_object/planner_objects_history.h"
#include "planner_protos/agent_intention.pb.h"

namespace planner {

namespace {

constexpr double KRequireLatGapInMeter = 0.5;

class LateralBlockingStateTest : public ::testing::Test {
 protected:
  LateralBlockingStateTest() : planner_objects_history_(20) {}

  void SetUp() override {
    // set agent pose
    const math::geometry::PolygonWithCache2d polygon_bbox(
        std::vector<math::geometry::Point2d>{
            {7.5, 0.5}, {12.5, 0.5}, {12.5, 1}, {7.5, 1}, {7.5, 0.5}},
        /*correct=*/true);
    agent_pose_ =
        AgentSnapshotPose(/*center=*/math::geometry::Point3d(10.0, 0.75, 0),
                          /*length=*/5.0, /*width=*/0.5,
                          /*height=*/0.0, /*heading=*/0.0, polygon_bbox,
                          /*should_use_contour=*/false);

    reference_line_ = math::geometry::PolylineCurve2d(
        {{0.0, 0.0}, {5.0, 0.0}, {10.0, 0.0}, {20.0, 0.0}, {100.0, 0.0}});

    // Initialize upper bound speed.
    speed::State fast_profile_init_state;
    fast_profile_init_state.t = 0.0;
    fast_profile_init_state.x = 0.0;
    fast_profile_init_state.v = 5.0;
    fast_profile_init_state.a = 1.0;
    upper_bound_speed_.push_back(fast_profile_init_state);

    int num_step = 80;
    // Initialize lower bound speed.
    lower_bound_speed_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, num_step, /*dt=*/0.1);

    speed::Limits limits(
        speed::LimitRange(/*brake_a.min=*/-2.0, /*brake_a.max=*/2.0),
        speed::LimitRange(/*accel_a.min=*/-2.0, /*accel_a.max=*/2.0),
        speed::LimitRange(/*brake_j.min=*/-3.0, /*brake_j.max=*/2.5),
        speed::LimitRange(/*accel_j.min=*/-2.0, /*accel_j.max=*/2.0),
        /*max_v=*/33.33, /*max_brake2accel_j=*/2.5);
    speed::GenerateBrakeProfile(0, num_step, limits, 0.1, &lower_bound_speed_);
    emergency_brake_speed_ = lower_bound_speed_;

    for (int i = 0; i < num_step; i++) {
      upper_bound_speed_.push_back(upper_bound_speed_.back().Move(0.1));
    }

    // Initialize ego parameter.
    ego_param_.front_bumper_arclength = 2.0;
    ego_param_.rear_axle_to_front_bumper_m = 0.5;
    ego_param_.rear_axle_to_rear_bumper_m = 0.5;
    ego_param_.width_m = 1.2;
    ego_param_.arclength_m = 1.5;
    ego_param_.plan_start_timestamp_ms = 0;

    config_.set_max_intention_horizon_ms(8000);
    config_.set_nudge_intention_longitudinal_buffer_m(5.0);
    config_.set_min_clearance_as_soft_blocking_m(0.55);
    config_.set_use_inlane_based_blocking_state(true);

    google::protobuf::TextFormat::ParseFromString(
        R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
    )",
        agent_proto_.mutable_tracked_object());
    const int64_t timestamp = 0;
    const TrafficParticipantPose pose_at_plan_init_ts(
        timestamp, agent_proto_.tracked_object());
    planner_object_ = std::make_unique<PlannerObject>(agent_proto_, timestamp,
                                                      pose_at_plan_init_ts);

    ego_bounding_box_ = math::geometry::OrientedBox2d(
        /*x=*/10.0, /*y=*/0.0, /*length=*/5.0, /*width=*/0.5, /*heading=*/0.0);

    auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
    auto planner_object_map_ptr =
        std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
    planner_object_map_ptr->emplace(planner_object_->id(), *planner_object_);
    planner_objects_history_.Update(planner_object_map_ptr, destruction_pool);
    planning_horizon_range_ = math::Range1d(0, 100);

    // Set up physical lateral clearance data.
    lateral_clearance::Clearance left_clearance;
    left_clearance.distance = 3.0;
    lateral_clearance::Clearance right_clearance;
    right_clearance.distance = -2.0;
    lateral_clearance::LateralClearance lateral_clearance{left_clearance,
                                                          right_clearance};

    const math::Range1d effective_reference_range(0.0, 20.0);
    physical_boundary_lateral_clearance_data_ =
        lateral_clearance::LateralClearanceData(
            /*resolution=*/1.0, /*max_abs_lateral_clearance=*/10.0,
            effective_reference_range,
            std::vector<lateral_clearance::LateralClearance>(
                21, lateral_clearance));
  }

  math::geometry::PolylineCurve2d reference_line_;
  AgentSnapshotPose agent_pose_;
  prediction::pb::Agent agent_proto_;
  std::unique_ptr<PlannerObject> planner_object_;
  PlannerObjectsHistory planner_objects_history_;
  std::vector<PredictedTrajectoryWrapper> predictions_;
  tbb::concurrent_unordered_map<int64_t,
                                std::vector<PredictedTrajectoryWrapper>>
      object_predictions_;
  math::geometry::OrientedBox2d ego_bounding_box_;
  math::Range1d planning_horizon_range_;
  speed::Profile emergency_brake_speed_;
  speed::Profile lower_bound_speed_;
  speed::Profile upper_bound_speed_;
  EgoInLaneParams ego_param_;
  RequiredLateralGap required_lat_gap_ = {0.0, KRequireLatGapInMeter};
  pb::LateralBlockingStateGeneratorConfig config_;
  planner::lateral_clearance::LateralClearanceData
      physical_boundary_lateral_clearance_data_;
};

// Generates the AgentInLaneStates for dynamic object.
AgentInLaneStates GenerateDynamicObject(
    const AgentSnapshotInLaneParam& init_state, int64_t init_timestamp,
    int step, int delta_t,
    pb::AgentSnapshotBlockingState blocking_state =
        pb::AgentSnapshotBlockingState::SOFT_BLOCKING) {
  AgentInLaneStates result;
  AgentSnapshotInLaneState snapshot_state;
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = false;
  result.agent_metadata = meta_data;
  snapshot_state.inlane_param = init_state;
  snapshot_state.blocking_info.blocking_state = blocking_state;
  result.tracked_state = snapshot_state;
  AgentSnapshotInLaneParam cur_param = init_state;
  AgentTrajectoryInLaneStates trajectory_states;
  const double object_width = 1.0;
  double cur_lateral_posi = 0.0;
  // The agent state evolves with initial cross and along track speed.
  for (int i = 0; i < step; i++) {
    AgentSnapshotInLaneState cur_state;
    cur_state.inlane_param = cur_param;
    cur_state.blocking_info.blocking_state = blocking_state;
    StampedAgentSnapshotInLaneState stamped_in_lane_state;
    stamped_in_lane_state.timestamp = init_timestamp + delta_t * i;
    stamped_in_lane_state.inlane_state = cur_state;
    trajectory_states.predicted_states.push_back(stamped_in_lane_state);
    // Update the in lane param for the next step.
    cur_param.start_arclength_m += 0.1 * cur_param.along_track_speed_mps;
    cur_param.full_body_start_arclength_m = cur_param.start_arclength_m;
    cur_param.end_arclength_m += 0.1 * cur_param.along_track_speed_mps;
    cur_param.full_body_end_arclength_m = cur_param.end_arclength_m;
    cur_param.left_inlane_clearance_m -= 0.1 * cur_param.cross_track_speed_mps;
    cur_param.left_inlane_clearance_m =
        std::max(cur_param.left_inlane_clearance_m, 0.0);
    cur_lateral_posi += 0.1 * cur_param.cross_track_speed_mps;
    if (cur_lateral_posi >= 1.5 && cur_lateral_posi <= 2.5) {
      cur_param.center_line_clearance_m = 0.0;
    } else if (cur_lateral_posi >= 2.5) {
      cur_param.center_line_clearance_m = 1.5;
    }
    if (cur_lateral_posi >= 1.0) {
      cur_param.right_inlane_clearance_m =
          std::min(4.0, cur_lateral_posi - object_width);
    }
  }
  trajectory_states.trajectory_id = 0;
  result.primary_trajectory_id = 0;
  result.predicted_trajectories.insert(
      {trajectory_states.trajectory_id, trajectory_states});
  return result;
}

std::vector<ObjectStampedSnapshotInfo> GenerateDynamicObjectOccupancyState(
    const ObjectOccupancyParam& init_state, const EgoInLaneParams& ego_params,
    const voy::TrackedObject& tracked_object, int64_t init_timestamp, int step,
    int delta_t, pb::MotionMode motion_mode = pb::MotionMode::FORWARD) {
  std::vector<ObjectStampedSnapshotInfo> result;
  ObjectOccupancyParam cur_param = init_state;
  const double object_width = 1.0;
  double cur_lateral_posi = 0.0;
  // The agent state evolves with initial cross and along track speed.
  for (int i = 0; i < step; i++) {
    int64_t cur_timestamp = init_timestamp + delta_t * i;
    result.emplace_back(
        cur_param, ego_params, /*pose=*/
        TrafficParticipantPose(/*timestamp=*/cur_timestamp, tracked_object),
        motion_mode);
    // Update the in lane param for the next step.
    cur_param.snapshot_start_arclength_m +=
        0.1 * cur_param.along_track_speed_mps;
    cur_param.full_body_start_arclength_m =
        cur_param.snapshot_start_arclength_m;
    cur_param.snapshot_end_arclength_m += 0.1 * cur_param.along_track_speed_mps;
    cur_param.full_body_end_arclength_m = cur_param.snapshot_end_arclength_m;
    cur_param.left_boundary_clearance_m -=
        0.1 * cur_param.cross_track_speed_mps;
    cur_param.left_boundary_clearance_m =
        std::max(cur_param.left_boundary_clearance_m, 0.0);
    cur_lateral_posi += 0.1 * cur_param.cross_track_speed_mps;
    if (cur_lateral_posi >= 1.5 && cur_lateral_posi <= 2.5) {
      cur_param.center_line_clearance_m = 0.0;
    } else if (cur_lateral_posi >= 2.5) {
      cur_param.center_line_clearance_m = 1.5;
    }
    if (cur_lateral_posi >= 1.0) {
      cur_param.right_boundary_clearance_m =
          std::min(4.0, cur_lateral_posi - object_width);
    }
  }
  return result;
}
}  // namespace

// The static object is soft blocking the lane.
// The center line clearance is smaller than ego_width / 2 + required_lat_gap.
TEST_F(LateralBlockingStateTest, StaticSoftBlockingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 2.0;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 1.0;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  snapshot_state.blocking_info.blocking_state =
      pb::AgentSnapshotBlockingState::SOFT_BLOCKING;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.left_boundary_clearance_m = 2.0;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 1.0;
  object_occupancy_param.center_line_side = math::pb::kRight;

  auto blocking_state = GetLateralBlockingStateForObject(
      object_occupancy_param, required_lat_gap_, ego_param_, config_);
  EXPECT_EQ(blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
}

// The static object is hard blocking the lane.
// The in line clearance is smaller than ego_width +
// set_min_clearance_as_soft_blocking_m.
TEST_F(LateralBlockingStateTest, StaticHardBlockingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 1.5;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 0.5;
  inlane_param.cross_track_encroachment_m = 0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  snapshot_state.blocking_info.blocking_state =
      pb::AgentSnapshotBlockingState::HARD_BLOCKING;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::HARD_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::HARD_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.left_boundary_clearance_m = 1.0;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.cross_track_encroachment_m = 0.5;
  object_occupancy_param.center_line_clearance_m = 0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;

  auto blocking_state = GetLateralBlockingStateForObject(
      object_occupancy_param, required_lat_gap_, ego_param_, config_);
  EXPECT_EQ(blocking_state, pb::TrajectoryState::HARD_BLOCKING);
}

// The static object is hard blocking the lane if we could not cross boundary
// nudge. The in line clearance + cross boundary buffer is larger than ego_width
// + set_min_clearance_as_soft_blocking_m thus it becomes soft blocking.
TEST_F(LateralBlockingStateTest, StaticSoftBlockingObjectWithBuffer) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);
  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 1.5;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotPassInfo pass_info;
  pass_info.left_lane_boundary_allowed_encroachment = 0.75;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  snapshot_state.blocking_info.blocking_state =
      pb::AgentSnapshotBlockingState::SOFT_BLOCKING;
  snapshot_state.pass_info = pass_info;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.left_boundary_clearance_m = 1.5;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;

  auto blocking_state = GetLateralBlockingStateForObject(
      object_occupancy_param, required_lat_gap_, ego_param_, config_);
  EXPECT_EQ(blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
}

// The static object is far away from the lane.
// The center line clearance is larger than ego_width / 2 + required_lat_gap.
TEST_F(LateralBlockingStateTest, StaticNoneBlockingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 3.5;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 2.0;
  inlane_param.cross_track_encroachment_m = -0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::NON_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::NON_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.left_boundary_clearance_m = 3.5;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 2.0;
  object_occupancy_param.cross_track_encroachment_m = -0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;

  auto blocking_state = GetLateralBlockingStateForObject(
      object_occupancy_param, required_lat_gap_, ego_param_, config_);
  EXPECT_EQ(blocking_state, pb::TrajectoryState::NON_BLOCKING);
}

// The static object is ahead of ego and the ego could not reach it even with
// the upper bound speed.
TEST_F(LateralBlockingStateTest, StaticFarObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = inlane_param.full_body_start_arclength_m =
      500.0;
  inlane_param.end_arclength_m = inlane_param.full_body_end_arclength_m = 502.0;
  inlane_param.full_body_start_lateral_distance_m = 1.0;
  inlane_param.full_body_end_lateral_distance_m = 3.0;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state,
            result.tracked_blocking_info.blocking_state());

  EXPECT_EQ(result.tracked_blocking_info.is_beyond_intention_range(), true);
  EXPECT_EQ(
      path::GetEffectiveLateralBlockingState(result.tracked_blocking_info),
      pb::LateralBlockingState::NON_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);
}

// The static object is behind the ego.
TEST_F(LateralBlockingStateTest, StaticBehindObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = inlane_param.full_body_start_arclength_m =
      -10.0;
  inlane_param.end_arclength_m = inlane_param.full_body_end_arclength_m = -8.0;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  AgentInLaneStates agent_state;
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state,
            result.tracked_blocking_info.blocking_state());

  EXPECT_EQ(result.tracked_blocking_info.is_beyond_intention_range(), true);
  EXPECT_EQ(
      path::GetEffectiveLateralBlockingState(result.tracked_blocking_info),
      pb::LateralBlockingState::NON_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);
}

// The moving object always provides enough clearance so none blocking.
TEST_F(LateralBlockingStateTest, DynamicNoneBlockingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = inlane_param.full_body_start_arclength_m =
      10.0;
  inlane_param.end_arclength_m = inlane_param.full_body_end_arclength_m = 12.0;
  inlane_param.full_body_start_lateral_distance_m = 1.0;
  inlane_param.full_body_end_lateral_distance_m = 3.0;
  inlane_param.left_inlane_clearance_m = 3.5;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 2.0;
  inlane_param.cross_track_encroachment_m = -0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.along_track_speed_mps = 2.0;
  inlane_param.cross_track_speed_mps = 0.0;
  inlane_param.pose = agent_pose_;
  int num_step = 60;
  int delta_t = 100;
  AgentInLaneStates agent_state = GenerateDynamicObject(
      inlane_param, 0, num_step, delta_t,
      /*blocking_state=*/pb::AgentSnapshotBlockingState::NOT_BLOCKING);
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::NON_BLOCKING);

  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::NON_BLOCKING);

  EXPECT_EQ(result.trajectory_blocking_infos.size(), num_step);
  for (const auto& info : result.trajectory_blocking_infos) {
    EXPECT_EQ(info.blocking_state(), pb::LateralBlockingState::NON_BLOCKING);
  }

  EXPECT_EQ(result.effective_intention_index_ranges.size(), 1);
  EXPECT_EQ(result.effective_intention_index_ranges[0].start_pos, 13);
  EXPECT_EQ(result.effective_intention_index_ranges[0].end_pos, 60);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.snapshot_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_arclength_m = 10.0;
  object_occupancy_param.full_body_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_lateral_distance_m = 1.0;
  object_occupancy_param.full_body_end_lateral_distance_m = 3.0;
  object_occupancy_param.left_boundary_clearance_m = 3.5;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 2.0;
  object_occupancy_param.cross_track_encroachment_m = -0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.along_track_speed_mps = 2.0;
  object_occupancy_param.cross_track_speed_mps = 0.0;
  const auto predicted_states = GenerateDynamicObjectOccupancyState(
      object_occupancy_param, ego_param_, agent_proto_.tracked_object(),
      /*init_timestamp=*/0, num_step, delta_t);
  LateralBlockingStateMetaData metadata;
  metadata.tracked_blocking_info.set_blocking_state(
      GetLateralBlockingStateForObject(object_occupancy_param,
                                       required_lat_gap_, ego_param_, config_));
  GenerateIntentionRangeAndTrajectoryBlockingStates(
      predicted_states, required_lat_gap_, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, config_,
      /*is_crossing_prediction=*/false, metadata);
  EXPECT_EQ(metadata.global_blocking_state, pb::TrajectoryState::NON_BLOCKING);

  EXPECT_EQ(metadata.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::NON_BLOCKING);

  EXPECT_EQ(metadata.trajectory_blocking_infos.size(), num_step);
  for (const auto& info : metadata.trajectory_blocking_infos) {
    EXPECT_EQ(info.blocking_state(), pb::LateralBlockingState::NON_BLOCKING);
    EXPECT_EQ(info.pass_side(), pb::AgentSnapshotPassSide::PASS_LEFT);
  }

  EXPECT_EQ(metadata.effective_intention_index_ranges.size(), 1);
  EXPECT_EQ(metadata.effective_intention_index_ranges[0].start_pos, 13);
  EXPECT_EQ(metadata.effective_intention_index_ranges[0].end_pos, 60);
}

// The moving object is fast in the front.
TEST_F(LateralBlockingStateTest, DynamicFastLeadingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = inlane_param.full_body_start_arclength_m =
      10.0;
  inlane_param.end_arclength_m = inlane_param.full_body_end_arclength_m = 12.0;
  inlane_param.full_body_start_lateral_distance_m = 1.0;
  inlane_param.full_body_end_lateral_distance_m = 3.0;
  inlane_param.left_inlane_clearance_m = 3.5;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 2.0;
  inlane_param.cross_track_encroachment_m = -0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.along_track_speed_mps = 10.0;
  inlane_param.cross_track_speed_mps = 0.0;
  inlane_param.pose = agent_pose_;
  int num_step = 60;
  int delta_t = 100;
  AgentInLaneStates agent_state = GenerateDynamicObject(
      inlane_param, 0, num_step, delta_t,
      /*blocking_state=*/pb::AgentSnapshotBlockingState::NOT_BLOCKING);
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::NON_BLOCKING);

  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::NON_BLOCKING);

  EXPECT_EQ(result.trajectory_blocking_infos.size(), num_step);
  for (const auto& info : result.trajectory_blocking_infos) {
    EXPECT_EQ(info.blocking_state(), pb::LateralBlockingState::NON_BLOCKING);
  }
  // No snapshot falls into the intention range.
  EXPECT_TRUE(result.effective_intention_index_ranges.empty());

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.snapshot_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_arclength_m = 10.0;
  object_occupancy_param.full_body_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_lateral_distance_m = 1.0;
  object_occupancy_param.full_body_end_lateral_distance_m = 3.0;
  object_occupancy_param.left_boundary_clearance_m = 3.5;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 2.0;
  object_occupancy_param.cross_track_encroachment_m = -0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.along_track_speed_mps = 10.0;
  object_occupancy_param.cross_track_speed_mps = 0.0;
  const auto predicted_states = GenerateDynamicObjectOccupancyState(
      object_occupancy_param, ego_param_, agent_proto_.tracked_object(),
      /*init_timestamp=*/0, num_step, delta_t);
  LateralBlockingStateMetaData metadata;
  metadata.tracked_blocking_info.set_blocking_state(
      GetLateralBlockingStateForObject(object_occupancy_param,
                                       required_lat_gap_, ego_param_, config_));
  GenerateIntentionRangeAndTrajectoryBlockingStates(
      predicted_states, required_lat_gap_, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, config_,
      /*is_crossing_prediction=*/false, metadata);
  EXPECT_EQ(metadata.global_blocking_state, pb::TrajectoryState::NON_BLOCKING);

  EXPECT_EQ(metadata.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::NON_BLOCKING);

  EXPECT_EQ(metadata.trajectory_blocking_infos.size(), num_step);
  for (const auto& info : metadata.trajectory_blocking_infos) {
    EXPECT_EQ(info.blocking_state(), pb::LateralBlockingState::NON_BLOCKING);
    EXPECT_EQ(info.pass_side(), pb::AgentSnapshotPassSide::PASS_LEFT);
  }
  // No snapshot falls into the intention range.
  EXPECT_TRUE(metadata.effective_intention_index_ranges.empty());
}

// The moving agent is slow in the front and soft blocking.
// The agent's center line clearance is smaller than the ego_width / 2 +
// required_lat_gap. In the planning horizon, the ego is able to catch the
// object so the object is consider as soft blocking.
TEST_F(LateralBlockingStateTest, DynamicSoftBlockingObject) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = inlane_param.full_body_start_arclength_m =
      10.0;
  inlane_param.end_arclength_m = inlane_param.full_body_end_arclength_m = 12.0;
  inlane_param.full_body_start_lateral_distance_m = 1.0;
  inlane_param.full_body_end_lateral_distance_m = 3.0;
  inlane_param.left_inlane_clearance_m = 2.0;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 1.0;
  inlane_param.cross_track_encroachment_m = -0.5;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.along_track_speed_mps = 3.0;
  inlane_param.cross_track_speed_mps = 0.0;
  inlane_param.pose = agent_pose_;
  int num_step = 60;
  int delta_t = 100;
  AgentInLaneStates agent_state =
      GenerateDynamicObject(inlane_param, 0, num_step, delta_t);
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);

  // The initial state is far ahead of ego, and it is soft blocking.
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);

  // The trajectory is soft blocking.
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);

  EXPECT_EQ(result.trajectory_blocking_infos.size(), num_step);
  EXPECT_EQ(result.effective_intention_index_ranges.size(), 1);
  EXPECT_EQ(result.effective_intention_index_ranges[0].start_pos, 17);
  EXPECT_EQ(result.effective_intention_index_ranges[0].end_pos, 60);

  // Test for the same params in object occupancy states.
  ObjectOccupancyParam object_occupancy_param;
  object_occupancy_param.snapshot_start_arclength_m = 10.0;
  object_occupancy_param.snapshot_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_arclength_m = 10.0;
  object_occupancy_param.full_body_end_arclength_m = 12.0;
  object_occupancy_param.full_body_start_lateral_distance_m = 1.0;
  object_occupancy_param.full_body_end_lateral_distance_m = 3.0;
  object_occupancy_param.left_boundary_clearance_m = 2.0;
  object_occupancy_param.right_boundary_clearance_m = 0.0;
  object_occupancy_param.center_line_clearance_m = 1.0;
  object_occupancy_param.cross_track_encroachment_m = -0.5;
  object_occupancy_param.center_line_side = math::pb::kRight;
  object_occupancy_param.along_track_speed_mps = 3.0;
  object_occupancy_param.cross_track_speed_mps = 0.0;
  const auto predicted_states = GenerateDynamicObjectOccupancyState(
      object_occupancy_param, ego_param_, agent_proto_.tracked_object(),
      /*init_timestamp=*/0, num_step, delta_t);
  LateralBlockingStateMetaData metadata;
  metadata.tracked_blocking_info.set_blocking_state(
      GetLateralBlockingStateForObject(object_occupancy_param,
                                       required_lat_gap_, ego_param_, config_));
  GenerateIntentionRangeAndTrajectoryBlockingStates(
      predicted_states, required_lat_gap_, emergency_brake_speed_,
      lower_bound_speed_, upper_bound_speed_, ego_param_, config_,
      /*is_crossing_prediction=*/false, metadata);
  // The initial state is far ahead of ego, and it is soft blocking.
  EXPECT_EQ(metadata.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);

  // The trajectory is soft blocking.
  EXPECT_EQ(metadata.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);

  EXPECT_EQ(metadata.trajectory_blocking_infos.size(), num_step);
  EXPECT_EQ(metadata.effective_intention_index_ranges.size(), 1);
  EXPECT_EQ(metadata.effective_intention_index_ranges[0].start_pos, 17);
  EXPECT_EQ(metadata.effective_intention_index_ranges[0].end_pos, 60);
}

// The encroaching construction zone ahead of the ego will be soft blocking.
TEST_F(LateralBlockingStateTest, ConstructionZone) {
  ConstructionZoneInLaneState cz_state;
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);
  cz_state.left_inlane_clearance_m = 2.0;
  cz_state.left_lane_boundary_violation_buffer = 1.0;
  cz_state.start_arclength_m = 10.0;
  cz_state.end_arclength_m = 12.0;
  cz_state.center_line_clearance_m = 1.0;
  cz_state.blocking_state = pb::ConstructionZoneBlockingState::SOFT_BLOCKING;
  cz_state.construction_zone_contour = agent_pose_.contour();
  AgentSLBoundary sl_boundary(cz_state);

  auto result = generator.GenerateConstructionZoneBlockingStateMetaData(
      cz_state, required_lat_gap_, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*was_nudge_intention=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);
}

// The object is considered to be crossing for its cross track speed.
TEST_F(LateralBlockingStateTest, DISABLED_Crossing) {
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 2.0;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 1.0;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.along_track_speed_mps = 3.0;
  // Lateral speed parameter.
  inlane_param.cross_track_speed_mps = 6.0;
  inlane_param.relative_heading_rad = 1.5;
  inlane_param.cross_track_encroachment_m = 0.2;
  inlane_param.pose = agent_pose_;
  int num_step = 60;
  int delta_t = 100;
  AgentInLaneStates agent_state = GenerateDynamicObject(
      inlane_param, 0, num_step, delta_t,
      /*blocking_state=*/pb::AgentSnapshotBlockingState::HARD_BLOCKING);
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, *planner_object_, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  // The initial state is far ahead of ego but it is hard blocking.
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::HARD_BLOCKING);

  // The trajectory is hard blocking as part of the trajectory is hard blocking.
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::HARD_BLOCKING);

  EXPECT_EQ(result.trajectory_blocking_infos.size(), num_step);
  EXPECT_EQ(result.effective_intention_index_ranges.size(), 1);
  EXPECT_EQ(result.effective_intention_index_ranges[0].start_pos, 17);
  EXPECT_EQ(result.effective_intention_index_ranges[0].end_pos, 60);
}

TEST_F(LateralBlockingStateTest, StaticUnknownObject) {
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: UNKNOWN
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object =
      PlannerObject(agent_proto, timestamp, pose_at_plan_init_ts);
  LateralBlockingStateGenerator generator(config_,
                                          /*behavior_type=*/pb::LANE_KEEP);

  AgentSnapshotInLaneParam inlane_param;
  inlane_param.start_arclength_m = 10.0;
  inlane_param.left_inlane_clearance_m = 2.0;
  inlane_param.right_inlane_clearance_m = 0.0;
  inlane_param.center_line_clearance_m = 1.0;
  inlane_param.center_line_side = math::pb::kRight;
  inlane_param.pose = agent_pose_;
  AgentSnapshotInLaneState snapshot_state;
  snapshot_state.inlane_param = inlane_param;
  snapshot_state.blocking_info.blocking_state =
      pb::AgentSnapshotBlockingState::SOFT_BLOCKING;
  AgentInLaneStates agent_state;
  agent_state.object_id = 0;
  agent_state.tracked_state = snapshot_state;
  agent_state.primary_trajectory_id = 0;
  StampedAgentSnapshotInLaneState snapshot_in_lane_state;
  snapshot_in_lane_state.inlane_state = snapshot_state;
  agent_state.predicted_trajectories[0].predicted_states.push_back(
      snapshot_in_lane_state);
  AgentInLaneMetadata meta_data;
  meta_data.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object.set_is_primary_stationary(true);
  agent_state.agent_metadata = meta_data;
  AgentSLBoundary sl_boundary(inlane_param);

  auto result = generator.GenerateBlockingStateMetaData(
      agent_state, planner_object, planner_objects_history_,
      object_predictions_, agent_state.primary_trajectory_id, required_lat_gap_,
      /*ls_tl_infos=*/{}, emergency_brake_speed_, lower_bound_speed_,
      upper_bound_speed_, ego_param_, ego_bounding_box_,
      planning_horizon_range_, sl_boundary,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/true, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      physical_boundary_lateral_clearance_data_,
      /*current_lane=*/
      test::GenerateLane(CreateStraightLaneSequenceGeometry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false);
  EXPECT_EQ(result.global_blocking_state, pb::TrajectoryState::SOFT_BLOCKING);
  EXPECT_EQ(result.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::SOFT_BLOCKING);
  EXPECT_EQ(result.trajectory_blocking_infos.size(), 1);
  EXPECT_FALSE(result.reasoning_info.is_ignorable_unknown());
}

}  // namespace planner
