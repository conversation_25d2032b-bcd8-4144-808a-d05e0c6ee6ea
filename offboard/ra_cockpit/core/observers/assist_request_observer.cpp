#include "assist_request_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

AssistRequestObserver::AssistRequestObserver(AssistRequestCallback callback)
    : callback_(std::move(callback)) {}

bool AssistRequestObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kAssistRequestMessage);
}

std::shared_ptr<google::protobuf::Message> AssistRequestObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto assist_req = std::make_shared<planner::pb::AssistRequest>();
  if (!assist_req->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error AssistRequest message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(assist_req, data_channel);
  }
  return assist_req;
}

}  // namespace remote_assist
