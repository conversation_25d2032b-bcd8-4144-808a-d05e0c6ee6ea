#ifndef ONBOARD_PNC_MAP_SERVICE_UTIL_PNC_MAP_SERVICE_UTILITY_H_
#define ONBOARD_PNC_MAP_SERVICE_UTIL_PNC_MAP_SERVICE_UTILITY_H_

#include <algorithm>
#include <limits>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "geometry/algorithms/distance.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline.h"
#include "hdmap/lib/zone_lane_occupancy_map.h"
#include "pnc_map_service/map_elements/directed_lanes.h"
#include "pnc_map_service/map_elements/junction.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/map_elements_utility.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/section.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace pnc_map {

// A structure to represent the near lane candidate near a ego pose.
// TODO(Tingran): Merge this struct with CurrentLaneCandidate into one proto.
// Add proximity to the struct.
struct NearLaneCandidate {
  voy::Pose pose;
  const pnc_map::Lane* lane = nullptr;
  double pose_to_lane_center_dist_in_m = 0.0;
  // Value range: [0, 2 * Pi). The angle is from the lane center line's
  // direction to the ego pose's heading. [0, Pi) means ego faces to left of the
  // lane's direction. (Pi, 2 * Pi) means ego faces to right of the lane's
  // direction.
  double heading_diff_in_rad = 0.0;
  // Is this lane in regional sections locator near lanes.
  bool is_selected_by_locator_near_lanes = false;
  // Is pose inside this lane.
  bool is_pose_in_lane = false;
  // Is this lane inside the first section of regional_section_vertices from
  // regional sections locator.
  bool is_in_root_section = false;
  bool is_in_cost_map = false;
  bool is_on_last_selected_lane_sequence = false;
  double pose_percentage_along_lane = 0.0;
  double pose_arclength_from_lane_start = 0.0;
};

// A lane's neighbor lane info, include in-section and cross-section ones.
struct NeighborLaneInfo {
  NeighborLaneInfo(
      const pnc_map::Lane* neighbor_lane_in, bool to_left_in,
      bool is_adjacent_in,
      const std::vector<CrossRoadNeighborLane>& road_neighbor_lanes_in)
      : neighbor_lane(neighbor_lane_in),
        to_left(to_left_in),
        is_adjacent(is_adjacent_in),
        road_neighbor_lanes(road_neighbor_lanes_in) {}

  // Get the cross road neighbor lane with the given percentage along lane.
  const pnc_map::Lane* GetCrossRoadNeighborLane(
      double percentage_along_lane) const;

  // Lane's same-section neighbor lane. If no same section ones, will return
  // nullptr.
  const pnc_map::Lane* neighbor_lane = nullptr;
  bool to_left = true;
  bool is_adjacent = false;
  // Lane's cross-section neighbor lanes. If no cross section ones, will return
  // nullopt. Since a road can have multiple head-to-end parallel neighbor
  // roads, we save it as a separate struct.
  std::vector<CrossRoadNeighborLane> road_neighbor_lanes;
};

// Returns the distance boundaries used to determine whether an object is on or
// near a lane, section, or road. "on" and "near" correspond to object lanes and
// target lanes respectively.
// NOTE: There may be gaps between lanes and their successors in our map. Here
// we add a small tolerance distance for "on lane" to handle these cases.
double GetOnLaneDistanceBoundary(voy::perception::ObjectType object_type);
double GetNearRoadDistanceBoundary();

// Gets heading boundary as a pair of (min_theta, max_theta) based on the lane
// type.
std::pair<double, double> GetThetaBoundary(hdmap::Lane::LaneType lane_type);

// Returns the boundaries of the heading difference between object and a valid
// inverse lane. The inverse lane is considered "valid" if the heading
// difference is within any one of the boundaries.
std::vector<std::pair<double, double>> GetThetaBoundaryForInverseLane();

// Returns the heading difference boundary between object and given lane for
// target lanes. For slow vehicles with speed smaller than
// kMaxSpeedForWideThetaBoundary and are near exit zone, returns wide angle
// difference range.
// NOTE(Haotian): For left/right turn only lanes, returns half angle difference
// range. These lanes will not be used as target lanes when vehicles are from
// the convex side, so we wll not generate trajectory caused harsh breaks like
// the one in http://agile.intra.xiaojukeji.com/browse/VOYAGER-6748.
std::pair<double, double> GetThetaBoundaryForTargetLane(
    voy::perception::ObjectType object_type, const Lane& lane, double speed,
    bool is_near_exit_zone);

// Returns the heading difference boundary between object and given lane for
// inverse target lanes.
std::vector<std::pair<double, double>> GetThetaBoundaryForInverseTargetLane(
    voy::perception::ObjectType object_type, double speed,
    bool is_near_exit_zone);

// Returns the distance boundary used to determine if an object is close to
// an exit zone.
double GetNearExitZoneDistanceBoundary();

// Returns true if the pose reached lane is valid (runnability, heading
// similarity) for that given pose. Only used for lanes of vehicles and
// cyclists in normal direction.
// NOTE(boyu): The |mocked_tracked_object| is constructed using the trajectory
// pose and object type. Thus, its length, width and omega are not set and
// should not be used.
bool IsValidPoseReachedLane(const Lane& lane,
                            const voy::TrackedObject& mocked_tracked_object);

// Removes duplicated lanes in |candidate_lanes|.
void RemoveDuplicateLanes(std::vector<const pnc_map::Lane*>* candidate_lanes);

// Returns true if the angle between heading of given tracked object and the
// lane's direction is within ANY and of given boundaries. This is used for
// checking heading difference conditions when associating an object or target
// lane.
bool AreHeadingsSimilar(
    const voy::TrackedObject& tracked_object, const Lane& lane,
    const std::vector<std::pair<double, double>>& theta_bounds);

// Returns true if the distance between agent center and lane left/right
// marking is less than the distance threshold.
bool IsNearLaneMarking(const voy::TrackedObject& tracked_object,
                       const Lane& lane, const math::pb::Side lane_marking_side,
                       double distance_threshold);

// Returns true if the tracked object's kinematics are consistent with object
// lane's turn type. Only used for vehicle in junction and with normal length
// for now.
bool IsKinematicsConsistentWithLaneTurnType(
    const voy::TrackedObject& tracked_object, const Lane& lane);

// Gets exit lanes given source road and turn type.
// NOTE(boyu): Only used for right turn and straight type; object type is
// vehicle or cyclist.
std::vector<const Lane*> GetExitLanesByTurnType(
    voy::perception::ObjectType object_type,
    const planner::pb::TrajectoryPose& pose, const Road& source_road,
    hdmap::Lane::Turn turn);

// Gets neareast section the point (x, y) in given |road|.
// TODO(wilsonhong): consider move to Road::SelectNearestSectionToPoint().
// TODO(boyulu): unit test.
const Section* GetNearestSectionInRoad(const Road& road, double x, double y);

// Gets the nearest lane in section given a pose. The returned lane must be
// runnable for |object_type|, and the distance between the returned lane and
// pose must be within |kNearRoadDistanceBoundaryForTargetLane|. Returns
// nullptr if no valid near lane is found.
const Lane* GetNearestLaneInSection(voy::perception::ObjectType object_type,
                                    const Section& section,
                                    const planner::pb::TrajectoryPose& pose);

// Returns a tracked object which state is the same with the given pose.
voy::TrackedObject GenerateTrackedObjectWithPose(
    const planner::pb::TrajectoryPose& pose,
    voy::perception::ObjectType object_type);

// Generates center line for exit zone.
math::geometry::PolylineCurve2d GenerateCenterLineForExitZone(
    const Zone& exit_zone);

// Returns closest map elements near to the point and distances.
template <typename MapElement>
std::vector<std::pair<const MapElement*, double>> GetClosestMapElement(
    const std::vector<const MapElement*>& map_elements,
    const math::geometry::Point2d& pt) {
  std::vector<double> distance_vec;
  distance_vec.reserve(map_elements.size());

  for (const MapElement* map_element : map_elements) {
    distance_vec.push_back(math::geometry::Distance(pt, map_element->border()));
  }

  // Get minimum distance.
  const auto min_distance_iter =
      std::min_element(distance_vec.begin(), distance_vec.end());

  // Get closest elements.
  std::vector<std::pair<const MapElement*, double>> closest_element_vec;

  for (size_t i = 0; i < map_elements.size(); ++i) {
    if (math::NearZero(distance_vec[i] - *min_distance_iter)) {
      closest_element_vec.emplace_back(map_elements[i], distance_vec[i]);
    }
  }

  return closest_element_vec;
}

// Get all the sections which are about to enter the given |junction|.
pnc_map::PncObjectSet<const Section*> GetEnterSections(
    const Junction& junction);

// Set related sections for the given enter section of some junctions.
void SetRelatedSections(Section* enter_section);

// Returns true if the |lane_marking| is close to hard boundary.
bool CloseToHardBoundary(const LaneMarking& lane_marking, const Lane& lane);

// Find all connected lanes using BFS.
pnc_map::PncObjectSet<const Lane*> GetConnectedLanes(const Lane& current_lane,
                                                     int search_degree,
                                                     LaneDirection direction);

// Gets filtered near lanes from input lanes by filtering with heading
// difference.
// Lanes in last_selected_lane_sequence and before next junction are not subject
// to heading filtering.
std::vector<const pnc_map::Lane*> GetFilteredNearLanesByHeading(
    const std::vector<const pnc_map::Lane*>& lanes,
    const math::geometry::Point2d& robot_center, double robot_heading,
    double max_heading_diff,
    const std::optional<std::vector<const pnc_map::Lane*>>&
        last_selected_lane_sequence);

// Returns the lanes intersect with the contour.
std::vector<const pnc_map::Lane*> GetAssociatedLanes(
    const std::vector<const pnc_map::Lane*>& lanes,
    const math::geometry::PolygonWithCache2d& contour);

// Returns the lanes from lane_sequence between from lane and to lane.
// from lane and to lane are included if they are within lane_sequence.
std::vector<const pnc_map::Lane*> GetSubsequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    int64_t from_lane_id, int64_t to_lane_id);

// Gets a sequence of sections from a sequence of lanes. If |prevent_loop| is
// true, we should not append the section which loop back to the same junction.
std::vector<const pnc_map::Section*> GetSectionsFromLanes(
    const std::vector<const pnc_map::Lane*>& lanes, bool prevent_loop);

// Gets a sequence of roads from a sequence of sections.
std::vector<const pnc_map::Road*> GetRoadsFromSections(
    const std::vector<const pnc_map::Section*>& sections);

// Gets the euclidean distance from a point to the start point of the center
// line of lane.
double GetDistanceToStartPointOfLane(const math::geometry::Point2d& point,
                                     const pnc_map::Lane& lane);

// Gets absolute direction difference in [0, pi] at the join point of two
// connect lanes. DCHECK fail when |lane| is not connected (be a
// successor) of |successor_lane|.
//
// For example, the absolute direction difference of A and C will be near zero,
// while the absolute direction difference of B and C will be about 0.3 (in
// radian).
//    |------|
//    |      |
//    |      |
//    |   C  |
//    |      |
//    |------|
//    |\     |\
//    | \    | \
//    |  \   |  \
//    | A \  | B \
//    |    \ |    \
//    |-----\|-----\
//
double GetAbsDirectionDiffOfConnectedLanes(const pnc_map::Lane& lane,
                                           const pnc_map::Lane& successor_lane);

// Gets a lane change neighbor lane for |curr_lane| given the lane change
// direction.
const pnc_map::Lane* GetLaneChangeNeighborLane(const pnc_map::Lane& curr_lane,
                                               bool to_left);

// Gets lane change neighbor lane info for |curr_lane| given the lane change
// direction.
NeighborLaneInfo GetLaneChangeNeighborLaneInfo(const pnc_map::Lane& curr_lane,
                                               bool to_left);

// Gets a lane change neighbor lane for |curr_lane| in |percentage_along_lane|
// given the lane change direction, which may come from neighbor road.
const pnc_map::Lane* GetLaneChangeNeighborLane(const pnc_map::Lane& curr_lane,
                                               double percentage_along_lane,
                                               bool to_left);

// Checks if the source lane and target lane make up a valid lane change
// instance.
bool IsValidLaneChangeInstance(const pnc_map::Lane& source_lane,
                               const pnc_map::Lane& target_lane);

// Returns the closest lane to the position in a range.
const pnc_map::Lane* GetClosestLaneInRange(
    const std::vector<const pnc_map::Lane*>& lanes,
    const math::geometry::Point2d& position, double lane_search_radius);

// Gets lane-related hard boundary informations of the road.
std::vector<HardBoundaryInfo> GetLaneMarkingRelatedHardBoundaryInfo(
    const math::geometry::PolylineCurve2d& center_line,
    const math::geometry::PolylineCurve2d& lane_marking_line, const Road& road,
    math::pb::Side side);

// Return true if the current lane has less lane curvature than the brother
// lane, indicating that it is relatively straight.
bool IsCurrentLaneStraighterThanBrotherLane(const pnc_map::Lane* current_lane,
                                            const pnc_map::Lane* brother_lane);

// Check if current lane will cross the non virtual lane marking of the brother
// lane during merge and fork. If so, generate turn signal to emphasis the merge
// and fork intention. The reference doc:
// https://cooper.didichuxing.com/docs/document/2199801467276.
bool WillCurrentLaneCrossBrotherLaneMarking(
    const pnc_map::Lane* current_lane_ptr, bool left_crossing);

// Returns true if the current lane has any virtual lane marking on the given
// side.
bool HasAnyVirtualLaneMarkingBySide(const pnc_map::Lane* current_lane_ptr,
                                    const math::pb::Side side);

// Returns true if the current lane has any virtual lane marking on its both
// side.
bool HasAnyVirtualLaneMarking(const pnc_map::Lane* current_lane_ptr);

// Computes the turn signal for merge fork lanes by the curvature of the current
// lane and brother lanes.
planner::pb::TurnMode ComputeTurnSignalForMergeForkLaneByCurvature(
    const pnc_map::Lane* current_lane_ptr);

// Returns true if the lane marking is virtual and straight by comparing with
// its successor and predecessor.
bool IsVirtualLaneMarkingStraight(const pnc_map::Lane* current_lane_ptr,
                                  const math::pb::Side side);

// A group of functions used to construct both online and offline pnc map.

// Sets brother lanes for a lane.
void SetBrothers(Lane* lane);
// Adds associated crosswalks to lane.
void SetCrosswalks(const hdmap::ZoneLaneOccupancyMap* zone_lane_occupancy_map,
                   Lane* lane);
// Adds truncated precedingLanes and section ids.
void SetTruncatedPrecedingLanesAndSectionIds(Lane* lane);
// Sets merge fork turn signal.
void SetMergeForkTurnSignal(Lane* lane);
// Sets left lane change lane of the given lane, shared lane marking is
// required.
void SetAdjacentLeftLane(Lane* lane);
// Sets right lane change lane of the given lane, shared lane marking is
// required.
void SetAdjacentRightLane(Lane* lane);
// Sets cross road left neighbor lane.
void SetCrossRoadLeftNeighborLane(Section* section);
// Sets cross road right neighbor lane.
void SetCrossRoadRightNeighborLane(Section* section);

// Gets |section|'s cross road neighbor lane, direction is |to_left|.
const Lane* GetNeighborLaneFromNeighborRoad(
    const Section* section, bool to_left,
    const std::vector<const Road*>& neighbor_roads);

// Gets |section|'s cross road neighbor lane, direction is |to_left|.
std::optional<CrossRoadNeighborLane> GetCrossRoadNeighborLane(
    const Section* section, const std::vector<const Road*>& neighbor_roads,
    bool to_left);

// Checks if the junction road is applicable to find opposite road.
bool IsApplicableJunctionRoad(const Road* road);
}  // namespace pnc_map

#endif  // ONBOARD_PNC_MAP_SERVICE_UTIL_PNC_MAP_SERVICE_UTILITY_H_
