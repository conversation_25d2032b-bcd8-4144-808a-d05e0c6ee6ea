#ifndef ONBOARD_SENSORS_RADAR_GEN4_RADAR_PARSER_BASE_H_
#define ONBOARD_SENSORS_RADAR_GEN4_RADAR_PARSER_BASE_H_

#include <iterator>
#include <map>
#include <memory>
#include <mutex>  // NOLINT
#include <string>
#include <typeinfo>
#include <utility>
#include <vector>

#include <boost/lockfree/spsc_queue.hpp>
#include <glog/logging.h>

#include "abnormal_checker.h"
#include "add_data.h"
#include "av_comm/car_id.h"
#include "node/timer.h"
#include "radar_config.h"
#include "radar_gflags.h"
#include "sinpro_msg.h"
#include "varch/vnode/message_util.h"
#include "voy_protos/capture.pb.h"
#include "voy_protos/hardware_monitor.pb.h"

#include "sensor_publisher/sensor_publisher_wrapper.h"

namespace radar {

using Publisher = varch::vnode::message_util::Publisher;
using PacketPtr = std::shared_ptr<std::vector<uint8_t>>;
using varch::vnode::message_util::CreatePublisher;

constexpr int kQueryIntervalMs = 2;
constexpr int kMaxPacketQueueSize = 1024;
constexpr char kLocalIp[] = "*************";

inline std::shared_ptr<voy::Capture> InitializeCapture(
    const std::string& src_ip, int src_port, const std::string& dst_ip,
    int dst_port) {
  auto capture = std::make_shared<voy::Capture>();
  auto send_meta = capture->mutable_header()->mutable_meta();
  (*send_meta)["src_ip"] = src_ip;
  (*send_meta)["dst_ip"] = dst_ip;
  (*send_meta)["src_port"] = std::to_string(src_port);
  (*send_meta)["dst_port"] = std::to_string(dst_port);
  return capture;
}

class RadarParser {
 public:
  RadarParser()
      : pkt_recv_queue_(kMaxPacketQueueSize),
        pkt_send_queue_(kMaxPacketQueueSize) {}
  virtual ~RadarParser() = default;
  virtual void ProcessReceive(const PacketPtr& pkt_buf_ptr) = 0;

  void EnqueueReceive(PacketPtr pkt_buf_ptr) {
    pkt_recv_queue_.push(pkt_buf_ptr);
  }

  void EnqueueSend(PacketPtr pkt_buf_ptr) {
    pkt_send_queue_.push(pkt_buf_ptr);
    if (pkt_send_queue_.read_available() >= kMaxPacketQueueSize) {
      LOG_EVERY_N(ERROR, 100)
          << "Send queue is full, size: " << pkt_send_queue_.read_available();
    }
  }

  void ReceiveProcessSpawn(boost::asio::io_service* io_service) {
    auto timer = std::make_unique<node::Timer>(*io_service);
    boost::asio::spawn(*io_service, [this, timer = std::move(timer)](
                                        boost::asio::yield_context yield) {
      boost::system::error_code ec;
      while (!ec) {
        PacketPtr pkt_buf_ptr = nullptr;
        if (!pkt_recv_queue_.pop(pkt_buf_ptr)) {
          timer->expires_from_now(std::chrono::milliseconds(kQueryIntervalMs),
                                  ec);
          timer->async_wait(yield[ec]);
          if (!pkt_recv_queue_.pop(pkt_buf_ptr)) continue;
        }
        ProcessReceive(std::move(pkt_buf_ptr));
      }
    });
  }

  void SendProcessSpawn(
      boost::asio::io_service* io_service,
      std::shared_ptr<
          std::function<void(PacketPtr, const boost::asio::ip::udp::endpoint&)>>
          send_callback) {
    auto timer = std::make_unique<node::Timer>(*io_service);
    boost::asio::spawn(
        *io_service, [this, timer = std::move(timer),
                      send_callback](boost::asio::yield_context yield) {
          boost::system::error_code ec;
          while (!ec) {
            PacketPtr pkt_buf_ptr = nullptr;
            if (!pkt_send_queue_.pop(pkt_buf_ptr)) {
              timer->expires_from_now(
                  std::chrono::milliseconds(kQueryIntervalMs), ec);
              timer->async_wait(yield[ec]);
              if (!pkt_send_queue_.pop(pkt_buf_ptr)) continue;
            }
            if (is_radar_state_working_) {
              RecordRawPacket(pkt_buf_ptr, base::NowNs(), PacketType::SEND);
              (*send_callback)(std::move(pkt_buf_ptr), radar_endpoint_);
            }
          }
        });
  }

  void ReportTimeout(bool is_timeout) {
    abnormal_checker_->ReportTimeout(is_timeout);
    if (is_timeout) {
      is_radar_state_working_ = false;
    }
  }

  monitor::HardwareMonitorData GetHardwareMonitorData() {
    std::lock_guard<std::mutex> lock(mtx);
    return monitor_data_single_;
  }

  void RecordRawPacket(const PacketPtr& pkt_buf_ptr, int64_t pkt_time_ns,
                       const PacketType type) {
    // Support raw packet from multicast version.
    if (!FLAGS_enable_raw_pkt || !FLAGS_is_multicast) return;
    auto ProcessPacket = [&](auto& packet_list) {
      auto* pkt = packet_list->add_packets();
      pkt->set_timestamp_ns(pkt_time_ns);
      pkt->set_eth_packet(pkt_buf_ptr->data(), pkt_buf_ptr->size());
    };

    switch (type) {
      case PacketType::RECV:
        ProcessPacket(radar_recv_pkt_);
        break;
      case PacketType::SEND:
        ProcessPacket(radar_send_pkt_);
        break;
      default:
        CHECK(false) << "Unknown packet type: " << static_cast<int>(type);
        break;
    }
  }

  void UdpPacketPublish() {
    auto PublishPacket = [&](auto& packet_list, auto& publisher) {
      packet_list->set_timestamp(base::Now());
      publisher.Publish(packet_list);
      packet_list->mutable_packets()->Clear();
    };

    PublishPacket(radar_recv_pkt_, radar_raw_pkt_pub_);
    PublishPacket(radar_send_pkt_, radar_raw_pkt_pub_);
  }

 protected:
  explicit RadarParser(const std::string& facing_direction)
      : car_type_(av_comm::CarId::Get().type()),
        radar_scan_pub_(
            sensors::SensorPublisherWrapper::CreateSensorPublisherWrapper<
                pb::RadarScan>(
                GetRadarConfig<RadarConfigItem::SCAN_TOPIC>(facing_direction))),
        facing_direction_(facing_direction),
        pkt_recv_queue_(kMaxPacketQueueSize),
        pkt_send_queue_(kMaxPacketQueueSize) {
    std::string radar_ip =
        GetRadarConfig<RadarConfigItem::IP_ADDRESS>(facing_direction);
    std::string multicast_ip =
        GetRadarConfig<RadarConfigItem::MULTICAST_ADDRESS>(facing_direction);
    abnormal_checker_ = std::make_shared<AbnormalChecker>(facing_direction);
    radar_endpoint_ = boost::asio::ip::udp::endpoint(
        boost::asio::ip::make_address(radar_ip), FLAGS_radar_port);
    facing_ip_ = "[" + facing_direction + ", " + radar_ip + "] ";
    if (FLAGS_is_multicast) {
      multicast_endpoint_ = boost::asio::ip::udp::endpoint(
          boost::asio::ip::make_address(multicast_ip), FLAGS_radar_port);
      LOG(INFO) << facing_ip_
                << "Create RadarParser for multicast: " << multicast_ip << ":"
                << FLAGS_radar_port;
    }

    // Setup and initialize capture proto.
    radar_recv_pkt_ = InitializeCapture(radar_ip, FLAGS_radar_port,
                                        multicast_ip, FLAGS_local_port);
    radar_send_pkt_ = InitializeCapture(kLocalIp, FLAGS_local_port,
                                        multicast_ip, FLAGS_radar_port);

    radar_raw_pkt_pub_ = CreatePublisher<voy::Capture>(
        GetRadarConfig<RadarConfigItem::PACKET_TOPIC>(facing_direction_));
  }

  template <typename T>
  const T* ExtractField(const PacketPtr& pkt_buf_ptr,
                        std::vector<uint8_t>::const_iterator& it) {
    auto end = pkt_buf_ptr->cend();
    if (std::distance(it, end) < static_cast<std::ptrdiff_t>(sizeof(T))) {
      CHECK(false) << "Packet buffer is too short to extract field: "
                   << typeid(T).name();
    }
    const T* field = reinterpret_cast<const T*>(&(*it));
    it += sizeof(T);
    return field;
  }

  template <typename T>
  static PacketPtr ObjectToByteArray(const T& obj) {
    return std::make_shared<std::vector<uint8_t>>(
        reinterpret_cast<const uint8_t*>(&obj),
        reinterpret_cast<const uint8_t*>(&obj) + sizeof(T));
  }

  av_comm::CarType car_type_;

  sensors::SensorPublisherWrapper radar_scan_pub_;
  Publisher radar_raw_pkt_pub_;
  std::shared_ptr<voy::Capture> radar_recv_pkt_;
  std::shared_ptr<voy::Capture> radar_send_pkt_;
  std::shared_ptr<AbnormalChecker> abnormal_checker_;

  boost::asio::ip::udp::endpoint radar_endpoint_;
  boost::asio::ip::udp::endpoint multicast_endpoint_;

  std::string facing_ip_;
  std::string facing_direction_;

  bool is_radar_state_working_ = false;

  boost::lockfree::spsc_queue<PacketPtr> pkt_recv_queue_;
  boost::lockfree::spsc_queue<PacketPtr> pkt_send_queue_;

  monitor::HardwareMonitorData monitor_data_single_;
  std::mutex mtx;
};

}  // namespace radar

#endif  // ONBOARD_SENSORS_RADAR_GEN4_RADAR_PARSER_BASE_H_
