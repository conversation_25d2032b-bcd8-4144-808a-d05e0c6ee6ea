#ifndef ONBOARD_PLANNER_UTILITY_COMMON_COMMON_UTILITY_H_
#define ONBOARD_PLANNER_UTILITY_COMMON_COMMON_UTILITY_H_

#include <algorithm>
#include <iterator>
#include <map>
#include <optional>
#include <sstream>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "tbb/concurrent_unordered_map.h"

#include "geometry/model/polyline.h"
#include "hdmap/lib/hdmap.h"
#include "math/range.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#if !defined(BUILD_PYBIND_IN_CENTOS7)
#include "planner/world_model/lane_blockage/lane_blockage.h"
#include "planner/world_model/planner_object/planner_object.h"
#endif
#include "planner_protos/common.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/relax_lane_sequence_parameter.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/pnc_object.h"
#include "pnc_map_service/map_elements/section.h"
#include "realtime_map_protos/common/base.pb.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/pose.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

namespace common {

// It stores a lane segment in arc length.
struct LaneSegment {
  LaneSegment() = delete;
  LaneSegment(const pnc_map::Lane* lane_in, double start_arc_length_m,
              double end_arc_length_m)
      : lane(CHECK_NOTNULL(lane_in)) {
    arc_length_range_m.set_start_pos(start_arc_length_m);
    arc_length_range_m.set_end_pos(end_arc_length_m);
  }

  double length() const {
    return arc_length_range_m.end_pos() - arc_length_range_m.start_pos();
  }

  const pnc_map::Lane* lane = nullptr;
  math::pb::Range arc_length_range_m;
};

// It stores a lane segment in percentage along lane.
struct LaneSegmentPercentRange {
  LaneSegmentPercentRange() = default;

  LaneSegmentPercentRange(const pnc_map::Lane* lane_in,
                          const math::Range1d& percentage_range_in)
      : lane(CHECK_NOTNULL(lane_in)), percentage_range(percentage_range_in) {}

  // The lane id.
  const pnc_map::Lane* lane = nullptr;
  // The start and end percentage of the lane segment.
  math::Range1d percentage_range;
};

// LaneSegmentPercentage stores a lane segment in start percentage and end
// percentage.
struct LaneSegmentPercentage {
  LaneSegmentPercentage() = default;

  LaneSegmentPercentage(int64_t lane_id_in,
                        const math::Range1d& percentage_range_in)
      : lane_id(lane_id_in), percentage_range(percentage_range_in) {}

  // The lane id.
  int64_t lane_id = 0;
  // The start and end percentage of the lane segment.
  math::Range1d percentage_range;
};

// The compare operator for PB lane segment.
struct PBLaneSegmentComparator {
  bool operator()(const pb::LaneSegment& lhs,
                  const pb::LaneSegment& rhs) const {
    return lhs.arc_length_range_m().start_pos() <
               rhs.arc_length_range_m().start_pos() ||
           (lhs.arc_length_range_m().start_pos() ==
                rhs.arc_length_range_m().start_pos() &&
            lhs.arc_length_range_m().end_pos() <
                rhs.arc_length_range_m().end_pos());
  }
};

// Converts to math::pb::Range format.
math::pb::Range ConvertToMathPbRange(const double start_pos,
                                     const double end_pos);

// Constructs a PB format lane segment.
void ConstructPBLaneSegment(int64_t lane_id, double start_arc_length,
                            double end_arc_length,
                            pb::LaneSegment* lane_segment);

// Converts lane segment into PB.
void ConvertLaneSegmentToPB(const LaneSegment& lane_segment,
                            pb::LaneSegment* pb_lane_segment);

// Converts a pb into lane segment.
LaneSegment ConvertPBToLaneSegment(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::LaneSegment& pb_lane_segment);

// Converts lane segments into protobuf.
void ConvertLaneSegmentsToPB(const std::vector<LaneSegment>& lane_segments,
                             std::vector<pb::LaneSegment>* pb_lane_segments);
void ConvertLaneSegmentsToPB(
    const std::vector<LaneSegment>& lane_segments,
    ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>*
        pb_lane_segments);

// Converts the protobuf format lane segments into a |LaneSegment| sequence.
void ConvertPBToLaneSegments(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>&
        lane_segments,
    std::vector<LaneSegment>* lane_segments_out);
void ConvertPBToLaneSegments(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<pb::LaneSegment>& lane_segments,
    std::vector<LaneSegment>* lane_segments_out);

// Converts planner::pb::LaneSegment to hdmap::LaneSegment format.
::google::protobuf::RepeatedPtrField<hdmap::LaneSegment>
ConvertToHdmapLaneSegments(
    const ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>&
        lane_segments);

voy::Pose ConvertTrajectoryPoseToImuPose(
    const pb::TrajectoryPose& trajectory_pose);

voy::TrackedObject GetTrackedObjectWithPredictedPose(
    const voy::TrackedObject& object, const pb::TrajectoryPose& new_pose);

// Constructs a PB format lane marking segment.
void ConstructPBLaneMarkingSegment(
    int64_t lane_marking_id, double start_arc_length, double end_arc_length,
    const pnc_map::Lane* left_lane, const pnc_map::Lane* right_lane,
    pb::LaneMarkingSegment* lane_marking_segment);

// Returns true if two range has intersection.
bool IsRangesOverlap(const math::pb::Range& lhs, const math::pb::Range& rhs);

// Returns true if given side of lane marking of lane segment is blocked.
bool HasBlockedLaneMarkingInLaneSegment(
    const ::google::protobuf::RepeatedPtrField<
        ::planner::pb::LaneMarkingSegment>& blocked_marking_segments,
    const pnc_map::Lane* lane, const math::pb::Range& arc_length_range_m,
    bool to_left);

// Returns true if two range has intersection.
bool IsRangesOverlap(const math::pb::Range& lhs, const math::Range1d& rhs);

#if !defined(BUILD_PYBIND_IN_CENTOS7)
// Returns true if given side of lane marking of lane segment is blocked.
bool HasBlockedLaneMarkingInLaneSegment(
    const std::map</*lane_marking_id=*/int64_t,
                   std::vector<LaneMarkingBlockage>>&
        blocked_lane_marking_segments,
    const pnc_map::Lane* lane, const math::pb::Range& arc_length_range_m,
    bool to_left);

// Returns true if planner should consider the open door signal, like the open
// door is facing ego path.
bool ShouldConsiderOpenDoorSignal(const planner::PlannerObject& planner_object,
                                  double signed_lat_gap,
                                  double relative_box_heading);

// Find the closest lead vehicle agent's primary predicted trajectory. It is
// possible that this function returns a nullptr if there is no lead agent.
const PredictedTrajectoryWrapper* FindClosestLeadAgentTrajectory(
    const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& lane_sequence_result,
    const math::geometry::PolylineCurve2d& nominal_path,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>&
        object_prediction_map);
#endif

// Returns true if given side of lane marking of any lane segment is blocked in
// the sequence.
bool HasBlockedLaneMarkingInSegmentSequence(
    const ::google::protobuf::RepeatedPtrField<
        ::planner::pb::LaneMarkingSegment>& blocked_marking_segments,
    const std::vector<LaneSegment>& segment_sequence, bool to_left);

// Returns true if given start pos and end pos are valid.
bool IsValidArcLengthRange(double start_pos, double end_pos);

// Returns true if given arc length range are valid.
bool IsValidArcLengthRange(const math::Range1d& arc_length_range_m);

// Returns true if second range if sub range of first range.
bool IsSubRange(const math::pb::Range& first_range,
                const math::pb::Range& second_range);

// Returns true if the construction zone is a map change area who has target map
// change area related attributes. If the parameter |attr| is nullopt, it is
// expected to return true for all map change areas.
bool IsMapChangeArea(
    const ConstructionZone& cz,
    const std::optional<voy::MapChangeAreaAttribute>& attr_need = std::nullopt);
bool IsMapChangeArea(
    const voy::ConstructionZone& cz,
    const std::optional<voy::MapChangeAreaAttribute>& attr_need = std::nullopt);

// Checks whether ego is near end of lane sequence.
bool IsEgoNearEndOfLaneSequence(
    const math::geometry::PolylineCurve2d& nominal_path_lane_sequence,
    const math::geometry::Point2d& front_bumper_position);

// Checks whether ego's current lane is short.
bool IsEgoInShortLaneSequence(
    const math::geometry::PolylineCurve2d& nominal_path_lane_sequence);

// Finds the center point of a convex polygon.
math::geometry::Point2d ComputeCentroid(
    const math::geometry::PolygonWithCache2d& polygon);

// Connects an array of lane center lines into one polyline.
math::geometry::PolylineCurve2d ConnectLanesCenterLines(
    const std::vector<const pnc_map::Lane*>& lanes);

// Checks whether ego is in the opposite road.
bool IsEgoInTheOppositeRoad(
    const math::geometry::Point2d& rear_axle_position,
    const std::vector<const pnc_map::Lane*>& current_lanes_for_regional_path);

// Checks whether ego will borrow the opposite road.
bool WillEgoBorrowOppositeRoad(
    const math::geometry::PolylineCurve2d& extended_path,
    const std::vector<const pnc_map::Lane*>& lane_sequence);

// Formats a field in a list of a class or struct into string.
template <typename Iterator, typename ElementType, typename ValueType = int64_t>
std::string FormatValueSequenceIntoString(
    const Iterator& begin, const Iterator& end,
    ValueType (*GetTargetValue)(const ElementType& element),
    const std::string& sep = "|") {
  if (begin == end || GetTargetValue == nullptr) {
    return "";
  }
  std::ostringstream oss;
  for (Iterator itr = begin; itr != end; ++itr) {
    oss << GetTargetValue(*itr);
    if (std::next(itr) != end) {
      oss << sep;
    }
  }
  return oss.str();
}

// Formats a value sequence into string and the element in the sequence should
// be simple type.
template <typename Iterator, typename ElementType>
std::string FormatValueSequenceIntoString(const Iterator& begin,
                                          const Iterator& end,
                                          const std::string& sep = "|") {
  return FormatValueSequenceIntoString<Iterator, ElementType, ElementType>(
      begin, end,
      /*GetTargetValue=*/[](const ElementType& value) { return value; }, sep);
}

// Formats a field in a list of a class or struct into string.
template <typename T, typename ValueType = int64_t>
std::string FormatValueVecIntoString(
    const std::vector<T>& object_list,
    ValueType (*GetTargetValue)(const T& object),
    const std::string& sep = "|") {
  if (object_list.empty()) {
    return "";
  }
  return FormatValueSequenceIntoString(object_list.begin(), object_list.end(),
                                       GetTargetValue, sep);
}

// Gets all keys from a map.
template <class KeyType, class ValueType>
std::vector<KeyType> GetAllKeysFromMap(
    const std::map<KeyType, ValueType>& value_map) {
  if (value_map.empty()) {
    return {};
  }
  std::vector<KeyType> keys;
  keys.reserve(value_map.size());
  std::transform(
      value_map.begin(), value_map.end(), std::back_inserter(keys),
      [](const std::pair<KeyType, ValueType>& pair) { return pair.first; });
  return keys;
}

// Converts a PB format construction zone into ConstructionZone object.
void ConvertConstructionZoneToPB(
    const ConstructionZone& construction_zone,
    voy::ConstructionZone* mutable_construction_zone_proto);

// Converts object ConstructionZone into a PB format construction zone.
ConstructionZone ConvertPBToConstructionZone(
    const voy::ConstructionZone& construction_zone_proto);

// Gets values from object list.
// Note: if |IsObjectValid| is a nullptr, it means no need to check whether the
// value is valid.
template <typename T, typename ValueType = double>
void GetValueFromObjects(const std::vector<T>& object_list,
                         ValueType (*GetTargetValue)(const T& object),
                         bool (*IsObjectValid)(const T& object),
                         std::vector<ValueType>* result_vec) {
  if (object_list.empty() || GetTargetValue == nullptr ||
      result_vec == nullptr) {
    return;
  }
  result_vec->reserve(object_list.size());
  for (const T& object : object_list) {
    if (IsObjectValid != nullptr && !IsObjectValid(object)) {
      continue;
    }
    result_vec->push_back(GetTargetValue(object));
  }
  return;
}

// Converts |math::pb::Range| to |math::Range1d|.
inline math::Range1d ConvertPbToRange(const math::pb::Range& pb_range) {
  return {pb_range.start_pos(), pb_range.end_pos()};
}

// Returns the overlapped range of |range_a| and |range_b|, if any;
// Otherwise, returns std::nullopt.
inline std::optional<math::Range1d> GetOverlappedRange(
    const math::Range1d& range_a, const math::Range1d& range_b) {
  const double start = std::max(range_a.start_pos, range_b.start_pos);
  const double end = std::min(range_a.end_pos, range_b.end_pos);
  return start < end ? std::make_optional<math::Range1d>(start, end)
                     : std::nullopt;
}

// Return the number of physical cores for planner node.
// CPU pinning is considered.
size_t CPUCoresNumberOfPlanner();

// High ratio of the maximum cores a thread pool can
// use to the total physical cores number. Usually
// needed in tbb.
constexpr double kHighCoresRatioOfThreadsPool = 0.8;

// Medium-High ratio of the maximum cores a thread pool can
// use to the total physical cores number.
constexpr double kMediumHighCoresRatioOfThreadsPool = 0.75;

// Medium ratio of the maximum cores a thread pool can
// use to the total physical cores number.
constexpr double kMediumCoresRatioOfThreadsPool = 0.6;

namespace local_state_view {
// Returns whether ego car is in autonomy control mode from planner's local
// view.
bool IsLocallyInAutonomyControlMode(const voy::VehicleMode& canbus_mode);

}  // namespace local_state_view

}  // namespace common

}  // namespace planner

#endif  // ONBOARD_PLANNER_UTILITY_COMMON_COMMON_UTILITY_H_
