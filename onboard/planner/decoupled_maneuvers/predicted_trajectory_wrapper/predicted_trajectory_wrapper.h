#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_PREDICTED_TRAJECTORY_WRAPPER_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_PREDICTED_TRAJECTORY_WRAPPER_H_

#include <optional>
#include <string>
#include <strings/stringprintf.h>
#include <unordered_set>
#include <utility>
#include <vector>

#include <glog/logging.h>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association_common.h"
#include "planner/world_model/traffic_participant/predicted_pose.h"
#include "planner/world_model/traffic_participant/predicted_trajectory_interpolator.h"
#include "planner_protos/predicted_trajectory_wrapper.pb.h"
#include "prediction_protos/agent.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"

namespace planner {

using PredictionId = int32_t;

// Creates a global unique id for a bp trajectory.
inline std::string GetBpUniqueId(int64_t object_id, PredictionId bp_id) {
  return strings::StringPrintf("obj-%ld-pred-%d", object_id, bp_id);
}

// This class wraps prediction::pb::PredictedTrajectory for planner usage.
// Planner should always use this class instead of the raw proto.
// NOTE: The stationary intention is an agent-level signal, which means it is
// the same for all predicted trajectories of one agent. A stationary trajectory
// (with all predicted poses being the same) might have a start-to-move (STM)
// StationaryIntention. The corresponding STM trajectory id is specified in the
// StationaryIntention.
// TODO(mengze): Refactor to only wrap stationary intention in PlannerObject.
class PredictedTrajectoryWrapper {
 public:
  PredictedTrajectoryWrapper(
      const prediction::pb::Agent& agent,
      const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
      bool is_primary_trajectory = false,
      const std::optional<int64_t>& max_interested_prediction_timestamp =
          std::nullopt);

  PredictedTrajectoryWrapper(
      const voy::TrackedObject& tracked_object_proto,
      const prediction::pb::PredictedTrajectory& predicted_trajectory_proto,
      bool is_primary_trajectory = false,
      prediction::pb::StationaryIntention object_stationary_intention = {});

  explicit PredictedTrajectoryWrapper(
      const pb::PredictedTrajectoryWrapper& predicted_trajectory_wrapper_proto);

  // See predicted_trajectory.proto for detailed definition.
  PredictionId id() const { return predicted_trajectory_proto_.id(); }

  // Returns true if this predicted trajectory is for decoupled maneuver.
  bool is_multi_output_trajectory() const {
    return predicted_trajectory_proto_.is_multi_output_trajectory();
  }

  // Returns true if this predicted trajectory is the primary trajectory.
  bool is_primary_trajectory() const { return is_primary_trajectory_; }

  double GetTrajectoryMinimumSpeed() const;
  double GetTrajectoryMaximumSpeed() const;
  double GetTrajectoryAverageSpeed() const;

  // Returns true if object level stationary intention is STATIONARY_TO_MOVE.
  bool MightStartToMoveSoon() const {
    return object_stationary_intention_.stationary_intention_type() ==
           prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE;
  }

  // Returns stationary intention.
  const prediction::pb::StationaryIntention& StationaryIntention() const {
    return object_stationary_intention_;
  }

  bool IsCompleteReverseDriving() const {
    return trajectory_motion_type_ ==
           prediction::pb::TrajectoryMotionType::COMPLETE_REVERSE_DRIVING;
  }

  bool IsKTurnDriving() const {
    return trajectory_motion_type_ ==
           prediction::pb::TrajectoryMotionType::KTURN_DRIVING;
  }

  prediction::pb::TrajectoryMotionType trajectory_motion_type() const {
    return trajectory_motion_type_;
  }

  // The ID of the object that this BP belongs to.
  ObjectId object_id() const { return object_id_; }

  //
  //  Functions for trajectory poses.
  //

  const prediction::pb::PredictedTrajectory& predicted_trajectory_proto()
      const {
    return predicted_trajectory_proto_;
  }

  bool empty() const {
    return predicted_trajectory_proto_.traj_poses().empty();
  }

  int size() const { return predicted_trajectory_proto_.traj_poses_size(); }

  const planner::pb::TrajectoryPose& pose(int index) const {
    DCHECK_LE(0, index);
    DCHECK_LT(index, size());
    return predicted_trajectory_proto_.traj_poses(index);
  }

  // Returns false if the query timestamp is not in the predicted timestamp
  // range.
  bool is_valid_query_timestamp(int64_t query_timestamp) const;

  // Returns a pose at the query timestamp with interpolated position, heading,
  // and velocity information.
  planner::pb::TrajectoryPose ComputePoseAtTimestamp(
      int64_t query_timestamp) const;

  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>& poses()
      const {
    return predicted_trajectory_proto_.traj_poses();
  }

  google::protobuf::RepeatedPtrField<
      planner::pb::TrajectoryPose>::const_iterator
  begin() const {
    return predicted_trajectory_proto_.traj_poses().begin();
  }

  google::protobuf::RepeatedPtrField<
      planner::pb::TrajectoryPose>::const_iterator
  end() const {
    return predicted_trajectory_proto_.traj_poses().end();
  }

  const prediction::pb::YieldIntention& yield_intention() const {
    return predicted_trajectory_proto_.yield_intention();
  }

  //
  //  Functions for tracked object. This should only be used to be compatible
  //  with other structures (e.g. to be used in a PredictedPose ctor).
  //
  const voy::TrackedObject& tracked_object() const {
    return tracked_object_proto_;
  }

  //
  // Functions for predicted_poses
  //

  // TODO(planner): This use_legacy_logic flag is kept for backward
  // compatibility. Some existing code relies on
  // PredictedTrajectory::GetPoses(), which uses logic based on the deprecated
  // TrackedObject class.
  //
  // Ideally, PredictedTrajectoryWrapper::predicted_poses() should produce the
  // same result as PredictedTrajectory::GetPoses(). However, the two currently
  // differ. To ensure consistent behavior during the TrackedObject deprecation
  // process, we temporarily replicate the legacy logic in
  // legacy_predicted_poses_ (logic is ported from
  //  PredictedTrajectory::GetPoses() )
  //
  // In the future, we should unify legacy_predicted_poses_ and predicted_poses_
  // to remove duplication and simplify maintenance.
  const std::vector<PredictedPose>& predicted_poses(
      bool use_legacy_logic = false) const {
    // Return predicted_poses_ by default or if legacy poses are unavailable
    if (!use_legacy_logic || legacy_predicted_poses_.empty()) {
      return predicted_poses_;
    }

    return legacy_predicted_poses_;
  }

  const PredictedPose& prediction_first_pose() const {
    DCHECK(!predicted_poses_.empty());
    return predicted_poses_.front();
  }

  // Maneuver accessor. This should be used carefully since it's not guaranteed
  // to be set.
  const prediction::pb::Maneuver& maneuver() const {
    return predicted_trajectory_proto_.maneuver();
  }

  const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
  associated_route_opt() const {
    return associated_route_opt_;
  }

  void set_associated_route(
      std::vector<route_association::MapElementAndPoseInfo> associated_route) {
    associated_route_opt_ = std::move(associated_route);
  }

  // Note(zhihaoruan): If the map element is virtual (i.e. in junction), agent's
  // center point may not necessarily strictly within the polygon of the
  // element, as we may extend the query range. This result is directly brought
  // from raw_equally_optimal_first_map_elements_.
  const std::vector<route_association::MapElementAndPoseInfo>&
  current_occupied_map_elements() const {
    return current_occupied_map_elements_;
  }

  void set_current_occupied_map_elements(
      std::vector<route_association::MapElementAndPoseInfo>
          current_occupied_map_elements) {
    current_occupied_map_elements_ = std::move(current_occupied_map_elements);
  }

  const std::optional<route_association::MapElementAndPoseInfo>&
  latest_exit_zone_covering_first_pose_opt() const {
    return latest_exit_zone_covering_first_pose_opt_;
  }

  void set_latest_exit_zone_covering_first_pose_opt(
      std::optional<route_association::MapElementAndPoseInfo>
          latest_exit_zone_covering_first_pose_opt) {
    latest_exit_zone_covering_first_pose_opt_ =
        std::move(latest_exit_zone_covering_first_pose_opt);
  }

  const std::optional<route_association::MapElementAndPoseInfo>&
  latest_isolated_lane_opt() const {
    return latest_isolated_lane_opt_;
  }

  void set_latest_isolated_lane_opt(
      std::optional<route_association::MapElementAndPoseInfo>
          latest_isolated_lane_opt) {
    latest_isolated_lane_opt_ = std::move(latest_isolated_lane_opt);
  }

  // Functions for likelihood.
  double Likelihood() const {
    return predicted_trajectory_proto_.probability_in_ppm() * 1e-6;
  }

  // Returns true if this predicted trajectory is stationary for the entire
  // prediction horizon.
  bool IsStationary() const { return is_stationary_; }

  // Returns true if the predicted trajectory is turning.
  bool IsTurning() const;

  bool IsTurningLeft() const;

  bool IsTurningRight() const;

  // Interpolates the poses to get a pose at query_timestamp.
  std::optional<math::geometry::OrientedBox2d> GetInterpolatedBoundingBox(
      double query_timestamp, bool use_legacy_logic = false) const;

  pb::PredictedTrajectoryWrapper ToProto() const;

  const std::unordered_set<prediction::pb::Maneuver::Attribute>&
  prediction_attributes() const {
    return prediction_attributes_;
  }

  bool should_ignore() const { return should_ignore_; }

 private:
  // Returns true if object level stationary intention is STATIONARY_NOT_MOVE.
  bool HasStationaryNotToMoveIntention() const {
    return object_stationary_intention_.stationary_intention_type() ==
           prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE;
  }

  void UpdatePredictedPosesForStaticObject(
      const voy::TrackedObject& tracked_object_proto);

  void UpdatePredictedPosesForDynamicObject(
      const voy::TrackedObject& tracked_object_proto);

  void UpdateLegacyPredictedPoses(
      const std::optional<int64_t>& max_interested_prediction_timestamp);

  const prediction::pb::PredictedTrajectory& predicted_trajectory_proto_;

  const voy::TrackedObject& tracked_object_proto_;

  const ObjectId object_id_;

  std::vector<PredictedPose> predicted_poses_;

  bool is_primary_trajectory_ = false;

  bool is_stationary_ = false;

  // Stationary intention of one object, identical among all
  // PredictedTrajectoryWrapper instances.
  prediction::pb::StationaryIntention object_stationary_intention_;

  // Trajectory motion type to differentiate various motions, such as normal
  // (i.e., forwarding), reversing, k-turn, etc.
  prediction::pb::TrajectoryMotionType trajectory_motion_type_;

  // Lane association result for the predicted trajectory, set by calling route
  // associator.
  std::optional<std::vector<route_association::MapElementAndPoseInfo>>
      associated_route_opt_ = std::nullopt;
  // Current occupied map elements computed from the predicted trajectory. Use
  // PredictedTrajectoryRouteAssociator.raw_equally_optimal_first_map_elements_
  // for now. If the map element is virtual (i.e. in junction), agent's
  // center point may not necessarily strictly within the polygon of the
  // element, as we may extend the query range.
  std::vector<route_association::MapElementAndPoseInfo>
      current_occupied_map_elements_;
  // Latest exit zone covers the first pose (i.e., whose arc length projected on
  // the associated road covers that of the first pose of predicted trajectory)
  // which is or ever was occupied by the agent. Set after obtaining the result
  // of route associator.
  std::optional<route_association::MapElementAndPoseInfo>
      latest_exit_zone_covering_first_pose_opt_ = std::nullopt;
  // Latest isolated lane (e.g., regular lane, bus lane, etc.) the agent ever
  // occupied or occupies, which is obtained from route association result. If
  // the agent newly appears in tracking or we do not observe it is in an
  // isolated lane so far, it is |std::nullopt|.
  std::optional<route_association::MapElementAndPoseInfo>
      latest_isolated_lane_opt_ = std::nullopt;

  // The attributes set of the predicted trajectory.
  std::unordered_set<prediction::pb::Maneuver::Attribute>
      prediction_attributes_;

  std::vector<PredictedPose> legacy_predicted_poses_;

  // Flag indicating whether this predicted trajectory should be ignored.
  // Helps reduce latency by filtering out low-rank or low-probability
  // trajectories. The value is determined by the logic in
  // ShouldIgnorePredictedTrajectory().
  bool should_ignore_{false};
};

// Util function for down-sampling a predicted trajectory.
std::vector<int> ComputeSamplingIndexForPredictedTrajectory(
    const PredictedTrajectoryWrapper& predicted_trajectory, int sampled_size,
    double min_required_odom_progress_in_meter);

}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_PREDICTED_TRAJECTORY_WRAPPER_H_
