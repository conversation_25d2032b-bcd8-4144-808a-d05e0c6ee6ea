#ifndef ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_UTILITY_H_
#define ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_UTILITY_H_

#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "planner/world_model/construction_zone/construction_zone.h"
#include "planner/world_model/immediate_pull_over/stage/stage_manager.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info.h"
#include "planner/world_model/regional_map/regional_map.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner_protos/immediate_pull_over.pb.h"
#include "planner_protos/regional_map.pb.h"

namespace planner {

// Returns true if pdz intersects with a construction zone.
bool IsPDZIntersectsWithConstructionZone(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<ConstructionZone>& construction_zones);

// Returns true if the candidate pdz is qualified for mrc immediate pull over.
bool IsPDZQualifiedForMRC(const PickupDropoffZoneInfo& pdz_info,
                          const std::vector<hdmap::StopCellEntity::Type>&
                              forbidden_stop_types_for_mrc);

// Converts regional path's turn info to RouteDirection. For no junction path,
// will return kBeforeJunction.
pb::RouteDirection ConvertToRouteDirection(const pb::TurnInfo& path_turn_info);

// Returns the required longitudinal distance for executing pull over
// comfortably.
double GetRequiredLongitudinalDistForComfortPullOver(double ego_speed);

// Returns true if the pdz is suitable for executing pull over.
bool IsLongitudinalDistanceBetweenEgoAndPDZSufficient(
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const RobotState& robot_state, const PickupDropoffZoneInfo& pdz_info);

// Returns true if the pdz generated by redirecting mode is better than pdz
// generated by local mode.
bool IsRedirectingModePDZBetterThanLocalModePDZ(
    const RobotState& robot_state,
    const PickupDropoffZoneInfo& pdz_info_from_redirecting_mode,
    const PickupDropoffZoneInfo& pdz_info_from_local_mode);

// Returns true if the routing-source immediate pull over should be ignored or
// terminated.
bool ShouldIgnoreOrTerminateRoutingSourceImmediatePullOver(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const routing::pb::RoutePoint& destination,
    pb::PullOverProgress pull_over_progress, double accumulated_distance);

// Returns true if the routing-source immediate pull over should be ignored or
// terminated.
bool ShouldIgnoreOrTerminateImmediatePullOverWhenEgoNearDestination(
    const RegionalMap& regional_map, bool has_found_immediate_pullover_pdz,
    pb::PullOverProgress pull_over_progress);

// Querys stop cell entities by a point.
std::vector<hdmap::StopCellEntity> QueryStopCellEntitiesByPoint(
    const pnc_map::PncMapService& pnc_map_service,
    const math::geometry::Point2d& ego_position);

// Check if ego is almost stopped in a stop cell.
bool IsEgoAlmostStoppedInStopCell(const pnc_map::PncMapService& pnc_map_service,
                                  const math::geometry::Point2d& ego_position,
                                  double ego_speed);

// Returns true if ego is in the exit zone range.
bool IsEgoInExitZoneRange(const RobotState& robot_state,
                          const PickupDropoffZoneInfo& pdz_info);

// Gets the stop cell type which ego is located.
std::string GetStopCellTypeWhichEgoLocated(
    const std::optional<PickupDropoffZoneInfo>& pickup_dropoff_zone_info,
    const voy::Pose& current_pose);

// Return true if the existing pdz is valid when planner receive a new immediate
// pull over request.
bool IsExistingPDZStillValidAfterRequestChange(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<hdmap::StopCellEntity::Type>&
        forbidden_stop_types_for_mrc,
    mrc::pb::MrmType current_cycle_mrm_type,
    mrc::pb::MrmType last_cycle_mrm_type,
    pb::PullOverProgress pull_over_progress);

// Checks if the existing immediate pull over pdz is valid in pull over.
bool IsPDZValidInPullOver(const PickupDropoffZoneInfo& pdz_info,
                          const pnc_map::Lane& current_lane,
                          const RobotState& robot_state,
                          const std::vector<hdmap::StopCellEntity::Type>&
                              forbidden_stop_types_for_mrc,
                          mrc::pb::MrmType current_cycle_mrm_type,
                          mrc::pb::MrmType last_cycle_mrm_type,
                          pb::PullOverProgress pull_over_progress);

// Checks if the existing immediate pullover pdz is reachable in routing.
bool IsPDZReachableInRouting(
    const std::vector<const pnc_map::Section*>& regional_path_sections,
    const pb::LaneSequenceCandidate& last_selected_lane_sequence_candidate,
    const pnc_map::Lane& current_lane, const PickupDropoffZoneInfo& pdz_info,
    int64_t current_timestamp, int64_t proposal_request_timestamp,
    bool is_using_redirecting_mode, pb::PullOverProgress pull_over_progress);

// Returns true is the pdz is suitable for pull over.
bool IsPDZSuitableForPullOver(
    const PickupDropoffZoneInfo& pdz_info,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const RobotState& robot_state,
    const std::vector<ConstructionZone>& construction_zones,
    const std::vector<hdmap::StopCellEntity::Type>&
        forbidden_stop_types_for_mrc,
    bool is_triggered_by_mrc);

// Generates lane change request, including immediate right lane change request
// and restrict left lane change request.
std::pair</*request_immediate_right_lc=*/bool,
          /*request_restricted_left_lc=*/bool>
GenerateLaneChangeRequest(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const stage::StageManager& stage_manager,
    const std::optional<PickupDropoffZoneInfo>& pdz_info,
    bool is_immediate_pull_over_triggered, bool is_using_redirecting_mode);

// Extracts immediate pull over state from mrc request.
routing::pb::ImmediatePullOverState ConvertMrcRequestToImmediatePullOverState(
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request);

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_IMMEDIATE_PULL_OVER_IMMEDIATE_PULL_OVER_UTILITY_H_
