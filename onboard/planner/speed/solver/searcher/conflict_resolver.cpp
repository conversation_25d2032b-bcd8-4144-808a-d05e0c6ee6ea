#include "planner/speed/solver/searcher/conflict_resolver.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <map>
#include <optional>
#include <string>
#include <utility>
#include <vector>

#include "math/constants.h"
#include "math/math_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/agent_reaction/agent_reaction_calculator/agent_reaction_calculator.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/constraint/decision_defs.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/solver/searcher/conflict_evaluator.h"
#include "planner/speed/solver/searcher/profile_searcher_defs.h"
#include "planner/speed/solver/searcher/profile_searcher_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/solver/util/risk_mitigation_util.h"
#include "planner/speed/timemap/time_map.h"
#include "planner/trajectory/util/trajectory_util.h"
#include "planner/utility/common/position_estimate_error.h"
#include "planner/utility/common/risk_model.h"
#include "planner_protos/risk_model.pb.h"
#include "planner_protos/speed_decision.pb.h"
#include "planner_protos/speed_solver_debug.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace {

// The potential number of conflict-resolving candidates we have.
// Currently we have four: FULL_STOP, EMERGENCY_STOP/EXTRA_FULL_STOP, and
// MAX_SPEED, CONST_SPEED.
constexpr int kPotentialCandidateNum = 4;

// Severity = pow(SeverityFactor, SeverityLevel)
constexpr double kSeverityFactor = 2;

// A heuristic threshold to determine whether a conflict occurs
// when ego is almost stopped. We expect the conflict should be in
// relative low risk (aka agent would be more responsible for
// avoiding the conflict) if ego speed is already below this at the conflict.
constexpr double kLowRiskConflictingSpeedInMps = 0.5;

// The absolute threshold to determine whether the max_displacement_wo_conflict
// difference between two conflicts is significant. This value approximates
// the length from ego front bumper to front axis.
constexpr double kMaxDisplacementDiffThresholdInMeter = 1.0;
// The relative threshold to determine whether the max_displacement_wo_conflict
// difference between two conflicts is significant.
constexpr double kMaxDisplacementDiffThresholdInRatio = 0.1;

// The absolute threshold to determine whether two SCR candidates
// do not have significant severity score diff thus we can apply
// hysteresis. Note: this is an empirical value from experience
// as the severity score does not have a unit. The value is
// subject to tuning.
constexpr double kAbsStConflicDiffForHysteresis = 0.01;
// The relative percentage threshold to determine whether
// two SCR candidates do not have significant severity
// score diff thus we can apply hysteresis.
constexpr double kRelStConflictDiffForHysteresis = 0.05;

// The threshold to determine whether a potential collision is near
// on time. If the TTC is smaller than this value, it is near.
constexpr double kNearTtcInSec = 1.5;

// In the binary search for the feasible EXTRA_FULL_STOP candidate, the diff of
// the selected acceleration and the optimal one is less than this value.
constexpr double kExtraFullStopAccelResolutionInMpss = 1.0;

// When collision detects before plan init time(ttc<0), the tolerances are used
// for determine whether the vel/accel diff between the ego and the agent should
// trigger the EB or the FS heuristic.
// TODO(shijie): Consider tuning the threshold for more precise collision
// recall.
constexpr double kNotSlowerToleranceInMps = 1.15;
constexpr double kRelaxedNotAcceleratingToleranceInMpss = -0.5;
constexpr double kNotAcceleratingToleranceInMpss = -0.35;
constexpr double kSignificantDecelerationInMpss = -1.5;

// The dimensions of the perturb states. Currently it is 2 (x and y
// coordinates).
constexpr int kPerturbDimension = 2;
constexpr int kMaxPerturbCandidateNum = kPerturbDimension * 2 + 1;

// Returns true if any of 'constraints' in the 'constraint_ixs' set
// allows emergency brake and its decision is YIELD, also updates
// 'allow_eb_cix' to be the constraint index that allows EB.
bool AllowEmergencyBrake(const std::vector<Constraint>& constraints,
                         const std::vector<Decision>& decisions,
                         const std::vector<int>& violated_constraint_ixs,
                         int& allow_eb_cix) {
  const auto eb_it = std::find_if(
      violated_constraint_ixs.begin(), violated_constraint_ixs.end(),
      [&constraints, &decisions](int ix) {
        return constraints[ix].settings.allow_emergency_brake &&
               decisions[ix] == Decision::YIELD;
      });
  if (eb_it != violated_constraint_ixs.end()) {
    allow_eb_cix = *eb_it;
    return true;
  }
  return false;
}

bool AllowExtraFullStop(const std::vector<Constraint>& constraints,
                        const std::vector<Decision>& decisions,
                        const std::vector<int>& violated_constraint_ixs,
                        int& allow_eb_cix) {
  const auto eb_it = std::find_if(
      violated_constraint_ixs.begin(), violated_constraint_ixs.end(),
      [&constraints, &decisions](int ix) {
        return !constraints[ix].settings.allow_emergency_brake &&
               constraints[ix].settings.allow_extra_full_stop &&
               decisions[ix] == Decision::YIELD;
      });
  if (eb_it != violated_constraint_ixs.end()) {
    allow_eb_cix = *eb_it;
    return true;
  }
  return false;
}

// Returns true if we allow braking more than -5, can be EFS or EB.
bool AllowBrakeMoreThanFullStop(const std::vector<Constraint>& constraints,
                                const std::vector<Decision>& decisions,
                                const std::vector<int>& violated_constraint_ixs,
                                int& allow_cix) {
  return AllowEmergencyBrake(constraints, decisions, violated_constraint_ixs,
                             allow_cix) ||
         AllowExtraFullStop(constraints, decisions, violated_constraint_ixs,
                            allow_cix);
}

// Returns true if all of 'constraints' in the 'constraint_ixs' set
// allows max speed and is not a no-pass constraint.
// TODO(tienan): consider whether we can relax the restriction, for example,
// do not need to consider very far-away (e.g. after 5s) violation.
bool AllowMaxSpeed(const std::vector<Constraint>& constraints,
                   const std::vector<int>& violated_constraint_ixs) {
  return std::all_of(violated_constraint_ixs.begin(),
                     violated_constraint_ixs.end(), [&constraints](int ix) {
                       return constraints[ix].settings.allow_max_speed &&
                              !IsNoPassConstraint(constraints[ix]);
                     });
}

// Returns true if SCR decides ego can react passively to all agents
// corresponding to the violated constraints.
bool AllowConstSpeed(
    const std::vector<Constraint>& constraints,
    const std::vector<int>& violated_constraint_ixs,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map) {
  for (const int ix : violated_constraint_ixs) {
    const Constraint& constraint = constraints[ix];
    if (!constraint.settings.allow_const_speed) {
      return false;
    }
    const ObjectId object_id = constraint.obj_id;
    // We do not allow CS to constraint that is not associated with actual
    // agent.
    if (object_id <= 0) {
      return false;
    }
    const auto object_iter = planner_object_map.find(object_id);
    if (object_iter == planner_object_map.end()) {
      return false;
    }
  }
  return true;
}

// Updates the decision to YIELD except that this is a
// must-pass/yield-impossible constraint, in which case update it to PASS.
void MaybeUpdateToYieldDecision(const std::vector<Constraint>& constraints,
                                const int c_ix, const double discomfort,
                                std::vector<Decision>& decisions) {
  if (IsMustPassConstraintAtDiscomfort(constraints[c_ix], discomfort) ||
      IsYieldImpossibleConstraint(constraints[c_ix], discomfort)) {
    decisions[c_ix] = Decision::PASS;
    // Constraints in the same group as a must-pass constraint should have
    // pass/ignore decisions.
    const bool decisions_are_consistent =
        ForAllConstraintsInGroup(constraints, c_ix, [&](int same_group_cix) {
          return decisions[same_group_cix] == Decision::PASS ||
                 decisions[same_group_cix] == Decision::IGNORE;
        });
    DCHECK(decisions_are_consistent);
    return;
  }
  decisions[c_ix] = Decision::YIELD;
}

// Updates the decision to PASS except for a no-pass constraint, in which
// case update it to YIELD.
void MaybeUpdateToPassDecision(const Constraint& constraint,
                               Decision& decision) {
  if (IsNoPassConstraint(constraint)) {
    decision = Decision::YIELD;
    return;
  }
  decision = Decision::PASS;
}

// Returns true if the pass entries of the constraint are always behind ego.
bool PassEntriesAlwaysBehindEgo(
    const std::map<int, std::vector<TimeMap::Entry>>& pass_entries_map,
    const int c_ix) {
  for (const auto& pass_entries : pass_entries_map) {
    for (const TimeMap::Entry& pass_entry : pass_entries.second) {
      if (pass_entry.constraint_ix == c_ix && pass_entry.start_or_end_x > 0.0) {
        return false;
      }
    }
  }
  return true;
}

// Returns true if the yield entries of the constraint are always in front of
// ego.
bool YieldEntriesAlwaysInFrontOfEgo(
    const std::map<int, std::vector<TimeMap::Entry>>& yield_entries_map,
    const int c_ix) {
  for (const auto& yield_entries : yield_entries_map) {
    for (const TimeMap::Entry& yield_entry : yield_entries.second) {
      if (yield_entry.constraint_ix == c_ix &&
          yield_entry.start_or_end_x < 0.0) {
        return false;
      }
    }
  }
  return true;
}

// If 1) MS/CS/SS is not among the min-risk candidates.
//    2) All braking candidates have minor risk(<risk_diff_absolute_tolerance).
// return true for allowing only retain FS as the final SCR candidate if FS is
// among the min-risk candidates, otherwise false.
bool AllCandidatesAreBrakingWithMinorRisk(
    double minor_risk_tolerance,
    const std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs) {
  DCHECK_GT(low_risk_candidate_ptrs.size(), 1);
  if (std::any_of(
          low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
          [](const ConflictResolvingCandidate* candidate) {
            return (candidate->conflict_resolving_type ==
                    pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE) ||
                   (candidate->conflict_resolving_type ==
                    pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE) ||
                   (candidate->conflict_resolving_type ==
                    pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE);
          })) {
    return false;
  }
  if (!std::all_of(
          low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
          [minor_risk_tolerance](const ConflictResolvingCandidate* candidate) {
            return candidate->collision_risk <= minor_risk_tolerance &&
                   candidate->collision_risk >= 0.0;
          })) {
    return false;
  }
  return true;
}

// Returns true if we consider a decision heuristic and set a decision for the
// violated constraint. Particularly, for any constraint that has a time map
// entry at the ego current time (aka. have an pass entry at time_ix 0), we
// check whether the constraint is always in front of ego (for yield), or always
// behind ego (for pass). If so, make a heuristic SCR decision to YIELD (always
// in front) or PASS (always behind).
bool MakeDecisionForViolatedConstraintPerRelativePosition(
    const TimeMap& max_discomfort_time_map,
    const std::vector<Constraint>& constraints, const int c_ix,
    std::vector<Decision>& decisions) {
  // Early return if the constraint has not emerged at the current time.
  // Note: we use pass entries to correctly consider whether the object
  // currently exists. For yield entries, they can be projected to
  // far-behind time_ix even the constraint exists now, e.g. for a single-state
  // constraint spanning (-5, 10), as the yield entries are generated using
  // constraint end time.
  const std::map<int, std::vector<TimeMap::Entry>>& pass_entries_map =
      max_discomfort_time_map.pass_entries();
  if (pass_entries_map.find(0) == pass_entries_map.end()) {
    return false;
  }
  if (!std::any_of(pass_entries_map.at(0).begin(), pass_entries_map.at(0).end(),
                   [c_ix](const TimeMap::Entry& entry) {
                     return entry.constraint_ix == c_ix;
                   })) {
    return false;
  }

  // Try to set pass decision if the pass entry is always behind ego.
  if (PassEntriesAlwaysBehindEgo(pass_entries_map, c_ix)) {
    MaybeUpdateToPassDecision(constraints[c_ix], decisions[c_ix]);
    return true;
  }
  // Try to set yield decision if the yield entry is always in front of ego.
  if (YieldEntriesAlwaysInFrontOfEgo(max_discomfort_time_map.yield_entries(),
                                     c_ix)) {
    MaybeUpdateToYieldDecision(constraints, c_ix, Discomforts::kMax, decisions);
    return true;
  }
  return false;
}

// Update the decisions to all violated constraints to be the expected
// decision based on the conflict resolving type. The decisions are made by:
// 1) Heuristic given relative current position of ego and other constraint;
// 2) Default: for the remaining constraints, it should either be: i) based on
// the heuristic towards certain candidate type (e.g. always prefer yield for
// FS/EFS/EB), or ii) the initial decision for others. If the initial decision
// is NOT_DECIDED, we explore both pass and yield for the constraint.
void UpdateViolatedConstraintDecisions(
    const TimeMap& max_discomfort_time_map,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& initial_decisions,
    const std::vector<int>& violated_constraint_ixs,
    const std::vector<int>& non_hard_violated_constraint_ixs,
    const pb::ConflictResolvingType_Enum conflict_resolving_type,
    std::vector<Decision>& decisions) {
  for (const int c_ix : violated_constraint_ixs) {
    // Before being finalized here, the decision should be IGNORE, which was
    // mandatorily set during the profile iteration.
    DCHECK_EQ(decisions[c_ix], Decision::IGNORE);
    // Handling the decision heuristic for constraints based on the relative
    // position.
    if (MakeDecisionForViolatedConstraintPerRelativePosition(
            max_discomfort_time_map, constraints, c_ix, decisions)) {
      continue;
    }
    // For the other constraints, their decision should either be based on
    // the SCR candidate heuristic or be set to the initial decision, for which
    // we will explore both the PASS and YIELD during the SCR evaluation.
    // Currently we only explore the both possibilities for the MS candidate.
    if (conflict_resolving_type ==
            pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE ||
        conflict_resolving_type ==
            pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE ||
        conflict_resolving_type ==
            pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE) {
      decisions[c_ix] = initial_decisions[c_ix];
    } else {
      DCHECK(conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_ML_SPEED_PROFILE ||
             conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE ||
             conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE ||
             conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE);
      MaybeUpdateToYieldDecision(constraints, c_ix, Discomforts::kMax,
                                 decisions);
    }
  }
  // For ML 3.0, we need to update soft/if_possible constraint's decision from
  // IGNORE to PASS/YIELD in order to finalize its discomfort.
  if (conflict_resolving_type ==
      pb::ConflictResolvingType_Enum_ML_SPEED_PROFILE) {
    for (const int c_ix : non_hard_violated_constraint_ixs) {
      MaybeUpdateToYieldDecision(constraints, c_ix, Discomforts::kMax,
                                 decisions);
    }
  }
}

// Updates the candidate to record the collision frame infos, if it has
// the highest risk so far.
void MaybeUpdateCandidate(
    const double single_constraint_risk, const int c_ix, const double ttc,
    std::vector<PerturbTypeAndCollisionInfo> collision_frame_infos,
    double& max_risk_for_the_candidate, ConflictResolvingCandidate& candidate) {
  if (math::UpdateMax(single_constraint_risk, max_risk_for_the_candidate)) {
    candidate.collision_risk = single_constraint_risk;
    candidate.max_risk_cix = c_ix;
    candidate.ttc_from_risk_model = ttc;
    candidate.collision_risk_infos = std::move(collision_frame_infos);
  }
}

// Returns a subset of all profile candidates whose max displacement value
// is not significantly different from the minimum max displacement candidate
// when they conflict with the same constraints. We always consider a
// candidate if its conflicted constraint is different from the minimum
// max displacement candidate.
std::vector<const ConflictResolvingCandidate*> GetLowMaxDisplacementCandidates(
    const std::vector<ConflictResolvingCandidate*>& profile_candidates) {
  const auto minimum_max_displacement_iter = std::min_element(
      profile_candidates.begin(), profile_candidates.end(),
      [](const ConflictResolvingCandidate* lhs,
         const ConflictResolvingCandidate* rhs) {
        return lhs->st_evaluation_result.max_displacement_wo_conflict <
               rhs->st_evaluation_result.max_displacement_wo_conflict;
      });
  const int minimum_max_displacement_cix =
      (*minimum_max_displacement_iter)->st_evaluation_result.c_ix;
  const double minimum_max_displacement_wo_conflict =
      (*minimum_max_displacement_iter)
          ->st_evaluation_result.max_displacement_wo_conflict;
  // If a max displacement is different from the minimum value by both the
  // absolute and relative thresholds, we consider the difference to be
  // significant.
  const double max_displacement_diff_threshold =
      std::max(kMaxDisplacementDiffThresholdInMeter,
               minimum_max_displacement_wo_conflict *
                   kMaxDisplacementDiffThresholdInRatio);
  std::vector<const ConflictResolvingCandidate*>
      low_max_displacement_candidate_ptrs;
  low_max_displacement_candidate_ptrs.reserve(profile_candidates.size());
  for (ConflictResolvingCandidate* profile_candidate : profile_candidates) {
    const double candidate_encroach_length =
        profile_candidate->st_evaluation_result.max_displacement_wo_conflict;
    const double candidate_encroach_c_ix =
        profile_candidate->st_evaluation_result.c_ix;
    // Do not consider the candidate if it has the same conflicted constraint as
    // the minimum max_displacement candidate and has a significantly larger
    // max_displacement value.
    if (candidate_encroach_c_ix == minimum_max_displacement_cix &&
        candidate_encroach_length - minimum_max_displacement_wo_conflict >
            max_displacement_diff_threshold) {
      continue;
    }
    low_max_displacement_candidate_ptrs.push_back(profile_candidate);
  }
  return low_max_displacement_candidate_ptrs;
}

// Special heuristic logic to consider certain candidates as low risk.
bool ShouldTreatAsLowRiskForSpecialCandidate(
    const ConflictResolvingCandidate& candidate) {
  // We explicit treat an FS candidate as low-risk if the conflicting
  // speed is low and severity level is S0 for all perturbed
  // prediction trajectories.
  if (candidate.conflict_resolving_type ==
      pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE) {
    const std::vector<PerturbTypeAndCollisionInfo>& collision_frame_infos =
        candidate.collision_risk_infos;
    if (collision_frame_infos.empty()) {
      return true;
    }
    if (std::all_of(collision_frame_infos.begin(), collision_frame_infos.end(),
                    [](const PerturbTypeAndCollisionInfo& type_and_info) {
                      const CollisionFrameInfo& info = type_and_info.second;
                      // Should we consider using severity value here?
                      const bool is_severity_s0 =
                          (info.severity.severity_level ==
                           voy::perception::CollisionDetection::S0);
                      return is_severity_s0 &&
                             info.ego_pose->speed() <
                                 kLowRiskConflictingSpeedInMps;
                    })) {
      return true;
    }
  }
  // We explicit treat an CS candidate as low-risk if collision is not found
  // on all candidates, and all of the collisions are S0.
  if (candidate.conflict_resolving_type ==
      pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE) {
    const std::vector<PerturbTypeAndCollisionInfo>& collision_frame_infos =
        candidate.collision_risk_infos;
    if (collision_frame_infos.size() >= kMaxPerturbCandidateNum) {
      return false;
    }
    if (std::all_of(collision_frame_infos.begin(), collision_frame_infos.end(),
                    [](const PerturbTypeAndCollisionInfo& type_and_info) {
                      const CollisionFrameInfo& info = type_and_info.second;
                      return (info.severity.severity_level ==
                              voy::perception::CollisionDetection::S0);
                    })) {
      return true;
    }
  }
  return false;
}

// Returns true if we are confident to trigger the heuristic of using FS.
// Currently we only trigger the FS heuristic if any of the following conditions
// is satisfied: 1) the constraint is not associated with an actual agent; 2)
// the min conflict candidate is EB and TTC is large; 3) the min conflict
// candidate is MS, TTC is large, and the critical object is not a VRU
bool ShouldTriggerFullStopHeuristic(
    const ConflictResolvingCandidate& fs_candidate,
    const std::vector<Constraint>& constraints,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const ConflictResolvingCandidate& min_conflict_profile,
    pb::ConflictResolverDebug* debug) {
  const ConflictEvaluationResult& st_result = fs_candidate.st_evaluation_result;
  const int c_ix = st_result.c_ix;
  DCHECK_GE(c_ix, 0);
  DCHECK_LT(c_ix, constraints.size());
  const ObjectId object_id = constraints[c_ix].obj_id;

  // CHECK 1: If the constraint is not associated with an actual agent
  // (e.g. stop line), we should trigger the heuristic.
  if (object_id <= 0) {
    if (debug != nullptr) {
      absl::StrAppend(debug->mutable_st_eval_debug_str(),
                      "Both the min conflict candidate and FS have near-zero "
                      "conflicting speed and the critical object is not an "
                      "actual agent (e.g. stop line), use FS.");
    }
    return true;
  }

  const auto object_iter = planner_object_map.find(object_id);
  // TODO(tienan): investigate what object is not in the map and handle
  // correspondingly.
  if (object_iter == planner_object_map.end()) {
    LOG(ERROR) << "[Speed][SCR] Object " << object_id
               << " is not in the planner object map.";
    return true;
  }

  // CHECK 2: If the min_conflict_profile is MAX_SPEED, trigger the heuristic
  // unless 1) the object is VRU and the ttc < kNearTtcInSec for preventing
  // aggressive MS maneuver due to the unreliable AR for slow speed objects.
  // 2) MS candidate has negative conflicting speed for scenarios where
  // accelerating can avoid collision and is safer than braking.
  if (min_conflict_profile.conflict_resolving_type ==
      pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE) {
    if (min_conflict_profile.st_evaluation_result.ego_conflicting_speed <
        -math::constants::kEpsilon) {
      if (debug != nullptr) {
        absl::StrAppend(debug->mutable_st_eval_debug_str(),
                        "MS avoids collision. Did not trigger FS heuristic.");
      }
      return false;
    }
    if (object_iter->second.is_pedestrian_or_cyclist() &&
        st_result.ttc < kNearTtcInSec) {
      if (debug != nullptr) {
        absl::StrAppend(
            debug->mutable_st_eval_debug_str(),
            "the min conflict candidate(MS) and FS have near-zero conflicting "
            "speed, the critical object is VRU and ttc is near, use min risk");
      }
      return false;
    }
    if (debug != nullptr) {
      absl::StrAppend(
          debug->mutable_st_eval_debug_str(),
          "the min conflict candidate(MS) and FS have near-zero conflicting "
          "speed, no VRU or ttc is far, use FS");
    }
    return true;
  }
  // CHECK 3: If the ttc is far from the ego. We trigger the heuristic
  // to select FS for comfort if the min conflict candidate is a hard
  // brake one (EFS or EB).
  // TODO(shijie): kNearTtcInSec is an hardcoded empirical value. We should
  // revisit this and decide whether we should associate it with the state
  // of the ego vehicle (e.g. speed).
  if (min_conflict_profile.conflict_resolving_type ==
          pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE ||
      min_conflict_profile.conflict_resolving_type ==
          pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE) {
    if (st_result.ttc > kNearTtcInSec) {
      if (debug != nullptr) {
        absl::StrAppend(
            debug->mutable_st_eval_debug_str(),
            "The min conflict candidate is hard braking. FS have near-zero "
            "conflicting speed. ttc is far, use FS");
      }
      return true;
    }
  }

  return false;
}

// Selects a final candidate with some hysteresis.
const ConflictResolvingCandidate& SelectCandidateWithHysteresis(
    const ConflictResolvingCandidate& selected_candidate,
    const pb::ConflictResolvingType::Enum previous_scr_result,
    const std::vector<ConflictResolvingCandidate*>& profile_candidate_ptrs,
    pb::ConflictResolverDebug* debug) {
  // Consider some hysteresis if the selected candidate w.o. hysteresis is
  // different from the one in the previous cycle.
  if (selected_candidate.conflict_resolving_type != previous_scr_result) {
    const auto same_candidate_as_prev_iter = std::find_if(
        profile_candidate_ptrs.begin(), profile_candidate_ptrs.end(),
        [previous_scr_result](const ConflictResolvingCandidate* candidate) {
          return candidate->conflict_resolving_type == previous_scr_result;
        });
    if (same_candidate_as_prev_iter != profile_candidate_ptrs.end()) {
      // Note the score of the selected candidate is not necessarily the lowest
      // because of the existence of heuristic. Thus, score_diff is not
      // necessarity positive. Here we treat score_diff samely even if the
      // value is negative. This means hysteresis has a higher priority,
      // aka. we can overwrite heuristic with hysteresis.
      const double selected_candidate_severity_score =
          selected_candidate.st_evaluation_result.severity_score;
      const double score_diff =
          (*same_candidate_as_prev_iter)->st_evaluation_result.severity_score -
          selected_candidate_severity_score;
      // Still use the last cycle candidate if the severity score does not have
      // large absolute value difference.
      if (score_diff < kAbsStConflicDiffForHysteresis) {
        if (debug != nullptr) {
          absl::StrAppend(
              debug->mutable_st_eval_debug_str(),
              "Select the previous scr type by abs-value hysteresis.");
        }
        return **same_candidate_as_prev_iter;
      }
      // Still use the last cycle candidate if the severity score does not have
      // large relative value difference.
      if (!math::NearZero(selected_candidate_severity_score) &&
          (score_diff / selected_candidate_severity_score <
           kRelStConflictDiffForHysteresis)) {
        if (debug != nullptr) {
          absl::StrAppend(
              debug->mutable_st_eval_debug_str(),
              "Select the previous scr type by rel-value hysteresis.");
        }
        return **same_candidate_as_prev_iter;
      }
    }
  }
  if (debug != nullptr) {
    // We make the default selection if no other debug has been added before.
    if (!debug->has_st_eval_debug_str()) {
      absl::StrAppend(
          debug->mutable_st_eval_debug_str(),
          "Default min-conflict candidate with acceptable encroach length.");
    }
  }
  return selected_candidate;
}

// Returns true if any candidate conflicts with special constraints
// that do not allow us to conduct risk model evaluation, aka. the
// constraints without a corresponding prediction.
// TODO(tienan): revisit if we can relax the restriction and apply
// risk model in more conditions.
bool AnyCandidateConflictingSpecialConstraints(
    const std::vector<ConflictResolvingCandidate>& candidates,
    const std::vector<Constraint>& constraints) {
  for (const ConflictResolvingCandidate& candidate : candidates) {
    for (const int c_ix : candidate.violated_constraint_ixs) {
      const Constraint& constraint = constraints[c_ix];
      const ConstraintType constraint_type = constraint.type;
      // We should not have the following constraint types in SCR.
      DCHECK_NE(constraint_type, ConstraintType::Constraint_Type_NA);
      DCHECK_NE(constraint_type, ConstraintType::Constraint_Type_GAP_ALIGN);
      DCHECK_NE(constraint_type,
                ConstraintType::Constraint_Type_PROXIMITY_SPEED);
      // AVOID_REGION, STOP_POINT, NO_BLOCK are special constraints because
      // they are not associated with predictions or agents.
      if (constraint_type == ConstraintType::Constraint_Type_AVOID_REGION ||
          constraint_type == ConstraintType::Constraint_Type_STOP_POINT ||
          constraint_type == ConstraintType::Constraint_Type_NO_BLOCK) {
        // Candidate.collision_risk should be -1.0 as the risk model is not
        // applicable to these constraints.
        DCHECK(candidate.collision_risk == -1.0);
        return true;
      }
      // For speed constraints without a corresponding unique prediction id
      // (e.g. const-accel or const-speed, construction zone), it is
      // a special constraints.
      const std::string& constraint_pred_id = constraint.unique_pred_traj_id;
      if (constraint_pred_id.empty()) {
        // Candidate.collision_risk should be -1.0 as the risk model is not
        // applicable to these constraints.
        DCHECK(candidate.collision_risk == -1.0);
        return true;
      }
    }
  }
  return false;
}

const ConflictResolvingCandidate& GetHardestBrakeCandidate(
    const std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs) {
  const auto eb_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE;
      });
  if (eb_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **eb_candidate_iter;
  }
  const auto efs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE;
      });
  if (efs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **efs_candidate_iter;
  }
  const auto fs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE;
      });
  if (fs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **fs_candidate_iter;
  }
  const auto cs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE;
      });
  if (cs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **cs_candidate_iter;
  }
  const auto ss_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE;
      });
  DCHECK(ss_candidate_iter != low_risk_candidate_ptrs.end());
  return **ss_candidate_iter;
}

const ConflictResolvingCandidate& GetMildestReactCandidate(
    const std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs) {
  const auto cs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE;
      });
  if (cs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **cs_candidate_iter;
  }
  const auto fs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE;
      });
  if (fs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **fs_candidate_iter;
  }
  const auto efs_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE;
      });
  if (efs_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **efs_candidate_iter;
  }
  const auto eb_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE;
      });
  if (eb_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **eb_candidate_iter;
  }
  const auto ss_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE;
      });
  if (ss_candidate_iter != low_risk_candidate_ptrs.end()) {
    return **ss_candidate_iter;
  }
  const auto ms_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE;
      });
  DCHECK(ms_candidate_iter != low_risk_candidate_ptrs.end());
  return **ms_candidate_iter;
}

bool IsHardBrakeCandidate(const pb::ConflictResolvingType::Enum type) {
  return type == pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE ||
         type == pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE ||
         type == pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE;
}

// Returns true if the agent associated with an constraint is not significantly
// slower or braking hard.
bool AgentIsFasterAndAccelerating(
    const speed::pb::ObjectProximityInfo& object_proximity_info,
    const double ego_vel) {
  const double object_lon_vel =
      object_proximity_info.signed_longitudinal_speed();
  const double object_lon_acc =
      object_proximity_info.signed_longitudinal_acceleration();
  // Condition 1: agent is strictly faster, we consider a relaxed threshold
  // for recognizing not accelerating.
  if (object_lon_vel >= ego_vel &&
      object_lon_acc >= kRelaxedNotAcceleratingToleranceInMpss) {
    return true;
  }
  // Condition 2: agent is slightly slower (within the threshold), we consider
  // a stricter threshold for recognizing not accelerating.
  if (object_lon_vel + kNotSlowerToleranceInMps >= ego_vel &&
      object_lon_acc >= kNotAcceleratingToleranceInMpss) {
    return true;
  }
  return false;
}

bool AgentIsSignificantlySlowerOrDecelerating(
    const speed::pb::ObjectProximityInfo& object_proximity_info,
    const double ego_vel) {
  const double object_lon_vel =
      object_proximity_info.signed_longitudinal_speed();
  const double object_lon_acc =
      object_proximity_info.signed_longitudinal_acceleration();
  return object_lon_vel + kNotSlowerToleranceInMps < ego_vel ||
         object_lon_acc < kSignificantDecelerationInMpss;
}

// This function is used to determine whether an object that ego conflicts ahead
// of the plan init time is potentially high-risk.
bool IsPotentiallyHighRiskObject(
    const PlannerObject& planner_obj,
    const speed::pb::ObjectProximityInfo& proximity_info, const double ego_vel,
    const double ego_ra_to_fa, const bool is_allow_const_speed_obj) {
  // Pedestrians are always high-risk.
  if (planner_obj.is_pedestrian()) {
    return true;
  }
  // A cyclist is high-risk when 1) ego speed is high, or 2) reasoning does not
  // allow CS to it, or 3) the agent is fully ahead of ego front axis, and it is
  // slower or apparently decelerating.
  if (planner_obj.is_cyclist()) {
    if (!is_allow_const_speed_obj) {
      return true;
    }
    if ((proximity_info.projected_ra_arc_length().start() >= ego_ra_to_fa) &&
        AgentIsSignificantlySlowerOrDecelerating(proximity_info, ego_vel)) {
      return true;
    }
    return false;
  }
  // For non-VRUs, we just check whether it is faster and accelerating.
  return !AgentIsFasterAndAccelerating(proximity_info, ego_vel);
}

}  // namespace

ConflictResolvingCandidate ConflictResolver::Resolve(
    const DiscomfortVaryingLimits& all_limits,
    const PerDiscomfortSearchProblem& scr_search_problem,
    const AgentReactionCalculator& agent_reaction_calculator,
    const bool globally_allow_emergency_brake,
    pb::ConflictResolverDebug* debug) {
  TRACE_EVENT_SCOPE(planner, ConflictResolver_Resolve);
  std::vector<ConflictResolvingCandidate> profile_candidates;
  profile_candidates.reserve(kPotentialCandidateNum);
  // FULL_STOP //
  // If all discomfort search fails, one option is to elicit a
  // hard brake profile at discomfort 1.0 and report violated constraints.
  const std::vector<Constraint>& constraints = scr_search_problem.constraints;
  Discomforts all_discomforts;
  const int max_discomfort_ix = all_discomforts.discomfort_ixs.back();
  // TODO(tienan): we should only use sampled_ref_profile hereafter.
  const Profile& ref_profile = scr_search_problem.ref_profile;
  const Profile& sampled_ref_profile = scr_search_problem.sampled_ref_profile;
  const std::vector<Decision>& initial_decisions =
      scr_search_problem.per_discomfort_initial_decisions;
  const TimeMap& max_discomfort_time_map = scr_search_problem.time_map;
  const Limits& max_discomfort_limits =
      all_limits.LimitsAtDiscomfortIx(max_discomfort_ix);
  DCHECK_EQ(ref_profile.size(), num_steps_);
  const State& profile_init_state = ref_profile[0];
  profile_candidates.push_back(GenerateImmediateBrakingCandidate(
      profile_init_state, max_discomfort_limits, constraints, initial_decisions,
      max_discomfort_time_map,
      pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE));
  const ConflictResolvingCandidate& full_stop_candidate =
      profile_candidates.back();
  DCHECK_EQ(full_stop_candidate.conflict_resolving_type,
            pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE);
  if (!should_invoke_scr_) {
    if (debug != nullptr) {
      debug->set_early_return_without_calculation(true);
      absl::StrAppend(debug->mutable_st_eval_debug_str(),
                      "Do not invoke SCR evaluation.");
    }
    return profile_candidates.back();
  }

  // EMERGENCY_STOP or EXTRA_FULL_STOP//
  // If any of the violated constraints in full-stop allow braking more than the
  // full-stop decel, we further elicit an emergency-stop or extra-full-stop
  // profile.
  // If the allow max braking is EFS/EB, we will first search EFS/EB, and if no
  // conflict, search a less braking profile if possible.
  // TODO(tienan): clean this logic up to make it more readable.
  int allow_eb_cix = -1;
  int unused = -1;
  bool allow_eb = false;
  if (FLAGS_planning_enable_emergency_brake && globally_allow_emergency_brake &&
      AllowBrakeMoreThanFullStop(
          constraints, full_stop_candidate.constraint_decisions,
          full_stop_candidate.violated_constraint_ixs, unused)) {
    allow_eb = AllowEmergencyBrake(
        constraints, full_stop_candidate.constraint_decisions,
        full_stop_candidate.violated_constraint_ixs, allow_eb_cix);
    // Generate an emergency brake profile.
    Limits emergency_brake_limits = max_discomfort_limits;
    emergency_brake_limits.brake_a.min =
        allow_eb ? scr_config_.emergency_brake_min_accel()
                 : scr_config_.extra_full_stop_min_accel();
    emergency_brake_limits.brake_j.min =
        allow_eb ? scr_config_.emergency_brake_min_jerk()
                 : scr_config_.extra_full_stop_min_jerk();
    emergency_brake_limits.accel_j.min =
        allow_eb ? scr_config_.emergency_brake_min_jerk()
                 : scr_config_.extra_full_stop_min_jerk();
    // TODO(tienan): EB can also use FS final decisions as the initial decisions
    // to save computation upon finding conflicts. Update this in a seperate CR.
    ConflictResolvingCandidate eb_candidate = GenerateImmediateBrakingCandidate(
        profile_init_state, emergency_brake_limits, constraints,
        initial_decisions, max_discomfort_time_map,
        allow_eb ? pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE
                 : pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE);
    // If EB can already fully avoid the conflicts. We try to generate an EFS
    // candidate that brakes in between of FS and EB using binary search. If
    // a EFS profile can also fully avoid the conflicts, we consider it as
    // the candidate instead of EB.
    // TODO(tienan): change FLAGS_planning_enable_extra_full_stop naming to
    // FLAGS_planning_enable_milder_brake_than_extreme
    if (FLAGS_planning_enable_extra_full_stop &&
        eb_candidate.violated_constraint_ixs.empty()) {
      // EFS uses the FS final decisions as the initial decisions, which
      // should reduce the computation time in FindViolatedConstraints() as FS
      // and EFS should have majorly the same decisions.
      std::optional<ConflictResolvingCandidate> feasible_efs_candidate =
          FindFeasibleExtraFullStopCandidate(
              profile_init_state,
              /*initial_decisions=*/full_stop_candidate.constraint_decisions,
              constraints, max_discomfort_limits, max_discomfort_time_map);
      profile_candidates.push_back(
          feasible_efs_candidate.has_value()
              ? std::move(feasible_efs_candidate.value())
              : std::move(eb_candidate));
    } else {
      profile_candidates.push_back(std::move(eb_candidate));
    }
    // Update the dominant constraint to the one that allow EB or that one that
    // allow EFS only.
    if (allow_eb) {
      DCHECK_GE(allow_eb_cix, 0);
      profile_candidates.back().UpdateDominantConstraintCix(allow_eb_cix);
    } else {
      int allow_efs_cix = -1;
      DCHECK(AllowExtraFullStop(
          constraints, full_stop_candidate.constraint_decisions,
          full_stop_candidate.violated_constraint_ixs, allow_efs_cix));
      DCHECK_GE(allow_efs_cix, 0);
      profile_candidates.back().UpdateDominantConstraintCix(allow_efs_cix);
    }
  }

  // MAX_SPEED//
  // Different from the above, this is a candidate that aims to aggressively
  // pass to mitigate the risk. It is the reference profile at max discomfort,
  // aka. the solver passing capability under regular discomfort levels.
  if (FLAGS_planning_enable_max_speed_for_conflict_resolver) {
    Profile max_speed_profile = ref_profile;
    ConflictResolvingCandidate ms_candidate =
        ConstructScrCandidate(pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE,
                              std::move(max_speed_profile), constraints,
                              initial_decisions, max_discomfort_time_map);
    // Either all conflicted constraints on the FS profile (handle the case when
    // MS profile conflicts with leader), or the ones on the MS profile (handle
    // the case when FS profile conflicts many constraints but MS profile
    // conflicts with none) allow max_speed, we add the MS canddiate.
    // TODO(tienan): consider removing the check on FS candidate once we have
    // SCR greedy search (in which case we should find a candidate that do not
    // conflicts with a leader).
    if (AllowMaxSpeed(constraints,
                      full_stop_candidate.violated_constraint_ixs) ||
        AllowMaxSpeed(constraints, ms_candidate.violated_constraint_ixs)) {
      ms_candidate.UpdateDominantConstraintCix(
          full_stop_candidate.dominant_constraint_ix);
      profile_candidates.push_back(std::move(ms_candidate));
    }
  }

  // CONST_SPEED //
  // This is a candidate to maintain a passive driving behavior (not significant
  // accelerating nor decelerating) towards aggressive interacting agents that
  // has a close-proximity to ego (e.g. overtaking cyclist). We expect the
  // agents are responsible to keep safe from ego in some of these circumstances
  // thus we just maintain the current speed to avoid over-reacting.
  constexpr double kConstSpeedActivationThresholdInMps = 5.0;
  const double ego_current_speed = sampled_ref_profile.front().v;
  if (FLAGS_planning_enable_const_speed_for_conflict_resolver &&
      ego_current_speed <= kConstSpeedActivationThresholdInMps) {
    Profile const_speed_profile;
    const_speed_profile.reserve(num_steps_);
    const_speed_profile.push_back(sampled_ref_profile.front());
    GenerateProfileToExpectedSpeed(
        sampled_ref_profile, /*modified_first_ix=*/0,
        /*modified_end_ix=*/num_steps_ - 1, all_limits.ComfortLimits(), dt_,
        /*expected_v=*/const_speed_profile.front().v, &const_speed_profile);
    DCHECK_EQ(const_speed_profile.size(), num_steps_);
    ConflictResolvingCandidate cs_candidate = ConstructScrCandidate(
        pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE,
        std::move(const_speed_profile), constraints, initial_decisions,
        max_discomfort_time_map);

    if (AllowConstSpeed(constraints, cs_candidate.violated_constraint_ixs,
                        planner_object_map_)) {
      cs_candidate.UpdateDominantConstraintCix(
          full_stop_candidate.dominant_constraint_ix);
      profile_candidates.push_back(std::move(cs_candidate));
    }
  }

  if (FLAGS_planning_enable_scr_search_for_conflict_resolver) {
    SearchSolution scr_search_solution(
        TimeMap(immutable_time_, dt_, num_steps_));
    profile_searcher_util_.PrepareSearchSolutionAtDiscomfort(
        /*tree_search_type=*/pb::TreeSearchType_Enum_kNone,
        scr_search_problem.discomfort_ix, scr_search_problem.discomfort,
        scr_search_problem.per_discomfort_initial_decisions,
        /*per_discomfort_risk_initial_decisions=*/std::nullopt,
        scr_search_problem.ref_profile, scr_search_problem.time_map,
        scr_search_solution);
    // Iteratively ignore the dominant constraint when a regular search fails
    // until a conflict-free profile is found.
    // TODO(shijie): Consider improving the code efficiency by recording the
    // violated_cixs and decisions during searching and using them for
    // constructing SCR candidate.
    bool allow_ss = true;
    while (!profile_searcher_util_.AdjustForAllConflict(
        scr_search_problem, immutable_time_,
        /*intermediate_result_debug=*/nullptr, scr_search_solution)) {
      const int dominant_cix = scr_search_solution.earliest_brake_cix;
      // DCHECK_GE(dominant_cix, 0);
      if (dominant_cix < 0) {
        allow_ss = false;
        if (debug != nullptr) {
          absl::StrAppend(debug->mutable_st_eval_debug_str(),
                          "GEN SS FAILED. DOMINANT_CIX < 0.");
        }
        // This should not happen. TODO(shijie): Remove this once the
        // dominant_cix < 0 issue is fixed.
        DCHECK(false);
        rt_event::PostRtEvent<
            rt_event::planner::ScrSearchFailureDueToDominantCix>();
        break;
      }
      if (!constraints[dominant_cix].settings.allow_max_speed) {
        allow_ss = false;
        if (debug != nullptr) {
          absl::StrAppend(
              debug->mutable_st_eval_debug_str(), "No SS as constraint ",
              constraints[dominant_cix].obj_id, " doesn't allow MS. ");
        }
        break;
      }
      scr_search_solution.decisions[dominant_cix] = Decision::IGNORE;
      scr_search_solution.profile = ref_profile;
    }
    if (allow_ss) {
      ConflictResolvingCandidate ss_candidate = ConstructScrCandidate(
          pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE,
          std::move(scr_search_solution.profile), constraints,
          initial_decisions, max_discomfort_time_map);
      // If the SS is not identical to the MS or no MS candidate, add SS to SCR
      // candidates.
      // TODO(shijie): Consider checking for equality by earlist_brake_time_ix
      // in search solution.
      const auto ms_candidate_iter = std::find_if(
          profile_candidates.begin(), profile_candidates.end(),
          [](const ConflictResolvingCandidate& candidate) {
            return candidate.conflict_resolving_type ==
                   pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE;
          });
      if (ms_candidate_iter == profile_candidates.end() ||
          ms_candidate_iter->profile != ss_candidate.profile) {
        profile_candidates.push_back(std::move(ss_candidate));
      } else {
        if (debug != nullptr) {
          absl::StrAppend(debug->mutable_st_eval_debug_str(),
                          "SS is identical to MS. ");
        }
      }
    }
  }

  // TODO(tienan): add the EXTRA_FAST candidate.

  // Select the best one among the profile candidates.
  return SelectConflictResolvingProfile(agent_reaction_calculator, constraints,
                                        std::move(profile_candidates), debug);
}

std::optional<ConflictResolvingCandidate>
ConflictResolver::FindFeasibleExtraFullStopCandidate(
    const State& profile_init_state,
    const std::vector<Decision>& initial_decisions,
    const std::vector<Constraint>& constraints,
    const Limits& max_discomfort_limits,
    const TimeMap& max_discomfort_time_map) const {
  Limits efs_limits = max_discomfort_limits;
  efs_limits.brake_j.min = scr_config_.extra_full_stop_min_jerk();
  efs_limits.accel_j.min = scr_config_.extra_full_stop_min_jerk();
  double lower_bound_a = scr_config_.extra_full_stop_min_accel();
  double upper_bound_a = max_discomfort_limits.accel_a.min;
  DCHECK_LT(lower_bound_a, upper_bound_a);
  efs_limits.brake_a.min = lower_bound_a;
  ConflictResolvingCandidate efs_candidate = GenerateImmediateBrakingCandidate(
      profile_init_state, efs_limits, constraints, initial_decisions,
      max_discomfort_time_map,
      pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE);
  // Early return if even the minimum accel we can use for EFS cannot
  // avoid conflicts.
  if (!efs_candidate.violated_constraint_ixs.empty()) {
    return std::nullopt;
  }
  std::optional<ConflictResolvingCandidate> feasible_efs_candidate =
      std::make_optional(std::move(efs_candidate));
  while (lower_bound_a + kExtraFullStopAccelResolutionInMpss < upper_bound_a) {
    const double mid_a = (lower_bound_a + upper_bound_a) * 0.5;
    efs_limits.brake_a.min = mid_a;
    ConflictResolvingCandidate efs_candidate =
        GenerateImmediateBrakingCandidate(
            profile_init_state, efs_limits, constraints, initial_decisions,
            max_discomfort_time_map,
            pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE);
    if (efs_candidate.violated_constraint_ixs.empty()) {
      // Has no conflict, try using milder deceleration.
      lower_bound_a = mid_a;
      feasible_efs_candidate.emplace(std::move(efs_candidate));
    } else {
      // Has conflicts, try using strong deceleration.
      upper_bound_a = mid_a;
    }
  }
  return feasible_efs_candidate;
}

ConflictResolvingCandidate ConflictResolver::GenerateImmediateBrakingCandidate(
    const State& profile_init_state, const Limits& limits,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& initial_decisions,
    const TimeMap& max_discomfort_time_map,
    const pb::ConflictResolvingType::Enum conflict_resolver_type) const {
  Profile brake_profile;
  brake_profile.reserve(num_steps_);
  brake_profile.push_back(profile_init_state);
  GenerateBrakeProfile(/*brake_first_ix=*/0, /*brake_end_ix=*/num_steps_ - 1,
                       limits, dt_, &brake_profile);
  return ConstructScrCandidate(conflict_resolver_type, std::move(brake_profile),
                               constraints, initial_decisions,
                               max_discomfort_time_map);
}

ConflictResolvingCandidate ConflictResolver::ConstructScrCandidate(
    pb::ConflictResolvingType::Enum conflict_resolver_type, Profile profile,
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& initial_decisions,
    const TimeMap& max_discomfort_time_map) const {
  std::vector<Decision> decisions = initial_decisions;
  int dominant_constraint_ix = -1;
  absl::flat_hash_map<int, GapAlignInfo> unused_gap_align_info_map;
  std::vector<int> violated_constraint_ixs = FindViolatedConstraints(
      profile, constraints, initial_decisions, max_discomfort_time_map,
      unused_gap_align_info_map, Discomforts::kMax, conflict_resolver_type,
      &decisions, &dominant_constraint_ix);
  return ConflictResolvingCandidate(std::move(profile), std::move(decisions),
                                    std::move(violated_constraint_ixs),
                                    dominant_constraint_ix,
                                    conflict_resolver_type);
}

std::vector<int> ConflictResolver::FindViolatedConstraints(
    const Profile& profile, const std::vector<Constraint>& constraints,
    const std::vector<Decision>& initial_decisions,
    const TimeMap& max_discomfort_time_map,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    const double discomfort,
    const pb::ConflictResolvingType_Enum conflict_resolving_type,
    std::vector<Decision>* decisions, int* dominant_constraint_ix) const {
  DCHECK(decisions != nullptr);
  DCHECK(dominant_constraint_ix != nullptr);
  DCHECK_EQ(decisions->size(), constraints.size());
  DCHECK_EQ(profile.size(), num_steps_);
  *dominant_constraint_ix = -1;
  std::vector<int> violated_constraint_ixs;
  violated_constraint_ixs.reserve(constraints.size());
  std::vector<int> non_hard_violated_constraint_ixs;
  non_hard_violated_constraint_ixs.reserve(constraints.size());
  // Generate time-map at max discomfort.
  int search_for_conflict_start_ix = 0;
  Conflict conflict;
  // Update the decisions for all of the NO_BLOCK constraints. This is
  // necessary for FindFirstConflictForProfile() to find the NO_BLOCK
  // constraints with yield decision and with conflict.
  profile_searcher_util_.UpdateNoBlockDecisions(
      constraints, profile, /*adjust_for_tree_branch=*/false,
      max_discomfort_time_map, discomfort, decisions,
      /*updated_decision_indices=*/nullptr,
      /*intermediate_result_debug=*/nullptr);

  // Iterate over the profile to find all violated constraint, aka, the
  // constraints that have conflicts with the profile.
  // NOTE(Tingran): In conflict resolver, it will be non gap align search
  // problem for regular search, and may be gap align search problem for inverse
  // search. Currently we assume that there will not be gap align tree search.
  // RM constraints should be treated as normal constraints in SCR, as we can
  // not elevate discomfort since we are already at max. Therefore, we set tree
  // search type to TreeSearchType_Enum_kNone, and set adjust_for_tree_branch to
  // false, and tree_switch_time_ix to n_steps.
  // TODO(Tienan): In future work, we can possibly try RM with EB, but that is a
  // stretched plan that we need to design in v1.
  while (FindFirstConflictForProfile(
      /*tree_search_type=*/pb::TreeSearchType_Enum_kNone,
      max_discomfort_time_map, profile, constraints, *decisions,
      search_for_conflict_start_ix, gap_align_info_map,
      /*tree_switch_time_ix=*/max_discomfort_time_map.steps(),
      /*adjust_for_tree_branch=*/false, &conflict)) {
    const int c_ix = conflict.constraint_ix;
    DCHECK_LE(0, c_ix);
    DCHECK_LT(c_ix, constraints.size());
    DCHECK((*decisions)[c_ix] != Decision::IGNORE);
    if ((*decisions)[c_ix] != Decision::YIELD) {
      // Decision is pass or not_decided or ignore. We adjust it to ignore or
      // yield, respectively.
      const bool success =
          SetNoPassDecision(constraints, c_ix, discomfort, decisions,
                            /*updated_decision_indices=*/nullptr);
      if (success) {
        // If the decision update succeeds, search from beginning and continue.
        // TODO(tienan/tingran): revisit here. Do we need to start over from ix
        // 0?
        search_for_conflict_start_ix = 0;
        continue;
      }
    }
    // There are two reasons to reach here:
    // 1. Decision is yield and we cannot adjust any more.
    // 2. We attempted to update a must-pass constraint to ignore decision
    // because we can not pass it, but fail to do so.
    // In both cases, mandatorily set decision to IGNORE as we can not satisfy
    // this constraint, then keep searching for other violated constraints.
    const PassYieldOption pass_yield_option =
        ((*decisions)[c_ix] == pb::PASS)
            ? constraints[c_ix].settings.pass_option
            : constraints[c_ix].settings.yield_option;
    // Only consider it to be a violated constraint if it is hard.
    if (pass_yield_option ==
        PassYieldOption::ConstraintSettings_PassYieldOption_HARD) {
      violated_constraint_ixs.push_back(c_ix);
    } else {
      non_hard_violated_constraint_ixs.push_back(c_ix);
    }
    (*decisions)[c_ix] = Decision::IGNORE;
    search_for_conflict_start_ix = conflict.time_ix;
    // Accommodate no-block constraints.
    // TODO(tienan/tingran): revisit here. Do we 1) need to handle and 2) have
    // correctly handled no-block in conflict resolver?
    const bool any_no_block_decision_updated =
        profile_searcher_util_.UpdateNoBlockDecisions(
            constraints, profile, /*adjust_for_tree_branch=*/false,
            max_discomfort_time_map, discomfort, decisions,
            /*updated_decision_indices=*/nullptr,
            /*intermediate_result_debug=*/nullptr);
    if (any_no_block_decision_updated) {
      // We made a new decision for a no-block constraint, so we need to
      // start from beginning to re-adjust the profile to avoid the new yield
      // conflicts.
      search_for_conflict_start_ix = 0;
    }
  }

  // ResolveConflict all undecided decision to PASS.
  for (pb::SpeedDecision& decision : *decisions) {
    if (decision == Decision::NOT_DECIDED) {
      decision = Decision::PASS;
    }
  }

  UpdateViolatedConstraintDecisions(max_discomfort_time_map, constraints,
                                    initial_decisions, violated_constraint_ixs,
                                    non_hard_violated_constraint_ixs,
                                    conflict_resolving_type, *decisions);

  // Update the dominant constraint index.
  for (const int c_ix : violated_constraint_ixs) {
    if (decisions->at(c_ix) == Decision::YIELD) {
      *dominant_constraint_ix = c_ix;
      break;
    }
  }

  return violated_constraint_ixs;
}

ConflictResolvingCandidate ConflictResolver::SelectConflictResolvingProfile(
    const AgentReactionCalculator& agent_reaction_calculator,
    const std::vector<Constraint>& constraints,
    std::vector<ConflictResolvingCandidate>&& profile_candidates,
    pb::ConflictResolverDebug* debug) {
  DCHECK_GT(profile_candidates.size(), 0);
  if (debug != nullptr) {
    debug->set_previous_result(previous_scr_result_);
  }
  // Early return if there is only one candidate.
  if (profile_candidates.size() == 1) {
    // Note, we do not early return except for FS to make sure we can always
    // go through additional heuristic logics for riskier signal (EB/MS).
    DCHECK_EQ(profile_candidates[0].conflict_resolving_type,
              pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE);
    if (debug != nullptr) {
      debug->set_early_return_without_calculation(true);
      absl::StrAppend(debug->mutable_st_eval_debug_str(), "FS candidate only.");
      for (const ConflictResolvingCandidate& candidate : profile_candidates) {
        *debug->add_candidates() = candidate.ToProto();
      }
    }
    return profile_candidates[0];
  }
  // Risk model evaluation.
  std::vector<ConflictResolvingCandidate*> min_risk_candidate_ptrs =
      GetMinRiskConflictResolvingCandidatesUsingRiskModel(
          agent_reaction_calculator, constraints, profile_candidates, debug);
  DCHECK(!min_risk_candidate_ptrs.empty());
  if (debug != nullptr) {
    std::string min_risk_candidates_str = "Min risk candidates: ";
    for (const auto* candidate : min_risk_candidate_ptrs) {
      switch (candidate->conflict_resolving_type) {
        case pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "FS ");
          break;
        case pb::ConflictResolvingType_Enum_EMERGENCY_STOP_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "EB ");
          break;
        case pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "MS ");
          break;
        case pb::ConflictResolvingType_Enum_EXTRA_FULL_STOP_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "EFS ");
          break;
        case pb::ConflictResolvingType_Enum_ML_SPEED_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "ML ");
          break;
        case pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "CS ");
          break;
        case pb::ConflictResolvingType_Enum_SCR_SEARCH_PROFILE:
          absl::StrAppend(&min_risk_candidates_str, "SS ");
          break;
        default:
          absl::StrAppend(&min_risk_candidates_str, "NA ");
          break;
      }
    }
    absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                    min_risk_candidates_str);
  }
  // Early return if there is only one min-risk candidate.
  if (min_risk_candidate_ptrs.size() == 1) {
    absl::StrAppend(debug->mutable_st_eval_debug_str(),
                    "Only one min-risk candidate. Skip st eval.");
    // Populate the candidate debug.
    if (debug != nullptr) {
      for (const ConflictResolvingCandidate& candidate : profile_candidates) {
        *debug->add_candidates() = candidate.ToProto();
      }
    }
    return *(min_risk_candidate_ptrs[0]);
  }
  // Use s-t evaluation for tie-breaking if there are multiple
  // low risk candidates, or if the risk model is not applicable.
  const ConflictResolvingCandidate& selected_candidate =
      SelectConflictResolvingProfileUsingStEvaluation(
          agent_reaction_calculator, constraints, min_risk_candidate_ptrs,
          debug);
  // Populate the candidate debug.
  if (debug != nullptr) {
    for (const ConflictResolvingCandidate& candidate : profile_candidates) {
      *debug->add_candidates() = candidate.ToProto();
    }
  }
  return selected_candidate;
}

void ConflictResolver::ConstructEgoPerturbFunctionsCache(
    pb::ConflictResolverDebug* debug) {
  const double original_prediction_weight =
      scr_config_.original_prediction_weight_in_perturb();
  const double perturb_std_factor =
      std::sqrt(kPerturbDimension / (1.0 - original_prediction_weight));
  // Ego perturb function is based on a static error model and ego current
  // error. Only one direction will be perturbed to the max of {error model
  // margin, current error} which is the one that the current error is larger
  // than the error model margin.  Note here we treat the current error as one
  // SD for the perturbation (equivalent of estimating the distribution with
  // only one data point).
  const std::optional<PlanInitErrorState>& error_state =
      DCHECK_NOTNULL(risk_model_input_)->plan_init_state.error_state;

  // Conduct lateral (kLeft and kRight) perturbation.
  const double cur_lat_error =
      error_state.has_value() ? error_state->lateral_error_m : 0.0;
  double lat_sd = position_estimate_error::kControlErrorSd;
  // Positive lateral error means plan init state is on the right of the actual
  // pose. Thus we should perturb to the left.
  if (math::UpdateMax(cur_lat_error, lat_sd)) {
    absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                    "Use real-time error in ego-perturb-left function.\n");
  }
  const double lat_left_perturb = lat_sd * perturb_std_factor;
  auto ego_perturb_left =
      [lat_left_perturb](const planner::pb::TrajectoryPose& ego_pose) {
        return Perturb(ego_pose, lat_left_perturb, 0.5 * M_PI);
      };
  // Agent perturbs right corresponds to that ego perturbs left.
  agent_perturb_type_to_ego_perturb_func_
      [planner::pb::PerturbType_Enum_kRight] = std::move(ego_perturb_left);
  lat_sd = position_estimate_error::kControlErrorSd;
  // Negative lateral error means we should perturb to right more.
  if (math::UpdateMax(-cur_lat_error, lat_sd)) {
    absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                    "Use real-time error in ego-perturb-right function.\n");
  }
  const double lat_right_perturb = lat_sd * perturb_std_factor;
  auto ego_perturb_right =
      [lat_right_perturb](const planner::pb::TrajectoryPose& ego_pose) {
        return Perturb(ego_pose, lat_right_perturb, -0.5 * M_PI);
      };
  // Agent perturbs left corresponds to that ego perturbs right.
  agent_perturb_type_to_ego_perturb_func_[planner::pb::PerturbType_Enum_kLeft] =
      std::move(ego_perturb_right);

  // Conduct longitudinal (kAhead and kBehind) perturbation.
  const double cur_lon_error =
      error_state.has_value() ? error_state->longitudinal_error_m : 0.0;
  double long_sd = position_estimate_error::kControlErrorSd;
  // Positive longitudinal error means plan init state is ahead the actual pose.
  // Thus we should perturb behind.
  if (math::UpdateMax(cur_lon_error, long_sd)) {
    absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                    "Use real-time error in ego-perturb-behind function.\n");
  }
  const double long_behind_perturb = long_sd * perturb_std_factor;
  auto ego_perturb_behind =
      [long_behind_perturb](const planner::pb::TrajectoryPose& ego_pose) {
        return Perturb(ego_pose, long_behind_perturb, M_PI);
      };
  // Agent perturbs ahead corresponds to that ego perturbs behind.
  agent_perturb_type_to_ego_perturb_func_
      [planner::pb::PerturbType_Enum_kAhead] = std::move(ego_perturb_behind);
  long_sd = position_estimate_error::kControlErrorSd;
  // Negative longitudinal error means we should perturb ahead.
  if (math::UpdateMax(-cur_lon_error, long_sd)) {
    absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                    "Use real-time error in ego-perturb-ahead function.\n");
  }
  const double long_ahead_perturb = long_sd * perturb_std_factor;
  auto ego_perturb_ahead =
      [long_ahead_perturb](const planner::pb::TrajectoryPose& ego_pose) {
        return Perturb(ego_pose, long_ahead_perturb, 0.0);
      };
  // Agent perturbs behind corresponds to that ego perturbs ahead.
  agent_perturb_type_to_ego_perturb_func_
      [planner::pb::PerturbType_Enum_kBehind] = std::move(ego_perturb_ahead);
}

std::vector<ScrPerturbTypeAndFunctions>
ConflictResolver::GetRiskPerturbFunctions(
    const bool should_perturb, const bool agent_is_large_vehicle,
    const PredictedTrajectoryWrapper& predicted_traj) const {
  static const auto original = [](const planner::pb::TrajectoryPose& pose) {
    return pose;
  };
  static const std::vector<ScrPerturbTypeAndFunctions> original_pose_functions(
      {{planner::pb::PerturbType_Enum_kOriginal, original, original}});

  if (!should_perturb ||
      !HasTrajectoryPoseUncertainty(predicted_traj.poses())) {
    return original_pose_functions;
  }

  static const double original_prediction_weight =
      scr_config_.original_prediction_weight_in_perturb();
  static const double perturb_std_factor =
      std::sqrt(kPerturbDimension / (1.0 - original_prediction_weight));
  // We consider the prediction uncertainty, tracking error, and ego control
  // error in the perturb functions as of now.
  // TODO(tienan): add ego localization error into the perturb functions as well
  // as in the speed min range.

  // Perturbations for agents.
  const double lat_tracking_error =
      agent_is_large_vehicle
          ? position_estimate_error::kTrackingLateralErrorSdForLargeVeh
          : position_estimate_error::kTrackingLateralErrorSdForSmallVeh;
  const double lat_tracking_var = math::Sqr(lat_tracking_error);
  auto agent_perturb_left =
      [lat_tracking_var](const planner::pb::TrajectoryPose& agent_pose) {
        const double lat_sd = std::sqrt(
            math::Sqr(agent_pose.uncertainty().lat_sd()) + lat_tracking_var);
        return Perturb(agent_pose, lat_sd * perturb_std_factor, 0.5 * M_PI);
      };
  auto agent_perturb_right =
      [lat_tracking_var](const planner::pb::TrajectoryPose& agent_pose) {
        const double lat_sd = std::sqrt(
            math::Sqr(agent_pose.uncertainty().lat_sd()) + lat_tracking_var);
        return Perturb(agent_pose, lat_sd * perturb_std_factor, -0.5 * M_PI);
      };
  const double long_tracking_error =
      position_estimate_error::kLongErrorSdToLatErrorSdRatio *
      lat_tracking_error;
  const double long_tracking_var = math::Sqr(long_tracking_error);
  auto agent_perturb_ahead =
      [long_tracking_var](const planner::pb::TrajectoryPose& agent_pose) {
        const double long_sd = std::sqrt(
            math::Sqr(agent_pose.uncertainty().long_sd()) + long_tracking_var);
        return Perturb(agent_pose, long_sd * perturb_std_factor, 0.0);
      };
  auto agent_perturb_behind =
      [long_tracking_var](const planner::pb::TrajectoryPose& agent_pose) {
        const double long_sd = std::sqrt(
            math::Sqr(agent_pose.uncertainty().long_sd()) + long_tracking_var);
        return Perturb(agent_pose, long_sd * perturb_std_factor, M_PI);
      };

  // We consider the worse case combination of perturbation (i.e. agent and ego
  // are perturbed towards each other) in the perturb functions.
  std::vector<ScrPerturbTypeAndFunctions> perturb_functions(
      {{planner::pb::PerturbType_Enum_kOriginal, original, original},
       {planner::pb::PerturbType_Enum_kLeft,
        agent_perturb_type_to_ego_perturb_func_.at(
            planner::pb::PerturbType_Enum_kLeft),
        std::move(agent_perturb_left)},
       {planner::pb::PerturbType_Enum_kRight,
        agent_perturb_type_to_ego_perturb_func_.at(
            planner::pb::PerturbType_Enum_kRight),
        std::move(agent_perturb_right)},
       {planner::pb::PerturbType_Enum_kAhead,
        agent_perturb_type_to_ego_perturb_func_.at(
            planner::pb::PerturbType_Enum_kAhead),
        std::move(agent_perturb_ahead)},
       {planner::pb::PerturbType_Enum_kBehind,
        agent_perturb_type_to_ego_perturb_func_.at(
            planner::pb::PerturbType_Enum_kBehind),
        std::move(agent_perturb_behind)}});
  return perturb_functions;
}

double ConflictResolver::GetPerturbTrajWeight(
    const bool has_perturb,
    const planner::pb::PerturbType_Enum perturb_type) const {
  static const double original_prediction_weight =
      scr_config_.original_prediction_weight_in_perturb();
  static const double perturb_prediction_weight =
      (1.0 - original_prediction_weight) * 0.5 / kPerturbDimension;
  return has_perturb ? (perturb_type == planner::pb::PerturbType_Enum_kOriginal
                            ? original_prediction_weight
                            : perturb_prediction_weight)
                     : 1.0;
}

std::vector<ConflictResolvingCandidate*>
ConflictResolver::GetMinRiskConflictResolvingCandidatesUsingRiskModel(
    const AgentReactionCalculator& agent_reaction_calculator,
    const std::vector<Constraint>& constraints,
    std::vector<ConflictResolvingCandidate>& profile_candidates,
    pb::ConflictResolverDebug* debug) {
  std::vector<ConflictResolvingCandidate*> min_risk_candidates;
  min_risk_candidates.reserve(profile_candidates.size());
  // Skip risk model evaluation if the SCR does not have a risk model input.
  if (risk_model_input_ == nullptr) {
    if (debug != nullptr) {
      absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                      "Risk model disabled.");
    }
    for (ConflictResolvingCandidate& candidate : profile_candidates) {
      min_risk_candidates.push_back(&candidate);
    }
    return min_risk_candidates;
  }
  // Skip risk model evaluation if any candidates conflicting
  // special constraints (aka. the constraints without a corresponding
  // prediction), in which case we can not adopt to the risk
  // model APIs.
  if (AnyCandidateConflictingSpecialConstraints(profile_candidates,
                                                constraints)) {
    if (debug != nullptr) {
      absl::StrAppend(debug->mutable_risk_eval_debug_str(),
                      "Skip risk model due to special constraints existence.");
    }
    for (ConflictResolvingCandidate& candidate : profile_candidates) {
      min_risk_candidates.push_back(&candidate);
    }
    return min_risk_candidates;
  }
  ConstructEgoPerturbFunctionsCache(debug);
  // Calculate the risks and push back the min-risk candidates.
  double min_risk = std::numeric_limits<double>::max();
  std::vector<double> risk_for_candidates;
  risk_for_candidates.reserve(profile_candidates.size());
  for (ConflictResolvingCandidate& candidate : profile_candidates) {
    const double single_candidate_risk =
        GetCollisionRiskForSingleCandidateUsingRiskModel(
            agent_reaction_calculator, constraints, candidate);
    // If the severity of the current candidate is smaller than
    // the min_severity among all candidates, update the min_severity
    // and clear the min risk candidate vec.
    math::UpdateMin(single_candidate_risk, min_risk);
    risk_for_candidates.push_back(single_candidate_risk);
  }
  // TODO(tienan): we should also consider some hysteresis here
  // after the risk estimation being more continuous.
  DCHECK_EQ(risk_for_candidates.size(), profile_candidates.size());
  const double risk_diff_absolute_tolerance =
      scr_config_.risk_diff_absolute_tolerance();
  for (unsigned i = 0; i < risk_for_candidates.size(); ++i) {
    ConflictResolvingCandidate& candidate = profile_candidates[i];
    if (ShouldTreatAsLowRiskForSpecialCandidate(candidate)) {
      min_risk_candidates.push_back(&candidate);
      continue;
    }
    // If the risk of the current candidate is not significantly different
    // from the min_risk (considering some tolerances), push the candidate
    // into the min risk candidate vec.
    const double single_candidate_risk = risk_for_candidates[i];
    DCHECK_GE(single_candidate_risk, min_risk);
    if (single_candidate_risk - min_risk <= risk_diff_absolute_tolerance) {
      min_risk_candidates.push_back(&candidate);
    }
  }
  return min_risk_candidates;
}

double ConflictResolver::GetCollisionRiskForSingleCandidateUsingRiskModel(
    const AgentReactionCalculator& agent_reaction_calculator,
    const std::vector<Constraint>& constraints,
    ConflictResolvingCandidate& candidate) const {
  TRACE_EVENT_SCOPE(
      planner,
      ConflictResolver_GetCollisionRiskForSingleCandidateUsingRiskModel);
  // Construct ego geo-center trajectory from the candidate profile.
  // TODO(tienan): double check if the risk model here supporting ego
  // backward maneuver.
  ::planner::pb::Trajectory ego_trajectory;
  CombinePathAndSpeedProfile(
      risk_model_input_->plan_init_state, risk_model_input_->vehicle_model,
      TurnSignalDirective(), /*honk_request=*/false,
      /*hazard_light_request=*/false,
      /*suppression_status=*/false, risk_model_input_->path_with_juke,
      candidate.profile, risk_model_input_->prediction_timestamp,
      /*motion_mode=*/planner::pb::MotionMode::FORWARD,
      /*has_ego_entered_mrc=*/false, &ego_trajectory);
  ConvertWheelBaseToGeoCenterTrajectory(risk_model_input_->ego_shape,
                                        ego_trajectory);
  const PredictedTrajectoryInterpolator ego_traj_interpolator(ego_trajectory);
  // Calculate the risk for each conflicting constraint and update the max
  // risk for this candidate.
  double max_risk_for_the_candidate = 0.0;
  for (const int c_ix : candidate.violated_constraint_ixs) {
    const Constraint& constraint = constraints[c_ix];
    Decision& decision = candidate.constraint_decisions[c_ix];
    const PlannerObject& planner_obj =
        agent_reaction_calculator.GetPlannerObject(constraint.obj_id);
    // Determine the decision based on the one that leads to smaller risk.
    if (decision == Decision::NOT_DECIDED) {
      std::vector<PerturbTypeAndCollisionInfo> pass_infos =
          CalculateCollisionInfosForOneConstraintAndDecision(
              agent_reaction_calculator, ego_traj_interpolator, planner_obj,
              constraint, /*is_pass=*/true);
      std::vector<PerturbTypeAndCollisionInfo> yield_infos =
          CalculateCollisionInfosForOneConstraintAndDecision(
              agent_reaction_calculator, ego_traj_interpolator, planner_obj,
              constraint, /*is_pass=*/false);
      const auto [pass_risk, pass_ttc] =
          ComputeSingleConstraintRiskAndTtc(pass_infos, /*has_perturb=*/true);
      const auto [yield_risk, yield_ttc] =
          ComputeSingleConstraintRiskAndTtc(yield_infos, /*has_perturb=*/true);
      // Update the candidate based on the lower one among the pass or yield
      // risk and finalize the decision.
      if (pass_risk > yield_risk) {
        MaybeUpdateCandidate(yield_risk, c_ix, yield_ttc,
                             std::move(yield_infos), max_risk_for_the_candidate,
                             candidate);
        decision = Decision ::YIELD;
      } else {
        MaybeUpdateCandidate(pass_risk, c_ix, pass_ttc, std::move(pass_infos),
                             max_risk_for_the_candidate, candidate);
        decision = Decision ::PASS;
      }
      continue;
    }
    std::vector<PerturbTypeAndCollisionInfo> collision_frame_infos =
        CalculateCollisionInfosForOneConstraintAndDecision(
            agent_reaction_calculator, ego_traj_interpolator, planner_obj,
            constraint, decision == Decision::PASS);
    const auto [single_constraint_risk, ttc_from_risk_model] =
        ComputeSingleConstraintRiskAndTtc(collision_frame_infos,
                                          /*has_perturb=*/true);
    MaybeUpdateCandidate(single_constraint_risk, c_ix, ttc_from_risk_model,
                         std::move(collision_frame_infos),
                         max_risk_for_the_candidate, candidate);
  }
  // if candidate collision_risk is -1.0, it means that the candidate has no
  // conflict with any constraints. In this case, we set the risk to 0.0.
  if (candidate.collision_risk == -1.0) {
    candidate.collision_risk = max_risk_for_the_candidate;
  }
  return max_risk_for_the_candidate;
}

std::vector<PerturbTypeAndCollisionInfo>
ConflictResolver::CalculateCollisionInfosForOneConstraintAndDecision(
    const AgentReactionCalculator& agent_reaction_calculator,
    const PredictedTrajectoryInterpolator& ego_traj_interpolator,
    const PlannerObject& planner_obj, const Constraint& constraint,
    const bool is_pass) const {
  const prediction::pb::PredictedTrajectory agent_ar_traj =
      agent_reaction_calculator.InterpolateArTrajectory(
          constraint, /*selected_discomfort=*/Discomforts::kMax, is_pass);
  const PredictedTrajectoryWrapper agent_ar_traj_wrapper(
      planner_obj.tracked_object(), agent_ar_traj);
  const std::vector<ScrPerturbTypeAndFunctions> perturb_functions =
      GetRiskPerturbFunctions(/*should_perturb=*/true,
                              planner_obj.is_large_vehicle(),
                              agent_ar_traj_wrapper);
  std::vector<PerturbTypeAndCollisionInfo> collision_frame_infos;
  collision_frame_infos.reserve(perturb_functions.size());
  // TODO(tienan): consider using tbb::parallel_for here.
  for (const ScrPerturbTypeAndFunctions& type_and_funcs : perturb_functions) {
    // TODO(chengji, tienan): Support ego backward maneuver.
    CollisionFrameInfo collision_frame_info = ComputeTrajectoryCollisionInfo(
        risk_model_input_->ego_shape, ego_traj_interpolator,
        agent_ar_traj_wrapper, planner::pb::MotionMode::FORWARD,
        type_and_funcs.ego_perturb_func, type_and_funcs.agent_perturb_func);
    if (collision_frame_info.severity.severity_level !=
        voy::perception::CollisionDetection::NO_COLLISION_LEVEL) {
      collision_frame_infos.emplace_back(type_and_funcs.perturb_type,
                                         std::move(collision_frame_info));
    }
  }
  return collision_frame_infos;
}

double GetSeverityValue(const CollisionFrameInfo& info) {
  if (FLAGS_planning_enable_continuous_severity_in_conflict_resolver) {
    // For S0, continuous_severity_value is within [0.0, 1.0).
    // We need to plus 1 to align with severity levels.
    return info.severity.continuous_severity_value + 1;
  }
  // NOTE: This is actually (severity_level + 1). E.g, S0 is converted to 1.
  return static_cast<double>(info.severity.severity_level);
}

// Based on the collision frame infos from different perturbed trajectories,
// computes the single constraint risk and the expected collision TTC.
// Currently we consider the minimum TTC among all collision infos from
// different perturbed trajectories. This is a conservative (safe) assumption.
// TODO(tienan): revisit this, we can consider the median or average.
std::pair<double, double> ConflictResolver::ComputeSingleConstraintRiskAndTtc(
    const std::vector<PerturbTypeAndCollisionInfo>& collision_frame_infos,
    const bool has_perturb) const {
  if (collision_frame_infos.empty()) {
    return std::make_pair(/*risk=*/0.0,
                          /*ttc=*/std::numeric_limits<double>::max());
  }
  const double planner_init_t =
      math::Ms2Sec(risk_model_input_->plan_init_state.start_time);
  double total_risk = 0.0;
  double min_ttc = std::numeric_limits<double>::max();
  for (const auto& [perturb_type, info] : collision_frame_infos) {
    const double ttc_from_risk_model =
        math::Ms2Sec(info.timestamp) - planner_init_t;
    DCHECK_GE(ttc_from_risk_model, 0.0);
    math::UpdateMin(ttc_from_risk_model, min_ttc);
    // TODO(tienan): add a UT to show the trade-off between severity level
    // and TTC.
    const double severity_value = GetSeverityValue(info);
    total_risk += GetPerturbTrajWeight(has_perturb, perturb_type) *
                  std::pow(kSeverityFactor, severity_value) *
                  conflict_evaluator_.TtcWeight(ttc_from_risk_model);
  }
  return std::make_pair(total_risk, min_ttc);
}

const ConflictResolvingCandidate&
ConflictResolver::SelectConflictResolvingProfileUsingStEvaluation(
    const AgentReactionCalculator& agent_reaction_calculator,
    const std::vector<Constraint>& constraints,
    std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs,
    pb::ConflictResolverDebug* debug) const {
  // Calculate the conflict severity score for each candidate.
  for (ConflictResolvingCandidate* candidate : low_risk_candidate_ptrs) {
    candidate->st_evaluation_result =
        conflict_evaluator_.CalculateConflictSeverityGivenSt(
            agent_reaction_calculator, constraints, *candidate);
  }
  if (debug != nullptr) {
    conflict_evaluator_.PopulateMinRangeDebug(debug);
  }
  // Early return if all candidates recognizes a current/past collision (aka.
  // TTC <= 0), in which case we directly select the hardest-possible braking
  // candidate.
  // TODO(shijiegao): fine tune the candidate selection heuristic when finding
  // a conflict before/at the plan init time.
  if (std::all_of(low_risk_candidate_ptrs.begin(),
                  low_risk_candidate_ptrs.end(),
                  [](const ConflictResolvingCandidate* candidate) {
                    return candidate->st_evaluation_result.ttc <=
                           math::constants::kEpsilon;
                  })) {
    rt_event::PostRtEvent<rt_event::planner::NegativeTtcStConflictInScr>();
    if (debug != nullptr) {
      absl::StrAppend(debug->mutable_st_eval_debug_str(),
                      "All candidates collide before plan init time. ");
    }
    return SelectCandidateForCollisionBeforePlanInitTime(
        constraints, low_risk_candidate_ptrs, debug);
  }
  // TODO(shijie): Consider to tune the minor_risk_tolerance if unnecessary EB
  // cases emerge.
  if (AllCandidatesAreBrakingWithMinorRisk(
          /*minor_risk_tolerance=*/0.0, low_risk_candidate_ptrs)) {
    const auto fs_candidate_iter = std::find_if(
        low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
        [](const ConflictResolvingCandidate* candidate) {
          return candidate->conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE;
        });
    if (fs_candidate_iter != low_risk_candidate_ptrs.end()) {
      if (debug != nullptr) {
        absl::StrAppend(
            debug->mutable_risk_eval_debug_str(),
            "All candidates are braking and have minor risks, retain FS. ");
      }
      return **fs_candidate_iter;
    }
  }

  // The candidate selection procedure in s-t evaluation follows this order:
  // 1. **small displacement**: the first priority of candidate selection is
  // to have a small max displacement w.o. conflict when the conflict occurs.
  const std::vector<const ConflictResolvingCandidate*>
      low_max_displacement_candidate_ptrs =
          GetLowMaxDisplacementCandidates(low_risk_candidate_ptrs);

  // 2. **low severity score**: in the remaining candidates with low max
  // displacement, we select the one with the minimum conflict severity score.
  const auto min_conflict_profile_iter =
      std::min_element(low_max_displacement_candidate_ptrs.begin(),
                       low_max_displacement_candidate_ptrs.end(),
                       [](const ConflictResolvingCandidate* lhs,
                          const ConflictResolvingCandidate* rhs) {
                         return lhs->st_evaluation_result.severity_score <
                                rhs->st_evaluation_result.severity_score;
                       });

  // 3. **additional heuristic**: if the min conflict profile is not the
  // default FS candidate, we go through additional heuristic for the
  // candidate selection. The purpose is to guard the precision of the extreme
  // SCR candidates (aka. EB/MS) triggering.
  const ConflictResolvingCandidate& selected_heuristic_candidate =
      PostAnalyzeMinConflictCandidate(**min_conflict_profile_iter, constraints,
                                      low_risk_candidate_ptrs, debug);
  // 4. **hysteresis**.
  return SelectCandidateWithHysteresis(selected_heuristic_candidate,
                                       previous_scr_result_,
                                       low_risk_candidate_ptrs, debug);
}

bool ConflictResolver::ShouldMsIfAllCandidatesPassCollidingConstraintEndState(
    const std::vector<Constraint>& constraints,
    const std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs)
    const {
  for (const ConflictResolvingCandidate* candidate : low_risk_candidate_ptrs) {
    for (const int c_ix : candidate->violated_constraint_ixs) {
      const pb::FenceType fence_type = constraints[c_ix].fence_type;
      if (fence_type == pb::FenceType::kMrcStop) {
        return false;
      }
      if (constraints[c_ix].states[0].abs_lat_gap < math::constants::kEpsilon ||
          !conflict_evaluator_.CandidateCanPassConstraintEndState(
              *candidate, constraints[c_ix])) {
        return false;
      }
    }
  }
  return true;
}

const ConflictResolvingCandidate&
ConflictResolver::SelectCandidateForCollisionBeforePlanInitTime(
    const std::vector<Constraint>& constraints,
    const std::vector<ConflictResolvingCandidate*>& low_risk_candidate_ptrs,
    pb::ConflictResolverDebug* debug) const {
  DCHECK_GT(low_risk_candidate_ptrs.size(), 0);
  // Early return if there is only one candidate.
  if (low_risk_candidate_ptrs.size() == 1) {
    return *(low_risk_candidate_ptrs.front());
  }

  const auto ms_candidate_iter = std::find_if(
      low_risk_candidate_ptrs.begin(), low_risk_candidate_ptrs.end(),
      [](const ConflictResolvingCandidate* candidate) {
        return candidate->conflict_resolving_type ==
               pb::ConflictResolvingType_Enum_MAX_SPEED_PROFILE;
      });

  // If all candidates ultimately pass the constraints and none collide with the
  // first constraint state, prefer MS over braking profiles. In this type of
  // scenario, braking is likely not helpful and speed up is more likely to
  // narrowly avoid collisions.
  if (ms_candidate_iter != low_risk_candidate_ptrs.end()) {
    if (ShouldMsIfAllCandidatesPassCollidingConstraintEndState(
            constraints, low_risk_candidate_ptrs)) {
      if (debug != nullptr) {
        absl::StrAppend(debug->mutable_st_eval_debug_str(),
                        "All candidates eventually pass the constraint, "
                        "braking isn't helpful, select MS.");
      }
      return **ms_candidate_iter;
    }
  }
  const double ego_speed = low_risk_candidate_ptrs[0]->profile.front().v;
  // Select a hard-braking or mild-reacting candidate based on 1) whether
  // there is special-request constraints (e.g. MRC), 2) for hard-braking
  // candidates, whether there is ANY high-risk conflicted objects, and for
  // mild-reacting candidates, whether the object associated with the
  // max st-severity constraint is high-risk.
  for (const ConflictResolvingCandidate* candidate : low_risk_candidate_ptrs) {
    const int max_st_severity_c_ix = candidate->st_evaluation_result.c_ix;
    const int is_hard_brake_candidate =
        IsHardBrakeCandidate(candidate->conflict_resolving_type);
    for (const int c_ix : candidate->violated_constraint_ixs) {
      const Constraint& constraint = constraints[c_ix];
      const pb::FenceType fence_type = constraint.fence_type;
      // TODO(shijie): consider choose the hardest-braking candidate if the
      // candidate is colliding with a hard boundary.
      if (fence_type == pb::FenceType::kMrcStop) {
        if (debug != nullptr) {
          absl::StrAppend(debug->mutable_st_eval_debug_str(),
                          "Choose hardest-braking candidate for MRC.");
        }
        return GetHardestBrakeCandidate(low_risk_candidate_ptrs);
      }
      // For non-hard-brake candidate, only evaluate the constraint with the max
      // st-severity.
      if (!is_hard_brake_candidate && c_ix != max_st_severity_c_ix) {
        continue;
      }
      if (IsConstraintAssociatedWithHighRiskObj(constraint, ego_speed)) {
        if (debug != nullptr) {
          absl::StrAppendFormat(debug->mutable_st_eval_debug_str(),
                                "Have a potentially high risk object %d. "
                                "Choose hardest-braking candidate.",
                                constraint.obj_id);
        }
        return GetHardestBrakeCandidate(low_risk_candidate_ptrs);
      }
    }
  }

  // Default to use the mild-reacting profile.
  if (debug != nullptr) {
    absl::StrAppend(debug->mutable_st_eval_debug_str(),
                    "Default to the mildest-reacting candidate.");
  }
  return GetMildestReactCandidate(low_risk_candidate_ptrs);
}

const ConflictResolvingCandidate&
ConflictResolver::PostAnalyzeMinConflictCandidate(
    const ConflictResolvingCandidate& min_conflict_candidate,
    const std::vector<Constraint>& constraints,
    const std::vector<ConflictResolvingCandidate*>& profile_candidate_ptrs,
    pb::ConflictResolverDebug* debug) const {
  const double ego_speed = min_conflict_candidate.profile.front().v;
  // Consider triggering CS heuristic if ego is currently not stop and
  // the min conflict candidate is hard-braking.
  if (!math::NearZero(ego_speed) &&
      IsHardBrakeCandidate(min_conflict_candidate.conflict_resolving_type)) {
    const auto const_speed_iter = std::find_if(
        profile_candidate_ptrs.begin(), profile_candidate_ptrs.end(),
        [](const ConflictResolvingCandidate* candidate) {
          return candidate->conflict_resolving_type ==
                 pb::ConflictResolvingType_Enum_CONST_SPEED_PROFILE;
        });
    // Select CS candidate as a heuristic as long as the candidate does
    // not associate with a high risk object.
    if (const_speed_iter != profile_candidate_ptrs.end()) {
      const ConflictResolvingCandidate& cs_candidate = **const_speed_iter;
      const int max_severity_c_ix = cs_candidate.st_evaluation_result.c_ix;
      if (max_severity_c_ix < 0) {
        if (debug != nullptr) {
          absl::StrAppendFormat(debug->mutable_st_eval_debug_str(),
                                "CS heuristic. No conflicted constraint.");
        }
        return cs_candidate;
      }
      DCHECK_LT(max_severity_c_ix, constraints.size());
      // Only evaluate the constraint with the max st-severity.
      const Constraint& constraint = constraints[max_severity_c_ix];
      if (!IsConstraintAssociatedWithHighRiskObj(constraint, ego_speed)) {
        if (debug != nullptr) {
          absl::StrAppendFormat(
              debug->mutable_st_eval_debug_str(),
              "CS heuristic. Critical constraint %s no high risk.",
              constraint.unique_constraint_id);
        }
        return cs_candidate;
      }
    }
  }
  // If ego will have a near-zero conflicting speed in both the min conflict
  // profile and FS profile, we just select FS. The rationale is that if
  // EB/MS does not provide a significant different advantaged speed over
  // FS, we should not need to use them.
  if (min_conflict_candidate.conflict_resolving_type !=
      pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE) {
    // TODO(tienan): consider if we should remove the near-zero restriction
    // and just select FS when FS and EB/MS has a similar conflicting speed.
    // We should also consider other conflict configs e.g. impacting angle and
    // impacting speeds.
    if (min_conflict_candidate.st_evaluation_result.ego_conflicting_speed <
        kLowRiskConflictingSpeedInMps) {
      const auto low_risk_full_stop_iter = std::find_if(
          profile_candidate_ptrs.begin(), profile_candidate_ptrs.end(),
          [](const ConflictResolvingCandidate* candidate) {
            return candidate->conflict_resolving_type ==
                       pb::ConflictResolvingType_Enum_FULL_STOP_PROFILE &&
                   candidate->st_evaluation_result.ego_conflicting_speed <
                       kLowRiskConflictingSpeedInMps;
          });
      if (low_risk_full_stop_iter != profile_candidate_ptrs.end()) {
        const ConflictResolvingCandidate& fs_candidate =
            **low_risk_full_stop_iter;
        if (ShouldTriggerFullStopHeuristic(fs_candidate, constraints,
                                           planner_object_map_,
                                           min_conflict_candidate, debug)) {
          return fs_candidate;
        }
      }
    }
  }
  return min_conflict_candidate;
}

bool ConflictResolver::IsConstraintAssociatedWithHighRiskObj(
    const Constraint& constraint, const double ego_speed) const {
  const ObjectId object_id = constraint.obj_id;
  if (object_id <= 0) {
    return false;
  }
  const auto object_planner_obj_iter = planner_object_map_.find(object_id);
  if (object_planner_obj_iter == planner_object_map_.end()) {
    return false;
  }
  const auto object_proximity_info_iter =
      object_proximity_info_map_.find(object_id);
  if (object_proximity_info_iter == object_proximity_info_map_.end()) {
    return false;
  }
  const bool is_allow_const_speed_obj = constraint.settings.allow_const_speed;
  DCHECK(!math::NearZero(ego_ra_to_fa_));
  if (!IsPotentiallyHighRiskObject(
          object_planner_obj_iter->second, object_proximity_info_iter->second,
          ego_speed, ego_ra_to_fa_, is_allow_const_speed_obj)) {
    return false;
  }
  return true;
}

}  // namespace speed
}  // namespace planner
