"""locsvr services"""
# https://engine.intra.xiaojukeji.com/docs/manual/locserver/locsvr_thrift.html
import time
import requests
from django.conf import settings
from thrift.protocol import TBinaryProtocol
from thrift.transport import TTransport
from thrift.transport import TSocket

from common.utils import cache_utils
from daypack.services.locsvr.locsvr import LocService, constants
import voy_didilog.logger as dlog


LOGGER = dlog.Logger(__name__)
HTTP_RET_CODE_OK = 10000


def _make_request(log_id: int, lng: float, lat: float):
  token = settings.LOCSVR_TOKEN
  coord = constants.Coordinate(lng, lat)
  coord_type = constants.CoordType.WGS84
  area_detail = constants.Area_RET_TYPE.AREA_ADMIN_DETAIL
  area_filter = constants.AreaFilter(area_detail)
  req = constants.MultiCoordRequest(
      log_id, token, [coord],
      coord_type, area_filter
  )
  return req


def get_area_by_coord(lng: float, lat: float):
  """
  Query area info by coord
  Args:
    lng: Longitude
    lat: Latitude
  Returns:
    list: City info sorted as province/city/county or None
  """
  try:
    start_time = int(time.time() * 1000)
    LOGGER.info(f"get_area_by_coord log_id: {start_time},"
                f"lng: {lng}, lat: {lat}")
    socket = TSocket.TSocket(
        settings.LOCSVR_HOST, settings.LOCSVR_THRIFT_PORT
    )
    socket.setTimeout(2000)
    transport = TTransport.TFramedTransport(socket)
    protocol = TBinaryProtocol.TBinaryProtocol(transport)
    client = LocService.Client(protocol)
    transport.open()

    req = _make_request(start_time, lng, lat)
    resp = client.MultiAreaInfoByCoord(req)
    latency = int(time.time() * 1000) - start_time
    LOGGER.info(f"get_area_by_coord log_id: {start_time},"
                f"resp: {resp}, cost: {latency}ms")
    if resp.ret_code == constants.Error.LOCSVR_RET_OK:
      city_info = resp.city_infos.get(0)
      return [
          city_info.provinceid, city_info.cityid,
          city_info.countyid
      ]
    LOGGER.error(f"get_area_by_coord log_id: {start_time},"
                 f"code: {resp.ret_code}, msg: {resp.ret_msg}")
    return None
  except Exception as e:  # pylint:disable=W0718
    LOGGER.error(f"get_area_by_coord log_id: {start_time},error: {e}")
    return None
  finally:
    transport.close()


@cache_utils.redis_cache(expire=120)
def get_city_by_county(county_id: int):
  """
  Query city info by county id
  Args:
    county_id: County id
  Returns:
    list: City info sorted as province/city/county or None
  """
  try:
    start_time = int(time.time() * 1000)
    LOGGER.info(f"get_city_by_county log_id: {start_time},"
                f"county_id: {county_id}")
    url = (f"http://{settings.LOCSVR_HOST}:{settings.LOCSVR_HTTP_PORT}"
           f"/locsvr/get_county")
    params = {
        "token": settings.LOCSVR_TOKEN,
        "countyids": county_id,
        "log_id": start_time,
    }
    resp = requests.get(url, params=params, timeout=2)
    latency = int(time.time() * 1000) - start_time
    LOGGER.info(f"get_city_by_county log_id: {start_time},"
                f"cost: {latency}ms")
    resp.raise_for_status()
    data = resp.json()
    if data["errno"] != HTTP_RET_CODE_OK:
      LOGGER.error(f"get_city_by_county log_id: {start_time},"
                   f"code: {data['errno']}, msg: {data['errmsg']}")
      return None
    city_info = data["countyinfo"][0]
    return [
        city_info["province_desc"], city_info["city_desc"],
        city_info["county_desc"]
    ]
  except Exception as e:  # pylint:disable=W0718
    LOGGER.error(f"get_city_by_county log_id: {start_time},error: {e}")
    return None
