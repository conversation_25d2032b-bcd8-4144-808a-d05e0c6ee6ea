#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_H_

#include <memory>
#include <string>
#include <vector>

#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/sequence_blocking_state_computer.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/scenario_identifier_util.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "voy_protos/telemetry.pb.h"

namespace planner {
namespace path {

// DynamicOvertakePathReasoner reasons about the dynamic agent that ego may want
// to overtake, and sets special required lateral gap or other properties.
class DynamicOvertakePathReasoner : public AbstractReasoner {
 public:
  // The default comfortable lateral gap for the dynamic nudged agent.
  static constexpr double kComfortRequiredLateralGapForOvertakeInMeter = 1.5;

  // Constructor.
  explicit DynamicOvertakePathReasoner(const EgoContextInfo& ego_context_info);
  // Destructor.
  virtual ~DynamicOvertakePathReasoner() = default;

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentInLaneStates& agent_inlane_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final;

  bool ShouldInvokePreReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const ObjectOccupancyState& object_occupancy_state,
      pb::BehaviorType behavior_type, bool is_stationary) const final;

  bool PreReason(
      const AgentInLaneStates& agent_inlane_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PreReason(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_data,
      const AgentSLBoundary& object_sl_boundary,
      TrajectoryIntentionMetaData& trajectory_intention_data) const final;

  bool PostReason(const AgentInLaneStates& agent_inlane_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  bool PostReason(const ObjectOccupancyState& object_occupancy_state,
                  const LateralBlockingStateMetaData& blocking_state_data,
                  const TrajectoryIntentionMetaData& trajectory_intention_data,
                  IntentionResultMetaData& intention_result) const final;

  bool ShouldInvokePreRepulsionReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
      std::string& debug_str) const final;

  std::unique_ptr<RepulsionMetaData> PreRepulsionReason(
      const AgentInLaneStates& agent_inlane_state,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      const LateralBlockingStateMetaData& blocking_state_data,
      std::string& debug_str) const;

  std::unique_ptr<RepulsionMetaData> PreRepulsionReason(
      const ObjectOccupancyState& object_occupancy_state,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      const LateralBlockingStateMetaData& blocking_state_data,
      std::string& debug_str) const;

  bool ShouldInvokePostReason(
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
      bool is_stationary) const final;

 private:
  // Updates the trajectory intention data with the pre-reasoned sequence
  // blocking state.
  void PreReasoningForBlockingSequence(
      const AgentBlockingSequenceInfo& recomputed_blocking_sequence_info,
      TrajectoryIntentionMetaData& trajectory_intention_data) const;

  // Adjusts the start nudge index such that we can start nudging earlier,
  // oncoming agent is excluded for now.
  void AdjustIntentionResultForPassDecision(
      const AgentInLaneStates& agent_inlane_state,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      const pb::SnapshotIntention::PassState& tracked_pass_decision,
      const LateralBlockingStateMetaData& blocking_state_data,
      IntentionResultMetaData& intention_result) const;

  void AdjustIntentionResultForPassDecision(
      const ObjectOccupancyState& object_occupancy_state,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      const pb::SnapshotIntention::PassState& tracked_pass_decision,
      const LateralBlockingStateMetaData& blocking_state_data,
      IntentionResultMetaData& intention_result) const;

  // Backward searches for the first snapshot index which ego can nudge
  // comfortably.
  int GetFirstNudgeSnapshotIndex(
      int original_start_pos, pb::SnapshotIntention::PassState pass_decision,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const std::vector<math::Range<int>>& effective_ranges,
      const std::vector<StampedAgentSnapshotInLaneState>& predicted_states)
      const;

  int GetFirstNudgeSnapshotIndex(
      int original_start_pos, pb::SnapshotIntention::PassState pass_decision,
      const ObjectOccupancyState& object_occupancy_state,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const std::vector<math::Range<int>>& effective_ranges,
      const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
      pb::LateralBlockingState::BlockingState current_snapshot_blocking_state)
      const;

  // Returns true if this snapshot is hard blocking.
  bool IsHardBlockingSnapshot(
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const StampedAgentSnapshotInLaneState& snapshot_param,
      const std::vector<math::Range<int>>& effective_ranges, int index) const;

  bool IsHardBlockingSnapshot(
      const ObjectOccupancyState& object_occupancy_state,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const pb::LateralBlockingState::BlockingState
          current_snapshot_blocking_state,
      const std::vector<math::Range<int>>& effective_ranges, int index) const;

  // Estimates the nudge range which ego can nudge with lateral acceleration.
  math::Range<int> EstimateComfortNudgeRange(
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
      const std::vector<math::Range<int>>& effective_ranges,
      pb::SnapshotIntention::PassState pass_decision) const;

  math::Range<int> EstimateComfortNudgeRange(
      const ObjectOccupancyState& object_occupancy_state,
      const std::vector<pb::BlockingSequence>& blocking_sequences,
      const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
      const std::vector<math::Range<int>>& effective_ranges,
      pb::SnapshotIntention::PassState pass_decision,
      pb::LateralBlockingState::BlockingState current_snapshot_blocking_state)
      const;

  //
  // Data member
  //
  SequenceBlockingStateComputer sequence_blocking_state_computer_;
};

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_DYNAMIC_OVERTAKE_PATH_REASONER_H_
