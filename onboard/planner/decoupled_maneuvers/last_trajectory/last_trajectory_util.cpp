#include "planner/decoupled_maneuvers/last_trajectory/last_trajectory_util.h"

#include <vector>

#include "adv_geom/path2d_with_juke.h"
#include "exception_handler/exception_handler_main/trajectory_result.h"
#include "planner/behavior/util/waypoint_edge_sequence_generator/waypoint_edge_sequence_utility.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"

namespace planner {

// Max odom step of last path for density check.
constexpr double kLastPathMaxSamplingDistInMeter = 0.59;
constexpr double kLastPathPoseErrorInMeter = 1e-3;
constexpr double kLastPathMaxCurvature = 0.15;
constexpr double kLastPathMinSpeed = 1;

// Max pose divergence in meter.
constexpr double kMaxPoseDivergenceInMeter = 0.1;

using adv_geom::Path2dWithJuke;
using math::geometry::PolylineCurve2d;

// Returns true if we can reuse the last path.
// prerequisite: no replan in init state, same Maneuver, same LaneSequence.
bool CanReuseLastPath(
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const std::vector<const pnc_map::Lane*>& curr_lane_sequence,
    const pb::BehaviorType behavior_type) {
  if (behavior_type != previous_iter_seed.selected_behavior_type()) {
    return false;
  }
  if (world_model.robot_state().plan_init_state().type ==
      pb::PlanInitType::COLDSTART) {
    return false;
  }
  if (world_model.last_seed().last_selected_maneuver_type() !=
      pb::ManeuverType::DECOUPLED_FORWARD) {
    return false;
  }
  if (!previous_iter_seed.has_selected_path()) {
    return false;
  }
  // If the lane sequence is short, the path planner will
  // extend the path with interpolation(http://cr.intra.xiaojukeji.com/D232530).
  // We can not reuse it. We latch lane sequence during lane change, so the lane
  // sequence is often not long enough. If the last behavior is lane change, we
  // want to keep the last CL candidate available to stable lane change.
  if (previous_iter_seed.path_seed().is_reference_extended() &&
      previous_iter_seed.selected_behavior_type() !=
          pb::BehaviorType::CROSS_LANE) {
    return false;
  }

  // While the behavior type is pull over, we should not select the last PO path
  // as the pull over destination or pull over state may be totally changed.
  // TODO(liwen, zixuan): Maybe consider to use the last pull over path again
  // when it is needed somewhere in the future. BUT at least for current logic,
  // we avoid to use it to prevent potentially introduced issues e.g. when
  // destination updated or dest gap changed.
  if (behavior_type == pb::BehaviorType::DECOUPLED_PULL_OVER) {
    return false;
  }

  // If the last trajectory is from ML planner, the last path can not be reused.
  if (world_model.robot_state().last_trajectory().is_ml_planner_trajectory()) {
    return false;
  }

  // If trajectory is not selected by planner, and the behavior type or the lane
  // sequence is not same with last trajectory, the last path can not be reused.
  if (FLAGS_planning_disable_planner_trajectory) {
    if (!(world_model.robot_state()
              .last_trajectory()
              .has_extra_planner_signals() &&
          world_model.robot_state()
              .last_trajectory()
              .extra_planner_signals()
              .has_scene_metadata_for_debug() &&
          world_model.robot_state()
                  .last_trajectory()
                  .extra_planner_signals()
                  .scene_metadata_for_debug()
                  .behavior() == behavior_type)) {
      return false;
    }
    if (!world_model.latest_snapshot_ptr()->global_init_state_ptr) {
      return false;
    }
    const auto last_lane_sequence =
        ::exception_handler::exception_handler_main::TrajectoryResultInstance::
            GetInstance()
                ->GetLastLaneSequenceFromGlobalInitState(
                    *world_model.latest_snapshot_ptr()->global_init_state_ptr);
    return lane_selection::IsEndMatchBeginning(last_lane_sequence,
                                               curr_lane_sequence);
  }

  // Do not reuse when lane sequence changed.
  return (lane_selection::IsEndMatchBeginning(
      previous_iter_seed.selected_lane_sequence(), curr_lane_sequence));
}

// Truncates last path to make its first pose starting from proximal point of
// init state and returns the path if exists. If this init state diverges too
// much from last path, return nullopt.
// TODO(judychen): Remove the LC scope after Q2 release cut.
std::optional<pb::Path> TruncateLastPath(
    const pb::Path& last_path,
    const RobotStateSnapshot& plan_init_state_snapshot,
    const bool is_last_iter_lane_change) {
  if (last_path.poses_size() < 2) {
    return std::nullopt;
  }
  const adv_geom::Path2dWithJuke last_path_curve(last_path);
  const math::geometry::Point2d& plan_init_ra_pose =
      plan_init_state_snapshot.rear_axle_position();
  const auto plan_init_ra_pose_proximity = last_path_curve.QueryProximity(
      plan_init_ra_pose, math::pb::UseExtensionFlag::kForbid);
  const auto plan_init_ra_pose_extend_proximity =
      last_path_curve.QueryProximity(plan_init_ra_pose,
                                     math::pb::UseExtensionFlag::kAllow);
  const bool is_outside_path = plan_init_ra_pose_proximity.relative_position !=
                               math::RelativePosition::kWithIn;
  // Divergence is large, we can not use last path. If ego is changing lane, we
  // allow longitudinal pose diverge error.
  const bool should_use_last_path_by_proximity_arc_length =
      is_last_iter_lane_change
          ? is_outside_path && (plan_init_ra_pose_extend_proximity.arc_length <
                                    -kLastPathPoseErrorInMeter ||
                                plan_init_ra_pose_extend_proximity.arc_length >
                                    last_path_curve.GetTotalArcLength())
          : is_outside_path;
  if (plan_init_ra_pose_proximity.dist >
          kMaxPoseDivergenceInMeter - kLastPathPoseErrorInMeter ||
      should_use_last_path_by_proximity_arc_length) {
    return std::nullopt;
  }
  // If ego is stationary, do not reuse last path for there is no path
  // flickering problem.
  if (plan_init_state_snapshot.speed() < kLastPathMinSpeed) {
    return std::nullopt;
  }
  pb::Path truncated_last_path;
  // use proximal point as the first pose of truncated_last_path.
  pb::PathPose* init_pose = truncated_last_path.add_poses();
  init_pose->set_odom(0.0);
  init_pose->set_x_pos(plan_init_ra_pose_proximity.x);
  init_pose->set_y_pos(plan_init_ra_pose_proximity.y);
  init_pose->set_heading(
      last_path_curve.GetInterpHeading(plan_init_ra_pose_proximity.arc_length));
  init_pose->set_curvature(last_path_curve.GetInterpCurvature(
      plan_init_ra_pose_proximity.arc_length));
  init_pose->set_pinch(
      last_path_curve.GetInterpPinch(plan_init_ra_pose_proximity.arc_length));

  for (const auto& pose : last_path.poses()) {
    // Do not reuse last path in high curvature.
    if (std::abs(pose.curvature()) > kLastPathMaxCurvature) {
      return std::nullopt;
    }
    // new pose odom must be larger than init pose with tiny error.
    if (pose.odom() <
        plan_init_ra_pose_proximity.arc_length + kLastPathPoseErrorInMeter) {
      continue;
    }
    const auto& last_pose =
        truncated_last_path.poses(truncated_last_path.poses_size() - 1);
    const double delta_odom = math::geometry::Distance(
        math::geometry::Point2d{pose.x_pos(), pose.y_pos()},
        math::geometry::Point2d{last_pose.x_pos(), last_pose.y_pos()});
    // check density, otherwise RunSpeedPipeline will DCHECK.
    if (delta_odom > kLastPathMaxSamplingDistInMeter) {
      return std::nullopt;
    }
    pb::PathPose* new_pose = truncated_last_path.add_poses();
    *new_pose = pose;
    new_pose->set_odom(last_pose.odom() + delta_odom);
  }
  if (truncated_last_path.poses_size() <= 2) {
    return std::nullopt;
  }
  return truncated_last_path;
}

// Generates warm start speed profile from last trajectory, which starts from
// the current iteration's planning initial state time.
std::optional<speed::Profile> GenerateWarmStartSpeedProfile(
    const RobotState& robot_state, pb::ManeuverType last_selected_maneuver_type,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const speed::pb::SpeedGeneratorConfig& speed_config,
    bool check_speed_accel_error) {
  if (robot_state.plan_init_state().type == pb::PlanInitType::COLDSTART) {
    return std::nullopt;
  }
  if (last_selected_maneuver_type != pb::ManeuverType::DECOUPLED_FORWARD) {
    return std::nullopt;
  }
  const pb::Trajectory& last_trajectory =
      previous_iter_seed.selected_trajectory();
  if (last_trajectory.poses_size() == 0) {
    return std::nullopt;
  }

  speed::Profile last_profile;
  last_profile.reserve(last_trajectory.poses_size());
  for (const auto& pose : last_trajectory.poses()) {
    last_profile.emplace_back(math::Ms2Sec(pose.timestamp()), pose.odom(),
                              pose.speed(), pose.accel(), pose.jerk());
  }
  const RobotStateSnapshot& plan_init_state_snapshot =
      robot_state.plan_init_state_snapshot();

  // Note, evasive_limits is used to extend the speed profile if it is shorter
  // than desired horizon from last trajectory.
  return GenerateWarmStartSpeedProfileFromLastProfile(
      last_profile, math::Ms2Sec(plan_init_state_snapshot.timestamp()),
      plan_init_state_snapshot.speed(), plan_init_state_snapshot.acceleration(),
      speed_config.for_search().n_horizon(), speed_config.for_search().dt(),
      speed::DiscomfortVaryingLimits::EvasiveLimits(
          speed_config.for_car_type().limits()),
      speed_config.for_optimizer().allowed_negative_speed_error(),
      speed_config.for_optimizer().allowed_move_forward_x_error(),
      check_speed_accel_error);
}

}  // namespace planner
