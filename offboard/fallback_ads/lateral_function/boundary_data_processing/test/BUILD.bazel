load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "boundary_data_processing_test",
    srcs = ["boundary_data_processing_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads/lateral_function/boundary_data_processing",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "check_lane_property_test",
    srcs = ["check_lane_property_test.cpp"],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
    ],
    deps = [
        "//offboard/fallback_ads/lateral_function/boundary_data_processing:check_lane_property",
        "@voy-sdk//:gtest",
    ],
)
