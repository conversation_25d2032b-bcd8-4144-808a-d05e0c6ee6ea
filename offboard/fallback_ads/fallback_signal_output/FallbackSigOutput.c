/*
 * File: FallbackSigOutput.c
 *
 * Code generated for Simulink model 'FallbackSigOutput'.
 *
 * Model version                  : 1.318
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Sun Jun  8 12:40:32 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "FallbackSigOutput.h"
#include "FallbackSigOutput_private.h"
#include "common_math_library/look1_iflf_binlcapw.h"

const SG_SwtExtrLiFromAPI FallbackSigOutput_rtZSG_SwtExtrLiFromAPI = {
  0U,                                  /* SwtExtrLiFromAPILiExtFctCntr */
  0U,                                  /* SwtExtrLiFromAPILiExtFctCrc */
  0U,                                  /* SwtExtrLiFromAPILiExtFctQf */
  0U                                   /* SwtExtrLiFromAPILiExtFctReq1 */
} ;                                    /* SG_SwtExtrLiFromAPI ground */

const SG_AdFusedFricEstimn FallbackSigOutput_rtZSG_AdFusedFricEstimn_adt = {
  0U,                                  /* AdFusedFricEstimnFricEstimn */
  Cx0_Qly3_De0                         /* AdFusedFricEstimnFricEstimnConf */
} ;                                    /* SG_AdFusedFricEstimn ground */

const SG_AdFreeDst FallbackSigOutput_rtZSG_AdFreeDst_adt = {
  0U,                                  /* AdFreeDstChks */
  0U,                                  /* AdFreeDstCntr */
  0U,                                  /* AdFreeDstFreeDstFwd */
  0U                                   /* AdFreeDstFreeDstRvs */
} ;                                    /* SG_AdFreeDst ground */

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
float32 EAD_AccRequest;                /* '<S19>/Switch' */
boolean EAD_AcuFbFdCanError;           /* '<S58>/Signal Conversion3' */
boolean EAD_AcuFbMid2CanError_P = 0;   /* Referenced by: '<S58>/Parameter16' */
boolean EAD_AcuFbMid3CanError;         /* '<S58>/OR3' */
boolean EAD_AcuFbMid3CanError_P = 0;   /* Referenced by: '<S58>/Parameter13' */
boolean EAD_AcuFbMid5CanError;         /* '<S58>/OR4' */
boolean EAD_AcuFbMid5CanError_P = 0;   /* Referenced by: '<S58>/Parameter14' */
boolean EAD_AcuFbMid6CanError;         /* '<S58>/OR5' */
boolean EAD_AcuFbMid6CanError_P = 0;   /* Referenced by: '<S58>/Parameter15' */
float32 EAD_AdActiveDelayTime_P = 1.0F;/* Referenced by: '<S10>/DelayTime' */
boolean EAD_AdActiveRequest;           /* '<S42>/Multiport Switch1' */
uint8 EAD_AdDirReqDirReq;              /* '<S10>/Switch22' */
uint8 EAD_AdDirReqDirReq_P = 0U;       /* Referenced by: '<S10>/Constant36' */
float32 EAD_AdNomALgtReqGroupSafeALgtNomReqMax;/* '<S16>/Signal Conversion' */
float32 EAD_AdNomALgtReqGroupSafeALgtNomReqMin;/* '<S16>/Signal Conversion1' */
float32 EAD_AdNomALgtReqGroupSafeNegLimForJerk;/* '<S16>/Signal Conversion9' */
float32 EAD_AdNomPosLimForJerk;        /* '<S16>/Signal Conversion10' */
float32 EAD_AdPrimALgtLimReqGroupSafeALgtMaxReq;/* '<S16>/Signal Conversion4' */
float32 EAD_AdPrimALgtLimReqGroupSafeALgtMinReq;/* '<S16>/Signal Conversion6' */
float32 EAD_AdPrimWhlAgReqGroupSafeWhlAgReq;/* '<S13>/Switch2' */
float32 EAD_AdQuitDelayTime_P = 1.0F;  /* Referenced by: '<S10>/DelayTime4' */
boolean EAD_AdQuitRequest;             /* '<S43>/Multiport Switch1' */
float32 EAD_AdSecALgtLimReqGroupSafeALgtMaxReq;/* '<S16>/Signal Conversion5' */
float32 EAD_AdSecALgtLimReqGroupSafeALgtMinReq;/* '<S16>/Signal Conversion7' */
float32 EAD_AdSecWhlAgReqGroupSafeWhlAgReq;/* '<S12>/Switch2' */
float32 EAD_AdSetSpd;                  /* '<S10>/Constant5' */
uint8 EAD_AdStandStillReqReq;          /* '<S10>/Switch21' */
uint8 EAD_AdStandStillReqReq_P = 0U;   /* Referenced by: '<S10>/Constant34' */
uint8 EAD_AdWhlLockReqNoReqApplyRel;   /* '<S10>/Constant2' */
uint8 EAD_AdpLiReqFromAPIHzrdLiActvnReq;/* '<S10>/Switch2' */
uint8 EAD_AdpLiReqFromAPIHzrdLiDeactnReq;/* '<S10>/Switch3' */
uint8 EAD_AdpLiReqFromAPIIncrLiRiReq;  /* '<S10>/Constant13' */
uint8 EAD_AdpLiReqFromAPIIndcrLeReq;   /* '<S10>/Signal Conversion' */
uint8 EAD_AswVersion_P = 103U;         /* Referenced by:
                                        * '<S7>/Parameter'
                                        * '<S47>/Parameter'
                                        * '<S57>/Parameter'
                                        */
uint8 EAD_AutnmsDrvStReqAutnmsDrvStReq;/* '<S10>/Switch1' */
boolean EAD_CameraCanError;            /* '<S58>/OR6' */
boolean EAD_CameraCanError_P = 0;      /* Referenced by: '<S58>/Parameter18' */
float32 EAD_CanTimeoutThd_P = 5.0F;    /* Referenced by: '<S49>/Constant17' */
boolean EAD_ChooseSteerAngleRateLimit_P = 0;/* Referenced by: '<S8>/Parameter7' */
boolean EAD_CswAccRateLmt_P = 1;       /* Referenced by: '<S17>/Parameter' */
boolean EAD_CswAdDirReqDirReq_P = 0;   /* Referenced by: '<S10>/Constant35' */
boolean EAD_CswAdStandStillReqReq_P = 0;/* Referenced by: '<S10>/Constant33' */
boolean EAD_CswEnterAdMode_P = 0;      /* Referenced by: '<S10>/Constant18' */
boolean EAD_CswFbAcuReserved_P = 0;    /* Referenced by: '<S57>/Parameter8' */
boolean EAD_CswHmiAutnmsSts_P = 0;     /* Referenced by: '<S10>/Constant31' */
boolean EAD_CswMcuStatus_P = 0;        /* Referenced by: '<S57>/Parameter6' */
boolean EAD_CswOutOfOddDetect_P = 0;   /* Referenced by: '<S57>/Parameter19' */
boolean EAD_CswQuitAdMode_P = 0;       /* Referenced by: '<S10>/Constant16' */
boolean EAD_CswRawValue_P = 0;         /* Referenced by: '<S57>/Parameter21' */
boolean EAD_CswSelfCheckStatus_P = 0;  /* Referenced by: '<S58>/Parameter' */
boolean EAD_CswSensor1v1rStatus_P = 0; /* Referenced by: '<S56>/Parameter2' */
boolean EAD_CswSteerAngleRate_P = 1;   /* Referenced by: '<S8>/Parameter' */
boolean EAD_CswVehControlStatus_P = 0; /* Referenced by: '<S57>/Parameter4' */
boolean EAD_CswVehOperStReq_P = 0;     /* Referenced by: '<S10>/Constant29' */
boolean EAD_EnableStandStill;          /* '<S10>/AND5' */
uint8 EAD_FbAcuReserved_P = 0U;        /* Referenced by: '<S57>/Parameter9' */
uint8 EAD_FbAcuRollingCounter;         /* '<S60>/Output' */
float32 EAD_FilteredActualVehAcc;      /* '<S20>/Add' */
boolean EAD_FrontCameraCalibrationEnable;/* '<S49>/AND1' */
boolean EAD_FrontCameraCanTimeout;     /* '<S49>/Signal Conversion15' */
float32 EAD_FrontCameraCanTimer;       /* '<S49>/Signal Conversion33' */
boolean EAD_FrontRadarCalibrationEnable;/* '<S49>/AND' */
boolean EAD_FrontRadarCanTimeout;      /* '<S49>/Signal Conversion16' */
float32 EAD_FrontRadarCanTimer;        /* '<S49>/Signal Conversion32' */
float32 EAD_HazardLightDelayTime_P = 0.15F;/* Referenced by:
                                            * '<S10>/Parameter3'
                                            * '<S10>/Parameter5'
                                            */
uint8 EAD_HmiAutnmsStsHmiAutnmsSts;    /* '<S10>/Switch20' */
uint8 EAD_HmiAutnmsSts_P = 0U;         /* Referenced by: '<S10>/Constant32' */
float32 EAD_JerkRequest;               /* '<S17>/Signal Copy' */
float32 EAD_LimitSteeringAngle;        /* '<S8>/Divide2' */
float32 EAD_LmtAccRequest;             /* '<S27>/Switch2' */
float32 EAD_LmtMaxAccRequest;          /* '<S26>/Switch2' */
float32 EAD_LmtMaxJerkRequest;         /* '<S28>/Switch2' */
float32 EAD_LmtMinAccRequest;          /* '<S25>/Switch2' */
float32 EAD_LmtMinJerkRequest;         /* '<S29>/Switch2' */
float32 EAD_MaxAccRate_P = 2.0F;       /* Referenced by: '<S17>/Parameter2' */
float32 EAD_MaxFrontSteerAngleRate_M[10] = { 25.0F, 25.0F, 25.0F, 25.0F, 25.0F,
  25.0F, 17.2F, 12.4F, 7.9F, 5.5F } ;
                                    /* Referenced by: '<S8>/1-D Lookup Table' */

float32 EAD_MaxSteerAngleRate_P = 0.06F;/* Referenced by: '<S8>/Parameter2' */
uint8 EAD_McuStatus;                   /* '<S57>/Switch3' */
uint8 EAD_McuStatus_P = 0U;            /* Referenced by: '<S57>/Parameter7' */
uint8 EAD_McuVersion;                  /* '<S57>/Switch4' */
boolean EAD_OnlyUseTrajectory_P = 0;   /* Referenced by:
                                        * '<S8>/Parameter3'
                                        * '<S16>/Parameter1'
                                        */
boolean EAD_RadarCanError;             /* '<S58>/OR7' */
boolean EAD_RadarCanError_P = 0;       /* Referenced by: '<S58>/Parameter20' */
float32 EAD_RawAccRequest;             /* '<S18>/Signal Conversion' */
boolean EAD_RawAcuFbMid3CanError;      /* '<S58>/Signal Conversion' */
boolean EAD_RawAcuFbMid5CanError;      /* '<S58>/Signal Conversion1' */
boolean EAD_RawAcuFbMid6CanError;      /* '<S58>/Signal Conversion2' */
boolean EAD_RawCameraCanError;         /* '<S58>/Signal Conversion7' */
uint8 EAD_RawHmiAutnmsSts;             /* '<S10>/Switch' */
float32 EAD_RawJerkRequest;            /* '<S18>/Signal Conversion1' */
boolean EAD_RawRadarCanError;          /* '<S58>/Signal Conversion8' */
uint16 EAD_RawSelfCheckStatus;         /* '<S65>/Bitwise AND1' */
boolean EAD_RawVehFbMid3CanError;      /* '<S58>/Signal Conversion5' */
boolean EAD_RawVehFbMid5CanError;      /* '<S58>/Signal Conversion4' */
boolean EAD_RawVehFbMid6CanError;      /* '<S58>/Signal Conversion6' */
float32 EAD_SecAdNomALgtReqGroupSafeALgtNomReqMax;/* '<S16>/Signal Conversion2' */
float32 EAD_SecAdNomALgtReqGroupSafeALgtNomReqMin;/* '<S16>/Signal Conversion3' */
float32 EAD_SecAdNomALgtReqGroupSafeNegLimForJerk;/* '<S16>/Signal Conversion8' */
float32 EAD_SecAdNomALgtReqGroupSafePosLimForJerk;/* '<S16>/Signal Conversion11' */
uint8 EAD_SecAdWhlLockReqNoReqApplyRel;/* '<S10>/Constant3' */
uint16 EAD_SelfCheckStatus;            /* '<S58>/Switch' */
uint16 EAD_SelfCheckStatus_P = 0U;     /* Referenced by: '<S58>/Parameter1' */
uint8 EAD_Sensor1v1rStatus;            /* '<S56>/Switch1' */
uint8 EAD_Sensor1v1rStatus_P = 0U;     /* Referenced by: '<S56>/Parameter3' */
float32 EAD_StandstillAccThd_P = 0.0F; /* Referenced by: '<S10>/Parameter' */
float32 EAD_StandstillSpeedThd_P = 1.0F;/* Referenced by: '<S10>/Parameter1' */
float32 EAD_SteeringAngle;             /* '<S8>/Divide1' */
float32 EAD_SysCycleTime_P = 0.01F;    /* Referenced by:
                                        * '<S49>/Constant18'
                                        * '<S8>/Parameter1'
                                        * '<S10>/DelayTime1'
                                        * '<S10>/DelayTime3'
                                        * '<S10>/Parameter2'
                                        * '<S10>/Parameter4'
                                        * '<S17>/Parameter1'
                                        * '<S17>/Parameter5'
                                        */
boolean EAD_UseTrajectory_P = 0;       /* Referenced by: '<S6>/Parameter3' */
uint8 EAD_VehControlStatus;            /* '<S57>/Switch2' */
uint8 EAD_VehControlStatus_P = 0U;     /* Referenced by: '<S57>/Parameter5' */
boolean EAD_VehFbMid2CanError_P = 0;   /* Referenced by: '<S58>/Parameter17' */
boolean EAD_VehFbMid3CanError;         /* '<S58>/OR1' */
boolean EAD_VehFbMid3CanError_P = 0;   /* Referenced by: '<S58>/Parameter12' */
boolean EAD_VehFbMid5CanError;         /* '<S58>/OR2' */
boolean EAD_VehFbMid5CanError_P = 0;   /* Referenced by: '<S58>/Parameter11' */
boolean EAD_VehFbMid6CanError;         /* '<S58>/OR' */
boolean EAD_VehFbMid6CanError_P = 0;   /* Referenced by: '<S58>/Parameter10' */
uint8 EAD_VehOperStReqVehOperStReq;    /* '<S10>/Switch19' */
uint8 EAD_VehOperStReq_P = 0U;         /* Referenced by: '<S10>/Constant30' */
uint8 TimeCntr = 0U;                   /* Referenced by: '<S75>/TaskForRadar' */
uint8 VTS_AliveCounterEscDa;           /* '<S79>/Output' */
uint8 VTS_AliveCounterFrntWhl;         /* '<S85>/Output' */
uint8 VTS_AliveCounterReWhl;           /* '<S91>/Output' */
uint8 VTS_ChecksumEscDa;               /* '<S78>/C Caller3' */
uint8 VTS_ChecksumFrntWhl;             /* '<S84>/C Caller3' */
uint8 VTS_ChecksumReWhl;               /* '<S88>/C Caller4' */
uint8 VTS_CswLateralAcce_P = 0U;       /* Referenced by: '<S106>/Parameter1' */
uint8 VTS_CswLateralSensorState_P = 0U;/* Referenced by: '<S106>/Parameter3' */
uint8 VTS_CswLongitAcce_P = 0U;        /* Referenced by: '<S113>/Parameter2' */
uint8 VTS_CswLongitSensorState_P = 0U; /* Referenced by: '<S113>/Parameter' */
uint8 VTS_CswSASCalibrationSts_P = 0U; /* Referenced by: '<S99>/Parameter7' */
uint8 VTS_CswSASFailureSts_P = 0U;     /* Referenced by: '<S99>/Parameter5' */
uint8 VTS_CswSpeedInvalidFrntLe_P = 0U;/* Referenced by: '<S82>/Parameter6' */
uint8 VTS_CswSpeedInvalidFrntRi_P = 0U;/* Referenced by: '<S83>/Parameter6' */
uint8 VTS_CswSpeedInvalidReLe_P = 0U;  /* Referenced by: '<S89>/Parameter6' */
uint8 VTS_CswSpeedInvalidReRi_P = 0U;  /* Referenced by: '<S90>/Parameter6' */
uint8 VTS_CswSteerWheelAngle_P = 0U;   /* Referenced by: '<S99>/Parameter1' */
uint8 VTS_CswSteerWheelRotSpd_P = 0U;  /* Referenced by: '<S99>/Parameter3' */
uint8 VTS_CswVehicleSpeed_P = 0U;      /* Referenced by: '<S95>/Parameter1' */
uint8 VTS_CswWheelDirectionFrntLe_P = 0U;/* Referenced by: '<S82>/Parameter4' */
uint8 VTS_CswWheelDirectionFrntRi_P = 0U;/* Referenced by: '<S83>/Parameter4' */
uint8 VTS_CswWheelDirectionReLe_P = 0U;/* Referenced by: '<S89>/Parameter4' */
uint8 VTS_CswWheelDirectionReRi_P = 0U;/* Referenced by: '<S90>/Parameter4' */
boolean VTS_CswWheelSpeedFrntLe_P = 0; /* Referenced by: '<S82>/Parameter2' */
uint8 VTS_CswWheelSpeedFrntRi_P = 0U;  /* Referenced by: '<S83>/Parameter2' */
uint8 VTS_CswWheelSpeedReLe_P = 0U;    /* Referenced by: '<S89>/Parameter2' */
uint8 VTS_CswWheelSpeedReRi_P = 0U;    /* Referenced by: '<S90>/Parameter2' */
uint8 VTS_CswYawRateSensorState_P = 0U;/* Referenced by: '<S106>/Parameter7' */
uint8 VTS_CswYawRate_P = 0U;           /* Referenced by: '<S106>/Parameter5' */
uint8 VTS_EscStatusAliveCounter;       /* '<S96>/Output' */
uint8 VTS_EscStatusChecksum;           /* '<S94>/C Caller5' */
float32 VTS_LateralAcce;               /* '<S106>/Switch2' */
float32 VTS_LateralAcce_P = 0.0F;      /* Referenced by: '<S106>/Parameter' */
uint8 VTS_LateralSensorState;          /* '<S106>/Switch3' */
uint8 VTS_LateralSensorState_P = 0U;   /* Referenced by: '<S106>/Parameter2' */
float32 VTS_LongitAcce;                /* '<S113>/Switch2' */
float32 VTS_LongitAcce_P = 0.0F;       /* Referenced by: '<S113>/Parameter3' */
uint8 VTS_LongitSensorState;           /* '<S113>/Switch1' */
uint8 VTS_LongitSensorState_P = 0U;    /* Referenced by: '<S113>/Parameter1' */
float32 VTS_PrimALatDataRawSafeNom;    /* '<S67>/Signal Conversion8' */
uint8 VTS_PrimALatDataRawSafeNomQf;    /* '<S67>/Signal Conversion9' */
float32 VTS_PrimALgtDataRawSafeNom;    /* '<S67>/Signal Conversion12' */
uint8 VTS_PrimALgtDataRawSafeNomQf;    /* '<S67>/Signal Conversion13' */
float32 VTS_PrimVehSpdGroupSafeNom;    /* '<S67>/Signal Conversion17' */
uint8 VTS_PrimVehSpdGroupSafeNomQf;    /* '<S67>/Signal Conversion18' */
float32 VTS_PrimWhlAgSpdFrntSafeLe;    /* '<S67>/Signal Conversion' */
uint8 VTS_PrimWhlAgSpdFrntSafeLeQf;    /* '<S67>/Signal Conversion1' */
float32 VTS_PrimWhlAgSpdFrntSafeRi;    /* '<S67>/Signal Conversion2' */
uint8 VTS_PrimWhlAgSpdFrntSafeRiQf;    /* '<S67>/Signal Conversion3' */
float32 VTS_PrimWhlAgSpdReSafeLe;      /* '<S67>/Signal Conversion4' */
uint8 VTS_PrimWhlAgSpdReSafeLeQf;      /* '<S67>/Signal Conversion5' */
float32 VTS_PrimWhlAgSpdReSafeRi;      /* '<S67>/Signal Conversion6' */
uint8 VTS_PrimWhlAgSpdReSafeRiQf;      /* '<S67>/Signal Conversion7' */
uint8 VTS_PrpsnTqDirAct;               /* '<S67>/Signal Conversion20' */
float32 VTS_RawLateralAcce;            /* '<S106>/Divide' */
uint8 VTS_RawLateralSensorState;       /* '<S106>/Switch' */
float32 VTS_RawLongitAcce;             /* '<S113>/Divide' */
uint8 VTS_RawLongitSensorState;        /* '<S113>/Switch' */
uint8 VTS_RawSpeedInvalidFrntLe;       /* '<S82>/Switch2' */
uint8 VTS_RawSpeedInvalidFrntRi;       /* '<S83>/Switch2' */
uint8 VTS_RawSpeedInvalidReLe;         /* '<S89>/Switch2' */
uint8 VTS_RawSpeedInvalidReRi;         /* '<S90>/Switch2' */
float32 VTS_RawSteerWheelAngle;        /* '<S99>/Product' */
float32 VTS_RawSteerWheelRotSpd;       /* '<S99>/Abs' */
float32 VTS_RawVehicleSpeed;           /* '<S95>/Product' */
uint8 VTS_RawWheelDirectionFrntLe;     /* '<S82>/Switch' */
uint8 VTS_RawWheelDirectionFrntRi;     /* '<S83>/Switch' */
uint8 VTS_RawWheelDirectionReLe;       /* '<S89>/Switch' */
uint8 VTS_RawWheelDirectionReRi;       /* '<S90>/Switch' */
float32 VTS_RawWheelSpeedFrntLe;       /* '<S82>/Abs' */
float32 VTS_RawWheelSpeedFrntRi;       /* '<S83>/Abs' */
float32 VTS_RawWheelSpeedReLe;         /* '<S89>/Abs' */
float32 VTS_RawWheelSpeedReRi;         /* '<S90>/Abs' */
float32 VTS_RawYawRate;                /* '<S106>/Product' */
uint8 VTS_RawYawRateSensorState;       /* '<S106>/Switch1' */
uint8 VTS_SASAliveCounter;             /* '<S102>/Output' */
uint8 VTS_SASCalibrationSts;           /* '<S99>/Switch4' */
uint8 VTS_SASCalibrationSts_P = 0U;    /* Referenced by: '<S99>/Parameter6' */
uint8 VTS_SASChecksum;                 /* '<S100>/C Caller2' */
uint8 VTS_SASFailureSts;               /* '<S99>/Switch3' */
uint8 VTS_SASFailureSts_P = 0U;        /* Referenced by: '<S99>/Parameter4' */
uint8 VTS_SpeedInvalidFrntLe;          /* '<S82>/Switch5' */
uint8 VTS_SpeedInvalidFrntLe_P = 0U;   /* Referenced by: '<S82>/Parameter5' */
uint8 VTS_SpeedInvalidFrntRi;          /* '<S83>/Switch5' */
uint8 VTS_SpeedInvalidFrntRi_P = 0U;   /* Referenced by: '<S83>/Parameter5' */
uint8 VTS_SpeedInvalidReLe;            /* '<S89>/Switch5' */
uint8 VTS_SpeedInvalidReLe_P = 0U;     /* Referenced by: '<S89>/Parameter5' */
uint8 VTS_SpeedInvalidReRi;            /* '<S90>/Switch5' */
uint8 VTS_SpeedInvalidReRi_P = 0U;     /* Referenced by: '<S90>/Parameter5' */
float32 VTS_SteerWheelAngle;           /* '<S99>/Switch1' */
float32 VTS_SteerWheelAngle_P = 0.0F;  /* Referenced by: '<S99>/Parameter' */
float32 VTS_SteerWheelRotSpd;          /* '<S99>/Switch2' */
float32 VTS_SteerWheelRotSpd_P = 0.0F; /* Referenced by: '<S99>/Parameter2' */
float32 VTS_SteerWhlAgSafe;            /* '<S67>/Signal Conversion14' */
float32 VTS_SteerWhlAgSpdSafe;         /* '<S67>/Signal Conversion15' */
uint8 VTS_SteerWhlSnsrQf;              /* '<S67>/Signal Conversion16' */
float32 VTS_TireRadiusFrntLe_P = 0.366F;/* Referenced by: '<S82>/Parameter' */
float32 VTS_TireRadiusFrntRi_P = 0.366F;/* Referenced by: '<S90>/Parameter' */
float32 VTS_TireRadiusReLe_P = 0.366F; /* Referenced by: '<S89>/Parameter' */
float32 VTS_TireRadiusReRi_P = 0.366F; /* Referenced by: '<S83>/Parameter' */
float32 VTS_VehicleSpeed;              /* '<S95>/Switch' */
uint8 VTS_VehicleSpeedInvalid;         /* '<S94>/Constant9' */
float32 VTS_VehicleSpeed_P = 0.0F;     /* Referenced by: '<S95>/Parameter' */
uint8 VTS_WheelDirectionFrntLe;        /* '<S82>/Switch4' */
uint8 VTS_WheelDirectionFrntLe_P = 0U; /* Referenced by: '<S82>/Parameter3' */
uint8 VTS_WheelDirectionFrntRi;        /* '<S83>/Switch4' */
uint8 VTS_WheelDirectionFrntRi_P = 0U; /* Referenced by: '<S83>/Parameter3' */
uint8 VTS_WheelDirectionReLe;          /* '<S89>/Switch4' */
uint8 VTS_WheelDirectionReLe_P = 0U;   /* Referenced by: '<S89>/Parameter3' */
uint8 VTS_WheelDirectionReRi;          /* '<S90>/Switch4' */
uint8 VTS_WheelDirectionReRi_P = 0U;   /* Referenced by: '<S90>/Parameter3' */
float32 VTS_WheelSpeedFrntLe;          /* '<S82>/Switch3' */
float32 VTS_WheelSpeedFrntLe_P = 0.0F; /* Referenced by: '<S82>/Parameter1' */
float32 VTS_WheelSpeedFrntRi;          /* '<S83>/Switch3' */
float32 VTS_WheelSpeedFrntRi_P = 0.0F; /* Referenced by: '<S83>/Parameter1' */
float32 VTS_WheelSpeedReLe;            /* '<S89>/Switch3' */
float32 VTS_WheelSpeedReLe_P = 0.0F;   /* Referenced by: '<S89>/Parameter1' */
float32 VTS_WheelSpeedReRi;            /* '<S90>/Switch3' */
float32 VTS_WheelSpeedReRi_P = 0.0F;   /* Referenced by: '<S90>/Parameter1' */
uint8 VTS_WhlLockStsLockSts;           /* '<S67>/Signal Conversion19' */
uint8 VTS_YRS1AliveCounter;            /* '<S110>/Output' */
uint8 VTS_YRS1Checksum;                /* '<S107>/C Caller' */
uint8 VTS_YRS2AliveCounter;            /* '<S116>/Output' */
uint8 VTS_YRS2Checksum;                /* '<S114>/C Caller1' */
float32 VTS_YawRate;                   /* '<S106>/Switch4' */
float32 VTS_YawRate1;                  /* '<S67>/Signal Conversion10' */
uint8 VTS_YawRate1Qf1;                 /* '<S67>/Signal Conversion11' */
uint8 VTS_YawRateSensorState;          /* '<S106>/Switch5' */
uint8 VTS_YawRateSensorState_P = 0U;   /* Referenced by: '<S106>/Parameter6' */
float32 VTS_YawRate_P = 0.0F;          /* Referenced by: '<S106>/Parameter4' */

/* Block signals (default storage) */
B_FallbackSigOutput_T FallbackSigOutput_B;

/* Block states (default storage) */
DW_FallbackSigOutput_T FallbackSigOutput_DW;

/* Model step function for TID1 */
void FallbackSigOutput_10ms_Runnable(void)
                            /* Explicit Task: FallbackSigOutput_10ms_Runnable */
{
  FBS_DebugInfo_Struct FBS_DebugInfo;
  SG_AdDirReq BusCreator9;
  SG_AdNomALgtReqGroupSafe BusCreator1;
  SG_AdPrimALgtLimReqGroupSafe BusCreator2;
  SG_AdPrimWhlAgReqGroupSafe BusCreator;
  SG_AdSecALgtLimReqGroupSafe BusCreator10;
  SG_AdSecWhlAgReqGroupSafe BusCreator4;
  SG_AdStandStillReq BusCreator8;
  SG_AdWhlLockReq BusCreator11;
  SG_AdpLiReqFromAPI BusCreator13;
  SG_AutnmsDrvStReq BusCreator3;
  SG_HmiAutnmsSts BusCreator7;
  SG_SecAdNomALgtReqGroupSafe BusCreator5;
  SG_SecAdWhlLockReq BusCreator12;
  SG_VehOperStReq BusCreator6;
  sint32 tmp_0;
  float32 rtb_Abs1;
  float32 rtb_EAD_MaxSteeringAngleRate;
  float32 rtb_Min2;
  float32 rtb_Product1_e_idx_0;
  float32 rtb_Product1_e_idx_1;
  float32 rtb_Switch;
  float32 rtb_Switch1_b;
  float32 rtb_Switch_i1_idx_0;
  float32 rtb_Switch_i1_idx_1;
  float32 rtb_Switch_j;
  float32 rtb_Switch_mg;
  float32 rtb_Switch_o;
  float32 rtb_Switch_ph;
  float32 rtb_UnitDelay_bj;
  float32 tmp_1;
  float32 tmp_10;
  float32 tmp_11;
  float32 tmp_12;
  float32 tmp_13;
  float32 tmp_14;
  float32 tmp_15;
  float32 tmp_16;
  float32 tmp_17;
  float32 tmp_18;
  float32 tmp_2;
  float32 tmp_3;
  float32 tmp_4;
  float32 tmp_5;
  float32 tmp_6;
  float32 tmp_7;
  float32 tmp_8;
  float32 tmp_9;
  float32 tmp_a;
  float32 tmp_b;
  float32 tmp_c;
  float32 tmp_d;
  float32 tmp_e;
  float32 tmp_f;
  float32 tmp_g;
  float32 tmp_h;
  float32 tmp_i;
  float32 tmp_j;
  float32 tmp_k;
  float32 tmp_l;
  float32 tmp_m;
  float32 tmp_n;
  float32 tmp_o;
  float32 tmp_p;
  float32 tmp_q;
  float32 tmp_r;
  float32 tmp_s;
  float32 tmp_t;
  float32 tmp_u;
  float32 tmp_v;
  float32 tmp_w;
  float32 tmp_x;
  float32 tmp_y;
  float32 tmp_z;
  uint32 y;
  AcuFbCanMessageID DataTypeConversion15;
  AcuMid3SsmCounter0MessageID DataTypeConversion9;
  AcuMid3SsmCounter1MessageID DataTypeConversion10;
  AcuMid5SsmCounter0MessageID DataTypeConversion11_i;
  AcuMid5SsmCounter1MessageID DataTypeConversion12;
  AcuMid6SsmCounter0MessageID DataTypeConversion13;
  AcuMid6SsmCounter1MessageID DataTypeConversion14;
  FallbackDebugInfoReserve2 DataTypeConversion84;
  FrontCameraCanMessageID DataTypeConversion16;
  FrontRadarCanMessageID DataTypeConversion17;
  ObjectStopTime DataTypeConversion88;
  uint16 rtb_SecAdNomALgtReqGroupSafePosLimForJerk;
  VehMid3SsmCounter0MessageID DataTypeConversion1_g;
  VehMid3SsmCounter1MessageID DataTypeConversion2;
  VehMid3VcuCounter0MessageID DataTypeConversion3_l;
  VehMid3VcuCounter1MessageID DataTypeConversion4_f;
  VehMid5SsmCounter0MessageID DataTypeConversion5;
  VehMid5SsmCounter1MessageID DataTypeConversion6_py;
  VehMid6SsmCounter0MessageID DataTypeConversion7;
  VehMid6SsmCounter1MessageID DataTypeConversion8;
  uint16 UnitDelay_DSTATE_fb;
  FallbackTriggerStatus DataTypeConversion101;
  FrontCameraCalibrationStatus DataTypeConversion36;
  FrontRadarCalibrationStatus DataTypeConversion97;
  LaneValidState DataTypeConversion100;
  LateralSystemState DataTypeConversion59;
  UDcDcAvlLoSideExt DataTypeConversion39_h;
  VehUsgStReq DataTypeConversion44_e;
  sint8 tmp[11];
  sint8 rtb_LessThan_0[8];
  uint8 rtb_BitwiseAND1;
  uint8 rtb_DataTypeConversion63;
  uint8 rtb_FixPtSum1_a;
  uint8 rtb_FixPtSwitch;
  uint8 rtb_Switch7;
  uint8 s59_iter;
  uint8 s65_iter;
  AcuFbCanTimeout DataTypeConversion35_l;
  AcuMid3SsmCounter0Timeout DataTypeConversion26_k;
  AcuMid3SsmCounter1Timeout DataTypeConversion27_g;
  AcuMid5SsmCounter0Timeout DataTypeConversion28;
  AcuMid5SsmCounter1Timeout DataTypeConversion29;
  AcuMid6SsmCounter0Timeout DataTypeConversion30_l;
  AcuMid6SsmCounter1Timeout DataTypeConversion31;
  VehMid3VcuCounter0Timeout DataTypeConversion20_h;
  VehMid3VcuCounter1Timeout DataTypeConversion21_p;
  VehMid5SsmCounter0Timeout DataTypeConversion22_k;
  VehMid5SsmCounter1Timeout DataTypeConversion23;
  VehMid6SsmCounter0Timeout DataTypeConversion24_b;
  VehMid6SsmCounter1Timeout DataTypeConversion25_h;
  boolean rtb_AND1;
  boolean rtb_AND2;
  boolean rtb_AND_m;
  boolean rtb_AND_n;
  boolean rtb_AND_pv;
  boolean rtb_AdpLiReqFromAPIHzrdLiDeactnReq;
  boolean rtb_AdpLiReqFromAPIIncrLiRiReq;
  boolean rtb_AdpLiReqFromAPIIndcrLeReq;
  boolean rtb_Equal1_m;
  boolean rtb_Equal2_b;
  boolean rtb_Equal3;
  boolean rtb_LessThan;
  boolean rtb_MultiportSwitch1;
  boolean rtb_NOT1_a;

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* UnitDelay: '<S20>/Unit Delay' incorporates:
   *  Constant: '<S8>/Constant10'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Lookup_n-D: '<S8>/1-D Lookup Table'
   *  Product: '<S8>/Product3'
   */
  rtb_UnitDelay_bj =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
    ->VSI_LongitudinalVelocity * FallbackSigOutput_P.Constant10_Value_hs;
  rtb_UnitDelay_bj = look1_iflf_binlcapw(rtb_UnitDelay_bj,
    FallbackSigOutput_P.uDLookupTable_bp01Data, (&(EAD_MaxFrontSteerAngleRate_M
    [0])), 9U);

  /* DataTypeConversion: '<S7>/Data Type Conversion29' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   *  S-Function (sfix_bitop): '<S8>/Bitwise Operator5'
   */
  rtb_SecAdNomALgtReqGroupSafePosLimForJerk = (uint16)
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
     ->LAT_SystemStateOut & FallbackSigOutput_P.BitwiseOperator5_BitMask);

  /* Switch: '<S8>/Switch3' incorporates:
   *  Constant: '<S8>/Constant11'
   *  RelationalOperator: '<S8>/Equal5'
   */
  if (rtb_SecAdNomALgtReqGroupSafePosLimForJerk !=
      FallbackSigOutput_P.Constant11_Value_j) {
    /* Abs: '<S46>/Abs1' incorporates:
     *  Constant: '<S8>/Constant13'
     */
    rtb_Abs1 = FallbackSigOutput_P.Constant13_Value;
  } else {
    /* S-Function (sfix_bitop): '<S8>/Bitwise Operator1' incorporates:
     *  Inport: '<Root>/LAT_CtrlCmd'
     */
    rtb_SecAdNomALgtReqGroupSafePosLimForJerk = (uint16)
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
       ->LAT_SystemStateOut & FallbackSigOutput_P.BitwiseOperator1_BitMask);

    /* Switch: '<S8>/Switch5' incorporates:
     *  Constant: '<S8>/Constant15'
     *  RelationalOperator: '<S8>/Equal1'
     */
    if (rtb_SecAdNomALgtReqGroupSafePosLimForJerk !=
        FallbackSigOutput_P.Constant15_Value_a) {
      /* Abs: '<S46>/Abs1' incorporates:
       *  Constant: '<S8>/Constant14'
       */
      rtb_Abs1 = FallbackSigOutput_P.Constant14_Value;
    } else {
      /* Abs: '<S46>/Abs1' incorporates:
       *  Constant: '<S8>/Constant16'
       */
      rtb_Abs1 = FallbackSigOutput_P.Constant16_Value;
    }

    /* End of Switch: '<S8>/Switch5' */
  }

  /* End of Switch: '<S8>/Switch3' */

  /* Product: '<S8>/Product4' */
  rtb_EAD_MaxSteeringAngleRate = rtb_UnitDelay_bj * rtb_Abs1;

  /* Switch: '<S8>/Switch' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Switch: '<S6>/Switch'
   */
  if ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
       ())->VSI_FallbackActive) {
    /* Switch: '<S8>/Switch' incorporates:
     *  Inport: '<Root>/LAT_CtrlCmd'
     */
    rtb_Switch =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
      ->LAT_FrontWheelSteerAngle;

    /* Switch: '<S6>/Switch' incorporates:
     *  UnitDelay: '<S6>/Unit Delay'
     */
    rtb_Equal1_m = FallbackSigOutput_DW.UnitDelay_DSTATE_h;
  } else {
    /* Switch: '<S8>/Switch' */
    rtb_Switch =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
       ())->VSI_FrontWheelSteeringAngle;

    /* RelationalOperator: '<S6>/Equal1' incorporates:
     *  Constant: '<S6>/Constant14'
     *  Inport: '<Root>/LAT_CtrlCmd'
     */
    rtb_Equal1_m =
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
       ->LAT_SystemStateOut != FallbackSigOutput_P.Constant14_Value_l4);

    /* Switch: '<S6>/Switch' incorporates:
     *  Constant: '<S6>/Parameter3'
     *  Logic: '<S6>/AND'
     */
    rtb_Equal1_m = (rtb_Equal1_m && EAD_UseTrajectory_P);
  }

  /* End of Switch: '<S8>/Switch' */

  /* Product: '<S8>/Divide1' incorporates:
   *  Constant: '<S8>/Constant1'
   *  Constant: '<S8>/Constant2'
   *  Product: '<S8>/Product1'
   */
  EAD_SteeringAngle = rtb_Switch * FallbackSigOutput_P.Constant2_Value_d /
    FallbackSigOutput_P.Constant1_Value_m;

  /* Switch: '<S11>/Switch' incorporates:
   *  Constant: '<S8>/Parameter'
   */
  if (EAD_CswSteerAngleRate_P) {
    /* Switch: '<S8>/Switch1' incorporates:
     *  Constant: '<S8>/Parameter7'
     */
    if (EAD_ChooseSteerAngleRateLimit_P) {
      /* Switch: '<S8>/Switch1' incorporates:
       *  Constant: '<S8>/Parameter2'
       */
      rtb_Switch1_b = EAD_MaxSteerAngleRate_P;
    } else {
      /* Switch: '<S8>/Switch1' incorporates:
       *  Constant: '<S8>/Constant4'
       *  Constant: '<S8>/Constant6'
       *  Product: '<S8>/Divide'
       *  Product: '<S8>/Product'
       */
      rtb_Switch1_b = rtb_EAD_MaxSteeringAngleRate *
        FallbackSigOutput_P.Constant6_Value /
        FallbackSigOutput_P.Constant4_Value;
    }

    /* End of Switch: '<S8>/Switch1' */

    /* Sum: '<S11>/Sum' incorporates:
     *  UnitDelay: '<S11>/Unit Delay'
     */
    rtb_Switch -= FallbackSigOutput_DW.UnitDelay_DSTATE;

    /* Switch: '<S14>/Switch2' incorporates:
     *  Constant: '<S8>/Parameter1'
     *  Product: '<S11>/Product'
     */
    rtb_Abs1 = rtb_Switch1_b * EAD_SysCycleTime_P;

    /* Switch: '<S14>/Switch2' incorporates:
     *  RelationalOperator: '<S14>/LowerRelop1'
     */
    if (rtb_Switch <= rtb_Abs1) {
      /* Product: '<S11>/Product1' incorporates:
       *  Constant: '<S8>/Parameter1'
       *  UnaryMinus: '<S8>/Unary Minus'
       */
      rtb_Switch1_b = -rtb_Switch1_b * EAD_SysCycleTime_P;

      /* Switch: '<S14>/Switch' incorporates:
       *  RelationalOperator: '<S14>/UpperRelop'
       */
      if (rtb_Switch < rtb_Switch1_b) {
        /* Switch: '<S14>/Switch2' */
        rtb_Abs1 = rtb_Switch1_b;
      } else {
        /* Switch: '<S14>/Switch2' */
        rtb_Abs1 = rtb_Switch;
      }

      /* End of Switch: '<S14>/Switch' */
    }

    /* End of Switch: '<S14>/Switch2' */

    /* Switch: '<S11>/Switch' incorporates:
     *  Sum: '<S11>/Difference Inputs3'
     *  UnitDelay: '<S11>/Unit Delay'
     *
     * Block description for '<S11>/Difference Inputs3':
     *
     *  Add in CPU
     */
    rtb_Switch = rtb_Abs1 + FallbackSigOutput_DW.UnitDelay_DSTATE;
  }

  /* End of Switch: '<S11>/Switch' */

  /* Switch: '<S8>/Switch6' incorporates:
   *  Constant: '<S8>/Parameter3'
   *  Switch: '<S8>/Switch4'
   */
  if (EAD_OnlyUseTrajectory_P) {
    /* Switch: '<S8>/Switch6' incorporates:
     *  Inport: '<Root>/ControlCommand'
     */
    rtb_Switch1_b =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_ControlCommand_ControlCommand())
      ->wheel_angle;
  } else if (rtb_Equal1_m) {
    /* Switch: '<S8>/Switch4' incorporates:
     *  Inport: '<Root>/ControlCommand'
     *  Switch: '<S8>/Switch6'
     */
    rtb_Switch1_b =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_ControlCommand_ControlCommand())
      ->wheel_angle;
  } else {
    /* Switch: '<S8>/Switch6' incorporates:
     *  Switch: '<S8>/Switch4'
     */
    rtb_Switch1_b = rtb_Switch;
  }

  /* End of Switch: '<S8>/Switch6' */

  /* Product: '<S8>/Divide2' incorporates:
   *  Constant: '<S8>/Constant3'
   *  Constant: '<S8>/Constant5'
   *  Product: '<S8>/Product2'
   */
  EAD_LimitSteeringAngle = rtb_Switch1_b * FallbackSigOutput_P.Constant5_Value_c
    / FallbackSigOutput_P.Constant3_Value_dk;

  /* Switch: '<S12>/Switch2' incorporates:
   *  Constant: '<S8>/Constant48'
   *  Constant: '<S8>/Constant49'
   *  RelationalOperator: '<S12>/LowerRelop1'
   *  RelationalOperator: '<S12>/UpperRelop'
   *  Switch: '<S12>/Switch'
   */
  if (rtb_Switch1_b > FallbackSigOutput_P.Constant48_Value) {
    /* Switch: '<S12>/Switch2' */
    EAD_AdSecWhlAgReqGroupSafeWhlAgReq = FallbackSigOutput_P.Constant48_Value;
  } else if (rtb_Switch1_b < FallbackSigOutput_P.Constant49_Value) {
    /* Switch: '<S12>/Switch' incorporates:
     *  Constant: '<S8>/Constant49'
     *  Switch: '<S12>/Switch2'
     */
    EAD_AdSecWhlAgReqGroupSafeWhlAgReq = FallbackSigOutput_P.Constant49_Value;
  } else {
    /* Switch: '<S12>/Switch2' incorporates:
     *  Switch: '<S12>/Switch'
     */
    EAD_AdSecWhlAgReqGroupSafeWhlAgReq = rtb_Switch1_b;
  }

  /* End of Switch: '<S12>/Switch2' */

  /* Switch: '<S13>/Switch2' incorporates:
   *  Constant: '<S8>/Constant50'
   *  Constant: '<S8>/Constant51'
   *  RelationalOperator: '<S13>/LowerRelop1'
   *  RelationalOperator: '<S13>/UpperRelop'
   *  Switch: '<S13>/Switch'
   */
  if (rtb_Switch1_b > FallbackSigOutput_P.Constant50_Value) {
    /* Switch: '<S13>/Switch2' */
    EAD_AdPrimWhlAgReqGroupSafeWhlAgReq = FallbackSigOutput_P.Constant50_Value;
  } else if (rtb_Switch1_b < FallbackSigOutput_P.Constant51_Value) {
    /* Switch: '<S13>/Switch' incorporates:
     *  Constant: '<S8>/Constant51'
     *  Switch: '<S13>/Switch2'
     */
    EAD_AdPrimWhlAgReqGroupSafeWhlAgReq = FallbackSigOutput_P.Constant51_Value;
  } else {
    /* Switch: '<S13>/Switch2' incorporates:
     *  Switch: '<S13>/Switch'
     */
    EAD_AdPrimWhlAgReqGroupSafeWhlAgReq = rtb_Switch1_b;
  }

  /* End of Switch: '<S13>/Switch2' */

  /* Abs: '<S46>/Abs1' incorporates:
   *  Abs: '<S17>/Abs'
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  rtb_Abs1 = fabsf
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_LongitudinalVelocity);

  /* DataTypeConversion: '<S7>/Data Type Conversion55' incorporates:
   *  Constant: '<S17>/Constant9'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S17>/Equal1'
   */
  rtb_AdpLiReqFromAPIIndcrLeReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackMode == FallbackSigOutput_P.Constant9_Value_bf);

  /* DataTypeConversion: '<S7>/Data Type Conversion54' incorporates:
   *  Constant: '<S17>/Constant5'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S17>/Equal2'
   */
  rtb_AdpLiReqFromAPIIncrLiRiReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackMode == FallbackSigOutput_P.Constant5_Value_cx);

  /* DataTypeConversion: '<S7>/Data Type Conversion53' incorporates:
   *  Constant: '<S17>/Constant13'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S17>/Equal6'
   */
  rtb_AdpLiReqFromAPIHzrdLiDeactnReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackMode == FallbackSigOutput_P.Constant13_Value_a);

  /* Switch: '<S21>/Switch' incorporates:
   *  Constant: '<S17>/Constant18'
   *  RelationalOperator: '<S17>/Less Than'
   */
  if (rtb_Abs1 < FallbackSigOutput_P.Constant18_Value) {
    /* Switch: '<S21>/Switch' incorporates:
     *  Constant: '<S17>/Constant20'
     *  Sum: '<S21>/Add'
     *  UnitDelay: '<S21>/Unit Delay'
     */
    rtb_Switch1_b = FallbackSigOutput_P.Constant20_Value +
      FallbackSigOutput_DW.UnitDelay_DSTATE_k;

    /* MultiPortSwitch: '<S24>/Multiport Switch2' incorporates:
     *  Constant: '<S17>/Constant19'
     *  MultiPortSwitch: '<S24>/Multiport Switch1'
     *  RelationalOperator: '<S21>/GreaterThan'
     */
    if (rtb_Switch1_b < FallbackSigOutput_P.Constant19_Value) {
      /* MultiPortSwitch: '<S24>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S24>/Multiport Switch2'
       *  UnitDelay: '<S24>/Unit Delay'
       */
      rtb_MultiportSwitch1 = FallbackSigOutput_DW.UnitDelay_DSTATE_iy;
    } else {
      /* MultiPortSwitch: '<S24>/Multiport Switch1' incorporates:
       *  Constant: '<S24>/Constant7'
       *  MultiPortSwitch: '<S24>/Multiport Switch2'
       */
      rtb_MultiportSwitch1 = FallbackSigOutput_P.Constant7_Value_d;
    }

    /* End of MultiPortSwitch: '<S24>/Multiport Switch2' */
  } else {
    /* Switch: '<S21>/Switch' incorporates:
     *  Constant: '<S21>/IAM_Ts_P2'
     */
    rtb_Switch1_b = FallbackSigOutput_P.IAM_Ts_P2_Value;

    /* MultiPortSwitch: '<S24>/Multiport Switch1' incorporates:
     *  Constant: '<S24>/Constant6'
     */
    rtb_MultiportSwitch1 = FallbackSigOutput_P.Constant6_Value_l;
  }

  /* End of Switch: '<S21>/Switch' */

  /* DataTypeConversion: '<S7>/Data Type Conversion53' incorporates:
   *  Logic: '<S17>/OR3'
   */
  rtb_AdpLiReqFromAPIHzrdLiDeactnReq = (rtb_AdpLiReqFromAPIHzrdLiDeactnReq ||
    rtb_AdpLiReqFromAPIIndcrLeReq);

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S18>/Constant15'
   *  Inport: '<Root>/LAT_CtrlCmd'
   *  RelationalOperator: '<S18>/Equal7'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
     ->LAT_SystemStateOut != FallbackSigOutput_P.Constant15_Value_h);

  /* Logic: '<S18>/AND3' incorporates:
   *  Logic: '<S18>/NOT1'
   */
  rtb_AdpLiReqFromAPIIndcrLeReq = (rtb_LessThan && (!rtb_Equal1_m));

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S18>/Constant16'
   *  Inport: '<Root>/LGT_CtrlCmd'
   *  RelationalOperator: '<S18>/Equal8'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
     ->LGT_SystemState == FallbackSigOutput_P.Constant16_Value_m);

  /* Switch: '<S18>/Switch' incorporates:
   *  Logic: '<S18>/OR'
   */
  if (rtb_AdpLiReqFromAPIIndcrLeReq || rtb_LessThan) {
    /* Sum: '<S54>/FixPt Sum1' incorporates:
     *  Constant: '<S18>/Constant14'
     */
    rtb_FixPtSum1_a = FallbackSigOutput_P.Constant14_Value_m;
  } else {
    /* Sum: '<S54>/FixPt Sum1' incorporates:
     *  Inport: '<Root>/LGT_CtrlCmd'
     */
    rtb_FixPtSum1_a =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
      ->LGT_SystemState;
  }

  /* End of Switch: '<S18>/Switch' */

  /* RelationalOperator: '<S18>/Equal1' incorporates:
   *  Constant: '<S18>/Constant8'
   */
  rtb_AdpLiReqFromAPIIndcrLeReq = (rtb_FixPtSum1_a ==
    FallbackSigOutput_P.Constant8_Value_n);

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Logic: '<S18>/AND'
   *  Logic: '<S18>/AND1'
   *  Logic: '<S18>/NOT'
   */
  rtb_LessThan =
    !(Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
    ->VSI_RearCollisionWarning;
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_ForwardCollisionWarning || (rtb_AdpLiReqFromAPIIndcrLeReq &&
      rtb_LessThan));

  /* Switch: '<S18>/Switch8' */
  if (rtb_LessThan) {
    /* SignalConversion: '<S18>/Signal Conversion' incorporates:
     *  Constant: '<S18>/Constant17'
     *  Product: '<S49>/Product1'
     */
    EAD_RawAccRequest = FallbackSigOutput_P.Constant17_Value;

    /* SignalConversion: '<S18>/Signal Conversion1' incorporates:
     *  Constant: '<S18>/Constant1'
     *  Product: '<S49>/Product1'
     */
    EAD_RawJerkRequest = FallbackSigOutput_P.Constant1_Value;
  } else {
    /* Logic: '<S18>/AND2' incorporates:
     *  Inport: '<Root>/VSI_VehicleInfo'
     */
    rtb_AdpLiReqFromAPIIndcrLeReq = (rtb_AdpLiReqFromAPIIndcrLeReq &&
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
       ())->VSI_RearCollisionWarning);

    /* Switch: '<S18>/Switch1' incorporates:
     *  Inport: '<Root>/VSI_VehicleInfo'
     *  Switch: '<S18>/Switch2'
     *  Switch: '<S18>/Switch3'
     */
    if (rtb_AdpLiReqFromAPIIndcrLeReq) {
      /* SignalConversion: '<S18>/Signal Conversion' incorporates:
       *  Constant: '<S18>/Constant3'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch1'
       */
      EAD_RawAccRequest = FallbackSigOutput_P.Constant3_Value_c;

      /* SignalConversion: '<S18>/Signal Conversion1' incorporates:
       *  Constant: '<S18>/Constant2'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch1'
       */
      EAD_RawJerkRequest = FallbackSigOutput_P.Constant2_Value;
    } else if
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
          ())->VSI_RearCollisionWarning) {
      /* SignalConversion: '<S18>/Signal Conversion' incorporates:
       *  Constant: '<S18>/Constant4'
       *  Inport: '<Root>/LGT_CtrlCmd'
       *  MinMax: '<S18>/Min'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch2'
       */
      EAD_RawAccRequest = fmaxf(FallbackSigOutput_P.Constant4_Value_j,
        (Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
        ->LGT_AccRequest);

      /* SignalConversion: '<S18>/Signal Conversion1' incorporates:
       *  Constant: '<S18>/Constant5'
       *  Inport: '<Root>/LGT_CtrlCmd'
       *  MinMax: '<S18>/Min'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch2'
       */
      EAD_RawJerkRequest = fmaxf(FallbackSigOutput_P.Constant5_Value,
        (Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
        ->LGT_JerkRequest);
    } else if (rtb_Equal1_m) {
      /* SignalConversion: '<S18>/Signal Conversion' incorporates:
       *  Inport: '<Root>/ControlCommand'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch2'
       *  Switch: '<S18>/Switch3'
       */
      EAD_RawAccRequest =
        (Rte_IRead_FallbackSigOutput_10ms_Runnable_ControlCommand_ControlCommand
         ())->acceleration;

      /* SignalConversion: '<S18>/Signal Conversion1' incorporates:
       *  Constant: '<S18>/Constant9'
       *  Product: '<S49>/Product1'
       *  Switch: '<S18>/Switch2'
       *  Switch: '<S18>/Switch3'
       */
      EAD_RawJerkRequest = FallbackSigOutput_P.Constant9_Value;
    } else {
      /* SignalConversion: '<S18>/Signal Conversion' incorporates:
       *  Constant: '<S18>/Constant6'
       *  Inport: '<Root>/LGT_CtrlCmd'
       *  MinMax: '<S18>/Min1'
       *  Switch: '<S18>/Switch2'
       *  Switch: '<S18>/Switch3'
       */
      EAD_RawAccRequest = fminf
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
         ->LGT_AccRequest, FallbackSigOutput_P.Constant6_Value_h);

      /* SignalConversion: '<S18>/Signal Conversion1' incorporates:
       *  Constant: '<S18>/Constant7'
       *  Inport: '<Root>/LGT_CtrlCmd'
       *  MinMax: '<S18>/Min2'
       *  Switch: '<S18>/Switch2'
       *  Switch: '<S18>/Switch3'
       */
      EAD_RawJerkRequest = fmaxf
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
         ->LGT_JerkRequest, FallbackSigOutput_P.Constant7_Value_m);
    }

    /* End of Switch: '<S18>/Switch1' */
  }

  /* End of Switch: '<S18>/Switch8' */

  /* Switch: '<S17>/Switch3' */
  if (rtb_AdpLiReqFromAPIIncrLiRiReq) {
    /* Switch: '<S17>/Switch3' */
    rtb_Product1_e_idx_0 = EAD_RawAccRequest;
    rtb_Product1_e_idx_1 = EAD_RawJerkRequest;
  } else {
    /* Switch: '<S17>/Switch3' incorporates:
     *  Constant: '<S17>/Constant8'
     *  MinMax: '<S17>/Min2'
     */
    rtb_Product1_e_idx_0 = EAD_RawAccRequest;
    rtb_Product1_e_idx_1 = fmaxf(EAD_RawJerkRequest,
      FallbackSigOutput_P.Constant8_Value);
  }

  /* End of Switch: '<S17>/Switch3' */

  /* Switch: '<S17>/Switch4' */
  if (rtb_AdpLiReqFromAPIHzrdLiDeactnReq) {
    /* Switch: '<S17>/Switch4' incorporates:
     *  Constant: '<S17>/Constant6'
     *  Constant: '<S17>/Constant7'
     */
    rtb_Product1_e_idx_0 = FallbackSigOutput_P.Constant6_Value_c;
    rtb_Product1_e_idx_1 = FallbackSigOutput_P.Constant7_Value;
  } else {
    /* RelationalOperator: '<S17>/Equal3' incorporates:
     *  Constant: '<S17>/Constant2'
     *  Inport: '<Root>/VSI_VehicleInfo'
     */
    rtb_AdpLiReqFromAPIIncrLiRiReq =
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
        ())->VSI_FallbackMode == FallbackSigOutput_P.Constant2_Value_j);

    /* Switch: '<S17>/Switch1' incorporates:
     *  Constant: '<S17>/Constant11'
     *  Constant: '<S17>/Constant14'
     *  MinMax: '<S17>/Min1'
     *  Switch: '<S17>/Switch4'
     */
    if (rtb_AdpLiReqFromAPIIncrLiRiReq) {
      rtb_Product1_e_idx_0 = fminf(FallbackSigOutput_P.Constant11_Value,
        rtb_Product1_e_idx_0);
      rtb_Product1_e_idx_1 = fminf(FallbackSigOutput_P.Constant14_Value_l,
        rtb_Product1_e_idx_1);
    } else {
      /* RelationalOperator: '<S17>/Equal10' incorporates:
       *  Constant: '<S17>/Constant1'
       *  Inport: '<Root>/VSI_VehicleInfo'
       */
      rtb_AdpLiReqFromAPIIncrLiRiReq =
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
          ())->VSI_FallbackMode == FallbackSigOutput_P.Constant1_Value_c);

      /* Switch: '<S17>/Switch2' incorporates:
       *  Constant: '<S17>/Constant3'
       *  Constant: '<S17>/Constant4'
       *  MinMax: '<S17>/Min'
       *  Switch: '<S17>/Switch4'
       */
      if (rtb_AdpLiReqFromAPIIncrLiRiReq) {
        rtb_Product1_e_idx_0 = fminf(FallbackSigOutput_P.Constant3_Value,
          rtb_Product1_e_idx_0);
        rtb_Product1_e_idx_1 = fminf(FallbackSigOutput_P.Constant4_Value_b,
          rtb_Product1_e_idx_1);
      }

      /* End of Switch: '<S17>/Switch2' */
    }

    /* End of Switch: '<S17>/Switch1' */
  }

  /* End of Switch: '<S17>/Switch4' */

  /* Product: '<S20>/Divide' incorporates:
   *  Constant: '<S17>/Parameter5'
   *  Constant: '<S17>/Parameter6'
   *  MinMax: '<S20>/Max1'
   */
  rtb_Abs1 = EAD_SysCycleTime_P / fmaxf(EAD_SysCycleTime_P,
    FallbackSigOutput_P.EAD_AccFilterTime_P);

  /* Switch: '<S23>/Switch2' incorporates:
   *  Constant: '<S20>/IAM_Ts_P1'
   *  Constant: '<S20>/IAM_Ts_P4'
   *  RelationalOperator: '<S23>/LowerRelop1'
   *  RelationalOperator: '<S23>/UpperRelop'
   *  Switch: '<S23>/Switch'
   */
  if (rtb_Abs1 > FallbackSigOutput_P.IAM_Ts_P4_Value) {
    /* Abs: '<S46>/Abs1' */
    rtb_Abs1 = FallbackSigOutput_P.IAM_Ts_P4_Value;
  } else if (rtb_Abs1 < FallbackSigOutput_P.IAM_Ts_P1_Value) {
    /* Switch: '<S23>/Switch' incorporates:
     *  Abs: '<S46>/Abs1'
     *  Constant: '<S20>/IAM_Ts_P1'
     */
    rtb_Abs1 = FallbackSigOutput_P.IAM_Ts_P1_Value;
  }

  /* End of Switch: '<S23>/Switch2' */

  /* UnitDelay: '<S20>/Unit Delay' */
  rtb_UnitDelay_bj = FallbackSigOutput_DW.UnitDelay_DSTATE_l;

  /* Switch: '<S57>/Switch8' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   *  Sum: '<S20>/Subtract'
   */
  rtb_Min2 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimALgtDataRawSafeNom - rtb_UnitDelay_bj;

  /* Sum: '<S20>/Add' incorporates:
   *  Product: '<S20>/Product'
   */
  EAD_FilteredActualVehAcc = rtb_Abs1 * rtb_Min2 + rtb_UnitDelay_bj;

  /* Switch: '<S17>/Switch7' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  if ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
       ())->VSI_FallbackActive) {
    /* Switch: '<S17>/Switch' */
    if (rtb_MultiportSwitch1) {
      /* Switch: '<S17>/Switch7' incorporates:
       *  Constant: '<S17>/Constant10'
       */
      rtb_Min2 = FallbackSigOutput_P.Constant10_Value;
    } else {
      /* Switch: '<S17>/Switch7' */
      rtb_Min2 = rtb_Product1_e_idx_0;
    }

    /* End of Switch: '<S17>/Switch' */
  } else {
    /* Switch: '<S17>/Switch7' */
    rtb_Min2 = EAD_FilteredActualVehAcc;
  }

  /* End of Switch: '<S17>/Switch7' */

  /* UnitDelay: '<S19>/Unit Delay' */
  rtb_Abs1 = FallbackSigOutput_DW.UnitDelay_DSTATE_ki;

  /* SignalConversion: '<S17>/Signal Copy' */
  EAD_JerkRequest = rtb_Product1_e_idx_1;

  /* Switch: '<S19>/Switch' incorporates:
   *  Constant: '<S17>/Parameter'
   */
  if (EAD_CswAccRateLmt_P) {
    /* Sum: '<S19>/Sum' */
    rtb_Min2 -= rtb_Abs1;

    /* Switch: '<S17>/Switch5' incorporates:
     *  Constant: '<S17>/Constant'
     *  Constant: '<S17>/Parameter2'
     */
    if (rtb_Equal1_m) {
      rtb_UnitDelay_bj = FallbackSigOutput_P.Constant_Value;
    } else {
      rtb_UnitDelay_bj = EAD_MaxAccRate_P;
    }

    /* End of Switch: '<S17>/Switch5' */

    /* Switch: '<S22>/Switch2' incorporates:
     *  Constant: '<S17>/Parameter1'
     *  Product: '<S19>/Product'
     */
    rtb_UnitDelay_bj *= EAD_SysCycleTime_P;

    /* Switch: '<S22>/Switch2' incorporates:
     *  RelationalOperator: '<S22>/LowerRelop1'
     */
    if (rtb_Min2 <= rtb_UnitDelay_bj) {
      /* Product: '<S19>/Product1' incorporates:
       *  Constant: '<S17>/Parameter1'
       */
      rtb_UnitDelay_bj = EAD_JerkRequest * EAD_SysCycleTime_P;

      /* Switch: '<S22>/Switch' incorporates:
       *  RelationalOperator: '<S22>/UpperRelop'
       */
      if (rtb_Min2 >= rtb_UnitDelay_bj) {
        /* Switch: '<S22>/Switch2' */
        rtb_UnitDelay_bj = rtb_Min2;
      }

      /* End of Switch: '<S22>/Switch' */
    }

    /* End of Switch: '<S22>/Switch2' */

    /* Switch: '<S19>/Switch' incorporates:
     *  Sum: '<S19>/Difference Inputs3'
     *
     * Block description for '<S19>/Difference Inputs3':
     *
     *  Add in CPU
     */
    EAD_AccRequest = rtb_UnitDelay_bj + rtb_Abs1;
  } else {
    /* Switch: '<S19>/Switch' */
    EAD_AccRequest = rtb_Min2;
  }

  /* End of Switch: '<S19>/Switch' */

  /* Switch: '<S16>/Switch' incorporates:
   *  Constant: '<S16>/Parameter1'
   */
  if (EAD_OnlyUseTrajectory_P) {
    /* Switch: '<S16>/Switch' incorporates:
     *  Inport: '<Root>/ControlCommand'
     */
    rtb_Abs1 =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_ControlCommand_ControlCommand())
      ->acceleration;
  } else {
    /* Switch: '<S16>/Switch' */
    rtb_Abs1 = EAD_AccRequest;
  }

  /* End of Switch: '<S16>/Switch' */

  /* Switch: '<S27>/Switch2' incorporates:
   *  Constant: '<S16>/Constant52'
   *  Constant: '<S16>/Constant53'
   *  RelationalOperator: '<S27>/LowerRelop1'
   *  RelationalOperator: '<S27>/UpperRelop'
   *  Switch: '<S27>/Switch'
   */
  if (rtb_Abs1 > FallbackSigOutput_P.Constant52_Value) {
    /* Switch: '<S27>/Switch2' */
    EAD_LmtAccRequest = FallbackSigOutput_P.Constant52_Value;
  } else if (rtb_Abs1 < FallbackSigOutput_P.Constant53_Value) {
    /* Switch: '<S27>/Switch' incorporates:
     *  Constant: '<S16>/Constant53'
     *  Switch: '<S27>/Switch2'
     */
    EAD_LmtAccRequest = FallbackSigOutput_P.Constant53_Value;
  } else {
    /* Switch: '<S27>/Switch2' incorporates:
     *  Switch: '<S27>/Switch'
     */
    EAD_LmtAccRequest = rtb_Abs1;
  }

  /* End of Switch: '<S27>/Switch2' */

  /* SignalConversion: '<S16>/Signal Conversion' */
  EAD_AdNomALgtReqGroupSafeALgtNomReqMax = EAD_LmtAccRequest;

  /* SignalConversion: '<S16>/Signal Conversion1' */
  EAD_AdNomALgtReqGroupSafeALgtNomReqMin = EAD_LmtAccRequest;

  /* SignalConversion: '<S16>/Signal Conversion2' */
  EAD_SecAdNomALgtReqGroupSafeALgtNomReqMax = EAD_LmtAccRequest;

  /* SignalConversion: '<S16>/Signal Conversion3' */
  EAD_SecAdNomALgtReqGroupSafeALgtNomReqMin = EAD_LmtAccRequest;

  /* Switch: '<S29>/Switch2' incorporates:
   *  Constant: '<S16>/Constant2'
   *  Constant: '<S16>/Constant58'
   *  Constant: '<S16>/Constant59'
   *  RelationalOperator: '<S29>/LowerRelop1'
   *  RelationalOperator: '<S29>/UpperRelop'
   *  Switch: '<S29>/Switch'
   */
  if (FallbackSigOutput_P.Constant2_Value_g >
      FallbackSigOutput_P.Constant58_Value) {
    /* Switch: '<S29>/Switch2' */
    EAD_LmtMinJerkRequest = FallbackSigOutput_P.Constant58_Value;
  } else if (FallbackSigOutput_P.Constant2_Value_g <
             FallbackSigOutput_P.Constant59_Value) {
    /* Switch: '<S29>/Switch' incorporates:
     *  Constant: '<S16>/Constant59'
     *  Switch: '<S29>/Switch2'
     */
    EAD_LmtMinJerkRequest = FallbackSigOutput_P.Constant59_Value;
  } else {
    /* Switch: '<S29>/Switch2' */
    EAD_LmtMinJerkRequest = FallbackSigOutput_P.Constant2_Value_g;
  }

  /* End of Switch: '<S29>/Switch2' */

  /* SignalConversion: '<S16>/Signal Conversion8' */
  EAD_SecAdNomALgtReqGroupSafeNegLimForJerk = EAD_LmtMinJerkRequest;

  /* SignalConversion: '<S16>/Signal Conversion9' */
  EAD_AdNomALgtReqGroupSafeNegLimForJerk = EAD_LmtMinJerkRequest;

  /* Switch: '<S28>/Switch2' incorporates:
   *  Constant: '<S16>/Constant3'
   *  Constant: '<S16>/Constant56'
   *  Constant: '<S16>/Constant57'
   *  RelationalOperator: '<S28>/LowerRelop1'
   *  RelationalOperator: '<S28>/UpperRelop'
   *  Switch: '<S28>/Switch'
   */
  if (FallbackSigOutput_P.Constant3_Value_b >
      FallbackSigOutput_P.Constant56_Value) {
    /* Switch: '<S28>/Switch2' */
    EAD_LmtMaxJerkRequest = FallbackSigOutput_P.Constant56_Value;
  } else if (FallbackSigOutput_P.Constant3_Value_b <
             FallbackSigOutput_P.Constant57_Value) {
    /* Switch: '<S28>/Switch' incorporates:
     *  Constant: '<S16>/Constant57'
     *  Switch: '<S28>/Switch2'
     */
    EAD_LmtMaxJerkRequest = FallbackSigOutput_P.Constant57_Value;
  } else {
    /* Switch: '<S28>/Switch2' */
    EAD_LmtMaxJerkRequest = FallbackSigOutput_P.Constant3_Value_b;
  }

  /* End of Switch: '<S28>/Switch2' */

  /* SignalConversion: '<S16>/Signal Conversion10' */
  EAD_AdNomPosLimForJerk = EAD_LmtMaxJerkRequest;

  /* SignalConversion: '<S16>/Signal Conversion11' */
  EAD_SecAdNomALgtReqGroupSafePosLimForJerk = EAD_LmtMaxJerkRequest;

  /* Switch: '<S26>/Switch2' incorporates:
   *  Constant: '<S16>/Constant46'
   *  Constant: '<S16>/Constant47'
   *  Constant: '<S16>/Constant7'
   *  RelationalOperator: '<S26>/LowerRelop1'
   *  RelationalOperator: '<S26>/UpperRelop'
   *  Switch: '<S26>/Switch'
   */
  if (FallbackSigOutput_P.Constant7_Value_l >
      FallbackSigOutput_P.Constant46_Value) {
    /* Switch: '<S26>/Switch2' */
    EAD_LmtMaxAccRequest = FallbackSigOutput_P.Constant46_Value;
  } else if (FallbackSigOutput_P.Constant7_Value_l <
             FallbackSigOutput_P.Constant47_Value) {
    /* Switch: '<S26>/Switch' incorporates:
     *  Constant: '<S16>/Constant47'
     *  Switch: '<S26>/Switch2'
     */
    EAD_LmtMaxAccRequest = FallbackSigOutput_P.Constant47_Value;
  } else {
    /* Switch: '<S26>/Switch2' */
    EAD_LmtMaxAccRequest = FallbackSigOutput_P.Constant7_Value_l;
  }

  /* End of Switch: '<S26>/Switch2' */

  /* SignalConversion: '<S16>/Signal Conversion4' */
  EAD_AdPrimALgtLimReqGroupSafeALgtMaxReq = EAD_LmtMaxAccRequest;

  /* SignalConversion: '<S16>/Signal Conversion5' */
  EAD_AdSecALgtLimReqGroupSafeALgtMaxReq = EAD_LmtMaxAccRequest;

  /* Switch: '<S25>/Switch2' incorporates:
   *  Constant: '<S16>/Constant44'
   *  Constant: '<S16>/Constant45'
   *  Constant: '<S16>/Constant8'
   *  RelationalOperator: '<S25>/LowerRelop1'
   *  RelationalOperator: '<S25>/UpperRelop'
   *  Switch: '<S25>/Switch'
   */
  if (FallbackSigOutput_P.Constant8_Value_g >
      FallbackSigOutput_P.Constant44_Value) {
    /* Switch: '<S25>/Switch2' */
    EAD_LmtMinAccRequest = FallbackSigOutput_P.Constant44_Value;
  } else if (FallbackSigOutput_P.Constant8_Value_g <
             FallbackSigOutput_P.Constant45_Value) {
    /* Switch: '<S25>/Switch' incorporates:
     *  Constant: '<S16>/Constant45'
     *  Switch: '<S25>/Switch2'
     */
    EAD_LmtMinAccRequest = FallbackSigOutput_P.Constant45_Value;
  } else {
    /* Switch: '<S25>/Switch2' */
    EAD_LmtMinAccRequest = FallbackSigOutput_P.Constant8_Value_g;
  }

  /* End of Switch: '<S25>/Switch2' */

  /* SignalConversion: '<S16>/Signal Conversion6' */
  EAD_AdPrimALgtLimReqGroupSafeALgtMinReq = EAD_LmtMinAccRequest;

  /* SignalConversion: '<S16>/Signal Conversion7' */
  EAD_AdSecALgtLimReqGroupSafeALgtMinReq = EAD_LmtMinAccRequest;

  /* Switch: '<S10>/Switch19' incorporates:
   *  Constant: '<S10>/Constant29'
   */
  if (EAD_CswVehOperStReq_P) {
    /* Switch: '<S10>/Switch19' incorporates:
     *  Constant: '<S10>/Constant30'
     */
    EAD_VehOperStReqVehOperStReq = EAD_VehOperStReq_P;
  } else {
    /* Switch: '<S10>/Switch19' incorporates:
     *  Constant: '<S10>/Constant'
     */
    EAD_VehOperStReqVehOperStReq = FallbackSigOutput_P.Constant_Value_b;
  }

  /* End of Switch: '<S10>/Switch19' */

  /* Constant: '<S10>/Constant5' */
  EAD_AdSetSpd = FallbackSigOutput_P.Constant5_Value_h;

  /* Switch: '<S10>/Switch22' incorporates:
   *  Constant: '<S10>/Constant35'
   */
  if (EAD_CswAdDirReqDirReq_P) {
    /* Switch: '<S10>/Switch22' incorporates:
     *  Constant: '<S10>/Constant36'
     */
    EAD_AdDirReqDirReq = EAD_AdDirReqDirReq_P;
  } else {
    /* Switch: '<S10>/Switch22' incorporates:
     *  Constant: '<S10>/Constant1'
     */
    EAD_AdDirReqDirReq = FallbackSigOutput_P.Constant1_Value_a;
  }

  /* End of Switch: '<S10>/Switch22' */

  /* Constant: '<S10>/Constant2' */
  EAD_AdWhlLockReqNoReqApplyRel = FallbackSigOutput_P.Constant2_Value_be;

  /* Constant: '<S10>/Constant3' */
  EAD_SecAdWhlLockReqNoReqApplyRel = FallbackSigOutput_P.Constant3_Value_j;

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Logic: '<S36>/AND'
   *  Logic: '<S36>/NOT'
   *  UnitDelay: '<S36>/Unit Delay1'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackActive && (!FallbackSigOutput_DW.UnitDelay1_DSTATE_k));

  /* Switch: '<S10>/Switch7' */
  if (rtb_LessThan) {
    /* Switch: '<S10>/Switch7' incorporates:
     *  Inport: '<Root>/VSI_VehicleInfo'
     */
    rtb_Switch7 =
      (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
       ())->VSI_TurnSignalStatus;
  } else {
    /* Switch: '<S10>/Switch7' incorporates:
     *  UnitDelay: '<S10>/Unit Delay1'
     */
    rtb_Switch7 = FallbackSigOutput_DW.UnitDelay1_DSTATE_h;
  }

  /* End of Switch: '<S10>/Switch7' */

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S10>/Constant20'
   *  RelationalOperator: '<S10>/Equal'
   */
  rtb_LessThan = (rtb_Switch7 != FallbackSigOutput_P.Constant20_Value_d);

  /* Logic: '<S10>/AND' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  rtb_AdpLiReqFromAPIIncrLiRiReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackActive && rtb_LessThan);

  /* DataTypeConversion: '<S7>/Data Type Conversion53' incorporates:
   *  Constant: '<S10>/Constant21'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S10>/Equal1'
   */
  rtb_AdpLiReqFromAPIHzrdLiDeactnReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackMode == FallbackSigOutput_P.Constant21_Value);

  /* Logic: '<S10>/AND3' */
  rtb_AdpLiReqFromAPIIndcrLeReq = (rtb_LessThan &&
    rtb_AdpLiReqFromAPIHzrdLiDeactnReq);

  /* DataTypeConversion: '<S7>/Data Type Conversion53' incorporates:
   *  Constant: '<S10>/Parameter1'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S10>/LessThan1'
   */
  rtb_AdpLiReqFromAPIHzrdLiDeactnReq =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_LongitudinalVelocity <= EAD_StandstillSpeedThd_P);

  /* Logic: '<S10>/AND5' incorporates:
   *  Constant: '<S10>/Parameter'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S10>/LessThan'
   */
  EAD_EnableStandStill = ((EAD_LmtAccRequest <= EAD_StandstillAccThd_P) &&
    rtb_AdpLiReqFromAPIHzrdLiDeactnReq &&
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
    ->VSI_FallbackActive);

  /* Logic: '<S35>/AND' incorporates:
   *  Logic: '<S35>/NOT'
   *  UnitDelay: '<S35>/Unit Delay1'
   */
  rtb_AdpLiReqFromAPIHzrdLiDeactnReq = (rtb_AdpLiReqFromAPIIncrLiRiReq &&
    (!FallbackSigOutput_DW.UnitDelay1_DSTATE_c));

  /* Logic: '<S37>/AND' incorporates:
   *  Logic: '<S37>/NOT'
   *  UnitDelay: '<S37>/Unit Delay1'
   */
  rtb_AND_n = (rtb_AdpLiReqFromAPIIndcrLeReq &&
               (!FallbackSigOutput_DW.UnitDelay1_DSTATE_d));

  /* Switch: '<S40>/Switch' */
  if (rtb_AdpLiReqFromAPIHzrdLiDeactnReq) {
    /* Switch: '<S40>/Switch' incorporates:
     *  Constant: '<S40>/IAM_Ts_P2'
     */
    rtb_UnitDelay_bj = FallbackSigOutput_P.IAM_Ts_P2_Value_c;
  } else {
    /* Switch: '<S40>/Switch' incorporates:
     *  Constant: '<S10>/Parameter2'
     *  Sum: '<S40>/Add'
     *  UnitDelay: '<S40>/Unit Delay'
     */
    rtb_UnitDelay_bj = EAD_SysCycleTime_P +
      FallbackSigOutput_DW.UnitDelay_DSTATE_c;
  }

  /* End of Switch: '<S40>/Switch' */

  /* MultiPortSwitch: '<S44>/Multiport Switch1' incorporates:
   *  Constant: '<S10>/Parameter3'
   *  Logic: '<S40>/Logical Operator'
   *  Logic: '<S40>/Logical Operator1'
   *  RelationalOperator: '<S40>/GreaterThan'
   */
  if (rtb_AdpLiReqFromAPIHzrdLiDeactnReq || (rtb_UnitDelay_bj <
       EAD_HazardLightDelayTime_P)) {
    /* MultiPortSwitch: '<S44>/Multiport Switch2' */
    if (!rtb_AdpLiReqFromAPIHzrdLiDeactnReq) {
      /* MultiPortSwitch: '<S44>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S44>/Multiport Switch2'
       *  UnitDelay: '<S44>/Unit Delay'
       */
      rtb_AdpLiReqFromAPIHzrdLiDeactnReq =
        FallbackSigOutput_DW.UnitDelay_DSTATE_d;
    } else {
      /* MultiPortSwitch: '<S44>/Multiport Switch1' incorporates:
       *  Constant: '<S44>/Constant7'
       *  MultiPortSwitch: '<S44>/Multiport Switch2'
       */
      rtb_AdpLiReqFromAPIHzrdLiDeactnReq = FallbackSigOutput_P.Constant7_Value_g;
    }

    /* End of MultiPortSwitch: '<S44>/Multiport Switch2' */
  } else {
    /* MultiPortSwitch: '<S44>/Multiport Switch1' incorporates:
     *  Constant: '<S44>/Constant6'
     */
    rtb_AdpLiReqFromAPIHzrdLiDeactnReq = FallbackSigOutput_P.Constant6_Value_ew;
  }

  /* End of MultiPortSwitch: '<S44>/Multiport Switch1' */

  /* Switch: '<S10>/Switch2' */
  if (rtb_AdpLiReqFromAPIHzrdLiDeactnReq) {
    /* Switch: '<S10>/Switch2' incorporates:
     *  Constant: '<S10>/Constant9'
     */
    EAD_AdpLiReqFromAPIHzrdLiActvnReq = FallbackSigOutput_P.Constant9_Value_gl;
  } else {
    /* Switch: '<S10>/Switch2' incorporates:
     *  Constant: '<S10>/Constant10'
     */
    EAD_AdpLiReqFromAPIHzrdLiActvnReq = FallbackSigOutput_P.Constant10_Value_m;
  }

  /* End of Switch: '<S10>/Switch2' */

  /* Switch: '<S10>/Switch21' incorporates:
   *  Constant: '<S10>/Constant33'
   *  Switch: '<S10>/Switch6'
   */
  if (EAD_CswAdStandStillReqReq_P) {
    /* Switch: '<S10>/Switch21' incorporates:
     *  Constant: '<S10>/Constant34'
     */
    EAD_AdStandStillReqReq = EAD_AdStandStillReqReq_P;
  } else if (EAD_EnableStandStill) {
    /* Switch: '<S10>/Switch6' incorporates:
     *  Constant: '<S10>/Constant7'
     *  Switch: '<S10>/Switch21'
     */
    EAD_AdStandStillReqReq = FallbackSigOutput_P.Constant7_Value_k;
  } else {
    /* Switch: '<S10>/Switch21' incorporates:
     *  Constant: '<S10>/Constant8'
     */
    EAD_AdStandStillReqReq = FallbackSigOutput_P.Constant8_Value_e;
  }

  /* End of Switch: '<S10>/Switch21' */

  /* Switch: '<S41>/Switch' */
  if (rtb_AND_n) {
    /* Switch: '<S41>/Switch' incorporates:
     *  Constant: '<S41>/IAM_Ts_P2'
     */
    rtb_Switch_mg = FallbackSigOutput_P.IAM_Ts_P2_Value_b;
  } else {
    /* Switch: '<S41>/Switch' incorporates:
     *  Constant: '<S10>/Parameter4'
     *  Sum: '<S41>/Add'
     *  UnitDelay: '<S41>/Unit Delay'
     */
    rtb_Switch_mg = EAD_SysCycleTime_P + FallbackSigOutput_DW.UnitDelay_DSTATE_i;
  }

  /* End of Switch: '<S41>/Switch' */

  /* MultiPortSwitch: '<S45>/Multiport Switch1' incorporates:
   *  Constant: '<S10>/Parameter5'
   *  Logic: '<S41>/Logical Operator'
   *  Logic: '<S41>/Logical Operator1'
   *  RelationalOperator: '<S41>/GreaterThan'
   */
  if (rtb_AND_n || (rtb_Switch_mg < EAD_HazardLightDelayTime_P)) {
    /* MultiPortSwitch: '<S45>/Multiport Switch2' */
    if (!rtb_AND_n) {
      /* MultiPortSwitch: '<S45>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S45>/Multiport Switch2'
       *  UnitDelay: '<S45>/Unit Delay'
       */
      rtb_AND_n = FallbackSigOutput_DW.UnitDelay_DSTATE_et;
    } else {
      /* MultiPortSwitch: '<S45>/Multiport Switch1' incorporates:
       *  Constant: '<S45>/Constant7'
       *  MultiPortSwitch: '<S45>/Multiport Switch2'
       */
      rtb_AND_n = FallbackSigOutput_P.Constant7_Value_c;
    }

    /* End of MultiPortSwitch: '<S45>/Multiport Switch2' */
  } else {
    /* MultiPortSwitch: '<S45>/Multiport Switch1' incorporates:
     *  Constant: '<S45>/Constant6'
     */
    rtb_AND_n = FallbackSigOutput_P.Constant6_Value_j;
  }

  /* End of MultiPortSwitch: '<S45>/Multiport Switch1' */

  /* Switch: '<S10>/Switch3' */
  if (rtb_AND_n) {
    /* Switch: '<S10>/Switch3' incorporates:
     *  Constant: '<S10>/Constant11'
     */
    EAD_AdpLiReqFromAPIHzrdLiDeactnReq = FallbackSigOutput_P.Constant11_Value_g;
  } else {
    /* Switch: '<S10>/Switch3' incorporates:
     *  Constant: '<S10>/Constant12'
     */
    EAD_AdpLiReqFromAPIHzrdLiDeactnReq = FallbackSigOutput_P.Constant12_Value_b;
  }

  /* End of Switch: '<S10>/Switch3' */

  /* Constant: '<S10>/Constant13' */
  EAD_AdpLiReqFromAPIIncrLiRiReq = FallbackSigOutput_P.Constant13_Value_h;

  /* SignalConversion: '<S10>/Signal Conversion' */
  EAD_AdpLiReqFromAPIIndcrLeReq = EAD_AdpLiReqFromAPIIncrLiRiReq;

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S32>/Constant'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S32>/Compare'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_VehicleMode == FallbackSigOutput_P.CompareToConstant9_const);

  /* Logic: '<S10>/AND1' incorporates:
   *  Constant: '<S10>/Constant18'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Logic: '<S10>/NOT'
   *  UnitDelay: '<S10>/Unit Delay'
   */
  rtb_AND1 = (rtb_LessThan &&
              (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
               ())->VSI_VehicleAutofullReady &&
              (!FallbackSigOutput_DW.UnitDelay_DSTATE_a) && EAD_CswEnterAdMode_P);

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S30>/Constant'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S30>/Compare'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_VehicleMode == FallbackSigOutput_P.CompareToConstant10_const);

  /* Logic: '<S10>/AND2' incorporates:
   *  Constant: '<S10>/Constant16'
   */
  rtb_AND2 = (rtb_LessThan && EAD_CswQuitAdMode_P);

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S31>/Constant'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  RelationalOperator: '<S31>/Compare'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_VehicleMode == FallbackSigOutput_P.CompareToConstant8_const);

  /* Logic: '<S33>/AND' incorporates:
   *  Logic: '<S33>/NOT'
   *  UnitDelay: '<S33>/Unit Delay1'
   */
  rtb_AND_pv = (rtb_AND1 && (!FallbackSigOutput_DW.UnitDelay1_DSTATE_o));

  /* Logic: '<S34>/AND' incorporates:
   *  Logic: '<S34>/NOT'
   *  UnitDelay: '<S34>/Unit Delay1'
   */
  rtb_AND_m = (rtb_AND2 && (!FallbackSigOutput_DW.UnitDelay1_DSTATE_l));

  /* Switch: '<S10>/Switch' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Switch: '<S10>/Switch4'
   */
  if (rtb_LessThan) {
    /* Switch: '<S10>/Switch' incorporates:
     *  Constant: '<S10>/Constant15'
     */
    EAD_RawHmiAutnmsSts = FallbackSigOutput_P.Constant15_Value_n;
  } else if
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
        ())->VSI_VehicleAutofullReady) {
    /* Switch: '<S10>/Switch4' incorporates:
     *  Constant: '<S10>/Constant4'
     *  Switch: '<S10>/Switch'
     */
    EAD_RawHmiAutnmsSts = FallbackSigOutput_P.Constant4_Value_i;
  } else {
    /* Switch: '<S10>/Switch' incorporates:
     *  Constant: '<S10>/Constant14'
     *  Switch: '<S10>/Switch4'
     */
    EAD_RawHmiAutnmsSts = FallbackSigOutput_P.Constant14_Value_c;
  }

  /* End of Switch: '<S10>/Switch' */

  /* Switch: '<S38>/Switch' */
  if (rtb_AND_pv) {
    /* Switch: '<S38>/Switch' incorporates:
     *  Constant: '<S38>/IAM_Ts_P2'
     */
    rtb_Switch_o = FallbackSigOutput_P.IAM_Ts_P2_Value_m;
  } else {
    /* Switch: '<S38>/Switch' incorporates:
     *  Constant: '<S10>/DelayTime1'
     *  Sum: '<S38>/Add'
     *  UnitDelay: '<S38>/Unit Delay'
     */
    rtb_Switch_o = EAD_SysCycleTime_P + FallbackSigOutput_DW.UnitDelay_DSTATE_f;
  }

  /* End of Switch: '<S38>/Switch' */

  /* MultiPortSwitch: '<S42>/Multiport Switch1' incorporates:
   *  Constant: '<S10>/DelayTime'
   *  Logic: '<S38>/Logical Operator'
   *  Logic: '<S38>/Logical Operator1'
   *  RelationalOperator: '<S38>/GreaterThan'
   */
  if (rtb_AND_pv || (rtb_Switch_o < EAD_AdActiveDelayTime_P)) {
    /* MultiPortSwitch: '<S42>/Multiport Switch2' */
    if (!rtb_AND_pv) {
      /* MultiPortSwitch: '<S42>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S42>/Multiport Switch2'
       *  UnitDelay: '<S42>/Unit Delay'
       */
      EAD_AdActiveRequest = FallbackSigOutput_DW.UnitDelay_DSTATE_o;
    } else {
      /* MultiPortSwitch: '<S42>/Multiport Switch1' incorporates:
       *  Constant: '<S42>/Constant7'
       *  MultiPortSwitch: '<S42>/Multiport Switch2'
       */
      EAD_AdActiveRequest = FallbackSigOutput_P.Constant7_Value_h;
    }

    /* End of MultiPortSwitch: '<S42>/Multiport Switch2' */
  } else {
    /* MultiPortSwitch: '<S42>/Multiport Switch1' incorporates:
     *  Constant: '<S42>/Constant6'
     */
    EAD_AdActiveRequest = FallbackSigOutput_P.Constant6_Value_d;
  }

  /* End of MultiPortSwitch: '<S42>/Multiport Switch1' */

  /* Switch: '<S39>/Switch' */
  if (rtb_AND_m) {
    /* Switch: '<S39>/Switch' incorporates:
     *  Constant: '<S39>/IAM_Ts_P2'
     */
    rtb_Switch_ph = FallbackSigOutput_P.IAM_Ts_P2_Value_e;
  } else {
    /* Switch: '<S39>/Switch' incorporates:
     *  Constant: '<S10>/DelayTime3'
     *  Sum: '<S39>/Add'
     *  UnitDelay: '<S39>/Unit Delay'
     */
    rtb_Switch_ph = EAD_SysCycleTime_P +
      FallbackSigOutput_DW.UnitDelay_DSTATE_kp;
  }

  /* End of Switch: '<S39>/Switch' */

  /* MultiPortSwitch: '<S43>/Multiport Switch1' incorporates:
   *  Constant: '<S10>/DelayTime4'
   *  Logic: '<S39>/Logical Operator'
   *  Logic: '<S39>/Logical Operator1'
   *  RelationalOperator: '<S39>/GreaterThan'
   */
  if (rtb_AND_m || (rtb_Switch_ph < EAD_AdQuitDelayTime_P)) {
    /* MultiPortSwitch: '<S43>/Multiport Switch2' */
    if (!rtb_AND_m) {
      /* MultiPortSwitch: '<S43>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S43>/Multiport Switch2'
       *  UnitDelay: '<S43>/Unit Delay'
       */
      EAD_AdQuitRequest = FallbackSigOutput_DW.UnitDelay_DSTATE_ey;
    } else {
      /* MultiPortSwitch: '<S43>/Multiport Switch1' incorporates:
       *  Constant: '<S43>/Constant7'
       *  MultiPortSwitch: '<S43>/Multiport Switch2'
       */
      EAD_AdQuitRequest = FallbackSigOutput_P.Constant7_Value_j;
    }

    /* End of MultiPortSwitch: '<S43>/Multiport Switch2' */
  } else {
    /* MultiPortSwitch: '<S43>/Multiport Switch1' incorporates:
     *  Constant: '<S43>/Constant6'
     */
    EAD_AdQuitRequest = FallbackSigOutput_P.Constant6_Value_b;
  }

  /* End of MultiPortSwitch: '<S43>/Multiport Switch1' */

  /* Switch: '<S10>/Switch1' incorporates:
   *  Switch: '<S10>/Switch5'
   */
  if (EAD_AdActiveRequest) {
    /* Switch: '<S10>/Switch1' incorporates:
     *  Constant: '<S10>/Constant6'
     */
    EAD_AutnmsDrvStReqAutnmsDrvStReq = FallbackSigOutput_P.Constant6_Value_ng;
  } else if (EAD_AdQuitRequest) {
    /* Switch: '<S10>/Switch5' incorporates:
     *  Constant: '<S10>/Constant17'
     *  Switch: '<S10>/Switch1'
     */
    EAD_AutnmsDrvStReqAutnmsDrvStReq = FallbackSigOutput_P.Constant17_Value_o;
  } else {
    /* Switch: '<S10>/Switch1' incorporates:
     *  Constant: '<S10>/Constant19'
     */
    EAD_AutnmsDrvStReqAutnmsDrvStReq = FallbackSigOutput_P.Constant19_Value_k;
  }

  /* End of Switch: '<S10>/Switch1' */

  /* Switch: '<S10>/Switch20' incorporates:
   *  Constant: '<S10>/Constant31'
   */
  if (EAD_CswHmiAutnmsSts_P) {
    /* Switch: '<S10>/Switch20' incorporates:
     *  Constant: '<S10>/Constant32'
     */
    EAD_HmiAutnmsStsHmiAutnmsSts = EAD_HmiAutnmsSts_P;
  } else {
    /* Switch: '<S10>/Switch20' */
    EAD_HmiAutnmsStsHmiAutnmsSts = EAD_RawHmiAutnmsSts;
  }

  /* End of Switch: '<S10>/Switch20' */

  /* DataTypeConversion: '<S7>/Data Type Conversion39' incorporates:
   *  Constant: '<S7>/Constant2'
   */
  DataTypeConversion39_h = (UDcDcAvlLoSideExt)
    (FallbackSigOutput_P.Constant2_Value_kc * 10U);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/VIMMid3CanFr04_IDcDcAvlMaxLoSideExt' incorporates:
   *  Constant: '<S7>/Constant1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt
    (FallbackSigOutput_P.Constant1_Value_c0);

  /* Outport: '<Root>/VIMMid3CanFr04_IDcDcAvlLoSideExt' incorporates:
   *  Constant: '<S7>/Constant'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_IDcDcAvlLoSideExt
    (FallbackSigOutput_P.Constant_Value_ip);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* BusCreator: '<S7>/Bus Creator7' */
  BusCreator7.HmiAutnmsStsChecksum = 0U;
  BusCreator7.HmiAutnmsStsCounter = 0U;
  BusCreator7.HmiAutnmsStsHmiAutnmsSts = EAD_HmiAutnmsStsHmiAutnmsSts;

  /* BusCreator: '<S7>/Bus Creator3' */
  BusCreator3.AutnmsDrvStReqAutnmsDrvStReq = EAD_AutnmsDrvStReqAutnmsDrvStReq;
  BusCreator3.AutnmsDrvStReqChecksum = 0U;
  BusCreator3.AutnmsDrvStReqCounter = 0U;

  /* BusCreator: '<S7>/Bus Creator6' */
  BusCreator6.VehOperStReqChecksum = 0U;
  BusCreator6.VehOperStReqCounter = 0U;
  BusCreator6.VehOperStReqVehOperStReq = EAD_VehOperStReqVehOperStReq;

  /* DataTypeConversion: '<S7>/Data Type Conversion44' incorporates:
   *  Constant: '<S7>/Constant3'
   */
  DataTypeConversion44_e = FallbackSigOutput_P.Constant3_Value_pc;

  /* BusCreator: '<S7>/Bus Creator8' */
  BusCreator8.AdStandStillReqChks = 0U;
  BusCreator8.AdStandStillReqCntr = 0U;
  BusCreator8.AdStandStillReqReq = EAD_AdStandStillReqReq;

  /* BusCreator: '<S7>/Bus Creator9' */
  BusCreator9.AdDirReqChks = 0U;
  BusCreator9.AdDirReqCntr = 0U;
  BusCreator9.AdDirReqDirReq = EAD_AdDirReqDirReq;

  /* BusCreator: '<S7>/Bus Creator13' incorporates:
   *  Constant: '<S7>/Constant5'
   *  Constant: '<S7>/Constant6'
   *  Constant: '<S7>/Constant7'
   *  DataTypeConversion: '<S7>/Data Type Conversion52'
   *  DataTypeConversion: '<S7>/Data Type Conversion53'
   *  DataTypeConversion: '<S7>/Data Type Conversion54'
   *  DataTypeConversion: '<S7>/Data Type Conversion55'
   */
  BusCreator13.AdpLiReqFromAPIAhbcActvn = FallbackSigOutput_P.Constant5_Value_k;
  BusCreator13.AdpLiReqFromAPIChks = FallbackSigOutput_P.Constant6_Value_py;
  BusCreator13.AdpLiReqFromAPICntr = FallbackSigOutput_P.Constant7_Value_di;
  BusCreator13.AdpLiReqFromAPIHzrdLiActvnReq =
    (EAD_AdpLiReqFromAPIHzrdLiActvnReq != 0);
  BusCreator13.AdpLiReqFromAPIHzrdLiDeactnReq =
    (EAD_AdpLiReqFromAPIHzrdLiDeactnReq != 0);
  BusCreator13.AdpLiReqFromAPIIncrLiRiReq = (EAD_AdpLiReqFromAPIIncrLiRiReq != 0);
  BusCreator13.AdpLiReqFromAPIIndcrLeReq = (EAD_AdpLiReqFromAPIIndcrLeReq != 0);

  /* DataTypeConversion: '<S7>/Data Type Conversion46' */
  rtb_Product1_e_idx_0 = fmodf(floorf(EAD_AdSetSpd / 0.1F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator11' */
  BusCreator11.AdWhlLockReqChks = 0U;
  BusCreator11.AdWhlLockReqCntr = 0U;
  BusCreator11.AdWhlLockReqNoReqApplyRel = EAD_AdWhlLockReqNoReqApplyRel;

  /* DataTypeConversion: '<S7>/Data Type Conversion4' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdNomALgtReqGroupSafeALgtNomReqMax *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator1' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion4'
   */
  BusCreator1.AdNomALgtReqGroupSafeALgtNomReqMax = (uint16)(rtb_Product1_e_idx_1
    < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion5' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdNomALgtReqGroupSafeALgtNomReqMin *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator1' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion5'
   */
  BusCreator1.AdNomALgtReqGroupSafeALgtNomReqMin = (uint16)(rtb_Product1_e_idx_1
    < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);
  BusCreator1.AdNomALgtReqGroupSafeChks = 0U;
  BusCreator1.AdNomALgtReqGroupSafeCntr = 0U;

  /* DataTypeConversion: '<S7>/Data Type Conversion12' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_AdNomALgtReqGroupSafeNegLimForJerk +
    100.0F) / 0.05F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator1' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion12'
   */
  BusCreator1.AdNomALgtReqGroupSafeNegLimForJerk = (uint16)(rtb_Product1_e_idx_1
    < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion11' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_AdNomPosLimForJerk + 100.0F) / 0.05F),
    65536.0F);

  /* BusCreator: '<S7>/Bus Creator1' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion11'
   */
  BusCreator1.AdNomALgtReqGroupSafePosLimForJerk = (uint16)(rtb_Product1_e_idx_1
    < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion14' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdPrimALgtLimReqGroupSafeALgtMaxReq *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator2' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion14'
   */
  BusCreator2.AdPrimALgtLimReqGroupSafeALgtMaxReq = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion15' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdPrimALgtLimReqGroupSafeALgtMinReq *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator2' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion15'
   */
  BusCreator2.AdPrimALgtLimReqGroupSafeALgtMinReq = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);
  BusCreator2.AdPrimALgtLimReqGroupSafeChks = 0U;
  BusCreator2.AdPrimALgtLimReqGroupSafeCntr = 0U;

  /* BusCreator: '<S7>/Bus Creator' */
  BusCreator.AdPrimWhlAgReqGroupSafeChks = 0U;
  BusCreator.AdPrimWhlAgReqGroupSafeCntr = 0U;

  /* DataTypeConversion: '<S7>/Data Type Conversion' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_AdPrimWhlAgReqGroupSafeWhlAgReq +
    0.85F) / 5.249E-5F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion'
   */
  BusCreator.AdPrimWhlAgReqGroupSafeWhlAgReq = (uint16)(rtb_Product1_e_idx_1 <
    0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);

  /* BusCreator: '<S7>/Bus Creator4' */
  BusCreator4.AdSecWhlAgReqGroupSafeChks = 0U;
  BusCreator4.AdSecWhlAgReqGroupSafeCntr = 0U;

  /* DataTypeConversion: '<S7>/Data Type Conversion3' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_AdSecWhlAgReqGroupSafeWhlAgReq +
    0.85F) / 5.249E-5F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator4' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion3'
   */
  BusCreator4.AdSecWhlAgReqGroupSafeWhlAgReq = (uint16)(rtb_Product1_e_idx_1 <
    0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Product1_e_idx_1 : (sint32)
    (uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion40' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdSecALgtLimReqGroupSafeALgtMaxReq *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator10' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion40'
   */
  BusCreator10.AdSecALgtLimReqGroupSafeALgtMaxReq = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion41' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_AdSecALgtLimReqGroupSafeALgtMinReq *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator10' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion41'
   */
  BusCreator10.AdSecALgtLimReqGroupSafeALgtMinReq = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);
  BusCreator10.AdSecALgtLimReqGroupSafeChks = 0U;
  BusCreator10.AdSecALgtLimReqGroupSafeCntr = 0U;

  /* DataTypeConversion: '<S7>/Data Type Conversion6' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_SecAdNomALgtReqGroupSafeALgtNomReqMax *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator5' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion6'
   */
  BusCreator5.SecAdNomALgtReqGroupSafeALgtNomReqMax = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion7' */
  rtb_Product1_e_idx_1 = fmodf(floorf(EAD_SecAdNomALgtReqGroupSafeALgtNomReqMin *
    128.0F + 1920.0F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator5' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion7'
   */
  BusCreator5.SecAdNomALgtReqGroupSafeALgtNomReqMin = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);
  BusCreator5.SecAdNomALgtReqGroupSafeChks = 0U;
  BusCreator5.SecAdNomALgtReqGroupSafeCntr = 0U;

  /* DataTypeConversion: '<S7>/Data Type Conversion28' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_SecAdNomALgtReqGroupSafeNegLimForJerk
    + 100.0F) / 0.05F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator5' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion28'
   */
  BusCreator5.SecAdNomALgtReqGroupSafeNegLimForJerk = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);

  /* DataTypeConversion: '<S7>/Data Type Conversion29' */
  rtb_Product1_e_idx_1 = fmodf(floorf((EAD_SecAdNomALgtReqGroupSafePosLimForJerk
    + 100.0F) / 0.05F), 65536.0F);

  /* BusCreator: '<S7>/Bus Creator5' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion29'
   */
  BusCreator5.SecAdNomALgtReqGroupSafePosLimForJerk = (uint16)
    (rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_1 : (sint32)(uint16)rtb_Product1_e_idx_1);

  /* BusCreator: '<S7>/Bus Creator12' */
  BusCreator12.SecAdWhlLockReqChks = 0U;
  BusCreator12.SecAdWhlLockReqCntr = 0U;
  BusCreator12.SecAdWhlLockReqNoReqApplyRel = EAD_SecAdWhlLockReqNoReqApplyRel;

  /* BusCreator: '<S7>/Bus Creator14' incorporates:
   *  Constant: '<S7>/Parameter'
   */
  FBS_DebugInfo.EAD_McuVersion = EAD_AswVersion_P;
  FBS_DebugInfo.EAD_SystemState = rtb_FixPtSum1_a;
  FBS_DebugInfo.EAD_AccRequest = EAD_AccRequest;
  FBS_DebugInfo.EAD_JerkRequest = EAD_JerkRequest;
  FBS_DebugInfo.EAD_SteeringAngle = EAD_SteeringAngle;
  FBS_DebugInfo.EAD_LimitSteeringAngle = rtb_EAD_MaxSteeringAngleRate;
  FBS_DebugInfo.EAD_MaxSteeringAngleRate = EAD_LimitSteeringAngle;

  /* DataTypeConversion: '<S46>/Data Type Conversion101' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  DataTypeConversion101 = (FallbackTriggerStatus)
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_FallbackMode & 15);

  /* DataTypeConversion: '<S46>/Data Type Conversion100' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  DataTypeConversion100 = (LaneValidState)
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
     ->VLP_LaneValidState & 15);

  /* DataTypeConversion: '<S46>/Data Type Conversion53' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  rtb_Product1_e_idx_1 = fmodf(floorf
    (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
      ->BDP_PosY0Cntr + 30.0F) / 0.01F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion54' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_17 = fmodf(floorf
                 (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                    ())->BDP_HeadingAngleCntr + 2.0F) / 0.01F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion55' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_16 = fmodf(floorf
                 (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                    ())->BDP_CurvatureCntr + 0.25F) / 1.6E-5F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion56' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_15 = fmodf(floorf
                 (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                    ())->BDP_CurvatureRateCntr + 0.024F) / 5.0E-8F),
                 4.2949673E+9F);

  /* DataTypeConversion: '<S46>/Data Type Conversion57' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_14 = fmodf(floorf
                 ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->BDP_ValidLengthCntr / 0.01F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion58' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_13 = fmodf(floorf
                 ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->VLP_MeasureLaneWidth / 0.02F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion59' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  DataTypeConversion59 = (LateralSystemState)
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
    ->LAT_SystemStateOut;

  /* DataTypeConversion: '<S46>/Data Type Conversion60' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_12 = fmodf(floorf
                 (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                    ())->LCT_ErrorDistY + 30.0F) / 0.01F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion64' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_11 = fmodf(floorf
                 (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                    ())->LCT_ErrHeadingAngle + 2.0F) / 0.01F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion65' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_10 = fmodf(floorf
                 ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->LCT_DistYErrorWeight), 128.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion66' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_z = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                  ())->LCT_HeadingErrorWeight), 128.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion67' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  rtb_SecAdNomALgtReqGroupSafePosLimForJerk = (LqrIterationNums)
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd())
     ->LCT_IterationNum & 1023);

  /* DataTypeConversion: '<S46>/Data Type Conversion68' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_y = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                  ())->LCT_IterationError / 0.002F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion71' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_x = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                  ())->LCT_SteeringAngleByLQR / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion72' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_w = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->LCT_FeedforwardSteeringAngle + 780.0F) / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion73' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_v = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->LCT_RawSteeringAngle + 780.0F) / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion74' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_u = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->LCT_LimitSteeringAngle + 780.0F) / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion75' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_t = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                   ())->LCT_MaxSteeringAngle + 780.0F) / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion76' incorporates:
   *  Inport: '<Root>/LAT_CtrlCmd'
   */
  tmp_s = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd
                  ())->LCT_MaxSteeringAngleRate * 0.25F), 256.0F);

  /* Outport: '<Root>/FallbackDebugInfo_LateralContribution' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion69'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralContribution
    (0U);

  /* Outport: '<Root>/FallbackDebugInfo_HeadingAngleContribution' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion70'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleContribution
    (0U);

  /* DataTypeConversion: '<S46>/Data Type Conversion85' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_r = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                   ())->CDS_CollisionDetection.CDS_LongNecAcc + 10.0F) / 0.05F),
                65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion86' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_q = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                   ())->CDS_CollisionDetection.CDS_LongNecAcc + 10.0F) / 0.05F),
                65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion88' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  DataTypeConversion88 = (ObjectStopTime)
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd())
     ->CDS_CollisionDetection.CDS_ObjectStopEnable * 20U);

  /* DataTypeConversion: '<S46>/Data Type Conversion89' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_p = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                  ())->CDS_CollisionDetection.CDS_EgoStopTime / 0.05F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion90' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_o = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                  ())->CDS_CollisionDetection.CDS_TimeToCollision / 0.05F),
                65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion91' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_n = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                  ())->CDS_CollisionDetection.CDS_SafeDistance * 4.0F), 256.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion92' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_m = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                   ())->FSC_FreeStopControl.FSC_AccRequestByLaneLength + 10.0F) /
                 0.05F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion93' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_l = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                   ())->FSC_FreeStopControl.FSC_AccRequestByLaneLength + 10.0F) /
                 0.05F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion94' incorporates:
   *  Inport: '<Root>/LGT_CtrlCmd'
   */
  tmp_k = fmodf(floorf
                (((Rte_IRead_FallbackSigOutput_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd
                   ())->FSC_FreeStopControl.FSC_FreeStopAccRequest + 10.0F) /
                 0.05F), 65536.0F);

  /* Outport: '<Root>/FallbackDebugInfo_AvoidCollisionEnable' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion87'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AvoidCollisionEnable
    (false);

  /* Abs: '<S46>/Abs1' incorporates:
   *  BusCreator: '<S7>/Bus Creator14'
   */
  rtb_Abs1 = fabsf(EAD_JerkRequest);

  /* DataTypeConversion: '<S46>/Data Type Conversion' incorporates:
   *  BusCreator: '<S7>/Bus Creator14'
   *  DataTypeConversion: '<S46>/Data Type Conversion77'
   *  DataTypeConversion: '<S46>/Data Type Conversion79'
   *  DataTypeConversion: '<S46>/Data Type Conversion81'
   *  DataTypeConversion: '<S46>/Data Type Conversion82'
   */
  tmp_18 = fmodf(floorf((EAD_AccRequest + 10.0F) / 0.05F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion78' incorporates:
   *  BusCreator: '<S7>/Bus Creator14'
   */
  tmp_j = fmodf(floorf(EAD_AccRequest / 0.1F), 65536.0F);

  /* DataTypeConversion: '<S46>/Data Type Conversion84' incorporates:
   *  BusCreator: '<S7>/Bus Creator14'
   */
  DataTypeConversion84 = rtb_FixPtSum1_a;

  /* Sum: '<S54>/FixPt Sum1' incorporates:
   *  Constant: '<S51>/FixPt Constant'
   *  Sum: '<S51>/FixPt Sum1'
   *  UnitDelay: '<S50>/Output'
   */
  rtb_FixPtSum1_a = (uint8)((uint32)FallbackSigOutput_DW.Output_DSTATE +
    FallbackSigOutput_P.FixPtConstant_Value_a);

  /* Switch: '<S52>/FixPt Switch' */
  if (rtb_FixPtSum1_a > FallbackSigOutput_P.CounterLimited2_uplimit_j) {
    /* Switch: '<S52>/FixPt Switch' incorporates:
     *  Constant: '<S52>/Constant'
     */
    rtb_FixPtSwitch = FallbackSigOutput_P.Constant_Value_a3;
  } else {
    /* Switch: '<S52>/FixPt Switch' */
    rtb_FixPtSwitch = rtb_FixPtSum1_a;
  }

  /* End of Switch: '<S52>/FixPt Switch' */

  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoRollingCounter' incorporates:
   *  UnitDelay: '<S50>/Output'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoRollingCounter
    (FallbackSigOutput_DW.Output_DSTATE);

  /* DataTypeConversion: '<S47>/Data Type Conversion95' incorporates:
   *  Constant: '<S47>/Constant24'
   *  Constant: '<S47>/Parameter'
   *  Product: '<S47>/Divide'
   */
  tmp_i = fmodf(floorf((float32)EAD_AswVersion_P *
                       FallbackSigOutput_P.Constant24_Value / 0.01F), 256.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion1' incorporates:
   *  Constant: '<S47>/Constant'
   */
  DataTypeConversion1_g = (VehMid3SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant_Value_go & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion18' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  rtb_AND_pv =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid3SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion19' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  rtb_AND_m =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid3SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion20' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion20_h =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid3VcuCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion21' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion21_p =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid3VcuCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion22' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion22_k =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid5SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion23' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion23 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid5SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion24' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion24_b =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid6SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion25' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion25_h =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid6SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion26' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion26_k =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid3SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion27' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion27_g =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid3SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion28' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion28 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid5SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion29' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion29 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid5SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion30' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion30_l =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid6SsmCounter0Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion31' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion31 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid6SsmCounter1Timeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion34' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_h = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid3SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion35' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  DataTypeConversion35_l =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuFbCanTimeout;

  /* DataTypeConversion: '<S47>/Data Type Conversion37' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_g = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid3SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion38' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_f = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid3VcuCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion39' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_e = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid3VcuCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion40' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_d = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid5SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion41' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_c = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid5SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion42' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_b = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid6SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion43' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_a = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_VehMid6SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion44' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_9 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid3SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion45' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_8 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid3SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion46' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_7 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid5SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion47' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_6 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid5SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion48' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_5 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid6SsmCounter0Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion49' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_4 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuMid6SsmCounter1Timer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion50' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  tmp_3 = fmodf(floorf
                ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
                  ())->VSI_AcuFbCanTimer / 10.0F), 65536.0F);

  /* DataTypeConversion: '<S47>/Data Type Conversion2' incorporates:
   *  Constant: '<S47>/Constant1'
   */
  DataTypeConversion2 = (VehMid3SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant1_Value_k & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion3' incorporates:
   *  Constant: '<S47>/Constant2'
   */
  DataTypeConversion3_l = (VehMid3VcuCounter0MessageID)
    (FallbackSigOutput_P.Constant2_Value_fd & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion4' incorporates:
   *  Constant: '<S47>/Constant3'
   */
  DataTypeConversion4_f = (VehMid3VcuCounter1MessageID)
    (FallbackSigOutput_P.Constant3_Value_a & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion5' incorporates:
   *  Constant: '<S47>/Constant4'
   */
  DataTypeConversion5 = (VehMid5SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant4_Value_k & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion6' incorporates:
   *  Constant: '<S47>/Constant5'
   */
  DataTypeConversion6_py = (VehMid5SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant5_Value_m & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion7' incorporates:
   *  Constant: '<S47>/Constant6'
   */
  DataTypeConversion7 = (VehMid6SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant6_Value_e & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion8' incorporates:
   *  Constant: '<S47>/Constant7'
   */
  DataTypeConversion8 = (VehMid6SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant7_Value_o & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion9' incorporates:
   *  Constant: '<S47>/Constant8'
   */
  DataTypeConversion9 = (AcuMid3SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant8_Value_j & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion10' incorporates:
   *  Constant: '<S47>/Constant9'
   */
  DataTypeConversion10 = (AcuMid3SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant9_Value_g & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion11' incorporates:
   *  Constant: '<S47>/Constant10'
   */
  DataTypeConversion11_i = (AcuMid5SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant10_Value_j & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion12' incorporates:
   *  Constant: '<S47>/Constant11'
   */
  DataTypeConversion12 = (AcuMid5SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant11_Value_d & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion13' incorporates:
   *  Constant: '<S47>/Constant12'
   */
  DataTypeConversion13 = (AcuMid6SsmCounter0MessageID)
    (FallbackSigOutput_P.Constant12_Value & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion14' incorporates:
   *  Constant: '<S47>/Constant13'
   */
  DataTypeConversion14 = (AcuMid6SsmCounter1MessageID)
    (FallbackSigOutput_P.Constant13_Value_m & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion15' incorporates:
   *  Constant: '<S47>/Constant14'
   */
  DataTypeConversion15 = (AcuFbCanMessageID)
    (FallbackSigOutput_P.Constant14_Value_e & 2047);

  /* DataTypeConversion: '<S47>/Data Type Conversion16' incorporates:
   *  Constant: '<S47>/Constant15'
   */
  DataTypeConversion16 = (FrontCameraCanMessageID)
    (FallbackSigOutput_P.Constant15_Value & 2047);

  /* Switch: '<S57>/Switch8' incorporates:
   *  Constant: '<S49>/Constant19'
   *  Inport: '<Root>/CSI_LaneInfo'
   *  Product: '<S49>/Product'
   */
  rtb_Min2 = FallbackSigOutput_P.Constant19_Value_h *
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo())
    ->CSI_LaneTimeStamp;

  /* DataTypeConversion: '<S49>/Data Type Conversion61' */
  tmp_2 = fmodf(floorf(rtb_Min2), 4.2949673E+9F);

  /* Math: '<S49>/Mod' incorporates:
   *  Constant: '<S49>/Constant20'
   *  DataTypeConversion: '<S49>/Data Type Conversion61'
   */
  if (FallbackSigOutput_P.Constant20_Value_j == 0U) {
    y = tmp_2 < 0.0F ? (uint32)-(sint32)(uint32)-tmp_2 : (uint32)tmp_2;
  } else {
    y = (tmp_2 < 0.0F ? (uint32)-(sint32)(uint32)-tmp_2 : (uint32)tmp_2) %
      FallbackSigOutput_P.Constant20_Value_j;
  }

  /* End of Math: '<S49>/Mod' */

  /* DataTypeConversion: '<S49>/Data Type Conversion63' incorporates:
   *  Inport: '<Root>/FRS_Status_FRS_Msg_AliveCounter'
   */
  rtb_DataTypeConversion63 =
    Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Msg_AliveCounter();

  /* Switch: '<S49>/Switch' incorporates:
   *  DataTypeConversion: '<S49>/Data Type Conversion62'
   *  RelationalOperator: '<S49>/Equal3'
   *  UnitDelay: '<S49>/Unit Delay'
   */
  if ((uint8)y == FallbackSigOutput_DW.UnitDelay_DSTATE_ls[0]) {
    /* Switch: '<S49>/Switch' incorporates:
     *  Constant: '<S49>/Constant18'
     *  Sum: '<S49>/Add'
     *  UnitDelay: '<S49>/Unit Delay1'
     */
    rtb_Switch_i1_idx_0 = EAD_SysCycleTime_P +
      FallbackSigOutput_DW.UnitDelay1_DSTATE[0];
  } else {
    /* Switch: '<S49>/Switch' incorporates:
     *  Constant: '<S49>/IAM_Ts_P2'
     */
    rtb_Switch_i1_idx_0 = FallbackSigOutput_P.IAM_Ts_P2_Value_j;
  }

  if (rtb_DataTypeConversion63 == FallbackSigOutput_DW.UnitDelay_DSTATE_ls[1]) {
    /* Switch: '<S49>/Switch' incorporates:
     *  Constant: '<S49>/Constant18'
     *  Sum: '<S49>/Add'
     *  UnitDelay: '<S49>/Unit Delay1'
     */
    rtb_Switch_i1_idx_1 = EAD_SysCycleTime_P +
      FallbackSigOutput_DW.UnitDelay1_DSTATE[1];
  } else {
    /* Switch: '<S49>/Switch' incorporates:
     *  Constant: '<S49>/IAM_Ts_P2'
     */
    rtb_Switch_i1_idx_1 = FallbackSigOutput_P.IAM_Ts_P2_Value_j;
  }

  /* End of Switch: '<S49>/Switch' */

  /* SignalConversion: '<S49>/Signal Conversion15' incorporates:
   *  Constant: '<S49>/Constant17'
   *  RelationalOperator: '<S49>/GreaterThan'
   */
  EAD_FrontCameraCanTimeout = (rtb_Switch_i1_idx_0 >= EAD_CanTimeoutThd_P);

  /* Outport: '<Root>/FallbackSystemStatus_FrontCameraCanTimeout' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion32'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimeout
    (EAD_FrontCameraCanTimeout);

  /* SignalConversion: '<S49>/Signal Conversion16' incorporates:
   *  Constant: '<S49>/Constant17'
   *  RelationalOperator: '<S49>/GreaterThan'
   */
  EAD_FrontRadarCanTimeout = (rtb_Switch_i1_idx_1 >= EAD_CanTimeoutThd_P);

  /* Outport: '<Root>/FallbackSystemStatus_FrontRadarCanTimeout' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion33'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimeout
    (EAD_FrontRadarCanTimeout);

  /* RelationalOperator: '<S57>/Less Than' incorporates:
   *  Constant: '<S49>/Constant8'
   *  Inport: '<Root>/CSI_LaneInfo'
   *  RelationalOperator: '<S49>/Equal1'
   */
  rtb_LessThan =
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo())
     ->CSI_CalibrationStatus == FallbackSigOutput_P.Constant8_Value_j0);

  /* Logic: '<S49>/AND1' incorporates:
   *  Logic: '<S49>/NOT'
   */
  EAD_FrontCameraCalibrationEnable = (rtb_LessThan &&
    (!EAD_FrontCameraCanTimeout));

  /* Switch: '<S47>/Switch' */
  if (EAD_FrontCameraCalibrationEnable) {
    /* DataTypeConversion: '<S47>/Data Type Conversion36' incorporates:
     *  Constant: '<S47>/Constant17'
     */
    DataTypeConversion36 = FallbackSigOutput_P.Constant17_Value_l;
  } else {
    /* DataTypeConversion: '<S47>/Data Type Conversion36' incorporates:
     *  Constant: '<S47>/Constant18'
     */
    DataTypeConversion36 = FallbackSigOutput_P.Constant18_Value_cm;
  }

  /* End of Switch: '<S47>/Switch' */

  /* SignalConversion: '<S49>/Signal Conversion32' incorporates:
   *  Constant: '<S49>/Constant23'
   *  Product: '<S49>/Product1'
   */
  EAD_FrontRadarCanTimer = rtb_Switch_i1_idx_0 *
    FallbackSigOutput_P.Constant23_Value;

  /* DataTypeConversion: '<S47>/Data Type Conversion51' */
  tmp_2 = fmodf(floorf(EAD_FrontRadarCanTimer / 10.0F), 65536.0F);

  /* SignalConversion: '<S49>/Signal Conversion33' incorporates:
   *  Constant: '<S49>/Constant23'
   *  Product: '<S49>/Product1'
   */
  EAD_FrontCameraCanTimer = rtb_Switch_i1_idx_1 *
    FallbackSigOutput_P.Constant23_Value;

  /* DataTypeConversion: '<S47>/Data Type Conversion52' */
  tmp_1 = fmodf(floorf(EAD_FrontCameraCanTimer / 10.0F), 65536.0F);

  /* Logic: '<S49>/AND' incorporates:
   *  Constant: '<S49>/Constant21'
   *  DataTypeConversion: '<S49>/Data Type Conversion64'
   *  Inport: '<Root>/FRS_Status_FRS_Status_MisAlign'
   *  Logic: '<S49>/NOT1'
   *  RelationalOperator: '<S49>/Equal2'
   */
  EAD_FrontRadarCalibrationEnable = ((!EAD_FrontRadarCanTimeout) &&
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_FRS_Status_FRS_Status_MisAlign() ==
     FallbackSigOutput_P.Constant21_Value_o));

  /* Switch: '<S47>/Switch1' */
  if (EAD_FrontRadarCalibrationEnable) {
    /* DataTypeConversion: '<S47>/Data Type Conversion97' incorporates:
     *  Constant: '<S47>/Constant19'
     */
    DataTypeConversion97 = (FrontRadarCalibrationStatus)
      (FallbackSigOutput_P.Constant19_Value_n & 15);
  } else {
    /* DataTypeConversion: '<S47>/Data Type Conversion97' incorporates:
     *  Constant: '<S47>/Constant20'
     */
    DataTypeConversion97 = (FrontRadarCalibrationStatus)
      (FallbackSigOutput_P.Constant20_Value_i & 15);
  }

  /* End of Switch: '<S47>/Switch1' */

  /* DataTypeConversion: '<S47>/Data Type Conversion17' incorporates:
   *  Constant: '<S47>/Constant16'
   */
  DataTypeConversion17 = (FrontRadarCanMessageID)
    (FallbackSigOutput_P.Constant16_Value_o & 2047);

  /* Sum: '<S54>/FixPt Sum1' incorporates:
   *  Constant: '<S54>/FixPt Constant'
   *  UnitDelay: '<S53>/Output'
   */
  rtb_FixPtSum1_a = (uint8)((uint32)FallbackSigOutput_DW.Output_DSTATE_l +
    FallbackSigOutput_P.FixPtConstant_Value_e);

  /* Switch: '<S55>/FixPt Switch' */
  if (rtb_FixPtSum1_a > FallbackSigOutput_P.CounterLimited1_uplimit_d) {
    /* Switch: '<S55>/FixPt Switch' incorporates:
     *  Constant: '<S55>/Constant'
     */
    rtb_FixPtSum1_a = FallbackSigOutput_P.Constant_Value_p;
  }

  /* End of Switch: '<S55>/FixPt Switch' */

  /* Outport: '<Root>/FallbackSystemStatus_SystemStatusRollingCounter' incorporates:
   *  UnitDelay: '<S53>/Output'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusRollingCounter
    (FallbackSigOutput_DW.Output_DSTATE_l);

  /* SignalConversion: '<S58>/Signal Conversion' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawAcuFbMid3CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid3SsmCounter0Timeout;

  /* Logic: '<S58>/OR3' incorporates:
   *  Constant: '<S58>/Parameter13'
   */
  EAD_AcuFbMid3CanError = (EAD_AcuFbMid3CanError_P || EAD_RawAcuFbMid3CanError);

  /* SignalConversion: '<S58>/Signal Conversion1' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawAcuFbMid5CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid5SsmCounter0Timeout;

  /* Logic: '<S58>/OR4' incorporates:
   *  Constant: '<S58>/Parameter14'
   */
  EAD_AcuFbMid5CanError = (EAD_AcuFbMid5CanError_P || EAD_RawAcuFbMid5CanError);

  /* SignalConversion: '<S58>/Signal Conversion2' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawAcuFbMid6CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuMid6SsmCounter0Timeout;

  /* Logic: '<S58>/OR5' incorporates:
   *  Constant: '<S58>/Parameter15'
   */
  EAD_AcuFbMid6CanError = (EAD_AcuFbMid6CanError_P || EAD_RawAcuFbMid6CanError);

  /* SignalConversion: '<S58>/Signal Conversion3' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_AcuFbFdCanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_AcuFbCanTimeout;

  /* SignalConversion: '<S58>/Signal Conversion5' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawVehFbMid3CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid3SsmCounter0Timeout;

  /* Logic: '<S58>/OR1' incorporates:
   *  Constant: '<S58>/Parameter12'
   */
  EAD_VehFbMid3CanError = (EAD_VehFbMid3CanError_P || EAD_RawVehFbMid3CanError);

  /* SignalConversion: '<S58>/Signal Conversion4' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawVehFbMid5CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid5SsmCounter0Timeout;

  /* Logic: '<S58>/OR2' incorporates:
   *  Constant: '<S58>/Parameter11'
   */
  EAD_VehFbMid5CanError = (EAD_VehFbMid5CanError_P || EAD_RawVehFbMid5CanError);

  /* SignalConversion: '<S58>/Signal Conversion6' incorporates:
   *  Inport: '<Root>/VSI_McuCanTimeout'
   */
  EAD_RawVehFbMid6CanError =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout
     ())->VSI_VehMid6SsmCounter0Timeout;

  /* Logic: '<S58>/OR' incorporates:
   *  Constant: '<S58>/Parameter10'
   */
  EAD_VehFbMid6CanError = (EAD_VehFbMid6CanError_P || EAD_RawVehFbMid6CanError);

  /* SignalConversion: '<S58>/Signal Conversion7' */
  EAD_RawCameraCanError = EAD_FrontCameraCanTimeout;

  /* Logic: '<S58>/OR6' incorporates:
   *  Constant: '<S58>/Parameter18'
   */
  EAD_CameraCanError = (EAD_CameraCanError_P || EAD_RawCameraCanError);

  /* SignalConversion: '<S58>/Signal Conversion8' */
  EAD_RawRadarCanError = EAD_FrontRadarCanTimeout;

  /* Logic: '<S58>/OR7' incorporates:
   *  Constant: '<S58>/Parameter20'
   */
  EAD_RadarCanError = (EAD_RadarCanError_P || EAD_RawRadarCanError);

  /* Outputs for Iterator SubSystem: '<S58>/Bool2UINT16' incorporates:
   *  ForIterator: '<S65>/For Iterator'
   */
  /* InitializeConditions for UnitDelay: '<S65>/Unit Delay' */
  UnitDelay_DSTATE_fb = FallbackSigOutput_P.UnitDelay_InitialCondition_pb;
  if (0 < FallbackSigOutput_ConstB.Width) {
    /* DataTypeConversion: '<S65>/Data Type Conversion' incorporates:
     *  Constant: '<S58>/Parameter16'
     *  Constant: '<S58>/Parameter17'
     */
    tmp[0] = (sint8)EAD_AcuFbMid2CanError_P;
    tmp[1] = (sint8)EAD_AcuFbMid3CanError;
    tmp[2] = (sint8)EAD_AcuFbMid5CanError;
    tmp[3] = (sint8)EAD_AcuFbMid6CanError;
    tmp[4] = (sint8)EAD_AcuFbFdCanError;
    tmp[5] = (sint8)EAD_VehFbMid2CanError_P;
    tmp[6] = (sint8)EAD_VehFbMid3CanError;
    tmp[7] = (sint8)EAD_VehFbMid5CanError;
    tmp[8] = (sint8)EAD_VehFbMid6CanError;
    tmp[9] = (sint8)EAD_CameraCanError;
    tmp[10] = (sint8)EAD_RadarCanError;
  }

  for (s65_iter = 0U; s65_iter < FallbackSigOutput_ConstB.Width; s65_iter++) {
    /* S-Function (sfix_bitop): '<S65>/Bitwise AND1' incorporates:
     *  ArithShift: '<S65>/Shift Arithmetic'
     *  Constant: '<S65>/Constant'
     *  MultiPortSwitch: '<S65>/Index Vector'
     *  S-Function (sfix_bitop): '<S65>/Bitwise AND'
     *  UnitDelay: '<S65>/Unit Delay'
     */
    EAD_RawSelfCheckStatus = (uint16)((uint16)((uint16)((uint16)tmp[s65_iter] &
      FallbackSigOutput_P.Constant_Value_l) << (uint32)s65_iter) |
      UnitDelay_DSTATE_fb);

    /* Update for UnitDelay: '<S65>/Unit Delay' */
    UnitDelay_DSTATE_fb = EAD_RawSelfCheckStatus;
  }

  /* End of Outputs for SubSystem: '<S58>/Bool2UINT16' */

  /* Switch: '<S58>/Switch' incorporates:
   *  Constant: '<S58>/Parameter'
   */
  if (EAD_CswSelfCheckStatus_P) {
    /* Switch: '<S58>/Switch' incorporates:
     *  Constant: '<S58>/Parameter1'
     */
    EAD_SelfCheckStatus = EAD_SelfCheckStatus_P;
  } else {
    /* Switch: '<S58>/Switch' */
    EAD_SelfCheckStatus = EAD_RawSelfCheckStatus;
  }

  /* End of Switch: '<S58>/Switch' */

  /* Outport: '<Root>/FbAcuAvailable_FallbackSelfCheckStatus' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion45'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FallbackSelfCheckStatus
    (EAD_SelfCheckStatus);

  /* Outputs for Iterator SubSystem: '<S56>/Bool2UINT8' incorporates:
   *  ForIterator: '<S59>/For Iterator'
   */
  /* InitializeConditions for UnitDelay: '<S59>/Unit Delay' */
  s65_iter = FallbackSigOutput_P.UnitDelay_InitialCondition_m;
  if (0 < FallbackSigOutput_ConstB.Width_p) {
    /* DataTypeConversion: '<S59>/Data Type Conversion' incorporates:
     *  Constant: '<S56>/Constant23'
     *  Constant: '<S56>/Constant24'
     *  Constant: '<S56>/Constant25'
     *  Constant: '<S56>/Constant26'
     *  Constant: '<S56>/Constant27'
     *  Constant: '<S56>/Constant28'
     *  Logic: '<S56>/NOT'
     *  Logic: '<S56>/NOT1'
     */
    rtb_LessThan_0[0] = (sint8)!EAD_FrontCameraCalibrationEnable;
    rtb_LessThan_0[1] = (sint8)!EAD_FrontRadarCalibrationEnable;
    rtb_LessThan_0[2] = (sint8)FallbackSigOutput_P.Constant23_Value_l;
    rtb_LessThan_0[3] = (sint8)FallbackSigOutput_P.Constant24_Value_b;
    rtb_LessThan_0[4] = (sint8)FallbackSigOutput_P.Constant25_Value;
    rtb_LessThan_0[5] = (sint8)FallbackSigOutput_P.Constant26_Value;
    rtb_LessThan_0[6] = (sint8)FallbackSigOutput_P.Constant27_Value;
    rtb_LessThan_0[7] = (sint8)FallbackSigOutput_P.Constant28_Value;
  }

  for (s59_iter = 0U; s59_iter < FallbackSigOutput_ConstB.Width_p; s59_iter++) {
    /* S-Function (sfix_bitop): '<S59>/Bitwise AND1' incorporates:
     *  ArithShift: '<S59>/Shift Arithmetic'
     *  Constant: '<S59>/Constant'
     *  MultiPortSwitch: '<S59>/Index Vector'
     *  S-Function (sfix_bitop): '<S59>/Bitwise AND'
     *  UnitDelay: '<S59>/Unit Delay'
     */
    rtb_BitwiseAND1 = (uint8)((uint8)((uint8)((uint8)rtb_LessThan_0[s59_iter] &
      FallbackSigOutput_P.Constant_Value_m) << (uint32)s59_iter) | s65_iter);

    /* Update for UnitDelay: '<S59>/Unit Delay' */
    s65_iter = rtb_BitwiseAND1;
  }

  /* End of Outputs for SubSystem: '<S56>/Bool2UINT8' */

  /* Switch: '<S56>/Switch1' incorporates:
   *  Constant: '<S56>/Parameter2'
   */
  if (EAD_CswSensor1v1rStatus_P) {
    /* Switch: '<S56>/Switch1' incorporates:
     *  Constant: '<S56>/Parameter3'
     */
    EAD_Sensor1v1rStatus = EAD_Sensor1v1rStatus_P;
  } else {
    /* Switch: '<S56>/Switch1' */
    EAD_Sensor1v1rStatus = rtb_BitwiseAND1;
  }

  /* End of Switch: '<S56>/Switch1' */

  /* Outport: '<Root>/FbAcuAvailable_Sensor1v1rStatus' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_Sensor1v1rStatus
    (EAD_Sensor1v1rStatus);

  /* Switch: '<S57>/Switch3' incorporates:
   *  Constant: '<S57>/Parameter6'
   */
  if (EAD_CswMcuStatus_P) {
    /* Switch: '<S57>/Switch3' incorporates:
     *  Constant: '<S57>/Parameter7'
     */
    EAD_McuStatus = EAD_McuStatus_P;
  } else {
    /* Switch: '<S57>/Switch3' */
    EAD_McuStatus = FBS_DebugInfo.EAD_SystemState;
  }

  /* End of Switch: '<S57>/Switch3' */

  /* Outport: '<Root>/FbAcuAvailable_McuStatus' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_McuStatus
    (EAD_McuStatus);

  /* Switch: '<S57>/Switch4' incorporates:
   *  Constant: '<S57>/Parameter8'
   */
  if (EAD_CswFbAcuReserved_P) {
    /* Switch: '<S57>/Switch4' incorporates:
     *  Constant: '<S57>/Parameter9'
     */
    EAD_McuVersion = EAD_FbAcuReserved_P;
  } else {
    /* Switch: '<S57>/Switch4' incorporates:
     *  Constant: '<S57>/Parameter'
     */
    EAD_McuVersion = EAD_AswVersion_P;
  }

  /* End of Switch: '<S57>/Switch4' */

  /* Outport: '<Root>/FbAcuAvailable_FbAcuReserved' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion3'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuReserved
    (EAD_McuVersion);

  /* UnitDelay: '<S60>/Output' */
  EAD_FbAcuRollingCounter = FallbackSigOutput_DW.Output_DSTATE_lj;

  /* Outport: '<Root>/FbAcuAvailable_FbAcuRollingCounter' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_FbAcuRollingCounter
    (EAD_FbAcuRollingCounter);

  /* Switch: '<S57>/Switch8' incorporates:
   *  Abs: '<S57>/Abs'
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  rtb_Min2 = fabsf
    ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
     ->VSI_LongitudinalVelocity);

  /* Switch: '<S61>/Switch' incorporates:
   *  Constant: '<S57>/Constant18'
   *  RelationalOperator: '<S57>/Less Than'
   */
  if (rtb_Min2 < FallbackSigOutput_P.Constant18_Value_c) {
    /* Switch: '<S61>/Switch' incorporates:
     *  Constant: '<S57>/Constant20'
     *  Sum: '<S61>/Add'
     *  UnitDelay: '<S61>/Unit Delay'
     */
    rtb_Switch_j = FallbackSigOutput_P.Constant20_Value_h +
      FallbackSigOutput_DW.UnitDelay_DSTATE_n;

    /* MultiPortSwitch: '<S64>/Multiport Switch2' incorporates:
     *  Constant: '<S57>/Constant19'
     *  MultiPortSwitch: '<S64>/Multiport Switch1'
     *  RelationalOperator: '<S61>/GreaterThan'
     */
    if (rtb_Switch_j < FallbackSigOutput_P.Constant19_Value_m) {
      /* MultiPortSwitch: '<S64>/Multiport Switch1' incorporates:
       *  MultiPortSwitch: '<S64>/Multiport Switch2'
       *  UnitDelay: '<S64>/Unit Delay'
       */
      rtb_LessThan = FallbackSigOutput_DW.UnitDelay_DSTATE_eyn;
    } else {
      /* MultiPortSwitch: '<S64>/Multiport Switch1' incorporates:
       *  Constant: '<S64>/Constant7'
       *  MultiPortSwitch: '<S64>/Multiport Switch2'
       */
      rtb_LessThan = FallbackSigOutput_P.Constant7_Value_gs;
    }

    /* End of MultiPortSwitch: '<S64>/Multiport Switch2' */
  } else {
    /* Switch: '<S61>/Switch' incorporates:
     *  Constant: '<S61>/IAM_Ts_P2'
     */
    rtb_Switch_j = FallbackSigOutput_P.IAM_Ts_P2_Value_mh;

    /* MultiPortSwitch: '<S64>/Multiport Switch1' incorporates:
     *  Constant: '<S64>/Constant6'
     */
    rtb_LessThan = FallbackSigOutput_P.Constant6_Value_n;
  }

  /* End of Switch: '<S61>/Switch' */

  /* Switch: '<S57>/Switch2' incorporates:
   *  Constant: '<S57>/Constant14'
   *  Constant: '<S57>/Parameter19'
   *  Constant: '<S57>/Parameter4'
   *  Inport: '<Root>/VSI_VehicleInfo'
   *  Logic: '<S57>/AND2'
   *  RelationalOperator: '<S57>/Equal'
   *  Switch: '<S57>/Switch5'
   *  Switch: '<S57>/Switch7'
   */
  if (EAD_CswVehControlStatus_P) {
    /* Switch: '<S57>/Switch2' incorporates:
     *  Constant: '<S57>/Parameter5'
     */
    EAD_VehControlStatus = EAD_VehControlStatus_P;
  } else if ((FBS_DebugInfo.EAD_SystemState !=
              FallbackSigOutput_P.Constant14_Value_g) && EAD_CswOutOfOddDetect_P)
  {
    /* Switch: '<S57>/Switch2' incorporates:
     *  Constant: '<S57>/Constant15'
     *  Switch: '<S57>/Switch7'
     */
    EAD_VehControlStatus = FallbackSigOutput_P.Constant15_Value_i;
  } else if
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
        ())->VSI_FallbackActive) {
    /* RelationalOperator: '<S57>/Equal1' incorporates:
     *  Constant: '<S57>/Constant6'
     *  Inport: '<Root>/VSI_VehicleInfo'
     *  Switch: '<S57>/Switch5'
     *  Switch: '<S57>/Switch7'
     */
    rtb_NOT1_a =
      ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
        ())->VSI_FallbackMode == FallbackSigOutput_P.Constant6_Value_i);

    /* Switch: '<S57>/Switch9' incorporates:
     *  Switch: '<S57>/Switch5'
     *  Switch: '<S57>/Switch7'
     */
    if (rtb_NOT1_a) {
      /* Switch: '<S57>/Switch1' */
      if (rtb_LessThan) {
        /* Switch: '<S57>/Switch2' incorporates:
         *  Constant: '<S57>/Constant1'
         *  Switch: '<S57>/Switch5'
         *  Switch: '<S57>/Switch9'
         */
        EAD_VehControlStatus = FallbackSigOutput_P.Constant1_Value_i;
      } else {
        /* Switch: '<S57>/Switch2' incorporates:
         *  Constant: '<S57>/Constant2'
         *  Switch: '<S57>/Switch5'
         *  Switch: '<S57>/Switch9'
         */
        EAD_VehControlStatus = FallbackSigOutput_P.Constant2_Value_c;
      }

      /* End of Switch: '<S57>/Switch1' */
    } else {
      /* RelationalOperator: '<S57>/Equal4' incorporates:
       *  Constant: '<S57>/Constant11'
       *  Inport: '<Root>/VSI_VehicleInfo'
       */
      rtb_NOT1_a =
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
          ())->VSI_FallbackMode == FallbackSigOutput_P.Constant11_Value_e);

      /* RelationalOperator: '<S57>/Equal3' incorporates:
       *  Constant: '<S57>/Constant8'
       *  Inport: '<Root>/VSI_VehicleInfo'
       */
      rtb_Equal3 =
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
          ())->VSI_FallbackMode == FallbackSigOutput_P.Constant8_Value_jv);

      /* RelationalOperator: '<S57>/Equal2' incorporates:
       *  Constant: '<S57>/Constant7'
       *  Inport: '<Root>/VSI_VehicleInfo'
       */
      rtb_Equal2_b =
        ((Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo
          ())->VSI_FallbackMode == FallbackSigOutput_P.Constant7_Value_ov);

      /* Switch: '<S57>/Switch11' incorporates:
       *  Logic: '<S57>/OR'
       *  Switch: '<S57>/Switch6'
       */
      if (rtb_Equal2_b || rtb_Equal3 || rtb_NOT1_a) {
        /* Switch: '<S57>/Switch10' */
        if (rtb_LessThan) {
          /* Switch: '<S57>/Switch2' incorporates:
           *  Constant: '<S57>/Constant3'
           *  Switch: '<S57>/Switch11'
           */
          EAD_VehControlStatus = FallbackSigOutput_P.Constant3_Value_h;
        } else {
          /* Switch: '<S57>/Switch2' incorporates:
           *  Constant: '<S57>/Constant5'
           *  Switch: '<S57>/Switch11'
           */
          EAD_VehControlStatus = FallbackSigOutput_P.Constant5_Value_o;
        }

        /* End of Switch: '<S57>/Switch10' */
      } else if (rtb_LessThan) {
        /* Switch: '<S57>/Switch6' incorporates:
         *  Constant: '<S57>/Constant22'
         *  Switch: '<S57>/Switch11'
         *  Switch: '<S57>/Switch2'
         */
        EAD_VehControlStatus = FallbackSigOutput_P.Constant22_Value;
      } else {
        /* Switch: '<S57>/Switch2' incorporates:
         *  Constant: '<S57>/Constant4'
         *  Switch: '<S57>/Switch11'
         */
        EAD_VehControlStatus = FallbackSigOutput_P.Constant4_Value_n;
      }

      /* End of Switch: '<S57>/Switch11' */
    }

    /* End of Switch: '<S57>/Switch9' */
  } else {
    /* Switch: '<S57>/Switch2' incorporates:
     *  Constant: '<S57>/Constant9'
     *  Switch: '<S57>/Switch5'
     *  Switch: '<S57>/Switch7'
     */
    EAD_VehControlStatus = FallbackSigOutput_P.Constant9_Value_b;
  }

  /* End of Switch: '<S57>/Switch2' */

  /* Outport: '<Root>/FbAcuAvailable_VehControlStatus' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FbAcuAvailable_VehControlStatus
    (EAD_VehControlStatus);

  /* Switch: '<S57>/Switch8' incorporates:
   *  Constant: '<S57>/Parameter21'
   */
  if (EAD_CswRawValue_P) {
    /* Switch: '<S57>/Switch8' incorporates:
     *  Constant: '<S57>/Constant10'
     *  Product: '<S57>/Product1'
     */
    rtb_Min2 = (float32)EAD_VehControlStatus *
      FallbackSigOutput_P.Constant10_Value_h;
  } else {
    /* Switch: '<S57>/Switch8' */
    rtb_Min2 = EAD_VehControlStatus;
  }

  /* End of Switch: '<S57>/Switch8' */

  /* Sum: '<S62>/FixPt Sum1' incorporates:
   *  Constant: '<S62>/FixPt Constant'
   */
  rtb_BitwiseAND1 = (uint8)((uint32)EAD_FbAcuRollingCounter +
    FallbackSigOutput_P.FixPtConstant_Value_lu);

  /* Switch: '<S63>/FixPt Switch' */
  if (rtb_BitwiseAND1 > FallbackSigOutput_P.CounterLimited1_uplimit_fk) {
    /* Switch: '<S63>/FixPt Switch' incorporates:
     *  Constant: '<S63>/Constant'
     */
    rtb_BitwiseAND1 = FallbackSigOutput_P.Constant_Value_lw;
  }

  /* End of Switch: '<S63>/FixPt Switch' */

  /* SignalConversion generated from: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve1' */
  FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve1
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve4' */
  FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve4
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve5' */
  FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve5
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_BootLoaderVersion' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_BootLoaderVersion =
    0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_BswSoftwarewareVersion' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_BswSoftwarewareVersion
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_FrontCameraFailureStatus' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_FrontCameraFailureStatus
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_FrontRadarFailureStatus' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_FrontRadarFailureStatus
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_SystemStatusReserved1' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved1
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_SystemStatusReserved2' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved2
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_SystemStatusReserved3' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved3
    = 0U;

  /* SignalConversion generated from: '<S2>/FallbackSystemStatus_SystemStatusReserved4' */
  FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved4
    = 0U;

  /* SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_AdFusedFricEstimn' */
  FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_AdFusedFricEstimn =
    FallbackSigOutput_rtZSG_AdFusedFricEstimn_adt;

  /* SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_SwtExtrLiFromAPI' */
  FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_SwtExtrLiFromAPI =
    FallbackSigOutput_rtZSG_SwtExtrLiFromAPI;

  /* SignalConversion generated from: '<S2>/VIMMid3CanFr13_SG_AdFreeDst' */
  FallbackSigOutput_B.OutportBufferForVIMMid3CanFr13_SG_AdFreeDst =
    FallbackSigOutput_rtZSG_AdFreeDst_adt;

  /* Chart: '<S75>/TaskForRadar' */
  tmp_0 = (sint32)(FallbackSigOutput_DW.TimeCntr_e + 1U);
  if (FallbackSigOutput_DW.TimeCntr_e + 1U > 255U) {
    tmp_0 = 255;
  }

  FallbackSigOutput_DW.TimeCntr_e = (uint8)tmp_0;
  s65_iter = (uint8)((uint8)((uint32)FallbackSigOutput_DW.TimeCntr_e - ((sint32)
    ((uint32)FallbackSigOutput_DW.TimeCntr_e >> 1) << 1)) == 1);
  if (FallbackSigOutput_DW.TimeCntr_e > 100) {
    FallbackSigOutput_DW.TimeCntr_e = 1U;
  }

  /* End of Chart: '<S75>/TaskForRadar' */

  /* Outputs for Enabled SubSystem: '<S70>/ESC_DA_MESSAGE_E2E' incorporates:
   *  EnablePort: '<S78>/Enable'
   */
  if (s65_iter > 0) {
    /* UnitDelay: '<S79>/Output' */
    VTS_AliveCounterEscDa = FallbackSigOutput_DW.Output_DSTATE_b;

    /* S-Function (scanpack): '<S78>/CAN_Pack_ESC_DA_MESSAGE' incorporates:
     *  Constant: '<S78>/Constant1'
     *  Constant: '<S78>/Constant10'
     *  Constant: '<S78>/Constant11'
     *  Constant: '<S78>/Constant12'
     *  Constant: '<S78>/Constant13'
     *  Constant: '<S78>/Constant14'
     *  Constant: '<S78>/Constant15'
     *  Constant: '<S78>/Constant2'
     *  Constant: '<S78>/Constant3'
     *  Constant: '<S78>/Constant4'
     *  Constant: '<S78>/Constant5'
     *  Constant: '<S78>/Constant6'
     *  Constant: '<S78>/Constant7'
     *  Constant: '<S78>/Constant8'
     *  Constant: '<S78>/Constant9'
     */
    /* S-Function (scanpack): '<S78>/CAN_Pack_ESC_DA_MESSAGE' */
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.ID = 295U;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 8
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant4_Value_bh);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] | (uint8)
                ((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 9
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant5_Value_a);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 5
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant6_Value_dc);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 4
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant7_Value_hb);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 4));
            }
          }
        }
      }

      /* --------------- START Packing signal 4 ------------------
       *  startBit                = 6
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant8_Value_f);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 6));
            }
          }
        }
      }

      /* --------------- START Packing signal 5 ------------------
       *  startBit                = 7
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant9_Value_o);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 7));
            }
          }
        }
      }

      /* --------------- START Packing signal 6 ------------------
       *  startBit                = 10
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant3_Value_dg);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 2));
            }
          }
        }
      }

      /* --------------- START Packing signal 7 ------------------
       *  startBit                = 11
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant2_Value_b);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 3));
            }
          }
        }
      }

      /* --------------- START Packing signal 8 ------------------
       *  startBit                = 17
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant1_Value_ib);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 9 ------------------
       *  startBit                = 48
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_AliveCounterEscDa);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[6] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[6] | (uint8)
                ((uint8)(packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 10 ------------------
       *  startBit                = 56
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant11_Value_i);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[7] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[7] | (uint8)
                (packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 11 ------------------
       *  startBit                = 16
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant10_Value_o);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] | (uint8)
                ((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 12 ------------------
       *  startBit                = 3
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant12_Value_e);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 3));
            }
          }
        }
      }

      /* --------------- START Packing signal 13 ------------------
       *  startBit                = 2
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant13_Value_l);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[0] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 2));
            }
          }
        }
      }

      /* --------------- START Packing signal 14 ------------------
       *  startBit                = 23
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant14_Value_eo);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[2] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x1U) << 7));
            }
          }
        }
      }

      /* --------------- START Packing signal 15 ------------------
       *  startBit                = 14
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant15_Value_i1);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data[1] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x3U) << 6));
            }
          }
        }
      }
    }

    /* CCaller: '<S78>/C Caller3' */
    VTS_ChecksumEscDa = crc_cal(FallbackSigOutput_B.CAN_Pack_ESC_DA_MESSAGE.Data);

    /* Sum: '<S80>/FixPt Sum1' incorporates:
     *  Constant: '<S80>/FixPt Constant'
     */
    s59_iter = (uint8)((uint32)VTS_AliveCounterEscDa +
                       FallbackSigOutput_P.FixPtConstant_Value);

    /* Switch: '<S81>/FixPt Switch' */
    if (s59_iter > FallbackSigOutput_P.CounterLimited1_uplimit) {
      /* Update for UnitDelay: '<S79>/Output' incorporates:
       *  Constant: '<S81>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_b =
        FallbackSigOutput_P.Constant_Value_c;
    } else {
      /* Update for UnitDelay: '<S79>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_b = s59_iter;
    }

    /* End of Switch: '<S81>/FixPt Switch' */
  }

  /* End of Outputs for SubSystem: '<S70>/ESC_DA_MESSAGE_E2E' */

  /* SignalConversion: '<S67>/Signal Conversion' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdFrntSafeLe =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdFrntSafeLe;

  /* SignalConversion: '<S67>/Signal Conversion1' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdFrntSafeLeQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdFrntSafeLeQf;

  /* SignalConversion: '<S67>/Signal Conversion2' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdFrntSafeRi =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdFrntSafeRi;

  /* SignalConversion: '<S67>/Signal Conversion3' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdFrntSafeRiQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdFrntSafeRiQf;

  /* Outputs for Enabled SubSystem: '<S66>/ESC_FrontWheelSpeedKPH' incorporates:
   *  EnablePort: '<S71>/Enable1'
   */
  if (s65_iter > 0) {
    /* Abs: '<S82>/Abs' incorporates:
     *  Constant: '<S82>/Constant'
     *  Constant: '<S82>/Parameter'
     *  Product: '<S82>/Product'
     *  Product: '<S82>/Product1'
     */
    VTS_RawWheelSpeedFrntLe = fabsf(VTS_PrimWhlAgSpdFrntSafeLe *
      VTS_TireRadiusFrntLe_P * FallbackSigOutput_P.Constant_Value_g);

    /* Switch: '<S82>/Switch' incorporates:
     *  Abs: '<S82>/Abs1'
     *  Constant: '<S82>/Constant3'
     *  RelationalOperator: '<S82>/Relational Operator'
     */
    if (fabsf(VTS_PrimWhlAgSpdFrntSafeLe) >=
        FallbackSigOutput_P.Constant3_Value_f) {
      /* Switch: '<S82>/Switch1' incorporates:
       *  Constant: '<S82>/Constant1'
       *  RelationalOperator: '<S82>/Relational Operator1'
       */
      if (VTS_PrimWhlAgSpdFrntSafeLe >= FallbackSigOutput_P.Constant1_Value_d) {
        /* Switch: '<S82>/Switch' incorporates:
         *  Constant: '<S82>/Constant2'
         */
        VTS_RawWheelDirectionFrntLe = FallbackSigOutput_P.Constant2_Value_e;
      } else {
        /* Switch: '<S82>/Switch' incorporates:
         *  Constant: '<S82>/Constant5'
         */
        VTS_RawWheelDirectionFrntLe = FallbackSigOutput_P.Constant5_Value_mv;
      }

      /* End of Switch: '<S82>/Switch1' */
    } else {
      /* Switch: '<S82>/Switch' incorporates:
       *  Constant: '<S82>/Constant4'
       */
      VTS_RawWheelDirectionFrntLe = FallbackSigOutput_P.Constant4_Value_h;
    }

    /* End of Switch: '<S82>/Switch' */

    /* Switch: '<S82>/Switch3' incorporates:
     *  Constant: '<S82>/Parameter2'
     */
    if (VTS_CswWheelSpeedFrntLe_P) {
      /* Switch: '<S82>/Switch3' incorporates:
       *  Constant: '<S82>/Parameter1'
       */
      VTS_WheelSpeedFrntLe = VTS_WheelSpeedFrntLe_P;
    } else {
      /* Switch: '<S82>/Switch3' */
      VTS_WheelSpeedFrntLe = VTS_RawWheelSpeedFrntLe;
    }

    /* End of Switch: '<S82>/Switch3' */

    /* Switch: '<S82>/Switch4' incorporates:
     *  Constant: '<S82>/Parameter4'
     */
    if (VTS_CswWheelDirectionFrntLe_P != 0) {
      /* Switch: '<S82>/Switch4' incorporates:
       *  Constant: '<S82>/Parameter3'
       */
      VTS_WheelDirectionFrntLe = VTS_WheelDirectionFrntLe_P;
    } else {
      /* Switch: '<S82>/Switch4' */
      VTS_WheelDirectionFrntLe = VTS_RawWheelDirectionFrntLe;
    }

    /* End of Switch: '<S82>/Switch4' */

    /* Switch: '<S82>/Switch2' incorporates:
     *  Constant: '<S82>/Constant6'
     *  RelationalOperator: '<S82>/Equal'
     */
    if (VTS_PrimWhlAgSpdFrntSafeLeQf == FallbackSigOutput_P.Constant6_Value_b2)
    {
      /* Switch: '<S82>/Switch2' incorporates:
       *  Constant: '<S82>/Constant7'
       */
      VTS_RawSpeedInvalidFrntLe = FallbackSigOutput_P.Constant7_Value_jf;
    } else {
      /* Switch: '<S82>/Switch2' incorporates:
       *  Constant: '<S82>/Constant8'
       */
      VTS_RawSpeedInvalidFrntLe = FallbackSigOutput_P.Constant8_Value_c;
    }

    /* End of Switch: '<S82>/Switch2' */

    /* Switch: '<S82>/Switch5' incorporates:
     *  Constant: '<S82>/Parameter6'
     */
    if (VTS_CswSpeedInvalidFrntLe_P != 0) {
      /* Switch: '<S82>/Switch5' incorporates:
       *  Constant: '<S82>/Parameter5'
       */
      VTS_SpeedInvalidFrntLe = VTS_SpeedInvalidFrntLe_P;
    } else {
      /* Switch: '<S82>/Switch5' */
      VTS_SpeedInvalidFrntLe = VTS_RawSpeedInvalidFrntLe;
    }

    /* End of Switch: '<S82>/Switch5' */

    /* Abs: '<S83>/Abs' incorporates:
     *  Constant: '<S83>/Constant'
     *  Constant: '<S83>/Parameter'
     *  Product: '<S83>/Product'
     *  Product: '<S83>/Product1'
     */
    VTS_RawWheelSpeedFrntRi = fabsf(VTS_PrimWhlAgSpdFrntSafeRi *
      VTS_TireRadiusReRi_P * FallbackSigOutput_P.Constant_Value_k);

    /* Switch: '<S83>/Switch' incorporates:
     *  Abs: '<S83>/Abs1'
     *  Constant: '<S83>/Constant3'
     *  RelationalOperator: '<S83>/Relational Operator'
     */
    if (fabsf(VTS_PrimWhlAgSpdFrntSafeRi) >=
        FallbackSigOutput_P.Constant3_Value_d) {
      /* Switch: '<S83>/Switch1' incorporates:
       *  Constant: '<S83>/Constant1'
       *  RelationalOperator: '<S83>/Relational Operator1'
       */
      if (VTS_PrimWhlAgSpdFrntSafeRi >= FallbackSigOutput_P.Constant1_Value_j) {
        /* Switch: '<S83>/Switch' incorporates:
         *  Constant: '<S83>/Constant2'
         */
        VTS_RawWheelDirectionFrntRi = FallbackSigOutput_P.Constant2_Value_h;
      } else {
        /* Switch: '<S83>/Switch' incorporates:
         *  Constant: '<S83>/Constant5'
         */
        VTS_RawWheelDirectionFrntRi = FallbackSigOutput_P.Constant5_Value_g;
      }

      /* End of Switch: '<S83>/Switch1' */
    } else {
      /* Switch: '<S83>/Switch' incorporates:
       *  Constant: '<S83>/Constant4'
       */
      VTS_RawWheelDirectionFrntRi = FallbackSigOutput_P.Constant4_Value_l;
    }

    /* End of Switch: '<S83>/Switch' */

    /* Switch: '<S83>/Switch3' incorporates:
     *  Constant: '<S83>/Parameter2'
     */
    if (VTS_CswWheelSpeedFrntRi_P != 0) {
      /* Switch: '<S83>/Switch3' incorporates:
       *  Constant: '<S83>/Parameter1'
       */
      VTS_WheelSpeedFrntRi = VTS_WheelSpeedFrntRi_P;
    } else {
      /* Switch: '<S83>/Switch3' */
      VTS_WheelSpeedFrntRi = VTS_RawWheelSpeedFrntRi;
    }

    /* End of Switch: '<S83>/Switch3' */

    /* Switch: '<S83>/Switch4' incorporates:
     *  Constant: '<S83>/Parameter4'
     */
    if (VTS_CswWheelDirectionFrntRi_P != 0) {
      /* Switch: '<S83>/Switch4' incorporates:
       *  Constant: '<S83>/Parameter3'
       */
      VTS_WheelDirectionFrntRi = VTS_WheelDirectionFrntRi_P;
    } else {
      /* Switch: '<S83>/Switch4' */
      VTS_WheelDirectionFrntRi = VTS_RawWheelDirectionFrntRi;
    }

    /* End of Switch: '<S83>/Switch4' */

    /* Switch: '<S83>/Switch2' incorporates:
     *  Constant: '<S83>/Constant6'
     *  RelationalOperator: '<S83>/Equal'
     */
    if (VTS_PrimWhlAgSpdFrntSafeRiQf == FallbackSigOutput_P.Constant6_Value_ek)
    {
      /* Switch: '<S83>/Switch2' incorporates:
       *  Constant: '<S83>/Constant7'
       */
      VTS_RawSpeedInvalidFrntRi = FallbackSigOutput_P.Constant7_Value_e;
    } else {
      /* Switch: '<S83>/Switch2' incorporates:
       *  Constant: '<S83>/Constant8'
       */
      VTS_RawSpeedInvalidFrntRi = FallbackSigOutput_P.Constant8_Value_o;
    }

    /* End of Switch: '<S83>/Switch2' */

    /* Switch: '<S83>/Switch5' incorporates:
     *  Constant: '<S83>/Parameter6'
     */
    if (VTS_CswSpeedInvalidFrntRi_P != 0) {
      /* Switch: '<S83>/Switch5' incorporates:
       *  Constant: '<S83>/Parameter5'
       */
      VTS_SpeedInvalidFrntRi = VTS_SpeedInvalidFrntRi_P;
    } else {
      /* Switch: '<S83>/Switch5' */
      VTS_SpeedInvalidFrntRi = VTS_RawSpeedInvalidFrntRi;
    }

    /* End of Switch: '<S83>/Switch5' */

    /* Outputs for Atomic SubSystem: '<S71>/FrontWheelSpeedKPH_E2E' */
    /* UnitDelay: '<S85>/Output' */
    VTS_AliveCounterFrntWhl = FallbackSigOutput_DW.Output_DSTATE_a;

    /* S-Function (scanpack): '<S84>/CAN_Pack_FrontWheelSpeedKPH' incorporates:
     *  Constant: '<S84>/Constant1'
     *  Constant: '<S84>/Constant2'
     *  Constant: '<S84>/Constant3'
     */
    /* S-Function (scanpack): '<S84>/CAN_Pack_FrontWheelSpeedKPH' */
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.ID = 291U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 9
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_WheelDirectionFrntLe);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] |
                (uint8)((uint8)((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 8
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_SpeedInvalidFrntLe);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] |
                (uint8)((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 11
       *  length                  = 13
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.05625
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_WheelSpeedFrntLe;

          /* no offset to apply */
          result = result * (1 / 0.05625F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(8191)) {
            packedValue = (uint16) 8191;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[1] |
                (uint8)((uint16)((uint16)(packedValue & (uint16)0x1FU) << 3));
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[0] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[0] |
                (uint8)((uint16)((uint16)(packedValue & (uint16)0x1FE0U) >> 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 25
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_WheelDirectionFrntRi);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] |
                (uint8)((uint8)((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 4 ------------------
       *  startBit                = 24
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_SpeedInvalidFrntRi);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] |
                (uint8)((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 5 ------------------
       *  startBit                = 27
       *  length                  = 13
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.05625
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_WheelSpeedFrntRi;

          /* no offset to apply */
          result = result * (1 / 0.05625F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(8191)) {
            packedValue = (uint16) 8191;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[3] |
                (uint8)((uint16)((uint16)(packedValue & (uint16)0x1FU) << 3));
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[2] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[2] |
                (uint8)((uint16)((uint16)(packedValue & (uint16)0x1FE0U) >> 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 6 ------------------
       *  startBit                = 48
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_AliveCounterFrntWhl);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[6] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[6] |
                (uint8)((uint8)(packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 7 ------------------
       *  startBit                = 56
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant2_Value_fr);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[7] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[7] |
                (uint8)(packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 8 ------------------
       *  startBit                = 32
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant1_Value_jp);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[4] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[4] |
                (uint8)(packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 9 ------------------
       *  startBit                = 52
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant3_Value_l);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[6] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data[6] |
                (uint8)((uint8)((uint8)(packedValue & (uint8)0x1U) << 4));
            }
          }
        }
      }
    }

    /* CCaller: '<S84>/C Caller3' */
    VTS_ChecksumFrntWhl = crc_cal
      (FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH_j.Data);

    /* Sum: '<S86>/FixPt Sum1' incorporates:
     *  Constant: '<S86>/FixPt Constant'
     */
    s59_iter = (uint8)((uint32)VTS_AliveCounterFrntWhl +
                       FallbackSigOutput_P.FixPtConstant_Value_b);

    /* Switch: '<S87>/FixPt Switch' */
    if (s59_iter > FallbackSigOutput_P.CounterLimited1_uplimit_c) {
      /* Update for UnitDelay: '<S85>/Output' incorporates:
       *  Constant: '<S87>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_a =
        FallbackSigOutput_P.Constant_Value_gc;
    } else {
      /* Update for UnitDelay: '<S85>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_a = s59_iter;
    }

    /* End of Switch: '<S87>/FixPt Switch' */
    /* End of Outputs for SubSystem: '<S71>/FrontWheelSpeedKPH_E2E' */
  }

  /* End of Outputs for SubSystem: '<S66>/ESC_FrontWheelSpeedKPH' */

  /* SignalConversion: '<S67>/Signal Conversion4' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdReSafeLe =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdReSafeLe;

  /* SignalConversion: '<S67>/Signal Conversion5' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdReSafeLeQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdReSafeLeQf;

  /* SignalConversion: '<S67>/Signal Conversion6' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdReSafeRi =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdReSafeRi;

  /* SignalConversion: '<S67>/Signal Conversion7' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimWhlAgSpdReSafeRiQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimWhlAgSpdReSafeRiQf;

  /* Outputs for Enabled SubSystem: '<S66>/ESC_RearWheelSpeedKPH' incorporates:
   *  EnablePort: '<S72>/Enable1'
   */
  if (s65_iter > 0) {
    /* Switch: '<S89>/Switch' incorporates:
     *  Abs: '<S89>/Abs1'
     *  Constant: '<S89>/Constant3'
     *  RelationalOperator: '<S89>/Relational Operator'
     */
    if (fabsf(VTS_PrimWhlAgSpdReSafeLe) >= FallbackSigOutput_P.Constant3_Value_p)
    {
      /* Switch: '<S89>/Switch1' incorporates:
       *  Constant: '<S89>/Constant1'
       *  RelationalOperator: '<S89>/Relational Operator1'
       */
      if (VTS_PrimWhlAgSpdReSafeLe >= FallbackSigOutput_P.Constant1_Value_p) {
        /* Switch: '<S89>/Switch' incorporates:
         *  Constant: '<S89>/Constant2'
         */
        VTS_RawWheelDirectionReLe = FallbackSigOutput_P.Constant2_Value_p;
      } else {
        /* Switch: '<S89>/Switch' incorporates:
         *  Constant: '<S89>/Constant5'
         */
        VTS_RawWheelDirectionReLe = FallbackSigOutput_P.Constant5_Value_oq;
      }

      /* End of Switch: '<S89>/Switch1' */
    } else {
      /* Switch: '<S89>/Switch' incorporates:
       *  Constant: '<S89>/Constant4'
       */
      VTS_RawWheelDirectionReLe = FallbackSigOutput_P.Constant4_Value_bc;
    }

    /* End of Switch: '<S89>/Switch' */

    /* Switch: '<S89>/Switch4' incorporates:
     *  Constant: '<S89>/Parameter4'
     */
    if (VTS_CswWheelDirectionReLe_P != 0) {
      /* Switch: '<S89>/Switch4' incorporates:
       *  Constant: '<S89>/Parameter3'
       */
      VTS_WheelDirectionReLe = VTS_WheelDirectionReLe_P;
    } else {
      /* Switch: '<S89>/Switch4' */
      VTS_WheelDirectionReLe = VTS_RawWheelDirectionReLe;
    }

    /* End of Switch: '<S89>/Switch4' */

    /* Switch: '<S89>/Switch2' incorporates:
     *  Constant: '<S89>/Constant6'
     *  RelationalOperator: '<S89>/Equal'
     */
    if (VTS_PrimWhlAgSpdReSafeLeQf == FallbackSigOutput_P.Constant6_Value_p) {
      /* Switch: '<S89>/Switch2' incorporates:
       *  Constant: '<S89>/Constant7'
       */
      VTS_RawSpeedInvalidReLe = FallbackSigOutput_P.Constant7_Value_dl;
    } else {
      /* Switch: '<S89>/Switch2' incorporates:
       *  Constant: '<S89>/Constant8'
       */
      VTS_RawSpeedInvalidReLe = FallbackSigOutput_P.Constant8_Value_i;
    }

    /* End of Switch: '<S89>/Switch2' */

    /* Switch: '<S89>/Switch5' incorporates:
     *  Constant: '<S89>/Parameter6'
     */
    if (VTS_CswSpeedInvalidReLe_P != 0) {
      /* Switch: '<S89>/Switch5' incorporates:
       *  Constant: '<S89>/Parameter5'
       */
      VTS_SpeedInvalidReLe = VTS_SpeedInvalidReLe_P;
    } else {
      /* Switch: '<S89>/Switch5' */
      VTS_SpeedInvalidReLe = VTS_RawSpeedInvalidReLe;
    }

    /* End of Switch: '<S89>/Switch5' */

    /* Abs: '<S89>/Abs' incorporates:
     *  Constant: '<S89>/Constant'
     *  Constant: '<S89>/Parameter'
     *  Product: '<S89>/Product'
     *  Product: '<S89>/Product1'
     */
    VTS_RawWheelSpeedReLe = fabsf(VTS_PrimWhlAgSpdReSafeLe *
      VTS_TireRadiusReLe_P * FallbackSigOutput_P.Constant_Value_f);

    /* Switch: '<S89>/Switch3' incorporates:
     *  Constant: '<S89>/Parameter2'
     */
    if (VTS_CswWheelSpeedReLe_P != 0) {
      /* Switch: '<S89>/Switch3' incorporates:
       *  Constant: '<S89>/Parameter1'
       */
      VTS_WheelSpeedReLe = VTS_WheelSpeedReLe_P;
    } else {
      /* Switch: '<S89>/Switch3' */
      VTS_WheelSpeedReLe = VTS_RawWheelSpeedReLe;
    }

    /* End of Switch: '<S89>/Switch3' */

    /* Switch: '<S90>/Switch' incorporates:
     *  Abs: '<S90>/Abs1'
     *  Constant: '<S90>/Constant3'
     *  RelationalOperator: '<S90>/Relational Operator'
     */
    if (fabsf(VTS_PrimWhlAgSpdReSafeRi) >= FallbackSigOutput_P.Constant3_Value_n)
    {
      /* Switch: '<S90>/Switch1' incorporates:
       *  Constant: '<S90>/Constant1'
       *  RelationalOperator: '<S90>/Relational Operator1'
       */
      if (VTS_PrimWhlAgSpdReSafeRi >= FallbackSigOutput_P.Constant1_Value_g) {
        /* Switch: '<S90>/Switch' incorporates:
         *  Constant: '<S90>/Constant2'
         */
        VTS_RawWheelDirectionReRi = FallbackSigOutput_P.Constant2_Value_m;
      } else {
        /* Switch: '<S90>/Switch' incorporates:
         *  Constant: '<S90>/Constant5'
         */
        VTS_RawWheelDirectionReRi = FallbackSigOutput_P.Constant5_Value_p;
      }

      /* End of Switch: '<S90>/Switch1' */
    } else {
      /* Switch: '<S90>/Switch' incorporates:
       *  Constant: '<S90>/Constant4'
       */
      VTS_RawWheelDirectionReRi = FallbackSigOutput_P.Constant4_Value_li;
    }

    /* End of Switch: '<S90>/Switch' */

    /* Switch: '<S90>/Switch4' incorporates:
     *  Constant: '<S90>/Parameter4'
     */
    if (VTS_CswWheelDirectionReRi_P != 0) {
      /* Switch: '<S90>/Switch4' incorporates:
       *  Constant: '<S90>/Parameter3'
       */
      VTS_WheelDirectionReRi = VTS_WheelDirectionReRi_P;
    } else {
      /* Switch: '<S90>/Switch4' */
      VTS_WheelDirectionReRi = VTS_RawWheelDirectionReRi;
    }

    /* End of Switch: '<S90>/Switch4' */

    /* Switch: '<S90>/Switch2' incorporates:
     *  Constant: '<S90>/Constant6'
     *  RelationalOperator: '<S90>/Equal'
     */
    if (VTS_PrimWhlAgSpdReSafeRiQf == FallbackSigOutput_P.Constant6_Value_h5) {
      /* Switch: '<S90>/Switch2' incorporates:
       *  Constant: '<S90>/Constant7'
       */
      VTS_RawSpeedInvalidReRi = FallbackSigOutput_P.Constant7_Value_gq;
    } else {
      /* Switch: '<S90>/Switch2' incorporates:
       *  Constant: '<S90>/Constant8'
       */
      VTS_RawSpeedInvalidReRi = FallbackSigOutput_P.Constant8_Value_ez;
    }

    /* End of Switch: '<S90>/Switch2' */

    /* Switch: '<S90>/Switch5' incorporates:
     *  Constant: '<S90>/Parameter6'
     */
    if (VTS_CswSpeedInvalidReRi_P != 0) {
      /* Switch: '<S90>/Switch5' incorporates:
       *  Constant: '<S90>/Parameter5'
       */
      VTS_SpeedInvalidReRi = VTS_SpeedInvalidReRi_P;
    } else {
      /* Switch: '<S90>/Switch5' */
      VTS_SpeedInvalidReRi = VTS_RawSpeedInvalidReRi;
    }

    /* End of Switch: '<S90>/Switch5' */

    /* Abs: '<S90>/Abs' incorporates:
     *  Constant: '<S90>/Constant'
     *  Constant: '<S90>/Parameter'
     *  Product: '<S90>/Product'
     *  Product: '<S90>/Product1'
     */
    VTS_RawWheelSpeedReRi = fabsf(VTS_PrimWhlAgSpdReSafeRi *
      VTS_TireRadiusFrntRi_P * FallbackSigOutput_P.Constant_Value_d);

    /* Switch: '<S90>/Switch3' incorporates:
     *  Constant: '<S90>/Parameter2'
     */
    if (VTS_CswWheelSpeedReRi_P != 0) {
      /* Switch: '<S90>/Switch3' incorporates:
       *  Constant: '<S90>/Parameter1'
       */
      VTS_WheelSpeedReRi = VTS_WheelSpeedReRi_P;
    } else {
      /* Switch: '<S90>/Switch3' */
      VTS_WheelSpeedReRi = VTS_RawWheelSpeedReRi;
    }

    /* End of Switch: '<S90>/Switch3' */

    /* Outputs for Atomic SubSystem: '<S72>/FrontWheelSpeedKPH_E2E' */
    /* UnitDelay: '<S91>/Output' */
    VTS_AliveCounterReWhl = FallbackSigOutput_DW.Output_DSTATE_i;

    /* S-Function (scanpack): '<S88>/CAN_Pack_FrontWheelSpeedKPH' incorporates:
     *  Constant: '<S88>/Constant7'
     */
    /* S-Function (scanpack): '<S88>/CAN_Pack_FrontWheelSpeedKPH' */
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.ID = 292U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 9
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_WheelDirectionReLe);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 8
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_SpeedInvalidReLe);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] | (uint8)
                ((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 11
       *  length                  = 13
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.05625
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_WheelSpeedReLe;

          /* no offset to apply */
          result = result * (1 / 0.05625F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(8191)) {
            packedValue = (uint16) 8191;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[1] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FU) << 3));
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[0] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[0] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FE0U) >> 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 25
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_WheelDirectionReRi);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] | (uint8)
                ((uint8)((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 4 ------------------
       *  startBit                = 24
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_SpeedInvalidReRi);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] | (uint8)
                ((uint8)(packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 5 ------------------
       *  startBit                = 27
       *  length                  = 13
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.05625
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_WheelSpeedReRi;

          /* no offset to apply */
          result = result * (1 / 0.05625F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(8191)) {
            packedValue = (uint16) 8191;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[3] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FU) << 3));
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[2] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[2] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FE0U) >> 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 6 ------------------
       *  startBit                = 48
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_AliveCounterReWhl);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[6] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[6] | (uint8)
                ((uint8)(packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 7 ------------------
       *  startBit                = 56
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant7_Value_hs);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[7] =
                FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data[7] | (uint8)
                (packedValue);
            }
          }
        }
      }
    }

    /* CCaller: '<S88>/C Caller4' */
    VTS_ChecksumReWhl = crc_cal
      (FallbackSigOutput_B.CAN_Pack_FrontWheelSpeedKPH.Data);

    /* Sum: '<S92>/FixPt Sum1' incorporates:
     *  Constant: '<S92>/FixPt Constant'
     */
    s59_iter = (uint8)((uint32)VTS_AliveCounterReWhl +
                       FallbackSigOutput_P.FixPtConstant_Value_h);

    /* Switch: '<S93>/FixPt Switch' */
    if (s59_iter > FallbackSigOutput_P.CounterLimited1_uplimit_f) {
      /* Update for UnitDelay: '<S91>/Output' incorporates:
       *  Constant: '<S93>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_i =
        FallbackSigOutput_P.Constant_Value_fn;
    } else {
      /* Update for UnitDelay: '<S91>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_i = s59_iter;
    }

    /* End of Switch: '<S93>/FixPt Switch' */
    /* End of Outputs for SubSystem: '<S72>/FrontWheelSpeedKPH_E2E' */
  }

  /* End of Outputs for SubSystem: '<S66>/ESC_RearWheelSpeedKPH' */

  /* SignalConversion: '<S67>/Signal Conversion17' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimVehSpdGroupSafeNom =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimVehSpdGroupSafeNom;

  /* Outputs for Enabled SubSystem: '<S66>/ESC_Status' incorporates:
   *  EnablePort: '<S73>/Enable'
   */
  if (s65_iter > 0) {
    /* Product: '<S95>/Product' incorporates:
     *  Constant: '<S95>/Constant'
     */
    VTS_RawVehicleSpeed = VTS_PrimVehSpdGroupSafeNom *
      FallbackSigOutput_P.Constant_Value_a;

    /* Switch: '<S95>/Switch' incorporates:
     *  Constant: '<S95>/Parameter1'
     */
    if (VTS_CswVehicleSpeed_P != 0) {
      /* Switch: '<S95>/Switch' incorporates:
       *  Constant: '<S95>/Parameter'
       */
      VTS_VehicleSpeed = VTS_VehicleSpeed_P;
    } else {
      /* Switch: '<S95>/Switch' */
      VTS_VehicleSpeed = VTS_RawVehicleSpeed;
    }

    /* End of Switch: '<S95>/Switch' */

    /* Outputs for Atomic SubSystem: '<S73>/ESC_Status_E2E' */
    /* UnitDelay: '<S96>/Output' */
    VTS_EscStatusAliveCounter = FallbackSigOutput_DW.Output_DSTATE_j;

    /* Constant: '<S94>/Constant9' */
    VTS_VehicleSpeedInvalid = FallbackSigOutput_P.Constant9_Value_e;

    /* S-Function (scanpack): '<S94>/CAN_Pack_ESC_Status' incorporates:
     *  Constant: '<S94>/Constant1'
     *  Constant: '<S94>/Constant10'
     *  Constant: '<S94>/Constant11'
     *  Constant: '<S94>/Constant12'
     *  Constant: '<S94>/Constant2'
     *  Constant: '<S94>/Constant3'
     *  Constant: '<S94>/Constant4'
     *  Constant: '<S94>/Constant5'
     *  Constant: '<S94>/Constant6'
     *  Constant: '<S94>/Constant7'
     *  Constant: '<S94>/Constant8'
     */
    /* S-Function (scanpack): '<S94>/CAN_Pack_ESC_Status' */
    FallbackSigOutput_B.CAN_Pack_ESC_Status.ID = 294U;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 0
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant2_Value_k);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] | (uint8)((uint8)
                (packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 25
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant4_Value_a);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 30
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant5_Value_c2);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 6));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 1
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant1_Value_ax);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 1));
            }
          }
        }
      }

      /* --------------- START Packing signal 4 ------------------
       *  startBit                = 27
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant3_Value_ct);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[3] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x3U) << 3));
            }
          }
        }
      }

      /* --------------- START Packing signal 5 ------------------
       *  startBit                = 5
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant6_Value_bo);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 6 ------------------
       *  startBit                = 7
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant7_Value_jy);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 7));
            }
          }
        }
      }

      /* --------------- START Packing signal 7 ------------------
       *  startBit                = 40
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant8_Value_ek);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[5] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[5] | (uint8)((uint8)
                (packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 8 ------------------
       *  startBit                = 16
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant10_Value_n);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] | (uint8)((uint8)
                (packedValue & (uint8)0x1U));
            }
          }
        }
      }

      /* --------------- START Packing signal 9 ------------------
       *  startBit                = 48
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_EscStatusAliveCounter);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[6] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[6] | (uint8)((uint8)
                (packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 10 ------------------
       *  startBit                = 56
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant12_Value_k);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[7] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[7] | (uint8)
                (packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 11 ------------------
       *  startBit                = 4
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant11_Value_o);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[0] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 4));
            }
          }
        }
      }

      /* --------------- START Packing signal 12 ------------------
       *  startBit                = 19
       *  length                  = 13
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.05625
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_VehicleSpeed;

          /* no offset to apply */
          result = result * (1 / 0.05625F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(8191)) {
            packedValue = (uint16) 8191;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FU) << 3));
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[1] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[1] | (uint8)
                ((uint16)((uint16)(packedValue & (uint16)0x1FE0U) >> 5));
            }
          }
        }
      }

      /* --------------- START Packing signal 13 ------------------
       *  startBit                = 18
       *  length                  = 1
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_VehicleSpeedInvalid);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(1)) {
            packedValue = (uint8) 1;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] =
                FallbackSigOutput_B.CAN_Pack_ESC_Status.Data[2] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x1U) << 2));
            }
          }
        }
      }
    }

    /* CCaller: '<S94>/C Caller5' */
    VTS_EscStatusChecksum = crc_cal(FallbackSigOutput_B.CAN_Pack_ESC_Status.Data);

    /* Sum: '<S97>/FixPt Sum1' incorporates:
     *  Constant: '<S97>/FixPt Constant'
     */
    s59_iter = (uint8)((uint32)VTS_EscStatusAliveCounter +
                       FallbackSigOutput_P.FixPtConstant_Value_o);

    /* Switch: '<S98>/FixPt Switch' */
    if (s59_iter > FallbackSigOutput_P.CounterLimited2_uplimit) {
      /* Update for UnitDelay: '<S96>/Output' incorporates:
       *  Constant: '<S98>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_j =
        FallbackSigOutput_P.Constant_Value_o;
    } else {
      /* Update for UnitDelay: '<S96>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_j = s59_iter;
    }

    /* End of Switch: '<S98>/FixPt Switch' */
    /* End of Outputs for SubSystem: '<S73>/ESC_Status_E2E' */
  }

  /* End of Outputs for SubSystem: '<S66>/ESC_Status' */

  /* SignalConversion: '<S67>/Signal Conversion14' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_SteerWhlAgSafe =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_SteerWhlAgSafe;

  /* SignalConversion: '<S67>/Signal Conversion15' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_SteerWhlAgSpdSafe =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_SteerWhlAgSpdSafe;

  /* SignalConversion: '<S67>/Signal Conversion16' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_SteerWhlSnsrQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_SteerWhlSnsrQf;

  /* Outputs for Enabled SubSystem: '<S66>/SAS_Status' incorporates:
   *  EnablePort: '<S74>/Enable'
   */
  /* Switch: '<S99>/Switch3' incorporates:
   *  Constant: '<S101>/Constant'
   *  Constant: '<S99>/Parameter5'
   *  RelationalOperator: '<S101>/Compare'
   *  Switch: '<S99>/Switch'
   */
  if (VTS_CswSASFailureSts_P != 0) {
    /* Switch: '<S99>/Switch3' incorporates:
     *  Constant: '<S99>/Parameter4'
     */
    VTS_SASFailureSts = VTS_SASFailureSts_P;
  } else if (VTS_SteerWhlSnsrQf == FallbackSigOutput_P.CompareToConstant_const)
  {
    /* Switch: '<S99>/Switch' incorporates:
     *  Constant: '<S99>/Constant'
     *  Switch: '<S99>/Switch3'
     */
    VTS_SASFailureSts = FallbackSigOutput_P.Constant_Value_b2;
  } else {
    /* Switch: '<S99>/Switch3' incorporates:
     *  Constant: '<S99>/Constant3'
     */
    VTS_SASFailureSts = FallbackSigOutput_P.Constant3_Value_o;
  }

  /* End of Switch: '<S99>/Switch3' */

  /* Switch: '<S99>/Switch4' incorporates:
   *  Constant: '<S99>/Parameter7'
   */
  if (VTS_CswSASCalibrationSts_P != 0) {
    /* Switch: '<S99>/Switch4' incorporates:
     *  Constant: '<S99>/Parameter6'
     */
    VTS_SASCalibrationSts = VTS_SASCalibrationSts_P;
  } else {
    /* Switch: '<S99>/Switch4' incorporates:
     *  Constant: '<S99>/Constant4'
     */
    VTS_SASCalibrationSts = FallbackSigOutput_P.Constant4_Value_ax;
  }

  /* End of Switch: '<S99>/Switch4' */

  /* Product: '<S99>/Product' incorporates:
   *  Constant: '<S99>/Constant1'
   */
  VTS_RawSteerWheelAngle = VTS_SteerWhlAgSafe *
    FallbackSigOutput_P.Constant1_Value_jk;

  /* Switch: '<S99>/Switch1' incorporates:
   *  Constant: '<S99>/Parameter1'
   */
  if (VTS_CswSteerWheelAngle_P != 0) {
    /* Switch: '<S99>/Switch1' incorporates:
     *  Constant: '<S99>/Parameter'
     */
    VTS_SteerWheelAngle = VTS_SteerWheelAngle_P;
  } else {
    /* Switch: '<S99>/Switch1' */
    VTS_SteerWheelAngle = VTS_RawSteerWheelAngle;
  }

  /* End of Switch: '<S99>/Switch1' */

  /* Abs: '<S99>/Abs' incorporates:
   *  Constant: '<S99>/Constant2'
   *  Product: '<S99>/Product1'
   */
  VTS_RawSteerWheelRotSpd = fabsf(VTS_SteerWhlAgSpdSafe *
    FallbackSigOutput_P.Constant2_Value_f);

  /* Switch: '<S99>/Switch2' incorporates:
   *  Constant: '<S99>/Parameter3'
   */
  if (VTS_CswSteerWheelRotSpd_P != 0) {
    /* Switch: '<S99>/Switch2' incorporates:
     *  Constant: '<S99>/Parameter2'
     */
    VTS_SteerWheelRotSpd = VTS_SteerWheelRotSpd_P;
  } else {
    /* Switch: '<S99>/Switch2' */
    VTS_SteerWheelRotSpd = VTS_RawSteerWheelRotSpd;
  }

  /* End of Switch: '<S99>/Switch2' */

  /* Outputs for Atomic SubSystem: '<S74>/SAS_Status_E2E' */
  /* UnitDelay: '<S102>/Output' */
  VTS_SASAliveCounter = FallbackSigOutput_DW.Output_DSTATE_n;

  /* S-Function (scanpack): '<S100>/CAN_Pack_SAS_Status' incorporates:
   *  Constant: '<S100>/Constant5'
   */
  /* S-Function (scanpack): '<S100>/CAN_Pack_SAS_Status' */
  FallbackSigOutput_B.CAN_Pack_SAS_Status.ID = 225U;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Length = 8U;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Extended = 0U;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Remote = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[0] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[1] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[2] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[3] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[4] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[5] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[6] = 0;
  FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[7] = 0;

  {
    /* --------------- START Packing signal 0 ------------------
     *  startBit                = 30
     *  length                  = 1
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = UNSIGNED
     *  factor                  = 1.0
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      uint32 packingValue = 0;

      {
        uint32 result = (uint32) (VTS_SASCalibrationSts);

        /* no scaling required */
        packingValue = result;
      }

      {
        uint8 packedValue;
        if (packingValue > (uint8)(1)) {
          packedValue = (uint8) 1;
        } else {
          packedValue = (uint8) (packingValue);
        }

        {
          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[3] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[3] | (uint8)((uint8)
              ((uint8)(packedValue & (uint8)0x1U) << 6));
          }
        }
      }
    }

    /* --------------- START Packing signal 1 ------------------
     *  startBit                = 31
     *  length                  = 1
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = UNSIGNED
     *  factor                  = 1.0
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      uint32 packingValue = 0;

      {
        uint32 result = (uint32) (VTS_SASFailureSts);

        /* no scaling required */
        packingValue = result;
      }

      {
        uint8 packedValue;
        if (packingValue > (uint8)(1)) {
          packedValue = (uint8) 1;
        } else {
          packedValue = (uint8) (packingValue);
        }

        {
          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[3] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[3] | (uint8)((uint8)
              ((uint8)(packedValue & (uint8)0x1U) << 7));
          }
        }
      }
    }

    /* --------------- START Packing signal 2 ------------------
     *  startBit                = 36
     *  length                  = 4
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = UNSIGNED
     *  factor                  = 1.0
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      uint32 packingValue = 0;

      {
        uint32 result = (uint32) (VTS_SASAliveCounter);

        /* no scaling required */
        packingValue = result;
      }

      {
        uint8 packedValue;
        if (packingValue > (uint8)(15)) {
          packedValue = (uint8) 15;
        } else {
          packedValue = (uint8) (packingValue);
        }

        {
          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[4] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[4] | (uint8)((uint8)
              ((uint8)(packedValue & (uint8)0xFU) << 4));
          }
        }
      }
    }

    /* --------------- START Packing signal 3 ------------------
     *  startBit                = 40
     *  length                  = 8
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = UNSIGNED
     *  factor                  = 1.0
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      uint32 packingValue = 0;

      {
        uint32 result = (uint32) (FallbackSigOutput_P.Constant5_Value_af);

        /* no scaling required */
        packingValue = result;
      }

      {
        uint8 packedValue;
        packedValue = (uint8) (packingValue);

        {
          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[5] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[5] | (uint8)
              (packedValue);
          }
        }
      }
    }

    /* --------------- START Packing signal 4 ------------------
     *  startBit                = 8
     *  length                  = 16
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = SIGNED
     *  factor                  = 0.1
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      float32 outValue = 0;

      {
        float32 result = VTS_SteerWheelAngle;

        /* no offset to apply */
        result = result * (1 / 0.1F);

        /* round to closest integer value for integer CAN signal */
        outValue = roundf(result);
      }

      {
        sint16 packedValue;
        sint32 scaledValue;
        if (outValue > 2147483647.0) {
          scaledValue = 2147483647;
        } else if (outValue < -2147483648.0) {
          scaledValue = -2147483647 - 1;
        } else {
          scaledValue = (sint32) outValue;
        }

        if (scaledValue > (sint32) (32767)) {
          packedValue = 32767;
        } else if (scaledValue < (sint32)((-(32767)-1))) {
          packedValue = (-(32767)-1);
        } else {
          packedValue = (sint16) (scaledValue);
        }

        {
          uint16* tempValuePtr = (uint16*)&packedValue;
          uint16 tempValue = *tempValuePtr;

          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[1] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[1] | (uint8)((uint16)
              (tempValue & (uint16)0xFFU));
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[0] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[0] | (uint8)((uint16)
              ((uint16)(tempValue & (uint16)0xFF00U) >> 8));
          }
        }
      }
    }

    /* --------------- START Packing signal 5 ------------------
     *  startBit                = 16
     *  length                  = 8
     *  desiredSignalByteLayout = BIGENDIAN
     *  dataType                = UNSIGNED
     *  factor                  = 4.0
     *  offset                  = 0.0
     *  minimum                 = 0.0
     *  maximum                 = 0.0
     * -----------------------------------------------------------------------*/
    {
      float32 outValue = 0;

      {
        float32 result = VTS_SteerWheelRotSpd;

        /* no offset to apply */
        result = result * (1 / 4.0F);

        /* round to closest integer value for integer CAN signal */
        outValue = roundf(result);
      }

      {
        uint8 packedValue;
        if (outValue > (float32)(255)) {
          packedValue = (uint8) 255;
        } else if (outValue < (float32)(0)) {
          packedValue = (uint8) 0;
        } else {
          packedValue = (uint8) (outValue);
        }

        {
          {
            FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[2] =
              FallbackSigOutput_B.CAN_Pack_SAS_Status.Data[2] | (uint8)
              (packedValue);
          }
        }
      }
    }
  }

  /* CCaller: '<S100>/C Caller2' */
  VTS_SASChecksum = crc_cal_SAS_Status
    (FallbackSigOutput_B.CAN_Pack_SAS_Status.Data);

  /* Sum: '<S103>/FixPt Sum1' incorporates:
   *  Constant: '<S103>/FixPt Constant'
   */
  s59_iter = (uint8)((uint32)VTS_SASAliveCounter +
                     FallbackSigOutput_P.FixPtConstant_Value_l);

  /* Switch: '<S104>/FixPt Switch' */
  if (s59_iter > FallbackSigOutput_P.CounterLimited6_uplimit) {
    /* Update for UnitDelay: '<S102>/Output' incorporates:
     *  Constant: '<S104>/Constant'
     */
    FallbackSigOutput_DW.Output_DSTATE_n = FallbackSigOutput_P.Constant_Value_h;
  } else {
    /* Update for UnitDelay: '<S102>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_n = s59_iter;
  }

  /* End of Switch: '<S104>/FixPt Switch' */
  /* End of Outputs for SubSystem: '<S74>/SAS_Status_E2E' */
  /* End of Outputs for SubSystem: '<S66>/SAS_Status' */

  /* SignalConversion: '<S67>/Signal Conversion8' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimALatDataRawSafeNom =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimALatDataRawSafeNom;

  /* SignalConversion: '<S67>/Signal Conversion9' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimALatDataRawSafeNomQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimALatDataRawSafeNomQf;

  /* SignalConversion: '<S67>/Signal Conversion10' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_YawRate1 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_YawRate1;

  /* SignalConversion: '<S67>/Signal Conversion11' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_YawRate1Qf1 =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_YawRate1Qf1;

  /* Outputs for Enabled SubSystem: '<S66>/YRS1' incorporates:
   *  EnablePort: '<S76>/Enable'
   */
  if (s65_iter > 0) {
    /* Product: '<S106>/Divide' incorporates:
     *  Constant: '<S106>/Constant1'
     */
    VTS_RawLateralAcce = VTS_PrimALatDataRawSafeNom /
      FallbackSigOutput_P.Constant1_Value_l;

    /* Switch: '<S106>/Switch2' incorporates:
     *  Constant: '<S106>/Parameter1'
     */
    if (VTS_CswLateralAcce_P != 0) {
      /* Switch: '<S106>/Switch2' incorporates:
       *  Constant: '<S106>/Parameter'
       */
      VTS_LateralAcce = VTS_LateralAcce_P;
    } else {
      /* Switch: '<S106>/Switch2' */
      VTS_LateralAcce = VTS_RawLateralAcce;
    }

    /* End of Switch: '<S106>/Switch2' */

    /* Switch: '<S106>/Switch' incorporates:
     *  Constant: '<S108>/Constant'
     *  RelationalOperator: '<S108>/Compare'
     */
    if (VTS_PrimALatDataRawSafeNomQf ==
        FallbackSigOutput_P.CompareToConstant_const_m) {
      /* Switch: '<S106>/Switch' incorporates:
       *  Constant: '<S106>/Constant'
       */
      VTS_RawLateralSensorState = FallbackSigOutput_P.Constant_Value_oj;
    } else {
      /* Switch: '<S106>/Switch' incorporates:
       *  Constant: '<S106>/Constant2'
       */
      VTS_RawLateralSensorState = FallbackSigOutput_P.Constant2_Value_fr0;
    }

    /* End of Switch: '<S106>/Switch' */

    /* Switch: '<S106>/Switch3' incorporates:
     *  Constant: '<S106>/Parameter3'
     */
    if (VTS_CswLateralSensorState_P != 0) {
      /* Switch: '<S106>/Switch3' incorporates:
       *  Constant: '<S106>/Parameter2'
       */
      VTS_LateralSensorState = VTS_LateralSensorState_P;
    } else {
      /* Switch: '<S106>/Switch3' */
      VTS_LateralSensorState = VTS_RawLateralSensorState;
    }

    /* End of Switch: '<S106>/Switch3' */

    /* Product: '<S106>/Product' incorporates:
     *  Constant: '<S106>/Constant3'
     */
    VTS_RawYawRate = VTS_YawRate1 * FallbackSigOutput_P.Constant3_Value_c3;

    /* Switch: '<S106>/Switch4' incorporates:
     *  Constant: '<S106>/Parameter5'
     */
    if (VTS_CswYawRate_P != 0) {
      /* Switch: '<S106>/Switch4' incorporates:
       *  Constant: '<S106>/Parameter4'
       */
      VTS_YawRate = VTS_YawRate_P;
    } else {
      /* Switch: '<S106>/Switch4' */
      VTS_YawRate = VTS_RawYawRate;
    }

    /* End of Switch: '<S106>/Switch4' */

    /* Switch: '<S106>/Switch1' incorporates:
     *  Constant: '<S109>/Constant'
     *  RelationalOperator: '<S109>/Compare'
     */
    if (VTS_YawRate1Qf1 == FallbackSigOutput_P.CompareToConstant1_const) {
      /* Switch: '<S106>/Switch1' incorporates:
       *  Constant: '<S106>/Constant4'
       */
      VTS_RawYawRateSensorState = FallbackSigOutput_P.Constant4_Value_f;
    } else {
      /* Switch: '<S106>/Switch1' incorporates:
       *  Constant: '<S106>/Constant5'
       */
      VTS_RawYawRateSensorState = FallbackSigOutput_P.Constant5_Value_on;
    }

    /* End of Switch: '<S106>/Switch1' */

    /* Switch: '<S106>/Switch5' incorporates:
     *  Constant: '<S106>/Parameter7'
     */
    if (VTS_CswYawRateSensorState_P != 0) {
      /* Switch: '<S106>/Switch5' incorporates:
       *  Constant: '<S106>/Parameter6'
       */
      VTS_YawRateSensorState = VTS_YawRateSensorState_P;
    } else {
      /* Switch: '<S106>/Switch5' */
      VTS_YawRateSensorState = VTS_RawYawRateSensorState;
    }

    /* End of Switch: '<S106>/Switch5' */

    /* Outputs for Atomic SubSystem: '<S76>/YRS1_E2E' */
    /* UnitDelay: '<S110>/Output' */
    VTS_YRS1AliveCounter = FallbackSigOutput_DW.Output_DSTATE_c;

    /* S-Function (scanpack): '<S107>/CAN_Pack_YRS1' incorporates:
     *  Constant: '<S107>/Constant4'
     */
    /* S-Function (scanpack): '<S107>/CAN_Pack_YRS1' */
    FallbackSigOutput_B.CAN_Pack_YRS1.ID = 305U;
    FallbackSigOutput_B.CAN_Pack_YRS1.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_YRS1.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_YRS1.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS1.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 56
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_YRS1AliveCounter);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[7] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[7] | (uint8)((uint8)
                (packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 0
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant4_Value_o);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[0] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[0] | (uint8)(packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 24
       *  length                  = 16
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.001
       *  offset                  = -2.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_LateralAcce;

          /* full scaling operation */
          result = (result - -2.0F) * (1 / 0.001F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(65535)) {
            packedValue = (uint16) 65535;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[3] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[3] | (uint8)((uint16)
                (packedValue & (uint16)0xFFU));
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[2] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[2] | (uint8)((uint16)
                ((uint16)(packedValue & (uint16)0xFF00U) >> 8));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 11
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_LateralSensorState);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[1] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[1] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x3U) << 3));
            }
          }
        }
      }

      /* --------------- START Packing signal 4 ------------------
       *  startBit                = 40
       *  length                  = 16
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.01
       *  offset                  = -180.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_YawRate;

          /* full scaling operation */
          result = (result - -180.0F) * (1 / 0.01F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(65535)) {
            packedValue = (uint16) 65535;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[5] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[5] | (uint8)((uint16)
                (packedValue & (uint16)0xFFU));
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[4] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[4] | (uint8)((uint16)
                ((uint16)(packedValue & (uint16)0xFF00U) >> 8));
            }
          }
        }
      }

      /* --------------- START Packing signal 5 ------------------
       *  startBit                = 9
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_YawRateSensorState);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS1.Data[1] =
                FallbackSigOutput_B.CAN_Pack_YRS1.Data[1] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x3U) << 1));
            }
          }
        }
      }
    }

    /* CCaller: '<S107>/C Caller' */
    VTS_YRS1Checksum = crc_cal_YRS(FallbackSigOutput_B.CAN_Pack_YRS1.Data);

    /* Sum: '<S111>/FixPt Sum1' incorporates:
     *  Constant: '<S111>/FixPt Constant'
     */
    s59_iter = (uint8)((uint32)VTS_YRS1AliveCounter +
                       FallbackSigOutput_P.FixPtConstant_Value_ht);

    /* Switch: '<S112>/FixPt Switch' */
    if (s59_iter > FallbackSigOutput_P.CounterLimited10_uplimit) {
      /* Update for UnitDelay: '<S110>/Output' incorporates:
       *  Constant: '<S112>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_c =
        FallbackSigOutput_P.Constant_Value_i;
    } else {
      /* Update for UnitDelay: '<S110>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_c = s59_iter;
    }

    /* End of Switch: '<S112>/FixPt Switch' */
    /* End of Outputs for SubSystem: '<S76>/YRS1_E2E' */
  }

  /* End of Outputs for SubSystem: '<S66>/YRS1' */

  /* SignalConversion: '<S67>/Signal Conversion12' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimALgtDataRawSafeNom =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimALgtDataRawSafeNom;

  /* SignalConversion: '<S67>/Signal Conversion13' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimALgtDataRawSafeNomQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimALgtDataRawSafeNomQf;

  /* Outputs for Enabled SubSystem: '<S66>/YRS2' incorporates:
   *  EnablePort: '<S77>/Enable'
   */
  if (s65_iter > 0) {
    /* Product: '<S113>/Divide' incorporates:
     *  Constant: '<S113>/Constant1'
     */
    VTS_RawLongitAcce = VTS_PrimALgtDataRawSafeNom /
      FallbackSigOutput_P.Constant1_Value_ji;

    /* Switch: '<S113>/Switch2' incorporates:
     *  Constant: '<S113>/Parameter2'
     */
    if (VTS_CswLongitAcce_P != 0) {
      /* Switch: '<S113>/Switch2' incorporates:
       *  Constant: '<S113>/Parameter3'
       */
      VTS_LongitAcce = VTS_LongitAcce_P;
    } else {
      /* Switch: '<S113>/Switch2' */
      VTS_LongitAcce = VTS_RawLongitAcce;
    }

    /* End of Switch: '<S113>/Switch2' */

    /* Switch: '<S113>/Switch' incorporates:
     *  Constant: '<S115>/Constant'
     *  RelationalOperator: '<S115>/Compare'
     */
    if (VTS_PrimALgtDataRawSafeNomQf ==
        FallbackSigOutput_P.CompareToConstant_const_n) {
      /* Switch: '<S113>/Switch' incorporates:
       *  Constant: '<S113>/Constant'
       */
      VTS_RawLongitSensorState = FallbackSigOutput_P.Constant_Value_o5;
    } else {
      /* Switch: '<S113>/Switch' incorporates:
       *  Constant: '<S113>/Constant2'
       */
      VTS_RawLongitSensorState = FallbackSigOutput_P.Constant2_Value_l;
    }

    /* End of Switch: '<S113>/Switch' */

    /* Switch: '<S113>/Switch1' incorporates:
     *  Constant: '<S113>/Parameter'
     */
    if (VTS_CswLongitSensorState_P != 0) {
      /* Switch: '<S113>/Switch1' incorporates:
       *  Constant: '<S113>/Parameter1'
       */
      VTS_LongitSensorState = VTS_LongitSensorState_P;
    } else {
      /* Switch: '<S113>/Switch1' */
      VTS_LongitSensorState = VTS_RawLongitSensorState;
    }

    /* End of Switch: '<S113>/Switch1' */

    /* Outputs for Atomic SubSystem: '<S77>/YRS2_E2E' */
    /* UnitDelay: '<S116>/Output' */
    VTS_YRS2AliveCounter = FallbackSigOutput_DW.Output_DSTATE_p;

    /* S-Function (scanpack): '<S114>/CAN_Pack_YRS2' incorporates:
     *  Constant: '<S114>/Constant6'
     */
    /* S-Function (scanpack): '<S114>/CAN_Pack_YRS2' */
    FallbackSigOutput_B.CAN_Pack_YRS2.ID = 306U;
    FallbackSigOutput_B.CAN_Pack_YRS2.Length = 8U;
    FallbackSigOutput_B.CAN_Pack_YRS2.Extended = 0U;
    FallbackSigOutput_B.CAN_Pack_YRS2.Remote = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[0] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[1] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[2] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[3] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[4] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[5] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[6] = 0;
    FallbackSigOutput_B.CAN_Pack_YRS2.Data[7] = 0;

    {
      /* --------------- START Packing signal 0 ------------------
       *  startBit                = 0
       *  length                  = 8
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (FallbackSigOutput_P.Constant6_Value_hd);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          packedValue = (uint8) (packingValue);

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS2.Data[0] =
                FallbackSigOutput_B.CAN_Pack_YRS2.Data[0] | (uint8)(packedValue);
            }
          }
        }
      }

      /* --------------- START Packing signal 1 ------------------
       *  startBit                = 56
       *  length                  = 4
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_YRS2AliveCounter);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(15)) {
            packedValue = (uint8) 15;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS2.Data[7] =
                FallbackSigOutput_B.CAN_Pack_YRS2.Data[7] | (uint8)((uint8)
                (packedValue & (uint8)0xFU));
            }
          }
        }
      }

      /* --------------- START Packing signal 2 ------------------
       *  startBit                = 24
       *  length                  = 16
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 0.001
       *  offset                  = -2.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        float32 outValue = 0;

        {
          float32 result = VTS_LongitAcce;

          /* full scaling operation */
          result = (result - -2.0F) * (1 / 0.001F);

          /* round to closest integer value for integer CAN signal */
          outValue = roundf(result);
        }

        {
          uint16 packedValue;
          if (outValue > (float32)(65535)) {
            packedValue = (uint16) 65535;
          } else if (outValue < (float32)(0)) {
            packedValue = (uint16) 0;
          } else {
            packedValue = (uint16) (outValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS2.Data[3] =
                FallbackSigOutput_B.CAN_Pack_YRS2.Data[3] | (uint8)((uint16)
                (packedValue & (uint16)0xFFU));
              FallbackSigOutput_B.CAN_Pack_YRS2.Data[2] =
                FallbackSigOutput_B.CAN_Pack_YRS2.Data[2] | (uint8)((uint16)
                ((uint16)(packedValue & (uint16)0xFF00U) >> 8));
            }
          }
        }
      }

      /* --------------- START Packing signal 3 ------------------
       *  startBit                = 14
       *  length                  = 2
       *  desiredSignalByteLayout = BIGENDIAN
       *  dataType                = UNSIGNED
       *  factor                  = 1.0
       *  offset                  = 0.0
       *  minimum                 = 0.0
       *  maximum                 = 0.0
       * -----------------------------------------------------------------------*/
      {
        uint32 packingValue = 0;

        {
          uint32 result = (uint32) (VTS_LongitSensorState);

          /* no scaling required */
          packingValue = result;
        }

        {
          uint8 packedValue;
          if (packingValue > (uint8)(3)) {
            packedValue = (uint8) 3;
          } else {
            packedValue = (uint8) (packingValue);
          }

          {
            {
              FallbackSigOutput_B.CAN_Pack_YRS2.Data[1] =
                FallbackSigOutput_B.CAN_Pack_YRS2.Data[1] | (uint8)((uint8)
                ((uint8)(packedValue & (uint8)0x3U) << 6));
            }
          }
        }
      }
    }

    /* CCaller: '<S114>/C Caller1' */
    VTS_YRS2Checksum = crc_cal_YRS(FallbackSigOutput_B.CAN_Pack_YRS2.Data);

    /* Sum: '<S117>/FixPt Sum1' incorporates:
     *  Constant: '<S117>/FixPt Constant'
     */
    s65_iter = (uint8)((uint32)VTS_YRS2AliveCounter +
                       FallbackSigOutput_P.FixPtConstant_Value_i);

    /* Switch: '<S118>/FixPt Switch' */
    if (s65_iter > FallbackSigOutput_P.CounterLimited9_uplimit) {
      /* Update for UnitDelay: '<S116>/Output' incorporates:
       *  Constant: '<S118>/Constant'
       */
      FallbackSigOutput_DW.Output_DSTATE_p =
        FallbackSigOutput_P.Constant_Value_ay;
    } else {
      /* Update for UnitDelay: '<S116>/Output' */
      FallbackSigOutput_DW.Output_DSTATE_p = s65_iter;
    }

    /* End of Switch: '<S118>/FixPt Switch' */
    /* End of Outputs for SubSystem: '<S77>/YRS2_E2E' */
  }

  /* End of Outputs for SubSystem: '<S66>/YRS2' */

  /* SignalConversion: '<S67>/Signal Conversion18' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrimVehSpdGroupSafeNomQf =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrimVehSpdGroupSafeNomQf;

  /* SignalConversion: '<S67>/Signal Conversion19' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_WhlLockStsLockSts =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_WhlLockStsLockSts;

  /* SignalConversion: '<S67>/Signal Conversion20' incorporates:
   *  Inport: '<Root>/VSI_VehInfoFor1V1R'
   */
  VTS_PrpsnTqDirAct =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R
     ())->VSI_PrpsnTqDirAct;

  /* Update for UnitDelay: '<S6>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_h = rtb_Equal1_m;

  /* Update for UnitDelay: '<S11>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE = rtb_Switch;

  /* Update for UnitDelay: '<S21>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_k = rtb_Switch1_b;

  /* Update for UnitDelay: '<S24>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_iy = rtb_MultiportSwitch1;

  /* Update for UnitDelay: '<S20>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_l = EAD_FilteredActualVehAcc;

  /* Update for UnitDelay: '<S19>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_ki = EAD_AccRequest;

  /* Update for UnitDelay: '<S36>/Unit Delay1' incorporates:
   *  Inport: '<Root>/VSI_VehicleInfo'
   */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_k =
    (Rte_IRead_FallbackSigOutput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo())
    ->VSI_FallbackActive;

  /* Update for UnitDelay: '<S10>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_h = rtb_Switch7;

  /* Update for UnitDelay: '<S35>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_c = rtb_AdpLiReqFromAPIIncrLiRiReq;

  /* Update for UnitDelay: '<S37>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_d = rtb_AdpLiReqFromAPIIndcrLeReq;

  /* Update for UnitDelay: '<S40>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_c = rtb_UnitDelay_bj;

  /* Update for UnitDelay: '<S44>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_d = rtb_AdpLiReqFromAPIHzrdLiDeactnReq;

  /* Update for UnitDelay: '<S41>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_i = rtb_Switch_mg;

  /* Update for UnitDelay: '<S45>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_et = rtb_AND_n;

  /* Update for UnitDelay: '<S10>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_a = EAD_AdQuitRequest;

  /* Update for UnitDelay: '<S33>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_o = rtb_AND1;

  /* Update for UnitDelay: '<S34>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE_l = rtb_AND2;

  /* Update for UnitDelay: '<S38>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_f = rtb_Switch_o;

  /* Update for UnitDelay: '<S42>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_o = EAD_AdActiveRequest;

  /* Update for UnitDelay: '<S39>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_kp = rtb_Switch_ph;

  /* Update for UnitDelay: '<S43>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_ey = EAD_AdQuitRequest;

  /* Update for UnitDelay: '<S50>/Output' */
  FallbackSigOutput_DW.Output_DSTATE = rtb_FixPtSwitch;

  /* Update for UnitDelay: '<S49>/Unit Delay1' */
  FallbackSigOutput_DW.UnitDelay1_DSTATE[0] = rtb_Switch_i1_idx_0;
  FallbackSigOutput_DW.UnitDelay1_DSTATE[1] = rtb_Switch_i1_idx_1;

  /* Update for UnitDelay: '<S49>/Unit Delay' incorporates:
   *  DataTypeConversion: '<S49>/Data Type Conversion62'
   */
  FallbackSigOutput_DW.UnitDelay_DSTATE_ls[0] = (uint8)y;
  FallbackSigOutput_DW.UnitDelay_DSTATE_ls[1] = rtb_DataTypeConversion63;

  /* Update for UnitDelay: '<S53>/Output' */
  FallbackSigOutput_DW.Output_DSTATE_l = rtb_FixPtSum1_a;

  /* Update for UnitDelay: '<S60>/Output' */
  FallbackSigOutput_DW.Output_DSTATE_lj = rtb_BitwiseAND1;

  /* Update for UnitDelay: '<S61>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_n = rtb_Switch_j;

  /* Update for UnitDelay: '<S64>/Unit Delay' */
  FallbackSigOutput_DW.UnitDelay_DSTATE_eyn = rtb_LessThan;

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion64'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum
    (VTS_ChecksumEscDa);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion63'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter
    ((uint8)(VTS_AliveCounterEscDa & 15));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_QDCFRS' incorporates:
   *  Constant: '<S69>/Constant'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_QDCFRS
    (FallbackSigOutput_P.Constant_Value_p4);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_BrakeTempTooHigh' incorporates:
   *  Constant: '<S69>/Constant1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh
    (FallbackSigOutput_P.Constant1_Value_h);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_DTC_Active' incorporates:
   *  Constant: '<S69>/Constant2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DTC_Active
    (FallbackSigOutput_P.Constant2_Value_eg);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_Vehiclestandstill' incorporates:
   *  Constant: '<S69>/Constant3'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_Vehiclestandstill
    (FallbackSigOutput_P.Constant3_Value_m);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_AWB_available' incorporates:
   *  Constant: '<S69>/Constant4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_available
    (FallbackSigOutput_P.Constant4_Value_bs);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_AWB_active' incorporates:
   *  Constant: '<S69>/Constant5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AWB_active
    (FallbackSigOutput_P.Constant5_Value_e);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_AEB_available' incorporates:
   *  Constant: '<S69>/Constant6'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_available
    (FallbackSigOutput_P.Constant6_Value_k);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_AEB_active' incorporates:
   *  Constant: '<S69>/Constant7'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_AEB_active
    (FallbackSigOutput_P.Constant7_Value_cs);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABP_available' incorporates:
   *  Constant: '<S69>/Constant12'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_available
    (FallbackSigOutput_P.Constant12_Value_a);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABA_available' incorporates:
   *  Constant: '<S69>/Constant13'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_available
    (FallbackSigOutput_P.Constant13_Value_c);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABP_active' incorporates:
   *  Constant: '<S69>/Constant8'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABP_active
    (FallbackSigOutput_P.Constant8_Value_fn);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABA_active' incorporates:
   *  Constant: '<S69>/Constant9'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_ABA_active
    (FallbackSigOutput_P.Constant9_Value_i);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_DiagExtModSts' incorporates:
   *  Constant: '<S69>/Constant10'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_DiagExtModSts
    (FallbackSigOutput_P.Constant10_Value_a);

  /* Outport: '<Root>/ESC_DA_MESSAGE_ESC_NoBrakeForce' incorporates:
   *  Constant: '<S69>/Constant11'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_DA_MESSAGE_ESC_NoBrakeForce
    (FallbackSigOutput_P.Constant11_Value_m);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion58'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum
    (VTS_ChecksumFrntWhl);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid' incorporates:
   *  Constant: '<S69>/Constant14'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid
    (FallbackSigOutput_P.Constant14_Value_lf);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter
    ((uint8)(VTS_AliveCounterFrntWhl & 15));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure' incorporates:
   *  Constant: '<S69>/Constant15'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure
    (FallbackSigOutput_P.Constant15_Value_ii);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection
    (VTS_WheelDirectionFrntRi);

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion3'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid
    (VTS_SpeedInvalidFrntRi);

  /* DataTypeConversion: '<S69>/Data Type Conversion4' */
  rtb_Switch = fmodf(roundf(VTS_WheelSpeedFrntRi / 0.05625F), 65536.0F);

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion9'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection
    (VTS_WheelDirectionFrntLe);

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid
    (VTS_SpeedInvalidFrntLe);

  /* DataTypeConversion: '<S69>/Data Type Conversion6' */
  rtb_Switch = fmodf(roundf(VTS_WheelSpeedFrntLe / 0.05625F), 65536.0F);

  /* Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion6'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion7'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum
    (VTS_ChecksumReWhl);

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion8'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter
    ((uint8)(VTS_AliveCounterReWhl & 15));

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelDirection' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion15'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection
    (VTS_WheelDirectionReRi);

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion10'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid
    (VTS_SpeedInvalidReRi);

  /* DataTypeConversion: '<S69>/Data Type Conversion11' */
  rtb_Switch = fmodf(roundf(VTS_WheelSpeedReRi / 0.05625F), 65536.0F);

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion11'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelDirection' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion12'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection
    (VTS_WheelDirectionReLe);

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion13'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid
    (VTS_SpeedInvalidReLe);

  /* DataTypeConversion: '<S69>/Data Type Conversion19' */
  rtb_Switch = fmodf(roundf(VTS_WheelSpeedReLe / 0.05625F), 65536.0F);

  /* Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion19'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/ESC_Status_ESC_Status_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion14'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_Checksum
    (VTS_EscStatusChecksum);

  /* Outport: '<Root>/ESC_Status_ESC_Status_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion16'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_Status_AliveCounter
    ((uint8)(VTS_EscStatusAliveCounter & 15));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_Status_ESC_HHCActive' incorporates:
   *  Constant: '<S69>/Constant16'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_HHCActive
    (FallbackSigOutput_P.Constant16_Value_c);

  /* Outport: '<Root>/ESC_Status_ESC_BrakePedalSwitchInvalid' incorporates:
   *  Constant: '<S69>/Constant17'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchInvalid
    (FallbackSigOutput_P.Constant17_Value_n);

  /* Outport: '<Root>/ESC_Status_ESC_EPBStatus' incorporates:
   *  Constant: '<S69>/Constant18'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_EPBStatus
    (FallbackSigOutput_P.Constant18_Value_n);

  /* Outport: '<Root>/ESC_Status_ESC_AVHStatus' incorporates:
   *  Constant: '<S69>/Constant19'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_AVHStatus
    (FallbackSigOutput_P.Constant19_Value_nz);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/ESC_Status_ESC_VehicleSpeedInvalid' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion17'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeedInvalid
    (VTS_VehicleSpeedInvalid);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_Status_ESC_PATAResponse' incorporates:
   *  Constant: '<S69>/Constant20'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_PATAResponse
    (FallbackSigOutput_P.Constant20_Value_o);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* DataTypeConversion: '<S69>/Data Type Conversion18' */
  rtb_Switch = fmodf(roundf(VTS_VehicleSpeed / 0.05625F), 65536.0F);

  /* Outport: '<Root>/ESC_Status_ESC_VehicleSpeed' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion18'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_VehicleSpeed((uint16)
    (rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch : (sint32)
     (uint16)rtb_Switch));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/ESC_Status_ESC_ESPFailed' incorporates:
   *  Constant: '<S69>/Constant21'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPFailed
    (FallbackSigOutput_P.Constant21_Value_a);

  /* Outport: '<Root>/ESC_Status_ESC_ESPActive' incorporates:
   *  Constant: '<S69>/Constant22'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ESPActive
    (FallbackSigOutput_P.Constant22_Value_l);

  /* Outport: '<Root>/ESC_Status_ESC_TCSActive' incorporates:
   *  Constant: '<S69>/Constant23'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_TCSActive
    (FallbackSigOutput_P.Constant23_Value_c);

  /* Outport: '<Root>/ESC_Status_ESC_BrakePedalSwitchStatus' incorporates:
   *  Constant: '<S69>/Constant24'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_BrakePedalSwitchStatus
    (FallbackSigOutput_P.Constant24_Value_g);

  /* Outport: '<Root>/ESC_Status_ESC_ABSActive' incorporates:
   *  Constant: '<S69>/Constant25'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_ESC_Status_ESC_ABSActive
    (FallbackSigOutput_P.Constant25_Value_j);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/SAS_Status_SAS_Status_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion34'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_Checksum
    (VTS_SASChecksum);

  /* Outport: '<Root>/SAS_Status_SAS_Status_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion30'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_Status_AliveCounter
    ((uint8)(VTS_SASAliveCounter & 15));

  /* Outport: '<Root>/SAS_Status_SAS_FailureSts' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion31'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_FailureSts
    (VTS_SASFailureSts);

  /* Outport: '<Root>/SAS_Status_SAS_CalibrationSts' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion32'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_CalibrationSts
    (VTS_SASCalibrationSts);

  /* DataTypeConversion: '<S69>/Data Type Conversion33' */
  rtb_Switch = fmodf(roundf(VTS_SteerWheelRotSpd * 0.25F), 256.0F);

  /* Outport: '<Root>/SAS_Status_SAS_SteerWheelRotSpd' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion33'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelRotSpd
    ((uint8)(rtb_Switch < 0.0F ? (sint32)(uint8)-(sint8)(uint8)-rtb_Switch :
             (sint32)(uint8)rtb_Switch));

  /* DataTypeConversion: '<S69>/Data Type Conversion25' */
  rtb_Switch = fmodf(roundf(VTS_SteerWheelAngle / 0.1F), 65536.0F);

  /* Outport: '<Root>/SAS_Status_SAS_SteerWheelAngle' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion25'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SAS_Status_SAS_SteerWheelAngle
    ((sint16)(rtb_Switch < 0.0F ? (sint32)(sint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(sint16)(uint16)rtb_Switch));

  /* Outport: '<Root>/YRS1_YRS1_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion20'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_AliveCounter((uint8)
    (VTS_YRS1AliveCounter & 15));

  /* DataTypeConversion: '<S69>/Data Type Conversion21' */
  rtb_Switch = fmodf(roundf((VTS_YawRate + 180.0F) / 0.01F), 65536.0F);

  /* Outport: '<Root>/YRS1_YRS_YawRate' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion21'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRate((uint16)
    (rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch : (sint32)
     (uint16)rtb_Switch));

  /* DataTypeConversion: '<S69>/Data Type Conversion22' */
  rtb_Switch = fmodf(roundf((VTS_LateralAcce + 2.0F) / 0.001F), 65536.0F);

  /* Outport: '<Root>/YRS1_YRS_LateralAcce' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion22'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralAcce((uint16)
    (rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch : (sint32)
     (uint16)rtb_Switch));

  /* Outport: '<Root>/YRS1_YRS_LateralSensorState' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion23'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_LateralSensorState
    (VTS_LateralSensorState);

  /* Outport: '<Root>/YRS1_YRS_YawRateSensorState' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion29'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS_YawRateSensorState
    (VTS_YawRateSensorState);

  /* Outport: '<Root>/YRS1_YRS1_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion24'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS1_YRS1_Checksum(VTS_YRS1Checksum);

  /* Outport: '<Root>/YRS2_YRS_AliveCounter' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion26'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_AliveCounter((uint8)
    (VTS_YRS2AliveCounter & 15));

  /* DataTypeConversion: '<S69>/Data Type Conversion27' */
  rtb_Switch = fmodf(roundf((VTS_LongitAcce + 2.0F) / 0.001F), 65536.0F);

  /* Outport: '<Root>/YRS2_YRS_LongitAcce' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion27'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitAcce((uint16)
    (rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch : (sint32)
     (uint16)rtb_Switch));

  /* Outport: '<Root>/YRS2_YRS_LongitSensorState' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion28'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS_LongitSensorState
    (VTS_LongitSensorState);

  /* Outport: '<Root>/YRS2_YRS2_Checksum' incorporates:
   *  DataTypeConversion: '<S69>/Data Type Conversion35'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_YRS2_YRS2_Checksum(VTS_YRS2Checksum);

  /* Outport: '<Root>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C
    (VTS_PrimVehSpdGroupSafeNomQf);

  /* DataTypeConversion: '<S68>/Data Type Conversion1' */
  rtb_Switch = fmodf(floorf(VTS_PrimVehSpdGroupSafeNom / 0.01F), 65536.0F);

  /* Outport: '<Root>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C
    (VTS_PrimALgtDataRawSafeNomQf);

  /* DataTypeConversion: '<S68>/Data Type Conversion3' */
  rtb_Switch = fmodf(floorf((VTS_PrimALgtDataRawSafeNom + 16.384F) / 0.001F),
                     65536.0F);

  /* Outport: '<Root>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion3'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* Outport: '<Root>/VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion8'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C
    (VTS_PrpsnTqDirAct);

  /* Outport: '<Root>/VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion7'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C
    (VTS_YawRate1Qf1);

  /* DataTypeConversion: '<S68>/Data Type Conversion6' */
  rtb_Switch = fmodf(floorf(VTS_YawRate1 * 4096.0F), 65536.0F);

  /* Outport: '<Root>/VCU1Mid3CanFr08_1V1R_YawRate1_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion6'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr08_1V1R_YawRate1_C
    ((sint16)(rtb_Switch < 0.0F ? (sint32)(sint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(sint16)(uint16)rtb_Switch));

  /* Outport: '<Root>/VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C
    (VTS_WhlLockStsLockSts);

  /* DataTypeConversion: '<S68>/Data Type Conversion58' */
  rtb_Switch = fmodf(floorf((VTS_PrimALatDataRawSafeNom + 16.384F) / 0.001F),
                     65536.0F);

  /* Outport: '<Root>/SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C' incorporates:
   *  DataTypeConversion: '<S68>/Data Type Conversion58'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C
    ((uint16)(rtb_Switch < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-rtb_Switch :
              (sint32)(uint16)rtb_Switch));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/VIMMid3CanFr04_UDcDcAvlLoSideExt' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion39'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_UDcDcAvlLoSideExt
    (DataTypeConversion39_h);

  /* Outport: '<Root>/VIMMid3CanFr04_SG_HmiAutnmsSts' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr04_SG_HmiAutnmsSts
    (&BusCreator7);

  /* Outport: '<Root>/VIMMid3CanFr07_SG_AutnmsDrvStReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr07_SG_AutnmsDrvStReq
    (&BusCreator3);

  /* Outport: '<Root>/VIMMid3CanFr08_SG_VehOperStReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr08_SG_VehOperStReq
    (&BusCreator6);

  /* Outport: '<Root>/VIMMid3CanFr09_VehUsgStReq' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion44'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_VehUsgStReq
    (DataTypeConversion44_e);

  /* Outport: '<Root>/VIMMid3CanFr09_SG_AdStandStillReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdStandStillReq
    (&BusCreator8);

  /* Outport: '<Root>/VIMMid3CanFr09_SG_AdDirReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr09_SG_AdDirReq
    (&BusCreator9);

  /* Outport: '<Root>/VIMMid3CanFr11_SG_SwtExtrLiFromAPI' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_SwtExtrLiFromAPI(
    &FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_SwtExtrLiFromAPI);

  /* Outport: '<Root>/VIMMid3CanFr11_SG_AdpLiReqFromAPI' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdpLiReqFromAPI
    (&BusCreator13);

  /* Outport: '<Root>/VIMMid3CanFr11_SG_AdFusedFricEstimn' incorporates:
   *  SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_AdFusedFricEstimn'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr11_SG_AdFusedFricEstimn
    (&FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_AdFusedFricEstimn);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/VIMMid3CanFr13_AdSetSpd' incorporates:
   *  DataTypeConversion: '<S7>/Data Type Conversion46'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_AdSetSpd((uint16)
    (rtb_Product1_e_idx_0 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
     rtb_Product1_e_idx_0 : (sint32)(uint16)rtb_Product1_e_idx_0));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/VIMMid3CanFr13_SG_AdWhlLockReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdWhlLockReq
    (&BusCreator11);

  /* Outport: '<Root>/VIMMid3CanFr13_SG_AdFreeDst' incorporates:
   *  SignalConversion generated from: '<S2>/VIMMid3CanFr13_SG_AdFreeDst'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr13_SG_AdFreeDst
    (&FallbackSigOutput_B.OutportBufferForVIMMid3CanFr13_SG_AdFreeDst);

  /* Outport: '<Root>/VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe
    (&BusCreator1);

  /* Outport: '<Root>/VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe
    (&BusCreator2);

  /* Outport: '<Root>/VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe
    (&BusCreator);

  /* Outport: '<Root>/VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe
    (&BusCreator4);

  /* Outport: '<Root>/VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator10'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe
    (&BusCreator10);

  /* Outport: '<Root>/VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe' incorporates:
   *  BusCreator: '<S7>/Bus Creator5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe
    (&BusCreator5);

  /* Outport: '<Root>/VIMBMid6CanFdFr29_SG_SecAdWhlLockReq' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq
    (&BusCreator12);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* DataTypeConversion: '<S48>/Data Type Conversion6' */
  rtb_Product1_e_idx_0 = fmodf(floorf(rtb_Min2 / 0.1F), 4.2949673E+9F);

  /* Outport: '<Root>/VCU1Mid3CanFr06_CarTiGlb_A' incorporates:
   *  DataTypeConversion: '<S48>/Data Type Conversion6'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_VCU1Mid3CanFr06_ACU_CarTiGlb_A
    (rtb_Product1_e_idx_0 < 0.0F ? (uint32)-(sint32)(uint32)-
     rtb_Product1_e_idx_0 : (uint32)rtb_Product1_e_idx_0);

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FBS_DebugInfo' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FBS_DebugInfo_FBS_DebugInfo
    (&FBS_DebugInfo);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AswSoftwarewareVersion' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion95'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AswSoftwarewareVersion
    ((AswSoftwarewareVersion)(tmp_i < 0.0F ? (sint32)(uint8)-(sint8)(uint8)-
      tmp_i : (sint32)(uint8)tmp_i));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_BootLoaderVersion' incorporates:
   *  SignalConversion generated from: '<S2>/FallbackSystemStatus_BootLoaderVersion'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BootLoaderVersion
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_BootLoaderVersion);

  /* Outport: '<Root>/FallbackSystemStatus_BswSoftwarewareVersion' incorporates:
   *  SignalConversion generated from: '<S2>/FallbackSystemStatus_BswSoftwarewareVersion'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_BswSoftwarewareVersion
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_BswSoftwarewareVersion);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timeout
    (rtb_AND_pv);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0MessageID
    (DataTypeConversion1_g);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion34'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter0Timer
    ((VehMid3SsmCounter0Timer)(tmp_h < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_h : (sint32)(uint16)tmp_h));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timeout
    (rtb_AND_m);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion2'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1MessageID
    (DataTypeConversion2);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion37'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3SsmCounter1Timer
    ((VehMid3SsmCounter1Timer)(tmp_g < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_g : (sint32)(uint16)tmp_g));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timeout
    (DataTypeConversion20_h);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion3'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0MessageID
    (DataTypeConversion3_l);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion38'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter0Timer
    ((VehMid3VcuCounter0Timer)(tmp_f < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_f : (sint32)(uint16)tmp_f));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timeout
    (DataTypeConversion21_p);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion39'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1Timer
    ((VehMid3VcuCounter1Timer)(tmp_e < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_e : (sint32)(uint16)tmp_e));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion4'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid3VcuCounter1MessageID
    (DataTypeConversion4_f);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timeout
    (DataTypeConversion22_k);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion5'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0MessageID
    (DataTypeConversion5);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion40'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter0Timer
    ((VehMid5SsmCounter0Timer)(tmp_d < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_d : (sint32)(uint16)tmp_d));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timeout
    (DataTypeConversion23);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion6'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1MessageID
    (DataTypeConversion6_py);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion41'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid5SsmCounter1Timer
    ((VehMid5SsmCounter1Timer)(tmp_c < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_c : (sint32)(uint16)tmp_c));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timeout
    (DataTypeConversion24_b);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion7'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0MessageID
    (DataTypeConversion7);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion42'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter0Timer
    ((VehMid6SsmCounter0Timer)(tmp_b < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_b : (sint32)(uint16)tmp_b));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timeout
    (DataTypeConversion25_h);

  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion8'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1MessageID
    (DataTypeConversion8);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion43'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_VehMid6SsmCounter1Timer
    ((VehMid6SsmCounter1Timer)(tmp_a < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_a : (sint32)(uint16)tmp_a));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timeout
    (DataTypeConversion26_k);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion9'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0MessageID
    (DataTypeConversion9);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion44'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter0Timer
    ((AcuMid3SsmCounter0Timer)(tmp_9 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_9 : (sint32)(uint16)tmp_9));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timeout
    (DataTypeConversion27_g);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion10'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1MessageID
    (DataTypeConversion10);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion45'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid3SsmCounter1Timer
    ((AcuMid3SsmCounter1Timer)(tmp_8 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_8 : (sint32)(uint16)tmp_8));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timeout
    (DataTypeConversion28);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion11'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0MessageID
    (DataTypeConversion11_i);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion46'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter0Timer
    ((AcuMid5SsmCounter0Timer)(tmp_7 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_7 : (sint32)(uint16)tmp_7));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timeout
    (DataTypeConversion29);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion12'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1MessageID
    (DataTypeConversion12);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion47'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid5SsmCounter1Timer
    ((AcuMid5SsmCounter1Timer)(tmp_6 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_6 : (sint32)(uint16)tmp_6));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timeout
    (DataTypeConversion30_l);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion13'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0MessageID
    (DataTypeConversion13);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion48'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter0Timer
    ((AcuMid6SsmCounter0Timer)(tmp_5 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_5 : (sint32)(uint16)tmp_5));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1Timeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timeout
    (DataTypeConversion31);

  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1MessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion14'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1MessageID
    (DataTypeConversion14);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1Timer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion49'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuMid6SsmCounter1Timer
    ((AcuMid6SsmCounter1Timer)(tmp_4 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_4 : (sint32)(uint16)tmp_4));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_AcuFbCanTimeout' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimeout
    (DataTypeConversion35_l);

  /* Outport: '<Root>/FallbackSystemStatus_AcuFbCanMessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion15'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanMessageID
    (DataTypeConversion15);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_AcuFbCanTimer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion50'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_AcuFbCanTimer
    ((AcuFbCanTimer)(tmp_3 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_3 :
                     (sint32)(uint16)tmp_3));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_FrontCameraCanMessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion16'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanMessageID
    (DataTypeConversion16);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_FrontCameraCanTimer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion52'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCanTimer
    ((FrontCameraCanTimer)(tmp_1 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_1 : (sint32)(uint16)tmp_1));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_FrontRadarCanMessageID' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion17'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanMessageID
    (DataTypeConversion17);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackSystemStatus_FrontRadarCanTimer' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion51'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCanTimer
    ((FrontRadarCanTimer)(tmp_2 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_2
      : (sint32)(uint16)tmp_2));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackSystemStatus_FrontCameraFailureStatus' incorporates:
   *  SignalConversion generated from: '<S2>/FallbackSystemStatus_FrontCameraFailureStatus'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraFailureStatus
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_FrontCameraFailureStatus);

  /* Outport: '<Root>/FallbackSystemStatus_FrontCameraCalibrationStatus' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion36'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontCameraCalibrationStatus
    (DataTypeConversion36);

  /* Outport: '<Root>/FallbackSystemStatus_FrontRadarFailureStatus' incorporates:
   *  SignalConversion generated from: '<S2>/FallbackSystemStatus_FrontRadarFailureStatus'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarFailureStatus
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_FrontRadarFailureStatus);

  /* Outport: '<Root>/FallbackSystemStatus_FrontRadarCalibrationStatus' incorporates:
   *  DataTypeConversion: '<S47>/Data Type Conversion97'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_FrontRadarCalibrationStatus
    (DataTypeConversion97);

  /* Outport: '<Root>/FallbackSystemStatus_SystemStatusReserved1' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved1
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved1);

  /* Outport: '<Root>/FallbackSystemStatus_SystemStatusReserved2' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved2
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved2);

  /* Outport: '<Root>/FallbackSystemStatus_SystemStatusReserved3' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved3
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved3);

  /* Outport: '<Root>/FallbackSystemStatus_SystemStatusReserved4' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackSystemStatus_SystemStatusReserved4
    (FallbackSigOutput_B.OutportBufferForFallbackSystemStatus_SystemStatusReserved4);

  /* Outport: '<Root>/FallbackDebugInfo_LaneValidState' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion100'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LaneValidState
    (DataTypeConversion100);

  /* Outport: '<Root>/FallbackDebugInfo_FallbackTriggerStatus' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion101'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackTriggerStatus
    (DataTypeConversion101);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackDebugInfo_TrajectoryPosY0' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion53'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryPosY0
    ((TrajectoryPosY0)(rtb_Product1_e_idx_1 < 0.0F ? (sint32)(uint16)-(sint16)
                       (uint16)-rtb_Product1_e_idx_1 : (sint32)(uint16)
                       rtb_Product1_e_idx_1));

  /* Outport: '<Root>/FallbackDebugInfo_TrajectoryHeadingAngle' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion54'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryHeadingAngle
    ((TrajectoryHeadingAngle)(tmp_17 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_17 : (sint32)(uint16)tmp_17));

  /* Outport: '<Root>/FallbackDebugInfo_TrajectoryCurvature' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion55'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvature
    ((TrajectoryCurvature)(tmp_16 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_16 : (sint32)(uint16)tmp_16));

  /* Outport: '<Root>/FallbackDebugInfo_TrajectoryCurvatureChange' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion56'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryCurvatureChange
    (tmp_15 < 0.0F ? (uint32)-(sint32)(uint32)-tmp_15 : (uint32)tmp_15);

  /* Outport: '<Root>/FallbackDebugInfo_TrajectoryLength' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion57'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TrajectoryLength
    ((TrajectoryLength)(tmp_14 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_14
                        : (sint32)(uint16)tmp_14));

  /* Outport: '<Root>/FallbackDebugInfo_EgoLaneWidth' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion58'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoLaneWidth
    ((EgoLaneWidth)(tmp_13 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_13 :
                    (sint32)(uint16)tmp_13));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackDebugInfo_LateralSystemState' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralSystemState
    (DataTypeConversion59);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackDebugInfo_LateralDistanceError' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion60'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceError
    ((LateralDistanceError)(tmp_12 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_12 : (sint32)(uint16)tmp_12));

  /* Outport: '<Root>/FallbackDebugInfo_HeadingAngleError' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion64'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleError
    ((HeadingAngleError)(tmp_11 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_11 : (sint32)(uint16)tmp_11));

  /* Outport: '<Root>/FallbackDebugInfo_LateralDistanceErrorWeight' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion65'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LateralDistanceErrorWeight
    ((LateralDistanceErrorWeight)((uint8)(sint8)tmp_10 & 127));

  /* Outport: '<Root>/FallbackDebugInfo_HeadingAngleErrorWeight' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion66'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_HeadingAngleErrorWeight
    ((HeadingAngleErrorWeight)((uint8)(sint8)tmp_z & 127));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackDebugInfo_LqrIterationNums' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion67'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationNums
    (rtb_SecAdNomALgtReqGroupSafePosLimForJerk);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackDebugInfo_LqrIterationError' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion68'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LqrIterationError
    ((LqrIterationError)(tmp_y < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_y :
      (sint32)(uint16)tmp_y));

  /* Outport: '<Root>/FallbackDebugInfo_SteerAngleByLQR' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion71'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleByLQR
    ((SteerAngleByLQR)(tmp_x < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_x :
                       (sint32)(uint16)tmp_x));

  /* Outport: '<Root>/FallbackDebugInfo_FeedforwardsSteerAngle' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion72'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FeedforwardsSteerAngle
    ((FeedforwardsSteerAngle)(tmp_w < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_w : (sint32)(uint16)tmp_w));

  /* Outport: '<Root>/FallbackDebugInfo_RawSteerAngle' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion73'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawSteerAngle
    ((RawSteerAngle)(tmp_v < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_v :
                     (sint32)(uint16)tmp_v));

  /* Outport: '<Root>/FallbackDebugInfo_LimitSteerAngle' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion74'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitSteerAngle
    ((LimitSteerAngle)(tmp_u < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_u :
                       (sint32)(uint16)tmp_u));

  /* Outport: '<Root>/FallbackDebugInfo_MaxSteerAngleThreshold' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion75'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleThreshold
    ((MaxSteerAngleThreshold)(tmp_t < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_t : (sint32)(uint16)tmp_t));

  /* Outport: '<Root>/FallbackDebugInfo_MaxSteerAngleRateThreshold' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion76'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MaxSteerAngleRateThreshold
    ((MaxSteerAngleRateThreshold)(tmp_s < 0.0F ? (sint32)(uint8)-(sint8)(uint8)-
      tmp_s : (sint32)(uint8)tmp_s));

  /* Outport: '<Root>/FallbackDebugInfo_AccRequestBySpeed' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion94'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestBySpeed
    ((AccRequestBySpeed)(tmp_k < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_k :
      (sint32)(uint16)tmp_k));

  /* Outport: '<Root>/FallbackDebugInfo_GradientLimitAccRequest' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion93'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_GradientLimitAccRequest
    ((GradientLimitAccRequest)(tmp_l < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_l : (sint32)(uint16)tmp_l));

  /* Outport: '<Root>/FallbackDebugInfo_LimitAccRequest' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion92'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LimitAccRequest
    ((LimitAccRequest)(tmp_m < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_m :
                       (sint32)(uint16)tmp_m));

  /* Outport: '<Root>/FallbackDebugInfo_SafeDistance' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion91'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SafeDistance
    ((SafeDistance)(tmp_n < 0.0F ? (sint32)(uint8)-(sint8)(uint8)-tmp_n :
                    (sint32)(uint8)tmp_n));

  /* Outport: '<Root>/FallbackDebugInfo_TimeToCollison' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion90'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_TimeToCollison
    ((TimeToCollison)(tmp_o < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_o :
                      (sint32)(uint16)tmp_o));

  /* Outport: '<Root>/FallbackDebugInfo_EgoStopTime' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion89'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EgoStopTime
    ((EgoStopTime)(tmp_p < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_p :
                   (sint32)(uint16)tmp_p));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackDebugInfo_ObjectStopTime' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion88'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_ObjectStopTime
    (DataTypeConversion88);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackDebugInfo_LongNecAcc' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion86'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongNecAcc
    ((LongNecAcc)(tmp_q < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_q :
                  (sint32)(uint16)tmp_q));

  /* Outport: '<Root>/FallbackDebugInfo_LongAccRequest' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion85'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_LongAccRequest
    ((LongAccRequest)(tmp_r < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_r :
                      (sint32)(uint16)tmp_r));

  /* Outport: '<Root>/FallbackDebugInfo_AccRequestByOutOfOdd' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestByOutOfOdd
    ((AccRequestByOutOfOdd)(tmp_18 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_18 : (sint32)(uint16)tmp_18));

  /* Outport: '<Root>/FallbackDebugInfo_AccRequestForSystemError' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion77'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestForSystemError
    ((AccRequestForSystemError)(tmp_18 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)
      -tmp_18 : (sint32)(uint16)tmp_18));

  /* Outport: '<Root>/FallbackDebugInfo_SteerAngleForSystemError' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion78'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngleForSystemError
    ((SteerAngleForSystemError)(tmp_j < 0.0F ? (sint32)(uint16)-(sint16)(uint16)
      -tmp_j : (sint32)(uint16)tmp_j));

  /* Outport: '<Root>/FallbackDebugInfo_RawAccRequest' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion79'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_RawAccRequest
    ((RawAccRequest)(tmp_18 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-tmp_18 :
                     (sint32)(uint16)tmp_18));

  /* Outport: '<Root>/FallbackDebugInfo_MinAccRate' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion80'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_MinAccRate((uint8)
    fmodf(floorf(rtb_Abs1 / 0.05F), 256.0F));

  /* Outport: '<Root>/FallbackDebugInfo_AccRequestAfterRateLimit' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion81'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_AccRequestAfterRateLimit
    ((AccRequestAfterRateLimit)(tmp_18 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)
      -tmp_18 : (sint32)(uint16)tmp_18));

  /* Outport: '<Root>/FallbackDebugInfo_EmergencyBrakeAcc' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion82'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_EmergencyBrakeAcc
    ((EmergencyBrakeAcc)(tmp_18 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
      tmp_18 : (sint32)(uint16)tmp_18));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve1' incorporates:
   *  SignalConversion generated from: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve1
    (FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve1);

  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve2' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve2
    (DataTypeConversion84);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve3' incorporates:
   *  Abs: '<S46>/Abs'
   *  BusCreator: '<S7>/Bus Creator14'
   *  DataTypeConversion: '<S46>/Data Type Conversion1'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve3
    ((uint16)fmodf(floorf(fabsf(rtb_EAD_MaxSteeringAngleRate)), 65536.0F));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve4' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve4
    (FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve4);

  /* Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve5' */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_FallbackDebugInfoReserve5
    (FallbackSigOutput_B.OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve5);

  /* RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
   *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
   */
  /* DataTypeConversion: '<S46>/Data Type Conversion83' incorporates:
   *  BusCreator: '<S7>/Bus Creator14'
   */
  rtb_Product1_e_idx_0 = fmodf(floorf((rtb_EAD_MaxSteeringAngleRate + 780.0F) /
    0.1F), 65536.0F);

  /* Outport: '<Root>/FallbackDebugInfo_SteerAngle' incorporates:
   *  DataTypeConversion: '<S46>/Data Type Conversion83'
   */
  Rte_IWrite_FallbackSigOutput_10ms_Runnable_FallbackDebugInfo_SteerAngle
    ((uint16)(rtb_Product1_e_idx_0 < 0.0F ? (sint32)(uint16)-(sint16)(uint16)-
              rtb_Product1_e_idx_0 : (sint32)(uint16)rtb_Product1_e_idx_0));

  /* End of Outputs for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */
}

/* Model initialize function */
void FallbackSigOutput_Init(void)
{
  {
    FBS_DebugInfo_Struct FBS_DebugInfo;
    SG_AdDirReq BusCreator9;
    SG_AdNomALgtReqGroupSafe BusCreator1;
    SG_AdPrimALgtLimReqGroupSafe BusCreator2;
    SG_AdPrimWhlAgReqGroupSafe BusCreator;
    SG_AdSecALgtLimReqGroupSafe BusCreator10;
    SG_AdSecWhlAgReqGroupSafe BusCreator4;
    SG_AdStandStillReq BusCreator8;
    SG_AdWhlLockReq BusCreator11;
    SG_AdpLiReqFromAPI BusCreator13;
    SG_AutnmsDrvStReq BusCreator3;
    SG_HmiAutnmsSts BusCreator7;
    SG_SecAdNomALgtReqGroupSafe BusCreator5;
    SG_SecAdWhlLockReq BusCreator12;
    SG_VehOperStReq BusCreator6;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' incorporates:
     *  SubSystem: '<Root>/FallbackSignalOutput_10ms_Runnable'
     */
    /* InitializeConditions for UnitDelay: '<S6>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_h =
      FallbackSigOutput_P.UnitDelay_InitialCondition_ev;

    /* InitializeConditions for UnitDelay: '<S11>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE =
      FallbackSigOutput_P.UnitDelay_InitialCondition;

    /* InitializeConditions for UnitDelay: '<S21>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_k =
      FallbackSigOutput_P.UnitDelay_InitialCondition_f;

    /* InitializeConditions for UnitDelay: '<S24>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_iy =
      FallbackSigOutput_P.UnitDelay_InitialCondition_b;

    /* InitializeConditions for UnitDelay: '<S20>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_l =
      FallbackSigOutput_P.UnitDelay_InitialCondition_e;

    /* InitializeConditions for UnitDelay: '<S19>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_ki =
      FallbackSigOutput_P.UnitDelay_InitialCondition_g;

    /* InitializeConditions for UnitDelay: '<S36>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_k =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_f;

    /* InitializeConditions for UnitDelay: '<S10>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_h =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_p4;

    /* InitializeConditions for UnitDelay: '<S35>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_c =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_p;

    /* InitializeConditions for UnitDelay: '<S37>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_d =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_fc;

    /* InitializeConditions for UnitDelay: '<S40>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_c =
      FallbackSigOutput_P.UnitDelay_InitialCondition_e5;

    /* InitializeConditions for UnitDelay: '<S44>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_d =
      FallbackSigOutput_P.UnitDelay_InitialCondition_j;

    /* InitializeConditions for UnitDelay: '<S41>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_i =
      FallbackSigOutput_P.UnitDelay_InitialCondition_p;

    /* InitializeConditions for UnitDelay: '<S45>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_et =
      FallbackSigOutput_P.UnitDelay_InitialCondition_a;

    /* InitializeConditions for UnitDelay: '<S10>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_a =
      FallbackSigOutput_P.UnitDelay_InitialCondition_c;

    /* InitializeConditions for UnitDelay: '<S33>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_o =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_b;

    /* InitializeConditions for UnitDelay: '<S34>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE_l =
      FallbackSigOutput_P.UnitDelay1_InitialCondition_k;

    /* InitializeConditions for UnitDelay: '<S38>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_f =
      FallbackSigOutput_P.UnitDelay_InitialCondition_eo;

    /* InitializeConditions for UnitDelay: '<S42>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_o =
      FallbackSigOutput_P.UnitDelay_InitialCondition_cb;

    /* InitializeConditions for UnitDelay: '<S39>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_kp =
      FallbackSigOutput_P.UnitDelay_InitialCondition_i;

    /* InitializeConditions for UnitDelay: '<S43>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_ey =
      FallbackSigOutput_P.UnitDelay_InitialCondition_pm;

    /* InitializeConditions for UnitDelay: '<S50>/Output' */
    FallbackSigOutput_DW.Output_DSTATE =
      FallbackSigOutput_P.Output_InitialCondition_l;

    /* InitializeConditions for UnitDelay: '<S49>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE[0] =
      FallbackSigOutput_P.UnitDelay1_InitialCondition;

    /* InitializeConditions for UnitDelay: '<S49>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_ls[0] =
      FallbackSigOutput_P.UnitDelay_InitialCondition_je;

    /* InitializeConditions for UnitDelay: '<S49>/Unit Delay1' */
    FallbackSigOutput_DW.UnitDelay1_DSTATE[1] =
      FallbackSigOutput_P.UnitDelay1_InitialCondition;

    /* InitializeConditions for UnitDelay: '<S49>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_ls[1] =
      FallbackSigOutput_P.UnitDelay_InitialCondition_je;

    /* InitializeConditions for UnitDelay: '<S53>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_l =
      FallbackSigOutput_P.Output_InitialCondition_a;

    /* InitializeConditions for UnitDelay: '<S60>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_lj =
      FallbackSigOutput_P.Output_InitialCondition_he;

    /* InitializeConditions for UnitDelay: '<S61>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_n =
      FallbackSigOutput_P.UnitDelay_InitialCondition_fd;

    /* InitializeConditions for UnitDelay: '<S64>/Unit Delay' */
    FallbackSigOutput_DW.UnitDelay_DSTATE_eyn =
      FallbackSigOutput_P.UnitDelay_InitialCondition_h;

    /* SystemInitialize for Iterator SubSystem: '<S58>/Bool2UINT16' */
    /* SystemInitialize for S-Function (sfix_bitop): '<S65>/Bitwise AND1' incorporates:
     *  Outport: '<S65>/Out1'
     */
    EAD_RawSelfCheckStatus = FallbackSigOutput_P.Out1_Y0;

    /* End of SystemInitialize for SubSystem: '<S58>/Bool2UINT16' */

    /* SystemInitialize for SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_AdFusedFricEstimn' */
    FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_AdFusedFricEstimn =
      FallbackSigOutput_rtZSG_AdFusedFricEstimn_adt;

    /* SystemInitialize for SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_SwtExtrLiFromAPI' */
    FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_SwtExtrLiFromAPI =
      FallbackSigOutput_rtZSG_SwtExtrLiFromAPI;

    /* SystemInitialize for SignalConversion generated from: '<S2>/VIMMid3CanFr13_SG_AdFreeDst' */
    FallbackSigOutput_B.OutportBufferForVIMMid3CanFr13_SG_AdFreeDst =
      FallbackSigOutput_rtZSG_AdFreeDst_adt;

    /* InitializeConditions for UnitDelay: '<S79>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_b =
      FallbackSigOutput_P.Output_InitialCondition;

    /* SystemInitialize for CCaller: '<S78>/C Caller3' incorporates:
     *  Outport: '<S78>/VTS_ChecksumEscDa'
     */
    VTS_ChecksumEscDa = FallbackSigOutput_P.VTS_ChecksumEscDa_Y0;

    /* SystemInitialize for UnitDelay: '<S79>/Output' incorporates:
     *  Outport: '<S78>/VTS_AliveCounterEscDa'
     */
    VTS_AliveCounterEscDa = FallbackSigOutput_P.VTS_AliveCounterEscDa_Y0;

    /* End of SystemInitialize for SubSystem: '<S70>/ESC_DA_MESSAGE_E2E' */
    /* InitializeConditions for UnitDelay: '<S85>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_a =
      FallbackSigOutput_P.Output_InitialCondition_h;

    /* End of SystemInitialize for SubSystem: '<S71>/FrontWheelSpeedKPH_E2E' */

    /* SystemInitialize for CCaller: '<S84>/C Caller3' incorporates:
     *  Outport: '<S71>/VTS_ChecksumFrntWhl'
     */
    VTS_ChecksumFrntWhl = FallbackSigOutput_P.VTS_ChecksumFrntWhl_Y0;

    /* SystemInitialize for UnitDelay: '<S85>/Output' incorporates:
     *  Outport: '<S71>/VTS_AliveCounterFrntWhl'
     */
    VTS_AliveCounterFrntWhl = FallbackSigOutput_P.VTS_AliveCounterFrntWhl_Y0;

    /* SystemInitialize for Switch: '<S83>/Switch4' incorporates:
     *  Outport: '<S71>/VTS_WheelDirectionFrntRi'
     */
    VTS_WheelDirectionFrntRi = FallbackSigOutput_P.VTS_WheelDirectionFrntRi_Y0;

    /* SystemInitialize for Switch: '<S83>/Switch5' incorporates:
     *  Outport: '<S71>/VTS_SpeedInvalidFrntRi'
     */
    VTS_SpeedInvalidFrntRi = FallbackSigOutput_P.VTS_SpeedInvalidFrntRi_Y0;

    /* SystemInitialize for Switch: '<S83>/Switch3' incorporates:
     *  Outport: '<S71>/VTS_WheelSpeedFrntRi'
     */
    VTS_WheelSpeedFrntRi = FallbackSigOutput_P.VTS_WheelSpeedFrntRi_Y0;

    /* SystemInitialize for Switch: '<S82>/Switch4' incorporates:
     *  Outport: '<S71>/VTS_WheelDirectionFrntLe'
     */
    VTS_WheelDirectionFrntLe = FallbackSigOutput_P.VTS_WheelDirectionFrntLe_Y0;

    /* SystemInitialize for Switch: '<S82>/Switch5' incorporates:
     *  Outport: '<S71>/VTS_SpeedInvalidFrntLe'
     */
    VTS_SpeedInvalidFrntLe = FallbackSigOutput_P.VTS_SpeedInvalidFrntLe_Y0;

    /* SystemInitialize for Switch: '<S82>/Switch3' incorporates:
     *  Outport: '<S71>/VTS_WheelSpeedFrntLe'
     */
    VTS_WheelSpeedFrntLe = FallbackSigOutput_P.VTS_WheelSpeedFrntLe_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/ESC_FrontWheelSpeedKPH' */
    /* InitializeConditions for UnitDelay: '<S91>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_i =
      FallbackSigOutput_P.Output_InitialCondition_f;

    /* End of SystemInitialize for SubSystem: '<S72>/FrontWheelSpeedKPH_E2E' */

    /* SystemInitialize for CCaller: '<S88>/C Caller4' incorporates:
     *  Outport: '<S72>/VTS_ChecksumReWhl'
     */
    VTS_ChecksumReWhl = FallbackSigOutput_P.VTS_ChecksumReWhl_Y0;

    /* SystemInitialize for UnitDelay: '<S91>/Output' incorporates:
     *  Outport: '<S72>/VTS_AliveCounterReWhl'
     */
    VTS_AliveCounterReWhl = FallbackSigOutput_P.VTS_AliveCounterReWhl_Y0;

    /* SystemInitialize for Switch: '<S90>/Switch4' incorporates:
     *  Outport: '<S72>/VTS_WheelDirectionReRi'
     */
    VTS_WheelDirectionReRi = FallbackSigOutput_P.VTS_WheelDirectionReRi_Y0;

    /* SystemInitialize for Switch: '<S90>/Switch5' incorporates:
     *  Outport: '<S72>/VTS_SpeedInvalidReRi'
     */
    VTS_SpeedInvalidReRi = FallbackSigOutput_P.VTS_SpeedInvalidReRi_Y0;

    /* SystemInitialize for Switch: '<S90>/Switch3' incorporates:
     *  Outport: '<S72>/VTS_WheelSpeedReRi'
     */
    VTS_WheelSpeedReRi = FallbackSigOutput_P.VTS_WheelSpeedReRi_Y0;

    /* SystemInitialize for Switch: '<S89>/Switch4' incorporates:
     *  Outport: '<S72>/VTS_WheelDirectionReLe'
     */
    VTS_WheelDirectionReLe = FallbackSigOutput_P.VTS_WheelDirectionReLe_Y0;

    /* SystemInitialize for Switch: '<S89>/Switch5' incorporates:
     *  Outport: '<S72>/VTS_SpeedInvalidReLe'
     */
    VTS_SpeedInvalidReLe = FallbackSigOutput_P.VTS_SpeedInvalidReLe_Y0;

    /* SystemInitialize for Switch: '<S89>/Switch3' incorporates:
     *  Outport: '<S72>/VTS_WheelSpeedReLe'
     */
    VTS_WheelSpeedReLe = FallbackSigOutput_P.VTS_WheelSpeedReLe_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/ESC_RearWheelSpeedKPH' */

    /* SystemInitialize for Enabled SubSystem: '<S66>/ESC_Status' */
    /* SystemInitialize for Atomic SubSystem: '<S73>/ESC_Status_E2E' */
    /* Start for Constant: '<S94>/Constant9' */
    VTS_VehicleSpeedInvalid = FallbackSigOutput_P.Constant9_Value_e;

    /* InitializeConditions for UnitDelay: '<S96>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_j =
      FallbackSigOutput_P.Output_InitialCondition_d;

    /* End of SystemInitialize for SubSystem: '<S73>/ESC_Status_E2E' */

    /* SystemInitialize for CCaller: '<S94>/C Caller5' incorporates:
     *  Outport: '<S73>/VTS_EscStatusChecksum'
     */
    VTS_EscStatusChecksum = FallbackSigOutput_P.VTS_EscStatusChecksum_Y0;

    /* SystemInitialize for UnitDelay: '<S96>/Output' incorporates:
     *  Outport: '<S73>/VTS_EscStatusAliveCounter'
     */
    VTS_EscStatusAliveCounter = FallbackSigOutput_P.VTS_EscStatusAliveCounter_Y0;

    /* SystemInitialize for Switch: '<S95>/Switch' incorporates:
     *  Outport: '<S73>/VTS_VehicleSpeed'
     */
    VTS_VehicleSpeed = FallbackSigOutput_P.VTS_VehicleSpeed_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/ESC_Status' */
    /* InitializeConditions for UnitDelay: '<S102>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_n =
      FallbackSigOutput_P.Output_InitialCondition_m;

    /* End of SystemInitialize for SubSystem: '<S74>/SAS_Status_E2E' */

    /* SystemInitialize for CCaller: '<S100>/C Caller2' incorporates:
     *  Outport: '<S74>/VTS_SASChecksum'
     */
    VTS_SASChecksum = FallbackSigOutput_P.VTS_SASChecksum_Y0;

    /* SystemInitialize for UnitDelay: '<S102>/Output' incorporates:
     *  Outport: '<S74>/VTS_SASAliveCounter'
     */
    VTS_SASAliveCounter = FallbackSigOutput_P.VTS_SASAliveCounter_Y0;

    /* SystemInitialize for Switch: '<S99>/Switch3' incorporates:
     *  Outport: '<S74>/VTS_SASFailureSts'
     */
    VTS_SASFailureSts = FallbackSigOutput_P.VTS_SASFailureSts_Y0;

    /* SystemInitialize for Switch: '<S99>/Switch4' incorporates:
     *  Outport: '<S74>/VTS_SASCalibrationSts'
     */
    VTS_SASCalibrationSts = FallbackSigOutput_P.VTS_SASCalibrationSts_Y0;

    /* SystemInitialize for Switch: '<S99>/Switch2' incorporates:
     *  Outport: '<S74>/VTS_SteerWheelRotSpd'
     */
    VTS_SteerWheelRotSpd = FallbackSigOutput_P.VTS_SteerWheelRotSpd_Y0;

    /* SystemInitialize for Switch: '<S99>/Switch1' incorporates:
     *  Outport: '<S74>/VTS_SteerWheelAngle'
     */
    VTS_SteerWheelAngle = FallbackSigOutput_P.VTS_SteerWheelAngle_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/SAS_Status' */

    /* SystemInitialize for Chart: '<S75>/TaskForRadar' */
    FallbackSigOutput_DW.TimeCntr_e = TimeCntr;

    /* InitializeConditions for UnitDelay: '<S110>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_c =
      FallbackSigOutput_P.Output_InitialCondition_o;

    /* End of SystemInitialize for SubSystem: '<S76>/YRS1_E2E' */

    /* SystemInitialize for UnitDelay: '<S110>/Output' incorporates:
     *  Outport: '<S76>/VTS_YRS1AliveCounter'
     */
    VTS_YRS1AliveCounter = FallbackSigOutput_P.VTS_YRS1AliveCounter_Y0;

    /* SystemInitialize for Switch: '<S106>/Switch4' incorporates:
     *  Outport: '<S76>/VTS_YawRate'
     */
    VTS_YawRate = FallbackSigOutput_P.VTS_YawRate_Y0;

    /* SystemInitialize for Switch: '<S106>/Switch2' incorporates:
     *  Outport: '<S76>/VTS_LateralAcce'
     */
    VTS_LateralAcce = FallbackSigOutput_P.VTS_LateralAcce_Y0;

    /* SystemInitialize for Switch: '<S106>/Switch3' incorporates:
     *  Outport: '<S76>/VTS_LateralSensorState'
     */
    VTS_LateralSensorState = FallbackSigOutput_P.VTS_LateralSensorState_Y0;

    /* SystemInitialize for Switch: '<S106>/Switch5' incorporates:
     *  Outport: '<S76>/VTS_YawRateSensorState'
     */
    VTS_YawRateSensorState = FallbackSigOutput_P.VTS_YawRateSensorState_Y0;

    /* SystemInitialize for CCaller: '<S107>/C Caller' incorporates:
     *  Outport: '<S76>/VTS_YRS1Checksum'
     */
    VTS_YRS1Checksum = FallbackSigOutput_P.VTS_YRS1Checksum_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/YRS1' */
    /* InitializeConditions for UnitDelay: '<S116>/Output' */
    FallbackSigOutput_DW.Output_DSTATE_p =
      FallbackSigOutput_P.Output_InitialCondition_m4;

    /* End of SystemInitialize for SubSystem: '<S77>/YRS2_E2E' */

    /* SystemInitialize for UnitDelay: '<S116>/Output' incorporates:
     *  Outport: '<S77>/VTS_YRS2AliveCounter'
     */
    VTS_YRS2AliveCounter = FallbackSigOutput_P.VTS_YRS2AliveCounter_Y0;

    /* SystemInitialize for Switch: '<S113>/Switch2' incorporates:
     *  Outport: '<S77>/VTS_LongitAcce'
     */
    VTS_LongitAcce = FallbackSigOutput_P.VTS_LongitAcce_Y0;

    /* SystemInitialize for Switch: '<S113>/Switch1' incorporates:
     *  Outport: '<S77>/VTS_LongitSensorState'
     */
    VTS_LongitSensorState = FallbackSigOutput_P.VTS_LongitSensorState_Y0;

    /* SystemInitialize for CCaller: '<S114>/C Caller1' incorporates:
     *  Outport: '<S77>/VTS_YRS2Checksum'
     */
    VTS_YRS2Checksum = FallbackSigOutput_P.VTS_YRS2Checksum_Y0;

    /* End of SystemInitialize for SubSystem: '<S66>/YRS2' */

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator7' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr04_SG_HmiAutnmsSts'
     */
    BusCreator7 = FallbackSigOutput_P.VIMMid3CanFr04_SG_HmiAutnmsSts_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator3' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr07_SG_AutnmsDrvStReq'
     */
    BusCreator3 = FallbackSigOutput_P.VIMMid3CanFr07_SG_AutnmsDrvStReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator6' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr08_SG_VehOperStReq'
     */
    BusCreator6 = FallbackSigOutput_P.VIMMid3CanFr08_SG_VehOperStReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator8' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr09_SG_AdStandStillReq'
     */
    BusCreator8 = FallbackSigOutput_P.VIMMid3CanFr09_SG_AdStandStillReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator9' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr09_SG_AdDirReq'
     */
    BusCreator9 = FallbackSigOutput_P.VIMMid3CanFr09_SG_AdDirReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator13' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr11_SG_AdpLiReqFromAPI'
     */
    BusCreator13 = FallbackSigOutput_P.VIMMid3CanFr11_SG_AdpLiReqFromAPI_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator11' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr13_SG_AdWhlLockReq'
     */
    BusCreator11 = FallbackSigOutput_P.VIMMid3CanFr13_SG_AdWhlLockReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator1' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe'
     */
    BusCreator1 = FallbackSigOutput_P.VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator2' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe'
     */
    BusCreator2 =
      FallbackSigOutput_P.VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator' incorporates:
     *  Outport: '<S2>/VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe'
     */
    BusCreator =
      FallbackSigOutput_P.VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator4' incorporates:
     *  Outport: '<S2>/VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe'
     */
    BusCreator4 =
      FallbackSigOutput_P.VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator10' incorporates:
     *  Outport: '<S2>/VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe'
     */
    BusCreator10 =
      FallbackSigOutput_P.VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator5' incorporates:
     *  Outport: '<S2>/VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe'
     */
    BusCreator5 =
      FallbackSigOutput_P.VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator12' incorporates:
     *  Outport: '<S2>/VIMBMid6CanFdFr29_SG_SecAdWhlLockReq'
     */
    BusCreator12 = FallbackSigOutput_P.VIMBMid6CanFdFr29_SG_SecAdWhlLockReq_Y0;

    /* SystemInitialize for BusCreator: '<S7>/Bus Creator14' incorporates:
     *  Outport: '<S2>/FBS_DebugInfo'
     */
    FBS_DebugInfo = FallbackSigOutput_P.FBS_DebugInfo_Y0;

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated from: '<Root>/FallbackSigOutput_10ms_Runnable' */

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_QDCFRS' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_QDCFRS'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_QDCFRS
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_QDCFRS_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_BrakeTempTooHigh' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_BrakeTempTooHigh'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_BrakeTempTooHigh_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_DTC_Active' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_DTC_Active'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DTC_Active
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_DTC_Active_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_Vehiclestandstill' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_Vehiclestandstill'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_Vehiclestandstill
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_Vehiclestandstill_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_AWB_available' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_AWB_available'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_available
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_AWB_available_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_AWB_active' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_AWB_active'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AWB_active
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_AWB_active_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_AEB_available' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_AEB_available'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_available
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_AEB_available_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_AEB_active' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_AEB_active'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_AEB_active
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_AEB_active_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABP_available' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_ABP_available'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_available
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_ABP_available_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABA_available' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_ABA_available'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_available
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_ABA_available_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABP_active' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_ABP_active'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABP_active
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_ABP_active_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_ABA_active' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_ABA_active'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_ABA_active
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_ABA_active_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_DiagExtModSts' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_DiagExtModSts'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_DiagExtModSts
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_DiagExtModSts_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_DA_MESSAGE_ESC_NoBrakeForce' incorporates:
     *  Outport: '<S2>/ESC_DA_MESSAGE_ESC_NoBrakeForce'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_DA_MESSAGE_ESC_NoBrakeForce
      (FallbackSigOutput_P.ESC_DA_MESSAGE_ESC_NoBrakeForce_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH' incorporates:
     *  Outport: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH
      (FallbackSigOutput_P.ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelDirection' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelDirection'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RRWheelDirection_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelDirection' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelDirection'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RLWheelDirection_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH' incorporates:
     *  Outport: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH
      (FallbackSigOutput_P.ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_Status_Checksum' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_Status_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_Status_Checksum
      (FallbackSigOutput_P.ESC_Status_ESC_Status_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_Status_AliveCounter' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_Status_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_Status_AliveCounter
      (FallbackSigOutput_P.ESC_Status_ESC_Status_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_HHCActive' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_HHCActive'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_HHCActive
      (FallbackSigOutput_P.ESC_Status_ESC_HHCActive_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_BrakePedalSwitchInvalid' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_BrakePedalSwitchInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchInvalid
      (FallbackSigOutput_P.ESC_Status_ESC_BrakePedalSwitchInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_EPBStatus' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_EPBStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_EPBStatus
      (FallbackSigOutput_P.ESC_Status_ESC_EPBStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_AVHStatus' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_AVHStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_AVHStatus
      (FallbackSigOutput_P.ESC_Status_ESC_AVHStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_VehicleSpeedInvalid' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_VehicleSpeedInvalid'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeedInvalid
      (FallbackSigOutput_P.ESC_Status_ESC_VehicleSpeedInvalid_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_PATAResponse' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_PATAResponse'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_PATAResponse
      (FallbackSigOutput_P.ESC_Status_ESC_PATAResponse_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_VehicleSpeed' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_VehicleSpeed'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_VehicleSpeed
      (FallbackSigOutput_P.ESC_Status_ESC_VehicleSpeed_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_ESPFailed' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_ESPFailed'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ESPFailed
      (FallbackSigOutput_P.ESC_Status_ESC_ESPFailed_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_ESPActive' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_ESPActive'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ESPActive
      (FallbackSigOutput_P.ESC_Status_ESC_ESPActive_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_TCSActive' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_TCSActive'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_TCSActive
      (FallbackSigOutput_P.ESC_Status_ESC_TCSActive_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_BrakePedalSwitchStatus' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_BrakePedalSwitchStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_BrakePedalSwitchStatus
      (FallbackSigOutput_P.ESC_Status_ESC_BrakePedalSwitchStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/ESC_Status_ESC_ABSActive' incorporates:
     *  Outport: '<S2>/ESC_Status_ESC_ABSActive'
     */
    Rte_IWrite_FallbackSigOutput_Init_ESC_Status_ESC_ABSActive
      (FallbackSigOutput_P.ESC_Status_ESC_ABSActive_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_Status_Checksum' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_Status_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_Status_Checksum
      (FallbackSigOutput_P.SAS_Status_SAS_Status_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_Status_AliveCounter' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_Status_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_Status_AliveCounter
      (FallbackSigOutput_P.SAS_Status_SAS_Status_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_FailureSts' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_FailureSts'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_FailureSts
      (FallbackSigOutput_P.SAS_Status_SAS_FailureSts_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_CalibrationSts' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_CalibrationSts'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_CalibrationSts
      (FallbackSigOutput_P.SAS_Status_SAS_CalibrationSts_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_SteerWheelRotSpd' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_SteerWheelRotSpd'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelRotSpd
      (FallbackSigOutput_P.SAS_Status_SAS_SteerWheelRotSpd_Y0);

    /* SystemInitialize for Outport: '<Root>/SAS_Status_SAS_SteerWheelAngle' incorporates:
     *  Outport: '<S2>/SAS_Status_SAS_SteerWheelAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_SAS_Status_SAS_SteerWheelAngle
      (FallbackSigOutput_P.SAS_Status_SAS_SteerWheelAngle_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS1_AliveCounter' incorporates:
     *  Outport: '<S2>/YRS1_YRS1_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS1_AliveCounter
      (FallbackSigOutput_P.YRS1_YRS1_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS_YawRate' incorporates:
     *  Outport: '<S2>/YRS1_YRS_YawRate'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_YawRate
      (FallbackSigOutput_P.YRS1_YRS_YawRate_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS_LateralAcce' incorporates:
     *  Outport: '<S2>/YRS1_YRS_LateralAcce'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_LateralAcce
      (FallbackSigOutput_P.YRS1_YRS_LateralAcce_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS_LateralSensorState' incorporates:
     *  Outport: '<S2>/YRS1_YRS_LateralSensorState'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_LateralSensorState
      (FallbackSigOutput_P.YRS1_YRS_LateralSensorState_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS_YawRateSensorState' incorporates:
     *  Outport: '<S2>/YRS1_YRS_YawRateSensorState'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS_YawRateSensorState
      (FallbackSigOutput_P.YRS1_YRS_YawRateSensorState_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS1_YRS1_Checksum' incorporates:
     *  Outport: '<S2>/YRS1_YRS1_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS1_YRS1_Checksum
      (FallbackSigOutput_P.YRS1_YRS1_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS2_YRS_AliveCounter' incorporates:
     *  Outport: '<S2>/YRS2_YRS_AliveCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_AliveCounter
      (FallbackSigOutput_P.YRS2_YRS_AliveCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS2_YRS_LongitAcce' incorporates:
     *  Outport: '<S2>/YRS2_YRS_LongitAcce'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_LongitAcce
      (FallbackSigOutput_P.YRS2_YRS_LongitAcce_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS2_YRS_LongitSensorState' incorporates:
     *  Outport: '<S2>/YRS2_YRS_LongitSensorState'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS_LongitSensorState
      (FallbackSigOutput_P.YRS2_YRS_LongitSensorState_Y0);

    /* SystemInitialize for Outport: '<Root>/YRS2_YRS2_Checksum' incorporates:
     *  Outport: '<S2>/YRS2_YRS2_Checksum'
     */
    Rte_IWrite_FallbackSigOutput_Init_YRS2_YRS2_Checksum
      (FallbackSigOutput_P.YRS2_YRS2_Checksum_Y0);

    /* SystemInitialize for Outport: '<Root>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C' incorporates:
     *  Outport: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C
      (FallbackSigOutput_P.SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C_Y0);

    /* SystemInitialize for Outport: '<Root>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C' incorporates:
     *  Outport: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C
      (FallbackSigOutput_P.SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C_Y0);

    /* SystemInitialize for Outport: '<Root>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C' incorporates:
     *  Outport: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C
      (FallbackSigOutput_P.SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C_Y0);

    /* SystemInitialize for Outport: '<Root>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C' incorporates:
     *  Outport: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C
      (FallbackSigOutput_P.SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C_Y0);

    /* SystemInitialize for Outport: '<Root>/VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C' incorporates:
     *  Outport: '<S2>/VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C
      (FallbackSigOutput_P.VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C_Y0);

    /* SystemInitialize for Outport: '<Root>/VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C' incorporates:
     *  Outport: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C
      (FallbackSigOutput_P.VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C_Y0);

    /* SystemInitialize for Outport: '<Root>/VCU1Mid3CanFr08_1V1R_YawRate1_C' incorporates:
     *  Outport: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr08_1V1R_YawRate1_C
      (FallbackSigOutput_P.VCU1Mid3CanFr08_1V1R_YawRate1_C_Y0);

    /* SystemInitialize for Outport: '<Root>/VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C' incorporates:
     *  Outport: '<S2>/VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C
      (FallbackSigOutput_P.VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C_Y0);

    /* SystemInitialize for Outport: '<Root>/SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C' incorporates:
     *  Outport: '<S2>/SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C'
     */
    Rte_IWrite_FallbackSigOutput_Init_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C
      (FallbackSigOutput_P.SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr04_UDcDcAvlLoSideExt' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr04_UDcDcAvlLoSideExt'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_UDcDcAvlLoSideExt
      (FallbackSigOutput_P.VIMMid3CanFr04_UDcDcAvlLoSideExt_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr04_IDcDcAvlMaxLoSideExt' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr04_IDcDcAvlMaxLoSideExt'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt
      (FallbackSigOutput_P.VIMMid3CanFr04_IDcDcAvlMaxLoSideExt_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr04_IDcDcAvlLoSideExt' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr04_IDcDcAvlLoSideExt'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_IDcDcAvlLoSideExt
      (FallbackSigOutput_P.VIMMid3CanFr04_IDcDcAvlLoSideExt_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr04_SG_HmiAutnmsSts' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr04_SG_HmiAutnmsSts
      (&BusCreator7);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr07_SG_AutnmsDrvStReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr07_SG_AutnmsDrvStReq
      (&BusCreator3);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr08_SG_VehOperStReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr08_SG_VehOperStReq
      (&BusCreator6);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr09_VehUsgStReq' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr09_VehUsgStReq'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_VehUsgStReq
      (FallbackSigOutput_P.VIMMid3CanFr09_VehUsgStReq_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr09_SG_AdStandStillReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdStandStillReq
      (&BusCreator8);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr09_SG_AdDirReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr09_SG_AdDirReq(&BusCreator9);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr11_SG_SwtExtrLiFromAPI' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_SwtExtrLiFromAPI
      (&FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_SwtExtrLiFromAPI);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr11_SG_AdpLiReqFromAPI' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdpLiReqFromAPI
      (&BusCreator13);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr11_SG_AdFusedFricEstimn' incorporates:
     *  SignalConversion generated from: '<S2>/VIMMid3CanFr11_SG_AdFusedFricEstimn'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr11_SG_AdFusedFricEstimn
      (&FallbackSigOutput_B.OutportBufferForVIMMid3CanFr11_SG_AdFusedFricEstimn);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr13_AdSetSpd' incorporates:
     *  Outport: '<S2>/VIMMid3CanFr13_AdSetSpd'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_AdSetSpd
      (FallbackSigOutput_P.VIMMid3CanFr13_AdSetSpd_Y0);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr13_SG_AdWhlLockReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdWhlLockReq
      (&BusCreator11);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr13_SG_AdFreeDst' incorporates:
     *  SignalConversion generated from: '<S2>/VIMMid3CanFr13_SG_AdFreeDst'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr13_SG_AdFreeDst
      (&FallbackSigOutput_B.OutportBufferForVIMMid3CanFr13_SG_AdFreeDst);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator1'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe
      (&BusCreator1);

    /* SystemInitialize for Outport: '<Root>/VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator2'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe
      (&BusCreator2);

    /* SystemInitialize for Outport: '<Root>/VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe
      (&BusCreator);

    /* SystemInitialize for Outport: '<Root>/VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator4'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe
      (&BusCreator4);

    /* SystemInitialize for Outport: '<Root>/VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator10'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe
      (&BusCreator10);

    /* SystemInitialize for Outport: '<Root>/VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe' incorporates:
     *  BusCreator: '<S7>/Bus Creator5'
     */
    Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe
      (&BusCreator5);

    /* SystemInitialize for Outport: '<Root>/VIMBMid6CanFdFr29_SG_SecAdWhlLockReq' */
    Rte_IWrite_FallbackSigOutput_Init_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq
      (&BusCreator12);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_FallbackSelfCheckStatus' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_FallbackSelfCheckStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FallbackSelfCheckStatus
      (FallbackSigOutput_P.FbAcuAvailable_FallbackSelfCheckStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_VehControlStatus' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_VehControlStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_VehControlStatus
      (FallbackSigOutput_P.FbAcuAvailable_VehControlStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_Sensor1v1rStatus' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_Sensor1v1rStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_Sensor1v1rStatus
      (FallbackSigOutput_P.FbAcuAvailable_Sensor1v1rStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_McuStatus' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_McuStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_McuStatus
      (FallbackSigOutput_P.FbAcuAvailable_McuStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_FbAcuReserved' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_FbAcuReserved'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FbAcuReserved
      (FallbackSigOutput_P.FbAcuAvailable_FbAcuReserved_Y0);

    /* SystemInitialize for Outport: '<Root>/FbAcuAvailable_FbAcuRollingCounter' incorporates:
     *  Outport: '<S2>/FbAcuAvailable_FbAcuRollingCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_FbAcuAvailable_FbAcuRollingCounter
      (FallbackSigOutput_P.FbAcuAvailable_FbAcuRollingCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/VCU1Mid3CanFr06_CarTiGlb_A' incorporates:
     *  Outport: '<S2>/VCU1Mid3CanFr06_CarTiGlb_A'
     */
    Rte_IWrite_FallbackSigOutput_Init_VCU1Mid3CanFr06_ACU_CarTiGlb_A
      (FallbackSigOutput_P.VCU1Mid3CanFr06_CarTiGlb_A_Y0);

    /* SystemInitialize for Outport: '<Root>/FBS_DebugInfo' */
    Rte_IWrite_FallbackSigOutput_Init_FBS_DebugInfo_FBS_DebugInfo(&FBS_DebugInfo);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AswSoftwarewareVersion' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AswSoftwarewareVersion'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AswSoftwarewareVersion
      (FallbackSigOutput_P.FallbackSystemStatus_AswSoftwarewareVersion_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid3VcuCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid3VcuCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid3VcuCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid5SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid5SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid5SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_VehMid6SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_VehMid6SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_VehMid6SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid3SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid3SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid3SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid5SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid5SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid5SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter0Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter0MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter0Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter0Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter0Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1Timeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter1Timeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1MessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1MessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1MessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter1MessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuMid6SsmCounter1Timer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuMid6SsmCounter1Timer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuMid6SsmCounter1Timer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuFbCanTimeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuFbCanTimeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimeout
      (FallbackSigOutput_P.FallbackSystemStatus_AcuFbCanTimeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuFbCanMessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuFbCanMessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanMessageID
      (FallbackSigOutput_P.FallbackSystemStatus_AcuFbCanMessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_AcuFbCanTimer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_AcuFbCanTimer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_AcuFbCanTimer
      (FallbackSigOutput_P.FallbackSystemStatus_AcuFbCanTimer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontCameraCanTimeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontCameraCanTimeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimeout
      (FallbackSigOutput_P.FallbackSystemStatus_FrontCameraCanTimeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontCameraCanMessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontCameraCanMessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanMessageID
      (FallbackSigOutput_P.FallbackSystemStatus_FrontCameraCanMessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontCameraCanTimer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontCameraCanTimer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCanTimer
      (FallbackSigOutput_P.FallbackSystemStatus_FrontCameraCanTimer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontRadarCanTimeout' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontRadarCanTimeout'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimeout
      (FallbackSigOutput_P.FallbackSystemStatus_FrontRadarCanTimeout_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontRadarCanMessageID' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontRadarCanMessageID'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanMessageID
      (FallbackSigOutput_P.FallbackSystemStatus_FrontRadarCanMessageID_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontRadarCanTimer' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontRadarCanTimer'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCanTimer
      (FallbackSigOutput_P.FallbackSystemStatus_FrontRadarCanTimer_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontCameraCalibrationStatus' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontCameraCalibrationStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontCameraCalibrationStatus
      (FallbackSigOutput_P.FallbackSystemStatus_FrontCameraCalibrationStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_FrontRadarCalibrationStatus' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_FrontRadarCalibrationStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_FrontRadarCalibrationStatus
      (FallbackSigOutput_P.FallbackSystemStatus_FrontRadarCalibrationStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackSystemStatus_SystemStatusRollingCounter' incorporates:
     *  Outport: '<S2>/FallbackSystemStatus_SystemStatusRollingCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackSystemStatus_SystemStatusRollingCounter
      (FallbackSigOutput_P.FallbackSystemStatus_SystemStatusRollingCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LaneValidState' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LaneValidState'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LaneValidState
      (FallbackSigOutput_P.FallbackDebugInfo_LaneValidState_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_FallbackTriggerStatus' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_FallbackTriggerStatus'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackTriggerStatus
      (FallbackSigOutput_P.FallbackDebugInfo_FallbackTriggerStatus_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TrajectoryPosY0' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TrajectoryPosY0'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryPosY0
      (FallbackSigOutput_P.FallbackDebugInfo_TrajectoryPosY0_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TrajectoryHeadingAngle' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TrajectoryHeadingAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryHeadingAngle
      (FallbackSigOutput_P.FallbackDebugInfo_TrajectoryHeadingAngle_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TrajectoryCurvature' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TrajectoryCurvature'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvature
      (FallbackSigOutput_P.FallbackDebugInfo_TrajectoryCurvature_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TrajectoryCurvatureChange' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TrajectoryCurvatureChange'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryCurvatureChange
      (FallbackSigOutput_P.FallbackDebugInfo_TrajectoryCurvatureChange_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TrajectoryLength' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TrajectoryLength'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TrajectoryLength
      (FallbackSigOutput_P.FallbackDebugInfo_TrajectoryLength_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_EgoLaneWidth' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_EgoLaneWidth'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EgoLaneWidth
      (FallbackSigOutput_P.FallbackDebugInfo_EgoLaneWidth_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LateralSystemState' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LateralSystemState'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralSystemState
      (FallbackSigOutput_P.FallbackDebugInfo_LateralSystemState_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LateralDistanceError' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LateralDistanceError'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceError
      (FallbackSigOutput_P.FallbackDebugInfo_LateralDistanceError_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_HeadingAngleError' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_HeadingAngleError'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleError
      (FallbackSigOutput_P.FallbackDebugInfo_HeadingAngleError_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LateralDistanceErrorWeight' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LateralDistanceErrorWeight'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralDistanceErrorWeight
      (FallbackSigOutput_P.FallbackDebugInfo_LateralDistanceErrorWeight_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_HeadingAngleErrorWeight' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_HeadingAngleErrorWeight'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleErrorWeight
      (FallbackSigOutput_P.FallbackDebugInfo_HeadingAngleErrorWeight_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LqrIterationNums' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LqrIterationNums'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationNums
      (FallbackSigOutput_P.FallbackDebugInfo_LqrIterationNums_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LqrIterationError' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LqrIterationError'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LqrIterationError
      (FallbackSigOutput_P.FallbackDebugInfo_LqrIterationError_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LateralContribution' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LateralContribution'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LateralContribution
      (FallbackSigOutput_P.FallbackDebugInfo_LateralContribution_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_HeadingAngleContribution' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_HeadingAngleContribution'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_HeadingAngleContribution
      (FallbackSigOutput_P.FallbackDebugInfo_HeadingAngleContribution_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_SteerAngleByLQR' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_SteerAngleByLQR'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleByLQR
      (FallbackSigOutput_P.FallbackDebugInfo_SteerAngleByLQR_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_FeedforwardsSteerAngle' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_FeedforwardsSteerAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FeedforwardsSteerAngle
      (FallbackSigOutput_P.FallbackDebugInfo_FeedforwardsSteerAngle_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_RawSteerAngle' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_RawSteerAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_RawSteerAngle
      (FallbackSigOutput_P.FallbackDebugInfo_RawSteerAngle_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LimitSteerAngle' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LimitSteerAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LimitSteerAngle
      (FallbackSigOutput_P.FallbackDebugInfo_LimitSteerAngle_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_MaxSteerAngleThreshold' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_MaxSteerAngleThreshold'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleThreshold
      (FallbackSigOutput_P.FallbackDebugInfo_MaxSteerAngleThreshold_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_MaxSteerAngleRateThreshold' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_MaxSteerAngleRateThreshold'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MaxSteerAngleRateThreshold
      (FallbackSigOutput_P.FallbackDebugInfo_MaxSteerAngleRateThreshold_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_AccRequestBySpeed' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_AccRequestBySpeed'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestBySpeed
      (FallbackSigOutput_P.FallbackDebugInfo_AccRequestBySpeed_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_GradientLimitAccRequest' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_GradientLimitAccRequest'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_GradientLimitAccRequest
      (FallbackSigOutput_P.FallbackDebugInfo_GradientLimitAccRequest_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LimitAccRequest' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LimitAccRequest'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LimitAccRequest
      (FallbackSigOutput_P.FallbackDebugInfo_LimitAccRequest_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_SafeDistance' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_SafeDistance'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SafeDistance
      (FallbackSigOutput_P.FallbackDebugInfo_SafeDistance_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_TimeToCollison' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_TimeToCollison'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_TimeToCollison
      (FallbackSigOutput_P.FallbackDebugInfo_TimeToCollison_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_EgoStopTime' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_EgoStopTime'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EgoStopTime
      (FallbackSigOutput_P.FallbackDebugInfo_EgoStopTime_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_ObjectStopTime' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_ObjectStopTime'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_ObjectStopTime
      (FallbackSigOutput_P.FallbackDebugInfo_ObjectStopTime_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_AvoidCollisionEnable' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_AvoidCollisionEnable'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AvoidCollisionEnable
      (FallbackSigOutput_P.FallbackDebugInfo_AvoidCollisionEnable_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LongNecAcc' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LongNecAcc'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LongNecAcc
      (FallbackSigOutput_P.FallbackDebugInfo_LongNecAcc_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_LongAccRequest' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_LongAccRequest'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_LongAccRequest
      (FallbackSigOutput_P.FallbackDebugInfo_LongAccRequest_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_AccRequestByOutOfOdd' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_AccRequestByOutOfOdd'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestByOutOfOdd
      (FallbackSigOutput_P.FallbackDebugInfo_AccRequestByOutOfOdd_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_AccRequestForSystemError' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_AccRequestForSystemError'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestForSystemError
      (FallbackSigOutput_P.FallbackDebugInfo_AccRequestForSystemError_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_SteerAngleForSystemError' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_SteerAngleForSystemError'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngleForSystemError
      (FallbackSigOutput_P.FallbackDebugInfo_SteerAngleForSystemError_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_RawAccRequest' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_RawAccRequest'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_RawAccRequest
      (FallbackSigOutput_P.FallbackDebugInfo_RawAccRequest_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_MinAccRate' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_MinAccRate'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_MinAccRate
      (FallbackSigOutput_P.FallbackDebugInfo_MinAccRate_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_AccRequestAfterRateLimit' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_AccRequestAfterRateLimit'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_AccRequestAfterRateLimit
      (FallbackSigOutput_P.FallbackDebugInfo_AccRequestAfterRateLimit_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_EmergencyBrakeAcc' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_EmergencyBrakeAcc'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_EmergencyBrakeAcc
      (FallbackSigOutput_P.FallbackDebugInfo_EmergencyBrakeAcc_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve2' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve2'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve2
      (FallbackSigOutput_P.FallbackDebugInfo_FallbackDebugInfoReserve2_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoReserve3' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve3'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoReserve3
      (FallbackSigOutput_P.FallbackDebugInfo_FallbackDebugInfoReserve3_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_FallbackDebugInfoRollingCounter' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_FallbackDebugInfoRollingCounter'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_FallbackDebugInfoRollingCounter
      (FallbackSigOutput_P.FallbackDebugInfo_FallbackDebugInfoRollingCounter_Y0);

    /* SystemInitialize for Outport: '<Root>/FallbackDebugInfo_SteerAngle' incorporates:
     *  Outport: '<S2>/FallbackDebugInfo_SteerAngle'
     */
    Rte_IWrite_FallbackSigOutput_Init_FallbackDebugInfo_SteerAngle
      (FallbackSigOutput_P.FallbackDebugInfo_SteerAngle_Y0);
  }
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
