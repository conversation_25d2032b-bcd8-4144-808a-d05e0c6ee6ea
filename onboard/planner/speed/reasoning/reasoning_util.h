#ifndef ONBOARD_PLANNER_SPEED_REASONING_REASONING_UTIL_H_
#define ONBOARD_PLANNER_SPEED_REASONING_REASONING_UTIL_H_

#include <cstdint>
#include <map>
#include <sstream>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "math/range.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/junction_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/zone_traffic_rule.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner/speed/solver/speed_result.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "voy_protos/perception_object_type.pb.h"

// This file contains the util functions which depend on wrapped
// reasoning classes from reasoning folder: WorldContext, ReasoningObject,
// TrajectoryInfo and AgentTrajectoryInfo.

namespace planner {
namespace speed {
namespace reasoning_util {

// Returns true if reasoning_object is a leading agent that we should NOT try to
// pass and should try to acc. Ego is expected to smoothly acc the leading agent
// without trying to overtake. Most of the leading agent are normal leading
// vehicles at the same lane with Ego and a few others are mild cut-in vehicles
// or merging vehicles that we do not want Ego to overtake.
// The other situation is the leader of target gap in lane change progress.
// The agent must have the object_type that Ego CanAccAgent to.
// The agent should move roughly along Ego's path and be close enough. The
// agent's rear bumper should be at least min_ra_arc_length ahead Ego's rear
// axle to be considered as a leading agent.
// TODO(speed): consider use ML method to identify LEAD agents.
bool IsLeadingAgent(const TrajectoryInfo& trajectory_info,
                    const ReasoningObject& reasoning_object,
                    double min_ra_arc_length);

// Returns true if ego is relatively safe with leading agent. It considers if
// agent's projection on ego path is in front of Ego's front bumper for a
// distance, or ego will not approach leading agent in a period.
bool IsSafeWithLeadingAgent(const ReasoningObject& reasoning_object,
                            double ego_speed_mps, double ra_to_front_bumper);

// Agents are considered fully behind Ego if their projection on ego path
// is behind Ego's rear bumper, and their abs lateral distance is within
// certain distance so that they are at the same lane or the neighboring lane
// of Ego.
bool IsFullyBehind(const ReasoningObject& reasoning_object,
                   double ra_to_rear_bumper_shift);

// Returns true if the agent is behind ego's front axle.
bool IsBehindEgoFrontAxle(const ReasoningObject& reasoning_object,
                          double ra_to_front_axle);

// Returns true if the entire bounding box of the agent is behind Ego's leading
// bumper.
bool IsBehindEgoLeadingBumper(const ReasoningObject& reasoning_object,
                              double ra_to_leading_bumper);

// Returns true if the entire bounding box of the agent is ahead of
// Ego's leading bumper.
bool IsFullyAhead(const ReasoningObject& reasoning_object,
                  double ra_to_leading_bumper);

// Return true if the agent is likely tailgating ego on object level.
// The object should be firstly fully behind ego and close to ego path.
bool IsLikelyTailgating(const TrajectoryInfo& trajectory_info,
                        const ReasoningObject& reasoning_object,
                        double ra_to_rear_bumper_shift,
                        int64_t plan_init_timestamp = -1);

// Returns true if the reasoning object is the dominant stuck object for xlane
// nudge and current lane-keep homotopy yields this object.
bool IsDominantStuckObjectForXLaneNudge(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if this OverlapRegion should be considered in the
// TailgaterReasoner.
bool ShouldConsiderOverlapAsTailgating(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region);

// Returns true if Ego path is overtaking a reasoning_object.
bool IsEgoPathOvertaking(const ReasoningObject& reasoning_object,
                         const planner::pb::IntentionResult& ego_intention);

// Returns true if Ego path is preferable to drive through the FoD.
bool IsFoDPreferableToDriveThrough(
    const ReasoningObject& reasoning_object,
    const planner::pb::IntentionResult& ego_intention);

// Returns true if the agent is in ego's neighboring lane (either left or
// right). The neighboring lane should be a driving lane.
bool IsAgentInNeighboringLane(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object);

// Returns true if the agent is in any of ego's neighboring lane (either left or
// right) along the entire lane sequence. The neighboring lane should be a
// driving lane.
bool IsAgentInNeighboringLaneSequence(const TrajectoryInfo& trajectory_info,
                                      const ReasoningObject& reasoning_object);

// Returns true if the overlap region is cutting out from ego path.
bool IsCutOutOverlapRegion(const pb::OverlapRegion& overlap_region);

// Returns true if the trajectory is cutting in ego path.
bool IsAgentTrajectoryCuttingInEgoPath(
    const AgentTrajectoryInfo& agent_trajectory_info);

// Returns true if ego is about to turn along with a large vehicle in a
// junction.
bool IsEgoAboutToTurnAlongWithLargeVehicleInJunction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory);

// Returns true if the trajectory is cutting out from ego path.
bool IsAgentTrajectoryCuttingOutFromEgoPath(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info);

// Returns true if ego is merging to higher priority conflicting lane, aka yield
// conflicting lane in hdmap. The main target scene is ego is merging to
// straight lane when ego taking right turn.
bool IsEgoMergingToHigherPriorityConflictingLane(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region);

// Returns true if the given object is a cyclist and its speed is larger than a
// threshold.
bool IsLikelyTurboPed(const ReasoningObject& reasoning_object);

// Returns true if the overlap region has crossing direction motion type.
bool IsCrossingOverlapRegion(const pb::OverlapRegion& overlap_region);

bool WithEBInterestedRange(const WorldContext& world_context,
                           const ReasoningObject& reasoning_object);
// TODO(speed): As the code volume grows, move following functions associated
// with emergency brake to a dedicated file.
// Returns true if the reasoning object is PED/CYC/VEHICLE and the object has
// already encroached our path and within some longitudinal range.
bool ShouldAllowEmergencyBrake(const WorldContext& world_context,
                               const ReasoningObject& reasoning_object);

// Returns true if we will allow emergency brake for vehicles and cyclists in
// LeadAndMergeAgentReasoner.
bool ShouldAllowEmergencyBrakeForLeadAndMergeAgentReasoner(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix,
    pb::LeadAndMergeEBDebug* emergency_brake_debug);

// Returns true if the EB should be allowed for the VRU.
bool ShouldAllowEmergencyBrakeForVRU(const WorldContext& world_context,
                                     const ReasoningObject& reasoning_object,
                                     const pb::OverlapRegion& overlap_region,
                                     pb::ReasonerId reasoner_in_charge);

// TODO(hezhihang): This function is used to compute the frequency of wrongly
// triggering EB for VRU. After collecting enough data, it should be removed.
bool ShouldAllowEmergencyBrakeForVRUWithLargerScope(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region, pb::ReasonerId reasoner_in_charge);

// TODO(hezhihang): We need to disable the feature of enlarging scope of EB for
// VRU on release version. Thus, the functions,
// ShouldAllowEmergencyBrakeForCrossVRU and
// ShouldAllowEmergencyBrakeForMergingCyclist, are reserved. After collecting
// enough data on master, we should remove this.
// Returns true if VRUs are crossing Ego path and close to Ego longitudinally
// and laterally.
bool ShouldAllowEmergencyBrakeForCrossVRU(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region, pb::ReasonerId reasoner_in_charge);

// Returns true if EB should be allowed for the cyclist merging into Ego path.
bool ShouldAllowEmergencyBrakeForMergingCyclist(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region, pb::ReasonerId reasoner_in_charge);

// Returns adjusted required lateral gap curved so that the comfort required
// lateral gap is slightly small than the min lateral gap of the overlap region.
// The result will be bounded to the minimum of the critical required lateral
// gap and the input argument |comfort_required_lateral_gap_lower_bound|.
DiscomfortVarying ComputeComfortRequiredLateralGapBaseOnOverlapRegion(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    double comfort_required_lateral_gap_lower_bound = 0.0);

// Return true if object is consistent yield object.
bool ShouldAddExtraBufferForConsistentYieldObject(
    const WorldContext& world_context, const ReasoningObject& reasoning_object);

// Computes the extra yield distance for agent which caused blockage.
// In order to leave enough longitudinal space for active lane change.
DiscomfortVarying ComputeYieldExtraDistanceForBlockage(
    const ReasoningObject& reasoning_object, const RobotState& robot_state,
    double comfort_yield_extra_distance, double critical_yield_extra_distance);

bool ShouldAdjustYieldExtraDistanceForPullOver(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

bool ShouldConsiderDefensiveDrivingForReversingVehicles(
    const ReasoningObject& reasoning_object);

bool ShouldBeCautiousForBusNearBusBulb(const ReasoningObject& reasoning_object,
                                       const TrajectoryInfo& trajectory_info);

bool IsVehicleEncroachingEgoLane(const ReasoningObject& reasoning_object,
                                 const TrajectoryInfo& trajectory_info);

bool ShouldBeCautiousAroundEncroachingEgoLaneVehicle(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info);

bool ShouldReducePassRequiredLateralGapUnderCriticalToUnstuck(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info, const double ego_speed);

bool IsAgentExtraCautiousOnTrajectory(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info);

// We use this function to get different threshold for different scenario type
double GetYieldIntentionThreshold(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info);

// Computes the pass required lateral gap for traffic cones to unstuck,
// considering their heights are usually shorter than that of the ego's side
// mirror.
double ComputeUnstuckPassRequiredLatGapForTrafficCone(
    const ReasoningObject& reasoning_object);

// There are two kinds of check in this function:
// 1, check whether the type from prediction is unknown.
// 2, check whether the scenario is a scenario of interest to use yield
// intention.
// The reason for the redundant check is that in this way planning can
// control when to use the yield intention, otherwise planning will totally
// depend on prediction's output and will be very sensitive to prediction's
// changes.
bool ShouldConsiderYieldIntention(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& ego_trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info);

// Return true if the agent is taking the first exiting road ahead of ego in
// roundabout to leave while ego's in roundabout and the agent is currently on
// inner lanes of roundabout w.r.t ego.
bool IsAgentExitingRoundaboutFromInnerLanesOfEgo(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info);

// Returns true if the agent current pose is in the same lane section along
// ego's lane sequence.
// NOTE: For agents in junction, particularly for
// stationary ones, it's hard to find the exact lane they currently stay on.
// Special logic is under development. For now, the function defaults to not
// process agents in the junction unless users explicitly set the flag and get
// well aware of the risk of using the result.
bool IsAgentAheadDrivingInSameLaneSectionAlongEgoLaneSequence(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    bool include_agent_in_junction = false);

// Returns true if the overlap slice is fully on the encroachment side of the
// ego center. Note we consider the encroached boundary as the reference.
bool IsOverlapSliceFullyOnTheEncroachedSideOfEgo(
    const TrajectoryInfo& trajectory_info,
    const math::geometry::OrientedBox2d& bounding_box, bool is_left_encroach);

// Computes an estimated yield extra distance between ego's front bumper and the
// agent's rear bumper.
double ComputeYieldDistanceToAgentRearBumper(
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const int overlap_region_ix, const double ra_to_leading_bumper);

// Returns the result of overlap region encroachment info.
speed::OverlapRegionEncroachmentInfo ComputeOverlapRegionEncroachmentInfo(
    const TrajectoryInfo& trajectory_info,
    const planner::RobotState& robot_state,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pb::OverlapRegion& overlap_region);

// Returns lane association result for a given predicted trajectory and seed
// info via ad-hoc calling the util function. NOTE: Ad-hoc calling prohibits the
// duplicate computation for trajectories being already associated in world
// model. Also, users need to be aware of the fact that only vehicles and
// cyclists are allowed for lane association. Additionally, as the underlining
// algorithm has not been optimized for cyclists, users should call this
// function judiciously when it's really necessary by setting the flag
// |allow_using_for_cyclist|.
std::vector<route_association::MapElementAndPoseInfo>
GetAdHocAssociatedRouteForPredictedTrajectory(
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const planner::pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seed,
    bool allow_using_for_cyclist);

// Overloaded util function to return lane association result for a given
// reasoning object and predicted trajectory via ad-hoc calling.
std::vector<route_association::MapElementAndPoseInfo>
GetAdHocAssociatedRouteForPredictedTrajectory(
    const ReasoningObject& reasoning_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    bool allow_using_for_cyclist);

// Returns true if the agent is the leading agent for gap align.
bool IsLaneChangeLeadingAgent(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object);

// Returns true if the agent is the leading agent in lane change progress.
bool IsLaneChangeLeadingAgentInProgress(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is the tailing agent for gap align.
bool IsLaneChangeTailingAgent(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object);

// Returns true if the agent is in the target lane during crawl.
bool IsTargetRegionAgentDuringLaneChangeCrawl(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is in the target lane during lane change.
bool IsTargetRegionAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is in the source lane during lane change.
bool IsSourceRegionAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if we should be cautious with the agent during lane change.
bool IsInterestedAgentForCautiousDrivingDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is lane change interested agent during lane change
// or lane change abort
bool IsLaneChangeInterestedAgentDuringLaneChangeOrLaneChangeAbort(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is the tailing agent in lane change progress.
bool IsLaneChangeTailingAgentInProgress(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent is lane change relevant agent.
bool IsLaneChangeAgent(const TrajectoryInfo& trajectory_info,
                       const ReasoningObject& reasoning_object);

// Returns true if the agent is relevant agent in lane change progress.
bool IsLaneChangeAgentInProgress(const TrajectoryInfo& trajectory_info,
                                 const ReasoningObject& reasoning_object);

// Returns true when the agent is behind ego and changes lane together with ego.
// TODO(pengfei): Move this attribute to cross lane object info.
bool IsSynLaneChangeBehind(
    const ReasoningObject& reasoning_object,
    const std::unordered_map<
        int64_t,
        std::unordered_map<std::string, planner::pb::XRegionMotionInfo>>&
        object_xregion_motion_info_map,
    int64_t plan_init_timestamp);

// Returns true when the global discomfort for progress should be set to a high
// value during lane change in progress.
bool ShouldEncourageHighDiscomfortProgressDuringLaneChange(
    const TrajectoryInfo& trajectory_info, double ego_speed);

// Computes the global discomfort for progress for lane change.
// - For lane change crawl and normal lane change at low speed, we encourage the
//   progress at discomfort 0.5 to be able to speed up quickly.
// - For normal lane change in other cases, we encourage a minimal discomfort
//   0.25, and also use the previous discomfort as heuristic.
std::optional<double> ComputeGlobalDiscomfortForProgressForLaneChange(
    const TrajectoryInfo& trajectory_info, double ego_speed);

// Returns true when there is no collision risk in the near future if the agent
// and ego brake with a constant acceleration from the timestamp when there is
// padded overlap.
bool ShouldReduceYieldHeadwayDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region);

// Returns true when we should use discomfort varying yield extra distance for
// soft constraints of agents fully ahead duing lane change, in order to keep up
// with the agent.
bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentAheadDuringLc(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    std::ostringstream& debug_oss);

// Returns true when we should use discomfort varying yield extra distance for
// soft constraints of agents not fully ahead duing lane change, in order to
// keep up with the agent.
bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceForAgentNonAheadDuringLc(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    std::ostringstream& debug_oss);

// Returns true when we should use discomfort varying yield extra distance for
// soft constraints duing lane change, in order to keep up with the leading
// agent.
bool ShouldEnableDiscomfortVaryingSoftYieldExtraDistanceDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const pb::OverlapRegion& overlap_region, std::ostringstream& debug_oss);

// Computes the extra yield extra distance when the agent ahead is braking hard
// during lane change to compensate the prediction speed error.
double ComputeExtraYieldExtraDistanceForLaneChange(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, double ego_speed);

// Return true when there is no agent behind and close to ego on target region,
// which means there is no rear end collision risk when ego brakes hard due to
// the increased yield extra time.
bool ShouldAddYieldExtraTimeDuringLaneChange(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true when we should be cautious with agents with lateral movements
// duing lane change or abort, for which the yield required lateral gap will be
// enlarged to avoid under yield.
bool ShouldBeCautiousWithLateralMovingAgentDuringLaneChange(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Returns true if the agent trajectory overlaps with encroachment segments of
// xlane nudge path.
bool IsXLaneNudgeInterestedTrajectory(
    const TrajectoryInfo& trajectory_info,
    bool is_overlap_region_in_encroachment_regions);

bool ShouldEnableLateralGapVaryingOverlap(
    const ReasoningObject& reasoning_object, pb::ReasonerId reasoner_in_charge,
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region,
    const OverlapRegionInfo& overlap_region_info,
    pb::GenerativeConstraintType::Enum generative_constraint_type,
    const bool yield_always_possible);

// Returns true if the agent is a vehicle and on the right side of ego path
// and has 45~135 degree diff with ego, or on the left side of ego path and
// has -135~-45 diff with ego's path direction.
bool IsNotFacingEgoVehicle(const ReasoningObject& reasoning_object);

// This function computes the min distance between agent and lane boundary.
// Negative means there is encroachment
// Inf means the agent to out of the longitudinal range of the lane boundary
// curve so that we cannot output a signed distance
double ComputeAgentToLaneBoundaryMinDistance(
    const LaneBoundaryPair& lane_boundary_pair,
    const math::geometry::PolygonWithCache2d& agent_contour,
    math::pb::Side side);

// Computes the proximity info of the box corner points with ego path.
pb::ObjectCornerPointsProximityInfo ComputeBoxCornerPointsProximityInfo(
    const adv_geom::Path2dWithJuke& ego_path,
    const math::geometry::OrientedBox2d& box);

std::vector<const pnc_map::Lane*> GetOverlapCoveredEgoLanes(
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region);

// Get the principled critical required gap assuming ego speed is zero.
double GetPrincipledCriticalRequiredLateralGap(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region);

// Returns true if the critical region is a valid squeeze region on the given
// squeeze side.
bool IsValidSqueezeRegionBySide(
    const CriticalRange1d& critical_region,
    const TrajectoryInfo& trajectory_info,
    const pb::ObjectProximityInfo& agent_proximity_info,
    math::pb::Side squeeze_side);

// Returns true if the queried range is in any narrow passages.
bool IsRangeInNarrowPassages(
    const std::vector<speed::pb::NarrowPassageInfo>& narrow_passage_infos,
    double range_start, double range_end);

// Returns true if the agent's overlap region has overlaps with squeeze regions.
bool IsOverlapRegionInSqueezeRegions(
    const TrajectoryInfo& trajectory_info,
    const pb::ObjectProximityInfo& agent_proximity_info,
    const pb::OverlapRegion& overlap_region);

// Returns true if overlap region is a cut-in on ego path in lane keep scenario.
// NOTE: We currently limit the use of this signal to moving vehicles.
// Reference doc:https://cooper.didichuxing.com/docs2/document/2202536545097.
bool IsCutInOverlapRegion(
    const TrajectoryInfo& trajectory_info,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const PlannerObject& planner_object,
    const pb::OverlapRegion& overlap_region);

bool IsTrajectoryCrossingEgoPath(const pb::OverlapRegion& overlap_region);

// Returns true of the padded overlap region is within the first junction ahead
// of ego.
bool IsOverlapRegionInFirstJunction(const TrajectoryInfo& trajectory_info,
                                    const pb::OverlapRegion& overlap_region);

// Returns true if the agent wants to cross lane immediately after Ego passes.
bool DoesVRUWantToCutBehindEgo(const WorldContext& world_context,
                               const TrajectoryInfo& trajectory_info,
                               const ReasoningObject& reasoning_object,
                               const pb::OverlapRegion& overlap_region);

// Returns true if the vehicle wants to cross lane immediately after Ego passes.
bool DoesVehicleWantToCutBehindEgo(const WorldContext& world_context,
                                   const TrajectoryInfo& ego_trajectory_info,
                                   const ReasoningObject& reasoning_object,
                                   const pb::OverlapRegion& overlap_region);

// Returns true if the agent is on sidewalk beside Ego lane.
bool IsVRUOnSidewalkBesideEgoLaneSequence(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object);

// Computes the stop point position based on agent and ego's distance and
// current speed.
double ComputeYieldPositionForOncomingAgent(
    const ReasoningObject& reasoning_object, double ra_to_leading_bumper,
    double ego_speed);

// Returns true if ADV passively interact with other agent, such as 1.) agent
// passive merge into ego lane, 2.) agent cut-in from neighbor lane, 3.) agent
// pass by and overtake ADV, etc.
bool IsEgoPassiveInteractionScene(
    const TrajectoryInfo& trajectory_info,
    const AgentTrajectoryInfo& agent_trajectory_info,
    const ReasoningObject& reasoning_object, int overlap_region_ix,
    bool is_in_considered_pinch_region = false);

bool IsSceneVulnerableToYieldParadox(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const OverlapRegionInfo& overlap_region_info,
    const std::vector<route_association::MapElementAndPoseInfo>& agent_route);

// Returns true if the agent is cyclist and goes straight at where Ego turns
// right or left.
bool IsCyclistGoingStraightAtHookRegion(
    const TrajectoryInfo& trajectory_info, const PlannerObject& planner_object,
    const planner::PredictedTrajectoryWrapper& predicted_trajectory,
    const pb::OverlapRegion& overlap_region, bool check_right_hook);

// Gets a list of candidate lanes being potentially encroached by ego. It won't
// return lanes being encroached due to road structure conflicting.
std::vector<const pnc_map::Lane*> GetCandidateLanesEncroachedByEgo(
    const TrajectoryInfo& trajectory_info,
    const OverlapRegionInfo& overlap_region_info);

// Gets the source lane of the encroachment segment, ego will encroach from the
// source lane to another lane.
// Note: The function will return nullptr if the start arc length of query
// encroachment segment is not inside the range of lane sequence range (rarely
// happens) or when ego is encroaching from the outside of the lane sequence and
// the neighbor lane (which is the source lane) is nullptr.
const pnc_map::Lane* GetEncroachmentSourceLane(
    const TrajectoryInfo& trajectory_info,
    const math::Range1d& encroachment_segment, bool is_left_encroach);

// Returns true if the agent is inside-passing Ego in a left-turn scenario.
// TODO(waylon): Consider inside passing in a right-turn scenario.
bool IsInsideOvertaking(const WorldContext& world_context,
                        const TrajectoryInfo& trajectory_info,
                        const ReasoningObject& reasoning_object,
                        const std::vector<OverlapRegionReference>&
                            predicted_trajectory_overlap_region);

// Computes the average acceleration in the first few seconds (around 3s) on the
// prediction trajectory.
double ComputeAverageAccelerationOnPredictedTrajectory(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info);

// Returns the maximum distance by which ego encroaches out of the lane along
// the path (when driving on the lane marking) before a specified arclength so
// far by considering history in seed.
// NOTE: If ego is not driving on the lane marking, zero will be returned.
double GetEgoTrajectoryMaxOutLaneDistSoFarBeforeArclength(
    const TrajectoryInfo& trajectory_info, double arclength);

// Returns true if the required lateral gap should be enlarged for the laterally
// approaching agent.
bool ShouldEnlargeRequiredLateralGapForLateralApproachAgent(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix);

// Returns true if the cyclist has intention of cutting-in based on the
// current(tracked) state of cyclist, Because the predicted trajectories of
// cyclists are prone to be wrong, they are not a good choice to compute this
// signal.
bool HasCuttingInIntentionBaseOnCyclistTrackedState(
    const ReasoningObject& reasoning_object);

// Returns true if the required lateral gap should be reduced for the low
// speed maneuvering vehicle.
bool ShouldReduceRequiredLateralGapForSlowMovingVehicle(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix);

// Returns true if the required lateral gap should be enlarged for the agent in
// the squeeze region from the nudge side.
bool ShouldEnlargeRequiredLateralGapForAgentInSqueezeRegion(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info, int overlap_region_ix);

// Returns true if the agent is the tailing agent on the rear of the pull over
// parking gap.
bool IsPullOverTailingAgent(const TrajectoryInfo& trajectory_info,
                            const ReasoningObject& reasoning_object);

// Returns true if the lateral gap between object's proximity info and Ego path
// is larger than the lateral gap between Ego path and the Ego road's physical
// boundary.
bool IsObjectOutsidePhysicalBoundaryBaseOnProximityInfo(
    const pb::ObjectProximityInfo& object_proximity_info,
    const pb::PhysicalBoundaryProximityInfo& boundary_proximity_info);

// Returns true if the agent is outside the physical boundary of Ego roads.
bool IsObjectOutsidePhysicalBoundary(
    const ReasoningObject& reasoning_object,
    const speed::pb::PhysicalBoundaryProximityInfoPair&
        physical_boundary_proximity_info);

// One predicted trajectory can have multiple overlap regions, this function
// finds the earliest time when the trajectory starts to have a strict overlap
// with ego path. Return Infinity if the trajectory doesn't have any
// corresponding strict overlap regions.
double FindEarliestStrictRelativeTime(
    const AgentTrajectoryInfo& agent_trajectory_info);

// Returns true if the agent is a potential u-turn blockage. If an agent is
// classified as a potential u-turn blockage, the yield extra distance will be
// reduced and make more room for the following open space unstuck behavior.
bool IsPotentialUTurnBlockage(const TrajectoryInfo& trajectory_info,
                              const ReasoningObject& reasoning_object);

// Finds the leader or tailgator in merge gap info. If for_pass is true, then
// the tailgator is searched from bottom to top according to the proximity info
// and speed results. Similarly, if for_pass is false, the leader is searched
// from top to bottom. NOTE: Both leader and tailgator should belongs to the
// merge gap candidates generated from reasoning pipeline.
ObjectId PopulateMergeLeaderOrTailgatorInfo(
    const speed::SpeedResult& speed_result,
    const std::map<ObjectId, speed::pb::ObjectProximityInfo>&
        object_proximity_info_map,
    const google::protobuf::RepeatedField<google::protobuf::int64>&
        merge_gap_candidates,
    bool for_pass, double& pass_or_yield_state_upper_bound,
    double& pass_or_yield_state_lower_bound);

// Gets the arclength given the reference path and the offset.
// TODO(@waylonwangyanlong): Clean up the current similar usages in
// traffic_rules.
double GetPointArcLengthWithOffset(
    const math::geometry::Point2d& query_point,
    const math::geometry::PolylineCurve2d& reference_path,
    double arclength_offset);

// Returns the segment of overlap region within near future. If the time of the
// first overlap slice is at far away future, the std::nullopt is returned. The
// end of the returned range is not included, i.e. the range is [start, end).
std::optional<std::pair<int, int>> GetOverlapSegmentWithinNearFutureForSafety(
    const pb::OverlapRegion& overlap_region,
    const ReasoningObject& reasoning_object);

// Compute the first hard boundary to ego from the given hard boundary info list
// and ego_ra_arclength wrt corresponding hard boundary lane center line.
const pnc_map::HardBoundaryInfo* GetFirstHardBoundaryAhead(
    const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos,
    const double ego_ra_arclength_in_lane);

// Compute the first yield zone ahead of ego from the given yield zone info
// list.
const traffic_rules::YieldZoneInfo* GetFirstYieldZoneAhead(
    const std::vector<traffic_rules::YieldZoneInfo>& yield_zone_infos);

// Returns the first crosswalk that is ahead of ego or ego is currently in.
const traffic_rules::CrosswalkInLaneSequence* GetFirstCrosswalkAhead(
    const std::vector<traffic_rules::CrosswalkInLaneSequence>& crosswalks);

// Returns the first traffic light info ahead in the lane sequence if existing.
const traffic_rules::TrafficLightInfo* GetFirstTrafficLightAhead(
    const std::vector<traffic_rules::TrafficLightInfo>& traffic_lights);

// Returns true if the agent's overlap region has overlaps with merge and fork
// lanes.
// TODO(waylon): The merge/fork lanes when ego turning in intersections is NOT
// considered in this function. We need to re-consider them if necessary.
bool IsOverlapRegionInMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_merge_fork_lanes_info,
    const pb::OverlapRegion& overlap_region, bool is_checking_merge,
    bool is_active_type);

// Returns true if the agent's overlap region has overlaps with active merge or
// fork lanes.
bool IsOverlapRegionInActiveMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_merge_fork_lanes_info,
    const math::Range1d& overlap_region_range, math::pb::Side agent_side,
    bool is_checking_merge);

// Returns true if the agent's overlap region has overlaps with passive merge or
// fork lanes.
bool IsOverlapRegionInPassiveMergeForkLanes(
    const std::vector<traffic_rules::EgoLaneMergeForkLanesInfo>&
        ego_passive_merge_fork_lanes_infos,
    const math::Range1d& overlap_region_range, math::pb::Side agent_side,
    bool is_checking_merge);

// Returns the extra yield extra time for yield always possible constraint to
// compensate the control error.
double ComputeExtraYieldExtraTimeForYieldAlwaysPossible(
    const WorldContext& world_context, const pb::OverlapRegion& overlap_region,
    double yield_dist_buffer);

// Returns true if the ego path is straight
bool IsEgoTrajectoryStraightFromRearAxleToRearBumper(
    const TrajectoryInfo& trajectory_info, const double ra_to_rear_bumper,
    const double path_straight_threshold, std::string* debug_str);

// Returns true if the queried agent is pull over gap align trailing agent.
bool IsPullOverGapAlignTrailingAgent(const TrajectoryInfo& trajectory_info,
                                     const ReasoningObject& reasoning_object);

// Returns true if there is a yield sign associated with the lane.
bool HasYieldSign(
    const pnc_map::Lane* lane,
    const std::vector<traffic_rules::TrafficSignInLaneSequence>& traffic_signs);

// Returns true if the query lane is in a junction and is an Unprotected Left
// Turn lane.
bool IsUnprotectedLeftTurn(
    const pnc_map::Lane* query_lane,
    const std::vector<traffic_rules::EgoLaneConflictingLanesInfo>&
        conflicting_lanes);

// Returns true if the query lane is in a junction and is a Right Turn Lane.
bool IsRightTurn(const pnc_map::Lane* query_lane);

// Returns true if the query lane is an U-Turn lane.
bool IsUTurn(const pnc_map::Lane* query_lane);

// Returns true if there are any lanes of the specified turn type in a given
// lane sequence.
bool HasSpecifiedTurnTypeInSequence(
    const std::vector<const pnc_map::Lane*>& lanes, hdmap::Lane::Turn turn_type,
    bool consider_regular_lane = false);

// Returns true if it is legal to proceed straight on this lane.
bool IsStraightLane(const pnc_map::Lane* query_lane);

// Returns true if the query lane is in a junction and is an Unprotected Right
// Turn lane.
bool IsUnprotectedRightTurn(
    const pnc_map::Lane* query_lane,
    const voy::TrafficLights& traffic_light_detection,
    const pnc_map::JointPncMapService& joint_pnc_map_service);

// Returns true if the road precedence of a conflicting lane against ego lane
// in the junction is lower.
bool IsRoadPrecedenceLowerAgainstEgoLane(
    const pnc_map::Lane* ego_lane_in_junction,
    const pnc_map::Lane* conflicting_lane,
    const std::vector<traffic_rules::TrafficSignInLaneSequence>& traffic_signs);

// If prefers straight lane, returns the STRAIGHT predecessor of the lane;
// If there are multiple STRAIGHT predecessors, return the first one.
// Otherwise returns the first predecessor of the lane;
// If no predecessor, return nullptr.
const pnc_map::Lane* GetPredecessorLane(const pnc_map::Lane* lane,
                                        bool prefer_straight_lane);

// Computes the corresponding valued of the field,
// |consistent_cross_intention_count_| in ReasoningObject.
int ComputeAgentConsistentCrossIntentionCountForVRU(
    const ReasoningObject& reasoning_object,
    const std::vector<planner::PredictedTrajectoryWrapper>&
        predicted_trajectories,
    const std::vector<speed::pb::OverlapRegion>& overlap_regions,
    const pb::AgentCrossIntentionTracker&
        previous_iter_agent_cross_intention_tracker,
    int64_t current_timestamp);

// Returns true if the agent is moving at a lower speed and is cutting into
// Ego's path with a large steering angle, commonly referred to as a slow-cut-in
// agent.
bool IsSlowCutInAgent(const WorldContext& world_context,
                      const TrajectoryInfo& trajectory_info,
                      const ReasoningObject& reasoning_object,
                      const AgentTrajectoryInfo& agent_trajectory_info,
                      std::string* debug_string_ptr = nullptr);

// Returns true if we should allow max speed for this cyclist to avoid collision
// that could be caused by Ego hard brake.
bool ShouldAllowMaxSpeedForCyclist(const WorldContext& world_context,
                                   const ReasoningObject& reasoning_object);

// Return waypoint assist phase.
planner::pb::AssistManeuverPhase::Enum WaypointAssistPhase(
    const WorldContext& world_context);

// If agent's front bumper arrives the conflicting point than ego's rear axle,
// ART may recall, however, this situation is tricky as ego's front bumper may
// arrives earlier than agent's front bumper. When this situation happens, i.e.
// ART recalls but ego may still have very weak leading advantage, we adjust the
// strong AR to mild AR to avoid FP hard brake.
bool ShouldAdjustARForPotentialNonreactiveAgent(
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info);

// This function uses a constant acceleration model to estimate the time to
// arrive x. As there is a upper bound of v, so the motion may have two parts,
// constant acceleration motion at first and then constant speed motion when the
// v reaches upper bound.
// The design can be find in
// https://kunpeng.xiaojukeji.com/view/revision/4648107.
// At the estimated ego show intention time, ego will reach a position
// x_show_intention with a speed v_show_intention, they meets the following
// equation:
// x_show_intention + v_show_intention ^ 2 / (2 * brake) = x_target_position
double EstimateEgoShowIntentionTime(const double x_target_position,
                                    const double v_0, const double accel,
                                    const double brake, const double v_max);

// This function returns a pair of ego_show_intention_time,
// {comfort_ego_show_intention_time, uncomfort_ego_show_intention_time}.
// comfort_ego_show_intention_time is computed by a comfortable acceleration,
// and the uncomfort_ego_show_intention_time is computed by a relatively
// uncomfort acceleration.
std::pair<double, double> EstimatedEgoShowIntentionTime(
    const TrajectoryInfo& trajectory_info,
    const pb::OverlapRegion& overlap_region, const double ego_speed);

// Returns true if we should consider ego show intention time in the reaction
// time strategy. Currently this function only returns true when ego is in upl,
// and the agent is crossing agents rather than same direction agents.
// TODO(speed): extend this logic to other scenarios, e.g., UPR/U_turn.
bool ShouldConsiderEgoShowIntentionTime(
    const ReasoningObject& reasoning_object,
    const OverlapRegionInfo& overlap_region_info);

// This function retrieves agent_occupied_map_element_infos_in_seed_ptr in
// reasoning object and returns the collection of occupied map elements in
// desired type.
std::vector<planner::pb::MapElementInfo> GetAgentOccupiedMapElements(
    const ReasoningObject& reasoning_object,
    planner::pb::MapElementType::Enum map_element_type);

// This function retrieves current_occupied_map_elements in
// PredictedTrajectoryWrapper and returns the collection of occupied map
// elements in desired type.
std::vector<route_association::MapElementAndPoseInfo>
GetAgentOccupiedMapElements(
    const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories,
    planner::pb::MapElementType::Enum map_element_type);

// This function check whether the agent trajectory is an u-turn trajectory
bool IsUturnTrajectory(const AgentTrajectoryInfo& agent_trajectory_info);

// Returns true if agent is low-precedence slow-cut-in agent in front of Ego.
bool IsLowPrecedenceSlowCutInVehicleInFrontOfEgo(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const AgentTrajectoryInfo& agent_trajectory_info,
    std::string* debug_string_ptr);

// Reduce pass required lateral gap to current gap (with protection) for unstuck
// ego
bool IsPassRequiredLateralGapResettableForStuckAgent(
    const WorldContext& world_context, const ReasoningObject& reasoning_object);

// Returns true if the traffic light just turned green within the
// elapsed_time_thresh.
bool IsTrafficLightJustTurnedGreen(
    const traffic_rules::TrafficLightInfo& traffic_light_info,
    const int64_t elapsed_time_thresh);

// Returns true when we should avoid rushing during UPL.
// We don't apply mild AR and discomfort for progress when this function returns
// true. Currently, this function only checks whether the traffic light just
// turned green and ego's speed is below some threshold. The reason to check
// ego's speed is that there are some junctions where the left turn light turns
// green a few seconds earlier than the going straight light. In that case, ego
// will proceed earlier and pick up some speed, then we can try to rush. We only
// want to be cautious when the two lights turn green simultaneously. NOTE: This
// function doesn't double check whether ego is in upl and has lower road
// precedence, so make sure to call this function when ego is in upl. This
// function is more like a helper function, the reason to store it in the
// reasoning util is both UrgencyInfo & AgentReactionStrategy share this
// function.
bool ShouldBeCautiousDuringUPL(const WorldContext& world_context,
                               const TrajectoryInfo& trajectory_info,
                               const ReasoningObject& reasoning_object);

// Returns true if we should consider applying Mild AR to the
// cyclist/vehicle. The two object types have different logic.
bool ShouldConsiderMildAR(const WorldContext& world_context,
                          const ReasoningObject& reasoning_object,
                          const TrajectoryInfo& trajectory_info,
                          const AgentTrajectoryInfo& agent_trajectory_info,
                          const OverlapRegionInfo& overlap_region_info);

}  // namespace reasoning_util
}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_REASONING_UTIL_H_
