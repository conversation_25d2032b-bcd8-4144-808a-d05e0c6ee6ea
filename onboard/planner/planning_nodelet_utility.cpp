#include "planner/planning_nodelet_utility.h"

#include <cstdint>
#include <string>
#include <utility>
#include <vector>

#include <strings/stringprintf.h>

#include "latency/pipeline_id.h"
#include "planner/behavior/maneuvers/util/maneuver_common.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/world_model/regional_map/regional_path_utility.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "routing/engine/utils.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace planning_vnode {
namespace {

// The ros param name.
constexpr char kPlanningConfigParamName[] = "/planning/config";

// The trip category param name.
constexpr char kTripCategoryParamName[] = "/trip_category";
constexpr char kMappingParamName[] = "mapping";
constexpr char kDataCollectionParamName[] = "data_collection";
// The issue tag for immediate pull over, which is predefined on the trail
// platform.
constexpr int32_t kImmediatePullOverIssueTag = 321;

// The distance to destination threshold to publish route completion when mrm
// pullright is triggered.
constexpr double kDistToDestThresholdToPublishRouteCompletionInMeter = 50.0;
//
constexpr int64_t kTimeLimitToAcceptNewRouteInMs = 1000;

// (TODO): Move this function to util.
// Return true if proposed global route is desired for planner.
bool IsDesiredProposedRoute(const routing::pb::RouteStatus& route_status) {
  const auto& proposed_route_solution = route_status.proposed_route_solution();
  if (proposed_route_solution.replan_purpose() ==
      routing::pb::ReplanPurpose::DESIRED_REPLAN_FOR_LOOP_ROUTE) {
    return true;
  }

  // Accept if proposed global route is following desired direction by desired
  // replan.
  if (proposed_route_solution.replan_purpose() ==
      routing::pb::ReplanPurpose::DESIRED_REPLAN_FOR_NEW_ROUTE) {
    if (proposed_route_solution.desired_route_response().success()) {
      rt_event::PostRtEvent<rt_event::planner::GlobalRouteDesiredReplanSucceed>(
          strings::StringPrintf(
              "Desired replan succeed, route query time is %ld.",
              route_status.proposed_route_query().query_timestamp()));
      return true;
    } else {
      rt_event::PostRtEvent<rt_event::planner::GlobalRouteDesiredReplanFailed>(
          strings::StringPrintf(
              "Desired replan failed, route query time is %ld.",
              route_status.proposed_route_query().query_timestamp()));
      return false;
    }
  }

  // Accept if proposed global route is same as current global route by
  // default replan.
  if (proposed_route_solution.replan_purpose() ==
      routing::pb::ReplanPurpose::DEFAULT_REPLAN_FOR_COSTMAP) {
    if (!AreSameGlobalRoutes(
            proposed_route_solution.route_lanes(),
            route_status.current_route_solution().route_lanes())) {
      // Post rt event for the replanned global route which is expected same
      // as last one but different.
      rt_event::PostRtEvent<rt_event::planner::GlobalChangedRouteAfterReplan>(
          strings::StringPrintf(
              "Global proposed route has changed after replan, route query "
              "time is %ld.",
              route_status.proposed_route_query().query_timestamp()));
      return false;
    } else {
      rt_event::PostRtEvent<rt_event::planner::GlobalMaintainRouteAfterReplan>(
          strings::StringPrintf(
              "Global proposed route maintained after replan, route query time "
              "is %ld.",
              route_status.proposed_route_query().query_timestamp()));
      return true;
    }
  }

  // Accept if proposed global route is from seed query in simulation.
  return true;
}
}  // namespace

pb::PlanningLaneSequence GeneratePlanningLaneSequence(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const pb::AbnormalSequenceSignal& abnormal_sequence_signal,
    const pb::ExtraPlannerSignals::PlannerBehaviorSignal
        planner_behavior_signal) {
  pb::PlanningLaneSequence planning_lane_sequence;
  *planning_lane_sequence.mutable_candidates() =
      behavior_decision.lane_sequence_candidates;
  // Add lane sequence.
  const std::vector<const pnc_map::Lane*>& selected_lane_sequence =
      behavior_decision.lane_sequence.selected_lane_sequence;
  planning_lane_sequence.mutable_lane_sequence()->Reserve(
      selected_lane_sequence.size());
  for (const pnc_map::Lane* lane : selected_lane_sequence) {
    planning_lane_sequence.add_lane_sequence(lane->id());
  }
  // Add untrimmed lane sequence.
  planning_lane_sequence.mutable_untrimmed_lane_sequence()->Reserve(
      behavior_decision.lane_sequence.untrimmed_selected_lane_sequence.size());
  for (const pnc_map::Lane* lane :
       behavior_decision.lane_sequence.untrimmed_selected_lane_sequence) {
    planning_lane_sequence.add_untrimmed_lane_sequence(lane->id());
  }
  // Add current lane IDs.
  planning_lane_sequence.mutable_current_lane_ids()->Reserve(
      behavior_decision.current_lanes.size());
  for (const pnc_map::Lane* lane : behavior_decision.current_lanes) {
    planning_lane_sequence.add_current_lane_ids(lane->id());
  }
  // Add information of immediate pullover destination.
  if (behavior_decision.pull_over_status_info.has_value()) {
    *planning_lane_sequence.mutable_immediate_pullover_destination() =
        behavior_decision.pull_over_status_info->immediate_pullover_destination;
  }
  // Add planner behavior signal.
  planning_lane_sequence.set_behavior_signal(planner_behavior_signal);
  // Add abnormal sequence signal.
  *planning_lane_sequence.mutable_abnormal_sequence_signal() =
      abnormal_sequence_signal;

  // Add other fields.
  planning_lane_sequence.set_timestamp(world_model.snapshot_timestamp());
  *planning_lane_sequence.mutable_pose() = world_model.pose();
  planning_lane_sequence.set_has_lane_change(
      IsLaneChangeManeuver(behavior_decision.maneuver_type));
  planning_lane_sequence.set_route_timestamp(
      world_model.global_route_solution()->GetRouteTimestamp());
  planning_lane_sequence.set_section_sequence_state(
      world_model.regional_map().section_sequence_state);
  planning_lane_sequence.set_dist_to_dest_m(
      world_model.regional_map().regional_path.distance_to_destination_m);
  planning_lane_sequence.set_time_to_dest_s(
      world_model.regional_map().regional_path.time_to_destination_s);
  planning_lane_sequence.set_route_mission_id(
      world_model.global_route_solution()->route_solution().mission_id());
  const std::optional<double> accumulate_distance_from_ride_start =
      world_model.OrderStartPositionDistance().has_value()
          ? world_model.OrderStartPositionDistance()
          : std::nullopt;
  if (accumulate_distance_from_ride_start.has_value()) {
    planning_lane_sequence.set_accumulate_distance_from_ride_start(
        accumulate_distance_from_ride_start.value());
  }

  // Add regional path.
  const std::vector<const pnc_map::Lane*>& regional_path_lane_sequence =
      world_model.regional_map().regional_path.lanes;
  planning_lane_sequence.mutable_regional_path_lane_sequence()->Reserve(
      regional_path_lane_sequence.size());
  for (const pnc_map::Lane* lane : regional_path_lane_sequence) {
    planning_lane_sequence.add_regional_path_lane_sequence(lane->id());
  }

  // Add untrimmed section sequence.
  const std::vector<const pnc_map::Section*> untrimmed_section_sequence =
      world_model.regional_map().extended_sections.empty()
          ? world_model.regional_map().lane_sequence_searchable_sections
          : world_model.regional_map().extended_sections;
  planning_lane_sequence.mutable_untrimmed_section_sequence()->Reserve(
      untrimmed_section_sequence.size());
  for (const pnc_map::Section* section : untrimmed_section_sequence) {
    planning_lane_sequence.add_untrimmed_section_sequence(section->id());
  }

  // Add untrimmed road sequence.
  const std::vector<const pnc_map::Road*> untrimmed_road_sequence =
      pnc_map::GetRoadsFromSections(untrimmed_section_sequence);
  planning_lane_sequence.mutable_untrimmed_road_sequence()->Reserve(
      untrimmed_road_sequence.size());
  for (const pnc_map::Road* road : untrimmed_road_sequence) {
    planning_lane_sequence.add_untrimmed_road_sequence(road->id());
  }

  // Add current road IDs.
  auto& mutable_current_road_ids =
      *planning_lane_sequence.mutable_current_road_ids();
  mutable_current_road_ids.Reserve(behavior_decision.current_lanes.size());
  for (const pnc_map::Lane* current_lane : behavior_decision.current_lanes) {
    if (current_lane == nullptr) {
      continue;
    }
    const int64_t current_road_id = current_lane->section()->road()->id();
    if (mutable_current_road_ids.empty() ||
        current_road_id != *(mutable_current_road_ids.end() - 1)) {
      mutable_current_road_ids.Add(current_road_id);
    }
  }

  return planning_lane_sequence;
}

std::optional<pb::PlanningLaneSequence> GenerateDowngradePlanningLaneSequence(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const pb::AbnormalSequenceSignal& abnormal_sequence_signal,
    const pb::ExtraPlannerSignals::PlannerBehaviorSignal
        planner_behavior_signal) {
  const lane_selection::ChangedLaneStructureReasonerSpeedData&
      changed_lane_structure_reasoner =
          world_model.changed_lane_structure_reasoner_speed_data();
  if (changed_lane_structure_reasoner.temp_lanes().empty()) {
    return std::nullopt;
  }

  const pnc_map::JointPncMapService& joint_pnc_map_service =
      *DCHECK_NOTNULL(world_model.joint_pnc_map_service());
  pb::PlanningLaneSequence planning_lane_sequence;
  planning_lane_sequence.mutable_candidates()->set_selected_lane_seq_idx(
      behavior_decision.lane_sequence_candidates.selected_lane_seq_idx());
  for (const auto& candidate :
       behavior_decision.lane_sequence_candidates.lane_sequence_candidates()) {
    pb::LaneSequenceCandidate& downgrade_candidate =
        *planning_lane_sequence.mutable_candidates()
             ->add_lane_sequence_candidates();
    downgrade_candidate = candidate;
    // The lane change segments may contain temporary lane when the lane
    // sequence does not.
    lane_selection::ConvertLaneSequenceCandidateToDowngradeProto(
        joint_pnc_map_service, changed_lane_structure_reasoner, candidate,
        downgrade_candidate);
  }
  // Add lane sequence.
  const std::vector<const pnc_map::Lane*>& selected_lane_sequence =
      behavior_decision.lane_sequence.selected_lane_sequence;
  planning_lane_sequence.mutable_lane_sequence()->Reserve(
      selected_lane_sequence.size());
  if (lane_selection::IsLaneSequenceContainsTempLane(selected_lane_sequence)) {
    changed_lane_structure_reasoner.ConvertLaneSequenceToDowngradeProto(
        joint_pnc_map_service, selected_lane_sequence,
        /*keep_sequential=*/true,
        planning_lane_sequence.mutable_lane_sequence());
  } else {
    for (const pnc_map::Lane* lane : selected_lane_sequence) {
      planning_lane_sequence.add_lane_sequence(lane->id());
    }
  }

  // Add untrimmed lane sequence.
  planning_lane_sequence.mutable_untrimmed_lane_sequence()->Reserve(
      behavior_decision.lane_sequence.untrimmed_selected_lane_sequence.size());
  if (lane_selection::IsLaneSequenceContainsTempLane(
          behavior_decision.lane_sequence.untrimmed_selected_lane_sequence)) {
    changed_lane_structure_reasoner.ConvertLaneSequenceToDowngradeProto(
        joint_pnc_map_service,
        behavior_decision.lane_sequence.untrimmed_selected_lane_sequence,
        /*keep_sequential=*/true,
        planning_lane_sequence.mutable_untrimmed_lane_sequence());
  } else {
    for (const pnc_map::Lane* lane :
         behavior_decision.lane_sequence.untrimmed_selected_lane_sequence) {
      planning_lane_sequence.add_untrimmed_lane_sequence(lane->id());
    }
  }

  // Add current lane IDs.
  planning_lane_sequence.mutable_current_lane_ids()->Reserve(
      behavior_decision.current_lanes.size());
  if (lane_selection::IsLaneSequenceContainsTempLane(
          behavior_decision.current_lanes)) {
    changed_lane_structure_reasoner.ConvertLaneSequenceToDowngradeProto(
        joint_pnc_map_service, behavior_decision.current_lanes,
        /*keep_sequential=*/false,
        planning_lane_sequence.mutable_current_lane_ids());
  } else {
    for (const pnc_map::Lane* lane : behavior_decision.current_lanes) {
      planning_lane_sequence.add_current_lane_ids(lane->id());
    }
  }

  // TODO(songjingru): refine the logic and avoid code copy which is not related
  // to temporary lanes.
  if (behavior_decision.pull_over_status_info.has_value()) {
    *planning_lane_sequence.mutable_immediate_pullover_destination() =
        behavior_decision.pull_over_status_info->immediate_pullover_destination;
  }
  // Add planner behavior signal.
  planning_lane_sequence.set_behavior_signal(planner_behavior_signal);
  // Add abnormal sequence signal.
  *planning_lane_sequence.mutable_abnormal_sequence_signal() =
      abnormal_sequence_signal;

  // Add other fields.
  planning_lane_sequence.set_timestamp(world_model.snapshot_timestamp());
  *planning_lane_sequence.mutable_pose() = world_model.pose();
  planning_lane_sequence.set_has_lane_change(
      IsLaneChangeManeuver(behavior_decision.maneuver_type));
  planning_lane_sequence.set_route_timestamp(
      world_model.global_route_solution()->GetRouteTimestamp());
  planning_lane_sequence.set_section_sequence_state(
      world_model.regional_map().section_sequence_state);
  planning_lane_sequence.set_dist_to_dest_m(
      world_model.regional_map().regional_path.distance_to_destination_m);
  planning_lane_sequence.set_time_to_dest_s(
      world_model.regional_map().regional_path.time_to_destination_s);
  planning_lane_sequence.set_route_mission_id(
      world_model.global_route_solution()->route_solution().mission_id());
  const std::optional<double> accumulate_distance_from_ride_start =
      world_model.OrderStartPositionDistance().has_value()
          ? world_model.OrderStartPositionDistance()
          : std::nullopt;
  if (accumulate_distance_from_ride_start.has_value()) {
    planning_lane_sequence.set_accumulate_distance_from_ride_start(
        accumulate_distance_from_ride_start.value());
  }

  // Add regional path.
  const std::vector<const pnc_map::Lane*>& regional_path_lane_sequence =
      world_model.regional_map().regional_path.lanes;
  planning_lane_sequence.mutable_regional_path_lane_sequence()->Reserve(
      regional_path_lane_sequence.size());
  if (lane_selection::IsLaneSequenceContainsTempLane(
          regional_path_lane_sequence)) {
    changed_lane_structure_reasoner.ConvertLaneSequenceToDowngradeProto(
        joint_pnc_map_service, regional_path_lane_sequence,
        /*keep_sequential=*/true,
        planning_lane_sequence.mutable_regional_path_lane_sequence());
  } else {
    for (const pnc_map::Lane* lane : regional_path_lane_sequence) {
      planning_lane_sequence.add_regional_path_lane_sequence(lane->id());
    }
  }

  // Add untrimmed section sequence.
  const std::vector<const pnc_map::Section*> untrimmed_section_sequence =
      world_model.regional_map().extended_sections.empty()
          ? world_model.regional_map().lane_sequence_searchable_sections
          : world_model.regional_map().extended_sections;
  planning_lane_sequence.mutable_untrimmed_section_sequence()->Reserve(
      untrimmed_section_sequence.size());
  for (const pnc_map::Section* section : untrimmed_section_sequence) {
    planning_lane_sequence.add_untrimmed_section_sequence(section->id());
  }

  // Add untrimmed road sequence.
  const std::vector<const pnc_map::Road*> untrimmed_road_sequence =
      pnc_map::GetRoadsFromSections(untrimmed_section_sequence);
  planning_lane_sequence.mutable_untrimmed_road_sequence()->Reserve(
      untrimmed_road_sequence.size());
  for (const pnc_map::Road* road : untrimmed_road_sequence) {
    planning_lane_sequence.add_untrimmed_road_sequence(road->id());
  }

  // Add current road IDs.
  auto& mutable_current_road_ids =
      *planning_lane_sequence.mutable_current_road_ids();
  mutable_current_road_ids.Reserve(behavior_decision.current_lanes.size());
  for (const pnc_map::Lane* current_lane : behavior_decision.current_lanes) {
    if (current_lane == nullptr) {
      continue;
    }
    const int64_t current_road_id = current_lane->section()->road()->id();
    if (mutable_current_road_ids.empty() ||
        current_road_id != *(mutable_current_road_ids.end() - 1)) {
      mutable_current_road_ids.Add(current_road_id);
    }
  }

  return planning_lane_sequence;
}

void UpdateDebugData(
    const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
    const SpeedWorldModel& speed_world_model,
    const TrajectoryGenerationResult& selected_result, double cycle_time,
    double execution_time, pb::WaypointAvailability waypoint_availability,
    pb::PlanningDebug* planning_debug) {
  TRACE_EVENT_SCOPE(
      planner, PlanningNodelet_UpdateDebugData,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());

  // Add pose and timestamp.
  *planning_debug->mutable_pose() = speed_world_model.pose();
  planning_debug->set_timestamp(speed_world_model.pose().timestamp());
  planning_debug->set_prediction_timestamp(
      speed_world_model.prediction_timestamp());
  // Update plan initial state.
  *planning_debug->mutable_replan_locator_debug()->mutable_plan_init_state() =
      speed_world_model.robot_state().plan_init_state().ToProto();

  // Update selected trajectory problem.
  planning_debug->set_selected_trajectory_problem_type(
      selected_result.problem_type);

  // Add timing related information.
  auto* debug_map = planning_debug->mutable_key_val();
  (*debug_map)["cycle_time(ms)"] = std::to_string(cycle_time);
  (*debug_map)["execute_time"] = std::to_string(execution_time);

  // Update pull out confirm debug.
  UpdatePullOutConfirmDebug(
      *Seed::Access<token::PullOutJumpOutSeed>::GetMsg(SpeedCurrentFrame()),
      previous_decoupled_maneuver_seed.speed_seed()
          .speed_reasoner_seeds()
          .pullout_reasoner_seed(),
      speed_world_model.OrderStartPositionDistance(),
      speed_world_model.IsEgoPositionOnRouteReadyForPullOut(),
      speed_world_model.global_route_solution(),
      speed_world_model.route_model().is_ego_start_close_to_stop(),
      speed_world_model.current_lanes_speed_data()
          .current_lanes_for_pull_out_jump_out(),
      speed_world_model.current_lanes_speed_data().drivable_physical_lanes(),
      speed_world_model.should_generate_pull_out_jump_out_sequence(),
      planning_debug->mutable_world_model_debug()
          ->mutable_pull_out_confirm_debug());
  // Update waypoint_availability in world model debug.
  planning_debug->mutable_world_model_debug()
      ->mutable_waypoint_assist_debug()
      ->set_waypoint_availability(waypoint_availability);
}

void WritePlanningConfigToRosParam(const pb::PlannerConfig& planner_config) {
  std::string config_json;
  google::protobuf::util::JsonPrintOptions options;
  options.add_whitespace = true;
  options.always_print_primitive_fields = true;
  options.preserve_proto_field_names = true;

  proto_util::WriteJsonProtoString(planner_config, options, &config_json);
  ros::param::set(kPlanningConfigParamName, config_json);
}

bool ShouldEnableEngageWarning() {
  // There will be no "/trip_category" param after onboard login deprecated.
  // Instead we will check order service result (in ops_warning.cpp) to judge
  // whether we allowed to engage and trigger ops warning.
  if (!ros::param::has(kTripCategoryParamName)) {
    return !av_comm::InSimulation() ||
           (av_comm::InSimulation() &&
            FLAGS_planning_enable_engage_maneuver_sim);
  }
  // Ego vehicle is not engageable if the trip is in mapping mode or data
  // collection mode. /trip_category is set in login dialog in onboard monitor.
  std::string trip_category;
  ros::param::get(kTripCategoryParamName, trip_category);
  const bool is_ego_engageable = !(trip_category == kMappingParamName ||
                                   trip_category == kDataCollectionParamName);
  // Engage maneuver is enabled onboard if ego is engageable. In simulation,
  // engage maneuver is disabled default. Users can use
  // FLAGS_planning_enable_engage_maneuver_sim to enable engage maneuver in
  // simulation.
  return is_ego_engageable && (!av_comm::InSimulation() ||
                               (av_comm::InSimulation() &&
                                FLAGS_planning_enable_engage_maneuver_sim));
}

// Create slim route solution with necessary fields.
routing::pb::RouteSolution CreateSlimRouteSolution(
    const routing::pb::RouteSolution& route_solution) {
  routing::pb::RouteSolution new_route_solution;
  new_route_solution.set_success(route_solution.success());
  new_route_solution.set_name(route_solution.name());
  new_route_solution.set_query_timestamp(route_solution.query_timestamp());
  new_route_solution.set_route_timestamp(route_solution.route_timestamp());
  return new_route_solution;
}

// Return true if new route lanes and last route lanes are same.
bool AreSameGlobalRoutes(
    const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
        new_route_lanes,
    const google::protobuf::RepeatedPtrField<routing::pb::RouteLane>&
        last_route_lanes) {
  // If new/last route lanes is empty, we think they are not same.
  if (new_route_lanes.empty() || last_route_lanes.empty()) {
    return false;
  }

  // For the section sequence of route lanes, there may be duplicate sections.
  // We only check the section sequence after removing the duplicate sections.
  std::vector<int64_t> new_route_section_ids;
  new_route_section_ids.reserve(new_route_lanes.size());
  for (const routing::pb::RouteLane& new_route_lane : new_route_lanes) {
    if (new_route_section_ids.empty() ||
        new_route_section_ids.back() != new_route_lane.section_id()) {
      new_route_section_ids.push_back(new_route_lane.section_id());
    }
  }
  int new_route_sections_idx = new_route_section_ids.size() - 1;
  int64_t pre_last_route_section_id = -1;
  // Note: size_t indicates unsigned integer. We can not use it to represent
  // negative integer and check whether it is bigger than zero.
  for (int idx = last_route_lanes.size() - 1; idx >= 0; idx--) {
    // If new route lanes is totally the latter part of the last, they are same.
    if (new_route_sections_idx < 0) {
      return true;
    }
    const int64_t last_route_section_id = last_route_lanes.at(idx).section_id();
    if (pre_last_route_section_id == last_route_section_id) {
      continue;
    }
    // If there is different section, they are not same.
    if (new_route_section_ids.at(new_route_sections_idx) !=
        last_route_section_id) {
      return false;
    }
    pre_last_route_section_id = last_route_section_id;
    new_route_sections_idx--;
  }
  // If new route lanes is totally same with the last, they are same.
  return true;
}

// Return true if should accept new global route directly.
bool ShouldAcceptReplanRouteDirectly(
    const routing::pb::RouteStatus& route_status) {
  if (FLAGS_planning_enable_new_global_route_selection) {
    // Accept directly if planner need trigger/cancel immediate pull over.
    if (route_status.proposed_route_query().followed_path_type() ==
            routing::pb::QueryFollowedPathType::
                NONE_FOLLOWED_PATH_AND_TRIGGER_IMMEDIATE_PULL_OVER ||
        route_status.proposed_route_query().followed_path_type() ==
            routing::pb::QueryFollowedPathType::
                NONE_FOLLOWED_PATH_AND_CANCEL_IMMEDIATE_PULL_OVER) {
      return true;
    }

    return IsDesiredProposedRoute(route_status);
  }

  switch (route_status.proposed_route_query().followed_path_type()) {
    case routing::pb::QueryFollowedPathType::NONE_FOLLOWED_PATH:
    case routing::pb::QueryFollowedPathType::LAST_REGIONAL_PATH:
    case routing::pb::QueryFollowedPathType::
        NONE_FOLLOWED_PATH_AND_TRIGGER_IMMEDIATE_PULL_OVER:
    case routing::pb::QueryFollowedPathType::
        NONE_FOLLOWED_PATH_AND_CANCEL_IMMEDIATE_PULL_OVER:
      // Accept directly if:
      // 1. Need a new route;
      // 2. Should follow last regional path.
      // 3. Trigger immediate pull over.
      // 4. Cancel immediate pull over.
      return true;
    case routing::pb::QueryFollowedPathType::NONE_REPLAN:
      LOG(ERROR) << "Abnormal replan query.";
      return false;
    case routing::pb::QueryFollowedPathType::LAST_GLOBAL_PATH:
      // Accept directly if new global route is totally same with the last
      // one.
      if (AreSameGlobalRoutes(
              route_status.proposed_route_solution().route_lanes(),
              route_status.current_route_solution().route_lanes())) {
        return true;
      }
      // Post rt event for the replanned global route which is expected same
      // as last one but different.
      rt_event::PostRtEvent<rt_event::planner::GlobalChangedRouteAfterReplan>(
          strings::StringPrintf(
              "Global proposed route has changed after replan, route query "
              "time is %ld.",
              route_status.proposed_route_query().query_timestamp()));
      return false;
    default:
      break;
  }
  return false;
}

// Return true if should reject new global route lanes.
bool ShouldRejectNewRouteLanesForReplan(
    const routing::pb::RouteStatus& route_status,
    const planner::RegionalPath& regional_path) {
  if (FLAGS_planning_enable_new_global_route_selection) {
    return !IsDesiredProposedRoute(route_status);
  }

  if (regional_path.reach_virtual_target && regional_path.is_ego_connectable) {
    // Reject new global route lanes when regional path is not diverged with
    // current global route lanes.
    if (!regional_path.diverge_with_global_route) {
      return true;
    }
    // Accept new global route lanes when regional path is diverged with
    // current global route lanes but not diverged with new global route lanes.
    if (!regional_path.diverge_with_new_global_route) {
      return false;
    }
    // Reject new global route lanes when regional path is diverged with both
    // current global route lanes and new global route lanes.
    return true;
  }
  // When regional path is invalid, maybe it means ego can not go along current
  // global route lanes. So we do not reject new route lanes.
  return false;
}

// Updates debug information for routing constraint.
void UpdateRoutingConstraintInfoDebug(
    const routing::pb::ConstraintInfo& constraint_info,
    bool is_from_routing_topic,
    pb::RoutingConstraintInfoDebug* routing_constraint_info_debug) {
  if (!routing_constraint_info_debug) {
    return;
  }
  routing_constraint_info_debug->Clear();
  routing_constraint_info_debug->set_constraint_name(
      constraint_info.constraint_name());
  routing_constraint_info_debug->set_major_version(
      constraint_info.major_version());
  routing_constraint_info_debug->set_minor_version(
      constraint_info.minor_version());
  routing_constraint_info_debug->set_is_from_cloud(
      constraint_info.is_from_cloud());
  routing_constraint_info_debug->set_is_default(constraint_info.is_default());
  routing_constraint_info_debug->set_is_from_routing_topic(
      is_from_routing_topic);
  routing_constraint_info_debug->mutable_constraint_definitions()->CopyFrom(
      constraint_info.constraint_definitions());
}

void PostRTEventForReplanRouteQueryType(
    const routing::pb::RouteQuery& replan_route_query) {
  switch (replan_route_query.followed_path_type()) {
    case routing::pb::QueryFollowedPathType::LAST_GLOBAL_PATH:
      rt_event::PostRtEvent<rt_event::planner::ReplanFollowedLastGlobalPath>(
          strings::StringPrintf("Planner node triggers a replan query followed "
                                "last global path, route query time is %ld.",
                                replan_route_query.query_timestamp()));
      return;
    case routing::pb::QueryFollowedPathType::LAST_REGIONAL_PATH:
      rt_event::PostRtEvent<rt_event::planner::ReplanFollowedLastRegionalPath>(
          strings::StringPrintf("Planner node triggers a replan query followed "
                                "last regional path, route query time is %ld.",
                                replan_route_query.query_timestamp()));
      return;
    case routing::pb::QueryFollowedPathType::NONE_FOLLOWED_PATH:
      rt_event::PostRtEvent<rt_event::planner::ReplanFollowedNoPath>(
          strings::StringPrintf("Planner node triggers a replan query followed "
                                "no path, route query time is %ld.",
                                replan_route_query.query_timestamp()));
      return;
    default:
      return;
  }
}

std::optional<voy::IssueTagChangeInfo> GeneratePlannerIssueTagChangeInfo(
    TriggerStateChangeType trigger_state_change_type,
    int64_t current_timestamp) {
  if (trigger_state_change_type == TriggerStateChangeType::kNoChange) {
    return std::nullopt;
  }

  // Add/Remove changed issue tags when immediate pull over trigger or cancel.
  voy::IssueTagChangeInfo issue_tag_change_info;
  issue_tag_change_info.set_timestamp(current_timestamp);
  voy::ChangedIssueTag changed_issue_tag_info;
  changed_issue_tag_info.set_tag(kImmediatePullOverIssueTag);
  if (trigger_state_change_type ==
      TriggerStateChangeType::kReceiveTriggerSignal) {
    changed_issue_tag_info.set_type(voy::IssueTagChangeType::ADD_TAG);
    *issue_tag_change_info.add_changed_issue_tags() = changed_issue_tag_info;
  } else if (trigger_state_change_type ==
             TriggerStateChangeType::kReceiveCancelSignal) {
    changed_issue_tag_info.set_type(voy::IssueTagChangeType::REMOVE_TAG);
    *issue_tag_change_info.add_changed_issue_tags() = changed_issue_tag_info;
  }
  return std::make_optional(issue_tag_change_info);
}

// The sequence number field name of a output message.
static constexpr char kSequenceNumberKey[] = "sequence_number";

planner::pb::MessageMetadata ComposeMessageMetadata(
    int64_t message_metadata_timestamp,
    const InputMsgTsMap& input_publish_timestamps_in_ns, bool input_topic_only,
    bool drop_current_snapshot_flag, int64_t message_sequence_number) {
  pb::MessageMetadata proto;

  // Note: when the set_timestamp parameter is set to 0,
  // the purpose is to mark that the topic in this group has hit
  // the drop_current_snapshot logic and has not been actually consumed.
  proto.set_timestamp(drop_current_snapshot_flag ? 0
                                                 : message_metadata_timestamp);

  if (!input_topic_only) {
    proto.set_output_unique_key(kSequenceNumberKey);
    proto.set_output_unique_value(message_sequence_number);

    // Add all published topics' names into outputs, excluding
    // message_metadata and intra-process topics.
    for (const auto& pub_topic : GetAllPubTopics()) {
      if (pub_topic == PlannerPubTopic::planning_message_metadata) {
        continue;
      }

      if (ShouldSkipAlignment(pub_topic)) {
        continue;
      }

      proto.add_outputs(GetPlannerTopicName(pub_topic));
    }
  }

  // Add sub topics' into message metadata with their publish timestamps, if
  // the topic's publish timestamp exists.
  for (const auto& [sub_topic, publish_timestamp_in_ns] :
       input_publish_timestamps_in_ns) {
    if (publish_timestamp_in_ns == std::nullopt) {
      continue;
    }

    if (ShouldSkipAlignment(sub_topic)) {
      continue;
    }

    pb::MessageMetadata::InputPair* input_pair = proto.add_inputs();
    input_pair->set_topic(GetPlannerTopicName(sub_topic));
    // Convert ns to ms.
    input_pair->set_publish_timestamp(publish_timestamp_in_ns.value() /
                                      1000000);
  }
  return proto;
}

std::optional<routing::pb::CommandPurpose>
GetCommandPurposeWhenHasReachedDestination(
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    pb::ImmediatePullOverTriggerSource immediate_pull_over_source,
    double dist_to_destination, bool has_reached_destination,
    bool is_immediate_pull_over_triggered_by_mrc) {
  if (!has_reached_destination) {
    return std::nullopt;
  }

  // If mrm immediate pull over is not triggered, get the corresponding command
  // purpose when has_reached_destination is true.
  if (!is_immediate_pull_over_triggered_by_mrc) {
    if (immediate_pull_over_source ==
        pb::ImmediatePullOverTriggerSource::kByDriverHMI) {
      return std::make_optional(
          routing::pb::CommandPurpose::
              kPlannerCompletesDHMIImmediatePullOverRoute);
    }
    if (immediate_pull_over_source ==
        pb::ImmediatePullOverTriggerSource::kByPassengerHMI) {
      return std::make_optional(
          routing::pb::CommandPurpose::
              kPlannerCompletesPHMIImmediatePullOverRoute);
    }
    return std::make_optional(
        routing::pb::CommandPurpose::kPullOverCompletesRoute);
  }

  // If mrm pullright is triggered, publish route completion when distance to
  // destination is less than threshold.
  DCHECK(mrc_request_ptr != nullptr);
  if (mrc_request_ptr->mrm_type() == mrc::pb::MRM_PULLRIGHT &&
      dist_to_destination <
          kDistToDestThresholdToPublishRouteCompletionInMeter) {
    return std::make_optional(routing::pb::CommandPurpose::
                                  kPlannerCompletesNearDestMRMPullRightRoute);
  }
  return std::nullopt;
}

bool IsLastOptimalRouteAcceptedByPlanner(
    const pb::WorldModelSeed& previous_world_model_seed,
    const pnc_map::JointPncMapService& joint_pnc_map_service) {
  const auto last_optimal_regional_path_seed =
      previous_world_model_seed.regional_map_generator_seed()
          .last_selected_regional_path_seed();
  const auto regional_path_section_id_sequence =
      lane_selection::GetSectionIdSequenceFromSeed(
          last_optimal_regional_path_seed);
  const auto last_selected_lane_sequence =
      joint_pnc_map_service.GetLaneSequence(
          previous_world_model_seed.last_selected_lane_sequence());

  return !lane_selection::IsRegionalPathDivergedFromLastSelectedLS(
      *joint_pnc_map_service.pnc_map_service()->hdmap(),
      regional_path_section_id_sequence, last_selected_lane_sequence);
}

bool IsEgoInJunction(const pb::WorldModelSeed& previous_world_model_seed,
                     const pnc_map::PncMapService* pnc_map_service) {
  const auto section_sequence =
      previous_world_model_seed.regional_map_generator_seed()
          .last_selected_regional_path_seed()
          .section_sequence();
  if (section_sequence.empty()) {
    return false;
  }
  const pnc_map::Section* ego_section =
      pnc_map_service->GetSectionById(section_sequence.at(0));
  return ego_section != nullptr && ego_section->road() != nullptr &&
         ego_section->road()->IsInJunction();
}

bool ShouldRejectProposedRoute(bool is_last_optimal_route_accepted_by_planner,
                               int64_t current_timestamp,
                               int64_t first_receive_timestamp) {
  if (!is_last_optimal_route_accepted_by_planner &&
      (current_timestamp - first_receive_timestamp >
       kTimeLimitToAcceptNewRouteInMs)) {
    return true;
  }
  return false;
}

}  // namespace planning_vnode
}  // namespace planner
