#include <gtest/gtest.h>

#include "common_math_library/matrix.h"

#include "common_math_library/linear_quadratic_regulator.h"

// road test data
TEST(SolveRiccatiEquation, SolveRiccatiEquationTest) {
  CML_MatrixCreate(A, 4, 4);
  CML_MatrixSetAll(A, 4, 4, 0.0F);
  CML_MatrixSetElement(A, 0, 0, 1.0F);
  CML_MatrixSetElement(A, 0, 1, 0.007977F);
  CML_MatrixSetElement(A, 0, 2, 0.004957F);
  CML_MatrixSetElement(A, 0, 3, 1.977e-5F);
  CML_MatrixSetElement(A, 1, 1, 0.5953F);
  CML_MatrixSetElement(A, 1, 2, 0.9915F);
  CML_MatrixSetElement(A, 1, 3, 0.003954F);
  CML_MatrixSetElement(A, 2, 2, 1.0F);
  CML_MatrixSetElement(A, 2, 3, 0.007977F);
  CML_MatrixSetElement(A, 3, 3, 0.5953F);

  CML_MatrixCreate(B, 4, 1);
  CML_MatrixSetAll(B, 4, 1, 0.0F);
  CML_MatrixSetElement(B, 1, 0, 0.6215F);
  CML_MatrixSetElement(B, 3, 0, 0.4165F);

  CML_MatrixCreate(Q, 4, 4);
  CML_MatrixSetZero(Q);
  CML_MatrixSetElement(Q, 0, 0, 0.025F);
  CML_MatrixSetElement(Q, 2, 2, 1.0F);

  CML_MatrixCreate(R, 1, 1);
  CML_MatrixSetElement(R, 0, 0, 2.0F);

  CML_MatrixCreate(K, 1, 4);
  CML_MatrixSetZero(K);
  float diff = 0.0F;
  uint16_t num_iteration = 0;
  SolveRiccatiEquation(A, B, Q, R, 1e-3F, 2000, K, &num_iteration, &diff);

  // matlab result, K = dlqr(A, B, Q, R)
  // reference, K = [0.1113, 0.0022, 0.9295, 0.0182]
  // K = [0.1111, 0.0022, 0.9288, 0.0182]
  CML_MatrixCreate(K_res, 1, 4);
  CML_MatrixSetElement(K_res, 0, 0, 0.1113F);
  CML_MatrixSetElement(K_res, 0, 1, 0.0022F);
  CML_MatrixSetElement(K_res, 0, 2, 0.9295F);
  CML_MatrixSetElement(K_res, 0, 3, 0.0182F);

  for (uint8_t row = 0; row < 1; ++row) {
    for (uint8_t col = 0; col < 2; ++col) {
      // ASSERT_NEAR(CML_MatrixGetElement(K, row, col),
      //             CML_MatrixGetElement(K_res, row, col), 0.001F);
    }
  }
}
