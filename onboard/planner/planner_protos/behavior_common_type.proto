syntax = "proto3";

package planner.pb;

// This file contains the common parameters and types shared by the new arch and
// the current system to unify the naming and reduce the system integration
// effort in the future. This file is designed as the basis of other proto
// files, please try not to import other user-defined files.

// The Object/Lane's priority against Ego/Ego-lane. It is determined by road
// structures and traffic lights.
message RoadPriorityType {
  // Next Available ID: 4
  enum Enum {
    UNKNOWN = 0;  // default
    HIGHER = 1;
    LOWER = 2;
    EQUAL = 3;
  }
}

// The Object/Lane's road precedence against Ego/Ego-lane.
message RoadPrecedence {
  // Next Available ID: 4
  enum Enum {
    UNKNOWN = 0;  // default
    HIGHER = 1;
    LOWER = 2;
    EQUAL = 3;
  }
}

// The source from where we get the precedence.
message PrecedenceSource {
  // Next Available ID: 21
  enum Enum {
    None = 0;     // default
    UNKNOWN = 1;  // unknown source
    // The precedence converted from legacy road priority.
    UseRoadPririty = 2;
    // The agent has no overlap with ego lane sequence.
    NoOverlapWithEgo = 3;
    // The scenario type is not known or considered for now.
    UnKnownScenarioCategory = 4;
    // Either agent or ego or both already entered the overlapped lane.
    AlreadyInLane = 5;
    KnownTrafficLight = 6;    // known traffic light color
    UnKnownTrafficLight = 7;  // unknown traffic light color
    MergingSection = 8;       // lane merging section excluding junction
    YieldSign = 9;            // yield sign
    StopSign = 10;            // stop sign
    // Either ego or agent has a lane change behavior.
    LaneChange = 11;
    // There is no any traffic signs or signals in junction.
    NoTrafficSign = 12;
    ExitZone = 13;  // exit zone
    // Zones allowing stop by, including bus bulb, bus stop, pickup dropoff.
    StopbyZone = 14;
    Crosswalk = 15;
    // Ego encroach agent's lane.
    EgoEncroachment = 16;
    // Ego pulling out or pulling over.
    PullOverPullOut = 17;
    // Ego in roundabout.
    Roundabout = 18;
    // Emergency vehicle has high precedence.
    EmergencyVehicle = 19;
    // Ego on temp lane.
    TempLane = 20;
  }
}

message PrecedenceSceneType {
  // Next Available ID: 9
  enum Enum {
    UnKnown = 0;  // default
    InJunction = 1;
    InMergeSection = 2;
    InExitZone = 3;
    InStopbyZone = 4;
    InRegularOrStandAloneVirtualLane = 5;
    InEgoEncroachment = 6;
    InPullOverPullOut = 7;
    InTempLane = 8;
  }
}

message EncroachmentSceneType {
  // Next Available ID: 5
  enum Enum {
    UnKnown = 0;  // default
    EgoNotEncroaching = 1;
    EgoEncroachingOnly = 2;
    EgoAgentBothEncroaching = 3;
    EgoSteppingOnMarkingNotCrossingLane =
        4;  // Ego is stepping on the lane marking and the enroachment side is
            // same as the direction its coming from.
  }
}

// The precedence inference source indicating the reason why additional
// inference is needed besides computed precedence based on the map labeling.
message PrecedenceInferenceSource {
  // Next Available ID: 2
  enum Enum {
    NoInference = 0;
    StraightMergeInJunction = 1;
  }
}

// The crosswalks' order in a range along the extended path.
//           |  ^  |
//  ---------|--|--|--
//  |        |  |  | | --> The Second Crosswalk
//  ---------|--|--|--
//           |  |  | ----> Lane Boundary
//           |  |--------> The Extended path
//  ---------|--|--|--
//  |        |  |  | | --> The First Crosswalk
//  ---------|--|--|--
//           |     |
message CrosswalkOrderType {
  // Next Available ID: 3
  enum Enum {
    UNKNOWN = 0;
    FIRST = 1;
    SECOND = 2;
  }
}

// Specifies the type of critical region along path that we need to pay
// special attention to.
message CriticalRegionType {
  // Next Available ID: 11
  enum Enum {
    Invalid = 0;
    EncroachLeftSide = 1;
    EncroachRightSide = 2;
    LeftMerge = 3;
    RightMerge = 4;
    LeftFork = 5;
    RightFork = 6;
    PassiveLeftMerge = 7;
    PassiveRightMerge = 8;
    SqueezeLeftNudge = 9;
    SqueezeRightNudge = 10;
  }
}

// Specify the stop reason for stop point constraint in traffic light reasoner.
message TrafficLightStopPointType {
  // Next Available ID: 5
  enum Enum {
    WatchLine = 0;
    Occlusion = 1;
    Broken = 2;
    StayStoppedForRedLight = 3;
    MapChanged = 4;
  }
}

// Relative state between ego and current lane.
message EgoCurrentLaneRelativeState {
  // Next Available ID: 7
  enum Enum {
    FullyOnLeftOutside = 0;
    CrossInFromLeftMarking = 1;
    CrossOutFromLeftMarking = 2;
    FullyOnTheLane = 3;
    CrossInFromRightMarking = 4;
    CrossOutFromRightMarking = 5;
    FullyOnRightOutside = 6;
  }
}

// The specific scene in which ego might honk.
message HonkScene {
  // Next Available ID: 12
  enum Enum {
    NA = 0;
    START_TO_MOVE_AGENT = 1;
    OCCLUSION_RISK = 2;
    CUT_IN_CONSTRAINT = 3;
    LATERALLY_MOVING_AGENT = 4;
    CONSTRAINT = 5 [ deprecated = true ];
    EMERGENCY = 6;
    REVERSE_DRIVING_AGENT = 7;
    REMOTE_ASSIST = 8;
    YIELD_CONSTRAINT = 9;
    VRU_CONSTRAINT = 10;
    JAYWALKER_CONSTRAINT = 11;
  }
}

// The specific scene in which ego might trigger hazard light.
message HazardLightScene {
  // Next Available ID: 6
  enum Enum {
    NA = 0;
    MRC = 1;
    PULL_OVER = 2;
    CONSTRUCTION_ZONE_CONSTRAINT = 3;
    REMOTE_ASSIST = 4;
    DECELERATION = 5;
  }
}

// Next Available ID: 3
message PointInfoForPolylineToPolylineGap {
  // Arc length of one point on the |target_polyline|.
  double arc_length = 1;
  // Signed dist, positive for the kLeft, negative for the kRight, and zero for
  // kOn.
  double signed_lateral_gap = 2;
}

// The reasons to indicate the urgency to proceed.
message UrgencyReason {
  // Next Available ID: 12
  enum Enum {
    NA = 0;
    MergeInDenseTraffic = 1;
    AvoidCutInInDenseTraffic = 2;
    BlockingTrafficInUPL = 3;
    UPLWaitingTooLong = 4;
    BlockingUTurn = 5;
    CrosswalkWithDenseVRU = 6;
    UnstuckFromKeepClearZone = 7;
    Waypoint = 8;
    StuckInDynamicDenseTraffic = 9;
    EndOfTrafficLight = 10;
    RightHookDenseTraffic = 11;
  }
}
