#include "planner/path/reasoning/agent_intention/reasoner/lead_and_merge_agent_path_reasoner/lead_and_merge_agent_path_reasoner.h"

#include <algorithm>
#include <optional>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>

#include "data_structure/longest_increasing_subsequence.h"
#include "geometry/algorithms/arithmetic.h"
#include "log_utils/log_macros.h"
#include "log_utils/map_macros.h"
#include "math/constants.h"
#include "math/curve2d_util.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_stamped_snapshot_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/frame_analyzer_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/profile/profile.h"
#include "planner/utility/physics/motion_model_1d.h"
#include "planner/utility/physics/motion_model_2d.h"
#include "planner/utility/physics/target_speed_profile.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

// The critical spatial buffer for cut-in agent to avoid over-nudge.
constexpr double kCriticalSpatialBufferForCutinAgentInMeter = 2.0;
// The critical temporal buffer for cut-in agent to avoid over-nudge.
constexpr double kCriticalTemporalBufferForCutinAgentInSecond = 0.2;

// The likelihood threshold, if the total cut in likelihood is larger than this,
// we use moderate strength, otherwise, weak.
constexpr double kCutInRepulsionStrengthLikelihoodThreshold = 0.3;

// The critical required lateral gap for slow cut-in vehicle during lane
// encroachment nudge in meter.
constexpr double kLenSlowCutInVehicleCriticalRlgInMeter = 0.4;

// The speed upper bound for vehicle faked traj.
constexpr double kSpeedUpperBoundForVehicleFakedTraj = 20.0;  // in m/s

// The odom upper bound for vehicle faked traj.
constexpr double kOdomUpperBoundForVehicleFakedTraj = 30.0;  // in m

// The max duration for vehicle faked traj.
constexpr double kMaxDurationForVehicleFakedTraj = 3.0;  // in s

// The dt for faked traj.
constexpr double kTrajectoryIntervalInSec = 0.1;  // in s

constexpr double kMaxLateralJukeForFakedCutInRepulsionInMpsss =
    1.0;  // in m/s^3

constexpr double kMaxLateralAccelForFakedCutInRepulsionInMpss =
    1.0;  // in m/s^2

constexpr double kMaxTtcToTrrigerPreventFrontalCollisionInSec = 3.0;  // in s

constexpr int64_t kMaxDurationToConsiderLaneChangeAgentTrajInMSec = 4000;
struct TrajPoseWithProjectionInfo {
  TrajPoseWithProjectionInfo(const pb::TrajectoryPose& traj_pose,
                             math::Range1d lateral_range,
                             math::Range1d arclength_range,
                             double center_arclength)
      : traj_pose(traj_pose),
        lateral_range(lateral_range),
        arclength_range(arclength_range),
        center_arclength(center_arclength) {}
  const pb::TrajectoryPose& traj_pose;
  math::Range1d lateral_range;
  math::Range1d arclength_range;
  double center_arclength;
};

// Checks if an agent is considered as crossing.
bool IsAgentCrossing(const AgentInLaneStates& agent_inlane_state) {
  const AgentTrajectoryInLaneStates& predicted_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id);
  // Use trajectory_intention_type directly.
  if (predicted_trajectory.trajectory_metadata.intention_type ==
      pb::AgentTrajectoryIntentionType::CROSS) {
    return true;
  }

  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      predicted_trajectory.predicted_states;
  math::pb::Side reference_side = math::pb::kOn;
  for (auto iter = predicted_states.begin(); iter != predicted_states.end();
       ++iter) {
    const math::pb::Side current_side =
        iter->inlane_state.inlane_param.center_line_side;
    if (current_side == math::pb::kOn || current_side == reference_side) {
      continue;
    }
    if (reference_side == math::pb::kOn) {
      reference_side = current_side;
    } else {
      // Agent is considered crossing if there's a side switch in its
      // predicted states.
      return true;
    }
  }
  return false;
}

// Checks if an agent is considered as crossing.
bool IsAgentCrossing(const ObjectOccupancyState& object_occupancy_state) {
  const ObjectTrajectoryStates& predicted_trajectory = FIND_OR_DIE_WITH_PRINT(
      object_occupancy_state.predicted_trajectory_occupancy_states(),
      object_occupancy_state.primary_trajectory_id());
  // Use trajectory_intention_type directly.
  if (predicted_trajectory.is_crossing_prediction()) {
    return true;
  }

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      predicted_trajectory.predicted_states();
  math::pb::Side reference_side = math::pb::kOn;
  for (auto iter = predicted_states.begin(); iter != predicted_states.end();
       ++iter) {
    const math::pb::Side current_side =
        iter->object_occupancy_param().center_line_side;
    if (current_side == math::pb::kOn || current_side == reference_side) {
      continue;
    }
    if (reference_side == math::pb::kOn) {
      reference_side = current_side;
    } else {
      // Agent is considered crossing if there's a side switch in its
      // predicted states.
      return true;
    }
  }
  return false;
}

// Computes repulsion meta data for a cut-in trajectory.
std::optional<RepulsionMetaData> ComputeRepulsionMetaDataForCutInTrajectory(
    const AgentTrajectoryInLaneStates& traj_states,
    RepulsionMetaData::RepulsionDirection repulsion_direction,
    const AgentSnapshotPose& current_pose, double total_cut_in_likelihood,
    bool is_risky, double repulsion_required_lat_gap,
    bool is_behind_front_bumper, bool is_lane_change_target_lane_agent,
    pb::AgentReasonerId reasoner_id, int64_t object_id, double ego_width) {
  DCHECK_GT(total_cut_in_likelihood, math::constants::kEpsilon);
  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool has_visited_on_central_line_state = false;
  const int64_t first_timestamp =
      traj_states.predicted_states.front().timestamp;
  // We only consider the cut-in part states, that is, states before hard
  // blocking or states before crossing to another side of central line.
  for (const auto& state : traj_states.predicted_states) {
    if ((is_risky ||
         !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion) &&
        state.inlane_state.blocking_info.blocking_state ==
            pb::AgentSnapshotBlockingState::HARD_BLOCKING) {
      break;
    }
    if (has_visited_on_central_line_state &&
        state.inlane_state.inlane_param.center_line_side != math::pb::kOn) {
      break;
    }
    if (state.inlane_state.inlane_param.center_line_side == math::pb::kOn) {
      has_visited_on_central_line_state = true;
    }
    const auto& pose = state.inlane_state.inlane_param.pose;
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    agent_center_path_points.emplace_back(pose.center_2d());
    profile_upper_bound.emplace_back(math::Ms2Sec(state.timestamp),
                                     state.odom_m,
                                     state.inlane_state.inlane_param.speed_mps,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
    if (is_lane_change_target_lane_agent &&
        state.timestamp - first_timestamp >
            kMaxDurationToConsiderLaneChangeAgentTrajInMSec) {
      break;
    }
  }
  // TODO(Harry): Consider a brake motion for cut in lower bound.
  profile_lower_bound = profile_upper_bound;
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    return std::nullopt;
  }

  // If the agent is behind the front-bumper of the Ego or the neighbor lane is
  // risky, we should not react it too much, as a result, set
  // ignore_if_nudge_failed true.
  bool ignore_if_nudge_failed =
      FLAGS_planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy &&
      (is_behind_front_bumper || is_risky);
  return RepulsionMetaData(
      repulsion_direction, current_pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), current_pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/ignore_if_nudge_failed,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      total_cut_in_likelihood > kCutInRepulsionStrengthLikelihoodThreshold &&
              !is_risky
          ? path::BoundaryStrength::kModerate
          : path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/traj_states.trajectory_metadata.likelihood /
          total_cut_in_likelihood,
      repulsion_required_lat_gap, reasoner_id,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      traj_states.trajectory_id, /*path_risk_mitigation_mode_in=*/false,
      std::nullopt, std::nullopt, std::nullopt,
      RepulsionMetaData::GuideProfileType::kLowerBoundIfIgnore,
      /*customized_guide_profile_in=*/std::nullopt,
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? RepulsionMetaData::ExtendBoundaryLevel::kNA
          : RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? std::nullopt
          : std::make_optional(ego_width),
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? std::nullopt
          : std::make_optional(ego_width / 2.0));
}

// Computes repulsion meta data for a cut-in trajectory.
std::optional<RepulsionMetaData> ComputeRepulsionMetaDataForCutInTrajectory(
    const ObjectTrajectoryStates& traj_states,
    const std::vector<pb::LateralBlockingInfo>& trajectory_blocking_info,
    RepulsionMetaData::RepulsionDirection repulsion_direction,
    const TrafficParticipantPose& current_pose, double total_cut_in_likelihood,
    bool is_risky, double repulsion_required_lat_gap,
    bool is_behind_front_bumper, bool is_lane_change_target_lane_agent,
    pb::AgentReasonerId reasoner_id, int64_t object_id, double ego_width) {
  DCHECK_GT(total_cut_in_likelihood, math::constants::kEpsilon);
  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool has_visited_on_central_line_state = false;
  const int64_t first_timestamp =
      traj_states.predicted_states().empty()
          ? 0
          : traj_states.predicted_states()[0].timestamp();
  // We only consider the cut-in part states, that is, states before hard
  // blocking or states before crossing to another side of central line.
  for (size_t state_id = 0; state_id < traj_states.predicted_states().size();
       ++state_id) {
    const auto& state = traj_states.predicted_states()[state_id];
    if ((is_risky ||
         !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion) &&
        trajectory_blocking_info[state_id].blocking_state() ==
            pb::LateralBlockingState::HARD_BLOCKING) {
      break;
    }
    if (has_visited_on_central_line_state &&
        state.object_occupancy_param().center_line_side != math::pb::kOn) {
      break;
    }
    if (state.object_occupancy_param().center_line_side == math::pb::kOn) {
      has_visited_on_central_line_state = true;
    }
    const auto& pose = state.pose();
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    agent_center_path_points.emplace_back(pose.center_2d());
    DCHECK(state.odom_m().has_value());
    profile_upper_bound.emplace_back(math::Ms2Sec(state.timestamp()),
                                     *state.odom_m(),
                                     state.object_occupancy_param().speed_mps,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
    if (is_lane_change_target_lane_agent &&
        state.timestamp() - first_timestamp >
            kMaxDurationToConsiderLaneChangeAgentTrajInMSec) {
      break;
    }
  }
  // TODO(Harry): Consider a brake motion for cut in lower bound.
  profile_lower_bound = profile_upper_bound;
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    return std::nullopt;
  }

  // If the agent is behind the front-bumper of the Ego or the neighbor lane is
  // risky, we should not react it too much, as a result, set
  // ignore_if_nudge_failed true.
  bool ignore_if_nudge_failed =
      FLAGS_planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy &&
      (is_behind_front_bumper || is_risky);
  return RepulsionMetaData(
      repulsion_direction, current_pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), current_pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/ignore_if_nudge_failed,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      total_cut_in_likelihood > kCutInRepulsionStrengthLikelihoodThreshold &&
              !is_risky
          ? path::BoundaryStrength::kModerate
          : path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/traj_states.Likelihood() / total_cut_in_likelihood,
      repulsion_required_lat_gap, reasoner_id,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      traj_states.trajectory_id(), /*path_risk_mitigation_mode_in=*/false,
      std::nullopt, std::nullopt, std::nullopt,
      RepulsionMetaData::GuideProfileType::kLowerBoundIfIgnore,
      /*customized_guide_profile_in=*/std::nullopt,
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? RepulsionMetaData::ExtendBoundaryLevel::kNA
          : RepulsionMetaData::ExtendBoundaryLevel::kVirtualSoft,
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? std::nullopt
          : std::make_optional(ego_width),
      (is_risky ||
       !FLAGS_planning_enable_boundary_extension_for_cut_in_repulsion)
          ? std::nullopt
          : std::make_optional(ego_width / 2.0));
}

// Returns true if ego should trigger lane encroach nudge.
bool ShouldTriggerLaneEncroachNudgeForCutIn(
    const std::vector<math::Range<int>>& agent_nudge_index_ranges,
    bool is_agent_crossing) {
  // Limit the large-scale nudge behavior.
  DCHECK(!agent_nudge_index_ranges.empty());
  const bool need_es_behavior =
      agent_nudge_index_ranges.front().start_pos < 10 &&
      agent_nudge_index_ranges.front().end_pos < 50;
  if (is_agent_crossing && !need_es_behavior) {
    return false;
  }

  return true;
}

// Returns true if critical required lateral gap based on reasoning is adjusted.
bool MaybeAdjustCriticalRlgDuringCutInLaneEncroachNudge(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    voy::perception::ObjectType object_type,
    RequiredLateralGap& required_lat_gap) {
  if (object_type == voy::perception::ObjectType::CYCLIST ||
      object_type == voy::perception::ObjectType::PED) {
    // Not adjust critical rlg for VRU as they have high uncertainty.
    return false;
  }
  if (reasoning_info.is_slow_cut_in_vehicle()) {
    // Since right now slow cut-in has more frequently speed and shape FN,
    // we increase the buffer to compensate for the FN.
    return math::UpdateMin(kLenSlowCutInVehicleCriticalRlgInMeter,
                           required_lat_gap.critical_required_lateral_gap);
  }
  if (reasoning_info.is_large_vehicle()) {
    // Large vehicle could have larger prediction error. Using more buffer to
    // compensate for that.
    rt_event::PostRtEvent<
        rt_event::planner::PlannerPathIncreaseLvBufferForLaneEncroachNudge>();
    return math::UpdateMin(0.2, required_lat_gap.critical_required_lateral_gap);
  }
  // For the case where the space in lane is not enough, we choose to trust
  // prediction completely.
  required_lat_gap.critical_required_lateral_gap = math::constants::kEpsilon;
  return true;
}

// If Ego has already been outside/crossing the lane, the pass state referes to
// the Ego's current direction, otherwise, it is based on the safe side.
pb::SnapshotIntention_PassState GetTargetPassState(
    const pb::SnapshotIntention_PassState& safe_pass_side,
    double relative_lateral_distance_to_line_center, bool is_ego_in_lane) {
  if (!is_ego_in_lane &&
      relative_lateral_distance_to_line_center > math::constants::kEpsilon) {
    return pb::SnapshotIntention::PASS_LEFT;
  }
  if (!is_ego_in_lane &&
      relative_lateral_distance_to_line_center < -math::constants::kEpsilon) {
    return pb::SnapshotIntention::PASS_RIGHT;
  }
  if (safe_pass_side == pb::SnapshotIntention::PASS_BOTH ||
      safe_pass_side == pb::SnapshotIntention::PASS_LEFT) {
    return pb::SnapshotIntention::PASS_LEFT;
  }
  if (safe_pass_side == pb::SnapshotIntention::PASS_RIGHT) {
    return pb::SnapshotIntention::PASS_RIGHT;
  }
  return pb::SnapshotIntention::IGNORE;
}

};  // namespace

LeadAndMergeAgentPathReasoner::LeadAndMergeAgentPathReasoner(
    const EgoContextInfo& ego_context_info)
    : AbstractReasoner(ego_context_info,
                       pb::AgentReasonerId::LEAD_AND_MERGE_AGENT) {
  // These buffers are more conservative than the default value.
  st_buffer_config_.set_spatial_bottom_buffer_m(1.0);
  st_buffer_config_.set_spatial_top_buffer_m(
      kCriticalSpatialBufferForCutinAgentInMeter);
  st_buffer_config_.set_temporal_left_buffer_s(0.1);
  st_buffer_config_.set_temporal_right_buffer_s(
      kCriticalTemporalBufferForCutinAgentInSecond);
}

bool LeadAndMergeAgentPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
  }
}

bool LeadAndMergeAgentPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(object_occupancy_state,
                                                blocking_state_data,
                                                trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
  }
}

bool LeadAndMergeAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForMixedBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() = st_buffer_config_;
  }
  return true;
}

bool LeadAndMergeAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForMixedBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() = st_buffer_config_;
  }
  return true;
}

bool LeadAndMergeAgentPathReasoner::PreReasonForHardBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  // If Ego would have collision with the leading agent within less than
  // kMaxTtcToTrrigerPreventFrontalCollisionInSec sec and there is a valid
  // target pass state, then we enlarge the bottom buffer of the leading agent.
  // To enlarge the bottom buffer, for each timestamp, we compute the extra
  // distance between the prediction trajectory and an estimated motion.
  // Finally, pick the max extra distance to add to the buffer.
  const double along_reference_ttc_in_sec =
      blocking_state_data.reasoning_info.along_reference_path_ttc_in_seconds();
  if (FLAGS_planning_enable_ttc_prevent_rear_front_collision &&
      0.0 < along_reference_ttc_in_sec &&
      along_reference_ttc_in_sec <
          kMaxTtcToTrrigerPreventFrontalCollisionInSec &&
      GetTargetPassState(
          traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
          ego_param_.relative_lateral_distance_to_line_center,
          traffic_rule_reasoning_info_.is_ego_in_lane()) !=
          pb::SnapshotIntention::IGNORE) {
    DCHECK(blocking_state_data.reasoning_info
               .has_recent_average_cross_track_accel() &&
           blocking_state_data.reasoning_info.has_recent_average_accel());
    const double abs_agent_along_track_accel = std::sqrt(
        math::Sqr(blocking_state_data.reasoning_info.recent_average_accel()) -
        math::Sqr(blocking_state_data.reasoning_info
                      .recent_average_cross_track_accel()));
    const double agent_along_track_accel =
        blocking_state_data.reasoning_info.recent_average_accel() > 0.0
            ? abs_agent_along_track_accel
            : -abs_agent_along_track_accel;
    const auto& primary_trajectory =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               agent_inlane_state.primary_trajectory_id);
    if (!primary_trajectory.predicted_states.empty()) {
      const auto& first_state = primary_trajectory.predicted_states.front();
      double max_extra_distance = 0.0;
      for (const auto& predicated_state : primary_trajectory.predicted_states) {
        int64_t timestamp = predicated_state.timestamp;
        const double estimated_odom = physics::GetOdomWithoutNegativeSpeed(
            first_state.inlane_state.inlane_param.start_arclength_m,
            std::max(
                first_state.inlane_state.inlane_param.along_track_speed_mps,
                0.0),
            agent_along_track_accel, /*j=*/0.0,
            math::Ms2Sec(timestamp - first_state.timestamp));
        const double predicted_odom =
            predicated_state.inlane_state.inlane_param.start_arclength_m;
        const double extra_distance = predicted_odom - estimated_odom;
        math::UpdateMax(extra_distance, max_extra_distance);
      }
      rt_event::PostRtEvent<
          rt_event::planner::PlannerPathEnlargeLeadAgentBottomBuffer>();
      trajectory_intention_data.blocking_sequences[0]
          .mutable_buffer_config()
          ->set_spatial_bottom_buffer_m(
              st_buffer_config_.spatial_bottom_buffer_m() + max_extra_distance);
    }
  }
  return true;
}

bool LeadAndMergeAgentPathReasoner::PreReasonForHardBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  // If Ego would have collision with the leading agent within less than
  // kMaxTtcToTrrigerPreventFrontalCollisionInSec sec and there is a valid
  // target pass state, then we enlarge the bottom buffer of the leading agent.
  // To enlarge the bottom buffer, for each timestamp, we compute the extra
  // distance between the prediction trajectory and an estimated motion.
  // Finally, pick the max extra distance to add to the buffer.
  const double along_reference_ttc_in_sec =
      blocking_state_data.reasoning_info.along_reference_path_ttc_in_seconds();
  if (FLAGS_planning_enable_ttc_prevent_rear_front_collision &&
      0.0 < along_reference_ttc_in_sec &&
      along_reference_ttc_in_sec <
          kMaxTtcToTrrigerPreventFrontalCollisionInSec &&
      GetTargetPassState(
          traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
          ego_param_.relative_lateral_distance_to_line_center,
          traffic_rule_reasoning_info_.is_ego_in_lane()) !=
          pb::SnapshotIntention::IGNORE) {
    DCHECK(blocking_state_data.reasoning_info
               .has_recent_average_cross_track_accel() &&
           blocking_state_data.reasoning_info.has_recent_average_accel());
    const double abs_agent_along_track_accel = std::sqrt(
        math::Sqr(blocking_state_data.reasoning_info.recent_average_accel()) -
        math::Sqr(blocking_state_data.reasoning_info
                      .recent_average_cross_track_accel()));
    const double agent_along_track_accel =
        blocking_state_data.reasoning_info.recent_average_accel() > 0.0
            ? abs_agent_along_track_accel
            : -abs_agent_along_track_accel;
    const auto& primary_trajectory = FIND_OR_DIE_WITH_PRINT(
        object_occupancy_state.predicted_trajectory_occupancy_states(),
        object_occupancy_state.primary_trajectory_id());
    if (!primary_trajectory.predicted_states().empty()) {
      const auto& first_state = primary_trajectory.predicted_states().front();
      double max_extra_distance = 0.0;
      for (const auto& predicated_state :
           primary_trajectory.predicted_states()) {
        int64_t timestamp = predicated_state.timestamp();
        const double estimated_odom = physics::GetOdomWithoutNegativeSpeed(
            first_state.object_occupancy_param().snapshot_start_arclength_m,
            std::max(first_state.object_occupancy_param().along_track_speed_mps,
                     0.0),
            agent_along_track_accel, /*j=*/0.0,
            math::Ms2Sec(timestamp - first_state.timestamp()));
        const double predicted_odom = predicated_state.object_occupancy_param()
                                          .snapshot_start_arclength_m;
        const double extra_distance = predicted_odom - estimated_odom;
        math::UpdateMax(extra_distance, max_extra_distance);
      }
      rt_event::PostRtEvent<
          rt_event::planner::PlannerPathEnlargeLeadAgentBottomBuffer>();
      trajectory_intention_data.blocking_sequences[0]
          .mutable_buffer_config()
          ->set_spatial_bottom_buffer_m(
              st_buffer_config_.spatial_bottom_buffer_m() + max_extra_distance);
    }
  }
  return true;
}

bool LeadAndMergeAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  intention_result.reasoner_id = reasoner_id_;
  AdjustSameDirectionReqLatGapInPostReason(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_,
      agent_inlane_state.tracked_state.inlane_param.speed_mps,
      /*ego_speed_near_agent=*/std::nullopt, intention_result.lateral_decision,
      agent_inlane_state.agent_metadata.agent_type,
      intention_result.required_lat_gap, intention_result.is_risky_agent,
      &(intention_result.debug_str));

  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          agent_inlane_state.tracked_state.inlane_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());

  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_start_arclength_m,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  // Set is_overtaken for cut-in vehicle.
  if (blocking_state_data.reasoning_info.is_cut_in() ||
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
    // Speed decider checks if there are enough space within the corridor for
    // ego to pass. If the corridor is not wide enough, lateral decision will
    // be set to ignore.
    const bool corridor_not_wide_enough =
        (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
    // Lane encroach nudge for cut_in agent.
    if (ShouldTriggerLaneEncroachNudgeBase(
            ego_param_, traffic_rule_reasoning_info_,
            blocking_state_data.reasoning_info,
            intention_result.lateral_decision, tracked_pass_decision,
            behavior_type_) &&
        ShouldTriggerLaneEncroachNudgeForCutIn(
            intention_result.nudge_index_ranges,
            IsAgentCrossing(agent_inlane_state)) &&
        corridor_not_wide_enough && FLAGS_planning_enable_lane_encroach_nudge) {
      rt_event::PostRtEvent<
          rt_event::planner::TriggerLaneEncroachNudgeForCutinAgents>();
      intention_result.lateral_decision = tracked_pass_decision;
      DCHECK(intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE);
      intention_result.is_overtaken = false;
      MaybeAdjustCriticalRlgDuringCutInLaneEncroachNudge(
          blocking_state_data.reasoning_info,
          agent_inlane_state.agent_metadata.agent_type,
          intention_result.required_lat_gap);
      intention_result.is_lane_encroach_intent_agent = true;
      absl::StrAppend(&intention_result.debug_str,
                      "emergency_swerve for cut-in; ");
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
  }
  // No need change the st planner's dicision for near-distance scenarios.
  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayCautiousAgent(blocking_state_data.reasoning_info)) {
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }
  // Get recommended intention result by dynamically adjusting the parameters
  // for cut-in agents.
  if (blocking_state_data.reasoning_info.is_cut_in() &&
      ShouldTriggerLaneEncroachNudgeForCutIn(
          intention_result.nudge_index_ranges,
          IsAgentCrossing(agent_inlane_state)) &&
      GetRecommendedAgentIntentionResult(
          agent_inlane_state, tracked_pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/true,
          /*is_ego_cut_behind=*/false, intention_result)) {
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(agent_inlane_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    return true;
  }
  // Get effective nudge index ranges with soft blocking sequence part.
  std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetEffectiveNudgeIndexRanges(
          intention_result.nudge_index_ranges,
          trajectory_intention_data.blocking_sequences,
          /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);
  if (effective_nudge_index_ranges.empty() &&
      !traffic_rule_reasoning_info_.is_ego_near_non_u_turn_merge()) {
    // If the collision is near, force to add nudge.
    if (FLAGS_planning_enable_ttc_prevent_rear_front_collision &&
        0.0 < blocking_state_data.reasoning_info
                  .along_reference_path_ttc_in_seconds() &&
        blocking_state_data.reasoning_info
                .along_reference_path_ttc_in_seconds() <
            kMaxTtcToTrrigerPreventFrontalCollisionInSec &&
        GetTargetPassState(
            traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
            ego_param_.relative_lateral_distance_to_line_center,
            traffic_rule_reasoning_info_.is_ego_in_lane()) !=
            pb::SnapshotIntention::IGNORE) {
      intention_result.lateral_decision = GetTargetPassState(
          traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
          ego_param_.relative_lateral_distance_to_line_center,
          traffic_rule_reasoning_info_.is_ego_in_lane());
      intention_result.is_overtaken = true;
      intention_result.is_lane_encroach_intent_agent = true;
      intention_result.is_emergency_swerve_agent = true;
      rt_event::PostRtEvent<
          rt_event::planner::PlannerPathPreventRearFrontalCollision>();
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
    // Ignore this agent which is faraway or hard_blocking tracked state (Bad
    // nudge decision).
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str, "bad nudge decision; ");
    return true;
  }
  if (traffic_rule_reasoning_info_.is_ego_near_non_u_turn_merge() &&
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE) {
    // In merge situation, even if there is not enough space, we still try to
    // provide nudge decision due to road geometry.
    rt_event::PostRtEvent<rt_event::planner::NudgeHardBlockingDuringMerge>();
    intention_result.lateral_decision = tracked_pass_decision;
    intention_result.is_overtaken = false;
  }
  // Add lateral decision for leading objects.
  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

bool LeadAndMergeAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  intention_result.reasoner_id = reasoner_id_;

  AdjustSameDirectionReqLatGapInPostReason(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .speed_mps,
      /*ego_speed_near_agent=*/std::nullopt, intention_result.lateral_decision,
      object_occupancy_state.object_type(), intention_result.required_lat_gap,
      intention_result.is_risky_agent, &(intention_result.debug_str));

  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());

  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_start_arclength_m,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  // Set is_overtaken for cut-in vehicle.
  if (blocking_state_data.reasoning_info.is_cut_in() ||
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
    // Speed decider checks if there are enough space within the corridor for
    // ego to pass. If the corridor is not wide enough, lateral decision will
    // be set to ignore.
    const bool corridor_not_wide_enough =
        (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
    // Lane encroach nudge for cut_in agent.
    if (ShouldTriggerLaneEncroachNudgeBase(
            ego_param_, traffic_rule_reasoning_info_,
            blocking_state_data.reasoning_info,
            intention_result.lateral_decision, tracked_pass_decision,
            behavior_type_) &&
        ShouldTriggerLaneEncroachNudgeForCutIn(
            intention_result.nudge_index_ranges,
            IsAgentCrossing(object_occupancy_state)) &&
        corridor_not_wide_enough && FLAGS_planning_enable_lane_encroach_nudge) {
      rt_event::PostRtEvent<
          rt_event::planner::TriggerLaneEncroachNudgeForCutinAgents>();
      intention_result.lateral_decision = tracked_pass_decision;
      DCHECK(intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE);
      intention_result.is_overtaken = false;
      MaybeAdjustCriticalRlgDuringCutInLaneEncroachNudge(
          blocking_state_data.reasoning_info,
          object_occupancy_state.planner_object().object_type(),
          intention_result.required_lat_gap);
      intention_result.is_lane_encroach_intent_agent = true;
      absl::StrAppend(&intention_result.debug_str,
                      "emergency_swerve for cut-in; ");
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
  }

  // No need change the st planner's dicision for near-distance scenarios.
  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayCautiousAgent(blocking_state_data.reasoning_info)) {
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  // Add effective nudge index ranges with soft blocking part.
  if (blocking_state_data.reasoning_info.is_cut_in() &&
      ShouldTriggerLaneEncroachNudgeForCutIn(
          intention_result.nudge_index_ranges,
          IsAgentCrossing(object_occupancy_state)) &&
      GetRecommendedAgentIntentionResult(
          object_occupancy_state, tracked_pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/true,
          /*is_ego_cut_behind=*/false, intention_result)) {
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(object_occupancy_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    return true;
  }

  // Get effective nudge index ranges with soft blocking sequence part.
  std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetEffectiveNudgeIndexRanges(
          intention_result.nudge_index_ranges,
          trajectory_intention_data.blocking_sequences,
          /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);
  if (effective_nudge_index_ranges.empty() &&
      !traffic_rule_reasoning_info_.is_ego_near_non_u_turn_merge()) {
    // If the collision is near, force to add nudge.
    if (FLAGS_planning_enable_ttc_prevent_rear_front_collision &&
        0.0 < blocking_state_data.reasoning_info
                  .along_reference_path_ttc_in_seconds() &&
        blocking_state_data.reasoning_info
                .along_reference_path_ttc_in_seconds() <
            kMaxTtcToTrrigerPreventFrontalCollisionInSec &&
        GetTargetPassState(
            traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
            ego_param_.relative_lateral_distance_to_line_center,
            traffic_rule_reasoning_info_.is_ego_in_lane()) !=
            pb::SnapshotIntention::IGNORE) {
      intention_result.lateral_decision = GetTargetPassState(
          traffic_rule_reasoning_info_.safe_pass_state_for_adjacent_lanes(),
          ego_param_.relative_lateral_distance_to_line_center,
          traffic_rule_reasoning_info_.is_ego_in_lane());
      intention_result.is_overtaken = true;
      intention_result.is_lane_encroach_intent_agent = true;
      intention_result.is_emergency_swerve_agent = true;
      rt_event::PostRtEvent<
          rt_event::planner::PlannerPathPreventRearFrontalCollision>();
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
    // Ignore this agent which is faraway or hard_blocking tracked state (Bad
    // nudge decision).
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str, "bad nudge decision; ");
    return true;
  }
  if (traffic_rule_reasoning_info_.is_ego_near_non_u_turn_merge() &&
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE) {
    // In merge situation, even if there is not enough space, we still try to
    // provide nudge decision due to road geometry.
    rt_event::PostRtEvent<rt_event::planner::NudgeHardBlockingDuringMerge>();
    intention_result.lateral_decision = tracked_pass_decision;
    intention_result.is_overtaken = false;
  }

  // Add lateral decision for leading objects.
  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);

  return true;
}

std::vector<RepulsionMetaData>
LeadAndMergeAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner,
      RunPathReasoning_LeadAndMergeAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(
          reusable_last_path,
          agent_inlane_state.tracked_state.inlane_param.pose.center_2d());
  if (!repulsion_direction) {
    debug_str += "no valid repulsion direction";
    return {};
  }
  bool is_risky =
      IsTargetDirectionRisky(*repulsion_direction, frame_analyzer_results);
  if (FLAGS_planning_enable_faked_cut_in_traj_repulsion) {
    std::optional<RepulsionMetaData> repulsion_for_faked_traj =
        MaybeAddRepulsionForPossibleCutIn(
            robot_state_snapshot, agent_inlane_state, blocking_state_data,
            repulsion_required_lat_gap,
            blocking_state_data.reasoning_info.is_agent_behind_front_bumper(),
            is_risky, *repulsion_direction, debug_str);
    if (repulsion_for_faked_traj.has_value()) {
      repulsion_meta_datas.push_back(std::move(*repulsion_for_faked_traj));
    }
  }
  // Compute total cut-in likelihood.
  // TODO(Harry): check whether we need to enhance those cut-in trajectories
  // with high likelihood by using function like softmax.
  double total_cut_in_likelihood = 0.0;
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!blocking_state_meta_data.reasoning_info.is_cut_in()) {
      continue;
    }
    int trajectory_id = id;
    const AgentTrajectoryInLaneStates& agent_trajectory_in_lane_state =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               trajectory_id);
    total_cut_in_likelihood +=
        agent_trajectory_in_lane_state.trajectory_metadata.likelihood;
  }
  if (total_cut_in_likelihood < math::constants::kEpsilon) {
    debug_str += "total_cut_in_likelihood near zero";
    return repulsion_meta_datas;
  }
  // Add repulsions.
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!blocking_state_meta_data.reasoning_info.is_cut_in()) {
      continue;
    }
    int trajectory_id = id;
    const AgentTrajectoryInLaneStates& agent_trajectory_in_lane_state =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               trajectory_id);
    std::optional<RepulsionMetaData> repulsion_meta_data =
        ComputeRepulsionMetaDataForCutInTrajectory(
            agent_trajectory_in_lane_state, *repulsion_direction,
            agent_inlane_state.tracked_state.inlane_param.pose,
            total_cut_in_likelihood, is_risky, repulsion_required_lat_gap,
            blocking_state_meta_data.reasoning_info
                .is_agent_behind_front_bumper(),
            blocking_state_meta_data.reasoning_info
                .is_lane_change_target_lane_agent(),
            reasoner_id_, agent_inlane_state.object_id, ego_param_.width_m);
    if (repulsion_meta_data.has_value()) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  return repulsion_meta_datas;
}

std::vector<RepulsionMetaData>
LeadAndMergeAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner,
      RunPathReasoning_LeadAndMergeAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(
          reusable_last_path,
          object_occupancy_state.current_snapshot_info().pose().center_2d());
  if (!repulsion_direction) {
    debug_str += "invalid repulsion direction";
    return {};
  }
  bool is_risky =
      IsTargetDirectionRisky(*repulsion_direction, frame_analyzer_results);
  if (FLAGS_planning_enable_faked_cut_in_traj_repulsion) {
    std::optional<RepulsionMetaData> repulsion_for_faked_traj =
        MaybeAddRepulsionForPossibleCutIn(
            robot_state_snapshot, object_occupancy_state, blocking_state_data,
            repulsion_required_lat_gap,
            blocking_state_data.reasoning_info.is_agent_behind_front_bumper(),
            is_risky, *repulsion_direction, debug_str);
    if (repulsion_for_faked_traj.has_value()) {
      repulsion_meta_datas.push_back(std::move(*repulsion_for_faked_traj));
    }
  }
  // Compute total cut-in likelihood.
  // TODO(Harry): check whether we need to enhance those cut-in trajectories
  // with high likelihood by using function like softmax.
  double total_cut_in_likelihood = 0.0;
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!blocking_state_meta_data.reasoning_info.is_cut_in()) {
      continue;
    }
    int trajectory_id = id;
    const ObjectTrajectoryStates& object_trajectory_states =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            trajectory_id);
    total_cut_in_likelihood += object_trajectory_states.Likelihood();
  }
  if (total_cut_in_likelihood < math::constants::kEpsilon) {
    debug_str += "total_cut_in_likelihood near zero";
    return repulsion_meta_datas;
  }
  // Add repulsions.
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!blocking_state_meta_data.reasoning_info.is_cut_in()) {
      continue;
    }
    int trajectory_id = id;
    const ObjectTrajectoryStates& object_trajectory_states =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            trajectory_id);
    std::optional<RepulsionMetaData> repulsion_meta_data =
        ComputeRepulsionMetaDataForCutInTrajectory(
            object_trajectory_states,
            blocking_state_meta_data.trajectory_blocking_infos,
            *repulsion_direction, object_occupancy_state.pose(),
            total_cut_in_likelihood, is_risky, repulsion_required_lat_gap,
            blocking_state_meta_data.reasoning_info
                .is_agent_behind_front_bumper(),
            blocking_state_meta_data.reasoning_info
                .is_lane_change_target_lane_agent(),
            reasoner_id_, object_occupancy_state.object_id(),
            ego_param_.width_m);
    if (repulsion_meta_data.has_value()) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  return repulsion_meta_datas;
}

bool LeadAndMergeAgentPathReasoner::ShouldInvokePreRepulsionReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
    bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
    std::string& debug_str) const {
  (void)imaginary_actual_type;
  (void)is_stationary;
  (void)behavior_type;
  (void)blocking_state_meta_data;
  if (!FLAGS_planning_enable_multi_traj_cut_in_repulsion) {
    debug_str += "flag closed";
    return false;
  }
  if (agent_type == voy::perception::PED) {
    debug_str += "not process ped";
    return false;
  }
  if (blocking_state_meta_data.reasoning_info.is_cyclist_overtaking()) {
    absl::StrAppend(&debug_str, "Don't handle cyclist overtaking");
    return false;
  }
  if (!blocking_state_meta_data.reasoning_info
           .is_agent_tracking_possible_cut_in()) {
    debug_str += "not likely to cut-in from tracking info";
    return false;
  }
  if (blocking_state_meta_data.reasoning_info.is_pull_over_rear_obstacle() &&
      behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type_ ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    debug_str += "is pull over rear agent and is pulling over";
    return false;
  }
  if (blocking_state_meta_data.reasoning_info
          .is_pull_over_gap_align_trailing_agent() &&
      behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type_ ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    debug_str += "is pull over gap align trailing agent and is pulling over";
    return false;
  }
  return true;
}

std::optional<RepulsionMetaData>
LeadAndMergeAgentPathReasoner::MaybeAddRepulsionForPossibleCutIn(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    double repulsion_required_lat_gap, bool is_behind_front_bumper,
    bool is_risky, RepulsionMetaData::RepulsionDirection repulsion_direction,
    std::string& debug_str) const {
  // The agent should not be a hard blocking agent, this should be checked in
  // ShouldInvoke.
  DCHECK_NE(blocking_state_data.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::HARD_BLOCKING);
  if (!truncated_reference_path_.has_value() ||
      truncated_reference_path_->size() < 2) {
    debug_str += "invalid truncated_reference_path_\n";
    return std::nullopt;
  }
  // Only important agents should be considered to reduce FP.
  constexpr double kMaximumPredictionAgentRank = 5;
  if (!(0 <= blocking_state_data.reasoning_info.prediction_agent_rank() &&
        blocking_state_data.reasoning_info.prediction_agent_rank() <=
            kMaximumPredictionAgentRank)) {
    debug_str += "prediction_agent_rank out of range\n";
    return std::nullopt;
  }
  // Oncoming and crossing check based on primary trajectory.
  const auto& primary_reasoning_info = blocking_state_data.reasoning_info;
  if (primary_reasoning_info.is_oncoming() ||
      primary_reasoning_info.is_crossing()) {
    debug_str += "primary traj is oncoming or crossing\n";
    return std::nullopt;
  }
  const auto& primary_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id);
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  // Make the faked_traj by a cyclist model, up to 3 sec.
  auto faked_traj = physics::CreateTrajectoryInConstLongAccelAndConstLatAccel(
      primary_reasoning_info.recent_average_accel(),
      primary_reasoning_info.recent_average_lat_accel(),
      primary_trajectory.predicted_states.empty()
          ? robot_state_snapshot.timestamp()
          : primary_trajectory.predicted_states.front().timestamp,
      tracked_param.speed_mps, tracked_param.pose.heading(),
      tracked_param.pose.center_x(), tracked_param.pose.center_y(),
      /*init_odom=*/0.0, /*speed_lower_bound=*/0.0,
      /*speed_upper_bound=*/kSpeedUpperBoundForVehicleFakedTraj,
      /*odom_upper_bound=*/kOdomUpperBoundForVehicleFakedTraj,
      /*max_duration=*/kMaxDurationForVehicleFakedTraj,
      /*dt=*/kTrajectoryIntervalInSec);
  const double lane_width = tracked_param.min_lane_width;

  std::vector<TrajPoseWithProjectionInfo> traj_pose_with_projection_infos;
  int csi_hint = math::kInvalidClosestSegmentIndex;
  // For each faked_pose in faked_traj, get its projection info on reference
  // line.
  for (const auto& faked_pose : faked_traj) {
    auto proximity_info = truncated_reference_path_->GetProximityWithHint(
        {faked_pose.x_pos(), faked_pose.y_pos()}, math::pb::kAllow, csi_hint);
    const double center_arclength = proximity_info.arc_length;
    const double center_lateral = proximity_info.signed_dist;
    auto moved_points = math::geometry::MovePointsToNewCenterAndHeading(
        tracked_param.pose.contour().points(), tracked_param.pose.center_2d(),
        {faked_pose.x_pos(), faked_pose.y_pos()}, tracked_param.pose.heading(),
        faked_pose.heading());
    math::Range1d arclength_range(center_arclength, center_arclength);
    math::Range1d lateral_range(center_lateral, center_lateral);
    for (const auto& point : moved_points) {
      auto proximity_info =
          truncated_reference_path_->GetProximityWithFixedHint(
              point, math::pb::kAllow, csi_hint);
      const double point_arclength = proximity_info.arc_length;
      const double point_lateral = proximity_info.signed_dist;
      math::UpdateMin(point_arclength, arclength_range.start_pos);
      math::UpdateMax(point_arclength, arclength_range.end_pos);
      math::UpdateMin(point_lateral, lateral_range.start_pos);
      math::UpdateMax(point_lateral, lateral_range.end_pos);
    }
    traj_pose_with_projection_infos.emplace_back(
        faked_pose, lateral_range, arclength_range, center_arclength);
  }

  // We only care about the part where agent moves toward the reference line.
  auto filtered_poses = data_structure::LongestIncreasingSubsequence(
      traj_pose_with_projection_infos.begin(),
      traj_pose_with_projection_infos.end(),
      [](const TrajPoseWithProjectionInfo& a,
         const TrajPoseWithProjectionInfo& b) {
        return a.center_arclength < b.center_arclength;
      });

  const double half_lane_width = lane_width * 0.5;

  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool has_visited_on_central_line_state = false;
  bool has_overlap_with_ego_lane = false;
  for (const auto& pose : filtered_poses) {
    has_overlap_with_ego_lane =
        has_overlap_with_ego_lane ||
        math::AreRangesOverlapping<double>(
            {pose.get().lateral_range,
             {-half_lane_width - math::constants::kEpsilon,
              half_lane_width + math::constants::kEpsilon}});
    // If no extra space, break (similar to HardBlocking). Here we only allow
    // Ego to cross the lane marking to at most half ego width.
    if (repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft &&
        pose.get().lateral_range.end_pos >
            half_lane_width - ego_param_.width_m * 0.5) {
      break;
    }
    if (repulsion_direction == RepulsionMetaData::RepulsionDirection::kRight &&
        pose.get().lateral_range.start_pos <
            -half_lane_width + ego_param_.width_m * 0.5) {
      break;
    }
    bool is_on_centeral_line = pose.get().lateral_range.start_pos <= 0 &&
                               0 <= pose.get().lateral_range.end_pos;
    if (has_visited_on_central_line_state && !is_on_centeral_line) {
      break;
    }
    if (is_on_centeral_line) {
      has_visited_on_central_line_state = true;
    }
    math::geometry::Point2d center_point(pose.get().traj_pose.x_pos(),
                                         pose.get().traj_pose.y_pos());
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            center_point, agent_center_path_points.back()))) {
      continue;
    }
    profile_upper_bound.emplace_back(
        math::Ms2Sec(pose.get().traj_pose.timestamp()),
        pose.get().traj_pose.odom(), pose.get().traj_pose.speed(),
        /*a_in=*/0.0, /*j_in=*/0.0);
    agent_center_path_points.emplace_back(pose.get().traj_pose.x_pos(),
                                          pose.get().traj_pose.y_pos());
  }
  // If the agent doesn't visited Ego lane, it should not be regarded as a
  // cut-in agent.
  if (!has_overlap_with_ego_lane) {
    debug_str += "no overlap with ego lane\n";
    return std::nullopt;
  }
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  profile_lower_bound = profile_upper_bound;
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "no valid center path points\n";
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    debug_str += "no valid center path points\n";
    return std::nullopt;
  }
  constexpr int kFakedTrajId = -1;
  // If the agent is behind the front-bumper of the Ego or the neighbor lane is
  // risky, we should not react it too much, as a result, set
  // ignore_if_nudge_failed true.
  bool ignore_if_nudge_failed =
      FLAGS_planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy &&
      (is_behind_front_bumper || is_risky);
  rt_event::PostRtEvent<rt_event::planner::AddFakeTrajCutInRepulsion>();
  return RepulsionMetaData(
      repulsion_direction, tracked_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), tracked_param.pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/ignore_if_nudge_failed,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/kFakedTrajId, /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/kMaxLateralAccelForFakedCutInRepulsionInMpss,
      /*max_abs_lateral_juke_in=*/kMaxLateralJukeForFakedCutInRepulsionInMpsss,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kLowerBoundIfIgnore);
}

std::optional<RepulsionMetaData>
LeadAndMergeAgentPathReasoner::MaybeAddRepulsionForPossibleCutIn(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    double repulsion_required_lat_gap, bool is_behind_front_bumper,
    bool is_risky, RepulsionMetaData::RepulsionDirection repulsion_direction,
    std::string& debug_str) const {
  // The agent should not be a hard blocking agent, this should be checked in
  // ShouldInvoke.
  DCHECK_NE(blocking_state_data.tracked_blocking_info.blocking_state(),
            pb::LateralBlockingState::HARD_BLOCKING);
  if (!truncated_reference_path_.has_value() ||
      truncated_reference_path_->size() < 2) {
    debug_str += "invalid truncated_reference_path_\n";
    return std::nullopt;
  }
  // Only important agents should be considered to reduce FP.
  constexpr double kMaximumPredictionAgentRank = 5;
  if (!(0 <= blocking_state_data.reasoning_info.prediction_agent_rank() &&
        blocking_state_data.reasoning_info.prediction_agent_rank() <=
            kMaximumPredictionAgentRank)) {
    debug_str += "prediction_agent_rank out of range\n";
    return std::nullopt;
  }
  // Oncoming and crossing check based on primary trajectory.
  const auto& primary_reasoning_info = blocking_state_data.reasoning_info;
  if (primary_reasoning_info.is_oncoming() ||
      primary_reasoning_info.is_crossing()) {
    debug_str += "primary traj is oncoming or crossing\n";
    return std::nullopt;
  }
  const auto& primary_trajectory = FIND_OR_DIE_WITH_PRINT(
      object_occupancy_state.predicted_trajectory_occupancy_states(),
      object_occupancy_state.primary_trajectory_id());
  // Make the faked_traj by a cyclist model, up to 3 sec.
  auto faked_traj = physics::CreateTrajectoryInConstLongAccelAndConstLatAccel(
      primary_reasoning_info.recent_average_accel(),
      primary_reasoning_info.recent_average_lat_accel(),
      primary_trajectory.predicted_states().empty()
          ? robot_state_snapshot.timestamp()
          : primary_trajectory.predicted_states().front().timestamp(),
      object_occupancy_state.pose().speed(),
      object_occupancy_state.pose().heading(),
      object_occupancy_state.pose().center_2d().x(),
      object_occupancy_state.pose().center_2d().y(),
      /*init_odom=*/0.0, /*speed_lower_bound=*/0.0,
      /*speed_upper_bound=*/kSpeedUpperBoundForVehicleFakedTraj,
      /*odom_upper_bound=*/kOdomUpperBoundForVehicleFakedTraj,
      /*max_duration=*/kMaxDurationForVehicleFakedTraj,
      /*dt=*/kTrajectoryIntervalInSec);
  const double lane_width = object_occupancy_state.current_snapshot_info()
                                .object_occupancy_param()
                                .min_corridor_width_m;

  std::vector<TrajPoseWithProjectionInfo> traj_pose_with_projection_infos;
  int csi_hint = math::kInvalidClosestSegmentIndex;
  // For each faked_pose in faked_traj, get its projection info on reference
  // line.
  for (const auto& faked_pose : faked_traj) {
    auto proximity_info = truncated_reference_path_->GetProximityWithHint(
        {faked_pose.x_pos(), faked_pose.y_pos()}, math::pb::kAllow, csi_hint);
    const double center_arclength = proximity_info.arc_length;
    const double center_lateral = proximity_info.signed_dist;
    auto moved_points = math::geometry::MovePointsToNewCenterAndHeading(
        object_occupancy_state.pose().contour().points(),
        object_occupancy_state.pose().center_2d(),
        {faked_pose.x_pos(), faked_pose.y_pos()},
        object_occupancy_state.pose().heading(), faked_pose.heading());
    math::Range1d arclength_range(center_arclength, center_arclength);
    math::Range1d lateral_range(center_lateral, center_lateral);
    for (const auto& point : moved_points) {
      auto proximity_info =
          truncated_reference_path_->GetProximityWithFixedHint(
              point, math::pb::kAllow, csi_hint);
      const double point_arclength = proximity_info.arc_length;
      const double point_lateral = proximity_info.signed_dist;
      math::UpdateMin(point_arclength, arclength_range.start_pos);
      math::UpdateMax(point_arclength, arclength_range.end_pos);
      math::UpdateMin(point_lateral, lateral_range.start_pos);
      math::UpdateMax(point_lateral, lateral_range.end_pos);
    }
    traj_pose_with_projection_infos.emplace_back(
        faked_pose, lateral_range, arclength_range, center_arclength);
  }

  // We only care about the part where agent moves toward the reference line.
  auto filtered_poses = data_structure::LongestIncreasingSubsequence(
      traj_pose_with_projection_infos.begin(),
      traj_pose_with_projection_infos.end(),
      [](const TrajPoseWithProjectionInfo& a,
         const TrajPoseWithProjectionInfo& b) {
        return a.center_arclength < b.center_arclength;
      });

  const double half_lane_width = lane_width * 0.5;

  speed::Profile profile_upper_bound;
  speed::Profile profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  bool has_visited_on_central_line_state = false;
  bool has_overlap_with_ego_lane = false;
  for (const auto& pose : filtered_poses) {
    has_overlap_with_ego_lane =
        has_overlap_with_ego_lane ||
        math::AreRangesOverlapping<double>(
            {pose.get().lateral_range,
             {-half_lane_width - math::constants::kEpsilon,
              half_lane_width + math::constants::kEpsilon}});
    // If no extra space, break (similar to HardBlocking). Here we only allow
    // Ego to cross the lane marking to at most half ego width.
    if (repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft &&
        pose.get().lateral_range.end_pos >
            half_lane_width - ego_param_.width_m * 0.5) {
      break;
    }
    if (repulsion_direction == RepulsionMetaData::RepulsionDirection::kRight &&
        pose.get().lateral_range.start_pos <
            -half_lane_width + ego_param_.width_m * 0.5) {
      break;
    }
    bool is_on_centeral_line = pose.get().lateral_range.start_pos <= 0 &&
                               0 <= pose.get().lateral_range.end_pos;
    if (has_visited_on_central_line_state && !is_on_centeral_line) {
      break;
    }
    if (is_on_centeral_line) {
      has_visited_on_central_line_state = true;
    }
    math::geometry::Point2d center_point(pose.get().traj_pose.x_pos(),
                                         pose.get().traj_pose.y_pos());
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            center_point, agent_center_path_points.back()))) {
      continue;
    }
    profile_upper_bound.emplace_back(
        math::Ms2Sec(pose.get().traj_pose.timestamp()),
        pose.get().traj_pose.odom(), pose.get().traj_pose.speed(),
        /*a_in=*/0.0, /*j_in=*/0.0);
    agent_center_path_points.emplace_back(pose.get().traj_pose.x_pos(),
                                          pose.get().traj_pose.y_pos());
  }
  // If the agent doesn't visited Ego lane, it should not be regarded as a
  // cut-in agent.
  if (!has_overlap_with_ego_lane) {
    debug_str += "no overlap with ego lane\n";
    return std::nullopt;
  }
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  profile_lower_bound = profile_upper_bound;
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += "no valid center path points\n";
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    debug_str += "no valid center path points\n";
    return std::nullopt;
  }
  constexpr int kFakedTrajId = -1;
  // If the agent is behind the front-bumper of the Ego or the neighbor lane is
  // risky, we should not react it too much, as a result, set
  // ignore_if_nudge_failed true.
  bool ignore_if_nudge_failed =
      FLAGS_planning_more_aggressive_cut_in_repulsion_in_ignore_homotopy &&
      (is_behind_front_bumper || is_risky);
  rt_event::PostRtEvent<rt_event::planner::AddFakeTrajCutInRepulsion>();
  return RepulsionMetaData(
      repulsion_direction, object_occupancy_state.pose().contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), object_occupancy_state.pose().heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/ignore_if_nudge_failed,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kWeak,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/kFakedTrajId, /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/kMaxLateralAccelForFakedCutInRepulsionInMpss,
      /*max_abs_lateral_juke_in=*/kMaxLateralJukeForFakedCutInRepulsionInMpsss,
      /*lateral_bound_in=*/std::nullopt,
      RepulsionMetaData::GuideProfileType::kLowerBoundIfIgnore);
}

}  // namespace path
}  // namespace planner
