#include "planner/speed/test_util/test_util.h"

#include <algorithm>
#include <vector>

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>
#include <absl/strings/str_join.h>

#include "av_comm/car_id.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/reference/reference_generator.h"

namespace planner {
namespace speed {

Profile CreateProfileFromStateWithJerk(const Limits& limits, State init_state,
                                       const int n_sample, const double jerk,
                                       const double dt) {
  Profile profile;
  profile.reserve(n_sample);
  for (int ix = 0; ix < n_sample; ix++) {
    // TODO(shiying): after core speed search is uploaded, check whether
    // this is still needed.
    /* if (ix == 0) {
      const double min_a = MinAccelToAvoidNegativeSpeed(dt, jerk, init_state.v);
      init_state.a = std::max(init_state.a, min_a);
    } */

    // The min and max of jerk allowance.
    LimitRange limit_j;
    MinMaxJerkToAvoidExceedingAccel(limits, init_state.a, 1.0 / dt, &limit_j);
    const double neg_v_min_j =
        MinJerkToAvoidNegativeSpeedAndDisplacement(dt, init_state);
    limit_j.min = std::max(neg_v_min_j, limit_j.min);
    DCHECK_LE(limit_j.min, limit_j.max);
    // TODO(speed): maybe also avoid going above max_v.

    init_state.j = math::Clamp(jerk, limit_j.min, limit_j.max);
    profile.push_back(init_state);

    init_state.MoveInPlace(dt);
  }
  return profile;
}

Profile CreateConstantSpeedProfileFromTime(const double t0, const double x0,
                                           const double v0, const int n_sample,
                                           const double dt) {
  DCHECK_GT(v0, 0.0);
  return CreateProfileFromStateWithJerk(Limits(), State({t0, x0, v0, 0.0, 0.0}),
                                        n_sample, /*jerk=*/0.0, dt);
}

Profile CreateStaticProfileFromTime(double t0, double x0, int n_sample,
                                    double dt) {
  return CreateProfileFromStateWithJerk(
      Limits(), State({t0, x0, 0.0, 0.0, 0.0}), n_sample, /*jerk=*/0.0, dt);
}

Profile CreateConstantAccelProfileFromTime(const Limits& limits,
                                           const double t0, const double x0,
                                           const double v0, const double a0,
                                           const int n_sample,
                                           const double dt) {
  DCHECK_GT(v0, 0.0);
  return CreateProfileFromStateWithJerk(limits, State({t0, x0, v0, a0, 0.0}),
                                        n_sample,
                                        /*jerk=*/0.0, dt);
}

void CreateOverlapRegionFromConstSpeed(
    const int obj_id, const int traj_id, const int region_id,
    const int first_padded_ix, const int last_padded_ix,
    const double start_time, const double dt, const double start_pos,
    const double move_along_speed, const double agent_length,
    const double lateral_gap, double leading_bumper_offset_from_ra,
    double trailing_bumper_offset_from_ra, pb::OverlapRegion* overlap_region) {
  DCHECK(overlap_region != nullptr);
  overlap_region->set_object_id(obj_id);
  overlap_region->set_trajectory_id(traj_id);
  overlap_region->set_region_id(region_id);
  overlap_region->set_first_padded_agent_state_ix(first_padded_ix);
  overlap_region->set_last_padded_agent_state_ix(last_padded_ix);
  overlap_region->set_first_strict_agent_state_ix(first_padded_ix);
  overlap_region->set_last_strict_agent_state_ix(last_padded_ix);
  const int num_slices = last_padded_ix - first_padded_ix + 1;
  // The relative time of the first overlap slice is the start time of
  // agent plus the time it travelled till the first padded index.
  double time = start_time + dt * first_padded_ix;
  const double delta_position = dt * move_along_speed;
  // The position of the first overlap slice is the start position of
  // agent plus the distance it travelled till the first padded index.
  double position = start_pos + delta_position * first_padded_ix;
  overlap_region->clear_overlap_slices();
  for (int ix = 0; ix < num_slices; ++ix) {
    pb::OverlapSlice* overlap_slice = overlap_region->add_overlap_slices();
    overlap_slice->set_relative_time_in_msec(math::Sec2Ms(time));
    overlap_slice->set_relative_time_in_sec(time);
    overlap_slice->set_timestamp_index(first_padded_ix + ix);
    overlap_slice->set_signed_longitudinal_speed(move_along_speed);
    overlap_slice->set_signed_lateral_gap(lateral_gap);
    overlap_slice->mutable_ego_moving_distance_padded()->set_start(
        position - leading_bumper_offset_from_ra);
    overlap_slice->mutable_ego_moving_distance_padded()->set_end(
        position + agent_length + trailing_bumper_offset_from_ra);
    if (math::NearZero(lateral_gap)) {
      overlap_slice->mutable_ego_moving_distance_strict()->set_start(
          position - leading_bumper_offset_from_ra);
      overlap_slice->mutable_ego_moving_distance_strict()->set_end(
          position + agent_length + trailing_bumper_offset_from_ra);
    }
    time += dt;
    position += delta_position;
  }
  overlap_region->set_contain_strict_overlap(true);
}

::planner::pb::Trajectory CreateTrajectory(double x_start, double y_start,
                                           double heading, double v,
                                           double time_in_sec, int num_step) {
  ::planner::pb::Trajectory trajectory;
  double odom = 0.0;
  while (num_step > 0) {
    ::planner::pb::TrajectoryPose* pose = trajectory.add_poses();
    pose->set_timestamp(math::Sec2Ms(time_in_sec));
    pose->set_x_pos(odom * cos(heading) + x_start);
    pose->set_y_pos(odom * sin(heading) + y_start);
    pose->set_heading(heading);
    pose->set_odom(odom);
    pose->set_speed(v);
    time_in_sec += constants::kTrajectoryIntervalInSec;
    odom += v * constants::kTrajectoryIntervalInSec;
    num_step--;
  }
  return trajectory;
}

ReferenceGenerator CreateReferenceGenerator(
    const double ego_speed, const double init_accel, const double min_speed,
    const double max_speed, const double dt, const double steps,
    const double pose_time,
    const pb::DiscomfortVaryingLimitsConfig& limits_config,
    const pb::ReferenceGeneratorConfig& ref_generator_config,
    double immutable_time) {
  return CreateReferenceGeneratorWithInitialState(
      ego_speed, init_accel, /*init_jerk=*/0.0, min_speed, max_speed, dt, steps,
      pose_time, limits_config, ref_generator_config,
      /*update_plan_init_state=*/false, immutable_time);
}

ReferenceGenerator CreateReferenceGeneratorWithInitialState(
    double init_speed, double init_accel, double init_jerk, double min_speed,
    double max_speed, double dt, double steps, double pose_time,
    const pb::DiscomfortVaryingLimitsConfig& limits_config,
    const pb::ReferenceGeneratorConfig& ref_generator_config,
    bool update_plan_init_state, double immutable_time) {
  // Create a long enough curve that spans at least max_speed*dt*n_steps.
  const double path_length = max_speed * dt * steps + 10.0;
  constexpr double kSamplingDistanceForPath = 0.5;
  const int size = static_cast<int>(path_length / kSamplingDistanceForPath);
  std::vector<math::geometry::Point2d> points(size);
  int index = 0;
  // Creat a straight path with heading, curvature, pinch and juke all zero.
  std::generate(points.begin(), points.end(), [&]() {
    return math::geometry::Point2d(kSamplingDistanceForPath * (index++), 0);
  });
  const adv_geom::Path2dWithJuke curve =
      CreateLinearPath2dWithJukeFromPoint(points);
  constexpr double kEgoRaArcLength = 0.5;
  const State init_state(
      {pose_time, kEgoRaArcLength, init_speed, init_accel, init_jerk});
  ReferenceProfile ref_profile(curve, kEgoRaArcLength, init_speed);
  const DiscomfortVaryingLimits all_limits(limits_config);
  ref_profile.UpdateMinAndMidDiscomfortV(
      min_speed, all_limits.ComfortLimits().accel_a.min);
  ref_profile.UpdateMaxDiscomfortV(max_speed,
                                   all_limits.EvasiveLimits().accel_a.min);
  ReferenceGenerator ref_generator(
      ref_generator_config, ref_profile, init_state, limits_config,
      /*lane_sequence=*/{}, planner::pb::IntentionResult::IGNORE_ALL,
      /*relax_ego_exceeding_road_speed_limit_factor=*/0.0,
      /*discomfort_to_decay_ego_exceeding_road_speed_limit=*/std::nullopt,
      ReferenceGeneratorSettings(), /*current_region=*/std::string(),
      update_plan_init_state, /*wheel_base=*/2.5, immutable_time);
  ref_generator.RegulateInitialStateAndUpdateImmutableTime(dt);
  return ref_generator;
}

adv_geom::Path2dWithJuke CreateLinearPath2dWithJukeFromPoint(
    std::vector<math::geometry::Point2d> points) {
  const int point_num = points.size();
  std::vector<double> headings(point_num, 0.0);
  std::vector<double> curvatures(point_num, 0.0);
  std::vector<double> pinches(point_num, 0.0);
  std::vector<double> jukes(point_num, 0.0);
  return adv_geom::Path2dWithJuke(std::move(points), std::move(headings),
                                  std::move(curvatures), std::move(pinches),
                                  std::move(jukes));
}

// Returns a simple linear Path2dWithJuke.
adv_geom::Path2dWithJuke CreateLinearPath2dWithJuke() {
  std::vector<math::geometry::Point2d> points = {
      {0.0, 0.0}, {1.0, 0.0}, {2.0, 0.0}, {3.0, 0.0}, {4.0, 0.0}, {5.0, 0.0}};
  return CreateLinearPath2dWithJukeFromPoint(std::move(points));
}

::prediction::pb::PredictedTrajectory CreateStraightPredictedTrajectory(
    double x_start, double y_start, double heading, double v,
    double time_in_sec, int num_step) {
  const ::planner::pb::Trajectory& trajectory =
      CreateTrajectory(x_start, y_start, heading, v, time_in_sec, num_step);

  ::prediction::pb::PredictedTrajectory prediction_traj;
  prediction_traj.set_id(1);
  for (int i = 0; i < trajectory.poses_size(); ++i) {
    *prediction_traj.add_traj_poses() = trajectory.poses(i);
  }
  return prediction_traj;
}

adv_geom::Path2dWithJuke CreateArcPath2dWithJuke(
    const double radius, const int size, const double angle,
    const bool positive_curvature) {
  const double sign = positive_curvature ? 1.0 : -1.0;
  const double angle_step = angle / (size - 1);
  std::vector<math::geometry::Point2d> points(size);
  std::vector<double> headings(size, 0.0);
  std::vector<double> curvatures(size, sign * 1 / radius);
  std::vector<double> pinches(size, 0.0);
  std::vector<double> jukes(size, 0.0);

  for (int ix = 0; ix < size; ++ix) {
    headings[ix] = angle_step * ix;
    points[ix] = math::geometry::Point2d(
        radius * sin(headings[ix]), sign * radius * (1 - cos(headings[ix])));
  }
  return adv_geom::Path2dWithJuke(std::move(points), std::move(headings),
                                  std::move(curvatures), std::move(pinches),
                                  std::move(jukes));
}

void GenerateTimeMap(const int c_ix, const double start_time,
                     const double start_x, const double speed,
                     const double end_time, const double sample_interval,
                     const Decision decision, const double delta_x,
                     TimeMap* time_map) {
  DCHECK(time_map != nullptr);
  if (decision == Decision::IGNORE) {
    return;
  }

  const int n_samples =
      math::CeilToIntWithTol((end_time - start_time) / sample_interval);

  for (int ix = 0; ix < n_samples; ix++) {
    const double position_x = start_x + ix * sample_interval * speed;
    if (decision == Decision::PASS || decision == Decision::NOT_DECIDED) {
      time_map->AddPassEntry(start_time + ix * sample_interval, c_ix,
                             position_x + delta_x);
    }
    if (decision == Decision::YIELD || decision == Decision::NOT_DECIDED) {
      time_map->AddYieldEntry(start_time + ix * sample_interval, c_ix,
                              position_x - delta_x);
    }
  }
}

std::string PredictedTrajectoryDebugString(
    const prediction::pb::PredictedTrajectory& predicted_trajectory,
    const std::string& suffix) {
  constexpr std::string_view kDoubleFormatter = "%.6lf";
  return absl::StrCat(
      "t", suffix, "=[",
      absl::StrJoin(predicted_trajectory.traj_poses(), ",",
                    [](std::string* out, const auto& pose) {
                      return out->append(absl::StrCat(pose.timestamp()));
                    }),
      "]\nx", suffix, "=[",
      absl::StrJoin(predicted_trajectory.traj_poses(), ",",
                    [&kDoubleFormatter](std::string* out, const auto& pose) {
                      return out->append(
                          absl::StrFormat(kDoubleFormatter, pose.x_pos()));
                    }),
      "]\ny", suffix, "=[",
      absl::StrJoin(predicted_trajectory.traj_poses(), ",",
                    [&kDoubleFormatter](std::string* out, const auto& pose) {
                      return out->append(
                          absl::StrFormat(kDoubleFormatter, pose.y_pos()));
                    }),
      "]\nheading", suffix, "=[",
      absl::StrJoin(predicted_trajectory.traj_poses(), ",",
                    [&kDoubleFormatter](std::string* out, const auto& pose) {
                      return out->append(
                          absl::StrFormat(kDoubleFormatter, pose.heading()));
                    }),
      "]\n");
}
}  // namespace speed
}  // namespace planner
