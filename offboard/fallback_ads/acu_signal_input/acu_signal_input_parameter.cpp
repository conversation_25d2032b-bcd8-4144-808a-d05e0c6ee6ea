#include "acu_signal_input/acu_signal_input_parameter.h"

namespace fallback_adas {
namespace acu_signal_input {
namespace {

// If the counter changes, it is considered that CAN communication is okay.
const bool kOnlyCheckCounterChange = true;
// If the counter updates ok and the duration exceeds the threshold, it is
// considered that can communication is ok.
const float kPoseUpdateOKTimeThreshold = 0.1F;

}  // namespace

bool GetOnlyCheckCounterChange() { return kOnlyCheckCounterChange; }
float GetPoseUpdateOKTimeThreshold() { return kPoseUpdateOKTimeThreshold; }

}  // namespace acu_signal_input
}  // namespace fallback_adas
