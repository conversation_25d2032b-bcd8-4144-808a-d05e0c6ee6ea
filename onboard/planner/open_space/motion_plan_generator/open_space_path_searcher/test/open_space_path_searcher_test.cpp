#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_searcher.h"

#include <google/protobuf/text_format.h>
#include <google/protobuf/util/json_util.h>
#include "gtest/gtest.h"

#include "base/base_dir.h"
#include "base/wall_clock_elapsed_timer.h"
#include "geometry/model/point_2d.h"
#include "math/math_util.h"
#include "matplotlibcpp/matplotlibcpp.h"
#include "onboard/planner/open_space/constraints/motion_plan_problem_manager.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_search_result.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "proto_util/proto_io.h"
#include "sim_protos/virtual_scene.pb.h"

namespace plt = matplotlibcpp;

namespace planner {

namespace open_space {

namespace {

void DrawPolyline(const math::geometry::Polyline2d& polyline,
                  const std::string& style) {
  std::vector<double> x, y;
  x.reserve(polyline.size());
  y.reserve(polyline.size());
  for (const auto& point : polyline) {
    x.push_back(point.x());
    y.push_back(point.y());
  }
  plt::plot(x, y, style);
}

[[maybe_unused]] void DrawDirection(const math::geometry::Point2d& start,
                                    const math::geometry::Point2d& end,
                                    const std::string& style) {
  plt::plot(std::vector<double>{start.x(), end.x()},
            std::vector<double>{start.y(), end.y()}, style);
}

math::geometry::OrientedBox2d GetBoundingBox(
    const PathState& state,
    const vehicle_model::CarModelWithAxleRectangularShape&
        car_model_with_shape) {
  const auto& measurement = car_model_with_shape.shape_measurement();
  double ego_length = measurement.length();
  double ego_width = measurement.width();
  double shift_distance =
      ego_length / 2.0 - measurement.rear_bumper_to_rear_axle();
  math::geometry::OrientedBox2d box(
      state.position.x() + shift_distance * std::cos(state.heading),
      state.position.y() + shift_distance * std::sin(state.heading), ego_length,
      ego_width, state.heading);

  return box;
}

void DrawInput(const MotionPlanProblem& motion_plan_problem,
               bool show_result = true) {
  // NOTE: To unblock online test, we skip the visualization.
  return;
  plt::figure();
  // Draw ego start and end states.
  const RobotStateSnapshot& current_state_snapshot =
      motion_plan_problem.current_state_snapshot();
  PathState start(current_state_snapshot.rear_axle_position(),
                  current_state_snapshot.heading(),
                  current_state_snapshot.steering(), true);
  math::geometry::OrientedBox2d start_box =
      GetBoundingBox(start, motion_plan_problem.car_model_with_shape());
  math::geometry::Polyline2d start_box_bound(start_box.CornerPoints());
  start_box_bound.push_back(start_box_bound[0]);

  // Only plot the first target pose.
  // TODO(Liwen, Zixuan): Adapt search to consume multiple target poses and
  // remove DCHECK below.
  DCHECK_EQ(motion_plan_problem.target_pose_infos.size(), 1)
      << "Open space search currently only supports a single target pose!";
  const math::Pose2d& target_pose =
      motion_plan_problem.target_pose_infos.front().target_pose();
  PathState end(target_pose.position(), target_pose.yaw().value(), 0.0, true);
  math::geometry::OrientedBox2d end_box =
      GetBoundingBox(end, motion_plan_problem.car_model_with_shape());
  math::geometry::Polyline2d end_box_bound(end_box.CornerPoints());
  end_box_bound.push_back(end_box_bound[0]);

  DrawPolyline(start_box_bound, "b-");
  DrawPolyline(
      std::vector<math::geometry::Point2d>{
          math::geometry::Add(start_box.center(), math::geometry::Multiply(
                                                      start_box.width_unit(),
                                                      0.5 * start_box.width())),
          math::geometry::Add(
              start_box.center(),
              math::geometry::Multiply(start_box.width_unit(),
                                       -0.5 * start_box.width())),
          math::geometry::Add(
              start_box.center(),
              math::geometry::Multiply(start_box.length_unit(),
                                       0.5 * start_box.length())),
          math::geometry::Add(
              start_box.center(),
              math::geometry::Multiply(start_box.width_unit(),
                                       0.5 * start_box.width()))},
      "b-");
  DrawPolyline(end_box_bound, "c-");
  DrawPolyline(
      std::vector<math::geometry::Point2d>{
          math::geometry::Add(end_box.center(),
                              math::geometry::Multiply(end_box.width_unit(),
                                                       0.5 * end_box.width())),
          math::geometry::Add(end_box.center(),
                              math::geometry::Multiply(end_box.width_unit(),
                                                       -0.5 * end_box.width())),
          math::geometry::Add(end_box.center(),
                              math::geometry::Multiply(end_box.length_unit(),
                                                       0.5 * end_box.length())),
          math::geometry::Add(end_box.center(),
                              math::geometry::Multiply(end_box.width_unit(),
                                                       0.5 * end_box.width()))},
      "c-");

  // Draw agents and czs.
  for (const auto& [object_id, contour] : motion_plan_problem.contours) {
    auto obstacle_vertices = contour.polygon().points();
    obstacle_vertices.push_back(obstacle_vertices[0]);
    DrawPolyline(obstacle_vertices, "g-");
  }

  // Draw road boundaries.
  for (const auto& [object_id, boundary] : motion_plan_problem.boundaries) {
    DrawPolyline(boundary.polyline().polyline(), "y-");
  }

  plt::axis("equal");
  if (show_result) {
    plt::show();
  }
}

void DrawOutput(const OpenSpacePathSearchResult& result,
                bool init_figure = true) {
  // NOTE: To unblock online test, we skip the visualization.
  return;
  // Draw optimal path and ego states.
  if (init_figure) {
    plt::figure();
  }
  math::geometry::Polyline2d optimal_polyline;
  math::geometry::Polyline2d segment_points;
  for (const MotionSegment& segment : result.motion_sequence) {
    for (const PathState& state : segment.path.path_states()) {
      optimal_polyline.emplace_back(state.position);
    }
    segment_points.emplace_back(segment.target_pose().position);
  }
  DrawPolyline(optimal_polyline, "r-");
  DrawPolyline(segment_points, "go");
  plt::axis("equal");
  plt::show();
}

// Get Robot State Snapshot.
RobotStateSnapshot GenerateRobotStateSnapshot(const voy::Pose& center_pose) {
  // Generate pose.
  // Generate parameter.
  const vehicle_model::CarModelWithAxleRectangularShape car_model_with_shape(
      vehicle_model::CarModel(
          vehicle_model::CarModel::GetMotionModelParam(av_comm::CarType::kTest),
          /*method=*/math::IntegrationMethod::RK_2),
      vehicle_model::GetEgoAxleRectangularMeasurementByType(
          av_comm::CarType::kTest));

  // Generate pose snapshot.
  return RobotStateSnapshot(center_pose, 0.0, car_model_with_shape);
}

// Populates input planner object map and lane sequence geometry from virtual
// scene data.
void LoadInputsFromVirtualSceneFile(
    const sim::pb::VirtualScene& virtual_scene, double lon_buffer,
    double lat_buffer, voy::Pose* end_pose_ptr,
    std::unordered_map<ObjectId, PlannerObject>* planner_object_map,
    lane_selection::LaneSequenceGeometry* lane_sequence_geometry_ptr) {
  // Load target pose.
  voy::Pose& end_pose = *DCHECK_NOTNULL(end_pose_ptr);
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (agent.name() == "target_pose") {
      LOG(ERROR) << "Target pose found";
      const sim::pb::TrajectoryWaypoint& agent_pose =
          agent.traffic_agent().follow_trajectory_action().trajectory_waypoints(
              0);
      end_pose.set_x(agent_pose.x_pos());
      end_pose.set_y(agent_pose.y_pos());
      end_pose.set_yaw(
          math::NormalizeMinusPiToPi(agent_pose.heading().value()));
      std::shared_ptr<RobotStateSnapshot> robot_snapshot =
          std::make_shared<RobotStateSnapshot>(
              GenerateRobotStateSnapshot(end_pose));
      end_pose.set_x(robot_snapshot->x());
      end_pose.set_y(robot_snapshot->y());
      end_pose.set_yaw(robot_snapshot->heading());
      break;
    }
  }
  // Load lane sequence geometry.
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (!agent.has_construction_zone()) {
      continue;
    }
    const ::voy::Point2d& position =
        agent.construction_zone().transform().position();
    const math::geometry::Point2d center(position.x(), position.y());
    std::vector<const pnc_map::Lane*> lanes;
    math::geometry::Polyline2d boundary_line;
    for (const ::voy::Point2d& point : agent.construction_zone().contour()) {
      boundary_line.push_back(math::geometry::Point2d(
          point.x() + position.x(), point.y() + position.y()));
    }
    boundary_line.pop_back();
    if (agent.name() == "left_boundary") {
      lane_sequence_geometry_ptr->left_hard_boundary_lines.push_back(
          math::geometry::PolylineCurve2d(boundary_line));
    } else if (agent.name() == "right_boundary") {
      lane_sequence_geometry_ptr->right_hard_boundary_lines.push_back(
          math::geometry::PolylineCurve2d(boundary_line));
    } else if (agent.name() == "nominal_path") {
      lane_sequence_geometry_ptr->nominal_path =
          math::geometry::PolylineCurve2d(boundary_line);
    }
  }

  // Prepare obstacles.
  for (const sim::pb::Agent& agent : virtual_scene.agents()) {
    if (agent.name() == "target_pose") {
      continue;
    }
    if (agent.has_traffic_agent()) {
      const sim::pb::TrajectoryWaypoint& agent_pose =
          agent.traffic_agent().follow_trajectory_action().trajectory_waypoints(
              0);
      prediction::pb::Agent agent_proto;
      auto& tracked_object = *agent_proto.mutable_tracked_object();
      tracked_object.set_center_x(agent_pose.x_pos());
      tracked_object.set_center_y(agent_pose.y_pos());
      tracked_object.set_width(agent.dimensions().width() + 2.0 * lat_buffer);
      tracked_object.set_length(agent.dimensions().length() + 2.0 * lon_buffer);
      tracked_object.set_heading(agent_pose.heading().value());

      TrafficParticipantPose traffic_pose(/*timestamp=*/0, tracked_object);

      planner_object_map->insert(std::make_pair(
          agent.id(),
          PlannerObject(agent_proto, /*timestamp=*/0, traffic_pose)));
    }
  }

  LOG(ERROR) << end_pose.DebugString();
  LOG(ERROR) << planner_object_map->size();
}

// Populates a motion plan problem with the input MotionPlanProblemManager.
void PopulateMotionPlanProblem(
    const RobotState& robot_state, const voy::Pose& target_pose,
    const lane_selection::LaneSequenceGeometry& lane_seq_geom,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        construction_zone_ptr_map,
    MotionPlanProblemManager* motion_plan_problem_manager_ptr) {
  MotionPlanProblemManager& motion_plan_problem_manager =
      *DCHECK_NOTNULL(motion_plan_problem_manager_ptr);

  motion_plan_problem_manager.SetStartPoseInfoFromRobotState(
      MotionModeGuidance(planner::pb::MotionMode::BACKWARD,
                         MotionModeGuidance::GuidanceLevel::ENFORCE));
  motion_plan_problem_manager.AppendTargetPoseInfo(
      target_pose.x(), target_pose.y(), target_pose.yaw(),
      /*terminal_motion_mode_guidance=*/std::nullopt,
      /*xy_tolerance_roi=*/std::nullopt,
      /*heading_tolerance_range=*/std::nullopt);
  motion_plan_problem_manager.SetMotionPlanROIFromStartAndTargetPoses(
      /*start_pose_xy_buffer=*/2.0 * robot_state.GetLength(),
      /*target_pose_xy_buffer=*/2.0 * robot_state.GetLength());
  motion_plan_problem_manager.PopulateOccupanciesFromStructuredDrivingInputs(
      planner_object_map, construction_zone_ptr_map, lane_seq_geom);
}

// Generates a RobotState based on initial state of the virtual scene.
RobotState GenerateRobotStateFromVirtualScene(
    const sim::pb::VirtualScene& virtual_scene) {
  voy::Pose start_pose = virtual_scene.initial_pose();
  start_pose.set_yaw(math::NormalizeMinusPiToPi(start_pose.yaw()));
  RobotState robot_state = test_utility::ConstructRobotState(
      /*robot_x=*/start_pose.x(), /*robot_y=*/start_pose.y(),
      /*vel_x=*/start_pose.vel_x(), /*vel_y=*/start_pose.vel_y());
  pb::ReplanLocatorDebug debug;
  robot_state.UpdatePoseCanbus(start_pose, robot_state.canbus(),
                               /*replan_request=*/nullptr, &debug);

  LOG(ERROR) << robot_state.current_state_snapshot().rear_bumper_position();
  LOG(ERROR)
      << robot_state.car_model_with_shape().param().measurement().DebugString();

  return robot_state;
}

// Returns a virtual scene from Json.
sim::pb::VirtualScene GenerateVirtualSceneFromJson(
    const std::string& virtual_scene_file_name) {
  const std::string virtual_scene_json_path =
      base::GetBaseDirPath(base::BaseDir::kDataDir) +
      "/planner/open_space_path_search_test/data/" + virtual_scene_file_name;
  sim::pb::VirtualScene virtual_scene;
  proto_util::ReadJsonProtoFile(virtual_scene_json_path, &virtual_scene, true);
  return virtual_scene;
}

}  // namespace

using ::google::protobuf::TextFormat;

class HybridATest : public ::testing::Test {
 public:
  virtual void SetUp() {
    CHECK(TextFormat::ParseFromString(
        R"pb(
          grid_config {
            xy_grid_resolution_in_meters: 0.5
            heading_slot_resolution_in_rad: 0.1
          }
          searcher_config {
            num_next_node_samples: 10
            path_segment_sampling_step_in_meters: 0.5
            max_num_of_feasible_path_solutions_to_find: 100
          }
          costing_config {
            forward_penalty: 1.0
            backward_penalty: 1.0
            gear_switch_penalty: 5.0
            steering_angle_penalty: 3.0
            steering_angle_change_penalty: 3.0
            heuristic_distance_penalty: 5.0
          }
          rs_path_generator_config {
            traj_short_length_penalty: 1.0
            traj_expected_shortest_length: 1.0
          }
        )pb",
        &planner_open_space_config_));

    open_space_path_searcher_ =
        std::make_unique<OpenSpacePathSearcher>(planner_open_space_config_);
  }

 protected:
  std::unique_ptr<OpenSpacePathSearcher> open_space_path_searcher_;
  pb::OpenSpaceSearchConfig planner_open_space_config_;
};

TEST_F(HybridATest, real_issue_test_1744951_lane) {
  const sim::pb::VirtualScene virtual_scene = GenerateVirtualSceneFromJson(
      /*virtual_scene_file_name=*/
      "pull_out_reverse_case_test_1744951_lane.json");

  voy::Pose end_pose;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  LoadInputsFromVirtualSceneFile(virtual_scene, /*lon_buffer=*/0.0,
                                 /*lat_buffer=*/0.0, &end_pose,
                                 &planner_object_map, &lane_sequence_geometry);

  const RobotState robot_state =
      GenerateRobotStateFromVirtualScene(virtual_scene);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  PopulateMotionPlanProblem(robot_state, end_pose, lane_sequence_geometry,
                            planner_object_map, dummy_construction_zone_ptr_map,
                            &motion_plan_problem_manager);
  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  DrawInput(motion_plan_problem);

  base::WallClockElapsedTimer timer_;
  std::optional<OpenSpacePathSearchResult> result =
      open_space_path_searcher_->Search(motion_plan_problem);
  LOG(ERROR) << "Search time: " << timer_.GetElapsedTimeInMs() << " ms";
  ASSERT_TRUE(result.has_value());
  if (result.has_value()) {
    DrawInput(motion_plan_problem, false);
    DrawOutput(result.value(), false);
  }
}

TEST_F(HybridATest, real_issue_test_1744986_lane) {
  const sim::pb::VirtualScene virtual_scene = GenerateVirtualSceneFromJson(
      /*virtual_scene_file_name=*/
      "pull_out_reverse_case_test_1744986_lane.json");

  voy::Pose end_pose;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  LoadInputsFromVirtualSceneFile(virtual_scene, /*lon_buffer=*/0.0,
                                 /*lat_buffer=*/0.0, &end_pose,
                                 &planner_object_map, &lane_sequence_geometry);

  const RobotState robot_state =
      GenerateRobotStateFromVirtualScene(virtual_scene);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  PopulateMotionPlanProblem(robot_state, end_pose, lane_sequence_geometry,
                            planner_object_map, dummy_construction_zone_ptr_map,
                            &motion_plan_problem_manager);
  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  DrawInput(motion_plan_problem);

  base::WallClockElapsedTimer timer_;
  std::optional<OpenSpacePathSearchResult> result =
      open_space_path_searcher_->Search(motion_plan_problem);
  LOG(ERROR) << "Search time: " << timer_.GetElapsedTimeInMs() << " ms";
  ASSERT_TRUE(result.has_value());
  if (result.has_value()) {
    DrawInput(motion_plan_problem, false);
    DrawOutput(result.value(), false);
  }
}

TEST_F(HybridATest, real_issue_test_1881176_lane) {
  const sim::pb::VirtualScene virtual_scene = GenerateVirtualSceneFromJson(
      /*virtual_scene_file_name=*/
      "pull_out_reverse_case_test_1881176_lane.json");

  voy::Pose end_pose;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  LoadInputsFromVirtualSceneFile(virtual_scene, /*lon_buffer=*/0.0,
                                 /*lat_buffer=*/0.0, &end_pose,
                                 &planner_object_map, &lane_sequence_geometry);

  const RobotState robot_state =
      GenerateRobotStateFromVirtualScene(virtual_scene);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  PopulateMotionPlanProblem(robot_state, end_pose, lane_sequence_geometry,
                            planner_object_map, dummy_construction_zone_ptr_map,
                            &motion_plan_problem_manager);
  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  DrawInput(motion_plan_problem);

  base::WallClockElapsedTimer timer_;
  std::optional<OpenSpacePathSearchResult> result =
      open_space_path_searcher_->Search(motion_plan_problem);
  LOG(ERROR) << "Search time: " << timer_.GetElapsedTimeInMs() << " ms";
  ASSERT_TRUE(result.has_value());
  if (result.has_value()) {
    DrawInput(motion_plan_problem, false);
    DrawOutput(result.value(), false);
  }
}

TEST_F(HybridATest, real_issue_test_1744984_lane) {
  // TODO(liwen): Test segfault.
  const sim::pb::VirtualScene virtual_scene = GenerateVirtualSceneFromJson(
      /*virtual_scene_file_name=*/
      "pull_out_reverse_case_test_1744984_lane.json");

  voy::Pose end_pose;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  LoadInputsFromVirtualSceneFile(virtual_scene, /*lon_buffer=*/0.0,
                                 /*lat_buffer=*/0.0, &end_pose,
                                 &planner_object_map, &lane_sequence_geometry);

  const RobotState robot_state =
      GenerateRobotStateFromVirtualScene(virtual_scene);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  PopulateMotionPlanProblem(robot_state, end_pose, lane_sequence_geometry,
                            planner_object_map, dummy_construction_zone_ptr_map,
                            &motion_plan_problem_manager);
  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  DrawInput(motion_plan_problem);

  base::WallClockElapsedTimer timer_;
  std::optional<OpenSpacePathSearchResult> result =
      open_space_path_searcher_->Search(motion_plan_problem);
  LOG(ERROR) << "Search time: " << timer_.GetElapsedTimeInMs() << " ms";
  ASSERT_TRUE(result.has_value());
  if (result.has_value()) {
    DrawInput(motion_plan_problem, false);
    DrawOutput(result.value(), false);
  }
}

TEST_F(HybridATest, real_issue_test_1793478_lane) {
  const sim::pb::VirtualScene virtual_scene = GenerateVirtualSceneFromJson(
      /*virtual_scene_file_name=*/
      "pull_out_reverse_case_test_1793478_lane.json");

  voy::Pose end_pose;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  std::unordered_map<ConstructionZoneId, const ConstructionZone*>
      dummy_construction_zone_ptr_map;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  LoadInputsFromVirtualSceneFile(virtual_scene, /*lon_buffer=*/0.0,
                                 /*lat_buffer=*/0.0, &end_pose,
                                 &planner_object_map, &lane_sequence_geometry);

  const RobotState robot_state =
      GenerateRobotStateFromVirtualScene(virtual_scene);
  MotionPlanProblemManager motion_plan_problem_manager(robot_state);
  PopulateMotionPlanProblem(robot_state, end_pose, lane_sequence_geometry,
                            planner_object_map, dummy_construction_zone_ptr_map,
                            &motion_plan_problem_manager);
  const MotionPlanProblem& motion_plan_problem =
      motion_plan_problem_manager.motion_plan_problem();
  DrawInput(motion_plan_problem);

  base::WallClockElapsedTimer timer_;
  std::optional<OpenSpacePathSearchResult> result =
      open_space_path_searcher_->Search(motion_plan_problem);
  LOG(ERROR) << "Search time: " << timer_.GetElapsedTimeInMs() << " ms";
  ASSERT_TRUE(result.has_value());
  if (result.has_value()) {
    DrawInput(motion_plan_problem, false);
    DrawOutput(result.value(), false);
  }
}

}  // namespace open_space

}  // namespace planner
