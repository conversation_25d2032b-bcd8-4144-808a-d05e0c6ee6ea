#include "planner/path/reasoning/agent_intention/trajectory_reasoner/trajectory_reasoner.h"

#include <absl/strings/str_cat.h>

#include <algorithm>
#include <unordered_map>

#include "geometry/algorithms/intersects.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_param.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

TrajectoryIntentionMetaData TrajectoryReasoner::GetTrajectoryIntentionMetaData(
    const AgentInLaneStates& agent_state,
    const RequiredLateralGap& required_lateral_gap,
    const RequiredLateralGap& required_lat_gap_for_pass_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const AgentSLBoundary& object_sl_boundary,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const bool should_ignore_by_upstream,
    const std::optional<planner::pb::YieldIntentionData>& updated_yield_data,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const {
  (void)path_reasoner_seed_per_agent;
  TRACE_EVENT_SCOPE(planner,
                    TrajectoryReasoner_GetTrajectoryIntentionMetaData_Agent);
  // Note(yongbing, harry): The reasoner order is
  // static->low_confidence ->
  // reverse->side->pull_over->oncoming->ped->crossing->lane_change
  // ->lead_and_merge->cyclist->fallback.

  // TODO(yawen): Add more rule based logics here.
  TrajectoryIntentionMetaData meta_data;
  meta_data.is_static = blocking_state_meta_data.reasoning_info.is_stationary();
  meta_data.required_lat_gap = required_lateral_gap;
  meta_data.required_lat_gap_for_pass_state = required_lat_gap_for_pass_state;
  meta_data.yield_intention_data = updated_yield_data;
  meta_data.fod_intentions.clear();
  if (blocking_state_meta_data.reasoning_info.is_far_away_behind()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to far away behind; ");
    return meta_data;
  }

  // Business logic, which will be removed in the future.
  if (!blocking_state_meta_data.reasoning_info.was_nudge_intention() &&
      blocking_state_meta_data.reasoning_info.is_ignorable_unknown()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore ignorable unknown; ");
    return meta_data;
  }

  // Ignores cyclist overtaking behind ego vehicle based on scenario result.
  if (blocking_state_meta_data.reasoning_info.is_cyclist_overtaking() &&
      IsIgnorableBehindAgent(blocking_state_meta_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore ignorable behind overtaking cyclist; ");
    return meta_data;
  }

  if (should_ignore_by_upstream) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to upstream; ");
    return meta_data;
  }

  if (behavior_type_ != pb::DECOUPLED_PULL_OVER &&
      blocking_state_meta_data.reasoning_info
          .is_primarilly_out_of_physical_boundary()) {
    // For agent that is primarily outside of physical boundary, ignore it.
    rt_event::PostRtEvent<
        rt_event::planner::IgnoreOutOfPhysicalBoundaryAgent>();
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to outside of physical boundary; ");
    return meta_data;
  }

  if (static_agent_reasoner_.ShouldInvokePreReason(blocking_state_meta_data,
                                                   agent_state, behavior_type_,
                                                   meta_data.is_static)) {
    static_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                     object_sl_boundary, meta_data);
    return meta_data;
  }

  if (!generate_intent_for_dynamic_ && !meta_data.is_static) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to ignoring dynamic agent; ");
    return meta_data;
  }

  if (blocking_state_meta_data.reasoning_info.is_ignorable_behind()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to ignorable behind agent; ");
    return meta_data;
  }

  if (blocking_state_meta_data.reasoning_info.contain_abnomal_inlane_state()) {
    if (blocking_state_meta_data.reasoning_info.is_agent_within_comfort()) {
      meta_data.is_static = true;
      meta_data.intention_type = pb::TrajectoryState::MIXED;
      absl::StrAppend(
          &meta_data.debug_str,
          "Consider as static due to close distance during abnormal state; ");
      return meta_data;
    }
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to abnormal state; ");
    return meta_data;
  }

  if (low_confidence_agent_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    low_confidence_agent_path_reasoner_.PreReason(
        agent_state, blocking_state_meta_data, object_sl_boundary, meta_data);
    return meta_data;
  }

  if (dynamic_overtake_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    dynamic_overtake_path_reasoner_.PreReason(
        agent_state, blocking_state_meta_data, object_sl_boundary, meta_data);
    return meta_data;
  }

  if (reversing_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    reversing_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                        object_sl_boundary, meta_data);
    return meta_data;
  }

  if (oncoming_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    oncoming_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                       object_sl_boundary, meta_data);
    return meta_data;
  }

  if (side_agent_reasoner_.ShouldInvokePreReason(blocking_state_meta_data,
                                                 agent_state, behavior_type_,
                                                 meta_data.is_static)) {
    side_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                   object_sl_boundary, meta_data);
    return meta_data;
  }

  if (pull_over_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    pull_over_path_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                       object_sl_boundary, meta_data);
    return meta_data;
  }

  if (pedestrian_reasoner_.ShouldInvokePreReason(blocking_state_meta_data,
                                                 agent_state, behavior_type_,
                                                 meta_data.is_static)) {
    pedestrian_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                   object_sl_boundary, meta_data);
    return meta_data;
  }

  if (cross_agent_reasoner_.ShouldInvokePreReason(blocking_state_meta_data,
                                                  agent_state, behavior_type_,
                                                  meta_data.is_static)) {
    cross_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                    object_sl_boundary, meta_data);
    return meta_data;
  }

  if (cyclist_reasoner_.ShouldInvokePreReason(blocking_state_meta_data,
                                              agent_state, behavior_type_,
                                              meta_data.is_static)) {
    cyclist_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                object_sl_boundary, meta_data);
    return meta_data;
  }

  if (lane_change_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    lane_change_agent_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                                          object_sl_boundary, meta_data);
    return meta_data;
  }

  if (lead_and_merge_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, agent_state, behavior_type_,
          meta_data.is_static)) {
    lead_and_merge_agent_reasoner_.PreReason(
        agent_state, blocking_state_meta_data, object_sl_boundary, meta_data);
    return meta_data;
  }

  fallback_reasoner_.PreReason(agent_state, blocking_state_meta_data,
                               object_sl_boundary, meta_data);
  return meta_data;
}

TrajectoryIntentionMetaData TrajectoryReasoner::GetTrajectoryIntentionMetaData(
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lateral_gap,
    const RequiredLateralGap& required_lat_gap_for_pass_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const AgentSLBoundary& object_sl_boundary,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const bool should_ignore_by_upstream,
    const std::optional<planner::pb::YieldIntentionData>& updated_yield_data,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const {
  (void)path_reasoner_seed_per_agent;
  // TODO(yawen): Add more rule based logics here.
  TrajectoryIntentionMetaData meta_data;
  meta_data.is_static = blocking_state_meta_data.reasoning_info.is_stationary();
  meta_data.required_lat_gap = required_lateral_gap;
  meta_data.required_lat_gap_for_pass_state = required_lat_gap_for_pass_state;
  meta_data.yield_intention_data = updated_yield_data;
  if (blocking_state_meta_data.reasoning_info.is_far_away_behind()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to far away behind; ");
    return meta_data;
  }

  // Business logic, which will be removed in the future.
  if (!blocking_state_meta_data.reasoning_info.was_nudge_intention() &&
      blocking_state_meta_data.reasoning_info.is_ignorable_unknown()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore ignorable unknown; ");
    return meta_data;
  }

  // Ignores cyclist overtaking behind ego vehicle based on scenario result.
  if (blocking_state_meta_data.reasoning_info.is_cyclist_overtaking() &&
      IsIgnorableBehindAgent(blocking_state_meta_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore ignorable behind overtaking cyclist; ");
    return meta_data;
  }

  if (should_ignore_by_upstream) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to upstream; ");
    return meta_data;
  }

  if (behavior_type_ != pb::DECOUPLED_PULL_OVER &&
      blocking_state_meta_data.reasoning_info
          .is_primarilly_out_of_physical_boundary()) {
    // For agent that is primarily outside of physical boundary, ignore it.
    rt_event::PostRtEvent<
        rt_event::planner::IgnoreOutOfPhysicalBoundaryAgent>();
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to outside of physical boundary; ");
    return meta_data;
  }

  if (static_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    static_agent_reasoner_.PreReason(object_occupancy_state,
                                     blocking_state_meta_data,
                                     object_sl_boundary, meta_data);
    return meta_data;
  }

  if (!generate_intent_for_dynamic_ && !meta_data.is_static) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to ignoring dynamic agent; ");
    return meta_data;
  }

  if (blocking_state_meta_data.reasoning_info.is_ignorable_behind()) {
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str,
                    "Ignore due to ignorable behind agent; ");
    return meta_data;
  }

  if (blocking_state_meta_data.reasoning_info.contain_abnomal_inlane_state()) {
    if (blocking_state_meta_data.reasoning_info.is_agent_within_comfort()) {
      meta_data.is_static = true;
      meta_data.intention_type = pb::TrajectoryState::MIXED;
      absl::StrAppend(
          &meta_data.debug_str,
          "Consider as static due to close distance during abnormal state; ");
      return meta_data;
    }
    meta_data.intention_type = pb::TrajectoryState::IGNORE;
    absl::StrAppend(&meta_data.debug_str, "Ignore due to abnormal state; ");
    return meta_data;
  }

  // Note(yongbing): The reasoner order is
  // low_confidence->dynamic_overtake->reverse->side->oncoming->ped->crossing->
  // lead_and_merge->cyclist->fallback.

  if (low_confidence_agent_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    low_confidence_agent_path_reasoner_.PreReason(
        object_occupancy_state, blocking_state_meta_data, object_sl_boundary,
        meta_data);
    return meta_data;
  }

  if (dynamic_overtake_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    dynamic_overtake_path_reasoner_.PreReason(object_occupancy_state,
                                              blocking_state_meta_data,
                                              object_sl_boundary, meta_data);
    return meta_data;
  }

  if (reversing_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    reversing_agent_reasoner_.PreReason(object_occupancy_state,
                                        blocking_state_meta_data,
                                        object_sl_boundary, meta_data);
    return meta_data;
  }

  if (oncoming_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    oncoming_agent_reasoner_.PreReason(object_occupancy_state,
                                       blocking_state_meta_data,
                                       object_sl_boundary, meta_data);
    return meta_data;
  }

  if (side_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    side_agent_reasoner_.PreReason(object_occupancy_state,
                                   blocking_state_meta_data, object_sl_boundary,
                                   meta_data);
    return meta_data;
  }

  if (pull_over_path_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    pull_over_path_reasoner_.PreReason(object_occupancy_state,
                                       blocking_state_meta_data,
                                       object_sl_boundary, meta_data);
    return meta_data;
  }

  if (pedestrian_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    pedestrian_reasoner_.PreReason(object_occupancy_state,
                                   blocking_state_meta_data, object_sl_boundary,
                                   meta_data);
    return meta_data;
  }

  if (cross_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    cross_agent_reasoner_.PreReason(object_occupancy_state,
                                    blocking_state_meta_data,
                                    object_sl_boundary, meta_data);
    return meta_data;
  }

  if (cyclist_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    cyclist_reasoner_.PreReason(object_occupancy_state,
                                blocking_state_meta_data, object_sl_boundary,
                                meta_data);
    return meta_data;
  }

  if (lane_change_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    lane_change_agent_reasoner_.PreReason(object_occupancy_state,
                                          blocking_state_meta_data,
                                          object_sl_boundary, meta_data);
    return meta_data;
  }

  if (lead_and_merge_agent_reasoner_.ShouldInvokePreReason(
          blocking_state_meta_data, object_occupancy_state, behavior_type_,
          meta_data.is_static)) {
    lead_and_merge_agent_reasoner_.PreReason(object_occupancy_state,
                                             blocking_state_meta_data,
                                             object_sl_boundary, meta_data);
    return meta_data;
  }

  fallback_reasoner_.PreReason(object_occupancy_state, blocking_state_meta_data,
                               object_sl_boundary, meta_data);
  return meta_data;
}

TrajectoryIntentionMetaData TrajectoryReasoner::GetTrajectoryIntentionMetaData(
    const ConstructionZoneInLaneState& construction_zone_state,
    const RequiredLateralGap& required_lateral_gap,
    const RequiredLateralGap& required_lateral_gap_for_pass_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const AgentSLBoundary& object_sl_boundary,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const {
  TRACE_EVENT_SCOPE(planner,
                    TrajectoryReasoner_GetTrajectoryIntentionMetaData_Cz);
  (void)path_reasoner_seed_per_agent;
  TrajectoryIntentionMetaData meta_data;
  meta_data.is_static = true;
  meta_data.required_lat_gap = required_lateral_gap;
  meta_data.required_lat_gap_for_pass_state =
      required_lateral_gap_for_pass_state;
  static_agent_reasoner_.PreReason(construction_zone_state,
                                   blocking_state_meta_data, object_sl_boundary,
                                   meta_data);
  return meta_data;
}

// TODO(Harry): show expected reasoner order here by some comments.
std::vector<RepulsionMetaData> TrajectoryReasoner::GetRepulsionMetaDatas(
    const RobotStateSnapshot& robot_state_snapshot,
    const AgentInLaneStates& agent_state,
    const RequiredLateralGap& required_lateral_gap,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<planner::pb::YieldIntentionData>& yield_intention_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent,
    std::unordered_map<pb::AgentReasonerId, std::string>&
        repulsion_reasoning_debugs) const {
  TRACE_EVENT_SCOPE(planner, TrajectoryReasoner_GetRepulsionMetaDatas);
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  auto object_type = agent_state.agent_metadata.agent_type;

  if (imaginary_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::IMAGINARY_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        imaginary_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::IMAGINARY_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
    // When the agent is an imaginary agent, no need for the following logics
    // which are designed for actual agents.
    return repulsion_meta_datas;
  }

  if (low_confidence_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs
              [pb::AgentReasonerId::LOW_CONFIDENCE_AGENT])) {
    std::vector<RepulsionMetaData> low_confidence_agent_repulsion_meta_data =
        low_confidence_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs
                [pb::AgentReasonerId::LOW_CONFIDENCE_AGENT]);
    repulsion_meta_datas.insert(
        repulsion_meta_datas.end(),
        low_confidence_agent_repulsion_meta_data.begin(),
        low_confidence_agent_repulsion_meta_data.end());
    if (blocking_state_meta_data.reasoning_info.is_low_confidence_object() &&
        !low_confidence_agent_repulsion_meta_data.empty()) {
      // When the agent is low confidence, we don't need to consider other
      // reasoners.
      return repulsion_meta_datas;
    }
  }

  if (lane_change_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::LANE_CHANGE_AGENT])) {
    std::vector<RepulsionMetaData> lane_change_repulsion_meta_data =
        lane_change_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info,
            repulsion_reasoning_debugs[pb::AgentReasonerId::LANE_CHANGE_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                lane_change_repulsion_meta_data.begin(),
                                lane_change_repulsion_meta_data.end());
  }
  // TODO(Harry): Remove this flag which is only used to make sure the change is
  // no-op.
  bool need_to_be_handled_by_pull_over_reasoner = false;
  if (static_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::STATIC_AGENT])) {
    std::vector<RepulsionMetaData> static_agent_repulsion_meta_datas =
        static_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            path_reasoner_seed_per_agent, lane_change_execution_info,
            frame_analyzer_results, trajectory_intention_data.fod_intentions,
            need_to_be_handled_by_pull_over_reasoner,
            repulsion_reasoning_debugs[pb::AgentReasonerId::STATIC_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                static_agent_repulsion_meta_datas.begin(),
                                static_agent_repulsion_meta_datas.end());
  }
  repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH] +=
      DUMP_TO_STRING(need_to_be_handled_by_pull_over_reasoner) + "\n";
  if (pull_over_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH]) ||
      need_to_be_handled_by_pull_over_reasoner) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        pull_over_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  // Add repulsions to cut-in agent with multiple trajectories.
  if (lead_and_merge_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs
              [pb::AgentReasonerId::LEAD_AND_MERGE_AGENT])) {
    std::vector<RepulsionMetaData> lead_and_merge_repulsion_meta_datas =
        lead_and_merge_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info, frame_analyzer_results,
            repulsion_reasoning_debugs
                [pb::AgentReasonerId::LEAD_AND_MERGE_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                lead_and_merge_repulsion_meta_datas.begin(),
                                lead_and_merge_repulsion_meta_datas.end());
  }
  // Add repulsions to side agent beside ego.
  if (side_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::SIDE_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        side_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info,
            repulsion_reasoning_debugs[pb::AgentReasonerId::SIDE_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (reversing_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::REVERSING_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        reversing_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::REVERSING_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (pedestrian_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::PDESTRIAN])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        pedestrian_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::PDESTRIAN]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (rear_end_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::REAR_END_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        rear_end_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            reusable_last_path, reusable_last_speed_profile,
            agent_state.agent_metadata.agent_type == voy::perception::VEHICLE
                ? required_lateral_gap.comfort_required_lateral_gap
                : 0.0,
            repulsion_reasoning_debugs[pb::AgentReasonerId::REAR_END_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (cross_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::CROSS_AGENT])) {
    std::vector<RepulsionMetaData> cross_repulsion_meta_datas =
        cross_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, agent_state, blocking_state_meta_data,
            trajectory_blocking_state_data_map, yield_intention_data,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info, frame_analyzer_results,
            trajectory_intention_data,
            repulsion_reasoning_debugs[pb::AgentReasonerId::CROSS_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                cross_repulsion_meta_datas.begin(),
                                cross_repulsion_meta_datas.end());
  }

  if (cyclist_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::CYCLIST])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        cyclist_reasoner_.PreRepulsionReason(
            agent_state, blocking_state_meta_data, reusable_last_path,
            required_lateral_gap, frame_analyzer_results,
            repulsion_reasoning_debugs[pb::AgentReasonerId::CYCLIST]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (dynamic_overtake_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::DYNAMIC_OVERTAKE])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        dynamic_overtake_path_reasoner_.PreRepulsionReason(
            agent_state, trajectory_intention_data, blocking_state_meta_data,
            repulsion_reasoning_debugs[pb::AgentReasonerId::DYNAMIC_OVERTAKE]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (oncoming_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_oncoming(),
          agent_state.agent_imaginary_actual_type,
          repulsion_reasoning_debugs[pb::AgentReasonerId::ONCOMING_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        oncoming_agent_reasoner_.PreRepulsionReason(
            agent_state, blocking_state_meta_data, robot_state_snapshot.speed(),
            required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::ONCOMING_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  return repulsion_meta_datas;
}

// TODO(Harry): show expected reasoner order here by some comments.
std::vector<RepulsionMetaData> TrajectoryReasoner::GetRepulsionMetaDatas(
    const RobotStateSnapshot& robot_state_snapshot,
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lateral_gap,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<planner::pb::YieldIntentionData>& yield_intention_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent,
    std::unordered_map<pb::AgentReasonerId, std::string>&
        repulsion_reasoning_debugs) const {
  (void)path_reasoner_seed_per_agent;
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  auto object_type = object_occupancy_state.object_type();
  if (imaginary_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::IMAGINARY_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        imaginary_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::IMAGINARY_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
    // When the agent is an imaginary agent, no need for the following logics
    // which are designed for actual agents.
    return repulsion_meta_datas;
  }

  if (low_confidence_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs
              [pb::AgentReasonerId::LOW_CONFIDENCE_AGENT])) {
    std::vector<RepulsionMetaData> low_confidence_agent_repulsion_meta_data =
        low_confidence_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs
                [pb::AgentReasonerId::LOW_CONFIDENCE_AGENT]);
    repulsion_meta_datas.insert(
        repulsion_meta_datas.end(),
        low_confidence_agent_repulsion_meta_data.begin(),
        low_confidence_agent_repulsion_meta_data.end());
    if (blocking_state_meta_data.reasoning_info.is_low_confidence_object() &&
        !low_confidence_agent_repulsion_meta_data.empty()) {
      // When the agent is low confidence, we don't need to consider other
      // reasoners.
      return repulsion_meta_datas;
    }
  }

  if (lane_change_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::LANE_CHANGE_AGENT])) {
    std::vector<RepulsionMetaData> lane_change_repulsion_meta_data =
        lane_change_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info,
            repulsion_reasoning_debugs[pb::AgentReasonerId::LANE_CHANGE_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                lane_change_repulsion_meta_data.begin(),
                                lane_change_repulsion_meta_data.end());
  }
  // TODO(Harry): Remove this flag which is only used to make sure the change is
  // no-op.
  bool need_to_be_handled_by_pull_over_reasoner = false;
  if (static_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::STATIC_AGENT])) {
    std::vector<RepulsionMetaData> static_agent_repulsion_meta_datas =
        static_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            path_reasoner_seed_per_agent, lane_change_execution_info,
            frame_analyzer_results, trajectory_intention_data.fod_intentions,
            need_to_be_handled_by_pull_over_reasoner,
            repulsion_reasoning_debugs[pb::AgentReasonerId::STATIC_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                static_agent_repulsion_meta_datas.begin(),
                                static_agent_repulsion_meta_datas.end());
  }
  repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH] +=
      DUMP_TO_STRING(need_to_be_handled_by_pull_over_reasoner) + "\n";
  if (pull_over_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH]) ||
      need_to_be_handled_by_pull_over_reasoner) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        pull_over_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::PULL_OVER_PATH]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  // Add repulsions to cut-in agent with multiple trajectories.
  if (lead_and_merge_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs
              [pb::AgentReasonerId::LEAD_AND_MERGE_AGENT])) {
    std::vector<RepulsionMetaData> lead_and_merge_repulsion_meta_datas =
        lead_and_merge_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, trajectory_blocking_state_data_map,
            reusable_last_path, reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info, frame_analyzer_results,
            repulsion_reasoning_debugs
                [pb::AgentReasonerId::LEAD_AND_MERGE_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                lead_and_merge_repulsion_meta_datas.begin(),
                                lead_and_merge_repulsion_meta_datas.end());
  }
  if (side_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::SIDE_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        side_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::SIDE_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  if (reversing_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::REVERSING_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        reversing_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::REVERSING_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (pedestrian_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::PDESTRIAN])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        pedestrian_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::PDESTRIAN]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (rear_end_agent_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::REAR_END_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        rear_end_agent_path_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, reusable_last_path,
            reusable_last_speed_profile,
            object_occupancy_state.object_type() == voy::perception::VEHICLE
                ? required_lateral_gap.comfort_required_lateral_gap
                : 0.0,
            repulsion_reasoning_debugs[pb::AgentReasonerId::REAR_END_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (cross_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::CROSS_AGENT])) {
    std::vector<RepulsionMetaData> cross_repulsion_meta_datas =
        cross_agent_reasoner_.PreRepulsionReason(
            robot_state_snapshot, object_occupancy_state,
            blocking_state_meta_data, trajectory_blocking_state_data_map,
            yield_intention_data, reusable_last_path,
            reusable_last_speed_profile,
            required_lateral_gap.comfort_required_lateral_gap,
            lane_change_execution_info, frame_analyzer_results,
            repulsion_reasoning_debugs[pb::AgentReasonerId::CROSS_AGENT]);
    repulsion_meta_datas.insert(repulsion_meta_datas.end(),
                                cross_repulsion_meta_datas.begin(),
                                cross_repulsion_meta_datas.end());
  }

  if (cyclist_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::CYCLIST])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        cyclist_reasoner_.PreRepulsionReason(
            object_occupancy_state, blocking_state_meta_data,
            reusable_last_path, required_lateral_gap, frame_analyzer_results,
            repulsion_reasoning_debugs[pb::AgentReasonerId::CYCLIST]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (dynamic_overtake_path_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_stationary(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::DYNAMIC_OVERTAKE])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        dynamic_overtake_path_reasoner_.PreRepulsionReason(
            object_occupancy_state, trajectory_intention_data,
            blocking_state_meta_data,
            repulsion_reasoning_debugs[pb::AgentReasonerId::DYNAMIC_OVERTAKE]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  if (oncoming_agent_reasoner_.ShouldInvokePreRepulsionReason(
          blocking_state_meta_data, object_type, behavior_type_,
          blocking_state_meta_data.reasoning_info.is_oncoming(),
          object_occupancy_state.agent_imaginary_actual_type(),
          repulsion_reasoning_debugs[pb::AgentReasonerId::ONCOMING_AGENT])) {
    std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
        oncoming_agent_reasoner_.PreRepulsionReason(
            object_occupancy_state, blocking_state_meta_data,
            robot_state_snapshot.speed(), required_lateral_gap,
            repulsion_reasoning_debugs[pb::AgentReasonerId::ONCOMING_AGENT]);
    if (repulsion_meta_data != nullptr) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  return repulsion_meta_datas;
}

std::vector<RepulsionMetaData> TrajectoryReasoner::GetRepulsionMetaData(
    const RobotStateSnapshot& robot_state_snapshot,
    const ConstructionZoneInLaneState& cz_inlane_state,
    pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const {
  (void)path_reasoner_seed_per_agent;
  std::unique_ptr<RepulsionMetaData> repulsion_meta_data =
      static_agent_reasoner_.PreRepulsionReason(robot_state_snapshot,
                                                cz_inlane_state);
  if (repulsion_meta_data != nullptr) {
    return {*repulsion_meta_data};
  }
  return {};
}

void TrajectoryReasoner::PostProcessAgentIntentionResult(
    const AgentInLaneStates& agent_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
    IntentionResultMetaData& intention_result) const {
  auto object_type = agent_state.agent_metadata.agent_type;
  if (static_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    static_agent_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                      trajectory_intention_meta_data,
                                      intention_result);
    return;
  }

  // PostReason is only for the agent which is handled by st_planner.
  if (trajectory_intention_meta_data.intention_type ==
          pb::TrajectoryState::IGNORE ||
      trajectory_intention_meta_data.blocking_sequences.empty()) {
    absl::StrAppend(&intention_result.debug_str, "Prereason ignore agent; ");
    return;
  }

  /*Revise wrong(unreasonable) intention after st_planner (e.g. nudge intention
   * for leading vehicle)*/
  // Ignore the agent st region has no collision with best st rollout.
  if (intention_result.nudge_index_ranges.empty() ||
      std::any_of(
          intention_result.nudge_index_ranges.begin(),
          intention_result.nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    absl::StrAppend(&intention_result.debug_str,
                    "Nudge range empty in st planner; ");
    return;
  }

  // Returns soft nudge constraint for is_ignorable_behind agent in normal
  // scene.
  if (IsIgnorableBehindAgent(blocking_state_meta_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    intention_result.required_lat_gap.critical_required_lateral_gap =
        math::constants::kEpsilon;
    intention_result.is_overtaken = false;
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    absl::StrAppend(&intention_result.debug_str,
                    "IgnorableBehindAgent set cri_rlg = 0; ");
    return;
  }

  // Reversing agent needs to decide whether it needs to be considered.
  if (reversing_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    reversing_agent_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                         trajectory_intention_meta_data,
                                         intention_result);
    return;
  }

  if (oncoming_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    oncoming_agent_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                        trajectory_intention_meta_data,
                                        intention_result);
    return;
  }

  // Post reason of dynamic overtake path reasoner should be earlier called than
  // lead and merge reasoner, cyclist reason, and later than oncoming reasoner.
  if (dynamic_overtake_path_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    dynamic_overtake_path_reasoner_.PostReason(
        agent_state, blocking_state_meta_data, trajectory_intention_meta_data,
        intention_result);
    return;
  }

  // Side agent needs to decide whether it needs to be considered according to
  // the scenario_identify_result.
  if (side_agent_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                                  object_type, behavior_type_,
                                                  intention_result.is_static)) {
    side_agent_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                    trajectory_intention_meta_data,
                                    intention_result);
    return;
  }

  // Note(yongbing): we may modify the nudge decision for lead agent,
  // CrossAgentReasoner and LeadAndMergeReasoner can not move to behind.
  if (cross_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    // In turning and U-turn scenarios, agents traveling in the same
    // direction as the ego vehicle may be considered as crossing.
    // In this case, we need to handle cyclists overtaking closely
    // from behind to avoid fast steering wheel in cyclist_reasoner.
    cross_agent_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                     trajectory_intention_meta_data,
                                     intention_result);
    return;
  }

  if (cyclist_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                               object_type, behavior_type_,
                                               intention_result.is_static)) {
    cyclist_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                 trajectory_intention_meta_data,
                                 intention_result);
    return;
  }

  if (pedestrian_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                                  object_type, behavior_type_,
                                                  intention_result.is_static)) {
    pedestrian_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                    trajectory_intention_meta_data,
                                    intention_result);
    return;
  }

  if (lane_change_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    lane_change_agent_reasoner_.PostReason(
        agent_state, blocking_state_meta_data, trajectory_intention_meta_data,
        intention_result);
    return;
  }

  if (lead_and_merge_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    lead_and_merge_agent_reasoner_.PostReason(
        agent_state, blocking_state_meta_data, trajectory_intention_meta_data,
        intention_result);
    return;
  }

  fallback_reasoner_.PostReason(agent_state, blocking_state_meta_data,
                                trajectory_intention_meta_data,
                                intention_result);
  return;
}

void TrajectoryReasoner::PostProcessAgentIntentionResult(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
    IntentionResultMetaData& intention_result) const {
  auto object_type = object_occupancy_state.object_type();
  if (static_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    static_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  // PostReason is only for the agent which is handled by st_planner.
  if (trajectory_intention_meta_data.intention_type ==
          pb::TrajectoryState::IGNORE ||
      trajectory_intention_meta_data.blocking_sequences.empty()) {
    return;
  }

  /*Revise wrong(unreasonable) intention after st_planner (e.g. nudge intention
   * for leading vehicle)*/
  // Ignore the agent st region has no collision with best st rollout.
  if (intention_result.nudge_index_ranges.empty() ||
      std::any_of(
          intention_result.nudge_index_ranges.begin(),
          intention_result.nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    return;
  }

  if (IsIgnorableBehindAgent(blocking_state_meta_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    intention_result.required_lat_gap.critical_required_lateral_gap =
        math::constants::kEpsilon;
    intention_result.is_overtaken = false;
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return;
  }

  // Reversing agent needs to decide whether it needs to be considered.
  if (reversing_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    reversing_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  if (oncoming_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    oncoming_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  // Post reason of dynamic overtake path reasoner should be earlier called than
  // lead and merge reasoner, cyclist reason, and later than oncoming reasoner.
  if (dynamic_overtake_path_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    dynamic_overtake_path_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  // Side agent needs to decide whether it needs to be considered according to
  // the scenario_identify_result.
  if (side_agent_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                                  object_type, behavior_type_,
                                                  intention_result.is_static)) {
    side_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  // Note(yongbing): we may modify the nudge decision for lead agent,
  // CrossAgentReasoner and LeadAndMergeReasoner can not move to behind.
  if (cross_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    // In turning and U-turn scenarios, agents traveling in the same
    // direction as the ego vehicle may be considered as crossing.
    // In this case, we need to handle cyclists overtaking closely
    // from behind to avoid fast steering wheel in cyclist_reasoner.
    cross_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  if (cyclist_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                               object_type, behavior_type_,
                                               intention_result.is_static)) {
    cyclist_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  if (pedestrian_reasoner_.ShouldInvokePostReason(blocking_state_meta_data,
                                                  object_type, behavior_type_,
                                                  intention_result.is_static)) {
    pedestrian_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  if (lane_change_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    lane_change_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  if (lead_and_merge_agent_reasoner_.ShouldInvokePostReason(
          blocking_state_meta_data, object_type, behavior_type_,
          intention_result.is_static)) {
    lead_and_merge_agent_reasoner_.PostReason(
        object_occupancy_state, blocking_state_meta_data,
        trajectory_intention_meta_data, intention_result);
    return;
  }

  fallback_reasoner_.PostReason(
      object_occupancy_state, blocking_state_meta_data,
      trajectory_intention_meta_data, intention_result);
  return;
}

void TrajectoryReasoner::PostProcessAgentIntentionResult(
    const ConstructionZoneInLaneState& construction_zone_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(intention_result.is_static);
  static_agent_reasoner_.PostReason(
      construction_zone_state, blocking_state_meta_data,
      trajectory_intention_meta_data, intention_result);
}

// This function determines whether to make lane boundaries immutable (like
// solid lines) to prevent undesired lane boundary crossing in tight spaces
// Returns true if boundaries were made immutable, false otherwise
bool TrajectoryReasoner::AdjustSolidLineConstraint(
    std::map<TypedObjectId, IntentionResultMetaData>& intention_meta_data_map)
    const {
  // First, check if any agent requires ego to cross lane boundaries
  // This includes explicit cross-lane decisions or emergency swerve situations
  bool is_lane_crossing_required = std::any_of(
      intention_meta_data_map.begin(), intention_meta_data_map.end(),
      [](const auto& agent_entry) {
        return (agent_entry.second.solid_line_lane_boundary_decision ==
                    LaneBoundaryDecision::kCrossLane ||
                agent_entry.second.is_emergency_swerve_agent ||
                agent_entry.second.is_lane_encroach_intent_agent);
      });
  if (!is_lane_crossing_required) {
    return false;
  }

  // Only consider hardening boundaries if no agent requires lane crossing
  // AND at least one agent explicitly requires staying in lane
  bool should_enforce_lane_boundaries =
      !is_lane_crossing_required &&
      std::any_of(
          intention_meta_data_map.begin(), intention_meta_data_map.end(),
          [](const auto& agent_entry) {
            return agent_entry.second.solid_line_lane_boundary_decision ==
                   LaneBoundaryDecision::kStayInLane;
          });
  if (!should_enforce_lane_boundaries) {
    return false;
  }

  // If we need to enforce lane boundaries, make them immutable
  rt_event::PostRtEvent<
      rt_event::planner::PathReasoningSolidLineEnhancementForLargeVehicle>();

  // Make both left and right lane boundaries immutable to prevent crossing
  // This effectively treats lane markings as solid lines that cannot be
  // crossed
  constraint_manager_
      ->SetVirtualHardLaneMarkingBoundaryToImmutableWithMotionEngine(
          ego_param_, math::pb::Side::kLeft);
  constraint_manager_
      ->SetVirtualHardLaneMarkingBoundaryToImmutableWithMotionEngine(
          ego_param_, math::pb::Side::kRight);

  return true;
}

}  // namespace planner
