#pragma once
#include <string>

#include <ros/ros.h>
#include "param_getter.h"

namespace system_supervisor {
class RosParamGetter : public IParamGetter {
 public:
  static constexpr const char* kTripIDRosParamKey = "/trip_id";

  [[nodiscard]] std::optional<std::string> getTripID(
      bool from_cache) const override {
    std::string trip_id;
    if (from_cache) {
      if (ros::param::getCached(kTripIDRosParamKey, trip_id)) {
        return trip_id;
      }
    } else {
      if (ros::param::get(kTripIDRosParamKey, trip_id)) {
        return trip_id;
      }
    }
    return std::nullopt;
  }
};
}  // namespace system_supervisor
