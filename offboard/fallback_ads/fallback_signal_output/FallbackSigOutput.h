/*
 * File: FallbackSigOutput.h
 *
 * Code generated for Simulink model 'FallbackSigOutput'.
 *
 * Model version                  : 1.315
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Sun Jun  8 12:15:52 2025
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#ifndef RTW_HEADER_FallbackSigOutput_h_
#define RTW_HEADER_FallbackSigOutput_h_
#include <math.h>
#ifndef FallbackSigOutput_COMMON_INCLUDES_
#define FallbackSigOutput_COMMON_INCLUDES_
#include <math.h>
#include "common_math_library/rtwtypes.h"
#include "_out/Appl/GenData/Components/Rte_FallbackSigOutput.h"
#endif                                 /* FallbackSigOutput_COMMON_INCLUDES_ */

#include "FallbackSigOutput_types.h"

/* Macros for accessing real-time model data structure */

/* user code (top of header file) */
#include "fallback_signal_output/checksum/checksum.h"

/* Block signals (default storage) */
typedef struct tag_B_FallbackSigOutput_T {
  CAN_MESSAGE_BUS CAN_Pack_YRS2;       /* '<S114>/CAN_Pack_YRS2' */
  CAN_MESSAGE_BUS CAN_Pack_YRS1;       /* '<S107>/CAN_Pack_YRS1' */
  CAN_MESSAGE_BUS CAN_Pack_SAS_Status; /* '<S100>/CAN_Pack_SAS_Status' */
  CAN_MESSAGE_BUS CAN_Pack_ESC_Status; /* '<S94>/CAN_Pack_ESC_Status' */
  CAN_MESSAGE_BUS CAN_Pack_FrontWheelSpeedKPH;/* '<S88>/CAN_Pack_FrontWheelSpeedKPH' */
  CAN_MESSAGE_BUS CAN_Pack_FrontWheelSpeedKPH_j;/* '<S84>/CAN_Pack_FrontWheelSpeedKPH' */
  CAN_MESSAGE_BUS CAN_Pack_ESC_DA_MESSAGE;/* '<S78>/CAN_Pack_ESC_DA_MESSAGE' */
  SG_SwtExtrLiFromAPI OutportBufferForVIMMid3CanFr11_SG_SwtExtrLiFromAPI;/* '<S2>/EnterAutonomousDrive' */
  SG_AdFusedFricEstimn OutportBufferForVIMMid3CanFr11_SG_AdFusedFricEstimn;/* '<S2>/EnterAutonomousDrive' */
  SG_AdFreeDst OutportBufferForVIMMid3CanFr13_SG_AdFreeDst;/* '<S2>/EnterAutonomousDrive' */
  FallbackDebugInfoReserve4
    OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve4;/* '<S2>/FallbackInfo' */
  SystemStatusReserved1
    OutportBufferForFallbackSystemStatus_SystemStatusReserved1;/* '<S2>/FallbackInfo' */
  SystemStatusReserved2
    OutportBufferForFallbackSystemStatus_SystemStatusReserved2;/* '<S2>/FallbackInfo' */
  SystemStatusReserved3
    OutportBufferForFallbackSystemStatus_SystemStatusReserved3;/* '<S2>/FallbackInfo' */
  FallbackDebugInfoReserve1
    OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve1;/* '<S2>/FallbackInfo' */
  FallbackDebugInfoReserve5
    OutportBufferForFallbackDebugInfo_FallbackDebugInfoReserve5;/* '<S2>/FallbackInfo' */
  SystemStatusReserved4
    OutportBufferForFallbackSystemStatus_SystemStatusReserved4;/* '<S2>/FallbackInfo' */
  BootLoaderVersion OutportBufferForFallbackSystemStatus_BootLoaderVersion;/* '<S2>/FallbackInfo' */
  BswSoftwarewareVersion
    OutportBufferForFallbackSystemStatus_BswSoftwarewareVersion;/* '<S2>/FallbackInfo' */
  FrontCameraFailureStatus
    OutportBufferForFallbackSystemStatus_FrontCameraFailureStatus;/* '<S2>/FallbackInfo' */
  FrontRadarFailureStatus
    OutportBufferForFallbackSystemStatus_FrontRadarFailureStatus;/* '<S2>/FallbackInfo' */
} B_FallbackSigOutput_T;

/* Block states (default storage) for system '<Root>' */
typedef struct tag_DW_FallbackSigOutput_T {
  float32 UnitDelay_DSTATE;            /* '<S11>/Unit Delay' */
  float32 UnitDelay_DSTATE_k;          /* '<S21>/Unit Delay' */
  float32 UnitDelay_DSTATE_l;          /* '<S20>/Unit Delay' */
  float32 UnitDelay_DSTATE_ki;         /* '<S19>/Unit Delay' */
  float32 UnitDelay_DSTATE_c;          /* '<S40>/Unit Delay' */
  float32 UnitDelay_DSTATE_i;          /* '<S41>/Unit Delay' */
  float32 UnitDelay_DSTATE_f;          /* '<S38>/Unit Delay' */
  float32 UnitDelay_DSTATE_kp;         /* '<S39>/Unit Delay' */
  float32 UnitDelay1_DSTATE[2];        /* '<S49>/Unit Delay1' */
  float32 UnitDelay_DSTATE_n;          /* '<S61>/Unit Delay' */
  sint32 CAN_Pack_YRS2_ModeSignalID;   /* '<S114>/CAN_Pack_YRS2' */
  sint32 CAN_Pack_YRS1_ModeSignalID;   /* '<S107>/CAN_Pack_YRS1' */
  sint32 CAN_Pack_SAS_Status_ModeSignalID;/* '<S100>/CAN_Pack_SAS_Status' */
  sint32 CAN_Pack_ESC_Status_ModeSignalID;/* '<S94>/CAN_Pack_ESC_Status' */
  sint32 CAN_Pack_FrontWheelSpeedKPH_ModeSignalID;/* '<S88>/CAN_Pack_FrontWheelSpeedKPH' */
  sint32 CAN_Pack_FrontWheelSpeedKPH_ModeSignalID_g;/* '<S84>/CAN_Pack_FrontWheelSpeedKPH' */
  sint32 CAN_Pack_ESC_DA_MESSAGE_ModeSignalID;/* '<S78>/CAN_Pack_ESC_DA_MESSAGE' */
  uint8 UnitDelay1_DSTATE_h;           /* '<S10>/Unit Delay1' */
  uint8 Output_DSTATE;                 /* '<S50>/Output' */
  uint8 UnitDelay_DSTATE_ls[2];        /* '<S49>/Unit Delay' */
  uint8 Output_DSTATE_l;               /* '<S53>/Output' */
  uint8 Output_DSTATE_lj;              /* '<S60>/Output' */
  uint8 Output_DSTATE_p;               /* '<S116>/Output' */
  uint8 Output_DSTATE_c;               /* '<S110>/Output' */
  uint8 Output_DSTATE_n;               /* '<S102>/Output' */
  uint8 Output_DSTATE_j;               /* '<S96>/Output' */
  uint8 Output_DSTATE_i;               /* '<S91>/Output' */
  uint8 Output_DSTATE_a;               /* '<S85>/Output' */
  uint8 Output_DSTATE_b;               /* '<S79>/Output' */
  boolean UnitDelay_DSTATE_h;          /* '<S6>/Unit Delay' */
  boolean UnitDelay_DSTATE_iy;         /* '<S24>/Unit Delay' */
  boolean UnitDelay1_DSTATE_k;         /* '<S36>/Unit Delay1' */
  boolean UnitDelay1_DSTATE_c;         /* '<S35>/Unit Delay1' */
  boolean UnitDelay1_DSTATE_d;         /* '<S37>/Unit Delay1' */
  boolean UnitDelay_DSTATE_d;          /* '<S44>/Unit Delay' */
  boolean UnitDelay_DSTATE_et;         /* '<S45>/Unit Delay' */
  boolean UnitDelay_DSTATE_a;          /* '<S10>/Unit Delay' */
  boolean UnitDelay1_DSTATE_o;         /* '<S33>/Unit Delay1' */
  boolean UnitDelay1_DSTATE_l;         /* '<S34>/Unit Delay1' */
  boolean UnitDelay_DSTATE_o;          /* '<S42>/Unit Delay' */
  boolean UnitDelay_DSTATE_ey;         /* '<S43>/Unit Delay' */
  boolean UnitDelay_DSTATE_eyn;        /* '<S64>/Unit Delay' */
  uint8 TimeCntr_e;                    /* '<S75>/TaskForRadar' */
} DW_FallbackSigOutput_T;

/* Invariant block signals (default storage) */
typedef struct {
  const uint8 Width;                   /* '<S65>/Width' */
  const uint8 Width_p;                 /* '<S59>/Width' */
} ConstB_FallbackSigOutput_T;

/* Parameters (default storage) */
struct P_FallbackSigOutput_T_ {
  float32 EAD_AccFilterTime_P;         /* Variable: EAD_AccFilterTime_P
                                        * Referenced by: '<S17>/Parameter6'
                                        */
  uint16 BitwiseOperator1_BitMask;   /* Mask Parameter: BitwiseOperator1_BitMask
                                      * Referenced by: '<S8>/Bitwise Operator1'
                                      */
  uint16 BitwiseOperator5_BitMask;   /* Mask Parameter: BitwiseOperator5_BitMask
                                      * Referenced by: '<S8>/Bitwise Operator5'
                                      */
  uint8 CompareToConstant_const;      /* Mask Parameter: CompareToConstant_const
                                       * Referenced by: '<S101>/Constant'
                                       */
  uint8 CompareToConstant_const_m;  /* Mask Parameter: CompareToConstant_const_m
                                     * Referenced by: '<S108>/Constant'
                                     */
  uint8 CompareToConstant1_const;    /* Mask Parameter: CompareToConstant1_const
                                      * Referenced by: '<S109>/Constant'
                                      */
  uint8 CompareToConstant_const_n;  /* Mask Parameter: CompareToConstant_const_n
                                     * Referenced by: '<S115>/Constant'
                                     */
  uint8 CompareToConstant9_const;    /* Mask Parameter: CompareToConstant9_const
                                      * Referenced by: '<S32>/Constant'
                                      */
  uint8 CompareToConstant10_const;  /* Mask Parameter: CompareToConstant10_const
                                     * Referenced by: '<S30>/Constant'
                                     */
  uint8 CompareToConstant8_const;    /* Mask Parameter: CompareToConstant8_const
                                      * Referenced by: '<S31>/Constant'
                                      */
  uint8 CounterLimited1_uplimit;      /* Mask Parameter: CounterLimited1_uplimit
                                       * Referenced by: '<S81>/FixPt Switch'
                                       */
  uint8 CounterLimited1_uplimit_c;  /* Mask Parameter: CounterLimited1_uplimit_c
                                     * Referenced by: '<S87>/FixPt Switch'
                                     */
  uint8 CounterLimited1_uplimit_f;  /* Mask Parameter: CounterLimited1_uplimit_f
                                     * Referenced by: '<S93>/FixPt Switch'
                                     */
  uint8 CounterLimited2_uplimit;      /* Mask Parameter: CounterLimited2_uplimit
                                       * Referenced by: '<S98>/FixPt Switch'
                                       */
  uint8 CounterLimited6_uplimit;      /* Mask Parameter: CounterLimited6_uplimit
                                       * Referenced by: '<S104>/FixPt Switch'
                                       */
  uint8 CounterLimited10_uplimit;    /* Mask Parameter: CounterLimited10_uplimit
                                      * Referenced by: '<S112>/FixPt Switch'
                                      */
  uint8 CounterLimited9_uplimit;      /* Mask Parameter: CounterLimited9_uplimit
                                       * Referenced by: '<S118>/FixPt Switch'
                                       */
  uint8 CounterLimited2_uplimit_j;  /* Mask Parameter: CounterLimited2_uplimit_j
                                     * Referenced by: '<S52>/FixPt Switch'
                                     */
  uint8 CounterLimited1_uplimit_d;  /* Mask Parameter: CounterLimited1_uplimit_d
                                     * Referenced by: '<S55>/FixPt Switch'
                                     */
  uint8 CounterLimited1_uplimit_fk;/* Mask Parameter: CounterLimited1_uplimit_fk
                                    * Referenced by: '<S63>/FixPt Switch'
                                    */
  FBS_DebugInfo_Struct FBS_DebugInfo_Y0;/* Computed Parameter: FBS_DebugInfo_Y0
                                         * Referenced by: '<S2>/FBS_DebugInfo'
                                         */
  SG_AdNomALgtReqGroupSafe VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe_Y0;
               /* Computed Parameter: VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe_Y0
                * Referenced by: '<S2>/VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe'
                */
  SG_SecAdNomALgtReqGroupSafe VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe_Y0;
         /* Computed Parameter: VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe_Y0
          * Referenced by: '<S2>/VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe'
          */
  SG_AdDirReq VIMMid3CanFr09_SG_AdDirReq_Y0;
                            /* Computed Parameter: VIMMid3CanFr09_SG_AdDirReq_Y0
                             * Referenced by: '<S2>/VIMMid3CanFr09_SG_AdDirReq'
                             */
  SG_AdPrimALgtLimReqGroupSafe VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe_Y0;
           /* Computed Parameter: VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe_Y0
            * Referenced by: '<S2>/VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe'
            */
  SG_AdPrimWhlAgReqGroupSafe VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe_Y0;
           /* Computed Parameter: VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe_Y0
            * Referenced by: '<S2>/VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe'
            */
  SG_AdSecALgtLimReqGroupSafe VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe_Y0;
         /* Computed Parameter: VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe_Y0
          * Referenced by: '<S2>/VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe'
          */
  SG_AdSecWhlAgReqGroupSafe VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe_Y0;
           /* Computed Parameter: VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe_Y0
            * Referenced by: '<S2>/VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe'
            */
  SG_AdStandStillReq VIMMid3CanFr09_SG_AdStandStillReq_Y0;
                     /* Computed Parameter: VIMMid3CanFr09_SG_AdStandStillReq_Y0
                      * Referenced by: '<S2>/VIMMid3CanFr09_SG_AdStandStillReq'
                      */
  SG_AdWhlLockReq VIMMid3CanFr13_SG_AdWhlLockReq_Y0;
                        /* Computed Parameter: VIMMid3CanFr13_SG_AdWhlLockReq_Y0
                         * Referenced by: '<S2>/VIMMid3CanFr13_SG_AdWhlLockReq'
                         */
  SG_AdpLiReqFromAPI VIMMid3CanFr11_SG_AdpLiReqFromAPI_Y0;
                     /* Computed Parameter: VIMMid3CanFr11_SG_AdpLiReqFromAPI_Y0
                      * Referenced by: '<S2>/VIMMid3CanFr11_SG_AdpLiReqFromAPI'
                      */
  SG_AutnmsDrvStReq VIMMid3CanFr07_SG_AutnmsDrvStReq_Y0;
                      /* Computed Parameter: VIMMid3CanFr07_SG_AutnmsDrvStReq_Y0
                       * Referenced by: '<S2>/VIMMid3CanFr07_SG_AutnmsDrvStReq'
                       */
  SG_HmiAutnmsSts VIMMid3CanFr04_SG_HmiAutnmsSts_Y0;
                        /* Computed Parameter: VIMMid3CanFr04_SG_HmiAutnmsSts_Y0
                         * Referenced by: '<S2>/VIMMid3CanFr04_SG_HmiAutnmsSts'
                         */
  SG_SecAdWhlLockReq VIMBMid6CanFdFr29_SG_SecAdWhlLockReq_Y0;
                  /* Computed Parameter: VIMBMid6CanFdFr29_SG_SecAdWhlLockReq_Y0
                   * Referenced by: '<S2>/VIMBMid6CanFdFr29_SG_SecAdWhlLockReq'
                   */
  SG_VehOperStReq VIMMid3CanFr08_SG_VehOperStReq_Y0;
                        /* Computed Parameter: VIMMid3CanFr08_SG_VehOperStReq_Y0
                         * Referenced by: '<S2>/VIMMid3CanFr08_SG_VehOperStReq'
                         */
  CarTiGlb_A VCU1Mid3CanFr06_CarTiGlb_A_Y0;
                            /* Computed Parameter: VCU1Mid3CanFr06_CarTiGlb_A_Y0
                             * Referenced by: '<S2>/VCU1Mid3CanFr06_CarTiGlb_A'
                             */
  TrajectoryCurvatureChange FallbackDebugInfo_TrajectoryCurvatureChange_Y0;
           /* Computed Parameter: FallbackDebugInfo_TrajectoryCurvatureChange_Y0
            * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryCurvatureChange'
            */
  float32 Constant6_Value;             /* Computed Parameter: Constant6_Value
                                        * Referenced by: '<S8>/Constant6'
                                        */
  float32 Constant4_Value;             /* Computed Parameter: Constant4_Value
                                        * Referenced by: '<S8>/Constant4'
                                        */
  float32 Constant49_Value;            /* Computed Parameter: Constant49_Value
                                        * Referenced by: '<S8>/Constant49'
                                        */
  float32 Constant51_Value;            /* Computed Parameter: Constant51_Value
                                        * Referenced by: '<S8>/Constant51'
                                        */
  float32 Constant13_Value;            /* Computed Parameter: Constant13_Value
                                        * Referenced by: '<S8>/Constant13'
                                        */
  float32 Constant14_Value;            /* Computed Parameter: Constant14_Value
                                        * Referenced by: '<S8>/Constant14'
                                        */
  float32 Constant16_Value;            /* Computed Parameter: Constant16_Value
                                        * Referenced by: '<S8>/Constant16'
                                        */
  float32 Constant_Value;              /* Computed Parameter: Constant_Value
                                        * Referenced by: '<S17>/Constant'
                                        */
  float32 IAM_Ts_P1_Value;             /* Computed Parameter: IAM_Ts_P1_Value
                                        * Referenced by: '<S20>/IAM_Ts_P1'
                                        */
  float32 Constant8_Value;             /* Computed Parameter: Constant8_Value
                                        * Referenced by: '<S17>/Constant8'
                                        */
  float32 Constant6_Value_c;           /* Computed Parameter: Constant6_Value_c
                                        * Referenced by: '<S17>/Constant6'
                                        */
  float32 Constant7_Value;             /* Computed Parameter: Constant7_Value
                                        * Referenced by: '<S17>/Constant7'
                                        */
  float32 Constant14_Value_l;          /* Computed Parameter: Constant14_Value_l
                                        * Referenced by: '<S17>/Constant14'
                                        */
  float32 Constant11_Value;            /* Computed Parameter: Constant11_Value
                                        * Referenced by: '<S17>/Constant11'
                                        */
  float32 Constant4_Value_b;           /* Computed Parameter: Constant4_Value_b
                                        * Referenced by: '<S17>/Constant4'
                                        */
  float32 Constant3_Value;             /* Computed Parameter: Constant3_Value
                                        * Referenced by: '<S17>/Constant3'
                                        */
  float32 Constant10_Value;            /* Computed Parameter: Constant10_Value
                                        * Referenced by: '<S17>/Constant10'
                                        */
  float32 Constant19_Value;            /* Computed Parameter: Constant19_Value
                                        * Referenced by: '<S17>/Constant19'
                                        */
  float32 Constant20_Value;            /* Computed Parameter: Constant20_Value
                                        * Referenced by: '<S17>/Constant20'
                                        */
  float32 IAM_Ts_P2_Value;             /* Computed Parameter: IAM_Ts_P2_Value
                                        * Referenced by: '<S21>/IAM_Ts_P2'
                                        */
  float32 Constant17_Value;            /* Computed Parameter: Constant17_Value
                                        * Referenced by: '<S18>/Constant17'
                                        */
  float32 Constant1_Value;             /* Computed Parameter: Constant1_Value
                                        * Referenced by: '<S18>/Constant1'
                                        */
  float32 Constant3_Value_c;           /* Computed Parameter: Constant3_Value_c
                                        * Referenced by: '<S18>/Constant3'
                                        */
  float32 Constant2_Value;             /* Computed Parameter: Constant2_Value
                                        * Referenced by: '<S18>/Constant2'
                                        */
  float32 Constant5_Value;             /* Computed Parameter: Constant5_Value
                                        * Referenced by: '<S18>/Constant5'
                                        */
  float32 Constant4_Value_j;           /* Computed Parameter: Constant4_Value_j
                                        * Referenced by: '<S18>/Constant4'
                                        */
  float32 Constant9_Value;             /* Computed Parameter: Constant9_Value
                                        * Referenced by: '<S18>/Constant9'
                                        */
  float32 Constant6_Value_h;           /* Computed Parameter: Constant6_Value_h
                                        * Referenced by: '<S18>/Constant6'
                                        */
  float32 Constant7_Value_m;           /* Computed Parameter: Constant7_Value_m
                                        * Referenced by: '<S18>/Constant7'
                                        */
  float32 Constant45_Value;            /* Computed Parameter: Constant45_Value
                                        * Referenced by: '<S16>/Constant45'
                                        */
  float32 Constant47_Value;            /* Computed Parameter: Constant47_Value
                                        * Referenced by: '<S16>/Constant47'
                                        */
  float32 Constant53_Value;            /* Computed Parameter: Constant53_Value
                                        * Referenced by: '<S16>/Constant53'
                                        */
  float32 Constant57_Value;            /* Computed Parameter: Constant57_Value
                                        * Referenced by: '<S16>/Constant57'
                                        */
  float32 Constant59_Value;            /* Computed Parameter: Constant59_Value
                                        * Referenced by: '<S16>/Constant59'
                                        */
  float32 IAM_Ts_P2_Value_m;           /* Computed Parameter: IAM_Ts_P2_Value_m
                                        * Referenced by: '<S38>/IAM_Ts_P2'
                                        */
  float32 IAM_Ts_P2_Value_e;           /* Computed Parameter: IAM_Ts_P2_Value_e
                                        * Referenced by: '<S39>/IAM_Ts_P2'
                                        */
  float32 IAM_Ts_P2_Value_c;           /* Computed Parameter: IAM_Ts_P2_Value_c
                                        * Referenced by: '<S40>/IAM_Ts_P2'
                                        */
  float32 IAM_Ts_P2_Value_b;           /* Computed Parameter: IAM_Ts_P2_Value_b
                                        * Referenced by: '<S41>/IAM_Ts_P2'
                                        */
  float32 Constant10_Value_h;          /* Computed Parameter: Constant10_Value_h
                                        * Referenced by: '<S57>/Constant10'
                                        */
  float32 Constant19_Value_m;          /* Computed Parameter: Constant19_Value_m
                                        * Referenced by: '<S57>/Constant19'
                                        */
  float32 Constant20_Value_h;          /* Computed Parameter: Constant20_Value_h
                                        * Referenced by: '<S57>/Constant20'
                                        */
  float32 IAM_Ts_P2_Value_mh;          /* Computed Parameter: IAM_Ts_P2_Value_mh
                                        * Referenced by: '<S61>/IAM_Ts_P2'
                                        */
  float32 Constant1_Value_d;           /* Computed Parameter: Constant1_Value_d
                                        * Referenced by: '<S82>/Constant1'
                                        */
  float32 Constant1_Value_j;           /* Computed Parameter: Constant1_Value_j
                                        * Referenced by: '<S83>/Constant1'
                                        */
  float32 VTS_WheelSpeedFrntRi_Y0;/* Computed Parameter: VTS_WheelSpeedFrntRi_Y0
                                   * Referenced by: '<S71>/VTS_WheelSpeedFrntRi'
                                   */
  float32 VTS_WheelSpeedFrntLe_Y0;/* Computed Parameter: VTS_WheelSpeedFrntLe_Y0
                                   * Referenced by: '<S71>/VTS_WheelSpeedFrntLe'
                                   */
  float32 Constant_Value_g;            /* Computed Parameter: Constant_Value_g
                                        * Referenced by: '<S82>/Constant'
                                        */
  float32 Constant3_Value_f;           /* Computed Parameter: Constant3_Value_f
                                        * Referenced by: '<S82>/Constant3'
                                        */
  float32 Constant_Value_k;            /* Computed Parameter: Constant_Value_k
                                        * Referenced by: '<S83>/Constant'
                                        */
  float32 Constant3_Value_d;           /* Computed Parameter: Constant3_Value_d
                                        * Referenced by: '<S83>/Constant3'
                                        */
  float32 Constant1_Value_p;           /* Computed Parameter: Constant1_Value_p
                                        * Referenced by: '<S89>/Constant1'
                                        */
  float32 Constant1_Value_g;           /* Computed Parameter: Constant1_Value_g
                                        * Referenced by: '<S90>/Constant1'
                                        */
  float32 VTS_WheelSpeedReRi_Y0;    /* Computed Parameter: VTS_WheelSpeedReRi_Y0
                                     * Referenced by: '<S72>/VTS_WheelSpeedReRi'
                                     */
  float32 VTS_WheelSpeedReLe_Y0;    /* Computed Parameter: VTS_WheelSpeedReLe_Y0
                                     * Referenced by: '<S72>/VTS_WheelSpeedReLe'
                                     */
  float32 Constant3_Value_p;           /* Computed Parameter: Constant3_Value_p
                                        * Referenced by: '<S89>/Constant3'
                                        */
  float32 Constant_Value_f;            /* Computed Parameter: Constant_Value_f
                                        * Referenced by: '<S89>/Constant'
                                        */
  float32 Constant3_Value_n;           /* Computed Parameter: Constant3_Value_n
                                        * Referenced by: '<S90>/Constant3'
                                        */
  float32 Constant_Value_d;            /* Computed Parameter: Constant_Value_d
                                        * Referenced by: '<S90>/Constant'
                                        */
  float32 VTS_VehicleSpeed_Y0;        /* Computed Parameter: VTS_VehicleSpeed_Y0
                                       * Referenced by: '<S73>/VTS_VehicleSpeed'
                                       */
  float32 Constant_Value_a;            /* Computed Parameter: Constant_Value_a
                                        * Referenced by: '<S95>/Constant'
                                        */
  float32 VTS_SteerWheelRotSpd_Y0;/* Computed Parameter: VTS_SteerWheelRotSpd_Y0
                                   * Referenced by: '<S74>/VTS_SteerWheelRotSpd'
                                   */
  float32 VTS_SteerWheelAngle_Y0;  /* Computed Parameter: VTS_SteerWheelAngle_Y0
                                    * Referenced by: '<S74>/VTS_SteerWheelAngle'
                                    */
  float32 Constant1_Value_jk;          /* Computed Parameter: Constant1_Value_jk
                                        * Referenced by: '<S99>/Constant1'
                                        */
  float32 Constant2_Value_f;           /* Computed Parameter: Constant2_Value_f
                                        * Referenced by: '<S99>/Constant2'
                                        */
  float32 VTS_YawRate_Y0;              /* Computed Parameter: VTS_YawRate_Y0
                                        * Referenced by: '<S76>/VTS_YawRate'
                                        */
  float32 VTS_LateralAcce_Y0;          /* Computed Parameter: VTS_LateralAcce_Y0
                                        * Referenced by: '<S76>/VTS_LateralAcce'
                                        */
  float32 Constant1_Value_l;           /* Computed Parameter: Constant1_Value_l
                                        * Referenced by: '<S106>/Constant1'
                                        */
  float32 Constant3_Value_c3;          /* Computed Parameter: Constant3_Value_c3
                                        * Referenced by: '<S106>/Constant3'
                                        */
  float32 VTS_LongitAcce_Y0;           /* Computed Parameter: VTS_LongitAcce_Y0
                                        * Referenced by: '<S77>/VTS_LongitAcce'
                                        */
  float32 Constant1_Value_ji;          /* Computed Parameter: Constant1_Value_ji
                                        * Referenced by: '<S113>/Constant1'
                                        */
  float32 Constant10_Value_hs;        /* Computed Parameter: Constant10_Value_hs
                                       * Referenced by: '<S8>/Constant10'
                                       */
  float32 uDLookupTable_bp01Data[10];
                              /* Expression: single([0 2 3 8 10 20 30 40 50 60])
                               * Referenced by: '<S8>/1-D Lookup Table'
                               */
  float32 Constant1_Value_m;           /* Computed Parameter: Constant1_Value_m
                                        * Referenced by: '<S8>/Constant1'
                                        */
  float32 Constant2_Value_d;           /* Computed Parameter: Constant2_Value_d
                                        * Referenced by: '<S8>/Constant2'
                                        */
  float32 Constant3_Value_dk;          /* Computed Parameter: Constant3_Value_dk
                                        * Referenced by: '<S8>/Constant3'
                                        */
  float32 Constant48_Value;            /* Computed Parameter: Constant48_Value
                                        * Referenced by: '<S8>/Constant48'
                                        */
  float32 Constant5_Value_c;           /* Computed Parameter: Constant5_Value_c
                                        * Referenced by: '<S8>/Constant5'
                                        */
  float32 Constant50_Value;            /* Computed Parameter: Constant50_Value
                                        * Referenced by: '<S8>/Constant50'
                                        */
  float32 UnitDelay_InitialCondition;
                               /* Computed Parameter: UnitDelay_InitialCondition
                                * Referenced by: '<S11>/Unit Delay'
                                */
  float32 Constant18_Value;            /* Computed Parameter: Constant18_Value
                                        * Referenced by: '<S17>/Constant18'
                                        */
  float32 UnitDelay_InitialCondition_f;
                             /* Computed Parameter: UnitDelay_InitialCondition_f
                              * Referenced by: '<S21>/Unit Delay'
                              */
  float32 IAM_Ts_P4_Value;             /* Computed Parameter: IAM_Ts_P4_Value
                                        * Referenced by: '<S20>/IAM_Ts_P4'
                                        */
  float32 UnitDelay_InitialCondition_e;
                             /* Computed Parameter: UnitDelay_InitialCondition_e
                              * Referenced by: '<S20>/Unit Delay'
                              */
  float32 UnitDelay_InitialCondition_g;
                             /* Computed Parameter: UnitDelay_InitialCondition_g
                              * Referenced by: '<S19>/Unit Delay'
                              */
  float32 Constant52_Value;            /* Computed Parameter: Constant52_Value
                                        * Referenced by: '<S16>/Constant52'
                                        */
  float32 Constant2_Value_g;           /* Computed Parameter: Constant2_Value_g
                                        * Referenced by: '<S16>/Constant2'
                                        */
  float32 Constant58_Value;            /* Computed Parameter: Constant58_Value
                                        * Referenced by: '<S16>/Constant58'
                                        */
  float32 Constant3_Value_b;           /* Computed Parameter: Constant3_Value_b
                                        * Referenced by: '<S16>/Constant3'
                                        */
  float32 Constant56_Value;            /* Computed Parameter: Constant56_Value
                                        * Referenced by: '<S16>/Constant56'
                                        */
  float32 Constant46_Value;            /* Computed Parameter: Constant46_Value
                                        * Referenced by: '<S16>/Constant46'
                                        */
  float32 Constant7_Value_l;           /* Computed Parameter: Constant7_Value_l
                                        * Referenced by: '<S16>/Constant7'
                                        */
  float32 Constant44_Value;            /* Computed Parameter: Constant44_Value
                                        * Referenced by: '<S16>/Constant44'
                                        */
  float32 Constant8_Value_g;           /* Computed Parameter: Constant8_Value_g
                                        * Referenced by: '<S16>/Constant8'
                                        */
  float32 Constant5_Value_h;           /* Computed Parameter: Constant5_Value_h
                                        * Referenced by: '<S10>/Constant5'
                                        */
  float32 UnitDelay_InitialCondition_e5;
                            /* Computed Parameter: UnitDelay_InitialCondition_e5
                             * Referenced by: '<S40>/Unit Delay'
                             */
  float32 UnitDelay_InitialCondition_p;
                             /* Computed Parameter: UnitDelay_InitialCondition_p
                              * Referenced by: '<S41>/Unit Delay'
                              */
  float32 UnitDelay_InitialCondition_eo;
                            /* Computed Parameter: UnitDelay_InitialCondition_eo
                             * Referenced by: '<S38>/Unit Delay'
                             */
  float32 UnitDelay_InitialCondition_i;
                             /* Computed Parameter: UnitDelay_InitialCondition_i
                              * Referenced by: '<S39>/Unit Delay'
                              */
  float32 Constant24_Value;            /* Computed Parameter: Constant24_Value
                                        * Referenced by: '<S47>/Constant24'
                                        */
  float32 UnitDelay1_InitialCondition;
                              /* Computed Parameter: UnitDelay1_InitialCondition
                               * Referenced by: '<S49>/Unit Delay1'
                               */
  float32 Constant19_Value_h;          /* Computed Parameter: Constant19_Value_h
                                        * Referenced by: '<S49>/Constant19'
                                        */
  float32 IAM_Ts_P2_Value_j;           /* Computed Parameter: IAM_Ts_P2_Value_j
                                        * Referenced by: '<S49>/IAM_Ts_P2'
                                        */
  float32 Constant23_Value;            /* Computed Parameter: Constant23_Value
                                        * Referenced by: '<S49>/Constant23'
                                        */
  float32 Constant18_Value_c;          /* Computed Parameter: Constant18_Value_c
                                        * Referenced by: '<S57>/Constant18'
                                        */
  float32 UnitDelay_InitialCondition_fd;
                            /* Computed Parameter: UnitDelay_InitialCondition_fd
                             * Referenced by: '<S61>/Unit Delay'
                             */
  uint32 Constant20_Value_j;           /* Computed Parameter: Constant20_Value_j
                                        * Referenced by: '<S49>/Constant20'
                                        */
  AccRequestBySpeed FallbackDebugInfo_AccRequestBySpeed_Y0;
                   /* Computed Parameter: FallbackDebugInfo_AccRequestBySpeed_Y0
                    * Referenced by: '<S2>/FallbackDebugInfo_AccRequestBySpeed'
                    */
  AccRequestByOutOfOdd FallbackDebugInfo_AccRequestByOutOfOdd_Y0;
                /* Computed Parameter: FallbackDebugInfo_AccRequestByOutOfOdd_Y0
                 * Referenced by: '<S2>/FallbackDebugInfo_AccRequestByOutOfOdd'
                 */
  AccRequestForSystemError FallbackDebugInfo_AccRequestForSystemError_Y0;
            /* Computed Parameter: FallbackDebugInfo_AccRequestForSystemError_Y0
             * Referenced by: '<S2>/FallbackDebugInfo_AccRequestForSystemError'
             */
  AccRequestAfterRateLimit FallbackDebugInfo_AccRequestAfterRateLimit_Y0;
            /* Computed Parameter: FallbackDebugInfo_AccRequestAfterRateLimit_Y0
             * Referenced by: '<S2>/FallbackDebugInfo_AccRequestAfterRateLimit'
             */
  AcuMid3SsmCounter0MessageID
    FallbackSystemStatus_AcuMid3SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0MessageID'
       */
  AcuFbCanMessageID FallbackSystemStatus_AcuFbCanMessageID_Y0;
                /* Computed Parameter: FallbackSystemStatus_AcuFbCanMessageID_Y0
                 * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanMessageID'
                 */
  AcuMid3SsmCounter0Timer FallbackSystemStatus_AcuMid3SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timer'
           */
  AcuMid3SsmCounter1MessageID
    FallbackSystemStatus_AcuMid3SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1MessageID'
       */
  AcuMid3SsmCounter1Timer FallbackSystemStatus_AcuMid3SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timer'
           */
  AcuMid5SsmCounter0MessageID
    FallbackSystemStatus_AcuMid5SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0MessageID'
       */
  AcuMid5SsmCounter0Timer FallbackSystemStatus_AcuMid5SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timer'
           */
  AcuMid5SsmCounter1MessageID
    FallbackSystemStatus_AcuMid5SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1MessageID'
       */
  AcuMid5SsmCounter1Timer FallbackSystemStatus_AcuMid5SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timer'
           */
  AcuMid6SsmCounter1Timer FallbackSystemStatus_AcuMid6SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timer'
           */
  AcuFbCanTimer FallbackSystemStatus_AcuFbCanTimer_Y0;
                    /* Computed Parameter: FallbackSystemStatus_AcuFbCanTimer_Y0
                     * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanTimer'
                     */
  AcuMid6SsmCounter0MessageID
    FallbackSystemStatus_AcuMid6SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0MessageID'
       */
  AcuMid6SsmCounter0Timer FallbackSystemStatus_AcuMid6SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timer'
           */
  AcuMid6SsmCounter1MessageID
    FallbackSystemStatus_AcuMid6SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1MessageID'
       */
  AdSetSpd VIMMid3CanFr13_AdSetSpd_Y0;
                               /* Computed Parameter: VIMMid3CanFr13_AdSetSpd_Y0
                                * Referenced by: '<S2>/VIMMid3CanFr13_AdSetSpd'
                                */
  ESC_FRWheelSpeedKPH ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH_Y0;
            /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH_Y0
             * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH'
             */
  ESC_FLWheelSpeedKPH ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write_Y0;
      /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write_Y0
       * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH_write'
       */
  ESC_RRWheelSpeedKPH ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH_Y0;
             /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH_Y0
              * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH'
              */
  ESC_RLWheelSpeedKPH ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH_Y0;
             /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH_Y0
              * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH'
              */
  ESC_VehicleSpeed ESC_Status_ESC_VehicleSpeed_Y0;
                           /* Computed Parameter: ESC_Status_ESC_VehicleSpeed_Y0
                            * Referenced by: '<S2>/ESC_Status_ESC_VehicleSpeed'
                            */
  EgoLaneWidth FallbackDebugInfo_EgoLaneWidth_Y0;
                        /* Computed Parameter: FallbackDebugInfo_EgoLaneWidth_Y0
                         * Referenced by: '<S2>/FallbackDebugInfo_EgoLaneWidth'
                         */
  EgoStopTime FallbackDebugInfo_EgoStopTime_Y0;
                         /* Computed Parameter: FallbackDebugInfo_EgoStopTime_Y0
                          * Referenced by: '<S2>/FallbackDebugInfo_EgoStopTime'
                          */
  EmergencyBrakeAcc FallbackDebugInfo_EmergencyBrakeAcc_Y0;
                   /* Computed Parameter: FallbackDebugInfo_EmergencyBrakeAcc_Y0
                    * Referenced by: '<S2>/FallbackDebugInfo_EmergencyBrakeAcc'
                    */
  FallbackSelfCheckStatus FbAcuAvailable_FallbackSelfCheckStatus_Y0;
                /* Computed Parameter: FbAcuAvailable_FallbackSelfCheckStatus_Y0
                 * Referenced by: '<S2>/FbAcuAvailable_FallbackSelfCheckStatus'
                 */
  FallbackDebugInfoReserve2 FallbackDebugInfo_FallbackDebugInfoReserve2_Y0;
           /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoReserve2_Y0
            * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve2'
            */
  FallbackDebugInfoReserve3 FallbackDebugInfo_FallbackDebugInfoReserve3_Y0;
           /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoReserve3_Y0
            * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoReserve3'
            */
  FeedforwardsSteerAngle FallbackDebugInfo_FeedforwardsSteerAngle_Y0;
              /* Computed Parameter: FallbackDebugInfo_FeedforwardsSteerAngle_Y0
               * Referenced by: '<S2>/FallbackDebugInfo_FeedforwardsSteerAngle'
               */
  FrontCameraCanMessageID FallbackSystemStatus_FrontCameraCanMessageID_Y0;
          /* Computed Parameter: FallbackSystemStatus_FrontCameraCanMessageID_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanMessageID'
           */
  FrontCameraCanTimer FallbackSystemStatus_FrontCameraCanTimer_Y0;
              /* Computed Parameter: FallbackSystemStatus_FrontCameraCanTimer_Y0
               * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanTimer'
               */
  FrontRadarCanMessageID FallbackSystemStatus_FrontRadarCanMessageID_Y0;
           /* Computed Parameter: FallbackSystemStatus_FrontRadarCanMessageID_Y0
            * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanMessageID'
            */
  FrontRadarCanTimer FallbackSystemStatus_FrontRadarCanTimer_Y0;
               /* Computed Parameter: FallbackSystemStatus_FrontRadarCanTimer_Y0
                * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanTimer'
                */
  GradientLimitAccRequest FallbackDebugInfo_GradientLimitAccRequest_Y0;
             /* Computed Parameter: FallbackDebugInfo_GradientLimitAccRequest_Y0
              * Referenced by: '<S2>/FallbackDebugInfo_GradientLimitAccRequest'
              */
  HeadingAngleError FallbackDebugInfo_HeadingAngleError_Y0;
                   /* Computed Parameter: FallbackDebugInfo_HeadingAngleError_Y0
                    * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleError'
                    */
  LateralDistanceError FallbackDebugInfo_LateralDistanceError_Y0;
                /* Computed Parameter: FallbackDebugInfo_LateralDistanceError_Y0
                 * Referenced by: '<S2>/FallbackDebugInfo_LateralDistanceError'
                 */
  LimitAccRequest FallbackDebugInfo_LimitAccRequest_Y0;
                     /* Computed Parameter: FallbackDebugInfo_LimitAccRequest_Y0
                      * Referenced by: '<S2>/FallbackDebugInfo_LimitAccRequest'
                      */
  LimitSteerAngle FallbackDebugInfo_LimitSteerAngle_Y0;
                     /* Computed Parameter: FallbackDebugInfo_LimitSteerAngle_Y0
                      * Referenced by: '<S2>/FallbackDebugInfo_LimitSteerAngle'
                      */
  LongNecAcc FallbackDebugInfo_LongNecAcc_Y0;
                          /* Computed Parameter: FallbackDebugInfo_LongNecAcc_Y0
                           * Referenced by: '<S2>/FallbackDebugInfo_LongNecAcc'
                           */
  LongAccRequest FallbackDebugInfo_LongAccRequest_Y0;
                      /* Computed Parameter: FallbackDebugInfo_LongAccRequest_Y0
                       * Referenced by: '<S2>/FallbackDebugInfo_LongAccRequest'
                       */
  LqrIterationError FallbackDebugInfo_LqrIterationError_Y0;
                   /* Computed Parameter: FallbackDebugInfo_LqrIterationError_Y0
                    * Referenced by: '<S2>/FallbackDebugInfo_LqrIterationError'
                    */
  LqrIterationNums FallbackDebugInfo_LqrIterationNums_Y0;
                    /* Computed Parameter: FallbackDebugInfo_LqrIterationNums_Y0
                     * Referenced by: '<S2>/FallbackDebugInfo_LqrIterationNums'
                     */
  MaxSteerAngleThreshold FallbackDebugInfo_MaxSteerAngleThreshold_Y0;
              /* Computed Parameter: FallbackDebugInfo_MaxSteerAngleThreshold_Y0
               * Referenced by: '<S2>/FallbackDebugInfo_MaxSteerAngleThreshold'
               */
  ObjectStopTime FallbackDebugInfo_ObjectStopTime_Y0;
                      /* Computed Parameter: FallbackDebugInfo_ObjectStopTime_Y0
                       * Referenced by: '<S2>/FallbackDebugInfo_ObjectStopTime'
                       */
  PrimALgtDataRawSafeNom_C SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C_Y0;
          /* Computed Parameter: SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C_Y0
           * Referenced by: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C'
           */
  PrimALatDataRawSafeNom_C SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C_Y0;
        /* Computed Parameter: SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C_Y0
         * Referenced by: '<S2>/SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C'
         */
  PrimVehSpdGroupSafeNom_C SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C_Y0;
          /* Computed Parameter: SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C_Y0
           * Referenced by: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C'
           */
  RawAccRequest FallbackDebugInfo_RawAccRequest_Y0;
                       /* Computed Parameter: FallbackDebugInfo_RawAccRequest_Y0
                        * Referenced by: '<S2>/FallbackDebugInfo_RawAccRequest'
                        */
  RawSteerAngle FallbackDebugInfo_RawSteerAngle_Y0;
                       /* Computed Parameter: FallbackDebugInfo_RawSteerAngle_Y0
                        * Referenced by: '<S2>/FallbackDebugInfo_RawSteerAngle'
                        */
  SAS_SteerWheelAngle SAS_Status_SAS_SteerWheelAngle_Y0;
                        /* Computed Parameter: SAS_Status_SAS_SteerWheelAngle_Y0
                         * Referenced by: '<S2>/SAS_Status_SAS_SteerWheelAngle'
                         */
  SteerAngleByLQR FallbackDebugInfo_SteerAngleByLQR_Y0;
                     /* Computed Parameter: FallbackDebugInfo_SteerAngleByLQR_Y0
                      * Referenced by: '<S2>/FallbackDebugInfo_SteerAngleByLQR'
                      */
  SteerAngleForSystemError FallbackDebugInfo_SteerAngleForSystemError_Y0;
            /* Computed Parameter: FallbackDebugInfo_SteerAngleForSystemError_Y0
             * Referenced by: '<S2>/FallbackDebugInfo_SteerAngleForSystemError'
             */
  SteerAngle FallbackDebugInfo_SteerAngle_Y0;
                          /* Computed Parameter: FallbackDebugInfo_SteerAngle_Y0
                           * Referenced by: '<S2>/FallbackDebugInfo_SteerAngle'
                           */
  TimeToCollison FallbackDebugInfo_TimeToCollison_Y0;
                      /* Computed Parameter: FallbackDebugInfo_TimeToCollison_Y0
                       * Referenced by: '<S2>/FallbackDebugInfo_TimeToCollison'
                       */
  TrajectoryCurvature FallbackDebugInfo_TrajectoryCurvature_Y0;
                 /* Computed Parameter: FallbackDebugInfo_TrajectoryCurvature_Y0
                  * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryCurvature'
                  */
  TrajectoryHeadingAngle FallbackDebugInfo_TrajectoryHeadingAngle_Y0;
              /* Computed Parameter: FallbackDebugInfo_TrajectoryHeadingAngle_Y0
               * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryHeadingAngle'
               */
  TrajectoryLength FallbackDebugInfo_TrajectoryLength_Y0;
                    /* Computed Parameter: FallbackDebugInfo_TrajectoryLength_Y0
                     * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryLength'
                     */
  TrajectoryPosY0 FallbackDebugInfo_TrajectoryPosY0_Y0;
                     /* Computed Parameter: FallbackDebugInfo_TrajectoryPosY0_Y0
                      * Referenced by: '<S2>/FallbackDebugInfo_TrajectoryPosY0'
                      */
  VehMid3SsmCounter0MessageID
    FallbackSystemStatus_VehMid3SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0MessageID'
       */
  VehMid3SsmCounter0Timer FallbackSystemStatus_VehMid3SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timer'
           */
  VehMid3SsmCounter1MessageID
    FallbackSystemStatus_VehMid3SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1MessageID'
       */
  VehMid3SsmCounter1Timer FallbackSystemStatus_VehMid3SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timer'
           */
  VehMid3VcuCounter0MessageID
    FallbackSystemStatus_VehMid3VcuCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0MessageID'
       */
  VehMid3VcuCounter0Timer FallbackSystemStatus_VehMid3VcuCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timer'
           */
  VehMid3VcuCounter1MessageID
    FallbackSystemStatus_VehMid3VcuCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1MessageID'
       */
  VehMid3VcuCounter1Timer FallbackSystemStatus_VehMid3VcuCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timer'
           */
  VehMid5SsmCounter0MessageID
    FallbackSystemStatus_VehMid5SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0MessageID'
       */
  VehMid5SsmCounter0Timer FallbackSystemStatus_VehMid5SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timer'
           */
  VehMid5SsmCounter1MessageID
    FallbackSystemStatus_VehMid5SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1MessageID'
       */
  VehMid5SsmCounter1Timer FallbackSystemStatus_VehMid5SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timer'
           */
  VehMid6SsmCounter0MessageID
    FallbackSystemStatus_VehMid6SsmCounter0MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0MessageID'
       */
  VehMid6SsmCounter0Timer FallbackSystemStatus_VehMid6SsmCounter0Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timer'
           */
  VehMid6SsmCounter1MessageID
    FallbackSystemStatus_VehMid6SsmCounter1MessageID_Y0;
      /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1MessageID_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1MessageID'
       */
  VehMid6SsmCounter1Timer FallbackSystemStatus_VehMid6SsmCounter1Timer_Y0;
          /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1Timer_Y0
           * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timer'
           */
  YRS_LateralAcce YRS1_YRS_LateralAcce_Y0;
                                  /* Computed Parameter: YRS1_YRS_LateralAcce_Y0
                                   * Referenced by: '<S2>/YRS1_YRS_LateralAcce'
                                   */
  YRS_LongitAcce YRS2_YRS_LongitAcce_Y0;
                                   /* Computed Parameter: YRS2_YRS_LongitAcce_Y0
                                    * Referenced by: '<S2>/YRS2_YRS_LongitAcce'
                                    */
  YRS_YawRate YRS1_YRS_YawRate_Y0;    /* Computed Parameter: YRS1_YRS_YawRate_Y0
                                       * Referenced by: '<S2>/YRS1_YRS_YawRate'
                                       */
  YawRate1_C VCU1Mid3CanFr08_1V1R_YawRate1_C_Y0;
                       /* Computed Parameter: VCU1Mid3CanFr08_1V1R_YawRate1_C_Y0
                        * Referenced by: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1_C'
                        */
  uint16 Constant14_Value_l4;         /* Computed Parameter: Constant14_Value_l4
                                       * Referenced by: '<S6>/Constant14'
                                       */
  uint16 Out1_Y0;                      /* Computed Parameter: Out1_Y0
                                        * Referenced by: '<S65>/Out1'
                                        */
  uint16 Constant_Value_l;             /* Computed Parameter: Constant_Value_l
                                        * Referenced by: '<S65>/Constant'
                                        */
  uint16 UnitDelay_InitialCondition_pb;
                            /* Computed Parameter: UnitDelay_InitialCondition_pb
                             * Referenced by: '<S65>/Unit Delay'
                             */
  uint16 Constant_Value_go;            /* Computed Parameter: Constant_Value_go
                                        * Referenced by: '<S47>/Constant'
                                        */
  uint16 Constant1_Value_k;            /* Computed Parameter: Constant1_Value_k
                                        * Referenced by: '<S47>/Constant1'
                                        */
  uint16 Constant2_Value_fd;           /* Computed Parameter: Constant2_Value_fd
                                        * Referenced by: '<S47>/Constant2'
                                        */
  uint16 Constant3_Value_a;            /* Computed Parameter: Constant3_Value_a
                                        * Referenced by: '<S47>/Constant3'
                                        */
  uint16 Constant4_Value_k;            /* Computed Parameter: Constant4_Value_k
                                        * Referenced by: '<S47>/Constant4'
                                        */
  uint16 Constant5_Value_m;            /* Computed Parameter: Constant5_Value_m
                                        * Referenced by: '<S47>/Constant5'
                                        */
  uint16 Constant6_Value_e;            /* Computed Parameter: Constant6_Value_e
                                        * Referenced by: '<S47>/Constant6'
                                        */
  uint16 Constant7_Value_o;            /* Computed Parameter: Constant7_Value_o
                                        * Referenced by: '<S47>/Constant7'
                                        */
  uint16 Constant8_Value_j;            /* Computed Parameter: Constant8_Value_j
                                        * Referenced by: '<S47>/Constant8'
                                        */
  uint16 Constant9_Value_g;            /* Computed Parameter: Constant9_Value_g
                                        * Referenced by: '<S47>/Constant9'
                                        */
  uint16 Constant10_Value_j;           /* Computed Parameter: Constant10_Value_j
                                        * Referenced by: '<S47>/Constant10'
                                        */
  uint16 Constant11_Value_d;           /* Computed Parameter: Constant11_Value_d
                                        * Referenced by: '<S47>/Constant11'
                                        */
  uint16 Constant12_Value;             /* Computed Parameter: Constant12_Value
                                        * Referenced by: '<S47>/Constant12'
                                        */
  uint16 Constant13_Value_m;           /* Computed Parameter: Constant13_Value_m
                                        * Referenced by: '<S47>/Constant13'
                                        */
  uint16 Constant14_Value_e;           /* Expression: 0x141
                                        * Referenced by: '<S47>/Constant14'
                                        */
  uint16 Constant15_Value;             /* Expression: 0x191
                                        * Referenced by: '<S47>/Constant15'
                                        */
  uint16 Constant16_Value_o;           /* Computed Parameter: Constant16_Value_o
                                        * Referenced by: '<S47>/Constant16'
                                        */
  AcuMid3SsmCounter0Timeout FallbackSystemStatus_AcuMid3SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter0Timeout'
         */
  AcuMid3SsmCounter1Timeout FallbackSystemStatus_AcuMid3SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid3SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid3SsmCounter1Timeout'
         */
  AcuMid5SsmCounter0Timeout FallbackSystemStatus_AcuMid5SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter0Timeout'
         */
  AcuMid5SsmCounter1Timeout FallbackSystemStatus_AcuMid5SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid5SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid5SsmCounter1Timeout'
         */
  AcuMid6SsmCounter0Timeout FallbackSystemStatus_AcuMid6SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter0Timeout'
         */
  AcuMid6SsmCounter1Timeout FallbackSystemStatus_AcuMid6SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_AcuMid6SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_AcuMid6SsmCounter1Timeout'
         */
  AcuFbCanTimeout FallbackSystemStatus_AcuFbCanTimeout_Y0;
                  /* Computed Parameter: FallbackSystemStatus_AcuFbCanTimeout_Y0
                   * Referenced by: '<S2>/FallbackSystemStatus_AcuFbCanTimeout'
                   */
  AswSoftwarewareVersion FallbackSystemStatus_AswSoftwarewareVersion_Y0;
           /* Computed Parameter: FallbackSystemStatus_AswSoftwarewareVersion_Y0
            * Referenced by: '<S2>/FallbackSystemStatus_AswSoftwarewareVersion'
            */
  AvoidCollisionEnable FallbackDebugInfo_AvoidCollisionEnable_Y0;
                /* Computed Parameter: FallbackDebugInfo_AvoidCollisionEnable_Y0
                 * Referenced by: '<S2>/FallbackDebugInfo_AvoidCollisionEnable'
                 */
  ESC_ABA_active ESC_DA_MESSAGE_ESC_ABA_active_Y0;
                         /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABA_active_Y0
                          * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABA_active'
                          */
  ESC_ABA_available ESC_DA_MESSAGE_ESC_ABA_available_Y0;
                      /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABA_available_Y0
                       * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABA_available'
                       */
  ESC_ABP_active ESC_DA_MESSAGE_ESC_ABP_active_Y0;
                         /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABP_active_Y0
                          * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABP_active'
                          */
  ESC_ABP_available ESC_DA_MESSAGE_ESC_ABP_available_Y0;
                      /* Computed Parameter: ESC_DA_MESSAGE_ESC_ABP_available_Y0
                       * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_ABP_available'
                       */
  ESC_ABSActive ESC_Status_ESC_ABSActive_Y0;
                              /* Computed Parameter: ESC_Status_ESC_ABSActive_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_ABSActive'
                               */
  ESC_AEB_active ESC_DA_MESSAGE_ESC_AEB_active_Y0;
                         /* Computed Parameter: ESC_DA_MESSAGE_ESC_AEB_active_Y0
                          * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AEB_active'
                          */
  ESC_AEB_available ESC_DA_MESSAGE_ESC_AEB_available_Y0;
                      /* Computed Parameter: ESC_DA_MESSAGE_ESC_AEB_available_Y0
                       * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AEB_available'
                       */
  ESC_AVHStatus ESC_Status_ESC_AVHStatus_Y0;
                              /* Computed Parameter: ESC_Status_ESC_AVHStatus_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_AVHStatus'
                               */
  ESC_AWB_active ESC_DA_MESSAGE_ESC_AWB_active_Y0;
                         /* Computed Parameter: ESC_DA_MESSAGE_ESC_AWB_active_Y0
                          * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AWB_active'
                          */
  ESC_AWB_available ESC_DA_MESSAGE_ESC_AWB_available_Y0;
                      /* Computed Parameter: ESC_DA_MESSAGE_ESC_AWB_available_Y0
                       * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_AWB_available'
                       */
  ESC_BrakePedalSwitchInvalid ESC_Status_ESC_BrakePedalSwitchInvalid_Y0;
                /* Computed Parameter: ESC_Status_ESC_BrakePedalSwitchInvalid_Y0
                 * Referenced by: '<S2>/ESC_Status_ESC_BrakePedalSwitchInvalid'
                 */
  ESC_BrakePedalSwitchStatus ESC_Status_ESC_BrakePedalSwitchStatus_Y0;
                 /* Computed Parameter: ESC_Status_ESC_BrakePedalSwitchStatus_Y0
                  * Referenced by: '<S2>/ESC_Status_ESC_BrakePedalSwitchStatus'
                  */
  ESC_BrakeTempTooHigh ESC_DA_MESSAGE_ESC_BrakeTempTooHigh_Y0;
                   /* Computed Parameter: ESC_DA_MESSAGE_ESC_BrakeTempTooHigh_Y0
                    * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_BrakeTempTooHigh'
                    */
  ESC_DA_MESSAGE_AliveCounter ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter_Y0;
            /* Computed Parameter: ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter_Y0
             * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter'
             */
  ESC_DTC_Active ESC_DA_MESSAGE_ESC_DTC_Active_Y0;
                         /* Computed Parameter: ESC_DA_MESSAGE_ESC_DTC_Active_Y0
                          * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DTC_Active'
                          */
  ESC_DiagExtModSts ESC_DA_MESSAGE_ESC_DiagExtModSts_Y0;
                      /* Computed Parameter: ESC_DA_MESSAGE_ESC_DiagExtModSts_Y0
                       * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DiagExtModSts'
                       */
  ESC_EPBStatus ESC_Status_ESC_EPBStatus_Y0;
                              /* Computed Parameter: ESC_Status_ESC_EPBStatus_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_EPBStatus'
                               */
  ESC_ESPActive ESC_Status_ESC_ESPActive_Y0;
                              /* Computed Parameter: ESC_Status_ESC_ESPActive_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_ESPActive'
                               */
  ESC_ESPFailed ESC_Status_ESC_ESPFailed_Y0;
                              /* Computed Parameter: ESC_Status_ESC_ESPFailed_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_ESPFailed'
                               */
  ESC_FLWheelDirection ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection_Y0;
           /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection_Y0
            * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection'
            */
  ESC_FLWheelSpeedInvalid ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid_Y0;
        /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid_Y0
         * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid'
         */
  ESC_FRWheelDirection ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection_Y0;
           /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection_Y0
            * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection'
            */
  ESC_FRWheelSpeedInvalid ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid_Y0;
        /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid_Y0
         * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid'
         */
  ESC_FrontWheelSpeedsKPH_AliveCounter
    ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter_Y0;
  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter'
   */
  ESC_HHCActive ESC_Status_ESC_HHCActive_Y0;
                              /* Computed Parameter: ESC_Status_ESC_HHCActive_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_HHCActive'
                               */
  ESC_Mcylinder_PressureInvalid
    ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid_Y0;
  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid'
   */
  ESC_NoBrakeForce ESC_DA_MESSAGE_ESC_NoBrakeForce_Y0;
                       /* Computed Parameter: ESC_DA_MESSAGE_ESC_NoBrakeForce_Y0
                        * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_NoBrakeForce'
                        */
  ESC_PATAResponse ESC_Status_ESC_PATAResponse_Y0;
                           /* Computed Parameter: ESC_Status_ESC_PATAResponse_Y0
                            * Referenced by: '<S2>/ESC_Status_ESC_PATAResponse'
                            */
  ESC_QDCFRS ESC_DA_MESSAGE_ESC_QDCFRS_Y0;
                             /* Computed Parameter: ESC_DA_MESSAGE_ESC_QDCFRS_Y0
                              * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_QDCFRS'
                              */
  ESC_RLWheelDirection ESC_RearWheelSpeedKPH_ESC_RLWheelDirection_Y0;
            /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelDirection_Y0
             * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelDirection'
             */
  ESC_RLWheelSpeedInvalid ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid_Y0;
         /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid_Y0
          * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid'
          */
  ESC_RRWheelDirection ESC_RearWheelSpeedKPH_ESC_RRWheelDirection_Y0;
            /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelDirection_Y0
             * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelDirection'
             */
  ESC_RRWheelSpeedInvalid ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid_Y0;
         /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid_Y0
          * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid'
          */
  ESC_RearWheelSpeedsKPH_AliveCounter
    ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter_Y0;
  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter'
   */
  ESC_Status_AliveCounter ESC_Status_ESC_Status_AliveCounter_Y0;
                    /* Computed Parameter: ESC_Status_ESC_Status_AliveCounter_Y0
                     * Referenced by: '<S2>/ESC_Status_ESC_Status_AliveCounter'
                     */
  ESC_TCSActive ESC_Status_ESC_TCSActive_Y0;
                              /* Computed Parameter: ESC_Status_ESC_TCSActive_Y0
                               * Referenced by: '<S2>/ESC_Status_ESC_TCSActive'
                               */
  ESC_VehicleSpeedInvalid ESC_Status_ESC_VehicleSpeedInvalid_Y0;
                    /* Computed Parameter: ESC_Status_ESC_VehicleSpeedInvalid_Y0
                     * Referenced by: '<S2>/ESC_Status_ESC_VehicleSpeedInvalid'
                     */
  ESC_Vehiclestandstill ESC_DA_MESSAGE_ESC_Vehiclestandstill_Y0;
                  /* Computed Parameter: ESC_DA_MESSAGE_ESC_Vehiclestandstill_Y0
                   * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_Vehiclestandstill'
                   */
  FrontCameraCalibrationStatus
    FallbackSystemStatus_FrontCameraCalibrationStatus_Y0;
     /* Computed Parameter: FallbackSystemStatus_FrontCameraCalibrationStatus_Y0
      * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCalibrationStatus'
      */
  FrontRadarCalibrationStatus
    FallbackSystemStatus_FrontRadarCalibrationStatus_Y0;
      /* Computed Parameter: FallbackSystemStatus_FrontRadarCalibrationStatus_Y0
       * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCalibrationStatus'
       */
  FallbackTriggerStatus FallbackDebugInfo_FallbackTriggerStatus_Y0;
               /* Computed Parameter: FallbackDebugInfo_FallbackTriggerStatus_Y0
                * Referenced by: '<S2>/FallbackDebugInfo_FallbackTriggerStatus'
                */
  LateralDistanceErrorWeight FallbackDebugInfo_LateralDistanceErrorWeight_Y0;
          /* Computed Parameter: FallbackDebugInfo_LateralDistanceErrorWeight_Y0
           * Referenced by: '<S2>/FallbackDebugInfo_LateralDistanceErrorWeight'
           */
  HeadingAngleErrorWeight FallbackDebugInfo_HeadingAngleErrorWeight_Y0;
             /* Computed Parameter: FallbackDebugInfo_HeadingAngleErrorWeight_Y0
              * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleErrorWeight'
              */
  LaneValidState FallbackDebugInfo_LaneValidState_Y0;
                      /* Computed Parameter: FallbackDebugInfo_LaneValidState_Y0
                       * Referenced by: '<S2>/FallbackDebugInfo_LaneValidState'
                       */
  LateralContribution FallbackDebugInfo_LateralContribution_Y0;
                 /* Computed Parameter: FallbackDebugInfo_LateralContribution_Y0
                  * Referenced by: '<S2>/FallbackDebugInfo_LateralContribution'
                  */
  HeadingAngleContribution FallbackDebugInfo_HeadingAngleContribution_Y0;
            /* Computed Parameter: FallbackDebugInfo_HeadingAngleContribution_Y0
             * Referenced by: '<S2>/FallbackDebugInfo_HeadingAngleContribution'
             */
  MaxSteerAngleRateThreshold FallbackDebugInfo_MaxSteerAngleRateThreshold_Y0;
          /* Computed Parameter: FallbackDebugInfo_MaxSteerAngleRateThreshold_Y0
           * Referenced by: '<S2>/FallbackDebugInfo_MaxSteerAngleRateThreshold'
           */
  MinAccRate FallbackDebugInfo_MinAccRate_Y0;
                          /* Computed Parameter: FallbackDebugInfo_MinAccRate_Y0
                           * Referenced by: '<S2>/FallbackDebugInfo_MinAccRate'
                           */
  PrimALgtDataRawSafeNomQf_C SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C_Y0;
        /* Computed Parameter: SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C_Y0
         * Referenced by: '<S2>/SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C'
         */
  PrimVehSpdGroupSafeNomQf_C SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C_Y0;
        /* Computed Parameter: SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C_Y0
         * Referenced by: '<S2>/SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C'
         */
  PrpsnTqDirAct_C VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C_Y0;
                  /* Computed Parameter: VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C_Y0
                   * Referenced by: '<S2>/VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C'
                   */
  SAS_CalibrationSts SAS_Status_SAS_CalibrationSts_Y0;
                         /* Computed Parameter: SAS_Status_SAS_CalibrationSts_Y0
                          * Referenced by: '<S2>/SAS_Status_SAS_CalibrationSts'
                          */
  SAS_FailureSts SAS_Status_SAS_FailureSts_Y0;
                             /* Computed Parameter: SAS_Status_SAS_FailureSts_Y0
                              * Referenced by: '<S2>/SAS_Status_SAS_FailureSts'
                              */
  SAS_Status_AliveCounter SAS_Status_SAS_Status_AliveCounter_Y0;
                    /* Computed Parameter: SAS_Status_SAS_Status_AliveCounter_Y0
                     * Referenced by: '<S2>/SAS_Status_SAS_Status_AliveCounter'
                     */
  SAS_SteerWheelRotSpd SAS_Status_SAS_SteerWheelRotSpd_Y0;
                       /* Computed Parameter: SAS_Status_SAS_SteerWheelRotSpd_Y0
                        * Referenced by: '<S2>/SAS_Status_SAS_SteerWheelRotSpd'
                        */
  SafeDistance FallbackDebugInfo_SafeDistance_Y0;
                        /* Computed Parameter: FallbackDebugInfo_SafeDistance_Y0
                         * Referenced by: '<S2>/FallbackDebugInfo_SafeDistance'
                         */
  UDcDcAvlLoSideExt VIMMid3CanFr04_UDcDcAvlLoSideExt_Y0;
                      /* Computed Parameter: VIMMid3CanFr04_UDcDcAvlLoSideExt_Y0
                       * Referenced by: '<S2>/VIMMid3CanFr04_UDcDcAvlLoSideExt'
                       */
  VehUsgStReq VIMMid3CanFr09_VehUsgStReq_Y0;
                            /* Computed Parameter: VIMMid3CanFr09_VehUsgStReq_Y0
                             * Referenced by: '<S2>/VIMMid3CanFr09_VehUsgStReq'
                             */
  WhlLockStsLockSts_C VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C_Y0;
              /* Computed Parameter: VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C_Y0
               * Referenced by: '<S2>/VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C'
               */
  YRS1_AliveCounter YRS1_YRS1_AliveCounter_Y0;
                                /* Computed Parameter: YRS1_YRS1_AliveCounter_Y0
                                 * Referenced by: '<S2>/YRS1_YRS1_AliveCounter'
                                 */
  YRS_AliveCounter YRS2_YRS_AliveCounter_Y0;
                                 /* Computed Parameter: YRS2_YRS_AliveCounter_Y0
                                  * Referenced by: '<S2>/YRS2_YRS_AliveCounter'
                                  */
  YRS_LateralSensorState YRS1_YRS_LateralSensorState_Y0;
                           /* Computed Parameter: YRS1_YRS_LateralSensorState_Y0
                            * Referenced by: '<S2>/YRS1_YRS_LateralSensorState'
                            */
  YRS_LongitSensorState YRS2_YRS_LongitSensorState_Y0;
                            /* Computed Parameter: YRS2_YRS_LongitSensorState_Y0
                             * Referenced by: '<S2>/YRS2_YRS_LongitSensorState'
                             */
  YRS_YawRateSensorState YRS1_YRS_YawRateSensorState_Y0;
                           /* Computed Parameter: YRS1_YRS_YawRateSensorState_Y0
                            * Referenced by: '<S2>/YRS1_YRS_YawRateSensorState'
                            */
  YawRate1Qf1_C VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C_Y0;
                    /* Computed Parameter: VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C_Y0
                     * Referenced by: '<S2>/VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C'
                     */
  boolean Constant7_Value_d;           /* Computed Parameter: Constant7_Value_d
                                        * Referenced by: '<S24>/Constant7'
                                        */
  boolean Constant6_Value_l;           /* Computed Parameter: Constant6_Value_l
                                        * Referenced by: '<S24>/Constant6'
                                        */
  boolean Constant7_Value_h;           /* Computed Parameter: Constant7_Value_h
                                        * Referenced by: '<S42>/Constant7'
                                        */
  boolean Constant6_Value_d;           /* Computed Parameter: Constant6_Value_d
                                        * Referenced by: '<S42>/Constant6'
                                        */
  boolean Constant7_Value_j;           /* Computed Parameter: Constant7_Value_j
                                        * Referenced by: '<S43>/Constant7'
                                        */
  boolean Constant6_Value_b;           /* Computed Parameter: Constant6_Value_b
                                        * Referenced by: '<S43>/Constant6'
                                        */
  boolean Constant7_Value_g;           /* Computed Parameter: Constant7_Value_g
                                        * Referenced by: '<S44>/Constant7'
                                        */
  boolean Constant6_Value_ew;          /* Computed Parameter: Constant6_Value_ew
                                        * Referenced by: '<S44>/Constant6'
                                        */
  boolean Constant7_Value_c;           /* Computed Parameter: Constant7_Value_c
                                        * Referenced by: '<S45>/Constant7'
                                        */
  boolean Constant6_Value_j;           /* Computed Parameter: Constant6_Value_j
                                        * Referenced by: '<S45>/Constant6'
                                        */
  boolean Constant7_Value_gs;          /* Computed Parameter: Constant7_Value_gs
                                        * Referenced by: '<S64>/Constant7'
                                        */
  boolean Constant6_Value_n;           /* Computed Parameter: Constant6_Value_n
                                        * Referenced by: '<S64>/Constant6'
                                        */
  VehMid3SsmCounter0Timeout FallbackSystemStatus_VehMid3SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter0Timeout'
         */
  VehMid3SsmCounter1Timeout FallbackSystemStatus_VehMid3SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid3SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid3SsmCounter1Timeout'
         */
  VehMid3VcuCounter0Timeout FallbackSystemStatus_VehMid3VcuCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter0Timeout'
         */
  VehMid3VcuCounter1Timeout FallbackSystemStatus_VehMid3VcuCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid3VcuCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid3VcuCounter1Timeout'
         */
  VehMid5SsmCounter0Timeout FallbackSystemStatus_VehMid5SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter0Timeout'
         */
  VehMid5SsmCounter1Timeout FallbackSystemStatus_VehMid5SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid5SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid5SsmCounter1Timeout'
         */
  VehMid6SsmCounter0Timeout FallbackSystemStatus_VehMid6SsmCounter0Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter0Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter0Timeout'
         */
  VehMid6SsmCounter1Timeout FallbackSystemStatus_VehMid6SsmCounter1Timeout_Y0;
        /* Computed Parameter: FallbackSystemStatus_VehMid6SsmCounter1Timeout_Y0
         * Referenced by: '<S2>/FallbackSystemStatus_VehMid6SsmCounter1Timeout'
         */
  FrontCameraCanTimeout FallbackSystemStatus_FrontCameraCanTimeout_Y0;
            /* Computed Parameter: FallbackSystemStatus_FrontCameraCanTimeout_Y0
             * Referenced by: '<S2>/FallbackSystemStatus_FrontCameraCanTimeout'
             */
  FrontRadarCanTimeout FallbackSystemStatus_FrontRadarCanTimeout_Y0;
             /* Computed Parameter: FallbackSystemStatus_FrontRadarCanTimeout_Y0
              * Referenced by: '<S2>/FallbackSystemStatus_FrontRadarCanTimeout'
              */
  boolean UnitDelay_InitialCondition_ev;
                            /* Computed Parameter: UnitDelay_InitialCondition_ev
                             * Referenced by: '<S6>/Unit Delay'
                             */
  boolean UnitDelay_InitialCondition_b;
                             /* Computed Parameter: UnitDelay_InitialCondition_b
                              * Referenced by: '<S24>/Unit Delay'
                              */
  boolean UnitDelay1_InitialCondition_f;
                            /* Computed Parameter: UnitDelay1_InitialCondition_f
                             * Referenced by: '<S36>/Unit Delay1'
                             */
  boolean UnitDelay1_InitialCondition_p;
                            /* Computed Parameter: UnitDelay1_InitialCondition_p
                             * Referenced by: '<S35>/Unit Delay1'
                             */
  boolean UnitDelay1_InitialCondition_fc;
                           /* Computed Parameter: UnitDelay1_InitialCondition_fc
                            * Referenced by: '<S37>/Unit Delay1'
                            */
  boolean UnitDelay_InitialCondition_j;
                             /* Computed Parameter: UnitDelay_InitialCondition_j
                              * Referenced by: '<S44>/Unit Delay'
                              */
  boolean UnitDelay_InitialCondition_a;
                             /* Computed Parameter: UnitDelay_InitialCondition_a
                              * Referenced by: '<S45>/Unit Delay'
                              */
  boolean UnitDelay_InitialCondition_c;
                             /* Computed Parameter: UnitDelay_InitialCondition_c
                              * Referenced by: '<S10>/Unit Delay'
                              */
  boolean UnitDelay1_InitialCondition_b;
                            /* Computed Parameter: UnitDelay1_InitialCondition_b
                             * Referenced by: '<S33>/Unit Delay1'
                             */
  boolean UnitDelay1_InitialCondition_k;
                            /* Computed Parameter: UnitDelay1_InitialCondition_k
                             * Referenced by: '<S34>/Unit Delay1'
                             */
  boolean UnitDelay_InitialCondition_cb;
                            /* Computed Parameter: UnitDelay_InitialCondition_cb
                             * Referenced by: '<S42>/Unit Delay'
                             */
  boolean UnitDelay_InitialCondition_pm;
                            /* Computed Parameter: UnitDelay_InitialCondition_pm
                             * Referenced by: '<S43>/Unit Delay'
                             */
  boolean Constant5_Value_k;           /* Computed Parameter: Constant5_Value_k
                                        * Referenced by: '<S7>/Constant5'
                                        */
  boolean Constant23_Value_l;          /* Computed Parameter: Constant23_Value_l
                                        * Referenced by: '<S56>/Constant23'
                                        */
  boolean Constant24_Value_b;          /* Computed Parameter: Constant24_Value_b
                                        * Referenced by: '<S56>/Constant24'
                                        */
  boolean Constant25_Value;            /* Computed Parameter: Constant25_Value
                                        * Referenced by: '<S56>/Constant25'
                                        */
  boolean Constant26_Value;            /* Computed Parameter: Constant26_Value
                                        * Referenced by: '<S56>/Constant26'
                                        */
  boolean Constant27_Value;            /* Computed Parameter: Constant27_Value
                                        * Referenced by: '<S56>/Constant27'
                                        */
  boolean Constant28_Value;            /* Computed Parameter: Constant28_Value
                                        * Referenced by: '<S56>/Constant28'
                                        */
  boolean UnitDelay_InitialCondition_h;
                             /* Computed Parameter: UnitDelay_InitialCondition_h
                              * Referenced by: '<S64>/Unit Delay'
                              */
  uint8 Constant15_Value_a;            /* Computed Parameter: Constant15_Value_a
                                        * Referenced by: '<S8>/Constant15'
                                        */
  uint8 Constant1_Value_c;             /* Computed Parameter: Constant1_Value_c
                                        * Referenced by: '<S17>/Constant1'
                                        */
  uint8 Constant2_Value_j;             /* Computed Parameter: Constant2_Value_j
                                        * Referenced by: '<S17>/Constant2'
                                        */
  uint8 Constant14_Value_m;            /* Computed Parameter: Constant14_Value_m
                                        * Referenced by: '<S18>/Constant14'
                                        */
  uint8 Constant_Value_b;              /* Computed Parameter: Constant_Value_b
                                        * Referenced by: '<S10>/Constant'
                                        */
  uint8 Constant6_Value_ng;            /* Computed Parameter: Constant6_Value_ng
                                        * Referenced by: '<S10>/Constant6'
                                        */
  uint8 Constant17_Value_o;            /* Computed Parameter: Constant17_Value_o
                                        * Referenced by: '<S10>/Constant17'
                                        */
  uint8 Constant19_Value_k;            /* Computed Parameter: Constant19_Value_k
                                        * Referenced by: '<S10>/Constant19'
                                        */
  uint8 Constant7_Value_k;             /* Computed Parameter: Constant7_Value_k
                                        * Referenced by: '<S10>/Constant7'
                                        */
  uint8 Constant8_Value_e;             /* Computed Parameter: Constant8_Value_e
                                        * Referenced by: '<S10>/Constant8'
                                        */
  uint8 Constant1_Value_a;             /* Computed Parameter: Constant1_Value_a
                                        * Referenced by: '<S10>/Constant1'
                                        */
  uint8 Constant9_Value_gl;            /* Computed Parameter: Constant9_Value_gl
                                        * Referenced by: '<S10>/Constant9'
                                        */
  uint8 Constant10_Value_m;            /* Computed Parameter: Constant10_Value_m
                                        * Referenced by: '<S10>/Constant10'
                                        */
  uint8 Constant11_Value_g;            /* Computed Parameter: Constant11_Value_g
                                        * Referenced by: '<S10>/Constant11'
                                        */
  uint8 Constant12_Value_b;            /* Computed Parameter: Constant12_Value_b
                                        * Referenced by: '<S10>/Constant12'
                                        */
  uint8 Constant15_Value_n;            /* Computed Parameter: Constant15_Value_n
                                        * Referenced by: '<S10>/Constant15'
                                        */
  uint8 Constant4_Value_i;             /* Computed Parameter: Constant4_Value_i
                                        * Referenced by: '<S10>/Constant4'
                                        */
  uint8 Constant14_Value_c;            /* Computed Parameter: Constant14_Value_c
                                        * Referenced by: '<S10>/Constant14'
                                        */
  uint8 Constant_Value_a3;             /* Computed Parameter: Constant_Value_a3
                                        * Referenced by: '<S52>/Constant'
                                        */
  uint8 Constant_Value_p;              /* Computed Parameter: Constant_Value_p
                                        * Referenced by: '<S55>/Constant'
                                        */
  uint8 Constant19_Value_n;            /* Computed Parameter: Constant19_Value_n
                                        * Referenced by: '<S47>/Constant19'
                                        */
  uint8 Constant20_Value_i;            /* Computed Parameter: Constant20_Value_i
                                        * Referenced by: '<S47>/Constant20'
                                        */
  uint8 Constant17_Value_l;            /* Computed Parameter: Constant17_Value_l
                                        * Referenced by: '<S47>/Constant17'
                                        */
  uint8 Constant18_Value_cm;          /* Computed Parameter: Constant18_Value_cm
                                       * Referenced by: '<S47>/Constant18'
                                       */
  uint8 Out1_Y0_j;                     /* Computed Parameter: Out1_Y0_j
                                        * Referenced by: '<S59>/Out1'
                                        */
  uint8 Constant_Value_m;              /* Computed Parameter: Constant_Value_m
                                        * Referenced by: '<S59>/Constant'
                                        */
  uint8 UnitDelay_InitialCondition_m;
                             /* Computed Parameter: UnitDelay_InitialCondition_m
                              * Referenced by: '<S59>/Unit Delay'
                              */
  uint8 Constant_Value_lw;             /* Computed Parameter: Constant_Value_lw
                                        * Referenced by: '<S63>/Constant'
                                        */
  uint8 Constant15_Value_i;            /* Computed Parameter: Constant15_Value_i
                                        * Referenced by: '<S57>/Constant15'
                                        */
  uint8 Constant1_Value_i;             /* Computed Parameter: Constant1_Value_i
                                        * Referenced by: '<S57>/Constant1'
                                        */
  uint8 Constant2_Value_c;             /* Computed Parameter: Constant2_Value_c
                                        * Referenced by: '<S57>/Constant2'
                                        */
  uint8 Constant3_Value_h;             /* Computed Parameter: Constant3_Value_h
                                        * Referenced by: '<S57>/Constant3'
                                        */
  uint8 Constant5_Value_o;             /* Computed Parameter: Constant5_Value_o
                                        * Referenced by: '<S57>/Constant5'
                                        */
  uint8 Constant22_Value;              /* Computed Parameter: Constant22_Value
                                        * Referenced by: '<S57>/Constant22'
                                        */
  uint8 Constant4_Value_n;             /* Computed Parameter: Constant4_Value_n
                                        * Referenced by: '<S57>/Constant4'
                                        */
  uint8 Constant11_Value_e;            /* Computed Parameter: Constant11_Value_e
                                        * Referenced by: '<S57>/Constant11'
                                        */
  uint8 Constant8_Value_jv;            /* Computed Parameter: Constant8_Value_jv
                                        * Referenced by: '<S57>/Constant8'
                                        */
  uint8 Constant7_Value_ov;            /* Computed Parameter: Constant7_Value_ov
                                        * Referenced by: '<S57>/Constant7'
                                        */
  uint8 Constant6_Value_i;             /* Computed Parameter: Constant6_Value_i
                                        * Referenced by: '<S57>/Constant6'
                                        */
  uint8 Constant9_Value_b;             /* Computed Parameter: Constant9_Value_b
                                        * Referenced by: '<S57>/Constant9'
                                        */
  uint8 Constant14_Value_g;            /* Computed Parameter: Constant14_Value_g
                                        * Referenced by: '<S57>/Constant14'
                                        */
  uint8 Constant_Value_c;              /* Computed Parameter: Constant_Value_c
                                        * Referenced by: '<S81>/Constant'
                                        */
  uint8 VTS_ChecksumEscDa_Y0;        /* Computed Parameter: VTS_ChecksumEscDa_Y0
                                      * Referenced by: '<S78>/VTS_ChecksumEscDa'
                                      */
  uint8 VTS_AliveCounterEscDa_Y0;/* Computed Parameter: VTS_AliveCounterEscDa_Y0
                                  * Referenced by: '<S78>/VTS_AliveCounterEscDa'
                                  */
  uint8 Constant4_Value_bh;            /* Computed Parameter: Constant4_Value_bh
                                        * Referenced by: '<S78>/Constant4'
                                        */
  uint8 Constant5_Value_a;             /* Computed Parameter: Constant5_Value_a
                                        * Referenced by: '<S78>/Constant5'
                                        */
  uint8 Constant6_Value_dc;            /* Computed Parameter: Constant6_Value_dc
                                        * Referenced by: '<S78>/Constant6'
                                        */
  uint8 Constant7_Value_hb;            /* Computed Parameter: Constant7_Value_hb
                                        * Referenced by: '<S78>/Constant7'
                                        */
  uint8 Constant8_Value_f;             /* Computed Parameter: Constant8_Value_f
                                        * Referenced by: '<S78>/Constant8'
                                        */
  uint8 Constant9_Value_o;             /* Computed Parameter: Constant9_Value_o
                                        * Referenced by: '<S78>/Constant9'
                                        */
  uint8 Constant3_Value_dg;            /* Computed Parameter: Constant3_Value_dg
                                        * Referenced by: '<S78>/Constant3'
                                        */
  uint8 Constant2_Value_b;             /* Computed Parameter: Constant2_Value_b
                                        * Referenced by: '<S78>/Constant2'
                                        */
  uint8 Constant1_Value_ib;            /* Computed Parameter: Constant1_Value_ib
                                        * Referenced by: '<S78>/Constant1'
                                        */
  uint8 Output_InitialCondition;  /* Computed Parameter: Output_InitialCondition
                                   * Referenced by: '<S79>/Output'
                                   */
  uint8 Constant11_Value_i;            /* Computed Parameter: Constant11_Value_i
                                        * Referenced by: '<S78>/Constant11'
                                        */
  uint8 Constant10_Value_o;            /* Computed Parameter: Constant10_Value_o
                                        * Referenced by: '<S78>/Constant10'
                                        */
  uint8 Constant12_Value_e;            /* Computed Parameter: Constant12_Value_e
                                        * Referenced by: '<S78>/Constant12'
                                        */
  uint8 Constant13_Value_l;            /* Computed Parameter: Constant13_Value_l
                                        * Referenced by: '<S78>/Constant13'
                                        */
  uint8 Constant14_Value_eo;          /* Computed Parameter: Constant14_Value_eo
                                       * Referenced by: '<S78>/Constant14'
                                       */
  uint8 Constant15_Value_i1;          /* Computed Parameter: Constant15_Value_i1
                                       * Referenced by: '<S78>/Constant15'
                                       */
  uint8 FixPtConstant_Value;          /* Computed Parameter: FixPtConstant_Value
                                       * Referenced by: '<S80>/FixPt Constant'
                                       */
  uint8 Constant7_Value_jf;            /* Computed Parameter: Constant7_Value_jf
                                        * Referenced by: '<S82>/Constant7'
                                        */
  uint8 Constant8_Value_c;             /* Computed Parameter: Constant8_Value_c
                                        * Referenced by: '<S82>/Constant8'
                                        */
  uint8 Constant2_Value_e;             /* Computed Parameter: Constant2_Value_e
                                        * Referenced by: '<S82>/Constant2'
                                        */
  uint8 Constant5_Value_mv;            /* Computed Parameter: Constant5_Value_mv
                                        * Referenced by: '<S82>/Constant5'
                                        */
  uint8 Constant4_Value_h;             /* Computed Parameter: Constant4_Value_h
                                        * Referenced by: '<S82>/Constant4'
                                        */
  uint8 Constant7_Value_e;             /* Computed Parameter: Constant7_Value_e
                                        * Referenced by: '<S83>/Constant7'
                                        */
  uint8 Constant8_Value_o;             /* Computed Parameter: Constant8_Value_o
                                        * Referenced by: '<S83>/Constant8'
                                        */
  uint8 Constant2_Value_h;             /* Computed Parameter: Constant2_Value_h
                                        * Referenced by: '<S83>/Constant2'
                                        */
  uint8 Constant5_Value_g;             /* Computed Parameter: Constant5_Value_g
                                        * Referenced by: '<S83>/Constant5'
                                        */
  uint8 Constant4_Value_l;             /* Computed Parameter: Constant4_Value_l
                                        * Referenced by: '<S83>/Constant4'
                                        */
  uint8 Constant_Value_gc;             /* Computed Parameter: Constant_Value_gc
                                        * Referenced by: '<S87>/Constant'
                                        */
  uint8 Output_InitialCondition_h;
                                /* Computed Parameter: Output_InitialCondition_h
                                 * Referenced by: '<S85>/Output'
                                 */
  uint8 Constant2_Value_fr;            /* Computed Parameter: Constant2_Value_fr
                                        * Referenced by: '<S84>/Constant2'
                                        */
  uint8 Constant1_Value_jp;            /* Computed Parameter: Constant1_Value_jp
                                        * Referenced by: '<S84>/Constant1'
                                        */
  uint8 Constant3_Value_l;             /* Computed Parameter: Constant3_Value_l
                                        * Referenced by: '<S84>/Constant3'
                                        */
  uint8 FixPtConstant_Value_b;      /* Computed Parameter: FixPtConstant_Value_b
                                     * Referenced by: '<S86>/FixPt Constant'
                                     */
  uint8 VTS_ChecksumFrntWhl_Y0;    /* Computed Parameter: VTS_ChecksumFrntWhl_Y0
                                    * Referenced by: '<S71>/VTS_ChecksumFrntWhl'
                                    */
  uint8 VTS_AliveCounterFrntWhl_Y0;
                               /* Computed Parameter: VTS_AliveCounterFrntWhl_Y0
                                * Referenced by: '<S71>/VTS_AliveCounterFrntWhl'
                                */
  uint8 VTS_WheelDirectionFrntRi_Y0;
                              /* Computed Parameter: VTS_WheelDirectionFrntRi_Y0
                               * Referenced by: '<S71>/VTS_WheelDirectionFrntRi'
                               */
  uint8 VTS_SpeedInvalidFrntRi_Y0;
                                /* Computed Parameter: VTS_SpeedInvalidFrntRi_Y0
                                 * Referenced by: '<S71>/VTS_SpeedInvalidFrntRi'
                                 */
  uint8 VTS_WheelDirectionFrntLe_Y0;
                              /* Computed Parameter: VTS_WheelDirectionFrntLe_Y0
                               * Referenced by: '<S71>/VTS_WheelDirectionFrntLe'
                               */
  uint8 VTS_SpeedInvalidFrntLe_Y0;
                                /* Computed Parameter: VTS_SpeedInvalidFrntLe_Y0
                                 * Referenced by: '<S71>/VTS_SpeedInvalidFrntLe'
                                 */
  uint8 Constant6_Value_b2;            /* Computed Parameter: Constant6_Value_b2
                                        * Referenced by: '<S82>/Constant6'
                                        */
  uint8 Constant6_Value_ek;            /* Computed Parameter: Constant6_Value_ek
                                        * Referenced by: '<S83>/Constant6'
                                        */
  uint8 Constant_Value_fn;             /* Computed Parameter: Constant_Value_fn
                                        * Referenced by: '<S93>/Constant'
                                        */
  uint8 Output_InitialCondition_f;
                                /* Computed Parameter: Output_InitialCondition_f
                                 * Referenced by: '<S91>/Output'
                                 */
  uint8 Constant7_Value_hs;            /* Computed Parameter: Constant7_Value_hs
                                        * Referenced by: '<S88>/Constant7'
                                        */
  uint8 FixPtConstant_Value_h;      /* Computed Parameter: FixPtConstant_Value_h
                                     * Referenced by: '<S92>/FixPt Constant'
                                     */
  uint8 Constant7_Value_dl;            /* Computed Parameter: Constant7_Value_dl
                                        * Referenced by: '<S89>/Constant7'
                                        */
  uint8 Constant8_Value_i;             /* Computed Parameter: Constant8_Value_i
                                        * Referenced by: '<S89>/Constant8'
                                        */
  uint8 Constant2_Value_p;             /* Computed Parameter: Constant2_Value_p
                                        * Referenced by: '<S89>/Constant2'
                                        */
  uint8 Constant5_Value_oq;            /* Computed Parameter: Constant5_Value_oq
                                        * Referenced by: '<S89>/Constant5'
                                        */
  uint8 Constant4_Value_bc;            /* Computed Parameter: Constant4_Value_bc
                                        * Referenced by: '<S89>/Constant4'
                                        */
  uint8 Constant7_Value_gq;            /* Computed Parameter: Constant7_Value_gq
                                        * Referenced by: '<S90>/Constant7'
                                        */
  uint8 Constant8_Value_ez;            /* Computed Parameter: Constant8_Value_ez
                                        * Referenced by: '<S90>/Constant8'
                                        */
  uint8 Constant2_Value_m;             /* Computed Parameter: Constant2_Value_m
                                        * Referenced by: '<S90>/Constant2'
                                        */
  uint8 Constant5_Value_p;             /* Computed Parameter: Constant5_Value_p
                                        * Referenced by: '<S90>/Constant5'
                                        */
  uint8 Constant4_Value_li;            /* Computed Parameter: Constant4_Value_li
                                        * Referenced by: '<S90>/Constant4'
                                        */
  uint8 VTS_ChecksumReWhl_Y0;        /* Computed Parameter: VTS_ChecksumReWhl_Y0
                                      * Referenced by: '<S72>/VTS_ChecksumReWhl'
                                      */
  uint8 VTS_AliveCounterReWhl_Y0;/* Computed Parameter: VTS_AliveCounterReWhl_Y0
                                  * Referenced by: '<S72>/VTS_AliveCounterReWhl'
                                  */
  uint8 VTS_WheelDirectionReRi_Y0;
                                /* Computed Parameter: VTS_WheelDirectionReRi_Y0
                                 * Referenced by: '<S72>/VTS_WheelDirectionReRi'
                                 */
  uint8 VTS_SpeedInvalidReRi_Y0;  /* Computed Parameter: VTS_SpeedInvalidReRi_Y0
                                   * Referenced by: '<S72>/VTS_SpeedInvalidReRi'
                                   */
  uint8 VTS_WheelDirectionReLe_Y0;
                                /* Computed Parameter: VTS_WheelDirectionReLe_Y0
                                 * Referenced by: '<S72>/VTS_WheelDirectionReLe'
                                 */
  uint8 VTS_SpeedInvalidReLe_Y0;  /* Computed Parameter: VTS_SpeedInvalidReLe_Y0
                                   * Referenced by: '<S72>/VTS_SpeedInvalidReLe'
                                   */
  uint8 Constant6_Value_p;             /* Computed Parameter: Constant6_Value_p
                                        * Referenced by: '<S89>/Constant6'
                                        */
  uint8 Constant6_Value_h5;            /* Computed Parameter: Constant6_Value_h5
                                        * Referenced by: '<S90>/Constant6'
                                        */
  uint8 Constant_Value_o;              /* Computed Parameter: Constant_Value_o
                                        * Referenced by: '<S98>/Constant'
                                        */
  uint8 Constant2_Value_k;             /* Computed Parameter: Constant2_Value_k
                                        * Referenced by: '<S94>/Constant2'
                                        */
  uint8 Constant4_Value_a;             /* Computed Parameter: Constant4_Value_a
                                        * Referenced by: '<S94>/Constant4'
                                        */
  uint8 Constant5_Value_c2;            /* Computed Parameter: Constant5_Value_c2
                                        * Referenced by: '<S94>/Constant5'
                                        */
  uint8 Constant1_Value_ax;            /* Computed Parameter: Constant1_Value_ax
                                        * Referenced by: '<S94>/Constant1'
                                        */
  uint8 Constant3_Value_ct;            /* Computed Parameter: Constant3_Value_ct
                                        * Referenced by: '<S94>/Constant3'
                                        */
  uint8 Constant6_Value_bo;            /* Computed Parameter: Constant6_Value_bo
                                        * Referenced by: '<S94>/Constant6'
                                        */
  uint8 Constant7_Value_jy;            /* Computed Parameter: Constant7_Value_jy
                                        * Referenced by: '<S94>/Constant7'
                                        */
  uint8 Constant8_Value_ek;            /* Computed Parameter: Constant8_Value_ek
                                        * Referenced by: '<S94>/Constant8'
                                        */
  uint8 Constant10_Value_n;            /* Computed Parameter: Constant10_Value_n
                                        * Referenced by: '<S94>/Constant10'
                                        */
  uint8 Output_InitialCondition_d;
                                /* Computed Parameter: Output_InitialCondition_d
                                 * Referenced by: '<S96>/Output'
                                 */
  uint8 Constant12_Value_k;            /* Computed Parameter: Constant12_Value_k
                                        * Referenced by: '<S94>/Constant12'
                                        */
  uint8 Constant11_Value_o;            /* Computed Parameter: Constant11_Value_o
                                        * Referenced by: '<S94>/Constant11'
                                        */
  uint8 Constant9_Value_e;             /* Computed Parameter: Constant9_Value_e
                                        * Referenced by: '<S94>/Constant9'
                                        */
  uint8 FixPtConstant_Value_o;      /* Computed Parameter: FixPtConstant_Value_o
                                     * Referenced by: '<S97>/FixPt Constant'
                                     */
  uint8 VTS_EscStatusChecksum_Y0;/* Computed Parameter: VTS_EscStatusChecksum_Y0
                                  * Referenced by: '<S73>/VTS_EscStatusChecksum'
                                  */
  uint8 VTS_EscStatusAliveCounter_Y0;
                             /* Computed Parameter: VTS_EscStatusAliveCounter_Y0
                              * Referenced by: '<S73>/VTS_EscStatusAliveCounter'
                              */
  uint8 Constant_Value_b2;             /* Computed Parameter: Constant_Value_b2
                                        * Referenced by: '<S99>/Constant'
                                        */
  uint8 Constant3_Value_o;             /* Computed Parameter: Constant3_Value_o
                                        * Referenced by: '<S99>/Constant3'
                                        */
  uint8 Constant4_Value_ax;            /* Computed Parameter: Constant4_Value_ax
                                        * Referenced by: '<S99>/Constant4'
                                        */
  uint8 Constant_Value_h;              /* Computed Parameter: Constant_Value_h
                                        * Referenced by: '<S104>/Constant'
                                        */
  uint8 Output_InitialCondition_m;
                                /* Computed Parameter: Output_InitialCondition_m
                                 * Referenced by: '<S102>/Output'
                                 */
  uint8 Constant5_Value_af;            /* Computed Parameter: Constant5_Value_af
                                        * Referenced by: '<S100>/Constant5'
                                        */
  uint8 FixPtConstant_Value_l;      /* Computed Parameter: FixPtConstant_Value_l
                                     * Referenced by: '<S103>/FixPt Constant'
                                     */
  uint8 VTS_SASChecksum_Y0;            /* Computed Parameter: VTS_SASChecksum_Y0
                                        * Referenced by: '<S74>/VTS_SASChecksum'
                                        */
  uint8 VTS_SASAliveCounter_Y0;    /* Computed Parameter: VTS_SASAliveCounter_Y0
                                    * Referenced by: '<S74>/VTS_SASAliveCounter'
                                    */
  uint8 VTS_SASFailureSts_Y0;        /* Computed Parameter: VTS_SASFailureSts_Y0
                                      * Referenced by: '<S74>/VTS_SASFailureSts'
                                      */
  uint8 VTS_SASCalibrationSts_Y0;/* Computed Parameter: VTS_SASCalibrationSts_Y0
                                  * Referenced by: '<S74>/VTS_SASCalibrationSts'
                                  */
  uint8 Constant4_Value_f;             /* Computed Parameter: Constant4_Value_f
                                        * Referenced by: '<S106>/Constant4'
                                        */
  uint8 Constant5_Value_on;            /* Computed Parameter: Constant5_Value_on
                                        * Referenced by: '<S106>/Constant5'
                                        */
  uint8 Constant_Value_oj;             /* Computed Parameter: Constant_Value_oj
                                        * Referenced by: '<S106>/Constant'
                                        */
  uint8 Constant2_Value_fr0;          /* Computed Parameter: Constant2_Value_fr0
                                       * Referenced by: '<S106>/Constant2'
                                       */
  uint8 Constant_Value_i;              /* Computed Parameter: Constant_Value_i
                                        * Referenced by: '<S112>/Constant'
                                        */
  uint8 Output_InitialCondition_o;
                                /* Computed Parameter: Output_InitialCondition_o
                                 * Referenced by: '<S110>/Output'
                                 */
  uint8 Constant4_Value_o;             /* Computed Parameter: Constant4_Value_o
                                        * Referenced by: '<S107>/Constant4'
                                        */
  uint8 FixPtConstant_Value_ht;    /* Computed Parameter: FixPtConstant_Value_ht
                                    * Referenced by: '<S111>/FixPt Constant'
                                    */
  uint8 VTS_YRS1AliveCounter_Y0;  /* Computed Parameter: VTS_YRS1AliveCounter_Y0
                                   * Referenced by: '<S76>/VTS_YRS1AliveCounter'
                                   */
  uint8 VTS_LateralSensorState_Y0;
                                /* Computed Parameter: VTS_LateralSensorState_Y0
                                 * Referenced by: '<S76>/VTS_LateralSensorState'
                                 */
  uint8 VTS_YawRateSensorState_Y0;
                                /* Computed Parameter: VTS_YawRateSensorState_Y0
                                 * Referenced by: '<S76>/VTS_YawRateSensorState'
                                 */
  uint8 VTS_YRS1Checksum_Y0;          /* Computed Parameter: VTS_YRS1Checksum_Y0
                                       * Referenced by: '<S76>/VTS_YRS1Checksum'
                                       */
  uint8 Constant_Value_o5;             /* Computed Parameter: Constant_Value_o5
                                        * Referenced by: '<S113>/Constant'
                                        */
  uint8 Constant2_Value_l;             /* Computed Parameter: Constant2_Value_l
                                        * Referenced by: '<S113>/Constant2'
                                        */
  uint8 Constant_Value_ay;             /* Computed Parameter: Constant_Value_ay
                                        * Referenced by: '<S118>/Constant'
                                        */
  uint8 Constant6_Value_hd;            /* Computed Parameter: Constant6_Value_hd
                                        * Referenced by: '<S114>/Constant6'
                                        */
  uint8 Output_InitialCondition_m4;
                               /* Computed Parameter: Output_InitialCondition_m4
                                * Referenced by: '<S116>/Output'
                                */
  uint8 FixPtConstant_Value_i;      /* Computed Parameter: FixPtConstant_Value_i
                                     * Referenced by: '<S117>/FixPt Constant'
                                     */
  uint8 VTS_YRS2AliveCounter_Y0;  /* Computed Parameter: VTS_YRS2AliveCounter_Y0
                                   * Referenced by: '<S77>/VTS_YRS2AliveCounter'
                                   */
  uint8 VTS_LongitSensorState_Y0;/* Computed Parameter: VTS_LongitSensorState_Y0
                                  * Referenced by: '<S77>/VTS_LongitSensorState'
                                  */
  uint8 VTS_YRS2Checksum_Y0;          /* Computed Parameter: VTS_YRS2Checksum_Y0
                                       * Referenced by: '<S77>/VTS_YRS2Checksum'
                                       */
  ESC_DA_MESSAGE_Checksum ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum_Y0;
                /* Computed Parameter: ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum_Y0
                 * Referenced by: '<S2>/ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum'
                 */
  ESC_FrontWheelSpeedsKPH_Checksum
    ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum_Y0;
  /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum_Y0
   * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum'
   */
  uint8 ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure_Y0;
         /* Computed Parameter: ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure_Y0
          * Referenced by: '<S2>/ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure'
          */
  ESC_RearWheelSpeedsKPH_Checksum
    ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum_Y0;
  /* Computed Parameter: ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum_Y0
   * Referenced by: '<S2>/ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum'
   */
  ESC_Status_Checksum ESC_Status_ESC_Status_Checksum_Y0;
                        /* Computed Parameter: ESC_Status_ESC_Status_Checksum_Y0
                         * Referenced by: '<S2>/ESC_Status_ESC_Status_Checksum'
                         */
  SAS_Status_Checksum SAS_Status_SAS_Status_Checksum_Y0;
                        /* Computed Parameter: SAS_Status_SAS_Status_Checksum_Y0
                         * Referenced by: '<S2>/SAS_Status_SAS_Status_Checksum'
                         */
  YRS1_Checksum YRS1_YRS1_Checksum_Y0;
                                    /* Computed Parameter: YRS1_YRS1_Checksum_Y0
                                     * Referenced by: '<S2>/YRS1_YRS1_Checksum'
                                     */
  YRS2_Checksum YRS2_YRS2_Checksum_Y0;
                                    /* Computed Parameter: YRS2_YRS2_Checksum_Y0
                                     * Referenced by: '<S2>/YRS2_YRS2_Checksum'
                                     */
  IDcDcAvlMaxLoSideExt VIMMid3CanFr04_IDcDcAvlMaxLoSideExt_Y0;
                   /* Computed Parameter: VIMMid3CanFr04_IDcDcAvlMaxLoSideExt_Y0
                    * Referenced by: '<S2>/VIMMid3CanFr04_IDcDcAvlMaxLoSideExt'
                    */
  IDcDcAvlLoSideExt VIMMid3CanFr04_IDcDcAvlLoSideExt_Y0;
                      /* Computed Parameter: VIMMid3CanFr04_IDcDcAvlLoSideExt_Y0
                       * Referenced by: '<S2>/VIMMid3CanFr04_IDcDcAvlLoSideExt'
                       */
  VehControlStatus FbAcuAvailable_VehControlStatus_Y0;
                       /* Computed Parameter: FbAcuAvailable_VehControlStatus_Y0
                        * Referenced by: '<S2>/FbAcuAvailable_VehControlStatus'
                        */
  Sensor1v1rStatus FbAcuAvailable_Sensor1v1rStatus_Y0;
                       /* Computed Parameter: FbAcuAvailable_Sensor1v1rStatus_Y0
                        * Referenced by: '<S2>/FbAcuAvailable_Sensor1v1rStatus'
                        */
  McuStatus FbAcuAvailable_McuStatus_Y0;
                              /* Computed Parameter: FbAcuAvailable_McuStatus_Y0
                               * Referenced by: '<S2>/FbAcuAvailable_McuStatus'
                               */
  FbAcuReserved FbAcuAvailable_FbAcuReserved_Y0;
                          /* Computed Parameter: FbAcuAvailable_FbAcuReserved_Y0
                           * Referenced by: '<S2>/FbAcuAvailable_FbAcuReserved'
                           */
  FbAcuRollingCounter FbAcuAvailable_FbAcuRollingCounter_Y0;
                    /* Computed Parameter: FbAcuAvailable_FbAcuRollingCounter_Y0
                     * Referenced by: '<S2>/FbAcuAvailable_FbAcuRollingCounter'
                     */
  SystemStatusRollingCounter FallbackSystemStatus_SystemStatusRollingCounter_Y0;
       /* Computed Parameter: FallbackSystemStatus_SystemStatusRollingCounter_Y0
        * Referenced by: '<S2>/FallbackSystemStatus_SystemStatusRollingCounter'
        */
  LateralSystemState FallbackDebugInfo_LateralSystemState_Y0;
                  /* Computed Parameter: FallbackDebugInfo_LateralSystemState_Y0
                   * Referenced by: '<S2>/FallbackDebugInfo_LateralSystemState'
                   */
  FallbackDebugInfoRollingCounter
    FallbackDebugInfo_FallbackDebugInfoRollingCounter_Y0;
     /* Computed Parameter: FallbackDebugInfo_FallbackDebugInfoRollingCounter_Y0
      * Referenced by: '<S2>/FallbackDebugInfo_FallbackDebugInfoRollingCounter'
      */
  uint8 Constant11_Value_j;            /* Computed Parameter: Constant11_Value_j
                                        * Referenced by: '<S8>/Constant11'
                                        */
  uint8 Constant13_Value_a;            /* Computed Parameter: Constant13_Value_a
                                        * Referenced by: '<S17>/Constant13'
                                        */
  uint8 Constant5_Value_cx;            /* Computed Parameter: Constant5_Value_cx
                                        * Referenced by: '<S17>/Constant5'
                                        */
  uint8 Constant9_Value_bf;            /* Computed Parameter: Constant9_Value_bf
                                        * Referenced by: '<S17>/Constant9'
                                        */
  uint8 Constant15_Value_h;            /* Computed Parameter: Constant15_Value_h
                                        * Referenced by: '<S18>/Constant15'
                                        */
  uint8 Constant16_Value_m;            /* Computed Parameter: Constant16_Value_m
                                        * Referenced by: '<S18>/Constant16'
                                        */
  uint8 Constant8_Value_n;             /* Computed Parameter: Constant8_Value_n
                                        * Referenced by: '<S18>/Constant8'
                                        */
  uint8 Constant2_Value_be;            /* Computed Parameter: Constant2_Value_be
                                        * Referenced by: '<S10>/Constant2'
                                        */
  uint8 Constant3_Value_j;             /* Computed Parameter: Constant3_Value_j
                                        * Referenced by: '<S10>/Constant3'
                                        */
  uint8 UnitDelay1_InitialCondition_p4;
                           /* Computed Parameter: UnitDelay1_InitialCondition_p4
                            * Referenced by: '<S10>/Unit Delay1'
                            */
  uint8 Constant20_Value_d;            /* Computed Parameter: Constant20_Value_d
                                        * Referenced by: '<S10>/Constant20'
                                        */
  uint8 Constant21_Value;              /* Computed Parameter: Constant21_Value
                                        * Referenced by: '<S10>/Constant21'
                                        */
  uint8 Constant13_Value_h;            /* Computed Parameter: Constant13_Value_h
                                        * Referenced by: '<S10>/Constant13'
                                        */
  uint8 Constant2_Value_kc;            /* Computed Parameter: Constant2_Value_kc
                                        * Referenced by: '<S7>/Constant2'
                                        */
  uint8 Constant1_Value_c0;            /* Computed Parameter: Constant1_Value_c0
                                        * Referenced by: '<S7>/Constant1'
                                        */
  uint8 Constant_Value_ip;             /* Computed Parameter: Constant_Value_ip
                                        * Referenced by: '<S7>/Constant'
                                        */
  uint8 Constant3_Value_pc;            /* Computed Parameter: Constant3_Value_pc
                                        * Referenced by: '<S7>/Constant3'
                                        */
  uint8 Constant6_Value_py;            /* Computed Parameter: Constant6_Value_py
                                        * Referenced by: '<S7>/Constant6'
                                        */
  uint8 Constant7_Value_di;            /* Computed Parameter: Constant7_Value_di
                                        * Referenced by: '<S7>/Constant7'
                                        */
  uint8 FixPtConstant_Value_a;      /* Computed Parameter: FixPtConstant_Value_a
                                     * Referenced by: '<S51>/FixPt Constant'
                                     */
  uint8 Output_InitialCondition_l;
                                /* Computed Parameter: Output_InitialCondition_l
                                 * Referenced by: '<S50>/Output'
                                 */
  uint8 UnitDelay_InitialCondition_je;
                            /* Computed Parameter: UnitDelay_InitialCondition_je
                             * Referenced by: '<S49>/Unit Delay'
                             */
  uint8 Constant8_Value_j0;            /* Computed Parameter: Constant8_Value_j0
                                        * Referenced by: '<S49>/Constant8'
                                        */
  uint8 Constant21_Value_o;            /* Computed Parameter: Constant21_Value_o
                                        * Referenced by: '<S49>/Constant21'
                                        */
  uint8 FixPtConstant_Value_e;      /* Computed Parameter: FixPtConstant_Value_e
                                     * Referenced by: '<S54>/FixPt Constant'
                                     */
  uint8 Output_InitialCondition_a;
                                /* Computed Parameter: Output_InitialCondition_a
                                 * Referenced by: '<S53>/Output'
                                 */
  uint8 Output_InitialCondition_he;
                               /* Computed Parameter: Output_InitialCondition_he
                                * Referenced by: '<S60>/Output'
                                */
  uint8 FixPtConstant_Value_lu;    /* Computed Parameter: FixPtConstant_Value_lu
                                    * Referenced by: '<S62>/FixPt Constant'
                                    */
  uint8 Constant_Value_p4;             /* Computed Parameter: Constant_Value_p4
                                        * Referenced by: '<S69>/Constant'
                                        */
  uint8 Constant1_Value_h;             /* Computed Parameter: Constant1_Value_h
                                        * Referenced by: '<S69>/Constant1'
                                        */
  uint8 Constant2_Value_eg;            /* Computed Parameter: Constant2_Value_eg
                                        * Referenced by: '<S69>/Constant2'
                                        */
  uint8 Constant3_Value_m;             /* Computed Parameter: Constant3_Value_m
                                        * Referenced by: '<S69>/Constant3'
                                        */
  uint8 Constant4_Value_bs;            /* Computed Parameter: Constant4_Value_bs
                                        * Referenced by: '<S69>/Constant4'
                                        */
  uint8 Constant5_Value_e;             /* Computed Parameter: Constant5_Value_e
                                        * Referenced by: '<S69>/Constant5'
                                        */
  uint8 Constant6_Value_k;             /* Computed Parameter: Constant6_Value_k
                                        * Referenced by: '<S69>/Constant6'
                                        */
  uint8 Constant7_Value_cs;            /* Computed Parameter: Constant7_Value_cs
                                        * Referenced by: '<S69>/Constant7'
                                        */
  uint8 Constant12_Value_a;            /* Computed Parameter: Constant12_Value_a
                                        * Referenced by: '<S69>/Constant12'
                                        */
  uint8 Constant13_Value_c;            /* Computed Parameter: Constant13_Value_c
                                        * Referenced by: '<S69>/Constant13'
                                        */
  uint8 Constant8_Value_fn;            /* Computed Parameter: Constant8_Value_fn
                                        * Referenced by: '<S69>/Constant8'
                                        */
  uint8 Constant9_Value_i;             /* Computed Parameter: Constant9_Value_i
                                        * Referenced by: '<S69>/Constant9'
                                        */
  uint8 Constant10_Value_a;            /* Computed Parameter: Constant10_Value_a
                                        * Referenced by: '<S69>/Constant10'
                                        */
  uint8 Constant11_Value_m;            /* Computed Parameter: Constant11_Value_m
                                        * Referenced by: '<S69>/Constant11'
                                        */
  uint8 Constant14_Value_lf;          /* Computed Parameter: Constant14_Value_lf
                                       * Referenced by: '<S69>/Constant14'
                                       */
  uint8 Constant15_Value_ii;          /* Computed Parameter: Constant15_Value_ii
                                       * Referenced by: '<S69>/Constant15'
                                       */
  uint8 Constant16_Value_c;            /* Computed Parameter: Constant16_Value_c
                                        * Referenced by: '<S69>/Constant16'
                                        */
  uint8 Constant17_Value_n;            /* Computed Parameter: Constant17_Value_n
                                        * Referenced by: '<S69>/Constant17'
                                        */
  uint8 Constant18_Value_n;            /* Computed Parameter: Constant18_Value_n
                                        * Referenced by: '<S69>/Constant18'
                                        */
  uint8 Constant19_Value_nz;          /* Computed Parameter: Constant19_Value_nz
                                       * Referenced by: '<S69>/Constant19'
                                       */
  uint8 Constant20_Value_o;            /* Computed Parameter: Constant20_Value_o
                                        * Referenced by: '<S69>/Constant20'
                                        */
  uint8 Constant21_Value_a;            /* Computed Parameter: Constant21_Value_a
                                        * Referenced by: '<S69>/Constant21'
                                        */
  uint8 Constant22_Value_l;            /* Computed Parameter: Constant22_Value_l
                                        * Referenced by: '<S69>/Constant22'
                                        */
  uint8 Constant23_Value_c;            /* Computed Parameter: Constant23_Value_c
                                        * Referenced by: '<S69>/Constant23'
                                        */
  uint8 Constant24_Value_g;            /* Computed Parameter: Constant24_Value_g
                                        * Referenced by: '<S69>/Constant24'
                                        */
  uint8 Constant25_Value_j;            /* Computed Parameter: Constant25_Value_j
                                        * Referenced by: '<S69>/Constant25'
                                        */
};

/* Block parameters (default storage) */
extern P_FallbackSigOutput_T FallbackSigOutput_P;

/* Block signals (default storage) */
extern B_FallbackSigOutput_T FallbackSigOutput_B;

/* Block states (default storage) */
extern DW_FallbackSigOutput_T FallbackSigOutput_DW;

/* External data declarations for dependent source files */
extern const SG_SwtExtrLiFromAPI FallbackSigOutput_rtZSG_SwtExtrLiFromAPI;/* SG_SwtExtrLiFromAPI ground */
extern const SG_AdFusedFricEstimn FallbackSigOutput_rtZSG_AdFusedFricEstimn_adt;/* SG_AdFusedFricEstimn ground */
extern const SG_AdFreeDst FallbackSigOutput_rtZSG_AdFreeDst_adt;/* SG_AdFreeDst ground */
extern const ConstB_FallbackSigOutput_T FallbackSigOutput_ConstB;/* constant block i/o */

/* Exported data declaration */

/* Declaration for custom storage class: ExportToFile */
extern float32 EAD_AccRequest;         /* '<S19>/Switch' */
extern boolean EAD_AcuFbFdCanError;    /* '<S58>/Signal Conversion3' */
extern boolean EAD_AcuFbMid2CanError_P;/* Referenced by: '<S58>/Parameter16' */
extern boolean EAD_AcuFbMid3CanError;  /* '<S58>/OR3' */
extern boolean EAD_AcuFbMid3CanError_P;/* Referenced by: '<S58>/Parameter13' */
extern boolean EAD_AcuFbMid5CanError;  /* '<S58>/OR4' */
extern boolean EAD_AcuFbMid5CanError_P;/* Referenced by: '<S58>/Parameter14' */
extern boolean EAD_AcuFbMid6CanError;  /* '<S58>/OR5' */
extern boolean EAD_AcuFbMid6CanError_P;/* Referenced by: '<S58>/Parameter15' */
extern float32 EAD_AdActiveDelayTime_P;/* Referenced by: '<S10>/DelayTime' */
extern boolean EAD_AdActiveRequest;    /* '<S42>/Multiport Switch1' */
extern uint8 EAD_AdDirReqDirReq;       /* '<S10>/Switch22' */
extern uint8 EAD_AdDirReqDirReq_P;     /* Referenced by: '<S10>/Constant36' */
extern float32 EAD_AdNomALgtReqGroupSafeALgtNomReqMax;/* '<S16>/Signal Conversion' */
extern float32 EAD_AdNomALgtReqGroupSafeALgtNomReqMin;/* '<S16>/Signal Conversion1' */
extern float32 EAD_AdNomALgtReqGroupSafeNegLimForJerk;/* '<S16>/Signal Conversion9' */
extern float32 EAD_AdNomPosLimForJerk; /* '<S16>/Signal Conversion10' */
extern float32 EAD_AdPrimALgtLimReqGroupSafeALgtMaxReq;/* '<S16>/Signal Conversion4' */
extern float32 EAD_AdPrimALgtLimReqGroupSafeALgtMinReq;/* '<S16>/Signal Conversion6' */
extern float32 EAD_AdPrimWhlAgReqGroupSafeWhlAgReq;/* '<S13>/Switch2' */
extern float32 EAD_AdQuitDelayTime_P;  /* Referenced by: '<S10>/DelayTime4' */
extern boolean EAD_AdQuitRequest;      /* '<S43>/Multiport Switch1' */
extern float32 EAD_AdSecALgtLimReqGroupSafeALgtMaxReq;/* '<S16>/Signal Conversion5' */
extern float32 EAD_AdSecALgtLimReqGroupSafeALgtMinReq;/* '<S16>/Signal Conversion7' */
extern float32 EAD_AdSecWhlAgReqGroupSafeWhlAgReq;/* '<S12>/Switch2' */
extern float32 EAD_AdSetSpd;           /* '<S10>/Constant5' */
extern uint8 EAD_AdStandStillReqReq;   /* '<S10>/Switch21' */
extern uint8 EAD_AdStandStillReqReq_P; /* Referenced by: '<S10>/Constant34' */
extern uint8 EAD_AdWhlLockReqNoReqApplyRel;/* '<S10>/Constant2' */
extern uint8 EAD_AdpLiReqFromAPIHzrdLiActvnReq;/* '<S10>/Switch2' */
extern uint8 EAD_AdpLiReqFromAPIHzrdLiDeactnReq;/* '<S10>/Switch3' */
extern uint8 EAD_AdpLiReqFromAPIIncrLiRiReq;/* '<S10>/Constant13' */
extern uint8 EAD_AdpLiReqFromAPIIndcrLeReq;/* '<S10>/Signal Conversion' */
extern uint8 EAD_AswVersion_P;         /* Referenced by:
                                        * '<S7>/Parameter'
                                        * '<S47>/Parameter'
                                        * '<S57>/Parameter'
                                        */
extern uint8 EAD_AutnmsDrvStReqAutnmsDrvStReq;/* '<S10>/Switch1' */
extern boolean EAD_CameraCanError;     /* '<S58>/OR6' */
extern boolean EAD_CameraCanError_P;   /* Referenced by: '<S58>/Parameter18' */
extern float32 EAD_CanTimeoutThd_P;    /* Referenced by: '<S49>/Constant17' */
extern boolean EAD_ChooseSteerAngleRateLimit_P;/* Referenced by: '<S8>/Parameter7' */
extern boolean EAD_CswAccRateLmt_P;    /* Referenced by: '<S17>/Parameter' */
extern boolean EAD_CswAdDirReqDirReq_P;/* Referenced by: '<S10>/Constant35' */
extern boolean EAD_CswAdStandStillReqReq_P;/* Referenced by: '<S10>/Constant33' */
extern boolean EAD_CswEnterAdMode_P;   /* Referenced by: '<S10>/Constant18' */
extern boolean EAD_CswFbAcuReserved_P; /* Referenced by: '<S57>/Parameter8' */
extern boolean EAD_CswHmiAutnmsSts_P;  /* Referenced by: '<S10>/Constant31' */
extern boolean EAD_CswMcuStatus_P;     /* Referenced by: '<S57>/Parameter6' */
extern boolean EAD_CswOutOfOddDetect_P;/* Referenced by: '<S57>/Parameter19' */
extern boolean EAD_CswQuitAdMode_P;    /* Referenced by: '<S10>/Constant16' */
extern boolean EAD_CswRawValue_P;      /* Referenced by: '<S57>/Parameter21' */
extern boolean EAD_CswSelfCheckStatus_P;/* Referenced by: '<S58>/Parameter' */
extern boolean EAD_CswSensor1v1rStatus_P;/* Referenced by: '<S56>/Parameter2' */
extern boolean EAD_CswSteerAngleRate_P;/* Referenced by: '<S8>/Parameter' */
extern boolean EAD_CswVehControlStatus_P;/* Referenced by: '<S57>/Parameter4' */
extern boolean EAD_CswVehOperStReq_P;  /* Referenced by: '<S10>/Constant29' */
extern boolean EAD_EnableStandStill;   /* '<S10>/AND5' */
extern uint8 EAD_FbAcuReserved_P;      /* Referenced by: '<S57>/Parameter9' */
extern uint8 EAD_FbAcuRollingCounter;  /* '<S60>/Output' */
extern float32 EAD_FilteredActualVehAcc;/* '<S20>/Add' */
extern boolean EAD_FrontCameraCalibrationEnable;/* '<S49>/AND1' */
extern boolean EAD_FrontCameraCanTimeout;/* '<S49>/Signal Conversion15' */
extern float32 EAD_FrontCameraCanTimer;/* '<S49>/Signal Conversion33' */
extern boolean EAD_FrontRadarCalibrationEnable;/* '<S49>/AND' */
extern boolean EAD_FrontRadarCanTimeout;/* '<S49>/Signal Conversion16' */
extern float32 EAD_FrontRadarCanTimer; /* '<S49>/Signal Conversion32' */
extern float32 EAD_HazardLightDelayTime_P;/* Referenced by:
                                           * '<S10>/Parameter3'
                                           * '<S10>/Parameter5'
                                           */
extern uint8 EAD_HmiAutnmsStsHmiAutnmsSts;/* '<S10>/Switch20' */
extern uint8 EAD_HmiAutnmsSts_P;       /* Referenced by: '<S10>/Constant32' */
extern float32 EAD_JerkRequest;        /* '<S17>/Signal Copy' */
extern float32 EAD_LimitSteeringAngle; /* '<S8>/Divide2' */
extern float32 EAD_LmtAccRequest;      /* '<S27>/Switch2' */
extern float32 EAD_LmtMaxAccRequest;   /* '<S26>/Switch2' */
extern float32 EAD_LmtMaxJerkRequest;  /* '<S28>/Switch2' */
extern float32 EAD_LmtMinAccRequest;   /* '<S25>/Switch2' */
extern float32 EAD_LmtMinJerkRequest;  /* '<S29>/Switch2' */
extern float32 EAD_MaxAccRate_P;       /* Referenced by: '<S17>/Parameter2' */
extern float32 EAD_MaxFrontSteerAngleRate_M[10];
                                    /* Referenced by: '<S8>/1-D Lookup Table' */
extern float32 EAD_MaxSteerAngleRate_P;/* Referenced by: '<S8>/Parameter2' */
extern uint8 EAD_McuStatus;            /* '<S57>/Switch3' */
extern uint8 EAD_McuStatus_P;          /* Referenced by: '<S57>/Parameter7' */
extern uint8 EAD_McuVersion;           /* '<S57>/Switch4' */
extern boolean EAD_OnlyUseTrajectory_P;/* Referenced by: '<S6>/Parameter1' */
extern boolean EAD_RadarCanError;      /* '<S58>/OR7' */
extern boolean EAD_RadarCanError_P;    /* Referenced by: '<S58>/Parameter20' */
extern float32 EAD_RawAccRequest;      /* '<S18>/Signal Conversion' */
extern boolean EAD_RawAcuFbMid3CanError;/* '<S58>/Signal Conversion' */
extern boolean EAD_RawAcuFbMid5CanError;/* '<S58>/Signal Conversion1' */
extern boolean EAD_RawAcuFbMid6CanError;/* '<S58>/Signal Conversion2' */
extern boolean EAD_RawCameraCanError;  /* '<S58>/Signal Conversion7' */
extern uint8 EAD_RawHmiAutnmsSts;      /* '<S10>/Switch' */
extern float32 EAD_RawJerkRequest;     /* '<S18>/Signal Conversion1' */
extern boolean EAD_RawRadarCanError;   /* '<S58>/Signal Conversion8' */
extern uint16 EAD_RawSelfCheckStatus;  /* '<S65>/Bitwise AND1' */
extern boolean EAD_RawVehFbMid3CanError;/* '<S58>/Signal Conversion5' */
extern boolean EAD_RawVehFbMid5CanError;/* '<S58>/Signal Conversion4' */
extern boolean EAD_RawVehFbMid6CanError;/* '<S58>/Signal Conversion6' */
extern float32 EAD_SecAdNomALgtReqGroupSafeALgtNomReqMax;/* '<S16>/Signal Conversion2' */
extern float32 EAD_SecAdNomALgtReqGroupSafeALgtNomReqMin;/* '<S16>/Signal Conversion3' */
extern float32 EAD_SecAdNomALgtReqGroupSafeNegLimForJerk;/* '<S16>/Signal Conversion8' */
extern float32 EAD_SecAdNomALgtReqGroupSafePosLimForJerk;/* '<S16>/Signal Conversion11' */
extern uint8 EAD_SecAdWhlLockReqNoReqApplyRel;/* '<S10>/Constant3' */
extern uint16 EAD_SelfCheckStatus;     /* '<S58>/Switch' */
extern uint16 EAD_SelfCheckStatus_P;   /* Referenced by: '<S58>/Parameter1' */
extern uint8 EAD_Sensor1v1rStatus;     /* '<S56>/Switch1' */
extern uint8 EAD_Sensor1v1rStatus_P;   /* Referenced by: '<S56>/Parameter3' */
extern float32 EAD_StandstillAccThd_P; /* Referenced by: '<S10>/Parameter' */
extern float32 EAD_StandstillSpeedThd_P;/* Referenced by: '<S10>/Parameter1' */
extern float32 EAD_SteeringAngle;      /* '<S8>/Divide1' */
extern float32 EAD_SysCycleTime_P;     /* Referenced by:
                                        * '<S49>/Constant18'
                                        * '<S8>/Parameter1'
                                        * '<S10>/DelayTime1'
                                        * '<S10>/DelayTime3'
                                        * '<S10>/Parameter2'
                                        * '<S10>/Parameter4'
                                        * '<S17>/Parameter1'
                                        * '<S17>/Parameter5'
                                        */
extern boolean EAD_UseTrajectory_P;    /* Referenced by: '<S6>/Parameter3' */
extern uint8 EAD_VehControlStatus;     /* '<S57>/Switch2' */
extern uint8 EAD_VehControlStatus_P;   /* Referenced by: '<S57>/Parameter5' */
extern boolean EAD_VehFbMid2CanError_P;/* Referenced by: '<S58>/Parameter17' */
extern boolean EAD_VehFbMid3CanError;  /* '<S58>/OR1' */
extern boolean EAD_VehFbMid3CanError_P;/* Referenced by: '<S58>/Parameter12' */
extern boolean EAD_VehFbMid5CanError;  /* '<S58>/OR2' */
extern boolean EAD_VehFbMid5CanError_P;/* Referenced by: '<S58>/Parameter11' */
extern boolean EAD_VehFbMid6CanError;  /* '<S58>/OR' */
extern boolean EAD_VehFbMid6CanError_P;/* Referenced by: '<S58>/Parameter10' */
extern uint8 EAD_VehOperStReqVehOperStReq;/* '<S10>/Switch19' */
extern uint8 EAD_VehOperStReq_P;       /* Referenced by: '<S10>/Constant30' */
extern uint8 TimeCntr;                 /* Referenced by: '<S75>/TaskForRadar' */
extern uint8 VTS_AliveCounterEscDa;    /* '<S79>/Output' */
extern uint8 VTS_AliveCounterFrntWhl;  /* '<S85>/Output' */
extern uint8 VTS_AliveCounterReWhl;    /* '<S91>/Output' */
extern uint8 VTS_ChecksumEscDa;        /* '<S78>/C Caller3' */
extern uint8 VTS_ChecksumFrntWhl;      /* '<S84>/C Caller3' */
extern uint8 VTS_ChecksumReWhl;        /* '<S88>/C Caller4' */
extern uint8 VTS_CswLateralAcce_P;     /* Referenced by: '<S106>/Parameter1' */
extern uint8 VTS_CswLateralSensorState_P;/* Referenced by: '<S106>/Parameter3' */
extern uint8 VTS_CswLongitAcce_P;      /* Referenced by: '<S113>/Parameter2' */
extern uint8 VTS_CswLongitSensorState_P;/* Referenced by: '<S113>/Parameter' */
extern uint8 VTS_CswSASCalibrationSts_P;/* Referenced by: '<S99>/Parameter7' */
extern uint8 VTS_CswSASFailureSts_P;   /* Referenced by: '<S99>/Parameter5' */
extern uint8 VTS_CswSpeedInvalidFrntLe_P;/* Referenced by: '<S82>/Parameter6' */
extern uint8 VTS_CswSpeedInvalidFrntRi_P;/* Referenced by: '<S83>/Parameter6' */
extern uint8 VTS_CswSpeedInvalidReLe_P;/* Referenced by: '<S89>/Parameter6' */
extern uint8 VTS_CswSpeedInvalidReRi_P;/* Referenced by: '<S90>/Parameter6' */
extern uint8 VTS_CswSteerWheelAngle_P; /* Referenced by: '<S99>/Parameter1' */
extern uint8 VTS_CswSteerWheelRotSpd_P;/* Referenced by: '<S99>/Parameter3' */
extern uint8 VTS_CswVehicleSpeed_P;    /* Referenced by: '<S95>/Parameter1' */
extern uint8 VTS_CswWheelDirectionFrntLe_P;/* Referenced by: '<S82>/Parameter4' */
extern uint8 VTS_CswWheelDirectionFrntRi_P;/* Referenced by: '<S83>/Parameter4' */
extern uint8 VTS_CswWheelDirectionReLe_P;/* Referenced by: '<S89>/Parameter4' */
extern uint8 VTS_CswWheelDirectionReRi_P;/* Referenced by: '<S90>/Parameter4' */
extern boolean VTS_CswWheelSpeedFrntLe_P;/* Referenced by: '<S82>/Parameter2' */
extern uint8 VTS_CswWheelSpeedFrntRi_P;/* Referenced by: '<S83>/Parameter2' */
extern uint8 VTS_CswWheelSpeedReLe_P;  /* Referenced by: '<S89>/Parameter2' */
extern uint8 VTS_CswWheelSpeedReRi_P;  /* Referenced by: '<S90>/Parameter2' */
extern uint8 VTS_CswYawRateSensorState_P;/* Referenced by: '<S106>/Parameter7' */
extern uint8 VTS_CswYawRate_P;         /* Referenced by: '<S106>/Parameter5' */
extern uint8 VTS_EscStatusAliveCounter;/* '<S96>/Output' */
extern uint8 VTS_EscStatusChecksum;    /* '<S94>/C Caller5' */
extern float32 VTS_LateralAcce;        /* '<S106>/Switch2' */
extern float32 VTS_LateralAcce_P;      /* Referenced by: '<S106>/Parameter' */
extern uint8 VTS_LateralSensorState;   /* '<S106>/Switch3' */
extern uint8 VTS_LateralSensorState_P; /* Referenced by: '<S106>/Parameter2' */
extern float32 VTS_LongitAcce;         /* '<S113>/Switch2' */
extern float32 VTS_LongitAcce_P;       /* Referenced by: '<S113>/Parameter3' */
extern uint8 VTS_LongitSensorState;    /* '<S113>/Switch1' */
extern uint8 VTS_LongitSensorState_P;  /* Referenced by: '<S113>/Parameter1' */
extern float32 VTS_PrimALatDataRawSafeNom;/* '<S67>/Signal Conversion8' */
extern uint8 VTS_PrimALatDataRawSafeNomQf;/* '<S67>/Signal Conversion9' */
extern float32 VTS_PrimALgtDataRawSafeNom;/* '<S67>/Signal Conversion12' */
extern uint8 VTS_PrimALgtDataRawSafeNomQf;/* '<S67>/Signal Conversion13' */
extern float32 VTS_PrimVehSpdGroupSafeNom;/* '<S67>/Signal Conversion17' */
extern uint8 VTS_PrimVehSpdGroupSafeNomQf;/* '<S67>/Signal Conversion18' */
extern float32 VTS_PrimWhlAgSpdFrntSafeLe;/* '<S67>/Signal Conversion' */
extern uint8 VTS_PrimWhlAgSpdFrntSafeLeQf;/* '<S67>/Signal Conversion1' */
extern float32 VTS_PrimWhlAgSpdFrntSafeRi;/* '<S67>/Signal Conversion2' */
extern uint8 VTS_PrimWhlAgSpdFrntSafeRiQf;/* '<S67>/Signal Conversion3' */
extern float32 VTS_PrimWhlAgSpdReSafeLe;/* '<S67>/Signal Conversion4' */
extern uint8 VTS_PrimWhlAgSpdReSafeLeQf;/* '<S67>/Signal Conversion5' */
extern float32 VTS_PrimWhlAgSpdReSafeRi;/* '<S67>/Signal Conversion6' */
extern uint8 VTS_PrimWhlAgSpdReSafeRiQf;/* '<S67>/Signal Conversion7' */
extern uint8 VTS_PrpsnTqDirAct;        /* '<S67>/Signal Conversion20' */
extern float32 VTS_RawLateralAcce;     /* '<S106>/Divide' */
extern uint8 VTS_RawLateralSensorState;/* '<S106>/Switch' */
extern float32 VTS_RawLongitAcce;      /* '<S113>/Divide' */
extern uint8 VTS_RawLongitSensorState; /* '<S113>/Switch' */
extern uint8 VTS_RawSpeedInvalidFrntLe;/* '<S82>/Switch2' */
extern uint8 VTS_RawSpeedInvalidFrntRi;/* '<S83>/Switch2' */
extern uint8 VTS_RawSpeedInvalidReLe;  /* '<S89>/Switch2' */
extern uint8 VTS_RawSpeedInvalidReRi;  /* '<S90>/Switch2' */
extern float32 VTS_RawSteerWheelAngle; /* '<S99>/Product' */
extern float32 VTS_RawSteerWheelRotSpd;/* '<S99>/Abs' */
extern float32 VTS_RawVehicleSpeed;    /* '<S95>/Product' */
extern uint8 VTS_RawWheelDirectionFrntLe;/* '<S82>/Switch' */
extern uint8 VTS_RawWheelDirectionFrntRi;/* '<S83>/Switch' */
extern uint8 VTS_RawWheelDirectionReLe;/* '<S89>/Switch' */
extern uint8 VTS_RawWheelDirectionReRi;/* '<S90>/Switch' */
extern float32 VTS_RawWheelSpeedFrntLe;/* '<S82>/Abs' */
extern float32 VTS_RawWheelSpeedFrntRi;/* '<S83>/Abs' */
extern float32 VTS_RawWheelSpeedReLe;  /* '<S89>/Abs' */
extern float32 VTS_RawWheelSpeedReRi;  /* '<S90>/Abs' */
extern float32 VTS_RawYawRate;         /* '<S106>/Product' */
extern uint8 VTS_RawYawRateSensorState;/* '<S106>/Switch1' */
extern uint8 VTS_SASAliveCounter;      /* '<S102>/Output' */
extern uint8 VTS_SASCalibrationSts;    /* '<S99>/Switch4' */
extern uint8 VTS_SASCalibrationSts_P;  /* Referenced by: '<S99>/Parameter6' */
extern uint8 VTS_SASChecksum;          /* '<S100>/C Caller2' */
extern uint8 VTS_SASFailureSts;        /* '<S99>/Switch3' */
extern uint8 VTS_SASFailureSts_P;      /* Referenced by: '<S99>/Parameter4' */
extern uint8 VTS_SpeedInvalidFrntLe;   /* '<S82>/Switch5' */
extern uint8 VTS_SpeedInvalidFrntLe_P; /* Referenced by: '<S82>/Parameter5' */
extern uint8 VTS_SpeedInvalidFrntRi;   /* '<S83>/Switch5' */
extern uint8 VTS_SpeedInvalidFrntRi_P; /* Referenced by: '<S83>/Parameter5' */
extern uint8 VTS_SpeedInvalidReLe;     /* '<S89>/Switch5' */
extern uint8 VTS_SpeedInvalidReLe_P;   /* Referenced by: '<S89>/Parameter5' */
extern uint8 VTS_SpeedInvalidReRi;     /* '<S90>/Switch5' */
extern uint8 VTS_SpeedInvalidReRi_P;   /* Referenced by: '<S90>/Parameter5' */
extern float32 VTS_SteerWheelAngle;    /* '<S99>/Switch1' */
extern float32 VTS_SteerWheelAngle_P;  /* Referenced by: '<S99>/Parameter' */
extern float32 VTS_SteerWheelRotSpd;   /* '<S99>/Switch2' */
extern float32 VTS_SteerWheelRotSpd_P; /* Referenced by: '<S99>/Parameter2' */
extern float32 VTS_SteerWhlAgSafe;     /* '<S67>/Signal Conversion14' */
extern float32 VTS_SteerWhlAgSpdSafe;  /* '<S67>/Signal Conversion15' */
extern uint8 VTS_SteerWhlSnsrQf;       /* '<S67>/Signal Conversion16' */
extern float32 VTS_TireRadiusFrntLe_P; /* Referenced by: '<S82>/Parameter' */
extern float32 VTS_TireRadiusFrntRi_P; /* Referenced by: '<S90>/Parameter' */
extern float32 VTS_TireRadiusReLe_P;   /* Referenced by: '<S89>/Parameter' */
extern float32 VTS_TireRadiusReRi_P;   /* Referenced by: '<S83>/Parameter' */
extern float32 VTS_VehicleSpeed;       /* '<S95>/Switch' */
extern uint8 VTS_VehicleSpeedInvalid;  /* '<S94>/Constant9' */
extern float32 VTS_VehicleSpeed_P;     /* Referenced by: '<S95>/Parameter' */
extern uint8 VTS_WheelDirectionFrntLe; /* '<S82>/Switch4' */
extern uint8 VTS_WheelDirectionFrntLe_P;/* Referenced by: '<S82>/Parameter3' */
extern uint8 VTS_WheelDirectionFrntRi; /* '<S83>/Switch4' */
extern uint8 VTS_WheelDirectionFrntRi_P;/* Referenced by: '<S83>/Parameter3' */
extern uint8 VTS_WheelDirectionReLe;   /* '<S89>/Switch4' */
extern uint8 VTS_WheelDirectionReLe_P; /* Referenced by: '<S89>/Parameter3' */
extern uint8 VTS_WheelDirectionReRi;   /* '<S90>/Switch4' */
extern uint8 VTS_WheelDirectionReRi_P; /* Referenced by: '<S90>/Parameter3' */
extern float32 VTS_WheelSpeedFrntLe;   /* '<S82>/Switch3' */
extern float32 VTS_WheelSpeedFrntLe_P; /* Referenced by: '<S82>/Parameter1' */
extern float32 VTS_WheelSpeedFrntRi;   /* '<S83>/Switch3' */
extern float32 VTS_WheelSpeedFrntRi_P; /* Referenced by: '<S83>/Parameter1' */
extern float32 VTS_WheelSpeedReLe;     /* '<S89>/Switch3' */
extern float32 VTS_WheelSpeedReLe_P;   /* Referenced by: '<S89>/Parameter1' */
extern float32 VTS_WheelSpeedReRi;     /* '<S90>/Switch3' */
extern float32 VTS_WheelSpeedReRi_P;   /* Referenced by: '<S90>/Parameter1' */
extern uint8 VTS_WhlLockStsLockSts;    /* '<S67>/Signal Conversion19' */
extern uint8 VTS_YRS1AliveCounter;     /* '<S110>/Output' */
extern uint8 VTS_YRS1Checksum;         /* '<S107>/C Caller' */
extern uint8 VTS_YRS2AliveCounter;     /* '<S116>/Output' */
extern uint8 VTS_YRS2Checksum;         /* '<S114>/C Caller1' */
extern float32 VTS_YawRate;            /* '<S106>/Switch4' */
extern float32 VTS_YawRate1;           /* '<S67>/Signal Conversion10' */
extern uint8 VTS_YawRate1Qf1;          /* '<S67>/Signal Conversion11' */
extern uint8 VTS_YawRateSensorState;   /* '<S106>/Switch5' */
extern uint8 VTS_YawRateSensorState_P; /* Referenced by: '<S106>/Parameter6' */
extern float32 VTS_YawRate_P;          /* Referenced by: '<S106>/Parameter4' */

/*-
 * These blocks were eliminated from the model due to optimizations:
 *
 * Block '<S8>/Constant7' : Unused code path elimination
 * Block '<S8>/Constant8' : Unused code path elimination
 * Block '<S8>/Constant9' : Unused code path elimination
 * Block '<S8>/Equal' : Unused code path elimination
 * Block '<S14>/Data Type Duplicate' : Unused code path elimination
 * Block '<S14>/Data Type Propagation' : Unused code path elimination
 * Block '<S12>/Data Type Duplicate' : Unused code path elimination
 * Block '<S12>/Data Type Propagation' : Unused code path elimination
 * Block '<S13>/Data Type Duplicate' : Unused code path elimination
 * Block '<S13>/Data Type Propagation' : Unused code path elimination
 * Block '<S8>/Switch2' : Unused code path elimination
 * Block '<S22>/Data Type Duplicate' : Unused code path elimination
 * Block '<S22>/Data Type Propagation' : Unused code path elimination
 * Block '<S23>/Data Type Duplicate' : Unused code path elimination
 * Block '<S23>/Data Type Propagation' : Unused code path elimination
 * Block '<S25>/Data Type Duplicate' : Unused code path elimination
 * Block '<S25>/Data Type Propagation' : Unused code path elimination
 * Block '<S26>/Data Type Duplicate' : Unused code path elimination
 * Block '<S26>/Data Type Propagation' : Unused code path elimination
 * Block '<S27>/Data Type Duplicate' : Unused code path elimination
 * Block '<S27>/Data Type Propagation' : Unused code path elimination
 * Block '<S28>/Data Type Duplicate' : Unused code path elimination
 * Block '<S28>/Data Type Propagation' : Unused code path elimination
 * Block '<S29>/Data Type Duplicate' : Unused code path elimination
 * Block '<S29>/Data Type Propagation' : Unused code path elimination
 * Block '<S50>/Data Type Propagation' : Unused code path elimination
 * Block '<S51>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S52>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S53>/Data Type Propagation' : Unused code path elimination
 * Block '<S54>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S55>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S60>/Data Type Propagation' : Unused code path elimination
 * Block '<S62>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S63>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S79>/Data Type Propagation' : Unused code path elimination
 * Block '<S80>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S81>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S85>/Data Type Propagation' : Unused code path elimination
 * Block '<S86>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S87>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S91>/Data Type Propagation' : Unused code path elimination
 * Block '<S92>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S93>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S96>/Data Type Propagation' : Unused code path elimination
 * Block '<S97>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S98>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S102>/Data Type Propagation' : Unused code path elimination
 * Block '<S103>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S104>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S110>/Data Type Propagation' : Unused code path elimination
 * Block '<S111>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S112>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S116>/Data Type Propagation' : Unused code path elimination
 * Block '<S117>/FixPt Data Type Duplicate' : Unused code path elimination
 * Block '<S118>/FixPt Data Type Duplicate1' : Unused code path elimination
 * Block '<S11>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S19>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S20>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S24>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S21>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S33>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S34>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S35>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S36>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S37>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S42>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S38>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S43>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S39>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S44>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S40>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S45>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S41>/Signal Copy' : Eliminate redundant signal conversion block
 * Block '<S7>/Data Type Conversion1' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion10' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion13' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion16' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion17' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion18' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion19' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion2' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion22' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion23' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion24' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion25' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion26' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion27' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion30' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion31' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion32' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion33' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion34' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion35' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion36' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion37' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion38' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion42' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion43' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion47' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion48' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion50' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion51' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion58' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion8' : Eliminate redundant data type conversion
 * Block '<S7>/Data Type Conversion9' : Eliminate redundant data type conversion
 * Block '<S64>/Signal Conversion' : Eliminate redundant signal conversion block
 * Block '<S61>/Signal Copy' : Eliminate redundant signal conversion block
 */

/*-
 * The generated code includes comments that allow you to trace directly
 * back to the appropriate location in the model.  The basic format
 * is <system>/block_name, where system is the system number (uniquely
 * assigned by Simulink) and block_name is the name of the block.
 *
 * Use the MATLAB hilite_system command to trace the generated code back
 * to the model.  For example,
 *
 * hilite_system('<S3>')    - opens system 3
 * hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
 *
 * Here is the system hierarchy for this model
 *
 * '<Root>' : 'FallbackSigOutput'
 * '<S1>'   : 'FallbackSigOutput/FallbackSigOutput_Init'
 * '<S2>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable'
 * '<S3>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive'
 * '<S4>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo'
 * '<S5>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor'
 * '<S6>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD'
 * '<S7>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/OutputToVehicleCan'
 * '<S8>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LateralControlLimit'
 * '<S9>'   : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit'
 * '<S10>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD'
 * '<S11>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LateralControlLimit/GradientLimiter'
 * '<S12>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LateralControlLimit/Saturation Dynamic2'
 * '<S13>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LateralControlLimit/Saturation Dynamic3'
 * '<S14>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LateralControlLimit/GradientLimiter/Saturation Dynamic'
 * '<S15>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake'
 * '<S16>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1'
 * '<S17>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode'
 * '<S18>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/Subsystem'
 * '<S19>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/GradientLimiter'
 * '<S20>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/LowPassFilter'
 * '<S21>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/TurnOnDelay1'
 * '<S22>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/GradientLimiter/Saturation Dynamic'
 * '<S23>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/LowPassFilter/Saturation Dynamic1'
 * '<S24>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/EmergencyBrake/OtherFallbackMode/TurnOnDelay1/S-R Flip-Flop'
 * '<S25>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1/Saturation Dynamic'
 * '<S26>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1/Saturation Dynamic1'
 * '<S27>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1/Saturation Dynamic4'
 * '<S28>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1/Saturation Dynamic6'
 * '<S29>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/LongitudinalControlLimit/Subsystem1/Saturation Dynamic7'
 * '<S30>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/Compare To Constant10'
 * '<S31>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/Compare To Constant8'
 * '<S32>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/Compare To Constant9'
 * '<S33>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/EdgeRising'
 * '<S34>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/EdgeRising1'
 * '<S35>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/EdgeRising2'
 * '<S36>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/EdgeRising3'
 * '<S37>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/EdgeRising4'
 * '<S38>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay'
 * '<S39>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay1'
 * '<S40>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay2'
 * '<S41>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay3'
 * '<S42>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay/S-R Flip-Flop'
 * '<S43>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay1/S-R Flip-Flop'
 * '<S44>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay2/S-R Flip-Flop'
 * '<S45>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/EnterAutonomousDrive/EnterAD/OtherForEnterAD/TurnOffDelay3/S-R Flip-Flop'
 * '<S46>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackDebugInfo'
 * '<S47>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackSystemStatus'
 * '<S48>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable'
 * '<S49>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/SensorState'
 * '<S50>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackDebugInfo/Counter Limited2'
 * '<S51>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackDebugInfo/Counter Limited2/Increment Real World'
 * '<S52>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackDebugInfo/Counter Limited2/Wrap To Zero'
 * '<S53>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackSystemStatus/Counter Limited1'
 * '<S54>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackSystemStatus/Counter Limited1/Increment Real World'
 * '<S55>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FallbackSystemStatus/Counter Limited1/Wrap To Zero'
 * '<S56>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/camera_and_radar_calibration_state'
 * '<S57>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state'
 * '<S58>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/receive_can_message_timeout'
 * '<S59>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/camera_and_radar_calibration_state/Bool2UINT8'
 * '<S60>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state/Counter Limited1'
 * '<S61>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state/TurnOnDelay1'
 * '<S62>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state/Counter Limited1/Increment Real World'
 * '<S63>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state/Counter Limited1/Wrap To Zero'
 * '<S64>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/fallback_state/TurnOnDelay1/S-R Flip-Flop'
 * '<S65>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/FallbackInfo/FbAcuAvailable/receive_can_message_timeout/Bool2UINT16'
 * '<S66>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar'
 * '<S67>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/VehInfoFor1V1R'
 * '<S68>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/VehicleInfoToCamara'
 * '<S69>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/VehicleInfoToRadar'
 * '<S70>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_DA_MESSAGE'
 * '<S71>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH'
 * '<S72>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH'
 * '<S73>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status'
 * '<S74>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status'
 * '<S75>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/TaskForRadar'
 * '<S76>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1'
 * '<S77>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2'
 * '<S78>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_DA_MESSAGE/ESC_DA_MESSAGE_E2E'
 * '<S79>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_DA_MESSAGE/ESC_DA_MESSAGE_E2E/Counter Limited1'
 * '<S80>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_DA_MESSAGE/ESC_DA_MESSAGE_E2E/Counter Limited1/Increment Real World'
 * '<S81>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_DA_MESSAGE/ESC_DA_MESSAGE_E2E/Counter Limited1/Wrap To Zero'
 * '<S82>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontLeftWheel'
 * '<S83>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontRightWheel'
 * '<S84>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontWheelSpeedKPH_E2E'
 * '<S85>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1'
 * '<S86>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1/Increment Real World'
 * '<S87>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_FrontWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1/Wrap To Zero'
 * '<S88>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/FrontWheelSpeedKPH_E2E'
 * '<S89>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/RearLeftWheel'
 * '<S90>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/RearRightWheel'
 * '<S91>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1'
 * '<S92>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1/Increment Real World'
 * '<S93>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_RearWheelSpeedKPH/FrontWheelSpeedKPH_E2E/Counter Limited1/Wrap To Zero'
 * '<S94>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status/ESC_Status_E2E'
 * '<S95>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status/SpeedUnitConversion'
 * '<S96>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status/ESC_Status_E2E/Counter Limited2'
 * '<S97>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status/ESC_Status_E2E/Counter Limited2/Increment Real World'
 * '<S98>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/ESC_Status/ESC_Status_E2E/Counter Limited2/Wrap To Zero'
 * '<S99>'  : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/CalculateSAS'
 * '<S100>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/SAS_Status_E2E'
 * '<S101>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/CalculateSAS/Compare To Constant'
 * '<S102>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/SAS_Status_E2E/Counter Limited6'
 * '<S103>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/SAS_Status_E2E/Counter Limited6/Increment Real World'
 * '<S104>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/SAS_Status/SAS_Status_E2E/Counter Limited6/Wrap To Zero'
 * '<S105>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/TaskForRadar/TaskForRadar'
 * '<S106>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/CalculateYRS1'
 * '<S107>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/YRS1_E2E'
 * '<S108>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/CalculateYRS1/Compare To Constant'
 * '<S109>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/CalculateYRS1/Compare To Constant1'
 * '<S110>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/YRS1_E2E/Counter Limited10'
 * '<S111>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/YRS1_E2E/Counter Limited10/Increment Real World'
 * '<S112>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS1/YRS1_E2E/Counter Limited10/Wrap To Zero'
 * '<S113>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/LgtAcc'
 * '<S114>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/YRS2_E2E'
 * '<S115>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/LgtAcc/Compare To Constant'
 * '<S116>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/YRS2_E2E/Counter Limited9'
 * '<S117>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/YRS2_E2E/Counter Limited9/Increment Real World'
 * '<S118>' : 'FallbackSigOutput/FallbackSignalOutput_10ms_Runnable/VehicleToSensor/CalculateVehicleInfoForRadar/YRS2/YRS2_E2E/Counter Limited9/Wrap To Zero'
 */
#endif                                 /* RTW_HEADER_FallbackSigOutput_h_ */

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
