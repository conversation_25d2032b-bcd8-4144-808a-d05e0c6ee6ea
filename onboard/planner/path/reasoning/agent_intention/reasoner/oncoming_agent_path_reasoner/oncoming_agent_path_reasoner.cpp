#include "planner/path/reasoning/agent_intention/reasoner/oncoming_agent_path_reasoner/oncoming_agent_path_reasoner.h"

#include <algorithm>
#include <utility>
#include <vector>
#include <voy_protos/math.pb.h>

#include "log_utils/map_macros.h"
#include "math/constants.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/profile/profile.h"
#include "planner/utility/physics/motion_model_2d.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace path {

namespace {

// Minimum length of right side oncoming agent predicted trajectory to add
// repulsion for.
constexpr double kMinAgentPredictedMovementToAddPulloverRepulsionInM = 10.0;

// Distance buffer to pullover destination to determine whether Ego need to
// yield for oncoming agent.
constexpr double kDistanceBufferForYieldingForOncomingAgentDuringPulloverInM =
    5.0;

// Distance buffer to pullover ref path to consider we can do left nudge for
// oncoming agent.
constexpr double kPulloverClearanceOutsideJumpInAreaToConsiderYieldInM = 2.5;
constexpr double kPulloverClearanceInsideJumpInAreaToConsiderYieldInM = 1.0;

// Minimum reference speed during pullover duration estimate.
constexpr double kMinReferenceSpeedInPulloverInMps = 1.5;

// Estimates duration to finish pullover.
double EstimateDurationToFinishPullover(
    const pull_over::JumpInGuidance& jump_in_guidance, double ego_arclength_m,
    double ego_speed_mps, double pullover_destination_arclength_m) {
  // Below shows what does jump in start and end means in a pullover ref path.
  //----------------------------------------------------------------------------
  //
  // ____________________*   jump in start
  //                       \
  //                          \     jump in end      pullover_destination_pt
  //                             \*__________________*___________
  //                             pullover_ref_path
  //----------------------------------------------------------------------------
  //
  const double jump_in_start_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_start_ref_path_arc_length();
  const double jump_in_end_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_end_ref_path_arc_length();
  const double jump_in_start_ref_speed_mps =
      jump_in_guidance.kinematic_info()
          .jump_in_start_min_comfort_speed_ref_limit();
  const double jump_in_end_ref_speed_mps =
      std::max(jump_in_guidance.kinematic_info()
                   .jump_in_end_min_comfort_speed_ref_limit(),
               kMinReferenceSpeedInPulloverInMps);
  const double jump_in_end_to_destination_m =
      pullover_destination_arclength_m - jump_in_end_arclength_m;
  const double ego_start_ref_speed_mps =
      std::max(ego_speed_mps, kMinReferenceSpeedInPulloverInMps);
  if (jump_in_end_to_destination_m <= 0.0) {
    // If destination is before jump in end, just use naive constant
    // deceleration model to estimate the pullover time.
    return std::max((pullover_destination_arclength_m - ego_arclength_m) /
                        (ego_start_ref_speed_mps * 0.5),
                    math::constants::kEpsilon);
  }

  double estimated_finish_pullover_time = 0.0;
  const double jump_in_end_to_destination_time =
      jump_in_end_to_destination_m / (jump_in_end_ref_speed_mps * 0.5);
  if (ego_arclength_m < jump_in_start_arclength_m) {
    const double ego_avg_speed_to_jump_in_start =
        (ego_start_ref_speed_mps + jump_in_start_ref_speed_mps) * 0.5;
    const double ego_to_jump_in_start_time =
        (jump_in_start_arclength_m - ego_arclength_m) /
        ego_avg_speed_to_jump_in_start;
    const double ego_to_jump_in_end_time =
        ego_to_jump_in_start_time +
        (jump_in_end_arclength_m - jump_in_start_arclength_m) /
            ((jump_in_start_ref_speed_mps + jump_in_end_ref_speed_mps) * 0.5);
    estimated_finish_pullover_time =
        ego_to_jump_in_end_time + jump_in_end_to_destination_time;
  } else if (ego_arclength_m > jump_in_end_arclength_m) {
    estimated_finish_pullover_time =
        (pullover_destination_arclength_m - ego_arclength_m) /
        (ego_start_ref_speed_mps * 0.5);
  } else {
    const double ego_to_jump_in_end_time =
        (jump_in_end_arclength_m - ego_arclength_m) /
        ((ego_start_ref_speed_mps + jump_in_end_ref_speed_mps) * 0.5);
    estimated_finish_pullover_time =
        ego_to_jump_in_end_time + jump_in_end_to_destination_time;
  }

  return std::max(estimated_finish_pullover_time, math::constants::kEpsilon);
}

// Determines whether Ego should pause pulling in but yield for the right
// side oncoming agent.
bool ShouldYieldForRightSideOncomingAgentDuringPullOver(
    const AgentTrajectoryInLaneStates& agent_trajectory,
    const LateralBlockingStateMetaData& blocking_state_data,
    const pull_over::JumpInGuidance& jump_in_guidance,
    double pullover_destination_arclength_m, double ego_speed_mps,
    double ego_arclength_m, std::string& debug_str) {
  if (blocking_state_data.reasoning_info
          .is_primarilly_out_of_physical_boundary() ||
      blocking_state_data.reasoning_info.tracked_side_to_ego() ==
          math::pb::kLeft) {
    // If oncoming agent is primarilly out of physical boundary or it is on left
    // of Ego, no need to consider it.
    absl::StrAppend(
        &debug_str,
        "Oncoming agent is out of physical boundary or at left side of ego. ");
    return false;
  }

  const double jump_in_start_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_start_ref_path_arc_length();
  const double jump_in_end_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_end_ref_path_arc_length();
  for (size_t i = 0; i < agent_trajectory.predicted_states.size(); ++i) {
    const auto& state = agent_trajectory.predicted_states[i];
    const bool is_state_inside_jump_in_region = math::IsInRange(
        state.inlane_state.inlane_param.full_body_start_arclength_m,
        jump_in_start_arclength_m, jump_in_end_arclength_m);
    const double inlane_clearance_threshold_m =
        is_state_inside_jump_in_region
            ? kPulloverClearanceInsideJumpInAreaToConsiderYieldInM
            : kPulloverClearanceOutsideJumpInAreaToConsiderYieldInM;
    const bool consider_nudge_left =
        (state.inlane_state.inlane_param.center_line_side == math::pb::kRight ||
         state.inlane_state.inlane_param.center_line_clearance_m <
             inlane_clearance_threshold_m);
    if (!consider_nudge_left) {
      // If the oncoming agent is not considered nudge left, or it is fully
      // blocking the lane, don't add the repulsion.
      absl::StrAppend(&debug_str,
                      "Oncoming agent is not consider nudge left. ");
      return false;
    }
    if (i == agent_trajectory.predicted_states.size() - 1 &&
        state.odom_m < kMinAgentPredictedMovementToAddPulloverRepulsionInM) {
      // If the oncoming agent is not moving much, meaning it could be
      // yielding for us, don't add the repulsion for it.
      absl::StrAppend(&debug_str,
                      "Oncoming agent predicted traj not long enough. ");
      return false;
    }
  }

  const auto& last_state = agent_trajectory.predicted_states.back();
  const auto& first_state = agent_trajectory.predicted_states.front();
  const double first_state_timestamp = math::Ms2Sec(first_state.timestamp);
  if (last_state.inlane_state.inlane_param.full_body_start_arclength_m >
      pullover_destination_arclength_m) {
    // Oncoming agent won't reach pullover destination. No need to worry about
    // it.
    absl::StrAppend(
        &debug_str,
        "Oncoming agent last state not crossing pullover destination. ");
    return false;
  }
  if (first_state.inlane_state.inlane_param.full_body_start_arclength_m >
          ego_arclength_m &&
      first_state.inlane_state.inlane_param.full_body_start_arclength_m <
          pullover_destination_arclength_m) {
    // Oncoming agent is between ego and pullover destination, yield for it.
    absl::StrAppend(&debug_str,
                    "Oncoming agent is between Ego and pullover destination. ");
    return true;
  }

  // Since Ego will gradually decelerating to reach pullover destination, if
  // oncoming agent cannot reach pullover destination before Ego already reached
  // it. Then there won't be any interaction between Ego and the oncoming agent
  // and we don't need to yield for it. On the other hand, if oncoming agent has
  // crossed pullover destination before the estimated time for Ego to reach it,
  // then we want to yield for it.
  const double estimated_pullover_time = EstimateDurationToFinishPullover(
      jump_in_guidance, ego_arclength_m, ego_speed_mps,
      pullover_destination_arclength_m);
  if (estimated_pullover_time >
      math::Ms2Sec(last_state.timestamp) - first_state_timestamp) {
    // If prediction horizon is shorter than estimated pullover finish duration,
    // it means oncoming agent will cross destination point before Ego finish
    // pullover. Consider it.
    absl::StrAppend(&debug_str,
                    "Pullover finish horizon longer than prediction horizon. ");
    return true;
  }

  const int64_t timestamp_to_find =
      first_state.timestamp + math::Sec2Ms(estimated_pullover_time);
  const auto oncoming_state_iter = std::lower_bound(
      agent_trajectory.predicted_states.begin(),
      agent_trajectory.predicted_states.end(), timestamp_to_find,
      [](const auto& state, int64_t value) { return state.timestamp < value; });
  DCHECK(oncoming_state_iter != agent_trajectory.predicted_states.end());

  // Calculate the arclength of oncoming agent when Ego finishes pullover to see
  // if it has interaction with Ego or not.
  const double oncoming_state_arclength_m =
      oncoming_state_iter->inlane_state.inlane_param
          .full_body_start_arclength_m;
  absl::StrAppend(&debug_str,
                  "Checking arclength diff when ego finish pullover. ");
  return oncoming_state_arclength_m <
         pullover_destination_arclength_m +
             kDistanceBufferForYieldingForOncomingAgentDuringPulloverInM;
}

bool ShouldYieldForRightSideOncomingAgentDuringPullOver(
    const ObjectTrajectoryStates& agent_trajectory,
    const LateralBlockingStateMetaData& blocking_state_data,
    const pull_over::JumpInGuidance& jump_in_guidance,
    double pullover_destination_arclength_m, double ego_speed_mps,
    double ego_arclength_m, std::string& debug_str) {
  if (blocking_state_data.reasoning_info
          .is_primarilly_out_of_physical_boundary() ||
      blocking_state_data.reasoning_info.tracked_side_to_ego() ==
          math::pb::kLeft) {
    // If oncoming agent is primarilly out of physical boundary or it is on left
    // of Ego, no need to consider it.
    absl::StrAppend(
        &debug_str,
        "Oncoming agent is out of physical boundary or at left side of ego. ");
    return false;
  }

  const double jump_in_start_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_start_ref_path_arc_length();
  const double jump_in_end_arclength_m =
      jump_in_guidance.kinematic_info().jump_in_end_ref_path_arc_length();
  for (size_t i = 0; i < agent_trajectory.predicted_states().size(); ++i) {
    const auto& state = agent_trajectory.predicted_states()[i];
    const bool is_state_inside_jump_in_region = math::IsInRange(
        state.object_occupancy_param().full_body_start_arclength_m,
        jump_in_start_arclength_m, jump_in_end_arclength_m);
    const double inlane_clearance_threshold_m =
        is_state_inside_jump_in_region
            ? kPulloverClearanceInsideJumpInAreaToConsiderYieldInM
            : kPulloverClearanceOutsideJumpInAreaToConsiderYieldInM;
    const bool consider_nudge_left =
        (state.object_occupancy_param().center_line_side == math::pb::kRight ||
         state.object_occupancy_param().center_line_clearance_m <
             inlane_clearance_threshold_m);
    if (!consider_nudge_left) {
      // If the oncoming agent is not considered nudge left, or it is fully
      // blocking the lane, don't add the repulsion.
      absl::StrAppend(&debug_str,
                      "Oncoming agent is not consider nudge left. ");
      return false;
    }
    if (i == agent_trajectory.predicted_states().size() - 1 &&
        state.odom_m() < kMinAgentPredictedMovementToAddPulloverRepulsionInM) {
      // If the oncoming agent is not moving much, meaning it could be
      // yielding for us, don't add the repulsion for it.
      absl::StrAppend(&debug_str,
                      "Oncoming agent predicted traj not long enough. ");
      return false;
    }
  }

  const auto& last_state = agent_trajectory.predicted_states().back();
  const auto& first_state = agent_trajectory.predicted_states().front();
  const double first_state_timestamp = math::Ms2Sec(first_state.timestamp());
  if (last_state.object_occupancy_param().full_body_start_arclength_m >
      pullover_destination_arclength_m) {
    // Oncoming agent won't reach pullover destination. No need to worry about
    // it.
    absl::StrAppend(
        &debug_str,
        "Oncoming agent last state not crossing pullover destination. ");
    return false;
  }
  if (first_state.object_occupancy_param().full_body_start_arclength_m >
          ego_arclength_m &&
      first_state.object_occupancy_param().full_body_start_arclength_m <
          pullover_destination_arclength_m) {
    // Oncoming agent is between ego and pullover destination, yield for it.
    absl::StrAppend(&debug_str,
                    "Oncoming agent is between Ego and pullover destination. ");
    return true;
  }

  // Since Ego will gradually decelerating to reach pullover destination, if
  // oncoming agent cannot reach pullover destination before Ego already reached
  // it. Then there won't be any interaction between Ego and the oncoming agent
  // and we don't need to yield for it. On the other hand, if oncoming agent has
  // crossed pullover destination before the estimated time for Ego to reach it,
  // then we want to yield for it.
  const double estimated_pullover_time = EstimateDurationToFinishPullover(
      jump_in_guidance, ego_arclength_m, ego_speed_mps,
      pullover_destination_arclength_m);
  if (estimated_pullover_time >
      math::Ms2Sec(last_state.timestamp()) - first_state_timestamp) {
    // If prediction horizon is shorter than estimated pullover finish duration,
    // it means oncoming agent will cross destination point before Ego finish
    // pullover. Consider it.
    absl::StrAppend(&debug_str,
                    "Pullover finish horizon longer than prediction horizon. ");
    return true;
  }
  const auto& oncoming_state_iter = std::find_if(
      agent_trajectory.predicted_states().begin(),
      agent_trajectory.predicted_states().end(),
      [estimated_pullover_time, first_state_timestamp](const auto& state) {
        return math::Ms2Sec(state.timestamp()) - first_state_timestamp >
               estimated_pullover_time;
      });
  DCHECK(oncoming_state_iter != agent_trajectory.predicted_states().end());

  // Calculate the arclength of oncoming agent when Ego finishes pullover to see
  // if it has interaction with Ego or not.
  const double oncoming_state_arclength_m =
      oncoming_state_iter->object_occupancy_param().full_body_start_arclength_m;
  absl::StrAppend(&debug_str,
                  "Checking arclength diff when ego finish pullover. ");
  return oncoming_state_arclength_m <
         pullover_destination_arclength_m +
             kDistanceBufferForYieldingForOncomingAgentDuringPulloverInM;
}

// Adjust the nudge range for oncoming agent during pullover to
// avoid over reacting.
void AdjustNudgeSnapshotsForOncomingAgentDuringPullover(
    IntentionResultMetaData& intention_result) {
  if (intention_result.nudge_index_ranges.empty()) {
    return;
  }
  // For now, the nudge index ranges size will only be 1.
  DCHECK_EQ(intention_result.nudge_index_ranges.size(), 1);
  const math::Range<int>& st_collision_range =
      intention_result.nudge_index_ranges.front();
  DCHECK(IsValidRange(st_collision_range));
  // Only consider max prediction horizon of 4.0s for oncoming agent during
  // pullover.
  constexpr int kMaxHorizonToConsiderNudgeOncomingDuringPullover = 40;
  if (intention_result.nudge_index_ranges.front().start_pos >=
      kMaxHorizonToConsiderNudgeOncomingDuringPullover) {
    // If the nudge index range is already after the max horizon, only nudge the
    // start snapshot.
    absl::StrAppend(&intention_result.debug_str,
                    "No need to react to 4s later predicted oncoming snapshots "
                    "during jump in pullover.");
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    intention_result.is_overtaken = false;
    return;
  }
  intention_result.nudge_index_ranges.front().end_pos =
      std::min(intention_result.nudge_index_ranges.front().end_pos,
               kMaxHorizonToConsiderNudgeOncomingDuringPullover);
  DCHECK(IsValidRange(intention_result.nudge_index_ranges.front()));
  return;
}

}  // namespace

OncomingAgentPathReasoner::OncomingAgentPathReasoner(
    const EgoContextInfo& ego_context_info)
    : AbstractReasoner(ego_context_info, pb::AgentReasonerId::ONCOMING_AGENT) {
  st_buffer_config_.set_spatial_bottom_buffer_m(1.0);
  st_buffer_config_.set_spatial_top_buffer_m(0.0);
  st_buffer_config_.set_temporal_left_buffer_s(0.0);
  st_buffer_config_.set_temporal_right_buffer_s(0.3);
}

bool OncomingAgentPathReasoner::PreReasonForSoftBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForSoftBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool OncomingAgentPathReasoner::PreReasonForSoftBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForSoftBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool OncomingAgentPathReasoner::PreReasonForHardBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool OncomingAgentPathReasoner::PreReasonForHardBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool OncomingAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const AgentInLaneStates& /* agent_inlane_state */,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // If there has soft blocking sequence for oncoming, we will change
  // hard_blocking to soft_blocking, and try to give nudge intention.
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  pb::BlockingSequence blocking_sequence = GetBlockingSequence(
      /*start_index=*/blocking_sequences.front().range().start_index(),
      /*end_index=*/blocking_sequences.back().range().end_index(),
      /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
  *blocking_sequence.mutable_buffer_config() = st_buffer_config_;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kPartialBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};
  return true;
}

bool OncomingAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const ObjectOccupancyState& /* object_occupancy_state */,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  // If there has soft blocking sequence for oncoming, we will change
  // hard_blocking to soft_blocking, and try to give nudge intention.
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  pb::BlockingSequence blocking_sequence = GetBlockingSequence(
      /*start_index=*/blocking_sequences.front().range().start_index(),
      /*end_index=*/blocking_sequences.back().range().end_index(),
      /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
  *blocking_sequence.mutable_buffer_config() = st_buffer_config_;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kPartialBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};
  return true;
}

std::unique_ptr<RepulsionMetaData>
OncomingAgentPathReasoner::PreRepulsionReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    double ego_speed_mps, const RequiredLateralGap& required_lat_gap,
    std::string& debug_str) const {
  const AgentTrajectoryInLaneStates& agent_traj =
      agent_inlane_state.predicted_trajectories.at(
          agent_inlane_state.primary_trajectory_id);

  DCHECK(pull_over_status_meta_.has_value());
  const double pull_over_destination_arclength_m =
      pull_over_status_meta_->pullover_reference_path
          .GetProximity(pull_over_status_meta_->pull_over_destination,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (!ShouldYieldForRightSideOncomingAgentDuringPullOver(
          agent_traj, blocking_state_data,
          pull_over_status_meta_->jump_in_guidance.value(),
          pull_over_destination_arclength_m, ego_speed_mps,
          ego_param_.arclength_m, debug_str)) {
    absl::StrAppend(&debug_str, "Shouldn't yield for right side oncoming. ");
    return nullptr;
  }

  const std::optional<AgentPredictedSpeedProfileAndPath>
      agent_predicted_profile_and_path =
          CalcAgentPredictedSpeedProfileAndPath(agent_traj);
  if (!agent_predicted_profile_and_path.has_value()) {
    absl::StrAppend(&debug_str, "Don't get valid predicted profile and path. ");
    return nullptr;
  }

  // Customize a ego decelerating speed profile with -3mpss deceleration.
  speed::Profile customized_guided_profile;
  const auto decelerating_traj = physics::CreateLinearTrajectoryInConstAccel(
      /*acceleration=*/-3.0,
      /*init_timestamp=*/agent_traj.predicted_states[0].timestamp,
      /*init_speed=*/ego_speed_mps,
      /*init_heading=*/0.0, /*init_pos_x=*/0.0, /*init_pos_y=*/0.0,
      /*init_odom=*/ego_param_.arclength_m,
      /*speed_lower_bound=*/0.0, /*speed_upper_bound=*/100.0,
      /*odom_upper_bound=*/1000.0,
      /*max_duration=*/8.0, planner::constants::kTrajectoryIntervalInSec);
  for (const auto& pose : decelerating_traj) {
    customized_guided_profile.emplace_back(math::Ms2Sec(pose.timestamp()),
                                           pose.odom(), pose.speed(),
                                           /*a_in=*/0.0, /*j_in=*/0.0);
  }
  const auto& agent_current_pose =
      agent_inlane_state.tracked_state.inlane_param.pose;
  absl::StrAppend(&debug_str, "Add repulsion for right side oncoming agent. ");
  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kLeft,
      agent_current_pose.contour().polygon(),
      agent_predicted_profile_and_path->predicted_path,
      agent_predicted_profile_and_path->predicted_speed_profile,
      agent_predicted_profile_and_path->predicted_speed_profile,
      agent_current_pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      required_lat_gap.comfort_required_lateral_gap, reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      agent_inlane_state.primary_trajectory_id,
      /*path_risk_mitigation_mode_in=*/false, std::nullopt, std::nullopt,
      std::nullopt, RepulsionMetaData::GuideProfileType::kCustomized,
      /*customized_guide_profile_in=*/
      std::make_optional<speed::Profile>(customized_guided_profile),
      /*extend_boundary_level_in=*/
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/std::nullopt, []() {
        rt_event::PostRtEvent<
            rt_event::planner::
                TriggerPulloverRepulsionForRightSideOncomingAgent>();
      });
}

std::unique_ptr<RepulsionMetaData>
OncomingAgentPathReasoner::PreRepulsionReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    double ego_speed_mps, const RequiredLateralGap& required_lat_gap,
    std::string& debug_str) const {
  const ObjectTrajectoryStates& agent_traj =
      object_occupancy_state.predicted_trajectory_occupancy_states().at(
          object_occupancy_state.primary_trajectory_id());

  DCHECK(pull_over_status_meta_.has_value());
  const double pull_over_destination_arclength_m =
      pull_over_status_meta_->pullover_reference_path
          .GetProximity(pull_over_status_meta_->pull_over_destination,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  if (!ShouldYieldForRightSideOncomingAgentDuringPullOver(
          agent_traj, blocking_state_data,
          pull_over_status_meta_->jump_in_guidance.value(),
          pull_over_destination_arclength_m, ego_speed_mps,
          ego_param_.arclength_m, debug_str)) {
    absl::StrAppend(&debug_str, "Shouldn't yield for right side oncoming. ");
    return nullptr;
  }

  const std::optional<AgentPredictedSpeedProfileAndPath>
      agent_predicted_profile_and_path =
          CalcAgentPredictedSpeedProfileAndPath(agent_traj);
  if (!agent_predicted_profile_and_path.has_value()) {
    absl::StrAppend(&debug_str, "Don't get valid predicted profile and path. ");
    return nullptr;
  }

  // Customize a ego decelerating speed profile.
  speed::Profile customized_guided_profile;
  const auto decelerating_traj = physics::CreateLinearTrajectoryInConstAccel(
      /*acceleration=*/-3.0,
      /*init_timestamp=*/agent_traj.predicted_states()[0].timestamp(),
      /*init_speed=*/ego_speed_mps,
      /*init_heading=*/0.0, /*init_pos_x=*/0.0, /*init_pos_y=*/0.0,
      /*init_odom=*/ego_param_.arclength_m,
      /*speed_lower_bound=*/0.0, /*speed_upper_bound=*/100.0,
      /*odom_upper_bound=*/1000.0,
      /*max_duration=*/8.0, planner::constants::kTrajectoryIntervalInSec);
  for (const auto& pose : decelerating_traj) {
    customized_guided_profile.emplace_back(math::Ms2Sec(pose.timestamp()),
                                           pose.odom(), pose.speed(),
                                           /*a_in=*/0.0, /*j_in=*/0.0);
  }
  const auto& agent_current_pose = object_occupancy_state.pose();
  absl::StrAppend(&debug_str, "Add repulsion for right side oncoming agent. ");
  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kLeft,
      agent_current_pose.contour().polygon(),
      agent_predicted_profile_and_path->predicted_path,
      agent_predicted_profile_and_path->predicted_speed_profile,
      agent_predicted_profile_and_path->predicted_speed_profile,
      agent_current_pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      required_lat_gap.comfort_required_lateral_gap, reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      object_occupancy_state.primary_trajectory_id(),
      /*path_risk_mitigation_mode_in=*/false, std::nullopt, std::nullopt,
      std::nullopt, RepulsionMetaData::GuideProfileType::kCustomized,
      /*customized_guide_profile_in=*/
      std::make_optional<speed::Profile>(customized_guided_profile),
      /*extend_boundary_level_in=*/
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/std::nullopt, []() {
        rt_event::PostRtEvent<
            rt_event::planner::
                TriggerPulloverRepulsionForRightSideOncomingAgent>();
      });
}

bool OncomingAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          tracked_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego(),
          tracked_param.left_inlane_clearance_m,
          tracked_param.right_inlane_clearance_m);
  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      tracked_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update ped RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, agent_inlane_state, ego_param_.arclength_m,
      buffer_before_agent_meters);
  if (agent_inlane_state.agent_metadata.agent_type == voy::perception::PED) {
    MaybeAdjustPedLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
        /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
        agent_inlane_state.agent_metadata.agent_type,
        intention_result.required_lat_gap, &(intention_result.debug_str));
  }

  MaybeAdjustLateralGapBasedOnTtc(
      blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_.ego_speed_mps,
      agent_inlane_state.agent_metadata.agent_type,
      traffic_rule_reasoning_info_, intention_result.lateral_decision,
      intention_result.required_lat_gap, &(intention_result.debug_str));

  // Update cyc RLG based on speed.
  constexpr double kMaxAbsCrossTrackSpeedMps = 0.5;
  const bool is_high_confidence_non_crossing_agent =
      std::fabs(tracked_param.cross_track_speed_mps) <
      kMaxAbsCrossTrackSpeedMps;
  const bool is_highly_interactive_agent_during_pullover =
      (behavior_type_ == pb::DECOUPLED_PULL_OVER &&
       pullover_jump_in_type_ ==
           planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN &&
       blocking_state_data.reasoning_info.tracked_side_to_ego() !=
           math::pb::kLeft);
  if (FLAGS_planning_enable_speed_based_rlg_for_oncoming_cyclist &&
      agent_inlane_state.agent_metadata.agent_type ==
          voy::perception::CYCLIST &&
      is_high_confidence_non_crossing_agent &&
      !is_highly_interactive_agent_during_pullover) {
    MaybeAdjustCycLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, tracked_param.along_track_speed_mps,
        active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
        intention_result.lateral_decision,
        blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
        agent_inlane_state.agent_metadata.agent_type,
        intention_result.required_lat_gap, &(intention_result.debug_str));
    rt_event::PostRtEvent<rt_event::planner::UseSpeedBasedRlgForOncomingCyc>();
  }

  // No need change the st planner's dicision.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) {
    if (is_highly_interactive_agent_during_pullover) {
      // If Ego is doing jump in pullover, and oncoming agent is not at Ego's
      // left side, then it means there could be large interaction between Ego
      // and agent, we shouldn't nudge agent's snapshot that are far in the
      // future.
      AdjustNudgeSnapshotsForOncomingAgentDuringPullover(intention_result);
    }
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  // When the ego vehicle cannot yield but can nudge safely, it needs to provide
  // a nudge decision.
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      IsSafeNecessaryCrossLaneNudge(intention_result.nudge_index_ranges,
                                    tracked_pass_decision,
                                    traffic_rule_reasoning_info_)) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str, "Unable to yield");

    const auto& predicted_states =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               agent_inlane_state.primary_trajectory_id)
            .predicted_states;
    DCHECK(!predicted_states.empty());
    absl::StrAppend(
        &intention_result.debug_str,
        absl::StrCat(" origin_range: ",
                     intention_result.nudge_index_ranges.front().start_pos,
                     ", ",
                     intention_result.nudge_index_ranges.front().end_pos));
    // Trims the nudge index range to avoid harsh swerve.
    AdjustNudgeRangeBasedOnKinematics(
        ego_param_, tracked_param, predicted_states, nudge_motion_checker(),
        /*is_oncoming_agent=*/true,
        agent_inlane_state.agent_metadata.agent_type, intention_result);
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  return true;
}

bool OncomingAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const auto& occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          occupancy_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego(),
          occupancy_param.left_boundary_clearance_m,
          occupancy_param.right_boundary_clearance_m);
  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      occupancy_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update ped RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, object_occupancy_state,
      ego_param_.arclength_m, buffer_before_agent_meters);
  if (object_occupancy_state.object_type() == voy::perception::PED) {
    MaybeAdjustPedLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, active_lat_gap_adjust_meta_,
        /*ego_speed=*/ego_speed_near_agent, intention_result.lateral_decision,
        object_occupancy_state.object_type(), intention_result.required_lat_gap,
        &(intention_result.debug_str));
  }

  MaybeAdjustLateralGapBasedOnTtc(
      blocking_state_data.reasoning_info,
      active_lat_gap_adjust_meta_.ego_speed_mps,
      object_occupancy_state.object_type(), traffic_rule_reasoning_info_,
      intention_result.lateral_decision, intention_result.required_lat_gap,
      &(intention_result.debug_str));

  // Update cyc RLG based on speed.
  constexpr double kMaxAbsCrossTrackSpeedMps = 0.5;
  const bool is_high_confidence_non_crossing_agent =
      std::fabs(occupancy_param.cross_track_speed_mps) <
      kMaxAbsCrossTrackSpeedMps;
  const bool is_highly_interactive_agent_during_pullover =
      (behavior_type_ == pb::DECOUPLED_PULL_OVER &&
       pullover_jump_in_type_ ==
           planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN &&
       blocking_state_data.reasoning_info.tracked_side_to_ego() !=
           math::pb::kLeft);
  if (FLAGS_planning_enable_speed_based_rlg_for_oncoming_cyclist &&
      object_occupancy_state.object_type() == voy::perception::CYCLIST &&
      is_high_confidence_non_crossing_agent &&
      !is_highly_interactive_agent_during_pullover) {
    MaybeAdjustCycLateralGapBasedOnSpeed(
        traffic_rule_reasoning_info_, occupancy_param.along_track_speed_mps,
        active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
        intention_result.lateral_decision,
        blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
        object_occupancy_state.object_type(), intention_result.required_lat_gap,
        &(intention_result.debug_str));
    rt_event::PostRtEvent<rt_event::planner::UseSpeedBasedRlgForOncomingCyc>();
  }

  // No need change the st planner's dicision.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) {
    if (is_highly_interactive_agent_during_pullover) {
      // If Ego is doing jump in pullover, and oncoming agent is not at Ego's
      // left side, then it means there could be large interaction between Ego
      // and agent, we shouldn't nudge agent's snapshot that are far in the
      // future.
      AdjustNudgeSnapshotsForOncomingAgentDuringPullover(intention_result);
    }
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  // When the ego vehicle cannot yield but can nudge safely, it needs to provide
  // a nudge decision.
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      IsSafeNecessaryCrossLaneNudge(intention_result.nudge_index_ranges,
                                    tracked_pass_decision,
                                    traffic_rule_reasoning_info_)) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str, "Unable to yield");
    const auto& predicted_states =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            object_occupancy_state.primary_trajectory_id())
            .predicted_states();
    DCHECK(!predicted_states.empty());
    absl::StrAppend(
        &intention_result.debug_str,
        absl::StrCat(" origin_range: ",
                     intention_result.nudge_index_ranges.front().start_pos,
                     ", ",
                     intention_result.nudge_index_ranges.front().end_pos));
    // Trims the nudge index range to avoid harsh swerve.
    AdjustNudgeRangeBasedOnKinematics(
        ego_param_, occupancy_param, predicted_states, nudge_motion_checker(),
        /*is_oncoming_agent=*/true, object_occupancy_state.object_type(),
        intention_result);
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  return true;
}

}  // namespace path
}  // namespace planner
