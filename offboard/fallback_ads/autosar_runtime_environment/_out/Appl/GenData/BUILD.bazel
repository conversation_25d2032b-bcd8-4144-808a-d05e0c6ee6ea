package(default_visibility = ["//visibility:public"])

cc_library(
    name = "os_compiler_cfg",
    hdrs = ["Os_Compiler_Cfg.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData",
)

cc_library(
    name = "rte_compiler_cfg",
    hdrs = ["Rte_Compiler_Cfg.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData",
)

cc_library(
    name = "rte_data_handle_type",
    hdrs = ["Rte_DataHandleType.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData",
)

cc_library(
    name = "rte_mem_map",
    hdrs = ["Rte_MemMap.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData",
)

cc_library(
    name = "rte_type",
    hdrs = ["Rte_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_include",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_mem_map",
    ],
)

cc_library(
    name = "rte",
    srcs = ["Rte.c"],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_include",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_mem_map",
    ],
)

cc_library(
    name = "rte_include",
    hdrs = ["Rte.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/BSW/Bsw/_Common:std_types",
    ],
)
