#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_TRAJECTORY_REASONER_TRAJECTORY_REASONER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_TRAJECTORY_REASONER_TRAJECTORY_REASONER_H_

#include <map>
#include <string>
#include <unordered_map>
#include <vector>
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/decoupled_maneuvers/geometric_guidance/geometric_guidance_generator_util.h"
#include "planner/decoupled_maneuvers/path/motion_engine/nudge_motion_checker.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/reasoner/abstract_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/cross_agent_path_reasoner/cross_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/cyclist_path_reasoner/cyclist_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/dynamic_overtake_path_reasoner/dynamic_overtake_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/imaginary_agent_path_reasoner/imaginary_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/lane_change_agent_path_reasoner/lane_change_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/lead_and_merge_agent_path_reasoner/lead_and_merge_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/low_confidence_agent_path_reasoner/low_confidence_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/oncoming_agent_path_reasoner/oncoming_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/pedestrian_path_reasoner/pedestrian_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/pull_over_path_reasoner/pull_over_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/rear_end_agent_path_reasoner/rear_end_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/reversing_agent_path_reasoner/reversing_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/side_agent_path_reasoner/side_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/reasoner/static_agent_path_reasoner/static_agent_path_reasoner.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/scenario_identifier_util.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner_protos/trajectory_reasoning_info.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

// TrajectoryReasoner considers the  entire prediction trajectory of the agent
// and makes a decision on if we should PASS/IGNORE the trajectory.
class TrajectoryReasoner {
 public:
  TrajectoryReasoner() = delete;

  // TODO(hongda): Replace 'nudge_motion_checker' with 'constraint manager',
  // and move 'nudge_motion_checker' into 'constraint manager'.
  TrajectoryReasoner(const path::EgoContextInfo& ego_context_info,
                     const bool generate_intent_for_dynamic)
      : ego_param_(ego_context_info.ego_param),
        scenario_identify_result_(ego_context_info.scenario_identifier_result),
        traffic_rule_reasoning_info_(
            ego_context_info.traffic_rule_reasoning_info),
        generate_intent_for_dynamic_(generate_intent_for_dynamic),
        behavior_type_(ego_context_info.behavior_type),
        lane_keep_behavior_type_(ego_context_info.lane_keep_behavior_type),
        constraint_manager_(ego_context_info.path_constraint_manager),
        cross_agent_reasoner_(path::CrossAgentPathReasoner(ego_context_info)),
        cyclist_reasoner_(path::CyclistPathReasoner(ego_context_info)),
        fallback_reasoner_(path::AbstractReasoner(ego_context_info)),
        lead_and_merge_agent_reasoner_(
            path::LeadAndMergeAgentPathReasoner(ego_context_info)),
        oncoming_agent_reasoner_(
            path::OncomingAgentPathReasoner(ego_context_info)),
        pedestrian_reasoner_(path::PedestrianPathReasoner(ego_context_info)),
        reversing_agent_reasoner_(
            path::ReversingAgentPathReasoner(ego_context_info)),
        side_agent_reasoner_(path::SideAgentPathReasoner(ego_context_info)),
        static_agent_reasoner_(path::StaticAgentPathReasoner(ego_context_info)),
        imaginary_agent_path_reasoner_(
            path::ImaginaryAgentPathReasoner(ego_context_info)),
        lane_change_agent_reasoner_(
            path::LaneChangeAgentPathReasoner(ego_context_info)),
        rear_end_agent_path_reasoner_(
            path::RearEndAgentPathReasoner(ego_context_info)),
        low_confidence_agent_path_reasoner_(
            path::LowConfidenceAgentPathReasoner(ego_context_info)),
        pull_over_path_reasoner_(path::PullOverPathReasoner(ego_context_info)),
        dynamic_overtake_path_reasoner_(
            path::DynamicOvertakePathReasoner(ego_context_info)) {}

  TrajectoryIntentionMetaData GetTrajectoryIntentionMetaData(
      const AgentInLaneStates& agent_state,
      const RequiredLateralGap& required_lateral_gap,
      const RequiredLateralGap& required_lat_gap_for_pass_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentSLBoundary& object_sl_boundary,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const bool should_ignore_by_upstream,
      const std::optional<planner::pb::YieldIntentionData>& updated_yield_data,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const;

  TrajectoryIntentionMetaData GetTrajectoryIntentionMetaData(
      const ObjectOccupancyState& object_occupancy_state,
      const RequiredLateralGap& required_lateral_gap,
      const RequiredLateralGap& required_lat_gap_for_pass_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentSLBoundary& object_sl_boundary,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const bool should_ignore_by_upstream,
      const std::optional<planner::pb::YieldIntentionData>& updated_yield_data,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const;

  TrajectoryIntentionMetaData GetTrajectoryIntentionMetaData(
      const ConstructionZoneInLaneState& construction_zone_state,
      const RequiredLateralGap& required_lateral_gap,
      const RequiredLateralGap& required_lateral_gap_for_pass_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const AgentSLBoundary& object_sl_boundary,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const;

  std::vector<RepulsionMetaData> GetRepulsionMetaDatas(
      const RobotStateSnapshot& robot_state_snapshot,
      const AgentInLaneStates& agent_state,
      const RequiredLateralGap& required_lateral_gap,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const std::unordered_map<int, LateralBlockingStateMetaData>&
          trajectory_blocking_state_data_map,
      const std::optional<planner::pb::YieldIntentionData>&
          yield_intention_data,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent,
      std::unordered_map<pb::AgentReasonerId, std::string>&
          repulsion_reasoning_debugs) const;

  std::vector<RepulsionMetaData> GetRepulsionMetaDatas(
      const RobotStateSnapshot& robot_state_snapshot,
      const ObjectOccupancyState& object_occupancy_state,
      const RequiredLateralGap& required_lateral_gap,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const std::unordered_map<int, LateralBlockingStateMetaData>&
          trajectory_blocking_state_data_map,
      const std::optional<planner::pb::YieldIntentionData>&
          yield_intention_data,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      const TrajectoryIntentionMetaData& trajectory_intention_data,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent,
      std::unordered_map<pb::AgentReasonerId, std::string>&
          repulsion_reasoning_debugs) const;

  std::vector<RepulsionMetaData> GetRepulsionMetaData(
      const RobotStateSnapshot& robot_state_snapshot,
      const ConstructionZoneInLaneState& cz_inlane_state,
      pb::PathReasonerSeedPerAgent& path_reasoner_seed_per_agent) const;

  // After agent intention searcher, there might still be agents that we
  // should(or not) consider in path planning.
  void PostProcessAgentIntentionResult(
      const AgentInLaneStates& agent_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
      IntentionResultMetaData& intention_result) const;

  void PostProcessAgentIntentionResult(
      const ObjectOccupancyState& object_occupancy_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
      IntentionResultMetaData& intention_result) const;

  void PostProcessAgentIntentionResult(
      const ConstructionZoneInLaneState& construction_zone_state,
      const LateralBlockingStateMetaData& blocking_state_meta_data,
      const TrajectoryIntentionMetaData& trajectory_intention_meta_data,
      IntentionResultMetaData& intention_result) const;

  // Makes lane boundaries immutable (like solid lines). This prevents undesired
  // lane boundary crossing in tight spaces. Returns true if boundaries were
  // made immutable, false otherwise. Skips adjustment for pull-out behavior and
  // cross-lane nudge scenarios.
  bool AdjustSolidLineConstraint(
      std::map<TypedObjectId, IntentionResultMetaData>& intention_meta_data_map)
      const;

 private:
  [[maybe_unused]] const EgoInLaneParams& ego_param_;
  const path::ScenarioIdentifierResult& scenario_identify_result_;
  const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info_;

  bool generate_intent_for_dynamic_ = false;
  pb::BehaviorType behavior_type_ = pb::BehaviorType::UNDEFINED_BEHAVIOR_TYPE;
  [[maybe_unused]] pb::LaneKeepBehaviorType lane_keep_behavior_type_ =
      pb::LaneKeepBehaviorType::LK_NA;
  path::ConstraintManager* constraint_manager_;

  path::CrossAgentPathReasoner cross_agent_reasoner_;
  path::CyclistPathReasoner cyclist_reasoner_;
  path::AbstractReasoner fallback_reasoner_;
  path::LeadAndMergeAgentPathReasoner lead_and_merge_agent_reasoner_;
  path::OncomingAgentPathReasoner oncoming_agent_reasoner_;
  path::PedestrianPathReasoner pedestrian_reasoner_;
  path::ReversingAgentPathReasoner reversing_agent_reasoner_;
  path::SideAgentPathReasoner side_agent_reasoner_;
  path::StaticAgentPathReasoner static_agent_reasoner_;
  path::ImaginaryAgentPathReasoner imaginary_agent_path_reasoner_;
  path::LaneChangeAgentPathReasoner lane_change_agent_reasoner_;
  path::RearEndAgentPathReasoner rear_end_agent_path_reasoner_;
  path::LowConfidenceAgentPathReasoner low_confidence_agent_path_reasoner_;
  path::PullOverPathReasoner pull_over_path_reasoner_;
  path::DynamicOvertakePathReasoner dynamic_overtake_path_reasoner_;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_TRAJECTORY_REASONER_TRAJECTORY_REASONER_H_
