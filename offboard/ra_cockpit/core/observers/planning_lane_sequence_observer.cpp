#include "planning_lane_sequence_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

PlanningLaneSequenceObserver::PlanningLaneSequenceObserver(
    PlanningLaneSequenceCallback callback)
    : callback_(std::move(callback)) {}

bool PlanningLaneSequenceObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kPlanningLaneSequence);
}

std::shared_ptr<google::protobuf::Message>
PlanningLaneSequenceObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto pls_msg = std::make_shared<planner::pb::PlanningLaneSequence>();
  if (!pls_msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error PlanningLaneSequence message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(pls_msg, data_channel);
  }
  return pls_msg;
}

}  // namespace remote_assist
