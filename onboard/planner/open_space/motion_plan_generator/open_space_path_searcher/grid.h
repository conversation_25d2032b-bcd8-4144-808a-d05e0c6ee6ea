#ifndef ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_H_
#define ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_H_

#include <algorithm>
#include <limits>
#include <unordered_map>
#include <utility>
#include <vector>

#include <glog/logging.h>

#include "onboard/planner/open_space/constraints/motion_plan_problem.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_cell.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_params.h"
#include "planner_protos/nominal_path_planner_config.pb.h"

namespace planner {

namespace open_space {

// The class Grid provides input constraints and stores grid samples
// for open space search.
//     __________________________________    Grid
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |    x  x   GridCell with 4
//     |  .    .    .    .    .    .    |    x  x   bounding vertices
//     |  .    .    x    x    .    .    |
//     |  .    .    x    x    .    .    |    .      GridCellVertex
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |  .    .    .    .    .    .    |
//     |________________________________|
// y ^
//   |__> x
class Grid {
 public:
  explicit Grid(const pb::OpenSpaceGridConfig& config) : config_(config) {}

  // Updates the open space grid based on motion plan problem input.
  void Update(const MotionPlanProblem& motion_plan_problem);

  // Returns true if the queried xy point is within the grid xy range.
  bool IsWithinGridArea(const math::geometry::Point2d pt) const {
    return math::IsInRange(pt.x(), grid_params_.x_range.start_pos,
                           grid_params_.x_range.end_pos) &&
           math::IsInRange(pt.y(), grid_params_.y_range.start_pos,
                           grid_params_.y_range.end_pos);
  }

  // Returns the OccupancyClearanceInfo given a queried position.
  // NOTE: drivable_clearance_thresh indicates an user-specified threshold for
  // determining the minimum required clearance to occupancy for a queried
  // position to be considered drivable.
  OccupancyClearanceInfo GetDominantOccupancyClearanceInfo(
      const math::geometry::Point2d& pt,
      double drivable_clearance_thresh) const {
    const GridKey& snapped_key = SnapToGrid(pt);
    DCHECK(!snapped_key.IsNullKey());
    const GridCell& grid_cell = GetGridCell(snapped_key);
    return grid_cell.GetDominantOccupancyClearanceInfo(
        pt, drivable_clearance_thresh);
  }

  // Returns the heuristic distance to target pose given a queried position.
  double GetHeuristicDistanceToTarget(const math::geometry::Point2d& pt) const {
    const GridKey& snapped_key = SnapToGrid(pt);
    DCHECK(!snapped_key.IsNullKey());
    const GridCell& grid_cell = GetGridCell(snapped_key);
    return grid_cell.GetHeuristicDistanceToTarget(pt);
  }

  // Returns the corresponding open space node key based on input path state.
  OpenSpaceNodeKey GetOpenSpaceNodeKeyFromPathState(
      const PathState& node_path_state) const;

  // Emplaces a new open space node into grid. Returns a non-null open space
  // node pointer if the emplacment is successful.
  OpenSpaceNode* EmplaceOpenSpaceNode(OpenSpaceNode&& new_node);

  // Returns mutable and const open space node pointer given input path state or
  // node key.
  OpenSpaceNode* GetMutableOpenSpaceNode(const PathState& node_path_state);
  OpenSpaceNode* GetMutableOpenSpaceNode(const OpenSpaceNodeKey& node_key);
  const OpenSpaceNode* GetOpenSpaceNode(const OpenSpaceNodeKey& node_key) const;

  // Returns the key of the GridCell that contains the queried the x and y.
  GridKey SnapToGrid(const math::geometry::Point2d& pt) const;

  // Const accessors.
  const MotionPlanProblem& motion_plan_problem() const {
    return *DCHECK_NOTNULL(motion_plan_problem_ptr_);
  }
  const pb::OpenSpaceGridConfig& config() const { return config_; }
  const GridParams& grid_params() const { return grid_params_; }
  const GridCell& GetGridCell(const GridKey& key) const {
    return grid_cells_[GetFlattenedKeyForGridCell(key)];
  }

 private:
  // Initializes grid cell vertices with xy sample resolution and populates the
  // vertices' clearance infos to the occupancy constraints in the motion plan
  // problem.
  void PopulateGridCellVertices(
      const std::unordered_map<OccupancyConstraintID, Boundary>& boundaries,
      const std::unordered_map<OccupancyConstraintID, Contour>& contours,
      double ego_disk_radius);

  // Populates the vertices' clearance infos to the occupancy constraints in the
  // motion plan problem.
  void PopulateGridCellVertexOccupancyClearanceInfo(
      const std::unordered_map<OccupancyConstraintID, Boundary>& boundaries,
      const std::unordered_map<OccupancyConstraintID, Contour>& contours,
      double ego_disk_radius, GridCellVertex* vertex_ptr);

  // Populates GridCells based on sampled GridCellVertices.
  void PopulateGridCells();

  // Populates for each grid cell vertex the pure grid DP heuristic cost.
  // TODO(Zixuan): Adapt heurisitic distance generation to multiple target
  // poses.
  void PopulateGridCellVerticesHeuristicDistance(
      const math::geometry::Point2d& target_pose_pt);

  // Returns the flattened 2-D grid cell key.
  int GetFlattenedKeyForGridCell(const GridKey& key) const {
    DCHECK_LT(key.x_idx, grid_params_.x_cell_size);
    DCHECK_LT(key.y_idx, grid_params_.y_cell_size);
    return key.x_idx * grid_params_.y_cell_size + key.y_idx;
  }
  int GetFlattenedKeyForGridCellVertex(const GridKey& key) const {
    DCHECK_LT(key.x_idx, grid_params_.x_vertices_size);
    DCHECK_LT(key.y_idx, grid_params_.y_vertices_size);
    return key.x_idx * grid_params_.y_vertices_size + key.y_idx;
  }

  // Returns the GridKey converted from the flattened key.
  GridKey GetGridKeyFromFlattenedKey(int flattened_key) const {
    const int x_idx = flattened_key / grid_params_.y_cell_size;
    const int y_idx = flattened_key - x_idx * grid_params_.y_cell_size;
    return GridKey{x_idx, y_idx};
  }
  GridKey GetGridCellVertexKeyFromFlattenedKey(int flattened_key) const {
    const int x_idx = flattened_key / grid_params_.y_vertices_size;
    const int y_idx = flattened_key - x_idx * grid_params_.y_vertices_size;
    return GridKey{x_idx, y_idx};
  }

  // Private mutators.
  // Grid cells and vertices mutator for open_space_path_searcher.
  GridCell& GetMutableGridCell(const GridKey& key) {
    const int flattened_key = GetFlattenedKeyForGridCell(key);
    DCHECK_LT(flattened_key, grid_cells_.size());
    return grid_cells_[flattened_key];
  }
  GridCellVertex& GetMutableGridCellVertex(const GridKey& key) {
    const int flattened_key = GetFlattenedKeyForGridCellVertex(key);
    DCHECK_LT(flattened_key, grid_cell_vertices_.size());
    return grid_cell_vertices_[flattened_key];
  }

  // Private const accessors.
  const GridCellVertex& GetGridCellVertex(const GridKey& key) const {
    return grid_cell_vertices_[GetFlattenedKeyForGridCellVertex(key)];
  }
  const std::vector<GridCellVertex>& grid_cell_vertices() const {
    return grid_cell_vertices_;
  }
  const std::vector<GridCell>& grid_cells() const { return grid_cells_; }

  // Clear open space grid data.
  void ClearGridData() {
    grid_cell_vertices_.clear();
    grid_cells_.clear();
  }

  // The motion plan problem associated with this grid.
  const MotionPlanProblem* motion_plan_problem_ptr_ = nullptr;

  // Config parameters for constructing the grid for open space path search.
  const pb::OpenSpaceGridConfig& config_;

  // Grid specifications and grid data.
  GridParams grid_params_;
  std::vector<GridCellVertex> grid_cell_vertices_;
  std::vector<GridCell> grid_cells_;

  FRIEND_TEST(OpenSpaceGridUpdateTest, GridUpdateTest);
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_MOTION_PLAN_GENERATOR_OPEN_SPACE_PATH_SEARCHER_GRID_H_
