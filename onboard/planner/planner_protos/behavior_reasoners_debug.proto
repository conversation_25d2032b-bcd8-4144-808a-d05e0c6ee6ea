syntax = "proto3";

package planner.pb;

import "google/protobuf/wrappers.proto";

import "planner_protos/assist_stuck_debug.proto";
import "planner_protos/agent_inlane_state.proto";
import "planner_protos/agent_intention_generator_debug.proto";
import "planner_protos/behavior_reasoner.proto";
import "planner_protos/behavior_reasoner_config.proto";
import "planner_protos/behavior_reasoner_seed.proto";
import "planner_protos/common.proto";
import "planner_protos/construction_zone_inlane_state.proto";
import "planner_protos/cross_lane_debug.proto";
import "planner_protos/ego_stuck_feature.proto";
import "planner_protos/exception_handler.proto";
import "planner_protos/global_object_manager_debug.proto";
import "planner_protos/intention_plan_debug.proto";
import "planner_protos/lane_blockage.proto";
import "planner_protos/lane_change.proto";
import "planner_protos/lane_sequence_candidate.proto";
import "planner_protos/object_source_type.proto";
import "planner_protos/path_generator_debug.proto";
import "planner_protos/path_planner_debug.proto";
import "planner_protos/path_reasoning_seed.proto";
import "planner_protos/planning_lane_sequence.proto";
import "planner_protos/planning_seed.proto";
import "planner_protos/pull_over.proto";
import "planner_protos/pull_over_info_debug.proto";
import "planner_protos/regional_map.proto";
import "planner_protos/relax_lane_sequence_parameter.proto";
import "planner_protos/remote_assist.proto";
import "planner_protos/remote_assist_config.proto";
import "planner_protos/remote_warning_debug.proto";
import "planner_protos/route_preview.proto";
import "planner_protos/st_planner_debug.proto";
import "planner_protos/selection_debug.proto";
import "planner_protos/world_model_debug.proto";
import "prediction_protos/agent_maneuver.proto";
import "voy_protos/perception_object_type.proto";
import "voy_protos/point.proto";
import "voy_protos/polygon.proto";
import "voy_protos/tracked_objects.proto";
import "voy_protos/traffic_light.proto";
import "voy_protos/trajectory.proto";
import "voy_protos/math.proto";

// The debug for each single stop sign.
// Next Available ID: 5
message StopSignDebug {
  reserved 2, 3;
  int64 id = 1;
  int64 stopped_timestamp = 4;
}

// The debug for the stop sign reasoner.
// Next Available ID: 2
message StopSignReasonerDebug { repeated StopSignDebug stop_signs = 1; }

// The debug for trajectory state of occupant in crosswalk.
// Next Available ID: 9
message CrosswalkOccupantTrajectoryInYieldZoneState {
  // Trajectory's id.
  int64 id = 1;
  // The time difference between enter timestamp and planning start timestamp.
  int64 enter_time_delta = 2;
  // The time difference between exit timestamp and planning start timestamp.
  int64 exit_time_delta = 3;
  // The decision for one predicted trajectory in crosswalk reasoner.
  YieldToTrajectoryDecision object_decision = 4;
  // The crossing intention from prediction.
  prediction.pb.Maneuver.IntentionType intention_type_from_prediction = 5;
  // The crossing intention reasoned by rules.
  ObjectManeuverIntentionType intention_type_by_rules = 6;
  // The arclength range while crossing ego lane.
  double start_arclength_m = 7;
  double end_arclength_m = 8;
}

// The debug for object state in a yield zone.
// Next Available ID: 14
message ObjectStateInYieldZone {
  reserved 2, 4, 5, 6;
  // The distance between current pose and nominal path.
  double distance_pose_to_nominal_path = 1;
  // The heading delta between object and nominal path.
  double heading_delta = 3;
  // The yield zone order in crosswalk.
  CrosswalkYieldZoneOrder order_in_crosswalk = 7;
  // The trajectory state for decision in yield zone.
  repeated CrosswalkOccupantTrajectoryInYieldZoneState trajectory_states = 8;
  // The entry time buffer for stopping constraint.
  int64 entry_time_buffer = 9;
  // The heading delta between face heading and nominal path.
  double face_heading_delta = 10;
  // The heading delta between body heading and nominal path.
  double body_heading_delta = 11;
  // The ignore decision of the object in yield zone.
  YieldPreliminaryIgnoreDecision ignore_decision = 12;
  // The lateral gap that Ego should keep with agent when passing crosswalk.
  double required_lateral_gap = 13;
}

// The debug for yield zone state of crosswalk.
// Next Available ID: 11
message YieldZoneState {
  reserved 9;
  // The start position of the speed zone on the nominal path.
  double start_arclength_m = 1;
  // The end position of the speed zone on the nominal path.
  double end_arclength_m = 2;
  // The lateral heading of crosswalk from left to right.
  double lateral_heading = 3;
  // The virtual yield line position on the nominal path.
  double virtual_yield_line_arclength_m = 4;
  // The associated stop line position in the real world on the nominal path.
  double associated_stop_line_arclength_m = 5;
  // Whether ego has passed this yield zone.
  bool ego_has_passed = 6;
  // The points of the most likely pedestrian flow direction.
  repeated voy.Point2d pedestrian_flow_direction_points = 7;
  // The yield zone order in crosswalk.
  CrosswalkYieldZoneOrder order_in_crosswalk = 8;
  // The distance between Ego front bumper of current pose and the yield line of
  // this yield zone.
  double ego_to_yield_line_distance = 10;
}

// The debug for object that triggering crosswalk's directive.
// Next Available ID: 20
message CrosswalkOccupant {
  reserved 3, 4, 6, 7, 8, 9, 10, 13, 14, 16, 17;
  // Object's id.
  int64 id = 1;
  // Object's contour.
  repeated voy.Point2d contour = 2;
  // Object's type.
  voy.perception.ObjectType type = 11;
  // Object's speed, m/s.
  double speed = 12;
  // The blocking state from predicted trajectory.
  AgentSnapshotBlockingState blocking_state = 15;
  // Object's state in a crosswalk yield zone.
  repeated ObjectStateInYieldZone object_states_in_yield_zone = 18;
  // The estimated stationary time from historical poses.
  int64 stationary_time_in_ms = 19;
}

// The debug for each single crosswalk.
// Next Available ID: 22
message CrosswalkDebug {
  reserved 2, 3, 4, 6, 7, 9, 11, 12, 13, 14, 15, 18, 19, 20;
  // Crosswalk's id.
  int64 id = 1;
  // Its true value means the stop_line_arclength_m parameter is valid.
  bool has_stop_line = 5;
  // The expanded contour of crosswalk.
  repeated voy.Point2d expanded_contour = 8;
  // The field includes all objects that occupy the crosswalk.
  // The field is empty when crosswalk isn't occupied by any object.
  repeated CrosswalkOccupant crosswalk_occupants = 10;
  // The right-of-way of ego car compared with pedestrians.
  RightOfWayType right_of_way = 16;
  // Crosswalk's order in a range.
  CrosswalkOrder crosswalk_order = 17;
  // The yield zone states in crosswalk.
  repeated YieldZoneState yield_zone_states = 21;
}

// The debug for the crosswalk reasoner.
// Next Available ID: 2
message CrosswalkReasonerDebug { repeated CrosswalkDebug crosswalks = 1; }

// Next Available ID: 4
message CautiousDrivingAgentDebug {
  int64 ped_id = 1;
  double clearance_m = 2;
  double speed_limit = 3;
}

// The debug for the cautious driving scene.
// Next Available ID: 4
message CautiousDrivingSceneDebug {
  CautiousSceneType scene_type = 1;
  double speed_limit = 2;
  string scene_message = 3;
}

// The debug for the cautious driving reasoner.
// Next Available ID: 3
message CautiousDrivingReasonerDebug {
  repeated CautiousDrivingAgentDebug agents = 1;
  repeated CautiousDrivingSceneDebug scenes = 2;
}

// Next Available ID: 8
message YieldObject {
  reserved 2, 3, 5, 6, 7;
  reserved "is_ignorable_per_low_speed", "is_leading", "type",
      "yield_object_decision", "trajectory_states";

  // Yield object id and contour points.
  int64 id = 1;
  // Object's contour.
  repeated voy.Point2d contour = 4;
}

// Next Available ID: 10
message YieldConflictingRegion {
  reserved 3, 6;
  reserved "conflicting_start_arclength_m", "has_yield_object";
  // Conflictint lane id;
  int64 id = 1;
  // Is active.
  bool is_active = 2;
  // The contour of clearance checking region.
  repeated voy.Polygon2d clearance_checking_regions = 4;
  // The start arclength of this conflicting lane in nominal path.
  double start_arclength_m = 5;
  // The end arclength of this conflicting lane in nominal path.
  double end_arclength_m = 7;
  // The flag denotes whether ego could stop before the conflicting region
  // smoothly.
  bool can_ego_stop_smoothly = 8;
  // The flag denotes whether ego could stop before the conflicting region
  // extremely.
  bool can_ego_stop_aggressively = 9;
}

// Next Available ID: 15
message TrajectoryYieldResult {
  reserved 2, 6, 11;
  reserved "should_yield", "is_ignore_allowed";
  // Trajectory id.
  int64 id = 1;
  // Yield decision for object's trajectory.
  YieldToTrajectoryDecision trajectory_decision = 10;
  // Whether has encroachment in ego lane region.
  bool has_encroachment_in_ego_lane_region = 3;
  // Associated conflicting region id of this predicted trajectory.
  int64 conflicting_region_id = 4;
  // Whether should ignore due to harsh brake.
  bool should_ignore_due_to_harsh_brake = 5;
  // Whether the trajectory is associated with the conflicting region.
  bool is_object_associated_with_conflicting_region = 7;
  // Whether the trajectory is blocking the conflicting region.
  bool is_trajectory_blocking_conflicting_region = 8;
  // Whether the trajectory is going through the conflicting region.
  bool is_trajectory_going_through_conflicting_region = 9;
  // Temporal range of the trajectory crossing a conflicting range.
  // They are relative time relative to the plan start time.
  int64 crossing_temporal_range_start_ms = 12;
  int64 crossing_temporal_range_end_ms = 13;
  // Preliminary ignore decision.
  YieldPreliminaryIgnoreDecision preliminary_ignore_decision = 14;
}

// Next Available ID: 7
message TrackedObjectYieldResult {
  reserved 5;
  reserved "is_leading";
  // Tracked object id.
  int64 id = 1;
  // Whether should yield to this tracked object.
  bool should_yield = 2;
  // Whether this object is moving along ego lane region.
  bool is_moving_along_ego_lane_region = 3;
  // Whether should ignore due to low speed.
  bool should_ignore_due_to_low_speed = 4;
  // Predicted trajectory yield results.
  repeated TrajectoryYieldResult trajectory_yield_results = 6;
}

// Next Available ID: 6
message ConflictingRegionYieldResult {
  // Conflicting lane id;
  int64 id = 1;
  // Yield conflicting region.
  YieldConflictingRegion yield_conflicting_region = 2;
  // Whether it has stop constraints.
  bool has_stop_constraints = 3;
  // Whether it has speed constraints.
  bool has_speed_constraints = 4;
  // Whether it has object to yield to.
  bool has_yield_object = 5;
}

// Next Available ID: 11
message YieldRegionDebug {
  reserved 2 to 6;
  // The ego lane id.
  int64 id = 1;
  // Yield results.
  repeated TrackedObjectYieldResult tracked_object_yield_results = 7;
  // Yield conflicting region results.
  repeated ConflictingRegionYieldResult conflicting_region_yield_results = 8;
  // Yield region stage.
  YieldRegionStage yield_stage = 9;
  // Yield to agent ids.
  repeated int64 yield_to_agent_ids = 10;
}

// The debug message for yield (for conflicting lane) reasoner.
// Next Available ID: 4
message YieldReasonerDebug {
  reserved 1;
  repeated YieldRegionDebug yield_regions = 2;
  repeated YieldObject yield_objects = 3;
}

// Indicates the type of the occlusion scenario.
// Next Available ID: 4
enum OcclusionAreaType {
  reserved 3;
  reserved "kSuperNudge";
  kCrosswalk = 0;
  kRoadExitZone = 1;
  kUnprotectedLeftTurn = 2;
}

// The debug message that describes a hallucinated agent.
// Next Available ID: 6
message HallucinatedAgent {
  voy.Point2d center = 1;
  double width = 2;   // m
  double length = 3;  // m
  double heading = 4;
  repeated voy.Point2d predicted_poses = 5;
}

// The debug message to show the related information of an area-of-interest.
// Next Available ID: 8
message OccludedAreaDebug {
  OcclusionAreaType type = 1;
  // Id of the corresponding area.
  int64 area_id = 2;
  voy.Polygon2d area_contour = 3;
  repeated voy.Point2d occluded_points = 4;
  bool has_hallucinated_agent = 5;
  HallucinatedAgent agent = 6;
  double speed_zone_limit = 7;
}

// The debug message for occlusion unprotected left turn scene.
// Next Available ID: 3
message OcclusionUPLSceneDebug {
  int64 oncoming_lane_id = 1;
  bool has_hallucinated_agent = 2;
}

// The debug message for occlusion reasoner.
// Next Available ID: 11
message OcclusionReasonerDebug {
  reserved 4, 5, 6, 7;
  // Points and polygons for occlusion area debug.
  repeated voy.Point2d visibility_polygon_points = 1;
  repeated voy.Point2d occluded_points = 2;
  repeated voy.Polygon2d agents_clear_region = 3;
  repeated OccludedAreaDebug areas = 8;
  repeated voy.Point2d visibility_points = 9;
  OcclusionUPLSceneDebug unprotected_left_turn_scene = 10;
}

// Next Available ID: 4
message SpeedLimitDebug {
  // The start position of the speed zone on the nominal path.
  double start_arclength_m = 1;
  // The end position of the speed zone on the nominal path.
  double end_arclength_m = 2;
  // Speed limit of the speed zone, in meters per second.
  double speed_limit = 3;
}

// Next Available ID: 2
message SpeedLimitReasonerDebug { repeated SpeedLimitDebug speed_limits = 1; }

// The debug for lane info.
// Next Available ID: 5
message LaneInfo {
  int64 lane_id = 1;
  voy.Polygon2d lane_boarder = 2 [ deprecated = true ];
  LaneChangeMode lane_change_mode = 3;
  voy.Polygon2d lane_border = 4;
}

// The lane sequence debug.
// Next Available ID: 22
message LaneSequenceDebug {
  repeated LaneInfo lane_sequence = 1;
  double cost_to_route_destination = 2;
  double dist_to_route_destination = 17;
  int64 current_lane_id = 19;
  double ego_arc_length_on_current_lane = 20;
  bool is_feasible = 3;
  JumpOutType jump_out_type = 12;
  bool is_pull_out_jump_out = 4 [ deprecated = true ];
  LaneSequenceCostFactors cost_factors = 5;
  bool is_stuck_avoidance_jump_out = 6 [ deprecated = true ];
  repeated LaneInfo lane_change_source_lane_sequence = 7;
  repeated LaneInfo lane_change_target_lane_sequence = 8;
  bool is_from_physical_current_lane = 9;
  bool is_relax_lane_change_restriction = 10;
  bool is_converted_from_regional_path = 11;
  RelaxLaneSequenceScenarioType relax_scenario_type = 13;
  bool is_source_feasible = 14;
  bool is_with_temp_lane = 21;
  LaneSequenceCandidate.LaneSequenceType lane_sequence_type = 15;
  int64 lane_sequence_result_index = 16;
  // The parameters to allow to connect a lane follow edge by violating the
  // direction of lanes.
  // Key: source lane ID whose direction is relaxed.
  // Value: The lanes who are allowed to be target lanes from the source lane.
  map<int64, RelaxLaneDirectionParam> relax_lane_direction_param_info = 18;
}

// The planning segment debug.
// Next Available ID: 3
message PlanningSegmentDebug {
  bool is_lane_change = 1;
  voy.Polygon2d lane_border = 2;
}

// The planning segment sequence debug.
// Next Available ID: 2
message PlanningSegmentSequenceDebug {
  repeated PlanningSegmentDebug planning_segment_debugs = 1;
}

// The debug for waypoint edge.
// Next Available ID: 8
message WaypointEdge {
  repeated int64 lane_ids = 1;
  repeated int64 waypoint_ids = 2;
  // The start and end points of the edge.
  repeated voy.Point2d edge_points = 3;
  LaneChangeMode lane_change_mode = 4;
  voy.Polygon2d lane_border = 5;
  // TODO(Kevinwangxiang) For future, we can display each segment's cost, and
  // even each category's cost(eta, risk, blockage, ...).
  double edge_cost = 6;
  // The points on the edge.
  // Note: this field serves as more light-weight data for edge visualization
  // compared to |lane_border|. This field might not be populated if
  // |lane_border| is set; and vice versa.
  repeated voy.Point2d center_line = 7;
}

// The waypoint sequence cost.
// Next Available ID: 5
message WaypointSequenceCost {
  // Sum of all edges' cost.
  double total_edge_cost = 1;
  // Sequence's exit cost. For feasible sequence, it's virtual target edge's
  // cost, for infeasible sequence, it's last waypoint's exit cost from global
  // cost map.
  double exit_cost = 2;
  // Extra punishment for infeasible sequence.
  double infeasible_sequence_cost = 3;
  // Sequence's total cost.
  double total_cost = 4;
}

// The waypoint sequence debug.
// Next Available ID: 20
message WaypointSequenceDebug {
  reserved 2, 4, 5, 11;
  reserved "cost_to_route_destination", "length_m", "is_pull_out_jump_out",
      "is_stuck_avoidance_jump_out";
  repeated WaypointEdge waypoint_sequence = 1;
  // double cost_to_route_destination = 2;
  // Cost for waypoint sequence.
  WaypointSequenceCost sequence_cost = 16;
  // The path length from ego pose to the last edge's end.
  double edge_sequence_length_m = 14;
  // The path length from ego pose to the last lane's end.
  double lane_sequence_length_m = 15;
  // The length from sequence first waypoint to route destination.
  double dist_to_route_destination = 12;
  bool is_feasible = 3;
  JumpOutType jump_out_type = 8;
  bool is_from_physical_current_lane = 6;
  bool is_relax_lane_change_restriction = 7;
  RelaxLaneSequenceScenarioType relax_scenario_type = 9;
  bool is_source_feasible = 10;
  bool is_with_temp_lane = 18;
  // The parameters to allow to connect a lane follow edge by violating the
  // direction of lanes.
  // Key: source lane ID whose direction is relaxed.
  // Value: The lanes who are allowed to be target lanes from the source lane.
  map<int64, RelaxLaneDirectionParam> relax_lane_direction_param_info = 13;
  // The lane change incentive information for the first lane change on the
  // sequence if any.
  optional LaneChangeIncentiveInfo incentive_info_for_first_lane_change = 17
      [ deprecated = true ];
  // The incentives for the lane changes on the sequence.
  // Currently the max size is 3.
  repeated LaneChangeIncentiveInfo lane_change_incentives = 19;
}

// The near ego keypoints debug.
// Next Available ID: 2
message NearEgoKeypointsDebug { repeated voy.Point2d keypoints = 1; }

// The immediate lane change request debug.
// Next Available ID: 5
message ImmediateLaneChangeRequestDebug {
  repeated int64 path_to_lane_change_position = 1;
  double arc_length = 2;
  ImmediateLaneChangeDirection.Enum direction = 3;
  bool has_hazardous_scene = 4;
}

// The waypoint assist drawable area debug.
// Next Available ID: 2
message WaypointAssistDrawableAreaDebug { repeated LaneInfo lane_sequence = 1; }

// Detail of preview route.
// Next Available ID: 9
message PreviewRouteInfo {
  double cost_to_destination = 1;
  double dist_to_destination = 2;
  repeated LaneInfo lane_sequence = 3;
  TurnInfo first_junction_info = 4;
  repeated LaneChangeInstance untrimmed_lane_change_instances = 5;
  // Segment sequence for preview route, now for onroute sequence only.
  optional PlanningSegmentSequenceDebug planning_segment_sequence = 6;
  // Note: Only fill for reroute preview route.
  PreviewFeatureFromGlobalRoute preview_feature_from_global_route = 7;
  // If previewed route can reach local target. True as default, could only be
  // false for onroute preview one.
  bool reach_sequence_end = 8;
}

// Next Available ID: 10
enum PreviewRouteFailReason {
  kNotFail = 0;
  // Waypoint costmap is empty.
  kEmptyWaypointCostmap = 1;
  // Request to use LC sequence as comparison, but optimal sequence is not LC.
  kNoOptimalLaneChangeSequenceToCompare = 2;
  // Given point to pass is not in costmap.
  kPointToPassNotInCostmap = 3;
  // We cannot give a path from ego pose to given point to pass.
  kPointToPassNotConnectFromSource = 4;
  // We cannot give a onroute path from given point to pass to virtual target.
  kOnRouteFailDueToNotReachDest = 5;
  // For onroute preview, we fail to search backward path.
  kOnRouteFailDueToFailToFindBackwardPath = 6;
  // For onroute preview, we fail to search forward path.
  kOnRouteFailDueToFailToFindForwardPath = 7;
  // For reroute preview, we cannot find optimal regional path for comparision.
  kReRouteFailDueToNoOptimalPath = 8;
  // For reroute preview, the given point to pass is not able to match any
  // regional path candidate, and the point is not in global costmap.
  kReRouteFailLaneToPassNotInGlobalCostMap = 9;
}

// Debug messages for preview route. Include input ego_point and requested pass
// point, and output preview route and optimal route.
// Next Available ID: 8
message PreviewRouteDebug {
  LanePoint ego_point = 1 [ deprecated = true ];
  LanePoint request_pass_point = 2;
  PreviewRouteInfo preview_onroute_route = 3;
  PreviewRouteInfo optimal_onroute_route = 4;
  PreviewRouteInfo preview_reroute_route = 5;
  PreviewRouteInfo optimal_reroute_route = 6;
  repeated PreviewRouteFailReason fail_reason = 7;
}

// The lane sequence rollout intention type.
// Next Avaliable ID: 12
enum LaneSequencePresetType {
  DEFAULT_PRESET = 0;
  LATCH_LAST_OPTIMAL_EDGE_SEQUENCE = 1;
  MINIMUM_LANE_CHANGE = 2;
  AVOID_RIGHTMOST_LANE_LEFT_LANE_CHANGE_SEQUENCE = 3 [ deprecated = true ];
  AVOID_BLOCKAGE_LEFT_LANE_CHANGE_SEQUENCE = 4;
  AVOID_BLOCKAGE_RIGHT_LANE_CHANGE_SEQUENCE = 5;
  AVOID_STM_LEFT_LANE_CHANGE_SEQUENCE = 6;
  LANE_KEEP_INTENTION_SEQUENCE = 7;
  LEFT_LANE_CHANGE_INTENTION_SEQUENCE = 8;
  RIGHT_LANE_CHANGE_INTENTION_SEQUENCE = 9;
  AVOID_RIGHT_SLOWMOVING_OBJECT_LANE_KEEP_SEQUENCE = 10;
  AVOID_LEFT_SLOWMOVING_OBJECT_LANE_KEEP_SEQUENCE = 11;
}

// Debug messages for STM collision.
// Next Avaliable ID: 4
message STMCollisionDebug {
  // The estimated collision distance for ego.
  double collision_distance = 1;
  // The start point of collision edge.
  voy.Point2d start_point = 2;
  // The end point of collision edge.
  voy.Point2d end_point = 3;
}

// The lane change urgency type for rollout evaluation.
// Next Avaliable ID: 5
enum EdgeSequenceUrgencyType {
  // The last change point of source lane change node can not reach destination.
  LAST_LANE_CHANGE_CHANCE_ROUTE = 0;
  // The last change point of source lane change is occupied by blockage.
  LAST_LANE_CHANGE_CHANCE_BLOCKAGE = 1;
  // There is a critical fork in front of the target lane change node.
  CRITICAL_FORK = 2;
  // There is congestion in front of the target lane change node.
  CONGESTION = 3;
  // There does not exist last lane change point, nor fork and congestion.
  // (default value)
  NO_LAST_LANE_CHANGE_POINT = 4;
}

// The lane change urgency information for rollout evaluation.
// Next Avaliable ID: 3
message UrgencyInfo {
  // The lane change urgency score for lane change source/target node along
  // sequence to the last change point.
  double lane_change_urgency_score = 1;
  // The type of this lane change urgency.
  EdgeSequenceUrgencyType urgency_type = 2;
}

// The information of a lane change plan's incentive analysis.
// Next Avaliable ID: 5
message LaneChangeIncentiveInfo {
  // The lane change edge.
  WaypointEdge lane_change_edge = 1;
  // The lane follow sequence starting from the lane change source, that is the
  // potential path if the ego opts for lane keep instead of taking this lane
  // change.
  // Note: Currently this field is not populated for performace.
  repeated WaypointEdge counterfactual_lane_follow_sequence = 2;
  // The lane change incentives found on the counterfactual sequence.
  repeated LaneChangeIncentive lane_change_incentives = 3;
  // Whether this lane change is compulsory, otherwise unstuck or reroute
  // would be required.
  // Now it is determined by if there is any L0 incentive.
  bool is_compulsory = 4;
}

// The information of point where ego could attempt to bypass the full blocked
// area.
// Next Avaliable ID: 9
message FullBlockUnstuckPoint {
  // The relative position of this point in the section.
  // Only the connectable lanes, which are able to connect to the next regional
  // path section, are considered.
  // Next Avaliable ID: 5
  enum BlockSide {
    // The default value.
    kNone = 0;
    // On the leftmost connnectable lane.
    kLeft = 1;
    // On the non leftmost or rightmost connnectable lane.
    kMid = 2;
    // On the rightmost connnectable lane.
    kRight = 3;
    // On the only connnectable lane in the section.
    kLeftAndRight = 4;
  };

  // The node id.
  int64 id = 1;
  // The relative position of this node in the section.
  BlockSide side = 2;
  // The distance from this node to ego.
  double dist_from_ego_m = 3;
  // A sequence of blocked edges id after this point.
  repeated int64 blocked_edge_ids = 4;
  // The lane sequence corresponding to the blocked_edges.
  repeated int64 blocked_lane_ids = 5;
  // The arc length of the blcoked_edges's start on the frist blocked lane.
  double start_arclength_on_first_lane = 6;
  // The arc length of the blcoked_edges's end on the last blocked lane.
  double end_arclength_on_last_lane = 7;
  // A boolean indicating whether there is traffic flow in the potential unstuck
  // side of this point.
  bool has_traffic_flow_to_unstuck_side = 8;
}

// Next  Available ID: 3
message SlowMovingConstInfo {
  // The distance from ego pose to the first slowmoving object.
  double lane_change_to_slowmoving_dist = 1;
  // The punishment cost for lane changing to the rear of a slowmoving object.
  double lane_change_to_slowmoving_cost = 2;
}

// Next Available ID: 33
message LaneSequenceRolloutDebug {
  // The candidate id.
  int64 id = 1;
  // Boolean indicates if this candidate is the best among all rollouts.
  bool is_best_candidate = 2;
  // The preset type of this rollout.
  LaneSequencePresetType preset_type = 3;
  // The lane ids of the sequence.
  repeated int64 lane_sequence_ids = 4;
  // The waypoint edge sequence.
  WaypointSequenceDebug waypoint_sequence = 5;
  // The overall final evaluation cost.
  double final_cost = 6;
  // The overall route difficulty related cost.
  double route_cost = 7;
  // The overall risk difficulty related cost.
  double risk_cost = 8;
  // The overall progress difficulty related cost.
  double progress_cost = 9;
  // The raw a star path cost.
  double a_star_cost = 10;
  // The number of lane changes on the sequence.
  int32 lane_change_num_on_sequence = 11;
  // The minimum lane change number required from the sequence end to the next
  // junction.
  int32 lane_change_num_post_sequence = 12;
  // The minimum lane change number required from virtual source to target.
  // If no feasible path to reach the target exists, it is set to -1.
  int32 least_required_lane_change_num = 13;
  // The extra lane change number other than the least required, i.e.
  // |lane_change_num_post_sequence| - |least_required_lane_change_num|;
  // If no feasible path exists, it is set to 0.
  int32 redundant_lane_change_num = 14;
  // If the first lane change on the sequence does not have a clear purpose.
  bool is_first_lane_change_unreasonable = 21;
  // The number of same-direction continuous lane change pairs.
  int32 consecutive_lane_change_num = 15;
  // The number of opposite-direction continuous lane change pairs.
  int32 reverse_lane_change_num = 16;
  // The distance from ego to the first hard block on the sequence; if no hard
  // block exits, it is set to -1.
  double dist_to_first_hard_block = 17;
  // The distance from ego to the first soft block on the sequence; if no soft
  // block exits, it is set to -1.
  double dist_to_first_soft_block = 18;
  // The length of congestion along lane sequence.
  double congestion_length_along_lane_sequence = 19;
  // The estimated collision distance that ego drives until collision happens
  // for each STM vehicle. It is calculated by searching along the edge
  // sequence of ego.
  repeated STMCollisionDebug stm_collision_info_vec = 20;
  // The length along sequence which has potential stuck risk for both sides.
  double both_side_stuck_length_along_sequence = 22;
  // The length along edge sequence which has potential stuck risk for one side.
  double one_side_stuck_length_along_sequence = 23;
  // The lane change urgency based on distance from source change point along
  // sequence to the last change point. The range is set within [0, 1], where
  // higher value means higher lane change urgency.
  double first_lane_change_urgency = 24 [ deprecated = true ];
  // The distances of opposite-direction continuous lane change pairs.
  repeated double reverse_lane_change_distances = 25;
  // The lane change urgency for lane chance source node along sequence to the
  // last change point.
  repeated double lane_change_urgencies = 26 [ deprecated = true ];
  // The lane change urgency for lane change source/target node along sequence
  // to the last change point.
  repeated UrgencyInfo lane_change_urgency_info_list = 27;
  // The distance of opposite-direction continuous lane change pair, where first
  // lane change is completed one from previous cycles, and second is current
  // sequence's first lane change instance.
  optional double reverse_lc_distance_by_considering_last_completed_lc = 28;
  // Returns whether latest completed lane change is requested by early lc.
  bool is_last_completed_lc_instance_request_by_early_lane_change = 29;
  // The stuck probability of lane sequence.
  double stuck_probability = 30;
  // Returns whether a lane along edge sequence whose both sides are close to
  // hard boundary.
  bool is_near_hard_boundary = 31;
  SlowMovingConstInfo slowmoving_cost_info = 32;
}

// The debug message for lane sequence generator.
// Next Available ID: 45
message LaneSequenceGeneratorDebug {
  // Lane sequences.
  LaneSequenceDebug optimal_lane_sequence = 1;
  LaneSequenceDebug non_lane_change_lane_sequence = 2;
  LaneSequenceDebug immediate_left_lane_change_lane_sequence = 11;
  LaneSequenceDebug immediate_right_lane_change_lane_sequence = 13;
  repeated LaneSequenceDebug alternative_lane_follow_lane_sequences = 17;
  LaneSequenceDebug stuck_avoidance_jump_out_lane_sequence = 22;
  LaneSequenceDebug relax_lane_change_restriction_lane_sequence = 24;
  repeated LaneSequenceDebug alternative_lane_change_lane_sequences = 35;
  LaneSequenceDebug reverse_driving_lane_sequence = 33;
  LaneSequenceDebug reroute_backup_lane_follow_lane_sequence = 43;

  // Waypoint sequences.
  WaypointSequenceDebug optimal_waypoint_sequence = 4;
  WaypointSequenceDebug non_lane_change_waypoint_sequence = 5;
  WaypointSequenceDebug immediate_left_lane_change_waypoint_sequence = 12;
  WaypointSequenceDebug immediate_right_lane_change_waypoint_sequence = 14;
  repeated WaypointSequenceDebug alternative_lane_follow_waypoint_sequences =
      18;
  WaypointSequenceDebug stuck_avoidance_jump_out_waypoint_sequence = 23;
  WaypointSequenceDebug relax_lane_change_restriction_waypoint_sequence = 25;
  repeated WaypointSequenceDebug alternative_lane_change_waypoint_sequences =
      36;
  // Indicates lane sequence for waypoint assist end of lane sequence scenario.
  repeated LaneInfo lane_sequence_for_end_of_lane_seq = 38;

  // Planning segment sequences.
  PlanningSegmentSequenceDebug optimal_planning_segment_sequence = 26;
  PlanningSegmentSequenceDebug non_lane_change_planning_segment_sequence = 29;
  PlanningSegmentSequenceDebug
      immediate_left_lane_change_planning_segment_sequence = 27;
  PlanningSegmentSequenceDebug
      immediate_right_lane_change_planning_segment_sequence = 28;
  repeated PlanningSegmentSequenceDebug
      alternative_lane_follow_planning_segment_sequences = 30;
  PlanningSegmentSequenceDebug
      stuck_avoidance_jump_out_planning_segment_sequence = 31;
  PlanningSegmentSequenceDebug
      relax_lane_change_restriction_planning_segment_sequence = 32;
  repeated PlanningSegmentSequenceDebug
      alternative_lane_change_planning_segment_sequences = 37;

  //
  // Other info.
  //
  LaneGraphSearchInfo lane_graph_search_info = 3;
  // The initial state for lane sequence generation.
  LaneSequencePlanInitState lane_sequence_plan_init_state = 8;
  NearEgoKeypointsDebug near_ego_keypoints_debug = 9;
  ImmediateLaneChangeRequestDebug immediate_lane_change_request = 10;
  // Waypoint graph for optimal waypoint sequence. It will only be set in
  // simulation.
  WaypointGraphDebug waypoint_graph_info_for_optimal = 19;
  // Blockage ranges we used when searching lane sequence.
  WaypointLaneSearchInfoDebug waypoint_lane_search_info_debug = 20;
  // Show an area composed of several lanes that can be used for waypoint assist
  // to draw points.
  WaypointAssistDrawableAreaDebug waypoint_assist_drawable_area_debug = 21;
  WaypointAssistDrawableAreaDebug waypoint_assist_extended_drawable_area_debug =
      44;
  PreviewRouteDebug preview_routes = 34;
  // The committed lanes for searching optimal waypoint sequence.
  repeated int64 committed_lanes_for_optimal_waypoint_sequence = 39;
  // Lane sequence rollouts.
  repeated LaneSequenceRolloutDebug lane_sequence_rollouts = 40;
  // The points where ego could attempt to bypass the full blocked area.
  repeated FullBlockUnstuckPoint full_block_unstuck_points = 41;

  //
  // Deprecated fields.
  //
  reserved 6, 7, 15, 16, 42;
  reserved "alternative_lane_follow_lane_sequence";
  reserved "alternative_lane_follow_waypoint_sequence";
  reserved "backup_lane_follow_lane_sequence";
}

// The debug for each single bus stop zone.
// Next Available ID: 5
message BusStopDebug {
  // The bus stop zone id.
  int64 id = 1;
  // The start position of the bus stop on the nominal path.
  double start_arclength_m = 2;
  // The end position of the bus stop on the nominal path.
  double end_arclength_m = 3;
  // Speed limit for this bus stop zone
  double speed_limit = 4;
}

// The debug for the bus stop reasoner.
// Next Available ID: 2
message BusStopReasonerDebug { repeated BusStopDebug bus_stops = 1; }

// The debug for the spatial info of the traffic light.
// Next Available ID: 7
message TrafficLightSpatialInfoDebug {
  // The stop line start arclength.
  double stop_line_start_arclength = 1;
  // The start of conflict zone arclength.
  double conflicting_zone_start_arclength = 2;
  // The end of conflict zone arclength.
  double conflicting_zone_end_arclength = 3;
  // The watch line arclength.
  google.protobuf.DoubleValue watch_line_arclength = 4;
  // The crosswalk start arclength.
  double crosswalk_start_arclength = 5;
  // The crosswalk end arclength.
  double crosswalk_end_arclength = 6;
}

// The debug for the temporal information of the traffic light.
// Next Available ID: 6
message TrafficLightInfoTemporalDebug {
  // Green light temporal range of current traffic light.
  math.pb.TimeRange green_temporal_range = 1;
  // Green light flashing temporal range of current traffic light.
  math.pb.TimeRange green_flashing_temporal_range = 2;
  // Yellow light temporal range of current traffic light.
  math.pb.TimeRange yellow_temporal_range = 3;
  // Red light temporal range of current traffic light.
  math.pb.TimeRange red_temporal_range = 4;
  // Unknown color temporal range of current traffic light.
  math.pb.TimeRange unknown_temporal_range = 5;
}

// The debug for the temporal and spatial information of the traffic light.
// Next Available ID: 11
message TrafficLightInfoDebug {
  // The spatial information of the traffic light.
  TrafficLightSpatialInfoDebug tl_spatial_info = 1;
  // The temporal information of the traffic light.
  TrafficLightInfoTemporalDebug tl_temporal_info = 2;
  // The controlled lane id.
  int64 controlled_lane_id = 3;
  // The traffic light id.
  int64 traffic_light_id = 7;
  // The color of current traffic light.
  voy.TrafficLight.Color color = 4;
  // Whether the current traffic light is flashing or not.
  bool is_flashing = 5;
  // Whether the tl uses watch line as stop line.
  bool is_use_watch_line = 6;
  // Whether the current traffic light is broken.
  bool is_broken = 8;
  // Whether the current traffic light is occluded.
  bool is_occluded = 9;
  // The countdown time in ms.
  google.protobuf.Int64Value countdown_ms = 10;
}

// The debug for traffic light.
// Next Available ID: 14
message TrafficLightReasonerDebug {
  reserved 1 to 12;
  reserved "signal_id", "color", "is_flashing", "controlled_lane_id",
      "previous_control_state", "current_control_state",
      "stop_line_start_arclength", "stop_fence_distance",
      "successive_green_or_red_color_num", "timer_param", "distance_param",
      "param";
  // Temporal and spatial information of traffic light information.
  repeated TrafficLightInfoDebug tl_info = 13;
}

// The debug for all the lane follow maneuver reasoners.
// Next Available ID: 16
message ReasonersDebug {
  reserved 2, 4, 6, 9, 11, 15;
  StopSignReasonerDebug stop_sign_reasoner = 1;
  CrosswalkReasonerDebug crosswalk_reasoner = 3;
  YieldReasonerDebug yield_reasoner = 5;
  SpeedLimitReasonerDebug speed_limit_reasoner = 7;
  BusStopReasonerDebug bus_stop_reasoner = 8;
  CautiousDrivingReasonerDebug cautious_driving_reasoner = 10;
  OcclusionReasonerDebug occlusion_reasoner = 12;
  TrafficLightReasonerDebug traffic_light_reasoner = 13;
  GearSwitchDebug gear_switch_reasoner = 14;
}

// Defines the gap or object type.
// Next Available ID: 2
enum LaneChangeGapObjectType {
  AGENT = 0;
  GAP = 1;
}

// Defines the leading and trailing type and attributes of a gap.
// Next Available ID: 5
message GapLeadingAndTrailingAgentType {
  voy.perception.ObjectType leading_type = 1;
  voy.perception.ObjectType trailing_type = 2;
  repeated voy.perception.Attribute leading_attributes = 3;
  repeated voy.perception.Attribute trailing_attributes = 4;
}

// Defines the gap or moving agent's description.
// Next Available ID: 9
message LaneChangeGapObject {
  double birth_time = 1;
  double dead_time = 2;
  double birth_start_arclength_m = 3;
  double birth_end_arclength_m = 4;
  double dead_start_arclength_m = 5;
  double dead_end_arclength_m = 6;
  LaneChangeGapObjectType object_type = 7;
  GapLeadingAndTrailingAgentType leading_and_trailing_type = 8;
}

// Defines the valid lane change range's start point and end point.
// Next Available ID: 3
message ValidRangePointsPairs {
  voy.Point2d start_point = 1;
  voy.Point2d end_point = 2;
}

// Defines the gap evaluation results.
// Next Available ID: 14
message LaneChangeGapEvaluation {
  reserved 13;
  reserved "valid_range_points_pairs";
  double jump_in_timestamp = 1;
  double jump_in_arclength = 2;
  double jump_in_speed = 3;
  double jump_in_effort = 4;
  double current_progress = 5;
  double future_progress = 6;
  double total_cost = 7;
  bool is_selected = 8;
  .google.protobuf.Int64Value leading_id = 9;
  .google.protobuf.Int64Value trailing_id = 10;
  voy.Point2d start_point = 11;
  voy.Point2d end_point = 12;
}

// The debug for lane change or pull over gap search.
// Next Available ID: 4
message GapSearchDebug {
  repeated LaneChangeGapObject gap_objects = 1;
  repeated LaneChangeGapEvaluation gap_evaluations = 2;
  repeated ValidRangePointsPairs valid_range_points_pairs = 3;
}

// The debug for agent's intention during crawl.
// Next Available ID: 4
message CrawlAgentIntention {
  .google.protobuf.Int64Value object_id = 1;
  double yield_intention = 2;
  bool is_leading = 3;
}

// The debug for crawl.
// Next Available ID: 3
message CrawlDebug {
  repeated CrawlAgentIntention crawl_agent_intention = 1;
  CrawlForwardSimulationResult forward_simulation_result_debug = 2;
}

// The debug for a lane change maneuver.
// Next Available ID: 8
message LaneChangeDebug {
  reserved 4;
  LaneChangePhase lane_change_phase = 1;
  string current_lane_change_instance = 2;
  string error_message = 3;
  // The lane change gap debug.
  GapSearchDebug lane_change_gaps = 5;
  LaneChangeClerkDebug lane_change_clerk_debug = 6;
  CrawlDebug crawl_debug = 7;
}

// The debug for lane chang clerk info.
// Next Available ID: 8
message LaneChangeClerkDebug {
  double min_front_clerance_on_source_lane = 1;
  double min_front_clerance_on_target_lane = 2;
  double min_rear_clearance_on_target_lane = 3;
  double closest_rear_tracked_object_accel = 4;
  int64 lane_change_period = 5;
  double jerk = 6;
  double acceleration = 7;
}

// Defines the gap range which is used in pull over.
// Next Available ID: 3
message PullOverGapRange {
  voy.Point2d start_point = 1;
  voy.Point2d end_point = 2;
}

// The debug for a pull over maneuver.
// Next Available ID: 5
message PullOverDebug {
  GapSearchDebug pull_over_gaps = 1;
  voy.Point2d pull_over_destination_point = 2;
  PullOverProgress pull_over_progress = 3;
  PullOverGapRange pull_over_gap_range = 4;
}

// Next Avaliable ID: 8
message XLaneNudgeDebug {
  // The error code for x-lane nudge trigger.
  // Next Available ID: 17
  enum XLaneNudgeResultType {
    kUnKnown = 0;
    kSuccess = 1;
    kFinished = 2;
    kNotPersistentStuck = 3;
    kFaraway = 4;
    kCloseToUTurn = 5;
    kTooFast = 6;
    kWideEnough = 7;
    kRightLanePassable = 8;
    kNoStuckSignal = 9;
    kTrafficLight = 10;
    kNoClearance = 11;
    kObjectBlocked = 12;
    kNotSelected = 13;
    kDisabled = 14;
    kWaitPolicy = 15;
    kNotCrossable = 16;
  }
  reserved 4;
  reserved "risky";
  // X-lane nudge trigger error code.
  XLaneNudgeResultType type = 1;
  // The mode indicates current side x-lane nudge, it could be kFreeSpace,
  // kOncomingLane, kNeighborLane.
  StuckRegion.OvertakeMode mode = 2;
  // Required boundary offset for current side x-lane nudge.
  double required_offset = 3;
  // Flag indicates if the current side could be trimmed.
  bool could_be_trimmed = 5;
  // The overtake policy for the stuck region.
  StuckRegion.OvertakePolicy policy = 6;
  // Required center line offset on current side.
  double required_center_line_offset = 7;
}

// Next Available ID: 4
message JudgeVoteResultDebug {
  // The name of the judge.
  string judge_name = 1;
  // Left side vote
  int64 left_side_vote_count = 2;
  // Right side total vote count.
  int64 right_side_vote_count = 3;
}

// Next Available ID: 2
message XLaneNudgeSideVoterDebug {
  // Vote results for all judges.
  repeated JudgeVoteResultDebug vote_results = 1;
}

// The debug info for ego's unstuck states, which corresponds to a specific lane
// sequence.
// Next Avaliable ID: 10
message UnstuckDebug {
  // Ego steering wheel angle.
  reserved 8;
  reserved "ego_steering_wheel_angle";
  // Flag indicates if ego was stuck in the last planning cycle.
  bool was_ego_stuck = 1;
  // Flag indicates if x-lane nudge was triggered in the last planning cycle.
  bool was_xlane_nudging = 2;
  // Flag indicates if driable boundary should be extended to search for x-lane
  // nudge path.
  bool is_boundary_extended = 3;
  // Debug info for stuck regions in the current lane sequence.
  repeated StuckRegion stuck_regions = 4;
  // Left-side x-lane nudge debug info.
  XLaneNudgeDebug left_xlane_nudge_debug = 5;
  // Right-side x-lane nudge debug info.
  XLaneNudgeDebug right_xlane_nudge_debug = 6;
  // The angle diff between ego heading and reference line.
  double ego_heading_diff = 7;
  // Debug info for xlane nudge side voter.
  XLaneNudgeSideVoterDebug xlane_nudge_side_voter_debug = 9;
}

// The debug for a lane change abort maneuver.
// Next Available ID: 3
message LaneChangeAbortDebug {
  reserved 1;  // deprecated field abort_curve_failed
  reserved "abort_curve_failed";
  string error_message = 2;
}

// Defines the optinal lane for engage maneuver including lane id and cost.
// Next Available ID: 3
message EngageOptionalLane {
  int64 id = 1;
  double cost = 2;
}

// The debug for a engage maneuver.
// Next Available ID: 8
message EngageDebug {
  reserved 2;
  reserved "distance_to_lane_center_line_m";
  repeated EngageOptionalLane optional_lanes = 1;
  double angle_with_lane_center_line_rad = 3;
  int64 lane_following_ready_timestamp = 4;
  google.protobuf.Int64Value close_object_id = 5;
  double distance_front_bumper_to_lane_center_m = 6;
  double distance_rear_bumper_to_lane_center_m = 7;
}

// Next Available ID: 2
message HardRoadBoundaryLine { repeated voy.Point2d points = 1; }

// Next Available ID: 8
message LaneSequenceGeometryDebug {
  repeated voy.Point2d nominal_path = 1;
  repeated voy.Point2d left_lane_boundary = 2;
  repeated voy.Point2d right_lane_boundary = 3;
  repeated voy.Point2d left_soft_road_boundary = 4;
  repeated voy.Point2d right_soft_road_boundary = 5;
  repeated HardRoadBoundaryLine left_hard_road_boundary_lines = 6;
  repeated HardRoadBoundaryLine right_hard_road_boundary_lines = 7;
}

// The debug for driving directives.
// Next Available ID: 3
message DrivingDirectiveDebug {
  LaneSequenceGeometryDebug lane_sequence_geometry = 1;
  repeated AgentInLaneStates traffic_participants = 2;
}

// The debug for a maneuver's end-approaching cost.
// Next Available ID: 3
message EndApproachingCostDebug {
  double cost = 1;
  double distance_to_end = 2;
}

// The debug for a maneuver's congestion-approaching cost.
// Next Available ID: 4
message CongestionApproachingCostDebug {
  double cost = 1;
  google.protobuf.DoubleValue distance_to_congestion = 2;
  google.protobuf.Int64Value congestion_object_id = 3;
}

// The debug for a maneuver's route-based cost.
// Next Available ID: 8
message RouteBasedCostDebug {
  double cost = 1;
  int32 optimal_lane_change_num = 2;
  int32 optimal_merge_num = 3;
  int32 lane_change_num = 4;
  int32 merge_num = 5;
  google.protobuf.DoubleValue remaining_distance = 6;
  double lane_sequence_cost = 7;
}

// The debug for a maneuver's cost.
// Next Available ID: 14
message ManeuverCostingDebug {
  reserved 10, 11;
  reserved "congestion_approaching_cost", "route_based_cost";
  bool is_valid = 1;

  // The total cost of a maneuver.
  double cost = 2;

  // Itemized costs.
  EndApproachingCostDebug end_approaching_cost = 3;
  CongestionApproachingCostDebug congestion_approaching_cost_debug = 12;
  double st_rollout_cost = 4;
  double maneuver_flickering_cost = 5;
  double destination_approaching_cost = 6;
  double lane_change_clearance_cost = 7;
  double bus_bulb_cost = 8;
  double hazardous_driving_state_cost = 9;
  RouteBasedCostDebug route_based_cost_debug = 13;
}

// Next Available ID: 2
message BackupBehaviorDebug {
  // Agent's for which ACC is guaranteed in juke suppression.
  repeated int64 agent_ids = 1;
}

// The debug for a maneuver.
// Next Available ID: 35
message ManeuverDebug {
  reserved 1 to 7, 11, 15 to 17, 24, 29, 30;
  reserved "juke_integrated_path_generator_debug", "super_nudge",
      "waypoint_assist";

  ManeuverType type = 9;
  ReasonersDebug reasoners = 8;

  // Maneuver execution status.
  bool maneuver_transition_check_passed = 10;
  ManeuverRunnableStatus runnable_status = 18;
  bool evaluate_passed = 12;

  // Maneuver cost debug.
  ManeuverCostingDebug maneuver_costing_debug = 22;

  // Inter maneuver request generated debug .
  InterManeuverCoordinationRequest inter_maneuver_request_generated_debug = 32;
  // Inter maneuver request received debug.
  repeated InterManeuverCoordinationRequest
      inter_maneuver_request_received_debug = 33;

  // Maneuver-specific debug.
  oneof extra_data {
    LaneChangeDebug lane_change = 13;
    LaneChangeAbortDebug lane_change_abort = 14;
    PullOverDebug pull_over = 26;
    EngageDebug engage = 28;
  }

  // Path generator debug.
  PathGeneratorDebug path_generator_debug = 21;

  // Nominal path planner debug.
  PathPlannerDebug path_planner_debug = 31;

  // The ST planner debug.
  STPlannerDebug st_planner_debug = 19;

  // The driving directive debug.
  DrivingDirectiveDebug driving_directive_debug = 20;

  // Backup behavior debug.
  BackupBehaviorDebug backup_debug = 23;

  // Ego Parameters debug.
  EgoInLaneParamsDebug ego_inlane_params_debug = 25;

  // Construction zone inlane state debug.
  ConstructionZoneDebug construction_zone_debug = 27;

  // The state of reverse driving function.
  ReverseDrivingState.Enum reverse_driving_state = 34;
}

// Indicates the type of the turn mode reason.
// Next Available ID: 6
enum TurnModeReason {
  reserved 3, 4;
  reserved "SUPER_NUDGE_OVERTAKING", "SUPER_NUDGE_MERGING_BACK";
  TURN_LANE = 0;
  DURING_LANE_CHANGE = 1;
  LANE_CHANGE_PROBING = 2;
  DURING_ENGAGE = 5;
}

// The debug for all the turn signal modes and reasons.
// Next Available ID: 3
message TurnSignalDebug {
  TurnMode turn_mode = 1;
  TurnModeReason turn_mode_reason = 2;
}

// The abandoned request of one kind of maneuver.
// Next Available ID: 2
message AbandonedRequests { repeated AssistRequest abandoned_request = 1; }

// Next Available ID: 7
message AssistInstructionDebug {
  bool is_ego_stuck = 1;
  repeated TypedObjectId creep_around_objects = 2;
  repeated int64 violable_lane_marking_ids = 3;
  repeated TypedObjectId xlane_nudge_bypass_objects = 4;
  UnstuckSide.Enum unstuck_side = 5;
  repeated TypedObjectId forward_unstuck_target_objects = 6;
}

// Next Available ID: 2
message ExpandedAssistInstructionDebug {
  optional ReverseDrivingResponse reverse_driving_assist_response = 1;
}

// The debug info for current unstuck directives which is holded in assist
// directive generator.
// Next Available ID: 3
message UnstuckDirectivesDebug {
  AssistInstructionDebug assist_instruction_debug = 1;
  ExpandedAssistInstructionDebug expanded_assist_instruction_debug = 2;
}

// The debug info for assist directive generator.
// Next Available ID: 9
message AssistDirectiveGenerationDebug {
  reserved 1, 4, 5, 6, 7;
  reserved "latest_received_assist_response", "request_confirm_status",
      "assist_directive_switch_type", "assist_directive_switch_reason",
      "unstuck_assist_responses";
  repeated AssistResponse assist_responses = 2;
  optional UnstuckDirectivesDebug unstuck_directives_debug = 3;
  bool is_unstuck_finished = 8;
}

// The debug info for assist requests & responses.
// Next Available ID: 7
message AssistDebug {
  // All kinds of maneuver's assist requests which are abandoned per cycle. The
  // key is maneuver type, the value is abandoned requests of the related
  // maneuver.
  map<string, AbandonedRequests> abandoned_requests_map = 1;
  AssistDirectiveGenerationDebug assist_directive_generation_debug = 2;
  // Requests generated by planner.
  repeated AssistRequest assist_requests = 3;
  // Responses received every cycle.
  repeated AssistResponse assist_responses = 4;
  // Assist request session.
  AssistRequestSession assist_request_session = 5;
  // Assist task queue of assist task scheduler.
  AssistTaskSchedulerDebug assist_task_scheduler_debug = 6;
}

// The status indicates the states of the stuck detection and execution.
// Next Available ID: 20.
// Reference doc link:
// https://cooper.didichuxing.com/docs/document/2201187128182
// TODO(alexma, liangxianghui), specify FP/FN types, and refine logging logic.
enum UnstuckStatus {
  UNSTUCK_UNDEFIEND = 0;
  CORE_PLANNER_DETECT = 1;
  WAITTING_CORE_PLANNER_UNSTUCK = 2;
  MODEL_FP = 3;
  MODEL_DETECT = 4;
  MODEL_REQUEST = 5;
  ALREADY_IN_WAYPOINT_ASSIST = 10;
  ALREADY_IN_LIGHT_ASSIST = 6;
  ALREADY_IN_REVERSING = 11;
  OPS_REFUSED_SCENE = 7;
  NOT_STATIONARY = 8;
  RULE_FN_DETECT = 9;
  MANUAL_DRIVING = 12;
  NORMAL_EXIT = 13;
  NO_DEBUG_MESSAGE = 14;
  PULLOUT_IN_REQUEST = 15;
  FLAG_DISABLE = 16;
  OUTER_RA_REQUEST = 17;
  MRC_REACHED = 18;
  INTERVENTION_FORBID = 19;
}

// This gives details about assist model logic in the following sections:
// 1.if hit model_fp logic, give the exact reason.
// 2.if hit model_fn logic, give the exact reason.
// doc link: https://cooper.didichuxing.com/docs2/document/2201416241911
// Next Available ID: 35.
enum AssistModelProcessReason {
  reserved 22;
  reserved "FN_TEMP_PARKED";
  UNDEFINED_REASON = 0;
  FP_TRAFFIC_JAM = 1;
  FP_RED_LIGHT = 2;
  FP_STOP_FENCE = 3;
  FP_STARTUP = 4;
  FP_MAXSPD = 5;
  FP_CROSSWALK = 6;
  FN_REJECT_INQUEUE = 7;
  FN_JUNCTION = 8;
  FN_SOLID_LANEMARK = 9;
  FN_LOW_PRED = 10;
  FP_PULL_OVER = 11;
  FP_YIELD_DYNAMIC_OBJECT = 12;
  FP_EOL_WITH_RED_TL = 13;
  // FP_YIELD_ON_RIGHT_TURN is not used. Please use FP_YIELD_ON_TURN.
  FP_YIELD_ON_RIGHT_TURN = 14 [ deprecated = true ];
  FP_QUEUING = 15;
  FN_RA_CZ = 16;
  // FP_YIELD_ON_TURN includes right and u turn scenario.
  FP_YIELD_ON_TURN = 17;
  FN_NEAR_HARD_BOUNDARY = 18;
  FN_SELECTION = 19;
  FP_OCCLUSION = 20;
  FN_BREAKDOWN_CAR = 21;
  REQUEST_FROM_ROUTING = 23;
  FN_PERCEPTION_FP = 24;
  FN_EOL = 25;
  FP_REMOTE_SPEED_LIMIT = 26;
  FN_CZ = 27;
  REQUEST_FROM_CREEP = 28;
  FN_NO_BLOCK = 29;
  FN_LANE_CHANGE_STUCK = 30;
  FN_FORCING_RECALL = 31;
  FN_VEHICLE_HAZARD_SIGNAL = 32;
  REQUEST_FROM_TIDAL_FLOW_LANE = 33;
  REQUIREMENT_OF_TRAFFIC_LIGHT = 34;
}

// This gives details when core planner is not unstucking.
// Next Available ID: 3.
enum NonVoluntaryUnstuckReason {
  Undefined = 0;
  UndetectedStuckScene = 1;
  UnstuckFail = 2;
}

// The debug info for the ego stuck estimation.
// Next Available ID: 35.
message EgoStuckDebug {
  reserved 14, 16, 17, 18;
  reserved "fp_queuing_debug", "fn_map_change_area_debug", "rule_decision",
      "rule_activation";
  // The tag indicating which type of config is in use.
  AssistStuckSceneDetectorConfig.ConfigTag config_tag = 26;
  // The description about config in use.
  string config_desc = 27;
  // The sequence of extracted features.
  repeated EgoStuckFeatureOnCycle extracted_features = 1;
  // The gbm inference result.
  optional double gbm_stuck_likelihood = 2;
  // The dnn inference stuck likelihood.
  optional double dnn_stuck_likelihood = 15;
  // The dnn inference stuck likelihood from ra model vnode.
  optional double dnn_stuck_likelihood_from_ra_vnode = 33;
  // The dnn inference stuck likelihood from ra model vnode.
  optional double scenario_dnn_stuck_likelihood_from_ra_vnode = 34;
  // Input features of a lightgbm model.
  repeated double gbm_features = 3;
  // Calculated SHAP values of the features, to indicate the contribution of
  // each feature to the final model output. If not empty, its dimension should
  // be |num_features + 1|, with the last value being the expected (baseline)
  // output of the model.
  // Reference: https://github.com/slundberg/shap
  repeated double shap_values = 4;
  // The states of the stuck detection and execution.
  UnstuckStatus unstuck_status = 5;
  // Indicates whether the model recalled the scene after model infer && post
  // computation. FP filter logic not included. If use_dnn_model is true, the
  // model is dnn model, otherwise gbm model.
  bool is_stuck_detected_by_model = 6;
  // Indicates whether the dnn model recalled the scene after dnn model infer &&
  // post computation. FP filter logic not included. This field is only to
  // record if dnn model recalls when dnn model is in shadow mode.
  bool is_stuck_detected_by_dnn_model = 25;
  // Indicates reason when assist_stuck_scene_detector hit model_fp or model_fn
  // logic.
  AssistModelProcessReason assist_model_process_reason = 7;
  // Indicates reason when core planner is not unstucking.
  NonVoluntaryUnstuckReason non_voluntary_unstuck_reason = 8;
  // Indicates whether the core planner recalled the scene.
  bool is_stuck_detected_by_core_planner = 9;
  // The threshold for assist stuck model predicting positive.
  double stuck_threshold = 10;
  // Recent predictions from stuck gbm model.
  repeated double recent_predictions = 23;
  // Recent predictions from stuck dnn model.
  repeated double recent_dnn_predictions = 24;
  // Recent predictions from dnn model in RemoteAssistModelVNode.
  repeated double recent_dnn_predictions_from_ra_vnode = 31;
  // Recent predictions from scenario dnn model in RemoteAssistModelVNode.
  repeated double recent_scenario_dnn_predictions_from_ra_vnode = 32;
  // The timestamp for last model request.
  int64 last_model_request_time = 11;
  // The timestamp for last core planner request.
  int64 last_core_planner_request_time = 12;
  // The stationary cycle count of ego.
  int64 stationary_cycle_count = 13;
  // The cycle count since the last model request.
  int64 model_request_lasted_cycles = 19;
  // The cycle count since the last ops refuse.
  int64 latest_refused_feedback_lasted_cycles = 20;
  // The cycle count accumulated by FN detected.
  int64 continuous_fn_cycle_cnt = 21;
  // Assist stuck rules' debug details.
  AssistStuckRules rules_debug = 22;
  bool use_congested_judge = 28;
  bool is_congested = 29;
  // Request type meta.
  AssistStuckRequestTypeMeta request_type_meta = 30;
}

// The debug for lane sequence infos.
// Next Available ID: 5
// TODO(hongda): Add more lane sequence infos.
message LaneSeqeunceInfoDebug {
  LaneSequenceGeometryDebug lane_sequence_geometry = 1;
  repeated AgentInLaneStates traffic_participants = 2;
  repeated int64 lane_ids = 3;
  int64 current_lane_id = 4;
}

// The debug info for given lane sequence based plan.
// Next Available ID: 12
message LaneSequenceInfoAndIntentPlanDebug {
  reserved 3;
  LaneSeqeunceInfoDebug lane_sequence_info = 1;

  IntentionGeneratorDebug intention_generator_debug = 4;

  // There are multiple path + speed plan debug according to different intent
  // homotopy.
  repeated IntentionPlanDebug single_intention_plan_debug = 2;

  PullOverDebug pull_over_debug = 5;

  // Debug info for unstuck in corresponding lane sequence.
  UnstuckDebug unstuck_debug = 6;

  GeometricGuidanceGenerationDebug geometric_guidance_generation_debug = 7;
  GlobalObjectManagerDebug global_object_manager_debug = 8;

  // Debug info for disable/filter ml path/trajectory path option.
  string disable_ml_path_reason = 9;
  string disable_ml_trajectory_reason = 10;
  repeated string filter_ml_candidate_reason = 11;
}

// The debug info for lane change metadata.
// Next Available ID: 3
message LaneChangeMetadataDebug {
  string debug_str = 1;
  LaneChangeMetadata lane_change_metadata = 2;
}

// The debug info for lane change env info.
// Next Available ID: 14
message LaneChangeEnvInfoDebug {
  map<string, LaneChangeRegionInfo> region_info_map = 1;
  repeated LaneChangeUrgencyInfo urgency_infos = 2;
  LaneChangeGapAlignUrgencyInfo gap_align_urgency_info = 10;
  LaneChangeGenericUrgencyInfo generic_urgency_info = 13;

  double urgency_score = 3;
  double smooth_urgency_score = 4;

  double safe_yield_acceleration = 6;
  map<int64, ObjectCollisionInfo> object_collision_info_map = 5;

  double blocking_traffic_score = 7;
  double difficulty_score = 8;

  // The key is the object id.
  map<int64, XRegionMotionInfoMap> object_xregion_motion_info_map = 9;

  bool is_current_lane_change_in_fork = 11;

  // Source region front blockages.
  repeated LaneObjectBlockingDebug source_region_front_blockages = 12;
}

// The debug info for early lane change signal.
// nest Available ID: 9
message EarlyLaneChangeSignalInfoDebug {
  LaneChangeMode lane_change_direction = 1;
  int64 source_lane_id = 2;
  int64 target_lane_id = 3;

  LaneChangeInstanceDetail lane_change_instance_detail = 4;

  PreviewRouteDebug min_cost_preview_route_debug = 5;
  PreviewRouteDebug min_lc_count_preview_route_debug = 6;

  bool should_propose_early_lane_change = 7;

  string debug_str = 8;
}

// The debug info for lane change info.
// Next Available ID: 17
message LaneChangeInfoDebug {
  reserved 4;
  string debug_str = 1;

  EarlyLaneChangeSignalInfoDebug early_lane_change_signal_info_debug = 12;

  double turn_light_duration_for_lc = 2;
  double lane_change_completion_rate = 3;

  LaneChangeMetadataDebug lane_change_metadata_debug = 9;

  LaneChangeEnvInfoDebug lane_change_env_info_debug = 5;

  bool can_trigger_crawl = 6;

  int64 elc_triggered_object_id = 7;
  bool should_set_no_yield_for_elc_triggered_object = 8;
  PreviewRouteDebug preview_route_debug = 10;
  LaneChangeSignalSourceType lane_change_signal_source_type = 11;
  LaneSequenceCandidate.LaneSequenceType lane_change_sequence_type = 16;
  bool is_inexecutable_lane_change_signal = 14;

  bool should_creep = 13;
  PreviewRouteDebug preview_only_route_debug = 15;
}

// The debug info for a single preview lane change info.
// Next Available ID: 6
message PreviewedLaneChangeInfoDebug {
  LaneSequenceCandidate.LaneSequenceType lane_sequence_type = 1;
  int64 unique_lane_id = 2;
  PreviewRouteDebug preview_route_debug = 3;
  LaneChangeMetadataDebug lane_change_metadata_debug = 4;
  double difficulty_score = 5;
}

// The debug info for previewed lane change infos.
// Next Available ID: 3
message PreviewedLaneChangeInfosDebug {
  repeated PreviewedLaneChangeInfoDebug previewed_lc_info_debug_list = 1;
  string debug_str = 2;
}

// The debug info for maneuvers in the decoupled arch.
// Next Available ID: 13
message DecoupledManeuverDebug {
  reserved 1, 2;
  repeated LaneSequenceInfoAndIntentPlanDebug single_lane_sequence_plan_debug =
      5;

  ManeuverType type = 3;

  ManeuverRunnableStatus runnable_status = 4;

  TrajectorySelectionDebug trajectory_selection_debug = 6;

  DrivingDirectiveDebug driving_directive_debug = 7;

  LaneChangeInfoDebug lane_change_info_debug = 8;

  // Ttc detect result in exception handler in perfect pose resim; empty on road
  // test.
  TtcDetectResult ttc_detect_result = 9;

  PullOverInfoDebug pull_over_info_debug = 10;

  map<string, planner.cross_lane.pb.CrossLaneInfoDebug> cross_lane_info_map =
      11;

  PreviewedLaneChangeInfosDebug previewed_lane_change_infos_debug = 12;
}

// The debug for all the behavior reasoners.
// Next Available ID: 20
message BehaviorReasonersDebug {
  reserved 1 to 9;
  ManeuverType selected_maneuver_type = 11;
  repeated ManeuverDebug maneuvers = 12;
  repeated DecoupledManeuverDebug decoupled_maneuvers = 15;
  LaneSequenceGeneratorDebug lane_sequence_generator = 10;
  TurnSignalDebug turn_signal_debug = 13;
  AssistDebug assist_debug = 14;
  bool executing_decoupled_arch_trajectory = 16;
  bool use_decoupled_lane_change = 17;
  EgoStuckDebug ego_stuck_debug = 18;
  RemoteWarningDetectorDebug remote_warning_detector_debug = 19;
}

// The debug for ego's inlane params
// Next Available ID: 5
message EgoInLaneParamsDebug {
  google.protobuf.DoubleValue ego_arclength_m = 1;
  google.protobuf.DoubleValue ego_speed_mps = 2;
  google.protobuf.DoubleValue ego_smooth_stopping_distance_m = 3;
  google.protobuf.DoubleValue ego_extreme_stopping_distance_m = 4;
}

// The debug for construction zone
// Next Available ID: 3
message ConstructionZoneDebug {
  bool is_valid = 1;
  repeated ConstructionZoneInLaneStateDebug
      construction_zone_inlane_state_debug = 2;
}

// The debug for construction zone's inlane state
// Next Available ID: 9
message ConstructionZoneInLaneStateDebug {
  int64 id = 1;
  double start_arclength_m = 2;
  double end_arclength_m = 3;
  double encroachment_m = 4;
  double clearance_m = 5;
  double max_encro_arclength_m = 6;
  ConstructionZoneBlockingState.Enum blocking_state = 7;
  int64 num_of_section = 8;
}

// The debug for gear switch reasoner.
// Next Available ID: 3
message GearSwitchDebug {
  bool is_active = 1;
  double elapsed_time_since_activation = 2;
}

// A pb message for struct LaneChangeLongitudinalSpanOptions.
// Next Available ID: 4
message LaneChangeLongitudinalSpanOptionsDebug {
  // Lane change option type.
  enum LcOptionType {
    // It is invalid lc option type and should not be used.
    kInvalid = 0;
    // The default lc longitudinal span. It is a fallback option when both
    // |start_delayed_option| and |earliest_option| is
    // unavailable.
    kDefault = 1;
    // The earliest lc longitudinal span that uses the lc available range start
    // as the lc start. It is the 2nd option when |start_delayed_option| is
    // unavailable.
    kEarliest = 2;
    // The start delayed lc longitudinal span that delays the lc start position
    // to |kMinDistFromRearAxleToStartLcPosIfPossible| from rear axle for
    // lateral comfort.
    kStartDelayed = 3;
    // The comfort priority lc option:
    // 1. delays the lc start position for 0.5s using current speed profile if
    // the rest range is sufficient.
    // 2. make a jerk-smoother lane change if feasible
    // Degisn doc: https://cooper.didichuxing.com/docs2/document/2203736995330
    kComfortPriority = 4;
    // The backup option generates a comfortable lane change option without
    // a gap align solution.
    kBackup = 5;
  };
  math.pb.Range feasible_lc_range = 1;
  map<string, math.pb.Range> lc_options_map = 2;
  LcOptionType selected_lc_option_type = 3;
}

// A pb message for struct LaneChangeSelectedGapInfo.
// Next Available ID: 3
message LaneChangeSelectedGapInfoDebug {
  repeated LaneChangeLongitudinalSpanOptionsDebug
      feasible_lc_ranges_and_options = 1;
  int32 selected_lc_range_and_option_ix = 2;
}

// The debug for geometric guidance generation.
// Next Available ID: 4
message GeometricGuidanceGenerationDebug {
  string debug_str = 1;
  LaneChangeGeometryMetaData lane_change_geometry_meta_data = 2;
  LaneChangeSelectedGapInfoDebug lane_change_selected_gap_info_debug = 3;
}

// The debug for assist taks queue.
// Next Available ID: 5
message AssistTaskSchedulerDebug {
  repeated RemoteAssistTask remote_assist_task = 1;
  repeated RemoteAssistConnectionSignal remote_assist_connection_signal = 2;
  repeated AssistResponse assist_responses = 3;
  repeated AssistResponse remote_assist_responses = 4;
}
