#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_APPROXIMATE_MAP_QUERY_UTIL_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_APPROXIMATE_MAP_QUERY_UTIL_H_

#include <fmt/format.h>
#include <utility>
#include <vector>

#include "geometry/model/point_2d.h"
#include "hdmap/lib/hdmap.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {

namespace route_association {

// NOTE: All util functions declared in this file are supposed to be only used
// for fast map element query in PredictedTrajectoryRouteAssociation under the
// same parent folder. <NAME_EMAIL> for other
// potential usage.

struct QueriedLaneAndApproxDistance {
  const pnc_map::Lane* lane = nullptr;
  double distance = 0.0;
};

double ComputeApproxDistanceBetweenPointAndLane(
    const pnc_map::Lane* lane, const math::geometry::Point2d& point);

std::vector<QueriedLaneAndApproxDistance> GetLanesWithApproxDistanceInRadius(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const hdmap::Point& point, double radius);

}  // namespace route_association
}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_APPROXIMATE_MAP_QUERY_UTIL_H_
