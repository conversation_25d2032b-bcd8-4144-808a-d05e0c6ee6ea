#include "vbag_recorder.h"

#include <cstdint>
#include <memory>
#include <unordered_map>
#include <utility>

#include <boost/filesystem.hpp>
#include <gflags/gflags.h>

#include "bag/metrics_collector.h"
#include "bag/trip_info_recorder.h"
#include "math/unit_conversion.h"
#include "mcap/types.hpp"
#include "strings/stringprintf.h"
#include "topic_utils.h"
#include "vbag/detail/mcap_writer.h"
#include "vbag/exceptions.h"
#include "vbag/types.h"
#include "vbag_protos/vbag_recorder_config.pb.h"
#include "voy_protos/health.pb.h"

DEFINE_int32(slow_io_ms, 0,
             "Slow down flush operation by slow_io_ms, only for testing.");
DEFINE_int32(slow_compression_ms, 0,
             "Slow down compression by slow_compression_ms, only for testing.");
DECLARE_bool(perf_test);
namespace vbag::recorder {
namespace {
// Set a 1Tb size which make it impossible to flush the chunk automatically
constexpr uint64_t kDefaultChunkSize =
    static_cast<uint64_t>(1024) * 1024 * 1024 * 1024;
constexpr uint64_t kMaxBagExpiredTimeS = 10;

// Converts a compression type form bag::Bag::CompressionType to
// rosbag::CompressionType.
mcap::Compression ToMcapCompressionType(
    const pb::BagConfig_CompressionType& compression_type) {
  switch (compression_type) {
    case pb::BagConfig_CompressionType_ZSTD:
      return mcap::Compression::Zstd;
    default:
      return mcap::Compression::None;
  }
}

mcap::CompressionLevel ToMcapCompressionLevel(
    const pb::BagConfig_CompressionLevel& compression_level) {
  switch (compression_level) {
    case pb::BagConfig_CompressionLevel_Fastest:
      return mcap::CompressionLevel::Fastest;
    case pb::BagConfig_CompressionLevel_Fast:
      return mcap::CompressionLevel::Fast;
    case pb::BagConfig_CompressionLevel_Slow:
      return mcap::CompressionLevel::Slow;
    case pb::BagConfig_CompressionLevel_Slowest:
      return mcap::CompressionLevel::Slowest;
    default:
      return mcap::CompressionLevel::Default;
  }
}

}  // namespace

VbagRecorder::VbagRecorder() : mcap_options_("") {}

VbagRecorder::~VbagRecorder() { Close(); }

void VbagRecorder::Close() {
  if (closed_) {
    return;
  }
  LOG(INFO) << "VbagRecorder starts closing.";
  closed_ = true;

  if (pre_bag_) {
    AsyncClose(std::move(pre_bag_));
  }
  if (current_bag_) {
    AsyncClose(std::move(current_bag_));
  }

  while (!pending_closing_bags_.empty()) {
    for (auto it = pending_closing_bags_.begin();
         it != pending_closing_bags_.end();) {
      if (!(*it)->is_open()) {
        it = pending_closing_bags_.erase(it);
      } else {
        it++;
      }
    }
    std::this_thread::sleep_for(std::chrono::milliseconds(10));
  }
  LOG(INFO) << "VbagRecorder finish closing.";
}

void VbagRecorder::CheckExpiredBags(uint64_t cur_timestamp_s) {
  if (current_bag_ && current_bag_->is_open() &&
      cur_timestamp_s >= options_.section_end_time_s + kMaxBagExpiredTimeS) {
    LOG(INFO) << "AsyncClose bag because it's expired: "
              << current_bag_->filename();
    AsyncClose(std::move(current_bag_));
  }
  if (pre_bag_ && pre_bag_->is_open() &&
      cur_timestamp_s >=
          prev_options_.section_end_time_s + kMaxBagExpiredTimeS) {
    LOG(INFO) << "AsyncClose bag because it's expired: "
              << pre_bag_->filename();
    AsyncClose(std::move(pre_bag_));
  }
}

void VbagRecorder::Init(const pb::BagConfig& config,
                        int number_compression_thread,
                        const boost::filesystem::path& output_dir,
                        const std::string& car_id) {
  config_ = config;
  options_.bag_name = config.bag_name();
  options_.car_id = car_id;
  options_.output_dir = output_dir;
  options_.split_count = 0;
  options_.bag_type = config.bag_type();
  // Section start time is the mutiple of |config.max_duration_s()|.
  options_.section_start_time_s = config.start_time_s();
  options_.section_end_time_s =
      config.start_time_s() -
      (config.start_time_s() % config.max_duration_s()) +
      config.max_duration_s();

  mcap_options_.chunkSize = kDefaultChunkSize;
  mcap_options_.parallelism = number_compression_thread;
  mcap_options_.compression = ToMcapCompressionType(config.compression_type());
  mcap_options_.compressionLevel =
      ToMcapCompressionLevel(config.compression_level());
  mcap_options_.useDirectIO = true;
  mcap_options_.slowIOMs = FLAGS_slow_io_ms;
  if (mcap_options_.slowIOMs > 0) {
    LOG(WARNING) << "BE CAUTIOUS! Vbag is set for write slow, every write "
                    "chunk would be slow by "
                 << mcap_options_.slowIOMs << " ms.";
  }
  mcap_options_.slowCompressionMs = FLAGS_slow_compression_ms;
  if (mcap_options_.slowCompressionMs > 0) {
    LOG(WARNING) << "BE CAUTIOUS! Vbag is set for do compression slow, every "
                    "chunk compression would be slow by "
                 << mcap_options_.slowCompressionMs << " ms.";
  }

  current_bag_ = std::make_unique<detail::McapWriter>();

  closed_ = false;
}

void VbagRecorder::Write(MessageRecordPtr&& record) {
  if (closed_) {
    LOG(ERROR) << "Failed to write message record " << record->topic() << " "
               << record->publish_timestamp()
               << " since vbag recorder is already closed";
    if (!record->IsDropped()) {
      bag::MetricCollectingService::GetInstance().DropMessage(
          /*bag_name=*/"", record->topic(), record->data_array(),
          record->info_ptr());
    }
    return;
  }
  // Retrieve the bag that contains the message timestamp. If the bag hasn't
  // been opened before, open it.
  detail::McapWriter* bag = nullptr;
  try {
    bag = GetBagWriter(record);
  } catch (const vbag::BagException& ex) {
    LOG_EVERY_N(ERROR, 100) << "Error Get Bag Writer: " << ex.what();
  } catch (const std::exception& ex) {
    LOG_EVERY_N(ERROR, 100) << "Error Get Bag Writer: " << ex.what();
  } catch (...) {
    LOG_EVERY_N(ERROR, 100) << "Error Get Bag Writer: ";
  }
  // If it's a nullptr, it means the record_time not in
  // both |pre_bag_| and |current_bag_|, we just return and ignore the messages.
  if (!bag) {
    std::string errors;
    strings::SStringPrintf(&errors,
                           "Failed to get bag, topic: %s, pub_timestamp: "
                           "%.3lf, log_timestamp: %.3lf",
                           record->topic().c_str(),
                           math::Ns2SDouble(record->publish_timestamp()),
                           math::Ns2SDouble(record->log_timestamp()));
    if (current_bag_) {
      strings::StringAppendF(
          &errors, ", current bag from %" PRIu64 " to %" PRIu64,
          options_.section_start_time_s, options_.section_end_time_s);
    }
    if (pre_bag_) {
      strings::StringAppendF(
          &errors, ", previous bag from %" PRIu64 " to %" PRIu64,
          prev_options_.section_start_time_s, prev_options_.section_end_time_s);
    }
    LOG_EVERY_N(ERROR, 100) << errors;
    if (!record->IsDropped()) {
      bag::MetricCollectingService::GetInstance().DropMessage(
          /*bag_name=*/"", record->topic(), record->data_array(),
          record->info_ptr());
    }
    return;
  }

  if (!bag->ContainsStream(StreamType::Topic, record->topic())) {
    auto [data_type, md5, msg_def] = GetTopicMetadata(record->topic());
    std::unordered_map<std::string, std::string> metadata{{kMd5Key, md5}};
    bag->AddStream(StreamType::Topic, record->topic(), data_type, msg_def,
                   metadata);
  }

  uint64_t record_size =
      record->IsDropped() ? kDropMessageLength : record->size();
  try {
    bag->Write(StreamType::Topic, record->topic(), record->publish_timestamp(),
               record->log_timestamp(), record_size, record->data(),
               record->metadata());
  } catch (const vbag::BagIOException& ex) {
    fault_reporter_.AddFault(::pb::RecorderFaultCode::WRITE_FAILURE,
                             "Cannot write data into bag file.");
    LOG(ERROR) << "Error writing: " << ex.what();
    if (!record->IsDropped()) {
      bag::MetricCollectingService::GetInstance().DropMessage(
          /*bag_name=*/"", record->topic(), record->data_array(),
          record->info_ptr());
    }
  }
}

int VbagRecorder::Flush(std::function<void(const mcap::Status&)> callback) {
  int pending_chunks = 0;
  if (pre_bag_) {
    pre_bag_->Flush(callback);
    pending_chunks++;
  }
  if (current_bag_) {
    current_bag_->Flush(std::move(callback));
    pending_chunks++;
  }
  return pending_chunks;
}

void VbagRecorder::CreateNextWriter() {
  if (pre_bag_) {
    AsyncClose(std::move(pre_bag_));
  }

  // Remove and destruct pending bag if it's closed.
  for (auto it = pending_closing_bags_.begin();
       it != pending_closing_bags_.end();) {
    if (!(*it)->is_open()) {
      it = pending_closing_bags_.erase(it);
    } else {
      it++;
    }
  }

  prev_options_ = options_;

  options_.split_count++;
  options_.section_start_time_s = options_.section_end_time_s;
  options_.section_end_time_s += config_.max_duration_s();
  if (options_.bag_type == vbag::pb::BagConfig_BagType_NORMAL &&
      !FLAGS_perf_test) {
    options_.output_dir =
        bag::TripInfoRecorder::GetInstance().GetExternalBagPath();
  }

  pre_bag_ = std::move(current_bag_);
  current_bag_ = std::make_unique<detail::McapWriter>();
}

detail::McapWriter* VbagRecorder::GetBagWriter(const MessageRecordPtr& record) {
  uint64_t publish_timestamp_s = math::Ns2Sec(record->publish_timestamp());
  while (publish_timestamp_s >= options_.section_end_time_s) {
    LOG(INFO) << "Creating the next bag, type: " << options_.bag_name
              << ", current bag start timestamp: "
              << options_.section_start_time_s
              << "; Triggered message topic: " << record->topic()
              << ", pub timestamp: " << record->publish_timestamp();
    CreateNextWriter();
  }

  if (current_bag_ && publish_timestamp_s >= options_.section_start_time_s &&
      publish_timestamp_s < options_.section_end_time_s) {
    // TODO(zecao): find and fix the root cause of same name files coexist
    if (current_bag_->is_close_called()) {
      LOG(ERROR) << "current_bag " << current_bag_->filename()
                 << " closed, message skipped.";
      return nullptr;
    }

    if (!current_bag_->is_open()) {
      current_bag_->Open(options_.generate_bag_path_str(), mcap_options_,
                         /*create_temp_file=*/true,
                         /*delete_if_empty=*/true);
      LOG(INFO) << "Create new bag file " << current_bag_->filename();
    }
    return current_bag_.get();
  }

  if (pre_bag_ && publish_timestamp_s >= prev_options_.section_start_time_s &&
      publish_timestamp_s < prev_options_.section_end_time_s) {
    if (pre_bag_->is_close_called()) {
      LOG(ERROR) << "pre_bag " << pre_bag_->filename()
                 << " closed, message skipped.";
      return nullptr;
    }

    if (!pre_bag_->is_open()) {
      pre_bag_->Open(prev_options_.generate_bag_path_str(), mcap_options_,
                     /*create_temp_file=*/true,
                     /*delete_if_empty=*/true);
      LOG(INFO) << "Create new bag file " << pre_bag_->filename();
    }
    return pre_bag_.get();
  }

  return nullptr;
}

void VbagRecorder::AsyncClose(std::unique_ptr<detail::McapWriter>&& writer) {
  if (!writer->is_open()) {
    return;
  }
  LOG(INFO) << "Async close file " << writer->filename();
  writer->AsyncClose();
  // Add prev bag to the pending closing bag sets.
  pending_closing_bags_.emplace(std::move(writer));
}

std::string VbagRecorder::Options::generate_bag_path_str() const {
  // For realtime bags, we has different name schema.
  if (bag_type == pb::BagConfig::RT_UPLOAD_BAG) {
    const auto start_time = static_cast<time_t>(1000LL * section_start_time_s);
    const auto end_time = static_cast<time_t>(1000LL * section_end_time_s);
    char filename[80];
    snprintf(filename, sizeof(filename), "%s_%s_%ld_%ld.vbag", car_id.c_str(),
             bag_name.c_str(), start_time, end_time);
    return (output_dir / filename).string();
  }

  // TODO(zecao): get start time from ros parameter to align between
  // cpubox 1 & 2.
  char datetime[20];
  const auto start_time = static_cast<time_t>(section_start_time_s);

  strftime(datetime, sizeof(datetime), "%Y-%m-%d-%H-%M-%S",
           localtime(&start_time));

  // gen2 filename is like "99999_camera_2020-11-19-13-18-20_0.vbag"
  // gen3 filename is like "17140_camera_box1_2022-04-19-11-06-20_0.vbag"
  // TODO(zecao): split platform information from bag_name.
  std::string filename = strings::StringPrintf("%s_%s_%s.vbag", car_id.c_str(),
                                               bag_name.c_str(), datetime);

  boost::filesystem::path full_path = output_dir;
  full_path /= filename;
  return full_path.string();
}

}  // namespace vbag::recorder
