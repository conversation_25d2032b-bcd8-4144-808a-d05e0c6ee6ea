#include "common_math_library/common_method.h"

#include <math.h>
#include <stdbool.h>

float CML_SafeDivision(float dividend, float divisor) {
  return CML_Sign(divisor) * dividend / fmaxf(fabsf(divisor), CML_EPSILON);
}

float CML_Limit(float input, float lower_limit, float upper_limit) {
  if (upper_limit < lower_limit) {
    float temp = lower_limit;
    lower_limit = upper_limit;
    upper_limit = temp;
  } else {
    // do nothing
  }

  return fminf(fmaxf(input, lower_limit), upper_limit);
}

void CML_RateLimit(float input, float lower_rate_limit, float upper_rate_limit,
                   float task_cycle_time, float *last_cycle_output) {
  float max_limit = *last_cycle_output + lower_rate_limit * task_cycle_time;
  float min_limit = *last_cycle_output + upper_rate_limit * task_cycle_time;

  *last_cycle_output = CML_Limit(input, min_limit, max_limit);
}

void CML_LowPassFilter(float input, float *output, float filter_time,
                       float task_cycle_time) {
  float filter_coeff = task_cycle_time / fmaxf(filter_time, task_cycle_time);
  *output = filter_coeff * input + (1.0 - filter_coeff) * (*output);
}
