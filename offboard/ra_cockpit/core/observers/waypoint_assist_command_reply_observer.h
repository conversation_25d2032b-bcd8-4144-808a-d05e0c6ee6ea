#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_WAYPOINT_ASSIST_COMMAND_REPLY_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_WAYPOINT_ASSIST_COMMAND_REPLY_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class WaypointAssistCommandObserver : public IDataObserver {
 public:
  using WaypointAssistCommandCallback = std::function<void(
      std::shared_ptr<teleassist::pb::WaypointAssistCommandReply>,
      DataChannel)>;

  explicit WaypointAssistCommandObserver(
      WaypointAssistCommandCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  WaypointAssistCommandCallback waypoint_assist_command_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_WAYPOINT_ASSIST_COMMAND_REPLY_OBSERVER_H_
