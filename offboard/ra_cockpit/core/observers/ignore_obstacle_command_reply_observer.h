#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_IGNORE_OBSTACLE_COMMAND_REPLY_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_IGNORE_OBSTACLE_COMMAND_REPLY_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class IgnoreObstacleCommandReplyObserver : public IDataObserver {
 public:
  using IgnoreObstacleCommandReplyCallback = std::function<void(
      std::shared_ptr<teleassist::pb::IgnoreObstacleCommandReply>,
      DataChannel)>;

  explicit IgnoreObstacleCommandReplyObserver(
      IgnoreObstacleCommandReplyCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  IgnoreObstacleCommandReplyCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_IGNORE_OBSTACLE_COMMAND_REPLY_OBSERVER_H_
