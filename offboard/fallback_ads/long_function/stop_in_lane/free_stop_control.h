#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_FREE_STOP_CONTROL_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_FREE_STOP_CONTROL_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#include "long_function/long_function_type.h"

extern float CalculateFreeStopDeceleration(float ego_velocity);
extern float CalculateAccelRequestByLaneLength(float ego_velocity,
                                               LaneInfo left_lane_info,
                                               LaneInfo right_lane_info);
#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_FREE_STOP_CONTROL_H_
