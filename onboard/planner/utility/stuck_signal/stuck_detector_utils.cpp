#include "planner/utility/stuck_signal/stuck_detector_utils.h"

#include <algorithm>
#include <cmath>
#include <cstdint>
#include <glog/logging.h>
#include <limits>
#include <memory>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "geometry/model/point_2d.h"
#include "gtl/map_util.h"
#include "math/math_util.h"
#include "math/piecewise_linear_function.h"
#include "math/range.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/decoupled_maneuvers/lane_change/early_lane_change_signal_reasoner_util.h"
#include "planner/elective_lane_change/elective_lane_change_decider_utils.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/selection_debug.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

namespace {

// The heading angle difference range to determine whether two objects are
// driving in different direction.
constexpr double kHeadingAngleDiffRangeMinInRad = 1.0 / 3.0 * M_PI;
constexpr double kHeadingAngleDiffRangeMaxInRad = 2.0 / 3.0 * M_PI;

// The l2 distance threshold to determine whether space in front of Ego is clear
// or not in junction area.
constexpr double kClearDistanceThresholdInJunctionInM = 15.0;

// The l2 distance threshold to determine if Ego is close to junction area or
// not.
constexpr double kCloseDistanceThrehosldToJunctionInM = 15.0;

// The object lane infringement ratio threshold to consider it during waiting
// in traffic calculation.
constexpr double kInfringementRatioThreshold = 0.5;

// The distance thresholds when determining close to junction cost.
constexpr double kIsWaitingInTrafficJamCloseDistanceInM = 90.0;
constexpr double kIsWaitingInTrafficJamFartherDistanceInM = 150.0;

// The distance threshold for calculating traffic jam density cost.
constexpr double kDistanceThresholdForDensityCalculationInM = 40.0;

// The height threhshold of the vehicle that will considered blocking Ego's
// view. This value is set to be a bit lower than Gen3 vehicle's height to
// reduce the false negative rate of occlusion detection.
constexpr double kHeightThresholdToConsiderBlockingInM = 2.0;

// The speed threshold for determining a temp parking car.
[[maybe_unused]] constexpr double kSpeedThresholdForTempParkingCarInMps = 0.1;
// The duration of remain stationary for determining a temp parking car.
constexpr double kStationaryDurationForTempParkingCarInMs = 10000;
// Number of cars to check to consider stuck vehicle is temp parking.
// If there are several consecutive vehicles that are statioanry and pulled
// over to the curb, then we can think they are temp parking cars.
constexpr int kNumOfConsecutiveVehiclesToCheckPullover = 3;

// The distance threshold to distinguish temp parking vehicles.
constexpr double kDistanceThresholdToDistinguishTempParkingCarInM = 100;

// The distance gap between two vehicles or between one vehicle and junction
// to think there is plenty room ahead to fit in.
constexpr double kDistanceWindowToThinkEgoCanFitInM = 40.0;

// The heading angle difference threshold between object and center line to
// think object is not pulled over. (20 degrees)
constexpr double kNonParkingHeadingAngleDiffInRad = M_PI / 9.0;

// Threshold under which we will consider that we are first the vehicle waiting
// for traffic light. From issues it is generally around 8m.
constexpr double kFirstVehicleWaitingForTrafficLightThresholdInM = 10.0;

// Longitudinal distance threshold from Ego rear axle to traffic light to
// determine whether ignore traffic light color or not.
constexpr double kDistanceThresholdToIgnoreTLColorInM = 80.0;

// Time threshold under which we will consider that the light just turned green.
constexpr int64_t kThresholdConsiderTrafficLightAsJustTurnGreenInMsec = 5000;

// Look forward distance when evaluate route preview.
constexpr double kRoutePreviewLookForwardThresholdInM = 20.0;

// Distance threshold to consider neighbor lane objects when evaluating traffic
// density.
constexpr double kDistThresholdToConsiderObjectInM = 25.0;

// Length threshold to double object count during traffic evaluation.
constexpr double kLengthThresholdForLongObjectInM = 8.0;

// Longitudinally distance threshold to think two vehicles are close/next to
// each other.
constexpr double kDistanceThresholdToConsiderVehiclesAdjacentInM = 10.0;

// The waiting in traffic cost difference threshold to trigger extra neighbor
// lane traffic analysis.
constexpr double kCostDiffThresholdToTriggerExtraTrafficAnalysis = 10.0;

// The minimum number of dominant objects that we will think dominant objects
// are changing frequently.
constexpr int64_t kMinNumOfDominantObjects = 3;

// The maximum longitudinal distance to find the non-dominant stuck object.
constexpr double kMaxLonDistanceToFindNonDominantStuckObjectInM = 20.0;

// The ratio of uturn lane to think Ego is exiting uturn.
constexpr double kUturnLaneRatioToThinkEgoIsExiting = 0.5;

// Calculate whether the controlled lane's traffic light is red.
bool IsTrafficLightRed(
    const std::vector<speed::traffic_rules::TrafficLightInfo>& traffic_lights,
    const pnc_map::Lane& controlled_lane) {
  for (const auto& light_info : traffic_lights) {
    if (light_info.controlled_lane().id() != controlled_lane.id() ||
        light_info.traffic_light_detected() == nullptr) {
      continue;
    }
    if (light_info.is_red(/*sustained=*/true)) {
      return true;
    }
  }
  return false;
}

// Calculate whether the controlled lane's traffic light is going to be red soon
// or not.
bool IsTrafficLightTurningRedSoon(
    const std::vector<speed::traffic_rules::TrafficLightInfo>& traffic_lights,
    const pnc_map::Lane& controlled_lane) {
  for (const auto& light_info : traffic_lights) {
    if (light_info.controlled_lane().id() != controlled_lane.id() ||
        light_info.traffic_light_detected() == nullptr) {
      continue;
    }
    if (light_info.is_yellow_flashing(/*sustained=*/true) ||
        light_info.is_yellow_non_flashing(/*sustained=*/true) ||
        light_info.is_green_flashing(/*sustained=*/true)) {
      return true;
    }
  }
  return false;
}

// Evaluate whether an object is in different route with Ego or not.
// Currently only use this in junction area.
bool IsObjectInDifferentRouteWithEgo(
    const PlannerObject& object, const RobotStateSnapshot& ego_state_snapshot,
    const pnc_map::Lane* current_lane) {
  if (current_lane == nullptr) {
    return false;
  }

  const double object_heading_rad = object.box_heading();
  const double ego_heading_rad = ego_state_snapshot.heading();
  double abs_heading_diff_rad = std::abs(
      math::NormalizeMinusPiToPi(ego_heading_rad - object_heading_rad));
  bool is_ego_and_object_in_different_route =
      math::IsInRange(abs_heading_diff_rad, kHeadingAngleDiffRangeMinInRad,
                      kHeadingAngleDiffRangeMaxInRad);

  if (object.is_vehicle()) {
    // If stuck object is vehicle, add turning signal evaluation to help better
    // recall object that is not in same route as ego. Currently we only evalute
    // it when Ego is driving straight.
    // TODO(jinghuang1): Handle Ego is turning while agent turning in different
    // direction.
    const bool is_ego_driving_straight =
        (current_lane->turn() == hdmap::Lane_Turn_STRAIGHT);
    const bool is_object_turning =
        object.HasPerceptionAttribute(
            voy::perception::Attribute::VEHICLE_LEFT_TURN_SIGNAL_ON) ||
        object.HasPerceptionAttribute(
            voy::perception::Attribute::VEHICLE_RIGHT_TURN_SIGNAL_ON);
    if (is_ego_driving_straight && is_object_turning) {
      is_ego_and_object_in_different_route = true;
    }
  }

  return is_ego_and_object_in_different_route;
}

// Calculate whether Ego is waiting for traffic with a cyclist meanwhile there
// is no stopped vehicle in front.
bool IsWaitingForTrafficWithCyclist(
    const selection::TrajectoryMetaData& candidate,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const PlannerObject& stuck_object,
    const math::geometry::PolygonWithCache2d& object_contour,
    const RobotStateSnapshot& ego_state_snapshot) {
  if (!vehicles_on_lane_sequence.empty() || !stuck_object.is_cyclist()) {
    // If there are stopped vehicles ahead Ego or the stuck object is not
    // cyclist, return false.
    return false;
  }

  bool is_cyclist_in_associated_lane = false;
  for (const pnc_map::Lane* lane : candidate.lane_sequence()) {
    if (math::geometry::Intersects(lane->border(), object_contour)) {
      is_cyclist_in_associated_lane = true;
      break;
    }
  }
  if (!is_cyclist_in_associated_lane) {
    // If the stuck cyclist is not in our associated lane, we cannot be waiting
    // traffic with it.
    return false;
  }

  const pnc_map::Lane* current_lane = candidate.current_lane();
  DCHECK(current_lane != nullptr);

  const bool is_ego_and_cyclist_in_different_route =
      IsObjectInDifferentRouteWithEgo(stuck_object, ego_state_snapshot,
                                      candidate.current_lane());

  if (current_lane->IsInJunction()) {
    // When Ego is currently inside junction:
    // If Ego and cyclist is in same route, then we should be waiting for
    // traffic together with it. If not, then we are stuck by this cyclist.
    return !is_ego_and_cyclist_in_different_route;
  }

  // Calculate the stuck cyclist's distance to junction.
  const double cyc_dist_to_junction_m = GetDistanceToJunction(
      stuck_object.contour(), stuck_object.center_2d(),
      /*current_lane=*/nullptr, candidate.lane_sequence());

  const auto& yield_fences =
      candidate.speed_result.yielding_fence_list().fence_list();
  bool is_cyc_yielding_traffic_light = false;
  const auto stuck_object_yield_fence_iter =
      std::find_if(yield_fences.begin(), yield_fences.end(),
                   [&stuck_object](const auto& fence) {
                     return fence.obj_id() == stuck_object.id();
                   });
  if (stuck_object_yield_fence_iter != yield_fences.end()) {
    const double dist_to_stuck_object_fence =
        stuck_object_yield_fence_iter->front_bumper_to_fence_m();

    for (const auto& fence : yield_fences) {
      if (fence.type() == planner::speed::pb::FenceType::kTrafficLight &&
          std::abs(fence.front_bumper_to_fence_m() -
                   dist_to_stuck_object_fence) <
              kCloseDistanceThrehosldToJunctionInM) {
        is_cyc_yielding_traffic_light = true;
        break;
      }
    }

    if (FirstTrafficLightJustTurnedGreen(
            candidate.trajectory_info->traffic_rules().traffic_lights)) {
      // If traffic light just turned green, cyclist might not respond quick
      // enough, so still treat it as yield traffic light.
      is_cyc_yielding_traffic_light = true;
    }
  }

  if ((cyc_dist_to_junction_m < kCloseDistanceThrehosldToJunctionInM ||
       is_cyc_yielding_traffic_light) &&
      !is_ego_and_cyclist_in_different_route) {
    return true;
  }

  return false;
}

// Determine whether agent in pulled over to right side curb by two conditions:
// 1. Agent is not fully inside the right boundary, meaning the agent is
// violating the right lane marking.
// 2. Agent's angle w.r.t center line heading is less than a threshold, meaning
// agent is leaning towards right side and is not pulling out. (Note, this check
// is only valid for stationary agents that are on right most lane.)
inline bool IsAgentPulledOverToRight(
    const AgentSnapshotInLaneParam& agent_inlane_param) {
  return !agent_inlane_param.is_fully_inside_right_boundary &&
         agent_inlane_param.relative_heading_rad <
             kNonParkingHeadingAngleDiffInRad;
}

// Check whether Ego is waiting for traffic or not when there is
// no car in front of Ego.
bool IsFirstVehicleWaitingForTl(
    const speed::SpeedResult& speed_result,
    const std::vector<speed::traffic_rules::TrafficLightInfo>&
        traffic_light_info) {
  if (traffic_light_info.empty()) {
    // If there are no TL in front, directly return false.
    return false;
  }
  const auto& first_tl = traffic_light_info.front();
  if (first_tl.start_ra_arclength() >= 80.0 ||
      first_tl.color() == voy::TrafficLight::GREEN) {
    // If the first TL is far away from ego, return false.
    // If traffic light is green, since Ego is the first vehicle in its lane
    // before traffic light, we shouldn't be waiting for traffic light. Directly
    // return false.
    return false;
  }

  std::optional<double> traffic_light_first_s = std::nullopt;
  for (size_t i = 0; i < speed_result.constraints().size(); ++i) {
    const speed::Constraint& constraint = speed_result.constraints()[i];
    if (!speed::IsTrafficLightConstraint(constraint)) {
      continue;
    }
    if (speed_result.decisions()[i] == speed::pb::YIELD) {
      double max_discomfort_tl_start_s =
          constraint.states.front().start_x(speed::Discomforts::kMax);
      if (!traffic_light_first_s.has_value() ||
          *traffic_light_first_s > max_discomfort_tl_start_s) {
        traffic_light_first_s = max_discomfort_tl_start_s;
      }
    }
  }

  // If Ego has constraint for waiting for traffic light and the distance to the
  // tl stop line is less than threshold, we think Ego is waiting for tl.
  // Otherwise, we don't think Ego is waiting for tl, and Ego should be able to
  // creep forward a bit.
  return traffic_light_first_s.has_value() &&
         *traffic_light_first_s <
             kFirstVehicleWaitingForTrafficLightThresholdInM;
}

void UpdateDensityCostBasedOnNeighborDensity(
    const NeighborLaneDensity& neighbor_densities,
    const pb::StuckDetectorConfig& stuck_parameters,
    const double interim_total_cost, double& density_cost) {
  if (stuck_parameters.traffic_jam_trigger_cost() - interim_total_cost >
          -math::constants::kEpsilon &&
      stuck_parameters.traffic_jam_trigger_cost() - interim_total_cost <
          kCostDiffThresholdToTriggerExtraTrafficAnalysis &&
      density_cost > 0.0) {
    // If the current total cost is slightly smaller than the traffic jam cost,
    // and the current lane density cost is greater than 0.0 meaning there are
    // some traffic in current lane. It means the situation is corner case and
    // we may need extra neighbor lane traffic understanding to help determine
    // whether we are waiting in traffic or not.
    // TODO(jinghuang1): Change density type from double to int.
    const double left_density =
        neighbor_densities.optional_left_lane_density.has_value()
            ? neighbor_densities.optional_left_lane_density.value()
            : 0.0;
    const double right_density =
        neighbor_densities.optional_right_lane_density.has_value()
            ? neighbor_densities.optional_right_lane_density.value()
            : 0.0;
    if (std::max(left_density, right_density) >=
        stuck_parameters.max_density()) {
      // If one of the neighbor lanes has large traffic density, we will
      // increase the density cost so that the total cost will be larger than
      // the traffic jam trigger cost.
      density_cost += (stuck_parameters.traffic_jam_trigger_cost() -
                       interim_total_cost + 1);
      return;
    }
  }

  if (!neighbor_densities.optional_left_lane_density.has_value() ||
      !neighbor_densities.optional_right_lane_density.has_value()) {
    // Left side lane density is much more important than right side lane
    // because right side lane density could be temporary parking cars which is
    // invalid density. But for first version, we will only update density cost
    // if both neighbor lanes have valid density evaluation.
    return;
  }

  // TODO(jinghuang1): Make use of neighbor lane density to do more evaluations:
  // Two more aggressive situations are:
  // 1. When left and right traffic both exist and are both very light, and when
  // current lane density cost is 0.0 return negative density cost so that we
  // won't think Ego is waiting in traffic.
  // 2. When both sides traffic exist but only 1 side traffic is very light,
  // reduce the density cost to make it easier to think Ego is not waiting in
  // traffic.
  const bool is_left_traffic_heavy =
      (neighbor_densities.optional_left_lane_density.value() >=
       stuck_parameters.max_density());
  const bool is_right_traffic_heavy =
      (neighbor_densities.optional_right_lane_density.value() >=
       stuck_parameters.max_density());
  if (is_left_traffic_heavy && is_right_traffic_heavy) {
    // If both left and right traffic is heavy, we are waiting in traffic.
    density_cost = stuck_parameters.traffic_jam_trigger_cost() + 1.0;
  }

  return;
}

double CalcDistanceToJunctionCost(
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const TrafficJamOcclusionMetaData& occlusion_data,
    const pb::StuckDetectorConfig& stuck_parameters,
    const math::geometry::PolylineCurve2d& nominal_path,
    const pnc_map::Lane* current_lane, const PlannerObject& stuck_object,
    const AgentInLaneStates& stuck_object_in_lane_state,
    const math::PiecewiseLinearFunction close_to_junction_cost_at_distance,
    double distance_to_junction, double ego_arclength_m) {
  double close_to_junction_cost =
      vehicles_on_lane_sequence.front().object->scenario_type().Has(
          prediction::pb::ScenarioType::VEHICLE_JUNCTION)
          ? stuck_parameters.close_to_junction_cost()
          : close_to_junction_cost_at_distance(distance_to_junction);

  if (!current_lane->IsInJunction() || !stuck_object.is_vehicle()) {
    // If Ego is not inside junction, or the front stuck object is not vehicle
    // type. Just return the nominal junction cost.
    return close_to_junction_cost;
  }

  const math::geometry::Point2d& current_lane_end_pt =
      current_lane->center_line().GetEndPoint();
  const double current_lane_end_arclength_m =
      nominal_path
          .GetProximity(current_lane_end_pt,
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double stuck_object_arclength_m =
      stuck_object_in_lane_state.tracked_state.inlane_param.start_arclength_m;
  constexpr double kLengthRatioToDetermineExitingJunction = 0.2;
  if ((current_lane_end_arclength_m - ego_arclength_m) /
              current_lane->length() <
          kLengthRatioToDetermineExitingJunction &&
      stuck_object_arclength_m > current_lane_end_arclength_m &&
      !occlusion_data.optional_occlusion_vehicle_id.has_value()) {
    // If Ego is still in junction, but Ego is almost going out of it, but Ego
    // get stuck by a car in front of Ego out of junction, if there is no
    // occlusion, then we want to set the distance to junction cost to be 0.0,
    // to let the density cost and traffic light cost determine whether Ego is
    // waiting in traffic or not.
    return 0.0;
  }
  return close_to_junction_cost;
}

// Obtain statioanry objects that possibily block Ego in front.
std::unordered_set<int64_t> ObtainInlaneStationaryObjectsWithinHorizon(
    const selection::TrajectoryMetaData& candidate,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    double look_forward_horizon_m) {
  std::unordered_set<int64_t> stationary_objects;
  const double ego_front_bumper_arclength_m =
      candidate.ego_in_lane_params().front_bumper_arclength;
  if (FLAGS_planning_enable_object_occupancy_state) {
    for (const auto& [typed_object_id, object_reasoning_info] :
         candidate.object_reasoning_info_map()) {
      const int64_t object_id = typed_object_id.id;
      const auto object_iter =
          candidate.object_occupancy_state_map().find(object_id);
      if (object_iter == candidate.object_occupancy_state_map().end()) {
        continue;
      }
      const std::unique_ptr<ObjectOccupancyState>& object_occupancy_state_ptr =
          object_iter->second;
      const ObjectOccupancyParam& object_occupancy_param =
          object_occupancy_state_ptr->current_snapshot_info()
              .object_occupancy_param();
      const PlannerObject& object =
          object_occupancy_state_ptr->planner_object();

      if (!object.is_stationary() ||
          object_occupancy_param.cross_track_encroachment_m < 0.0 ||
          object_occupancy_param.full_body_start_arclength_m -
                  ego_front_bumper_arclength_m >
              look_forward_horizon_m) {
        // If object is not stationary, out of lane or out of look forward
        // horizon, continue.
        continue;
      }
      stationary_objects.insert(object_id);
    }
  } else {
    for (const auto& agent_in_lane_state :
         candidate.agent_in_lane_states_map()) {
      const int64_t agent_id = agent_in_lane_state.first;
      const auto object_iter = planner_object_map.find(agent_id);
      if (object_iter == planner_object_map.end()) {
        continue;
      }
      const AgentSnapshotInLaneParam& agent_inlane_param =
          DCHECK_NOTNULL(agent_in_lane_state.second)
              ->tracked_state.inlane_param;
      const PlannerObject& object = object_iter->second;
      if (!object.is_stationary() ||
          agent_inlane_param.cross_track_encroachment_m < 0.0 ||
          agent_inlane_param.full_body_start_arclength_m -
                  ego_front_bumper_arclength_m >
              look_forward_horizon_m) {
        // If object is not stationary, out of lane or out of look forward
        // horizon, continue.
        continue;
      }
      stationary_objects.insert(agent_id);
    }
  }

  return stationary_objects;
}

// Obtain moving object's primary predicted arclength range.
// Return nullopt if object is stationary.
std::optional<math::Range1d> ObtainMovingObjectArclengthRange(
    const selection::TrajectoryMetaData& candidate,
    const PlannerObject& object) {
  if (object.is_stationary()) {
    return std::nullopt;
  }
  math::Range1d arclength_range;
  if (FLAGS_planning_enable_object_occupancy_state) {
    const auto& object_occupancy_states_map =
        candidate.object_occupancy_state_map();
    const auto& moving_object_states =
        gtl::FindOrDie(object_occupancy_states_map, object.id());
    arclength_range.start_pos = DCHECK_NOTNULL(moving_object_states)
                                    ->current_snapshot_info()
                                    .object_occupancy_param()
                                    .full_body_end_arclength_m;

    // Check moving objects' predicted trajectory.
    const auto& moving_object_predicted_states = gtl::FindOrDie(
        DCHECK_NOTNULL(moving_object_states)
            ->predicted_trajectory_occupancy_states(),
        DCHECK_NOTNULL(moving_object_states)->primary_trajectory_id());
    DCHECK(!moving_object_predicted_states.predicted_states().empty());
    arclength_range.end_pos = moving_object_predicted_states.predicted_states()
                                  .back()
                                  .object_occupancy_param()
                                  .full_body_start_arclength_m;

  } else {
    const auto& agent_inlane_states_map = candidate.agent_in_lane_states_map();
    const auto& moving_object_states =
        gtl::FindOrDie(agent_inlane_states_map, object.id());
    arclength_range.start_pos =
        DCHECK_NOTNULL(moving_object_states)
            ->tracked_state.inlane_param.full_body_end_arclength_m;

    // Check moving objects' predicted trajectory.
    const auto& moving_object_predicted_states = gtl::FindOrDie(
        DCHECK_NOTNULL(moving_object_states)->predicted_trajectories,
        DCHECK_NOTNULL(moving_object_states)->primary_trajectory_id);
    DCHECK(!moving_object_predicted_states.predicted_states.empty());
    arclength_range.end_pos =
        moving_object_predicted_states.predicted_states.back()
            .inlane_state.inlane_param.full_body_start_arclength_m;
  }

  return std::make_optional<math::Range1d>(arclength_range);
}

// Returns the blockage object constraint index if targeted overtaking scenario.
// Otherwise return nullopt.
std::optional<int64_t> TargetOvertakingBlockageObjectSituation(
    const selection::TrajectoryMetaData& candidate,
    const speed::DiscomfortVaryingMinRange& min_range,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    ObjectId initial_dominant_obj_id, double min_range_max_required_lat_gap,
    double ra_to_fb_shift) {
  const auto initial_dominant_obj_iter =
      planner_object_map.find(initial_dominant_obj_id);
  if (initial_dominant_obj_iter == planner_object_map.end() ||
      planner_object_map.at(initial_dominant_obj_id).is_stationary()) {
    return std::nullopt;
  }

  // If initial dominant object is not stationary, try to see if it is
  // overtaking a front blockage object, if so, directly return the front
  // blockage object's constraint index.
  constexpr double kDistanceToBlockageObjectBufferToAllowEarlyReaction = 25.0;
  const auto possible_stuck_objects =
      ObtainInlaneStationaryObjectsWithinHorizon(
          candidate, planner_object_map,
          kDistanceToBlockageObjectBufferToAllowEarlyReaction);
  if (possible_stuck_objects.empty()) {
    return std::nullopt;
  }

  const auto& speed_result = candidate.speed_result;
  const PlannerObject& initial_dominant_obj = initial_dominant_obj_iter->second;
  double next_closest_ra_dist_to_subject = 0.0;
  const auto next_dominant_ix =
      speed::FindClosestYieldConstraintIfShouldConsider(
          speed_result.constraints(), speed_result.decisions(),
          [&initial_dominant_obj,
           &possible_stuck_objects](const speed::Constraint& constraint) {
            if (constraint.type ==
                    speed::ConstraintType::Constraint_Type_NO_BLOCK ||
                constraint.obj_id == initial_dominant_obj.id() ||
                possible_stuck_objects.count(constraint.obj_id) == 0) {
              // Don't consider no block constraint, soft constraint, initial
              // dominant object and object that is not stationary within
              // horizon.
              return false;
            }
            return true;
          },
          min_range, min_range_max_required_lat_gap, ra_to_fb_shift,
          &next_closest_ra_dist_to_subject);
  if (next_dominant_ix == -1) {
    // If we didn't find next dominant constraint idx that satisfy condition,
    // just return nullopt.
    return std::nullopt;
  }
  const ObjectId next_obj_id =
      speed_result.constraints()[next_dominant_ix].obj_id;
  const auto next_obj_iter = planner_object_map.find(next_obj_id);
  const bool is_next_obj_blockage =
      (speed_result.constraints()[next_dominant_ix].IsConstructionZone() ||
       (next_obj_iter != planner_object_map.end() &&
        next_obj_iter->second.is_blockage_object()));
  if (!is_next_obj_blockage) {
    // If the next stuck constraint is not a blockage object, return nullopt.
    return std::nullopt;
  }

  double next_obj_overtake_arclength_m = 0.0;
  if (speed_result.constraints()[next_dominant_ix].IsConstructionZone()) {
    const auto& cz_states = candidate.construction_zones_inlane_state();
    const auto& cz_iter = std::find_if(
        cz_states.begin(), cz_states.end(),
        [next_obj_id](const auto& state) { return state.id == next_obj_id; });
    DCHECK(cz_iter != cz_states.end());
    next_obj_overtake_arclength_m =
        (cz_iter->start_arclength_m + cz_iter->end_arclength_m) * 0.5;
  } else {
    next_obj_overtake_arclength_m =
        FLAGS_planning_enable_object_occupancy_state
            ? DCHECK_NOTNULL(
                  gtl::FindOrDie(candidate.object_occupancy_state_map(),
                                 next_obj_id))
                  ->current_snapshot_info()
                  .object_occupancy_param()
                  .full_body_end_arclength_m
            : DCHECK_NOTNULL(
                  gtl::FindOrDie(candidate.agent_in_lane_states_map(),
                                 next_obj_id))
                  ->tracked_state.inlane_param.full_body_end_arclength_m;
  }
  const auto optional_initial_object_range =
      ObtainMovingObjectArclengthRange(candidate, initial_dominant_obj);
  DCHECK(optional_initial_object_range.has_value());
  const bool is_object_overtaking_blockage_object =
      (optional_initial_object_range->start_pos <
           next_obj_overtake_arclength_m &&
       optional_initial_object_range->end_pos > next_obj_overtake_arclength_m);

  if (is_object_overtaking_blockage_object) {
    // Initial dominant obj is overtaking a blockage object, use the
    // blockage object as the real stuck constraint.
    return std::make_optional<int64_t>(next_dominant_ix);
  }

  return std::nullopt;
}
}  // namespace

void AddStuckSignalDebugInfo(
    const std::string& debug_name, const std::string& debug_value,
    const std::vector<std::pair<std::string, std::string>>&
        sub_level_debug_list,
    pb::StuckDebug& stuck_debug) {
  pb::StuckSignalDebugNameAndValue debug_name_and_value;
  debug_name_and_value.set_debug_name(debug_name);
  debug_name_and_value.set_debug_value(debug_value);
  auto* debug_info = stuck_debug.add_stuck_signal_debug_info_list();

  // Add sub level debug information.
  for (const auto& iter : sub_level_debug_list) {
    pb::DebugNameAndValue sub_level_debug;
    sub_level_debug.set_debug_name(iter.first);
    sub_level_debug.set_debug_value(iter.second);
    auto* sub_level_debug_info =
        debug_name_and_value.add_sub_level_debug_list();
    *sub_level_debug_info = sub_level_debug;
  }
  *debug_info = debug_name_and_value;
}

std::optional<TypedObjectId> GetStuckConstraintId(
    const selection::TrajectoryMetaData& candidate,
    const speed::DiscomfortVaryingMinRange& min_range,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    double min_range_max_required_lat_gap, double ra_to_fb_shift) {
  const auto& speed_result = candidate.speed_result;
  int dominant_ix = speed_result.dominant_constraint_ix();
  if (dominant_ix < 0) {
    return std::nullopt;
  }
  DCHECK_LT(dominant_ix, speed_result.constraints().size());
  double closest_ra_dist_to_subject = 0.0;
  // We need to find the closest effective constraint that is not NO_BLOCK
  // as the stuck constraint.
  dominant_ix = speed::FindClosestYieldConstraintIfShouldConsider(
      speed_result.constraints(), speed_result.decisions(),
      [](const speed::Constraint& constraint) {
        return (constraint.type !=
                speed::ConstraintType::Constraint_Type_NO_BLOCK);
      },
      min_range, min_range_max_required_lat_gap, ra_to_fb_shift,
      &closest_ra_dist_to_subject);
  if (dominant_ix == -1) {
    return std::nullopt;
  }

  const ObjectId initial_dominant_obj_id =
      speed_result.constraints()[dominant_ix].obj_id;
  if (planner_object_map.find(initial_dominant_obj_id) !=
          planner_object_map.end() &&
      planner_object_map.at(initial_dominant_obj_id).is_pedestrian()) {
    // If the initial dominant constraint is ped, check if it is near a blockage
    // object or not, if so, then return the blockage object as the dominant
    // constraint.
    constexpr double kDistanceThresholdToBindPedToBlockageObjectInM = 5.0;
    double next_closest_ra_dist_to_subject = 0.0;
    const auto next_dominant_ix =
        speed::FindClosestYieldConstraintIfShouldConsider(
            speed_result.constraints(), speed_result.decisions(),
            [&planner_object_map](const speed::Constraint& constraint) {
              if (constraint.type ==
                  speed::ConstraintType::Constraint_Type_NO_BLOCK) {
                // Don't consider no block constraint.
                return false;
              }
              const ObjectId obj_id = constraint.obj_id;
              if (planner_object_map.find(obj_id) == planner_object_map.end()) {
                // Consider other non-object type speed constraint. Including
                // construction zone or other speed constraints like traffic
                // light.
                return true;
              }
              // Filter pedestrian type object constraint.
              return (!planner_object_map.at(obj_id).is_pedestrian());
            },
            min_range, min_range_max_required_lat_gap, ra_to_fb_shift,
            &next_closest_ra_dist_to_subject);
    if (next_dominant_ix != -1) {
      const ObjectId next_obj_id =
          speed_result.constraints()[next_dominant_ix].obj_id;
      const bool is_next_obj_blockage =
          (speed_result.constraints()[next_dominant_ix].IsConstructionZone() ||
           (planner_object_map.find(next_obj_id) != planner_object_map.end() &&
            planner_object_map.at(next_obj_id).is_blockage_object()));
      if (is_next_obj_blockage &&
          std::abs(next_closest_ra_dist_to_subject -
                   closest_ra_dist_to_subject) <
              kDistanceThresholdToBindPedToBlockageObjectInM) {
        // Update the dominant index to be the next closest blockage object
        // index that is very close.
        dominant_ix = next_dominant_ix;
        speed::CheckDominantConstraintDecision(
            speed_result.constraints(),
            speed_result.planning_time_horizon_in_second(),
            speed_result.decisions(), speed_result.tree_decisions(),
            dominant_ix);

        return std::make_optional<TypedObjectId>(TypedObjectId(
            speed_result.constraints()[dominant_ix].obj_id,
            speed_result.constraints()[dominant_ix].IsConstructionZone()
                ? pb::ObjectSourceType::kConstructionZone
                : pb::ObjectSourceType::kTrackedObject));
      }
    }
  }

  // For different special scenario, we might want to target different dominant
  // constraint index other than the original dominant constraint index.
  std::optional<int64_t> optional_special_dominant_idx =
      TargetOvertakingBlockageObjectSituation(
          candidate, min_range, planner_object_map, initial_dominant_obj_id,
          min_range_max_required_lat_gap, ra_to_fb_shift);
  if (optional_special_dominant_idx.has_value()) {
    speed::CheckDominantConstraintDecision(
        speed_result.constraints(),
        speed_result.planning_time_horizon_in_second(),
        speed_result.decisions(), speed_result.tree_decisions(),
        optional_special_dominant_idx.value());
    return std::make_optional<TypedObjectId>(TypedObjectId(
        speed_result.constraints()[optional_special_dominant_idx.value()]
            .obj_id,
        speed_result.constraints()[optional_special_dominant_idx.value()]
                .IsConstructionZone()
            ? pb::ObjectSourceType::kConstructionZone
            : pb::ObjectSourceType::kTrackedObject));
  }
  speed::CheckDominantConstraintDecision(
      speed_result.constraints(),
      speed_result.planning_time_horizon_in_second(), speed_result.decisions(),
      speed_result.tree_decisions(), dominant_ix);
  return std::make_optional<TypedObjectId>(
      TypedObjectId(speed_result.constraints()[dominant_ix].obj_id,
                    speed_result.constraints()[dominant_ix].IsConstructionZone()
                        ? pb::ObjectSourceType::kConstructionZone
                        : pb::ObjectSourceType::kTrackedObject));
}

bool IsEgoAtEndOfLaneSequence(const selection::TrajectoryMetaData& candidate,
                              const voy::Pose& pose,
                              const pb::StuckDetectorConfig& stuck_parameters) {
  const pnc_map::Lane* current_lane = candidate.current_lane();
  DCHECK(current_lane != nullptr);
  const std::vector<const pnc_map::Lane*>& lane_sequence =
      candidate.lane_sequence();
  if (lane_sequence.empty()) {
    // TODO(Zhaorui) : deal with the first frame
    return false;
  }
  if (std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [&current_lane](const pnc_map::Lane* lane_finder) {
                     return current_lane->id() == lane_finder->id();
                   }) == lane_sequence.end()) {
    // current lane not in lane sequence
    return false;
  }
  if (current_lane->id() == lane_sequence.back()->id()) {
    const double distance_to_end =
        current_lane->length() -
        current_lane->GetArclength({pose.x(), pose.y()});
    if (distance_to_end < stuck_parameters.distance_to_end_of_lane()) {
      return true;
    }
  }
  return false;
}

std::optional<math::geometry::PolygonWithCache2d> GetObjectContour(
    const SpeedWorldModel& world_model,
    const std::optional<TypedObjectId>& typed_object_id) {
  if (!typed_object_id.has_value()) {
    return std::nullopt;
  }

  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map =
      world_model.planner_object_map();
  if (planner_object_map.find(typed_object_id->id) ==
      planner_object_map.end()) {
    return std::nullopt;
  }

  const PlannerObject& object = planner_object_map.at(typed_object_id->id);
  return object.contour();
}

bool IsObjectPersistentSlow(const PlannerObject& object,
                            const pb::StuckDetectorSeed& seed,
                            double slow_speed_threshold_mps,
                            int64_t persistent_duration_ms,
                            int64_t current_timestamp) {
  const double object_velocity = object.tracked_object().velocity();
  const int64_t object_id = object.tracked_object().id();
  const std::optional<TypedObjectId> optional_seed_stuck_object_id =
      seed.has_optional_stuck_object_id()
          ? std::make_optional<TypedObjectId>(seed.optional_stuck_object_id())
          : std::nullopt;

  if (!optional_seed_stuck_object_id.has_value() ||
      optional_seed_stuck_object_id->id != object_id) {
    // If the stuck object is not consistent the same as from last cycle, don't
    // treat it as persistent slow.
    return false;
  }

  const auto& slow_start_objects = seed.slow_start_objects();
  const auto iter =
      std::find_if(slow_start_objects.begin(), slow_start_objects.end(),
                   [&optional_seed_stuck_object_id](const auto& iter) {
                     return iter.typed_object_id().object_id() ==
                            optional_seed_stuck_object_id->id;
                   });

  const bool object_very_slow = object_velocity < slow_speed_threshold_mps;
  if (iter == slow_start_objects.end() || !object_very_slow ||
      !seed.slow_stuck_flag()) {
    return false;
  }

  return ((current_timestamp - iter->slow_start_time()) >
          persistent_duration_ms) &&
         object_very_slow;
}

std::vector<TrafficJamVehicleMetaData> GenerateVehiclesOnLaneSequence(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const pb::StuckDetectorConfig& stuck_parameters) {
  std::vector<TrafficJamVehicleMetaData> vehicles_on_lane_sequence;
  // TODO(Ziyue): No need to use this map for ObjectOccupancyStates.
  const std::unordered_map<ObjectId, PlannerObject>& object_map =
      world_model.planner_object_map();
  if (FLAGS_planning_enable_object_occupancy_state) {
    for (const auto& [typed_object_id, object_reasoning_info] :
         candidate.object_reasoning_info_map()) {
      const int64_t object_id = typed_object_id.id;
      if (candidate.object_occupancy_state_map().find(object_id) ==
          candidate.object_occupancy_state_map().end()) {
        continue;
      }
      const std::unique_ptr<ObjectOccupancyState>& object_occupancy_state_ptr =
          candidate.object_occupancy_state_map().at(object_id);
      // If the object is behind the ego, ignore it.
      if (object_occupancy_state_ptr->current_snapshot_info()
              .is_fully_behind_ego()) {
        continue;
      }
      const ObjectOccupancyParam& object_occupancy_param =
          object_occupancy_state_ptr->current_snapshot_info()
              .object_occupancy_param();
      const pb::LateralBlockingState::BlockingState blockage_state =
          object_reasoning_info.blocking_state_data()
              .tracked_blocking_info.blocking_state();
      if (blockage_state == pb::LateralBlockingState::NON_BLOCKING) {
        continue;
      }
      const PlannerObject& object =
          object_occupancy_state_ptr->planner_object();
      const double object_cross_track_encroachment_m =
          object_occupancy_param.cross_track_encroachment_m;
      const double object_infringement_ratio =
          object_cross_track_encroachment_m / object.width();

      if (blockage_state != pb::LateralBlockingState::HARD_BLOCKING &&
          object_infringement_ratio < kInfringementRatioThreshold) {
        // If the object is not infringing Ego's lane sufficiently, we don't
        // consider it.
        continue;
      }

      if ((!object.is_vehicle() && !object.is_cyclist()) ||
          object.speed() > stuck_parameters.traffic_jam_speed()) {
        continue;
      }
      const double arc_length =
          object_occupancy_param.snapshot_start_arclength_m;
      vehicles_on_lane_sequence.push_back(TrafficJamVehicleMetaData{
          arc_length, &object, object_occupancy_param.left_boundary_clearance_m,
          object_occupancy_param.right_boundary_clearance_m});
    }
  } else {
    for (const auto& agent_in_lane_state :
         candidate.agent_in_lane_states_map()) {
      const int64_t agent_id = agent_in_lane_state.first;
      const AgentSnapshotInLaneParam& agent_inlane_param =
          DCHECK_NOTNULL(agent_in_lane_state.second)
              ->tracked_state.inlane_param;
      if (object_map.find(agent_id) == object_map.end()) {
        continue;
      }

      const std::unique_ptr<AgentInLaneStates>& in_lane_state =
          agent_in_lane_state.second;
      const pb::AgentSnapshotBlockingState blockage_state =
          in_lane_state->tracked_state.blocking_info.blocking_state;
      if (blockage_state == pb::AgentSnapshotBlockingState::NOT_BLOCKING) {
        continue;
      }
      const PlannerObject& object = object_map.at(agent_id);
      const double object_cross_track_encroachment_m =
          agent_inlane_param.cross_track_encroachment_m;
      const double object_infringement_ratio =
          object_cross_track_encroachment_m / object.width();

      if (blockage_state != pb::AgentSnapshotBlockingState::HARD_BLOCKING &&
          object_infringement_ratio < kInfringementRatioThreshold) {
        // If the object is not infringing Ego's lane sufficiently, we don't
        // consider it.
        continue;
      }

      if (!object.is_vehicle() ||
          object.speed() > stuck_parameters.traffic_jam_speed()) {
        continue;
      }
      const double arc_length =
          in_lane_state->tracked_state.inlane_param.start_arclength_m;
      vehicles_on_lane_sequence.push_back(TrafficJamVehicleMetaData{
          arc_length, &object, agent_inlane_param.left_inlane_clearance_m,
          agent_inlane_param.right_inlane_clearance_m});
    }
  }
  std::sort(vehicles_on_lane_sequence.begin(), vehicles_on_lane_sequence.end(),
            [](const TrafficJamVehicleMetaData& lhs,
               const TrafficJamVehicleMetaData& rhs) {
              return lhs.arclength_m < rhs.arclength_m;
            });
  return vehicles_on_lane_sequence;
}

TrafficJamStuckMetaData IsWaitingInTrafficJam(
    const SpeedWorldModel& world_model, const double distance_to_junction,
    const voy::TrafficLights& traffic_lights,
    const selection::TrajectoryMetaData& candidate,
    const pb::StuckDetectorConfig& stuck_parameters,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    bool is_stuck_object, const PlannerObject& stuck_object,
    const RobotStateSnapshot& ego_state_snapshot,
    const math::geometry::PolygonWithCache2d& object_contour,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const speed::DiscomfortVaryingMinRange& min_range,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    const pb::StuckDetectorSeed& last_iteration_seed,
    std::optional<pb::StuckDebug>& optional_stuck_debug) {
  TrafficJamStuckMetaData meta_data;
  meta_data.is_ego_waiting_in_traffic_jam = false;
  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      world_model.robot_state().car_model_with_shape().shape_measurement();
  CHECK_GT(ego_shape.length() - ego_shape.rear_bumper_to_rear_axle(), 0);
  const double ra_to_fb_shift =
      ego_shape.length() - ego_shape.rear_bumper_to_rear_axle();
  if (vehicles_on_lane_sequence.empty()) {
    std::optional<int64_t> optional_close_yielding_cyclist_id;
    if (!stuck_object.is_cyclist()) {
      constexpr double kDistanceThresholdToConsiderYieldingCyclistInM = 15.0;
      double next_closest_ra_dist_to_subject = 0.0;
      // If the stuck object is not cyclist, it can be vehicles on the neighbor
      // lane or other types of non-blockage objects. We want to find if Ego is
      // yielding for a cyclist or not.
      const int cyclist_yield_idx =
          speed::FindClosestYieldConstraintIfShouldConsider(
              candidate.speed_result.constraints(),
              candidate.speed_result.decisions(),
              [&planner_object_map](const speed::Constraint& constraint) {
                // Only consider cyclist speed constraint.
                const ObjectId obj_id = constraint.obj_id;
                if (planner_object_map.find(obj_id) ==
                    planner_object_map.end()) {
                  return false;
                }
                return (planner_object_map.at(obj_id).is_cyclist());
              },
              min_range, /*min_range_max_required_lat_gap=*/0.0, ra_to_fb_shift,
              &next_closest_ra_dist_to_subject);
      if (cyclist_yield_idx != -1 &&
          next_closest_ra_dist_to_subject <
              kDistanceThresholdToConsiderYieldingCyclistInM) {
        // If Ego is yielding for a near cyclist, we want to use that cyclist to
        // evaluate whether Ego is waiting in traffic with it or not.
        optional_close_yielding_cyclist_id =
            candidate.speed_result.constraints()[cyclist_yield_idx].obj_id;
      }
    }

    const PlannerObject& evaluate_object =
        optional_close_yielding_cyclist_id.has_value()
            ? planner_object_map.at(optional_close_yielding_cyclist_id.value())
            : stuck_object;
    const bool is_waiting_for_traffic_with_cyc = IsWaitingForTrafficWithCyclist(
        candidate, vehicles_on_lane_sequence, evaluate_object, object_contour,
        ego_state_snapshot);
    const bool is_first_vehicle_for_tl = IsFirstVehicleWaitingForTl(
        candidate.speed_result,
        candidate.trajectory_info->traffic_rules().traffic_lights);

    if (is_waiting_for_traffic_with_cyc || is_first_vehicle_for_tl) {
      meta_data.is_ego_waiting_in_traffic_jam = true;
    }

    if (optional_stuck_debug.has_value()) {
      const std::vector<std::pair<std::string, std::string>>
          sub_level_debug_list = {
              std::make_pair("is_waiting_for_traffic_with_cyc: ",
                             absl::StrCat(is_waiting_for_traffic_with_cyc)),
              std::make_pair("is_first_vehicle_for_tl: ",
                             absl::StrCat(is_first_vehicle_for_tl))};

      AddStuckSignalDebugInfo(
          /*debug_name=*/"is_ego_waiting_in_traffic_jam: ",
          /*debug_value=*/absl::StrCat(meta_data.is_ego_waiting_in_traffic_jam),
          sub_level_debug_list, optional_stuck_debug.value());
    }

    return meta_data;
  }
  double yield_fence_cost = 0.0;
  const auto& yield_fences = candidate.speed_result.yielding_fence_list();
  for (const auto& fence : yield_fences.fence_list()) {
    if (fence.type() == planner::speed::pb::FenceType::kTrafficLight) {
      yield_fence_cost = stuck_parameters.yield_fence_cost();
    }
  }
  if (candidate.trajectory_info != nullptr &&
      FirstTrafficLightJustTurnedGreen(
          candidate.trajectory_info->traffic_rules().traffic_lights)) {
    // If there is no yield fence cost, check whether the traffic light
    // just turned green. If so, set the yield fence cost.
    yield_fence_cost = stuck_parameters.yield_fence_cost();
  }

  // Check whether there is any occlusion in front.
  const double max_arclength_m = candidate.front_bumper_arclength() +
                                 kDistanceThresholdForDensityCalculationInM;
  TrafficJamOcclusionMetaData occlusion_data =
      FLAGS_planning_enable_object_occupancy_state
          ? EvaluateTrafficJamOcclusion(
                vehicles_on_lane_sequence,
                candidate.object_occupancy_state_map(),
                DCHECK_NOTNULL(candidate.path_option.geometric_constraint
                                   .lane_sequence_info)
                    ->robot_in_lane_param(),
                candidate.current_lane(), max_arclength_m)
          : EvaluateTrafficJamOcclusion(
                vehicles_on_lane_sequence, candidate.agent_in_lane_states_map(),
                DCHECK_NOTNULL(candidate.path_option.geometric_constraint
                                   .lane_sequence_info)
                    ->robot_in_lane_param(),
                candidate.current_lane(), max_arclength_m);

  DCHECK(distance_to_junction >= 0.0);
  const std::vector<double> distance_to_junction_vec{
      0.0, kIsWaitingInTrafficJamCloseDistanceInM,
      kIsWaitingInTrafficJamFartherDistanceInM,
      std::numeric_limits<double>::max()};
  const std::vector<double> close_to_junction_cost_vec{
      stuck_parameters.close_to_junction_cost(),
      stuck_parameters.close_to_junction_cost(), 0.0, 0.0};
  const math::PiecewiseLinearFunction close_to_junction_cost_at_distance(
      distance_to_junction_vec, close_to_junction_cost_vec);

  const double ego_arclength_m =
      candidate.lane_sequence_geometry()
          .nominal_path
          .GetProximity({ego_state_snapshot.x(), ego_state_snapshot.y()},
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double close_to_junction_cost = CalcDistanceToJunctionCost(
      vehicles_on_lane_sequence, occlusion_data, stuck_parameters,
      candidate.lane_sequence_geometry().nominal_path, candidate.current_lane(),
      stuck_object,
      *DCHECK_NOTNULL(gtl::FindOrDieNoPrint(
          candidate.agent_in_lane_states_map(), stuck_object.id())),
      close_to_junction_cost_at_distance, distance_to_junction,
      ego_arclength_m);

  double light_in_front_cost = 0.0;
  if (traffic_lights.has_traffic_light() == voy::TrafficLights::TL_ON_MAP) {
    light_in_front_cost = stuck_parameters.light_in_front_cost();
  }

  // calculate density cost
  const auto last_vehicle_iter = std::lower_bound(
      vehicles_on_lane_sequence.begin(), vehicles_on_lane_sequence.end(),
      max_arclength_m,
      [](const TrafficJamVehicleMetaData& it, const double val) {
        return (it.arclength_m) <= val;
      });

  const auto object_density = static_cast<double>(
      std::distance(vehicles_on_lane_sequence.begin(), last_vehicle_iter));
  double density_cost = math::GetLinearInterpolatedY(
      stuck_parameters.max_density(), stuck_parameters.min_density(),
      stuck_parameters.max_density_cost(), stuck_parameters.min_density_cost(),
      object_density);
  if (occlusion_data.optional_occlusion_vehicle_id.has_value() &&
      !(occlusion_data.occlusion_on_first_vehicle)) {
    // The occlusion isn't triggered by the very first vehicle in front of Ego.
    // Meaning there are another vehicles queuing behind the big vehicle, so it
    // is more possible that we are still waiting in traffic jam. Just use the
    // max density cost.
    density_cost = stuck_parameters.max_density_cost();
  }

  const double interim_total_cost =
      (yield_fence_cost + density_cost + close_to_junction_cost +
       light_in_front_cost);
  NeighborLaneDensity neighbor_densities = EvaluateNeighborLanesTrafficDensity(
      world_model, candidate.lane_sequence(), candidate.current_lane(),
      lane_sequence_candidates, ego_state_snapshot.rear_axle_position(),
      stuck_parameters, agent_map_element_occupancy_seeds);
  if (FLAGS_planning_enable_using_neighbor_lane_to_help_evaluate_density_cost) {
    UpdateDensityCostBasedOnNeighborDensity(
        neighbor_densities, stuck_parameters, interim_total_cost, density_cost);
  }

  const double total_cost = (yield_fence_cost + density_cost +
                             close_to_junction_cost + light_in_front_cost);

  meta_data.is_ego_stuck_inside_junction = IsEgoStuckInsideJunction(
      candidate, vehicles_on_lane_sequence, is_stuck_object, stuck_object,
      planner_object_map, last_iteration_seed, min_range, ra_to_fb_shift,
      ego_state_snapshot);
  meta_data.is_ego_waiting_in_traffic_jam =
      (!(meta_data.is_ego_stuck_inside_junction) &&
       total_cost > stuck_parameters.traffic_jam_trigger_cost());
  if (meta_data.is_ego_stuck_inside_junction) {
    rt_event::PostRtEvent<rt_event::planner::TargetEgoStuckInsideJunction>();
  }

  if (optional_stuck_debug.has_value()) {
    const std::vector<std::pair<std::string, std::string>>
        sub_level_debug_list = {
            std::make_pair(
                "has_perception_occlusion: ",
                absl::StrCat(
                    occlusion_data.optional_occlusion_vehicle_id.has_value())),
            std::make_pair(
                "occlusion_on_first_vehicle: ",
                absl::StrCat(occlusion_data.occlusion_on_first_vehicle)),
            std::make_pair("yield_fence_cost: ",
                           absl::StrCat(yield_fence_cost)),
            std::make_pair("close_to_junction_cost: ",
                           absl::StrCat(close_to_junction_cost)),
            std::make_pair("light_in_front_cost: ",
                           absl::StrCat(light_in_front_cost)),
            std::make_pair("density_cost: ", absl::StrCat(density_cost)),
            std::make_pair(
                "is_ego_stuck_inside_junction: ",
                absl::StrCat(meta_data.is_ego_stuck_inside_junction))};

    AddStuckSignalDebugInfo(
        /*debug_name=*/"is_ego_waiting_in_traffic_jam: ",
        /*debug_value=*/absl::StrCat(meta_data.is_ego_waiting_in_traffic_jam),
        sub_level_debug_list, optional_stuck_debug.value());
  }
  return meta_data;
}

bool IsEgoStuckInsideJunction(
    const selection::TrajectoryMetaData& candidate,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    bool is_initial_stuck_object_persistent_slow,
    const PlannerObject& initial_stuck_object,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::StuckDetectorSeed& last_iteration_seed,
    const speed::DiscomfortVaryingMinRange& min_range, double ra_to_fb_shift,
    const RobotStateSnapshot& ego_state_snapshot) {
  const pnc_map::Lane* current_lane = candidate.current_lane();
  DCHECK(current_lane != nullptr);
  if (current_lane->type() != hdmap::Lane::VIRTUAL ||
      !current_lane->IsInJunction()) {
    // TODO(reasoning): Handle turn situations as well.
    // If Ego is not currently driving straight inside junction, return false.
    return false;
  }

  const auto& traffic_lights =
      DCHECK_NOTNULL(candidate.trajectory_info)->traffic_rules().traffic_lights;
  if (IsTrafficLightRed(traffic_lights, *current_lane)) {
    // If traffic light is red, return false.
    return false;
  }

  const auto& speed_result = candidate.speed_result;
  const auto& history_dominant_objects = last_iteration_seed.dominant_objects();
  const int dominant_objects_size_to_consider_passing =
      IsTrafficLightTurningRedSoon(traffic_lights, *current_lane) ? 1 : 4;
  int next_dominant_ix = -1;
  if (!is_initial_stuck_object_persistent_slow &&
      history_dominant_objects.size() >=
          dominant_objects_size_to_consider_passing) {
    // Since the seed will only store historical dominant objects within 15s, if
    // the size of it is larger or equal than threshold, we think the neighbor
    // traffic are passing us inside junction, hence we might be stuck.
    double next_closest_ra_dist_to_subject;
    next_dominant_ix = speed::FindClosestYieldConstraintIfShouldConsider(
        speed_result.constraints(), speed_result.decisions(),
        [&planner_object_map](const speed::Constraint& constraint) {
          if (constraint.type ==
              speed::ConstraintType::Constraint_Type_NO_BLOCK) {
            // Don't consider no block constraint.
            return false;
          }
          const ObjectId obj_id = constraint.obj_id;
          if (planner_object_map.find(obj_id) == planner_object_map.end()) {
            // Consider other non-object type speed constraint. Including
            // construction zone or other speed constraints like traffic
            // light.
            return false;
          }
          return (planner_object_map.at(obj_id).is_vehicle() &&
                  planner_object_map.at(obj_id).is_stationary());
        },
        min_range, /*min_range_max_required_lat_gap=*/0.0, ra_to_fb_shift,
        &next_closest_ra_dist_to_subject);
  }

  if (next_dominant_ix == -1 && !is_initial_stuck_object_persistent_slow) {
    // If the original stuck object is not persistently slow, don't consider Ego
    // being stuck.
    return false;
  }

  // If we didn't find the next stuck object in junction, we will keep using the
  // original stuck object for analysis, otherwise use the next stuck object as
  // the real stuck object.
  const auto& stuck_object =
      (next_dominant_ix == -1)
          ? initial_stuck_object
          : planner_object_map.at(
                speed_result.constraints()[next_dominant_ix].obj_id);
  std::optional<double> optional_next_dominant_obj_arclength;
  if (next_dominant_ix != -1) {
    optional_next_dominant_obj_arclength =
        candidate.lane_sequence_geometry()
            .nominal_path
            .GetProximity(stuck_object.center_2d(),
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
  }

  if (stuck_object.is_vehicle() &&
      !IsObjectInDifferentRouteWithEgo(stuck_object, ego_state_snapshot,
                                       current_lane)) {
    // If stuck object is vehicle and in same route with Ego, don't consider Ego
    // being stuck.
    return false;
  }

  // If stuck object is not vehicle, like a cyclist. The heading of it can be
  // unstable since cyclist's heading angle is much more changable compared to
  // vehicle. In this situation we want to check the closest extremely slow
  // moving vehicles in front of Ego on lane seqeunce other than the stuck
  // object, if it is inside junction, Ego might be queing, for safety concern,
  // we don't think Ego is stuck in junction. If it is not inside junction, it
  // means lane sequence is clear in junction. Note: vehicles_on_lane_sequence
  // is sorted based on vehicles' arclength.
  bool is_ego_lane_sequence_clear_inside_junction = true;
  DCHECK(std::is_sorted(vehicles_on_lane_sequence.begin(),
                        vehicles_on_lane_sequence.end(),
                        [](const TrafficJamVehicleMetaData& v1,
                           const TrafficJamVehicleMetaData& v2) {
                          return v1.arclength_m < v2.arclength_m;
                        }));
  for (const auto& vehicle : vehicles_on_lane_sequence) {
    if (vehicle.object->id() == stuck_object.id() ||
        vehicle.arclength_m < candidate.front_bumper_arclength()) {
      // Don't consider the stuck object or object that is not in front of Ego.
      continue;
    }
    if (optional_next_dominant_obj_arclength.has_value() &&
        vehicle.arclength_m < optional_next_dominant_obj_arclength.value()) {
      // Don't consider the object that is before the targeted next stuck object
      // ahead.
      continue;
    }
    if (IsObjectInDifferentRouteWithEgo(*(vehicle.object), ego_state_snapshot,
                                        current_lane)) {
      // Skip the vehicle that is in different route when checking
      // clearance within intersection.
      continue;
    }

    is_ego_lane_sequence_clear_inside_junction =
        (vehicle.object->l2_distance_to_ego_m() >=
         kClearDistanceThresholdInJunctionInM);
    break;
  }
  return is_ego_lane_sequence_clear_inside_junction;
}

TrafficJamOcclusionMetaData EvaluateTrafficJamOcclusion(
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const AgentInLaneStatesMap& agent_in_lane_states_map,
    const EgoInLaneParams& ego_inlane_param, const pnc_map::Lane* current_lane,
    double end_arclength_m) {
  TrafficJamOcclusionMetaData meta_data;

  for (size_t i = 0; i < vehicles_on_lane_sequence.size(); ++i) {
    const TrafficJamVehicleMetaData& meta = vehicles_on_lane_sequence[i];
    const double arc_length = meta.arclength_m;
    const PlannerObject* planner_object_ptr = meta.object;
    if (arc_length > end_arclength_m) {
      // Vehicle is too far, don't consider.
      break;
    }

    DCHECK(planner_object_ptr != nullptr);
    if (i == 0 && vehicles_on_lane_sequence.size() > 1 &&
        vehicles_on_lane_sequence[1].arclength_m - arc_length <
            kDistanceThresholdToConsiderVehiclesAdjacentInM &&
        vehicles_on_lane_sequence[1].object->height() >
            planner_object_ptr->height()) {
      // If there are more than 1 vehicles in front and the 2nd vehicle has
      // larger height than 1st vehicle and they are adjacent, then 1st vehicle
      // cannot be the one that causes whole occlusion.
      continue;
    }

    const auto& agent_inlane_state = gtl::FindOrDieNoPrint(
        agent_in_lane_states_map, planner_object_ptr->id());
    const AgentSnapshotInLaneParam& agent_inlane_param =
        DCHECK_NOTNULL(agent_inlane_state)->tracked_state.inlane_param;

    const double ego_relative_dist_to_lane_center =
        ego_inlane_param.relative_lateral_distance_to_line_center;
    const double lane_width =
        current_lane->GetWidthAtPoint(planner_object_ptr->center_2d());
    // Evaluate whether the agent is occluding us from left side or right side.
    // Only if it both occludes us from left and right then we consider it as
    // laterally occluded.
    const bool left_occluded =
        (agent_inlane_param.left_inlane_clearance_m <
         (lane_width / 2.0 - ego_relative_dist_to_lane_center));
    const bool right_occluded =
        (agent_inlane_param.right_inlane_clearance_m <
         (lane_width / 2.0 + ego_relative_dist_to_lane_center));
    const bool laterally_occluded = (left_occluded && right_occluded);
    const bool vertically_occluded =
        (planner_object_ptr->height() > kHeightThresholdToConsiderBlockingInM);

    if (vertically_occluded && laterally_occluded) {
      meta_data.occlusion_on_first_vehicle = (i == 0);
      meta_data.optional_occlusion_vehicle_id = planner_object_ptr->id();
      break;
    }
  }

  return meta_data;
}

// TODO(Ziyue): Remove overload function after OOS is enabled
TrafficJamOcclusionMetaData EvaluateTrafficJamOcclusion(
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const ObjectOccupancyStateMap& object_occupancy_state_map,
    const EgoInLaneParams& ego_inlane_param, const pnc_map::Lane* current_lane,
    double end_arclength_m) {
  TrafficJamOcclusionMetaData meta_data;
  for (size_t i = 0; i < vehicles_on_lane_sequence.size(); ++i) {
    const auto& meta = vehicles_on_lane_sequence[i];
    const double arc_length = meta.arclength_m;
    const PlannerObject* planner_object_ptr = meta.object;
    if (arc_length > end_arclength_m) {
      // Vehicle is too far, don't consider.
      break;
    }

    DCHECK(planner_object_ptr != nullptr);
    if (i == 0 && vehicles_on_lane_sequence.size() > 1 &&
        vehicles_on_lane_sequence[1].arclength_m - arc_length <
            kDistanceThresholdToConsiderVehiclesAdjacentInM &&
        vehicles_on_lane_sequence[1].object->height() >
            planner_object_ptr->height()) {
      // If there are more than 1 vehicles in front and the 2nd vehicle has
      // larger height than 1st vehicle and they are adjacent, then 1st vehicle
      // cannot be the one that causes whole occlusion.
      continue;
    }

    const auto& object_occupancy_state = gtl::FindOrDieNoPrint(
        object_occupancy_state_map, planner_object_ptr->id());
    const ObjectOccupancyParam& object_occupancy_param =
        DCHECK_NOTNULL(object_occupancy_state)
            ->current_snapshot_info()
            .object_occupancy_param();

    const double ego_relative_dist_to_lane_center =
        ego_inlane_param.relative_lateral_distance_to_line_center;
    const double lane_width =
        current_lane->GetWidthAtPoint(planner_object_ptr->center_2d());
    const bool laterally_blocks_ego =
        (object_occupancy_param.left_boundary_clearance_m <
         (lane_width / 2.0 - ego_relative_dist_to_lane_center));
    const bool vertically_blocks_ego =
        (planner_object_ptr->height() > kHeightThresholdToConsiderBlockingInM);

    if (vertically_blocks_ego && laterally_blocks_ego) {
      meta_data.occlusion_on_first_vehicle = (i == 0);
      meta_data.optional_occlusion_vehicle_id = planner_object_ptr->id();
      break;
    }
  }

  return meta_data;
}

bool HasDrivableGapAheadStuckObject(
    const EgoInLaneParams& ego_inlane_param,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    const TrafficJamOcclusionMetaData& occlusion_data,
    double drivable_gap_evaluation_distance_m,
    const TrafficJamVehicleMetaData& stuck_object_pair) {
  if (occlusion_data.optional_occlusion_vehicle_id.has_value()) {
    // TODO(jinghuang1): Add logic to handle if there are occlusion vehicle
    // ahead. Basically if there are occlusion vehicles far ahead, we could set
    // the consider region to that occluded vehicle instead of to junction.
    // Currently if there is occlusion, return false.
    return false;
  }

  // If there is no occlusion, calculate whether there is drivable gap ahead.
  double last_stationary_vehicle_arclength_m = stuck_object_pair.arclength_m;
  for (const auto& object_meta : vehicles_on_lane_sequence) {
    if (object_meta.arclength_m <= stuck_object_pair.arclength_m) {
      // Skip vehicles behind stuck object.
      continue;
    }
    if (object_meta.arclength_m - ego_inlane_param.arclength_m >
        drivable_gap_evaluation_distance_m) {
      // Break if vehicle is out of distance threshold.
      break;
    }
    const double distance_to_last_stationary_object =
        object_meta.arclength_m - last_stationary_vehicle_arclength_m;
    if (distance_to_last_stationary_object >
        kDistanceWindowToThinkEgoCanFitInM) {
      // If two persistent stationary car has a distance gap that is larger
      // than window, they are temp parking cars, return true.
      return true;
    }

    const std::optional<int64_t> optional_object_stationary_time_ms =
        object_meta.object->optional_observed_stationary_duration_ms();
    if (!optional_object_stationary_time_ms.has_value() ||
        optional_object_stationary_time_ms.value() <
            kStationaryDurationForTempParkingCarInMs) {
      // We have reached a vehicle that hasn't been stationary long enough,
      // meanning it cannot be temp parking vehicle. Further distance gap
      // won't be between a moving vehicle and a temp parking vehicle, so we
      // shouldn't keep calculating.
      return false;
    }
    last_stationary_vehicle_arclength_m = object_meta.arclength_m;
  }

  // Need to calculate the gap between following 2 things:
  // A: The last persistent stationary vehicle we considered within
  // drivable_gap_evaluation_distance_m.
  // B: The drivable_gap_evaluation_distance_m.
  //
  //--------------------------------------------------------------------------------------------------------------------------
  //  _______      _______
  // |       |    |       | <----      Empty Gap calculated       ----> |
  // |_______|    |_______|                                             |
  //                  A                                                 B:
  //                  drivable_gap_evaluation_distance_m
  //
  //
  //--------------------------------------------------------------------------------------------------------------------------
  //
  const double empty_gap_in_front_m =
      drivable_gap_evaluation_distance_m -
      (last_stationary_vehicle_arclength_m - ego_inlane_param.arclength_m);
  return empty_gap_in_front_m > kDistanceWindowToThinkEgoCanFitInM;
}

bool IsStuckObjectTempParking(
    const PlannerObject& stuck_object, const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const std::vector<TrafficJamVehicleMetaData>& vehicles_on_lane_sequence,
    double ego_distance_to_junction) {
  [[maybe_unused]] const int64_t current_timestamp =
      world_model.snapshot_timestamp();
  const std::optional<int64_t> optional_stuck_object_stationary_time_ms =
      stuck_object.optional_observed_stationary_duration_ms();
  if (!stuck_object.is_vehicle() || !stuck_object.is_stationary() ||
      !optional_stuck_object_stationary_time_ms.has_value() ||
      optional_stuck_object_stationary_time_ms.value() <
          kStationaryDurationForTempParkingCarInMs) {
    // The object is not a vehicle or it is not persistent stationary for long
    // enough, don't treat it as temp parking car.
    return false;
  }

  const pnc_map::Lane* current_lane = candidate.current_lane();
  const pnc_map::Lane* right_adjacent_lane =
      current_lane->adjacent_right_lane();
  const pnc_map::Lane* right_most_lane =
      current_lane->section()->GetRightMostDrivableLane();
  const bool is_right_most_lane = (right_adjacent_lane == nullptr &&
                                   right_most_lane->id() == current_lane->id());
  if (!is_right_most_lane) {
    // Ego is not in right most lane, there won't be temp parking vehicles.
    // TODO(jinghuang1): Consider left most lane in the future.
    return false;
  }

  const auto stuck_object_iter = std::find_if(
      vehicles_on_lane_sequence.begin(), vehicles_on_lane_sequence.end(),
      [&stuck_object](const auto& iter) {
        return iter.object->id() == stuck_object.id();
      });
  if (stuck_object_iter == vehicles_on_lane_sequence.end()) {
    // Stuck object is not stored, return false.
    return false;
  }

  const auto& agent_in_lane_states_map = candidate.agent_in_lane_states_map();
  const int64_t stuck_agent_id = stuck_object.id();
  const auto& stuck_agent_inlane_state =
      gtl::FindOrDieNoPrint(agent_in_lane_states_map, stuck_agent_id);
  const AgentSnapshotInLaneParam& stuck_agent_inlane_param =
      DCHECK_NOTNULL(stuck_agent_inlane_state)->tracked_state.inlane_param;

  if (IsAgentPulledOverToRight(stuck_agent_inlane_param)) {
    // When the stuck object is pulled over to the curb, check the next
    // (kNumOfConsecutiveVehiclesToCheckPullover - 1) vehicles in front of it to
    // see if they are also pulled over as well. The reason is, the single stuck
    // vehicle in front could try to swerve to the right side to try to bypass
    // front vehicle, but if a sequence of vehicles are all pulled over to the
    // curb, then high likely they are temp parking cars.
    const int vehicles_in_front_of_stuck_vehicle =
        std::distance(stuck_object_iter, vehicles_on_lane_sequence.end()) - 1;
    if (vehicles_in_front_of_stuck_vehicle >=
        kNumOfConsecutiveVehiclesToCheckPullover - 1) {
      // Check the pullover property when there are more than the required
      // number of consecutive vehicles to check in front.
      const bool has_front_vehicle_not_pulled_over = std::any_of(
          stuck_object_iter + 1,
          stuck_object_iter + kNumOfConsecutiveVehiclesToCheckPullover,
          [&agent_in_lane_states_map](const auto& iter) {
            const int64_t front_agent_id = DCHECK_NOTNULL(iter.object)->id();
            const auto& front_agent_inlane_state =
                gtl::FindOrDieNoPrint(agent_in_lane_states_map, front_agent_id);
            const AgentSnapshotInLaneParam& front_agent_inlane_param =
                DCHECK_NOTNULL(front_agent_inlane_state)
                    ->tracked_state.inlane_param;
            return !IsAgentPulledOverToRight(front_agent_inlane_param);
          });
      // If all the vehicles are pulled over, then they are temp parking
      // vehicles.
      if (!has_front_vehicle_not_pulled_over) {
        return true;
      }
    }
  }

  // Check whether there is any occlusion in front.
  const auto& ego_inlane_param =
      DCHECK_NOTNULL(
          candidate.path_option.geometric_constraint.lane_sequence_info)
          ->robot_in_lane_param();
  const double max_arclength_m =
      ego_inlane_param.arclength_m +
      std::min(ego_distance_to_junction,
               kDistanceThresholdToDistinguishTempParkingCarInM);
  TrafficJamOcclusionMetaData occlusion_data =
      FLAGS_planning_enable_object_occupancy_state
          ? EvaluateTrafficJamOcclusion(
                vehicles_on_lane_sequence,
                candidate.object_occupancy_state_map(),
                /*ego_inlane_param=*/
                DCHECK_NOTNULL(candidate.path_option.geometric_constraint
                                   .lane_sequence_info)
                    ->robot_in_lane_param(),
                current_lane, max_arclength_m)
          : EvaluateTrafficJamOcclusion(
                vehicles_on_lane_sequence, agent_in_lane_states_map,
                /*ego_inlane_param=*/
                DCHECK_NOTNULL(candidate.path_option.geometric_constraint
                                   .lane_sequence_info)
                    ->robot_in_lane_param(),
                current_lane, max_arclength_m);

  const std::vector<speed::traffic_rules::TrafficLightInfo>&
      traffic_light_info =
          candidate.trajectory_info->traffic_rules().traffic_lights;
  double drivable_gap_evaluation_distance_m =
      std::min(ego_distance_to_junction,
               kDistanceThresholdToDistinguishTempParkingCarInM);
  if (!traffic_light_info.empty()) {
    // If there is traffic light in front, use the min between closest traffic
    // light distance and junction distance as the drivable gap eval distance.
    drivable_gap_evaluation_distance_m =
        std::min(drivable_gap_evaluation_distance_m,
                 traffic_light_info.front().start_ra_arclength());
  }

  // If there are drivable gap ahead, then the bunch of persistent stationary
  // vehicles are temp parking vehicles.
  return HasDrivableGapAheadStuckObject(
      ego_inlane_param, vehicles_on_lane_sequence, occlusion_data,
      drivable_gap_evaluation_distance_m, *stuck_object_iter);
}

double CalculateEgoDistanceToJunction(
    const RobotStateSnapshot& ego_state_snapshot,
    const selection::TrajectoryMetaData& candidate) {
  const math::geometry::PolylineCurve2d& nominal_path =
      candidate.lane_sequence_geometry().nominal_path;
  const std::vector<const pnc_map::Lane*>& lane_sequence =
      candidate.lane_sequence();
  const pnc_map::Lane* uturn_lane_ptr =
      ObtainUTurnLane(lane_sequence, candidate.current_lane());

  if (uturn_lane_ptr) {
    // Calculate the object distance to the U turn lane.
    const math::geometry::Point2d uturn_lane_start_pt =
        uturn_lane_ptr->center_line().GetStartPoint();
    const double uturn_lane_start_arclength_m =
        nominal_path
            .GetProximity(uturn_lane_start_pt,
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    const double ego_front_bumper_arclength_m =
        candidate.ego_in_lane_params().front_bumper_arclength;
    if ((ego_front_bumper_arclength_m - uturn_lane_start_arclength_m) <
        uturn_lane_ptr->length() * kUturnLaneRatioToThinkEgoIsExiting) {
      return std::max(
          0.0, uturn_lane_start_arclength_m - ego_front_bumper_arclength_m);
    }
  }

  double ego_dist_to_junction = std::numeric_limits<double>::max();
  const pnc_map::Lane* current_lane = candidate.current_lane();
  const pnc_map::Road& road = *DCHECK_NOTNULL(current_lane->section()->road());
  if (road.IsInJunction()) {
    ego_dist_to_junction = 0.0;
  } else {
    ego_dist_to_junction =
        road.road_start_to_nearest_junction_dist_m() -
        road.reference_line()
            .GetProximity({ego_state_snapshot.x(), ego_state_snapshot.y()},
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
  }
  return ego_dist_to_junction;
}

bool FirstTrafficLightJustTurnedGreen(
    const std::vector<speed::traffic_rules::TrafficLightInfo>&
        traffic_light_info) {
  if (traffic_light_info.empty()) {
    // If there are no TL in front, directly return false.
    return false;
  }
  const auto& first_tl = traffic_light_info.front();
  if (first_tl.start_ra_arclength() >= kDistanceThresholdToIgnoreTLColorInM) {
    // If the first TL is far away from ego, directly return false.
    return false;
  }
  if (first_tl.color() != voy::TrafficLight::GREEN) {
    // Only consider the situation where traffic light is green.
    return false;
  }
  return first_tl.GetColorDuration() <
         kThresholdConsiderTrafficLightAsJustTurnGreenInMsec;
}

// We need to be conservative at the beginning for this feature.
// We only want to handle when Ego is having FP stuck signal previously where
// density cost is low due to occlusion in current lane. Now we want to make use
// of neighbor traffic density to help evaluate density cost.
// Also, left side density is more valuable since right side could be temp
// parking cars.
std::optional<double> EvaluateTrafficDensity(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const math::geometry::Point2d& ego_ra_position,
    const pb::StuckDetectorConfig& stuck_parameters,
    const pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seeds) {
  if (lane_sequence.empty()) {
    return std::nullopt;
  }

  const math::geometry::PolylineCurve2d& center_line =
      lane_selection::GetLaneSequenceCurve(
          lane_sequence, lane_selection::LaneCurveType::kCenterLine);

  const double ego_arc_length =
      center_line
          .GetProximity(ego_ra_position, math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  // Find the point for the route preview query.
  const lane_selection::PreviewLanePoint preview_lane_point =
      GetAimLaneAndOffsetForPreviewOnrouteResult(
          lane_sequence, ego_arc_length,
          /*preview_buffer_m=*/kRoutePreviewLookForwardThresholdInM);

  if (preview_lane_point.lane == nullptr ||
      preview_lane_point.lane->turn() != DCHECK_NOTNULL(current_lane)->turn()) {
    // If we cannot get a preview lane point, return nullopt.
    return std::nullopt;
  }

  const std::optional<lane_selection::PreviewRouteResult>&
      preview_route_result = lane_sequence_candidates.GetPreviewRouteResult(
          *world_model.pnc_map_service(), world_model.regional_map(),
          *world_model.global_route_solution(),
          preview_lane_point, /*compared_sequence_type=*/
          lane_selection::PreviewRequestOptimalSequenceType::kPreviewRouteOnly,
          lane_selection::PreviewRouteSearchStrategy::kDefaultByMinCost,
          /*debug=*/nullptr);

  if (!preview_route_result.has_value() ||
      !preview_route_result->on_route_preview_route_result.has_value()) {
    // If we cannot get a preview route or on route result, it means we cannot
    // go to global destination given preview lane point, there is no need to
    // keep evaluate the traffic density of the given lane sequence.
    return std::nullopt;
  }

  int total_considered_objects = 0;
  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map =
      world_model.planner_object_map();

  const auto& agent_occupancy_map =
      agent_map_element_occupancy_seeds.agent_element_occupancy_map();
  for (const auto& it : planner_object_map) {
    const auto& planner_object = it.second;
    bool should_consider = false;
    if (!planner_object.is_vehicle() ||
        planner_object.speed() > stuck_parameters.traffic_jam_speed()) {
      continue;
    }

    if (math::geometry::Distance(planner_object.center_2d(), ego_ra_position) >
        kDistThresholdToConsiderObjectInM) {
      continue;
    }

    for (const auto& associated_lane : planner_object.associated_lanes()) {
      if (std::find_if(lane_sequence.begin(), lane_sequence.end(),
                       [&associated_lane](const auto& lane) {
                         return lane->id() == associated_lane->id();
                       }) != lane_sequence.end()) {
        should_consider = true;
        break;
      }
    }

    if (!should_consider) {
      // Agent's associated lanes only contains perception range lanes, hence if
      // lanes are behind Ego, it might not be included in agent's associated
      // lanes. Hence we also want to check agent's occupancy lane as well.
      const auto agent_iter = agent_occupancy_map.find(planner_object.id());
      if (agent_iter != agent_occupancy_map.end() &&
          agent_iter->second.has_latest_occupied_isolated_lane()) {
        const auto& latest_occupied_lane =
            agent_iter->second.latest_occupied_isolated_lane();
        if (std::find_if(lane_sequence.begin(), lane_sequence.end(),
                         [&latest_occupied_lane](const auto& lane) {
                           return lane->id() ==
                                  latest_occupied_lane.element_id();
                         }) != lane_sequence.end()) {
          // Agent's occupancy lane is within the lane sequence, should
          // consider it.
          should_consider = true;
        }
      }
    }
    const int object_increment =
        (planner_object.length() > kLengthThresholdForLongObjectInM) ? 2 : 1;
    total_considered_objects += should_consider ? object_increment : 0;
  }

  return std::make_optional<double>(
      static_cast<double>(total_considered_objects));
}

NeighborLaneDensity EvaluateNeighborLanesTrafficDensity(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const math::geometry::Point2d& ego_ra_position,
    const pb::StuckDetectorConfig& stuck_parameters,
    const pb::AgentMapElementOccupancySeeds&
        agent_map_element_occupancy_seeds) {
  NeighborLaneDensity densities;

  // Get Neighbor Lane Sequences.
  const std::vector<const pnc_map::Lane*>& left_neighbor_lane_sequence =
      GetNeighborLaneSequence(
          lane_sequence,
          lane_sequence.empty() ? nullptr : lane_sequence.front(),
          /*get_left_neighbor_lane_sequence=*/true);
  const std::vector<const pnc_map::Lane*>& right_neighbor_lane_sequence =
      GetNeighborLaneSequence(
          lane_sequence,
          lane_sequence.empty() ? nullptr : lane_sequence.front(),
          /*get_left_neighbor_lane_sequence=*/false);

  densities.optional_left_lane_density = EvaluateTrafficDensity(
      world_model, left_neighbor_lane_sequence, current_lane,
      lane_sequence_candidates, ego_ra_position, stuck_parameters,
      agent_map_element_occupancy_seeds);

  densities.optional_right_lane_density = EvaluateTrafficDensity(
      world_model, right_neighbor_lane_sequence, current_lane,
      lane_sequence_candidates, ego_ra_position, stuck_parameters,
      agent_map_element_occupancy_seeds);

  return densities;
}

const pnc_map::Lane* ObtainUTurnLane(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* current_lane) {
  const auto current_lane_iter =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [&current_lane](const auto& lane) {
                     return lane->id() == current_lane->id();
                   });
  DCHECK(current_lane_iter != lane_sequence.end());
  const auto uturn_lane_iter = std::find_if(
      current_lane_iter, lane_sequence.cend(), [](const pnc_map::Lane* lane) {
        return lane->turn() == hdmap::Lane::U_TURN &&
               lane->type() == hdmap::Lane_LaneType_VIRTUAL;
      });
  if (uturn_lane_iter == lane_sequence.end()) {
    return nullptr;
  }
  return *uturn_lane_iter;
}

std::optional<TypedObjectId> IdentifyNonDominantStuckObject(
    const speed::SpeedResult& speed_result,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const pb::StuckDetectorSeed& seed, const double stuck_speed_mps) {
  const auto& dominant_objects = seed.dominant_objects();
  if (dominant_objects.size() < kMinNumOfDominantObjects) {
    // If the historical dominant objects is not changing rapidly, we think the
    // enviroment is relatively stable and there are not many objects passing
    // us. Don't calculate the non dominant stuck object.
    return std::nullopt;
  }

  for (const auto& obj : dominant_objects) {
    if (obj.typed_object_id().object_type() ==
            pb::ObjectSourceType::kTrackedObject &&
        planner_object_map.find(obj.typed_object_id().object_id()) !=
            planner_object_map.end()) {
      const PlannerObject& historical_planner_object =
          planner_object_map.at(obj.typed_object_id().object_id());
      if (historical_planner_object.speed() < stuck_speed_mps) {
        // Only try to target the non-dominant blockage object if all the
        // historical dominant objects are not stuck.
        return std::nullopt;
      }
    }
  }

  for (size_t i = 0; i < speed_result.constraints().size(); ++i) {
    const speed::Constraint& constraint = speed_result.constraints()[i];
    ObjectId object_id = constraint.obj_id;
    if (constraint.states.empty()) {
      continue;
    }
    if (constraint.IsConstructionZone() &&
        math::IsApprox(constraint.states[0].abs_lat_gap, 0.0) &&
        constraint.states[0].start_x(speed::Discomforts::kMax) <
            kMaxLonDistanceToFindNonDominantStuckObjectInM) {
      rt_event::PostRtEvent<rt_event::planner::TargetNonDominantStuckObject>(
          "");
      return std::make_optional<TypedObjectId>(
          TypedObjectId(object_id, pb::ObjectSourceType::kConstructionZone));
    }
  }

  return std::nullopt;
}

}  // namespace planner
