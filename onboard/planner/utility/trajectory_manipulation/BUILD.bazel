package(default_visibility = ["//visibility:public"])

cc_library(
    name = "trajectory_manipulation",
    srcs = ["trajectory_manipulation_util.cpp"],
    hdrs = ["trajectory_manipulation_util.h"],
    include_prefix = "planner/utility/trajectory_manipulation",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/world_model:planner_object",
        "//protobuf_cpp:protos_cpp",
    ],
)
