features:
  agent:
    - type: START_POINT_COORDINATE
      dimension: 2
    - type: END_POINT_COORDINATE
      dimension: 2
    - type: AGENT_SIZE
      dimension: 3
    - type: AGENT_HEADING
      dimension: 1
    - type: AGENT_SPEED
      dimension: 1
    - type: TURN_RATE
      dimension: 1
    - type: AGENT_BOX_HEADING
      dimension: 1
    - type: AGENT_ACCEL
      dimension: 1
    - type: AGENT_SPEED_STD
      dimension: 1
    - type: AGENT_HEADING_STD
      dimension: 1
    - type: AGENT_TYPE
      dimension: 1
    - type: VEHICLE_SIGNAL_LIGHTS_STATUS
      dimension: 4
  map:
    - type: START_POINT_COORDINATE
      dimension: 2
    - type: END_POINT_COORDINATE
      dimension: 2
    - type: MAP_OBJECT_DIRECTION
      dimension: 1
    - type: MAP_OBJECT_TYPE
      dimension: 1
    - type: MAP_OBJECT_TRAFFIC_LIGHT_COLOR
      dimension: 1
    - type: MAP_OBJECT_IS_HARD_BOUNDARY
      dimension: 1
    - type: LANE_HDMAP_TURN_TYPE
      dimension: 1
    - type: LANE_LEFT_MARKING_TYPE
      dimension: 1
    - type: LANE_RIGHT_MARKING_TYPE
      dimension: 1
  route:
    - type: START_POINT_COORDINATE
      dimension: 2
    - type: END_POINT_COORDINATE
      dimension: 2
    - type: SPEED_LIMIT
      dimension: 1
extractor_config:
  # Agent feature config.
  max_num_agents: 33
  num_vectors_in_agent_feature: 20
  # Route feature config.
  num_vectors_in_route_feature: 30
  max_num_routes: 1
  # Map object feature config.
  max_num_map_objects_segment: 128
  num_vectors_in_map_object_feature: 20
  use_ellipse_distance_to_select_map_objects: true
  map_object_types:
    - LANE
    - CROSSWALK
    - EXIT_ZONE
    - ROAD_HARD_BOUNDARY
    - CONSTRUCTION_ZONE
    - BUS_STOP_ZONE
    - TRAFFIC_CONE
    - BARRIER
  highlight_map_objects:
    - EGO_LANE
  mask_light_status_for_ego_agent: false
  treat_traffic_cone_and_barrier_as_map_objects: true
  use_dense_map_sampling_for_map_objects_other_than_lane: true
  extract_accurate_lane_marking_type: true
  extract_middle_hard_boundary: true
model_config:
  model_type: CONDITIONAL_PREDICTION_FOR_LANE_CHANGE
  num_modals: 10
  prediction_horizon: 90
  # Model output tensor types.
  # Enum defined in prediction_protos/prediction_config.proto.
  # The number and order of it must be consistent with model outputs.
  output_tensor_types:
    - TRAJS
    - TRAJ_PROBS
    # TODO(yuxi): Re-design the tensor names to make them meaningful.
    - CBP_LANE_CHANGE_YIELD_PROB
    - CBP_LANE_CHANGE_AGENTS_TRAJ
    - CBP_LANE_CHANGE_YIELD_TRAJ
    - CBP_LANE_CHANGE_BEHIND_AGENTS_TRAJ
    - CBP_LANE_CHANGE_PASS_TRAJ
