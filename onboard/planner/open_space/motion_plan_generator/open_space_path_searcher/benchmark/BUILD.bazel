load("//bazel:defs.bzl", "voy_benchmark")

package(default_visibility = ["//visibility:public"])

voy_benchmark(
    name = "open_space_grid_benchmark",
    srcs = ["open_space_grid_benchmark.cpp"],
    deps = [
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/open_space/constraints:motion_plan_problem_manager",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:grid",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:benchmark",
    ],
)

voy_benchmark(
    name = "reeds_shepp_path_generator_benchmark",
    srcs = ["reeds_shepp_path_generator_benchmark.cpp"],
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/math",
        "//onboard/common/vehicle_model",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:reeds_shepp_path_generator",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:benchmark",
    ],
)

voy_benchmark(
    name = "open_space_path_searcher_benchmark",
    srcs = ["open_space_path_searcher_benchmark.cpp"],
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/base:voy_base",
        "//onboard/common/math",
        "//onboard/common/vehicle_model",
        "//onboard/common/voy_protos:proto_util",
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/open_space/constraints:motion_plan_problem_manager",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher",
        "//onboard/planner/world_model:planner_object",
        "//onboard/planner/world_model:robot_state",
        "//onboard/varch:varch_base",
        "//protobuf_cpp:protos_cpp",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:benchmark",
    ],
)
