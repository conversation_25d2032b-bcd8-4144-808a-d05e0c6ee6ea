#include "planner/path/reasoning/agent_intention/reasoner/lane_change_agent_path_reasoner/lane_change_agent_path_reasoner.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <optional>
#include <utility>
#include <vector>

#include <unordered_map>
#include "log_utils/log_macros.h"
#include "log_utils/map_macros.h"
#include "math/constants.h"

#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/physics/target_speed_profile.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "trace/trace.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

// The critical spatial buffer for cut-in agent to avoid over-nudge.
constexpr double kCriticalSpatialBufferForCutinAgentInMeter = 2.0;
// The critical temporal buffer for cut-in agent to avoid over-nudge.
constexpr double kCriticalTemporalBufferForCutinAgentInSecond = 0.2;
// The step size of critical req_lat_gap decreases gradually to avoid overnudge.
constexpr double kCirticalRequiredLateralGapDecreaseStepSizeInMeter = 0.1;
// The step size of comfort req_lat_gap decreases gradually to avoid overnudge.
constexpr double kRequiredLateralGapDecreaseStepSizeInMeter = 0.2;
// The maximum time duration to consider lane change agent in the target lane
// traj.
constexpr int64 kMaxTimeDurationForRepulsionInMSec = 3000;
constexpr int64 kMaxTimeDurationForNudgeInMSec = 5000;

constexpr double kMinJerkAtSlowDownInMpss = -5.0;
constexpr double kMaxJerkAtSlowDownInMpss = 5.0;
constexpr double kExtremeJerkAtSlowDownInMpss = 10.0;
constexpr double kRelativeDistanceThresholdInMeter = 0.01;

// Copied from lead and merge path reasoner.Return true if the snapshot has
// comfortable lateral drivable space for the given needed_req_lat_gap_in_meter.
inline bool HasEnoughLateralSpacesWithGivenReqLatGap(
    const EgoInLaneParams& ego_param, const double cross_track_encroachment,
    const double min_corridor_width, double needed_req_lat_gap) {
  return cross_track_encroachment + needed_req_lat_gap + ego_param.width_m <=
         min_corridor_width;
}

// Copied from lead and merge path reasoner. Return true if all nudge snapshots
// have comfortable lateral drivable space for the given
// needed_req_lat_gap_in_meter.
bool EgoCanPassWithGivenReqLatGap(
    const EgoInLaneParams& ego_param,
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double needed_req_lat_gap_in_meter) {
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      if (!HasEnoughLateralSpacesWithGivenReqLatGap(
              ego_param,
              predicted_states[idx]
                  .inlane_state.inlane_param.cross_track_encroachment_m,
              predicted_states[idx].inlane_state.inlane_param.min_lane_width,
              needed_req_lat_gap_in_meter)) {
        return false;
      }
    }
  }
  return true;
}

// Copied from lead and merge path reasoner.
bool EgoCanPassWithGivenReqLatGap(
    const EgoInLaneParams& ego_param,
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double needed_req_lat_gap_in_meter) {
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      if (!HasEnoughLateralSpacesWithGivenReqLatGap(
              ego_param,
              predicted_states[idx]
                  .object_occupancy_param()
                  .cross_track_encroachment_m,
              predicted_states[idx]
                  .object_occupancy_param()
                  .min_corridor_width_m,
              needed_req_lat_gap_in_meter)) {
        return false;
      }
    }
  }
  return true;
}

std::optional<double> GetAgentLastSpeedDecisionPassReactionTime(
    const google::protobuf::RepeatedPtrField<speed::pb::SpeedConstraintResult>&
        last_speed_constraint_result,
    const int64_t object_id) {
  const bool has_any_yield_decision = std::any_of(
      last_speed_constraint_result.begin(), last_speed_constraint_result.end(),
      [&object_id](const speed::pb::SpeedConstraintResult& constraint_result) {
        return constraint_result.constraint().obj_id() == object_id &&
               constraint_result.decision() ==
                   speed::pb::SpeedDecision::YIELD &&
               constraint_result.constraint().generating_reasoner_id() ==
                   speed::pb::ReasonerId_Name(
                       speed::pb::ReasonerId::LEAD_AND_MERGE);
      });
  if (has_any_yield_decision) {
    return std::nullopt;
  }
  auto iter = std::find_if(
      last_speed_constraint_result.begin(), last_speed_constraint_result.end(),
      [&object_id](
          const speed::pb::SpeedConstraintResult& last_constraint_result) {
        return last_constraint_result.constraint().obj_id() == object_id &&
               last_constraint_result.decision() == speed::pb::PASS &&
               last_constraint_result.constraint().generating_reasoner_id() ==
                   speed::pb::ReasonerId_Name(
                       speed::pb::ReasonerId::LEAD_AND_MERGE);
      });
  if (iter == last_speed_constraint_result.end()) {
    return std::nullopt;
  }
  const double selected_discomfort = iter->constraint().selected_discomfort();
  const speed::DiscomfortVarying pass_reaction_time = speed::DiscomfortVarying(
      iter->constraint().settings().reaction_times().pass());
  return std::make_optional(pass_reaction_time(selected_discomfort));
}

// Copied from lead and merge path reasoner.
// Return the effective_nudge_index_ranges for the given
// needed_req_lat_gap_in_meter.
std::vector<math::Range<int>> GetFilteredNudgeIndexRanges(
    const EgoInLaneParams& ego_param,
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double needed_req_lat_gap_in_meter) {
  std::vector<math::Range<int>> nudge_index_ranges;
  nudge_index_ranges.reserve(effective_nudge_index_ranges.size());
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      if (HasEnoughLateralSpacesWithGivenReqLatGap(
              ego_param,
              predicted_states[idx]
                  .inlane_state.inlane_param.cross_track_encroachment_m,
              predicted_states[idx].inlane_state.inlane_param.min_lane_width,
              needed_req_lat_gap_in_meter)) {
        continue;
      }
      const math::Range<int> index_range(nudge_index_range.start_pos, idx - 1);
      if (math::IsValidRange(index_range)) {
        nudge_index_ranges.push_back(index_range);
      }
      return nudge_index_ranges;
    }
    nudge_index_ranges.push_back(nudge_index_range);
  }
  return nudge_index_ranges;
}

// Copied from lead and merge path reasoner.
// Return the effective_nudge_index_ranges for the given
// needed_req_lat_gap_in_meter.
std::vector<math::Range<int>> GetFilteredNudgeIndexRanges(
    const EgoInLaneParams& ego_param,
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double needed_req_lat_gap_in_meter) {
  std::vector<math::Range<int>> nudge_index_ranges;
  nudge_index_ranges.reserve(effective_nudge_index_ranges.size());
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      if (HasEnoughLateralSpacesWithGivenReqLatGap(
              ego_param,
              predicted_states[idx]
                  .object_occupancy_param()
                  .cross_track_encroachment_m,
              predicted_states[idx]
                  .object_occupancy_param()
                  .min_corridor_width_m,
              needed_req_lat_gap_in_meter)) {
        continue;
      }
      const math::Range<int> index_range(nudge_index_range.start_pos, idx - 1);
      if (math::IsValidRange(index_range)) {
        nudge_index_ranges.push_back(index_range);
      }
      return nudge_index_ranges;
    }
    nudge_index_ranges.push_back(nudge_index_range);
  }
  return nudge_index_ranges;
}

// Return the effective_nudge_index_ranges within the given time duration.
std::vector<math::Range<int>> GetFilteredNudgeIndexRangesWithinTimeDuration(
    const AgentInLaneStates& agent_inlane_state,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    const int64_t time_duration_ms) {
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  std::vector<math::Range<int>> nudge_index_ranges;
  nudge_index_ranges.reserve(effective_nudge_index_ranges.size());
  const int64_t first_time_ms = predicted_states.front().timestamp;
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    const int64_t idx_start = nudge_index_range.start_pos;
    if (predicted_states[idx_start].timestamp - first_time_ms >
        time_duration_ms) {
      // Skip the nudge index range if the first snapshot is out of time
      // duration.
      continue;
    }
    const int64_t idx_end = nudge_index_range.end_pos;

    if (predicted_states[idx_end].timestamp - first_time_ms <=
        time_duration_ms) {
      nudge_index_ranges.push_back(nudge_index_range);
      continue;
    }
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      if (predicted_states[idx].timestamp - first_time_ms > time_duration_ms) {
        const math::Range<int> index_range(nudge_index_range.start_pos,
                                           idx - 1);
        nudge_index_ranges.push_back(index_range);
        break;
      }
    }
  }
  return nudge_index_ranges;
}

// Copied from lead and merge path reasoner.
// Returns the recommend_required_lat_gap for the given
// needed_req_lat_gap_in_meter.
RequiredLateralGap GetRecommendedRequiredLateralGap(
    const RequiredLateralGap& required_lat_gap,
    double needed_req_lat_gap_in_meter,
    bool should_prioritize_nudge_snapshots = false) {
  // modify the critical req_lat_gap.
  if (should_prioritize_nudge_snapshots) {
    DCHECK_LE(needed_req_lat_gap_in_meter,
              required_lat_gap.comfort_required_lateral_gap);
    return RequiredLateralGap(std::max(0.0, needed_req_lat_gap_in_meter),
                              required_lat_gap.comfort_required_lateral_gap);
  }
  // modify the comfort req_lat_gap.
  if (needed_req_lat_gap_in_meter <
      required_lat_gap.critical_required_lateral_gap) {
    // (yongbing): add an RT event to track how frequently would this be called.
    return RequiredLateralGap(0.0, needed_req_lat_gap_in_meter);
  }
  return RequiredLateralGap(required_lat_gap.critical_required_lateral_gap,
                            needed_req_lat_gap_in_meter);
}

// Copied from lead and merge path reasoner.
// Two strategies to avoid the overnudge of cut-in here. One is
// priority_reducing snapshots (currently used), will reduce req_lat_gap if all
// snapshots is invalid until the needed_req_lat_gap_in_meter makes the snapshot
// not empty. The second is to reduce req_lat_gap first, so that the snapshots
// remain unchanged.
void GetRecommendedAgentIntentionResult(
    const EgoInLaneParams& ego_param,
    const AgentInLaneStates& agent_inlane_state,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    bool should_prioritize_nudge_snapshots,
    IntentionResultMetaData& intention_result) {
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return;
  }
  double needed_req_lat_gap_in_meter =
      should_prioritize_nudge_snapshots
          ? intention_result.required_lat_gap.critical_required_lateral_gap
          : intention_result.required_lat_gap.comfort_required_lateral_gap;
  while (needed_req_lat_gap_in_meter > -math::constants::kEpsilon) {
    if (should_prioritize_nudge_snapshots) {
      // If should_prioritize_nudge_snapshots = true, we will try to decrease
      // the req_lat_gap to give more snapshot.
      if (EgoCanPassWithGivenReqLatGap(ego_param, predicted_states,
                                       intention_result.nudge_index_ranges,
                                       needed_req_lat_gap_in_meter)) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            should_prioritize_nudge_snapshots);
        AddMultipleNominalNudgeConstraintOnPrediction(
            agent_inlane_state, intention_result.lateral_decision,
            intention_result.nudge_index_ranges,
            intention_result.required_lat_gap, intention_result);
        return;
      }
    } else {
      // If should_prioritize_nudge_snapshots = false, we will try to decrease
      // the nudge_index_ranges to keep a comfort lateral distance.
      const std::vector<math::Range<int>> effective_nudge_index_ranges =
          GetFilteredNudgeIndexRanges(ego_param, predicted_states,
                                      intention_result.nudge_index_ranges,
                                      needed_req_lat_gap_in_meter);
      if (!effective_nudge_index_ranges.empty()) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            should_prioritize_nudge_snapshots);
        intention_result.nudge_index_ranges = effective_nudge_index_ranges;
        AddMultipleNominalNudgeConstraintOnPrediction(
            agent_inlane_state, intention_result.lateral_decision,
            intention_result.nudge_index_ranges,
            intention_result.required_lat_gap, intention_result);
        return;
      }
    }
    needed_req_lat_gap_in_meter -=
        (should_prioritize_nudge_snapshots
             ? kCirticalRequiredLateralGapDecreaseStepSizeInMeter
             : kRequiredLateralGapDecreaseStepSizeInMeter);
  }

  needed_req_lat_gap_in_meter = std::max(
      /*critical_req_lat_gap=*/(should_prioritize_nudge_snapshots ? 0.0 : 0.2),
      needed_req_lat_gap_in_meter);
  const std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetFilteredNudgeIndexRanges(ego_param, predicted_states,
                                  intention_result.nudge_index_ranges,
                                  needed_req_lat_gap_in_meter);
  if (!effective_nudge_index_ranges.empty()) {
    intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
        intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
        should_prioritize_nudge_snapshots);
    intention_result.nudge_index_ranges = effective_nudge_index_ranges;
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return;
  } else if (tracked_pass_decision != pb::SnapshotIntention::IGNORE) {
    // Treat as a static agent for empty effective_nudge_index_ranges.
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge range empty treat as static; ");
    AddCurrentPoseNominalConstraint(agent_inlane_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return;
  }
  absl::StrAppend(&intention_result.debug_str,
                  "both effective_range and pass_decision are invalid; ");
  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
}

// Copied from lead and merge path reasoner.
void GetRecommendedAgentIntentionResult(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    bool should_prioritize_nudge_snapshots,
    IntentionResultMetaData& intention_result) {
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  if (predicted_states.empty()) {
    return;
  }
  double needed_req_lat_gap_in_meter =
      should_prioritize_nudge_snapshots
          ? intention_result.required_lat_gap.critical_required_lateral_gap
          : intention_result.required_lat_gap.comfort_required_lateral_gap;
  while (needed_req_lat_gap_in_meter > -math::constants::kEpsilon) {
    if (should_prioritize_nudge_snapshots) {
      // If should_prioritize_nudge_snapshots = true, we will try to decrease
      // the req_lat_gap to give more snapshot.
      if (EgoCanPassWithGivenReqLatGap(ego_param, predicted_states,
                                       intention_result.nudge_index_ranges,
                                       needed_req_lat_gap_in_meter)) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            should_prioritize_nudge_snapshots);
        AddMultipleNominalNudgeConstraintOnPrediction(
            object_occupancy_state, intention_result.lateral_decision,
            intention_result.nudge_index_ranges,
            intention_result.required_lat_gap, intention_result);
        return;
      }
    } else {
      // If should_prioritize_nudge_snapshots = false, we will try to decrease
      // the nudge_index_ranges to keep a comfort lateral distance.
      const std::vector<math::Range<int>> effective_nudge_index_ranges =
          GetFilteredNudgeIndexRanges(ego_param, predicted_states,
                                      intention_result.nudge_index_ranges,
                                      needed_req_lat_gap_in_meter);
      if (!effective_nudge_index_ranges.empty()) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            should_prioritize_nudge_snapshots);
        intention_result.nudge_index_ranges = effective_nudge_index_ranges;
        AddMultipleNominalNudgeConstraintOnPrediction(
            object_occupancy_state, intention_result.lateral_decision,
            intention_result.nudge_index_ranges,
            intention_result.required_lat_gap, intention_result);
        return;
      }
    }
    needed_req_lat_gap_in_meter -=
        (should_prioritize_nudge_snapshots
             ? kCirticalRequiredLateralGapDecreaseStepSizeInMeter
             : kRequiredLateralGapDecreaseStepSizeInMeter);
  }

  needed_req_lat_gap_in_meter = std::max(
      /*critical_req_lat_gap=*/(should_prioritize_nudge_snapshots ? 0.0 : 0.2),
      needed_req_lat_gap_in_meter);
  const std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetFilteredNudgeIndexRanges(ego_param, predicted_states,
                                  intention_result.nudge_index_ranges,
                                  needed_req_lat_gap_in_meter);
  if (!effective_nudge_index_ranges.empty()) {
    intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
        intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
        should_prioritize_nudge_snapshots);
    intention_result.nudge_index_ranges = effective_nudge_index_ranges;
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return;
  } else if (tracked_pass_decision != pb::SnapshotIntention::IGNORE) {
    // Treat as a static agent for empty effective_nudge_index_ranges.
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge range empty treat as static; ");
    AddCurrentPoseNominalConstraint(object_occupancy_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return;
  }
  absl::StrAppend(&intention_result.debug_str,
                  "both effective_range and pass_decision are invalid; ");
  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
}

// Returns true if the slow down agent will undershoot target speed
// given the initial speed and accel, even when using the maximum jerk.
inline bool IsInvalidInitialState(const double v0, const double a0,
                                  const double vt, const double j_max) {
  return 2 * j_max * (v0 - vt) < a0 * a0;
}

// This function handles the corner cases when the slow down start speed
// is close to the target speed and the absolute value of initial accel
// is large. In such cases, the profile undershoots target speed and lead to
// no solution. See the investigations in
// https://docs.google.com/document/d/1-Vve_N6kis0nxCH5hoNEW1Q-2jjSY9sdBaSRrdxMYlA/edit
// If found infeasible, this function first tries to enlarge max_jerk.
// If it still does not work, adjust initial_a and target_speed to
// produce a constant speed profile.
bool MaybeUpdateSlowDownProfileParams(const double initial_speed,
                                      double* initial_a,
                                      double* target_min_speed,
                                      double* max_jerk) {
  // If the initial a is small enough, no need to update.
  if (math::NearZero(*initial_a)) {
    return false;
  }
  if (IsInvalidInitialState(initial_speed, *initial_a, *target_min_speed,
                            *max_jerk)) {
    // Increase the jerk limit if the initial state is invalid.
    DVLOG(3) << "Invalid initial kinematic limit for agent reaction"
             << " v0 " << initial_speed << " vt " << *target_min_speed << " a0 "
             << *initial_a << " j_max " << *max_jerk
             << ". Set a larger max_jerk.";
    *max_jerk = kExtremeJerkAtSlowDownInMpss;
    if (IsInvalidInitialState(initial_speed, *initial_a, *target_min_speed,
                              *max_jerk)) {
      // If still invalid, set the params for a constant_v profile at the
      // initial speed.
      DVLOG(2) << "Invalid kinematic for agent reaction after extending "
                  "the jerk limit"
               << " v0 " << initial_speed << " vt " << *target_min_speed
               << " a0 " << *initial_a << " j_max " << *max_jerk
               << ". Return a constant speed profile at the initial speed.";
      *initial_a = 0.0;
      *target_min_speed = initial_speed;
    }
    return true;
  }
  return false;
}

std::optional<planner::physics::TargetSpeedProfile>
GenerateSlowDownSpeedProfile(const double initial_vel, double& initial_a,
                             const double yield_probability) {
  // If initial velocity is too small, we just disable agent reaction.
  constexpr double kMaxInitialSpeedToDisableAgentReactionInMps = 1.0;
  if (initial_vel < kMaxInitialSpeedToDisableAgentReactionInMps) {
    return std::nullopt;
  }
  constexpr double kMaxSpeedReductionToDisableAgentReactionInMps = 0.5;
  if (initial_vel * yield_probability <
      kMaxSpeedReductionToDisableAgentReactionInMps) {
    return std::nullopt;
  }

  // Use MM1D model to calculate the slow down profile starting from the
  // reaction start pose.
  constexpr double kSpeedReductionRatioDuringLaneChange = 0.4;

  double target_min_speed =
      initial_vel *
      (1 - yield_probability * kSpeedReductionRatioDuringLaneChange);
  // When the reaction time is near-zero (aka. the agent is reacting now),
  // we use the current acceleration of the object (provided by tracking) as
  // the initial acceleration in the AR slow down profile. In the other
  // cases, we assume no acceleration on the reaction start pose (provided by
  // prediction).
  double max_jerk = kMaxJerkAtSlowDownInMpss;

  // Handle the corner cases when the reaction start pose has extreme
  // kinematic that leads to infeasible slow down profile.
  MaybeUpdateSlowDownProfileParams(/*v0=*/initial_vel, &initial_a,
                                   &target_min_speed, &max_jerk);

  constexpr double kMinDecelerationForAgentsInMpss = -2.0;
  const double a_min = std::min(initial_a, kMinDecelerationForAgentsInMpss);
  const double a_max = std::max(initial_a, 0.0);

  const std::optional<planner::physics::TargetSpeedProfile> slow_down_profile =
      planner::physics::ComputeTargetSpeedProfile(
          /*v0=*/initial_vel,
          /*vt=*/target_min_speed,
          /*a0=*/initial_a,
          {/*a_min=*/a_min, /*j_min=*/kMinJerkAtSlowDownInMpss,
           /*j_max=*/max_jerk, /*a_max=*/a_max});
  // There should always be a feasible solution with the assigned agent slow
  // down params.
  DCHECK(slow_down_profile.has_value())
      << std::setprecision(17) << " v0 " << initial_vel << " vt "
      << target_min_speed << " a0 " << initial_a << " a_min " << a_min
      << " j_min " << kMinJerkAtSlowDownInMpss << " j_max "
      << kMaxJerkAtSlowDownInMpss << " a_max " << a_max;
  // We might fail to have a slow down profile in very rare cases. If this
  // happens onboard, we will fallback to use the original prediction for this
  // slow down constraint.
  return slow_down_profile;
}

// Adds agent reaction.
template <class PredictedState>
bool CanAddAgentReaction(
    const double yield_probability,
    const std::vector<PredictedState>& predicted_states,
    const std::function<void(const PredictedState&, int64_t&, double&, double&,
                             std::optional<double>&, math::geometry::Point2d&)>&
        fetch_func,
    int& cur_idx, speed::Profile& profile_upper_bound,
    math::geometry::Polyline2d& agent_center_path_points) {
  DCHECK(!profile_upper_bound.empty());
  DCHECK(!agent_center_path_points.empty());
  const int num_traj_poses = predicted_states.size();
  DCHECK_GT(num_traj_poses, 0);
  // Temporary output variables for fetching a state's information.
  int64_t state_timestamp = 0;
  double state_v = 0.0;
  double state_a = 0.0;
  std::optional<double> state_odom_m = std::nullopt;
  math::geometry::Point2d state_center = {};

  // Fetches initial state's information.
  const PredictedState& initial_state = predicted_states[cur_idx];
  fetch_func(initial_state, state_timestamp, state_v, state_a, state_odom_m,
             state_center);
  if (!state_odom_m.has_value()) {
    return false;
  }

  const int64_t initial_timestamp = state_timestamp;
  const double initial_vel = state_v;
  double initial_a = state_a;
  const double initial_odom_m = state_odom_m.value();

  const std::optional<planner::physics::TargetSpeedProfile> slow_down_profile =
      GenerateSlowDownSpeedProfile(initial_vel, initial_a, yield_probability);
  if (!slow_down_profile.has_value()) {
    return false;
  }

  // Fetches first state's information.
  fetch_func(predicted_states.front(), state_timestamp, state_v, state_a,
             state_odom_m, state_center);
  const int64_t first_timestamp = state_timestamp;
  const int64_t first_delta_timestamp = initial_timestamp - first_timestamp;

  // For the pose after reaction timestamp, use the relative odom to query the
  // corresponding travel time and fill in the updated timestamp.
  for (; cur_idx < num_traj_poses; ++cur_idx) {
    // Fetches current state's information.
    const PredictedState& agent_current_state = predicted_states[cur_idx];
    fetch_func(agent_current_state, state_timestamp, state_v, state_a,
               state_odom_m, state_center);
    if (!state_odom_m.has_value()) {
      return false;
    }
    const int64_t cur_timestamp = state_timestamp;
    const double cur_odom_m = state_odom_m.value();
    const math::geometry::Point2d cur_center = state_center;

    const double rel_dist_from_reaction_point = cur_odom_m - initial_odom_m;

    // Handle the case when the agent is still near the reaction start pose.
    // This means the agent just stops for a while after the reaction point
    // in the original prediction, we assign the status of the original
    // prediction pose in the case.
    if (math::NearZero(rel_dist_from_reaction_point,
                       kRelativeDistanceThresholdInMeter)) {
      DCHECK_GE(cur_timestamp, initial_timestamp);
      return true;
    }

    // The updated_t is w.r.t. reaction start timestamp.
    double updated_t = 0.0;
    double updated_v = 0.0;
    double updated_a = 0.0;
    double updated_j = 0.0;

    slow_down_profile->QueryByOdom(rel_dist_from_reaction_point, &updated_t,
                                   &updated_v, &updated_a, &updated_j);
    // If the agent reaches full stop, some original poses might never be
    // reached. In this happens, the query returns a max_double traveled time.
    // Set a max_int64_t timestamp correspondingly in this case.
    if (updated_t == std::numeric_limits<double>::max()) {
      return true;
    }
    if (math::Sec2Ms(updated_t) + first_delta_timestamp >
        kMaxTimeDurationForRepulsionInMSec) {
      return true;
    }
    int64_t abs_timestamp = math::Sec2Ms(updated_t) + initial_timestamp;
    // If the timestamp in the 'slow_down_profile' is even smaller than the one
    // in the original prediction, it means the AR-assumed agent deceleration is
    // weaker than the predicted deceleration (i.e. agent arrives at the same
    // position earlier). We use the original timestamp to capture the
    // agent pose at this state in the case. This procedure guarantees that,
    // the AR processed constraint will always be at least as slow as or slower
    // than the original prediction (aka. it is always easier to pass an
    // AR constraint than the original constraint).
    const bool is_prediction_slower =
        math::UpdateMax(cur_timestamp, abs_timestamp);
    if (is_prediction_slower) {
      return false;
    }
    agent_center_path_points.emplace_back(cur_center);
    profile_upper_bound.emplace_back(math::Ms2Sec(abs_timestamp), cur_odom_m,
                                     updated_v,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
  }
  return true;
}

};  // namespace

LaneChangeAgentPathReasoner::LaneChangeAgentPathReasoner(
    const EgoContextInfo& ego_context_info)
    : AbstractReasoner(ego_context_info,
                       pb::AgentReasonerId::LANE_CHANGE_AGENT) {
  // These buffers more conservative than the default value.
  st_buffer_config_.set_spatial_bottom_buffer_m(1.0);
  st_buffer_config_.set_spatial_top_buffer_m(
      kCriticalSpatialBufferForCutinAgentInMeter);
  st_buffer_config_.set_temporal_left_buffer_s(0.1);
  st_buffer_config_.set_temporal_right_buffer_s(
      kCriticalTemporalBufferForCutinAgentInSecond);
}

bool LaneChangeAgentPathReasoner::ShouldInvokePreRepulsionReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
    bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
    std::string& debug_str) const {
  (void)imaginary_actual_type;
  if (!IsAgentHandledByLaneChangeRepulsion(agent_type, blocking_state_meta_data,
                                           behavior_type, is_stationary)) {
    debug_str = "Not handled by lane change reasoner";
    return false;
  }
  return true;
}

bool LaneChangeAgentPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
  }
}

bool LaneChangeAgentPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(object_occupancy_state,
                                                blocking_state_data,
                                                trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
  }
}

bool LaneChangeAgentPathReasoner::GenerateLastPathCurve(
    const int64_t object_id,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    math::geometry::PolylineCurve2d& last_path_curve,
    std::string& debug_str) const {
  if (!reusable_last_path.has_value() ||
      !reusable_last_speed_profile.has_value() ||
      (reusable_last_path->poses_size() < 2 ||
       !lane_change_execution_info.has_value())) {
    debug_str +=
        absl::StrCat(" Add repulsion fail! The agent id is : ", object_id,
                     " reusable last path ", reusable_last_path.has_value(),
                     " lane change execution info ",
                     lane_change_execution_info.has_value(), "\n");
    return false;
  }

  math::geometry::Polyline2d last_path_curve_points;
  for (const auto& pose : reusable_last_path->poses()) {
    last_path_curve_points.emplace_back(pose.x_pos(), pose.y_pos());
  }
  // To avoid duplication of points.
  math::geometry::Polyline2d simplified_last_path_curve_points =
      math::geometry::Simplify(std::move(last_path_curve_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_last_path_curve_points.size() < 2) {
    debug_str += absl::StrCat(
        " Add repulsion fail: last path curve points < 2! The agent id is : ",
        object_id, "\n");
    return false;
  }
  last_path_curve =
      math::geometry::PolylineCurve2d(simplified_last_path_curve_points);
  return true;
}

std::vector<RepulsionMetaData>
LaneChangeAgentPathReasoner::AddRepulsionForLaneChange(
    const int64_t object_id, const int64_t trajectory_id,
    const bool is_lane_change_ignore_agent,
    const double agent_relative_lateral_speed, const double object_heading,
    const math::geometry::Polygon2d& object_polygon,
    const math::geometry::Point2d& object_center,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const double repulsion_required_lat_gap, const double ego_width,
    const speed::Profile& profile_upper_bound,
    const speed::Profile& yield_upper_bound,
    const math::geometry::Polyline2d& agent_center_path_points,
    std::string& debug_str) const {
  // To avoid duplication of points.
  std::vector<RepulsionMetaData> repulsion_meta_datas;

  const planner::pb::LaneChangeMode lane_change_direction =
      lane_change_execution_info->lane_change_direction;
  // If the agent is not on the lane change side, we don't add repulsion.
  if (lane_change_execution_info->last_path_curve.has_value()) {
    math::geometry::PolylineCurve2d path_curve =
        lane_change_execution_info->last_path_curve.value();
    math::pb::Side last_path_curve_side = path_curve.GetSide(object_center);
    if ((lane_change_direction ==
             planner::pb::LaneChangeMode::LEFT_LANE_CHANGE &&
         last_path_curve_side == math::pb::Side::kRight) ||
        (lane_change_direction ==
             planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE &&
         last_path_curve_side == math::pb::Side::kLeft)) {
      debug_str += absl::StrCat(
          " Add repulsion fail: agent not on the lane change side! The agent "
          "id is : ",
          object_id, "\n");
      return repulsion_meta_datas;
    }
  }
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    debug_str += absl::StrCat(
        " Add repulsion fail: agent center path point < 2! The agent id is : ",
        object_id, "\n");
    return repulsion_meta_datas;
  }
  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    debug_str += absl::StrCat(
        " Add repulsion fail: agent center path length < 0.2m! The agent id is "
        ": ",
        object_id, "\n");
    return repulsion_meta_datas;
  }

  const double lane_change_sign =
      lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? 1.0
          : -1.0;
  const double ego_relative_lateral_speed = ego_param_.cross_track_speed_mps;

  const double delta_v =
      agent_relative_lateral_speed - ego_relative_lateral_speed;
  constexpr double kMinLateralDecelerationForEgoInMpss = -1.0;
  constexpr double kMaxRequiredLatGapBufferInMeters = 1.0;
  const double delta_required_lat_gap =
      lane_change_sign * delta_v < 0.0
          ? std::min(kMaxRequiredLatGapBufferInMeters,
                     delta_v * delta_v /
                         (0.0 - 2.0 * kMinLateralDecelerationForEgoInMpss))
          : 0.0;
  debug_str += absl::StrCat(
      " Add repulsion success! The agent id is : ", object_id,
      " ego_relative_lateral_speed ", ego_relative_lateral_speed,
      " agent_relative_lateral_speed ", agent_relative_lateral_speed, "\n");
  constexpr double kExtendBoundaryMaxDistanceForPassRepulsionInMeters = 0.5;
  RepulsionMetaData::GuideProfileType pass_guide_profile_type =
      is_lane_change_ignore_agent
          ? RepulsionMetaData::GuideProfileType::kCustomized
          : RepulsionMetaData::GuideProfileType::kBestSTRollout;
  const std::optional<speed::Profile>& pass_customized_guide_profile =
      is_lane_change_ignore_agent
          ? std::make_optional(lane_change_execution_info->accelerate_speed)
          : std::nullopt;
  // Add pass repulsion.
  const RepulsionMetaData& pass_repulsion = RepulsionMetaData(
      lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? RepulsionMetaData::RepulsionDirection::kRight
          : RepulsionMetaData::RepulsionDirection::kLeft,
      object_polygon, simplified_agent_center_path, profile_upper_bound,
      profile_upper_bound, object_heading,
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt,
      repulsion_required_lat_gap + delta_required_lat_gap, reasoner_id_,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/trajectory_id,
      /*path_risk_mitigation_mode_in = */ false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in = */ std::nullopt,
      /*lateral_bound_in=*/std::nullopt,
      /*guide_profile_type_in=*/
      pass_guide_profile_type,
      /*customized_guide_profile_in*/ pass_customized_guide_profile,
      /*extend_boundary_level_in= */
      RepulsionMetaData::ExtendBoundaryLevel::kVirtualHard,
      /*clearance_in= */ kExtendBoundaryMaxDistanceForPassRepulsionInMeters,
      /*max_extend_boundary_distance_in= */
      kExtendBoundaryMaxDistanceForPassRepulsionInMeters,
      /*st_buffer_in=*/std::nullopt,
      /*post_handler_in= */ []() {});

  // Add yield repulsion, using customized guide profile.
  const std::optional<speed::Profile>& yield_customized_guide_profile =
      is_lane_change_ignore_agent
          ? std::make_optional(
                lane_change_execution_info->moderate_slow_down_speed)
          : std::make_optional(
                lane_change_execution_info->comfort_slow_down_speed);
  const RepulsionMetaData& yield_repulsion = RepulsionMetaData(
      lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? RepulsionMetaData::RepulsionDirection::kRight
          : RepulsionMetaData::RepulsionDirection::kLeft,
      object_polygon, simplified_agent_center_path, yield_upper_bound,
      yield_upper_bound, object_heading,
      /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/true, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(object_id, pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/trajectory_id,
      /*path_risk_mitigation_mode_in = */ false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in = */ std::nullopt,
      /*lateral_bound_in=*/std::nullopt,
      /*guide_profile_type_in=*/
      RepulsionMetaData::GuideProfileType::kCustomized,
      /*customized_guide_profile_in*/ yield_customized_guide_profile,
      /*extend_boundary_level_in= */
      RepulsionMetaData::ExtendBoundaryLevel::kVirtualHard,
      /*clearance_in= */ ego_width,
      /*max_extend_boundary_distance_in= */
      ego_width * 0.5,
      /*st_buffer_in=*/std::nullopt,
      /*post_handler_in= */ []() {});
  repulsion_meta_datas.push_back(pass_repulsion);
  repulsion_meta_datas.push_back(yield_repulsion);
  return repulsion_meta_datas;
}

// Gets agent yield intention.
std::optional<double> LaneChangeAgentPathReasoner::GetAgentLastYieldProbability(
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const int64_t object_id, std::string& debug_str) const {
  const google::protobuf::Map<::google::protobuf::int64, double>&
      last_agent_intention_map =
          lane_change_execution_info.value().last_agent_intention_map;
  if (last_agent_intention_map.empty()) {
    return std::nullopt;
  }
  double highest_yield_intention = -std::numeric_limits<double>::infinity();
  for (auto it = last_agent_intention_map.begin();
       it != last_agent_intention_map.end(); ++it) {
    if (it->second > highest_yield_intention) {
      highest_yield_intention = it->second;
    }
  }
  if (highest_yield_intention <
      speed::AgentIntentionTracker::kUncertainYieldProbabilityWithIntention) {
    debug_str += absl::StrCat(
        " The highest yield intention: : ", highest_yield_intention, " < ",
        speed::AgentIntentionTracker::kUncertainYieldProbabilityWithIntention,
        "\n");
    return std::nullopt;
  }
  return speed::AgentIntentionTracker::GetAgentYieldIntention(
      last_agent_intention_map, object_id);
}

void LaneChangeAgentPathReasoner::FetchInfoFromStampedAgentSnapshotInLaneState(
    const StampedAgentSnapshotInLaneState& state, int64_t& timestamp, double& v,
    double& a, std::optional<double>& odom_m,
    math::geometry::Point2d& center) const {
  timestamp = state.timestamp;
  v = state.inlane_state.inlane_param.speed_mps;
  a = state.inlane_state.inlane_param.acceleration_mpss;
  odom_m = std::make_optional(state.odom_m);
  center = state.inlane_state.inlane_param.pose.center_2d();
}

std::vector<RepulsionMetaData> LaneChangeAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
    /* trajectory_blocking_state_data_map */,
    const std::optional<pb::Path>& /* reusable_last_path*/,
    const std::optional<speed::Profile>& /*reusable_last_speed_profile*/,
    double repulsion_required_lat_gap,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_LaneChangeAgentPathReasoner_PreRepulsionReason);

  // Get the agent predicted states.
  const int64_t object_id = agent_inlane_state.object_id;
  const AgentTrajectoryInLaneStates& agent_traj_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id);
  const std::vector<StampedAgentSnapshotInLaneState> predicted_states =
      agent_traj_states.predicted_states;
  const int64_t first_timestamp = predicted_states.front().timestamp;

  // Get the agent agent reaction info from last speed profile.
  const std::optional<double>& agent_yield_probability =
      GetAgentLastYieldProbability(lane_change_execution_info, object_id,
                                   debug_str);
  const std::optional<double> last_pass_reaction_time =
      GetAgentLastSpeedDecisionPassReactionTime(
          lane_change_execution_info.value().last_speed_constraint_result,
          object_id);

  speed::Profile profile_upper_bound;
  math::geometry::Polyline2d agent_center_path_points;

  // Get the agent center path points and profile_upper_bound for repulsion.
  int cur_idx = 0;
  while (cur_idx < static_cast<int>(predicted_states.size())) {
    const StampedAgentSnapshotInLaneState& agent_current_state =
        predicted_states[cur_idx];
    const AgentSnapshotPose& agent_pose =
        agent_current_state.inlane_state.inlane_param.pose;
    const int64_t current_timestamp = agent_current_state.timestamp;
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            agent_pose.center_2d(), agent_center_path_points.back()))) {
      cur_idx++;
      continue;
    }
    agent_center_path_points.emplace_back(agent_pose.center_2d());
    profile_upper_bound.emplace_back(
        math::Ms2Sec(current_timestamp), agent_current_state.odom_m,
        agent_current_state.inlane_state.inlane_param.speed_mps,
        /*a_in=*/0.0, /*j_in=*/0.0);
    if (current_timestamp - first_timestamp >
        kMaxTimeDurationForRepulsionInMSec) {
      break;
    }
    auto fetch_func = [this](const StampedAgentSnapshotInLaneState& state,
                             int64_t& timestamp, double& v, double& a,
                             std::optional<double>& odom_m,
                             math::geometry::Point2d& center) {
      return this->FetchInfoFromStampedAgentSnapshotInLaneState(
          state, timestamp, v, a, odom_m, center);
    };
    if (((agent_yield_probability.has_value() &&
          last_pass_reaction_time.has_value()) &&
         math::Ms2Sec(current_timestamp) > last_pass_reaction_time.value()) &&
        CanAddAgentReaction<StampedAgentSnapshotInLaneState>(
            agent_yield_probability.value(), predicted_states, fetch_func,
            cur_idx, profile_upper_bound, agent_center_path_points)) {
      break;
    }
    cur_idx++;
  }
  const double agent_relative_lateral_speed =
      agent_inlane_state.tracked_state.inlane_param.cross_track_speed_mps;

  return AddRepulsionForLaneChange(
      object_id, agent_inlane_state.primary_trajectory_id,
      blocking_state_data.reasoning_info.is_lane_change_ignore_agent(),
      agent_relative_lateral_speed,
      agent_inlane_state.tracked_state.inlane_param.pose.heading(),
      agent_inlane_state.tracked_state.inlane_param.pose.contour().polygon(),
      agent_inlane_state.tracked_state.inlane_param.pose.center_2d(),
      lane_change_execution_info, repulsion_required_lat_gap,
      ego_param_.width_m, profile_upper_bound, profile_upper_bound,
      agent_center_path_points, debug_str);
}

void LaneChangeAgentPathReasoner::FetchInfoFromObjectStampedSnapshotInfo(
    const ObjectStampedSnapshotInfo& state, int64_t& timestamp, double& v,
    double& a, std::optional<double>& odom_m,
    math::geometry::Point2d& center) const {
  timestamp = state.timestamp();
  v = state.pose().speed();
  a = state.pose().acceleration();
  odom_m = state.odom_m();
  center = state.pose().center_2d();
}

std::vector<RepulsionMetaData> LaneChangeAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& /*reusable_last_path*/,
    const std::optional<speed::Profile>& /*reusable_last_speed_profile*/,
    double repulsion_required_lat_gap,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    std::string& debug_str) const {
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  TRACE_EVENT_SCOPE(
      planner, RunPathReasoning_LaneChangeAgentPathReasoner_PreRepulsionReason);
  // Get the agent predicted states.
  const int64_t object_id = object_occupancy_state.object_id();
  const ObjectTrajectoryStates& object_traj_states = FIND_OR_DIE_WITH_PRINT(
      object_occupancy_state.predicted_trajectory_occupancy_states(),
      object_occupancy_state.primary_trajectory_id());
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      object_traj_states.predicted_states();
  const int64_t first_timestamp = predicted_states.front().timestamp();

  // Get the agent agent reaction info from last speed profile.
  const std::optional<double>& agent_yield_probability =
      GetAgentLastYieldProbability(lane_change_execution_info, object_id,
                                   debug_str);
  DCHECK(lane_change_execution_info.has_value());
  const std::optional<double> last_pass_reaction_time =
      GetAgentLastSpeedDecisionPassReactionTime(
          lane_change_execution_info.value().last_speed_constraint_result,
          object_id);

  // Get the agent center path points and profile_upper_bound,yield_upper_bound
  // for repulsion.
  speed::Profile profile_upper_bound;
  math::geometry::Polyline2d agent_center_path_points;
  int cur_idx = 0;
  while (cur_idx < static_cast<int>(predicted_states.size())) {
    const ObjectStampedSnapshotInfo& agent_current_state =
        predicted_states[cur_idx];
    const TrafficParticipantPose& agent_pose = agent_current_state.pose();
    const int64_t current_timestamp = agent_current_state.timestamp();
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            agent_pose.center_2d(), agent_center_path_points.back()))) {
      cur_idx++;
      continue;
    }
    if (!agent_current_state.odom_m().has_value()) {
      break;
    }
    agent_center_path_points.emplace_back(agent_pose.center_2d());
    profile_upper_bound.emplace_back(math::Ms2Sec(current_timestamp),
                                     agent_current_state.odom_m().value(),
                                     agent_pose.speed(),
                                     /*a_in=*/0.0, /*j_in=*/0.0);
    if (current_timestamp - first_timestamp >
        kMaxTimeDurationForRepulsionInMSec) {
      break;
    }
    auto fetch_func = [this](const ObjectStampedSnapshotInfo& state,
                             int64_t& timestamp, double& v, double& a,
                             std::optional<double>& odom_m,
                             math::geometry::Point2d& center) {
      return this->FetchInfoFromObjectStampedSnapshotInfo(state, timestamp, v,
                                                          a, odom_m, center);
    };

    if (((agent_yield_probability.has_value() &&
          last_pass_reaction_time.has_value()) &&
         math::Ms2Sec(current_timestamp) > last_pass_reaction_time.value()) &&
        CanAddAgentReaction<ObjectStampedSnapshotInfo>(
            agent_yield_probability.value(), predicted_states, fetch_func,
            cur_idx, profile_upper_bound, agent_center_path_points)) {
      break;
    }
    cur_idx++;
  }
  const double agent_relative_lateral_speed =
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .cross_track_speed_mps;

  return AddRepulsionForLaneChange(
      object_id, object_occupancy_state.primary_trajectory_id(),
      blocking_state_data.reasoning_info.is_lane_change_ignore_agent(),
      agent_relative_lateral_speed, object_occupancy_state.pose().heading(),
      object_occupancy_state.pose().contour().polygon(),
      object_occupancy_state.pose().center_2d(), lane_change_execution_info,
      repulsion_required_lat_gap, ego_param_.width_m, profile_upper_bound,
      profile_upper_bound, agent_center_path_points, debug_str);
}

bool LaneChangeAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForMixedBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() = st_buffer_config_;
  }
  return true;
}

bool LaneChangeAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForMixedBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() = st_buffer_config_;
  }
  return true;
}

bool LaneChangeAgentPathReasoner::PreReasonForHardBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool LaneChangeAgentPathReasoner::PreReasonForHardBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForHardBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  DCHECK_EQ(trajectory_intention_data.blocking_sequences.size(), 1);
  *trajectory_intention_data.blocking_sequences[0].mutable_buffer_config() =
      st_buffer_config_;
  return true;
}

bool LaneChangeAgentPathReasoner::
    AdjustNudgeRequiredLatGapForLaneChangePathReasoner(
        const int64_t object_id,
        const pb::TrajectoryReasoningInfo& reasoning_info,
        const pb::SnapshotIntention::PassState intention_algorithm_pass_side,
        const double agent_speed_mps, RequiredLateralGap& required_lat_gap,
        std::string* debug_str) const {
  if (reasoning_info.is_open_door_vehicle()) {
    required_lat_gap.comfort_required_lateral_gap = 1.8;
    required_lat_gap.critical_required_lateral_gap = 0.8;
    if (debug_str) {
      absl::StrAppend(debug_str, "open_door_vehicle: increase rlg; ");
    }
    return true;
  }
  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, object_id, intention_algorithm_pass_side,
      active_lat_gap_adjust_meta_, agent_speed_mps,
      reasoning_info.is_interested_agent_during_lane_change_or_abort(),
      required_lat_gap, debug_str);
  if (reasoning_info.is_large_vehicle()) {
    // Increases comfort req lat gap for large vehicle.
    required_lat_gap.comfort_required_lateral_gap = 1.2;
    if (debug_str) {
      absl::StrAppend(debug_str, "large_veh increase com_rlg 1.2; ");
    }
  }

  return true;
}

bool LaneChangeAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  intention_result.reasoner_id = reasoner_id_;
  // Get effective nudge index ranges with soft blocking sequence part.
  std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetEffectiveNudgeIndexRanges(
          intention_result.nudge_index_ranges,
          trajectory_intention_data.blocking_sequences,
          /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);
  AdjustNudgeRequiredLatGapForLaneChangePathReasoner(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      intention_result.lateral_decision,
      agent_inlane_state.tracked_state.inlane_param.speed_mps,
      intention_result.required_lat_gap, &(intention_result.debug_str));

  // Set is_overtaken for cut-in vehicle.
  if (blocking_state_data.reasoning_info.is_cut_in() ||
      behavior_type_ == pb::DECOUPLED_PULL_OVER) {
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
  }
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          agent_inlane_state.tracked_state.inlane_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());
  if (blocking_state_data.reasoning_info
          .is_lane_change_ignore_agent_in_post_reason()) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "lane change post reason ignore; ");
    return true;
  }
  if (effective_nudge_index_ranges.empty() ||
      (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE)) {
    // No need change the st planner's dicision for near-distance scenarios.
    if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
        intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
        ShouldStayCautiousAgent(blocking_state_data.reasoning_info)) {
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
    // Treat as a static agent for nudge intention disappearance scenarios.
    if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
        (ShouldStayCautiousAgent(blocking_state_data.reasoning_info) ||
         ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                      blocking_state_data.reasoning_info))) {
      intention_result.is_overtaken = false;
      intention_result.is_static = true;
      intention_result.lateral_decision = tracked_pass_decision;
      absl::StrAppend(&intention_result.debug_str,
                      "lat_dec igonre treat as static; ");
      AddCurrentPoseNominalConstraint(agent_inlane_state,
                                      intention_result.lateral_decision,
                                      intention_result);
      return true;
    }

    // Ignore this agent which is faraway or hard_blocking tracked state (Bad
    // nudge decision).
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str, "bad nudge decision; ");
    return true;
  }

  // Add effective nudge index ranges with soft blocking part.
  DCHECK(intention_result.lateral_decision != pb::SnapshotIntention::IGNORE);
  if (blocking_state_data.reasoning_info.is_cut_in()) {
    GetRecommendedAgentIntentionResult(
        ego_param_, agent_inlane_state, tracked_pass_decision,
        /*should_prioritize_nudge_snapshots=*/true, intention_result);
    return true;
  }

  // For the tail agent in the target lane when the ego almost finished lane
  // change, we consider less nudge snapshot to avoid fp nudge.
  const double lane_change_sign =
      lane_change_info_.lane_change_instance.direction() ==
              planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? 1.0
          : -1.0;
  constexpr double kMaxEncroachDistanceCrossLaneCurveInMeter = 1.4;
  constexpr int64
      kMaxTimeDurationForTailAgentWhenEgoAlmostFinishLaneChangeInMSec = 600;
  const bool is_tail_agent_when_ego_almost_finish_lane_change =
      lane_change_info_.lane_change_metadata
                  .ego_corner_signed_lat_dist_to_cross_lane_curve() *
              lane_change_sign >
          kMaxEncroachDistanceCrossLaneCurveInMeter &&
      blocking_state_data.reasoning_info.is_agent_behind_rear_bumper() &&
      blocking_state_data.reasoning_info.is_lane_change_target_lane_agent();
  // We consider the nudge snapshot within lane change time duration.
  const std::vector<math::Range<int>>& nudge_index_ranges =
      GetFilteredNudgeIndexRangesWithinTimeDuration(
          agent_inlane_state, intention_result.nudge_index_ranges,
          is_tail_agent_when_ego_almost_finish_lane_change
              ? kMaxTimeDurationForTailAgentWhenEgoAlmostFinishLaneChangeInMSec
              : kMaxTimeDurationForNudgeInMSec);
  if (nudge_index_ranges.empty()) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge index is empty within time duration; ");
    return true;
  }
  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision, nudge_index_ranges,
      intention_result.required_lat_gap, intention_result);
  return true;
}

bool LaneChangeAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  intention_result.reasoner_id = reasoner_id_;
  // Get effective nudge index ranges with soft blocking sequence part.
  std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetEffectiveNudgeIndexRanges(
          intention_result.nudge_index_ranges,
          trajectory_intention_data.blocking_sequences,
          /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);

  AdjustNudgeRequiredLatGapForLaneChangePathReasoner(
      blocking_state_data.object_id, blocking_state_data.reasoning_info,
      intention_result.lateral_decision,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .speed_mps,
      intention_result.required_lat_gap, &(intention_result.debug_str));
  // Set is_overtaken for cut-in vehicle.
  if (blocking_state_data.reasoning_info.is_cut_in()) {
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
  }
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());
  if (blocking_state_data.reasoning_info
          .is_lane_change_ignore_agent_in_post_reason()) {
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "lane change post reason ignore; ");
    return true;
  }
  if (effective_nudge_index_ranges.empty() ||
      (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE)) {
    // No need change the st planner's dicision for near-distance scenarios.
    if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
        intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
        ShouldStayCautiousAgent(blocking_state_data.reasoning_info)) {
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      return true;
    }
    // Treat as a static agent for nudge intention disappearance scenarios.
    if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
        (ShouldStayCautiousAgent(blocking_state_data.reasoning_info) ||
         ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                      blocking_state_data.reasoning_info))) {
      intention_result.is_overtaken = false;
      intention_result.is_static = true;
      intention_result.lateral_decision = tracked_pass_decision;
      absl::StrAppend(&intention_result.debug_str,
                      "lat_dec igonre treat as static; ");
      AddCurrentPoseNominalConstraint(object_occupancy_state,
                                      intention_result.lateral_decision,
                                      intention_result);
      return true;
    }

    // Ignore this agent which is faraway or hard_blocking tracked state (Bad
    // nudge decision).
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str, "bad nudge decision; ");
    return true;
  }

  // Add effective nudge index ranges with soft blocking part.
  DCHECK(intention_result.lateral_decision != pb::SnapshotIntention::IGNORE);
  intention_result.nudge_index_ranges = effective_nudge_index_ranges;
  DCHECK(
      !object_occupancy_state.predicted_trajectory_occupancy_states().empty());
  if (blocking_state_data.reasoning_info.is_cut_in()) {
    GetRecommendedAgentIntentionResult(
        ego_param_, object_occupancy_state, tracked_pass_decision,
        /*should_prioritize_nudge_snapshots=*/true, intention_result);
  } else {
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
  }

  return true;
}

bool LaneChangeAgentPathReasoner::IsAgentHandledByLaneChangeNudge(
    const LateralBlockingStateMetaData& blocking_state_meta_data) const {
  return blocking_state_meta_data.reasoning_info
             .is_interested_agent_during_lane_change_or_abort() ||
         blocking_state_meta_data.reasoning_info
             .is_cut_ahead_agent_during_lane_change();
}

// TODO(judychen): Refactor the IsAgentHandledByLaneChangeRepulsion API to
// reuse the logic here.
// Returns true for the agent which is handled by lane change path reasoner.
bool LaneChangeAgentPathReasoner::IsAgentHandledByLaneChangeRepulsion(
    const voy::perception::ObjectType agent_type,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const pb::BehaviorType behavior_type, const bool is_statinoary) const {
  if (behavior_type != pb::CROSS_LANE) {
    return false;
  }
  if (agent_type != voy::perception::ObjectType::VEHICLE) {
    return false;
  }
  // TODO(Judychen): revisit this logic for lane change, those logic is now from
  // GetTrajectoryIntentionMetaData.
  if ((is_statinoary ||
       blocking_state_meta_data.reasoning_info.is_crossing()) ||
      (!blocking_state_meta_data.reasoning_info.is_in_current_lane() ||
       blocking_state_meta_data.reasoning_info.is_oncoming())) {
    return false;
  }
  if (blocking_state_meta_data.reasoning_info.is_far_away_behind() ||
      blocking_state_meta_data.reasoning_info.is_ignorable_behind()) {
    return false;
  }
  if ((!blocking_state_meta_data.reasoning_info.was_nudge_intention() &&
       blocking_state_meta_data.reasoning_info.is_ignorable_unknown()) ||
      (blocking_state_meta_data.reasoning_info.contain_abnomal_inlane_state() &&
       !blocking_state_meta_data.reasoning_info.is_agent_within_comfort())) {
    return false;
  }
  return blocking_state_meta_data.reasoning_info
             .is_lane_change_target_lane_agent() &&
         !blocking_state_meta_data.reasoning_info
              .is_cut_ahead_agent_during_lane_change();
}

}  // namespace path
}  // namespace planner
