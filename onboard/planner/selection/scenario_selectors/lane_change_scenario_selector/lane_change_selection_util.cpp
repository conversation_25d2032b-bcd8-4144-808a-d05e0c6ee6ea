#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <sstream>

#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "planner/behavior/util/lane_sequence/route_preview_utility.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/discomforts/discomforts.h"

namespace planner {
namespace selection {

namespace {
// Heading difference threshold that will be preceived by agents as LC cut-in.
constexpr double kLcHeadDiffThres = 0.07;
// Threshold to determine if ego's heading deviation from lane center line can
// be considered as LC start signal.
constexpr double kLcHeadDeviThres = 0.055;
// Thresholds for LC safety checking.
constexpr double kMinTolerableLcRiskLevel = 0.01;
constexpr double kMaxTolerableLcRiskLevel = 0.2;
constexpr double kTolerableLcRearEndRiskLevel = 0.01;
// Consecutive LC ready cycles required to decay the related flicker cost to 0.
constexpr double kLcFlickerBarrierCost = 1.0;
constexpr double kLcFlickerBarrierCostFactor = 4.5;
constexpr int kRequiredConsecutiveLcReadyCycles = 1;
constexpr int kMidConsecutiveLcReadyCycles = 2;
constexpr int kSuffcientConsecutiveLcReadyCycles = 5;
constexpr int kReducedLcReadyCyclesForRisk = 2;

}  // namespace

std::optional<speed::State>
ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
    const speed::State& init_state, const double t, const double jerk) {
  if (init_state.v < 0.0) {
    LOG(INFO) << __func__ << "reversing motion not supported;";
    return std::nullopt;
  }

  if (jerk < math::constants::kEpsilon) {
    LOG(INFO) << __func__
              << "input jerk should be absolute value and non-zero;";
    return std::nullopt;
  }

  speed::State mid_state = init_state;
  speed::State final_state = mid_state;

  // Phase 1: release brake or gas with the input jerk until the acceleration
  // reaches zero.
  const double phase1_jerk = init_state.a > 0.0 ? -jerk : jerk;
  DCHECK_GT(std::abs(phase1_jerk), 0.0);
  DCHECK_LE(init_state.a * phase1_jerk, 0.0);

  const double phase1_duration = -init_state.a / phase1_jerk;
  DCHECK_GE(phase1_duration, 0.0);

  mid_state.j = phase1_jerk;

  // Compute the state when zero speed is reached in a constant jerk motion. The
  // zero speed state should be guaranteed to be available by the inputs.
  auto compute_zero_speed_state = [phase1_jerk,
                                   phase1_duration](const speed::State& state) {
    const double delta = state.a * state.a - 2.0 * phase1_jerk * state.v;
    DCHECK_GE(delta, 0.0);
    const double reach_zero_speed_duration =
        phase1_duration - std::sqrt(delta) / phase1_jerk;
    DCHECK_GE(reach_zero_speed_duration, 0.0);
    DCHECK_LE(reach_zero_speed_duration, phase1_duration);
    return state.Move(reach_zero_speed_duration,
                      /*check_neg_speed=*/false);
  };

  if (t < phase1_duration) {
    // Constant jerk motion only.
    final_state = mid_state.Move(t, /*check_neg_speed=*/false);

    if (final_state.v < -math::constants::kEpsilon) {
      // Update the valid braking duration.
      return compute_zero_speed_state(mid_state);
    }

    return final_state;
  }

  // Constant jerk + constant speed motion.
  final_state = mid_state.Move(phase1_duration, /*check_neg_speed=*/false);

  if (final_state.v < -math::constants::kEpsilon) {
    // Update the valid braking duration.
    return compute_zero_speed_state(mid_state);
  }

  // Phase 2: keep moving with constant speed.
  DCHECK_GE(final_state.v, -math::constants::kEpsilon);
  DCHECK_GE(t, phase1_duration);
  final_state.j = 0.0;
  final_state.x += final_state.v * (t - phase1_duration);
  final_state.t = t;

  return final_state;
}

double ComputeLaneChangeGapAlignedTolerance(
    const CrossLaneObjectInfo* gap_object_info, const bool is_pass_gap,
    const double ego_speed, const double urgency_score,
    const bool is_consecutive_lc, std::ostringstream& debug_oss) {
  // The max duration allowed in advance for a gap being seen as ready, which
  // means the CL candidate's flicker cost will be decreased gradually.
  constexpr double kLcGapAlignedTimeMaxToleranceForStaticObjectInSec = 3.0;
  constexpr double kLcGapAlignedTimeMaxToleranceInSec = 1.5;
  constexpr double kLcGapAlignedTimeMinToleranceInSec = 0.5;

  if (is_pass_gap &&
      gap_object_info->planner_object().is_primary_stationary() &&
      (is_consecutive_lc ||
       !gap_object_info->planner_object().has_stationary_to_move_intention())) {
    debug_oss << "\nGap aligned tolerance info:\n"
              << "gap_aligned_time_tolerance: "
              << kLcGapAlignedTimeMaxToleranceForStaticObjectInSec
              << "s, static gap tail object; is_consecutive_lc: "
              << is_consecutive_lc << "\n";
    return kLcGapAlignedTimeMaxToleranceForStaticObjectInSec;
  }

  // Compute gap align duration tolerance w.r.t. ego's speed. The higher the
  // speed, the less the tolerance.
  constexpr double kLcGapAlignedMinEgoSpeedInMps = 3.0;
  constexpr double kLcGapAlignedMaxEgoSpeedInMps = 6.0;
  const double gap_aligned_time_tolerance_by_speed =
      math::GetLinearInterpolatedY(
          kLcGapAlignedMinEgoSpeedInMps, kLcGapAlignedMaxEgoSpeedInMps,
          kLcGapAlignedTimeMaxToleranceInSec,
          kLcGapAlignedTimeMinToleranceInSec, ego_speed);

  // Compute gap align duration tolerance w.r.t. urgency score. The more urgent
  // the lane change, the more the tolerance.
  constexpr double kMaxEnlargeFactorAtHighUrgency = 2.0;
  const double urgency_score_factor = math::GetLinearInterpolatedY(
      0.0, 1.0, 1.0, kMaxEnlargeFactorAtHighUrgency, urgency_score);

  const double gap_aligned_time_tolerance =
      std::min(gap_aligned_time_tolerance_by_speed * urgency_score_factor,
               kLcGapAlignedTimeMaxToleranceInSec);

  debug_oss << "\nGap aligned tolerance info:\n"
            << DUMP_TO_STREAM(gap_aligned_time_tolerance,
                              kLcGapAlignedTimeMaxToleranceInSec,
                              gap_aligned_time_tolerance_by_speed,
                              urgency_score_factor, urgency_score, ego_speed)
            << "\n";
  return gap_aligned_time_tolerance;
}

bool IsLaneChangeGapReady(const SpeedWorldModel& world_model,
                          const speed::TrajectoryInfo& trajectory_info,
                          const math::geometry::PolylineCurve2d& extended_path,
                          const speed::pb::SpeedSeed& speed_seed,
                          const double urgency_score,
                          const double gap_aligned_time, const bool is_pass_gap,
                          std::ostringstream& debug_oss) {
  if (speed_seed.lc_guide_seed().empty()) {
    return true;
  }

  const CrossLaneObjectInfoList* target_region_object_info_list =
      trajectory_info.cross_lane_info_manager()
          .GetTargetRegionObjectInfoListPtr();
  if (target_region_object_info_list == nullptr) {
    return true;
  }

  const int64_t object_id = is_pass_gap
                                ? speed_seed.lc_guide_seed(0).tail_obj_id()
                                : speed_seed.lc_guide_seed(0).lead_obj_id();
  debug_oss << "is_pass_gap: " << is_pass_gap << ", object_id: " << object_id
            << "\n";
  const CrossLaneObjectInfo* object_info =
      target_region_object_info_list->Find(object_id);
  if (object_info == nullptr) {
    debug_oss
        << "Skip gap ready check due to the object is not in target region.\n";
    return true;
  }

  const double gap_aligned_time_tolerance =
      ComputeLaneChangeGapAlignedTolerance(
          object_info, is_pass_gap,
          world_model.robot_state().plan_init_state_snapshot().speed(),
          urgency_score, /*is_consecutive_lc =*/
          (trajectory_info.GetCrossLaneInfo()
               .cross_lane_meta.cross_lane_count() > 1),
          debug_oss);

  // TODO(pengfei): Keep computing both methods for now. See if these two
  // conditions could be replaced with the second one only.
  const bool is_gap_ready_by_aligned_time =
      gap_aligned_time < gap_aligned_time_tolerance;

  std::ostringstream inner_debug_oss;
  const bool is_gap_ready_by_relative_states =
      IsLaneChangeGapReadyByRelativeStates(
          world_model, trajectory_info, extended_path, speed_seed, object_info,
          is_pass_gap, gap_aligned_time_tolerance, inner_debug_oss);

  debug_oss << "\nis_gap_ready_by_aligned_time: "
            << is_gap_ready_by_aligned_time
            << "\ngap_aligned_time = " << gap_aligned_time
            << ", gap_aligned_time_tolerance = " << gap_aligned_time_tolerance
            << "\n\nis_gap_ready_by_relative_states: "
            << is_gap_ready_by_relative_states << "\n"
            << inner_debug_oss.str();

  return is_gap_ready_by_aligned_time || is_gap_ready_by_relative_states;
}

bool IsLaneChangeGapReadyByRelativeStates(
    const SpeedWorldModel& world_model,
    const speed::TrajectoryInfo& trajectory_info,
    const math::geometry::PolylineCurve2d& extended_path,
    const speed::pb::SpeedSeed& speed_seed,
    const CrossLaneObjectInfo* gap_object_info, const bool is_pass_gap,
    const double future_t, std::ostringstream& debug_oss) {
  DCHECK(!speed_seed.lc_guide_seed().empty());
  DCHECK(gap_object_info != nullptr);

  if (extended_path.size() < 2) {
    return false;
  }

  const int64_t object_id = gap_object_info->id;

  // Skip gap ready condition check for non vehicle or cyclist tail object.
  const voy::perception::ObjectType object_type =
      gap_object_info->planner_object().object_type();
  if (is_pass_gap && object_type != voy::perception::ObjectType::VEHICLE &&
      object_type != voy::perception::ObjectType::CYCLIST) {
    debug_oss << "gap tail object: " << object_id << ", "
              << voy::perception::ObjectType_Name(object_type)
              << ", non vehicle or cyclist; skip gap ready check;\n";
    return true;
  }

  const RobotState& robot_state = world_model.robot_state();
  const double ego_speed = robot_state.plan_init_state_snapshot().speed();
  const double ego_accel =
      robot_state.plan_init_state_snapshot().acceleration();
  const double ego_arc_length =
      trajectory_info.plan_init_ra_arc_length_on_extended_path();

  debug_oss << "Ego init state: "
            << DUMP_TO_STREAM(ego_arc_length, ego_speed, ego_accel) << "\n";

  if (ego_speed < -math::constants::kEpsilon) {
    debug_oss << "skip for reversing motion;";
    return false;
  }

  // Compute the relative position between ego and the gap agent in the future.
  constexpr double kPassYieldComfortableJerkInMpsss = 1.0;
  const std::optional<speed::State> future_ego_state =
      ComputeStateAtTimeForReleasingBrakeOrGasMotionWithConstrantJerk(
          /*init_state=*/
          {/*t_in=*/0.0,
           /*x_in=*/ego_arc_length, ego_speed, ego_accel,
           /*j_in=*/0.0},
          /*t=*/future_t, kPassYieldComfortableJerkInMpsss);

  if (!future_ego_state.has_value()) {
    debug_oss << "no future_ego_state;";
    return false;
  }

  debug_oss << "Ego future state: "
            << DUMP_TO_STREAM(future_ego_state->x, future_ego_state->v,
                              future_ego_state->a, future_ego_state->t)
            << "\n";

  // Compute the position of the agent in the near future.
  const auto& agent_bps = world_model.object_prediction_map().find(object_id);
  if (agent_bps == world_model.object_prediction_map().end()) {
    return false;
  }

  // TODO(pengfei): Only use the primary bp for now. See if we need to consider
  // those multi-output bps.
  const auto& primary_bp =
      std::find_if(agent_bps->second.begin(), agent_bps->second.end(),
                   [](const PredictedTrajectoryWrapper& bp) {
                     return bp.is_primary_trajectory();
                   });
  DCHECK(primary_bp != agent_bps->second.end());

  const planner::pb::TrajectoryPose& agent_future_pose =
      primary_bp->ComputePoseAtTimestamp(
          robot_state.plan_init_state_snapshot().timestamp() +
          math::Sec2Ms(future_t));
  const double future_agent_center_position =
      extended_path
          .GetProximity({agent_future_pose.x_pos(), agent_future_pose.y_pos()},
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const double agent_half_length =
      0.5 * gap_object_info->planner_object().length();
  const double future_ego_ra_position = future_ego_state->x;
  const double ego_ra_to_rb = robot_state.car_model_with_shape()
                                  .shape_measurement()
                                  .rear_bumper_to_rear_axle();
  const double ego_ra_to_fb =
      robot_state.car_model_with_shape().shape_measurement().length() -
      ego_ra_to_rb;

  debug_oss << "\nEgo-agent relative states info in the future:\n"
            << DUMP_TO_STREAM(future_ego_ra_position,
                              future_agent_center_position, agent_half_length,
                              ego_ra_to_rb, ego_ra_to_fb)
            << "\n";

  double future_relative_distance = 0.0;
  if (is_pass_gap) {
    future_relative_distance = future_ego_ra_position - ego_ra_to_rb -
                               future_agent_center_position - agent_half_length;
  } else {
    future_relative_distance = future_agent_center_position -
                               agent_half_length - future_ego_ra_position -
                               ego_ra_to_fb;
  }

  debug_oss << "future_relative_distance: " << future_relative_distance;

  return future_relative_distance > 0.0;
}

bool GetLaneChangeGapInfo(const speed::pb::SpeedSeed& speed_seed,
                          bool& is_pass_gap, double& gap_aligned_time) {
  if (speed_seed.lc_guide_seed().empty()) {
    return false;
  }

  const planner::speed::pb::SpeedSolverLcGuideSeed& lc_guide_seed =
      speed_seed.lc_guide_seed(0);
  if (!lc_guide_seed.is_valid()) {
    return false;
  }

  const int64_t gap_aligned_time_ix = lc_guide_seed.gap_aligned_time_ix();
  if (gap_aligned_time_ix < 0) {
    return false;
  }

  is_pass_gap = lc_guide_seed.is_pass();
  gap_aligned_time = gap_aligned_time_ix * constants::kTrajectoryIntervalInSec;
  return true;
}

bool IsCrossLaneSafe(const TrajectoryMetaData& candidate,
                     std::ostringstream& debug_oss) {
  debug_oss << "\n[Risk info]:\n";

  if (candidate.lane_change_selection_info.lc_risk_info.lc_risk >
      kTolerableLcRearEndRiskLevel) {
    return false;
  }

  if (candidate.pose_collision_risk < kMinTolerableLcRiskLevel) {
    return true;
  }

  // Check if the risk happens in the far future. Relax the risk tolerance if
  // the ttc is large.
  constexpr double kMaxTtcToRelaxRiskToleranceInSec = 6.0;
  constexpr double kMinTtcToRelaxRiskToleranceInSec = 5.0;
  const RiskEvalResult& risk_result = candidate.risk_eval_result;
  for (const AgentRiskInfo& agent_risk_info : risk_result.agents_risk_info) {
    for (const RiskConstraintEvalResult& constraint_eval_result :
         agent_risk_info.constraints_eval_results) {
      const double ttc = constraint_eval_result.ttc_in_sec;
      debug_oss << "Agent: " << agent_risk_info.agent_id
                << ", bp: " << constraint_eval_result.prediction_id
                << ", risk: " << constraint_eval_result.risk << ", ttc: " << ttc
                << "\n";

      // Ignore the risk if the ttc is large enough.
      if (ttc > planner::constants::kTrajectoryHorizonInSec) {
        continue;
      }

      // Relax the risk tolerance if the ttc is large.
      const double risk_tolerance_by_ttc = math::GetLinearInterpolatedY(
          kMinTtcToRelaxRiskToleranceInSec, kMaxTtcToRelaxRiskToleranceInSec,
          kMinTolerableLcRiskLevel, kMaxTolerableLcRiskLevel, ttc);
      debug_oss << "risk_tolerance_by_ttc: " << risk_tolerance_by_ttc << "\n";
      if (constraint_eval_result.risk > risk_tolerance_by_ttc) {
        return false;
      }
    }
  }

  return true;
}

void ComputeCrossLaneFlickerCost(const SpeedWorldModel& world_model,
                                 const planner::pb::DecoupledManeuverSeed& seed,
                                 TrajectoryMetaData* candidate,
                                 bool is_lane_change_ready) {
  if (!candidate->IsCrossLane()) {
    return;
  }

  // If collision risk of a LC candidate is higher than threshold, or that the
  // lane change is not ready from reasoning, we decrease the number of LC
  // ready cycles to increase flicker cost and delay LC.
  int effective_ready_cycle = seed.selection_seed()
                                  .cross_lane_selection_seed()
                                  .consecutive_cl_ready_cycles();
  std::ostringstream debug_oss;
  std::ostringstream inner_debug_oss;
  inner_debug_oss << effective_ready_cycle << " [consecutive_lc_ready_cycles]";
  candidate->lane_change_selection_info.is_cross_lane_safe =
      IsCrossLaneSafe(*candidate, debug_oss);
  if (!candidate->lane_change_selection_info.is_cross_lane_safe) {
    effective_ready_cycle -= kReducedLcReadyCyclesForRisk;
    inner_debug_oss
        << " - " << kReducedLcReadyCyclesForRisk << " [pose_collision_risk("
        << candidate->pose_collision_risk << ") > " << kMinTolerableLcRiskLevel
        << " or lc_risk("
        << candidate->lane_change_selection_info.lc_risk_info.lc_risk << ") > "
        << kTolerableLcRearEndRiskLevel << "]";
  }

  if (!is_lane_change_ready) {
    effective_ready_cycle -= kSuffcientConsecutiveLcReadyCycles;
    inner_debug_oss << " - " << kSuffcientConsecutiveLcReadyCycles
                    << " [turn light duration not enough]";
  }

  const double urgency_score =
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? candidate->trajectory_info->GetCrossLaneInfo().generic_urgency_score
          : candidate->trajectory_info->GetCrossLaneInfo().urgency_score;

  // Check if the selected gap is ready for ego.
  // Skip this check during abort for faster lane change recovery.
  bool is_gap_ready = true;
  bool is_pass_gap = true;
  double gap_aligned_time = 0.0;
  if (seed.selected_lane_change_state() !=
          ::planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      GetLaneChangeGapInfo(seed.speed_seed(), is_pass_gap, gap_aligned_time)) {
    is_gap_ready = IsLaneChangeGapReady(
        world_model, *DCHECK_NOTNULL(candidate->trajectory_info),
        *DCHECK_NOTNULL(candidate->extended_path), seed.speed_seed(),
        urgency_score, gap_aligned_time, is_pass_gap, debug_oss);
  }

  inner_debug_oss << " = " << effective_ready_cycle
                  << " [effective_ready_cycle]\n";

  debug_oss << "\n\nis_gap_ready: " << is_gap_ready << "\n";
  if (!is_gap_ready) {
    effective_ready_cycle = 0;
    debug_oss << "effective_ready_cycle reset to 0 due to gap_not_ready\n";
  }

  debug_oss << "\nLane change discomfort info:\n"
            << "discomfort: " << candidate->speed_discomfort << "\n";
  // Don't start a progress ELC with non-zero discomfort.
  if (seed.selected_lane_change_state() !=
          ::planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      IsRiskyELC(world_model.planner_object_map(),
                 candidate->trajectory_info->GetCrossLaneInfo()
                     .cross_lane_meta.cross_lane_signal_source_type()
                     .lane_change_signal_source_type(),
                 candidate->trajectory_info->GetCrossLaneInfo()
                     .elc_triggered_object_id,
                 candidate->speed_discomfort)) {
    effective_ready_cycle = 0;
    debug_oss << "\neffective_ready_cycle reset to 0 due to non-zero "
                 "discomfort for progress elc;\n";
  }

  // Don't execute lane change at high discomfort when it is not urgent.
  if (seed.selected_lane_change_state() !=
          ::planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      IsHighDiscomfortLaneChangeUnderLowUrgency(
          candidate->trajectory_info->GetCrossLaneInfo(),
          candidate->speed_discomfort,
          world_model.robot_state().plan_init_state_snapshot().speed(),
          world_model.robot_state()
              .car_model_with_shape()
              .shape_measurement()
              .rear_bumper_to_rear_axle(),
          debug_oss)) {
    effective_ready_cycle = 0;
    debug_oss << "\neffective_ready_cycle reset to 0 due to high discomfort "
                 "under low urgency;\n";
  }

  // Don't start a lane change in fork with high discomfort.
  const bool is_risky_lane_change_in_fork =
      seed.selected_lane_change_state() !=
          ::planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      IsRiskyLaneChangeInFork(candidate->trajectory_info->GetCrossLaneInfo()
                                  .is_current_lane_change_in_fork,
                              candidate->speed_discomfort);
  if (is_risky_lane_change_in_fork) {
    effective_ready_cycle = 0;
    debug_oss << "\nHigh discomfort LC in fork; effective_ready_cycle reset to "
                 "0; flicker cost not scaled by urgency score;\n";
  }

  // Don't start a lane change on highway with non-zero discomfort when the lane
  // change is under low urgency.
  const bool is_risky_lane_change_on_highway =
      seed.selected_lane_change_state() !=
          ::planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      IsRiskyLaneChangeOnHighway(candidate->trajectory_info->GetCrossLaneInfo(),
                                 candidate->speed_discomfort);
  if (is_risky_lane_change_on_highway) {
    effective_ready_cycle = 0;
    debug_oss << "\nNon-zero discomfort LC under low urgency on highway; "
                 "effective_ready_cycle reset to 0; flicker cost not scaled by "
                 "urgency score;\n";
  }

  // Compute flicker cost w.r.t. the effective_ready_cycle.
  constexpr double kUrgencyScoreThreshForLcReadyCycles = 0.6;
  const double lc_ready_cycles_thresh =
      urgency_score > kUrgencyScoreThreshForLcReadyCycles
          ? kMidConsecutiveLcReadyCycles
          : kSuffcientConsecutiveLcReadyCycles;
  candidate->lane_seq_flicker_cost =
      (effective_ready_cycle > 0)
          ? math::GetLinearInterpolatedY(
                kRequiredConsecutiveLcReadyCycles, lc_ready_cycles_thresh,
                kLcFlickerBarrierCost, 0.0, effective_ready_cycle)
          : kLcFlickerBarrierCostFactor * kLcFlickerBarrierCost;

  effective_ready_cycle > 0
      ? (debug_oss << "\neffective_ready_cycle between ["
                   << kRequiredConsecutiveLcReadyCycles << ", "
                   << lc_ready_cycles_thresh
                   << "] will be projected into cost range ["
                   << kLcFlickerBarrierCost << ", 0.0]")
      : (debug_oss
         << "\neffective_ready_cycle <= 0 results in flicker barrier cost "
         << kLcFlickerBarrierCostFactor * kLcFlickerBarrierCost << ". ");

  // Adjust flicker cost by lane change urgency score.
  if (is_lane_change_ready && is_gap_ready && !is_risky_lane_change_in_fork &&
      !is_risky_lane_change_on_highway) {
    candidate->lane_seq_flicker_cost = math::GetLinearInterpolatedY(
        0.0, 1.0, candidate->lane_seq_flicker_cost, 0.0, urgency_score);
    debug_oss << "\nScaled to " << candidate->lane_seq_flicker_cost
              << " due to urgency score " << urgency_score << ". ";
  }

  std::string flicker_cost_debug_str = debug_oss.str();
  debug_oss.str("");
  debug_oss << "flicker cost = " << candidate->lane_seq_flicker_cost
            << ", effective_ready_cycle = " << effective_ready_cycle << "\n"
            << flicker_cost_debug_str << "\n\n"
            << inner_debug_oss.str();
  candidate->flicker_debug_str = debug_oss.str();
}

// Adjusts path diff and lateral cost for lane change candidates according to
// the last path latching status.
double CalculateCrossLaneDiffCompensationByLastPath(
    const std::vector<TrajectoryMetaData*>& candidates,
    const int64_t consecutive_last_cl_cycles) {
  const auto last_cl_iter = std::find_if(
      candidates.begin(), candidates.end(),
      [](const TrajectoryMetaData* candidate) {
        return candidate->IsCrossLane() && candidate->IsLastTrajectory();
      });
  if (last_cl_iter == candidates.end()) {
    return 0.0;
  }

  double max_risk_in_normal_lane_change =
      -std::numeric_limits<double>::infinity();
  double min_risk_in_normal_lane_change =
      std::numeric_limits<double>::infinity();
  double min_path_diff_in_normal_lane_change =
      std::numeric_limits<double>::infinity();
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane() || candidate->IsLastTrajectory()) {
      continue;
    }

    math::UpdateMax(candidate->collision_risk, max_risk_in_normal_lane_change);
    math::UpdateMin(candidate->collision_risk, min_risk_in_normal_lane_change);
    math::UpdateMin(candidate->normalized_path_diff,
                    min_path_diff_in_normal_lane_change);
  }

  if ((*last_cl_iter)->collision_risk < max_risk_in_normal_lane_change) {
    return 0.0;
  }

  constexpr int64_t kMaxConsecutiveLastCLCycles = 5;
  if (consecutive_last_cl_cycles < kMaxConsecutiveLastCLCycles &&
      (*last_cl_iter)->collision_risk <
          min_risk_in_normal_lane_change + math::constants::kEpsilon) {
    return 0.0;
  }

  // Last CL has been selected for a while or there is risk in last CL
  // candidate, recompute the path diff cost for normal lane change candidates.
  const double path_diff_compensation = (*last_cl_iter)->normalized_path_diff -
                                        min_path_diff_in_normal_lane_change;
  return path_diff_compensation;
}

// Updates selected lane change gap info.
void UpdateSelectedLaneChangeGapInfo(
    const std::map<std::string, TrajectoryMetaData*>& candidates,
    const planner::pb::SelectedLaneChangeGapInfo& prev_selected_gap_info,
    planner::pb::SelectedLaneChangeGapInfo* selected_gap_info) {
  const auto iter =
      std::find_if(candidates.begin(), candidates.end(), [](const auto& pair) {
        const TrajectoryMetaData& candidate = *pair.second;
        return candidate.is_selected &&
               candidate.speed_result.lc_guide_seed().is_valid();
      });
  if (iter == candidates.end()) {
    selected_gap_info->Clear();
    return;
  }

  const TrajectoryMetaData& gap_candidate = *iter->second;
  const speed::pb::SpeedSolverLcGuideSeed& lc_guide_seed =
      gap_candidate.speed_result.lc_guide_seed();
  const int64_t tail_obj_id = lc_guide_seed.tail_obj_id();
  const int64_t lead_obj_id = lc_guide_seed.lead_obj_id();
  selected_gap_info->set_tail_obj_id(tail_obj_id);
  selected_gap_info->set_lead_obj_id(lead_obj_id);

  const CrossLaneInfo& cross_lane_info =
      gap_candidate.trajectory_info->GetCrossLaneInfo();
  const CrossLaneRegionInfo* target_region_info_ptr = GetRegionInfoPtrByType(
      CrossLaneRegionType::TARGET, cross_lane_info.region_infos);
  if (target_region_info_ptr == nullptr) {
    selected_gap_info->Clear();
    return;
  }

  const CrossLaneObjectInfoList& target_region_object_info_list =
      target_region_info_ptr->object_info_list;
  const CrossLaneObjectInfo* tail_object_info_ptr =
      target_region_object_info_list.FindNonLeaving(tail_obj_id);
  const CrossLaneObjectInfo* lead_object_info_ptr =
      target_region_object_info_list.FindNonLeaving(lead_obj_id);
  if (tail_object_info_ptr == nullptr || lead_object_info_ptr == nullptr) {
    selected_gap_info->set_gap_length(std::numeric_limits<double>::infinity());
  } else {
    const double excluded_length =
        0.5 * (lead_object_info_ptr->planner_object().length() +
               tail_object_info_ptr->planner_object().length());
    selected_gap_info->set_gap_length(lead_object_info_ptr->object_ego_dist -
                                      tail_object_info_ptr->object_ego_dist -
                                      excluded_length);
  }

  // TODO(Huoliang): Consider if we should regard it as the same gap if a new
  // tail or lead appears in this cycle.
  if ((tail_obj_id != 0 &&
       tail_obj_id == prev_selected_gap_info.tail_obj_id()) ||
      (lead_obj_id != 0 &&
       lead_obj_id == prev_selected_gap_info.lead_obj_id())) {
    // It is the same gap with last cycle.
    selected_gap_info->set_gap_selected_cycles(
        prev_selected_gap_info.gap_selected_cycles() + 1);
  } else {
    // It is a new gap.
    selected_gap_info->set_gap_selected_cycles(1);
  }
}

bool IsLaneInRoundabout(const pnc_map::Lane* current_lane) {
  DCHECK_NE(current_lane, nullptr);
  return DCHECK_NOTNULL(DCHECK_NOTNULL(current_lane->section())->road())
      ->is_through_roundabout();
}

std::optional<double> GetRerouteIncreasingDistance(
    const std::optional<lane_selection::PreviewRouteResult>&
        preview_route_result,
    const pnc_map::Lane* current_lane) {
  const std::optional<double> reroute_increasing_distance_from_preview =
      FLAGS_planning_enable_use_can_reroute_flag_for_lc_high_risk
          ? lane_selection::GetRerouteIncreasingDistToDest(preview_route_result)
          : lane_selection::GetRerouteIncreasingDistToDestFromSequencePreview(
                preview_route_result);
  if (reroute_increasing_distance_from_preview.has_value()) {
    return std::make_optional(reroute_increasing_distance_from_preview.value());
  }
  if (!IsLaneInRoundabout(current_lane)) {
    return std::nullopt;
  }
  // Return an estimation value of reroute increasing distance that drive around
  // a roundabout.
  constexpr double kIncreasingDistanceOfRoundaboutInMeter = 300.0;
  return std::make_optional(kIncreasingDistanceOfRoundaboutInMeter);
}

std::optional<double> GetLaneChangeRouteUrgencyScore(
    const CrossLaneInfo& cross_lane_info, const pnc_map::Lane* current_lane,
    bool& can_reroute, std::ostringstream& debug_oss) {
  // TODO(elliotsong): Open for all consecutive lane change.
  if (cross_lane_info.cross_lane_meta.cross_lane_count() > 1 &&
      !IsLaneInRoundabout(current_lane)) {
    debug_oss << "Ignore consecutive lane change that not in a roundabout.";
    return std::nullopt;
  }

  // Calculate the score of the distance to hard lane change end.
  const std::optional<LaneChangeUrgencyInfo>& lc_end_urgency_info =
      GetMostUrgencyInfoByUrgencyType(
          cross_lane_info.urgency_infos,
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END);
  const bool has_hard_lane_change_end =
      lc_end_urgency_info.has_value() &&
      lc_end_urgency_info->stop_line_type() ==
          planner::pb::LaneChangeStopLineType::HARD_STOP_LINE;
  if (!has_hard_lane_change_end) {
    debug_oss << "No hard lane change end.";
    return std::nullopt;
  }
  const double dist_to_hard_lane_change_end =
      lc_end_urgency_info->urgency_dist();
  static constexpr double kMinDistToHardLaneChangeEnd = 15.0;
  static constexpr double kMaxDistToHardLaneChangeEnd = 30.0;
  const double score_dist_to_hard_lane_change_end = math::GetInterpolationRatio(
      kMinDistToHardLaneChangeEnd, kMaxDistToHardLaneChangeEnd,
      dist_to_hard_lane_change_end);

  const std::optional<double> reroute_increasing_distance =
      GetRerouteIncreasingDistance(cross_lane_info.preview_route_result,
                                   current_lane);
  can_reroute = reroute_increasing_distance.has_value();
  static constexpr double kMinRerouteIncreasingDistance = 500.0;
  static constexpr double kMaxRerouteIncreasingDistance = 3000.0;
  // If cannot reroute, use max increasing distance.
  const double valid_reroute_increasing_distance =
      can_reroute ? reroute_increasing_distance.value()
                  : kMaxRerouteIncreasingDistance;
  const double score_reroute_increasing_distance = math::GetInterpolationRatio(
      kMinRerouteIncreasingDistance, kMaxRerouteIncreasingDistance,
      valid_reroute_increasing_distance);
  debug_oss << DUMP_TO_STREAM(can_reroute, dist_to_hard_lane_change_end,
                              score_dist_to_hard_lane_change_end,
                              valid_reroute_increasing_distance,
                              score_reroute_increasing_distance);
  return std::make_optional(std::max(score_dist_to_hard_lane_change_end,
                                     score_reroute_increasing_distance));
}

bool ShouldGiveRewardToLaneChange(
    const speed::TrajectoryInfo& trajectory_info) {
  const auto previous_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedLastFrame());

  return trajectory_info.is_lane_change_crawl() ||
         (previous_maneuver_seed->selected_behavior_type() ==
              planner::pb::BehaviorType::CROSS_LANE &&
          trajectory_info.GetCrossLaneInfo().should_creep);
}

bool IsLaneChangeDueToBlockage(const CrossLaneInfo& cross_lane_info) {
  const std::vector<LaneChangeUrgencyInfo>& lane_change_urgency_infos =
      cross_lane_info.urgency_infos;
  if (lane_change_urgency_infos.empty()) {
    return false;
  }

  return lane_change_urgency_infos.front().urgency_type() ==
         planner::pb::LaneChangeUrgencyType::BLOCKAGE_ON_SOURCE_REGION;
}

bool IsRiskyLaneChangeAtHighDiscomfort(
    const double speed_discomfort, const double ego_speed,
    const double comfortable_lc_half_duration, const double ego_ra_to_rb,
    const CrossLaneRegionInfo* target_region_info_ptr,
    std::ostringstream& debug_oss) {
  // Check the speed and the discomfort condition.
  constexpr double kMidSpeedThreshInMps = 6.0;
  constexpr double kHighSpeedThreshInMps = 10.0;
  constexpr double kLateralStateTransitionDurationInSec = 1.0;
  constexpr double kLongitudinalSafeDistanceInFutureInMeter = 6.0;

  // Block LC with a high discomfort when ego's speed is high.
  if (ego_speed > kMidSpeedThreshInMps &&
      speed_discomfort >
          (speed::Discomforts::kMid + speed::Discomforts::kHighLevelRes * 2.0 -
           math::constants::kEpsilon)) {
    debug_oss << "High speed[" << ego_speed << "m/s], high discomfort["
              << speed_discomfort << "];\n";
    return true;
  }

  // Don't block LC when either ego's speed or the discomfort is low.
  if (ego_speed < kHighSpeedThreshInMps ||
      speed_discomfort <
          (speed::Discomforts::kMid + speed::Discomforts::kHighLevelRes -
           math::constants::kEpsilon)) {
    debug_oss << "Low speed[" << ego_speed << "m/s] or low discomfort["
              << speed_discomfort << "];\n";
    return false;
  }

  debug_oss << "High speed[" << ego_speed << "m/s], mid discomfort["
            << speed_discomfort << "];\n";

  // Check if there are other agents close.
  DCHECK(target_region_info_ptr != nullptr);
  const double clear_distance =
      ego_speed *
      (comfortable_lc_half_duration - kLateralStateTransitionDurationInSec);
  const std::vector<int64_t>& object_ids = GetObjectIdsInArclengthRange(
      target_region_info_ptr->object_info_list,
      target_region_info_ptr->ego_ra_arc_length - clear_distance,
      target_region_info_ptr->ego_ra_arc_length + clear_distance * 0.5,
      /*consider_leaving_object=*/true);

  for (const int64_t object_id : object_ids) {
    const CrossLaneObjectInfo* object_info =
        target_region_info_ptr->object_info_list.Find(object_id);
    DCHECK(object_info != nullptr);
    debug_oss << "Agent " << object_id << ", ";
    if (object_info->object_ego_dist > 0.0) {
      debug_oss << "in front;\n";
      continue;
    }

    // Block LC with a mid discomfort when there are agents close.
    const double object_speed = object_info->planner_object().speed();
    const double ego_rb_to_object_fb_dist =
        -object_info->object_ego_dist -
        object_info->planner_object().length() * 0.5 - ego_ra_to_rb;
    const double reduced_distance =
        (object_speed - ego_speed) * comfortable_lc_half_duration;

    if (ego_rb_to_object_fb_dist - reduced_distance <
        kLongitudinalSafeDistanceInFutureInMeter) {
      debug_oss << "close; "
                << DUMP_TO_STREAM(ego_rb_to_object_fb_dist, reduced_distance,
                                  object_speed, ego_speed)
                << "\n";
      return true;
    }

    debug_oss << "not close; "
              << DUMP_TO_STREAM(ego_rb_to_object_fb_dist, reduced_distance,
                                object_speed, ego_speed)
              << "\n";
  }

  // Don't block LC with a mid discomfort when there aren't agents close.
  debug_oss << "No risky agent; clear_distance[" << clear_distance << "m];\n";
  return false;
}

bool IsHighDiscomfortLaneChangeUnderLowUrgency(
    const CrossLaneInfo& cross_lane_info, const double speed_discomfort,
    const double ego_speed, const double ego_ra_to_rb,
    std::ostringstream& debug_oss) {
  // Don't block LC when it is urgent.
  constexpr double kMaxUrgencyScore = 1.0;
  const double urgency_score =
      FLAGS_planning_enable_gap_align_urgency_score_for_lane_change
          ? cross_lane_info.generic_urgency_score
          : cross_lane_info.urgency_score;
  if (urgency_score >
      LaneChangeUrgencyInfo::GetUrgencyScoreWeightByUrgencyLevel(
          LaneChangeUrgencyInfo::UrgencyLevel::kLow) *
              kMaxUrgencyScore -
          math::constants::kEpsilon) {
    debug_oss << "High urgency score[" << urgency_score << "];\n";
    return false;
  }

  debug_oss << "Low urgency score[" << urgency_score << "];\n";

  // Check the traffic density of target region. We pick a relative small
  // threshold here since the traffic density computed for now is usually
  // smaller than what it really is.
  constexpr double kTrafficDensityThresh = 0.3;
  const CrossLaneRegionInfo* target_region_info_ptr = GetRegionInfoPtrByType(
      CrossLaneRegionType::TARGET, cross_lane_info.region_infos);

  // TODO(lane-change): Should use 'DCHECK(target_region_info_ptr != nullptr);'
  // here to replace the early return.
  // But we have inconsistency of lane change instance between lane change info
  // and path, which could result in DCHECK failure. So we use an early return
  // temporarily.
  if (target_region_info_ptr == nullptr) {
    debug_oss << "No target region info;\n";
    return false;
  }

  // Don't block LC when the traffic is busy.
  if (target_region_info_ptr->traffic_density > kTrafficDensityThresh) {
    debug_oss << "High traffic density["
              << target_region_info_ptr->traffic_density << "];\n";
    return false;
  }

  debug_oss << "Low traffic density[" << target_region_info_ptr->traffic_density
            << "];\n";

  // Check the remaining distance for lane change.
  constexpr double kMinRemainingDistanceForLaneChangeInMeter = 100.0;
  constexpr double kComfortableLaneChangeDurationInSec = 8.0;
  constexpr double kMaxFrontDistanceToConsiderInMeter = 150.0;

  const CrossLaneMetaData& cross_lane_meta = cross_lane_info.cross_lane_meta;
  const double remaining_distance_per_lc =
      (cross_lane_meta.has_dist_to_cross_lane_last_chance_pt()
           ? cross_lane_meta.dist_to_cross_lane_last_chance_pt()
           : cross_lane_meta.remaining_distance_for_cross_lane()) /
      cross_lane_meta.cross_lane_count();
  const double comfortable_lane_change_distance =
      std::fmax(kMinRemainingDistanceForLaneChangeInMeter,
                ego_speed * kComfortableLaneChangeDurationInSec);

  // When there are slow agents ahead in source lane, we need more distance to
  // do a comfortable lane change. Assuming we don't want to brake for the slow
  // agent in our planning horizon before executing the lane change, we need to
  // keep an extra clear distance ahead considering the speed diff between ego
  // and the agent.
  double extra_distance_for_source_region_agent = 0.0;
  const CrossLaneRegionInfo* source_region_info_ptr = GetRegionInfoPtrByType(
      CrossLaneRegionType::SOURCE, cross_lane_info.region_infos);
  // TODO(lane-change): Replace with DCHECK(source_region_info_ptr != nullptr).
  if (source_region_info_ptr != nullptr) {
    const std::vector<int64_t>& source_object_ids =
        GetObjectIdsInArclengthRange(source_region_info_ptr->object_info_list,
                                     source_region_info_ptr->ego_ra_arc_length,
                                     source_region_info_ptr->ego_ra_arc_length +
                                         kMaxFrontDistanceToConsiderInMeter,
                                     /*consider_leaving_object=*/true);
    if (!source_object_ids.empty()) {
      const double closest_source_region_agent_speed =
          DCHECK_NOTNULL(source_region_info_ptr->object_info_list.Find(
                             source_object_ids.front()))
              ->planner_object()
              .speed();
      extra_distance_for_source_region_agent =
          closest_source_region_agent_speed < ego_speed
              ? (ego_speed - closest_source_region_agent_speed) *
                    planner::constants::kTrajectoryHorizonInSec
              : 0.0;
      debug_oss << "extra_distance_for_source_region_agent["
                << extra_distance_for_source_region_agent << "m], agent_id["
                << source_object_ids.front() << "], agent_speed["
                << closest_source_region_agent_speed << "m/s];";
    }
  }

  const double required_remaining_distance =
      extra_distance_for_source_region_agent > math::constants::kEpsilon
          ? std::max(comfortable_lane_change_distance * 0.5 +
                         extra_distance_for_source_region_agent,
                     comfortable_lane_change_distance)
          : comfortable_lane_change_distance;

  // Don't block LC when the remaining distance is not enough.
  if (remaining_distance_per_lc < required_remaining_distance) {
    debug_oss << "\nNot enough LC distance; remaining_distance_per_lc ["
              << remaining_distance_per_lc
              << "m] < required_remaining_distance["
              << required_remaining_distance << "]; "
              << DUMP_TO_STREAM(comfortable_lane_change_distance,
                                extra_distance_for_source_region_agent)
              << "\n";
    return false;
  }

  debug_oss << "\nEnough LC distance; remaining_distance_per_lc ["
            << remaining_distance_per_lc << "m] >= required_remaining_distance["
            << required_remaining_distance << "]; "
            << DUMP_TO_STREAM(comfortable_lane_change_distance,
                              extra_distance_for_source_region_agent)
            << "\n";

  return IsRiskyLaneChangeAtHighDiscomfort(
      speed_discomfort, ego_speed, kComfortableLaneChangeDurationInSec * 0.5,
      ego_ra_to_rb, target_region_info_ptr, debug_oss);
}

bool IsRiskyELC(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const planner::pb::LaneChangeSignalSourceType&
        lane_change_signal_source_type,
    const int64_t elc_triggered_object_id, const double speed_discomfort) {
  // Consider the progress elc to be risky when it is not triggered for bus and
  // the speed discomfort on CL is greater than 0.0.
  // TODO(pengfei, zhaorui): Only exclude the scenario where the bus is entering
  // the bus station.
  return lane_change_signal_source_type ==
             planner::pb::LaneChangeSignalSourceType::
                 kLaneChangeSignalFromProgressELC &&
         elc_triggered_object_id != kInvalidObjectId &&
         planner_object_map.find(elc_triggered_object_id) !=
             planner_object_map.end() &&
         !planner_object_map.at(elc_triggered_object_id).is_bus() &&
         speed_discomfort >
             speed::Discomforts::kMid + math::constants::kEpsilon;
}

bool IsRiskyLaneChangeInFork(const bool is_current_lane_change_in_fork,
                             const double speed_discomfort) {
  return is_current_lane_change_in_fork &&
         speed_discomfort > speed::Discomforts::kMax -
                                speed::Discomforts::kHighLevelRes -
                                math::constants::kEpsilon;
}

bool IsRiskyLaneChangeOnHighway(const CrossLaneInfo& cross_lane_info,
                                const double speed_discomfort) {
  if (!FLAGS_planning_enable_driving_on_freeway ||
      cross_lane_info.lane_change_instance.source_lane()
              .section()
              ->road()
              ->road_class() != hdmap::Road::EXPRESSWAY) {
    return false;
  }

  constexpr double kMinRemainingDistanceForSingleLcOnHighwayInMeter = 500.0;

  const CrossLaneMetaData& cross_lane_meta = cross_lane_info.cross_lane_meta;
  const double remaining_distance_per_lc =
      (cross_lane_meta.has_dist_to_cross_lane_last_chance_pt()
           ? cross_lane_meta.dist_to_cross_lane_last_chance_pt()
           : cross_lane_meta.remaining_distance_for_cross_lane()) /
      cross_lane_meta.cross_lane_count();

  return speed_discomfort >
             speed::Discomforts::kMid + math::constants::kEpsilon &&
         remaining_distance_per_lc >
             kMinRemainingDistanceForSingleLcOnHighwayInMeter;
}

bool IsCutinAgentRiskyByRelativeStatesOnTargetNeighborRegion(
    const RobotState& robot_state,
    const CrossLaneInfoManager& cross_lane_info_manager,
    const int64_t object_id, std::ostringstream& debug_oss) {
  // NOTE: The input agent is a cut-in agent.
  // Check relative speed and distance of the agent and ego to see if there may
  // be risk in the near future when we both keep the current motion trends.
  const CrossLaneMetaData& cross_lane_meta =
      cross_lane_info_manager.GetCrossLaneInfo().cross_lane_meta;
  const CrossLaneObjectInfo* object_info = DCHECK_NOTNULL(
      DCHECK_NOTNULL(cross_lane_info_manager.GetTargetRegionObjectInfoListPtr())
          ->Find(object_id));

  // Check lateral ttc.
  constexpr double kLateralSafeDistanceInMeter = 0.2;
  constexpr double kLongitudinalSafeDistanceInMeter = 3.0;
  constexpr double kMaxInteractionDurationToCheckInSec = 3.0;
  const double object_ego_lateral_distance = abs(
      object_info->agent_closest_corner_signed_lat_dist_to_cross_lane_curve -
      cross_lane_meta.ego_corner_signed_lat_dist_to_cross_lane_curve());
  const double object_ego_lateral_speed =
      abs(object_info->agent_signed_lat_speed_wrt_cross_lane_curve -
          cross_lane_meta.ego_signed_lat_speed_wrt_cross_lane_curve());
  const double lateral_ttc =
      (object_ego_lateral_distance - kLateralSafeDistanceInMeter) /
      std::max(object_ego_lateral_speed, math::constants::kEpsilon);
  const auto& ego_shape =
      robot_state.car_model_with_shape().shape_measurement();
  const double current_object_rb_to_ego_fb_dist =
      object_info->object_ego_dist -
      object_info->planner_object().length() * 0.5 - ego_shape.length() +
      ego_shape.rear_bumper_to_rear_axle();
  const double future_object_rb_to_ego_fb_dist =
      current_object_rb_to_ego_fb_dist +
      (object_info->planner_object().speed() -
       robot_state.plan_init_state_snapshot().speed()) *
          std::min(lateral_ttc, kMaxInteractionDurationToCheckInSec);
  debug_oss << "\n"
            << DUMP_TO_STREAM(lateral_ttc, current_object_rb_to_ego_fb_dist,
                              future_object_rb_to_ego_fb_dist)
            << ";";

  return future_object_rb_to_ego_fb_dist < kLongitudinalSafeDistanceInMeter;
}

double ComputeLcCompletionRate(const RobotState& ego,
                               const LaneChangeInstance& lane_change_instance) {
  constexpr double kLaneChangeMaxCompletionRate = 1.0;
  if (lane_change_instance.direction() ==
      planner::pb::LaneChangeMode::NONE_LANE_CHANGE) {
    return kLaneChangeMaxCompletionRate;
  }

  // Checks whether ego's center of rear axel has entered into the target
  // region, if yes, regard lane change completion rate as the max one.
  const bool is_left_lane_change =
      lane_change_instance.direction() ==
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE;
  const math::geometry::PolylineCurve2d& boundary =
      is_left_lane_change
          ? lane_change_instance.target_region().right_boundary()
          : lane_change_instance.target_region().left_boundary();
  const int direction_sign = is_left_lane_change ? -1 : 1;
  const double ra_center_signed_distance =
      boundary
          .GetProximity({ego.plan_init_state_snapshot().x(),
                         ego.plan_init_state_snapshot().y()},
                        math::pb::UseExtensionFlag::kForbid)
          .signed_dist *
      direction_sign;
  if (ra_center_signed_distance < 0.0) {
    return kLaneChangeMaxCompletionRate;
  }

  // Computes lane change completion rate by the front corner encroachment into
  // the target region.
  const double front_corner_distance =
      lane_change_instance.CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
          ego.plan_init_state_snapshot());
  return front_corner_distance > 0.0
             ? 0.0
             : math::GetLinearInterpolatedY(0.0, ego.GetWidth(), 0.0,
                                            kLaneChangeMaxCompletionRate,
                                            std::fabs(front_corner_distance));
}

// Returns if ego has shown lane change intention by judging if any of the
// following is true:
// - Ego heads toward the target lane.
// - Ego has changed some heading from its first pose.
// - Ego's bbox intersects with target lane or its successors' borders.
bool EgoHasShownLcIntention(
    const vehicle_model::pb::AxleRectangularMeasurement& ego_shape,
    const planner::pb::TrajectoryPose& ego_pose,
    const lane_selection::LaneSequenceGeometry* lf_lane_seq_geometry,
    const pnc_map::Lane& target_lane, const TrajectoryMetaData& candidate) {
  const math::geometry::Point2d ego_center(ego_pose.x_pos(), ego_pose.y_pos());
  const math::geometry::OrientedBox2d ego_bbox(
      ego_center, ego_shape.length(), ego_shape.width(), ego_pose.heading());
  const std::vector<const pnc_map::Lane*>& target_successors =
      target_lane.successors();
  bool head_deviation_exceed_thres = false;
  if (lf_lane_seq_geometry != nullptr) {
    const math::ProximityQueryInfo proximity =
        lf_lane_seq_geometry->nominal_path.GetProximity(
            ego_center, math::pb::UseExtensionFlag::kForbid);
    if (proximity.closest_segment_index < 0 ||
        proximity.closest_segment_index >=
            lf_lane_seq_geometry->nominal_path.num_segments()) {
      return true;
    }
    const math::geometry::Point2d proj_segment_dir =
        lf_lane_seq_geometry->nominal_path.GetSegmentDirection(
            proximity.closest_segment_index);
    const double head_deviation =
        math::AngleDiff(ego_pose.heading(),
                        std::atan2(proj_segment_dir.y(), proj_segment_dir.x()));
    head_deviation_exceed_thres = std::abs(head_deviation) > kLcHeadDeviThres;
  }
  const double head_diff = math::AngleDiff(
      ego_pose.heading(), candidate.trajectory.poses(0).heading());
  const bool show_init_lc_intent =
      head_deviation_exceed_thres || std::abs(head_diff) > kLcHeadDiffThres ||
      math::geometry::Intersects(ego_bbox, target_lane.border()) ||
      std::any_of(target_successors.begin(), target_successors.end(),
                  [&ego_bbox](const auto* lane) {
                    return math::geometry::Intersects(
                        ego_bbox, DCHECK_NOTNULL(lane)->border());
                  });
  return show_init_lc_intent;
}

bool IsLcGapObsolete(const speed::pb::SpeedSeed& speed_seed,
                     const TrajectoryMetaData& candidate,
                     std::ostringstream& debug_oss) {
  if (speed_seed.lc_guide_seed().empty()) {
    debug_oss << "\nEmpty lane change guide seed; ";
    return false;
  }

  // Finds if there is a yield constraint on the tail object.
  const int64_t tail_obj_id = speed_seed.lc_guide_seed(0).tail_obj_id();
  debug_oss << "\nLC with tail object " << tail_obj_id;

  const speed::SpeedResult& speed_result = candidate.speed_result;
  DCHECK_EQ(speed_result.constraints().size(), speed_result.decisions().size());

  const auto iter = std::find_if(
      speed_result.constraints().begin(), speed_result.constraints().end(),
      [tail_obj_id](const speed::Constraint& constraint) {
        return IsSpeedObjectConstraint(constraint) &&
               constraint.obj_id == tail_obj_id;
      });
  if (iter == speed_result.constraints().end()) {
    return false;
  }
  return speed_result.decisions().at(
             std::distance(speed_result.constraints().begin(), iter)) ==
         speed::pb::SpeedDecision::YIELD;
}

bool HasYieldDecision(const speed::SpeedResult& speed_result,
                      const int64_t object_id,
                      const std::string& unique_primary_traj_id) {
  DCHECK_EQ(speed_result.constraints().size(), speed_result.decisions().size());
  for (size_t idx = 0; idx < speed_result.constraints().size(); ++idx) {
    const speed::Constraint& constraint = speed_result.constraints()[idx];
    if (constraint.obj_id == object_id &&
        speed_result.decisions()[idx] == speed::pb::SpeedDecision::YIELD) {
      if (unique_primary_traj_id.empty() ||
          unique_primary_traj_id == constraint.unique_pred_traj_id) {
        return true;
      }
    }
  }
  return false;
}

bool HasYieldDecision(
    const speed::SpeedResult& speed_result, const int64_t object_id,
    const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories,
    const bool only_check_primary_decision) {
  std::string unique_primary_traj_id;
  if (only_check_primary_decision) {
    const auto& primary_trajectory = std::find_if(
        predicted_trajectories.begin(), predicted_trajectories.end(),
        [](const PredictedTrajectoryWrapper& predicted_trajectory) {
          return predicted_trajectory.is_primary_trajectory();
        });
    DCHECK(primary_trajectory != predicted_trajectories.end());
    unique_primary_traj_id = GetBpUniqueId(object_id, primary_trajectory->id());
  }

  return HasYieldDecision(speed_result, object_id, unique_primary_traj_id);
}

double GetMinOrMaxAccelerationInTimeRange(
    const planner::pb::Trajectory& trajectory, const double max_time_range,
    const bool update_min) {
  // Compute the min or max acceleration in the first few seconds
  // (max_time_range) on the trajectory.
  DCHECK_GT(trajectory.poses_size(), 0);
  double target_acceleration = trajectory.poses(0).accel();
  for (int i = 0; i < trajectory.poses_size(); i++) {
    const planner::pb::TrajectoryPose& pose = trajectory.poses(i);
    if (pose.timestamp() - trajectory.plan_init_timestamp() > max_time_range) {
      break;
    }
    update_min ? math::UpdateMin(pose.accel(), target_acceleration)
               : math::UpdateMax(pose.accel(), target_acceleration);
  }
  return target_acceleration;
}

void UpdateCrossLaneSeed(
    const std::map<std::string, TrajectoryMetaData*>& candidates,
    const TrajectoryMetaData* selected_trajectory,
    const planner::pb::SelectionSeed& prev_selection_seed,
    const bool enable_lane_change_scenario,
    planner::pb::SelectionSeed* selection_seed) {
  DCHECK_NE(selection_seed, nullptr);

  planner::cross_lane::pb::CrossLaneSelectionSeed* cross_lane_selection_seed =
      selection_seed->mutable_cross_lane_selection_seed();

  // Reset seed for non lane change scenario.
  if (!enable_lane_change_scenario) {
    cross_lane_selection_seed->set_is_cl_blocking_traffic(false);
  }

  const bool in_simulation_or_auto_mode =
      av_comm::InSimulation() ||
      av_comm::IsAutonomyControlMode(
          Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame())
              ->last_vehicle_mode());
  const bool cl_available_and_ready =
      in_simulation_or_auto_mode &&
      std::any_of(candidates.begin(), candidates.end(), [=](const auto& kv) {
        const TrajectoryMetaData& candidate = *kv.second;
        return candidate.is_safe && candidate.IsCrossLane() &&
               candidate.lane_change_selection_info.is_cross_lane_safe;
      });
  if (cl_available_and_ready) {
    selection_seed->set_consecutive_lc_ready_cycles(
        prev_selection_seed.consecutive_lc_ready_cycles() + 1);
    cross_lane_selection_seed->set_consecutive_cl_ready_cycles(
        (prev_selection_seed.has_cross_lane_selection_seed()
             ? prev_selection_seed.cross_lane_selection_seed()
                   .consecutive_cl_ready_cycles()
             : prev_selection_seed.consecutive_lc_ready_cycles()) +
        1);
  } else {
    selection_seed->set_consecutive_lc_ready_cycles(0);
    cross_lane_selection_seed->set_consecutive_cl_ready_cycles(0);
  }

  // Update cross lane risks.
  auto update_min_risk_in_lc = [&candidates](double& min_pose_collision_risk,
                                             double& min_lc_risk) -> bool {
    bool updated = false;
    for (const auto& [id, candidate] : candidates) {
      if (!candidate->is_safe || !candidate->IsCrossLane()) {
        continue;
      }
      math::UpdateMin(candidate->pose_collision_risk, min_pose_collision_risk);
      math::UpdateMin(
          candidate->lane_change_selection_info.lc_risk_info.lc_risk,
          min_lc_risk);
      updated = true;
    }
    return updated;
  };

  double min_pose_collision_risk = std::numeric_limits<double>::infinity();
  double min_lc_risk = std::numeric_limits<double>::infinity();
  if (update_min_risk_in_lc(min_pose_collision_risk, min_lc_risk)) {
    cross_lane_selection_seed->set_pose_collision_risk(min_pose_collision_risk);
    cross_lane_selection_seed->set_cl_risk(min_lc_risk);
  } else {
    cross_lane_selection_seed->clear_pose_collision_risk();
    cross_lane_selection_seed->clear_cl_risk();
  }

  // Update seed related to the selected trajectory info.
  UpdateSelectedLaneChangeGapInfo(
      candidates,
      prev_selection_seed.lane_change_selection_seed()
          .selected_lane_change_gap_info(),
      selection_seed->mutable_lane_change_selection_seed()
          ->mutable_selected_lane_change_gap_info());

  if (selected_trajectory == nullptr || !selected_trajectory->IsCrossLane()) {
    selection_seed->set_consecutive_last_cl_cycles(0);
    cross_lane_selection_seed->set_consecutive_last_cl_cycles(0);
    cross_lane_selection_seed->set_consecutive_risky_cl_cycles(0);
    return;
  }

  // Update consecutive last CL cycles.
  if (!selected_trajectory->IsLastTrajectory()) {
    selection_seed->set_consecutive_last_cl_cycles(0);
    cross_lane_selection_seed->set_consecutive_last_cl_cycles(0);
  } else {
    selection_seed->set_consecutive_last_cl_cycles(
        prev_selection_seed.consecutive_last_cl_cycles() + 1);
    // The extra computation logic to check has_cross_lane_selection_seed here
    // is for simulation reproducibility on old bags.
    cross_lane_selection_seed->set_consecutive_last_cl_cycles(
        (prev_selection_seed.has_cross_lane_selection_seed()
             ? prev_selection_seed.cross_lane_selection_seed()
                   .consecutive_last_cl_cycles()
             : prev_selection_seed.consecutive_last_cl_cycles()) +
        1);
  }

  // Update consecutive risky CL cycles.
  if (selected_trajectory->is_risky_cross_lane) {
    cross_lane_selection_seed->set_consecutive_risky_cl_cycles(
        prev_selection_seed.cross_lane_selection_seed()
            .consecutive_risky_cl_cycles() +
        1);
  } else {
    cross_lane_selection_seed->set_consecutive_risky_cl_cycles(0);
  }
}

bool IsExcessiveBlockageAvoidanceLc(
    const CrossLaneInfo& cross_lane_info,
    const planner::pb::LaneChangeInfoSeed& previous_lane_change_info_seed,
    std::ostringstream& debug_oss) {
  // Check if the current lane change is for blockage avoidance.
  const auto& urgency_infos_in_seed =
      previous_lane_change_info_seed.urgency_info_map();
  const bool has_blockage_urgency_info =
      urgency_infos_in_seed.contains(planner::pb::LaneChangeUrgencyType_Name(
          planner::pb::LaneChangeUrgencyType::BLOCKAGE_ON_SOURCE_REGION));
  const bool is_stuck_avoidance_lc =
      previous_lane_change_info_seed.lane_change_sequence_type() ==
      planner::pb::LaneSequenceCandidate::STUCK_AVOIDANCE_LANE_CHANGE;
  debug_oss << DUMP_TO_STREAM(has_blockage_urgency_info, is_stuck_avoidance_lc)
            << "\n";
  if (!has_blockage_urgency_info && !is_stuck_avoidance_lc) {
    return false;
  }
  // If there isn't blockage on source region before in-progress state or
  // still has source blockage now. We don't want to recompute to try to
  // abort current lane change.
  const bool has_source_blockage_non_in_progress_from_seed =
      previous_lane_change_info_seed
          .has_source_blockage_start_arclength_before_in_progress();
  const bool has_source_blockage_from_lane_change_info =
      cross_lane_info.source_blockage_start_arclength.has_value();
  // We consider current lane change is excessive when there is source blockage
  // non in-progress but now there isn't.
  const bool is_excessive_blockage_avoidance_lc =
      has_source_blockage_non_in_progress_from_seed &&
      !has_source_blockage_from_lane_change_info;
  debug_oss << DUMP_TO_STREAM(has_source_blockage_non_in_progress_from_seed,
                              has_source_blockage_from_lane_change_info)
            << "\n";
  return is_excessive_blockage_avoidance_lc;
}

}  // namespace selection
}  // namespace planner
