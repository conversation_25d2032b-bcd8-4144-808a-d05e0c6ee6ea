#include "scan_convert/multi_scan_message_manager.h"

#include "av_comm/mode_config.h"
#include "av_comm/sensor_util.h"
#include "rate_limited_log/rate_limited_log.h"

namespace lidar {

MultiScanMessageManager::~MultiScanMessageManager() {}

/////// WholeMultiScanMessageManager::ScanCompletionTracker ///////

namespace {
constexpr int kMaxTimestampGap = 50;  // ms

lidar::PointCloudMessage& GetPointCloudRef(PclMsgAdpatorUptr& adaptor) {
  auto* ptr = reinterpret_cast<PointCloudMessageAdaptor*>(adaptor.get());
  return ptr->point_cloud_;
}
}  // namespace

WholeMultiScanMessageManager::ScanSyncTracker::ScanSyncTracker(
    int expected_count, int timeout_ms /*  = 50 */)
    : expected_count_(expected_count), timeout_ms_(timeout_ms) {
  LOG(INFO) << "ScanSyncTracker initialized with expected_count="
            << expected_count << ", timeout=" << timeout_ms;
}

void WholeMultiScanMessageManager::ScanSyncTracker::InitIfNeeded() {
  if (start_time_ == 0) {
    start_time_ = base::Now();
  }
}

WholeMultiScanMessageManager::ScanSyncTracker::Status
WholeMultiScanMessageManager::ScanSyncTracker::AddScan(
    voy::Sensor::SensorType sensor_type, int64_t timestamp) {
  InitIfNeeded();

  // Check timestamp difference
  if (!scan_time_map_.empty() &&
      std::abs(timestamp - min_timestamp_) > kMaxTimestampGap) {
    LOG(ERROR) << "Timestamp out-of-sync: " << timestamp
               << ", min=" << min_timestamp_ << ", " << kMaxTimestampGap;
    for (const auto& [sensor, ts] : scan_time_map_) {
      LOG(INFO) << "Current [" << static_cast<int>(sensor) << "] ts=" << ts;
    }
    ClearState();
    InitIfNeeded();
    scan_time_map_[sensor_type] = timestamp;
    min_timestamp_ = timestamp;
    return Status::EXPIRED;
  }

  // Duplicate check
  auto it = scan_time_map_.find(sensor_type);
  if (it != scan_time_map_.end()) {
    LOG(WARNING) << "Repeated scan for sensor type " << sensor_type
                 << " at timestamp " << timestamp;

    for (auto& pair : scan_time_map_) {
      LOG(INFO) << "Scan timestamp for sensor type " << pair.first << " is "
                << pair.second;
    }
  }

  scan_time_map_[sensor_type] = timestamp;
  min_timestamp_ =
      (min_timestamp_ == 0) ? timestamp : std::min(min_timestamp_, timestamp);

  return IsComplete() ? Status::COMPLETE : Status::INCOMPLETE;
}

bool WholeMultiScanMessageManager::ScanSyncTracker::IsComplete() const {
  return static_cast<int>(scan_time_map_.size()) == expected_count_;
}

bool WholeMultiScanMessageManager::ScanSyncTracker::IsTimedOut() const {
  if (av_comm::InSimulation()) return false;
  if (start_time_ == 0) return false;
  int64_t elapsed = base::Now() - start_time_;
  if (elapsed > timeout_ms_) {
    LOG(ERROR) << "Scan timeout: elapsed=" << elapsed
               << "ms, timeout=" << timeout_ms_ << "ms, " << start_time_
               << "ms";
    for (auto& pair : scan_time_map_) {
      LOG(INFO) << "Scan timestamp for sensor type " << pair.first << " is "
                << pair.second;
    }
    return true;
  }
  return false;
}

void WholeMultiScanMessageManager::ScanSyncTracker::ClearState() {
  scan_time_map_.clear();
  min_timestamp_ = 0;
  start_time_ = 0;
}

/////////////////// WholeMultiScanMessageManager ///////////////////
WholeMultiScanMessageManager::WholeMultiScanMessageManager(int expected_scans,
                                                           int timeout_ms)
    : tracker_(expected_scans, timeout_ms) {}

WholeMultiScanMessageManager::~WholeMultiScanMessageManager() {}

PclMsgAdpatorUptr WholeMultiScanMessageManager::NewElem(voy::Sensor::SensorType,
                                                        bool) {
  return std::make_unique<PointCloudMessageAdaptor>();
}

bool WholeMultiScanMessageManager::PushBack(PclMsgAdpatorUptr&& adaptor,
                                            bool is_surrounding_point) {
  if (!is_surrounding_point) {
    if (tracker_.IsComplete() || tracker_.IsTimedOut()) {
      ResetAll();
    }
    auto status =
        tracker_.AddScan(adaptor->GetLidarType(), adaptor->GetEndTimestamp());

    if (status == ScanSyncTracker::Status::EXPIRED) {
      LOG(ERROR) << "Out-of-sync scan, resetting tracker."
                 << adaptor->GetLidarType() << " at timestamp "
                 << adaptor->GetEndTimestamp();
      Reset();
    }
  }
  auto& merged_scan_ref =
      is_surrounding_point ? merged_surrounding_scan_ : merged_scan_;
  merged_scan_ref.PushBack(std::move(GetPointCloudRef(adaptor)));
  return tracker_.IsComplete();
}

std::optional<ScanMessage> WholeMultiScanMessageManager::GetMergedScan(
    bool is_surrounding_point) {
  if (tracker_.IsComplete() || tracker_.IsTimedOut()) {
    return is_surrounding_point ? merged_surrounding_scan_ : merged_scan_;
  }
  return std::nullopt;
}

void WholeMultiScanMessageManager::ResetAll() {
  Reset();
  tracker_.ClearState();
}

void WholeMultiScanMessageManager::Reset() {
  merged_scan_.Clear();
  merged_surrounding_scan_.Clear();
}

/////////////////// LazySliceMultiScanMessageManager ///////////////////

LazySliceMultiScanMessageManager::LazySliceMultiScanMessageManager() {}

LazySliceMultiScanMessageManager::~LazySliceMultiScanMessageManager() {}

PclMsgAdpatorUptr LazySliceMultiScanMessageManager::NewElem(
    voy::Sensor::SensorType, bool is_surrounding_point) {
  return is_surrounding_point ? std::make_unique<PointCloudMessageAdaptor>(
                                    std::move(converted_surrounding_scan_))
                              : std::make_unique<PointCloudMessageAdaptor>(
                                    std::move(converted_scan_));
}

bool LazySliceMultiScanMessageManager::PushBack(PclMsgAdpatorUptr&& adaptor,
                                                bool is_surrounding_point) {
  if (!full_scan_checker_) {
    LOG(FATAL) << "FullScanChecker is Lazily initialized, SetNumScans shoule "
                  "be called first";
    return false;
  }

  if (!is_surrounding_point) {
    if (full_scan_checker_->Check(adaptor->GetScanId(),
                                  adaptor->GetSliceId()) !=
        FullScanChecker::ScanStatus::SLICE_IN_CORRECT_ORDER) {
      LOG(ERROR) << "Received out-of-order scan for sensor type "
                 << adaptor->GetLidarType() << " at timestamp "
                 << adaptor->GetEndTimestamp();
      Reset();
    }
  }

  if (full_scan_checker_->IsFullScanReady()) {
    adaptor->SetStartTimestamp(0);
    adaptor->SetSliceId(0);
    adaptor->SetScanId(0);
    adaptor->SetNumSlices(0);
    if (is_surrounding_point) {
      merged_surrounding_scan_.PushBack(std::move(GetPointCloudRef(adaptor)));
      converted_surrounding_scan_.Reset();
    } else {
      merged_scan_.PushBack(std::move(GetPointCloudRef(adaptor)));
      converted_scan_.Reset();
    }
  } else {
    auto& pcl =
        is_surrounding_point ? converted_surrounding_scan_ : converted_scan_;
    pcl = std::move(GetPointCloudRef(adaptor));
  }

  return full_scan_checker_->IsFullScanReady();
}

std::optional<ScanMessage> LazySliceMultiScanMessageManager::GetMergedScan(
    bool is_surrounding_point) {
  if ((!full_scan_checker_) || (!full_scan_checker_->IsFullScanReady())) {
    return std::nullopt;
  }
  return is_surrounding_point ? std::move(merged_surrounding_scan_)
                              : std::move(merged_scan_);
}

void LazySliceMultiScanMessageManager::SetNumScans(int total_num_scans) {
  if (full_scan_checker_ == nullptr && total_num_scans > 0) {
    full_scan_checker_ = std::make_unique<FullScanChecker>(total_num_scans);
  }
}

void LazySliceMultiScanMessageManager::Reset() {
  merged_scan_.Clear();
  merged_surrounding_scan_.Clear();
  converted_scan_.Reset();
  converted_surrounding_scan_.Reset();
}

/////////////////// SeparateMultiScanMessageManager ///////////////////

SeparateMultiScanMessageManager::SeparateMultiScanMessageManager(
    const std::vector<voy::Sensor::SensorType>& sensor_types, int buffer_size) {
  // Create a history buffer for each sensor type
  for (const auto& sensor_type : sensor_types) {
    point_cloud_message_history_buffers_[sensor_type] =
        std::make_unique<av_comm::HistoryBuffer<int64_t, PointCloudMessage>>(
            buffer_size);
    surrounding_point_cloud_message_history_buffers_[sensor_type] =
        std::make_unique<av_comm::HistoryBuffer<int64_t, PointCloudMessage>>(
            buffer_size);
  }
}

SeparateMultiScanMessageManager::~SeparateMultiScanMessageManager() {}

PclMsgAdpatorUptr SeparateMultiScanMessageManager::NewElem(
    voy::Sensor::SensorType, bool) {
  return std::make_unique<PointCloudMessageAdaptor>();
}

bool SeparateMultiScanMessageManager::PushBack(PclMsgAdpatorUptr&& adaptor,
                                               bool is_surrounding_point) {
  PointCloudHistoryBuffer& buffers =
      is_surrounding_point ? (surrounding_point_cloud_message_history_buffers_)
                           : (point_cloud_message_history_buffers_);
  auto& point_cloud = GetPointCloudRef(adaptor);
  buffers[point_cloud.lidar_type]->EmplaceBack(point_cloud.timestamp,
                                               std::move(point_cloud));
  return true;
}

std::vector<PointCloudMessage>
SeparateMultiScanMessageManager::GetPointCloudMessageByTimestamp(
    int64_t scan_timestamp, bool is_surrounding_point, int max_diff_timestamp) {
  PointCloudHistoryBuffer& history_buffers =
      is_surrounding_point ? (surrounding_point_cloud_message_history_buffers_)
                           : (point_cloud_message_history_buffers_);
  std::vector<PointCloudMessage> point_cloud_msg_vec;
  for (const auto& [sensor_type, point_cloud_buffer] : history_buffers) {
    auto iter = point_cloud_buffer->FindClosestDataWithin(scan_timestamp,
                                                          max_diff_timestamp);
    if (iter == point_cloud_buffer->end()) {
      if (point_cloud_buffer->empty()) {
        // There is no need to output warning logs. LIDAR_16_0 and LIDAR_16_1
        // are sensors of the Gen2 platform and have historical compatibility
        // issues, which need to be implemented in the Gen3 logic.
        if (av_comm::IsGen3Platform() &&
            (sensor_type == voy::Sensor::LIDAR_16_0 ||
             sensor_type == voy::Sensor::LIDAR_16_1)) {
          continue;
        }
        LOG_EVERY_N(ERROR, 10)
            << av_comm::GetLidarComponent(sensor_type)
            << " No point cloud message within " << max_diff_timestamp << "ms.";
        continue;
      }
      LOG_EVERY_N(ERROR, 10)
          << av_comm::GetLidarComponent(sensor_type)
          << " Not found point cloud message within " << max_diff_timestamp
          << "ms."
          << " Earliest timestamp: " << point_cloud_buffer->earliest_timestamp()
          << " Latest timestamp: " << point_cloud_buffer->latest_timestamp()
          << " scan_timestamp: " << scan_timestamp
          << " Sensor type: " << sensor_type;
      continue;
    }

    const int matched_diff_time = 25;
    if (std::abs(scan_timestamp - point_cloud_buffer->latest_timestamp()) <
        matched_diff_time) {
      LOG_EVERY_N(INFO, 400)
          << av_comm::GetLidarComponent(sensor_type)
          << " Matched nearly. within the " << matched_diff_time << "ms."
          << " mt: " << scan_timestamp
          << " st: " << point_cloud_buffer->latest_timestamp() << " ("
          << point_cloud_buffer->latest_timestamp() - scan_timestamp << ")";
    } else {
      LOG_EVERY_N(INFO, 400)
          << av_comm::GetLidarComponent(sensor_type)
          << " Matched barely. outside the " << matched_diff_time << "ms."
          << " mt: " << scan_timestamp
          << " st: " << point_cloud_buffer->latest_timestamp() << " ("
          << point_cloud_buffer->latest_timestamp() - scan_timestamp << ")";
    }

    point_cloud_msg_vec.push_back(iter->second);
  }
  return point_cloud_msg_vec;
}

/////////////////// DefaultMultiScanMessageManager ///////////////////

DefaultMultiScanMessageManager::DefaultMultiScanMessageManager() {}
DefaultMultiScanMessageManager::~DefaultMultiScanMessageManager() {}

PclMsgAdpatorUptr DefaultMultiScanMessageManager::NewElem(
    voy::Sensor::SensorType, bool) {
  return std::make_unique<PointCloudMessageAdaptor>();
}

bool DefaultMultiScanMessageManager::PushBack(PclMsgAdpatorUptr&& adaptor,
                                              bool is_surrounding_point) {
  PointCloudMap& msg_map = is_surrounding_point
                               ? (surrounding_point_cloud_msg_map_)
                               : (point_cloud_msg_map_);
  auto& point_cloud = GetPointCloudRef(adaptor);
  msg_map[point_cloud.lidar_type] = std::move(point_cloud);
  return true;
}

std::map<voy::Sensor::SensorType, PointCloudMessage>
DefaultMultiScanMessageManager::GetPcls(bool is_surrounding_point) {
  return is_surrounding_point ? std::move(surrounding_point_cloud_msg_map_)
                              : std::move(point_cloud_msg_map_);
}

}  // namespace lidar
