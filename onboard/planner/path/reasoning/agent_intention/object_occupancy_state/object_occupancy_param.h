#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_PARAM_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_PARAM_H_

#include <limits>

#include "geometry/model/point_2d.h"
#include "math/range.h"
#include "voy_protos/math.pb.h"

namespace planner {

struct ObjectOccupancyParam {
  // Along track arc length to the nearer end of object's occupancy part.
  // Considers only the part within the corridor.
  double snapshot_start_arclength_m = 0.0;
  // Along track arc length to the further end of object's occupancy part.
  double snapshot_end_arclength_m = 0.0;
  // Along track arc length to the nearer end of object's entire contour.
  double full_body_start_arclength_m = 0.0;
  // Along track arc length to the further end of object's entire contour.
  double full_body_end_arclength_m = 0.0;
  // Rightmost lateral distance to center line of object's entire contour.
  double full_body_start_lateral_distance_m = 0.0;
  // Leftmost lateral distance to center line of object's entire contour.
  double full_body_end_lateral_distance_m = 0.0;
  // Object's relative occupancy clearance with side information.
  // If the object's contour is within the corridor, the respective clearance
  // value is the closest distance to the boundary of the contour. If the
  // object's contour is on the boundary, the respective clearance value is 0.
  // If the object's contour is out of the boundary, both of the left and right
  // clearances are equal to the min_corridor_width_m.
  double left_boundary_clearance_m = 0.0;
  double right_boundary_clearance_m = 0.0;
  // These two fields are currently not used.
  double left_boundary_allowed_encroachment = 0.0;
  double right_boundary_allowed_encroachment = 0.0;
  // Object's closest distance to the center line. If the object is on the
  // center line, the clearance is 0.
  double center_line_clearance_m = 0.0;
  // Object's signed distance to center line range, negative value means the
  // object's is on the right side of the center line and positive value means
  // left side.
  math::Range1d dist_to_center_line_range{};
  // Object's side relative to the center line.
  math::pb::Side center_line_side = math::pb::kOn;
  // Object's boundary corridor encroachment.
  // If the object is within the corridor, this value is the maximum corridor
  // encroachment of the contour.
  // If the object is on the center line, this value is equal to the
  // min_corridor_width_m. If the object is out of the boundary, this value is
  // equal to the opposite of the minimum clearance to the corridor.
  double cross_track_encroachment_m = 0.0;

  // Object's heading relative to the center line.
  double relative_heading_rad = 0.0;
  // The speed in the direction of the nominal curve
  double along_track_speed_mps = 0.0;
  // The speed in the direction that is perpendicular to the nominal curve.
  double cross_track_speed_mps = 0.0;

  // Minimum width of the corridor projected by agent snapshot.
  double min_corridor_width_m = 0.0;

  // Max left/right boundary encroachment of the object's contour. Negative
  // values means encroachment value and positive value means no encroachment.
  double max_left_violation = std::numeric_limits<double>::lowest();
  double max_right_violation = std::numeric_limits<double>::lowest();
  // Flag indicates if the snapshot is fully within the corridor.
  bool is_fully_within_corridor = false;

  // Object's speed.
  double speed_mps = 0.0;
  // Object's acceleration.
  double acceleration_mpss = 0.0;
  // Object's velocity, a directed vector (optional). It should equal to
  // speed_mps * pose.direction() if speed_mps is not zero, otherwise nullopt
  std::optional<math::geometry::Point2d> velocity = std::nullopt;
  // Object's position is fully inside left lane marking.
  bool is_fully_inside_left_boundary = true;
  // Object's position is fully inside right lane marking.
  bool is_fully_inside_right_boundary = true;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_OBJECT_OCCUPANCY_STATE_OBJECT_OCCUPANCY_PARAM_H_
