syntax = "proto3";

package planner.pb;

import "planner_protos/directive.proto";
import "planner_protos/exception_handler.proto";
import "voy_protos/trajectory.proto";
import "voy_protos/polyline.proto";

// Type of trajectory token level.
enum TrajectoryTokenLevel {
  // no trigger EH.
  NO_TRAJECTORY = 0;
  // soft brake trajectory.
  SOFT_BRAKE_TRAJECTORY = 1;
  // emergency brake trajectory.
  EMERGENCY_BRAKE_TRAJECTORY = 2;
  // swerve+emergency brake trajectory.
  SWERVE_EMERGENCY_BRAKE_TRAJECTORY = 3;
};

// The trajectory token seed.
// Next Available ID: 5
message TrajectoryTokenSeed {
  // Trajectory token.
  Trajectory trajectory_token = 1;

  // Exception handler nodelet token.
  TrajectoryTokenLevel exception_handler_nodelet_token = 2;

  // Exception handler for perception nodelet token.
  TrajectoryTokenLevel exception_handler_for_perception_nodelet_token = 3;

  // Global_selection token.
  TrajectoryTokenLevel global_selection_token = 4;
}

// The ttc detect result seed.
// Next Available ID: 5
message TtcDetectResultSeed {
  // The exception start timestamp.
  int64 exception_start_timestamp = 1;

  // The ttc detect result last cycle.
  TtcDetectResult ttc_detect_result = 2;

  // The ttc detect result of most recent last cycle.
  TtcDetectResult ttc_detect_result_updated = 3;

  // The ttc detect result type of most recent last cycle.
  EHTriggerType last_ttc_detect_result_type = 4;
}

// This is a common struct to wrap binary filter for every object.
message ObjectCollisionBelief {
  int64 object_id = 1;
  // belief of collision with object_id.
  double belief = 3;
}

// This is a common struct to wrap object collision belief for every trajectory.
message EgoTrajectoryBelief {
  EgoTrajectoryType ego_trajectory_type = 1;
  repeated ObjectCollisionBelief object_collision_beliefs = 2;
}

message LastTrajectoryResult {
  // Last planning path.
  Path last_path = 1;
  // Last planning trajectory.
  Trajectory last_trajectory = 2;
}

message ControlDriftPose {
  int64 pose_timestamp = 1;
  double drift = 2;
}

// Next Available ID: 8
message ControlDriftState {
  // Last trigger timestamp;
  int64 last_trigger_timestamp = 1;
  // control_drift.
  repeated double control_drift = 2 [ deprecated = true ];
  repeated double lateral_control_drift = 3 [ deprecated = true ];
  repeated double longitudinal_control_drift = 4 [ deprecated = true ];

  // control_drift with pose timestamp.
  repeated ControlDriftPose longitudinal_drift = 5;
  repeated ControlDriftPose lateral_drift = 6;

  // Real control error of each timestamp.
  repeated ControlErrorState control_error_state = 7;
}

// Relative direction to ego.
enum RelativeDir {
  REL_DIR_UNKNOWN = 0;
  REL_DIR_EGO = 1;
  REL_DIR_LEFT = 2;
  REL_DIR_RIGHT = 3;
}

// Lane sequence info history.
// Next Available ID: 4
message HistoryLaneSequenceInfo {
  repeated int64 lane_seq_lane_ids = 1;
  int64 seq_update_timestamp = 2;
  RelativeDir lane_relative_dir = 3;
}

// Lane sequence geometry info from exception handler geometry provider.
// Next Available ID: 4
message LaneSequenceGeometryInfo {
  // lane sequence lane ids
  repeated int64 lane_seq_lane_ids = 1;
  // lane relative direction
  RelativeDir lane_relative_dir = 2;
  // lane sequence nominal path
  voy.Polyline2d nominal_path = 3;
}

// Free space geometry info from exception handler free space geometry
// generator. Next Available ID: 3
message FreeSpaceGeometryInfo {
  // lane relative direction
  RelativeDir lane_relative_dir = 1;
  // free space driving corridor
  DrivingCorridor driving_corridor = 2;
}

// Local map geometry info around ego pose.
// Next Available ID: 7
message GeometryInfoSeed {
  // Latest ego lane id info and switching direction.
  int64 latest_ego_lane_id = 1;
  int64 ego_lane_switch_timestamp = 2;
  RelativeDir ego_lane_switch_dir = 3;

  // History lane sequence info from routing.
  repeated HistoryLaneSequenceInfo history_lane_seq_info = 4;
  // Lane sequence geometry info from geometry provider.
  repeated LaneSequenceGeometryInfo lane_sequence_geometry_infos = 5;
  // Free space geometry  info from geometry provider.
  repeated FreeSpaceGeometryInfo free_space_geometry_infos = 6;
}

// Storing Es trajectory obstacle collision condition.
// Next Available ID: 6.
message EsObstacleRisk {
  int64 id = 1;
  double ttc = 2;
  double collision_risk = 3;
  double nearmiss_risk = 4;
  double collision_severity = 5;
}

// Storing Es trajectory selection information.
// Next Available ID: 12.
message SelectionInfo {
  bool collision_pruning = 1;
  bool construction_zone_pruning = 2;
  double min_ttc = 3 [ deprecated = true ];
  double total_cost = 4;
  double long_comfort_cost = 5;
  double lat_comfort_cost = 6;
  double lat_offset_cost = 7;
  double path_diff_cost = 8;
  double risk_cost = 9;
  bool selected = 10;
  repeated EsObstacleRisk es_obstacle_risks = 11;
}

// Storing EH trajectory generation medium info.
// Next Available ID: 4
message TrajectoryGenerationInfo {
  string trajectory_id = 1;
  Trajectory trajectory_sampling = 2;
  SelectionInfo selection_info = 3;
}

// Trajectory check result.
// Next Available ID: 4
enum TrajectoryCheckStatus {
  CHECK_NONE = 0;
  CHECK_NORMAL = 1;
  CHECK_RISKY_AGENT = 2;
  CHECK_RISKY_BOUNDARY = 3;
}

// Next Available ID: 17
message EmergencyLaneKeepSeed {
  // Last elk start timestamp.
  int64 last_start_timestamp = 1;
  // Last elk executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last xlane risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Steer dir.
  TakeOverSteerDir last_steer_dir = 4;
  // Whether elk excuting in last cycle.
  bool is_executing = 5;
  // Enable condition satisfied in current loop.
  bool is_condition_satisfied = 6;
  // Last ttc collision info.
  ObjectTimeToCollision last_ttc_info = 7;
  // Whether latch enable state this cycle.
  bool is_latching = 8;
  // Last ego lane id.
  int64 last_ego_lane_id = 9;
  // Lane ids in last lane sequence.
  repeated int64 last_lane_seq_lane_ids = 10;
  // Last lane sequence update timestamp.
  int64 last_seq_update_timestamp = 11;
  // Xlane risk ttc collision info.
  repeated ObjectTimeToCollision last_xlane_risk_ttc_infos = 12;
  // Trajectory generation result.
  repeated TrajectoryGenerationInfo trajectory_generation_infos = 13;
  // Selected trajectory id.
  string selected_trajectory_id = 14;
  // Selected trajectory.
  Trajectory selected_trajectory = 15;
  // Selected trajectory check result.
  TrajectoryCheckStatus selected_trajectory_status = 16;
}

// Next Available ID: 13
message EmergencyEscapingSeed {
  // Last elk start timestamp.
  int64 last_start_timestamp = 1;
  // Last elk executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last xlane risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Steer dir.
  TakeOverSteerDir last_steer_dir = 4;
  // Whether elk excuting in last cycle.
  bool is_executing = 5;
  // Enable condition satisfied in current loop.
  bool is_condition_satisfied = 6;
  // Last ttc collision info.
  ObjectTimeToCollision last_ttc_info = 7;
  // Whether latch enable state this cycle.
  bool is_latching = 8;
}

// Tailgater protection trajectory generation info.
// Next Available ID: 17
message TailgaterProtectionSeed {
  // Last start timestamp.
  int64 last_start_timestamp = 1;
  // Last executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Steer dir.
  TakeOverSteerDir last_steer_dir = 4;
  // Whether excuting in last cycle.
  bool is_executing = 5;
  // Enable condition satisfied in current loop.
  bool is_condition_satisfied = 6;
  // Last ttc collision info.
  ObjectTimeToCollision last_ttc_info = 7;
  // Whether latch enable state this cycle.
  bool is_latching = 8;
  // Last ego lane id.
  int64 last_ego_lane_id = 9;
  // Lane ids in last lane sequence.
  repeated int64 last_lane_seq_lane_ids = 10;
  // Last lane sequence update timestamp.
  int64 last_seq_update_timestamp = 11;
  // Risk ttc collision info.
  repeated ObjectTimeToCollision last_risk_ttc_infos = 12;
  // Trajectory generation result.
  repeated TrajectoryGenerationInfo trajectory_generation_infos = 13;
  // Selected trajectory id.
  string selected_trajectory_id = 14;
  // Selected trajectory.
  Trajectory selected_trajectory = 15;
  // Selected trajectory check result.
  TrajectoryCheckStatus selected_trajectory_status = 16;
}

// Emergency brake swerve trajectory generation info.
// Next Available ID: 20
message EmergencyBrakeSwerveSeed {
  // Last start timestamp.
  int64 last_start_timestamp = 1;
  // Last executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Steer dir.
  TakeOverSteerDir last_steer_dir = 4;
  // Whether excuting in last cycle.
  bool is_executing = 5;
  // Enable condition satisfied in current loop.
  bool is_condition_satisfied = 6;
  // Last ttc collision info.
  ObjectTimeToCollision last_ttc_info = 7;
  // Whether latch enable state this cycle.
  bool is_latching = 8;
  // Last ego lane id.
  int64 last_ego_lane_id = 9;
  // Lane ids in last lane sequence.
  repeated int64 last_lane_seq_lane_ids = 10;
  // Last lane sequence update timestamp.
  int64 last_seq_update_timestamp = 11;
  // Risk ttc collision info.
  repeated ObjectTimeToCollision last_risk_ttc_infos = 12;
  // Trajectory generation result.
  repeated TrajectoryGenerationInfo trajectory_generation_infos = 13;
  // Selected trajectory id.
  string selected_trajectory_id = 14;
  // Selected trajectory.
  Trajectory selected_trajectory = 15;
  // Selected trajectory check result.
  TrajectoryCheckStatus selected_trajectory_status = 16;
  // Whether overwrite default ebs trajectory.
  bool is_overwrite_left = 17;
  bool is_overwrite_right = 18;
  // Whether eb enabled.
  bool is_eb_excuting = 19;
}

// Emergency brake state info.
// Next Available ID: 11
message EmergencyBrakeStateSeed {
  // Last start timestamp.
  int64 last_start_timestamp = 1;
  // Last executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Last start eb object ttc info.
  ObjectTimeToCollision last_start_object_ttc_info = 4;
  // Last executing eb object ttc info.
  ObjectTimeToCollision last_executing_object_ttc_info = 5;
  // last tick risk ttc collision infos.
  repeated ObjectTimeToCollision last_risk_ttc_infos = 6;
  // Whether latch enable state this cycle.
  bool is_latching = 7;
  // Whether excuting in last cycle.
  bool is_eb_executing = 8;
  // Whether need keep EB.
  bool is_need_keep_eb = 9;
  // EB state machine state.
  EhState eb_state_machine_state = 10;
}

// Emergency brake swerve state info.
// Next Available ID: 12
message EmergencyBrakeSwerveStateSeed {
  // Last start timestamp.
  int64 last_start_timestamp = 1;
  // Last executing timestamp.
  int64 last_executing_timestamp = 2;
  // Last risk detection timestamp.
  int64 last_risky_timestamp = 3;
  // Last start eb object ttc info.
  ObjectTimeToCollision last_start_object_ttc_info = 4;
  // Last executing eb object ttc info.
  ObjectTimeToCollision last_executing_object_ttc_info = 5;
  // last tick risk ttc collision infos.
  repeated ObjectTimeToCollision last_risk_ttc_infos = 6;
  // Whether latch enable state this cycle.
  bool is_latching = 7;
  // Whether excuting in last cycle.
  bool is_ebs_executing = 8;
  // Whether need keep EBS.
  bool is_need_keep_ebs = 9;
  // Last executing EBS direction. left is true, right is false.
  bool last_ebs_direction_is_left = 10;
  // EBS state machine state.
  EhState ebs_state_machine_state = 11;
}

// Trajectory generation relative info.
// Next Available ID: 7
message TrajectoryGenerationSeed {
  TakeOverTrajectoryType executing_trajectory_type = 1;
  EmergencyLaneKeepSeed elk_seed = 2;
  EmergencyEscapingSeed ee_seed = 3;
  TailgaterProtectionSeed tp_seed = 5;
  EmergencyBrakeSwerveSeed ebs_seed = 6;
  GeometryInfoSeed geometry_info_seed = 4;
}

// The exception handler seed.
// Next Available ID: 17
message ExceptionHandlerSeed {
  // The pose's time stamp of current cycle.
  int64 timestamp = 1;
  // The sequence number identifying the message of the exception handler cycle.
  int64 sequence_number = 2;

  // The ttc detect result seed.
  TtcDetectResultSeed ttc_detect_result_seed = 3;

  // Beliefs of collision with object.
  repeated ObjectCollisionBelief object_collision_beliefs = 4
      [ deprecated = true ];

  // Beliefs of agent yield intention.
  repeated ObjectCollisionBelief agent_yield_beliefs = 5;

  // Beliefs of collision with trajectory.
  repeated EgoTrajectoryBelief ego_trajectory_beliefs = 6;

  // timestamp of latest eb. 0 for no eb recently.
  int64 last_eb_timestamp = 7;

  // timestamp of latest start eb. 0 for no eb triggered.
  int64 last_start_eb_timestamp = 16;

  // Last planning trajectory.
  Trajectory last_trajectory = 8;

  // Last trajectory result.
  LastTrajectoryResult last_trajectory_result = 9;

  // timestamp of lastest critical exception.
  int64 last_critical_exception_timestamp = 10;

  // Control drift state.
  ControlDriftState control_drift_state = 11;

  // The trajectory token seed.
  TrajectoryTokenSeed trajectory_token_seed = 12;

  // Seed for EH trajectory generation.
  TrajectoryGenerationSeed trajectory_generation_seed = 13;

  // Seed for EB state.
  EmergencyBrakeStateSeed eb_seed = 14;

  // Seed for EBS state.
  EmergencyBrakeSwerveStateSeed ebs_seed = 15;
}
