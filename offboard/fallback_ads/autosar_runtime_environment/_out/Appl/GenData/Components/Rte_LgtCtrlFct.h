/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_LgtCtrlFct.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <LgtCtrlFct>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_LGTCTRLFCT_H
# define RTE_LGTCTRLFCT_H

//# ifndef RTE_CORE
//#  ifdef RTE_APPLICATION_HEADER_FILE
//#   error Multiple application header files included.
//#  endif
//#  define RTE_APPLICATION_HEADER_FILE
//#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
//#   define RTE_PTR2ARRAYBASETYPE_PASSING
//#  endif
//# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_LgtCtrlFct_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Buffers for inter-runnable variables
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(CDS_CollisionDetection_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms;
extern VAR(CDS_CollisionDetection_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_OsTask_Init_Core0_APP;

extern VAR(FSC_FreeStopControl_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_MainTask_Core0_10ms;
extern VAR(FSC_FreeStopControl_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_OsTask_Init_Core0_APP;

extern VAR(OPS_ObjectList_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms;
extern VAR(OPS_ObjectList_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_OPS_ObjectList_OsTask_Init_Core0_APP;

extern VAR(VSP_VehicleSignal_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms;
extern VAR(VSP_VehicleSignal_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_OsTask_Init_Core0_APP;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
# endif /* !defined(RTE_CORE) */


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_FreeStopControl_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)




#  define Rte_IWrite_LongitudinalControlFunction_Init_LGT_CtrlCmd_LGT_CtrlCmd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_LgtCtrlFct_LongitudinalControlFunction_Init.Rte_LGT_CtrlCmd_LGT_CtrlCmd.value = *(data) \
  )


#  define Rte_IWriteRef_LongitudinalControlFunction_Init_LGT_CtrlCmd_LGT_CtrlCmd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_LgtCtrlFct_LongitudinalControlFunction_Init.Rte_LGT_CtrlCmd_LGT_CtrlCmd.value \
  )


#  define Rte_IRead_ObjectPreSelection_10ms_Runnable_CSI_ObjectInfo_CSI_ObjectInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_ObjectInfo_CSI_ObjectInfo.value)


#  define Rte_IRead_SituationEvaluationDecision_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)
#  define Rte_IRead_SituationEvaluationDecision_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value)
#  define Rte_IWrite_SituationEvaluationDecision_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LgtCtrlFct_LGT_CtrlCmd_LGT_CtrlCmd.value = *(data) \
  )


#  define Rte_IWriteRef_SituationEvaluationDecision_10ms_Runnable_LGT_CtrlCmd_LGT_CtrlCmd() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LgtCtrlFct_LGT_CtrlCmd_LGT_CtrlCmd.value \
  )


#  define Rte_IRead_VehicleSignalProcess_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value)


/**********************************************************************************************************************
 * Inter-runnable variables
 *********************************************************************************************************************/

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_CollisionDetectionSystem_10ms_Runnable_OPS_ObjectList() \
  (&Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_CollisionDetectionSystem_10ms_Runnable_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_CollisionDetectionSystem_10ms_Runnable_CDS_CollisionDetection(data) \
  (Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms = *(data))
#  define Rte_IrvIWriteRef_CollisionDetectionSystem_10ms_Runnable_CDS_CollisionDetection() \
  (&Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_FreeStopControl_10ms_Runnable_CDS_CollisionDetection() \
  (&Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_FreeStopControl_10ms_Runnable_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_FreeStopControl_10ms_Runnable_FSC_FreeStopControl(data) \
  (Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_MainTask_Core0_10ms = *(data))
#  define Rte_IrvIWriteRef_FreeStopControl_10ms_Runnable_FSC_FreeStopControl() \
  (&Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LongitudinalControlFunction_Init_CDS_CollisionDetection(data) \
  (Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LongitudinalControlFunction_Init_CDS_CollisionDetection() \
  (&Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LongitudinalControlFunction_Init_FSC_FreeStopControl(data) \
  (Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LongitudinalControlFunction_Init_FSC_FreeStopControl() \
  (&Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LongitudinalControlFunction_Init_OPS_ObjectList(data) \
  (Rte_Irv_LgtCtrlFct_OPS_ObjectList_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LongitudinalControlFunction_Init_OPS_ObjectList() \
  (&Rte_Irv_LgtCtrlFct_OPS_ObjectList_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LongitudinalControlFunction_Init_VSP_VehicleSignal(data) \
  (Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LongitudinalControlFunction_Init_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_ObjectPreSelection_10ms_Runnable_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_ObjectPreSelection_10ms_Runnable_OPS_ObjectList(data) \
  (Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms = *(data))
#  define Rte_IrvIWriteRef_ObjectPreSelection_10ms_Runnable_OPS_ObjectList() \
  (&Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_CDS_CollisionDetection() \
  (&Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_FSC_FreeStopControl() \
  (&Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_OPS_ObjectList() \
  (&Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_SituationEvaluationDecision_10ms_Runnable_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_VehicleSignalProcess_10ms_Runnable_VSP_VehicleSignal(data) \
  (Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms = *(data))
#  define Rte_IrvIWriteRef_VehicleSignalProcess_10ms_Runnable_VSP_VehicleSignal() \
  (&Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms)
/* PRQA L:L1 */


# endif /* !defined(RTE_CORE) */


# define LgtCtrlFct_START_SEC_CODE
//# include "LgtCtrlFct_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_CollisionDetectionSystem_10ms_Runnable CollisionDetectionSystem_10ms_Runnable
#  define RTE_RUNNABLE_FreeStopControl_10ms_Runnable FreeStopControl_10ms_Runnable
#  define RTE_RUNNABLE_LongitudinalControlFunction_Init LongitudinalControlFunction_Init
#  define RTE_RUNNABLE_ObjectPreSelection_10ms_Runnable ObjectPreSelection_10ms_Runnable
#  define RTE_RUNNABLE_SituationEvaluationDecision_10ms_Runnable SituationEvaluationDecision_10ms_Runnable
#  define RTE_RUNNABLE_VehicleSignalProcess_10ms_Runnable VehicleSignalProcess_10ms_Runnable
# endif

FUNC(void, LgtCtrlFct_CODE) CollisionDetectionSystem_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LgtCtrlFct_CODE) FreeStopControl_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LgtCtrlFct_CODE) LongitudinalControlFunction_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LgtCtrlFct_CODE) ObjectPreSelection_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LgtCtrlFct_CODE) SituationEvaluationDecision_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LgtCtrlFct_CODE) VehicleSignalProcess_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define LgtCtrlFct_STOP_SEC_CODE
//# include "LgtCtrlFct_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_LGTCTRLFCT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
