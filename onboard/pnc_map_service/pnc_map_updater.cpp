#include "pnc_map_service/pnc_map_updater.h"

#include <algorithm>
#include <cstdint>
#include <memory>
#include <optional>
#include <set>
#include <unordered_map>
#include <utility>
#include <vector>

#include "hdmap/lib/hdmap.h"
#include "hdmap_protos/hard_boundary.pb.h"
#include "hdmap_protos/map_change.pb.h"
#include "planner/planning_gflags.h"
#include "pnc_map_service/map_elements/hard_boundary.h"
#include "pnc_map_service/map_elements/pnc_object.h"
#include "pnc_map_service/map_elements/traffic_signal.h"
#include "pnc_map_service/pnc_map_service.h"
#include "pnc_map_service/util/crosswalk_utility.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"

namespace pnc_map {

namespace {}  // namespace

Road* PncMapUpdater::GetRoad(int64_t id) const {
  Road* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->road_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetRoad(id);
}

HardBoundary* PncMapUpdater::GetHardBoundary(int64_t id) const {
  HardBoundary* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->hard_boundary_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetHardBoundary(id);
}

Section* PncMapUpdater::GetSection(int64_t id) const {
  Section* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->section_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetSection(id);
}

Lane* PncMapUpdater::GetLane(int64_t id) const {
  Lane* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->lane_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetLane(id);
}

PriorLane* PncMapUpdater::GetPriorLane(int64_t id) const {
  return pnc_map_service_->GetPriorLane(id);
}

LaneMarking* PncMapUpdater::GetLaneMarking(int64_t id) const {
  LaneMarking* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->lane_marking_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetLaneMarking(id);
}

TrafficSign* PncMapUpdater::GetTrafficSign(int64_t id) const {
  TrafficSign* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->traffic_sign_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetTrafficSign(id);
}

TrafficSignal* PncMapUpdater::GetTrafficSignal(int64_t id) const {
  TrafficSignal* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->traffic_signal_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetTrafficSignal(id);
}

Crosswalk* PncMapUpdater::GetCrosswalk(int64_t id) const {
  Crosswalk* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->crosswalk_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetCrosswalk(id);
}

StopLine* PncMapUpdater::GetStopLine(int64_t id) const {
  StopLine* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->stop_line_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetStopLine(id);
}

Zone* PncMapUpdater::GetZone(int64_t id) const {
  Zone* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->zone_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetZone(id);
}

Junction* PncMapUpdater::GetJunction(int64_t id) const {
  Junction* object = ElementsCache::GetMutableObject(
      id, DCHECK_NOTNULL(elements_cache_)->junction_map);
  if (object != nullptr) {
    return object;
  }
  return pnc_map_service_->GetJunction(id);
}

const hdmap::HdMap* PncMapUpdater::hdmap() const {
  return pnc_map_service_->hdmap();
}

int64_t PncMapUpdater::GetTimestampId() const {
  DCHECK(elements_cache_ != nullptr);
  if (elements_cache_ == nullptr) {
    return kTimestampForOfflineMap;
  }
  return elements_cache_->GetTimestampId();
}

void PncMapUpdater::PopulateElementsCache(
    const PncMapService* pnc_map_service,
    const hdmap::ChangedData& changed_protos,
    const std::map<
        std::pair</*predecessor_id=*/int64_t, /*successor_id=*/int64_t>,
        planner::lane_selection::TempLane>& temp_lanes,
    bool generate_effective_hb, ElementsCache* elements_cache) {
  DCHECK(elements_cache != nullptr);
  DCHECK(pnc_map_service != nullptr);

  pnc_map_service_ = pnc_map_service;
  elements_cache_ = elements_cache;
  generate_effective_hb_ = generate_effective_hb;
  // Must make a copy, since pnc map elements hold proto raw pointers.
  elements_cache->changed_protos = std::make_optional(changed_protos);
  const auto& hard_boundary_protos =
      elements_cache->changed_protos->changed_hardboundaries();
  const auto& road_protos = elements_cache->changed_protos->changed_roads();
  const auto& stop_line_protos =
      elements_cache->changed_protos->changed_stoplines();
  const auto& crosswalk_protos =
      elements_cache->changed_protos->changed_crosswalks();
  const auto& junction_protos =
      elements_cache->changed_protos->changed_junctions();

  // Step 1: add all types of elements to cache using element constructor.
  // At this step, many other element data fields are not populated.
  AddHardBoundaries(hard_boundary_protos);

  for (const auto& road_proto : road_protos) {
    for (const auto& section_proto : road_proto.lane_sections()) {
      for (const auto& lane_marking_proto : section_proto.lane_markings()) {
        AddLaneMarking(lane_marking_proto);
      }
    }
  }

  for (const auto& road_proto : road_protos) {
    for (const auto& section_proto : road_proto.lane_sections()) {
      for (const auto& lane_proto : section_proto.lanes()) {
        AddLane(lane_proto);
      }
    }
  }

  for (const auto& stop_line_proto : stop_line_protos) {
    AddStopLine(stop_line_proto);
  }

  for (const auto& road_proto : road_protos) {
    for (const auto& sign_proto : road_proto.signs()) {
      for (const auto& stop_line_proto : sign_proto.stop_lines()) {
        const auto* stop_line =
            elements_cache->GetStopLine(stop_line_proto.id());
        if (stop_line == nullptr) {
          AddStopLine(stop_line_proto);
        }
      }
    }
  }

  for (const auto& road_proto : road_protos) {
    // traffic_signals is a retired field. These codes may not be necessary.
    for (const auto& signal_proto : road_proto.traffic_signals()) {
      for (const auto& stop_line_proto : signal_proto.stop_lines()) {
        const auto* stop_line =
            elements_cache->GetStopLine(stop_line_proto.id());
        if (stop_line == nullptr) {
          AddStopLine(stop_line_proto);
        }
      }
    }
  }

  for (const auto& road_proto : road_protos) {
    for (const auto& section_proto : road_proto.lane_sections()) {
      AddSection(section_proto);
    }
  }

  for (const auto& road_proto : road_protos) {
    AddRoad(road_proto);
  }

  for (const auto& junction_proto : junction_protos) {
    AddJunction(junction_proto);
  }

  for (const auto& road_proto : road_protos) {
    // traffic_signals is a retired fields.
    for (const auto& signal_proto : road_proto.traffic_signals()) {
      AddTrafficSignalAndStopLinesIfNew(signal_proto);
    }
  }

  for (const auto& road_proto : road_protos) {
    for (const auto& sign_proto : road_proto.signs()) {
      AddTrafficSign(sign_proto);
    }
  }

  for (const auto& crosswalk_proto : crosswalk_protos) {
    AddCrosswalk(crosswalk_proto);
  }

  // Step 2: populate remaining element data fields such as element association
  // and pre-calculated data.
  UpdateElements(temp_lanes);

  elements_cache_ = nullptr;
}

void PncMapUpdater::UpdateElements(
    const std::map<
        std::pair</*predecessor_id=*/int64_t, /*successor_id=*/int64_t>,
        planner::lane_selection::TempLane>& temp_lanes) {
  UpdateRoads();
  UpdateSections();
  UpdateLanes(temp_lanes);
  UpdateTrafficSignals();
  UpdateCrosswalks();
  UpdateLaneMarkings();
}

void PncMapUpdater::AddLaneMarking(
    const hdmap::LaneMarking& lane_marking_proto) {
  elements_cache_->lane_marking_map.emplace(
      lane_marking_proto.id(),
      std::make_shared<LaneMarking>(&lane_marking_proto,
                                    /*timestamp_ms=*/GetTimestampId()));
}

void PncMapUpdater::AddHardBoundary(
    const hdmap::HardBoundary& hard_boundary_proto) {
  elements_cache_->hard_boundary_map.emplace(
      hard_boundary_proto.id(),
      std::make_shared<HardBoundary>(&hard_boundary_proto,
                                     /*timestamp_ms=*/GetTimestampId()));
}

void PncMapUpdater::AddHardBoundaries(
    const ::google::protobuf::RepeatedPtrField<::hdmap::HardBoundary>&
        hard_boundary_protos) {
  for (const auto& hard_boundary_proto : hard_boundary_protos) {
    AddHardBoundary(hard_boundary_proto);
  }
}

void PncMapUpdater::AddLane(const hdmap::Lane& lane_proto) {
  const LaneMarking* left_marking =
      elements_cache_->GetLaneMarking(lane_proto.left_lane_marking_id());
  DCHECK(left_marking != nullptr);
  const LaneMarking* right_marking =
      elements_cache_->GetLaneMarking(lane_proto.right_lane_marking_id());
  DCHECK(right_marking != nullptr);

  elements_cache_->lane_map.emplace(
      lane_proto.id(), std::make_shared<Lane>(&lane_proto,
                                              /*timestamp_ms=*/GetTimestampId(),
                                              left_marking, right_marking));
}

void PncMapUpdater::AddStopLine(const hdmap::StopLine& stop_line_proto) {
  const Lane* lane = GetLane(stop_line_proto.lane_id());
  // NOTE: It is possible the associated Lane is not created due to not in
  // global path. In this case, just return.
  if (lane == nullptr) {
    return;
  }
  elements_cache_->stop_line_map.emplace(
      stop_line_proto.id(),
      std::make_shared<StopLine>(&stop_line_proto,
                                 /*timestamp_ms=*/GetTimestampId(), lane));
}

void PncMapUpdater::AddSection(const hdmap::LaneSection& section_proto) {
  std::vector<const LaneMarking*> lane_markings;
  lane_markings.reserve(section_proto.lane_markings_size());
  for (const auto& lane_marking_proto : section_proto.lane_markings()) {
    const LaneMarking* lane_marking =
        elements_cache_->GetLaneMarking(lane_marking_proto.id());
    lane_markings.push_back(lane_marking);
  }

  std::vector<Lane*> lanes;
  lanes.reserve(section_proto.lanes_size());
  for (const auto& lane_proto : section_proto.lanes()) {
    Lane* lane = elements_cache_->GetMutableLane(lane_proto.id());
    lanes.push_back(lane);
  }

  const LaneMarking* leftmost_marking = elements_cache_->GetLaneMarking(
      section_proto.left_most_lane_marking_id());
  const LaneMarking* rightmost_marking = elements_cache_->GetLaneMarking(
      section_proto.right_most_lane_marking_id());

  elements_cache_->section_map.emplace(
      section_proto.id(), std::make_shared<Section>(
                              &section_proto,
                              /*timestamp_ms=*/GetTimestampId(), lane_markings,
                              leftmost_marking, rightmost_marking, &lanes));
}

void PncMapUpdater::AddRoad(const hdmap::Road& road_proto) {
  // Create road ZoneInfos.
  std::vector<ZoneInfo> zone_infos;
  zone_infos.reserve(road_proto.zones().size());
  for (const auto& zone_info_proto : road_proto.zones()) {
    const Zone* zone = GetZone(zone_info_proto.zone_id());
    zone_infos.emplace_back(zone, zone_info_proto.track_start(),
                            zone_info_proto.track_end());
  }

  std::sort(zone_infos.begin(), zone_infos.end(),
            [](const ZoneInfo& info1, const ZoneInfo& info2) {
              return info1.track_start.s() < info2.track_start.s();
            });

  std::vector<Section*> sections;
  sections.reserve(road_proto.lane_sections().size());
  for (const auto& section_proto : road_proto.lane_sections()) {
    Section* section = elements_cache_->GetMutableSection(section_proto.id());
    sections.push_back(section);
  }

  std::map<int64_t, std::shared_ptr<HardBoundary>> hard_boundary_map;
  // Prepare hard_boundary_map needed to construct road.
  std::set</*hard_boundary id*/ int64_t> road_hard_boundary_ids;
  for (const auto& hard_boundary :
       road_proto.middle_boundary().hard_boundaries()) {
    road_hard_boundary_ids.insert(hard_boundary.hard_boundary_id());
  }
  for (const auto& hard_boundary :
       road_proto.left_boundary().hard_boundaries()) {
    road_hard_boundary_ids.insert(hard_boundary.hard_boundary_id());
  }
  for (const auto& hard_boundary :
       road_proto.right_boundary().hard_boundaries()) {
    road_hard_boundary_ids.insert(hard_boundary.hard_boundary_id());
  }
  for (const auto hard_boundary_id : road_hard_boundary_ids) {
    const auto online_map_iter =
        elements_cache_->hard_boundary_map.find(hard_boundary_id);
    if (online_map_iter != elements_cache_->hard_boundary_map.end()) {
      hard_boundary_map[hard_boundary_id] = online_map_iter->second;
      continue;
    }
    const auto& static_hard_boundary_map =
        pnc_map_service_->offline_elements_cache_.hard_boundary_map;
    const auto static_map_iter =
        static_hard_boundary_map.find(hard_boundary_id);
    if (static_map_iter != static_hard_boundary_map.end()) {
      hard_boundary_map[hard_boundary_id] = static_map_iter->second;
    }
  }

  elements_cache_->road_map.emplace(
      road_proto.id(),
      std::make_shared<Road>(&road_proto,
                             /*timestamp_ms=*/GetTimestampId(),
                             hard_boundary_map, std::move(zone_infos),
                             generate_effective_hb_, &sections));
}

void PncMapUpdater::AddJunction(const hdmap::Junction& junction_proto) {
  std::vector<const Road*> roads;
  roads.reserve(junction_proto.road_ids().size());
  for (int64_t road_id : junction_proto.road_ids()) {
    Road* road = GetRoad(road_id);
    if (road == nullptr) {
      LOG(ERROR) << " Road is missing for id: " << road_id;
      continue;
    }
    roads.push_back(road);
  }

  const auto [iter, is_inserted] = elements_cache_->junction_map.emplace(
      junction_proto.id(),
      std::make_shared<Junction>(&junction_proto,
                                 /*timestamp_ms=*/GetTimestampId(), roads));

  const Junction* junction = iter->second.get();
  for (const auto* road : roads) {
    Road* mutable_road = elements_cache_->GetMutableRoad(road->id());
    if (mutable_road == nullptr) {
      continue;
    }
    mutable_road->set_junction(junction);
  }
}

void PncMapUpdater::AddTrafficSign(const hdmap::Sign& sign_proto) {
  std::vector<const StopLine*> stop_lines;
  stop_lines.reserve(sign_proto.stop_lines_size());
  for (const auto& stop_line_proto : sign_proto.stop_lines()) {
    const auto* stop_line = GetStopLine(stop_line_proto.id());
    stop_lines.push_back(stop_line);
  }

  elements_cache_->traffic_sign_map.emplace(
      sign_proto.id(), std::make_shared<TrafficSign>(
                           &sign_proto,
                           /*timestamp_ms=*/GetTimestampId(), stop_lines));
}

void PncMapUpdater::AddTrafficSignalAndStopLinesIfNew(
    const hdmap::Signal& signal_proto) {
  const auto* traffic_signal =
      elements_cache_->GetTrafficSignal(signal_proto.id());
  if (traffic_signal != nullptr) {
    return;
  }

  std::vector<const StopLine*> stop_lines;
  stop_lines.reserve(signal_proto.stop_lines_size());
  for (const auto& stop_line_proto : signal_proto.stop_lines()) {
    const auto* stop_line = elements_cache_->GetStopLine(stop_line_proto.id());
    if (stop_line == nullptr) {
      AddStopLine(stop_line_proto);
      stop_line = elements_cache_->GetStopLine(stop_line_proto.id());
    }
    stop_lines.push_back(stop_line);
  }

  elements_cache_->traffic_signal_map.emplace(
      signal_proto.id(), std::make_shared<TrafficSignal>(
                             &signal_proto,
                             /*timestamp_ms=*/GetTimestampId(), stop_lines));
}

void PncMapUpdater::AddCrosswalk(const hdmap::Crosswalk& crosswalk_proto) {
  std::vector<const StopLine*> stop_lines;
  stop_lines.reserve(crosswalk_proto.stop_lines_size());
  for (const auto& stop_line_proto : crosswalk_proto.stop_lines()) {
    const auto* stop_line = elements_cache_->GetStopLine(stop_line_proto.id());
    if (stop_line == nullptr) {
      AddStopLine(stop_line_proto);
      stop_line = elements_cache_->GetStopLine(stop_line_proto.id());
    }
    stop_lines.push_back(stop_line);
  }

  std::vector<const TrafficSignal*> traffic_signals;
  traffic_signals.reserve(crosswalk_proto.signal_ids_size());
  for (const auto signal_id : crosswalk_proto.signal_ids()) {
    const auto* signal = GetTrafficSignal(signal_id);
    if (signal == nullptr) {
      LOG(ERROR) << "Missing traffic signal for id: " << signal_id;
      continue;
    }
    traffic_signals.push_back(signal);
  }

  elements_cache_->crosswalk_map.emplace(
      crosswalk_proto.id(),
      std::make_shared<Crosswalk>(
          &crosswalk_proto, /*timestamp_ms=*/GetTimestampId(),
          std::move(stop_lines), std::move(traffic_signals)));
}

void PncMapUpdater::UpdateRoads() {
  for (auto& [_, road_ptr] : elements_cache_->road_map) {
    for (const ZoneInfo& zone_info : road_ptr->zone_infos()) {
      Zone* zone = DCHECK_NOTNULL(pnc_map_service_->GetZone(zone_info.id()));
      if (zone->type() == hdmap::Zone::ROUNDABOUT) {
        // Set this for all new roads before calling UpdateRoad().
        road_ptr->set_is_associated_with_roundabout(true);
      }
    }
  }

  for (auto& [_, road_ptr] : elements_cache_->road_map) {
    UpdateRoad(road_ptr.get());
  }

  const std::unordered_map<int64_t,
                           std::shared_ptr<const hdmap::PickupDropoffZone>>*
      pickup_dropoff_zones = hdmap()->GetAllPickupDropOffZones();
  if (pickup_dropoff_zones != nullptr) {
    for (const auto& [id, pickup_dropoff_zones_proto] : *pickup_dropoff_zones) {
      const int64_t road_id = pickup_dropoff_zones_proto->road_id();
      Road* road = ElementsCache::GetMutableObject(
          road_id, DCHECK_NOTNULL(elements_cache_)->road_map);
      if (road != nullptr) {
        road->AppendRelatedPickupDropoffZone(
            pnc_map_service_->GetPickupDropoffZone(id));
      }
    }
  }
}

void PncMapUpdater::UpdateRoad(Road* road_ptr) {
  DCHECK(road_ptr != nullptr);

  const auto& road_proto = road_ptr->proto();
  std::vector<const Crosswalk*> crosswalks;
  crosswalks.reserve(road_proto.crosswalks().size());
  for (const auto& crosswalk_id : road_proto.crosswalks()) {
    Crosswalk* crosswalk = GetCrosswalk(crosswalk_id);
    crosswalks.push_back(DCHECK_NOTNULL(crosswalk));
  }
  road_ptr->set_crosswalks(crosswalks);
  // Note that, this updates road's head and tail sections too.
  LinkRoadAndInnerSections(road_ptr);
  pnc_map_service_->SetNeighborRoad(pnc_map_service_->road_feature_map(),
                                    road_ptr);
  SetRoadRelationToRoundabout(road_ptr);
  const auto* static_road = pnc_map_service_->GetRoad(road_ptr->id());
  if (static_road != nullptr) {
    // TODO(xiaodong) set these fields for an added road (i.e., static_road ==
    // nullptr).
    const auto* static_opposite_road = static_road->opposite_road();
    if (static_opposite_road != nullptr) {
      GetRoad(static_road->id());
      // Use static_opposite_road->id() to find the road from joined map
      // sources.
      road_ptr->set_opposite_road(GetRoad(static_opposite_road->id()));
    }
    road_ptr->set_nearest_junction(static_road->nearest_junction());
    road_ptr->set_road_start_to_nearest_junction_dist_m(
        static_road->road_start_to_nearest_junction_dist_m());
  }
}

void PncMapUpdater::UpdateSections() {
  for (auto& [_, road_ptr] : elements_cache_->road_map) {
    std::vector<Section*> sections;
    sections.reserve(road_ptr->sections().size());
    for (const auto* section : road_ptr->sections()) {
      Section* mutable_section =
          elements_cache_->GetMutableSection(section->id());
      DCHECK(mutable_section != nullptr);
      sections.push_back(mutable_section);
    }
    // Link Sections' successors (except the tail) and predecessors (except the
    // head).
    for (size_t i = 0; i < sections.size(); ++i) {
      if (i > 0) {
        sections[i]->set_predecessors({sections[i - 1]});
      }
      if (i + 1 < sections.size()) {
        sections[i]->set_successors({sections[i + 1]});
      }
    }
  }

  for (auto& [_, section_ptr] : elements_cache_->section_map) {
    UpdateSection(section_ptr.get());
  }
}

void PncMapUpdater::UpdateSection(Section* section_ptr) {
  const auto* static_section = pnc_map_service_->GetSection(section_ptr->id());
  if (static_section->is_enter_section()) {
    section_ptr->set_is_enter_section(true);
    // TODO(xiaodong) This is a very involved method, should verify if we can
    // just use the function for pnc map service as it is.
    SetRelatedSections(section_ptr);
  }

  SetCrossRoadLeftNeighborLane(section_ptr);
  SetCrossRoadRightNeighborLane(section_ptr);
}

void PncMapUpdater::UpdateLanes(
    const std::map<
        std::pair</*predecessor_id=*/int64_t, /*successor_id=*/int64_t>,
        planner::lane_selection::TempLane>& temp_lanes) {
  std::set</*section id=*/int64_t> processed_section_set;
  for (auto& [_, lane_ptr] : elements_cache_->lane_map) {
    UpdateLane(temp_lanes, lane_ptr.get(), &processed_section_set);
  }

  for (auto& [_, sign_ptr] : elements_cache_->traffic_sign_map) {
    // Link TrafficSign to Lane.
    for (const StopLine* stop_line : sign_ptr->stop_lines()) {
      if (stop_line->lane() == nullptr) {
        continue;
      }
      const int64_t lane_id = stop_line->lane()->id();
      Lane* online_lane = elements_cache_->GetMutableLane(lane_id);
      // TODO(xiaodong) Prevent adding same sign more than once.
      if (online_lane != nullptr) {
        online_lane->add_traffic_sign(sign_ptr.get());
      }
    }
  }

  CollectLanesForNewAssociateTrafficSignals();
  SetupAssociateTrafficSignalsForLanes(temp_lanes);
}

void PncMapUpdater::CollectLanesForNewAssociateTrafficSignals() {
  auto& lane_to_traffic_signal_map =
      elements_cache_->lane_to_traffic_signal_map;
  // All online lanes need associated traffic signals.
  for (const auto& [_, lane_ptr] : elements_cache_->lane_map) {
    // Create an empty entry first.
    lane_to_traffic_signal_map[lane_ptr->id()] =
        std::vector<AssociateTrafficSignal>{};
  }

  // Online traffic signal may change lane to signal associations. We collect
  // all affected lanes.
  for (const auto& [signal_id, online_signal] :
       elements_cache_->traffic_signal_map) {
    for (const auto& associate_lane_proto :
         online_signal->proto().associate_lanes()) {
      const int64_t lane_id = associate_lane_proto.lane_id();
      const auto iter = lane_to_traffic_signal_map.find(lane_id);
      if (iter != lane_to_traffic_signal_map.end()) {
        // Already added.
        continue;
      }
      // This lane is associated with an online traffic signal.
      // Note that this lane may not have a online version, since hdmap lane
      // proto does not contain the relation information. Lane is not considered
      // as changed. Create an empty entry for this lane.
      lane_to_traffic_signal_map[lane_id] =
          std::vector<AssociateTrafficSignal>{};
    }

    // Check if static association relation is removed by online traffic signal.
    const auto* static_signal = pnc_map_service_->GetTrafficSignal(signal_id);
    if (static_signal == nullptr) {
      continue;
    }
    for (const auto& associate_lane_proto :
         static_signal->proto().associate_lanes()) {
      const int64_t static_association_lane_id = associate_lane_proto.lane_id();
      const auto iter =
          lane_to_traffic_signal_map.find(static_association_lane_id);
      if (iter != lane_to_traffic_signal_map.end()) {
        // Already added.
        continue;
      }
      // static_association_lane_id is not presented in online_signal associate
      // lanes. Therefore the relationship is removed by new signal. Create an
      // empty entry for this lane.
      // Proof: by contradiction, if static_association_lane_id is one of
      // online_signal->proto().associate_lanes(), then
      // lane_to_traffic_signal_map should contains the
      // static_association_lane_id due to previous loop.
      lane_to_traffic_signal_map[static_association_lane_id] =
          std::vector<AssociateTrafficSignal>{};
    }
  }

  // Process every deleted traffic signal.
  for (const auto& change_meta :
       elements_cache_->changed_protos->change_metas()) {
    if (change_meta.id().type() != hdmap::ElementId_ElementType_SIGNAL ||
        change_meta.meta().type() !=
            hdmap::ChangeMeta_ChangeType_REMOVE_ELEMENT) {
      continue;
    }
    // Signal deletion case.
    const auto* static_signal =
        pnc_map_service_->GetTrafficSignal(change_meta.id().id());
    if (static_signal == nullptr) {
      continue;
    }
    // Every lane associated with deleted traffic signal is affected.
    for (const auto& associate_lane_proto :
         static_signal->proto().associate_lanes()) {
      const int64_t static_association_lane_id = associate_lane_proto.lane_id();
      const auto iter =
          lane_to_traffic_signal_map.find(static_association_lane_id);
      if (iter != lane_to_traffic_signal_map.end()) {
        // Already added.
        continue;
      }
      lane_to_traffic_signal_map[static_association_lane_id] =
          std::vector<AssociateTrafficSignal>{};
    }
  }
}

void PncMapUpdater::CollectTrafficSignalsForNewAssociateCrosswalks() {
  auto& traffic_signal_to_crosswalk_map =
      elements_cache_->traffic_signal_to_crosswalk_map;
  // All online traffic signals need associated crosswalks.
  for (const auto& [_, signal_ptr] : elements_cache_->traffic_signal_map) {
    // Create an empty entry first.
    traffic_signal_to_crosswalk_map[signal_ptr->id()] =
        std::vector<std::shared_ptr<const Crosswalk>>{};
  }

  // Online crosswalk may change traffic signal to crosswalk associations. We
  // collect all affected signals.
  for (const auto& [crosswalk_id, online_crosswalk] :
       elements_cache_->crosswalk_map) {
    for (const auto signal_id : online_crosswalk->proto().signal_ids()) {
      const auto iter = traffic_signal_to_crosswalk_map.find(signal_id);
      if (iter != traffic_signal_to_crosswalk_map.end()) {
        // Already added.
        continue;
      }
      // This signal is associated with an online crosswalk.
      // Note that this signal may not have a online version, since hdmap lane
      // proto does not contain the relation information. signal is not
      // considered as changed. Create an empty entry for this signal.
      traffic_signal_to_crosswalk_map[signal_id] =
          std::vector<std::shared_ptr<const Crosswalk>>{};
    }

    // Check if static association relation is removed by online crosswalk.
    const auto* static_crosswalk = pnc_map_service_->GetCrosswalk(crosswalk_id);
    if (static_crosswalk == nullptr) {
      continue;
    }
    for (const auto static_association_signal_id :
         static_crosswalk->proto().signal_ids()) {
      const auto iter =
          traffic_signal_to_crosswalk_map.find(static_association_signal_id);
      if (iter != traffic_signal_to_crosswalk_map.end()) {
        // Already added.
        continue;
      }
      // static_association_signal_id is not presented in online crosswalk
      // associated traffic signals. Therefore the relationship is removed by
      // new crosswalk. Create an empty entry for this signal.
      traffic_signal_to_crosswalk_map[static_association_signal_id] =
          std::vector<std::shared_ptr<const Crosswalk>>{};
    }
  }

  // Process every deleted crosswalk.
  for (const auto& change_meta :
       elements_cache_->changed_protos->change_metas()) {
    if (change_meta.id().type() != hdmap::ElementId_ElementType_CROSSWALK ||
        change_meta.meta().type() !=
            hdmap::ChangeMeta_ChangeType_REMOVE_ELEMENT) {
      continue;
    }
    // Crosswalk deletion case.
    const auto* static_crosswal =
        pnc_map_service_->GetCrosswalk(change_meta.id().id());
    if (static_crosswal == nullptr) {
      continue;
    }
    // Every traffic signal associated with deleted crosswalk is affected.
    for (const auto static_association_signal_id :
         static_crosswal->proto().signal_ids()) {
      const auto iter =
          traffic_signal_to_crosswalk_map.find(static_association_signal_id);
      if (iter != traffic_signal_to_crosswalk_map.end()) {
        // Already added.
        continue;
      }
      traffic_signal_to_crosswalk_map[static_association_signal_id] =
          std::vector<std::shared_ptr<const Crosswalk>>{};
    }
  }
}

void PncMapUpdater::SetupAssociateTrafficSignalsForLanes(
    const std::map<
        std::pair</*predecessor_id=*/int64_t, /*successor_id=*/int64_t>,
        planner::lane_selection::TempLane>& temp_lanes) {
  auto& lane_to_traffic_signal_map =
      elements_cache_->lane_to_traffic_signal_map;
  // Step 1: Handle online signal.
  for (auto& [lane_id, traffic_signals] : lane_to_traffic_signal_map) {
    const auto iter =
        std::find_if(temp_lanes.begin(), temp_lanes.end(),
                     [search_lane_id = lane_id](const auto& item) {
                       return item.second.lane.id() == search_lane_id &&
                              item.second.pair_lane_id.has_value();
                     });
    const int64_t lane_id_for_signal =
        iter != temp_lanes.end() ? iter->second.pair_lane_id.value() : lane_id;
    // Check all online signals.
    for (auto& [_, online_signal] : elements_cache_->traffic_signal_map) {
      for (const auto& associate_lane_proto :
           online_signal->proto().associate_lanes()) {
        if (lane_id_for_signal != associate_lane_proto.lane_id()) {
          continue;
        }
        // Link TrafficSignal to Lane.
        traffic_signals.emplace_back(online_signal.get(),
                                     associate_lane_proto.type());
        break;
      }
    }

    // Step 2: Handle static signal.
    const auto* static_lane = pnc_map_service_->GetLane(lane_id_for_signal);
    if (static_lane != nullptr) {
      for (const auto& associate_traffic_signal :
           static_lane->traffic_signals()) {
        if (elements_cache_->IsObjectDeleted<TrafficSignal>(
                associate_traffic_signal.id())) {
          continue;
        }
        const auto* online_signal =
            elements_cache_->GetTrafficSignal(associate_traffic_signal.id());
        if (online_signal) {
          // online_signal to lane association is processed.
          // If we have corresponding online signal, we use the updated
          // association data.
          continue;
        }
        // Link TrafficSignal to Lane.
        traffic_signals.push_back(associate_traffic_signal);
      }
    }
  }

  // lane_to_traffic_signal_map is fully populated now.
  // Step 3: Add traffic signals for all online lanes.
  for (const auto& [lane_id, lane_ptr] : elements_cache_->lane_map) {
    const auto iter = lane_to_traffic_signal_map.find(lane_id);
    if (iter == lane_to_traffic_signal_map.end()) {
      LOG(ERROR) << "Missing associate traffic signals for lane id: "
                 << lane_id;
      continue;
    }
    for (const auto& associate_traffic_signal : iter->second) {
      lane_ptr->add_traffic_signal(associate_traffic_signal.signal,
                                   associate_traffic_signal.type);
    }
  }
}

void PncMapUpdater::SetupAssociateCrosswalksForTrafficSignals() {
  auto& traffic_signal_to_crosswalk_map =
      elements_cache_->traffic_signal_to_crosswalk_map;
  // Step 1: Handle online crosswalk.
  for (auto& [signal_id, crosswalks] : traffic_signal_to_crosswalk_map) {
    // Find all online crosswalks associated with signal_id.
    for (auto& [_, online_crosswalk] : elements_cache_->crosswalk_map) {
      for (const auto& associate_traffic_signal :
           online_crosswalk->traffic_signals()) {
        if (signal_id != associate_traffic_signal->id()) {
          continue;
        }
        // Link Crosswalk to signal.
        crosswalks.push_back(online_crosswalk);
        break;
      }
    }

    // Step 2: Handle static crosswalk.
    const auto* static_signal = pnc_map_service_->GetTrafficSignal(signal_id);
    if (static_signal == nullptr) {
      continue;
    }

    for (const auto* associate_crosswalk :
         static_signal->associated_crosswalks()) {
      if (elements_cache_->IsObjectDeleted<Crosswalk>(
              associate_crosswalk->id())) {
        continue;
      }
      const auto* associate_crosswalk_ptr =
          elements_cache_->GetCrosswalk(associate_crosswalk->id());
      if (associate_crosswalk_ptr) {
        // online_crosswalk to traffic signal relation is already processed.
        // Should continue.
        continue;
      }
      // Link Crosswalk to Signal.
      const auto static_crosswalk_ptr = ElementsCache::GetObjectSharedPtr(
          associate_crosswalk->id(),
          pnc_map_service_->offline_elements_cache_.crosswalk_map);
      if (static_crosswalk_ptr) {
        crosswalks.push_back(static_crosswalk_ptr);
      }
    }
  }

  // traffic_signal_to_crosswalk_map is fully populated now.
  // Step 3: Add crosswalks for all online traffic signals.
  for (const auto& [signal_id, signal_ptr] :
       elements_cache_->traffic_signal_map) {
    const auto iter = traffic_signal_to_crosswalk_map.find(signal_id);
    if (iter == traffic_signal_to_crosswalk_map.end()) {
      LOG(ERROR) << "Missing traffic signal for signal id: " << signal_id;
      continue;
    }
    for (const auto& crosswalk : iter->second) {
      signal_ptr->add_associated_crosswalk(crosswalk.get());
    }
  }
}

void PncMapUpdater::UpdateLane(
    const std::map<
        std::pair</*predecessor_id=*/int64_t, /*successor_id=*/int64_t>,
        planner::lane_selection::TempLane>& temp_lanes,
    Lane* lane_ptr, std::set</*section id=*/int64_t>* processed_section_set) {
  DCHECK(lane_ptr != nullptr);
  DCHECK(processed_section_set != nullptr);

  const auto iter =
      std::find_if(temp_lanes.begin(), temp_lanes.end(),
                   [search_lane_id = lane_ptr->id()](const auto& item) {
                     return item.second.lane.id() == search_lane_id;
                   });
  const routing::RoutingVertex* pair_lane_routing_vertex = nullptr;
  if (iter != temp_lanes.end()) {
    lane_ptr->set_is_temp_lane(true);
    if (iter->second.pair_lane_id.has_value()) {
      const pnc_map::Lane* static_pair_lane =
          pnc_map_service_->GetLaneById(iter->second.pair_lane_id.value());
      if (static_pair_lane != nullptr) {
        pair_lane_routing_vertex = &static_pair_lane->routing_vertex();
      }
    }
  }

  LinkLane(lane_ptr);
  LinkConflictingLane(lane_ptr);
  const auto* road = lane_ptr->section()->road();
  lane_ptr->AddZones(road->zone_infos());
  SetAdjacentLeftLane(lane_ptr);
  SetAdjacentRightLane(lane_ptr);
  SetNeighborLaneConnectionSegments(lane_ptr);
  SetBrothers(lane_ptr);
  SetHardBoundaryInfoForLane(lane_ptr);
  SetCrosswalks(&pnc_map_service_->zone_lane_occupancy_map(), lane_ptr);
  SetTruncatedPrecedingLanesAndSectionIds(lane_ptr);
  SetMergeForkTurnSignal(lane_ptr);
  const auto* prior_lane = pnc_map_service_->GetPriorLane(lane_ptr->id());
  if (prior_lane != nullptr) {
    // It is doing add.
    lane_ptr->AddPriorLane(prior_lane);
  }

  // Build Routing Vertex for each lane.
  // Vehicle width is a required parameter. Since we provided
  // precalculated_lane_feature_map, the width parameter is not used.
  if (lane_ptr->is_temp_lane()) {
    lane_ptr->mutable_routing_vertex()->BuildVertex(
        *pnc_map_service_->hdmap(), pnc_map_service_->road_feature_map(),
        pnc_map_service_->precalculated_lane_feature_map(), lane_ptr->proto(),
        lane_ptr->section()->proto(), lane_ptr->section()->road()->proto(),
        pair_lane_routing_vertex,
        hdmap::prior_map::kDefaultVehicleWidthInMeters);
  } else {
    lane_ptr->mutable_routing_vertex()->BuildVertex(
        *pnc_map_service_->hdmap(), pnc_map_service_->zone_lane_occupancy_map(),
        pnc_map_service_->road_feature_map(),
        pnc_map_service_->precalculated_lane_feature_map(),
        hdmap::prior_map::kDefaultVehicleWidthInMeters, lane_ptr->id());
  }

  const auto* static_lane_for_sign =
      pnc_map_service_->GetLane(pair_lane_routing_vertex != nullptr
                                    ? pair_lane_routing_vertex->lane()->id()
                                    : lane_ptr->id());
  if (static_lane_for_sign != nullptr) {
    for (const auto* traffic_sign : static_lane_for_sign->traffic_signs()) {
      const auto* online_sign =
          elements_cache_->GetTrafficSign(traffic_sign->id());
      if (online_sign) {
        // online_sign to lane association is processed in UpdateLanes().
        continue;
      }
      lane_ptr->add_traffic_sign(traffic_sign);
    }
  }

  const auto [_, is_inserted] =
      processed_section_set->insert(lane_ptr->section()->id());
  if (!is_inserted) {
    // section is processed already.
    return;
  }
  const auto* section = lane_ptr->section();
  DCHECK(section != nullptr);
  std::vector<Lane*> lanes;
  lanes.reserve(section->lanes().size());
  for (const auto* section_lane : section->lanes()) {
    DCHECK(section_lane != nullptr);
    Lane* mutable_lane = elements_cache_->GetMutableLane(section_lane->id());
    DCHECK(mutable_lane != nullptr);
    lanes.push_back(mutable_lane);
  }
  for (size_t i = 0; i < lanes.size(); ++i) {
    // Link left lane.
    if (i > 0) {
      lanes[i]->set_left_lane(lanes[i - 1]);
    }
    // Link right lane.
    if (i + 1 < lanes.size()) {
      lanes[i]->set_right_lane(lanes[i + 1]);
    }
  }
}

void PncMapUpdater::UpdateTrafficSignals() {
  CollectTrafficSignalsForNewAssociateCrosswalks();
  SetupAssociateCrosswalksForTrafficSignals();
}

void PncMapUpdater::UpdateCrosswalks() {
  for (auto& [_, crosswalk_ptr] : elements_cache_->crosswalk_map) {
    UpdateCrosswalk(crosswalk_ptr.get());
  }
}

void PncMapUpdater::UpdateCrosswalk(Crosswalk* crosswalk_ptr) {
  math::geometry::Polyline2d center_line_string =
      GenerateCenterLineForCrosswalk(*crosswalk_ptr);
  // NOTE(Jiajun): Since not all crosswalk's have labeled associated roads or
  // stop lines, we can't guarantee that all crosswalk's center line can be
  // generated. We will notify those crosswalks without calculated center
  // lines.
  if (center_line_string.size() < kMinPointsOfCrosswalkCenterLine) {
    LOG(ERROR) << "Can't generate proper center line for crosswalk: "
               << crosswalk_ptr->id();
  } else {
    crosswalk_ptr->set_center_line(
        math::geometry::PolylineCurve2d(std::move(center_line_string)));
  }
  crosswalk_ptr->set_driving_areas(
      GenerateDrivingAreasForCrosswalk(*crosswalk_ptr));

  const Crosswalk* static_crosswalk_ptr = GetCrosswalk(crosswalk_ptr->id());
  if (static_crosswalk_ptr == nullptr) {
    return;
  }

  for (const auto* road : static_crosswalk_ptr->roads()) {
    DCHECK(road != nullptr);
    const auto* associated_road = GetRoad(road->id());
    if (associated_road == nullptr) {
      LOG(ERROR) << " Road is missing for id: " << road->id();
      continue;
    }
    crosswalk_ptr->add_road(associated_road);
  }
}

void PncMapUpdater::UpdateLaneMarkings() {
  for (auto& [_, lane_ptr] : elements_cache_->lane_map) {
    SetCloseToHardBoundaryForLaneMarkings(*lane_ptr);
  }
}
}  // namespace pnc_map
