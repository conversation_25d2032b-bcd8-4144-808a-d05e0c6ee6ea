#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_LANE_DEPARTURE_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_LANE_DEPARTURE_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

extern bool CheckLeftLaneDeparture(bool left_lane_exist, float left_lane_y0,
                                   float left_lane_heading,
                                   float ego_long_velocity);

extern bool CheckRightLaneDeparture(bool right_lane_exist, float right_lane_y0,
                                    float right_lane_heading,
                                    float ego_long_velocity);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_LANE_CENTER_LANE_DEPARTURE_H_
