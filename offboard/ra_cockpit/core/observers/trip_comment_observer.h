#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRIP_COMMENT_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRIP_COMMENT_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "voy_protos/trip_comment.pb.h"

namespace remote_assist {

class TripCommentObserver : public IDataObserver {
 public:
  using TripCommentCallback =
      std::function<void(std::shared_ptr<voy::TripComment>, DataChannel)>;

  explicit TripCommentObserver(TripCommentCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  TripCommentCallback trip_comment_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRIP_COMMENT_OBSERVER_H_
