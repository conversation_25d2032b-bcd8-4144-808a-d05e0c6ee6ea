#ifndef ONBOARD_VARCH_VNODE_MESSAGE_UTIL_H_
#define ONBOARD_VARCH_VNODE_MESSAGE_UTIL_H_

#include <gflags/gflags.h>

#include <memory>
#include <unordered_map>
#include <utility>

#include "av_comm/onboard_config.h"
#include "onboard/gen4/ntb_bridge/ntb_driver_checker.h"
#include "varch/protos/vnode/message_ack.pb.h"
#include "varch/vnode/detail/publisher/publisher_wrapper.h"
#include "varch/vnode/detail/subscriber/subscriber_wrapper.h"
#include "varch/vnode/global.h"

namespace varch {
namespace vnode {
namespace message_util {

using Publisher = detail::publisher::PublisherWrapper;
using PublisherOption = detail::publisher::PublisherOption;
using Subscriber = detail::subscriber::SubscriberWrapper;
using SubscriberConfig = detail::subscriber::SubscriberConfig;
using TransportType = varch::protos::vnode::TransportType;
using ProcessorType = varch::protos::vnode::ProcessorType;
using MessageAck = varch::protos::vnode::MessageAck;

DECLARE_int32(transport);

/**
 * @brief Get the Transport Type from gflags parameter
 *
 * @return TransportType
 */
inline TransportType GetTransportType(bool raw = false) {
  static const std::unordered_map<int32_t, TransportType> transport_map = {
      {0, TransportType::kInterProcess},
      {1, TransportType::kUDP},
      {2, TransportType::kTCP}};
  static const std::unordered_map<TransportType, TransportType>
      transport_raw_map = {{TransportType::kUDP, TransportType::kRawMessageUDP},
                           {TransportType::kInterProcess,
                            TransportType::kRawMessageInterProcess}};
  auto trans_type = transport_map.at(FLAGS_transport);
  if (raw) {
    trans_type = transport_raw_map.at(trans_type);
  }
  return trans_type;
}

/**
 * @brief Create a Subscriber object
 *
 * @tparam MsgType The type of message.
 * @tparam CallbackType The type of callback.
 * @param topic_name Topic name, string.
 * @param queue_size The length of the queue, int.
 * 0 means the queue length is infinite, negative means that the subscriber is
 * in trigger mode, callback is called when the message arrives, |SpinOnce| or
 * |CallAvailable| will not work.
 * @param callback The callback function corresponding to MsgType.
 * @param type_name The type name string of MsgType.
 * @param trans_type Transport type.
 * @return Subscriber
 */
template <typename MsgType, typename CallbackType>
Subscriber CreateSubscriber(
    const std::string& topic_name, int32_t queue_size, CallbackType&& callback,
    const std::string& type_name = utils::GetTypeName<MsgType>(),
    const TransportType& trans_type = GetTransportType(),
    const ProcessorType& processor_type = ProcessorType::kDedicateThread) {
  SubscriberConfig config(topic_name,
                          varch::vnode::GetVArchGlobalData()->IsReplayTestMode()
                              ? TransportType::kIntraProcess
                              : trans_type,
                          processor_type, queue_size);
  Subscriber subscriber;
  subscriber.Init<MsgType, CallbackType>(
      config, std::forward<CallbackType>(callback), type_name);
  return subscriber;
}

// Same as above, just return a unique_ptr of the created subscriber.
template <typename MsgType, typename CallbackType>
std::unique_ptr<Subscriber> CreateSubscriberPtr(
    const std::string& topic_name, int32_t queue_size, CallbackType&& callback,
    const ProcessorType& processor_type = ProcessorType::kDedicateThread,
    const std::string& type_name = utils::GetTypeName<MsgType>(),
    const TransportType& trans_type = GetTransportType()) {
  // Force ack subscriber to run on a dedicated thread.
  // const ProcessorType processor_type = ProcessorType::kDedicateThread;
  SubscriberConfig config(topic_name,
                          varch::vnode::GetVArchGlobalData()->IsReplayTestMode()
                              ? TransportType::kIntraProcess
                              : trans_type,
                          processor_type, queue_size);
  std::unique_ptr<Subscriber> subscriber = std::make_unique<Subscriber>();
  subscriber->Init<MsgType, CallbackType>(
      config, std::forward<CallbackType>(callback), type_name);
  return subscriber;
}

// Create a subscriber that is able to send message ack to the publisher.
template <typename MsgType, typename CallbackType>
Subscriber CreateSubscriberWithAck(
    const std::string& topic_name, int32_t queue_size, CallbackType&& callback,
    const TransportType& trans_type = GetTransportType(),
    const ProcessorType& processor_type = ProcessorType::kDedicateThread) {
  SubscriberConfig config(topic_name, trans_type, processor_type, queue_size);
  config.send_ack = true;
  if (::av_comm::GetFeatureSelection().disable_publish_with_ack()) {
    LOG(WARNING) << "Publish with ack is disabled by cloud config, setting "
                    "config.send_ack as false to disable any ack related "
                    "actions on subscriber side";
    config.send_ack = false;
  }
  Subscriber subscriber;
  subscriber.Init<MsgType, CallbackType>(config,
                                         std::forward<CallbackType>(callback),
                                         utils::GetTypeName<MsgType>());
  return subscriber;
}

/**
 * @brief Create a Publisher object
 *
 * @tparam MsgType The type of message.
 * @param topic_name Topic name, string.
 * @param type_name The type name string of MsgType.
 * @param trans_type Transport type.
 * @return Publisher
 */
template <typename MsgType>
Publisher CreatePublisher(
    const std::string& topic_name,
    const std::string& type_name = utils::GetTypeName<MsgType>(),
    const TransportType& trans_type = GetTransportType(),
    int32_t timeout_ms = -1) {
  TransportType real_trans_type = trans_type;
  if (varch::vnode::GetVArchGlobalData()->IsReplayTestMode()) {
    real_trans_type = TransportType::kIntraProcess;
  } else if (trans_type == TransportType::kPCIe && av_comm::IsGen4Platform()) {
    if (!ntb_bridge::IsGen4NtbDriverLoaded()) {
      real_trans_type = TransportType::kUDP;
      LOG(WARNING) << "Gen4 NTB driver is not loaded, fallback to UDP "
                      "transport type for topic: "
                   << topic_name;
    }
  }
  PublisherOption option(topic_name, real_trans_type);
  option.timeout = timeout_ms;
  Publisher publisher;
  publisher.Init<MsgType>(option, type_name);
  return publisher;
}

// Same as above, just return a shared_ptr of the created publisher.
template <typename MsgType>
std::shared_ptr<Publisher> CreatePublisherPtr(
    const std::string& topic_name,
    const std::string& type_name = utils::GetTypeName<MsgType>(),
    const TransportType& trans_type = GetTransportType(),
    int32_t timeout_ms = -1) {
  TransportType real_trans_type = trans_type;
  if (varch::vnode::GetVArchGlobalData()->IsReplayTestMode()) {
    real_trans_type = TransportType::kIntraProcess;
  } else if (trans_type == TransportType::kPCIe && av_comm::IsGen4Platform()) {
    if (!ntb_bridge::IsGen4NtbDriverLoaded()) {
      real_trans_type = TransportType::kUDP;
      LOG(WARNING) << "Gen4 NTB driver is not loaded, fallback to UDP "
                      "transport type for topic: "
                   << topic_name;
    }
  }
  PublisherOption option(topic_name, real_trans_type);
  option.timeout = timeout_ms;
  auto publisher = std::make_shared<Publisher>();
  publisher->Init<MsgType>(option, type_name);
  return publisher;
}

/**
 * @brief Create a Publisher object that requires subscriber to send ack after
 * receiving it. TODO(shuoyang): for now, it simply returns a regular publisher,
 * implementation will follow.
 *
 * @param topic_name Topic name.
 * @param ack_level Ack level, defined at: varch/protos/vnode/message_ack.proto
 * @param ack_timeout_sec Wait for ack times out after this many seconds.
 * @return Publisher
 */
template <typename MsgType>
Publisher CreatePublisherWithAck(const std::string& topic_name,
                                 MessageAck::AckLevel ack_level,
                                 MessageAck::ReceiveMode ack_recv_mode,
                                 int ack_timeout_ms) {
  LOG(INFO) << "Creating publisher for topic " << topic_name
            << " that requires ack";

  auto trans_type = varch::vnode::GetVArchGlobalData()->IsReplayTestMode()
                        ? TransportType::kIntraProcess
                        : TransportType::kUDP;
  PublisherOption option(topic_name, trans_type);
  option.ack_option = {ack_level, ack_recv_mode, ack_timeout_ms};
  LOG(INFO) << "Ack config: " << option.ack_option;

  Publisher publisher;
  publisher.Init<MsgType>(option, utils::GetTypeName<MsgType>());

  return publisher;
}

}  // namespace message_util
}  // namespace vnode
}  // namespace varch

#endif  // ONBOARD_VARCH_VNODE_MESSAGE_UTIL_H
