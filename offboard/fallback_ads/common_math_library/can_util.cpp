#include "common_math_library/can_util.h"

#include <algorithm>
#include <cstdint>

namespace fallback_adas {
namespace common {

void Uint64ToBytes(uint64_t value, uint8_t* array, std::size_t size,
                   bool is_big_endian) {
  size = std::min(size, static_cast<std::size_t>(8));

  if (is_big_endian) {
    for (std::size_t i = 0; i < size; ++i) {
      array[i] = static_cast<uint8_t>((value >> ((size - 1 - i) * 8)) & 0xFF);
    }
  } else {
    for (std::size_t i = 0; i < size; ++i) {
      array[i] = static_cast<uint8_t>((value >> (i * 8)) & 0xFF);
    }
  }
}

void BytesToUint64(const uint8_t* array, std::size_t size, uint64_t* value,
                   bool is_big_endian) {
  *value = 0;
  size = std::min(size, static_cast<std::size_t>(8));

  if (is_big_endian) {
    for (std::size_t i = 0; i < size; ++i) {
      *value |= static_cast<uint64_t>(array[i]) << ((size - 1 - i) * 8);
    }
  } else {
    for (std::size_t i = 0; i < size; ++i) {
      *value |= static_cast<uint64_t>(array[i]) << (i * 8);
    }
  }
}

}  // namespace common
}  // namespace fallback_adas
