#include "boundary_builder.h"

#include <algorithm>
#include <cmath>
#include <experimental/filesystem>
#include <limits>
#include <map>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <utility>
#include <vector>

#include <glog/logging.h>
#include <pcl/io/ply_io.h>
#include <pcl/point_types.h>

#include "change_detection/change_detection_utils.h"
#include "common/semantic_mapping_gflags.h"
#include "mapping/online_map/tracking/utils.h"
#include "road_cognition/io/visual_stream_io.h"
#include "road_cognition/utils/config_util.hpp"
#include "road_cognition/utils/geometry_util.hpp"
#include "slam_protos/online_map_meta.pb.h"

namespace {
const std::unordered_set<voy::perception::segmentation::Type> kCurbTypes{
    voy::perception::segmentation::Type::VEGETATION,
    voy::perception::segmentation::Type::BUILDING,
    voy::perception::segmentation::Type::WALL,
    voy::perception::segmentation::Type::FENCE,
    voy::perception::segmentation::Type::CURB,
    voy::perception::segmentation::Type::TRAFFIC_CONE,
    voy::perception::segmentation::Type::BARRIER};

const std::unordered_set<voy::perception::segmentation::Type> kUnreleventTypes{
    voy::perception::segmentation::Type::SPLASH};

const std::unordered_set<voy::perception::ObjectType> kDynamicObjectTypes{
    voy::perception::ObjectType::VEHICLE, voy::perception::ObjectType::UNKNOWN,
    voy::perception::ObjectType::CYCLIST};

constexpr int kLogFrequency{20};  // 20 * 100ms = 2s/(per log)

constexpr char kReconBoundaryStreamName[]{"Boundary_Recon"};

constexpr double kDefaultVertexUncertainties{
    0.5};  // Set default rmse to 0.5 when tracking missing vertext
           // uncertainties, unit:m.

constexpr double kEpsilon = 1e-2;  // Epsilon to judge float/double zero.
constexpr double kDistDeltaJudgeEndInRoi{
    5.0};  // Distance to judge boundary end is in Roi. Unit: m

static constexpr char kRawBoundaryStreamName[]{"Boundary_RawPts"};
}  // namespace

namespace slam {
namespace semantic_mapping {
namespace road_cognition {
namespace fs = std::experimental::filesystem;
bool BoundaryBuilder::Init() {
  if (FLAGS_enable_road_cognition_debug) {
    debug_outpath_ =
        fs::path(FLAGS_road_cognition_debug_outpath) / "BoundaryBuilder";
    if (!fs::exists(debug_outpath_) &&
        !fs::create_directories(debug_outpath_)) {
      return false;
    }
  }
  return true;
}

void BoundaryBuilder::Process(
    const PosePtr& pose,
    const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
    const std::shared_ptr<const voy::Obstacles>& obstacles,
    const std::shared_ptr<const perception::ScanMessage>& scan_msg,
    const std::shared_ptr<const voy::TrackedObjectList>& tracked_objects,
    std::shared_ptr<proto::TrackedMapElementList>& filtered_percp_map,
    std::shared_ptr<proto::HeightDiffGrid>& output_grid_ptr) {
  if (percp_map) {
    const PosePtr header_pose = std::make_shared<Pose>(
        math::GetQuaternion(percp_map->pose().yaw(), percp_map->pose().pitch(),
                            percp_map->pose().roll()),
        Eigen::Vector3d(percp_map->pose().x(), percp_map->pose().y(),
                        percp_map->pose().z()));
    voxel_util_ptr_ =
        CreateExistenceVoxel(header_pose, scan_msg, tracked_objects, obstacles);
  }
  if (PreProcessPose(pose)) {
    auto single_frame_boundaries =
        PreProcessFrame(pose, percp_map, voxel_util_ptr_);
    TrackBoundary(single_frame_boundaries);
    DLOG_EVERY_N(INFO, 10) << "RoadBoundary processing, dist = " << pose->dist;
  }

  const auto current_pose = pose ? pose : prev_pose_;
  const auto output_boundaries = UpdateBoundary(current_pose);
  ConvertToProto(percp_map, filtered_percp_map, output_boundaries);
  ConvertToProto(voxel_util_ptr_, output_grid_ptr);
}

bool BoundaryBuilder::PreProcessPose(const PosePtr& pose) {
  if (!pose) {
    return false;
  }

  // Check Quality
  if (pose->is_localization_fault ||
      pose->status != voy::Pose::Status::Pose_Status_CONVERGED) {
    LOG_EVERY_N(WARNING, kLogFrequency)
        << "BoundaryBuilder pending. Pose status is not CONVERGED or "
           "localization is not NORMAL! Ts="
        << pose->ts;
    return false;
  }

  // Downsample to key frame according to distance
  if (!prev_pose_ ||
      pose->dist - prev_pose_->dist >
          ConfigUtil::Instance()->Config().online_map_sample_distance()) {
    prev_pose_ = pose;
    return true;
  }

  return false;
}

Voxel2DUtilPtr BoundaryBuilder::CreateExistenceVoxel(
    const PosePtr& pose,
    const std::shared_ptr<const perception::ScanMessage>& scan_msg,
    const std::shared_ptr<const voy::TrackedObjectList>& tracked_objects,
    const std::shared_ptr<const voy::Obstacles>& obstacles) const {
  if (!pose) {
    return nullptr;
  }

  Voxel2DUtilPtr voxel_util_ptr = std::make_shared<Voxel2DUtil>(
      ConfigUtil::Instance()->Config().voxel_roi_front(),
      ConfigUtil::Instance()->Config().voxel_roi_back(),
      ConfigUtil::Instance()->Config().voxel_roi_left(),
      ConfigUtil::Instance()->Config().voxel_roi_right(),
      ConfigUtil::Instance()->Config().voxel_grid_size());
  if (voxel_util_ptr->Init()) {
    ExtractToVoxel(pose, scan_msg, voxel_util_ptr);
    ExtractToVoxel(pose, tracked_objects, voxel_util_ptr);
    ExtractToVoxel(pose, obstacles, voxel_util_ptr);
  }

  return voxel_util_ptr;
}

std::vector<LaneBoundarySinglePtr> BoundaryBuilder::PreProcessFrame(
    const PosePtr& pose,
    const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
    const Voxel2DUtilPtr& voxel_util_ptr) const {
  if (!percp_map || !pose) {
    return {};
  }
  const PosePtr header_pose = std::make_shared<Pose>(
      math::GetQuaternion(percp_map->pose().yaw(), percp_map->pose().pitch(),
                          percp_map->pose().roll()),
      Eigen::Vector3d(percp_map->pose().x(), percp_map->pose().y(),
                      percp_map->pose().z()));

  const int64_t timestamp{percp_map->timestamp()};
  if (FLAGS_enable_road_cognition_debug) {
    OutputDebugPly(voxel_util_ptr, percp_map);
  }

  std::vector<LaneBoundarySinglePtr> single_frame_boundaries;
  for (const auto& msg : percp_map->tracked_elements()) {
    if (!IsTargetElement(msg)) {
      continue;
    }
    if (online_map::tracking::IsTrackedElementWithTypeAndAttribute(
            msg, proto::OnlineMapElementType::FENCE,
            proto::MapElementAttribute::IS_POLE_LIKE_FENCE)) {
      DLOG(WARNING) << "HB_id = " << msg.id() << ", is pole_like";
      continue;
    }

    std::vector<glm::dvec3> car_center_points;
    car_center_points.reserve(msg.fused_line().points().size());
    for (const auto& point : msg.fused_line().points()) {
      car_center_points.emplace_back(point.x(), point.y(), point.z());
    }

    if (car_center_points.size() <= 1) {
      continue;
    }

    const bool is_horizontal = IsHorizontal(car_center_points);
    LOG_IF(INFO, is_horizontal) << "TkId=" << msg.id() << " is horizontal!";
    const auto indexes =
        GetIndexOfPolyline(pose, car_center_points, is_horizontal);
    if (indexes.size() != car_center_points.size()) {
      LOG(WARNING) << "Get polyline index failed, will ignore obj TkId= "
                   << msg.id();
      continue;
    }

    const double observe_angle_rad =
        is_horizontal ? M_PI_2 : GetObserveAngleRad(car_center_points);

    // Trans to self-defined struct
    LaneBoundarySinglePtr single_frame_boundary =
        std::make_shared<LaneBoundarySingle>();
    single_frame_boundary->type = msg.type().type();
    single_frame_boundary->track_id = msg.id();
    single_frame_boundary->confidence = msg.confidence();
    single_frame_boundary->timestamp = timestamp;
    single_frame_boundary->direction = CalElementDirection(car_center_points);
    std::for_each(msg.attributes().begin(), msg.attributes().end(),
                  [&](const proto::MapElementAttribute& attr) {
                    single_frame_boundary->attr.emplace_back(attr.attr(),
                                                             attr.confidence());
                  });

    for (size_t i = 0; i < car_center_points.size(); ++i) {
      auto&& car_center_point = car_center_points[i];
      auto&& raw_index{indexes[i]};

      Eigen::Vector3d global_pt =
          header_pose->q * Eigen::Vector3d(car_center_point.x,
                                           car_center_point.y,
                                           car_center_point.z) +
          header_pose->p;
      ObsPoint obs_pt(
          raw_index, car_center_point.x, car_center_point.y,
          msg.vertex_uncertainties().empty() ? kDefaultVertexUncertainties
                                             : msg.vertex_uncertainties(i),
          observe_angle_rad, msg.vertex_widths(i), timestamp, global_pt);

      if (voxel_util_ptr && TypeUtil::IsHardBoundary(msg.type().type())) {
        const auto& voxel_type =
            voxel_util_ptr->Query(car_center_point.x, car_center_point.y);

        const double diff_z = voxel_util_ptr->QueryHeightDiffWithLeftRight(
            car_center_point.x, car_center_point.y,
            ConfigUtil::Instance()->Config().voxel_grid_size());

        obs_pt.existence_status = ExistenceStatus::Unknown;
        if (diff_z > ConfigUtil::Instance()->Config().hb_lidar_z_diff_thres() ||
            voxel_type == Voxel2DUtil::kVoxelType::kCurb) {
          obs_pt.existence_status = ExistenceStatus::Confirmed;
        }
        if (voxel_type == Voxel2DUtil::kVoxelType::kMovingObj ||
            voxel_type == Voxel2DUtil::kVoxelType::kNoise) {
          obs_pt.existence_status = ExistenceStatus::Removed;
        }
      }

      single_frame_boundary->raw_obs_pts.emplace(raw_index, std::move(obs_pt));
    }
    single_frame_boundary->index =
        single_frame_boundary->raw_obs_pts.begin()->first;
    if (TypeUtil::IsHardBoundary(static_cast<proto::OnlineMapElementType::Type>(
            single_frame_boundary->type)) &&
        // Hard-Boundary's end is fully in ROI. Will cut unexists pts.
        single_frame_boundary->raw_obs_pts.rbegin()->second.obs_x <
            (ConfigUtil::Instance()->Config().online_map_roi_front() -
             kDistDeltaJudgeEndInRoi)) {
      CutBoundaryEndByStatus(single_frame_boundary);
    }
    if (!single_frame_boundary->raw_obs_pts.empty()) {
      single_frame_boundaries.emplace_back(single_frame_boundary);
    }
  }
  return single_frame_boundaries;
}

std::vector<double> BoundaryBuilder::GetIndexOfPolyline(
    const PosePtr& pose, const std::vector<glm::dvec3>& car_center_points,
    bool is_horizontal) const {
  if (!pose || car_center_points.empty()) {
    return {};
  }
  std::vector<double> indexes;
  indexes.reserve(car_center_points.size());
  if (!is_horizontal) {
    for (const auto& car_center_point : car_center_points) {
      indexes.emplace_back(pose->dist + car_center_point.x);
    }
  } else {
    const auto min_x =
        std::min_element(
            car_center_points.begin(), car_center_points.end(),
            [](const glm::vec3& a, const glm::vec3& b) { return a.x < b.x; })
            ->x;
    const auto min_y{car_center_points.begin()->y};
    for (const auto& car_center_point : car_center_points) {
      indexes.emplace_back(pose->dist + min_x +
                           std::abs(car_center_point.y - min_y));
    }
  }
  return indexes;
}

bool BoundaryBuilder::IsTargetElement(
    const slam::proto::TrackedMapElement& element) const {
  return TypeUtil::IsPolylineType(element.type().type()) &&
         element.status() == proto::TrackStatus::CONFIRMED;
}

bool BoundaryBuilder::IsHorizontal(
    const std::vector<glm::dvec3>& car_center_points) const {
  if (car_center_points.size() <= 1) {
    return false;
  }

  bool x_increasing{true};
  bool x_decreasing{true};
  for (size_t i = 1; i < car_center_points.size(); i++) {
    if (car_center_points[i].x > car_center_points[i - 1].x) {
      x_decreasing = false;
    } else if (car_center_points[i].x < car_center_points[i - 1].x) {
      x_increasing = false;
    }
  }
  double length =
      std::abs(car_center_points.rbegin()->x - car_center_points.begin()->x);
  double width =
      std::abs(car_center_points.rbegin()->y - car_center_points.begin()->y);

  return !(x_increasing || x_decreasing) && (length < width) && length < 1.0;
}

ElementDirection BoundaryBuilder::CalElementDirection(
    const std::vector<glm::dvec3>& car_center_points) const {
  if (car_center_points.size() <= 1) {
    return ElementDirection::Unknown;
  }

  return car_center_points.front().x < car_center_points.back().x
             ? ElementDirection::Forward
             : ElementDirection::Backward;
}

double BoundaryBuilder::GetObserveAngleRad(
    const std::vector<glm::dvec3>& car_center_points) const {
  if (car_center_points.size() <= 1) {
    return 0.0;
  }
  auto&& start_pt = car_center_points.front();
  auto&& end_pt = car_center_points.back();
  return std::atan((end_pt.y - start_pt.y) / (end_pt.x - start_pt.x));
}
void BoundaryBuilder::CutBoundaryEndByStatus(
    LaneBoundarySinglePtr& boundary) const {
  auto&& points = boundary->raw_obs_pts;
  // Skip invalid HB.
  if (points.size() <= 1 ||
      !TypeUtil::IsHardBoundary(
          static_cast<slam::proto::OnlineMapElementType::Type>(
              boundary->type))) {
    return;
  }

  constexpr double kCutPointsMaxIdx{
      20.0};  // Max cut length of HB when no fp. Unit:m
  constexpr double kCutFpPointsMaxIdx{
      50.0};  // Max cut length of HB when fp. Unit:m
  constexpr int kRequiredConsecutivePtNum{
      3};  // Only cut when consecutive pts found. Unit: pts num.

  const double raw_idx_begin{points.begin()->first};
  const double raw_idx_end{points.rbegin()->first};
  const size_t before_cut_num{points.size()};

  // Get cut position reference.
  bool find_consecutive_exist_pt{false}, find_begin_of_fp_seg{false};
  auto last_exist_pt_rit = points.rend();
  auto begin_of_fp_seg_rit = points.rend();
  int consecutive_confirmed_count = 0;

  for (auto rit = points.rbegin(); rit != points.rend(); ++rit) {
    // Find last existence point.
    if (rit->second.existence_status == ExistenceStatus::Confirmed) {
      consecutive_confirmed_count++;
      if (!find_consecutive_exist_pt &&
          consecutive_confirmed_count >= kRequiredConsecutivePtNum) {
        last_exist_pt_rit = rit;
        find_consecutive_exist_pt = true;
      }
    } else {
      consecutive_confirmed_count = 0;
    }

    // Find FP begin.
    if (!find_begin_of_fp_seg &&
        rit->second.existence_status == ExistenceStatus::Removed &&
        std::next(rit) != points.rend() &&
        std::next(rit)->second.existence_status != ExistenceStatus::Removed) {
      begin_of_fp_seg_rit = rit;
      find_begin_of_fp_seg = true;
    }
    if (find_consecutive_exist_pt && find_begin_of_fp_seg) {
      break;
    }
  }
  DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
      << "CutHB TkId=" << boundary->track_id << ",found exist idx="
      << (find_consecutive_exist_pt ? last_exist_pt_rit->first : 0.0)
      << ",find fp idx="
      << (find_begin_of_fp_seg ? begin_of_fp_seg_rit->first : 0.0)
      << ", range=[" << raw_idx_begin << "," << raw_idx_end
      << "], pts num=" << before_cut_num;

  // There is no existence point. Will not cut because no polygon will be
  // created.
  // TODO(weiyanchao): Consider erase when too much fp and no existence.
  if (!find_consecutive_exist_pt) {
    points.clear();
    DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
        << "CutHB TkId=" << boundary->track_id
        << ", no consecutive existence points found, can't cut. Will "
           "erase.";
    return;
  }

  // All end pts are confirmed, no need to cut.
  if (std::distance(points.rbegin(), last_exist_pt_rit) <=
      kRequiredConsecutivePtNum) {
    DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
        << "CutHB TkId=" << boundary->track_id
        << " end points are all exist. No need to cut.";
    return;
  }

  // If find fp seg, but fp seg is before last existence point, ignore it.
  // e.g. exist......fp..fp...exist....exist. FP in middle usually not a big
  // problem.
  if (find_begin_of_fp_seg &&
      (last_exist_pt_rit->first > begin_of_fp_seg_rit->first)) {
    find_begin_of_fp_seg = false;
  }

  // Do not cut too much. Fp will cut more.
  auto force_keep_pos_it =
      find_begin_of_fp_seg
          ? points.upper_bound(raw_idx_end - kCutFpPointsMaxIdx)
          : points.upper_bound(raw_idx_end - kCutPointsMaxIdx);

  // Adjust last exist point position. Fp will cut more.
  last_exist_pt_rit =
      find_begin_of_fp_seg
          ? last_exist_pt_rit
          : std::prev(last_exist_pt_rit, kRequiredConsecutivePtNum);

  bool use_force_cut{false};
  if (force_keep_pos_it == points.end() ||
      last_exist_pt_rit->first > force_keep_pos_it->first) {
    // Cut position : std::next(std::prev(rit.base()))
    points.erase(last_exist_pt_rit.base(), points.end());
  } else {
    points.erase(force_keep_pos_it, points.end());
    use_force_cut = true;
  }

  if (points.size() <= 1) {
    points.clear();
  }

  DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
      << "CutHB TkId=" << boundary->track_id
      << ",find_fp=" << find_begin_of_fp_seg
      << ",can_be_cut=" << find_consecutive_exist_pt
      << ",force_cut=" << use_force_cut
      << ",removed pts num=" << before_cut_num - points.size()
      << ",before dist=" << raw_idx_end << ",size=" << before_cut_num
      << ",after dist=" << (points.empty() ? 0.0 : points.rbegin()->first)
      << ",after size=" << points.size();
}

void BoundaryBuilder::ExtractToVoxel(
    const PosePtr& pose,
    const std::shared_ptr<const perception::ScanMessage>& scan_msg,
    Voxel2DUtilPtr& voxel_util) const {
  if (!scan_msg || !voxel_util || !pose) {
    return;
  }

  const Eigen::Matrix4d t_global_to_veh{pose->GetMatrix().inverse()};
  const auto unique_points =
      scan_msg->GetStrongestPclWrapper(voy::Sensor::LIDAR_64);
  const auto unique_points_ptr = unique_points.Data();
  const auto unique_points_size = unique_points.Size();
  if (unique_points_ptr && unique_points_size > 0) {
    for (size_t i = 0; i < unique_points_size; ++i) {
      const perception::VPoint& pt = unique_points_ptr[i];
      const Eigen::Vector4d pt_veh{t_global_to_veh *
                                   Eigen::Vector4d(pt.x, pt.y, pt.z, 1.0)};
      if (pt_veh.z() < ConfigUtil::Instance()->Config().scan_lidar_min_z() ||
          pt_veh.z() > ConfigUtil::Instance()->Config().scan_lidar_max_z()) {
        continue;
      }
      voxel_util->AddHeight(pt_veh.x(), pt_veh.y(), pt_veh.z());
    }
  }
}

void BoundaryBuilder::ExtractToVoxel(
    const PosePtr& pose,
    const std::shared_ptr<const voy::TrackedObjectList>& tracked_objs,
    Voxel2DUtilPtr& voxel_util) const {
  if (!tracked_objs || !voxel_util || !pose) {
    return;
  }

  for (const auto& tracked_obj : tracked_objs->tracked_objects()) {
    if (kDynamicObjectTypes.find(tracked_obj.object_type()) ==
        kDynamicObjectTypes.end()) {
      continue;
    }
    auto objs_centers = ExtractObjectVehCenters(pose, tracked_obj);
    for (auto& obj_c_veh : objs_centers) {
      const double obj_len_ext{
          (tracked_obj.length() +
           ConfigUtil::Instance()->Config().voxel_grid_size()) /
          2};
      const double obj_wdt_ext{
          (tracked_obj.width() +
           ConfigUtil::Instance()->Config().voxel_grid_size()) /
          2};
      Eigen::Vector3d vertex_1 =
          Eigen::AngleAxisd(obj_c_veh.w(), Eigen::Vector3d::UnitZ()) *
              Eigen::Vector3d(-obj_len_ext, obj_wdt_ext, 0) +
          obj_c_veh.head(3);

      Eigen::Vector3d vertex_2 =
          Eigen::AngleAxisd(obj_c_veh.w(), Eigen::Vector3d::UnitZ()) *
              Eigen::Vector3d(obj_len_ext, obj_wdt_ext, 0) +
          obj_c_veh.head(3);
      Eigen::Vector3d vertex_3 =
          Eigen::AngleAxisd(obj_c_veh.w(), Eigen::Vector3d::UnitZ()) *
              Eigen::Vector3d(obj_len_ext, -obj_wdt_ext, 0) +
          obj_c_veh.head(3);
      Eigen::Vector3d vertex_4 =
          Eigen::AngleAxisd(obj_c_veh.w(), Eigen::Vector3d::UnitZ()) *
              Eigen::Vector3d(-obj_len_ext, -obj_wdt_ext, 0) +
          obj_c_veh.head(3);

      std::vector<std::pair<double, double>> box_vertexes{
          {vertex_1.x(), vertex_1.y()},
          {vertex_2.x(), vertex_2.y()},
          {vertex_3.x(), vertex_3.y()},
          {vertex_4.x(), vertex_4.y()}};

      voxel_util->FillBox(box_vertexes);
    }
  }
}

std::vector<Eigen::Vector4d> BoundaryBuilder::ExtractObjectVehCenters(
    const PosePtr& pose, const voy::TrackedObject& tracked_obj,
    bool extract_history_centers) const {
  constexpr double kRoiExtendDistance{5.0};

  const auto func_is_obj_in_range = [&](const double pt_x,
                                        const double pt_y) -> bool {
    return pt_x > ConfigUtil::Instance()->Config().online_map_roi_back() -
                      kRoiExtendDistance &&
           pt_x < ConfigUtil::Instance()->Config().online_map_roi_front() +
                      kRoiExtendDistance &&
           pt_y > ConfigUtil::Instance()->Config().online_map_roi_right() -
                      kRoiExtendDistance &&
           pt_y < ConfigUtil::Instance()->Config().online_map_roi_left() +
                      kRoiExtendDistance;
  };

  // Variable history_objs is record of poses in pass.
  std::vector<Eigen::Vector4d> history_objs;

  const Eigen::Matrix4d t_global_to_veh{pose->GetMatrix().inverse()};
  const double car_yaw{math::GetEulerAngles(pose->q).x()};
  // Variable pt_veh is current pose of vehicle.
  Eigen::Vector4d pt_veh =
      t_global_to_veh * Eigen::Vector4d(tracked_obj.center_x(),
                                        tracked_obj.center_y(),
                                        tracked_obj.center_z(), 1.0);

  if (func_is_obj_in_range(pt_veh.x(), pt_veh.y())) {
    history_objs.emplace_back(
        Eigen::Vector4d(pt_veh.x(), pt_veh.y(), pt_veh.z(),
                        tracked_obj.box_heading() - car_yaw));
  }

  if (!extract_history_centers) {
    return history_objs;
  }

  const int obj_size = std::min(tracked_obj.prev_centers_size(),
                                tracked_obj.prev_headings_size());
  for (int i = obj_size - 1; i >= 0; --i) {
    pt_veh =
        t_global_to_veh * Eigen::Vector4d(tracked_obj.prev_centers(i).x(),
                                          tracked_obj.prev_centers(i).y(),
                                          tracked_obj.prev_centers(i).z(), 1.0);

    Eigen::Vector4d prev_obj(pt_veh.x(), pt_veh.y(), pt_veh.z(),
                             tracked_obj.prev_headings(i) - car_yaw);

    if (!history_objs.empty() &&
        (history_objs.back() - prev_obj).head(2).norm() <
            std::max(tracked_obj.length(), 1.0)) {
      continue;
    }

    if (func_is_obj_in_range(pt_veh.x(), pt_veh.y())) {
      history_objs.emplace_back(prev_obj);
    }
  }

  return history_objs;
}

void BoundaryBuilder::ExtractToVoxel(
    const PosePtr& pose, const std::shared_ptr<const voy::Obstacles>& obstacles,
    Voxel2DUtilPtr& voxel_util) const {
  if (!obstacles || !voxel_util) {
    return;
  }

  const Eigen::Matrix4d t_global_to_veh{pose->GetMatrix().inverse()};
  auto&& voxel_grid_size = ConfigUtil::Instance()->Config().voxel_grid_size();

  auto fill_type_func = [&](const Eigen::Vector4d& pt_veh,
                            Voxel2DUtil::kVoxelType type) {
    voxel_util->Add(pt_veh.x(), pt_veh.y(), type);
    voxel_util->Add(pt_veh.x() + voxel_grid_size, pt_veh.y(), type);
    voxel_util->Add(pt_veh.x() - voxel_grid_size, pt_veh.y(), type);
    voxel_util->Add(pt_veh.x(), pt_veh.y() + voxel_grid_size, type);
    voxel_util->Add(pt_veh.x(), pt_veh.y() - voxel_grid_size, type);
  };

  for (const auto& pt : obstacles->obstacles()) {
    if (kCurbTypes.find(pt.type()) != kCurbTypes.end()) {
      const Eigen::Vector4d pt_veh{
          t_global_to_veh *
          Eigen::Vector4d(pt.x(), pt.y(), pt.ground_z(), 1.0)};
      fill_type_func(pt_veh, Voxel2DUtil::kVoxelType::kCurb);
    }
    if (kUnreleventTypes.find(pt.type()) != kUnreleventTypes.end()) {
      const Eigen::Vector4d pt_veh{
          t_global_to_veh *
          Eigen::Vector4d(pt.x(), pt.y(), pt.ground_z(), 1.0)};
      fill_type_func(pt_veh, Voxel2DUtil::kVoxelType::kNoise);
    }
  }
}

void BoundaryBuilder::TrackBoundary(
    std::vector<LaneBoundarySinglePtr>& single_frame_boundaries) {
  if (FLAGS_enable_road_cognition_visualization) {
    VisualStreamIo::ClearStream(kRawBoundaryStreamName);
  }

  for (auto& single_frame_boundary : single_frame_boundaries) {
    if (!single_frame_boundary || single_frame_boundary->raw_obs_pts.empty()) {
      continue;
    }
    if (FLAGS_enable_road_cognition_visualization) {
      VisualStreamIo::AppendVisualStream<double, ObsPoint>(
          kRawBoundaryStreamName, single_frame_boundary->raw_obs_pts);
    }

    const auto& track_id{single_frame_boundary->track_id};
    if (tracker_map_.count(track_id)) {
      if (IsFixBoundaryNeedReset(track_id, single_frame_boundary->raw_obs_pts,
                                 tracker_map_[track_id]->GetFixPoints())) {
        tracker_map_.erase(track_id);
        BoundaryTrackerPtr boundary_tracker =
            std::make_shared<BoundaryTracker>(*single_frame_boundary);
        tracker_map_.emplace(track_id, boundary_tracker);
        LOG(WARNING) << "TkId=" << track_id
                     << " reset to new frame because of large "
                        "diff with fixed points.";
      } else {
        tracker_map_[track_id]->Track(single_frame_boundary);
      }
    } else {
      BoundaryTrackerPtr boundary_tracker =
          std::make_shared<BoundaryTracker>(*single_frame_boundary);
      tracker_map_.emplace(track_id, boundary_tracker);
    }
  }
}

bool BoundaryBuilder::IsFixBoundaryNeedReset(
    const int64_t& track_id, const std::map<double, ObsPoint>& src_boundary,
    const std::map<double, ObsPoint>& tgt_boundary) {
  constexpr double kResetDistanceThres{1.0};
  constexpr double kResetExistenceRatioThres{0.3};

  const auto func_is_exist =
      [](const std::pair<double, ObsPoint>& obs_pt) -> bool {
    return obs_pt.second.existence_status == ExistenceStatus::Confirmed;
  };

  if (src_boundary.empty() || tgt_boundary.empty()) {
    return false;
  }

  const double src_existence_ratio =
      static_cast<double>(std::count_if(src_boundary.begin(),
                                        src_boundary.end(), func_is_exist)) /
      src_boundary.size();
  const double tgt_existence_ratio =
      static_cast<double>(std::count_if(tgt_boundary.begin(),
                                        tgt_boundary.end(), func_is_exist)) /
      tgt_boundary.size();
  if (tgt_existence_ratio > kResetExistenceRatioThres ||
      src_existence_ratio < tgt_existence_ratio) {
    return false;
  }

  double horizontal_proj_dist{0.0};
  if (CalProjDistance(src_boundary, tgt_boundary, horizontal_proj_dist)) {
    if (horizontal_proj_dist > kResetDistanceThres) {
      DLOG(WARNING) << "NeedReset TkId=" << track_id
                    << ", find large diff with fixed points. Distance="
                    << horizontal_proj_dist
                    << ", new frame existence ratio=" << src_existence_ratio
                    << ", fixed existence ratio=" << tgt_existence_ratio;
      return true;
    }
  }
  return false;
}

bool BoundaryBuilder::CalProjDistance(
    const std::map<double, ObsPoint>& src_boundary,
    const std::map<double, ObsPoint>& tgt_boundary, double& dis) const {
  constexpr size_t kMaxProjPtsNumWithoutOverlap{5};

  double total_dis = 0;
  size_t total_count = 0;
  if (tgt_boundary.size() <= 1) {
    return false;
  }
  if (src_boundary.empty()) {
    return false;
  }
  for (const auto& pt : src_boundary) {
    auto end_pt = tgt_boundary.upper_bound(pt.first);
    auto start_pt = std::prev(end_pt);
    if (end_pt == tgt_boundary.begin() || end_pt == tgt_boundary.end()) {
      continue;
    }
    double pt_dis = GeometryUtil::PointToSegmentVerticalDistance(
        pt.second.pt, start_pt->second.pt, end_pt->second.pt);
    total_dis += pt_dis;
    total_count++;
  }
  // If there is no overlap, take the last two points of the previous frame as
  // the reference line, and calculate the average distance for the first
  // several points of the next frame
  if (total_count == 0) {
    const auto end_pt = tgt_boundary.rbegin();
    const auto start_pt = std::next(tgt_boundary.rbegin());
    total_dis = 0;
    for (const auto& it : src_boundary) {
      double pt_dis = GeometryUtil::PointToSegmentVerticalDistance(
          it.second.pt, start_pt->second.pt, end_pt->second.pt);
      total_dis += pt_dis;
      total_count++;
      if (total_count >= kMaxProjPtsNumWithoutOverlap) {
        break;
      }
    }
  }
  dis = total_dis / total_count;
  return true;
}

std::vector<ReconBoundaryPtr> BoundaryBuilder::UpdateBoundary(
    const PosePtr& current_pose) {
  if (!current_pose) {
    return {};
  }
  if (FLAGS_enable_road_cognition_visualization) {
    BoundaryTracker::ClearVisualizePts();
  }

  const double pass_index{
      current_pose->dist +
      ConfigUtil::Instance()->Config().local_online_map_roi_back()};
  const double fixed_index{current_pose->dist};
  const double update_index{
      current_pose->dist +
      ConfigUtil::Instance()->Config().local_online_map_roi_front()};

  std::vector<ReconBoundaryPtr> output_boundaries;
  for (auto it = tracker_map_.begin(); it != tracker_map_.end();) {
    bool is_able_to_remove{false};
    const auto updated_boundary = it->second->Update(
        pass_index, fixed_index, update_index, is_able_to_remove);
    if (updated_boundary && updated_boundary->points.size() > 1) {
      output_boundaries.emplace_back(updated_boundary);
    }
    if (is_able_to_remove) {
      DLOG(WARNING) << "Boundary id=" << updated_boundary->track_id
                    << " erased by out of observation.";
      it = tracker_map_.erase(it);
      continue;
    }
    it++;
  }

  if (FLAGS_enable_road_cognition_visualization) {
    VisualStreamIo::PublishVisualStream<ReconBoundary>(kReconBoundaryStreamName,
                                                       output_boundaries);
    VisualStreamIo::Publish();
  }

  return output_boundaries;
}

void BoundaryBuilder::ConvertToProto(
    const std::shared_ptr<const proto::TrackedMapElementList>& percp_map,
    std::shared_ptr<proto::TrackedMapElementList>& filtered_percp_map,
    const std::vector<ReconBoundaryPtr>& output_boundaries) const {
  if (!filtered_percp_map) {
    filtered_percp_map = std::make_shared<proto::TrackedMapElementList>();
  }
  if (!percp_map) {
    return;
  }

  filtered_percp_map->CopyFrom(*percp_map);
  filtered_percp_map->clear_tracked_elements();

  const Pose header_pose(
      math::GetQuaternion(percp_map->pose().yaw(), percp_map->pose().pitch(),
                          percp_map->pose().roll()),
      Eigen::Vector3d(percp_map->pose().x(), percp_map->pose().y(),
                      percp_map->pose().z()));

  const Eigen::Matrix4d t_global_to_veh{header_pose.GetMatrix().inverse()};
  for (const auto& msg : percp_map->tracked_elements()) {
    // Note(kangrong): for non-hard-boundary and tentative/supplement
    // hard-boundaries, we will pass through to downstream.
    if (!IsTargetElement(msg)) {
      filtered_percp_map->add_tracked_elements()->CopyFrom(msg);
    }
  }

  std::unordered_set<int64_t> output_hb_ids;
  for (const auto& boundary : output_boundaries) {
    output_hb_ids.insert(boundary->track_id);
    // Note(weiyanchao): road_cognition will not inherit all
    // meta infos
    auto* new_bd = filtered_percp_map->add_tracked_elements();
    new_bd->set_id(boundary->track_id);
    new_bd->set_meta_track_id(boundary->id);
    new_bd->set_status(proto::TrackStatus::CONFIRMED);
    new_bd->mutable_type()->set_type(
        static_cast<proto::OnlineMapElementType::Type>(boundary->type));

    for (const auto& [attr, attr_conf] : boundary->attr) {
      auto* new_attr = new_bd->add_attributes();
      new_attr->set_attr(static_cast<proto::MapElementAttribute::Attr>(attr));
      new_attr->set_confidence(attr_conf);
    }

    new_bd->mutable_fused_line()->Clear();
    new_bd->mutable_existences()->Clear();
    new_bd->mutable_vertex_uncertainties()->Clear();

    auto&& boundary_points = boundary->points;
    for (const auto& pt : boundary_points) {
      auto* point_proto = new_bd->mutable_fused_line()->add_points();
      // Variable pt_veh is current pose of vehicle.
      Eigen::Vector4d pt_veh =
          t_global_to_veh * Eigen::Vector4d(pt.second.pt.x(), pt.second.pt.y(),
                                            pt.second.pt.z(), 1.0);
      point_proto->set_x(pt_veh.x());
      point_proto->set_y(pt_veh.y());
      point_proto->set_z(pt_veh.z());
      new_bd->add_existences(pt.second.existence_status ==
                             ExistenceStatus::Confirmed);
      new_bd->add_vertex_uncertainties(pt.second.rmse);
      if (TypeUtil::IsFence(new_bd->type().type()) &&
          pt.second.width > kEpsilon) {
        new_bd->add_vertex_widths(pt.second.width);
      }
    }
    new_bd->set_confidence(boundary->confidence);
  }

  // Outputs pole-like fences and filtered hard boundaries as supplement.
  for (const auto& msg : percp_map->tracked_elements()) {
    if (TypeUtil::IsHardBoundary(msg.type().type()) ||
        output_hb_ids.find(msg.id()) == output_hb_ids.end() ||
        online_map::tracking::IsTrackedElementWithTypeAndAttribute(
            msg, proto::OnlineMapElementType::FENCE,
            proto::MapElementAttribute::IS_POLE_LIKE_FENCE)) {
      auto* new_bd = filtered_percp_map->add_tracked_elements();
      new_bd->CopyFrom(msg);
      new_bd->set_status(proto::TrackStatus::TENTATIVE);
    }
  }
}

void BoundaryBuilder::ConvertToProto(
    const Voxel2DUtilPtr& voxel_util_ptr,
    std::shared_ptr<proto::HeightDiffGrid>& grid_proto_ptr) {
  grid_proto_ptr = std::make_shared<proto::HeightDiffGrid>(
      voxel_util_ptr->VoxelToGridProto());
}

void BoundaryBuilder::OutputDebugPly(
    const PosePtr& pose,
    const std::shared_ptr<const voy::Obstacles>& obstacles) const {
  if (!FLAGS_enable_road_cognition_debug ||
      !fs::exists(FLAGS_road_cognition_debug_outpath)) {
    return;
  }
  if (!obstacles) {
    return;
  }

  pcl::PointCloud<pcl::PointXYZI>::Ptr debug_cloud{
      new pcl::PointCloud<pcl::PointXYZI>};
  const Eigen::Matrix4d t_global_to_veh{pose->GetMatrix().inverse()};
  for (const auto& pt : obstacles->obstacles()) {
    if (pt.type() <= voy::perception::segmentation::Type::UNKNOWN ||
        pt.type() >= voy::perception::segmentation::Type::NOT_GIVEN) {
      continue;
    }
    const Eigen::Vector4d pt_veh{
        t_global_to_veh * Eigen::Vector4d(pt.x(), pt.y(), pt.max_z(), 1.0)};

    pcl::PointXYZI point;
    point.x = pt_veh.x();
    point.y = pt_veh.y();
    point.z = pt_veh.z();
    point.intensity = pt.type();
    debug_cloud->points.emplace_back(point);
  }

  if (!debug_cloud->points.empty()) {
    const std::string debug_file_path{
        fs::path(debug_outpath_) /
        (std::to_string(pose->ts) + "_obstacles.ply")};
    pcl::io::savePLYFileBinary(debug_file_path, *debug_cloud);
  }
}

void BoundaryBuilder::OutputDebugPly(
    const Voxel2DUtilPtr& voxel_util,
    const std::shared_ptr<const proto::TrackedMapElementList>& percp_map)
    const {
  if (!FLAGS_enable_road_cognition_debug ||
      !fs::exists(FLAGS_road_cognition_debug_outpath)) {
    return;
  }
  if (!voxel_util || !percp_map) {
    return;
  }
  pcl::PointCloud<pcl::PointXYZI>::Ptr debug_cloud{
      new pcl::PointCloud<pcl::PointXYZI>};
  for (const auto& msg : percp_map->tracked_elements()) {
    if (TypeUtil::IsHardBoundary(msg.type().type())) {
      const auto& car_center_points = change_detection_utils::FusedLineToGlmPts(
          msg.fused_line(),
          ConfigUtil::Instance()->Config().online_map_roi_front(),
          ConfigUtil::Instance()->Config().online_map_roi_back(),
          ConfigUtil::Instance()->Config().online_map_boundary_sample_length());
      if (car_center_points.size() <= 1) {
        continue;
      }

      for (const auto& pt_veh : car_center_points) {
        pcl::PointXYZI point;
        point.x = pt_veh.x;
        point.y = pt_veh.y;
        point.z = pt_veh.z;
        point.intensity = 0;
        const auto voxel_type = voxel_util->Query(pt_veh.x, pt_veh.y);

        double height_diff = voxel_util->QueryHeightDiffAround(
            pt_veh.x, pt_veh.y,
            ConfigUtil::Instance()->Config().voxel_grid_size());
        if (height_diff >
            ConfigUtil::Instance()->Config().hb_lidar_z_diff_thres()) {
          point.intensity = 255;
        }
        if (voxel_type == Voxel2DUtil::kVoxelType::kMovingObj) {
          point.intensity = 127;
        }
        debug_cloud->points.emplace_back(point);
      }
    }
  }

  if (!debug_cloud->points.empty()) {
    std::string fp_path{
        fs::path(debug_outpath_) /
        (std::to_string(percp_map->pose().timestamp()) + "_hb.ply")};
    pcl::io::savePLYFileBinary(fp_path, *debug_cloud);
  }

  pcl::PointCloud<pcl::PointXYZI>::Ptr voxel_pts{
      new pcl::PointCloud<pcl::PointXYZI>};
  for (double x = ConfigUtil::Instance()->Config().online_map_roi_back();
       x < ConfigUtil::Instance()->Config().online_map_roi_front();
       x += ConfigUtil::Instance()->Config().voxel_grid_size()) {
    for (double y = ConfigUtil::Instance()->Config().online_map_roi_right();
         y < ConfigUtil::Instance()->Config().online_map_roi_left();
         y += ConfigUtil::Instance()->Config().voxel_grid_size()) {
      const auto height_diff = voxel_util->QueryHeightDiff(x, y);
      const auto voxel_type = voxel_util->Query(x, y);
      if (voxel_type == Voxel2DUtil::kVoxelType::kMovingObj) {
        pcl::PointXYZI point;
        point.x = x;
        point.y = y;
        point.z = height_diff;
        point.intensity = 255;
        voxel_pts->points.push_back(point);
        continue;
      }

      if (voxel_type == Voxel2DUtil::kVoxelType::kCurb) {
        pcl::PointXYZI point;
        point.x = x;
        point.y = y;
        point.z = height_diff;
        point.intensity = 127;
        voxel_pts->points.push_back(point);
        continue;
      }
      if (voxel_type == Voxel2DUtil::kVoxelType::kNoise) {
        pcl::PointXYZI point;
        point.x = x;
        point.y = y;
        point.z = height_diff;
        point.intensity = 63;
        voxel_pts->points.push_back(point);
        continue;
      }

      if (height_diff >
          ConfigUtil::Instance()->Config().hb_lidar_z_diff_thres()) {
        pcl::PointXYZI point;
        point.x = x;
        point.y = y;
        point.z = height_diff;
        point.intensity = 0;
        voxel_pts->points.push_back(point);
      }
    }
  }
  if (!voxel_pts->points.empty()) {
    const std::string fp_path{
        fs::path(debug_outpath_) /
        (std::to_string(percp_map->pose().timestamp()) + "_voxel.ply")};
    pcl::io::savePLYFileBinary(fp_path, *voxel_pts);
  }
}
}  // namespace road_cognition
}  // namespace semantic_mapping
}  // namespace slam
