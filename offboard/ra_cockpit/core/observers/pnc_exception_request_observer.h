#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PNC_EXCEPTION_REQUEST_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PNC_EXCEPTION_REQUEST_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class PncExceptionHandlerObserver : public IDataObserver {
 public:
  using PncExceptionHandlerCallback = std::function<void(
      std::shared_ptr<teleassist::pb::PncExceptionHandler>, DataChannel)>;

  explicit PncExceptionHandlerObserver(PncExceptionHandlerCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  PncExceptionHandlerCallback pnc_excep_requst_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_PNC_EXCEPTION_REQUEST_OBSERVER_H_
