# code: https://git.xiaojukeji.com/ddcloud-builder/voyager/tree/master/trail-server
FROM registry.xiaojukeji.com/didionline/scenariolib-python37-base:stable
LABEL Author="<<EMAIL>>"
LABEL Description="The docker image for scenariolib project"

# Suoervisor configurations.
COPY deploy_conf/supervisor_celery.conf /etc/supervisord.d/celery.conf
COPY deploy_conf/supervisor_nginx.conf /etc/supervisord.d/nginx.conf
COPY deploy_conf/supervisor_uwsgi.conf /etc/supervisord.d/uwsgi.conf
COPY deploy_conf/supervisor_docker_sig_handle.conf /etc/supervisord.d/supervisor_docker_sig_handle.conf

# echo env
RUN env

RUN mkdir -p /home/<USER>/voyager/scenariolib
COPY . /home/<USER>/voyager/scenariolib

ADD docker_sig_handle.sh /home/<USER>/
ADD entrypoint.py /home/<USER>
RUN chmod +x /home/<USER>/entrypoint.py /home/<USER>/docker_sig_handle.sh
# Installs python packages
RUN /usr/bin/python3 -m pip install psutil -i http://************/pypi/voyager/stable/+simple/ --trusted-host ************
# Installs python packages
RUN chown -R xiaoju:xiaoju /home/<USER>/ \
    && su - xiaoju -c "cd /home/<USER>/voyager/scenariolib && ./control.sh install"

ENTRYPOINT /home/<USER>/entrypoint.py

EXPOSE 8000
