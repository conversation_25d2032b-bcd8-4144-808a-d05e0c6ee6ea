#include "planner/speed/reasoning/reasoner/lane_change_speed_reasoner/lane_change_speed_reasoner_util.h"

#include <cmath>
#include <gtest/gtest.h>

#include "math/constants.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/speed/test_util/test_util.h"

namespace planner {
namespace speed {
namespace reasoner {
TEST(LaneChangeSpeedReasonerUtilTest, GetSpeedUsingConstantAccelModel) {
  // Accel model, |a| shoule be positive.
  const double init_speed = 0.0;
  const double target_speed = 5.0;
  const double a = 1.0;

  EXPECT_DOUBLE_EQ(
      GetSpeedUsingConstantAccelModel(init_speed, target_speed, a, 1.0), 1.0);
  EXPECT_DOUBLE_EQ(
      GetSpeedUsingConstantAccelModel(init_speed, target_speed, a, 3.0), 3.0);
  EXPECT_DOUBLE_EQ(
      GetSpeedUsingConstantAccelModel(init_speed, target_speed, a, 5.0), 5.0);
  EXPECT_DOUBLE_EQ(
      GetSpeedUsingConstantAccelModel(init_speed, target_speed, a, 10.0), 5.0);
}

TEST(LaneChangeSpeedReasonerUtilTest, GetOdomUsingConstantAccelModel) {
  // Accel model, |a| shoule be positive.
  const double init_speed = 0.0;
  const double target_speed = 5.0;
  const double a = 1.0;

  EXPECT_DOUBLE_EQ(
      GetOdomUsingConstantAccelModel(init_speed, target_speed, a, 1.0), 0.5);
  EXPECT_DOUBLE_EQ(
      GetOdomUsingConstantAccelModel(init_speed, target_speed, a, 3.0), 4.5);
  EXPECT_DOUBLE_EQ(
      GetOdomUsingConstantAccelModel(init_speed, target_speed, a, 5.0), 12.5);
  EXPECT_DOUBLE_EQ(
      GetOdomUsingConstantAccelModel(init_speed, target_speed, a, 10.0), 37.5);
}

TEST(LaneChangeSpeedReasonerUtilTest,
     GetTargetYieldSpeedUsingConstantAccelModel) {
  constexpr double kToleranceError = 0.001;
  // Need deceleration. |a| shoule be negative.
  {
    const double init_speed = 5.0;
    const double target_yield_time = 5.0;
    const double target_odom = 20.0;
    const double a = -1.0;

    EXPECT_NEAR(GetTargetYieldSpeedUsingConstantAccelModel(
                    init_speed, target_yield_time, target_odom, a),
                3.873, kToleranceError);
  }

  // Need acceleration. |a| shoule be positive.
  {
    const double init_speed = 5.0;
    const double target_yield_time = 5.0;
    const double target_odom = 30.0;
    const double a = 1.0;

    EXPECT_NEAR(GetTargetYieldSpeedUsingConstantAccelModel(
                    init_speed, target_yield_time, target_odom, a),
                6.127, kToleranceError);
  }
}

TEST(LaneChangeSpeedReasonerUtilTest,
     GetStopConstraintDistanceFromRAForUrgencyInfo) {
  // Constants defined in the method.
  constexpr double kEarlierStopConstraintBufferInM = 3.0;
  constexpr double kDistanceToRemoveForSoftConstraintInM = 10.0;
  constexpr double kSoftSmoothStoppingMinAccelInMpss = -1.5;
  constexpr double kHardSmoothStoppingMinAccelInMpss = -2.5;
  constexpr double kNearNominalStopLineBufferInM = 2.0;

  // Other constants.
  constexpr double kUrgencyDistInM = 20.0;
  constexpr double kError = 0.1;
  constexpr double kMaximumSafeYieldAccelerationInMpss = -3.0;

  const vehicle_model::pb::AxleRectangularMeasurement shape_measurement =
      test_utility::ConstructRobotStateSnapshot(
          /*x=*/0.0, /*y=*/0.0, /*speed=*/0.0)
          .car_model_with_shape()
          .shape_measurement();
  const double ra_to_fb =
      shape_measurement.length() - shape_measurement.rear_bumper_to_rear_axle();
  const double dist_to_stop_line =
      kUrgencyDistInM - kEarlierStopConstraintBufferInM;
  const double dist_to_stop_line_from_fb = dist_to_stop_line - ra_to_fb;
  const double close_dist_to_stop_line = kEarlierStopConstraintBufferInM +
                                         kDistanceToRemoveForSoftConstraintInM +
                                         ra_to_fb;
  const double dist_to_stop_line_with_buffer_from_fb =
      dist_to_stop_line + kNearNominalStopLineBufferInM - ra_to_fb;
  const double soft_initial_speed_to_stop_at_stop_line =
      std::sqrt(2 * std::abs(kSoftSmoothStoppingMinAccelInMpss) *
                dist_to_stop_line_from_fb);
  const double soft_initial_speed_to_stop_at_stop_line_with_buffer =
      std::sqrt(2 * std::abs(kSoftSmoothStoppingMinAccelInMpss) *
                dist_to_stop_line_with_buffer_from_fb);
  [[maybe_unused]] const double hard_initial_speed_to_stop_at_stop_line =
      std::sqrt(2 * std::abs(kHardSmoothStoppingMinAccelInMpss) *
                dist_to_stop_line_from_fb);
  const double hard_initial_speed_to_stop_at_stop_line_with_buffer =
      std::sqrt(2 * std::abs(kHardSmoothStoppingMinAccelInMpss) *
                dist_to_stop_line_with_buffer_from_fb);

  // Test urgency info close to stop line.
  {
    const RobotStateSnapshot snapshot =
        test_utility::ConstructRobotStateSnapshot(
            /*x=*/0.0, /*y=*/0.0, /*speed=*/0.0);
    // With soft urgency info, remove stop constraint when ego is close to stop
    // line.
    {
      std::ostringstream debug_oss;
      const LaneChangeUrgencyInfo urgency_info(
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END,
          close_dist_to_stop_line - kError,
          planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE);
      EXPECT_FALSE(GetStopConstraintDistanceFromRAForUrgencyInfo(
                       /*can_trigger_crawl=*/false,
                       /*is_inexecutable_lc_signal=*/false, urgency_info,
                       snapshot, kMaximumSafeYieldAccelerationInMpss, debug_oss)
                       .has_value());
    }
    {
      std::ostringstream debug_oss;
      const LaneChangeUrgencyInfo urgency_info(
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END,
          close_dist_to_stop_line + kError,
          planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE);
      EXPECT_TRUE(GetStopConstraintDistanceFromRAForUrgencyInfo(
                      /*can_trigger_crawl=*/false,
                      /*is_inexecutable_lc_signal=*/false, urgency_info,
                      snapshot, kMaximumSafeYieldAccelerationInMpss, debug_oss)
                      .has_value());
    }
    // With hard urgency info, do not remove stop constraint when ego is close
    // to stop line.
    {
      std::ostringstream debug_oss;
      const LaneChangeUrgencyInfo urgency_info(
          planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END,
          close_dist_to_stop_line - kError,
          planner::pb::LaneChangeStopLineType::HARD_STOP_LINE);
      EXPECT_TRUE(GetStopConstraintDistanceFromRAForUrgencyInfo(
                      /*can_trigger_crawl=*/false,
                      /*is_inexecutable_lc_signal=*/false, urgency_info,
                      snapshot, kMaximumSafeYieldAccelerationInMpss, debug_oss)
                      .has_value());
    }
  }

  // Test soft urgency info stopping near the nominal stop line.
  {
    const LaneChangeUrgencyInfo urgency_info(
        planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END,
        kUrgencyDistInM, planner::pb::LaneChangeStopLineType::SOFT_STOP_LINE);
    // When ego can stop smoothly before the nominal stop line, use the nominal
    // stop line distance.
    {
      std::ostringstream debug_oss;
      const RobotStateSnapshot snapshot =
          test_utility::ConstructRobotStateSnapshot(
              /*x=*/0.0, /*y=*/0.0,
              /*speed=*/soft_initial_speed_to_stop_at_stop_line - kError,
              /*heading=*/0.0,
              /*accel=*/kSoftSmoothStoppingMinAccelInMpss);
      const std::optional<double> distance =
          GetStopConstraintDistanceFromRAForUrgencyInfo(
              /*can_trigger_crawl=*/false, /*is_inexecutable_lc_signal=*/false,
              urgency_info, snapshot, kMaximumSafeYieldAccelerationInMpss,
              debug_oss);
      DCHECK(distance.has_value());
      EXPECT_DOUBLE_EQ(dist_to_stop_line, distance.value());
    }
    // When ego can stop smoothly close to the nominal stop line, use the smooth
    // stopping distance, which is larger than the nominal stop line distance.
    {
      std::ostringstream debug_oss;
      const RobotStateSnapshot snapshot =
          test_utility::ConstructRobotStateSnapshot(
              /*x=*/0.0, /*y=*/0.0,
              /*speed=*/soft_initial_speed_to_stop_at_stop_line + kError,
              /*heading=*/0.0,
              /*accel=*/kSoftSmoothStoppingMinAccelInMpss);
      const std::optional<double> distance =
          GetStopConstraintDistanceFromRAForUrgencyInfo(
              /*can_trigger_crawl=*/false, /*is_inexecutable_lc_signal=*/false,
              urgency_info, snapshot, kMaximumSafeYieldAccelerationInMpss,
              debug_oss);
      DCHECK(distance.has_value());
      EXPECT_LT(dist_to_stop_line, distance.value());
    }
    // When ego cannot stop smoothly close to the nominal stop line and the
    // urgency info is soft, don't add stop line.
    {
      std::ostringstream debug_oss;
      const RobotStateSnapshot snapshot =
          test_utility::ConstructRobotStateSnapshot(
              /*x=*/0.0, /*y=*/0.0,
              /*speed=*/soft_initial_speed_to_stop_at_stop_line_with_buffer +
                  kError,
              /*heading=*/0.0,
              /*accel=*/kSoftSmoothStoppingMinAccelInMpss);
      const std::optional<double> distance =
          GetStopConstraintDistanceFromRAForUrgencyInfo(
              /*can_trigger_crawl=*/false, /*is_inexecutable_lc_signal=*/false,
              urgency_info, snapshot, kMaximumSafeYieldAccelerationInMpss,
              debug_oss);
      EXPECT_FALSE(distance.has_value());
    }
  }

  // Test hard urgency info stopping near the nominal stop line.
  {
    const LaneChangeUrgencyInfo urgency_info(
        planner::pb::LaneChangeUrgencyType::VALID_LANE_CHANGE_END,
        kUrgencyDistInM, planner::pb::LaneChangeStopLineType::HARD_STOP_LINE);
    // When ego cannot stop smoothly close to the nominal stop line and the
    // urgency info is hard, use the smooth stopping distance, which is larger
    // than the nominal stop line distance.
    {
      std::ostringstream debug_oss;
      const RobotStateSnapshot snapshot =
          test_utility::ConstructRobotStateSnapshot(
              /*x=*/0.0, /*y=*/0.0,
              /*speed=*/hard_initial_speed_to_stop_at_stop_line_with_buffer +
                  kError,
              /*heading=*/0.0,
              /*accel=*/kHardSmoothStoppingMinAccelInMpss);
      const std::optional<double> distance =
          GetStopConstraintDistanceFromRAForUrgencyInfo(
              /*can_trigger_crawl=*/false, /*is_inexecutable_lc_signal=*/false,
              urgency_info, snapshot, kMaximumSafeYieldAccelerationInMpss,
              debug_oss);
      DCHECK(distance.has_value());
      EXPECT_LT(dist_to_stop_line, distance.value());
    }
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, GetSuggestedPassMaximumAccel) {
  constexpr double kLeastUrgencyGapAlignPassMaxAccelInMpss = 1.0;
  constexpr double kMostUrgencyGapAlignPassMaxAccelInMpss = 1.5;
  std::vector<double> urgency_scores{0.0, 0.2, 0.4, 0.6, 0.8, 1.0};
  for (const double urgency_score : urgency_scores) {
    EXPECT_DOUBLE_EQ(
        GetSuggestedPassMaximumAccel(urgency_score),
        math::LinearInterpolate(kLeastUrgencyGapAlignPassMaxAccelInMpss,
                                kMostUrgencyGapAlignPassMaxAccelInMpss,
                                urgency_score));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, GetSuggestedYieldMinimumSpeed) {
  constexpr double kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps = 3.0;
  constexpr double kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps = 4.5;
  constexpr double kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps = 6.0;
  [[maybe_unused]] constexpr double kLeastUrgencyYieldWithMinSpeedInMps = 3.0;
  constexpr double kMostUrgencyYieldWithMinSpeedInMps = 0.0;

  constexpr double kEgoSpeed = 11.1;
  constexpr double kAgentSpeedForStatic = 0.0;
  constexpr double kAgentSpeedForSlowMoving = 6.0;
  constexpr double kAgentSpeedForFastMoving = 15.0;
  constexpr double kAgentSpeedForOverSpeedDriving = 20.0;

  constexpr double kLeastUrgencyScore = 0.0;
  constexpr double kMostUrgencyScore = 1.0;

  // Tests for static agent.
  {
    // Least urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForStatic,
                                      kLeastUrgencyScore),
        kEgoSpeed - kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps);

    // Most urgency.
    EXPECT_DOUBLE_EQ(GetSuggestedYieldMinimumSpeed(
                         kEgoSpeed, kAgentSpeedForStatic, kMostUrgencyScore),
                     kMostUrgencyYieldWithMinSpeedInMps);
  }

  // Tests for slow moving agent.
  {
    // Least urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForSlowMoving,
                                      kLeastUrgencyScore),
        kEgoSpeed -
            math::GetLinearInterpolatedY(
                0.0,
                kEgoSpeed +
                    kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps,
                kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kAgentSpeedForSlowMoving));

    // Most urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForSlowMoving,
                                      kMostUrgencyScore),
        kEgoSpeed -
            math::GetLinearInterpolatedY(
                0.0,
                kEgoSpeed + kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kEgoSpeed, kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kAgentSpeedForSlowMoving));
  }

  // Tests for fast moving agent.
  {
    // Least urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForFastMoving,
                                      kLeastUrgencyScore),
        kAgentSpeedForFastMoving -
            math::GetLinearInterpolatedY(
                0.0,
                kEgoSpeed +
                    kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kLeastUrgencyYieldDeltaSpeedForSmallerSpeedAgentInMps,
                kLeastUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kAgentSpeedForFastMoving));

    // Most urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForFastMoving,
                                      kMostUrgencyScore),
        kAgentSpeedForFastMoving -
            math::GetLinearInterpolatedY(
                0.0,
                kEgoSpeed + kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kEgoSpeed, kMostUrgencyYieldDeltaSpeedForLargerSpeedAgentInMps,
                kAgentSpeedForFastMoving));
  }

  // Tests for over-speed driving agent.
  {
    // Least urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForOverSpeedDriving,
                                      kLeastUrgencyScore),
        kEgoSpeed);
    // Most urgency.
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumSpeed(kEgoSpeed, kAgentSpeedForOverSpeedDriving,
                                      kMostUrgencyScore),
        kEgoSpeed);
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, GetSuggestedYieldMinimumAccel) {
  constexpr double kLeastUrgencyGapAlignYieldMinAccelInMpss = -1.0;
  constexpr double kMostUrgencyGapAlignYieldMinAccelInMpss = -2.0;
  std::vector<double> urgency_scores{0.0, 0.2, 0.4, 0.6, 0.8, 1.0};
  for (const double urgency_score : urgency_scores) {
    EXPECT_DOUBLE_EQ(
        GetSuggestedYieldMinimumAccel(kMostUrgencyGapAlignYieldMinAccelInMpss,
                                      urgency_score),
        math::LinearInterpolate(kLeastUrgencyGapAlignYieldMinAccelInMpss,
                                kMostUrgencyGapAlignYieldMinAccelInMpss,
                                urgency_score));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, IsLikelyParkedCar) {
  // Construct a static agent.
  prediction::pb::Agent static_agent_proto;
  auto& static_tracked_object = *static_agent_proto.mutable_tracked_object();
  static_tracked_object.set_id(1);
  static_tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  constexpr double kObjectLength = 5.0;
  static_tracked_object.set_length(kObjectLength);
  std::unique_ptr<PlannerObject> static_planner_object =
      std::make_unique<PlannerObject>(
          static_agent_proto, /*timestamp=*/1,
          TrafficParticipantPose(/*timestamp=*/1, static_tracked_object));
  static_planner_object->set_is_primary_stationary(true);

  // Construct lane change object info.
  planner::LaneChangeObjectInfo object_info;
  object_info.is_in_queue = false;
  object_info.is_in_rightmost_lane = true;

  std::ostringstream debug_oss;

  // When agent is within rightmost lane and within 50m, return false.
  {
    const std::optional<double> distance_to_last_lc_chance_point = std::nullopt;
    object_info.arc_length_on_region = 50 - math::constants::kEpsilon;
    object_info.object_ego_dist = object_info.arc_length_on_region;
    EXPECT_FALSE(IsLikelyParkedCar(object_info, *static_planner_object,
                                   distance_to_last_lc_chance_point,
                                   /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // When agent is within rightmost lane, beyond 50m, and before last lc point,
  // return true.
  {
    const std::optional<double> distance_to_last_lc_chance_point =
        std::make_optional<double>(80.0);
    object_info.arc_length_on_region =
        50 + kObjectLength / 2 + math::constants::kEpsilon;
    object_info.object_ego_dist = object_info.arc_length_on_region;
    EXPECT_TRUE(IsLikelyParkedCar(object_info, *static_planner_object,
                                  distance_to_last_lc_chance_point,
                                  /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // When agent is within rightmost lane, beyond 50m, and before last lc point,
  // but in a queue, return false.
  {
    const std::optional<double> distance_to_last_lc_chance_point =
        std::make_optional<double>(80.0);
    object_info.arc_length_on_region =
        50 + kObjectLength / 2 + math::constants::kEpsilon;
    object_info.object_ego_dist = object_info.arc_length_on_region;
    object_info.is_in_queue = true;
    EXPECT_FALSE(IsLikelyParkedCar(object_info, *static_planner_object,
                                   distance_to_last_lc_chance_point,
                                   /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // When agent is within rightmost lane, beyond 50m, and before last lc point,
  // not in a queue, but with high parked car score, return true.
  {
    const std::optional<double> distance_to_last_lc_chance_point =
        std::make_optional<double>(80.0);
    object_info.arc_length_on_region =
        50 + kObjectLength / 2 + math::constants::kEpsilon;
    object_info.object_ego_dist = object_info.arc_length_on_region;
    object_info.is_likely_parked_car_from_score = true;
    object_info.is_in_queue = false;
    EXPECT_TRUE(IsLikelyParkedCar(object_info, *static_planner_object,
                                  distance_to_last_lc_chance_point,
                                  /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // When agent is within rightmost lane, beyond 50m, but beyond last lc point,
  // return false.
  {
    const std::optional<double> distance_to_last_lc_chance_point =
        std::make_optional<double>(80.0);
    object_info.arc_length_on_region =
        distance_to_last_lc_chance_point.value() + kObjectLength / 2 +
        math::constants::kEpsilon;
    object_info.object_ego_dist = object_info.arc_length_on_region;
    EXPECT_FALSE(IsLikelyParkedCar(object_info, *static_planner_object,
                                   distance_to_last_lc_chance_point,
                                   /*is_lc_backup_sequence=*/false, debug_oss));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, IsSafeToYieldForGapAlign) {
  constexpr double kSafeYieldAccelerationInMpss = -2.0;
  constexpr double kLeastUrgencyScore = 0.0;
  constexpr double kMostUrgencyScore = 1.0;

  constexpr double kEgoAccelerationL1InMpss = -1.1;
  constexpr double kEgoAccelerationL2InMpss = -1.4;
  constexpr double kEgoAccelerationL3InMpss = -1.7;

  // Test for the general case with the smaller deceleration.
  {
    EXPECT_TRUE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL1InMpss, kSafeYieldAccelerationInMpss,
        kLeastUrgencyScore, /*was_safe_to_yield=*/true));
    EXPECT_TRUE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL1InMpss, kSafeYieldAccelerationInMpss,
        kLeastUrgencyScore, /*was_safe_to_yield=*/false));
  }

  // Test for the corner case with the medium deceleration.
  {
    EXPECT_TRUE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL2InMpss, kSafeYieldAccelerationInMpss,
        kLeastUrgencyScore, /*was_safe_to_yield=*/true));
    EXPECT_FALSE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL2InMpss, kSafeYieldAccelerationInMpss,
        kLeastUrgencyScore, /*was_safe_to_yield=*/false));
  }

  // Test for the corner case with the larger deceleration at the most urgency
  // situation.
  {
    EXPECT_TRUE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL3InMpss, kSafeYieldAccelerationInMpss,
        kMostUrgencyScore, /*was_safe_to_yield=*/true));
    EXPECT_FALSE(IsSafeToYieldForGapAlign(
        kEgoAccelerationL3InMpss, kSafeYieldAccelerationInMpss,
        kMostUrgencyScore, /*was_safe_to_yield=*/false));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, DoAllConstraintsMatchDecision) {
  pb::SpeedSeed last_speed_seed;
  const ObjectId object_id = 101;

  // Test for only a yield decision.
  {
    auto* constraint_result = last_speed_seed.add_constraint_results();
    constraint_result->mutable_constraint()->set_type(
        pb::Constraint_Type_GAP_ALIGN);
    constraint_result->mutable_constraint()->set_obj_id(object_id);
    constraint_result->set_decision(pb::SpeedDecision::YIELD);
    EXPECT_TRUE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                              pb::Constraint_Type_GAP_ALIGN,
                                              pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_GAP_ALIGN,
                                               pb::SpeedDecision::PASS));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::PASS));
  }

  // Test for two yield decisions.
  {
    auto* constraint_result = last_speed_seed.add_constraint_results();
    constraint_result->mutable_constraint()->set_type(
        pb::Constraint_Type_GAP_ALIGN);
    constraint_result->mutable_constraint()->set_obj_id(object_id);
    constraint_result->set_decision(pb::SpeedDecision::YIELD);
    EXPECT_TRUE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                              pb::Constraint_Type_GAP_ALIGN,
                                              pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_GAP_ALIGN,
                                               pb::SpeedDecision::PASS));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::PASS));
  }

  // Test for two yield decisions and a pass decision.
  {
    auto* constraint_result = last_speed_seed.add_constraint_results();
    constraint_result->mutable_constraint()->set_type(
        pb::Constraint_Type_GAP_ALIGN);
    constraint_result->mutable_constraint()->set_obj_id(object_id);
    constraint_result->set_decision(pb::SpeedDecision::PASS);
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_GAP_ALIGN,
                                               pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_GAP_ALIGN,
                                               pb::SpeedDecision::PASS));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::YIELD));
    EXPECT_FALSE(DoAllConstraintsMatchDecision(last_speed_seed, object_id,
                                               pb::Constraint_Type_AVOID_REGION,
                                               pb::SpeedDecision::PASS));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, AddHysteresisForLastSelectedGap) {
  // Test ego speed is lower than 50km/h. We should add hysteresis to avoid
  // flipping to yield.
  {
    const ObjectId object_id = 101;
    Constraint constraint(/*c_type=*/pb::Constraint_Type_GAP_ALIGN,
                          /*fence_type=*/pb::FenceType::kGapAlign,
                          /*reasoner_id=*/"lane-change",
                          /*constraint_id=*/"gap-align-101", object_id,
                          /*c_traj_id=*/"traj-101",
                          /*c_overlap_id=*/"overlap_101");
    ConstraintMutator mutator(&constraint);
    pb::SpeedSeed last_speed_seed;
    auto* lc_guide_seed = last_speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(101);
    lc_guide_seed->set_lead_obj_id(102);
    lc_guide_seed->set_is_valid(true);
    const double ego_speed = 10.0;
    AddHysteresisForLastSelectedGap(last_speed_seed, object_id, ego_speed,
                                    mutator);
    EXPECT_DOUBLE_EQ(5.0,
                     constraint.settings.gap_align_yield_max_active_time(0.0));
    EXPECT_DOUBLE_EQ(-1.0,
                     constraint.settings.gap_align_pass_max_active_time(0.0));
  }

  // Test ego speed is higher than 50km/h. We should NOT add hysteresis to avoid
  // flipping to yield.
  {
    const ObjectId object_id = 101;
    Constraint constraint(/*c_type=*/pb::Constraint_Type_GAP_ALIGN,
                          /*fence_type=*/pb::FenceType::kGapAlign,
                          /*reasoner_id=*/"lane-change",
                          /*constraint_id=*/"gap-align-101", object_id,
                          /*c_traj_id=*/"traj-101",
                          /*c_overlap_id=*/"overlap_101");
    ConstraintMutator mutator(&constraint);
    pb::SpeedSeed last_speed_seed;
    auto* lc_guide_seed = last_speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(101);
    lc_guide_seed->set_lead_obj_id(102);
    lc_guide_seed->set_is_valid(true);
    const double ego_speed = 15.0;
    AddHysteresisForLastSelectedGap(last_speed_seed, object_id, ego_speed,
                                    mutator);
    EXPECT_DOUBLE_EQ(-1.0,
                     constraint.settings.gap_align_yield_max_active_time(0.0));
    EXPECT_DOUBLE_EQ(-1.0,
                     constraint.settings.gap_align_pass_max_active_time(0.0));
  }

  // Test that we should add hysteresis to avoid flipping to pass.
  {
    const ObjectId object_id = 102;
    Constraint constraint(/*c_type=*/pb::Constraint_Type_GAP_ALIGN,
                          /*fence_type=*/pb::FenceType::kGapAlign,
                          /*reasoner_id=*/"lane-change",
                          /*constraint_id=*/"gap-align-102", object_id,
                          /*c_traj_id=*/"traj-102",
                          /*c_overlap_id=*/"overlap_102");
    ConstraintMutator mutator(&constraint);
    pb::SpeedSeed last_speed_seed;
    auto* lc_guide_seed = last_speed_seed.add_lc_guide_seed();
    lc_guide_seed->set_tail_obj_id(101);
    lc_guide_seed->set_lead_obj_id(102);
    lc_guide_seed->set_is_valid(true);
    const double ego_speed = 15.0;
    AddHysteresisForLastSelectedGap(last_speed_seed, object_id, ego_speed,
                                    mutator);
    EXPECT_DOUBLE_EQ(-1.0,
                     constraint.settings.gap_align_yield_max_active_time(0.0));
    EXPECT_DOUBLE_EQ(5.0,
                     constraint.settings.gap_align_pass_max_active_time(0.0));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, CheckYieldPosition) {
  constexpr double kSuggestedYieldMinAccelInMpss = -1.0;
  double ego_speed_in_mps = 5.0;
  double default_yield_min_speed_in_mps = 5.0;

  // Test that we should NOT have an eligible yield position to invoke
  // increasing yield min speed logic because ego speed is higher than the speed
  // diff threshold.
  {
    const double agent_speed = 10.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 6; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i);
    }
    overlap_regions.emplace_back(overlap_region);
    ego_speed_in_mps = agent_speed - kDeltaSpeedToYieldToAgentBehindEgoInMps +
                       math::constants::kEpsilon;
    default_yield_min_speed_in_mps = 9.0;
    const double accel = default_yield_min_speed_in_mps > ego_speed_in_mps
                             ? kEgoMaxAccelWhenYieldInMpss
                             : kSuggestedYieldMinAccelInMpss;
    std::optional<double> agent_first_overlap_slice_lon_speed;
    EXPECT_FALSE(CheckYieldPosition(
        overlap_regions, ego_speed_in_mps, default_yield_min_speed_in_mps,
        accel, /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        agent_first_overlap_slice_lon_speed));
  }

  // Test that we should have an eligible yield position to invoke increasing
  // yield min speed logic because ego speed is lower than the speed diff
  // threshold.
  {
    const double agent_speed = 10.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 6; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i);
    }
    overlap_regions.emplace_back(overlap_region);
    ego_speed_in_mps = agent_speed - kDeltaSpeedToYieldToAgentBehindEgoInMps -
                       math::constants::kEpsilon;
    default_yield_min_speed_in_mps = 9.0;
    const double accel = default_yield_min_speed_in_mps > ego_speed_in_mps
                             ? kEgoMaxAccelWhenYieldInMpss
                             : kSuggestedYieldMinAccelInMpss;
    std::optional<double> agent_first_overlap_slice_lon_speed;
    EXPECT_TRUE(CheckYieldPosition(
        overlap_regions, ego_speed_in_mps, default_yield_min_speed_in_mps,
        accel, /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        agent_first_overlap_slice_lon_speed));
  }

  // Test that we should have an eligible yield position to invoke increasing
  // yield min speed logic because the default yield min speed is very low.
  {
    const double agent_speed = 10.0;
    const double agent_start_arc_length = -8.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 6; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i + agent_start_arc_length);
    }
    overlap_regions.emplace_back(overlap_region);
    ego_speed_in_mps = 8.0;
    default_yield_min_speed_in_mps = 3.0;
    const double accel = default_yield_min_speed_in_mps > ego_speed_in_mps
                             ? kEgoMaxAccelWhenYieldInMpss
                             : kSuggestedYieldMinAccelInMpss;
    std::optional<double> agent_first_overlap_slice_lon_speed;
    EXPECT_TRUE(CheckYieldPosition(
        overlap_regions, ego_speed_in_mps, default_yield_min_speed_in_mps,
        accel, /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        agent_first_overlap_slice_lon_speed));
  }

  // Test that we should NOT have an eligible yield position to invoke
  // increasing yield min speed logic because the default yield min speed is
  // relative high.
  {
    const double agent_speed = 10.0;
    const double agent_start_arc_length = -8.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 6; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i + agent_start_arc_length);
    }
    overlap_regions.emplace_back(overlap_region);
    ego_speed_in_mps = 8.0;
    default_yield_min_speed_in_mps = 8.0;
    const double accel = default_yield_min_speed_in_mps > ego_speed_in_mps
                             ? kEgoMaxAccelWhenYieldInMpss
                             : kSuggestedYieldMinAccelInMpss;
    std::optional<double> agent_first_overlap_slice_lon_speed;
    EXPECT_FALSE(CheckYieldPosition(
        overlap_regions, ego_speed_in_mps, default_yield_min_speed_in_mps,
        accel, /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        agent_first_overlap_slice_lon_speed));
  }

  // Test that we should NOT have an eligible yield position to invoke
  // increasing yield min speed logic because ego cannot yield to the agent
  // before the expected time.
  {
    const double agent_speed = 10.0;
    const double agent_start_arc_length = -8.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 6; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i + agent_start_arc_length);
    }
    overlap_regions.emplace_back(overlap_region);
    ego_speed_in_mps = 10.0;
    default_yield_min_speed_in_mps = 9.5;
    const double accel = default_yield_min_speed_in_mps > ego_speed_in_mps
                             ? kEgoMaxAccelWhenYieldInMpss
                             : kSuggestedYieldMinAccelInMpss;
    std::optional<double> agent_first_overlap_slice_lon_speed;
    EXPECT_FALSE(CheckYieldPosition(
        overlap_regions, ego_speed_in_mps, default_yield_min_speed_in_mps,
        accel, /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        agent_first_overlap_slice_lon_speed));
  }
}

TEST(LaneChangeSpeedReasonerUtilTest, PopulateYieldInfoAtExpectedTime) {
  // The last overlap slice is after kExpectedYieldActiveTimeInSecond = 5.0s. So
  // the target position is derived from the overlap slice at
  // kExpectedYieldActiveTimeInSecond.
  {
    const double agent_speed = 10.0;
    const double agent_start_arc_length = -8.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 7; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i + agent_start_arc_length);
    }
    overlap_regions.emplace_back(overlap_region);

    double target_odom = std::numeric_limits<double>::infinity();
    double target_speed = std::numeric_limits<double>::infinity();
    double target_yield_time = std::numeric_limits<double>::infinity();
    EXPECT_TRUE(PopulateYieldInfoAtExpectedTime(
        overlap_regions,
        /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        kExpectedYieldActiveTimeInSecond, target_odom, target_speed,
        target_yield_time));
    EXPECT_DOUBLE_EQ(target_yield_time, kExpectedYieldActiveTimeInSecond);
    EXPECT_DOUBLE_EQ(target_odom, 42.0);
    EXPECT_DOUBLE_EQ(target_speed, agent_speed);
  }

  // The last overlap slice is at 4.0s. So the target position is derived from
  // the last overlap slice.
  {
    const double agent_speed = 10.0;
    const double agent_start_arc_length = -8.0;
    std::vector<speed::pb::OverlapRegion> overlap_regions;
    speed::pb::OverlapRegion overlap_region;
    for (int i = 0; i < 5; ++i) {
      auto* overlap_slice = overlap_region.add_overlap_slices();
      overlap_slice->set_signed_longitudinal_speed(agent_speed);
      overlap_slice->set_relative_time_in_sec(0.0 + i);
      overlap_slice->mutable_ego_moving_distance_padded()->set_start(
          agent_speed * i + agent_start_arc_length);
    }
    overlap_regions.emplace_back(overlap_region);

    double target_odom = std::numeric_limits<double>::infinity();
    double target_speed = std::numeric_limits<double>::infinity();
    double target_yield_time = std::numeric_limits<double>::infinity();
    EXPECT_TRUE(PopulateYieldInfoAtExpectedTime(
        overlap_regions,
        /*yield_extra_distance=*/0.0, /*yield_extra_time=*/0.0,
        kExpectedYieldActiveTimeInSecond, target_odom, target_speed,
        target_yield_time));
    EXPECT_DOUBLE_EQ(target_yield_time, 4.0);
    EXPECT_DOUBLE_EQ(target_odom, 32.0);
    EXPECT_DOUBLE_EQ(target_speed, agent_speed);
  }
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
