#include "planner/speed/reasoning/reasoner/oncoming_agent_reasoner/oncoming_agent_reasoner.h"

#include <algorithm>
#include <limits>
#include <optional>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>
#include <absl/strings/str_format.h>

#include "math/math_util.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/control_error.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/constraint/constraint_mutator.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/overlap/ego_lateral_clearance.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/overlap_region_info.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/prediction_decision_maker.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_yielding_fence.pb.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {

// The longitudinal safety distance range for oncoming agents.
constexpr double kMaxSafetyDistanceToAgentInMeter = 3.0;
constexpr double kMinSafetyDistanceToAgentInMeter = 0.25;
// The maximum comfort required lateral gap for oncoming cyclist and vehicles,
// if ego is not overtaking.
constexpr double kComfortRequiredLateralGapInMeter = 0.4;
// The yield extra distance for oncoming agent when ego is reverse driving.
constexpr double kYieldExtraDistanceWhenReversingInMeter = 1.0;
constexpr double kStrongCautiousSpeedLimitForEgoBlockOncomingCyclistsInMps =
    math::KmphToMps(15.0);
constexpr double kStrongCautiousAccelInMpss = -2.0;
// The minimal pass required lateral gap for stuck agent
constexpr double kMinResetablePLGForUnstuckSteerAwayAgentInMeters = 0.1;
constexpr double kEpsilon = 1e-3;
// Returns the pinch regions for the object. If there is cached result, the
// cached one is returned. Otherwise, the pinch regions are computed and
// cached, then the cached one is returned.
const std::vector<PinchRegion>& FindOrGetPinchRegionsForAgent(
    const pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    const ReasoningObject& reasoning_object, const bool check_left,
    std::unordered_map<ObjectId, std::vector<PinchRegion>>&
        object_id_to_pinch_region) {
  constexpr double kLateralGapBufferInMeters =
      kComfortRequiredLateralGapInMeter * 2.0;
  if (reasoning_object.is_cyclist()) {
    // Cyclists are excluded because cyclists could pass through on Ego right
    // side.
    return object_id_to_pinch_region[reasoning_object.id()];
  }

  const auto pinch_region_iter =
      object_id_to_pinch_region.find(reasoning_object.id());
  if (pinch_region_iter != object_id_to_pinch_region.end()) {
    return pinch_region_iter->second;
  }

  // There is no cached pinch regions for this agent and they are computed and
  // then cached.
  const auto insert_result = object_id_to_pinch_region.emplace(
      reasoning_object.id(),
      reasoning_util::ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance_info,
          reasoning_object.width() + kLateralGapBufferInMeters,
          reasoning_object.length(), check_left, /*debug=*/nullptr));

  CHECK(insert_result.second);
  return insert_result.first->second;
}

// Computes yield extra time.
DiscomfortVarying ComputeYieldExtraTime(
    const ReasoningObject& reasoning_object) {
  constexpr double kComfortYieldExtraTimeForObjectOnEgoPathInSecond = 0.5;
  constexpr double kCriticalYieldExtraTimeForObjectOnEgoPathInSecond = 0.3;
  if (reasoning_object.IsOnEgoPath()) {
    return DiscomfortVarying(kComfortYieldExtraTimeForObjectOnEgoPathInSecond,
                             kCriticalYieldExtraTimeForObjectOnEgoPathInSecond);
  }
  return DiscomfortVarying(0.0);
}

// Computes pass required lateral gap.
DiscomfortVarying ComputePassRequiredLateralGap(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object, const AgentPolicy& agent_policy,
    std::string* debug_str_ptr) {
  if (reasoning_object.is_cyclist() &&
      agent_policy.overlap_region_info().is_cut_behind) {
    absl::StrAppend(debug_str_ptr,
                    "Pass RLG is set to zero for cyclist cut-behind; ");
    return DiscomfortVarying(0.0);
  }
  if (reasoning_object.is_unstuck_object_in_narrow_passage() ||
      reasoning_object.ReduceCriticalReqLatGapByRaConfirmation()) {
    const double unstuck_pass_required_lat_gap =
        (reasoning_object.planner_object().object_type() ==
         voy::perception::ObjectType::TRAFFIC_CONE)
            ? reasoning_util::ComputeUnstuckPassRequiredLatGapForTrafficCone(
                  reasoning_object)
            : planner::control_error::LateralControlError(/*curvature=*/0.0);
    return DiscomfortVarying(unstuck_pass_required_lat_gap);
  }
  if (reasoning_object.is_plg_resettable_for_unstuck()) {
    const double overlap_abs_lat_gap_to_unstuck =
        std::max((std::abs(reasoning_object.plan_init_pose_overlap_slice_ptr()
                               ->signed_lateral_gap()) -
                  kEpsilon),
                 kMinResetablePLGForUnstuckSteerAwayAgentInMeters);
    return DiscomfortVarying(overlap_abs_lat_gap_to_unstuck);
  }
  if (reasoning_object.IsLargeVehicle() &&
      reasoning_util::ShouldReducePassRequiredLateralGapUnderCriticalToUnstuck(
          reasoning_object, trajectory_info, world_context.ego_speed())) {
    const pb::ObjectProximityInfo& object_proximity_info =
        reasoning_object.object_proximity_info();
    const double curvature = trajectory_info.GetEgoCurvature(
        object_proximity_info.projected_ra_arc_length().start());
    return DiscomfortVarying(
        planner::control_error::LateralControlError(curvature));
  }
  return DiscomfortVarying(kComfortRequiredLateralGapInMeter,
                           reasoning_object.CriticalRequiredLateralGap());
}

// Returns the segments of overlap region that are used to add constraint.
// The end of each overlap segment is not included, i.e. the range is
// [start, end).
std::vector<OverlapSegment>
ComputeOverlapSegmentsAddingConstraintBasedOnPinchRegion(
    const ReasoningObject& reasoning_object,
    const std::vector<PinchRegion>& pinch_regions,
    const pb::OverlapRegion& overlap_region) {
  const auto& overlap_slices = overlap_region.overlap_slices();
  DCHECK_GT(overlap_slices.size(), 0);

  std::optional<OverlapSegment> segment_for_safety =
      reasoning_util::GetOverlapSegmentWithinNearFutureForSafety(
          overlap_region, reasoning_object);

  std::vector<OverlapSegment> overlap_segments_in_pinch_region =
      reasoning_util::ComputeIndexRangeInPinchRegions(overlap_region,
                                                      pinch_regions);
  std::sort(overlap_segments_in_pinch_region.begin(),
            overlap_segments_in_pinch_region.end());

  std::vector<OverlapSegment> segments_to_add_constraint;
  if (segment_for_safety.has_value()) {
    segments_to_add_constraint.reserve(overlap_segments_in_pinch_region.size() +
                                       1);
    segments_to_add_constraint.push_back(segment_for_safety.value());
    for (const OverlapSegment& segment : overlap_segments_in_pinch_region) {
      DCHECK_LE(segments_to_add_constraint.back().first, segment.first);
      if (segment.first <= segments_to_add_constraint.back().second) {
        math::UpdateMax(segment.second,
                        segments_to_add_constraint.back().second);
      } else {
        segments_to_add_constraint.push_back(segment);
      }
    }
  } else {
    segments_to_add_constraint = std::move(overlap_segments_in_pinch_region);
  }

  return segments_to_add_constraint;
}

// Returns the segments of overlap region that are used to add constraint in
// wrong way encroachment region. The end of each overlap segment is not
// included, i.e. the range is [start, end).
std::vector<OverlapSegment>
ComputeOverlapSegmentsAddingConstraintBasedOnWrongWayEncroachmentRegion(
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region,
    const OverlapRegionInfo& overlap_region_info) {
  const auto& overlap_slices = overlap_region.overlap_slices();
  DCHECK_GT(overlap_slices.size(), 0);
  DCHECK(overlap_region_info.encroachment_segment_opt.has_value());
  DCHECK(overlap_region_info.is_encroaching_oncoming_lane);
  std::optional<OverlapSegment> segment_for_safety =
      reasoning_util::GetOverlapSegmentWithinNearFutureForSafety(
          overlap_region, reasoning_object);

  std::vector<OverlapSegment> segments_to_add_constraint;
  if (segment_for_safety.has_value()) {
    segments_to_add_constraint.reserve(
        overlap_region_info.overlap_segments_in_wrong_way_encroachment_region
            .size() +
        1);
    segments_to_add_constraint.push_back(segment_for_safety.value());
    for (const OverlapSegment& segment :
         overlap_region_info
             .overlap_segments_in_wrong_way_encroachment_region) {
      DCHECK_LE(segments_to_add_constraint.back().first, segment.first);
      if (segment.first <= segments_to_add_constraint.back().second) {
        math::UpdateMax(segment.second,
                        segments_to_add_constraint.back().second);
      } else {
        segments_to_add_constraint.push_back(segment);
      }
    }
  } else {
    segments_to_add_constraint = std::move(
        overlap_region_info.overlap_segments_in_wrong_way_encroachment_region);
  }
  return segments_to_add_constraint;
}

// If ego is on the left or right most lane, and the gap to the hard
// boundary is too narrow for an oncoming cyclist to pass safely, apply strong
// caution.
bool ShouldAddStrongCautiousAsEgoBlockOncomingCyclist(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    const math::Range1d& query_ra_arclength_range, std::string* debug_str) {
  constexpr double kAgentMinRequiredLateralGapInMeter = 0.4;

  // Only consider on ego path oncoming cyclist.
  if (!reasoning_object.is_cyclist() || !reasoning_object.IsOnEgoPath()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "Not on ego path cyclist, no strong cautious;");
    }
    return false;
  }

  const pnc_map::Lane* current_lane =
      *(trajectory_info.lane_sequence_iterator().current_lane());

  // Consider only leftmost or rightmost lanes for strong cautious.
  if (!current_lane->IsRightmostDrivableLane() &&
      !current_lane->IsLeftmostDrivableLane()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "Not in left or right most lane, no strong cautious;");
    }
    return false;
  }

  // Collect clearance info data based on Ego's lane position.
  const google::protobuf::RepeatedField<double>& clearances =
      current_lane->IsLeftmostDrivableLane()
          ? ego_lateral_clearance_info.left_clearances()
          : ego_lateral_clearance_info.right_clearances();
  const google::protobuf::RepeatedField<double>& arc_lengths =
      ego_lateral_clearance_info.ego_moving_distances();
  DCHECK_EQ(arc_lengths.size(), clearances.size());

  // Check if the query range within Ego's lateral clearance calculation bounds.
  if (arc_lengths.empty() ||
      !math::IsInRange(query_ra_arclength_range.start_pos,
                       *(arc_lengths.begin()), *(arc_lengths.rbegin()))) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "Not in lateral clearance range, no strong cautious;");
    }
    return false;
  }

  // Get min lateral clearence based on query_ra_arclength_range.
  const auto searched_iter =
      std::upper_bound(arc_lengths.begin(), arc_lengths.end(),
                       query_ra_arclength_range.start_pos);
  // As we already checked query_ra_arclength_range.start_pos was within the
  // arclength range, arc_lengths.begin() will not be reached.
  DCHECK(searched_iter != arc_lengths.begin());
  const auto start_idx =
      std::distance(arc_lengths.begin(), std::prev(searched_iter));
  double min_lateral_gap = std::numeric_limits<double>::infinity();
  for (int idx = start_idx; idx < arc_lengths.size(); ++idx) {
    if (arc_lengths[idx] > query_ra_arclength_range.end_pos) {
      break;
    }
    math::UpdateMin(clearances[idx], min_lateral_gap);
  }

  // Check if the agent can safely pass beside Ego with a reasonable buffer.
  if (reasoning_object.width() + kAgentMinRequiredLateralGapInMeter <=
      min_lateral_gap) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "Clearance enough for agent pass, no strong cautious;");
    }
    return false;
  }

  if (debug_str != nullptr) {
    absl::StrAppend(debug_str,
                    "Strong cautious added for block oncoming cyc!;");
  }

  return true;
}

bool AddStrongCautiousForOnComingEgoBlockCyclist(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    CautiousDrivingLimiter* reference_limiter_ptr, std::string* debug_str) {
  const double cautious_start_arclength =
      std::max(reasoning_util::ComputeYieldPositionForOncomingAgent(
                   reasoning_object, world_context.ra_to_leading_bumper(),
                   world_context.ego_speed()),
               0.0);
  const math::Range1d speed_zone_ra_arclength_range(
      cautious_start_arclength, cautious_start_arclength +
                                    world_context.robot_state().GetLength() +
                                    reasoning_object.length());
  const math::Range1d speed_limit_range = {
      kStrongCautiousSpeedLimitForEgoBlockOncomingCyclistsInMps,
      kStrongCautiousSpeedLimitForEgoBlockOncomingCyclistsInMps};

  if (ShouldAddStrongCautiousAsEgoBlockOncomingCyclist(
          reasoning_object, trajectory_info, ego_lateral_clearance_info,
          speed_zone_ra_arclength_range, debug_str)) {
    return reference_limiter_ptr->AddReferenceLimit(
        speed_limit_range, speed_zone_ra_arclength_range,
        kStrongCautiousAccelInMpss, reasoning_object.id());
  }
  return false;
}

}  // namespace

bool OncomingAgentReasoner::AddConstraintForOverlapSegments(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy,
    const std::vector<OverlapSegment>& segments_to_add_constraints,
    ConstraintCreator* constraint_creator_ptr, std::string* debug_str_ptr) {
  if (segments_to_add_constraints.empty()) {
    return false;
  }

  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  DCHECK(!overlap_region.overlap_slices().empty());

  for (unsigned ii = 0; ii < segments_to_add_constraints.size(); ++ii) {
    const OverlapSegment& segment = segments_to_add_constraints[ii];
    const std::string constraint_id =
        absl::StrFormat("%s-%u", GetOverlapRegionUniqueId(overlap_region), ii);
    const DiscomfortVarying pass_required_lateral_gap =
        ComputePassRequiredLateralGap(world_context, trajectory_info,
                                      reasoning_object, agent_policy,
                                      debug_str_ptr);
    ConstraintMutator mutator =
        constraint_creator_ptr->AddSpeedConstraintFromOverlapRegionSegment(
            pb::FenceType::kOncoming, reasoner_id(), constraint_id,
            overlap_region, pass_required_lateral_gap,
            DiscomfortVarying(reasoning_object.CriticalRequiredLateralGap()),
            reasoning_object.id(),
            agent_policy.agent_trajectory_info().id().ToString(),
            /*use_discomfort_varying_overlap=*/false,
            /*start_idx=*/segment.first, /*end_idx=*/segment.second);

    mutator.set_yield_extra_distance(DiscomfortVarying(
        kMaxSafetyDistanceToAgentInMeter, kMinSafetyDistanceToAgentInMeter));
    mutator.set_yield_extra_time(
        ComputeYieldExtraTime(agent_policy.reasoning_object()));

    if (agent_policy.HasAnyAgentReaction()) {
      mutator.set_pass_agent_reaction(
          agent_policy.agent_reaction_params().reaction_times,
          agent_policy.agent_reaction_params().slow_down_params,
          agent_policy.agent_reaction_params().shift_params);
      mutator.set_yield_agent_reaction(
          agent_policy.agent_reaction_params().reaction_times,
          agent_policy.agent_reaction_params().slow_down_params,
          agent_policy.agent_reaction_params().shift_params);
    }

    if (reasoning_object.is_plg_resettable_for_unstuck() ||
        (reasoning_object.is_cyclist() &&
         agent_policy.overlap_region_info().is_cut_behind) ||
        (reasoning_object.ReduceCriticalReqLatGapByRaConfirmation() ||
         reasoning_util::
             ShouldReducePassRequiredLateralGapUnderCriticalToUnstuck(
                 reasoning_object, trajectory_info,
                 world_context.ego_speed()))) {
      mutator.set_allow_ignoring_min_range_lat_gap();
    }

    if (FLAGS_planning_enable_enlarging_scope_of_eb_for_vru &&
        reasoning_object.is_cyclist() &&
        reasoning_util::ShouldAllowEmergencyBrakeForVRU(
            world_context, reasoning_object, overlap_region,
            /*reasoner_in_charge=*/pb::ReasonerId::ONCOMING_AGENT)) {
      mutator.set_allow_emergency_brake();
    }

    if (reasoning_util::ShouldAllowEmergencyBrake(world_context,
                                                  reasoning_object)) {
      mutator.set_allow_emergency_brake();
      if (debug_str_ptr != nullptr) {
        absl::StrAppendFormat(debug_str_ptr, "allow EB for obj onpath, ");
      }
    }
    absl::StrAppendFormat(debug_str_ptr, "constraint added for [%i, %i],",
                          segment.first, segment.second);
  }

  return true;
}

bool OncomingAgentReasoner::AddConstraintForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy,
    const speed::pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    std::unordered_map<ObjectId, std::vector<PinchRegion>>&
        object_id_to_pinch_region,
    ConstraintCreator* constraint_creator_ptr,
    CautiousDrivingLimiter* reference_limiter_ptr, std::string* debug_str) {
  bool constraint_added = false;
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  if (debug_str != nullptr) {
    absl::StrAppendFormat(debug_str,
                          "\noverlap %i: ", overlap_region.region_id());
  }

  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const std::string overlap_id_str = GetOverlapRegionUniqueId(overlap_region);
  const OverlapRegionInfo& overlap_region_info =
      agent_policy.overlap_region_info();
  if (trajectory_info.IsReverseDriving()) {
    ConstraintMutator constraint_mutator =
        agent_policy.AddConstraintWhenReversing(
            pb::FenceType::kOncoming, reasoning_object.required_lateral_gap(),
            overlap_id_str, constraint_creator_ptr);
    constraint_mutator.set_yield_extra_distance(
        DiscomfortVarying(kYieldExtraDistanceWhenReversingInMeter));
    constraint_added = true;
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "set closer acc fence when reverse driving.");
    }
    return constraint_added;
  }

  constraint_added |= AddStrongCautiousForOnComingEgoBlockCyclist(
      world_context, reasoning_object, trajectory_info,
      ego_lateral_clearance_info, reference_limiter_ptr, debug_str);

  const std::vector<PinchRegion>& pinch_regions = FindOrGetPinchRegionsForAgent(
      ego_lateral_clearance_info, reasoning_object, /*check_left=*/true,
      object_id_to_pinch_region);
  std::vector<OverlapSegment> segments_to_add_constraint;
  if (pinch_regions.empty()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "No Pinch Region; ");
    }
    if (trajectory_info.IsXLaneNudging()) {
      // When Ego is performing cross-lane nudge and there is no pinch region,
      // the whole predicted trajectory should be considered in order to avoid
      // stopping at middle of cross-lane nudge.
      segments_to_add_constraint.emplace_back(
          0, overlap_region.overlap_slices().size());
    } else if (!reasoning_object.is_vehicle_or_cyclist_on_wrong_way_lane() &&
               overlap_region_info.is_encroaching_oncoming_lane) {
      segments_to_add_constraint =
          ComputeOverlapSegmentsAddingConstraintBasedOnWrongWayEncroachmentRegion(
              reasoning_object, overlap_region, overlap_region_info);
      if (debug_str != nullptr) {
        absl::StrAppend(debug_str, "Encroach oncoming; ");
      }
    } else {
      // Otherwise, only add constraint for first some seconds of predicted
      // trajectory.
      const std::optional<OverlapSegment> segment =
          reasoning_util::GetOverlapSegmentWithinNearFutureForSafety(
              overlap_region, agent_policy.reasoning_object());
      if (segment.has_value()) {
        segments_to_add_constraint.emplace_back(segment.value());
      }
    }
  } else {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Pinch Region: ");
      for (const PinchRegion& pinch_region : pinch_regions) {
        absl::StrAppendFormat(debug_str, "{%.2f, %.2f}",
                              pinch_region.start_ra_arclength,
                              pinch_region.end_ra_arclength);
      }
    }
    segments_to_add_constraint =
        ComputeOverlapSegmentsAddingConstraintBasedOnPinchRegion(
            agent_policy.reasoning_object(), pinch_regions, overlap_region);
  }

  return AddConstraintForOverlapSegments(
      world_context, trajectory_info, agent_policy, segments_to_add_constraint,
      constraint_creator_ptr, debug_str);
}

bool OncomingAgentReasoner::AddConstraintForPrediction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const PredictionDecision& prediction_decision,
    const speed::pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    std::unordered_map<ObjectId, std::vector<PinchRegion>>&
        object_id_to_pinch_region,
    ConstraintCreator* constraint_creator_ptr,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::OncomingAgentTrajectoryDebug* trajectory_debug) {
  bool constraint_added = false;
  if (prediction_decision.Ignore()) {
    DCHECK(prediction_decision.agent_policies().empty());
    return constraint_added;
  }

  std::string debug_str;
  for (const AgentPolicy& agent_policy : prediction_decision.agent_policies()) {
    if (agent_policy.reasoner_in_charge() != reasoner_id()) {
      continue;
    }

    constraint_added |= AddConstraintForPolicy(
        world_context, trajectory_info, agent_policy,
        ego_lateral_clearance_info, object_id_to_pinch_region,
        constraint_creator_ptr, reference_limiter_ptr, &debug_str);
  }

  if (trajectory_debug != nullptr) {
    trajectory_debug->set_debug_str(std::move(debug_str));
  }
  return constraint_added;
}

bool OncomingAgentReasoner::Reason(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    ConstraintCreator* constraint_creator_ptr,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::ReasoningDebug* debug_proto_ptr) {
  pb::OncomingAgentReasonerDebug* oncoming_agent_reasoner_debug =
      debug_proto_ptr ? debug_proto_ptr->mutable_reasoner_debug()
                            ->mutable_oncoming_agent_reasoner()
                      : nullptr;
  std::unordered_map<ObjectId, std::vector<PinchRegion>>
      object_id_to_pinch_region;
  bool constraint_added = false;

  for (const PredictionDecisionMakerOutput& output :
       wrapped_prediction_decisions) {
    if (!output.HasAnyAgentPolicyHandledByGivenReasoner(reasoner_id())) {
      continue;
    }

    // Per agent debug info.
    pb::OncomingAgentDebug* agent_debug =
        oncoming_agent_reasoner_debug
            ? oncoming_agent_reasoner_debug->add_agents()
            : nullptr;
    if (agent_debug) {
      agent_debug->set_object_id(output.reasoning_object().id());
    }

    for (const PredictionDecision& prediction_decision :
         output.prediction_decisions()) {
      // Per predicted trajectory debug info.
      pb::OncomingAgentTrajectoryDebug* trajectory_debug =
          agent_debug ? agent_debug->add_agent_trajectories() : nullptr;
      if (trajectory_debug) {
        trajectory_debug->set_trajectory_id(
            prediction_decision.trajectory_id());
      }

      constraint_added |= AddConstraintForPrediction(
          world_context, trajectory_info, prediction_decision,
          ego_lateral_clearance_info, object_id_to_pinch_region,
          constraint_creator_ptr, reference_limiter_ptr, trajectory_debug);
    }
  }

  return constraint_added;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
