#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_cell.h"

#include <algorithm>
#include <vector>

namespace planner {

namespace open_space {

namespace {

// Returns the interval index given an input range and the interval resolution.
int GetIntervalIndexByRangeAndRes(double val, const math::Range1d& range,
                                  double res) {
  if (!math::IsInRange(val, range.start_pos, range.end_pos)) {
    DCHECK(false) << std::setprecision(17) << "The input value " << val
                  << " is out of the [" << range.start_pos << " ,"
                  << range.end_pos << "] range!";
    return kInvalidOpenSpaceGridIndex;
  }
  return static_cast<int>((val - range.start_pos) / res);
}

}  // namespace

DiscretizedHeadingSearchSpace::DiscretizedHeadingSearchSpace(
    int x_idx_in, int y_idx_in, double heading_res_rad_in)
    : overall_heading_range(-<PERSON>_P<PERSON>, M_PI), heading_res_rad(heading_res_rad_in) {
  const int intervals_size = static_cast<int>(std::ceil(
      (overall_heading_range.end_pos - overall_heading_range.start_pos) /
      heading_res_rad));
  heading_intervals = std::vector<HeadingSearchSpaceInterval>(intervals_size);
  for (int idx = 0; idx < intervals_size; ++idx) {
    HeadingSearchSpaceInterval& interval = heading_intervals[idx];
    interval.interval_idx = idx;
    interval.heading_range =
        math::Range1d(interval.interval_idx * heading_res_rad +
                          overall_heading_range.start_pos,
                      std::min((interval.interval_idx + 1) * heading_res_rad +
                                   overall_heading_range.start_pos,
                               overall_heading_range.end_pos));
    interval.searched_node.key =
        OpenSpaceNodeKey{x_idx_in, y_idx_in, interval.interval_idx};
  }
}

int DiscretizedHeadingSearchSpace::GetIntervalIndex(double heading_rad) const {
  return GetIntervalIndexByRangeAndRes(
      heading_rad,
      /*heading_range=*/math::Range1d(-M_PI, M_PI), heading_res_rad);
}

int GridCell::GetHeadingIntervalIndex(double heading_rad) const {
  return GetIntervalIndexByRangeAndRes(
      heading_rad,
      /*heading_range=*/math::Range1d(-M_PI, M_PI), heading_res_rad_);
}

std::pair<double, double> GridCell::GetQuadraticInterpolationRatio(
    double x, double y) const {
  DCHECK(math::IsInRange(x, x_range_.start_pos, x_range_.end_pos));
  DCHECK(math::IsInRange(y, y_range_.start_pos, y_range_.end_pos));

  return std::make_pair(
      math::GetInterpolationRatio(x_range_.start_pos, x_range_.end_pos, x,
                                  /*allow_extension=*/false),
      math::GetInterpolationRatio(y_range_.start_pos, y_range_.end_pos, y,
                                  /*allow_extension=*/false));
}

double GridCell::GetInterpDominantOccupancyClearance(double x, double y) const {
  const auto [x_ratio, y_ratio] = GetQuadraticInterpolationRatio(x, y);
  const double upper_x_interp_val =
      math::LinearInterpolate(upper_left().dominant_clearance(),
                              upper_right().dominant_clearance(), x_ratio);
  const double lower_x_interp_val =
      math::LinearInterpolate(lower_left().dominant_clearance(),
                              lower_right().dominant_clearance(), x_ratio);
  return math::LinearInterpolate(lower_x_interp_val, upper_x_interp_val,
                                 y_ratio);
}

OccupancyClearanceInfo GridCell::GetDominantOccupancyClearanceInfo(
    const math::geometry::Point2d& pt, double drivable_clearance_thresh) const {
  DCHECK(math::IsInRange(pt.x(), x_range_.start_pos, x_range_.end_pos));
  DCHECK(math::IsInRange(pt.y(), y_range_.start_pos, y_range_.end_pos));

  // Find the pt's min heuristic clearance to a dominant occupancy. Heuristic
  // distance to occupancy means the sum of the distance from the queried pt to
  // the grid cell vertex and the vertex's dominant occupancy clearance.
  // NOTE: This heuristic is based on the assumption that the grid cell's
  // resolution is rather small, and the error induced from this approximation
  // is acceptable.
  double min_heuristic_clearance = std::numeric_limits<double>::max();
  OccupancyConstraintID dominant_constraint_id;
  const std::array<const GridCellVertex*, 4> corner_vertices = CornerVertices();
  for (size_t idx = 0; idx < corner_vertices.size(); ++idx) {
    const GridCellVertex& vertex = *DCHECK_NOTNULL(corner_vertices[idx]);
    if (!vertex.is_available) {
      // NOTE: Based on current grid xy resolution, if any of the four vertices
      // are not available, any xy point within this grid cannot be drivable.
      // Hence, early return.
      DCHECK(!vertex.dominant_clearance_info.constraint_id.IsNull());
      return OccupancyClearanceInfo{
          vertex.dominant_clearance_info.constraint_id,
          /*is_drivable=*/false, /*clearance=*/0.0};
    }

    const double heuristic_clearance =
        math::geometry::Distance(vertex.xy_point, pt) +
        vertex.dominant_clearance();
    if (heuristic_clearance < min_heuristic_clearance) {
      min_heuristic_clearance = heuristic_clearance;
      dominant_constraint_id = vertex.dominant_clearance_info.constraint_id;
    }
  }
  math::UpdateMin(GetInterpDominantOccupancyClearance(pt.x(), pt.y()),
                  min_heuristic_clearance);

  return OccupancyClearanceInfo{
      dominant_constraint_id,
      /*is_drivable=*/min_heuristic_clearance >= drivable_clearance_thresh,
      /*clearance=*/min_heuristic_clearance};
}

double GridCell::GetHeuristicDistanceToTarget(
    const math::geometry::Point2d& pt) const {
  const auto get_heuristic_dist_given_vertex =
      [&pt](const GridCellVertex& vertex) {
        return math::geometry::Distance(vertex.xy_point, pt) +
               vertex.heuristic_dist_to_target;
      };
  return std::min({get_heuristic_dist_given_vertex(lower_left()),
                   get_heuristic_dist_given_vertex(lower_right()),
                   get_heuristic_dist_given_vertex(upper_left()),
                   get_heuristic_dist_given_vertex(upper_right())});
}

}  // namespace open_space

}  // namespace planner
