#ifndef ONBOARD_PLANNER_SPEED_SOLVER_SEARCHER_SIMPLE_PROFILE_SEARCHER_H_
#define ONBOARD_PLANNER_SPEED_SOLVER_SEARCHER_SIMPLE_PROFILE_SEARCHER_H_

#include <limits>
#include <map>
#include <string>
#include <vector>

#include <absl/container/flat_hash_map.h>

#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/constraint/gap_align_info.h"
#include "planner/speed/discomforts/discomfort_varying_min_range.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/discomforts/profile_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/timemap/time_map.h"
#include "planner/speed/timemap/time_map_util.h"
#include "planner_protos/speed_solver_debug.pb.h"

// This file contains utility function to adjust a simple profile for a given
// conflict:
// 1) The utility functions to generate braking profile for a given conflict;
// 2) The utility function to accelerate a profile to match a target reference
// profile.
namespace planner {
namespace speed {

struct Conflict {
  int constraint_ix = -1;  // Constraint ix of the conflict.
  int time_ix = -1;        // Time index of the conflict.
  // Position of the conflict, we should either yield to or pass.
  double pos = std::numeric_limits<double>::infinity();
  // Whether it is a pass or yield conflict.
  bool pass = true;
  void Clear();

  [[nodiscard]] std::string DebugString() const;
};

// This struct stores how many time steps a jerk be applied for a speed state.
struct JerkAndSteps {
  double jerk = -std::numeric_limits<double>::infinity();
  int steps = 0;
};

using JerkSequence = std::vector<JerkAndSteps>;

// Returns the minimum steps to go from current state to the min_accel without
// undershooting 'expected_v' and updates 'mutable_state'. This is the phase
// that we press on brake peddle when the go is in deceleration state. Populates
// 'can_release_wo_undershoot_expected_v' with true, if it is possible to
// release brake from 'mutable_state' to zero acceleration without going below
// expected speed.
int BrakeToMinAccel(const Limits& limits, double dt, double expected_v,
                    State& mutable_state,
                    bool* can_release_wo_undershoot_expected_v,
                    JerkSequence* brake_jerks = nullptr);

// Returns the minimum number of time steps to go from positive acceleration to
// negative acceleration and updates |mutable_state|. Updates |reach_expected_v|
// to true if ego already reaches |expected_v| at the end of this stage.
int ReleaseGasAndGetOnBrake(const Limits& limits, double dt, double expected_v,
                            State& mutable_state, bool* reach_expected_v,
                            JerkSequence* brake_jerks = nullptr);

// Returns true if it is possible to release brake from 'current_state' with max
// jerk without resulting in undershooting the goal_v at zero_accel. Populates
// 'feasible_initial_min_accel' with the min feasible initial negative
// acceleration such that we do not result in undershoot (mostly used in
// avoid negative speed, which is a special-case undershoot when expected_v
// is zero). Also populates the jerk sequence and steps to reach zero_accel.
bool CanReleaseBrakeToZeroAccelWithoutUndershoot(
    const Limits& limits, const State& current_state, double dt,
    double expected_v, double* feasible_initial_min_accel = nullptr,
    int* release_brake_step = nullptr,
    JerkSequence* release_brake_jerks = nullptr);

// Returns true if it is possible to release gas from 'current_state' with min
// jerk and not overshooting the expected speed. It is a symmetric problem
// to the above.
bool CanReleaseGasToZeroAccelWithoutOvershoot(
    const Limits& limits, const State& current_state, double dt, double goal_v,
    int* release_gas_step = nullptr, JerkSequence* release_gas_jerks = nullptr);

// Returns true if we can release brake to get to zero_accel within
// limits and reach a const-speed state at |expected_v|. Also updates
// |mutable_state|. This is the last phase when ego is in deceleration mode.
bool ReleaseBrakeToZeroAccelToExpectedSpeed(
    const Limits& limits, double dt, double expected_v, bool check_neg_accel,
    State& mutable_state, int* brake_steps,
    JerkSequence* brake_jerks = nullptr);

// Returns true if we reach steady state with the found |brake_jerks| to go
// to expected speed and updates |mutable_state|. Moves the state forward with
// |brake_jerks|.
bool DoLastPhaseToExpectedSpeed(const Limits& limits, double dt,
                                double expected_v, const std::string& debug_str,
                                State& mutable_state, int* steps,
                                JerkSequence* brake_jerks);

// Returns true if we can find a braking profile that avoids the conflict.
// Otherwise, returns false.
bool CanBrakeForConflict(int brake_first_ix, const Conflict& conflict,
                         const ProfileLimits& profile_limits,
                         const Profile& profile, double dt,
                         bool for_risk_search);

// Returns true if we can accelerate from the |trial_ix| of |profile| without
// having a conflict with |prev_yield_conflict|. Othewise, returns false.
bool CanAccelWithoutConflict(const Profile& sampled_ref_profile, int trial_ix,
                             const Conflict& prev_yield_conflict,
                             const ProfileLimits& profile_limits,
                             const Profile& profile, double dt, int n_steps,
                             bool for_risk_search);

// Modifies |mutable_profile| from |brake_first_ix| to brake as hard as possible
// within |limits| until |brake_end_ix|. If the length of the mutable_profile is
// shorter than |brake_end_ix + 1|, it will be extended to the length. If the
// accel of the state at |brake_first_ix| is already smaller than the bound of
// limits, it will keep the out-of-limit accel (to brake as hard as possible)
// instead of trying to release brake to regulate the state back to |limits|.
// The profile will try to finally reach a stable state with |expected_v| and
// zero acceleration. |nullable_sampled_ref_profile| is used for the
// accelerating part after ego is unavoidably undershoot (aka. ego speed <
// |expected_v|). It can only be nullptr if the expected_v is zero (aka. ego
// won't undershoot). Readers can refer to the profile generation logic in
// https://docs.google.com/document/d/15VVdLPGcvjtwDnpi35ZpUHtuqvER-pc33j3cUmfs9zM
void GenerateBrakeProfileToExpectedSpeed(
    int brake_first_ix, int brake_end_ix, const Limits& limits, double dt,
    double expected_v, const Profile* nullable_sampled_ref_profile,
    Profile* mutable_profile);

// Special version of the above to generate a fully brake profile towards zero
// speed.
void GenerateBrakeProfile(int brake_first_ix, int brake_end_ix,
                          const Limits& limits, double dt,
                          Profile* mutable_profile);

// This function splits the brake into two steps: Before switch time, use
// regular limits to brake; After switch time, use elevated limits to brake if
// for risk search profile.
void GenerateBrakeProfileWithProfileLimits(int brake_first_ix, int brake_end_ix,
                                           const ProfileLimits& profile_limits,
                                           double dt, bool for_risk_search,
                                           Profile* mutable_profile);

// This function uses a small limit(0.1) to generate a braking profile that
// ensures comfort during the parking process.
Profile GenerateComfortStoppingProfile(const State& initial_state,
                                       int num_steps);

// Generates stopped profile from initial state according to the given limits.
void GenerateStopProfileWithLimits(const State& planning_init_state,
                                   int num_steps, const Limits& limits,
                                   Profile* stopped_profile);

// Modifies 'mutable_profile' to reach an expected speed with limits.
// If the 'modified_first_ix' state is higher than expected_v, it generates a
// brake profile; otherwise it generates an acceleration profile. The generated
// acceleration profile will be bounded by the 'sampled_ref_profile'.
void GenerateProfileToExpectedSpeed(const Profile& sampled_ref_profile,
                                    int modified_first_ix, int modified_end_ix,
                                    const Limits& limits, double dt,
                                    double expected_v,
                                    Profile* mutable_profile);

// Returns true if we can adjust the profile down to avoid 'conflict'. Also
// updates 'mutable_profile' to be the brake profile until conflict.ix and
// populates 'first_change_ix' with the latest index we can brake. Sets
// 'first_change_ix' to be profile size if no need to brake. Returns false if
// impossible to brake for conflict, updates the 'mutable_profile' to be the
// braking profile from index 0 and sets 'first_change_ix' to -1.
bool BrakeForYieldConflict(const Conflict& conflict,
                           const ProfileLimits& profile_limits,
                           int start_search_ix, double dt, bool for_risk_search,
                           Profile* mutable_profile, int* first_change_ix,
                           double* x_resolve);

// Tries to find the earliest time to accelerate for |mutable_profile| while
// avoiding conflict with the |prev_yield_conflict|. Returns true if we find the
// earliest time to do so and updates the new profile to |mutable_profile|.
bool EarliestAccelWhileAvoidYieldConflict(
    const Conflict& prev_yield_conflict, const ProfileLimits& profile_limits,
    double dt, bool for_risk_search, const Profile& sampled_ref_profile,
    int n_steps, Profile& mutable_profile,
    int& earliest_accel_time_ix_for_adjust_for_pass);

// Returns true if we found an earliest conflict between the given
// 'profile' and the 'time_map'.
// |tree_switch_time_ix| is the time before which we observe all constraints,
// while after which we only honor either main constraints or tree
// constraints.
// |adjust_for_tree_branch| indicates whether or not this profile is a
// tree profile.
bool FindFirstConflictForProfile(
    pb::TreeSearchType::Enum tree_search_type, const TimeMap& time_map,
    const Profile& profile, const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions, int search_for_conflict_start_ix,
    const absl::flat_hash_map<int, GapAlignInfo>& gap_align_info_map,
    int tree_switch_time_ix, bool adjust_for_tree_branch, Conflict* conflict);

// Extends 'mutable_profile' by accelerating towards the 'sampled_ref_profile',
// until we reach the time horizon.
// If 'max_v' is given, the speed of 'mutable_profile' will be bounded by
// 'max_v' when accelerating towards the 'sampled_ref_profile'
void AccelerateTowardsReferenceProfile(const Profile& sampled_ref_profile,
                                       const Limits& limits,
                                       int profile_first_ix, double dt,
                                       int n_horizon,
                                       const std::optional<double>& max_v,
                                       Profile* mutable_profile,
                                       bool is_ref_reachable = true);

// This function splits the acceleration into two steps: Before switch time, use
// regular limits to accel; After switch time, use elevated limits to accel if
// for risk search profile.
// TODO(Tingran): Maybe consider moving |sampled_ref_profile| into
// profile_limits in order to make it consistent with the dynamic limits.
void AccelerateTowardsReferenceProfileWithProfileLimits(
    const Profile& sampled_ref_profile, const ProfileLimits& profile_limits,
    int profile_first_ix, double dt, bool for_risk_search, int n_horizon,
    const std::optional<double>& max_v, Profile& mutable_profile);

// Extends 'mutable_profile' by accelerating until we reach the time horizon.
// The difference with the above one is that the profile is only bounded
// by a given goal speed. Refer to the profile generation logic in
// https://docs.google.com/document/d/15VVdLPGcvjtwDnpi35ZpUHtuqvER-pc33j3cUmfs9zM
// TODO(tienan): Remove this function. It does not consider the reference
// profile thus is incompatible with other functions in the lib. Users should
// use AccelerateToExpectedSpeedUnderReference instead.
void AccelerateToExpectedSpeed(const Limits& limits, int profile_first_ix,
                               double dt, int n_horizon, double goal_v,
                               Profile* mutable_profile);

// Extends the mutable profile by moving one step by an amount dt
// while avoiding backward motion. The |min_jerk| and |max_jerk| are used for
// clamping |jerk_sol|. The |min_jerk| value will be further updated inside
// the function to stay in the jerk limit. The |max_jerk| value will be updated
// based on 1) it will not be too small that leads to a small acceleration value
// which violates the min accel limit; and 2) it will not be too large that
// violates the max jerk limit.".
void MoveNonBackwardOneStepGivenJerk(const Limits& limits, double dt,
                                     double jerk_sol, double min_jerk,
                                     double max_jerk, Profile& mutable_profile);

// Extends 'mutable_profile' by accelerating until we reach the time horizon.
// The profile is bounded by sampled ref profile and goal v at the same time.
// TODO(lewisliu): consolidate this and AccelerateTowardsReferenceProfile.
void AccelerateToExpectedSpeedUnderReference(const Profile& sampled_ref_profile,
                                             const Limits& limits,
                                             int profile_first_ix, double dt,
                                             int n_horizon, double goal_v,
                                             Profile* mutable_profile);

// Returns the uniformly time sampled profile of 'reference_profile' and also
// updates the profile such that it follows the 'limits'. Note that, if new
// limits are smaller than original profile, this may not work as intended.
Profile ResampleProfile(const Profile& reference_profile, const Limits& limits,
                        double dt, int n_horizon, int* start_ix = nullptr);

// Grows from last state of 'profile 'with brake max jerk for one step.
void ReleaseBrakeForOneStep(const Limits& limits, double dt, Profile* profile);

// Returns warm start profile from last trajectory starting at 'plan_init_time'.
// If the state is bad, returns nullopt.
std::optional<Profile> GenerateWarmStartSpeedProfileFromLastProfile(
    const Profile& last_profile, double plan_init_time, double plan_init_speed,
    double plan_init_accel, int n_horizon, double dt,
    const speed::Limits& limits, double min_speed_tolerance,
    double min_odom_tolerance, bool check_speed_accel_error = true);

// Extends profile by zero jerk when the horizon is less than n_horizon.
void ExtendProfileByZeroJerkOrReleasingBrake(int n_horizon, double dt,
                                             const speed::Limits& limits,
                                             Profile* mutable_profile);

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_SOLVER_SEARCHER_SIMPLE_PROFILE_SEARCHER_H_
