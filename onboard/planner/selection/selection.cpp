#include "planner/selection/selection.h"

#include <algorithm>
#include <cstdint>
#include <deque>
#include <limits>
#include <map>
#include <memory>
#include <optional>
#include <string>
#include <unordered_set>
#include <utility>

#include <glog/logging.h>
#include <tbb/parallel_for.h>
#include <tbb/tbb.h>

#include "absl/strings/str_format.h"
#include "adv_geom/path_curve.h"
#include "base/now.h"
#include "exception_handler/utility/common/exception_handler_util.h"
#include "geometry/algorithms/distance.h"
#include "geometry/model/oriented_box.h"
#include "geometry/model/point_2d.h"
#include "latency/pipeline_id.h"
#include "log_utils/log_macros.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "perception/common/map_util.h"
#include "planner/behavior/types/directive.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/waypoint_edge_sequence_generator/waypoint_edge_sequence_utility.h"
#include "planner/decoupled_maneuvers/path/reasoning/path_reasoning.h"
#include "planner/elective_lane_change/elective_lane_change_decider.h"
#include "planner/elective_lane_change/elective_lane_change_decider_utils.h"
#include "planner/open_space/unstuck_planner/unstuck_planner_invoker.h"
#include "planner/planning_gflags.h"
#include "planner/selection/costs/cost_registry.h"
#include "planner/selection/costs/longitudinal_diff_cost.h"
#include "planner/selection/costs/longitudinal_discomfort_cost.h"
#include "planner/selection/costs/mrc_latch_cost.h"
#include "planner/selection/costs/path_diff_cost.h"
#include "planner/selection/costs/safety_precheck_cost.h"
#include "planner/selection/costs/short_path_cost.h"
#include "planner/selection/costs/speed_diversity_cost.h"
#include "planner/selection/costs/traffic_rule_cost.h"
#include "planner/selection/dry_steering/dry_steering_state_manager.h"
#include "planner/selection/ml_selection/selectnet_sample_generator.h"
#include "planner/selection/risk/risk_reasoner.h"
#include "planner/selection/risk/risk_solver.h"
#include "planner/selection/risk/specialized_risk.h"
#include "planner/selection/route_feature_cost_utils.h"
#include "planner/selection/scenario_selectors/basic_scenario_selector.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_scenario_selector.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"
#include "planner/selection/scenario_selectors/lane_selection_scenario_selector.h"
#include "planner/selection/scenario_selectors/open_space_scenario_selector/open_space_scenario_selector.h"
#include "planner/selection/scenario_selectors/pull_out_scenario_selector/pull_out_scenario_selector.h"
#include "planner/selection/scenario_selectors/pull_over_scenario_selector/pull_over_scenario_selector.h"
#include "planner/selection/scenario_selectors/selector_utils.h"
#include "planner/selection/scenario_selectors/unstuck_scenario_selector.h"
#include "planner/selection/selection_rt_events.h"
#include "planner/selection/selection_utils.h"
#include "planner/selection/trajectory_meta.h"
#include "planner/selection/trajectory_precheck.h"
#include "planner/selection/unstuck_handle.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/discomforts/discomfort_varying_limits.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/speed_common/honk_request.h"
#include "planner/trajectory/evaluation/trajectory_evaluation_util.h"
#include "planner/trajectory/util/trajectory_util.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/utility/stuck_understanding/stuck_understanding_utils.h"
#include "planner/utility/unstuck/unstuck_util.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/unstuck_planner_seed.pb.h"
#include "prediction/common/utility/prediction_utility.h"
#include "rt_event/rt_event.h"
#include "strings/stringprintf.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace selection {
using BoxEdgeType = math::geometry::Box2dEdgeType;

namespace {
using CostComputerFunctionPtr =
    std::function<std::vector<double>(const std::vector<TrajectoryMetaData*>&)>;
using CostType = planner::selection::pb::CostType;
using CostArbitratorFunctionPtr =
    std::function<void(const std::vector<TrajectoryMetaData*>&,
                       std::unordered_map<CostType, CostComputerMetadata>*)>;
using CostClampFunc = std::function<double(const CostType&, double)>;

// If the number of pose is less than this, it is unsafe.
constexpr int kMinNumTrajectoryPose = 40;

constexpr int kMaxDecisionHistorySize = 20;

// Cap the maximum difference between candidates' risk cost. This is to avoid
// risk spike causing ego to make very harsh swerve even its discomfort is high.
constexpr double kMaxCollisionRiskDiff = 2.5;

// Usually RA unstuck is urgent, minus overall cost by kRAUnstuckAdjustReward to
// make sure it can be selected with high priority.
constexpr double kRAUnstuckAdjustReward = 10.0;

// The Speed Discomfort will be discounted when Speed Diversity is activated,
// for in speed diversity, the uncertainty risk is activated which is a better
// signal than SpeedDiscomfort.
constexpr double kSpeedDiversitySpeedDiscomfortFactor = 0.2;
constexpr double kMultiLKSelectWeightAdjustRisk = 0.8;

// If risk signal is detected recently.
constexpr int kRiskSignalLastDetectedTimeBuffer = 1000;  // in ms

// Debug format for unsafe reason.
constexpr char kNumPoseFail[] = "poses_size";

// The dry steering angle diff to stop dry steering.
constexpr double kSteeringDiffThresholdToStopDrySteering = 0.015;

// The duration threshold to latch a positive stuck signal in Ms.
constexpr int kLatchDurationOfPositiveStuckSignalInMsec = 5000;

TrajectoryMetaData* SelectWaypointAssistTrajectoryIfPossible(
    const SpeedWorldModel& world_model,
    const std::vector<TrajectoryMetaData*>& trajectory_candidates) {
  if (!world_model.waypoint_assist_tracker().IsWaypointAssistInvoked()) {
    return nullptr;
  }

  // There could be multiple valid solutions. The expected behavior here is to
  // return a valid one with the lowest possible overall cost.
  std::vector<TrajectoryMetaData*> sorted_trajectory_candidates(
      trajectory_candidates);
  std::sort(sorted_trajectory_candidates.begin(),
            sorted_trajectory_candidates.end(),
            [](TrajectoryMetaData* a, TrajectoryMetaData* b) {
              return a->overall_cost < b->overall_cost;
            });
  bool is_waypoint_backup_trajectory_used = std::all_of(
      sorted_trajectory_candidates.begin(), sorted_trajectory_candidates.end(),
      [](const TrajectoryMetaData* meta) {
        return meta->trajectory_info->is_waypoint_backup_trajectory_used();
      });

  // Select the lane keep trajectory with nudge intention when ego try to nudge
  // blockage.
  // TODO(shaxiang): also exploit LC/SN for unblock for RA.
  auto iter = std::find_if(
      sorted_trajectory_candidates.begin(), sorted_trajectory_candidates.end(),
      [&is_waypoint_backup_trajectory_used](const TrajectoryMetaData* meta) {
        return (meta->IsInLaneNudge() &&
                meta->trajectory_info->is_waypoint_backup_trajectory_used() ==
                    is_waypoint_backup_trajectory_used);
      });

  // When there is no nudge intention or ego stucked by non-blockage reason
  // (such as end of lane sequence), select the lane keep trajectory with ignore
  // all intention.
  if (iter == sorted_trajectory_candidates.end()) {
    iter = std::find_if(
        sorted_trajectory_candidates.begin(),
        sorted_trajectory_candidates.end(),
        [&is_waypoint_backup_trajectory_used](const TrajectoryMetaData* meta) {
          return (meta->IsAcc() &&
                  meta->trajectory_info->is_waypoint_backup_trajectory_used() ==
                      is_waypoint_backup_trajectory_used);
        });
  }

  if (iter == sorted_trajectory_candidates.end()) {
    LOG(ERROR) << "No feasible waypoint assist trajectory is selected.";
    return nullptr;
  }

  return *iter;
}

void AddIsSelectedToCostDebugTable(const std::string& trajectory_unique_id,
                                   pb::CostDebugTable* cost_debug_table_ptr) {
  if (!cost_debug_table_ptr) return;
  for (auto& trajectory_debug_info :
       *cost_debug_table_ptr->mutable_trajectories_debug_info()) {
    trajectory_debug_info.set_is_selected(
        trajectory_debug_info.trajectory_name() == trajectory_unique_id);
  }
}

// TODO(liwen): The function is for temp fix of pull over pull out scenario
// recognization, clean it when together with the temp fix later.
// Returns list of safe trajectory candidates, if all the original trajectory
// candidates are not safe, or it's not in PIPO scenario, it will return all the
// original candidates.
std::vector<TrajectoryMetaData*> GetSafeCandidatesForPullOverPullOut(
    const std::vector<TrajectoryMetaData*>& candidates) {
  DCHECK(!candidates.empty());

  // The function should only be invoked in PIPO scenario.
  DCHECK((candidates.front()->trajectory_info != nullptr &&
          candidates.front()->trajectory_info->is_pull_over_triggered()) ||
         candidates.front()->IsPullOutTriggered());

  std::vector<TrajectoryMetaData*> safe_candidates;
  safe_candidates.reserve(candidates.size());
  for (const auto& candidate : candidates) {
    if (candidate->is_safe) {
      safe_candidates.push_back(candidate);
    }
  }

  // If NO feasible candidate, just return all.
  if (safe_candidates.empty()) {
    LOG(ERROR) << "All trajectory candidates are infeasible!";
    for (const auto& candidate : candidates) {
      if (candidate->IsMLTrajectory()) continue;
      safe_candidates.push_back(candidate);
      LOG(ERROR) << "fail reason " << candidate->unsafe_reason;
    }
  }
  return safe_candidates;
}

// Returns true if pull over is triggered and has safe pull over trajectory
// candidate.
bool IsPullOverAvailable(const std::vector<TrajectoryMetaData*>& candidates) {
  if (candidates.front()->trajectory_info == nullptr ||
      !candidates.front()->trajectory_info->is_pull_over_triggered()) {
    return false;
  }

  const std::vector<TrajectoryMetaData*> safe_candidates =
      GetSafeCandidatesForPullOverPullOut(candidates);
  return std::any_of(safe_candidates.begin(), safe_candidates.end(),
                     [](const TrajectoryMetaData* candidate) {
                       return candidate->IsPullOver();
                     });
}
}  // namespace

void TrajectorySelector::RecognizeScenarioType(
    const std::vector<TrajectoryMetaData*>& candidates,
    planner::pb::TrajectorySelectionDebug* debug) {
  const auto previous_selection_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());
  const auto previous_worldmodel_seed =
      Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame());
  const auto previous_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedLastFrame());
  auto mutable_selection_seed =
      Seed::Access<token::SelectionSeed>::MutableMsg(SpeedCurrentFrame());
  auto mutable_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(
          SpeedCurrentFrame());
  // TODO(liwen): This is a temporary fix to only provide safe trajectories to
  // pull over and pull out here, because pipo stores a local copy of the
  // trajectories below. Even though these unsafe trajectories have a
  // overwhelmingly large cost, pipo has its own selection logic and there is
  // chance the unsafe trajectories can be chosen.
  const bool pull_over_available = IsPullOverAvailable(candidates);
  const bool pull_out_available =
      std::any_of(candidates.begin(), candidates.end(),
                  [](const auto* cand) { return cand->IsPullOutTriggered(); });
  const bool unstuck_scenario_check = CheckScenarioUnstuck(
      world_model_, candidates, previous_selection_seed.get(),
      mutable_selection_seed.get());
  const bool cross_lane_available =
      std::any_of(candidates.begin(), candidates.end(),
                  [](const auto* cand) { return cand->IsCrossLane(); });
  const bool multi_lf_scenario_check =
      LaneSelectionScenarioSelector::CheckScenarioCondition(
          candidates, world_model_, lane_sequence_candidates_,
          previous_worldmodel_seed->plan_execute_time_ms(),
          mutable_selection_seed.get());

  const bool open_space_available =
      Seed::Access<token::UnstuckPlannerSeed>::GetMsg(SpeedCurrentFrame())
          ->current_state() != planner::pb::UnstuckPlannerState::IDLE;

  DCHECK_NE(mutable_maneuver_seed, nullptr);
  if (open_space_available) {
    enable_open_space_scenario_ = true;
    scenario_type_ = planner::pb::SelectionScenarioType::SCENARIO_OPEN_SPACE;
    scenario_selector_ptr_ = std::make_unique<OpenSpaceScenarioSelector>(
        trajectory_selection_config_, world_model_, *mutable_maneuver_seed);
  } else if (pull_over_available) {
    enable_pull_over_scenario_ = true;
    scenario_type_ = planner::pb::SelectionScenarioType::SCENARIO_PULL_OVER;
    scenario_selector_ptr_ = std::make_unique<PullOverScenarioSelector>(
        trajectory_selection_config_, world_model_, *previous_maneuver_seed,
        *mutable_maneuver_seed, GetSafeCandidatesForPullOverPullOut(candidates),
        debug == nullptr ? nullptr
                         : debug->mutable_pull_over_selection_debug());
  } else if (pull_out_available) {
    enable_pull_out_scenario_ = true;
    scenario_type_ = planner::pb::SelectionScenarioType::SCENARIO_PULL_OUT;
    std::vector<TrajectoryMetaData*> safe_candidates =
        GetSafeCandidatesForPullOverPullOut(candidates);
    pull_out_meta_data_ = std::make_optional<PullOutMetaData>(
        world_model_, safe_candidates,
        trajectory_selection_config_.pull_out_jump_out_selection_config(),
        mutable_selection_seed->mutable_pull_out_selection_seed(),
        DCHECK_NOTNULL(debug)->mutable_pull_out_meta_data_debug());
    scenario_selector_ptr_ = std::make_unique<PullOutScenarioSelector>(
        trajectory_selection_config_, world_model_, *mutable_maneuver_seed,
        pull_out_meta_data_.value(), std::move(safe_candidates), debug);
  } else if (unstuck_scenario_check) {
    enable_unstuck_scenario_ = true;
    scenario_type_ = planner::pb::SelectionScenarioType::SCENARIO_UNSTUCK;
    scenario_selector_ptr_ = std::make_unique<UnstuckScenarioSelector>(
        trajectory_selection_config_, world_model_, *previous_maneuver_seed,
        trajectory_selection_config_, lane_sequence_candidates_,
        previewed_lane_change_infos_);
  } else if (cross_lane_available) {
    enable_lane_change_scenario_ = true;
    scenario_type_ = planner::pb::SelectionScenarioType::SCENARIO_LANE_CHANGE;
    scenario_selector_ptr_ = std::make_unique<LaneChangeScenarioSelector>(
        trajectory_selection_config_, world_model_, *previous_maneuver_seed,
        *mutable_maneuver_seed);
  } else if (multi_lf_scenario_check) {
    enable_multi_lf_selection_ = true;
    scenario_type_ =
        planner::pb::SelectionScenarioType::SCENARIO_FORK_SELECTION;
    scenario_selector_ptr_ = std::make_unique<LaneSelectionScenarioSelector>(
        trajectory_selection_config_, world_model_, *previous_maneuver_seed,
        *mutable_maneuver_seed, trajectory_selection_config_,
        lane_sequence_candidates_, previewed_lane_change_infos_);
  } else {
    scenario_selector_ptr_ = std::make_unique<BasicScenarioSelector>(
        trajectory_selection_config_, world_model_, *previous_maneuver_seed);
  }
  enable_speed_diversity_ = IsSpeedDiversityActivated(candidates);

  if (previous_selection_seed->ego_scenario_type() ==
          planner::pb::SelectionScenarioType::SCENARIO_LANE_CHANGE &&
      scenario_type_ !=
          planner::pb::SelectionScenarioType::SCENARIO_LANE_CHANGE) {
    LaneChangeScenarioSelector::Clear();
  }

  // TODO(selection): remove scenario_type in TrajectoryMetaData if possible as
  // it's a scene level concept.
  std::for_each(candidates.begin(), candidates.end(),
                [=](auto* cand) { cand->scenario_type = scenario_type_; });
  mutable_selection_seed->set_ego_scenario_type(scenario_type_);

  // compute if risk signal was fired.
  const auto& last_risk_signal = previous_selection_seed->risk_signal();
  const int64_t plan_init_timestamp =
      world_model_.robot_state().plan_init_state_snapshot().timestamp();
  is_risk_signal_detected_ =
      (plan_init_timestamp - last_risk_signal.last_detected_timestamp()) <
      kRiskSignalLastDetectedTimeBuffer;
}

TrajectoryMetaData* TrajectorySelector::SelectProperTrajectory(
    const SpeedWorldModel& world_model,
    const std::vector<TrajectoryMetaData*>& trajectory_candidates) {
  // remove unsafe trajectories
  std::vector<TrajectoryMetaData*> safe_trajectory_candidates;
  for (const auto& candidate : trajectory_candidates) {
    if (!candidate->is_safe) continue;
    safe_trajectory_candidates.push_back(candidate);
  }
  // If NO feasible candidate, just return all.
  // TODO(zhanghuanming): revisit and need more reasonable fallback method.
  // TODO(wutulin): try replace this with just turn nullptr. Right now the
  // autonomy stack will crash.
  if (safe_trajectory_candidates.empty()) {
    LOG(ERROR) << "All trajectory candidates are infeasible!";
    safe_trajectory_candidates = trajectory_candidates;
  }

  // Forces to select the ml path if available.
  if (FLAGS_planning_enable_forcing_selecting_ml_path) {
    for (TrajectoryMetaData* trajectory_candidate :
         safe_trajectory_candidates) {
      if (trajectory_candidate->trajectory_type() ==
          planner::pb::TrajectoryType::ML_PATH) {
        trajectory_candidate->is_selected = true;
        return trajectory_candidate;
      }
    }
  }
  // Returns the pointer to the trajectory with some additional signal when RA
  // is invoked.
  TrajectoryMetaData* selected_trajectory =
      SelectWaypointAssistTrajectoryIfPossible(world_model,
                                               safe_trajectory_candidates);
  if (selected_trajectory) {
    selected_trajectory->is_selected = true;
    return selected_trajectory;
  }

  DCHECK(safe_trajectory_candidates.front() != nullptr);
  DCHECK(scenario_selector_ptr_ != nullptr);
  // select best trajectory for different scenario
  TrajectoryMetaData* best_trajectory = nullptr;
  // TODO(wutulin): this is a short-term work around for some special maneuvers
  // that do not choose the lowest cost trajectory
  if (enable_open_space_scenario_) {
    OpenSpaceScenarioSelector* open_space_scenario_selector =
        static_cast<OpenSpaceScenarioSelector*>(scenario_selector_ptr_.get());
    best_trajectory =
        open_space_scenario_selector->SelectProperOpenSpaceTrajectory(
            safe_trajectory_candidates);
  } else if (enable_pull_over_scenario_) {
    PullOverScenarioSelector* pull_over_scenario_selector =
        static_cast<PullOverScenarioSelector*>(scenario_selector_ptr_.get());
    best_trajectory =
        pull_over_scenario_selector->SelectProperPullOverTrajectory(
            safe_trajectory_candidates);
  } else if (enable_pull_out_scenario_) {
    PullOutScenarioSelector* pull_out_scenario_selector =
        static_cast<PullOutScenarioSelector*>(scenario_selector_ptr_.get());
    best_trajectory = pull_out_scenario_selector->SelectProperPullOutTrajectory(
        safe_trajectory_candidates);
  } else {
    best_trajectory = scenario_selector_ptr_->SelectLowestCostTrajectory(
        safe_trajectory_candidates);
  }

  return best_trajectory;
}

TrajectoryMetaData* TrajectorySelector::SelectBestTrajectory(
    tbb::concurrent_vector<TrajectoryMetaData>& candidates,
    planner::pb::TrajectorySelectionDebug* debug) {
  std::string debug_info;
  TRACE_EVENT_SCOPE(
      planner, DecoupledSelection_Total,
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>());
  if (candidates.empty()) {
    return nullptr;
  }
  DCHECK(unique_id_to_traj_.empty())
      << "SelectBestTrajectory should only be called once per object";
  for (auto& candidate : candidates) {
    unique_id_to_traj_[candidate.unique_id] = &candidate;
  }

  // Step 1: safety check.
  std::vector<TrajectoryMetaData*> feasible_trajectory_candidates =
      GenerateFeasibleTrajectoryList();
  // No feasible trajectory candidates.
  if (feasible_trajectory_candidates.empty()) {
    FillDebugProto(debug_info, /*selected_trajectory=*/nullptr, debug);
    return nullptr;
  }

  // Scenario decider
  RecognizeScenarioType(feasible_trajectory_candidates, debug);
  // Step 2: calculate features and weighed sum for ranking.
  ComputeAndPopulateOverallCost(feasible_trajectory_candidates, &debug_info,
                                debug);

  // Step 3: select proper trajectory.
  // Usually select the trajectory with minimum overall cost when RA is not
  // invoked. Selects trajectory with some additional signal when RA is invoked.
  TrajectoryMetaData* selected_trajectory =
      SelectProperTrajectory(world_model_, feasible_trajectory_candidates);

  auto mutable_selection_seed =
      Seed::Access<token::SelectionSeed>::MutableMsg(SpeedCurrentFrame());

  // Step 4: Decide stuck related operations.
  HandleStuck(lane_sequence_candidates_, world_model_,
              *Seed::Access<token::UnstuckSeed>::GetMsg(SpeedLastFrame()),
              feasible_trajectory_candidates, selected_trajectory,
              selected_trajectory->speed_result.speed_pipeline_meta()
                  .elc_risk_directives,
              elective_lane_change_decider_, debug,
              selected_trajectory->stuck_signal_meta.optional_stuck_debug);

  // Step 5: Invoke unstuck planner or dry steering manager to get unstuck.
  unstuck::UnstuckPlannerInvoker unstuck_planner_invoker;
  if (!unstuck_planner_invoker.Invoke(
          unstuck_planner_config_, world_model_, feasible_trajectory_candidates,
          pull_out_meta_data_,
          *Seed::Access<token::UnstuckPlannerSeed>::GetMsg(SpeedLastFrame()),
          selected_trajectory,
          Seed::Access<token::UnstuckPlannerSeed>::MutableMsg(
              SpeedCurrentFrame())
              .get(),
          DCHECK_NOTNULL(debug)->mutable_unstuck_planner_debug())) {
    // Unstuck planner is not invoked.
    DrySteeringStateManager dry_steering_manager;
    dry_steering_manager.UpdateDrySteeringTargetAngle(
        world_model_, feasible_trajectory_candidates,
        object_required_lat_gap_map_, cz_required_lat_gap_map_,
        curr_reverse_driving_seed_, selected_trajectory,
        mutable_selection_seed->mutable_dry_steering_seed());

    if (FLAGS_planning_enable_dry_steering_controller) {
      // Stop dry steering if the current steer is reaching the target angle.
      if (selected_trajectory != nullptr &&
          selected_trajectory->dry_steering_target_angle.has_value()) {
        const double steering_diff = math::AngleDiff(
            selected_trajectory->dry_steering_target_angle.value(),
            world_model_.robot_state().current_state_snapshot().steering());
        if (std::abs(steering_diff) < kSteeringDiffThresholdToStopDrySteering) {
          selected_trajectory->dry_steering_target_angle = std::nullopt;
        } else {
          // If steering angle is not reaching target, control will continue dry
          // steering.
          mutable_selection_seed->mutable_dry_steering_seed()
              ->set_last_dry_steering_timestamp(
                  world_model_.snapshot_timestamp());
        }
      }
    }
  }

  // Step 6: Compute risk signal.
  // Update Risk Signal.
  if (selected_trajectory != nullptr && FLAGS_planning_enable_risk_signal) {
    ComputeRiskSignal(*selected_trajectory,
                      selected_trajectory->ToRiskEvalTarget(), debug);
  }

  FillDebugProto(debug_info, selected_trajectory, debug);
  RecordRtEvents(selected_trajectory, feasible_trajectory_candidates);
  UpdateSeed(selected_trajectory, feasible_trajectory_candidates);
  return selected_trajectory;
}

std::vector<TrajectoryMetaData*>
TrajectorySelector::GenerateFeasibleTrajectoryList() {
  std::vector<TrajectoryMetaData*> safety_checked_candidates{};
  safety_checked_candidates.reserve(unique_id_to_traj_.size());
  for (auto& kv : unique_id_to_traj_) {
    TrajectoryMetaData& candidate = *kv.second;
    safety_checked_candidates.push_back(&candidate);

    // If the trajectory is too short, reject.
    if (candidate.trajectory.poses_size() < kMinNumTrajectoryPose) {
      candidate.is_safe = false;
      candidate.unsafe_reason = kNumPoseFail;
      candidate.overall_cost = kMaxUnsafeOverallCost;
      continue;
    }
    if (candidate.IsMLTrajectory() &&
        (candidate.HasSpeedSearchFailed() ||
         IsMLTrajectoryInitErrorTooLarge(
             world_model_.robot_state().plan_init_state_snapshot(),
             candidate))) {
      candidate.is_safe = false;
      candidate.unsafe_reason = "Speed search failed or init error too large.";
      continue;
    }
    // TODO(selection): add static collision check into risk cost term.
    const ViolationResult violation_result = ViolateDynamicLimits(
        candidate, speed_generator_config_, trajectory_selection_config_);
    if (violation_result.is_violated) {
      candidate.is_safe = false;
      DCHECK(violation_result.unsafe_reason.has_value());
      candidate.unsafe_reason = *violation_result.unsafe_reason;
      candidate.overall_cost = kMaxUnsafeOverallCost;
      LOG(ERROR) << "unsafe " << candidate.lane_seq_ix() << ","
                 << candidate.homotopy() << ":" << candidate.unsafe_reason;
    } else {
      candidate.is_safe = true;
    }
  }

  // Filter out error trajectories which miss red traffic light yield
  // constraints.
  MarkRedTrafficLightRunner(safety_checked_candidates);

  return safety_checked_candidates;
}

void TrajectorySelector::RegisterCosts(
    const std::unique_ptr<BasicScenarioSelector>& scenario_selector_ptr,
    const std::vector<AgentInterpolateInfo>& agents_info,
    const std::vector<TrajectoryPoseInterpolator>& agents_trajectory,
    const RiskEvalContext& risk_eval_context) {
  DCHECK(scenario_selector_ptr != nullptr);
  CostRegistry& registry = CostRegistry::GetInstance();
  const planner::pb::CostWeights& weight_config =
      scenario_selector_ptr->get_cost_weights();

  {
    const CostArbitratorFunctionPtr cost_arbitrator_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates,
            std::unordered_map<CostType, CostComputerMetadata>* sub_costs_ptr) {
          return scenario_selector_ptr->CostArbitrator(candidates,
                                                       sub_costs_ptr);
        };
    registry.RegisterCostArbitrator(cost_arbitrator_func_ptr);
  }
  {
    const CostClampFunc cost_clamp_func = [&](const CostType& cost_type,
                                              double cost_value) {
      return scenario_selector_ptr->CostClamp(cost_type, cost_value);
    };
    registry.RegisterCostClampFunc(cost_clamp_func);
  }
  // SAFETY_PRECHECK cost
  {
    const CostComputerFunctionPtr safety_precheck_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return ComputeSafetyPrecheckCost(candidates,
                                           trajectory_selection_config_);
        };
    registry.RegisterCost(CostType::SAFETY_PRECHECK, kSafetyPrecheckWeight,
                          safety_precheck_cost_func_ptr);
  }
  // COLLISION_RISK cost
  {
    const CostComputerFunctionPtr risk_computer_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return ComputeAndPopulateRiskCost(risk_eval_context, candidates);
        };
    registry.RegisterCost(CostType::COLLISION_RISK,
                          weight_config.collision_risk(), risk_computer_ptr);
  }
  // STUCK_POTENTIAL cost
  {
    const CostComputerFunctionPtr stuck_potential_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateStuckCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->stuck_potential;
          }
          return cost;
        };
    if (!enable_multi_lf_selection_) {
      registry.RegisterCost(CostType::STUCK_POTENTIAL,
                            weight_config.stuck_potential(),
                            stuck_potential_cost_func_ptr);
    }
  }
  // PROGRESS_IMMEDIATE cost
  {
    const CostComputerFunctionPtr progress_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateProgressCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->progress_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::PROGRESS_IMMEDIATE,
                          weight_config.progress(), progress_cost_func_ptr);
  }
  // TRAFFIC_RULE cost
  {
    const CostComputerFunctionPtr traffic_rule_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return ComputeTrafficRuleCost(candidates, world_model_);
        };
    registry.RegisterCost(CostType::TRAFFIC_RULE, weight_config.traffic_rule(),
                          traffic_rule_cost_func_ptr);
  }
  // ROUTING cost
  {
    const CostComputerFunctionPtr routing_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateRoutingCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->normalized_routing_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::ROUTING, weight_config.routing(),
                          routing_cost_func_ptr);
  }
  // LONGITUDINAL_DISCOMFORT cost
  {
    const CostComputerFunctionPtr longitudinal_discomfort_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return ComputeBasicLongitudinalDiscomfortCost(
              candidates, trajectory_selection_config_);
        };
    registry.RegisterCost(CostType::LONGITUDINAL_DISCOMFORT,
                          weight_config.longitudinal_discomfort(),
                          longitudinal_discomfort_cost_func_ptr);
  }
  // LONGITUDINAL_DIFF cost
  if (FLAGS_planning_enable_selection_longitudinal_speed_diff_cost) {
    const CostComputerFunctionPtr longitudinal_diff_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          const auto previous_maneuver_seed =
              Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                  SpeedLastFrame());
          return ComputeLongitudinalSpeedDiffCost(
              candidates, previous_maneuver_seed->selected_trajectory(),
              trajectory_selection_config_);
        };
    registry.RegisterCost(CostType::LONGITUDINAL_DIFF,
                          weight_config.longitudinal_diff(),
                          longitudinal_diff_cost_func_ptr);
  }
  // LATERAL_DISCOMFORT cost
  {
    const CostComputerFunctionPtr lateral_discomfort_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateLateralDiscomfortCost(
              candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->lateral_discomfort;
          }
          return cost;
        };
    registry.RegisterCost(CostType::LATERAL_DISCOMFORT,
                          weight_config.lateral_discomfort(),
                          lateral_discomfort_cost_func_ptr);
  }
  // SPEED_DISCOMFORT cost
  {
    const CostComputerFunctionPtr speed_discomfort_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateSpeedDiscomfortCost(
              candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->normalized_speed_discomfort_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::SPEED_DISCOMFORT,
                          weight_config.speed_discomfort(),
                          speed_discomfort_cost_func_ptr);
  }
  // GEOMETRY_LANE_MARKING cost
  {
    const CostComputerFunctionPtr geometry_lane_marking_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateGeometryLaneMarkingCost(
              candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->geometry_lane_marking_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::GEOMETRY_LANE_MARKING,
                          weight_config.geometry_lane_marking(),
                          geometry_lane_marking_cost_func_ptr);
  }
  // GEOMETRY_HARD_BOUNDARY cost
  {
    const CostComputerFunctionPtr geometry_hard_boundary_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateGeometryHardBoundaryRiskCost(
              candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->geometry_hard_boundary_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::GEOMETRY_HARD_BOUNDARY,
                          weight_config.geometry_hard_boundary(),
                          geometry_hard_boundary_cost_func_ptr);
  }
  // PATH_DIFF cost
  {
    const CostComputerFunctionPtr path_diff_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulatePathDiffCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->normalized_path_diff;
          }
          return cost;
        };
    registry.RegisterCost(CostType::PATH_DIFF, weight_config.path_diff(),
                          path_diff_cost_func_ptr);
  }
  // LATERAL_DIFF cost
  {
    const CostComputerFunctionPtr lateral_diff_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateLateralDiffCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->normalized_lateral_diff;
          }
          return cost;
        };
    registry.RegisterCost(CostType::LATERAL_DIFF, weight_config.lateral_diff(),
                          lateral_diff_cost_func_ptr);
  }
  // LANE_SEQ_FLICKER cost
  {
    const CostComputerFunctionPtr lane_seq_flicker_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateLaneSequenceFlickerCost(
              candidates, is_lane_change_ready_);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->lane_seq_flicker_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::LANE_SEQ_FLICKER,
                          weight_config.lane_seq_flicker(),
                          lane_seq_flicker_cost_func_ptr);
  }
  // LAST_PATH cost
  {
    const CostComputerFunctionPtr last_path_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          scenario_selector_ptr->ComputeAndPopulateLastPathCost(candidates);
          std::vector<double> cost(candidates.size(), 0.0);
          for (size_t i = 0; i < candidates.size(); ++i) {
            cost[i] = candidates[i]->last_path_cost;
          }
          return cost;
        };
    registry.RegisterCost(CostType::LAST_PATH, weight_config.last_path_cost(),
                          last_path_cost_func_ptr);
  }
  // SHORT_PATH cost
  {
    registry.RegisterCost(CostType::SHORT_PATH, weight_config.short_path_cost(),
                          ComputeShortPathCost);
  }
  // LC_CRAWL_REWARD cost
  {
    registry.RegisterCost(
        CostType::LC_CRAWL_REWARD, /*cost_weight=*/
        LaneChangeScenarioSelector::kLaneChangeCrawlRewardCostWeight,
        LaneChangeScenarioSelector::ComputeLcCrawlRewardCost);
  }
  // LC_RA_ABORT cost
  if (FLAGS_planning_enable_lane_change_abort_response) {
    registry.RegisterCost(
        CostType::LC_RA_ABORT, /*cost_weight=*/
        LaneChangeScenarioSelector::kManualLaneChangeAbortCostWeight,
        LaneChangeScenarioSelector::ComputeLcRaAbortCost);
  }
  // SPEED_DIVERSITY cost
  if (enable_speed_diversity_ && last_speed_profile_.has_value()) {
    const CostComputerFunctionPtr speed_diversity_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return ComputeSpeedDiversityCost(candidates, *last_speed_profile_,
                                           trajectory_selection_config_);
        };
    registry.RegisterCost(CostType::SPEED_DIVERSITY, weight_config.speed_diff(),
                          speed_diversity_cost_func_ptr);
  }
  // HUMAN_LIKENESS cost
  // ML score is computed last because it requires rule-based overall costs for
  // pair-wise comparison.
  if (FLAGS_planning_enable_selection_human_likeness_cost &&
      !enable_lane_change_scenario_ && !enable_speed_diversity_) {
    bool is_fork_infront_junction_turning_scenario = false;
    // TODO(fengshi): try to remove this flag or turn off human likenss in fork
    if (enable_multi_lf_selection_) {
      const LaneSelectionScenarioSelector* multi_lf_selector =
          static_cast<LaneSelectionScenarioSelector*>(
              scenario_selector_ptr_.get());
      is_fork_infront_junction_turning_scenario =
          multi_lf_selector->is_fork_infront_junction_turning_scenario();
    }
    const CostComputerFunctionPtr human_likeness_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          return selectnet_cost_calculator_.ComputeHumanLikenessCost(
              candidates, agents_info, agents_trajectory,
              enable_unstuck_scenario_, enable_multi_lf_selection_,
              is_fork_infront_junction_turning_scenario, debug_info_);
        };

    registry.RegisterCost(CostType::HUMAN_LIKENESS,
                          weight_config.human_likeness_cost(),
                          human_likeness_cost_func_ptr);
  }
  // MRC_LATCH cost
  // Ego is expected to latch intention in mrc request, if it still exist.
  if (world_model_.should_planner_respond_mrc() &&
      world_model_.mrc_request_ptr() != nullptr &&
      (world_model_.mrc_request_ptr()->mrm_type() == mrc::pb::MRM_INLANE ||
       world_model_.mrc_request_ptr()->mrm_type() == mrc::pb::MRM_URGENCY)) {
    const CostComputerFunctionPtr mrc_latch_cost_func_ptr =
        [&](const std::vector<TrajectoryMetaData*>& candidates) {
          const auto previous_maneuver_seed =
              Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
                  SpeedLastFrame());
          return ComputeMrcLatchCost(*previous_maneuver_seed, candidates);
        };
    registry.RegisterCost(CostType::MRC_LATCH, weight_config.mrc_latch(),
                          mrc_latch_cost_func_ptr);
  }
}

void TrajectorySelector::ComputeAndPopulateOverallCost(
    const std::vector<TrajectoryMetaData*>& trajectory_candidates,
    std::string* /* debug_info */,
    planner::pb::TrajectorySelectionDebug* selection_debug_ptr) {
  std::vector<AgentInterpolateInfo> agents_info{};
  std::vector<TrajectoryPoseInterpolator> agents_trajectory{};
  ComputeAndPopulateSharedInfo(trajectory_candidates, agents_info,
                               agents_trajectory);

  RegisterCosts(scenario_selector_ptr_, agents_info, agents_trajectory,
                *DCHECK_NOTNULL(risk_eval_context_));
  CostRegistry& registry = CostRegistry::GetInstance();
  registry.ComputeOverallCostAndPopulateDebug(trajectory_candidates,
                                              selection_debug_ptr);

  for (TrajectoryMetaData* candidate : trajectory_candidates) {
    if (enable_speed_diversity_) {
      candidate->normalized_speed_discomfort_cost *=
          kSpeedDiversitySpeedDiscomfortFactor;
    }

    if (enable_multi_lf_selection_) {
      candidate->collision_risk *= kMultiLKSelectWeightAdjustRisk;
    }

    if (candidate->trajectory_info != nullptr &&
        candidate->trajectory_info->is_for_ra_unstuck_request()) {
      candidate->overall_cost -= kRAUnstuckAdjustReward;
    }
  }
}

void TrajectorySelector::ComputeAndPopulateSharedInfo(
    const std::vector<TrajectoryMetaData*>& candidates,
    std::vector<AgentInterpolateInfo>& agents_info,
    std::vector<TrajectoryPoseInterpolator>& agents_trajectory) {
  selectnet_cost_calculator_.ExtractPlannedTrajectoryFeature(
      candidates, agents_info, agents_trajectory);
  const auto prev_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          SpeedLastFinishedFrame());
  ComputeRawPathDiff(candidates, trajectory_selection_config_,
                     enable_multi_lf_selection_);
  for (TrajectoryMetaData* candidate : candidates) {
    const auto last_selection_seed =
        Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());
    // Calculate stuck signal for each candidate.
    candidate->stuck_signal_meta = stuck_detector_.Detect(
        world_model_, *candidate, lane_sequence_candidates_,
        speed_generator_config_,
        prev_maneuver_seed->agent_map_element_occupancy_seeds(),
        last_selection_seed->stuck_detector_seed());
  }

  risk_eval_context_ = ExtractRiskEvalContext(
      *prev_maneuver_seed, scenario_type_, world_model_,
      object_required_lat_gap_map_, path_risky_agents_, candidates);
}

void TrajectorySelector::UpdateLaneSequenceSelectedInfo(
    const TrajectoryMetaData* selected_candidate) {
  auto mutable_selection_seed =
      Seed::Access<token::SelectionSeed>::MutableMsg(SpeedCurrentFrame());
  auto* scenario_info =
      mutable_selection_seed->mutable_selection_scenario_info()
          ->mutable_lane_selection_info();

  // update selected fork sequence info
  if (selected_candidate == nullptr) {
    scenario_info->clear_fork_lane_id_selected();
    scenario_info->clear_last_sequence_selection_start_timestamp();
    return;
  }

  for (const auto* lane :
       selected_candidate->lane_sequence_result().untrimmed_lane_sequence) {
    // update sequence selection info if selected fork changed
    if (lane->IsForkLane() &&
        std::find(scenario_info->fork_lane_ids().begin(),
                  scenario_info->fork_lane_ids().end(),
                  lane->id()) != scenario_info->fork_lane_ids().end()) {
      if (scenario_info->fork_lane_id_selected() != lane->id()) {
        scenario_info->set_fork_lane_id_selected(lane->id());
        scenario_info->set_last_sequence_selection_start_timestamp(
            world_model_.snapshot_timestamp());
      }
      break;
    }
  }
}

// Extract all agent predictions that are in WorldModel's object_prediction_map
// and considered by at least one of trajectory candidate's constraint.
std::unique_ptr<RiskEvalContext> TrajectorySelector::ExtractRiskEvalContext(
    const planner::pb::DecoupledManeuverSeed& seed,
    planner::pb::SelectionScenarioType scenario_type,
    const SpeedWorldModel& world_model,
    const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
        object_required_lat_gap_map,
    const std::unordered_set<ObjectId>& path_risky_agents,
    const std::vector<TrajectoryMetaData*>& candidates) {
  const lane_selection::LaneSequenceGeometry* lf_lane_seq_geometry = nullptr;
  for (TrajectoryMetaData* candidate : candidates) {
    if (candidate->lane_sequence_type() ==
        planner::pb::LaneSequenceCandidate::LANE_FOLLOW) {
      lf_lane_seq_geometry = &(candidate->lane_sequence_geometry());
    }
  }
  std::unique_ptr<RiskEvalContext> risk_eval_context =
      std::make_unique<RiskEvalContext>(
          seed, scenario_type, world_model.robot_state(),
          world_model.planner_object_map(), path_risky_agents,
          world_model.object_prediction_map(), object_required_lat_gap_map,
          lf_lane_seq_geometry,
          FLAGS_planning_enable_selection_multi_bp_risk &&
              !IsLaneChangeScenario(scenario_type));

  absl::flat_hash_map<
      ObjectId,
      absl::flat_hash_map<std::string, const PredictedTrajectoryWrapper*>>
      object_to_bps_map{};
  for (auto& [obj_id, predictions] : world_model.object_prediction_map()) {
    for (const PredictedTrajectoryWrapper& prediction : predictions) {
      object_to_bps_map[obj_id][GetBpUniqueId(obj_id, prediction.id())] =
          &prediction;
    }
  }

  for (const TrajectoryMetaData* candidate : candidates) {
    for (const speed::Constraint& constraint :
         DCHECK_NOTNULL(candidate)->speed_result.constraints()) {
      const bool is_constraint_from_obj_pred_map =
          object_to_bps_map.contains(constraint.obj_id) &&
          object_to_bps_map[constraint.obj_id].contains(
              constraint.unique_pred_traj_id);
      if (!is_constraint_from_obj_pred_map) continue;

      PredictionInfo& prediction_info =
          risk_eval_context
              ->considered_bps_for_agents[constraint.obj_id]
                                         [constraint.unique_pred_traj_id];
      prediction_info.prediction =
          object_to_bps_map[constraint.obj_id][constraint.unique_pred_traj_id];
      prediction_info.candidates_constrained.insert(candidate->unique_id);
      if (constraint.type ==
              speed::ConstraintType::Constraint_Type_SPEED_OBJECT ||
          constraint.type == speed::ConstraintType::Constraint_Type_GAP_ALIGN) {
        prediction_info.candidates_regularly_constrained.insert(
            candidate->unique_id);
      }
      // TODO(jieruan): should we check different AR BP for one candidate?
      if (constraint.adjusted_prediction.has_value()) {
        prediction_info.candidate_ar_bp_map[candidate->unique_id] =
            &(*constraint.adjusted_prediction);
      }
      CHECK(candidate->object_overlap_region_map != nullptr);
      planner::speed::reasoning_util::PredictionTruncationInfo truncation_info =
          planner::speed::reasoning_util::GetPredictionTruncationInfo(
              constraint, *candidate->object_overlap_region_map);
      if (truncation_info.is_truncated) {
        math::UpdateMax(
            truncation_info.last_considered_relative_time,
            prediction_info
                .speed_check_horizon_map[&candidate->lane_sequence_result()]);
      }
      prediction_info.has_speed_cautious =
          prediction_info.has_speed_cautious ||
          (constraint.generating_reasoner_id ==
           speed::pb::ReasonerId_Name(speed::pb::ReasonerId::CAUTIOUS_DRIVING));
    }
  }
  for (auto& [obj_id, predictions] : world_model.object_prediction_map()) {
    if (object_to_bps_map.contains(obj_id)) continue;

    for (const PredictedTrajectoryWrapper& prediction : predictions) {
      risk_eval_context
          ->considered_bps_for_agents[obj_id]
                                     [GetBpUniqueId(obj_id, prediction.id())]
          .prediction = &prediction;
    }
  }
  return risk_eval_context;
}

// Computes cost related to all types of risk, including collision, short
// range of view, etc.
std::vector<double> TrajectorySelector::ComputeAndPopulateRiskCost(
    const RiskEvalContext& risk_eval_context,
    const std::vector<TrajectoryMetaData*>& candidates) {
  TRACE_EVENT_SCOPE(planner, DecoupledSelection_Risk);
  planner::pb::LaneSequenceCandidate::LaneSequenceType last_selected_lane_type =
      planner::pb::LaneSequenceCandidate::UNKNOWN;
  const auto& decision_history =
      risk_eval_context.seed.selection_seed().decision_history();
  if (decision_history.size() > 0) {
    const auto& last_frame_candidates =
        decision_history.Get(decision_history.size() - 1).candidates();
    const auto last_selected_cand =
        std::find_if(last_frame_candidates.begin(), last_frame_candidates.end(),
                     [](const auto& cand) { return cand.is_selected(); });
    if (last_selected_cand != last_frame_candidates.end()) {
      last_selected_lane_type = last_selected_cand->lane_seq_type();
    }
  }
  for (TrajectoryMetaData* candidate : candidates) {
    const int64_t current_lane_id =
        DCHECK_NOTNULL(candidate->current_lane())->id();
    const bool current_lane_not_in_other_candidates_lane_seq = std::any_of(
        candidates.begin(), candidates.end(), [=](const auto* cand) {
          return std::none_of(
              cand->lane_sequence().begin(), cand->lane_sequence().end(),
              [=](const auto* lane) { return lane->id() == current_lane_id; });
        });
    const bool no_corresponding_speed_diversity = std::none_of(
        candidates.begin(), candidates.end(), [candidate](const auto* cand) {
          return cand->unique_id != candidate->unique_id &&
                 cand->path == candidate->path;
        });
    // TODO(jieruan): use a more robust comparison for double.
    if (candidate->scenario_type ==
            planner::pb::SelectionScenarioType::SCENARIO_FORK_SELECTION &&
        candidate->lane_sequence_type() == last_selected_lane_type &&
        current_lane_not_in_other_candidates_lane_seq &&
        no_corresponding_speed_diversity) {
      candidate->consider_speed_change = true;
    }
  }

  auto compute_candidate_risk = [&risk_eval_context, &candidates,
                                 th_owner = ThreadOwnerMarker::CurrentOwner()](
                                    int candidate_idx) {
    PLANNER_MARK_THREAD_OWNER(th_owner);
    TrajectoryMetaData& candidate = *DCHECK_NOTNULL(candidates[candidate_idx]);
    RiskEvalTarget risk_eval_target = candidate.ToRiskEvalTarget();
    if (!IsValidRiskEvalTarget(risk_eval_target)) return;

    RiskConstraintSet risk_constraint_set =
        ReasonRiskConstraints(risk_eval_context, risk_eval_target);
    candidate.risk_eval_result =
        SolveRisk(risk_eval_context, risk_constraint_set, risk_eval_target);
    candidate.collision_risk = candidate.risk_eval_result.collision_risk;
    candidate.lane_change_selection_info.lc_risk_info.lc_risk =
        ComputeLaneChangeRisk(
            risk_eval_context.robot_state, risk_eval_context.seed.speed_seed(),
            risk_eval_context.seed.selected_behavior_type(),
            risk_eval_context.planner_object_map,
            risk_eval_context.object_prediction_map,
            risk_eval_context.lane_keep_lane_seq_geometry, &candidate);
  };
  tbb::parallel_for(
      /*first=*/0, /*last=*/static_cast<int>(candidates.size()),
      compute_candidate_risk);

  double max_allowed_risk = std::numeric_limits<double>::max();
  for (const TrajectoryMetaData* candidate : candidates) {
    math::UpdateMin(candidate->collision_risk + kMaxCollisionRiskDiff,
                    max_allowed_risk);
  }

  const bool should_use_generic_risk_for_lc =
      risk_eval_context.seed.selected_behavior_type() ==
          planner::pb::BehaviorType::CROSS_LANE &&
      std::any_of(candidates.begin(), candidates.end(),
                  [](const TrajectoryMetaData* candidate) {
                    return candidate->IsCrossLane() &&
                           candidate->is_risky_lane_change;
                  });

  for (TrajectoryMetaData* candidate : candidates) {
    math::UpdateMin(max_allowed_risk, candidate->collision_risk);
    candidate->pose_collision_risk = candidate->collision_risk;
    if (!should_use_generic_risk_for_lc) {
      candidate->collision_risk =
          std::max(candidate->collision_risk,
                   candidate->lane_change_selection_info.lc_risk_info.lc_risk);
    } else {
      candidate->collision_risk = candidate->pose_collision_risk;
    }
  }

  std::vector<double> cost(candidates.size(), 0.0);
  for (size_t i = 0; i < candidates.size(); ++i) {
    cost[i] = candidates[i]->collision_risk;
  }
  return cost;
}

void TrajectorySelector::RecordRtEvents(
    const TrajectoryMetaData* selected_trajectory,
    const std::vector<TrajectoryMetaData*>& feasible_trajectory_candidates)
    const {
  if (selected_trajectory != nullptr &&
      selected_trajectory->trajectory_type() ==
          planner::pb::TrajectoryType::ML_TRAJECTORY) {
    rt_event::PostRtEvent<rt_event::planner::SelectionSelectedMLTrajectory>("");
  }
  if (selected_trajectory != nullptr &&
      selected_trajectory->intent_result().is_emergency_swerve()) {
    rt_event::PostRtEvent<rt_event::planner::SelectedEmergencySwerveTrajectory>(
        "");
  }
  if (selected_trajectory != nullptr && scenario_selector_ptr_ != nullptr) {
    RecordForkLaneFlickerCostRTEvent(feasible_trajectory_candidates,
                                     *selected_trajectory,
                                     scenario_selector_ptr_->get_cost_weights(),
                                     trajectory_selection_config_);
    RecordHumanLikenessCostResultInDifferentChoiceRTEvent(
        feasible_trajectory_candidates, trajectory_selection_config_,
        scenario_selector_ptr_->get_cost_weights().human_likeness_cost());
    // TODO(fengshi): for rt_event analysis. Delete these code after the study
    // is done
    if (FLAGS_planning_enable_lane_selection_rt_event) {
      RecordForkLaneRoutingCostRTEvent(
          feasible_trajectory_candidates, trajectory_selection_config_,
          enable_multi_lf_selection_,
          scenario_selector_ptr_->get_cost_weights().routing());
    }
    RecordPathDiffV2CostRTEvent(
        feasible_trajectory_candidates, trajectory_selection_config_,
        scenario_selector_ptr_->get_cost_weights().lateral_diff(),
        scenario_selector_ptr_->get_cost_weights().path_diff());
  }
  if (FLAGS_planning_enable_selection_human_likeness_compare_mode) {
    RecordHumanLikenessCompareModeRTEvent(
        feasible_trajectory_candidates, trajectory_selection_config_,
        scenario_selector_ptr_->get_cost_weights().human_likeness_cost());
  }
  RecordHighRiskOverriddenByOtherCostRTEvent(selected_trajectory,
                                             feasible_trajectory_candidates);
}

void TrajectorySelector::FillDebugProto(
    const std::string& debug_info,
    const TrajectoryMetaData* selected_trajectory,
    planner::pb::TrajectorySelectionDebug* debug) const {
  if (debug == nullptr) {
    return;
  }
  const auto prev_selection_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());
  const auto prev_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedLastFrame());

  debug->set_debug_info(debug_info + debug_info_);
  debug->set_last_maneuver_type(LastManeuverSeed());
  debug->set_last_intent_homotopy(prev_maneuver_seed->last_intent().homotopy());
  debug->set_last_consecutive_lc_ready_cycles(
      prev_selection_seed->cross_lane_selection_seed()
          .consecutive_cl_ready_cycles());
  debug->mutable_trajectory_meta_debugs()->Reserve(unique_id_to_traj_.size());
  if (selected_trajectory != nullptr &&
      selected_trajectory->stuck_signal_meta.optional_stuck_debug.has_value()) {
    *debug->mutable_stuck_debug() =
        selected_trajectory->stuck_signal_meta.optional_stuck_debug.value();
  }

  for (const auto& [trajectory_unique_id, candidate] : unique_id_to_traj_) {
    candidate->ToDebugProto(debug->add_trajectory_meta_debugs());
    if (candidate->is_selected) {
      // TODO(wutulin): Using unique_id matching is a short term fix. Once
      // ComputeAndPopulateOverallCost is fully migrated inside CostRegister and
      // if we follow the lowest cost principle. This label can be set inside
      // the CostRegister.
      AddIsSelectedToCostDebugTable(trajectory_unique_id,
                                    debug->mutable_cost_debug_table());
      debug->set_if_using_trajectory_guider_output_in_path(
          candidate->if_use_trajectory_guider_output_in_path);
      const auto& lane_sequence = candidate->lane_sequence();
      debug->mutable_selected_lane_sequence()->Reserve(lane_sequence.size());
      for (const auto& lane_ptr : lane_sequence) {
        debug->add_selected_lane_sequence(lane_ptr->id());
      }
    }
  }
  // Generate training samples for ML selection model.
  if (FLAGS_planning_enable_selectnet_sample_generation) {
    DumpFeatureInOfflineSimulator(debug);
  }

  if (selected_trajectory && FLAGS_planning_save_risk_model_framewise_io) {
    DumpFramewiseRiskIOInOfflineSimulator(*selected_trajectory, debug);
  }
}

inline planner::pb::ManeuverType TrajectorySelector::LastManeuverSeed() const {
  return Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame())
      ->last_selected_maneuver_type();
}

void TrajectorySelector::UpdateEgoOffroadInfoSeed(
    const TrajectoryMetaData* selected_trajectory,
    planner::pb::SelectionSeed* selection_seed) {
  if (selected_trajectory == nullptr) {
    return;
  }
  bool is_selected_path_off_road = false;
  if (selected_trajectory->path != nullptr) {
    if (world_model_.pnc_map_service() != nullptr &&
        world_model_.pnc_map_service()->hdmap() != nullptr) {
      is_selected_path_off_road = std::any_of(
          selected_trajectory->path->poses().begin(),
          selected_trajectory->path->poses().end(),
          [this](const planner::pb::PathPose& pose) {
            const double ego_width = world_model_.robot_state().GetWidth();
            const double ego_length = world_model_.robot_state().GetLength();
            const double heading = pose.heading();
            const math::geometry::Point2d car_center = {pose.x_pos(),
                                                        pose.y_pos()};
            math::geometry::OrientedBox ego_bbox(car_center, ego_length,
                                                 ego_width, heading);
            return std::any_of(
                ego_bbox.CornerPoints().begin(), ego_bbox.CornerPoints().end(),
                [this](const math::geometry::Point2d& point) {
                  const auto& roads =
                      world_model_.pnc_map_service()->hdmap()->GetRoads(
                          perception::map_util::MakeHdmapPoint(point.x(),
                                                               point.y()),
                          /*radius=*/0.1);
                  return roads.empty();
                });
          });
    }
  }
  const auto prev_selection_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());
  selection_seed->mutable_ego_offroad_info_seed()->CopyFrom(
      prev_selection_seed->ego_offroad_info_seed());
  selection_seed->mutable_ego_offroad_info_seed()
      ->set_is_selected_path_off_road(is_selected_path_off_road);
  if (is_selected_path_off_road) {
    selection_seed->mutable_ego_offroad_info_seed()->set_off_road_timestamp(
        world_model_.snapshot_timestamp());
  }
}

void TrajectorySelector::UpdateSeed(
    const TrajectoryMetaData* selected_trajectory,
    const std::vector<TrajectoryMetaData*>& feasible_trajectory_candidates) {
  UpdateAutoTriggerReverseInfo(selected_trajectory);

  const auto prev_selection_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());
  auto selection_seed =
      Seed::Access<token::SelectionSeed>::MutableMsg(SpeedCurrentFrame());
  scenario_selector_ptr_->UpdateSeed(unique_id_to_traj_, *prev_selection_seed,
                                     selection_seed.get());
  DCHECK_NE(selection_seed, nullptr);
  if (selected_trajectory != nullptr &&
      selected_trajectory->IsLastTrajectory()) {
    // Update last path seed.
    selection_seed->set_last_path_cost(selected_trajectory->last_path_cost);
  } else {
    // Clear last path seed.
    selection_seed->set_last_path_cost(0.0);
  }
  if (FLAGS_planning_enable_offroad_objects_filtering) {
    UpdateEgoOffroadInfoSeed(selected_trajectory, selection_seed.get());
  }

  UpdateCrossLaneSeed(unique_id_to_traj_, selected_trajectory,
                      *prev_selection_seed, enable_lane_change_scenario_,
                      selection_seed.get());

  auto* decision_snapshot = selection_seed->add_decision_history();
  for (const auto& kv : unique_id_to_traj_) {
    const TrajectoryMetaData& candidate = *kv.second;
    candidate.ToCandidateInfoProto(decision_snapshot->add_candidates());
  }
  auto* decision_history = selection_seed->mutable_decision_history();
  if (decision_history->size() > kMaxDecisionHistorySize) {
    DCHECK_EQ(decision_history->size(), kMaxDecisionHistorySize + 1);
    decision_history->erase(decision_history->begin());
  }

  if (selected_trajectory != nullptr) {
    *selection_seed->mutable_stuck_signal() =
        selected_trajectory->stuck_signal_meta.stuck_signal;
  }

  // Update the latched positive stuck signal.
  if (selection_seed->stuck_signal().reason() !=
      planner::pb::StuckSignal_Reason_kNoStuck) {
    // If we got valid positive stuck signal, set it to latched positive stuck
    // signal in seed.
    selection_seed->mutable_latched_last_positive_stuck_signal()->CopyFrom(
        selection_seed->stuck_signal());
  } else {
    // If we don't get valid positive stuck signal, we need to check the
    // previous latched positive stuck signal to see if we still want to latch
    // it or not.
    const auto& previous_latched_stuck_signal =
        prev_selection_seed->latched_last_positive_stuck_signal();
    if (previous_latched_stuck_signal.reason() !=
            planner::pb::StuckSignal_Reason_kNoStuck &&
        world_model_.snapshot_timestamp() -
                previous_latched_stuck_signal.last_stuck_timestamp() <
            kLatchDurationOfPositiveStuckSignalInMsec) {
      selection_seed->mutable_latched_last_positive_stuck_signal()->CopyFrom(
          previous_latched_stuck_signal);
    } else {
      selection_seed->mutable_latched_last_positive_stuck_signal()->Clear();
    }
  }

  // fill selection scenario info
  constexpr double kUnstuckPotentialValidMinThresh = 0.2;
  auto* scenario_info = selection_seed->mutable_selection_scenario_info();
  scenario_info->mutable_unstuck_scenario_info()->set_stuck_potential_detected(
      std::any_of(unique_id_to_traj_.begin(), unique_id_to_traj_.end(),
                  [=](const auto& kv) {
                    const TrajectoryMetaData& candidate = *kv.second;
                    return candidate.stuck_potential >
                           kUnstuckPotentialValidMinThresh;
                  }));
  // fill multi sequence info
  if (enable_multi_lf_selection_) {
    UpdateLaneSequenceSelectedInfo(selected_trajectory);
  }

  // Update Risk Signal.
  *(selection_seed->mutable_risk_signal()) = risk_signal_;

  // Update selected key object id.
  if (selected_trajectory) {
    auto* key_object = selection_seed->add_key_object_history();
    key_object->set_object_id(
        GetYieldDominantConstraintObjectId(*selected_trajectory));
    const int64_t plan_init_time =
        selected_trajectory->trajectory.plan_init_timestamp();
    constexpr int kMaxObjectAgeInMs = 5000;
    key_object->set_timestamp(plan_init_time);
    for (auto it = selection_seed->mutable_key_object_history()->begin();
         it != selection_seed->mutable_key_object_history()->end(); it++) {
      if (it->timestamp() + kMaxObjectAgeInMs < plan_init_time) {
        selection_seed->mutable_key_object_history()->erase(it);
      }
    }

    // Update stuck detector seed based on selected trajectory's stuck signal.
    PopulateStuckSeed(
        selected_trajectory->stuck_signal_meta,
        world_model_.snapshot_timestamp(),
        *DCHECK_NOTNULL(selection_seed->mutable_stuck_detector_seed()));
  }

  // Populate each trajectory candidate and its stuck signal into seed.
  UpdateTrajStuckMetaDataInSeed(feasible_trajectory_candidates,
                                selection_seed.get());

  // Reset seed for pull over consecutive-cycle safety check.
  // TODO(liwen, zixuan): Fix the situation of intermediate pull over
  // situations, where the pull over is not triggered but we may also need pull
  // over gap align.
  if (!enable_pull_over_scenario_) {
    planner::pb::PullOverSelectionSeed* mutable_pull_over_selection_seed =
        selection_seed->mutable_pull_over_selection_seed();
    mutable_pull_over_selection_seed->set_has_encountered_safety_risk(false);
    mutable_pull_over_selection_seed->set_consecutive_safe_pull_over_cycle_cnt(
        0);
    mutable_pull_over_selection_seed
        ->set_consecutive_pull_over_gap_align_successful_cycle_cnt(0);
  }

  if (!enable_pull_out_scenario_) {
    planner::pb::PullOutSelectionSeed* mutable_pull_out_selection_seed =
        selection_seed->mutable_pull_out_selection_seed();
    mutable_pull_out_selection_seed->set_is_target_lane_stably_blocked(true);
    mutable_pull_out_selection_seed->set_target_lane_block_state_counter(0);
    mutable_pull_out_selection_seed->clear_has_pull_out_jump_out_candidate();
  }
  CostRegistry& registry = CostRegistry::GetInstance();
  registry.Reset();
}

void TrajectorySelector::ComputeRiskSignal(
    const TrajectoryMetaData& selected_trajectory,
    const RiskEvalTarget& risk_eval_target,
    planner::pb::TrajectorySelectionDebug* debug) {
  const bool in_real_manual_mode =
      !av_comm::InSimulation() &&
      !av_comm::IsAutonomyControlMode(
          Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame())
              ->last_vehicle_mode());
  if (in_real_manual_mode) {
    return;
  }
  TRACE_EVENT_SCOPE(planner, DecoupledSelection_RiskSignal);
  constexpr double kEgoSlowMovingSpeed = 3.0;  // in m/s
  const double ego_speed =
      world_model_.robot_state().plan_init_state_snapshot().speed();
  // No risk signal in high densisty or ego is slow moving to reduce
  // system latency.
  if (world_model_.IsHighAgentDensityScenario() ||
      ego_speed < kEgoSlowMovingSpeed) {
    return;
  }
  if (selected_trajectory.trajectory.poses_size() < 2) {
    return;
  }

  const auto prev_selection_seed =
      Seed::Access<token::SelectionSeed>::GetMsg(SpeedLastFrame());

  // TODO(jieruan): use risk cost as risk signal after risk cost is unified.
  const SpecializedRiskEvalResult risk_eval_result =
      ComputeSpecializedCollisionRisk(risk_eval_target,
                                      world_model_.object_prediction_map(),
                                      object_required_lat_gap_map_);
  risk_signal_ = ToRiskSignal(
      risk_eval_target, risk_eval_result, world_model_.object_prediction_map(),
      selected_trajectory.trajectory.plan_init_timestamp());
  if (risk_signal_.risk_agents_size() > 0) {
    // Fire Risk Agent Signal.
    rt_event::PostRtEvent<rt_event::planner::RiskSignalAgent>();
    debug->set_if_fire_risk_signal(true);
  } else {
    // If no risk is found in this cycle, but was found in the previous cycles,
    // Keep the risk signal for a while to reduce flickering.
    if (is_risk_signal_detected_) {
      risk_signal_ = prev_selection_seed->risk_signal();
    }
  }

  // Fire Hard Boundary Risk Signal.
  if (selected_trajectory.geometry_hard_boundary_dist < 1e-3) {
    std::string info = absl::StrFormat(
        "%.3f", selected_trajectory.geometry_hard_boundary_dist);
    rt_event::PostRtEvent<rt_event::planner::RiskSignalHardBoundary>(info);
    debug->set_if_fire_risk_signal(true);
  }

  // Fire Speed Diversity Event.
  if (selected_trajectory.speed_ix > 0) {
    rt_event::PostRtEvent<rt_event::planner::SpeedExtraDiversity>();
  }

  // Fire localization drift and curb collision.
  if (IsLocalizationDrift(world_model_)) {
    rt_event::PostRtEvent<rt_event::planner::RiskSignalDrift>();
    if (IsCurbCollision(world_model_, selected_trajectory.trajectory)) {
      debug->set_if_fire_risk_signal(true);
      rt_event::PostRtEvent<rt_event::planner::RiskSignalDriftCurbCollision>();
    }
  }

  // Fire perception FN signal if key object disappears.
  const auto object_history = prev_selection_seed->key_object_history();
  std::vector<int64_t> key_object_ids;
  for (const auto& key_object : object_history) {
    if (std::find(key_object_ids.begin(), key_object_ids.end(),
                  key_object.object_id()) == key_object_ids.end()) {
      key_object_ids.push_back(key_object.object_id());
    }
  }
  if (IfKeyObjectDisappear(world_model_, selected_trajectory.extended_path,
                           key_object_ids)) {
    debug->set_if_fire_risk_signal(true);
    rt_event::PostRtEvent<rt_event::planner::RiskSignalLeadingObjectMissing>();
  }
}

// Dump data-driven feature to planning_debug in offline simulation.
// Note: there may be performance issues but will not be fixed.
void TrajectorySelector::DumpFeatureInOfflineSimulator(
    planner::pb::TrajectorySelectionDebug* debug) const {
  SelectnetSampleGenerator sample_generator(trajectory_selection_config_);
  std::vector<TrajectoryMetaData*> trajectory_candidates{};
  for (const auto& [unique_id, trajectory_meta] : unique_id_to_traj_) {
    trajectory_candidates.push_back(trajectory_meta);
  }
  sample_generator.GenerateSample(world_model_.robot_state(),
                                  world_model_.agent_list(),
                                  trajectory_candidates, debug);
  // TODO(selection): current logic'd guarantee proto consistency automatically,
  // check if further onboard-offboard consistency check is needed.
}

// Dump framewise io data for risk model to debug in offline simulation.
void TrajectorySelector::DumpFramewiseRiskIOInOfflineSimulator(
    const TrajectoryMetaData& selected_trajectory,
    planner::pb::TrajectorySelectionDebug* debug) const {
  if (debug == nullptr) return;
  int64_t plan_init_timestamp =
      selected_trajectory.trajectory.plan_init_timestamp();
  // Margin in milliseconds around the target timestamp within which the
  // plan_init_timestamp should fall to save the risk model framewise I/O.
  constexpr int64_t kTimestampSaveMarginInMSec = 150;
  if (std::abs(plan_init_timestamp -
               FLAGS_planning_selection_framewise_timestamp_to_save) >
      kTimestampSaveMarginInMSec)
    return;
  std::vector<TrajectoryMetaData*> candidates{};
  for (const auto& kv : unique_id_to_traj_) {
    TrajectoryMetaData* candidate = kv.second;
    if (candidate->is_safe) {
      candidates.push_back(candidate);
    }
  }
  const auto prev_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(
          SpeedLastFinishedFrame());
  google::protobuf::Arena arena;
  pb::RiskModelFramewiseInput* risk_model_framewise_input =
      ConvertCandidatesToRiskInput(
          candidates, *prev_maneuver_seed, enable_unstuck_scenario_,
          object_required_lat_gap_map_, world_model_.object_prediction_map(),
          &arena);
  pb::RiskModelFramewiseOutput risk_model_framewise_output;
  for (const auto& trajectory_candidate : candidates) {
    pb::RiskModelFramewiseOutput::TrajectoryCandidateRisk candidate_risk_info;
    candidate_risk_info.set_trajectory_type_unique_name(
        trajectory_candidate->unique_id);
    candidate_risk_info.set_collision_risk(
        trajectory_candidate->collision_risk);
    *risk_model_framewise_output.add_candidates() = candidate_risk_info;
  }
  debug->mutable_risk_model_framewise_input()->CopyFrom(
      *risk_model_framewise_input);
  debug->mutable_risk_model_framewise_output()->CopyFrom(
      risk_model_framewise_output);
}

}  // namespace selection
}  // namespace planner
