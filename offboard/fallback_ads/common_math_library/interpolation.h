#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_INTERPOLATION_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_INTERPOLATION_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

typedef struct {
  float x;
  float y;
} CML_Table1D;

extern float CML_Interpolation1D(float x, const CML_Table1D table[],
                                 uint8_t table_rows);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_INTERPOLATION_H_
