
#include "vehicle_signal_input/can_timeout_detection/can_timeout_detection.h"

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/assert_cfg.h"
#include "common_math_library/common_method.h"
#include "common_math_library/debounce_method.h"
#include "vehicle_signal_input/vehicle_signal_input_parameter.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

void CanCounterMapping(const VehicleAndAcuCanSignal *can_signal_ptr,
                       uint8 mcu_can_counter[], int size) {
  // prevent array overflow
  CML_ASSERT(size > 14);

  // vehicle mid3 ssm to mcu counter0
  mcu_can_counter[0] = can_signal_ptr->PrimVehSpdGroupSafeCntr;

  // vehicle mid3 ssm to mcu counter0
  mcu_can_counter[1] = can_signal_ptr->PrimALgtDataRawSafeCntr;

  // vehicle mid3 vcu to mcu counter0
  mcu_can_counter[2] = can_signal_ptr->AgDataRawSafeCntr;

  // vehicle mid3 vcu to mcu counter1
  mcu_can_counter[3] = can_signal_ptr->BrkTqMinReqBrkTqMinReqCntr;

  // vehicle mid5 ssm to mcu counter0
  mcu_can_counter[4] = can_signal_ptr->AdPrimWhlAgEstimdGroupSafeCntr;

  // vehicle mid5 ssm to mcu counter1
  mcu_can_counter[5] = can_signal_ptr->PrimWhlAgSpdFrntSafeCntr;

  // vehicle mid6 ssm to mcu counter0
  mcu_can_counter[6] = can_signal_ptr->BrkDegradedRdntCntr;

  // vehicle mid6 ssm to mcu counter1
  mcu_can_counter[7] = can_signal_ptr->AdSecSteerStsSafeGroupCntr;

  // acu mid3 ssm to mcu counter0
  mcu_can_counter[8] = can_signal_ptr->AdNomALgtReqGroupSafeCntr_A;

  // acu mid3 vim to mcu counter1
  mcu_can_counter[9] = can_signal_ptr->AdPrimALgtLimReqGroupSafeCntr_A;

  // acu mid5 vim to mcu counter0
  mcu_can_counter[10] = can_signal_ptr->AdPrimPoseCntr_A;

  // acu mid5 vim to mcu counter1
  mcu_can_counter[11] = can_signal_ptr->AdPrimWhlAgReqGroupSafeCntr_A;

  // acu mid6 vim to mcu counter0
  mcu_can_counter[12] = can_signal_ptr->AdSecALgtLimReqGroupSafeCntr_A;

  // acu mid6 vim to mcu counter1
  mcu_can_counter[13] = can_signal_ptr->AdSecWhlAgReqGroupSafeCntr_A;

  // acu vcan10 vim to mcu counter0
  mcu_can_counter[14] = can_signal_ptr->AcuFbRollingCounter;
}

void GetMcuCanTimeoutResult(const float can_timeout_timer_array[],
                            const bool can_timeout_enable_array[], int size,
                            VSI_McuCanTimeout_Struct *mcu_can_timeout_ptr) {
  // prevent array overflow
  CML_ASSERT(size > 14);

  // can timeout timer
  mcu_can_timeout_ptr->VSI_VehMid3SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[0]);
  mcu_can_timeout_ptr->VSI_VehMid3SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[1]);
  mcu_can_timeout_ptr->VSI_VehMid3VcuCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[2]);
  mcu_can_timeout_ptr->VSI_VehMid3VcuCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[3]);
  mcu_can_timeout_ptr->VSI_VehMid5SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[4]);
  mcu_can_timeout_ptr->VSI_VehMid5SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[5]);
  mcu_can_timeout_ptr->VSI_VehMid6SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[6]);
  mcu_can_timeout_ptr->VSI_VehMid6SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[7]);
  mcu_can_timeout_ptr->VSI_AcuMid3SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[8]);
  mcu_can_timeout_ptr->VSI_AcuMid3SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[9]);
  mcu_can_timeout_ptr->VSI_AcuMid5SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[10]);
  mcu_can_timeout_ptr->VSI_AcuMid5SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[11]);
  mcu_can_timeout_ptr->VSI_AcuMid6SsmCounter0Timer =
      CML_SecondsToMs(can_timeout_timer_array[12]);
  mcu_can_timeout_ptr->VSI_AcuMid6SsmCounter1Timer =
      CML_SecondsToMs(can_timeout_timer_array[13]);
  mcu_can_timeout_ptr->VSI_AcuFbCanTimer =
      CML_SecondsToMs(can_timeout_timer_array[14]);

  // can timeout enable
  mcu_can_timeout_ptr->VSI_VehMid3SsmCounter0Timeout =
      can_timeout_enable_array[0];
  mcu_can_timeout_ptr->VSI_VehMid3SsmCounter1Timeout =
      can_timeout_enable_array[1];
  mcu_can_timeout_ptr->VSI_VehMid3VcuCounter0Timeout =
      can_timeout_enable_array[2];
  mcu_can_timeout_ptr->VSI_VehMid3VcuCounter1Timeout =
      can_timeout_enable_array[3];
  mcu_can_timeout_ptr->VSI_VehMid5SsmCounter0Timeout =
      can_timeout_enable_array[4];
  mcu_can_timeout_ptr->VSI_VehMid5SsmCounter1Timeout =
      can_timeout_enable_array[5];
  mcu_can_timeout_ptr->VSI_VehMid6SsmCounter0Timeout =
      can_timeout_enable_array[6];
  mcu_can_timeout_ptr->VSI_VehMid6SsmCounter1Timeout =
      can_timeout_enable_array[7];
  mcu_can_timeout_ptr->VSI_AcuMid3SsmCounter0Timeout =
      can_timeout_enable_array[8];
  mcu_can_timeout_ptr->VSI_AcuMid3SsmCounter1Timeout =
      can_timeout_enable_array[9];
  mcu_can_timeout_ptr->VSI_AcuMid5SsmCounter0Timeout =
      can_timeout_enable_array[10];
  mcu_can_timeout_ptr->VSI_AcuMid5SsmCounter1Timeout =
      can_timeout_enable_array[11];
  mcu_can_timeout_ptr->VSI_AcuMid6SsmCounter0Timeout =
      can_timeout_enable_array[12];
  mcu_can_timeout_ptr->VSI_AcuMid6SsmCounter1Timeout =
      can_timeout_enable_array[13];
  mcu_can_timeout_ptr->VSI_AcuFbCanTimeout = can_timeout_enable_array[14];
}

bool CheckMainCanTimeout(uint8_t main_can_counter) {
  static uint8_t last_main_can_counter = 0;
  static float main_can_timeout_timer = 0.0;
  static bool main_can_timeout_enable = false;

  CML_TurnOnDelay((main_can_counter == last_main_can_counter),
                  GetVsiKeyCanTimeoutThreshold(), VSI_TASK_CYCLE_TIME,
                  &main_can_timeout_timer, &main_can_timeout_enable);
  last_main_can_counter = main_can_counter;

  return main_can_timeout_enable;
}

bool CheckRedundantCanTimeout(uint8_t redundant_can_counter) {
  static uint8_t last_redundant_can_counter = 0;
  static float redundant_can_timeout_timer = 0.0;
  static bool redundant_can_timeout_enable = false;
  CML_TurnOnDelay((redundant_can_counter == last_redundant_can_counter),
                  GetVsiKeyCanTimeoutThreshold(), VSI_TASK_CYCLE_TIME,
                  &redundant_can_timeout_timer, &redundant_can_timeout_enable);
  last_redundant_can_counter = redundant_can_counter;

  return redundant_can_timeout_enable;
}

void CheckMcuCanTimeout(const VehicleAndAcuCanSignal *can_signal_ptr,
                        VSI_McuCanTimeout_Struct *mcu_can_timeout_ptr) {
  static uint8_t last_can_counter_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  static float can_timeout_timer_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  static bool can_timeout_enable_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};

  uint8_t can_counter_array[VSI_CAN_TIME_OUT_ARRAY_SIZE] = {0};
  CanCounterMapping(can_signal_ptr, can_counter_array,
                    VSI_CAN_TIME_OUT_ARRAY_SIZE);

  for (int i = 0; i < VSI_CAN_TIME_OUT_ARRAY_SIZE; ++i) {
    CML_TurnOnDelay((can_counter_array[i] == last_can_counter_array[i]),
                    GetVsiCanTimeoutThreshold(), VSI_TASK_CYCLE_TIME,
                    &can_timeout_timer_array[i], &can_timeout_enable_array[i]);
    last_can_counter_array[i] = can_counter_array[i];
  }

  GetMcuCanTimeoutResult(can_timeout_timer_array, can_timeout_enable_array,
                         VSI_CAN_TIME_OUT_ARRAY_SIZE, mcu_can_timeout_ptr);
}
