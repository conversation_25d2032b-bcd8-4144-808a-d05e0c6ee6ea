#ifndef ONBOARD_PLANNER_PLANNING_TOPICS_H_
#define ONBOARD_PLANNER_PLANNING_TOPICS_H_

#include <string>
#include <vector>

// We need a file to track all planning pub/sub topics because:
// 1. A special topic (/planning/metadata) will publish the information on all
// pub/sub topics of planner for alignment purpose in the Web Monitor.
// 2. Reference by strings is more error prone than reference by enums.
namespace planner {

// All planner's pub topics.
// Reference: onboard/planner/vgraph/planner_node.vgraph
enum class PlannerPubTopic {
  // HdmapVNode is skipped because its published topics are not relevant to
  // planner.

  // SnapshotVNode published topics.
  planning_snapshot = 1,  // intra-process
  planning_intra_snapshot_ra,
  planning_snapshot_seed,

  // PlanningPathVNode published topics.
  routing_planning_route_state,
  planning_intra_speed_planner_param,  // intra

  // PlanningSpeedVNode published topics
  planning_ops_warning,
  planning_intra_planning_debug_wrapper,  // intra-process
  planning_seed,
  planner_state,
  planning_planning_lane_sequence,
  planning_customer_monitor_visual,
  planning_pullout_request,
  planning_cloud_cell_request,
  planning_stuck_detection_recall_signal,
  planning_assist_request,
  routing_planner_route_command,
  planning_trajectory,
  planning_selectnet_sample,
  planning_trajectory_result,
  planning_message_metadata,
  planning_message_metadata_input,
  planning_intra_speed_completion_signal,  // intra-process
  planning_empirical_raw_data,
  remote_warning_signal,
  remote_assist_model_input,  // intra-process

  // PublishVNode published topics.
  planning_planning_debug,

  // ExceptionHandlerVNode published topics.
  planning_exception_handler_seed,
  planning_ttc_detect_result,
  exception_handler_trajectory,
  exception_handler_for_perception_debug,

  // ML Planner published topics.
  ml_planner_close_loop_assist_trajectory,

  // RemoteAssistModelVNode published topics.
  remote_assist_model_output,  // intra-process
};

// All planner's sub topics.
enum class PlannerSubTopic {
  // SnapshotVNode subscribed topics.
  prediction_predicted_objects = 1,
  bag_planning_snapshot_seed,
  pose,
  simulation_pose,
  traffic_light_detect,
  gateway,
  simulation_vehicle,
  obstacle_list,
  perception_construction_zone_list,
  routing_route_status,
  planning_assist_response,
  planning_fault_detector_response,
  trajectory_guider_guide_trajectory,
  order_service,
  trip_comment,
  perception_lidar_detected_curb_list,
  health_localization_node,
  planning_mrc_immediate_pullover,
  control_replan_request,
  localization_localization_fault,
  mrc_request,
  hdmap_changed_data,
  planning_remote_speed_limit,
  vehicle_exception_status,
  empirical_publish_data_list,
  global_init_state,
  exception_handler_trajectory,

  // PlanningPathVNode subscribed topics.
  planning_snapshot,
  planning_intra_snapshot_ra,
  bag_planning_seed,
  bag_planner_state,
  map_change_area_list,
  planning_intra_speed_completion_signal,

  // PlanningSpeedVNode subscribed topics.
  planning_intra_speed_planner_param,
  remote_assist_model_output,  // intra-process
  exception_handler_for_perception_debug,
  system_state,
  se_early_warning,
  perception_collision_detection,
  perception_sensor_abnormal_pub,
  perception_camera_abnormal_pub,

  // PublishVNode subscribed topics.
  planning_intra_planning_debug_wrapper,

  // ExceptionHandlerVNode subscribed topics.
  planning_trajectory_result,
  bag_planning_exception_handler_seed,

  // RemoteAssistModelVNode subscribed topics.
  remote_assist_model_input,  // intra-process
  // Other subscribed topics are already subscribed by SnapshotVNode.
};

// Gets the publisher name of a published topic.
std::string GetPublisherName(const PlannerPubTopic& pub_topic);

// Gets the topic name of a published topic.
std::string GetPlannerTopicName(const PlannerPubTopic& pub_topic);

// Gets the topic name of a subscribed topic.
std::string GetPlannerTopicName(const PlannerSubTopic& sub_topic);

// Gets all the published topics of planner.
std::vector<PlannerPubTopic> GetAllPubTopics();

// Gets all the subscribed topics of planner.
std::vector<PlannerSubTopic> GetAllSubTopics();

// Checks if a published topic does not need to be aligned.
bool ShouldSkipAlignment(const PlannerPubTopic& pub_topic);

/// Checks if a subscribed topic does not need to be aligned.
bool ShouldSkipAlignment(const PlannerSubTopic& sub_topic);

}  // namespace planner

#endif  // ONBOARD_PLANNER_PLANNING_TOPICS_H_
