#include "ntb_receiver.h"

#include <cstdint>
#include <fcntl.h>
#include <glog/logging.h>
#include <memory>
#include <string>
#include <utility>

#include "base/now.h"
#include "common.h"
#include "trace/trace.h"
#include "varch/vnode/detail/underlay_message/raw_message.h"
#include "voy_trace/trace_ntb_bridge.h"

namespace ntb_bridge {

using RawMessage = varch::vnode::detail::underlay_message::RawMessage;

NtbReceiver::NtbReceiver(
    const ntb_host_id dest_host_id,
    const std::shared_ptr<PublisherMapType>& topic_id_to_publisher,
    const int num_threads)
    : is_running_(false),
      dest_host_id_(dest_host_id),
      ntb_dev_fd_(-1),
      topic_id_to_publisher_(topic_id_to_publisher),
      num_threads_(num_threads) {}

NtbReceiver::~NtbReceiver() {
  if (is_running_) {
    Stop();
  }
}

bool NtbReceiver::Start() {
  if (is_running_) {
    LOG(INFO) << "NtbReceiver is already running";
    return true;
  }
  if (!OpenNtbDevice()) {
    return false;
  }
  is_running_ = true;
  thread_pool_ = std::make_unique<av_comm::ThreadPool>(num_threads_);
  LOG(INFO) << "Ntb device (host_id: " << dest_host_id_ << ") is opened.";
  StartDispatchThread();
  return true;
}

bool NtbReceiver::Stop() {
  if (!is_running_) {
    LOG(INFO) << "NtbReceiver is not running, host_id: " << dest_host_id_;
    return true;
  }
  is_running_ = false;
  // Clean threads
  if (dispatch_thread_.joinable()) {
    dispatch_thread_.join();
  }
  thread_pool_.reset();

  CloseNtbDevice();
  return true;
}

bool NtbReceiver::OpenNtbDevice() {
  ntb_dev_fd_ = ntb_open(dest_host_id_, O_RDWR);
  if (ntb_dev_fd_ < 0) {
    LOG(ERROR) << "Failed to open ntb device for host " << dest_host_id_ << ".";
    return false;
  }
  return true;
}

bool NtbReceiver::CloseNtbDevice() {
  if (ntb_dev_fd_ >= 0) {
    ntb_close(ntb_dev_fd_);
    ntb_dev_fd_ = -1;
  }
  return true;
}

bool NtbReceiver::StartDispatchThread() {
  dispatch_thread_ = std::thread([this]() {
    TRACE_EVENT_SCOPE(ntb_bridge, ReceiverDispatch, dest_host_id_);
    while (is_running_) {
      int64_t recv_start_time_ns = base::NowNs();
      ntb_msg* packet_msg = ntb_recv(ntb_dev_fd_);
      if (packet_msg == nullptr) {
        TRACE_EVENT_SCOPE(ntb_bridge, ReceiverWaitingForData, dest_host_id_);
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
        LOG(WARNING)
            << "No available ntb packet, sleep for a while, dest_host: "
            << dest_host_id_;
        continue;
      }
      int64_t recv_end_time_ns = base::NowNs();

      thread_pool_->PushTask([this, packet_msg, recv_start_time_ns,
                              recv_end_time_ns] {
        TRACE_EVENT_SCOPE(ntb_bridge, ReceiverPacketUnpack, dest_host_id_,
                          packet_msg->index, packet_msg->size, packet_msg->len);
        if (packet_msg->len < kNtbMsgHeaderSize) {
          LOG(ERROR) << "Invalid message received, length too short. msg_idx: "
                     << packet_msg->index << ", msg_size: " << packet_msg->size
                     << ", msg_len: " << packet_msg->len;
          FreeNtbPacket(packet_msg);
          return;
        }
        int64_t process_start_time_ns = base::NowNs();
        void* packet_buf = packet_msg->buf;
        void* msg_data_buf =
            static_cast<char*>(packet_buf) + sizeof(NtbMsgHeader);
        const auto ntb_msg_header = static_cast<NtbMsgHeader*>(packet_buf);

        int topic_id = ntb_msg_header->topic_id;
        int64_t message_publish_timestamp =
            ntb_msg_header->msg_publish_timestamp;
        int64_t message_send_timestamp = ntb_msg_header->timestamp;
        const std::string& topic_name = NtbTopicIDToTopicName(topic_id);
        int64_t hw_timestamp = ntb_msg_header->hw_timestamp;
        const int32_t data_size = ntb_msg_header->data_size;

        if (ntb_msg_header->data_size <= 0) {
          LOG(ERROR) << "Invalid ntb message received, data_size too small. "
                     << "topic_name: " << topic_name
                     << ", hw_time: " << hw_timestamp
                     << ", data_size: " << data_size;
          FreeNtbPacket(packet_msg);
          return;
        }

        int64_t msg_send_to_recv_use_ns = 0;
        int64_t ntb_recv_use_ns = 0;
        int64_t thread_dispatch_use_ns = 0;
        int64_t msg_build_use_ns = 0;
        int64_t total_use_ns = 0;
        TRACE_EVENT_SCOPE(ntb_bridge, ReceiverBuildAndPub, topic_name,
                          data_size, hw_timestamp, message_publish_timestamp,
                          message_send_timestamp, total_use_ns);
        // Copy message data from ntb buffer
        RawMessage raw_msg;
        constexpr int32_t data_size_len = sizeof(data_size);
        int raw_msg_size = data_size + data_size_len;
        raw_msg.resize(raw_msg_size);
        memcpy(raw_msg.data(), &data_size, data_size_len);
        memcpy(raw_msg.data() + data_size_len, msg_data_buf, data_size);
        FreeNtbPacket(packet_msg);
        int64_t msg_build_fin_time_ns = base::NowNs();

        // Find the publisher by topic_id and publish the message
        const auto publisher_it = topic_id_to_publisher_->find(topic_id);
        if (publisher_it == topic_id_to_publisher_->end()) {
          LOG(ERROR) << "Failed to find publisher for topic "
                     << NtbTopicIDToTopicName(topic_id)
                     << ", dest_host_id: " << dest_host_id_;
          return;
        }
        auto& publisher = publisher_it->second;
        if (message_publish_timestamp != 0) {
          publisher.PublishAsyncWithPublishTimestamp(std::move(raw_msg),
                                                     message_publish_timestamp);
        } else {
          publisher.PublishAsync(std::move(raw_msg));
        }
        int64_t pub_fin_time_ns = base::NowNs();
        msg_send_to_recv_use_ns = recv_end_time_ns - message_send_timestamp;
        ntb_recv_use_ns = recv_end_time_ns - recv_start_time_ns;
        thread_dispatch_use_ns = process_start_time_ns - recv_end_time_ns;
        msg_build_use_ns = msg_build_fin_time_ns - process_start_time_ns;
        total_use_ns = pub_fin_time_ns - message_send_timestamp;
        LOG_EVERY_N(INFO, 10)
            << "Ntb message published, dest_host: " << this->dest_host_id_
            << ", topic_name: " << NtbTopicIDToTopicName(topic_id)
            << ", msg_idx: " << packet_msg->index
            << ", data_size: " << data_size << ", msg_hw_time: " << hw_timestamp
            << ", msg_pub_time: " << message_publish_timestamp
            << ", msg_send_time: " << message_send_timestamp
            << ", msg_send_to_recv_use_ns: " << msg_send_to_recv_use_ns
            << ", ntb_recv_use_ns: " << ntb_recv_use_ns
            << ", thread_dispatch_use_ns: " << thread_dispatch_use_ns
            << ", msg_build_use_ns: " << msg_build_use_ns
            << ", total_use_ns: " << total_use_ns;
      });
      TRACE_EVENT_INSTANT(ntb_bridge, ReceiverTaskPushed);
    }
  });
  return true;
}

bool NtbReceiver::FreeNtbPacket(ntb_msg* packet) const {
  int ret = ntb_rx_mem_free(ntb_dev_fd_, packet);
  if (ret < 0) {
    LOG(ERROR) << "Failed to free rx mem, dest_host: " << this->dest_host_id_
               << ", msg_idx: " << packet->index << ", ret: " << ret;
    return false;
  }
  return true;
}

}  // namespace ntb_bridge
