#include "planner/selection/costs/lateral_diff_cost.h"

#include "av_comm/mode_config.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "planner/planning_gflags.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "voy_protos/math.pb.h"

namespace planner {
namespace selection {

double CalculateCandidateLateralDiff(
    const math::geometry::PolylineCurve2d& candidate_path,
    const math::geometry::PolylineCurve2d& last_path, double ego_arc_length,
    const planner::pb::TrajectorySelectionConfig& config) {
  constexpr double kPathDiffV2MaxLength = 40.0;
  constexpr double kPathDiffV2Resolution = 0.5;

  std::vector<math::geometry::Point2d> points_on_path;
  points_on_path.reserve(static_cast<int>(
      std::floor(kPathDiffV2MaxLength / kPathDiffV2Resolution)));

  const double path_diff_check_horizon = ego_arc_length + kPathDiffV2MaxLength;
  for (double arc_length = ego_arc_length; arc_length < path_diff_check_horizon;
       arc_length += kPathDiffV2Resolution) {
    points_on_path.push_back(candidate_path.GetInterp(
        arc_length, math::pb::UseExtensionFlag::kAllow));
  }
  double decay = 1.0;
  double decay_normalizer = 0.0;
  double lateral_diff = 0.0;
  for (const math::geometry::Point2d& path_point : points_on_path) {
    const auto proximity =
        last_path.GetProximity(path_point, math::pb::UseExtensionFlag::kAllow);
    lateral_diff += std::min(proximity.dist, config.max_pose_diff()) * decay;
    decay_normalizer += decay;
    decay *= config.time_decay_factor();
  }
  lateral_diff = math::Clamp(lateral_diff / decay_normalizer, 0.0, 1.0);
  return lateral_diff;
}

void ComputeBasicLateralDiffCost(
    const std::vector<TrajectoryMetaData*>& candidates,
    const planner::pb::Path& last_selected_path,
    const planner::pb::TrajectorySelectionConfig& config) {
  const auto last_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedLastFrame());
  const math::geometry::PolylineCurve2d& last_path =
      math::geometry::Convert<math::geometry::Point2d>(last_selected_path);
  for (TrajectoryMetaData* candidate : candidates) {
    const math::geometry::PolylineCurve2d& extended_path =
        *DCHECK_NOTNULL(candidate->extended_path);
    const double ego_arc_length =
        candidate->trajectory_info->plan_init_ra_arc_length_on_extended_path();
    candidate->lateral_diff = CalculateCandidateLateralDiff(
        extended_path, last_path, ego_arc_length, config);
  }
}

void ComputeLateralDiffCost(
    const std::vector<TrajectoryMetaData*>& candidates,
    const planner::pb::Path& last_selected_path,
    const planner::pb::TrajectorySelectionConfig& config) {
  const bool last_path_too_short = (last_selected_path.poses_size() < 2);
  const bool in_real_manual_mode =
      !av_comm::InSimulation() &&
      !av_comm::IsAutonomyControlMode(
          Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame())
              ->last_vehicle_mode());
  // Disables flicker cost when transitioning from manual to auto in real
  // driving, to avoid latching to a wrong traj and restart state calculation.
  if (in_real_manual_mode || last_path_too_short) {
    for (TrajectoryMetaData* candidate : candidates) {
      candidate->normalized_lateral_diff = 0.0;
    }
    return;
  }
  ComputeBasicLateralDiffCost(candidates, last_selected_path, config);

  constexpr double kLateralDiffZeroWeightSpeed = 1.0;  // m/s
  constexpr double kLateralDiffFullWeightSpeed = 5.0;  // m/s
  for (TrajectoryMetaData* candidate : candidates) {
    const double weight =
        FLAGS_planning_discount_lateral_diff_cost_at_high_speed
            ? math::GetLinearInterpolatedY(
                  kLateralDiffZeroWeightSpeed, kLateralDiffFullWeightSpeed, 0.0,
                  1.0, candidate->trajectory.poses()[0].speed())
            : 1.0;
    candidate->normalized_lateral_diff = candidate->lateral_diff * weight;
  }
}

}  // namespace selection
}  // namespace planner
