
#include "planner/speed/solver/optimizer/lqr_potentials_generator.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <iomanip>
#include <iostream>
#include <limits>
#include <optional>
#include <ostream>
#include <utility>
#include <vector>

#include <glog/logging.h>
#include <tbb/concurrent_vector.h>
#include <tbb/parallel_for.h>
#include <tbb/tbb.h>

#include "math/eigen_util.h"
#include "math/math_util.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_util.h"
#include "planner/speed/constraint/decision.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/discomforts/dynamic_limits.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/optimizer/lqr_potentials.h"
#include "planner/speed/solver/optimizer/potentials.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/timemap/time_mapper.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {
namespace {
// When ref length lower this value, we shouldn't add attractor.
constexpr double kMinAttractingLength = 0.1;
// When ref speed lower this value, we shouldn't add attractor.
constexpr double kMinAttractingSpeed = 0.1;
// The discount of proximity speed cost gain over states.
constexpr double kProximitySpeedGainDiscount = 0.95;
// The discount of jerk attractor cost gain over states.
constexpr double kLastProfileJerkAttractorGainDiscount = 0.5;
// The maximum discomfort to add jerk attractors for the last profile.
constexpr double kMaxDiscomfortForLastProfileJerkAttraction = 0.5;
// The dx of length attractor and following potentials if fast acceleration is
// enabled.
// TODO(yuwang): Move fast acceleration related params to conf.
// TODO(yuwang): Change dx for all the following attractor to this value.
constexpr double kAttractorDxForFastAcceleration = 3.0;
constexpr double kAttractorDxForFastAccelerationWithXUpperBound = 1.0;
// The magnification of following potentials if fast acceleration is enabled.
constexpr double kMagnificationForFastAcceleration = 4.0;
// TODO(lewisliu): update param conf to explicitly set positive gain and
// negative gain.
// The factor of jerk attractor positive gain  w.r.t. the negative gain.
constexpr double kJerkAttractorPositiveGainFactor = 0.25;
// We only allow static range to be softer when ego initial speed is below this.
constexpr double kInitSpeedThreForSoftenStaticRangeInMps = 4.0;
// The gain of attraction cost for creep scenario.
// TODO(lewisliu): Move to conf when clean up following attractor.
constexpr double kCreepAttractorGain = 500.0;
constexpr double kCreepAttractorDx = 20.0;
// If the search profile's length is less than this threshold, the optimizer
// will not attract to max speed ref profile, instead attract to the lower bound
// of the search profile and static range.
// TODO(lewisliu): Gradually expand the scope of this attraction strategy with
// proper gain.
constexpr double kSearchProfileProgressThresholdForAttraction = 5.0;

// Gets the min profile based on the proximity speed defined min accel and min
// jerk.
Profile GetTargetMinProfile(
    const Profile& min_profile, const Limits& limits, double dt,
    const pb::ProximitySpeedParams& proximity_speed_params) {
  Profile target_min_profile = min_profile;
  const bool is_upper_bound_speed_limit =
      proximity_speed_params.speed_limit_case() ==
          pb::ProximitySpeedParams::SpeedLimitCase::kMaxAllowedSpeed ||
      proximity_speed_params.max_allowed_rel_speed() > 0.0;
  if (is_upper_bound_speed_limit &&
      (proximity_speed_params.min_a() > limits.brake_a.min ||
       proximity_speed_params.min_j() > limits.brake_j.min)) {
    Limits override_limits = limits;
    override_limits.brake_a.min =
        std::max(limits.brake_a.min, proximity_speed_params.min_a());
    override_limits.accel_a.min =
        std::max(limits.accel_a.min, proximity_speed_params.min_a());
    override_limits.brake_j.min =
        std::max(limits.brake_j.min, proximity_speed_params.min_j());
    override_limits.accel_j.min =
        std::max(limits.accel_j.min, proximity_speed_params.min_j());
    GenerateBrakeProfile(/*brake_first_ix=*/0,
                         /*brake_end_ix=*/target_min_profile.size() - 1,
                         override_limits, dt, &target_min_profile);
  }
  return target_min_profile;
}

// Generates the initial profile bases on the yield_min_v/a/j setting.
// TODO(speed): refactor to use the util functions in IDP.
Profile GenerateInitialProfile(const ConstraintSettings& settings,
                               const Profile& ref_profile, const Limits& limits,
                               double dt, int steps) {
  // Generate creep profile.
  const Profile& sampled_ref_profile =
      ResampleProfile(ref_profile, limits, dt, steps + 2);
  Profile initial_profile(1, sampled_ref_profile.front());

  DCHECK(!settings.yield_min_a.VaryWithDiscomfort());
  DCHECK(!settings.yield_min_v.VaryWithDiscomfort());
  DCHECK(!settings.yield_min_j.VaryWithDiscomfort());
  const double yield_min_a = settings.yield_min_a(0.0);
  const double yield_min_v = settings.yield_min_v(0.0);
  const double yield_min_j = settings.yield_min_j(0.0);
  Limits creep_limits = limits;
  creep_limits.brake_a.min = yield_min_a;
  creep_limits.accel_a.min = yield_min_a;
  if (!std::isinf(yield_min_j)) {
    // If yield min j equals inf, it means that users wish to use the default
    // yield min j value at the current discomfort.
    creep_limits.brake_j.min = yield_min_j;
    creep_limits.accel_j.min = yield_min_j;
  }
  GenerateProfileToExpectedSpeed(sampled_ref_profile, /*modified_first_ix=*/0,
                                 /*modified_end_ix=*/steps - 1, creep_limits,
                                 dt, yield_min_v, &initial_profile);
  return initial_profile;
}
}  // namespace

LqrPotentialsGenerator::LqrPotentialsGenerator(
    const SpeedOptInput& opt_input,
    speed::pb::SpeedOptPotentialDebug* potential_debug)
    : opt_conf_(opt_input.opt_conf),
      profiles_(opt_input.profiles),
      tree_search_result_(opt_input.tree_search_result),
      gap_align_info_map_(opt_input.gap_align_info_map),
      opt_settings_(opt_input.settings),
      global_discomfort_(opt_input.global_discomfort),
      potential_params_(opt_input.opt_conf.conf.potentials_params()),
      time_mapper_(speed_immutable_t_, opt_conf_.steps, opt_conf_.dt),
#pragma GCC diagnostic push
// max_required_lateral_gap is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
      constraint_state_processor_(
          opt_conf_.dt, opt_conf_.steps, opt_conf_.ra_to_fb, opt_conf_.ra_to_rb,
          potential_params_.barrier_params().max_required_lateral_gap()),
#pragma GCC diagnostic pop
      lqr_potentials_(LqrPotentials(
          opt_conf_.limits, potential_params_, profiles_.ref_profile,
          profiles_.search_profile, opt_input.path, opt_conf_.steps,
          opt_input.opt_conf.ego_max_speed)) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_GenerateStatePotentials);

  const std::vector<Constraint>& constraints = opt_input.constraints;
  const std::vector<Decision>& decisions = opt_input.decisions;

  if (opt_input.check_param) {
    CHECK(potential_params_.has_barrier_params());
    CHECK(potential_params_.has_square_potentials());
    CHECK(potential_params_.has_length_attractor_params());
    CHECK(potential_params_.has_speed_attractor_params());
    const auto& barrier_params = potential_params_.barrier_params();
    const auto& static_range_params = potential_params_.static_range_params();
    CHECK_GT(barrier_params.potential_at_barrier(), 0.0);
    CHECK_GT(barrier_params.barrier_width(), 0.0);
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
    CHECK_GT(barrier_params.max_required_lateral_gap(), 0.0);
#pragma GCC diagnostic pop
    CHECK_GT(barrier_params.square_barrier_gain(), 0.0);
    CHECK_GT(static_range_params.static_range_gain(), 0.0);
    const auto& square_params = potential_params_.square_potentials();
    CHECK_GT(square_params.jerk_effort_gains().positive_gain(), 0.0);
    CHECK_GT(square_params.jerk_effort_gains().negative_gain(), 0.0);
    CHECK_GT(square_params.accel_effort_gains().positive_gain(), 0.0);
    CHECK_GT(square_params.accel_effort_gains().negative_gain(), 0.0);
    CHECK_GT(square_params.lateral_accel_effort_gain(), 0.0);
    const auto& speed_attractor_params =
        potential_params_.speed_attractor_params();
    CHECK_GT(speed_attractor_params.min_gain(), 0.0);
    CHECK_GT(speed_attractor_params.dx(), 0.0);
    const auto& length_attractor_params =
        potential_params_.length_attractor_params();
    CHECK_GT(length_attractor_params.min_gain(), 0.0);
    CHECK_GT(length_attractor_params.dx(), 0.0);
  }

  DCHECK_EQ(constraints.size(), decisions.size());

  // This concurrent_vector is used to record debug information about the
  // proximity speed when generating proximity speed potentials in parallel.
  // TODO(yuwang): After the subsequent modification of ProximitySpeed to rely
  // on a buffer set by the state, add its debug information in a more concise
  // form.
  tbb::concurrent_vector<::planner::speed::pb::ProximitySpeedConstraintDebug>
      proximity_speed_debugs;

  // Loop through all constraints and add barriers, static/dynamic range
  // potentials for passing/yielding to a given constraint, proximity speed.
  auto task = [&](const tbb::blocked_range<int>& r) {
    for (int ix = r.begin(); ix != r.end(); ix++) {
      SetStatePotentialsForOneConstraint(constraints[ix], decisions[ix], ix,
                                         proximity_speed_debugs);
    }
  };
  tbb::parallel_for(tbb::blocked_range<int>(0, constraints.size()), task);

  // Sort ProximitySpeed to avoid determinism issues introduced by summation
  // in disordered order.
  for (auto& state_potentials : lqr_potentials_.state_potentials) {
    std::sort(state_potentials.proximity_speed_attractors.begin(),
              state_potentials.proximity_speed_attractors.end());
  }

  if (FLAGS_planning_enable_alilqr_in_speed_opt) {
    for (int i = 0; i < opt_conf_.steps; ++i) {
      // Currently the search profile might slightly violate the limits, to
      // make sure the opt problem have a solution whenever search has a
      // solution, we extend the limit according to the search profile.
      auto& state_potential = lqr_potentials_.state_potentials[i];
      state_potential.al_j_constraints = JerkAlInequalityConstraint(
          opt_conf_.limits, profiles_.search_profile[i].j);

      state_potential.al_a_constraints.emplace_back(
          /*sign=*/true, std::min(opt_conf_.limits.brake_a.min,
                                  profiles_.search_profile[i].a));
      state_potential.al_a_constraints.emplace_back(
          /*sign=*/false, std::max(opt_conf_.limits.accel_a.max,
                                   profiles_.search_profile[i].a));
      state_potential.al_v_constraints.emplace_back(
          /*sign=*/true, 0.0);
      state_potential.al_v_constraints.emplace_back(
          /*sign=*/false, opt_conf_.ego_max_speed);
    }
  }

  if (IsValidTreeSwitchTimeIx(tree_search_result_.tree_switch_time_ix,
                              opt_conf_.steps) &&
      HasAnyValidRiskMitigationConstraint(constraints,
                                          opt_conf_.dt * opt_conf_.steps)) {
    DCHECK(tree_search_result_.tree_search_type ==
           pb::TreeSearchType_Enum_kRiskMitigation);
    AddSwitchPointYieldBarrierForRiskMitigation();
  }

  // Prune unnecessary state potentials, including barriers, static range and
  // dynamic range currently.
  PruneStatePotentials();

  // After pruning, generate the lower bound of the repeller which we do not
  // want to violate by attractor.
  LowerBoundOfPrunedYieldRepeller();

  // TODO(speed): refactor the code to make it more clear and scalable,
  // consolidate the length/position attractor and the following attractor.
  if (AddCreepAttractor(constraints, decisions)) {
  } else if (AddConstraintAttractor(constraints, decisions)) {
    lqr_potentials_.num_state_potentials++;
  } else {
    // If we have added attractor to constraint, then we don't need to add
    // attractor for the ref profile.
    AddSpeedAttractor();
    AddLengthAttractor();

    // If following attractor has been added, we don't call
    // AddFollowingAttractor, because they do the similar things but for
    // different APIs.
    // TODO(lewisliu): consolidate the APIs.
    if (AddFollowingAttractor(constraints, decisions)) {
      lqr_potentials_.num_state_potentials++;
    }
  }
  AddLastProfileJerkAttractor();

  // TODO(Tingran): Decide if we should add an attractor for the the state at
  // switch time for lane change tree search.

  lqr_potentials_.ValidateStatePotentials();
  UpdateStatePotentialDebug(proximity_speed_debugs, potential_debug);

  DCHECK_EQ(lqr_potentials_.state_potentials.size(), opt_input.opt_conf.steps);
}

// Returns true if we should add barriers for the constraint.
bool LqrPotentialsGenerator::ShouldAddStateBarriers(
    const Constraint& constraint, const bool pass) {
  // If the constraint is GAP_ALIGN, we should not add state barriers.
  if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
    return false;
  }
  // If the constraint is PROXIMITY_SPEED, we should not add state barriers.
  if (constraint.type == ConstraintType::Constraint_Type_PROXIMITY_SPEED) {
    return false;
  }
  if (pass && constraint.settings.pass_always_possible) {
    return false;
  }
  // If we yield a creep constraint, we should not add a state barrier to it.
  // TODO(yuwang): Review if pass barriers should be added for creep
  // constraints.
  if (!pass && constraint.settings.is_creep) {
    return false;
  }

  DCHECK(!constraint.settings.is_for_lane_change_tree_search)
      << "We currently do not support non gap align constraint to be lane "
         "change tree constraint.";
  // If the constraint's pass/yield option is SOFT given the decision,
  // we should not add state barriers.
  if ((pass ? constraint.settings.pass_option
            : constraint.settings.yield_option) ==
      pb::ConstraintSettings_PassYieldOption_SOFT) {
    return false;
  }
  return true;
}

// Returns true if we should add repellers for the constraint.
bool LqrPotentialsGenerator::ShouldAddStateRepellers(
    const Constraint& constraint, const bool pass) {
  // If the constraint is GAP_ALIGN, we should not add state repellers.
  if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
    return false;
  }
  // If the constraint is PROXIMITY_SPEED, we should not add state repellers.
  if (constraint.type == ConstraintType::Constraint_Type_PROXIMITY_SPEED) {
    return false;
  }
  if (pass && constraint.settings.pass_always_possible) {
    return false;
  }
  // We should not add any state repeller to creep constraint.
  if (constraint.settings.is_creep) {
    return false;
  }
  DCHECK(!constraint.settings.is_for_lane_change_tree_search)
      << "We currently do not support non gap align constraint to be lane "
         "change tree constraint.";
  // If the constraint's pass/yield option is SOFT given the decision,
  // we should add state repellers.
  if ((pass ? constraint.settings.pass_option
            : constraint.settings.yield_option) ==
      pb::ConstraintSettings_PassYieldOption_SOFT) {
    return true;
  }
  return false;
}

// TODO(speed): consolidate AddStateStaticRanges/AddStateBarrierAndRepeller.
bool LqrPotentialsGenerator::AddStateBarrierAndRepeller(
    const Constraint& constraint, const bool pass) {
  if (pass) {
    DCHECK(!constraint.settings.pass_always_possible);
  }
  DCHECK(constraint.type != ConstraintType::Constraint_Type_GAP_ALIGN);
  DCHECK(constraint.type != ConstraintType::Constraint_Type_PROXIMITY_SPEED);

  // Currently, we have two types of state boundaries. One is the state barrier,
  // which is a hard boundary and cannot be violated. The other is the state
  // repeller, which can be slightly violated. For now, we only add state
  // repellers to soft constraints.
  const bool is_soft = (pass ? constraint.settings.pass_option
                             : constraint.settings.yield_option) ==
                       pb::ConstraintSettings_PassYieldOption_SOFT;
  const bool for_barrier = !is_soft;
  // We should not add state repeller to creep constraint.
  if (!for_barrier) {
    DCHECK(!constraint.settings.is_creep);
  }
  // For barriers, we only consider the states within barrier_lat_gap which is
  // the min gap for safety guarantee.
  const bool only_consider_min_lat_gap = for_barrier;

  const double discomfort = constraint.selected_discomfort;
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);

  bool boundary_added = false;
  const bool only_one_state = constraint.states.size() == 1;
  if (only_one_state) {
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        constraint.type == ConstraintType::Constraint_Type_AVOID_REGION ||
        constraint.type == ConstraintType::Constraint_Type_STOP_POINT ||
        constraint.type == ConstraintType::Constraint_Type_NO_BLOCK ||
        math::IsApprox(constraint.states[0].start_time,
                       constraint.states[0].end_time))
        << constraint.unique_constraint_id;

    auto process_single_constraint_state = [this, pass, &boundary_added,
                                            for_barrier](double start_time,
                                                         double end_time,
                                                         double boundary_x) {
      // Ignore if constraint is before speed immutable time.
      if (end_time <= speed_immutable_t_) {
        return;
      }
      // Add boundary from start_time to end_time. No need to add if decision
      // is pass and start_time is beyond horizon, or if yield and end time
      // is smaller than immutable time.
      int start_ix = 0, end_ix = 0;
      if (pass && !time_mapper_.MaybeGetTimeIx(start_time, pass, &start_ix)) {
        return;
      }
      if (!pass && !time_mapper_.MaybeGetTimeIx(end_time, pass, &end_ix)) {
        return;
      }
      // Calculate the start_ix for yield and end_ix for pass, which is
      // clamped inside [immutable_time, horizon].
      start_ix = pass ? start_ix
                      : time_mapper_.GetTimeIx(start_time, /*is_pass=*/false);
      end_ix =
          pass ? time_mapper_.GetTimeIx(end_time, /*is_pass=*/true) : end_ix;
      // Add boundaries for all indices within [start_ix, end_ix].
      for (int ix = start_ix; ix <= end_ix; ix++) {
        if (for_barrier) {
          if (FLAGS_planning_enable_alilqr_in_speed_opt) {
            lqr_potentials_.state_potentials[ix].al_x_constraints.emplace_back(
                pass, boundary_x);
          } else {
            lqr_potentials_.state_potentials[ix].barriers.emplace_back(
                pass, boundary_x,
                potential_params_.barrier_params().potential_at_barrier() *
                    math::Sqr(
                        1.0 /
                        potential_params_.barrier_params().barrier_width()));
          }
        } else {
          lqr_potentials_.state_potentials[ix].repellers.emplace_back(
              pass, boundary_x,
              potential_params_.state_repeller_params().gain());
        }
        boundary_added = true;
      }
    };

    constraint_state_processor_.ProcessStateOfSingleStateConstraint(
        constraint.states[0], constraint.settings, opt_conf_.min_range,
        discomfort, pass, /*ignore_reasoning_context=*/false,
        /*only_consider_min_lat_gap=*/only_consider_min_lat_gap,
        process_single_constraint_state);
  } else {
    // Multiple constraint states can only come from agent-related constraints.
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        constraint.type == ConstraintType::Constraint_Type_SPEED_OBJECT ||
        constraint.type == ConstraintType::Constraint_Type_SHIFT_SPEED_OBJECT);

    auto process_constraint_state = [this, pass, &boundary_added, for_barrier](
                                        double time, double boundary_x) {
      int boundary_time_ix = 0;
      if (!time_mapper_.MaybeGetTimeIx(time, pass, &boundary_time_ix)) {
        return;
      }
      DCHECK(
          !math::NearZero(potential_params_.barrier_params().barrier_width()));
      if (for_barrier) {
        if (FLAGS_planning_enable_alilqr_in_speed_opt) {
          lqr_potentials_.state_potentials[boundary_time_ix]
              .al_x_constraints.emplace_back(pass, boundary_x);
        } else {
          lqr_potentials_.state_potentials[boundary_time_ix]
              .barriers.emplace_back(
                  pass, boundary_x,
                  potential_params_.barrier_params().potential_at_barrier() *
                      math::Sqr(
                          1.0 /
                          potential_params_.barrier_params().barrier_width()));
        }
      } else {
        lqr_potentials_.state_potentials[boundary_time_ix]
            .repellers.emplace_back(
                pass, boundary_x,
                potential_params_.state_repeller_params().gain());
      }
      boundary_added = true;
    };
    // TODO(Tingran): Figure out how to clean up state barrier contract.
    // Currently, we ignore required lat gap from reasoning context by only
    // considering min lat gap, but longitudinally we still respect reasoning
    // context such as pass extra time.
    constraint_state_processor_.ProcessStates(
        constraint.states, constraint.settings, opt_conf_.min_range, discomfort,
        pass, /*ignore_reasoning_context=*/false,
        /*only_consider_min_lat_gap=*/only_consider_min_lat_gap,
        /*only_consider_strict=*/false, process_constraint_state);
  }
  return boundary_added;
}

void LqrPotentialsGenerator::AddSwitchPointYieldBarrierForRiskMitigation() {
  if (FLAGS_planning_enable_alilqr_in_speed_opt) {
    lqr_potentials_.state_potentials[tree_search_result_.tree_switch_time_ix]
        .al_x_constraints.emplace_back(
            /*pass=*/false,
            profiles_.search_profile[tree_search_result_.tree_switch_time_ix]
                .x);
  } else {
    lqr_potentials_.state_potentials[tree_search_result_.tree_switch_time_ix]
        .barriers.emplace_back(
            /*pass=*/false,
            profiles_.search_profile[tree_search_result_.tree_switch_time_ix]
                .x);
  }
  lqr_potentials_.num_state_potentials++;
}

// Returns true if we should add static ranges for the constraint.
bool LqrPotentialsGenerator::ShouldAddStaticRanges(const Constraint& constraint,
                                                   const bool pass) {
  // If the constraint is PROXIMITY_SPEED, we should not add static ranges.
  if (constraint.type == ConstraintType::Constraint_Type_PROXIMITY_SPEED) {
    return false;
  }
  // No need to add any static range, if we pass a constraint that is
  // pass_always_possible.
  if (pass && constraint.settings.pass_always_possible) {
    return false;
  }
  // We should not add any static range to creep constraint since its stop range
  // is 0.0.
  if (constraint.settings.is_creep) {
    return false;
  }

  return true;
}

bool LqrPotentialsGenerator::AddStaticRanges(const int32_t c_ix,
                                             const Constraint& constraint,
                                             const bool pass) {
  if (pass) {
    DCHECK(!constraint.settings.pass_always_possible);
  }

  DCHECK(constraint.type != ConstraintType::Constraint_Type_PROXIMITY_SPEED);
  const double discomfort = constraint.selected_discomfort;
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);
  const auto& settings = constraint.settings;

  // Default to 0 so if not specified it has no impact on adding static range.
  int32_t yield_or_pass_active_time_ix = 0;
  if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
    auto iter = gap_align_info_map_.find(c_ix);
    DCHECK(iter != gap_align_info_map_.end());
    yield_or_pass_active_time_ix = pass ? iter->second.pass_active_time_ix
                                        : iter->second.yield_active_time_ix;
  }

  // To limit the impact and prevent safety regression, "Soften StaticRange For
  // Comfort" will only be enabled when all of the following conditions are met
  // simultaneously:
  // 1. Initial speed < kInitSpeedThreForSoftenStaticRangeInMps
  // 2. Not for gap align constraint
  // 3. Discomfort < 0.75
  const bool is_gap_align =
      constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN;
  const double initial_speed = profiles_.ref_profile.front().v;
  const bool should_soften_static_range =
      FLAGS_planning_enable_soften_static_range_for_comfort &&
      initial_speed < kInitSpeedThreForSoftenStaticRangeInMps &&
      !is_gap_align && global_discomfort_ < 0.75;

  const double softer_static_range_gain =
      potential_params_.static_range_params().softer_static_range_gain();
  const double softer_static_range_gain_discount =
      potential_params_.static_range_params()
          .softer_static_range_gain_discount();
  const double nominal_static_range_gain =
      potential_params_.static_range_params().static_range_gain();

  bool static_range_repeller_added = false;
  const bool only_one_state = constraint.states.size() == 1;
  const PassYieldOption pass_yield_option =
      (pass ? constraint.settings.pass_option
            : constraint.settings.yield_option);
  if (only_one_state) {
    // It is possible for agent related constraint to have only one state.
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        constraint.type == ConstraintType::Constraint_Type_AVOID_REGION ||
        constraint.type == ConstraintType::Constraint_Type_STOP_POINT ||
        constraint.type == ConstraintType::Constraint_Type_NO_BLOCK ||
        math::IsApprox(constraint.states[0].start_time,
                       constraint.states[0].end_time))
        << constraint.unique_constraint_id;

    auto process_single_constraint_state = [&](const double start_time,
                                               const double end_time,
                                               const double static_range_x) {
      // Add static range from start time to end time.
      DCHECK_LE(start_time, end_time);
      // Ignore if end_time is before immutable time, as we can do nothing.
      if (end_time <= speed_immutable_t_) {
        return;
      }
      // Add static range from start time to end time. No need to add if
      // decision is pass and start_time is beyond horizon, or if yield and end
      // time is smaller than immutable time.
      // TODO(speed): consider setting this block as a function for non-object
      // related constraint and use also in barrier.
      int start_ix = 0, end_ix = 0;
      if (pass && !time_mapper_.MaybeGetTimeIx(start_time, pass, &start_ix)) {
        return;
      }
      if (!pass && !time_mapper_.MaybeGetTimeIx(end_time, pass, &end_ix)) {
        return;
      }
      // Calculate the start_ix for yield and end_ix for pass, which is clamped
      // inside [immutable_time, horizon].
      start_ix = pass ? start_ix
                      : time_mapper_.GetTimeIx(start_time,
                                               /*is_pass=*/false);
      end_ix =
          pass ? time_mapper_.GetTimeIx(end_time, /*is_pass=*/true) : end_ix;
      const double stop_range = pass ? settings.pass_stop_range(discomfort)
                                     : settings.yield_stop_range(discomfort);
      if (math::NearZero(stop_range)) {
        // The pass option for creep constraint is hard, but currently its stop
        // range is also 0.0.
        DCHECK(pass_yield_option !=
                   PassYieldOption::ConstraintSettings_PassYieldOption_HARD ||
               settings.is_creep);
        return;
      }
      // we do not want stop range to be zero as it would cause discomfort
      // increasing.
      DCHECK_GT(stop_range, 0.0);

      // stop_range is a buffer on top of static_range_x, we do this to make
      // sure static_range_x-stop_range >=0 for yielding.
      // For barriers we don't have that concern because we don't have extra
      // buffers for barriers in the opt, if there is any yield barrier <= 0,
      // the search yield time map should be <= 0 as well, so the search should
      // fail and we don't go to the opt.
      const double outer_layer_static_range_x =
          pass ? static_range_x : std::max(stop_range, static_range_x);

      for (int ix = start_ix; ix <= end_ix; ++ix) {
        if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN &&
            !MaybeConsiderAsConflictAccordingToActiveTime(
                pass_yield_option, yield_or_pass_active_time_ix,
                opt_conf_.steps, ix)) {
          // Do not add static range for state that is not a conflict according
          // to active time.
          continue;
        }
        const double static_range_gain = should_soften_static_range
                                             ? softer_static_range_gain
                                             : nominal_static_range_gain;
        const double state_decay =
            should_soften_static_range
                ? std::pow(softer_static_range_gain_discount, ix)
                : 1.0;
        lqr_potentials_.state_potentials[ix]
            .static_range_repellers.emplace_back(
                pass, outer_layer_static_range_x,
                /*buffer=*/stop_range, static_range_gain * state_decay,
                /*is_softer_static_range=*/
                should_soften_static_range);
        static_range_repeller_added = true;
      }
    };

    constraint_state_processor_.ProcessStateOfSingleStateConstraint(
        constraint.states[0], settings, opt_conf_.min_range, discomfort, pass,
        /*ignore_reasoning_context=*/false,
        /*only_consider_min_lat_gap=*/false, process_single_constraint_state);
  } else {
    // Multiple constraint states can only come from agent-related
    // constraints.
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        constraint.type == ConstraintType::Constraint_Type_SPEED_OBJECT ||
        constraint.type == ConstraintType::Constraint_Type_SHIFT_SPEED_OBJECT ||
        constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN);

    auto process_constraint_state = [&](const double time,
                                        const double static_range_x) {
      int static_range_state_ix = 0;
      if (!time_mapper_.MaybeGetTimeIx(time, pass, &static_range_state_ix)) {
        return;
      }
      if (constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN) {
        if (!MaybeConsiderAsConflictAccordingToActiveTime(
                pass_yield_option, yield_or_pass_active_time_ix,
                opt_conf_.steps, static_range_state_ix)) {
          // Do not add static range for state that is not a conflict according
          // to active time.
          return;
        }
        if (constraint.settings.is_for_lane_change_tree_search &&
            static_range_state_ix >= tree_search_result_.tree_switch_time_ix &&
            tree_search_result_.tree_search_type ==
                pb::TreeSearchType_Enum_kLaneChangeGapAlign) {
          // The potential conflict is after switch time, and this constraint
          // has enabled lane change tree search.
          if (!MaybeConsiderAsConflictForConflictAfterLcSwitchTime(
                  /*adjust_for_tree_branch=*/false, pass, constraint)) {
            // In speed opt, we only consider adding static range after switch
            // time if its decision is yield and we forbid yield decision
            // branching.
            return;
          }
        }
      }
      const double stop_range = pass ? settings.pass_stop_range(discomfort)
                                     : settings.yield_stop_range(discomfort);
      if (math::NearZero(stop_range)) {
        // The pass option for creep constraint is hard, but currently its stop
        // range is also 0.0.
        DCHECK(pass_yield_option !=
                   PassYieldOption::ConstraintSettings_PassYieldOption_HARD ||
               settings.is_creep);
        return;
      }
      // we do not want stop range to be zero as it would cause discomfort
      // increasing.
      DCHECK_GT(stop_range, 0.0);

      // stop_range is a buffer on top of static_range_x, we do this to make
      // suer static_range_x-stop_range >=0 for yielding.
      // For barriers we don't have that concern because we don't have extra
      // buffers for barriers in the opt, if there is any yield barrier <= 0,
      // the search yield time map should be <= 0 as well, so the search should
      // fail and we don't go to the opt.
      const double outer_layer_static_range_x =
          pass ? static_range_x : std::max(stop_range, static_range_x);

      const double static_range_gain = should_soften_static_range
                                           ? softer_static_range_gain
                                           : nominal_static_range_gain;
      const double state_decay =
          should_soften_static_range
              ? std::pow(softer_static_range_gain_discount,
                         static_range_state_ix)
              : 1.0;
      lqr_potentials_.state_potentials[static_range_state_ix]
          .static_range_repellers.emplace_back(pass, outer_layer_static_range_x,
                                               /*buffer=*/stop_range,
                                               static_range_gain * state_decay,
                                               /*is_softer_static_range=*/
                                               should_soften_static_range);
      static_range_repeller_added = true;
    };

    constraint_state_processor_.ProcessStates(
        constraint.states, settings, opt_conf_.min_range, discomfort, pass,
        /*ignore_reasoning_context=*/false,
        /*only_consider_min_lat_gap=*/false,
        /*only_consider_strict=*/false, process_constraint_state);
  }

  // Process standoffs.
  // TODO(yuwang): At present, it seems that standoff is no longer in use.
  // Consider removing this part of the code.
  const bool should_consider_standoff = !pass && !constraint.standoffs.empty();
  if (should_consider_standoff) {
    const double discomfort = constraint.selected_discomfort;
    DCHECK_LE(Discomforts::kMin, discomfort);
    DCHECK_LE(discomfort, Discomforts::kMax);

    constraint_state_processor_.ProcessStandoffs(
        constraint.standoffs, discomfort, [&](double time, double position_x) {
          int standoff_state_ix = 0;
          if (!time_mapper_.MaybeGetTimeIx(time, /*is_pass=*/false,
                                           &standoff_state_ix)) {
            return;
          }
          // Soft repeller for standoff, with stop range.
          lqr_potentials_.state_potentials[standoff_state_ix]
              .static_range_repellers.emplace_back(
                  /*pass=*/false, position_x,
                  /*buffer=*/constraint.settings.yield_stop_range(discomfort),
                  potential_params_.static_range_params().static_range_gain(),
                  true);
          static_range_repeller_added = true;
        });
  }
  return static_range_repeller_added;
}

// Returns true if we should add dynamic ranges for the constraint.
bool LqrPotentialsGenerator::ShouldAddDynamicRanges(
    const Constraint& constraint, const bool pass) {
  // No need to add any dynamic range, if we pass a constraint that is
  // pass_always_possible.
  if (pass && constraint.settings.pass_always_possible) {
    return false;
  }

  const double discomfort = constraint.selected_discomfort;
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);
  const double pass_headway = constraint.settings.pass_headway(discomfort);
  const double yield_headway = constraint.settings.yield_headway(discomfort);
  if ((pass ? pass_headway : yield_headway) <= 0.0) {
    return false;
  }

  return true;
}

bool LqrPotentialsGenerator::AddDynamicRanges(const Constraint& constraint,
                                              bool pass) {
  DCHECK(  // NOLINT, FP when checking macro DCHECK
      constraint.type == ConstraintType::Constraint_Type_SPEED_OBJECT ||
      constraint.type == ConstraintType::Constraint_Type_SHIFT_SPEED_OBJECT ||
      constraint.type == ConstraintType::Constraint_Type_GAP_ALIGN);
  const double discomfort = constraint.selected_discomfort;
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);

  const double headway = pass ? constraint.settings.pass_headway(discomfort)
                              : constraint.settings.yield_headway(discomfort);
  bool dynamic_range_added = false;
  const auto& settings = constraint.settings;
  const bool only_one_state = (constraint.states.size() == 1);
  if (only_one_state) {
    DVLOG(2) << "The speed object only has one state.";
  }

  auto process_constraint_state = [&](double time, double dynamic_range_x,
                                      double signed_longitudinal_speed) {
    int state_ix = -1;
    // Filter out states before adding headway to be consistant with speed
    // search. Otherwise, we may add pass dynamic ranges for constraints that
    // are out of the planning horizon with unreasonable pass decisions.
    if (!time_mapper_.MaybeGetTimeIx(time, pass, &state_ix)) {
      return;
    }

    time += (pass ? headway * -1.0 : headway);
    if (!time_mapper_.MaybeGetTimeIx(time, pass, &state_ix)) {
      return;
    }
    // Do not add dynamic range when agent is moving towards ego along path.
    if (signed_longitudinal_speed <= 0.0) {
      return;
    }

    const double gain =
        potential_params_.dynamic_range_params().dynamic_range_gain() *
        std::pow(potential_params_.dynamic_range_params()
                     .dynamic_range_gain_discount(),
                 state_ix);
    // Estimate the buffer dist introduced by headway.
    // Note that headway * signed_longitudinal_speed may significantly under
    // estimate the actual buffer and cause high-cost issues, so we use the
    // distance between dynamic_rang_x and search profile as a lower bound.
    // TODO(lewisliu): Consider use x lower and upper bound for pass and yield
    // respectively, which are constructed based on min/ref profile as well as
    // barrier positions.
    const double buffer_dist = std::max(
        headway * signed_longitudinal_speed,
        std::abs(dynamic_range_x - profiles_.search_profile[state_ix].x));
    const double ego_sudden_decel = opt_conf_.limits.brake_a.min;
    const double agent_sudden_decel = opt_conf_.limits.brake_a.min;
    if (pass) {
      lqr_potentials_.state_potentials[state_ix]
          .pass_dynamic_range_repellers.emplace_back(
              gain,
              /*sign=*/true, profiles_.min_profile[state_ix].v,
              ego_sudden_decel, dynamic_range_x, signed_longitudinal_speed,
              agent_sudden_decel, buffer_dist);
    } else {
      lqr_potentials_.state_potentials[state_ix]
          .yield_dynamic_range_repellers.emplace_back(
              gain,
              /*sign=*/false, profiles_.search_profile[state_ix].v,
              ego_sudden_decel, dynamic_range_x, signed_longitudinal_speed,
              agent_sudden_decel, buffer_dist);
    }

    dynamic_range_added = true;
  };

  constraint_state_processor_.ProcessStatesForDynamicRange(
      constraint.states, settings, opt_conf_.min_range, discomfort,
      speed_immutable_t_, pass, process_constraint_state);

  return dynamic_range_added;
}

bool LqrPotentialsGenerator::ShouldAddProximitySpeed(
    const pb::ProximitySpeedParams& proximity_speed_params,
    double global_discomfort) {
  return global_discomfort >= proximity_speed_params.min_req_discomfort();
}

// TODO(yuwang): Refactor this function with a mechanism similar to
// StateProcessor and add unit tests for the function that generates the
// proximity speed of a state.
bool LqrPotentialsGenerator::AddProximitySpeed(
    const Constraint& constraint,
    tbb::concurrent_vector<::planner::speed::pb::ProximitySpeedConstraintDebug>&
        proximity_speed_debugs) {
  DCHECK(constraint.type == ConstraintType::Constraint_Type_PROXIMITY_SPEED);
  const auto& proximity_speed_params =
      constraint.settings.proximity_speed_params;
  DCHECK_GT(proximity_speed_params.max_lat_gap(), 0.0);
  DCHECK_NE(proximity_speed_params.speed_limit_case(),
            pb::ProximitySpeedParams::SpeedLimitCase::SPEED_LIMIT_NOT_SET);

  const Profile target_min_profile =
      GetTargetMinProfile(profiles_.min_profile, opt_conf_.limits, opt_conf_.dt,
                          proximity_speed_params);
  bool proximity_speed_added = false;
  int first_valid_state_ix = -1;
  // TODO(lewisliu): Support reasoning context related buffer setting if
  // necessary.
  // True for speed lower bound and false for speed upper bound.
  const bool sign =
      proximity_speed_params.speed_limit_case() ==
          pb::ProximitySpeedParams::SpeedLimitCase::kMaxAllowedRelSpeed &&
      proximity_speed_params.max_allowed_rel_speed() <= 0.0;

  auto proximity_speed_debug =
      ::planner::speed::pb::ProximitySpeedConstraintDebug();
  proximity_speed_debug.set_unique_constraint_id(
      constraint.unique_constraint_id);
  proximity_speed_debug.set_sign(sign);
  for (const auto& state : constraint.states) {
    if (state.abs_lat_gap > proximity_speed_params.max_lat_gap()) {
      continue;
    }

    int state_ix = -1;
    if (FLAGS_planning_enable_continuous_proximity_speed_in_speed_opt) {
      // Skip the state with start time > planning horizon.
      if (!time_mapper_.MaybeGetTimeIx(state.start_time, true, &state_ix)) {
        continue;
      }
    }

    if (!time_mapper_.MaybeGetTimeIx(state.start_time, false, &state_ix)) {
      continue;
    }

    if (first_valid_state_ix == -1) {
      first_valid_state_ix = state_ix;
    }

    double start_x = state.start_x(Discomforts::kMid);
    double end_x = state.end_x(Discomforts::kMid);

    // Only consider the states overlap with the search profile. And for speed
    // upper bound setting, we also consider the states after overlapped states
    // with higher end x than the target brake profile.
    // TODO(lewisliu): figure out if we need similar logic for speed lower bound
    // setting.
    if (FLAGS_planning_enable_continuous_proximity_speed_in_speed_opt) {
      if (profiles_.search_profile[state_ix].x < start_x ||
          profiles_.search_profile[state_ix].x > end_x) {
        if (!sign && proximity_speed_added &&
            end_x >= target_min_profile[state_ix].x) {
          // Update the end x to better normalize the proximity speed cost.
          end_x = std::max(end_x, profiles_.search_profile[state_ix].x);
        } else {
          continue;
        }
      }
    }

    double min_speed = 0.0;
    double max_speed = std::numeric_limits<double>::infinity();
    // Preprocess the max/min allow speed to make sure it is no less/more than
    // min/max profile achievable speed. Otherwise the optimized profile may not
    // brake as much as possible.
    if (proximity_speed_params.speed_limit_case() ==
        pb::ProximitySpeedParams::SpeedLimitCase::kMaxAllowedSpeed) {
      max_speed = std::max(proximity_speed_params.max_allowed_speed(),
                           target_min_profile[state_ix].v);
    } else if (proximity_speed_params.max_allowed_rel_speed() > 0.0) {
      DCHECK_EQ(proximity_speed_params.speed_limit_case(),
                pb::ProximitySpeedParams::SpeedLimitCase::kMaxAllowedRelSpeed);
      const double speed_limit =
          math::Clamp(state.signed_longitudinal_speed +
                          proximity_speed_params.max_allowed_rel_speed(),
                      proximity_speed_params.abs_speed_bound().start_pos(),
                      proximity_speed_params.abs_speed_bound().end_pos());
      max_speed = std::max(speed_limit, target_min_profile[state_ix].v);
    } else {
      DCHECK_EQ(proximity_speed_params.speed_limit_case(),
                pb::ProximitySpeedParams::SpeedLimitCase::kMaxAllowedRelSpeed);
      DCHECK_NE(proximity_speed_params.max_allowed_rel_speed(), 0.0);
      const double speed_limit =
          math::Clamp(state.signed_longitudinal_speed +
                          proximity_speed_params.max_allowed_rel_speed(),
                      proximity_speed_params.abs_speed_bound().start_pos(),
                      proximity_speed_params.abs_speed_bound().end_pos());
      min_speed = std::min(speed_limit, profiles_.ref_profile[state_ix].v);
    }

    const double state_decay =
        std::pow(kProximitySpeedGainDiscount, state_ix - first_valid_state_ix);
    const double gain =
        potential_params_.proximity_speed_params().gain() * state_decay;

    DCHECK_GE(first_valid_state_ix, 0);
    DCHECK_LT(first_valid_state_ix, opt_conf_.steps);
    lqr_potentials_.state_potentials[state_ix]
        .proximity_speed_attractors.emplace_back(gain, min_speed, max_speed,
                                                 start_x, end_x, sign);
    proximity_speed_added = true;

    auto* state_debug = proximity_speed_debug.add_states();
    state_debug->set_time_ix(state_ix);
    state_debug->set_start_x(start_x);
    state_debug->set_end_x(end_x);
    state_debug->set_speed_limit(sign ? min_speed : max_speed);
  }
  proximity_speed_debugs.push_back(std::move(proximity_speed_debug));
  return proximity_speed_added;
}

bool LqrPotentialsGenerator::AddCreepAttractor(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions) {
  // TODO(speed): consider moving the creep signal reasoning code to a
  // post-search reasoning submodule
  double unused_closest_ra_dist_to_subject = std::numeric_limits<double>::max();
  auto creep_should_consider = [](const Constraint& constraint) {
    (void)constraint;
    return true;
  };
  int target_constraint_ix = FindClosestYieldConstraintIfShouldConsider(
      constraints, decisions, creep_should_consider, opt_conf_.min_range,
      opt_conf_.min_range_max_required_lat_gap, opt_conf_.ra_to_fb,
      &unused_closest_ra_dist_to_subject, /*should_check_all_states*/ true);
  if (target_constraint_ix < 0) {
    return false;
  }

  const Constraint& constraint = constraints[target_constraint_ix];
  const ConstraintSettings& settings = constraint.settings;
  if (!settings.is_creep) {
    return false;
  }
  // If the closest yield constraint is creep constraint, we add creep
  // attractor.
  rt_event::PostRtEvent<rt_event::planner::AddCreepAttractor>();

  DCHECK(decisions[target_constraint_ix] == Decision::YIELD);
  DCHECK_EQ(constraint.type, ConstraintType::Constraint_Type_SPEED_OBJECT);
  const double discomfort = constraint.selected_discomfort;
  DCHECK(math::NearZero(settings.pass_stop_range(discomfort)));
  DCHECK(math::NearZero(settings.yield_stop_range(discomfort)));
  DCHECK(math::NearZero(settings.pass_headway(discomfort)));
  DCHECK(math::NearZero(settings.yield_headway(discomfort)));
  DCHECK(math::NearZero(settings.min_creep_distance));

  is_creep_ = true;

  // The search profile can be completely static in the first the few seconds
  // even if there is space to move forward due to the adjust for pass feature.
  // Therefore we use the upper bound of the search profile and the initial
  // profile as the target to avoid generating stop profile when creeping.
  const Profile creep_initial_profile =
      GenerateInitialProfile(settings, profiles_.ref_profile, opt_conf_.limits,
                             opt_conf_.dt, opt_conf_.steps);

  // Add following attractor.
  for (size_t state_ix = 0; state_ix < profiles_.search_profile.size();
       ++state_ix) {
    auto& following_attractor =
        lqr_potentials_.state_potentials[state_ix].following_attractor;
    const double ref_x = std::max(profiles_.search_profile[state_ix].x,
                                  creep_initial_profile[state_ix].x);
    if (ref_x < kMinAttractingLength) {
      continue;
    }
    following_attractor.emplace(ref_x, kCreepAttractorDx, kCreepAttractorGain,
                                1.0 / ref_x);
  }
  return true;
}

bool LqrPotentialsGenerator::AddConstraintAttractor(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions) {
  if (!opt_settings_.enable_fast_acceleration) {
    return false;
  }

  if (FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration) {
    const int steps = opt_conf_.steps;
    DCHECK_EQ(yield_repeller_lower_bound_.size(), steps);
    for (int state_ix = 0; state_ix < steps; ++state_ix) {
      auto& following_attractor =
          lqr_potentials_.state_potentials[state_ix].following_attractor;
      DCHECK(!following_attractor.has_value());
      DCHECK_GE(yield_repeller_lower_bound_[state_ix], 0.0);
      const double attractor_length =
          std::min(yield_repeller_lower_bound_[state_ix],
                   profiles_.ref_profile[state_ix].x);
      following_attractor.emplace(
          attractor_length,
          /*dx=*/kAttractorDxForFastAccelerationWithXUpperBound,
          potential_params_.position_attractor_params().gain() *
              kMagnificationForFastAcceleration,
          /*normalization_factor=*/1.0);
    }
    return true;
  }

  double unused_closest_ra_dist_to_subject = std::numeric_limits<double>::max();
  auto fast_acceleration_should_consider = [](const Constraint& constraint) {
    return constraint.type != ConstraintType::Constraint_Type_GAP_ALIGN &&
           (IsSpeedObjectConstraint(constraint) ||
            constraint.type == ConstraintType::Constraint_Type_AVOID_REGION ||
            constraint.type == ConstraintType::Constraint_Type_STOP_POINT ||
            constraint.type == ConstraintType::Constraint_Type_NO_BLOCK);
  };
  const int target_constraint_ix = FindClosestYieldConstraintIfShouldConsider(
      constraints, decisions, fast_acceleration_should_consider,
      opt_conf_.min_range, opt_conf_.min_range_max_required_lat_gap,
      opt_conf_.ra_to_fb, &unused_closest_ra_dist_to_subject);
  if (target_constraint_ix < 0) {
    return false;
  }

  const Constraint& constraint = constraints[target_constraint_ix];
  DCHECK(decisions[target_constraint_ix] == Decision::YIELD);

  AddConstraintAttractor(constraint);
  return true;
}

void LqrPotentialsGenerator::AddConstraintAttractor(
    const Constraint& constraint) {
  const double discomfort = constraint.selected_discomfort;
  const double yield_extra_time =
      constraint.settings.yield_extra_time(discomfort);
  const double yield_extra_dist =
      constraint.settings.yield_extra_distance(discomfort);
  const double yield_stop_range =
      constraint.settings.yield_stop_range(discomfort);
  if (constraint.type == ConstraintType::Constraint_Type_AVOID_REGION ||
      constraint.type == ConstraintType::Constraint_Type_STOP_POINT ||
      constraint.type == ConstraintType::Constraint_Type_NO_BLOCK) {
    const auto& state = constraint.states[0];
    const int start_state_ix = time_mapper_.GetTimeIx(state.start_time,
                                                      /*is_pass=*/false);
    const int end_state_ix =
        time_mapper_.GetTimeIx(state.end_time + yield_extra_time,
                               /*is_pass=*/false);
    for (int state_ix = start_state_ix; state_ix <= end_state_ix; ++state_ix) {
      auto& following_attractor =
          lqr_potentials_.state_potentials[state_ix].following_attractor;
      const double yield_min_range = GetMinRangeForConstraintState(
          constraint.settings, state, opt_conf_.min_range, discomfort,
          opt_conf_.min_range_max_required_lat_gap,
          /*is_pass=*/false);
      const double ref_x = state.start_x(discomfort) - yield_min_range -
                           yield_extra_dist - yield_stop_range;
      if (ref_x < kMinAttractingLength) {
        continue;
      }

      auto following_attractor_params =
          potential_params_.following_attractor_params();
      following_attractor_params.set_gain(following_attractor_params.gain());
      following_attractor_params.set_dx(kAttractorDxForFastAcceleration);
      following_attractor.emplace(ref_x, following_attractor_params,
                                  1.0 / ref_x);
    }
  } else {
    for (const ConstraintState& state : constraint.states) {
      DCHECK_EQ(state.start_time, state.end_time);
      const double state_time = state.end_time + yield_extra_time;
      int state_ix = 0;
      if (!time_mapper_.MaybeGetTimeIx(state_time, /*is_pass=*/false,
                                       &state_ix)) {
        continue;
      }
      auto& following_attractor =
          lqr_potentials_.state_potentials[state_ix].following_attractor;
      const double yield_min_range = GetMinRangeForConstraintState(
          constraint.settings, state, opt_conf_.min_range, discomfort,
          opt_conf_.min_range_max_required_lat_gap,
          /*is_pass=*/false);
      const double ref_x = state.start_x(discomfort) - yield_min_range -
                           yield_extra_dist - yield_stop_range;
      if (ref_x < kMinAttractingLength) {
        continue;
      }

      auto following_attractor_params =
          potential_params_.following_attractor_params();
      following_attractor_params.set_gain(following_attractor_params.gain());
      following_attractor_params.set_dx(kAttractorDxForFastAcceleration);
      following_attractor.emplace(ref_x, following_attractor_params,
                                  1.0 / ref_x);
    }
  }
}

void LqrPotentialsGenerator::AddSpeedAttractor() {
  if (opt_settings_.disable_speed_attractor) {
    return;
  }
  // We don't add any speed attractor if we need to stay stopped.
  if (opt_settings_.stay_stop_info.maybe_stay_stop) {
    return;
  }

  if (FLAGS_planning_enable_x_upper_bound_attraction_in_speed_opt &&
      !opt_settings_.enable_fast_acceleration &&
      profiles_.search_profile.back().x <
          kSearchProfileProgressThresholdForAttraction) {
    return;
  }

  // Attraction to the ref profile ensures acceleration and progress. However,
  // the ref profile can be much faster than the optimal profile anyway when
  // there are constraints. So there will be inevitable high attraction costs
  // in these cases, which probably results in an unsmooth optimized profile.
  // To get reasonable attraction costs in all cases, we use the length ratio of
  // the search profile and the ref profile as a heuristic to dynamically adjust
  // the attraction gain.
  const double progress_discount =
      math::NearZero(profiles_.ref_profile.back().x)
          ? 1.0
          : profiles_.search_profile.back().x / profiles_.ref_profile.back().x;

  const double scaling_factor =
      opt_settings_.enable_fast_acceleration ? 1.0 : progress_discount;

  // At the higher discomfort, attractor should be weaker since high discomfort
  // usually means emergency, sudden cut-in, or other cases ego need to brake.
  // At zero discomfort , the search space is the widest, so stronger attractor
  // can be acceptable since the buffer is large enough to guarantee safety, and
  // progress is relatively insufficient due to narrow ranges of j/a.
  // TODO(lewisliu): Test out if we still need this logic considering it has
  // conflict with progress_for_kinematic.
  const double gain = DiscomfortVarying::EvaluateAtDiscomfort(
                          potential_params_.speed_attractor_params().max_gain(),
                          potential_params_.speed_attractor_params().min_gain(),
                          global_discomfort_) *
                      scaling_factor;

  for (int state_ix = 0; state_ix < opt_conf_.steps; ++state_ix) {
    AddSpeedAttractorForState(state_ix, gain);
  }
}

void LqrPotentialsGenerator::AddSpeedAttractorForState(const int state_ix,
                                                       double gain) {
  auto& speed_progress =
      lqr_potentials_.state_potentials[state_ix].speed_progress;
  DCHECK(!speed_progress.has_value());
  const double ref_speed = profiles_.ref_profile[state_ix].v;
  if (ref_speed < kMinAttractingSpeed) {
    return;
  }
  speed_progress.emplace(ref_speed,
                         potential_params_.speed_attractor_params().dx(), gain,
                         /*sign=*/false, 1.0 / ref_speed);
}

void LqrPotentialsGenerator::AddLengthAttractor() {
  // We will add length attractors for the min profile to quickly decelerate to
  // a full stop and then stay stopped if we need to stay stopped.
  if (opt_settings_.stay_stop_info.maybe_stay_stop) {
    for (int state_ix = 0; state_ix < opt_conf_.steps; ++state_ix) {
      auto& length_progress =
          lqr_potentials_.state_potentials[state_ix].length_progress;
      DCHECK(!length_progress.has_value());
      DCHECK(!std::isinf(opt_settings_.stay_stop_info.desired_stopping_x));
      length_progress.emplace(
          opt_settings_.stay_stop_info.desired_stopping_x,
          potential_params_.length_attractor_params().dx(),
          potential_params_.length_attractor_params().max_gain(),
          /*sign=*/true,
          /*normalization_factor=*/
          std::min(1.0 / opt_settings_.stay_stop_info.desired_stopping_x, 1.0));
    }
    return;
  }

  if (FLAGS_planning_enable_x_upper_bound_attraction_in_speed_opt &&
      !opt_settings_.enable_fast_acceleration &&
      profiles_.search_profile.back().x <
          kSearchProfileProgressThresholdForAttraction) {
    const int steps = opt_conf_.steps;
    DCHECK_EQ(yield_repeller_lower_bound_.size(), steps);
    for (int state_ix = 0; state_ix < steps; ++state_ix) {
      auto& length_progress =
          lqr_potentials_.state_potentials[state_ix].length_progress;
      DCHECK(!length_progress.has_value());
      DCHECK_GE(yield_repeller_lower_bound_[state_ix], 0.0);
      // TODO(yuwang): Check if we can make it more consistent, use search or
      // ref.
      const double ref_length =
          std::min(profiles_.search_profile.at(state_ix).x,
                   yield_repeller_lower_bound_[state_ix]);
      length_progress.emplace(
          ref_length, potential_params_.position_attractor_params(),
          /*sign=*/false,
          /*normalization_factor=*/1.0 / std::max(ref_length, 1.0));
    }
    return;
  }

  const double progress_discount =
      math::NearZero(profiles_.ref_profile.back().x)
          ? 1.0
          : profiles_.search_profile.back().x / profiles_.ref_profile.back().x;

  const double scaling_factor =
      opt_settings_.enable_fast_acceleration ? 1.0 : progress_discount;

  const double gain =
      DiscomfortVarying::EvaluateAtDiscomfort(
          potential_params_.length_attractor_params().max_gain(),
          potential_params_.length_attractor_params().min_gain(),
          global_discomfort_) *
      scaling_factor;

  for (int state_ix = 0; state_ix < opt_conf_.steps; ++state_ix) {
    AddLengthAttractorForState(state_ix, gain);
  }
}

// TODO(lewisliu): Try length attractor only for the final state.
void LqrPotentialsGenerator::AddLengthAttractorForState(const int state_ix,
                                                        double gain) {
  auto& length_progress =
      lqr_potentials_.state_potentials[state_ix].length_progress;
  DCHECK(!length_progress.has_value());
  const double ref_length = profiles_.ref_profile[state_ix].x;
  // if the ref length is too small, we don't add progress cost to avoid
  // computation issues.
  if (ref_length < kMinAttractingLength) {
    return;
  }
  // TODO(yuwang): Change the dx value for length attractor to a uniform value.
  const double dx = opt_settings_.enable_fast_acceleration
                        ? kAttractorDxForFastAcceleration
                        : potential_params_.length_attractor_params().dx();
  length_progress.emplace(ref_length, dx, gain, /*sign=*/false,
                          1.0 / ref_length);
}

// Returns true if following attractor should be added for the constraint.
bool LqrPotentialsGenerator::ShouldAddFollowingAttractor(
    const Constraint& constraint) {
  const double discomfort = constraint.selected_discomfort;
  const double yield_headway = constraint.settings.yield_headway(discomfort);
  // Only add for lead dynamic object with headway > 0.
  if (constraint.type != ConstraintType::Constraint_Type_SPEED_OBJECT &&
      constraint.type != ConstraintType::Constraint_Type_SHIFT_SPEED_OBJECT) {
    return false;
  }
  if (!constraint.settings.enable_dynamic_attractor || yield_headway < 0.0) {
    return false;
  }
  return true;
}

void LqrPotentialsGenerator::AddFollowingAttractor(
    const Constraint& constraint) {
  const double discomfort = constraint.selected_discomfort;
  DCHECK(ShouldAddFollowingAttractor(constraint));
  const double yield_extra_time =
      constraint.settings.yield_extra_time(discomfort);
  const double yield_required_lat_gap =
      constraint.settings.yield_required_lat_gap(discomfort);
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  // TODO(speed): align this to use min_range_max_req_lat_gap.
  const double barrier_req_lat_gap =
      potential_params_.barrier_params().max_required_lateral_gap();
#pragma GCC diagnostic pop
  DCHECK_LE(barrier_req_lat_gap, yield_required_lat_gap);

  DCHECK_NE(constraint.states.size(), 1);
  const double yield_dist =
      constraint.settings.yield_extra_distance(discomfort);
  for (const auto& state : constraint.states) {
    if (state.abs_lat_gap > yield_required_lat_gap) {
      continue;
    }
    // As the following attractor is only added for yielding, so only the yield
    // distance is used for attraction.
    const double attractor_x =
        state.start_x(discomfort) - yield_dist -
        GetMinRangeForConstraintState(constraint.settings, state,
                                      opt_conf_.min_range, discomfort,
                                      barrier_req_lat_gap,
                                      /*is_pass=*/false);

    const double state_time = state.end_time + yield_extra_time;
    const std::optional<int> state_ix =
        time_mapper_.GetTimeIxWithinHorizon(state_time,
                                            /*floor_round=*/false);
    if (!state_ix.has_value()) {
      continue;
    }
    auto& following_attractor =
        lqr_potentials_.state_potentials[state_ix.value()].following_attractor;
    // For one timestamp, there is only one following attractor at most.
    DCHECK(!following_attractor.has_value());
    const double max_attractor_x = state.start_x(discomfort);
    DCHECK_GT(max_attractor_x, 0);
    following_attractor.emplace(attractor_x,
                                potential_params_.following_attractor_params(),
                                1.0 / max_attractor_x);
  }
}

bool LqrPotentialsGenerator::AddFollowingAttractor(
    const std::vector<Constraint>& constraints,
    const std::vector<Decision>& decisions) {
  double unused_closest_ra_dist_to_subject = std::numeric_limits<double>::max();
  const int closest_yield_constraint_ix =
      FindClosestYieldConstraintIfShouldConsider(
          constraints, decisions, &ShouldAddFollowingAttractor,
          opt_conf_.min_range, opt_conf_.min_range_max_required_lat_gap,
          opt_conf_.ra_to_fb, &unused_closest_ra_dist_to_subject);
  if (closest_yield_constraint_ix < 0) {
    return false;
  }
  const Constraint& constraint = constraints[closest_yield_constraint_ix];
  DCHECK(decisions[closest_yield_constraint_ix] == Decision::YIELD);
  AddFollowingAttractor(constraint);
  return true;
}

bool LqrPotentialsGenerator::ShouldAddLastProfileJerkAttractor(
    const std::optional<Profile>& last_profile, const Limits& limits,
    double global_discomfort, bool enable_fast_acceleration,
    bool stay_stopped) {
  if (!FLAGS_planning_enable_last_profile_attraction_in_speed_opt) {
    return false;
  }

  if (enable_fast_acceleration) {
    return false;
  }

  if (global_discomfort > kMaxDiscomfortForLastProfileJerkAttraction) {
    return false;
  }

  if (!last_profile.has_value()) {
    return false;
  }

  // We don't want to add jerk attractor if the last profile is not smooth at
  // all.
  const double max_j_diff = std::max(limits.accel_j.max, limits.brake_j.max) -
                            std::min(limits.accel_j.min, limits.brake_j.min);
  DCHECK_GT(max_j_diff, 0.0);
  // The jerk of the last few poses, which are generated to extend the last
  // profile, can be super high. And the last profile attraction will be  super
  // small after some time window, so these last poses will not impact the cost
  // any way. So we don't check the jerk of last few poses.
  // TODO(lewisliu): Try to improve the generation of the last profile.
  constexpr int kNumOfPoseToCheck = 70;
  const auto end_iter = last_profile->begin() + kNumOfPoseToCheck;
  DCHECK_GT(last_profile->end() - end_iter, 0);
  const bool has_jerk_jump =
      std::adjacent_find(last_profile->begin(), end_iter,
                         [max_j_diff](const State& lhs, const State& rhs) {
                           return std::abs(lhs.j - rhs.j) >= max_j_diff * 0.5;
                         }) != end_iter;
  if (has_jerk_jump) {
    return false;
  }

  // To not affect the vehicle's acceleration at start-up, we don't add jerk
  // attractor if the last profile is static and the ego don't need to stay
  // stopped.
  // TODO(lewisliu): Remove this rule when fast acceleration API is properly
  // used.
  constexpr double static_speed_threshold = 0.1;
  const bool is_static = std::none_of(
      last_profile->begin(), last_profile->end(),
      [](const State& state) { return state.v > static_speed_threshold; });
  return !is_static || stay_stopped;
}

void LqrPotentialsGenerator::AddLastProfileJerkAttractor() {
  if (!ShouldAddLastProfileJerkAttractor(
          profiles_.last_profile, opt_conf_.limits, global_discomfort_,
          opt_settings_.enable_fast_acceleration,
          opt_settings_.stay_stop_info.maybe_stay_stop)) {
    return;
  }

  for (int state_ix = 0; state_ix < opt_conf_.steps; ++state_ix) {
    auto& jerk_attractor =
        lqr_potentials_.state_potentials[state_ix].jerk_attractor;
    DCHECK(!jerk_attractor.has_value());
    DCHECK(profiles_.last_profile.has_value());
    const double ref_j = (*profiles_.last_profile)[state_ix].j;

    jerk_attractor.emplace(
        ref_j, potential_params_.last_profile_jerk_attractor_params().dx(),
        kJerkAttractorPositiveGainFactor *
            potential_params_.last_profile_jerk_attractor_params().gain() *
            std::pow(kLastProfileJerkAttractorGainDiscount, state_ix),
        potential_params_.last_profile_jerk_attractor_params().gain() *
            std::pow(kLastProfileJerkAttractorGainDiscount, state_ix));
  }
}

bool LqrPotentialsGenerator::SetStatePotentialsForOneConstraint(
    const Constraint& constraint, const Decision& decision,
    const size_t constraint_ix,
    tbb::concurrent_vector<::planner::speed::pb::ProximitySpeedConstraintDebug>&
        proximity_speed_debugs) {
  // Skip constraints with "IGNORE" decision, except proximity speed
  // constraints, whose decision are always "ignore" since they are not
  // considered in the speed search phase.
  if (decision == Decision::IGNORE &&
      constraint.type != ConstraintType::Constraint_Type_PROXIMITY_SPEED) {
    return true;
  }

  DCHECK(  // NOLINT, FP when checking macro DCHECK
      decision == Decision::PASS || decision == Decision::YIELD ||
      (decision == Decision::IGNORE &&
       constraint.type == ConstraintType::Constraint_Type_PROXIMITY_SPEED));
  const bool pass = (decision == Decision::PASS);
  DCHECK_LE(Discomforts::kMin, constraint.selected_discomfort);
  DCHECK_LE(constraint.selected_discomfort, Discomforts::kMax);

  // If ego passes a no-block, nothing needs to be added.
  if (constraint.type == ConstraintType::Constraint_Type_NO_BLOCK && pass) {
    return true;
  }

  // Risk mitigation constraint would have been ignored in the main profile
  // optimization.
  // We add state boundaries to constraints other than GAP_ALIGN and
  // PROXIMITY_SPEED. If their yield/pass option is soft, we will add state
  // repeller for states within required lat gap, else we will add state barrier
  // for states within min lat gap.
  if ((ShouldAddStateBarriers(constraint, pass) ||
       ShouldAddStateRepellers(constraint, pass)) &&
      AddStateBarrierAndRepeller(constraint, pass)) {
    lqr_potentials_.num_state_potentials++;
  }

  // Add static range for following behind or passing a constraint.
  if (ShouldAddStaticRanges(constraint, pass) &&
      AddStaticRanges(constraint_ix, constraint, pass)) {
    lqr_potentials_.num_state_potentials++;
  }

  switch (constraint.type) {
    case ConstraintType::Constraint_Type_SPEED_OBJECT:
    case ConstraintType::Constraint_Type_SHIFT_SPEED_OBJECT:
    case ConstraintType::Constraint_Type_GAP_ALIGN: {
      if (ShouldAddDynamicRanges(constraint, pass) &&
          AddDynamicRanges(constraint, pass)) {
        lqr_potentials_.num_state_potentials++;
      }
      break;
    }
    case ConstraintType::Constraint_Type_AVOID_REGION:
    case ConstraintType::Constraint_Type_STOP_POINT:
    case ConstraintType::Constraint_Type_NO_BLOCK: {
      break;
    }

    case ConstraintType::Constraint_Type_PROXIMITY_SPEED: {
      if (ShouldAddProximitySpeed(constraint.settings.proximity_speed_params,
                                  global_discomfort_) &&
          AddProximitySpeed(constraint, proximity_speed_debugs)) {
        lqr_potentials_.num_state_potentials++;
      }
      break;
    }
    default:
      break;
  }
  return true;
}

void LqrPotentialsGenerator::PruneStatePotentials() {
  PruneBarriers();
  PruneRepellers();
  PruneStaticRanges();
  PruneDynamicRanges();
  PruneAlConstraintsOnPosition();
}

void LqrPotentialsGenerator::PruneBarriers() {
  for (auto& state_potential : lqr_potentials_.state_potentials) {
    int yield_ix = -1;
    double smallest_yield_x = std::numeric_limits<double>::infinity();
    int pass_ix = -1;
    double largest_pass_x = -std::numeric_limits<double>::infinity();
    for (size_t ix = 0; ix < state_potential.barriers.size(); ++ix) {
      // True for pass barriers, and false for yield barriers.
      if (state_potential.barriers[ix].sign()) {
        // Find the pass barriers with the max x0 and prune the others.
        if (math::UpdateMax(state_potential.barriers[ix].x0(),
                            largest_pass_x)) {
          pass_ix = ix;
        }
      } else {
        // Find the yield barriers with the min x0 and prune the others.
        if (math::UpdateMin(state_potential.barriers[ix].x0(),
                            smallest_yield_x)) {
          yield_ix = ix;
        }
      }
    }
    // Only keep the strictest barrier for pass/yield.
    std::vector<Barrier> barriers;
    barriers.reserve(2);
    if (yield_ix != -1) {
      barriers.push_back(state_potential.barriers[yield_ix]);
    }
    if (pass_ix != -1) {
      barriers.push_back(state_potential.barriers[pass_ix]);
    }
    state_potential.barriers.clear();
    std::copy(barriers.begin(), barriers.end(),
              std::back_inserter(state_potential.barriers));
  }
}

void LqrPotentialsGenerator::PruneRepellers() {
  for (auto& state_potential : lqr_potentials_.state_potentials) {
    int yield_ix = -1;
    double smallest_yield_x = std::numeric_limits<double>::infinity();
    int pass_ix = -1;
    double largest_pass_x = -std::numeric_limits<double>::infinity();
    for (size_t ix = 0; ix < state_potential.repellers.size(); ++ix) {
      // True for pass repellers, and false for yield repellers.
      if (state_potential.repellers[ix].sign()) {
        // Find the pass repellers with the max x0 and prune the others.
        if (math::UpdateMax(state_potential.repellers[ix].x0(),
                            largest_pass_x)) {
          pass_ix = ix;
        }
      } else {
        // Find the yield repellers with the min x0 and prune the others.
        if (math::UpdateMin(state_potential.repellers[ix].x0(),
                            smallest_yield_x)) {
          yield_ix = ix;
        }
      }
    }
    // Only keep the strictest repeller for pass/yield.
    std::vector<Barrier> repellers;
    repellers.reserve(2);
    if (yield_ix != -1) {
      repellers.push_back(state_potential.repellers[yield_ix]);
    }
    if (pass_ix != -1) {
      repellers.push_back(state_potential.repellers[pass_ix]);
    }
    state_potential.repellers.clear();
    std::copy(repellers.begin(), repellers.end(),
              std::back_inserter(state_potential.repellers));
  }
}

// TODO(yuwang): Since the current gap align constraint uses nominal static
// range, there still exists a situation where both softer static range and
// nominal static range coexist within the same frame. For now, we retain the
// previous prune function. When applying softer static range to the gap
// align constraint, the prune process should be simplified.
void LqrPotentialsGenerator::PruneStaticRanges() {
  for (auto& state_potential : lqr_potentials_.state_potentials) {
    std::vector<StaticRange> static_ranges;
    static_ranges.reserve(4);
    // There are two types of static range that need to be sorted, namely softer
    // and nominal. There are also two purposes for sorting, which are to obtain
    // the maximum value and to obtain the minimum value. Therefore, the sorting
    // static range type can be set by sort_for_softer, the sorting purpose can
    // be set by sort_for_min_iter (true for yielding the static range, false
    // for passing the static range). Through the combination of these two
    // variables, we can respectively obtain the minimum yield x0, the minimum
    // softer yield x0, the maximum pass x0, and the maximum softer pass x0.
    bool sort_for_softer = true;
    bool sort_for_min_iter = true;

    // With this comparer, when the two static ranges are both softer or both
    // nominal, |yield with small x0| < |yield with large x0| < |pass with small
    // x0| < |pass with large x0|. If the types of the two are different, move
    // the type of the current sorting towards the purpose of the current
    // sorting. For example, when the sorting type is softer and the sorting
    // target is the minimum value, then |softer's x0| < |nominal's x0| is
    // considered, and we want the orders to be like y-soft, p-soft, y-hard,
    // p-hard.
    auto static_range_comparer = [&sort_for_softer, &sort_for_min_iter](
                                     const StaticRange& lhs,
                                     const StaticRange& rhs) {
      if (lhs.is_softer_static_range() == rhs.is_softer_static_range()) {
        if (lhs.sign() == rhs.sign()) {
          if (lhs.x0() != rhs.x0()) {
            return lhs.x0() < rhs.x0();
          }
          // If the x0 and sign are the same, we want to sort by buffer. If we
          // sort for min iter, we want the static range with the smaller buffer
          // to be smaller, and for max iter, we want the static range with the
          // smaller buffer to be larger.
          if (sort_for_min_iter) {
            return lhs.buffer() < rhs.buffer();
          }
          return lhs.buffer() > rhs.buffer();
        }
        return !lhs.sign() && rhs.sign();
      }
      if (lhs.is_softer_static_range() == sort_for_softer) {
        return sort_for_min_iter;
      }
      return !sort_for_min_iter;
    };

    // Find the lowest static range for yielding.
    sort_for_softer = false;
    sort_for_min_iter = true;
    const auto min_static_range_iter = std::min_element(
        state_potential.static_range_repellers.begin(),
        state_potential.static_range_repellers.end(), static_range_comparer);
    if (min_static_range_iter != state_potential.static_range_repellers.end() &&
        !min_static_range_iter->sign() &&
        !min_static_range_iter->is_softer_static_range()) {
      static_ranges.push_back(*min_static_range_iter);
    }

    // Find the highest static range for pass.
    sort_for_softer = false;
    sort_for_min_iter = false;
    const auto max_static_range_iter = std::max_element(
        state_potential.static_range_repellers.begin(),
        state_potential.static_range_repellers.end(), static_range_comparer);
    if (max_static_range_iter != state_potential.static_range_repellers.end() &&
        max_static_range_iter->sign() &&
        !max_static_range_iter->is_softer_static_range()) {
      static_ranges.push_back(*max_static_range_iter);
    }

    // Find the lowest static range for softer yielding.
    sort_for_softer = true;
    sort_for_min_iter = true;
    const auto min_softer_static_range_iter = std::min_element(
        state_potential.static_range_repellers.begin(),
        state_potential.static_range_repellers.end(), static_range_comparer);
    if (min_softer_static_range_iter !=
            state_potential.static_range_repellers.end() &&
        !min_softer_static_range_iter->sign() &&
        min_softer_static_range_iter->is_softer_static_range()) {
      static_ranges.push_back(*min_softer_static_range_iter);
    }

    // Find the highest static range for softer pass.
    sort_for_softer = true;
    sort_for_min_iter = false;
    const auto max_softer_static_range_iter = std::max_element(
        state_potential.static_range_repellers.begin(),
        state_potential.static_range_repellers.end(), static_range_comparer);
    if (max_softer_static_range_iter !=
            state_potential.static_range_repellers.end() &&
        max_softer_static_range_iter->sign() &&
        max_softer_static_range_iter->is_softer_static_range()) {
      static_ranges.push_back(*max_softer_static_range_iter);
    }

    state_potential.static_range_repellers.clear();
    std::copy(static_ranges.begin(), static_ranges.end(),
              std::back_inserter(state_potential.static_range_repellers));
  }
}

void LqrPotentialsGenerator::PruneDynamicRanges() {
  for (auto& state_potential : lqr_potentials_.state_potentials) {
    // Find the dynamic range with lowest other stop dist for yielding.
    const auto min_iter = std::min_element(
        state_potential.yield_dynamic_range_repellers.begin(),
        state_potential.yield_dynamic_range_repellers.end(),
        [](const auto& lhs, const auto& rhs) {
          if (lhs.other_stop_x() != rhs.other_stop_x()) {
            return lhs.other_stop_x() < rhs.other_stop_x();
          }
          // If the other_stop_x of two repellers are the same, then choose
          // the one with smaller nominal_max_diff as the smaller one.
          return lhs.nominal_max_diff() < rhs.nominal_max_diff();
        });
    if (min_iter != state_potential.yield_dynamic_range_repellers.end()) {
      DCHECK_EQ(min_iter->sign(), false);
      std::vector<DynamicRange> yield_dynamic_range_repellers =
          std::vector<DynamicRange>{*min_iter};
      state_potential.yield_dynamic_range_repellers.clear();
      std::copy(
          yield_dynamic_range_repellers.begin(),
          yield_dynamic_range_repellers.end(),
          std::back_inserter(state_potential.yield_dynamic_range_repellers));
    }

    // Find the dynamic range with highest other stop dist for passing.
    const auto max_iter = std::max_element(
        state_potential.pass_dynamic_range_repellers.begin(),
        state_potential.pass_dynamic_range_repellers.end(),
        [](const auto& lhs, const auto& rhs) {
          if (lhs.other_stop_x() != rhs.other_stop_x()) {
            return lhs.other_stop_x() < rhs.other_stop_x();
          }
          // If the other_stop_x of two repellers are the same, then choose
          // the one with smaller nominal_max_diff as the greater one.
          return lhs.nominal_max_diff() > rhs.nominal_max_diff();
        });
    if (max_iter != state_potential.pass_dynamic_range_repellers.end()) {
      DCHECK_EQ(max_iter->sign(), true);
      std::vector<DynamicRange> pass_dynamic_range_repellers =
          std::vector<DynamicRange>{*max_iter};
      state_potential.pass_dynamic_range_repellers.clear();
      std::copy(
          pass_dynamic_range_repellers.begin(),
          pass_dynamic_range_repellers.end(),
          std::back_inserter(state_potential.pass_dynamic_range_repellers));
    }
  }
}

void LqrPotentialsGenerator::PruneAlConstraintsOnPosition() {
  for (auto& state_potential : lqr_potentials_.state_potentials) {
    int yield_ix = -1;
    double smallest_yield_x = std::numeric_limits<double>::infinity();
    int pass_ix = -1;
    double largest_pass_x = -std::numeric_limits<double>::infinity();
    for (size_t ix = 0; ix < state_potential.al_x_constraints.size(); ++ix) {
      // True for pass constraints, and false for yield constraints.
      if (state_potential.al_x_constraints[ix].sign()) {
        // Find the pass constraints with the max x0 and prune the others.
        if (math::UpdateMax(state_potential.al_x_constraints[ix].x0(),
                            largest_pass_x)) {
          pass_ix = ix;
        }
      } else {
        // Find the yield constraints with the min x0 and prune the others.
        if (math::UpdateMin(state_potential.al_x_constraints[ix].x0(),
                            smallest_yield_x)) {
          yield_ix = ix;
        }
      }
    }
    // Only keep the strictest constraints for pass/yield.
    std::vector<AlInequalityConstraint> constraints;
    constraints.reserve(2);
    if (yield_ix != -1) {
      constraints.push_back(state_potential.al_x_constraints[yield_ix]);
    }
    if (pass_ix != -1) {
      constraints.push_back(state_potential.al_x_constraints[pass_ix]);
    }
    state_potential.al_x_constraints.clear();
    std::copy(constraints.begin(), constraints.end(),
              std::back_inserter(state_potential.al_x_constraints));
  }
}

void LqrPotentialsGenerator::LowerBoundOfPrunedYieldRepeller() {
  const int steps = lqr_potentials_.state_potentials.size();
  yield_repeller_lower_bound_.resize(steps);
  double next_x_ub = std::numeric_limits<double>::infinity();
  for (int state_ix = steps - 1; state_ix >= 0; --state_ix) {
    double lowest_yield_static_range_x =
        std::numeric_limits<double>::infinity();
    const auto& static_ranges =
        lqr_potentials_.state_potentials[state_ix].static_range_repellers;
    DCHECK(static_ranges.size() <= 4)
        << "As we generate yield repeller lower bound after pruning, the "
           "static range's size should not be larger than 4.";
    for (const auto& static_range : static_ranges) {
      if (!static_range.sign() &&
          static_range.x0() < lowest_yield_static_range_x) {
        lowest_yield_static_range_x = static_range.x0();
      }
    }
    yield_repeller_lower_bound_[state_ix] =
        std::min({lowest_yield_static_range_x, next_x_ub});
    DCHECK_GE(yield_repeller_lower_bound_[state_ix], 0.0);
    next_x_ub = yield_repeller_lower_bound_[state_ix];
  }
}

void LqrPotentialsGenerator::UpdateStatePotentialDebug(
    const tbb::concurrent_vector<
        ::planner::speed::pb::ProximitySpeedConstraintDebug>&
        proximity_speed_debugs,
    speed::pb::SpeedOptPotentialDebug* potential_debug) {
  if (potential_debug == nullptr) {
    return;
  }

  // Add state barrier debug.
  potential_debug->mutable_state_barriers()->Reserve(
      lqr_potentials_.state_potentials.size());
  if (FLAGS_planning_enable_alilqr_in_speed_opt) {
    for (const auto& state_potential : lqr_potentials_.state_potentials) {
      auto* state_barrier_debug_ptr = potential_debug->add_state_barriers();
      for (const auto& x_constraint : state_potential.al_x_constraints) {
        const double x0 = x_constraint.x0();
        if (x_constraint.sign()) {
          if (!state_barrier_debug_ptr->has_largest_pass_x() ||
              state_barrier_debug_ptr->largest_pass_x() < x0) {
            state_barrier_debug_ptr->set_largest_pass_x(x0);
          }
          continue;
        }

        if (!state_barrier_debug_ptr->has_smallest_yield_x() ||
            state_barrier_debug_ptr->smallest_yield_x() > x0) {
          state_barrier_debug_ptr->set_smallest_yield_x(x0);
        }
      }
    }
  } else {
    for (const auto& state_potential : lqr_potentials_.state_potentials) {
      auto* state_barrier_debug_ptr = potential_debug->add_state_barriers();
      for (const auto& barrier : state_potential.barriers) {
        const double x0 = barrier.x0();
        if (barrier.sign()) {
          if (!state_barrier_debug_ptr->has_largest_pass_x() ||
              state_barrier_debug_ptr->largest_pass_x() < x0) {
            state_barrier_debug_ptr->set_largest_pass_x(x0);
          }
          continue;
        }

        if (!state_barrier_debug_ptr->has_smallest_yield_x() ||
            state_barrier_debug_ptr->smallest_yield_x() > x0) {
          state_barrier_debug_ptr->set_smallest_yield_x(x0);
        }
      }
    }
  }

  // Add state repeller debug.
  potential_debug->mutable_state_repellers()->Reserve(
      lqr_potentials_.state_potentials.size());
  for (const auto& state_potential : lqr_potentials_.state_potentials) {
    auto* state_repeller_debug_ptr = potential_debug->add_state_repellers();
    for (const auto& repeller : state_potential.repellers) {
      const double x0 = repeller.x0();
      if (repeller.sign()) {
        if (!state_repeller_debug_ptr->has_largest_pass_x() ||
            state_repeller_debug_ptr->largest_pass_x() < x0) {
          state_repeller_debug_ptr->set_largest_pass_x(x0);
        }
        continue;
      }

      if (!state_repeller_debug_ptr->has_smallest_yield_x() ||
          state_repeller_debug_ptr->smallest_yield_x() > x0) {
        state_repeller_debug_ptr->set_smallest_yield_x(x0);
      }
    }
  }

  // Add static range debug.
  potential_debug->mutable_static_ranges()->Reserve(
      lqr_potentials_.state_potentials.size());
  for (const auto& state_potential : lqr_potentials_.state_potentials) {
    auto* static_range_debug_ptr = potential_debug->add_static_ranges();
    for (const auto& static_range : state_potential.static_range_repellers) {
      if (static_range.sign() && !static_range.is_softer_static_range()) {
        static_range_debug_ptr->add_pass_x(static_range.x0());
      }
      if (static_range.sign() && static_range.is_softer_static_range()) {
        static_range_debug_ptr->add_pass_x_softer(static_range.x0());
      }
      if (!static_range.sign() && !static_range.is_softer_static_range()) {
        static_range_debug_ptr->add_yield_x(static_range.x0());
      }
      if (!static_range.sign() && static_range.is_softer_static_range()) {
        static_range_debug_ptr->add_yield_x_softer(static_range.x0());
      }
    }
  }

  // Add dynamic range debug.
  potential_debug->mutable_dynamic_ranges()->Reserve(
      lqr_potentials_.state_potentials.size());
  for (const auto& state_potential : lqr_potentials_.state_potentials) {
    auto* dynamic_range_debug_ptr = potential_debug->add_dynamic_ranges();
    for (const auto& pass_dynamic_range :
         state_potential.pass_dynamic_range_repellers) {
      DCHECK_EQ(pass_dynamic_range.sign(), true);
      dynamic_range_debug_ptr->add_pass_x(pass_dynamic_range.other_x());
      dynamic_range_debug_ptr->add_pass_stop_x(
          pass_dynamic_range.other_stop_x());
      dynamic_range_debug_ptr->add_pass_nominal_max_diff(
          pass_dynamic_range.nominal_max_diff());
    }

    for (const auto& yield_dynamic_range :
         state_potential.yield_dynamic_range_repellers) {
      DCHECK_EQ(yield_dynamic_range.sign(), false);
      dynamic_range_debug_ptr->add_yield_x(yield_dynamic_range.other_x());
      dynamic_range_debug_ptr->add_yield_stop_x(
          yield_dynamic_range.other_stop_x());
      dynamic_range_debug_ptr->add_yield_nominal_max_diff(
          yield_dynamic_range.nominal_max_diff());
    }
  }
  potential_debug->set_ego_sudden_decel(opt_conf_.limits.brake_a.min);

  // Add proximity speed debug.
  potential_debug->mutable_proximity_speed_constraints()->Reserve(
      proximity_speed_debugs.size());
  for (const auto& proximity_speed_debug : proximity_speed_debugs) {
    auto* debug = potential_debug->add_proximity_speed_constraints();
    *debug = std::move(proximity_speed_debug);
  }

  // Add following/position attractor debug.
  for (size_t state_ix = 0; state_ix < lqr_potentials_.state_potentials.size();
       ++state_ix) {
    const auto& following_attractor =
        lqr_potentials_.state_potentials[state_ix].following_attractor;
    if (!following_attractor.has_value()) {
      continue;
    }
    auto* following_attractor_debug_ptr =
        potential_debug->add_following_attractors();
    following_attractor_debug_ptr->set_state_ix(state_ix);
    following_attractor_debug_ptr->set_x(following_attractor->x0());

    const auto& position_attractor =
        lqr_potentials_.state_potentials[state_ix].length_progress;
    if (!position_attractor.has_value()) {
      continue;
    }
    auto* position_attractor_debug_ptr =
        potential_debug->add_position_attractors();
    position_attractor_debug_ptr->set_state_ix(state_ix);
    position_attractor_debug_ptr->set_x(position_attractor->x0());
  }
}

}  // namespace speed
}  // namespace planner
