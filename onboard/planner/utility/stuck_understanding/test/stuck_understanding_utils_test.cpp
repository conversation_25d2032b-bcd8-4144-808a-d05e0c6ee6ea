#include "planner/utility/stuck_understanding/stuck_understanding_utils.h"

#include <memory>

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/solver/searcher/profile_searcher_defs.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/speed_constraint.pb.h"
#include "planner_protos/speed_decision.pb.h"
#include "planner_protos/speed_yielding_fence.pb.h"
#include "pnc_map_service/test/map_test_util.h"
#include "protobuf_matchers/protocol_buffer_matchers.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {
namespace {

using ::protobuf_matchers::EqualsProto;

constexpr ObjectId kObjectId_1 = 1;
constexpr ObjectId kObjectId_2 = 2;

class StuckUnderstandingUtilsTest : public testing::Test {
 protected:
  void SetUp() override {
    google::protobuf::TextFormat::ParseFromString(
        R"pb(
          object_type: VEHICLE
          center_x: 10.0
          center_y: 0.75
          center_z: 0.0
          width: 3.0
          length: 10.0
          height: 3.5
        )pb",
        agent_1_proto_.mutable_tracked_object());
    google::protobuf::TextFormat::ParseFromString(
        R"pb(
          object_type: TRAFFIC_CONE
          center_x: 4.0
          center_y: 0.75
          center_z: 0.0
          width: 0.3
          length: 0.3
          height: 0.8
        )pb",
        agent_2_proto_.mutable_tracked_object());
    const int64_t timestamp = 0;
    const TrafficParticipantPose pose_at_plan_init_ts(
        timestamp, agent_1_proto_.tracked_object());
    planner_object_map_.emplace(
        kObjectId_1,
        PlannerObject{agent_1_proto_, timestamp, pose_at_plan_init_ts});
    planner_object_map_.emplace(
        kObjectId_2,
        PlannerObject{agent_2_proto_, timestamp, pose_at_plan_init_ts});

    const double init_map_x = 28821.68601358193;
    const double init_map_y = 39039.90699229622;
    map_test_util_.InitPncMapService(init_map_x, init_map_y, /*z=*/0.0,
                                     hdmap::test_util::SceneType::kMerge,
                                     "SCENARIO_MERGE");

    agent_in_lane_states_map_[kObjectId_1] =
        std::make_unique<AgentInLaneStates>();
    std::unique_ptr<AgentInLaneStates>& state_1_ptr =
        agent_in_lane_states_map_[kObjectId_1];
    state_1_ptr->tracked_state.inlane_param.center_line_side = math::pb::kRight;

    agent_in_lane_states_map_[kObjectId_2] =
        std::make_unique<AgentInLaneStates>();
    std::unique_ptr<AgentInLaneStates>& state_2_ptr =
        agent_in_lane_states_map_[kObjectId_2];
    state_2_ptr->tracked_state.inlane_param.inlane_clearance_m = 3.5;

    ego_params_.relative_lateral_distance_to_line_center = 0.1;
    ego_params_.width_m = 1.0;

    ra_creep_around_meta_ = RACreepAroundMetaData{
        10000, 0, false, false, map_test_util_.GetLane(128231)};
    SingleTrajCreepAroundMetaData meta_data;
    meta_data.agent_in_lane_states = &agent_in_lane_states_map_;
    meta_data.cz_in_lane_states = &construction_zones_inlane_states_;
    ra_creep_around_meta_.traj_related_meta.push_back(meta_data);
  }

  prediction::pb::Agent agent_1_proto_;
  prediction::pb::Agent agent_2_proto_;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map_;
  RACreepAroundMetaData ra_creep_around_meta_;
  pnc_map::MapTestUtil map_test_util_;
  AgentInLaneStatesMap agent_in_lane_states_map_;
  EgoInLaneParams ego_params_;
  std::vector<ConstructionZoneInLaneState> construction_zones_inlane_states_;
  std::optional<planner::pb::StuckDebug> optional_stuck_debug_;
};

TEST_F(StuckUnderstandingUtilsTest, DynamicObjectTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(kObjectId_1)
      ->second.set_is_primary_stationary(false);

  EXPECT_TRUE(ExtractStaticObjectPassageInfo(speed_result, planner_object_map_)
                  .empty());
}

TEST_F(StuckUnderstandingUtilsTest, NoStateConstraintTest) {
  // Set up constraint with empty states.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(kObjectId_1)
      ->second.set_is_primary_stationary(false);

  EXPECT_TRUE(ExtractStaticObjectPassageInfo(speed_result, planner_object_map_)
                  .empty());
}

TEST_F(StuckUnderstandingUtilsTest, StaticbjectTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.6;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  const auto gaps =
      ExtractStaticObjectPassageInfo(speed_result, planner_object_map_);
  ASSERT_EQ(gaps.size(), 1);
  EXPECT_EQ(gaps[0].is_dominating_constraint, true);
  EXPECT_NEAR(gaps[0].abs_lat_gap_m, 0.6, 1e-3);
  EXPECT_NEAR(gaps[0].critical_req_lat_gap_m, 0.4, 1e-3);
  EXPECT_NEAR(gaps[0].pass_minimum_req_lat_gap_m, 0.25, 1e-3);
  EXPECT_EQ(gaps[0].decision, speed::pb::YIELD);
  EXPECT_TRUE(gaps[0].typed_object_id.is_tracked_object());
  EXPECT_EQ(gaps[0].typed_object_id.id, kObjectId_1);
}

TEST_F(StuckUnderstandingUtilsTest, GetCreepAroundRequest_BaseTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;
  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  const auto optional_request = GetCreepAroundRequest(
      ego_params_, planner_object_map_, ra_creep_around_meta_,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_TRUE(optional_request.has_value());
  EXPECT_THAT(*optional_request, EqualsProto(R"pb(
    creep_around_objects: { object_id: 1 object_type: kTrackedObject }
  )pb"));
}

TEST_F(StuckUnderstandingUtilsTest, GetCreepAroundRequest_TwoVehiclesTest) {
  // Set up constraint for the first object. It is a dominant constraint.
  speed::Constraint dominant_constraint(speed::pb::Constraint::SPEED_OBJECT,
                                        /*fence_type=*/speed::pb::kLead,
                                        /*reasoner_id=*/"unit_test",
                                        /*constraint_id=*/"123");
  dominant_constraint.obj_id = 1;
  dominant_constraint.states.push_back(speed::ConstraintState(
      {/*start_time=*/1.5, std::numeric_limits<double>::infinity(),
       /*start_x=*/15.0,
       /*end_x=*/100.0}));
  dominant_constraint.states.back().abs_lat_gap = 0.35;
  dominant_constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  // Add a second object that is not dominating constraint.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  object_type: VEHICLE
                                                  center_x: 10.0
                                                  center_y: 8.75
                                                  center_z: 0.0
                                                  width: 3.0
                                                  length: 10.0
                                                  height: 3.5
                                                )pb",
                                                &tracked_object);
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(timestamp, tracked_object);
  speed::Constraint yield_constraint(speed::pb::Constraint::STOP_POINT,
                                     /*fence_type=*/speed::pb::kLead,
                                     /*reasoner_id=*/"unit_test",
                                     /*constraint_id=*/"123");
  yield_constraint.obj_id = 3;
  yield_constraint.states.push_back(speed::ConstraintState(
      {/*start_time=*/1.5, std::numeric_limits<double>::infinity(),
       /*start_x=*/20.0,
       /*end_x=*/100.0}));
  yield_constraint.states.back().abs_lat_gap = 0.35;
  yield_constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result2;
  constraint_result2.constraints = {dominant_constraint, yield_constraint};
  constraint_result2.decisions = {speed::pb::SpeedDecision::YIELD,
                                  speed::pb::SpeedDecision::YIELD};
  constraint_result2.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result2(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result2),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt, /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result2(std::move(speed_search_result2),
                                   speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result2;

  planner_object_map_.emplace(
      3, PlannerObject{agent_proto, timestamp, pose_at_plan_init_ts});
  planner_object_map_.find(3)->second.set_is_primary_stationary(true);
  agent_in_lane_states_map_[3] = std::make_unique<AgentInLaneStates>();

  const auto optional_request = GetCreepAroundRequest(
      ego_params_, planner_object_map_, ra_creep_around_meta_,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_TRUE(optional_request.has_value());
  EXPECT_THAT(*optional_request, EqualsProto(R"pb(
    creep_around_objects: { object_id: 1 object_type: kTrackedObject }
  )pb"));
}

TEST_F(StuckUnderstandingUtilsTest, GetCreepAroundRequest_DisableRequestTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = kObjectId_1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  ra_creep_around_meta_.is_dry_steering = true;
  const auto optional_request = GetCreepAroundRequest(
      ego_params_, planner_object_map_, ra_creep_around_meta_,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest, GetCreepAroundRequest_TrafficConeTest) {
  // Set up constraint for the traffic cone. It is a dominant constraint.
  speed::Constraint dominant_constraint(speed::pb::Constraint::SPEED_OBJECT,
                                        /*fence_type=*/speed::pb::kLead,
                                        /*reasoner_id=*/"unit_test",
                                        /*constraint_id=*/"123");
  dominant_constraint.obj_id = 2;
  dominant_constraint.states.push_back(speed::ConstraintState(
      {/*start_time=*/1.5, std::numeric_limits<double>::infinity(),
       /*start_x=*/15.0,
       /*end_x=*/100.0}));
  dominant_constraint.states.back().abs_lat_gap = 0.35;
  dominant_constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(dominant_constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);
  planner_object_map_.find(kObjectId_2)->second.set_is_primary_stationary(true);

  const auto optional_request = GetCreepAroundRequest(
      ego_params_, planner_object_map_, ra_creep_around_meta_,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_TRUE(optional_request.has_value());
  EXPECT_THAT(*optional_request, EqualsProto(R"pb(
    creep_around_objects: { object_id: 2 object_type: kTrackedObject }
  )pb"));
}

TEST_F(StuckUnderstandingUtilsTest,
       GetCreepAroundRequest_DisableIfVehicleInFrontTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  // If object is in the middle of the lane or the whole body is at the same
  // side of ref path as Ego, don't enable creep.
  agent_in_lane_states_map_.find(kObjectId_1)
      ->second->tracked_state.inlane_param.center_line_side = math::pb::kOn;

  const auto optional_request = GetCreepAroundRequest(
      ego_params_, planner_object_map_, ra_creep_around_meta_,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest, GetStuckSceneRequest_NoStuckTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(
      R"pb(reason: kNoStuck stuck_score: 0.0)pb", &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/true,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest, GetStuckSceneRequest_HasStuckTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  reason: kNoStuck
                                                  stuck_score: 1.0)pb",
                                                &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/true,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_TRUE(optional_request.has_value());
  EXPECT_THAT(*optional_request, EqualsProto(R"pb(
    questions: CREEP_AROUND
    creep_around_request: {
      creep_around_objects: { object_id: 1 object_type: kTrackedObject }
    }
  )pb"));
}

TEST_F(StuckUnderstandingUtilsTest, GetStuckSceneRequest_HasTrafficLightTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});

  // Set up constraints.
  speed::Constraint tl_constraint(speed::pb::Constraint::STOP_POINT,
                                  /*fence_type=*/speed::pb::kTrafficLight,
                                  /*reasoner_id=*/"unit_test",
                                  /*constraint_id=*/"tl_123");
  tl_constraint.obj_id = 123;
  constexpr double kTlConstraintStartTime = 0.0;
  constexpr double kTlConstraintStartX = 8.0;
  tl_constraint.states.push_back(speed::ConstraintState(
      {kTlConstraintStartTime, std::numeric_limits<double>::infinity(),
       kTlConstraintStartX,
       /*end_x=*/21.0}));
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.constraints.push_back(tl_constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  reason: kNoStuck
                                                  stuck_score: 1.0)pb",
                                                &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/true,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest, GetStuckSceneRequest_NotInAutoModeTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);

  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  reason: kNoStuck
                                                  stuck_score: 1.0)pb",
                                                &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/true,
      /*is_in_autonomous_driving=*/false,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest,
       GetStuckSceneRequest_HasStuckWithSpeedTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);

  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  reason: kNoStuck
                                                  stuck_score: 1.0)pb",
                                                &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/2.0,
      /*are_all_traj_without_much_progress=*/true,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest,
       GetStuckSceneRequest_HasStuckButHasCandidateWithProgressTest) {
  // Set up constraints.
  speed::Constraint constraint(speed::pb::Constraint::SPEED_OBJECT,
                               /*fence_type=*/speed::pb::kLead,
                               /*reasoner_id=*/"unit_test",
                               /*constraint_id=*/"123");
  constraint.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constexpr double kStartX = 15.0;
  constraint.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), kStartX,
       /*end_x=*/100.0}));
  constraint.states.back().abs_lat_gap = 0.35;
  constraint.settings.pass_required_lat_gap =
      speed::DiscomfortVarying({0.0, 1.0}, {1.0, 0.4});
  speed::ConstraintResult constraint_result;
  constraint_result.constraints.push_back(constraint);
  constraint_result.decisions.push_back(speed::pb::SpeedDecision::YIELD);
  constraint_result.dominant_constraint_ix = 0;
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_is_primary_stationary(true);

  pb::StuckSignal stuck_signal;
  google::protobuf::TextFormat::ParseFromString(R"pb(
                                                  reason: kNoStuck
                                                  stuck_score: 1.0)pb",
                                                &stuck_signal);
  const auto optional_request = GetStuckSceneRequest(
      stuck_signal, construction_zones_inlane_states_, ego_params_,
      planner_object_map_, pb::UnstuckSeed(),
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/false,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  EXPECT_FALSE(optional_request.has_value());
}

TEST_F(StuckUnderstandingUtilsTest,
       GetStuckSceneRequest_InvalidatePreviousRequestTest) {
  // Set up constraints.
  speed::SpeedSearchResult speed_search_result;
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  ra_creep_around_meta_.traj_related_meta[0].speed_result = &speed_result;

  // Set up planner object.
  planner_object_map_.find(kObjectId_1)->second.set_l2_distance_to_ego_m(30);

  pb::UnstuckSeed unstuck_seed;
  google::protobuf::TextFormat::ParseFromString(
      R"pb(
        latest_stuck_scene_request: {
          questions: CREEP_AROUND
          creep_around_request: {
            creep_around_objects: { object_id: 1 object_type: kTrackedObject }
          }
        })pb",
      &unstuck_seed);
  const auto optional_request = GetStuckSceneRequest(
      pb::StuckSignal(), construction_zones_inlane_states_, ego_params_,
      planner_object_map_, unstuck_seed,
      /*traffic_light_info=*/{}, ra_creep_around_meta_, /*ego_speed_mps=*/0.0,
      /*are_all_traj_without_much_progress=*/false,
      /*is_in_autonomous_driving=*/true,
      /*optional_stuck_debug=*/optional_stuck_debug_);
  ASSERT_TRUE(optional_request.has_value());
  EXPECT_THAT(*optional_request, EqualsProto(R"pb(
    cancel_previous_stuck_request: true
  )pb"));
}

class StucSceneRequestObsoleteTest : public testing::Test {
 protected:
  void SetUp() override {
    google::protobuf::TextFormat::ParseFromString(
        R"pb(
          object_type: VEHICLE
          center_x: 10.0
          center_y: 0.75
          center_z: 0.0
          width: 3.0
          length: 10.0
          height: 3.5
        )pb",
        agent_proto_.mutable_tracked_object());
    const int64_t timestamp = 0;
    const TrafficParticipantPose pose_at_plan_init_ts(
        timestamp, agent_proto_.tracked_object());
    planner_object_map_.emplace(
        kObjectId_1,
        PlannerObject{agent_proto_, timestamp, pose_at_plan_init_ts});

    google::protobuf::TextFormat::ParseFromString(
        R"pb(
          questions: CREEP_AROUND
          creep_around_request: {
            creep_around_objects: { object_id: 1 object_type: kTrackedObject }
          }
        )pb",
        &stuck_scene_request_);
  }
  prediction::pb::Agent agent_proto_;
  std::unordered_map<ObjectId, PlannerObject> planner_object_map_;
  pb::StuckSceneRequest stuck_scene_request_;
  std::vector<ConstructionZoneInLaneState> construction_zones_inlane_states_;
};

TEST_F(StucSceneRequestObsoleteTest, ObjectFarAwayTest) {
  planner_object_map_.find(kObjectId_1)->second.set_l2_distance_to_ego_m(30);
  EXPECT_TRUE(IsStuckSceneRequestObsolete(
      stuck_scene_request_, construction_zones_inlane_states_,
      planner_object_map_, /*ego_arclength_m=*/0.0));
}

TEST_F(StucSceneRequestObsoleteTest, ObjectCloseByTest) {
  planner_object_map_.find(kObjectId_1)->second.set_l2_distance_to_ego_m(10);
  EXPECT_FALSE(IsStuckSceneRequestObsolete(
      stuck_scene_request_, construction_zones_inlane_states_,
      planner_object_map_, /*ego_arclength_m=*/0.0));
}

TEST_F(StucSceneRequestObsoleteTest, ObjectNotFoundTest) {
  planner_object_map_.clear();
  EXPECT_TRUE(IsStuckSceneRequestObsolete(
      stuck_scene_request_, construction_zones_inlane_states_,
      planner_object_map_, /*ego_arclength_m=*/0.0));
}

TEST_F(StuckUnderstandingUtilsTest, IsCreepDistanceTooShort_BaseTest) {
  // Set up constraints.
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constraint_2.obj_id = 2;
  constexpr double small_start_x = 14.0;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), small_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.0;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::YIELD};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);
  planner_object_map_.find(constraint_2.obj_id)
      ->second.set_is_primary_stationary(true);

  EXPECT_TRUE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

TEST_F(StuckUnderstandingUtilsTest,
       IsCreepDistanceTooShort_ConstraintIsFarTest) {
  // Set up constraints.
  // Set constraint 2 to start x to be larger than threshold.
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constraint_2.obj_id = 2;
  constexpr double large_start_x = 15.1;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), large_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.0;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::YIELD};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);
  planner_object_map_.find(constraint_2.obj_id)
      ->second.set_is_primary_stationary(true);

  EXPECT_FALSE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

TEST_F(StuckUnderstandingUtilsTest,
       IsCreepDistanceTooShort_ConstraintIsNotStationaryTest) {
  // Set up constraints.
  // Set constraint 2 to be not stationary
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constraint_2.obj_id = 2;
  constexpr double small_start_x = 14.0;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), small_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.0;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::YIELD};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);
  planner_object_map_.find(constraint_2.obj_id)
      ->second.set_is_primary_stationary(false);

  EXPECT_FALSE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

TEST_F(StuckUnderstandingUtilsTest,
       IsCreepDistanceTooShort_ConstraintNotInMiddleTest) {
  // Set up constraints.
  // Set constraint 2 to have abs lat gap larger than 0.0.
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constraint_2.obj_id = 2;
  constexpr double small_start_x = 14.0;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), small_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.1;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::YIELD};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);
  planner_object_map_.find(constraint_2.obj_id)
      ->second.set_is_primary_stationary(true);

  EXPECT_FALSE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

TEST_F(StuckUnderstandingUtilsTest,
       IsCreepDistanceTooShort_ConstraintNotSpeedObjectTest) {
  // Set up constraints.
  // Set constraint 2 to be a non-speed-object type.
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::STOP_POINT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constexpr double small_start_x = 14.0;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), small_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.0;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::YIELD};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);

  EXPECT_FALSE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

TEST_F(StuckUnderstandingUtilsTest,
       IsCreepDistanceTooShort_ConstraintNotYieldTest) {
  // Set up constraints.
  // Set constraint 2 to be non-yield constraint.
  speed::Constraint constraint_1(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"123");
  constraint_1.obj_id = 1;
  constexpr double kStartTime = 1.0;
  constraint_1.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), /*start_x=*/3.0,
       /*end_x=*/10.0}));
  constraint_1.states.back().abs_lat_gap = 0.35;

  speed::Constraint constraint_2(speed::pb::Constraint::SPEED_OBJECT,
                                 /*fence_type=*/speed::pb::kLead,
                                 /*reasoner_id=*/"unit_test",
                                 /*constraint_id=*/"124");
  constraint_2.obj_id = 2;
  constexpr double small_start_x = 14.0;
  constraint_2.states.push_back(speed::ConstraintState(
      {kStartTime, std::numeric_limits<double>::infinity(), small_start_x,
       /*end_x=*/10.0}));
  constraint_2.states.back().abs_lat_gap = 0.0;

  speed::ConstraintResult constraint_result;
  constraint_result.constraints = {constraint_1, constraint_2};
  constraint_result.decisions = {speed::pb::SpeedDecision::YIELD,
                                 speed::pb::SpeedDecision::PASS};
  speed::SpeedSearchResult speed_search_result(
      speed::pb::SearchResult_Enum_FAIL_AT_LOW_DISCOMFORT,
      speed::pb::ConflictResolvingType_Enum_NA, speed::pb::LOWEST_DISCOMFORT,
      /*selected_discomfort=*/-std::numeric_limits<double>::infinity(),
      /*selected_discomfort_ix=*/-1,
      /*earliest_brake_time_ix=*/-1,
      /*profile=*/speed::Profile(),
      /*tree_profile=*/std::nullopt, speed::pb::SpeedSolverLcGuideSeed(),
      /*tree_switch_time_ix=*/std::numeric_limits<int>::max(),
      speed::pb::TreeSearchType_Enum_kNone, std::move(constraint_result),
      /*planning_time_horizon_in_second=*/constants::kTrajectoryHorizonInSec,
      /*gap_info=*/std::nullopt,
      /*stay_stop_info=*/std::nullopt);
  speed::SpeedResult speed_result(std::move(speed_search_result),
                                  speed::SpeedPipelineMeta());
  // Set up planner object.
  planner_object_map_.find(constraint_1.obj_id)
      ->second.set_is_primary_stationary(true);
  planner_object_map_.find(constraint_2.obj_id)
      ->second.set_is_primary_stationary(true);

  EXPECT_FALSE(IsCreepDistanceTooShort(speed_result, planner_object_map_));
}

}  // namespace
}  // namespace planner
