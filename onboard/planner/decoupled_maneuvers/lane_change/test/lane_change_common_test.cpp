#include "planner/decoupled_maneuvers/lane_change/lane_change_common.h"

#include <cmath>
#include <gtest/gtest.h>
#include <sstream>

#include "geometry/model/point_2d.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "planner/behavior/util/lane_sequence/test/test_utility.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner_protos/hazardous_state.pb.h"
#include "planner_protos/lane_change.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "pnc_map_service/test/map_test_util.h"

namespace planner {
namespace {

class LaneChangeCommonTestFixture : public testing::Test,
                                    public speed::ReasoningTestFixture {
 public:
  LaneChangeCommonTestFixture() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};

TEST(LaneChangeCommonTest, ComputeLaneChangeStateForHomotopy) {
  using pb::LaneChangeState::LANE_CHANGE_STATE_ABORT;
  using pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;
  using pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
  using pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/true,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/true));

  EXPECT_EQ(LANE_CHANGE_STATE_PREPARATION,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/true,
                /*should_latch_abort_state=*/true));

  EXPECT_EQ(LANE_CHANGE_STATE_NONE,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/true,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_NONE,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/true,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_fake_lc_signal=*/true,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/true, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/true));

  EXPECT_EQ(LANE_CHANGE_STATE_PREPARATION,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/true, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_NONE,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/true,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/true, /*is_lc_signal_from_seed=*/true,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_IN_PROGRESS,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_IN_PROGRESS,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_ABORT,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_ABORT,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_IN_PROGRESS,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_IN_PROGRESS,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_IN_PROGRESS,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_IN_PROGRESS,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_IN_PROGRESS,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/true,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_NONE,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_IN_PROGRESS,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/true,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_PREPARATION,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_PREPARATION,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_IN_PROGRESS,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_PREPARATION,
                /*is_current_homotopy_lane_change=*/true,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));

  EXPECT_EQ(LANE_CHANGE_STATE_PREPARATION,
            ComputeLaneChangeStateForHomotopy(
                LANE_CHANGE_STATE_NONE,
                /*is_current_homotopy_lane_change=*/false,
                /*can_ego_finish_lane_change=*/false,
                /*is_ego_fully_in_source_lane=*/false,
                /*is_ego_slow=*/false, /*is_lc_signal_from_seed=*/false,
                /*should_latch_abort_state=*/false));
}

TEST(LaneChangeCommonTest, ComputeLaneChangeInProgressInfoForHomotopy) {
  using pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;
  using pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
  using pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
  // Test no in-progress info.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_PREPARATION,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/0.5,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_PREPARATION,
        /*last_in_progress_timestamp=*/std::nullopt,
        /*last_turn_light_duration_before_in_progress=*/std::nullopt,
        /*last_speed_discomfort_for_progress=*/std::nullopt,
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_FALSE(in_progress_timestamp.has_value());
    EXPECT_FALSE(turn_light_duration_before_in_progress.has_value());
    EXPECT_FALSE(speed_discomfort_for_progress.has_value());

    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_NONE,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/2.0,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*last_in_progress_timestamp=*/std::make_optional(50),
        /*last_turn_light_duration_before_in_progress=*/std::make_optional(0.5),
        /*last_speed_discomfort_for_progress=*/std::make_optional(0.5),
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_FALSE(in_progress_timestamp.has_value());
    EXPECT_FALSE(turn_light_duration_before_in_progress.has_value());
    EXPECT_FALSE(speed_discomfort_for_progress.has_value());
  }

  // Test entering in-progress state from preparation state in the current
  // frame.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/0.5,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_PREPARATION,
        /*last_in_progress_timestamp=*/std::nullopt,
        /*last_turn_light_duration_before_in_progress=*/std::nullopt,
        /*last_speed_discomfort_for_progress=*/std::nullopt,
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_TRUE(in_progress_timestamp.has_value());
    EXPECT_TRUE(turn_light_duration_before_in_progress.has_value());
    EXPECT_EQ(100, in_progress_timestamp.value());
    EXPECT_DOUBLE_EQ(0.5, turn_light_duration_before_in_progress.value());
    EXPECT_FALSE(speed_discomfort_for_progress.has_value());
  }

  // Test entering in-progress state from none state in the current frame.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/0.0,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_NONE,
        /*last_in_progress_timestamp=*/std::nullopt,
        /*last_turn_light_duration_before_in_progress=*/std::nullopt,
        /*last_speed_discomfort_for_progress=*/std::nullopt,
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_TRUE(in_progress_timestamp.has_value());
    EXPECT_TRUE(turn_light_duration_before_in_progress.has_value());
    EXPECT_EQ(100, in_progress_timestamp.value());
    EXPECT_DOUBLE_EQ(0.0, turn_light_duration_before_in_progress.value());
    EXPECT_FALSE(speed_discomfort_for_progress.has_value());
  }

  // Test entering in-progress state in the previous frame with discomfort 0.5.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/3.0,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*last_in_progress_timestamp=*/std::make_optional(50),
        /*last_turn_light_duration_before_in_progress=*/std::make_optional(1.0),
        /*last_speed_discomfort_for_progress=*/std::make_optional(0.5),
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_TRUE(in_progress_timestamp.has_value());
    EXPECT_TRUE(turn_light_duration_before_in_progress.has_value());
    EXPECT_EQ(50, in_progress_timestamp.value());
    EXPECT_DOUBLE_EQ(1.0, turn_light_duration_before_in_progress.value());
    EXPECT_TRUE(speed_discomfort_for_progress.has_value());
    EXPECT_DOUBLE_EQ(0.5, speed_discomfort_for_progress.value());
  }

  // Test entering in-progress state in the previous frame with discomfort 0.75.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/3.0,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*last_in_progress_timestamp=*/std::make_optional(50),
        /*last_turn_light_duration_before_in_progress=*/std::make_optional(1.0),
        /*last_speed_discomfort_for_progress=*/std::make_optional(0.75),
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_TRUE(in_progress_timestamp.has_value());
    EXPECT_TRUE(turn_light_duration_before_in_progress.has_value());
    EXPECT_EQ(50, in_progress_timestamp.value());
    EXPECT_DOUBLE_EQ(1.0, turn_light_duration_before_in_progress.value());
    EXPECT_TRUE(speed_discomfort_for_progress.has_value());
    EXPECT_DOUBLE_EQ(0.5, speed_discomfort_for_progress.value());
  }

  // Test entering in-progress state in the previous frame with discomfort 1.0.
  {
    std::optional<int64_t> in_progress_timestamp;
    std::optional<double> turn_light_duration_before_in_progress;
    std::optional<double> speed_discomfort_for_progress;
    ComputeLaneChangeInProgressInfoForHomotopy(
        /*current_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*plan_init_state_timestamp=*/100,
        /*turn_light_duration=*/3.0,
        /*last_selected_lane_change_state=*/LANE_CHANGE_STATE_IN_PROGRESS,
        /*last_in_progress_timestamp=*/std::make_optional(50),
        /*last_turn_light_duration_before_in_progress=*/std::make_optional(1.0),
        /*last_speed_discomfort_for_progress=*/std::make_optional(1.0),
        in_progress_timestamp, turn_light_duration_before_in_progress,
        speed_discomfort_for_progress);
    EXPECT_TRUE(in_progress_timestamp.has_value());
    EXPECT_TRUE(turn_light_duration_before_in_progress.has_value());
    EXPECT_EQ(50, in_progress_timestamp.value());
    EXPECT_DOUBLE_EQ(1.0, turn_light_duration_before_in_progress.value());
    EXPECT_TRUE(speed_discomfort_for_progress.has_value());
    EXPECT_DOUBLE_EQ(0.75, speed_discomfort_for_progress.value());
  }
}

TEST_F(LaneChangeCommonTestFixture, IsLaneChangeReady) {
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      {128229, 128253, 128237, 128291, 128269, 128327, 128323},
      {pb::LaneChangeMode::LEFT_LANE_CHANGE}, {{128327, 128323}}, 128229,
      map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);

  [[maybe_unused]] const double lane_change_region_length =
      lane_change_instance.source_region().nominal_path().GetTotalArcLength();
  constexpr double kUrgencyScoreForNoTurnSignal = 0.8;
  constexpr double kUrgencyScoreForMaxTurnSignal = 0.0;
  constexpr double kMaxTurnLightDuration = 2.0;
  constexpr int64_t kPlanInitTimestampInMSec = 10000;

  // Test no lane change.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(0);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        /*turn_light_duration_for_lc=*/0.0, /*generic_urgency_score=*/0.0));
  }

  // Test min urgency score.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kMaxTurnLightDuration + math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
    EXPECT_FALSE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kMaxTurnLightDuration - math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
  }

  // Test abort state.
  {
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_FALSE(IsLaneChangeReady(
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kMaxTurnLightDuration - math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
    EXPECT_TRUE(IsLaneChangeReady(
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kMaxTurnLightDuration - math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
  }

  // Test max urgency score.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        /*turn_light_duration_for_lc=*/0.0, kUrgencyScoreForNoTurnSignal));
  }

  // Test between min distance and max distance.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kMaxTurnLightDuration,
        0.5 * (kUrgencyScoreForMaxTurnSignal + kUrgencyScoreForMaxTurnSignal)));
    EXPECT_FALSE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        /*turn_light_duration_for_lc=*/0.0,
        0.5 * (kUrgencyScoreForMaxTurnSignal + kUrgencyScoreForMaxTurnSignal)));
  }

  // Test stuck signal.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    pb::LaneChangeMetadata metadata;
    pb::StuckSignal stuck_signal;
    stuck_signal.set_last_stuck_timestamp(static_cast<int64_t>(
        kPlanInitTimestampInMSec - kMaxTurnLightDuration * 1000 + 1));
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, stuck_signal, kPlanInitTimestampInMSec,
        /*turn_light_duration_for_lc=*/0.0, kUrgencyScoreForMaxTurnSignal));

    stuck_signal.set_last_stuck_timestamp(static_cast<int64_t>(
        kPlanInitTimestampInMSec - kMaxTurnLightDuration * 1000 - 1));
    EXPECT_FALSE(IsLaneChangeReady(
        prev_lane_change_state, metadata,
        /*elc_decision=*/{}, stuck_signal, kPlanInitTimestampInMSec,
        /*turn_light_duration_for_lc=*/0.0, kUrgencyScoreForMaxTurnSignal));
  }

  // Test elc decision.
  {
    const pb::LaneChangeState& prev_lane_change_state =
        planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    constexpr double kELCTurnSignalMaxDurationInSec = 0.5;
    pb::LaneChangeMetadata metadata;
    pb::ElectiveLaneChangeDecision elc_decision;
    elc_decision.set_is_triggered(true);
    metadata.set_consecutive_lane_change_count(1);
    EXPECT_TRUE(IsLaneChangeReady(
        prev_lane_change_state, metadata, elc_decision, /*stuck_signal=*/{},
        kPlanInitTimestampInMSec,
        kELCTurnSignalMaxDurationInSec + math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
    EXPECT_FALSE(IsLaneChangeReady(
        prev_lane_change_state, metadata, elc_decision, /*stuck_signal=*/{},
        kPlanInitTimestampInMSec,
        kELCTurnSignalMaxDurationInSec - math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));

    EXPECT_TRUE(IsLaneChangeReady(prev_lane_change_state, metadata,
                                  elc_decision,
                                  /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
                                  0.0, kUrgencyScoreForNoTurnSignal));

    elc_decision.set_is_triggered(false);
    EXPECT_FALSE(IsLaneChangeReady(
        prev_lane_change_state, metadata, elc_decision,
        /*stuck_signal=*/{}, kPlanInitTimestampInMSec,
        kELCTurnSignalMaxDurationInSec + math::constants::kEpsilon,
        kUrgencyScoreForMaxTurnSignal));
  }
}

TEST_F(LaneChangeCommonTestFixture,
       CanEgoFinishLaneChangeForConsecutiveLaneChange) {
  // Original function constants.
  constexpr double kShortLaneChangeRawMaxThresholdInMeter = 60.0;
  constexpr double kShortLaneChangeRawMinThresholdInMeter = 30.0;
  constexpr double kMinFrontCornerThresholdInMeter = 1.3;
  constexpr double kMaxFrontCornerThresholdInMeter = 1.7;

  SetEgoPose(/*lane_id=*/128327, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      /*lane_ids=*/{128327, 128323},
      /*lane_change_modes=*/{pb::LaneChangeMode::LEFT_LANE_CHANGE},
      /*lane_change_instances=*/{{128327, 128323}}, /*current_lane_id=*/128327,
      map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);

  const pnc_map::Lane* source_lane = map_test_util().GetLane(128327);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util().GetLane(128323);
  ASSERT_NE(target_lane, nullptr);

  const math::geometry::Point2d& cross_curve_point =
      source_lane->left_marking()->line().GetInterp(/*arc_length=*/5.0);
  const double heading_at_point =
      source_lane->GetLaneDirection(cross_curve_point);
  const double normal_heading_at_point_toward_target_lane =
      heading_at_point + M_PI_2;

  const RobotStateSnapshot snapshot_on_cross_curve =
      test_utility::ConstructRobotStateSnapshot(
          cross_curve_point.x(), cross_curve_point.y(),
          /*speed=*/0.0, /*heading=*/heading_at_point);
  const double ego_width = snapshot_on_cross_curve.car_model_with_shape()
                               .shape_measurement()
                               .width();

  constexpr double kError = 0.1;
  constexpr int kNumLaneChange = 2;

  // Construct snapshot with front bumper corner just below min threshold.
  const double just_below_min_threshold_shift =
      kMinFrontCornerThresholdInMeter - kError - ego_width * 0.5;
  const double just_below_min_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_below_min_threshold_shift;
  const double just_below_min_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_below_min_threshold_shift;
  const RobotStateSnapshot just_below_min_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_below_min_threshold_x,
                                                just_below_min_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just above min threshold.
  const double just_above_min_threshold_shift =
      kMinFrontCornerThresholdInMeter + kError - ego_width * 0.5;
  const double just_above_min_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_above_min_threshold_shift;
  const double just_above_min_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_above_min_threshold_shift;
  const RobotStateSnapshot just_above_min_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_above_min_threshold_x,
                                                just_above_min_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just below max threshold.
  const double just_below_max_threshold_shift =
      kMaxFrontCornerThresholdInMeter - kError - ego_width * 0.5;
  const double just_below_max_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_below_max_threshold_shift;
  const double just_below_max_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_below_max_threshold_shift;
  const RobotStateSnapshot just_below_max_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_below_max_threshold_x,
                                                just_below_max_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just above max threshold.
  const double just_above_max_threshold_shift =
      kMaxFrontCornerThresholdInMeter + kError - ego_width * 0.5;
  const double just_above_max_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_above_max_threshold_shift;
  const double just_above_max_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_above_max_threshold_shift;
  const RobotStateSnapshot just_above_max_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_above_max_threshold_x,
                                                just_above_max_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);
  std::ostringstream debug_oss;
  // Test consecutive lane change with large remaining distance.
  {
    pb::LaneChangeMetadata lane_change_metadata;
    lane_change_metadata.set_consecutive_lane_change_count(kNumLaneChange);
    lane_change_metadata.set_remaining_distance_for_consecutive_lane_change(
        kNumLaneChange * kShortLaneChangeRawMaxThresholdInMeter + kError);
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test consecutive lane change with small remaining distance.
  {
    pb::LaneChangeMetadata lane_change_metadata;
    lane_change_metadata.set_consecutive_lane_change_count(kNumLaneChange);
    lane_change_metadata.set_remaining_distance_for_consecutive_lane_change(
        kNumLaneChange * kShortLaneChangeRawMinThresholdInMeter - kError);
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test consecutive lane change with medium remaining distance.
  {
    pb::LaneChangeMetadata lane_change_metadata;
    lane_change_metadata.set_consecutive_lane_change_count(kNumLaneChange);
    const double medium_remaining_distance_per_lc =
        0.5 * (kShortLaneChangeRawMinThresholdInMeter +
               kShortLaneChangeRawMaxThresholdInMeter);
    lane_change_metadata.set_remaining_distance_for_consecutive_lane_change(
        kNumLaneChange * medium_remaining_distance_per_lc);
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    LOG(INFO) << __func__ << "\n" << debug_oss.str();
  }
}

TEST_F(LaneChangeCommonTestFixture,
       CanEgoFinishLaneChangeWhenCloseToLaneSequenceEnd) {
  // Original function constants.
  constexpr double kFrontBumperToLaneSequenceEndRawMinThresholdInMeter = 3.0;
  constexpr double kFrontBumperToLaneSequenceEndRawMaxThresholdInMeter = 15.0;
  constexpr double kFrontBumperToLaneSequenceEndDurationMinThresholdInSec = 2.0;
  constexpr double kFrontBumperToLaneSequenceEndDurationMaxThresholdInSec = 3.5;
  constexpr double kMinFrontCornerThresholdInMeter = 1.3;
  constexpr double kMaxFrontCornerThresholdInMeter = 1.7;

  SetEgoPose(/*lane_id=*/128327, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      {128327, 128323}, {pb::LaneChangeMode::LEFT_LANE_CHANGE},
      {{128327, 128323}}, 128327, map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);

  const pnc_map::Lane* source_lane = map_test_util().GetLane(128327);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util().GetLane(128323);
  ASSERT_NE(target_lane, nullptr);

  const math::geometry::Point2d& cross_curve_point =
      source_lane->left_marking()->line().GetInterp(/*arc_length=*/5.0);
  const double heading_at_point =
      source_lane->GetLaneDirection(cross_curve_point);
  const double normal_heading_at_point_toward_target_lane =
      heading_at_point + M_PI_2;

  const RobotStateSnapshot snapshot_on_cross_curve =
      test_utility::ConstructRobotStateSnapshot(
          cross_curve_point.x(), cross_curve_point.y(),
          /*speed=*/0.0, /*heading=*/heading_at_point);
  const double ego_width = snapshot_on_cross_curve.car_model_with_shape()
                               .shape_measurement()
                               .width();

  constexpr double kError = 0.1;

  // Construct snapshot with front bumper corner just below min threshold.
  const double just_below_min_threshold_shift =
      kMinFrontCornerThresholdInMeter - kError - ego_width * 0.5;
  const double just_below_min_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_below_min_threshold_shift;
  const double just_below_min_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_below_min_threshold_shift;
  const RobotStateSnapshot just_below_min_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_below_min_threshold_x,
                                                just_below_min_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just above min threshold.
  const double just_above_min_threshold_shift =
      kMinFrontCornerThresholdInMeter + kError - ego_width * 0.5;
  const double just_above_min_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_above_min_threshold_shift;
  const double just_above_min_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_above_min_threshold_shift;
  const RobotStateSnapshot just_above_min_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_above_min_threshold_x,
                                                just_above_min_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just below max threshold.
  const double just_below_max_threshold_shift =
      kMaxFrontCornerThresholdInMeter - kError - ego_width * 0.5;
  const double just_below_max_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_below_max_threshold_shift;
  const double just_below_max_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_below_max_threshold_shift;
  const RobotStateSnapshot just_below_max_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_below_max_threshold_x,
                                                just_below_max_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  // Construct snapshot with front bumper corner just above max threshold.
  const double just_above_max_threshold_shift =
      kMaxFrontCornerThresholdInMeter + kError - ego_width * 0.5;
  const double just_above_max_threshold_x =
      snapshot_on_cross_curve.x() +
      std::cos(normal_heading_at_point_toward_target_lane) *
          just_above_max_threshold_shift;
  const double just_above_max_threshold_y =
      snapshot_on_cross_curve.y() +
      std::sin(normal_heading_at_point_toward_target_lane) *
          just_above_max_threshold_shift;
  const RobotStateSnapshot just_above_max_threshold_snapshot =
      test_utility::ConstructRobotStateSnapshot(just_above_max_threshold_x,
                                                just_above_max_threshold_y,
                                                /*speed=*/0.0,
                                                /*heading=*/heading_at_point);

  const double target_region_length =
      lane_change_instance.target_region().nominal_path().GetTotalArcLength();
  const auto& ego_shape =
      world_model_->robot_state().car_model_with_shape().shape_measurement();
  const double ego_ra_to_fb =
      ego_shape.length() - ego_shape.rear_bumper_to_rear_axle();

  // Construct a default metadata without short consecutive lane change.
  pb::LaneChangeMetadata default_lane_change_metadata;
  default_lane_change_metadata.set_consecutive_lane_change_count(1);
  default_lane_change_metadata
      .set_remaining_distance_for_consecutive_lane_change(200.0);

  std::ostringstream debug_oss;
  // Test lane change just above min threshold to lane sequence end.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length -
        (kFrontBumperToLaneSequenceEndRawMinThresholdInMeter + ego_ra_to_fb) -
        kError);
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test lane change between min & max threshold to lane sequence end.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length -
        (0.5 * (kFrontBumperToLaneSequenceEndRawMinThresholdInMeter +
                kFrontBumperToLaneSequenceEndRawMaxThresholdInMeter) +
         ego_ra_to_fb));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test lane change just below max threshold to lane sequence end.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length -
        (kFrontBumperToLaneSequenceEndRawMaxThresholdInMeter + ego_ra_to_fb) +
        kError);
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test ego with speed.
  constexpr double kEgoSpeed = 8.0;
  const RobotStateSnapshot just_below_min_threshold_snapshot_w_speed =
      test_utility::ConstructRobotStateSnapshot(
          just_below_min_threshold_x, just_below_min_threshold_y, kEgoSpeed,
          /*heading=*/heading_at_point);
  const RobotStateSnapshot just_above_min_threshold_snapshot_w_speed =
      test_utility::ConstructRobotStateSnapshot(
          just_above_min_threshold_x, just_above_min_threshold_y, kEgoSpeed,
          /*heading=*/heading_at_point);
  const RobotStateSnapshot just_below_max_threshold_snapshot_w_speed =
      test_utility::ConstructRobotStateSnapshot(
          just_below_max_threshold_x, just_below_max_threshold_y, kEgoSpeed,
          /*heading=*/heading_at_point);
  const RobotStateSnapshot just_above_max_threshold_snapshot_w_speed =
      test_utility::ConstructRobotStateSnapshot(
          just_above_max_threshold_x, just_above_max_threshold_y, kEgoSpeed,
          /*heading=*/heading_at_point);

  const double fb_to_end_min_threshold_with_speed =
      kFrontBumperToLaneSequenceEndDurationMinThresholdInSec * kEgoSpeed;
  const double fb_to_end_max_threshold_with_speed =
      kFrontBumperToLaneSequenceEndDurationMaxThresholdInSec * kEgoSpeed;

  // Test lane change just above min threshold to lane sequence end when ego has
  // speed.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length -
        (fb_to_end_min_threshold_with_speed + ego_ra_to_fb) - kError);
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test lane change between min & max threshold to lane sequence end when ego
  // has speed.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length - (0.5 * (fb_to_end_min_threshold_with_speed +
                                       fb_to_end_max_threshold_with_speed) +
                                ego_ra_to_fb));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }

  // Test lane change just below max threshold to lane sequence end when ego has
  // speed.
  {
    pb::LaneChangeMetadata lane_change_metadata = default_lane_change_metadata;
    lane_change_metadata.set_ego_ra_arc_length_on_target_region(
        target_region_length -
        (fb_to_end_max_threshold_with_speed + ego_ra_to_fb) + kError);
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_max_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_TRUE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_above_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
    EXPECT_FALSE(CanEgoFinishLaneChange(
        /*global_route_solution=*/nullptr, lane_change_instance,
        just_below_min_threshold_snapshot_w_speed, lane_change_metadata,
        /*region_infos=*/{}, /*is_lc_backup_sequence=*/false, debug_oss));
  }
}

TEST_F(LaneChangeCommonTestFixture, PopulateEgoArcLengthOnRegions) {
  SetEgoPose(/*lane_id=*/128271, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      /*lane_ids=*/{128271, 128323, 128321},
      /*lane_change_modes=*/{pb::LaneChangeMode::LEFT_LANE_CHANGE},
      /*lane_change_instances=*/{{128323, 128321}}, /*current_lane_id=*/128271,
      map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);
  lane_change_instance.SetTargetRegionMarkingSequence({});

  constexpr double kError = 0.1;

  const pnc_map::Lane* lane_before_lc = map_test_util().GetLane(128271);
  const double lane_before_lc_length = lane_before_lc->length();
  const pnc_map::Lane* source_lane = map_test_util().GetLane(128323);
  [[maybe_unused]] const double source_lane_length = source_lane->length();
  const pnc_map::Lane* target_lane = map_test_util().GetLane(128321);
  [[maybe_unused]] const double target_lane_length = target_lane->length();

  // Test ego_ra_arc_length_on_source/target_region.
  // Test a point before LC region.
  {
    constexpr double kArcLengthBeforeLCRegionInMeter = 1.0;
    const math::geometry::Point2d& ego_ra_point =
        lane_before_lc->center_line().GetInterp(
            lane_before_lc_length - kArcLengthBeforeLCRegionInMeter);

    pb::LaneChangeMetadata metadata;
    PopulateEgoArcLengthOnRegions(lane_change_instance, ego_ra_point, metadata);

    EXPECT_NEAR(-kArcLengthBeforeLCRegionInMeter,
                metadata.ego_ra_arc_length_on_source_region(), kError);
    EXPECT_NEAR(-kArcLengthBeforeLCRegionInMeter,
                metadata.ego_ra_arc_length_on_target_region(), kError);
  }

  // Test a point in LC region.
  {
    constexpr double kArcLengthInLCRegionInMeter = 1.0;
    const math::geometry::Point2d& ego_ra_point =
        source_lane->center_line().GetInterp(kArcLengthInLCRegionInMeter);

    pb::LaneChangeMetadata metadata;
    PopulateEgoArcLengthOnRegions(lane_change_instance, ego_ra_point, metadata);

    EXPECT_NEAR(kArcLengthInLCRegionInMeter,
                metadata.ego_ra_arc_length_on_source_region(), kError);
    EXPECT_NEAR(kArcLengthInLCRegionInMeter,
                metadata.ego_ra_arc_length_on_target_region(), kError);
  }
}

TEST_F(LaneChangeCommonTestFixture, PopulateConsecutiveLaneChangeInfo) {
  SetEgoPose(/*lane_id=*/128271, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);
  pnc_map::JointPncMapService joint_pnc_map_service(
      map_test_util().pnc_map_service());
  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      /*lane_ids=*/{128271, 128323, 128321},
      /*lane_change_modes=*/{pb::LaneChangeMode::LEFT_LANE_CHANGE},
      /*lane_change_instances=*/{{128323, 128321}}, /*current_lane_id=*/128271,
      map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);
  lane_change_instance.SetTargetRegionMarkingSequence({});

  constexpr double kError = 0.1;

  const pnc_map::Lane* lane_before_lc = map_test_util().GetLane(128271);
  const double lane_before_lc_length = lane_before_lc->length();
  const pnc_map::Lane* source_lane = map_test_util().GetLane(128323);
  const double source_lane_length = source_lane->length();
  const pnc_map::Lane* target_lane = map_test_util().GetLane(128321);
  const double target_lane_length = target_lane->length();

  // Test consecutive lane change data.
  const pnc_map::Lane* source_lane_2 = map_test_util().GetLane(128411);
  const double source_lane_2_length = source_lane_2->length();
  const pnc_map::Lane* target_lane_2 = map_test_util().GetLane(128409);
  const double target_lane_2_length = target_lane_2->length();
  {
    pb::PlanningSegmentSequence planning_segment_sequence;

    // Add LF segment 1: on lane 128271.
    constexpr double kArcLengthBeforeLCRegionInMeter = 1.0;
    {
      pb::PlanningSegment& planning_segment =
          *planning_segment_sequence.add_planning_segments();
      auto* segment = planning_segment.mutable_lane_follow_segment();
      segment->mutable_lf_segment()->set_lane_id(128271);
      segment->mutable_lf_segment()->set_start_arclength(
          lane_before_lc_length - kArcLengthBeforeLCRegionInMeter);
      segment->mutable_lf_segment()->set_end_arclength(lane_before_lc_length);
    }

    // Add LF segment 2: on lane 128323.
    constexpr double kLfArcLengthOnLCRegionInMeter = 10.0;
    {
      pb::PlanningSegment& planning_segment =
          *planning_segment_sequence.add_planning_segments();
      auto* segment = planning_segment.mutable_lane_follow_segment();
      segment->mutable_lf_segment()->set_lane_id(128323);
      segment->mutable_lf_segment()->set_start_arclength(0.0);
      segment->mutable_lf_segment()->set_end_arclength(
          kLfArcLengthOnLCRegionInMeter);
    }

    // Add LC segment 1: lane 128323 -> lane 128321
    {
      pb::PlanningSegment& planning_segment =
          *planning_segment_sequence.add_planning_segments();
      auto* segment = planning_segment.mutable_lane_change_segment();
      auto* source_segment = segment->add_lc_source_segments();
      source_segment->set_lane_id(128323);
      source_segment->set_start_arclength(kLfArcLengthOnLCRegionInMeter);
      source_segment->set_end_arclength(source_lane_length);
      auto* target_segment = segment->add_lc_target_segments();
      target_segment->set_lane_id(128321);
      target_segment->set_start_arclength(kLfArcLengthOnLCRegionInMeter);
      target_segment->set_end_arclength(target_lane_length);
    }

    // Add LC segment 2: lane 128411 -> lane 128409
    {
      pb::PlanningSegment& planning_segment =
          *planning_segment_sequence.add_planning_segments();
      auto* segment = planning_segment.mutable_lane_change_segment();
      auto* source_segment = segment->add_lc_source_segments();
      source_segment->set_lane_id(128411);
      source_segment->set_start_arclength(0.0);
      source_segment->set_end_arclength(source_lane_2_length);
      auto* target_segment = segment->add_lc_target_segments();
      target_segment->set_lane_id(128409);
      target_segment->set_start_arclength(0.0);
      target_segment->set_end_arclength(target_lane_2_length);
    }

    // Test when ego is at the start of the planning segment sequence.
    {
      const math::geometry::Point2d& point =
          lane_before_lc->center_line().GetInterp(
              lane_before_lc_length - kArcLengthBeforeLCRegionInMeter);

      pb::LaneChangeMetadata metadata;
      PopulateConsecutiveLaneChangeInfo(
          joint_pnc_map_service, planning_segment_sequence, point, metadata);
      EXPECT_EQ(2, metadata.consecutive_lane_change_count());
      const double expected_remaining_distance =
          target_lane_length + target_lane_2_length +
          kArcLengthBeforeLCRegionInMeter;
      EXPECT_NEAR(expected_remaining_distance,
                  metadata.remaining_distance_for_consecutive_lane_change(),
                  kError);
    }

    // Test when ego is inside the LF part of the planning segment sequence.
    {
      constexpr double kArcLengthInSourceLaneLFInMeter = 1.0;
      const math::geometry::Point2d& point =
          source_lane->center_line().GetInterp(kArcLengthInSourceLaneLFInMeter);

      pb::LaneChangeMetadata metadata;
      PopulateConsecutiveLaneChangeInfo(
          joint_pnc_map_service, planning_segment_sequence, point, metadata);
      EXPECT_EQ(2, metadata.consecutive_lane_change_count());
      const double expected_remaining_distance =
          target_lane_length + target_lane_2_length -
          kArcLengthInSourceLaneLFInMeter;
      EXPECT_NEAR(expected_remaining_distance,
                  metadata.remaining_distance_for_consecutive_lane_change(),
                  kError);
    }

    // Test when ego is inside the LC part of the planning segment sequence.
    {
      constexpr double kArcLengthInLCSequenceInMeter = 5.0;
      const math::geometry::Point2d& point =
          source_lane->center_line().GetInterp(kLfArcLengthOnLCRegionInMeter +
                                               kArcLengthInLCSequenceInMeter);

      pb::LaneChangeMetadata metadata;
      PopulateConsecutiveLaneChangeInfo(
          joint_pnc_map_service, planning_segment_sequence, point, metadata);
      EXPECT_EQ(2, metadata.consecutive_lane_change_count());
      const double expected_remaining_distance =
          target_lane_length + target_lane_2_length -
          kLfArcLengthOnLCRegionInMeter - kArcLengthInLCSequenceInMeter;
      EXPECT_NEAR(expected_remaining_distance,
                  metadata.remaining_distance_for_consecutive_lane_change(),
                  kError);
    }
  }
}

TEST_F(LaneChangeCommonTestFixture, PopulateConsecutiveLaneChangeSlowDownInfo) {
  // Original function constants;
  constexpr double kMinEgoSpeedInMps = 2.0;
  constexpr double kLaneChangeDurationInSec = 4.5;
  // Params to adapt the feature of dynamic look ahead time in ref generator.
  constexpr double kMinEgoSpeedForDynamicLookAheadTimeInMps = 3.5;
  constexpr double kLaneChangeDurationForDynamicLookAheadTimeInSec = 4.0;
  const double min_ego_speed =
      FLAGS_planning_enable_dynamic_look_ahead_time_in_ref_generator
          ? kMinEgoSpeedForDynamicLookAheadTimeInMps
          : kMinEgoSpeedInMps;
  const double lane_change_duration =
      FLAGS_planning_enable_dynamic_look_ahead_time_in_ref_generator
          ? kLaneChangeDurationForDynamicLookAheadTimeInSec
          : kLaneChangeDurationInSec;

  constexpr double kIntervalBetweenLaneChangesInSec = 1.0;
  constexpr double kMinAccDuringCurrentLCInMpss = -2.0;

  SetEgoPose(/*lane_id=*/128271, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      /*lane_ids=*/{128271, 128323, 128321},
      /*lane_change_modes=*/{pb::LaneChangeMode::LEFT_LANE_CHANGE},
      /*lane_change_instances=*/{{128323, 128321}}, /*current_lane_id=*/128271,
      map_test_util());
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result_.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result_.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);
  lane_change_instance.SetTargetRegionMarkingSequence({});

  constexpr double kError = 0.1;

  const pnc_map::Lane* lane_before_lc = map_test_util().GetLane(128271);
  [[maybe_unused]] const double lane_before_lc_length =
      lane_before_lc->length();
  const pnc_map::Lane* source_lane = map_test_util().GetLane(128323);
  [[maybe_unused]] const double source_lane_length = source_lane->length();
  const pnc_map::Lane* target_lane = map_test_util().GetLane(128321);
  [[maybe_unused]] const double target_lane_length = target_lane->length();

  // Test no consecutive lane change.
  {
    constexpr double kEgoSpeedInMps = 10.0;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(0);
    metadata.set_remaining_distance_for_consecutive_lane_change(1.0);

    std::ostringstream debug_oss;
    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_DOUBLE_EQ(0.0, metadata.max_longitudinal_span_for_current_lc());
    EXPECT_DOUBLE_EQ(0.0, metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(0.0, metadata.min_acc_for_speed_limit());

    metadata.set_consecutive_lane_change_count(1);

    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_DOUBLE_EQ(0.0, metadata.max_longitudinal_span_for_current_lc());
    EXPECT_DOUBLE_EQ(0.0, metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(0.0, metadata.min_acc_for_speed_limit());
  }

  // Test enough span to do all LCs comfortably.
  {
    constexpr double kEgoSpeedInMps = 10.0;
    constexpr int kNumLCs = 2;
    pb::LaneChangeMetadata metadata;
    metadata.set_consecutive_lane_change_count(kNumLCs);
    metadata.set_remaining_distance_for_consecutive_lane_change(
        kEgoSpeedInMps * lane_change_duration * kNumLCs +
        kEgoSpeedInMps * kIntervalBetweenLaneChangesInSec * (kNumLCs - 1) +
        kError);

    std::ostringstream debug_oss;
    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_DOUBLE_EQ(0.0, metadata.max_longitudinal_span_for_current_lc());
    EXPECT_DOUBLE_EQ(0.0, metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(0.0, metadata.min_acc_for_speed_limit());
  }

  // Test high starting speed.
  {
    constexpr double kEgoSpeedInMps = 20.0;
    constexpr int kNumLCs = 2;
    const double first_lc_end_speed =
        kEgoSpeedInMps + kMinAccDuringCurrentLCInMpss * lane_change_duration;
    const double first_lc_hard_brake_distance =
        0.5 * lane_change_duration * (kEgoSpeedInMps + first_lc_end_speed);
    const double second_lc_hard_brake_distance =
        lane_change_duration * first_lc_end_speed;
    const double interval_distance =
        kIntervalBetweenLaneChangesInSec * first_lc_end_speed;

    pb::LaneChangeMetadata metadata;

    // Test not enough span to do LCs even w/ hard brake.
    metadata.Clear();
    metadata.set_consecutive_lane_change_count(kNumLCs);
    metadata.set_remaining_distance_for_consecutive_lane_change(
        first_lc_hard_brake_distance + second_lc_hard_brake_distance +
        interval_distance - kError);

    std::ostringstream debug_oss;
    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_DOUBLE_EQ(first_lc_hard_brake_distance,
                     metadata.max_longitudinal_span_for_current_lc());
    EXPECT_DOUBLE_EQ(first_lc_end_speed,
                     metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(kMinAccDuringCurrentLCInMpss,
                     metadata.min_acc_for_speed_limit());

    // Test enough span to do LCs w/ some brake.
    metadata.Clear();
    metadata.set_consecutive_lane_change_count(kNumLCs);
    metadata.set_remaining_distance_for_consecutive_lane_change(
        first_lc_hard_brake_distance + second_lc_hard_brake_distance +
        interval_distance + kError);

    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_LT(first_lc_hard_brake_distance + math::constants::kEpsilon,
              metadata.max_longitudinal_span_for_current_lc());
    EXPECT_LT(first_lc_end_speed + math::constants::kEpsilon,
              metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(kMinAccDuringCurrentLCInMpss,
                     metadata.min_acc_for_speed_limit());
  }

  // Test low starting speed.
  {
    constexpr double kEgoSpeedInMps = 6.0;
    constexpr int kNumLCs = 2;
    const double first_lc_end_speed = min_ego_speed;
    const double first_lc_hard_brake_distance =
        0.5 * lane_change_duration * (kEgoSpeedInMps + first_lc_end_speed);
    const double second_lc_hard_brake_distance =
        lane_change_duration * first_lc_end_speed;
    const double interval_distance =
        kIntervalBetweenLaneChangesInSec * first_lc_end_speed;

    pb::LaneChangeMetadata metadata;

    // Test not enough span to do LCs even w/ hard brake.
    metadata.Clear();
    metadata.set_consecutive_lane_change_count(kNumLCs);
    metadata.set_remaining_distance_for_consecutive_lane_change(
        first_lc_hard_brake_distance + second_lc_hard_brake_distance +
        interval_distance - kError);

    std::ostringstream debug_oss;
    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_DOUBLE_EQ(first_lc_hard_brake_distance,
                     metadata.max_longitudinal_span_for_current_lc());
    EXPECT_DOUBLE_EQ(first_lc_end_speed,
                     metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(kMinAccDuringCurrentLCInMpss,
                     metadata.min_acc_for_speed_limit());

    // Test enough span to do LCs w/ some brake.
    metadata.Clear();
    metadata.set_consecutive_lane_change_count(kNumLCs);
    metadata.set_remaining_distance_for_consecutive_lane_change(
        first_lc_hard_brake_distance + second_lc_hard_brake_distance +
        interval_distance + kError);

    PopulateConsecutiveLaneChangeSlowDownInfo(
        lane_change_instance, kEgoSpeedInMps, metadata, debug_oss);
    EXPECT_LT(first_lc_hard_brake_distance + math::constants::kEpsilon,
              metadata.max_longitudinal_span_for_current_lc());
    EXPECT_LT(first_lc_end_speed + math::constants::kEpsilon,
              metadata.speed_limit_at_current_lc_end());
    EXPECT_DOUBLE_EQ(kMinAccDuringCurrentLCInMpss,
                     metadata.min_acc_for_speed_limit());
  }
}

// TODO(judychen): Implement this test.
TEST_F(LaneChangeCommonTestFixture, CanTriggerLaneChangeCrawl) {}

TEST_F(LaneChangeCommonTestFixture, GetLaneChangeSignalFromSequenceOrSeed) {
  SetEgoPose(/*lane_id=*/128229, /*portion=*/0.0, /*speed=*/0.0, /*accel=*/0.0);

  lane_sequence_result_ = test_utility::ConstructLaneSequenceResult(
      {128229, 128253, 128237, 128291, 128269, 128327, 128323},
      {pb::LaneChangeMode::LEFT_LANE_CHANGE}, {{128327, 128323}}, 128229,
      map_test_util());

  lane_selection::LaneSequenceCandidates lane_sequence_candidates_w_lc(nullptr);
  lane_sequence_candidates_w_lc.AddLaneSequenceResult(lane_sequence_result_);
  lane_selection::LaneSequenceCandidates lane_sequence_candidates_wo_lc(
      nullptr);
  LaneChangeInfo lane_change_info;
  std::ostringstream debug_oss;

  {  // Have signal from sequence.
    EXPECT_EQ(
        pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRouting,
        GetLaneChangeSignal(
            *world_model().GetLatestJointPncMapService(),
            world_model().robot_state(), lane_sequence_candidates_w_lc,
            previous_iter_seed(), lane_change_info.lane_sequence_w_lane_change,
            lane_change_info.lane_change_sequence,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.lane_change_sequence_prefix_sources,
            lane_change_info.is_lc_backup_sequence,
            lane_change_info.is_early_lc_sequence,
            world_model()
                .ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_planning_segment_sequence,
            world_model().drivable_lane_reasoner(), debug_oss));
  }

  {  //  Don't have signal from sequence.
    mutable_previous_iter_seed().set_selected_lane_change_state(
        pb::LaneChangeState::LANE_CHANGE_STATE_NONE);
    EXPECT_EQ(
        pb::LaneChangeSignalSourceType::kLaneChangeSignalNone,
        GetLaneChangeSignal(
            *world_model().GetLatestJointPncMapService(),
            world_model().robot_state(), lane_sequence_candidates_wo_lc,
            previous_iter_seed(), lane_change_info.lane_sequence_w_lane_change,
            lane_change_info.lane_change_sequence,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.lane_change_sequence_prefix_sources,
            lane_change_info.is_lc_backup_sequence,
            lane_change_info.is_early_lc_sequence,
            world_model()
                .ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_planning_segment_sequence,
            world_model().drivable_lane_reasoner(), debug_oss));
    EXPECT_EQ(
        pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRouting,
        GetLaneChangeSignal(
            *world_model().GetLatestJointPncMapService(),
            world_model().robot_state(), lane_sequence_candidates_w_lc,
            previous_iter_seed(), lane_change_info.lane_sequence_w_lane_change,
            lane_change_info.lane_change_sequence,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.lane_change_sequence_prefix_sources,
            lane_change_info.is_lc_backup_sequence,
            lane_change_info.is_early_lc_sequence,
            world_model()
                .ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_planning_segment_sequence,
            world_model().drivable_lane_reasoner(), debug_oss));

    mutable_previous_iter_seed().set_selected_lane_change_state(
        pb::LaneChangeState::LANE_CHANGE_STATE_ABORT);
    [[maybe_unused]] auto lane_change_info_seed_ptr =
        mutable_previous_iter_seed().mutable_lane_change_info_seed();
    EXPECT_EQ(
        pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed,
        GetLaneChangeSignal(
            *world_model().GetLatestJointPncMapService(),
            world_model().robot_state(), lane_sequence_candidates_wo_lc,
            previous_iter_seed(), lane_change_info.lane_sequence_w_lane_change,
            lane_change_info.lane_change_sequence,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.lane_change_sequence_prefix_sources,
            lane_change_info.is_lc_backup_sequence,
            lane_change_info.is_early_lc_sequence,
            world_model()
                .ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_planning_segment_sequence,
            world_model().drivable_lane_reasoner(), debug_oss));

    mutable_previous_iter_seed().clear_lane_change_info_seed();
    EXPECT_EQ(
        pb::LaneChangeSignalSourceType::kLaneChangeSignalNone,
        GetLaneChangeSignal(
            *world_model().GetLatestJointPncMapService(),
            world_model().robot_state(), lane_sequence_candidates_wo_lc,
            previous_iter_seed(), lane_change_info.lane_sequence_w_lane_change,
            lane_change_info.lane_change_sequence,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.lane_change_sequence_prefix_sources,
            lane_change_info.is_lc_backup_sequence,
            lane_change_info.is_early_lc_sequence,
            world_model()
                .ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_planning_segment_sequence,
            world_model().drivable_lane_reasoner(), debug_oss));
  }
}

}  // namespace

TEST(LaneChangeCommonTest, HasLaneChangeGapValidityChange) {
  speed::pb::SpeedSolverLcGuideSeed selected_lc_guide_seed;
  google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>
      last_lc_guide_seeds;

  const pb::LaneChangeState lc_none_state =
      pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
  const pb::LaneChangeState lc_preparation_state =
      pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
  const pb::LaneChangeState lc_in_progress_state =
      pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

  // Test for the empty lc guide seeds.
  {
    std::string gap_validity_change_str;
    EXPECT_FALSE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
  }

  // Test for the valid lc guide seeds for current and last.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    EXPECT_FALSE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
  }

  // Test for the invalid lc guide seeds for current and last.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(false);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(false);
    EXPECT_FALSE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
  }

  // Test for the invalid lc guide seeds for current and last.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(false);
    EXPECT_TRUE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
    EXPECT_EQ(gap_validity_change_str, "GAP_INITIATED");
  }

  // Test for the valid lc guide seeds for current and last.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(false);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    EXPECT_TRUE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
    EXPECT_EQ(gap_validity_change_str, "GAP_DISAPPEARED");
  }

  // Test when ego starts to enter preparation state and find a gap.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(false);
    EXPECT_TRUE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_none_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
    EXPECT_EQ(gap_validity_change_str, "GAP_INITIATED");
  }

  // Test when ego starts to enter in progress state with a gap.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    // For CL candidate, it does not have a gap since we do not do gap align for
    // lane change candidate.
    selected_lc_guide_seed.set_is_valid(false);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    EXPECT_FALSE(HasLaneChangeGapValidityChange(
        lc_in_progress_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
  }

  // Test when ego is in lc preparation and the gap disappears.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_validity_change_str;
    selected_lc_guide_seed.set_is_valid(false);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    EXPECT_TRUE(HasLaneChangeGapValidityChange(
        lc_preparation_state, lc_preparation_state, selected_lc_guide_seed,
        last_lc_guide_seeds, gap_validity_change_str));
    EXPECT_EQ(gap_validity_change_str, "GAP_DISAPPEARED");
  }
}

TEST(LaneChangeCommonTest, HasLaneChangeGapObjectChange) {
  speed::pb::SpeedSolverLcGuideSeed selected_lc_guide_seed;
  google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>
      last_lc_guide_seeds;

  // Test for the empty lc guide seeds.
  {
    std::string gap_object_change_str;
    EXPECT_FALSE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
  }

  // Test for the matched gap.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    mutable_last_lc_guide_seed->set_lead_obj_id(1);
    mutable_last_lc_guide_seed->set_tail_obj_id(2);
    EXPECT_FALSE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
  }

  // Test for the gap lead object change.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    mutable_last_lc_guide_seed->set_lead_obj_id(3);
    mutable_last_lc_guide_seed->set_tail_obj_id(2);
    EXPECT_TRUE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
    EXPECT_EQ(gap_object_change_str, "GAP_LEAD_OBJECT_CHANGE");
  }

  // Test for the gap tail object change.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    mutable_last_lc_guide_seed->set_lead_obj_id(1);
    mutable_last_lc_guide_seed->set_tail_obj_id(3);
    EXPECT_TRUE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
    EXPECT_EQ(gap_object_change_str, "GAP_TAIL_OBJECT_CHANGE");
  }

  // Test for the gap objects change.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    mutable_last_lc_guide_seed->set_lead_obj_id(2);
    mutable_last_lc_guide_seed->set_tail_obj_id(3);
    EXPECT_TRUE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
    EXPECT_EQ(gap_object_change_str, "GAP_OBJECTS_CHANGE");
  }

  // Test for the invalid current lc guide seed.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(false);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(true);
    mutable_last_lc_guide_seed->set_lead_obj_id(2);
    mutable_last_lc_guide_seed->set_tail_obj_id(3);
    EXPECT_FALSE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
  }

  // Test for the invalid last lc guide seed.
  {
    selected_lc_guide_seed.Clear();
    last_lc_guide_seeds.Clear();
    std::string gap_object_change_str;
    selected_lc_guide_seed.set_is_valid(true);
    selected_lc_guide_seed.set_lead_obj_id(1);
    selected_lc_guide_seed.set_tail_obj_id(2);
    speed::pb::SpeedSolverLcGuideSeed* mutable_last_lc_guide_seed =
        last_lc_guide_seeds.Add();
    mutable_last_lc_guide_seed->set_is_valid(false);
    mutable_last_lc_guide_seed->set_lead_obj_id(2);
    mutable_last_lc_guide_seed->set_tail_obj_id(3);
    EXPECT_FALSE(HasLaneChangeGapObjectChange(
        selected_lc_guide_seed, last_lc_guide_seeds, gap_object_change_str));
  }
}

TEST(LaneChangeCommonTest, UpdateManualLaneChangeStateByTime) {
  // Test for the disallowed logic.
  {
    pb::ManualLaneChangeState last_mlc_state;
    PopulateManualLaneChangeState(
        /*allow_lane_change_timestamp_in_msec=*/10,
        /*is_lc_disallowed=*/true,
        /*need_lc_signal=*/false,
        /*lc_direction=*/pb::LaneChangeMode::RIGHT_LANE_CHANGE, last_mlc_state);
    const pb::LaneChangeState last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    const pb::LaneChangeMode last_lc_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;
    const pb::LaneChangeMode lc_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;

    // Test for the scenario that the lane change is disallowed.
    {
      pb::ManualLaneChangeState mlc_state;
      UpdateManualLaneChangeStateByTime(
          last_mlc_state, last_lc_state, last_lc_direction, lc_direction,
          /*plan_init_timestamp_in_msec=*/5, mlc_state);
      EXPECT_TRUE(mlc_state.is_lc_disallowed());
    }

    // Test for the scenario that the lane change is allowed.
    {
      pb::ManualLaneChangeState mlc_state;
      UpdateManualLaneChangeStateByTime(
          last_mlc_state, last_lc_state, last_lc_direction, lc_direction,
          /*plan_init_timestamp_in_msec=*/15, mlc_state);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
    }
  }

  // Test for the cases whether the elc signal is needed.
  {
    pb::ManualLaneChangeState last_mlc_state;
    PopulateManualLaneChangeState(
        /*allow_lane_change_timestamp_in_msec=*/10,
        /*is_lc_disallowed=*/false,
        /*need_lc_signal=*/true,
        /*lc_direction=*/pb::LaneChangeMode::RIGHT_LANE_CHANGE, last_mlc_state);
    const pb::LaneChangeMode lc_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;

    // Test for the scenario that the lane change needs elc signal.
    {
      const pb::LaneChangeState last_lc_state =
          pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
      const pb::LaneChangeMode last_lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      UpdateManualLaneChangeStateByTime(
          last_mlc_state, last_lc_state, last_lc_direction, lc_direction,
          /*plan_init_timestamp_in_msec=*/20, mlc_state);
      EXPECT_TRUE(mlc_state.need_lc_signal());
    }

    // Test for the scenario that the lane change has finished.
    {
      const pb::LaneChangeState last_lc_state =
          pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
      const pb::LaneChangeMode last_lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      UpdateManualLaneChangeStateByTime(
          last_mlc_state, last_lc_state, last_lc_direction, lc_direction,
          /*plan_init_timestamp_in_msec=*/20, mlc_state);
      EXPECT_FALSE(mlc_state.need_lc_signal());
    }
  }
}

TEST(LaneChangeCommonTest, AnswerLaneChangeAbortResponse) {
  // Test for the scenario when the ego is not in lane change progress.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION,
        /*is_ego_in_target_lane_sequence=*/false,
        /*plan_init_timestamp_in_msec=*/10000, mlc_state, debug_oss));
  }

  // Test for the scenario when the ego is in the target lane sequence.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*is_ego_in_target_lane_sequence=*/true,
        /*plan_init_timestamp_in_msec=*/10000, mlc_state, debug_oss));
  }

  // Test for the scenario when no lane change abort response is found.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*is_ego_in_target_lane_sequence=*/false,
        /*plan_init_timestamp_in_msec=*/10000, mlc_state, debug_oss));
  }

  // Test for the scenario when the lane change abort response is delayed
  // more than 2s.
  {
    // Add a lane change abort response.
    std::vector<pb::AssistResponse> assist_responses;
    pb::AssistResponse response;
    response.set_is_unidirectional(true);
    pb::LaneChangeAbortResponse* lca_response =
        response.mutable_lane_change_abort_response();
    lca_response->set_timestamp(10000);
    assist_responses.emplace_back(response);

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*is_ego_in_target_lane_sequence=*/false,
        /*plan_init_timestamp_in_msec=*/15000, mlc_state, debug_oss));
  }

  // Test for the normal scenario.
  constexpr int64 kAllowLaneChangeDurationInMSec = 3000;
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::AssistResponse response;
    response.set_is_unidirectional(true);
    pb::LaneChangeAbortResponse* lca_response =
        response.mutable_lane_change_abort_response();
    lca_response->set_timestamp(10000);
    assist_responses.emplace_back(response);

    pb::ManualLaneChangeState mlc_state;
    constexpr int64 kInitAllowLaneChangeTimestampInMSec = 8000;
    mlc_state.set_allow_lane_change_timestamp_in_msec(
        kInitAllowLaneChangeTimestampInMSec);
    std::ostringstream debug_oss;
    constexpr int64 kPlanInitTimestampInMSec = 11000;
    EXPECT_TRUE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*is_ego_in_target_lane_sequence=*/false,
        /*plan_init_timestamp_in_msec=*/kPlanInitTimestampInMSec, mlc_state,
        debug_oss));
    EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
              kPlanInitTimestampInMSec + kAllowLaneChangeDurationInMSec);
  }

  // Test for the normal scenario.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::AssistResponse response;
    response.set_is_unidirectional(true);
    pb::LaneChangeAbortResponse* lca_response =
        response.mutable_lane_change_abort_response();
    lca_response->set_timestamp(10000);
    assist_responses.emplace_back(response);

    pb::ManualLaneChangeState mlc_state;
    constexpr int64 kInitAllowLaneChangeTimestampInMSec = 15000;
    mlc_state.set_allow_lane_change_timestamp_in_msec(
        kInitAllowLaneChangeTimestampInMSec);
    std::ostringstream debug_oss;
    constexpr int64 kPlanInitTimestampInMSec = 11000;
    EXPECT_TRUE(AnswerLaneChangeAbortResponse(
        assist_responses,
        /*last_lc_state=*/pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
        /*is_ego_in_target_lane_sequence=*/false,
        /*plan_init_timestamp_in_msec=*/kPlanInitTimestampInMSec, mlc_state,
        debug_oss));
    EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
              kInitAllowLaneChangeTimestampInMSec);
  }
}

TEST(LaneChangeCommonTest, AnswerManualLaneChangeCommand) {
  // Test for the scenario without any lane change command.
  {
    std::vector<pb::AssistResponse> assist_responses;
    const pb::LaneChangeMode lc_direction =
        pb::LaneChangeMode::NONE_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerManualLaneChangeCommand(assist_responses, lc_direction,
                                               plan_init_timestamp_in_msec,
                                               mlc_state, debug_oss));
  }

  // Test for the scenario with invalid command.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::AssistResponse response;
    response.set_is_unidirectional(true);
    pb::ManualLaneChangeCommand* mlc_command =
        response.mutable_manual_lane_change_command();
    mlc_command->set_timestamp(10000);
    mlc_command->set_lane_change_direction(
        pb::LaneChangeMode::NONE_LANE_CHANGE);
    assist_responses.emplace_back(response);

    const pb::LaneChangeMode lc_direction =
        pb::LaneChangeMode::NONE_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_FALSE(AnswerManualLaneChangeCommand(assist_responses, lc_direction,
                                               plan_init_timestamp_in_msec,
                                               mlc_state, debug_oss));
  }

  // Test for the normal scenario.
  {
    std::vector<pb::AssistResponse> assist_responses;
    pb::AssistResponse response;
    response.set_is_unidirectional(true);
    pb::ManualLaneChangeCommand* mlc_command =
        response.mutable_manual_lane_change_command();
    mlc_command->set_timestamp(10000);
    mlc_command->set_lane_change_direction(
        pb::LaneChangeMode::LEFT_LANE_CHANGE);
    assist_responses.emplace_back(response);

    const pb::LaneChangeMode lc_direction =
        pb::LaneChangeMode::NONE_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    EXPECT_TRUE(AnswerManualLaneChangeCommand(assist_responses, lc_direction,
                                              plan_init_timestamp_in_msec,
                                              mlc_state, debug_oss));
  }
}

TEST(LaneChangeCommonTest, AnswerMLCStateAllowedWOSignal) {
  // Test for left command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::LEFT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for none lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWOSignal(command_direction, lc_direction,
                                                plan_init_timestamp_in_msec,
                                                mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
      EXPECT_EQ(mlc_state.lc_direction(), command_direction);
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWOSignal(command_direction, lc_direction,
                                                plan_init_timestamp_in_msec,
                                                mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                std::numeric_limits<int64>::max());
      EXPECT_TRUE(mlc_state.is_lc_disallowed());
      EXPECT_FALSE(mlc_state.need_lc_signal());
    }
  }

  // Test for right command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for none lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWOSignal(command_direction, lc_direction,
                                                plan_init_timestamp_in_msec,
                                                mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
      EXPECT_EQ(mlc_state.lc_direction(), command_direction);
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWOSignal(command_direction, lc_direction,
                                                plan_init_timestamp_in_msec,
                                                mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                std::numeric_limits<int64>::max());
      EXPECT_TRUE(mlc_state.is_lc_disallowed());
      EXPECT_FALSE(mlc_state.need_lc_signal());
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }
  }
}

TEST(LaneChangeCommonTest, AnswerMLCStateAllowedWSignal) {
  // Test for the right command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                                mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                               mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                std::numeric_limits<int64>::max());
      EXPECT_TRUE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                                mlc_state));
    }
  }

  // Test for the left command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::LEFT_LANE_CHANGE;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                                mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                                mlc_state));
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                               mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                std::numeric_limits<int64>::max());
      EXPECT_TRUE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
    }
  }
}

TEST(LaneChangeCommonTest, AnswerMLCStateDisallowedWOSignal) {
  // Test for the right command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_FALSE(mlc_state.need_lc_signal());
    }
  }

  // Test for the left command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::LEFT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_FALSE(mlc_state.need_lc_signal());
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWOSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }
  }
}

TEST(LaneChangeCommonTest, AnswerMLCStateDisallowedWSignal) {
  // Test for the right command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::RIGHT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
    }
  }

  // Test for the left command.
  {
    const pb::LaneChangeMode command_direction =
        pb::LaneChangeMode::LEFT_LANE_CHANGE;
    const int64_t plan_init_timestamp_in_msec = 10;

    // Test for non lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::NONE_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }

    // Test for the left lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::LEFT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_TRUE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
      EXPECT_EQ(mlc_state.allow_lane_change_timestamp_in_msec(),
                plan_init_timestamp_in_msec);
      EXPECT_FALSE(mlc_state.is_lc_disallowed());
      EXPECT_TRUE(mlc_state.need_lc_signal());
    }

    // Test for the right lane change.
    {
      const pb::LaneChangeMode lc_direction =
          pb::LaneChangeMode::RIGHT_LANE_CHANGE;
      pb::ManualLaneChangeState mlc_state;
      EXPECT_FALSE(AnswerMLCStateDisallowedWSignal(
          command_direction, lc_direction, plan_init_timestamp_in_msec,
          mlc_state));
    }
  }
}

TEST(LaneChangeCommonTest, AnswerLaneChangeAbortFromFlagRightLaneChange) {
  // Init the map test util.
  constexpr double x_pos = 28801.00;
  constexpr double y_pos = 39085.22;
  pnc_map::MapTestUtil map_test_util;
  map_test_util.InitPncMapService(x_pos, y_pos, /*z=*/0.0,
                                  hdmap::test_util::SceneType::kMerge,
                                  "SCENARIO_MERGE");

  // Construct lane sequence result.
  constexpr int64_t kSourceLaneId = 128233;
  constexpr int64_t kTargetLaneId = 128229;
  LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult(
          /*lane_ids=*/{kSourceLaneId, kTargetLaneId},
          /*lane_change_modes=*/{pb::LaneChangeMode::RIGHT_LANE_CHANGE},
          /*lane_change_instances=*/{{kSourceLaneId, kTargetLaneId}},
          /*current_lane_id=*/kSourceLaneId, map_test_util);
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);
  const pnc_map::Lane* source_lane = map_test_util.GetLane(kSourceLaneId);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util.GetLane(kTargetLaneId);
  ASSERT_NE(target_lane, nullptr);

  // Get specific point.
  const math::geometry::Point2d& source_center_line_point =
      source_lane->center_line().GetInterp(/*arc_length=*/5.0);
  const double source_center_line_heading_at_point =
      source_lane->GetLaneDirection(source_center_line_point);

  const math::geometry::Point2d& cross_curve_point =
      source_lane->right_marking()->line().GetInterp(/*arc_length=*/5.0);
  const double cross_curve_heading_at_point =
      source_lane->GetLaneDirection(cross_curve_point);

  const math::geometry::Point2d& target_center_line_point =
      target_lane->center_line().GetInterp(/*arc_length=*/5.0);
  const double target_center_line_heading_at_point =
      target_lane->GetLaneDirection(target_center_line_point);

  // Test for the case which is not in progress.
  {
    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            source_center_line_point.x(), source_center_line_point.y(),
            /*speed=*/0.0, /*heading=*/source_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    EXPECT_FALSE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }

  // Test for the case when ego is on the source lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            source_center_line_point.x(), source_center_line_point.y(),
            /*speed=*/0.0, /*heading=*/source_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 0.0;
    EXPECT_FALSE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }

  // Test for the case when ego is on the cross lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            cross_curve_point.x(), cross_curve_point.y(),
            /*speed=*/0.0,
            /*heading=*/cross_curve_heading_at_point - M_PI_4 / 2);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    // Test for the positive larger encroachment dist.
    {
      pb::ManualLaneChangeState mlc_state;
      std::ostringstream debug_oss;
      FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 2.0;
      EXPECT_FALSE(
          AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                        last_lc_state, mlc_state, debug_oss));
    }

    // Test for the negative smaller encroachment dist.
    {
      pb::ManualLaneChangeState mlc_state;
      std::ostringstream debug_oss;
      FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = -1.0;
      EXPECT_TRUE(
          AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                        last_lc_state, mlc_state, debug_oss));
    }
  }

  // Test for the case when ego is on the target lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            target_center_line_point.x(), target_center_line_point.y(),
            /*speed=*/0.0, /*heading=*/target_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 0.0;
    EXPECT_TRUE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }
}

TEST(LaneChangeCommonTest, AnswerLaneChangeAbortFromFlagForLeftLaneChange) {
  // Init the map test util.
  constexpr double x_pos = 28801.00;
  constexpr double y_pos = 39085.22;
  pnc_map::MapTestUtil map_test_util;
  map_test_util.InitPncMapService(x_pos, y_pos, /*z=*/0.0,
                                  hdmap::test_util::SceneType::kMerge,
                                  "SCENARIO_MERGE");

  // Construct lane sequence result.
  constexpr int64_t kSourceLaneId = 128229;
  constexpr int64_t kTargetLaneId = 128233;
  LaneSequenceResult lane_sequence_result =
      test_utility::ConstructLaneSequenceResult(
          /*lane_ids=*/{kSourceLaneId, kTargetLaneId},
          /*lane_change_modes=*/{pb::LaneChangeMode::LEFT_LANE_CHANGE},
          /*lane_change_instances=*/{{kSourceLaneId, kTargetLaneId}},
          /*current_lane_id=*/kSourceLaneId, map_test_util);
  std::vector<const pnc_map::Lane*>& lane_sequence =
      lane_sequence_result.lane_sequence;
  LaneChangeInstance& lane_change_instance =
      lane_sequence_result.lane_change_instances.at(0);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), lane_sequence);
  const pnc_map::Lane* source_lane = map_test_util.GetLane(kSourceLaneId);
  ASSERT_NE(source_lane, nullptr);
  const pnc_map::Lane* target_lane = map_test_util.GetLane(kTargetLaneId);
  ASSERT_NE(target_lane, nullptr);

  // Get specific points.
  const math::geometry::Point2d& source_center_line_point =
      source_lane->center_line().GetInterp(/*arc_length=*/5.0);
  const double source_center_line_heading_at_point =
      source_lane->GetLaneDirection(source_center_line_point);

  const math::geometry::Point2d& cross_curve_point =
      source_lane->left_marking()->line().GetInterp(/*arc_length=*/5.0);
  const double cross_curve_heading_at_point =
      source_lane->GetLaneDirection(cross_curve_point);

  const math::geometry::Point2d& target_center_line_point =
      target_lane->center_line().GetInterp(/*arc_length=*/5.0);
  const double target_center_line_heading_at_point =
      target_lane->GetLaneDirection(target_center_line_point);

  // Test for the case which is not in progress.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            source_center_line_point.x(), source_center_line_point.y(),
            /*speed=*/0.0, /*heading=*/source_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 0.0;
    EXPECT_FALSE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }

  // Test for the case when ego is on the source lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            source_center_line_point.x(), source_center_line_point.y(),
            /*speed=*/0.0, /*heading=*/source_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 0.0;
    EXPECT_FALSE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }

  // Test for the case when ego is on the cross lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            cross_curve_point.x(), cross_curve_point.y(),
            /*speed=*/0.0,
            /*heading=*/cross_curve_heading_at_point + M_PI_4 / 2);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    // Test for the positive larger encroachment dist.
    {
      pb::ManualLaneChangeState mlc_state;
      std::ostringstream debug_oss;
      FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 2.0;
      EXPECT_FALSE(
          AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                        last_lc_state, mlc_state, debug_oss));
    }

    // Test for the negative smaller encroachment dist.
    {
      pb::ManualLaneChangeState mlc_state;
      std::ostringstream debug_oss;
      FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = -1.0;
      EXPECT_TRUE(
          AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                        last_lc_state, mlc_state, debug_oss));
    }
  }

  // Test for the case when ego is on the target lane.
  {
    // Construct robot state on the center line of the source line.
    const RobotStateSnapshot current_snapshot =
        test_utility::ConstructRobotStateSnapshot(
            target_center_line_point.x(), target_center_line_point.y(),
            /*speed=*/0.0,
            /*heading=*/target_center_line_heading_at_point);
    const pb::LaneChangeState& last_lc_state =
        pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;

    pb::ManualLaneChangeState mlc_state;
    std::ostringstream debug_oss;
    FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter = 0.0;
    EXPECT_TRUE(
        AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                      last_lc_state, mlc_state, debug_oss));
  }
}

TEST_F(LaneChangeCommonTestFixture, GetStitchedLaneSequence) {
  const std::vector<const pnc_map::Lane*>& untrimmed_lane_sequence =
      world_model().pnc_map_service()->GetLaneSequence(
          {128253, 128257, 128291, 128269});
  {  // Lane sequence is aligned with untrimmed lane sequence.
    const std::vector<const pnc_map::Lane*>& lane_sequence =
        world_model().pnc_map_service()->GetLaneSequence(
            {128253, 128257, 128291});
    const std::vector<const pnc_map::Lane*>& stitched_lane_sequence =
        GetStitchedLaneSequence(lane_sequence, untrimmed_lane_sequence);
    EXPECT_EQ(4, stitched_lane_sequence.size());
  }

  {  // Lane sequence has one lane ahead.
    const std::vector<const pnc_map::Lane*>& lane_sequence =
        world_model().pnc_map_service()->GetLaneSequence(
            {128229, 128253, 128257});
    const std::vector<const pnc_map::Lane*>& stitched_lane_sequence =
        GetStitchedLaneSequence(lane_sequence, untrimmed_lane_sequence);
    EXPECT_EQ(5, stitched_lane_sequence.size());
  }

  {  // Lane sequence has one lane ahead that not a predecessor.
    const std::vector<const pnc_map::Lane*>& lane_sequence =
        world_model().pnc_map_service()->GetLaneSequence(
            {128251, 128253, 128257});
    const std::vector<const pnc_map::Lane*>& stitched_lane_sequence =
        GetStitchedLaneSequence(lane_sequence, untrimmed_lane_sequence);
    EXPECT_EQ(4, stitched_lane_sequence.size());
  }
}

TEST_F(LaneChangeCommonTestFixture, CalculateTTCOfArclengthRange) {
  constexpr double kArclengthRangeBufferInMeter = 1.0;
  constexpr double kEgoRearBumperArclength = 15.0;
  constexpr double kEgoFrontBumperArclength = 20.0;
  constexpr double kEgoSpeedInMps = 5.0;

  constexpr double kObjectLength = 4.5;
  constexpr double kOverlappingObjectArclength = 17.5;
  constexpr double kSmallLateralGapInMeter = 0.3;
  std::string debug_str;

  auto construct_object_info =
      [&](double object_arclength, double start_padded_relative_time,
          double constant_lateral_gap,
          double furthest_corner_signed_lat_dist_to_cross_lane_curve)
      -> LaneChangeObjectInfo {
    LaneChangeObjectInfo object_info;
    object_info.arc_length_on_region = object_arclength;
    object_info.length = kObjectLength;
    object_info.is_vehicle = true;
    object_info.is_cyclist = false;
    object_info.is_likely_parked_car_from_score = false;
    object_info.is_primary_stationary = false;
    object_info.has_stationary_to_move_intention = false;
    object_info.agent_furthest_corner_signed_lat_dist_to_cross_lane_curve =
        furthest_corner_signed_lat_dist_to_cross_lane_curve;
    constexpr double kMaxEndRelativeTimeInSec = 8.0;
    speed::pb::OverlapRegion overlap_region;
    overlap_region.set_start_padded_relative_time_in_sec(
        start_padded_relative_time);
    overlap_region.set_end_padded_relative_time_in_sec(
        std::max(start_padded_relative_time, kMaxEndRelativeTimeInSec));
    for (int i = 0; i < kMaxEndRelativeTimeInSec * 10.0; ++i) {
      speed::pb::OverlapSlice* overlap_slice =
          overlap_region.add_overlap_slices();
      overlap_slice->set_signed_lateral_gap(constant_lateral_gap);
    }
    object_info.overlap_regions.push_back(overlap_region);
    return object_info;
  };
  constexpr bool kIsToLeft = true;
  constexpr double kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter =
      1.0;
  constexpr double kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter =
      2.0;

  LaneChangeObjectInfo object_info = construct_object_info(
      kOverlappingObjectArclength, /*start_padded_relative_time=*/0.0,
      kSmallLateralGapInMeter,
      kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
  EXPECT_TRUE(std::isfinite(CalculateTTCOfArclengthRange(
      object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
      kEgoSpeedInMps, kArclengthRangeBufferInMeter,
      kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
      debug_str)));
  /*
    Test filtering by object's attr.
   */
  {  // Non-vehicle and non-cyclist object.
    LaneChangeObjectInfo object_info = construct_object_info(
        kOverlappingObjectArclength, /*start_padded_relative_time=*/0.0,
        kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.is_vehicle = false;
    object_info.is_cyclist = false;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Parked car.
    LaneChangeObjectInfo object_info = construct_object_info(
        kOverlappingObjectArclength, /*start_padded_relative_time=*/0.0,
        kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.is_vehicle = true;
    object_info.is_parked_car = true;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Likely parked car.
    LaneChangeObjectInfo object_info = construct_object_info(
        kOverlappingObjectArclength, /*start_padded_relative_time=*/0.0,
        kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.is_vehicle = true;
    object_info.is_likely_parked_car_from_score = true;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Stationary object w/o STM intention.
    LaneChangeObjectInfo object_info = construct_object_info(
        kOverlappingObjectArclength, /*start_padded_relative_time=*/0.0,
        kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.is_vehicle = true;
    object_info.is_primary_stationary = true;
    object_info.has_stationary_to_move_intention = false;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  /*
    Test filtering by object's overlap.
   */
  {  // Large start_padded_relative_time
    constexpr double kLargeStartPaddedRelativeTimeInSec = 2.5;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        construct_object_info(
            kOverlappingObjectArclength, kLargeStartPaddedRelativeTimeInSec,
            kSmallLateralGapInMeter,
            kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter),
        kEgoRearBumperArclength, kEgoFrontBumperArclength, kEgoSpeedInMps,
        kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Large lateral gap
    constexpr double kLargeLateralGapInMeter = 0.7;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        construct_object_info(
            kOverlappingObjectArclength,
            /*start_padded_relative_time=*/0.0, kLargeLateralGapInMeter,
            kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter),
        kEgoRearBumperArclength, kEgoFrontBumperArclength, kEgoSpeedInMps,
        kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  /*
    Test TTC of non-filtering objects.
   */
  constexpr double kObjectLowSpeedInMps = 2.0;
  constexpr double kObjectHighSpeedInMps = 8.0;
  {  // Object behind ego.
    constexpr double kDistanceBehindEgoInMeter = 10.0;
    double object_arclength = kEgoRearBumperArclength -
                              kArclengthRangeBufferInMeter -
                              kDistanceBehindEgoInMeter - kObjectLength * 0.5;
    LaneChangeObjectInfo object_info = construct_object_info(
        object_arclength,
        /*start_padded_relative_time=*/0.0, kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    // Slower than ego.
    object_info.speed = kObjectLowSpeedInMps;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
    // Faster than ego.
    object_info.speed = kObjectHighSpeedInMps;
    EXPECT_TRUE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Object ahead of ego.
    constexpr double kDistanceAheadOfEgoInMeter = 10.0;
    double object_arclength = kEgoFrontBumperArclength +
                              kArclengthRangeBufferInMeter +
                              kDistanceAheadOfEgoInMeter + kObjectLength * 0.5;
    LaneChangeObjectInfo object_info = construct_object_info(
        object_arclength,
        /*start_padded_relative_time=*/0.0, kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    // Slower than ego.
    object_info.speed = kObjectLowSpeedInMps;
    EXPECT_TRUE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
    // Faster than ego.
    object_info.speed = kObjectHighSpeedInMps;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  /*
    Test filtering by encroachment to target region.
   */
  {  // Ego has less encroachment.
    constexpr double kDistanceAheadOfEgoInMeter = 10.0;
    double object_arclength = kEgoFrontBumperArclength +
                              kArclengthRangeBufferInMeter +
                              kDistanceAheadOfEgoInMeter + kObjectLength * 0.5;
    LaneChangeObjectInfo object_info = construct_object_info(
        object_arclength,
        /*start_padded_relative_time=*/0.0, kSmallLateralGapInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.speed = kObjectLowSpeedInMps;
    EXPECT_TRUE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
  {  // Ego has larger encroachment.
    constexpr double kDistanceAheadOfEgoInMeter = 10.0;
    double object_arclength = kEgoFrontBumperArclength +
                              kArclengthRangeBufferInMeter +
                              kDistanceAheadOfEgoInMeter + kObjectLength * 0.5;
    LaneChangeObjectInfo object_info = construct_object_info(
        object_arclength,
        /*start_padded_relative_time=*/0.0, kSmallLateralGapInMeter,
        kMediumFurtherCornerSignedLatDistToCrossLaneCurveInMeter);
    object_info.speed = kObjectLowSpeedInMps;
    EXPECT_FALSE(std::isfinite(CalculateTTCOfArclengthRange(
        object_info, kEgoRearBumperArclength, kEgoFrontBumperArclength,
        kEgoSpeedInMps, kArclengthRangeBufferInMeter,
        kLargeFurtherCornerSignedLatDistToCrossLaneCurveInMeter, kIsToLeft,
        debug_str)));
  }
}

TEST_F(LaneChangeCommonTestFixture, IsDifficultLcForPullover) {
  constexpr double kUrgencyDistForDestinationInMeter = 50.0;
  LaneChangeInfo complete_lane_change_info;
  complete_lane_change_info.lane_change_env_info.urgency_infos.emplace_back(
      pb::LaneChangeUrgencyType::DESTINATION_FOR_PULL_OVER,
      kUrgencyDistForDestinationInMeter,
      pb::LaneChangeStopLineType::HARD_STOP_LINE);
  constexpr double kHighDistanceDifficultyScore = 1.5;
  constexpr double kLowDistanceDifficultyScore = 0.5;
  // Lane change for pullover is difficult.
  {
    LaneChangeInfo lane_change_info = complete_lane_change_info;
    lane_change_info.lane_change_env_info.distance_difficulty_score =
        kHighDistanceDifficultyScore;
    lane_change_info.preview_route_result = std::nullopt;
    std::ostringstream debug_oss;
    EXPECT_TRUE(IsDifficultLcForPullover(pb::LANE_CHANGE_STATE_PREPARATION,
                                         lane_change_info, debug_oss));
  }

  // Lane change for pullover isn't difficult with low difficulty score.
  {
    LaneChangeInfo lane_change_info = complete_lane_change_info;
    lane_change_info.lane_change_env_info.distance_difficulty_score =
        kLowDistanceDifficultyScore;
    lane_change_info.preview_route_result = std::nullopt;
    std::ostringstream debug_oss;
    EXPECT_FALSE(IsDifficultLcForPullover(pb::LANE_CHANGE_STATE_PREPARATION,
                                          lane_change_info, debug_oss));
  }

  // Lane change for pullover isn't difficult at in-progress state.
  {
    LaneChangeInfo lane_change_info = complete_lane_change_info;
    lane_change_info.lane_change_env_info.distance_difficulty_score =
        kHighDistanceDifficultyScore;
    lane_change_info.preview_route_result = std::nullopt;
    std::ostringstream debug_oss;
    EXPECT_FALSE(IsDifficultLcForPullover(pb::LANE_CHANGE_STATE_IN_PROGRESS,
                                          lane_change_info, debug_oss));
  }

  // No urgency info of destination for pullover.
  {
    LaneChangeInfo lane_change_info = complete_lane_change_info;
    lane_change_info.lane_change_env_info.distance_difficulty_score =
        kHighDistanceDifficultyScore;
    lane_change_info.lane_change_env_info.urgency_infos.clear();
    std::ostringstream debug_oss;
    EXPECT_FALSE(IsDifficultLcForPullover(pb::LANE_CHANGE_STATE_PREPARATION,
                                          lane_change_info, debug_oss));
  }
}

TEST_F(LaneChangeCommonTestFixture, GetLaneChangeDurationTime) {
  // Assume the lateral movement's plot as below, the rise & fall parts
  // in speed and dist plot should be curve.
  //  accel plot        speed plot      dist plot___
  //    /\                  /\                  /
  //  _/  \    _           /  \                /
  //       \  /           /    \              /
  //        \/         __/      \__       ___/
  // 1. If achieve max accel is a, max jerk time is t, then we have:
  // actual max speed = a * t, actual lateral distance = 2 * a * t^2
  // if has extra dist togo, drive with max speed.
  // extra time = d' / (a * t), overall time = 4 * t + d' / (a * t).
  // 2. If not achieve max accel, set max jerk as j, max jerk time as t, then we
  // have: actual max accel = j * t, actual max speed = j * t^2,
  // actual lateral distance = 2 * j * t^3, overall time = 4 * t.
  const double lat_dist = 4.2;
  // Test w/o initial lateral speed.
  {
    const double init_lat_speed = 0.0;
    const double max_lat_jerk = 1.0;
    // Not achieve max_lat_accel.
    {
      const double max_lat_accel = 2.0;
      EXPECT_NEAR(GetLaneChangeDurationTime(lat_dist, init_lat_speed,
                                            max_lat_accel, max_lat_jerk),
                  4 * std::cbrt(lat_dist * 0.5), 0.01);
    }

    // Achieve max_lat_accel.
    {
      const double max_lat_accel = 1.0;
      const double max_acc_time = max_lat_accel / max_lat_jerk;
      const double max_lat_speed = max_lat_accel * max_acc_time;
      EXPECT_NEAR(GetLaneChangeDurationTime(lat_dist, init_lat_speed,
                                            max_lat_accel, max_lat_jerk),
                  2 * max_acc_time + lat_dist / max_lat_speed, 0.01);
    }
  }

  // Test w/ initial lateral speed.
  {
    const double init_lat_speed = 1.0;
    const double max_lat_jerk = 1.0;
    // Not achieve max_lat_accel.
    {
      const double max_lat_accel = 0.8;
      const double max_acc_time = max_lat_accel / max_lat_jerk;
      const double max_lat_speed =
          max_lat_accel * max_acc_time + init_lat_speed;
      const double max_dec_time = std::sqrt(max_lat_speed / max_lat_jerk);
      EXPECT_NEAR(GetLaneChangeDurationTime(lat_dist, init_lat_speed,
                                            max_lat_accel, max_lat_jerk),
                  2 * (max_acc_time + max_dec_time), 0.01);
    }

    // Achieve max_lat_accel.
    {
      const double max_lat_accel = 0.5;
      const double max_acc_time = max_lat_accel / max_lat_jerk;
      const double max_lat_speed =
          max_lat_accel * max_acc_time + init_lat_speed;
      const double max_dec_time = std::sqrt(max_lat_speed / max_lat_jerk);
      const double lat_disp = (max_lat_speed + init_lat_speed) * max_acc_time +
                              max_lat_speed * max_dec_time;
      EXPECT_NEAR(GetLaneChangeDurationTime(lat_dist, init_lat_speed,
                                            max_lat_accel, max_lat_jerk),
                  2 * (max_acc_time + max_dec_time) +
                      (lat_dist - lat_disp) / max_lat_speed,
                  0.01);
    }
  }
}

}  // namespace planner
