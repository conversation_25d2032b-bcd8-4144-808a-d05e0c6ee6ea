#ifndef ONBOARD_PLANNER_SPEED_REASONING_INPUT_URGENCY_INFO_H_
#define ONBOARD_PLANNER_SPEED_REASONING_INPUT_URGENCY_INFO_H_

#include <algorithm>
#include <map>
#include <set>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "base/optional_value_accessor.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/speed/reasoning/prediction_decision_maker_input.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/util/creep_gap_util.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"

namespace planner {
namespace speed {

// LaneAgentOccupancyMap defines a set of lanes and corresponding agents
// occupying the lanes.
using LaneAgentOccupancyMap = std::map<int64_t, std::vector<ObjectId>>;

// The UrgencyInfo includes information on whether the vehicle needs to proceed
// more urgently, including inefficiencies in the vehicle's behavior or
// environmental congestion, etc.
class UrgencyInfo {
 public:
  UrgencyInfo(const WorldContext& world_context,
              const TrajectoryInfo& trajectory_info,
              const std::unordered_map<ObjectId, planner::PlannerObject>&
                  planner_object_map,
              const std::unordered_map<ConstructionZoneId,
                                       const ConstructionZone*>& cz_ptr_map,
              const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
              const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
              const pb::ReasoningConfig& reasoning_config,
              pb::UrgencyInfoSeed& current_seed, pb::UrgencyInfoDebug* debug);

  // Returns true if a specific urgency reason exists in the urgency_reasons
  // set.
  bool HasUrgencyReason(
      const planner::pb::UrgencyReason::Enum urgency_reason) const {
    return urgency_reasons_.find(urgency_reason) != urgency_reasons_.end();
  }

  // Returns true if ego should proceed more urgently and creep in the dense
  // traffic moving. Note the U-turn, UPL and VRU at crosswalks are not included
  // in this bool signal.
  // TODO(jinhao): Output the urgency score/level and urgency reason for
  // detailed scoping and tuning.
  bool ShouldProceedUrgently() const {
    return ShouldSetDiscomfortForProgressInDenseTraffic() ||
           HasUrgencyReason(planner::pb::UrgencyReason::EndOfTrafficLight);
  }

  // Returns true if ego should set discomfort for progress in dense traffic.
  bool ShouldSetDiscomfortForProgressInDenseTraffic() const {
    return HasUrgencyReason(
               planner::pb::UrgencyReason::AvoidCutInInDenseTraffic) ||
           HasUrgencyReason(planner::pb::UrgencyReason::MergeInDenseTraffic) ||
           HasUrgencyReason(
               planner::pb::UrgencyReason::UnstuckFromKeepClearZone) ||
           HasUrgencyReason(
               planner::pb::UrgencyReason::StuckInDynamicDenseTraffic) ||
           (was_proceeding_urgently_ && is_ego_in_low_speed_ &&
            !non_leading_interacting_agent_ids_.empty());
  }

  // Returns true if ego proceed more urgently during upl.
  bool ShouldSetDiscomfortForProgressInUPL() const {
    return HasUrgencyReason(planner::pb::UrgencyReason::BlockingTrafficInUPL) ||
           HasUrgencyReason(planner::pb::UrgencyReason::UPLWaitingTooLong) ||
           (should_latch_discomfort_for_progress_in_upl_ &&
            was_discomfort_for_progress_in_upl_);
  }

  bool ShouldProceedUrgentlyInRightHook(const pb::OverlapRegion& overlap_region,
                                        double ra_to_front_bumper,
                                        double ego_length) const {
    if (!HasUrgencyReason(planner::pb::UrgencyReason::RightHookDenseTraffic)) {
      return false;
    }

    const math::Range1d& right_hook_range =
        ::base::CheckAndGetValue(right_hook_ra_arclength_range_);
    return math::IsInRange(overlap_region.ego_moving_distance_padded().start() +
                               ra_to_front_bumper,
                           right_hook_range.start_pos,
                           right_hook_range.end_pos) ||
           math::IsInRange(
               overlap_region.ego_moving_distance_padded().end() - ego_length,
               right_hook_range.start_pos, right_hook_range.end_pos);
  }

  // Returns true if ego proceed more urgently during waypoint.
  bool ShouldSetDiscomfortForProgressInWaypoint() const {
    return HasUrgencyReason(planner::pb::UrgencyReason::Waypoint) &&
           (is_dominant_constraint_stationary_ ||
            HasUrgencyReason(planner::pb::UrgencyReason::MergeInDenseTraffic));
  }

  bool ShouldProceedUrgentlyNearKeepClearZone() const {
    return HasUrgencyReason(
        planner::pb::UrgencyReason::UnstuckFromKeepClearZone);
  }

  // Returns true if Ego should trigger creep around the given crosswalk.
  bool ShouldProceedUrgentlyAroundCrosswalk(int64_t crosswalk_id) const {
    return creeping_crosswalk_ids().count(crosswalk_id) > 0;
  }

  const std::set<int64_t>& creeping_crosswalk_ids() const {
    return creeping_crosswalk_ids_;
  }

  const CreepTimeGapInfo& GetCrosswalkTimeGapInfo(int64_t crosswalk_id) const {
    const auto crosswalk_gap_info_iter =
        crosswalk_id_to_time_gap_info_.find(crosswalk_id);
    DCHECK(crosswalk_gap_info_iter != crosswalk_id_to_time_gap_info_.end());
    return crosswalk_gap_info_iter->second;
  }

  void PopulateDebug(pb::UrgencyInfoDebug& urgency_info_debug) const;

 private:
  // Updates the context of the traffic flow around ego,
  // including the non-leading agents interacting with ego and the
  // neighbor/merge lane's occupancy status.
  void UpdateTrafficFlowContext(
      const TrajectoryInfo& trajectory_info,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
      const std::vector<PredictionDecisionMakerInput>& reasoning_inputs);

  // Updates info about the VRU flow using crosswalks.
  void UpdateCrosswalkTrafficFlowContext(
      const TrajectoryInfo& trajectory_info,
      const std::vector<PredictionDecisionMakerInput>& reasoning_inputs);

  // Adds interested lane clusters around ego for estimating the density of the
  // traffic flow.
  // TODO(jinhao): Consider fork lanes as well, example: cn14310309.
  void AddInterestedLaneClusters(const TrajectoryInfo& trajectory_info);

  // Adds interested neighbor lane clusters around ego for estimating the
  // density of the traffic flow.
  void AddInterestedNeighborLaneClusters(const TrajectoryInfo& trajectory_info);

  // Adds interested merge lane clusters around ego for estimating the density
  // of the traffic flow.
  void AddInterestedMergeLaneClusters(const TrajectoryInfo& trajectory_info);

  // Updates reasons for the urgency to proceed more.
  void UpdateUrgencyReasons();

  // Updates urgency reasons for the end of traffic light.
  void UpdateUrgencyReasonsForTheEndOfTrafficLight(
      const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
      const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
      const pb::UrgencyInfoSeed& previous_urgency_info_seed,
      std::string* debug_str_ptr);

  // Updates urgency reasons when ego is in upl.
  void UpdateUPLUrgencyReasons(
      const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
      const std::vector<PredictionDecisionMakerInput>& reasoning_inputs);

  // Updates urgency reasons when ego is in u turn.
  void UpdateUTurnUrgencyReasons(const WorldContext& world_context,
                                 const TrajectoryInfo& trajectory_info);

  void UpdateRightHookUrgencyReasons(
      const WorldContext& world_context,
      const std::vector<const traffic_rules::LaneInfoInLaneSequence*>&
          target_right_turn_lanes,
      const std::vector<PredictionDecisionMakerInput>& pdm_inputs,
      const pb::ReasoningConfig& reasoning_config, std::string* debug_str_ptr);

  // Updates urgency reasons when ego will go through crosswalk with dense VRUs.
  void UpdateCrosswalkUrgencyReasons(
      const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
      const pb::UrgencyInfoSeed& previous_iter_seed,
      const std::vector<const traffic_rules::LaneInfoInLaneSequence*>*
          target_right_turn_lanes,
      std::string* debug_str_ptr);

  // Updates the signal indicating whether there are traffic flow moving ahead
  // of stopped ego.
  void UpdateUrgencyReasonsForKeepClearZone(
      const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
      const std::vector<PredictionDecisionMakerInput>& reasoning_inputs);

  void UpdateWaypointUrgencyReasons(const WorldContext& world_context);

  void UpdateCurrentIterSeed(pb::UrgencyInfoSeed& current_seed);

  // Has ego stay stopped and stuck the traffic for a long time.
  bool has_ego_stuck_for_long_time_ = false;

  // Whether ego was proceeding urgently.
  bool was_proceeding_urgently_ = false;

  // Whether ego is driving in a low speed.
  bool is_ego_in_low_speed_ = false;

  // Whether we previously set discomfort for progress in upl.
  bool was_discomfort_for_progress_in_upl_ = false;

  // Whether we previously set discomfort for progress in u-turn.
  bool was_discomfort_for_progress_in_u_turn_ = false;

  bool was_discomfort_for_progress_in_right_hook_ = false;

  // TODO(junying): Store context information rather than policy decision in
  // such class. Whether we should latch the discomfort for progress during UPL.
  bool should_latch_discomfort_for_progress_in_upl_ = false;

  // whether the dominant constraint is stationary.
  bool is_dominant_constraint_stationary_ = false;

  // Whether ego has stuck in the junction since the last red light, indicating
  // a very long blocking of traffic.
  bool has_stuck_in_junction_since_last_red_light_ = false;

  // The reasons for the urgency status.
  std::unordered_set<planner::pb::UrgencyReason::Enum> urgency_reasons_;

  // A vector where each element is a map representing a lane cluster,
  // which includes a neighbor lane and its connected predecessor. Each map in
  // the vector corresponding to a specific neighbor lane cluster, where the
  // key is the lane id of the lane and the value is a list of ids of the
  // objects occupying the lane.
  std::vector<LaneAgentOccupancyMap> neighbor_lanes_agent_occ_maps_;

  // A vector where each element is a map representing a lane cluster,
  // which includes a merge lane and its connected predecessor. Each map in
  // the vector corresponding to a specific merge lane cluster, where the
  // key is the lane id of the lane and the value is a list of ids of the
  // objects occupying the lane.
  // Illustration: https://cooper.didichuxing.com/shares/vmIJTduE5El5
  std::vector<LaneAgentOccupancyMap> merge_lanes_agent_occ_maps_;

  // The vector of the ids of non leading agents interacting with ego.
  std::vector<int64_t> non_leading_interacting_agent_ids_;

  // The crosswalks with dense VRUs and Ego should creep around them;
  std::set<int64_t> creeping_crosswalk_ids_;

  std::optional<math::Range1d> right_hook_ra_arclength_range_;
  // The crosswalks' occupancy info and gap info.
  std::unordered_map<int64_t, CreepTimeGapInfo> crosswalk_id_to_time_gap_info_;
};

}  // namespace speed
}  // namespace planner

#endif  // ONBOARD_PLANNER_SPEED_REASONING_INPUT_URGENCY_INFO_H_
