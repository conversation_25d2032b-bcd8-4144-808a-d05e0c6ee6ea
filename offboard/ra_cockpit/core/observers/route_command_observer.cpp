#include "route_command_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

RouteCommandObserver::RouteCommandObserver(RouteCommandCallback callback)
    : route_command_callback_(std::move(callback)) {}

bool RouteCommandObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kRouteCommand);
}

std::shared_ptr<google::protobuf::Message> RouteCommandObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto route_command = std::make_shared<routing::pb::RouteCommand>();
  if (!route_command->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error RouteCommand message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (route_command_callback_) {
    route_command_callback_(route_command, data_channel);
  }
  return route_command;
}

}  // namespace remote_assist
