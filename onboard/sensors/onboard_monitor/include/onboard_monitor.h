#ifndef ONBOARD_SENSORS_ONBOARD_MONITOR_INCLUDE_ONBOARD_MONITOR_H_
#define ONBOARD_SENSORS_ONBOARD_MONITOR_INCLUDE_ONBOARD_MONITOR_H_

#include <memory>
#include <string>
#include <thread>  // NOLINT

#include "varch/utils/name.h"
#include "varch/vnode/message_util.h"

#include "av_comm/car_id.h"
#include "json/json.hpp"
#include "voy_protos/hardware_monitor.pb.h"
#include "varch/protos/vnode/subscriber_conf.pb.h"

#include "add_data.h"
#include "thread_safe_queue.h"
#include "timesync.h"

namespace onboard_monitor {
constexpr char kMonitorConfigPath[] =
    "/opt/voyager/etc/onboard_monitor/monitor.json";
constexpr char kGetLDPCErrosCmd[] =
    "echo 'didi1234' | sudo -S /opt/quanta_tools/readstat --phy_c | grep "
    "\"LDPC Errors\" | awk '{sum += $NF} END {print sum}'";
constexpr char kGen3Cpubox1[] = "cpubox1";
constexpr char kGen3Cpubox2[] = "cpubox2";

constexpr char kGen4Cpubox[] = "gen4x86";

using varch::vnode::detail::underlay_message::RawMessage;
using varch::vnode::message_util::CreatePublisher;
using varch::vnode::message_util::CreateSubscriber;
using varch::vnode::message_util::Publisher;
using varch::vnode::message_util::Subscriber;

using namespace varch::vnode;
using namespace varch::utils;
using namespace varch::vnode::message_util;
using Publisher = detail::publisher::PublisherWrapper;
using PublisherOption = detail::publisher::PublisherOption;
using Subscriber = detail::subscriber::SubscriberWrapper;
using SubscriberConfig = detail::subscriber::SubscriberConfig;
using TransportType = varch::protos::vnode::TransportType;

using MonitorDataPtr = std::shared_ptr<const monitor::HardwareMonitorData>;
using HardwareMonitorMsgQueue = ThreadSafeQueue<MonitorDataPtr>;

class OnboardMonitor {
 public:
  OnboardMonitor();
  ~OnboardMonitor();

  void Init();
  void Run(boost::asio::io_service* io_service);
  static double GetCurrentTimestamp();

 private:
  void ParseConfig(const std::string& config_path);
  void SetupDomainSocket(std::string& path);
  bool WaitForSocket(std::string& sockPath, int timeout = 30);
  void SubscribeMessage(const std::string& topic_name);
  void MessageCallback(const MonitorDataPtr hardware_status_ptr);
  void HandleMessage();
  void ProcessHardwareStatusMsg(
      std::shared_ptr<const monitor::HardwareMonitorData> hardware_status_ptr);
  void AddMonitorTask();
  std::string IcmpMeasure(const std::string& target_ip, bool upload_metrics,
                          bool with_fault);
  void MeasureTimesyncMetrics();

  void TransferToJson();
  void SendJsonData(const std::string& json_data);
  void ProduceDebugMessage();

  void AddLDPCMonitorTask();
  void InitCarId();
  void InitCarSn();
  std::string GetCarId();
  std::string GetCarSn();

  void PushMonitorData(
      const std::shared_ptr<monitor::HardwareMonitorData>& monitor_data_single);

  static std::string GetHostName();

 private:
  // Socket Members
  int socket_fd_{-1};
  bool debug_mode_{false};
  std::thread socket_thread_;
  std::thread mqtt_thread_;
  std::thread icmp_thread_;
  std::thread message_handle_thread_;
  std::thread timesync_thread_;
  HardwareMonitorMsgQueue msg_queue_;
  std::atomic<bool> stop_thread_;
  nlohmann::json config_json_;

  std::shared_ptr<Subscriber> hardware_monitor_subscriber_;
  std::shared_ptr<Publisher> hardware_monitor_publisher_;
  std::shared_ptr<Publisher> hardware_status_record_publisher_;

  node::FaultReporter fault_reporter_;

  std::string hostname_;
  std::string car_id_;
  std::string car_sn_;
  std::unique_ptr<PhcClock> phc_;
};
}  // namespace onboard_monitor
#endif  // ONBOARD_SENSORS_ONBOARD_MONITOR_INCLUDE_ONBOARD_MONITOR_H_
