#include "varch/tests/tools/launcher/vgraph_checker_test.h"

#include <string>

namespace varch {
namespace tools {
namespace launcher {
namespace {

const char base_path[] =
    "${VOY_DATA_DIR}/varch/tests/tools/launcher/resources/";

TEST_F(VGraphCheckerTest, CorrectVGraph) {
  const std::string vgraph =
      std::string(base_path) + "reality_mode_xtrigger.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), true);
}

TEST_F(VGraphCheckerTest, WhiteListVGraph) {
  const std::string vgraph = std::string(base_path) + "main_lidar_node.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), true);
}

TEST_F(VGraphCheckerTest, DuplicatePubTopics) {
  const std::string vgraph = std::string(base_path) +
                             "invalid_vgraph_with_duplicate_pub_topics.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), false);
}

TEST_F(VGraphCheckerTest, DuplicatePublisherName) {
  const std::string vgraph =
      std::string(base_path) +
      "invalid_vgraph_with_duplicate_publisher_name.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), false);
}

TEST_F(VGraphCheckerTest, DuplicateSubTopics) {
  const std::string vgraph = std::string(base_path) +
                             "invalid_vgraph_with_duplicate_sub_topics.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), false);
}

TEST_F(VGraphCheckerTest, DuplicateVNodeName) {
  const std::string vgraph =
      std::string(base_path) + "reality_mode_xtrigger.vgraph";
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), true);
  // Loading the same VGraph twice leads to NoDuplicateVNodeName check
  // failure.
  EXPECT_EQ(GetVGraphChecker().IsVGraphValid(vgraph), false);
}

}  // namespace
}  // namespace launcher
}  // namespace tools
}  // namespace varch
