#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAFFIC_LIGHTS_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAFFIC_LIGHTS_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace remote_assist {

class TrafficLightsObserver : public IDataObserver {
 public:
  using TrafficLightsCallback =
      std::function<void(std::shared_ptr<voy::TrafficLights>, DataChannel)>;

  explicit TrafficLightsObserver(TrafficLightsCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  TrafficLightsCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_TRAFFIC_LIGHTS_OBSERVER_H_
