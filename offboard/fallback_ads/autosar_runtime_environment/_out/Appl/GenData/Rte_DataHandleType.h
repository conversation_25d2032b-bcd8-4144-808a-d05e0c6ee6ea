/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_DataHandleType.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Header file containing Data Handle type declarations for component data structures
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_DATA_HANDLE_TYPE_H
# define RTE_DATA_HANDLE_TYPE_H


/**********************************************************************************************************************
 * Type definitions for implicit access to S/R communication
 *********************************************************************************************************************/
/* Types for Data Handles section */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuControl value;
} Rte_DE_AcuControl;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseInput value;
} Rte_DE_GnssPoseInput;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LocalizationPoseInput value;
} Rte_DE_LocalizationPoseInput;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  uint32 value;
} Rte_DE_uint32;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInput value;
} Rte_DE_TrajectoryInput;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuPoseRollingCounter value;
} Rte_DE_AcuPoseRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseHeading value;
} Rte_DE_GnssPoseHeading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseHeadingType value;
} Rte_DE_GnssPoseHeadingType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseNumSatsTracked value;
} Rte_DE_GnssPoseNumSatsTracked;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPosePosType value;
} Rte_DE_GnssPosePosType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPosePositionX value;
} Rte_DE_GnssPosePositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPosePositionY value;
} Rte_DE_GnssPosePositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseReserve1 value;
} Rte_DE_GnssPoseReserve1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseTimeStamp value;
} Rte_DE_GnssPoseTimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseVelocityX value;
} Rte_DE_GnssPoseVelocityX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseVelocityY value;
} Rte_DE_GnssPoseVelocityY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnssPoseYaw value;
} Rte_DE_GnssPoseYaw;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GnsssPoseReserve0 value;
} Rte_DE_GnsssPoseReserve0;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseAccelerationX value;
} Rte_DE_PoseAccelerationX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseAccelerationY value;
} Rte_DE_PoseAccelerationY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PosePitch value;
} Rte_DE_PosePitch;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PosePositionX value;
} Rte_DE_PosePositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PosePositionY value;
} Rte_DE_PosePositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseStatus value;
} Rte_DE_PoseStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseTimeStamp value;
} Rte_DE_PoseTimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseVelocityX value;
} Rte_DE_PoseVelocityX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseVelocityY value;
} Rte_DE_PoseVelocityY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseYaw value;
} Rte_DE_PoseYaw;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseYawRate value;
} Rte_DE_PoseYawRate;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuControlTimestamp value;
} Rte_DE_AcuControlTimestamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlWheelAngleOffset value;
} Rte_DE_ControlWheelAngleOffset;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EgoLaneLightColor value;
} Rte_DE_EgoLaneLightColor;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontObjectTTC value;
} Rte_DE_FrontObjectTTC;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontObjectValid value;
} Rte_DE_FrontObjectValid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  IsInTheHighway value;
} Rte_DE_IsInTheHighway;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeftRoadEdgeLatDist value;
} Rte_DE_LeftRoadEdgeLatDist;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeftRoadEdgeLength value;
} Rte_DE_LeftRoadEdgeLength;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeftRoadEdgeValid value;
} Rte_DE_LeftRoadEdgeValid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RearObjectTTC value;
} Rte_DE_RearObjectTTC;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RearObjectValid value;
} Rte_DE_RearObjectValid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RightRoadEdgeLatDist value;
} Rte_DE_RightRoadEdgeLatDist;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RightRoadEdgeLength value;
} Rte_DE_RightRoadEdgeLength;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RightRoadEdgeValid value;
} Rte_DE_RightRoadEdgeValid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  StopLineLeftEdgeX value;
} Rte_DE_StopLineLeftEdgeX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  StopLineLeftEdgeY value;
} Rte_DE_StopLineLeftEdgeY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  StopLineRightEdgeX value;
} Rte_DE_StopLineRightEdgeX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  StopLineRightEdgeY value;
} Rte_DE_StopLineRightEdgeY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  StopLineValid value;
} Rte_DE_StopLineValid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoHarzardLight value;
} Rte_DE_TrajectoryInfoHarzardLight;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoHorn value;
} Rte_DE_TrajectoryInfoHorn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoInitTimeStamp value;
} Rte_DE_TrajectoryInfoInitTimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoLaneChange value;
} Rte_DE_TrajectoryInfoLaneChange;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoMotion value;
} Rte_DE_TrajectoryInfoMotion;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoPlanningStatus value;
} Rte_DE_TrajectoryInfoPlanningStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve0 value;
} Rte_DE_TrajectoryInfoReserve0;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve1 value;
} Rte_DE_TrajectoryInfoReserve1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve2 value;
} Rte_DE_TrajectoryInfoReserve2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve3 value;
} Rte_DE_TrajectoryInfoReserve3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve4 value;
} Rte_DE_TrajectoryInfoReserve4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve5 value;
} Rte_DE_TrajectoryInfoReserve5;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoReserve6 value;
} Rte_DE_TrajectoryInfoReserve6;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryInfoRollingCounter value;
} Rte_DE_TrajectoryInfoRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00Acceleration value;
} Rte_DE_TrajectoryPose00Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00Curvature value;
} Rte_DE_TrajectoryPose00Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00Heading value;
} Rte_DE_TrajectoryPose00Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00PositionX value;
} Rte_DE_TrajectoryPose00PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00PositionY value;
} Rte_DE_TrajectoryPose00PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00Speed value;
} Rte_DE_TrajectoryPose00Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose00Steering value;
} Rte_DE_TrajectoryPose00Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01Acceleration value;
} Rte_DE_TrajectoryPose01Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01Curvature value;
} Rte_DE_TrajectoryPose01Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01Heading value;
} Rte_DE_TrajectoryPose01Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01PositionX value;
} Rte_DE_TrajectoryPose01PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01PositionY value;
} Rte_DE_TrajectoryPose01PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01Speed value;
} Rte_DE_TrajectoryPose01Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose01Steering value;
} Rte_DE_TrajectoryPose01Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02Acceleration value;
} Rte_DE_TrajectoryPose02Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02Curvature value;
} Rte_DE_TrajectoryPose02Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02Heading value;
} Rte_DE_TrajectoryPose02Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02PositionX value;
} Rte_DE_TrajectoryPose02PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02PositionY value;
} Rte_DE_TrajectoryPose02PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02Speed value;
} Rte_DE_TrajectoryPose02Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose02Steering value;
} Rte_DE_TrajectoryPose02Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03Acceleration value;
} Rte_DE_TrajectoryPose03Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03Curvature value;
} Rte_DE_TrajectoryPose03Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03Heading value;
} Rte_DE_TrajectoryPose03Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03PositionX value;
} Rte_DE_TrajectoryPose03PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03PositionY value;
} Rte_DE_TrajectoryPose03PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03Speed value;
} Rte_DE_TrajectoryPose03Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose03Steering value;
} Rte_DE_TrajectoryPose03Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04Acceleration value;
} Rte_DE_TrajectoryPose04Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04Curvature value;
} Rte_DE_TrajectoryPose04Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04Heading value;
} Rte_DE_TrajectoryPose04Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04PositionX value;
} Rte_DE_TrajectoryPose04PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04PositionY value;
} Rte_DE_TrajectoryPose04PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04Speed value;
} Rte_DE_TrajectoryPose04Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose04Steering value;
} Rte_DE_TrajectoryPose04Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05Acceleration value;
} Rte_DE_TrajectoryPose05Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05Curvature value;
} Rte_DE_TrajectoryPose05Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05Heading value;
} Rte_DE_TrajectoryPose05Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05PositionX value;
} Rte_DE_TrajectoryPose05PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05PositionY value;
} Rte_DE_TrajectoryPose05PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05Speed value;
} Rte_DE_TrajectoryPose05Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose05Steering value;
} Rte_DE_TrajectoryPose05Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06Acceleration value;
} Rte_DE_TrajectoryPose06Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06Curvature value;
} Rte_DE_TrajectoryPose06Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06Heading value;
} Rte_DE_TrajectoryPose06Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06PositionX value;
} Rte_DE_TrajectoryPose06PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06PositionY value;
} Rte_DE_TrajectoryPose06PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06Speed value;
} Rte_DE_TrajectoryPose06Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose06Steering value;
} Rte_DE_TrajectoryPose06Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07Acceleration value;
} Rte_DE_TrajectoryPose07Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07Curvature value;
} Rte_DE_TrajectoryPose07Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07Heading value;
} Rte_DE_TrajectoryPose07Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07PositionX value;
} Rte_DE_TrajectoryPose07PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07PositionY value;
} Rte_DE_TrajectoryPose07PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07Speed value;
} Rte_DE_TrajectoryPose07Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose07Steering value;
} Rte_DE_TrajectoryPose07Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08Acceleration value;
} Rte_DE_TrajectoryPose08Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08Curvature value;
} Rte_DE_TrajectoryPose08Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08Heading value;
} Rte_DE_TrajectoryPose08Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08PositionX value;
} Rte_DE_TrajectoryPose08PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08PositionY value;
} Rte_DE_TrajectoryPose08PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08Speed value;
} Rte_DE_TrajectoryPose08Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose08Steering value;
} Rte_DE_TrajectoryPose08Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09Acceleration value;
} Rte_DE_TrajectoryPose09Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09Curvature value;
} Rte_DE_TrajectoryPose09Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09Heading value;
} Rte_DE_TrajectoryPose09Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09PositionX value;
} Rte_DE_TrajectoryPose09PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09PositionY value;
} Rte_DE_TrajectoryPose09PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09Speed value;
} Rte_DE_TrajectoryPose09Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose09Steering value;
} Rte_DE_TrajectoryPose09Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10Acceleration value;
} Rte_DE_TrajectoryPose10Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10Curvature value;
} Rte_DE_TrajectoryPose10Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10Heading value;
} Rte_DE_TrajectoryPose10Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10PositionX value;
} Rte_DE_TrajectoryPose10PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10PositionY value;
} Rte_DE_TrajectoryPose10PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10Speed value;
} Rte_DE_TrajectoryPose10Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose10Steering value;
} Rte_DE_TrajectoryPose10Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11Acceleration value;
} Rte_DE_TrajectoryPose11Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11Curvature value;
} Rte_DE_TrajectoryPose11Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11Heading value;
} Rte_DE_TrajectoryPose11Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11PositionX value;
} Rte_DE_TrajectoryPose11PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11PositionY value;
} Rte_DE_TrajectoryPose11PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11Speed value;
} Rte_DE_TrajectoryPose11Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose11Steering value;
} Rte_DE_TrajectoryPose11Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12Acceleration value;
} Rte_DE_TrajectoryPose12Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12Curvature value;
} Rte_DE_TrajectoryPose12Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12Heading value;
} Rte_DE_TrajectoryPose12Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12PositionX value;
} Rte_DE_TrajectoryPose12PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12PositionY value;
} Rte_DE_TrajectoryPose12PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12Speed value;
} Rte_DE_TrajectoryPose12Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose12Steering value;
} Rte_DE_TrajectoryPose12Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13Acceleration value;
} Rte_DE_TrajectoryPose13Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13Curvature value;
} Rte_DE_TrajectoryPose13Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13Heading value;
} Rte_DE_TrajectoryPose13Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13PositionX value;
} Rte_DE_TrajectoryPose13PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13PositionY value;
} Rte_DE_TrajectoryPose13PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13Speed value;
} Rte_DE_TrajectoryPose13Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose13Steering value;
} Rte_DE_TrajectoryPose13Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14Acceleration value;
} Rte_DE_TrajectoryPose14Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14Curvature value;
} Rte_DE_TrajectoryPose14Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14Heading value;
} Rte_DE_TrajectoryPose14Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14PositionX value;
} Rte_DE_TrajectoryPose14PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14PositionY value;
} Rte_DE_TrajectoryPose14PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14Speed value;
} Rte_DE_TrajectoryPose14Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose14Steering value;
} Rte_DE_TrajectoryPose14Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15Acceleration value;
} Rte_DE_TrajectoryPose15Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15Curvature value;
} Rte_DE_TrajectoryPose15Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15Heading value;
} Rte_DE_TrajectoryPose15Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15PositionX value;
} Rte_DE_TrajectoryPose15PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15PositionY value;
} Rte_DE_TrajectoryPose15PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15Speed value;
} Rte_DE_TrajectoryPose15Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose15Steering value;
} Rte_DE_TrajectoryPose15Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16Acceleration value;
} Rte_DE_TrajectoryPose16Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16Curvature value;
} Rte_DE_TrajectoryPose16Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16Heading value;
} Rte_DE_TrajectoryPose16Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16PositionX value;
} Rte_DE_TrajectoryPose16PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16PositionY value;
} Rte_DE_TrajectoryPose16PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16Speed value;
} Rte_DE_TrajectoryPose16Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose16Steering value;
} Rte_DE_TrajectoryPose16Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17Acceleration value;
} Rte_DE_TrajectoryPose17Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17Curvature value;
} Rte_DE_TrajectoryPose17Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17Heading value;
} Rte_DE_TrajectoryPose17Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17PositionX value;
} Rte_DE_TrajectoryPose17PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17PositionY value;
} Rte_DE_TrajectoryPose17PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17Speed value;
} Rte_DE_TrajectoryPose17Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose17Steering value;
} Rte_DE_TrajectoryPose17Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18Acceleration value;
} Rte_DE_TrajectoryPose18Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18Curvature value;
} Rte_DE_TrajectoryPose18Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18Heading value;
} Rte_DE_TrajectoryPose18Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18PositionX value;
} Rte_DE_TrajectoryPose18PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18PositionY value;
} Rte_DE_TrajectoryPose18PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18Speed value;
} Rte_DE_TrajectoryPose18Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose18Steering value;
} Rte_DE_TrajectoryPose18Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19Acceleration value;
} Rte_DE_TrajectoryPose19Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19Curvature value;
} Rte_DE_TrajectoryPose19Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19Heading value;
} Rte_DE_TrajectoryPose19Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19PositionX value;
} Rte_DE_TrajectoryPose19PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19PositionY value;
} Rte_DE_TrajectoryPose19PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19Speed value;
} Rte_DE_TrajectoryPose19Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose19Steering value;
} Rte_DE_TrajectoryPose19Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20Acceleration value;
} Rte_DE_TrajectoryPose20Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20Curvature value;
} Rte_DE_TrajectoryPose20Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20Heading value;
} Rte_DE_TrajectoryPose20Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20PositionX value;
} Rte_DE_TrajectoryPose20PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20PositionY value;
} Rte_DE_TrajectoryPose20PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20Speed value;
} Rte_DE_TrajectoryPose20Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose20Steering value;
} Rte_DE_TrajectoryPose20Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21Acceleration value;
} Rte_DE_TrajectoryPose21Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21Curvature value;
} Rte_DE_TrajectoryPose21Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21Heading value;
} Rte_DE_TrajectoryPose21Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21PositionX value;
} Rte_DE_TrajectoryPose21PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21PositionY value;
} Rte_DE_TrajectoryPose21PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21Speed value;
} Rte_DE_TrajectoryPose21Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose21Steering value;
} Rte_DE_TrajectoryPose21Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22Acceleration value;
} Rte_DE_TrajectoryPose22Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22Curvature value;
} Rte_DE_TrajectoryPose22Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22Heading value;
} Rte_DE_TrajectoryPose22Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22PositionX value;
} Rte_DE_TrajectoryPose22PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22PositionY value;
} Rte_DE_TrajectoryPose22PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22Speed value;
} Rte_DE_TrajectoryPose22Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose22Steering value;
} Rte_DE_TrajectoryPose22Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23Acceleration value;
} Rte_DE_TrajectoryPose23Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23Curvature value;
} Rte_DE_TrajectoryPose23Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23Heading value;
} Rte_DE_TrajectoryPose23Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23PositionX value;
} Rte_DE_TrajectoryPose23PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23PositionY value;
} Rte_DE_TrajectoryPose23PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23Speed value;
} Rte_DE_TrajectoryPose23Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose23Steering value;
} Rte_DE_TrajectoryPose23Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24Acceleration value;
} Rte_DE_TrajectoryPose24Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24Curvature value;
} Rte_DE_TrajectoryPose24Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24Heading value;
} Rte_DE_TrajectoryPose24Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24PositionX value;
} Rte_DE_TrajectoryPose24PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24PositionY value;
} Rte_DE_TrajectoryPose24PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24Speed value;
} Rte_DE_TrajectoryPose24Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose24Steering value;
} Rte_DE_TrajectoryPose24Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25Acceleration value;
} Rte_DE_TrajectoryPose25Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25Curvature value;
} Rte_DE_TrajectoryPose25Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25Heading value;
} Rte_DE_TrajectoryPose25Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25PositionX value;
} Rte_DE_TrajectoryPose25PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25PositionY value;
} Rte_DE_TrajectoryPose25PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25Speed value;
} Rte_DE_TrajectoryPose25Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose25Steering value;
} Rte_DE_TrajectoryPose25Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26Acceleration value;
} Rte_DE_TrajectoryPose26Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26Curvature value;
} Rte_DE_TrajectoryPose26Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26Heading value;
} Rte_DE_TrajectoryPose26Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26PositionX value;
} Rte_DE_TrajectoryPose26PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26PositionY value;
} Rte_DE_TrajectoryPose26PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26Speed value;
} Rte_DE_TrajectoryPose26Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose26Steering value;
} Rte_DE_TrajectoryPose26Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27Acceleration value;
} Rte_DE_TrajectoryPose27Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27Curvature value;
} Rte_DE_TrajectoryPose27Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27Heading value;
} Rte_DE_TrajectoryPose27Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27PositionX value;
} Rte_DE_TrajectoryPose27PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27PositionY value;
} Rte_DE_TrajectoryPose27PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27Speed value;
} Rte_DE_TrajectoryPose27Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose27Steering value;
} Rte_DE_TrajectoryPose27Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28Acceleration value;
} Rte_DE_TrajectoryPose28Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28Curvature value;
} Rte_DE_TrajectoryPose28Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28Heading value;
} Rte_DE_TrajectoryPose28Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28PositionX value;
} Rte_DE_TrajectoryPose28PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28PositionY value;
} Rte_DE_TrajectoryPose28PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28Speed value;
} Rte_DE_TrajectoryPose28Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose28Steering value;
} Rte_DE_TrajectoryPose28Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29Acceleration value;
} Rte_DE_TrajectoryPose29Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29Curvature value;
} Rte_DE_TrajectoryPose29Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29Heading value;
} Rte_DE_TrajectoryPose29Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29PositionX value;
} Rte_DE_TrajectoryPose29PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29PositionY value;
} Rte_DE_TrajectoryPose29PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29Speed value;
} Rte_DE_TrajectoryPose29Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose29Steering value;
} Rte_DE_TrajectoryPose29Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30Acceleration value;
} Rte_DE_TrajectoryPose30Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30Curvature value;
} Rte_DE_TrajectoryPose30Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30Heading value;
} Rte_DE_TrajectoryPose30Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30PositionX value;
} Rte_DE_TrajectoryPose30PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30PositionY value;
} Rte_DE_TrajectoryPose30PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30Speed value;
} Rte_DE_TrajectoryPose30Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose30Steering value;
} Rte_DE_TrajectoryPose30Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31Acceleration value;
} Rte_DE_TrajectoryPose31Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31Curvature value;
} Rte_DE_TrajectoryPose31Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31Heading value;
} Rte_DE_TrajectoryPose31Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31PositionX value;
} Rte_DE_TrajectoryPose31PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31PositionY value;
} Rte_DE_TrajectoryPose31PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31Speed value;
} Rte_DE_TrajectoryPose31Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose31Steering value;
} Rte_DE_TrajectoryPose31Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32Acceleration value;
} Rte_DE_TrajectoryPose32Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32Curvature value;
} Rte_DE_TrajectoryPose32Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32Heading value;
} Rte_DE_TrajectoryPose32Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32PositionX value;
} Rte_DE_TrajectoryPose32PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32PositionY value;
} Rte_DE_TrajectoryPose32PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32Speed value;
} Rte_DE_TrajectoryPose32Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose32Steering value;
} Rte_DE_TrajectoryPose32Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33Acceleration value;
} Rte_DE_TrajectoryPose33Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33Curvature value;
} Rte_DE_TrajectoryPose33Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33Heading value;
} Rte_DE_TrajectoryPose33Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33PositionX value;
} Rte_DE_TrajectoryPose33PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33PositionY value;
} Rte_DE_TrajectoryPose33PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33Speed value;
} Rte_DE_TrajectoryPose33Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose33Steering value;
} Rte_DE_TrajectoryPose33Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34Acceleration value;
} Rte_DE_TrajectoryPose34Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34Curvature value;
} Rte_DE_TrajectoryPose34Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34Heading value;
} Rte_DE_TrajectoryPose34Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34PositionX value;
} Rte_DE_TrajectoryPose34PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34PositionY value;
} Rte_DE_TrajectoryPose34PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34Speed value;
} Rte_DE_TrajectoryPose34Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose34Steering value;
} Rte_DE_TrajectoryPose34Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35Acceleration value;
} Rte_DE_TrajectoryPose35Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35Curvature value;
} Rte_DE_TrajectoryPose35Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35Heading value;
} Rte_DE_TrajectoryPose35Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35PositionX value;
} Rte_DE_TrajectoryPose35PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35PositionY value;
} Rte_DE_TrajectoryPose35PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35Speed value;
} Rte_DE_TrajectoryPose35Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose35Steering value;
} Rte_DE_TrajectoryPose35Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36Acceleration value;
} Rte_DE_TrajectoryPose36Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36Curvature value;
} Rte_DE_TrajectoryPose36Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36Heading value;
} Rte_DE_TrajectoryPose36Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36PositionX value;
} Rte_DE_TrajectoryPose36PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36PositionY value;
} Rte_DE_TrajectoryPose36PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36Speed value;
} Rte_DE_TrajectoryPose36Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose36Steering value;
} Rte_DE_TrajectoryPose36Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37Acceleration value;
} Rte_DE_TrajectoryPose37Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37Curvature value;
} Rte_DE_TrajectoryPose37Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37Heading value;
} Rte_DE_TrajectoryPose37Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37PositionX value;
} Rte_DE_TrajectoryPose37PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37PositionY value;
} Rte_DE_TrajectoryPose37PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37Speed value;
} Rte_DE_TrajectoryPose37Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose37Steering value;
} Rte_DE_TrajectoryPose37Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38Acceleration value;
} Rte_DE_TrajectoryPose38Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38Curvature value;
} Rte_DE_TrajectoryPose38Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38Heading value;
} Rte_DE_TrajectoryPose38Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38PositionX value;
} Rte_DE_TrajectoryPose38PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38PositionY value;
} Rte_DE_TrajectoryPose38PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38Speed value;
} Rte_DE_TrajectoryPose38Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose38Steering value;
} Rte_DE_TrajectoryPose38Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39Acceleration value;
} Rte_DE_TrajectoryPose39Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39Curvature value;
} Rte_DE_TrajectoryPose39Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39Heading value;
} Rte_DE_TrajectoryPose39Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39PositionX value;
} Rte_DE_TrajectoryPose39PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39PositionY value;
} Rte_DE_TrajectoryPose39PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39Speed value;
} Rte_DE_TrajectoryPose39Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose39Steering value;
} Rte_DE_TrajectoryPose39Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40Acceleration value;
} Rte_DE_TrajectoryPose40Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40Curvature value;
} Rte_DE_TrajectoryPose40Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40Heading value;
} Rte_DE_TrajectoryPose40Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40PositionX value;
} Rte_DE_TrajectoryPose40PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40PositionY value;
} Rte_DE_TrajectoryPose40PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40Speed value;
} Rte_DE_TrajectoryPose40Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose40Steering value;
} Rte_DE_TrajectoryPose40Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41Acceleration value;
} Rte_DE_TrajectoryPose41Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41Curvature value;
} Rte_DE_TrajectoryPose41Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41Heading value;
} Rte_DE_TrajectoryPose41Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41PositionX value;
} Rte_DE_TrajectoryPose41PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41PositionY value;
} Rte_DE_TrajectoryPose41PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41Speed value;
} Rte_DE_TrajectoryPose41Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose41Steering value;
} Rte_DE_TrajectoryPose41Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42Acceleration value;
} Rte_DE_TrajectoryPose42Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42Curvature value;
} Rte_DE_TrajectoryPose42Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42Heading value;
} Rte_DE_TrajectoryPose42Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42PositionX value;
} Rte_DE_TrajectoryPose42PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42PositionY value;
} Rte_DE_TrajectoryPose42PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42Speed value;
} Rte_DE_TrajectoryPose42Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose42Steering value;
} Rte_DE_TrajectoryPose42Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43Acceleration value;
} Rte_DE_TrajectoryPose43Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43Curvature value;
} Rte_DE_TrajectoryPose43Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43Heading value;
} Rte_DE_TrajectoryPose43Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43PositionX value;
} Rte_DE_TrajectoryPose43PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43PositionY value;
} Rte_DE_TrajectoryPose43PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43Speed value;
} Rte_DE_TrajectoryPose43Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose43Steering value;
} Rte_DE_TrajectoryPose43Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44Acceleration value;
} Rte_DE_TrajectoryPose44Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44Curvature value;
} Rte_DE_TrajectoryPose44Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44Heading value;
} Rte_DE_TrajectoryPose44Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44PositionX value;
} Rte_DE_TrajectoryPose44PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44PositionY value;
} Rte_DE_TrajectoryPose44PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44Speed value;
} Rte_DE_TrajectoryPose44Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose44Steering value;
} Rte_DE_TrajectoryPose44Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45Acceleration value;
} Rte_DE_TrajectoryPose45Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45Curvature value;
} Rte_DE_TrajectoryPose45Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45Heading value;
} Rte_DE_TrajectoryPose45Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45PositionX value;
} Rte_DE_TrajectoryPose45PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45PositionY value;
} Rte_DE_TrajectoryPose45PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45Speed value;
} Rte_DE_TrajectoryPose45Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose45Steering value;
} Rte_DE_TrajectoryPose45Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46Acceleration value;
} Rte_DE_TrajectoryPose46Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46Curvature value;
} Rte_DE_TrajectoryPose46Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46Heading value;
} Rte_DE_TrajectoryPose46Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46PositionX value;
} Rte_DE_TrajectoryPose46PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46PositionY value;
} Rte_DE_TrajectoryPose46PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46Speed value;
} Rte_DE_TrajectoryPose46Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose46Steering value;
} Rte_DE_TrajectoryPose46Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47Acceleration value;
} Rte_DE_TrajectoryPose47Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47Curvature value;
} Rte_DE_TrajectoryPose47Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47Heading value;
} Rte_DE_TrajectoryPose47Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47PositionX value;
} Rte_DE_TrajectoryPose47PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47PositionY value;
} Rte_DE_TrajectoryPose47PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47Speed value;
} Rte_DE_TrajectoryPose47Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose47Steering value;
} Rte_DE_TrajectoryPose47Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48Acceleration value;
} Rte_DE_TrajectoryPose48Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48Curvature value;
} Rte_DE_TrajectoryPose48Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48Heading value;
} Rte_DE_TrajectoryPose48Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48PositionX value;
} Rte_DE_TrajectoryPose48PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48PositionY value;
} Rte_DE_TrajectoryPose48PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48Speed value;
} Rte_DE_TrajectoryPose48Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose48Steering value;
} Rte_DE_TrajectoryPose48Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49Acceleration value;
} Rte_DE_TrajectoryPose49Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49Curvature value;
} Rte_DE_TrajectoryPose49Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49Heading value;
} Rte_DE_TrajectoryPose49Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49PositionX value;
} Rte_DE_TrajectoryPose49PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49PositionY value;
} Rte_DE_TrajectoryPose49PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49Speed value;
} Rte_DE_TrajectoryPose49Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose49Steering value;
} Rte_DE_TrajectoryPose49Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50Acceleration value;
} Rte_DE_TrajectoryPose50Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50Curvature value;
} Rte_DE_TrajectoryPose50Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50Heading value;
} Rte_DE_TrajectoryPose50Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50PositionX value;
} Rte_DE_TrajectoryPose50PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50PositionY value;
} Rte_DE_TrajectoryPose50PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50Speed value;
} Rte_DE_TrajectoryPose50Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose50Steering value;
} Rte_DE_TrajectoryPose50Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51Acceleration value;
} Rte_DE_TrajectoryPose51Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51Curvature value;
} Rte_DE_TrajectoryPose51Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51Heading value;
} Rte_DE_TrajectoryPose51Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51PositionX value;
} Rte_DE_TrajectoryPose51PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51PositionY value;
} Rte_DE_TrajectoryPose51PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51Speed value;
} Rte_DE_TrajectoryPose51Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose51Steering value;
} Rte_DE_TrajectoryPose51Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52Acceleration value;
} Rte_DE_TrajectoryPose52Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52Curvature value;
} Rte_DE_TrajectoryPose52Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52Heading value;
} Rte_DE_TrajectoryPose52Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52PositionX value;
} Rte_DE_TrajectoryPose52PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52PositionY value;
} Rte_DE_TrajectoryPose52PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52Speed value;
} Rte_DE_TrajectoryPose52Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose52Steering value;
} Rte_DE_TrajectoryPose52Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53Acceleration value;
} Rte_DE_TrajectoryPose53Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53Curvature value;
} Rte_DE_TrajectoryPose53Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53Heading value;
} Rte_DE_TrajectoryPose53Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53PositionX value;
} Rte_DE_TrajectoryPose53PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53PositionY value;
} Rte_DE_TrajectoryPose53PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53Speed value;
} Rte_DE_TrajectoryPose53Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose53Steering value;
} Rte_DE_TrajectoryPose53Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54Acceleration value;
} Rte_DE_TrajectoryPose54Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54Curvature value;
} Rte_DE_TrajectoryPose54Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54Heading value;
} Rte_DE_TrajectoryPose54Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54PositionX value;
} Rte_DE_TrajectoryPose54PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54PositionY value;
} Rte_DE_TrajectoryPose54PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54Speed value;
} Rte_DE_TrajectoryPose54Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose54Steering value;
} Rte_DE_TrajectoryPose54Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55Acceleration value;
} Rte_DE_TrajectoryPose55Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55Curvature value;
} Rte_DE_TrajectoryPose55Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55Heading value;
} Rte_DE_TrajectoryPose55Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55PositionX value;
} Rte_DE_TrajectoryPose55PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55PositionY value;
} Rte_DE_TrajectoryPose55PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55Speed value;
} Rte_DE_TrajectoryPose55Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose55Steering value;
} Rte_DE_TrajectoryPose55Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56Acceleration value;
} Rte_DE_TrajectoryPose56Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56Curvature value;
} Rte_DE_TrajectoryPose56Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56Heading value;
} Rte_DE_TrajectoryPose56Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56PositionX value;
} Rte_DE_TrajectoryPose56PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56PositionY value;
} Rte_DE_TrajectoryPose56PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56Speed value;
} Rte_DE_TrajectoryPose56Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose56Steering value;
} Rte_DE_TrajectoryPose56Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57Acceleration value;
} Rte_DE_TrajectoryPose57Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57Curvature value;
} Rte_DE_TrajectoryPose57Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57Heading value;
} Rte_DE_TrajectoryPose57Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57PositionX value;
} Rte_DE_TrajectoryPose57PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57PositionY value;
} Rte_DE_TrajectoryPose57PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57Speed value;
} Rte_DE_TrajectoryPose57Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose57Steering value;
} Rte_DE_TrajectoryPose57Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58Acceleration value;
} Rte_DE_TrajectoryPose58Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58Curvature value;
} Rte_DE_TrajectoryPose58Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58Heading value;
} Rte_DE_TrajectoryPose58Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58PositionX value;
} Rte_DE_TrajectoryPose58PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58PositionY value;
} Rte_DE_TrajectoryPose58PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58Speed value;
} Rte_DE_TrajectoryPose58Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose58Steering value;
} Rte_DE_TrajectoryPose58Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59Acceleration value;
} Rte_DE_TrajectoryPose59Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59Curvature value;
} Rte_DE_TrajectoryPose59Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59Heading value;
} Rte_DE_TrajectoryPose59Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59PositionX value;
} Rte_DE_TrajectoryPose59PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59PositionY value;
} Rte_DE_TrajectoryPose59PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59Speed value;
} Rte_DE_TrajectoryPose59Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose59Steering value;
} Rte_DE_TrajectoryPose59Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60Acceleration value;
} Rte_DE_TrajectoryPose60Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60Curvature value;
} Rte_DE_TrajectoryPose60Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60Heading value;
} Rte_DE_TrajectoryPose60Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60PositionX value;
} Rte_DE_TrajectoryPose60PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60PositionY value;
} Rte_DE_TrajectoryPose60PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60Speed value;
} Rte_DE_TrajectoryPose60Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose60Steering value;
} Rte_DE_TrajectoryPose60Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61Acceleration value;
} Rte_DE_TrajectoryPose61Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61Curvature value;
} Rte_DE_TrajectoryPose61Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61Heading value;
} Rte_DE_TrajectoryPose61Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61PositionX value;
} Rte_DE_TrajectoryPose61PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61PositionY value;
} Rte_DE_TrajectoryPose61PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61Speed value;
} Rte_DE_TrajectoryPose61Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose61Steering value;
} Rte_DE_TrajectoryPose61Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62Acceleration value;
} Rte_DE_TrajectoryPose62Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62Curvature value;
} Rte_DE_TrajectoryPose62Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62Heading value;
} Rte_DE_TrajectoryPose62Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62PositionX value;
} Rte_DE_TrajectoryPose62PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62PositionY value;
} Rte_DE_TrajectoryPose62PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62Speed value;
} Rte_DE_TrajectoryPose62Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose62Steering value;
} Rte_DE_TrajectoryPose62Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63Acceleration value;
} Rte_DE_TrajectoryPose63Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63Curvature value;
} Rte_DE_TrajectoryPose63Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63Heading value;
} Rte_DE_TrajectoryPose63Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63PositionX value;
} Rte_DE_TrajectoryPose63PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63PositionY value;
} Rte_DE_TrajectoryPose63PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63Speed value;
} Rte_DE_TrajectoryPose63Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose63Steering value;
} Rte_DE_TrajectoryPose63Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64Acceleration value;
} Rte_DE_TrajectoryPose64Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64Curvature value;
} Rte_DE_TrajectoryPose64Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64Heading value;
} Rte_DE_TrajectoryPose64Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64PositionX value;
} Rte_DE_TrajectoryPose64PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64PositionY value;
} Rte_DE_TrajectoryPose64PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64Speed value;
} Rte_DE_TrajectoryPose64Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose64Steering value;
} Rte_DE_TrajectoryPose64Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65Acceleration value;
} Rte_DE_TrajectoryPose65Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65Curvature value;
} Rte_DE_TrajectoryPose65Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65Heading value;
} Rte_DE_TrajectoryPose65Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65PositionX value;
} Rte_DE_TrajectoryPose65PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65PositionY value;
} Rte_DE_TrajectoryPose65PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65Speed value;
} Rte_DE_TrajectoryPose65Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose65Steering value;
} Rte_DE_TrajectoryPose65Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66Acceleration value;
} Rte_DE_TrajectoryPose66Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66Curvature value;
} Rte_DE_TrajectoryPose66Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66Heading value;
} Rte_DE_TrajectoryPose66Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66PositionX value;
} Rte_DE_TrajectoryPose66PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66PositionY value;
} Rte_DE_TrajectoryPose66PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66Speed value;
} Rte_DE_TrajectoryPose66Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose66Steering value;
} Rte_DE_TrajectoryPose66Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67Acceleration value;
} Rte_DE_TrajectoryPose67Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67Curvature value;
} Rte_DE_TrajectoryPose67Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67Heading value;
} Rte_DE_TrajectoryPose67Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67PositionX value;
} Rte_DE_TrajectoryPose67PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67PositionY value;
} Rte_DE_TrajectoryPose67PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67Speed value;
} Rte_DE_TrajectoryPose67Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose67Steering value;
} Rte_DE_TrajectoryPose67Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68Acceleration value;
} Rte_DE_TrajectoryPose68Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68Curvature value;
} Rte_DE_TrajectoryPose68Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68Heading value;
} Rte_DE_TrajectoryPose68Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68PositionX value;
} Rte_DE_TrajectoryPose68PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68PositionY value;
} Rte_DE_TrajectoryPose68PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68Speed value;
} Rte_DE_TrajectoryPose68Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose68Steering value;
} Rte_DE_TrajectoryPose68Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69Acceleration value;
} Rte_DE_TrajectoryPose69Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69Curvature value;
} Rte_DE_TrajectoryPose69Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69Heading value;
} Rte_DE_TrajectoryPose69Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69PositionX value;
} Rte_DE_TrajectoryPose69PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69PositionY value;
} Rte_DE_TrajectoryPose69PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69Speed value;
} Rte_DE_TrajectoryPose69Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose69Steering value;
} Rte_DE_TrajectoryPose69Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70Acceleration value;
} Rte_DE_TrajectoryPose70Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70Curvature value;
} Rte_DE_TrajectoryPose70Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70Heading value;
} Rte_DE_TrajectoryPose70Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70PositionX value;
} Rte_DE_TrajectoryPose70PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70PositionY value;
} Rte_DE_TrajectoryPose70PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70Speed value;
} Rte_DE_TrajectoryPose70Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose70Steering value;
} Rte_DE_TrajectoryPose70Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71Acceleration value;
} Rte_DE_TrajectoryPose71Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71Curvature value;
} Rte_DE_TrajectoryPose71Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71Heading value;
} Rte_DE_TrajectoryPose71Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71PositionX value;
} Rte_DE_TrajectoryPose71PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71PositionY value;
} Rte_DE_TrajectoryPose71PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71Speed value;
} Rte_DE_TrajectoryPose71Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose71Steering value;
} Rte_DE_TrajectoryPose71Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72Acceleration value;
} Rte_DE_TrajectoryPose72Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72Curvature value;
} Rte_DE_TrajectoryPose72Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72Heading value;
} Rte_DE_TrajectoryPose72Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72PositionX value;
} Rte_DE_TrajectoryPose72PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72PositionY value;
} Rte_DE_TrajectoryPose72PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72Speed value;
} Rte_DE_TrajectoryPose72Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose72Steering value;
} Rte_DE_TrajectoryPose72Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73Acceleration value;
} Rte_DE_TrajectoryPose73Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73Curvature value;
} Rte_DE_TrajectoryPose73Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73Heading value;
} Rte_DE_TrajectoryPose73Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73PositionX value;
} Rte_DE_TrajectoryPose73PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73PositionY value;
} Rte_DE_TrajectoryPose73PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73Speed value;
} Rte_DE_TrajectoryPose73Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose73Steering value;
} Rte_DE_TrajectoryPose73Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74Acceleration value;
} Rte_DE_TrajectoryPose74Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74Curvature value;
} Rte_DE_TrajectoryPose74Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74Heading value;
} Rte_DE_TrajectoryPose74Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74PositionX value;
} Rte_DE_TrajectoryPose74PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74PositionY value;
} Rte_DE_TrajectoryPose74PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74Speed value;
} Rte_DE_TrajectoryPose74Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose74Steering value;
} Rte_DE_TrajectoryPose74Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75Acceleration value;
} Rte_DE_TrajectoryPose75Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75Curvature value;
} Rte_DE_TrajectoryPose75Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75Heading value;
} Rte_DE_TrajectoryPose75Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75PositionX value;
} Rte_DE_TrajectoryPose75PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75PositionY value;
} Rte_DE_TrajectoryPose75PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75Speed value;
} Rte_DE_TrajectoryPose75Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose75Steering value;
} Rte_DE_TrajectoryPose75Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76Acceleration value;
} Rte_DE_TrajectoryPose76Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76Curvature value;
} Rte_DE_TrajectoryPose76Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76Heading value;
} Rte_DE_TrajectoryPose76Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76PositionX value;
} Rte_DE_TrajectoryPose76PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76PositionY value;
} Rte_DE_TrajectoryPose76PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76Speed value;
} Rte_DE_TrajectoryPose76Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose76Steering value;
} Rte_DE_TrajectoryPose76Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77Acceleration value;
} Rte_DE_TrajectoryPose77Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77Curvature value;
} Rte_DE_TrajectoryPose77Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77Heading value;
} Rte_DE_TrajectoryPose77Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77PositionX value;
} Rte_DE_TrajectoryPose77PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77PositionY value;
} Rte_DE_TrajectoryPose77PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77Speed value;
} Rte_DE_TrajectoryPose77Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose77Steering value;
} Rte_DE_TrajectoryPose77Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78Acceleration value;
} Rte_DE_TrajectoryPose78Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78Curvature value;
} Rte_DE_TrajectoryPose78Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78Heading value;
} Rte_DE_TrajectoryPose78Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78PositionX value;
} Rte_DE_TrajectoryPose78PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78PositionY value;
} Rte_DE_TrajectoryPose78PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78Speed value;
} Rte_DE_TrajectoryPose78Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose78Steering value;
} Rte_DE_TrajectoryPose78Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79Acceleration value;
} Rte_DE_TrajectoryPose79Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79Curvature value;
} Rte_DE_TrajectoryPose79Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79Heading value;
} Rte_DE_TrajectoryPose79Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79PositionX value;
} Rte_DE_TrajectoryPose79PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79PositionY value;
} Rte_DE_TrajectoryPose79PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79Speed value;
} Rte_DE_TrajectoryPose79Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose79Steering value;
} Rte_DE_TrajectoryPose79Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80Acceleration value;
} Rte_DE_TrajectoryPose80Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80Curvature value;
} Rte_DE_TrajectoryPose80Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80Heading value;
} Rte_DE_TrajectoryPose80Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80PositionX value;
} Rte_DE_TrajectoryPose80PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80PositionY value;
} Rte_DE_TrajectoryPose80PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80Speed value;
} Rte_DE_TrajectoryPose80Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose80Steering value;
} Rte_DE_TrajectoryPose80Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81Acceleration value;
} Rte_DE_TrajectoryPose81Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81Curvature value;
} Rte_DE_TrajectoryPose81Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81Heading value;
} Rte_DE_TrajectoryPose81Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81PositionX value;
} Rte_DE_TrajectoryPose81PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81PositionY value;
} Rte_DE_TrajectoryPose81PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81Speed value;
} Rte_DE_TrajectoryPose81Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose81Steering value;
} Rte_DE_TrajectoryPose81Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82Acceleration value;
} Rte_DE_TrajectoryPose82Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82Curvature value;
} Rte_DE_TrajectoryPose82Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82Heading value;
} Rte_DE_TrajectoryPose82Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82PositionX value;
} Rte_DE_TrajectoryPose82PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82PositionY value;
} Rte_DE_TrajectoryPose82PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82Speed value;
} Rte_DE_TrajectoryPose82Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose82Steering value;
} Rte_DE_TrajectoryPose82Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83Acceleration value;
} Rte_DE_TrajectoryPose83Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83Curvature value;
} Rte_DE_TrajectoryPose83Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83Heading value;
} Rte_DE_TrajectoryPose83Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83PositionX value;
} Rte_DE_TrajectoryPose83PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83PositionY value;
} Rte_DE_TrajectoryPose83PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83Speed value;
} Rte_DE_TrajectoryPose83Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose83Steering value;
} Rte_DE_TrajectoryPose83Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84Acceleration value;
} Rte_DE_TrajectoryPose84Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84Curvature value;
} Rte_DE_TrajectoryPose84Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84Heading value;
} Rte_DE_TrajectoryPose84Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84PositionX value;
} Rte_DE_TrajectoryPose84PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84PositionY value;
} Rte_DE_TrajectoryPose84PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84Speed value;
} Rte_DE_TrajectoryPose84Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose84Steering value;
} Rte_DE_TrajectoryPose84Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85Acceleration value;
} Rte_DE_TrajectoryPose85Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85Curvature value;
} Rte_DE_TrajectoryPose85Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85Heading value;
} Rte_DE_TrajectoryPose85Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85PositionX value;
} Rte_DE_TrajectoryPose85PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85PositionY value;
} Rte_DE_TrajectoryPose85PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85Speed value;
} Rte_DE_TrajectoryPose85Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose85Steering value;
} Rte_DE_TrajectoryPose85Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86Acceleration value;
} Rte_DE_TrajectoryPose86Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86Curvature value;
} Rte_DE_TrajectoryPose86Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86Heading value;
} Rte_DE_TrajectoryPose86Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86PositionX value;
} Rte_DE_TrajectoryPose86PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86PositionY value;
} Rte_DE_TrajectoryPose86PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86Speed value;
} Rte_DE_TrajectoryPose86Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose86Steering value;
} Rte_DE_TrajectoryPose86Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87Acceleration value;
} Rte_DE_TrajectoryPose87Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87Curvature value;
} Rte_DE_TrajectoryPose87Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87Heading value;
} Rte_DE_TrajectoryPose87Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87PositionX value;
} Rte_DE_TrajectoryPose87PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87PositionY value;
} Rte_DE_TrajectoryPose87PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87Speed value;
} Rte_DE_TrajectoryPose87Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose87Steering value;
} Rte_DE_TrajectoryPose87Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88Acceleration value;
} Rte_DE_TrajectoryPose88Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88Curvature value;
} Rte_DE_TrajectoryPose88Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88Heading value;
} Rte_DE_TrajectoryPose88Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88PositionX value;
} Rte_DE_TrajectoryPose88PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88PositionY value;
} Rte_DE_TrajectoryPose88PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88Speed value;
} Rte_DE_TrajectoryPose88Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose88Steering value;
} Rte_DE_TrajectoryPose88Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89Acceleration value;
} Rte_DE_TrajectoryPose89Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89Curvature value;
} Rte_DE_TrajectoryPose89Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89Heading value;
} Rte_DE_TrajectoryPose89Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89PositionX value;
} Rte_DE_TrajectoryPose89PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89PositionY value;
} Rte_DE_TrajectoryPose89PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89Speed value;
} Rte_DE_TrajectoryPose89Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose89Steering value;
} Rte_DE_TrajectoryPose89Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90Acceleration value;
} Rte_DE_TrajectoryPose90Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90Curvature value;
} Rte_DE_TrajectoryPose90Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90Heading value;
} Rte_DE_TrajectoryPose90Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90PositionX value;
} Rte_DE_TrajectoryPose90PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90PositionY value;
} Rte_DE_TrajectoryPose90PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90Speed value;
} Rte_DE_TrajectoryPose90Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose90Steering value;
} Rte_DE_TrajectoryPose90Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91Acceleration value;
} Rte_DE_TrajectoryPose91Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91Curvature value;
} Rte_DE_TrajectoryPose91Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91Heading value;
} Rte_DE_TrajectoryPose91Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91PositionX value;
} Rte_DE_TrajectoryPose91PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91PositionY value;
} Rte_DE_TrajectoryPose91PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91Speed value;
} Rte_DE_TrajectoryPose91Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose91Steering value;
} Rte_DE_TrajectoryPose91Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92Acceleration value;
} Rte_DE_TrajectoryPose92Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92Curvature value;
} Rte_DE_TrajectoryPose92Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92Heading value;
} Rte_DE_TrajectoryPose92Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92PositionX value;
} Rte_DE_TrajectoryPose92PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92PositionY value;
} Rte_DE_TrajectoryPose92PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92Speed value;
} Rte_DE_TrajectoryPose92Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose92Steering value;
} Rte_DE_TrajectoryPose92Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93Acceleration value;
} Rte_DE_TrajectoryPose93Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93Curvature value;
} Rte_DE_TrajectoryPose93Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93Heading value;
} Rte_DE_TrajectoryPose93Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93PositionX value;
} Rte_DE_TrajectoryPose93PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93PositionY value;
} Rte_DE_TrajectoryPose93PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93Speed value;
} Rte_DE_TrajectoryPose93Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose93Steering value;
} Rte_DE_TrajectoryPose93Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94Acceleration value;
} Rte_DE_TrajectoryPose94Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94Curvature value;
} Rte_DE_TrajectoryPose94Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94Heading value;
} Rte_DE_TrajectoryPose94Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94PositionX value;
} Rte_DE_TrajectoryPose94PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94PositionY value;
} Rte_DE_TrajectoryPose94PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94Speed value;
} Rte_DE_TrajectoryPose94Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose94Steering value;
} Rte_DE_TrajectoryPose94Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95Acceleration value;
} Rte_DE_TrajectoryPose95Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95Curvature value;
} Rte_DE_TrajectoryPose95Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95Heading value;
} Rte_DE_TrajectoryPose95Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95PositionX value;
} Rte_DE_TrajectoryPose95PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95PositionY value;
} Rte_DE_TrajectoryPose95PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95Speed value;
} Rte_DE_TrajectoryPose95Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose95Steering value;
} Rte_DE_TrajectoryPose95Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96Acceleration value;
} Rte_DE_TrajectoryPose96Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96Curvature value;
} Rte_DE_TrajectoryPose96Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96Heading value;
} Rte_DE_TrajectoryPose96Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96PositionX value;
} Rte_DE_TrajectoryPose96PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96PositionY value;
} Rte_DE_TrajectoryPose96PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96Speed value;
} Rte_DE_TrajectoryPose96Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose96Steering value;
} Rte_DE_TrajectoryPose96Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97Acceleration value;
} Rte_DE_TrajectoryPose97Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97Curvature value;
} Rte_DE_TrajectoryPose97Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97Heading value;
} Rte_DE_TrajectoryPose97Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97PositionX value;
} Rte_DE_TrajectoryPose97PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97PositionY value;
} Rte_DE_TrajectoryPose97PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97Speed value;
} Rte_DE_TrajectoryPose97Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose97Steering value;
} Rte_DE_TrajectoryPose97Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98Acceleration value;
} Rte_DE_TrajectoryPose98Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98Curvature value;
} Rte_DE_TrajectoryPose98Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98Heading value;
} Rte_DE_TrajectoryPose98Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98PositionX value;
} Rte_DE_TrajectoryPose98PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98PositionY value;
} Rte_DE_TrajectoryPose98PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98Speed value;
} Rte_DE_TrajectoryPose98Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose98Steering value;
} Rte_DE_TrajectoryPose98Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99Acceleration value;
} Rte_DE_TrajectoryPose99Acceleration;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99Curvature value;
} Rte_DE_TrajectoryPose99Curvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99Heading value;
} Rte_DE_TrajectoryPose99Heading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99PositionX value;
} Rte_DE_TrajectoryPose99PositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99PositionY value;
} Rte_DE_TrajectoryPose99PositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99Speed value;
} Rte_DE_TrajectoryPose99Speed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPose99Steering value;
} Rte_DE_TrajectoryPose99Steering;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_LineTiStamp value;
} Rte_DE_FC_LineTiStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_HeadingAngle value;
} Rte_DE_FC_Line_01_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_Id value;
} Rte_DE_FC_Line_01_Id;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_Type value;
} Rte_DE_FC_Line_01_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_Width value;
} Rte_DE_FC_Line_01_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_color value;
} Rte_DE_FC_Line_01_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_dx_End value;
} Rte_DE_FC_Line_01_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_dx_End_std value;
} Rte_DE_FC_Line_01_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_dx_Start value;
} Rte_DE_FC_Line_01_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_dx_Start_std value;
} Rte_DE_FC_Line_01_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_dy value;
} Rte_DE_FC_Line_01_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_01_exist_prob value;
} Rte_DE_FC_Line_01_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_HeadingAngle value;
} Rte_DE_FC_Line_02_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_Id value;
} Rte_DE_FC_Line_02_Id;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_Type value;
} Rte_DE_FC_Line_02_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_Width value;
} Rte_DE_FC_Line_02_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_color value;
} Rte_DE_FC_Line_02_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_dx_End value;
} Rte_DE_FC_Line_02_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_dx_End_std value;
} Rte_DE_FC_Line_02_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_dx_Start value;
} Rte_DE_FC_Line_02_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_dx_Start_std value;
} Rte_DE_FC_Line_02_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_dy value;
} Rte_DE_FC_Line_02_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_02_exist_prob value;
} Rte_DE_FC_Line_02_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_HeadingAngle value;
} Rte_DE_FC_Line_03_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_Id value;
} Rte_DE_FC_Line_03_Id;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_Type value;
} Rte_DE_FC_Line_03_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_Width value;
} Rte_DE_FC_Line_03_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_color value;
} Rte_DE_FC_Line_03_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_dx_End value;
} Rte_DE_FC_Line_03_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_dx_End_std value;
} Rte_DE_FC_Line_03_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_dx_Start value;
} Rte_DE_FC_Line_03_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_dx_Start_std value;
} Rte_DE_FC_Line_03_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_dy value;
} Rte_DE_FC_Line_03_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_03_exist_prob value;
} Rte_DE_FC_Line_03_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_MeasureType value;
} Rte_DE_FTFC_Line_01_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_ObstacleFlg value;
} Rte_DE_FTFC_Line_01_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_ParseConf value;
} Rte_DE_FTFC_Line_01_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_RMSE value;
} Rte_DE_FTFC_Line_01_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_curvature_alte value;
} Rte_DE_FTFC_Line_01_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_01_curve value;
} Rte_DE_FTFC_Line_01_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_MeasureType value;
} Rte_DE_FTFC_Line_02_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_ObstacleFlg value;
} Rte_DE_FTFC_Line_02_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_ParseConf value;
} Rte_DE_FTFC_Line_02_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_RMSE value;
} Rte_DE_FTFC_Line_02_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_curvature_alte value;
} Rte_DE_FTFC_Line_02_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_02_curve value;
} Rte_DE_FTFC_Line_02_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_MeasureType value;
} Rte_DE_FTFC_Line_03_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_curvature_alte value;
} Rte_DE_FTFC_Line_03_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_curve value;
} Rte_DE_FTFC_Line_03_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_HeadingAngle value;
} Rte_DE_FC_Line_04_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_Id value;
} Rte_DE_FC_Line_04_Id;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_Type value;
} Rte_DE_FC_Line_04_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_Width value;
} Rte_DE_FC_Line_04_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_color value;
} Rte_DE_FC_Line_04_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_dx_End value;
} Rte_DE_FC_Line_04_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_dx_End_std value;
} Rte_DE_FC_Line_04_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_dx_Start value;
} Rte_DE_FC_Line_04_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_dx_Start_std value;
} Rte_DE_FC_Line_04_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_dy value;
} Rte_DE_FC_Line_04_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_04_exist_prob value;
} Rte_DE_FC_Line_04_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_HeadingAngle value;
} Rte_DE_FC_Line_05_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_Type value;
} Rte_DE_FC_Line_05_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_Width value;
} Rte_DE_FC_Line_05_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_color value;
} Rte_DE_FC_Line_05_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_dx_End value;
} Rte_DE_FC_Line_05_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_dx_End_std value;
} Rte_DE_FC_Line_05_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_dx_Start value;
} Rte_DE_FC_Line_05_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_dx_Start_std value;
} Rte_DE_FC_Line_05_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_dy value;
} Rte_DE_FC_Line_05_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_05_exist_prob value;
} Rte_DE_FC_Line_05_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_HeadingAngle value;
} Rte_DE_FC_Line_06_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_Type value;
} Rte_DE_FC_Line_06_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_Width value;
} Rte_DE_FC_Line_06_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_color value;
} Rte_DE_FC_Line_06_color;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_dx_End value;
} Rte_DE_FC_Line_06_dx_End;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_dx_End_std value;
} Rte_DE_FC_Line_06_dx_End_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_dx_Start value;
} Rte_DE_FC_Line_06_dx_Start;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_dx_Start_std value;
} Rte_DE_FC_Line_06_dx_Start_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_dy value;
} Rte_DE_FC_Line_06_dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Line_06_exist_prob value;
} Rte_DE_FC_Line_06_exist_prob;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_ObstacleFlg value;
} Rte_DE_FTFC_Line_03_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_ParseConf value;
} Rte_DE_FTFC_Line_03_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_03_RMSE value;
} Rte_DE_FTFC_Line_03_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_MeasureType value;
} Rte_DE_FTFC_Line_04_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_ObstacleFlg value;
} Rte_DE_FTFC_Line_04_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_ParseConf value;
} Rte_DE_FTFC_Line_04_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_RMSE value;
} Rte_DE_FTFC_Line_04_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_curvature_alte value;
} Rte_DE_FTFC_Line_04_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_04_curve value;
} Rte_DE_FTFC_Line_04_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_MeasureType value;
} Rte_DE_FTFC_Line_05_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_ObstacleFlg value;
} Rte_DE_FTFC_Line_05_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_ParseConf value;
} Rte_DE_FTFC_Line_05_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_RMSE value;
} Rte_DE_FTFC_Line_05_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_curvature_alte value;
} Rte_DE_FTFC_Line_05_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_05_curve value;
} Rte_DE_FTFC_Line_05_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_MeasureType value;
} Rte_DE_FTFC_Line_06_MeasureType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_ObstacleFlg value;
} Rte_DE_FTFC_Line_06_ObstacleFlg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_ParseConf value;
} Rte_DE_FTFC_Line_06_ParseConf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_RMSE value;
} Rte_DE_FTFC_Line_06_RMSE;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_curvature_alte value;
} Rte_DE_FTFC_Line_06_curvature_alte;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Line_06_curve value;
} Rte_DE_FTFC_Line_06_curve;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_CIPV_ID value;
} Rte_DE_FC_CIPV_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Ax value;
} Rte_DE_FC_Obj20_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Ay value;
} Rte_DE_FC_Obj20_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_ClassifiedView value;
} Rte_DE_FC_Obj20_ClassifiedView;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Dx value;
} Rte_DE_FC_Obj20_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Dx_Vnce value;
} Rte_DE_FC_Obj20_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Dy value;
} Rte_DE_FC_Obj20_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Dy_Vnce value;
} Rte_DE_FC_Obj20_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_ExistProb value;
} Rte_DE_FC_Obj20_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_HeadingAngle value;
} Rte_DE_FC_Obj20_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Height value;
} Rte_DE_FC_Obj20_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_LaneAssignment value;
} Rte_DE_FC_Obj20_LaneAssignment;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Length value;
} Rte_DE_FC_Obj20_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_MotionType value;
} Rte_DE_FC_Obj20_MotionType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Track_Age value;
} Rte_DE_FC_Obj20_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Track_ID value;
} Rte_DE_FC_Obj20_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Type value;
} Rte_DE_FC_Obj20_Type;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Vx value;
} Rte_DE_FC_Obj20_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Vy value;
} Rte_DE_FC_Obj20_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_Obj20_Width value;
} Rte_DE_FC_Obj20_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_obj20_Brakelight_Info value;
} Rte_DE_FC_obj20_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_obj20_Taillight_Info value;
} Rte_DE_FC_obj20_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_CenterAngle value;
} Rte_DE_FTFC_Obj20_CenterAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_CornerPoint_x value;
} Rte_DE_FTFC_Obj20_CornerPoint_x;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_CornerPoint_y value;
} Rte_DE_FTFC_Obj20_CornerPoint_y;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_DistInLane value;
} Rte_DE_FTFC_Obj20_DistInLane;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_objCutInFlag value;
} Rte_DE_FTFC_Obj20_objCutInFlag;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FTFC_Obj20_objCutInLane value;
} Rte_DE_FTFC_Obj20_objCutInLane;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Class value;
} Rte_DE_FrFr_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Width value;
} Rte_DE_FrFr_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Ax value;
} Rte_DE_Fr_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Ay value;
} Rte_DE_Fr_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Brakelight_Info value;
} Rte_DE_Fr_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Class value;
} Rte_DE_Fr_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Dx value;
} Rte_DE_Fr_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Dx_Vnce value;
} Rte_DE_Fr_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Dy value;
} Rte_DE_Fr_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Dy_Vnce value;
} Rte_DE_Fr_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_ExistProb value;
} Rte_DE_Fr_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_Fr_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_HeadingAngle value;
} Rte_DE_Fr_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Height value;
} Rte_DE_Fr_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Length value;
} Rte_DE_Fr_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_ObstacleProb value;
} Rte_DE_Fr_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Taillight_Info value;
} Rte_DE_Fr_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Track_Age value;
} Rte_DE_Fr_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Track_ID value;
} Rte_DE_Fr_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Vx value;
} Rte_DE_Fr_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Vx_std value;
} Rte_DE_Fr_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Vy value;
} Rte_DE_Fr_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Vy_std value;
} Rte_DE_Fr_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_Width value;
} Rte_DE_Fr_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_fusion_Sts value;
} Rte_DE_Fr_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Ax value;
} Rte_DE_FrFr_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Ay value;
} Rte_DE_FrFr_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Brakelight_Info value;
} Rte_DE_FrFr_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Dx value;
} Rte_DE_FrFr_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Dx_Vnce value;
} Rte_DE_FrFr_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Dy value;
} Rte_DE_FrFr_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Dy_Vnce value;
} Rte_DE_FrFr_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_ExistProb value;
} Rte_DE_FrFr_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_FrFr_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_HeadingAngle value;
} Rte_DE_FrFr_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Height value;
} Rte_DE_FrFr_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Length value;
} Rte_DE_FrFr_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_ObstacleProb value;
} Rte_DE_FrFr_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Taillight_Info value;
} Rte_DE_FrFr_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Track_Age value;
} Rte_DE_FrFr_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Track_ID value;
} Rte_DE_FrFr_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Vx value;
} Rte_DE_FrFr_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Vx_std value;
} Rte_DE_FrFr_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Vy value;
} Rte_DE_FrFr_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_Vy_std value;
} Rte_DE_FrFr_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_fusion_Sts value;
} Rte_DE_FrFr_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Class value;
} Rte_DE_LeFr_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Dy value;
} Rte_DE_LeFr_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Dy_Vnce value;
} Rte_DE_LeFr_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_HeadingAngle value;
} Rte_DE_LeFr_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Height value;
} Rte_DE_LeFr_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Length value;
} Rte_DE_LeFr_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Width value;
} Rte_DE_LeFr_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Ax value;
} Rte_DE_Le_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Ay value;
} Rte_DE_Le_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Brakelight_Info value;
} Rte_DE_Le_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Class value;
} Rte_DE_Le_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Dx value;
} Rte_DE_Le_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Dx_Vnce value;
} Rte_DE_Le_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Dy value;
} Rte_DE_Le_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Dy_Vnce value;
} Rte_DE_Le_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_ExistProb value;
} Rte_DE_Le_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_Le_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_HeadingAngle value;
} Rte_DE_Le_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Height value;
} Rte_DE_Le_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Length value;
} Rte_DE_Le_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_ObstacleProb value;
} Rte_DE_Le_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Taillight_Info value;
} Rte_DE_Le_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Track_Age value;
} Rte_DE_Le_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Track_ID value;
} Rte_DE_Le_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Vx value;
} Rte_DE_Le_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Vx_std value;
} Rte_DE_Le_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Vy value;
} Rte_DE_Le_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Vy_std value;
} Rte_DE_Le_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_Width value;
} Rte_DE_Le_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_fusion_Sts value;
} Rte_DE_Le_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Ax value;
} Rte_DE_LeFr_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Ay value;
} Rte_DE_LeFr_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Brakelight_Info value;
} Rte_DE_LeFr_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Dx value;
} Rte_DE_LeFr_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Dx_Vnce value;
} Rte_DE_LeFr_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_ExistProb value;
} Rte_DE_LeFr_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_LeFr_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_ObstacleProb value;
} Rte_DE_LeFr_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Taillight_Info value;
} Rte_DE_LeFr_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Track_Age value;
} Rte_DE_LeFr_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Track_ID value;
} Rte_DE_LeFr_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Vx value;
} Rte_DE_LeFr_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Vx_std value;
} Rte_DE_LeFr_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Vy value;
} Rte_DE_LeFr_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_Vy_std value;
} Rte_DE_LeFr_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_fusion_Sts value;
} Rte_DE_LeFr_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Class value;
} Rte_DE_RiFr_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Dx value;
} Rte_DE_RiFr_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Dx_Vnce value;
} Rte_DE_RiFr_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Dy value;
} Rte_DE_RiFr_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Dy_Vnce value;
} Rte_DE_RiFr_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_HeadingAngle value;
} Rte_DE_RiFr_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Height value;
} Rte_DE_RiFr_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Length value;
} Rte_DE_RiFr_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Vy value;
} Rte_DE_RiFr_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Vy_std value;
} Rte_DE_RiFr_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Width value;
} Rte_DE_RiFr_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Ax value;
} Rte_DE_Ri_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Ay value;
} Rte_DE_Ri_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Brakelight_Info value;
} Rte_DE_Ri_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Class value;
} Rte_DE_Ri_AccOBJ_Class;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Dx value;
} Rte_DE_Ri_AccOBJ_Dx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Dx_Vnce value;
} Rte_DE_Ri_AccOBJ_Dx_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Dy value;
} Rte_DE_Ri_AccOBJ_Dy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Dy_Vnce value;
} Rte_DE_Ri_AccOBJ_Dy_Vnce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_ExistProb value;
} Rte_DE_Ri_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_Ri_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_HeadingAngle value;
} Rte_DE_Ri_AccOBJ_HeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Height value;
} Rte_DE_Ri_AccOBJ_Height;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Length value;
} Rte_DE_Ri_AccOBJ_Length;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_ObstacleProb value;
} Rte_DE_Ri_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Taillight_Info value;
} Rte_DE_Ri_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Track_Age value;
} Rte_DE_Ri_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Track_ID value;
} Rte_DE_Ri_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Vx value;
} Rte_DE_Ri_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Vx_std value;
} Rte_DE_Ri_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Vy value;
} Rte_DE_Ri_AccOBJ_Vy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Vy_std value;
} Rte_DE_Ri_AccOBJ_Vy_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_Width value;
} Rte_DE_Ri_AccOBJ_Width;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_fusion_Sts value;
} Rte_DE_Ri_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrFr_AccOBJ_confi value;
} Rte_DE_FrFr_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Fr_AccOBJ_confi value;
} Rte_DE_Fr_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LeFr_AccOBJ_confi value;
} Rte_DE_LeFr_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Le_AccOBJ_confi value;
} Rte_DE_Le_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Ax value;
} Rte_DE_RiFr_AccOBJ_Ax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Ay value;
} Rte_DE_RiFr_AccOBJ_Ay;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Brakelight_Info value;
} Rte_DE_RiFr_AccOBJ_Brakelight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_ExistProb value;
} Rte_DE_RiFr_AccOBJ_ExistProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_FusionedFC_Track_ID value;
} Rte_DE_RiFr_AccOBJ_FusionedFC_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_ObstacleProb value;
} Rte_DE_RiFr_AccOBJ_ObstacleProb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Taillight_Info value;
} Rte_DE_RiFr_AccOBJ_Taillight_Info;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Track_Age value;
} Rte_DE_RiFr_AccOBJ_Track_Age;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Track_ID value;
} Rte_DE_RiFr_AccOBJ_Track_ID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Vx value;
} Rte_DE_RiFr_AccOBJ_Vx;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_Vx_std value;
} Rte_DE_RiFr_AccOBJ_Vx_std;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_confi value;
} Rte_DE_RiFr_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RiFr_AccOBJ_fusion_Sts value;
} Rte_DE_RiFr_AccOBJ_fusion_Sts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Ri_AccOBJ_confi value;
} Rte_DE_Ri_AccOBJ_confi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_FrontCameraCalibrationStatus value;
} Rte_DE_FC_FrontCameraCalibrationStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_LaneChangeStatus value;
} Rte_DE_FC_LaneChangeStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FC_RollingCounter value;
} Rte_DE_FC_RollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CSI_LaneInfo_Struct value;
} Rte_DE_CSI_LaneInfo_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CSI_ObjectInfo_Struct value;
} Rte_DE_CSI_ObjectInfo_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VSI_VehicleInfo_Struct value;
} Rte_DE_VSI_VehicleInfo_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlOutput value;
} Rte_DE_ControlOutput;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlDebug value;
} Rte_DE_ControlDebug;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EstimationDebug value;
} Rte_DE_EstimationDebug;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlErrorHeading value;
} Rte_DE_ControlErrorHeading;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlErrorLateral value;
} Rte_DE_ControlErrorLateral;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlErrorLongitudinal value;
} Rte_DE_ControlErrorLongitudinal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlErrorVelocity value;
} Rte_DE_ControlErrorVelocity;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve0 value;
} Rte_DE_ControlReserve0;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve1 value;
} Rte_DE_ControlReserve1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve2 value;
} Rte_DE_ControlReserve2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve3 value;
} Rte_DE_ControlReserve3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve4 value;
} Rte_DE_ControlReserve4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve5 value;
} Rte_DE_ControlReserve5;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve6 value;
} Rte_DE_ControlReserve6;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve7 value;
} Rte_DE_ControlReserve7;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve8 value;
} Rte_DE_ControlReserve8;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlReserve9 value;
} Rte_DE_ControlReserve9;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ControlTimeStamp value;
} Rte_DE_ControlTimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidDistanceDComponent value;
} Rte_DE_LatPidDistanceDComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidDistanceIComponent value;
} Rte_DE_LatPidDistanceIComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidDistancePComponent value;
} Rte_DE_LatPidDistancePComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidDistanceTotal value;
} Rte_DE_LatPidDistanceTotal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidHeadingDComponent value;
} Rte_DE_LatPidHeadingDComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidHeadingIComponent value;
} Rte_DE_LatPidHeadingIComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidHeadingPComponent value;
} Rte_DE_LatPidHeadingPComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatPidHeadingTotal value;
} Rte_DE_LatPidHeadingTotal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatSteeringFeeback value;
} Rte_DE_LatSteeringFeeback;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LatSteeringFeedforward value;
} Rte_DE_LatSteeringFeedforward;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongAccelerationFeeback value;
} Rte_DE_LongAccelerationFeeback;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongAccelerationFeedforward value;
} Rte_DE_LongAccelerationFeedforward;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidDistanceDComponent value;
} Rte_DE_LongPidDistanceDComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidDistanceIComponent value;
} Rte_DE_LongPidDistanceIComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidDistancePComponent value;
} Rte_DE_LongPidDistancePComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidDistanceTotal value;
} Rte_DE_LongPidDistanceTotal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidSpeedDComponent value;
} Rte_DE_LongPidSpeedDComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidSpeedIComponent value;
} Rte_DE_LongPidSpeedIComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidSpeedPComponent value;
} Rte_DE_LongPidSpeedPComponent;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongPidSpeedTotal value;
} Rte_DE_LongPidSpeedTotal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugCanbusState value;
} Rte_DE_PoseDebugCanbusState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugIsStationary value;
} Rte_DE_PoseDebugIsStationary;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugPositionX value;
} Rte_DE_PoseDebugPositionX;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugPositionXStd value;
} Rte_DE_PoseDebugPositionXStd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugPositionY value;
} Rte_DE_PoseDebugPositionY;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugPositionYStd value;
} Rte_DE_PoseDebugPositionYStd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved0 value;
} Rte_DE_PoseDebugReserved0;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved1 value;
} Rte_DE_PoseDebugReserved1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved2 value;
} Rte_DE_PoseDebugReserved2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved3 value;
} Rte_DE_PoseDebugReserved3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved4 value;
} Rte_DE_PoseDebugReserved4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved5 value;
} Rte_DE_PoseDebugReserved5;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved6 value;
} Rte_DE_PoseDebugReserved6;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved7 value;
} Rte_DE_PoseDebugReserved7;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugReserved8 value;
} Rte_DE_PoseDebugReserved8;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugSideSlipAngle value;
} Rte_DE_PoseDebugSideSlipAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugSpeed value;
} Rte_DE_PoseDebugSpeed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugSpeedStd value;
} Rte_DE_PoseDebugSpeedStd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugStateType value;
} Rte_DE_PoseDebugStateType;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugTimeStamp value;
} Rte_DE_PoseDebugTimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugVehicleState value;
} Rte_DE_PoseDebugVehicleState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugYaw value;
} Rte_DE_PoseDebugYaw;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PoseDebugYawStd value;
} Rte_DE_PoseDebugYawStd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Fail value;
} Rte_DE_FRS_Fail;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_HostSpeed value;
} Rte_DE_FRS_HostSpeed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Host_Yaw value;
} Rte_DE_FRS_Host_Yaw;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Latency value;
} Rte_DE_FRS_Latency;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_MeasEnabled value;
} Rte_DE_FRS_MeasEnabled;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Msg_AliveCounter value;
} Rte_DE_FRS_Msg_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Msg_CheckSum value;
} Rte_DE_FRS_Msg_CheckSum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Status_BlkProg value;
} Rte_DE_FRS_Status_BlkProg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Status_HWErr value;
} Rte_DE_FRS_Status_HWErr;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_Status_MisAlign value;
} Rte_DE_FRS_Status_MisAlign;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FRS_TimeStamp value;
} Rte_DE_FRS_TimeStamp;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LAT_CtrlCmd_Struct value;
} Rte_DE_LAT_CtrlCmd_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LGT_CtrlCmd_Struct value;
} Rte_DE_LGT_CtrlCmd_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VSI_McuCanTimeout_Struct value;
} Rte_DE_VSI_McuCanTimeout_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VSI_VehInfoFor1V1R_Struct value;
} Rte_DE_VSI_VehInfoFor1V1R_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ABA_active value;
} Rte_DE_ESC_ABA_active;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ABA_available value;
} Rte_DE_ESC_ABA_available;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ABP_active value;
} Rte_DE_ESC_ABP_active;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ABP_available value;
} Rte_DE_ESC_ABP_available;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_AEB_active value;
} Rte_DE_ESC_AEB_active;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_AEB_available value;
} Rte_DE_ESC_AEB_available;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_AWB_active value;
} Rte_DE_ESC_AWB_active;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_AWB_available value;
} Rte_DE_ESC_AWB_available;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_BrakeTempTooHigh value;
} Rte_DE_ESC_BrakeTempTooHigh;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_DA_MESSAGE_AliveCounter value;
} Rte_DE_ESC_DA_MESSAGE_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_DA_MESSAGE_Checksum value;
} Rte_DE_ESC_DA_MESSAGE_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_DTC_Active value;
} Rte_DE_ESC_DTC_Active;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_DiagExtModSts value;
} Rte_DE_ESC_DiagExtModSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_NoBrakeForce value;
} Rte_DE_ESC_NoBrakeForce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_QDCFRS value;
} Rte_DE_ESC_QDCFRS;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_Vehiclestandstill value;
} Rte_DE_ESC_Vehiclestandstill;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FLWheelDirection value;
} Rte_DE_ESC_FLWheelDirection;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FLWheelSpeedInvalid value;
} Rte_DE_ESC_FLWheelSpeedInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FLWheelSpeedKPH value;
} Rte_DE_ESC_FLWheelSpeedKPH;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FRWheelDirection value;
} Rte_DE_ESC_FRWheelDirection;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FRWheelSpeedInvalid value;
} Rte_DE_ESC_FRWheelSpeedInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FRWheelSpeedKPH value;
} Rte_DE_ESC_FRWheelSpeedKPH;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FrontWheelSpeedsKPH_AliveCounter value;
} Rte_DE_ESC_FrontWheelSpeedsKPH_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_FrontWheelSpeedsKPH_Checksum value;
} Rte_DE_ESC_FrontWheelSpeedsKPH_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_Mcylinder_Pressure value;
} Rte_DE_ESC_Mcylinder_Pressure;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_Mcylinder_PressureInvalid value;
} Rte_DE_ESC_Mcylinder_PressureInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RLWheelDirection value;
} Rte_DE_ESC_RLWheelDirection;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RLWheelSpeedInvalid value;
} Rte_DE_ESC_RLWheelSpeedInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RLWheelSpeedKPH value;
} Rte_DE_ESC_RLWheelSpeedKPH;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RRWheelDirection value;
} Rte_DE_ESC_RRWheelDirection;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RRWheelSpeedInvalid value;
} Rte_DE_ESC_RRWheelSpeedInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RRWheelSpeedKPH value;
} Rte_DE_ESC_RRWheelSpeedKPH;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RearWheelSpeedsKPH_AliveCounter value;
} Rte_DE_ESC_RearWheelSpeedsKPH_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_RearWheelSpeedsKPH_Checksum value;
} Rte_DE_ESC_RearWheelSpeedsKPH_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ABSActive value;
} Rte_DE_ESC_ABSActive;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_AVHStatus value;
} Rte_DE_ESC_AVHStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_BrakePedalSwitchInvalid value;
} Rte_DE_ESC_BrakePedalSwitchInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_BrakePedalSwitchStatus value;
} Rte_DE_ESC_BrakePedalSwitchStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_EPBStatus value;
} Rte_DE_ESC_EPBStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ESPActive value;
} Rte_DE_ESC_ESPActive;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_ESPFailed value;
} Rte_DE_ESC_ESPFailed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_HHCActive value;
} Rte_DE_ESC_HHCActive;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_PATAResponse value;
} Rte_DE_ESC_PATAResponse;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_Status_AliveCounter value;
} Rte_DE_ESC_Status_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_Status_Checksum value;
} Rte_DE_ESC_Status_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_TCSActive value;
} Rte_DE_ESC_TCSActive;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_VehicleSpeed value;
} Rte_DE_ESC_VehicleSpeed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ESC_VehicleSpeedInvalid value;
} Rte_DE_ESC_VehicleSpeedInvalid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FBS_DebugInfo_Struct value;
} Rte_DE_FBS_DebugInfo_Struct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AccRequestAfterRateLimit value;
} Rte_DE_AccRequestAfterRateLimit;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AccRequestByOutOfOdd value;
} Rte_DE_AccRequestByOutOfOdd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AccRequestBySpeed value;
} Rte_DE_AccRequestBySpeed;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AccRequestForSystemError value;
} Rte_DE_AccRequestForSystemError;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AvoidCollisionEnable value;
} Rte_DE_AvoidCollisionEnable;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EgoLaneWidth value;
} Rte_DE_EgoLaneWidth;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EgoStopTime value;
} Rte_DE_EgoStopTime;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EmergencyBrakeAcc value;
} Rte_DE_EmergencyBrakeAcc;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoReserve1 value;
} Rte_DE_FallbackDebugInfoReserve1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoReserve2 value;
} Rte_DE_FallbackDebugInfoReserve2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoReserve3 value;
} Rte_DE_FallbackDebugInfoReserve3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoReserve4 value;
} Rte_DE_FallbackDebugInfoReserve4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoReserve5 value;
} Rte_DE_FallbackDebugInfoReserve5;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackDebugInfoRollingCounter value;
} Rte_DE_FallbackDebugInfoRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackTriggerStatus value;
} Rte_DE_FallbackTriggerStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FeedforwardsSteerAngle value;
} Rte_DE_FeedforwardsSteerAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  GradientLimitAccRequest value;
} Rte_DE_GradientLimitAccRequest;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HeadingAngleContribution value;
} Rte_DE_HeadingAngleContribution;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HeadingAngleError value;
} Rte_DE_HeadingAngleError;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HeadingAngleErrorWeight value;
} Rte_DE_HeadingAngleErrorWeight;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LaneValidState value;
} Rte_DE_LaneValidState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LateralContribution value;
} Rte_DE_LateralContribution;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LateralDistanceError value;
} Rte_DE_LateralDistanceError;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LateralDistanceErrorWeight value;
} Rte_DE_LateralDistanceErrorWeight;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LateralSystemState value;
} Rte_DE_LateralSystemState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LimitAccRequest value;
} Rte_DE_LimitAccRequest;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LimitSteerAngle value;
} Rte_DE_LimitSteerAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LimitSteerAngleRequest value;
} Rte_DE_LimitSteerAngleRequest;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongAccRequest value;
} Rte_DE_LongAccRequest;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LongNecAcc value;
} Rte_DE_LongNecAcc;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LqrIterationError value;
} Rte_DE_LqrIterationError;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  LqrIterationNums value;
} Rte_DE_LqrIterationNums;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  MaxSteerAngleRateThreshold value;
} Rte_DE_MaxSteerAngleRateThreshold;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  MaxSteerAngleThreshold value;
} Rte_DE_MaxSteerAngleThreshold;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  MinAccRate value;
} Rte_DE_MinAccRate;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ObjectStopTime value;
} Rte_DE_ObjectStopTime;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RawAccRequest value;
} Rte_DE_RawAccRequest;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  RawSteerAngle value;
} Rte_DE_RawSteerAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SafeDistance value;
} Rte_DE_SafeDistance;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SteerAngle value;
} Rte_DE_SteerAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SteerAngleByLQR value;
} Rte_DE_SteerAngleByLQR;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SteerAngleForSystemError value;
} Rte_DE_SteerAngleForSystemError;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemState value;
} Rte_DE_SystemState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TimeToCollison value;
} Rte_DE_TimeToCollison;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryCurvature value;
} Rte_DE_TrajectoryCurvature;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryCurvatureChange value;
} Rte_DE_TrajectoryCurvatureChange;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryHeadingAngle value;
} Rte_DE_TrajectoryHeadingAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryLength value;
} Rte_DE_TrajectoryLength;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrajectoryPosY0 value;
} Rte_DE_TrajectoryPosY0;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbCanMessageID value;
} Rte_DE_AcuFbCanMessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbCanTimeout value;
} Rte_DE_AcuFbCanTimeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbCanTimer value;
} Rte_DE_AcuFbCanTimer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter0MessageID value;
} Rte_DE_AcuMid3SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter0Timeout value;
} Rte_DE_AcuMid3SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter0Timer value;
} Rte_DE_AcuMid3SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter1MessageID value;
} Rte_DE_AcuMid3SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter1Timeout value;
} Rte_DE_AcuMid3SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid3SsmCounter1Timer value;
} Rte_DE_AcuMid3SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter0MessageID value;
} Rte_DE_AcuMid5SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter0Timeout value;
} Rte_DE_AcuMid5SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter0Timer value;
} Rte_DE_AcuMid5SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter1MessageID value;
} Rte_DE_AcuMid5SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter1Timeout value;
} Rte_DE_AcuMid5SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid5SsmCounter1Timer value;
} Rte_DE_AcuMid5SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter0MessageID value;
} Rte_DE_AcuMid6SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter0Timeout value;
} Rte_DE_AcuMid6SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter0Timer value;
} Rte_DE_AcuMid6SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter1MessageID value;
} Rte_DE_AcuMid6SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter1Timeout value;
} Rte_DE_AcuMid6SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuMid6SsmCounter1Timer value;
} Rte_DE_AcuMid6SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AswSoftwarewareVersion value;
} Rte_DE_AswSoftwarewareVersion;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  BootLoaderVersion value;
} Rte_DE_BootLoaderVersion;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  BswSoftwarewareVersion value;
} Rte_DE_BswSoftwarewareVersion;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontCameraCalibrationStatus value;
} Rte_DE_FrontCameraCalibrationStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontCameraCanMessageID value;
} Rte_DE_FrontCameraCanMessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontCameraCanTimeout value;
} Rte_DE_FrontCameraCanTimeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontCameraCanTimer value;
} Rte_DE_FrontCameraCanTimer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontCameraFailureStatus value;
} Rte_DE_FrontCameraFailureStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontRadarCalibrationStatus value;
} Rte_DE_FrontRadarCalibrationStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontRadarCanMessageID value;
} Rte_DE_FrontRadarCanMessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontRadarCanTimeout value;
} Rte_DE_FrontRadarCanTimeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontRadarCanTimer value;
} Rte_DE_FrontRadarCanTimer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FrontRadarFailureStatus value;
} Rte_DE_FrontRadarFailureStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemStatusReserved1 value;
} Rte_DE_SystemStatusReserved1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemStatusReserved2 value;
} Rte_DE_SystemStatusReserved2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemStatusReserved3 value;
} Rte_DE_SystemStatusReserved3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemStatusReserved4 value;
} Rte_DE_SystemStatusReserved4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SystemStatusRollingCounter value;
} Rte_DE_SystemStatusRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter0MessageID value;
} Rte_DE_VehMid3SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter0Timeout value;
} Rte_DE_VehMid3SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter0Timer value;
} Rte_DE_VehMid3SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter1MessageID value;
} Rte_DE_VehMid3SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter1Timeout value;
} Rte_DE_VehMid3SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3SsmCounter1Timer value;
} Rte_DE_VehMid3SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter0MessageID value;
} Rte_DE_VehMid3VcuCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter0Timeout value;
} Rte_DE_VehMid3VcuCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter0Timer value;
} Rte_DE_VehMid3VcuCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter1MessageID value;
} Rte_DE_VehMid3VcuCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter1Timeout value;
} Rte_DE_VehMid3VcuCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid3VcuCounter1Timer value;
} Rte_DE_VehMid3VcuCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter0MessageID value;
} Rte_DE_VehMid5SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter0Timeout value;
} Rte_DE_VehMid5SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter0Timer value;
} Rte_DE_VehMid5SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter1MessageID value;
} Rte_DE_VehMid5SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter1Timeout value;
} Rte_DE_VehMid5SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid5SsmCounter1Timer value;
} Rte_DE_VehMid5SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter0MessageID value;
} Rte_DE_VehMid6SsmCounter0MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter0Timeout value;
} Rte_DE_VehMid6SsmCounter0Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter0Timer value;
} Rte_DE_VehMid6SsmCounter0Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter1MessageID value;
} Rte_DE_VehMid6SsmCounter1MessageID;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter1Timeout value;
} Rte_DE_VehMid6SsmCounter1Timeout;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehMid6SsmCounter1Timer value;
} Rte_DE_VehMid6SsmCounter1Timer;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FallbackSelfCheckStatus value;
} Rte_DE_FallbackSelfCheckStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FbAcuReserved value;
} Rte_DE_FbAcuReserved;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  FbAcuRollingCounter value;
} Rte_DE_FbAcuRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  McuStatus value;
} Rte_DE_McuStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  Sensor1v1rStatus value;
} Rte_DE_Sensor1v1rStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehControlStatus value;
} Rte_DE_VehControlStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_CalibrationSts value;
} Rte_DE_SAS_CalibrationSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_FailureSts value;
} Rte_DE_SAS_FailureSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_Status_AliveCounter value;
} Rte_DE_SAS_Status_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_Status_Checksum value;
} Rte_DE_SAS_Status_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_SteerWheelAngle value;
} Rte_DE_SAS_SteerWheelAngle;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SAS_SteerWheelRotSpd value;
} Rte_DE_SAS_SteerWheelRotSpd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrimVehSpdGroupSafeNomQf_C value;
} Rte_DE_PrimVehSpdGroupSafeNomQf_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrimVehSpdGroupSafeNom_C value;
} Rte_DE_PrimVehSpdGroupSafeNom_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrimALgtDataRawSafeNomQf_C value;
} Rte_DE_PrimALgtDataRawSafeNomQf_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrimALgtDataRawSafeNom_C value;
} Rte_DE_PrimALgtDataRawSafeNom_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrimALatDataRawSafeNom_C value;
} Rte_DE_PrimALatDataRawSafeNom_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrpsnTqDirAct_C value;
} Rte_DE_PrpsnTqDirAct_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CarTiGlb_A value;
} Rte_DE_CarTiGlb_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YawRate1Qf1_C value;
} Rte_DE_YawRate1Qf1_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YawRate1_C value;
} Rte_DE_YawRate1_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WhlLockStsLockSts_C value;
} Rte_DE_WhlLockStsLockSts_C;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecWhlAgReqGroupSafe value;
} Rte_DE_SG_AdSecWhlAgReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecALgtLimReqGroupSafe value;
} Rte_DE_SG_AdSecALgtLimReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecAdNomALgtReqGroupSafe value;
} Rte_DE_SG_SecAdNomALgtReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecAdWhlLockReq value;
} Rte_DE_SG_SecAdWhlLockReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  IDcDcAvlLoSideExt value;
} Rte_DE_IDcDcAvlLoSideExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  IDcDcAvlMaxLoSideExt value;
} Rte_DE_IDcDcAvlMaxLoSideExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_HmiAutnmsSts value;
} Rte_DE_SG_HmiAutnmsSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  UDcDcAvlLoSideExt value;
} Rte_DE_UDcDcAvlLoSideExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AutnmsDrvStReq value;
} Rte_DE_SG_AutnmsDrvStReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_VehOperStReq value;
} Rte_DE_SG_VehOperStReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdDirReq value;
} Rte_DE_SG_AdDirReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdStandStillReq value;
} Rte_DE_SG_AdStandStillReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehUsgStReq value;
} Rte_DE_VehUsgStReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdFusedFricEstimn value;
} Rte_DE_SG_AdFusedFricEstimn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdpLiReqFromAPI value;
} Rte_DE_SG_AdpLiReqFromAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SwtExtrLiFromAPI value;
} Rte_DE_SG_SwtExtrLiFromAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AdSetSpd value;
} Rte_DE_AdSetSpd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdFreeDst value;
} Rte_DE_SG_AdFreeDst;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdWhlLockReq value;
} Rte_DE_SG_AdWhlLockReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdNomALgtReqGroupSafe value;
} Rte_DE_SG_AdNomALgtReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimALgtLimReqGroupSafe value;
} Rte_DE_SG_AdPrimALgtLimReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimWhlAgReqGroupSafe value;
} Rte_DE_SG_AdPrimWhlAgReqGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS1_AliveCounter value;
} Rte_DE_YRS1_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS1_Checksum value;
} Rte_DE_YRS1_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_LateralAcce value;
} Rte_DE_YRS_LateralAcce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_LateralSensorState value;
} Rte_DE_YRS_LateralSensorState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_YawRate value;
} Rte_DE_YRS_YawRate;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_YawRateSensorState value;
} Rte_DE_YRS_YawRateSensorState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS2_Checksum value;
} Rte_DE_YRS2_Checksum;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_AliveCounter value;
} Rte_DE_YRS_AliveCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_LongitAcce value;
} Rte_DE_YRS_LongitAcce;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  YRS_LongitSensorState value;
} Rte_DE_YRS_LongitSensorState;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbReserved value;
} Rte_DE_AcuFbReserved;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbReserved1 value;
} Rte_DE_AcuFbReserved1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbReserved2 value;
} Rte_DE_AcuFbReserved2;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbReserved3 value;
} Rte_DE_AcuFbReserved3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbReserved4 value;
} Rte_DE_AcuFbReserved4;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AcuFbRollingCounter value;
} Rte_DE_AcuFbRollingCounter;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AdsFaultStatus value;
} Rte_DE_AdsFaultStatus;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AdsFaultStatusBackup value;
} Rte_DE_AdsFaultStatusBackup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ReqResleaseFbControl value;
} Rte_DE_ReqResleaseFbControl;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecSteerStsSafeGroup value;
} Rte_DE_SG_AdSecSteerStsSafeGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SSMBDegraded value;
} Rte_DE_SG_SSMBDegraded;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecPoseMonSafe value;
} Rte_DE_SG_SecPoseMonSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecSteerMotTq value;
} Rte_DE_SG_SecSteerMotTq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecWhlLockSts value;
} Rte_DE_SG_SecWhlLockSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkDegradedRdnt value;
} Rte_DE_SG_BrkDegradedRdnt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecPahStsGroupSafe value;
} Rte_DE_SG_AdSecPahStsGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecSafeStopActvGroupSafe value;
} Rte_DE_SG_AdSecSafeStopActvGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecWhlAgRateLimEstimdSafe value;
} Rte_DE_SG_AdSecWhlAgRateLimEstimdSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecMaxALatEstimdGroup value;
} Rte_DE_SG_SecMaxALatEstimdGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_WhlAgReqFbRdnt value;
} Rte_DE_SG_WhlAgReqFbRdnt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecSteerModStsSafeGroupByGat value;
} Rte_DE_SG_AdSecSteerModStsSafeGroupByGat;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecSteerStsSafeGroupByGatewy value;
} Rte_DE_SG_AdSecSteerStsSafeGroupByGatewy;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimVehSpdGroupSafe value;
} Rte_DE_SG_PrimVehSpdGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimALgtDataRawSafe value;
} Rte_DE_SG_PrimALgtDataRawSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimWhlAgEstimdGroupSafe value;
} Rte_DE_SG_AdPrimWhlAgEstimdGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimALatDataRawSafe value;
} Rte_DE_SG_PrimALatDataRawSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimAxleSlipStsAndRelAg value;
} Rte_DE_SG_PrimAxleSlipStsAndRelAg;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimVLatSafe value;
} Rte_DE_SG_PrimVLatSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimWhlRotDirReSafe1 value;
} Rte_DE_SG_PrimWhlRotDirReSafe1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimWhlRotToothCntr value;
} Rte_DE_SG_PrimWhlRotToothCntr;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimYawRateSafe value;
} Rte_DE_SG_PrimYawRateSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimVehMSafe value;
} Rte_DE_SG_PrimVehMSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimWhlAgSpdFrntSafe value;
} Rte_DE_SG_PrimWhlAgSpdFrntSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrimWhlAgSpdReSafe value;
} Rte_DE_SG_PrimWhlAgSpdReSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimSafeStopActvGroupSafe value;
} Rte_DE_SG_AdPrimSafeStopActvGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SSMDegraded value;
} Rte_DE_SG_SSMDegraded;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_WhlAgReqFb value;
} Rte_DE_SG_WhlAgReqFb;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimSteerStsSafeGroup value;
} Rte_DE_SG_AdPrimSteerStsSafeGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_CarModInCrashStsSafe value;
} Rte_DE_SG_CarModInCrashStsSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HornActvIf value;
} Rte_DE_HornActvIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HornSwtStsIf value;
} Rte_DE_HornSwtStsIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkTqMinReq value;
} Rte_DE_SG_BrkTqMinReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WshrFldLvlContnsIf value;
} Rte_DE_WshrFldLvlContnsIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecSteerActvnGroupSafe value;
} Rte_DE_SG_AdSecSteerActvnGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrpsnTqDir value;
} Rte_DE_SG_PrpsnTqDir;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SteerWhlTqGroup value;
} Rte_DE_SG_SteerWhlTqGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_FricEstimnFromVehDynGroup value;
} Rte_DE_SG_FricEstimnFromVehDynGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_PrpsnTqDirCpby value;
} Rte_DE_SG_PrpsnTqDirCpby;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SwtExtrLiToAPI value;
} Rte_DE_SG_SwtExtrLiToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PassSeatSts value;
} Rte_DE_PassSeatSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecBlindStopMonActvnGroupSaf value;
} Rte_DE_SG_AdSecBlindStopMonActvnGroupSaf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_DrvrGearShiftManReq value;
} Rte_DE_SG_DrvrGearShiftManReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SnsrClngErrIf value;
} Rte_DE_SG_SnsrClngErrIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_VehMGroup value;
} Rte_DE_SG_VehMGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WinWipgAutCmdIf value;
} Rte_DE_WinWipgAutCmdIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WipgFrntAutModIf value;
} Rte_DE_WipgFrntAutModIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WiprActvIf value;
} Rte_DE_WiprActvIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WshngCycActvIf value;
} Rte_DE_WshngCycActvIf;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AgDataRawSafe value;
} Rte_DE_SG_AgDataRawSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_ClstrSts1ForAutnmsDrv value;
} Rte_DE_SG_ClstrSts1ForAutnmsDrv;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSteerPaddlPsdGroupSafe value;
} Rte_DE_SG_AdSteerPaddlPsdGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SteerWhlSnsr value;
} Rte_DE_SG_SteerWhlSnsr;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  WhlMotSysCluSts value;
} Rte_DE_WhlMotSysCluSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AhbcIndcnToAPI value;
} Rte_DE_AhbcIndcnToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorDrvrMovmtFailNotif value;
} Rte_DE_DoorDrvrMovmtFailNotif;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorDrvrReMovmtFailNotif value;
} Rte_DE_DoorDrvrReMovmtFailNotif;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorPassMovmtFailNotif value;
} Rte_DE_DoorPassMovmtFailNotif;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorPassReMovmtFailNotif value;
} Rte_DE_DoorPassReMovmtFailNotif;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BltLockStAtDrvr value;
} Rte_DE_SG_BltLockStAtDrvr;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BltLockStAtPass value;
} Rte_DE_SG_BltLockStAtPass;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BltLockStAtRowSecLe value;
} Rte_DE_SG_BltLockStAtRowSecLe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BltLockStAtRowSecRi value;
} Rte_DE_SG_BltLockStAtRowSecRi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_DoorPassRePosnStsToAPI value;
} Rte_DE_SG_DoorPassRePosnStsToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ExtDcDcActvnAllwd value;
} Rte_DE_ExtDcDcActvnAllwd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PtTqAtAxleAvlFrntMax value;
} Rte_DE_PtTqAtAxleAvlFrntMax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PtTqAtAxleAvlReMax value;
} Rte_DE_PtTqAtAxleAvlReMax;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkPedlVal value;
} Rte_DE_SG_BrkPedlVal;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_ClstrSts2ForAutnmsDrv value;
} Rte_DE_SG_ClstrSts2ForAutnmsDrv;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AdActvnOkFromVehDyn value;
} Rte_DE_AdActvnOkFromVehDyn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PtTqAtAxleAvlReMaxLong value;
} Rte_DE_PtTqAtAxleAvlReMaxLong;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AutnmsDrvModMngtGlbSafe1 value;
} Rte_DE_SG_AutnmsDrvModMngtGlbSafe1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_EngFltIndcn value;
} Rte_DE_SG_EngFltIndcn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CllsnFwdWarnReq value;
} Rte_DE_CllsnFwdWarnReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CllsnThreat value;
} Rte_DE_CllsnThreat;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CnclReqForCrsCtrl value;
} Rte_DE_CnclReqForCrsCtrl;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CooltFlowInDtElecForExt value;
} Rte_DE_CooltFlowInDtElecForExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  CooltTInDtElecForExt value;
} Rte_DE_CooltTInDtElecForExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  IndcrDisp1WdSts value;
} Rte_DE_IndcrDisp1WdSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  IndcrTurnSts1WdSts value;
} Rte_DE_IndcrTurnSts1WdSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_CllsnAidSnvtySeld value;
} Rte_DE_SG_CllsnAidSnvtySeld;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SrvRqrdForCllsnAid value;
} Rte_DE_SrvRqrdForCllsnAid;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AsySftyDecelEnadByVehDyn value;
} Rte_DE_AsySftyDecelEnadByVehDyn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EngOilPWarn value;
} Rte_DE_EngOilPWarn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  InhbOfAsySftyDecelByVehDyn value;
} Rte_DE_InhbOfAsySftyDecelByVehDyn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PrpsnErrIndcnReq value;
} Rte_DE_PrpsnErrIndcnReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_RoadLoadNom value;
} Rte_DE_SG_RoadLoadNom;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TankFlapSts value;
} Rte_DE_TankFlapSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrsmFltIndcn value;
} Rte_DE_TrsmFltIndcn;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorDrvrReSts value;
} Rte_DE_DoorDrvrReSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorDrvrSts value;
} Rte_DE_DoorDrvrSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorPassReSts value;
} Rte_DE_DoorPassReSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DoorPassSts value;
} Rte_DE_DoorPassSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EngOilLvlSts value;
} Rte_DE_EngOilLvlSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HoodSts value;
} Rte_DE_HoodSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_ALnchTiDly3 value;
} Rte_DE_SG_ALnchTiDly3;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AlrmSts1 value;
} Rte_DE_SG_AlrmSts1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  TrSts value;
} Rte_DE_TrSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AsySftyBrkDlyEstimd value;
} Rte_DE_AsySftyBrkDlyEstimd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  HvSysActvStsExt1 value;
} Rte_DE_HvSysActvStsExt1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AccrPedlPsd value;
} Rte_DE_SG_AccrPedlPsd;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkPedlPsdSafeGroup value;
} Rte_DE_SG_BrkPedlPsdSafeGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_OvrdDecelByDrvr value;
} Rte_DE_SG_OvrdDecelByDrvr;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  AccrPedlRat value;
} Rte_DE_AccrPedlRat;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ChrgnUReqExt value;
} Rte_DE_ChrgnUReqExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  DrvrDecelReq value;
} Rte_DE_DrvrDecelReq;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PtGearAct value;
} Rte_DE_PtGearAct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_TirePWarnFrntRi value;
} Rte_DE_SG_TirePWarnFrntRi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_TirePWarnReLe value;
} Rte_DE_SG_TirePWarnReLe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_TirePWarnReRi value;
} Rte_DE_SG_TirePWarnReRi;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  PtTqAtAxleAvlFrntMaxLong value;
} Rte_DE_PtTqAtAxleAvlFrntMaxLong;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecBrkActvnGroupSafe value;
} Rte_DE_SG_AdSecBrkActvnGroupSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkFricTqTotAtWhlsAct value;
} Rte_DE_SG_BrkFricTqTotAtWhlsAct;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EgyAvlChrgTot value;
} Rte_DE_EgyAvlChrgTot;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  EgyAvlDchaTot value;
} Rte_DE_EgyAvlDchaTot;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_DrvrIntvSts value;
} Rte_DE_SG_DrvrIntvSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_DrvrPrsntGroup value;
} Rte_DE_SG_DrvrPrsntGroup;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  BattChrgnTiEstimdExt value;
} Rte_DE_BattChrgnTiEstimdExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  BattIExt value;
} Rte_DE_BattIExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  BattUExt value;
} Rte_DE_BattUExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ChrgnTypExt value;
} Rte_DE_ChrgnTypExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  ChrgrHndlStsExt value;
} Rte_DE_ChrgrHndlStsExt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AutnmsDrvModMngtExtSafe value;
} Rte_DE_SG_AutnmsDrvModMngtExtSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_BrkDegraded value;
} Rte_DE_SG_BrkDegraded;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehManDeactvnReqInProgs1 value;
} Rte_DE_VehManDeactvnReqInProgs1;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  VehUsgSt value;
} Rte_DE_VehUsgSt;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_StandStillMgrStsForHldSafe value;
} Rte_DE_SG_StandStillMgrStsForHldSafe;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SwtIndcrToAPI value;
} Rte_DE_SG_SwtIndcrToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SwtBeamHiToAPI value;
} Rte_DE_SwtBeamHiToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SwtLiHzrdWarnToAPI value;
} Rte_DE_SwtLiHzrdWarnToAPI;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_ALgtMaxAvl value;
} Rte_DE_SG_ALgtMaxAvl;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_WhlLockSts value;
} Rte_DE_SG_WhlLockSts;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecWhlAgReqGroupSafe_A value;
} Rte_DE_SG_AdSecWhlAgReqGroupSafe_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdSecALgtLimReqGroupSafe_A value;
} Rte_DE_SG_AdSecALgtLimReqGroupSafe_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_SecAdNomALgtReqGroupSafe_A value;
} Rte_DE_SG_SecAdNomALgtReqGroupSafe_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AutnmsDrvStReq_A value;
} Rte_DE_SG_AutnmsDrvStReq_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdNomALgtReqGroupSafe_A value;
} Rte_DE_SG_AdNomALgtReqGroupSafe_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimALgtLimReqGroupSafe_A value;
} Rte_DE_SG_AdPrimALgtLimReqGroupSafe_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimPose_A value;
} Rte_DE_SG_AdPrimPose_A;
/* PRQA L:L1 */

 /* PRQA S 1039 L1 */ /* MD_Rte_1039 */
typedef struct
{
  SG_AdPrimWhlAgReqGroupSafe_A value;
} Rte_DE_SG_AdPrimWhlAgReqGroupSafe_A;
/* PRQA L:L1 */

#endif /* RTE_DATA_HANDLE_TYPE_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_1039:  MISRA rule: Rule1.2
     Reason:     Same macro and function names are required to meet AUTOSAR spec.
     Risk:       No functional risk. Macro will be undefined before function definition.
     Prevention: Not required.

*/
