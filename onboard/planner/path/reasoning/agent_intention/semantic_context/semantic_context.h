#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_SEMANTIC_CONTEXT_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_SEMANTIC_CONTEXT_H_

#include <map>
#include <memory>
#include <unordered_map>
#include <vector>

#include "geometry/model/polyline_curve.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_candidates.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_info.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/required_lateral_gap_info.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/path_solver/constraint_manager/road_boundaries.h"
#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/lateral_blocking_state_generator.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/semantic_context/narrow_meeting/narrow_meeting_params.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/object_reasoning_info.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/avoid_nudge_identifier.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/inlane_nudge_identifier.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/xlane_nudge_identifier.h"
#include "planner/path/reasoning/agent_intention/semantic_context/semantic_context_result.h"
#include "planner/speed/occlusion/hallucinated_agent_manager.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/world_model/lane_blockage/lane_blockage_detector.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/path_reasoning_seed.pb.h"
#include "planner_protos/traffic_rule_reasoning_info.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {
namespace path {

// Class AbstractReasoner represents the base reasoner.
class SemanticContext {
 public:
  // Constructor.
  SemanticContext() = delete;
  SemanticContext(const pb::AgentIntentionConfig& config,
                  pb::BehaviorType behavior_type,
                  pb::LaneKeepBehaviorType lane_keep_behavior_type,
                  const lane_selection::LaneSequenceCandidates& candidates)
      : agent_intention_config_(config),
        behavior_type_(behavior_type),
        lane_keep_behavior_type_(lane_keep_behavior_type),
        blocking_state_generator_(config.blocking_generator_config(),
                                  behavior_type),
        candidates_(candidates),
        avoid_nudge_identifier_(behavior_type_),
        xlane_nudge_identifier_(lane_keep_behavior_type) {}

  // Updates the semantic context result for given lane sequence info.
  SemanticContextResult Update(
      const WorldModel& world_model,
      const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
      const lateral_clearance::LateralClearanceData& lateral_clearance_data,
      const RequiredLateralGapInfo& required_lateral_gap_info,
      const speed::Profile& emergency_brake_speed_profile,
      const speed::Profile& lower_bound_speed_profile,
      const speed::Profile& upper_bound_speed_profile,
      const math::Range1d& planning_horizon_range,
      const pb::DecoupledManeuverSeed& previous_iter_seed,
      const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const LaneChangeInfo& lane_change_info, bool is_ego_in_lane,
      bool should_reduce_gap_for_pass_state,
      pb::PathReasoningSeed& path_reasoning_seed,
      pb::SemanticContextDebug* debug) const;

  void UpdateImaginaryAgent(
      const WorldModel& world_model,
      const pb::DecoupledManeuverSeed& previous_decoupled_maneuver_seed,
      const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
      pb::MotionMode motion_mode);

 private:
  // Calculates the ObjectReasoningInfo of each object(agent and construction
  // zone).
  std::map<TypedObjectId, ObjectReasoningInfo> GenerateObjectReasoningInfoMap(
      const AgentInLaneStatesMap& agent_state_map,
      const RequiredLateralGapInfo& required_lateral_gap_info,
      const std::unordered_map<ObjectId, PlannerObject>& planner_objects,
      const PlannerObjectsHistory& planner_objects_history,
      const tbb::concurrent_unordered_map<
          ObjectId, std::vector<PredictedTrajectoryWrapper>>&
          object_predictions,
      const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
      const RobotStateSnapshot& robot_state_snapshot,
      const EgoInLaneParams& ego_param,
      const speed::Profile& emergency_brake_speed_profile,
      const speed::Profile& lower_bound_speed_profile,
      const speed::Profile& upper_bound_speed_profile,
      const pb::IntentionResult& last_intent_seed,
      const pb::PathReasoningSeed& last_path_reasoning_seed,
      const pb::PullOverInfoSeed& last_pull_over_info_seed,
      const pb::UnstuckModeSeed& unstuck_mode_seed,
      const math::Range1d& planning_horizon_range,
      const math::geometry::PolylineCurve2d& reference_line,
      const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
      const std::vector<lane_selection::TrafficLightInfoReference>&
          lane_sequence_tl_infos,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const LaneChangeInfo& lane_change_info,
      const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
      const planner::lateral_clearance::LateralClearanceData&
          lateral_clearance_data,
      const pnc_map::Lane& current_lane, bool should_reduce_gap_for_pass_state,
      bool is_ego_near_merge, pb::PathReasoningSeed& path_reasoning_seed,
      bool use_tbb) const;

  // Calculates the ObjectReasoningInfo of each object given the object
  // occupancy state map.
  std::map<TypedObjectId, ObjectReasoningInfo> GenerateObjectReasoningInfoMap(
      const ObjectOccupancyStateMap& object_occupancy_state_map,
      const PlannerObjectsHistory& planner_objects_history,
      const std::vector<ConstructionZoneInLaneState>& construction_zone_states,
      const RequiredLateralGapInfo& required_lateral_gap_info,
      const RobotStateSnapshot& robot_state_snapshot,
      const EgoInLaneParams& ego_param,
      const speed::Profile& emergency_brake_speed_profile,
      const speed::Profile& lower_bound_speed_profile,
      const speed::Profile& upper_bound_speed_profile,
      const pb::IntentionResult& last_intent_seed,
      const pb::PathReasoningSeed& last_path_reasoning_seed,
      const pb::PullOverInfoSeed& last_pull_over_info_seed,
      const pb::UnstuckModeSeed& unstuck_mode_seed,
      const math::Range1d& planning_horizon_range,
      const math::geometry::PolylineCurve2d& reference_line,
      const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
      const std::vector<lane_selection::TrafficLightInfoReference>&
          lane_sequence_tl_infos,
      const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
      const LaneChangeInfo& lane_change_info,
      const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
      const planner::lateral_clearance::LateralClearanceData&
          lateral_clearance_data,
      const pnc_map::Lane& current_lane, bool should_reduce_gap_for_pass_state,
      bool is_ego_near_merge, pb::PathReasoningSeed& path_reasoning_seed,
      bool use_tbb) const;

  // Analyzes the lane_blockage_info, traffic_flow_info for all frames.
  std::vector<FrameAnalyzerResult> AnalyzeFrame(
      const WorldModel& world_model, const speed::Profile& speed_upper_bound,
      const LaneSequenceResult& lane_sequence_result,
      const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
      const EgoInLaneParams& ego_param,
      const lane_selection::LaneSequenceTrafficRules& traffic_rules,
      const math::Range1d& planning_horizon_range,
      const std::map<TypedObjectId, ObjectReasoningInfo>&
          object_reasoning_info_map,
      const AssistInstruction* assist_instruction,
      const lateral_clearance::LateralClearanceData& lateral_clearance_data,
      const pb::DecoupledManeuverSeed& previous_iter_seed,
      pb::PathReasoningSeed* path_reasoning_seed,
      pb::SemanticContextDebug* debug) const;

  // Identifies the scene type according to the frame informations.
  ScenarioIdentifierResult CategorizeScenario(
      const std::map<TypedObjectId, ObjectReasoningInfo>&
          object_reasoning_info_map,
      const EgoInLaneParams& ego_param,
      const std::vector<FrameAnalyzerResult>& frame_analyzer_results,
      const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
      pb::PathReasoningSeed* path_reasoning_seed) const;

  NarrowMeetingSceneParams GenerateNarrowMeetingSceneParams(
      pb::ScenarioRecognition::ScenarioType scenario_type,
      const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
      const math::Range1d& planning_horizon_range,
      const RobotStateSnapshot& robot_state_snapshot,
      const EgoInLaneParams& ego_param,
      const std::map<TypedObjectId, ObjectReasoningInfo>&
          object_reasoning_info_map,
      const pnc_map::Lane* current_lane,
      const speed::Profile& lower_bound_speed_profile,
      const pb::NarrowMeetingSeed& last_seed,
      const lateral_clearance::LateralClearanceData& lateral_clearance_data,
      bool is_in_autonomous_mode, pb::NarrowMeetingSeed* mutable_seed) const;

  const tbb::concurrent_unordered_map<ObjectId,
                                      std::vector<PredictedTrajectoryWrapper>>&
  prediction_map_for_imaginary_planner_object() const {
    return prediction_map_for_imaginary_planner_object_;
  }

  const std::unordered_map<ObjectId, PlannerObject>&
  imaginary_planner_object_map() const {
    return imaginary_planner_object_map_;
  }

  const RequiredLateralGapInfo& imaginary_objects_required_lateral_gap_info()
      const {
    return *DCHECK_NOTNULL(imaginary_objects_required_lateral_gap_info_);
  }

  const ObjectOccupancyStateMap& imaginary_object_occupancy_state_map() const {
    return imaginary_object_occupancy_state_map_;
  }

  // Returns in lane state map of imaginary tracked objects, key is object's ID,
  // value is the object's in lane state including the predicted
  // predicted_trajectories' snapshot in lane states.
  const AgentInLaneStatesMap& imaginary_tracked_object_in_lane_param_map()
      const {
    return DCHECK_NOTNULL(imaginary_agent_in_lane_state_generator_)
        ->planner_object_param_map();
  }

  // Agent intention config used in this generator.
  const pb::AgentIntentionConfig& agent_intention_config_;
  // Behavior type that this generator corresponds to.
  const pb::BehaviorType behavior_type_ = pb::UNDEFINED_BEHAVIOR_TYPE;
  // Indicates the exact lane keep behavior type.
  const pb::LaneKeepBehaviorType lane_keep_behavior_type_ = pb::LK_NA;
  // Lateral blocking state generator.
  LateralBlockingStateGenerator blocking_state_generator_;
  // Reference to the lane sequence candidate.
  const lane_selection::LaneSequenceCandidates& candidates_;

  // The scenario identifier.
  AvoidNudgeIdentifier avoid_nudge_identifier_;
  InLaneNudgeIdentifier inlane_nudge_identifier_;
  XLaneNudgeIdentifier xlane_nudge_identifier_;

  // The map of the imaginary agent object occupancy states.
  ObjectOccupancyStateMap imaginary_object_occupancy_state_map_;

  // Imaginary agent in lane state generator.
  std::unique_ptr<lane_selection::AgentInLaneStateGenerator>
      imaginary_agent_in_lane_state_generator_ = nullptr;

  // Imaginary planner objects map.
  std::unordered_map<ObjectId, PlannerObject> imaginary_planner_object_map_;

  // Object prediction map for imaginary planner object.
  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      prediction_map_for_imaginary_planner_object_;

  // Required lateral gap info for each imaginary object.
  std::unique_ptr<RequiredLateralGapInfo>
      imaginary_objects_required_lateral_gap_info_ = nullptr;

  // Hallucinate agent manager used to generated part of imaginary object of
  // this lane sequence.
  std::unique_ptr<speed::HallucinatedAgentManager> hallucinate_agent_manager_ =
      nullptr;
};

// Check if a lane sequence has a UPL turn.
inline const pnc_map::Lane* GetUplLane(
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const adv_geom::PathCurve2d& reference_path, const double min_distance) {
  const double ego_arc_length =
      lane_sequence_info.robot_in_lane_param().arclength_m;
  const double kEgoPositionOffset = 1.0;
  const auto traffic_lights =
      speed::traffic_rules::GetLaneSequenceTrafficLights(
          lane_sequence_info.lane_sequence_iterator(),
          *world_model.GetLatestJointPncMapService(), reference_path,
          world_model.traffic_light_detection(),
          world_model.robot_state().plan_init_state_snapshot().timestamp(),
          world_model.robot_state().region(), &previous_iter_seed,
          -kEgoPositionOffset);
  const auto& lane_sequence_conflicting_lanes =
      speed::traffic_rules::ComputeConflictingLanesInLaneSequence(
          lane_sequence_info.lane_sequence_iterator(),
          *world_model.GetLatestJointPncMapService(), reference_path,
          traffic_lights, planner::pb::MotionMode::FORWARD, kEgoPositionOffset);
  for (const auto* lane :
       lane_sequence_info.lane_sequence_iterator().lane_sequence()) {
    const auto& [lane_start_arclength, lane_end_arclength] =
        lane_selection::FindLaneArcRangeOnACurve(lane->center_line(),
                                                 reference_path);
    const double ego_before_lane_start = lane_start_arclength - ego_arc_length;
    const double ego_after_lane_end = ego_arc_length - lane_end_arclength;
    if (ego_after_lane_end > 0.0) {
      continue;
    }
    if (ego_before_lane_start > min_distance) {
      break;
    }

    if (speed::reasoning_util::IsUnprotectedLeftTurn(
            lane, lane_sequence_conflicting_lanes)) {
      return lane;
    }
  }

  return nullptr;
}

}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_SEMANTIC_CONTEXT_SEMANTIC_CONTEXT_H_
