#include "planner/path/reasoning/agent_intention/reasoner/cross_agent_path_reasoner/cross_agent_path_reasoner.h"

#include <gtest/gtest.h>

#include "planner/behavior/directive_generator/lateral_clearance_corridor_directive_generator.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/reasoner/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/semantic_context/test/test_utility.h"

namespace planner {
namespace path {
namespace {
class CrossAgentPathReasonerTest : public ::testing::Test {
 public:
  void SetUp() override {
    // AgentInLaneStates
    agent_inlane_state_.agent_metadata
        .features[pb::AgentFeature::IS_STATIONARY] = false;
    agent_inlane_state_.agent_metadata.agent_type =
        voy::perception::ObjectType::VEHICLE;

    ego_param_ = test::GetEgoInLaneParams();

    pb::LateralBlockingInfo info;
    info.set_is_beyond_intention_range(false);
    info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
    soft_blocking_infos_ = std::vector<pb::LateralBlockingInfo>(20, info);
    info.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
    hard_blocking_infos_ = std::vector<pb::LateralBlockingInfo>(40, info);
    // Lane structure.
    const std::vector<math::geometry::Point2d> nominal_path_points = {
        {0.0, 0.0}, {10.0, 0.0}};
    const math::geometry::PolylineCurve2d nominal_path_curve(
        nominal_path_points);
    lane_sequence_geometry_.nominal_path = nominal_path_curve;
    const std::vector<math::geometry::Point2d> left_boundary_points = {
        {0.0, 3.0}, {10.0, 3.0}};
    const math::geometry::PolylineCurve2d left_boundary_curve(
        left_boundary_points);
    lane_sequence_geometry_.left_lane_boundary = left_boundary_curve;
    const std::vector<math::geometry::Point2d> right_boundary_points = {
        {0.0, -3.0}, {10.0, -3.0}};
    const math::geometry::PolylineCurve2d right_boundary_curve(
        right_boundary_points);
    lane_sequence_geometry_.right_lane_boundary = right_boundary_curve;

    drivable_space_corridor_.driving_corridor.left_boundary =
        lane_sequence_geometry_.left_lane_boundary;
    drivable_space_corridor_.driving_corridor.right_boundary =
        lane_sequence_geometry_.right_lane_boundary;
    const math::Range1d planning_horizon_range(
        0.0, lane_sequence_geometry_.nominal_path.GetTotalArcLength());
    LateralClearanceCorridorDirectiveGenerator corridor_generator(
        kDrivableCorridorSamplingResolutionInMeter, kMaxLateralHorizonInMeter);
    drivable_space_corridor_.driving_lateral_corridor =
        corridor_generator.GenerateCorridor(
            lane_sequence_geometry_.nominal_path, planning_horizon_range,
            {drivable_space_corridor_.driving_corridor.left_boundary},
            {drivable_space_corridor_.driving_corridor.right_boundary});
  }

  EgoInLaneParams ego_param_;
  AgentInLaneStates agent_inlane_state_;
  RequiredLateralGap required_lat_gap_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  std::vector<pb::LateralBlockingInfo> soft_blocking_infos_;
  std::vector<pb::LateralBlockingInfo> hard_blocking_infos_;

  LateralBlockingStateMetaData blocking_state_data_;
  TrajectoryIntentionMetaData trajectory_intention_data_;
  path::ScenarioIdentifierResult scenario_identify_result_;
  pb::TrafficRuleReasoningInfoDebug traffic_rule_reasoning_info_;
  // The following parameters are used for object occupancy states.
  lane_selection::LaneSequenceGeometry lane_sequence_geometry_;
  std::vector<PlannerObject> planner_object_list_;
  std::vector<prediction::pb::PredictedTrajectory> predicted_trajectory_protos_;
  std::vector<std::vector<PredictedTrajectoryWrapper>>
      predicted_trajectory_wrappers_;
  ObjectOccupancyStateMap object_occupancy_state_map_;
  DrivableSpaceCorridor drivable_space_corridor_;
  pb::ObstacleSTBufferConfig st_buffer_config_;
  pb::PathReasonerSeed last_path_reasoner_seed_;
};

TEST_F(CrossAgentPathReasonerTest, PreReasonTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.nominal_path,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  CrossAgentPathReasoner reasoner(ego_context_info);
  // 1. soft + hard + soft for cross agent.
  blocking_state_data_.trajectory_blocking_infos = soft_blocking_infos_;
  blocking_state_data_.trajectory_blocking_infos.insert(
      blocking_state_data_.trajectory_blocking_infos.end(),
      hard_blocking_infos_.begin(), hard_blocking_infos_.end());
  blocking_state_data_.trajectory_blocking_infos.insert(
      blocking_state_data_.trajectory_blocking_infos.end(),
      soft_blocking_infos_.begin(), soft_blocking_infos_.end());
  // Get blocking sequence infos.
  blocking_state_data_.blocking_sequences = GetEffectiveBlockingSequences(
      blocking_state_data_.trajectory_blocking_infos);
  blocking_state_data_.blocking_sequence_type =
      GetBlockingSequencesType(blocking_state_data_.blocking_sequences);
  reasoner.PreReason(agent_inlane_state_, blocking_state_data_,
                     AgentSLBoundary(), trajectory_intention_data_);
  EXPECT_EQ(trajectory_intention_data_.intention_type,
            pb::TrajectoryState::MIXED);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequence_type,
            pb::BlockingSequenceType::kMixedBlocking);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences.size(), 2);
  EXPECT_EQ(trajectory_intention_data_.reasoner_id,
            pb::AgentReasonerId::CROSS_AGENT);
  test::CheckBlockingSequence(
      trajectory_intention_data_.blocking_sequences[0],
      /*index_range=*/std::make_pair(0, 19),
      /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
  test::CheckBlockingSequence(
      trajectory_intention_data_.blocking_sequences[1],
      /*index_range=*/
      std::make_pair(20,
                     blocking_state_data_.trajectory_blocking_infos.size() - 1),
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);

  // 2. hard + soft for cross agent.
  blocking_state_data_.trajectory_blocking_infos = hard_blocking_infos_;
  blocking_state_data_.trajectory_blocking_infos.insert(
      blocking_state_data_.trajectory_blocking_infos.end(),
      soft_blocking_infos_.begin(), soft_blocking_infos_.end());
  // Set the crossing agent with oncoming snapshots.
  blocking_state_data_.reasoning_info.set_is_oncoming_ahead_of_ego_front_bumper(
      true);
  // Get blocking sequence infos.
  blocking_state_data_.blocking_sequences = GetEffectiveBlockingSequences(
      blocking_state_data_.trajectory_blocking_infos);
  blocking_state_data_.blocking_sequence_type =
      GetBlockingSequencesType(blocking_state_data_.blocking_sequences);
  reasoner.PreReason(agent_inlane_state_, blocking_state_data_,
                     AgentSLBoundary(), trajectory_intention_data_);
  EXPECT_EQ(trajectory_intention_data_.intention_type,
            pb::TrajectoryState::MIXED);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequence_type,
            pb::BlockingSequenceType::kCompleteBlocking);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences.size(), 1);
  EXPECT_EQ(trajectory_intention_data_.reasoner_id,
            pb::AgentReasonerId::CROSS_AGENT);
  EXPECT_TRUE(
      trajectory_intention_data_.blocking_sequences[0].has_buffer_config());
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences[0]
                .buffer_config()
                .temporal_left_buffer_s(),
            0.0);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences[0]
                .buffer_config()
                .temporal_right_buffer_s(),
            0.3);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences[0]
                .buffer_config()
                .spatial_top_buffer_m(),
            0.0);
  EXPECT_EQ(trajectory_intention_data_.blocking_sequences[0]
                .buffer_config()
                .spatial_bottom_buffer_m(),
            1.0);
  test::CheckBlockingSequence(
      trajectory_intention_data_.blocking_sequences[0],
      /*index_range=*/
      std::make_pair(0,
                     blocking_state_data_.trajectory_blocking_infos.size() - 1),
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
}

TEST_F(CrossAgentPathReasonerTest, PostReasonTest_EnableCutBehind) {
  const NudgeMotionChecker nudge_motion_checker(
      lane_sequence_geometry_.nominal_path,
      planner::test::GenerateRobotStateSnapshot(
          /*front_pose=*/math::geometry::Point2d{0.1, 0.0}));
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      &nudge_motion_checker,
      ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.nominal_path,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  CrossAgentPathReasoner reasoner(ego_context_info);
  planner_object_list_.clear();
  predicted_trajectory_protos_.clear();
  predicted_trajectory_wrappers_.clear();
  object_occupancy_state_map_.clear();
  planner_object_list_.reserve(planner::test::kNumObjectForTest);
  predicted_trajectory_protos_.reserve(planner::test::kNumObjectForTest);
  predicted_trajectory_wrappers_.reserve(planner::test::kNumObjectForTest);

  IntentionResultMetaData intention_result(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/false, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::PASS_LEFT,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 1)},
      /*required_lat_gap=*/required_lat_gap_);
  AgentTrajectoryInLaneStates trajectory_inlane_states;
  trajectory_inlane_states.trajectory_id = 0;
  StampedAgentSnapshotInLaneState state_1, state_2;
  trajectory_inlane_states.predicted_states.push_back(state_1);
  trajectory_inlane_states.predicted_states.push_back(state_2);
  agent_inlane_state_.predicted_trajectories.insert(
      {trajectory_inlane_states.trajectory_id, trajectory_inlane_states});

  // 1. Nudge the agent that is yield ego and ego cut front.
  // Sets the agent to yield ego vehicle.
  planner::pb::YieldIntentionData yield_intention;
  yield_intention.set_yield_probability(0.9999);
  yield_intention.set_consecutive_yield_count(10);
  trajectory_intention_data_.yield_intention_data = yield_intention;
  // Sets the ego cut front.
  agent_inlane_state_.tracked_state.inlane_param.center_line_side =
      math::pb::kRight;
  // Sets the agent blocking sequence type.
  trajectory_intention_data_.blocking_sequence_type =
      pb::BlockingSequenceType::kMixedBlocking;
  reasoner.PostReason(agent_inlane_state_, blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision,
            pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(intention_result.is_overtaken, true);
  ASSERT_EQ(intention_result.agent_constraints.size(), 1);
  EXPECT_EQ(intention_result.agent_constraints[0].pass_state,
            pb::SnapshotIntention::PASS_LEFT);

  // Test object occupancy state.
  intention_result.agent_constraints.clear();
  // Sets the agent center line side to the right.
  voy::TrackedObject tracked_object_1 =
      planner::test::ConstructTrackedObjectFromDimension(
          /*obj_id=*/1, /*x0=*/5.0,
          /*y0=*/-2.0,
          /*width=*/1.9, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
          /*heading=*/0.0);
  planner::test::ConstructObjectOccupancyStateForTest(
      lane_sequence_geometry_, ego_param_, tracked_object_1,
      planner_object_list_, predicted_trajectory_protos_,
      predicted_trajectory_wrappers_, object_occupancy_state_map_);
  reasoner.PostReason(*object_occupancy_state_map_.at(1), blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision,
            pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(intention_result.is_overtaken, true);
  ASSERT_EQ(intention_result.agent_constraints.size(), 1);
  EXPECT_EQ(intention_result.agent_constraints[0].pass_state,
            pb::SnapshotIntention::PASS_LEFT);

  // 2. Nudge the agent that not yield ego and ego cut behind.
  intention_result.agent_constraints.clear();
  // Sets the agent not to yield ego vehicle.
  planner::pb::YieldIntentionData not_yield_intention;
  not_yield_intention.set_yield_probability(0.1);
  not_yield_intention.set_consecutive_yield_count(0);
  trajectory_intention_data_.yield_intention_data = not_yield_intention;
  // Sets the ego cut behind.
  agent_inlane_state_.tracked_state.inlane_param.center_line_side =
      math::pb::kLeft;
  reasoner.PostReason(agent_inlane_state_, blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision,
            pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(intention_result.is_overtaken, true);
  ASSERT_EQ(intention_result.agent_constraints.size(), 1);
  EXPECT_EQ(intention_result.agent_constraints[0].pass_state,
            pb::SnapshotIntention::PASS_LEFT);

  // Test object occupancy state.
  intention_result.agent_constraints.clear();
  // Sets the agent center line side to the left.
  voy::TrackedObject tracked_object_2 =
      planner::test::ConstructTrackedObjectFromDimension(
          /*obj_id=*/2, /*x0=*/5.0,
          /*y0=*/2.0,
          /*width=*/1.9, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
          /*heading=*/0.0);
  planner::test::ConstructObjectOccupancyStateForTest(
      lane_sequence_geometry_, ego_param_, tracked_object_2,
      planner_object_list_, predicted_trajectory_protos_,
      predicted_trajectory_wrappers_, object_occupancy_state_map_);
  reasoner.PostReason(*object_occupancy_state_map_.at(2), blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision,
            pb::SnapshotIntention::PASS_LEFT);
  EXPECT_EQ(intention_result.is_overtaken, true);
  ASSERT_EQ(intention_result.agent_constraints.size(), 1);
  EXPECT_EQ(intention_result.agent_constraints[0].pass_state,
            pb::SnapshotIntention::PASS_LEFT);

  // 3. Ignore the agent that not yield ego and ego cut front.
  intention_result.agent_constraints.clear();
  // Sets the ego cut front and ego can emergency brake.
  AgentSnapshotInLaneParam tracked_param;
  tracked_param.center_line_side = math::pb::kRight;
  tracked_param.full_body_start_arclength_m = 10.0;
  agent_inlane_state_.tracked_state.inlane_param = tracked_param;
  reasoner.PostReason(agent_inlane_state_, blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision, pb::SnapshotIntention::IGNORE);

  // Test object occupancy state.
  intention_result.agent_constraints.clear();
  // Sets the agent center line side to left.
  intention_result.debug_str.clear();
  // Restores the lateral decision.
  intention_result.lateral_decision = pb::SnapshotIntention::PASS_LEFT;
  voy::TrackedObject tracked_object_3 =
      planner::test::ConstructTrackedObjectFromDimension(
          /*obj_id=*/3, /*x0=*/5.0,
          /*y0=*/-2.0,
          /*width=*/1.9, /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0,
          /*heading=*/0.0);
  planner::test::ConstructObjectOccupancyStateForTest(
      lane_sequence_geometry_, ego_param_, tracked_object_3,
      planner_object_list_, predicted_trajectory_protos_,
      predicted_trajectory_wrappers_, object_occupancy_state_map_);
  reasoner.PostReason(*object_occupancy_state_map_.at(3), blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision, pb::SnapshotIntention::IGNORE);
}

TEST_F(CrossAgentPathReasonerTest, PostReasonrLaneEncroachNudgeTest) {
  FLAGS_planning_enable_lane_encroach_nudge = true;
  traffic_rule_reasoning_info_.set_safe_pass_state_for_adjacent_lanes(
      pb::SnapshotIntention::PASS_BOTH);
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      lane_sequence_geometry_.nominal_path,
      lane_sequence_geometry_.nominal_path,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  CrossAgentPathReasoner reasoner(ego_context_info);
  IntentionResultMetaData intention_result(
      /*object_id=*/TypedObjectId(
          /*raw_id=*/1, /*type=*/pb::ObjectSourceType::kTrackedObject),
      /*blocking_state_size=*/20,
      /*is_static=*/false, /*is_overtaken=*/true,
      /*lateral_decision=*/pb::SnapshotIntention::IGNORE,
      /*lon_decision=*/speed::pb::SpeedDecision::PASS,
      /*effective_intention_index_ranges=*/
      std::vector<math::Range<int>>{math::Range<int>(0, 1)},
      /*required_lat_gap=*/required_lat_gap_);

  AgentTrajectoryInLaneStates trajectory_inlane_states;
  trajectory_inlane_states.predicted_states =
      std::vector<StampedAgentSnapshotInLaneState>{
          StampedAgentSnapshotInLaneState(), StampedAgentSnapshotInLaneState()};
  agent_inlane_state_.predicted_trajectories.insert(
      {/*trajectory_id=*/0, trajectory_inlane_states});
  // 1. No need revise intention result for MixedBlocking.
  blocking_state_data_.reasoning_info.set_is_beside_ego(true);
  blocking_state_data_.reasoning_info.set_contain_emergency_brake_state(true);
  blocking_state_data_.reasoning_info.set_tracked_side_to_ego(math::pb::kLeft);

  reasoner.PostReason(agent_inlane_state_, blocking_state_data_,
                      trajectory_intention_data_, intention_result);
  EXPECT_EQ(intention_result.is_static, false);
  EXPECT_EQ(intention_result.lateral_decision,
            pb::SnapshotIntention::PASS_RIGHT);
  EXPECT_EQ(intention_result.is_overtaken, false);
  ASSERT_EQ(intention_result.agent_constraints.size(), 1);
  EXPECT_EQ(intention_result.agent_constraints[0].pass_state,
            pb::SnapshotIntention::PASS_RIGHT);
}

}  // namespace
}  // namespace path
}  // namespace planner
