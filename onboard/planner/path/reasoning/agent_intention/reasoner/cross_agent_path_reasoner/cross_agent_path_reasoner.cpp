#include "planner/path/reasoning/agent_intention/reasoner/cross_agent_path_reasoner/cross_agent_path_reasoner.h"

#include <algorithm>
#include <limits>
#include <string>
#include <utility>
#include <vector>

#include "log_utils/map_macros.h"
#include "planner/path/reasoning/agent_intention/agent_intention_estimation/agent_intention_estimation.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/frame_analyzer_util.h"
#include "planner/utility/physics/motion_model_2d.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

// Confidence threshold for agent yielding to ego vehicle.
constexpr double kYieldProbabilityThresholdForAgent = 0.95;
// The consecutive yield count threshold for agent yielding to ego vehicle.
constexpr int kConsecutiveYieldCountThresholdForAgent = 5;
// The absolute speed emergency brake decelaration, it needs to be less than 10
// m/s^2 under the speed module.
constexpr double kMaxDecelarationForVehAndCycInMpss = 7.0;
constexpr double kMaxDecelarationForPedInMpss = 5.0;
// The minimum relative heading for crossing agent at large angles.
constexpr double kMinHeadingForCrossingAgentInRads = M_PI / 3.0;
// The maximun lateral deceration for crossing agent at large angles.
constexpr double kMaxLateralDecerationForCrossingAgent = 3.0;

// The max left distance from the oncoming left turn agnet to the reference path
// that we will create a repulsion.
constexpr double kMaxLeftLateralDistanceForOncomingLeftTurnAgentRepulsion = 5.0;
// The max right distance from the oncoming left turn agnet to the reference
// path that we will create a repulsion.
constexpr double kMaxRightLateralDistanceForOncomingLeftTurnAgentRepulsion =
    0.0;
// The repulsion will be more right when it's getting closer to the oncoming
// left turn agnet, and this is the max deviation value.
constexpr double KMaxRepulsionRightDeviationFromOncomingLeftTurnAgent = 1.0;
// The maximum speed we will add a repulsion for the oncoming left turn agnet.
constexpr double KMaxSpeedAddingRepulsionForOncomingLeftTurnAgent = 2.0;
// The max relative heading threshold which it is considered that the crossing
// agent will stably travel in the oncoming direction. (10 degree)
constexpr double kMaxRelativeHeadingForCertaintyOncomingInRad = M_PI / 18.0;
// The minimum relative box heading for potential crossing agent.
constexpr double kMinRelativeBoxHeadingForPotentialCrossingInRads = M_PI / 9.0;
// The maximum lateral bound distance from ego vehicle side in meters
constexpr double kMaxLateralBoundFromEgoSideInMeter = 1.0;
// Returns true if ego should trigger lane encroach nudge.
bool ShouldTriggerLaneEncroachNudgeForCross(
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    const std::vector<math::Range<int>>& agent_nudge_index_ranges,
    voy::perception::ObjectType agent_type, double agent_speed_mps,
    bool is_yielding_to_ego) {
  // only for vehicle not yielding to ego.
  if (agent_type != voy::perception::ObjectType::VEHICLE ||
      is_yielding_to_ego) {
    return false;
  }

  // Get effective nudge index ranges with hard blocking sequence part.
  std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetEffectiveNudgeIndexRanges(
          agent_nudge_index_ranges,
          trajectory_intention_data.blocking_sequences,
          /*blocking_state*/ pb::LateralBlockingState::HARD_BLOCKING);

  const bool is_agent_complete_blocking =
      (trajectory_intention_data.blocking_sequence_type ==
       pb::BlockingSequenceType::kCompleteBlocking) ||
      (!effective_nudge_index_ranges.empty() &&
       effective_nudge_index_ranges.front().start_pos > 10);
  // Limit the large-scale nudge behavior.
  DCHECK(!agent_nudge_index_ranges.empty());
  const bool need_es_behavior =
      (agent_nudge_index_ranges.front().start_pos < 10 &&
       agent_nudge_index_ranges.front().end_pos < 40) ||
      agent_speed_mps < 4.0;
  if (is_agent_complete_blocking && !need_es_behavior) {
    return false;
  }

  return true;
}

bool IsCutBehindNudgeBehavior(math::pb::Side center_line_side,
                              pb::SnapshotIntention::PassState nudge_side) {
  return (center_line_side == math::pb::kLeft &&
          nudge_side == pb::SnapshotIntention::PASS_LEFT) ||
         (center_line_side == math::pb::kRight &&
          nudge_side == pb::SnapshotIntention::PASS_RIGHT);
}

bool HasLateralCollisionRisk(const EgoInLaneParams& ego_param,
                             const AgentSnapshotInLaneParam& tracked_param,
                             bool is_left_of_ego, std::string* debug_str) {
  // The agent has greater ability to brake laterally when crossing at a
  // large angle.
  auto rss_config = path::RssConfig();
  if (std::fabs(tracked_param.relative_heading_rad) >
          kMinHeadingForCrossingAgentInRads &&
      std::fabs(tracked_param.relative_heading_rad) <
          M_PI - kMinHeadingForCrossingAgentInRads) {
    rss_config.lateral_brake_max = kMaxLateralDecerationForCrossingAgent;
    rss_config.lateral_brake_min = kMaxLateralDecerationForCrossingAgent;
  }
  const double safe_lateral_dist = path::CalculateSafeLateralDistance(
      ego_param.cross_track_speed_mps, tracked_param.cross_track_speed_mps,
      is_left_of_ego, rss_config);

  const double dist_ego_to_agent =
      is_left_of_ego ? tracked_param.full_body_start_lateral_distance_m -
                           (ego_param.relative_lateral_distance_to_line_center +
                            ego_param.width_m / 2)
                     : ego_param.relative_lateral_distance_to_line_center -
                           ego_param.width_m / 2 -
                           tracked_param.full_body_end_lateral_distance_m;
  absl::StrAppend(debug_str,
                  absl::StrCat("\nsafe_lateral_dist: ", safe_lateral_dist,
                               ", dist_ego_to_agent: ", dist_ego_to_agent));
  return dist_ego_to_agent < safe_lateral_dist;
}

bool HasLateralCollisionRisk(const EgoInLaneParams& ego_param,
                             const ObjectOccupancyParam& tracked_param,
                             bool is_left_of_ego, std::string* debug_str) {
  // The agent has greater ability to brake laterally when crossing at a
  // large angle.
  auto rss_config = path::RssConfig();
  if (std::fabs(tracked_param.relative_heading_rad) >
          kMinHeadingForCrossingAgentInRads &&
      std::fabs(tracked_param.relative_heading_rad) <
          M_PI - kMinHeadingForCrossingAgentInRads) {
    rss_config.lateral_brake_max = kMaxLateralDecerationForCrossingAgent;
    rss_config.lateral_brake_min = kMaxLateralDecerationForCrossingAgent;
  }
  const double safe_lateral_dist = path::CalculateSafeLateralDistance(
      ego_param.cross_track_speed_mps, tracked_param.cross_track_speed_mps,
      is_left_of_ego, rss_config);

  const double dist_ego_to_agent =
      is_left_of_ego ? tracked_param.full_body_start_lateral_distance_m -
                           (ego_param.relative_lateral_distance_to_line_center +
                            ego_param.width_m / 2)
                     : ego_param.relative_lateral_distance_to_line_center -
                           ego_param.width_m / 2 -
                           tracked_param.full_body_end_lateral_distance_m;
  absl::StrAppend(debug_str,
                  absl::StrCat("\nsafe_lateral_dist: ", safe_lateral_dist,
                               ", dist_ego_to_agent: ", dist_ego_to_agent));
  return dist_ego_to_agent < safe_lateral_dist;
}

bool IsPotentialYeildIntentionCrossingAgent(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const std::optional<planner::pb::YieldIntentionData>& yield_intention,
    double relative_lane_heading) {
  bool potential_yield_ego = !yield_intention.has_value() ||
                             (yield_intention.has_value() &&
                              (yield_intention->yield_probability() > 0.5 ||
                               yield_intention->consecutive_yield_count() > 2));
  auto box_relative_heading =
      reasoning_info.is_motion_heading_opposite_to_box_heading()
          ? math::NormalizeMinusPiToPi(relative_lane_heading + M_PI)
          : relative_lane_heading;
  bool is_agent_box_heading_toward_reference_line =
      std::fabs(box_relative_heading) >
          kMinRelativeBoxHeadingForPotentialCrossingInRads &&
      std::fabs(box_relative_heading) <
          M_PI - kMinRelativeBoxHeadingForPotentialCrossingInRads;
  return potential_yield_ego &&
         (reasoning_info.is_agent_toward_reference_line() ||
          is_agent_box_heading_toward_reference_line);
}

std::optional<RepulsionMetaData> ComputeRepulsionMetaDataForCrossingTrajectory(
    const EgoInLaneParams& ego_param,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    int trajectory_id,
    RepulsionMetaData::RepulsionDirection repulsion_direction, bool is_risky,
    bool is_uncrossable, double total_crossing_likelihood,
    double repulsion_required_lat_gap, pb::AgentReasonerId reasoner_id,
    const pb::ObstacleSTBufferConfig& st_buffer, std::string& debug_str) {
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  const AgentTrajectoryInLaneStates& predicted_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             trajectory_id);
  const bool is_risky_cross_boundary = is_risky || is_uncrossable;
  // For dynamic agent, using the prediction trajectory.
  if (!(blocking_state_meta_data.reasoning_info.is_crossing() ||
        blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing())) {
    absl::StrAppend(&debug_str,
                    absl::StrCat("Traj ", trajectory_id, " non crossing;"));
    return std::nullopt;
  }

  // Only consider the snapshots with spatiotemporal overlap.
  if (blocking_state_meta_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "kNonBlocking");
    return std::nullopt;
  }

  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  const auto& predicted_states = predicted_trajectory.predicted_states;
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(
        state.inlane_state.inlane_param.pose.center_2d());
  }

  for (const pb::BlockingSequence& blocking_sequence :
       blocking_state_meta_data.blocking_sequences) {
    DCHECK_NE(blocking_sequence.blocking_state(),
              pb::LateralBlockingState::NON_BLOCKING);
    for (size_t i =
             static_cast<size_t>(blocking_sequence.range().start_index());
         i < static_cast<size_t>(blocking_sequence.range().end_index()); ++i) {
      DCHECK_LT(i, predicted_states.size());
      speed_profile.emplace_back(
          math::Ms2Sec(predicted_states[i].timestamp),
          predicted_states[i].odom_m,
          predicted_states[i].inlane_state.inlane_param.along_track_speed_mps,
          /*a_in=*/0.0, /*j_in=*/0.0);
    }
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return std::nullopt;
  }
  // Statistically analyze the driving performance considering non-primary
  // crossing trajectories.
  if (trajectory_id != agent_inlane_state.primary_trajectory_id) {
    rt_event::PostRtEvent<
        rt_event::planner::RepulsionCrossingNonPrimaryTrajectory>();
  }

  return RepulsionMetaData(
      repulsion_direction, tracked_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile, tracked_param.pose.heading(),
      /*add_only_overtaking=*/true,
      /*ignore_if_nudge_failed_in=*/true,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/
      is_risky ? path::BoundaryStrength::kWeak
               : path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/
      predicted_trajectory.trajectory_metadata.likelihood /
          total_crossing_likelihood,
      /*repulsion_required_lat_gap=*/
      repulsion_required_lat_gap, reasoner_id,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/trajectory_id,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft
          ? std::min(0.5 * tracked_param.min_lane_width -
                         (is_risky_cross_boundary ? ego_param.width_m
                                                  : 0.5 * ego_param.width_m),
                     ego_param.relative_lateral_distance_to_line_center +
                         0.5 * ego_param.width_m +
                         kMaxLateralBoundFromEgoSideInMeter)
          : std::max(-0.5 * tracked_param.min_lane_width +
                         (is_risky_cross_boundary ? ego_param.width_m
                                                  : 0.5 * ego_param.width_m),
                     ego_param.relative_lateral_distance_to_line_center -
                         0.5 * ego_param.width_m -
                         kMaxLateralBoundFromEgoSideInMeter),
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      is_risky_cross_boundary
          ? RepulsionMetaData::ExtendBoundaryLevel::kNA
          : RepulsionMetaData::ExtendBoundaryLevel::kVirtualHard,
      /*clearance_in=*/
      is_risky_cross_boundary ? std::nullopt
                              : std::make_optional(ego_param.width_m),
      /*max_extend_boundary_distance_in=*/
      is_risky_cross_boundary ? std::nullopt
                              : std::make_optional(0.5 * ego_param.width_m),
      /*st_buffer_in=*/st_buffer);
}

std::optional<RepulsionMetaData> ComputeRepulsionMetaDataForCrossingTrajectory(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    int trajectory_id,
    RepulsionMetaData::RepulsionDirection repulsion_direction, bool is_risky,
    bool is_uncrossable, double total_crossing_likelihood,
    double repulsion_required_lat_gap, pb::AgentReasonerId reasoner_id,
    const pb::ObstacleSTBufferConfig& st_buffer, std::string& debug_str) {
  const auto& tracked_param = object_occupancy_state.current_snapshot_info();
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  const ObjectTrajectoryStates& object_trajectory_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          trajectory_id);
  const bool is_risky_cross_boundary = is_risky || is_uncrossable;
  // For dynamic agent, using the prediction trajectory.
  if (!(blocking_state_meta_data.reasoning_info.is_crossing() ||
        blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing())) {
    absl::StrAppend(&debug_str,
                    absl::StrCat("Traj ", trajectory_id, " non crossing;"));
    return std::nullopt;
  }

  // Only consider the snapshots with spatiotemporal overlap.
  if (blocking_state_meta_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "kNonBlocking");
    return std::nullopt;
  }

  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  const auto& predicted_states = object_trajectory_states.predicted_states();
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(state.pose().center_2d());
  }

  for (const pb::BlockingSequence& blocking_sequence :
       blocking_state_meta_data.blocking_sequences) {
    DCHECK_NE(blocking_sequence.blocking_state(),
              pb::LateralBlockingState::NON_BLOCKING);
    for (size_t i =
             static_cast<size_t>(blocking_sequence.range().start_index());
         i < static_cast<size_t>(blocking_sequence.range().end_index()); ++i) {
      DCHECK_LT(i, predicted_states.size());
      speed_profile.emplace_back(
          math::Ms2Sec(predicted_states[i].timestamp()),
          predicted_states[i].odom_m().value_or(0.0),
          predicted_states[i].object_occupancy_param().along_track_speed_mps,
          /*a_in=*/0.0, /*j_in=*/0.0);
    }
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return std::nullopt;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return std::nullopt;
  }
  // Statistically analyze the driving performance considering non-primary
  // crossing trajectories.
  if (trajectory_id != object_occupancy_state.primary_trajectory_id()) {
    rt_event::PostRtEvent<
        rt_event::planner::RepulsionCrossingNonPrimaryTrajectory>();
  }
  const double min_corridor_width_m =
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .min_corridor_width_m;

  return RepulsionMetaData(
      repulsion_direction, tracked_param.pose().contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile, tracked_param.pose().heading(),
      /*add_only_overtaking=*/true,
      /*ignore_if_nudge_failed_in=*/true,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/
      is_risky ? path::BoundaryStrength::kWeak
               : path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/
      object_trajectory_states.Likelihood() / total_crossing_likelihood,
      /*repulsion_required_lat_gap=*/
      repulsion_required_lat_gap, reasoner_id,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      /*trajectory_id_in=*/trajectory_id,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft
          ? std::min(0.5 * min_corridor_width_m -
                         (is_risky_cross_boundary ? ego_param.width_m
                                                  : 0.5 * ego_param.width_m),
                     ego_param.relative_lateral_distance_to_line_center +
                         0.5 * ego_param.width_m +
                         kMaxLateralBoundFromEgoSideInMeter)
          : std::max(-0.5 * min_corridor_width_m +
                         (is_risky_cross_boundary ? ego_param.width_m
                                                  : 0.5 * ego_param.width_m),
                     ego_param.relative_lateral_distance_to_line_center -
                         0.5 * ego_param.width_m -
                         kMaxLateralBoundFromEgoSideInMeter),
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      is_risky_cross_boundary
          ? RepulsionMetaData::ExtendBoundaryLevel::kNA
          : RepulsionMetaData::ExtendBoundaryLevel::kVirtualHard,
      /*clearance_in=*/
      is_risky_cross_boundary ? std::nullopt
                              : std::make_optional(ego_param.width_m),
      /*max_extend_boundary_distance_in=*/
      is_risky_cross_boundary ? std::nullopt
                              : std::make_optional(0.5 * ego_param.width_m),
      /*st_buffer_in=*/st_buffer);
}

bool IsLeftTurnOncommingVehicleYieldingToEgo(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const std::optional<pb::YieldIntentionData>& yield_intention_data) {
  const bool is_agent_yielding = yield_intention_data.has_value() &&
                                 yield_intention_data->yield_probability() >
                                     kYieldProbabilityThresholdForAgent;

  const bool is_left_turn_vehicle =
      reasoning_info.is_agent_in_junction() &&
      reasoning_info.is_left_turn_oncoming_vehicle();

  return FLAGS_planner_enable_soften_all_type_of_yielding_crossing
             ? is_agent_yielding
             : is_agent_yielding && is_left_turn_vehicle;
}

};  // namespace

CrossAgentPathReasoner::CrossAgentPathReasoner(
    const EgoContextInfo& ego_context_info)
    : AbstractReasoner(ego_context_info, pb::AgentReasonerId::CROSS_AGENT) {}

pb::ObstacleSTBufferConfig CrossAgentPathReasoner::GetSTBufferConfig(
    const pb::TrajectoryReasoningInfo& reasoning_info) const {
  pb::ObstacleSTBufferConfig st_buffer = st_buffer_config_;
  if (reasoning_info.is_oncoming_ahead_of_ego_front_bumper()) {
    st_buffer.set_spatial_bottom_buffer_m(1.0);
    st_buffer.set_spatial_top_buffer_m(0.0);
    st_buffer.set_temporal_left_buffer_s(0.0);
    st_buffer.set_temporal_right_buffer_s(0.3);
  }
  return st_buffer;
}

bool CrossAgentPathReasoner::SoftenBlockingSequencesForLeftTurnYieldingAgent(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  const bool is_left_turn_agent_yielding =
      IsLeftTurnOncommingVehicleYieldingToEgo(
          reasoning_info, trajectory_intention_data.yield_intention_data);
  if (!is_left_turn_agent_yielding) {
    return false;
  }

  trajectory_intention_data.is_softened_blocking_sequence = true;
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kPartialBlocking;
  pb::BlockingSequence blocking_sequence = GetBlockingSequence(
      /*start_index=*/
      blocking_sequences.front().range().start_index(),
      /*end_index=*/
      blocking_sequences.back().range().end_index(),
      /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
  trajectory_intention_data.blocking_sequences = {blocking_sequence};
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() = GetSTBufferConfig(reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PreReasonForSoftBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForSoftBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PreReasonForSoftBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  AbstractReasoner::PreReasonForSoftBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}
bool CrossAgentPathReasoner::PreReasonForHardBlockingSequences(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  // For left turn agent which yield to ego, soften hard blocking sequences to
  // SOFT_BLOCKING.
  if (SoftenBlockingSequencesForLeftTurnYieldingAgent(
          blocking_state_data.reasoning_info, blocking_sequences,
          trajectory_intention_data)) {
    return true;
  }

  AbstractReasoner::PreReasonForHardBlockingSequences(
      agent_inlane_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PreReasonForHardBlockingSequences(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  // For left turn agent which yield to ego, soften hard blocking sequences to
  // SOFT_BLOCKING.
  if (SoftenBlockingSequencesForLeftTurnYieldingAgent(
          blocking_state_data.reasoning_info, blocking_sequences,
          trajectory_intention_data)) {
    return true;
  }

  AbstractReasoner::PreReasonForHardBlockingSequences(
      object_occupancy_state, blocking_state_data, trajectory_intention_data);
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const AgentInLaneStates& /* agent_inlane_state */,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  // For left turn agent which yield to ego, soften hard blocking sequences to
  // SOFT_BLOCKING.
  if (SoftenBlockingSequencesForLeftTurnYieldingAgent(
          blocking_state_data.reasoning_info, blocking_sequences,
          trajectory_intention_data)) {
    return true;
  }

  if (blocking_sequences.front().blocking_state() ==
      pb::LateralBlockingState::SOFT_BLOCKING) {
    // We add the front part of soft blocking states for nudge, and add the back
    // part of hard blocking for punish nudge in the future.
    const int start_soft_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int end_soft_index = GetEndIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int start_hard_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    const int end_index = blocking_sequences.back().range().end_index();
    pb::BlockingSequence soft_blocking_sequence = GetBlockingSequence(
        start_soft_index, std::min(end_soft_index, start_hard_index - 1),
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    pb::BlockingSequence hard_blocking_sequence = GetBlockingSequence(
        start_hard_index, end_index,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    trajectory_intention_data.blocking_sequence_type =
        pb::BlockingSequenceType::kMixedBlocking;
    trajectory_intention_data.blocking_sequences =
        std::vector<pb::BlockingSequence>{soft_blocking_sequence,
                                          hard_blocking_sequence};
    for (auto& sequence : trajectory_intention_data.blocking_sequences) {
      *sequence.mutable_buffer_config() =
          GetSTBufferConfig(blocking_state_data.reasoning_info);
    }
    return true;
  }

  pb::BlockingSequence blocking_sequence = GetBlockingSequence(
      blocking_sequences.front().range().start_index(),
      blocking_sequences.back().range().end_index(),
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PreReasonForMixedBlockingSequences(
    const ObjectOccupancyState& /* object_occupancy_state */,
    const LateralBlockingStateMetaData& blocking_state_data,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  const std::vector<pb::BlockingSequence>& blocking_sequences =
      blocking_state_data.blocking_sequences;
  // For left turn agent which yield to ego, soften hard blocking sequences to
  // SOFT_BLOCKING.
  if (SoftenBlockingSequencesForLeftTurnYieldingAgent(
          blocking_state_data.reasoning_info, blocking_sequences,
          trajectory_intention_data)) {
    return true;
  }
  if (blocking_sequences.front().blocking_state() ==
      pb::LateralBlockingState::SOFT_BLOCKING) {
    // We add the front part of soft blocking states for nudge, and add the back
    // part of hard blocking for punish nudge in the future.
    const int start_soft_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int end_soft_index = GetEndIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    const int start_hard_index = GetStartIndexForState(
        blocking_sequences,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    const int end_index = blocking_sequences.back().range().end_index();
    pb::BlockingSequence soft_blocking_sequence = GetBlockingSequence(
        start_soft_index, std::min(end_soft_index, start_hard_index - 1),
        /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
    pb::BlockingSequence hard_blocking_sequence = GetBlockingSequence(
        start_hard_index, end_index,
        /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
    trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
    trajectory_intention_data.blocking_sequence_type =
        pb::BlockingSequenceType::kMixedBlocking;
    trajectory_intention_data.blocking_sequences =
        std::vector<pb::BlockingSequence>{soft_blocking_sequence,
                                          hard_blocking_sequence};
    for (auto& sequence : trajectory_intention_data.blocking_sequences) {
      *sequence.mutable_buffer_config() =
          GetSTBufferConfig(blocking_state_data.reasoning_info);
    }
    return true;
  }

  pb::BlockingSequence blocking_sequence = GetBlockingSequence(
      blocking_sequences.front().range().start_index(),
      blocking_sequences.back().range().end_index(),
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);
  trajectory_intention_data.blocking_sequence_type =
      pb::BlockingSequenceType::kCompleteBlocking;
  trajectory_intention_data.blocking_sequences =
      std::vector<pb::BlockingSequence>{blocking_sequence};
  trajectory_intention_data.intention_type = pb::TrajectoryState::MIXED;
  for (auto& sequence : trajectory_intention_data.blocking_sequences) {
    *sequence.mutable_buffer_config() =
        GetSTBufferConfig(blocking_state_data.reasoning_info);
  }
  return true;
}

bool CrossAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          tracked_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego(),
          tracked_param.left_inlane_clearance_m,
          tracked_param.right_inlane_clearance_m);
  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      tracked_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  const bool is_yielding_agent =
      trajectory_intention_data.yield_intention_data.has_value() &&
      (trajectory_intention_data.yield_intention_data->yield_probability() >
           kYieldProbabilityThresholdForAgent ||
       trajectory_intention_data.yield_intention_data
               ->consecutive_yield_count() >
           kConsecutiveYieldCountThresholdForAgent);
  // We always hope to give a nudge decision for pedestrain to ensure basic
  // safety. Here using a lower deceleration allow for a nudge decision for
  // ped at a greater distance.
  const double eb_deceleration_mpss =
      agent_inlane_state.agent_metadata.agent_type ==
              voy::perception::ObjectType::PED
          ? kMaxDecelarationForPedInMpss
          : kMaxDecelarationForVehAndCycInMpss;
  const auto [can_longitudinal_eb, is_agent_cut_front] =
      CanEmergencyBrakeStopBeforeAgent(
          ego_param_, agent_inlane_state, blocking_state_data.reasoning_info,
          intention_result.required_lat_gap.critical_required_lateral_gap,
          eb_deceleration_mpss, &(intention_result.debug_str));
  const bool is_oncoming_ahead_ego =
      blocking_state_data.reasoning_info
          .is_oncoming_ahead_of_ego_front_bumper();
  // The uncertainty of cyclists crossing in the oncoming direction is
  // relatively high. Especially, cyclists moving at high speeds or with a
  // large relative heading toward reference line tend to cut front of the ego
  // vehicle. Therefore, we only intend to stably nudge those cyclists who are
  // moving slowly and show an intention to yield. When the longitudinal
  // distance is short and lateral avoidance is necessary,
  // `can_longitudinal_eb` can ensure a nudge decision.
  const bool is_non_cyclist = agent_inlane_state.agent_metadata.agent_type !=
                              voy::perception::ObjectType::CYCLIST;
  const bool is_certainly_oncoming_cyclist =
      agent_inlane_state.agent_metadata.agent_type ==
          voy::perception::ObjectType::CYCLIST &&
      std::fabs(tracked_param.relative_heading_rad) >
          M_PI - kMaxRelativeHeadingForCertaintyOncomingInRad;
  const bool is_certainty_oncoming_ahead_ego =
      is_oncoming_ahead_ego &&
      (is_non_cyclist || is_certainly_oncoming_cyclist);
  /********************************************************************
   * This diagram shows several common types of crossing trajectories.
     ___          ^
    |   | agent_3 |  ^
    |___|         |   \   <--- agent_2
      \           |    \        ____
        \---->     |  <--*------｜___| agent_1
                  _|_        ____
                |   | <----｜___| agent_4
                |___|
                  ego

  ********************************************************************/
  // Considers the agents cutting in front of ego vehicle, include agent_1,
  // agent_2 and agent_3.
  bool agent_required_active_nudge =
      (is_yielding_agent || is_certainty_oncoming_ahead_ego ||
       !can_longitudinal_eb) &&
      is_agent_cut_front;

  // Considers the agent that is forced to stop by the ego vehicle and has
  // collision risk laterally, include agent_4.
  bool is_laterally_collision = false;
  if (!is_agent_cut_front) {
    const bool is_left_of_ego =
        tracked_param.center_line_side == math::pb::kLeft;
    is_laterally_collision = HasLateralCollisionRisk(
        ego_param_, tracked_param, is_left_of_ego, &intention_result.debug_str);
    agent_required_active_nudge =
        agent_required_active_nudge || is_laterally_collision;
  }

  const bool is_ego_cut_behind = IsCutBehindNudgeBehavior(
      tracked_param.center_line_side, intention_result.lateral_decision);
  const std::string yield_prob_str =
      trajectory_intention_data.yield_intention_data.has_value()
          ? std::to_string(trajectory_intention_data.yield_intention_data
                               ->yield_probability())
          : "no yield prob; ";
  absl::StrAppend(
      &intention_result.debug_str,
      absl::StrCat(
          "\naccel: ",
          absl::StrFormat("%.4f", tracked_param.acceleration_mpss *
                                      std::sin(std::fabs(
                                          tracked_param.relative_heading_rad))),
          ", speed: ", absl::StrFormat("%.4f", tracked_param.speed_mps),
          ", yield_prob: ", yield_prob_str, ", is_yielding_agent: ",
          is_yielding_agent, ", can_longitudinal_eb: ", can_longitudinal_eb,
          ", is_agent_cut_front: ", is_agent_cut_front,
          ", is_laterally_collision: ", is_laterally_collision,
          ", is_ego_cut_behind: ", is_ego_cut_behind,
          ", is_certainly_oncoming_cyclist: ", is_certainly_oncoming_cyclist,
          "; "));

  // If the agent does not yield to the ego and the ego can brake in time, or
  // the agent is yield to ego but laterally far away from ego,
  // then do not provide a nudge decision in the same direction as the agent's
  // driving direction.
  if (!agent_required_active_nudge && !is_ego_cut_behind) {
    rt_event::PostRtEvent<
        rt_event::planner::IgnoreCrossingAgentNoYieldIntentionWhenEgoCanEB>();
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "\nNon required active nudge agent; ");
    return true;
  }

  // For yielding_agent, is_ego_cut_behind, can_not_eb or oncoming, we don't
  // need change the st planner's dicision for
  // kNonBlocking/kPartialBlocking/MixedBlocking.
  intention_result.is_overtaken =
      (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
      (intention_result.longitudinal_decision ==
       speed::pb::SpeedDecision::PASS);

  absl::StrAppend(&intention_result.debug_str,
                  absl::StrCat("\nlongitudinal_decision: ",
                               speed::pb::SpeedDecision_Name(
                                   intention_result.longitudinal_decision),
                               "; "));

  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      trajectory_intention_data.blocking_sequence_type !=
          pb::BlockingSequenceType::kCompleteBlocking &&
      !trajectory_intention_data.is_softened_blocking_sequence) {
    absl::StrAppend(&intention_result.debug_str, "\nNormal st nudge decision");
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }

  // Lane encroach nudge for crossing agent.
  if (ShouldTriggerLaneEncroachNudgeBase(
          ego_param_, traffic_rule_reasoning_info_,
          blocking_state_data.reasoning_info, intention_result.lateral_decision,
          tracked_pass_decision, behavior_type_) &&
      ShouldTriggerLaneEncroachNudgeForCross(
          trajectory_intention_data, intention_result.nudge_index_ranges,
          agent_inlane_state.agent_metadata.agent_type, tracked_param.speed_mps,
          is_yielding_agent)) {
    const bool is_missing_nudge_decision =
        (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
    if (is_missing_nudge_decision) {
      rt_event::PostRtEvent<
          rt_event::planner::TriggerLaneEncroachNudgeForCrossingAgents>();
    }

    if (FLAGS_planning_enable_lane_encroach_nudge) {
      if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE) {
        intention_result.lateral_decision = tracked_pass_decision;
      }
      DCHECK(intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE);
      intention_result.is_overtaken = false;
      intention_result.required_lat_gap.critical_required_lateral_gap =
          math::constants::kEpsilon;
      intention_result.is_lane_encroach_intent_agent =
          is_missing_nudge_decision;
      absl::StrAppend(&intention_result.debug_str,
                      "\nemergency_swerve for crossing; ");
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      if (ShouldCrossLane(blocking_state_data)) {
        intention_result.solid_line_lane_boundary_decision =
            LaneBoundaryDecision::kCrossLane;
      }
      return true;
    }
  }

  // For situations where the ST planner has no solution, such as
  // CompleteBlocking with some necessary nudgable agents(oncoming_agents,
  // yielding_agents, close_agents, ego cannot emergency brake), we provide a
  // nudge decision when the nudge side is safe.
  const pb::SnapshotIntention::PassState pass_decision =
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE
          ? tracked_pass_decision
          : intention_result.lateral_decision;
  const bool is_necessary_nudgable_agent =
      agent_required_active_nudge ||
      ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                   blocking_state_data.reasoning_info);
  const bool is_necessary_nudgable_scenario = IsSafeNecessaryCrossLaneNudge(
      intention_result.nudge_index_ranges, pass_decision,
      traffic_rule_reasoning_info_);
  const auto& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  DCHECK(!predicted_states.empty());
  if (is_necessary_nudgable_agent && is_necessary_nudgable_scenario) {
    rt_event::PostRtEvent<
        rt_event::planner::EncroachLaneNudgeNecessaryAgentWhenSideSafe>();
    intention_result.lateral_decision = pass_decision;
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
    absl::StrAppend(
        &intention_result.debug_str, "\nNudge across lane to avoid danger; ",
        absl::StrCat(" origin range: ",
                     intention_result.nudge_index_ranges.front().start_pos,
                     ", ", intention_result.nudge_index_ranges.front().end_pos),
        "; ");
    // Trims the nudge index range to avoid harsh swerve.
    AdjustNudgeRangeBasedOnKinematics(
        ego_param_, tracked_param, predicted_states, nudge_motion_checker(),
        is_oncoming_ahead_ego, agent_inlane_state.agent_metadata.agent_type,
        intention_result);
    // For sequences that are actually hard-blocking but were softened during
    // prereasoning:
    // The ST planner may output a yielding ST rollout due to the presence of
    // other agents' ST regions. In this scenario, the nudge range shifts
    // backward, leading to a more aggressive nudge decision, which conflicts
    // with the intent of yielding. Therefore, we restrict the ego vehicle to
    // yield while remaining within the lane.
    const bool is_softened_blocking_without_overtaking =
        trajectory_intention_data.is_softened_blocking_sequence &&
        !intention_result.is_overtaken;

    // In low-speed interaction situation, for agent not beside of ego, the
    // uncertainty of the single-frame predicted trajectory is relatively
    // high. Therefore, the ego should be restricted from nudging out of the
    // lane, and instead, it should only nudge at its current position.
    if ((ego_param_.speed_mps < 2.0 ||
         is_softened_blocking_without_overtaking) &&
        !blocking_state_data.reasoning_info.is_beside_ego()) {
      GetRecommendedAgentIntentionResult(
          agent_inlane_state, pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/false, is_ego_cut_behind,
          intention_result);
      absl::StrAppend(
          &intention_result.debug_str,
          "\nInlane nudge due to ego slow moving or ego yield to agent; ");
    }
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(agent_inlane_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }

  // When it is unsafe on one side of the lane, only nudge in the lane.
  if (is_necessary_nudgable_agent &&
      pass_decision != pb::SnapshotIntention::IGNORE &&
      GetRecommendedAgentIntentionResult(
          agent_inlane_state, tracked_pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/false, is_ego_cut_behind,
          intention_result)) {
    rt_event::PostRtEvent<
        rt_event::planner::InLaneNudgeNecessaryAgentWhenSideNotSafe>();
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(agent_inlane_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      // Although the nudge behavior is constrained not to cross the lane,
      // there may still be a harsh swerve when the distance from the vehicle
      // is close. Trims the nudge index range to avoid harsh swerve.
      AdjustNudgeRangeBasedOnKinematics(
          ego_param_, tracked_param, predicted_states, nudge_motion_checker(),
          is_oncoming_ahead_ego, agent_inlane_state.agent_metadata.agent_type,
          intention_result);
      AddMultipleNominalNudgeConstraintOnPrediction(
          agent_inlane_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    absl::StrAppend(&intention_result.debug_str,
                    "\nyield agent but side lane not safe do inlane nudge; ");
    return true;
  }

  // Ignore the agent in two cases:
  // 1. The agent is not necessary nudgable, the ego vehicle cuts behind it,
  // and the BlockingSequenceType is kCompleteBlocking.
  // 2. The agent is necessary nudgable, but the pass decision is IGNORE.
  absl::StrAppend(&intention_result.debug_str,
                  "\nego cut behind but CompleteBlocking || yield agent but "
                  "pass decision is ignore;");
  intention_result.is_overtaken = false;
  intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
  return true;
}

bool CrossAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  intention_result.reasoner_id = reasoner_id_;
  const auto& tracked_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          tracked_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego(),
          tracked_param.left_boundary_clearance_m,
          tracked_param.right_boundary_clearance_m);
  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      tracked_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  const bool is_yielding_agent =
      trajectory_intention_data.yield_intention_data.has_value() &&
      (trajectory_intention_data.yield_intention_data->yield_probability() >
           kYieldProbabilityThresholdForAgent ||
       trajectory_intention_data.yield_intention_data
               ->consecutive_yield_count() >
           kConsecutiveYieldCountThresholdForAgent);
  // We always hope to give a nudge decision for pedestrain to ensure basic
  // safety. Here using a lower deceleration allow for a nudge decision for
  // ped at a greater distance.
  const double eb_deceleration_mpss =
      object_occupancy_state.object_type() == voy::perception::ObjectType::PED
          ? kMaxDecelarationForPedInMpss
          : kMaxDecelarationForVehAndCycInMpss;
  const auto [can_longitudinal_eb, is_agent_cut_front] =
      CanEmergencyBrakeStopBeforeAgent(
          ego_param_, object_occupancy_state,
          blocking_state_data.reasoning_info,
          intention_result.required_lat_gap.critical_required_lateral_gap,
          eb_deceleration_mpss, &(intention_result.debug_str));
  const bool is_oncoming_ahead_ego =
      blocking_state_data.reasoning_info
          .is_oncoming_ahead_of_ego_front_bumper();
  // The uncertainty of cyclists crossing in the oncoming direction is
  // relatively high. Especially, cyclists moving at high speeds or with a
  // large relative heading toward reference line tend to cut front of the ego
  // vehicle. Therefore, we only intend to stably nudge those cyclists who are
  // moving slowly and show an intention to yield. When the longitudinal
  // distance is short and lateral avoidance is necessary,
  // `can_longitudinal_eb` can ensure a nudge decision.
  const bool is_non_cyclist = object_occupancy_state.object_type() !=
                              voy::perception::ObjectType::CYCLIST;
  const bool is_certainly_oncoming_cyclist =
      object_occupancy_state.object_type() ==
          voy::perception::ObjectType::CYCLIST &&
      std::fabs(tracked_param.relative_heading_rad) >
          M_PI - kMaxRelativeHeadingForCertaintyOncomingInRad;
  const bool is_certainty_oncoming_ahead_ego =
      is_oncoming_ahead_ego &&
      (is_non_cyclist || is_certainly_oncoming_cyclist);
  /********************************************************************
   * This diagram shows several common types of crossing trajectories.
     ___          ^
    |   | agent_3 |  ^
    |___|         |   \   <--- agent_2
      \           |    \        ____
       \---->     |  <--*------｜___| agent_1
                 _|_        ____
                |   | <----｜___| agent_4
                |___|
                 ego

  ********************************************************************/
  // Considers the agents cutting in front of ego vehicle, include agent_1,
  // agent_2 and agent_3.
  bool agent_required_active_nudge =
      (is_yielding_agent || is_certainty_oncoming_ahead_ego ||
       !can_longitudinal_eb) &&
      is_agent_cut_front;

  // Considers the agent that is forced to stop by the ego vehicle and has
  // collision risk laterally, include agent_4.
  bool is_laterally_collision = false;
  if (!is_agent_cut_front) {
    const bool is_left_of_ego =
        tracked_param.center_line_side == math::pb::kLeft;
    is_laterally_collision = HasLateralCollisionRisk(
        ego_param_, tracked_param, is_left_of_ego, &intention_result.debug_str);
    agent_required_active_nudge =
        agent_required_active_nudge || is_laterally_collision;
  }

  const bool is_ego_cut_behind = IsCutBehindNudgeBehavior(
      tracked_param.center_line_side, intention_result.lateral_decision);
  const std::string yield_prob_str =
      trajectory_intention_data.yield_intention_data.has_value()
          ? std::to_string(trajectory_intention_data.yield_intention_data
                               ->yield_probability())
          : "no yield prob; ";
  absl::StrAppend(
      &intention_result.debug_str,
      absl::StrCat(
          "\naccel: ",
          absl::StrFormat("%.4f", tracked_param.acceleration_mpss *
                                      std::sin(std::fabs(
                                          tracked_param.relative_heading_rad))),
          ", speed: ", absl::StrFormat("%.4f", tracked_param.speed_mps),
          ", yield_prob: ", yield_prob_str, ", is_yielding_agent: ",
          is_yielding_agent, ", can_longitudinal_eb: ", can_longitudinal_eb,
          ", is_agent_cut_front: ", is_agent_cut_front,
          ", is_laterally_collision: ", is_laterally_collision,
          ", is_ego_cut_behind: ", is_ego_cut_behind,
          ", is_certainly_oncoming_cyclist: ", is_certainly_oncoming_cyclist,
          "; "));
  // If the agent does not yield to the ego and the ego can brake in time,
  // then do not provide a nudge decision in the same direction as the agent's
  // driving direction.
  if (!agent_required_active_nudge && !is_ego_cut_behind) {
    rt_event::PostRtEvent<
        rt_event::planner::IgnoreCrossingAgentNoYieldIntentionWhenEgoCanEB>();
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
    absl::StrAppend(&intention_result.debug_str,
                    "\nNon required active nudge agent; ");
    return true;
  }

  // For yielding_agent, is_ego_cut_behind, can_not_eb or oncoming, we don't
  // need change the st planner's dicision for
  // kNonBlocking/kPartialBlocking/MixedBlocking.
  intention_result.is_overtaken =
      (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
      (intention_result.longitudinal_decision ==
       speed::pb::SpeedDecision::PASS);
  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      trajectory_intention_data.blocking_sequence_type !=
          pb::BlockingSequenceType::kCompleteBlocking) {
    absl::StrAppend(&intention_result.debug_str, "\nNormal nudge decision");
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }

  // Lane encroach nudge for crossing agent.
  if (ShouldTriggerLaneEncroachNudgeBase(
          ego_param_, traffic_rule_reasoning_info_,
          blocking_state_data.reasoning_info, intention_result.lateral_decision,
          tracked_pass_decision, behavior_type_) &&
      ShouldTriggerLaneEncroachNudgeForCross(
          trajectory_intention_data, intention_result.nudge_index_ranges,
          object_occupancy_state.object_type(), tracked_param.speed_mps,
          is_yielding_agent)) {
    const bool is_missing_nudge_decision =
        (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
    if (is_missing_nudge_decision) {
      rt_event::PostRtEvent<
          rt_event::planner::TriggerLaneEncroachNudgeForCrossingAgents>();
    }

    if (FLAGS_planning_enable_lane_encroach_nudge) {
      if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE) {
        intention_result.lateral_decision = tracked_pass_decision;
      }
      DCHECK(intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE);
      intention_result.is_overtaken = false;
      intention_result.required_lat_gap.critical_required_lateral_gap =
          math::constants::kEpsilon;
      intention_result.is_lane_encroach_intent_agent =
          is_missing_nudge_decision;
      absl::StrAppend(&intention_result.debug_str,
                      "\nemergency_swerve for crossing; ");
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
      if (ShouldCrossLane(blocking_state_data)) {
        intention_result.solid_line_lane_boundary_decision =
            LaneBoundaryDecision::kCrossLane;
      }
      return true;
    }
  }

  // For situations where the ST planner has no solution, such as
  // CompleteBlocking with some necessary nudgable agents(oncoming_agents,
  // yielding_agents, close_agents, ego cannot emergency brake), we provide a
  // nudge decision when the nudge side is safe.
  const pb::SnapshotIntention::PassState pass_decision =
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE
          ? tracked_pass_decision
          : intention_result.lateral_decision;
  const bool is_necessary_nudgable_agent =
      agent_required_active_nudge ||
      ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                   blocking_state_data.reasoning_info);
  const bool is_necessary_nudgable_scenario = IsSafeNecessaryCrossLaneNudge(
      intention_result.nudge_index_ranges, tracked_pass_decision,
      traffic_rule_reasoning_info_);
  const auto& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  DCHECK(!predicted_states.empty());
  if (is_necessary_nudgable_agent && is_necessary_nudgable_scenario) {
    rt_event::PostRtEvent<
        rt_event::planner::EncroachLaneNudgeNecessaryAgentWhenSideSafe>();
    intention_result.lateral_decision = pass_decision;
    intention_result.is_overtaken =
        (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE) &&
        (intention_result.longitudinal_decision ==
         speed::pb::SpeedDecision::PASS);
    absl::StrAppend(
        &intention_result.debug_str, "\nNudge across lane to avoid danger; ",
        absl::StrCat(" origin range: ",
                     intention_result.nudge_index_ranges.front().start_pos,
                     ", ",
                     intention_result.nudge_index_ranges.front().end_pos));
    // Trims the nudge index range to avoid harsh swerve.
    AdjustNudgeRangeBasedOnKinematics(
        ego_param_, tracked_param, predicted_states, nudge_motion_checker(),
        is_oncoming_ahead_ego, object_occupancy_state.object_type(),
        intention_result);
    // For sequences that are actually hard-blocking but were softened during
    // prereasoning:
    // The ST planner may output a yielding ST rollout due to the presence of
    // other agents' ST regions. In this scenario, the nudge range shifts
    // backward, leading to a more aggressive nudge decision, which conflicts
    // with the intent of yielding. Therefore, we restrict the ego vehicle to
    // yield while remaining within the lane.
    const bool is_softened_blocking_without_overtaking =
        trajectory_intention_data.is_softened_blocking_sequence &&
        !intention_result.is_overtaken;

    // In low-speed interaction situation, for agent not beside of ego, the
    // uncertainty of the single-frame predicted trajectory is relatively
    // high. Therefore, the ego should be restricted from nudging out of the
    // lane, and instead, it should only nudge at its current position.
    if ((ego_param_.speed_mps < 2.0 ||
         is_softened_blocking_without_overtaking) &&
        !blocking_state_data.reasoning_info.is_beside_ego()) {
      GetRecommendedAgentIntentionResult(
          object_occupancy_state, pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/false, is_ego_cut_behind,
          intention_result);
      absl::StrAppend(
          &intention_result.debug_str,
          "\nInlane nudge due to ego slow moving or ego yield to agent; ");
    }
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(object_occupancy_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }

    return true;
  }

  // When it is unsafe on one side of the lane, only nudge in the lane.
  if (is_necessary_nudgable_agent &&
      pass_decision != pb::SnapshotIntention::IGNORE &&
      GetRecommendedAgentIntentionResult(
          object_occupancy_state, tracked_pass_decision, ego_param_.width_m,
          /*should_prioritize_nudge_snapshots=*/false, is_ego_cut_behind,
          intention_result)) {
    rt_event::PostRtEvent<
        rt_event::planner::InLaneNudgeNecessaryAgentWhenSideNotSafe>();
    if (intention_result.is_static) {
      AddCurrentPoseNominalConstraint(object_occupancy_state,
                                      intention_result.lateral_decision,
                                      intention_result);
    } else {
      // Although the nudge behavior is constrained not to cross the lane,
      // there may still be a harsh swerve when the distance from the vehicle
      // is close. Trims the nudge index range to avoid harsh swerve.
      AdjustNudgeRangeBasedOnKinematics(
          ego_param_, tracked_param, predicted_states, nudge_motion_checker(),
          is_oncoming_ahead_ego, object_occupancy_state.object_type(),
          intention_result);
      AddMultipleNominalNudgeConstraintOnPrediction(
          object_occupancy_state, intention_result.lateral_decision,
          intention_result.nudge_index_ranges,
          intention_result.required_lat_gap, intention_result);
    }
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    absl::StrAppend(&intention_result.debug_str,
                    "\nyield agent but side lane not safe do inlane nudge; ");
    return true;
  }

  // Ignore the agent in two cases:
  // 1. The agent is not necessary nudgable, the ego vehicle cuts behind it,
  // and the BlockingSequenceType is kCompleteBlocking.
  // 2. The agent is necessary nudgable, but the pass decision is IGNORE.
  absl::StrAppend(&intention_result.debug_str,
                  "\nego cut behind but CompleteBlocking || yield agent but "
                  "pass decision is ignore;");
  intention_result.is_overtaken = false;
  intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
  return true;
}

std::vector<RepulsionMetaData> CrossAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<planner::pb::YieldIntentionData>& yield_intention_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_CrossAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  // Enable this repulsion when there is an oncoming left turn vehicle in the
  // junction and the right lane is safe.
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  if (blocking_state_data.reasoning_info.is_agent_in_junction() &&
      blocking_state_data.reasoning_info.is_left_turn_oncoming_vehicle() &&
      tracked_param.cross_track_speed_mps <=
          KMaxSpeedAddingRepulsionForOncomingLeftTurnAgent) {
    const double agent_start_arclength_m =
        tracked_param.full_body_start_arclength_m;
    const double agent_end_arclength_m =
        tracked_param.full_body_end_arclength_m;
    const double lateral_offset_m =
        tracked_param.dist_to_center_line_range.start_pos;
    if (lateral_offset_m >=
            kMaxLeftLateralDistanceForOncomingLeftTurnAgentRepulsion ||
        lateral_offset_m <=
            kMaxRightLateralDistanceForOncomingLeftTurnAgentRepulsion) {
      return repulsion_meta_datas;
    }
    // TODO(wanghao): If the agent has a strong tendency to yield, maybe we can
    // also increase the lateral offset here to be slightly more aggressive
    // (show our intent that we do would prefer to pass). This is added on both
    // pass homotopy and yield homotopy.
    const double lateral_offset_buffer_m =
        lateral_offset_m - KMaxRepulsionRightDeviationFromOncomingLeftTurnAgent;

    std::vector<FrenetPoint> repulsion_points;
    repulsion_points.reserve(2);
    // TODO(chengding): Refine the repulsion position.
    // https://kunpeng.xiaojukeji.com/view/revision/4200445/files#comment-45206519.
    repulsion_points.emplace_back(agent_start_arclength_m,
                                  lateral_offset_buffer_m);
    repulsion_points.emplace_back(agent_end_arclength_m,
                                  lateral_offset_buffer_m);

    auto boundary_strength = path::BoundaryStrength::kWeak;
    if (traffic_rule_reasoning_info_.is_right_side_lane_safe()) {
      boundary_strength = path::BoundaryStrength::kModerate;
    }
    repulsion_meta_datas.push_back(RepulsionMetaData(
        RepulsionMetaData::RepulsionDirection::kRight,
        std::move(repulsion_points),
        /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false, boundary_strength,
        /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
        pb::AgentReasonerId::CROSS_AGENT,
        ToProto(TypedObjectId(agent_inlane_state.object_id,
                              pb::ObjectSourceType::kTrackedObject)),
        /*trajectory_id_in=*/std::nullopt));
  }

  // Ignore the agent that likely will not yield to ego.
  if (!IsPotentialYeildIntentionCrossingAgent(
          blocking_state_data.reasoning_info, yield_intention_data,
          tracked_param.relative_heading_rad)) {
    absl::StrAppend(&debug_str,
                    "Not yield to ego or heading not toarwad ref line");
    return repulsion_meta_datas;
  }

  // Ignore the agent behind ego rear bumper
  if (blocking_state_data.reasoning_info.is_agent_behind_rear_bumper()) {
    absl::StrAppend(&debug_str, "no add repulsion for behind rear bumper");
    return repulsion_meta_datas;
  }

  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(reusable_last_path,
                                                tracked_param.pose.center_2d());
  if (!repulsion_direction.has_value()) {
    // If we don't have a reusable path, we use the tracked side to determine
    // the repulsion direction.
    const pb::SnapshotIntention::PassState tracked_pass_decision =
        GetPassStateWithAgentTrackedState(
            tracked_param.center_line_side,
            blocking_state_data.reasoning_info.tracked_side_to_ego());
    if (tracked_pass_decision == pb::SnapshotIntention::IGNORE) {
      absl::StrAppend(&debug_str, "no valid repulsion direction");
      return repulsion_meta_datas;
    }

    repulsion_direction =
        tracked_pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? RepulsionMetaData::RepulsionDirection::kLeft
            : RepulsionMetaData::RepulsionDirection::kRight;
  }
  const bool is_risky =
      IsTargetDirectionRisky(*repulsion_direction, frame_analyzer_results);

  bool is_uncrossable = IsTargetDirectionLaneBoundaryUncrossable(
      *repulsion_direction, traffic_rule_reasoning_info_);

  // Calculates the total crossing likelihood.
  double total_crossing_likelihood = 0.0;
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!(blocking_state_meta_data.reasoning_info.is_crossing() ||
          blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing())) {
      continue;
    }
    int trajectory_id = id;
    const AgentTrajectoryInLaneStates& agent_trajectory_in_lane_state =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               trajectory_id);
    total_crossing_likelihood +=
        agent_trajectory_in_lane_state.trajectory_metadata.likelihood;
  }
  const pb::ObstacleSTBufferConfig& crossing_st_buffer =
      GetSTBufferConfig(blocking_state_data.reasoning_info);
  // Add repulsions for dynamic crossing agent.
  for (const auto& [trajectory_id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    const std::optional<RepulsionMetaData>& repulsion_meta_data =
        ComputeRepulsionMetaDataForCrossingTrajectory(
            ego_param_, agent_inlane_state, blocking_state_meta_data,
            trajectory_id, *repulsion_direction, is_risky, is_uncrossable,
            total_crossing_likelihood, repulsion_required_lat_gap, reasoner_id_,
            crossing_st_buffer, debug_str);
    if (repulsion_meta_data.has_value()) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }

  // TODO(wanghao): Add repulsions for static potential crossing agent.
  return repulsion_meta_datas;
}

std::vector<RepulsionMetaData> CrossAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
        trajectory_blocking_state_data_map,
    const std::optional<planner::pb::YieldIntentionData>& yield_intention_data,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_CrossAgentPathReasoner_PreRepulsionReason);
  std::vector<RepulsionMetaData> repulsion_meta_datas;
  // Enable this repulsion when there is an oncoming left turn vehicle in the
  // junction and the right lane is safe.
  if (blocking_state_data.reasoning_info.is_agent_in_junction() &&
      blocking_state_data.reasoning_info.is_left_turn_oncoming_vehicle() &&
      object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .cross_track_speed_mps <=
          KMaxSpeedAddingRepulsionForOncomingLeftTurnAgent) {
    const double agent_start_arclength_m =
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_start_arclength_m;
    const double agent_end_arclength_m =
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_end_arclength_m;
    const double lateral_offset_m =
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .dist_to_center_line_range.start_pos;
    if (lateral_offset_m >=
            kMaxLeftLateralDistanceForOncomingLeftTurnAgentRepulsion ||
        lateral_offset_m <=
            kMaxRightLateralDistanceForOncomingLeftTurnAgentRepulsion) {
      return repulsion_meta_datas;
    }
    const double lateral_offset_buffer_m =
        lateral_offset_m - KMaxRepulsionRightDeviationFromOncomingLeftTurnAgent;

    std::vector<FrenetPoint> repulsion_points;
    repulsion_points.reserve(2);
    repulsion_points.emplace_back(agent_start_arclength_m,
                                  lateral_offset_buffer_m);
    repulsion_points.emplace_back(agent_end_arclength_m,
                                  lateral_offset_buffer_m);

    auto boundary_strength = path::BoundaryStrength::kWeak;
    if (traffic_rule_reasoning_info_.is_right_side_lane_safe()) {
      boundary_strength = path::BoundaryStrength::kModerate;
    }

    repulsion_meta_datas.push_back(RepulsionMetaData(
        RepulsionMetaData::RepulsionDirection::kRight,
        std::move(repulsion_points),
        /*add_only_overtaking=*/false, /*ignore_if_nudge_failed_in=*/false,
        /*add_only_not_overtaking_in=*/false,
        /*ignore_if_nudge_yield_in=*/false, boundary_strength,
        /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
        pb::AgentReasonerId::CROSS_AGENT,
        ToProto(TypedObjectId(object_occupancy_state.object_id(),
                              pb::ObjectSourceType::kTrackedObject)),
        /*trajectory_id_in=*/std::nullopt));
  }
  // Ignore the agent that likely will not yield to ego.
  const auto& tracked_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  if (!IsPotentialYeildIntentionCrossingAgent(
          blocking_state_data.reasoning_info, yield_intention_data,
          tracked_param.relative_heading_rad)) {
    absl::StrAppend(&debug_str,
                    "Not yield to ego or heading not toarwad ref line");
    return repulsion_meta_datas;
  }

  // Ignore the agent behind ego rear bumper
  if (blocking_state_data.reasoning_info.is_agent_behind_rear_bumper()) {
    absl::StrAppend(&debug_str, "no add repulsion for behind rear bumper");
    return repulsion_meta_datas;
  }

  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(
          reusable_last_path, object_occupancy_state.pose().center_2d());
  if (!repulsion_direction.has_value()) {
    // If we don't have a reusable path, we use the tracked side to determine
    // the repulsion direction.
    const pb::SnapshotIntention::PassState tracked_pass_decision =
        GetPassStateWithAgentTrackedState(
            tracked_param.center_line_side,
            blocking_state_data.reasoning_info.tracked_side_to_ego());
    if (tracked_pass_decision == pb::SnapshotIntention::IGNORE) {
      absl::StrAppend(&debug_str, "no valid repulsion direction");
      return repulsion_meta_datas;
    }

    repulsion_direction =
        tracked_pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? RepulsionMetaData::RepulsionDirection::kLeft
            : RepulsionMetaData::RepulsionDirection::kRight;
  }
  const bool is_risky =
      IsTargetDirectionRisky(*repulsion_direction, frame_analyzer_results);

  bool is_uncrossable = IsTargetDirectionLaneBoundaryUncrossable(
      *repulsion_direction, traffic_rule_reasoning_info_);

  // Calculates the total crossing likelihood.
  double total_crossing_likelihood = 0.0;
  for (const auto& [id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    if (!(blocking_state_meta_data.reasoning_info.is_crossing() ||
          blocking_state_meta_data.reasoning_info.is_cyclist_semi_crossing())) {
      continue;
    }
    int trajectory_id = id;
    const ObjectTrajectoryStates& object_trajectory_states =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            trajectory_id);
    total_crossing_likelihood += object_trajectory_states.Likelihood();
  }
  const pb::ObstacleSTBufferConfig& crossing_st_buffer =
      GetSTBufferConfig(blocking_state_data.reasoning_info);
  // Add repulsions for dynamic crossing agent.
  for (const auto& [trajectory_id, blocking_state_meta_data] :
       trajectory_blocking_state_data_map) {
    const std::optional<RepulsionMetaData>& repulsion_meta_data =
        ComputeRepulsionMetaDataForCrossingTrajectory(
            ego_param_, object_occupancy_state, blocking_state_meta_data,
            trajectory_id, *repulsion_direction, is_risky, is_uncrossable,
            total_crossing_likelihood, repulsion_required_lat_gap, reasoner_id_,
            crossing_st_buffer, debug_str);
    if (repulsion_meta_data.has_value()) {
      repulsion_meta_datas.push_back(*repulsion_meta_data);
    }
  }
  return repulsion_meta_datas;
}

}  // namespace path
}  // namespace planner
