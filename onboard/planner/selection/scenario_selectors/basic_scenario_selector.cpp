#include <algorithm>
#include <limits>
#include <map>
#include <string>

#include "math/interpolation.h"
#include "math/math_util.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_utils.h"
#include "planner/selection/scenario_selectors/basic_scenario_selector.h"

#include "planner/selection/costs/hard_boundary_risk_cost.h"
#include "planner/selection/costs/lane_marking_cost.h"
#include "planner/selection/costs/last_path_cost.h"
#include "planner/selection/costs/lateral_diff_cost.h"
#include "planner/selection/costs/lateral_discomfort_cost.h"
#include "planner/selection/costs/progress_cost.h"
#include "planner/selection/scenario_selectors/lane_change_scenario_selector/lane_change_selection_util.h"
#include "planner/selection/scenario_selectors/progress_efficiency_util.h"
#include "planner/selection/scenario_selectors/selector_utils.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace selection {
namespace {

// Thresholds to determine candidates' risk are significantly different.
constexpr double kSignificantRiskDiff = 0.07;
constexpr double kSignificantRiskDiffPercentage = 0.1;
// Consecutive cycles required to decay in-lane nudge's flicker cost to 0.
constexpr int kRequiredConsecutiveSaferCycles = 5;
constexpr int kRequiredConsecutiveXLaneNudgeReadyCycles = 5;
constexpr double kCLNudgeStuckAdvantageThres = 0.5;

bool IsAcc(const planner::pb::SelectionCandidateInfo& candidate) {
  return candidate.behavior_type() == planner::pb::BehaviorType::LANE_KEEP &&
         candidate.intent_homotopy() ==
             planner::pb::IntentionResult::IGNORE_ALL;
}

bool IsRegularTrajectory(const planner::pb::SelectionCandidateInfo& candidate) {
  return candidate.trajectory_type() !=
         planner::pb::TrajectoryType::LAST_TRAJECTORY;
}

bool IsEquivalentCandidate(const planner::pb::SelectionCandidateInfo& cand1,
                           const TrajectoryMetaData& cand2) {
  return cand1.behavior_type() == cand2.behavior_type() &&
         cand1.intent_homotopy() == cand2.homotopy();
}

// Returns adjust factor based on how many consecutive cycles a nudge candidate
// is available and significantly safer than ignore_all candidate, as this
// indicates the nudge is likely not a noise and should have less flicker cost.
// Note: currently it only affects acc-to-nudge transition.
double ComputePathDiffAdjustByRisk(
    const planner::pb::DecoupledManeuverSeed& seed,
    const TrajectoryMetaData& candidate,
    const std::vector<TrajectoryMetaData*>& /* candidates */) {
  const bool is_acc_to_nudge =
      (seed.selected_behavior_type() == planner::pb::BehaviorType::LANE_KEEP &&
       seed.last_intent().homotopy() ==
           planner::pb::IntentionResult::IGNORE_ALL &&
       candidate.IsInLaneNudge());
  if (!is_acc_to_nudge) return 1.0;

  int consecutive_safer_cycles = 0;
  const auto& decision_hist = seed.selection_seed().decision_history();
  for (auto it = decision_hist.rbegin(); it != decision_hist.rend(); ++it) {
    const auto& hist_candidates = it->candidates();
    const auto same_type_iter =
        std::find_if(hist_candidates.begin(), hist_candidates.end(),
                     [&](const auto& hist_cand) {
                       return IsRegularTrajectory(hist_cand) &&
                              IsEquivalentCandidate(hist_cand, candidate);
                     });
    const auto regular_acc_iter = std::find_if(
        hist_candidates.begin(), hist_candidates.end(),
        [](const auto& hist_cand) {
          return IsRegularTrajectory(hist_cand) && IsAcc(hist_cand);
        });
    const bool acc_selected =
        std::any_of(hist_candidates.begin(), hist_candidates.end(),
                    [](const auto& hist_cand) {
                      return hist_cand.is_selected() && IsAcc(hist_cand);
                    });
    // condition is acc is selected, comparable pair exists and nudge is safer.
    // TODO(selection): try absolute risk filter if over-nudge happens.
    const bool decay_condition_satisfied =
        acc_selected && same_type_iter != hist_candidates.end() &&
        regular_acc_iter != hist_candidates.end() &&
        consecutive_safer_cycles < kRequiredConsecutiveSaferCycles &&
        (regular_acc_iter->collision_risk() - same_type_iter->collision_risk() >
         std::max(kSignificantRiskDiff, same_type_iter->collision_risk() *
                                            kSignificantRiskDiffPercentage));
    if (!decay_condition_satisfied) break;

    consecutive_safer_cycles += 1;
  }
  return math::GetLinearInterpolatedY(0, kRequiredConsecutiveSaferCycles, 1.0,
                                      0.0, consecutive_safer_cycles);
}

}  // namespace

void BasicScenarioSelector::ComputeAndPopulatePathDiffCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  const bool last_path_too_short = (seed_.selected_path().poses_size() < 2);
  const bool in_real_manual_mode =
      !av_comm::InSimulation() &&
      !av_comm::IsAutonomyControlMode(
          Seed::Access<token::WorldModelSeed>::GetMsg(SpeedLastFrame())
              ->last_vehicle_mode());
  for (TrajectoryMetaData* candidate : candidates) {
    // Disables flicker cost when transitioning from manual to auto in real
    // driving, to avoid latching to a wrong traj and restart state calculation.
    if (in_real_manual_mode || last_path_too_short) continue;

    candidate->path_diff_adjust =
        ComputePathDiffAdjustByRisk(seed_, *candidate, candidates) *
        ComputePathDiffAdjustByProgress(seed_, *candidate, candidates);
    candidate->normalized_path_diff =
        std::max(candidate->path_diff * candidate->path_diff_adjust,
                 (seed_.selected_behavior_type() != candidate->behavior_type())
                     ? math::constants::kEpsilon
                     : 0.0);
  }
  // Adjust path diff cost by last path for lane change candidates.
  const double path_diff_compensation =
      CalculateCrossLaneDiffCompensationByLastPath(
          candidates, seed_.selection_seed().consecutive_last_cl_cycles());
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane() || candidate->IsLastTrajectory()) {
      continue;
    }
    candidate->normalized_path_diff += path_diff_compensation;
  }
  for (TrajectoryMetaData* candidate : candidates) {
    candidate->normalized_path_diff =
        math::Clamp(candidate->normalized_path_diff, 0.0, 1.0);
  }

  if (seed_.selected_behavior_type() != planner::pb::BehaviorType::LANE_KEEP)
    return;

  // TODO(selection): unify such addjustment for LF to LC cases.
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) continue;

    candidate->normalized_path_diff = 0.0;
  }
}

void BasicScenarioSelector::ComputeAndPopulateLateralDiffCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeLateralDiffCost(candidates, seed_.selected_path(),
                         base_selection_config_);
  // Adjust path diff cost by last path for lane change candidates.
  const double lateral_diff_compensation =
      CalculateCrossLaneDiffCompensationByLastPath(
          candidates, seed_.selection_seed().consecutive_last_cl_cycles());
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane() || candidate->IsLastTrajectory()) {
      continue;
    }
    candidate->normalized_lateral_diff = math::Clamp(
        candidate->normalized_lateral_diff + lateral_diff_compensation, 0.0,
        1.0);
  }

  if (seed_.selected_behavior_type() != planner::pb::BehaviorType::LANE_KEEP)
    return;

  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) continue;
    candidate->normalized_lateral_diff = 0.0;
  }
}

void BasicScenarioSelector::ComputeAndPopulateLaneSequenceFlickerCost(
    const std::vector<TrajectoryMetaData*>& candidates,
    bool is_lane_change_ready) {
  TRACE_EVENT_SCOPE(planner, DecoupledSelection_Flicker);
  // CL can be available in unstuck and pullout scenario as well, thus this
  // logic can't be put in LaneChangeScenarioSelector only.
  if (seed_.selected_behavior_type() != planner::pb::BehaviorType::LANE_KEEP)
    return;

  // TODO(selection): unify such addjustment for LF to LC cases.
  for (TrajectoryMetaData* candidate : candidates) {
    if (!candidate->IsCrossLane()) continue;
    ComputeCrossLaneFlickerCost(world_model_, seed_, candidate,
                                is_lane_change_ready);
  }
}

void BasicScenarioSelector::ComputeAndPopulateLastPathCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  for (auto* last_path_candidate : candidates) {
    if (last_path_candidate->IsLastTrajectory()) {
      // Restore last_path_cost from seed if we keep reusing it.
      // If we reuse for the first time, the last_path_cost in seed is zero.
      last_path_candidate->last_path_cost =
          ComputeBasicLastPathCost(*last_path_candidate, candidates,
                                   seed_.selection_seed().last_path_cost());
    }
  }
}

void BasicScenarioSelector::ComputeAndPopulateProgressCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicProgressCost(candidates, base_selection_config_, world_model_);
}

void BasicScenarioSelector::ComputeAndPopulateProgressEfficiencyCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeAndPopulateProgressCost(candidates);
  ComputeBasicProgressEfficiencyCost(candidates, world_model_);
}

void BasicScenarioSelector::ComputeAndPopulateLateralDiscomfortCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicLateralDiscomfortCost(candidates, base_selection_config_);
}

void BasicScenarioSelector::ComputeAndPopulateSpeedDiscomfortCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicSpeedDiscomfortCost(candidates);
}

void BasicScenarioSelector::ComputeAndPopulateStuckCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicStuckCost(candidates, world_model_, seed_);
}

void BasicScenarioSelector::ComputeAndPopulateGeometryLaneMarkingCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicGeometryLaneMarkingCost(candidates);
}

void BasicScenarioSelector::ComputeAndPopulateGeometryHardBoundaryRiskCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  ComputeBasicGeometryHardBoundaryRiskCost(candidates, base_selection_config_);
}

void BasicScenarioSelector::ComputeAndPopulateRoutingCost(
    const std::vector<TrajectoryMetaData*>& candidates) {
  for (TrajectoryMetaData* candidate : candidates) {
    candidate->normalized_routing_cost = 0.0;
  }
}

void BasicScenarioSelector::UpdateSeed(
    const std::map<std::string, TrajectoryMetaData*>& /*candidates*/,
    const planner::pb::SelectionSeed& prev_seed,
    planner::pb::SelectionSeed* seed) {
  const int crosslane_nudge_ready_cycles =
      prev_seed.consecutive_crosslane_nudge_ready_cycles();
  seed->set_consecutive_crosslane_nudge_ready_cycles(
      is_crosslane_nudge_ready_ ? crosslane_nudge_ready_cycles + 1 : 0);
}

double BasicScenarioSelector::ComputePathDiffAdjustByProgress(
    const planner::pb::DecoupledManeuverSeed& seed,
    const TrajectoryMetaData& candidate,
    const std::vector<TrajectoryMetaData*>& candidates) {
  const bool is_acc_to_crosslane_nudge =
      (seed.selected_behavior_type() == planner::pb::BehaviorType::LANE_KEEP &&
       seed.last_intent().homotopy() ==
           planner::pb::IntentionResult::IGNORE_ALL &&
       candidate.IsCrossLaneNudge());
  if (!is_acc_to_crosslane_nudge) return 1.0;
  // check if any of cross-lane nudge candidate has significant stuck advantage
  // than acc candidate with worst stuck cost.
  double max_acc_stuck = 0.0;
  double min_nudge_stuck = 1.0;
  for (const TrajectoryMetaData* candidate : candidates) {
    if (candidate->IsCrossLaneNudge()) {
      math::UpdateMin(candidate->stuck_potential, min_nudge_stuck);
    }
    if (candidate->IsAcc()) {
      math::UpdateMax(candidate->stuck_potential, max_acc_stuck);
    }
  }
  is_crosslane_nudge_ready_ =
      ((max_acc_stuck - min_nudge_stuck) > kCLNudgeStuckAdvantageThres);
  if (!is_crosslane_nudge_ready_) return 1.0;

  return math::GetLinearInterpolatedY(
      0, kRequiredConsecutiveXLaneNudgeReadyCycles, 1.0, 0.0,
      seed.selection_seed().consecutive_crosslane_nudge_ready_cycles());
}

}  // namespace selection
}  // namespace planner
