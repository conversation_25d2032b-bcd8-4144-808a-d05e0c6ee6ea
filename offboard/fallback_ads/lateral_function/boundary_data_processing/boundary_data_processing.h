
#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_BOUNDARY_DATA_PROCESSING_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_BOUNDARY_DATA_PROCESSING_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "_out/Appl/GenData/Rte_Type.h"

typedef enum {
  NO_LANE_VALID = 0,
  ONLY_LEFT_LANE_VALID = 1,
  ONLY_RIGHT_LANE_VALID = 2,
  LEFT_LANE_IS_VIRTUAL = 3,
  RIGHT_LANE_IS_VIRTUAL = 4,
  BOTH_LANES_VALID = 5,
} LaneState;

typedef struct {
  float y0;
  float heading;
  float curvature;
  float curvature_change;
  float length;
} LaneEquation;

extern LaneEquation LaneGeneration(const CSI_LaneInfo_Struct* lane_info_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_BOUNDARY_DATA_PROCESSING_H_
