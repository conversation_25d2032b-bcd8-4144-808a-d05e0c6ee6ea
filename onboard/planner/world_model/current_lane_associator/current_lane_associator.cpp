#include "planner/world_model/current_lane_associator/current_lane_associator.h"

#include <algorithm>
#include <iterator>
#include <limits>
#include <map>
#include <optional>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "geometry/algorithms/specialization/area_polygon_with_cache.h"
#include "math/angle.h"
#include "math/math_util.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence/lane_sequence_generator_utility2.h"
#include "planner/constants.h"
#include "planner/planning_gflags.h"
#include "planner/utility/common/common_utility.h"
#include "planner/world_model/current_lane_associator/current_lane_associator_utility.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/regional_map/common_utility.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_param.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/pnc_object.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/pnc_map_service.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "routing/engine/route_point_util.h"
#include "types/canbus_mode.h"
#include "voy_protos/math.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace {
constexpr int kMaximumNumOfLanesInASection = 10;
constexpr double kStaticEgoSpeedInMps = 0.5;
constexpr double kMinimumIntersectionPercentage = 0.05;
constexpr bool kEnableJumpOutToNeighborRoad = false;
// If the distance from ego to a section is bigger than this threshold along the
// last selected regional path, we should not regard it near ego. And these
// lanes on this section should not be current lane candidate.
constexpr double kMaxDistanceToGetNearSectionFromEgoInMeter = 20.0;
constexpr double kMaxDistanceToGetClosestNearLaneFromEgoInMeter = 15.0;

// Adds logs for current lane candidates. It will only be used when we are going
// to debug.
[[maybe_unused]] void ShowCurrentLaneCandidates(
    const std::vector<lane_candidate::CurrentLaneCandidate>& candidates,
    const std::string& prefix) {
  LOG(ERROR) << prefix << ", size=" << candidates.size();
  for (const auto& candidate : candidates) {
    LOG(ERROR) << prefix
               << ", lane=" << (candidate.lane() ? candidate.lane()->id() : -1)
               << ", on_last=" << candidate.is_in_last_selected_lane_sequence()
               << ", pose_in=" << candidate.is_physical()
               << ", in_cost_map=" << candidate.is_in_cost_map()
               << ", jump_out_type=" << candidate.jump_out_type();
  }
}

// Adds logs for map element vector or set. The CollectorType should be a
// std::vector<const Lane*/Section*> or std::set<const Lane*/section*>. It will
// only be used when we are going to debug.
template <typename CollectorType>
void ShowMapElementSequence(const CollectorType& elements,
                            const std::string& prefix) {
  LOG(ERROR) << prefix << ", size=" << elements.size();
  for (const auto* element : elements) {
    LOG(ERROR) << prefix << ", ele=" << (element ? element->id() : -1);
  }
}

// Gets the set of sections in the section sequence.
void GetRegionalSectionSet(
    const std::vector<const pnc_map::Section*>& section_sequence,
    pnc_map::PncObjectSet<const pnc_map::Section*>* section_set) {
  if (!section_set || section_sequence.empty()) {
    return;
  }
  section_set->clear();
  for (const auto* section : section_sequence) {
    section_set->insert(section);
  }
}

// Returns true if the lane is in any section in the set.
bool IsLaneInSectionSet(
    const pnc_map::Lane* lane,
    const std::optional<pnc_map::PncObjectSet<const pnc_map::Section*>>&
        section_set) {
  return lane != nullptr &&
         (!section_set.has_value() ||
          section_set->find(lane->section()) != section_set->end());
}

// Returns true if the lane is in any section in the set.
[[maybe_unused]] bool IsLaneInSectionSet(
    int64_t lane_id,
    const std::optional<pnc_map::PncObjectSet<const pnc_map::Section*>>&
        section_set) {
  if (!section_set.has_value()) {
    return true;
  }
  const std::function<bool(const pnc_map::Section*)> is_lane_in_section =
      [lane_id](const pnc_map::Section* section) {
        return section != nullptr &&
               std::any_of(section->lanes().begin(), section->lanes().end(),
                           [&lane_id](const pnc_map::Lane* lane) {
                             return lane != nullptr && lane->id() == lane_id;
                           });
      };
  return std::any_of(section_set->begin(), section_set->end(),
                     is_lane_in_section);
}

// Gets the intersection area percentage between ego and the lane in the area of
// ego.
double GetEgoIntersectionPercentageWithLane(
    const math::geometry::OrientedBox2d& robot_bounding_box,
    const pnc_map::Lane& lane, double ego_area) {
  if (ego_area < math::constants::kEpsilon) {
    return 0.0;
  }
  const double intersection_area =
      IntersectionArea(robot_bounding_box, lane.border());
  return intersection_area / ego_area;
}

// Construct a current lane candidate from a lane and near lane candidate.
lane_candidate::CurrentLaneCandidate GetCurrentLaneCandidate(
    const lane_candidate::NearLaneCandidate& near_lane_candidate,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const CurrentLaneCostEstimator& current_lane_cost_estimator,
    pb::JumpOutType jump_out_type, const std::set<int32_t>& prefix_ids) {
  const pnc_map::Lane* lane = near_lane_candidate.lane();
  routing::pb::LaneCandidate lane_candidate;
  lane_candidate.set_lane_id(lane->id());
  const math::ProximityQueryInfo proximity = lane->center_line().GetProximity(
      ego_position, math::pb::UseExtensionFlag::kForbid);
  const double path_point_cost = routing::utility::GetPathPointCost(
      proximity.dist, routing_cost_generator.cost_engine_config()
                          .cost_engine_config_info()
                          .speed_for_path_point_meters_per_sec());
  // Not consider it as jump out when pose is in lane and it's not a fork lane.
  const pb::JumpOutType modified_jump_out_type =
      near_lane_candidate.is_physical() &&
              !lane->routing_vertex().is_fork_lane()
          ? pb::JumpOutType::NOT_JUMP_OUT
          : jump_out_type;
  const double cost =
      path_point_cost +
      current_lane_cost_estimator.GetCost(
          near_lane_candidate.is_in_last_selected_lane_sequence(),
          near_lane_candidate.is_in_cost_map(),
          modified_jump_out_type == pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT);
  lane_candidate.set_candidate_cost(cost);
  lane_candidate.mutable_position()->set_x(ego_position.x());
  lane_candidate.mutable_position()->set_y(ego_position.y());
  const double percentage = proximity.arc_length / lane->length();
  lane_candidate.set_percentage_along_lane(percentage);
  return {near_lane_candidate, std::move(lane_candidate), cost,
          modified_jump_out_type, prefix_ids};
}

// Returns overlap drivable lanes.
std::vector<const pnc_map::Lane*> GetOverlapDrivableLanes(
    const std::vector<const pnc_map::Lane*>& drivable_lanes,
    const math::geometry::OrientedBox2d& robot_bounding_box) {
  if (drivable_lanes.empty()) {
    return {};
  }

  std::vector<const pnc_map::Lane*> overlap_drivable_lanes;
  overlap_drivable_lanes.reserve(drivable_lanes.size());
  for (const pnc_map::Lane* drivable_lane : drivable_lanes) {
    // Check for intersection between two Polygons
    if (math::geometry::Intersects(robot_bounding_box,
                                   drivable_lane->border())) {
      // Save overlap drivable lane.
      overlap_drivable_lanes.push_back(drivable_lane);
    }
  }

  return overlap_drivable_lanes;
}

// Returns true if ego is in a normal lane follow or lane change maneuver.
bool IsEgoInNormalLFOrLCManeuver(
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    double ego_speed, bool is_new_trip, bool is_in_autonomous_mode,
    bool is_last_cycle_lane_change_finished, bool is_lane_follow) {
  if (last_selected_lane_sequence_candidate == nullptr) {
    return false;
  }

  if (!is_in_autonomous_mode ||
      (is_new_trip && ego_speed < kStaticEgoSpeedInMps)) {
    return false;
  }

  if (lane_selection::IsJumpOutLaneSequence(
          *last_selected_lane_sequence_candidate)) {
    return false;
  }

  // If in last cycle, lane change was finished, there is no need to check the
  // lane sequence type. If lane change was finished in last cycle, we regard it
  // as lane follow.
  if (is_last_cycle_lane_change_finished) {
    return is_lane_follow;
  }

  if (is_lane_follow
          ? !lane_selection::IsLaneFollowType(
                last_selected_lane_sequence_candidate->lane_sequence_type())
          : !lane_selection::IsLaneChangeType(
                last_selected_lane_sequence_candidate->lane_sequence_type())) {
    return false;
  }

  return true;
}

// Returns true if the input lane is physical lanes.
bool IsPhysicalLane(const std::vector<const pnc_map::Lane*>& physical_lanes,
                    const pnc_map::Lane* lane) {
  return std::any_of(physical_lanes.begin(), physical_lanes.end(),
                     [lane](const pnc_map::Lane* physical_lane) {
                       return lane != nullptr && physical_lane != nullptr &&
                              lane->id() == physical_lane->id();
                     });
}

// Returns true if any hard boundary is between the given lanes at corresponding
// arc length. Note(Huoliang): This method is only meaningful for nearby lanes
// to check if there is any hard boundary between them. In current lane
// associator, the given lanes should be physical lane and logic lane.
bool HasHardBoundaryBetweenNearbyLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane* lane_a, const pnc_map::Lane* lane_b,
    double arc_length_a, double arc_length_b) {
  CHECK(lane_a);
  CHECK(lane_b);
  const int64_t section_id_a = lane_a->section()->id();
  const int64_t section_id_b = lane_b->section()->id();
  // Return false if the lanes in the same section and there is no middle hard
  // boundary in the road.
  if (section_id_a == section_id_b) {
    const auto road = joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Road>(
        lane_a->section()->road()->id(),
        pnc_map::ElementQueryType::kOnlineFirst);
    if (!road->online() &&
        road->middle_boundary().hard_boundary_lines.empty()) {
      // Road has no dynamic hard boundaries and no middle_boundary.
      // There will be no hard boundary between lanes in the same section.
      return false;
    }
  }

  // Return false if the lanes are in the successive sections.
  if (lane_a->section()->IsSuccessor(section_id_b) ||
      lane_b->section()->IsSuccessor(section_id_a)) {
    return false;
  }

  // Return true if any hard boundary line:
  // 1. has valid range at |hard_boundary_arc_length| and
  // 2. the point in center line at |center_line_arc_length| can project to it
  // with the side same as |check_left_for_hard_boundary|.
  auto is_different_side =
      [](const math::geometry::PolylineCurve2d& center_line,
         const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos,
         double center_line_arc_length, double hard_boundary_arc_length,
         bool check_left_for_hard_boundary) {
        const math::geometry::Point2d& center_line_point =
            center_line.GetInterp(center_line_arc_length);
        return std::any_of(
            hard_boundary_infos.begin(), hard_boundary_infos.end(),
            [&center_line_point, hard_boundary_arc_length,
             check_left_for_hard_boundary](
                const pnc_map::HardBoundaryInfo& hard_boundary_info) {
              if (!math::IsInRange(
                      hard_boundary_arc_length,
                      hard_boundary_info.range_on_center_line.start_pos,
                      hard_boundary_info.range_on_center_line.end_pos)) {
                return false;
              }
              const math::ProximityQueryInfo proximity =
                  hard_boundary_info.hard_boundary_line->GetProximity(
                      center_line_point, math::pb::UseExtensionFlag::kForbid);
              // The hard boundary is beyond the position of
              // |center_line_point|.
              if (proximity.relative_position !=
                  math::RelativePosition::kWithIn) {
                return false;
              }
              // This should not happen because lane's center line will not be
              // on a hard boundary.
              if (proximity.side == math::pb::kOn) {
                return true;
              }
              return check_left_for_hard_boundary
                         ? proximity.side == math::pb::kLeft
                         : proximity.side == math::pb::kRight;
            });
      };

  // Return true if the lanes are separated by hard boundary.
  auto is_separated_by_hard_boundary =
      [&is_different_side, arc_length_a, arc_length_b](
          const pnc_map::Lane* lane_a_for_hard_boundary,
          const pnc_map::Lane* lane_b_for_hard_boundary, bool check_left_a) {
        const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos_a =
            check_left_a ? lane_a_for_hard_boundary->left_hard_boundary_info()
                         : lane_a_for_hard_boundary->right_hard_boundary_info();
        const std::vector<pnc_map::HardBoundaryInfo>& hard_boundary_infos_b =
            check_left_a ? lane_b_for_hard_boundary->right_hard_boundary_info()
                         : lane_b_for_hard_boundary->left_hard_boundary_info();
        if (hard_boundary_infos_a.empty() || hard_boundary_infos_b.empty()) {
          return false;
        }

        const bool has_valid_hard_boundary_id =
            hard_boundary_infos_a.front().has_valid_hard_boundary_id();
        if (!has_valid_hard_boundary_id) {
          return is_different_side(lane_b_for_hard_boundary->center_line(),
                                   hard_boundary_infos_a, arc_length_b,
                                   arc_length_a, check_left_a) ||
                 is_different_side(lane_a_for_hard_boundary->center_line(),
                                   hard_boundary_infos_b, arc_length_a,
                                   arc_length_b, !check_left_a);
        }

        for (const pnc_map::HardBoundaryInfo& hard_boundary_info_a :
             hard_boundary_infos_a) {
          if (!math::IsInRange(
                  arc_length_a,
                  hard_boundary_info_a.range_on_center_line.start_pos,
                  hard_boundary_info_a.range_on_center_line.end_pos)) {
            continue;
          }
          for (const pnc_map::HardBoundaryInfo& hard_boundary_info_b :
               hard_boundary_infos_b) {
            if (!math::IsInRange(
                    arc_length_b,
                    hard_boundary_info_b.range_on_center_line.start_pos,
                    hard_boundary_info_b.range_on_center_line.end_pos)) {
              continue;
            }
            if (hard_boundary_info_a.id == hard_boundary_info_b.id) {
              return true;
            }
          }
        }

        return false;
      };

  const auto lane_a_for_hard_boundary =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Lane>(
          lane_a->id(), pnc_map::ElementQueryType::kOnlineFirst);
  const auto lane_b_for_hard_boundary =
      joint_pnc_map_service.GetObjectSharedPtr<pnc_map::Lane>(
          lane_b->id(), pnc_map::ElementQueryType::kOnlineFirst);
  return is_separated_by_hard_boundary(lane_a_for_hard_boundary.get(),
                                       lane_b_for_hard_boundary.get(),
                                       /*check_left_a=*/true) ||
         is_separated_by_hard_boundary(lane_a_for_hard_boundary.get(),
                                       lane_b_for_hard_boundary.get(),
                                       /*check_left_a=*/false);
}

// Returns true if any physical lane and current lane are separated by hard
// boundary.
bool IsSeparatedWithEgoByHardBoundary(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const lane_candidate::CurrentLaneCandidate& current_lane_candidate) {
  if (physical_lanes.empty()) {
    return false;
  }
  return std::any_of(
      physical_lanes.begin(), physical_lanes.end(),
      [&joint_pnc_map_service, &near_lane_candidates_map,
       &current_lane_candidate](const pnc_map::Lane* physical_lane) {
        const auto it = near_lane_candidates_map.find(physical_lane->id());
        if (it == near_lane_candidates_map.end()) {
          return false;
        }
        return HasHardBoundaryBetweenNearbyLanes(
            joint_pnc_map_service, physical_lane, current_lane_candidate.lane(),
            it->second.position_arclength_from_lane_start(),
            current_lane_candidate.position_arclength_from_lane_start());
      });
}

// Filters invalid candidates in some determine scenarios.
std::vector<lane_candidate::CurrentLaneCandidate>
FilterCurrentLanesInSomeDetermineScenarios(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        drivable_lane_candidates,
    bool is_in_normal_LF_maneuver, bool is_last_cycle_lane_change_finished,
    const pb::EgoReachTargetLaneSequenceState::HasReached&
        has_ego_reached_target_lane_sequence) {
  std::set<int64_t> filtered_lane_ids;
  const bool is_current_on_fork_lane = std::any_of(
      drivable_lane_candidates.begin(), drivable_lane_candidates.end(),
      [](const lane_candidate::CurrentLaneCandidate& candidate) {
        return candidate.lane()->IsForkLane();
      });
  if (is_current_on_fork_lane) {
    // If ego is on fork, the diversity of current lanes is maybe necessary.
    return drivable_lane_candidates;
  }
  // If waypoint assist is engaged but have not reached target lane sequence, do
  // not trim physical lane and return current drivable lanes.
  if (!is_in_normal_LF_maneuver ||
      has_ego_reached_target_lane_sequence ==
          pb::EgoReachTargetLaneSequenceState::NOT_REACHED) {
    return drivable_lane_candidates;
  }

  if (is_last_cycle_lane_change_finished) {
    for (const auto& candidate : drivable_lane_candidates) {
      if (!candidate.is_in_last_target_lane_sequence() ||
          !candidate.is_overlapped() ||
          IsSeparatedWithEgoByHardBoundary(joint_pnc_map_service,
                                           near_lane_candidates_map,
                                           physical_lanes, candidate)) {
        // If in last cycle, lane change was finished, the overlapped current
        // lane on last cycle target lane sequence is prefer. If the candidate
        // is separated with ego by hard boudary, we should filter out it.
        filtered_lane_ids.insert(candidate.lane()->id());
      }
    }
  } else {
    for (const auto& candidate : drivable_lane_candidates) {
      if (!candidate.is_in_last_selected_lane_sequence() ||
          IsSeparatedWithEgoByHardBoundary(joint_pnc_map_service,
                                           near_lane_candidates_map,
                                           physical_lanes, candidate)) {
        // If the last selected lane sequence candidate is a normal lane follow
        // one, the current lane on last lane sequence is prefer.
        // If the candidate is separated with ego by hard boudary, we should
        // filter out it.
        filtered_lane_ids.insert(candidate.lane()->id());
      }
    }
  }
  if (filtered_lane_ids.size() == drivable_lane_candidates.size()) {
    // If all candidates are filtered, we just keep the origin ones.
    return drivable_lane_candidates;
  }
  std::vector<lane_candidate::CurrentLaneCandidate> current_lane_candidates;
  current_lane_candidates.reserve(drivable_lane_candidates.size() -
                                  filtered_lane_ids.size());
  std::copy_if(drivable_lane_candidates.begin(), drivable_lane_candidates.end(),
               std::back_inserter(current_lane_candidates),
               [&filtered_lane_ids](
                   const lane_candidate::CurrentLaneCandidate& candidate) {
                 return filtered_lane_ids.find(candidate.lane()->id()) ==
                        filtered_lane_ids.end();
               });
  return current_lane_candidates;
}

// Adds lanes into current lane candidates.
void AddLanesIntoCurrentLaneCandidates(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const std::set<LightCurrentLaneCandidate>& light_current_lane_candidates,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const CurrentLaneCostEstimator& current_lane_cost_estimator,
    std::vector<lane_candidate::CurrentLaneCandidate>*
        current_lane_candidates) {
  if (light_current_lane_candidates.empty()) {
    return;
  }
  for (const auto& light_candidate : light_current_lane_candidates) {
    const pnc_map::Lane* lane = CHECK_NOTNULL(light_candidate.lane);
    const auto near_lane_candidate_it =
        near_lane_candidates_map.find(lane->id());
    if (near_lane_candidate_it == near_lane_candidates_map.end()) {
      continue;
    }
    const auto candidate_it = std::find_if(
        current_lane_candidates->begin(), current_lane_candidates->end(),
        [lane](const lane_candidate::CurrentLaneCandidate& candidate) {
          return lane->id() == candidate.lane()->id();
        });
    if (candidate_it == current_lane_candidates->end()) {
      // If not existing, add the new current lane candidate.
      current_lane_candidates->push_back(GetCurrentLaneCandidate(
          near_lane_candidate_it->second, ego_position, routing_cost_generator,
          current_lane_cost_estimator, light_candidate.jump_out_type,
          light_candidate.prefix_ids));
    } else {
      // If already existing, update the fields of |jump_out_type| and
      // |prefix_ids|.
      // [Note]: Not consider it as jump out when pose is in lane and it's not a
      // fork lane.
      if (light_candidate.jump_out_type != pb::JumpOutType::NOT_JUMP_OUT &&
          (!candidate_it->is_physical() ||
           lane->routing_vertex().is_fork_lane())) {
        candidate_it->set_jump_out_type(light_candidate.jump_out_type);
        if (candidate_it->jump_out_type() ==
            pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT) {
          candidate_it->set_cost(
              candidate_it->cost() +
              current_lane_cost_estimator.GetCostEstimateCost(
                  pb::CurrentLaneCostType::kJumpOutForStuckAvoidance));
        }
      }
      // Update the prefix ids for existing candidate.
      candidate_it->set_prefix_ids(light_candidate.prefix_ids);
    }
  }
}

// Returns overlap drivable lanes who intersect with ego with a minimum
// intersection percentage.
std::vector<lane_candidate::CurrentLaneCandidate>
GetOverlapDrivableLaneCandidatesWithMinIntersection(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const CurrentLaneCostEstimator& current_lane_cost_estimator,
    const std::vector<const pnc_map::Lane*>& drivable_lanes,
    const math::geometry::OrientedBox2d& robot_bounding_box, double ego_area,
    double minimum_overlap_area_percentage) {
  if (drivable_lanes.empty()) {
    return {};
  }

  std::set<LightCurrentLaneCandidate> overlap_drivable_lanes;
  double maximum_intersection_percent = 0;
  const pnc_map::Lane* lane_with_largest_positive_intersection = nullptr;
  for (const pnc_map::Lane* drivable_lane : drivable_lanes) {
    // Check for intersection between two Polygons
    if (!drivable_lane) continue;
    const double intersection_percentage = GetEgoIntersectionPercentageWithLane(
        robot_bounding_box, *drivable_lane, ego_area);
    if (intersection_percentage > maximum_intersection_percent) {
      maximum_intersection_percent = intersection_percentage;
      lane_with_largest_positive_intersection = drivable_lane;
    }
    if (intersection_percentage >= minimum_overlap_area_percentage) {
      // Save overlap drivable lane.
      overlap_drivable_lanes.emplace(drivable_lane);
    }
  }
  if (overlap_drivable_lanes.empty() &&
      lane_with_largest_positive_intersection) {
    overlap_drivable_lanes.emplace(lane_with_largest_positive_intersection);
  }
  std::vector<lane_candidate::CurrentLaneCandidate> current_lane_candidates;
  AddLanesIntoCurrentLaneCandidates(
      near_lane_candidates_map, overlap_drivable_lanes, ego_position,
      routing_cost_generator, current_lane_cost_estimator,
      &current_lane_candidates);
  return current_lane_candidates;
}

// Fills a nearest lane who is on last lane sequence, if all candidates are not
// on last lane sequence.
void FillLaneOnLastSelectedLaneSequenceIfMissed(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const CurrentLaneCostEstimator& current_lane_cost_estimator,
    const std::vector<const pnc_map::Lane*>& drivable_lanes,
    bool is_in_autonomous_mode,
    std::vector<lane_candidate::CurrentLaneCandidate>*
        current_drivable_lane_candidates) {
  if (!current_drivable_lane_candidates || drivable_lanes.empty() ||
      !is_in_autonomous_mode) {
    // If the last cycle is not in autonomous mode, we can not fill in the
    // current lane on last selected lane sequence. Because we can not expect
    // that ego will follow the lane sequence.
    return;
  }
  const bool is_current_lane_on_last_sequence_exist =
      std::any_of(current_drivable_lane_candidates->begin(),
                  current_drivable_lane_candidates->end(),
                  [](const lane_candidate::CurrentLaneCandidate& candidate) {
                    return candidate.is_in_last_selected_lane_sequence();
                  });
  if (is_current_lane_on_last_sequence_exist) {
    // Already exist a lane on last sequence in the candidate, do not need to do
    // anything.
    return;
  }
  const std::string debug_info =
      "All lanes are not in last selected lane sequence, should add a "
      "closest lane from it.";
  rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchNotInLastSequence>(
      debug_info);

  // All candidates are not on the last selected lane sequence, we find the
  // nearest lane on last sequence from |drivable_lanes| and add it.
  const pnc_map::Lane* nearest_lane_on_last_sequence = nullptr;
  double min_dist_to_current_lane_on_last_sequence =
      std::numeric_limits<double>::max();
  for (const pnc_map::Lane* drivable_lane : drivable_lanes) {
    const auto& near_lane_it =
        near_lane_candidates_map.find(drivable_lane->id());
    const bool is_on_last_sequence =
        near_lane_it != near_lane_candidates_map.end() &&
        near_lane_it->second.is_in_last_selected_lane_sequence();
    if (is_on_last_sequence &&
        near_lane_it->second.position_to_lane_center_dist_in_m() <
            min_dist_to_current_lane_on_last_sequence) {
      min_dist_to_current_lane_on_last_sequence =
          near_lane_it->second.position_to_lane_center_dist_in_m();
      nearest_lane_on_last_sequence = drivable_lane;
    }
  }
  if (nearest_lane_on_last_sequence) {
    // This lane is what we need and it will not duplicate with these lanes in
    // |current_drivable_lane_candidates|.
    AddLanesIntoCurrentLaneCandidates(
        near_lane_candidates_map,
        {LightCurrentLaneCandidate(nearest_lane_on_last_sequence)},
        ego_position, routing_cost_generator, current_lane_cost_estimator,
        current_drivable_lane_candidates);
  }
}

// Filters |current_drivable_lanes| and returns matched lanes on the immutable
// lane sequence. if no lane is on the lane sequence, returns all.
// std::vector<const pnc_map::Lane*> GetCurrentLanesFromImmutableLaneSequence(
//     const std::vector<const pnc_map::Lane*>& current_drivable_lanes,
//     const pb::LaneSequencePlanInitState& lane_sequence_plan_init_state,
//     const pb::RouteEvent& route_event) {
//   if (route_event.event_type() != pb::RouteEventType::kOnTrip) {
//     return current_drivable_lanes;
//   }
//   // If the ego is on trip, we should respect the immutable lane sequence
//   from
//   // last cycle. Filter out lanes that are not on the lane sequence.
//   std::vector<const pnc_map::Lane*> current_drivable_lanes_on_sequence;
//   current_drivable_lanes_on_sequence.reserve(current_drivable_lanes.size());
//   for (const pnc_map::Lane* current_drivable_lane : current_drivable_lanes) {
//     if (std::any_of(
//             lane_sequence_plan_init_state.immutable_lane_sequence().begin(),
//             lane_sequence_plan_init_state.immutable_lane_sequence().end(),
//             [current_drivable_lane](const int64_t lane_id) {
//               return lane_id == current_drivable_lane->id();
//             })) {
//       current_drivable_lanes_on_sequence.emplace_back(current_drivable_lane);
//     }
//   }
//   if (!current_drivable_lanes_on_sequence.empty()) {
//     // If any lane on immutable lane sequence from last cycle is found,
//     return. return current_drivable_lanes_on_sequence;
//   }
//   return current_drivable_lanes;
// }

// Gets successor-removed current drivable lanes.
std::vector<lane_candidate::CurrentLaneCandidate> GetSuccessorRemovedLanes(
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_drivable_lane_candidates) {
  // For any pair of lanes in current_drivable_lanes, if they are successor and
  // predecessor, do not consider the successor as current lanes candidate for
  // regional path generation. This is to avoid routing problems both for
  // horizon lane graph and waypoint graph where two lane candidates being
  // successive relationship might cause short lane sequence and end of lane
  // sequence fence behind ego. For example, if there are three current drivable
  // lanes A, B and C, if A->B->C, only return A. If A->C, and B->C, return both
  // A and B. If A->B and C is B's neighbor, return both A and C.
  std::set<int64_t> lanes_to_remove;
  for (const lane_candidate::CurrentLaneCandidate& candidate_a :
       current_drivable_lane_candidates) {
    for (const lane_candidate::CurrentLaneCandidate& candidate_b :
         current_drivable_lane_candidates) {
      if (candidate_a.lane()->id() == candidate_b.lane()->id()) {
        continue;
      }
      if (candidate_a.lane()->IsSuccessor(*candidate_b.lane())) {
        lanes_to_remove.insert(candidate_b.lane()->id());
      }
    }
  }

  std::vector<lane_candidate::CurrentLaneCandidate>
      filtered_current_drivable_lanes;
  filtered_current_drivable_lanes.reserve(
      current_drivable_lane_candidates.size());
  std::copy_if(current_drivable_lane_candidates.begin(),
               current_drivable_lane_candidates.end(),
               std::back_inserter(filtered_current_drivable_lanes),
               [&lanes_to_remove](
                   const lane_candidate::CurrentLaneCandidate& current_lane) {
                 return lanes_to_remove.count(current_lane.lane()->id()) == 0;
               });
  return filtered_current_drivable_lanes;
}

// Returns the current lane on the last selected section sequence.
const pnc_map::Lane* GetCurrentLaneOnLastSelectedSectionSequence(
    const math::geometry::Point2d& robot_center,
    const std::vector<const pnc_map::Lane*>& near_lanes,
    const std::vector<const pnc_map::Section*>& last_selected_section_sequence,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    bool is_waypoint_assist_invoked) {
  DCHECK(!last_selected_section_sequence.empty());

  std::set<int64_t> last_selected_section_ids;
  if (is_waypoint_assist_invoked) {
    DCHECK(!last_selected_lane_sequence.empty());
    for (const auto* lane : last_selected_lane_sequence) {
      if (lane == nullptr) {
        continue;
      }
      const int64_t section_id = lane->section()->id();
      if (last_selected_section_ids.count(section_id) == 0) {
        last_selected_section_ids.insert(section_id);
      }
    }
  } else {
    double accumulated_dist = 0.0;
    for (const pnc_map::Section* section : last_selected_section_sequence) {
      if (accumulated_dist >= kMaxDistanceToGetNearSectionFromEgoInMeter) {
        // If the section is too faraway from ego along the last selected
        // section sequence, it is no necessary. And if we keep them, if there
        // is a cycle in physical in the section sequence, it will leave some
        // confuse to the next step.
        break;
      }
      last_selected_section_ids.insert(section->id());
      if (section->id() != last_selected_section_sequence.front()->id()) {
        // We assume ego is in the first section or at the edge of the first and
        // second section. If ego is at the end of first section and the first
        // section is very long, it will mistake to filter some sections. So we
        // will not accumulate the length of the first section.
        accumulated_dist += section->GetMinLength();
      }
    }
  }

  std::vector<const pnc_map::Lane*>
      near_lanes_on_last_selected_section_sequence;
  near_lanes_on_last_selected_section_sequence.reserve(near_lanes.size());
  for (const pnc_map::Lane* near_lane : near_lanes) {
    if (last_selected_section_ids.find(near_lane->section()->id()) !=
        last_selected_section_ids.end()) {
      near_lanes_on_last_selected_section_sequence.push_back(near_lane);
    }
  }

  if (near_lanes_on_last_selected_section_sequence.empty()) {
    return nullptr;
  }

  // Find the closest lane.
  // TODO(yuzehao): The logic of getting closest near lane in last selected
  // section sequence may lead to issues. This logic could be refined for better
  // reliability.
  return DCHECK_NOTNULL(pnc_map::GetClosestLaneInRange(
      near_lanes_on_last_selected_section_sequence, robot_center,
      kMaxDistanceToGetClosestNearLaneFromEgoInMeter));
}

// Returns the closest near lane on the last selected section sequence if it
// also overlaps with ego pose. Otherwise, returns near lanes.
std::vector<const pnc_map::Lane*> GetCurrentLanesFromNearLanes(
    const math::geometry::OrientedBox2d& robot_bounding_box,
    const std::vector<const pnc_map::Lane*>& near_lanes,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const pnc_map::Section*>& last_selected_section_sequence,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    double ego_speed, bool is_new_trip, bool is_waypoint_assist_invoked,
    const GlobalRouteSolution& global_route_solution) {
  std::string debug_info;
  const bool is_in_junction =
      std::any_of(physical_lanes.begin(), physical_lanes.end(),
                  [](const pnc_map::Lane* physical_lane) {
                    return physical_lane->IsInJunction();
                  });
  // When it is a new trip, we use lanes in cost map. If ego is in junction and
  // has multiple near lanes, we need check if is in high speed.
  const bool use_lanes_in_cost_map =
      is_new_trip && ((is_in_junction && near_lanes.size() > 1)
                          ? ego_speed < kStaticEgoSpeedInMps
                          : true);
  if (last_selected_section_sequence.empty() || use_lanes_in_cost_map) {
    // If there is no section sequence from last cycle, use near lanes that are
    // in global route cost map if any is found. otherwise, we should make sure
    // that near lanes in cost map are in last selected section sequence.
    std::vector<const pnc_map::Lane*> near_lanes_in_cost_map;
    near_lanes_in_cost_map.reserve(near_lanes.size());
    for (const pnc_map::Lane* near_lane : near_lanes) {
      CHECK(near_lane);
      if (global_route_solution.GetCostToDestination(
              *near_lane, global_route_solution.waypoint_count()) != nullptr) {
        if (!last_selected_section_sequence.empty() &&
            std::none_of(last_selected_section_sequence.begin(),
                         last_selected_section_sequence.end(),
                         [near_lane](const pnc_map::Section* section) {
                           return section != nullptr &&
                                  section->id() == near_lane->section()->id();
                         })) {
          continue;
        }
        near_lanes_in_cost_map.push_back(near_lane);
      }
    }
    if (near_lanes_in_cost_map.empty()) {
      debug_info = "Near lanes are not in cost map.";
      rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchNotInCostMap>(
          debug_info);
      LOG(ERROR) << "There is no near lane in global route cost map.";
      return near_lanes;
    }

    return near_lanes_in_cost_map;
  }

  const math::geometry::Point2d robot_center(robot_bounding_box.center().x(),
                                             robot_bounding_box.center().y());
  // Get current lane from last selected section sequence.
  const pnc_map::Lane* current_lane_on_last_selected_section_sequence =
      GetCurrentLaneOnLastSelectedSectionSequence(
          robot_center, near_lanes, last_selected_section_sequence,
          last_selected_lane_sequence, is_waypoint_assist_invoked);

  if (current_lane_on_last_selected_section_sequence == nullptr) {
    LOG(ERROR)
        << "There is no near lane on the last selected section sequence.";
    return {};
  }

  std::vector<const pnc_map::Lane*>
      ego_overlapped_lanes_on_last_selected_section_sequence =
          GetOverlapDrivableLanes(
              {current_lane_on_last_selected_section_sequence},
              robot_bounding_box);

  if (ego_overlapped_lanes_on_last_selected_section_sequence.empty()) {
    // If |current_lane_on_last_selected_section_sequence| is not overlapped
    // with pose. Here are some cases:
    // 1. Ego is conducting a super nudge.
    // 2. Ego is conducting a pull out and ego pose is not in drivable lane.
    // 3. We have a short-cut regional path while ego is still moving forward.
    // For all these cases, for safety reasons, we should return all
    // |near_lanes|. Later on, it will be selected again by checking drivable
    // lanes, and overlapped lanes.
    return near_lanes;
  }

  return {current_lane_on_last_selected_section_sequence};
}

// Returns all drivable lanes from same sections with the current lanes.
std::vector<const pnc_map::Lane*> GetDrivableLanesFromCurrentLanesSections(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& current_lanes) {
  const std::vector<const pnc_map::Section*> current_sections =
      GetSectionsFromLanes(current_lanes, /*prevent_loop=*/false);

  std::vector<const pnc_map::Lane*> drivable_lanes;
  drivable_lanes.reserve(current_sections.size() *
                         kMaximumNumOfLanesInASection);
  for (const pnc_map::Section* section : current_sections) {
    for (const pnc_map::Lane* lane : section->lanes()) {
      if (drivable_lane_reasoner.IsLaneRobotDrivable(
              lane, /*check_local_hold=*/true, /*check_edge_filter=*/false)) {
        drivable_lanes.push_back(lane);
      }
    }
  }

  return drivable_lanes;
}

// Gets the left most drivable lane.
const pnc_map::Lane* GetLeftMostDrivableLane(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pnc_map::Section& section) {
  auto iter = std::find_if(
      section.lanes().begin(), section.lanes().end(),
      [&drivable_lane_reasoner](const pnc_map::Lane* lane) {
        return drivable_lane_reasoner.IsLaneRobotDrivable(
            lane, /*check_local_hold=*/true, /*check_edge_filter=*/false);
      });

  return iter == section.lanes().end() ? nullptr : *iter;
}

// Gets the right most drivable lane.
const pnc_map::Lane* GetRightMostDrivableLane(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pnc_map::Section& section) {
  auto iter = std::find_if(
      section.lanes().rbegin(), section.lanes().rend(),
      [&drivable_lane_reasoner](const pnc_map::Lane* lane) {
        return drivable_lane_reasoner.IsLaneRobotDrivable(
            lane, /*check_local_hold=*/true, /*check_edge_filter=*/false);
      });

  return iter == section.lanes().rend() ? nullptr : *iter;
}

const pnc_map::Lane* GetNonPhysicalRightMostDrivableLane(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const pnc_map::Section& section) {
  auto iter = std::find_if(
      section.lanes().rbegin(), section.lanes().rend(),
      [&physical_lanes, &drivable_lane_reasoner](const pnc_map::Lane* lane) {
        return drivable_lane_reasoner.IsLaneRobotDrivable(
                   lane, /*check_local_hold=*/true,
                   /*check_edge_filter=*/false) &&
               !IsPhysicalLane(physical_lanes, lane);
      });

  return iter == section.lanes().rend() ? nullptr : *iter;
}

// Adds a lane to |lanes| if it is not in |lanes|.
[[maybe_unused]] void AddLane(const pnc_map::Lane* input_lane,
                              std::vector<const pnc_map::Lane*>* lanes) {
  DCHECK(input_lane);
  const auto it = std::find_if(lanes->begin(), lanes->end(),
                               [input_lane](const pnc_map::Lane* lane) {
                                 return input_lane->id() == lane->id();
                               });
  // Skip if this lane exists.
  if (it != lanes->end()) {
    return;
  }
  lanes->push_back(input_lane);
}

// True if ego has entered pull out jump out sequence.
bool HasEnteredPullOutJumpOutSequence(
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const ::google::protobuf::RepeatedField<int64_t>&
        last_jump_out_lane_sequence) {
  if (physical_lanes.empty()) {
    return false;
  }
  for (const pnc_map::Lane* physical_lane : physical_lanes) {
    if (std::any_of(last_jump_out_lane_sequence.begin(),
                    last_jump_out_lane_sequence.end(),
                    [&physical_lane](const int64_t lane_id) {
                      return physical_lane->id() == lane_id;
                    })) {
      return true;
    }
  }

  return false;
}

// Gets pull out jump out current lane from |section|. Most of time we find the
// second drivable lane on the right. But if this lane is fork or merge lane and
// ego's pose is in it, we find another.
// We use the function in the first frame of pull out.
const pnc_map::Lane* GetPullOutJumpOutCurrentLaneFromSection(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const pnc_map::Section& section) {
  const pnc_map::Lane* rightmost_drivable_lane =
      GetRightMostDrivableLane(drivable_lane_reasoner, section);
  if (rightmost_drivable_lane == nullptr) {
    return nullptr;
  }
  // Is ego's pose is not in rightmost drivable lane, we do not need to add jump
  // out lane.
  if (!IsPhysicalLane(physical_lanes, rightmost_drivable_lane)) {
    return nullptr;
  }
  const pnc_map::Lane* jump_out_drivable_lane =
      GetNonPhysicalRightMostDrivableLane(drivable_lane_reasoner,
                                          physical_lanes, section);

  return jump_out_drivable_lane;
}

// Gets pull out jump out current lanes.
pnc_map::PncObjectSet<const pnc_map::Lane*> GetPullOutJumpOutCurrentLanes(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const ::google::protobuf::RepeatedField<int64_t>&
        last_jump_out_lane_sequence,
    bool last_jump_out_current_lanes_empty) {
  // If physical_lanes is empty, it means that ego starts from non-drivable
  // lanes and we should not add any jump out current lanes. Note(Huoliang):
  // Here we also check last cycle's pull out JO current lanes are empty because
  // ego may come across empty area even if it starts from drivable lane, such
  // as some bus bulbs.
  if (physical_lanes.empty() && last_jump_out_current_lanes_empty) {
    return {};
  }

  pnc_map::PncObjectSet<const pnc_map::Lane*> jump_out_current_lanes;
  const std::vector<const pnc_map::Section*> physical_sections =
      GetSectionsFromLanes(physical_lanes, /*prevent_loop=*/false);
  const std::vector<const pnc_map::Section*> sections =
      GetSectionsFromLanes(current_lanes, /*prevent_loop=*/false);
  bool is_section_overlapped = false;
  for (const pnc_map::Section* physical_section : physical_sections) {
    for (const pnc_map::Section* section : sections) {
      if (physical_section->id() == section->id()) {
        is_section_overlapped = true;
        break;
      }
    }
  }
  // Find current lanes from last jump out lane sequence and make sure it is
  // the left lane of one of physical lanes.
  if (!last_jump_out_lane_sequence.empty()) {
    for (const pnc_map::Section* section : sections) {
      for (const pnc_map::Lane* lane : section->lanes()) {
        if (std::any_of(last_jump_out_lane_sequence.begin(),
                        last_jump_out_lane_sequence.end(),
                        [&lane](const int64_t lane_id) {
                          return lane_id == lane->id();
                        }) &&
            (!is_section_overlapped ||
             std::any_of(physical_lanes.begin(), physical_lanes.end(),
                         [&lane](const pnc_map::Lane* physical_lane) {
                           return physical_lane->left_lane() != nullptr &&
                                  physical_lane->left_lane()->id() ==
                                      lane->id();
                         }))) {
          jump_out_current_lanes.emplace(lane);
        }
      }
    }
  }

  // Find current lanes from section if last_jump_out_lane_sequence is empty or
  // we can not find current lane from it.
  if (jump_out_current_lanes.empty()) {
    for (const pnc_map::Section* section : sections) {
      const pnc_map::Lane* jump_out_current_lane_from_section =
          GetPullOutJumpOutCurrentLaneFromSection(drivable_lane_reasoner,
                                                  physical_lanes, *section);
      if (jump_out_current_lane_from_section == nullptr) {
        continue;
      }
      jump_out_current_lanes.emplace(jump_out_current_lane_from_section);
    }
  }

  return jump_out_current_lanes;
}

// Returns true if ego is in solid lane marking range before a junction.
// |dist_to_solid_lane_marking_before_junction| is the distance from current
// lane's start to the solid lane marking. |ego_arc_length| is the ego's
// arc_length in current lane.
bool IsInSolidLaneMarkingRangeBeforeJunction(
    const pnc_map::Lane* lane,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        lane_to_junction_info_map,
    double ego_arc_length, bool to_left) {
  if (!lane || lane_to_junction_info_map.empty()) {
    return false;
  }
  const auto it = lane_to_junction_info_map.find(lane->id());
  if (it == lane_to_junction_info_map.end()) {
    return false;
  }
  const double dist_to_solid_lane_marking_before_junction =
      to_left ? it->second.dist_to_left_solid_lane_marking_before_junction()
              : it->second.dist_to_right_solid_lane_marking_before_junction();
  if (dist_to_solid_lane_marking_before_junction ==
      std::numeric_limits<double>::max()) {
    return false;
  }

  return dist_to_solid_lane_marking_before_junction <= ego_arc_length;
}

// Returns true if ego has entered jump out sequence or we should not generate
// jump out sequence any more in this trip.
bool ShouldTerminatePullOutJumpOutSequence(
    const GlobalRouteSolution& global_route_solution,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const pb::PullOutJumpOutSeed& pull_out_jump_out_seed,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    bool is_new_trip, bool is_selecting_pull_out_jump_out_sequence,
    bool should_generate_pull_out_jump_out_sequence) {
  // Ego has entered pull out jump out sequence in last cycle.
  if (pull_out_jump_out_seed.has_entered_jump_out_sequence()) {
    return false;
  }
  const ::google::protobuf::RepeatedField<int64_t>&
      last_jump_out_lane_sequence =
          pull_out_jump_out_seed.last_jump_out_lane_sequence();
  // Ego has entered pull out jump out sequence in this cycle.
  if (HasEnteredPullOutJumpOutSequence(physical_lanes,
                                       last_jump_out_lane_sequence)) {
    return true;
  }
  // Ego's position is not ready for pull out. We should make sure that ego has
  // valid start position because planning node may not receive new route
  // solution for a new trip.
  if (!should_generate_pull_out_jump_out_sequence &&
      global_route_solution.IsEgoStartFromValidPosition()) {
    return true;
  }
  // Ego does not select the jump out sequence and is in the solid lane marking
  // range. This is to avoid ego crossing solid lane marking using pull out jump
  // out sequence before a junction. Note that if it is a new trip, we should
  // not check the last lane to junction info map.
  if (!is_new_trip && !is_selecting_pull_out_jump_out_sequence &&
      std::any_of(
          current_lane_candidates.begin(), current_lane_candidates.end(),
          [&last_jump_out_lane_sequence, &last_lane_to_junction_info_map](
              const lane_candidate::CurrentLaneCandidate& candidate) {
            // If ego chooses JO sequence and has not entered it, the
            // |current_lane_candidates| may include the JO current lane.
            return std::none_of(last_jump_out_lane_sequence.begin(),
                                last_jump_out_lane_sequence.end(),
                                [&candidate](const int64_t lane_id) {
                                  return lane_id == candidate.lane()->id();
                                }) &&
                   IsInSolidLaneMarkingRangeBeforeJunction(
                       candidate.lane(), last_lane_to_junction_info_map,
                       candidate.position_arclength_from_lane_start(),
                       /*to_left=*/true);
          })) {
    return true;
  }

  return false;
}

// Gets the jump out lane from the left neighbor section. If a section shares
// the same successor section with current section, and the section is at the
// left hand of current section, we say it is the left neighbor section of
// current section. 1) We will try to get the rightmost lane in the left
// neighbor section. 2) And if the rightmost lane in left neighbor section is
// not valid, we can try get the drivable successor's left lane.
// [Note]: In this function, we will implement the section solution above first.
const pnc_map::Lane* FindJumpOutCurrentLaneFromLeftNeighborSection(
    const pnc_map::Lane* current_lane,
    const GlobalRouteSolution& global_route_solution,
    const std::set<int64_t>& regional_section_vertex_ids) {
  if (!current_lane) {
    return nullptr;
  }
  if (current_lane->GetDrivableSuccessors().size() != 1 ||
      current_lane->section()->successors().size() != 1) {
    // We just deal with the situation that current lane only has

    // one successor.
    return nullptr;
  }
  const pnc_map::Section* successor_section =
      current_lane->section()->successors().front();

  size_t min_successor_index_of_current_lane =
      std::numeric_limits<size_t>::max();
  for (size_t lane_idx = 0; lane_idx < successor_section->lanes().size();
       ++lane_idx) {
    if (current_lane->IsSuccessor(*successor_section->lanes().at(lane_idx))) {
      min_successor_index_of_current_lane =
          std::min(min_successor_index_of_current_lane, lane_idx);
    }
  }
  if (min_successor_index_of_current_lane > 0) {
    const pnc_map::Lane* left_lane_of_successor =
        successor_section->lanes()
            .at(min_successor_index_of_current_lane)
            ->left_lane();
    if (left_lane_of_successor != nullptr &&
        global_route_solution.GetCostToDestination(
            *left_lane_of_successor, global_route_solution.waypoint_count()) !=
            nullptr &&
        regional_section_vertex_ids.find(
            left_lane_of_successor->section()->id()) !=
            regional_section_vertex_ids.end()) {
      return left_lane_of_successor;
    }
  }
  return nullptr;
}

// Returns true if ego is in any one of the lane segments.
bool IsEgoInLaneSegments(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<common::LaneSegment>& lane_segments) {
  for (const auto& segment : lane_segments) {
    const auto& iter =
        near_lane_candidates_map.find(DCHECK_NOTNULL(segment.lane)->id());
    if (iter == near_lane_candidates_map.end()) {
      continue;
    }
    if (!math::IsInRange(iter->second.position_arclength_from_lane_start(),
                         segment.arc_length_range_m.start_pos(),
                         segment.arc_length_range_m.end_pos())) {
      continue;
    }
    if (math::geometry::Intersects(segment.lane->border(),
                                   robot_state_snapshot.bounding_box())) {
      return true;
    }
  }
  return false;
}

// Trims these lane segments who are before ego in backward direction.
std::vector<common::LaneSegment> TrimOutLaneSegmentsAfterEgo(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<common::LaneSegment>& lane_segments) {
  if (lane_segments.empty() || near_lane_candidates_map.empty()) {
    return {};
  }
  std::vector<common::LaneSegment> trimmed_lane_segments;
  trimmed_lane_segments.reserve(lane_segments.size());
  bool meet_current_lane = false;
  for (const auto& segment : lane_segments) {
    if (!segment.lane) {
      continue;
    }
    if (!meet_current_lane) {
      if (!math::geometry::Intersects(segment.lane->border(),
                                      robot_state_snapshot.bounding_box())) {
        continue;
      }
      // First meet the lane where ego is in.
      meet_current_lane = true;
      const auto& iter =
          near_lane_candidates_map.find(DCHECK_NOTNULL(segment.lane)->id());
      if (iter == near_lane_candidates_map.end()) {
        // If we can not decide whether it is after ego, prefer to regard it is
        // not after ego.
        trimmed_lane_segments.emplace_back(
            segment.lane, segment.arc_length_range_m.start_pos(),
            segment.arc_length_range_m.end_pos());
        continue;
      }
      const auto& current_lane_candidate = iter->second;
      const double position_arclength_from_lane_start =
          current_lane_candidate.position_arclength_from_lane_start();
      const double range_start =
          std::max(position_arclength_from_lane_start,
                   segment.arc_length_range_m.start_pos());
      const double range_end = segment.arc_length_range_m.end_pos();
      if (range_start <= range_end) {
        trimmed_lane_segments.emplace_back(segment.lane, range_start,
                                           range_end);
      }
      continue;
    }
    // Add these lane after ego.
    trimmed_lane_segments.emplace_back(segment.lane,
                                       segment.arc_length_range_m.start_pos(),
                                       segment.arc_length_range_m.end_pos());
  }
  return trimmed_lane_segments;
}

// Returns the directed relax lane segments which ego is in it.
lane_selection::DirectedRelaxLaneSegments GetDirectedRelaxLaneSegments(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<pb::LaneSegment>& stuck_lane_segments,
    const pb::JumpOutParam& jump_out_param) {
  if (!jump_out_param.use_jump_out_sequence() ||
      (stuck_lane_segments.empty() &&
       jump_out_param.staged_relax_lane_segments_sequence().empty())) {
    return lane_selection::DirectedRelaxLaneSegments();
  }
  lane_selection::DirectedRelaxLaneSegments directed_relax_lane_segments;

#pragma GCC diagnostic push
  // relax_direction is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
  if (jump_out_param.relax_direction() !=
      hdmap::RelaxDirection::kInvalidDirection) {
    // It commonly happen in simulation for a old bag.
    common::ConvertPBToLaneSegments(
        joint_pnc_map_service, stuck_lane_segments,
        &directed_relax_lane_segments.lane_segments);
    directed_relax_lane_segments.relax_direction =
        jump_out_param.relax_direction();
    return directed_relax_lane_segments;
  }
#pragma GCC diagnostic pop

  const auto& staged_relax_lane_segments_sequence =
      jump_out_param.staged_relax_lane_segments_sequence();
  for (const auto& directed_segments : staged_relax_lane_segments_sequence) {
    std::vector<common::LaneSegment> lane_segments;
    common::ConvertPBToLaneSegments(joint_pnc_map_service,
                                    directed_segments.lane_segments(),
                                    &lane_segments);

    const bool is_ego_in_lane_segments = IsEgoInLaneSegments(
        near_lane_candidates_map, robot_state_snapshot, lane_segments);
    if (is_ego_in_lane_segments) {
      // If ego is in the lane segments of one stage, we will only do a jump
      // out from the lane segments to the specific direction and ignore
      // others stages.
      directed_relax_lane_segments.lane_segments = lane_segments;
      directed_relax_lane_segments.relax_direction =
          directed_segments.relax_direction();
      return directed_relax_lane_segments;
    }
  }
  return lane_selection::DirectedRelaxLaneSegments();
}

// Returns true if ego is in area to relax lane direction.
// TODO(zhanshushi): Please remove this after operation.
bool IsInPatchAreaToAllowRelaxLaneDirection(const pnc_map::Lane* curr_lane) {
  const std::set<hdmap::SectionId> patch_section_ids = {69188, 69189, 69199};
  return curr_lane != nullptr &&
         patch_section_ids.find(curr_lane->section()->id()) !=
             patch_section_ids.end();
}

// Returns true if ego is in area to relax lane direction.
bool IsEgoInPatchArea(const std::vector<const pnc_map::Lane*>& physical_lanes) {
  return std::any_of(physical_lanes.begin(), physical_lanes.end(),
                     [](const pnc_map::Lane* lane) {
                       return IsInPatchAreaToAllowRelaxLaneDirection(lane);
                     });
}

// Returns a set of jump out current lanes for stuck avoidance.
// The parameter |normal_current_lanes| is these candidates who are determined
// by ego pose.
pnc_map::PncObjectSet<const pnc_map::Lane*> GetStuckAvoidanceJumpOutLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const pnc_map::Lane*>& current_lanes) {
  if ((stuck_avoidance_param.stuck_lane_segments.empty() &&
       stuck_avoidance_param.jump_out_param
           .staged_relax_lane_segments_sequence()
           .empty()) ||
      !stuck_avoidance_param.jump_out_param.use_jump_out_sequence() ||
      !stuck_avoidance_param.should_execute) {
    // 1) If not in stuck avoidance mode or do not need to add jump out lane
    // lane sequence, we do not add jump out current lane for stuck avoidance.
    // 2) If ego in stuck avoidance mode, but we do not decide to execute, it
    // means we are waiting for confirming the scenario, so we do nothing
    // right now.
    // 3) If ego is in pull out, we need to skip it.
    return {};
  }

  const auto& jump_out_param = stuck_avoidance_param.jump_out_param;
  const lane_selection::DirectedRelaxLaneSegments directed_relax_lane_segments =
      GetDirectedRelaxLaneSegments(
          joint_pnc_map_service, near_lane_candidates_map, robot_state_snapshot,
          stuck_avoidance_param.stuck_lane_segments, jump_out_param);
  if (directed_relax_lane_segments.relax_direction ==
      hdmap::RelaxDirection::kInvalidDirection) {
    return {};
  }
  std::set<int64_t> regional_section_vertex_ids;
  for (const pnc_map::SectionVertex& vertex : regional_section_vertices) {
    regional_section_vertex_ids.insert(vertex.section->id());
  }

  pnc_map::PncObjectSet<const pnc_map::Lane*> stuck_avoidance_jump_out_lanes;
  const std::vector<common::LaneSegment>& stuck_lane_segments =
      directed_relax_lane_segments.lane_segments;
  const auto& blocked_marking_segments =
      lane_blockage_detector_seed.blocked_lane_marking_seed();
  const std::vector<common::LaneSegment> lane_segments_to_check_hb =
      IsEgoInPatchArea(physical_lanes)
          ? TrimOutLaneSegmentsAfterEgo(near_lane_candidates_map,
                                        robot_state_snapshot,
                                        stuck_lane_segments)
          : stuck_lane_segments;
  const bool need_left = (directed_relax_lane_segments.relax_direction ==
                              hdmap::RelaxDirection::kToLeft ||
                          directed_relax_lane_segments.relax_direction ==
                              hdmap::RelaxDirection::kToBoth) &&
                         !lane_selection::HasHardBoundaryInSegmentSequence(
                             joint_pnc_map_service, lane_segments_to_check_hb,
                             /*to_left=*/true) &&
                         !common::HasBlockedLaneMarkingInSegmentSequence(
                             blocked_marking_segments,
                             lane_segments_to_check_hb, /*to_left=*/true);
  const bool need_right = (directed_relax_lane_segments.relax_direction ==
                               hdmap::RelaxDirection::kToRight ||
                           directed_relax_lane_segments.relax_direction ==
                               hdmap::RelaxDirection::kToBoth) &&
                          !lane_selection::HasHardBoundaryInSegmentSequence(
                              joint_pnc_map_service, lane_segments_to_check_hb,
                              /*to_left=*/false) &&
                          !common::HasBlockedLaneMarkingInSegmentSequence(
                              blocked_marking_segments,
                              lane_segments_to_check_hb, /*to_left=*/false);

  std::set<int64_t> processed_lane_ids;
  for (const auto& stuck_lane_segment : stuck_lane_segments) {
    const pnc_map::Lane* stuck_lane = stuck_lane_segment.lane;
    if (stuck_lane == nullptr ||
        !std::any_of(current_lanes.begin(), current_lanes.end(),
                     [stuck_lane](const pnc_map::Lane* current_lane) {
                       return current_lane->id() == stuck_lane->id();
                     }) ||
        processed_lane_ids.find(stuck_lane->id()) != processed_lane_ids.end()) {
      // If a stuck lane is not in current lanes, we regard it as an invalid
      // one. As a result of doing this, the candidates who will be added by
      // this function will be in the same sections of the normal ones. And
      // after the processing of 'GetSuccessorRemovedLanes', they will keep in
      // the same section.
      continue;
    }
    processed_lane_ids.insert(stuck_lane->id());

    // 1- Find in current section.
    // If ego is in a normal lane, try to just find the left and right neighbor
    // as the jump out lane.
    bool find_in_current_section_succ = false;
    if (need_left) {
      // Find left adjacent neighbor lane.
      const pnc_map::Lane* left_lane = stuck_lane->adjacent_left_lane();
      // If stuck lane is a fork lane, we allow to get the fork neighbor lane
      // which with same predecessor.
      if (left_lane == nullptr && stuck_lane->routing_vertex().is_fork_lane() &&
          stuck_lane->left_lane() != nullptr &&
          stuck_lane->IsBrother(
              *stuck_lane->left_lane(),
              /*relation_type=*/pnc_map::BrotherLane::RelationType::kDiverge,
              /*is_drivable_only=*/true)) {
        left_lane = stuck_lane->left_lane();
      }
      if (left_lane && !IsPhysicalLane(physical_lanes, left_lane)) {
        stuck_avoidance_jump_out_lanes.insert(left_lane);
        find_in_current_section_succ = true;
      }
    }
    if (need_right) {
      // Find right adjacent neighbor lane.
      const pnc_map::Lane* right_lane = stuck_lane->adjacent_right_lane();
      // If stuck lane is a fork lane, we allow to get the fork neighbor lane
      // which with same predecessor.
      if (right_lane == nullptr &&
          stuck_lane->routing_vertex().is_fork_lane() &&
          stuck_lane->right_lane() != nullptr &&
          stuck_lane->IsBrother(
              *stuck_lane->right_lane(),
              /*relation_type=*/pnc_map::BrotherLane::RelationType::kDiverge,
              /*is_drivable_only=*/true)) {
        right_lane = stuck_lane->right_lane();
      }
      if (right_lane && !IsPhysicalLane(physical_lanes, right_lane)) {
        stuck_avoidance_jump_out_lanes.insert(right_lane);
        find_in_current_section_succ = true;
      }
    }

    // 2- Find in the successor or neighbor section.
    if (kEnableJumpOutToNeighborRoad && !find_in_current_section_succ) {
      // If we can not find a lane in current section, try to find in the
      // neighbor section.
      if (need_left) {
        const pnc_map::Lane* lane_in_left_section =
            FindJumpOutCurrentLaneFromLeftNeighborSection(
                stuck_lane, global_route_solution, regional_section_vertex_ids);
        if (lane_in_left_section &&
            !IsPhysicalLane(physical_lanes, lane_in_left_section)) {
          stuck_avoidance_jump_out_lanes.insert(lane_in_left_section);
        }
      }
    }
  }
  return stuck_avoidance_jump_out_lanes;
}

// Gets current drivable lanes.
// For finding current drivable lanes, the steps are:
// (1) Gets lanes from near lanes on last selected section sequence, then
//  returns the closest lane of these lanes to the robot center as the current
//  lane. If the last selected section sequence is empty, returns all near
//  lanes as the current lanes.
// (2) Gets drivable lanes from the current lanes of step1. All drivable lanes
// are from the same section with the current lanes.
// (3) Uses robot bounding box to get overlap lanes from drivable lanes.
//  these overlap drivable lanes are regarded as current drivable lanes to
// search lane graph.
// (4) If can not get overlap drivable lanes on step3, we find the closest
// lanes which the distance to the robot center is not greater than
// kLaneSearchingRadiusInMeter(5.0m) from drivable lanes of step2. These
// closest drivable lanes are regarded as current drivable lanes to search
// lane graph.
std::vector<lane_candidate::CurrentLaneCandidate> GetCurrentDrivableLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const CurrentLaneCostEstimator& current_lane_cost_estimator,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<const pnc_map::Lane*>& near_lanes,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Section*>& last_cycle_section_sequence,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    bool is_new_trip, bool should_generate_pull_out_jump_out_sequence,
    bool is_waypoint_assist_invoked, bool is_in_autonomous_mode,
    bool is_last_cycle_lane_change_finished,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const pb::EgoReachTargetLaneSequenceState::HasReached&
        has_ego_reached_target_lane_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed) {
  DCHECK(!near_lanes.empty());

  // Get current lanes from near lanes.
  const math::geometry::OrientedBox2d& robot_bounding_box =
      robot_state_snapshot.bounding_box();
  const math::geometry::Point2d robot_center(robot_bounding_box.center().x(),
                                             robot_bounding_box.center().y());
  const math::geometry::Point2d ego_position(robot_state_snapshot.x(),
                                             robot_state_snapshot.y());

  const std::vector<const pnc_map::Lane*> current_lanes_from_near_lanes =
      GetCurrentLanesFromNearLanes(
          robot_bounding_box, near_lanes, physical_lanes,
          last_cycle_section_sequence, last_selected_lane_sequence,
          robot_state_snapshot.speed(), is_new_trip, is_waypoint_assist_invoked,
          global_route_solution);

  std::string debug_info;
  if (current_lanes_from_near_lanes.empty()) {
    debug_info = "Failed to search current lanes from near lanes.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchEmptyFromNearLane>(
        debug_info);
    return {};
  }

  // First collect drivable lanes from current lanes.
  const std::vector<const pnc_map::Lane*> drivable_lanes =
      GetDrivableLanesFromCurrentLanesSections(drivable_lane_reasoner,
                                               current_lanes_from_near_lanes);

  if (drivable_lanes.empty()) {
    debug_info = "Failed to search drivable lanes from current lane sections.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchEmptyDrivableLane>(
        debug_info);
    return {};
  }

  // Then get overlap drivable lanes. We also treat these overlap drivable
  // lanes as current lane.
  const double ego_area = math::geometry::Area(robot_bounding_box);
  std::vector<lane_candidate::CurrentLaneCandidate>
      current_drivable_lane_candidates =
          GetOverlapDrivableLaneCandidatesWithMinIntersection(
              near_lane_candidates_map, ego_position, routing_cost_generator,
              current_lane_cost_estimator, drivable_lanes, robot_bounding_box,
              ego_area,
              /*minimum_overlap_area_percentage=*/
              kMinimumIntersectionPercentage);

  if (current_drivable_lane_candidates.empty()) {
    debug_info = "Failed to search overlapped lanes.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchEmptyOverlappedLane>(
        debug_info);
  }

  const bool is_in_normal_LF_maneuver = IsEgoInNormalLFOrLCManeuver(
      last_selected_lane_sequence_candidate, robot_state_snapshot.speed(),
      is_new_trip, is_in_autonomous_mode, is_last_cycle_lane_change_finished,
      /*is_lane_follow=*/true);
  const bool is_in_normal_LC_maneuver = IsEgoInNormalLFOrLCManeuver(
      last_selected_lane_sequence_candidate, robot_state_snapshot.speed(),
      is_new_trip, is_in_autonomous_mode, is_last_cycle_lane_change_finished,
      /*is_lane_follow=*/false);

  if (is_in_normal_LF_maneuver || is_in_normal_LC_maneuver) {
    FillLaneOnLastSelectedLaneSequenceIfMissed(
        near_lane_candidates_map, ego_position, routing_cost_generator,
        current_lane_cost_estimator, drivable_lanes, is_in_autonomous_mode,
        &current_drivable_lane_candidates);
  }

  std::vector<lane_candidate::CurrentLaneCandidate> current_lane_candidates =
      FilterCurrentLanesInSomeDetermineScenarios(
          joint_pnc_map_service, near_lane_candidates_map, physical_lanes,
          current_drivable_lane_candidates, is_in_normal_LF_maneuver,
          is_last_cycle_lane_change_finished,
          has_ego_reached_target_lane_sequence);

  if (!current_lane_candidates.empty()) {
    const std::vector<const pnc_map::Lane*> current_lanes =
        GetCurrentLanesFromCandidates(current_lane_candidates);
    // Set has_entered_jump_out_sequence to true when we should terminate
    // generation of pull out jumo out sequence.
    if (ShouldTerminatePullOutJumpOutSequence(
            global_route_solution, current_lane_candidates, physical_lanes,
            *mutable_pull_out_jump_out_seed, last_lane_to_junction_info_map,
            is_new_trip,
            last_selected_lane_sequence_candidate != nullptr &&
                lane_selection::IsPullOutJumpOutLaneSequence(
                    *last_selected_lane_sequence_candidate),
            should_generate_pull_out_jump_out_sequence)) {
      mutable_pull_out_jump_out_seed->set_has_entered_jump_out_sequence(true);
    }
    // Note(Huoliang): |should_generate_pull_out_jump_out_sequence| is the flag
    // in world model to tell that ego position meet the requirement in a trip
    // to generate pull out jump out sequence. |has_entered_jump_out_sequence|
    // is the flag to tell if ego has entered the JO sequence. When updating
    // current lanes, we know before regional path and lane sequence that ego
    // has entered JO sequence and we should not add JO current lane in this
    // cycle. And for old bags, which do not have valid ride start pose, the
    // |has_entered_jump_out_sequence| will not have the chance to be true from
    // false.
    const bool should_add_pull_out_jump_out_current_lanes =
        should_generate_pull_out_jump_out_sequence &&
        !mutable_pull_out_jump_out_seed->has_entered_jump_out_sequence();
    const pnc_map::PncObjectSet<const pnc_map::Lane*> jump_out_current_lanes =
        should_add_pull_out_jump_out_current_lanes
            ? GetPullOutJumpOutCurrentLanes(
                  drivable_lane_reasoner, physical_lanes, current_lanes,
                  mutable_pull_out_jump_out_seed->last_jump_out_lane_sequence(),
                  mutable_pull_out_jump_out_seed->last_jump_out_current_lanes()
                      .empty())
            : GetStuckAvoidanceJumpOutLanes(
                  joint_pnc_map_service, global_route_solution,
                  regional_section_vertices, stuck_avoidance_param,
                  lane_blockage_detector_seed, near_lane_candidates_map,
                  robot_state_snapshot, physical_lanes, current_lanes);
    std::set<LightCurrentLaneCandidate> light_candidates;
    for (const auto* lane : jump_out_current_lanes) {
      light_candidates.emplace(lane,
                               /*jump_out_type_in=*/
                               should_add_pull_out_jump_out_current_lanes
                                   ? pb::JumpOutType::PULL_OUT_JUMP_OUT
                                   : pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT,
                               /*prefix_ids_in=*/std::set<int32_t>{});
    }
    AddLanesIntoCurrentLaneCandidates(
        near_lane_candidates_map, light_candidates, ego_position,
        routing_cost_generator, current_lane_cost_estimator,
        &current_lane_candidates);

    // Filter out lanes that are not selected by the locator's potential
    // sections. However, lanes used for stuck avoidance jump out may not be
    // included in potential sections, so they are skipped during filtering.
    // TODO(yuzehao): Take current lane for |STUCK_AVOIDANCE_JUMP_OUT| into
    // consideration.
    std::vector<lane_candidate::CurrentLaneCandidate>
        candidates_in_potential_sections;
    candidates_in_potential_sections.reserve(current_lane_candidates.size());
    for (const auto& current_lane_candidate : current_lane_candidates) {
      if (current_lane_candidate.is_in_locator_potential_sections() ||
          current_lane_candidate.jump_out_type() ==
              pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT) {
        candidates_in_potential_sections.push_back(current_lane_candidate);
      }
    }
    if (!candidates_in_potential_sections.empty()) {
      return candidates_in_potential_sections;
    }
  }

  // If the near lanes returned by regional sections locator are bike lanes or
  // lanes with no overlap with the robot, or ego is conducting a super nudge,
  // then the current_drivable_lanes might be empty. In this case, return the
  // closest drivable lane as the current drivable lane.
  // |current_drivable_lane| is logically within the locator's potential
  // sections, as |drivable_lanes| is derived from potential sections.
  const pnc_map::Lane* current_drivable_lane = pnc_map::GetClosestLaneInRange(
      drivable_lanes, robot_center, constants::kLaneSearchingRadiusInMeter);

  if (current_drivable_lane == nullptr) {
    return {};
  }
  AddLanesIntoCurrentLaneCandidates(
      near_lane_candidates_map,
      {LightCurrentLaneCandidate(
          current_drivable_lane,
          /*jump_out_type_in=*/pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT,
          /*prefix_ids_in=*/{})},
      ego_position, routing_cost_generator, current_lane_cost_estimator,
      &current_lane_candidates);
  return current_lane_candidates;
}

// Gets the left or right most drivable lane based on the robot's location.
const pnc_map::Lane* GetLeftOrRightMostDrivableLane(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pnc_map::Section& section, const RobotStateSnapshot& robot_snapshot) {
  // Check if the robot is on the left side of the leftmost drivable lane. If
  // so, return the leftmost drivable lane.
  const pnc_map::Lane* leftmost_lane =
      GetLeftMostDrivableLane(drivable_lane_reasoner, section);
  if (leftmost_lane != nullptr) {
    const pnc_map::LaneMarking& leftmost_marking =
        *DCHECK_NOTNULL(leftmost_lane->left_marking());
    const math::pb::Side leftmost_marking_side =
        leftmost_marking.line()
            .GetProximity({robot_snapshot.x(), robot_snapshot.y()},
                          math::pb::UseExtensionFlag::kForbid)
            .side;
    if (leftmost_marking_side == math::pb::kLeft) {
      return leftmost_lane;
    }
  }

  // Check if the robot is on the right side of the rightmost drivable lane. If
  // so, return the rightmost drivable lane.
  const pnc_map::Lane* rightmost_lane =
      GetRightMostDrivableLane(drivable_lane_reasoner, section);
  if (rightmost_lane != nullptr) {
    const pnc_map::LaneMarking& rightmost_marking =
        *DCHECK_NOTNULL(rightmost_lane->right_marking());
    const math::pb::Side rightmost_marking_side =
        rightmost_marking.line()
            .GetProximity({robot_snapshot.x(), robot_snapshot.y()},
                          math::pb::UseExtensionFlag::kForbid)
            .side;
    if (rightmost_marking_side == math::pb::kRight) {
      return rightmost_lane;
    }
  }

  return nullptr;
}

// Checks if the robot's heading is align with the lane.
bool IsRobotHeadingAlignWithLane(
    const pnc_map::Lane& lane, const RobotStateSnapshot& robot_state_snapshot) {
  const double lane_heading = lane.GetLaneDirection(
      {robot_state_snapshot.x(), robot_state_snapshot.y()});
  const double robot_heading =
      math::NormalizeMinusPiToPi(robot_state_snapshot.heading());
  return std::abs(math::AngleDiff(lane_heading, robot_heading)) < M_PI_2;
}

// Gets the current lanes from the current section.
std::vector<const pnc_map::Lane*> GetCurrentLanes(
    const std::vector<const pnc_map::Lane*> last_selected_lane_sequence,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const pnc_map::Section& current_section,
    const RobotStateSnapshot& robot_state_snapshot,
    const pnc_map::PncObjectSet<const pnc_map::Section*>& allowed_section_set) {
  (void)last_selected_lane_sequence;
  // Get the drivable lanes from the current ones.
  std::vector<const pnc_map::Lane*> current_drivable_lanes;
  current_drivable_lanes.reserve(current_lane_candidates.size());
  for (const auto& current_lane_candidate : current_lane_candidates) {
    if (current_lane_candidate.lane() != nullptr &&
        drivable_lane_reasoner.IsLaneRobotDrivable(
            current_lane_candidate.lane(), /*check_local_hold=*/true,
            /*check_edge_filter=*/false) &&
        (current_lane_candidate.lane()->section()->id() ==
             current_section.id() ||
         (current_lane_candidate.IsPullOutJumpOut() &&
          IsLaneInSectionSet(current_lane_candidate.lane(),
                             allowed_section_set)))) {
      // [Note]: We additionally push the current lane for stuck avoidance who
      // is not at the start section of regional path, but we need it is in
      // regional path. We can expect this happen when ego is just at the
      // connection point of two section. And we also can expect the current
      // lane for stuck avoidance is in one of the sections where ego overlap
      // with.
      current_drivable_lanes.push_back(current_lane_candidate.lane());
    }
  }

  // Return the left or right-most lane if the current_drivable_lanes is
  // empty, which usually happens when the robot is maneuvering pull over or
  // pull out, so return the leftmost or rightmost lane for that purpose.
  if (current_drivable_lanes.empty()) {
    const pnc_map::Lane* closest_drivable_lane = GetLeftOrRightMostDrivableLane(
        drivable_lane_reasoner, current_section, robot_state_snapshot);
    if (closest_drivable_lane != nullptr &&
        IsRobotHeadingAlignWithLane(*closest_drivable_lane,
                                    robot_state_snapshot)) {
      current_drivable_lanes.push_back(closest_drivable_lane);
    }
  }

  return current_drivable_lanes;
}

// Gets near lane candidate from the given |lane|, and some important fields
// are default to false and to be filled later.
lane_candidate::NearLaneCandidate GetNearLaneCandidate(
    const pnc_map::Lane* lane, const voy::Pose& pose,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<const pnc_map::Section*>&
        locator_selected_potential_sections,
    const std::vector<const pnc_map::Section*>& extend_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Lane*>& last_cycle_target_lane_sequence) {
  const math::geometry::Point2d pose_point(pose.x(), pose.y());
  const auto proximity = DCHECK_NOTNULL(lane)->center_line().GetProximity(
      pose_point, math::pb::UseExtensionFlag::kForbid);
  const double pose_to_lane_center_dist_in_m = proximity.dist;
  const double pose_percentage_along_lane =
      proximity.arc_length / lane->length();
  const double pose_arclength_from_lane_start = proximity.arc_length;
  const double lane_heading =
      lane->center_line().GetInterpTheta(proximity.arc_length);
  // Lane heading is in [-M_PI, M_PI], pose heading is in [0, 2 * M_PI]. The
  // heading diff should be in [0, M_PI].
  const double heading_diff = std::abs(
      math::AngleDiff(math::WrapFromMinusPiToPi(pose.yaw()), lane_heading));
  const bool is_selected_by_locator_near_lanes = std::any_of(
      locator_selected_near_lanes.begin(), locator_selected_near_lanes.end(),
      [lane](const pnc_map::Lane* selected_lane) {
        return lane->id() == DCHECK_NOTNULL(selected_lane)->id();
      });
  const auto lane_section_id = DCHECK_NOTNULL(lane->section())->id();
  const bool is_in_locator_potential_sections = std::any_of(
      locator_selected_potential_sections.begin(),
      locator_selected_potential_sections.end(),
      [&lane_section_id](const auto* section) {
        return section != nullptr && lane_section_id == section->id();
      });
  const bool is_in_extend_potential_sections = std::any_of(
      extend_potential_sections.begin(), extend_potential_sections.end(),
      [&lane_section_id](const auto* neighbor_section) {
        return neighbor_section != nullptr &&
               lane_section_id == neighbor_section->id();
      });
  const bool is_physical = math::geometry::Within(pose_point, lane->border());

  const bool is_overlapped =
      is_physical ||
      math::geometry::Intersects(lane->border(), ego_bounding_box);
  const bool is_in_cost_map =
      global_route_solution.GetCostToDestination(
          *lane, global_route_solution.waypoint_count()) != nullptr;
  const bool is_in_last_selected_lane_sequence = std::any_of(
      last_selected_lane_sequence.begin(), last_selected_lane_sequence.end(),
      [lane](const pnc_map::Lane* selected_lane) {
        return selected_lane != nullptr && lane->id() == selected_lane->id();
      });
  const bool is_in_last_cycle_target_lane_sequence = std::any_of(
      last_cycle_target_lane_sequence.begin(),
      last_cycle_target_lane_sequence.end(),
      [lane](const pnc_map::Lane* target_lane) {
        return target_lane != nullptr && lane->id() == target_lane->id();
      });

  // The hard code fields are not really false for the candidate, just not
  // calculated here.
  return {lane,
          heading_diff,
          pose_to_lane_center_dist_in_m,
          pose_arclength_from_lane_start,
          pose_percentage_along_lane,
          /*is_drivable=*/false,
          is_physical,
          is_overlapped,
          is_in_cost_map,
          is_selected_by_locator_near_lanes,
          is_in_locator_potential_sections,
          is_in_extend_potential_sections,
          /*is_in_regional_section_vertices=*/false,
          is_in_last_selected_lane_sequence,
          is_in_last_cycle_target_lane_sequence,
          /*is_in_last_selected_section_sequence=*/false};
}

// Returns near lane candidates map.
std::map<int64_t, lane_candidate::NearLaneCandidate> GetNearLaneCandidatesMap(
    const voy::Pose& pose,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<const pnc_map::Section*>&
        locator_selected_potential_sections,
    const std::vector<const pnc_map::Section*>&
        locator_selected_extend_potential_sections,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Lane*>& last_cycle_target_lane_sequence) {
  std::map<int64_t, lane_candidate::NearLaneCandidate> near_lane_candidates_map;

  std::vector<const pnc_map::Lane*> near_lane_candidate_lanes =
      joint_pnc_map_service.GetNearLanesWithPose(
          pose, constants::kLaneSearchingRadiusInMeter,
          /*max_heading_diff=*/M_PI_2,
          /*prefer_overlapped_lanes=*/false, last_selected_lane_sequence);

  for (const pnc_map::Lane* lane : near_lane_candidate_lanes) {
    if (near_lane_candidates_map.find(DCHECK_NOTNULL(lane)->id()) !=
        near_lane_candidates_map.end()) {
      continue;
    }
    near_lane_candidates_map.try_emplace(
        lane->id(),
        GetNearLaneCandidate(
            lane, pose, ego_bounding_box, global_route_solution,
            locator_selected_near_lanes, locator_selected_potential_sections,
            locator_selected_extend_potential_sections,
            last_selected_lane_sequence, last_cycle_target_lane_sequence));
  }

  // To ensure the continuity of the current lane,
  // |locator_selected_near_lanes| are added into the
  // |near_lane_candidates_map|.
  for (const pnc_map::Lane* lane : locator_selected_near_lanes) {
    if (near_lane_candidates_map.find(DCHECK_NOTNULL(lane)->id()) !=
        near_lane_candidates_map.end()) {
      continue;
    }
    near_lane_candidates_map.try_emplace(
        lane->id(),
        GetNearLaneCandidate(
            lane, pose, ego_bounding_box, global_route_solution,
            /*locator_selected_near_lanes=*/{lane},
            locator_selected_potential_sections,
            locator_selected_extend_potential_sections,
            last_selected_lane_sequence, last_cycle_target_lane_sequence));
  }

  return near_lane_candidates_map;
}

std::string GetLaneIdsString(
    const std::vector<const pnc_map::Lane*>& current_lanes) {
  std::string lane_ids_str;
  for (const pnc_map::Lane* current_lane : current_lanes) {
    lane_ids_str += std::to_string(current_lane->id()) + ", ";
  }
  return lane_ids_str;
}

std::string GetNearLaneCandidateIdsString(
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates) {
  std::string lane_ids_str;
  for (const auto& [_, near_lane_candidate] : near_lane_candidates) {
    lane_ids_str += std::to_string(near_lane_candidate.lane()->id()) + ", ";
  }
  return lane_ids_str;
}

// Log the debug info if failed to find current lanes for all prefixes.
void LogDebugInfoForUnassociatedPrefixes(
    const std::vector<lane_selection::LaneSequencePrefixInfo>& prefixes,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  if (prefixes.empty()) {
    return;
  }

  // Find all unassociated prefix ids.
  std::set<int32_t> unassociated_prefix_ids;
  for (const auto& prefix : prefixes) {
    if (std::none_of(current_lane_candidates.begin(),
                     current_lane_candidates.end(),
                     [&prefix](const auto& candidate) {
                       return candidate.prefix_ids().find(prefix.id) !=
                              candidate.prefix_ids().end();
                     })) {
      unassociated_prefix_ids.insert(prefix.id);
    }
  }
  if (unassociated_prefix_ids.empty()) {
    return;
  }

  // Log rt-event.
  std::stringstream debug_info;
  debug_info << "Failed to find current lanes for "
             << unassociated_prefix_ids.size() << " out of " << prefixes.size()
             << " prefixes.";
  rt_event::PostRtEvent<rt_event::planner::RoutePropFailedToFindCurrentLane>(
      debug_info.str());
  // Log detailed info to glog.
  debug_info << " Unassociated prefix ids: ";
  for (const auto id : unassociated_prefix_ids) {
    debug_info << id << ", ";
  }
  LOG(INFO) << debug_info.str();
}

// Returns true if we have physical current lane candidate.
bool HasPhysicalLanes(const std::vector<lane_candidate::CurrentLaneCandidate>&
                          current_lane_candidates) {
  return std::any_of(current_lane_candidates.begin(),
                     current_lane_candidates.end(),
                     [](const lane_candidate::CurrentLaneCandidate& candidate) {
                       return candidate.is_physical();
                     });
}

// Returns true if any lane is in cost map.
bool CheckInCostMap(const GlobalRouteSolution& global_route_solution,
                    const std::vector<const pnc_map::Lane*>& lanes) {
  return std::any_of(
      lanes.begin(), lanes.end(),
      [&global_route_solution](const pnc_map::Lane* lane) {
        return lane != nullptr &&
               global_route_solution.GetCostToDestination(
                   *lane, global_route_solution.waypoint_count()) != nullptr;
      });
}

// Checks if the target lane is the first-order or section-order successor of
// any current lane candidates.
bool IsSuccessorOfCurrentLane(
    const pnc_map::Lane* lane,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  if (lane == nullptr) {
    return false;
  }
  return any_of(current_lane_candidates.begin(), current_lane_candidates.end(),
                [lane](const auto& candidate) {
                  if (candidate.lane() == nullptr) {
                    return false;
                  }
                  if (candidate.lane()->IsSuccessor(*lane)) {
                    return true;
                  }
                  // Check if target lane is one of the second-order successors
                  // of current lanes.
                  for (const auto* first_order_successor :
                       candidate.lane()->successors()) {
                    if (first_order_successor == nullptr) {
                      continue;
                    }
                    if (first_order_successor->IsSuccessor(*lane)) {
                      return true;
                    }
                  }
                  return false;
                });
}

// Returns true if we should apply backup lanes as current lanes directly.
bool ShouldApplyBackupLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates,
    const std::vector<const pnc_map::Lane*>& physical_lanes,
    const std::vector<const pnc_map::Lane*>& backup_lanes,
    const GlobalRouteSolution& global_route_solution,
    bool is_in_autonomous_mode, pb::BackupLanesDebug* backup_lanes_debug) {
  if (backup_lanes_debug == nullptr) {
    return false;
  }
  backup_lanes_debug->Clear();
  backup_lanes_debug->set_apply_backup_lane_reason(
      pb::ApplyBackupLaneReason::NOT_APPLIED);

  // Check if backup lanes are empty.
  if (backup_lanes.empty()) {
    return false;
  }
  auto* mutable_backup_lanes = backup_lanes_debug->mutable_backup_lanes();
  for (const auto* lane : backup_lanes) {
    if (lane != nullptr) {
      mutable_backup_lanes->Add(lane->id());
    }
  }

  // Check if current lane candidates are empty.
  if (current_lane_candidates.empty()) {
    backup_lanes_debug->set_apply_backup_lane_reason(
        pb::ApplyBackupLaneReason::CURRENT_LANE_CANDIDATES_EMPTY);
    return true;
  }
  auto* mutable_original_current_lanes =
      backup_lanes_debug->mutable_original_current_lane_candidates();
  for (const auto& candidate : current_lane_candidates) {
    *mutable_original_current_lanes->Add() =
        candidate.ConstructCurrentLaneDebug();
  }

  // If the current_lane_candidates has physical lanes, there is no need to
  // apply backup lanes. Meanwhile, to prevent unsafe current lane flicker
  // issues, if any backup lane is in junction,  we temporarily don't apply
  // backup lanes as well.
  if (HasPhysicalLanes(current_lane_candidates) ||
      std::any_of(
          backup_lanes.begin(), backup_lanes.end(),
          [](const pnc_map::Lane* lane) { return lane->IsInJunction(); })) {
    return false;
  }

  // If ego is in manual mode and leaves cost map, we just use backup lanes.
  if (!is_in_autonomous_mode &&
      !CheckInCostMap(global_route_solution, backup_lanes)) {
    backup_lanes_debug->set_apply_backup_lane_reason(
        pb::ApplyBackupLaneReason::MANUAL_MODE_AND_BACKUP_LANE_NOT_IN_COST_MAP);
    return true;
  }

  // If all the current lane candidates are separated with ego by hard boundary,
  // we just use backup lanes.
  if (std::all_of(
          current_lane_candidates.begin(), current_lane_candidates.end(),
          [&joint_pnc_map_service, &near_lane_candidates_map, &physical_lanes](
              const lane_candidate::CurrentLaneCandidate& candidate) {
            return IsSeparatedWithEgoByHardBoundary(joint_pnc_map_service,
                                                    near_lane_candidates_map,
                                                    physical_lanes, candidate);
          })) {
    backup_lanes_debug->set_apply_backup_lane_reason(
        pb::ApplyBackupLaneReason::HAS_HARD_BOUNDARY_WITH_PHYSICAL_LANES);
    return true;
  }

  // Check if the ego has completely passed the current lane.
  const bool has_passed_current_lane = std::all_of(
      current_lane_candidates.begin(), current_lane_candidates.end(),
      [](const lane_candidate::CurrentLaneCandidate& candidate) {
        return candidate.position_percentage_along_lane() >= 1.0 &&
               !candidate.is_overlapped();
      });

  if (has_passed_current_lane && !physical_lanes.empty()) {
    // If ego has passed the current lane, we need to check if all physical
    // lanes are successors of the current lane. This is because:
    // 1. If ego has moved into a successor lane of the current lane, it is
    // unlikely to return to the current lane, and backup lanes should be used.
    // 2. If ego has moved into a lane that is not a successor of the current
    // lane(e.g., a neighbor current lane's successor), it might be performing a
    // nudge or unstuck maneuver, and backup lanes should not be used in this
    // case.
    const bool is_in_successors = std::all_of(
        physical_lanes.begin(), physical_lanes.end(),
        [&current_lane_candidates](const pnc_map::Lane* lane) {
          return IsSuccessorOfCurrentLane(lane, current_lane_candidates);
        });
    if (is_in_successors) {
      backup_lanes_debug->set_apply_backup_lane_reason(
          pb::ApplyBackupLaneReason::DRIVE_INTO_CURRENT_LANE_SUCCESSORS);
      return true;
    }
  }

  return false;
}

// Returns true if the candidate is the lane change source lane when the last
// cycle was lane change finish.
bool IsLaneChangeSourceLaneInForkWhenLCFinish(
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const pb::LaneChangeInstance& last_lane_change_finish_instance,
    const lane_candidate::CurrentLaneCandidate& physical_candidate) {
  if (last_selected_lane_sequence_candidate == nullptr) {
    return false;
  }

  const auto& source_lane_sequence =
      last_lane_change_finish_instance.source_lane_sequence();
  // If the last cycle wasn't the lane change finish cycle, the source lane
  // sequence in |last_lane_change_finish_instance| is empty.
  if (!lane_selection::IsLaneChangeType(
          last_selected_lane_sequence_candidate->lane_sequence_type()) ||
      source_lane_sequence.empty()) {
    return false;
  }

  // Now we only consider lane change in fork, to keep the current lane  not on
  // source lane.
  if (!physical_candidate.lane()->IsForkLane()) {
    return false;
  }

  // If the physical current lane is in source lane seuqence, return true.
  return std::any_of(source_lane_sequence.begin(), source_lane_sequence.end(),
                     [&physical_candidate](const int64_t& source_lane_id) {
                       return physical_candidate.lane()->id() == source_lane_id;
                     });
}

}  // namespace

std::vector<const pnc_map::Lane*> GetCurrentLanesFromCandidates(
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  std::vector<const pnc_map::Lane*> current_lanes;
  current_lanes.reserve(current_lane_candidates.size());
  for (const auto& candidate : current_lane_candidates) {
    current_lanes.push_back(candidate.lane());
  }
  return current_lanes;
}

CurrentLaneAssociator::CurrentLaneAssociator(
    const DrivableLaneReasoner* drivable_lane_reasoner)
    : drivable_lane_reasoner_(DCHECK_NOTNULL(drivable_lane_reasoner)) {}

bool CurrentLaneAssociator::Update(
    const RobotStateSnapshot& robot_state_snapshot, const voy::Pose& pose,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<const pnc_map::Section*>&
        locator_selected_potential_sections,
    const std::vector<const pnc_map::Section*>&
        locator_selected_extend_potential_sections,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Section*>& last_cycle_section_sequence,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
        last_lane_to_junction_info_map,
    bool is_new_trip, bool should_generate_pull_out_jump_out_sequence,
    bool is_waypoint_assist_invoked, bool is_in_autonomous_mode,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const lane_selection::LaneSequenceProposalInfo& lane_sequence_proposal_info,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed,
    const pb::EgoReachTargetLaneSequenceState::HasReached&
        has_ego_reached_target_lane_sequence,
    pb::PullOutJumpOutSeed* mutable_pull_out_jump_out_seed) {
  DCHECK(!regional_section_vertices.empty());
  has_ego_reached_target_lane_sequence_ = has_ego_reached_target_lane_sequence;

  // 1. Get near lane candidates.
  const auto& target_lane_sequence_ids =
      previous_decoupled_lane_follow_maneuver_seed.lane_change_info_seed()
          .lane_change_instance()
          .target_lane_sequence();
  const auto& last_cycle_target_lane_sequence =
      joint_pnc_map_service.GetLaneSequence(std::vector<int64_t>(
          target_lane_sequence_ids.begin(), target_lane_sequence_ids.end()));
  std::vector<const pnc_map::Lane*> valid_target_lane_sequence;
  valid_target_lane_sequence.reserve(last_cycle_target_lane_sequence.size());
  for (const auto* target_lane : last_cycle_target_lane_sequence) {
    if (target_lane != nullptr) {
      valid_target_lane_sequence.push_back(target_lane);
    }
  }
  near_lane_candidates_map_ = GetNearLaneCandidatesMap(
      pose, robot_state_snapshot.bounding_box(), joint_pnc_map_service,
      global_route_solution, locator_selected_near_lanes,
      locator_selected_potential_sections,
      locator_selected_extend_potential_sections, last_selected_lane_sequence,
      valid_target_lane_sequence);

  const std::vector<const pnc_map::Lane*> physical_lanes =
      GetDrivablePhysicalLanes();

  // 2. Get current drivable lanes for regional path and replan route query.
  const bool is_last_cycle_lane_change_finished =
      previous_decoupled_lane_follow_maneuver_seed
          .last_lane_change_finish_instance()
          .ByteSizeLong() > 0;
  std::vector<lane_candidate::CurrentLaneCandidate> drivable_current_lanes =
      GetCurrentDrivableLanes(
          joint_pnc_map_service, drivable_lane_reasoner(),
          routing_cost_generator, current_lane_cost_estimator_,
          robot_state_snapshot, locator_selected_near_lanes, physical_lanes,
          near_lane_candidates_map_, global_route_solution,
          regional_section_vertices, last_cycle_section_sequence,
          last_selected_lane_sequence, last_selected_lane_sequence_candidate,
          last_lane_to_junction_info_map, is_new_trip,
          should_generate_pull_out_jump_out_sequence,
          is_waypoint_assist_invoked, is_in_autonomous_mode,
          is_last_cycle_lane_change_finished, stuck_avoidance_param,
          lane_blockage_detector_seed, has_ego_reached_target_lane_sequence_,
          mutable_pull_out_jump_out_seed);

  prefixes_info_ = GetAllLaneSequencePrefixes(
      lane_sequence_proposal_info.lane_seq_latch_signal,
      FLAGS_planning_enable_lane_sequence_proposal_for_lane_change
          ? lane_sequence_proposal_info.lane_change_latch_signal
          : std::nullopt);
  const math::geometry::Point2d ego_position(robot_state_snapshot.x(),
                                             robot_state_snapshot.y());
  // Add regional path current lanes for lane sequence proposal prefixes.
  AddPrefixAssociatedCurrentLanes(prefixes_info_, ego_position,
                                  routing_cost_generator,
                                  &drivable_current_lanes);

  const std::vector<const pnc_map::Lane*> backup_lanes =
      GetBackupLanes(joint_pnc_map_service);
  if (ShouldApplyBackupLanes(joint_pnc_map_service, near_lane_candidates_map_,
                             drivable_current_lanes, physical_lanes,
                             backup_lanes, global_route_solution,
                             is_in_autonomous_mode, &backup_lanes_debug_)) {
    drivable_current_lanes.clear();
    std::set<LightCurrentLaneCandidate> backup_current_lane_candidates;
    for (const pnc_map::Lane* backup_lane : backup_lanes) {
      if (backup_lane == nullptr) {
        continue;
      }
      backup_current_lane_candidates.insert(
          LightCurrentLaneCandidate(backup_lane));
    }

    AddLanesIntoCurrentLaneCandidates(
        near_lane_candidates_map_, backup_current_lane_candidates, ego_position,
        routing_cost_generator, current_lane_cost_estimator_,
        &drivable_current_lanes);
  }

  // Add regional path current lanes for last selected lane sequence.
  if (last_selected_lane_sequence_candidate &&
      last_selected_lane_sequence_candidate->lane_sequence_type() ==
          pb::LaneSequenceCandidate::BACKUP_LANE_FOLLOW) {
    bool matched = false;
    if (!drivable_current_lanes.empty() &&
        !last_selected_lane_sequence.empty()) {
      // Check whether drivable_current_lanes has candidate along
      // last_selected_lane_sequence.
      for (const auto& candidate : drivable_current_lanes) {
        DCHECK_NOTNULL(candidate.lane());
        if (candidate.lane() == nullptr) {
          break;
        }
        const auto candidate_lane_id = candidate.lane()->id();
        if (std::any_of(
                last_selected_lane_sequence.begin(),
                last_selected_lane_sequence.end(),
                [candidate_lane_id](const pnc_map::Lane* sequence_lane) {
                  return sequence_lane != nullptr &&
                         candidate_lane_id == sequence_lane->id();
                })) {
          matched = true;
          break;
        }
      }
    }
    if (!matched && !last_selected_lane_sequence.empty()) {
      // Recollect current lanes on last_selected_lane_sequence but not in
      // near_lane_candidate_lanes.
      std::vector<const pnc_map::Lane*> near_lane_candidate_lanes =
          joint_pnc_map_service.GetNearLanesWithPose(
              pose, constants::kLaneSearchingRadiusInMeter,
              /*max_heading_diff=*/M_PI_2,
              /*prefer_overlapped_lanes=*/false, last_selected_lane_sequence);
      std::set<LightCurrentLaneCandidate> candidates_on_last_selected_ls;
      for (const pnc_map::Lane* lane : last_selected_lane_sequence) {
        if (lane == nullptr) {
          candidates_on_last_selected_ls.clear();
          break;
        }
        if (std::any_of(near_lane_candidate_lanes.begin(),
                        near_lane_candidate_lanes.end(),
                        [lane](const pnc_map::Lane* candidate_lane) {
                          return candidate_lane != nullptr &&
                                 lane->id() == candidate_lane->id();
                        })) {
          candidates_on_last_selected_ls.insert(
              LightCurrentLaneCandidate(lane));
          near_lane_candidates_map_.try_emplace(
              lane->id(),
              GetNearLaneCandidate(
                  lane, pose, robot_state_snapshot.bounding_box(),
                  global_route_solution, locator_selected_near_lanes,
                  locator_selected_potential_sections,
                  locator_selected_extend_potential_sections,
                  last_selected_lane_sequence, valid_target_lane_sequence));
        }
      }
      if (!candidates_on_last_selected_ls.empty()) {
        // Clear drivable current lanes if last selected lane sequence is
        // diverged with last optimal regional path and they have no public
        // sections.
        bool has_candidate_on_last_cycle_section_sequence = false;
        for (const auto& candidate : candidates_on_last_selected_ls) {
          if (candidate.lane == nullptr) {
            continue;
          }
          if (std::any_of(
                  last_cycle_section_sequence.begin(),
                  last_cycle_section_sequence.end(),
                  [&candidate](const pnc_map::Section* sequence_section) {
                    return sequence_section != nullptr &&
                           candidate.lane->section()->id() ==
                               sequence_section->id();
                  })) {
            has_candidate_on_last_cycle_section_sequence = true;
          }
        }
        if (!has_candidate_on_last_cycle_section_sequence) {
          for (const auto& candidate : drivable_current_lanes) {
            if (candidate.lane() == nullptr) {
              continue;
            }
            near_lane_candidates_map_.erase(candidate.lane()->id());
          }
          drivable_current_lanes.clear();
        }
        // Only save current lane candidates on last selected lane sequence.
        AddLanesIntoCurrentLaneCandidates(
            near_lane_candidates_map_, candidates_on_last_selected_ls,
            ego_position, routing_cost_generator, current_lane_cost_estimator_,
            &drivable_current_lanes);
      }
    }
  }

  current_lane_candidates_for_regional_path_ =
      GetSuccessorRemovedLanes(drivable_current_lanes);

  LogDebugInfoForUnassociatedPrefixes(
      prefixes_info_, current_lane_candidates_for_regional_path_);

  current_lanes_for_regional_path_ =
      GetCurrentLanesFromCandidates(current_lane_candidates_for_regional_path_);

  // 3. Get current drivable lanes for lane blockage.
  // current_lanes_for_lane_blockage_ =
  // GetCurrentLanesFromImmutableLaneSequence(
  //     current_lanes_for_regional_path_, lane_sequence_plan_init_state,
  //     route_event);
  current_lanes_for_lane_blockage_ = current_lanes_for_regional_path_;

  return !current_lanes_for_regional_path_.empty();
}

std::vector<const pnc_map::Lane*>
CurrentLaneAssociator::GetDrivablePhysicalLanes() const {
  std::vector<const pnc_map::Lane*> drivable_physical_lanes;
  drivable_physical_lanes.reserve(near_lane_candidates_map_.size());
  for (const auto& [_, candidate] : near_lane_candidates_map_) {
    // Physical lanes also have a need for continuity, so lanes outside the
    // locator selected extend potential sections will be filtered out.
    if (candidate.is_physical() &&
        candidate.is_in_extend_potential_sections() &&
        drivable_lane_reasoner().IsLaneRobotDrivable(
            candidate.lane(), /*check_local_hold=*/true,
            /*check_edge_filter=*/false)) {
      drivable_physical_lanes.push_back(candidate.lane());
    }
  }
  return drivable_physical_lanes;
}

std::vector<const lane_candidate::NearLaneCandidate*>
CurrentLaneAssociator::GetDrivablePhysicalNearLaneCandidates() const {
  std::vector<const lane_candidate::NearLaneCandidate*>
      drivable_physical_near_lane_candidates;
  drivable_physical_near_lane_candidates.reserve(
      near_lane_candidates_map_.size());
  for (const auto& [_, candidate] : near_lane_candidates_map_) {
    if (candidate.is_physical() &&
        drivable_lane_reasoner().IsLaneRobotDrivable(
            candidate.lane(), /*check_local_hold=*/true,
            /*check_edge_filter=*/false)) {
      drivable_physical_near_lane_candidates.push_back(&candidate);
    }
  }
  return drivable_physical_near_lane_candidates;
}

std::vector<const pnc_map::Lane*> CurrentLaneAssociator::GetBackupLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service) const {
  std::vector<const pnc_map::Lane*> backup_lanes;
  backup_lanes.reserve(near_lane_candidates_map_.size());

  LaneSelectionParam lane_selection_param;

  // 1. Prefer physical lanes on last selected lane sequence for stability.
  lane_selection_param.is_in_last_selected_lane_sequence = true;
  lane_selection_param.is_physical = true;
  backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);

  // 2. Still try to select lanes on last selected lane sequence, but relax the
  // distance to lane and limit the heading diff.
  if (backup_lanes.empty()) {
    lane_selection_param.is_physical = false;
    lane_selection_param.max_position_to_lane_center_dist_in_m =
        constants::kLaneSearchingRadiusInMeter / 3.0;
    lane_selection_param.max_heading_diff_in_rad = M_PI_4;
    backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);
  }

  // 3. Can not find appropriate lanes from last selected lane sequence. Try to
  // find lanes according to ego pose. Prefer physical lanes, but should be more
  // restricted to heading diff.
  if (backup_lanes.empty()) {
    lane_selection_param.is_in_last_selected_lane_sequence = false;
    lane_selection_param.is_physical = true;
    lane_selection_param.max_heading_diff_in_rad = M_PI_2 / 3.0;
    backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);
  }

  // 4. Go on to relax the distance and heading diff.
  if (backup_lanes.empty()) {
    lane_selection_param.is_physical = false;
    lane_selection_param.max_position_to_lane_center_dist_in_m =
        constants::kLaneSearchingRadiusInMeter / 3.0;
    lane_selection_param.max_heading_diff_in_rad = M_PI_4;
    backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);
  }

  // 5. Go on to relax the distance.
  if (backup_lanes.empty()) {
    lane_selection_param.max_position_to_lane_center_dist_in_m =
        constants::kLaneSearchingRadiusInMeter / 2.0;
    backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);
  }

  // 6. Go on to relax the heading diff.
  if (backup_lanes.empty()) {
    lane_selection_param.max_heading_diff_in_rad = M_PI_2;
    backup_lanes = SelectLanes(joint_pnc_map_service, lane_selection_param);
  }

  if (backup_lanes.empty()) {
    LOG(ERROR)
        << "Can not find appropriate backup lane from NearLaneCandidate!";
  }

  return backup_lanes;
}

bool CurrentLaneAssociator::UpdateCurrentLanesForWaypointGraph(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const voy::Pose& pose, const RegionalPath& regional_path,
    const RobotStateSnapshot& robot_state_snapshot,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<pnc_map::SectionVertex>& regional_section_vertices,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const pb::LaneSequenceCandidate* last_selected_lane_sequence_candidate,
    const pb::PullOutJumpOutSeed& pull_out_jump_out_seed,
    const lane_selection::StuckAvoidanceParameter& stuck_avoidance_param,
    const pb::LaneBlockageDetectorSeed& lane_blockage_detector_seed,
    const pb::DecoupledManeuverSeed&
        previous_decoupled_lane_follow_maneuver_seed,
    bool should_generate_pull_out_jump_out_sequence, bool is_new_trip,
    bool is_in_autonomous_mode) {
  (void)pose;
  DCHECK(!regional_path.sections.empty());
  const pnc_map::Section* current_section =
      DCHECK_NOTNULL(regional_path.sections.front());
  pnc_map::PncObjectSet<const pnc_map::Section*> regional_section_set;
  GetRegionalSectionSet(regional_path.sections, &regional_section_set);
  DCHECK(!regional_section_set.empty());
  // Update current lanes.
  // TODO(zhanshushi): Call IsLaneInSectionSet in other functions to avoid add a
  // jump out current who is not in regional path.
  std::vector<const pnc_map::Lane*> current_lanes = GetCurrentLanes(
      last_selected_lane_sequence, drivable_lane_reasoner(),
      current_lane_candidates_for_regional_path(), *current_section,
      robot_state_snapshot, regional_section_set);
  if (last_selected_lane_sequence_candidate &&
      last_selected_lane_sequence_candidate->lane_sequence_type() ==
          pb::LaneSequenceCandidate::BACKUP_LANE_FOLLOW) {
    bool matched = false;
    if (!current_lanes.empty() && !last_selected_lane_sequence.empty()) {
      for (const auto* lane : current_lanes) {
        if (lane == nullptr) {
          break;
        }
        if (std::any_of(last_selected_lane_sequence.begin(),
                        last_selected_lane_sequence.end(),
                        [lane](const pnc_map::Lane* sequence_lane) {
                          return sequence_lane != nullptr &&
                                 lane->id() == sequence_lane->id();
                        })) {
          matched = true;
          break;
        }
      }
    }
    if (!matched && !last_selected_lane_sequence.empty()) {
      std::vector<const pnc_map::Lane*> near_lane_candidate_lanes =
          joint_pnc_map_service.GetNearLanesWithPose(
              pose, constants::kLaneSearchingRadiusInMeter,
              /*max_heading_diff=*/M_PI_2,
              /*prefer_overlapped_lanes=*/false, last_selected_lane_sequence);
      for (const pnc_map::Lane* lane : last_selected_lane_sequence) {
        if (lane == nullptr) {
          break;
        }
        if (std::any_of(near_lane_candidate_lanes.begin(),
                        near_lane_candidate_lanes.end(),
                        [lane](const pnc_map::Lane* candidate_lane) {
                          return candidate_lane != nullptr &&
                                 lane->id() == candidate_lane->id();
                        })) {
          current_lanes.push_back(lane);
        }
      }
    }
  }
  current_lanes_on_last_selected_lane_sequence_.clear();
  current_lanes_for_pull_out_jump_out_.clear();
  current_lanes_on_fork_.clear();
  current_lanes_for_stuck_avoidance_jump_out_.clear();
  prefix_associated_current_lanes_.clear();

  current_lanes_for_waypoint_graph_.clear();
  std::string debug_info;
  if (current_lanes.empty()) {
    debug_info = "Failed to search current lanes from current section.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneSearchNotInCurrentSection>(
        debug_info);
    LOG(ERROR) << "Current lane associator fails to get current lanes for lane "
                  "sequence.";
    return false;
  }
  const math::geometry::Point2d ego_position(robot_state_snapshot.x(),
                                             robot_state_snapshot.y());
  const std::vector<const pnc_map::Lane*> physical_lanes =
      GetDrivablePhysicalLanes();
  // All current lane candidates.
  std::vector<lane_candidate::CurrentLaneCandidate> current_lane_candidates;
  // Only the jump out current lane which is in regional path current lanes will
  // be added to lane sequence current lanes.
  // Here we only use |should_generate_pull_out_jump_out_sequence| to check if
  // we need to add jump out lanes, which will not be in regional path current
  // lane's pool if ego has entered JO sequence.
  const pnc_map::PncObjectSet<const pnc_map::Lane*> jump_out_current_lanes =
      should_generate_pull_out_jump_out_sequence
          ? GetPullOutJumpOutCurrentLanes(
                drivable_lane_reasoner(), physical_lanes, current_lanes,
                pull_out_jump_out_seed.last_jump_out_lane_sequence(),
                pull_out_jump_out_seed.last_jump_out_current_lanes().empty())
          : GetStuckAvoidanceJumpOutLanes(
                joint_pnc_map_service, global_route_solution,
                regional_section_vertices, stuck_avoidance_param,
                lane_blockage_detector_seed, near_lane_candidates_map_,
                robot_state_snapshot, physical_lanes, current_lanes);
  for (const pnc_map::Lane* lane : current_lanes) {
    AddCurrentLaneCandidate(lane, near_lane_candidates_map_, ego_position,
                            routing_cost_generator, jump_out_current_lanes,
                            should_generate_pull_out_jump_out_sequence,
                            &current_lane_candidates);
  }

  // Add waypoint graph current lanes for lane sequence proposal prefixes on
  // the optimal regional path.
  AddPrefixAssociatedCurrentLanes(
      lane_selection::GetLaneSequencePrefixesByIds(
          prefixes_info_, regional_path.associated_prefix_ids),
      ego_position, routing_cost_generator, &current_lane_candidates);

  // Filter out lanes that are not selected by the locator potential
  // sections. Lanes used for stuck avoidance jump out may not be
  // included in the locator potential sections, so they are skipped during
  // filtering.
  // Notes(yuzehao): We require that all types of current lanes cannot be
  // outside the range of locator potential sections.
  std::vector<lane_candidate::CurrentLaneCandidate>
      candidates_in_potential_sections;
  candidates_in_potential_sections.reserve(current_lane_candidates.size());
  for (const auto& candidate : current_lane_candidates) {
    if (candidate.is_in_locator_potential_sections() ||
        candidate.IsStuckAvoidanceJumpOut()) {
      candidates_in_potential_sections.push_back(candidate);
    }
  }
  // TODO(yuzehao): Take current lane for |STUCK_AVOIDANCE_JUMP_OUT| into
  // consideration.
  if (!candidates_in_potential_sections.empty()) {
    current_lane_candidates = candidates_in_potential_sections;
  }

  if (current_lane_candidates.empty()) {
    DCHECK(!current_lanes.empty());
    LOG(ERROR) << "Current lanes are not in near_lane_candidates_. We need to "
                  "manually check what is wrong.";
    LOG(ERROR) << "Current lane Ids: " << GetLaneIdsString(current_lanes);
    LOG(ERROR) << "Near lane candidate Ids: "
               << GetNearLaneCandidateIdsString(near_lane_candidates_map_);
    return false;
  }

  // Lane candidates which satisfy some hard requirements.
  std::vector<lane_candidate::CurrentLaneCandidate>
      filtered_current_lane_candidates;
  current_lanes_on_last_selected_lane_sequence_.reserve(
      current_lane_candidates.size());
  current_lanes_for_pull_out_jump_out_.reserve(current_lane_candidates.size());
  current_lanes_for_stuck_avoidance_jump_out_.reserve(
      current_lane_candidates.size());

  const bool is_ego_at_static_speed =
      robot_state_snapshot.speed() < kStaticEgoSpeedInMps;
  const bool is_last_cycle_lane_change_finished =
      previous_decoupled_lane_follow_maneuver_seed
          .last_lane_change_finish_instance()
          .ByteSizeLong() > 0;
  for (const lane_candidate::CurrentLaneCandidate& candidate :
       current_lane_candidates) {
    if (candidate.is_in_last_selected_lane_sequence()) {
      current_lanes_on_last_selected_lane_sequence_.push_back(candidate);
    }

    if (candidate.lane()->IsForkLane()) {
      current_lanes_on_fork_.push_back(candidate);
    }

    if (!candidate.prefix_ids().empty()) {
      prefix_associated_current_lanes_.push_back(candidate);
    }

    // When current lane candidate is for pull out jump out, only add it to
    // |current_lanes_for_pull_out_jump_out_|.
    if (candidate.IsPullOutJumpOut()) {
      current_lanes_for_pull_out_jump_out_.push_back(candidate);
      continue;
    }

    // When current lane candidate is for stuck avoidance jump out, only add it
    // to |current_lanes_for_stuck_avoidance_jump_out_|.
    if (candidate.IsStuckAvoidanceJumpOut()) {
      current_lanes_for_stuck_avoidance_jump_out_.push_back(candidate);
      continue;
    }

    if (is_ego_at_static_speed) {
      // Keep more possibility when ego is static.
      // In the next round of filters, lanes that are overlapped with pose will
      // be preferred.
      filtered_current_lane_candidates.push_back(candidate);
      continue;
    }

    if (is_last_cycle_lane_change_finished &&
        candidate.is_in_last_target_lane_sequence() &&
        candidate.is_physical()) {
      // If last cycle is a lane change finish cycle, and a lane is both on last
      // cycle target lane sequence and has pose inside it, we have confidence
      // that this lane should be picked up as candidates.
      filtered_current_lane_candidates.push_back(candidate);
      continue;
    }

    if (candidate.is_in_last_selected_lane_sequence() &&
        candidate.is_physical()) {
      // If a lane is both on last selected lane sequence and has pose inside
      // it, we have confidence that this lane should be picked up as
      // candidates.
      filtered_current_lane_candidates.push_back(candidate);
      continue;
    }
  }
  const std::vector<lane_candidate::CurrentLaneCandidate>& candidates =
      filtered_current_lane_candidates.empty()
          ? current_lane_candidates
          : filtered_current_lane_candidates;

  int closest_index = 0;
  double closest_dist = candidates.front().position_to_lane_center_dist_in_m();
  int closest_continuous_lane_index = -1;
  double on_last_selected_sequence_closest_dist =
      std::numeric_limits<double>::max();
  std::set<int64_t> processed_lane_ids;
  std::vector<lane_candidate::CurrentLaneCandidate>
      fallback_current_lane_candidates;
  fallback_current_lane_candidates.reserve(candidates.size());
  for (size_t i = 0; i < candidates.size(); ++i) {
    const auto& candidate_i = candidates.at(i);
    if (candidate_i.IsPullOutJumpOut() ||
        candidate_i.IsStuckAvoidanceJumpOut()) {
      continue;
    }

    if (closest_dist > candidate_i.position_to_lane_center_dist_in_m()) {
      closest_dist = candidate_i.position_to_lane_center_dist_in_m();
      closest_index = i;
    }

    if (is_last_cycle_lane_change_finished) {
      // If last cycle is in lane change finishing state, try to get the closest
      // overlapped target lanes.
      if (candidate_i.is_in_last_target_lane_sequence() &&
          candidate_i.is_overlapped() &&
          on_last_selected_sequence_closest_dist >
              candidate_i.position_to_lane_center_dist_in_m()) {
        on_last_selected_sequence_closest_dist =
            candidate_i.position_to_lane_center_dist_in_m();
        closest_continuous_lane_index = i;
      }
    } else {
      // If not, still get the closest overlapped lanes from last selected lane
      // sequence.
      if (candidate_i.is_in_last_selected_lane_sequence() &&
          on_last_selected_sequence_closest_dist >
              candidate_i.position_to_lane_center_dist_in_m()) {
        on_last_selected_sequence_closest_dist =
            candidate_i.position_to_lane_center_dist_in_m();
        closest_continuous_lane_index = i;
      }
    }

    if (candidate_i.is_physical() &&
        processed_lane_ids.count(candidate_i.lane()->id()) == 0) {
      processed_lane_ids.insert(candidate_i.lane()->id());
      // If the last cycle was the lane change finish cycle, keep current lane
      // on target lane if ego is in fork.
      if (IsLaneChangeSourceLaneInForkWhenLCFinish(
              last_selected_lane_sequence_candidate,
              previous_decoupled_lane_follow_maneuver_seed
                  .last_lane_change_finish_instance(),
              candidate_i)) {
        continue;
      }
      // We temporarily store the physical current lanes.
      fallback_current_lane_candidates.push_back(candidate_i);
    }
  }

  const bool is_in_normal_LF_maneuver = IsEgoInNormalLFOrLCManeuver(
      last_selected_lane_sequence_candidate, robot_state_snapshot.speed(),
      is_new_trip, is_in_autonomous_mode, is_last_cycle_lane_change_finished,
      /*is_lane_follow=*/true);
  const bool is_in_pull_over =
      previous_decoupled_lane_follow_maneuver_seed.selected_trajectory()
          .extra_planner_signals()
          .behavior_signal() == pb::ExtraPlannerSignals::PULL_OVER;

  // Now we select the closest lane in last selection lane seuqnece, when ego is
  // in normal lane follow maneuver.
  // TODO(yuzehao): Also select the closest continuous lane in lane change
  // maneuver and improve the selection logic in the lane change finish cycle.
  if (is_in_normal_LF_maneuver && closest_continuous_lane_index >= 0) {
    const auto& closest_candidate =
        candidates.at(closest_continuous_lane_index);
    current_lanes_for_waypoint_graph_.push_back(closest_candidate);
    if (is_in_pull_over &&
        processed_lane_ids.count(closest_candidate.lane()->id()) == 0) {
      // If ego is in pull over behavior, add this candidate into fallback
      // sequence, to ensure that the closest continuous lane and the physical
      // lanes are all current lans in pull over behavior.
      fallback_current_lane_candidates.push_back(closest_candidate);
    }
  }

  // Use fallback current lanes if:
  // 1. current_lanes_for_waypoint_graph_ can't be updated by last selected lane
  // sequence.
  // 2. Or in pullover.
  // 3. Or waypoint assist is engaged but have not reached target lane sequence.
  if (current_lanes_for_waypoint_graph_.empty() || is_in_pull_over ||
      has_ego_reached_target_lane_sequence_ ==
          pb::EgoReachTargetLaneSequenceState::NOT_REACHED) {
    current_lanes_for_waypoint_graph_ = fallback_current_lane_candidates;
  }

  // Still cannot found any suitable current lanes, use the fallback lane.
  if (current_lanes_for_waypoint_graph_.empty()) {
    current_lanes_for_waypoint_graph_.push_back(candidates.at(
        closest_continuous_lane_index < 0 ? closest_index
                                          : closest_continuous_lane_index));
  }

  // Sort with ascending order by cost.
  std::sort(current_lanes_for_waypoint_graph_.begin(),
            current_lanes_for_waypoint_graph_.end());

  const bool has_physical_lane =
      std::any_of(current_lanes_for_waypoint_graph_.begin(),
                  current_lanes_for_waypoint_graph_.end(),
                  [](const lane_candidate::CurrentLaneCandidate& candidate) {
                    return candidate.is_physical();
                  });
  if (!has_physical_lane) {
    debug_info = "There is no physical current lane.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneResEmptyPhysicalLane>(
        debug_info);
  }

  const bool has_normal_lane = std::any_of(
      current_lanes_for_waypoint_graph_.begin(),
      current_lanes_for_waypoint_graph_.end(),
      [](const lane_candidate::CurrentLaneCandidate& candidate) {
        return candidate.jump_out_type() == pb::JumpOutType::NOT_JUMP_OUT;
      });
  if (!has_normal_lane) {
    debug_info = "There is no normal current lane.";
    rt_event::PostRtEvent<rt_event::planner::CurrLaneResEmptyNormalLane>(
        debug_info);
  }

  return true;
}

const std::vector<lane_candidate::CurrentLaneCandidate>&
CurrentLaneAssociator::GetCrossLaneNudgeCurrentLanes(
    pb::ManeuverType last_selected_maneuver_type,
    bool latch_current_lane) const {
  static std::vector<lane_candidate::CurrentLaneCandidate> empty_current_lanes;
  if (!FLAGS_planning_enable_decoupled_maneuvers ||
      last_selected_maneuver_type != pb::ManeuverType::LANE_FOLLOW ||
      !latch_current_lane) {
    return empty_current_lanes;
  }

  return current_lanes_on_last_selected_lane_sequence();
}

std::vector<const pnc_map::Lane*> CurrentLaneAssociator::SelectLanes(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const LaneSelectionParam& lane_selection_param) const {
  std::vector<const pnc_map::Lane*> selected_lanes;
  selected_lanes.reserve(near_lane_candidates_map_.size());

  const std::vector<const lane_candidate::NearLaneCandidate*>
      physical_candidates = GetDrivablePhysicalNearLaneCandidates();

  for (const auto& [_, candidate] : near_lane_candidates_map_) {
    const pnc_map::Lane* lane = candidate.lane();
    if (lane == nullptr) {
      continue;
    }

    if (!drivable_lane_reasoner().IsLaneRobotDrivable(
            lane, /*check_local_hold=*/true,
            /*check_edge_filter=*/false)) {
      continue;
    }

    const double position_arclength_from_lane_start =
        candidate.position_arclength_from_lane_start();
    if (std::any_of(
            physical_candidates.begin(), physical_candidates.end(),
            [&joint_pnc_map_service, lane, position_arclength_from_lane_start](
                const lane_candidate::NearLaneCandidate* physical_candidate) {
              return HasHardBoundaryBetweenNearbyLanes(
                  joint_pnc_map_service, lane, physical_candidate->lane(),
                  position_arclength_from_lane_start,
                  physical_candidate->position_arclength_from_lane_start());
            })) {
      continue;
    }

    if (!lane_selection_param.ShouldSelectLane(candidate)) {
      continue;
    }

    selected_lanes.push_back(lane);
  }

  return selected_lanes;
}

void CurrentLaneAssociator::AddCurrentLaneCandidate(
    const pnc_map::Lane* lane,
    const std::map<int64_t, lane_candidate::NearLaneCandidate>&
        near_lane_candidates_map,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const pnc_map::PncObjectSet<const pnc_map::Lane*>& jump_out_current_lanes,
    bool should_generate_pull_out_jump_out_sequence,
    std::vector<lane_candidate::CurrentLaneCandidate>*
        current_lane_candidates) {
  const auto it = near_lane_candidates_map.find(lane->id());
  if (it != near_lane_candidates_map.end()) {
    const auto& near_lane_candidate = it->second;
    const bool is_jump_out =
        (jump_out_current_lanes.find(lane) != jump_out_current_lanes.end());
    pb::JumpOutType jump_out_type = pb::JumpOutType::NOT_JUMP_OUT;
    if (is_jump_out) {
      jump_out_type = should_generate_pull_out_jump_out_sequence
                          ? pb::JumpOutType::PULL_OUT_JUMP_OUT
                          : pb::JumpOutType::STUCK_AVOIDANCE_JUMP_OUT;
    }
    current_lane_candidates->push_back(GetCurrentLaneCandidate(
        near_lane_candidate, ego_position, routing_cost_generator,
        current_lane_cost_estimator_, jump_out_type, /*prefix_ids=*/{}));
  }
}

void CurrentLaneAssociator::AddPrefixAssociatedCurrentLanes(
    const std::vector<lane_selection::LaneSequencePrefixInfo>& prefixes,
    const math::geometry::Point2d& ego_position,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    std::vector<lane_candidate::CurrentLaneCandidate>*
        current_lane_candidates) {
  if (prefixes.empty()) {
    return;
  }
  CHECK(current_lane_candidates);

  // 1. Associate existing current lanes with lane sequence proposal prefixes,
  // and collect the prefixes that do not find corresponding current lanes.
  std::vector<const lane_selection::LaneSequencePrefixInfo*>
      unassociated_prefixes;
  AssociatePrefixesAndCurrentLanes(prefixes, current_lane_candidates,
                                   &unassociated_prefixes);
  if (unassociated_prefixes.empty()) {
    return;
  }

  // 2. Populate a current lane pool with all lanes that share the same sections
  // with existing current lanes. The key is the lane id, and the value is the
  // lane.
  const pnc_map::PncObjectSet<const pnc_map::Lane*> current_lane_pool =
      GetOtherDrivableLanesInTheSameSections(*current_lane_candidates,
                                             *drivable_lane_reasoner_);

  // 3. Attempt to find suitable current lanes in the pool for the unassociated
  // prefixes.
  const std::set<LightCurrentLaneCandidate> current_lanes_to_be_added =
      GetCurrentLaneCandidatesForUnassociatedPrefixes(
          current_lane_pool, ego_position, unassociated_prefixes);

  // 4. Add the current lanes found to candidates.
  AddLanesIntoCurrentLaneCandidates(
      near_lane_candidates_map_, current_lanes_to_be_added, ego_position,
      routing_cost_generator, current_lane_cost_estimator_,
      current_lane_candidates);
}

void CurrentLaneAssociator::AddFallbackCurrentLaneForRegionalPath(
    const pnc_map::Lane* current_lane,
    const RobotStateSnapshot& robot_state_snapshot, const voy::Pose& pose,
    const routing::cost_engine::CostGenerator& routing_cost_generator,
    const std::vector<const pnc_map::Lane*>& locator_selected_near_lanes,
    const std::vector<const pnc_map::Section*>&
        locator_selected_potential_sections,
    const std::vector<const pnc_map::Section*>&
        locator_selected_extend_potential_sections,
    const GlobalRouteSolution& global_route_solution,
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<const pnc_map::Lane*>& last_cycle_target_lane_sequence) {
  if (!current_lane_candidates_for_regional_path_.empty() ||
      current_lane == nullptr) {
    return;
  }
  current_lanes_for_regional_path_.push_back(current_lane);

  const lane_candidate::NearLaneCandidate near_lane_candidate =
      GetNearLaneCandidate(
          current_lane, pose, robot_state_snapshot.bounding_box(),
          global_route_solution, locator_selected_near_lanes,
          locator_selected_potential_sections,
          locator_selected_extend_potential_sections,
          last_selected_lane_sequence, last_cycle_target_lane_sequence);
  near_lane_candidates_map_.try_emplace(current_lane->id(),
                                        near_lane_candidate);
  current_lane_candidates_for_regional_path_.push_back(GetCurrentLaneCandidate(
      near_lane_candidate,
      math::geometry::Point2d(robot_state_snapshot.x(),
                              robot_state_snapshot.y()),
      routing_cost_generator, current_lane_cost_estimator_,
      pb::JumpOutType::NOT_JUMP_OUT, /*prefix_ids=*/{}));
}

std::optional<bool> CurrentLaneAssociator::IsCurrentLanesContinuousWhenRA(
    bool is_waypoint_assist_invoked) const {
  // Return true if waypoint assist is invoked and there exists current lanes in
  // last selected lane sequence.
  std::optional<bool> is_current_lanes_continuous_when_ra = std::nullopt;
  if (is_waypoint_assist_invoked) {
    const bool is_any_curr_lane_in_last_lane_seq =
        std::any_of(current_lane_candidates_for_regional_path_.begin(),
                    current_lane_candidates_for_regional_path_.end(),
                    [](const auto& candidate) {
                      return candidate.is_in_locator_potential_sections() &&
                             candidate.is_physical();
                    });
    is_current_lanes_continuous_when_ra =
        std::make_optional(is_any_curr_lane_in_last_lane_seq);
  }
  return is_current_lanes_continuous_when_ra;
}

}  // namespace planner
