#ifndef OFFBOARD_FALLBACK_ADS_DEBUG_CONTROL_DEBUG_ENCODE_CONTROL_DEBUG_ENCODE_H_
#define OFFBOARD_FALLBACK_ADS_DEBUG_CONTROL_DEBUG_ENCODE_CONTROL_DEBUG_ENCODE_H_

#include <cstddef>
#include <cstdint>

#include "_out/Appl/GenData/Rte_Type.h"

namespace fallback_adas {
namespace debug {

void ControlDebugEncode(const ControlDebug& control_debug);

}  // namespace debug
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_DEBUG_CONTROL_DEBUG_ENCODE_CONTROL_DEBUG_ENCODE_H_
