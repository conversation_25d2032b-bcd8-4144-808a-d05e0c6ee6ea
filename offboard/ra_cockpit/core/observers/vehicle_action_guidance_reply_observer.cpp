#include "vehicle_action_guidance_reply_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

VehicleActionGuidanceReplyObserver::VehicleActionGuidanceReplyObserver(
    VehicleActionGuidanceReplyCallback callback)
    : vehicle_action_guidance_reply_callback_(std::move(callback)) {}

bool VehicleActionGuidanceReplyObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kVehicleActionGuidanceReply);
}

std::shared_ptr<google::protobuf::Message>
VehicleActionGuidanceReplyObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto vehicle_action_guidance_reply =
      std::make_shared<teleassist::pb::VehicleActionGuidanceReply>();
  if (!vehicle_action_guidance_reply->ParseFromString(message.message_data())) {
    LOG(ERROR)
        << "Received error VehicleActionGuidanceReply message format, len = "
        << message.message_data().length();
    return nullptr;
  }
  if (vehicle_action_guidance_reply_callback_) {
    vehicle_action_guidance_reply_callback_(vehicle_action_guidance_reply,
                                            data_channel);
  }
  return vehicle_action_guidance_reply;
}

}  // namespace remote_assist
