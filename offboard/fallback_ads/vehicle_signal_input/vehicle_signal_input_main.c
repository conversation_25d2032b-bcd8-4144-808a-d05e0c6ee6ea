#include "vehicle_signal_input/vehicle_signal_input_main.h"

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Components/Rte_VehSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/debounce_method.h"
#include "vehicle_signal_input/can_signal_decode/can_signal_decode.h"
#include "vehicle_signal_input/can_timeout_detection/can_timeout_detection.h"
#include "vehicle_signal_input/vehicle_signal_input_parameter.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

// Model initialize function
void VehSigInput_Init(void) {
  VSI_McuCanTimeout_Struct VSI_McuCanTimeout = {
      0.0F,  false, 0.0F,  0.0F,  0.0F,  0.0F,  0.0F,  0.0F,  0.0F,  0.0F,
      0.0F,  0.0F,  0.0F,  0.0F,  0.0F,  false, false, false, false, false,
      false, false, false, false, false, 0U,    false, false, false, false};

  VSI_VehInfoFor1V1R_Struct VSI_VehInfoFor1V1R = {
      0.0F, 0U,   0.0F, 0U,   0.0F, 0U, 0.0F, 0U, 0.0F, 0U, 0.0F,
      0U,   0.0F, 0U,   0.0F, 0.0F, 0U, 0.0F, 0U, 0U,   0U};

  VSI_VehicleInfo_Struct VSI_VehicleInfo = {0.0F, 0.0F,  0.0F,  0.0F, 0.0F,
                                            0.0F, 0.0F,  0.0F,  0.0F, 0U,
                                            0U,   false, false, 0U,   0U};

  // SystemInitialize for Outport
  Rte_IWrite_VehSigInput_Init_VSI_VehicleInfo_VSI_VehicleInfo(&VSI_VehicleInfo);

  Rte_IWrite_VehSigInput_Init_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R(
      &VSI_VehInfoFor1V1R);

  Rte_IWrite_VehSigInput_Init_VSI_McuCanTimeout_VSI_McuCanTimeout(
      &VSI_McuCanTimeout);
}

void VehSigInput_10ms_Runnable(void) {
  // convert dbc signal to physical signal
  VehicleAndAcuCanSignal can_signal = {0};
  ConvertVehicleAndAcuDbcSignals(&can_signal);

  // get xc90 chassis info for algo
  VSI_VehicleInfo_Struct vehicle_info = {0};
  GetAlgorithmRequiredChassisInfo(&can_signal, &vehicle_info);
  Rte_IWrite_VehSigInput_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo(
      &vehicle_info);

  // mcu to sensors
  VSI_VehInfoFor1V1R_Struct sensor_chassis_info = {0};
  GetSensorRequiredChassisInfo(&can_signal, &sensor_chassis_info);
  Rte_IWrite_VehSigInput_10ms_Runnable_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R(
      &sensor_chassis_info);

  // can timeout, mcu to acu
  VSI_McuCanTimeout_Struct mcu_can_timeout = {0};
  CheckMcuCanTimeout(&can_signal, &mcu_can_timeout);
  Rte_IWrite_VehSigInput_10ms_Runnable_VSI_McuCanTimeout_VSI_McuCanTimeout(
      &mcu_can_timeout);
}
