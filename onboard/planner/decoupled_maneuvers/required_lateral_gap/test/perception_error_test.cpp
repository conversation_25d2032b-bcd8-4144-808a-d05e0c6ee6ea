#include "planner/decoupled_maneuvers/required_lateral_gap/perception_error.h"

#include <gtest/gtest.h>

#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "voy_protos/tracked_objects.pb.h"

namespace planner {

namespace {
class PerceptionErrorTest : public ::testing::Test {
 public:
  PerceptionErrorTest()
      : pose_at_plan_init_ts_(/*timestamp=*/0, agent_proto_.tracked_object()) {}

 protected:
  prediction::pb::Agent agent_proto_;

  TrafficParticipantPose pose_at_plan_init_ts_;
};

}  // namespace

TEST_F(PerceptionErrorTest, VehicleTest) {
  // Small vehicle
  auto& tracked_object = *agent_proto_.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(2.0);
  tracked_object.set_length(4.0);
  {
    PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                                 pose_at_plan_init_ts_);

    planner_object.set_l2_distance_to_ego_m(10.0);
    EXPECT_NEAR(
        0.12,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);

    planner_object.set_l2_distance_to_ego_m(40.0);
    EXPECT_NEAR(
        0.15,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);
  }
  // Large vehicle
  tracked_object.set_width(3.0);
  tracked_object.set_length(15.0);
  {
    PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                                 pose_at_plan_init_ts_);

    planner_object.set_l2_distance_to_ego_m(10.0);
    EXPECT_DEATH(
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        "This type is not implemented now");
  }
}

TEST_F(PerceptionErrorTest, ConeTest) {
  // Small vehicle
  auto& tracked_object = *agent_proto_.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::TRAFFIC_CONE);
  tracked_object.set_width(2.0);
  tracked_object.set_length(4.0);

  PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                               pose_at_plan_init_ts_);

  planner_object.set_l2_distance_to_ego_m(10.0);
  EXPECT_NEAR(
      0.23,
      ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
      1e-6);

  planner_object.set_l2_distance_to_ego_m(40.0);
  EXPECT_NEAR(
      0.29,
      ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
      1e-6);
}

TEST_F(PerceptionErrorTest, VRUTest) {
  // Cyclist
  auto& tracked_object = *agent_proto_.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::CYCLIST);
  tracked_object.set_width(1.0);
  tracked_object.set_length(2.0);
  {
    PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                                 pose_at_plan_init_ts_);

    planner_object.set_l2_distance_to_ego_m(10.0);
    EXPECT_NEAR(
        0.19,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);

    planner_object.set_l2_distance_to_ego_m(40.0);
    EXPECT_NEAR(
        0.2,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);
  }
  // Ped
  tracked_object.set_object_type(voy::perception::ObjectType::PED);
  tracked_object.set_width(0.5);
  tracked_object.set_length(0.5);
  {
    PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                                 pose_at_plan_init_ts_);

    planner_object.set_l2_distance_to_ego_m(10.0);
    EXPECT_NEAR(
        0.16,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);

    planner_object.set_l2_distance_to_ego_m(40.0);
    EXPECT_NEAR(
        0.19,
        ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
        1e-6);
  }
}

TEST_F(PerceptionErrorTest, CyclistWithoutPersonTest) {
  auto& tracked_object = *agent_proto_.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  tracked_object.set_width(1.0);
  tracked_object.set_length(2.0);
  tracked_object.add_attributes(voy::perception::CYC_WITHOUT_PERSON_ATTR);
  PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                               pose_at_plan_init_ts_);
  planner_object.set_l2_distance_to_ego_m(10.0);
  EXPECT_NEAR(
      ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
      0.28, 1e-6);
  planner_object.set_l2_distance_to_ego_m(40.0);
  EXPECT_NEAR(
      ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object),
      0.35, 1e-6);
}

TEST_F(PerceptionErrorTest, RaConfirmedUnstuckTest) {
  auto& tracked_object = *agent_proto_.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(2.0);
  tracked_object.set_length(4.0);
  PlannerObject planner_object(agent_proto_, /*object_timestamp=*/0,
                               pose_at_plan_init_ts_);
  planner_object.set_should_reduce_critical_req_lat_gap_by_ra_confirmation(
      true);

  EXPECT_NEAR(
      ComputePercerptionErrorForCriticalRequiredLateralGap(planner_object), 0.0,
      1e-6);
}

}  // namespace planner
