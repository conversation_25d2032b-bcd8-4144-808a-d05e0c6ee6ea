package(default_visibility = ["//visibility:public"])

cc_library(
    name = "st_agent",
    srcs = [
        "st_agent.cpp",
    ],
    hdrs = [
        "st_agent.h",
    ],
    include_prefix = "planner/ops_warning/st_agent",
    deps = [
        ":st_agent_utility",
        "//onboard/common/math",
    ],
)

cc_library(
    name = "st_agent_utility",
    srcs = [
        "st_agent_utility.cpp",
    ],
    hdrs = [
        "st_agent_utility.h",
    ],
    include_prefix = "planner/ops_warning/st_agent",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/decoupled_maneuvers/predicted_trajectory_wrapper",
        "//onboard/planner/trajectory/util",
        "//protobuf_cpp:protos_cpp",
    ],
)
