/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Control_Type.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application types header file for SW-C <Control>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_CONTROL_TYPE_H
# define RTE_CONTROL_TYPE_H

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

# include "_out/Appl/GenData/Rte_Type.h"

# ifndef RTE_CORE

/**********************************************************************************************************************
 * Range, Invalidation, Enumeration and Bit Field Definitions
 *********************************************************************************************************************/

#  ifndef kMcuUnknownEstimationState
#   define kMcuUnknownEstimationState (0U)
#  endif

#  ifndef kMcuPosePassThrough
#   define kMcuPosePassThrough (1U)
#  endif

#  ifndef kMcuCanbusGnssFusion
#   define kMcuCanbusGnssFusion (2U)
#  endif

#  ifndef kMcuCanbusDeadReckoning
#   define kMcuCanbusDeadReckoning (3U)
#  endif

#  ifndef NONE_LANE_CHANGE
#   define NONE_LANE_CHANGE (0U)
#  endif

#  ifndef LEFT_LANE_CHANGE
#   define LEFT_LANE_CHANGE (1U)
#  endif

#  ifndef RIGHT_LANE_CHANGE
#   define RIGHT_LANE_CHANGE (2U)
#  endif

#  ifndef MCU_UNKNOWN_TYPE
#   define MCU_UNKNOWN_TYPE (0U)
#  endif

#  ifndef MCU_RTK_FLOAT
#   define MCU_RTK_FLOAT (1U)
#  endif

#  ifndef MCU_RTK_INTEGER
#   define MCU_RTK_INTEGER (2U)
#  endif

#  ifndef MCU_PSRDIFF
#   define MCU_PSRDIFF (3U)
#  endif

#  ifndef MCU_SINGLE
#   define MCU_SINGLE (4U)
#  endif

#  ifndef MCU_PROPAGATED
#   define MCU_PROPAGATED (5U)
#  endif

#  ifndef MCU_PPP
#   define MCU_PPP (6U)
#  endif

#  ifndef MCU_DOPPLER_VELOCITY
#   define MCU_DOPPLER_VELOCITY (7U)
#  endif

#  ifndef MCU_NONE
#   define MCU_NONE (8U)
#  endif

#  ifndef MCU_FIXEDPOS
#   define MCU_FIXEDPOS (9U)
#  endif

#  ifndef MCU_FIXEDHEIGHT
#   define MCU_FIXEDHEIGHT (10U)
#  endif

#  ifndef MCU_SBAS
#   define MCU_SBAS (11U)
#  endif

#  ifndef MCU_L1_FLOAT
#   define MCU_L1_FLOAT (12U)
#  endif

#  ifndef MCU_IONOFREE_FLOAT
#   define MCU_IONOFREE_FLOAT (13U)
#  endif

#  ifndef MCU_NARROW_FLOAT
#   define MCU_NARROW_FLOAT (14U)
#  endif

#  ifndef MCU_L1_INT
#   define MCU_L1_INT (15U)
#  endif

#  ifndef MCU_WIDE_INT
#   define MCU_WIDE_INT (16U)
#  endif

#  ifndef MCU_NARROW_INT
#   define MCU_NARROW_INT (17U)
#  endif

#  ifndef PARKED
#   define PARKED (0U)
#  endif

#  ifndef FORWARD
#   define FORWARD (1U)
#  endif

#  ifndef BACKWARD
#   define BACKWARD (2U)
#  endif

# endif /* RTE_CORE */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_CONTROL_TYPE_H */
