#include "lateral_function/boundary_data_processing/boundary_data_processing.h"

#include <gtest/gtest.h>

namespace fallback_adas {
namespace common {
TEST(LaneGeneration, BothLaneTest) {
  CSI_LaneInfo_Struct lane_info = {};
  lane_info.CSI_LaneIDLf = 1U;
  lane_info.CSI_ExistProbLf = 0.99F;
  lane_info.CSI_ParseConfLf = 99;
  lane_info.CSI_PolyfitRmseLf = 99;
  lane_info.CSI_PosY0Lf = 1.75F;
  lane_info.CSI_HeadingLf = -0.01F;
  lane_info.CSI_CurvatureLf = 0.001F;
  lane_info.CSI_LaneIDRi = 3U;
  lane_info.CSI_ExistProbRi = 0.99F;
  lane_info.CSI_ParseConfRi = 99;
  lane_info.CSI_PolyfitRmseRi = 99;
  lane_info.CSI_PosY0Ri = -1.77F;
  lane_info.CSI_HeadingRi = -0.03F;
  lane_info.CSI_CurvatureRi = 0.003F;

  LaneEquation center_lane = LaneGeneration(&lane_info);

  EXPECT_FLOAT_EQ(center_lane.y0,
                  (lane_info.CSI_PosY0Lf + lane_info.CSI_PosY0Ri) * 0.5F);
  EXPECT_FLOAT_EQ(center_lane.heading,
                  (lane_info.CSI_HeadingLf + lane_info.CSI_HeadingRi) * 0.5F);
  EXPECT_FLOAT_EQ(
      center_lane.curvature,
      (lane_info.CSI_CurvatureLf + lane_info.CSI_CurvatureRi) * 0.5F);
}

TEST(LaneGeneration, OnlyLeftLaneTest) {
  CSI_LaneInfo_Struct lane_info = {};
  lane_info.CSI_LaneIDLf = 1U;
  lane_info.CSI_ExistProbLf = 0.99F;
  lane_info.CSI_ParseConfLf = 99;
  lane_info.CSI_PolyfitRmseLf = 99;
  lane_info.CSI_PosY0Lf = 1.75F;
  lane_info.CSI_HeadingLf = -0.01F;
  lane_info.CSI_CurvatureLf = 0.001F;

  LaneEquation center_lane = LaneGeneration(&lane_info);

  float virtual_lane_width = 3.0F;
  float ref_right_lane_y0 = lane_info.CSI_PosY0Lf - virtual_lane_width;
  float ref_center_lane_y0 = (lane_info.CSI_PosY0Lf + ref_right_lane_y0) * 0.5F;
  float ref_center_lane_heading = lane_info.CSI_HeadingLf;
  float ref_center_lane_curvature = lane_info.CSI_CurvatureLf;
  EXPECT_FLOAT_EQ(center_lane.y0, ref_center_lane_y0);
  EXPECT_FLOAT_EQ(center_lane.heading, ref_center_lane_heading);
  EXPECT_FLOAT_EQ(center_lane.curvature, ref_center_lane_curvature);
}

TEST(LaneGeneration, OnlyRightLaneTest) {
  CSI_LaneInfo_Struct lane_info = {};
  lane_info.CSI_LaneIDRi = 3U;
  lane_info.CSI_ExistProbRi = 0.99F;
  lane_info.CSI_ParseConfRi = 99;
  lane_info.CSI_PolyfitRmseRi = 99;
  lane_info.CSI_PosY0Ri = -1.80F;
  lane_info.CSI_HeadingRi = -0.03F;
  lane_info.CSI_CurvatureRi = 0.003F;

  LaneEquation center_lane = LaneGeneration(&lane_info);

  float virtual_lane_width = 3.0F;
  float ref_left_lane_y0 = lane_info.CSI_PosY0Ri + virtual_lane_width;
  float ref_center_lane_y0 = (ref_left_lane_y0 + lane_info.CSI_PosY0Ri) * 0.5F;
  float ref_center_lane_heading = lane_info.CSI_HeadingRi;
  float ref_center_lane_curvature = lane_info.CSI_CurvatureRi;
  EXPECT_FLOAT_EQ(center_lane.y0, ref_center_lane_y0);
  EXPECT_FLOAT_EQ(center_lane.heading, ref_center_lane_heading);
  EXPECT_FLOAT_EQ(center_lane.curvature, ref_center_lane_curvature);
}

}  // namespace common
}  // namespace fallback_adas
