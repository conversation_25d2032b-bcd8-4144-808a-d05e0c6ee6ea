#include "planner/path/reasoning/agent_intention/intention_generator.h"

#include <algorithm>
#include <cstdint>
#include <iterator>
#include <limits>
#include <map>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>

#include "gtl/map_util.h"
#include "log_utils/log_macros.h"
#include "math/math_util.h"
#include "planner/constants.h"
#include "tbb/parallel_for.h"
#include "tbb/task_group.h"

#include "geometry/model/polyline_curve.h"
#include "math/curve2d.h"
#include "planner/behavior/types/directive.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/path/reasoning/agent_intention/agent_intention_constants.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/reasoner/ego_context_info.h"
#include "planner/path/reasoning/agent_intention/trajectory_reasoner/trajectory_reasoner.h"
#include "planner/speed/profile/profile.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "planner_protos/speed_profile.pb.h"
#include "trace/trace.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {

void UpdateRepulsionMetaDataDebug(
    const std::vector<RepulsionMetaData>& repulsion_meta_datas,
    const std::unordered_map<pb::AgentReasonerId, std::string>&
        repulsion_reasoning_debugs,
    double plan_init_time, pb::IntentionObject* object_debug) {
  DCHECK(object_debug != nullptr);
  for (const auto& repulsion_meta_data : repulsion_meta_datas) {
    auto* mutable_repulsion_meta_data_debug =
        object_debug->add_repulsion_meta_datas();
    *mutable_repulsion_meta_data_debug =
        repulsion_meta_data.ToProto(plan_init_time);
  }
  for (const auto& [agent_reasoner_id, reasoning_debug_str] :
       repulsion_reasoning_debugs) {
    if (reasoning_debug_str.empty()) {
      continue;
    }
    auto* mutable_repulsion_reasoning_debug =
        object_debug->add_repulsion_reasoning_debugs();
    *(mutable_repulsion_reasoning_debug->mutable_debug_str()) =
        reasoning_debug_str;
    mutable_repulsion_reasoning_debug->set_reasoner_id(agent_reasoner_id);
  }
}

#pragma GCC diagnostic push
// for deprecated tracked_blocking_state
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
// Updates the object debug inside intention generator.
void UpdateObjectDebug(const std::map<TypedObjectId, ObjectIntentionInfo>&
                           object_intention_info_map,
                       const double plan_init_time,
                       pb::IntentionGeneratorDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  for (const auto& [object_id, intention_info] : object_intention_info_map) {
    pb::IntentionObject object_debug;
    object_debug.set_object_id(object_id.id);
    object_debug.set_is_cz(object_id.is_construction_zone());
    object_debug.set_is_static(
        intention_info.trajectory_intention_data.is_static);
    const LateralBlockingStateMetaData& agent_meta_data =
        intention_info.object_reasoning_info.blocking_state_data();
    *object_debug.mutable_tracked_blocking_info() =
        agent_meta_data.tracked_blocking_info;
    object_debug.set_global_blocking_state(
        agent_meta_data.global_blocking_state);
    object_debug.set_tracked_blocking_state(
        agent_meta_data.tracked_blocking_info.blocking_state());
    *object_debug.mutable_reasoning_info() = agent_meta_data.reasoning_info;

    for (const auto& range : agent_meta_data.effective_intention_index_ranges) {
      auto effective_range_iter = object_debug.add_effective_range();
      effective_range_iter->set_start_index(range.start_pos);
      effective_range_iter->set_end_index(range.end_pos);
    }
    if (agent_meta_data.backup_intention_meta_data.has_value()) {
      for (const auto& range :
           agent_meta_data.backup_intention_meta_data->nudge_index_ranges) {
        auto backup_intention_range_iter =
            object_debug.add_backup_intention_range();
        backup_intention_range_iter->set_start_index(range.start_pos);
        backup_intention_range_iter->set_end_index(range.start_pos);
      }
    }

    for (const auto& blocking_info :
         agent_meta_data.trajectory_blocking_infos) {
      *object_debug.add_trajectory_blocking_infos() = blocking_info;
    }

    const TrajectoryIntentionMetaData& trajectory_meta_data =
        intention_info.trajectory_intention_data;
    object_debug.set_trajectory_type(trajectory_meta_data.intention_type);
    object_debug.set_reasoner_id(trajectory_meta_data.reasoner_id);
    object_debug.set_blocking_sequence_type(
        trajectory_meta_data.blocking_sequence_type);
    for (const auto& sequence : trajectory_meta_data.blocking_sequences) {
      *object_debug.add_blocking_sequences() = sequence;
    }
    // Populate repulsion meta data debug.
    UpdateRepulsionMetaDataDebug(intention_info.repulsion_meta_datas,
                                 intention_info.repulsion_reasoning_debugs,
                                 plan_init_time, &object_debug);
    if (trajectory_meta_data.yield_intention_data.has_value()) {
      object_debug.mutable_yield_intention_data()->CopyFrom(
          trajectory_meta_data.yield_intention_data.value());
    }
    if (!trajectory_meta_data.debug_str.empty()) {
      object_debug.set_debug_str(trajectory_meta_data.debug_str);
    }
    object_debug.mutable_req_lat_gap_for_pass_state()
        ->set_critical_required_lateral_gap(
            trajectory_meta_data.required_lat_gap_for_pass_state
                .critical_required_lateral_gap);
    object_debug.mutable_req_lat_gap_for_pass_state()
        ->set_comfort_required_lateral_gap(
            trajectory_meta_data.required_lat_gap_for_pass_state
                .comfort_required_lateral_gap);
    (*debug->mutable_object_intentions())[object_id.id] = object_debug;
  }
}
#pragma GCC diagnostic pop

// Update debug for speed bound profiles.
void UpdateSpeedBoundDebug(const speed::Profile& lower_bound_speed,
                           const speed::Profile& upper_bound_speed,
                           const EgoInLaneParams& ego_state,
                           pb::IntentionGeneratorDebug* debug) {
  if (debug == nullptr) {
    return;
  }
  // Gets the relative state with ego vehicle for speed bound profile.
  const double plan_init_time = math::Ms2Sec(ego_state.plan_start_timestamp_ms);
  const double plan_init_position = ego_state.arclength_m;
  const auto AddToProto = [plan_init_time, plan_init_position](
                              const speed::Profile& profile,
                              planner::speed::pb::Profile* pb_profile) {
    pb_profile->mutable_states()->Reserve(profile.size());
    for (const auto& state : profile) {
      auto* pb_state = pb_profile->add_states();
      pb_state->set_t(state.t - plan_init_time);
      pb_state->set_x(state.x - plan_init_position);
      pb_state->set_v(state.v);
      pb_state->set_a(state.a);
      pb_state->set_j(state.j);
    }
  };
  AddToProto(lower_bound_speed, debug->mutable_speed_lower_bound());
  AddToProto(upper_bound_speed, debug->mutable_speed_upper_bound());
}

std::optional<math::geometry::PolylineCurve2d> GetTruncatedNominalPath(
    double ego_arclength_m,
    const math::geometry::PolylineCurve2d& nominal_path) {
  double start_pos =
      math::Clamp(ego_arclength_m, 0.0, nominal_path.GetTotalArcLength());
  double end_pos = nominal_path.GetTotalArcLength();

  if (end_pos - start_pos < constants::kPathIntervalInMeter) {
    return std::nullopt;
  }

  return math::geometry::PolylineCurve2d(
      nominal_path.GetSkeletonInRange(start_pos, end_pos));
}

}  // namespace

std::vector<path::PathReasoningResult> IntentionGenerator::Generate(
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const RobotStateSnapshot& robot_state_snapshot,
    const speed::Profile& lower_bound_speed,
    const speed::Profile& upper_bound_speed,
    const path::SemanticContextResult& semantic_context_result,
    const DrivableSpaceCorridor& search_corridor,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const std::optional<path::PullOverStatusMetaData>&
        optional_pullover_status_meta,
    const std::optional<math::geometry::Point2d>& reverse_driving_destination,
    const pb::TrajectoryGuiderOutput* trajectory_guider_output,
    const std::unordered_set<int64_t>& ignore_agent_ids,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const speed::pb::SpeedSeed& last_speed_seed,
    const pb::PathReasonerSeed& last_path_reasoner_seed,
    const path::NudgeMotionChecker* nudge_motion_checker,
    const bool is_high_crossing_vru_scenario,
    path::ConstraintManager& geo_guidance_constraint_manager,
    const std::string& current_region, pb::IntentionGeneratorDebug* debug) {
  TRACE_EVENT_SCOPE(planner, DecoupledForwardManeuver_IntentionGenerator);
  const math::pb::Side intended_lat_move_direction =
      path::CalcActiveLateralMoveDirection(
          behavior_type_, lane_keep_behavior_type_, lane_change_execution_info,
          semantic_context_result.traffic_rule_reasoning_info);
  const path::ActiveLatGapAdjustMetaData active_lat_gap_adjust_meta{
      intended_lat_move_direction,
      robot_state_snapshot.rear_axle_position(),
      robot_state_snapshot.speed(),
      robot_state_snapshot.heading(),
      DCHECK_NOTNULL(lane_sequence_info.lane_sequence_result().current_lane)
          ->center_line(),
      behavior_type_};
  const planner::pb::PullOverJumpInType pullover_jump_in_type =
      (optional_pullover_status_meta.has_value() &&
       optional_pullover_status_meta->jump_in_guidance.has_value())
          ? optional_pullover_status_meta->jump_in_guidance->jump_in_type()
          : planner::pb::PullOverJumpInType::INVALID_TYPE;

  std::optional<math::geometry::PolylineCurve2d> truncated_reference_path =
      GetTruncatedNominalPath(
          lane_sequence_info.robot_in_lane_param().arclength_m,
          lane_sequence_info.lane_sequence_geometry().nominal_path);

  path::EgoContextInfo ego_context_info{
      lane_sequence_info.robot_in_lane_param(),
      semantic_context_result.scenario_identify_result,
      semantic_context_result.traffic_rule_reasoning_info,
      semantic_context_result.lane_change_info,
      search_corridor,
      lane_keep_behavior_type_,
      behavior_type_,
      nudge_motion_checker,
      active_lat_gap_adjust_meta,
      geo_guidance_constraint_manager.lateral_clearance_data(),
      pullover_jump_in_type,
      std::move(truncated_reference_path),
      lane_sequence_info.lane_sequence_geometry().nominal_path,
      lower_bound_speed,
      upper_bound_speed,
      reusable_last_speed_profile,
      config_.intention_searcher_config()
          .speed_decider_config()
          .buffer_config(),
      last_path_reasoner_seed,
      optional_pullover_status_meta,
      &geo_guidance_constraint_manager};

  TrajectoryReasoner trajectory_reasoner(
      ego_context_info,
      config_.path_reasoning_config().nudge_dynamic_objects());

  pb::PathReasonerSeed path_reasoner_seed;

  const std::map<TypedObjectId, ObjectIntentionInfo> object_intention_info_map =
      GenerateObjectIntentionInfoMap(
          lane_sequence_info.robot_in_lane_param(), robot_state_snapshot,
          semantic_context_result.object_reasoning_info_map,
          semantic_context_result.scenario_identify_result,
          semantic_context_result.traffic_rule_reasoning_info,
          semantic_context_result.frame_analyzer_results,
          /*expected_intention_map=*/{}, ignore_agent_ids,
          lane_change_execution_info, reusable_last_path,
          reusable_last_speed_profile, trajectory_reasoner, path_reasoner_seed);
  const std::map<TypedObjectId, ObjectIntentionInfo>
      imaginary_object_intention_info_map = GenerateObjectIntentionInfoMap(
          lane_sequence_info.robot_in_lane_param(), robot_state_snapshot,
          semantic_context_result.imaginary_object_reasoning_info_map,
          semantic_context_result.scenario_identify_result,
          semantic_context_result.traffic_rule_reasoning_info,
          semantic_context_result.frame_analyzer_results,
          /*expected_intention_map=*/{}, ignore_agent_ids,
          lane_change_execution_info, reusable_last_path,
          reusable_last_speed_profile, trajectory_reasoner, path_reasoner_seed);
  tbb::task_group task_group;
  if (debug != nullptr) {
    UpdateSpeedBoundDebug(lower_bound_speed, upper_bound_speed,
                          lane_sequence_info.robot_in_lane_param(), debug);
    task_group.run([&] {
      UpdateObjectDebug(
          object_intention_info_map,
          math::Ms2Sec(
              lane_sequence_info.robot_in_lane_param().plan_start_timestamp_ms),
          debug);
    });
  }

  // Use pull over destination to clip planning horizon range.
  const double ego_arclength_m =
      lane_sequence_info.robot_in_lane_param().arclength_m;
  const double path_length =
      upper_bound_speed.back().x - upper_bound_speed.front().x;
  std::optional<double> optional_destination_arclength_m;
  const std::optional<math::geometry::Point2d> pull_over_destination =
      optional_pullover_status_meta.has_value()
          ? std::make_optional(
                optional_pullover_status_meta->pull_over_destination)
          : std::nullopt;
  if (pull_over_destination.has_value()) {
    optional_destination_arclength_m =
        std::max(ego_arclength_m,
                 lane_sequence_info.lane_sequence_geometry()
                         .nominal_path
                         .GetProximity(pull_over_destination.value(),
                                       math::pb::UseExtensionFlag::kForbid)
                         .arc_length -
                     kPlanningHorizonExtendBufferInMeter);
  }

  if (reverse_driving_destination.has_value()) {
    optional_destination_arclength_m =
        std::max(ego_arclength_m,
                 lane_sequence_info.lane_sequence_geometry()
                         .nominal_path
                         .GetProximity(reverse_driving_destination.value(),
                                       math::pb::UseExtensionFlag::kForbid)
                         .arc_length -
                     kPlanningHorizonExtendBufferInMeterWhenReverse);
  }

  double path_planning_end_arclength = ego_arclength_m + path_length;
  if (optional_destination_arclength_m.has_value()) {
    math::UpdateMin(*optional_destination_arclength_m,
                    path_planning_end_arclength);
  }
  const math::Range1d planning_horizon_range = GetExtendedPlanningHorizonRange(
      lane_sequence_info.lane_sequence_geometry().nominal_path,
      math::Range1d(ego_arclength_m, path_planning_end_arclength),
      reverse_driving_destination.has_value()
          ? kPlanningHorizonExtendBufferInMeterWhenReverse
          : kPlanningHorizonExtendBufferInMeter);

  geo_guidance_constraint_manager.AssignNudgeMotionCheck(*nudge_motion_checker);

  auto intention = intention_searcher_.Execute(
      object_intention_info_map, imaginary_object_intention_info_map,
      robot_state_snapshot, lane_sequence_info.robot_in_lane_param(),
      lane_sequence_info.lane_sequence_geometry().nominal_path,
      lane_sequence_info.lane_sequence_result().lane_sequence,
      planning_horizon_range, lower_bound_speed, upper_bound_speed,
      semantic_context_result.traffic_rule_reasoning_info,
      semantic_context_result.scenario_identify_result,
      semantic_context_result.frame_analyzer_results, search_corridor,
      trajectory_guider_output, reusable_last_path, reusable_last_speed_profile,
      last_speed_seed, optional_destination_arclength_m, trajectory_reasoner,
      geo_guidance_constraint_manager, pull_over_destination,
      path_reasoner_seed, current_region, is_high_crossing_vru_scenario,
      debug ? debug->mutable_intention_searcher_debug() : nullptr);

  task_group.wait();
  return intention;
}

std::map<TypedObjectId, ObjectIntentionInfo>
IntentionGenerator::GenerateObjectIntentionInfoMap(
    const EgoInLaneParams& /* ego_state */,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::map<TypedObjectId, path::ObjectReasoningInfo>&
        object_reasoning_info_map,
    const path::ScenarioIdentifierResult& /* scenario_identify_result */,
    const pb::TrafficRuleReasoningInfoDebug& /* traffic_rule_reasoning_info */,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    const std::unordered_map<TypedObjectId, ExpectedIntentionMetaData>&
        object_expected_intention_map,
    const std::unordered_set<int64_t>& ignore_agent_ids,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const std::optional<pb::Path>& reusable_last_path,
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const TrajectoryReasoner& trajectory_reasoner,
    pb::PathReasonerSeed& path_reasoner_seed) const {
  TRACE_EVENT_SCOPE(planner, IntentionGenerator_GenerateObjectIntentionInfoMap);
  std::map<TypedObjectId, ObjectIntentionInfo> object_intention_info_map;
  std::map<TypedObjectId, pb::PathReasonerSeedPerAgent> path_reasoner_seed_map;
  // Create a mutex that will protect map.
  std::mutex map_mutex;
  tbb::parallel_for(
      0, static_cast<int>(object_reasoning_info_map.size()),
      [&robot_state_snapshot, &object_reasoning_info_map,
       &frame_analyzer_results, &object_expected_intention_map,
       &ignore_agent_ids, &lane_change_execution_info, &reusable_last_path,
       &reusable_last_speed_profile, &trajectory_reasoner,
       &object_intention_info_map, &path_reasoner_seed_map,
       &map_mutex](int agent_index) {
        auto it = object_reasoning_info_map.begin();
        std::advance(it, agent_index);
        const auto& [object_id, object_info] = *it;
        const auto expected_intention_iter =
            object_expected_intention_map.find(object_id);
        ExpectedIntentionMetaData expected_intention =
            expected_intention_iter != object_expected_intention_map.end()
                ? expected_intention_iter->second
                : ExpectedIntentionMetaData{
                      std::nullopt, speed::pb::SpeedDecision::NOT_DECIDED};
        TrajectoryIntentionMetaData trajectory_intention_data;
        std::vector<RepulsionMetaData> repulsion_meta_datas;
        std::unordered_map<pb::AgentReasonerId, std::string>
            repulsion_reasoning_debugs;
        pb::PathReasonerSeedPerAgent path_reasoner_seed_per_agent;
        if (object_info.is_construction_zone()) {
          trajectory_intention_data =
              trajectory_reasoner.GetTrajectoryIntentionMetaData(
                  *object_info.cz_state_ptr(), object_info.required_lat_gap(),
                  object_info.required_lat_gap_for_pass_state(),
                  object_info.blocking_state_data(),
                  object_info.tracked_sl_boundary(),
                  path_reasoner_seed_per_agent);
          repulsion_meta_datas = trajectory_reasoner.GetRepulsionMetaData(
              robot_state_snapshot, *object_info.cz_state_ptr(),
              path_reasoner_seed_per_agent);
        } else {
          const bool should_ignore_by_upstream =
              ignore_agent_ids.find(object_id.id) != ignore_agent_ids.end();
          trajectory_intention_data =
              FLAGS_planning_enable_object_occupancy_state
                  ? trajectory_reasoner.GetTrajectoryIntentionMetaData(
                        *object_info.object_occupancy_state_ptr(),
                        object_info.required_lat_gap(),
                        object_info.required_lat_gap_for_pass_state(),
                        object_info.blocking_state_data(),
                        object_info.tracked_sl_boundary(),
                        lane_change_execution_info, should_ignore_by_upstream,
                        object_info.yield_intention_data(),
                        path_reasoner_seed_per_agent)
                  : trajectory_reasoner.GetTrajectoryIntentionMetaData(
                        *object_info.agent_state_ptr(),
                        object_info.required_lat_gap(),
                        object_info.required_lat_gap_for_pass_state(),
                        object_info.blocking_state_data(),
                        object_info.tracked_sl_boundary(),
                        lane_change_execution_info, should_ignore_by_upstream,
                        object_info.yield_intention_data(),
                        path_reasoner_seed_per_agent);
          repulsion_meta_datas =
              FLAGS_planning_enable_object_occupancy_state
                  ? trajectory_reasoner.GetRepulsionMetaDatas(
                        robot_state_snapshot,
                        *object_info.object_occupancy_state_ptr(),
                        object_info.required_lat_gap(),
                        object_info.blocking_state_data(),
                        object_info.trajectory_blocking_state_data_map(),
                        object_info.yield_intention_data(), reusable_last_path,
                        reusable_last_speed_profile, lane_change_execution_info,
                        frame_analyzer_results, trajectory_intention_data,
                        path_reasoner_seed_per_agent,
                        repulsion_reasoning_debugs)
                  : trajectory_reasoner.GetRepulsionMetaDatas(
                        robot_state_snapshot, *object_info.agent_state_ptr(),
                        object_info.required_lat_gap(),
                        object_info.blocking_state_data(),
                        object_info.trajectory_blocking_state_data_map(),
                        object_info.yield_intention_data(), reusable_last_path,
                        reusable_last_speed_profile, lane_change_execution_info,
                        frame_analyzer_results, trajectory_intention_data,
                        path_reasoner_seed_per_agent,
                        repulsion_reasoning_debugs);
        }
        ObjectIntentionInfo object_intention_info(
            object_info, std::move(trajectory_intention_data),
            std::move(expected_intention), std::move(repulsion_meta_datas),
            std::move(repulsion_reasoning_debugs));

        std::lock_guard<std::mutex> l(map_mutex);
        object_intention_info_map.emplace(object_id,
                                          std::move(object_intention_info));
        path_reasoner_seed_map.emplace(object_id,
                                       std::move(path_reasoner_seed_per_agent));
      });  // End for loop object_reasoning_info_map
  // Dump path reasoner seed.
  for (const auto& [object_id, path_reasoner_seed_per_agent] :
       path_reasoner_seed_map) {
    path_reasoner_seed.mutable_path_reasoner_seed_map()->insert(
        {object_id.ToString(), path_reasoner_seed_per_agent});
  }
  return object_intention_info_map;
}

}  // namespace planner
