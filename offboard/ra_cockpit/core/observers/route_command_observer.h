#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_COMMAND_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_COMMAND_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "planner_protos/state/planner.pb.h"

namespace remote_assist {

class RouteCommandObserver : public IDataObserver {
 public:
  using RouteCommandCallback = std::function<void(
      std::shared_ptr<routing::pb::RouteCommand>, DataChannel)>;

  explicit RouteCommandObserver(RouteCommandCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  RouteCommandCallback route_command_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_COMMAND_OBSERVER_H_
