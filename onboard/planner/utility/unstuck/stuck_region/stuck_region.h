#ifndef ONBOARD_PLANNER_UTILITY_UNSTUCK_STUCK_REGION_STUCK_REGION_H_
#define ONBOARD_PLANNER_UTILITY_UNSTUCK_STUCK_REGION_STUCK_REGION_H_

#include <limits>
#include <unordered_map>
#include <vector>

#include "geometry/model/polygon_with_cache.h"
#include "math/range.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/object_source_type.pb.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {

// Struct StuckObjectMeta stores meta data that is useful to construct stuck
// region. Each stuck object corresponds to real object from TrackedObject,
// ConstructionZone, or a preliminary StuckRegion.
struct StuckObjectMeta {
  // Constructor.
  StuckObjectMeta() = default;
  StuckObjectMeta(int64_t region_id, const math::Range1d& s_range,
                  const math::Range1d& fully_s_range,
                  const math::Range1d& l_range,
                  const math::geometry::PolygonWithCache2d& contour_in,
                  bool is_preliminary_stuck_region_in)
      : id(region_id, pb::ObjectSourceType::kConstructionZone),
        s_range(s_range),
        fully_s_range(fully_s_range),
        l_range(l_range),
        contour(&contour_in),
        is_preliminary_stuck_region(is_preliminary_stuck_region_in) {}
  StuckObjectMeta(
      int64_t object_id, pb::ObjectSourceType::Enum type,
      const math::Range1d& s_range, const math::Range1d& fully_s_range,
      const math::Range1d& l_range, double left_inlane_clearance,
      double right_inlane_clearance, double speed,
      const std::vector<const pnc_map::Lane*>& associated_lanes_in,
      const math::geometry::PolygonWithCache2d& contour_in,
      pb::BlockingType blocking_type_in, int64_t temp_parked_duration_in = 0,
      const std::optional<voy::perception::ObjectType>& perception_type_in =
          std::nullopt,
      const bool is_restored_from_last_seed_in = false)
      : id(object_id, type),
        s_range(s_range),
        fully_s_range(fully_s_range),
        l_range(l_range),
        left_inlane_clearance(left_inlane_clearance),
        right_inlane_clearance(right_inlane_clearance),
        speed(speed),
        associated_lanes(associated_lanes_in),
        contour(&contour_in),
        blocking_type(blocking_type_in),
        temp_parked_duration(temp_parked_duration_in),
        perception_type(perception_type_in),
        is_restored_from_last_seed(is_restored_from_last_seed_in) {}

  // Object id.
  TypedObjectId id;
  // Object’s projection range in the S direction.
  math::Range1d s_range{};
  // Object's fully range in the S direction.
  math::Range1d fully_s_range{};
  // Object's projection range in the L direction.
  math::Range1d l_range{};
  // Clearance between object's inlane part and left lane marking, considering
  // the lane marking violation.
  double left_inlane_clearance = 0.0;
  // Clearance between object's inlane part and right lane marking, considering
  // the lane marking violation.
  double right_inlane_clearance = 0.0;
  // The object speed in mps.
  double speed = 0.0;
  // Associated lanes of the object.
  std::vector<const pnc_map::Lane*> associated_lanes = {};
  // Object’s contour, it should initialize in the constructor. It's guaranteed
  // to be not nullptr.
  const math::geometry::PolygonWithCache2d* contour = nullptr;
  // Object's blocking type.
  pb::BlockingType blocking_type = pb::BlockingType::kInvalid;
  // Temp-parked duration for kTempParked blockage.
  int64_t temp_parked_duration = 0;
  // Perception object type, it will be std::nullopt if the the blocking type is
  // kConstructionZone.
  std::optional<voy::perception::ObjectType> perception_type = std::nullopt;

  //
  // Special attributes for post-grouping.
  //
  // Flag indicating if it is a stuck region.
  bool is_preliminary_stuck_region = false;
  // Flag indicating if it is an object proceed during post grouping, Default is
  // false. Only set it to true for post-grouping objects which are out of lane
  // sequence.
  bool is_post_group_object = false;
  // Flags indicating if the stuck region is restored from last seed.
  bool is_restored_from_last_seed = false;
};

// Struct region stores meta data of a series of stuck objects which could be
// TrackedObject or ConstructionZone. Those stuck object is supposed to be a
// group due to ego cannot pass through them. Each stuck region belongs to a
// specific lane sequence and it has an identifier corresponding to the lane
// sequence.
class StuckRegion {
 public:
  // Constructor.
  StuckRegion() = default;
  StuckRegion(const std::vector<const pnc_map::Lane*>& lane_sequence,
              const std::vector<StuckObjectMeta>& stuck_meta_list,
              const math::geometry::PolylineCurve2d& reference_line,
              double freshness = 1.0);

  // Writes information into stuck region defined in protobuf.
  void ToProto(pb::StuckRegion* stuck_region) const;

  // This function takes a new region as input and merges it into the existing
  // stuck region.
  bool Merge(const math::geometry::PolylineCurve2d& reference_line,
             const StuckRegion& new_region);

  // Merge input stuck meta list into current region.
  void Merge(const math::geometry::PolylineCurve2d& reference_line,
             const std::vector<StuckObjectMeta>& stuck_meta_list);

  // Accessors.
  const std::vector<const pnc_map::Lane*>& identifier() const {
    return lane_sequence_identifier_;
  }
  const math::Range1d& longitudinal_range() const {
    return longitudinal_blocking_range_;
  }
  const math::Range1d& fully_longitudinal_range() const {
    return fully_longitudinal_blocking_range_;
  }
  const math::Range1d& lateral_range() const { return lateral_blocking_range_; }
  const std::unordered_map<TypedObjectId, StuckObjectMeta>& stuck_objects()
      const {
    return stuck_objects_;
  }
  const math::geometry::PolygonWithCache2d& contour() const { return contour_; }
  const std::vector<const pnc_map::Lane*>& occupied_lanes() const {
    return occupied_lanes_;
  }
  double left_inlane_clearance() const { return left_inlane_clearance_; }
  double right_inlane_clearance() const { return right_inlane_clearance_; }
  double freshness() const { return freshness_; }
  bool has_ra_confirmed_blockage() const { return has_ra_confirmed_blockage_; }
  bool is_restored_from_last_seed() const {
    return is_restored_from_last_seed_;
  }

  // Returns the dominant blocking object id on the lane sequence.
  std::optional<TypedObjectId> GetDominantObjectId() const;

  // Returns the blocking type of the first blockage whose longitudinal start
  // pos is farther than the given start pos.
  pb::BlockingType GetFirstBlockageType(double start_pos = 0.0) const;

  // Returns the max temp-parked duration for the stuck region. In the stuck
  // region, there might be several temp-parked vehicles. This function returns
  // the max temp-parked duration among them.
  int64_t GetMaxTempParkedDuration() const;

 private:
  // Updates both longitudinal and lateral blocking range of the region.
  void UpdateBlockingRangeAndClearance(const StuckObjectMeta& meta);

  // Merges new stuck objects into current stuck region.
  void MergeStuckObjects(const StuckRegion& new_region);

  // Merges current region with new lateral & longitudinal blocking ranges.
  void MergeBlockingRangeAndClearance(
      const math::Range1d& new_lateral_range,
      const math::Range1d& new_longitudinal_range,
      const math::Range1d& new_fully_longitudinal_range,
      double new_left_inlane_clearance, double new_right_inlane_clearance);

  // Merges current region contour with new region contour.
  void MergeContour(const math::geometry::PolygonWithCache2d& new_contour);

  //
  // Data member.
  //
  // The original lane sequence that related to the stuck region.
  std::vector<const pnc_map::Lane*> original_lane_sequence_;

  // The identifier indicates which lane sequence the stuck region belongs to.
  // Note that it starts from the original lane sequencen and ends at the last
  // stuck region associated lane.
  std::vector<const pnc_map::Lane*> lane_sequence_identifier_;

  // Stuck objects in the stuck region.
  std::unordered_map<TypedObjectId, StuckObjectMeta> stuck_objects_;

  // Concave polygon that wraps all stuck objects in the region.
  math::geometry::PolygonWithCache2d contour_;

  // Longitudinal blocking range is the start arclength and end arclength of
  // region's in-lane part on the center line of the lane sequence.
  math::Range1d longitudinal_blocking_range_ = {
      std::numeric_limits<double>::max(),
      std::numeric_limits<double>::lowest()};

  // Fully longitudinal blocking range is the start arclength and end arclength
  // of region's fully contour on the center line of the lane sequence.
  math::Range1d fully_longitudinal_blocking_range_ = {
      std::numeric_limits<double>::max(),
      std::numeric_limits<double>::lowest()};

  // Lateral blocking range on the lane sequence. 0.0 refers to the center line,
  // and the left side is positive(+) right side is negative(-).
  math::Range1d lateral_blocking_range_ = {
      std::numeric_limits<double>::max(),
      std::numeric_limits<double>::lowest()};

  // Lanes occupied by stuck region.
  std::vector<const pnc_map::Lane*> occupied_lanes_;

  // inlane clearance to the left and right lane marking considering
  double left_inlane_clearance_ = 0.0;
  double right_inlane_clearance_ = 0.0;

  // Freshness indicates the ratio of the new stuck object (not in the last
  // stuck region) in the whole stuck region.
  double freshness_ = 1.0;

  // Flags indicates if the stuck region has RA confirmed blockage.
  bool has_ra_confirmed_blockage_ = false;

  // Flags indicates if the stuck region is restored from last seed.
  bool is_restored_from_last_seed_ = false;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_UTILITY_UNSTUCK_STUCK_REGION_STUCK_REGION_H_
