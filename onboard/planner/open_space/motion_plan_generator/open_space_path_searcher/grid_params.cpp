#include "planner/open_space/motion_plan_generator/open_space_path_searcher/grid_params.h"

#include <utility>

namespace planner {

namespace open_space {

GridParams::GridParams(const MotionPlanROI& motion_plan_roi, double xy_res_m_in)
    : xy_res_m(xy_res_m_in) {
  x_cell_size =
      static_cast<int>(std::ceil(motion_plan_roi.x_length() / xy_res_m));
  y_cell_size =
      static_cast<int>(std::ceil(motion_plan_roi.y_length() / xy_res_m));
  DCHECK_GT(x_cell_size, 0);
  DCHECK_GT(y_cell_size, 0);

  // Correct x and y range length with number of grids and sample resolution.
  x_range_length = x_cell_size * xy_res_m;
  y_range_length = y_cell_size * xy_res_m;

  // Generate the x, y range with corrected range length.
  const double x_range_start_pos = motion_plan_roi.x_range().start_pos;
  const double y_range_start_pos = motion_plan_roi.y_range().start_pos;
  x_range =
      math::Range1d(x_range_start_pos, x_range_start_pos + x_range_length);
  y_range =
      math::Range1d(y_range_start_pos, y_range_start_pos + y_range_length);

  // Max grid vertices number is one more than the grid cell number.
  x_vertices_size = x_cell_size + 1;
  y_vertices_size = y_cell_size + 1;
}

}  // namespace open_space

}  // namespace planner
