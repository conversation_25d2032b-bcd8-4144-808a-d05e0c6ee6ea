#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_AGENT_INTENTION_SEARCHER_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_AGENT_INTENTION_SEARCHER_H_

#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <unordered_set>
#include <vector>

#include "planner/path/path_solver/path_input.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/drivable_tunnel_searcher.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/sampling_based_speed_decider.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/intention_generator_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/frame_analyzer_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/scenario_identifier/scenario_identifier_util.h"
#include "planner/path/reasoning/agent_intention/trajectory_reasoner/trajectory_reasoner.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/traffic_rule_reasoning_info.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

// Stores intermediate and final result associated with a single speed profile's
// decision..
struct IntentionDecisionResult {
  // Result that will be provided to path solver for path generation.
  path::PathReasoningResult path_reasoning_result;
  // Intermediate decision result in path reasoning.
  std::map<TypedObjectId, IntentionResultMetaData> intention_meta_data_map;
  // Index for the corresponding drivable tunnel.
  int drivable_tunnel_index = -1;
};

// Class AgentIntentionSearcher defines the intention generator for tracked
// objects.
class AgentIntentionSearcher {
 public:
  explicit AgentIntentionSearcher(
      const pb::AgentIntentionSearcherConfig& config,
      pb::BehaviorType behavior_type =
          pb::BehaviorType::UNDEFINED_BEHAVIOR_TYPE,
      pb::LaneKeepBehaviorType lane_keep_behavior_type = pb::LK_NA,
      pb::ManeuverType maneuver_type = pb::ManeuverType::DECOUPLED_FORWARD)
      : config_(config),
        behavior_type_(behavior_type),
        lane_keep_behavior_type_(lane_keep_behavior_type),
        tunnel_searcher_(config.tunnel_searcher_config(), behavior_type,
                         maneuver_type),
        speed_decider_(config.speed_decider_config(), behavior_type) {}

  // The main function for agent intention generate.
  // TODO(yongbing) : consider grouping a few args into a struct.
  std::vector<path::PathReasoningResult> Execute(
      const std::map<TypedObjectId, ObjectIntentionInfo>&
          object_intention_info_map,
      const std::map<TypedObjectId, ObjectIntentionInfo>&
          imaginary_object_intention_info_map,
      const RobotStateSnapshot& robot_state_snapshot,
      const EgoInLaneParams& ego_param,
      const math::geometry::PolylineCurve2d& reference_path,
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const math::Range1d& planning_horizon_range,
      const speed::Profile& lower_bound_speed,
      const speed::Profile& upper_bound_speed,
      const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
      const path::ScenarioIdentifierResult& scenario_identify_result,
      const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
      const DrivableSpaceCorridor& drivable_space_corridor,
      const pb::TrajectoryGuiderOutput* trajectory_guider_output,
      const std::optional<pb::Path>& reusable_last_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      const speed::pb::SpeedSeed& last_speed_seed,
      const std::optional<double> optional_destination_arclength_m,
      const TrajectoryReasoner& trajectory_reasoner,
      const path::ConstraintManager& geo_guidance_constraint_manager,
      const std::optional<math::geometry::Point2d>& pull_over_destination,
      const pb::PathReasonerSeed& path_reasoner_seed,
      const std::string& current_region,
      const bool is_high_crossing_vru_scenario,
      pb::AgentIntentionSearcherDebug* debug);

 private:
  // Returns the expected lateral drivable tunnel search space half horizon.
  double GetDrivableTunnelSearchSpaceHalfHorizon(
      const path::ScenarioIdentifierResult& scenario_identify_result) const {
    double drivable_tunnel_search_space_half_horizon =
        kMaxLateralHorizonInMeter;
    math::UpdateMax(scenario_identify_result.max_lateral_space_horizon_m,
                    drivable_tunnel_search_space_half_horizon);
    return drivable_tunnel_search_space_half_horizon;
  }

  // Converts speed decision and tunnel result into path reasoning decision
  // result.
  IntentionDecisionResult GenerateIntenionDecision(
      const STRolloutDecision* speed_decision, const DrivableTunnel& tunnel,
      const std::map<TypedObjectId, ObjectIntentionInfo>&
          object_intention_info_map,
      const std::map<TypedObjectId, ObjectIntentionInfo>&
          imaginary_object_intention_info_map,
      const EgoInLaneParams& ego_param,
      const path::ScenarioIdentifierResult& scenario_identify_result,
      const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
      const math::Range1d& planning_horizon_range,
      const TrajectoryReasoner& trajectory_reasoner,
      const pb::SnapshotIntention::PassState safe_pass_side,
      const math::geometry::PolylineCurve2d& reference_path,
      const std::optional<speed::Profile>& reusable_last_speed_profile,
      const speed::Profile& upper_bound_speed,
      const speed::Profile& lower_bound_speed,
      const pb::PathReasonerSeed& path_reasoner_seed,
      std::unique_ptr<path::ConstraintManager> constraint_manager,
      pb::SearchBasedDecisionResult* search_based_decision_debug,
      pb::SearchBasedDecisionResult::DecisionResult* decision_result_debug);

  // configs.
  pb::AgentIntentionSearcherConfig config_;
  [[maybe_unused]] const pb::BehaviorType behavior_type_;
  // Tries to override homotopy for cross-lane nudge behavior.
  pb::LaneKeepBehaviorType lane_keep_behavior_type_;
  // For tunnel search.
  DrivableTunnelSearcher tunnel_searcher_;
  // For speed decide.
  SamplingBasedSpeedDecider speed_decider_;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_AGENT_INTENTION_SEARCHER_AGENT_INTENTION_SEARCHER_H_
