#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_LINEAR_QUADRATIC_REGULATOR_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_LINEAR_QUADRATIC_REGULATOR_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "common_math_library/matrix.h"

// solve algebraic Riccati equation (DARE) by iterative method
// A_ptr: system dynamic matrix
// B_ptr: control matrix
// Q_ptr: state cost matrix
// R_ptr: control cost matrix
// tolerance: tolerance of the iterative method
// max_num_iteration: maximum number of iteration
// K_ptr: control gain matrix
// num_iteration_ptr: number of iteration
// diff_ptr: difference between two consecutive iteration
void SolveRiccatiEquation(const CML_Matrix* A_ptr, const CML_Matrix* B_ptr,
                          const CML_Matrix* Q_ptr, const CML_Matrix* R_ptr,
                          float tolerance, const uint16_t max_num_iteration,
                          CML_Matrix* K_ptr, uint16_t* num_iteration_ptr,
                          float* diff_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_LINEAR_QUADRATIC_REGULATOR_H_
