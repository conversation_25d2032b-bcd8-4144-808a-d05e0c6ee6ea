#ifndef OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_MATRIX_H_
#define OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_MATRIX_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>
#include <stdint.h>

#include "common_math_library/assert_cfg.h"

// GSL matrix library
typedef struct {
  uint8_t rows;       // number of rows in the matrix
  uint8_t cols;       // number of columns in the matrix
  uint16_t max_size;  // maximum memory size allocated for this matrix
  float* data_ptr;    // pointer location where the data is stored
} CML_Matrix;

// create matrix and allocates data with global scope
#define CML_MatrixCreate(name, rows, cols)                                  \
  float matrix_data_##name[(uint32_t)(rows) * (uint32_t)(cols)];            \
  CML_Matrix matrix_struct_##name = {                                       \
      (uint8_t)(rows), (uint8_t)(cols),                                     \
      (uint16_t)((uint32_t)(rows) * (uint32_t)(cols)), matrix_data_##name}; \
  CML_Matrix* name = &matrix_struct_##name;

// get matrix element
#define CML_MatrixGetElement(name, row, col) \
  (name)->data_ptr[(uint32_t)(col) + ((uint32_t)(row) * (name)->cols)]

// set matrix element
#define CML_MatrixSetElement(name, row, col, value) \
  (name)->data_ptr[(uint32_t)(col) + ((uint32_t)(row) * (name)->cols)] = (value)

// set new matrix size
#define CML_MatrixSetSize(name, new_rows, new_cols)                      \
  if ((uint32_t)(new_rows) * (uint32_t)(new_cols) <= (name)->max_size) { \
    (name)->rows = (uint8_t)(new_rows);                                  \
    (name)->cols = (uint8_t)(new_cols);                                  \
  } else {                                                               \
    CML_ASSERT(false);                                                   \
  }

// check if matrix is empty
#define CML_MatrixIsEmpty(name) \
  (((name)->cols == (uint8_t)0U) || ((name)->rows == (uint8_t)0U))

// set all matrix elements to a value
extern void CML_MatrixSetAll(CML_Matrix* matrix_ptr, uint32_t row, uint32_t col,
                             float value);

// set all matrix elements to zero
extern void CML_MatrixSetZero(CML_Matrix* matrix_ptr);

// set matrix to identity matrix
extern void CML_MatrixSetIdentity(CML_Matrix* matrix_ptr, uint32_t size);

// matrix addition c = a + b, two matrices a and b with same dimensions
// and store the result in matrix c
// the matrix a or b can be the same as matrix c, a = a + b or b = a + b
extern void CML_MatrixAdd(CML_Matrix* matrix_c_ptr,
                          const CML_Matrix* matrix_a_ptr,
                          const CML_Matrix* matrix_b_ptr);

// matrix subtraction c = a - b, two matrices a and b with same dimensions
// and store the result in matrix c
// the matrix a can be the same as matrix c, a = a - b
extern void CML_MatrixSub(CML_Matrix* matrix_c_ptr,
                          const CML_Matrix* matrix_a_ptr,
                          const CML_Matrix* matrix_b_ptr);

// matrix multiplication, c = a * b
void CML_MatrixMul(CML_Matrix* matrix_c_ptr, const CML_Matrix* matrix_a_ptr,
                   const CML_Matrix* matrix_b_ptr);

// multiplies the elements of matrix a by the constant factor
extern void CML_MatrixScale(CML_Matrix* matrix_a_ptr, float factor);

// transpose matrix, at = transpose(a)
extern void CML_MatrixTranspose(CML_Matrix* matrix_at_ptr,
                                const CML_Matrix* matrix_a_ptr);

// matrix inversion
extern bool CML_MatrixInv(CML_Matrix* matrix_res_ptr, CML_Matrix* matrix_a_ptr);

// the maximum value in the matrix
extern float CML_MatrixMax(const CML_Matrix* matrix_ptr);

// the minimum value in the matrix
extern float CML_MatrixMin(const CML_Matrix* matrix_ptr);

// copy data from src matrix to dest matrix
extern void CML_MatrixCopy(CML_Matrix* matrix_dest_ptr,
                           const CML_Matrix* matrix_src_ptr);

// swap data from matrix a to matrix b
extern void CML_MatrixSwap(CML_Matrix* matrix_a_ptr, CML_Matrix* matrix_b_ptr);

// print matrix, just for debugging
extern void CML_MatrixPrint(const CML_Matrix* matrix_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_COMMON_MATH_LIBRARY_MATRIX_H_
