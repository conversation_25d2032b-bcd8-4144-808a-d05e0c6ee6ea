#include "planner/world_model/immediate_pull_over/immediate_pull_over_state_manager.h"

#include <set>
#include <string>
#include <vector>

#include "planner/behavior/util/lane_common/lane_sequence_iterator.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/constants.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner_protos/immediate_pull_over.pb.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

namespace {
// Returns subscribed cloud cells from assist responses.
std::optional<size_t> GetSubscribedCloudCellResponseIndex(
    const std::vector<pb::AssistResponse>& assist_responses) {
  if (assist_responses.empty()) {
    return std::nullopt;
  }

  for (size_t i = 0; i < assist_responses.size(); ++i) {
    if (assist_responses[i].has_match_stop_cell_response()) {
      return std::make_optional<size_t>(i);
    }
  }

  return std::nullopt;
}

}  // namespace

void ImmediatePullOverStateManager::LoadState(
    const pb::ImmediatePullOverStateManagerState& from_pb) {
  if (from_pb.has_immediate_pull_over_point()) {
    const auto& pb_point = from_pb.immediate_pull_over_point();
    immediate_pull_over_point_ =
        math::geometry::Point2d(pb_point.x(), pb_point.y());
  } else {
    immediate_pull_over_point_ = std::nullopt;
  }
  immediate_pullover_triggered_timestamp_ =
      from_pb.immediate_pullover_triggered_timestamp() != 0
          ? from_pb.immediate_pullover_triggered_timestamp()
          : -1;
  last_timestamp_ =
      from_pb.last_timestamp() != 0 ? from_pb.last_timestamp() : -1;
  accumulated_distance_ = from_pb.accumulated_distance();
  initial_dist_to_destination_ = from_pb.initial_dist_to_destination();
  immediate_pullover_source_ = from_pb.immediate_pullover_source();
  immediate_pullover_state_ = from_pb.immediate_pullover_state();
  stage_manager_.set_proposal_request_timestamp(
      from_pb.proposal_request_timestamp());
}

void ImmediatePullOverStateManager::DumpState(
    pb::ImmediatePullOverStateManagerState& to_pb) const {
  if (immediate_pull_over_point_.has_value()) {
    auto* pb_point = to_pb.mutable_immediate_pull_over_point();
    pb_point->set_x(immediate_pull_over_point_->x());
    pb_point->set_y(immediate_pull_over_point_->y());
  }
  to_pb.set_immediate_pullover_triggered_timestamp(
      immediate_pullover_triggered_timestamp_);
  to_pb.set_last_timestamp(last_timestamp_);
  to_pb.set_accumulated_distance(accumulated_distance_);
  to_pb.set_initial_dist_to_destination(initial_dist_to_destination_);
  to_pb.set_immediate_pullover_source(immediate_pullover_source_);
  to_pb.set_immediate_pullover_state(immediate_pullover_state_);
  to_pb.set_proposal_request_timestamp(
      stage_manager_.proposal_request_timestamp());
}

void ImmediatePullOverStateManager::ResetRedirectingModeStageManagerToIdleStage(
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  stage_manager_.ResetStageManagerToIdleStage(redirecting_imm_pull_over_debug);
}

void ImmediatePullOverStateManager::HandleImmediatePullOverSignal(
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  switch (immediate_pullover_state_) {
    case routing::pb::ImmediatePullOverState::kNotTriggered:
      // Reset or keep in idle stage.
      stage_manager_.ReceiveImmediatePulloverNotTriggerSignal(
          redirecting_imm_pull_over_debug);
      stage_manager_.ResetStageManagerToIdleStage(
          redirecting_imm_pull_over_debug);
      break;
    case routing::pb::ImmediatePullOverState::kTriggered:
      // If currently in idle stage, change to cell-collecting stage; Otherwise
      // keep in current stage.
      stage_manager_.ReceiveImmediatePulloverTriggerSignal(
          redirecting_imm_pull_over_debug);
      break;
    case routing::pb::ImmediatePullOverState::kSuccess:
      // Change to idle stage.
      stage_manager_.CompleteImmediatePullOver(redirecting_imm_pull_over_debug);
      break;
    case routing::pb::ImmediatePullOverState::kFailed:
      // Change to idle stage.
      stage_manager_.ReceiveImmediatePulloverFailedSignal(
          redirecting_imm_pull_over_debug);
      break;
    default:
      DCHECK(false) << "Immediate PullOver State is error.";
      break;
  }
}

void ImmediatePullOverStateManager::HandleCellCollectingSignal(
    const pnc_map::PncMapService* pnc_map_service,
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const voy::Pose& current_pose, const OddInfo& odd_info,
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  stage_manager_.ReceiveCellCollectingSignal(
      pnc_map_service, regional_map, robot_state,
      last_lane_follow_lane_sequence, current_pose,
      FLAGS_planning_enable_cloud_cell_request &&
          (!av_comm::InSimulation() ||
           FLAGS_planning_use_cloud_cell_in_simulation),
      odd_info, redirecting_imm_pull_over_debug);
}

void ImmediatePullOverStateManager::HandleCloudCellSubscription(
    const pnc_map::PncMapService* pnc_map_service,
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const voy::Pose& current_pose,
    const std::vector<pb::AssistResponse>& assist_responses,
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  const std::optional<size_t> subscribed_cloud_cells_index =
      GetSubscribedCloudCellResponseIndex(assist_responses);
  if (subscribed_cloud_cells_index.has_value()) {
    const pb::MatchStopCellResponse& subscribed_cloud_cells =
        assist_responses[subscribed_cloud_cells_index.value()]
            .match_stop_cell_response();
    stage_manager_.SubscribedCloudCells(
        pnc_map_service, regional_map, robot_state,
        last_lane_follow_lane_sequence, current_pose, subscribed_cloud_cells,
        redirecting_imm_pull_over_debug);
  }
}

void ImmediatePullOverStateManager::HandleRouteDirectingSignal(
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const RegionalMap& regional_map,
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  const auto previous_seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::GetMsg(
          PathLastFinishedFrame());
  stage_manager_.TriggerRouteDirecting(
      last_lane_follow_lane_sequence, current_lane, regional_map,
      *previous_seed, redirecting_imm_pull_over_debug);
  UpdateRouteDirectionInfoToSeed();
}

void ImmediatePullOverStateManager::HandleImmediateRightLCSignal(
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const RobotState& robot_state,
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  stage_manager_.TriggerImmediateRightLC(last_lane_follow_lane_sequence,
                                         current_lane, robot_state,
                                         redirecting_imm_pull_over_debug);
}

void ImmediatePullOverStateManager::HandlePullOverSignal(
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const RobotState& robot_state,
    pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug) {
  stage_manager_.HandlePullOverState(last_lane_follow_lane_sequence,
                                     current_lane, robot_state,
                                     redirecting_imm_pull_over_debug);
}

void ImmediatePullOverStateManager::UpdateRouteDirectionInfoToSeed() {
  auto mutable_seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::MutableMsg(
          PathCurrentFrame());
  mutable_seed->Clear();
  const std::optional<LightRegionalPath>& redirecting_regional_path =
      stage_manager_.redirecting_regional_path();
  // Only update if have valid redirecting regional path.
  if (redirecting_regional_path.has_value()) {
    const pb::TurnInfo& turn_info =
        redirecting_regional_path.value()
            .route_cost_factors.next_junction_turn_info();
    pb::RouteDirectionInfo* seed = mutable_seed->mutable_route_direction_info();
    seed->set_road_id(turn_info.road_id());
    seed->set_junction_id(turn_info.junction_id());
    const pb::RouteDirection route_direction =
        ConvertToRouteDirection(turn_info);
    seed->set_direction(route_direction);
  }
}

// Note: Since we introduce stage manager, on each cycle, we assume always
// receive following signals in order. And stage can change accordingly in
// stage mananger.
void ImmediatePullOverStateManager::
    GeneratePDZAndRunImmediatePullOverByRedirectingMode(
        const pnc_map::PncMapService* pnc_map_service,
        const RegionalMap& regional_map, const RobotState& robot_state,
        const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
        const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
        const OddInfo& odd_info,
        const std::vector<pb::AssistResponse>& assist_responses,
        bool is_current_pdz_valid,
        pb::RedirectingImmediatePullOverDebug*
            redirecting_imm_pull_over_debug) {
  if (!ShouldTryUsingRedirectingMode()) {
    ResetRedirectingModeStageManagerToIdleStage(
        redirecting_imm_pull_over_debug);
    return;
  }

  // Reset the state manager to idle stage when the following conditions are met
  // at the same time,
  // 1.Redirecting mode has generated a pdz info;
  // 2.The pdz info is judged as invalid in the current cycle.
  if (stage_manager_.pdz_info().has_value() && !is_current_pdz_valid) {
    ResetRedirectingModeStageManagerToIdleStage(
        redirecting_imm_pull_over_debug);
  }

  stage_manager_.UpdateContextForPDZInfoGenerator(
      GetForbiddenStopTypesForMRC(), IsImmediatePullOverTriggeredByMRC());
  stage_manager_.ClearAndAddCurrentStageToDebug(
      redirecting_imm_pull_over_debug);

  // Set should_use_impo_odd_type to true when immediate pull over is triggered
  // by order service.
  stage_manager_.set_odd_type(ShouldUseImpoOddType()
                                  ? ::pb::OddInfo::ODD_IMPO
                                  : odd_info.odd_info.odd_type());
  stage_manager_.set_immediate_pullover_triggered_timestamp(
      immediate_pullover_triggered_timestamp_);

  // Handles multiple signals in a series.
  HandleImmediatePullOverSignal(redirecting_imm_pull_over_debug);
  HandleCellCollectingSignal(pnc_map_service, regional_map, robot_state,
                             last_lane_follow_lane_sequence, current_pose,
                             odd_info, redirecting_imm_pull_over_debug);
  HandleCloudCellSubscription(pnc_map_service, regional_map, robot_state,
                              last_lane_follow_lane_sequence, current_pose,
                              assist_responses,
                              redirecting_imm_pull_over_debug);
  HandleRouteDirectingSignal(last_lane_follow_lane_sequence, current_lane,
                             regional_map, redirecting_imm_pull_over_debug);
  HandleImmediateRightLCSignal(last_lane_follow_lane_sequence, current_lane,
                               robot_state, redirecting_imm_pull_over_debug);
  HandlePullOverSignal(last_lane_follow_lane_sequence, current_lane,
                       robot_state, redirecting_imm_pull_over_debug);
}

bool ImmediatePullOverStateManager::ShouldTryUsingRedirectingMode() const {
  // The redirecting mode will not be used in the following cases,
  // 1.Immediate pull over is not triggered by dhmi or phmi or mrc;
  // 2.'should_ignore_or_terminate_request_' is true.
  if ((!IsTriggeredByOrderService() && !IsImmediatePullOverTriggeredByMRC()) ||
      should_ignore_or_terminate_request_) {
    return false;
  }

  // Try using redirecting mode in the following cases,
  // 1.Has not immediate pull over point;
  // 2.Has immediate pull over point and is_using_redirecting_mode_ is true.
  return !immediate_pull_over_point_.has_value() || is_using_redirecting_mode_;
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverPDZ(
    const GlobalRouteSolution& global_route_solution,
    const pnc_map::PncMapService* pnc_map_service,
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<ConstructionZone>& construction_zones,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
    const std::vector<pb::AssistResponse>& assist_responses,
    const OddInfo& odd_info, int64_t current_timestamp,
    pb::PullOverProgress pull_over_progress,
    pb::ImmediatePullOverDebug* immediate_pull_over_debug) {
  // Reset the state manager when the state is kNotTriggered.
  if (immediate_pullover_state_ ==
          routing::pb::ImmediatePullOverState::kNotTriggered ||
      immediate_pullover_state_ ==
          routing::pb::ImmediatePullOverState::kFailed) {
    ResetImmediatePullOverStateManager(immediate_pull_over_debug);
    return;
  }

  // Early return when the state is kSuccess.
  if (immediate_pullover_state_ ==
      routing::pb::ImmediatePullOverState::kSuccess) {
    // For immediate pull over is completed by kRouteDestination without
    // an immediate pull over point, it is necessary to use ego position as
    // immediate pull over point.
    immediate_pull_over_point_ =
        immediate_pull_over_point_.has_value()
            ? immediate_pull_over_point_
            : std::make_optional(
                  math::geometry::Point2d(current_pose.x(), current_pose.y()));
    RecordImmediatePullOverSuccessRTEvent(selected_pdz_info_, current_pose,
                                          current_timestamp);
    return;
  }

  // Don't generate immediate pull over pdz when the mode isn't autonomous or
  // should_ignore_or_terminate_request_ is true.
  if (!robot_state.IsInAutonomousMode() ||
      should_ignore_or_terminate_request_) {
    immediate_pull_over_point_ = std::nullopt;
    selected_pdz_info_ = std::nullopt;
    ResetRedirectingModeStageManagerToIdleStage(
        immediate_pull_over_debug->mutable_redirecting_mode_debug());
    return;
  }

  // Check if the existing immediate pull over pdz valid. If valid, just keep it
  // during the immediate pull over; if not valid, re search another pdz.
  const auto seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::GetMsg(PathLastFrame());
  const auto& last_selected_lane_sequence_candidate =
      Seed::Access<token::WorldModelSeed>::GetMsg(PathLastFrame())
          ->last_selected_lane_sequence_candidate();
  const bool is_current_pdz_valid =
      selected_pdz_info_.has_value() &&
      IsPDZValidInPullOver(selected_pdz_info_.value(), current_lane,
                           robot_state, GetForbiddenStopTypesForMRC(),
                           mrm_type_,
                           seed->dynamic_pull_over_request_info().mrm_type(),
                           pull_over_progress) &&
      IsPDZReachableInRouting(regional_map.regional_path.sections,
                              last_selected_lane_sequence_candidate,
                              current_lane, selected_pdz_info_.value(),
                              current_timestamp,
                              stage_manager().proposal_request_timestamp(),
                              is_using_redirecting_mode_, pull_over_progress);
  if (is_current_pdz_valid) {
    GeneratePDZAndRunImmediatePullOverByRedirectingMode(
        pnc_map_service, regional_map, robot_state,
        last_lane_follow_lane_sequence, current_lane, current_pose, odd_info,
        assist_responses, is_current_pdz_valid,
        immediate_pull_over_debug->mutable_redirecting_mode_debug());
    return;
  }

  immediate_pull_over_point_ = std::nullopt;
  selected_pdz_info_ = std::nullopt;
  pdz_info_from_local_mode_ = std::nullopt;
  if (last_lane_follow_lane_sequence.empty()) {
    return;
  }

  GeneratePDZAndRunImmediatePullOverByRedirectingMode(
      pnc_map_service, regional_map, robot_state,
      last_lane_follow_lane_sequence, current_lane, current_pose, odd_info,
      assist_responses, is_current_pdz_valid,
      immediate_pull_over_debug->mutable_redirecting_mode_debug());
  GenerateImmediatePullOverPDZByLocalMode(
      global_route_solution, pnc_map_service, robot_state, construction_zones,
      last_lane_follow_lane_sequence, current_lane, current_pose, odd_info);
  SelectPDZBetweenLocalModeAndRedirectingMode(robot_state);
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverStateManager(
    const GlobalRouteSolution& global_route_solution,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<ConstructionZone>& construction_zones,
    const pb::LaneSequenceCandidates& last_candidates,
    const pnc_map::Lane& current_lane,
    const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request,
    const voy::Pose& current_pose,
    const std::vector<pb::AssistResponse>& assist_responses,
    const OddInfo& odd_info, int64_t current_timestamp,
    pb::PullOverProgress pull_over_progress,
    pb::ImmediatePullOverDebug* immediate_pull_over_debug) {
  immediate_pullover_info_ = immediate_pullover_info;
  const routing::pb::RoutePoint& destination =
      global_route_solution.route_solution().destination();
  std::vector<const pnc_map::Lane*> last_lane_follow_lane_sequence;
  for (const auto& candidate : last_candidates.lane_sequence_candidates()) {
    if (candidate.lane_sequence_type() ==
        pb::LaneSequenceCandidate_LaneSequenceType_LANE_FOLLOW) {
      last_lane_follow_lane_sequence = joint_pnc_map_service.GetLaneSequence(
          candidate.untrimmed_lane_sequence());
      break;
    }
  }

  UpdateImmediatePullOverState(immediate_pullover_info, mrc_request);
  UpdateImmediatePullOverSourceAndMRMType(immediate_pullover_info, mrc_request);
  UpdateImmediatePullOverTriggeredTimestamp(current_pose, current_timestamp);
  UpdateAccumulatedDistance(robot_state, destination, current_pose,
                            current_timestamp);
  UpdateShouldRefuseAndTerminateRequest(regional_map, robot_state, destination,
                                        pull_over_progress);
  UpdateImmediatePullOverPDZ(
      global_route_solution, joint_pnc_map_service.pnc_map_service(),
      regional_map, robot_state, construction_zones,
      last_lane_follow_lane_sequence, current_lane, current_pose,
      assist_responses, odd_info, current_timestamp, pull_over_progress,
      immediate_pull_over_debug);
  UpdateLaneChangeRequest(regional_map, robot_state,
                          last_lane_follow_lane_sequence, current_lane);
  UpdateDynamicPullOverRequestInfoSeed();
  UpdateImmediatePullOverDebug(immediate_pull_over_debug, current_timestamp);
}

void ImmediatePullOverStateManager::ResetImmediatePullOverStateManager(
    pb::ImmediatePullOverDebug* immediate_pull_over_debug) {
  immediate_pull_over_point_ = std::nullopt;
  immediate_pullover_triggered_timestamp_ = -1;
  immediate_pullover_state_ =
      routing::pb::ImmediatePullOverState::kNotTriggered;
  immediate_pullover_source_ =
      pb::ImmediatePullOverTriggerSource::kUnknownSource;
  mrm_type_ = mrc::pb::UNDEFINED;
  pdz_info_from_local_mode_ = std::nullopt;
  selected_pdz_info_ = std::nullopt;
  accumulated_distance_ = 0.0;
  last_timestamp_ = -1;
  initial_dist_to_destination_ = 0.0;
  is_using_redirecting_mode_ = false;
  should_ignore_or_terminate_request_ = false;
  ResetRedirectingModeStageManagerToIdleStage(
      immediate_pull_over_debug->mutable_redirecting_mode_debug());
}

void ImmediatePullOverStateManager::SelectPDZBetweenLocalModeAndRedirectingMode(
    const RobotState& robot_state) {
  const auto& pdz_info_from_redirecting_mode = stage_manager_.pdz_info();
  is_using_redirecting_mode_ = false;
  // Select pdz info from local_mode if redirecting mode can't generate pdz.
  if (!pdz_info_from_redirecting_mode.has_value()) {
    selected_pdz_info_ = pdz_info_from_local_mode_;
    return;
  }

  // Select the pdz from redirecting mode if local mode can't generate pdz or
  // pdz from redirecting mode is better than pdz from local mode.
  if (!pdz_info_from_local_mode_.has_value() ||
      IsRedirectingModePDZBetterThanLocalModePDZ(
          robot_state, pdz_info_from_redirecting_mode.value(),
          pdz_info_from_local_mode_.value())) {
    selected_pdz_info_ = pdz_info_from_redirecting_mode;
    is_using_redirecting_mode_ = true;
    rt_event::PostRtEvent<rt_event::planner::UsingRedirectingMode>();
    return;
  }

  // Use the pdz generated by local mode.
  selected_pdz_info_ = pdz_info_from_local_mode_;
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverState(
    const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request) {
  const routing::pb::ImmediatePullOverState
      immediate_pullover_state_in_last_cycle = immediate_pullover_state_;
  // Planner responds to phmi-triggered or dhmi-triggered immediate pull over
  // with priority.
  immediate_pullover_state_ =
      immediate_pullover_info.immediate_pullover_state();

  // Planner will respond to mrc-triggered immediate pull over if immediate pull
  // over from routing does not exist.
  if (immediate_pullover_state_ ==
      routing::pb::ImmediatePullOverState::kNotTriggered) {
    immediate_pullover_state_ =
        ConvertMrcRequestToImmediatePullOverState(mrc_request);
  }

  // Set trigger_state_change_type according to the change of immediate pull
  // over state.
  if (immediate_pullover_state_in_last_cycle ==
          routing::pb::ImmediatePullOverState::kNotTriggered &&
      immediate_pullover_state_ !=
          routing::pb::ImmediatePullOverState::kNotTriggered) {
    trigger_state_change_type_ = TriggerStateChangeType::kReceiveTriggerSignal;
  } else if (immediate_pullover_state_in_last_cycle !=
                 routing::pb::ImmediatePullOverState::kNotTriggered &&
             immediate_pullover_state_ ==
                 routing::pb::ImmediatePullOverState::kNotTriggered) {
    trigger_state_change_type_ = TriggerStateChangeType::kReceiveCancelSignal;
  } else {
    trigger_state_change_type_ = TriggerStateChangeType::kNoChange;
  }
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverSourceAndMRMType(
    const routing::pb::ImmediatePullOverInfo& immediate_pullover_info,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request) {
  if (immediate_pullover_state_ ==
      routing::pb::ImmediatePullOverState::kNotTriggered) {
    immediate_pullover_source_ =
        pb::ImmediatePullOverTriggerSource::kUnknownSource;
    mrm_type_ = mrc::pb::UNDEFINED;
    return;
  }

  if (mrc_request != nullptr) {
    // Update mrm type.
    mrm_type_ = mrc_request->mrm_type();
    // Update source by mrc request.
    if (mrc_request->mrm_type() == mrc::pb::MrmType::MRM_HARBOR ||
        mrc_request->mrm_type() == mrc::pb::MrmType::MRM_PULLRIGHT) {
      if (mrc_request->mrc_reason() == mrc::pb::MrcReason::kSystemSupervisor) {
        immediate_pullover_source_ =
            pb::ImmediatePullOverTriggerSource::kBySystemSupervisor;
      } else if (mrc_request->mrc_reason() ==
                 mrc::pb::MrcReason::kRemoteAssist) {
        immediate_pullover_source_ =
            pb::ImmediatePullOverTriggerSource::kByRemoteAssist;
      } else {
        CHECK(false) << "Unknown mrc request source";
      }

      // If mrc request is None, will update source by immediate pull over
      // info.
      if (mrc_request->mrm_progress() != mrc::pb::MrcProgress::NONE) {
        return;
      }
    }
  }

  // Update source by immediate pull over info.
  mrm_type_ = mrc::pb::UNDEFINED;
  switch (immediate_pullover_info.immediate_pullover_source()) {
    case routing::pb::kOrderService:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kByOrderService;
      break;
    case routing::pb::kPlanning:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kByPlanning;
      break;
    case routing::pb::kRouting:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kByRouting;
      break;
    case routing::pb::kDriverHMI:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kByDriverHMI;
      break;
    case routing::pb::kPassengerHMI:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kByPassengerHMI;
      break;
    default:
      immediate_pullover_source_ =
          pb::ImmediatePullOverTriggerSource::kUnknownSource;
      break;
  }
}

void ImmediatePullOverStateManager::UpdateShouldRefuseAndTerminateRequest(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const routing::pb::RoutePoint& destination,
    pb::PullOverProgress pull_over_progress) {
  should_ignore_or_terminate_request_ = false;
  // Check should ignore or terminate routing-source immediate pull over.
  if (immediate_pullover_source_ ==
      pb::ImmediatePullOverTriggerSource::kByRouting) {
    should_ignore_or_terminate_request_ =
        ShouldIgnoreOrTerminateRoutingSourceImmediatePullOver(
            regional_map, robot_state, destination, pull_over_progress,
            accumulated_distance_);
    return;
  }

  // Check should ignore or terminate mrc-triggered or phmi/dhmi-triggered
  // immediate pull over when ego is about to reach the routing destination or
  // is executing pull over near the routing destination.
  if (IsTriggeredByOrderService() || IsImmediatePullOverTriggeredByMRC()) {
    should_ignore_or_terminate_request_ =
        ShouldIgnoreOrTerminateImmediatePullOverWhenEgoNearDestination(
            regional_map, selected_pdz_info_.has_value(), pull_over_progress);
  }
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverTriggeredTimestamp(
    const voy::Pose& current_pose, int64_t current_timestamp) {
  // Reset immediate_pullover_triggered_timestamp when received new request.
  const auto seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::GetMsg(PathLastFrame());
  const pb::ImmediatePullOverTriggerSource
      last_cycle_immediate_pullover_source =
          seed->dynamic_pull_over_request_info().trigger_source();
  const mrc::pb::MrmType last_cycle_mrm_type =
      seed->dynamic_pull_over_request_info().mrm_type();
  if (last_cycle_immediate_pullover_source != immediate_pullover_source_ ||
      last_cycle_mrm_type != mrm_type_) {
    immediate_pullover_triggered_timestamp_ = -1;
  }

  if (immediate_pullover_state_ ==
      routing::pb::ImmediatePullOverState::kTriggered) {
    // When the immediate pullover state is triggered for the first time, we
    // update the triggered timestamp.
    if (immediate_pullover_triggered_timestamp_ == -1) {
      immediate_pullover_triggered_timestamp_ = current_timestamp;
      std::stringstream rt_event_info;
      rt_event_info << "\"TriggeredSource\": " << immediate_pullover_source_
                    << ",\"MRMType\":" << mrm_type_
                    << ",\"TriggeredTimeStamp\":" << current_timestamp
                    << ",\"EgoPose\":" << current_pose.x() << ","
                    << current_pose.y();

      rt_event::PostRtEvent<rt_event::planner::ImmediatePullOverTriggeredTime>(
          rt_event_info.str());
    }
  } else if (immediate_pullover_state_ ==
             routing::pb::ImmediatePullOverState::kNotTriggered) {
    // Reset start timestamp to default value if current immediate pullover
    // state is not kTriggered.
    immediate_pullover_triggered_timestamp_ = -1;
  }
}

void ImmediatePullOverStateManager::UpdateAccumulatedDistance(
    const RobotState& robot_state, const routing::pb::RoutePoint& destination,
    const voy::Pose& current_pose, int64_t current_timestamp) {
  // Reset accumulated distance info when received new request.
  const auto seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::GetMsg(PathLastFrame());
  const pb::ImmediatePullOverTriggerSource
      last_cycle_immediate_pullover_source =
          seed->dynamic_pull_over_request_info().trigger_source();
  const mrc::pb::MrmType last_cycle_mrm_type =
      seed->dynamic_pull_over_request_info().mrm_type();
  if (last_cycle_immediate_pullover_source != immediate_pullover_source_ ||
      last_cycle_mrm_type != mrm_type_) {
    accumulated_distance_ = 0.0;
    last_timestamp_ = -1;
    initial_dist_to_destination_ = 0.0;
  }

  // Update initial dist_to_destination when planning-source immediate pull
  // over is triggered.
  if (last_timestamp_ == -1) {
    initial_dist_to_destination_ =
        immediate_pullover_source_ ==
                pb::ImmediatePullOverTriggerSource::kByPlanning
            ? math::geometry::Distance(
                  math::geometry::Point2d(current_pose.x(), current_pose.y()),
                  math::geometry::Point2d(destination.position().x(),
                                          destination.position().y()))
            : 0.0;
  }

  // Update accumulated distance.
  if (last_timestamp_ != -1 && last_timestamp_ != 0) {
    accumulated_distance_ += robot_state.GetEstimatedDrivingDistance(
        current_timestamp - last_timestamp_);
  }
  last_timestamp_ = current_timestamp;
}

void ImmediatePullOverStateManager::GenerateImmediatePullOverPDZByLocalMode(
    const GlobalRouteSolution& global_route_solution,
    const pnc_map::PncMapService* pnc_map_service,
    const RobotState& robot_state,
    const std::vector<ConstructionZone>& construction_zones,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane, const voy::Pose& current_pose,
    const OddInfo& odd_info) {
  // TODO(peterxu): Remove the restriction on trigger source if all immediate
  // pull over use cloud cells.
  // Don't use local stop cells if we are still trying to generate pdz using
  // cloud cells.
  if (!stage_manager_.should_use_local_stop_cells() &&
      (IsTriggeredByOrderService() || IsImmediatePullOverTriggeredByMRC())) {
    return;
  }

  if (pdz_info_from_local_mode_.has_value()) {
    return;
  }

  // Note: When immediate pull over is triggered, ego needs to needs to drive
  // to rightmost lane or the second right lane by lane change. After that,
  // immediate pull over point starts to be searched.
  if (!current_lane.IsRightmostDrivableLane() &&
      (current_lane.right_lane() != nullptr &&
       !current_lane.right_lane()->IsRightmostDrivableLane())) {
    return;
  }

  // Filter out the lanes that the ego has passed.
  DCHECK(!last_lane_follow_lane_sequence.empty());
  const auto current_lane_iter = LaneSequenceIterator::FindLane(
      last_lane_follow_lane_sequence, current_lane.id());
  std::vector<const pnc_map::Lane*> lane_sequence;
  lane_sequence.insert(lane_sequence.end(), current_lane_iter,
                       last_lane_follow_lane_sequence.end());
  const auto candidate_immediate_pullover_points =
      SampleImmediatePullOverPointsForVirtualPDZ(*pnc_map_service, robot_state,
                                                 lane_sequence, current_pose);
  std::set<int64_t> lane_sequence_road_ids;
  lane_sequence_road_ids.insert(current_lane.section()->road()->id());
  for (const auto* lane : lane_sequence) {
    if (lane != nullptr) {
      lane_sequence_road_ids.insert(lane->section()->road()->id());
    }
  }
  pdz_info_from_local_mode_ = GeneratePDZBySampledImmediatePullOvePoints(
      global_route_solution, lane_sequence_road_ids, pnc_map_service,
      last_lane_follow_lane_sequence, robot_state, construction_zones,
      candidate_immediate_pullover_points, GetForbiddenStopTypesForMRC(),
      IsImmediatePullOverTriggeredByMRC(), ShouldUseImpoOddType(), odd_info);
}

void ImmediatePullOverStateManager::UpdateImmediatePullOverDebug(
    pb::ImmediatePullOverDebug* immediate_pull_over_debug,
    int64_t current_timestamp) {
  if (immediate_pull_over_debug == nullptr) {
    return;
  }

  immediate_pull_over_debug->set_immediate_pullover_state(
      immediate_pullover_state_);
  immediate_pull_over_debug->set_triggered_timestamp(
      immediate_pullover_triggered_timestamp_);
  immediate_pull_over_debug->set_use_redirecting_mode(
      is_using_redirecting_mode_);
  if (immediate_pullover_state_ !=
      routing::pb::ImmediatePullOverState::kNotTriggered) {
    immediate_pull_over_debug->set_triggered_duration_time(
        current_timestamp - immediate_pullover_triggered_timestamp_);
    immediate_pull_over_debug->set_accumulated_distance(accumulated_distance_);
    immediate_pull_over_debug->set_initial_dist_to_destination(
        initial_dist_to_destination_);
    immediate_pull_over_debug->set_trigger_source(immediate_pullover_source_);
    immediate_pull_over_debug->set_mrm_type(mrm_type_);
    immediate_pull_over_debug->set_should_ignore_or_terminate_request(
        should_ignore_or_terminate_request_);
    immediate_pull_over_debug->set_should_request_immediate_right_lane_change(
        should_request_immediate_right_lane_change_);
    immediate_pull_over_debug->set_should_request_restricted_left_lane_change(
        should_request_restricted_left_lane_change_);
  }

  pb::RedirectingImmediatePullOverDebug* redirecting_imm_pull_over_debug =
      immediate_pull_over_debug->mutable_redirecting_mode_debug();
  redirecting_imm_pull_over_debug->set_is_using_cloud_cells(
      stage_manager().is_using_cloud_cells());
  redirecting_imm_pull_over_debug->set_is_deviate_original_route(
      stage_manager().is_deviate_original_route());

  if (immediate_pull_over_point_.has_value()) {
    voy::Point2d* immediate_pull_over_point =
        immediate_pull_over_debug->mutable_immediate_pull_over_point();
    immediate_pull_over_point->set_x(immediate_pull_over_point_.value().x());
    immediate_pull_over_point->set_y(immediate_pull_over_point_.value().y());
  }
}

void ImmediatePullOverStateManager::RecordImmediatePullOverSuccessRTEvent(
    const std::optional<PickupDropoffZoneInfo>& pickup_dropoff_zone_info,
    const voy::Pose& current_pose, int64_t current_timestamp) {
  const auto stop_cell_type_info =
      GetStopCellTypeWhichEgoLocated(pickup_dropoff_zone_info, current_pose);
  const int64_t triggered_duration =
      current_timestamp - immediate_pullover_triggered_timestamp_;
  std::stringstream rt_event_info;
  rt_event_info << "\"TriggeredSource\": " << immediate_pullover_source_
                << ",\"MRMType\":" << mrm_type_ << ",\"TriggeredTimeStamp\": "
                << immediate_pullover_triggered_timestamp_
                << ",\"TriggeredDuration\": " << triggered_duration
                << ",\"AccumulatedDistance\": " << accumulated_distance_
                << ",\"StopCellType\": \"" << stop_cell_type_info
                << ",\"EgoPose\":" << current_pose.x() << ","
                << current_pose.y()
                << ",\"UsingRedirectingMode\":" << is_using_redirecting_mode_
                << ",\"DeviateOriginalRoute\":"
                << stage_manager_.is_deviate_original_route()
                << ",\"UsingCloudCells\":"
                << stage_manager_.is_using_cloud_cells();
  rt_event::PostRtEvent<rt_event::planner::ImmediatePullOverCompletedTime>(
      rt_event_info.str());
}

void ImmediatePullOverStateManager::UpdateDynamicPullOverRequestInfoSeed() {
  auto mutable_seed =
      Seed::Access<token::ImmediatePulloverStateSeed>::MutableMsg(
          PathCurrentFrame());
  pb::DynamicPullOverRequestInfo* mutable_dynamic_pull_over_request_info_seed =
      mutable_seed->mutable_dynamic_pull_over_request_info();
  mutable_dynamic_pull_over_request_info_seed->set_immediate_pullover_state(
      immediate_pullover_state_);
  mutable_dynamic_pull_over_request_info_seed->set_trigger_source(
      immediate_pullover_source_);
  mutable_dynamic_pull_over_request_info_seed->set_mrm_type(mrm_type_);
  mutable_dynamic_pull_over_request_info_seed->set_use_redirecting_mode(
      is_using_redirecting_mode_);
  if (immediate_pull_over_point_.has_value()) {
    voy::Point2d* immediate_pull_over_point =
        mutable_dynamic_pull_over_request_info_seed
            ->mutable_immediate_pull_over_point();
    immediate_pull_over_point->set_x(immediate_pull_over_point_.value().x());
    immediate_pull_over_point->set_y(immediate_pull_over_point_.value().y());
  }
}

void ImmediatePullOverStateManager::UpdateLaneChangeRequest(
    const RegionalMap& regional_map, const RobotState& robot_state,
    const std::vector<const pnc_map::Lane*>& last_lane_follow_lane_sequence,
    const pnc_map::Lane& current_lane) {
  std::tie(should_request_immediate_right_lane_change_,
           should_request_restricted_left_lane_change_) =
      GenerateLaneChangeRequest(regional_map, robot_state,
                                last_lane_follow_lane_sequence, current_lane,
                                stage_manager_, selected_pdz_info_,
                                ShouldResponseToTriggeredImmediatePullOver(),
                                is_using_redirecting_mode_);
}

bool ImmediatePullOverStateManager::ShouldUseImpoOddType() const {
  // Set should_use_impo_odd_type to true when immediate pull over is triggered
  // by order service.
  return FLAGS_planning_enable_impo_odd_type && IsTriggeredByOrderService();
}

}  // namespace planner
