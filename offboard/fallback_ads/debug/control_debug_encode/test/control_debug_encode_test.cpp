#include "debug/control_debug_encode/control_debug_encode.h"

#include <gtest/gtest.h>

#include <cmath>
#include <cstdint>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"
#include "offboard/fallback_ads/fallback_simulation.h"

namespace fallback_adas {
namespace debug {

TEST(ControlDebugEncode, TimestampTest) {
  auto& raw_control_debug = Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  ControlDebug control_debug = {};
  // control_debug.timestamp_ms = 188094675843216.
  control_debug.timestamp_ms = 0xAB1234567890;

  ControlDebugEncode(control_debug);

  auto& timestamp_bytes =
      raw_control_debug.Rte_FallbackControlDebugInfo_ControlTimeStamp.value;
  EXPECT_EQ(timestamp_bytes[0], 0xAB);
  EXPECT_EQ(timestamp_bytes[1], 0x12);
  EXPECT_EQ(timestamp_bytes[2], 0x34);
  EXPECT_EQ(timestamp_bytes[3], 0x56);
  EXPECT_EQ(timestamp_bytes[4], 0x78);
  EXPECT_EQ(timestamp_bytes[5], 0x90);

  uint64_t timestamp_ms = 0;
  common::BytesToUint64(timestamp_bytes,
                        sizeof(timestamp_bytes) / sizeof(timestamp_bytes[0]),
                        &timestamp_ms, true);
  EXPECT_EQ(timestamp_ms, control_debug.timestamp_ms);
}

TEST(ControlDebugEncode, MetricsTest) {
  auto& raw_control_debug = Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  ControlDebug control_debug = {};
  auto& metrics = control_debug.metrics;
  metrics.error_longitudinal = 0.1F;
  metrics.error_velocity = 0.2F;
  metrics.error_heading = 0.3F;
  metrics.error_lateral = 0.4F;

  ControlDebugEncode(control_debug);

  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_ControlErrorLongitudinal
          .value,
      static_cast<int16_t>(std::roundf(metrics.error_longitudinal / 0.01F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_ControlErrorVelocity.value,
      static_cast<int16_t>(std::roundf(metrics.error_velocity / 0.01F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_ControlErrorHeading.value,
      static_cast<int16_t>(std::roundf(metrics.error_heading / 0.001F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_ControlErrorLateral.value,
      static_cast<int16_t>(std::roundf(metrics.error_lateral / 0.01F)));
}

TEST(ControlDebugEncode, LateralPIDDebugTest) {
  auto& raw_control_debug = Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  ControlDebug control_debug = {};
  auto& lateral_pid = control_debug.controller.lateral_pid;
  lateral_pid.steering_feedback = 0.1F;
  lateral_pid.steering_feedforward = 0.2F;
  lateral_pid.pid_distance.pid_total = 0.3F;
  lateral_pid.pid_distance.p_component = 0.4F;
  lateral_pid.pid_distance.i_component = 0.5F;
  lateral_pid.pid_distance.d_component = 0.6F;
  lateral_pid.pid_heading.pid_total = 0.7F;
  lateral_pid.pid_heading.p_component = 0.8F;
  lateral_pid.pid_heading.i_component = 0.9F;
  lateral_pid.pid_heading.d_component = 1.0F;

  ControlDebugEncode(control_debug);

  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_LatSteeringFeeback.value,
      static_cast<int16_t>(
          std::roundf(lateral_pid.steering_feedback / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatSteeringFeedforward.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.steering_feedforward / 0.001F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_LatPidDistanceTotal.value,
      static_cast<int16_t>(
          std::roundf(lateral_pid.pid_distance.pid_total / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidDistancePComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_distance.p_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidDistanceIComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_distance.i_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidDistanceDComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_distance.d_component / 0.001F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_LatPidHeadingTotal.value,
      static_cast<int16_t>(
          std::roundf(lateral_pid.pid_heading.pid_total / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidHeadingPComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_heading.p_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidHeadingIComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_heading.i_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LatPidHeadingDComponent.value,
            static_cast<int16_t>(
                std::roundf(lateral_pid.pid_heading.d_component / 0.001F)));
}

TEST(ControlDebugEncode, LongitudinalPidTest) {
  auto& raw_control_debug = Rte_MainTask_Core0_60ms.Rte_RB.Rte_Debug_Debug_60ms;

  // Create a ControlDebug object
  ControlDebug control_debug = {};
  auto& long_pid = control_debug.controller.longitudinal_pid;
  long_pid.feedback_acceleration = 1.0F;
  long_pid.feedforward_acceleration = 0.9F;
  long_pid.pid_distance.pid_total = 0.8F;
  long_pid.pid_distance.p_component = 0.7F;
  long_pid.pid_distance.i_component = 0.6F;
  long_pid.pid_distance.d_component = 0.5F;
  long_pid.pid_speed.pid_total = 0.4F;
  long_pid.pid_speed.p_component = 0.3F;
  long_pid.pid_speed.i_component = 0.2F;
  long_pid.pid_speed.d_component = 0.1F;

  ControlDebugEncode(control_debug);

  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongAccelerationFeeback.value,
            static_cast<int16_t>(
                std::roundf(long_pid.feedback_acceleration / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongAccelerationFeedforward.value,
            static_cast<int16_t>(
                std::roundf(long_pid.feedforward_acceleration / 0.001F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_LongPidDistanceTotal.value,
      static_cast<int16_t>(
          std::roundf(long_pid.pid_distance.pid_total / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidDistancePComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_distance.p_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidDistanceIComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_distance.i_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidDistanceDComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_distance.d_component / 0.001F)));
  EXPECT_EQ(
      raw_control_debug.Rte_FallbackControlDebugInfo_LongPidSpeedTotal.value,
      static_cast<int16_t>(std::roundf(long_pid.pid_speed.pid_total / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidSpeedPComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_speed.p_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidSpeedIComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_speed.i_component / 0.001F)));
  EXPECT_EQ(raw_control_debug
                .Rte_FallbackControlDebugInfo_LongPidSpeedDComponent.value,
            static_cast<int16_t>(
                std::roundf(long_pid.pid_speed.d_component / 0.001F)));
}

}  // namespace debug
}  // namespace fallback_adas
