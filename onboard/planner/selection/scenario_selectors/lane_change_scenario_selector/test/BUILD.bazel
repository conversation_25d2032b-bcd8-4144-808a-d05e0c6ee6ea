load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "selection_lane_change_test",
    srcs = [
        "selection_lane_change_test.cpp",
    ],
    deps = [
        "//onboard/common/math",
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/decoupled_maneuvers/cross_lane:cross_lane_utils",
        "//onboard/planner/decoupled_maneuvers/lane_change:lane_change_env_analyzer",
        "//onboard/planner/decoupled_maneuvers/lane_change:lane_change_info",
        "//onboard/planner/decoupled_maneuvers/path/reasoning:path_reasoning",
        "//onboard/planner/selection",
        "//onboard/planner/selection:selection_utils",
        "//onboard/planner/selection/scenario_selectors/lane_change_scenario_selector",
        "//onboard/planner/selection/scenario_selectors/lane_change_scenario_selector:lane_change_selection_util",
        "//onboard/planner/speed/reasoning/test:reasoning_test_fixture",
        "//onboard/planner/speed/test_util:speed_test_util",
        "//onboard/planner/trajectory/util",
        "//onboard/planner/utility/config_center",
        "//onboard/planner/world_model:test_utility",
        "//onboard/pnc_map_service:test_util",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "lane_change_selection_util_test",
    srcs = [
        "lane_change_selection_util_test.cpp",
    ],
    deps = [
        "//onboard/common/math",
        "//onboard/planner/selection/scenario_selectors/lane_change_scenario_selector:lane_change_selection_util",
        "//onboard/planner/speed/reasoning/test:reasoning_test_fixture",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "lane_change_scenario_selector_test",
    srcs = [
        "lane_change_scenario_selector_test.cpp",
    ],
    deps = [
        "//onboard/planner/selection",
        "//onboard/planner/selection/scenario_selectors/lane_change_scenario_selector",
        "//onboard/planner/selection/scenario_selectors/lane_change_scenario_selector:lane_change_selection_util",
        "//onboard/planner/selection/test:selection_test_fixture",
        "@voy-sdk//:gtest",
    ],
)
