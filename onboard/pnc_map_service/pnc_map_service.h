#ifndef ONBOARD_PNC_MAP_SERVICE_PNC_MAP_SERVICE_H_
#define ONBOARD_PNC_MAP_SERVICE_PNC_MAP_SERVICE_H_

#include <cstddef>
#include <map>
#include <memory>
#include <set>
#include <string>
#include <utility>
#include <vector>

#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/zone_lane_occupancy_map.h"
#include "hdmap_protos/geo_info.pb.h"
#include "hdmap_protos/map_config.pb.h"
#include "hdmap_protos/signal.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "pnc_map_service/map_elements/crosswalk.h"
#include "pnc_map_service/map_elements/directed_lanes.h"
#include "pnc_map_service/map_elements/elements_cache.h"
#include "pnc_map_service/map_elements/hard_boundary.h"
#include "pnc_map_service/map_elements/junction.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/lane_marking.h"
#include "pnc_map_service/map_elements/object_nearby_crosswalk.h"
#include "pnc_map_service/map_elements/pickup_dropoff_zone.h"
#include "pnc_map_service/map_elements/prior_lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/section.h"
#include "pnc_map_service/map_elements/traffic_sign.h"
#include "pnc_map_service/map_elements/traffic_signal.h"
#include "pnc_map_service/map_elements/zone.h"
#include "pnc_map_service/pnc_map_updater_base.h"
#include "pnc_map_service/util/lane_sequence_map.h"
#include "strings/stringprintf.h"
#include "voy_protos/tracked_objects.pb.h"

namespace pnc_map {

// Forward declarations.
class JointPncMapService;
class PncMapUpdater;

// PncMapService is a data layer on top of hdmap to retrieve map objects and
// by geographical queries. Map objects are in the form of PncObject and owned
// by this class.
// This class is not thread-safe.
// See:
// https://docs.google.com/document/d/1Xr9WB_L4eCGLa5wej0P2IP9bBaNGivo1hNVqCJE8L1o/
class PncMapService : public PncMapUpdaterBase {
 public:
  PncMapService() = default;

  // Initializes the service with a HdMap instance. Prebuild PncObject and
  // precompute association based on all objects in HdMap.
  // Note that generate_effective_hb denotes whether to generate effective HB,
  // planner need to use effective HB thus set it to true.
  // For the modules which expects complete HB, please set this flag to false.
  void Init(std::shared_ptr<const hdmap::HdMap> hdmap,
            bool generate_effective_hb = false);

  // Gets lanes with the given pose within |lane_searching_radius_m|.
  // Returned near lanes may not contain the robot center. The heading is
  // constrained within |max_heading_diff|. If prefer_overlapped_lanes is true,
  // only return lanes which overlap with pose unless all eligible lanes are at
  // bus bulb.
  // Lanes in last_selected_lane_sequence and before next junction are not
  // subject to heading filtering.
  std::vector<const Lane*> GetNearLanesWithPose(
      const voy::Pose& pose, double lane_searching_radius_m,
      double max_heading_diff, bool prefer_overlapped_lanes,
      const std::optional<std::vector<const pnc_map::Lane*>>&
          last_selected_lane_sequence = std::nullopt) const;

  // Gets road geographic information of a point in a road.
  std::unique_ptr<RoadGeoInfo> GetRoadGeoInfo(const hdmap::Point& point,
                                              const Road& road) const;

  // Returns map region name from map configuration.
  const std::string& map_region() const { return map_region_; }

  const hdmap::HdMap* hdmap() const { return hdmap_.get(); }

  const hdmap::ZoneLaneOccupancyMap& zone_lane_occupancy_map() const {
    return zone_lane_occupancy_map_;
  }

  const std::unordered_map</*road id*/ int64_t, routing::RoadFeature>&
  road_feature_map() const {
    return road_feature_map_;
  }

  const std::unordered_map</*lane_id*/ int64_t,
                           routing::PrecalculatedLaneFeature>&
  precalculated_lane_feature_map() const {
    return precalculated_lane_feature_map_;
  }

  // APIs to lookup static PNC objects from cache.
  // PNC objects may be updated dynamically.
  // make sure to use JointPncMapService APIs to get online objects to get
  // dynamic contents such as hard boundary and traffic signals.
  const Lane* GetLaneById(int64_t id) const {
    return GetObject(id, offline_elements_cache_.lane_map);
  }

  const Section* GetSectionById(int64_t id) const {
    return GetObject(id, offline_elements_cache_.section_map);
  }

  const Road* GetRoadById(int64_t id) const {
    return GetObject(id, offline_elements_cache_.road_map);
  }

  // Gets liftrod for a given id.
  const LiftRod* GetLiftRodById(int64_t id) const {
    return GetObject(id, offline_elements_cache_.lift_rod_map);
  }

  // Gets lane sequence with the given lane ids.
  std::vector<const Lane*> GetLaneSequence(
      const std::vector<int64_t>& lane_ids) const;

  // Gets lane sequence with the given lane ids.
  std::vector<const Lane*> GetLaneSequence(
      const ::google::protobuf::RepeatedField<int64>& lane_ids) const;

  // Gets road vector with the given road ids. Nullptr roads are skipped.
  std::vector<const Road*> GetRoadsById(
      const std::vector<int64_t>& road_ids) const;

  // Gets road vector with the given road ids. Nullptr roads are skipped.
  std::vector<const Road*> GetRoads(
      const ::google::protobuf::RepeatedField<int64>& road_ids) const;

  // Gets section vector with the given section ids.
  std::vector<const Section*> GetSections(
      const std::vector<int64_t>& section_ids) const;

  // Gets section vector with the given section ids. Not found sections are
  // skipped.
  std::vector<const Section*> GetSections(
      const ::google::protobuf::RepeatedField<int64>& section_ids) const;

  // Gets zone vector with the given zone ids.
  std::vector<const Zone*> GetZones(const std::vector<int64_t>& zone_ids) const;

  // Returns true if the query point is in junction.
  bool IsInJunction(const hdmap::Point& point) const;

  // Returns true if the query point is in a zone with ROAD_EXIT label.
  bool IsInExitZone(const hdmap::Point& point) const;

  // Returns true if the query point is in a zone with BUS_BULB label.
  bool IsInBusBulb(const hdmap::Point& point) const;

 protected:
  // Clears cached PNC objects.
  void Clear();

  // Creates Road from proto and insert to cache. It does the following:
  //   1. Create inner Sections.
  //   2. Create inner Lanes.
  //   3. Create inner Crosswalks and Zones if not in cache.
  //   4. Road's junction data will be set later during create junctions.
  Road* CreateRoad(const hdmap::Road& road_proto);

  // Creates Section from protos and insert to cache. It does the following:
  //   1. Create inner LaneMarkings.
  //   2. Create inner Lanes.
  //   3. Link Lane's left and right neighbors.
  Section* CreateSection(const hdmap::LaneSection& section_proto);

  // Creates Crosswalk from proto and insert to cache. It does the following:
  //   1. Create inner StopLines, if not existing.
  Crosswalk* CreateCrosswalk(const hdmap::Crosswalk& crosswalk_proto);

  // Creates Junction from proto and insert to cache. It does the following:
  //   1. Create inner Roads, if not existing.
  Junction* CreateJunction(const hdmap::Junction& junction_proto);

  // Creates TrafficSign from proto and insert to cache. It does the following:
  //   1. Create inner StopLines, if not existing.
  //   2. Link TrafficSign to Lane, if exiting.
  TrafficSign* CreateTrafficSign(const hdmap::Sign& sign_proto);

  // Creates TrafficSignal from proto and insert to cache. It does the
  // following:
  //   1. Create inner StopLines, if not existing.
  //   2. Link TrafficSignal to Lane, if existing.
  TrafficSignal* CreateTrafficSignal(const hdmap::Signal& signal_proto);

  // Creates HardBoundary from left_boundary, right_boundary and
  // middle_boundary. It might be duplicated hard boundary in those road
  // boundaries.
  void CreateHardBoundary(const hdmap::RoadBoundary& road_boundary);

  // Gets a list of TrafficSignal from cache. If TrafficSignal does not exist in
  // cache, it will try to create one.
  std::vector<const TrafficSignal*> GetOrCreateTrafficSignals(
      const ::google::protobuf::RepeatedField<int64>& signal_ids);

  // Gets a list of StopLine from cache. If StopLine does not exist in cache, it
  // will try to create one.
  std::vector<const StopLine*> GetOrCreateStopLines(
      const ::google::protobuf::RepeatedPtrField<hdmap::StopLine>&
          stop_line_rep_field);

  // Creates StopLine from proto and inserts to cache. Returns NULL if
  // associated Lane does not exist.
  StopLine* CreateStopLine(const hdmap::StopLine& stop_line_proto);

  // Creates offline LiftRod from proto and inserts to cache, after all lanes
  // are created.
  std::shared_ptr<LiftRod> CreateLiftRod(const hdmap::LiftRod& rod_proto);

  // Finds pnc map LaneMarking, if not found, creates one.
  const LaneMarking* GetOrCreateLaneMarking(int64_t lane_marking_id);

  // Gets the opposite road of the given road.
  // TODO(jialong): this is temporary implementation. Remove when map data is
  // ready for providing accurate opposite road information.
  // Must call GetOppositeRoad for non-junction roads first, then for junction
  // roads.
  Road* GetOppositeRoad(const Road* road) const;

  // Checks if successor/predecessor road of current road is the opposite road
  // of predecessor/successor of the near road.
  // IsOppositeRoadPair assumes that opposite road for non-junction roads are
  // set already.
  bool IsOppositeRoadPair(const Road* current_road,
                          const Road* near_road) const;

  // For every road, tries to set if its near road has opposite direction and
  // similar curvature.
  void TrySetOppositeRoadForAllRoads();

  // Tries to set if its near road has opposite direction and similar curvature.
  void TrySetOppositeRoad(Road* road) const;

  // Links TrafficSignal to its Lane through StopLine.
  void LinkTrafficSignalToLane(const TrafficSignal* traffic_signal);

  // Sets neighbor road.
  void SetNeighborRoad(
      const std::unordered_map</*road_id*/ int64_t, routing::RoadFeature>&
          road_feature_map,
      Road* road) const;

  // Gets Lanes of a road which are overlap with |points|.
  // Only consider Lanes built when constructing global path.
  std::vector<const Lane*> GetOverlapLanesInRoad(
      const std::vector<hdmap::Point>& points, int64_t road_id) const;

  // Gets nearby Roads centered at robot pose with range |radius|.
  // Only consider Roads built when constructing global path.
  std::vector<const Road*> GetNearRoads(double radius) const;

  // Helper function to retrieve the pnc object by id.
  template <class PncObject>
  PncObject* GetObject(
      int64_t id,
      const std::map<int64_t, std::shared_ptr<PncObject>>& object_map) const;

  // APIs to lookup mutable PNC objects from cache.
  Road* GetRoad(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.road_map);
  }

  HardBoundary* GetHardBoundary(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.hard_boundary_map);
  }
  Section* GetSection(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.section_map);
  }
  Lane* GetLane(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.lane_map);
  }
  PriorLane* GetPriorLane(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.prior_lane_map);
  }
  LaneMarking* GetLaneMarking(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.lane_marking_map);
  }
  TrafficSign* GetTrafficSign(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.traffic_sign_map);
  }
  TrafficSignal* GetTrafficSignal(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.traffic_signal_map);
  }
  Crosswalk* GetCrosswalk(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.crosswalk_map);
  }
  StopLine* GetStopLine(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.stop_line_map);
  }
  Zone* GetZone(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.zone_map);
  }
  Junction* GetJunction(int64_t id) const override {
    return GetObject(id, offline_elements_cache_.junction_map);
  }
  PickupDropoffZone* GetPickupDropoffZone(int64_t id) const {
    return GetObject(id, offline_elements_cache_.pickup_dropoff_zone_map);
  }

  // Appends closest lanes into the input |lanes| from |geo_info_vec|.
  void AppendLanesFromGeoInfo(const math::geometry::Point2d& point,
                              const std::vector<hdmap::GeoInfo>& geo_info_vec,
                              double search_radius,
                              std::vector<const pnc_map::Lane*>* lanes,
                              std::set<int64_t>* lane_ids_set) const;

  std::shared_ptr<const hdmap::HdMap> hdmap_;
  // Prior data layer from hdmap.
  const hdmap::prior_map::PriorMap* prior_layer_ = nullptr;

  hdmap::ZoneLaneOccupancyMap zone_lane_occupancy_map_;

  std::unordered_map</*road id*/ int64_t, routing::RoadFeature>
      road_feature_map_;

  std::unordered_map</*lane_id*/ int64_t, routing::PrecalculatedLaneFeature>
      precalculated_lane_feature_map_;

  // Map region name.
  std::string map_region_;

  // Cache for PNC objects. Loaded from static hdmap.
  // Within pnc map service module, we do not update offline_elements_cache_
  // after it is loaded. fallback module may update offline_elements_cache_ with
  // realtime data.
  ElementsCache offline_elements_cache_;
  // This boolean indicates whether to generate effective har boundary lines. If
  // true, map adapter will convert raw HB data to effective HB polyline
  // according to effective index range provided by map, otherwise complete hard
  // boundary lines will be generated.
  bool generate_effective_hb_ = false;
  // This friend class provides unit test the ability to access pnc map object
  // by hdmap object id.
  friend class JointPncMapService;
  friend class MapTestUtil;
  friend class PncMapUpdater;
};

template <class PncObject>
PncObject* PncMapService::GetObject(
    int64_t id,
    const std::map<int64_t, std::shared_ptr<PncObject>>& object_map) const {
  if (id <= 0) return nullptr;
  auto iter = object_map.find(id);
  if (iter == object_map.end()) return nullptr;
  return DCHECK_NOTNULL(iter->second).get();
}

}  // namespace pnc_map
#endif  // ONBOARD_PNC_MAP_SERVICE_PNC_MAP_SERVICE_H_
