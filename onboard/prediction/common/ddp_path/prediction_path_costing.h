#ifndef ONBOARD_PREDICTION_COMMON_DDP_PATH_PREDICTION_PATH_COSTING_H_
#define ONBOARD_PREDICTION_COMMON_DDP_PATH_PREDICTION_PATH_COSTING_H_

#include "control_util/costing_base.h"
#include "prediction/common/ddp_path/primitive_definition_prediction_path.h"
#include "vehicle_model/utils.h"

namespace control_util {

// Trait for PredictionPathFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kPredictionPath> {
  using StateControlSpecs = vehicle_model::GeometricModel;

  using PbState = vehicle_model::pb::GeometricState;
  using PbControl = vehicle_model::pb::GeometricControl;

  using PbFeatureType = control_util::pb::PredictionPathFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = control_util::pb::PredictionPathFeatureParameter;
  using PbCostingTerm = control_util::pb::PredictionPathCostingTerm;
  struct StepwisePrimitives {
    vehicle_model::VehicleRepresentation ego_representation;
  };

  using PbSolution = planner::pb::PathSolution;
  using PbWarmstarterDebug = planner::pb::PathWarmstarterDebug;
  using PbSolverDebug = planner::pb::PathSolverDebug;
  using PbSolverInterationDebug = planner::pb::PathSolverIteration;

  using Primitives = prediction::PredictionPathPrimitives;

  static constexpr int FEATURE_DIM = pb::PredictionPathFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace prediction {

using PredictionPathFormula =
    control_util::Formula<control_util::DdpProblemType::kPredictionPath>;

// PredictionPathFeatureParameter includes parameters defined through
// motion/shape model that are not included in the top level costing .conf files
// since they are vehicle-specific. This helper function merges feature
// parameter from .conf file with motion model to create the full
// PredictionPathFeatureParameter object.
control_util::pb::PredictionPathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PredictionPathFeatureParameter feature_param,
    const vehicle_model::GeometricModelWithAxleRectangularShape&
        motion_model_with_shape);

}  // namespace prediction

namespace control_util {

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kPredictionPath>(
    int step, int total_step_size,
    const prediction::PredictionPathFormula::PbCostingTerm& costing_term,
    const prediction::PredictionPathFormula::PbFeatureParameter& feature_param,
    const prediction::PredictionPathFormula::State& x,
    const prediction::PredictionPathFormula::Control& u,
    const prediction::PredictionPathFormula::StepwisePrimitives&
        stepwise_primitives,
    const prediction::PredictionPathFormula::Primitives& primitives,
    prediction::PredictionPathFormula::Feature* feature);

template <>
prediction::PredictionPathFormula::StepwisePrimitives
prediction::PredictionPathFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;

}  // namespace control_util

#endif  // ONBOARD_PREDICTION_COMMON_DDP_PATH_PREDICTION_PATH_COSTING_H_
