#include "planner/speed/reasoning/prediction_decision_maker_util.h"

#include <algorithm>
#include <string>

#include <gtest/gtest.h>

#include "hdmap/lib/test/test_util.h"
#include "math/pose_2d.h"
#include "planner/planning_gflags.h"
#include "planner/speed/agent_reaction/agent_reaction_param_def.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/agent_trajectory_info.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/test/reasoning_test_fixture.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/prediction_decision.pb.h"
#include "planner_protos/speed_constraint.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "prediction_protos/agent.pb.h"
#include "voy_protos/perception_object_type.pb.h"

namespace planner {
namespace speed {
namespace prediction_decision_maker_util {

class PredictionDecisionMakerUtilTest : public ::testing::Test,
                                        public ReasoningTestFixture {
 public:
  PredictionDecisionMakerUtilTest() {
    // Currently this test uses default map(Fremont), you can also use other
    // maps by moving this set up map step into each individual test. Now we
    // also support scene maps(See ReasoningTestFeature::SetUpSceneMap. You can
    // check ReasoningTestFixtureTest for some examples).
    SetUpRegionMap(hdmap::test_util::kFremontData);
  }
};

TEST_F(PredictionDecisionMakerUtilTest, DoNotIgnoreAgentBehindWhenMerge) {
  SetEgoPose(/*lane_id=*/97151, /*portion=*/0.5);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{97151, 97155, 97157},
                             planner::pb::ManeuverType::DECOUPLED_FORWARD);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/97149, /*portion=*/0.1,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/97149,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/97153, /*end_portion=*/1.0,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();
  ASSERT_EQ(1, wrapped_prediction_decisions().size());
  ASSERT_EQ(
      1, wrapped_prediction_decisions().front().prediction_decisions().size());

  const PredictionDecision& decision =
      wrapped_prediction_decisions().front().prediction_decisions().front();
  EXPECT_TRUE(decision.Nominal());
}

// https://drive.google.com/file/d/1GSJj4aad1IHVawjzrEjC_sGx3_eC6TUg/view?usp=drive_link
TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForCyclistBehindEgoWhenEgoTurnsRight) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/13299, /*portion=*/0.1);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{13299, 10997});

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, /*lane_id=*/9633,
               /*portion=*/0.8, /*length=*/3.0, /*width=*/1.0,
               /*velocity=*/5.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/9633, /*start_portion=*/0.8,
                                 /*end_lane_id=*/58083, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  EXPECT_TRUE(ComputeReasonerInCharge(
                  world_context(), reasoning_object, trajectory_info(),
                  agent_trajectory_info,
                  agent_trajectory_info.overlap_regions().front().get(),
                  /*agent_policy_debug_str=*/nullptr) ==
              planner::speed::pb::CROSS_AGENT);
}

TEST_F(
    PredictionDecisionMakerUtilTest,
    ComputerReasonerInChargeForTrailingVehicleTailgatingDuringLaneFollowing) {
  SetUpSceneMap(hdmap::test_util::SceneType::kBusBulb);
  SetEgoPose(/*lane_id=*/129489, /*portion=*/0.8, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{129489, 129497});

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/129489,
      /*portion=*/0.4, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/10.0);

  AddStraightPredictedTrajectory(/*start_lane_id=*/129489,
                                 /*start_portion=*/0.4,
                                 /*end_lane_id=*/129497, /*end_portion=*/0.5,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());
  EXPECT_TRUE(reasoning_object.is_likely_tailgating());
  EXPECT_EQ(planner::speed::pb::TAILGATER,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().front().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForTrailingVehicleTailgatingDuringXLaneNudge) {
  FLAGS_planning_enable_decoupled_xlane_nudge = true;
  SetUpSceneMap(hdmap::test_util::SceneType::kTjunction);
  SetEgoPose(/*lane_id=*/32915, /*portion=*/0.5, /*speed=*/10.0);

  planner::pb::IntentionResult& ego_intention = mutable_ego_intention();
  ego_intention.set_is_boundary_extended(true);
  ego_intention.set_homotopy(
      planner::pb::IntentionResult::XLANE_PASS_FROM_LEFT);

  const std::string path_wkt =
      "LINESTRING(-11199.5 -33991.9,"
      "-11205.2 -33837.8,"
      "-11205.3 -33834.7,"
      "-11205.9 -33831.6,"
      "-11206.5 -33825.5,"
      "-11207.1 -33822.4,"
      "-11207.7 -33819.4,"
      "-11208.3 -33816.3,"
      "-11206.3 -33807,"
      "-11210.6 -33683.7)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {32915, 32927, 9385},
      planner::pb::ManeuverType::DECOUPLED_FORWARD);

  // Spawn a random agent as a blocking vehicle.
  prediction::pb::Agent& blocking_vehicle =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/32915, /*portion=*/0.55,
               /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/0.0);

  // Set a stationary predicted trajectory for the agent.
  AddStationaryPredictedTrajectory(/*lane_id=*/32915, /*portion=*/0.55,
                                   /*likelihood=*/1.0, /*traj_id=*/1,
                                   blocking_vehicle);

  // Add a trailing vehicle nudging to the left after ego.
  prediction::pb::Agent& trailing_vehicle =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/2, /*lane_id=*/32915, /*portion=*/0.4,
               /*length=*/5.0, /*width=*/2.5,
               /*velocity=*/10.0, /*height=*/1.5);
  AddStraightPredictedTrajectory(/*start_lane_id=*/32915, /*start_portion=*/0.4,
                                 /*end_lane_id=*/32915, /*end_portion=*/0.8,
                                 /*likelihood=*/1.0, /*traj_id=*/1,
                                 trailing_vehicle);

  // Validate the agent list.
  EXPECT_EQ(2, agent_list().agent_list().size());

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    EXPECT_EQ(1, reasoning_input.agent_trajectory_infos.size());
  }

  const auto iter = std::find_if(
      reasoning_inputs().begin(), reasoning_inputs().end(),
      [&trailing_vehicle](const PredictionDecisionMakerInput& reasoning_input) {
        return reasoning_input.reasoning_object.id() ==
               trailing_vehicle.tracked_object().id();
      });
  const ReasoningObject& reasoning_object = iter->reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      iter->agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());
  EXPECT_TRUE(reasoning_object.is_likely_tailgating());
  EXPECT_EQ(planner::speed::pb::TAILGATER,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().front().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForRightTurningAgent) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
  SetEgoPose(/*lane_id=*/9023, /*portion=*/0.9, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9023, 9713, 57543});

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/13347,
      /*portion=*/0.2, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/15.0);

  // Construct agent's predicted trajectory is turning right.
  std::vector<math::Pose2d> waypoints{
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.2,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.6,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0),
      LanePointToPoseWithShift(/*lane_id=*/13347, /*portion=*/0.9,
                               /*arclength_shift=*/0.0,
                               /*lateral_shift=*/0.0,
                               /*theta_shift=*/0.0)};
  AddMultiPointPredictedTrajectory(waypoints,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());
  EXPECT_EQ(planner::speed::pb::CROSS_AGENT,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().back().get(),
                /*agent_policy_debug_str=*/nullptr));
}

// https://drive.google.com/file/d/1ys7qeGh9LFEOMr38sugORGXuY9PKXq4-/view?usp=drive_link
TEST_F(PredictionDecisionMakerUtilTest,
       AddAgentPoliciesForCyclistBehindEgoWhenEgoTurnsRight) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9025, /*portion=*/0.8);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9025, 13299, 10997});

  const math::Pose2d start_pose(/*x_in=*/-8885.246388, /*y_in=*/-33543.165532,
                                /*yaw_in=*/1.740504);
  const math::Pose2d end_pose(/*x_in=*/-8896.915552, /*y_in=*/-33475.066798,
                              /*yaw_in=*/1.740504);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/3.0, /*width=*/1.0, /*velocity=*/8.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  std::string debug_str;
  PredictionDecision prediction_decision(
      reasoning_object, agent_trajectory_info,
      planner::speed::pb::Decision::DECISION_NOMINAL);
  AddAgentPoliciesForLeadAndMergeAgentReasoner(
      world_context(), trajectory_info(), urgency_info(), agent_trajectory_info,
      reasoning_object, pinch_regions_mapper(), GetReasoningConfig(), debug_str,
      /*overlap_region_ix=*/0, AgentReactionType(),
      /*yield_always_possible=*/false, &prediction_decision);
  EXPECT_TRUE(reasoning_object.is_behind_ego_rear_axle());
  ASSERT_EQ(prediction_decision.agent_policies().size(), 1);
  const AgentPolicy& agent_policy =
      prediction_decision.agent_policies().front();
  EXPECT_TRUE(agent_policy.generative_constraint_type() ==
              pb::GenerativeConstraintType::SAFETY_CRITICAL);
}

// https://drive.google.com/file/d/16NE5ACiuOlygVSfsNRnRxa7_dELglzW_/view?usp=drive_link
TEST_F(PredictionDecisionMakerUtilTest,
       AddAgentPoliciesForCyclistBehindEgoWhenEgoTurnsLeft) {
  SetUpSceneMap(hdmap::test_util::SceneType::kJunction);
  SetEgoPose(/*lane_id=*/9071, /*portion=*/0.8);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9083, 9071, 10523});

  const math::Pose2d start_pose(/*x_in=*/-8960.274, /*y_in=*/-33487.722,
                                /*yaw_in=*/0.283);
  const math::Pose2d end_pose(/*x_in=*/-8890.195, /*y_in=*/-33467.323,
                              /*yaw_in=*/0.283);
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/3.0, /*width=*/1.0, /*velocity=*/8.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, agent);

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  std::string debug_str;
  PredictionDecision prediction_decision(
      reasoning_object, agent_trajectory_info,
      planner::speed::pb::Decision::DECISION_NOMINAL);
  EXPECT_EQ(
      planner::speed::pb::LEAD_AND_MERGE,
      ComputeReasonerInCharge(world_context(), reasoning_object,
                              trajectory_info(), agent_trajectory_info,
                              agent_trajectory_info.overlap_regions().front(),
                              /*agent_policy_debug_str=*/nullptr));
  AddAgentPoliciesForLeadAndMergeAgentReasoner(
      world_context(), trajectory_info(), urgency_info(), agent_trajectory_info,
      reasoning_object, pinch_regions_mapper(), GetReasoningConfig(), debug_str,
      /*overlap_region_ix=*/0, AgentReactionType(),
      /*yield_always_possible=*/false, &prediction_decision);
  // TODO(speed): Take a look at the logic here. Cyclist is fully behind with
  // OverlapV2.
  EXPECT_TRUE(reasoning_object.is_behind_ego_rear_axle());
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForStationaryParallelVehicle) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
  SetEgoPose(/*lane_id=*/9109, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9109, 10997});

  // Constructs an agent with a predicted trajectory that parallels ego lane
  // sequence.
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9101,
               /*portion=*/0.2, /*length=*/4.0, /*width=*/4.0,
               /*velocity=*/0.00);
  AddStationaryPredictedTrajectory(/*lane_id=*/9101, /*portion=*/0.2,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());
  EXPECT_TRUE(reasoning_object.IsStationary());

  EXPECT_EQ(planner::speed::pb::LEAD_AND_MERGE,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().back().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForStationaryCrossVehicle) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRightStraightMerge);
  SetEgoPose(/*lane_id=*/9109, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9109, 10997});

  // Constructs an agent with a predicted trajectory that crosses ego lane
  // sequence.
  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/9713,
               /*portion=*/0.2, /*length=*/20.0, /*width=*/4.0,
               /*velocity=*/0.00);
  AddStationaryPredictedTrajectory(/*lane_id=*/9713, /*portion=*/0.2,
                                   /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());
  EXPECT_TRUE(reasoning_object.IsStationary());

  EXPECT_EQ(planner::speed::pb::CROSS_AGENT,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().back().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForExitingAgentInRoundabout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152723, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{152723, 152788});

  // Constructs an agent with a predicted trajectory that exits roundabout.
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/152724,
      /*portion=*/0.2, /*length=*/5.0, /*width=*/3.0,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/152724,
                                 /*start_portion=*/0.3,
                                 /*end_lane_id=*/166412, /*end_portion=*/0.9,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());

  EXPECT_EQ(planner::speed::pb::LEAD_AND_MERGE,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().back().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ComputerReasonerInChargeForRightTurnAgentInRounAbout) {
  SetUpSceneMap(hdmap::test_util::SceneType::kRoundabout);
  SetEgoPose(/*lane_id=*/152763, /*portion=*/0.2, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{152763, 152693, 166667});

  // Constructs an agent with a predicted trajectory that exits roundabout.
  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/152700,
      /*portion=*/0.2, /*length=*/5.0, /*width=*/3.0,
      /*velocity=*/10.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/152700,
                                 /*start_portion=*/0.3,
                                 /*end_lane_id=*/166667, /*end_portion=*/0.9,
                                 /*likelihood=*/0.9, /*traj_id=*/1, agent);

  Update();

  EXPECT_FALSE(reasoning_inputs().empty());
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();

  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());

  EXPECT_EQ(planner::speed::pb::LEAD_AND_MERGE,
            ComputeReasonerInCharge(
                world_context(), reasoning_object, trajectory_info(),
                agent_trajectory_info,
                agent_trajectory_info.overlap_regions().back().get(),
                /*agent_policy_debug_str=*/nullptr));
}

TEST_F(PredictionDecisionMakerUtilTest,
       ShouldConsiderAgentBehindWithOverlapDuringLaneChange) {
  SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.1, /*speed=*/3.0);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE,
               /*id=*/1, /*lane_id=*/kTargetLaneId, /*portion=*/0.0,
               /*length=*/4.0, /*width=*/2.5,
               /*velocity=*/12.0);

  AddStraightPredictedTrajectory(
      /*start_lane_id=*/kTargetLaneId, /*start_portion=*/0.0,
      /*end_lane_id=*/kTargetLaneId, /*end_portion=*/1.0,
      /*likelihood=*/0.9, /*traj_id=*/1, agent);

  EXPECT_TRUE(world_context().construction_zones().empty());
  EXPECT_FALSE(trajectory_info().path().empty());
  EXPECT_EQ(1, agent_list().agent_list().size());

  Update();

  ASSERT_FALSE(reasoning_inputs().empty());
  ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);

  EXPECT_TRUE(ShouldConsiderAgentBehindWithOverlapDuringLaneChange(
      world_context(), reasoning_object, agent_trajectory_info,
      trajectory_info(),
      /*start_behind_front_axle=*/true));
}

class ShouldEgoYieldAlwaysPossibleTest : public ::testing::Test,
                                         public ReasoningTestFixture {
 public:
  ShouldEgoYieldAlwaysPossibleTest() = default;

  void Prepare() {
    Update();

    ASSERT_EQ(reasoning_inputs().size(), 1);
    ASSERT_EQ(reasoning_inputs().front().agent_trajectory_infos.size(), 1);
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_inputs().front().agent_trajectory_infos.front();
    ASSERT_EQ(agent_trajectory_info.overlap_regions().size(), 1);
  }

  const ReasoningObject& GetReasoningObject() const {
    return reasoning_inputs().front().reasoning_object;
  }

  const AgentTrajectoryInfo& GetAgentTrajectoryInfo() const {
    return reasoning_inputs().front().agent_trajectory_infos.front();
  }

  const pb::OverlapRegion& GetOverlapRegion() const {
    return reasoning_inputs()
        .front()
        .agent_trajectory_infos.front()
        .overlap_regions()
        .front();
  }
};

TEST_F(ShouldEgoYieldAlwaysPossibleTest, CloseOverlapAroundCrosswalkTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/1.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8932.12, /*y_in=*/-33481.08,
                                /*yaw_in=*/-1.53);
  const math::Pose2d end_pose(/*x_in=*/-8932.41, /*y_in=*/-33490.31,
                              /*yaw_in=*/-1.53);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Prepare();

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::VRU, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleTest, FarOverlapAroundCrosswalkTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/0.9);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8930.42, /*y_in=*/-33476.91,
                                /*yaw_in=*/-1.53);
  const math::Pose2d end_pose(/*x_in=*/-8926.64, /*y_in=*/-33492.04,
                              /*yaw_in=*/-1.53);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Prepare();

  EXPECT_FALSE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::VRU, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleTest, EgoNotStationaryTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPose(/*lane_id=*/9073, /*portion=*/1.0, /*speed=*/10.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9073, 9099});

  const math::Pose2d start_pose(/*x_in=*/-8932.93, /*y_in=*/-33478.14,
                                /*yaw_in=*/-1.53);
  const math::Pose2d end_pose(/*x_in=*/-8932.41, /*y_in=*/-33490.31,
                              /*yaw_in=*/-1.53);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);

  Prepare();

  EXPECT_FALSE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::VRU, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleTest, CloseOverlapOfJaywalkerTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPoseWoYaw(/*x_pos=*/-8912.74, /*y_pos=*/-33478.47, /*lane_id=*/9099,
                  /*speed=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9099, 10993});

  const math::Pose2d start_pose(/*x_in=*/-8913.62, /*y_in=*/-33474.75,
                                /*yaw_in=*/-1.29);
  const math::Pose2d end_pose(/*x_in=*/-8910.50, /*y_in=*/-33485.72,
                              /*yaw_in=*/-1.29);
  prediction::pb::Agent& jaywalker =
      AddAgent(voy::perception::ObjectType::PED, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, jaywalker);

  Prepare();

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::JAYWALKER, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleTest, NoStrictOverlapOfJaywalkerTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kCrosswalk);
  SetEgoPoseWoYaw(/*x_pos=*/-8912.74, /*y_pos=*/-33478.85, /*lane_id=*/9099,
                  /*speed=*/0.0);
  CreatePathWithLaneSequence(/*lane_sequence_id=*/{9099, 10993});

  const math::Pose2d start_pose(/*x_in=*/-8910.62, /*y_in=*/-33477.18,
                                /*yaw_in=*/-1.37);
  const math::Pose2d end_pose(/*x_in=*/-8910.58, /*y_in=*/-33477.38,
                              /*yaw_in=*/-1.37);
  prediction::pb::Agent& jaywalker =
      AddAgent(voy::perception::ObjectType::PED, /*id*/ 1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/0.3);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, jaywalker);

  Prepare();

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::JAYWALKER, /*overlap_debug_str=*/nullptr));
}

// https://cooper.didichuxing.com/shares/u0dD6NTsNEdA
TEST_F(ShouldEgoYieldAlwaysPossibleTest, PullOutYieldAlwaysPossibleTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49376.83, /*y_pos=*/13557.06,
             /*yaw=*/0.01, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49376.83 13557.06,49372.99 13556.63,49369.23 "
      "13555.81,49365.24 13554.76,49359.86 13553.71,49352.77 13552.14)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  SetIsPullOutJumpOut(true);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841},
      planner::pb::ManeuverType::PULL_OUT);

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/49376.98, /*y_in=*/13556.15,
                                /*yaw_in=*/0.01);
  const math::Pose2d end_pose(/*x_in=*/49361.59, /*y_in=*/13556.06,
                              /*yaw_in=*/0.01);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);
  Prepare();

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr));
}

// https://cooper.didichuxing.com/shares/a394g8J3mDlY
TEST_F(ShouldEgoYieldAlwaysPossibleTest, PullOverYieldAlwaysPossibleTest) {
  SetUpSceneMap(hdmap::test_util::SceneType::kPickUpDropOffZone);
  SetEgoLane(/*current_lane_id=*/166835);
  SetEgoPose(/*x_pos=*/49397.81, /*y_pos=*/13552.45,
             /*yaw=*/0.01, /*speed=*/0.0, /*accel=*/0.0);
  const std::string path_wkt =
      "LINESTRING(49397.81 13552.45,49393.34 13552.20,49390.63 "
      "13553.59,49388.10 13554.41,49385.61 13555.64,49382.65 13556.72,49378.80 "
      "13557.22)";
  math::geometry::Polyline2d path_curve;
  math::geometry::ReadWKT(path_wkt, path_curve);
  SetPullOverStatus(planner::pull_over::PullOverStatus(
      /*should_trigger_pull_over=*/true,
      /*execution_state=*/planner::pb::PullOverExecutionState::EXECUTE,
      /*pull_over_progress=*/planner::pb::PullOverProgress::kStarted,
      /*is_in_abort_recovery=*/false));
  SetBehaviorType(planner::pb::BehaviorType::DECOUPLED_PULL_OVER);
  SetPullOverDestination(/*portion=*/1.0, /*is_pull_over_jump_in=*/false);
  CreatePathWithPathCurveAndLaneSequence(
      math::geometry::PolylineCurve2d(path_curve), {167576, 166835, 166841});

  EXPECT_FALSE(path().empty());
  EXPECT_FALSE(lane_sequence_result().lane_sequence.empty());

  const math::Pose2d start_pose(/*x_in=*/49401.49, /*y_in=*/13553.23,
                                /*yaw_in=*/0.01);
  const math::Pose2d end_pose(/*x_in=*/49382.82, /*y_in=*/13553.53,
                              /*yaw_in=*/0.01);
  prediction::pb::Agent& cyclist =
      AddAgent(voy::perception::ObjectType::CYCLIST, /*id=*/1, start_pose,
               /*length=*/1.5,
               /*width=*/1.0, /*velocity=*/5.0);
  AddStraightPredictedTrajectory(start_pose, end_pose, /*likelihood=*/0.9,
                                 /*traj_id=*/1, cyclist);
  Prepare();

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossible(
      world_context(), GetReasoningObject(), GetOverlapRegion(),
      pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr));
}

class ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest
    : public ::testing::Test,
      public ReasoningTestFixture {
 public:
  ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest() {
    SetUpSceneMap(hdmap::test_util::SceneType::kMerge);
  }
};

TEST_F(ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest,
       EgoSpeedConditionTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.0, /*speed=*/1.4);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.6, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/12.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.6,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_EQ(1, agent_list().agent_list().size());
  EXPECT_EQ(1, reasoning_inputs().size());
  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_EQ(1, agent_trajectory_info.overlap_regions().size());

  EXPECT_FALSE(ShouldEgoYieldAlwaysPossibleDuringLaneChange(
      world_context(), trajectory_info(), reasoning_object,
      agent_trajectory_info.predicted_trajectory(),
      agent_trajectory_info.overlap_regions().front(),
      pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest, EgoBlocksAgentTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.5, /*speed=*/0.8);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*lane_id=*/kTargetLaneId,
               /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/1.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.4,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_EQ(1, agent_list().agent_list().size());
  EXPECT_EQ(1, reasoning_inputs().size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_FALSE(agent_trajectory_info.overlap_regions().empty());

  EXPECT_FALSE(ShouldEgoYieldAlwaysPossibleDuringLaneChange(
      world_context(), trajectory_info(), reasoning_object,
      agent_trajectory_info.predicted_trajectory(),
      agent_trajectory_info.overlap_regions().front(),
      pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest, EgoNotBlockAgentTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.5, /*speed=*/0.2);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent =
      AddAgent(voy::perception::ObjectType::VEHICLE, /*id=*/1,
               /*lane_id=*/kTargetLaneId,
               /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
               /*velocity=*/1.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.1,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/1.0,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent);

  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_EQ(1, agent_list().agent_list().size());
  EXPECT_EQ(1, reasoning_inputs().size());

  const ReasoningObject& reasoning_object =
      reasoning_inputs().front().reasoning_object;
  EXPECT_EQ(1, reasoning_inputs().front().agent_trajectory_infos.size());
  const AgentTrajectoryInfo& agent_trajectory_info =
      reasoning_inputs().front().agent_trajectory_infos.front();
  EXPECT_FALSE(agent_trajectory_info.overlap_regions().empty());

  EXPECT_TRUE(ShouldEgoYieldAlwaysPossibleDuringLaneChange(
      world_context(), trajectory_info(), reasoning_object,
      agent_trajectory_info.predicted_trajectory(),
      agent_trajectory_info.overlap_regions().front(),
      pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr));
}

TEST_F(ShouldEgoYieldAlwaysPossibleDuringLaneChangeTest, LateralConditionTest) {
  constexpr int64_t kSourceLaneId = 128479;
  constexpr int64_t kTargetLaneId = 128481;
  SetEgoPose(/*lane_id=*/kSourceLaneId, /*portion=*/0.5, /*speed=*/0.2);
  CreateLaneChangePath(/*source_lane_id=*/kSourceLaneId,
                       /*target_lane_id=*/kTargetLaneId);
  SetLaneChangeStatus(
      /*direction=*/planner::pb::LaneChangeMode::LEFT_LANE_CHANGE,
      /*lane_change_state=*/
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS,
      /*start_arclength=*/5.0, kSourceLaneId, kTargetLaneId,
      /*is_current_homotopy_lane_change=*/true);

  prediction::pb::Agent& agent1 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/1, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/1.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.2,
                                 /*end_lane_id=*/kTargetLaneId,
                                 /*end_portion=*/0.8,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent1);
  prediction::pb::Agent& agent2 = AddAgent(
      voy::perception::ObjectType::VEHICLE, /*id=*/2, /*lane_id=*/kTargetLaneId,
      /*portion=*/0.0, /*length=*/4.0, /*width=*/2.0,
      /*velocity=*/1.0);
  AddStraightPredictedTrajectory(/*start_lane_id=*/kTargetLaneId,
                                 /*start_portion=*/0.4,
                                 /*end_lane_id=*/kSourceLaneId,
                                 /*end_portion=*/0.8,
                                 /*likelihood=*/1.0,
                                 /*traj_id=*/1, agent2);
  UpdateForLaneChange();

  EXPECT_TRUE(trajectory_info().is_lane_change_in_progress());
  EXPECT_EQ(2, agent_list().agent_list().size());
  EXPECT_EQ(2, reasoning_inputs().size());
  for (const PredictionDecisionMakerInput& reasoning_input :
       reasoning_inputs()) {
    const ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    EXPECT_EQ(1, reasoning_input.agent_trajectory_infos.size());
    const AgentTrajectoryInfo& agent_trajectory_info =
        reasoning_input.agent_trajectory_infos.front();
    EXPECT_FALSE(agent_trajectory_info.overlap_regions().empty());

    const bool yield_always_possible_during_lc =
        ShouldEgoYieldAlwaysPossibleDuringLaneChange(
            world_context(), trajectory_info(), reasoning_object,
            agent_trajectory_info.predicted_trajectory(),
            agent_trajectory_info.overlap_regions().front(),
            pb::ReasonerId::LEAD_AND_MERGE, /*overlap_debug_str=*/nullptr);
    if (reasoning_object.id() == 1) {
      // Agent can nudge before ego fully stops.
      EXPECT_TRUE(yield_always_possible_during_lc);
    } else if (reasoning_object.id() == 2) {
      // Agent can't nudge before ego fully stops.
      EXPECT_FALSE(yield_always_possible_during_lc);
    }
  }
}

}  // namespace prediction_decision_maker_util
}  // namespace speed
}  // namespace planner
