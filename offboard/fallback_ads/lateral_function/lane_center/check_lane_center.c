#include "lateral_function/lane_center/check_lane_center.h"

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Components/Rte_LatCtrlFct.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "common_math_library/debounce_method.h"
#include "common_math_library/vehicle_parameters.h"
#include "lateral_function/boundary_data_processing/check_lane_property.h"
#include "lateral_function/lane_center/lane_departure.h"
#include "lateral_function/lateral_function_parameter.h"

static void DebounceLaneCenteringCondition(float ego_velocity,
                                           uint16_t *invalid_bitfield_ptr) {
  static bool invalid_array_turn_on_last_cycle[16] = {false};
  static bool invalid_array_turn_off_last_cycle[16] = {false};
  static float turn_on_delay_timer[16] = {0.0};
  static float turn_off_delay_timer[16] = {0.0};

  for (int i = 0; i < 16; ++i) {
    bool input =
        (((*invalid_bitfield_ptr) & (1 << i)) != 0U) && ego_velocity > 1.0F;
    CML_TurnOnDelay(input, 0.3, FSA_TASK_CYCLE_TIME, &turn_on_delay_timer[i],
                    &invalid_array_turn_on_last_cycle[i]);
    CML_TurnOffDelay(invalid_array_turn_on_last_cycle[i], 1.0,
                     FSA_TASK_CYCLE_TIME, &turn_off_delay_timer[i],
                     &invalid_array_turn_off_last_cycle[i]);
    invalid_array_turn_off_last_cycle[i]
        ? CML_SetBit(*invalid_bitfield_ptr, i)
        : CML_ClearBit(*invalid_bitfield_ptr, i);
  }
}

uint16_t CheckLaneCenteringCondition(
    const VSI_VehicleInfo_Struct *vehicle_info_ptr,
    const CSI_LaneInfo_Struct *lane_info_ptr) {
  static uint16_t invalid_bitfield = 0U;

  // bit0: both lanes exist
  bool both_lanes_exist =
      (lane_info_ptr->CSI_LaneIDLf != 0) && (lane_info_ptr->CSI_LaneIDRi != 0);
  (!both_lanes_exist) ? CML_SetBit(invalid_bitfield, 0U)
                      : CML_ClearBit(invalid_bitfield, 0U);

  // bit1: the curvature radius of the lane cannot be too small
  bool lane_radius_valid = CheckLaneRadius(lane_info_ptr->CSI_CurvatureLf,
                                           lane_info_ptr->CSI_CurvatureRi);
  (!lane_radius_valid) ? CML_SetBit(invalid_bitfield, 1U)
                       : CML_ClearBit(invalid_bitfield, 1U);

  // bit2: the length of the lane cannot be too short
  bool lane_length_valid = CheckLaneLength(lane_info_ptr->CSI_PosXEndLf,
                                           lane_info_ptr->CSI_PosXEndRi);
  (!lane_length_valid) ? CML_SetBit(invalid_bitfield, 2U)
                       : CML_ClearBit(invalid_bitfield, 2U);

  // calculate left lane position and position at front axle
  float left_lane_y0 = CalculateLanePosition(
      lane_info_ptr->CSI_PosY0Lf, lane_info_ptr->CSI_HeadingLf,
      lane_info_ptr->CSI_CurvatureLf, lane_info_ptr->CSI_CrvRateLf,
      GetVehicleWheelBase());

  float left_lane_heading = CalculateLaneHeading(
      lane_info_ptr->CSI_HeadingLf, lane_info_ptr->CSI_CurvatureLf,
      lane_info_ptr->CSI_CrvRateLf, GetVehicleWheelBase());

  // calculate right lane position and position at front axle
  float right_lane_y0 = CalculateLanePosition(
      lane_info_ptr->CSI_PosY0Ri, lane_info_ptr->CSI_HeadingRi,
      lane_info_ptr->CSI_CurvatureRi, lane_info_ptr->CSI_CrvRateRi,
      GetVehicleWheelBase());

  float right_lane_heading = CalculateLaneHeading(
      lane_info_ptr->CSI_HeadingRi, lane_info_ptr->CSI_CurvatureRi,
      lane_info_ptr->CSI_CrvRateRi, GetVehicleWheelBase());

  // bit3: the lane width is invalid
  bool lane_width_valid = CheckLaneWidth(left_lane_y0, left_lane_heading,
                                         right_lane_y0, right_lane_heading);
  (!lane_width_valid) ? CML_SetBit(invalid_bitfield, 3U)
                      : CML_ClearBit(invalid_bitfield, 3U);

  // bit4: lane position is invalid
  bool distance_to_lane_valid =
      CheckDistanceToLane(left_lane_y0, right_lane_y0);
  (!distance_to_lane_valid) ? CML_SetBit(invalid_bitfield, 4U)
                            : CML_ClearBit(invalid_bitfield, 4U);

  // bit5: lane in capture range is invalid
  bool lane_in_capture_range_valid = CheckLaneInCaptureRange(
      left_lane_y0, left_lane_heading, right_lane_y0, right_lane_heading);
  (!lane_in_capture_range_valid) ? CML_SetBit(invalid_bitfield, 5U)
                                 : CML_ClearBit(invalid_bitfield, 5U);

  // bit6: lane departure
  bool left_lane_departure_enable = CheckLeftLaneDeparture(
      lane_info_ptr->CSI_LaneIDLf, left_lane_y0, left_lane_heading,
      vehicle_info_ptr->VSI_LongitudinalVelocity);
  bool right_lane_departure_enable = CheckRightLaneDeparture(
      lane_info_ptr->CSI_LaneIDRi, right_lane_y0, right_lane_heading,
      vehicle_info_ptr->VSI_LongitudinalVelocity);
  bool lane_departure_enable =
      left_lane_departure_enable || right_lane_departure_enable;
  (lane_departure_enable) ? CML_SetBit(invalid_bitfield, 6U)
                          : CML_ClearBit(invalid_bitfield, 6U);
  // bit7: lane merge
  bool lane_merge_enable = CheckLaneMerge(lane_info_ptr);
  (lane_merge_enable) ? CML_SetBit(invalid_bitfield, 7U)
                      : CML_ClearBit(invalid_bitfield, 7U);
  // debounce process
  DebounceLaneCenteringCondition(vehicle_info_ptr->VSI_LongitudinalVelocity,
                                 &invalid_bitfield);
  return invalid_bitfield;
}
