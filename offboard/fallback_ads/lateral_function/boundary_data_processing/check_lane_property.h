#ifndef OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_CHECK_LANE_PROPERTY_H_
#define OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_CHECK_LANE_PROPERTY_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>

#include "_out/Appl/GenData/Rte_Type.h"

extern float CalculateLanePosition(float y0, float heading, float curvature,
                                   float curvature_change, float x);

extern float CalculateLaneHeading(float heading, float curvature,
                                  float curvature_change, float x);

extern bool CheckLaneRadius(float left_lane_curvature,
                            float right_lane_curvature);

extern bool CheckLaneLength(float left_lane_length, float right_lane_length);

extern bool CheckLaneWidth(float left_lane_y0, float left_lane_heading,
                           float right_lane_y0, float right_lane_heading);

extern bool CheckDistanceToLane(float left_lane_y0, float right_lane_y0);

extern bool CheckLaneInCaptureRange(float left_lane_y0, float left_lane_heading,
                                    float right_lane_y0,
                                    float right_lane_heading);

extern bool CheckLaneMerge(const CSI_LaneInfo_Struct *lane_info_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LATERAL_FUNCTION_BOUNDARY_DATA_PROCESSING_CHECK_LANE_PROPERTY_H_
