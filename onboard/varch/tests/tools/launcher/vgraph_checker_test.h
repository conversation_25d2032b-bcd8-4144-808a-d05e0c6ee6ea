#ifndef ONBOARD_VARCH_TESTS_TOOLS_LAUNCHER_VGRAPH_CHECKER_TEST_H_
#define ONBOARD_VARCH_TESTS_TOOLS_LAUNCHER_VGRAPH_CHECKER_TEST_H_

#include <memory>
#include <string>
#include <tuple>

#include <gtest/gtest.h>

#include "varch/tools/launcher/vgraph_checker.h"

namespace varch {
namespace tools {
namespace launcher {

using VGraphChecker = varch::tools::launcher::VGraphChecker;

struct VGraphConfParams {
  std::tuple<std::string, bool /*is valid*/> vgraph_conf;
};

class VGraphCheckerTest : public testing::TestWithParam<VGraphConfParams> {
 public:
  void SetUp() override { vgraph_checker_ = std::make_unique<VGraphChecker>(); }

  void TearDown() override { vgraph_checker_.reset(); }

  [[nodiscard]] VGraph<PERSON>he<PERSON>& GetVGraphChecker() { return *vgraph_checker_; }

 private:
  std::unique_ptr<VGraphChecker> vgraph_checker_;
};

}  // namespace launcher
}  // namespace tools
}  // namespace varch

#endif  // ONBOARD_VARCH_TESTS_TOOLS_LAUNCHER_VGRAPH_CHECKER_TEST_H_
