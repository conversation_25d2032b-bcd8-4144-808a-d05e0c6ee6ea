#include "planner/speed/reasoning_input/world_context.h"

#include <algorithm>
#include <cmath>
#include <map>
#include <optional>
#include <unordered_map>
#include <utility>

#include "math/math_util.h"
#include "planner/constants.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"

#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_decision.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"

namespace planner {
namespace speed {
namespace {

// When a stationary vehicle is behind Ego within this threshold, it is
// considered as waiting behind Ego.
constexpr double kWaitingBehindEgoMaxDistanceInMeter = 10.0;
//  A fully relaxed (1.0) speed limit will decay to 0 after 4 seconds.
constexpr double kRelaxSpeedLimitDecayFactor = 0.025;

// Gets the dominant risk object id from the previous search result. Returns
// zero if there is no dominant risk object. dominant risk object is defined
// as 1.) was dominant constraint 2.) had yield decision 3.) selected discomfort
// >= 0.75
ObjectId GetDominantRiskObjectId(
    const speed::pb::SpeedSeed& previous_speed_seed) {
  const speed::pb::SpeedSolverResult& previous_solver_result =
      previous_speed_seed.speed_solver_result();
  [[maybe_unused]] const int previous_dominant_constraint_ix =
      previous_solver_result.dominant_constraint_ix();
  [[maybe_unused]] const double previous_discomfort =
      previous_solver_result.selected_discomfort();
  if (previous_speed_seed.dominant_object_history().empty()) {
    return kInvalidObjectId;
  }

  const pb::DominantObjectInfo& last_iter_dominant_object_info =
      *(previous_speed_seed.dominant_object_history().rbegin());

  if (last_iter_dominant_object_info.dominant_object_id() <= kInvalidObjectId ||
      last_iter_dominant_object_info.selected_discomfort() <= 0.5 ||
      last_iter_dominant_object_info.decision() !=
          speed::pb::SpeedDecision::YIELD) {
    // Currently dominant constraint is not well defined when the search result
    // is emergency brake. So we latch the previous result for emergency brake.
    // Note this will not recall the case if Ego decides to emergency brake at
    // the first iteration. (cn11476067)
    // TODO(mengze): further fix this problem.
    if (last_iter_dominant_object_info.conflict_resolving_type() ==
        speed::pb::ConflictResolvingType::EMERGENCY_STOP_PROFILE) {
      return previous_speed_seed.world_context_seed()
          .previous_dominant_risk_object_id();
    }

    return kInvalidObjectId;
  }

  // There are a few cases when the obj_id is negative for unreal objects. Clap
  // it to kInvalidObjectId.
  return last_iter_dominant_object_info.dominant_object_id();
}

// Returns true if it is a constraint is stopping ego in purpose, e.g. traffic
// light constraint or the stop point from PIPO business logic.
bool IsConstraintStopEgoInPurpose(const pb::Constraint& constraint) {
  if (constraint.generating_reasoner_id() !=
          pb::ReasonerId_Name(pb::ReasonerId::TRAFFIC_LIGHT) &&
      constraint.generating_reasoner_id() !=
          pb::ReasonerId_Name(pb::ReasonerId::PULLOUT) &&
      constraint.generating_reasoner_id() !=
          pb::ReasonerId_Name(pb::ReasonerId::DESTINATION)) {
    return false;
  }

  constexpr double kMaxDistWaitingForTrafficLightInMeter = 10.0;
  constexpr double kMaxDistConsideringPullOverPullOutConstraintInMeter = 2.0;

  const double min_dist_to_consider =
      constraint.generating_reasoner_id() ==
              pb::ReasonerId_Name(pb::ReasonerId::TRAFFIC_LIGHT)
          ? kMaxDistWaitingForTrafficLightInMeter
          : kMaxDistConsideringPullOverPullOutConstraintInMeter;

  DCHECK(!constraint.states().empty());

  return constraint.states()[0].strict_start_x() < min_dist_to_consider;
}

}  // namespace

WorldContext::WorldContext(const WorldModel& world_model,
                           planner::pb::MotionMode motion_mode)
    : robot_state_(world_model.robot_state()),
      joint_pnc_map_service_(world_model.GetLatestJointPncMapService()),
      order_start_time_info_(world_model.order_start_time_info()),
      occlusion_checker_(world_model.occlusion_checker()),
      construction_zones_(world_model.construction_zones()),
      assist_responses_(world_model.assist_responses()),
      assist_task_queue_(
          world_model.assist_task_scheduler().assist_task_queue()),
      remote_assist_signal_(
          world_model.assist_task_scheduler().remote_assist_signal()),
      waypoint_assist_tracker_(world_model.waypoint_assist_tracker()),
      mrc_request_ptr_(world_model.mrc_request_ptr().get()),
      replan_request_ptr_(world_model.replan_request().get()),
      traffic_light_detection_(&(world_model.traffic_light_detection())),
      distance_to_destination_m_(
          world_model.regional_map().regional_path.distance_to_destination_m),
      is_ego_position_on_route_ready_for_pull_out_(
          world_model.IsEgoPositionOnRouteReadyForPullOut()),
      distance_to_ride_start_position_(
          world_model.OrderStartPositionDistance()),
      should_planner_respond_mrc_(world_model.should_planner_respond_mrc()),
      should_trigger_pull_out_by_mrc_(
          world_model.should_trigger_pull_out_by_mrc()),
      has_pull_out_finished_(world_model.HasPullOutFinished()),
      route_solution_mission_id_(
          world_model.global_route_solution()->route_solution().mission_id()),
      motion_mode_(motion_mode),
      is_high_agent_density_(world_model.IsHighAgentDensityScenario()),
      keep_ego_in_static_state_request_(
          world_model.special_planner_response_request() ==
          routing::pb::kKeepEgoInStaticState),
      should_ignore_map_change_traffic_lights_(
          world_model.should_ignore_map_change_traffic_lights()),
      object_conditional_prediction_map_(
          world_model.object_conditional_prediction_map()),
      remote_speed_limit_(
          world_model.assist_directive_generator().remote_speed_limit()),
      pickup_dropoff_zone_infos_(world_model.pickup_dropoff_zone_infos()),
      forbidden_stop_types_for_mrc_(
          world_model.immediate_pull_over_state_manager()
              .GetForbiddenStopTypesForMRC()) {}

WorldContext::WorldContext(const SpeedWorldModel& world_model,
                           planner::pb::MotionMode motion_mode)
    : robot_state_(world_model.robot_state()),
      joint_pnc_map_service_(world_model.joint_pnc_map_service()),
      order_start_time_info_(world_model.order_start_time_info()),
      occlusion_checker_(world_model.occlusion_checker()),
      construction_zones_(world_model.construction_zones()),
      assist_responses_(world_model.assist_responses()),
      assist_task_queue_(
          world_model.assist_task_scheduler().assist_task_queue()),
      remote_assist_signal_(
          world_model.assist_task_scheduler().remote_assist_signal()),
      waypoint_assist_tracker_(world_model.waypoint_assist_tracker()),
      mrc_request_ptr_(world_model.mrc_request_ptr().get()),
      replan_request_ptr_(world_model.replan_request().get()),
      traffic_light_detection_(&(world_model.traffic_light_detection())),
      distance_to_destination_m_(
          world_model.regional_map().regional_path.distance_to_destination_m),
      is_ego_position_on_route_ready_for_pull_out_(
          world_model.IsEgoPositionOnRouteReadyForPullOut()),
      distance_to_ride_start_position_(
          world_model.OrderStartPositionDistance()),
      should_planner_respond_mrc_(world_model.should_planner_respond_mrc()),
      should_trigger_pull_out_by_mrc_(world_model.ShouldTriggerPullOutByMrc()),
      has_pull_out_finished_(world_model.HasPullOutFinished()),
      route_solution_mission_id_(
          world_model.global_route_solution()->route_solution().mission_id()),
      motion_mode_(motion_mode),
      is_high_agent_density_(world_model.IsHighAgentDensityScenario()),
      keep_ego_in_static_state_request_(
          world_model.special_planner_response_request() ==
          routing::pb::kKeepEgoInStaticState),
      should_ignore_map_change_traffic_lights_(
          world_model.should_ignore_map_change_traffic_lights()),
      object_conditional_prediction_map_(
          world_model.object_conditional_prediction_map()),
      remote_speed_limit_(
          world_model.assist_directive_generator().remote_speed_limit()),
      pickup_dropoff_zone_infos_(world_model.pickup_dropoff_zone_infos()),
      forbidden_stop_types_for_mrc_(
          world_model.immediate_pull_over_state_manager()
              .GetForbiddenStopTypesForMRC()) {}

double WorldContext::ra_to_leading_bumper() const {
  const auto& car_model_with_shape =
      robot_state().car_model_with_shape().shape_measurement();
  CHECK_GT(car_model_with_shape.length() -
               car_model_with_shape.rear_bumper_to_rear_axle(),
           0);
  CHECK_GT(car_model_with_shape.rear_bumper_to_rear_axle(), 0);
  return motion_mode_ == planner::pb::MotionMode::BACKWARD
             ? car_model_with_shape.rear_bumper_to_rear_axle()
             : (car_model_with_shape.length() -
                car_model_with_shape.rear_bumper_to_rear_axle());
}

double WorldContext::ra_to_front_axle() const {
  const auto& car_model_with_shape =
      robot_state().car_model_with_shape().shape_measurement();
  return car_model_with_shape.length() -
         car_model_with_shape.rear_bumper_to_rear_axle() -
         car_model_with_shape.front_bumper_to_front_axle();
}

double WorldContext::ra_to_trailing_bumper_shift() const {
  const auto& car_model_with_shape =
      robot_state().car_model_with_shape().shape_measurement();
  CHECK_GT(car_model_with_shape.length() -
               car_model_with_shape.rear_bumper_to_rear_axle(),
           0);
  CHECK_GT(car_model_with_shape.rear_bumper_to_rear_axle(), 0);
  return motion_mode_ == planner::pb::MotionMode::BACKWARD
             ? -(car_model_with_shape.length() -
                 car_model_with_shape.rear_bumper_to_rear_axle())
             : -car_model_with_shape.rear_bumper_to_rear_axle();
}

void WorldContext::PopulateDebug(pb::WorldContextDebug* debug) const {
  if (debug == nullptr) {
    return;
  }

  pb::RobotStateDebug* robot_state_debug = debug->mutable_robot_state();
  robot_state_debug->set_pose_timestamp(ego_pose_timestamp());
  robot_state_debug->set_heading(heading());
  robot_state_debug->set_is_almost_stopped(EgoAlmostStopped());

  debug->set_previous_dominant_risk_object_id(
      previous_dominant_risk_object_id_);
  debug->set_previous_dominant_risk_object_duration(
      previous_dominant_risk_object_duration_);

  debug->set_is_pull_out_triggered(is_pull_out_triggered_);
}

void WorldContext::UpdateEgoAlmostStoppedDuration(
    const pb::SpeedSeed& previous_speed_seed,
    pb::WorldContextSeed& current_seed, pb::WorldContextDebug* debug) {
  if (!EgoAlmostStopped()) {
    // Early return if ego is moving.
    current_seed.clear_ego_almost_stopped_start_timestamp();
    current_seed.clear_ego_stuck_start_timestamp();
    ego_almost_stopped_duration_ms_ = 0;
    ego_stuck_duration_ms_ = 0;
    return;
  }

  const pb::WorldContextSeed& previous_seed =
      previous_speed_seed.world_context_seed();
  // Updates ego_almost_stopped_duration_ms_ and its start timestamp.
  if (previous_seed.has_ego_almost_stopped_start_timestamp()) {
    current_seed.set_ego_almost_stopped_start_timestamp(
        previous_seed.ego_almost_stopped_start_timestamp());
  } else {
    // Previous seed does not have |ego_almost_stopped_start_timestamp|,
    // indicating ego was not stopped at last iter, so we set the current
    // timestamp as the start time stamp.
    current_seed.set_ego_almost_stopped_start_timestamp(ego_pose_timestamp());
  }

  ego_almost_stopped_duration_ms_ =
      ego_pose_timestamp() - current_seed.ego_almost_stopped_start_timestamp();

  if (debug != nullptr) {
    debug->mutable_robot_state()->set_almost_stopped_duration_ms(
        ego_almost_stopped_duration_ms_);
  }

  const bool is_ego_stopped_in_purpose = std::any_of(
      previous_speed_seed.constraint_results().begin(),
      previous_speed_seed.constraint_results().end(),
      [](const pb::SpeedConstraintResult& constraint_result) {
        return constraint_result.decision() == pb::SpeedDecision::YIELD &&
               IsConstraintStopEgoInPurpose(constraint_result.constraint());
      });

  if (is_ego_stopped_in_purpose) {
    // Ego is stopped in purpose, not considering it as stuck.
    current_seed.clear_ego_stuck_start_timestamp();
    ego_stuck_duration_ms_ = 0;
    return;
  }

  // Updates ego_stuck_duration_ms_ and its start timestamp.
  if (previous_seed.has_ego_stuck_start_timestamp()) {
    current_seed.set_ego_stuck_start_timestamp(
        previous_seed.ego_stuck_start_timestamp());
  } else {
    // Previous seed does not have |ego_stuck_start_timestamp|,
    // indicating ego was not stuck at last iter, so we set the current
    // timestamp as the start time stamp.
    current_seed.set_ego_stuck_start_timestamp(ego_pose_timestamp());
  }

  ego_stuck_duration_ms_ =
      ego_pose_timestamp() - current_seed.ego_stuck_start_timestamp();

  if (debug != nullptr) {
    debug->mutable_robot_state()->set_stuck_duration_ms(ego_stuck_duration_ms_);
  }
}

void WorldContext::UpdateEgoComeToFullStopTimestamp(
    const planner::pb::Trajectory& selected_trajectory,
    pb::WorldContextDebug* debug) {
  const google::protobuf::RepeatedPtrField<planner::pb::TrajectoryPose>&
      trajectory_poses = selected_trajectory.poses();
  auto first_stop_iter = std::find_if(
      trajectory_poses.begin(), trajectory_poses.end(),
      [](const planner::pb::TrajectoryPose& pose) {
        return pose.speed() < constants::kDefaultEgoNearStaticSpeedInMps;
      });
  if (first_stop_iter != trajectory_poses.end()) {
    ego_come_to_full_stop_timestamp_ = first_stop_iter->timestamp();
    if (debug != nullptr) {
      debug->mutable_robot_state()->set_will_stop_ts(
          ego_come_to_full_stop_timestamp_.value());
    }
  }
}

void WorldContext::UpdateRelaxSpeedLimitInfo(
    const TrajectoryInfo& trajectory_info,
    const speed::pb::SpeedSeed& previous_speed_seed,
    pb::WorldContextSeed& current_seed, pb::WorldContextDebug* /* debug */) {
  constexpr double kRoadSpeedLimitMaxRelaxationDiscomfort = 0.5;
  const double previous_discomfort =
      previous_speed_seed.speed_solver_result().selected_discomfort();
  const bool is_lane_change = trajectory_info.is_lane_change_preparation() ||
                              trajectory_info.is_lane_change_in_progress();
  // Update the factor related to decaying ego exceeding road speed limit.
  if (is_lane_change &&
      (previous_discomfort >
           kRoadSpeedLimitMaxRelaxationDiscomfort - math::constants::kEpsilon ||
       previous_discomfort >
           previous_speed_seed.world_context_seed()
               .discomfort_to_decay_ego_exceeding_road_speed_limit())) {
    // The road speed limit during lane change will be relaxed to the maximum
    // (120%) when the discomfort is greater than 0.5. When the discomfort is
    // less than 0.5, the higher the discomfort is, the larger the road speed
    // limit is. If the discomfort reaches 0.5 and above or the current
    // discomfort goes higher than the previous one, we reset the decay counting
    // so that we could decay the part where ego's speed exceeds the larger road
    // speed limit.
    relax_ego_exceeding_road_speed_limit_factor_ =
        1.0 - kRelaxSpeedLimitDecayFactor;
  } else {
    relax_ego_exceeding_road_speed_limit_factor_ =
        std::max(0.0, previous_speed_seed.world_context_seed()
                              .relax_ego_exceeding_road_speed_limit_factor() -
                          kRelaxSpeedLimitDecayFactor);
  }

  // Update the discomfort value to decay ego exceeding road speed limit.
  if (is_lane_change) {
    discomfort_to_decay_ego_exceeding_road_speed_limit_ =
        std::make_optional(previous_discomfort);
  }

  // Update current seed.
  discomfort_to_decay_ego_exceeding_road_speed_limit_.has_value()
      ? current_seed.set_discomfort_to_decay_ego_exceeding_road_speed_limit(
            discomfort_to_decay_ego_exceeding_road_speed_limit_.value())
      : current_seed.clear_discomfort_to_decay_ego_exceeding_road_speed_limit();
  current_seed.set_relax_ego_exceeding_road_speed_limit_factor(
      relax_ego_exceeding_road_speed_limit_factor_);
}

void WorldContext::UpdateLaneChangeState(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed) {
  previous_selected_lane_change_state_ =
      previous_iter_seed.selected_lane_change_state();
}

void WorldContext::UpdateHasVehicleWaitingBehindEgo(
    const std::unordered_map<ObjectId, PlannerObject>& objects,
    const std::map<ObjectId, speed::pb::ObjectProximityInfo>&
        object_proximity_infos,
    pb::WorldContextDebug* debug) {
  if (!EgoAlmostStopped()) {
    has_vehicle_waiting_behind_ego_ = false;
    if (debug != nullptr) {
      debug->mutable_robot_state()->set_has_vehicle_waiting_behind_ego(
          has_vehicle_waiting_behind_ego_);
    }
    return;
  }
  has_vehicle_waiting_behind_ego_ = std::any_of(
      object_proximity_infos.begin(), object_proximity_infos.end(),
      [&objects](const auto& proximity_info) {
        const auto object_iter = objects.find(proximity_info.first);
        if (object_iter == objects.end()) {
          return false;
        }
        if (!object_iter->second.is_vehicle()) {
          return false;
        }

        // The vehicle that is behind Ego within certain range is consider to
        // exclude some case that stationary vehicle does not wait behind ego.
        if (proximity_info.second.projected_ra_arc_length().end() > 0.0) {
          return false;
        }

        if (proximity_info.second.projected_ra_arc_length().end() <
            -kWaitingBehindEgoMaxDistanceInMeter) {
          return false;
        }

        if (!math::NearZero(proximity_info.second.abs_lateral_gap())) {
          return false;
        }
        return std::abs(proximity_info.second.signed_longitudinal_speed()) <
               constants::kDefaultEgoNearStaticSpeedInMps;
      });

  if (debug != nullptr) {
    debug->mutable_robot_state()->set_has_vehicle_waiting_behind_ego(
        has_vehicle_waiting_behind_ego_);
  }
}

void WorldContext::UpdateTheNearestTLChangedPolygonStart(
    const std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>&
        cz_proximity_info_map) {
  for (const ConstructionZone& cz : construction_zones_) {
    if (!common::IsMapChangeArea(
            cz, voy::MapChangeAreaAttribute::TRAFFIC_LIGHT_CHANGE)) {
      // Skip if it is not the traffic light changed construction zone.
      continue;
    }
    const auto cz_proximity_iter = cz_proximity_info_map.find(cz.id);
    DCHECK(cz_proximity_iter != cz_proximity_info_map.end())
        << "Cannot query cz from cz_proximity_info_map, id: " << cz.id;
    const speed::pb::ObjectProximityInfo& proximity_info =
        cz_proximity_iter->second;
    if (!math::NearZero(proximity_info.abs_lateral_gap())) {
      // Skip if it is not on ego path.
      continue;
    }

    const double start_ra_arclength =
        proximity_info.projected_ra_arc_length().start();
    constexpr double kMinDistToNotConsiderMapChangedPolygonBehindInMeter = 5.0;
    if (start_ra_arclength <
        -kMinDistToNotConsiderMapChangedPolygonBehindInMeter) {
      // Skip if ego has already passed the map changed polygon.
      continue;
    }

    if (!nearest_tl_changed_polygon_start_ra_arclength_.has_value() ||
        nearest_tl_changed_polygon_start_ra_arclength_.value() >
            start_ra_arclength) {
      nearest_tl_changed_polygon_start_ra_arclength_ = start_ra_arclength;
    }
  }
}

void WorldContext::UpdateHasEgoEncroachedLaneBoundary(
    const LaneSequenceIterator& lane_sequence_iterator,
    pb::WorldContextDebug* debug) {
  const std::vector<math::geometry::Point2d>& bounding_box =
      robot_state().current_state_snapshot().bounding_box().CornerPoints();

  const math::geometry::PolylineCurve2d left_lane_boundary =
      (*lane_sequence_iterator.current_lane())->left_marking()->line();
  const math::geometry::PolylineCurve2d right_lane_boundary =
      (*lane_sequence_iterator.current_lane())->right_marking()->line();

  for (const auto& point : bounding_box) {
    // Check if the point has exceed the left lane boundary
    if (left_lane_boundary.GetSide(point) == math::pb::kLeft) {
      has_ego_encroached_left_lane_boundary_ = true;
    }
    // Check if the point has exceed the right lane boundary
    if (right_lane_boundary.GetSide(point) == math::pb::kRight) {
      has_ego_encroached_right_lane_boundary_ = true;
    }
  }
  if (debug != nullptr) {
    debug->mutable_robot_state()->set_has_ego_encroached_left_lane_boundary(
        has_ego_encroached_left_lane_boundary_);
    debug->mutable_robot_state()->set_has_ego_encroached_right_lane_boundary(
        has_ego_encroached_right_lane_boundary_);
  }
}

void WorldContext::UpdateDominantRiskObjectId(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    speed::pb::WorldContextSeed& current_seed) {
  // Compute current dominant risk object id.
  previous_dominant_risk_object_id_ =
      GetDominantRiskObjectId(previous_iter_seed.speed_seed());
  const pb::WorldContextSeed& previous_world_context_seed =
      previous_iter_seed.speed_seed().world_context_seed();

  // Update the current iteration seed.
  current_seed.set_previous_dominant_risk_object_id(
      previous_dominant_risk_object_id());
  if (previous_dominant_risk_object_id() ==
      previous_world_context_seed.previous_dominant_risk_object_id()) {
    // If the dominant risk object persists.
    current_seed.set_previous_dominant_risk_object_duration(
        previous_world_context_seed.previous_dominant_risk_object_duration() +
        1);
  } else {
    current_seed.set_previous_dominant_risk_object_duration(1);
  }

  previous_dominant_risk_object_duration_ =
      current_seed.previous_dominant_risk_object_duration();
}

void WorldContext::UpdateEgoStateHistory(
    const planner::speed::pb::AgentReactionTrackerSeed&
        previous_iter_art_seed) {
  // The art seed should has sorted the current Ego states. We must ensure this
  // because the ego state history should include the current Ego states.
  DCHECK(previous_iter_art_seed.ego_state_at_timestamp_map().find(
             ego_pose_timestamp()) !=
         previous_iter_art_seed.ego_state_at_timestamp_map().end());
  ego_state_history_.SetEgoStatesFromARTSeed(previous_iter_art_seed);
}

// Currently SpeedWorldModel is extracted from WorldModel. And to avoid
// inconsistent seed content among path and speed node it does not include any
// seed info. Thus we assign |is_pull_out_triggered| with an update function.
// TODO(jinhao): Get the signal from SpeedWorldModel.
void WorldContext::UpdateIsPullOutTriggered(
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed) {
  is_pull_out_triggered_ = previous_iter_seed.speed_seed()
                               .speed_reasoner_seeds()
                               .pullout_reasoner_seed()
                               .is_triggered();
}

std::optional<double> WorldContext::GetAgentConditionalPredictionYieldIntention(
    const int64_t object_id) const {
  if (object_conditional_prediction_map_.find(object_id) ==
      object_conditional_prediction_map_.end()) {
    return std::nullopt;
  }
  if (object_conditional_prediction_map_.at(object_id)
          .yield_cbp_trajs_pair.target_trajectories.empty()) {
    return std::nullopt;
  }
  const PredictedTrajectoryWrapper& yield_cbp_traj =
      object_conditional_prediction_map_.at(object_id)
          .yield_cbp_trajs_pair.target_trajectories[0];
  return std::make_optional(
      yield_cbp_traj.yield_intention().yield_probability());
}

std::optional<PairedPredictedTrajectoryWrapper>
WorldContext::GetAgentConditionalPredictionYieldTrajectoryPair(

    const int64_t object_id) const {
  if (object_conditional_prediction_map_.find(object_id) ==
      object_conditional_prediction_map_.end()) {
    return std::nullopt;
  }
  if (object_conditional_prediction_map_.at(object_id)
          .yield_cbp_trajs_pair.target_trajectories.empty() ||
      object_conditional_prediction_map_.at(object_id)
          .yield_cbp_trajs_pair.ego_trajectories.empty()) {
    return std::nullopt;
  }
  return std::make_optional(
      object_conditional_prediction_map_.at(object_id).yield_cbp_trajs_pair);
}

}  // namespace speed
}  // namespace planner
