#include "math/math_util.h"
#include "planner/speed/solver/optimizer/lqr_potentials_generator.h"

#include <optional>

#include "planner/speed/constraint/constraint.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/solver/optimizer/lqr_potentials.h"
#include "planner/speed/solver/optimizer/potentials.h"
#include "planner/speed/solver/optimizer/speed_opt_input.h"
#include "planner/speed/solver/optimizer/test/speed_opt_test_fixture.h"

namespace planner {
namespace speed {
namespace {
// Verify that when normally enable the speed attractor,its parameters are set
// correctly.
TEST(PotentialsGeneratorTest, SpeedAttractor) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder.build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  const double scaling_factor = opt_input.profiles.search_profile.back().x /
                                opt_input.profiles.ref_profile.back().x;
  const double gain = DiscomfortVarying::EvaluateAtDiscomfort(
                          opt_input.opt_conf.conf.potentials_params()
                              .speed_attractor_params()
                              .max_gain(),
                          opt_input.opt_conf.conf.potentials_params()
                              .speed_attractor_params()
                              .min_gain(),
                          opt_input.global_discomfort) *
                      scaling_factor;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<SingleSidedAttractor>& speed_progress =
        lqr_potentials.state_potentials.at(state_ix).speed_progress;
    DCHECK(speed_progress.has_value());
    const auto [speed_progress_sign, speed_progress_x0, speed_progress_dx,
                speed_progress_normalization_factor_sqr, speed_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            speed_progress.value());
    EXPECT_EQ(speed_progress_gain, gain);
    EXPECT_EQ(speed_progress_x0, opt_input.profiles.ref_profile.at(state_ix).v);
    EXPECT_EQ(speed_progress_dx, opt_input.opt_conf.conf.potentials_params()
                                     .speed_attractor_params()
                                     .dx());
    EXPECT_EQ(
        speed_progress_normalization_factor_sqr,
        std::pow(1.0 / opt_input.profiles.ref_profile.at(state_ix).v, 2.0));
    EXPECT_FALSE(speed_progress_sign);
  }
}

// Verify that when normally enable the length attractor,its parameters are set
// correctly.
TEST(PotentialsGeneratorTest, LengthAttractor) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder.build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  const double scaling_factor = opt_input.profiles.search_profile.back().x /
                                opt_input.profiles.ref_profile.back().x;
  const double gain = DiscomfortVarying::EvaluateAtDiscomfort(
                          opt_input.opt_conf.conf.potentials_params()
                              .length_attractor_params()
                              .max_gain(),
                          opt_input.opt_conf.conf.potentials_params()
                              .length_attractor_params()
                              .min_gain(),
                          opt_input.global_discomfort) *
                      scaling_factor;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<SingleSidedAttractor>& length_progress =
        lqr_potentials.state_potentials.at(state_ix).length_progress;
    DCHECK(length_progress.has_value());
    const auto [length_progress_sign, length_progress_x0, length_progress_dx,
                length_progress_normalization_factor_sqr,
                length_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            length_progress.value());
    EXPECT_EQ(length_progress_gain, gain);
    EXPECT_EQ(length_progress_x0,
              opt_input.profiles.ref_profile.at(state_ix).x);
    EXPECT_EQ(length_progress_dx, opt_input.opt_conf.conf.potentials_params()
                                      .length_attractor_params()
                                      .dx());
    EXPECT_EQ(length_progress_normalization_factor_sqr,
              math::Sqr(1.0 / opt_input.profiles.ref_profile.at(state_ix).x));
    EXPECT_FALSE(length_progress_sign);
  }
}

// Verify if the length attractors' parameters are set correctly for low
// progress scenario.
TEST(PotentialsGeneratorTest, LengthAttractorForLowProgressScenario) {
  if (!FLAGS_planning_enable_x_upper_bound_attraction_in_speed_opt) {
    return;
  }
  OptInputBuilder input_builder;
  // Set a search profile with low progress and add a yield constraint.
  constexpr double yield_x = 4.0;
  constexpr double stop_range = 0.1;
  input_builder
      .update_search_profile_with_const_jerk(/*v_in=*/0.5, /*a_in=*/0.0,
                                             /*j_in=*/0.0, /*jerk=*/0.0)
      .add_const_speed_constraint(/*t_start=*/6.0, /*x_start=*/yield_x,
                                  /*v_const=*/0.0, /*num_states=*/20,
                                  /*decision=*/Decision::YIELD)
      .set_yield_extra_time(DiscomfortVarying(0.0))
      .set_yield_extra_distance(DiscomfortVarying(0.0))
      .set_yield_stop_range(DiscomfortVarying(stop_range))
      .set_soft_yield();
  SpeedOptInput opt_input = input_builder.build();
  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();
  const int num_steps = opt_input.opt_conf.steps;
  const double static_range_x =
      yield_x - stop_range - opt_input.opt_conf.ra_to_fb;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<SingleSidedAttractor>& length_progress =
        lqr_potentials.state_potentials.at(state_ix).length_progress;
    DCHECK(length_progress.has_value());
    const auto [length_progress_sign, length_progress_x0, length_progress_dx,
                length_progress_normalization_factor_sqr,
                length_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            length_progress.value());
    EXPECT_EQ(length_progress_gain, opt_input.opt_conf.conf.potentials_params()
                                        .position_attractor_params()
                                        .gain());
    EXPECT_EQ(length_progress_dx, opt_input.opt_conf.conf.potentials_params()
                                      .position_attractor_params()
                                      .dx());
    const double ref_position =
        std::min(opt_input.profiles.search_profile[state_ix].x, static_range_x);
    EXPECT_NEAR(length_progress_x0, ref_position, 1e-6) << state_ix;
    EXPECT_NEAR(length_progress_normalization_factor_sqr,
                math::Sqr(1.0 / std::max(ref_position, 1.0)), 1e-6);
    EXPECT_FALSE(length_progress_sign);
  }
}

// Verify that when set a last profile, the last attractor's parameters are set
// correctly.
TEST(PotentialsGeneratorTest, JerkAttractor) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  constexpr double kLastProfileJerkAttractorGainDiscount = 0.5;
  constexpr double kJerkAttractorPositiveGainFactor = 0.25;
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder
                                .update_last_profile_with_const_jerk(
                                    /*v_in=*/2.0, /*a_in=*/0.0,
                                    /*j_in=*/0.0, /*jerk=*/0.0)
                                .build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<AsymmetricalAttractor>& jerk_attractor =
        lqr_potentials.state_potentials.at(state_ix).jerk_attractor;
    DCHECK(jerk_attractor.has_value());
    const double ref_j = opt_input.profiles.ref_profile.at(state_ix).j;
    const double decay =
        std::pow(kLastProfileJerkAttractorGainDiscount, state_ix);
    // Test positive side of jerk attractor.
    const auto [jerk_attractor_pos_x0, jerk_attractor_pos_dx,
                jerk_attractor_pos_normalization_factor_sqr,
                jerk_attractor_pos_gain] =
        PrivateMemberAccessorForUT::GetAsymmetricalAttractorMembers(
            jerk_attractor.value(),
            /*positive=*/true);
    const auto [jerk_attractor_neg_x0, jerk_attractor_neg_dx,
                jerk_attractor_neg_normalization_factor_sqr,
                jerk_attractor_neg_gain] =
        PrivateMemberAccessorForUT::GetAsymmetricalAttractorMembers(
            jerk_attractor.value(),
            /*positive=*/false);
    EXPECT_EQ(jerk_attractor_pos_x0, ref_j);
    EXPECT_EQ(jerk_attractor_pos_dx, opt_input.opt_conf.conf.potentials_params()
                                         .last_profile_jerk_attractor_params()
                                         .dx());
    EXPECT_EQ(jerk_attractor_pos_gain,
              opt_input.opt_conf.conf.potentials_params()
                      .last_profile_jerk_attractor_params()
                      .gain() *
                  decay * kJerkAttractorPositiveGainFactor);
    EXPECT_EQ(jerk_attractor_pos_normalization_factor_sqr, 1.0);

    // Test negative side of jerk attractor.
    EXPECT_EQ(jerk_attractor_neg_x0, ref_j);
    EXPECT_EQ(jerk_attractor_neg_dx, opt_input.opt_conf.conf.potentials_params()
                                         .last_profile_jerk_attractor_params()
                                         .dx());
    EXPECT_EQ(jerk_attractor_neg_gain,
              opt_input.opt_conf.conf.potentials_params()
                      .last_profile_jerk_attractor_params()
                      .gain() *
                  decay);
    EXPECT_EQ(jerk_attractor_neg_normalization_factor_sqr, 1.0);
  }
}

// Verify that when last profile is nullopt then the jerk attractor is empty.
TEST(PotentialsGeneratorTest, JerkAttractorWithNullOpt) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::jerk_attractor);
}

// Verify that when disable_speed_attractor is set to true, speed_progress is
// empty.
TEST(PotentialsGeneratorTest, DisabledSpeedAttractor) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder.disable_speed_attract().build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::speed_progress);
}

// Verify the followings when stay stopped is set to true:
// 1. speed_progress is empty.
// 2. length_progress will be attracted to desired_stopping_x.
// 3. jerk_attractor will not be empty with a static last profile.
TEST(PotentialsGeneratorTest, SetStayStopped) {
  OptInputBuilder input_builder;
  const double desired_stopping_x = 2.0;
  SpeedOptInput opt_input =
      input_builder.enable_stay_stopped(desired_stopping_x)
          .update_last_profile_with_const_jerk(/*v_in=*/0.0, /*a_in=*/0.0,
                                               /*j_in=*/0.0, /*jerk=*/0.0)
          .build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const int num_steps = opt_input.opt_conf.steps;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<SingleSidedAttractor>& speed_progress =
        lqr_potentials.state_potentials.at(state_ix).speed_progress;
    EXPECT_FALSE(speed_progress.has_value());

    const std::optional<SingleSidedAttractor>& length_progress =
        lqr_potentials.state_potentials.at(state_ix).length_progress;
    DCHECK(length_progress.has_value());
    EXPECT_EQ(length_progress.value().x0(), desired_stopping_x);
    const auto [length_progress_sign, length_progress_x0, length_progress_dx,
                length_progress_normalization_factor_sqr,
                length_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            length_progress.value());
    EXPECT_EQ(length_progress_dx, opt_input.opt_conf.conf.potentials_params()
                                      .length_attractor_params()
                                      .dx());
    EXPECT_EQ(length_progress_gain, opt_input.opt_conf.conf.potentials_params()
                                        .length_attractor_params()
                                        .max_gain());
    EXPECT_EQ(length_progress_normalization_factor_sqr,
              std::pow(1.0 / desired_stopping_x, 2.0));
    EXPECT_TRUE(length_progress_sign);

    const std::optional<AsymmetricalAttractor>& jerk_attractor =
        lqr_potentials.state_potentials.at(state_ix).jerk_attractor;
    EXPECT_TRUE(jerk_attractor.has_value());
  }
}

// Verify that when fast acceleration is set to true, scaling_factor of speed
// attractor and length attractor is set to kMagnificationForFastAcceleration,
// and the jerk attractor is empty.
// TODO(yuwang): Remove this test after we clean up the old form of fast
// acceleration.
TEST(PotentialsGeneratorTest, EnableFastAccelerationOldForm) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = false;
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder.enable_fast_accel()
                                .update_last_profile_with_const_jerk(
                                    /*v_in=*/2.0, /*a_in=*/0.0,
                                    /*j_in=*/0.0, /*jerk=*/0.0)
                                .build();

  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();

  const double expect_speed_progress_gain =
      DiscomfortVarying::EvaluateAtDiscomfort(
          opt_input.opt_conf.conf.potentials_params()
              .speed_attractor_params()
              .max_gain(),
          opt_input.opt_conf.conf.potentials_params()
              .speed_attractor_params()
              .min_gain(),
          opt_input.global_discomfort);
  const double expect_length_progress_gain =
      DiscomfortVarying::EvaluateAtDiscomfort(
          opt_input.opt_conf.conf.potentials_params()
              .length_attractor_params()
              .max_gain(),
          opt_input.opt_conf.conf.potentials_params()
              .length_attractor_params()
              .min_gain(),
          opt_input.global_discomfort);
  constexpr double kAttractorDxForFastAcceleration = 3.0;

  const int num_steps = opt_input.opt_conf.steps;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<SingleSidedAttractor>& speed_progress =
        lqr_potentials.state_potentials.at(state_ix).speed_progress;
    DCHECK(speed_progress.has_value());
    const auto [speed_progress_sign, speed_progress_x0, speed_progress_dx,
                speed_progress_normalization_factor_sqr, speed_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            speed_progress.value());
    EXPECT_EQ(speed_progress_gain, expect_speed_progress_gain);

    const std::optional<SingleSidedAttractor>& length_progress =
        lqr_potentials.state_potentials.at(state_ix).length_progress;
    DCHECK(length_progress.has_value());
    const auto [length_progress_sign, length_progress_x0, length_progress_dx,
                length_progress_normalization_factor_sqr,
                length_progress_gain] =
        PrivateMemberAccessorForUT::GetSingleSideAttractorMembers(
            length_progress.value());
    EXPECT_EQ(length_progress_gain, expect_length_progress_gain);
    EXPECT_EQ(length_progress_dx, kAttractorDxForFastAcceleration);

    const std::optional<AsymmetricalAttractor>& jerk_attractor =
        lqr_potentials.state_potentials.at(state_ix).jerk_attractor;
    EXPECT_FALSE(jerk_attractor.has_value());
  }
}

// Verify that when set enable fast acceleration and use x_upper_bound
// attraction, the attractor's parameters are set correctly.
TEST(PotentialsGeneratorTest, EnableFastAcceleration) {
  GflagGuard gflag_guard;
  FLAGS_planning_enable_last_profile_attraction_in_speed_opt = true;
  FLAGS_planning_enable_x_upper_bound_attraction_in_fast_acceleration = true;

  constexpr double kAttractorDxForFastAccelerationWithXUpperBound = 1.0;
  constexpr double kMagnificationForFastAcceleration = 4.0;

  OptInputBuilder input_builder;
  // Set a search profile with low progress and add a yield constraint.
  constexpr double yield_x = 4.0;
  constexpr double stop_range = 0.1;
  input_builder
      .add_const_speed_constraint(/*t_start=*/6.0, /*x_start=*/yield_x,
                                  /*v_const=*/0.0, /*num_states=*/80,
                                  /*decision=*/Decision::YIELD)
      .set_yield_extra_time(DiscomfortVarying(0.0))
      .set_yield_extra_distance(DiscomfortVarying(0.0))
      .set_yield_stop_range(DiscomfortVarying(stop_range))
      .set_soft_yield();
  SpeedOptInput opt_input =
      input_builder
          .update_ref_profile_with_const_jerk(/*v_in=*/0.5, /*a_in=*/0.0,
                                              /*j_in=*/0.0, /*jerk=*/0.0)
          .enable_fast_accel()
          .build();
  LqrPotentialsGenerator potentials_generator(opt_input, nullptr);
  const LqrPotentials& lqr_potentials = potentials_generator.lqr_potentials();
  const int num_steps = opt_input.opt_conf.steps;
  const double static_range_x =
      yield_x - stop_range - opt_input.opt_conf.ra_to_fb;
  for (int state_ix = 1; state_ix < num_steps; state_ix++) {
    const std::optional<Attractor>& following_attractor =
        lqr_potentials.state_potentials.at(state_ix).following_attractor;
    DCHECK(following_attractor.has_value());
    const auto [following_attractor_x0, following_attractor_dx,
                following_attractor_normalization_factor_sqr,
                following_attractor_gain] =
        PrivateMemberAccessorForUT::GetAttractorMembers(
            following_attractor.value());
    EXPECT_EQ(following_attractor_gain,
              opt_input.opt_conf.conf.potentials_params()
                      .position_attractor_params()
                      .gain() *
                  kMagnificationForFastAcceleration);
    EXPECT_EQ(following_attractor_dx,
              kAttractorDxForFastAccelerationWithXUpperBound);
    const double attractor_position =
        std::min(opt_input.profiles.ref_profile[state_ix].x, static_range_x);
    EXPECT_NEAR(following_attractor_x0, attractor_position, 1e-6) << state_ix;
    EXPECT_NEAR(following_attractor_normalization_factor_sqr, 1.0, 1e-6);

    const std::optional<AsymmetricalAttractor>& jerk_attractor =
        lqr_potentials.state_potentials.at(state_ix).jerk_attractor;
    EXPECT_FALSE(jerk_attractor.has_value());
  }
}

// Verify that when reference profile has no progress, speed_progress and
// length_progress is empty.
TEST(PotentialsGeneratorTest, StaticRefProfile) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder
                                .update_ref_profile_with_const_jerk(
                                    /*v_in=*/0.0, /*a_in=*/0.0,
                                    /*j_in=*/0.0, /*jerk=*/0.0)
                                .build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::length_progress);
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::speed_progress);
}

// Verify that when last profile has no progress and stay stopped is false,
// jerk attractor is empty.
TEST(PotentialsGeneratorTest, StaticLastProfile) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input = input_builder
                                .update_last_profile_with_const_jerk(
                                    /*v_in=*/0.0, /*a_in=*/0.0,
                                    /*j_in=*/0.0, /*jerk=*/0.0)
                                .build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::jerk_attractor);
}

// Verify that when last profile is rough, jerk attractor is empty.
TEST(PotentialsGeneratorTest, RoughLastProfile) {
  OptInputBuilder input_builder;
  std::optional<Profile>& last_profile =
      input_builder
          .update_last_profile_with_const_jerk(/*v_in=*/2.0, /*a_in=*/0.0,
                                               /*j_in=*/0.0, /*jerk=*/0.0)
          .mutable_last_profile();
  DCHECK(last_profile.has_value());
  // By setting the jerk for the first two states, the last profile becomes
  // non-smooth. Although modifying the jerk will cause the states to not
  // conform to the kinematic model, it is acceptable for use in unit testing.
  last_profile->at(0).j = -10.0;
  last_profile->at(1).j = 10.0;
  SpeedOptInput opt_input = input_builder.build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::jerk_attractor);
}

// Verify that when discomfort is higher than 0.5, jerk attractor is empty.
TEST(PotentialsGeneratorTest, EmptyLastAttractorWithHighDiscomfort) {
  OptInputBuilder input_builder;
  SpeedOptInput opt_input =
      input_builder
          .update_last_profile_with_const_jerk(/*v_in=*/2.0, /*a_in=*/0.0,
                                               /*j_in=*/0.0, /*jerk=*/0.0)
          .set_discomfort(0.75)
          .build();
  VerifyPotentialIsEmpty(opt_input,
                         &LqrPotentials::StatePotentials::jerk_attractor);
}

}  // namespace
}  // namespace speed
}  // namespace planner
