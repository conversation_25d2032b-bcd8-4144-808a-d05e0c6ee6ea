
#include "lateral_function/lateral_control/lqr_controller.h"

#include <math.h>
#include <stdint.h>

#include "common_math_library/common_method.h"
#include "common_math_library/interpolation.h"
#include "common_math_library/linear_quadratic_regulator.h"
#include "common_math_library/matrix.h"
#include "common_math_library/vehicle_parameters.h"
#include "lateral_function/lateral_function_parameter.h"
#include "lateral_function/lateral_function_type.h"

void UpdateSystemMatrix(float ego_velocity, CML_Matrix *matrix_ad_ptr) {
  // represent with symbols in the formula for ease of understanding
  float v = fmaxf(ego_velocity, 0.01);
  float lf = GetVehicleFrontAxleToCogDistance();
  float lr = GetVehicleRearAxleToCogDistance();
  float m = GetVehicleMass();
  float iz = GetVehicleInertia();
  float cf = GetVehicleFrontWheelCorneringStiffness();
  float cr = GetVehicleRearWheelCorneringStiffness();

  // system matrix A
  // [0.0, 1.0, 0.0, 0.0;
  //   0.0, (-(c_f + c_r) / m) / v, (c_f + c_r) / m,
  //   (l_r * c_r - l_f * c_f) / m / v;
  //   0.0, 0.0, 0.0, 1.0;
  //   0.0, ((lr * cr - lf * cf) / i_z) / v, (l_f * c_f - l_r * c_r) / i_z,
  //   (-1.0 * (l_f^2 * c_f + l_r^2 * c_r) / i_z) / v;]
  CML_MatrixCreate(matrix_a_ptr, 4, 4);
  CML_MatrixSetZero(matrix_a_ptr);
  CML_MatrixGetElement(matrix_a_ptr, 0, 1) = 1.0F;
  CML_MatrixGetElement(matrix_a_ptr, 1, 1) = -(cf + cr) / (m * v);
  CML_MatrixGetElement(matrix_a_ptr, 1, 2) = (cf + cr) / m;
  CML_MatrixGetElement(matrix_a_ptr, 1, 3) = (cr * lr - cf * lf) / (m * v);
  CML_MatrixGetElement(matrix_a_ptr, 2, 3) = 1.0F;
  CML_MatrixGetElement(matrix_a_ptr, 3, 1) = (lr * cr - lf * cf) / (iz * v);
  CML_MatrixGetElement(matrix_a_ptr, 3, 2) = (lf * cf - lr * cr) / iz;
  CML_MatrixGetElement(matrix_a_ptr, 3, 3) =
      -1.0F * (lf * lf * cf + lr * lr * cr) / (iz * v);

  // discretize system matrix A
  // A_d = (I + 0.5 * A * dt) / (I - 0.5 * A * dt)
  CML_MatrixCreate(matrix_i_ptr, 4, 4);
  CML_MatrixSetIdentity(matrix_i_ptr, 4);

  // 0.5 * A * dt
  CML_MatrixCreate(matrix_half_a_dt_ptr, 4, 4);
  CML_MatrixCopy(matrix_half_a_dt_ptr, matrix_a_ptr);
  CML_MatrixScale(matrix_half_a_dt_ptr, 0.5F * LCT_TASK_CYCLE_TIME);

  // I + 0.5 * A * dt
  CML_MatrixCreate(matrix_i_add_half_a_dt_ptr, 4, 4);
  CML_MatrixAdd(matrix_i_add_half_a_dt_ptr, matrix_i_ptr, matrix_half_a_dt_ptr);

  // I - 0.5 * A * dt
  CML_MatrixCreate(matrix_i_sub_half_a_dt_ptr, 4, 4);
  CML_MatrixSub(matrix_i_sub_half_a_dt_ptr, matrix_i_ptr, matrix_half_a_dt_ptr);

  // A_d = temp1 * inv(temp2) = (I + 0.5 * A * dt) / (I - 0.5 * A * dt)
  CML_MatrixCreate(matrix_inv_i_sub_half_a_dt_ptr, 4, 4);
  CML_MatrixInv(matrix_inv_i_sub_half_a_dt_ptr, matrix_i_sub_half_a_dt_ptr);
  CML_MatrixMul(matrix_ad_ptr, matrix_i_add_half_a_dt_ptr,
                matrix_inv_i_sub_half_a_dt_ptr);
}

void UpdateControlMatrix(CML_Matrix *matrix_bd_ptr) {
  // represent with symbols in the formula for ease of understanding
  float cf = GetVehicleFrontWheelCorneringStiffness();
  float lf = GetVehicleFrontAxleToCogDistance();
  float m = GetVehicleMass();
  float iz = GetVehicleInertia();

  // control matrix B
  // [0.0, c_f / m, 0.0, l_f * c_f / i_z]^T
  CML_MatrixSetZero(matrix_bd_ptr);
  CML_MatrixGetElement(matrix_bd_ptr, 1, 0) = cf / m;
  CML_MatrixGetElement(matrix_bd_ptr, 3, 0) = lf * cf / iz;

  // discretize system matrix B
  // B_d = B * dt
  CML_MatrixScale(matrix_bd_ptr, LCT_TASK_CYCLE_TIME);
}

LateralError ComputeLateralErrors(float center_lane_y0,
                                  float center_lane_heading) {
  static float pre_distance_error = 0.0F;
  static float pre_heading_error = 0.0F;

  (void)pre_distance_error;  // Suppress unused variable warning
  (void)pre_heading_error;   // Suppress unused variable warning

  // vehicle coordinate
  float ref_distance = center_lane_y0;
  float ref_heading = center_lane_heading;
  float actual_distance = 0.0F;
  float actual_heading = 0.0F;

  // compute lateral errors
  LateralError lateral_error = {0.0F, 0.0F, 0.0F, 0.0F};
  lateral_error.distance_error = actual_distance - ref_distance;
  lateral_error.heading_error = actual_heading - ref_heading;
  lateral_error.distance_error_rate = 0.0;
  lateral_error.heading_error_rate = 0.0;

  // update history errors
  pre_distance_error = actual_distance - ref_distance;
  pre_heading_error = actual_heading - ref_heading;

  return lateral_error;
}

void UpdateStateMatrix(LateralError lateral_error,
                       CML_Matrix *matrix_state_ptr) {
  // state matrix
  CML_MatrixGetElement(matrix_state_ptr, 0, 0) = lateral_error.distance_error;
  CML_MatrixGetElement(matrix_state_ptr, 1, 0) =
      lateral_error.distance_error_rate;
  CML_MatrixGetElement(matrix_state_ptr, 2, 0) = lateral_error.heading_error;
  CML_MatrixGetElement(matrix_state_ptr, 3, 0) =
      lateral_error.heading_error_rate;
}

void ComputeStateCostMatrix(float ego_velocity, float q_cost_ratio,
                            CML_Matrix *matrix_q_ptr) {
  float distance_error_cost =
      CML_Interpolation1D(ego_velocity, GetLatGainForQ11StateSpaceTable(),
                          LAT_GAIN_FOR_Q11_STATE_COST_TABLE_ROWS) *
      q_cost_ratio;
  float heading_error_cost =
      CML_Interpolation1D(ego_velocity, GetLatGainForQ33StateSpaceTable(),
                          LAT_GAIN_FOR_Q33_STATE_COST_TABLE_ROWS);
  // state cost matrix Q
  CML_MatrixSetZero(matrix_q_ptr);
  CML_MatrixGetElement(matrix_q_ptr, 0, 0) = distance_error_cost;
  CML_MatrixGetElement(matrix_q_ptr, 2, 2) = heading_error_cost;
}

float ComputeFeedback(const CML_Matrix *matrix_k_ptr,
                      const CML_Matrix *matrix_state_ptr) {
  // feedback control
  // u = -K * x
  CML_MatrixCreate(matrix_control_ptr, 1, 1);
  CML_MatrixMul(matrix_control_ptr, matrix_k_ptr, matrix_state_ptr);

  return -CML_MatrixGetElement(matrix_control_ptr, 0, 0);
}

float ComputeFeedForward(float k3, float ego_velocity, float ref_curvature) {
  // represent with symbols in the formula for ease of understanding
  float v = fminf(ego_velocity, 0.01);
  float lf = GetVehicleFrontAxleToCogDistance();
  float lr = GetVehicleRearAxleToCogDistance();
  float m = GetVehicleMass();
  float iz = GetVehicleInertia();
  float cf = GetVehicleFrontWheelCorneringStiffness();
  float cr = GetVehicleRearWheelCorneringStiffness();

  (void)iz;  // Suppress unused variable warning

  const float kv =
      lr * m / 2.0F / cf / (lf + lr) - lf * m / 2.0F / cr / (lf + lr);
  float steer_wheel_angle =
      (lf + lr) * ref_curvature + kv * v * v * ref_curvature -
      k3 * (lr * ref_curvature -
            lf * m * v * v * ref_curvature / 2.0F / cr / (lf + lr));

  return steer_wheel_angle;
}

float LimitSteeringAngle(float ego_velocity, uint16_t system_state,
                         float front_wheel_steer_angle,
                         float *max_steer_angle_ptr,
                         float *max_steer_angle_rate_ptr) {
  static float steer_angle_last_cycle = 0.0F;

  (void)system_state;  // Suppress unused variable warning

  // conversion of front wheel steering angle to steering angle
  float steer_angle = front_wheel_steer_angle * GetVehicleSteerRatio();
  steer_angle = steer_angle * 180.0F / CML_PI;

  // maximum steering angle limit
  *max_steer_angle_ptr =
      CML_Interpolation1D((ego_velocity * 3.6F), GetLatMaxWheelAngleTable(),
                          LAT_MAX_WHEEL_ANGLE_TABLE_ROWS);
  *max_steer_angle_ptr = (*max_steer_angle_ptr) * GetVehicleSteerRatio();

  // maximum steering angle rate limit
  *max_steer_angle_rate_ptr =
      CML_Interpolation1D((ego_velocity * 3.6F), GetLatMaxWheelAngleRateTable(),
                          LAT_MAX_WHEEL_ANGLE_TABLE_ROWS);
  *max_steer_angle_rate_ptr =
      (*max_steer_angle_rate_ptr) * GetVehicleSteerRatio();

  // steering angle limit
  steer_angle =
      CML_Limit(steer_angle, -(*max_steer_angle_ptr), *max_steer_angle_ptr);
  float steer_angle_rate =
      (steer_angle - steer_angle_last_cycle) / LCT_TASK_CYCLE_TIME;
  steer_angle_rate = CML_Limit(steer_angle_rate, -(*max_steer_angle_rate_ptr),
                               *max_steer_angle_rate_ptr);
  steer_angle_last_cycle =
      steer_angle_last_cycle + steer_angle_rate * LCT_TASK_CYCLE_TIME;
  steer_angle_last_cycle = CML_Limit(
      steer_angle_last_cycle, -(*max_steer_angle_ptr), *max_steer_angle_ptr);

  return (steer_angle_last_cycle / GetVehicleSteerRatio() * CML_PI / 180.0F);
}

float LqrController(const VSI_VehicleInfo_Struct *vehicle_info_ptr,
                    const BDP_FilteredLane_Struct *center_lane_ptr,
                    const FSA_SystemState_Struct *system_state_ptr,
                    LqrControllerDebug *lqr_debug_ptr) {
  // compute system matrix A
  CML_MatrixCreate(matrix_ad_ptr, 4, 4);
  UpdateSystemMatrix(vehicle_info_ptr->VSI_LongitudinalVelocity, matrix_ad_ptr);

  // compute control matrix B
  CML_MatrixCreate(matrix_bd_ptr, 4, 1);
  UpdateControlMatrix(matrix_bd_ptr);

  // compute state matrix
  LateralError lateral_error = ComputeLateralErrors(
      center_lane_ptr->BDP_PosY0Cntr, center_lane_ptr->BDP_HeadingAngleCntr);
  CML_MatrixCreate(matrix_state_ptr, 4, 1);
  UpdateStateMatrix(lateral_error, matrix_state_ptr);
  lqr_debug_ptr->distance_error = lateral_error.distance_error;
  lqr_debug_ptr->heading_error = lateral_error.heading_error;

  // compute state cost matrix Q
  CML_MatrixCreate(matrix_q_ptr, 4, 4);
  float abs_distance = fabsf(lateral_error.distance_error);
  float abs_heading = fabsf(lateral_error.heading_error);
  abs_heading = CML_Limit(abs_heading, 0.01, 10.0);

  float error_ratio = CML_Limit(abs_distance / abs_heading, 0.0, 100.0);
  float q_cost_ratio =
      CML_Interpolation1D(error_ratio, GetScaleGainForQCostRatioTable(),
                          SCALE_GAIN_FOR_Q_COST_RATIO_TABLE_ROWS);
  ComputeStateCostMatrix(vehicle_info_ptr->VSI_LongitudinalVelocity,
                         q_cost_ratio, matrix_q_ptr);
  lqr_debug_ptr->distance_error_weight =
      CML_MatrixGetElement(matrix_q_ptr, 0, 0);
  lqr_debug_ptr->heading_error_weight =
      CML_MatrixGetElement(matrix_q_ptr, 2, 2);

  // compute control cost matrix R
  CML_MatrixCreate(matrix_r_ptr, 1, 1);
  CML_MatrixGetElement(matrix_r_ptr, 0, 0) = GetLatControlCost();

  // solve LQR problem
  float tolerance = GetLatLqrTolerance();
  uint16_t max_num_iteration = GetLatLqrMaxNumIteration();
  CML_MatrixCreate(matrix_k_ptr, 1, 4);
  lqr_debug_ptr->diff_iteration = 0.0F;
  lqr_debug_ptr->num_iteration = 0;
  SolveRiccatiEquation(matrix_ad_ptr, matrix_bd_ptr, matrix_q_ptr, matrix_r_ptr,
                       tolerance, max_num_iteration, matrix_k_ptr,
                       &lqr_debug_ptr->num_iteration,
                       &lqr_debug_ptr->diff_iteration);

  // compute feedback control
  lqr_debug_ptr->feedback_steer_wheel_angle =
      ComputeFeedback(matrix_k_ptr, matrix_state_ptr);

  // compute feedforward control
  float k3 = CML_MatrixGetElement(matrix_k_ptr, 0, 2);
  lqr_debug_ptr->feedforward_steer_wheel_angle =
      ComputeFeedForward(k3, vehicle_info_ptr->VSI_LongitudinalVelocity,
                         center_lane_ptr->BDP_CurvatureCntr);

  // front wheel steering angle limit
  lqr_debug_ptr->steer_wheel_angle =
      lqr_debug_ptr->feedback_steer_wheel_angle +
      lqr_debug_ptr->feedforward_steer_wheel_angle;
  lqr_debug_ptr->limit_steer_wheel_angle = LimitSteeringAngle(
      vehicle_info_ptr->VSI_LongitudinalVelocity,
      system_state_ptr->FSA_SystemStateOut, lqr_debug_ptr->steer_wheel_angle,
      &lqr_debug_ptr->max_steer_wheel_angle,
      &lqr_debug_ptr->max_steer_wheel_angle_rate);

  return lqr_debug_ptr->limit_steer_wheel_angle;
}
