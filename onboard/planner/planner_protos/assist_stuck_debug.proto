syntax = "proto3";

package planner.pb;

import "planner_protos/assist_intervention_requirement.proto";
import "planner_protos/ego_stuck_feature.proto";
import "planner_protos/lane_change.proto";
import "planner_protos/remote_assist.proto";
import "voy_protos/construction_zones.proto";

// The debug for testing better progress for a given lane.
// Next Available ID: 4.
message HasBetterProgressDebug {
  // Has better progress.
  bool has_better_progress = 1;
  // The given lane.
  int64 lane_id = 2;
  // The lane sequence from the given lane to the following from global route.
  repeated int64 connective_lane_seq = 3;
}

// The debug for FP rule about queuing of the assist stuck scene detector.
// Next Available ID: 7.
message AssistStuckFPQueuingDebug {
  // The reason types for the queuing FP rule. Only when kQueuing means
  // the rule is triggered and blocking assist request.
  // Next Available ID: 9.
  enum DecisionReason {
    // Default value, should not be presented.
    kUnKnown = 0;
    // Triggered FP by reasoned queuing scene.
    kQueuing = 1;
    // Not trigger FP by no yielding object before the ego.
    kNoYieldingObj = 2;
    // Not trigger FP by a parked car before the ego.
    kYieldingParkedCar = 3;
    // Not trigger FP by no current lane information.
    kNoCurrentLane = 4;
    // Not trigger FP by the ego's planning path not queuing.
    kCurPathNotQueuing = 5;
    // Not Trigger FP by found a better lane seq which is connective to global
    // route and not queuing.
    kBetterProgress = 6;
    // Not trigger FP by temp parked cars new road boundary before the ego.
    kYieldingCarsNearBound = 7;
    // Triggered FP by reasoned stationary leading vehicle scene, which would be
    // thought of as a queuing scene in forcing recall.
    kWaitingInLineForLeadingVehicle = 8;
  }

  // The decision reason about the return of the rule.
  DecisionReason decision = 1;
  // The id of yielding object.
  int64 yielding_obj_id = 2;
  // The id of lane where the ego is located.
  int64 current_lane_id = 3;
  // The following lanes from global route after current lane.
  repeated int64 following_lanes = 4;
  // The adjacent lane progress debug.
  repeated HasBetterProgressDebug adjacent_lane_progress_debug = 5;
  // The message of progress feature.
  EgoStuckFeatureAboutProgress progress_feature = 6;
}

// The debug message for FN rule about map change area.
// Next Available ID: 6.
message AssistStuckFNMapChangeAreaDebug {
  // The id of the map change area construction zone.
  uint64 id = 1;
  // The map change attribute of the blockage construction zone. If multi
  // attributes defined, the priori one will be set.
  voy.MapChangeAreaAttribute map_change_attribute = 2;
  // Will be stuck by map change area from routing.
  bool will_be_stuck_by_map_change_area = 3;
  // Has map change area in ego's routing.
  bool has_map_change_area_in_routing = 4;
  // Has dominant constraint about traffic light map change area.
  bool has_dominant_constraint_about_traffic_light_map_change = 5;
}

// The message for FN rule about stuck by perception FP.
// Next Available ID: 2.
message AssistStuckFNPerceptionFPDebug {
  // The current dominant obstacle's meta.
  ObstacleMeta current_dominant_obstacle_meta = 1;
}

// The message about assist stuck detector.
// Next Available ID: 2.
message AssistStuck {
  // The decision made by checking all rules of assist stuck detector.
  // Next Available ID: 4.
  enum RuleDecision {
    // None rule have been triggered, then the assist stuck model will eval the
    // scene.
    kNone = 0;
    // The detect process should be abort, because the ego is not in stuck by
    // rule.
    kAbort = 1;
    // The detect process may trigger a request. but will wait for core planner
    // to unstuck, because the ego is in stuck by rule.
    kStuckDetected = 2;
    // The detect process should trigger a request immediately, for some scenes
    // need ops to make decision as soon as possible, i.e. map change area.
    kRequestImmediately = 3;
  }

  // The rule decision.
  RuleDecision rule_decision = 1;
}

// The message about activation states rules in assist stuck detector.
// Next Available ID: 25.
message AssistStuckRuleActivation {
  reserved 17;
  reserved "fn_temp_parked";
  bool fp_traffic_light = 1;
  bool fp_yielding = 2;
  bool fp_pullover = 3;
  bool fp_crosswalk = 4;
  bool fp_stop_fence = 5;
  bool fp_startup = 6;
  bool fp_max_speed = 7;
  bool fp_occlusion = 8;
  bool fp_eol = 9;
  bool fp_on_turn = 10;
  bool fp_queuing = 11;
  bool fp_jam = 12;
  bool fp_remote_speed_limit = 20;
  bool fn_selection = 13;
  bool fn_near_hb = 14;
  bool fn_map_change_area = 15;
  bool fn_breakdown_car = 16;
  bool fn_perception_fp = 18;
  bool fn_eol = 19;
  bool fn_construction_zone = 21;
  bool fn_no_block = 22;
  bool fn_lane_change_stuck = 23;
  bool fn_vehicle_hazard_signal = 24;
}

// The message about assist FN selection strategy.
// Next Available ID: 8.
message AssistStuckFNSelectionDebug {
  // The message about FN selection scenarios.
  // Next Available ID: 5.
  enum FNSelectionScenario {
    kDefault = 0;
    kRightMostLaneStuck = 1;
    kJunctionStuck = 2;
    kLaneChangeStuck = 3;
    kBetterProgress = 4;
  }
  FNSelectionScenario fn_scenario = 1;
  bool current_lane_available = 2;
  bool left_lane_available = 3;
  bool right_lane_available = 4;
  LaneChangeState lane_change_state = 5;
  double distance_to_junction = 6;
  bool has_other_candidate = 7;
}

// The debug message for forcing recall.
// Next Available ID: 3.
message AssistStuckForcingRecallDebug {
  // The road structure where the ego locates.
  RoadStructureFeature road_structure = 1;
  // The states.
  AssistStuckForcingRecall state = 2;
}

// The debug message for tidal flow lane scene reasoner.
// Next Available ID: 3
message TidalFlowLaneSceneReasonerDebug {
  bool is_ego_stuck_in_tidal_flow_lane = 1;
  repeated TidalFlowLaneInfo tidal_flow_lane_infos = 2;
}

// The debug message for FP rule about traffic light.
// Next Available ID: 3.
message AssistStuckFPTrafficLightDebug {
  // The reason types for the traffic light FP rule. Only when decision is
  // kCloseToTrafficLight or kWaitingRedLight means the rule is
  // triggered and blocking assist request.
  // Next Available ID: 5.
  enum DecisionReason {
    // Default value, should not be presented.
    kUnKnown = 0;
    // Not trigger FP by map change area about traffic light.
    kTrafficLightMapChangeArea = 1;
    // Trigger FP by ego close to traffic light.
    kNearTrafficLight = 2;
    // Trigger FP by waiting red light cyles less than threshold.
    kWaitingRedLight = 3;
    // Not trigger FP by waiting cycles reach threshold.
    kStuckByTrafficLight = 4;
  }
  // Represents the cycle count since latest traffic light turned from red.
  int64 tl_turned_cycles = 1;
  // The decision reason about the return of the rule.
  DecisionReason decision = 2;
}

// The message about assist stuck detector's rules.
// Next Available ID: 11.
message AssistStuckRules {
  reserved 6;
  reserved "fn_temp_parked_debug";
  // The decision made by checking all rules of assist stuck detector.
  // Next Available ID: 5.
  enum RuleDecision {
    // None rule have been triggered, then the assist stuck model will eval the
    // scene.
    kNone = 0;
    // The detect process should be abort, because the ego is not in stuck by
    // rule.
    kAbort = 1;
    // The detect process may trigger a request. but will wait for core planner
    // to unstuck, because the ego is in stuck by rule.
    kStuckDetected = 2;
    // The detect process should trigger a request immediately, for some scenes
    // need ops to make decision as soon as possible, i.e. map change area.
    kRequestImmediately = 3;
    // There is one or more FN rule being activate, but not trigger RA request,
    // just waiting continue fn cycle cnt to accumulate.
    kHasActivateFN = 4;
  }
  // The rule decision.
  RuleDecision rule_decision = 1;
  // Activation status about rules.
  AssistStuckRuleActivation rule_activation = 2;
  // The debug for FP rule about queuing.
  AssistStuckFPQueuingDebug fp_queuing_debug = 3;
  // The debug for FP rule about traffic light.
  AssistStuckFPTrafficLightDebug fp_traffic_light_debug = 10;
  // The debug for FN rule about map change area.
  AssistStuckFNMapChangeAreaDebug fn_map_change_area_debug = 4;
  // The debug for FN rule about perception FP.
  AssistStuckFNPerceptionFPDebug fn_perception_fp_debug = 5;
  // The debug for FN rule about selection.
  AssistStuckFNSelectionDebug fn_selection_debug = 7;
  // The debug for forcing recall.
  AssistStuckForcingRecallDebug forcing_recall_debug = 8;
  // The debug for tidal flow lane scene reasoner.
  TidalFlowLaneSceneReasonerDebug tidal_flow_lane_scene_reasoner_debug = 9;
}
