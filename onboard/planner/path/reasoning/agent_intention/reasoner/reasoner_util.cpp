#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"

#include <algorithm>
#include <cstdint>
#include <string>
#include <vector>

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "log_utils/map_macros.h"
#include "math/constants.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/st_planner/rollout/st_rollout_utility.h"
#include "planner/path/path_solver/constraint_manager/road_boundaries.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/speed/profile/profile_util.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace path {
namespace {
// The speed threshold to think agent is slow moving.
constexpr double kSlowMovingSpeedThresholdInMps = 2.0;
// The distance threshold to think Ego is close to pullover destination.
constexpr double kCloseDistanceThresholdToDestinationInM = 30.0;
// The increment of object critical lateral gap when we are actively nudging to
// the object.
constexpr double kDefensiveCriticalLatGapIncrementInM = 0.2;
// The increment of object comfort lateral gap when we are actively nudging to
// the object.
constexpr double kDefensiveComfortLatGapIncrementInM = 0.2;
// The angle in radius to think Ego is doing inclined while doing active lateral
// movement. (about 5.7 dgree)
constexpr double kActiveLatMoveAngleThresholdInRad = 0.1;
// The step size of critical req_lat_gap decreases gradually to avoid overnudge.
constexpr double kCirticalRequiredLateralGapDecreaseStepSizeInMeter = 0.1;
// The step size of comfort req_lat_gap decreases gradually to avoid overnudge.
constexpr double kRequiredLateralGapDecreaseStepSizeInMeter = 0.2;
// When Ego abs lateral speed is larger than this, we assume Ego is active
// lateral moving.
constexpr double kEgoLateralSpeedThresholdForActiveLateralMovingInMps = 0.2;

// Return true if the snapshot has comfortable lateral drivable space for the
// given needed_req_lat_gap_in_meter.
inline bool HasEnoughLateralSpacesWithGivenReqLatGap(
    double side_inlane_clearance, double min_required_ego_width_m,
    double needed_req_lat_gap_in_meter) {
  return needed_req_lat_gap_in_meter + min_required_ego_width_m <=
         side_inlane_clearance;
}

// Return true if all nudge snapshots have comfortable lateral drivable space
// for the given needed_req_lat_gap_in_meter.
bool EgoCanPassWithGivenReqLatGap(
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double min_required_ego_width_m, double needed_req_lat_gap_in_meter,
    pb::SnapshotIntention::PassState recommend_lateral_decision) {
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      const double side_inlane_clearance =
          recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
              ? predicted_states[idx]
                    .inlane_state.inlane_param.left_inlane_clearance_m
              : predicted_states[idx]
                    .inlane_state.inlane_param.right_inlane_clearance_m;
      if (!HasEnoughLateralSpacesWithGivenReqLatGap(
              side_inlane_clearance, min_required_ego_width_m,
              needed_req_lat_gap_in_meter)) {
        return false;
      }
    }
  }
  return true;
}

bool EgoCanPassWithGivenReqLatGap(
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    double min_required_ego_width_m, double needed_req_lat_gap_in_meter,
    pb::SnapshotIntention::PassState recommend_lateral_decision) {
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    for (int idx = nudge_index_range.start_pos;
         idx <= nudge_index_range.end_pos; ++idx) {
      const double side_inlane_clearance =
          recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
              ? predicted_states[idx]
                    .object_occupancy_param()
                    .left_boundary_clearance_m
              : predicted_states[idx]
                    .object_occupancy_param()
                    .right_boundary_clearance_m;
      if (!HasEnoughLateralSpacesWithGivenReqLatGap(
              side_inlane_clearance, min_required_ego_width_m,
              needed_req_lat_gap_in_meter)) {
        return false;
      }
    }
  }
  return true;
}

// Return the effective_nudge_index_ranges for the given
// needed_req_lat_gap_in_meter.
std::vector<math::Range<int>> GetFilteredRecommendNudgeIndexRanges(
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    const double min_required_ego_width_m,
    const double needed_req_lat_gap_in_meter,
    const pb::SnapshotIntention::PassState recommend_lateral_decision,
    const bool is_ego_cut_behind) {
  DCHECK(recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT ||
         recommend_lateral_decision == pb::SnapshotIntention::PASS_RIGHT);
  std::vector<math::Range<int>> nudge_index_ranges;
  nudge_index_ranges.reserve(effective_nudge_index_ranges.size());
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    if (is_ego_cut_behind) {
      // Backward search for the start index where ego has enough in-lane space
      // to nudge pass for cut-behind scenario.
      for (int idx = nudge_index_range.end_pos;
           idx >= nudge_index_range.start_pos; --idx) {
        const auto& inlane_param =
            predicted_states[idx].inlane_state.inlane_param;
        const double side_inlane_clearance =
            recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
                ? inlane_param.left_inlane_clearance_m
                : inlane_param.right_inlane_clearance_m;
        const bool is_out_of_lane =
            math::IsApprox(inlane_param.min_lane_width, side_inlane_clearance);
        bool is_snapshot_and_ego_nudge_both_out_of_lane = false;
        // When the snapshot is fully outside of lane, the
        // side_inlane_clearance is equal to min_lane_width. No need to consider
        // the snapshot out of lane if the EGO nudge direction is also out of
        // lane.
        if (is_out_of_lane) {
          if ((recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT &&
               inlane_param.center_line_side == math::pb::kLeft) ||
              (recommend_lateral_decision ==
                   pb::SnapshotIntention::PASS_RIGHT &&
               inlane_param.center_line_side == math::pb::kRight)) {
            is_snapshot_and_ego_nudge_both_out_of_lane = true;
          }
        }
        if (HasEnoughLateralSpacesWithGivenReqLatGap(
                side_inlane_clearance, min_required_ego_width_m,
                needed_req_lat_gap_in_meter) &&
            !is_snapshot_and_ego_nudge_both_out_of_lane) {
          continue;
        }
        const math::Range<int> index_range(idx + 1, nudge_index_range.end_pos);
        if (math::IsValidRange(index_range)) {
          nudge_index_ranges.push_back(index_range);
        }
        return nudge_index_ranges;
      }
    } else {
      // Forward search for the end index where ego has enough in-lane space
      // to nudge pass.
      for (int idx = nudge_index_range.start_pos;
           idx <= nudge_index_range.end_pos; ++idx) {
        const auto& inlane_param =
            predicted_states[idx].inlane_state.inlane_param;
        const double side_inlane_clearance =
            recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
                ? inlane_param.left_inlane_clearance_m
                : inlane_param.right_inlane_clearance_m;
        const bool is_out_of_lane =
            math::IsApprox(inlane_param.min_lane_width, side_inlane_clearance);

        // When the snapshot is fully outside of lane, the
        // side_inlane_clearance is equal to min_lane_width. No need to consider
        // the snapshot out of lane if the EGO nudge direction is also out of
        // lane.
        const bool is_snapshot_and_ego_nudge_both_out_of_lane =
            is_out_of_lane &&
            ((recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT &&
              inlane_param.center_line_side == math::pb::kLeft) ||
             (recommend_lateral_decision == pb::SnapshotIntention::PASS_RIGHT &&
              inlane_param.center_line_side == math::pb::kRight));

        if (HasEnoughLateralSpacesWithGivenReqLatGap(
                side_inlane_clearance, min_required_ego_width_m,
                needed_req_lat_gap_in_meter) &&
            !is_snapshot_and_ego_nudge_both_out_of_lane) {
          continue;
        }
        const math::Range<int> index_range(nudge_index_range.start_pos,
                                           idx - 1);
        if (math::IsValidRange(index_range)) {
          nudge_index_ranges.push_back(index_range);
        }
        return nudge_index_ranges;
      }
    }

    // Keep nudge ranges that has enough lateral space with given lateral gap.
    nudge_index_ranges.push_back(nudge_index_range);
  }
  return nudge_index_ranges;
}

std::vector<math::Range<int>> GetFilteredRecommendNudgeIndexRanges(
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const std::vector<math::Range<int>>& effective_nudge_index_ranges,
    const double min_required_ego_width_m,
    const double needed_req_lat_gap_in_meter,
    const pb::SnapshotIntention::PassState recommend_lateral_decision,
    const bool is_ego_cut_behind) {
  DCHECK(recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT ||
         recommend_lateral_decision == pb::SnapshotIntention::PASS_RIGHT);
  std::vector<math::Range<int>> nudge_index_ranges;
  nudge_index_ranges.reserve(effective_nudge_index_ranges.size());
  for (const auto& nudge_index_range : effective_nudge_index_ranges) {
    if (is_ego_cut_behind) {
      // Backward search for the start index where ego has enough in-lane space
      // to nudge pass for cut-behind scenario.
      for (int idx = nudge_index_range.end_pos;
           idx >= nudge_index_range.start_pos; --idx) {
        const auto& occupancy_param =
            predicted_states[idx].object_occupancy_param();
        const double side_inlane_clearance =
            recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
                ? occupancy_param.left_boundary_clearance_m
                : occupancy_param.right_boundary_clearance_m;
        const bool is_out_of_lane = math::IsApprox(
            occupancy_param.min_corridor_width_m, side_inlane_clearance);
        bool is_snapshot_and_ego_nudge_both_out_of_lane = false;
        // When the snapshot is fully outside of lane, the
        // side_inlane_clearance is equal to min_corridor_width_m. No need to
        // consider the snapshot out of lane if the EGO nudge direction is also
        // out of lane.
        if (is_out_of_lane) {
          if ((recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT &&
               occupancy_param.center_line_side == math::pb::kLeft) ||
              (recommend_lateral_decision ==
                   pb::SnapshotIntention::PASS_RIGHT &&
               occupancy_param.center_line_side == math::pb::kRight)) {
            is_snapshot_and_ego_nudge_both_out_of_lane = true;
          }
        }
        if (HasEnoughLateralSpacesWithGivenReqLatGap(
                side_inlane_clearance, min_required_ego_width_m,
                needed_req_lat_gap_in_meter) &&
            !is_snapshot_and_ego_nudge_both_out_of_lane) {
          continue;
        }
        const math::Range<int> index_range(idx + 1, nudge_index_range.end_pos);
        if (math::IsValidRange(index_range)) {
          nudge_index_ranges.push_back(index_range);
        }
        return nudge_index_ranges;
      }
    } else {
      // Forward search for the end index where ego has enough in-lane space
      // to nudge pass.
      for (int idx = nudge_index_range.start_pos;
           idx <= nudge_index_range.end_pos; ++idx) {
        const auto& occupancy_param =
            predicted_states[idx].object_occupancy_param();
        const double side_inlane_clearance =
            recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT
                ? occupancy_param.left_boundary_clearance_m
                : occupancy_param.right_boundary_clearance_m;
        const bool is_out_of_lane = math::IsApprox(
            occupancy_param.min_corridor_width_m, side_inlane_clearance);
        bool is_snapshot_and_ego_nudge_both_out_of_lane = false;
        // When the snapshot is fully outside of lane, the
        // side_inlane_clearance is equal to min_corridor_width_m. No need to
        // consider the snapshot out of lane if the EGO nudge direction is also
        // out of lane.
        if (is_out_of_lane) {
          if ((recommend_lateral_decision == pb::SnapshotIntention::PASS_LEFT &&
               occupancy_param.center_line_side == math::pb::kLeft) ||
              (recommend_lateral_decision ==
                   pb::SnapshotIntention::PASS_RIGHT &&
               occupancy_param.center_line_side == math::pb::kRight)) {
            is_snapshot_and_ego_nudge_both_out_of_lane = true;
          }
        }
        if (HasEnoughLateralSpacesWithGivenReqLatGap(
                side_inlane_clearance, min_required_ego_width_m,
                needed_req_lat_gap_in_meter) &&
            !is_snapshot_and_ego_nudge_both_out_of_lane) {
          continue;
        }
        const math::Range<int> index_range(nudge_index_range.start_pos,
                                           idx - 1);
        if (math::IsValidRange(index_range)) {
          nudge_index_ranges.push_back(index_range);
        }
        return nudge_index_ranges;
      }
    }

    // Keep nudge ranges that has enough lateral space with given lateral gap.
    nudge_index_ranges.push_back(nudge_index_range);
  }
  return nudge_index_ranges;
}

// Returns the recommend_required_lat_gap for the given
// needed_req_lat_gap_in_meter.
RequiredLateralGap GetRecommendedRequiredLateralGap(
    const RequiredLateralGap& required_lat_gap,
    const double needed_req_lat_gap_in_meter,
    bool should_prioritize_nudge_snapshots = false) {
  // modify the critical req_lat_gap.
  if (should_prioritize_nudge_snapshots) {
    DCHECK_LE(needed_req_lat_gap_in_meter,
              required_lat_gap.comfort_required_lateral_gap);
    if (needed_req_lat_gap_in_meter < math::constants::kEpsilon) {
      return RequiredLateralGap(0.0, 0.2);
    }
    return RequiredLateralGap(std::max(0.0, needed_req_lat_gap_in_meter),
                              required_lat_gap.comfort_required_lateral_gap);
  }
  // modify the comfort req_lat_gap.
  // (yongbing): add an RT event to track how frequently would this be called.
  if (needed_req_lat_gap_in_meter < math::constants::kEpsilon) {
    return RequiredLateralGap(0.0, 0.2);
  }
  if (needed_req_lat_gap_in_meter <
      required_lat_gap.critical_required_lateral_gap) {
    return RequiredLateralGap(0.0, needed_req_lat_gap_in_meter);
  }
  return RequiredLateralGap(required_lat_gap.critical_required_lateral_gap,
                            needed_req_lat_gap_in_meter);
}

}  // namespace

pb::LateralBlockingState::BlockingState GetEffectiveLateralBlockingState(
    const pb::LateralBlockingInfo& blocking_info) {
  // Returns pb::LateralBlockingState::NON_BLOCKING if the snapshot is out of
  // intention range.
  if (blocking_info.is_beyond_intention_range()) {
    return pb::LateralBlockingState::NON_BLOCKING;
  }
  return blocking_info.blocking_state();
}

std::vector<pb::BlockingSequence> GetEffectiveBlockingSequences(
    const std::vector<pb::LateralBlockingInfo>& trajectory_blocking_infos) {
  std::vector<pb::BlockingSequence> ret;
  if (trajectory_blocking_infos.empty()) {
    return ret;
  }
  size_t last_blocking_idx = 0;
  pb::LateralBlockingState::BlockingState last_blocking_state =
      GetEffectiveLateralBlockingState(trajectory_blocking_infos.front());
  for (size_t idx = 1; idx < trajectory_blocking_infos.size(); ++idx) {
    const pb::LateralBlockingState::BlockingState cur_blocking_state =
        GetEffectiveLateralBlockingState(trajectory_blocking_infos[idx]);
    if (cur_blocking_state == last_blocking_state) {
      continue;
    }
    if (last_blocking_idx < idx - 1 &&
        last_blocking_state != pb::LateralBlockingState::NON_BLOCKING) {
      ret.push_back(
          GetBlockingSequence(last_blocking_idx, idx - 1, last_blocking_state));
    }
    last_blocking_state = cur_blocking_state;
    last_blocking_idx = idx;
  }
  if (last_blocking_idx < trajectory_blocking_infos.size() - 1 &&
      last_blocking_state != pb::LateralBlockingState::NON_BLOCKING) {
    ret.push_back(GetBlockingSequence(
        last_blocking_idx,
        static_cast<int>(trajectory_blocking_infos.size() - 1),
        last_blocking_state));
  }
  return ret;
}

pb::BlockingSequenceType GetBlockingSequencesType(
    const std::vector<pb::BlockingSequence>& blocking_sequences) {
  const bool has_soft_blocking_sequence =
      std::any_of(blocking_sequences.begin(), blocking_sequences.end(),
                  [](const auto& sequence) {
                    return sequence.blocking_state() ==
                           pb::LateralBlockingState::SOFT_BLOCKING;
                  });
  const bool has_hard_blocking_sequence =
      std::any_of(blocking_sequences.begin(), blocking_sequences.end(),
                  [](const auto& sequence) {
                    return sequence.blocking_state() ==
                           pb::LateralBlockingState::HARD_BLOCKING;
                  });
  if (has_soft_blocking_sequence && !has_hard_blocking_sequence) {
    return pb::BlockingSequenceType::kPartialBlocking;
  } else if (!has_soft_blocking_sequence && has_hard_blocking_sequence) {
    return pb::BlockingSequenceType::kCompleteBlocking;
  } else if (has_soft_blocking_sequence && has_hard_blocking_sequence) {
    return pb::BlockingSequenceType::kMixedBlocking;
  }
  return pb::BlockingSequenceType::kNonBlocking;
}

std::vector<math::Range<int>> GetEffectiveNudgeIndexRanges(
    const std::vector<math::Range<int>>& nudge_index_ranges,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::LateralBlockingState::BlockingState blocking_state) {
  if (std::any_of(
          nudge_index_ranges.begin(), nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    return std::vector<math::Range<int>>{};
  }

  std::vector<math::Range<int>> expected_blocking_state_ranges;
  for (const auto& sequence : blocking_sequences) {
    if (sequence.blocking_state() == blocking_state) {
      math::Range<int> expected_index_range(sequence.range().start_index(),
                                            sequence.range().end_index());
      DCHECK(math::IsValidRange(expected_index_range));
      expected_blocking_state_ranges.push_back(std::move(expected_index_range));
    }
  }
  return math::IntersectRanges(nudge_index_ranges,
                               expected_blocking_state_ranges);
}

int GetStartIndexForState(
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::LateralBlockingState::BlockingState blocking_state) {
  // begin->end to find the smallest start_pos.
  const auto start_iter =
      std::find_if(blocking_sequences.begin(), blocking_sequences.end(),
                   [blocking_state](const auto& sequence) {
                     return sequence.blocking_state() == blocking_state;
                   });
  DCHECK(start_iter != blocking_sequences.end());
  return start_iter->range().start_index();
}

int GetEndIndexForState(
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::LateralBlockingState::BlockingState blocking_state) {
  // end->begin to find the biggest end_pos.
  const auto end_iter =
      std::find_if(blocking_sequences.rbegin(), blocking_sequences.rend(),
                   [blocking_state](const auto& sequence) {
                     return sequence.blocking_state() == blocking_state;
                   });
  DCHECK(end_iter != blocking_sequences.rend());

  return end_iter->range().end_index();
}

pb::BlockingSequence GetStartEndBlockingSequence(
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::LateralBlockingState::BlockingState blocking_state) {
  const int start_index =
      GetStartIndexForState(blocking_sequences, blocking_state);
  const int end_index = GetEndIndexForState(blocking_sequences, blocking_state);
  DCHECK_LT(start_index, end_index);
  return GetBlockingSequence(start_index, end_index, blocking_state);
}

std::vector<pb::BlockingSequence> GetMixedBlockingSequences(
    const std::vector<pb::BlockingSequence>& blocking_sequences) {
  const pb::BlockingSequence soft_sequence = GetStartEndBlockingSequence(
      blocking_sequences,
      /*blocking_state=*/pb::LateralBlockingState::SOFT_BLOCKING);
  const pb::BlockingSequence hard_sequence = GetStartEndBlockingSequence(
      blocking_sequences,
      /*blocking_state=*/pb::LateralBlockingState::HARD_BLOCKING);

  // Hard blocking needs to be removed from soft blocking.
  const std::vector<math::Range<int>> free_soft_blocking_ranges =
      math::SubtractRanges(std::vector<math::Range<int>>{math::Range<int>(
                               soft_sequence.range().start_index(),
                               soft_sequence.range().end_index())},
                           std::vector<math::Range<int>>{math::Range<int>(
                               hard_sequence.range().start_index(),
                               hard_sequence.range().end_index())});
  std::vector<pb::BlockingSequence> merged_blocking_sequences;
  merged_blocking_sequences.reserve(free_soft_blocking_ranges.size() + 1);
  // Add hard blocking.
  merged_blocking_sequences.push_back(hard_sequence);
  // Add the rest of the soft blocking.
  for (const auto& soft_range : free_soft_blocking_ranges) {
    if (math::IsInsideRange(hard_sequence.range().start_index() - 1,
                            soft_range.start_pos, soft_range.end_pos)) {
      merged_blocking_sequences.push_back(GetBlockingSequence(
          soft_range.start_pos, hard_sequence.range().start_index() - 1,
          pb::LateralBlockingState::SOFT_BLOCKING));
    } else if (math::IsInsideRange(hard_sequence.range().end_index() + 1,
                                   soft_range.start_pos, soft_range.end_pos)) {
      merged_blocking_sequences.push_back(GetBlockingSequence(
          hard_sequence.range().end_index() + 1, soft_range.end_pos,
          pb::LateralBlockingState::SOFT_BLOCKING));
    } else {
      merged_blocking_sequences.push_back(
          GetBlockingSequence(soft_range.start_pos, soft_range.end_pos,
                              pb::LateralBlockingState::SOFT_BLOCKING));
    }
  }
  // Sort merged_blocking_sequences with start_index.
  std::sort(merged_blocking_sequences.begin(), merged_blocking_sequences.end(),
            [](const auto& left, const auto& right) {
              return left.range().start_index() < right.range().start_index();
            });
  return merged_blocking_sequences;
}

bool IsHighRiskAgent(const pb::TrajectoryReasoningInfo& reasoning_info) {
  return (reasoning_info.was_nudge_intention() &&
          !reasoning_info.is_agent_in_front_of_front_bumper()) ||
         reasoning_info.is_agent_within_comfort();
}

bool ShouldStayCautiousAgent(
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  return reasoning_info.was_nudge_intention() || reasoning_info.is_beside_ego();
}

bool ShouldTreatAgentAsStationary(
    const pb::LateralBlockingInfo& traked_blocking_info,
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  // TODO(yongbing): was_nudge_intention only be applied to close agent (i.e.
  // arc_length is not far away from ego).
  return reasoning_info.is_agent_within_comfort() ||
         ((reasoning_info.was_nudge_intention() ||
           GetEffectiveLateralBlockingState(traked_blocking_info) ==
               pb::LateralBlockingState::SOFT_BLOCKING) &&
          reasoning_info.is_beside_ego());
}

bool IsSafeNecessaryCrossLaneNudge(
    const std::vector<math::Range<int>>& nudge_index_ranges,
    const pb::SnapshotIntention::PassState tracked_pass_state,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info) {
  // Only for valid collision range in st-planner.
  if (nudge_index_ranges.empty() ||
      std::any_of(
          nudge_index_ranges.begin(), nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    return false;
  }

  // Only considers agents at the current position that can be nudged.
  if (tracked_pass_state == pb::SnapshotIntention::IGNORE) {
    return false;
  }
  // Check the safe pass side with safe_adjacent_lanes in traffic_rules.
  if (traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes() ==
          pb::SnapshotIntention::PASS_BOTH ||
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes() ==
          tracked_pass_state) {
    return true;
  }

  return false;
}

bool ShouldTriggerLaneEncroachNudgeBase(
    const EgoInLaneParams& ego_param,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const pb::TrajectoryReasoningInfo& trajectory_reasoning_info,
    const pb::SnapshotIntention::PassState agent_lateral_decision,
    const pb::SnapshotIntention::PassState agent_pass_side,
    const pb::BehaviorType behavior_type) {
  // Avoid triggering lane encroachment nudge above 60km/h as the lateral
  // tracking error data is limited.
  if ((FLAGS_planning_enable_lane_encroach_nudge_at_high_speed &&
       ego_param.speed_mps > 16.67) ||
      (!FLAGS_planning_enable_lane_encroach_nudge_at_high_speed &&
       ego_param.speed_mps > 12.0)) {
    return false;
  }

  // Only support pb::LANE_KEEP.
  if (behavior_type != pb::LANE_KEEP) {
    return false;
  }

  // Skip hard_blocking state.
  if (agent_lateral_decision == pb::SnapshotIntention::IGNORE &&
      agent_pass_side == pb::SnapshotIntention::IGNORE) {
    return false;
  }

  // Only consider side agent.
  const bool is_es_target_object =
      trajectory_reasoning_info.contain_emergency_brake_state();
  if (!is_es_target_object) {
    return false;
  }

  // Check the pass side with safe_adjacent_lanes in traffic_rules.
  if (traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes() ==
      pb::SnapshotIntention::IGNORE) {
    return false;
  }
  if (traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes() !=
          pb::SnapshotIntention::PASS_BOTH &&
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes() !=
          agent_pass_side) {
    return false;
  }

  return true;
}

math::Range1d GetEgoLateralDistanceRange(const EgoInLaneParams& ego_param) {
  const double ego_half_width = 0.5 * ego_param.width_m;
  const double ego_start_lateral_distance =
      ego_param.relative_lateral_distance_to_line_center - ego_half_width;
  const double ego_end_lateral_distance =
      ego_param.relative_lateral_distance_to_line_center + ego_half_width;
  return math::Range1d(ego_start_lateral_distance, ego_end_lateral_distance);
}

math::pb::Side GetAgentLateralTrackedSideToEgo(
    const EgoInLaneParams& ego_param,
    const math::Range1d& agent_lateral_distance_range,
    const double extended_lateral_buffer) {
  const math::Range1d ego_lateral_range = GetEgoLateralDistanceRange(ego_param);
  const bool is_completely_left_beside_ego =
      agent_lateral_distance_range.start_pos - extended_lateral_buffer >
      ego_lateral_range.end_pos;
  const bool is_completely_right_beside_ego =
      agent_lateral_distance_range.end_pos + extended_lateral_buffer <
      ego_lateral_range.start_pos;
  if (is_completely_left_beside_ego) {
    DCHECK(!is_completely_right_beside_ego);
    return math::pb::kLeft;
  } else if (is_completely_right_beside_ego) {
    DCHECK(!is_completely_left_beside_ego);
    return math::pb::kRight;
  }
  return math::pb::kOn;
}

// Calculates lateral_acceleration by combining the following four formulas:
// 1) lateral_acceleration = v^2 / radius,
// 2) tan(front_wheel_ange) = wheel_base / radius,
// 3) front_wheel_ange = pi/2 - need_turn_angle,
// 4) tan(need_turn_angle) = longitudinal_distance / lateral_overlap_distance;
double ComputeLateralAcceleration(const double speed_mps,
                                  const double wheel_base,
                                  const double lateral_overlap_distance,
                                  const double longitudinal_distance) {
  DCHECK_GE(lateral_overlap_distance, 0.0);
  DCHECK_GE(longitudinal_distance, 0.0);
  double tan_a = longitudinal_distance /
                 (lateral_overlap_distance + math::constants::kEpsilon);

  return std::pow(speed_mps + math::constants::kEpsilon, 2) / wheel_base /
         (tan_a + math::constants::kEpsilon);
}

void ComputeOverlapDistanceForLateralAndLongitudinal(
    const EgoInLaneParams& ego_param,
    const AgentSnapshotInLaneState& agent_inlane_state,
    const RequiredLateralGap& required_lat_gap,
    double& lateral_overlap_distance, double& longitudinal_distance) {
  // Computes lateral overlap distance based on math::range.
  const std::vector<math::Range1d> intersect_range = math::IntersectRanges(
      std::vector<math::Range1d>({GetEgoLateralDistanceRange(ego_param)}),
      std::vector<math::Range1d>({math::Range1d(
          agent_inlane_state.inlane_param.full_body_start_lateral_distance_m -
              required_lat_gap.critical_required_lateral_gap,
          agent_inlane_state.inlane_param.full_body_end_lateral_distance_m +
              required_lat_gap.critical_required_lateral_gap)}));
  DCHECK_EQ(intersect_range.size(), 1);
  lateral_overlap_distance =
      intersect_range.front().end_pos - intersect_range.front().start_pos;
  // Computes longitudinal distance between the agent snapshot and ego.
  longitudinal_distance =
      agent_inlane_state.inlane_param.full_body_start_arclength_m -
      required_lat_gap.critical_required_lateral_gap -
      ego_param.front_bumper_arclength;
}

void ComputeOverlapDistanceForLateralAndLongitudinal(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyParam& agent_occupancy_param,
    const RequiredLateralGap& required_lat_gap,
    double& lateral_overlap_distance, double& longitudinal_distance) {
  // Computes lateral overlap distance based on math::range.
  const std::vector<math::Range1d> intersect_range = math::IntersectRanges(
      std::vector<math::Range1d>({GetEgoLateralDistanceRange(ego_param)}),
      std::vector<math::Range1d>({math::Range1d(
          agent_occupancy_param.full_body_start_lateral_distance_m -
              required_lat_gap.critical_required_lateral_gap,
          agent_occupancy_param.full_body_end_lateral_distance_m +
              required_lat_gap.critical_required_lateral_gap)}));
  DCHECK_EQ(intersect_range.size(), 1);
  lateral_overlap_distance =
      intersect_range.front().end_pos - intersect_range.front().start_pos;
  // Computes longitudinal distance between the agent snapshot and ego.
  longitudinal_distance = agent_occupancy_param.full_body_start_arclength_m -
                          required_lat_gap.critical_required_lateral_gap -
                          ego_param.front_bumper_arclength;
}

void AdjustNudgeRangeBasedOnKinematics(
    const EgoInLaneParams& ego_param,
    const AgentSnapshotInLaneParam& agent_tracked_param,
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states,
    const path::NudgeMotionChecker& nudge_motion_checker,
    const bool is_oncoming_agent, const voy::perception::ObjectType object_type,
    IntentionResultMetaData& intention_result) {
  // Do not adjust nudge range for the oncoming agent that are the side and
  // behind ego, because we need to maintain a safe distance from the agent to
  // the side and behind. And for the not oncoming agent, we need to check all
  // snapshots if meet the ego vehicle kinematic constraint.
  if (agent_tracked_param.full_body_start_arclength_m -
              intention_result.required_lat_gap.critical_required_lateral_gap <
          ego_param.front_bumper_arclength &&
      is_oncoming_agent) {
    return;
  }
  // Ensure nudge index ranges are not empty.
  DCHECK(!intention_result.nudge_index_ranges.empty());
  // Currently path reasoning only generate 1 nudge index range.
  DCHECK_EQ(intention_result.nudge_index_ranges.size(), 1);
  const math::Range<int> st_collision_range =
      intention_result.nudge_index_ranges.front();
  DCHECK(IsValidRange(st_collision_range));

  // Trims snapshots which lead to uncomfortable lateral movement.
  // To ensure the nudge range is valid, the minimum nudge range is start_pos to
  // start_pos + 1.
  int expected_end_index = st_collision_range.start_pos + 1;
  for (int end_index = st_collision_range.start_pos + 2;
       end_index <= st_collision_range.end_pos; ++end_index) {
    // Gets desired position of the ego rear axle center in frenet based on
    // lateral decision.
    const auto& inlane_param =
        predicted_states[end_index].inlane_state.inlane_param;
    const math::pb::Side snapshot_side_to_ego = GetAgentLateralTrackedSideToEgo(
        ego_param,
        math::Range1d(inlane_param.full_body_start_lateral_distance_m,
                      inlane_param.full_body_end_lateral_distance_m),
        /*extended_buffer=*/
        intention_result.required_lat_gap.critical_required_lateral_gap);
    // Skips if snapshots are not overlaped with ego vehicle laterally.
    if ((intention_result.lateral_decision ==
             pb::SnapshotIntention::PASS_LEFT &&
         snapshot_side_to_ego == math::pb::kRight) ||
        (intention_result.lateral_decision ==
             pb::SnapshotIntention::PASS_RIGHT &&
         snapshot_side_to_ego == math::pb::kLeft)) {
      expected_end_index = end_index;
      continue;
    }

    /**************************************************************
     * The diagram illustrates the method to find the desired ego vehicle pose
    from the current position of the ego vehicle and the snapshot that needs to
    be nudged.
                ______
        ^      |      |
         \     |      | <——— the snapshot overlap with ego laterally
          \ ___|______|
           |   |
           |\  | <———— desired ego position
           |_*_|
              \ <———— desired ego heading
               \ ___
                |   |
                |\  |<———— ego current position
                |_*_|
    ***************************************************************/
    const double desired_s = inlane_param.full_body_start_arclength_m -
                             ego_param.rear_axle_to_front_bumper_m;
    const double desired_l =
        intention_result.lateral_decision == pb::SnapshotIntention::PASS_LEFT
            ? inlane_param.full_body_end_lateral_distance_m +
                  intention_result.required_lat_gap
                      .critical_required_lateral_gap +
                  0.5 * ego_param.width_m
            : inlane_param.full_body_start_lateral_distance_m -
                  intention_result.required_lat_gap
                      .critical_required_lateral_gap -
                  0.5 * ego_param.width_m;
    const double speed_at_desired_s = st_planner::GetMaxSpeedByDist(
        *intention_result.best_st_rollout, desired_s - ego_param.arclength_m);
    const double desired_speed_mps =
        std::max(speed_at_desired_s, ego_param.speed_mps);
    const double desired_lateral_acc =
        nudge_motion_checker
            .CalculateDesiredLateralAccelerationWithinKinematicLimit(
                desired_s, desired_l, desired_speed_mps);
    // Checks lateral acceleration threshold.
    const double max_lateral_acc_threshold =
        object_type == voy::perception::ObjectType::PED
            ? kMaxComfortableLateralAccelerationForPedInMpss
            : kMaxComfortableLateralAccelerationForVehAndCycInMpss;
    if (desired_lateral_acc > max_lateral_acc_threshold) {
      break;
    }
    expected_end_index = end_index;
  }

  // Updates comfortable nudge range.
  intention_result.nudge_index_ranges.front().end_pos = expected_end_index;
}

void AdjustNudgeRangeBasedOnKinematics(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyParam& object_occupancy_param,
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states,
    const path::NudgeMotionChecker& nudge_motion_checker,
    const bool is_oncoming_agent, const voy::perception::ObjectType object_type,
    IntentionResultMetaData& intention_result) {
  // Do not adjust nudge range for the oncoming agent that are the side and
  // behind ego, because we need to maintain a safe distance from the agent to
  // the side and behind. And for the not oncoming agent, we need to check all
  // snapshots if meet the ego vehicle kinematic constraint.
  if (object_occupancy_param.full_body_start_arclength_m -
              intention_result.required_lat_gap.critical_required_lateral_gap <
          ego_param.front_bumper_arclength &&
      is_oncoming_agent) {
    return;
  }
  // Ensure nudge index ranges are not empty.
  DCHECK(!intention_result.nudge_index_ranges.empty());
  // Currently path reasoning only generate 1 nudge index range.
  DCHECK_EQ(intention_result.nudge_index_ranges.size(), 1);
  const math::Range<int> st_collision_range =
      intention_result.nudge_index_ranges.front();
  DCHECK(IsValidRange(st_collision_range));

  // Trims snapshots which lead to uncomfortable lateral movement.
  // To ensure the nudge range is valid, the minimum nudge range is start_pos to
  // start_pos + 1.
  int expected_end_index = st_collision_range.start_pos + 1;
  for (int end_index = st_collision_range.start_pos + 2;
       end_index <= st_collision_range.end_pos; ++end_index) {
    // Gets desired position of the ego rear axle center in frenet based on
    // lateral decision.
    const auto& occupancy_param =
        predicted_states[end_index].object_occupancy_param();
    const math::pb::Side snapshot_side_to_ego = GetAgentLateralTrackedSideToEgo(
        ego_param,
        math::Range1d(occupancy_param.full_body_start_lateral_distance_m,
                      occupancy_param.full_body_end_lateral_distance_m),
        /*extended_buffer=*/
        intention_result.required_lat_gap.critical_required_lateral_gap);
    // Skips if snapshots are not overlaped with ego vehicle laterally.
    if ((intention_result.lateral_decision ==
             pb::SnapshotIntention::PASS_LEFT &&
         snapshot_side_to_ego == math::pb::kRight) ||
        (intention_result.lateral_decision ==
             pb::SnapshotIntention::PASS_RIGHT &&
         snapshot_side_to_ego == math::pb::kLeft)) {
      expected_end_index = end_index;
      continue;
    }
    /**************************************************************
     * The diagram illustrates the method to find the desired ego vehicle pose
    from the current position of the ego vehicle and the snapshot that needs to
    be nudged.
                ______
        ^      |      |
         \     |      | <——— the snapshot overlap with ego laterally
          \ ___|______|
           |   |
           |\  | <———— desired ego position
           |_*_|
              \ <———— desired ego heading
               \ ___
                |   |
                |\  |<———— ego current position
                |_*_|
    ***************************************************************/
    const double desired_s = occupancy_param.full_body_start_arclength_m -
                             ego_param.rear_axle_to_front_bumper_m;
    const double desired_l =
        intention_result.lateral_decision == pb::SnapshotIntention::PASS_LEFT
            ? occupancy_param.full_body_end_lateral_distance_m +
                  intention_result.required_lat_gap
                      .critical_required_lateral_gap +
                  0.5 * ego_param.width_m
            : occupancy_param.full_body_start_lateral_distance_m -
                  intention_result.required_lat_gap
                      .critical_required_lateral_gap -
                  0.5 * ego_param.width_m;
    const double speed_at_desired_s = st_planner::GetMaxSpeedByDist(
        *intention_result.best_st_rollout, desired_s - ego_param.arclength_m);
    const double desired_speed_mps =
        std::max(speed_at_desired_s, ego_param.speed_mps);
    const double desired_lateral_acc =
        nudge_motion_checker
            .CalculateDesiredLateralAccelerationWithinKinematicLimit(
                desired_s, desired_l, desired_speed_mps);
    // Checks lateral acceleration threshold.
    const double max_lateral_acc_threshold =
        object_type == voy::perception::ObjectType::PED
            ? kMaxComfortableLateralAccelerationForPedInMpss
            : kMaxComfortableLateralAccelerationForVehAndCycInMpss;
    if (desired_lateral_acc > max_lateral_acc_threshold) {
      break;
    }
    expected_end_index = end_index;
  }

  // Updates comfortable nudge range.
  intention_result.nudge_index_ranges.front().end_pos = expected_end_index;
}

bool GetRecommendedAgentIntentionResult(
    const AgentInLaneStates& agent_inlane_state,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    double min_required_ego_width_m, bool should_prioritize_nudge_snapshots,
    bool is_ego_cut_behind, IntentionResultMetaData& intention_result) {
  // Only for valid collision range in st-planner.
  if (intention_result.nudge_index_ranges.empty() ||
      std::any_of(
          intention_result.nudge_index_ranges.begin(),
          intention_result.nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    return false;
  }

  // Ignore cases where we can't decide a nudge direction.
  if (tracked_pass_decision == pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE) {
    return false;
  }

  // Gets the recommend_lateral_decision with st planner and tracked pass side.
  // Not enough drivable space for nudge decision in st planner.
  const bool is_missing_nudge_decision =
      (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
  const pb::SnapshotIntention::PassState recommend_lateral_decision =
      is_missing_nudge_decision ? tracked_pass_decision
                                : intention_result.lateral_decision;
  DCHECK(recommend_lateral_decision != pb::SnapshotIntention::IGNORE);

  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }
  double needed_req_lat_gap_in_meter =
      should_prioritize_nudge_snapshots
          ? intention_result.required_lat_gap.critical_required_lateral_gap
          : intention_result.required_lat_gap.comfort_required_lateral_gap;
  while (needed_req_lat_gap_in_meter > -math::constants::kEpsilon) {
    if (should_prioritize_nudge_snapshots) {
      // If should_prioritize_nudge_snapshots = true, we will try to decrease
      // the req_lat_gap to give more snapshot.
      if (EgoCanPassWithGivenReqLatGap(
              predicted_states, intention_result.nudge_index_ranges,
              min_required_ego_width_m, needed_req_lat_gap_in_meter,
              recommend_lateral_decision)) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            is_missing_nudge_decision ? false
                                      : should_prioritize_nudge_snapshots);
        intention_result.lateral_decision = recommend_lateral_decision;
        absl::StrAppend(
            &intention_result.debug_str,
            "can nudge all snapshots with reduced critical buffer; ");
        return true;
      }
    } else {
      // If should_prioritize_nudge_snapshots = false, we will try to decrease
      // the nudge_index_ranges to keep a comfort lateral distance.
      const std::vector<math::Range<int>> effective_nudge_index_ranges =
          GetFilteredRecommendNudgeIndexRanges(
              predicted_states, intention_result.nudge_index_ranges,
              min_required_ego_width_m, needed_req_lat_gap_in_meter,
              recommend_lateral_decision, is_ego_cut_behind);
      if (!effective_nudge_index_ranges.empty()) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            is_missing_nudge_decision ? false
                                      : should_prioritize_nudge_snapshots);
        intention_result.nudge_index_ranges = effective_nudge_index_ranges;
        intention_result.lateral_decision = recommend_lateral_decision;
        absl::StrAppend(
            &intention_result.debug_str,
            "can nudge all snapshots with reduced comfort buffer; ");
        return true;
      }
    }
    needed_req_lat_gap_in_meter -=
        (should_prioritize_nudge_snapshots
             ? kCirticalRequiredLateralGapDecreaseStepSizeInMeter
             : kRequiredLateralGapDecreaseStepSizeInMeter);
  }

  needed_req_lat_gap_in_meter = std::max(
      /*critical_req_lat_gap=*/(should_prioritize_nudge_snapshots ? 0.0 : 0.2),
      needed_req_lat_gap_in_meter);
  const std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetFilteredRecommendNudgeIndexRanges(
          predicted_states, intention_result.nudge_index_ranges,
          min_required_ego_width_m, needed_req_lat_gap_in_meter,
          recommend_lateral_decision, is_ego_cut_behind);
  if (!effective_nudge_index_ranges.empty()) {
    intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
        intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
        is_missing_nudge_decision ? false : should_prioritize_nudge_snapshots);
    intention_result.nudge_index_ranges = effective_nudge_index_ranges;
    intention_result.lateral_decision = recommend_lateral_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge reduced snapshots to avoid leave current lane; ");
    return true;
  } else if (recommend_lateral_decision != pb::SnapshotIntention::IGNORE) {
    // Treat as a static agent for empty effective_nudge_index_ranges.
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = recommend_lateral_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge range empty treat as static; ");
    return true;
  }

  absl::StrAppend(&intention_result.debug_str,
                  "both effective_range and pass_decision are invalid; ");
  return false;
}

bool GetRecommendedAgentIntentionResult(
    const ObjectOccupancyState& object_occupancy_state,
    const pb::SnapshotIntention::PassState& tracked_pass_decision,
    double min_required_ego_width_m, bool should_prioritize_nudge_snapshots,
    bool is_ego_cut_behind, IntentionResultMetaData& intention_result) {
  // Only for valid collision range in st-planner.
  if (intention_result.nudge_index_ranges.empty() ||
      std::any_of(
          intention_result.nudge_index_ranges.begin(),
          intention_result.nudge_index_ranges.end(),
          [](const auto& range) { return !math::IsValidRange(range); })) {
    return false;
  }

  // Ignore cases where we can't decide a nudge direction
  if (tracked_pass_decision == pb::SnapshotIntention::IGNORE &&
      intention_result.lateral_decision == pb::SnapshotIntention::IGNORE) {
    return false;
  }

  // Gets the recommend_lateral_decision with st planner and tracked pass side.
  // Not enough drivable space for nudge decision in st planner.
  const bool is_missing_nudge_decision =
      (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE);
  const pb::SnapshotIntention::PassState recommend_lateral_decision =
      is_missing_nudge_decision ? tracked_pass_decision
                                : intention_result.lateral_decision;
  DCHECK(recommend_lateral_decision != pb::SnapshotIntention::IGNORE);

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }
  double needed_req_lat_gap_in_meter =
      should_prioritize_nudge_snapshots
          ? intention_result.required_lat_gap.critical_required_lateral_gap
          : intention_result.required_lat_gap.comfort_required_lateral_gap;
  while (needed_req_lat_gap_in_meter > -math::constants::kEpsilon) {
    if (should_prioritize_nudge_snapshots) {
      // If should_prioritize_nudge_snapshots = true, we will try to decrease
      // the req_lat_gap to give more snapshot.
      if (EgoCanPassWithGivenReqLatGap(
              predicted_states, intention_result.nudge_index_ranges,
              min_required_ego_width_m, needed_req_lat_gap_in_meter,
              recommend_lateral_decision)) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            is_missing_nudge_decision ? false
                                      : should_prioritize_nudge_snapshots);
        intention_result.lateral_decision = recommend_lateral_decision;
        absl::StrAppend(
            &intention_result.debug_str,
            "can nudge all snapshots with reduced critical buffer; ");
        return true;
      }
    } else {
      // If should_prioritize_nudge_snapshots = false, we will try to decrease
      // the nudge_index_ranges to keep a comfort lateral distance.
      const std::vector<math::Range<int>> effective_nudge_index_ranges =
          GetFilteredRecommendNudgeIndexRanges(
              predicted_states, intention_result.nudge_index_ranges,
              min_required_ego_width_m, needed_req_lat_gap_in_meter,
              recommend_lateral_decision, is_ego_cut_behind);
      if (!effective_nudge_index_ranges.empty()) {
        intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
            intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
            is_missing_nudge_decision ? false
                                      : should_prioritize_nudge_snapshots);
        intention_result.nudge_index_ranges = effective_nudge_index_ranges;
        intention_result.lateral_decision = recommend_lateral_decision;
        absl::StrAppend(
            &intention_result.debug_str,
            "can nudge all snapshots with reduced comfort buffer; ");
        return true;
      }
    }
    needed_req_lat_gap_in_meter -=
        (should_prioritize_nudge_snapshots
             ? kCirticalRequiredLateralGapDecreaseStepSizeInMeter
             : kRequiredLateralGapDecreaseStepSizeInMeter);
  }

  needed_req_lat_gap_in_meter = std::max(
      /*critical_req_lat_gap=*/(should_prioritize_nudge_snapshots ? 0.0 : 0.2),
      needed_req_lat_gap_in_meter);
  const std::vector<math::Range<int>> effective_nudge_index_ranges =
      GetFilteredRecommendNudgeIndexRanges(
          predicted_states, intention_result.nudge_index_ranges,
          min_required_ego_width_m, needed_req_lat_gap_in_meter,
          recommend_lateral_decision, is_ego_cut_behind);
  if (!effective_nudge_index_ranges.empty()) {
    intention_result.required_lat_gap = GetRecommendedRequiredLateralGap(
        intention_result.required_lat_gap, needed_req_lat_gap_in_meter,
        is_missing_nudge_decision ? false : should_prioritize_nudge_snapshots);
    intention_result.nudge_index_ranges = effective_nudge_index_ranges;
    intention_result.lateral_decision = recommend_lateral_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge reduced snapshots to avoid leave current lane; ");
    return true;
  } else if (recommend_lateral_decision != pb::SnapshotIntention::IGNORE) {
    // Treat as a static agent for empty effective_nudge_index_ranges.
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = recommend_lateral_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "nudge range empty treat as static; ");
    return true;
  }

  absl::StrAppend(&intention_result.debug_str,
                  "both effective_range and pass_decision are invalid; ");
  return false;
}

bool ShouldNudgeAgentDuringPullOver(
    const EgoInLaneParams& ego_param,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const double brake_limit_mpss) {
  if (blocking_state_data.reasoning_info.is_agent_in_front_of_front_bumper()) {
    // If the agent is in front of Ego and Ego cannot brake to yield it, then
    // consider nudge for it. Otherwise IGNORE it so that Ego will tend to
    // brake and yield for it.
    const auto& agent_trajectory =
        FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                               agent_inlane_state.primary_trajectory_id)
            .predicted_states;
    DCHECK(!agent_trajectory.empty());
    // TODO(jinghuang1): Revisit whether we want to use agent state timestamp
    // or planning init timestamp as initial time.
    const int64_t initial_timestamp = agent_trajectory.front().timestamp;
    const bool ego_cannot_brake_to_yield = std::any_of(
        agent_trajectory.begin(), agent_trajectory.end(),
        [initial_timestamp, &ego_param, &blocking_state_data,
         brake_limit_mpss](const auto& agent_state) {
          const auto& agent_params = agent_state.inlane_state.inlane_param;
          const double snapshot_time =
              math::Ms2Sec(agent_state.timestamp - initial_timestamp);
          return !CanEgoBrakeToYieldSnapshot(
              ego_param, agent_params, std::abs(brake_limit_mpss),
              blocking_state_data.reasoning_info.is_oncoming(), snapshot_time);
        });

    return ego_cannot_brake_to_yield;
  }

  if (blocking_state_data.reasoning_info.is_far_away_behind() ||
      blocking_state_data.reasoning_info.is_ignorable_behind()) {
    // If the agent is far away behind or ignorable, then ignore it.
    return false;
  }

  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    // If the agent is an overtaking cyclist, nudge for it for safety.
    return true;
  }

  if (blocking_state_data.reasoning_info.is_agent_behind_rear_bumper() &&
      agent_inlane_state.agent_metadata.agent_type ==
          voy::perception::ObjectType::CYCLIST) {
    // If the agent is a cyclist behind Ego and is not overtaking us, just
    // ignore it.
    return false;
  }

  return true;
}

bool ShouldNudgeAgentDuringPullOver(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const double brake_limit_mpss) {
  if (blocking_state_data.reasoning_info.is_agent_in_front_of_front_bumper()) {
    // If the agent is in front of Ego and Ego cannot brake to yield it, then
    // consider nudge for it. Otherwise IGNORE it so that Ego will tend to
    // brake and yield for it.
    const auto& agent_trajectory =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            object_occupancy_state.primary_trajectory_id())
            .predicted_states();
    DCHECK(!agent_trajectory.empty());
    const int64_t initial_timestamp = agent_trajectory.front().timestamp();
    const bool ego_cannot_brake_to_yield = std::any_of(
        agent_trajectory.begin(), agent_trajectory.end(),
        [initial_timestamp, &ego_param, &blocking_state_data,
         brake_limit_mpss](const auto& agent_state) {
          const auto& agent_params = agent_state.object_occupancy_param();
          const double snapshot_time =
              math::Ms2Sec(agent_state.timestamp() - initial_timestamp);
          return !CanEgoBrakeToYieldSnapshot(
              ego_param, agent_params, std::abs(brake_limit_mpss),
              blocking_state_data.reasoning_info.is_oncoming(), snapshot_time);
        });
    return ego_cannot_brake_to_yield;
  }

  if (blocking_state_data.reasoning_info.is_far_away_behind() ||
      blocking_state_data.reasoning_info.is_ignorable_behind()) {
    // If the agent is far away behind or ignorable, then ignore it.
    return false;
  }

  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    // If the agent is an overtaking cyclist, nudge for it for safety.
    return true;
  }

  if (blocking_state_data.reasoning_info.is_agent_behind_rear_bumper() &&
      object_occupancy_state.object_type() ==
          voy::perception::ObjectType::CYCLIST) {
    // If the agent is a cyclist behind Ego and is not overtaking us, just
    // ignore it.
    return false;
  }

  return true;
}

bool ShouldGenerateIgnoreDuringPullover(
    const math::geometry::Point2d& ego_point,
    const std::optional<math::geometry::Point2d>& pull_over_destination,
    const double ego_speed_mps, const pb::BehaviorType behavior_type) {
  if (!pull_over_destination.has_value() ||
      behavior_type != pb::BehaviorType::DECOUPLED_PULL_OVER) {
    // If it is not pullover or we don't have pullover destination, return
    // false.
    return false;
  }

  const math::geometry::Point2d direction(
      pull_over_destination->x() - ego_point.x(),
      pull_over_destination->y() - ego_point.y());
  const double dist_to_destination_m = math::geometry::Length(direction);

  if (dist_to_destination_m < kCloseDistanceThresholdToDestinationInM &&
      ego_speed_mps < kSlowMovingSpeedThresholdInMps) {
    return true;
  }

  return false;
}

std::optional<RepulsionMetaData::RepulsionDirection>
GetRepulsionDirectionBasedOnLastPathCurve(
    const std::optional<pb::Path>& reusable_last_path,
    const math::geometry::Point2d& center_point) {
  if (!reusable_last_path.has_value() || reusable_last_path->poses_size() < 2) {
    return std::nullopt;
  }
  math::geometry::Polyline2d last_path_curve_points;
  for (const auto& pose : reusable_last_path->poses()) {
    last_path_curve_points.emplace_back(pose.x_pos(), pose.y_pos());
  }

  math::geometry::Polyline2d simplified_last_path_curve_points =
      math::geometry::Simplify(std::move(last_path_curve_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_last_path_curve_points.size() < 2) {
    return std::nullopt;
  }
  const math::geometry::PolylineCurve2d last_path_curve(
      std::move(simplified_last_path_curve_points));
  return GetRepulsionDirection(last_path_curve, center_point);
}

RepulsionMetaData::RepulsionDirection GetRepulsionDirection(
    const math::geometry::PolylineCurve2d& refer_path_curve,
    const math::geometry::Point2d& center_point) {
  math::pb::Side curve_side = refer_path_curve.GetSide(center_point);
  return curve_side == math::pb::Side::kLeft
             ? RepulsionMetaData::RepulsionDirection::kRight
             : RepulsionMetaData::RepulsionDirection::kLeft;
}

math::pb::Side CalcActiveLateralMoveDirection(
    const pb::BehaviorType behavior_type,
    const pb::LaneKeepBehaviorType lane_keep_behavior_type,
    const std::optional<LaneChangeExecutionInfo>&
        optional_lane_change_execution_info,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info) {
  math::pb::Side dir = math::pb::Side::kOn;

  if (FLAGS_planning_enable_enlarge_lat_gap_for_xlane_nudge &&
      behavior_type == pb::BehaviorType::LANE_KEEP) {
    if (lane_keep_behavior_type == pb::LK_LEFT_XLANE) {
      dir = math::pb::Side::kLeft;
    } else if (lane_keep_behavior_type == pb::LK_RIGHT_XLANE) {
      dir = math::pb::Side::kRight;
    }
  }

  if (FLAGS_planning_enable_enlarge_lat_gap_for_lane_change &&
      behavior_type == pb::BehaviorType::CROSS_LANE) {
    DCHECK(optional_lane_change_execution_info.has_value());
    if (optional_lane_change_execution_info->lane_change_direction ==
        planner::pb::LaneChangeMode::LEFT_LANE_CHANGE) {
      dir = math::pb::Side::kLeft;
    } else if (optional_lane_change_execution_info->lane_change_direction ==
               planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE) {
      dir = math::pb::Side::kRight;
    }
  }

  if (FLAGS_planning_enable_enlarge_lat_gap_for_pull_over &&
      behavior_type == pb::BehaviorType::DECOUPLED_PULL_OVER) {
    dir = math::pb::Side::kRight;
  }

  // For inlane nudge, update based on Ego lateral speed.
  if (behavior_type == pb::BehaviorType::LANE_KEEP &&
      lane_keep_behavior_type == pb::LK_DEFAULT) {
    const auto ego_lateral_speed =
        traffic_rule_reasoning_info.ego_cross_track_speed_mps();
    if (ego_lateral_speed >
        kEgoLateralSpeedThresholdForActiveLateralMovingInMps) {
      dir = math::pb::Side::kLeft;
    } else if (ego_lateral_speed <
               -1.0 * kEgoLateralSpeedThresholdForActiveLateralMovingInMps) {
      dir = math::pb::Side::kRight;
    }
  }

  return dir;
}

void MaybeAdjustPedLateralGapBasedOnSpeed(
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const path::ActiveLatGapAdjustMetaData& active_lat_gap_adjust_meta,
    const std::optional<double>& ego_speed_near_agent,
    const pb::SnapshotIntention::PassState intention_algorithm_pass_side,
    const voy::perception::ObjectType object_type,
    RequiredLateralGap& required_lat_gap, std::string* debug_str) {
  if (!planner::FLAGS_planning_enable_speed_based_rlg_for_ped) {
    return;
  }

  DCHECK(object_type == voy::perception::PED);

  const double valid_ego_speed_mps =
      ego_speed_near_agent.has_value()
          ? *ego_speed_near_agent
          : active_lat_gap_adjust_meta.ego_speed_mps;
  // These numbers are from
  // https://cooper.didichuxing.com/knowledge/2199518142735/2199894063360.
  // And we ignored the quadratic item.
  constexpr double kSpeedBasedLateralGapSlope = 0.07;
  constexpr double kSpeedBasedLateralGapIntercept = 0.5;
  const double speed_based_rlg =
      kSpeedBasedLateralGapSlope * valid_ego_speed_mps +
      kSpeedBasedLateralGapIntercept;

  bool adjusted_comfort_rlg = false;
  bool adjusted_critical_rlg = false;

  constexpr double comfort_rlg_buffer_meters = 0.5;
  // 1. Increase comfort RLG when it's smaller than speed_based_rlg plus buffer.
  if (speed_based_rlg + comfort_rlg_buffer_meters >
      required_lat_gap.comfort_required_lateral_gap) {
    required_lat_gap.comfort_required_lateral_gap =
        speed_based_rlg + comfort_rlg_buffer_meters;
    adjusted_comfort_rlg = true;
    if (debug_str) {
      absl::StrAppendFormat(debug_str, "ped use speed based comfort rlg %.2f; ",
                            required_lat_gap.comfort_required_lateral_gap);
    }
  }

  const pb::SnapshotIntention::PassState safe_pass_side =
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes();
  const bool should_increase_critical_rlg =
      safe_pass_side == pb::SnapshotIntention::PASS_BOTH ||
      safe_pass_side == intention_algorithm_pass_side;
  // 2. Increase critical RLG when it's smaller than speed_based_rlg and there
  // is empty lane on the other side.
  if (should_increase_critical_rlg &&
      speed_based_rlg > required_lat_gap.critical_required_lateral_gap) {
    required_lat_gap.critical_required_lateral_gap = speed_based_rlg;
    adjusted_critical_rlg = true;
    if (debug_str) {
      absl::StrAppendFormat(debug_str,
                            "ped use speed based critical rlg %.2f; ",
                            required_lat_gap.critical_required_lateral_gap);
    }
  }

  if (adjusted_comfort_rlg || adjusted_critical_rlg) {
    rt_event::PostRtEvent<rt_event::planner::UseSpeedBasedRlgForPed>();
  }
}

// This function calculates and adjusts the required lateral gap during lane
// change or lane change abort. It considers the relative position of objects in
// the target region and modifies the gap requirements accordingly to ensure
// safety.
void AdjustLateralGapBasedOnLateralMoveDirectionDuringLaneChangeOrLaneChangeAbort(
    const double ego_speed, const math::geometry::Point2d ego_ra_point,
    const LaneChangeInfo& lane_change_info, const int64_t object_id,
    const pb::BehaviorType behavior_type, RequiredLateralGap& required_lat_gap,
    std::string* debug_str, bool* adjusted_rlg) {
  const LaneChangeObjectInfoList* interested_region_object_info_list =
      (behavior_type == pb::BehaviorType::LANE_KEEP)
          ? GetSourceRegionObjectInfoListPtr(lane_change_info)
          : GetTargetRegionObjectInfoListPtr(lane_change_info);

  if (interested_region_object_info_list == nullptr) {
    if (debug_str) {
      absl::StrAppend(debug_str,
                      "No active lateral move based RLG update for LC: "
                      "interested region is null; ");
    }
    return;
  }
  const LaneChangeObjectInfo* obj_ptr =
      interested_region_object_info_list->Find(object_id);
  if (obj_ptr == nullptr) {
    if (debug_str) {
      absl::StrAppend(
          debug_str,
          "No active lateral move based RLG update for LC: no obj found; ");
    }
    return;
  }

  // Sometimes Ego need to creep near agents, we don't want to increase RLG in
  // this case.
  if (ego_speed < kSlowMovingSpeedThresholdInMps &&
      obj_ptr->speed < kSlowMovingSpeedThresholdInMps) {
    if (debug_str) {
      absl::StrAppend(debug_str,
                      "No active lateral move based RLG update for LC: ego and "
                      "agent are slow; ");
    }
    return;
  }

  constexpr double kMaxAgentEgoLonDistWhenAdjustReqLatGapIncrementInM = 5.0;
  constexpr double kMinAgentEgoLonDistWhenAdjustReqLatGapIncrementInM = 2.0;
  constexpr double kTimeForSpeedBasedLonDistInSec = 2.0;
  const double speed_based_lon_distance =
      std::min(kTimeForSpeedBasedLonDistInSec * obj_ptr->speed +
                   kMinAgentEgoLonDistWhenAdjustReqLatGapIncrementInM,
               kMaxAgentEgoLonDistWhenAdjustReqLatGapIncrementInM);

  // We don't add required lat gap for the object which is too far behind.
  if (obj_ptr->agent_fb_to_ego_rb < -speed_based_lon_distance) {
    if (debug_str) {
      absl::StrAppend(
          debug_str,
          "No active lateral move based RLG update for LC: agent far behind; ");
    }
    return;
  }

  if (adjusted_rlg) {
    *adjusted_rlg = true;
  }

  const double initial_critical_required_lateral_gap =
      required_lat_gap.critical_required_lateral_gap;
  const double max_critical_required_lateral_gap =
      required_lat_gap.critical_required_lateral_gap +
      kDefensiveCriticalLatGapIncrementInM;
  const double agent_corner_signed_lat_dist_to_cross_lane_curve =
      obj_ptr->agent_corner_signed_lat_dist_to_cross_lane_curve;
  const double ego_corner_signed_lat_dist_to_cross_lane_curve =
      lane_change_info.lane_change_metadata
          .ego_corner_signed_lat_dist_to_cross_lane_curve();
  // The direction sign is used to determine the direction of the lane change or
  // lane change abort.
  double direction_sign = 0.0;
  if (behavior_type == pb::BehaviorType::LANE_KEEP) {
    const double ego_ra_signed_dist =
        lane_change_info.lane_change_instance.source_lane()
            .center_line()
            .GetProximity(ego_ra_point, math::pb::UseExtensionFlag::kAllow)
            .signed_dist;
    direction_sign = ego_ra_signed_dist > 0.0 ? 1.0 : -1.0;
  } else {
    direction_sign = lane_change_info.lane_change_instance.direction() ==
                             planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
                         ? -1.0
                         : 1.0;
  }
  // If the agent and the lane change or lane change abort direction are
  // consistent,  ego_agent_min_lateral_distance is positive.
  const double ego_agent_min_lateral_distance =
      (ego_corner_signed_lat_dist_to_cross_lane_curve -
       agent_corner_signed_lat_dist_to_cross_lane_curve) *
      direction_sign;
  const double lateral_dist_based_critical_required_lateral_gap =
      std::max(initial_critical_required_lateral_gap,
               std::min(max_critical_required_lateral_gap,
                        ego_agent_min_lateral_distance));

  // Case 1: The agent's front bumper is ahead of the ego rear bumper, calculate
  // required lat
  // gap based on lateral distance between the agent and the ego. If the agent's
  // rear bumper has passed ego's front bumper, we will smooth the required lat
  // gap from lateral_dist_based_critical_required_lateral_gap to
  // max_critical_required_lateral_gap according to the longitudinal distance
  // between the agent and the ego.
  if (obj_ptr->agent_fb_to_ego_rb > 0.0) {
    const double new_critical_required_lateral_gap =
        math::GetLinearInterpolatedY(
            0.0, speed_based_lon_distance,
            lateral_dist_based_critical_required_lateral_gap,
            max_critical_required_lateral_gap, obj_ptr->agent_rb_to_ego_fb,
            false);

    required_lat_gap.critical_required_lateral_gap =
        new_critical_required_lateral_gap;
    required_lat_gap.comfort_required_lateral_gap +=
        new_critical_required_lateral_gap -
        initial_critical_required_lateral_gap;
    if (debug_str) {
      absl::StrAppend(
          debug_str,
          " \nego actively move lateral, add agent lat gap according "
          "to ego agent's distance during "
          "lane change %.4f , lateral_distance_based_lateral_gap %.4f; ",
          new_critical_required_lateral_gap, ego_agent_min_lateral_distance);
    }
    return;
  }
  // Case 2: If the agent is behind certain distance, we will smooth the
  // required lat gap from initial_critical_required_lateral_gap to
  // lateral_dist_based_critical_required_lateral_gap according to the
  // longitudinal distance between the agent and the ego.
  const double new_critical_required_lateral_gap = math::GetLinearInterpolatedY(
      -speed_based_lon_distance, 0.0, initial_critical_required_lateral_gap,
      lateral_dist_based_critical_required_lateral_gap,
      obj_ptr->agent_fb_to_ego_rb, false);
  required_lat_gap.critical_required_lateral_gap =
      new_critical_required_lateral_gap;
  required_lat_gap.comfort_required_lateral_gap +=
      new_critical_required_lateral_gap - initial_critical_required_lateral_gap;
  if (debug_str) {
    absl::StrAppend(debug_str,
                    "add lat gap proportional increment based on lon "
                    "distance during lane change for agent behind ");
  }
}

void AdjustLateralGapBasedOnLateralMoveDirection(
    const LaneChangeInfo& lane_change_info, const int64_t object_id,
    const pb::SnapshotIntention::PassState agent_pass_state,
    const ActiveLatGapAdjustMetaData& active_lat_gap_adjust_meta,
    const double agent_speed_mps,
    const bool is_interested_agent_during_lane_change_or_abort,
    RequiredLateralGap& required_lat_gap, std::string* debug_str,
    bool* adjusted_rlg) {
  if (is_interested_agent_during_lane_change_or_abort) {
    AdjustLateralGapBasedOnLateralMoveDirectionDuringLaneChangeOrLaneChangeAbort(
        active_lat_gap_adjust_meta.ego_speed_mps,
        active_lat_gap_adjust_meta.ego_ra_point, lane_change_info, object_id,
        active_lat_gap_adjust_meta.behavior_type, required_lat_gap, debug_str,
        adjusted_rlg);
    return;
  }

  if (active_lat_gap_adjust_meta.intended_lat_move_direction ==
      math::pb::Side::kOn) {
    // If Ego is not having active lateral movement, don't adjust.
    if (debug_str) {
      absl::StrAppend(debug_str, "Ego no active lateral move; ");
    }
    return;
  }

  const double arc_length =
      active_lat_gap_adjust_meta.current_lane_center_line
          .GetProximity(
              math::geometry::Point2d{
                  active_lat_gap_adjust_meta.ego_ra_point.x(),
                  active_lat_gap_adjust_meta.ego_ra_point.y()},
              math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const double reference_point_heading =
      active_lat_gap_adjust_meta.current_lane_center_line.GetInterpTheta(
          arc_length);
  const double relative_heading_rad = math::AngleDiff(
      active_lat_gap_adjust_meta.ego_heading_rad, reference_point_heading);

  if ((active_lat_gap_adjust_meta.intended_lat_move_direction ==
           math::pb::Side::kRight &&
       relative_heading_rad > -kActiveLatMoveAngleThresholdInRad) ||
      (active_lat_gap_adjust_meta.intended_lat_move_direction ==
           math::pb::Side::kLeft &&
       relative_heading_rad < kActiveLatMoveAngleThresholdInRad)) {
    // If Ego's current heading is not pointing to the lateral move direction
    // enough, meaning Ego is not engaging the lateral movement enough, don't
    // enlarge lat gap to avoid swerving to the opposite side.
    if (debug_str) {
      absl::StrAppend(
          debug_str,
          "Ego heading not align with active lateral move direction; ");
    }
    return;
  }

  if (active_lat_gap_adjust_meta.ego_speed_mps <
          kSlowMovingSpeedThresholdInMps &&
      agent_speed_mps < kSlowMovingSpeedThresholdInMps) {
    // If Ego and agent are both slow moving, there is not much safety issue. To
    // avoid Ego have unnecessary veer to opposite direction, we don't enlarge
    // lat gap.
    if (debug_str) {
      absl::StrAppend(
          debug_str,
          "No active lateral move based RLG update: ego and agent are slow; ");
    }
    return;
  }

  const pb::SnapshotIntention::PassState interested_pass_state =
      active_lat_gap_adjust_meta.intended_lat_move_direction ==
              math::pb::Side::kLeft
          ? pb::SnapshotIntention::PASS_RIGHT
          : pb::SnapshotIntention::PASS_LEFT;

  if (agent_pass_state == interested_pass_state) {
    required_lat_gap.critical_required_lateral_gap +=
        kDefensiveCriticalLatGapIncrementInM;
    required_lat_gap.comfort_required_lateral_gap +=
        kDefensiveComfortLatGapIncrementInM;
    if (debug_str) {
      absl::StrAppend(debug_str,
                      "ego actively move lateral, may adjust agent lat gap; ");
    }
    if (adjusted_rlg) {
      *adjusted_rlg = true;
    }
  }

  return;
}

void MaybeAdjustLateralGapBasedOnTtc(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const double ego_speed_mps, const voy::perception::ObjectType object_type,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const pb::SnapshotIntention::PassState intention_algorithm_pass_side,
    RequiredLateralGap& required_lat_gap, std::string* debug_str) {
  const auto safe_pass_side =
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes();
  const bool should_increase_rlg =
      safe_pass_side == pb::SnapshotIntention::PASS_BOTH ||
      safe_pass_side == intention_algorithm_pass_side;
  if (!should_increase_rlg) {
    return;
  }

  // Sometimes Ego need to creep near agents (for example, creep peds near
  // crosswalk), we don't want to increase RLG in this case.
  constexpr double kMinEgoSpeedMps = 2.0;
  if (ego_speed_mps < kMinEgoSpeedMps) {
    return;
  }

  // When agent TTC (time to collision) is less than this, we may increase the
  // required lateral gap.
  constexpr double kMaxTtcForLateralGapAdjustmentSeconds = 3.0;

  // Increase RLG for ped in intersection.
  if (object_type == voy::perception::PED &&
      reasoning_info.is_agent_in_junction() &&
      reasoning_info.tracked_pose_ttc_in_seconds() <
          kMaxTtcForLateralGapAdjustmentSeconds) {
    constexpr double kMinCriticalRLGMeters = 1.0;
    constexpr double kMinComfortRLGMeters = 1.5;
    const bool need_update_rlg =
        kMinCriticalRLGMeters >
            required_lat_gap.critical_required_lateral_gap ||
        kMinComfortRLGMeters > required_lat_gap.comfort_required_lateral_gap;
    if (need_update_rlg) {
      rt_event::PostRtEvent<
          rt_event::planner::IncreaseRLGForPedInIntersection>();
      if (debug_str) {
        absl::StrAppendFormat(debug_str, "increase RLG for ped in junction, ");
      }
    }
    math::UpdateMax(kMinCriticalRLGMeters,
                    required_lat_gap.critical_required_lateral_gap);
    math::UpdateMax(kMinComfortRLGMeters,
                    required_lat_gap.comfort_required_lateral_gap);
  }

  // Increase RLG for vehicle when Ego is turning.
  const bool is_ego_turning_in_intersection =
      traffic_rule_reasoning_info.is_ego_in_junction() &&
      (traffic_rule_reasoning_info.is_ego_in_left_turn_lane() ||
       traffic_rule_reasoning_info.is_ego_in_right_turn_lane());
  if (object_type == voy::perception::ObjectType::VEHICLE &&
      is_ego_turning_in_intersection &&
      reasoning_info.tracked_pose_ttc_in_seconds() <
          kMaxTtcForLateralGapAdjustmentSeconds) {
    constexpr double kMinCriticalRLGMeters = 0.8;
    constexpr double kMinComfortRLGMeters = 1.5;
    const bool need_update_rlg =
        kMinCriticalRLGMeters >
            required_lat_gap.critical_required_lateral_gap ||
        kMinComfortRLGMeters > required_lat_gap.comfort_required_lateral_gap;
    if (need_update_rlg) {
      rt_event::PostRtEvent<
          rt_event::planner::IncreaseRLGForVehicleWhenEgoTurns>();
      if (debug_str) {
        absl::StrAppendFormat(debug_str,
                              "increase RLG for vehicle when Ego turns, ");
      }
    }
    math::UpdateMax(kMinCriticalRLGMeters,
                    required_lat_gap.critical_required_lateral_gap);
    math::UpdateMax(kMinComfortRLGMeters,
                    required_lat_gap.comfort_required_lateral_gap);
  }
}

bool IsAgentPrimarilyOutOfPhysicalBoundary(
    const lateral_clearance::LateralClearanceData&
        physical_boundary_lateral_clearance_data,
    const std::vector<StampedAgentSnapshotInLaneState>&
        agent_primary_traj_states,
    bool is_irregular_object,
    const math::geometry::PolylineCurve2d& nominal_path) {
  // Heading angle diff range to determine whether agent is moving parallel with
  // nominal path.
  constexpr double kHeadingAngleDiffRangeMinInRad = 1.0 / 12.0 * M_PI;
  constexpr double kHeadingAngleDiffRangeMaxInRad = 11.0 / 12.0 * M_PI;

  if (is_irregular_object) {
    // If the agent is irregular shape, don't consider it is out of physical
    // boundary.
    return false;
  }

  // Check along agent's predicted states to see if it is out of physical
  // boundary.
  for (const auto& state : agent_primary_traj_states) {
    const auto& inlane_param = state.inlane_state.inlane_param;
    if (inlane_param.center_line_side == math::pb::kOn ||
        inlane_param.is_fully_in_lane) {
      return false;
    }
    const double state_arclength_m = inlane_param.full_body_start_arclength_m;
    const double state_heading = inlane_param.pose.heading();
    const double reference_point_heading =
        nominal_path.GetInterpTheta(state_arclength_m);
    double abs_heading_diff_rad = std::abs(
        math::NormalizeMinusPiToPi(state_heading - reference_point_heading));
    const bool agent_not_parallel_to_nominal_path =
        math::IsInRange(abs_heading_diff_rad, kHeadingAngleDiffRangeMinInRad,
                        kHeadingAngleDiffRangeMaxInRad);
    if (agent_not_parallel_to_nominal_path) {
      // Agent's moving direction is not parallel to nominal path, meaning it
      // could go into physical boundary and interact with Ego.
      return false;
    }

    const std::optional<lateral_clearance::LateralClearance>
        optional_physical_lateral_clearance =
            physical_boundary_lateral_clearance_data.GetClearanceOptional(
                state_arclength_m);
    if (!optional_physical_lateral_clearance.has_value()) {
      // Agent state is out of physical boundary longitudinal range. Return
      // false for conservative purpose.
      return false;
    }
    const auto& interested_clearance =
        inlane_param.center_line_side == math::pb::kRight
            ? optional_physical_lateral_clearance->right_clearance
            : optional_physical_lateral_clearance->left_clearance;

    if (math::NearZero(std::abs(interested_clearance.distance) -
                       physical_boundary_lateral_clearance_data
                           .max_abs_lateral_clearance())) {
      // If there is no physical boundary in agent's state region, then we
      // cannot tell if it is out of boundary or not. Return false for
      // conservative purpose.
      return false;
    }

    const double abs_agent_lat_distance_to_center_line =
        inlane_param.center_line_side == math::pb::kRight
            ? std::abs(inlane_param.full_body_end_lateral_distance_m)
            : std::abs(inlane_param.full_body_start_lateral_distance_m);
    if (abs_agent_lat_distance_to_center_line <
        std::abs(interested_clearance.distance)) {
      // Agent state is not outside of physical boundary, return false.
      return false;
    }
  }

  return true;
}

bool IsAgentPrimarilyOutOfPhysicalBoundary(
    const lateral_clearance::LateralClearanceData&
        physical_boundary_lateral_clearance_data,
    const std::vector<ObjectStampedSnapshotInfo>& agent_primary_traj_states,
    bool is_irregular_object,
    const math::geometry::PolylineCurve2d& nominal_path) {
  // Heading angle diff range to determine whether agent is moving parallel with
  // nominal path.
  constexpr double kHeadingAngleDiffRangeMinInRad = 1.0 / 12.0 * M_PI;
  constexpr double kHeadingAngleDiffRangeMaxInRad = 11.0 / 12.0 * M_PI;

  if (is_irregular_object) {
    // If the agent is irregular shape, don't consider it is out of physical
    // boundary.
    return false;
  }

  // Check along agent's predicted states to see if it is out of physical
  // boundary.
  for (const auto& state : agent_primary_traj_states) {
    const auto& occupancy_param = state.object_occupancy_param();
    if (occupancy_param.center_line_side == math::pb::kOn ||
        occupancy_param.is_fully_within_corridor) {
      return false;
    }
    const double state_arclength_m =
        occupancy_param.full_body_start_arclength_m;
    const double state_heading = state.pose().heading();
    const double reference_point_heading =
        nominal_path.GetInterpTheta(state_arclength_m);
    double abs_heading_diff_rad = std::abs(
        math::NormalizeMinusPiToPi(state_heading - reference_point_heading));
    const bool agent_not_parallel_to_nominal_path =
        math::IsInRange(abs_heading_diff_rad, kHeadingAngleDiffRangeMinInRad,
                        kHeadingAngleDiffRangeMaxInRad);
    if (agent_not_parallel_to_nominal_path) {
      // Agent's moving direction is not parallel to nominal path, meaning it
      // could go into physical boundary and interact with Ego.
      return false;
    }

    const std::optional<lateral_clearance::LateralClearance>
        optional_physical_lateral_clearance =
            physical_boundary_lateral_clearance_data.GetClearanceOptional(
                state_arclength_m);
    if (!optional_physical_lateral_clearance.has_value()) {
      // Agent state is out of physical boundary longitudinal range. Return
      // false for conservative purpose.
      return false;
    }
    const auto& interested_clearance =
        occupancy_param.center_line_side == math::pb::kRight
            ? optional_physical_lateral_clearance->right_clearance
            : optional_physical_lateral_clearance->left_clearance;

    if (math::NearZero(std::abs(interested_clearance.distance) -
                       physical_boundary_lateral_clearance_data
                           .max_abs_lateral_clearance())) {
      // If there is no physical boundary in agent's state region, then we
      // cannot tell if it is out of boundary or not. Return false for
      // conservative purpose.
      return false;
    }

    const double abs_agent_lat_distance_to_center_line =
        occupancy_param.center_line_side == math::pb::kRight
            ? std::abs(occupancy_param.full_body_end_lateral_distance_m)
            : std::abs(occupancy_param.full_body_start_lateral_distance_m);
    if (abs_agent_lat_distance_to_center_line <
        std::abs(interested_clearance.distance)) {
      // Agent state is not outside of physical boundary, return false.
      return false;
    }
  }

  return true;
}

std::optional<math::geometry::PolylineCurve2d> GetLastPathCurve(
    const std::optional<pb::Path>& reusable_last_path, std::string& debug_str) {
  if (!reusable_last_path.has_value() || reusable_last_path->poses_size() < 2) {
    debug_str += "no valid reusable_last_path";
    return std::nullopt;
  }
  math::geometry::Polyline2d last_path_curve_points;
  for (const auto& pose : reusable_last_path->poses()) {
    last_path_curve_points.emplace_back(pose.x_pos(), pose.y_pos());
  }

  math::geometry::Polyline2d simplified_last_path_curve_points =
      math::geometry::Simplify(
          std::move(last_path_curve_points),
          /*max_distance=*/math::constants::kDefaultSimplifyThreshold,
          /*assert_no_self_intersection=*/false);
  if (simplified_last_path_curve_points.size() < 2) {
    debug_str += "last path simplify failed";
    return std::nullopt;
  }
  math::geometry::PolylineCurve2d last_path_curve(
      std::move(simplified_last_path_curve_points));
  return last_path_curve;
}

std::optional<math::geometry::PolylineCurve2d> GetLastPathCurveOrRefPathCurve(
    const std::optional<pb::Path>& reusable_last_path,
    std::optional<math::geometry::PolylineCurve2d> truncated_reference_path,
    std::string& debug_str) {
  std::optional<math::geometry::PolylineCurve2d> refer_path_curve =
      std::nullopt;
  std::optional<math::geometry::PolylineCurve2d> last_path_curve =
      GetLastPathCurve(reusable_last_path, debug_str);
  if (last_path_curve.has_value()) {
    refer_path_curve = std::move(*last_path_curve);
  } else if (truncated_reference_path.has_value()) {
    rt_event::PostRtEvent<rt_event::planner::NoLastTrajUseRefAsAlternative>();
    debug_str += "no usable last path, use truncated reference path instead";
    refer_path_curve = *truncated_reference_path;
  } else {
    debug_str += "no valid last path or truncated reference path";
  }
  return refer_path_curve;
}

bool IsTargetDirectionRisky(
    const RepulsionMetaData::RepulsionDirection& repulsion_direction,
    const std::vector<FrameAnalyzerResult>& frame_analyzer_results) {
  for (const auto& frame_analyzer_result : frame_analyzer_results) {
    if (frame_analyzer_result.frame_type ==
            pb::FrameAnalysis_FrameType_LEFT_LANE &&
        !frame_analyzer_result.hazardous_object_ids.empty() &&
        repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft) {
      return true;
    } else if (frame_analyzer_result.frame_type ==
                   pb::FrameAnalysis_FrameType_RIGHT_LANE &&
               !frame_analyzer_result.hazardous_object_ids.empty() &&
               repulsion_direction ==
                   RepulsionMetaData::RepulsionDirection::kRight) {
      return true;
    }
  }
  return false;
}
bool IsTargetDirectionLaneBoundaryUncrossable(
    const RepulsionMetaData::RepulsionDirection& repulsion_direction,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info) {
  if (repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft) {
    return !traffic_rule_reasoning_info.can_ego_drive_on_left_lane();
  } else if (repulsion_direction ==
             RepulsionMetaData::RepulsionDirection::kRight) {
    return !traffic_rule_reasoning_info.can_ego_drive_on_right_lane();
  }
  return false;
}

std::optional<double> GetMaxEgoSpeedNearAgent(
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const AgentInLaneStates& agent_inlane_state, const double ego_arclength,
    const double buffer_before_agent_meters) {
  if (!reusable_last_speed_profile.has_value()) {
    return std::nullopt;
  }

  const auto& agent_inlane_param =
      agent_inlane_state.tracked_state.inlane_param;
  const std::optional<speed::State> ego_state_at_agent_start_s =
      speed::GetStateAtArcLength(
          *reusable_last_speed_profile,
          agent_inlane_param.full_body_start_arclength_m - ego_arclength -
              buffer_before_agent_meters);
  const std::optional<speed::State> ego_state_at_agent_end_s =
      speed::GetStateAtArcLength(
          *reusable_last_speed_profile,
          agent_inlane_param.full_body_end_arclength_m - ego_arclength);

  if (!ego_state_at_agent_start_s.has_value() ||
      !ego_state_at_agent_end_s.has_value()) {
    return std::nullopt;
  }

  return std::max(ego_state_at_agent_start_s->v, ego_state_at_agent_end_s->v);
}

std::optional<double> GetMaxEgoSpeedNearAgent(
    const std::optional<speed::Profile>& reusable_last_speed_profile,
    const ObjectOccupancyState& object_occupancy_state,
    const double ego_arclength, const double buffer_before_agent_meters) {
  if (!reusable_last_speed_profile.has_value()) {
    return std::nullopt;
  }

  const auto& agent_inlane_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const std::optional<speed::State> ego_state_at_agent_start_s =
      speed::GetStateAtArcLength(
          *reusable_last_speed_profile,
          agent_inlane_param.full_body_start_arclength_m - ego_arclength -
              buffer_before_agent_meters);
  const std::optional<speed::State> ego_state_at_agent_end_s =
      speed::GetStateAtArcLength(
          *reusable_last_speed_profile,
          agent_inlane_param.full_body_end_arclength_m - ego_arclength);

  if (!ego_state_at_agent_start_s.has_value() ||
      !ego_state_at_agent_end_s.has_value()) {
    return std::nullopt;
  }

  return std::max(ego_state_at_agent_start_s->v, ego_state_at_agent_end_s->v);
}

void MaybeAdjustCycLateralGapBasedOnSpeed(
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    const double cyc_along_track_speed_mps, const double current_ego_speed_mps,
    const std::optional<double>& estimated_ego_speed_near_agent_mps,
    const pb::SnapshotIntention::PassState intention_algorithm_pass_side,
    const bool is_cyc_behind_rear_bumper,
    const voy::perception::ObjectType object_type,
    RequiredLateralGap& required_lat_gap, std::string* debug_str) {
  if (!FLAGS_planning_enable_speed_based_rlg_for_cyclist) {
    return;
  }

  DCHECK(object_type == voy::perception::CYCLIST);

  // For now, we focused on nudge and pass situations, so don't consider cyc
  // behind Ego or cyc that's faster than Ego.
  if (is_cyc_behind_rear_bumper ||
      cyc_along_track_speed_mps > current_ego_speed_mps) {
    return;
  }

  // Here we assume Ego speed is mostly along track.
  const double valid_ego_speed_mps =
      estimated_ego_speed_near_agent_mps.has_value()
          ? *estimated_ego_speed_near_agent_mps
          : current_ego_speed_mps;
  const double abs_relative_speed_mps =
      std::abs(valid_ego_speed_mps - cyc_along_track_speed_mps);

  // These numbers are determined by the following criterions:
  // (1) When Ego speed is 0, we keep RLG the same as in
  // decoupled_forward_maneuver.conf (2) When Ego speed is not high, we just
  // slightly increase RLG, for example, set critical RLG as 0.55 when Ego speed
  // is 4m/s. (3) When Ego speed is high, increase RLG much more, for example,
  // set critical RLG as 1.05 when Ego speed is 10m/s. (4) Set an upper bound
  // for critical RLG as 1.5m.
  constexpr double kSpeedBasedLateralGapQuadratic = 0.006;
  constexpr double kSpeedBasedLateralGapIntercept = 0.45;
  constexpr double kSpeedBasedCriticalLateralGapUpperBoundMeters = 1.5;
  const double speed_based_critical_rlg =
      std::min(kSpeedBasedCriticalLateralGapUpperBoundMeters,
               kSpeedBasedLateralGapQuadratic * abs_relative_speed_mps *
                       abs_relative_speed_mps +
                   kSpeedBasedLateralGapIntercept);

  bool adjusted_comfort_rlg = false;
  bool adjusted_critical_rlg = false;

  // This value should be the same as cyc comfort RLG minus critical RLG
  // as in decoupled_forward_maneuver.conf
  constexpr double comfort_rlg_buffer_meters = 0.55;
  // 1. Increase comfort RLG when it's smaller than speed_based_rlg plus buffer.
  if (speed_based_critical_rlg + comfort_rlg_buffer_meters >
      required_lat_gap.comfort_required_lateral_gap) {
    required_lat_gap.comfort_required_lateral_gap =
        speed_based_critical_rlg + comfort_rlg_buffer_meters;
    adjusted_comfort_rlg = true;
    if (debug_str) {
      absl::StrAppendFormat(debug_str, "cyc use speed based comfort rlg %.2f; ",
                            required_lat_gap.comfort_required_lateral_gap);
    }
  }

  // 2. Increase critical RLG when it's smaller than speed_based_rlg and there
  // is empty lane on the nudge direction.
  const pb::SnapshotIntention::PassState safe_pass_side =
      traffic_rule_reasoning_info.safe_pass_state_for_adjacent_lanes();
  const bool should_increase_critical_rlg =
      safe_pass_side == pb::SnapshotIntention::PASS_BOTH ||
      (safe_pass_side != pb::SnapshotIntention::IGNORE &&
       safe_pass_side == intention_algorithm_pass_side);
  if (should_increase_critical_rlg &&
      speed_based_critical_rlg >
          required_lat_gap.critical_required_lateral_gap) {
    required_lat_gap.critical_required_lateral_gap = speed_based_critical_rlg;
    adjusted_critical_rlg = true;
    if (debug_str) {
      absl::StrAppendFormat(debug_str,
                            "cyc use speed based critical rlg %.2f; ",
                            required_lat_gap.critical_required_lateral_gap);
    }
  }

  if (adjusted_comfort_rlg || adjusted_critical_rlg) {
    rt_event::PostRtEvent<rt_event::planner::UseSpeedBasedRlgForCyc>();
  }
}

// Returns true for objects whose current pose is ignorable behind.
bool IsIgnorableBehindAgent(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info) {
  if (reasoning_info.is_interested_agent_during_lane_change_or_abort()) {
    return false;
  }
  // Only consider behind rear bumper agent.
  if (!reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }

  // When Ego is
  // 1. In left turn lane or will enter virtual left turn lane, and
  // 2. Not in leftmost lane.
  // We should not ignore agent on left (for example, task/292357).
  if ((traffic_rule_reasoning_info.is_ego_in_left_turn_lane() ||
       traffic_rule_reasoning_info.will_ego_enter_virtual_left_turn_lane()) &&
      !traffic_rule_reasoning_info.is_ego_in_left_most_drivable_lane() &&
      reasoning_info.tracked_side_to_ego() != math::pb::kRight) {
    return false;
  }

  // 2) Check with the results of scenario identifier.
  // TODO(tianxi): revisit this logic for xlane nudge.
  if (scenario_identify_result.scenario_type ==
          pb::ScenarioRecognition::AVOID_NUDGE ||
      scenario_identify_result.scenario_type ==
          pb::ScenarioRecognition::XLANE_NUDGE) {
    return true;
  }

  if (scenario_identify_result.scenario_type ==
          pb::ScenarioRecognition::INLANE_NUDGE &&
      scenario_identify_result.inlane_nudge_scene ==
          pb::InLaneNudgeSceneType::kDefault) {
    return true;
  }

  return false;
}

std::optional<AgentPredictedSpeedProfileAndPath>
CalcAgentPredictedSpeedProfileAndPath(
    const AgentTrajectoryInLaneStates& agent_traj) {
  AgentPredictedSpeedProfileAndPath res;
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_predicted_path_pts;
  std::optional<math::geometry::Point2d> optional_last_pt;
  for (const auto& state : agent_traj.predicted_states) {
    const auto& pose = state.inlane_state.inlane_param.pose;
    if (optional_last_pt.has_value() &&
        math::NearZero(math::geometry::Distance(pose.center_2d(),
                                                optional_last_pt.value()))) {
      speed_profile.emplace_back(math::Ms2Sec(state.timestamp), state.odom_m,
                                 /*v_in=*/0.0,
                                 /*a_in=*/0.0, /*j_in=*/0.0);
      continue;
    }
    optional_last_pt = std::make_optional(pose.center_2d());
    speed_profile.emplace_back(math::Ms2Sec(state.timestamp), state.odom_m,
                               state.inlane_state.inlane_param.speed_mps,
                               /*a_in=*/0.0, /*j_in=*/0.0);
    agent_predicted_path_pts.emplace_back(pose.center_2d());
  }
  res.predicted_speed_profile = std::move(speed_profile);

  math::geometry::Polyline2d simplified_agent_predicted_path_pts =
      math::geometry::Simplify(std::move(agent_predicted_path_pts),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_predicted_path_pts.size() < 2) {
    return std::nullopt;
  }
  res.predicted_path = math::geometry::PolylineCurve2d(
      std::move(simplified_agent_predicted_path_pts));

  return std::make_optional<AgentPredictedSpeedProfileAndPath>(res);
}

std::optional<AgentPredictedSpeedProfileAndPath>
CalcAgentPredictedSpeedProfileAndPath(
    const ObjectTrajectoryStates& agent_traj) {
  AgentPredictedSpeedProfileAndPath res;
  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_predicted_path_pts;
  std::optional<math::geometry::Point2d> optional_last_pt;
  for (const auto& state : agent_traj.predicted_states()) {
    const auto& pose = state.pose();
    DCHECK(state.odom_m().has_value());
    if (optional_last_pt.has_value() &&
        math::NearZero(math::geometry::Distance(pose.center_2d(),
                                                optional_last_pt.value()))) {
      speed_profile.emplace_back(math::Ms2Sec(state.timestamp()),
                                 state.odom_m().value(),
                                 /*v_in=*/0.0,
                                 /*a_in=*/0.0, /*j_in=*/0.0);
      continue;
    }
    optional_last_pt = std::make_optional(pose.center_2d());
    speed_profile.emplace_back(math::Ms2Sec(state.timestamp()),
                               state.odom_m().value(),
                               state.object_occupancy_param().speed_mps,
                               /*a_in=*/0.0, /*j_in=*/0.0);
    agent_predicted_path_pts.emplace_back(pose.center_2d());
  }
  res.predicted_speed_profile = std::move(speed_profile);

  math::geometry::Polyline2d simplified_agent_predicted_path_pts =
      math::geometry::Simplify(std::move(agent_predicted_path_pts),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_predicted_path_pts.size() < 2) {
    return std::nullopt;
  }
  res.predicted_path = math::geometry::PolylineCurve2d(
      std::move(simplified_agent_predicted_path_pts));

  return std::make_optional<AgentPredictedSpeedProfileAndPath>(res);
}
// Adds fod_intention to IntentionResultMetadata given the lateral decision.
void AddFoDIntentionToIntentionResultMetadata(
    const std::set<pb::IntentionResult::FoDIntention>& fod_intentions,
    IntentionResultMetaData& intention_result_metadata) {
  if (fod_intentions.empty()) {
    return;
  }
  if (HasFodIntention(fod_intentions, pb::IntentionResult::NOT_APPLICABLE)) {
    return;
  }
  // Should only work for static objects.
  if (!intention_result_metadata.is_static) {
    return;
  }
  // Drive_through and undercarriage_over are added for objects we want to
  // ignore.
  if (intention_result_metadata.lateral_decision ==
      pb::SnapshotIntention::IGNORE) {
    if (HasFodIntention(fod_intentions, pb::IntentionResult::DRIVE_THROUGH)) {
      intention_result_metadata.fod_intention =
          pb::IntentionResult::DRIVE_THROUGH;
    } else if (HasFodIntention(fod_intentions,
                               pb::IntentionResult::UNDERCARRIAGE_OVER)) {
      intention_result_metadata.fod_intention =
          pb::IntentionResult::UNDERCARRIAGE_OVER;
    }
    return;
  }
  // Nudge intention is only added for objects decided to be nudged.
  if (intention_result_metadata.lateral_decision !=
          pb::SnapshotIntention::IGNORE &&
      HasFodIntention(fod_intentions, pb::IntentionResult::NUDGE)) {
    intention_result_metadata.fod_intention = pb::IntentionResult::NUDGE;
    return;
  }
  // Yield intention is added if we do not want to overtake the object.
  if (!intention_result_metadata.is_overtaken &&
      HasFodIntention(fod_intentions, pb::IntentionResult::YIELD)) {
    intention_result_metadata.fod_intention = pb::IntentionResult::YIELD;
  }
}

// Adds extra needs to be considered objects' intention to
// IntentionResultMetadataMap. Currently, this function is used for adding
// drivable FOD intentions that are ignored by tunnel search.
void AddExtraNeedsToBeConsideredIntention(
    const std::map<TypedObjectId, ObjectIntentionInfo>&
        object_intention_info_map,
    std::map<TypedObjectId, IntentionResultMetaData>& intention_meta_data_map) {
  // Add FOD intention with Ignore Trajectory Intention.
  for (const auto& [typed_object_id, object_intention_info] :
       object_intention_info_map) {
    auto intention_metadata_iter =
        intention_meta_data_map.find(typed_object_id);
    const auto& fod_intentions =
        object_intention_info.trajectory_intention_data.fod_intentions;
    if (intention_metadata_iter == intention_meta_data_map.end() &&
        (HasFodIntention(fod_intentions, pb::IntentionResult::DRIVE_THROUGH) ||
         HasFodIntention(fod_intentions,
                         pb::IntentionResult::UNDERCARRIAGE_OVER))) {
      IntentionResultMetaData intention_result_metadata(
          typed_object_id, /*blocking_state_size=*/-1,
          /*is_static=*/true, /*is_overtaken=*/true,
          /*lateral_decision=*/pb::SnapshotIntention::IGNORE,
          /*lon_decision=*/speed::pb::SpeedDecision::NOT_DECIDED,
          /*nudge_index_ranges=*/{}, /*required_lat_gap=*/
          object_intention_info.trajectory_intention_data.required_lat_gap);
      AddFoDIntentionToIntentionResultMetadata(fod_intentions,
                                               intention_result_metadata);
      intention_meta_data_map.emplace(typed_object_id,
                                      intention_result_metadata);
    }
  }
}
}  // namespace path
}  // namespace planner
