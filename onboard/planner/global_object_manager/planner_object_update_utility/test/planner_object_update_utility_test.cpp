#include "planner/global_object_manager/planner_object_update_utility/planner_object_update_utility.h"

#include <glog/logging.h>
#include <gtest/gtest.h>

#include "planner/global_object_manager/objects_evaluation_utility/objects_evaluation_utility.h"
#include "planner/global_object_manager/prediction_trajectory_update_utility/prediction_trajectory_update_utility.h"
#include "planner/global_object_manager/test/global_object_manager_test_utility_fixture.h"

namespace planner {

class PlannerObjectUpdateUtilityTest
    : public ::testing::Test,
      public assist::GlobalObjectManagerTestUtilityFixture {};

TEST_F(PlannerObjectUpdateUtilityTest, UpdatePlannerObject) {
  std::unordered_set<TypedObjectId> irrelevant_object_ids;
  std::unordered_set<TypedObjectId> stationary_object_ids;
  pb::PlannerObjectDebug planner_object_debug;
  bool has_off_road_objects{false};
  bool has_path_recently_go_offroad{false};

  TbbPredictionTrajectoryMap object_prediction_map;
  TbbConditionalPredictionTrajectoryMap object_conditional_prediction_map;

  utility::AddIgnoreObjectIdsToIrrelevantObjectIds(
      agent_list_, perception_fp_obstacle_tracker_, &irrelevant_object_ids,
      &planner_object_debug);

  utility::GetStationaryObjectIds(
      agent_list_,
      planner_object_config_.pedestrian_near_stationary_speed_in_mps(),
      planner_object_config_.default_agent_near_stationary_speed_in_mps(),
      &stationary_object_ids);

  utility::AddOffroadObjectIdsToIrrelevantObjectIds(
      snapshot_timestamp_, agent_list_, ego_offroad_info_seed_,
      stationary_object_ids, distance_to_destination_m_, &has_off_road_objects,
      &has_path_recently_go_offroad, &irrelevant_object_ids,
      &planner_object_debug);

  utility::UpdatePredictionTrajectoryMap(
      snapshot_timestamp_, agent_list_, irrelevant_object_ids,
      &object_prediction_map, &object_conditional_prediction_map,
      destruction_background_pool_.get());

  auto planner_object_map_ptr = std::make_shared<PlannerObjectMap>();

  utility::UpdatePlannerObjectMap(
      agent_list_, stationary_object_ids, irrelevant_object_ids, robot_state_,
      *map_test_util_.pnc_map_service(), object_type_tracking_map_, {},
      object_prediction_map, planner_objects_history_,
      planner_object_map_ptr.get(), &planner_object_debug);

  EXPECT_EQ(agent_list_.agent_list_size() - irrelevant_object_ids.size(),
            planner_object_map_ptr->size());

  // Basic existence checks
  EXPECT_TRUE(planner_object_map_ptr->count(211));   // open-door vehicle
  EXPECT_TRUE(planner_object_map_ptr->count(213));   // stationary ped
  EXPECT_TRUE(planner_object_map_ptr->count(301));   // not in junction
  EXPECT_TRUE(planner_object_map_ptr->count(302));   // vegetation
  EXPECT_FALSE(planner_object_map_ptr->count(101));  // ignored VEHICLE
  EXPECT_FALSE(planner_object_map_ptr->count(102));  // ignored CYCLIST
  EXPECT_FALSE(planner_object_map_ptr->count(201));  // off-road cone
  EXPECT_FALSE(planner_object_map_ptr->count(202));  // off-road unknown
  EXPECT_FALSE(planner_object_map_ptr->count(203));  // off-road barrier

  const auto& obj_301 = planner_object_map_ptr->at(301);
  EXPECT_FALSE(obj_301.is_in_junction());

  const auto& obj_302 = planner_object_map_ptr->at(302);
  EXPECT_EQ(obj_302.object_type(), voy::perception::ObjectType::UNKNOWN);
  EXPECT_TRUE(obj_302.is_vegetation());

  const auto& obj_211 = planner_object_map_ptr->at(211);
  EXPECT_TRUE(obj_211.is_vehicle());
  EXPECT_TRUE(obj_211.is_open_door_vehicle());

  const auto& obj_213 = planner_object_map_ptr->at(213);
  EXPECT_TRUE(obj_213.is_stationary());
  EXPECT_EQ(obj_213.object_type(), voy::perception::ObjectType::PED);

  // verify debug info
  const auto& debug_map = planner_object_debug.per_planner_obj_debug();
  for (const auto& [object_id, planner_object] : *planner_object_map_ptr) {
    EXPECT_TRUE(debug_map.contains(object_id));
  }
}

}  // namespace planner
