#include "planner/decoupled_maneuvers/lane_change/lane_change_common.h"

#include <algorithm>
#include <cmath>
#include <cstddef>
#include <iterator>
#include <limits>
#include <optional>
#include <sstream>
#include <string>
#include <tbb/parallel_for.h>
#include <unordered_set>
#include <vector>

#include <boost/scope_exit.hpp>
#include <glog/logging.h>

#include <tbb/parallel_for.h>
#include <tbb/task_arena.h>

#include "geometry/model/polyline_curve.h"
#include "latency/latency_stat.h"
#include "log_utils/log_macros.h"
#include "math/angle.h"
#include "math/constants.h"
#include "math/curve2d_util.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "planner/behavior/util/lane_common/lane_change_instance.h"
#include "planner/behavior/util/lane_common/lane_follow_sequence_curve.h"
#include "planner/behavior/util/lane_common/lane_sequence_iterator.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/behavior/util/lane_sequence/route_preview_utility.h"
#include "planner/behavior/util/pull_over/pull_over_utility.h"
#include "planner/decoupled_maneuvers/lane_change/early_lane_change_signal_reasoner.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common_util.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_info.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_object_info.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/world_model/pickup_dropoff_zone/pickup_dropoff_zone_info_util.h"
#include "planner/world_model/previous_action_recorder/previous_action_recorder.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/hazardous_state.pb.h"
#include "planner_protos/lane_change.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/pnc_map_service.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {
// We only start lc preparation when the lc start position is within this amount
// of distance from ego.
// TODO(Tingran): Revise this value or use dynamic value.
constexpr double kMaxDistBeforeLcStartPosToBeginPreparationInMeter = 80.0;
// We only start lc preparation when the lc start position is within this amount
// of distance from ego when ego is in junction.
constexpr double kMaxDistBeforeLcStartPosToBeginPreparationInJunctionInMeter =
    30.0;
constexpr double kTriggerCrawlFromEgoToDeadendInMeter = 30.0;
constexpr double kMaxSpeedToEnableCreepInMps = 3.0;
// The lane change duration with buffer in ms to consider if ego will overtake
// any agent.
constexpr int64_t kLaneChangeDurationWithBufferInMSec = 6000;

// Converts a math::Range1d to math::pb::Range.
math::pb::Range ConvertToMathPbRange(const math::Range1d& range) {
  math::pb::Range range_pb;
  range_pb.set_start_pos(range.start_pos);
  range_pb.set_end_pos(range.end_pos);
  return range_pb;
}

// Gets ego closest lane on target region.
const pnc_map::Lane* GetEgoClosestLaneOnTargetRegion(
    const std::vector<const pnc_map::Lane*>& target_lane_sequence,
    const double ego_ra_arc_length_on_target_region) {
  double distance_from_region_start_to_lane_end = 0.0;
  for (const pnc_map::Lane* lane : target_lane_sequence) {
    distance_from_region_start_to_lane_end += lane->length();
    if (distance_from_region_start_to_lane_end >
        ego_ra_arc_length_on_target_region) {
      return lane;
    }
  }

  return nullptr;
}
}  // namespace

bool HasLaneChangeGapValidityChange(
    const pb::LaneChangeState curr_lane_change_state,
    const pb::LaneChangeState prev_lane_change_state,
    const speed::pb::SpeedSolverLcGuideSeed& selected_lc_guide_seed,
    const google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>&
        last_lc_guide_seeds,
    std::string& gap_validity_change_str) {
  DCHECK_LE(last_lc_guide_seeds.size(), 1);

  // Return false if the lc guide seeds are empty.
  if (last_lc_guide_seeds.empty()) {
    return false;
  }

  const speed::pb::SpeedSolverLcGuideSeed& last_lc_guide_seed =
      last_lc_guide_seeds[0];

  // Return true if the gap is initiated when the selected_lc_guide_seed is
  // valid and the last_lc_guide_seed is invalid, and when the current lane
  // change state is PREPARATION.
  if (curr_lane_change_state ==
          pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION &&
      selected_lc_guide_seed.is_valid() && !last_lc_guide_seed.is_valid()) {
    gap_validity_change_str = "GAP_INITIATED";
    return true;
  }

  // Check if the gap is disappeared when the selected_lc_guide_seed is invalid
  // and the last_lc_guide_seed is valid, and when both the current lane change
  // state and the previous lane change state are PREPARATION.
  if (curr_lane_change_state ==
          pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION &&
      prev_lane_change_state ==
          pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION &&
      !selected_lc_guide_seed.is_valid() && last_lc_guide_seed.is_valid()) {
    gap_validity_change_str = "GAP_DISAPPEARED";
    return true;
  }

  // NOTE(Tingran): Note that we do also do gap align in lane change abort
  // state. For now we do not need to scope it for gap stability.

  return false;
}

bool HasLaneChangeGapObjectChange(
    const speed::pb::SpeedSolverLcGuideSeed& selected_lc_guide_seed,
    const google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>&
        last_lc_guide_seeds,
    std::string& gap_object_change_str) {
  DCHECK_LE(last_lc_guide_seeds.size(), 1);

  // Return false if the lc guide seeds are empty.
  if (last_lc_guide_seeds.empty()) {
    return false;
  }

  const speed::pb::SpeedSolverLcGuideSeed& last_lc_guide_seed =
      last_lc_guide_seeds[0];

  // Return false if one of the lc guide seed is invalid.
  if (!selected_lc_guide_seed.is_valid() || !last_lc_guide_seed.is_valid()) {
    return false;
  }

  const bool is_lead_object_matching = (selected_lc_guide_seed.lead_obj_id() ==
                                        last_lc_guide_seed.lead_obj_id());
  const bool is_tail_object_matching = (selected_lc_guide_seed.tail_obj_id() ==
                                        last_lc_guide_seed.tail_obj_id());

  // Return false if the lead object id and tail object id are matched.
  if (is_lead_object_matching && is_tail_object_matching) {
    return false;
  }

  // Gap object change is found.
  if (!is_lead_object_matching && is_tail_object_matching) {
    gap_object_change_str = "GAP_LEAD_OBJECT_CHANGE";
  } else if (is_lead_object_matching && !is_tail_object_matching) {
    gap_object_change_str = "GAP_TAIL_OBJECT_CHANGE";
  } else {
    gap_object_change_str = "GAP_OBJECTS_CHANGE";
  }
  return true;
}

LaneChangeGeometryMetaData::LaneChangeGeometryMetaData(
    const pb::LaneChangeGeometryMetaData& lane_change_geom_meta)
    : base_meta_data(lane_change_geom_meta.cross_lane_geom_meta()),
      cross_lane_start_arc_length_on_source_region(
          lane_change_geom_meta.cross_lane_start_arc_length_on_source_region()),
      cross_lane_start_pt_on_extended_source_lane_seq(
          lane_change_geom_meta
              .cross_lane_start_pt_on_extended_source_lane_seq()
              .x(),
          lane_change_geom_meta
              .cross_lane_start_pt_on_extended_source_lane_seq()
              .y()),
      feasible_range_end_arc_length_on_source_lane_seq(
          lane_change_geom_meta
              .feasible_range_end_arc_length_on_source_lane_seq()),
      feasible_range_end_pt_on_source_lane_seq(
          lane_change_geom_meta.feasible_range_end_pt_on_source_lane_seq().x(),
          lane_change_geom_meta.feasible_range_end_pt_on_source_lane_seq().y()),
      latest_cross_lane_end_arc_length_on_source_lane_seq(
          lane_change_geom_meta
              .latest_cross_lane_end_arc_length_on_source_lane_seq()),
      latest_cross_lane_end_pt_on_source_lane_seq(
          lane_change_geom_meta.latest_cross_lane_end_pt_on_source_lane_seq()
              .x(),
          lane_change_geom_meta.latest_cross_lane_end_pt_on_source_lane_seq()
              .y()),
      actual_cross_lane_end_arc_length_on_target_lane_seq(
          lane_change_geom_meta
              .actual_cross_lane_end_arc_length_on_target_lane_seq()),
      actual_cross_lane_end_pt_on_target_lane_seq(
          lane_change_geom_meta.actual_cross_lane_end_pt_on_target_lane_seq()
              .x(),
          lane_change_geom_meta.actual_cross_lane_end_pt_on_target_lane_seq()
              .y()) {
  cross_lane_marking_curve =
      PolylineCurveFromProto(lane_change_geom_meta.cross_lane_marking_curve());
}

pb::LaneChangeGeometryMetaData LaneChangeGeometryMetaData::ToProto() const {
  pb::LaneChangeGeometryMetaData pb_lane_change_geom_meta;
  *pb_lane_change_geom_meta.mutable_cross_lane_geom_meta() =
      base_meta_data.ToProto();

  math::geometry::detail::Convert<
      math::geometry::Polyline<math::geometry::Point2d>,
      ::google::protobuf::RepeatedPtrField<voy::Point2d>>::
      Apply(cross_lane_marking_curve.points(),
            *pb_lane_change_geom_meta.mutable_cross_lane_marking_curve()
                 ->mutable_points());

  pb_lane_change_geom_meta.set_cross_lane_start_arc_length_on_source_region(
      cross_lane_start_arc_length_on_source_region);
  math::geometry::detail::Convert<math::geometry::Point2d, voy::Point2d>::Apply(
      cross_lane_start_pt_on_extended_source_lane_seq,
      *pb_lane_change_geom_meta
           .mutable_cross_lane_start_pt_on_extended_source_lane_seq());

  pb_lane_change_geom_meta.set_feasible_range_end_arc_length_on_source_lane_seq(
      feasible_range_end_arc_length_on_source_lane_seq);
  math::geometry::detail::Convert<math::geometry::Point2d, voy::Point2d>::Apply(
      feasible_range_end_pt_on_source_lane_seq,
      *pb_lane_change_geom_meta
           .mutable_feasible_range_end_pt_on_source_lane_seq());

  pb_lane_change_geom_meta
      .set_latest_cross_lane_end_arc_length_on_source_lane_seq(
          latest_cross_lane_end_arc_length_on_source_lane_seq);
  math::geometry::detail::Convert<math::geometry::Point2d, voy::Point2d>::Apply(
      latest_cross_lane_end_pt_on_source_lane_seq,
      *pb_lane_change_geom_meta
           .mutable_latest_cross_lane_end_pt_on_source_lane_seq());

  pb_lane_change_geom_meta
      .set_actual_cross_lane_end_arc_length_on_target_lane_seq(
          actual_cross_lane_end_arc_length_on_target_lane_seq);
  math::geometry::detail::Convert<math::geometry::Point2d, voy::Point2d>::Apply(
      actual_cross_lane_end_pt_on_target_lane_seq,
      *pb_lane_change_geom_meta
           .mutable_actual_cross_lane_end_pt_on_target_lane_seq());

  for (auto agent_id : ignore_agent_ids_during_lane_change) {
    pb_lane_change_geom_meta.add_ignore_agent_ids_during_lane_change(agent_id);
  }

  pb_lane_change_geom_meta
      .set_is_ego_last_trajectory_on_or_beyond_cross_lane_curve(
          is_ego_last_trajectory_on_or_beyond_cross_lane_curve);
  pb_lane_change_geom_meta
      .set_is_ego_last_trajectory_on_or_beyond_solid_lane_marking(
          is_ego_last_trajectory_on_or_beyond_solid_lane_marking);
  pb_lane_change_geom_meta
      .set_ego_last_trajectory_max_encroachment_on_cross_lane_curve(
          ego_last_trajectory_max_encroachment_on_cross_lane_curve);

  return pb_lane_change_geom_meta;
}

pb::LaneChangeLongitudinalSpanOptionsDebug
LaneChangeLongitudinalSpanOptions::ToProto() const {
  pb::LaneChangeLongitudinalSpanOptionsDebug lc_options_debug;
  *lc_options_debug.mutable_feasible_lc_range() =
      ConvertToMathPbRange(feasible_lc_range);
  for (const auto& [lc_option_type, lc_option_range] : lc_options_map) {
    (*lc_options_debug.mutable_lc_options_map())
        [pb::LaneChangeLongitudinalSpanOptionsDebug::LcOptionType_Name(
            lc_option_type)] = ConvertToMathPbRange(lc_option_range);
  }
  lc_options_debug.set_selected_lc_option_type(selected_lc_option_type);
  return lc_options_debug;
}

pb::LaneChangeSelectedGapInfoDebug LaneChangeSelectedGapInfo::ToProto() const {
  pb::LaneChangeSelectedGapInfoDebug lc_selected_gap_info_debug;
  lc_selected_gap_info_debug.set_selected_lc_range_and_option_ix(
      selected_lc_range_and_option_ix);
  for (const LaneChangeLongitudinalSpanOptions& lc_ranges_and_options :
       feasible_lc_ranges_and_options) {
    *lc_selected_gap_info_debug.add_feasible_lc_ranges_and_options() =
        lc_ranges_and_options.ToProto();
  }
  return lc_selected_gap_info_debug;
}

bool ShouldLatchStatesDuringLaneChange(
    const pb::LaneChangeState& lane_change_state) {
  return lane_change_state ==
         pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;
}

bool ShouldRunLaneChangeGapAlign(const LaneChangeInfo& lane_change_info,
                                 const pb::LaneChangeState& lane_change_state) {
  const bool is_lc_signal_from_seed =
      lane_change_info.signal_source_type ==
      pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed;
  const bool is_gap_align_state =
      lane_change_state ==
          planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION ||
      lane_change_state ==
          planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT;
  return !is_lc_signal_from_seed && is_gap_align_state;
}

bool ShouldRunLaneChangePath(
    const pnc_map::Lane& current_lane, const LaneChangeInfo& lane_change_info,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::optional<speed::pb::SpeedSolverLcGuideSeed>&
        last_selected_lc_guide_seed,
    const pb::LaneChangeState last_selected_lane_change_state) {
  if (lane_change_info.lane_change_info_level == pb::kLightLaneChangeInfo) {
    LOG(INFO) << "[lane change][state]Got light lane change info.";
    return false;
  }
  const LaneChangeInstance& lane_change_instance =
      lane_change_info.lane_change_instance;
  if (IsInvalidLaneChangeInstance(lane_change_instance)) {
    LOG(INFO) << "[lane change][state]Invalid lane change instance.";
    return false;
  }
  // Returns false if ego is NOT in lane change source lane sequence or target
  // lane sequence.
  if (!lane_change_instance.IsInLaneChangeExtendedSourceOrTargetLaneSequence(
          &current_lane)) {
    LOG(INFO) << "[lane change][state] Current lane is not in extended lane "
                 "sequence.";
    return false;
  }

  // TODO(lane change): Instead of not running lane change path. Consider clear
  // the lane change seed or don't use it, so that lane change path can still
  // generate a non seed reliant path if possible.
  if (last_selected_lc_guide_seed.has_value()) {
    const pb::LaneChangeMode last_selected_lc_direction =
        last_selected_lc_guide_seed.value().lc_direction();
    const pb::LaneChangeMode current_lc_direction =
        lane_change_info.lane_change_instance.direction();
    if (last_selected_lc_direction != pb::LaneChangeMode::NONE_LANE_CHANGE &&
        current_lc_direction != pb::LaneChangeMode::NONE_LANE_CHANGE &&
        last_selected_lc_direction != current_lc_direction) {
      LOG(INFO) << "[lane change][state] Have different lane change direction.";
      return false;
    }
  }

  const LaneChangeRegionInfo& target_region_info =
      *DCHECK_NOTNULL(GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos()));
  const bool has_queue = target_region_info.HasQueue();
  // Return false if all conditions are met:
  // 1. No queue is found.
  // 2. last selected state is NOT IN_PROGRESS or ABORT.
  // 3. Ego is not near or past the start arclength.
  if (!has_queue &&
      last_selected_lane_change_state !=
          pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS &&
      last_selected_lane_change_state !=
          pb::LaneChangeState::LANE_CHANGE_STATE_ABORT &&
      !lane_change_instance.IsEgoNearOrPastStartArclength(
          robot_state_snapshot, /*extra_time_in_sec=*/0.0)) {
    LOG(INFO) << "[lane change][state] Should not run lane change path.";
    return false;
  }

  return true;
}

bool ShouldStartLaneChangeTurnSignal(
    const RobotStateSnapshot& robot_state_snapshot,
    const LaneChangeStatusForSpeed& lane_change_status,
    const LaneChangeInfo& lane_change_info) {
  if (lane_change_status.lane_change_state !=
          planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION &&
      lane_change_status.lane_change_state !=
          planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    return false;
  }

  const LaneChangeRegionInfo& target_region_info =
      *DCHECK_NOTNULL(GetLaneChangeRegionInfoPtrByType(
          /*region_type=*/pb::LaneChangeRegionType::TARGET,
          lane_change_info.region_infos()));

  constexpr double kTurnSignalDurationBeforePathGenerationInSec = 3.0;
  return target_region_info.HasQueue() ||
         lane_change_info.lane_change_instance.IsEgoNearOrPastStartArclength(
             robot_state_snapshot,
             /*extra_time_in_sec=*/
             kTurnSignalDurationBeforePathGenerationInSec);
}

double GetTurnSignalDurationForLaneChange(
    const planner::pb::LaneChangeMode lane_change_direction,
    const TurnSignalState turn_signal_state) {
  // TODO(reasoning): revise the case during left/right turn.
  // TODO(reasoning): maybe include U turn as well;
  if ((lane_change_direction == planner::pb::LaneChangeMode::LEFT_LANE_CHANGE &&
       turn_signal_state.turn_mode == pb::TurnMode::LEFT_TURN) ||
      (lane_change_direction ==
           planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE &&
       turn_signal_state.turn_mode == pb::TurnMode::RIGHT_TURN)) {
    return turn_signal_state.duration;
  }
  return 0.0;
}

const LaneSequenceResult* GetLaneSequenceProposalResult(
    const std::vector<const LaneSequenceResult*>&
        all_lane_change_sequence_results,
    const pb::LaneSequenceLatchSignal& lane_change_sequence_proposal) {
  if (lane_change_sequence_proposal.prefixes().empty()) {
    return nullptr;
  }

  const int32_t prefix_id = lane_change_sequence_proposal.prefixes().at(0).id();
  for (const LaneSequenceResult* result : all_lane_change_sequence_results) {
    DCHECK(result != nullptr);
    if (result->prefix_ids.find(prefix_id) != result->prefix_ids.end()) {
      return result;
    }
  }
  return nullptr;
}

LaneChangeInstance GetLaneChangeInstance(
    const LaneSequenceResult* lane_seq_result,
    const math::geometry::Point2d& plan_init_ra,
    const bool should_latch_lane_change_geom,
    const bool is_lc_backup_sequence) {
  if (lane_seq_result == nullptr ||
      lane_seq_result->lane_change_instances.empty()) {
    return LaneChangeInstance();
  }

  if (should_latch_lane_change_geom) {
    // When latching, return the first lane change instance.
    return lane_seq_result->lane_change_instances.front();
  }

  // Return first lane change instance if this is a lane change backup sequence.
  if (is_lc_backup_sequence) {
    return lane_seq_result->lane_change_instances.front();
  }

  const LaneSequenceFeatures features =
      LaneSequenceIterator::ComputeLaneSequenceFeaturesBeforeJunction(
          *lane_seq_result, plan_init_ra,
          /*skip_going_straight_junction=*/false,
          kMaxDistBeforeLcStartPosToBeginPreparationInMeter);
  // The lane change instances are ordered based on the distance to ego.
  // Currently we only need to consider the closest / first lane change
  // instance.
  if (features.lane_change_instances_before_query_end.empty()) {
    return LaneChangeInstance();
  }
  return features.lane_change_instances_before_query_end[0];
}

LaneChangeInstance PopulateLaneChangeInstanceFromSeed(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pb::LaneChangeInstance& lane_change_instance_pb,
    const std::vector<const pnc_map::Lane*>& lane_change_sequence) {
  if (lane_change_sequence.empty()) {
    return {};
  }
  const std::vector<const pnc_map::Lane*> lanes =
      joint_pnc_map_service.GetLaneSequence(
          std::vector<int64_t>({lane_change_instance_pb.id().src_lane_id(),
                                lane_change_instance_pb.id().dst_lane_id()}));
  const pnc_map::Lane* source_lane = lanes.front();
  const pnc_map::Lane* target_lane = lanes.back();

  LaneChangeInstance lane_change_instance(
      lane_change_instance_pb.lane_change_direction(),
      lane_change_instance_pb.lc_start_arc_length(), source_lane, target_lane);
  lane_change_instance.set_urgency_type(lane_change_instance_pb.urgency_type());
  lane_change_instance.set_instance_info(
      lane_change_instance_pb.instance_info());
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      drivable_lane_reasoner, lane_change_sequence);
  return lane_change_instance;
}

void SelectLaneChangeSequence(
    const RobotState& robot_state,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const LaneSequenceResult*& lane_sequence_w_lane_change,
    std::set<pb::LaneSequencePrefix::Source>& lc_sequence_prefix_sources,
    bool& is_lc_backup_sequence, bool& is_early_lc_sequence,
    std::ostringstream& debug_oss) {
  // Get lane change sequence which could be an alternative lane change.
  lane_sequence_w_lane_change =
      FLAGS_planning_enable_lane_sequence_proposal_for_lane_change
          ? GetLaneSequenceResultWithLaneChange(
                lane_sequence_candidates, previous_iter_seed,
                robot_state.plan_init_state_snapshot(),
                /*favor_immediate_lc_over_optimal_lc=*/true)
          : lane_sequence_candidates
                .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
                    /*favor_immediate_lc_over_optimal_lc=*/true);

  lc_sequence_prefix_sources = GetLaneSequencePrefixSources(
      lane_sequence_w_lane_change, previous_iter_seed);
  is_lc_backup_sequence = lc_sequence_prefix_sources.find(
                              pb::LaneSequencePrefix_Source_LC_BACKUP) !=
                          lc_sequence_prefix_sources.end();
  is_early_lc_sequence =
      lc_sequence_prefix_sources.find(pb::LaneSequencePrefix_Source_LC_EARLY) !=
      lc_sequence_prefix_sources.end();
  const math::geometry::Point2d& ego_ra_position =
      robot_state.plan_init_state_snapshot().rear_axle_position();
  if (!is_early_lc_sequence) {
    return;
  }
  // For an early lane change sequence, check if there is other non-alternative
  // lane change sequence can be executed. Pick non-alternative lane change
  // sequence instead if so.
  const LaneSequenceResult* non_alter_lc_sequence =
      lane_sequence_candidates
          .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
              /*favor_immediate_lc_over_optimal_lc=*/true);
  const std::set<pb::LaneSequencePrefix::Source>
      non_alter_lc_sequence_prefix_sources = GetLaneSequencePrefixSources(
          non_alter_lc_sequence, previous_iter_seed);
  const bool is_non_alter_lc_sequence_backup =
      lc_sequence_prefix_sources.find(
          pb::LaneSequencePrefix_Source_LC_BACKUP) !=
      lc_sequence_prefix_sources.end();
  LaneChangeInstance non_alter_lane_change_instance =
      GetLaneChangeInstance(non_alter_lc_sequence, ego_ra_position,
                            previous_iter_seed.should_latch_lane_change_geom(),
                            is_non_alter_lc_sequence_backup);
  if (non_alter_lane_change_instance.direction() != pb::NONE_LANE_CHANGE) {
    debug_oss << "Replace early lane change sequence to non-alternative lane "
                 "change sequence.";
    lane_sequence_w_lane_change = non_alter_lc_sequence;
    lc_sequence_prefix_sources =
        std::move(non_alter_lc_sequence_prefix_sources);
    is_lc_backup_sequence = is_non_alter_lc_sequence_backup;
    is_early_lc_sequence = false;
  }
}

pb::LaneChangeSignalSourceType GetLaneChangeSignal(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const RobotState& robot_state,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const LaneSequenceResult*& lane_sequence_w_lane_change,
    std::vector<const pnc_map::Lane*>& lc_sequence,
    pb::LaneSequenceCandidate::LaneSequenceType& lc_sequence_type,
    std::set<pb::LaneSequencePrefix::Source>& lc_sequence_prefix_sources,
    bool& is_lc_backup_sequence, bool& is_early_lc_sequence,
    bool trigger_right_lane_change_for_immediate_pullover,
    LaneChangeInstance& lc_instance,
    pb::PlanningSegmentSequence& lc_planning_segment_sequence,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    std::ostringstream& debug_oss) {
  SelectLaneChangeSequence(robot_state, lane_sequence_candidates,
                           previous_iter_seed, lane_sequence_w_lane_change,
                           lc_sequence_prefix_sources, is_lc_backup_sequence,
                           is_early_lc_sequence, debug_oss);
  const math::geometry::Point2d& ego_ra_position =
      robot_state.plan_init_state_snapshot().rear_axle_position();
  // Get the lane change instance from sequence.
  LaneChangeInstance lane_change_instance_from_sequence =
      GetLaneChangeInstance(lane_sequence_w_lane_change, ego_ra_position,
                            previous_iter_seed.should_latch_lane_change_geom(),
                            is_lc_backup_sequence);

  // If the lane change instance is not a lane change, we might want to load
  // from seed.
  bool should_get_signal_from_seed =
      lane_change_instance_from_sequence.direction() ==
          pb::LaneChangeMode::NONE_LANE_CHANGE &&
      previous_iter_seed.selected_lane_change_state() ==
          pb::LaneChangeState::LANE_CHANGE_STATE_ABORT;

  if (lane_sequence_w_lane_change == nullptr && !should_get_signal_from_seed) {
    return pb::LaneChangeSignalSourceType::kLaneChangeSignalNone;
  }

  if (lane_sequence_w_lane_change != nullptr && !should_get_signal_from_seed) {
    debug_oss << "\nGet signal from sequence.";
    lc_sequence = lane_sequence_w_lane_change->lane_sequence;
    lc_instance = std::move(lane_change_instance_from_sequence);
    lc_sequence_type = lane_sequence_w_lane_change->lane_sequence_type;
    lc_planning_segment_sequence =
        lane_sequence_w_lane_change->planning_segment_sequence;
    if (is_early_lc_sequence) {
      return pb::LaneChangeSignalSourceType::
          kLaneChangeSignalFromEarlyLaneChange;
    }

    if (lc_sequence_type ==
        pb::LaneSequenceCandidate::STUCK_AVOIDANCE_LANE_CHANGE) {
      return pb::LaneChangeSignalSourceType::
          kLaneChangeSignalFromStuckAvoidance;
    }

    if (lc_sequence_type ==
            pb::LaneSequenceCandidate::IMMEDIATE_RIGHT_LANE_CHANGE &&
        trigger_right_lane_change_for_immediate_pullover) {
      return pb::LaneChangeSignalSourceType::
          kLaneChangeSignalFromImmediatePullOver;
    }

    // For LC backup sequence, latch the source type.
    const pb::LaneChangeSignalSourceType&
        previous_lane_change_signal_source_type =
            previous_iter_seed.lane_change_info_seed()
                .lane_change_signal_source_type();
    if (is_lc_backup_sequence) {
      return previous_lane_change_signal_source_type;
    }

    if (!previous_iter_seed.selection_seed()
             .has_elective_lane_change_decision()) {
      return pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRouting;
    }

    // When is_triggered=true, trigger_type will have a value that's not
    // NONE_ELC. Here we write the latter as a if condition instead of a DCHECK
    // for old bags.
    const pb::ElectiveLaneChangeDecision& elc_decision =
        previous_iter_seed.selection_seed().elective_lane_change_decision();
    if (elc_decision.is_triggered() &&
        elc_decision.trigger_type() != pb::ElectiveLaneChangeType::NONE_ELC &&
        (lc_sequence_type ==
             pb::LaneSequenceCandidate::IMMEDIATE_LEFT_LANE_CHANGE ||
         lc_sequence_type ==
             pb::LaneSequenceCandidate::IMMEDIATE_RIGHT_LANE_CHANGE)) {
      switch (elc_decision.trigger_type()) {
        case pb::ElectiveLaneChangeType::PROGRESS_ELC:
          return pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromProgressELC;
        case pb::ElectiveLaneChangeType::RISK_AVOIDANCE_ELC:
          return pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromRiskAvoidanceELC;
        case pb::ElectiveLaneChangeType::MANUAL_ELC:
          return pb::LaneChangeSignalSourceType::kLaneChangeSignalFromManualELC;
        default:
          DCHECK(false) << "Unknown elective lane change type: "
                        << elc_decision.trigger_type();
      }
    }

    // Latch ELC signal type during in-progress.
    if (!previous_iter_seed.has_lane_change_info_seed() ||
        previous_iter_seed.selected_lane_change_state() !=
            pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
      return pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRouting;
    }

    if (previous_lane_change_signal_source_type ==
            pb::LaneChangeSignalSourceType::kLaneChangeSignalFromProgressELC ||
        previous_lane_change_signal_source_type ==
            pb::LaneChangeSignalSourceType::
                kLaneChangeSignalFromRiskAvoidanceELC ||
        previous_lane_change_signal_source_type ==
            pb::LaneChangeSignalSourceType::kLaneChangeSignalFromManualELC) {
      return previous_lane_change_signal_source_type;
    }

    return pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRouting;
  }

  if (!previous_iter_seed.has_lane_change_info_seed()) {
    debug_oss << "\nNo signal from either sequence or seed.";
    return pb::LaneChangeSignalSourceType::kLaneChangeSignalNone;
  }
  debug_oss << "\nGet signal from seed.";
  lc_sequence = joint_pnc_map_service.GetLaneSequence(
      previous_iter_seed.lane_change_info_seed().lane_change_sequence());
  lc_instance = PopulateLaneChangeInstanceFromSeed(
      joint_pnc_map_service, drivable_lane_reasoner,
      previous_iter_seed.lane_change_info_seed().lane_change_instance(),
      lc_sequence);
  lc_sequence_type =
      previous_iter_seed.lane_change_info_seed().lane_change_sequence_type();
  lc_planning_segment_sequence = previous_iter_seed.lane_change_info_seed()
                                     .lane_change_planning_segment_sequence();
  return pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed;
}

pb::LaneChangeState ComputeLaneChangeStateForHomotopy(
    const pb::LaneChangeState last_selected_lane_change_state,
    const bool is_current_homotopy_lane_change,
    const bool can_ego_finish_lane_change,
    const bool is_ego_fully_in_source_lane, const bool is_ego_slow,
    const bool is_lc_signal_from_seed, const bool should_latch_abort_state) {
  switch (last_selected_lane_change_state) {
    case pb::LaneChangeState::LANE_CHANGE_STATE_ABORT: {
      // If Ego is aborting the lane change, assign InProgress to the lane
      // change homotopy and abort to the others, except 1)when current lane
      // change can be finished, in which case we'll assign NONE. 2)when ego is
      // back to the source region or that it is slow, in which case we'll
      // assign PREPARATION and let it try again.
      if (is_current_homotopy_lane_change) {
        return pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS;
      }
      if (can_ego_finish_lane_change) {
        return pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
      }
      const bool can_exit_abort = (is_ego_fully_in_source_lane || is_ego_slow);
      if (can_exit_abort && !should_latch_abort_state) {
        return is_lc_signal_from_seed
                   ? pb::LaneChangeState::LANE_CHANGE_STATE_NONE
                   : pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
      }
      return pb::LaneChangeState::LANE_CHANGE_STATE_ABORT;
    }

    case pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS:
      // If Ego is performing lane change, assign InProgress to the
      // lane change homotopy and abort to the others.
      return is_current_homotopy_lane_change
                 ? (can_ego_finish_lane_change
                        ? pb::LaneChangeState::LANE_CHANGE_STATE_NONE
                        : pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS)
                 : pb::LaneChangeState::LANE_CHANGE_STATE_ABORT;
    // If Ego is not performing lane change, assign InProgress to the lane
    // change homotopy and preparation for the others. This will ask all the
    // other homotopies to prepare for the lane change.
    case pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION:
      return is_current_homotopy_lane_change
                 ? pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS
                 : pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    case pb::LaneChangeState::LANE_CHANGE_STATE_NONE:
      return pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION;
    default:
      DCHECK(false) << "Unhandled lane change state!";
      return pb::LaneChangeState::LANE_CHANGE_STATE_NONE;
  }
}

bool IsConsecutiveLaneChangeInSameDirection(
    const LaneChangeInstance& lane_change_instance,
    const pb::LaneChangeMetadata& lane_change_metadata,
    const std::vector<LaneChangeInstance>& all_lane_change_instances,
    const pb::LaneChangeSignalSourceType signal_source_type) {
  DCHECK(!lane_change_instance.IsDummy());
  DCHECK(lane_change_instance.direction() !=
         pb::LaneChangeMode::NONE_LANE_CHANGE);

  int64_t consecutive_lane_change_count =
      lane_change_metadata.consecutive_lane_change_count();
  if (consecutive_lane_change_count <= 1 ||
      all_lane_change_instances.size() <= 1) {
    return false;
  }

  const LaneChangeInstance& second_lane_change_instance =
      all_lane_change_instances.at(1);
  const bool has_opposite_direction_for_first_two_lc =
      (second_lane_change_instance.direction() !=
           pb::LaneChangeMode::NONE_LANE_CHANGE &&
       lane_change_instance.direction() !=
           second_lane_change_instance.direction());
  if (!has_opposite_direction_for_first_two_lc) {
    return true;
  }

  // A set of special lane change intentions which may cause excessive lane
  // changes on the sequence.
  static const std::unordered_set<pb::LaneChangeSignalSourceType>
      special_lane_change_intention_set{
          pb::LaneChangeSignalSourceType::kLaneChangeSignalFromEarlyLaneChange,
          pb::LaneChangeSignalSourceType::kLaneChangeSignalFromProgressELC,
          pb::LaneChangeSignalSourceType::kLaneChangeSignalFromRiskAvoidanceELC,
          pb::LaneChangeSignalSourceType::kLaneChangeSignalFromManualELC,
          pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromImmediatePullOver};
  // Handle the case where the first two lane changes are in the opposite
  // direction.
  // TODO(elliot): Compute the accurate lane change count.
  if (signal_source_type == pb::LaneChangeSignalSourceType::
                                kLaneChangeSignalFromEarlyLaneChange ||
      signal_source_type ==
          pb::LaneChangeSignalSourceType::kLaneChangeSignalFromProgressELC) {
    // Hack for the situation that routing gives excessive lane change when
    // triggering the early lane change or progress ELC.
    constexpr int64_t kMinConsecutiveLaneChangeCount = 1;
    consecutive_lane_change_count = std::max(kMinConsecutiveLaneChangeCount,
                                             consecutive_lane_change_count - 2);
    // The first two opposite lane changes are due to the special lane change
    // intention.
    return consecutive_lane_change_count > 1;
  }

  // For other special lane change intentions, leave unchanged.
  if (special_lane_change_intention_set.count(signal_source_type) > 0) {
    return consecutive_lane_change_count > 1;
  }

  // Normal lane change signal from routing. Cases where there are more than
  // two lane changes with the first two in the opposite direction should be
  // handled by the real-time route understanding.
  return consecutive_lane_change_count > 2;
}

bool IsDifficultLcForPullover(
    const pb::LaneChangeState& last_selected_lane_change_state,
    const LaneChangeInfo& lane_change_info, std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";
  // TODO(elliotsong): Need to consider difficulty during in-progress state.
  if (last_selected_lane_change_state ==
      pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    debug_oss << "Don't consider lane change state "
              << pb::LaneChangeState_Name(last_selected_lane_change_state);
    return false;
  }
  const std::optional<LaneChangeUrgencyInfo> destination_urgency_info =
      GetMostUrgencyInfoByUrgencyType(
          lane_change_info.urgency_infos(),
          pb::LaneChangeUrgencyType::DESTINATION_FOR_PULL_OVER);
  if (!destination_urgency_info.has_value()) {
    debug_oss << DUMP_TO_STREAM(destination_urgency_info.has_value());
    return false;
  }
  constexpr double kDistanceDifficultyScoreThresholdForPullover = 1.0;
  debug_oss << DUMP_TO_STREAM(
      lane_change_info.lane_change_env_info.distance_difficulty_score,
      kDistanceDifficultyScoreThresholdForPullover);
  const bool is_difficult_lc_for_pullover =
      lane_change_info.lane_change_env_info.distance_difficulty_score >
      kDistanceDifficultyScoreThresholdForPullover - math::constants::kEpsilon;
  if (is_difficult_lc_for_pullover) {
    rt_event::PostRtEvent<
        rt_event::planner::PlannerDifficultLaneChangeForPullover>();
  }
  return is_difficult_lc_for_pullover;
}

bool IsDifficultLcInTheNearFuture(
    const RobotState& ego_state, const double dist_to_nearest_object,
    const LaneChangeObjectInfoList& object_info_list) {
  // An empty lane should has no difficulty.
  constexpr double kMinInteractionDistInMeter = 5.0;
  if (dist_to_nearest_object > kMinInteractionDistInMeter ||
      object_info_list.non_leaving_ids().empty()) {
    return false;
  }

  const double ego_speed = ego_state.plan_init_state_snapshot().speed();
  const double rb_to_ra_dist = ego_state.car_model_with_shape()
                                   .shape_measurement()
                                   .rear_bumper_to_rear_axle();
  const double fb_to_ra_dist =
      ego_state.car_model_with_shape().shape_measurement().length() -
      ego_state.car_model_with_shape()
          .shape_measurement()
          .rear_bumper_to_rear_axle();

  // Define the interaction range for next lane change and check if the average
  // gap length is enough.
  constexpr double kMaxTimeForBehindInteractionInSec = 5.0;
  constexpr double kMaxDistForBehindInteractionInMeter = 20.0;
  constexpr double kMaxSpeedAsSlowMovingInMps = 5.0;
  const double behind_interaction_dist =
      std::max(ego_speed * kMaxTimeForBehindInteractionInSec,
               kMaxDistForBehindInteractionInMeter);
  double agent_count = 1;
  double last_agent_ego_dist = kMinInteractionDistInMeter;
  for (const int64_t object_id : object_info_list.non_leaving_ids()) {
    const LaneChangeObjectInfo* object_info = object_info_list.Find(object_id);
    if (object_info == nullptr) {
      continue;
    }
    const double agent_fb_to_ego_rb = object_info->object_ego_dist +
                                      0.5 * object_info->length + rb_to_ra_dist;
    const double agent_rb_to_ego_fb = object_info->object_ego_dist -
                                      0.5 * object_info->length - fb_to_ra_dist;

    // Do not consider agents too far behind.
    if (agent_fb_to_ego_rb < -behind_interaction_dist) {
      continue;
    }
    // Early break if this agent is slow moving or too far ahead.
    if (object_info->speed < kMaxSpeedAsSlowMovingInMps ||
        agent_rb_to_ego_fb > kMinInteractionDistInMeter) {
      last_agent_ego_dist = object_info->object_ego_dist;
      break;
    }
    agent_count++;
  }

  constexpr double kMinRequiredGapLengthInMeter = 5.0;
  double average_gap_length =
      (behind_interaction_dist + last_agent_ego_dist) / agent_count;
  return average_gap_length < kMinRequiredGapLengthInMeter;
}

bool ShouldRestrictLaneChangeByDifficulty(
    const pb::LaneChangeState& last_selected_lane_change_state,
    const LaneChangeInfo& lane_change_info, std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";

  // Return false if the signal source type is from the seed.
  if (lane_change_info.signal_source_type ==
      pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed) {
    debug_oss << "Signal source type is from seed.";
    return false;
  }

  DCHECK(last_selected_lane_change_state !=
         pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS);

  const pb::LaneChangeMetadata& lane_change_metadata =
      lane_change_info.lane_change_metadata;
  if (lane_change_info.lane_change_instance.direction() ==
          pb::LaneChangeMode::NONE_LANE_CHANGE ||
      lane_change_metadata.consecutive_lane_change_count() == 0) {
    debug_oss << "No need to consider non lane change.";
    return false;
  }

  // Currently we are not considering non-consecutive lane change.
  if (lane_change_metadata.consecutive_lane_change_count() == 1) {
    debug_oss << "Don't consider the difficulty of single lane change.";
    return false;
  }

  const LaneSequenceResult* lf_sequence =
      lane_change_info.lane_sequence_wo_lane_change;
  const std::optional<pb::LastLaneChangeChancePoint> last_lc_chance_point =
      (lf_sequence == nullptr ||
       !lf_sequence->cost_factors.has_last_lane_change_chance_point())
          ? std::nullopt
          : std::make_optional(
                lf_sequence->cost_factors.last_lane_change_chance_point());
  if (last_lc_chance_point.has_value() &&
      last_lc_chance_point->lane_change_reason() ==
          pb::LastLaneChangeChancePoint::BLOCKAGE) {
    debug_oss
        << "Don't consider the difficulty of blockage triggered lane change.";
    return false;
  }

  // Filter out the fork lane change.
  if (last_lc_chance_point.has_value() &&
      last_lc_chance_point->dist_from_ego() >
          lane_change_metadata
              .remaining_distance_for_consecutive_lane_change()) {
    debug_oss << "Don't consider the difficulty of non-last lane change.";
    return false;
  }

  // TODO(elliot): Should trigger rerouting or postponing the pull over point
  // when the difficulty score of consecutive lane change is high.
  const std::optional<LaneChangeUrgencyInfo> hard_urgency_info =
      GetMostUrgencyInfoByStopLineType(
          lane_change_info.urgency_infos(),
          pb::LaneChangeStopLineType::HARD_STOP_LINE);
  if (hard_urgency_info.has_value() &&
      hard_urgency_info.value().urgency_type() ==
          pb::LaneChangeUrgencyType::DESTINATION_FOR_PULL_OVER) {
    debug_oss << "Don't consider the difficulty of pulling over.";
    return false;
  }

  const std::optional<lane_selection::PreviewRouteResult>&
      preview_route_result = lane_change_info.preview_route_result;
  const std::optional<double> reroute_increasing_distance =
      FLAGS_planning_enable_use_can_reroute_flag_for_cons_lc
          ? lane_selection::GetRerouteIncreasingDistToDest(preview_route_result)
          : lane_selection::GetRerouteIncreasingDistToDestFromSequencePreview(
                preview_route_result);
  // Don't restrict if it doesn't have any preview route result.
  if (!preview_route_result.has_value() ||
      (!preview_route_result->on_route_preview_route_result.has_value() &&
       !reroute_increasing_distance.has_value())) {
    debug_oss << "Should not restrict cause no or empty preview route result.";
    return false;
  }

  // We dynamically determine the tolerance threshold of the difficulty score
  // based on the preview route.
  constexpr double kMaxTolDifficultyScore = 1.0;
  constexpr double kMinTolDifficultyScore = 0.8;
  double tol_difficulty_score = kMinTolDifficultyScore;
  // If has re_route preview result, we use the re_route increasing distance
  // to destination to compute the threshold. Otherwise it only has on_route
  // preview result and we will use the kMinTolDifficultyScore as the threshold.
  if (reroute_increasing_distance.has_value()) {
    debug_oss << "reroute_increasing_distance["
              << reroute_increasing_distance.value() << "]";
    constexpr double kMaxRerouteIncreasingDistanceInMeter = 3000;
    constexpr double kMinRerouteIncreasingDistanceInMeter = 400;
    tol_difficulty_score = math::GetLinearInterpolatedY(
        kMinRerouteIncreasingDistanceInMeter,
        kMaxRerouteIncreasingDistanceInMeter, kMinTolDifficultyScore,
        kMaxTolDifficultyScore, reroute_increasing_distance.value());
  }
  debug_oss << "difficulty_score["
            << lane_change_info.lane_change_env_info.difficulty_score
            << "]tol_difficulty_score[" << tol_difficulty_score << "]";
  const bool should_restrict =
      lane_change_info.lane_change_env_info.difficulty_score >
      tol_difficulty_score - math::constants::kEpsilon;

  if (!should_restrict) {
    debug_oss << "Should not restrict cause low difficulty score.";
    return false;
  }

  std::string rt_msg_str;
  rt_msg_str +=
      "Restrict lane change state from " +
      pb::LaneChangeState_Name(last_selected_lane_change_state) + " to " +
      pb::LaneChangeState_Name(pb::LaneChangeState::LANE_CHANGE_STATE_NONE);
  rt_event::PostRtEvent<rt_event::planner::PlannerConsLaneChangeRestrict>(
      rt_msg_str);
  debug_oss << rt_msg_str;
  return true;
}

bool ShouldRestrictLaneChange(
    const WorldModel& world_model, const LaneChangeInfo& lane_change_info,
    const pull_over::PullOverInfo& pull_over_info,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    std::ostringstream& debug_oss) {
  debug_oss << "\n " << __func__ << ": ";

  // Restrict LC if the ego is not in autonomous mode.
  if (!world_model.robot_state().IsInAutonomousMode()) {
    debug_oss << "Restrict LC when the ego is not in autonomous mode.";
    return true;
  }

  // Restrict LC if the waypoint assist is invoked.
  if (world_model.waypoint_assist_tracker().IsWaypointAssistInvoked()) {
    debug_oss << "Restrict LC when the waypoint assist is invoked.";
    return true;
  }

  // TODO(elliot): Only restrict early LC when it's necessary after reasoning.
  if (!IsEarlyLaneChangeExecutionEnable(world_model.order_start_time_info()) &&
      lane_change_info.is_early_lc_sequence) {
    debug_oss << "Restrict early LC based on configuration.";
    return true;
  }

  // Do not restrict lane change if the ego is in the lane change in progress
  // and abort state.
  const pb::LaneChangeState& last_selected_lane_change_state =
      previous_iter_seed.selected_lane_change_state();
  if (last_selected_lane_change_state ==
          pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS ||
      last_selected_lane_change_state ==
          pb::LaneChangeState::LANE_CHANGE_STATE_ABORT) {
    debug_oss << "Do not restrict LC when the ego's state is "
              << pb::LaneChangeState_Name(last_selected_lane_change_state)
              << ".";
    return false;
  }

  // Restrict LC if the mrc request (MRM_INLANE or MRM_URGENCY) exists.
  if (world_model.should_planner_respond_mrc() &&
      world_model.mrc_request_ptr() != nullptr &&
      (world_model.mrc_request_ptr()->mrm_type() == mrc::pb::MRM_INLANE ||
       world_model.mrc_request_ptr()->mrm_type() == mrc::pb::MRM_URGENCY)) {
    debug_oss << "Restrict LC when the mrc request exists.";
    return true;
  }

  // Restrict LC based on coordination info from pull over.
  if (pull_over_info.external_signals()
          .lc_coordination_info.IsLaneChangeDirectionRestricted(
              lane_change_info.lane_change_instance.direction())) {
    debug_oss << "Restrict "
              << pb::LaneChangeMode_Name(
                     lane_change_info.lane_change_instance.direction())
              << " based on pull over coordination info.";
    return true;
  }

  // Restrict LC if the lane change difficulty is large.
  if (ShouldRestrictLaneChangeByDifficulty(last_selected_lane_change_state,
                                           lane_change_info, debug_oss)) {
    return true;
  }

  return false;
}
void ComputeLaneChangeInProgressInfoForHomotopy(
    const pb::LaneChangeState current_lane_change_state,
    const int64_t plan_init_state_timestamp, const double turn_light_duration,
    const pb::LaneChangeState last_selected_lane_change_state,
    const std::optional<int64_t> last_in_progress_timestamp,
    const std::optional<double> last_turn_light_duration_before_in_progress,
    const std::optional<double> last_speed_discomfort_for_progress,
    std::optional<int64_t>& in_progress_timestamp,
    std::optional<double>& turn_light_duration_before_in_progress,
    std::optional<double>& speed_discomfort_for_progress) {
  if (current_lane_change_state !=
      pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    return;
  }

  if (last_selected_lane_change_state !=
      pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    in_progress_timestamp = std::make_optional(plan_init_state_timestamp);
    turn_light_duration_before_in_progress =
        std::make_optional(turn_light_duration);
    return;
  }

  if (!last_in_progress_timestamp.has_value()) {
    return;
  }

  DCHECK(last_turn_light_duration_before_in_progress.has_value());
  DCHECK(last_speed_discomfort_for_progress.has_value());
  in_progress_timestamp = last_in_progress_timestamp;
  turn_light_duration_before_in_progress =
      last_turn_light_duration_before_in_progress;

  // Update discomfort for progress during lane change.
  // Since the speed searcher is greedy, we may have an unexpected yield
  // decision on the agents behind at a low discomfort level during lane change,
  // when the pass/yield solution space differ not much. Thus once we already
  // had a good pass decision at a high discomfort, we would like to keep the
  // discomfort to make ego able to move faster and to avoid decision flipping.
  // Besides, we should also consider the discomfort value since it may be
  // unsafe when the interaction strength is too high. Thus when the previous
  // discomfort reaches larger than the mid value, we lower one level of
  // discomfort for progress.
  const double last_speed_discomfort_value =
      last_speed_discomfort_for_progress.value();
  if (last_speed_discomfort_value >
      0.5 * (speed::Discomforts::kMax + speed::Discomforts::kMid) +
          math::constants::kEpsilon) {
    speed_discomfort_for_progress = std::make_optional(
        last_speed_discomfort_value - speed::Discomforts::kHighLevelRes);
  } else {
    speed_discomfort_for_progress =
        std::make_optional(last_speed_discomfort_value);
  }
}

std::vector<const pnc_map::Lane*> GetStitchedLaneSequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::vector<const pnc_map::Lane*>& untrimmed_lane_sequence) {
  if (untrimmed_lane_sequence.empty() || lane_sequence.empty()) {
    return untrimmed_lane_sequence;
  }
  const int64_t first_lane_id =
      DCHECK_NOTNULL(untrimmed_lane_sequence.at(0))->id();
  const auto first_lane_iter =
      std::find_if(lane_sequence.rbegin(), lane_sequence.rend(),
                   [&first_lane_id](const pnc_map::Lane* lane) {
                     return DCHECK_NOTNULL(lane)->id() == first_lane_id;
                   });
  if (first_lane_iter == lane_sequence.rend()) {
    return untrimmed_lane_sequence;
  }
  std::vector<const pnc_map::Lane*> stitched_lane_sequence(
      untrimmed_lane_sequence.rbegin(), untrimmed_lane_sequence.rend());
  for (auto iter = first_lane_iter + 1; iter < lane_sequence.rend(); ++iter) {
    const pnc_map::Lane* lane = DCHECK_NOTNULL(*iter);
    if (!lane->IsSuccessor(*stitched_lane_sequence.back())) {
      break;
    }
    stitched_lane_sequence.insert(stitched_lane_sequence.end(), lane);
  }
  std::reverse(stitched_lane_sequence.begin(), stitched_lane_sequence.end());
  return stitched_lane_sequence;
}

bool ShouldNotRunLcOnNonStraightLane(
    const RobotStateSnapshot& plan_init_state,
    std::vector<const pnc_map::Lane*>::const_iterator current_lane_iter,
    std::vector<const pnc_map::Lane*>::const_iterator source_lane_iter,
    bool is_left_lane_change) {
  if (current_lane_iter >= source_lane_iter) {
    return false;
  }
  // Don't allow to run LC if it has u-turn lane before source lane.
  // Notes: In this case, the lane change instance may have to be filtered.
  if (std::any_of(current_lane_iter, source_lane_iter,
                  [](const pnc_map::Lane* lane) {
                    return DCHECK_NOTNULL(lane)->IsTurnable(pb::U_TURN);
                  })) {
    return true;
  }
  const pnc_map::Lane& current_lane = *DCHECK_NOTNULL(*current_lane_iter);
  if (current_lane.IsTurnable(pb::STRAIGHT)) {
    return false;
  }
  // Only the opposite lane change direction is allowed.
  // TODO(elliot): Allow the same lane change direction with small heading diff.
  if ((current_lane.IsTurnable(pb::LEFT_TURN) && is_left_lane_change) ||
      (current_lane.IsTurnable(pb::RIGHT_TURN) && !is_left_lane_change)) {
    return true;
  }
  // Find the first straight lane, if there isn't, use source lane.
  auto straight_lane_iter = std::find_if(
      current_lane_iter, source_lane_iter,
      [](const pnc_map::Lane* lane) { return lane->IsTurnable(pb::STRAIGHT); });
  const pnc_map::Lane& straight_or_source_lane =
      straight_lane_iter != source_lane_iter ? **straight_lane_iter
                                             : **source_lane_iter;

  // Only consider heading diff between ego and source lane center line.
  const math::geometry::PolylineCurve2d& straight_or_source_lane_center_line =
      straight_or_source_lane.center_line();
  const double straight_or_source_lane_center_line_heading =
      straight_or_source_lane_center_line.GetInterpTheta(
          straight_or_source_lane_center_line
              .GetProximity(plan_init_state.rear_axle_position(),
                            math::pb::UseExtensionFlag::kAllow)
              .arc_length);
  const double signed_heading_diff = math::WrapFromMinusPiToPi(
      plan_init_state.heading() - straight_or_source_lane_center_line_heading);
  static constexpr double kMaxHeadingDiffToRunLcOnNonStraightLaneInRad = 0.3;
  const double valid_heading_diff =
      is_left_lane_change ? signed_heading_diff : -signed_heading_diff;
  const bool is_small_heading_diff =
      valid_heading_diff < kMaxHeadingDiffToRunLcOnNonStraightLaneInRad;
  LOG(INFO) << "[lane change][state]" << __func__ << "\n"
            << DUMP_TO_STREAM(straight_or_source_lane.id(), signed_heading_diff,
                              valid_heading_diff, is_small_heading_diff);
  return !is_small_heading_diff;
}

bool ShouldRunLaneChangePreparation(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const LaneChangeInstance& lane_change_instance,
    const pnc_map::Lane* current_lane,
    const RobotStateSnapshot& robot_state_snapshot) {
  DCHECK(current_lane != nullptr);
  const auto current_lane_iter =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [&current_lane](const pnc_map::Lane* lane) {
                     return DCHECK_NOTNULL(lane)->id() == current_lane->id();
                   });
  if (current_lane_iter == lane_sequence.end()) {
    LOG(INFO) << "[lane change][state]Current lane " << current_lane->id()
              << " is not in lane sequence.";
    return false;
  }
  DCHECK((*current_lane_iter) != nullptr);

  const pnc_map::Lane& source_lane = lane_change_instance.source_lane();
  // Since this lane change instance could be a proposal result that is based on
  // an alternative lane follow sequence which generated on a brother lane of
  // one lane in current lane follow sequence, so it meets expectation if we
  // find the source lane or its brother lane in current lane follow sequence.
  const auto source_lane_iter = std::find_if(
      lane_sequence.begin(), lane_sequence.end(),
      [&source_lane](const pnc_map::Lane* lane) {
        DCHECK(lane != nullptr);
        return (lane->id() == source_lane.id() ||
                lane->IsBrother(source_lane,
                                pnc_map::BrotherLane::RelationType::kDiverge,
                                true));
      });
  if (source_lane_iter == lane_sequence.end()) {
    LOG(INFO) << "[lane change][state]Source lane " << source_lane.id()
              << " is not in lane sequence.";
    return false;
  }
  DCHECK((*source_lane_iter) != nullptr);

  // To prevent for longer unnecessary preparation, ego can enter preparation
  // state before start arclength with a constant distance.
  const bool is_current_lane_before_source_lane =
      current_lane_iter < source_lane_iter;
  const auto start_lane_iter =
      is_current_lane_before_source_lane ? current_lane_iter : source_lane_iter;
  const auto end_lane_iter =
      is_current_lane_before_source_lane ? source_lane_iter : current_lane_iter;
  const bool is_left_lane_change =
      lane_change_instance.direction() == pb::LEFT_LANE_CHANGE;
  const bool should_not_run_lc_on_non_straight_lane =
      ShouldNotRunLcOnNonStraightLane(robot_state_snapshot, current_lane_iter,
                                      source_lane_iter, is_left_lane_change);
  // If the current lane is behind source lane in the lane sequence, this length
  // would be a negative value.
  double length_before_source_lane = 0.0;
  for (auto iter = start_lane_iter; iter < end_lane_iter; iter++) {
    const pnc_map::Lane* lane = *iter;
    // Do not enter preparation state if it can't run LC on non-straight lane
    // and there is left/right/u turn between current lane and source lane.
    if (should_not_run_lc_on_non_straight_lane &&
        !lane->IsTurnable(planner::pb::TurnMode::STRAIGHT)) {
      LOG(INFO) << "[lane change][state]Lane " << lane->id()
                << " 's turn mode is not STRAIGHT and shouldn't run LC on "
                   "non-straight lane.";
      return false;
    }
    length_before_source_lane +=
        (is_current_lane_before_source_lane ? 1.0 : -1.0) * lane->length();
  }

  const double ego_arc_length_on_current_lane =
      current_lane->center_line()
          .GetProximity(robot_state_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  // Flag indicates if ego is in junction while current lane is before source
  // region.
  const bool is_ego_in_junction =
      length_before_source_lane > math::constants::kEpsilon &&
      current_lane->IsInJunction();
  LOG(INFO) << "[lane change][state] Ego is "
            << (is_ego_in_junction ? "" : "not ") << "in junction with "
            << length_before_source_lane - ego_arc_length_on_current_lane
            << " m distance to the source lane.";
  return length_before_source_lane - ego_arc_length_on_current_lane +
             lane_change_instance.start_arclength_on_source_lane() <
         (is_ego_in_junction
              ? kMaxDistBeforeLcStartPosToBeginPreparationInJunctionInMeter
              : kMaxDistBeforeLcStartPosToBeginPreparationInMeter);
}

bool ShouldRunLaneChange(
    const LaneChangeInfo& lane_change_info,
    const LaneChangeInstance& lane_change_instance,
    const LaneSequenceResult& lane_sequence_result,
    const RobotStateSnapshot& robot_state_snapshot,
    const std::optional<speed::pb::SpeedSolverLcGuideSeed>&
        last_selected_lc_guide_seed,
    const pb::LaneSequenceCandidate::LaneSequenceType
        current_plan_lane_sequence_type,
    const pb::LaneChangeState last_selected_lane_change_state) {
  const pnc_map::Lane* current_lane = lane_sequence_result.current_lane;
  DCHECK(current_lane != nullptr);
  if (lane_change_info.lane_change_info_level ==
      pb::LaneChangeInfoLevel::kLightLaneChangeInfo) {
    return false;
  }

  if (IsLaneChangeSequenceType(current_plan_lane_sequence_type)) {
    return ShouldRunLaneChangePath(
        *current_lane, lane_change_info, robot_state_snapshot,
        last_selected_lc_guide_seed, last_selected_lane_change_state);
  }
  // For non lane change sequence, check if ego is ready to run lane change
  // preparation according to it's relative position to the source lane in lane
  // follow sequence. Even if ego is not in source lane region, ego can enter
  // preparation state to adjust the speed and create feasible gap in the
  // future.
  // TODO(elliot): We only consider LANE_FOLLOW and STUCK_AVOIDANCE_LANE_FOLLOW
  // here. Need to enlarge the scope if it has motivation for other lane follow
  // sequence.
  if ((current_plan_lane_sequence_type ==
           pb::LaneSequenceCandidate::LANE_FOLLOW ||
       current_plan_lane_sequence_type ==
           pb::LaneSequenceCandidate::STUCK_AVOIDANCE_LANE_FOLLOW) &&
      !ShouldRunLaneChangePreparation(
          GetStitchedLaneSequence(lane_sequence_result.lane_sequence,
                                  lane_sequence_result.untrimmed_lane_sequence),
          lane_change_instance, current_lane, robot_state_snapshot)) {
    LOG(INFO) << "[lane change][state] Should not run lane change for lane "
                 "sequence type: "
              << pb::LaneSequenceCandidate::LaneSequenceType_Name(
                     current_plan_lane_sequence_type);
    return false;
  }
  return true;
}

LaneChangeStatusForSpeed GenerateLaneChangeStatusFromLaneChangeInfo(
    const SpeedWorldModel& world_model, const LaneChangeInfo& lane_change_info,
    const LaneSequenceResult& lane_sequence_result,
    const pb::LaneChangeState last_selected_lane_change_state,
    const planner::pb::DrySteeringSeed& last_dry_steering_seed,
    const std::optional<int64_t> last_in_progress_timestamp,
    const std::optional<double> last_turn_light_duration_before_in_progress,
    const std::optional<double> last_speed_discomfort_for_progress,
    const google::protobuf::RepeatedPtrField<speed::pb::SpeedSolverLcGuideSeed>&
        last_lc_guide_seeds) {
  if (lane_change_info.should_restrict_lc) {
    return {};
  }

  const LaneChangeInstance& lane_change_instance =
      lane_change_info.lane_change_instance;
  const double turn_light_duration_for_lc =
      lane_change_info.turn_light_duration_for_lc;

  // Checks if lane change instance is valid.
  if (lane_change_instance.direction() ==
      pb::LaneChangeMode::NONE_LANE_CHANGE) {
    return {};
  }

  const pb::LaneSequenceCandidate::LaneSequenceType
      current_plan_lane_sequence_type = lane_sequence_result.lane_sequence_type;
  if (current_plan_lane_sequence_type == pb::LaneSequenceCandidate::UNKNOWN) {
    LOG(INFO) << "[lane change][state] Unknown lane sequence type.";
    return {};
  }

  const RobotStateSnapshot& robot_state_snapshot =
      world_model.robot_state().plan_init_state_snapshot();
  if (!ShouldRunLaneChange(lane_change_info, lane_change_instance,
                           lane_sequence_result, robot_state_snapshot,
                           last_lc_guide_seeds.empty()
                               ? std::nullopt
                               : std::make_optional(last_lc_guide_seeds[0]),
                           current_plan_lane_sequence_type,
                           last_selected_lane_change_state)) {
    return {};
  }

  LaneChangeStatusForSpeed lane_change_status;
  lane_change_status.lane_change_instance = lane_change_instance;
  lane_change_status.direction =
      lane_change_info.lane_change_instance.direction();
  lane_change_status.distance_to_start_lc = 0;
  lane_change_status.turn_signal_duration = turn_light_duration_for_lc;
  lane_change_status.is_triggered_by_immediate_pull_over =
      world_model.ShouldTriggerImmediateRightLaneChangeForImmediatePullover() &&
      lane_change_instance.direction() == pb::LaneChangeMode::RIGHT_LANE_CHANGE;
  const bool is_lane_change_sequence_type =
      IsLaneChangeSequenceType(current_plan_lane_sequence_type);
  lane_change_status.is_current_homotopy_lane_change =
      is_lane_change_sequence_type;

  if (!last_lc_guide_seeds.empty()) {
    // Currently we only consider one lc guide.
    const speed::pb::SpeedSolverLcGuideSeed& last_lc_guide_seed =
        last_lc_guide_seeds[0];
    lane_change_status.leading_object_id = last_lc_guide_seed.lead_obj_id();
    lane_change_status.tailing_object_id = last_lc_guide_seed.tail_obj_id();
  }

  const std::vector<math::geometry::Point2d> ego_bounding_box =
      robot_state_snapshot.bounding_box().CornerPoints();
  const bool can_ego_finish_lane_change =
      lane_change_info.can_ego_finish_lane_change;
  const bool is_ego_fully_in_source_lane =
      !can_ego_finish_lane_change &&
      lane_change_instance.IsEgoFullyInSourceLaneSequence(ego_bounding_box);
  constexpr double kEgoSlowSpeedInMps = 1.0;
  const bool is_ego_slow = robot_state_snapshot.speed() < kEgoSlowSpeedInMps;
  const bool is_lc_signal_from_seed =
      lane_change_info.signal_source_type ==
      pb::LaneChangeSignalSourceType::kLaneChangeSignalFromSeed;

  const bool should_latch_abort_state =
      last_dry_steering_seed.dry_steering_state() ==
          pb::DRY_STEERING_STATE_TRIGGER &&
      last_dry_steering_seed.trigger_reason() ==
          pb::DRY_STEERING_LANE_CHANGE_ABORT;
  lane_change_status.lane_change_state = ComputeLaneChangeStateForHomotopy(
      last_selected_lane_change_state, is_lane_change_sequence_type,
      can_ego_finish_lane_change, is_ego_fully_in_source_lane, is_ego_slow,
      is_lc_signal_from_seed, should_latch_abort_state);
  ComputeLaneChangeInProgressInfoForHomotopy(
      lane_change_status.lane_change_state,
      world_model.robot_state().plan_init_state_snapshot().timestamp(),
      turn_light_duration_for_lc, last_selected_lane_change_state,
      last_in_progress_timestamp, last_turn_light_duration_before_in_progress,
      last_speed_discomfort_for_progress,
      lane_change_status.in_progress_timestamp,
      lane_change_status.turn_light_duration_before_in_progress,
      lane_change_status.speed_discomfort_for_progress);

  return lane_change_status;
}

void CalculateEgoTargetRegionProximityInfo(
    const LaneChangeInstance& lane_change_instance,
    const RobotStateSnapshot& robot_state_snapshot,
    double& ego_front_corner_cross_lane_curve_dist,
    double& ra_target_region_center_curve_dist,
    double& ego_target_region_center_curve_heading_diff) {
  // Cross lane curve related result.
  ego_front_corner_cross_lane_curve_dist =
      -lane_change_instance.CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
          robot_state_snapshot);
  // Target center curve related result.
  const math::geometry::PolylineCurve2d& target_center_curve =
      lane_change_instance.target_region().nominal_path();
  const math::ProximityQueryInfo& ra_center_curve_proximity_info =
      target_center_curve.GetProximity(
          robot_state_snapshot.rear_axle_position(),
          math::pb::UseExtensionFlag::kAllow);
  ra_target_region_center_curve_dist =
      std::fabs(ra_center_curve_proximity_info.signed_dist);
  ego_target_region_center_curve_heading_diff =
      std::fabs(math::WrapFromMinusPiToPi(
          robot_state_snapshot.heading() -
          target_center_curve.GetInterpTheta(
              ra_center_curve_proximity_info.arc_length)));
}

// TODO(elliotsong): Add UTs.
double CalculatePoseFinishScore(
    const RobotStateSnapshot& robot_state_snapshot,
    const double ego_front_corner_cross_lane_curve_dist,
    const double ra_target_region_center_curve_dist,
    const double ego_target_region_center_curve_heading_diff,
    const double finish_urgency_score, std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";
  // Calculate the score of the distance that front bumper corner is encroaching
  // the cross lane curve.
  constexpr double kMinFrontCornerEncroachmentInMeter = 1.3;
  constexpr double kMaxFrontCornerEncroachmentInMeter = 1.7;
  const double max_encroachment_distance = math::LinearInterpolate(
      kMaxFrontCornerEncroachmentInMeter, kMinFrontCornerEncroachmentInMeter,
      finish_urgency_score);
  const double front_corner_dist_score =
      ego_front_corner_cross_lane_curve_dist > max_encroachment_distance ? 1.0
                                                                         : 0.0;
  debug_oss << DUMP_TO_STREAM(max_encroachment_distance,
                              ego_front_corner_cross_lane_curve_dist,
                              front_corner_dist_score)
            << "\n";

  // Calculate the score of the distance between rear axle center point and
  // target region's center curve.
  // TODO(elliotsong): This required minimum distance needs to consider the
  // minimum clearance of target lane sequence ahead of ego.
  constexpr double kMinRACenterToCenterCurveDistInMeter = 0.5;
  constexpr double kMaxRACenterToCenterCurveDistInMeter = 1.0;
  const double ra_center_curve_dist_score = math::GetInterpolationRatio(
      kMaxRACenterToCenterCurveDistInMeter,
      kMinRACenterToCenterCurveDistInMeter, ra_target_region_center_curve_dist,
      /*allow_extension=*/false);
  debug_oss << DUMP_TO_STREAM(ra_target_region_center_curve_dist,
                              ra_center_curve_dist_score)
            << "\n";

  // If ego is quite slow, don't consider the heading diff.
  constexpr double kEgoSlowSpeedInMps = 2.0;
  if (robot_state_snapshot.speed() < kEgoSlowSpeedInMps) {
    return std::max(front_corner_dist_score, ra_center_curve_dist_score);
  }

  // Calculate the score of heading diff between ego and center curve.
  constexpr double kSmallEgoCenterCurveHeadingDiffInRad = 0.3;
  constexpr double kLargeEgoCenterCurveHeadingDiffInRad = 0.4;
  constexpr double kMaxEgoCenterCurveHeadingDiffInRad = 0.5;
  const double min_heading_diff = math::LinearInterpolate(
      kSmallEgoCenterCurveHeadingDiffInRad,
      kLargeEgoCenterCurveHeadingDiffInRad, finish_urgency_score);
  const double ego_center_curve_heading_diff_score =
      math::GetInterpolationRatio(kMaxEgoCenterCurveHeadingDiffInRad,
                                  min_heading_diff,
                                  ego_target_region_center_curve_heading_diff,
                                  /*allow_extension=*/false);
  debug_oss << DUMP_TO_STREAM(min_heading_diff,
                              ego_target_region_center_curve_heading_diff,
                              ego_center_curve_heading_diff_score)
            << "\n";
  return std::min(std::max(front_corner_dist_score, ra_center_curve_dist_score),
                  ego_center_curve_heading_diff_score);
}

bool ShouldConsiderInteractionFinishScore(
    const LaneChangeInstance& lane_change_instance,
    const RobotStateSnapshot& robot_state_snapshot,
    const double ego_center_curve_heading_diff,
    const double finish_urgency_score, std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";
  if (!lane_change_instance.IsEgoFullyInTargetLaneSequence(
          robot_state_snapshot.bounding_box().CornerPoints())) {
    debug_oss << "Should consider interaction when ego isn't fully in target "
                 "region.\n";
    return true;
  }
  const double ego_speed = robot_state_snapshot.speed();
  constexpr double kEgoSlowSpeedInMps = 1.0;
  if (ego_speed < kEgoSlowSpeedInMps) {
    debug_oss << "Ignore interaction when ego is slow.\n";
    return false;
  }
  constexpr double kMinHeadingDiffInRad = 0.10;
  constexpr double kMaxHeadingDiffInRad = 0.20;
  const double heading_diff_threshold = math::LinearInterpolate(
      kMinHeadingDiffInRad, kMaxHeadingDiffInRad, finish_urgency_score);
  debug_oss << DUMP_TO_STREAM(heading_diff_threshold,
                              ego_center_curve_heading_diff)
            << "\n";
  return ego_center_curve_heading_diff >
         heading_diff_threshold - math::constants::kEpsilon;
}

// TODO(elliotsong): Add UTs.
double CalculateInteractionFinishScore(
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<LaneChangeRegionInfo>& region_infos,
    const double ego_corner_signed_lat_dist_to_cross_lane_curve,
    const bool is_to_left, std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";
  const LaneChangeRegionInfo* target_region_info =
      GetLaneChangeRegionInfoPtrByType(pb::LaneChangeRegionType::TARGET,
                                       region_infos);
  if (target_region_info == nullptr) {
    return 1.0;
  }
  const LaneChangeObjectInfoList& target_region_objects =
      target_region_info->object_info_list;
  if (target_region_objects.empty()) {
    debug_oss << "No target region objects.";
    return 1.0;
  }
  // For every object, calculate the longitudinal TTC with ego. Get the
  // interaction score based on the minimum TTC.
  const double ego_speed = robot_state_snapshot.speed();
  const double ego_ra_arclength = target_region_info->ego_ra_arc_length;
  const double ego_length =
      robot_state_snapshot.car_model_with_shape().shape_measurement().length();
  const double ego_rb_arclength = ego_ra_arclength - ego_length * 0.5;
  const double ego_fb_arclength = ego_ra_arclength + ego_length * 0.5;

  constexpr double kArclengthRangeBufferInMeter = 1.5;
  double ttc_of_arclength_range = std::numeric_limits<double>::infinity();
  for (const int64_t id : target_region_objects.non_leaving_ids()) {
    std::string debug_str;
    const double object_ttc = CalculateTTCOfArclengthRange(
        *DCHECK_NOTNULL(target_region_objects.Find(id)), ego_rb_arclength,
        ego_fb_arclength, ego_speed, kArclengthRangeBufferInMeter,
        ego_corner_signed_lat_dist_to_cross_lane_curve, is_to_left, debug_str);
    debug_oss << "Object " << id << " has ttc = " << object_ttc << debug_str
              << "\n";
    math::UpdateMin(object_ttc, ttc_of_arclength_range);
  }
  constexpr double kMinRemainingDurationInSec = 2.0;
  constexpr double kMaxRemainingDurationInSec = 4.0;
  return math::GetInterpolationRatio(
      kMinRemainingDurationInSec, kMaxRemainingDurationInSec,
      std::min(kMaxRemainingDurationInSec, ttc_of_arclength_range),
      /*allow_extension=*/false);
}

// TODO(elliotsong): Add UTs.
double CalculateTTCOfArclengthRange(
    const LaneChangeObjectInfo& object_info, const double ego_rb_arclength,
    const double ego_fb_arclength, const double ego_speed,
    const double distance_buffer,
    const double ego_corner_signed_lat_dist_to_cross_lane_curve,
    const bool is_to_left, std::string& debug_str) {
  // Don't consider the object by some simple attributes.
  const bool is_vehicle_or_cyclist =
      object_info.is_vehicle || object_info.is_cyclist;
  const bool is_likely_parked_car =
      object_info.is_likely_parked_car_from_score || object_info.is_parked_car;
  const double object_end_arclength =
      object_info.arc_length_on_region + 0.5 * object_info.length;
  const bool is_stationary_wo_stm_or_behind_ego =
      object_info.is_primary_stationary &&
      (!object_info.has_stationary_to_move_intention ||
       object_end_arclength < ego_fb_arclength);
  if (!is_vehicle_or_cyclist || is_likely_parked_car ||
      is_stationary_wo_stm_or_behind_ego) {
    static const std::string kObjectAttrDebugStr = ", filter by attr.";
    debug_str = kObjectAttrDebugStr;
    return std::numeric_limits<double>::infinity();
  }

  // Don't consider the object that has less encroachment to target region than
  // ego.
  const double agent_furthest_corner_signed_lat_dist_to_cross_lane_curve =
      object_info.agent_furthest_corner_signed_lat_dist_to_cross_lane_curve;
  const bool ego_has_more_lc_progress =
      is_to_left
          ? ego_corner_signed_lat_dist_to_cross_lane_curve >
                agent_furthest_corner_signed_lat_dist_to_cross_lane_curve
          : ego_corner_signed_lat_dist_to_cross_lane_curve <
                agent_furthest_corner_signed_lat_dist_to_cross_lane_curve;
  if (ego_has_more_lc_progress) {
    static const std::string kEgoHasMoreLcProgress =
        ", filter by ego_has_more_lc_progress.";
    debug_str = kEgoHasMoreLcProgress;
    return std::numeric_limits<double>::infinity();
  }

  // Don't consider the object that will enter target region in the future.
  DCHECK(!object_info.overlap_regions.empty());
  constexpr double kMaxStartPaddedRelativeTimeInSec = 2.0;
  if (object_info.overlap_regions.at(0).start_padded_relative_time_in_sec() >
      kMaxStartPaddedRelativeTimeInSec) {
    static const std::string kStartPaddedRelativeTimeDebugStr =
        ", filter by start_padded_relative_time.";
    debug_str = kStartPaddedRelativeTimeDebugStr;
    return std::numeric_limits<double>::infinity();
  }

  // Don't consider the object that has small encroachment to target region for
  // its first overlap region.
  const auto& first_region_overlap_slices =
      object_info.overlap_regions.at(0).overlap_slices();
  constexpr double kMinLateralGapInMeter = 0.5;
  const bool is_small_encroachment = !std::any_of(
      first_region_overlap_slices.begin(), first_region_overlap_slices.end(),
      [](const speed::pb::OverlapSlice& slice) {
        return std::fabs(slice.signed_lateral_gap()) < kMinLateralGapInMeter;
      });
  if (is_small_encroachment) {
    static const std::string kSmallEncroachmentDebugStr =
        ", filter by small encroachment.";
    debug_str = kSmallEncroachmentDebugStr;
    return std::numeric_limits<double>::infinity();
  }

  // Calculate the longitudinal TTC.
  const double object_start_arclength =
      object_info.arc_length_on_region - 0.5 * object_info.length;
  const double ego_rb_buffer_arclength = ego_rb_arclength - distance_buffer;
  const double ego_fb_buffer_arclength = ego_fb_arclength + distance_buffer;

  // Object is behind ego rear bumper with buffer.
  if (object_end_arclength < ego_rb_buffer_arclength) {
    const double relative_speed = object_info.speed - ego_speed;
    if (relative_speed < math::constants::kEpsilon) {
      static const std::string kSlowObjectBehindmentDebugStr =
          ", slow object behind ego.";
      debug_str = kSlowObjectBehindmentDebugStr;
      return std::numeric_limits<double>::infinity();
    }
    const double current_ego_object_distance =
        ego_rb_buffer_arclength - object_end_arclength;
    return current_ego_object_distance / relative_speed;
  }
  // Object is ahead of ego front bumper with buffer.
  if (object_start_arclength > ego_fb_buffer_arclength) {
    const double relative_speed = ego_speed - object_info.speed;
    if (relative_speed < math::constants::kEpsilon) {
      static const std::string kFastObjectAheadDebugStr =
          ", fast object ahead of ego.";
      debug_str = kFastObjectAheadDebugStr;
      return std::numeric_limits<double>::infinity();
    }
    const double current_ego_object_distance =
        object_start_arclength - ego_fb_buffer_arclength;
    return current_ego_object_distance / relative_speed;
  }
  // Object and ego has overlap with buffer. TTC is 0.
  return 0.0;
}

bool CanEgoFinishLaneChange(
    const GlobalRouteSolution* global_route_solution,
    const LaneChangeInstance& lane_change_instance,
    const RobotStateSnapshot& robot_state_snapshot,
    const pb::LaneChangeMetadata& lane_change_metadata,
    const std::vector<LaneChangeRegionInfo>& region_infos,
    const bool is_lc_backup_sequence, std::ostringstream& debug_oss) {
  debug_oss << "\n\n[" << __func__ << "]\n";
  const bool has_reached_target_lane =
      lane_change_metadata.ego_ra_arc_length_on_target_region() >
      lane_change_instance.target_region_length_before_target_lane();
  const double ego_speed = robot_state_snapshot.speed();
  constexpr double kMaxSpeedToConsiderAsSlowInMps = 2.0;
  const bool is_ego_in_low_speed = ego_speed < kMaxSpeedToConsiderAsSlowInMps;
  const pnc_map::Lane* closest_lane_on_target_lane =
      GetEgoClosestLaneOnTargetRegion(
          lane_change_instance.target_lane_sequence(),
          lane_change_metadata.ego_ra_arc_length_on_target_region());
  const bool is_closest_lane_in_cost_map =
      closest_lane_on_target_lane != nullptr &&
      global_route_solution != nullptr &&
      global_route_solution->GetCostToDestination(
          *closest_lane_on_target_lane,
          global_route_solution->waypoint_count()) != nullptr;
  // Don't finish if ego is before target lane and the closest lane in not in
  // cost map in high speed.
  if (!(has_reached_target_lane || is_ego_in_low_speed ||
        is_closest_lane_in_cost_map)) {
    debug_oss << "Can not early finish LC because: "
              << DUMP_TO_STREAM(has_reached_target_lane, is_ego_in_low_speed,
                                is_closest_lane_in_cost_map);
    return false;
  }

  // We can't finish early when the ego ra hasn't exceeded the cross lane
  // marking curve yet. Otherwise the route will be updated back to the source
  // lane.
  const math::geometry::PolylineCurve2d& cross_lane_marking_curve =
      lane_change_instance.GetCrossLaneCurve();
  const math::ProximityQueryInfo& ego_ra_proximity_on_cross_lane_marking =
      cross_lane_marking_curve.GetProximity(
          robot_state_snapshot.rear_axle_position(),
          math::pb::UseExtensionFlag::kForbid);
  const bool has_ego_passed_cross_lane_marking =
      lane_change_instance.direction() == pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? ego_ra_proximity_on_cross_lane_marking.side == math::pb::Side::kLeft
          : ego_ra_proximity_on_cross_lane_marking.side ==
                math::pb::Side::kRight;
  if (!has_ego_passed_cross_lane_marking) {
    debug_oss << "Ego's ra hasn't passed cross lane marking.";
    return false;
  }

  // When there is a short lane change ahead, we want to finish early
  // to start the subsequent lane changes early.
  constexpr double kShortLaneChangeRawMaxThresholdInMeter = 60.0;
  constexpr double kShortLaneChangeDurationMaxThresholdInSec = 8.0;
  const double short_lane_change_max_threshold =
      std::max(kShortLaneChangeRawMaxThresholdInMeter,
               kShortLaneChangeDurationMaxThresholdInSec * ego_speed);
  const double remaining_distance_per_lc =
      lane_change_metadata.remaining_distance_for_consecutive_lane_change() /
      lane_change_metadata.consecutive_lane_change_count();
  const bool has_short_consecutive_lane_change =
      remaining_distance_per_lc < short_lane_change_max_threshold;
  debug_oss << DUMP_TO_STREAM(short_lane_change_max_threshold,
                              remaining_distance_per_lc)
            << "\n";

  // As the lane sequence is latched during lane change, sometimes ego can't
  // enter the target region fully before the lane sequence end. We want to
  // reroute early in this case.
  const double ego_ra_to_lane_sequence_end =
      lane_change_instance.target_region().nominal_path().GetTotalArcLength() -
      lane_change_metadata.ego_ra_arc_length_on_target_region();
  const double ego_ra_to_fb =
      robot_state_snapshot.car_model_with_shape().shape_measurement().length() -
      robot_state_snapshot.car_model_with_shape()
          .shape_measurement()
          .rear_bumper_to_rear_axle();
  const double ego_fb_to_lane_sequence_end =
      ego_ra_to_lane_sequence_end - ego_ra_to_fb;
  constexpr double kFrontBumperToLaneSequenceEndRawMaxThresholdInMeter = 25.0;
  constexpr double kFrontBumperToLaneSequenceEndDurationMaxThresholdInSec = 8.0;
  const double front_bumper_to_lane_sequence_end_max_threshold = std::max(
      kFrontBumperToLaneSequenceEndRawMaxThresholdInMeter,
      kFrontBumperToLaneSequenceEndDurationMaxThresholdInSec * ego_speed);
  const bool is_ego_close_to_lane_sequence_end =
      ego_fb_to_lane_sequence_end <
      front_bumper_to_lane_sequence_end_max_threshold;
  debug_oss << DUMP_TO_STREAM(front_bumper_to_lane_sequence_end_max_threshold,
                              ego_fb_to_lane_sequence_end)
            << "\n";

  // Otherwise, calculate a score between 0 to 1 and use it to scale how early
  // we want to finish the lane change.
  // Close to lane sequence end score.
  constexpr double kFrontBumperToLaneSequenceEndRawMinThresholdInMeter = 15.0;
  constexpr double kFrontBumperToLaneSequenceEndDurationMinThresholdInSec = 5.0;
  const double front_bumper_to_lane_sequence_end_min_threshold = std::max(
      kFrontBumperToLaneSequenceEndRawMinThresholdInMeter,
      kFrontBumperToLaneSequenceEndDurationMinThresholdInSec * ego_speed);
  const double close_to_lane_sequence_score =
      is_ego_close_to_lane_sequence_end
          ? math::GetInterpolationRatio(
                front_bumper_to_lane_sequence_end_max_threshold,
                front_bumper_to_lane_sequence_end_min_threshold,
                ego_fb_to_lane_sequence_end)
          : 0.0;

  // TODO(elliotsong): Add UT on this section.
  // For a backup sequence, when ego is closed to source region's end, there is
  // a chance that regional path and target lane sequence are diverged. In that
  // case, we want to finish current lane sequence quickly to avoid unexpected
  // aborting.
  const double ego_ra_to_source_region_end =
      lane_change_instance.source_region().nominal_path().GetTotalArcLength() -
      lane_change_metadata.ego_ra_arc_length_on_source_region();
  const double ego_fb_to_source_region_end =
      ego_ra_to_source_region_end - ego_ra_to_fb;
  constexpr double kFrontBumperToSourceRegionEndRawMaxThresholdInMeter = 15.0;
  constexpr double kFrontBumperToSourceRegionEndDurationMaxThresholdInSec = 3.5;
  const double front_bumper_to_source_region_end_max_threshold = std::max(
      kFrontBumperToSourceRegionEndRawMaxThresholdInMeter,
      kFrontBumperToSourceRegionEndDurationMaxThresholdInSec * ego_speed);
  const bool is_ego_close_to_source_region_end =
      ego_fb_to_source_region_end <
      front_bumper_to_source_region_end_max_threshold;
  debug_oss << DUMP_TO_STREAM(front_bumper_to_source_region_end_max_threshold,
                              ego_fb_to_source_region_end)
            << "\n";

  constexpr double kFrontBumperToSourceRegionEndRawMinThresholdInMeter = 3.0;
  constexpr double kFrontBumperToSourceRegionEndDurationMinThresholdInSec = 2.0;
  const double front_bumper_to_source_region_end_min_threshold = std::max(
      kFrontBumperToSourceRegionEndRawMinThresholdInMeter,
      kFrontBumperToSourceRegionEndDurationMinThresholdInSec * ego_speed);
  // Currently this score is only available when the current lane change
  // sequence is a backup sequence.
  const double close_to_source_region_end_score =
      is_ego_close_to_source_region_end
          ? math::GetInterpolationRatio(
                front_bumper_to_source_region_end_max_threshold,
                front_bumper_to_source_region_end_min_threshold,
                ego_fb_to_source_region_end)
          : 0.0;

  // Short consecutive lane change score.
  constexpr double kShortLaneChangeRawMinThresholdInMeter = 30.0;
  constexpr double kShortLaneChangeDurationMinThresholdInSec = 4.0;
  const double short_lane_change_min_threshold =
      std::max(kShortLaneChangeRawMinThresholdInMeter,
               kShortLaneChangeDurationMinThresholdInSec * ego_speed);
  const double short_consecutive_lane_change_score =
      has_short_consecutive_lane_change
          ? math::GetInterpolationRatio(short_lane_change_max_threshold,
                                        short_lane_change_min_threshold,
                                        remaining_distance_per_lc)
          : 0.0;
  debug_oss << DUMP_TO_STREAM(close_to_lane_sequence_score,
                              short_consecutive_lane_change_score,
                              is_lc_backup_sequence,
                              close_to_source_region_end_score)
            << "\n";

  const double finish_urgency_score = math::Clamp(
      std::max({close_to_lane_sequence_score, close_to_source_region_end_score,
                short_consecutive_lane_change_score}),
      0.0, 1.0);
  constexpr double kMinFinishScoreThreshold = 0.8;
  constexpr double kMaxFinishScoreThreshold = 1.0;
  const double finish_score_threshold = math::LinearInterpolate(
      kMaxFinishScoreThreshold, kMinFinishScoreThreshold, finish_urgency_score);
  debug_oss << DUMP_TO_STREAM(finish_urgency_score, finish_score_threshold)
            << "\n";

  double ego_front_corner_cross_lane_curve_dist = 0.0;
  double ra_target_region_center_curve_dist = 0.0;
  double ego_target_region_center_curve_heading_diff = 0.0;
  CalculateEgoTargetRegionProximityInfo(
      lane_change_instance, robot_state_snapshot,
      ego_front_corner_cross_lane_curve_dist,
      ra_target_region_center_curve_dist,
      ego_target_region_center_curve_heading_diff);
  const double pose_finish_score = CalculatePoseFinishScore(
      robot_state_snapshot, ego_front_corner_cross_lane_curve_dist,
      ra_target_region_center_curve_dist,
      ego_target_region_center_curve_heading_diff, finish_urgency_score,
      debug_oss);
  debug_oss << DUMP_TO_STREAM(pose_finish_score) << "\n";
  // If this is a lc backup sequence and ego is close to source region end,
  // don't consider interaction finish score.
  constexpr double kHighCloseToSourceRegionEndScore = 1.0;
  const bool is_high_close_to_source_region_end_score =
      close_to_source_region_end_score >
      kHighCloseToSourceRegionEndScore - math::constants::kEpsilon;
  const bool should_consider_interaction_finish_score =
      FLAGS_planning_enable_lane_change_interaction_finish_score &&
      !is_high_close_to_source_region_end_score &&
      ShouldConsiderInteractionFinishScore(
          lane_change_instance, robot_state_snapshot,
          ego_target_region_center_curve_heading_diff, finish_urgency_score,
          debug_oss);
  const bool is_to_left =
      lane_change_instance.direction() == pb::LaneChangeMode::LEFT_LANE_CHANGE;
  const double interaction_finish_score =
      should_consider_interaction_finish_score
          ? CalculateInteractionFinishScore(
                robot_state_snapshot, region_infos,
                lane_change_metadata
                    .ego_corner_signed_lat_dist_to_cross_lane_curve(),
                is_to_left, debug_oss)
          : kMaxFinishScoreThreshold;
  debug_oss << DUMP_TO_STREAM(is_high_close_to_source_region_end_score,
                              should_consider_interaction_finish_score,
                              interaction_finish_score)
            << "\n";
  return std::min(pose_finish_score, interaction_finish_score) >
         finish_score_threshold - math::constants::kEpsilon;
}

bool IsLaneChangeReady(const pb::LaneChangeState& prev_lane_change_state,
                       const pb::LaneChangeMetadata& lane_change_metadata,
                       const pb::ElectiveLaneChangeDecision& elc_decision,
                       const pb::StuckSignal& stuck_signal,
                       const int64_t plan_init_timestamp_in_msec,
                       const double turn_light_duration_for_lc,
                       const double generic_urgency_score) {
  if (lane_change_metadata.consecutive_lane_change_count() <= 0) {
    return true;
  }

  // When ego is aborting a lane change, we should allow it to resume lane
  // change without prior turn signal because it has done so in the first
  // attempt.
  if (prev_lane_change_state ==
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_ABORT) {
    return true;
  }

  constexpr double kMaxTurnSignalDurationInSec = 2.0;

  // No turn signal duration requirement for stuck scenarios.
  if (plan_init_timestamp_in_msec - stuck_signal.last_stuck_timestamp() <
      kMaxTurnSignalDurationInSec * 1000) {
    return true;
  }

  // Reduce turn signal duration requirement for ELC.
  double max_turn_signal_duration_for_elc =
      std::numeric_limits<double>::infinity();
  if (elc_decision.is_triggered()) {
    constexpr double kELCTurnSignalMaxDurationInSec = 0.5;
    max_turn_signal_duration_for_elc = kELCTurnSignalMaxDurationInSec;
  }

  // Scale turn signal duration based on urgency score.
  constexpr double kUrgencyScoreForNoTurnSignal = 0.8;
  constexpr double kUrgencyScoreForMaxTurnSignal = 0.0;
  const double required_turn_signal_duration = math::GetLinearInterpolatedY(
      kUrgencyScoreForNoTurnSignal, kUrgencyScoreForMaxTurnSignal, 0.0,
      kMaxTurnSignalDurationInSec, generic_urgency_score);

  return turn_light_duration_for_lc >
         std::min(required_turn_signal_duration,
                  max_turn_signal_duration_for_elc) -
             math::constants::kEpsilon;
}

void PopulateEgoArcLengthOnRegions(
    const LaneChangeInstance& lane_change_instance,
    const math::geometry::Point2d& ego_ra_position,
    pb::LaneChangeMetadata& lane_change_metadata) {
  // Calculate ego arc length on source region.
  const double ego_ra_arc_length_on_source_region =
      lane_change_instance.source_region()
          .nominal_path()
          .GetProximity(ego_ra_position, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  lane_change_metadata.set_ego_ra_arc_length_on_source_region(
      ego_ra_arc_length_on_source_region);

  // Calculate ego arc length on target region.
  const double ego_ra_arc_length_on_target_region =
      lane_change_instance.target_region()
          .nominal_path()
          .GetProximity(ego_ra_position, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  lane_change_metadata.set_ego_ra_arc_length_on_target_region(
      ego_ra_arc_length_on_target_region);
}

void PopulateEgoLateralStatesWrtCrossLaneCurve(
    const LaneChangeInstance& lane_change_instance,
    const RobotStateSnapshot& robot_state_snapshot,
    pb::LaneChangeMetadata& lane_change_metadata) {
  const bool is_left_lane_change =
      lane_change_instance.direction() ==
      planner::pb::LaneChangeMode::LEFT_LANE_CHANGE;
  const math::geometry::PolylineCurve2d& cross_lane_curve =
      lane_change_instance.GetCrossLaneCurve();
  const double cross_lane_curve_heading = cross_lane_curve.GetInterpTheta(
      cross_lane_curve
          .GetProximity(robot_state_snapshot.rear_axle_position(),
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length);

  lane_change_metadata.set_cross_lane_curve_heading_at_ego_ra_position(
      cross_lane_curve_heading);

  lane_change_metadata.set_ego_corner_signed_lat_dist_to_cross_lane_curve(
      ComputeSignedDistanceToCurve(robot_state_snapshot.bounding_box(),
                                   cross_lane_curve,
                                   /*update_max=*/is_left_lane_change));
  lane_change_metadata.set_ego_signed_lat_speed_wrt_cross_lane_curve(
      ComputeProjectionOnDirection(robot_state_snapshot.speed(),
                                   robot_state_snapshot.heading(),
                                   cross_lane_curve_heading));
  lane_change_metadata.set_ego_signed_lat_accel_wrt_cross_lane_curve(
      ComputeProjectionOnDirection(robot_state_snapshot.acceleration(),
                                   robot_state_snapshot.heading(),
                                   cross_lane_curve_heading));
}

void PopulateConsecutiveLaneChangeSpanInfo(
    const RobotStateSnapshot& current_state_snapshot,
    const LaneChangeInstance& lane_change_instance,
    const pnc_map::Lane& current_lane,
    pb::LaneChangeMetadata& lane_change_metadata,
    std::ostringstream& debug_oss) {
  const math::geometry::Point2d& ego_ra_position =
      current_state_snapshot.rear_axle_position();
  // Check if ego is in the extended source region which is not reached to
  // source region.
  const std::vector<const pnc_map::Lane*>& extended_source_lane_sequence =
      lane_change_instance.extended_source_lane_sequence();
  const auto iter_current_lane_in_extend_source_region =
      std::find_if(extended_source_lane_sequence.begin(),
                   extended_source_lane_sequence.end(),
                   [&current_lane](const pnc_map::Lane* lane) {
                     return DCHECK_NOTNULL(lane)->id() == current_lane.id();
                   });
  const bool is_current_lane_in_extended_source_region =
      iter_current_lane_in_extend_source_region ==
      extended_source_lane_sequence.end();
  // If ego has not entered the source region, use current lane to get the
  // relative lateral position.
  const bool has_ego_entered_source_region =
      lane_change_metadata.ego_ra_arc_length_on_source_region() >
          -math::constants::kEpsilon &&
      !is_current_lane_in_extended_source_region;
  const math::geometry::PolylineCurve2d& source_lane_curve =
      has_ego_entered_source_region
          ? lane_change_instance.source_region().nominal_path()
          : current_lane.center_line();
  const auto& proximity_info = source_lane_curve.GetProximity(
      ego_ra_position, math::pb::UseExtensionFlag::kAllow);
  const double ego_source_lane_curve_lat_distance = proximity_info.signed_dist;
  const double ego_source_lane_curve_theta_diff =
      current_state_snapshot.heading() -
      source_lane_curve.GetInterpTheta(proximity_info.arc_length);

  // Calculate total duration.
  const double total_duration_needed_for_cons_lc =
      CalculateLcDuration(current_state_snapshot, lane_change_instance,
                          lane_change_metadata.consecutive_lane_change_count(),
                          ego_source_lane_curve_lat_distance,
                          ego_source_lane_curve_theta_diff, debug_oss);
  lane_change_metadata.set_total_duration_needed_for_cons_lc(
      total_duration_needed_for_cons_lc);
  // Calculate total longitudinal distance.
  const std::optional<double> needed_lon_span = CalculateMinLcLonSpan(
      current_state_snapshot, ego_source_lane_curve_theta_diff,
      total_duration_needed_for_cons_lc, debug_oss);
  if (!needed_lon_span.has_value()) {
    debug_oss << "total_long_dist_needed_for_cons_lc is invalid.";
    return;
  }
  lane_change_metadata.set_total_long_dist_needed_for_cons_lc(
      needed_lon_span.value());
}

void PopulateConsecutiveLaneChangeSlowDownInfo(
    const LaneChangeInstance& /* lc_instance */, const double ego_speed,
    pb::LaneChangeMetadata& lane_change_metadata,
    std::ostringstream& debug_oss) {
  const int lc_count = lane_change_metadata.consecutive_lane_change_count();
  if (lc_count < 2) {
    debug_oss << " No consecutive lane change, lc_count=" << lc_count << "\n";
    return;
  }

  // Compute the span needed to do all LCs comfortably.
  constexpr double kMinEgoSpeedInMps = 2.0;
  constexpr double kLaneChangeDurationInSec = 4.5;
  // Params to adapt the feature of dynamic look ahead time in ref generator.
  constexpr double kMinEgoSpeedForDynamicLookAheadTimeInMps = 3.5;
  constexpr double kLaneChangeDurationForDynamicLookAheadTimeInSec = 4.0;
  const double min_ego_speed =
      FLAGS_planning_enable_dynamic_look_ahead_time_in_ref_generator
          ? kMinEgoSpeedForDynamicLookAheadTimeInMps
          : kMinEgoSpeedInMps;
  const double lane_change_duration =
      FLAGS_planning_enable_dynamic_look_ahead_time_in_ref_generator
          ? kLaneChangeDurationForDynamicLookAheadTimeInSec
          : kLaneChangeDurationInSec;
  constexpr double kIntervalBetweenLaneChangesInSec = 1.0;
  const double clamped_ego_speed = std::max(ego_speed, min_ego_speed);
  const double comfortable_lane_change_total_span =
      clamped_ego_speed * lane_change_duration * lc_count +
      clamped_ego_speed * kIntervalBetweenLaneChangesInSec * (lc_count - 1);
  const double total_remaining_distance =
      lane_change_metadata.remaining_distance_for_consecutive_lane_change();
  // When there are enough span to do all LCs comfortably, we don't limit the
  // current LC.
  if (total_remaining_distance > comfortable_lane_change_total_span) {
    debug_oss << "Total remaining distance " << total_remaining_distance
              << "m is enough to finish " << lc_count << " LCs comfortably ("
              << comfortable_lane_change_total_span << ").\n";
    return;
  }

  // Compute the span needed with maximum brake for the current LC.
  constexpr double kMinAccDuringCurrentLCInMpss = -2.0;
  lane_change_metadata.set_min_acc_for_speed_limit(
      kMinAccDuringCurrentLCInMpss);
  const double hard_brake_end_speed_for_current_lc =
      (clamped_ego_speed + kMinAccDuringCurrentLCInMpss * lane_change_duration >
       min_ego_speed)
          ? (clamped_ego_speed +
             kMinAccDuringCurrentLCInMpss * lane_change_duration)
          : min_ego_speed;
  const double hard_brake_lane_change_span =
      0.5 * (clamped_ego_speed + hard_brake_end_speed_for_current_lc) *
      lane_change_duration;
  const double hard_brake_lane_change_total_span =
      hard_brake_lane_change_span +
      (lc_count - 1) * hard_brake_end_speed_for_current_lc *
          (lane_change_duration + kIntervalBetweenLaneChangesInSec);
  // When there are not enough span to do all LCs with a hard brake first LC, we
  // limit the current LC with the hard brake configuration.
  if (total_remaining_distance <
      hard_brake_lane_change_total_span - math::constants::kEpsilon) {
    lane_change_metadata.set_max_longitudinal_span_for_current_lc(
        hard_brake_lane_change_span);
    lane_change_metadata.set_speed_limit_at_current_lc_end(
        hard_brake_end_speed_for_current_lc);
    debug_oss << "Total remaining distance " << total_remaining_distance
              << "m is not enough for " << lc_count
              << " LCs even with hard brake span ("
              << comfortable_lane_change_total_span << ").\n";
    return;
  }

  // Otherwise, we calculate the minimum brake needed to do a decelerating first
  // LC and comfortable subsequent LCs.
  const double decelerating_end_speed_for_current_lc =
      std::max((total_remaining_distance -
                0.5 * clamped_ego_speed * lane_change_duration) /
                   (0.5 * lane_change_duration +
                    (lc_count - 1) * (lane_change_duration +
                                      kIntervalBetweenLaneChangesInSec)),
               min_ego_speed);
  const double decelerating_lane_change_span =
      0.5 * (clamped_ego_speed + decelerating_end_speed_for_current_lc) *
      lane_change_duration;
  lane_change_metadata.set_max_longitudinal_span_for_current_lc(
      decelerating_lane_change_span);
  lane_change_metadata.set_speed_limit_at_current_lc_end(
      decelerating_end_speed_for_current_lc);
  debug_oss << "Total remaining distance " << total_remaining_distance
            << "m is not enough for " << lc_count << " comfortable LCs.\n";
}

void ProcessConsecutiveLaneChangePlanningSegments(
    const google::protobuf::RepeatedPtrField<
        pb::PlanningSegment>::const_iterator& front_iter,
    google::protobuf::RepeatedPtrField<pb::PlanningSegment>::const_iterator&
        back_iter,
    int& cons_lc_count, double& total_cons_lc_distance, bool& is_first_segment,
    double& leading_lf_offset, std::vector<int64_t>& leading_lf_sequence) {
  DCHECK(back_iter < front_iter);
  while (back_iter != front_iter) {
    DCHECK(back_iter->has_lane_change_segment());
    cons_lc_count++;
    for (const auto& target_segment :
         back_iter->lane_change_segment().lc_target_segments()) {
      total_cons_lc_distance +=
          target_segment.end_arclength() - target_segment.start_arclength();
    }

    if (cons_lc_count > 1) {
      back_iter++;
      continue;
    }

    // Add the first lane change's source lane sequence to
    // leading_lf_sequence.
    for (const auto& source_segment :
         back_iter->lane_change_segment().lc_source_segments()) {
      if (is_first_segment) {
        leading_lf_offset = source_segment.start_arclength();
        is_first_segment = false;
      }
      // The last LF segment may be the same lane as the first source lane
      // of the LC segment, so do a check before adding the lane ID.
      if (leading_lf_sequence.empty() ||
          leading_lf_sequence.back() != source_segment.lane_id()) {
        leading_lf_sequence.emplace_back(source_segment.lane_id());
      }
    }
    back_iter++;
  }
}

void ProcessConsecutiveLaneFollowPlanningSegments(
    const google::protobuf::RepeatedPtrField<
        pb::PlanningSegment>::const_iterator& front_iter,
    google::protobuf::RepeatedPtrField<pb::PlanningSegment>::const_iterator&
        back_iter,
    double& total_cons_lf_segment_length, bool& is_first_segment,
    double& leading_lf_offset, std::vector<int64_t>& cons_lf_lane_id) {
  DCHECK(back_iter < front_iter);
  while (back_iter != front_iter) {
    DCHECK(back_iter->has_lane_follow_segment());
    const pb::Segment& lf_segment =
        back_iter->lane_follow_segment().lf_segment();
    total_cons_lf_segment_length +=
        lf_segment.end_arclength() - lf_segment.start_arclength();
    if (is_first_segment) {
      leading_lf_offset = lf_segment.start_arclength();
      is_first_segment = false;
    }
    cons_lf_lane_id.emplace_back(lf_segment.lane_id());
    back_iter++;
  }
}

void PopulateConsecutiveLaneChangeInfo(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const math::geometry::Point2d& ego_ra,
    pb::LaneChangeMetadata& lane_change_metadata) {
  // Calculate the number of consecutive lane changes, and the total remaining
  // distance of the consecutive lane changes.
  double distance_to_first_lc_segment = 0.0;
  int cons_lc_count = 0;
  double total_cons_lc_distance = 0.0;
  // The leading_lf_sequence contains all lanes before the LC, and the source
  // lanes of the first lane change. It is used later to align data when ego's
  // position doesn't match with planning segment's start.
  std::vector<int64_t> leading_lf_sequence;
  double leading_lf_offset = 0.0;
  bool is_first_segment = true;

  // Use two iters to process planning segments with following steps:
  // 1) Move front_iter to the first element that has different segment type
  // with the back_iter or to the end of all segments.
  // 2a) If back_iter points to a lc segment, we can process consecutive lc
  // segments between back_iter and front_iter to get the consecutive lc count
  // and total consecutive lc distance.
  // 2b) If back_iter points to a lf segment, we can process consecutive lf
  // segments between back_iter and front_iter to get the leading lf sequence
  // and total consecutive lf distance.
  // 3) Loop to 1) until back_iter points to the end of all segments or just
  // processed long consecutive lf segments.
  google::protobuf::RepeatedPtrField<pb::PlanningSegment>::const_iterator
      front_iter = planning_segment_sequence.planning_segments().begin();
  google::protobuf::RepeatedPtrField<pb::PlanningSegment>::const_iterator
      back_iter = planning_segment_sequence.planning_segments().begin();

  pb::LaneChangeMode last_lc_mode = pb::LaneChangeMode::NONE_LANE_CHANGE;

  auto get_lc_mode =
      [](const pb::PlanningSegment& segment) -> pb::LaneChangeMode {
    return segment.has_lane_change_segment()
               ? segment.lane_change_segment().lane_change_direction()
               : pb::LaneChangeMode::NONE_LANE_CHANGE;
  };

  while (back_iter != planning_segment_sequence.planning_segments().end()) {
    const bool is_back_iter_lc_segment = back_iter->has_lane_change_segment();

    while (front_iter != planning_segment_sequence.planning_segments().end() &&
           front_iter->has_lane_change_segment() == is_back_iter_lc_segment) {
      front_iter++;
    }

    if (is_back_iter_lc_segment) {
      ProcessConsecutiveLaneChangePlanningSegments(
          front_iter, back_iter, cons_lc_count, total_cons_lc_distance,
          is_first_segment, leading_lf_offset, leading_lf_sequence);
      last_lc_mode = get_lc_mode(*(back_iter - 1));
      continue;
    }

    double total_cons_lf_segment_length = 0.0;
    std::vector<int64_t> cons_lf_lane_id;
    ProcessConsecutiveLaneFollowPlanningSegments(
        front_iter, back_iter, total_cons_lf_segment_length, is_first_segment,
        leading_lf_offset, cons_lf_lane_id);

    if (cons_lc_count == 0) {
      distance_to_first_lc_segment += total_cons_lf_segment_length;
      leading_lf_sequence.insert(leading_lf_sequence.end(),
                                 cons_lf_lane_id.begin(),
                                 cons_lf_lane_id.end());
      continue;
    }
    // If the lf segments after the lc segments satisfy one of these
    // conditions, break this process.
    // 1) The lf segments are at the end of all planning segments.
    // 2) The lc modes are different between the lc segments before and after
    // the lf segments.
    // 3) The lf segments are not short enough.
    constexpr double kMaxTolDistBetweenLcSegInMeter = 25.0;
    if (back_iter == planning_segment_sequence.planning_segments().end() ||
        get_lc_mode(*back_iter) != last_lc_mode ||
        total_cons_lf_segment_length > kMaxTolDistBetweenLcSegInMeter) {
      break;
    }
    total_cons_lc_distance += total_cons_lf_segment_length;
  }

  // The ego's current location and the start of the planning segment sequence
  // may not align (especially when the lane sequence is latched). Here we align
  // them by subtracting ego's projected arc-length on the leading lf sequence.
  const std::vector<const pnc_map::Lane*>& leading_lf_lane_sequence =
      joint_pnc_map_service.GetLaneSequence(leading_lf_sequence);
  bool is_leading_lf_sequence_valid = true;
  for (size_t i = 0; i + 1 < leading_lf_lane_sequence.size(); i++) {
    if (leading_lf_lane_sequence[i] == nullptr ||
        leading_lf_lane_sequence[i + 1] == nullptr) {
      is_leading_lf_sequence_valid = false;
      rt_event::PostRtEvent<
          rt_event::planner::LaneChangeMetadataConstructionError>(
          "Lane in leading lane follow sequence is nullptr!");
      break;
    }
    if (leading_lf_lane_sequence[i]->IsSuccessor(
            *leading_lf_lane_sequence[i + 1])) {
      continue;
    }

    is_leading_lf_sequence_valid = false;
    rt_event::PostRtEvent<
        rt_event::planner::LaneChangeMetadataConstructionError>();
    break;
  }
  double ego_arc_length_on_planning_segment_sequence = 0.0;
  if (!leading_lf_sequence.empty() && is_leading_lf_sequence_valid) {
    const LaneFollowSequenceCurve& lf_and_first_lc_source_sequence_curve =
        LaneFollowSequenceCurve(leading_lf_lane_sequence);
    const double ego_arc_length_on_leading_lf_sequence =
        lf_and_first_lc_source_sequence_curve.nominal_path()
            .GetProximity(ego_ra, math::pb::kAllow)
            .arc_length;
    ego_arc_length_on_planning_segment_sequence =
        ego_arc_length_on_leading_lf_sequence - leading_lf_offset;
  }

  lane_change_metadata.set_consecutive_lane_change_count(cons_lc_count);
  lane_change_metadata.set_remaining_distance_for_consecutive_lane_change(
      distance_to_first_lc_segment + total_cons_lc_distance -
      ego_arc_length_on_planning_segment_sequence);
}

// TODO(howardgao): add unit test.
// TODO(elliotsong): use the elliotsong's time window model.
void PopulateLaneChangeCriticalPointsInPreparation(
    const double ego_speed, pb::LaneChangeMetadata& lane_change_metadata) {
  constexpr double kLaneChangeComfortTimeWindowInSec = 6.0;

  lane_change_metadata.set_lane_change_cross_lane_time_in_sec(
      kLaneChangeComfortTimeWindowInSec * 0.5);
  lane_change_metadata.set_lane_change_cross_lane_arc_length_in_meter(
      ego_speed * kLaneChangeComfortTimeWindowInSec * 0.5);
  lane_change_metadata.set_lane_change_finished_time_in_sec(
      kLaneChangeComfortTimeWindowInSec);
  lane_change_metadata.set_lane_change_finished_arc_length_in_meter(
      ego_speed * kLaneChangeComfortTimeWindowInSec);
}

// TODO(howardgao): add unit test.
void PopulateLaneChangeCriticalPointsInProgress(
    const LaneChangeInstance& lane_change_instance,
    const google::protobuf::RepeatedPtrField<pb::TrajectoryPose>&
        trajectory_poses,
    const double /* ego_speed */, const int64_t plan_init_timestamp,
    pb::LaneChangeMetadata& lane_change_metadata) {
  const pb::LaneChangeMode& direction = lane_change_instance.direction();
  const LaneFollowSequenceCurve& target_region =
      lane_change_instance.target_region();
  const bool is_left_lc = (direction == pb::LaneChangeMode::LEFT_LANE_CHANGE);
  const math::geometry::PolylineCurve2d& cross_lane_curve =
      is_left_lc ? target_region.right_boundary()
                 : target_region.left_boundary();
  const math::geometry::PolylineCurve2d& target_region_nominal_path =
      target_region.nominal_path();

  // Update lane change cross lane time and arc length.
  for (int i = trajectory_poses.size() - 1; i >= 0; --i) {
    const pb::TrajectoryPose& pose = trajectory_poses.at(i);
    const double signed_dist =
        cross_lane_curve
            .GetProximity({pose.x_pos(), pose.y_pos()},
                          math::pb::UseExtensionFlag::kAllow)
            .signed_dist;
    const double sign = is_left_lc ? 1 : -1;
    // Populate info if the last pose is ready to cross lane through
    // backtracking.
    if (signed_dist * sign < math::constants::kEpsilon) {
      lane_change_metadata.set_lane_change_cross_lane_time_in_sec(
          math::Ms2Sec(pose.timestamp() - plan_init_timestamp));
      lane_change_metadata.set_lane_change_cross_lane_arc_length_in_meter(
          pose.odom());
      break;
    }
  }

  // TODO(howardgao): refactor the logic with lane change finishing condition in
  // lane change status.
  // Update lane change finished time and arc length.
  constexpr double kMinimumDistToTargetRegionNominalPathInMeter = 0.5;
  for (int i = trajectory_poses.size() - 1; i >= 0; --i) {
    const pb::TrajectoryPose& pose = trajectory_poses.at(i);
    const double signed_dist =
        target_region_nominal_path
            .GetProximity({pose.x_pos(), pose.y_pos()},
                          math::pb::UseExtensionFlag::kAllow)
            .signed_dist;
    const double sign = is_left_lc ? -1 : 1;
    // Populate info if the last pose closes to the target nominal path with a
    // constant buffer through backtracking.
    if (signed_dist * sign > kMinimumDistToTargetRegionNominalPathInMeter) {
      lane_change_metadata.set_lane_change_finished_time_in_sec(
          math::Ms2Sec(pose.timestamp() - plan_init_timestamp));
      lane_change_metadata.set_lane_change_finished_arc_length_in_meter(
          pose.odom());
      break;
    }
  }
}

void PopulateLaneChangeCriticalPointsInfo(
    const LaneChangeInstance& lane_change_instance,
    const pb::DecoupledManeuverSeed& previous_iter_seed, const double ego_speed,
    const int64_t plan_init_timestamp,
    pb::LaneChangeMetadata& lane_change_metadata) {
  DCHECK(lane_change_instance.direction() !=
         pb::LaneChangeMode::NONE_LANE_CHANGE);

  const pb::LaneChangeState& selected_lane_change_state =
      previous_iter_seed.selected_lane_change_state();
  const google::protobuf::RepeatedPtrField<pb::TrajectoryPose>&
      trajectory_poses = previous_iter_seed.selected_trajectory().poses();

  // Populate lane change critical points in preparation.
  if (selected_lane_change_state ==
      pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION) {
    PopulateLaneChangeCriticalPointsInPreparation(ego_speed,
                                                  lane_change_metadata);
  }

  // Populate lane change critical points in progress.
  if (selected_lane_change_state ==
      pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    PopulateLaneChangeCriticalPointsInProgress(
        lane_change_instance, trajectory_poses, ego_speed, plan_init_timestamp,
        lane_change_metadata);
  }
}

std::vector<int64_t>
GetExpandingLaneFollowLaneOrSourceLanesFromPlanningSegments(
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const size_t start_index, const std::vector<int64_t>& lane_ids) {
  std::vector<int64_t> expanding_lane_ids;
  for (int i = start_index;
       i < planning_segment_sequence.planning_segments_size(); ++i) {
    const pb::PlanningSegment& planning_segment =
        planning_segment_sequence.planning_segments().at(i);
    if (planning_segment.has_lane_follow_segment() &&
        planning_segment.lane_follow_segment().has_lf_segment()) {
      const int64_t candidate_lane_id =
          planning_segment.lane_follow_segment().lf_segment().lane_id();
      if (!std::any_of(lane_ids.begin(), lane_ids.end(),
                       [&candidate_lane_id](const int64_t& lane_id) {
                         return lane_id == candidate_lane_id;
                       })) {
        expanding_lane_ids.emplace_back(
            planning_segment.lane_follow_segment().lf_segment().lane_id());
      }
      // Don't stop expanding when met a lf segment.
      continue;
    }

    if (!planning_segment.has_lane_change_segment()) {
      continue;
    }
    // For lc segment, we try to expand with all the source lanes in it.
    for (const auto& source_seg :
         planning_segment.lane_change_segment().lc_source_segments()) {
      const int64_t candidate_lane_id = source_seg.lane_id();
      if (std::any_of(lane_ids.begin(), lane_ids.end(),
                      [&candidate_lane_id](const int64_t& lane_id) {
                        return lane_id == candidate_lane_id;
                      })) {
        continue;
      }
      expanding_lane_ids.emplace_back(source_seg.lane_id());
    }
    // Break if we have met a lc segment.
    break;
  }
  return expanding_lane_ids;
}

std::vector<LaneChangeInstance>
GenerateLaneChangeInstancesFromPlanningSegmentSequence(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const std::vector<const pnc_map::Lane*>& first_source_lane_lf_sequence,
    std::ostringstream& debug_oss) {
  debug_oss << "\n" << __func__ << ": ";
  static constexpr auto get_lane_from_seq =
      [](const std::vector<const pnc_map::Lane*>& lanes,
         const int64_t lane_id) -> const pnc_map::Lane* {
    auto iter = std::find_if(lanes.begin(), lanes.end(),
                             [lane_id](const pnc_map::Lane* lane) {
                               return DCHECK_NOTNULL(lane)->id() == lane_id;
                             });
    return (iter == lanes.end()) ? nullptr : *iter;
  };

  std::vector<const pnc_map::Lane*> basic_source_lane_sequence =
      first_source_lane_lf_sequence;
  std::vector<LaneChangeInstance> lane_change_instances;
  pb::LaneChangeMode first_lc_direction = pb::LaneChangeMode::NONE_LANE_CHANGE;
  bool has_opposite_lc_direction = false;
  for (auto seg_iter = planning_segment_sequence.planning_segments().begin();
       seg_iter < planning_segment_sequence.planning_segments().end();
       seg_iter++) {
    const pb::PlanningSegment& planning_segment = *seg_iter;
    if (!planning_segment.has_lane_change_segment()) {
      continue;
    }

    const pb::LaneChangeSegment& lc_segment =
        planning_segment.lane_change_segment();
    if (lc_segment.lc_source_segments().empty() ||
        lc_segment.lc_target_segments().empty() ||
        lc_segment.lane_change_direction() ==
            pb::LaneChangeMode::NONE_LANE_CHANGE) {
      debug_oss << "Invalid lc segment.";
      continue;
    }
    const int64_t source_lane_id =
        lc_segment.lc_source_segments().begin()->lane_id();
    const int64_t target_lane_id =
        lc_segment.lc_target_segments().begin()->lane_id();
    const pb::LaneChangeMode& lc_direction = lc_segment.lane_change_direction();
    // Break this process when met with segment sequence having lc direction
    // like left->right->left.
    if (first_lc_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
      first_lc_direction = lc_direction;
    } else if (!has_opposite_lc_direction &&
               (first_lc_direction != lc_direction)) {
      has_opposite_lc_direction = true;
    } else if (has_opposite_lc_direction &&
               (first_lc_direction == lc_direction)) {
      break;
    }
    // The lane sequence to generate lc instance includes four parts.
    // (1) Lanes before the first source lane in the basic lane sequence.
    // (2) The first source lane and the whole target lanes in the current lc
    // segment.
    // (3) The following lf segment or lc segment's source segments.
    // (4) Adjacent lanes of the basic source lane sequence.
    std::vector<int64_t> lane_ids;
    // Part 1. Lanes before the first source lane in the basic lane sequence.
    const auto start_source_lane_iter = std::find_if(
        basic_source_lane_sequence.begin(), basic_source_lane_sequence.end(),
        [&source_lane_id](const pnc_map::Lane* lane) {
          return DCHECK_NOTNULL(lane)->id() == source_lane_id;
        });
    if (start_source_lane_iter != basic_source_lane_sequence.end()) {
      for (auto iter = basic_source_lane_sequence.begin();
           iter < start_source_lane_iter; ++iter) {
        lane_ids.emplace_back(DCHECK_NOTNULL(*iter)->id());
      }
    }
    // Part 2. The first source lane and the whole target lanes in the current
    // lc segment.
    lane_ids.emplace_back(source_lane_id);
    for (const pb::Segment& lc_target_seg : lc_segment.lc_target_segments()) {
      lane_ids.emplace_back(lc_target_seg.lane_id());
    }
    // Part 3. The following lf segment or lc segment's source segments.
    auto next_seg_iter = seg_iter + 1;
    const std::vector<int64_t>& first_expanding_target_lane_ids =
        GetExpandingLaneFollowLaneOrSourceLanesFromPlanningSegments(
            planning_segment_sequence,
            std::distance(planning_segment_sequence.planning_segments().begin(),
                          next_seg_iter),
            lane_ids);
    lane_ids.insert(lane_ids.end(), first_expanding_target_lane_ids.begin(),
                    first_expanding_target_lane_ids.end());
    std::vector<const pnc_map::Lane*> lane_seq =
        joint_pnc_map_service.GetLaneSequence(lane_ids);
    // Part 4. Adjacent lanes of the basic source lane sequence.
    // Use the basic source lane sequence to expand the original lane
    // sequence.
    const int64_t original_end_source_lane_id =
        lc_segment.lc_source_segments().cbegin()->lane_id();
    const auto last_source_lane_iter = std::find_if(
        basic_source_lane_sequence.begin(), basic_source_lane_sequence.end(),
        [&original_end_source_lane_id](const pnc_map::Lane* lane) {
          return DCHECK_NOTNULL(lane)->id() == original_end_source_lane_id;
        });
    // The starting index equals the index of the last source lane in the
    // current lc segment plus the count of expanded target lanes count above
    // plus one, which is supposed to be the index of the first unexpanded
    // source lane.
    for (auto iter =
             last_source_lane_iter + first_expanding_target_lane_ids.size() + 1;
         iter < basic_source_lane_sequence.end(); ++iter) {
      DCHECK(*iter);
      // Use the adjacent lane as the target lane.
      const pnc_map::Lane* adjacent_lane =
          lc_direction == pb::LaneChangeMode::LEFT_LANE_CHANGE
              ? (*iter)->adjacent_left_lane()
              : (*iter)->adjacent_right_lane();
      if (adjacent_lane == nullptr ||
          (!lane_seq.empty() &&
           !DCHECK_NOTNULL(lane_seq.back())->IsSuccessor(*adjacent_lane))) {
        break;
      }
      lane_ids.emplace_back(adjacent_lane->id());
      lane_seq.emplace_back(adjacent_lane);
    }
    // Check the validity of lane sequence.
    auto print_lane_ids = [](const std::vector<int64_t>& lane_ids) {
      std::string lane_ids_str;
      for (const auto& id : lane_ids) {
        lane_ids_str += " " + std::to_string(id);
      }
      return lane_ids_str;
    };
    const bool is_valid_lane_sequence =
        !std::any_of(lane_seq.begin(), lane_seq.end(),
                     [](const pnc_map::Lane* lane) { return lane == nullptr; });
    DCHECK(is_valid_lane_sequence)
        << "Invalid lane sequence: " << print_lane_ids(lane_ids);
    const pnc_map::Lane* source_lane =
        get_lane_from_seq(lane_seq, source_lane_id);
    const pnc_map::Lane* target_lane =
        get_lane_from_seq(lane_seq, target_lane_id);
    if (source_lane == nullptr || target_lane == nullptr) {
      debug_oss << "Invalid source or target lane.";
      continue;
    }
    LaneChangeInstance lane_change_instance(
        lc_direction,
        lc_segment.lc_source_segments().begin()->start_arclength(), source_lane,
        target_lane);
    lane_change_instance.UpdateSourceAndTargetLaneSequences(
        drivable_lane_reasoner, lane_seq);
    lane_change_instances.emplace_back(lane_change_instance);

    // The next basic source lane sequence is the same as the last target lane
    // sequence.
    basic_source_lane_sequence = lane_change_instance.target_lane_sequence();
  }
  return lane_change_instances;
}

void PreviewedLaneChangeInfoGenerator::
    GeneratePreviewedLaneChangeInfoFromLaneFollowSequence(
        const WorldModel& world_model,
        const pb::DecoupledManeuverSeed& previous_iter_seed,
        const LaneSequenceResult& lane_sequence_result,
        PreviewedLaneChangeInfoForLfSeq& previewed_lane_change_info,
        std::ostringstream& debug_oss,
        pb::PreviewedLaneChangeInfoDebug* debug) {
  debug_oss << "[lane change][common]" << __func__ << ":\nLane sequence type: "
            << pb::LaneSequenceCandidate::LaneSequenceType_Name(
                   lane_sequence_result.lane_sequence_type);
  previewed_lane_change_info.lane_sequence_type =
      lane_sequence_result.lane_sequence_type;
  const int64_t unique_lane_id = previewed_lane_change_info.unique_lane_id;

  debug_oss << "\nunique_lane_id: " << unique_lane_id;
  if (debug != nullptr) {
    debug->set_lane_sequence_type(lane_sequence_result.lane_sequence_type);
    debug->set_unique_lane_id(unique_lane_id);
  }

  if (unique_lane_id < 0) {
    debug_oss << "\nInvalid unique lane id.";
    return;
  }

  // If the on-route preview result is empty, it means there is no chance to lc
  // back to regional path.
  const std::optional<lane_selection::PreviewRouteResult>&
      preview_route_result = previewed_lane_change_info.preview_route_result;
  if (!preview_route_result.has_value() ||
      !preview_route_result->on_route_preview_route_result.has_value()) {
    debug_oss << " No preview on-route result.";
    previewed_lane_change_info.difficulty_score = 1.0;
    return;
  }

  const pb::PlanningSegmentSequence& onroute_planning_segments =
      preview_route_result->on_route_preview_route_result->previewed_route_info
          .planning_segment_sequence;
  previewed_lane_change_info.lane_change_instances =
      GenerateLaneChangeInstancesFromPlanningSegmentSequence(
          *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
          world_model.drivable_lane_reasoner(), onroute_planning_segments,
          lane_sequence_result.untrimmed_lane_sequence, debug_oss);
  debug_oss << "\nLane change instances count = "
            << previewed_lane_change_info.lane_change_instances.size();
  if (previewed_lane_change_info.lane_change_instances.empty()) {
    return;
  }

  previewed_lane_change_info.lane_change_metadata = CalculateLaneChangeMetadata(
      onroute_planning_segments,
      previewed_lane_change_info.lane_change_instances.front(),
      *DCHECK_NOTNULL(lane_sequence_result.current_lane),
      lane_sequence_result.cost_factors,
      *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
      previous_iter_seed, world_model.robot_state(),
      debug ? debug->mutable_lane_change_metadata_debug() : nullptr);
  debug_oss << "\nConsecutive lc count = "
            << previewed_lane_change_info.lane_change_metadata
                   .consecutive_lane_change_count()
            << ", remaining_distance = "
            << previewed_lane_change_info.lane_change_metadata
                   .remaining_distance_for_consecutive_lane_change();
  previewed_lane_change_info.difficulty_score =
      ComputePreviewedLaneChangeDifficultyForLaneFollowSequence(
          world_model, lane_sequence_result,
          previewed_lane_change_info.lane_change_instances,
          previewed_lane_change_info.lane_change_metadata, debug_oss);

  if (debug != nullptr) {
    debug->set_difficulty_score(previewed_lane_change_info.difficulty_score);
  }
}

double ComputePreviewedLaneChangeDifficultyForLaneFollowSequence(
    const WorldModel& world_model,
    const LaneSequenceResult& lane_sequence_result,
    const std::vector<LaneChangeInstance>& lane_change_instances,
    const pb::LaneChangeMetadata& lane_change_metadata,
    std::ostringstream& debug_oss) {
  if (lane_change_instances.empty()) {
    return 0.0;
  }
  const LaneChangeInstance& lc_instance = lane_change_instances.front();
  if (IsInvalidLaneChangeInstance(lc_instance)) {
    return 0.0;
  }

  const double score =
      LaneChangeEnvAnalyzer::CalculateLaneChangeDifficultyScore(
          world_model, lane_sequence_result.lane_sequence,
          pb::LaneSequenceCandidate::LANE_CHANGE, lane_change_instances.front(),
          lane_change_metadata, lane_sequence_result.current_lane,
          pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo,
          lane_change_instances, debug_oss);

  return score;
}

void GetPreviewRouteResultOnLane(
    const WorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pnc_map::Lane* lane, const math::geometry::Point2d& ego_ra,
    std::optional<lane_selection::PreviewLanePoint>& lane_point_to_pass,
    std::optional<lane_selection::PreviewRouteResult>& preview_route_result,
    pb::PreviewRouteDebug* debug) {
  const double ego_ra_arc_length_on_diverged_lane =
      DCHECK_NOTNULL(lane)
          ->center_line()
          .GetProximity(ego_ra, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  lane_point_to_pass = std::make_optional<lane_selection::PreviewLanePoint>(
      {.lane = lane, .arc_length = ego_ra_arc_length_on_diverged_lane});
  preview_route_result = lane_sequence_candidates.GetPreviewRouteResult(
      *world_model.pnc_map_service(), world_model.regional_map(),
      *world_model.global_route_solution(), lane_point_to_pass.value(),
      /*compared_sequence_type=*/
      lane_selection::PreviewRequestOptimalSequenceType::kPreviewRouteOnly,
      lane_selection::PreviewRouteSearchStrategy::kDefaultByMinCost, debug);
}

std::vector<PreviewedLaneChangeInfoForLfSeq>
GeneratePreviewedLaneChangeInfoFromAllLaneFollowSequence(
    const WorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    pb::PreviewedLaneChangeInfosDebug* debug) {
  TRACE_EVENT_SCOPE(planner, LaneChangeCommon_GeneratePreviewedLaneChangeInfo);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(
      LAT_STAT_GeneratePreviewedLaneChangeInfoFromAllLaneFollowSequence);
  const std::vector<const LaneSequenceResult*>& all_lf_sequence =
      lane_sequence_candidates.GetAllLaneFollowSequenceResults();
  if (all_lf_sequence.empty()) {
    return {};
  }

  std::vector<PreviewedLaneChangeInfoForLfSeq> previewed_lane_change_infos(
      all_lf_sequence.size(), PreviewedLaneChangeInfoForLfSeq());
  if (debug) {
    debug->mutable_previewed_lc_info_debug_list()->Reserve(
        all_lf_sequence.size());
    for (size_t i = 0; i < all_lf_sequence.size(); ++i) {
      debug->add_previewed_lc_info_debug_list();
    }
  }
  const math::geometry::Point2d& ego_ra_position =
      world_model.robot_state().plan_init_state_snapshot().rear_axle_position();
  // The default lf sequence is used to compare with other lf sequence and find
  // out the first diverged lane.
  const LaneSequenceResult& default_lf_sequence =
      *(DCHECK_NOTNULL(all_lf_sequence.at(0)));
  const std::vector<const pnc_map::Lane*> default_lane_seq =
      default_lf_sequence.untrimmed_lane_sequence;
  // This index is used to construct the lane point to pass on the default lf
  // sequence.
  std::atomic_int the_first_diverged_lane_index = -1;

  std::vector<std::string> debug_strs;
  debug_strs.resize(all_lf_sequence.size());

  auto task = [&all_lf_sequence, &previewed_lane_change_infos,
               &the_first_diverged_lane_index, &default_lane_seq, &world_model,
               &lane_sequence_candidates, &ego_ra_position, &debug](int k) {
    const size_t seq_id = k;
    if (seq_id >= all_lf_sequence.size()) {
      return;
    }
    const LaneSequenceResult* lf_sequence = all_lf_sequence.at(seq_id);
    if (lf_sequence == nullptr) {
      return;
    }
    PreviewedLaneChangeInfoForLfSeq& previewed_lane_change_info =
        previewed_lane_change_infos[seq_id];
    const std::vector<const pnc_map::Lane*>& lane_seq =
        lf_sequence->untrimmed_lane_sequence;
    // Here we are assuming that the beginning of the lane sequence is aligned
    // between two lf sequences.
    int diverged_index = -1;
    for (size_t i = 0; i < lane_seq.size(); ++i) {
      if (i >= default_lane_seq.size()) {
        break;
      }

      if (lane_seq.at(i)->id() != default_lane_seq.at(i)->id()) {
        diverged_index = i;
        break;
      }
    }
    if (diverged_index < 0) {
      return;
    }

    // Only update for non-JO sequence to keep the diverged lane on default LK
    // seq in the fork.
    if (!lf_sequence->IsPullOutJumpOut()) {
      // Update the_first_diverged_lane_index atomically.
      auto old_index = the_first_diverged_lane_index.load();
      while (!the_first_diverged_lane_index.compare_exchange_weak(
          old_index, std::max(old_index, diverged_index))) {
      }
    }
    pb::PreviewedLaneChangeInfoDebug* previewed_lane_change_info_debug =
        debug ? debug->mutable_previewed_lc_info_debug_list(seq_id) : nullptr;
    const pnc_map::Lane* diverged_lane = lane_seq.at(diverged_index);
    previewed_lane_change_info.unique_lane_id = diverged_lane->id();
    GetPreviewRouteResultOnLane(
        world_model, lane_sequence_candidates, diverged_lane, ego_ra_position,
        previewed_lane_change_info.lane_point_to_pass,
        previewed_lane_change_info.preview_route_result,
        previewed_lane_change_info_debug
            ? previewed_lane_change_info_debug->mutable_preview_route_debug()
            : nullptr);
  };

  auto cache_ptr =
      const_cast<WorldModel*>(&world_model)->mutable_waypoint_lanes_cache_ptr();
  cache_ptr->SetCacheStatus(true);
  tbb::parallel_for(1, static_cast<int>(all_lf_sequence.size()), task);
  cache_ptr->SetCacheStatus(false);

  if (the_first_diverged_lane_index < 0) {
    LOG(INFO) << "The first diverged lane is not found.";
    return previewed_lane_change_infos;
  }

  PreviewedLaneChangeInfoForLfSeq& previewed_lane_change_info =
      previewed_lane_change_infos.at(0);
  pb::PreviewedLaneChangeInfoDebug* previewed_lane_change_info_debug =
      debug ? debug->mutable_previewed_lc_info_debug_list(0) : nullptr;
  const pnc_map::Lane* diverged_lane =
      default_lane_seq.at(the_first_diverged_lane_index);
  previewed_lane_change_info.unique_lane_id = diverged_lane->id();
  GetPreviewRouteResultOnLane(
      world_model, lane_sequence_candidates, diverged_lane, ego_ra_position,
      previewed_lane_change_info.lane_point_to_pass,
      previewed_lane_change_info.preview_route_result,
      previewed_lane_change_info_debug
          ? previewed_lane_change_info_debug->mutable_preview_route_debug()
          : nullptr);

  tbb::parallel_for(
      0, static_cast<int>(previewed_lane_change_infos.size()),
      [&world_model, &previous_iter_seed, &all_lf_sequence,
       &previewed_lane_change_infos, &debug_strs, &debug](int k) {
        std::ostringstream debug_oss;
        pb::PreviewedLaneChangeInfoDebug* previewed_lane_change_info_debug =
            debug ? debug->mutable_previewed_lc_info_debug_list(k) : nullptr;
        PreviewedLaneChangeInfoGenerator::
            GeneratePreviewedLaneChangeInfoFromLaneFollowSequence(
                world_model, previous_iter_seed, *all_lf_sequence.at(k),
                previewed_lane_change_infos.at(k), debug_oss,
                previewed_lane_change_info_debug);
        debug_strs[k] += "\n" + debug_oss.str();
      });

  std::ostringstream debug_oss;
  for (auto& str : debug_strs) {
    debug_oss << str;
  }
  if (debug != nullptr) {
    debug->set_debug_str(debug_oss.str());
  } else {
    LOG(INFO) << debug_oss.str();
  }

  return previewed_lane_change_infos;
}

pb::LaneChangeMetadata CalculateLaneChangeMetadata(
    const pb::PlanningSegmentSequence& planning_segment_sequence,
    const LaneChangeInstance& lane_change_instance,
    const pnc_map::Lane& current_lane,
    const pb::LaneSequenceCostFactors& lane_sequence_cost_factors,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const RobotState& robot_state, pb::LaneChangeMetadataDebug* debug) {
  // Logging & debugging setup.
  std::ostringstream debug_oss;
  BOOST_SCOPE_EXIT(&debug_oss, debug) {
    const std::string& debug_str = debug_oss.str();
    if (debug_str.empty()) {
      return;
    }
    if (debug != nullptr) {
      debug->set_debug_str(debug_str);
    }
  }
  BOOST_SCOPE_EXIT_END

  // If there is no lane change, return empty metadata.
  if (lane_change_instance.IsDummy()) {
    return pb::LaneChangeMetadata();
  }

  pb::LaneChangeMetadata metadata;

  const math::geometry::Point2d& ego_ra_position =
      robot_state.current_state_snapshot().rear_axle_position();
  const double ego_speed = robot_state.current_state_snapshot().speed();
  const int64_t plan_init_timestamp =
      robot_state.plan_init_state_snapshot().timestamp();

  // Calculate ego arc length on source and target region.
  PopulateEgoArcLengthOnRegions(lane_change_instance, ego_ra_position,
                                metadata);

  // Calculate ego's lateral states w.r.t. the cross lane curve.
  PopulateEgoLateralStatesWrtCrossLaneCurve(
      lane_change_instance, robot_state.plan_init_state_snapshot(), metadata);

  // Calculate the consecutive lane change info.
  PopulateConsecutiveLaneChangeInfo(joint_pnc_map_service,
                                    planning_segment_sequence, ego_ra_position,
                                    metadata);

  // Calculate needed consecutive lane change span.
  PopulateConsecutiveLaneChangeSpanInfo(robot_state.current_state_snapshot(),
                                        lane_change_instance, current_lane,
                                        metadata, debug_oss);

  // Calculate if we need to slow down for short consecutive lane changes.
  PopulateConsecutiveLaneChangeSlowDownInfo(lane_change_instance, ego_speed,
                                            metadata, debug_oss);

  // Calculate the lane change critical points info.
  PopulateLaneChangeCriticalPointsInfo(lane_change_instance, previous_iter_seed,
                                       ego_speed, plan_init_timestamp,
                                       metadata);

  // Populate last lane change chance point info.
  if (lane_sequence_cost_factors.has_last_lane_change_chance_point()) {
    *metadata.mutable_last_lane_change_chance_point() =
        lane_sequence_cost_factors.last_lane_change_chance_point();
  }

  // Populate lane change metadata debug.
  if (debug != nullptr) {
    *debug->mutable_lane_change_metadata() = metadata;
  }

  return metadata;
}

int64_t ComputeLaneChangeNoGapCycles(
    const pb::DecoupledManeuverSeed& previous_iter_seed) {
  // Only accumulates in lane change preparation.
  if (previous_iter_seed.selected_lane_change_state() !=
      pb::LANE_CHANGE_STATE_PREPARATION) {
    return 0;
  }

  // Check if gap align success in last cycle.
  const bool lc_guide_seeds_is_valid =
      !previous_iter_seed.speed_seed().lc_guide_seed().empty() &&
      previous_iter_seed.speed_seed().lc_guide_seed()[0].is_valid();
  if (lc_guide_seeds_is_valid) {
    return 0;
  }

  if (!previous_iter_seed.has_lane_change_info_seed()) {
    return 0;
  }
  return previous_iter_seed.lane_change_info_seed()
             .consecutive_no_gap_cycles() +
         1;
}

bool CanTriggerLaneChangeCrawl(
    const std::vector<LaneChangeUrgencyInfo>& urgency_infos,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const bool is_inexecutable_lane_change_signal,
    const bool is_last_lc_crawl) {
  if (previous_iter_seed.selected_behavior_type() == pb::DECOUPLED_PULL_OVER) {
    return false;
  }
  const bool lc_guide_seeds_is_valid =
      !previous_iter_seed.speed_seed().lc_guide_seed().empty() &&
      previous_iter_seed.speed_seed().lc_guide_seed()[0].is_valid();
  const bool can_last_iter_trigger_crawl =
      previous_iter_seed.has_lane_change_info_seed() &&
      previous_iter_seed.lane_change_info_seed().can_trigger_lc_crawl();
  if (lc_guide_seeds_is_valid && !is_last_lc_crawl) {
    return false;
  }
  if (previous_iter_seed.selected_lane_change_state() ==
      planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    return can_last_iter_trigger_crawl && !lc_guide_seeds_is_valid;
  }
  if (is_inexecutable_lane_change_signal) {
    return false;
  }
  constexpr double kRerouteDistanceBeforeSolidLineInMeter = 7.0;
  return std::any_of(
      urgency_infos.begin(), urgency_infos.end(),
      [](const LaneChangeUrgencyInfo& urgency_info) {
        const double urgency_dist = urgency_info.urgency_dist();
        return (urgency_info.stop_line_type() ==
                    planner::pb::LaneChangeStopLineType::HARD_STOP_LINE &&
                (urgency_dist < kTriggerCrawlFromEgoToDeadendInMeter &&
                 urgency_dist > kRerouteDistanceBeforeSolidLineInMeter));
      });
}

// TODO(howard): consider lane change metadata to improve urgency info and
// replace logics here.
// TODO(howard): add uts for this api.
bool ShouldSetNoYieldForELCTriggeredObject(
    const pb::LaneChangeMetadata lane_change_metadata,
    const pb::LaneSequenceCandidate::LaneSequenceType& lane_sequence_type,
    const double urgency_score, const int64_t duration_in_msec,
    const bool is_on_highway, std::ostringstream& debug_oss) {
  // Return false if current lane sequence type is not lane change to avoid fp
  // settings in the immediate lane change triggered by elc. The condition can
  // make sure that only set no yield in the next normal lane change when
  // returning back.
  if (lane_sequence_type != pb::LaneSequenceCandidate::LANE_CHANGE) {
    debug_oss << __func__ << "Not normal lane change type. \n";
    return false;
  }

  // Return false if the average distance is not enough for consecutive lane
  // change.
  DCHECK_GT(lane_change_metadata.consecutive_lane_change_count(), 0);
  const double average_lane_change_distance_for_consecutive_lane_change =
      lane_change_metadata.remaining_distance_for_consecutive_lane_change() /
      lane_change_metadata.consecutive_lane_change_count();
  constexpr double kMinimumNormalLaneChangeDistInMeter = 30.0;
  if (average_lane_change_distance_for_consecutive_lane_change <
      kMinimumNormalLaneChangeDistInMeter) {
    debug_oss << __func__
              << "No enough distance for the consecutive lane change. \n";
    return false;
  }

  // Return false if the duration is larger than the threshold, which means ego
  // cannot efficiently pass the elc triggered object.
  constexpr double kMinimumDurationFromELCTriggeredTimeInMSec = 6000;
  constexpr double kMinimumDurationFromELCTriggeredTimeOnHighwayInMSec = 12000;
  constexpr double kMaximumDurationFromELCTriggeredTimeInMSec = 15000;
  constexpr double kMaximumDurationFromELCTriggeredTimeOnHighwayInMSec = 30000;
  const double duration_upper_bound = math::LinearInterpolate(
      is_on_highway ? kMaximumDurationFromELCTriggeredTimeOnHighwayInMSec
                    : kMaximumDurationFromELCTriggeredTimeInMSec,
      is_on_highway ? kMinimumDurationFromELCTriggeredTimeOnHighwayInMSec
                    : kMinimumDurationFromELCTriggeredTimeInMSec,
      urgency_score);
  if (duration_in_msec > duration_upper_bound) {
    debug_oss << __func__ << "Duration(" << duration_in_msec
              << "msec) exceed the upper bound(" << duration_upper_bound
              << "msec). \n";
    return false;
  }

  return true;
}

void PopulateLaneChangeInfoDebug(const LaneChangeInfo& lane_change_info,
                                 pb::LaneChangeInfoDebug* debug) {
  // Populate common infos.
  debug->set_turn_light_duration_for_lc(
      lane_change_info.turn_light_duration_for_lc);
  debug->set_lane_change_completion_rate(
      lane_change_info.lane_change_completion_rate);
  debug->set_can_trigger_crawl(lane_change_info.can_trigger_crawl);
  debug->set_should_creep(lane_change_info.should_creep);
  debug->set_elc_triggered_object_id(lane_change_info.elc_triggered_object_id);
  debug->set_should_set_no_yield_for_elc_triggered_object(
      lane_change_info.should_set_no_yield_for_elc_triggered_object);
  debug->set_lane_change_signal_source_type(
      lane_change_info.signal_source_type);
  debug->set_lane_change_sequence_type(
      lane_change_info.lane_change_sequence_type);
  debug->set_is_inexecutable_lane_change_signal(
      lane_change_info.is_inexecutable_lane_change_signal);

  // Populate early lane change signal info debug.
  const EarlyLaneChangeSignalInfo& early_lc_signal_info =
      lane_change_info.early_lane_change_signal_info;
  pb::EarlyLaneChangeSignalInfoDebug* mutable_early_lc_signal_debug =
      debug->mutable_early_lane_change_signal_info_debug();
  mutable_early_lc_signal_debug->set_lane_change_direction(
      early_lc_signal_info.early_lc_direction);
  if (early_lc_signal_info.early_lc_source_lane.has_value() &&
      early_lc_signal_info.early_lc_source_lane.value() != nullptr) {
    mutable_early_lc_signal_debug->set_source_lane_id(
        early_lc_signal_info.early_lc_source_lane.value()->id());
  }
  if (early_lc_signal_info.early_lc_target_lane.has_value() &&
      early_lc_signal_info.early_lc_target_lane.value() != nullptr) {
    mutable_early_lc_signal_debug->set_target_lane_id(
        early_lc_signal_info.early_lc_target_lane.value()->id());
  }
  if (early_lc_signal_info.early_lc_instance_detail.has_value()) {
    mutable_early_lc_signal_debug->mutable_lane_change_instance_detail()
        ->CopyFrom(early_lc_signal_info.early_lc_instance_detail.value());
  }
  mutable_early_lc_signal_debug->set_should_propose_early_lane_change(
      early_lc_signal_info.should_propose_early_lane_change);

  // Populate lane change env info debug.
  const LaneChangeEnvInfo& lane_change_env_info =
      lane_change_info.lane_change_env_info;
  pb::LaneChangeEnvInfoDebug* mutable_lane_change_env_info_debug =
      debug->mutable_lane_change_env_info_debug();
  mutable_lane_change_env_info_debug->set_urgency_score(
      lane_change_env_info.urgency_score);
  mutable_lane_change_env_info_debug->set_smooth_urgency_score(
      lane_change_env_info.smooth_urgency_score);
  mutable_lane_change_env_info_debug->set_blocking_traffic_score(
      lane_change_env_info.blocking_traffic_score);
  mutable_lane_change_env_info_debug->set_difficulty_score(
      lane_change_env_info.difficulty_score);

  // Populate region infos.
  ::google::protobuf::Map<std::string, pb::LaneChangeRegionInfo>*
      mutable_region_info_map =
          mutable_lane_change_env_info_debug->mutable_region_info_map();
  mutable_lane_change_env_info_debug->clear_source_region_front_blockages();
  for (const LaneChangeRegionInfo& region_info :
       lane_change_env_info.region_infos) {
    mutable_region_info_map->insert(
        {pb::LaneChangeRegionType_Name(region_info.region_type),
         region_info.ToProto()});
    if (region_info.region_type == pb::LaneChangeRegionType::SOURCE) {
      for (const auto& [start_arclength, blockage] :
           region_info.front_blockages) {
        blockage.PopulateDebug(mutable_lane_change_env_info_debug
                                   ->add_source_region_front_blockages());
      }
    }
  }

  // Populate urgency infos.
  for (const LaneChangeUrgencyInfo& urgency_info :
       lane_change_env_info.urgency_infos) {
    *mutable_lane_change_env_info_debug->add_urgency_infos() =
        urgency_info.ToProto();
  }
  mutable_lane_change_env_info_debug->mutable_gap_align_urgency_info()
      ->CopyFrom(lane_change_env_info.gap_align_urgency_info.ToProto());
  mutable_lane_change_env_info_debug->mutable_generic_urgency_info()->CopyFrom(
      lane_change_env_info.generic_urgency_info.ToProto());

  // Populate safe info map.
  ::google::protobuf::Map<::google::protobuf::int64, pb::ObjectCollisionInfo>*
      mutable_object_collision_info_map =
          mutable_lane_change_env_info_debug
              ->mutable_object_collision_info_map();
  for (const auto& [object_id, object_collision_info] :
       lane_change_env_info.object_collision_info_map) {
    mutable_object_collision_info_map->insert(
        {object_id, object_collision_info.ToProto()});
  }
  mutable_lane_change_env_info_debug->set_safe_yield_acceleration(
      lane_change_env_info.safe_yield_acceleration);

  // Populate the object xregion motion info map.
  mutable_lane_change_env_info_debug->clear_object_xregion_motion_info_map();
  ::google::protobuf::Map<::google::protobuf::int64, pb::XRegionMotionInfoMap>*
      mutable_object_xregion_motion_info_map =
          mutable_lane_change_env_info_debug
              ->mutable_object_xregion_motion_info_map();
  for (const auto& [object_id, xregion_motion_info_map] :
       lane_change_env_info.object_xregion_motion_info_map) {
    pb::XRegionMotionInfoMap xregion_motion_info_map_pb;
    auto mutable_xregion_motion_info_map_pb =
        xregion_motion_info_map_pb.mutable_xregion_motion_info_map();
    for (const auto& [xregion_motion_type, xregion_motion_info] :
         xregion_motion_info_map) {
      mutable_xregion_motion_info_map_pb->insert(
          {xregion_motion_type, xregion_motion_info});
    }

    mutable_object_xregion_motion_info_map->insert(
        {object_id, std::move(xregion_motion_info_map_pb)});
  }
  mutable_lane_change_env_info_debug->set_is_current_lane_change_in_fork(
      lane_change_env_info.is_current_lane_change_in_fork);
}

const LaneSequenceResult* GetLaneSequenceResultWithLaneChange(
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const RobotStateSnapshot& robot_state_snapshot,
    const bool favor_immediate_lc_over_optimal_lc) {
  if (!previous_iter_seed.has_lane_sequence_proposal() ||
      !previous_iter_seed.lane_sequence_proposal()
           .has_lane_change_latch_signal() ||
      previous_iter_seed.lane_sequence_proposal()
          .lane_change_latch_signal()
          .prefixes()
          .empty()) {
    LOG(INFO) << "[lane change][common]" << __func__
              << ": Get default lane sequence with lane change or immediate "
                 "lane change.";
    return lane_sequence_candidates
        .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
            favor_immediate_lc_over_optimal_lc);
  }
  const LaneSequenceResult* lane_change_proposal_result =
      GetLaneSequenceProposalResult(
          lane_sequence_candidates.GetAllLaneChangeSequenceResults(),
          previous_iter_seed.lane_sequence_proposal()
              .lane_change_latch_signal());
  if (lane_change_proposal_result == nullptr) {
    LOG(INFO) << "[lane change][common]" << __func__
              << ": Invalid lane change proposal result, get default lane "
                 "sequence with lane change or immediate "
                 "lane change.";
    return lane_sequence_candidates
        .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
            favor_immediate_lc_over_optimal_lc);
  }

  // Check if can run lane change based on the proposal result sequence to avoid
  // flickering status when we get a new lane change instance that is not
  // allowed to do lane change for current pose. Return default result if can
  // not.
  if ((previous_iter_seed.selected_lane_change_state() !=
       pb::LANE_CHANGE_STATE_IN_PROGRESS) &&
      !ShouldRunLaneChangePreparation(
          lane_change_proposal_result->untrimmed_lane_sequence,
          lane_change_proposal_result->lane_change_instances.front(),
          lane_change_proposal_result->current_lane, robot_state_snapshot)) {
    LOG(INFO)
        << "[lane change][common]" << __func__
        << ": Should not run lane change on lane change proposal result, get "
           "default sequence with lane change or immediate lane change.";
    return lane_sequence_candidates
        .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
            favor_immediate_lc_over_optimal_lc);
  }

  // TODO(elliot): There is a chance we got an invalid instance from the
  // proposal result if we previously generated lc proposal based on an
  // nonconsecutive lf segment sequence. Will remove if routing has fixed this
  // issue.
  if (lane_change_proposal_result->lane_change_instances.empty() ||
      IsInvalidLaneChangeInstance(
          lane_change_proposal_result->lane_change_instances.front())) {
    LOG(INFO)
        << "[lane change][common]" << __func__
        << ": Invalid lane change instance from lane change proposal result, "
           "get default sequence with lane change or immediate lane change.";
    return lane_sequence_candidates
        .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
            favor_immediate_lc_over_optimal_lc);
  }
  const LaneSequenceResult* non_alter_lc_sequence =
      lane_sequence_candidates
          .GetLaneSequenceWithLaneChangeOrImmediateLaneChange(
              favor_immediate_lc_over_optimal_lc);
  if (non_alter_lc_sequence == nullptr ||
      non_alter_lc_sequence->lane_sequence_type !=
          pb::LaneSequenceCandidate::LANE_CHANGE) {
    LOG(INFO) << "[lane change][common]" << __func__
              << ": Get lane change proposal result.";
    return lane_change_proposal_result;
  }
  // Non alter lc sequence is optimal lane change. We should choose it over
  // backup sequence w/o route.
  const std::set<pb::LaneSequencePrefix::Source>& lc_sequence_prefix_sources =
      GetLaneSequencePrefixSources(lane_change_proposal_result,
                                   previous_iter_seed);
  const bool is_lc_backup_sequence =
      lc_sequence_prefix_sources.find(
          pb::LaneSequencePrefix_Source_LC_BACKUP) !=
      lc_sequence_prefix_sources.end();
  const bool is_lc_backup_sequence_wo_route =
      is_lc_backup_sequence &&
      previous_iter_seed.lane_change_sequence_proposal_metadata().type() ==
          pb::LaneChangeSequenceProposalType::kBackupSequenceWoRoute;
  if (is_lc_backup_sequence_wo_route) {
    LOG(INFO)
        << "[lane change][common]" << __func__
        << ": Get optimal lane change sequence over backup sequence w/o route.";
    return non_alter_lc_sequence;
  }
  LOG(INFO) << "[lane change][common]" << __func__
            << ": Get lane change proposal result.";
  return lane_change_proposal_result;
}

bool IsSimilarLaneChangeSignal(
    const LaneChangeInfo& lane_change_info,
    const pb::LaneChangeInfoSeed& lane_change_info_seed,
    std::string& rt_message) {
  const LaneChangeInstance& lane_change_instance =
      lane_change_info.lane_change_instance;
  const pb::LaneChangeInstance& last_lane_change_instance =
      lane_change_info_seed.lane_change_instance();

  // Lane change instance: direction change.
  if (lane_change_instance.direction() !=
      last_lane_change_instance.lane_change_direction()) {
    rt_message += ", NORMAL";
    return false;
  }

  // Lane change instance: start arc length flicker.
  constexpr double kMaximumAllowedLCStartArcLengthDiffInMeter = 5.0;
  if (std::abs(lane_change_instance.start_arclength_on_source_lane() -
               last_lane_change_instance.lc_start_arc_length()) >
      kMaximumAllowedLCStartArcLengthDiffInMeter) {
    rt_message += ", FLICKER";
    return false;
  }

  // Lane change instance: consecutive lane change finished.
  if (lane_change_info.lane_change_metadata.consecutive_lane_change_count() !=
      lane_change_info_seed.lane_change_metadata()
          .consecutive_lane_change_count()) {
    rt_message += ", CONSECUTIVE";
    return false;
  }

  return true;
}

void PopulateManualLaneChangeState(
    const int64_t allow_lane_change_timestamp_in_msec,
    const bool is_lc_disallowed, const bool need_lc_signal,
    const pb::LaneChangeMode& lc_direction,
    pb::ManualLaneChangeState& mlc_state) {
  mlc_state.set_is_lc_disallowed(is_lc_disallowed);
  mlc_state.set_allow_lane_change_timestamp_in_msec(
      allow_lane_change_timestamp_in_msec);
  mlc_state.set_need_lc_signal(need_lc_signal);
  mlc_state.set_lc_direction(lc_direction);
}

void UpdateManualLaneChangeStateByTime(
    const pb::ManualLaneChangeState& last_mlc_state,
    const pb::LaneChangeState& last_lc_state,
    const pb::LaneChangeMode& last_lc_direction,
    const pb::LaneChangeMode& lc_direction,
    const int64_t plan_init_timestamp_in_msec,
    pb::ManualLaneChangeState& mlc_state) {
  const int64_t allow_lane_change_timestamp_in_msec =
      last_mlc_state.allow_lane_change_timestamp_in_msec();
  // The lane change is disallowed if the allow lane change timestamp is larger
  // than the plan init timestamp.
  const bool is_lc_disallowed =
      allow_lane_change_timestamp_in_msec > plan_init_timestamp_in_msec;

  // Set the flag as false only when the lane change has finished.
  const bool need_lc_signal =
      (!last_mlc_state.is_lc_disallowed() && last_mlc_state.need_lc_signal() &&
       last_lc_state == pb::LaneChangeState::LANE_CHANGE_STATE_NONE &&
       last_lc_direction != pb::LaneChangeMode::NONE_LANE_CHANGE)
          ? false
          : last_mlc_state.need_lc_signal();
  // When LC signal is needed, keep the lane change direction from the last MLC
  // state; otherwise, use the direction from the lane change instance.
  const pb::LaneChangeMode& target_lc_direction =
      need_lc_signal ? last_mlc_state.lc_direction() : lc_direction;
  PopulateManualLaneChangeState(allow_lane_change_timestamp_in_msec,
                                is_lc_disallowed, need_lc_signal,
                                target_lc_direction, mlc_state);
}

bool AnswerLaneChangeAbortResponse(
    const std::vector<pb::AssistResponse>& assist_responses,
    const pb::LaneChangeState& last_lc_state,
    const bool is_ego_in_target_lane_sequence,
    const int64_t plan_init_timestamp_in_msec,
    pb::ManualLaneChangeState& mlc_state, std::ostringstream& debug_oss) {
  debug_oss << "\n " << __func__ << ": ";

  // Return false if the ego is not in lane change in progress.
  if (last_lc_state != pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    debug_oss << "Not in lane change in progress.";
    return false;
  }

  // Return false if the ego is in the target lane sequence.
  if (is_ego_in_target_lane_sequence) {
    debug_oss << "In the target lane sequence.";
    return false;
  }

  // Get the lane change abort response from the assist responses.
  const auto lca_response_iter =
      std::find_if(assist_responses.begin(), assist_responses.end(),
                   [](const pb::AssistResponse& response) {
                     return response.type_case() ==
                            pb::AssistResponse::kLaneChangeAbortResponse;
                   });
  if (lca_response_iter == assist_responses.end()) {
    debug_oss << "No lane change abort response is found.";
    return false;
  }

  // Return false if the lane change abort response is emitted earlier than
  // 2s(2000msec). The condition cannot be tested on the offboard monitor
  // because the lca response timestamp uses the current time, while the plan
  // init timestamp uses the bag time. The delay time is always smaller than
  // zero. The condition cannot be invoked anymore.
  constexpr double kMaximumAllowLCAResponseDelayTimeInMSec = 2000;
  const int64_t lca_response_timestamp =
      lca_response_iter->lane_change_abort_response().timestamp();
  const int64_t lca_response_delay_time =
      plan_init_timestamp_in_msec - lca_response_timestamp;
  if (lca_response_delay_time > kMaximumAllowLCAResponseDelayTimeInMSec) {
    debug_oss << "LCA response delay time (" << lca_response_delay_time
              << "msec) is greater than "
              << kMaximumAllowLCAResponseDelayTimeInMSec << "msec";
    return false;
  }

  // Populate the mlc state if the lane change abort response is found.
  // Per-offline discussed with XTeams, the lane change in process should be
  // inhibited at least 3s(3000ms) to ensure that the onboard intervention has
  // positive effects on the driverless test.
  constexpr int64 kAllowLaneChangeDurationInMSec = 3000;
  const int64_t allow_lane_change_timestamp_in_msec =
      std::max(plan_init_timestamp_in_msec + kAllowLaneChangeDurationInMSec,
               mlc_state.allow_lane_change_timestamp_in_msec());

  // Populate the mlc state.
  PopulateManualLaneChangeState(allow_lane_change_timestamp_in_msec,
                                /*is_lc_disallowed=*/true,
                                /*need_lc_signal=*/false,
                                /*lc_direction=*/mlc_state.lc_direction(),
                                mlc_state);

  debug_oss << "Answer the lane change abort response ("
            << lca_response_timestamp << " msec) at "
            << plan_init_timestamp_in_msec
            << " msec. Set the allow lane change timestamp as "
            << allow_lane_change_timestamp_in_msec << " msec.";

  rt_event::PostRtEvent<
      rt_event::planner::PlannerLaneChangeAbortResponseResponded>();

  return true;
}

bool AnswerMLCStateAllowedWOSignal(const pb::LaneChangeMode& command_direction,
                                   const pb::LaneChangeMode& lc_direction,
                                   const int64_t plan_init_timestamp_in_msec,
                                   pb::ManualLaneChangeState& mlc_state) {
  DCHECK(command_direction != pb::LaneChangeMode::NONE_LANE_CHANGE);

  //
  // source: is_lc_disallowed=false, need_lc_signal=false
  // target: is_lc_disallowed=false, need_lc_signal=true
  //

  // Check if the lane change instance provided by router exists.
  if (lc_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
    PopulateManualLaneChangeState(
        /*allow_lane_change_timestamp_in_msec=*/plan_init_timestamp_in_msec,
        /*is_lc_disallowed=*/false,
        /*need_lc_signal=*/true, /*lc_direction=*/command_direction, mlc_state);

    return true;
  }

  // Router signal exists.
  // source: is_lc_disallowed=false, need_lc_signal=false
  // target: is_lc_disallowed=true, need_lc_signal=false
  //

  // Check if the command direction is opposite to the lc direction.
  if (command_direction != lc_direction) {
    PopulateManualLaneChangeState(
        /*allow_lane_change_timestamp_in_msec=*/std::numeric_limits<
            int64>::max(),
        /*is_lc_disallowed=*/true,
        /*need_lc_signal=*/false, lc_direction, mlc_state);

    return true;
  }

  return false;
}

bool AnswerMLCStateAllowedWSignal(const pb::LaneChangeMode& command_direction,
                                  const pb::LaneChangeMode& lc_direction,
                                  pb::ManualLaneChangeState& mlc_state) {
  DCHECK(command_direction != pb::LaneChangeMode::NONE_LANE_CHANGE);
  if (lc_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
    return false;
  }

  // Check if the command direction is consistent to the lc direction.
  if (command_direction == lc_direction) {
    return false;
  }

  //
  // source: is_lc_disallowed=false, need_lc_signal=true
  // target: is_lc_disallowed=true, need_lc_signal=true
  //

  // The command direction is opposite to the lc direction.
  PopulateManualLaneChangeState(
      /*allow_lane_change_timestamp_in_msec=*/std::numeric_limits<int64>::max(),
      /*is_lc_disallowed=*/true,
      /*need_lc_signal=*/true, lc_direction, mlc_state);

  return true;
}

bool AnswerMLCStateDisallowedWOSignal(
    const pb::LaneChangeMode& command_direction,
    const pb::LaneChangeMode& lc_direction,
    const int64_t plan_init_timestamp_in_msec,
    pb::ManualLaneChangeState& mlc_state) {
  DCHECK(command_direction != pb::LaneChangeMode::NONE_LANE_CHANGE);
  if (lc_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
    return false;
  }

  // Check if the command direction is opposite to the lc direction.
  if (command_direction != lc_direction) {
    return false;
  }

  //
  // source: is_lc_disallowed=true, need_lc_signal=false
  // target: is_lc_disallowed=false, need_lc_signal=false
  //

  // The command direction is consistent to the lc direction.
  PopulateManualLaneChangeState(
      /*allow_lane_change_timestamp_in_msec=*/plan_init_timestamp_in_msec,
      /*is_lc_disallowed=*/false,
      /*need_lc_signal=*/false, lc_direction, mlc_state);

  return true;
}

bool AnswerMLCStateDisallowedWSignal(
    const pb::LaneChangeMode& command_direction,
    const pb::LaneChangeMode& lc_direction,
    const int64_t plan_init_timestamp_in_msec,
    pb::ManualLaneChangeState& mlc_state) {
  DCHECK(command_direction != pb::LaneChangeMode::NONE_LANE_CHANGE);
  if (lc_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
    return false;
  }

  // Check if the command direction is opposite to the lc direction.
  if (command_direction != lc_direction) {
    return false;
  }

  //
  // source: is_lc_disallowed=true, need_lc_signal=true
  // target: is_lc_disallowed=false, need_lc_signal=true
  //

  // The command direction is consistent to the lc direction.
  PopulateManualLaneChangeState(
      /*allow_lane_change_timestamp_in_msec=*/plan_init_timestamp_in_msec,
      /*is_lc_disallowed=*/false,
      /*need_lc_signal=*/true, lc_direction, mlc_state);
  return true;
}

bool AnswerManualLaneChangeCommand(
    const std::vector<pb::AssistResponse>& assist_responses,
    const pb::LaneChangeMode& lc_direction,
    const int64_t plan_init_timestamp_in_msec,
    pb::ManualLaneChangeState& mlc_state, std::ostringstream& debug_oss) {
  debug_oss << "\n " << __func__ << ": ";

  // Get the manual lane change command from the assist responses.
  const auto mlc_command_iter =
      std::find_if(assist_responses.begin(), assist_responses.end(),
                   [](const pb::AssistResponse& response) {
                     return response.type_case() ==
                            pb::AssistResponse::kManualLaneChangeCommand;
                   });
  if (mlc_command_iter == assist_responses.end()) {
    debug_oss << "No manual lane change command is found.";
    return false;
  }

  // Check if the mlc command is valid with the left or right direction.
  const pb::LaneChangeMode& command_direction =
      mlc_command_iter->manual_lane_change_command().lane_change_direction();
  if (command_direction == pb::LaneChangeMode::NONE_LANE_CHANGE) {
    debug_oss << "Incorrect mlc command direction.";
    return false;
  }
  const int64_t command_timestamp =
      mlc_command_iter->manual_lane_change_command().timestamp();
  //
  // Extract the decision for the command.
  //

  if (!mlc_state.is_lc_disallowed() && !mlc_state.need_lc_signal() &&
      AnswerMLCStateAllowedWOSignal(command_direction, lc_direction,
                                    plan_init_timestamp_in_msec, mlc_state)) {
    debug_oss << "Answered the mlc command (" << command_timestamp
              << " msec) with mlc state (allowed, without signal) at "
              << plan_init_timestamp_in_msec << " msec.";
    return true;
  }

  if (!mlc_state.is_lc_disallowed() && mlc_state.need_lc_signal() &&
      AnswerMLCStateAllowedWSignal(command_direction, lc_direction,
                                   mlc_state)) {
    debug_oss << "Answered the mlc command (" << command_timestamp
              << " msec) with mlc state (allowed, with signal) at "
              << plan_init_timestamp_in_msec << " msec.";
    return true;
  }

  if (mlc_state.is_lc_disallowed() && !mlc_state.need_lc_signal() &&
      AnswerMLCStateDisallowedWOSignal(command_direction, lc_direction,
                                       plan_init_timestamp_in_msec,
                                       mlc_state)) {
    debug_oss << "Answered the mlc command (" << command_timestamp
              << " msec) with mlc state (disallowed, without signal) at "
              << plan_init_timestamp_in_msec << " msec.";
    return true;
  }

  if (mlc_state.is_lc_disallowed() && mlc_state.need_lc_signal() &&
      AnswerMLCStateDisallowedWSignal(command_direction, lc_direction,
                                      plan_init_timestamp_in_msec, mlc_state)) {
    debug_oss << "Answered the mlc command (" << command_timestamp
              << " msec) with mlc state (disallowed, with signal) at "
              << plan_init_timestamp_in_msec << " msec.";
    return true;
  }

  debug_oss << "No valid answer.";
  return false;
}

bool AnswerLaneChangeAbortFromFlag(
    const RobotStateSnapshot& current_snapshot,
    const LaneChangeInstance& lane_change_instance,
    const pb::LaneChangeState& last_lc_state,
    pb::ManualLaneChangeState& mlc_state, std::ostringstream& debug_oss) {
  debug_oss << "\n " << __func__ << ": ";

  if (last_lc_state != pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    debug_oss << "Ego is not in progress.";
    return false;
  }
  // There is a chance that the state is in-progress w/o lane change
  // sequence in simulation if the sequence should have been the response to a
  // proposal but the proposal hasn't been restored. So we use early return here
  // to avoid DCHECK failure.
  if (lane_change_instance.direction() ==
      pb::LaneChangeMode::NONE_LANE_CHANGE) {
    debug_oss << " Invalid lane change instance.";
    return false;
  }
  const double encroachment_dist_over_cross_lane_curve =
      -lane_change_instance.CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
          current_snapshot);
  if (encroachment_dist_over_cross_lane_curve <
      FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter) {
    debug_oss
        << "Encroachment dist over the cross lane curve ("
        << encroachment_dist_over_cross_lane_curve << ") is smaller than "
        << FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter
        << " m.";
    return false;
  }

  // Per-offline discussed with lindazhang, the function only test the lane
  // change abort. Hence, the disallowed time set as the infinity.
  PopulateManualLaneChangeState(
      /*allow_lane_change_timestamp_in_msec=*/std::numeric_limits<int64>::max(),
      /*is_lc_disallowed=*/true,
      /*need_lc_signal=*/false,
      /*lc_direction=*/lane_change_instance.direction(), mlc_state);

  debug_oss
      << "Abort lane change. Encroachment dist over the cross lane curve ("
      << encroachment_dist_over_cross_lane_curve << ") is larger than "
      << FLAGS_planning_trigger_lane_change_abort_by_encroachment_in_meter
      << " m.";
  return true;
}

// Update the mlc state.
void UpdateManualLaneChangeState(
    const RobotState& robot_state,
    const std::vector<pb::AssistResponse>& assist_responses,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const LaneChangeInstance& lane_change_instance,
    pb::ManualLaneChangeState& mlc_state) {
  std::ostringstream debug_oss;
  debug_oss << "\n " << __func__ << ": ";

  BOOST_SCOPE_EXIT(&debug_oss) { LOG(INFO) << debug_oss.str(); }
  BOOST_SCOPE_EXIT_END

  // Early return if not in autonomous mode and set the mlc state as default.
  if (!robot_state.IsInAutonomousMode()) {
    mlc_state.Clear();
    debug_oss << "Ego is in manual mode.";
    return;
  }

  const pb::ManualLaneChangeState& last_mlc_state =
      previous_iter_seed.lane_change_info_seed().mlc_state();
  const pb::LaneChangeState& last_lc_state =
      previous_iter_seed.selected_lane_change_state();
  const int64_t plan_init_timestamp_in_msec =
      robot_state.plan_init_state_snapshot().timestamp();
  const pb::LaneChangeMode& lc_direction = lane_change_instance.direction();
  const pb::LaneChangeInstance& last_lc_instance =
      previous_iter_seed.lane_change_info_seed().lane_change_instance();

  // Update the mlc state by time.
  UpdateManualLaneChangeStateByTime(
      last_mlc_state, last_lc_state, last_lc_instance.lane_change_direction(),
      lc_direction, plan_init_timestamp_in_msec, mlc_state);

  // Early return if the lane change in progress is disallowed while ego is in
  // such state.
  if (mlc_state.is_lc_disallowed() &&
      last_lc_state == pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS) {
    debug_oss << "Ego should not be in lane change state in progress.";
    return;
  }

  // Check if the lane change abort should be automatically triggered in the
  // simulation.
  const RobotStateSnapshot& current_snapshot =
      robot_state.current_state_snapshot();
  if (av_comm::InSimulation() &&
      AnswerLaneChangeAbortFromFlag(current_snapshot, lane_change_instance,
                                    last_lc_state, mlc_state, debug_oss)) {
    return;
  }

  // Answer remote assist responses with the following priority:
  // 1. Lane change abort response;
  // 2. Manual lane change command;
  // Check if the lane change abort response exists and update mlc state.
  const bool is_ego_in_target_lane_sequence =
      lane_change_instance.IsEgoEncroachingTargetRegion(current_snapshot);
  if (FLAGS_planning_enable_lane_change_abort_response &&
      AnswerLaneChangeAbortResponse(
          assist_responses, last_lc_state, is_ego_in_target_lane_sequence,
          plan_init_timestamp_in_msec, mlc_state, debug_oss)) {
    return;
  }

  // Check if the manual lane change command exists and update the mlc state
  // according to the command.
  if (FLAGS_planning_enable_manual_lane_change_command &&
      AnswerManualLaneChangeCommand(assist_responses, lc_direction,
                                    plan_init_timestamp_in_msec, mlc_state,
                                    debug_oss)) {
    return;
  }
}

bool WillOvertakeAgent(
    const pb::Trajectory& last_trajectory,
    const LaneChangeObjectInfoList* target_region_object_info_list,
    const int64_t relative_time_in_msec) {
  if (last_trajectory.poses_size() == 0 ||
      target_region_object_info_list == nullptr) {
    return false;
  }

  const std::vector<int64_t>& non_leaving_object_ids =
      target_region_object_info_list->non_leaving_ids();

  return std::any_of(
      non_leaving_object_ids.begin(), non_leaving_object_ids.end(),
      [&last_trajectory, target_region_object_info_list,
       relative_time_in_msec](const int64_t object_id) {
        const LaneChangeObjectInfo* target_agent =
            target_region_object_info_list->Find(object_id);
        if (target_agent == nullptr || target_agent->object_ego_dist < 0.0) {
          return false;
        }
        int64_t lead_obj_overlap_relative_time_in_msec = -1;
        const std::optional<math::Range1d>&
            lead_obj_overlap_at_lane_change_end =
                speed::GetLatestOverlapRangeBeforeTime(
                    target_agent->overlap_regions, relative_time_in_msec,
                    lead_obj_overlap_relative_time_in_msec);
        if (!lead_obj_overlap_at_lane_change_end.has_value()) {
          return false;
        }
        const double lead_obj_front_from_ego =
            lead_obj_overlap_at_lane_change_end->end_pos;
        for (const auto& pose : last_trajectory.poses()) {
          if (pose.timestamp() - last_trajectory.poses()[0].timestamp() >
              lead_obj_overlap_relative_time_in_msec) {
            return pose.odom() > lead_obj_front_from_ego;
          }
        }

        return false;
      });
}

std::optional<math::Range1d> GenerateLaneChangeProbingLateralInfo(
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const LaneChangeInfo& lane_change_info, const double half_ego_width,
    const RobotStateSnapshot& plan_init_state_snapshot) {
  const double ego_front_corner_dist_to_cross_lane_curve =
      lane_change_info.lane_change_instance
          .CalculateEgoFrontBumperCornerDistToCrossLaneCurve(
              plan_init_state_snapshot);
  // If the ego already crosses lane or is far away from cross lane marking, we
  // don't probe.
  constexpr double
      kMaxDistanceFromEgoToCrossLaneCurveDuringLaneChangeProbingInMeter = 1.0;
  if (ego_front_corner_dist_to_cross_lane_curve < 0.0 ||
      ego_front_corner_dist_to_cross_lane_curve >
          kMaxDistanceFromEgoToCrossLaneCurveDuringLaneChangeProbingInMeter) {
    return std::nullopt;
  }
  const LaneChangeObjectInfoList* target_region_object_info_list =
      GetTargetRegionObjectInfoListPtr(lane_change_info);
  if (target_region_object_info_list == nullptr) {
    return std::nullopt;
  }
  // If the ego will overtake the agent in the future, return an empty value;
  // otherwise, proceed with the subsequent logic.
  if (WillOvertakeAgent(previous_iter_seed.selected_trajectory(),
                        target_region_object_info_list,
                        kLaneChangeDurationWithBufferInMSec)) {
    return std::nullopt;
  }
  // We calculate attraction's lateral range for probing.
  const double half_lane_width =
      0.5 *
      lane_change_info.lane_change_instance.source_lane().avg_sampled_width();
  constexpr double
      kLatDistanceBeforeLaneMarkingDuringCalmLaneChangeProbingInMeter = 0.2;
  const pb::LaneChangeMode& lane_change_direction =
      lane_change_info.lane_change_instance.direction();
  constexpr double kLaneChangeProbingAttractionLatToleranceInMeter = 0.3;
  const double calm_offset_distance =
      half_lane_width - half_ego_width -
      kLatDistanceBeforeLaneMarkingDuringCalmLaneChangeProbingInMeter;
  const double gentle_rightmost_lateral_position =
      lane_change_direction == pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? calm_offset_distance -
                kLaneChangeProbingAttractionLatToleranceInMeter
          : -calm_offset_distance;

  const std::optional<math::Range1d>& gentle_res =
      calm_offset_distance < 0.0
          ? std::nullopt
          : std::make_optional(math::Range1d{
                gentle_rightmost_lateral_position,
                gentle_rightmost_lateral_position +
                    kLaneChangeProbingAttractionLatToleranceInMeter});
  // If we don't find a gap and the last trajectory has low discomfort, return
  // the lateral range of attraction when the ego makes a gentle lateral probe.
  if (!previous_iter_seed.speed_seed().lc_guide_seed()[0].is_valid()) {
    return previous_iter_seed.speed_seed()
                       .speed_solver_result()
                       .selected_discomfort() < 0.5
               ? gentle_res
               : std::nullopt;
  }
  const int64_t lead_agent_id =
      previous_iter_seed.speed_seed().lc_guide_seed()[0].lead_obj_id();
  const int64_t tail_agent_id =
      previous_iter_seed.speed_seed().lc_guide_seed()[0].tail_obj_id();
  const LaneChangeObjectInfo* tail_obj_ptr =
      target_region_object_info_list->FindNonLeaving(tail_agent_id);
  const LaneChangeObjectInfo* lead_obj_ptr =
      target_region_object_info_list->FindNonLeaving(lead_agent_id);
  // If there is no lead agent and tail agent, we don't apply attraction.
  if (tail_obj_ptr == nullptr && lead_obj_ptr == nullptr) {
    return std::nullopt;
  }
  // If there is lead agent behind the ego, return the lateral range of
  // attraction when the ego makes a gentle lateral probe.
  if (lead_obj_ptr != nullptr && lead_obj_ptr->object_ego_dist < 0.0) {
    return gentle_res;
  }
  // If there is tail agent behind the ego, return the lateral range of
  // attraction when the ego makes a mild lateral probe.
  constexpr double
      kLatDistanceBeforeLaneMarkingDuringMildLaneChangeProbingInMeter = 0.1;
  const double mild_offset_distance =
      half_lane_width - half_ego_width -
      kLatDistanceBeforeLaneMarkingDuringMildLaneChangeProbingInMeter;
  const double mild_rightmost_lateral_position =
      lane_change_direction == pb::LaneChangeMode::LEFT_LANE_CHANGE
          ? mild_offset_distance -
                kLaneChangeProbingAttractionLatToleranceInMeter

          : -mild_offset_distance;
  const std::optional<math::Range1d>& mild_res =
      mild_offset_distance < 0.0
          ? std::nullopt
          : std::make_optional(math::Range1d{
                mild_rightmost_lateral_position,
                mild_rightmost_lateral_position +
                    kLaneChangeProbingAttractionLatToleranceInMeter});
  if ((tail_obj_ptr != nullptr && tail_obj_ptr->object_ego_dist < 0.0) &&
      (lead_obj_ptr == nullptr ||
       (lead_obj_ptr != nullptr && lead_obj_ptr->object_ego_dist > 0.0))) {
    return mild_res;
  }

  return std::nullopt;
}

bool HasDenseTraffic(const LaneChangeInfo& lane_change_info,
                     const double ego_speed, std::ostringstream& debug_oss) {
  const LaneChangeObjectInfoList* target_region_object_info_list =
      GetTargetRegionObjectInfoListPtr(lane_change_info);
  if (target_region_object_info_list == nullptr) {
    return false;
  }

  constexpr int kNumAgentToConsiderAsDenseTrafficForUrgentLc = 3;
  constexpr int kNumAgentToConsiderAsDenseTrafficForNonUrgentLc = 5;

  const double remaining_lane_change_distance =
      lane_change_info.lane_change_metadata.has_last_lane_change_chance_point()
          ? lane_change_info.lane_change_metadata
                .last_lane_change_chance_point()
                .dist_from_ego()
          : lane_change_info.lane_change_metadata
                .remaining_distance_for_consecutive_lane_change();
  // TODO(pengfei): consider to use traffic density here.
  const size_t target_region_agent_num =
      target_region_object_info_list->non_leaving_ids().size();

  debug_oss << "\n\nCreep info:\n"
            << DUMP_TO_STREAM(remaining_lane_change_distance,
                              target_region_agent_num);

  constexpr double kMaxRemainingDistanceForUrgentLcInMeter = 50.0;
  constexpr double kMinRemainingDistanceForNonUrgentLcInMeter = 100.0;

  // If <50m to last lane change point, only trigger creep when the number of
  // agents on the target lane exceeds a certain number.
  if (remaining_lane_change_distance <
      kMaxRemainingDistanceForUrgentLcInMeter) {
    debug_oss << "\n< 50m";
    return target_region_agent_num >=
           kNumAgentToConsiderAsDenseTrafficForUrgentLc;
  }

  const LaneChangeObjectInfoList* source_region_object_info_list =
      GetSourceRegionObjectInfoListPtr(lane_change_info);
  if (source_region_object_info_list == nullptr) {
    return false;
  }
  const size_t source_region_agent_num = source_region_object_info_list->size();
  debug_oss << "; source_region_agent_num: " << source_region_agent_num;

  // If >50m to last lane change point, only trigger creep when the number of
  // agents on the target and source lane both exceeds a certain number.
  if (target_region_agent_num <
          kNumAgentToConsiderAsDenseTrafficForNonUrgentLc ||
      source_region_agent_num <
          kNumAgentToConsiderAsDenseTrafficForNonUrgentLc) {
    debug_oss << "\ntarget/source region agent num < "
              << kNumAgentToConsiderAsDenseTrafficForNonUrgentLc;
    // Post rt event when creep is disabled for non-urgent lane change in dense
    // traffic on target region.
    if (FLAGS_planning_enable_lane_change_creep &&
        ego_speed < kMaxSpeedToEnableCreepInMps &&
        target_region_agent_num >
            kNumAgentToConsiderAsDenseTrafficForNonUrgentLc) {
      std::ostringstream payload;
      payload << DUMP_TO_STREAM(remaining_lane_change_distance,
                                target_region_agent_num,
                                source_region_agent_num);
      rt_event::PostRtEvent<
          rt_event::planner::DisableCreepForNonUrgentLcInDenseTraffic>(
          payload.str());
    }
    return false;
  }

  // Compute the closest object ahead on the source lane.
  constexpr double kMaxAppearanceRelativeTimeToConsiderAgentInSec = 1.0;
  const LaneChangeObjectInfo* object_ahead = nullptr;
  for (const int64_t id : source_region_object_info_list->non_leaving_ids()) {
    const LaneChangeObjectInfo* object_info =
        source_region_object_info_list->Find(id);
    if (object_info == nullptr) {
      continue;
    }

    if (object_info->overlap_regions.empty() ||
        object_info->overlap_regions[0].overlap_slices().empty()) {
      continue;
    }

    if (object_info->overlap_regions[0]
                .overlap_slices(0)
                .relative_time_in_sec() <
            kMaxAppearanceRelativeTimeToConsiderAgentInSec &&
        object_info->object_ego_dist > 0.0) {
      object_ahead = object_info;
      break;
    }
  }

  if (object_ahead == nullptr) {
    debug_oss << "\nno object ahead;";
    return false;
  }

  const double object_ego_speed_diff = object_ahead->speed - ego_speed;
  debug_oss << "\nobject ahead: " << object_ahead->id << "; "
            << DUMP_TO_STREAM(object_ego_speed_diff, object_ahead->speed,
                              ego_speed);

  constexpr double kMaxSpeedDiffToEnableCreepForUrgentLcInMps = 5.0;
  constexpr double kMaxSpeedDiffToEnableCreepForNonUrgentLcInMps = 3.0;

  // If 50-100m to last lane change point, only trigger creep when the closest
  // object ahead is not much faster than ego.
  if (remaining_lane_change_distance <
      kMinRemainingDistanceForNonUrgentLcInMeter) {
    debug_oss << "\n50-100m";
    return object_ego_speed_diff < kMaxSpeedDiffToEnableCreepForUrgentLcInMps;
  }

  // If >100m to last lane change point, only trigger creep when the closest
  // object ahead is not much faster than ego, with a smaller threshold.
  debug_oss << "\n> 100m";
  return object_ego_speed_diff < kMaxSpeedDiffToEnableCreepForNonUrgentLcInMps;
}

// TODO(haiming, anwu): Refactor this function to separate the logic used for
// seed generation in path_node and speed_node respectively.
LaneChangeInfo GenerateLaneChangeInfo(
    const WorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const TurnSignalState& turn_signal_state,
    const pull_over::PullOverInfo& pull_over_info,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    pb::LaneChangeInfoDebug* debug) {
  TRACE_EVENT_SCOPE(planner, LaneChangeInfo_Generate);
  VOY_LATENCY_STAT_RECORD_PLANNER_S1(LAT_STAT_LaneChangeInfo_Generate);
  // Logging & debugging setup.
  std::ostringstream debug_oss;
  BOOST_SCOPE_EXIT(&debug_oss, debug) {
    const std::string& debug_str = debug_oss.str();
    if (debug_str.empty()) {
      return;
    }
    if (debug != nullptr) {
      debug->set_debug_str(debug_str);
    }
  }
  BOOST_SCOPE_EXIT_END

  LaneChangeInfo lane_change_info;
  lane_change_info.signal_source_type = GetLaneChangeSignal(
      *world_model.GetLatestJointPncMapService(), world_model.robot_state(),
      lane_sequence_candidates, previous_iter_seed,
      lane_change_info.lane_sequence_w_lane_change,
      lane_change_info.lane_change_sequence,
      lane_change_info.lane_change_sequence_type,
      lane_change_info.lane_change_sequence_prefix_sources,
      lane_change_info.is_lc_backup_sequence,
      lane_change_info.is_early_lc_sequence,
      world_model.ShouldTriggerImmediateRightLaneChangeForImmediatePullover(),
      lane_change_info.lane_change_instance,
      lane_change_info.lane_change_planning_segment_sequence,
      world_model.drivable_lane_reasoner(), debug_oss);

  // Get default lane follow sequence.
  const LaneSequenceResult& lane_sequence_wo_lane_change =
      lane_sequence_candidates.GetLaneSequenceWithoutLaneChange();
  lane_change_info.lane_sequence_wo_lane_change = &lane_sequence_wo_lane_change;

  // Update manual lane change state.
  UpdateManualLaneChangeState(
      world_model.robot_state(), world_model.assist_responses(),
      previous_iter_seed, lane_change_info.lane_change_instance,
      lane_change_info.mlc_state);

  // Reason early lane change signal.
  EarlyLaneChangeSignalReasoner early_lane_change_signal_reasoner;
  if (FLAGS_planning_enable_early_lane_change_reasoning) {
    early_lane_change_signal_reasoner.PreReason(
        world_model, lane_sequence_candidates, previous_iter_seed,
        lane_change_info,
        debug == nullptr
            ? nullptr
            : debug->mutable_early_lane_change_signal_info_debug());
  }

  const std::optional<CompletedLCInfo>&
      last_completed_lc_info_after_turn_juncion =
          world_model.previous_action_recorder()
              .GetLastCompletedLCInfoAfterTurnJunction();
  lane_change_info.lane_change_intention_monitor.emplace(
      world_model, lane_sequence_candidates,
      last_completed_lc_info_after_turn_juncion,
      lane_change_info.lane_change_instance,
      lane_change_info.lane_change_metadata,
      lane_change_info.signal_source_type,
      lane_change_info.lane_sequence_wo_lane_change);

  if (lane_change_info.lane_change_instance.direction() ==
      pb::LaneChangeMode::NONE_LANE_CHANGE) {
    debug_oss << "\n No lane change instance.";
    PopulateLaneChangeInfoDebug(lane_change_info, debug);
    return lane_change_info;
  }

  // TODO(elliot): Replace it with DCHECK if routing has fixed the
  // nonconsecutive lf segments issue.
  if (IsInvalidLaneChangeInstance(lane_change_info.lane_change_instance)) {
    debug_oss << "\n Got an invalid lane change instance.";
    return lane_change_info;
  }

  if (lane_change_info.lane_change_info_level ==
      pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo) {
    // Copy the last lc guide seeds for the downstream user.
    lane_change_info.last_lc_guide_seeds.CopyFrom(
        previous_iter_seed.speed_seed().lc_guide_seed());

    // Get turn signal duration.
    lane_change_info.turn_light_duration_for_lc =
        GetTurnSignalDurationForLaneChange(
            lane_change_info.lane_change_instance.direction(),
            turn_signal_state);

    lane_change_info.lane_change_completion_rate =
        lane_change_info.lane_change_instance.ComputeEgoLcCompletionRate(
            world_model.robot_state().plan_init_state_snapshot());

    const auto lane_change_info_seed =
        Seed::Access<token::LaneChangeInfoSeed>::GetMsg(
            PathLastFinishedFrame());

    // Get the preview route result.
    if (previous_iter_seed.should_latch_lane_change_geom()) {
      debug_oss << "\n Populate preview route result from proto.";
      // If the sequence is latched, routing API cannot response with a valid
      // preview route result, so we restore it from seed.
      PopulatePreviewRouteResultFromProto(
          *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
          lane_change_info_seed->preview_route_result(),
          lane_change_info.lane_point_to_pass,
          lane_change_info.preview_route_result);
      PopulatePreviewRouteResultFromProto(
          *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
          lane_change_info_seed->target_region_preview_route_result(),
          lane_change_info.target_region_lane_point_to_pass,
          lane_change_info.target_region_preview_route_result);
    } else {
      debug_oss << "\n Get preview route result from routing API.";
      GetPreviewRerouteResult(world_model, lane_sequence_candidates,
                              lane_change_info.lane_change_instance,
                              /*is_source_region=*/true,
                              lane_change_info.lane_point_to_pass,
                              lane_change_info.preview_route_result, debug);
      GetPreviewRerouteResult(
          world_model, lane_sequence_candidates,
          lane_change_info.lane_change_instance, /*is_source_region=*/false,
          lane_change_info.target_region_lane_point_to_pass,
          lane_change_info.target_region_preview_route_result, debug);
      // TODO(elliot): Consider move it into GetPreviewRerouteResult to replace
      // it as kOptimalLaneChange preview route on source region.
      lane_change_info.preview_only_route_result =
          lane_change_info.lane_point_to_pass.has_value()
              ? lane_sequence_candidates.GetPreviewRouteResult(
                    *world_model.pnc_map_service(), world_model.regional_map(),
                    *world_model.global_route_solution(),
                    lane_change_info.lane_point_to_pass
                        .value(), /*compared_sequence_type=*/
                    lane_selection::PreviewRequestOptimalSequenceType::
                        kPreviewRouteOnly,
                    lane_selection::PreviewRouteSearchStrategy::
                        kDefaultByMinCost,
                    debug ? debug->mutable_preview_only_route_debug() : nullptr)
              : std::nullopt;
    }
  }

  // Calculate the lane change metadata.
  const pnc_map::Lane& current_lane = *DCHECK_NOTNULL(
      lane_change_info.lane_sequence_wo_lane_change->current_lane);
  lane_change_info.lane_change_metadata = CalculateLaneChangeMetadata(
      lane_change_info.lane_change_planning_segment_sequence,
      lane_change_info.lane_change_instance, current_lane,
      lane_sequence_wo_lane_change.cost_factors,
      *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
      previous_iter_seed, world_model.robot_state(),
      debug->mutable_lane_change_metadata_debug());

  if (lane_change_info.lane_change_info_level ==
      pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo) {
    // Generate all lc instances from planning segment sequence.
    lane_change_info.all_lane_change_instances =
        GenerateLaneChangeInstancesFromPlanningSegmentSequence(
            *DCHECK_NOTNULL(world_model.GetLatestJointPncMapService()),
            world_model.drivable_lane_reasoner(),
            lane_change_info.lane_change_planning_segment_sequence,
            lane_change_info.lane_change_instance.source_lane_sequence(),
            debug_oss);
  }

  // Analyze and populate lane change infos.
  lane_change_info.all_lane_follow_sequence =
      lane_sequence_candidates.GetAllLaneFollowSequenceResults();
  const double dist_to_destination =
      lane_change_info.lane_sequence_w_lane_change != nullptr
          ? lane_change_info.lane_sequence_w_lane_change
                ->dist_to_route_destination
      : lane_change_info.lane_sequence_wo_lane_change != nullptr
          ? lane_change_info.lane_sequence_wo_lane_change
                ->dist_to_route_destination
          : std::numeric_limits<double>::infinity();
  const bool should_latch_urgency_info =
      previous_iter_seed.selected_lane_change_state() ==
          pb::LANE_CHANGE_STATE_IN_PROGRESS ||
      (previous_iter_seed.selected_lane_change_state() ==
           pb::LANE_CHANGE_STATE_ABORT &&
       lane_change_info.is_lc_backup_sequence);
  const bool has_valid_preview_route =
      lane_selection::HasValidPreviewRouteResult(
          lane_change_info.preview_route_result) ||
      lane_selection::HasValidPreviewRouteResult(
          lane_change_info.preview_only_route_result);
  LaneChangeEnvAnalyzer::PopulateLaneChangeEnvInfo(
      world_model, previous_iter_seed,
      lane_sequence_wo_lane_change.lane_sequence,
      lane_sequence_wo_lane_change.cost_factors,
      lane_change_info.lane_change_sequence_type,
      lane_change_info.lane_change_instance,
      lane_change_info.lane_change_metadata,
      lane_sequence_wo_lane_change.current_lane,
      lane_change_info.all_lane_change_instances,
      lane_change_info.lane_change_info_level,
      lane_change_info.signal_source_type, dist_to_destination,
      should_latch_urgency_info, has_valid_preview_route,
      lane_change_info.lane_change_env_info, debug_oss);
  if (FLAGS_planning_enable_early_lane_change_reasoning) {
    early_lane_change_signal_reasoner.Reason(
        world_model, previous_iter_seed, lane_change_info,
        debug == nullptr
            ? nullptr
            : debug->mutable_early_lane_change_signal_info_debug());
  }
  // Reset early LC signal source type if should not propose early LC.
  if (lane_change_info.signal_source_type ==
          pb::LaneChangeSignalSourceType::
              kLaneChangeSignalFromEarlyLaneChange &&
      !lane_change_info.early_lane_change_signal_info
           .should_propose_early_lane_change) {
    lane_change_info.signal_source_type =
        pb::LaneChangeSignalSourceType::kLaneChangeSignalNone;
  }

  if (lane_change_info.lane_change_info_level ==
      pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo) {
    const pb::LaneChangeSequenceProposalMetadata&
        lc_sequence_proposal_metadata_seed =
            previous_iter_seed.lane_change_sequence_proposal_metadata();
    lane_change_info.is_inexecutable_lane_change_signal =
        lane_change_info.is_lc_backup_sequence &&
        lane_change_info.lane_change_sequence_type ==
            pb::LaneSequenceCandidate::ALTERNATIVE_LANE_CHANGE &&
        lc_sequence_proposal_metadata_seed.type() ==
            pb::LaneChangeSequenceProposalType::kBackupSequenceWoRoute;

    // Computation based on lane change env.
    // Latch last cl's crawl type when ego is in abort state.
    const bool lc_guide_seeds_is_valid =
        !previous_iter_seed.speed_seed().lc_guide_seed().empty() &&
        previous_iter_seed.speed_seed().lc_guide_seed()[0].is_valid();
    lane_change_info.is_last_lc_crawl =
        (previous_iter_seed.selected_lane_change_state() ==
             planner::pb::LaneChangeState::LANE_CHANGE_STATE_NONE ||
         previous_iter_seed.selected_lane_change_state() ==
             planner::pb::LaneChangeState::LANE_CHANGE_STATE_PREPARATION)
            ? false
        : previous_iter_seed.selected_lane_change_state() ==
                planner::pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS
            ? !lc_guide_seeds_is_valid
            : previous_iter_seed.lane_change_info_seed().is_last_lc_crawl();

    lane_change_info.can_trigger_crawl = CanTriggerLaneChangeCrawl(
        lane_change_info.urgency_infos(), previous_iter_seed,
        lane_change_info.is_inexecutable_lane_change_signal,
        lane_change_info.is_last_lc_crawl);

    lane_change_info.consecutive_no_gap_cycles =
        ComputeLaneChangeNoGapCycles(previous_iter_seed);

    lane_change_info.is_consecutive_cross_lane_in_same_direction =
        IsConsecutiveLaneChangeInSameDirection(
            lane_change_info.lane_change_instance,
            lane_change_info.lane_change_metadata,
            lane_change_info.all_lane_change_instances,
            lane_change_info.signal_source_type);

    lane_change_info.last_selected_lane_change_state =
        previous_iter_seed.selected_lane_change_state();

    lane_change_info.elc_triggered_object_id =
        previous_iter_seed.selection_seed()
            .elective_lane_change_decision()
            .trigger_object_id();
    const bool is_on_highway =
        current_lane.section()->road()->road_class() == hdmap::Road::EXPRESSWAY;
    lane_change_info.should_set_no_yield_for_elc_triggered_object =
        ShouldSetNoYieldForELCTriggeredObject(
            lane_change_info.lane_change_metadata,
            lane_change_info.lane_change_sequence_type,
            lane_change_info.urgency_score(),
            world_model.snapshot_timestamp() -
                previous_iter_seed.selection_seed()
                    .elective_lane_change_decision()
                    .trigger_timestamp(),
            is_on_highway, debug_oss);

    lane_change_info.is_difficult_lc_for_pullover = IsDifficultLcForPullover(
        previous_iter_seed.selected_lane_change_state(), lane_change_info,
        debug_oss);

    const LaneChangeRegionInfo* target_region_info =
        GetLaneChangeRegionInfoPtrByType(
            /*region_type=*/pb::LaneChangeRegionType::TARGET,
            lane_change_info.region_infos());
    lane_change_info.is_difficult_lc_in_the_near_future =
        IsDifficultLcInTheNearFuture(world_model.robot_state(),
                                     target_region_info->DistToNearestObject(),
                                     target_region_info->object_info_list);

    lane_change_info.should_restrict_lc =
        ShouldRestrictLaneChange(world_model, lane_change_info, pull_over_info,
                                 previous_iter_seed, debug_oss);

    lane_change_info.can_ego_finish_lane_change = CanEgoFinishLaneChange(
        world_model.global_route_solution(),
        lane_change_info.lane_change_instance,
        world_model.robot_state().current_state_snapshot(),
        lane_change_info.lane_change_metadata, lane_change_info.region_infos(),
        lane_change_info.is_lc_backup_sequence, debug_oss);
    debug_oss << "\ncan_ego_finish_lane_change="
              << lane_change_info.can_ego_finish_lane_change;

    const double ego_speed =
        world_model.robot_state().plan_init_state_snapshot().speed();
    lane_change_info.should_creep =
        FLAGS_planning_enable_lane_change_creep &&
        ego_speed < kMaxSpeedToEnableCreepInMps &&
        HasDenseTraffic(lane_change_info, ego_speed, debug_oss);
  }
  // Populate lane change info debug.
  PopulateLaneChangeInfoDebug(lane_change_info, debug);
  if (lane_change_info.lane_change_info_level ==
      pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo) {
    // Record rt events.
    std::string rt_message;
    rt_message = pb::LaneSequenceCandidate::LaneSequenceType_Name(
                     lane_change_info.lane_change_sequence_type) +
                 ", " +
                 pb::LaneChangeMode_Name(
                     lane_change_info.lane_change_instance.direction());
    if (!IsSimilarLaneChangeSignal(lane_change_info,
                                   previous_iter_seed.lane_change_info_seed(),
                                   rt_message)) {
      rt_event::PostRtEvent<rt_event::planner::PlannerLaneChangeSignal>(
          rt_message);
    }
  }
  return lane_change_info;
}

void GetPreviewRerouteResult(
    const WorldModel& world_model,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const LaneChangeInstance& lane_change_instance, const bool is_source_region,
    std::optional<lane_selection::PreviewLanePoint>& lane_point_to_pass,
    std::optional<lane_selection::PreviewRouteResult>& preview_route_result,
    pb::LaneChangeInfoDebug* debug) {
  TRACE_EVENT_SCOPE(planner, LaneChangeCommon_GetPreviewRerouteResult);

  lane_point_to_pass.reset();
  preview_route_result.reset();

  // Use source lane sequence's end as lane point in query.
  const std::vector<const pnc_map::Lane*>& lane_sequence =
      is_source_region ? lane_change_instance.source_lane_sequence()
                       : lane_change_instance.target_lane_sequence();
  if (lane_sequence.empty()) {
    LOG(INFO) << "[lane change][preview route]"
              << (is_source_region ? "Source" : "Target")
              << " lane sequence is empty";
    return;
  }
  const pnc_map::Lane* last_lane_of_lane_sequence = lane_sequence.back();
  DCHECK(last_lane_of_lane_sequence != nullptr);
  lane_point_to_pass = std::make_optional<lane_selection::PreviewLanePoint>(
      {.lane = last_lane_of_lane_sequence,
       .arc_length = last_lane_of_lane_sequence->length()});
  pb::PreviewRouteDebug* preview_route_debug =
      (debug != nullptr && is_source_region)
          ? debug->mutable_preview_route_debug()
          : nullptr;
  preview_route_result = lane_sequence_candidates.GetPreviewRouteResult(
      *world_model.pnc_map_service(), world_model.regional_map(),
      *world_model.global_route_solution(),
      lane_point_to_pass.value(), /*compared_sequence_type=*/
      lane_selection::PreviewRequestOptimalSequenceType::kOptimalLaneChange,
      lane_selection::PreviewRouteSearchStrategy::kDefaultByMinCost,
      preview_route_debug);
}

double GetLaneChangeComfortTimeWindow(const double lateral_displacement,
                                      const double max_lat_accel) {
  // Compute a_lat * T^2 / (2 * pi)= X_lat.
  const double pi_x_2 = 2.0 * lateral_displacement * M_PI / max_lat_accel;
  DCHECK_GE(pi_x_2, 0.0);
  // This is a factor to increase lane change time window to make it more
  // comfortable.
  constexpr double kLaneChangeWindowScalingFactor = 2.0;
  // TODO(Tingran): See whether we want to remove
  // kLaneChangeWindowScalingFactor.
  return std::sqrt(pi_x_2 * kLaneChangeWindowScalingFactor);
}

double GetLaneChangeDurationTime(const double lat_dist,
                                 const double init_lat_speed,
                                 const double max_lat_accel,
                                 const double max_lat_jerk) {
  // Check for positive lat_dist.
  if (lat_dist < math::constants::kEpsilon) {
    return 0.0;
  }

  // Compute lateral displacement w/ accel.
  const auto& compute_greedy_jerk_lc =
      [&init_lat_speed, &max_lat_jerk](
          const double max_lat_accel, double& lat_disp, double& max_lat_speed) {
        const double max_acc_time = max_lat_accel / max_lat_jerk;
        max_lat_speed = max_lat_accel * max_acc_time + init_lat_speed;
        // Early return if max_lat_accel is too small.
        if (max_lat_speed < math::constants::kEpsilon) {
          lat_disp = 0.0;
          return std::numeric_limits<double>::infinity();
        }
        const double max_dec_time = std::sqrt(max_lat_speed / max_lat_jerk);
        lat_disp = (max_lat_speed + init_lat_speed) * max_acc_time +
                   max_lat_speed * max_dec_time;
        return max_acc_time + max_dec_time;
      };

  // Initialize lateral displacement, max speed, and max acceleration.
  double min_accel = 0.0;
  double max_accel = max_lat_accel;
  double lat_disp = 0.0, max_lat_speed = 0.0;
  double half_time =
      compute_greedy_jerk_lc(max_lat_accel, lat_disp, max_lat_speed);
  // Fully acceleration + constant speed + deceleration.
  if (lat_disp < lat_dist) {
    return half_time * 2 + (lat_dist - lat_disp) / max_lat_speed;
  }

  // Hard to find the analytical solution, use binary search to obtain optimal
  // accel & lc duration time.
  constexpr double kMaxDistToleranceInMeter = 0.1;
  constexpr int64_t kMaxIterNum = 20;
  int64_t iter = 0;
  while (iter < kMaxIterNum &&
         (lat_disp < math::constants::kEpsilon ||
          std::abs(lat_disp - lat_dist) > kMaxDistToleranceInMeter)) {
    const double lat_accel = 0.5 * (min_accel + max_accel);
    half_time = compute_greedy_jerk_lc(lat_accel, lat_disp, max_lat_speed);
    min_accel = lat_disp > lat_dist ? min_accel : lat_accel;
    max_accel = lat_disp > lat_dist ? lat_accel : max_accel;
    iter++;
  }
  return half_time * 2;
}

// Gets lane sequence prefix id with given source type in seed. Returns
// nullopt if there isn't a matched prefix.
std::optional<int32_t> GetLaneSequencePrefixIdWithSourceType(
    const pb::DecoupledManeuverSeed& seed,
    const pb::LaneSequencePrefix::Source& source_type) {
  if (!seed.has_lane_sequence_proposal() ||
      !seed.lane_sequence_proposal().has_lane_change_latch_signal()) {
    return std::nullopt;
  }
  const auto& prefixes =
      seed.lane_sequence_proposal().lane_change_latch_signal().prefixes();
  auto matched_sequence_proposal_iter =
      std::find_if(prefixes.begin(), prefixes.end(),
                   [&source_type](const pb::LaneSequencePrefix& prefix) {
                     return prefix.source() == source_type;
                   });
  return matched_sequence_proposal_iter == prefixes.end()
             ? std::nullopt
             : std::make_optional(matched_sequence_proposal_iter->id());
}

std::set<pb::LaneSequencePrefix::Source> GetLaneSequencePrefixSources(
    const LaneSequenceResult* lane_sequence_result,
    const pb::DecoupledManeuverSeed& seed) {
  if (lane_sequence_result == nullptr ||
      lane_sequence_result->prefix_ids.empty()) {
    return {};
  }
  if (!seed.has_lane_sequence_proposal() ||
      !seed.lane_sequence_proposal().has_lane_change_latch_signal()) {
    return {};
  }
  std::set<pb::LaneSequencePrefix::Source> sources;
  const auto& lc_prefixes =
      seed.lane_sequence_proposal().lane_change_latch_signal().prefixes();
  const std::set<int32_t>& sequence_prefix_ids =
      lane_sequence_result->prefix_ids;
  size_t found_prefixes_count = 0;
  for (const auto& lc_prefix : lc_prefixes) {
    if (sequence_prefix_ids.find(lc_prefix.id()) == sequence_prefix_ids.end()) {
      continue;
    }
    sources.emplace(lc_prefix.source());
    if (++found_prefixes_count >= sequence_prefix_ids.size()) {
      break;
    }
  }
  return sources;
}

}  // namespace planner
