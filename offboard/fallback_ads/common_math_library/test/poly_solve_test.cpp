#include "common_math_library/poly_solve.h"

#include <gtest/gtest.h>

TEST(CMLSolveQuadraticEquation, CMLSolveQuadraticEquationTest) {
  float root_array[2] = {0.0F, 0.0F};
  uint8_t root_num = 2;

  // case 1: a = 1, b = -4, c = 3
  // two different roots
  EXPECT_TRUE(CML_SolveQuadraticEquation(1.0F, -4.0F, 3.0F, 0.0F, root_array,
                                         &root_num));
  EXPECT_FLOAT_EQ(root_array[0], 3.0F);
  EXPECT_FLOAT_EQ(root_array[1], 1.0F);
  EXPECT_EQ(root_num, 2);

  // case 2: a = 1, b = 2, c = 1
  // two same roots
  EXPECT_TRUE(CML_SolveQuadraticEquation(1.0F, 2.0F, 1.0F, 0.0F, root_array,
                                         &root_num));
  EXPECT_FLOAT_EQ(root_array[0], -1.0F);
  EXPECT_FLOAT_EQ(root_array[1], -1.0F);
  EXPECT_EQ(root_num, 1);

  // case 3: a = 1, b = 2, c = 3
  // no solution
  root_num = 2;
  EXPECT_TRUE(CML_SolveQuadraticEquation(1.0F, 2.0F, 3.0F, 0.0F, root_array,
                                         &root_num));
  EXPECT_FLOAT_EQ(root_array[0], 0.0F);
  EXPECT_FLOAT_EQ(root_array[1], 0.0F);
  EXPECT_EQ(root_num, 0);

  // case 4: a = 0, b = 2, c = 1
  // one root
  root_num = 2;
  EXPECT_TRUE(CML_SolveQuadraticEquation(0.0F, 2.0F, 1.0F, 0.0F, root_array,
                                         &root_num));
  EXPECT_FLOAT_EQ(root_array[0], -0.5F);
  EXPECT_FLOAT_EQ(root_array[1], 0.0F);
  EXPECT_EQ(root_num, 1);
}
