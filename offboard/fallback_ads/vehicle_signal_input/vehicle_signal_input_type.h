#ifndef OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_TYPE_H_
#define OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_TYPE_H_

#include <stdint.h>

typedef enum {
  NO_FAULT = 0,
  SYSTEM_ERROR = 2,     // common system error
  SYSTEM_ERROR_EB = 3,  // localization fault, EB allowed
  EMERGENCY_SWITCH_ON = 5,
  COLLISION_SLOW_BRAKING = 6,
  COLLISION_QUICK_BRAKING = 7,
  COLLISION_IMMEDIATELY_BRAKING = 8
} FallbackMode;

typedef enum {
  MANUAL = 0,      // Manual mode, ready to engage.
  TRANSITION = 1,  // Transition from manual to auto mode.
  AUTO_FULL = 2,   // Full autonomous mode..
} VehicleMode;

// keep the signals name the same as the DBC signals name
typedef struct {
  float PrimVehSpdGroupSafeNom;
  float PrimVehSpdGroupSafeNomQf;
  float PrimVLatSafeNom;
  float PrimALgtDataRawSafeNom;
  float PrimALgtDataRawSafeNomQf;
  float PrimALatDataRawSafeNom;
  float PrimALatDataRawSafeNomQf;
  float PrimYawRateSafeNom;
  float YawRate1;
  float YawRate1Qf1;
  float AdPrimWhlAgEstimdGroupSafeWhlAg;
  float AdPrimWhlAgEstimdGroupSafeWhlAgRate;
  float SteerWhlAgSafe;
  float SteerWhlAgSpdSafe;
  float SteerWhlSnsrQf;
  float PrimWhlAgSpdFrntSafeLe;
  uint8_t PrimWhlAgSpdFrntSafeLeQf;
  float PrimWhlAgSpdFrntSafeRi;
  float PrimWhlAgSpdFrntSafeRiQf;
  float PrimWhlAgSpdReSafeLe;
  float PrimWhlAgSpdReSafeLeQf;
  float PrimWhlAgSpdReSafeRi;
  float PrimWhlAgSpdReSafeRiQf;
  uint8_t PrimVehSpdGroupSafeMovDir;
  uint8_t WhlLockStsLockSts;
  uint8_t PrpsnTqDirAct;
  uint8_t IndcrTurnSts1WdSts;
  uint8_t AdActvnOkFromVehDyn;
  uint8_t AutnmsDrvModMngtExtSafeAutnmsDrvModSts1;
  uint8_t AcuFbReserved;
  uint8_t AcuFbRollingCounter;
  uint8_t AdNomALgtReqGroupSafeCntr_A;
  uint8_t AutnmsDrvStReqCounter_A;  // redundant, vcan4
  uint8_t ReqResleaseFbControl;
  uint8_t PrimVehSpdGroupSafeCntr;
  uint8_t PrimALgtDataRawSafeCntr;
  uint8_t AgDataRawSafeCntr;
  uint8_t BrkTqMinReqBrkTqMinReqCntr;
  uint8_t AdPrimWhlAgEstimdGroupSafeCntr;
  uint8_t PrimWhlAgSpdFrntSafeCntr;
  uint8_t BrkDegradedRdntCntr;
  uint8_t AdSecSteerStsSafeGroupCntr;
  uint8_t AdPrimALgtLimReqGroupSafeCntr_A;
  uint8_t AdPrimPoseCntr_A;
  uint8_t AdPrimWhlAgReqGroupSafeCntr_A;
  uint8_t AdSecALgtLimReqGroupSafeCntr_A;
  uint8_t AdSecWhlAgReqGroupSafeCntr_A;
} VehicleAndAcuCanSignal;

typedef struct {
  uint8_t VSI_VehMid3SsmCounter0;
  uint8_t VSI_VehMid3SsmCounter1;
  uint8_t VSI_VehMid3VcuCounter0;
  uint8_t VSI_VehMid3VcuCounter1;
  uint8_t VSI_VehMid5SsmCounter0;
  uint8_t VSI_VehMid5SsmCounter1;
  uint8_t VSI_VehMid6SsmCounter0;
  uint8_t VSI_VehMid6SsmCounter1;
  uint8_t VSI_AcuMid3SsmCounter0;
  uint8_t VSI_AcuMid3SsmCounter1;
  uint8_t VSI_AcuMid5SsmCounter0;
  uint8_t VSI_AcuMid5SsmCounter1;
  uint8_t VSI_AcuMid6SsmCounter0;
  uint8_t VSI_AcuMid6SsmCounter1;
  uint8_t VSI_AcuFbCanTimeout;
} McuCanCounter;

#endif  // OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_VEHICLE_SIGNAL_INPUT_TYPE_H_
