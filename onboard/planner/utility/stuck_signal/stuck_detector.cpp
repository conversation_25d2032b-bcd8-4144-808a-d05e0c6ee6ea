#include "planner/utility/stuck_signal/stuck_detector.h"

#include <absl/strings/str_cat.h>
#include <algorithm>
#include <glog/logging.h>
#include <limits>
#include <memory>
#include <queue>
#include <sstream>
#include <string>
#include <unordered_map>
#include <vector>

#include <boost/scope_exit.hpp>

#include "geometry/model/polygon_with_cache.h"
#include "math/math_util.h"
#include "planner/assist/assist_instruction.h"
#include "planner/elective_lane_change/elective_lane_change_decider_utils.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/stuck_signal/stuck_detector_utils.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace {
// The maximum step used to consider whether Ego is stuck due to physical
// boundary speed limit. 1 step corresponds to 0.5m.
constexpr int64_t kMaxStepToConsiderPhysicalBoundaryStuck = 15;

// The speed limit below which we can consider Ego is stuck by
// physical boundary.
constexpr double kSpeedLimitToConsiderEgoStuckByPhysicalBoundaryInMps = 0.3;
}  // namespace

StuckDetector::StuckDetector(bool enable_debug) : enable_debug_(enable_debug) {
  stuck_parameters_ = PlannerConfigCenter::GetInstance()
                          .GetDecoupledForwardManeuverConfig()
                          .trajectory_selection_config()
                          .hazardous_state_config()
                          .stuck_config();
}

// TODO(jinghuang1): Split stuck signal population logic into smaller functions
// based on different stuck reasons.
StuckSignalMetaData StuckDetector::Detect(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& candidate,
    const lane_selection::LaneSequenceCandidates& lane_sequence_candidates,
    const speed::pb::SpeedGeneratorConfig& speed_generator_config,
    const pb::AgentMapElementOccupancySeeds& agent_map_element_occupancy_seeds,
    const pb::StuckDetectorSeed& last_iteration_seed) {
  StuckSignalMetaData stuck_signal_meta;
  auto& stuck_signal = stuck_signal_meta.stuck_signal;
  stuck_signal.set_reason(pb::StuckSignal_Reason_kNoStuck);
  DCHECK_LE(last_iteration_seed.stuck_score(), 1.0);
  if (enable_debug_) {
    // Initialize a valid stuck debug.
    stuck_signal_meta.optional_stuck_debug =
        std::make_optional<pb::StuckDebug>();
  }

  const int64_t current_timestamp = world_model.snapshot_timestamp();
  // Get planner object map
  const std::unordered_map<ObjectId, PlannerObject>& planner_object_map =
      world_model.planner_object_map();
  double stuck_score = last_iteration_seed.stuck_score();

  std::optional<AssistInstruction> optional_assist_instruction;
  const auto& optional_unstuck_directives =
      world_model.assist_directive_generator().unstuck_directives();
  if (optional_unstuck_directives.has_value()) {
    optional_assist_instruction =
        optional_unstuck_directives->assist_instruction;
  }
  if (FLAGS_planning_enable_populate_stuck_signal_based_on_ra_instruction &&
      optional_assist_instruction.has_value() &&
      !(optional_assist_instruction->xlane_nudge_bypass_objects.empty())) {
    // If there is RA certified stuck object, populate
    // it into stuck signal directly.
    DCHECK(optional_assist_instruction->xlane_nudge_bypass_objects.size() == 1);
    const TypedObjectId stuck_object_id =
        optional_assist_instruction->xlane_nudge_bypass_objects[0];
    std::optional<math::geometry::PolygonWithCache2d>
        optional_stuck_object_contour =
            GetObjectContour(world_model, stuck_object_id);
    DCHECK(optional_stuck_object_contour.has_value());
    stuck_signal.set_reason(pb::StuckSignal_Reason_kRemoteAssistTaggedObject);
    math::UpdateMax(1.0, stuck_score);
    PopulateStuckSignal(candidate.lane_sequence_result(),
                        *optional_stuck_object_contour, stuck_object_id.id,
                        current_timestamp, stuck_score, last_iteration_seed,
                        stuck_signal_meta);
    return stuck_signal_meta;
  }

  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      world_model.robot_state().car_model_with_shape().shape_measurement();
  CHECK_GT(ego_shape.length() - ego_shape.rear_bumper_to_rear_axle(), 0);
  const double ra_to_fb_shift =
      ego_shape.length() - ego_shape.rear_bumper_to_rear_axle();

  const speed::DiscomfortVaryingMinRange min_range(
      speed_generator_config.for_car_type().min_range());
  const std::optional<TypedObjectId> typed_object_id = GetStuckConstraintId(
      candidate, min_range, planner_object_map,
      /*min_range_max_required_lat_gap=*/
      speed_generator_config.for_search().min_range_max_required_lat_gap(),
      ra_to_fb_shift);

  const bool is_ego_at_end_of_lane_sequence = IsEgoAtEndOfLaneSequence(
      candidate, world_model.pose(), stuck_parameters_);
  if (enable_debug_) {
    AddStuckSignalDebugInfo(
        /*debug_name=*/"dominant_constraint_object_id: ",
        /*debug_value=*/typed_object_id.has_value()
            ? absl::StrCat(typed_object_id->id)
            : "None",
        /*sub_level_debug_list=*/{},
        stuck_signal_meta.optional_stuck_debug.value());
    AddStuckSignalDebugInfo(
        /*debug_name=*/"is_ego_at_end_of_lane_sequence: ",
        /*debug_value=*/absl::StrCat(is_ego_at_end_of_lane_sequence),
        /*sub_level_debug_list=*/{},
        stuck_signal_meta.optional_stuck_debug.value());
  }

  const math::geometry::PolygonWithCache2d ego_contour =
      world_model.robot_state()
          .current_state_snapshot()
          .bounding_box()
          .ToPolygonWithCache2d();
  if (is_ego_at_end_of_lane_sequence) {
    stuck_signal.set_reason(pb::StuckSignal_Reason_kEndOfLaneSequence);
    math::UpdateMax(1.0, stuck_score);
    PopulateStuckSignal(candidate.lane_sequence_result(), ego_contour, 0,
                        current_timestamp, stuck_score, last_iteration_seed,
                        stuck_signal_meta);
    return stuck_signal_meta;
  }

  const RobotStateSnapshot& ego_state_snapshot =
      world_model.robot_state().current_state_snapshot();
  if (!typed_object_id.has_value()) {
    // Check whether Ego is stuck due to physical boundary.
    const auto physical_boundary_stop_point_iter = std::find_if(
        candidate.speed_result.constraints().begin(),
        candidate.speed_result.constraints().end(), [](const auto& it) {
          // Check if constraint has physical boundary fence type.
          return it.fence_type == speed::pb::kLeftSideHardRoadBoundary ||
                 it.fence_type == speed::pb::kRightSideHardRoadBoundary;
        });

    const auto& hb_cautious_speed_limits =
        candidate.speed_result.speed_pipeline_meta()
            .hard_boundary_cautious_speed_limits;
    const auto is_physical_boundary_speed_limit_zero =
        [](const auto& boundary_speed_limits) {
          if (boundary_speed_limits.speed_limits().empty()) {
            return false;
          }
          for (const auto& single_limit :
               boundary_speed_limits.speed_limits()) {
            if (single_limit.ix() > kMaxStepToConsiderPhysicalBoundaryStuck) {
              break;
            }
            if (math::NearZero(single_limit.speed_limit().max_discomfort_v())) {
              return true;
            }
          }
          return false;
        };

    const bool has_physical_boundary_obstruction =
        (physical_boundary_stop_point_iter !=
         candidate.speed_result.constraints().end()) ||
        (is_physical_boundary_speed_limit_zero(
             hb_cautious_speed_limits.first) ||
         is_physical_boundary_speed_limit_zero(
             hb_cautious_speed_limits.second));
    if (ego_state_snapshot.speed() <
            kSpeedLimitToConsiderEgoStuckByPhysicalBoundaryInMps &&
        has_physical_boundary_obstruction) {
      // Ego is stuck due to physical boundary speed limit.
      stuck_signal.set_reason(pb::StuckSignal_Reason_kPhysicalBoundary);
      math::UpdateMax(0.0, stuck_score);
      PopulateStuckSignal(candidate.lane_sequence_result(), ego_contour, 0,
                          current_timestamp, stuck_score, last_iteration_seed,
                          stuck_signal_meta);
      rt_event::PostRtEvent<
          rt_event::planner::PhysicalBoundaryStuckTriggered>();
      return stuck_signal_meta;
    }

    stuck_signal.set_last_stuck_timestamp(
        last_iteration_seed.last_stuck_timestamp());
    if (enable_debug_) {
      *(stuck_signal_meta.optional_stuck_debug->mutable_stuck_signal()) =
          stuck_signal;
    }
    return stuck_signal_meta;
  }
  stuck_signal_meta.optional_stuck_object_id = typed_object_id;

  if (typed_object_id->is_construction_zone()) {
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        construction_zone_ptr_map = world_model.construction_zone_ptr_map();
    const auto iter = construction_zone_ptr_map.find(typed_object_id->id);
    if (iter != construction_zone_ptr_map.end()) {
      stuck_signal.set_reason(pb::StuckSignal_Reason_kConstructionZone);
      math::UpdateMax(1.0, stuck_score);
      PopulateStuckSignal(candidate.lane_sequence_result(),
                          iter->second->contour, typed_object_id->id,
                          current_timestamp, stuck_score, last_iteration_seed,
                          stuck_signal_meta);
      return stuck_signal_meta;
    }
  }

  if (typed_object_id->type == pb::ObjectSourceType::kTrackedObject) {
    if (planner_object_map.find(typed_object_id->id) !=
        planner_object_map.end()) {
      const PlannerObject& object = planner_object_map.at(typed_object_id->id);
      stuck_signal_meta.is_stuck_object_slow =
          (object.tracked_object().velocity() <
           stuck_parameters_.stuck_speed());

      std::optional<math::geometry::PolygonWithCache2d> contour =
          GetObjectContour(world_model, typed_object_id);
      DCHECK(contour.has_value());

      const auto& fod_intention_map =
          candidate.intent_result().fod_intention_map();
      const auto fod_intention_iter =
          fod_intention_map.find(typed_object_id->id);
      const bool is_undercarriage_over_fod =
          fod_intention_iter != fod_intention_map.end()
              ? fod_intention_iter->second ==
                    pb::IntentionResult::UNDERCARRIAGE_OVER
              : false;

      if (enable_debug_) {
        AddStuckSignalDebugInfo(
            /*debug_name=*/"is_stuck_object_blockage: ",
            /*debug_value=*/absl::StrCat(object.is_blockage_object()),
            /*sub_level_debug_list=*/{},
            stuck_signal_meta.optional_stuck_debug.value());
      }

      if (is_undercarriage_over_fod) {
        // If the stuck object is under carriage over fod, populate nostuck
        // since we can go through it.
        stuck_signal.set_reason(pb::StuckSignal_Reason_kNoStuck);
        stuck_signal.set_last_stuck_timestamp(
            last_iteration_seed.last_stuck_timestamp());
        stuck_signal.add_object_ids(typed_object_id->id);
        if (enable_debug_) {
          *(stuck_signal_meta.optional_stuck_debug->mutable_stuck_signal()) =
              stuck_signal;
        }
        return stuck_signal_meta;
      }

      if (object.is_blockage_object()) {
        stuck_signal.set_reason(pb::StuckSignal_Reason_kBlockageObject);
        math::UpdateMax(1.0, stuck_score);
        PopulateStuckSignal(candidate.lane_sequence_result(), *contour,
                            typed_object_id->id, current_timestamp, stuck_score,
                            last_iteration_seed, stuck_signal_meta);
        return stuck_signal_meta;
      }

      const std::vector<TrafficJamVehicleMetaData> vehicles_on_lane_sequence =
          GenerateVehiclesOnLaneSequence(world_model, candidate,
                                         stuck_parameters_);
      const double distance_to_junction =
          CalculateEgoDistanceToJunction(ego_state_snapshot, candidate);

      const bool is_stuck_object_temp_parking = IsStuckObjectTempParking(
          object, world_model, candidate, vehicles_on_lane_sequence,
          distance_to_junction);

      if (enable_debug_) {
        AddStuckSignalDebugInfo(
            /*debug_name=*/"stuck_object_distance_to_junction: ",
            /*debug_value=*/
            absl::StrCat(std::min(distance_to_junction, 2000.0)),
            /*sub_level_debug_list=*/{},
            stuck_signal_meta.optional_stuck_debug.value());
        AddStuckSignalDebugInfo(
            /*debug_name=*/"is_stuck_object_temp_parking: ",
            /*debug_value=*/
            absl::StrCat(is_stuck_object_temp_parking),
            /*sub_level_debug_list=*/{},
            stuck_signal_meta.optional_stuck_debug.value());
        std::string vehicles_on_lane_sequence_debug;
        for (size_t i = 0; i < vehicles_on_lane_sequence.size(); ++i) {
          absl::StrAppend(
              &vehicles_on_lane_sequence_debug, " arclength: ",
              absl::StrCat(vehicles_on_lane_sequence[i].arclength_m), ", id: ",
              absl::StrCat(
                  DCHECK_NOTNULL(vehicles_on_lane_sequence[i].object)->id()));
        }
        AddStuckSignalDebugInfo(
            /*debug_name=*/"vehicles_on_lane_sequence: ",
            /*debug_value=*/
            vehicles_on_lane_sequence_debug,
            /*sub_level_debug_list=*/{},
            stuck_signal_meta.optional_stuck_debug.value());
      }

      if (is_stuck_object_temp_parking) {
        stuck_signal.set_reason(pb::StuckSignal_Reason_kNotBlockageObject);
        math::UpdateMax(1.0, stuck_score);
        PopulateStuckSignal(candidate.lane_sequence_result(), *contour,
                            typed_object_id->id, current_timestamp, stuck_score,
                            last_iteration_seed, stuck_signal_meta);
        rt_event::PostRtEvent<
            rt_event::planner::PositiveTempParkingStuckSignal>("");
        return stuck_signal_meta;
      }

      const bool is_stuck_object = IsObjectPersistentSlow(
          object, last_iteration_seed, stuck_parameters_.stuck_speed(),
          stuck_parameters_.state_persistent(), current_timestamp);
      if (enable_debug_) {
        AddStuckSignalDebugInfo(
            /*debug_name=*/"is_stuck_object_consistently_slow: ",
            /*debug_value=*/absl::StrCat(is_stuck_object),
            /*sub_level_debug_list=*/{},
            stuck_signal_meta.optional_stuck_debug.value());
      }
      const TrafficJamStuckMetaData meta_data = IsWaitingInTrafficJam(
          world_model, distance_to_junction,
          world_model.traffic_light_detection(), candidate, stuck_parameters_,
          vehicles_on_lane_sequence, is_stuck_object, object,
          ego_state_snapshot, *contour, lane_sequence_candidates, min_range,
          planner_object_map, agent_map_element_occupancy_seeds,
          last_iteration_seed, stuck_signal_meta.optional_stuck_debug);

      if ((is_stuck_object && !meta_data.is_ego_waiting_in_traffic_jam) ||
          (meta_data.is_ego_stuck_inside_junction)) {
        stuck_signal.set_reason(pb::StuckSignal_Reason_kNotBlockageObject);
        math::UpdateMax(meta_data.is_ego_stuck_inside_junction ? 1.0 : 0.75,
                        stuck_score);
        PopulateStuckSignal(candidate.lane_sequence_result(), *contour,
                            typed_object_id->id, current_timestamp, stuck_score,
                            last_iteration_seed, stuck_signal_meta);
        return stuck_signal_meta;
      }

      if (!is_stuck_object) {
        // If the object is not causing stuck, try to target the non-dominant
        // stuck object ahead.
        const std::optional<TypedObjectId> optional_non_domiant_stuck_object =
            IdentifyNonDominantStuckObject(
                candidate.speed_result, planner_object_map, last_iteration_seed,
                stuck_parameters_.stuck_speed());

        if (optional_non_domiant_stuck_object.has_value()) {
          const auto stuck_reason =
              optional_non_domiant_stuck_object->is_construction_zone()
                  ? pb::StuckSignal_Reason_kConstructionZone
                  : pb::StuckSignal_Reason_kBlockageObject;
          stuck_signal.set_reason(stuck_reason);
          math::UpdateMax(1.0, stuck_score);
          PopulateStuckSignal(candidate.lane_sequence_result(), *contour,
                              optional_non_domiant_stuck_object->id,
                              current_timestamp, stuck_score,
                              last_iteration_seed, stuck_signal_meta);
          return stuck_signal_meta;
        }
      }
      stuck_signal_meta.optional_is_ego_waiting_in_traffic =
          std::make_optional<bool>(meta_data.is_ego_waiting_in_traffic_jam);
    }
  }
  stuck_signal.set_last_stuck_timestamp(
      last_iteration_seed.last_stuck_timestamp());
  stuck_signal.add_object_ids(typed_object_id->id);
  if (enable_debug_) {
    *(stuck_signal_meta.optional_stuck_debug->mutable_stuck_signal()) =
        stuck_signal;
  }

  return stuck_signal_meta;
}

void StuckDetector::PopulateStuckSignal(
    const LaneSequenceResult& lane_seq_res,
    const math::geometry::PolygonWithCache2d& object_contour,
    const ObjectId& object_id, int64_t current_timestamp, double stuck_score,
    const pb::StuckDetectorSeed& last_iteration_seed,
    StuckSignalMetaData& stuck_signal_meta) {
  // TODO(jinghuang1): Add stuck score escalation logic.
  auto& stuck_signal = stuck_signal_meta.stuck_signal;
  stuck_signal.set_stuck_score(stuck_score);
  stuck_signal.set_last_stuck_timestamp(current_timestamp);
  stuck_signal.add_object_ids(object_id);
  const int64_t stuck_start_timestamp =
      last_iteration_seed.stuck_start_time() == 0
          ? current_timestamp
          : last_iteration_seed.stuck_start_time();
  stuck_signal.set_start_timestamp(stuck_start_timestamp);

  const pnc_map::Lane* associated_lane = nullptr;
  for (const pnc_map::Lane* lane : lane_seq_res.lane_sequence) {
    if (math::geometry::Intersects(lane->border(), object_contour)) {
      associated_lane = lane;
      break;
    }
  }
  if (enable_debug_) {
    AddStuckSignalDebugInfo(
        /*debug_name=*/"is_stuck_object_in_associated_lane: ",
        /*debug_value=*/absl::StrCat((associated_lane != nullptr)),
        /*sub_level_debug_list=*/{},
        stuck_signal_meta.optional_stuck_debug.value());
  }

  if (associated_lane == nullptr) {
    // If stuck object is not intersecting with lane sequence, just use current
    // lane to calculate the arclength range of the stuck object. Stuck object
    // might not intersect with lane sequence when Ego's yaw angle is not
    // aligned with lane direction.
    associated_lane = lane_seq_res.current_lane;
  }

  double arc_length = std::numeric_limits<double>::max();
  for (const auto& pt : object_contour.points()) {
    arc_length = std::min(
        arc_length, associated_lane->center_line()
                        .GetProximity(pt, math::pb::UseExtensionFlag::kForbid)
                        .arc_length);
  }
  stuck_signal.set_region_start_arclength(arc_length);

  bool add_lane_flag = false;
  for (const pnc_map::Lane* lane : lane_seq_res.lane_sequence) {
    if (!add_lane_flag && lane->id() != lane_seq_res.current_lane->id()) {
      continue;
    }
    stuck_signal.add_lane_path(lane->id());
    add_lane_flag = true;
    if (lane->id() == associated_lane->id()) {
      break;
    }
  }

  if (enable_debug_) {
    *(stuck_signal_meta.optional_stuck_debug->mutable_stuck_signal()) =
        stuck_signal;
  }
}

}  // namespace planner
