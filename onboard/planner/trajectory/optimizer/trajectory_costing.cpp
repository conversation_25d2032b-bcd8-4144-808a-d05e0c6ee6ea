#include "planner/trajectory/optimizer/trajectory_costing.h"

#include "planner/trajectory/optimizer/trajectory_costing_util.h"
#include "planner/utility/physics/motion_model_1d.h"

namespace {

using planner::primitive::TimeInvariantTrajectoryPrimitives;
using planner::primitive::TimeVariantTrajectoryPrimitives;

using State = planner::TrajectoryFormula::State;
using Control = planner::TrajectoryFormula::Control;
using StateIndex = planner::TrajectoryFormula::StateIndex;

using Feature = planner::TrajectoryFormula::Feature;
using PbCostingTerm = planner::TrajectoryFormula::PbCostingTerm;
using PbFeatureType = planner::TrajectoryFormula::PbFeatureType;
using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
using PbFeatureParameter = planner::TrajectoryFormula::PbFeatureParameter;

using ClearanceType = planner::trajectory::ClearanceType;
using NudgeType = planner::trajectory::NudgeType;

// By default, we don't add additional side buffer when computing violations.
constexpr double kDefaultSideBufferInMeters = 0.0;

// By default, we don't decay nudge weights.
constexpr double kDefaultNudgeDecayRatio = 1.0;
}  // namespace

namespace planner {

planner::pb::TrajectoryFeatureParameter MergeWithMotionModelParams(
    planner::pb::TrajectoryFeatureParameter feature_param,
    const vehicle_model::CarModel::PbMotionModelParam& param,
    const vehicle_model::CarModelWithAxleRectangularShape::PbShapeMeasurement&
        shape_measurement) {
  *feature_param.mutable_motion_model_param() = param;
  *feature_param.mutable_shape_measurement() = shape_measurement;
  return feature_param;
}

}  // namespace planner

namespace control_util {
template <>
planner::TrajectoryFormula::StepwisePrimitives
planner::TrajectoryFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const {
  (void)step;
  (void)is_terminal;
  (void)u;
  (void)primitives;
  return {.ego_representation = vehicle_model::VehicleRepresentation(
              feature_param_.shape_measurement(), x[StateIndex::X_POS],
              x[StateIndex::Y_POS], x[StateIndex::THETA],
              /*disk_numbers=*/{3})};
}

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kTrajectory>(
    int step, int total_step_size, const PbCostingTerm& costing_term,
    const PbFeatureParameter& feature_param, const State& x, const Control& u,
    const typename planner::TrajectoryFormula::StepwisePrimitives&
        stepwise_primitives,
    const planner::TrajectoryFormula::Primitives& primitives,
    planner::TrajectoryFormula::Feature* feature) {
  const auto& feature_type = costing_term.feature_type();
  const TimeVariantTrajectoryPrimitives& time_variant =
      primitives.time_variant[step];
  const TimeInvariantTrajectoryPrimitives& time_invariant =
      primitives.time_invariant;

  const std::vector<vehicle_model::DiskInfo>& ego_disks =
      stepwise_primitives.ego_representation.disks().at(3);
  const math::geometry::OrientedBoxWithCache2d& ego_box =
      stepwise_primitives.ego_representation.box();
  bool is_terminal = (step == total_step_size);

  switch (feature_type) {
    case PbFeatureType::CONSTRAINT_DELTA:
      planner::trajectory::ConstraintDelta(
          x, feature_param.motion_model_param().limit(), feature);
      break;
    case PbFeatureType::CONSTRAINT_OMEGA:
      planner::trajectory::ConstraintOmega(
          x, feature_param.motion_model_param(),
          feature_param.mild_juke_steering_rate_limit(),
          feature_param.min_speed_steering_rate_limit(), feature);
      break;
    case PbFeatureType::CONSTRAINT_ALPHA:
      planner::trajectory::ConstraintAlpha(
          u, feature_param.motion_model_param().limit(), is_terminal, feature);
      break;
    case PbFeatureType::CONSTRAINT_SPEED:
      planner::trajectory::ConstraintSpeed(
          x, feature_param.motion_model_param().limit(), feature);
      break;
    case PbFeatureType::CONSTRAINT_ACCEL:
      planner::trajectory::ConstraintAccel(
          x, feature_param.motion_model_param().limit(), feature);
      break;
    case PbFeatureType::CONSTRAINT_JERK:
      planner::trajectory::ConstraintJerk(
          u, feature_param.motion_model_param().limit(), is_terminal, feature);
      break;
    case PbFeatureType::CONSTRAINT_LATERAL_ACCEL:
      planner::trajectory::ConstraintLateralAccel(
          x, feature_param.motion_model_param(),
          feature_param.extreme_lateral_acceleration(), feature);
      break;
    case PbFeatureType::CONSTRAINT_JUKE:
      planner::trajectory::ConstraintJuke(x, feature_param.motion_model_param(),
                                          feature_param.extreme_juke(),
                                          feature);
      break;
    case PbFeatureType::EFFORT_DELTA:
      planner::trajectory::EffortDelta(x, feature);
      break;
    case PbFeatureType::EFFORT_OMEGA:
      planner::trajectory::EffortOmega(x, feature);
      break;
    case PbFeatureType::EFFORT_ALPHA:
      planner::trajectory::EffortAlpha(u, is_terminal, feature);
      break;
    case PbFeatureType::EFFORT_ACCELERATION:
      planner::trajectory::EffortAcceleration(x, feature);
      break;
    case PbFeatureType::EFFORT_DECELERATION:
      planner::trajectory::EffortDeceleration(x, feature);
      break;
    case PbFeatureType::EFFORT_POSITIVE_JERK:
      planner::trajectory::EffortPositiveJerk(u, is_terminal, feature);
      break;
    case PbFeatureType::EFFORT_NEGATIVE_JERK:
      planner::trajectory::EffortNegativeJerk(u, is_terminal, feature);
      break;
    case PbFeatureType::EFFORT_NEGATIVE_COMFORT_JERK:
      planner::trajectory::EffortNegativeComfortJerk(
          u, feature_param.negative_comfort_jerk_limit(), is_terminal, feature);
      break;
    case PbFeatureType::EFFORT_LATERAL_ACCEL:
      planner::trajectory::EffortLateralAccel(
          x, feature_param.motion_model_param(), feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_STOP_LINE:
      planner::trajectory::PrimitiveViolationStopLine(
          x, time_variant.nearest_stop_fence,
          feature_param.stop_line_violation_buffer(), feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_SPEED_ZONE:
      planner::trajectory::PrimitiveViolationSpeedZone(
          x, time_invariant.hard_speed_zones, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_ACC_SAFETY:
      planner::trajectory::PrimitiveViolationAccSafety(
          x, time_variant.acc_fences_collection, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_PUSH_SAFETY:
      planner::trajectory::PrimitiveViolationPushSafety(
          x, time_variant.furthest_push_fence, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_LEFT_ROAD_BOUNDARY:
      planner::trajectory::
          ComputePolylineViolationFeatureByEgoClearanceCorridor(
              x, feature_param.shape_measurement(), math::pb::kLeft,
              *DCHECK_NOTNULL(time_invariant.lane_geometry.road_corridor),
              time_invariant.lane_geometry.nominal_path.get(),
              feature_param.safety_distance_to_road_boundary(), feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_RIGHT_ROAD_BOUNDARY:
      planner::trajectory::
          ComputePolylineViolationFeatureByEgoClearanceCorridor(
              x, feature_param.shape_measurement(), math::pb::kRight,
              *DCHECK_NOTNULL(time_invariant.lane_geometry.road_corridor),
              time_invariant.lane_geometry.nominal_path.get(),
              feature_param.safety_distance_to_road_boundary(), feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_LEFT_ROAD_BOUNDARY_IN_JUNCTION:
      planner::trajectory::
          ComputeInJunctionPolylineViolationFeatureByEgoClearanceCorridor(
              x, primitives.plan_start_arclength,
              feature_param.shape_measurement(), math::pb::kLeft,
              time_invariant.lane_geometry,
              feature_param.comfort_distance_to_road_boundary_in_junction(),
              feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_RIGHT_ROAD_BOUNDARY_IN_JUNCTION:
      planner::trajectory::
          ComputeInJunctionPolylineViolationFeatureByEgoClearanceCorridor(
              x, primitives.plan_start_arclength,
              feature_param.shape_measurement(), math::pb::kRight,
              time_invariant.lane_geometry,
              feature_param.comfort_distance_to_road_boundary_in_junction(),
              feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_LEFT_STATIC_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kNegative,
          time_invariant.static_nudge.left_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_RIGHT_STATIC_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kPositive,
          time_invariant.static_nudge.right_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_LEFT_STATIC_IN_LANE_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kNegative,
          time_invariant.static_nudge.left_in_lane_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_RIGHT_STATIC_IN_LANE_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kPositive,
          time_invariant.static_nudge.right_in_lane_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_LEFT_DYNAMIC_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kNegative,
          time_variant.dynamic_nudge.left_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_RIGHT_DYNAMIC_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kPositive,
          time_variant.dynamic_nudge.right_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_LEFT_DYNAMIC_IN_LANE_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kNegative,
          time_variant.dynamic_nudge.left_in_lane_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_RIGHT_DYNAMIC_IN_LANE_NUDGE:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kPositive,
          time_variant.dynamic_nudge.right_in_lane_nudge_boundary.get(),
          kDefaultSideBufferInMeters, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_REWARD_STUCK_ESCAPE:
      planner::trajectory::PrimitiveSoftRewardProgress(
          x, feature_param.motion_model_param().limit(),
          time_variant.acc_fences_collection, time_variant.nearest_stop_fence,
          time_invariant.pull_over_target_pose, step,
          feature_param.stop_line_violation_buffer(),
          /* is_terminal=*/true, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_REWARD_PROGRESS:
      planner::trajectory::PrimitiveSoftRewardProgress(
          x, feature_param.motion_model_param().limit(),
          time_variant.acc_fences_collection, time_variant.nearest_stop_fence,
          time_invariant.pull_over_target_pose, step,
          feature_param.stop_line_violation_buffer(), is_terminal, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_NOMINAL_ATTRACTION:
      planner::trajectory::PrimitiveSoftNominalAttraction(
          x, ego_box.center_segment().end(),
          feature_param.shape_measurement().length() -
              feature_param.shape_measurement().rear_bumper_to_rear_axle(),
          /*is_reverse=*/false, time_invariant.lane_geometry.nominal_path.get(),
          feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_STOP_LINE_PULL:
      planner::trajectory::PrimitiveSoftStopLinePull(
          x, time_variant.nearest_stop_fence, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_ACC_COMFORT:
      planner::trajectory::PrimitiveSoftAccComfort(
          x, time_variant.acc_fences_collection, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_ACC_PREFERRED:
      planner::trajectory::PrimitiveSoftAccPreferred(
          x, time_variant.acc_fences_collection, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_PUSH_COMFORT:
      planner::trajectory::PrimitiveSoftPushComfort(
          x, time_variant.furthest_push_fence, feature);
      break;
    case PbFeatureType::PRIMITIVE_VIOLATION_NUDGE_SAFETY_PADDING:
      planner::trajectory::ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          time_invariant.static_nudge.nudge_geometric_paddings,
          NudgeType::kSafety, kDefaultNudgeDecayRatio,
          /*init_weight_adjust=*/2.0, feature);
      planner::trajectory::ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          time_variant.dynamic_nudge.nudge_geometric_paddings,
          NudgeType::kSafety,
          planner::trajectory::GetNudgeDecayRatio(
              step, feature_param.nudge_decay_rate()),
          /*init_weight_adjust=*/1.0, feature);
      break;

    case PbFeatureType::PRIMITIVE_SOFT_NUDGE_COMFORT_PADDING:
      planner::trajectory::ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          time_invariant.static_nudge.nudge_geometric_paddings,
          NudgeType::kComfort, kDefaultNudgeDecayRatio,
          /*init_weight_adjust=*/1.0, feature);
      planner::trajectory::ComputeGeometricPaddingViolationFeatureByEgoBox(
          ego_box, feature_param.shape_measurement(),
          time_variant.dynamic_nudge.nudge_geometric_paddings,
          NudgeType::kComfort,
          planner::trajectory::GetNudgeDecayRatio(
              step, feature_param.nudge_decay_rate()),
          /*init_weight_adjust=*/1.0, feature);
      break;

    case PbFeatureType::PRIMITIVE_SOFT_SPEED_ZONE:
      planner::trajectory::PrimitiveViolationSpeedZone(
          x, time_invariant.soft_speed_zones, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_LEFT_LANE_BOUNDARY:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kNegative,
          time_invariant.lane_geometry.left_lane_boundary.get(),
          feature_param.safety_distance_to_lane_boundary(), feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_RIGHT_LANE_BOUNDARY:
      planner::trajectory::ComputePolylineViolationFeatureByEgoDisks(
          ego_disks, x[StateIndex::THETA], ClearanceType::kPositive,
          time_invariant.lane_geometry.right_lane_boundary.get(),
          feature_param.safety_distance_to_lane_boundary(), feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TRACK_POSITION:
      planner::trajectory::PrimitiveTrackOptionalPosition(
          x, time_variant.target_pose, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TRACK_HEADING:
      planner::trajectory::PrimitiveTrackOptionalHeading(
          x, time_variant.target_pose, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TRACK_STOP_HEADING:
      planner::trajectory::PrimitiveTrackStopHeading(
          x, time_variant.acc_fences_collection,
          time_variant.nearest_stop_fence,
          feature_param.static_acc_speed_threshold(),
          feature_param.stop_cost_distance_buffer(),
          math::Degree2Radian(
              feature_param.stop_heading_angle_track_threshold()),
          feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TRACK_STOP_LINE_STEERING:
      planner::trajectory::PrimitiveTrackStopLineSteering(
          x, time_variant.nearest_stop_fence,
          feature_param.stop_cost_distance_buffer(), feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_PULL_OVER_TRACK_HEADING:
      planner::trajectory::PrimitiveTrackPullOverHeading(
          x, time_invariant.pull_over_target_pose, is_terminal, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_PULL_OVER_TRACK_POSITION:
      planner::trajectory::PrimitiveTrackPullOverPosition(
          x, time_invariant.pull_over_target_pose, is_terminal, feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TERMINAL_ACC_SPEED_GUIDANCE:
      planner::trajectory::ComputeTerminalAccSafetySpeedGuidance(
          x, time_variant.acc_fences_collection, is_terminal,
          feature_param.acc_speed_zone_entry_decel(), feature);
      break;
    case PbFeatureType::PRIMITIVE_SOFT_TERMINAL_STOP_LINE_SPEED_GUIDANCE:
      planner::trajectory::ComputeTerminalStopLineSpeedGuidance(
          x, time_variant.nearest_stop_fence, is_terminal,
          feature_param.stop_line_speed_zone_entry_decel(), feature);
      break;
    default:
      DCHECK(false) << "Unrecognized feature type";
      return;
  }
}

}  // namespace control_util
