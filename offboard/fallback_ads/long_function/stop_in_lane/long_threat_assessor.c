#include "long_function/stop_in_lane/long_threat_assessor.h"

#include <math.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "common_math_library/interpolation.h"
#include "common_math_library/vehicle_parameters.h"
#include "long_function/long_function_parameter.h"
#include "long_function/long_function_type.h"
#include "long_function/stop_in_lane/avoidance_deceleration.h"
#include "long_function/stop_in_lane/free_stop_control.h"
#include "long_function/stop_in_lane/time_to_collision.h"

static MotionState GetVehicleMotionState(
    const VSP_VehicleSignal_Struct* vehicle_signal_ptr) {
  MotionState ego_state = {0.0F, 0.0F, 0.0F};

  ego_state.velocity_x = vehicle_signal_ptr->VSP_LgtVehSpdFild;

  ego_state.accel_x = vehicle_signal_ptr->VSP_LgtVehAccFild;
  return ego_state;
}

static MotionState GetObjectMotionState(
    const OPS_ObjectList_Struct* ops_object_list_ptr) {
  MotionState object_state = {0.0F, 0.0F, 0.0F};

  object_state.distance_x = ops_object_list_ptr->OPS_MainObjectDx;

  object_state.velocity_x = ops_object_list_ptr->OPS_MainObjectVx;

  object_state.accel_x = ops_object_list_ptr->OPS_MainObjectAx;

  object_state.distance_y = ops_object_list_ptr->OPS_MainObjectDy;

  object_state.velocity_y = ops_object_list_ptr->OPS_MainObjectVy;

  object_state.accel_y = ops_object_list_ptr->OPS_MainObjectAy;
  return object_state;
}

static bool CheckObjectPositionValid(float object_distance_x,
                                     float object_distance_y) {
  return (fabsf(object_distance_x) < GetCdsMaxObjectLongDistance()) &&
         (fabsf(object_distance_y) < GetCdsMaxObjectLateralDistance());
}

static void WriteLongThreatAssessor(
    bool object_valid, float safe_distance, float time_to_reach_stop_point,
    float avoid_decel, uint8_t avoid_decel_state, float time_to_collision,
    bool object_pos_valid, bool lateral_pos_valid, float collision_probability,
    CDS_CollisionDetection_Struct* collision_detection_ptr) {
  if (!object_valid) {
    time_to_collision = GetCdsMaxTimeToCollision();
    time_to_reach_stop_point = GetCdsMaxTimeToCollision();
    avoid_decel = 0.0F;
    avoid_decel_state = 0U;
  } else {
    // do nothing
  }

  collision_detection_ptr->CDS_TimeToCollision = time_to_collision;
  collision_detection_ptr->CDS_ObjectPositionValid = object_pos_valid;
  collision_detection_ptr->CDS_CollisionEnableByDy = lateral_pos_valid;
  collision_detection_ptr->CDS_CollisionProbability = collision_probability;
  collision_detection_ptr->CDS_SafeDistance = safe_distance;
  collision_detection_ptr->CDS_TimeToReachStopPoint = time_to_reach_stop_point;
  collision_detection_ptr->CDS_LongNecAcc = avoid_decel;
  collision_detection_ptr->CDS_LongNecAccState = avoid_decel_state;
}

void LongThreatAssessor(
    const VSP_VehicleSignal_Struct* vehicle_signal_ptr,
    const OPS_ObjectList_Struct* ops_object_list_ptr,
    CDS_CollisionDetection_Struct* collision_detection_ptr) {
  // follow the object to brake
  MotionState ego_state = GetVehicleMotionState(vehicle_signal_ptr);
  float safe_distance = CML_Interpolation1D(
      ego_state.velocity_x, GetCdsSafeDistance(), CDS_SAFE_DISTANCE_TABLE_ROWS);

  // avoid collision deceleration state
  uint8_t state = 0U;
  MotionState object_state = GetObjectMotionState(ops_object_list_ptr);
  float accel_request = CalculateAvoidanceDeceleration(
      (safe_distance + GetVehicleRearAxleToFrontBumperDistance()),
      ego_state.velocity_x, ego_state.accel_x, object_state.distance_x,
      object_state.velocity_x, object_state.accel_x, &state);

  float time_to_reach_stop_point = CalculateTimeToCollision(
      (safe_distance + GetVehicleRearAxleToFrontBumperDistance()),
      ego_state.velocity_x, ego_state.accel_x, object_state.distance_x,
      object_state.velocity_x, object_state.accel_x);

  // collision property calculation
  float time_to_collision = CalculateTimeToCollision(
      GetVehicleRearAxleToFrontBumperDistance(), ego_state.velocity_x,
      ego_state.accel_x, object_state.distance_x, object_state.velocity_x,
      object_state.accel_x);

  // check object position valid
  bool object_pos_valid = CheckObjectPositionValid(object_state.distance_x,
                                                   object_state.distance_y);

  // todo, 20250102, to be implemented
  bool lateral_pos_valid = true;

  // todo, 20250102, to be implemented
  float collision_probability = 0.0F;

  WriteLongThreatAssessor(ops_object_list_ptr->OPS_MainObjectValid,
                          safe_distance, time_to_reach_stop_point,
                          accel_request, state, time_to_collision,
                          object_pos_valid, lateral_pos_valid,
                          collision_probability, collision_detection_ptr);
}
