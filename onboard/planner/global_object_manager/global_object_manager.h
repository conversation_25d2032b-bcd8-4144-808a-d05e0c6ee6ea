#ifndef ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_GLOBAL_OBJECT_MANAGER_H_
#define ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_GLOBAL_OBJECT_MANAGER_H_

#include <memory>
#include <vector>

#include "av_comm/thread_pool.h"
#include "planner/assist/assist_instruction.h"
#include "planner/assist/ignore_tracker/perception_fp_obstacle_tracker.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/required_lateral_gap_info.h"
#include "planner/global_object_manager/behavior_relevant_objects_info.h"
#include "planner/global_object_manager/common_types.h"
#include "planner/global_object_manager/utility/rtree_utility.h"
#include "planner/world_model/planner_object/overtaking_evaluator.h"
#include "planner/world_model/planner_object/planner_objects_history.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner_protos/planner_object_config.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/pnc_map_service.h"
#include "prediction_protos/agent.pb.h"

namespace planner {
class GlobalObjectManager {
 public:
  GlobalObjectManager(pb::RequiredLateralGapConfig required_lateral_gap_config,
                      pb::PlannerObjectConfig planner_object_config);

  // Updates the internal state of GlobalObjectManager, including:
  // - planner_object_map
  // - planner_objects_history
  // - object_prediction_map
  //
  // The update consists of the following steps:
  // 1. Evaluates agents (objects) in the scene
  // 2. Update prediction data for all relevant objects
  // 3. Construct planner objects and update their attributes
  // 4. Update overtaking-related information
  // 5. Apply assist instructions to planner objects and construction zones
  // 6. Determine whether the scenario qualifies as high agent density
  void Update(
      int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
      const pb::EgoOffroadInfoSeed& ego_off_road_seed,
      const assist::PerceptionFPObstacleTracker& perception_fpobstacle_tracker,
      double distance_to_destination_m, const RobotState& robot_state,
      const pnc_map::PncMapService& pnc_map_service,
      const ObjectTypeTrackingMap& object_type_tracking_map,
      const std::vector<const pnc_map::Lane*>&
          tracked_object_perception_range_lanes,
      const std::optional<UnstuckDirectives>& unstuck_directives,
      const AssistInstruction* assist_instruction,
      pb::PlannerObjectDebug* planner_object_debug);

  const RequiredLateralGapInfo& required_lateral_gap_info() const {
    return *DCHECK_NOTNULL(required_lateral_gap_info_);
  }

  const BehaviorRelevantObjectsInfo& base_behavior_relevant_objects_info()
      const {
    return *DCHECK_NOTNULL(base_behavior_relevant_objects_info_);
  }

  // TODO(DuongLe): move this to private after migrate all update planner
  // objects logic in GlobalObjectManager
  void UpdateBaseBehaviorRelevantObjectsInfo();

  const std::shared_ptr<PlannerObjectMap>& planner_object_map_ptr() const {
    return planner_object_map_ptr_;
  }

  std::shared_ptr<PlannerObjectMap>& mutable_planner_object_map_ptr() {
    return planner_object_map_ptr_;
  }

  const PlannerObjectsHistory& planner_objects_history() const {
    return planner_objects_history_;
  }

  PlannerObjectsHistory& mutable_planner_objects_history() {
    return planner_objects_history_;
  }

  const TbbPredictionTrajectoryMap& object_prediction_map() const {
    return object_prediction_map_;
  }

  TbbPredictionTrajectoryMap& mutable_object_prediction_map() {
    return object_prediction_map_;
  }

  const TbbConditionalPredictionTrajectoryMap&
  object_conditional_prediction_map() const {
    return object_conditional_prediction_map_;
  }

  TbbConditionalPredictionTrajectoryMap&
  mutable_object_conditional_prediction_map() {
    return object_conditional_prediction_map_;
  }

  const std::vector<ConstructionZone>& construction_zones() const {
    return construction_zones_;
  }

  std::vector<ConstructionZone>& mutable_construction_zones() {
    return construction_zones_;
  }

  const ConstructionZoneMap& construction_zone_map() const {
    return construction_zone_map_;
  }

  ConstructionZoneMap& mutable_construction_zone_map() {
    return construction_zone_map_;
  }

  bool is_high_agent_density() const { return is_high_agent_density_; }

  bool is_high_crossing_vru_density() const {
    return is_high_crossing_vru_density_;
  }

  // This method is required for the world_model_test_utility; dont used else
  // where.
  void UpdateIsHighAgentDensityScenario();

  // APIs

  // Returns planner objects that intersect with the given polyline.
  // Applies optional filter_fn to skip irrelevant objects before intersection
  // checks. This reduces computation by avoiding unnecessary geometry
  // evaluations.
  std::vector<const PlannerObject*> GetPlannerObjectsIntersectWithPolyline(
      const math::geometry::PolylineCurve2d& query_line,
      const std::function<bool(const PlannerObject&)>&
          unrelated_objects_filter_fn = nullptr) const;
  // TODO(ThaiDuongLe/Planner): temporary move to public to support
  // world_model_test_utility (required_lateral_gap_info is not stored in the
  // world_model), need to move back to private after update
  // world_model_test_utility .
  void UpdateRequiredLateralGapInfo();

 private:
  // Updates the planner object map using agent list, ego state, map info,
  // irrelevant, etc. This includes:
  // - Generating prediction trajectories
  // - Constructing and populating PlannerObjects for planning
  // - Writing relevant debug info
  void UpdatePlannerObjects(
      int64_t snapshot_timestamp, const prediction::pb::AgentList& agent_list,
      const RobotState& robot_state,
      const pnc_map::PncMapService& pnc_map_service,
      const ObjectTypeTrackingMap& object_type_tracking_map,
      const std::vector<const pnc_map::Lane*>&
          tracked_object_perception_range_lanes,
      const ObjectsEvaluationResult& objects_evaluation_result,
      const std::optional<UnstuckDirectives>& unstuck_directives,
      const AssistInstruction* assist_instruction,
      pb::PlannerObjectDebug* planner_object_debug);

  pb::PlannerObjectConfig planner_object_config_;

  pb::RequiredLateralGapConfig required_lateral_gap_config_;

  std::unique_ptr<RequiredLateralGapInfo> required_lateral_gap_info_;

  std::unique_ptr<BehaviorRelevantObjectsInfo>
      base_behavior_relevant_objects_info_;

  // A map from the Object ID to the object's all predicted trajectories for
  // decoupled planner.
  TbbPredictionTrajectoryMap object_prediction_map_;

  // A map to store Condition-Based Prediction for predicted objects
  TbbConditionalPredictionTrajectoryMap object_conditional_prediction_map_;

  // A map of PlannerObject for decoupled planner.
  std::shared_ptr<PlannerObjectMap> planner_object_map_ptr_;

  // History of planner_object_map
  PlannerObjectsHistory planner_objects_history_;

  // A vector of construction zones.
  std::vector<ConstructionZone> construction_zones_;
  ConstructionZoneMap construction_zone_map_;

  // overtaking_object_evaluator evaluates if the object is in the process of
  // overtaking the ego.
  OvertakingEvaluator overtaking_object_evaluator_;

  std::unique_ptr<av_comm::ThreadPool> destruction_background_pool_;

  // True if ego is at high agent density scenario.
  bool is_high_agent_density_{false};

  // True if Ego is at high crossing vru density scenario.
  bool is_high_crossing_vru_density_{false};

  // Spatial index used for efficient geometry queries on planner objects.
  std::unique_ptr<utility::PlannerObjectGeometryRTree>
      planner_object_spatial_index_{nullptr};

  // friend class PathGenerationTest;
  FRIEND_TEST(PathGenerationTest, BaseTest);
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_GLOBAL_OBJECT_MANAGER_GLOBAL_OBJECT_MANAGER_H_
