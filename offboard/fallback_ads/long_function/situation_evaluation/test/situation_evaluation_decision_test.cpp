#include "long_function/situation_evaluation/situation_evaluation_decision.h"

#include <gtest/gtest.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "long_function/long_function_parameter.h"
#include "long_function/long_function_type.h"

TEST(CheckEmergencyBrake, NoEmergencyWithValidObject) {
  // Setup
  bool object_position_valid = true;
  bool lateral_collision_enable = true;
  float collision_probability = 0.0F;
  float time_to_collision = GetSedTimeToCollisionThreshold() + 0.01F;

  // Execute
  bool emergency_brake_enable =
      CheckEmergencyBrake(object_position_valid, lateral_collision_enable,
                          collision_probability, time_to_collision);

  // Verify
  EXPECT_FALSE(emergency_brake_enable);
}

TEST(CheckEmergencyBrake, EmergencyEnable) {
  // Setup
  bool object_position_valid = true;
  bool lateral_collision_enable = true;
  float collision_probability = 0.0F;
  float time_to_collision = GetSedTimeToCollisionThreshold() - 0.01F;

  // Execute
  bool emergency_brake_enable =
      CheckEmergencyBrake(object_position_valid, lateral_collision_enable,
                          collision_probability, time_to_collision);

  // Verify
  EXPECT_TRUE(emergency_brake_enable);
}

TEST(CheckEmergencyBrake, EmergencyTurnOffDelay) {
  // Setup
  bool object_position_valid = true;
  bool lateral_collision_enable = true;
  float collision_probability = 0.0F;
  float time_to_collision = 1.0F;

  // Execute
  bool emergency_brake_enable =
      CheckEmergencyBrake(object_position_valid, lateral_collision_enable,
                          collision_probability, time_to_collision);
  // Verify
  EXPECT_TRUE(emergency_brake_enable);

  time_to_collision = 10.0F;
  int count =
      GetSedEmergencyBrakeEnableDelayTime() / LGT_CONTROL_TASK_CYCLE_TIME;
  for (int i = 0; i < count - 1; ++i) {
    emergency_brake_enable =
        CheckEmergencyBrake(object_position_valid, lateral_collision_enable,
                            collision_probability, time_to_collision);
    EXPECT_TRUE(emergency_brake_enable);
  }
  emergency_brake_enable =
      CheckEmergencyBrake(object_position_valid, lateral_collision_enable,
                          collision_probability, time_to_collision);
  EXPECT_FALSE(emergency_brake_enable);
}

TEST(CheckObjectFollowingBrake, NoNeedObjectFollowingBrake) {
  // Setup
  float time_to_reach_stop_point = GetSedTimeToReachStopPointThreshold() + 0.1F;

  // Execute
  bool object_following_brake_enable =
      CheckObjectFollowingBrake(time_to_reach_stop_point);

  // Verify
  EXPECT_FALSE(object_following_brake_enable);
}

TEST(CheckObjectFollowingBrake, NeedObjectFollowingBrake) {
  // Setup
  float time_to_reach_stop_point = GetSedTimeToReachStopPointThreshold() - 0.1F;

  // Execute
  bool object_following_brake_enable =
      CheckObjectFollowingBrake(time_to_reach_stop_point);

  // Verify
  EXPECT_TRUE(object_following_brake_enable);
}

TEST(CheckObjectFollowingBrake, ObjectFollowingBrakeTurnOffDelay) {
  // Setup
  float time_to_reach_stop_point = 4;

  // Execute
  bool object_following_brake_enable =
      CheckObjectFollowingBrake(time_to_reach_stop_point);
  // Verify
  EXPECT_TRUE(object_following_brake_enable);

  time_to_reach_stop_point = 10.0F;
  int count =
      GetSedObjectFollowingBrakeDelayTime() / LGT_CONTROL_TASK_CYCLE_TIME;
  for (int i = 0; i < count; ++i) {
    object_following_brake_enable =
        CheckObjectFollowingBrake(time_to_reach_stop_point);
    EXPECT_TRUE(object_following_brake_enable);
  }
  object_following_brake_enable =
      CheckObjectFollowingBrake(time_to_reach_stop_point);
  EXPECT_FALSE(object_following_brake_enable);
}

TEST(CheckFreeStop, FreeStopEnableTest) {
  // Setup
  float brake_distance = 40.0F;
  float ego_velocity = 10.0F;
  float accel_request_for_free_stop = -2.0F;
  float accel_request_for_object = -1.0F;
  float accel_request_for_lane = -1.0F;

  // Execute
  bool free_stop_enable =
      CheckFreeStop(brake_distance, ego_velocity, accel_request_for_free_stop,
                    accel_request_for_object, accel_request_for_lane);

  // Verify
  EXPECT_TRUE(free_stop_enable);
}

TEST(CheckFreeStop, DistanceNotEnoughTest) {
  // Setup
  float brake_distance = 20.0F;
  float ego_velocity = 10.0F;
  float accel_request_for_free_stop = -2.0F;
  float accel_request_for_object = -1.0F;
  float accel_request_for_lane = -1.0F;

  // Execute
  bool free_stop_enable =
      CheckFreeStop(brake_distance, ego_velocity, accel_request_for_free_stop,
                    accel_request_for_object, accel_request_for_lane);

  // Verify
  EXPECT_FALSE(free_stop_enable);
}

TEST(CheckFreeStop, DisableByObjectFollowingBrakeTest) {
  // Setup
  float brake_distance = 40.0F;
  float ego_velocity = 10.0F;
  float accel_request_for_free_stop = -2.0F;
  float accel_request_for_object = -2.1F;
  float accel_request_for_lane = -1.0F;

  // Execute
  bool free_stop_enable =
      CheckFreeStop(brake_distance, ego_velocity, accel_request_for_free_stop,
                    accel_request_for_object, accel_request_for_lane);

  // Verify
  EXPECT_FALSE(free_stop_enable);
}

TEST(CheckFreeStop, DisableByLaneFollowingBrakeTest) {
  // Setup
  float brake_distance = 40.0F;
  float ego_velocity = 10.0F;
  float accel_request_for_free_stop = -2.0F;
  float accel_request_for_object = -1.0F;
  float accel_request_for_lane = -2.1F;

  // Execute
  bool free_stop_enable =
      CheckFreeStop(brake_distance, ego_velocity, accel_request_for_free_stop,
                    accel_request_for_object, accel_request_for_lane);

  // Verify
  EXPECT_FALSE(free_stop_enable);
}

TEST(CheckLaneFollowingBrake, LaneFollowingBrakeEnable) {
  EXPECT_TRUE(CheckLaneFollowingBrake(-8.0F + 0.1F));
  EXPECT_FALSE(CheckLaneFollowingBrake(-8.0F - 0.1F));
}

TEST(CheckLaneIsTooShort, LaneIsTooShortTest) {
  // Setup
  float ego_velocity = 10.0F;
  float lane_length = 5.0F;
  float accel_request = -1.0F;

  // Execute
  bool lane_is_too_short =
      CheckLaneIsTooShort(ego_velocity, lane_length, accel_request);

  // Verify
  EXPECT_TRUE(lane_is_too_short);
}

TEST(CheckLaneIsTooShort, LaneNotTooShortTest) {
  // Setup
  float ego_velocity = 10.0F;
  float lane_length = 15.0F;
  float accel_request = -1.0F;

  // Execute
  bool lane_is_too_short = false;
  int count = static_cast<int>(GetSedLaneIsTooShortDelayTime() /
                               LGT_CONTROL_TASK_CYCLE_TIME);
  for (int i = 0; i < count + 1; ++i) {
    lane_is_too_short =
        CheckLaneIsTooShort(ego_velocity, lane_length, accel_request);
  }

  // Verify
  EXPECT_FALSE(lane_is_too_short);
}

TEST(VehicleIsStandstill, IsStandstillTest) {
  // Setup
  float ego_velocity = 0.0F;

  // Execute
  bool vehicle_is_standstill = false;
  int count = static_cast<int>(0.5F / LGT_CONTROL_TASK_CYCLE_TIME);
  for (int i = 0; i < count + 1; ++i) {
    vehicle_is_standstill = VehicleIsStandstill(ego_velocity);
  }

  // Verify
  EXPECT_TRUE(vehicle_is_standstill);
}

TEST(UpdateStateStandbyExit, UpdateStateStandbyExitTest) {
  // Setup
  bool fallback_active = false;
  bool ego_is_standstill = false;
  bool collision_enable = false;
  bool ego_lane_is_too_short = false;
  bool free_stop_enable = false;
  bool lane_following_brake_enable = false;
  bool object_following_brake_enable = false;
  SystemState system_state = STANDBY;

  // case 1: fallback not active, system state is standby
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, STANDBY);

  // case 2: fallback active, system state is standby
  fallback_active = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, STANDBY);

  // case 3: fallback active, collision enable, system state is emergency brake
  collision_enable = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);

  // case 4: fallback active, ego lane is too short, system state is emergency
  // brake
  collision_enable = false;
  ego_lane_is_too_short = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);

  // case 5: fallback active, free stop enable, system state is free stop
  ego_lane_is_too_short = false;
  free_stop_enable = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 6: fallback active, lane following brake enable, object following
  // brake enable, system state is combine brake
  free_stop_enable = false;
  lane_following_brake_enable = true;
  object_following_brake_enable = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, COMBINE_BRAKE);

  // case 7: fallback active, lane following brake enable, system state is lane
  // following brake
  object_following_brake_enable = false;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, LANE_FOLLOWING_BRAKE);

  // case 8: fallback active, object following brake enable, system state is
  // object following brake
  lane_following_brake_enable = false;
  object_following_brake_enable = true;
  UpdateStateStandbyExit(fallback_active, ego_is_standstill, collision_enable,
                         ego_lane_is_too_short, free_stop_enable,
                         lane_following_brake_enable,
                         object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, OBJECT_FOLLOWING_BRAKE);
}

TEST(UpdateStateFreeStopExit, UpdateStateFreeStopExitTest) {
  // Setup
  bool ego_is_standstill = false;
  bool collision_enable = false;
  bool ego_lane_is_too_short = false;
  bool free_stop_enable = true;
  bool lane_following_brake_enable = false;
  bool object_following_brake_enable = false;
  SystemState system_state = STANDBY;

  // case 1: free stop enable, system state is free stop
  UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                          ego_lane_is_too_short, free_stop_enable,
                          lane_following_brake_enable,
                          object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 2: free stop enable, lane following brake enable, object following
  // brake enable, system state is free stop
  lane_following_brake_enable = true;
  object_following_brake_enable = true;
  UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                          ego_lane_is_too_short, free_stop_enable,
                          lane_following_brake_enable,
                          object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 3: free stop enable, lane following brake enable,
  // system state is free stop
  object_following_brake_enable = false;
  UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                          ego_lane_is_too_short, free_stop_enable,
                          lane_following_brake_enable,
                          object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 4: free stop enable, object following brake enable,
  // system state is object free stop
  lane_following_brake_enable = false;
  object_following_brake_enable = true;
  UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                          ego_lane_is_too_short, free_stop_enable,
                          lane_following_brake_enable,
                          object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 5: free stop enable, system state is free stop
  lane_following_brake_enable = false;
  object_following_brake_enable = false;
  UpdateStateFreeStopExit(ego_is_standstill, collision_enable,
                          ego_lane_is_too_short, free_stop_enable,
                          lane_following_brake_enable,
                          object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);
}

TEST(UpdateStateAdaptiveBrakeExit, UpdateStateAdaptiveBrakeExitTest) {
  // Setup
  bool ego_is_standstill = false;
  bool collision_enable = false;
  bool ego_lane_is_too_short = false;
  bool free_stop_enable = false;
  bool lane_following_brake_enable = false;
  bool object_following_brake_enable = false;
  SystemState system_state = COMBINE_BRAKE;

  // case 1: the system status remains unchanged
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, COMBINE_BRAKE);

  // case 1: collision enable, system state is emergency brake
  collision_enable = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);

  // case 2: ego lane is too short, system state is emergency brake
  collision_enable = false;
  ego_lane_is_too_short = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);

  // case 3: free stop enable, system state is free stop
  ego_lane_is_too_short = false;
  free_stop_enable = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 4: ego is standstill, system state is free stop
  free_stop_enable = false;
  ego_is_standstill = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, FREE_STOP);

  // case 5: lane following brake enable, object following brake enable, system
  // state is combine brake
  ego_is_standstill = false;
  lane_following_brake_enable = true;
  object_following_brake_enable = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, COMBINE_BRAKE);

  // case 6: lane following brake enable, system state is lane following brake
  object_following_brake_enable = false;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, LANE_FOLLOWING_BRAKE);

  // case 7: object following brake enable, system state is object following
  // brake
  lane_following_brake_enable = false;
  object_following_brake_enable = true;
  UpdateStateAdaptiveBrakeExit(ego_is_standstill, collision_enable,
                               ego_lane_is_too_short, free_stop_enable,
                               lane_following_brake_enable,
                               object_following_brake_enable, &system_state);
  ASSERT_EQ(system_state, OBJECT_FOLLOWING_BRAKE);
}

TEST(UpdateSystemState, FreeStopToEmergencyBrakeTest) {
  // Setup
  bool fallback_active = true;
  bool ego_is_standstill = false;
  bool collision_enable = false;
  bool ego_lane_is_too_short = false;
  bool object_following_brake_enable = false;
  bool lane_following_brake_enable = false;
  bool free_stop_enable = true;
  float free_stop_accel = -1.5F;
  float lane_following_brake_accel = 0.0F;
  float object_following_brake_accel = 0.0F;
  float accel_request = 0.0F;
  float jerk_request = 0.0F;
  SystemState system_state = STANDBY;

  // step 1: system state from standby to freestop
  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -1.0F);
  ASSERT_EQ(jerk_request, -3.0F);

  // step 2: system state from free stop to emergency brake
  collision_enable = true;
  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);
  ASSERT_EQ(accel_request, -1.5F);
  ASSERT_EQ(jerk_request, -3.0F);

  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, EMERGENCY_BRAKE);
  ASSERT_EQ(accel_request, -10.0F);
  ASSERT_EQ(jerk_request, -20.0F);

  // step 3: system state from emergency brake to free stop
  ego_is_standstill = true;
  collision_enable = false;
  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -10.0F);
  ASSERT_EQ(jerk_request, -20.0F);

  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -1.5F);
  ASSERT_EQ(jerk_request, -3.0F);
}

TEST(UpdateSystemState, FreeStopToCombineBrakeTest) {
  // Setup
  bool fallback_active = true;
  bool ego_is_standstill = false;
  bool collision_enable = false;
  bool ego_lane_is_too_short = false;
  bool object_following_brake_enable = false;
  bool lane_following_brake_enable = false;
  bool free_stop_enable = true;
  float free_stop_accel = -1.5F;
  float lane_following_brake_accel = -2.0F;
  float object_following_brake_accel = -2.5F;
  float accel_request = 0.0F;
  float jerk_request = 0.0F;
  SystemState system_state = STANDBY;

  // step 1: system state from standby to freestop
  // eliminate the impact of previous calculations
  for (int i = 0; i < 2; ++i) {
    system_state = UpdateSystemState(
        fallback_active, ego_is_standstill, collision_enable,
        ego_lane_is_too_short, object_following_brake_enable,
        lane_following_brake_enable, free_stop_enable, free_stop_accel,
        lane_following_brake_accel, object_following_brake_accel,
        &accel_request, &jerk_request);
  }
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -1.5F);
  ASSERT_EQ(jerk_request, -3.0F);

  // step 2: system state from free stop to combine brake
  free_stop_enable = false;
  object_following_brake_enable = true;
  lane_following_brake_enable = true;
  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, COMBINE_BRAKE);
  ASSERT_EQ(accel_request, -1.5F);
  ASSERT_EQ(jerk_request, -3.0F);

  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, COMBINE_BRAKE);
  ASSERT_EQ(accel_request, -2.5F);
  ASSERT_EQ(jerk_request, -4.0F);

  // step 3: system state from emergency combine to free stop
  ego_is_standstill = true;
  object_following_brake_enable = false;
  lane_following_brake_enable = false;
  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -2.5F);
  ASSERT_EQ(jerk_request, -4.0F);

  system_state = UpdateSystemState(
      fallback_active, ego_is_standstill, collision_enable,
      ego_lane_is_too_short, object_following_brake_enable,
      lane_following_brake_enable, free_stop_enable, free_stop_accel,
      lane_following_brake_accel, object_following_brake_accel, &accel_request,
      &jerk_request);
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -1.5F);
  ASSERT_EQ(jerk_request, -3.0F);
}

TEST(SituationEvaluationDecision, FreeStopTest) {
  // setup
  VSI_VehicleInfo_Struct vehicle_info = {.VSI_FallbackActive =
                                             static_cast<boolean>(true)};
  CSI_LaneInfo_Struct lane_info = {.CSI_PosXEndLf = 43.8F,
                                   .CSI_PosXEndRi = 43.8F};
  VSP_VehicleSignal_Struct vehicle_signal = {.VSP_LgtVehSpdFild = 8.05F};
  FSC_FreeStopControl_Struct free_stop_control = {
      .FSC_FreeStopAccRequest = -1.95F, .FSC_AccRequestByLaneLength = -0.846F};
  CDS_CollisionDetection_Struct collision_detection = {
      .CDS_TimeToCollision = 40.0F,
      .CDS_ObjectPositionValid = static_cast<boolean>(true),
      .CDS_CollisionEnableByDy = static_cast<boolean>(true),
      .CDS_CollisionProbability = 0.0F,
      .CDS_LongNecAcc = 0.0F,
      .CDS_TimeToReachStopPoint = 40.0F};

  // execute
  float accel_request = 0.0F;
  float jerk_request = 0.0F;
  SystemState system_state = STANDBY;
  for (int i = 0; i < 5000; ++i) {
    system_state = SituationEvaluationDecision(
        &vehicle_info, &lane_info, &vehicle_signal, &free_stop_control,
        &collision_detection, &accel_request, &jerk_request);
  }

  // verify
  ASSERT_EQ(system_state, FREE_STOP);
  ASSERT_EQ(accel_request, -1.95F);
  ASSERT_EQ(jerk_request, -3.0F);
}

TEST(SituationEvaluationDecision, FreeStopWithFrontObjectTest) {
  // setup
  VSI_VehicleInfo_Struct vehicle_info = {.VSI_FallbackActive =
                                             static_cast<boolean>(true)};
  CSI_LaneInfo_Struct lane_info = {.CSI_PosXEndLf = 43.5F,
                                   .CSI_PosXEndRi = 43.75F};
  VSP_VehicleSignal_Struct vehicle_signal = {.VSP_LgtVehSpdFild = 8.05F};
  FSC_FreeStopControl_Struct free_stop_control = {
      .FSC_FreeStopAccRequest = -2.47F, .FSC_AccRequestByLaneLength = -1.237F};
  CDS_CollisionDetection_Struct collision_detection = {
      .CDS_TimeToCollision = 0.0F,
      .CDS_ObjectPositionValid = static_cast<boolean>(true),
      .CDS_CollisionEnableByDy = static_cast<boolean>(true),
      .CDS_CollisionProbability = 0.0F,
      .CDS_LongNecAcc = -1.61F,
      .CDS_TimeToReachStopPoint = 0.0F};

  // execute
  float accel_request = 0.0F;
  float jerk_request = 0.0F;
  SystemState system_state = STANDBY;
  for (int i = 0; i < 5000; ++i) {
    system_state = SituationEvaluationDecision(
        &vehicle_info, &lane_info, &vehicle_signal, &free_stop_control,
        &collision_detection, &accel_request, &jerk_request);
  }

  // verify
  EXPECT_EQ(system_state, EMERGENCY_BRAKE);
}
