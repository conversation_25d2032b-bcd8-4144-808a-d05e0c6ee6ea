#include "planner/utility/path/juke_integrated_path_costing.h"

#include <algorithm>
#include <utility>
#include <vector>
#include <voy_protos/control_util.pb.h>

#include "adv_geom/path_pose.h"
#include "math/math_util.h"

#include "planner/path/path_solver/nominal_path_smoother/semantic_corridor.h"
#include "planner/path/path_solver/path_problem/constraints/boundary_constraints.h"
#include "planner/planning_gflags.h"
#include "voy_protos/math.pb.h"

namespace control_util {
namespace {

using StateIndex = planner::path::JukeIntegratedPathFormula::StateIndex;
using ControlIndex = planner::path::JukeIntegratedPathFormula::ControlIndex;
using CostingUtil =
    planner::path::JukeIntegratedPathFormula::FormulaCostingUtil;
using FeatureParameter =
    planner::path::JukeIntegratedPathFormula::PbFeatureParameter;
using CostingTerm = planner::path::JukeIntegratedPathFormula::PbCostingTerm;
using FeatureType = planner::path::JukeIntegratedPathFormula::PbFeatureType;
using StepwisePrimitives =
    planner::path::JukeIntegratedPathFormula::StepwisePrimitives;
using WeightAdjustSpec =
    planner::path::JukeIntegratedPathFormula::PbWeightAdjustSpec;
using WeightAdjustForBoundary = control_util::pb::WeightAdjustForBoundary;
using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;

using State = planner::path::JukeIntegratedPathFormula::State;
using Control = planner::path::JukeIntegratedPathFormula::Control;

constexpr int kSampleNumOnEgoBoundary = 5;

// If the diff of heading angle and target angle is within the tolerance range,
// no cost is applied to the state.
constexpr double kTerminalHeadingToleranceInRad = 0.02;
// If the diff of path state's heading and reference curve's heading is
// within the tolerance range, no cost is applied to the state.
constexpr double kReferenceHeadingToleranceInRad = 0.25;
// If the diff of path state's curvature and reference curve's curvature is
// within the tolerance range, no cost is applied to the state.
constexpr double kReferenceCurvatureTolerance = 0.05;

// An extra distance added to physical barrier buffer, to guarantee the barrier
// position not being violated.
constexpr double kPhysicalBarrierTolerance = 0.01;
// TODO(guanao): move param to config.
constexpr double kToleranceForVirtualHard = 0.01;
// When dense sampling on ego bbox is enabled, the sampling points number should
// multiple this factor.
constexpr int kDenseSamplingFactor = 4;
// The max deceleration when we calculate risk cost, the ego is assumed to
// stop at this deceleration to avoid collision.
constexpr double kMaxStopDecelMpss = 3.0;
// Taylor expansion related coefficients.
constexpr double kSecondOrderCoef = 0.5;
constexpr double kThirdOrderCoef = 1.0 / 6;
// When the clearance between risk states and HB is lower than this buffer, risk
// cost will be triggered.
constexpr double kRiskCostBufferForPhysical = 0.6;
// The number of states to be costed. We only cost the first fixed number of
// states, as the later ones are more uncertain, and can over-constrain the
// problem.
constexpr int kRiskCostStateNumForPhysical = 60;
// The max arclength when we calculate forward state for risk cost, note it's in
// meter. Now we take 10m for the threshold, since if ego current speed is
// 30km/h and decel at -3 m/ss, at TTC = 2s the moving distance is around 10m.
// 30 km/h is a relatively high speed, so use it as distance threshold.
// TODO(guanao): further polish this scoping logic by considering collision
// time.
constexpr double kMaxArclengthForwardStateInMeter = 10.0;
// Decay risk cost by multipling this decay factor every sampling step.
constexpr double kUncertaintyStepFactor = 0.75;
// Reduce risk buffer every sampling step.
constexpr double kUncertaintyStepBufferReduction = 0.2;
// Motion model that we use to generate future costing states base on current
// state.
enum class RiskAwareMotionModel { kConstHeading, kConstCurvature };
enum class EgoGeometricModel { kDisks, kBBox };
// Default value for control error.
// TODO(path solver): move default control error into a centralized module.
constexpr double kControlError = 0.2;

// This flag denotes what costing terms we should skip.
// TODO(huanhui):  in the future we should specify these terms in config file.
bool ShouldSkipCostingTerm(
    FeatureType feature_type,
    const std::optional<planner::primitive::TargetStatusInfo>&
        target_status_info,
    int step) {
  if (!target_status_info.has_value() ||
      !target_status_info->destination_ddp_index.has_value() ||
      (step <= target_status_info->destination_ddp_index.value())) {
    return false;
  }

  return feature_type == FeatureType::PRIMITIVE_VIOLATION_LEFT_NUDGE_CORRIDOR ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_RIGHT_NUDGE_CORRIDOR ||
         feature_type == FeatureType::PRIMITIVE_SOFT_LEFT_NUDGE_CORRIDOR ||
         feature_type == FeatureType::PRIMITIVE_SOFT_RIGHT_NUDGE_CORRIDOR ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_LEFT_NUDGE_CLEARANCE ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_RIGHT_NUDGE_CLEARANCE ||
         feature_type == FeatureType::PRIMITIVE_SOFT_LEFT_NUDGE_CLEARANCE ||
         feature_type == FeatureType::PRIMITIVE_SOFT_RIGHT_NUDGE_CLEARANCE ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_LEFT_PHYSICAL_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_RIGHT_PHYSICAL_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_LEFT_VIRTUAL_SOFT_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_RIGHT_VIRTUAL_SOFT_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_LEFT_PURE_VIRTUAL_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_VIOLATION_RIGHT_PURE_VIRTUAL_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_SOFT_LEFT_RISK_PHYSICAL_BOUNDARY ||
         feature_type ==
             FeatureType::PRIMITIVE_SOFT_RIGHT_RISK_PHYSICAL_BOUNDARY ||
         feature_type == FeatureType::PRIMITIVE_SOFT_ATTRACTION_SEGMENTS;
}

// Returns weight adjust coefficient based on behavior/maneuver for terminal
// heading cost.
inline double GetWeightAdjustForTerminalHeading(
    planner::pb::MotionMode motion_mode,
    planner::pb::BehaviorType behavior_type,
    const control_util::pb::WeightAdjustForBehavior& weight_adjust) {
  if (motion_mode == planner::pb::MotionMode::FORWARD &&
      behavior_type == planner::pb::BehaviorType::DECOUPLED_PULL_OVER) {
    return weight_adjust.for_pullover();
  }
  if (motion_mode == planner::pb::MotionMode::BACKWARD) {
    return weight_adjust.for_reverse();
  }
  // Return weight zero for default.
  return 0.0;
}

// Returns true if we want stronger attraction of given ref path.
inline bool ShouldEnhanceAttraction(
    const planner::path::JukeIntegratedPathFormula::Primitives& primitives) {
  return primitives.path_maneuver_primitive.behavior_type ==
             planner::pb::BehaviorType::LANE_KEEP &&
         primitives.path_maneuver_primitive.intention_homotopy ==
             planner::pb::IntentionResult::IGNORE_ALL &&
         !primitives.path_maneuver_primitive.is_pull_out_jump_out &&
         !primitives.path_maneuver_primitive.is_in_uturn;
}

// Returns weight adjust coefficient based on |strength| and
// |strength_weight_ratio|.
double GetBoundaryWeightAdjust(
    planner::path::BoundaryStrength strength,
    const control_util::pb::WeightAdjustForBoundary& weight_adjust,
    std::optional<double> strength_weight_ratio) {
  double base_adjust = 0.0;
  switch (strength) {
    case planner::path::BoundaryStrength::kWeak:
      base_adjust = weight_adjust.for_weak();
      break;
    case planner::path::BoundaryStrength::kModerate:
      base_adjust = weight_adjust.for_moderate();
      break;
    case planner::path::BoundaryStrength::kStrong:
      base_adjust = weight_adjust.for_strong();
      break;
    default:
      DCHECK(false) << "Wrong strength type!";
  }

  if (strength_weight_ratio.has_value()) {
    // strength_weight_ratio should be in (0.0, 1.0].
    DCHECK_GT(strength_weight_ratio.value(), 0.0);
    DCHECK_LE(strength_weight_ratio.value(), 1.0);
    return base_adjust * strength_weight_ratio.value();
  }
  return base_adjust;
}

control_util::pb::CostingWeightAdjustSpec::WeightAdjustType
GetRepulsionWeightDecayType(
    planner::path::RepulsionWeightDecayType weight_decay_type) {
  control_util::pb::CostingWeightAdjustSpec::WeightAdjustType
      cost_weight_adjust_spec;
  switch (weight_decay_type) {
    case planner::path::RepulsionWeightDecayType::kNoDecay:
      cost_weight_adjust_spec =
          control_util::pb::CostingWeightAdjustSpec::NO_DECAY;
      break;
    case planner::path::RepulsionWeightDecayType::kLinearReduction:
      cost_weight_adjust_spec =
          control_util::pb::CostingWeightAdjustSpec::LINEAR_REDUCTION;
      break;
    case planner::path::RepulsionWeightDecayType::kReverseSigmoid:
      cost_weight_adjust_spec =
          control_util::pb::CostingWeightAdjustSpec::REVERSED_SIGMOID;
      break;
    case planner::path::RepulsionWeightDecayType::kBellDecay:
      cost_weight_adjust_spec =
          control_util::pb::CostingWeightAdjustSpec::BELL_DECAY;
      break;
    default:
      DCHECK(false) << "Wrong weight decay type!";
  }
  return cost_weight_adjust_spec;
}

// Updates the value of a clearance-related featurelet. Input parameter dist
// represents the distance from a query point to its proximal point on a
// geometry, and tighter buffer usually means critical and wider for comfort.
inline void UpdateClearanceRelatedFeatureLet(
    double dist, double jacob_x, double jacob_y, double jacob_theta,
    double wider_buffer, double weight_adjust,
    planner::path::ClearanceType type, FeatureLet* feature_let,
    const double* tighter_buffer = nullptr) {
  feature_let->val = type == planner::path::ClearanceType::kNegative
                         ? std::max(dist + wider_buffer, 0.0)
                         : std::min(dist - wider_buffer, 0.0);
  feature_let->weight_adjust = weight_adjust;

  const bool update_jacob = (type == planner::path::ClearanceType::kPositive)
                                ? (feature_let->val < 0.0)
                                : (feature_let->val > 0.0);
  if (update_jacob) {
    feature_let->jacob[StateIndex::X_POS] = jacob_x;
    feature_let->jacob[StateIndex::Y_POS] = jacob_y;
    feature_let->jacob[StateIndex::THETA] = jacob_theta;
  }
  if (tighter_buffer != nullptr) {
    DCHECK_LE(*tighter_buffer, wider_buffer);
    feature_let->additional_param = wider_buffer - *tighter_buffer;
  }
}

// Updates the value of a clearance-related featurelet for augmented lagrangian
// constraints. Input parameter dist represents the distance from a query point
// to its proximal point on a geometry, and tighter buffer usually means
// critical and wider for comfort.
// TODO(huanhui): Now we haven't set some boundary constraints to augmented
// lagrangian constraints, will do it in future if necessary.
[[maybe_unused]] inline void
UpdateClearanceRelatedFeatureLetForInequalityConstraint(
    double dist, double jacob_x, double jacob_y, double jacob_theta,
    [[maybe_unused]] double wider_buffer, double weight_adjust,
    planner::path::ClearanceType type, FeatureLet* feature_let,
    const double* tighter_buffer = nullptr) {
  static const double dist_buffer = 0.02;
  const double sign =
      type == planner::path::ClearanceType::kPositive ? -1.0 : 1.0;
  feature_let->val =
      sign * dist + (tighter_buffer ? *tighter_buffer : 0) + dist_buffer;

  feature_let->weight_adjust = weight_adjust;
  const bool update_jacob = (sign * feature_let->val) > 0.0;
  if (update_jacob) {
    feature_let->jacob[StateIndex::X_POS] = jacob_x * sign;
    feature_let->jacob[StateIndex::Y_POS] = jacob_y * sign;
    feature_let->jacob[StateIndex::THETA] = jacob_theta * sign;
  }

  // Inequality constraint for convergence tolerance.
  static const double convergence_threshold = 0.02;
  feature_let->additional_param = convergence_threshold;
}

void UpdateClearanceAndCurvatureRelatedFeatureLet(
    double dist, double jacob_x, double jacob_y, double jacob_theta,
    double jacob_curvature, double buffer, double weight_adjust,
    planner::path::ClearanceType type, FeatureLet* feature_let) {
  feature_let->val = type == planner::path::ClearanceType::kNegative
                         ? std::max(dist + buffer, 0.0)
                         : std::min(dist - buffer, 0.0);
  feature_let->weight_adjust = weight_adjust;

  const bool update_jacob = (type == planner::path::ClearanceType::kPositive)
                                ? (feature_let->val < 0.0)
                                : (feature_let->val > 0.0);
  if (update_jacob) {
    feature_let->jacob[StateIndex::X_POS] = jacob_x;
    feature_let->jacob[StateIndex::Y_POS] = jacob_y;
    feature_let->jacob[StateIndex::THETA] = jacob_theta;
    feature_let->jacob[StateIndex::KAPPA] = jacob_curvature;
  }
}

// Computes the polyline distance violation given the a circle-based coverage of
// the vehicle.
bool ComputeFullBodyPolylineViolationFeature(
    const std::vector<vehicle_model::DiskInfo>& ego_disks, double theta,
    planner::path::ClearanceType type,
    math::geometry::PolylineCurve2dProximityCache* cached_poly,
    Feature* feature) {
  if (cached_poly == nullptr) {
    return true;
  }

  if (cached_poly->curve().empty()) {
    return true;
  }

  feature->components.resize(ego_disks.size());
  const double sin_theta = sin(theta);
  const double cos_theta = cos(theta);

  math::EVector2x1 query_pt;
  math::EVector2x1 d_dist;
  for (size_t i = 0; i < ego_disks.size(); ++i) {
    const auto& ego_disk = ego_disks[i];
    query_pt << ego_disk.disk.center().x(), ego_disk.disk.center().y();
    double dist = 0.0;
    d_dist = math::EVector2x1::Zero();
    cached_poly->Query(query_pt, &dist, &d_dist);

    const double l = ego_disk.lon_offset;
    const double w = ego_disk.lat_offset;
    const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                               (l * cos_theta - w * sin_theta) * d_dist(1);
    UpdateClearanceRelatedFeatureLet(
        dist, d_dist(0), d_dist(1), jacob_theta, ego_disk.disk.radius(),
        /*weight_adjust=*/1.0, type, &feature->components[i]);
  }

  return true;
}

// Computes the clearance violation given the a circle-based coverage of
// the vehicle.
void ComputeFullBodyClearanceViolationFeatureByDisk(
    const std::vector<vehicle_model::DiskInfo>& ego_disks, double theta,
    bool is_left, bool is_critical,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    Feature* feature) {
  DCHECK(constraint_proximity_map != nullptr);

  feature->components.resize(ego_disks.size());
  const double sin_theta = math::FastSin(theta);
  const double cos_theta = math::FastCos(theta);

  for (size_t i = 0; i < ego_disks.size(); ++i) {
    const auto& ego_disk = ego_disks[i];
    const auto& constraint_proximity =
        constraint_proximity_map->GetAgentProximity(ego_disk.disk.center().x(),
                                                    ego_disk.disk.center().y(),
                                                    is_left, is_critical);

    const double l = ego_disk.lon_offset;
    const double w = ego_disk.lat_offset;
    const double jacob_theta =
        (-l * sin_theta - w * cos_theta) * constraint_proximity.d_dist(0) +
        (l * cos_theta - w * sin_theta) * constraint_proximity.d_dist(1);

    const double required_lateral_gap =
        is_critical ? constraint_proximity.critical_lat_gap.value()
                    : constraint_proximity.comfort_lat_gap.value();
    const double wider_buffer = ego_disk.disk.radius() + required_lateral_gap;
    const planner::path::ClearanceType type =
        is_left ? planner::path::ClearanceType::kPositive
                : planner::path::ClearanceType::kNegative;
    UpdateClearanceRelatedFeatureLet(
        constraint_proximity.dist, constraint_proximity.d_dist(0),
        constraint_proximity.d_dist(1), jacob_theta, wider_buffer,
        /*weight_adjust=*/1.0, type, &feature->components[i],
        /*tighter_buffer=*/nullptr);
  }
}

// Computes the clearance violation given the sampled points on the side edge of
// the agent.
void ComputeFullBodyClearanceViolationFeatureBySampledPoints(
    const State& x,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    bool is_left, bool is_critical,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    Feature* feature) {
  static constexpr int kSampleNumOnEgoForAgentCosting = 11;
  feature->components.resize(kSampleNumOnEgoForAgentCosting);

  const double w = shape_measurement.width() * (is_left ? 0.5 : -0.5);
  const double dl =
      shape_measurement.length() / (kSampleNumOnEgoForAgentCosting - 1);
  const double sin_theta = math::FastSin(x[StateIndex::THETA]);
  const double cos_theta = math::FastCos(x[StateIndex::THETA]);
  const double x_anchor = x[StateIndex::X_POS] - w * sin_theta;
  const double y_anchor = x[StateIndex::Y_POS] + w * cos_theta;

  for (int sample_pt_ix = 0; sample_pt_ix < kSampleNumOnEgoForAgentCosting;
       ++sample_pt_ix) {
    const double l =
        -shape_measurement.rear_bumper_to_rear_axle() + sample_pt_ix * dl;
    const auto& constraint_proximity =
        constraint_proximity_map->GetAgentProximity(x_anchor + l * cos_theta,
                                                    y_anchor + l * sin_theta,
                                                    is_left, is_critical);
    const double jacob_theta =
        (-l * sin_theta - w * cos_theta) * constraint_proximity.d_dist(0) +
        (l * cos_theta - w * sin_theta) * constraint_proximity.d_dist(1);

    if (is_critical) {
      DCHECK(constraint_proximity.critical_lat_gap.has_value());
    } else {
      DCHECK(constraint_proximity.comfort_lat_gap.has_value());
    }
    const double required_lateral_gap =
        is_critical ? constraint_proximity.critical_lat_gap.value()
                    : constraint_proximity.comfort_lat_gap.value();
    const planner::path::ClearanceType type =
        is_left ? planner::path::ClearanceType::kPositive
                : planner::path::ClearanceType::kNegative;
    UpdateClearanceRelatedFeatureLet(
        constraint_proximity.dist, constraint_proximity.d_dist(0),
        constraint_proximity.d_dist(1), jacob_theta, required_lateral_gap,
        /*weight_adjust=*/1.0, type, &feature->components[sample_pt_ix],
        /*tighter_buffer=*/nullptr);
  }
}

// Computes the clearance violation of EGO's current state.
void ComputeFullBodyClearanceViolation(
    const State& x, const std::vector<vehicle_model::DiskInfo>& ego_disks,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    bool is_left, bool is_critical,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    Feature* feature) {
  if (planner::FLAGS_planning_use_ego_sampled_points_for_sl_nudge_costing) {
    ComputeFullBodyClearanceViolationFeatureBySampledPoints(
        x, shape_measurement, is_left, is_critical, constraint_proximity_map,
        feature);
  } else {
    ComputeFullBodyClearanceViolationFeatureByDisk(
        ego_disks, x[StateIndex::THETA], is_left, is_critical,
        constraint_proximity_map, feature);
  }
}

// Generates physical boundaries' buffer through physical lateral gaps.
inline std::pair<double, double> GetPhysicalBufferPair(
    double critical_lat_gap, double comfort_lat_gap,
    double dynamic_critical_buffer) {
  const double tighter_buffer = std::min(
      dynamic_critical_buffer, critical_lat_gap + kPhysicalBarrierTolerance);
  const double wider_buffer =
      std::max(tighter_buffer + math::constants::kEpsilon, comfort_lat_gap);
  return {tighter_buffer, wider_buffer};
}

// Computes violation for kPhysical boundary using boxes and lateral clearances,
// and populate numerical info to feature.
void ComputePhysicalBoundaryViolation(
    const State& x,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<vehicle_model::DiskInfo>& /* ego_disks */,
    const WeightAdjustSpec& /* weight_adjust_spec */,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    bool is_left, Feature* feature) {
  const int sample_num_on_ego = kSampleNumOnEgoBoundary * kDenseSamplingFactor;

  feature->components.resize(sample_num_on_ego);

  const planner::path::ClearanceType type =
      is_left ? planner::path::ClearanceType::kPositive
              : planner::path::ClearanceType::kNegative;

  const double w = shape_measurement.width() * (is_left ? 0.5 : -0.5);
  const double dl = shape_measurement.length() / (sample_num_on_ego - 1);
  const double sin_theta = sin(x[StateIndex::THETA]);
  const double cos_theta = cos(x[StateIndex::THETA]);
  const double x_anchor = x[StateIndex::X_POS] - w * sin_theta;
  const double y_anchor = x[StateIndex::Y_POS] + w * cos_theta;

  for (int sample_pt_ix = 0; sample_pt_ix < sample_num_on_ego; ++sample_pt_ix) {
    const double l =
        -shape_measurement.rear_bumper_to_rear_axle() + sample_pt_ix * dl;

    const auto& constraint_proximity =
        constraint_proximity_map->GetPhysicalBoundaryProximity(
            x_anchor + l * cos_theta, y_anchor + l * sin_theta, is_left);

    const double dist = constraint_proximity.dist;
    const auto& d_dist = constraint_proximity.d_dist;
    DCHECK(constraint_proximity.critical_lat_gap.has_value());
    DCHECK(constraint_proximity.comfort_lat_gap.has_value());
    const auto [tighter_buffer, wider_buffer] =
        GetPhysicalBufferPair(*constraint_proximity.critical_lat_gap,
                              *constraint_proximity.comfort_lat_gap,
                              constraint_proximity_map->boundary_constraints()
                                  .road_corridor_safety_distance_buffer);

    const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                               (l * cos_theta - w * sin_theta) * d_dist(1);

    UpdateClearanceRelatedFeatureLet(
        dist, d_dist(0), d_dist(1), jacob_theta, wider_buffer,
        /*weight_adjust=*/1.0, type, &feature->components[sample_pt_ix],
        &tighter_buffer);
  }
}

// Computes violation for kVirtualHard boundary using boxes and clearances, and
// populate numerical info to feature.
void ComputeVirtualHardBoundaryViolation(
    const State& x,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    const std::vector<vehicle_model::DiskInfo>& /* ego_disks */,
    const WeightAdjustSpec& /* weight_adjust_spec */,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    int step, bool is_left, Feature* feature) {
  const double barrier_buffer = kControlError + kToleranceForVirtualHard;
  const double tighter_buffer = 0.0;

  feature->components.reserve(kSampleNumOnEgoBoundary);

  const planner::path::ClearanceType type =
      is_left ? planner::path::ClearanceType::kPositive
              : planner::path::ClearanceType::kNegative;

  const double w = shape_measurement.width() * (is_left ? 0.5 : -0.5);
  const double dl = shape_measurement.length() / (kSampleNumOnEgoBoundary - 1);
  const double sin_theta = sin(x[StateIndex::THETA]);
  const double cos_theta = cos(x[StateIndex::THETA]);
  const double x_anchor = x[StateIndex::X_POS] - w * sin_theta;
  const double y_anchor = x[StateIndex::Y_POS] + w * cos_theta;

  for (int i = 0; i < kSampleNumOnEgoBoundary; ++i) {
    const double l = -shape_measurement.rear_bumper_to_rear_axle() + i * dl;
    const double x = x_anchor + l * cos_theta;
    const double y = y_anchor + l * sin_theta;

    const auto& constraint_proximity =
        constraint_proximity_map->GetVirtualHardBoundaryProximity(x, y, is_left,
                                                                  step);
    if (!constraint_proximity.has_value()) {
      continue;
    }
    const double dist = constraint_proximity->dist;
    const auto& d_dist = constraint_proximity->d_dist;

    const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                               (l * cos_theta - w * sin_theta) * d_dist(1);
    feature->components.emplace_back();
    UpdateClearanceRelatedFeatureLet(dist, d_dist(0), d_dist(1), jacob_theta,
                                     /*wider_buffer=*/barrier_buffer,
                                     /*weight_adjust=*/1.0, type,
                                     &feature->components.back(),
                                     &tighter_buffer);
  }
}

// Computes violation for kVirtualSoft boundary using disks and clearances, and
// populate numerical info to feature.
void ComputeVirtualSoftBoundaryViolation(
    const State& x,
    const vehicle_model::pb::
        AxleRectangularMeasurement& /* shape_measurement */,
    const std::vector<vehicle_model::DiskInfo>& ego_disks,
    const WeightAdjustSpec& weight_adjust_spec,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    int step, bool is_left, Feature* feature) {
  const double sin_theta = sin(x[StateIndex::THETA]);
  const double cos_theta = cos(x[StateIndex::THETA]);

  const int effective_segment_num =
      constraint_proximity_map->GetEffectiveSegmentNumForVirtualSoftBoundaries(
          is_left, step);
  DCHECK_NOTNULL(feature)->components.resize(effective_segment_num *
                                             ego_disks.size());

  for (size_t i = 0; i < ego_disks.size(); ++i) {
    const auto& constraint_proximity_list =
        constraint_proximity_map->GetVirtualSoftBoundaryProximity(
            ego_disks[i].disk.center().x(), ego_disks[i].disk.center().y(),
            is_left, step);

    for (size_t k = 0; k < constraint_proximity_list.size(); ++k) {
      const auto& constraint_proximity = constraint_proximity_list[k];

      const double dist = constraint_proximity.dist;
      const auto& d_dist = constraint_proximity.d_dist;

      const double l = ego_disks[i].lon_offset;
      const double w = ego_disks[i].lat_offset;
      const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                                 (l * cos_theta - w * sin_theta) * d_dist(1);

      DCHECK(constraint_proximity.max_violation.has_value());
      const double tighter_buffer_with_disk =
          -(*constraint_proximity.max_violation) + ego_disks[i].disk.radius();
      const double wider_buffer_with_disk = ego_disks[i].disk.radius();
      DCHECK_LE(tighter_buffer_with_disk, wider_buffer_with_disk);

      const int component_idx = k * ego_disks.size() + i;
      DCHECK_LT(component_idx, feature->components.size());

      DCHECK(constraint_proximity.strength.has_value());
      double weight_adjust = GetBoundaryWeightAdjust(
          *constraint_proximity.strength,
          weight_adjust_spec.weight_adjust_for_boundary(),
          constraint_proximity.repulsion_attributes.strength_weight_ratio);
      const auto& repulsion_weight_decay_info =
          constraint_proximity.repulsion_attributes.repulsion_weight_decay_info;
      if (repulsion_weight_decay_info.has_value()) {
        weight_adjust *= planner::path::GetCostWeightByType(
            GetRepulsionWeightDecayType(
                repulsion_weight_decay_info->weight_decay_type),
            static_cast<double>(
                step - constraint_proximity.boundary_ddp_step_range.start_pos) /
                constraint_proximity.boundary_ddp_step_range.end_pos,
            repulsion_weight_decay_info->weight_decay_ratio_range.start_pos,
            repulsion_weight_decay_info->weight_decay_ratio_range.end_pos);
      }

      UpdateClearanceRelatedFeatureLet(
          dist, d_dist(0), d_dist(1), jacob_theta,
          /*wider_buffer*/ wider_buffer_with_disk, weight_adjust,
          /*clearance_type=*/
          is_left ? planner::path::ClearanceType::kPositive
                  : planner::path::ClearanceType::kNegative,
          &feature->components[component_idx], &tighter_buffer_with_disk);
    }
  }
}

// Computes violation for kPureVirtual boundary using disks and clearances, and
// populate numerical info to feature.
void ComputePureVirtualBoundaryViolation(
    const State& x,
    const vehicle_model::pb::
        AxleRectangularMeasurement& /* shape_measurement */,
    const std::vector<vehicle_model::DiskInfo>& ego_disks,
    const WeightAdjustSpec& weight_adjust_spec,
    planner::path::path_problem::ConstraintProximityMap*
        constraint_proximity_map,
    int step, bool is_left, Feature* feature) {
  const double sin_theta = sin(x[StateIndex::THETA]);
  const double cos_theta = cos(x[StateIndex::THETA]);

  const int effective_segment_num =
      constraint_proximity_map->GetEffectiveSegmentNumForPureVirtualBoundaries(
          is_left, step);
  DCHECK_NOTNULL(feature)->components.resize(effective_segment_num *
                                             ego_disks.size());

  for (size_t i = 0; i < ego_disks.size(); ++i) {
    const auto& constraint_proximity_list =
        constraint_proximity_map->GetPureVirtualBoundaryProximity(
            ego_disks[i].disk.center().x(), ego_disks[i].disk.center().y(),
            is_left, step);

    for (size_t k = 0; k < constraint_proximity_list.size(); ++k) {
      const auto& constraint_proximity = constraint_proximity_list[k];

      const double dist = constraint_proximity.dist;
      const auto& d_dist = constraint_proximity.d_dist;

      const double l = ego_disks[i].lon_offset;
      const double w = ego_disks[i].lat_offset;
      const double jacob_theta = (-l * sin_theta - w * cos_theta) * d_dist(0) +
                                 (l * cos_theta - w * sin_theta) * d_dist(1);

      const int component_idx = k * ego_disks.size() + i;
      DCHECK_LT(component_idx, feature->components.size());

      DCHECK(constraint_proximity.strength.has_value());
      double weight_adjust = GetBoundaryWeightAdjust(
          *constraint_proximity.strength,
          weight_adjust_spec.weight_adjust_for_boundary(),
          constraint_proximity.repulsion_attributes.strength_weight_ratio);
      const auto& repulsion_weight_decay_info =
          constraint_proximity.repulsion_attributes.repulsion_weight_decay_info;
      if (repulsion_weight_decay_info.has_value()) {
        weight_adjust *= planner::path::GetCostWeightByType(
            GetRepulsionWeightDecayType(
                repulsion_weight_decay_info->weight_decay_type),
            static_cast<double>(
                step - constraint_proximity.boundary_ddp_step_range.start_pos) /
                constraint_proximity.boundary_ddp_step_range.end_pos,
            repulsion_weight_decay_info->weight_decay_ratio_range.start_pos,
            repulsion_weight_decay_info->weight_decay_ratio_range.end_pos);
      }

      UpdateClearanceRelatedFeatureLet(
          dist, d_dist(0), d_dist(1), jacob_theta,
          /*wider_buffer*/ 0.0 + ego_disks[i].disk.radius(), weight_adjust,
          /*clearance_type=*/
          is_left ? planner::path::ClearanceType::kPositive
                  : planner::path::ClearanceType::kNegative,
          &feature->components[component_idx], /*tighter_buffer=*/nullptr);
    }
  }
}

adv_geom::PathPose2d GetEgoPoseByConstCurvatureModel(
    const planner::path::JukeIntegratedPathFormula::State& state,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    RiskAwareMotionModel risk_model, double ds) {
  const double x = state[StateIndex::X_POS];
  const double y = state[StateIndex::Y_POS];
  const double heading = state[StateIndex::THETA];
  const vehicle_model::CachedShapeData cached_shape_data =
      vehicle_model::GetCachedShapeData(shape_measurement, x, y, heading);
  const bool using_const_heading_model =
      risk_model == RiskAwareMotionModel::kConstHeading;
  const double curvature =
      using_const_heading_model ? 0.0 : state[StateIndex::KAPPA];
  const double curvature_sqr = math::Sqr(curvature);

  const double state_heading = state[StateIndex::THETA] + ds * curvature;

  // Taylor expansion is faster than sin/cos computation.

  const double ds_sqr = math::Sqr(ds);
  const double ds_cube = ds_sqr * ds;
  const double state_x =
      x + cached_shape_data.cos_theta * ds -
      kSecondOrderCoef * cached_shape_data.sin_theta * curvature * ds_sqr -
      kThirdOrderCoef * curvature_sqr * cached_shape_data.cos_theta * ds_cube;

  const double state_y =
      y + cached_shape_data.sin_theta * ds +
      kSecondOrderCoef * cached_shape_data.cos_theta * curvature * ds_sqr +
      -kThirdOrderCoef * curvature_sqr * cached_shape_data.sin_theta * ds_cube;
  return adv_geom::PathPose2d(
      /*arc_length=*/0.0, math::geometry::Point2d(state_x, state_y),
      state_heading, curvature);
}

[[maybe_unused]] std::pair<std::vector<vehicle_model::DiskInfo>,
                           vehicle_model::CachedShapeData>
GetEgoDisksInfoByConstCurvatureModel(
    const planner::path::JukeIntegratedPathFormula::State& state,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    RiskAwareMotionModel risk_model, double ds, double radius,
    int covering_disks_num) {
  const auto state_pose =
      GetEgoPoseByConstCurvatureModel(state, shape_measurement, risk_model, ds);

  return vehicle_model::GetCoveringDiskAndCachedShapeInfo(
      shape_measurement, state_pose.pos_.x(), state_pose.pos_.y(),
      state_pose.heading_, radius, covering_disks_num);
}

bool SoftRiskCostOnPhysicalBoundary(
    const planner::path::JukeIntegratedPathFormula::State& x,
    const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement,
    double speed_reference, double buffer,
    const std::vector<double>& merged_physical_lateral_clearances,
    double default_lateral_clearance, double resolution,
    const math::Range1d& arclength_range, bool is_left_side, int step,
    int total_step_size,
    const control_util::pb::CostingWeightAdjustSpec& weight_adjust_spec,
    RiskAwareMotionModel risk_model, EgoGeometricModel ego_model,
    math::geometry::PolylineCurve2dProximityCache* reference,
    Feature* feature) {
  DCHECK(feature != nullptr);
  const bool using_const_heading_model =
      risk_model == RiskAwareMotionModel::kConstHeading;
  const planner::path::ClearanceType type =
      is_left_side ? planner::path::ClearanceType::kPositive
                   : planner::path::ClearanceType::kNegative;

  const double speed_reference_squared = math::Sqr(speed_reference);
  const double stop_distance =
      std::min(kMaxArclengthForwardStateInMeter,
               0.5 * speed_reference_squared / kMaxStopDecelMpss);
  const int costed_state_num =
      math::CeilToIntWithTol(stop_distance / shape_measurement.length());
  const double step_length = stop_distance / costed_state_num;

  const double signed_default_lateral_clearance =
      is_left_side ? default_lateral_clearance : -default_lateral_clearance;

  const double curvature =
      using_const_heading_model ? 0.0 : x[StateIndex::KAPPA];
  const double curvature_sqr = math::Sqr(curvature);
  const double sin_theta0 = sin(x[StateIndex::THETA]);
  const double cos_theta0 = math::GetCosBySin(x[StateIndex::THETA], sin_theta0);
  math::EVector2x1 query_pt;
  double weight_adjust = planner::path::GetCostWeightByType(
                             weight_adjust_spec.type(),
                             static_cast<double>(step) / total_step_size,
                             weight_adjust_spec.weight_limit().min_val(),
                             weight_adjust_spec.weight_limit().max_val()) /
                         costed_state_num;

  const bool use_disks_as_ego_model = (ego_model == EgoGeometricModel::kDisks);

  int covering_disks_num = 0;
  double disk_radius = 0.0;
  int sample_point_num_on_bbox = 0;
  if (use_disks_as_ego_model) {
    covering_disks_num = vehicle_model::kDefaultEgoDiskNumber;
    disk_radius =
        vehicle_model::GetDiskRadius(shape_measurement, covering_disks_num);
    feature->components.resize(costed_state_num * covering_disks_num);
  } else {
    DCHECK(ego_model == EgoGeometricModel::kBBox);
    sample_point_num_on_bbox = kSampleNumOnEgoBoundary;
    feature->components.resize(costed_state_num * sample_point_num_on_bbox);
  }
  auto process_cost_feature = [&](double l, double w,
                                  const math::EVector2x1& query_pt, double ds,
                                  double ds_sqr, double ds_cube,
                                  double sin_theta1, double cos_theta1,
                                  double buffer_with_shape,
                                  FeatureLet& feature_let) {
    double dist = 0.0;
    math::EVector2x1 d_dist = math::EVector2x1::Zero();
    reference->Query(query_pt, resolution, arclength_range.start_pos,
                     arclength_range.end_pos, signed_default_lateral_clearance,
                     merged_physical_lateral_clearances, &dist, &d_dist);
    const double jacob_theta =
        d_dist(0) * (-sin_theta0 * ds -
                     kSecondOrderCoef * cos_theta0 * curvature * ds_sqr +
                     kThirdOrderCoef * sin_theta0 * curvature_sqr * ds_cube -
                     l * sin_theta1 - w * cos_theta1) +
        d_dist(1) * (cos_theta0 * ds -
                     kSecondOrderCoef * sin_theta0 * curvature * ds_sqr -
                     kThirdOrderCoef * curvature_sqr * cos_theta0 * ds_cube +
                     l * cos_theta1 - w * sin_theta1);

    const double jacob_k =
        using_const_heading_model
            ? 0.0
            : (d_dist(0) *
                   (-kSecondOrderCoef * sin_theta0 * ds_sqr -
                    2 * kThirdOrderCoef * cos_theta0 * curvature * ds_cube -
                    l * sin_theta1 * ds - w * cos_theta1 * ds) +
               d_dist(1) *
                   (kSecondOrderCoef * cos_theta0 * ds_sqr -
                    2 * kThirdOrderCoef * sin_theta0 * curvature * ds_cube +
                    l * cos_theta1 * ds - w * sin_theta1 * ds));

    UpdateClearanceAndCurvatureRelatedFeatureLet(
        dist, d_dist(0), d_dist(1), jacob_theta, jacob_k, buffer_with_shape,
        weight_adjust, type, &feature_let);
  };

  for (int i = 0; i < costed_state_num; ++i) {
    // the arclength of i-th state is (i+1)*step_length.
    const double ds = (i + 1) * step_length;
    const double ds_sqr = math::Sqr(ds);
    const double ds_cube = ds_sqr * ds;
    const auto state_pose =
        GetEgoPoseByConstCurvatureModel(x, shape_measurement, risk_model, ds);

    if (use_disks_as_ego_model) {
      const auto [inferred_state_disks, cached_shape_data] =
          vehicle_model::GetCoveringDiskAndCachedShapeInfo(
              shape_measurement, state_pose.pos_.x(), state_pose.pos_.y(),
              state_pose.heading_, disk_radius, covering_disks_num);
      const double sin_theta1 = cached_shape_data.sin_theta;
      const double cos_theta1 = cached_shape_data.cos_theta;
      for (size_t disk_ix = 0; disk_ix < inferred_state_disks.size();
           ++disk_ix) {
        query_pt << inferred_state_disks[disk_ix].disk.center().x(),
            inferred_state_disks[disk_ix].disk.center().y();
        auto& feature_let =
            feature->components[i * inferred_state_disks.size() + disk_ix];

        const double l = inferred_state_disks[disk_ix].lon_offset;
        const double w = inferred_state_disks[disk_ix].lat_offset;
        const double buffer_with_shape =
            std::max(buffer - kUncertaintyStepBufferReduction * i, 0.0) +
            inferred_state_disks[disk_ix].disk.radius();
        process_cost_feature(l, w, query_pt, ds, ds_sqr, ds_cube, sin_theta1,
                             cos_theta1, buffer_with_shape, feature_let);
      }
    } else {
      const double sin_theta1 = sin(state_pose.heading_);
      const double cos_theta1 = cos(state_pose.heading_);
      const double w = shape_measurement.width() * (is_left_side ? 0.5 : -0.5);
      DCHECK_GT(sample_point_num_on_bbox, 1);
      const double dl =
          shape_measurement.length() / (sample_point_num_on_bbox - 1);
      const double x_anchor = state_pose.pos_.x() - w * sin_theta1;
      const double y_anchor = state_pose.pos_.y() + w * cos_theta1;
      for (int sample_pt_ix = 0; sample_pt_ix < sample_point_num_on_bbox;
           ++sample_pt_ix) {
        auto& feature_let =
            feature->components[i * sample_point_num_on_bbox + sample_pt_ix];
        const double l =
            -shape_measurement.rear_bumper_to_rear_axle() + sample_pt_ix * dl;
        query_pt << x_anchor + l * cos_theta1, y_anchor + l * sin_theta1;
        const double buffer_with_shape =
            std::max(buffer - kUncertaintyStepBufferReduction * i, 0.0);
        process_cost_feature(l, w, query_pt, ds, ds_sqr, ds_cube, sin_theta1,
                             cos_theta1, buffer_with_shape, feature_let);
      }
    }
    weight_adjust *= kUncertaintyStepFactor;
  }

  return true;
}
}  // namespace

pb::PathFeatureParameter MergeWithMotionModelParams(
    control_util::pb::PathFeatureParameter feature_param,
    const vehicle_model::JukeIntegratedModelWithAxleRectangularShape&
        motion_model_with_shape) {
  *feature_param.mutable_juke_integrated_model_param() =
      motion_model_with_shape.param();
  *feature_param.mutable_shape_measurement() =
      motion_model_with_shape.shape_measurement();
  return feature_param;
}

template <>
StepwisePrimitives
planner::path::JukeIntegratedPathFormula::GetStepwisePrimitives(
    int /* step */, bool /* is_terminal */, const State& x,
    const Control& /* u */, const Primitives& /* primitives */) const {
  return {.ego_representation = vehicle_model::VehicleRepresentation(
              feature_param_.shape_measurement(), x[StateIndex::X_POS],
              x[StateIndex::Y_POS], x[StateIndex::THETA],
              /*disk_numbers=*/{3})};
}

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kJukeIntegratedPath>(
    int step, int total_step_size, const CostingTerm& costing_term,
    const FeatureParameter& feature_param, const State& x, const Control& u,
    const StepwisePrimitives& stepwise_primitives,
    const planner::path::JukeIntegratedPathFormula::Primitives& primitives,
    planner::path::JukeIntegratedPathFormula::Feature* feature) {
  const auto& feature_type = costing_term.feature_type();
  const auto& weight_adjust_spec = costing_term.weight_adjust_spec();
  const std::vector<vehicle_model::DiskInfo>& ego_disks =
      stepwise_primitives.ego_representation.disks().at(
          vehicle_model::kDefaultEgoDiskNumber);
  const auto& ego_box = stepwise_primitives.ego_representation.box();
  bool is_terminal = (step == total_step_size);
  // Use destination ddp index to determine whether we should skip some of cost
  // terms.
  const auto& target_status_info =
      primitives.path_metadata_primitive.target_info;
  if (ShouldSkipCostingTerm(feature_type, target_status_info, step)) {
    return;
  }

  switch (feature_type) {
    case FeatureType::CONSTRAINT_KAPPA:
      if (costing_term.costing_spec().type() ==
          pb::CostingType::AL_INEQUALITY_CONSTRAINT) {
        planner::path::ALConstraintKappa(
            x, feature_param.juke_integrated_model_param().limit(), feature);
      } else {
        planner::path::ConstraintKappa(
            x, feature_param.juke_integrated_model_param().limit(), feature);
      }
      break;
    case FeatureType::CONSTRAINT_ETA:
      planner::path::ConstraintEta(
          x, feature_param.juke_integrated_model_param().limit(), feature);
      break;
    case FeatureType::CONSTRAINT_GAMMA:
      if (!is_terminal) {
        planner::path::ConstraintGamma(u, feature_param.max_abs_juke(),
                                       feature);
      }
      break;
    case FeatureType::CONSTRAINT_APPROXIMATE_LATERAL_ACCEL:
      planner::path::ConstraintApproximateLateralAccel(
          x,
          primitives.path_speed_info_primitive
              .path_step_ref_slow_down_speed[step],
          feature_param.max_lateral_acceleration(),
          primitives.path_heuristic_primitive.reference_curve.get(), feature);
      break;
    case FeatureType::CONSTRAINT_APPROXIMATE_LATERAL_JERK:
      planner::path::ConstraintApproximateLateralJerk(
          x,
          primitives.path_speed_info_primitive
              .path_step_ref_slow_down_speed[step],
          feature_param.max_lateral_jerk(),
          primitives.path_heuristic_primitive.reference_curve.get(), feature);
      break;
    case FeatureType::CONSTRAINT_TERMINAL_HEADING:
      if (target_status_info) {
        DCHECK(weight_adjust_spec.has_weight_adjust_for_behavior());
        const double weight_adjust = GetWeightAdjustForTerminalHeading(
            primitives.path_maneuver_primitive.motion_mode,
            primitives.path_maneuver_primitive.behavior_type,
            weight_adjust_spec.weight_adjust_for_behavior());
        planner::path::ConstraintTerminalHeading(
            x,
            /*target_heading=*/
            target_status_info->target_heading,
            /*heading_tolerance=*/kTerminalHeadingToleranceInRad, step,
            total_step_size,
            /*weight_adjust=*/weight_adjust,
            /*target_index_range=*/
            target_status_info->target_index_range, weight_adjust_spec,
            feature);
      }
      break;

    case FeatureType::CONSTRAINT_APPROXIMATE_STEERING_ACCEL:
      if (costing_term.costing_spec().type() ==
          pb::CostingType::AL_INEQUALITY_CONSTRAINT) {
        planner::path::ALConstraintApproximateSteeringAccel(
            u,
            primitives.path_speed_info_primitive
                .path_step_ref_slow_down_speed[step],
            feature_param.juke_integrated_model_param()
                .measurement()
                .wheel_base(),
            feature_param.max_abs_steering_accel(), feature);
      } else {
        planner::path::ConstraintApproximateSteeringAccel(
            u,
            primitives.path_speed_info_primitive
                .path_step_ref_slow_down_speed[step],
            feature_param.juke_integrated_model_param()
                .measurement()
                .wheel_base(),
            feature_param.max_abs_steering_accel(), feature);
      }
      break;

    case FeatureType::CONSTRAINT_APPROXIMATE_STEERING_RATE:
      if (primitives.path_maneuver_primitive.intention_homotopy ==
          planner::pb::IntentionResult::IGNORE_ALL) {
        planner::path::ConstraintApproximateSteeringRate(
            x, primitives.path_speed_info_primitive.ego_speed_squared,
            primitives.path_speed_info_primitive
                .path_step_ref_slow_down_speed[step],
            feature_param.juke_integrated_model_param()
                .measurement()
                .wheel_base(),
            primitives.path_heuristic_primitive.reference_curve.get(), feature);
      }
      break;
    case FeatureType::EFFORT_KAPPA:
      planner::path::EffortKappa(x, feature);
      break;
    case FeatureType::EFFORT_ETA:
      planner::path::EffortEta(x, feature);
      break;
    case FeatureType::EFFORT_GAMMA:
      if (!is_terminal) {
        planner::path::EffortGamma(u, feature);
      }
      break;
    case FeatureType::EFFORT_APPROXIMATE_LATERAL_ACCEL:
      planner::path::EffortApproximateLateralAccel(
          x,
          std::max(primitives.path_speed_info_primitive
                       .path_step_ref_slow_down_speed[step],
                   primitives.path_speed_info_primitive
                       .path_step_ref_intention_speed[step]),
          step, total_step_size, weight_adjust_spec,
          primitives.path_heuristic_primitive.reference_curve.get(), feature);
      break;
    case FeatureType::EFFORT_APPROXIMATE_LATERAL_JERK:
      planner::path::EffortApproximateLateralJerk(
          x,
          std::max(primitives.path_speed_info_primitive
                       .path_step_ref_slow_down_speed[step],
                   primitives.path_speed_info_primitive
                       .path_step_ref_intention_speed[step]),
          step, total_step_size, weight_adjust_spec,
          primitives.path_heuristic_primitive.reference_curve.get(), feature);
      break;
    case planner::path::JukeIntegratedPathFormula::PbFeatureType::
        EFFORT_APPROXIMATE_STEERING_RATE:
      planner::path::EffortApproximateSteeringRate(
          x,
          feature_param.juke_integrated_model_param()
              .measurement()
              .wheel_base(),
          std::max(primitives.path_speed_info_primitive
                       .path_step_ref_slow_down_speed[step],
                   primitives.path_speed_info_primitive
                       .path_step_ref_intention_speed[step]),
          step, total_step_size,
          primitives.path_heuristic_primitive.reference_curve.get(),
          weight_adjust_spec, feature);
      break;
    case FeatureType::EFFORT_APPROXIMATE_STEERING_ACCEL:
      planner::path::EffortApproximateSteeringAccel(
          u,
          feature_param.juke_integrated_model_param()
              .measurement()
              .wheel_base(),
          std::max(primitives.path_speed_info_primitive
                       .path_step_ref_slow_down_speed[step],
                   primitives.path_speed_info_primitive
                       .path_step_ref_intention_speed[step]),
          step, total_step_size, weight_adjust_spec, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_LEFT_NUDGE_CORRIDOR:
      if (!planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyPolylineViolationFeature(
            ego_disks, x[StateIndex::THETA],
            planner::path::ClearanceType::kNegative,
            primitives.path_nudge_primitive.hard_nudge_corridors[step]
                .left_boundary.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_NUDGE_CORRIDOR:
      if (!planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyPolylineViolationFeature(
            ego_disks, x[StateIndex::THETA],
            planner::path::ClearanceType::kPositive,
            primitives.path_nudge_primitive.hard_nudge_corridors[step]
                .right_boundary.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_LEFT_NUDGE_CORRIDOR:
      if (!planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyPolylineViolationFeature(
            ego_disks, x[StateIndex::THETA],
            planner::path::ClearanceType::kNegative,
            primitives.path_nudge_primitive.soft_nudge_corridors[step]
                .left_boundary.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_RIGHT_NUDGE_CORRIDOR:
      if (!planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyPolylineViolationFeature(
            ego_disks, x[StateIndex::THETA],
            planner::path::ClearanceType::kPositive,
            primitives.path_nudge_primitive.soft_nudge_corridors[step]
                .right_boundary.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_VIOLATION_LEFT_NUDGE_CLEARANCE:
      if (planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyClearanceViolation(
            x, ego_disks, feature_param.shape_measurement(),
            /*is_left=*/true,
            /*is_critical=*/true, primitives.constraint_proximity_map.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_NUDGE_CLEARANCE:
      if (planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyClearanceViolation(
            x, ego_disks, feature_param.shape_measurement(),
            /*is_left=*/false,
            /*is_critical=*/true, primitives.constraint_proximity_map.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_LEFT_NUDGE_CLEARANCE:
      if (planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyClearanceViolation(
            x, ego_disks, feature_param.shape_measurement(),
            /*is_left=*/true,
            /*is_critical=*/false, primitives.constraint_proximity_map.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_RIGHT_NUDGE_CLEARANCE:
      if (planner::FLAGS_planning_enable_sl_nudge_costing) {
        ComputeFullBodyClearanceViolation(
            x, ego_disks, feature_param.shape_measurement(),
            /*is_left=*/false,
            /*is_critical=*/false, primitives.constraint_proximity_map.get(),
            feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_NOMINAL_PATH_ATTRACTION:
      planner::path::PrimitiveSoftPathAttraction(
          ego_box.center_segment().end(), x[StateIndex::THETA],
          feature_param.juke_integrated_model_param(),
          feature_param.shape_measurement(),
          primitives.path_heuristic_primitive.weight_adjusted_regions, step,
          total_step_size,
          primitives.path_heuristic_primitive.attraction_path.get(),
          weight_adjust_spec, ShouldEnhanceAttraction(primitives), feature);

      break;
    case FeatureType::PRIMITIVE_SOFT_NOMINAL_PATH_HEADING_ATTRACTION:
      // TODO(huanhui): 1. Now we only address u-turn scene, since reference
      // smoothing
      // is only triggered in u-turn scene. 2. Improve this logic, we should
      // deprecate scenario information(u-turn logic), and use a more reasonable
      // way to determine.
      if (primitives.path_maneuver_primitive.is_in_uturn) {
        planner::path::ComputeReferenceHeadingAttraction(
            x,
            /*heading_tolerance=*/kReferenceHeadingToleranceInRad, step,
            total_step_size,
            primitives.path_heuristic_primitive.attraction_path.get(),
            weight_adjust_spec, ShouldEnhanceAttraction(primitives), feature);
      }
      break;
    case FeatureType::PRIMITIVE_SOFT_NOMINAL_PATH_CURVATURE_ATTRACTION:
      // TODO(huanhui): 1. Now we only address u-turn scene, since reference
      // smoothing
      // is only triggered in u-turn scene. 2. Improve this logic, we should
      // deprecate scenario information(u-turn logic), and use a more reasonable
      // way to determine.
      if (primitives.path_maneuver_primitive.is_in_uturn) {
        planner::path::ComputeReferenceCurvatureAttraction(
            x,
            /*curvature_tolerance=*/kReferenceCurvatureTolerance, step,
            total_step_size,
            primitives.path_heuristic_primitive.attraction_path.get(),
            primitives.path_heuristic_primitive.attraction_path_curve,
            weight_adjust_spec, ShouldEnhanceAttraction(primitives), feature);
      }

      break;
    case FeatureType::PRIMITIVE_VIOLATION_LEFT_PHYSICAL_BOUNDARY:
      ComputePhysicalBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(),
          /*is_left=*/true, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_PHYSICAL_BOUNDARY:
      ComputePhysicalBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(),
          /*is_left=*/false, feature);
      break;

    case FeatureType::PRIMITIVE_VIOLATION_LEFT_VIRTUAL_HARD_BOUNDARY:
      ComputeVirtualHardBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/true, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_VIRTUAL_HARD_BOUNDARY:
      ComputeVirtualHardBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/false, feature);
      break;

    case FeatureType::PRIMITIVE_VIOLATION_LEFT_VIRTUAL_SOFT_BOUNDARY:
      ComputeVirtualSoftBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/true, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_VIRTUAL_SOFT_BOUNDARY:
      ComputeVirtualSoftBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/false, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_LEFT_PURE_VIRTUAL_BOUNDARY:
      ComputePureVirtualBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/true, feature);
      break;
    case FeatureType::PRIMITIVE_VIOLATION_RIGHT_PURE_VIRTUAL_BOUNDARY:
      ComputePureVirtualBoundaryViolation(
          x, feature_param.shape_measurement(), ego_disks, weight_adjust_spec,
          primitives.constraint_proximity_map.get(), step,
          /*is_left=*/false, feature);
      break;
    case FeatureType::PRIMITIVE_SOFT_LEFT_RISK_PHYSICAL_BOUNDARY:
      if (step < kRiskCostStateNumForPhysical) {
        SoftRiskCostOnPhysicalBoundary(
            x, feature_param.shape_measurement(),
            primitives.path_speed_info_primitive
                .path_step_ref_intention_speed[step],
            /*buffer=*/kRiskCostBufferForPhysical,
            primitives.constraint_proximity_map->lateral_clearance_data()
                .left_lateral_clearances(),
            planner::path::SemanticCorridor::kMaxLateralClearance,
            primitives.constraint_proximity_map->lateral_clearance_data()
                .sampled_resolution(),
            primitives.constraint_proximity_map->lateral_clearance_data()
                .effective_reference_range(),
            /*is_left_side=*/true, step, total_step_size, weight_adjust_spec,
            RiskAwareMotionModel::kConstHeading, EgoGeometricModel::kBBox,
            primitives.constraint_proximity_map->reference_path_cache().get(),
            feature);
      }
      break;

    case FeatureType::PRIMITIVE_SOFT_RIGHT_RISK_PHYSICAL_BOUNDARY:
      if (step < kRiskCostStateNumForPhysical) {
        SoftRiskCostOnPhysicalBoundary(
            x, feature_param.shape_measurement(),
            primitives.path_speed_info_primitive
                .path_step_ref_intention_speed[step],
            /*buffer=*/kRiskCostBufferForPhysical,
            primitives.constraint_proximity_map->lateral_clearance_data()
                .right_lateral_clearances(),
            planner::path::SemanticCorridor::kMaxLateralClearance,
            primitives.constraint_proximity_map->lateral_clearance_data()
                .sampled_resolution(),
            primitives.constraint_proximity_map->lateral_clearance_data()
                .effective_reference_range(),
            /*is_left_side=*/false, step, total_step_size, weight_adjust_spec,
            RiskAwareMotionModel::kConstHeading, EgoGeometricModel::kBBox,
            primitives.constraint_proximity_map->reference_path_cache().get(),
            feature);
      }
      break;

    case FeatureType::PRIMITIVE_SOFT_ATTRACTION_SEGMENTS:
      planner::path::ComputeAttractionSegmentCost(
          ego_box.center_segment().end(), x[StateIndex::THETA],
          *primitives.path_heuristic_primitive.attractions,
          primitives.path_maneuver_primitive, weight_adjust_spec,
          feature_param.juke_integrated_model_param(),
          feature_param.shape_measurement(), step, total_step_size, feature);
      break;

    default:
      DCHECK(false) << "Unrecognized feature type!";
      (*feature) = Feature(1);
  }
}

}  // namespace control_util
