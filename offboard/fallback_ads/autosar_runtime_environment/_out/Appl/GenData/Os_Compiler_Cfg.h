/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                 This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                 Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                 All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  LICENSE
 *  -------------------------------------------------------------------------------------------------------------------
 *            Module: Os
 *           Program: MSR_Vector_SLP4
 *          Customer: Beijing Voyager Technology Co., Ltd.
 *       Expiry Date: 2023-12-31
 *  Ordered Derivat.: TC397X_StepB
 *    License Scope : The usage is restricted to CBD2100894_D00
 *
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *              File: Os_Compiler_Cfg.h
 *   Generation Time: 2022-04-29 11:33:53
 *           Project: DiDi_FBU - Version 1.0
 *          Delivery: CBD2100894_D00
 *      Tool Version: DaVinci Configurator (beta) 5.24.40 SP3
 *
 *
 *********************************************************************************************************************/

/**********************************************************************************************************************
 ! BETA VERSION                                                                                                       !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
 ! This version of DaVinci Configurator Pro and/or the related Basic Software Package is BETA software.               !
 ! BETA Software is basically operable, but not sufficiently tested, verified and/or qualified for use in series      !
 ! production and/or in vehicles operating on public or non-public roads.                                             !
 ! In particular, without limitation, BETA Software may cause unpredictable ECU behavior, may not provide all         !
 ! functions necessary for use in series production and/or may not comply with quality requirements which are         !
 ! necessary according to the state of the art. BETA Software must not be used in series production.                  !
 !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
**********************************************************************************************************************/

#ifndef OS_COMPILER_CFG_H
# define OS_COMPILER_CFG_H

/**********************************************************************************************************************
 *  OS USER CALLOUT CODE SECTIONS
 *********************************************************************************************************************/

# define OS_CANISR_0_CODE
# define OS_CANISR_1_CODE
# define OS_CANISR_10_CODE
# define OS_CANISR_11_CODE
# define OS_CANISR_2_CODE
# define OS_CANISR_3_CODE
# define OS_CANISR_4_CODE
# define OS_CANISR_5_CODE
# define OS_CANISR_6_CODE
# define OS_CANISR_7_CODE
# define OS_CANISR_8_CODE
# define OS_CANISR_9_CODE
# define OS_ETHISR_ETHCTRLCONFIG_FBU_ETHINTERRUPTSERVICEROUTINE_S2_CODE
# define OS_ETHISR_ETHCTRLCONFIG_FBU_ETHINTERRUPTSERVICEROUTINE_S6_CODE
# define OS_MAINTASK_CORE0_10MS_CODE
# define OS_MAINTASK_CORE0_20MS_CODE
# define OS_MAINTASK_CORE0_5MS_CODE
# define OS_MAINTASK_CORE0_60MS_CODE
# define OS_MAINTASK_CORE1_10MS_CODE
# define OS_MAINTASK_CORE2_10MS_CODE
# define OS_MAINTASK_CORE3_10MS_CODE
# define OS_MAINTASK_CORE4_10MS_CODE
# define OS_MAINTASK_CORE5_10MS_CODE
# define OS_OSTASK_INIT_CORE0_CODE
# define OS_OSTASK_INIT_CORE0_APP_CODE
# define OS_OSTASK_INIT_CORE0_END_CODE
# define OS_OSTASK_INIT_CORE1_CODE
# define OS_OSTASK_INIT_CORE1_APP_CODE
# define OS_OSTASK_INIT_CORE2_CODE
# define OS_OSTASK_INIT_CORE2_APP_CODE
# define OS_OSTASK_INIT_CORE3_CODE
# define OS_OSTASK_INIT_CORE3_APP_CODE
# define OS_OSTASK_INIT_CORE4_CODE
# define OS_OSTASK_INIT_CORE4_APP_CODE
# define OS_OSTASK_INIT_CORE5_CODE
# define OS_OSTASK_INIT_CORE5_APP_CODE
# define OS_SCHMTASK_CORE0_CODE
# define OS_SCHMTASK_CORE1_CODE
# define OS_SCHMTASK_CORE2_CODE
# define OS_SCHMTASK_CORE3_CODE
# define OS_SCHMTASK_CORE4_CODE
# define OS_SCHMTASK_CORE5_CODE


#endif /* OS_COMPILER_CFG_H */

/**********************************************************************************************************************
 *  END OF FILE: Os_Compiler_Cfg.h
 *********************************************************************************************************************/
 
