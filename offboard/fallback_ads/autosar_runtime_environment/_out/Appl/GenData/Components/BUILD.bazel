package(default_visibility = ["//visibility:public"])

cc_library(
    name = "rte_acu_sig_input_type",
    hdrs = ["Rte_AcuSigInput_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_acu_sig_input",
    hdrs = ["Rte_AcuSigInput.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_acu_sig_input_type",
    ],
)

cc_library(
    name = "rte_camera_sig_input_type",
    hdrs = ["Rte_CameraSigInput_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_camera_sig_input",
    hdrs = ["Rte_CameraSigInput.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_camera_sig_input_type",
    ],
)

cc_library(
    name = "rte_control_type",
    hdrs = ["Rte_Control_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_control",
    hdrs = ["Rte_Control.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_control_type",
    ],
)

cc_library(
    name = "rte_debug_type",
    hdrs = ["Rte_Debug_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_debug",
    hdrs = ["Rte_Debug.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_debug_type",
    ],
)

cc_library(
    name = "rte_fallback_sig_output_type",
    hdrs = ["Rte_FallbackSigOutput_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_fallback_sig_output",
    hdrs = ["Rte_FallbackSigOutput.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_fallback_sig_output_type",
    ],
)

cc_library(
    name = "rte_lateral_function_type",
    hdrs = ["Rte_LatCtrlFct_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_lateral_function",
    hdrs = ["Rte_LatCtrlFct.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
    ],
)

cc_library(
    name = "rte_long_function_type",
    hdrs = ["Rte_LgtCtrlFct_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_long_function",
    hdrs = ["Rte_LgtCtrlFct.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_long_function_type",
    ],
)

cc_library(
    name = "rte_vehicle_signal_input_type",
    hdrs = ["Rte_VehSigInput_Type.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
    ],
)

cc_library(
    name = "rte_vehicle_signal_input",
    hdrs = ["Rte_VehSigInput.h"],
    copts = ["-x c-header"],
    include_prefix = "_out/Appl/GenData/Components",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_data_handle_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_vehicle_signal_input_type",
    ],
)
