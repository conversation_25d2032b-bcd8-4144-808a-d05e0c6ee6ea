#ifndef ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_ASSIST_WAYPOINT_ASSIST_TRACKER_H_
#define ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_ASSIST_WAYPOINT_ASSIST_TRACKER_H_

#include <deque>
#include <map>
#include <memory>
#include <optional>
#include <unordered_set>
#include <utility>
#include <vector>

#include "planner/assist/assist_task_definition/assist_task_base.h"
#include "planner/behavior/util/lane_common/lane_sequence_result_definition.h"
#include "planner/utility/state/planner_internal_state_interface.h"
#include "planner/world_model/current_lane_associator/current_lane_associator.h"
#include "planner/world_model/current_lane_reasoner/current_lane_reasoner.h"
#include "planner/world_model/route_model/global_route_solution.h"
#include "planner/world_model/route_model/route_model.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner_protos/behavior_reasoner.pb.h"
#include "planner_protos/behavior_reasoners_debug.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/state/world_model.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {

class WaypointAssistTrackerSpeedData {
 public:
  WaypointAssistTrackerSpeedData(
      bool has_arrived_at_waypoint_assist_path_end,
      bool is_waypoint_triggered_by_routing, bool is_ego_in_oppsite_road,
      bool is_road_entirely_blocked, bool is_last_waypoint_in_opposite_lanes,
      bool is_turn_right_scene, bool is_ego_in_waypoint_lane_sequence,
      pb::EgoReachTargetLaneSequenceState::HasReached
          has_ego_reached_target_lane_sequence,
      const std::optional<voy::Point2d>& waypoint_assist_end_point,
      const double waypoint_assist_arclength,
      const std::shared_ptr<const LaneSequenceResult>&
          lane_sequence_result_for_waypoint_assist,
      const std::unordered_set<const pnc_map::Lane*>& drawable_lane_sequence,
      pb::AssistManeuverPhase::Enum waypoint_assist_phase,
      pb::AssistManeuverPhase::Enum last_waypoint_assist_phase,
      ::google::protobuf::RepeatedPtrField<::voy::Point2d>& reference_points)
      : waypoint_assist_phase_(waypoint_assist_phase),
        last_waypoint_assist_phase_(last_waypoint_assist_phase),
        reference_points_(reference_points),
        waypoint_assist_end_point_(waypoint_assist_end_point),
        waypoint_assist_arclength_(waypoint_assist_arclength),
        lane_sequence_result_for_waypoint_assist_(
            lane_sequence_result_for_waypoint_assist),
        has_arrived_at_waypoint_assist_path_end_(
            has_arrived_at_waypoint_assist_path_end),
        drawable_lane_sequence_(drawable_lane_sequence),
        is_waypoint_triggered_by_routing_(is_waypoint_triggered_by_routing),
        is_ego_in_oppsite_road_(is_ego_in_oppsite_road),
        is_road_entirely_blocked_(is_road_entirely_blocked),
        is_last_waypoint_in_opposite_lanes_(is_last_waypoint_in_opposite_lanes),
        is_turn_right_scene_(is_turn_right_scene),
        is_ego_in_waypoint_lane_sequence_(is_ego_in_waypoint_lane_sequence),
        has_ego_reached_target_lane_sequence_(
            has_ego_reached_target_lane_sequence) {}

  bool IsWaypointAssistInvoked() const {
    return waypoint_assist_phase_ != pb::AssistManeuverPhase::kIdle;
  }

  // If waypoint_assist_phase equals kIdle or kExitReasoning, it means ops has
  // exit waypoint assist, but in kExitReasoning phase, waypoint assist is still
  // working.
  bool HasOpsExitWaypointAssist() const {
    return waypoint_assist_phase_ == pb::AssistManeuverPhase::kIdle ||
           waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning;
  }

  pb::AssistManeuverPhase::Enum waypoint_assist_phase() const {
    return waypoint_assist_phase_;
  }

  pb::AssistManeuverPhase::Enum last_waypoint_assist_phase() const {
    return last_waypoint_assist_phase_;
  }

  const std::optional<voy::Point2d>& waypoint_assist_end_point() const {
    return waypoint_assist_end_point_;
  }

  double waypoint_assist_arclength() const {
    return waypoint_assist_arclength_;
  }

  const std::shared_ptr<const LaneSequenceResult>&
  lane_sequence_result_for_waypoint_assist() const {
    return lane_sequence_result_for_waypoint_assist_;
  }

  bool has_arrived_at_waypoint_assist_path_end() const {
    return has_arrived_at_waypoint_assist_path_end_;
  }

  const std::unordered_set<const pnc_map::Lane*>& drawable_lane_sequence()
      const {
    return drawable_lane_sequence_;
  }

  bool is_waypoint_triggered_by_routing() const {
    return is_waypoint_triggered_by_routing_;
  }

  bool is_ego_in_oppsite_road() const { return is_ego_in_oppsite_road_; }

  bool is_road_entirely_blocked() const { return is_road_entirely_blocked_; }

  bool is_last_waypoint_in_opposite_lanes() const {
    return is_last_waypoint_in_opposite_lanes_;
  }

  bool is_turn_right_scene() const { return is_turn_right_scene_; }

  bool is_ego_in_waypoint_lane_sequence() const {
    return is_ego_in_waypoint_lane_sequence_;
  }

  pb::EgoReachTargetLaneSequenceState::HasReached
  has_ego_reached_target_lane_sequence() const {
    return has_ego_reached_target_lane_sequence_;
  }

  const ::google::protobuf::RepeatedPtrField<::voy::Point2d>& reference_points()
      const {
    return reference_points_;
  }

 private:
  // The waypoint assist phase of current state.
  pb::AssistManeuverPhase::Enum waypoint_assist_phase_ =
      pb::AssistManeuverPhase::kIdle;

  // The waypoint assist phase of last state.
  pb::AssistManeuverPhase::Enum last_waypoint_assist_phase_ =
      pb::AssistManeuverPhase::kIdle;

  ::google::protobuf::RepeatedPtrField<::voy::Point2d> reference_points_;

  // The last waypoint given by ops.
  std::optional<voy::Point2d> waypoint_assist_end_point_;

  // Extend waypoint assit endpoint stopline length.
  double waypoint_assist_arclength_ = 0.0;

  // The lane sequence generated for end of lane sequence case from waypoint
  // assist end point.
  std::shared_ptr<const LaneSequenceResult>
      lane_sequence_result_for_waypoint_assist_;

  bool has_arrived_at_waypoint_assist_path_end_ = false;

  std::unordered_set<const pnc_map::Lane*> drawable_lane_sequence_;

  bool is_waypoint_triggered_by_routing_ = false;

  // Whether ego is in the opposite road.
  bool is_ego_in_oppsite_road_ = false;

  // Whether current road has been entirely blocked.
  bool is_road_entirely_blocked_ = false;

  // Whether the last waypoint is in the opposite lanes.
  bool is_last_waypoint_in_opposite_lanes_ = false;

  // Indicates if the current scene is turn right.
  bool is_turn_right_scene_ = false;

  bool is_ego_in_waypoint_lane_sequence_ = false;

  pb::EgoReachTargetLaneSequenceState::HasReached
      has_ego_reached_target_lane_sequence_ =
          pb::EgoReachTargetLaneSequenceState::NOT_DEFINED;
};

// Class WaypointAssistTracker tracks the waypoint assist state.
// More details about waypoint assist phase transition are shown in the graph:
// https://cooper.didichuxing.com/docs/document/*************
class WaypointAssistTracker
    : public PlannerInternalStateInterface<pb::WaypointAssistTrackerState> {
 public:
  void LoadState(const pb::WaypointAssistTrackerState& from_pb) final;

  void DumpState(pb::WaypointAssistTrackerState& to_pb) const final;

  // Updates waypoint assist state.
  void UpdateWaypointAssistState(
      const RobotState& robot_state, const RouteModel& route_model,
      const std::deque<std::unique_ptr<AssistTaskBase>>& assist_task_queue,
      const std::vector<pb::AssistResponse>& responses,
      const std::vector<pb::AssistResponse>& waypoint_responses_from_routing,
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const DrivableLaneReasoner& drivable_lane_reasoner,
      const GlobalRouteSolution& global_route_solution,
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const pb::DecoupledManeuverSeed& prev_maneuver_seed,
      const std::map<int64_t, std::vector<planner::RoadBlockageGroup>>&
          road_blockages,
      const bool has_left_assist_blockage_area,
      bool is_immediate_pull_over_triggered_by_planning,
      const std::optional<bool>& is_current_lanes_continuous,
      pb::WaypointAssistDebug* waypoint_assist_debug,
      pb::LaneSequenceGeneratorDebug* lane_sequence_generator_debug);

  void SetWaypointAssistPhase(pb::AssistManeuverPhase::Enum phase) {
    waypoint_assist_phase_ = phase;
  }

  void SetWaypointAssistEndPoint(const voy::Point2d& point) {
    waypoint_assist_end_point_ = point;
  }

  bool IsWaypointAssistInvoked() const {
    return waypoint_assist_phase_ != pb::AssistManeuverPhase::kIdle;
  }

  // If waypoint_assist_phase equals kIdle or kExitReasoning, it means ops has
  // exit waypoint assist, but in kExitReasoning phase, waypoint assist is still
  // working.
  bool HasOpsExitWaypointAssist() const {
    return waypoint_assist_phase_ == pb::AssistManeuverPhase::kIdle ||
           waypoint_assist_phase_ == pb::AssistManeuverPhase::kExitReasoning;
  }

  pb::AssistManeuverPhase::Enum waypoint_assist_phase() const {
    return waypoint_assist_phase_;
  }

  pb::AssistManeuverPhase::Enum last_waypoint_assist_phase() const {
    return last_waypoint_assist_phase_;
  }

  const std::optional<pb::WaypointBasedAssistCommand>&
  current_waypoint_assist_command() const {
    return current_waypoint_assist_command_;
  }

  const std::optional<voy::Point2d>& waypoint_assist_end_point() const {
    return waypoint_assist_end_point_;
  }

  double waypoint_assist_arclength() const {
    return waypoint_assist_arclength_;
  }

  const std::vector<math::geometry::Point2d>& trimmed_reference_point() const {
    return trimmed_reference_point_;
  }

  const std::shared_ptr<const LaneSequenceResult>
  lane_sequence_result_for_waypoint_assist() const {
    return lane_sequence_result_for_waypoint_assist_;
  }

  std::vector<const pnc_map::Lane*> lane_sequence_from_end_point() const {
    return lane_sequence_from_end_point_;
  }

  bool has_arrived_at_waypoint_assist_path_end() const {
    return has_arrived_at_waypoint_assist_path_end_;
  }

  const std::unordered_set<const pnc_map::Lane*>& drawable_lane_sequence()
      const {
    return drawable_lane_sequence_;
  }

  const std::optional<double>& hard_boundary_buffer_in_meter() const {
    return hard_boundary_buffer_in_meter_;
  }

  const std::optional<std::vector<const pnc_map::Lane*>>
  reference_point_located_lane() const {
    return reference_point_located_lane_;
  }

  pb::PathFailureType path_failure_type() const { return path_failure_type_; }

  bool is_waypoint_triggered_by_routing() const {
    return is_waypoint_triggered_by_routing_;
  }

  int64_t start_stuck_time_in_kExitReasoning_ms() const {
    return start_stuck_time_in_kExitReasoning_ms_;
  }

  const ::google::protobuf::RepeatedPtrField<::voy::Point2d>& reference_points()
      const {
    return reference_points_;
  }

  WaypointAssistTrackerSpeedData CreateWaypointAssistTrackerSpeedData() {
    return WaypointAssistTrackerSpeedData(
        has_arrived_at_waypoint_assist_path_end_,
        is_waypoint_triggered_by_routing_, is_ego_in_oppsite_road_,
        is_road_entirely_blocked_, is_last_waypoint_in_opposite_lanes_,
        is_turn_right_scene_, is_ego_in_waypoint_lane_sequence_,
        has_ego_reached_target_lane_sequence_, waypoint_assist_end_point_,
        waypoint_assist_arclength_, lane_sequence_result_for_waypoint_assist_,
        drawable_lane_sequence_, waypoint_assist_phase_,
        last_waypoint_assist_phase_, reference_points_);
  }

  bool is_ego_in_oppsite_road() const { return is_ego_in_oppsite_road_; }

  bool is_last_waypoint_in_opposite_lanes() const {
    return is_last_waypoint_in_opposite_lanes_;
  }

  bool is_road_entirely_blocked() const { return is_road_entirely_blocked_; }

  std::optional<bool> is_routing_unstuck_success() const {
    return is_routing_unstuck_success_;
  }

  bool is_turn_right_scene() const { return is_turn_right_scene_; }

  bool is_ego_in_waypoint_lane_sequence() const {
    return is_ego_in_waypoint_lane_sequence_;
  }

  pb::EgoReachTargetLaneSequenceState::HasReached
  has_ego_reached_target_lane_sequence() const {
    return has_ego_reached_target_lane_sequence_;
  }

 private:
  void UpdateDebug(
      const std::vector<pb::AssistResponse>& responses,
      pb::LaneSequenceGeneratorDebug* lane_sequence_generator_debug,
      pb::WaypointAssistDebug* waypoint_assist_debug);

  // Backtrack from the end point of the drawing point until finds the lane
  // where ego in or the lane of the same section. If neither is found, go back
  // up to 50 meters.
  double BackTrackFromEndPointLane(
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const math::geometry::Point2d ego_pos, const pnc_map::Lane* start_lane,
      const pnc_map::Lane* end_point_located_lane, int back_arclength,
      double& min_dist_to_prefix, std::vector<const pnc_map::Lane*>& result);

  bool IsReadyForIdlePhase(
      const std::optional<pb::WaypointBasedAssistCommand>& command,
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const RobotState& robot_state,
      const pb::DecoupledManeuverSeed& prev_maneuver_seed,
      const bool is_reverse_driving_active, const bool is_near_stationary,
      const bool has_left_assist_blockage_area,
      const std::optional<bool>& is_current_lanes_continuous) const;

  bool IsReadyForWaitingPointsPhase(
      const std::optional<pb::WaypointBasedAssistCommand>& command,
      bool is_near_stationary) const;

  bool IsReadyForPreparingPathPhase(
      const std::optional<pb::WaypointBasedAssistCommand>& command,
      bool is_near_stationary) const;

  bool IsReadyForWaitingConfirmPhase(bool is_near_stationary) const;

  bool IsReadyForFollowingPathPhase(
      const std::optional<pb::WaypointBasedAssistCommand>& command,
      bool is_near_stationary) const;

  bool IsReadyForExitReasoningPhase(
      const std::optional<pb::WaypointBasedAssistCommand>& command) const;

  std::vector<const pnc_map::Lane*> GetOppositeLeftMostLanes(
      const RobotState& robot_state,
      const std::vector<const pnc_map::Lane*>& lane_sequence);

  void UpdateDrawableLaneSequence(
      const RobotState& robot_state,
      const DrivableLaneReasoner& drivable_lane_reasoner,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const GlobalRouteSolution& global_route_solution,
      const RouteModel& route_model,
      const std::map<int64_t, std::vector<planner::RoadBlockageGroup>>&
          road_blockages,
      bool is_immediate_pull_over_triggered_by_planning);

  void UpdateCurrentLaneForWaypointAssist(const RobotState& robot_state);

  void UpdateIsEgoReachTargetLaneSequence(
      const math::geometry::OrientedBox2d& ego_bbox);

  bool ValidPointProjectionIncremental(
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const math::geometry::Point2d& front_bumper_position);

  std::vector<const pnc_map::Lane*> FindBestMatchLaneSequence(
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const math::geometry::Point2d ego_pos,
      std::vector<int64_t>& end_point_lane_ids);

  pb::PathFailureType DoPreparingPath(
      const RobotState& robot_state,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const GlobalRouteSolution& global_route_solution,
      const CurrentLaneAssociator& current_lane_associator,
      const CurrentLaneReasoner& current_lane_reasoner,
      const std::vector<const pnc_map::Lane*>& lane_sequence,
      const DrivableLaneReasoner& drivable_lane_reasoner,
      const ::google::protobuf::RepeatedPtrField<::voy::Point2d>&
          reference_points);

  // Resets waypoint assist phase as idle, which happens when receiving
  // tele-ops' exit waypoint assist mode command.
  void ResetToIdlePhase() {
    last_waypoint_assist_phase_ = waypoint_assist_phase_;
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kIdle;
    waypoint_assist_end_point_ = std::nullopt;
    has_arrived_at_waypoint_assist_path_end_ = false;
    lane_sequence_result_for_waypoint_assist_.reset();
    hard_boundary_buffer_in_meter_ = std::nullopt;
    reference_point_located_lane_ = std::nullopt;
    has_lane_sequence_proposal_responded_ = false;
    is_waypoint_triggered_by_routing_ = false;
    is_turn_right_scene_ = false;
    is_ego_in_waypoint_lane_sequence_ = false;
    start_stuck_time_in_kExitReasoning_ms_ = -1;
    lane_sequence_from_end_point_.clear();
    ResetDrawableLaneSequence();
    reference_points_.Clear();
  }

  void ResetToWaitingPointsPhase() {
    last_waypoint_assist_phase_ = waypoint_assist_phase_;
    waypoint_assist_phase_ = pb::AssistManeuverPhase::kWaitingPoints;
    waypoint_assist_end_point_ = std::nullopt;
    lane_sequence_result_for_waypoint_assist_.reset();
    hard_boundary_buffer_in_meter_ = std::nullopt;
    reference_point_located_lane_ = std::nullopt;
    start_stuck_time_in_kExitReasoning_ms_ = -1;
    lane_sequence_from_end_point_.clear();
    has_lane_sequence_proposal_responded_ = false;
    is_turn_right_scene_ = false;
    is_ego_in_waypoint_lane_sequence_ = false;
    path_failure_type_ = pb::PathFailureType::PATH_VALID;
    has_ego_reached_target_lane_sequence_ =
        pb::EgoReachTargetLaneSequenceState::NOT_DEFINED;
  }

  void ResetHasArrivedAtWaypointAssistPathEnd() {
    has_arrived_at_waypoint_assist_path_end_ = false;
  }

  void ResetDrawableLaneSequence() {
    drawable_lane_sequence_.clear();
    extended_drawable_lane_sequence_.clear();
  }

  void UpdateRoutingTriggerWaypointAssistUnstuckState();

  // The waypoint assist phase of current state.
  pb::AssistManeuverPhase::Enum waypoint_assist_phase_ =
      pb::AssistManeuverPhase::kIdle;

  // The waypoint assist phase of last state.
  pb::AssistManeuverPhase::Enum last_waypoint_assist_phase_ =
      pb::AssistManeuverPhase::kIdle;

  std::optional<pb::WaypointBasedAssistCommand>
      current_waypoint_assist_command_;

  // The last waypoint given by ops.
  std::optional<voy::Point2d> waypoint_assist_end_point_;

  // Extend waypoint assit endpoint stopline length.
  double waypoint_assist_arclength_ = 0.0;

  std::vector<math::geometry::Point2d> trimmed_reference_point_;

  ::google::protobuf::RepeatedPtrField<::voy::Point2d> reference_points_;

  // The lane sequence generated for end of lane sequence case from waypoint
  // assist end point.
  std::shared_ptr<LaneSequenceResult> lane_sequence_result_for_waypoint_assist_;

  // Indicates lane sequence generated by routing from the last point given by
  // ops.
  std::vector<const pnc_map::Lane*> lane_sequence_from_end_point_;

  bool has_arrived_at_waypoint_assist_path_end_ = false;

  std::unordered_set<const pnc_map::Lane*> drawable_lane_sequence_;

  std::unordered_set<const pnc_map::Lane*> extended_drawable_lane_sequence_;

  std::vector<const pnc_map::Lane*> opposite_lane_sequence_;

  // Indicates hard boundary buffer size for waypoint assist.
  std::optional<double> hard_boundary_buffer_in_meter_ = std::nullopt;

  // Indicates lanes where reference points located in.
  std::optional<std::vector<const pnc_map::Lane*>>
      reference_point_located_lane_ = std::nullopt;

  // Indicates path_failure_type when received waypoints.
  pb::PathFailureType path_failure_type_ = pb::PathFailureType::PATH_VALID;

  // Routing has accept the lane sequence proposal launched by waypoint assist.
  bool has_lane_sequence_proposal_responded_ = false;

  // If ego is in the opposite road.
  bool is_ego_in_oppsite_road_ = false;

  // If current road has been entirely blocked.
  bool is_road_entirely_blocked_ = false;

  // If th last waypoint is in the opposite lanes.
  bool is_last_waypoint_in_opposite_lanes_ = false;

  // Indicates waypoint assist is launched by routing node.
  bool is_waypoint_triggered_by_routing_ = false;

  int64_t start_stuck_time_in_kExitReasoning_ms_ = -1;

  std::optional<bool> is_routing_unstuck_success_ = std::nullopt;

  // Indicates if the current scene is turn right.
  bool is_turn_right_scene_ = false;

  // Indicates if ego is in waypoint lane sequence.
  bool is_ego_in_waypoint_lane_sequence_ = false;

  // Indicates whether ego has reach the target lane sequence when switch to
  // idle phase.
  pb::EgoReachTargetLaneSequenceState::HasReached
      has_ego_reached_target_lane_sequence_ =
          pb::EgoReachTargetLaneSequenceState::NOT_DEFINED;
};

}  // namespace planner

#endif  // ONBOARD_PLANNER_WORLD_MODEL_WAYPOINT_ASSIST_WAYPOINT_ASSIST_TRACKER_H_
