#include "order_service_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

OrderServiceObserver::OrderServiceObserver(OrderServiceCallback callback)
    : order_service_callback_(std::move(callback)) {}

bool OrderServiceObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kOrderService);
}

std::shared_ptr<google::protobuf::Message> OrderServiceObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto order_service = std::make_shared<order::pb::OrderService>();
  if (!order_service->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error OrderService message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (order_service_callback_) {
    order_service_callback_(order_service, data_channel);
  }
  return order_service;
}

}  // namespace remote_assist
