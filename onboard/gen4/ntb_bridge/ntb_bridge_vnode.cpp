#include <fcntl.h>
#include <glog/logging.h>
#include <memory>
#include <string>
#include <unistd.h>

#include "av_comm/thread_pool.h"
#include "common.h"
#include "ntb_receiver.h"
#include "onboard/varch/vnode/message_util.h"
#include "varch/vnode/detail/underlay_message/raw_message.h"
#include "varch/vnode/vnode.h"
#include "voy_protos/image.pb.h"
#include "voy_protos/video_frame.pb.h"

namespace ntb_bridge {
namespace vnode = varch::vnode;

using RawMessage = vnode::detail::underlay_message::RawMessage;

template <typename... MessageTypes>
class NtbBridgeVNode : public vnode::VNode<> {
 public:
  NtbBridgeVNode() = default;

  bool Init() override {
    // Create publishers
    topic_id_to_publisher_ = std::make_shared<PublisherMapType>();
    VNodeConfigPtr vnode_conf_ptr = this->GetConfig();
    CHECK(vnode_conf_ptr->pub_size() == sizeof...(MessageTypes));
    CreatePublishers<MessageTypes...>(vnode_conf_ptr);
    LOG(INFO) << "All publishers created, cnt: "
              << topic_id_to_publisher_->size();

    orin1_receiver_ =
        std::make_shared<NtbReceiver>(NTB_ORIN1, topic_id_to_publisher_);
    orin2_receiver_ =
        std::make_shared<NtbReceiver>(NTB_ORIN2, topic_id_to_publisher_);

    if (!orin1_receiver_->Start()) {
      LOG(ERROR) << "Failed to start ntb receiver for ORIN1";
      return false;
    }
    LOG(INFO) << "Receiver for ORIN1 has been started.";
    if (!orin2_receiver_->Start()) {
      LOG(ERROR) << "Failed to start ntb receiver for ORIN2";
      return false;
    }
    LOG(INFO) << "Receiver for ORIN2 has been started.";
    return true;
  }

  void ShutDown() override {
    orin1_receiver_->Stop();
    LOG(INFO) << "Receiver for ORIN1 has been stopped.";
    orin2_receiver_->Stop();
    LOG(INFO) << "Receiver for ORIN2 has been stopped.";
  }

  void Callback() override {}

 private:
  using PublisherMapType =
      std::unordered_map<int, varch::vnode::message_util::Publisher>;

  template <typename Message, typename... Messages>
  void CreatePublishers(const VNodeConfigPtr& cfg) {
    const int idx = sizeof...(MessageTypes) - sizeof...(Messages) - 1;
    const std::string topic = cfg->pub().at(idx).topic();
    int topic_id = TopicToNtbTopicID(topic);
    topic_id_to_publisher_->emplace(
        topic_id, vnode::message_util::CreatePublisher<RawMessage>(
                      topic, varch::utils::GetTypeName<Message>()));
    LOG(INFO) << "Publisher created, topic_id: " << topic_id
              << ", topic: " << topic;
    if constexpr (sizeof...(Messages) > 0) {
      CreatePublishers<Messages...>(cfg);
    }
  }

  std::shared_ptr<PublisherMapType> topic_id_to_publisher_;
  std::shared_ptr<NtbReceiver> orin1_receiver_;
  std::shared_ptr<NtbReceiver> orin2_receiver_;
};

using NtbBridge = NtbBridgeVNode<
    pb::Image /* av_comm::topic::kCameraRaw102 */,
    pb::Image /* av_comm::topic::kCameraRaw103 */,
    pb::Image /* av_comm::topic::kCameraRaw104 */,
    pb::Image /* av_comm::topic::kCameraRaw105 */,
    pb::Image /* av_comm::topic::kCameraRaw106 */,
    pb::Image /* av_comm::topic::kCameraRaw107 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo102 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo103 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo104 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo105 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo106 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo107 */,
    pb::Image /* av_comm::topic::kCameraRaw101 */,
    pb::Image /* av_comm::topic::kCameraGen4RawTrafficLight */,
    pb::Image /* av_comm::topic::kCameraGen4RawShort1 */,
    pb::Image /* av_comm::topic::kCameraGen4RawShort2 */,
    pb::Image /* av_comm::topic::kCameraGen4RawShort3 */,
    pb::Image /* av_comm::topic::kCameraGen4RawShort4 */,
    pb::Image /* av_comm::topic::kCameraGen4RawInfrared1 */,
    pb::VideoFrame /* av_comm::topic::kCameraVideo101 */,
    pb::VideoFrame /* av_comm::topic::kCameraGen4VideoTrafficLight */,
    pb::VideoFrame /* av_comm::topic::kCameraGen4VideoShort1 */,
    pb::VideoFrame /* av_comm::topic::kCameraGen4VideoShort2 */,
    pb::VideoFrame /* av_comm::topic::kCameraGen4VideoShort3 */,
    pb::VideoFrame /* av_comm::topic::kCameraGen4VideoShort4 */
    >;
REGISTER_VNODE(NtbBridge);

}  // namespace ntb_bridge
