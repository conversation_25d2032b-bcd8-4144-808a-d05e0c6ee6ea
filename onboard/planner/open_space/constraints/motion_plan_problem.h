#ifndef ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_MOTION_PLAN_PROBLEM_H_
#define ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_MOTION_PLAN_PROBLEM_H_

#include <unordered_map>
#include <utility>
#include <variant>
#include <vector>

#include "onboard/planner/open_space/constraints/boundary.h"
#include "onboard/planner/open_space/constraints/contour.h"
#include "onboard/planner/open_space/constraints/motion_effort_constraint.h"
#include "onboard/planner/open_space/constraints/motion_plan_roi.h"
#include "onboard/planner/open_space/constraints/start_pose_info.h"
#include "onboard/planner/open_space/constraints/target_pose_info.h"
#include "onboard/planner/open_space/constraints/way_point.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "vehicle_model/utils.h"

namespace planner {

namespace open_space {

// Struct MotionPlanProblem defines the set of constraint input data required
// for solving an open space motion plan from start to target pose.
struct MotionPlanProblem {
  explicit MotionPlanProblem(const RobotState& robot_state_in)
      : robot_state(robot_state_in) {
    ego_disk_radius = vehicle_model::GetDiskRadius(
        current_state_snapshot().car_model_with_shape().shape_measurement(),
        vehicle_model::kDefaultEgoDiskNumber);
  }

  // Ego robot state and params.
  const RobotState& robot_state;
  double ego_disk_radius = 0.0;

  // NOTE: A motion plan problem is valid ONLY IF the start and target poses are
  // NOT null.
  // The start pose of the motion plan, with all related infos and heuristics.
  std::optional<StartPoseInfo> start_pose_info;

  // The expected target poses for motion plan generation, each coupled with
  // optional acceptable tolerances of alignment.
  std::vector<TargetPoseInfo> target_pose_infos;

  // The motion plan ROI, based on which all constraints will be filtered and in
  // which the motion plan will be solved.
  MotionPlanROI roi;

  // The geometry occupancies that should be considered by motion plan
  // generation. The occupancies should either be a polyline-type or
  // polygon-type of constraints.
  std::unordered_map<OccupancyConstraintID, Boundary> boundaries;
  std::unordered_map<OccupancyConstraintID, Contour> contours;

  //
  // Guidances and controls on motion plan behaviors.
  //

  // All types of motion effort constraints limiting the motion plan maneuvers.
  MotionEffortConstraintInfo motion_effort_constraints;
  // Geometry guidances that the motion plan should attract to.
  WayPointSegment way_point_segment;

  // Returns true if the data in the motion plan problem is valid.
  bool IsValid() const;

  // Returns a pointer to either a Boundary or a Contour constraint, given a
  // queried constraint ID.
  std::variant<const Boundary*, const Contour*> GetOccupancyConstraintByID(
      const OccupancyConstraintID& constraint_id) const {
    if (constraint_id.type == OccupancyType::BOUNDARY) {
      return &(boundaries.at(constraint_id));
    }
    DCHECK(constraint_id.type == OccupancyType::CONTOUR)
        << "Unrecognized constraint type!";
    return &(contours.at(constraint_id));
  }

  // Const accessors for ego states and params.
  const RobotStateSnapshot& current_state_snapshot() const {
    return robot_state.current_state_snapshot();
  }
  const RobotStateSnapshot& plan_init_state_snapshot() const {
    return robot_state.plan_init_state_snapshot();
  }
  double ego_length() const { return robot_state.GetLength(); }
  double ego_width() const { return robot_state.GetWidth(); }
  const vehicle_model::CarModelWithAxleRectangularShape& car_model_with_shape()
      const {
    return robot_state.car_model_with_shape();
  }
  const vehicle_model::pb::AxleRectangularMeasurement& shape_measurement()
      const {
    return car_model_with_shape().shape_measurement();
  }
};

}  // namespace open_space

}  // namespace planner

#endif  // ONBOARD_PLANNER_OPEN_SPACE_CONSTRAINTS_MOTION_PLAN_PROBLEM_H_
