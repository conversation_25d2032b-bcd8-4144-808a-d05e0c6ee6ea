/**
 * @file serial_port.hpp
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-26
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __SERIAL_PORT_HPP__
#define __SERIAL_PORT_HPP__

#include <fcntl.h>
#include <sys/file.h>
#include <sys/select.h>
#include <termios.h>
#include <unistd.h>

#include <atomic>
#include <cstring>
#include <iostream>
#include <string>

namespace voyager {
namespace hardware {
struct SerialCfgSt {
  std::string serial_path;
  int baud_rate;
  int parity;
  uint8_t vmin;
  uint8_t vtime;
  bool open_flag;
};

class SerialPort {
 public:
  SerialPort(const std::string path, int baud_rate = 9600, int parity = 0,
             uint8_t vmin = 0, uint8_t vtime = 0, bool open_flag = false)
      : serial_path_(path),
        open_flag_(open_flag),
        baud_rate_(baud_rate),
        parity_(parity),
        vmin_(vmin),
        vtime_(vtime) {}
  SerialPort(const SerialCfgSt &cfg)
      : serial_path_(cfg.serial_path),
        open_flag_(cfg.open_flag),
        baud_rate_(cfg.baud_rate),
        parity_(cfg.parity),
        vmin_(cfg.vmin),
        vtime_(cfg.vtime) {}

  SerialPort(const SerialPort &) = delete;
  SerialPort(SerialPort &&) = delete;
  virtual ~SerialPort() { close_serial(); }

 public:
  bool open_serial() {
    bool expected = false;
    if (!open_flag_.compare_exchange_strong(expected, true)) return true;
    serial_fd_ = open(serial_path_.c_str(), O_RDWR | O_NOCTTY);
    if (serial_fd_ == -1) {
      open_flag_.store(false);
      err_msg_ = serial_path_ + std::string(" open failure.");
      return false;
    }
    flock(serial_fd_, LOCK_EX | LOCK_NB);

    struct termios options;
    memset(&options, 0, sizeof(options));
    if (tcgetattr(serial_fd_, &options) != 0) {
      err_msg_ = serial_path_ + std::string(" tcgetattr failure.");
      close_serial();
      return false;
    }

    speed_t speed;
    switch (baud_rate_) {
      case 9600:
        speed = B9600;
        break;
      case 19200:
        speed = B19200;
        break;
      case 38400:
        speed = B38400;
        break;
      case 57600:
        speed = B57600;
        break;
      case 115200:
        speed = B115200;
        break;
      case 230400:
        speed = B230400;
        break;
      default:
        close(serial_fd_);
        return false;
    }
    cfsetispeed(&options, speed);
    cfsetospeed(&options, speed);

    if (parity_ == 0) {  // 无校验
      options.c_cflag &= ~PARENB;
    } else if (parity_ == 1) {  // 奇校验
      options.c_cflag |= PARENB;
      options.c_cflag |= PARODD;
    } else {  // 偶校验
      options.c_cflag |= PARENB;
      options.c_cflag &= ~PARODD;
    }

    // Set data bits and stop bits
    options.c_cflag &= ~CSIZE;
    options.c_cflag |= CS8;
    options.c_cflag &= ~CSTOPB;

    options.c_iflag &= ~IGNBRK;
    options.c_iflag &= ~(IXON | IXOFF | IXANY);
    options.c_lflag = 0;
    options.c_oflag = 0;
    options.c_cflag |= (CLOCAL | CREAD);
    options.c_cflag &= ~CRTSCTS;
    options.c_cc[VMIN] = vmin_;
    options.c_cc[VTIME] = vtime_;

    if (tcsetattr(serial_fd_, TCSANOW, &options) != 0) {
      err_msg_ = serial_path_ + std::string(" tcsetattr failure.");
      close_serial();
      return false;
    }

    return true;
  }

  bool is_open() { return open_flag_.load(); }

  void close_serial() {
    if (serial_fd_ != -1) {
      close(serial_fd_);
      serial_fd_ = -1;
      open_flag_.store(false);
      flock(serial_fd_, LOCK_UN);
    }
  }

  ssize_t write_serial(const char *data, size_t len) {
    if (serial_fd_ == -1) {
      err_msg_ = serial_path_ + std::string(" write failure, fd=-1.");
      return -1;
    }

    size_t total_written = 0;
    while (total_written < len) {
      ssize_t n =
          ::write(serial_fd_, data + total_written, len - total_written);
      if (n < 0) {
        err_msg_ = serial_path_ + std::string(" write failure.");
        return -1;
      }
      total_written += n;
    }

    return len;
  }

  ssize_t write_serial(const std::string &str) {
    return write_serial(str.c_str(), str.size());
  }

  ssize_t read_serial(char *data, size_t len) {
    if (serial_fd_ == -1) {
      err_msg_ = serial_path_ + std::string(" read failure, fd=-1.");
      return -1;
    }
    return read(serial_fd_, data, len);
  }

  ssize_t read_serial(std::string &str, size_t len) {
    if (serial_fd_ == -1) {
      err_msg_ = serial_path_ + std::string(" read failure, fd=-1.");
      return -1;
    }

    char data[len];
    ssize_t nread = read(serial_fd_, data, len);
    if (nread > 0) {
      str = std::string(data, nread);
    }
    return nread;
  }

  ssize_t read_serial_timeout(char *data, size_t len, int timeout_ms) {
    if (serial_fd_ == -1) {
      err_msg_ = serial_path_ + std::string(" read failure, fd=-1.");
      return -1;
    }
    if (vtime_ != 0) {
      err_msg_ = serial_path_ + std::string(" read failure, vtime != 0.");
      return -1;
    }

    fd_set read_set;
    FD_ZERO(&read_set);
    FD_SET(serial_fd_, &read_set);

    timeval timeout;
    timeout.tv_sec = timeout_ms / 1000;
    timeout.tv_usec = (timeout_ms % 1000) * 1000;

    int result = select(serial_fd_ + 1, &read_set, nullptr, nullptr,
                        timeout_ms >= 0 ? &timeout : nullptr);
    if (result == 0)
      return 0;
    else if (result < 0)
      return -1;
    if (FD_ISSET(serial_fd_, &read_set)) {
      return read(serial_fd_, data, len);
    }

    return -1;
  }

  int get_fd() { return serial_fd_; }

  std::string &get_err() { return err_msg_; }

 private:
  std::string serial_path_;
  std::atomic<bool> open_flag_;
  int serial_fd_ = -1;
  int baud_rate_;
  int parity_;  // 0-none; 1-odd; 2-even
  uint8_t vmin_;
  uint8_t vtime_;  // Unit 0.1 seconds
  std::string err_msg_;
};
}  // namespace hardware
}  // namespace voyager

#endif
