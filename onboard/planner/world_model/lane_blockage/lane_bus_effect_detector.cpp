#include "planner/world_model/lane_blockage/lane_bus_effect_detector.h"

#include <algorithm>
#include <optional>
#include <utility>

#include "planner/behavior/util/agent_state/agent_in_lane_param_generation.h"
#include "pnc_map_service/util/pnc_map_service_utility.h"
#include "rt_event/rt_event.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace {
// Max forward search distance to group bus bulb with bus.
constexpr double kMaxDistanceToGroupBusForwardlyInMeter = 200.0;
// Max backward search distance to group bus bulb with bus.
constexpr double kMaxDistanceToGroupBusBackwardlyInMeter = 100.0;
// Max distance from bus to the bus bulb that the bus need to reduce speed.
constexpr double kMaxDistanceForBusToReduceSpeedInMeter = 50.0;
// Speed limit for bus when bus is near the bus bulb, if bus speed is higher
// than the limit, we will cosider it as a bus that may leave the bus bulb.
constexpr double kMaxSpeedForBusNearBusBulbInMeterPerSec = 12.5;
// Minimum distance from bus bulb to destination that may trigger bus avoidance.
constexpr double kMinDistanceFromBulbToDestinationInMeter = 50.0;
// Max speed of a bus that can be considered as a bus with potential interaction
// with a bus bulb.
constexpr double kMaxBusSpeedForPotentialCheckInMps = 15.27;

// Returns distance from pose at |target_section_arclength_m| on
// |target_section| to destination. if |target_section| is not contained in
// |extended_regional_section_sequence|, returns nullopt.
std::optional<double> GetDistFromTargetToDestination(
    const std::vector<const pnc_map::Section*>&
        extended_regional_section_sequence,
    double dist_from_pose_to_destination_m,
    const math::geometry::Point2d& ego_pose,
    const pnc_map::Section* target_section, double target_section_arclength_m) {
  if (extended_regional_section_sequence.empty()) {
    return std::nullopt;
  }
  double dist_from_ego_pose_to_target_pose = 0.00;
  double start_arclength_m =
      extended_regional_section_sequence.front()->GetArclength(ego_pose);
  for (const pnc_map::Section* section : extended_regional_section_sequence) {
    // Calculate the distance from |ego_pose| to |target_section_arclength_m| on
    // |target_section| by adding the length of |section| to
    // |dist_from_ego_pose_to_target_pose|.
    if (target_section->id() == section->id()) {
      dist_from_ego_pose_to_target_pose +=
          (target_section_arclength_m - start_arclength_m);
      return std::make_optional<double>(dist_from_pose_to_destination_m -
                                        dist_from_ego_pose_to_target_pose);
    }
    dist_from_ego_pose_to_target_pose +=
        section->GetLength() - start_arclength_m;
    start_arclength_m = 0.00;
  }
  // If not found, return nullopt.
  return std::nullopt;
}

// Returns true if the bus bulb in |bus_search_info| is close to destination.
bool CheckIsBusBulbCloseToDestination(
    const BusBulbSearchInfo& bus_search_info,
    const pnc_map::Lane* bus_bulb_lane, const voy::Pose& pose,
    const std::vector<const pnc_map::Section*>&
        extended_regional_section_sequence,
    double dist_from_pose_to_destination_m) {
  const math::geometry::Point2d ego_pose(pose.x(), pose.y());
  const pnc_map::Section* target_section = bus_bulb_lane->section();
  const auto dist_from_ego_pose_to_target_pose_opt =
      GetDistFromTargetToDestination(extended_regional_section_sequence,
                                     dist_from_pose_to_destination_m, ego_pose,
                                     target_section,
                                     bus_search_info.zone_lane_relation.value()
                                             .relation_range.range.end_pos *
                                         bus_bulb_lane->length());
  return dist_from_ego_pose_to_target_pose_opt.has_value() &&
         dist_from_ego_pose_to_target_pose_opt.value() <=
             kMinDistanceFromBulbToDestinationInMeter;
}

// Checks if the bus |object| in |bus_range| will leave the bus bulb
// |bus_bulb_search_lane_segment| instead of stopped using the info about the
// bus bulb |bus_zone_info|.
bool CheckIfBusReadyToLeaveBusBulb(
    const common::LaneSegment& bus_bulb_search_lane_segment,
    const BusBulbSearchInfo& bus_zone_info, const PlannerObject& object,
    const common::LaneSegment& bus_range) {
  if (!bus_zone_info.zone_lane_relation.has_value()) {
    return false;
  }
  const double range_start =
      bus_zone_info.zone_lane_relation.value().relation_range.range.start_pos *
      bus_range.lane->length();
  if (bus_bulb_search_lane_segment.lane->id() == bus_range.lane->id() &&
      (bus_range.arc_length_range_m.end_pos() > range_start) &&
      object.is_stm_object()) {
    // If bus is in bus bulb and is stm, do not consider it as a bus will leave
    // bus bulb.
    return true;
  }

  if ((bus_zone_info.search_distance <=
       kMaxDistanceForBusToReduceSpeedInMeter) &&
      (object.speed() >= kMaxSpeedForBusNearBusBulbInMeterPerSec)) {
    // If bus is near the bus bulb and speed is large, we do not consider it as
    // a bus that may stop in the bus bulb.
    return true;
  }
  return false;
}

// Merges the ranges in |potential_effects| with the type
// |potential_effect_type|.
void MergePotentialEffects(
    PotentialEffectType potential_effect_type,
    std::vector<LanePotentialEffect>& potential_effects) {
  if (potential_effects.empty()) {
    return;
  }
  std::vector<math::Range1d> arc_length_ranges;
  arc_length_ranges.reserve(potential_effects.size());
  const pnc_map::Lane* curr_lane = nullptr;
  for (auto iter = potential_effects.begin(); iter != potential_effects.end();
       iter++) {
    const auto& potential_effect = *iter;
    if (potential_effect.potential_effect_type != potential_effect_type) {
      continue;
    }
    if (potential_effect.lane == nullptr ||
        !common::IsValidArcLengthRange(
            potential_effect.arc_length_range_m.start_pos(),
            potential_effect.arc_length_range_m.end_pos())) {
      potential_effects.erase(iter);
      iter--;
      continue;
    }
    if (curr_lane == nullptr) {
      curr_lane = potential_effect.lane;
    }
    arc_length_ranges.emplace_back(
        potential_effect.arc_length_range_m.start_pos(),
        potential_effect.arc_length_range_m.end_pos());
    potential_effects.erase(iter);
    iter--;
  }
  if (curr_lane == nullptr || arc_length_ranges.empty()) {
    return;
  }
  const auto merged_ranges = math::MergeRange(arc_length_ranges);
  if (merged_ranges.empty()) {
    return;
  }
  for (const auto& merged_range : merged_ranges) {
    potential_effects.emplace_back(curr_lane, merged_range.start_pos,
                                   merged_range.end_pos, potential_effect_type);
  }
}

// Finds the lane where |point| locates in |lane_list|.
// |lane_list| is the lane candidates, |reference_lane_ids| is the lane ids
// which are preferred when |point| locates in more than one lane.
const pnc_map::Lane* GetPointAssociatedLane(
    const std::vector<const pnc_map::Lane*>& lane_list,
    const math::geometry::Point2d& point,
    const std::vector<int64_t>& reference_lane_ids) {
  std::vector<const pnc_map::Lane*> point_associated_lanes;
  for (const auto& lane_ptr : lane_list) {
    if (math::geometry::CoveredBy(point, lane_ptr->border())) {
      point_associated_lanes.push_back(lane_ptr);
    }
  }

  if (point_associated_lanes.empty()) {
    return nullptr;
  } else if (point_associated_lanes.size() == 1) {
    return point_associated_lanes.back();
  } else {
    // If point locates in multiple lanes, use |reference_lane_ids| as
    // referrence.
    for (const auto& lane_ptr : point_associated_lanes) {
      if (std::find(reference_lane_ids.begin(), reference_lane_ids.end(),
                    lane_ptr->id()) != reference_lane_ids.end()) {
        return lane_ptr;
      }
    }
  }
  return point_associated_lanes.back();
}

// Converts the predicted trajectory of object from |PredictedTrajectory| to the
// list of |LaneSegment|.
std::vector<common::LaneSegment> ConvertTrajectoryToLaneSegments(
    const prediction::pb::PredictedTrajectory& trajectory,
    const std::vector<int64_t>& reference_lane_ids,
    const pnc_map::PncMapService& pnc_map_service) {
  std::vector<const pnc_map::Lane*> lane_list;
  // |lane_list| stores the all lanes in the sections extended by lanes in
  // |reference_lane_ids|.
  lane_list.reserve(3 * reference_lane_ids.size());
  for (const auto lane_id : reference_lane_ids) {
    // Use the |reference_lane_ids| to create |lane_list| as candidates.
    const auto* lane_ptr = pnc_map_service.GetLaneById(lane_id);
    if (lane_ptr != nullptr && std::find(lane_list.begin(), lane_list.end(),
                                         lane_ptr) == lane_list.end()) {
      // Do not add duplicate lanes.
      const auto* lane_id_associated_section = lane_ptr->section();
      lane_list.insert(lane_list.end(),
                       lane_id_associated_section->lanes().begin(),
                       lane_id_associated_section->lanes().end());
    }
  }
  const auto& point_start = trajectory.traj_poses().begin();
  math::geometry::Point2d segment_start_point(point_start->x_pos(),
                                              point_start->y_pos());
  const pnc_map::Lane* cur_lane_ptr = GetPointAssociatedLane(
      lane_list, segment_start_point, reference_lane_ids);
  std::vector<common::LaneSegment> lane_segments;
  if (cur_lane_ptr == nullptr) {
    // It means the lane sequence and trajectory of object don't match.
    LOG(ERROR) << "[LaneBusEffectDetector] Can not found trajectory point "
                  "assoicated lane";
    return lane_segments;
  }

  lane_segments.reserve(reference_lane_ids.size());
  for (int idx = 1; idx < trajectory.traj_poses_size(); idx++) {
    const auto& traj_pose = trajectory.traj_poses().at(idx);
    const math::geometry::Point2d point(traj_pose.x_pos(), traj_pose.y_pos());
    const auto* search_lane_ptr =
        GetPointAssociatedLane(lane_list, point, reference_lane_ids);
    if (search_lane_ptr == nullptr) {
      // It means the lane sequence and trajectory of object don't match.
      LOG(ERROR) << "[LaneBusEffectDetector] Can not found trajectory point "
                    "assoicated lane";
      return lane_segments;
    }
    if (cur_lane_ptr->id() == search_lane_ptr->id() &&
        idx != trajectory.traj_poses_size() - 1) {
      continue;
    }
    const double start_pos = cur_lane_ptr->GetArclength(segment_start_point);
    const double end_pos = cur_lane_ptr->GetArclength(point);
    if (!common::IsValidArcLengthRange(start_pos, end_pos)) {
      // If prediction trajectory has reversed, we do not use it now.
      return {};
    }
    lane_segments.emplace_back(cur_lane_ptr, start_pos, end_pos);
    segment_start_point = point;
    cur_lane_ptr = search_lane_ptr;
  }
  return lane_segments;
}

// Selects the lane where cost need to be added.
std::vector<const pnc_map::Lane*> SelectEffectLanes(
    const std::vector<const pnc_map::Lane*>& lanes) {
  std::vector<const pnc_map::Lane*> selected_lanes;
  const pnc_map::Lane* prev_lane = lanes.front();
  selected_lanes.reserve(lanes.size());
  for (const auto& lane : lanes) {
    // Exclude the lane that are not straightly connected to the bus bulb;
    if (lane->IsForkLane() || lane->IsInJunction()) {
      // Add cost to fork or lane in junction to avoid ego to enter.
      selected_lanes.push_back(lane);
      break;
    }
    if (lane->IsMergeLane() || lane->IsNeighbor(*prev_lane)) {
      break;
    }
    prev_lane = lane;
    selected_lanes.push_back(lane);
  }
  return selected_lanes;
}

// Chooses the next lane segment to search when forward searching bus bulb.
std::optional<common::LaneSegment> ChooseForwardSearchNextSegment(
    common::LaneSegment& cur_lane_segment,
    std::vector<common::LaneSegment>& pred_traj_lane_segments) {
  // Use pred trajectory |pred_traj_lane_segments| to choose next lane segment.
  bool use_next_pred_lane_segment = false;
  for (const auto& pred_lane_segment : pred_traj_lane_segments) {
    if (use_next_pred_lane_segment == true) {
      return std::make_optional<common::LaneSegment>(pred_lane_segment);
    }
    // If current lane has not been searched fully, continue searching this
    // lane.
    if (cur_lane_segment.lane->id() == pred_lane_segment.lane->id()) {
      if (cur_lane_segment.arc_length_range_m.end_pos() <
          pred_lane_segment.arc_length_range_m.end_pos() - 1e-3) {
        return std::make_optional<common::LaneSegment>(
            cur_lane_segment.lane,
            cur_lane_segment.arc_length_range_m.end_pos(),
            pred_lane_segment.arc_length_range_m.end_pos());
      }
      // If |cur_lane_segment| exceeds |pred_traj_lane_segments|, do not use
      // prediction again.
      if (cur_lane_segment.arc_length_range_m.end_pos() >
          pred_lane_segment.arc_length_range_m.end_pos() + 1e-3) {
        break;
      }
      use_next_pred_lane_segment = true;
    }
  }
  // Prediction trajectory is useless.
  // If current lane has not been searched fully, continue searching this lane.
  if (cur_lane_segment.arc_length_range_m.end_pos() <
      cur_lane_segment.lane->length() - 1e-3) {
    return std::make_optional<common::LaneSegment>(
        cur_lane_segment.lane, cur_lane_segment.arc_length_range_m.end_pos(),
        cur_lane_segment.lane->length());
  }
  // If current lane has been searched fully, choose its straight right most
  // successor.
  const std::vector<const pnc_map::Lane*>& successors =
      cur_lane_segment.lane->successors();
  if (successors.empty()) {
    return std::nullopt;
  }
  // Always use the rightmost lane as successor.
  return std::make_optional<common::LaneSegment>(successors.back(), 0.00,
                                                 successors.back()->length());
}

// Chooses the next lane segment to search when backward searching bus bulb
// affected segements.
std::optional<common::LaneSegment> ChooseBackWardsSearchNextSegment(
    common::LaneSegment& cur_lane_segment) {
  // If current lane has not been searched fully, continue searching this lane.
  if (cur_lane_segment.arc_length_range_m.start_pos() > 0) {
    return std::make_optional<common::LaneSegment>(
        cur_lane_segment.lane, 0.00,
        cur_lane_segment.arc_length_range_m.start_pos());
  }
  // If current lane has been searched fully, choose its straight right most
  // predecessors.
  const std::vector<const pnc_map::Lane*>& predecessors =
      cur_lane_segment.lane->predecessors();
  if (predecessors.empty()) {
    return std::nullopt;
  }
  // Always use the rightmost lane as predecessor.
  return std::make_optional<common::LaneSegment>(predecessors.back(), 0.00,
                                                 predecessors.back()->length());
}

void AddEffectToBusEffectPotentialEffectsMap(
    int64_t lane_id, const LanePotentialEffect& potential_effect,
    std::map<int64_t, std::vector<LanePotentialEffect>>*
        bus_potential_effects_map) {
  const auto iter = bus_potential_effects_map->find(lane_id);
  if (iter == bus_potential_effects_map->end()) {
    bus_potential_effects_map->emplace(
        lane_id, std::vector<LanePotentialEffect>({potential_effect}));
  } else {
    iter->second.push_back(potential_effect);
  }
}

// Gets farthest bus zone on |search_lane_segemnt|,
// |searched_distance| is updated as well.
BusBulbSearchInfo GetFarthestBusZoneOnLane(
    const hdmap::ZoneLaneOccupancyMap& zone_lane_occupancy_map,
    common::LaneSegment& search_lane_segemnt, double searched_distance) {
  // Remaining length of current lane, since lane may not start from start.
  const pnc_map::Lane* search_lane = search_lane_segemnt.lane;
  const double remaining_lane_length =
      std::max(search_lane_segemnt.arc_length_range_m.end_pos() -
                   search_lane_segemnt.arc_length_range_m.start_pos(),
               0.0);

  // If lane does not associated with zones, accumulate searched distance by
  // lane's remaining distance, and return null zone lane relation.
  const hdmap::ZoneLaneAssociation* bus_lane_zone_lane_association =
      zone_lane_occupancy_map.GetZoneLaneAssociationByLaneId(search_lane->id());
  if (bus_lane_zone_lane_association == nullptr) {
    searched_distance += remaining_lane_length;
    return {std::nullopt, searched_distance};
  }

  // If lane does associated with zone but not bus bulb, we accumulate
  // searched distance by lane's remaining distance, and return null zone lane
  // relation.
  auto bus_bulbs_it =
      bus_lane_zone_lane_association->zone_lane_occupancy_list_map().find(
          voy::ZoneType::kDirectBusBulb);
  if (bus_bulbs_it ==
      bus_lane_zone_lane_association->zone_lane_occupancy_list_map().end()) {
    bus_bulbs_it =
        bus_lane_zone_lane_association->zone_lane_occupancy_list_map().find(
            voy::ZoneType::kBayStyleBusBulb);
    if (bus_bulbs_it ==
        bus_lane_zone_lane_association->zone_lane_occupancy_list_map().end()) {
      searched_distance += remaining_lane_length;
      return {std::nullopt, searched_distance};
    }
  }

  // Farthest zone's end arc length.
  double max_end_arc_length =
      search_lane_segemnt.arc_length_range_m.start_pos();
  // Farthest zone's index.
  int farthest_zone_idx = -1;
  for (unsigned idx = 0; idx < bus_bulbs_it->second.relation_list.size();
       ++idx) {
    // Sometimes ZoneLaneOccupancy associates bus bulb which may over size with
    // lane which is not right most, now we ignore these relations by judging
    // its lane and it clearance.
    if ((!search_lane->IsRightmostVehicleLane()) ||
        (bus_bulbs_it->second.relation_list.at(idx).is_passable)) {
      LOG(ERROR) << "Found a bus bulb not on right most lane and lane id is "
                 << search_lane->id();
      continue;
    }
    const double zone_start_arc_length =
        bus_bulbs_it->second.relation_list.at(idx)
            .relation_range.range.start_pos *
        search_lane->length();
    const double zone_end_arc_length =
        bus_bulbs_it->second.relation_list.at(idx)
            .relation_range.range.end_pos *
        search_lane->length();
    // Ignore bus zone whose range has no overlap with search range or search
    // distance larger than 200m. For further bus zone, we do not want to group
    // together.
    if (!common::GetOverlappedRange(
            {zone_start_arc_length, zone_end_arc_length},
            common::ConvertPbToRange(search_lane_segemnt.arc_length_range_m)) ||
        (zone_end_arc_length -
         search_lane_segemnt.arc_length_range_m.start_pos()) >=
            (kMaxDistanceToGroupBusForwardlyInMeter - searched_distance)) {
      continue;
    }

    max_end_arc_length = std::max(max_end_arc_length, zone_end_arc_length);
    farthest_zone_idx = idx;
  }

  // If cannot find zone within 200m, just return nullopt.
  if (farthest_zone_idx == -1) {
    searched_distance += remaining_lane_length;
    return {std::nullopt, searched_distance};
  }

  // If find valid zone, use its end arc length to update searched distance, and
  // return this zone's zone lane info.
  searched_distance += std::max(
      max_end_arc_length - search_lane_segemnt.arc_length_range_m.start_pos(),
      0.0);
  return {bus_bulbs_it->second.relation_list.at(farthest_zone_idx),
          searched_distance};
}

// Updates |bus_range|'s potential effect and update to
// bus_potential_effects_map. Returns the LaneSequenceBusPotentialEffects if
// current |bus_range| is near a bus bulb forwardly, otherwise returns nullopt.
std::optional<LaneSequenceBusPotentialEffects> CheckAndUpdateBusPotentialEffecs(
    const PlannerObject& object, const common::LaneSegment& bus_range,
    const pnc_map::PncMapService& pnc_map_service,
    const hdmap::ZoneLaneOccupancyMap& zone_lane_occupancy_map,
    const voy::Pose& pose,
    const std::vector<const pnc_map::Section*>&
        extended_regional_section_sequence,
    double dist_from_pose_to_destination_m) {
  std::map<int64_t, std::vector<LanePotentialEffect>> bus_potential_effects_map;
  std::vector<const pnc_map::Lane*> bus_effects_lane_sequences;
  std::vector<LanePotentialEffect> temp_bus_potential_effects;

  // Step 1. Forward search from bus, find its forward bus zone, consider as a
  // group and add relative lane effects to bus_potential_effects_map.
  bool found_bus_bulb_forwardly = false;
  common::LaneSegment curr_lane_segment(
      bus_range.lane, bus_range.arc_length_range_m.start_pos(),
      bus_range.lane->GetArclength(object.center_2d()));
  double searched_distance = 0.0;

  // Get predicted trajectory info from object.
  std::vector<common::LaneSegment> pred_lane_segments;
  if (object.output_trajectory().has_value() &&
      object.predicted_lane_sequences().has_value()) {
    pred_lane_segments = ConvertTrajectoryToLaneSegments(
        object.output_trajectory().value(),
        object.predicted_lane_sequences().value(), pnc_map_service);
  }

  while (searched_distance < kMaxDistanceToGroupBusForwardlyInMeter) {
    // Get |curr_lane|'s associated bus zone, and update |searched_distance|.
    const auto curr_lane_segment_opt =
        ChooseForwardSearchNextSegment(curr_lane_segment, pred_lane_segments);
    if (!curr_lane_segment_opt.has_value()) {
      break;
    }
    curr_lane_segment = curr_lane_segment_opt.value();
    const pnc_map::Lane* curr_lane = curr_lane_segment.lane;
    // Exclude uturn and junctions that is not straight.
    if (curr_lane->turn() == hdmap::Lane::U_TURN ||
        (curr_lane->IsInJunction() &&
         curr_lane->turn() != hdmap::Lane::STRAIGHT)) {
      break;
    }
    bus_effects_lane_sequences.insert(bus_effects_lane_sequences.begin(),
                                      curr_lane_segment.lane);
    const BusBulbSearchInfo& farthest_bus_zone = GetFarthestBusZoneOnLane(
        zone_lane_occupancy_map, curr_lane_segment, searched_distance);
    searched_distance = farthest_bus_zone.search_distance;

    // Once found a lane with bus bulb, we will terminate search.
    if (farthest_bus_zone.zone_lane_relation.has_value()) {
      const double range_end = farthest_bus_zone.zone_lane_relation.value()
                                   .relation_range.range.end_pos *
                               curr_lane_segment.lane->length();
      if (range_end > curr_lane_segment.arc_length_range_m.start_pos()) {
        if (!CheckIfBusReadyToLeaveBusBulb(curr_lane_segment, farthest_bus_zone,
                                           object, bus_range) &&
            !CheckIsBusBulbCloseToDestination(
                farthest_bus_zone, curr_lane_segment.lane, pose,
                extended_regional_section_sequence,
                dist_from_pose_to_destination_m)) {
          // If bus is in bus bulb and is stm, do not consider it as a bus that
          // may has potential effects. If bus bulb is close to destination, we
          // do not consider its potential effects to prevent the failure of
          // parking by the roadside.
          found_bus_bulb_forwardly = true;
          temp_bus_potential_effects.emplace_back(
              curr_lane_segment.lane,
              curr_lane_segment.arc_length_range_m.start_pos(), range_end,
              PotentialEffectType::kBusMayEffect);
        }
        break;
      }
    }

    temp_bus_potential_effects.emplace_back(
        curr_lane_segment.lane,
        curr_lane_segment.arc_length_range_m.start_pos(),
        std::max(curr_lane_segment.arc_length_range_m.start_pos() +
                     math::constants::kEpsilon,
                 curr_lane_segment.arc_length_range_m.end_pos()),
        PotentialEffectType::kBusMayEffect);
  }

  // If we cannot find a bus bulb by forward search, we will not push relative
  // effect to map.
  if (!found_bus_bulb_forwardly) {
    return std::nullopt;
  }

  // If do find a bus bulb, insert effects to map and continue backward search.
  for (const LanePotentialEffect& effect : temp_bus_potential_effects) {
    AddEffectToBusEffectPotentialEffectsMap(effect.lane->id(), effect,
                                            &bus_potential_effects_map);
  }

  // Step 2. Backward search, create a effect with max 100m or to lane start,
  // and add to bus_potential_effects_map.
  double backward_search_distance = 0;
  // Include the bus range in backward range.
  common::LaneSegment backward_lane_segemnt(
      bus_range.lane, bus_range.arc_length_range_m.end_pos(),
      bus_range.arc_length_range_m.end_pos());
  while (backward_search_distance < kMaxDistanceToGroupBusBackwardlyInMeter) {
    const auto backward_lane_segemnt_opt =
        ChooseBackWardsSearchNextSegment(backward_lane_segemnt);
    if (!backward_lane_segemnt_opt.has_value()) {
      break;
    }
    backward_lane_segemnt = backward_lane_segemnt_opt.value();
    // Exclude uturn and junctions that is not straight.
    const pnc_map::Lane* backward_lane = backward_lane_segemnt.lane;
    if (backward_lane->turn() == hdmap::Lane::U_TURN ||
        (backward_lane->IsInJunction() &&
         backward_lane->turn() != hdmap::Lane::STRAIGHT)) {
      break;
    }
    bus_effects_lane_sequences.push_back(backward_lane_segemnt.lane);
    const LanePotentialEffect backward_predecessor_effect = {
        backward_lane_segemnt.lane,
        std::max(backward_lane_segemnt.arc_length_range_m.end_pos() -
                     (kMaxDistanceToGroupBusBackwardlyInMeter -
                      backward_search_distance),
                 0.0),
        backward_lane_segemnt.arc_length_range_m.end_pos(),
        PotentialEffectType::kBusMayEffect};
    AddEffectToBusEffectPotentialEffectsMap(backward_lane_segemnt.lane->id(),
                                            backward_predecessor_effect,
                                            &bus_potential_effects_map);
    backward_search_distance +=
        (backward_lane_segemnt.arc_length_range_m.end_pos() -
         backward_lane_segemnt.arc_length_range_m.start_pos());
  }

  // Post RT event for monitoring.
  rt_event::PostRtEvent<rt_event::planner::BlockBusHasPotentialEffects>(
      "Bus creates potential effects with forwared distance as " +
      std::to_string(
          std::min(searched_distance, kMaxDistanceToGroupBusForwardlyInMeter)) +
      " m and backward distance as " +
      std::to_string(std::min(backward_search_distance,
                              kMaxDistanceToGroupBusBackwardlyInMeter)) +
      " m");

  return std::make_optional<LaneSequenceBusPotentialEffects>(
      {std::move(bus_effects_lane_sequences),
       std::move(bus_potential_effects_map)});
}
}  // namespace

PotentialBusInfo LaneBusEffectDetector::CheckPotentialBus(
    const PlannerObject& object, const RobotState& robot_state,
    const pnc_map::JointPncMapService& joint_pnc_map_service) const {
  // Check some properties of object and lane.
  AgentSnapshotInLaneParam inlane_param;
  const pnc_map::Lane* lane_ptr = nullptr;
  if (!object.is_bus() || object.speed() > kMaxBusSpeedForPotentialCheckInMps) {
    return {false, std::move(inlane_param), lane_ptr};
  }

  if (object.center_associated_lanes().empty()) {
    // Do not handle bus outside the roads.
    return {false, std::move(inlane_param), lane_ptr};
  }
  int64_t reference_center_lane_id = -1;
  if (object.predicted_lane_sequences().has_value() &&
      object.predicted_lane_sequences().value().size() > 0) {
    reference_center_lane_id = object.predicted_lane_sequences().value().at(0);
  }
  for (const auto& center_lane_ptr : object.center_associated_lanes()) {
    lane_ptr = center_lane_ptr;
    // If center of this bus locates in more than one lane, use its prediction
    // start to choose its start lane, if prediection is useless, use its right
    // one.
    if (lane_ptr->id() == reference_center_lane_id) {
      break;
    }
  }
  DCHECK_NE(lane_ptr, nullptr);
  bool has_entered_right_most_lane = false;
  bool will_enter_right_most_lane = false;
  // Check if the center of this object locates in right most lane.
  if (lane_ptr->IsRightmostVehicleLane()) {
    has_entered_right_most_lane = true;
  } else if (object.predicted_lane_sequences().has_value()) {
    // Check whether its predicted trajectory will enter the right most lane.
    for (const auto lane_id : object.predicted_lane_sequences().value()) {
      const pnc_map::Lane* pred_lane =
          joint_pnc_map_service.pnc_map_service()->GetLaneById(lane_id);
      if (pred_lane != nullptr && pred_lane->IsRightmostVehicleLane()) {
        will_enter_right_most_lane = true;
        break;
      }
    }
  }

  if (has_entered_right_most_lane || will_enter_right_most_lane) {
    inlane_param = lane_selection::ComputeObjectStaticInLaneParam(
        lane_ptr->center_line(), lane_ptr->left_marking()->line(),
        lane_ptr->right_marking()->line(), lane_ptr->border(),
        object.contour());
    const EgoInLaneParams ego_in_lane_params =
        lane_selection::GetEgoInLaneParam(robot_state, lane_ptr->center_line());
    // Do not consider the object fully behind ego.
    if (inlane_param.end_arclength_m >
        ego_in_lane_params.arclength_m -
            ego_in_lane_params.rear_axle_to_rear_bumper_m) {
      // Post RT event for monitoring.
      rt_event::PostRtEvent<rt_event::planner::BlockHasPotentialBus>();
      return {true, std::move(inlane_param), lane_ptr};
    }
  }
  return {false, std::move(inlane_param), lane_ptr};
}

std::optional<std::map<int64_t, std::vector<LanePotentialEffect>>>
LaneBusEffectDetector::CalculateBusEffect(
    const PlannerObject& object, const common::LaneSegment& bus_range,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const voy::Pose& pose,
    const std::vector<const pnc_map::Section*>&
        extended_regional_section_sequence,
    double dist_from_pose_to_destination_m) const {
  const hdmap::ZoneLaneOccupancyMap& zone_lane_occupancy_map =
      joint_pnc_map_service.pnc_map_service()->zone_lane_occupancy_map();

  // Note here we use bus_potential_effects_map to store each bus's affect lane
  // effects since effects may have overlap, and effect's range is in
  // arc_length.
  std::map<int64_t, std::vector<LanePotentialEffect>> bus_potential_effects_map;
  auto lane_bus_potential_effects = CheckAndUpdateBusPotentialEffecs(
      object, bus_range, *(joint_pnc_map_service.pnc_map_service()),
      zone_lane_occupancy_map, pose, extended_regional_section_sequence,
      dist_from_pose_to_destination_m);
  if (!lane_bus_potential_effects.has_value()) {
    return std::nullopt;
  }
  for (auto& [lane_id, lane_potential_effects] :
       lane_bus_potential_effects.value().bus_potential_effects_map) {
    if (lane_potential_effects.size() > 1) {
      MergePotentialEffects(PotentialEffectType::kBusMayEffect,
                            lane_potential_effects);
    }
  }
  for (const auto* lane_ptr : SelectEffectLanes(
           lane_bus_potential_effects.value().bus_effects_lane_sequences)) {
    for (auto& lane_potential_effect :
         lane_bus_potential_effects.value().bus_potential_effects_map.at(
             lane_ptr->id())) {
      lane_potential_effect.potential_effect_type =
          PotentialEffectType::kBusEffect;
    }
  }
  return std::make_optional<
      std::map<int64_t, std::vector<LanePotentialEffect>>>(
      std::move(lane_bus_potential_effects.value().bus_potential_effects_map));
}

}  // namespace planner
