#ifndef ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_TEST_TEST_UTILITY_H_
#define ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_TEST_TEST_UTILITY_H_

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include <google/protobuf/text_format.h>
#include "strings/stringprintf.h"

#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner_protos/agent_intention.pb.h"
#include "planner_protos/agent_intention_config.pb.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"

namespace planner {
namespace path {
namespace test {

constexpr int kTrajectoryPosesSize = 80;
// Check blocking sequence results for reasoner.
void CheckBlockingSequence(
    const pb::BlockingSequence& blocking_sequence,
    const std::pair<int, int>& expected_index_range,
    pb::LateralBlockingState::BlockingState expected_blocking_state) {
  EXPECT_EQ(blocking_sequence.blocking_state(), expected_blocking_state);
  EXPECT_EQ(blocking_sequence.range().start_index(),
            expected_index_range.first);
  EXPECT_EQ(blocking_sequence.range().end_index(), expected_index_range.second);
}

// Get ego in-lane params.
EgoInLaneParams GetEgoInLaneParams() {
  EgoInLaneParams ego_param;
  ego_param.plan_start_timestamp_ms = 1551180900106;
  ego_param.speed_mps = 1.0;
  ego_param.accleration_mpss = 0.0;
  ego_param.min_accel_limit = -2.0;
  ego_param.max_accel_limit = 2.0;
  ego_param.min_jerk_limit = -2.0;
  ego_param.max_jerk_limit = 2.0;
  ego_param.max_speed_limit = 33.3;

  ego_param.arclength_m = 3.0;
  ego_param.width_m = 1.0;
  ego_param.rear_axle_to_rear_bumper_m = 0.5;
  ego_param.rear_axle_to_front_bumper_m = 1.0;
  return ego_param;
}

// Get agent trajectory in-lane states.
AgentTrajectoryInLaneStates GetAgentPredictionTrajectory(
    const EgoInLaneParams& ego_param, double relative_arclength_of_ego,
    double agent_speed_mps) {
  AgentTrajectoryInLaneStates trajectory_inlane_states;
  std::vector<StampedAgentSnapshotInLaneState> predicted_states;
  int64_t start_timestamp = ego_param.plan_start_timestamp_ms;
  double start_arclength = ego_param.arclength_m - relative_arclength_of_ego;
  // Generate a predicted trajectory with a uniform speed and an
  // interval of 0.1 seconds
  for (int i = 0; i < /*trajectory poses size=*/80; ++i) {
    StampedAgentSnapshotInLaneState stamped_snapshot_state;
    stamped_snapshot_state.timestamp = start_timestamp + i * /*0.1s=*/100;
    stamped_snapshot_state.inlane_state.inlane_param.start_arclength_m =
        start_arclength + i * agent_speed_mps * 0.1;
    stamped_snapshot_state.inlane_state.inlane_param
        .cross_track_encroachment_m = 0.3;
    stamped_snapshot_state.inlane_state.inlane_param.min_lane_width = 3.5;
    trajectory_inlane_states.predicted_states.emplace_back(
        std::move(stamped_snapshot_state));
  }
  return trajectory_inlane_states;
}

// Generate a predicted trajectory proto of the tracked object given the
// headings.
prediction::pb::PredictedTrajectory GeneratePredictedTrajectory(
    const voy::TrackedObject& tracked_object, int trajectory_id,
    const double initial_timestamp, const std::vector<double>& headings) {
  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  std::vector<planner::pb::TrajectoryPose> poses(headings.size());

  double x = tracked_object.center_x(), y = tracked_object.center_y(),
         v = tracked_object.velocity();
  poses[0].set_timestamp(static_cast<int64_t>(initial_timestamp));
  poses[0].set_x_pos(x);
  poses[0].set_y_pos(y);
  poses[0].set_speed(v);
  poses[0].set_heading(headings[0]);

  for (size_t i = 1; i < headings.size(); i++) {
    x += v * std::cos(headings[i]) * /*time_interval=*/0.1;
    y += v * std::sin(headings[i]) * /*time_interval=*/0.1;
    poses[i].set_timestamp(
        static_cast<int64_t>(initial_timestamp + i * /*0.1s=*/100));
    poses[i].set_x_pos(x);
    poses[i].set_y_pos(y);
    poses[i].set_speed(v);
    poses[i].set_heading(headings[i]);
    poses[i].set_odom(poses[i - 1].odom() + v * /*time_interval=*/0.1);
    v += tracked_object.acceleration() * /*time_interval=*/0.1;
  }
  for (const auto& pose : poses) {
    predicted_trajectory_proto.add_traj_poses()->CopyFrom(pose);
  }
  predicted_trajectory_proto.set_id(/*id=*/trajectory_id);
  predicted_trajectory_proto.set_is_multi_output_trajectory(true);
  return predicted_trajectory_proto;
}

}  // namespace test
}  // namespace path
}  // namespace planner

#endif  // ONBOARD_PLANNER_PATH_REASONING_AGENT_INTENTION_REASONER_TEST_TEST_UTILITY_H_
