#include "planner/utility/longitudinal_profile/longitudinal_costing.h"

#include <algorithm>
#include <limits>
#include <vector>

#include "planner/constants.h"
#include "planner/utility/primitive/primitive_definition/longitudinal_primitive.h"
#include "planner/utility/primitive/utility/primitive_costing_utility.h"

namespace planner {

namespace longitudinal {

namespace {

using primitive::ACCPrimitive;
using primitive::LongitudinalPrimitives;
using primitive::PushPrimitive;
using primitive::SpeedZonePrimitive;
using primitive::StopLinePrimitive;

using StateIndex = LongitudinalFormula::StateIndex;
using ControlIndex = LongitudinalFormula::ControlIndex;
using CostingUtil = LongitudinalFormula::FormulaCostingUtil;
using FeatureParameter = LongitudinalFormula::PbFeatureParameter;

using Feature = CostingUtil::Feature;
using FeatureLet = CostingUtil::FeatureLet;
using Hamiltonian = CostingUtil::Hamiltonian;

constexpr int X_DIM = LongitudinalFormula::StateIndex::X_DIM;
[[maybe_unused]] constexpr int U_DIM = LongitudinalFormula::ControlIndex::U_DIM;

using State = LongitudinalFormula::State;
using Control = LongitudinalFormula::Control;

// TODO(Hengtong): clean up the repeated function in the file
// planner/trajectory/trajectory_costing.cpp.

// Computes the longitudinal speed constraint.
Feature ConstraintSpeed(
    const State& x, const vehicle_model::pb::LongitudinalModelLimit& limit) {
  const double speed = x[StateIndex::SPEED];
  const double speed_violation = control_util::ComputeViolation(
      speed, limit.speed_limit().min_val(), limit.speed_limit().max_val());

  Feature feature(1);
  feature.components[0].val = speed_violation;
  if (!math::NearZero(feature.components[0].val)) {
    feature.components[0].jacob[StateIndex::SPEED] = 1.0;
  }

  return feature;
}

// Computes the longitudinal acceleration constraint.
Feature ConstraintAccel(
    const State& x, const vehicle_model::pb::LongitudinalModelLimit& limit) {
  const double accel = x[StateIndex::ACCEL];
  const double accel_violation = control_util::ComputeViolation(
      accel, limit.accel_limit().min_val(), limit.accel_limit().max_val());

  Feature feature(1);
  feature.components[0].val = accel_violation;
  if (!math::NearZero(feature.components[0].val)) {
    feature.components[0].jacob[StateIndex::ACCEL] = 1.0;
  }

  return feature;
}

// Computes the longitudinal jerk constraint.
Feature ConstraintJerk(const Control& u,
                       const vehicle_model::pb::LongitudinalModelLimit& limit) {
  const double jerk = u[ControlIndex::JERK];
  const double jerk_violation = control_util::ComputeViolation(
      jerk, limit.jerk_limit().min_val(), limit.jerk_limit().max_val());

  Feature feature(1);
  feature.components[0].val = jerk_violation;
  if (!math::NearZero(feature.components[0].val)) {
    feature.components[0].jacob[X_DIM + ControlIndex::JERK] = 1.0;
  }

  return feature;
}

// Computes the lateral accel constraint.
Feature ConstraintLateralAccel(const State& x, const math::Curve2d& curve,
                               double plan_start_arclength,
                               double max_abs_lateral_accel_mpss) {
  const double arclength = x[StateIndex::ODOM] + plan_start_arclength;
  // Query the curvature on curve, if out ot range, kappa is zero.
  const double kappa = curve.InRange(arclength)
                           ? curve.GetInterpSignedCurvature(arclength)
                           : 0.0;
  const double v = x[StateIndex::SPEED];
  const double lat_acc = kappa * v * v;
  const double lat_acc_violation = control_util::ComputeViolation(
      lat_acc, -max_abs_lateral_accel_mpss, max_abs_lateral_accel_mpss);

  Feature feature(1);
  feature.components[0].val = lat_acc_violation;
  if (!math::NearZero(feature.components[0].val)) {
    feature.components[0].jacob[StateIndex::SPEED] = 2 * v * kappa;
  }

  return feature;
}

// Computes longitudinal acceleration effort.
Feature EffortAcceleration(const State& x) {
  const double accel = x[StateIndex::ACCEL];

  Feature feature(1);
  if (accel >= 0.0) {
    feature.components[0].val = accel;
    feature.components[0].jacob[StateIndex::ACCEL] = 1.0;
  }

  return feature;
}

// Computes longitudinal deceleration effort.
Feature EffortDeceleration(const State& x) {
  const double accel = x[StateIndex::ACCEL];

  Feature feature(1);
  if (accel <= 0.0) {
    feature.components[0].val = accel;
    feature.components[0].jacob[StateIndex::ACCEL] = 1.0;
  }

  return feature;
}

// Computes longitudinal positive jerk effort.
Feature EffortPositiveJerk(const Control& u) {
  const double jerk = u[ControlIndex::JERK];

  Feature feature(1);
  if (jerk >= 0.0) {
    feature.components[0].val = jerk;
    feature.components[0].jacob[X_DIM + ControlIndex::JERK] = 1.0;
  }

  return feature;
}

// Computes longitudinal negative jerk effort.
Feature EffortNegativeJerk(const Control& u) {
  const double jerk = u[ControlIndex::JERK];

  Feature feature(1);
  if (jerk <= 0.0) {
    feature.components[0].val = jerk;
    feature.components[0].jacob[X_DIM + ControlIndex::JERK] = 1.0;
  }

  return feature;
}

// Computes longitudinal negative jerk effort.
Feature EffortNegativeComfortJerk(const Control& u, double min_jerk_mpsss) {
  const double jerk = u[ControlIndex::JERK];

  Feature feature(1);
  if (jerk <= 0.0) {
    const double jerk_violation =
        control_util::ComputeViolation(jerk, min_jerk_mpsss, 0.0);
    feature.components[0].val = jerk_violation;
    if (!math::NearZero(feature.components[0].val)) {
      feature.components[0].jacob[X_DIM + ControlIndex::JERK] = 1.0;
    }
  }

  return feature;
}

// Computes the lateral acceleration effort.
Feature EffortLateralAccel(const State& x, const math::Curve2d& curve,
                           double plan_start_arclength) {
  const double arclength = x[StateIndex::ODOM] + plan_start_arclength;
  // Query the curvature on curve, if out ot range, kappa is zero.
  const double kappa =
      curve.InRange(arclength) ? curve.GetInterpCurvature(arclength) : 0.0;
  const double v = x[StateIndex::SPEED];
  const double lat_acc = kappa * v * v;

  Feature feature(1);
  feature.components[0].val = lat_acc;
  feature.components[0].jacob[StateIndex::SPEED] = 2 * v * kappa;

  return feature;
}

// Computes stop line violation primitive.
Feature PrimitiveViolationStopLine(
    const State& x, const std::optional<StopLinePrimitive>& stop_fence,
    double odom_buffer_m) {
  if (!stop_fence) {
    return Feature();
  }

  // Find the closest stop fence.
  const double stopping_odom =
      stop_fence->shifted_feasible_arclength_m - odom_buffer_m;

  const double odom = x[StateIndex::ODOM];
  const double odom_violation = std::max(odom - stopping_odom, 0.0);

  Feature feature(1);
  feature.components[0].val = odom_violation;
  if (odom_violation > 0.0) {
    feature.components[0].jacob[StateIndex::ODOM] = 1.0;
  }

  return feature;
}

// Computes speed zone violation primitive.
Feature PrimitiveViolationSpeedZone(
    const State& x, const std::vector<SpeedZonePrimitive>& speed_zones) {
  const double speed_min = 0.0;
  const double speed_max =
      primitive::QuerySpeedLimitByOdometer(speed_zones, x[StateIndex::ODOM]);
  const double speed = x[StateIndex::SPEED];
  const double speed_violation =
      control_util::ComputeViolation(speed, speed_min, speed_max);

  Feature feature(1);
  feature.components[0].val = speed_violation;
  if (!math::NearZero(feature.components[0].val)) {
    feature.components[0].jacob[StateIndex::SPEED] = 1.0;
  }

  return feature;
}

// Computes acc safety violation primitive.
Feature PrimitiveViolationAccSafety(
    const State& x,
    const primitive::ACCPrimitiveCollection& acc_fences_collection) {
  // Find the closest acc safety fence.
  double acc_odom = std::numeric_limits<double>::max();
  if (!acc_fences_collection.acc_fences.empty()) {
    acc_odom =
        acc_fences_collection
            .acc_fences[acc_fences_collection.nearest_acc_safety_fence_idx]
            .shifted_safety_fence_feasible_arclength_m;
  }

  const double odom = x[StateIndex::ODOM];
  const double odom_violation = std::max(odom - acc_odom, 0.0);

  Feature feature(1);
  feature.components[0].val = odom_violation;
  if (odom_violation > 0.0) {
    feature.components[0].jacob[StateIndex::ODOM] = 1.0;
  }

  return feature;
}

// Computes push safety violation primitive.
Feature PrimitiveViolationPushSafety(
    const State& x, const std::optional<PushPrimitive>& furthest_push_fence) {
  if (!furthest_push_fence) {
    return Feature();
  }

  const double odom = x[StateIndex::ODOM];
  const double odom_violation =
      std::max(furthest_push_fence->shifted_safety_arclength_m - odom, 0.0);

  Feature feature(1);
  if (odom_violation > 0.0) {
    feature.components[0].val = odom_violation;
    feature.components[0].jacob[StateIndex::ODOM] = -1.0;
  }

  return feature;
}

// Computes reward progress primitive.
Feature PrimitiveSoftRewardProgress(
    const State& x, const vehicle_model::pb::LongitudinalModelLimit& limit,
    const primitive::ACCPrimitiveCollection& acc_fences_collection,
    const std::optional<StopLinePrimitive>& stop_fence, double odom_buffer_m) {
  // Find the closest acc safety fence.
  double acc_safety_odom = std::numeric_limits<double>::max();
  if (!acc_fences_collection.acc_fences.empty()) {
    acc_safety_odom =
        acc_fences_collection
            .acc_fences[acc_fences_collection.nearest_acc_safety_fence_idx]
            .shifted_safety_fence_feasible_arclength_m;
  }

  const double stopping_odom =
      stop_fence ? stop_fence->shifted_feasible_arclength_m - odom_buffer_m
                 : std::numeric_limits<double>::max();
  const double odom = x[StateIndex::ODOM];
  const double odom_limit =
      constants::kTrajectoryHorizonInSec * limit.speed_limit().max_val();

  Feature feature(1);
  feature.components[0].val = std::max(
      std::min({odom_limit, acc_safety_odom, stopping_odom}) - odom, 0.0);
  if (feature.components[0].val > 0) {
    feature.components[0].jacob[StateIndex::ODOM] = -1.0;
  }
  return feature;
}

// Computes pull to stop line primitive.
Feature PrimitiveSoftStopLinePull(
    const State& x, const std::optional<StopLinePrimitive>& stop_fence) {
  if (!stop_fence) {
    return Feature();
  }

  // Find the closest stop fence.
  const double stopping_odom = stop_fence->shifted_feasible_arclength_m;

  const double odom = x[StateIndex::ODOM];
  const double odom_to_stopline = std::max(stopping_odom - odom, 0.0);

  Feature feature(1);
  feature.components[0].val = odom_to_stopline;
  if (odom_to_stopline > 0.0) {
    feature.components[0].jacob[StateIndex::ODOM] = -1.0;
  }

  return feature;
}

// Computes acc soft repellence primitive.
Feature PrimitiveSoftAccComfort(
    const State& x,
    const primitive::ACCPrimitiveCollection& acc_fences_collection) {
  // TODO(tianyugu): costing of multiple soft fences.
  // Find the closest acc comfort fence.
  double acc_odom = std::numeric_limits<double>::max();
  double ratio = 1.0;
  if (!acc_fences_collection.acc_fences.empty()) {
    const ACCPrimitive& nearest_acc_fence =
        acc_fences_collection
            .acc_fences[acc_fences_collection.nearest_acc_comfort_fence_idx];
    acc_odom = nearest_acc_fence.shifted_comfort_fence_arclength_m;
    ratio = nearest_acc_fence.encroachment_ratio;
  }

  const double odom = x[StateIndex::ODOM];
  const double odom_violation = std::max(odom - acc_odom, 0.0);

  Feature feature(1);
  feature.components[0].val = odom_violation;
  if (odom_violation > 0.0) {
    feature.components[0].jacob[StateIndex::ODOM] = 1.0;
  }
  feature.components[0].weight_adjust = ratio;

  return feature;
}

// Computes push soft repellence primitive.
Feature PrimitiveSoftPushComfort(
    const State& x, const std::optional<PushPrimitive>& furthest_push_fence) {
  if (!furthest_push_fence) {
    return Feature();
  }

  const double odom = x[StateIndex::ODOM];
  const double odom_violation =
      std::max(furthest_push_fence->shifted_comfort_arclength_m - odom, 0.0);

  Feature feature(1);
  if (odom_violation > 0.0) {
    feature.components[0].val = odom_violation;
    feature.components[0].jacob[StateIndex::ODOM] = -1.0;
  }
  return feature;
}

}  // namespace

control_util::pb::LongitudinalFeatureParameter MergeWithMotionModelParams(
    control_util::pb::LongitudinalFeatureParameter feature_param,
    const vehicle_model::LongitudinalModelWithAxleRectangularShape&
        motion_model_with_shape) {
  *feature_param.mutable_motion_model_limit() =
      motion_model_with_shape.param().limit();
  return feature_param;
}

}  // namespace longitudinal
}  // namespace planner

namespace control_util {
template <>
planner::longitudinal::LongitudinalFormula::StepwisePrimitives
planner::longitudinal::LongitudinalFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const {
  (void)step;
  (void)is_terminal;
  (void)x;
  (void)u;
  (void)primitives;
  return {};
}

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kLongitudinal>(
    int step, int total_step_size,
    const planner::longitudinal::LongitudinalFormula::PbCostingTerm&
        costing_term,
    const planner::longitudinal::LongitudinalFormula::PbFeatureParameter&
        feature_param,
    const planner::longitudinal::LongitudinalFormula::State& x,
    const planner::longitudinal::LongitudinalFormula::Control& u,
    const planner::longitudinal::LongitudinalFormula::StepwisePrimitives&
        stepwise_primitives,
    const planner::longitudinal::LongitudinalFormula::Primitives& primitives,
    planner::longitudinal::LongitudinalFormula::Feature* feature) {
  (void)stepwise_primitives;
  const auto& feature_type = costing_term.feature_type();
  const double plan_start_arclength = primitives.plan_start_arclength;
  const planner::primitive::TimeInvariantLongitudinalPrimitives&
      time_invariant = primitives.time_invariant;
  const planner::primitive::TimeVariantLongitudinalPrimitives& time_variant =
      primitives.time_variant[step];
  const auto& nominal_path_curve = time_invariant.nominal_path->curve();
  bool is_terminal = (step == total_step_size);

  switch (feature_type) {
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        CONSTRAINT_LON_SPEED:
      (*feature) = planner::longitudinal::ConstraintSpeed(
          x, feature_param.motion_model_limit());
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        CONSTRAINT_LON_ACCEL:
      (*feature) = planner::longitudinal::ConstraintAccel(
          x, feature_param.motion_model_limit());
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        CONSTRAINT_LON_JERK:
      (*feature) = !is_terminal ? planner::longitudinal::ConstraintJerk(
                                      u, feature_param.motion_model_limit())
                                : planner::longitudinal::Feature(1);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        CONSTRAINT_LON_LATERAL_ACCEL:
      (*feature) = planner::longitudinal::ConstraintLateralAccel(
          x, nominal_path_curve, plan_start_arclength,
          feature_param.extreme_lateral_acceleration());
      break;

    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_ACCELERATION:
      (*feature) = planner::longitudinal::EffortAcceleration(x);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_DECELERATION:
      (*feature) = planner::longitudinal::EffortDeceleration(x);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_POSITIVE_JERK:
      (*feature) = !is_terminal ? planner::longitudinal::EffortPositiveJerk(u)
                                : planner::longitudinal::Feature(1);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_NEGATIVE_JERK:
      (*feature) = !is_terminal ? planner::longitudinal::EffortNegativeJerk(u)
                                : planner::longitudinal::Feature(1);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_LATERAL_ACCEL:
      (*feature) = !is_terminal
                       ? planner::longitudinal::EffortNegativeComfortJerk(
                             u, feature_param.negative_comfort_jerk_limit())
                       : planner::longitudinal::Feature(1);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        EFFORT_LON_NEGATIVE_COMFORT_JERK:
      (*feature) = planner::longitudinal::EffortLateralAccel(
          x, nominal_path_curve, plan_start_arclength);
      break;

    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LON_STOP_LINE:
      (*feature) = planner::longitudinal::PrimitiveViolationStopLine(
          x, time_variant.nearest_stop_fence,
          feature_param.stop_line_violation_buffer());
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LON_SPEED_ZONE:
      (*feature) = planner::longitudinal::PrimitiveViolationSpeedZone(
          x, time_invariant.hard_speed_zones);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LON_ACC_SAFETY:
      (*feature) = planner::longitudinal::PrimitiveViolationAccSafety(
          x, time_variant.acc_fences_collection);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_VIOLATION_LON_PUSH_SAFETY:
      (*feature) = planner::longitudinal::PrimitiveViolationPushSafety(
          x, time_variant.furthest_push_fence);
      break;

    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_SOFT_LON_REWARD_PROGRESS:
      (*feature) = is_terminal
                       ? planner::longitudinal::PrimitiveSoftRewardProgress(
                             x, feature_param.motion_model_limit(),
                             time_variant.acc_fences_collection,
                             time_variant.nearest_stop_fence,
                             feature_param.stop_line_violation_buffer())
                       : planner::longitudinal::Feature(1);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_SOFT_LON_STOP_LINE_PULL:
      (*feature) = planner::longitudinal::PrimitiveSoftStopLinePull(
          x, time_variant.nearest_stop_fence);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_SOFT_LON_ACC_COMFORT:
      (*feature) = planner::longitudinal::PrimitiveSoftAccComfort(
          x, time_variant.acc_fences_collection);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_SOFT_LON_PUSH_COMFORT:
      (*feature) = planner::longitudinal::PrimitiveSoftPushComfort(
          x, time_variant.furthest_push_fence);
      break;
    case planner::longitudinal::LongitudinalFormula::PbFeatureType::
        PRIMITIVE_SOFT_LON_SPEED_ZONE:
      (*feature) = planner::longitudinal::PrimitiveViolationSpeedZone(
          x, time_invariant.soft_speed_zones);
      break;
    default:
      DCHECK(false) << "Unrecognized feature type";
      (*feature) = planner::longitudinal::Feature(1);
  }
}

}  // namespace control_util
