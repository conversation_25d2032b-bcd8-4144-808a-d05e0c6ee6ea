#ifndef OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_AVOIDANCE_DECELERATION_H_
#define OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_AVOIDANCE_DECELERATION_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdint.h>

#define CD_FLOAT_ALMOST_ZERO ((float)0.0001F)

/* Maximum necessary longitudinal deceleration */
#define CD_MAX_NEC_LONG_DECEL (-10.0F)

#define CD_MIN_LONG_DISTANCE (0.02F)
/* root array size for a quadratic equation */
#define CD_ROOT_ARRAY_SIZE

extern float CalculateAvoidanceDeceleration(float ego_dx, float ego_vx,
                                            float ego_ax, float obj_dx,
                                            float obj_vx, float obj_ax,
                                            uint8_t *state);

extern float CalculateAvoidanceDecelerationWithJerk(float ego_dx, float ego_vx,
                                                    float ego_ax, float obj_dx,
                                                    float obj_vx, float obj_ax,
                                                    uint8_t *state,
                                                    uint8_t *iteration_number);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_LONG_FUNCTION_STOP_IN_LANE_AVOIDANCE_DECELERATION_H_
