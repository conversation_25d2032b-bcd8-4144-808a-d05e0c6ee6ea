#include "planner/speed/reasoning/reasoning_pipeline.h"

#include <algorithm>
#include <limits>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

#include "absl/strings/str_cat.h"

#include <tbb/parallel_for.h>
#include <tbb/task_arena.h>

#include "latency/latency_stat.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/assist/feature_extraction/utils/assist_stuck_feature_extraction.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/speed/agent_reaction/agent_reaction_seed_manager.h"
#include "planner/speed/agent_reaction/agent_reaction_tracker/agent_intention_tracker.h"
#include "planner/speed/agent_reaction/agent_reaction_tracker/agent_reaction_tracker.h"
#include "planner/speed/agent_reaction/reaction_utils.h"
#include "planner/speed/reasoning/agent_trajectory_info_util.h"
#include "planner/speed/reasoning/overtaking_overtaken_reasoning.h"
#include "planner/speed/reasoning/reasoner/cautious_driving_reasoner/cautious_driving_reasoner.h"
#include "planner/speed/reasoning/reasoner/construction_zone_reasoner/construction_zone_reasoner.h"
#include "planner/speed/reasoning/reasoner/cross_agent_reasoner/cross_agent_reasoner.h"
#include "planner/speed/reasoning/reasoner/destination_reasoner/destination_reasoner.h"
#include "planner/speed/reasoning/reasoner/hazard_light_reasoner/hazard_light_reasoner.h"
#include "planner/speed/reasoning/reasoner/hold_reasoner/hold_reasoner.h"
#include "planner/speed/reasoning/reasoner/honk_reasoner/honk_reasoner.h"
#include "planner/speed/reasoning/reasoner/jaywalker_reasoner/jaywalker_reasoner.h"
#include "planner/speed/reasoning/reasoner/keep_clear_reasoner/keep_clear_reasoner.h"
#include "planner/speed/reasoning/reasoner/lane_change_speed_reasoner/lane_change_speed_reasoner.h"
#include "planner/speed/reasoning/reasoner/lead_and_merge_agent_reasoner/lead_and_merge_agent_reasoner.h"
#include "planner/speed/reasoning/reasoner/mrc_stop_reasoner/mrc_stop_reasoner.h"
#include "planner/speed/reasoning/reasoner/occlusion_reasoner/occlusion_reasoner.h"
#include "planner/speed/reasoning/reasoner/oncoming_agent_reasoner/oncoming_agent_reasoner.h"
#include "planner/speed/reasoning/reasoner/pullout_reasoner/pullout_reasoner.h"
#include "planner/speed/reasoning/reasoner/pullover_prepare_reasoner/pullover_prepare_reasoner.h"
#include "planner/speed/reasoning/reasoner/stop_sign_reasoner/stop_sign_reasoner.h"
#include "planner/speed/reasoning/reasoner/tailgater_reasoner/tailgater_reasoner.h"
#include "planner/speed/reasoning/reasoner/traffic_light_reasoner/traffic_light_reasoner.h"
#include "planner/speed/reasoning/reasoner/turn_signal_reasoner/turn_signal_reasoner.h"
#include "planner/speed/reasoning/reasoner/unstuck_speed_reasoner/unstuck_speed_reasoner.h"
#include "planner/speed/reasoning/reasoner/vru_reasoner/vru_reasoner.h"
#include "planner/speed/reasoning/reasoner/waypoint_assist_reasoner/waypoint_assist_reasoner.h"
#include "planner/speed/reasoning/reasoning_basic_util.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning/route_association_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/util/matching_crosswalk.h"
#include "planner/speed/util/precedence/road_precedence_util.h"
#include "planner/speed/util/precedence/road_priority_util.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "prediction/common/definition/prediction_constants.h"
#include "prediction_protos/agent.pb.h"
#include "prediction_protos/predicted_trajectory.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/perception_attribute.pb.h"
#include "voy_protos/perception_object_type.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace speed {
namespace {

// The minimum distance to junction to enable lane association.
constexpr double kMinDistanceOfEgoToJunctionToEnableLaneAssociationInMeter =
    15.0;

// If agent's velocity is below this threshold, then we think the agent is near
// stationary. There is a similar config called
// default_agent_near_stationary_speed_in_mps, the reason we don't use this
// config is that we still want to tune this threshold within ART.
constexpr double kAgentAtLowSpeedThresholdInMps = 0.3;

// The time duration to estimate agent's jerk from the its previous
// acceleration.
constexpr double kDurationToEstimateJerkInSec = 0.5;

// This time duration is used to identify objects with limited tracking frames.
// If the object's tracking duration is within this limit (5 frames or 0.5
// second), the object is considered to have unreliable prediction information.
constexpr double kMaxDurationOfLimitedTrackingInSeconds = 0.5;

// Below this threshold cyclist will be considered obviously slow down.
constexpr double kObviousSlowDownAccelForVRUInMpss = -0.4;

// Adds the overlap_slice ptr closest to plan_init_pose from primary
// trajectory if one exists.
void AddPlanInitPoseOverlapSlicePtr(
    const std::vector<pb::OverlapRegion>& overlap_regions,
    ReasoningObject& reasoning_object) {
  const pb::OverlapSlice* closest_slice_to_ego_ptr = nullptr;
  for (const auto& region : overlap_regions) {
    if (!region.is_primary() && !region.is_construction_zone()) continue;
    const auto* slice_near_plan_init_time =
        reasoning_util::FindOverlapSliceNearPlanInitTime(region);
    if (slice_near_plan_init_time == nullptr) continue;
    // There can be multiple overlap regions for one
    // trajectory. Choose the one that has the smallest
    // moving distance.
    if (closest_slice_to_ego_ptr == nullptr ||
        GetPaddedOverlapStart(*slice_near_plan_init_time) <
            GetPaddedOverlapStart(*closest_slice_to_ego_ptr)) {
      closest_slice_to_ego_ptr = slice_near_plan_init_time;
    }
  }
  if (closest_slice_to_ego_ptr != nullptr) {
    reasoning_object.set_plan_init_pose_overlap_slice_ptr(
        closest_slice_to_ego_ptr);
  }
}

// Updates the Value of ReasoningObject's member variables.
void UpdateReasoningObject(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::IntentionResult& ego_intention,
    const std::vector<PredictedTrajectoryWrapper>& predicted_trajectories,
    const std::vector<pb::OverlapRegion>& overlap_regions,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const pb::AgentReactionTrackerSeed& current_agent_reaction_tracker_seed,
    const planner::pb::AgentMapElementOccupancySeeds&
        current_agent_map_element_occupancy_seeds,
    const speed::pb::AgentReactionSeeds& current_agent_reaction_seeds,
    ReasoningObject& reasoning_object) {
  reasoning_object.set_is_open_door(
      reasoning_object.planner_object().is_open_door_vehicle());
  reasoning_object.set_is_stationary(
      reasoning_object.planner_object().is_stationary());
  reasoning_object.set_is_truck(reasoning_object.planner_object().is_truck());
  reasoning_object.set_has_seen_ego_time(AgentHasSeenEgoTime(
      reasoning_object.id(),
      GetAgentPredictedTrajectoryInitialTime(predicted_trajectories),
      current_agent_reaction_seeds));

  // is_path_overtaking must be set before is_leading_agent.
  // It corresponds to path setting is_overtaken to true to allow
  // speed reduce comfort required lateral gap.
  reasoning_object.set_is_ego_path_overtaking(
      reasoning_util::IsEgoPathOvertaking(reasoning_object, ego_intention));

  // Considers agents ahead Ego's front bumper as leading agents.
  reasoning_object.set_is_leading_agent(reasoning_util::IsLeadingAgent(
      trajectory_info, reasoning_object,
      /*min_ra_arc_length=*/world_context.ra_to_leading_bumper()));

  reasoning_object.set_is_behind_ego_front_axle(
      reasoning_util::IsBehindEgoFrontAxle(reasoning_object,
                                           world_context.ra_to_front_axle()));

  reasoning_object.set_is_behind_ego_leading_bumper(
      reasoning_util::IsBehindEgoLeadingBumper(
          reasoning_object, world_context.ra_to_leading_bumper()));

  reasoning_object.set_is_behind_ego_rear_axle(
      reasoning_object.object_proximity_info().projected_ra_arc_length().end() <
      0.0);

  reasoning_object.set_is_fully_behind(reasoning_util::IsFullyBehind(
      reasoning_object, world_context.ra_to_trailing_bumper_shift()));

  reasoning_object.set_is_fully_ahead(reasoning_util::IsFullyAhead(
      reasoning_object, world_context.ra_to_leading_bumper()));

  reasoning_object.set_is_far_away_behind(reasoning_util::IsFarAwayBehind(
      reasoning_object.object_proximity_info(), world_context.ego_speed(),
      world_context.ra_to_trailing_bumper_shift()));

  reasoning_object.set_is_likely_tailgating(reasoning_util::IsLikelyTailgating(
      trajectory_info, reasoning_object,
      world_context.ra_to_trailing_bumper_shift(),
      world_context.robot_state().plan_init_state_snapshot().timestamp()));

  reasoning_object.set_is_dominant_stuck_object_for_xlane_nudge(
      reasoning_util::IsDominantStuckObjectForXLaneNudge(trajectory_info,
                                                         reasoning_object));

  reasoning_object.set_is_likely_turbo_ped(
      reasoning_util::IsLikelyTurboPed(reasoning_object));

  reasoning_object.set_is_not_facing_ego_vehicle(
      reasoning_util::IsNotFacingEgoVehicle(reasoning_object));

  reasoning_object.set_is_in_neighboring_lane(
      reasoning_util::IsAgentInNeighboringLane(trajectory_info,
                                               reasoning_object));

  const auto& previous_constraint_results =
      previous_iter_seed.speed_seed().constraint_results();
  reasoning_object.set_was_yield(std::any_of(
      previous_constraint_results.begin(), previous_constraint_results.end(),
      [&reasoning_object](
          const speed::pb::SpeedConstraintResult& constraint_result) {
        return constraint_result.constraint().obj_id() ==
                   reasoning_object.id() &&
               constraint_result.constraint().settings().yield_option() !=
                   pb::ConstraintSettings::SOFT &&
               constraint_result.decision() == speed::pb::SpeedDecision::YIELD;
      }));

  reasoning_object.set_was_pass(
      !reasoning_object.was_yield() &&
      std::any_of(
          previous_constraint_results.begin(),
          previous_constraint_results.end(),
          [&reasoning_object](
              const speed::pb::SpeedConstraintResult& constraint_result) {
            return constraint_result.constraint().obj_id() ==
                       reasoning_object.id() &&
                   constraint_result.decision() ==
                       speed::pb::SpeedDecision::PASS;
          }));

  reasoning_object.set_is_on_sidewalk(
      reasoning_util::IsVRUOnSidewalkBesideEgoLaneSequence(trajectory_info,
                                                           reasoning_object));
  reasoning_object.set_is_outside_physical_boundary(
      reasoning_util::IsObjectOutsidePhysicalBoundary(
          reasoning_object,
          trajectory_info.closest_physical_boundary_proximity_info()));

  reasoning_object.set_is_encroaching_lane_vehicle(
      reasoning_util::IsVehicleEncroachingEgoLane(reasoning_object,
                                                  trajectory_info));

  reasoning_object.set_is_fod_preferable_to_drive_through(
      reasoning_util::IsFoDPreferableToDriveThrough(reasoning_object,
                                                    ego_intention));

  reasoning_object.set_is_potential_uturn_blockage(
      reasoning_util::IsPotentialUTurnBlockage(trajectory_info,
                                               reasoning_object));

  const auto& agent_element_occupancy_map =
      current_agent_map_element_occupancy_seeds.agent_element_occupancy_map();
  const auto iter = agent_element_occupancy_map.find(reasoning_object.id());
  if (iter != agent_element_occupancy_map.end()) {
    // If the agent occupied map elements info exists in the seed, retrieve its
    // pointer to populate corresponding field for the reasoning object,
    // otherwise it defaults to nullptr.
    reasoning_object.set_agent_occupied_map_element_infos_in_seed_ptr(
        &iter->second);
  }

  // Set agent filtered_acceleration based on current acceleration if no
  // historical tracking data is available.
  double filtered_acceleration = reasoning_object.acceleration();

  bool is_vru_obviously_slow_down =
      reasoning_object.acceleration() < kObviousSlowDownAccelForVRUInMpss;

  // Add all ART related info
  const auto art_object_iter =
      current_agent_reaction_tracker_seed.agent_reaction_tracker_info_map()
          .find(reasoning_object.id());
  if (art_object_iter !=
      current_agent_reaction_tracker_seed.agent_reaction_tracker_info_map()
          .end()) {
    const speed::pb::AgentReactionTrackerInfo& art_info =
        art_object_iter->second;
    const int64_t curr_timestamp = world_context.ego_pose_timestamp();
    ARTObjectInfo art_object_info;
    art_object_info.agent_art_seed_ptr = &(art_object_iter->second);
    art_object_info.latest_constraint_timestamp =
        art_info.last_timestamp_constraint_added();
    art_object_info.min_v_in_seed =
        AgentReactionTracker::GetAgentMinVelocityFromSeed(art_info,
                                                          curr_timestamp);
    art_object_info.agent_leads_duration =
        AgentReactionTracker::ComputeAgentLeadsDuration(art_info,
                                                        curr_timestamp,
                                                        /*threshold=*/0.0);
    const int t_gap_continuous_tracking_start_idx =
        AgentReactionTracker::FindContinousTGapTrackingStartIndex(
            art_info, curr_timestamp);
    art_object_info.t_gap_shrinks_duration =
        AgentReactionTracker::ComputeTGapShrinksDuration(
            art_info, curr_timestamp, t_gap_continuous_tracking_start_idx);
    art_object_info.t_gap_enlarges_duration =
        AgentReactionTracker::ComputeTGapEnlargesDuration(
            art_info, curr_timestamp, t_gap_continuous_tracking_start_idx);
    art_object_info.near_stationary_duration =
        reasoning_util::ComputeAgentBelowSpeedThresholdDuration(
            art_info, reasoning_object.speed(), kAgentAtLowSpeedThresholdInMps,
            curr_timestamp);
    if (art_info.agent_conflict_info_tracking().size() > 0) {
      auto latest_record = art_info.agent_conflict_info_tracking().rbegin();
      if ((latest_record->has_t_gap())) {
        DCHECK(latest_record->has_distance_gap());
        art_object_info.latest_t_gap = latest_record->t_gap();
        art_object_info.latest_distance_gap = latest_record->distance_gap();
      }
      if ((latest_record->has_expected_lat_accel())) {
        art_object_info.expected_lat_accel =
            latest_record->expected_lat_accel();
      }
      if ((latest_record->has_min_agent_arrives_time_from_prediction())) {
        art_object_info.min_agent_arrives_time_from_prediction_in_last_frame =
            latest_record->min_agent_arrives_time_from_prediction();
      }
    }
    reasoning_object.set_art_object_info(std::move(art_object_info));

    reasoning_object.set_latest_low_speed_timestamp(
        reasoning_util::ComputeLatestTimestampAgentLowSpeed(art_info));

    // Calculate agent filtered_acceleration based on ART info.
    filtered_acceleration = reasoning_util::FilterAgentAcceleration(art_info);

    // As pedestrian's acceleration value is not obvious, so we will use history
    // speed to check whether a pedestrian is obviously slowing down or not.
    if (reasoning_object.is_pedestrian_or_cyclist()) {
      is_vru_obviously_slow_down |=
          reasoning_object.is_cyclist()
              ? reasoning_util::IsAgentUnderObviouslySlowDownState(
                    art_info, kObviousSlowDownAccelForVRUInMpss)
              : reasoning_util::
                    IsAgentObviouslySlowingDownFromAgentHistorySpeed(
                        art_info, reasoning_object.speed());
    }

    reasoning_object.set_estimated_jerk(reasoning_util::EstimateAgentJerk(
        art_info, kDurationToEstimateJerkInSec, reasoning_object.acceleration(),
        curr_timestamp));
  }
  reasoning_object.set_filtered_acceleration(filtered_acceleration);

  reasoning_object.set_is_vru_obviously_slow_down(is_vru_obviously_slow_down);

  reasoning_object.set_is_driving_ahead_in_same_section_along_ego(
      reasoning_util::IsAgentAheadDrivingInSameLaneSectionAlongEgoLaneSequence(
          reasoning_object, trajectory_info));

  // Set the field |is_reversing_vehicle_for_defensive_driving| after ART result
  // being populated as it requires related tracking info.
  reasoning_object.set_is_reversing_vehicle_for_defensive_driving(
      reasoning_util::ShouldConsiderDefensiveDrivingForReversingVehicles(
          reasoning_object));

  reasoning_object.set_has_limited_tracking_frames(
      (reasoning_object.TrackedDurationInSeconds() <
           kMaxDurationOfLimitedTrackingInSeconds &&
       reasoning_object.TrackedDurationInSeconds() != 0));

  // Set the field |consistent_cross_intention_count_|.
  reasoning_object.set_consistent_cross_intention_count(
      reasoning_util::ComputeAgentConsistentCrossIntentionCountForVRU(
          reasoning_object, predicted_trajectories, overlap_regions,
          previous_iter_seed.speed_seed().agent_cross_intention_tracker(),
          world_context.ego_pose_timestamp()));

  AddPlanInitPoseOverlapSlicePtr(overlap_regions, reasoning_object);

  reasoning_object.set_is_longitudinally_in_narrow_passage(
      reasoning_object.plan_init_pose_overlap_slice_ptr() != nullptr &&
      reasoning_util::IsRangeInNarrowPassages(
          world_context.narrow_passage_infos(),
          GetPaddedOverlapStart(
              *reasoning_object.plan_init_pose_overlap_slice_ptr()),
          GetPaddedOverlapEnd(
              *reasoning_object.plan_init_pose_overlap_slice_ptr())));
  reasoning_object.set_is_unstuck_object_in_narrow_passage(
      reasoning_object.is_longitudinally_in_narrow_passage() &&
      reasoning_util::ShouldReducePassRequiredLateralGapUnderCriticalToUnstuck(
          reasoning_object, trajectory_info, world_context.ego_speed()));

  reasoning_object.set_is_plg_resettable_for_unstuck(
      reasoning_util::IsPassRequiredLateralGapResettableForStuckAgent(
          world_context, reasoning_object));
}

// Associate linked reasoning objects and update infos based on the associated
// head or tail.
// NOTE: Call this function after all reasoning objects have been created and
// updated.
void AssociateAndUpdateInfoOfLinkedObjects(
    std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    pb::ReasoningInputDebug* reasoning_input_debug) {
  for (PredictionDecisionMakerInput& reasoning_input : reasoning_inputs) {
    ReasoningObject& reasoning_object = reasoning_input.reasoning_object;
    if (reasoning_object.IsLinkedHeadVehicle()) {
      const ObjectId tail_id = reasoning_object.associated_vehicle_tail_id();
      auto tail_input_iter = std::find_if(
          reasoning_inputs.begin(), reasoning_inputs.end(),
          [tail_id](const PredictionDecisionMakerInput& other_reasoning_input) {
            return other_reasoning_input.reasoning_object.id() == tail_id;
          });
      if (tail_input_iter != reasoning_inputs.end()) {
        reasoning_object.set_associated_vehicle_tail_object_ptr(
            &(tail_input_iter->reasoning_object));
      }
    }

    if (reasoning_object.IsLinkedTailVehicle()) {
      const ObjectId head_id = reasoning_object.associated_vehicle_head_id();
      auto head_input_iter = std::find_if(
          reasoning_inputs.begin(), reasoning_inputs.end(),
          [head_id](const PredictionDecisionMakerInput& other_reasoning_input) {
            return other_reasoning_input.reasoning_object.id() == head_id;
          });
      if (head_input_iter != reasoning_inputs.end()) {
        reasoning_object.set_associated_vehicle_head_object_ptr(
            &(head_input_iter->reasoning_object));
        // Use the linked head object's |has_seen_ego_time| and
        // |is_encroaching_lane_vehicle| to update that of the tail object.
        reasoning_object.set_has_seen_ego_time(
            head_input_iter->reasoning_object.has_seen_ego_time());
        if (!reasoning_object.is_encroaching_lane_vehicle() &&
            reasoning_object.associated_vehicle_head_object_ptr() != nullptr &&
            reasoning_object.associated_vehicle_head_object_ptr()
                ->is_encroaching_lane_vehicle()) {
          reasoning_object.set_is_encroaching_lane_vehicle(true);
        }

        // Update debug as well.
        if (reasoning_input_debug != nullptr) {
          auto debug_iter = std::find_if(
              reasoning_input_debug->mutable_reasoning_objects()->begin(),
              reasoning_input_debug->mutable_reasoning_objects()->end(),
              [&reasoning_object](const pb::ReasoningObjectDebug& debug) {
                return debug.object_id() == reasoning_object.id();
              });
          if (debug_iter ==
              reasoning_input_debug->mutable_reasoning_objects()->end()) {
            DCHECK(false) << "Not found reasoning obj debug, Obj: "
                          << reasoning_object.id();
            return;
          }
          debug_iter->set_has_seen_ego_time(
              reasoning_object.has_seen_ego_time());
          debug_iter->mutable_attributes()->set_is_crossing_lane_veh(
              reasoning_object.is_encroaching_lane_vehicle());
        }
      }
    }
  }
}

// Generates AgentTrajectoryInfos of an PredictionDecisionMakerInput object.
std::vector<AgentTrajectoryInfo> GenerateAgentPredictionDecisionMakerInput(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<planner::PredictedTrajectoryWrapper>&
        predicted_trajectories,
    const std::vector<pb::OverlapRegion>& overlap_regions,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const ReasoningObject& reasoning_object,
    const std::vector<std::pair<int64_t, math::Range<double>>>&
        ego_lane_sequence_id_ra_arclength_ranges,
    std::string& debug_str) {
  const planner::PlannerObject& planner_object =
      reasoning_object.planner_object();
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  speed::AgentIntentionTracker agent_intention_tracker;
  const std::optional<double>& yield_probability_with_intention_tracking =
      reasoning_util::
              IsLaneChangeInterestedAgentDuringLaneChangeOrLaneChangeAbort(
                  trajectory_info, reasoning_object)
          ? std::make_optional(
                agent_intention_tracker
                    .ComputeAgentYieldProbabilityWithIntentionTracking(
                        trajectory_info.cross_lane_info_manager()
                            .GetInteractiveAgentsIds(
                                trajectory_info.is_lane_change_in_abort(),
                                world_context.ra_to_leading_bumper()),
                        world_context.ego_speed(),
                        world_context.ra_to_trailing_bumper_shift(),
                        world_context.robot_state()
                            .plan_init_state_snapshot()
                            .timestamp(),
                        world_context
                            .GetAgentConditionalPredictionYieldIntention(
                                reasoning_object.id()),
                        trajectory_info.GetCrossLaneInfo(), reasoning_object,
                        object_proximity_info, previous_iter_seed, debug_str))
          : std::nullopt;
  const pb::LaneChangeAgentIntentionFusionResult&
      agent_intention_with_model_fusion =
          agent_intention_tracker.ComputeAgentYieldProbabilityWithModelFusion(
              world_context.ra_to_trailing_bumper_shift(),
              world_context.robot_state().GetLength(),
              world_context.robot_state().GetWidth(), world_context.ego_speed(),
              world_context.robot_state()
                  .plan_init_state_snapshot()
                  .timestamp(),
              world_context.GetAgentConditionalPredictionYieldIntention(
                  reasoning_object.id()),
              world_context.GetAgentConditionalPredictionYieldTrajectoryPair(
                  reasoning_object.id()),
              yield_probability_with_intention_tracking,
              trajectory_info.GetCrossLaneInfo(),
              trajectory_info.is_lane_change_in_progress(), reasoning_object,
              previous_iter_seed, debug_str);
  // Construct AgentTrajectoryInfo.
  std::vector<AgentTrajectoryInfo> agent_trajectory_infos;
  agent_trajectory_infos.reserve(predicted_trajectories.size());
  for (const planner::PredictedTrajectoryWrapper& predicted_trajectory :
       predicted_trajectories) {
    // DecoupledManeuver only considers predictions from multiple outputs,
    // except for the STM trajectory with the specific ID 999.
    DCHECK(predicted_trajectory.id() ==
                   prediction::constants::kVehicleStartToMoveTrajectoryId
               ? predicted_trajectory.MightStartToMoveSoon()
               : true);

    if (!predicted_trajectory.is_multi_output_trajectory() &&
        predicted_trajectory.id() !=
            prediction::constants::kVehicleStartToMoveTrajectoryId) {
      absl::StrAppend(&debug_str, "ignore traj ", predicted_trajectory.id(),
                      " for non-multi output;");
      continue;
    }

    std::vector<OverlapRegionReference> trajectory_overlap_regions;
    // NOTE: Handle multiple overlap regions for one trajectory,
    // especially for U-turn scenes.
    for (const auto& overlap_region : overlap_regions) {
      // Construction zone does not have corresponding trajectory or tracked
      // object and will be handled directly by ConstructionZoneReasoner.
      DCHECK(!overlap_region.is_construction_zone());
      DCHECK_EQ(overlap_region.object_id(), planner_object.id());
      if (overlap_region.trajectory_id() != predicted_trajectory.id()) {
        continue;
      }

      trajectory_overlap_regions.push_back(std::cref(overlap_region));
    }

    // If the predicted trajectory does not overlap with Ego path, in most
    // of the cases Ego is fine to fully ignore it.
    if (trajectory_overlap_regions.empty()) {
      absl::StrAppend(&debug_str, "ignore traj ", predicted_trajectory.id(),
                      " for empty overlap;");
      continue;
    }

    AgentTrajectoryInfo& agent_trajectory_info =
        agent_trajectory_infos.emplace_back(GenerateAgentTrajectoryInfo(
            world_context, trajectory_info, reasoning_object,
            predicted_trajectory, std::move(trajectory_overlap_regions)));

    const int64_t agent_will_see_ego_timestamp = AgentWillSeeEgoTime(
        predicted_trajectory, world_context.robot_state().last_trajectory());
    if (agent_will_see_ego_timestamp != std::numeric_limits<int64_t>::max()) {
      agent_trajectory_info.set_will_see_ego_time(
          math::Ms2Sec(agent_will_see_ego_timestamp -
                       predicted_trajectory.begin()->timestamp()));
    }

    // NOTE: This is a short-term solution to get the lane association result
    // when ego is straight through a junction for scoping a couple of specific
    // scenes, e.g., agent's doing UPL , going straight along with ego, or
    // merging from right. This won't introduce additional computation load than
    // before as we already did lane association for
    // |IsAgentStraightThroughJunctionInSameDirection| in road precedence
    // inference, and |IsAgentMergingFromRightTurnAtJunction| for reasoner in
    // charge computation.
    // TODO(minhanli): Revisit and clean up the logic here when global lane
    // association is enabled.

    const traffic_rules::JunctionInLaneSequence* first_junction_ahead =
        traffic_rules::GetFirstJunctionAhead(
            trajectory_info.traffic_rules().lane_sequence_junctions);
    const bool is_ego_about_to_going_straight_through_junction =
        first_junction_ahead != nullptr &&
        first_junction_ahead->ra_arclength_range_m.start_pos <
            kMinDistanceOfEgoToJunctionToEnableLaneAssociationInMeter &&
        first_junction_ahead->ego_lane_in_junction_ptr->turn() ==
            hdmap::Lane_Turn_STRAIGHT;

    if (!predicted_trajectory.associated_route_opt().has_value()) {
      if (FLAGS_planning_enable_agent_predicted_trajectory_route_association ||
          is_ego_about_to_going_straight_through_junction) {
        agent_trajectory_info.set_adhoc_agent_associated_route_opt(
            reasoning_util::GetAdHocAssociatedRouteForPredictedTrajectory(
                reasoning_object, predicted_trajectory,
                *world_context.joint_pnc_map_service(),
                /*allow_using_for_cyclist=*/false));
      }
    }

    std::string debug_str;
    const RoadPrecedenceResult road_precedence_result =
        util::GetPredictedTrajectoryRoadPrecedenceAgainstEgo(
            trajectory_info, ego_lane_sequence_id_ra_arclength_ranges,
            agent_trajectory_info.overlap_regions(),
            agent_trajectory_info.overlap_region_infos(), reasoning_object,
            predicted_trajectory, agent_trajectory_info.road_priority(),
            agent_trajectory_info.associated_route_opt(),
            *world_context.joint_pnc_map_service(),
            world_context.ra_to_leading_bumper(),
            world_context.ra_to_trailing_bumper_shift(), &debug_str);
    agent_trajectory_info.set_road_precedence(
        road_precedence_result.road_precedence);
    agent_trajectory_info.set_precedence_source(
        road_precedence_result.precedence_source);
    agent_trajectory_info.set_precedence_scene_type(
        road_precedence_result.precedence_scene_type);
    agent_trajectory_info.set_consider_yield_intention(
        reasoning_util::ShouldConsiderYieldIntention(
            reasoning_object, trajectory_info, agent_trajectory_info));
    // As the function IsAgentExtraCautiousOnTrajectory utilizes the field
    // consider_yield_intention, consider_yield_intention needs to be set first.
    agent_trajectory_info.set_is_agent_extra_cautious_on_trajectory(
        reasoning_util::IsAgentExtraCautiousOnTrajectory(
            reasoning_object, trajectory_info, agent_trajectory_info));
    agent_trajectory_info.set_is_agent_extra_cautious_for_slow_cut_in_in_front(
        reasoning_util::IsLowPrecedenceSlowCutInVehicleInFrontOfEgo(
            world_context, trajectory_info, reasoning_object,
            agent_trajectory_info, &debug_str));
    agent_trajectory_info.set_is_overtaking_ego(
        reasoning_util::IsAgentOvertakingEgo(
            world_context, reasoning_object,
            agent_trajectory_info.overlap_regions()));
    agent_trajectory_info.set_is_overtaken_by_ego(
        reasoning_util::IsAgentOvertakenByEgo(
            world_context, reasoning_object,
            agent_trajectory_info.overlap_regions()));
    agent_trajectory_info.set_is_cutting_in(
        reasoning_util::IsAgentTrajectoryCuttingInEgoPath(
            agent_trajectory_info));
    agent_trajectory_info.set_is_cutting_out(
        reasoning_util::IsAgentTrajectoryCuttingOutFromEgoPath(
            trajectory_info, reasoning_object, agent_trajectory_info));
    agent_trajectory_info.set_is_agent_exiting_roundabout_from_inner_lane(
        reasoning_util::IsAgentExitingRoundaboutFromInnerLanesOfEgo(
            reasoning_object, trajectory_info, agent_trajectory_info));

    const bool is_ego_about_to_turn_along_with_large_vehicle =
        reasoning_util::IsEgoAboutToTurnAlongWithLargeVehicleInJunction(
            world_context, trajectory_info, reasoning_object,
            predicted_trajectory);
    if (is_ego_about_to_turn_along_with_large_vehicle) {
      agent_trajectory_info.set_is_large_vehicle_turning_along_with_ego(true);
    }

    const auto& optional_associated_route =
        agent_trajectory_info.associated_route_opt();
    if (optional_associated_route.has_value()) {
      const std::vector<route_association::MapElementAndPoseInfo>&
          associated_route = optional_associated_route.value();
      agent_trajectory_info.set_is_merging_from_side_road(
          route_association_util::IsAgentMergingFromSideRoad(
              trajectory_info.lane_sequence_iterator(), associated_route,
              agent_trajectory_info.EgoHasHigherRoadPrecedence()));
      agent_trajectory_info.set_is_merging_from_right_turn_at_junction(
          route_association_util::IsAgentMergingFromRightTurnAtJunction(
              trajectory_info.lane_sequence_iterator().lane_sequence(),
              associated_route));
      agent_trajectory_info.set_is_on_u_turn_lane(
          route_association_util::IsAgentTrajectoryOnUTurnLane(
              associated_route));
      agent_trajectory_info.set_is_crossing_opposite_road_blocks(
          route_association_util::IsAgentTrajectoryCrossingOppositeBlocks(
              associated_route));
    }
    agent_trajectory_info.set_is_reversing_trajectory_for_defensive_driving(
        reasoning_object.is_reversing_vehicle_for_defensive_driving() &&
        (agent_trajectory_info.IsTurningLeft() ||
         agent_trajectory_info.IsCompleteReverseDriving()));
    agent_trajectory_info.set_is_inside_overtaking(
        reasoning_util::IsInsideOvertaking(
            world_context, trajectory_info, reasoning_object,
            agent_trajectory_info.overlap_regions()));
    // The reason for agent reaction tracker result to be part of agent
    // trajectory info instead of reasoning object is that, we may want to
    // include the yield probability as part of ART determination.
    // TODO(junying): The placement of agent reaction tracker result is still
    // subject to discussion.
    agent_trajectory_info.set_agent_reaction_tracker_result(
        speed::AgentReactionTracker::GetAgentReactionTrackerResult(
            reasoning_object, agent_trajectory_info, previous_iter_seed,
            agent_trajectory_info.mutable_art_debug()));
    agent_trajectory_info.set_yield_probability_with_intention_tracking(
        yield_probability_with_intention_tracking);
    agent_trajectory_info.set_agent_intention_with_model_fusion(
        agent_intention_with_model_fusion);
    agent_trajectory_info.set_predicted_average_acceleration(
        reasoning_util::ComputeAverageAccelerationOnPredictedTrajectory(
            reasoning_object, agent_trajectory_info));
    agent_trajectory_info.set_debug_str(debug_str);
  }

  return agent_trajectory_infos;
}

[[maybe_unused]] bool HasLargeVehicleRearEndCollisionRisk(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs) {
  constexpr double kMaxLateralGapForLikelyTailgating = 1.0;
  constexpr double kMaxLongitudinalGapForLikelyTailgating = 30.;
  for (const PredictionDecisionMakerInput& input : reasoning_inputs) {
    const ReasoningObject& reasoning_object = input.reasoning_object;
    const std::vector<AgentTrajectoryInfo>& agent_trajectory_infos =
        input.agent_trajectory_infos;
    bool interact_with_ego = std::any_of(
        agent_trajectory_infos.begin(), agent_trajectory_infos.end(),
        [](const AgentTrajectoryInfo& agent_trajectory_info) {
          const std::vector<OverlapRegionReference>& overlap_regions =
              agent_trajectory_info.overlap_regions();
          return std::any_of(overlap_regions.begin(), overlap_regions.end(),
                             [](const OverlapRegionReference& region) {
                               return region.get().contain_strict_overlap();
                             });
        });
    bool within_interested_range =
        reasoning_object.object_proximity_info()
                .projected_ra_arc_length()
                .end() < world_context.ra_to_leading_bumper() &&
        reasoning_object.object_proximity_info()
                .projected_ra_arc_length()
                .end() > world_context.ra_to_leading_bumper() -
                             kMaxLongitudinalGapForLikelyTailgating &&
        reasoning_object.object_proximity_info().abs_lateral_gap() <
            kMaxLateralGapForLikelyTailgating;
    if (reasoning_object.IsLargeVehicle() && interact_with_ego &&
        within_interested_range) {
      return true;
    }
  }
  return false;
}

// TODO(junying): Even thought the ART result is trajectory level, but currently
// we don't take any BP level signal(e.g. yield intention) into consideration.
// So the results of each BP are the same for now, so we only need update once
// for each agent. Moreover, we would like to latch the most influential one.
// Similar to was_pass/was_yield, if the agent is determined to be nonreactive
// on any bp, we would like to latch the NONREACTIVE result. In the future, if
// the ART results are different for each BP, this part needs to be updated.
void UpdateAgentReactionTrackerResultSeed(
    const std::vector<PredictionDecisionMakerOutput>&
        prediction_decision_maker_outputs,
    speed::pb::AgentReactionTrackerResultSeed* mutable_seed) {
  for (const PredictionDecisionMakerOutput& output :
       prediction_decision_maker_outputs) {
    const ObjectId object_id = output.reasoning_object().id();
    const auto iter =
        mutable_seed->object_id_to_agent_reaction_tracker_result_map().find(
            object_id);
    if (iter !=
        mutable_seed->object_id_to_agent_reaction_tracker_result_map().end()) {
      continue;
    }
    for (const PredictionDecision& prediction_decision :
         output.prediction_decisions()) {
      if (prediction_decision.agent_trajectory_info()
              .agent_reaction_tracker_result() !=
          pb::AgentReactionTrackerResult::ART_NONE) {
        (*mutable_seed
              ->mutable_object_id_to_agent_reaction_tracker_result_map())
            [object_id] = prediction_decision.agent_trajectory_info()
                              .agent_reaction_tracker_result();
        continue;
      }
    }
  }
  // Note that even though the agent reaction tracker result is NONREACTIVE for
  // some agent, it does not mean the AR will be disabled. It will depends on
  // the decision if ART result should be considered in prediction decision
  // maker. This is similar to agent reaction, where we are able always compute
  // agent reaction parameters, but its update prediction decision maker to
  // decide if AR should be considered.
  // For instance, tailgaters are mostly nonreactive, and we should be able to
  // see them in the AgentReactionTrackerResultSeed.
  for (const PredictionDecisionMakerOutput& output :
       prediction_decision_maker_outputs) {
    const ObjectId object_id = output.reasoning_object().id();
    for (const PredictionDecision& prediction_decision :
         output.prediction_decisions()) {
      const auto& optional_yield_probability_with_intention_tracking =
          prediction_decision.agent_trajectory_info()
              .yield_probability_with_intention_tracking();
      const auto& agent_intention_with_model_fusion =
          prediction_decision.agent_trajectory_info()
              .agent_intention_with_model_fusion();
      if (agent_intention_with_model_fusion.is_agent_intention_fusion_valid()) {
        (*mutable_seed
              ->mutable_object_id_to_yield_probability_with_model_fusion_map())
            [object_id] = agent_intention_with_model_fusion;
      }
      if (optional_yield_probability_with_intention_tracking.has_value()) {
        (*mutable_seed
              ->mutable_object_id_to_yield_probability_with_intention_tracking_map())
            [object_id] =
                optional_yield_probability_with_intention_tracking.value();
        continue;
      }
    }
  }
}

speed::PinchRegionsMapper ComputePinchRegionsBasedOnEgoLateralClearance(
    const pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    const std::vector<PredictionDecisionMakerInput>& /* reasoning_inputs */,
    const speed::pb::PinchRegionComputationConfig& config,
    speed::pb::PinchRegionsDebug* debug) {
  speed::PinchRegionsMapper pinch_regions_mapper;
  // Compute the left and right pinch points for the vehicle.
  pinch_regions_mapper.left_pinch_regions_for_vehicles =
      reasoning_util::ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance_info,
          config.vehicle().min_clearance_to_consider_pinch_region(),
          config.vehicle().min_gap_between_pinch_regions(), /*is_left=*/true,
          debug == nullptr ? nullptr
                           : debug->mutable_left_pinch_regions_for_vehicles());
  pinch_regions_mapper.right_pinch_regions_for_vehicles =
      reasoning_util::ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance_info,
          config.vehicle().min_clearance_to_consider_pinch_region(),
          config.vehicle().min_gap_between_pinch_regions(), /*is_left=*/false,
          debug == nullptr ? nullptr
                           : debug->mutable_right_pinch_regions_for_vehicles());

  // Compute the left and right pinch points for the cyclist.
  pinch_regions_mapper.left_pinch_regions_for_cyclists =
      reasoning_util::ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance_info,
          config.cyclist().min_clearance_to_consider_pinch_region(),
          config.cyclist().min_gap_between_pinch_regions(), /*is_left=*/true,
          debug == nullptr ? nullptr
                           : debug->mutable_left_pinch_regions_for_cyclists());
  pinch_regions_mapper.right_pinch_regions_for_cyclists =
      reasoning_util::ComputePinchRegionBasedOnLateralClearance(
          ego_lateral_clearance_info,
          config.cyclist().min_clearance_to_consider_pinch_region(),
          config.cyclist().min_gap_between_pinch_regions(), /*is_left=*/false,
          debug == nullptr ? nullptr
                           : debug->mutable_right_pinch_regions_for_cyclists());

  // TODO(chantong): scope the situation where the pinch region should be
  // updated with different threshold for specific reasoning objects. Store into
  // the left_pinch_regions_for_agents OR right_pinch_regions_for_agents.

  return pinch_regions_mapper;
}

// Removes the agent whose AgentCrossIntentionInfo has not been updated for a
// long while from the seed.
void RemoveStaleAgentCrossIntentionInfo(
    const pb::AgentCrossIntentionTracker& previous_iter_seed,
    const int64_t current_timestamp,
    pb::AgentCrossIntentionTracker& mutable_current_iter_seed) {
  constexpr int64_t kRemoveStaleAgentHoldInfoThresholdInMs = 3000;
  mutable_current_iter_seed = previous_iter_seed;
  auto& mutable_object_id_to_agent_intention_info =
      *(mutable_current_iter_seed
            .mutable_object_id_to_agent_cross_intention_info_map());
  for (auto iter = mutable_object_id_to_agent_intention_info.begin();
       iter != mutable_object_id_to_agent_intention_info.end();) {
    if (iter->second.latest_timestamp_having_cross_intention() <
        current_timestamp - kRemoveStaleAgentHoldInfoThresholdInMs) {
      iter = mutable_object_id_to_agent_intention_info.erase(iter);
    } else {
      ++iter;
    }
  }
}

// Updates the cross intention of agents. Currently, we only keep tracking
// the cross intention for VRUs.
void UpdateAgentCrossIntentionTrackerSeed(
    const std::vector<PredictionDecisionMakerOutput>&
        prediction_decision_maker_outputs,
    const pb::AgentCrossIntentionTracker& previous_iter_seed,
    const int64_t current_timestamp,
    pb::AgentCrossIntentionTracker& mutable_current_iter_seed) {
  // 1. Remove the stale agent cross intention. If one agent with cross
  // intention disappear suddenly from Ego field of view, its cross intention
  // will exist in the seed forever and will cause the increased memory usage.
  // Thus, we should remove them explicitly.
  RemoveStaleAgentCrossIntentionInfo(previous_iter_seed, current_timestamp,
                                     mutable_current_iter_seed);

  // 2. Update the agents' AgentCrossIntentionInfo according to ReasoningObject.
  auto& mutable_object_id_agent_cross_intention_info_map =
      *(mutable_current_iter_seed
            .mutable_object_id_to_agent_cross_intention_info_map());
  for (const PredictionDecisionMakerOutput& one_agent_pdm_output :
       prediction_decision_maker_outputs) {
    const ReasoningObject& reasoning_object =
        one_agent_pdm_output.reasoning_object();

    if (reasoning_object.consistent_cross_intention_count() == 0) {
      // The timestamp having cross intention is too old and the result is not
      // latched.
      mutable_object_id_agent_cross_intention_info_map.erase(
          reasoning_object.id());
      continue;
    }

    pb::AgentCrossIntentionInfo& mutable_agent_cross_intention_info =
        mutable_object_id_agent_cross_intention_info_map[reasoning_object.id()];
    DCHECK_GE(
        reasoning_object.consistent_cross_intention_count(),
        mutable_agent_cross_intention_info.consistent_cross_intention_count());
    if (reasoning_object.consistent_cross_intention_count() <=
        mutable_agent_cross_intention_info.consistent_cross_intention_count()) {
      // The result is latched. The seed should not be updated.
      continue;
    }

    // The count of the current iteration is larger than that of the last
    // iteration, so the agent has cross intention at the current iteration.
    // Therefore, we should update the seed.
    mutable_agent_cross_intention_info.set_consistent_cross_intention_count(
        reasoning_object.consistent_cross_intention_count());
    mutable_agent_cross_intention_info
        .set_latest_timestamp_having_cross_intention(current_timestamp);
  }
}

// Updates the speed seed of the current iteration.
void UpdateCurrentIterSpeedSeed(
    const std::vector<PredictionDecisionMakerOutput>&
        prediction_decision_maker_outputs,
    const pb::SpeedSeed& previous_iter_speed_seed,
    const int64_t current_timestamp,
    pb::SpeedSeed& mutable_current_iter_speed_seed) {
  UpdateAgentCrossIntentionTrackerSeed(
      prediction_decision_maker_outputs,
      previous_iter_speed_seed.agent_cross_intention_tracker(),
      current_timestamp,
      *(mutable_current_iter_speed_seed
            .mutable_agent_cross_intention_tracker()));

  UpdateAgentReactionTrackerResultSeed(
      prediction_decision_maker_outputs,
      mutable_current_iter_speed_seed
          .mutable_agent_reaction_tracker_result_seed());
}

}  // namespace

// A function template to prepare reasoning inputs more efficiently.
template <typename MapType>
std::vector<PredictionDecisionMakerInput> PrepareReasoningInputsInternal(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::IntentionResult& ego_intention,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>&
        overlap_computation_policy_map,
    const MapType& object_prediction_map,
    const std::optional<std::map<ObjectId, ObjectOccupancyParamInfo>>&
        optional_object_occupancy_param_map,
    const std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
        object_overlap_region_map,
    const std::map<int64_t, pb::ObjectProximityInfo>& object_proximity_infos,
    const std::unordered_map<int64_t, PrincipledRequiredLateralGap>&
        obj_id_to_required_lat_gaps,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed, bool use_tbb,
    pb::ReasoningInputDebug* reasoning_input_debug) {
  TRACE_EVENT_SCOPE(planner, PredictionDecisionMakerInput_Prepare);

  std::vector<PredictionDecisionMakerInput> reasoning_inputs;
  reasoning_inputs.reserve(planner_object_map.size());

  using PrincipledRequiredLateralGapIter = typename std::remove_reference<
      decltype(obj_id_to_required_lat_gaps)>::type::const_iterator;
  using ObjectProximityInfoIter = typename std::remove_reference<
      decltype(object_proximity_infos)>::type::const_iterator;
  using PlannerObjectIter = typename std::remove_reference<
      decltype(planner_object_map)>::type::const_iterator;
  using ObjectOccupancyParamInfoIter = typename std::remove_reference<
      decltype(optional_object_occupancy_param_map)>::type::value_type::
      const_iterator;
  using OverlapComputationPolicyIter = typename std::remove_reference<
      decltype(overlap_computation_policy_map)>::type::const_iterator;
  std::vector<PrincipledRequiredLateralGapIter> required_lateral_gap_iters;
  std::vector<ObjectProximityInfoIter> object_proximity_iters;
  std::vector<PlannerObjectIter> planner_object_iters;
  std::vector<ObjectOccupancyParamInfoIter> object_occupancy_param_iters{};
  std::vector<OverlapComputationPolicyIter> overlap_computation_policy_iters;

  required_lateral_gap_iters.resize(planner_object_map.size(),
                                    obj_id_to_required_lat_gaps.begin());
  object_proximity_iters.resize(planner_object_map.size(),
                                object_proximity_infos.begin());
  planner_object_iters.resize(planner_object_map.size(),
                              planner_object_map.begin());
  overlap_computation_policy_iters.resize(
      overlap_computation_policy_map.size(),
      overlap_computation_policy_map.begin());
  if (optional_object_occupancy_param_map.has_value()) {
    object_occupancy_param_iters.resize(
        planner_object_map.size(),
        optional_object_occupancy_param_map.value().begin());
  }

  // constexpr int kTbbTaskArenaThreadsNum = 6;
  const size_t total_cpu_numbers = common::CPUCoresNumberOfPlanner();
  const auto tbb_thread_num = static_cast<size_t>(
      total_cpu_numbers * common::kMediumCoresRatioOfThreadsPool);
  tbb::task_arena tbb_task_arena(tbb_thread_num);

  // Retrieve the iterators to the stuff needed for planner_object.
  auto GetConstIteratosTask =
      [&object_proximity_infos, &obj_id_to_required_lat_gaps,
       &planner_object_map, &overlap_computation_policy_map,
       &optional_object_occupancy_param_map, &required_lateral_gap_iters,
       &object_proximity_iters, &planner_object_iters,
       &overlap_computation_policy_iters,
       &object_occupancy_param_iters](int k) {
        auto planner_object_iter = planner_object_map.begin();
        std::advance(planner_object_iter, k);
        auto object_id = planner_object_iter->first;
        const PrincipledRequiredLateralGapIter& required_lateral_gap_iter =
            obj_id_to_required_lat_gaps.find(object_id);
        const ObjectProximityInfoIter& object_proximity_iter =
            object_proximity_infos.find(object_id);
        const OverlapComputationPolicyIter& overlap_computation_policy_iter =
            overlap_computation_policy_map.find(object_id);

        required_lateral_gap_iters[k] = required_lateral_gap_iter;
        planner_object_iters[k] = planner_object_iter;
        object_proximity_iters[k] = object_proximity_iter;
        overlap_computation_policy_iters[k] = overlap_computation_policy_iter;
        if (optional_object_occupancy_param_map.has_value()) {
          const ObjectOccupancyParamInfoIter& object_occupancy_param_iter =
              optional_object_occupancy_param_map.value().find(object_id);
          DCHECK(object_occupancy_param_iter !=
                 optional_object_occupancy_param_map.value().end());
          object_occupancy_param_iters[k] = object_occupancy_param_iter;
        }
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(planner_object_map.size()),
                        GetConstIteratosTask);
    });
  } else {
    for (size_t k = 0; k < planner_object_map.size(); ++k) {
      GetConstIteratosTask(k);
    }
  }

  // DCHECK to enforce the size of |object_occupancy_param_iters| being
  // consistent with the result of |optional_object_occupancy_param_map|.
  if (!object_occupancy_param_iters.empty()) {
    DCHECK(optional_object_occupancy_param_map.has_value());
    DCHECK_EQ(optional_object_occupancy_param_map.value().size(),
              object_occupancy_param_iters.size());
  }

  // Construct reasoning_input sequentially with the stuff produced above
  for (size_t k = 0; k < planner_object_map.size(); ++k) {
    ReasoningObject reasoning_object(
        planner_object_iters[k]->second, object_proximity_iters[k]->second,
        required_lateral_gap_iters[k]->second,
        object_occupancy_param_iters.empty()
            ? nullptr
            : &(object_occupancy_param_iters[k]->second));
    reasoning_object.set_overlap_computation_policy(
        overlap_computation_policy_iters[k]->second);
    reasoning_inputs.emplace_back(reasoning_object,
                                  std::vector<AgentTrajectoryInfo>{});
  }

  // Add debug objects for reasoning input
  const size_t base_reasoning_objects_size =
      reasoning_input_debug->reasoning_objects_size();
  if (reasoning_input_debug) {
    for (size_t i = 0; i < planner_object_map.size(); ++i) {
      reasoning_input_debug->add_reasoning_objects();
    }
  }

  std::vector<std::pair<int64_t, math::Range<double>>>
      ego_lane_sequence_id_ra_arclength_ranges;
  ego_lane_sequence_id_ra_arclength_ranges.reserve(
      trajectory_info.lane_sequence_iterator().size());
  for (const pnc_map::Lane* lane : trajectory_info.lane_sequence_iterator()) {
    const double lane_start_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            lane->center_line().GetStartPoint(), math::pb::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
    const double lane_end_ra_arclength =
        trajectory_info.path().GetProjectionArcLength(
            lane->center_line().GetEndPoint(), math::pb::kForbid) -
        trajectory_info.plan_init_ra_arc_length_on_extended_path();
    ego_lane_sequence_id_ra_arclength_ranges.emplace_back(
        lane->id(),
        math::Range<double>(lane_start_ra_arclength, lane_end_ra_arclength));
  }

  std::shared_ptr<const speed::pb::AgentReactionTrackerSeed>
      current_agent_reaction_tracker_seed =
          Seed::Access<token::AgentReactionTrackerSeed>::GetMsg(
              SpeedCurrentFrame());
  const auto current_agent_map_element_occupancy_seeds =
      Seed::Access<token::AgentMapElementOccupancySeeds>::GetMsg(
          SpeedCurrentFrame());
  const auto current_agent_reaction_seeds =
      Seed::Access<token::AgentReactionSeeds>::GetMsg(SpeedCurrentFrame());

  // Further populate the reasoning_inputs with more infos
  auto PrepareReasoningInputsRange =
      [&world_context, &trajectory_info, &ego_intention, &object_prediction_map,
       &object_overlap_region_map, &previous_iter_seed, &reasoning_input_debug,
       &planner_object_iters, &required_lateral_gap_iters,
       &base_reasoning_objects_size, &reasoning_inputs,
       &ego_lane_sequence_id_ra_arclength_ranges,
       &current_agent_reaction_tracker_seed,
       &current_agent_map_element_occupancy_seeds,
       &current_agent_reaction_seeds](int k) {
        auto planner_object_iter = planner_object_iters[k];
        auto object_id = planner_object_iter->first;
        [[maybe_unused]] const PrincipledRequiredLateralGapIter&
            required_lateral_gap_iter = required_lateral_gap_iters[k];
        const std::vector<planner::PredictedTrajectoryWrapper>&
            predicted_trajectories = object_prediction_map.at(object_id);
        const std::vector<speed::pb::OverlapRegion>& overlap_regions =
            object_overlap_region_map.at(object_id);

        PredictionDecisionMakerInput& reasoning_input = reasoning_inputs[k];

        UpdateReasoningObject(
            world_context, trajectory_info, ego_intention,
            predicted_trajectories, overlap_regions, previous_iter_seed,
            *current_agent_reaction_tracker_seed,
            *current_agent_map_element_occupancy_seeds,
            *current_agent_reaction_seeds, reasoning_input.reasoning_object);

        std::string debug_str;
        reasoning_input.agent_trajectory_infos =
            GenerateAgentPredictionDecisionMakerInput(
                world_context, trajectory_info, predicted_trajectories,
                overlap_regions, previous_iter_seed,
                reasoning_input.reasoning_object,
                ego_lane_sequence_id_ra_arclength_ranges, debug_str);

        // Update debug info for reasoning inputs.
        if (reasoning_input_debug) {
          pb::ReasoningObjectDebug* reasoning_object_debug =
              reasoning_input_debug->mutable_reasoning_objects(
                  base_reasoning_objects_size + k);
          *reasoning_object_debug = reasoning_input.reasoning_object.ToProto();
          reasoning_object_debug->set_debug_str(std::move(debug_str));
          for (const AgentTrajectoryInfo& trajectory_info :
               reasoning_input.agent_trajectory_infos) {
            *(reasoning_object_debug->add_trajectory_infos()) =
                trajectory_info.ToProto();
          }
        }
      };

  if (use_tbb) {
    tbb_task_arena.execute([&] {
      tbb::parallel_for(0, static_cast<int>(planner_object_map.size()),
                        PrepareReasoningInputsRange);
    });
  } else {
    for (size_t k = 0; k < planner_object_map.size(); ++k) {
      PrepareReasoningInputsRange(k);
    }
  }

  // After all reasoning objects have been populated, associate linked objects
  // together and update infos and debugs according to linked objects.
  AssociateAndUpdateInfoOfLinkedObjects(reasoning_inputs,
                                        reasoning_input_debug);

  return reasoning_inputs;
}

std::vector<PredictionDecisionMakerInput> PrepareReasoningInputs(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::IntentionResult& ego_intention,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>&
        overlap_computation_policy_map,
    const std::unordered_map<ObjectId,
                             std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const std::optional<std::map<ObjectId, ObjectOccupancyParamInfo>>&
        optional_object_occupancy_param_map,
    const std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
        object_overlap_region_map,
    const std::map<ObjectId, pb::ObjectProximityInfo>& object_proximity_infos,
    const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
        obj_id_to_required_lat_gaps,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const bool use_tbb, pb::ReasoningInputDebug* reasoning_input_debug) {
  return PrepareReasoningInputsInternal(
      world_context, trajectory_info, ego_intention, planner_object_map,
      overlap_computation_policy_map, object_prediction_map,
      optional_object_occupancy_param_map, object_overlap_region_map,
      object_proximity_infos, obj_id_to_required_lat_gaps, previous_iter_seed,
      use_tbb, reasoning_input_debug);
}

std::vector<PredictionDecisionMakerInput> PrepareReasoningInputs(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::IntentionResult& ego_intention,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>&
        overlap_computation_policy_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const std::optional<std::map<ObjectId, ObjectOccupancyParamInfo>>&
        optional_object_occupancy_param_map,
    const std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
        object_overlap_region_map,
    const std::map<ObjectId, pb::ObjectProximityInfo>& object_proximity_infos,
    const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
        obj_id_to_required_lat_gaps,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const bool use_tbb, pb::ReasoningInputDebug* reasoning_input_debug) {
  return PrepareReasoningInputsInternal(
      world_context, trajectory_info, ego_intention, planner_object_map,
      overlap_computation_policy_map, object_prediction_map,
      optional_object_occupancy_param_map, object_overlap_region_map,
      object_proximity_infos, obj_id_to_required_lat_gaps, previous_iter_seed,
      use_tbb, reasoning_input_debug);
}

void RunReasoning(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const planner::pb::IntentionResult& ego_intention,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const std::unordered_map<ConstructionZoneId, PrincipledRequiredLateralGap>&
        cz_id_to_required_lat_gaps,
    const std::vector<OcclusionArea>& occlusion_areas,
    const std::unordered_map<ObjectId, planner::PlannerObject>&
        hallucinated_planner_object_map,
    const std::unordered_map<ObjectId,
                             std::vector<planner::PredictedTrajectoryWrapper>>&
        hallucinated_object_prediction_map,
    const std::unordered_map<ObjectId, PrincipledRequiredLateralGap>&
        hallucinated_obj_id_to_required_lat_gaps,
    const std::map<ObjectId, std::vector<speed::pb::OverlapRegion>>&
        object_overlap_region_map,
    const std::map<ConstructionZoneId, std::vector<speed::pb::OverlapRegion>>&
        cz_overlap_region_map,
    const std::map<ObjectId, pb::ObjectProximityInfo>& object_proximity_infos,
    const std::map<ConstructionZoneId, speed::pb::ObjectProximityInfo>&
        cz_proximity_infos,
    const std::unordered_map<ConstructionZoneId, const ConstructionZone*>&
        cz_ptr_map,
    const std::vector<PredictionDecisionMakerInput>& reasoning_inputs,
    const speed::pb::EgoLateralClearanceInfo& ego_lateral_clearance_info,
    const pb::SpeedGeneratorConfig& config,
    const planner::pb::DecoupledManeuverSeed& previous_iter_seed,
    const TurnSignalState& current_turn_signal_state,
    speed::ReasoningPolicies& reasoning_policies,
    SpeedPipelineMeta& speed_pipeline_meta,
    speed::pb::SpeedSeed* mutable_current_iter_speed_seed,
    std::vector<planner::pb::AssistRequest>& mutable_assist_requests,
    ConstraintCreator* constraint_creator_ptr,
    GlobalStrategySetter* global_strategy_setter,
    CautiousDrivingLimiter* reference_limiter_ptr,
    planner::GapQualityEvaluator* lane_change_gap_quality_evaluator_ptr,
    planner::TurnSignalDirective* turn_signal_request_ptr,
    HonkRequest* honk_request_ptr, HazardLightRequest* hazard_light_request_ptr,
    planner::pb::EgoStuckFeatureAboutDecoupledSpeed*
        ego_stuck_feature_about_speed,
    speed::pb::ReasoningDebug* debug_proto_ptr) {
  speed::pb::SpeedReasonerSeeds* mutable_current_iter_seed =
      mutable_current_iter_speed_seed->mutable_speed_reasoner_seeds();
  // Prepare Reasoning Inputs.
  pb::ReasoningInputDebug* reasoning_input_debug =
      (debug_proto_ptr != nullptr) ? debug_proto_ptr->mutable_input_debug()
                                   : nullptr;

  // Compute pinch regions for reasoning objects.
  pb::PinchRegionsDebug* pinch_regions_debug =
      (debug_proto_ptr != nullptr) ? debug_proto_ptr->mutable_pinch_regions()
                                   : nullptr;
  const speed::PinchRegionsMapper pinch_region_mapper =
      ComputePinchRegionsBasedOnEgoLateralClearance(
          ego_lateral_clearance_info, reasoning_inputs,
          config.pinch_region_computation_config(), pinch_regions_debug);

  const UrgencyInfo urgency_info(
      world_context, trajectory_info, planner_object_map, cz_ptr_map,
      reasoning_inputs, previous_iter_seed, config.reasoning(),
      *(mutable_current_iter_speed_seed->mutable_urgency_info_seed()),
      (debug_proto_ptr == nullptr) ? nullptr
                                   : debug_proto_ptr->mutable_urgency_info());

  if (FLAGS_planning_enable_uturn_unstuck &&
      std::any_of(reasoning_inputs.begin(), reasoning_inputs.end(),
                  [](const PredictionDecisionMakerInput& reasoning_input) {
                    const ReasoningObject& reasoning_object =
                        reasoning_input.reasoning_object;
                    return reasoning_object.is_potential_uturn_blockage();
                  })) {
    // Skip opt profile check if driving towards a potential uturn blockage.
    global_strategy_setter->set_enable_opt_profile_validation(false);
  }

  // Increase execution efficiency for k-turn.
  if (trajectory_info.open_space_info().IsActive()) {
    global_strategy_setter->set_enable_opt_profile_validation(false);
    if (trajectory_info.open_space_info().unstuck_scene_type ==
        planner::pb::UnstuckSceneType::UTURN) {
      global_strategy_setter->set_attraction_for_progress();
    }
  }

  // WaypointAssistReasoner.
  {
    TRACE_EVENT_SCOPE(planner, WaypointAssistReasoner_Speed);
    reasoner::WaypointAssistReasoner waypoint_assist_reasoner;
    waypoint_assist_reasoner.Reason(world_context, trajectory_info,
                                    object_proximity_infos, urgency_info,
                                    previous_iter_seed, global_strategy_setter,
                                    constraint_creator_ptr, debug_proto_ptr);
  }

  // Run Trajectory Related Reasoners and limiters to add non-agent
  // constraints and speed limits.
  // TrafficLightReasoner.
  {
    TRACE_EVENT_SCOPE(planner, TrafficLightReasoner_Speed);
    reasoner::TrafficLightReasoner traffic_light_reasoner;
    traffic_light_reasoner.Reason(
        world_context, trajectory_info, previous_iter_seed,
        config.reasoning().reasoners().traffic_light_reasoner(),
        mutable_current_iter_seed, reference_limiter_ptr,
        constraint_creator_ptr, debug_proto_ptr);
  }

  // DestinationReasoner.
  {
    TRACE_EVENT_SCOPE(planner, DestinationReasoner_Speed);
    reasoner::DestinationReasoner destination_reasoner;
    destination_reasoner.Reason(
        world_context, trajectory_info, trajectory_info.reverse_driving_info(),
        constraint_creator_ptr, ego_stuck_feature_about_speed, debug_proto_ptr);
  }

  // KeepClearReasoner.
  {
    TRACE_EVENT_SCOPE(planner, KeepClearReasoner_Speed);
    reasoner::KeepClearReasoner keep_clear_reasoner;
    keep_clear_reasoner.Reason(
        world_context, trajectory_info, urgency_info,
        config.reasoning().reasoners().keep_clear_reasoner(),
        constraint_creator_ptr, debug_proto_ptr);
  }

  // TurnSignalReasoner.
  {
    TRACE_EVENT_SCOPE(planner, TurnSignalReasoner_Speed);
    reasoner::TurnSignalReasoner turn_signal_reasoner;
    turn_signal_reasoner.Reason(world_context, trajectory_info,
                                previous_iter_seed, current_turn_signal_state,
                                mutable_current_iter_seed,
                                turn_signal_request_ptr, debug_proto_ptr);
  }

  // StopSignReasoner
  {
    TRACE_EVENT_SCOPE(planner, StopSignReasoner_Speed);
    reasoner::StopSignReasoner stop_sign_reasoner;
    stop_sign_reasoner.Reason(world_context, trajectory_info,
                              previous_iter_seed, mutable_current_iter_seed,
                              constraint_creator_ptr, debug_proto_ptr);
  }

  // For each ReasoningAgentInput, MakePredictionDecision
  PredictionDecisionMaker prediction_decision_maker;
  const std::vector<PredictionDecisionMakerOutput>
      prediction_decision_maker_outputs =
          prediction_decision_maker.MakeDecisions(
              world_context, trajectory_info, urgency_info, reasoning_inputs,
              pinch_region_mapper, config.reasoning(), debug_proto_ptr);

  // Run Agent Related Reasoners
  // CrossAgentReasoner
  {
    TRACE_EVENT_SCOPE(planner, CrossAgentReasoner_Speed);
    reasoner::CrossAgentReasoner cross_agent_reasoner;
    cross_agent_reasoner.Reason(world_context, trajectory_info, urgency_info,
                                prediction_decision_maker_outputs,
                                constraint_creator_ptr, honk_request_ptr,
                                debug_proto_ptr);
    if (FLAGS_planning_enable_discomfort_for_progress_in_upl &&
        urgency_info.ShouldSetDiscomfortForProgressInUPL()) {
      global_strategy_setter->set_global_discomfort_for_progress(0.5);
    }
    if (!trajectory_info.open_space_info().IsActive() &&
        trajectory_info.open_space_info().unstuck_scene_type !=
            planner::pb::UnstuckSceneType::UTURN &&
        urgency_info.HasUrgencyReason(
            planner::pb::UrgencyReason::BlockingUTurn)) {
      global_strategy_setter->set_global_discomfort_for_progress(0.5);
      global_strategy_setter->set_progress_for_kinematic(true);
    }
  }

  // LeadAndMergeReasoner
  {
    TRACE_EVENT_SCOPE(planner, LeadAndMergeAgentReasoner_Speed);
    VOY_LATENCY_STAT_RECORD_PLANNER_S2(
        LAT_STAT_LeadAndMergeAgentReasoner_Speed);
    reasoner::LeadAndMergeAgentReasoner lead_and_merge_agent_reasoner;
    lead_and_merge_agent_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        pinch_region_mapper, urgency_info,
        config.reasoning().reasoners().lead_and_merge_reasoner(),
        previous_iter_seed.speed_seed()
            .speed_reasoner_seeds()
            .lead_and_merge_reasoner_seed(),
        speed_pipeline_meta,
        *(mutable_current_iter_seed->mutable_lead_and_merge_reasoner_seed()),
        constraint_creator_ptr, reference_limiter_ptr, global_strategy_setter,
        honk_request_ptr, debug_proto_ptr);
  }

  // VRUReasoner
  {
    TRACE_EVENT_SCOPE(planner, VruReasoner_Speed);
    reasoner::VruReasoner vru_reasoner;
    vru_reasoner.Reason(world_context, trajectory_info, urgency_info,
                        prediction_decision_maker_outputs,
                        config.reasoning().reasoners().vru_reasoner(),
                        constraint_creator_ptr, honk_request_ptr,
                        debug_proto_ptr);
  }

  // CautiousDrivingReasoner
  {
    TRACE_EVENT_SCOPE(planner, CautiousDrivingReasoner_Speed);
    reasoner::CautiousDrivingReasoner cautious_driving_reasoner;
    cautious_driving_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        object_prediction_map,
        config.reasoning().reasoners().cautious_driving_reasoner(),
        config.reasoning().prediction_decision_maker(),
        config.reference().road_speed_limiter().target_speed_limit_patch(),
        previous_iter_seed.speed_seed()
            .speed_reasoner_seeds()
            .cautious_driving_reasoner_seed(),
        *(mutable_current_iter_seed->mutable_cautious_driving_reasoner_seed()),
        reasoning_policies, reference_limiter_ptr, constraint_creator_ptr,
        global_strategy_setter, ego_stuck_feature_about_speed, debug_proto_ptr);
  }

  // TailgaterReasoner
  {
    TRACE_EVENT_SCOPE(planner, TailgaterReasoner_Speed);
    reasoner::TailgaterReasoner tailgater_reasoner;
    tailgater_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        mutable_current_iter_seed->mutable_tailgater_reasoner_seed(),
        constraint_creator_ptr, hazard_light_request_ptr, debug_proto_ptr);
  }

  // Prepare Reasoning Inputs for hallucinated agents.
  std::map<ObjectId, speed::pb::OverlapComputationPolicy::Type>
      hallucinated_overlap_computation_policy_map =
          GetDefaultOverlapComputationPolicy(hallucinated_planner_object_map);
  const std::vector<PredictionDecisionMakerInput>
      hallucinated_reasoning_inputs = PrepareReasoningInputsInternal(
          world_context, trajectory_info, ego_intention,
          hallucinated_planner_object_map,
          hallucinated_overlap_computation_policy_map,
          hallucinated_object_prediction_map,
          /*optional_object_occupancy_param_map=*/std::nullopt,
          object_overlap_region_map, object_proximity_infos,
          hallucinated_obj_id_to_required_lat_gaps, previous_iter_seed, false,
          reasoning_input_debug);

  // For each ReasoningAgentInput, MakePredictionDecision for hallucinated
  // agents
  PredictionDecisionMaker hallucinated_prediction_decision_maker;
  const std::vector<PredictionDecisionMakerOutput>
      hallucinated_prediction_decision_maker_outputs =
          hallucinated_prediction_decision_maker.MakeDecisions(
              world_context, trajectory_info, urgency_info,
              hallucinated_reasoning_inputs, pinch_region_mapper,
              config.reasoning(), debug_proto_ptr);

  // OcclusionReasoner
  {
    TRACE_EVENT_SCOPE(planner, OcclusionReasoner_Speed);
    reasoner::OcclusionReasoner occlusion_reasoner;
    occlusion_reasoner.Reason(
        world_context, trajectory_info, occlusion_areas, reasoning_inputs,
        hallucinated_prediction_decision_maker_outputs,
        previous_iter_seed.selected_trajectory(),
        previous_iter_seed.speed_seed()
            .speed_reasoner_seeds()
            .occlusion_reasoner_seed(),
        *(mutable_current_iter_seed->mutable_occlusion_reasoner_seed()),
        constraint_creator_ptr, reference_limiter_ptr, debug_proto_ptr);
  }

  // HonkReasoner
  if (FLAGS_planning_enable_honk_reasoner) {
    TRACE_EVENT_SCOPE(planner, HonkReasoner_Speed);
    reasoner::HonkReasoner honk_reasoner;
    honk_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        hallucinated_prediction_decision_maker_outputs,
        config.reasoning().reasoners().honk_reasoner(), previous_iter_seed,
        mutable_current_iter_seed->honk_reasoner_seed(), honk_request_ptr,
        debug_proto_ptr);
  }

  // HazardLightReasoner
  {
    TRACE_EVENT_SCOPE(planner, HazardLightReasoner_Speed);
    reasoner::HazardLightReasoner hazard_light_reasoner;
    hazard_light_reasoner.Reason(world_context, trajectory_info, cz_ptr_map,
                                 hazard_light_request_ptr, debug_proto_ptr);
  }

  // ConstructionZoneReasoner
  {
    TRACE_EVENT_SCOPE(planner, ConstructionZoneReasoner_Speed);
    VOY_LATENCY_STAT_RECORD_PLANNER_S2(LAT_STAT_ConstructionZoneReasoner_Speed);
    reasoner::ConstructionZoneReasoner construction_zone_reasoner;
    construction_zone_reasoner.Reason(
        world_context, trajectory_info, ego_lateral_clearance_info,
        cz_id_to_required_lat_gaps, cz_overlap_region_map, cz_proximity_infos,
        cz_ptr_map, config.reasoning().reasoners().construction_zone_reasoner(),
        constraint_creator_ptr, reference_limiter_ptr, debug_proto_ptr);
  }

  // JaywalkerReasoner
  {
    TRACE_EVENT_SCOPE(planner, JaywalkerReasoner_Speed);
    reasoner::JaywalkerReasoner jaywalker_reasoner;
    jaywalker_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        constraint_creator_ptr, honk_request_ptr, debug_proto_ptr);
  }

  // OncomingAgentReasoner
  {
    TRACE_EVENT_SCOPE(planner, OncomingAgentReasoner_Speed);
    reasoner::OncomingAgentReasoner oncoming_agent_reasoner;
    oncoming_agent_reasoner.Reason(
        world_context, trajectory_info, prediction_decision_maker_outputs,
        ego_lateral_clearance_info, constraint_creator_ptr,
        reference_limiter_ptr, debug_proto_ptr);
  }

  // MrcStopReasoner
  {
    TRACE_EVENT_SCOPE(planner, MrcStopReasoner_Speed);
    reasoner::MrcStopReasoner mrc_stop_reasoner;
    mrc_stop_reasoner.Reason(world_context, trajectory_info,
                             config.for_car_type().limits(), previous_iter_seed,
                             mutable_current_iter_seed, reference_limiter_ptr,
                             constraint_creator_ptr, debug_proto_ptr);
  }

  if (FLAGS_planning_enable_decoupled_pull_out) {
    TRACE_EVENT_SCOPE(planner, PulloutReasoner_Speed);
    reasoner::PulloutReasoner pullout_reasoner;
    pullout_reasoner.Reason(world_context, trajectory_info, planner_object_map,
                            previous_iter_seed, mutable_current_iter_seed,
                            mutable_assist_requests, constraint_creator_ptr,
                            reference_limiter_ptr);
  }

  TRACE_EVENT_SCOPE(planner, PullOverPrepareReasoner_Speed);
  reasoner::PullOverPrepareReasoner pullover_prepare_reasoner;
  pullover_prepare_reasoner.Reason(
      world_context, trajectory_info, prediction_decision_maker_outputs,
      config.reasoning().nominal_model(),
      config.reasoning().reasoners().pullover_prepare_reasoner(),
      constraint_creator_ptr, global_strategy_setter, reference_limiter_ptr,
      debug_proto_ptr);

  // TODO(howardgao): move to lane change info for state management.
  // Do not execute LaneChangeSpeedReasoner in the special homotopy.
  if (!(trajectory_info.is_pull_out_jump_out() ||
        trajectory_info.is_pull_over_triggered() ||
        trajectory_info.IsReverseDriving())) {
    TRACE_EVENT_SCOPE(planner, LaneChangePreparationReasoner_Speed);
    reasoner::LaneChangeSpeedReasoner lane_change_speed_reasoner;
    lane_change_speed_reasoner.Reason(
        world_context, reasoning_inputs, trajectory_info, planner_object_map,
        previous_iter_seed, config.reasoning().nominal_model(),
        mutable_current_iter_seed->mutable_lane_change_speed_reasoner_seed(),
        constraint_creator_ptr, global_strategy_setter, reference_limiter_ptr,
        lane_change_gap_quality_evaluator_ptr, debug_proto_ptr);
  }

  const planner::pb::UnstuckPlannerState unstuck_planner_current_state =
      Seed::Access<token::UnstuckPlannerSeed>::GetMsg(SpeedLastFinishedFrame())
          ->current_state();
  if (unstuck_planner_current_state ==
          planner::pb::UnstuckPlannerState::EXECUTING ||
      unstuck_planner_current_state ==
          planner::pb::UnstuckPlannerState::ABORTING) {
    TRACE_EVENT_SCOPE(planner, UnstuckSpeedReasoner_Speed);
    reasoner::UnstuckSpeedReasoner unstuck_speed_reasoner;
    unstuck_speed_reasoner.Reason(world_context, trajectory_info,
                                  previous_iter_seed, constraint_creator_ptr);
  }

  // HoldReasoner.
  // NOTE: because the |HoldReasoner| needs the output of
  // |PredictionDecisionMaker| to determine whether constraints will be added
  // for some agents, it should be called after |PredictionDecisionMaker|.
  {
    TRACE_EVENT_SCOPE(planner, HoldReasoner_Speed);
    reasoner::HoldReasoner hold_reasoner;
    hold_reasoner.Reason(world_context, trajectory_info,
                         prediction_decision_maker_outputs,
                         config.reasoning().reasoners().hold_reasoner(),
                         config.for_car_type().limits(), constraint_creator_ptr,
                         debug_proto_ptr);
  }

  global_strategy_setter->set_globally_allow_emergency_brake(true);

  UpdateCurrentIterSpeedSeed(
      prediction_decision_maker_outputs, previous_iter_seed.speed_seed(),
      world_context.ego_pose_timestamp(), *mutable_current_iter_speed_seed);

  // Extracts features for assist stuck.
  assist_stuck::ExtractSpeedReasoning(
      trajectory_info, prediction_decision_maker_outputs,
      hallucinated_prediction_decision_maker_outputs,
      ego_stuck_feature_about_speed);
}
}  // namespace speed
}  // namespace planner
