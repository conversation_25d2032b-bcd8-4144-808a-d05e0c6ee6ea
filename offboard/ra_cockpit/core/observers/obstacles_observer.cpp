#include "obstacles_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

ObstaclesObserver::ObstaclesObserver(ObstaclesCallback callback)
    : callback_(std::move(callback)) {}

bool ObstaclesObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kObstaclesMessage);
}

std::shared_ptr<google::protobuf::Message> ObstaclesObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto obs_msg = std::make_shared<voy::Obstacles>();
  if (!obs_msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error Obstacles message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  if (callback_) {
    callback_(obs_msg, data_channel);
  }
  return obs_msg;
}

}  // namespace remote_assist
