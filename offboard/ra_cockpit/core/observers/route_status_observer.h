#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_STATUS_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_STATUS_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class RouteStatusObserver : public IDataObserver {
 public:
  using RouteStatusCallback = std::function<void(
      std::shared_ptr<teleassist::pb::RoutePath>, DataChannel)>;

  explicit RouteStatusObserver(RouteStatusCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  RouteStatusCallback callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_ROUTE_STATUS_OBSERVER_H_
