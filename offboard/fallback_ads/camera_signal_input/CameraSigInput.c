/*
 * File: CameraSigInput.c
 *
 * Code generated for Simulink model 'CameraSigInput'.
 *
 * Model version                  : 1.22
 * Simulink Coder version         : 9.5 (R2021a) 14-Nov-2020
 * C/C++ source code generated on : Thu Dec 12 16:01:01 2024
 *
 * Target selection: autosar.tlc
 * Embedded hardware selection: Infineon->TriCore
 * Code generation objectives: Unspecified
 * Validation result: Not run
 */

#include "CameraSigInput.h"
#include "CameraSigInput_private.h"

/* Exported data definition */

/* Definition for custom storage class: ExportToFile */
uint8 CSI_CalibrationStatus; /* '<S4>/Signal Conversion' */

/* - */
uint8 CSI_ColorTypeLf;                     /* '<S4>/Signal Conversion10' */
uint8 CSI_ColorTypeLfAdj;                  /* '<S4>/Signal Conversion39' */
uint8 CSI_ColorTypeLfRe;                   /* '<S4>/Signal Conversion49' */
uint8 CSI_ColorTypeRi;                     /* '<S4>/Signal Conversion27' */
uint8 CSI_ColorTypeRiAdj;                  /* '<S4>/Signal Conversion57' */
uint8 CSI_ColorTypeRiRe;                   /* '<S4>/Signal Conversion87' */
float32 CSI_CrvRateLf;                     /* '<S4>/Signal Conversion11' */
float32 CSI_CrvRateLfAdj;                  /* '<S4>/Signal Conversion40' */
float32 CSI_CrvRateLfRe;                   /* '<S4>/Signal Conversion37' */
float32 CSI_CrvRateRi;                     /* '<S4>/Signal Conversion28' */
float32 CSI_CrvRateRiAdj;                  /* '<S4>/Signal Conversion58' */
float32 CSI_CrvRateRiRe;                   /* '<S4>/Signal Conversion86' */
float32 CSI_CurvatureLf;                   /* '<S4>/Signal Conversion9' */
float32 CSI_CurvatureLfAdj;                /* '<S4>/Signal Conversion73' */
float32 CSI_CurvatureLfRe;                 /* '<S4>/Signal Conversion38' */
float32 CSI_CurvatureRi;                   /* '<S4>/Signal Conversion26' */
float32 CSI_CurvatureRiAdj;                /* '<S4>/Signal Conversion56' */
float32 CSI_CurvatureRiRe;                 /* '<S4>/Signal Conversion84' */
float32 CSI_ExistProbLf;                   /* '<S4>/Signal Conversion6' */
float32 CSI_ExistProbLfAdj;                /* '<S4>/Signal Conversion70' */
float32 CSI_ExistProbLfRe;                 /* '<S4>/Signal Conversion102' */
float32 CSI_ExistProbRi;                   /* '<S4>/Signal Conversion23' */
float32 CSI_ExistProbRiAdj;                /* '<S4>/Signal Conversion53' */
float32 CSI_ExistProbRiRe;                 /* '<S4>/Signal Conversion82' */
uint8 CSI_FC_CIPV_ID;                      /* '<S8>/Convert178' */
uint8 CSI_FC_FrontCameraCalibrationStatus; /* '<S12>/Convert176' */

/* - */
uint8 CSI_FC_LaneChangeStatus; /* '<S12>/Convert177' */

/* - */
float32 CSI_FC_LineTiStamp;          /* '<S6>/Convert34' */
float32 CSI_FC_Line_01_HeadingAngle; /* '<S6>/Convert8' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_01_Id; /* '<S6>/Convert9' */

/* Left Line of Host Lane */
uint8 CSI_FC_Line_01_Type;     /* '<S6>/Convert10' */
float32 CSI_FC_Line_01_Width;  /* '<S6>/Convert11' */
uint8 CSI_FC_Line_01_color;    /* '<S6>/Convert1' */
float32 CSI_FC_Line_01_dx_End; /* '<S6>/Convert2' */

/* End of Effective visual range */
float32 CSI_FC_Line_01_dx_End_std; /* '<S6>/Convert3' */
float32 CSI_FC_Line_01_dx_Start;   /* '<S6>/Convert4' */

/* Start of Effective visual range */
float32 CSI_FC_Line_01_dx_Start_std; /* '<S6>/Convert5' */
float32 CSI_FC_Line_01_dy;           /* '<S6>/Convert6' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_FC_Line_01_exist_prob;   /* '<S6>/Convert7' */
float32 CSI_FC_Line_02_HeadingAngle; /* '<S6>/Convert19' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_02_Id; /* '<S6>/Convert20' */

/* Right Line of Host Lane */
uint8 CSI_FC_Line_02_Type;     /* '<S6>/Convert21' */
float32 CSI_FC_Line_02_Width;  /* '<S6>/Convert22' */
uint8 CSI_FC_Line_02_color;    /* '<S6>/Convert12' */
float32 CSI_FC_Line_02_dx_End; /* '<S6>/Convert13' */

/* End of Effective visual range */
float32 CSI_FC_Line_02_dx_End_std; /* '<S6>/Convert14' */
float32 CSI_FC_Line_02_dx_Start;   /* '<S6>/Convert15' */

/* Start of Effective visual range */
float32 CSI_FC_Line_02_dx_Start_std; /* '<S6>/Convert16' */
float32 CSI_FC_Line_02_dy;           /* '<S6>/Convert17' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
float32 CSI_FC_Line_02_exist_prob;   /* '<S6>/Convert18' */
float32 CSI_FC_Line_03_HeadingAngle; /* '<S6>/Convert30' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_03_Id; /* '<S6>/Convert31' */

/* Left Line of Host Lane */
uint8 CSI_FC_Line_03_Type;     /* '<S6>/Convert48' */
float32 CSI_FC_Line_03_Width;  /* '<S6>/Convert49' */
uint8 CSI_FC_Line_03_color;    /* '<S6>/Convert23' */
float32 CSI_FC_Line_03_dx_End; /* '<S6>/Convert24' */

/* End of Effective visual range */
float32 CSI_FC_Line_03_dx_End_std; /* '<S6>/Convert25' */
float32 CSI_FC_Line_03_dx_Start;   /* '<S6>/Convert26' */

/* Start of Effective visual range */
float32 CSI_FC_Line_03_dx_Start_std; /* '<S6>/Convert27' */
float32 CSI_FC_Line_03_dy;           /* '<S6>/Convert28' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_FC_Line_03_exist_prob;   /* '<S6>/Convert29' */
float32 CSI_FC_Line_04_HeadingAngle; /* '<S7>/Convert34' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_04_Id; /* '<S7>/Convert35' */

/* Left Line of Host Lane */
uint8 CSI_FC_Line_04_Type;     /* '<S7>/Convert36' */
float32 CSI_FC_Line_04_Width;  /* '<S7>/Convert37' */
uint8 CSI_FC_Line_04_color;    /* '<S7>/Convert17' */
float32 CSI_FC_Line_04_dx_End; /* '<S7>/Convert18' */

/* End of Effective visual range */
float32 CSI_FC_Line_04_dx_End_std; /* '<S7>/Convert19' */
float32 CSI_FC_Line_04_dx_Start;   /* '<S7>/Convert20' */

/* Start of Effective visual range */
float32 CSI_FC_Line_04_dx_Start_std; /* '<S7>/Convert21' */
float32 CSI_FC_Line_04_dy;           /* '<S7>/Convert22' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_FC_Line_04_exist_prob;   /* '<S7>/Convert31' */
float32 CSI_FC_Line_05_HeadingAngle; /* '<S7>/Convert8' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_05_Type;     /* '<S7>/Convert15' */
float32 CSI_FC_Line_05_Width;  /* '<S7>/Convert16' */
uint8 CSI_FC_Line_05_color;    /* '<S7>/Convert1' */
float32 CSI_FC_Line_05_dx_End; /* '<S7>/Convert2' */

/* End of Effective visual range */
float32 CSI_FC_Line_05_dx_End_std; /* '<S7>/Convert3' */
float32 CSI_FC_Line_05_dx_Start;   /* '<S7>/Convert4' */

/* Start of Effective visual range */
float32 CSI_FC_Line_05_dx_Start_std; /* '<S7>/Convert5' */
float32 CSI_FC_Line_05_dy;           /* '<S7>/Convert6' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_FC_Line_05_exist_prob;   /* '<S7>/Convert7' */
float32 CSI_FC_Line_06_HeadingAngle; /* '<S7>/Convert30' */

/* Conterclolkwise is Positive */
uint8 CSI_FC_Line_06_Type;     /* '<S7>/Convert48' */
float32 CSI_FC_Line_06_Width;  /* '<S7>/Convert49' */
uint8 CSI_FC_Line_06_color;    /* '<S7>/Convert23' */
float32 CSI_FC_Line_06_dx_End; /* '<S7>/Convert24' */

/* End of Effective visual range */
float32 CSI_FC_Line_06_dx_End_std; /* '<S7>/Convert25' */
float32 CSI_FC_Line_06_dx_Start;   /* '<S7>/Convert26' */

/* Start of Effective visual range */
float32 CSI_FC_Line_06_dx_Start_std; /* '<S7>/Convert27' */
float32 CSI_FC_Line_06_dy;           /* '<S7>/Convert28' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_FC_Line_06_exist_prob;         /* '<S7>/Convert29' */
uint8 CSI_FTFC_Line_01_MeasureType;        /* '<S6>/Convert37' */
uint8 CSI_FTFC_Line_01_ObstacleFlg;        /* '<S6>/Convert38' */
uint8 CSI_FTFC_Line_01_ParseConf;          /* '<S6>/Convert39' */
uint8 CSI_FTFC_Line_01_RMSE;               /* '<S6>/Convert40' */
float32 CSI_FTFC_Line_01_curvature_alte;   /* '<S6>/Convert35' */
float32 CSI_FTFC_Line_01_curve;            /* '<S6>/Convert36' */
uint8 CSI_FTFC_Line_02_MeasureType;        /* '<S6>/Convert43' */
uint8 CSI_FTFC_Line_02_ObstacleFlg;        /* '<S6>/Convert44' */
uint8 CSI_FTFC_Line_02_ParseConf;          /* '<S6>/Convert45' */
uint8 CSI_FTFC_Line_02_RMSE;               /* '<S6>/Convert46' */
float32 CSI_FTFC_Line_02_curvature_alte;   /* '<S6>/Convert41' */
float32 CSI_FTFC_Line_02_curve;            /* '<S6>/Convert42' */
uint8 CSI_FTFC_Line_03_MeasureType;        /* '<S6>/Convert32' */
uint8 CSI_FTFC_Line_03_ObstacleFlg;        /* '<S7>/Convert50' */
uint8 CSI_FTFC_Line_03_ParseConf;          /* '<S7>/Convert51' */
uint8 CSI_FTFC_Line_03_RMSE;               /* '<S7>/Convert52' */
float32 CSI_FTFC_Line_03_curvature_alte;   /* '<S6>/Convert33' */
float32 CSI_FTFC_Line_03_curve;            /* '<S6>/Convert47' */
uint8 CSI_FTFC_Line_04_MeasureType;        /* '<S7>/Convert43' */
uint8 CSI_FTFC_Line_04_ObstacleFlg;        /* '<S7>/Convert38' */
uint8 CSI_FTFC_Line_04_ParseConf;          /* '<S7>/Convert39' */
uint8 CSI_FTFC_Line_04_RMSE;               /* '<S7>/Convert40' */
float32 CSI_FTFC_Line_04_curvature_alte;   /* '<S7>/Convert41' */
float32 CSI_FTFC_Line_04_curve;            /* '<S7>/Convert42' */
uint8 CSI_FTFC_Line_05_MeasureType;        /* '<S7>/Convert9' */
uint8 CSI_FTFC_Line_05_ObstacleFlg;        /* '<S7>/Convert11' */
uint8 CSI_FTFC_Line_05_ParseConf;          /* '<S7>/Convert12' */
uint8 CSI_FTFC_Line_05_RMSE;               /* '<S7>/Convert13' */
float32 CSI_FTFC_Line_05_curvature_alte;   /* '<S7>/Convert10' */
float32 CSI_FTFC_Line_05_curve;            /* '<S7>/Convert14' */
uint8 CSI_FTFC_Line_06_MeasureType;        /* '<S7>/Convert32' */
uint8 CSI_FTFC_Line_06_ObstacleFlg;        /* '<S7>/Convert44' */
uint8 CSI_FTFC_Line_06_ParseConf;          /* '<S7>/Convert45' */
uint8 CSI_FTFC_Line_06_RMSE;               /* '<S7>/Convert46' */
float32 CSI_FTFC_Line_06_curvature_alte;   /* '<S7>/Convert33' */
float32 CSI_FTFC_Line_06_curve;            /* '<S7>/Convert47' */
float32 CSI_FrFr_AccOBJ_Ax;                /* '<S9>/Convert118' */
float32 CSI_FrFr_AccOBJ_Ay;                /* '<S9>/Convert119' */
uint8 CSI_FrFr_AccOBJ_Brakelight_Info;     /* '<S9>/Convert120' */
uint8 CSI_FrFr_AccOBJ_Class;               /* '<S8>/Convert116' */
float32 CSI_FrFr_AccOBJ_Dx;                /* '<S9>/Convert121' */
float32 CSI_FrFr_AccOBJ_Dx_Vnce;           /* '<S9>/Convert215' */
float32 CSI_FrFr_AccOBJ_Dy;                /* '<S9>/Convert122' */
float32 CSI_FrFr_AccOBJ_Dy_Vnce;           /* '<S9>/Convert216' */
float32 CSI_FrFr_AccOBJ_ExistProb;         /* '<S9>/Convert123' */
uint8 CSI_FrFr_AccOBJ_FusionedFC_Track_ID; /* '<S9>/Convert218' */

/* It is the track ID of FC that fusioned with FR ACC FrFr target */
float32 CSI_FrFr_AccOBJ_HeadingAngle;    /* '<S9>/Convert124' */
float32 CSI_FrFr_AccOBJ_Height;          /* '<S9>/Convert219' */
float32 CSI_FrFr_AccOBJ_Length;          /* '<S9>/Convert220' */
float32 CSI_FrFr_AccOBJ_ObstacleProb;    /* '<S9>/Convert125' */
uint8 CSI_FrFr_AccOBJ_Taillight_Info;    /* '<S9>/Convert221' */
uint8 CSI_FrFr_AccOBJ_Track_Age;         /* '<S9>/Convert222' */
uint8 CSI_FrFr_AccOBJ_Track_ID;          /* '<S9>/Convert223' */
float32 CSI_FrFr_AccOBJ_Vx;              /* '<S9>/Convert126' */
float32 CSI_FrFr_AccOBJ_Vx_std;          /* '<S9>/Convert224' */
float32 CSI_FrFr_AccOBJ_Vy;              /* '<S9>/Convert127' */
float32 CSI_FrFr_AccOBJ_Vy_std;          /* '<S9>/Convert225' */
float32 CSI_FrFr_AccOBJ_Width;           /* '<S8>/Convert117' */
uint8 CSI_FrFr_AccOBJ_confi;             /* '<S11>/Convert265' */
uint8 CSI_FrFr_AccOBJ_fusion_Sts;        /* '<S9>/Convert217' */
float32 CSI_Fr_AccOBJ_Ax;                /* '<S8>/Convert102' */
float32 CSI_Fr_AccOBJ_Ay;                /* '<S8>/Convert103' */
uint8 CSI_Fr_AccOBJ_Brakelight_Info;     /* '<S8>/Convert104' */
uint8 CSI_Fr_AccOBJ_Class;               /* '<S8>/Convert105' */
float32 CSI_Fr_AccOBJ_Dx;                /* '<S8>/Convert106' */
float32 CSI_Fr_AccOBJ_Dx_Vnce;           /* '<S8>/Convert200' */
float32 CSI_Fr_AccOBJ_Dy;                /* '<S8>/Convert107' */
float32 CSI_Fr_AccOBJ_Dy_Vnce;           /* '<S8>/Convert201' */
float32 CSI_Fr_AccOBJ_ExistProb;         /* '<S8>/Convert108' */
uint8 CSI_Fr_AccOBJ_FusionedFC_Track_ID; /* '<S8>/Convert203' */

/* It is the track ID of FC that fusioned with FR ACC Fr target */
float32 CSI_Fr_AccOBJ_HeadingAngle; /* '<S8>/Convert109' */
float32 CSI_Fr_AccOBJ_Height;       /* '<S8>/Convert110' */
float32 CSI_Fr_AccOBJ_Length;       /* '<S8>/Convert111' */
float32 CSI_Fr_AccOBJ_ObstacleProb; /* '<S8>/Convert112' */
uint8 CSI_Fr_AccOBJ_Taillight_Info; /* '<S8>/Convert221' */
uint8 CSI_Fr_AccOBJ_Track_Age;      /* '<S8>/Convert205' */
uint8 CSI_Fr_AccOBJ_Track_ID;       /* '<S8>/Convert206' */
float32 CSI_Fr_AccOBJ_Vx;           /* '<S8>/Convert113' */
float32 CSI_Fr_AccOBJ_Vx_std;       /* '<S8>/Convert207' */
float32 CSI_Fr_AccOBJ_Vy;           /* '<S8>/Convert114' */
float32 CSI_Fr_AccOBJ_Vy_std;       /* '<S8>/Convert208' */
float32 CSI_Fr_AccOBJ_Width;        /* '<S8>/Convert115' */
uint8 CSI_Fr_AccOBJ_confi;          /* '<S11>/Convert264' */
uint8 CSI_Fr_AccOBJ_fusion_Sts;     /* '<S8>/Convert202' */
float32 CSI_HeadingAngleLf;         /* '<S4>/Signal Conversion8' */

/* Conterclolkwise is Positive */
float32 CSI_HeadingAngleLfAdj; /* '<S4>/Signal Conversion72' */

/* Conterclolkwise is Positive */
float32 CSI_HeadingAngleLfRe; /* '<S4>/Signal Conversion104' */

/* Conterclolkwise is Positive */
float32 CSI_HeadingAngleRi; /* '<S4>/Signal Conversion25' */

/* Conterclolkwise is Positive */
float32 CSI_HeadingAngleRiAdj; /* '<S4>/Signal Conversion55' */

/* Conterclolkwise is Positive */
float32 CSI_HeadingAngleRiRe; /* '<S4>/Signal Conversion85' */

/* Conterclolkwise is Positive */
uint8 CSI_LaneChangeStatus; /* '<S4>/Signal Conversion1' */

/* - */
uint8 CSI_LaneIDLf; /* '<S4>/Signal Conversion4' */

/* Left Line of Host Lane */
uint8 CSI_LaneIDLfAdj; /* '<S4>/Signal Conversion68' */

/* Left Line of Host Lane */
uint8 CSI_LaneIDRi; /* '<S4>/Signal Conversion20' */

/* Right Line of Host Lane */
uint8 CSI_LaneIDRiAdj; /* '<S4>/Signal Conversion50' */

/* Right Line of Host Lane */
float32 CSI_LaneTimeStamp;                 /* '<S4>/Signal Conversion2' */
float32 CSI_LeFr_AccOBJ_Ax;                /* '<S10>/Convert144' */
float32 CSI_LeFr_AccOBJ_Ay;                /* '<S10>/Convert145' */
uint8 CSI_LeFr_AccOBJ_Brakelight_Info;     /* '<S10>/Convert146' */
uint8 CSI_LeFr_AccOBJ_Class;               /* '<S9>/Convert140' */
float32 CSI_LeFr_AccOBJ_Dx;                /* '<S10>/Convert147' */
float32 CSI_LeFr_AccOBJ_Dx_Vnce;           /* '<S10>/Convert240' */
float32 CSI_LeFr_AccOBJ_Dy;                /* '<S9>/Convert141' */
float32 CSI_LeFr_AccOBJ_Dy_Vnce;           /* '<S9>/Convert237' */
float32 CSI_LeFr_AccOBJ_ExistProb;         /* '<S10>/Convert148' */
uint8 CSI_LeFr_AccOBJ_FusionedFC_Track_ID; /* '<S10>/Convert242' */

/* It is the track ID of FC that fusioned with FR ACC LeFr target */
float32 CSI_LeFr_AccOBJ_HeadingAngle;    /* '<S9>/Convert142' */
float32 CSI_LeFr_AccOBJ_Height;          /* '<S9>/Convert238' */
float32 CSI_LeFr_AccOBJ_Length;          /* '<S9>/Convert239' */
float32 CSI_LeFr_AccOBJ_ObstacleProb;    /* '<S10>/Convert149' */
uint8 CSI_LeFr_AccOBJ_Taillight_Info;    /* '<S10>/Convert243' */
uint8 CSI_LeFr_AccOBJ_Track_Age;         /* '<S10>/Convert244' */
uint8 CSI_LeFr_AccOBJ_Track_ID;          /* '<S10>/Convert245' */
float32 CSI_LeFr_AccOBJ_Vx;              /* '<S10>/Convert150' */
float32 CSI_LeFr_AccOBJ_Vx_std;          /* '<S10>/Convert246' */
float32 CSI_LeFr_AccOBJ_Vy;              /* '<S10>/Convert151' */
float32 CSI_LeFr_AccOBJ_Vy_std;          /* '<S10>/Convert247' */
float32 CSI_LeFr_AccOBJ_Width;           /* '<S9>/Convert143' */
uint8 CSI_LeFr_AccOBJ_confi;             /* '<S11>/Convert267' */
uint8 CSI_LeFr_AccOBJ_fusion_Sts;        /* '<S10>/Convert241' */
float32 CSI_Le_AccOBJ_Ax;                /* '<S9>/Convert128' */
float32 CSI_Le_AccOBJ_Ay;                /* '<S9>/Convert129' */
uint8 CSI_Le_AccOBJ_Brakelight_Info;     /* '<S9>/Convert130' */
uint8 CSI_Le_AccOBJ_Class;               /* '<S9>/Convert131' */
float32 CSI_Le_AccOBJ_Dx;                /* '<S9>/Convert132' */
float32 CSI_Le_AccOBJ_Dx_Vnce;           /* '<S9>/Convert226' */
float32 CSI_Le_AccOBJ_Dy;                /* '<S9>/Convert133' */
float32 CSI_Le_AccOBJ_Dy_Vnce;           /* '<S9>/Convert227' */
float32 CSI_Le_AccOBJ_ExistProb;         /* '<S9>/Convert134' */
uint8 CSI_Le_AccOBJ_FusionedFC_Track_ID; /* '<S9>/Convert229' */

/* It is the track ID of FC that fusioned with FR ACC Le target */
float32 CSI_Le_AccOBJ_HeadingAngle; /* '<S9>/Convert135' */
float32 CSI_Le_AccOBJ_Height;       /* '<S9>/Convert230' */
float32 CSI_Le_AccOBJ_Length;       /* '<S9>/Convert231' */
float32 CSI_Le_AccOBJ_ObstacleProb; /* '<S9>/Convert136' */
uint8 CSI_Le_AccOBJ_Taillight_Info; /* '<S9>/Convert232' */
uint8 CSI_Le_AccOBJ_Track_Age;      /* '<S9>/Convert233' */
uint8 CSI_Le_AccOBJ_Track_ID;       /* '<S9>/Convert234' */
float32 CSI_Le_AccOBJ_Vx;           /* '<S9>/Convert137' */
float32 CSI_Le_AccOBJ_Vx_std;       /* '<S9>/Convert235' */
float32 CSI_Le_AccOBJ_Vy;           /* '<S9>/Convert138' */
float32 CSI_Le_AccOBJ_Vy_std;       /* '<S9>/Convert236' */
float32 CSI_Le_AccOBJ_Width;        /* '<S9>/Convert139' */
uint8 CSI_Le_AccOBJ_confi;          /* '<S11>/Convert266' */
uint8 CSI_Le_AccOBJ_fusion_Sts;     /* '<S9>/Convert228' */
float32 CSI_MakerWidthLf;           /* '<S4>/Signal Conversion12' */
float32 CSI_MakerWidthLfAdj;        /* '<S4>/Signal Conversion41' */
float32 CSI_MakerWidthLfRe;         /* '<S4>/Signal Conversion74' */
float32 CSI_MakerWidthRi;           /* '<S4>/Signal Conversion29' */
float32 CSI_MakerWidthRiAdj;        /* '<S4>/Signal Conversion59' */
float32 CSI_MakerWidthRiRe;         /* '<S4>/Signal Conversion88' */
uint8 CSI_MarkerTypeLf;             /* '<S4>/Signal Conversion3' */
uint8 CSI_MarkerTypeLfAdj;          /* '<S4>/Signal Conversion60' */
uint8 CSI_MarkerTypeLfRe;           /* '<S4>/Signal Conversion96' */
uint8 CSI_MarkerTypeRi;             /* '<S4>/Signal Conversion22' */
uint8 CSI_MarkerTypeRiAdj;          /* '<S4>/Signal Conversion52' */
uint8 CSI_MarkerTypeRiRe;           /* '<S4>/Signal Conversion80' */
uint8 CSI_MeasureTypeLf;            /* '<S4>/Signal Conversion5' */
uint8 CSI_MeasureTypeLfAdj;         /* '<S4>/Signal Conversion69' */
uint8 CSI_MeasureTypeLfRe;          /* '<S4>/Signal Conversion101' */
uint8 CSI_MeasureTypeRi;            /* '<S4>/Signal Conversion21' */
uint8 CSI_MeasureTypeRiAdj;         /* '<S4>/Signal Conversion51' */
uint8 CSI_MeasureTypeRiRe;          /* '<S4>/Signal Conversion81' */
uint8 CSI_ObstacleFlgLf;            /* '<S4>/Signal Conversion18' */
uint8 CSI_ObstacleFlgLfAdj;         /* '<S4>/Signal Conversion47' */
uint8 CSI_ObstacleFlgLfRe;          /* '<S4>/Signal Conversion91' */
uint8 CSI_ObstacleFlgRi;            /* '<S4>/Signal Conversion36' */
uint8 CSI_ObstacleFlgRiAdj;         /* '<S4>/Signal Conversion67' */
uint8 CSI_ObstacleFlgRiRe;          /* '<S4>/Signal Conversion93' */
uint8 CSI_ParseConfLf;              /* '<S4>/Signal Conversion17' */
uint8 CSI_ParseConfLfAdj;           /* '<S4>/Signal Conversion46' */
uint8 CSI_ParseConfLfRe;            /* '<S4>/Signal Conversion99' */
uint8 CSI_ParseConfRi;              /* '<S4>/Signal Conversion34' */
uint8 CSI_ParseConfRiAdj;           /* '<S4>/Signal Conversion65' */
uint8 CSI_ParseConfRiRe;            /* '<S4>/Signal Conversion94' */
uint8 CSI_PolyfitRmseLf;            /* '<S4>/Signal Conversion19' */
uint8 CSI_PolyfitRmseLfAdj;         /* '<S4>/Signal Conversion48' */
uint8 CSI_PolyfitRmseLfRe;          /* '<S4>/Signal Conversion100' */
uint8 CSI_PolyfitRmseRi;            /* '<S4>/Signal Conversion35' */
uint8 CSI_PolyfitRmseRiAdj;         /* '<S4>/Signal Conversion66' */
uint8 CSI_PolyfitRmseRiRe;          /* '<S4>/Signal Conversion78' */
float32 CSI_PosXEndLf;              /* '<S4>/Signal Conversion15' */

/* End of Effective visual range */
float32 CSI_PosXEndLfAdj; /* '<S4>/Signal Conversion44' */

/* End of Effective visual range */
float32 CSI_PosXEndLfRe; /* '<S4>/Signal Conversion76' */

/* End of Effective visual range */
float32 CSI_PosXEndRi; /* '<S4>/Signal Conversion32' */

/* End of Effective visual range */
float32 CSI_PosXEndRiAdj; /* '<S4>/Signal Conversion63' */

/* End of Effective visual range */
float32 CSI_PosXEndRiRe; /* '<S4>/Signal Conversion90' */

/* End of Effective visual range */
float32 CSI_PosXEndStdLf;    /* '<S4>/Signal Conversion16' */
float32 CSI_PosXEndStdLfAdj; /* '<S4>/Signal Conversion45' */
float32 CSI_PosXEndStdLfRe;  /* '<S4>/Signal Conversion77' */
float32 CSI_PosXEndStdRi;    /* '<S4>/Signal Conversion33' */
float32 CSI_PosXEndStdRiAdj; /* '<S4>/Signal Conversion64' */
float32 CSI_PosXEndStdRiRe;  /* '<S4>/Signal Conversion95' */
float32 CSI_PosXStartLf;     /* '<S4>/Signal Conversion13' */
float32 CSI_PosXStartLfAdj;  /* '<S4>/Signal Conversion42' */
float32 CSI_PosXStartLfRe;   /* '<S4>/Signal Conversion75' */
float32 CSI_PosXStartRi;     /* '<S4>/Signal Conversion30' */

/* Start of Effective visual range */
float32 CSI_PosXStartRiAdj; /* '<S4>/Signal Conversion61' */

/* Start of Effective visual range */
float32 CSI_PosXStartRiRe; /* '<S4>/Signal Conversion92' */

/* Start of Effective visual range */
float32 CSI_PosY0Lf; /* '<S4>/Signal Conversion7' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_PosY0LfAdj; /* '<S4>/Signal Conversion71' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_PosY0LfRe; /* '<S4>/Signal Conversion103' */

/* dy of Left Side line is positive,dy of Right Side line is negative */
float32 CSI_PosY0Ri; /* '<S4>/Signal Conversion24' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
float32 CSI_PosY0RiAdj; /* '<S4>/Signal Conversion54' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
float32 CSI_PosY0RiRe; /* '<S4>/Signal Conversion83' */

/* dy of Left Side line is positive, dy of Right Side line is negative */
float32 CSI_PosYStartStdLf; /* '<S4>/Signal Conversion14' */

/* Start of Effective visual range */
float32 CSI_PosYStartStdLfAdj; /* '<S4>/Signal Conversion43' */

/* Start of Effective visual range */
float32 CSI_PosYStartStdLfRe; /* '<S4>/Signal Conversion79' */

/* Start of Effective visual range */
float32 CSI_PosYStartStdRi;                /* '<S4>/Signal Conversion31' */
float32 CSI_PosYStartStdRiAdj;             /* '<S4>/Signal Conversion62' */
float32 CSI_PosYStartStdRiRe;              /* '<S4>/Signal Conversion89' */
float32 CSI_RiFr_AccOBJ_Ax;                /* '<S11>/Convert170' */
float32 CSI_RiFr_AccOBJ_Ay;                /* '<S11>/Convert171' */
uint8 CSI_RiFr_AccOBJ_Brakelight_Info;     /* '<S11>/Convert172' */
uint8 CSI_RiFr_AccOBJ_Class;               /* '<S10>/Convert164' */
float32 CSI_RiFr_AccOBJ_Dx;                /* '<S10>/Convert165' */
float32 CSI_RiFr_AccOBJ_Dx_Vnce;           /* '<S10>/Convert259' */
float32 CSI_RiFr_AccOBJ_Dy;                /* '<S10>/Convert166' */
float32 CSI_RiFr_AccOBJ_Dy_Vnce;           /* '<S10>/Convert260' */
float32 CSI_RiFr_AccOBJ_ExistProb;         /* '<S11>/Convert173' */
uint8 CSI_RiFr_AccOBJ_FusionedFC_Track_ID; /* '<S11>/Convert271' */

/* It is the track ID of FC that fusioned with FR ACC RiFr target */
float32 CSI_RiFr_AccOBJ_HeadingAngle;    /* '<S10>/Convert167' */
float32 CSI_RiFr_AccOBJ_Height;          /* '<S10>/Convert261' */
float32 CSI_RiFr_AccOBJ_Length;          /* '<S10>/Convert262' */
float32 CSI_RiFr_AccOBJ_ObstacleProb;    /* '<S11>/Convert174' */
uint8 CSI_RiFr_AccOBJ_Taillight_Info;    /* '<S11>/Convert272' */
uint8 CSI_RiFr_AccOBJ_Track_Age;         /* '<S11>/Convert273' */
uint8 CSI_RiFr_AccOBJ_Track_ID;          /* '<S11>/Convert274' */
float32 CSI_RiFr_AccOBJ_Vx;              /* '<S11>/Convert175' */
float32 CSI_RiFr_AccOBJ_Vx_std;          /* '<S11>/Convert275' */
float32 CSI_RiFr_AccOBJ_Vy;              /* '<S10>/Convert168' */
float32 CSI_RiFr_AccOBJ_Vy_std;          /* '<S10>/Convert263' */
float32 CSI_RiFr_AccOBJ_Width;           /* '<S10>/Convert169' */
uint8 CSI_RiFr_AccOBJ_confi;             /* '<S11>/Convert269' */
uint8 CSI_RiFr_AccOBJ_fusion_Sts;        /* '<S11>/Convert270' */
float32 CSI_Ri_AccOBJ_Ax;                /* '<S10>/Convert152' */
float32 CSI_Ri_AccOBJ_Ay;                /* '<S10>/Convert153' */
uint8 CSI_Ri_AccOBJ_Brakelight_Info;     /* '<S10>/Convert154' */
uint8 CSI_Ri_AccOBJ_Class;               /* '<S10>/Convert155' */
float32 CSI_Ri_AccOBJ_Dx;                /* '<S10>/Convert156' */
float32 CSI_Ri_AccOBJ_Dx_Vnce;           /* '<S10>/Convert248' */
float32 CSI_Ri_AccOBJ_Dy;                /* '<S10>/Convert157' */
float32 CSI_Ri_AccOBJ_Dy_Vnce;           /* '<S10>/Convert249' */
float32 CSI_Ri_AccOBJ_ExistProb;         /* '<S10>/Convert158' */
uint8 CSI_Ri_AccOBJ_FusionedFC_Track_ID; /* '<S10>/Convert251' */

/* It is the track ID of FC that fusioned with FR ACC Ri target */
float32 CSI_Ri_AccOBJ_HeadingAngle; /* '<S10>/Convert159' */
float32 CSI_Ri_AccOBJ_Height;       /* '<S10>/Convert252' */
float32 CSI_Ri_AccOBJ_Length;       /* '<S10>/Convert253' */
float32 CSI_Ri_AccOBJ_ObstacleProb; /* '<S10>/Convert160' */
uint8 CSI_Ri_AccOBJ_Taillight_Info; /* '<S10>/Convert254' */
uint8 CSI_Ri_AccOBJ_Track_Age;      /* '<S10>/Convert255' */
uint8 CSI_Ri_AccOBJ_Track_ID;       /* '<S10>/Convert256' */
float32 CSI_Ri_AccOBJ_Vx;           /* '<S10>/Convert161' */
float32 CSI_Ri_AccOBJ_Vx_std;       /* '<S10>/Convert257' */
float32 CSI_Ri_AccOBJ_Vy;           /* '<S10>/Convert162' */
float32 CSI_Ri_AccOBJ_Vy_std;       /* '<S10>/Convert258' */
float32 CSI_Ri_AccOBJ_Width;        /* '<S10>/Convert163' */
uint8 CSI_Ri_AccOBJ_confi;          /* '<S11>/Convert268' */
uint8 CSI_Ri_AccOBJ_fusion_Sts;     /* '<S10>/Convert250' */

/* Model step function for TID1 */
void CameraSigInput_10ms_Runnable(void)
/* Explicit Task: CameraSigInput_10ms_Runnable */
{
  CSI_LaneInfo_Struct CSI_LaneInfo;
  CSI_ObjectInfo_Struct CSI_ObjectInfo;

  /* RootInportFunctionCallGenerator generated from:
   * '<Root>/CameraSigInput_10ms_Runnable' incorporates: SubSystem:
   * '<Root>/CameraSigInput_10ms'
   */
  /* DataTypeConversion: '<S6>/Convert25' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_dx_End_std'
   */
  CSI_FC_Line_03_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert24' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_dx_End'
   */
  CSI_FC_Line_03_dx_End =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_End() *
      0.25F;

  /* DataTypeConversion: '<S6>/Convert27' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_dx_Start_std'
   */
  CSI_FC_Line_03_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert26' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_dx_Start'
   */
  CSI_FC_Line_03_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S6>/Convert49' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_Width'
   */
  CSI_FC_Line_03_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Width() *
      0.01F;

  /* DataTypeConversion: '<S6>/Convert23' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_color'
   */
  CSI_FC_Line_03_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_color();

  /* DataTypeConversion: '<S6>/Convert33' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_03_curvature_alte'
   */
  CSI_FTFC_Line_03_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S6>/Convert47' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_03_curve'
   */
  CSI_FTFC_Line_03_curve =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_curve() *
          1.90735E-6F -
      0.015625F;

  /* DataTypeConversion: '<S6>/Convert30' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_HeadingAngle'
   */
  CSI_FC_Line_03_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S6>/Convert28' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_dy'
   */
  CSI_FC_Line_03_dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S6>/Convert29' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_exist_prob'
   */
  CSI_FC_Line_03_exist_prob =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_exist_prob() *
      2.0F;

  /* DataTypeConversion: '<S6>/Convert32' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_03_MeasureType'
   */
  CSI_FTFC_Line_03_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_03_MeasureType();

  /* DataTypeConversion: '<S6>/Convert48' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_Type'
   */
  CSI_FC_Line_03_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Type();

  /* DataTypeConversion: '<S6>/Convert31' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_03_Id'
   */
  CSI_FC_Line_03_Id =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_03_Id();

  /* DataTypeConversion: '<S6>/Convert44' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_ObstacleFlg'
   */
  CSI_FTFC_Line_02_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_ObstacleFlg();

  /* DataTypeConversion: '<S6>/Convert46' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_RMSE'
   */
  CSI_FTFC_Line_02_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_RMSE();

  /* DataTypeConversion: '<S6>/Convert45' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_ParseConf'
   */
  CSI_FTFC_Line_02_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_ParseConf();

  /* DataTypeConversion: '<S6>/Convert14' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_dx_End_std'
   */
  CSI_FC_Line_02_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert13' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_dx_End'
   */
  CSI_FC_Line_02_dx_End =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_End() *
      0.25F;

  /* DataTypeConversion: '<S6>/Convert16' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_dx_Start_std'
   */
  CSI_FC_Line_02_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert15' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_dx_Start'
   */
  CSI_FC_Line_02_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S6>/Convert22' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_Width'
   */
  CSI_FC_Line_02_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Width() *
      0.01F;

  /* DataTypeConversion: '<S6>/Convert12' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_color'
   */
  CSI_FC_Line_02_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_color();

  /* DataTypeConversion: '<S6>/Convert41' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_curvature_alte'
   */
  CSI_FTFC_Line_02_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S6>/Convert42' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_curve'
   */
  CSI_FTFC_Line_02_curve =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_curve() *
          1.90735E-6F -
      0.015625F;

  /* DataTypeConversion: '<S6>/Convert19' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_HeadingAngle'
   */
  CSI_FC_Line_02_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S6>/Convert17' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_dy'
   */
  CSI_FC_Line_02_dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S6>/Convert18' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_exist_prob'
   */
  CSI_FC_Line_02_exist_prob =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_exist_prob() *
      2.0F;

  /* DataTypeConversion: '<S6>/Convert43' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_02_MeasureType'
   */
  CSI_FTFC_Line_02_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_02_MeasureType();

  /* DataTypeConversion: '<S6>/Convert21' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_Type'
   */
  CSI_FC_Line_02_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Type();

  /* DataTypeConversion: '<S6>/Convert20' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_02_Id'
   */
  CSI_FC_Line_02_Id =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_02_Id();

  /* DataTypeConversion: '<S6>/Convert38' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_ObstacleFlg'
   */
  CSI_FTFC_Line_01_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_ObstacleFlg();

  /* DataTypeConversion: '<S6>/Convert40' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_RMSE'
   */
  CSI_FTFC_Line_01_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_RMSE();

  /* DataTypeConversion: '<S6>/Convert39' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_ParseConf'
   */
  CSI_FTFC_Line_01_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_ParseConf();

  /* DataTypeConversion: '<S6>/Convert3' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_dx_End_std'
   */
  CSI_FC_Line_01_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert2' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_dx_End'
   */
  CSI_FC_Line_01_dx_End =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_End() *
      0.25F;

  /* DataTypeConversion: '<S6>/Convert5' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_dx_Start_std'
   */
  CSI_FC_Line_01_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S6>/Convert4' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_dx_Start'
   */
  CSI_FC_Line_01_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S6>/Convert11' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_Width'
   */
  CSI_FC_Line_01_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Width() *
      0.01F;

  /* DataTypeConversion: '<S6>/Convert1' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_color'
   */
  CSI_FC_Line_01_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_color();

  /* DataTypeConversion: '<S6>/Convert35' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_curvature_alte'
   */
  CSI_FTFC_Line_01_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S6>/Convert36' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_curve'
   */
  CSI_FTFC_Line_01_curve =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_curve() *
          1.90735E-6F -
      0.015625F;

  /* DataTypeConversion: '<S6>/Convert8' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_HeadingAngle'
   */
  CSI_FC_Line_01_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S6>/Convert6' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_dy'
   */
  CSI_FC_Line_01_dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S6>/Convert7' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_exist_prob'
   */
  CSI_FC_Line_01_exist_prob =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_exist_prob() *
      2.0F;

  /* DataTypeConversion: '<S6>/Convert37' incorporates:
   *  Inport: '<Root>/FC_191_FTFC_Line_01_MeasureType'
   */
  CSI_FTFC_Line_01_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FTFC_Line_01_MeasureType();

  /* DataTypeConversion: '<S6>/Convert10' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_Type'
   */
  CSI_FC_Line_01_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Type();

  /* DataTypeConversion: '<S6>/Convert9' incorporates:
   *  Inport: '<Root>/FC_191_FC_Line_01_Id'
   */
  CSI_FC_Line_01_Id =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_Line_01_Id();

  /* DataTypeConversion: '<S6>/Convert34' incorporates:
   *  Inport: '<Root>/FC_191_FC_LineTiStamp'
   */
  CSI_FC_LineTiStamp =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_191_FC_LineTiStamp() *
      0.0001F;

  /* DataTypeConversion: '<S7>/Convert25' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_dx_End_std'
   */
  CSI_FC_Line_06_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert24' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_dx_End'
   */
  CSI_FC_Line_06_dx_End = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_End();

  /* DataTypeConversion: '<S7>/Convert27' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_dx_Start_std'
   */
  CSI_FC_Line_06_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert26' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_dx_Start'
   */
  CSI_FC_Line_06_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S7>/Convert49' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_Width'
   */
  CSI_FC_Line_06_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_Width() *
      0.01F;

  /* DataTypeConversion: '<S7>/Convert23' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_color'
   */
  CSI_FC_Line_06_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_color();

  /* DataTypeConversion: '<S7>/Convert33' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_curvature_alte'
   */
  CSI_FTFC_Line_06_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S7>/Convert47' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_curve'
   */
  CSI_FTFC_Line_06_curve = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_curve();

  /* DataTypeConversion: '<S7>/Convert30' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_HeadingAngle'
   */
  CSI_FC_Line_06_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S7>/Convert28' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_dy'
   */
  CSI_FC_Line_06_dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S7>/Convert29' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_exist_prob'
   */
  CSI_FC_Line_06_exist_prob = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_exist_prob();

  /* DataTypeConversion: '<S7>/Convert32' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_MeasureType'
   */
  CSI_FTFC_Line_06_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_MeasureType();

  /* DataTypeConversion: '<S7>/Convert48' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_06_Type'
   */
  CSI_FC_Line_06_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_06_Type();

  /* DataTypeConversion: '<S7>/Convert44' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_ObstacleFlg'
   */
  CSI_FTFC_Line_06_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_ObstacleFlg();

  /* DataTypeConversion: '<S7>/Convert46' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_RMSE'
   */
  CSI_FTFC_Line_06_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_RMSE();

  /* DataTypeConversion: '<S7>/Convert45' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_06_ParseConf'
   */
  CSI_FTFC_Line_06_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_06_ParseConf();

  /* DataTypeConversion: '<S7>/Convert3' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_dx_End_std'
   */
  CSI_FC_Line_05_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert2' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_dx_End'
   */
  CSI_FC_Line_05_dx_End = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_End();

  /* DataTypeConversion: '<S7>/Convert5' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_dx_Start'
   */
  CSI_FC_Line_05_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S7>/Convert4' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_Width'
   */
  CSI_FC_Line_05_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_Width() *
      0.01F;

  /* DataTypeConversion: '<S7>/Convert16' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_dx_Start_std'
   */
  CSI_FC_Line_05_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert1' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_color'
   */
  CSI_FC_Line_05_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_color();

  /* DataTypeConversion: '<S7>/Convert10' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_curvature_alte'
   */
  CSI_FTFC_Line_05_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S7>/Convert14' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_curve'
   */
  CSI_FTFC_Line_05_curve = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_curve();

  /* DataTypeConversion: '<S7>/Convert8' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_HeadingAngle'
   */
  CSI_FC_Line_05_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S7>/Convert6' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_dy'
   */
  CSI_FC_Line_05_dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S7>/Convert7' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_exist_prob'
   */
  CSI_FC_Line_05_exist_prob = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_exist_prob();

  /* DataTypeConversion: '<S7>/Convert9' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_MeasureType'
   */
  CSI_FTFC_Line_05_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_MeasureType();

  /* DataTypeConversion: '<S7>/Convert15' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_05_Type'
   */
  CSI_FC_Line_05_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_05_Type();

  /* DataTypeConversion: '<S7>/Convert11' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_ObstacleFlg'
   */
  CSI_FTFC_Line_05_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_ObstacleFlg();

  /* DataTypeConversion: '<S7>/Convert13' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_RMSE'
   */
  CSI_FTFC_Line_05_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_RMSE();

  /* DataTypeConversion: '<S7>/Convert12' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_05_ParseConf'
   */
  CSI_FTFC_Line_05_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_05_ParseConf();

  /* DataTypeConversion: '<S7>/Convert38' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_ObstacleFlg'
   */
  CSI_FTFC_Line_04_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_ObstacleFlg();

  /* DataTypeConversion: '<S7>/Convert40' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_RMSE'
   */
  CSI_FTFC_Line_04_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_RMSE();

  /* DataTypeConversion: '<S7>/Convert39' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_ParseConf'
   */
  CSI_FTFC_Line_04_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_ParseConf();

  /* DataTypeConversion: '<S7>/Convert19' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_dx_End_std'
   */
  CSI_FC_Line_04_dx_End_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_End_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert18' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_dx_End'
   */
  CSI_FC_Line_04_dx_End = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_End();

  /* DataTypeConversion: '<S7>/Convert21' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_dx_Start_std'
   */
  CSI_FC_Line_04_dx_Start_std =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_Start_std() *
      0.0078125F;

  /* DataTypeConversion: '<S7>/Convert20' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_dx_Start'
   */
  CSI_FC_Line_04_dx_Start =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dx_Start() *
      0.025F;

  /* DataTypeConversion: '<S7>/Convert37' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_Width'
   */
  CSI_FC_Line_04_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Width() *
      0.01F;

  /* DataTypeConversion: '<S7>/Convert17' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_color'
   */
  CSI_FC_Line_04_color =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_color();

  /* DataTypeConversion: '<S7>/Convert41' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_curvature_alte'
   */
  CSI_FTFC_Line_04_curvature_alte =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_curvature_alte() *
          1.2716E-7F -
      0.00026042F;

  /* DataTypeConversion: '<S7>/Convert42' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_curve'
   */
  CSI_FTFC_Line_04_curve = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_curve();

  /* DataTypeConversion: '<S7>/Convert34' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_HeadingAngle'
   */
  CSI_FC_Line_04_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_HeadingAngle() *
          0.000488281F -
      1.6F;

  /* DataTypeConversion: '<S7>/Convert22' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_exist_prob'
   */
  CSI_FC_Line_04_dy = (float32)
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_exist_prob();

  /* DataTypeConversion: '<S7>/Convert31' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_dy'
   */
  CSI_FC_Line_04_exist_prob =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_dy() *
          0.015625F -
      32.0F;

  /* DataTypeConversion: '<S7>/Convert43' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_04_MeasureType'
   */
  CSI_FTFC_Line_04_MeasureType =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_04_MeasureType();

  /* DataTypeConversion: '<S7>/Convert36' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_Type'
   */
  CSI_FC_Line_04_Type =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Type();

  /* DataTypeConversion: '<S7>/Convert35' incorporates:
   *  Inport: '<Root>/FC_192_FC_Line_04_Id'
   */
  CSI_FC_Line_04_Id =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FC_Line_04_Id();

  /* DataTypeConversion: '<S7>/Convert50' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_03_ObstacleFlg'
   */
  CSI_FTFC_Line_03_ObstacleFlg =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_ObstacleFlg();

  /* DataTypeConversion: '<S7>/Convert52' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_03_RMSE'
   */
  CSI_FTFC_Line_03_RMSE =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_RMSE();

  /* DataTypeConversion: '<S7>/Convert51' incorporates:
   *  Inport: '<Root>/FC_192_FTFC_Line_03_ParseConf'
   */
  CSI_FTFC_Line_03_ParseConf =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_192_FTFC_Line_03_ParseConf();

  /* DataTypeConversion: '<S8>/Convert117' incorporates:
   *  Inport: '<Root>/FC_19D_FrFr_AccOBJ_Width'
   */
  CSI_FrFr_AccOBJ_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FrFr_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S8>/Convert116' incorporates:
   *  Inport: '<Root>/FC_19D_FrFr_AccOBJ_Class'
   */
  CSI_FrFr_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FrFr_AccOBJ_Class();

  /* DataTypeConversion: '<S8>/Convert205' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Track_Age'
   */
  CSI_Fr_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S8>/Convert206' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Track_ID'
   */
  CSI_Fr_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S8>/Convert203' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_Fr_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S8>/Convert202' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_fusion_Sts'
   */
  CSI_Fr_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S8>/Convert112' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_ObstacleProb'
   */
  CSI_Fr_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S8>/Convert108' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_ExistProb'
   */
  CSI_Fr_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S8>/Convert221' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Taillight_Info'
   */
  CSI_Fr_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S8>/Convert104' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Brakelight_Info'
   */
  CSI_Fr_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S8>/Convert102' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Ax'
   */
  CSI_Fr_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S8>/Convert103' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Ay'
   */
  CSI_Fr_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S8>/Convert207' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Vx_std'
   */
  CSI_Fr_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S8>/Convert113' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Vx'
   */
  CSI_Fr_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S8>/Convert208' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Vy_std'
   */
  CSI_Fr_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S8>/Convert114' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Vy'
   */
  CSI_Fr_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S8>/Convert200' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Dx_Vnce'
   */
  CSI_Fr_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S8>/Convert106' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Dx'
   */
  CSI_Fr_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S8>/Convert201' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Dy_Vnce'
   */
  CSI_Fr_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S8>/Convert107' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Dy'
   */
  CSI_Fr_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S8>/Convert109' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_HeadingAngle'
   */
  CSI_Fr_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S8>/Convert111' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Length'
   */
  CSI_Fr_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S8>/Convert110' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Height'
   */
  CSI_Fr_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S8>/Convert115' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Width'
   */
  CSI_Fr_AccOBJ_Width =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S8>/Convert105' incorporates:
   *  Inport: '<Root>/FC_19D_Fr_AccOBJ_Class'
   */
  CSI_Fr_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_Fr_AccOBJ_Class();

  /* DataTypeConversion: '<S8>/Convert178' incorporates:
   *  Inport: '<Root>/FC_19D_FC_CIPV_ID'
   */
  CSI_FC_CIPV_ID = Rte_IRead_CameraSigInput_10ms_Runnable_FC_19D_FC_CIPV_ID();

  /* DataTypeConversion: '<S9>/Convert237' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Dy_Vnce'
   */
  CSI_LeFr_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S9>/Convert141' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Dy'
   */
  CSI_LeFr_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert142' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_HeadingAngle'
   */
  CSI_LeFr_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S9>/Convert239' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Length'
   */
  CSI_LeFr_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S9>/Convert238' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Height'
   */
  CSI_LeFr_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S9>/Convert143' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Width'
   */
  CSI_LeFr_AccOBJ_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S9>/Convert140' incorporates:
   *  Inport: '<Root>/FC_19E_LeFr_AccOBJ_Class'
   */
  CSI_LeFr_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_LeFr_AccOBJ_Class();

  /* DataTypeConversion: '<S9>/Convert233' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Track_Age'
   */
  CSI_Le_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S9>/Convert234' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Track_ID'
   */
  CSI_Le_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S9>/Convert229' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_Le_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S9>/Convert228' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_fusion_Sts'
   */
  CSI_Le_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S9>/Convert136' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_ObstacleProb'
   */
  CSI_Le_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S9>/Convert134' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_ExistProb'
   */
  CSI_Le_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S9>/Convert130' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Brakelight_Info'
   */
  CSI_Le_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S9>/Convert232' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Taillight_Info'
   */
  CSI_Le_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S9>/Convert128' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Ax'
   */
  CSI_Le_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S9>/Convert129' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Ay'
   */
  CSI_Le_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S9>/Convert235' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Vx_std'
   */
  CSI_Le_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert137' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Vx'
   */
  CSI_Le_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert236' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Vy_std'
   */
  CSI_Le_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S9>/Convert138' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Vy'
   */
  CSI_Le_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S9>/Convert226' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Dx_Vnce'
   */
  CSI_Le_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S9>/Convert132' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Dx'
   */
  CSI_Le_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S9>/Convert227' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Dy_Vnce'
   */
  CSI_Le_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S9>/Convert133' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Dy'
   */
  CSI_Le_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert135' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_HeadingAngle'
   */
  CSI_Le_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S9>/Convert231' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Length'
   */
  CSI_Le_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S9>/Convert230' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Height'
   */
  CSI_Le_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S9>/Convert139' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Width'
   */
  CSI_Le_AccOBJ_Width =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S9>/Convert131' incorporates:
   *  Inport: '<Root>/FC_19E_Le_AccOBJ_Class'
   */
  CSI_Le_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_Le_AccOBJ_Class();

  /* DataTypeConversion: '<S9>/Convert222' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Track_Age'
   */
  CSI_FrFr_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S9>/Convert223' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Track_ID'
   */
  CSI_FrFr_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S9>/Convert218' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_FrFr_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S9>/Convert217' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_fusion_Sts'
   */
  CSI_FrFr_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S9>/Convert125' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_ObstacleProb'
   */
  CSI_FrFr_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S9>/Convert123' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_ExistProb'
   */
  CSI_FrFr_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S9>/Convert221' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Taillight_Info'
   */
  CSI_FrFr_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S9>/Convert120' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Brakelight_Info'
   */
  CSI_FrFr_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S9>/Convert118' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Ax'
   */
  CSI_FrFr_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S9>/Convert119' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Ay'
   */
  CSI_FrFr_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S9>/Convert224' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Vx_std'
   */
  CSI_FrFr_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert126' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Vx'
   */
  CSI_FrFr_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert225' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Vy_std'
   */
  CSI_FrFr_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S9>/Convert127' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Vy'
   */
  CSI_FrFr_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S9>/Convert215' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Dx_Vnce'
   */
  CSI_FrFr_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S9>/Convert121' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Dx'
   */
  CSI_FrFr_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S9>/Convert216' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Dy_Vnce'
   */
  CSI_FrFr_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S9>/Convert122' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Dy'
   */
  CSI_FrFr_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S9>/Convert124' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_HeadingAngle'
   */
  CSI_FrFr_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S9>/Convert220' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Length'
   */
  CSI_FrFr_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S9>/Convert219' incorporates:
   *  Inport: '<Root>/FC_19E_FrFr_AccOBJ_Height'
   */
  CSI_FrFr_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19E_FrFr_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S10>/Convert263' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Vy_std'
   */
  CSI_RiFr_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert168' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Vy'
   */
  CSI_RiFr_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert259' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Dx_Vnce'
   */
  CSI_RiFr_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S10>/Convert165' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Dx'
   */
  CSI_RiFr_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S10>/Convert260' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Dy_Vnce'
   */
  CSI_RiFr_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S10>/Convert166' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Dy'
   */
  CSI_RiFr_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert167' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_HeadingAngle'
   */
  CSI_RiFr_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S10>/Convert262' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Length'
   */
  CSI_RiFr_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S10>/Convert261' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Height'
   */
  CSI_RiFr_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S10>/Convert169' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Width'
   */
  CSI_RiFr_AccOBJ_Width =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S10>/Convert164' incorporates:
   *  Inport: '<Root>/FC_19F_RiFr_AccOBJ_Class'
   */
  CSI_RiFr_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_RiFr_AccOBJ_Class();

  /* DataTypeConversion: '<S10>/Convert255' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Track_Age'
   */
  CSI_Ri_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S10>/Convert256' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Track_ID'
   */
  CSI_Ri_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S10>/Convert251' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_Ri_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S10>/Convert250' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_fusion_Sts'
   */
  CSI_Ri_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S10>/Convert160' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_ObstacleProb'
   */
  CSI_Ri_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S10>/Convert158' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_ExistProb'
   */
  CSI_Ri_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S10>/Convert154' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Brakelight_Info'
   */
  CSI_Ri_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S10>/Convert254' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Taillight_Info'
   */
  CSI_Ri_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S10>/Convert152' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Ax'
   */
  CSI_Ri_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S10>/Convert153' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Ay'
   */
  CSI_Ri_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S10>/Convert257' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Vx_std'
   */
  CSI_Ri_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert161' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Vx'
   */
  CSI_Ri_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert258' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Vy_std'
   */
  CSI_Ri_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert162' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Vy'
   */
  CSI_Ri_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert248' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Dx_Vnce'
   */
  CSI_Ri_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S10>/Convert156' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Dx'
   */
  CSI_Ri_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S10>/Convert249' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Dy_Vnce'
   */
  CSI_Ri_AccOBJ_Dy_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dy_Vnce() *
      0.02F;

  /* DataTypeConversion: '<S10>/Convert157' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Dy'
   */
  CSI_Ri_AccOBJ_Dy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Dy() *
          0.125F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert159' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_HeadingAngle'
   */
  CSI_Ri_AccOBJ_HeadingAngle =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_HeadingAngle() *
          0.01F -
      1.57F;

  /* DataTypeConversion: '<S10>/Convert253' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Length'
   */
  CSI_Ri_AccOBJ_Length =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Length() *
      0.05F;

  /* DataTypeConversion: '<S10>/Convert252' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Height'
   */
  CSI_Ri_AccOBJ_Height =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Height() *
      0.01F;

  /* DataTypeConversion: '<S10>/Convert163' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Width'
   */
  CSI_Ri_AccOBJ_Width =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Width() *
      0.01F;

  /* DataTypeConversion: '<S10>/Convert155' incorporates:
   *  Inport: '<Root>/FC_19F_Ri_AccOBJ_Class'
   */
  CSI_Ri_AccOBJ_Class =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_Ri_AccOBJ_Class();

  /* DataTypeConversion: '<S10>/Convert244' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Track_Age'
   */
  CSI_LeFr_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S10>/Convert245' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Track_ID'
   */
  CSI_LeFr_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S10>/Convert242' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_LeFr_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S10>/Convert241' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_fusion_Sts'
   */
  CSI_LeFr_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S10>/Convert149' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_ObstacleProb'
   */
  CSI_LeFr_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S10>/Convert148' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_ExistProb'
   */
  CSI_LeFr_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S10>/Convert146' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Brakelight_Info'
   */
  CSI_LeFr_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S10>/Convert243' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Taillight_Info'
   */
  CSI_LeFr_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S10>/Convert144' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Ax'
   */
  CSI_LeFr_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S10>/Convert145' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Ay'
   */
  CSI_LeFr_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S10>/Convert246' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Vx_std'
   */
  CSI_LeFr_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert150' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Vx'
   */
  CSI_LeFr_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S10>/Convert247' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Vy_std'
   */
  CSI_LeFr_AccOBJ_Vy_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vy_std() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert151' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Vy'
   */
  CSI_LeFr_AccOBJ_Vy =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Vy() *
          0.125F -
      64.0F;

  /* DataTypeConversion: '<S10>/Convert240' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Dx_Vnce'
   */
  CSI_LeFr_AccOBJ_Dx_Vnce =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Dx_Vnce() *
      0.05F;

  /* DataTypeConversion: '<S10>/Convert147' incorporates:
   *  Inport: '<Root>/FC_19F_LeFr_AccOBJ_Dx'
   */
  CSI_LeFr_AccOBJ_Dx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_19F_LeFr_AccOBJ_Dx() *
          0.0625F -
      256.0F;

  /* DataTypeConversion: '<S11>/Convert273' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Track_Age'
   */
  CSI_RiFr_AccOBJ_Track_Age =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Track_Age();

  /* DataTypeConversion: '<S11>/Convert274' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Track_ID'
   */
  CSI_RiFr_AccOBJ_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Track_ID();

  /* DataTypeConversion: '<S11>/Convert271' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID'
   */
  CSI_RiFr_AccOBJ_FusionedFC_Track_ID =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID();

  /* DataTypeConversion: '<S11>/Convert270' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_fusion_Sts'
   */
  CSI_RiFr_AccOBJ_fusion_Sts =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_fusion_Sts();

  /* DataTypeConversion: '<S11>/Convert174' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_ObstacleProb'
   */
  CSI_RiFr_AccOBJ_ObstacleProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_ObstacleProb() *
      2.0F;

  /* DataTypeConversion: '<S11>/Convert173' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_ExistProb'
   */
  CSI_RiFr_AccOBJ_ExistProb =
      (float32)
          Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_ExistProb() *
      2.0F;

  /* DataTypeConversion: '<S11>/Convert172' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Brakelight_Info'
   */
  CSI_RiFr_AccOBJ_Brakelight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Brakelight_Info();

  /* DataTypeConversion: '<S11>/Convert272' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Taillight_Info'
   */
  CSI_RiFr_AccOBJ_Taillight_Info =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Taillight_Info();

  /* DataTypeConversion: '<S11>/Convert170' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Ax'
   */
  CSI_RiFr_AccOBJ_Ax =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Ax() *
          0.03125F -
      16.0F;

  /* DataTypeConversion: '<S11>/Convert171' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Ay'
   */
  CSI_RiFr_AccOBJ_Ay =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Ay() *
          0.0625F -
      16.0F;

  /* DataTypeConversion: '<S11>/Convert275' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Vx_std'
   */
  CSI_RiFr_AccOBJ_Vx_std =
      (float32)
              Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Vx_std() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S11>/Convert175' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_Vx'
   */
  CSI_RiFr_AccOBJ_Vx =
      (float32)Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_Vx() *
          0.0625F -
      128.0F;

  /* DataTypeConversion: '<S11>/Convert264' incorporates:
   *  Inport: '<Root>/FC_1A0_Fr_AccOBJ_confi'
   */
  CSI_Fr_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Fr_AccOBJ_confi();

  /* DataTypeConversion: '<S11>/Convert265' incorporates:
   *  Inport: '<Root>/FC_1A0_FrFr_AccOBJ_confi'
   */
  CSI_FrFr_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_FrFr_AccOBJ_confi();

  /* DataTypeConversion: '<S11>/Convert266' incorporates:
   *  Inport: '<Root>/FC_1A0_Le_AccOBJ_confi'
   */
  CSI_Le_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Le_AccOBJ_confi();

  /* DataTypeConversion: '<S11>/Convert267' incorporates:
   *  Inport: '<Root>/FC_1A0_LeFr_AccOBJ_confi'
   */
  CSI_LeFr_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_LeFr_AccOBJ_confi();

  /* DataTypeConversion: '<S11>/Convert268' incorporates:
   *  Inport: '<Root>/FC_1A0_Ri_AccOBJ_confi'
   */
  CSI_Ri_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_Ri_AccOBJ_confi();

  /* DataTypeConversion: '<S11>/Convert269' incorporates:
   *  Inport: '<Root>/FC_1A0_RiFr_AccOBJ_confi'
   */
  CSI_RiFr_AccOBJ_confi =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A0_RiFr_AccOBJ_confi();

  /* DataTypeConversion: '<S12>/Convert176' incorporates:
   *  Inport: '<Root>/FC_1A5_FC_FrontCameraCalibrationStatus'
   */
  CSI_FC_FrontCameraCalibrationStatus =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A5_FC_FrontCameraCalibrationStatus();

  /* DataTypeConversion: '<S12>/Convert177' incorporates:
   *  Inport: '<Root>/FC_1A5_FC_LaneChangeStatus'
   */
  CSI_FC_LaneChangeStatus =
      Rte_IRead_CameraSigInput_10ms_Runnable_FC_1A5_FC_LaneChangeStatus();

  /* SignalConversion: '<S4>/Signal Conversion' */
  CSI_CalibrationStatus = CSI_FC_FrontCameraCalibrationStatus;

  /* SignalConversion: '<S4>/Signal Conversion1' */
  CSI_LaneChangeStatus = CSI_FC_LaneChangeStatus;

  /* SignalConversion: '<S4>/Signal Conversion2' */
  CSI_LaneTimeStamp = CSI_FC_LineTiStamp;

  /* SignalConversion: '<S4>/Signal Conversion3' */
  CSI_MarkerTypeLf = CSI_FC_Line_01_Type;

  /* SignalConversion: '<S4>/Signal Conversion4' */
  CSI_LaneIDLf = CSI_FC_Line_01_Id;

  /* SignalConversion: '<S4>/Signal Conversion5' */
  CSI_MeasureTypeLf = CSI_FTFC_Line_01_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion6' */
  CSI_ExistProbLf = CSI_FC_Line_01_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion7' */
  CSI_PosY0Lf = CSI_FC_Line_01_dy;

  /* SignalConversion: '<S4>/Signal Conversion8' */
  CSI_HeadingAngleLf = CSI_FC_Line_01_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion9' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureLf = CSI_FTFC_Line_01_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion10' */
  CSI_ColorTypeLf = CSI_FC_Line_01_color;

  /* SignalConversion: '<S4>/Signal Conversion11' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateLf =
      CSI_FTFC_Line_01_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion12' */
  CSI_MakerWidthLf = CSI_FC_Line_01_Width;

  /* SignalConversion: '<S4>/Signal Conversion13' */
  CSI_PosXStartLf = CSI_FC_Line_01_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion14' */
  CSI_PosYStartStdLf = CSI_FC_Line_01_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion15' */
  CSI_PosXEndLf = CSI_FC_Line_01_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion16' */
  CSI_PosXEndStdLf = CSI_FC_Line_01_dx_End_std;

  /* SignalConversion: '<S4>/Signal Conversion17' */
  CSI_ParseConfLf = CSI_FTFC_Line_01_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion18' */
  CSI_ObstacleFlgLf = CSI_FTFC_Line_01_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion19' */
  CSI_PolyfitRmseLf = CSI_FTFC_Line_01_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion20' */
  CSI_LaneIDRi = CSI_FC_Line_02_Id;

  /* SignalConversion: '<S4>/Signal Conversion21' */
  CSI_MeasureTypeRi = CSI_FTFC_Line_02_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion22' */
  CSI_MarkerTypeRi = CSI_FC_Line_02_Type;

  /* SignalConversion: '<S4>/Signal Conversion23' */
  CSI_ExistProbRi = CSI_FC_Line_02_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion24' */
  CSI_PosY0Ri = CSI_FC_Line_02_dy;

  /* SignalConversion: '<S4>/Signal Conversion25' */
  CSI_HeadingAngleRi = CSI_FC_Line_02_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion26' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureRi = CSI_FTFC_Line_02_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion27' */
  CSI_ColorTypeRi = CSI_FC_Line_02_color;

  /* SignalConversion: '<S4>/Signal Conversion28' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateRi =
      CSI_FTFC_Line_02_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion29' */
  CSI_MakerWidthRi = CSI_FC_Line_02_Width;

  /* SignalConversion: '<S4>/Signal Conversion30' */
  CSI_PosXStartRi = CSI_FC_Line_02_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion31' */
  CSI_PosYStartStdRi = CSI_FC_Line_02_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion32' */
  CSI_PosXEndRi = CSI_FC_Line_02_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion33' */
  CSI_PosXEndStdRi = CSI_FC_Line_02_dx_End_std;

  /* SignalConversion: '<S4>/Signal Conversion34' */
  CSI_ParseConfRi = CSI_FTFC_Line_02_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion35' */
  CSI_PolyfitRmseRi = CSI_FTFC_Line_02_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion36' */
  CSI_ObstacleFlgRi = CSI_FTFC_Line_02_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion68' */
  CSI_LaneIDLfAdj = CSI_FC_Line_03_Id;

  /* SignalConversion: '<S4>/Signal Conversion69' */
  CSI_MeasureTypeLfAdj = CSI_FTFC_Line_03_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion60' */
  CSI_MarkerTypeLfAdj = CSI_FC_Line_03_Type;

  /* SignalConversion: '<S4>/Signal Conversion70' */
  CSI_ExistProbLfAdj = CSI_FC_Line_03_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion71' */
  CSI_PosY0LfAdj = CSI_FC_Line_03_dy;

  /* SignalConversion: '<S4>/Signal Conversion72' */
  CSI_HeadingAngleLfAdj = CSI_FC_Line_03_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion73' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureLfAdj = CSI_FTFC_Line_03_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion40' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateLfAdj =
      CSI_FTFC_Line_03_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion39' */
  CSI_ColorTypeLfAdj = CSI_FC_Line_03_color;

  /* SignalConversion: '<S4>/Signal Conversion41' */
  CSI_MakerWidthLfAdj = CSI_FC_Line_03_Width;

  /* SignalConversion: '<S4>/Signal Conversion42' */
  CSI_PosXStartLfAdj = CSI_FC_Line_03_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion43' */
  CSI_PosYStartStdLfAdj = CSI_FC_Line_03_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion44' */
  CSI_PosXEndLfAdj = CSI_FC_Line_03_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion45' */
  CSI_PosXEndStdLfAdj = CSI_FC_Line_03_dx_End_std;

  /* SignalConversion: '<S4>/Signal Conversion47' */
  CSI_ObstacleFlgLfAdj = CSI_FTFC_Line_03_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion48' */
  CSI_PolyfitRmseLfAdj = CSI_FTFC_Line_03_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion46' */
  CSI_ParseConfLfAdj = CSI_FTFC_Line_03_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion50' */
  CSI_LaneIDRiAdj = CSI_FC_Line_04_Id;

  /* SignalConversion: '<S4>/Signal Conversion51' */
  CSI_MeasureTypeRiAdj = CSI_FTFC_Line_04_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion52' */
  CSI_MarkerTypeRiAdj = CSI_FC_Line_04_Type;

  /* SignalConversion: '<S4>/Signal Conversion53' */
  CSI_ExistProbRiAdj = CSI_FC_Line_04_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion54' */
  CSI_PosY0RiAdj = CSI_FC_Line_04_dy;

  /* SignalConversion: '<S4>/Signal Conversion55' */
  CSI_HeadingAngleRiAdj = CSI_FC_Line_04_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion56' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureRiAdj = CSI_FTFC_Line_04_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion58' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateRiAdj =
      CSI_FTFC_Line_04_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion57' */
  CSI_ColorTypeRiAdj = CSI_FC_Line_04_color;

  /* SignalConversion: '<S4>/Signal Conversion59' */
  CSI_MakerWidthRiAdj = CSI_FC_Line_04_Width;

  /* SignalConversion: '<S4>/Signal Conversion61' */
  CSI_PosXStartRiAdj = CSI_FC_Line_04_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion62' */
  CSI_PosYStartStdRiAdj = CSI_FC_Line_04_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion63' */
  CSI_PosXEndRiAdj = CSI_FC_Line_04_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion64' */
  CSI_PosXEndStdRiAdj = CSI_FC_Line_04_dx_End_std;

  /* SignalConversion: '<S4>/Signal Conversion65' */
  CSI_ParseConfRiAdj = CSI_FTFC_Line_04_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion66' */
  CSI_PolyfitRmseRiAdj = CSI_FTFC_Line_04_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion67' */
  CSI_ObstacleFlgRiAdj = CSI_FTFC_Line_04_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion99' */
  CSI_ParseConfLfRe = CSI_FTFC_Line_05_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion100' */
  CSI_PolyfitRmseLfRe = CSI_FTFC_Line_05_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion91' */
  CSI_ObstacleFlgLfRe = CSI_FTFC_Line_05_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion101' */
  CSI_MeasureTypeLfRe = CSI_FTFC_Line_05_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion96' */
  CSI_MarkerTypeLfRe = CSI_FC_Line_05_Type;

  /* SignalConversion: '<S4>/Signal Conversion102' */
  CSI_ExistProbLfRe = CSI_FC_Line_05_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion103' */
  CSI_PosY0LfRe = CSI_FC_Line_05_dy;

  /* SignalConversion: '<S4>/Signal Conversion104' */
  CSI_HeadingAngleLfRe = CSI_FC_Line_05_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion38' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureLfRe = CSI_FTFC_Line_05_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion37' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateLfRe =
      CSI_FTFC_Line_05_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion49' */
  CSI_ColorTypeLfRe = CSI_FC_Line_05_color;

  /* SignalConversion: '<S4>/Signal Conversion74' */
  CSI_MakerWidthLfRe = CSI_FC_Line_05_Width;

  /* SignalConversion: '<S4>/Signal Conversion75' */
  CSI_PosXStartLfRe = CSI_FC_Line_05_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion79' */
  CSI_PosYStartStdLfRe = CSI_FC_Line_05_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion76' */
  CSI_PosXEndLfRe = CSI_FC_Line_05_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion77' */
  CSI_PosXEndStdLfRe = CSI_FC_Line_05_dx_End_std;

  /* SignalConversion: '<S4>/Signal Conversion94' */
  CSI_ParseConfRiRe = CSI_FTFC_Line_06_ParseConf;

  /* SignalConversion: '<S4>/Signal Conversion78' */
  CSI_PolyfitRmseRiRe = CSI_FTFC_Line_06_RMSE;

  /* SignalConversion: '<S4>/Signal Conversion93' */
  CSI_ObstacleFlgRiRe = CSI_FTFC_Line_06_ObstacleFlg;

  /* SignalConversion: '<S4>/Signal Conversion80' */
  CSI_MarkerTypeRiRe = CSI_FC_Line_06_Type;

  /* SignalConversion: '<S4>/Signal Conversion81' */
  CSI_MeasureTypeRiRe = CSI_FTFC_Line_06_MeasureType;

  /* SignalConversion: '<S4>/Signal Conversion82' */
  CSI_ExistProbRiRe = CSI_FC_Line_06_exist_prob;

  /* SignalConversion: '<S4>/Signal Conversion83' */
  CSI_PosY0RiRe = CSI_FC_Line_06_dy;

  /* SignalConversion: '<S4>/Signal Conversion85' */
  CSI_HeadingAngleRiRe = CSI_FC_Line_06_HeadingAngle;

  /* SignalConversion: '<S4>/Signal Conversion84' incorporates:
   *  Constant: '<S4>/Constant'
   *  Product: '<S4>/Product'
   */
  CSI_CurvatureRiRe = CSI_FTFC_Line_06_curve * CameraSigInput_P.Constant_Value;

  /* SignalConversion: '<S4>/Signal Conversion86' incorporates:
   *  Constant: '<S4>/Constant1'
   *  Product: '<S4>/Product1'
   */
  CSI_CrvRateRiRe =
      CSI_FTFC_Line_06_curvature_alte * CameraSigInput_P.Constant1_Value;

  /* SignalConversion: '<S4>/Signal Conversion87' */
  CSI_ColorTypeRiRe = CSI_FC_Line_06_color;

  /* SignalConversion: '<S4>/Signal Conversion88' */
  CSI_MakerWidthRiRe = CSI_FC_Line_06_Width;

  /* SignalConversion: '<S4>/Signal Conversion92' */
  CSI_PosXStartRiRe = CSI_FC_Line_06_dx_Start;

  /* SignalConversion: '<S4>/Signal Conversion89' */
  CSI_PosYStartStdRiRe = CSI_FC_Line_06_dx_Start_std;

  /* SignalConversion: '<S4>/Signal Conversion90' */
  CSI_PosXEndRiRe = CSI_FC_Line_06_dx_End;

  /* SignalConversion: '<S4>/Signal Conversion95' */
  CSI_PosXEndStdRiRe = CSI_FC_Line_06_dx_End_std;

  /* BusCreator: '<S4>/Bus Creator' */
  CSI_LaneInfo.CSI_CalibrationStatus = CSI_CalibrationStatus;
  CSI_LaneInfo.CSI_LaneChangeStatus = CSI_LaneChangeStatus;
  CSI_LaneInfo.CSI_LaneTimeStamp = CSI_LaneTimeStamp;
  CSI_LaneInfo.CSI_MarkerTypeLf = CSI_MarkerTypeLf;
  CSI_LaneInfo.CSI_LaneIDLf = CSI_LaneIDLf;
  CSI_LaneInfo.CSI_MeasureTypeLf = CSI_MeasureTypeLf;
  CSI_LaneInfo.CSI_ExistProbLf = CSI_ExistProbLf;
  CSI_LaneInfo.CSI_PosY0Lf = CSI_PosY0Lf;
  CSI_LaneInfo.CSI_HeadingLf = CSI_HeadingAngleLf;
  CSI_LaneInfo.CSI_CurvatureLf = CSI_CurvatureLf;
  CSI_LaneInfo.CSI_ColorTypeLf = CSI_ColorTypeLf;
  CSI_LaneInfo.CSI_CrvRateLf = CSI_CrvRateLf;
  CSI_LaneInfo.CSI_MakerWidthLf = CSI_MakerWidthLf;
  CSI_LaneInfo.CSI_PosXStartLf = CSI_PosXStartLf;
  CSI_LaneInfo.CSI_PosYStartStdLf = CSI_PosYStartStdLf;
  CSI_LaneInfo.CSI_PosXEndLf = CSI_PosXEndLf;
  CSI_LaneInfo.CSI_PosXEndStdLf = CSI_PosXEndStdLf;
  CSI_LaneInfo.CSI_ParseConfLf = CSI_ParseConfLf;
  CSI_LaneInfo.CSI_ObstacleFlgLf = CSI_ObstacleFlgLf;
  CSI_LaneInfo.CSI_PolyfitRmseLf = CSI_PolyfitRmseLf;
  CSI_LaneInfo.CSI_LaneIDRi = CSI_LaneIDRi;
  CSI_LaneInfo.CSI_MeasureTypeRi = CSI_MeasureTypeRi;
  CSI_LaneInfo.CSI_MarkerTypeRi = CSI_MarkerTypeRi;
  CSI_LaneInfo.CSI_ExistProbRi = CSI_ExistProbRi;
  CSI_LaneInfo.CSI_PosY0Ri = CSI_PosY0Ri;
  CSI_LaneInfo.CSI_HeadingRi = CSI_HeadingAngleRi;
  CSI_LaneInfo.CSI_CurvatureRi = CSI_CurvatureRi;
  CSI_LaneInfo.CSI_ColorTypeRi = CSI_ColorTypeRi;
  CSI_LaneInfo.CSI_CrvRateRi = CSI_CrvRateRi;
  CSI_LaneInfo.CSI_MakerWidthRi = CSI_MakerWidthRi;
  CSI_LaneInfo.CSI_PosXStartRi = CSI_PosXStartRi;
  CSI_LaneInfo.CSI_PosYStartStdRi = CSI_PosYStartStdRi;
  CSI_LaneInfo.CSI_PosXEndRi = CSI_PosXEndRi;
  CSI_LaneInfo.CSI_PosXEndStdRi = CSI_PosXEndStdRi;
  CSI_LaneInfo.CSI_ParseConfRi = CSI_ParseConfRi;
  CSI_LaneInfo.CSI_PolyfitRmseRi = CSI_PolyfitRmseRi;
  CSI_LaneInfo.CSI_ObstacleFlgRi = CSI_ObstacleFlgRi;
  CSI_LaneInfo.CSI_LaneIDLfAdj = CSI_LaneIDLfAdj;
  CSI_LaneInfo.CSI_MeasureTypeLfAdj = CSI_MeasureTypeLfAdj;
  CSI_LaneInfo.CSI_MarkerTypeLfAdj = CSI_MarkerTypeLfAdj;
  CSI_LaneInfo.CSI_ExistProbLfAdj = CSI_ExistProbLfAdj;
  CSI_LaneInfo.CSI_PosY0LfAdj = CSI_PosY0LfAdj;
  CSI_LaneInfo.CSI_HeadingLfAdj = CSI_HeadingAngleLfAdj;
  CSI_LaneInfo.CSI_CurvatureLfAdj = CSI_CurvatureLfAdj;
  CSI_LaneInfo.CSI_CrvRateLfAdj = CSI_CrvRateLfAdj;
  CSI_LaneInfo.CSI_ColorTypeLfAdj = CSI_ColorTypeLfAdj;
  CSI_LaneInfo.CSI_MakerWidthLfAdj = CSI_MakerWidthLfAdj;
  CSI_LaneInfo.CSI_PosXStartLfAdj = CSI_PosXStartLfAdj;
  CSI_LaneInfo.CSI_PosYStartStdLfAdj = CSI_PosYStartStdLfAdj;
  CSI_LaneInfo.CSI_PosXEndLfAdj = CSI_PosXEndLfAdj;
  CSI_LaneInfo.CSI_PosXEndStdLfAdj = CSI_PosXEndStdLfAdj;
  CSI_LaneInfo.CSI_ObstacleFlgLfAdj = CSI_ObstacleFlgLfAdj;
  CSI_LaneInfo.CSI_PolyfitRmseLfAdj = CSI_PolyfitRmseLfAdj;
  CSI_LaneInfo.CSI_ParseConfLfAdj = CSI_ParseConfLfAdj;
  CSI_LaneInfo.CSI_LaneIDRiAdj = CSI_LaneIDRiAdj;
  CSI_LaneInfo.CSI_MeasureTypeRiAdj = CSI_MeasureTypeRiAdj;
  CSI_LaneInfo.CSI_MarkerTypeRiAdj = CSI_MarkerTypeRiAdj;
  CSI_LaneInfo.CSI_ExistProbRiAdj = CSI_ExistProbRiAdj;
  CSI_LaneInfo.CSI_PosY0RiAdj = CSI_PosY0RiAdj;
  CSI_LaneInfo.CSI_HeadingAngleRiAdj = CSI_HeadingAngleRiAdj;
  CSI_LaneInfo.CSI_CurvatureRiAdj = CSI_CurvatureRiAdj;
  CSI_LaneInfo.CSI_CrvRateRiAdj = CSI_CrvRateRiAdj;
  CSI_LaneInfo.CSI_ColorTypeRiAdj = CSI_ColorTypeRiAdj;
  CSI_LaneInfo.CSI_MakerWidthRiAdj = CSI_MakerWidthRiAdj;
  CSI_LaneInfo.CSI_PosXStartRiAdj = CSI_PosXStartRiAdj;
  CSI_LaneInfo.CSI_PosYStartStdRiAdj = CSI_PosYStartStdRiAdj;
  CSI_LaneInfo.CSI_PosXEndRiAdj = CSI_PosXEndRiAdj;
  CSI_LaneInfo.CSI_PosXEndStdRiAdj = CSI_PosXEndStdRiAdj;
  CSI_LaneInfo.CSI_ParseConfRiAdj = CSI_ParseConfRiAdj;
  CSI_LaneInfo.CSI_ObstacleFlgRiAdj = CSI_PolyfitRmseRiAdj;
  CSI_LaneInfo.CSI_PolyfitRmseRiAdj = CSI_ObstacleFlgRiAdj;
  CSI_LaneInfo.CSI_ParseConfLfRe = CSI_ParseConfLfRe;
  CSI_LaneInfo.CSI_PolyfitRmseLfRe = CSI_PolyfitRmseLfRe;
  CSI_LaneInfo.CSI_ObstacleFlgLfRe = CSI_ObstacleFlgLfRe;
  CSI_LaneInfo.CSI_MeasureTypeLfRe = CSI_MeasureTypeLfRe;
  CSI_LaneInfo.CSI_MarkerTypeLfRe = CSI_MarkerTypeLfRe;
  CSI_LaneInfo.CSI_ExistProbLfRe = (uint8)CSI_ExistProbLfRe;
  CSI_LaneInfo.CSI_PosY0LfRe = CSI_PosY0LfRe;
  CSI_LaneInfo.CSI_HeadingLfRe = CSI_HeadingAngleLfRe;
  CSI_LaneInfo.CSI_CurvatureLfRe = CSI_CurvatureLfRe;
  CSI_LaneInfo.CSI_CrvRateLfRe = CSI_CrvRateLfRe;
  CSI_LaneInfo.CSI_ColorTypeLfRe = CSI_ColorTypeLfRe;
  CSI_LaneInfo.CSI_MakerWidthLfRe = CSI_MakerWidthLfRe;
  CSI_LaneInfo.CSI_PosXStartLfRe = CSI_PosXStartLfRe;
  CSI_LaneInfo.CSI_PosYStartStdLfRe = CSI_PosYStartStdLfRe;
  CSI_LaneInfo.CSI_PosXEndLfRe = CSI_PosXEndLfRe;
  CSI_LaneInfo.CSI_PosXEndStdLfRe = CSI_PosXEndStdLfRe;
  CSI_LaneInfo.CSI_ParseConfRiRe = CSI_ParseConfRiRe;
  CSI_LaneInfo.CSI_PolyfitRmseRiRe = CSI_PolyfitRmseRiRe;
  CSI_LaneInfo.CSI_ObstacleFlgRiRe = CSI_ObstacleFlgRiRe;
  CSI_LaneInfo.CSI_MarkerTypeRiRe = CSI_MarkerTypeRiRe;
  CSI_LaneInfo.CSI_MeasureTypeRiRe = CSI_MeasureTypeRiRe;
  CSI_LaneInfo.CSI_ExistProbRiRe = CSI_ExistProbRiRe;
  CSI_LaneInfo.CSI_PosY0RiRe = CSI_PosY0RiRe;
  CSI_LaneInfo.CSI_HeadingAngleRiRe = CSI_HeadingAngleRiRe;
  CSI_LaneInfo.CSI_CurvatureRiRe = CSI_CurvatureRiRe;
  CSI_LaneInfo.CSI_CrvRateRiRe = CSI_CrvRateRiRe;
  CSI_LaneInfo.CSI_ColorTypeRiRe = CSI_ColorTypeRiRe;
  CSI_LaneInfo.CSI_MakerWidthRiRe = CSI_MakerWidthRiRe;
  CSI_LaneInfo.CSI_PosXStartRiRe = CSI_PosXStartRiRe;
  CSI_LaneInfo.CSI_PosYStartStdRiRe = CSI_PosYStartStdRiRe;
  CSI_LaneInfo.CSI_PosXEndRiRe = CSI_PosXEndRiRe;
  CSI_LaneInfo.CSI_PosXEndStdRiRe = CSI_PosXEndStdRiRe;

  /* BusCreator: '<S5>/Bus Creator' */
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Width = CSI_Ri_AccOBJ_Width;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Vy = CSI_Ri_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Vx = CSI_Ri_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_ObstacleProb = CSI_Ri_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_HeadingAngle = CSI_Ri_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_ExistProb = CSI_Ri_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Dy = CSI_Ri_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Dx = CSI_Ri_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Class = CSI_Ri_AccOBJ_Class;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Brakelight_Info = CSI_Ri_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Ay = CSI_Ri_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_Ri_AccOBJ_Ax = CSI_Ri_AccOBJ_Ax;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Width = CSI_RiFr_AccOBJ_Width;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Vy = CSI_RiFr_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Vx = CSI_RiFr_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_ObstacleProb = CSI_RiFr_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_HeadingAngle = CSI_RiFr_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_ExistProb = CSI_RiFr_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Dy = CSI_RiFr_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Dx = CSI_RiFr_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Class = CSI_RiFr_AccOBJ_Class;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Brakelight_Info =
      CSI_RiFr_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Ay = CSI_RiFr_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_RiFr_AccOBJ_Ax = CSI_RiFr_AccOBJ_Ax;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Width = CSI_Le_AccOBJ_Width;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Vy = CSI_Le_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Vx = CSI_Le_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_Le_AccOBJ_ObstacleProb = CSI_Le_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_Le_AccOBJ_HeadingAngle = CSI_Le_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_Le_AccOBJ_ExistProb = CSI_Le_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Dy = CSI_Le_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Dx = CSI_Le_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Class = CSI_Le_AccOBJ_Class;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Brakelight_Info = CSI_Le_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Ay = CSI_Le_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_Le_AccOBJ_Ax = CSI_Le_AccOBJ_Ax;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Width = CSI_LeFr_AccOBJ_Width;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Vy = CSI_LeFr_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Vx = CSI_LeFr_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_ObstacleProb = CSI_LeFr_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_HeadingAngle = CSI_LeFr_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_ExistProb = CSI_LeFr_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Dy = CSI_LeFr_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Dx = CSI_LeFr_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Class = CSI_LeFr_AccOBJ_Class;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Brakelight_Info =
      CSI_LeFr_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Ay = CSI_LeFr_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_LeFr_AccOBJ_Ax = CSI_LeFr_AccOBJ_Ax;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Width = CSI_Fr_AccOBJ_Width;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Vy = CSI_Fr_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Vx = CSI_Fr_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_ObstacleProb = CSI_Fr_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Length = CSI_Fr_AccOBJ_Length;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Height = CSI_Fr_AccOBJ_Height;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_HeadingAngle = CSI_Fr_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_ExistProb = CSI_Fr_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Dy = CSI_Fr_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Dx = CSI_Fr_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Class = CSI_Fr_AccOBJ_Class;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Brakelight_Info = CSI_Fr_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Ay = CSI_Fr_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_Fr_AccOBJ_Ax = CSI_Fr_AccOBJ_Ax;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Width = CSI_FrFr_AccOBJ_Width;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Vy = CSI_FrFr_AccOBJ_Vy;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Vx = CSI_FrFr_AccOBJ_Vx;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_ObstacleProb = CSI_FrFr_AccOBJ_ObstacleProb;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_HeadingAngle = CSI_FrFr_AccOBJ_HeadingAngle;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_ExistProb = CSI_FrFr_AccOBJ_ExistProb;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Dy = CSI_FrFr_AccOBJ_Dy;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Dx = CSI_FrFr_AccOBJ_Dx;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Class = CSI_FrFr_AccOBJ_Class;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Brakelight_Info =
      CSI_FrFr_AccOBJ_Brakelight_Info;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Ay = CSI_FrFr_AccOBJ_Ay;
  CSI_ObjectInfo.CSI_FrFr_AccOBJ_Ax = CSI_FrFr_AccOBJ_Ax;

  /* End of Outputs for RootInportFunctionCallGenerator generated from:
   * '<Root>/CameraSigInput_10ms_Runnable' */

  /* Outport: '<Root>/CSI_LaneInfo' */
  Rte_IWrite_CameraSigInput_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo(
      &CSI_LaneInfo);

  /* Outport: '<Root>/CSI_ObjectInfo' */
  Rte_IWrite_CameraSigInput_10ms_Runnable_CSI_ObjectInfo_CSI_ObjectInfo(
      &CSI_ObjectInfo);
}

/* Model initialize function */
void CameraSigInput_Init(void) {
  {
    CSI_LaneInfo_Struct CSI_LaneInfo;
    CSI_ObjectInfo_Struct CSI_ObjectInfo;

    /* SystemInitialize for RootInportFunctionCallGenerator generated from:
     * '<Root>/CameraSigInput_10ms_Runnable' incorporates: SubSystem:
     * '<Root>/CameraSigInput_10ms'
     */
    /* SystemInitialize for BusCreator: '<S4>/Bus Creator' incorporates:
     *  Outport: '<S1>/CSI_LaneInfo'
     */
    CSI_LaneInfo = CameraSigInput_P.CSI_LaneInfo_Y0;

    /* SystemInitialize for BusCreator: '<S5>/Bus Creator' incorporates:
     *  Outport: '<S1>/CSI_ObjectInfo'
     */
    CSI_ObjectInfo = CameraSigInput_P.CSI_ObjectInfo_Y0;

    /* End of SystemInitialize for RootInportFunctionCallGenerator generated
     * from: '<Root>/CameraSigInput_10ms_Runnable' */

    /* SystemInitialize for Outport: '<Root>/CSI_LaneInfo' */
    Rte_IWrite_CameraSigInput_Init_CSI_LaneInfo_CSI_LaneInfo(&CSI_LaneInfo);

    /* SystemInitialize for Outport: '<Root>/CSI_ObjectInfo' */
    Rte_IWrite_CameraSigInput_Init_CSI_ObjectInfo_CSI_ObjectInfo(
        &CSI_ObjectInfo);
  }
}

/*
 * File trailer for generated code.
 *
 * [EOF]
 */
