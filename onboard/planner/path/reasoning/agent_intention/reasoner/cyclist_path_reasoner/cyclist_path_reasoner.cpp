#include "planner/path/reasoning/agent_intention/reasoner/cyclist_path_reasoner/cyclist_path_reasoner.h"

#include <algorithm>
#include <cstdint>
#include <limits>
#include <memory>
#include <string>
#include <utility>
#include <vector>

#include "log_utils/map_macros.h"
#include "planner/path/reasoning/agent_intention/agent_intention_constants.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner_protos/agent_intention_generator_debug.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace path {
namespace {
// A buffer of comfort required lateral gap for scenario that ego
// is overtaking cyclist.
constexpr double kComfortRequiredLateralGapBufferInMeter = 0.05;
// Additional number of snapshot for overtake behavior.
constexpr int kAdditionalNumberOfSnapshotForOvertake = 3;
// A weight for relative longitudinal position between agent and ego.
constexpr double kWeightForRelativeLongitudinalPostion = 0.7;
// Minimum comfort required lateral gap to ensure safery.
constexpr double kMinComfortRequiredLateralGapInMeter = 0.2;
// Maxmum comfort required lateral gap to ensure safery.
constexpr double kMaxComfortRequiredLateralGapInMeter = 1.0;
// Minimum proportion condition for critical required lateral gap for
// overtaking cyclist.
constexpr double kMinCriticalRequiredLateralGapProportion = 0.6;
// The maximum lateral distance that a cyclist overtaking from behind encroachs
// the ego vehicle.
constexpr double kCyclistOvertakingMaxLateralEncroachDistanceInMeter = 0.2;

// If the ego doesn't laterally move toward to the cyclist behind in the source
// lane, we will ignore the cyclist during lane change.
bool ShouldIgnoreCyclistInSourceRegionDuringLaneChange(
    const LaneChangeInfo& lane_change_info, const int64_t object_id,
    const bool is_agent_behind_rear_bumper) {
  if (!is_agent_behind_rear_bumper) {
    return false;
  }
  const LaneChangeObjectInfoList* source_region_object_info_list =
      GetSourceRegionObjectInfoListPtr(lane_change_info);

  if (source_region_object_info_list == nullptr) {
    return false;
  }
  const LaneChangeObjectInfo* obj_ptr =
      source_region_object_info_list->Find(object_id);
  if (obj_ptr == nullptr) {
    return false;
  }
  // If the ego doesn't laterally move toward to the cyclist, we will ignore the
  // cyclist.
  const double agent_corner_signed_lat_dist_to_cross_lane_curve =
      obj_ptr->agent_corner_signed_lat_dist_to_cross_lane_curve;
  const double ego_corner_signed_lat_dist_to_cross_lane_curve =
      lane_change_info.lane_change_metadata
          .ego_corner_signed_lat_dist_to_cross_lane_curve();
  const double ego_agent_min_lateral_distance =
      (ego_corner_signed_lat_dist_to_cross_lane_curve -
       agent_corner_signed_lat_dist_to_cross_lane_curve) *
      (lane_change_info.lane_change_instance.direction() ==
               planner::pb::LaneChangeMode::LEFT_LANE_CHANGE
           ? -1.0
           : 1.0);
  constexpr double kMaxEgoAgentLateralDistanceForIgnoreCycInMeter = 0.2;
  return ego_agent_min_lateral_distance <
         kMaxEgoAgentLateralDistanceForIgnoreCycInMeter;
}

// Returns the longitudinal arclength range for ego.
math::Range1d GetEgoLongitudinalArclengthRange(
    const EgoInLaneParams& ego_param) {
  const double ego_start_s =
      ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m;
  const double ego_end_s =
      ego_param.arclength_m + ego_param.rear_axle_to_front_bumper_m;

  return math::Range1d(ego_start_s, ego_end_s);
}

double GetDurationOfInteractionWithAgent(
    const std::vector<math::Range<int>>& nudge_index_ranges) {
  double duration_of_interaction = 0.0;
  for (const auto& range : nudge_index_ranges) {
    duration_of_interaction += range.end_pos - range.start_pos;
  }
  return duration_of_interaction;
}

// The following two functions will adaptively adjust required lateral gap based
// on relative longitudinal position and duration of interaction. doc:
// https://cooper.didichuxing.com/docs/document/2200506715272
double ComputeRelativeProportion(
    const EgoInLaneParams& ego_param,
    const AgentSnapshotInLaneParam& tracked_inlane_param) {
  // Relative longitudinal position.
  double relative_pos_proportion = 0.0;
  math::Range1d ego_lon_range = GetEgoLongitudinalArclengthRange(ego_param);
  if (tracked_inlane_param.full_body_end_arclength_m <
      ego_lon_range.start_pos) {
    relative_pos_proportion = 0.0;
  } else if (tracked_inlane_param.full_body_start_arclength_m >
             ego_lon_range.end_pos) {
    relative_pos_proportion = 1.0;
  } else {
    // In above doc, there is the relative_pos_proportion calculation method and
    // used it to represent the longitudinal position of the overtaking agent
    // relative to ego.
    relative_pos_proportion =
        (tracked_inlane_param.full_body_end_arclength_m -
         ego_lon_range.start_pos) /
        (ego_lon_range.end_pos + tracked_inlane_param.pose.length() -
         ego_lon_range.start_pos);
  }
  return relative_pos_proportion;
}

double ComputeRelativeProportion(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyParam& object_occupancy_param,
    const double object_length) {
  // Relative longitudinal position.
  double relative_pos_proportion = 0.0;
  math::Range1d ego_lon_range = GetEgoLongitudinalArclengthRange(ego_param);
  if (object_occupancy_param.full_body_end_arclength_m <
      ego_lon_range.start_pos) {
    relative_pos_proportion = 0.0;
  } else if (object_occupancy_param.full_body_start_arclength_m >
             ego_lon_range.end_pos) {
    relative_pos_proportion = 1.0;
  } else {
    // In above doc, there is the relative_pos_proportion calculation method and
    // used it to represent the longitudinal position of the overtaking agent
    // relative to ego.
    relative_pos_proportion =
        (object_occupancy_param.full_body_end_arclength_m -
         ego_lon_range.start_pos) /
        (ego_lon_range.end_pos + object_length - ego_lon_range.start_pos);
  }
  return relative_pos_proportion;
}

void UpdateRequiredLateralGapForCyclistOvertakingEgo(
    const double relative_pos_proportion, const double duration_of_interaction,
    const double plan_init_state_distance,
    const math::pb::Side side_to_ego_centerline,
    const math::CurvatureInfo& curvature_info,
    RequiredLateralGap& required_lat_gap, std::string* debug_str) {
  // Calculate the duration of interaction between ego and agent.
  double interaction_proportion =
      duration_of_interaction / planner::constants::kTrajectoryControlNum;
  // The combined proportion of relative longitudinal position and duration of
  // interaction.
  double combined_proportion =
      kWeightForRelativeLongitudinalPostion * relative_pos_proportion +
      (1 - kWeightForRelativeLongitudinalPostion) * interaction_proportion;

  double curvature_ratio = 0.0;
  // The curvature is positive when the curve turns left (in the
  // counter-clockwise direction) and negative when the curve turns right (in
  // the clockwise direction).
  if ((curvature_info.max_signed_curvature > kMaxCurvatureForStraightLane &&
       side_to_ego_centerline == math::pb::kLeft) ||
      (curvature_info.min_signed_curvature < -kMaxCurvatureForStraightLane &&
       side_to_ego_centerline == math::pb::kRight)) {
    curvature_ratio = std::fabs(curvature_info.max_absolute_curvature) /
                      kMaxCurvatureForStraightLane;
  }

  if (debug_str) {
    absl::StrAppend(
        debug_str,
        absl::StrCat(
            "proportion pos: ", relative_pos_proportion,
            " duration: ", duration_of_interaction,
            " comb: ", combined_proportion, " curvature: ", curvature_ratio,
            ", min_signed_curva: ", curvature_info.min_signed_curvature,
            ", max_signed_curva: ", curvature_info.max_signed_curvature));
  }
  // Ego needs to be cautious when turning near a curve lane.
  const double comfort_req_lat_gap_for_curvature = std::min(
      kMinComfortRequiredLateralGapInMeter * std::max(curvature_ratio, 1.0),
      required_lat_gap.comfort_required_lateral_gap);
  // Many overtaking cyclists have a very close lateral distance to the ego,
  // which can cause a harsh swerve when it just passes the rear axle and the
  // critical required lateral gap is not satisfied. The cyclist of overtaking
  // should ensure a lateral distance from the ego, unless it has already
  // exceeded a large part of the ego at this time, and the ego should also
  // ensure a lateral distance.
  if (relative_pos_proportion < kMinCriticalRequiredLateralGapProportion) {
    required_lat_gap.critical_required_lateral_gap = 0.0;
    required_lat_gap.comfort_required_lateral_gap =
        comfort_req_lat_gap_for_curvature;
    if (debug_str) {
      absl::StrAppend(debug_str,
                      absl::StrCat("| pos_prop < ",
                                   kMinCriticalRequiredLateralGapProportion,
                                   ", cri_rlg = 0"));
    }
    return;
  }

  required_lat_gap.critical_required_lateral_gap =
      std::min(plan_init_state_distance,
               required_lat_gap.critical_required_lateral_gap) *
      combined_proportion;
  required_lat_gap.comfort_required_lateral_gap =
      std::max(std::min(plan_init_state_distance,
                        required_lat_gap.comfort_required_lateral_gap) *
                   combined_proportion,
               comfort_req_lat_gap_for_curvature);
}

// For an agent that forcefully cuts in near the ego vehicle, use a hard
// constraint for emergency lateral avoidance if side lane is safe.
bool NeedHardNudgeForForcefullyCutinAgent(
    const pb::SnapshotIntention::PassState pass_decision,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const pb::TrafficRuleReasoningInfoDebug& traffic_rule_reasoning_info,
    double ego_cross_track_speed_mps, double agent_cross_track_speed_mps,
    double* safe_lateral_distance) {
  if (pass_decision == pb::SnapshotIntention::IGNORE) {
    return false;
  }

  // Only consider the agent that overlaps the ego front bumper.
  if (reasoning_info.is_agent_behind_front_bumper()) {
    return false;
  }

  // Only consider the cut in agent within near distance.
  if (!(reasoning_info.is_cut_in() || reasoning_info.is_same_direction()) ||
      !reasoning_info.is_agent_within_comfort()) {
    return false;
  }

  // When the agent shows a tendency to rush towards the ego vehicle and the
  // nudge side is safe, calculate the safe distance between the agent and the
  // ego vehicle.
  auto rss_config = path::RssConfig();
  rss_config.lateral_miu = 0.1;  // meters
  if (pass_decision == pb::SnapshotIntention::PASS_LEFT &&
      traffic_rule_reasoning_info.is_left_side_lane_safe() &&
      reasoning_info.recent_average_cross_track_accel() > 0.2) {
    *safe_lateral_distance = path::CalculateSafeLateralDistance(
        ego_cross_track_speed_mps, agent_cross_track_speed_mps,
        /*is_left_of_ego=*/false, rss_config);
  } else if (pass_decision == pb::SnapshotIntention::PASS_RIGHT &&
             traffic_rule_reasoning_info.is_right_side_lane_safe() &&
             reasoning_info.recent_average_cross_track_accel() < -0.2) {
    *safe_lateral_distance = path::CalculateSafeLateralDistance(
        ego_cross_track_speed_mps, agent_cross_track_speed_mps,
        /*is_left_of_ego=*/true, rss_config);
  }

  // Returns true when there is not enough safe distance.
  return reasoning_info.plan_init_state_distance() < *safe_lateral_distance;
}

// For cyclist overtaking repulsion, if ego is in lane, it is expected to
// slightly yield space for overtaking cyclist, but without outside the
// lane. If the ego vehicle is not in the current lane, its right of way is
// relatively low, so don't set lateral bound, it is expected to maintain a safe
// lateral distance.
std::optional<double> CaculateLateralBoundForCyclistOvertakingRepulsion(
    const EgoInLaneParams& ego_param,
    RepulsionMetaData::RepulsionDirection repulsion_direction,
    bool is_ego_in_lane, bool is_squeezing_scene, double current_lane_width) {
  if (!is_ego_in_lane) {
    return std::nullopt;
  }

  const double max_lateral_repulsion_encroach_on_path =
      is_squeezing_scene ? 0.0
                         : kCyclistOvertakingMaxLateralEncroachDistanceInMeter;
  if (is_squeezing_scene) {
    rt_event::PostRtEvent<
        rt_event::planner::PlannerSqueezingSceneWithCyclistOvertaking>();
  }

  return repulsion_direction == RepulsionMetaData::RepulsionDirection::kLeft
             ? std::min(0.5 * current_lane_width - ego_param.width_m,
                        ego_param.relative_lateral_distance_to_line_center -
                            0.5 * ego_param.width_m +
                            max_lateral_repulsion_encroach_on_path)
             : std::max(-0.5 * current_lane_width + ego_param.width_m,
                        ego_param.relative_lateral_distance_to_line_center +
                            0.5 * ego_param.width_m -
                            max_lateral_repulsion_encroach_on_path);
}

bool IsSqueezeSceneWithOvertakingCyclistOnBothSides(
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results) {
  if (frame_analyzer_results.empty()) {
    return false;
  }

  const auto& ego_lane_frame_result =
      frame_analyzer_results[pb::FrameAnalysis::IN_LANE];
  const bool has_left_overtaking_cyclists =
      !ego_lane_frame_result.left_overtaking_cyclist_ids.empty();
  const bool has_right_overtaking_cyclists =
      !ego_lane_frame_result.right_overtaking_cyclist_ids.empty();
  return has_left_overtaking_cyclists && has_right_overtaking_cyclists;
}

}  // namespace

bool CyclistPathReasoner::PreReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;
  if (ShouldIgnoreCyclistInSourceRegionDuringLaneChange(
          lane_change_info_, agent_inlane_state.object_id,
          blocking_state_data.reasoning_info.is_agent_behind_rear_bumper())) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    return true;
  }

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(
          agent_inlane_state, blocking_state_data, trajectory_intention_data);
  }
}

bool CyclistPathReasoner::PreReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const AgentSLBoundary& /* object_sl_boundary */,
    TrajectoryIntentionMetaData& trajectory_intention_data) const {
  trajectory_intention_data.reasoner_id = reasoner_id_;

  if (ShouldIgnoreCyclistInSourceRegionDuringLaneChange(
          lane_change_info_, object_occupancy_state.object_id(),
          blocking_state_data.reasoning_info.is_agent_behind_rear_bumper())) {
    trajectory_intention_data.intention_type = pb::TrajectoryState::IGNORE;
    return true;
  }

  switch (blocking_state_data.blocking_sequence_type) {
    case pb::BlockingSequenceType::kNonBlocking:
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
    case pb::BlockingSequenceType::kPartialBlocking:
      return PreReasonForSoftBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kCompleteBlocking:
      return PreReasonForHardBlockingSequences(object_occupancy_state,
                                               blocking_state_data,
                                               trajectory_intention_data);
    case pb::BlockingSequenceType::kMixedBlocking:
      return PreReasonForMixedBlockingSequences(object_occupancy_state,
                                                blocking_state_data,
                                                trajectory_intention_data);
    default:
      DCHECK(false) << "unsupported blocking sequence type";
      return PreReasonForNonBlockingSequences(object_occupancy_state,
                                              blocking_state_data,
                                              trajectory_intention_data);
  }
}

bool CyclistPathReasoner::ShouldInvokePreRepulsionReason(
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    voy::perception::ObjectType agent_type, pb::BehaviorType behavior_type,
    bool is_stationary, pb::AgentImaginaryActualType imaginary_actual_type,
    std::string& debug_str) const {
  (void)agent_type;
  (void)is_stationary;
  (void)imaginary_actual_type;
  // In the waypoint scenario, the smoothness of the reference line may not meet
  // the requirements for generating the wrap repulsion polyline.
  // TODO(wanghao): Will be enabled after the issue is fixed in the future.
  if (lane_keep_behavior_type_ == pb::LaneKeepBehaviorType::LK_RA_WAYPOINT &&
      !FLAGS_planning_enable_waypoint_cyclist_overtaking_repulsion) {
    absl::StrAppend(&debug_str, "Not support WAYPOINT behavior\n");
    return false;
  }

  // In scenarios where the ego vehicle actively crosses lanes, if agent is
  // located behind ego rear bumper, there is no need to add repulsion. The
  // repulsion from rear_end_agent_path_reasoner is used to ensure safety.
  if ((behavior_type != pb::BehaviorType::LANE_KEEP ||
       lane_keep_behavior_type_ != pb::LaneKeepBehaviorType::LK_DEFAULT) &&
      blocking_state_meta_data.reasoning_info.is_agent_behind_rear_bumper()) {
    absl::StrAppend(&debug_str,
                    "Ego actively cross lane and agent behind rear bumper");
    return false;
  }
  return blocking_state_meta_data.reasoning_info.is_cyclist_overtaking();
}

bool CyclistPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  // Currently, oncoming cyclist doesn't need special post-processing.
  // It will be handled by fallback post-processing.
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  intention_result.reasoner_id = reasoner_id_;
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          tracked_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego(),
          tracked_param.left_inlane_clearance_m,
          tracked_param.right_inlane_clearance_m);

  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      tracked_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update cyc RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, agent_inlane_state, ego_param_.arclength_m,
      buffer_before_agent_meters);
  MaybeAdjustCycLateralGapBasedOnSpeed(
      traffic_rule_reasoning_info_, tracked_param.along_track_speed_mps,
      active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
      intention_result.lateral_decision,
      blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
      agent_inlane_state.agent_metadata.agent_type,
      intention_result.required_lat_gap, &(intention_result.debug_str));

  // For the vast majority of cyclists overtaking, we expect to use soft
  // constraints(repulsion) instead of hard constraints(nudge corridor).
  // However, for agents that forcefully cut in, when there is safe space on one
  // side, hard constraints should be used to ensure safety.
  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    intention_result.is_overtaken = false;
    double safe_lateral_distance = std::numeric_limits<double>::min();
    if (lane_keep_behavior_type_ != pb::LaneKeepBehaviorType::LK_RA_WAYPOINT ||
        FLAGS_planning_enable_waypoint_cyclist_overtaking_repulsion) {
      if (!NeedHardNudgeForForcefullyCutinAgent(
              tracked_pass_decision, blocking_state_data.reasoning_info,
              traffic_rule_reasoning_info_, ego_param_.cross_track_speed_mps,
              tracked_param.cross_track_speed_mps, &safe_lateral_distance)) {
        intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
        return true;
      }
    } else {
      absl::StrAppend(&intention_result.debug_str,
                      "Keep the nudge in WAYPOINT behavior\n");
    }

    AdjustIntentionResultForYieldDecision(
        agent_inlane_state.tracked_state.inlane_param,
        blocking_state_data.reasoning_info,
        trajectory_intention_data.blocking_sequence_type,
        trajectory_intention_data.blocking_sequences, intention_result);
    intention_result.required_lat_gap.comfort_required_lateral_gap = std::max(
        intention_result.required_lat_gap.comfort_required_lateral_gap,
        std::min(safe_lateral_distance, kMaxComfortRequiredLateralGapInMeter));
  }

  // Treat as a static agent for scenarios without nudge intention.
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                   blocking_state_data.reasoning_info)) {
    intention_result.is_static = true;
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_dec igonre, treat as staic; ");
    AddCurrentPoseNominalConstraint(agent_inlane_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }

  // Excluding overtaking cyclist, for cyclist that longitudinal decision is
  // pass, we expect to overtake nudge them.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      !blocking_state_data.reasoning_info.is_cyclist_overtaking() &&
      intention_result.longitudinal_decision ==
          speed::pb::SpeedDecision::PASS) {
    AdjustIntentionResultForPassDecision(
        agent_inlane_state, tracked_pass_decision, intention_result);
    intention_result.is_overtaken = true;
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    if (ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }
  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  if (ShouldCrossLane(blocking_state_data)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kCrossLane;
    intention_result.is_lane_encroach_intent_agent = true;
  }
  return true;
}

bool CyclistPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& trajectory_intention_data,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  // Currently, oncoming cyclist doesn't need special post-processing.
  // It will be handled by fallback post-processing.
  DCHECK(!blocking_state_data.reasoning_info.is_oncoming());
  intention_result.reasoner_id = reasoner_id_;
  const auto& object_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          object_occupancy_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());

  AdjustLateralGapBasedOnLateralMoveDirection(
      lane_change_info_, blocking_state_data.object_id,
      intention_result.lateral_decision, active_lat_gap_adjust_meta_,
      object_occupancy_param.speed_mps,
      blocking_state_data.reasoning_info
          .is_interested_agent_during_lane_change_or_abort(),
      intention_result.required_lat_gap, &intention_result.debug_str);

  // Update cyc RLG based on speed.
  const double buffer_before_agent_meters =
      ego_param_.rear_axle_to_front_bumper_m;
  std::optional<double> ego_speed_near_agent = GetMaxEgoSpeedNearAgent(
      reusable_last_speed_profile_, object_occupancy_state,
      ego_param_.arclength_m, buffer_before_agent_meters);
  MaybeAdjustCycLateralGapBasedOnSpeed(
      traffic_rule_reasoning_info_,
      object_occupancy_param.along_track_speed_mps,
      active_lat_gap_adjust_meta_.ego_speed_mps, ego_speed_near_agent,
      intention_result.lateral_decision,
      blocking_state_data.reasoning_info.is_agent_behind_rear_bumper(),
      object_occupancy_state.object_type(), intention_result.required_lat_gap,
      &(intention_result.debug_str));

  // For the vast majority of cyclists overtaking, we expect to use soft
  // constraints(repulsion) instead of hard constraints(nudge corridor).
  // However, for agents that forcefully cut in, when there is safe space on one
  // side, hard constraints should be used to ensure safety.
  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    intention_result.is_overtaken = false;
    double safe_lateral_distance = std::numeric_limits<double>::min();
    if (lane_keep_behavior_type_ != pb::LaneKeepBehaviorType::LK_RA_WAYPOINT ||
        FLAGS_planning_enable_waypoint_cyclist_overtaking_repulsion) {
      if (!NeedHardNudgeForForcefullyCutinAgent(
              tracked_pass_decision, blocking_state_data.reasoning_info,
              traffic_rule_reasoning_info_, ego_param_.cross_track_speed_mps,
              object_occupancy_param.cross_track_speed_mps,
              &safe_lateral_distance)) {
        intention_result.lateral_decision = pb::SnapshotIntention::IGNORE;
        return true;
      }
    } else {
      absl::StrAppend(&intention_result.debug_str,
                      "Keep the nudge in WAYPOINT behavior\n");
    }
    AdjustIntentionResultForYieldDecision(
        object_occupancy_state, blocking_state_data.reasoning_info,
        trajectory_intention_data.blocking_sequence_type,
        trajectory_intention_data.blocking_sequences, intention_result);
    intention_result.required_lat_gap.comfort_required_lateral_gap = std::max(
        intention_result.required_lat_gap.comfort_required_lateral_gap,
        std::min(safe_lateral_distance, kMaxComfortRequiredLateralGapInMeter));
  }

  // Treat as a static agent for scenarios without nudge intention.
  if (intention_result.lateral_decision == pb::SnapshotIntention::IGNORE &&
      tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                   blocking_state_data.reasoning_info)) {
    intention_result.is_static = true;
    intention_result.is_overtaken = false;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_dec igonre, treat as staic; ");
    AddCurrentPoseNominalConstraint(object_occupancy_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
        ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }

  // Excluding overtaking cyclist, for cyclist that longitudinal decision is
  // pass, we expect to overtake nudge them.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      !blocking_state_data.reasoning_info.is_cyclist_overtaking() &&
      intention_result.longitudinal_decision ==
          speed::pb::SpeedDecision::PASS) {
    AdjustIntentionResultForPassDecision(
        object_occupancy_state, tracked_pass_decision, intention_result);
    intention_result.is_overtaken = true;
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
        ShouldCrossLane(blocking_state_data)) {
      intention_result.solid_line_lane_boundary_decision =
          LaneBoundaryDecision::kCrossLane;
      intention_result.is_lane_encroach_intent_agent = true;
    }
    return true;
  }
  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldCrossLane(blocking_state_data)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kCrossLane;
    intention_result.is_lane_encroach_intent_agent = true;
  }
  return true;
}

void CyclistPathReasoner::AdjustIntentionResultForPassDecision(
    const AgentInLaneStates& agent_inlane_state,
    const pb::SnapshotIntention::PassState& /* tracked_pass_decision */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.nudge_index_ranges.empty());
  // Increase required lateral gap.
  // Note(wanghao): This buffer is beneficial for speed model to make
  // overtaking decisions.
  intention_result.required_lat_gap.comfort_required_lateral_gap +=
      kComfortRequiredLateralGapBufferInMeter;

  DCHECK(!agent_inlane_state.predicted_trajectories.empty());
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  DCHECK(!predicted_states.empty());

  // Increase the number of snapshot.
  // Note(wanghao): There is currently no separate requirement for
  // post-reasoning soft and hard blocking, so there is only 1
  // nudge_index_range.
  DCHECK_EQ(intention_result.nudge_index_ranges.size(), 1);
  int start_nudge_idx = intention_result.nudge_index_ranges.front().start_pos;
  int end_nudge_idx = intention_result.nudge_index_ranges.front().end_pos;
  // Step_1: reduce start pos.
  for (int i = 0; i < kAdditionalNumberOfSnapshotForOvertake; ++i) {
    if (start_nudge_idx == 0) {
      break;
    }
    --start_nudge_idx;
    if (!HasEnoughDistanceBasedEgoWidthGivenReqLatGap(
            predicted_states[start_nudge_idx]
                .inlane_state.inlane_param.cross_track_encroachment_m,
            predicted_states[start_nudge_idx]
                .inlane_state.inlane_param.min_lane_width,
            0.5 * ego_param_.width_m,
            intention_result.required_lat_gap.comfort_required_lateral_gap)) {
      ++start_nudge_idx;
      break;
    }
  }
  // Step_2: increase end pos.
  for (int i = 0; i < kAdditionalNumberOfSnapshotForOvertake; ++i) {
    if (end_nudge_idx + 1 == static_cast<int>(predicted_states.size())) {
      break;
    }
    ++end_nudge_idx;
    if (!HasEnoughDistanceBasedEgoWidthGivenReqLatGap(
            predicted_states[start_nudge_idx]
                .inlane_state.inlane_param.cross_track_encroachment_m,
            predicted_states[start_nudge_idx]
                .inlane_state.inlane_param.min_lane_width,
            0.5 * ego_param_.width_m,
            intention_result.required_lat_gap.comfort_required_lateral_gap)) {
      --end_nudge_idx;
      break;
    }
  }
  intention_result.nudge_index_ranges.front().start_pos = start_nudge_idx;
  intention_result.nudge_index_ranges.front().end_pos = end_nudge_idx;
  absl::StrAppend(&intention_result.debug_str,
                  "Nudge pass increase rlg and snapshots");
}

void CyclistPathReasoner::AdjustIntentionResultForPassDecision(
    const ObjectOccupancyState& object_occupancy_state,
    const pb::SnapshotIntention::PassState& /* tracked_pass_decision */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.nudge_index_ranges.empty());
  // Increase required lateral gap.
  // Note(wanghao): This buffer is beneficial for speed model to make
  // overtaking decisions.
  intention_result.required_lat_gap.comfort_required_lateral_gap +=
      kComfortRequiredLateralGapBufferInMeter;

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  DCHECK(!predicted_states.empty());

  // Increase the number of snapshot.
  // Note(wanghao): There is currently no separate requirement for
  // post-reasoning soft and hard blocking, so there is only 1
  // nudge_index_range.
  DCHECK_EQ(intention_result.nudge_index_ranges.size(), 1);
  int start_nudge_idx = intention_result.nudge_index_ranges.front().start_pos;
  int end_nudge_idx = intention_result.nudge_index_ranges.front().end_pos;
  // Step_1: reduce start pos.
  for (int i = 0; i < kAdditionalNumberOfSnapshotForOvertake; ++i) {
    if (start_nudge_idx == 0) {
      break;
    }
    --start_nudge_idx;
    if (!HasEnoughDistanceBasedEgoWidthGivenReqLatGap(
            predicted_states[start_nudge_idx]
                .object_occupancy_param()
                .cross_track_encroachment_m,
            predicted_states[start_nudge_idx]
                .object_occupancy_param()
                .min_corridor_width_m,
            0.5 * ego_param_.width_m,
            intention_result.required_lat_gap.comfort_required_lateral_gap)) {
      ++start_nudge_idx;
      break;
    }
  }
  // Step_2: increase end pos.
  for (int i = 0; i < kAdditionalNumberOfSnapshotForOvertake; ++i) {
    if (end_nudge_idx + 1 == static_cast<int>(predicted_states.size())) {
      break;
    }
    ++end_nudge_idx;
    if (!HasEnoughDistanceBasedEgoWidthGivenReqLatGap(
            predicted_states[start_nudge_idx]
                .object_occupancy_param()
                .cross_track_encroachment_m,
            predicted_states[start_nudge_idx]
                .object_occupancy_param()
                .min_corridor_width_m,
            0.5 * ego_param_.width_m,
            intention_result.required_lat_gap.comfort_required_lateral_gap)) {
      --end_nudge_idx;
      break;
    }
  }
  intention_result.nudge_index_ranges.front().start_pos = start_nudge_idx;
  intention_result.nudge_index_ranges.front().end_pos = end_nudge_idx;
  absl::StrAppend(&intention_result.debug_str,
                  "Nudge pass increase rlg and snapshots");
}

void CyclistPathReasoner::AdjustIntentionResultForYieldDecision(
    const AgentSnapshotInLaneParam& inlane_param,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const pb::BlockingSequenceType& blocking_sequence_type,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    IntentionResultMetaData& intention_result) const {
  double duration_of_inteaction =
      GetDurationOfInteractionWithAgent(intention_result.nudge_index_ranges);
  // Reduce required lateral gap based on distance between agent and ego.
  UpdateRequiredLateralGapForCyclistOvertakingEgo(
      ComputeRelativeProportion(ego_param_, inlane_param),
      duration_of_inteaction, reasoning_info.plan_init_state_distance(),
      reasoning_info.oriented_side_to_ego_centerline(),
      ego_param_.nearby_curvature_info, intention_result.required_lat_gap,
      &(intention_result.debug_str));
  // For mixed blocking, we only nudge the first soft bloking sanpshots.
  if (blocking_sequence_type == pb::BlockingSequenceType::kMixedBlocking) {
    std::vector<math::Range<int>> soft_nudge_index_ranges =
        GetEffectiveNudgeIndexRanges(
            intention_result.nudge_index_ranges, blocking_sequences,
            /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);
    if (!soft_nudge_index_ranges.empty()) {
      intention_result.nudge_index_ranges.clear();
      intention_result.nudge_index_ranges.emplace_back(
          soft_nudge_index_ranges.front());
    }
  }
}

void CyclistPathReasoner::AdjustIntentionResultForYieldDecision(
    const ObjectOccupancyState& object_occupancy_state,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const pb::BlockingSequenceType& blocking_sequence_type,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    IntentionResultMetaData& intention_result) const {
  double duration_of_inteaction =
      GetDurationOfInteractionWithAgent(intention_result.nudge_index_ranges);
  // Reduce required lateral gap based on distance between agent and ego.
  UpdateRequiredLateralGapForCyclistOvertakingEgo(
      ComputeRelativeProportion(ego_param_,
                                object_occupancy_state.current_snapshot_info()
                                    .object_occupancy_param(),
                                object_occupancy_state.planner_object()
                                    .pose_at_plan_init_ts()
                                    .length()),
      duration_of_inteaction, reasoning_info.plan_init_state_distance(),
      reasoning_info.oriented_side_to_ego_centerline(),
      ego_param_.nearby_curvature_info, intention_result.required_lat_gap,
      &(intention_result.debug_str));
  // For mixed blocking, we only nudge the first soft bloking sanpshots.
  if (blocking_sequence_type == pb::BlockingSequenceType::kMixedBlocking) {
    std::vector<math::Range<int>> soft_nudge_index_ranges =
        GetEffectiveNudgeIndexRanges(
            intention_result.nudge_index_ranges, blocking_sequences,
            /*blocking_state*/ pb::LateralBlockingState::SOFT_BLOCKING);
    if (!soft_nudge_index_ranges.empty()) {
      intention_result.nudge_index_ranges.clear();
      intention_result.nudge_index_ranges.emplace_back(
          soft_nudge_index_ranges.front());
    }
  }
}

std::unique_ptr<RepulsionMetaData> CyclistPathReasoner::PreRepulsionReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& reusable_last_path,
    const RequiredLateralGap& required_lat_gap,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    std::string& debug_str) const {
  rt_event::PostRtEvent<
      rt_event::planner::PlannerRepulsionCyclistOvertakingTrigger>();
  DCHECK(blocking_state_data.reasoning_info.is_cyclist_overtaking());
  const auto& tracked_param = agent_inlane_state.tracked_state.inlane_param;
  const bool is_squeezing_scene =
      IsSqueezeSceneWithOvertakingCyclistOnBothSides(frame_analyzer_results);
  // In the squeezing scenario where there are overtaking cyclists on both the
  // left and right sides, we cannot ignore the cyclists on one side. Otherwise,
  // it is easy to squeeze the cyclists on that side.
  if (!is_squeezing_scene && traffic_rule_reasoning_info_.is_ego_in_lane() &&
      IsIgnorableBehindAgent(blocking_state_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    absl::StrAppend(&debug_str, "Ignorable behind agent");
    return nullptr;
  }
  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(reusable_last_path,
                                                tracked_param.pose.center_2d());
  if (!repulsion_direction.has_value()) {
    // If we don't have a reusable path, we use the tracked side to determine
    // the repulsion direction.
    const pb::SnapshotIntention::PassState tracked_pass_decision =
        GetPassStateWithAgentTrackedState(
            tracked_param.center_line_side,
            blocking_state_data.reasoning_info.tracked_side_to_ego());
    if (tracked_pass_decision == pb::SnapshotIntention::IGNORE) {
      absl::StrAppend(&debug_str, "no valid repulsion direction");
      return nullptr;
    }

    repulsion_direction =
        tracked_pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? RepulsionMetaData::RepulsionDirection::kLeft
            : RepulsionMetaData::RepulsionDirection::kRight;
  }

  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id)
          .predicted_states;
  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  if (blocking_state_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "have no effective snapshots");
    return nullptr;
  }

  // Note(wanghao): The 'agent_center_path_points' is used to calculate the
  // offset distance and heading of each state in the 'speed_profile' relative
  // to the starting point of the 'agent_center_path_points'. Trimming the
  // 'agent_center_path_points' would shift the coordinate origin for states in
  // the 'speed_profile'. Therefore, we only allow trimming the 'speed_profile'
  // (corresponding to the st region in st planner) while prohibiting any
  // modification to the 'agent_center_path_points'.
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(
        state.inlane_state.inlane_param.pose.center_2d());
  }

  for (const pb::BlockingSequence& blocking_sequence :
       blocking_state_data.blocking_sequences) {
    DCHECK_NE(blocking_sequence.blocking_state(),
              pb::LateralBlockingState::NON_BLOCKING);
    for (size_t i =
             static_cast<size_t>(blocking_sequence.range().start_index());
         i < static_cast<size_t>(blocking_sequence.range().end_index()); ++i) {
      DCHECK_LT(i, predicted_states.size());
      speed_profile.emplace_back(
          math::Ms2Sec(predicted_states[i].timestamp),
          predicted_states[i].odom_m,
          predicted_states[i].inlane_state.inlane_param.along_track_speed_mps,
          /*a_in=*/0.0, /*j_in=*/0.0);
    }
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return nullptr;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return nullptr;
  }

  RequiredLateralGap adjusted_req_lat_gap = required_lat_gap;
  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    // Reduce required lateral gap based on distance between agent and ego.
    UpdateRequiredLateralGapForCyclistOvertakingEgo(
        ComputeRelativeProportion(ego_param_, tracked_param),
        /*duration_of_inteaction=*/0.0,
        blocking_state_data.reasoning_info.plan_init_state_distance(),
        blocking_state_data.reasoning_info.oriented_side_to_ego_centerline(),
        ego_param_.nearby_curvature_info, adjusted_req_lat_gap, &debug_str);
  }

  return std::make_unique<RepulsionMetaData>(
      *repulsion_direction, tracked_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile, tracked_param.pose.heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/true, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/1.0,
      /*repulsion_required_lat_gap=*/
      adjusted_req_lat_gap.comfort_required_lateral_gap, reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      agent_inlane_state.primary_trajectory_id,
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      CaculateLateralBoundForCyclistOvertakingRepulsion(
          ego_param_, *repulsion_direction,
          traffic_rule_reasoning_info_.is_ego_in_lane(), is_squeezing_scene,
          tracked_param.min_lane_width),
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/st_buffer_config_);
}

std::unique_ptr<RepulsionMetaData> CyclistPathReasoner::PreRepulsionReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& reusable_last_path,
    const RequiredLateralGap& required_lat_gap,
    const std::vector<path::FrameAnalyzerResult>& frame_analyzer_results,
    std::string& debug_str) const {
  rt_event::PostRtEvent<
      rt_event::planner::PlannerRepulsionCyclistOvertakingTrigger>();
  DCHECK(blocking_state_data.reasoning_info.is_cyclist_overtaking());
  const ObjectOccupancyParam& object_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  const bool is_squeezing_scene =
      IsSqueezeSceneWithOvertakingCyclistOnBothSides(frame_analyzer_results);
  // In the squeezing scenario where there are overtaking cyclists on both the
  // left and right sides, we cannot ignore the cyclists on one side. Otherwise,
  // it is easy to squeeze the cyclists on that side.
  if (!is_squeezing_scene && traffic_rule_reasoning_info_.is_ego_in_lane() &&
      IsIgnorableBehindAgent(blocking_state_data.reasoning_info,
                             scenario_identify_result_,
                             traffic_rule_reasoning_info_)) {
    absl::StrAppend(&debug_str, "Ignorable behind agent");
    return nullptr;
  }
  std::optional<RepulsionMetaData::RepulsionDirection> repulsion_direction =
      GetRepulsionDirectionBasedOnLastPathCurve(
          reusable_last_path, object_occupancy_state.pose().center_2d());
  if (!repulsion_direction.has_value()) {
    // If we don't have a reusable path, we use the tracked side to determine
    // the repulsion direction.
    const pb::SnapshotIntention::PassState tracked_pass_decision =
        GetPassStateWithAgentTrackedState(
            object_occupancy_param.center_line_side,
            blocking_state_data.reasoning_info.tracked_side_to_ego());
    if (tracked_pass_decision == pb::SnapshotIntention::IGNORE) {
      absl::StrAppend(&debug_str, "no valid repulsion direction");
      return nullptr;
    }

    repulsion_direction =
        tracked_pass_decision == pb::SnapshotIntention::PASS_LEFT
            ? RepulsionMetaData::RepulsionDirection::kLeft
            : RepulsionMetaData::RepulsionDirection::kRight;
  }

  speed::Profile speed_profile;
  math::geometry::Polyline2d agent_center_path_points;
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          object_occupancy_state.primary_trajectory_id())
          .predicted_states();
  // Constructs agent path and speed profile based on snapshots with
  // spatiotemporal overlap.
  if (blocking_state_data.blocking_sequence_type ==
      pb::BlockingSequenceType::kNonBlocking) {
    absl::StrAppend(&debug_str, "have no effective snapshots");
    return nullptr;
  }

  // Note(wanghao): The 'agent_center_path_points' is used to calculate the
  // offset distance and heading of each state in the 'speed_profile' relative
  // to the starting point of the 'agent_center_path_points'. Trimming the
  // 'agent_center_path_points' would shift the coordinate origin for states in
  // the 'speed_profile'. Therefore, we only allow trimming the 'speed_profile'
  // (corresponding to the st region in st planner) while prohibiting any
  // modification to the 'agent_center_path_points'.
  for (const auto& state : predicted_states) {
    agent_center_path_points.emplace_back(state.pose().center_2d());
  }

  for (const pb::BlockingSequence& blocking_sequence :
       blocking_state_data.blocking_sequences) {
    DCHECK_NE(blocking_sequence.blocking_state(),
              pb::LateralBlockingState::NON_BLOCKING);
    for (size_t i =
             static_cast<size_t>(blocking_sequence.range().start_index());
         i < static_cast<size_t>(blocking_sequence.range().end_index()); ++i) {
      DCHECK_LT(i, predicted_states.size());
      speed_profile.emplace_back(
          math::Ms2Sec(predicted_states[i].timestamp()),
          predicted_states[i].odom_m().value(),
          predicted_states[i].object_occupancy_param().along_track_speed_mps,
          /*a_in=*/0.0, /*j_in=*/0.0);
    }
  }

  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(std::move(agent_center_path_points),
                               math::constants::kDefaultSimplifyThreshold,
                               /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    absl::StrAppend(&debug_str, "simplified agent path points < 2");
    return nullptr;
  }

  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    absl::StrAppend(&debug_str, absl::StrCat("simplified agent path length < ",
                                             kMinLengthToAddRepulsion));
    return nullptr;
  }

  RequiredLateralGap adjusted_req_lat_gap = required_lat_gap;
  if (blocking_state_data.reasoning_info.is_cyclist_overtaking()) {
    // Reduce required lateral gap based on distance between agent and ego.
    UpdateRequiredLateralGapForCyclistOvertakingEgo(
        ComputeRelativeProportion(ego_param_, object_occupancy_param,
                                  object_occupancy_state.planner_object()
                                      .pose_at_plan_init_ts()
                                      .length()),
        /*duration_of_inteaction=*/0.0,
        blocking_state_data.reasoning_info.plan_init_state_distance(),
        blocking_state_data.reasoning_info.oriented_side_to_ego_centerline(),
        ego_param_.nearby_curvature_info, adjusted_req_lat_gap, &debug_str);
  }

  return std::make_unique<RepulsionMetaData>(
      *repulsion_direction, object_occupancy_state.pose().contour().polygon(),
      std::move(simplified_agent_center_path),
      /*profile_lower_bound=*/speed_profile,
      /*profile_upper_bound=*/speed_profile,
      object_occupancy_state.pose().heading(),
      /*add_only_overtaking=*/false,
      /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/true, /*ignore_if_nudge_yield_in=*/false,
      /*strength_in=*/path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/1.0,
      /*repulsion_required_lat_gap=*/
      adjusted_req_lat_gap.comfort_required_lateral_gap, reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      object_occupancy_state.primary_trajectory_id(),
      /*path_risk_mitigation_mode_in=*/false,
      /*max_abs_lateral_accel_in=*/std::nullopt,
      /*max_abs_lateral_juke_in=*/std::nullopt,
      /*lateral_bound_in=*/
      CaculateLateralBoundForCyclistOvertakingRepulsion(
          ego_param_, *repulsion_direction,
          traffic_rule_reasoning_info_.is_ego_in_lane(),
          /*is_squeeze_scene=*/is_squeezing_scene,
          object_occupancy_param.min_corridor_width_m),
      RepulsionMetaData::GuideProfileType::kBestSTRollout,
      /*customized_guide_profile_in=*/std::nullopt,
      RepulsionMetaData::ExtendBoundaryLevel::kNA,
      /*clearance_in=*/std::nullopt,
      /*max_extend_boundary_distance_in=*/std::nullopt,
      /*st_buffer_in=*/st_buffer_config_);
}

}  // namespace path
}  // namespace planner
