#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_PREDICTED_TRAJECTORY_ROUTE_ASSOCIATION_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_PREDICTED_TRAJECTORY_ROUTE_ASSOCIATION_H_

#include <limits>
#include <list>
#include <unordered_map>
#include <utility>
#include <vector>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association/approximate_map_query_util.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_route_association_common.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner_protos/agent_map_element_occupancy_seed.pb.h"
#include "planner_protos/behavior_reasoner.pb.h"
#include "planner_protos/world_model_debug.pb.h"
#include "pnc_map_service/joint_pnc_map_service.h"
#include "pnc_map_service/map_elements/lane.h"

namespace planner {

namespace route_association {

// Class which is to find a sequence of map elements representing
// the route of agent predicted trajectory.
class PredictedTrajectoryRouteAssociator {
 public:
  PredictedTrajectoryRouteAssociator(
      const PredictedTrajectoryWrapper& predicted_trajectory,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const pb::AgentMapElementOccupancySeeds&
          agent_map_element_occupancy_seeds,
      planner::pb::WorldModelDebug* debug_proto_ptr);

  PredictedTrajectoryRouteAssociator(
      const PredictedTrajectoryWrapper& predicted_trajectory,
      const std::vector<int>& sampled_pose_indices,
      const pnc_map::JointPncMapService& joint_pnc_map_service,
      const pb::AgentMapElementOccupancySeeds&
          agent_map_element_occupancy_seeds,
      planner::pb::WorldModelDebug* debug_proto_ptr);

  int64_t object_id() const { return object_id_; }

  int64_t trajectory_id() const { return trajectory_id_; }

  const std::vector<MapElementAndPoseInfo>& most_likely_route() const {
    return most_likely_route_;
  }

  const std::vector<MapElementAndPoseInfo>&
  raw_equally_optimal_first_map_elements() const {
    return raw_equally_optimal_first_map_elements_;
  }

  const std::optional<MapElementAndPoseInfo>&
  partial_route_inferred_by_the_first_pose_opt() const {
    return partial_route_inferred_by_the_first_pose_opt_;
  }

  const std::optional<MapElementAndPoseInfo>&
  latest_exit_zone_info_in_seed_opt() const {
    return latest_exit_zone_info_in_seed_opt_;
  }

  const std::optional<MapElementAndPoseInfo>&
  latest_isolated_lane_info_in_seed_opt() const {
    return latest_isolated_lane_info_in_seed_opt_;
  }

  void GetOrCreateMultiPredictedTrajectoryRouteAssociationDebug(
      planner::pb::AgentPredictedTrajectoryRoutesDebug*
          agent_association_debug_ptr) const;

 private:
  using AssociationType = pb::AgentPredictedTrajectoryRouteAssociationType;
  // Struct holds all queried map element infos of a sampled trajectory pose.
  struct SampledPoseInfo {
    int sampled_pose_index_in_trajectory = 0;
    std::vector<const MapElementAndPoseInfo*> occupied_elements;
  };

  // Stuct holds cost info to connect two map elements and corresponding
  // association type.
  struct ConnectionCostInfo {
    double connection_cost = std::numeric_limits<double>::infinity();
    AssociationType::Enum association_type = AssociationType::kUnknown;
  };

  // Struct holds a single step result in dynamic programming by appending a
  // target map element to candidate sequences of association.
  struct TargetElementAssociationResult {
    // Minimum cumulative association cost along all candidate sequences after
    // appending the target map element.
    double min_association_cost_so_far;
    // Info of the target map element.
    const MapElementAndPoseInfo* target_element_info_ptr = nullptr;
    // Single step cost infos, each corresponding to a connection between the
    // target element and the last element (i.e., source element) in respective
    // candidate sequence.
    std::vector<ConnectionCostInfo> connection_cost_infos;
    // A list of optimal association results of which source elements yielding
    // the minimum cumulative association cost for the target element.
    std::vector<const TargetElementAssociationResult*>
        optimal_source_element_results;
  };

  struct PncLaneAndAssociatedHeadingDiff {
    const pnc_map::Lane* lane_ptr = nullptr;
    double heading_diff = 0.0;
  };

  using RouteInfo = std::list<const MapElementAndPoseInfo*>;

  // Preprocessing elements queried from map, e.g., selecting elements of
  // interest, discarding duplicated elements in sequence, computing pose
  // related auxiliary info of map element (heading diff, etc.) .
  std::vector<SampledPoseInfo> PreProcessingMapQueriedInfos(
      const PredictedTrajectoryWrapper& predicted_trajectory,
      const pnc_map::PncMapService* pnc_map_service,
      const std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>&
          idx_unexpanded_dist_lane_pairs_map,
      const std::unordered_map<int, std::vector<QueriedLaneAndApproxDistance>>&
          idx_expanded_dist_lane_pairs_map,
      const std::unordered_map<int, std::vector<const hdmap::Junction*>>&
          idx_junctions_map,
      const std::unordered_map<int, std::vector<const hdmap::Zone*>>&
          idx_zones_map);

  // A member function to populate what map elements of interest each sampled
  // pose of the trajectory occupies.
  std::vector<SampledPoseInfo> PopulateSampledPoseInfos(
      const PredictedTrajectoryWrapper& predicted_trajectory,
      const pnc_map::JointPncMapService& joint_pnc_map_service);

  // Compute single step association results for associating map elements
  // occupied by the first sampled pose, populate corresponding infos in raw
  // lookup table for dynamic programming.
  std::vector<TargetElementAssociationResult> ComputeFirstPoseSingleStepResults(
      const SampledPoseInfo& first_pose_info,
      const pnc_map::PncMapService* pnc_map_service,
      const pb::AgentMapElementOccupancySeeds&
          agent_map_element_occupancy_seeds);

  // Compute single step association results for associating map elements
  // occupied by the sampled pose other than the first one, populate
  // corresponding infos in raw lookup table for dynamic programming.
  std::vector<TargetElementAssociationResult>
  ComputeNonFirstPoseSingleStepResults(
      const SampledPoseInfo& pose_info,
      const std::vector<TargetElementAssociationResult>&
          last_pose_single_step_results);

  // Run dynamic programming algorithm and populate raw look-up table.
  std::unordered_map<size_t, std::vector<TargetElementAssociationResult>>
  PopulateRawAssociationLookupTable(
      const pnc_map::PncMapService* pnc_map_service,
      const pb::AgentMapElementOccupancySeeds&
          agent_map_element_occupancy_seeds);

  // Recursively backward search all routes with equally so-far minimum
  // cumulative association costs given any single intermediate result.
  void SearchOptimalRoutesGivenAssociationResult(
      const TargetElementAssociationResult* target_result_ptr,
      std::vector<RouteInfo>& optimal_routes_collection,
      RouteInfo& optimal_route_info_in_backtracking);

  // Recursively backward search all equally optimal routes (i.e., routes with
  // minimum cumulative association cost) given a raw look-up table.
  std::vector<RouteInfo> GetRawEquallyOptimalRoutes();

  // Search missed route between two disjoint elements (excluding source and
  // target) which are contented (i.e., being of recursive predecessor and
  // successor relationship at lane level) via recursively finding predecessors.
  // NOTE: This function will only return one feasible route with the least
  // number of element transitions given early return logic being implemented,
  // which holds true for most cases.
  // TODO(minhanli): Add logic to handle multiple feasible routes when
  // motivating cases arise.
  RouteInfo SearchRouteBetweenContentedElements(
      const MapElementAndPoseInfo& source_element,
      const MapElementAndPoseInfo& target_element);

  // Search missed route between two disjoint elements (excluding source and
  // target) which are neighborhood (i.e., being under the same section) via
  // iterating all lanes in a section. If source is the same as the target,
  // return empty |RouteInfo|.
  RouteInfo SearchRouteBetweenNeighboringElements(
      const MapElementAndPoseInfo& source_element,
      const MapElementAndPoseInfo& target_element);

  // Search missed route between two disjoint elements (excluding source and
  // target) which are super-neighborhood (i.e., being of recursive predecessor
  // and successor relationship at section level but not lane level) via (1)
  // finding crossing point between sections; (2) inferring exiting and entering
  // lanes between contended sections; (3) finding route between source and
  // exiting lanes as well as that between entering and target lanes, which are
  // of neighborhood relationship.
  RouteInfo SearchRouteBetweenSuperNeighboringElements(
      const MapElementAndPoseInfo& source_element,
      const MapElementAndPoseInfo& target_element,
      const PredictedTrajectoryWrapper& predicted_trajectory);

  // Find missed route between two disjoint elements.
  RouteInfo FindMissedRouteBetweenDisjointElements(
      const MapElementAndPoseInfo& source_element,
      const MapElementAndPoseInfo& target_element,
      const AssociationType::Enum association_type,
      const PredictedTrajectoryWrapper& predicted_trajectory);

  // Fill spatial gaps in sequence for each raw route caused by down-sampling.
  void ConnectingDisjointElementsInRoute(
      const PredictedTrajectoryWrapper& predicted_trajectory,
      RouteInfo& route_info);

  // Get the most likely route based off of raw equally optimal routes obtained
  // from naive dynamic programming, which is selected by some post processing
  // steps, such as tie-braking, gap filling, and duplicate removal.
  void GenerateMostLikelyRoute(
      const PredictedTrajectoryWrapper& predicted_trajectory);

  // Get a partial route consisting of only one map element, which is inferred
  // solely by the first pose.
  void GeneratePartialRouteInferredByTheFirstPose();

  pb::PredictedTrajectoryRouteAssociationDebug ToProto() const;

  static std::vector<RouteInfo> RemoveDuplicateElementsAlongRawRoutes(
      const std::vector<RouteInfo>& route_infos);

  static RouteInfo RemoveDuplicateElementsAlongRawRoute(
      const RouteInfo& route_info);

  // Tie-braking strategy for multiple tied optimal route found by naive dynamic
  // programming.
  static size_t SelectMostLikelyRouteIndex(
      const std::vector<RouteInfo>& equally_optimal_routes_with_duplicate);

  // Return the first lane in junction or region with conflicting lanes along
  // with associated heading diff, if any, in a given route.
  static PncLaneAndAssociatedHeadingDiff
  FindFirstLanePtrInConflictingRegionAlongRoute(const RouteInfo& route_info);

  // Return topological relationship between two map elements defined in
  // |pb::AgentPredictedTrajectoryRouteAssociationType|.
  static AssociationType::Enum GetAssociationTypeBetweenTwoElements(
      const MapElementAndPoseInfo& source_element_info,
      const MapElementAndPoseInfo& target_element_info);

  // Return single step cost of connecting two map elements in a sequence based
  // off of their topological relationship.
  static ConnectionCostInfo ComputeConnectionCostBetweenTwoElements(
      const MapElementAndPoseInfo& source_element_info,
      const MapElementAndPoseInfo& target_element_info);

  const int64_t object_id_;
  const int64_t trajectory_id_;
  // The latest isolated lane (e.g., regular lane, bus lane, etc.) retrieved
  // from |pb::AgentMapElementOccupancySeeds|, the agent ever occupied or
  // occupies. If the agent newly appears in tracking or we do not observe it is
  // in an isolated lane so far, it is |std::nullopt|.
  std::optional<MapElementAndPoseInfo> latest_isolated_lane_info_in_seed_opt_ =
      std::nullopt;
  // The latest exit zone the agent ever occupied or occupies obtained from
  // |pb::AgentMapElementOccupancySeeds|. If the agent newly appears in tracking
  // or we do not observe it is or was in an exit zone, it is |std::nullopt|.
  std::optional<MapElementAndPoseInfo> latest_exit_zone_info_in_seed_opt_ =
      std::nullopt;
  // An ordered list of indices of which the pose is being sampled for map
  // element query.
  std::vector<int> sampled_pose_indices_;
  // An ordered list of indices of which the pose is being sampled for map
  // element query but being skipped for route search i.e., dynamic programming.
  std::vector<int> sampled_pose_indices_being_skipped_;
  // A list of map element infos being queried or inferred.
  std::vector<MapElementAndPoseInfo> cached_map_element_infos_;
  // A list of sampled pose infos.
  const std::vector<SampledPoseInfo> sampled_pose_infos_;
  // A map between sampled pose index and corresponding occupied map elements'
  // association results.
  std::unordered_map<size_t, std::vector<TargetElementAssociationResult>>
      raw_association_cost_lookup_table_;
  // A list of route info whose raw cumulative association cost are equally
  // minimum, whose infos are used for post-processing.
  std::vector<RouteInfo> raw_equally_optimal_routes_;
  // A list collecting the raw equally optimal first map elements. Collected
  // from raw_equally_optimal_routes_.
  std::vector<MapElementAndPoseInfo> raw_equally_optimal_first_map_elements_;
  // A list of map elements representing the most likely route sequence after
  // removing duplicates.
  std::vector<MapElementAndPoseInfo> most_likely_route_;
  // A raw list of map elements representing the most likely route
  // sequence with duplicates. This is used for getting some auxillary info
  // regarding the raw route, e.g., pose index ranges for each map element along
  // the route, which are lost after duplicate removal.
  std::vector<MapElementAndPoseInfo> most_likely_route_with_duplicate_;
  // A partial route consisting of only one map element, which is inferred
  // solely by the first pose. This field is populated only when users specify
  // |sampled_pose_indices| with {0} in ctor for non-stationary agents.
  std::optional<MapElementAndPoseInfo>
      partial_route_inferred_by_the_first_pose_opt_ = std::nullopt;
};

}  // namespace route_association
}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_PREDICTED_TRAJECTORY_WRAPPER_UTIL_PREDICTED_TRAJECTORY_ROUTE_ASSOCIATION_H_
