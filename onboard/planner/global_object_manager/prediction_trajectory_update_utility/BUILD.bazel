load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "prediction_trajectory_update_utility",
    srcs = [
        "prediction_trajectory_update_utility.cpp",
    ],
    hdrs = [
        "prediction_trajectory_update_utility.h",
    ],
    include_prefix = "planner/global_object_manager/prediction_trajectory_update_utility",
    deps = [
        "//onboard/planner:constants",
        "//onboard/planner/global_object_manager:common_types",
        "//onboard/planner/utility/object_id",
        "//onboard/planner/utility/seed:planning_seed",
        "//onboard/prediction:utility",
        "//protobuf_cpp:protos_cpp",
    ],
)

voy_cc_test(
    name = "prediction_trajectory_update_utility_test",
    srcs = [
        "test/prediction_trajectory_update_utility_test.cpp",
    ],
    deps = [
        ":prediction_trajectory_update_utility",
        "//onboard/planner/global_object_manager/objects_evaluation_utility",
        "//onboard/planner/global_object_manager/test:test_utility",
        "@voy-sdk//:gtest",
    ],
)
