#include "routing/utility/rt_event_util.h"

#include "routing_protos/planning_route_state.pb.h"
#include "routing_protos/route_status.pb.h"
#include "routing_protos/state/state_manager.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace routing {
namespace utility {
void AddRtEventForAstarHasEndLessLoopWhenConstructPath(
    int64_t backtrack_edge_nums, int64_t cost_map_size) {
  const std::string rt_event_info = absl::StrFormat(
      "Find a endless while loop when constructing path. Backtrack edge "
      "number %d is greater than cost map size %d.",
      backtrack_edge_nums, cost_map_size);
  rt_event::PostRtEvent<
      rt_event::routing::GlobalHasEndLessWhileLoopWhenConstructPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarFindALoopWhenConstructPath(int64_t node_id) {
  const std::string rt_event_info = absl::StrFormat(
      "Find a loop in astar search result, loop node id is %d.", node_id);
  rt_event::PostRtEvent<rt_event::routing::GlobalFindALoopInAstarSearchResult>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarFindNullPtrWhenConstructPath() {
  const std::string rt_event_info = "Find a nullptr when constructing path.";
  rt_event::PostRtEvent<
      rt_event::routing::GlobalAstarFindNullPtrWhenConstructPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarFindNullPtrWhenExecuteSearch() {
  const std::string rt_event_info = "Find a nullptr when executing search.";
  rt_event::PostRtEvent<
      rt_event::routing::GlobalAstarFindNullPtrWhenExecuteSearch>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarHasEmptyQueueInWarmStart(
    int64_t node_ids_reset_to_open_status_size) {
  const std::string rt_event_info = absl::StrFormat(
      "Astar has empty open queue in warm start! The num of node ids "
      "reset to open status is %d.",
      node_ids_reset_to_open_status_size);
  rt_event::PostRtEvent<
      rt_event::routing::GlobalAstarHasEmptyOpenQueueInWarmStart>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForGlobalAstarHasNegativeCost(int64_t from_id, int64_t to_id,
                                             double cost,
                                             const std::string& edge_type) {
  const std::string rt_event_info = absl::StrFormat(
      "Detect negative cost edge in gloabl route, from %d to %d, cost %f, "
      "edge_type %s.",
      from_id, to_id, cost, edge_type);
  rt_event::PostRtEvent<rt_event::routing::GlobalHasNegativeCostInAstarSearch>(
      rt_event_info);
}

void AddRtEventForRoutingResearchAfterFailedWarmStartSearch(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search in cold start succesfully after last routing failed in "
        "warm start.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalAstarResearchSucceedAfterFailedWarmStart>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after last routing failed in "
                  "warm start.";
  }
}

void AddRtEventForRoutingResearchWithLoopInResult(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search in cold start successfully after last routing failed "
        "for finding a loop in astar result.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalAstarResearchSucceedAfterLoopInAstarResult>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after last routing failed for "
                  "finding a loop in astar result.";
  }
}

void AddRtEventForRoutingResearchWithNullPtrInAstarSearch(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search in cold start successfully after last routing failed "
        "for finding a nullptr in astar search.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalAstarResearchSucceedForNullPtrInAstarSearch>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after last routing failed for "
                  "finding a nullptr in astar search.";
  }
}

void AddRtEventForRoutingResearchWithNullPtrInBackTrackPath(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search in cold start successfully after last routing failed "
        "for finding a nullptr in backtrack path.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalAstarResearchSucceedForNullPtrInBackTrackPath>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after last routing failed for "
                  "finding a nullptr in backtrack path.";
  }
}

void AddRtEventForRoutingResearchWithIllegalBackTrackPath(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search in cold start successfully after last routing failed "
        "for illegal backtrack path.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalAstarResearchSucceedAfterIllegalBackTrackPath>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after last routing failed for "
                  "illegal backtrack path.";
  }
}

void AddRtEventForRoutingResearchWithUnknownReasonFailure(bool found_path) {
  if (found_path) {
    const std::string rt_event_info =
        "Astar search again successfully after unknown reason failure.";
    rt_event::PostRtEvent<
        rt_event::routing::GlobalReSearchSucceedAfterUnknownReasonFailure>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  } else {
    LOG(ERROR) << "Astar search again failed after unknown reason failure.";
  }
}

void AddRtEventForRecommendReplanHasZeroConnectableSourceRoads(
    const std::set<int64_t>& source_road_ids,
    const std::set<int64_t>& target_road_ids) {
  if (source_road_ids.empty() || target_road_ids.empty()) {
    LOG(ERROR) << "source or target road ids are empty!";
    return;
  }
  std::string source_road_str = "";
  for (const int64_t soure_road_id : source_road_ids) {
    source_road_str += std::to_string(soure_road_id) + ",";
  }
  std::string target_road_str = "";
  for (const int64_t target_road_id : target_road_ids) {
    target_road_str += std::to_string(target_road_id) + ",";
  }
  const std::string rt_event_info =
      "Recommend replan has zero connectable source roads, source roads "
      "include: " +
      source_road_str + " target roads include: " + target_road_str;
  rt_event::PostRtEvent<
      rt_event::routing::GlobalRecommendReplanHasNoConnectableSourceRoads>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarHasValidRecommendReplan() {
  const std::string rt_event_info = "Astar has valid recommend replan!";
  rt_event::PostRtEvent<rt_event::routing::GlobalAstarHasValidRecommendReplan>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForRecommendReplanHasZeroTargetRoads() {
  const std::string rt_event_info = "The number of target roads is zero.";
  rt_event::PostRtEvent<
      rt_event::routing::GlobalRecommendReplanHasZeroTargetRoads>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForAstarExceedsTimeThreshold(
    double warm_astar_max_run_time_ms, double cold_astar_max_run_time_ms,
    double elapsed_time, bool is_warmstart, bool found_path,
    bool has_searched_all_recommend_lanes) {
  const double timeout_threshold =
      is_warmstart ? warm_astar_max_run_time_ms : cold_astar_max_run_time_ms;
  if (elapsed_time > timeout_threshold) {
    const std::string rt_event_info = absl::StrFormat(
        "AStar timed out with %s, search time: %0.2f vs threshold: %0.2f (ms), "
        "found path: %s, has_searched_all_recommend_lanes: %s.",
        (is_warmstart ? "warm start" : "cold start"), elapsed_time,
        timeout_threshold, (found_path ? "true" : "false"),
        (has_searched_all_recommend_lanes ? "true" : "false"));
    rt_event::PostRtEvent<rt_event::routing::GlobalAstarExceedsTimeThreshold>(
        rt_event_info);
    LOG(ERROR) << rt_event_info;
  }
}

void AddRtEventForFailedToReadConstraintFromCloudForRouting() {
  const std::string rt_event_info =
      "Failed to read constraint from cloud for routing.";
  rt_event::PostRtEvent<
      rt_event::routing::ConstraintFailedToReadFromCloudForRouting>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForGlobalPathDivergeWithRegionalPath() {
  const std::string rt_event_info =
      "Global path diverge with regional path after replan.";
  rt_event::PostRtEvent<rt_event::routing::GlobalPathDivergeWithRegionalPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForGlobalPathConvergeWithRegionalPath() {
  const std::string rt_event_info =
      "Global path converge with regional path after replan.";
  rt_event::PostRtEvent<rt_event::routing::GlobalPathConvergeWithRegionalPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForRouteStatusManagerHasIllegalRejectingPath() {
  const std::string rt_event_info =
      "Routing RouteStatusManager has illegal rejection of route lanes.";
  rt_event::PostRtEvent<
      rt_event::routing::RouteStatusManagerHasIllegalRejectingPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForQueryManagerHasNotPreparedForReplan() {
  const std::string rt_event_info =
      "Routing QueryManager has not prepared for replanning route query.";
  rt_event::PostRtEvent<rt_event::routing::QueryManagerHasNotPreparedForReplan>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForRouteSolutionHasNoDestinationLaneCandidateInPath() {
  const std::string rt_event_info =
      "Routing RouteSolution has no destination lane candidate in optimal "
      "path.";
  rt_event::PostRtEvent<
      rt_event::routing::RouteSolutionHasNoDestinationLaneCandidateInPath>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForGlobalRouteHasNoLaneCandidatesForOrigin(
    const voy::Point2d& point) {
  const std::string rt_event_info =
      absl::StrFormat("Find no valid lane candidates for origin (%f, %f).",
                      point.x(), point.y());
  rt_event::PostRtEvent<
      rt_event::routing::GlobalRouteHasNoLaneCandidatesForOrigin>(
      rt_event_info);
  LOG(ERROR) << rt_event_info;
}

void AddRtEventForOriginLaneInconsistentWithPose(int64_t road_id) {
  const std::string& rt_event_str = absl::StrFormat(
      "Global route origin lane candidate in road %d is inconsistent with "
      "ego pose!.",
      road_id);
  rt_event::PostRtEvent<rt_event::planner::OriginLaneInconsistentWithPose>(
      rt_event_str);
  LOG(ERROR) << rt_event_str;
}

void AddRtEventForRecommendReplan(bool success) {
  if (success) {
    rt_event::PostRtEvent<rt_event::routing::GlobalRecommendReplanSucceed>(
        "Recommend replan succeed!");
  } else {
    rt_event::PostRtEvent<rt_event::routing::GlobalRecommendReplanFailed>(
        "Recommend replan Failed!");
  }
}

void AddRtEventForRoutingSearch(bool success, bool is_replan,
                                bool is_unknown_failure) {
  if (success) {
    if (is_replan) {
      rt_event::PostRtEvent<
          rt_event::routing::GlobalRoutingSucceedForReplanQuery>(
          "Replan query succeed!");
    } else {
      rt_event::PostRtEvent<
          rt_event::routing::GlobalRoutingSucceedForRideQuery>(
          "Ride query succeed!");
    }
    return;
  }

  if (is_unknown_failure) {
    rt_event::PostRtEvent<
        rt_event::routing::GlobalRoutingFailedWithUnknownReason>(
        "Routing failed with unknown reason!");
  }
}

void AddRtEventForPlanningRejectNewRoute(
    const pb::RouteSolution& route_solution, int64_t timestamp,
    bool is_in_junction) {
  AddRtEventAsJson<rt_event::planner::PlanningRejectDangerousNewRoute>(
      "order_id", route_solution.order_info().order_id(), "match_type",
      order::pb::MatchType_Name(route_solution.order_info().match_type()),
      "mission_id", route_solution.mission_id(), "route_query_timestamp",
      route_solution.query_timestamp(), "timestamp", timestamp,
      "is_in_junction", is_in_junction);
}

void AddRtEventForRouteCommand(const std::optional<pb::RouteQuery>& route_query,
                               pb::CommandType command_type,
                               pb::CommandPurpose command_purpose) {
  if (!route_query) {
    return;
  }

  AddRtEventAsJson<rt_event::routing::OrderRoutingStateTrack>(
      "type", "Routing_state_received_command", "command_type",
      pb::CommandType_Name(command_type), "command_purpose",
      pb::CommandPurpose_Name(command_purpose), "order_id",
      route_query->order_id(), "match_type",
      order::pb::MatchType_Name(route_query->match_type()), "route_operation",
      pb::RouteOperationInfo::OperationType_Name(
          route_query->route_operation_info().operation_type()),
      "mission_id", route_query->mission_id(), "timestamp",
      route_query->query_timestamp());
}

void AddRtEventForRouteConfirmation(const pb::RouteSolution& route_solution,
                                    pb::RouteState route_state) {
  AddRtEventAsJson<rt_event::routing::OrderRoutingStateTrack>(
      "type", "Routing_state_published_confirmation", "route_state",
      pb::RouteState_Name(route_state), "order_id",
      route_solution.order_info().order_id(), "match_type",
      order::pb::MatchType_Name(route_solution.order_info().match_type()),
      "route_operation",
      pb::RouteOperationInfo::OperationType_Name(
          route_solution.order_info().route_operation_info().operation_type()),
      "mission_id", route_solution.mission_id(), "timestamp",
      route_solution.query_timestamp());
}

void AddRtEventForEDAAndETA(const routing::pb::RouteStatus& route_status) {
  const routing::pb::RouteSolution& proposed_route_solution =
      route_status.proposed_route_solution();
  const routing::pb::RouteQuery& ride_route_query =
      route_status.ride_route_query();
  AddRtEventAsJson<rt_event::routing::GlobalETAAndEDA>(
      "route_state", pb::RouteState_Name(route_status.route_state()),
      "order_id", ride_route_query.order_id(), "query_timestamp",
      ride_route_query.query_timestamp(), "mission_id",
      ride_route_query.mission_id(), "EDA", route_status.dist_to_dest_m(),
      "ETA", route_status.time_to_dest_s(), "origin_x",
      proposed_route_solution.origin().position().x(), "origin_y",
      proposed_route_solution.origin().position().y(), "dest_x",
      proposed_route_solution.destination().position().x(), "dest_y",
      proposed_route_solution.destination().position().y());
}

void AddRtEventForPDZGenerationFailure(const pb::RouteSolution& route_solution,
                                       const ::pb::OddInfo::OddType& odd_type,
                                       const std::string& failure_reason) {
  AddRtEventAsJson<rt_event::planner::FailToGeneratePDZ>(
      "order_id", route_solution.order_info().order_id(), "match_type",
      order::pb::MatchType_Name(route_solution.order_info().match_type()),
      "timestamp", route_solution.query_timestamp(), "odd",
      ::pb::OddInfo::OddType_Name(odd_type), "failure_reason", failure_reason);
}

void AddRtEventForSameOriginAndDest(const pb::RouteQuery& route_query,
                                    const voy::Point2d& origin_pose,
                                    const voy::Point2d& destination_pose) {
  AddRtEventAsJson<rt_event::routing::SameOriginAndDestImmediatePullover>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp(),
      "origin_x", origin_pose.x(), "origin_y", origin_pose.y(), "dest_x",
      destination_pose.x(), "dest_y", destination_pose.y());
}

void AddRtEventForStaticSameOriginAndDest(const pb::RouteQuery& route_query) {
  AddRtEventAsJson<
      rt_event::routing::CompleteRouteForStaticSameOriginAndDestRoute>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp());
}

void AddRtEventForSameOriginAndDestImmediatePullover(
    const pb::RouteQuery& route_query) {
  AddRtEventAsJson<
      rt_event::routing::CompleteRouteForSameOriginAndDestImmediatePullover>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp());
}

void AddRtEventForRoutingFailedForFirstRideQuery(
    const pb::RouteQuery& route_query, const std::string& error_msg,
    const std::string& failed_reason) {
  AddRtEventAsJson<rt_event::routing::GlobalRoutingFailedForFirstRideQuery>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp(),
      "error_msg", error_msg, "failed_reason", failed_reason);
}

void AddRtEventForRoutingFailedForReplanQuery(
    const pb::RouteQuery& route_query, const std::string& error_msg,
    const std::string& failed_reason) {
  AddRtEventAsJson<rt_event::routing::GlobalRoutingFailedForReplanQuery>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp(),
      "error_msg", error_msg, "failed_reason", failed_reason);
}

void AddRtEventForRoutingFailedForChangingMissionRideQuery(
    const pb::RouteQuery& route_query, const std::string& error_msg,
    const std::string& failed_reason) {
  AddRtEventAsJson<
      rt_event::routing::GlobalRoutingFailedForChangingMissionRideQuery>(
      "order_id", route_query.order_id(), "match_type",
      order::pb::MatchType_Name(route_query.match_type()), "mission_id",
      route_query.mission_id(), "timestamp", route_query.query_timestamp(),
      "error_msg", error_msg, "failed_reason", failed_reason);
}

void AddRtEventForRoutingSuccessForNewRoute(
    const pb::RouteQuery& route_query,
    const pb::CommandPurpose& command_purpose) {
  AddRtEventAsJson<rt_event::routing::RoutingSuccessForNewRoute>(
      "order_id", route_query.order_id(), "command_purpose", command_purpose,
      "match_type", order::pb::MatchType_Name(route_query.match_type()),
      "mission_id", route_query.mission_id(), "timestamp",
      route_query.query_timestamp());
}

void AddRtEventForPulloutIsConfirmed(const pb::RouteSolution& route_solution) {
  AddRtEventAsJson<rt_event::planner::PulloutIsConfirmed>(
      "order_id", route_solution.order_info().order_id(), "match_type",
      order::pb::MatchType_Name(route_solution.order_info().match_type()));
}

void AddRtEventForPulloverIsTriggered(const pb::RouteSolution& route_solution) {
  AddRtEventAsJson<rt_event::planner::PulloverIsTriggered>(
      "order_id", route_solution.order_info().order_id(), "match_type",
      order::pb::MatchType_Name(route_solution.order_info().match_type()));
}

std::string OrderInfo::GetOrderString() const {
  if (!IsValid()) {
    return "unavailable_order_information";
  }
  Json json;
  if (command_type.has_value()) {
    AddKeyValuePairsToJson(json, "command_type",
                           pb::CommandType_Name(command_type.value()));
  }
  if (command_purpose.has_value()) {
    AddKeyValuePairsToJson(json, "command_purpose",
                           pb::CommandPurpose_Name(command_purpose.value()));
  }
  AddKeyValuePairsToJson(
      json, "order_id", order_id, "match_type",
      order::pb::MatchType_Name(match_type), "route_operation",
      pb::RouteOperationInfo::OperationType_Name(operation_type), "mission_id",
      mission_id, "query_timestamp", query_timestamp);
  return json.dump();
}

bool OrderInfo::IsValid() const {
  return !order_id.empty() && mission_id >= 0 && query_timestamp >= 0;
}

void OrderInfo::Clear() {
  order_id.clear();
  command_type = std::nullopt;
  command_purpose = std::nullopt;
  match_type = order::pb::MatchType();
  operation_type = pb::RouteOperationInfo::OperationType();
  mission_id = -1;
  query_timestamp = -1;
}

}  // namespace utility
}  // namespace routing
