/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_LatCtrlFct.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <LatCtrlFct>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_LATCTRLFCT_H
# define RTE_LATCTRLFCT_H

//# ifndef RTE_CORE
//#  ifdef RTE_APPLICATION_HEADER_FILE
//#   error Multiple application header files included.
//#  endif
//#  define RTE_APPLICATION_HEADER_FILE
//#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
//#   define RTE_PTR2ARRAYBASETYPE_PASSING
//#  endif
//# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_LatCtrlFct_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Buffers for inter-runnable variables
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h"

extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_10ms;
extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms;
extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_OsTask_Init_Core0_APP;

extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_10ms;
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_60ms;
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_OsTask_Init_Core0_APP;

extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_10ms;
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_60ms;
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_OsTask_Init_Core0_APP;

extern VAR(VDP_VehicleState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_VDP_VehicleState_MainTask_Core0_60ms;
extern VAR(VDP_VehicleState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_VDP_VehicleState_OsTask_Init_Core0_APP;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
# endif /* !defined(RTE_CORE) */


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_60ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_60ms; /* PRQA S 0759 */ /* MD_MSR_Union */
#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IRead_BDP_60ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_60ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)


#  define Rte_IRead_FSA_60ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_60ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)


#  define Rte_IRead_FSA_60ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_60ms.Rte_RB.Rte_LatCtrlFct_FSA_60ms_Runnable.Rte_VSI_VehicleInfo_VSI_VehicleInfo.value)


#  define Rte_IWrite_LCT_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LatCtrlFct_LAT_CtrlCmd_LAT_CtrlCmd.value = *(data) \
  )


#  define Rte_IWriteRef_LCT_10ms_Runnable_LAT_CtrlCmd_LAT_CtrlCmd() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_LatCtrlFct_LAT_CtrlCmd_LAT_CtrlCmd.value \
  )


#  define Rte_IRead_LSI_10ms_Runnable_CSI_LaneInfo_CSI_LaneInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo.value)


#  define Rte_IRead_LSI_10ms_Runnable_VSI_VehicleInfo_VSI_VehicleInfo() \
  (&Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo.value)


#  define Rte_IWrite_LatCtrlFct_Init_LAT_CtrlCmd_LAT_CtrlCmd(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_LatCtrlFct_LatCtrlFct_Init.Rte_LAT_CtrlCmd_LAT_CtrlCmd.value = *(data) \
  )


#  define Rte_IWriteRef_LatCtrlFct_Init_LAT_CtrlCmd_LAT_CtrlCmd() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_LatCtrlFct_LatCtrlFct_Init.Rte_LAT_CtrlCmd_LAT_CtrlCmd.value \
  )


/**********************************************************************************************************************
 * Inter-runnable variables
 *********************************************************************************************************************/

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_BDP_60ms_Runnable_LSI_LatSigInput() \
  (&Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_BDP_60ms_Runnable_BDP_FilteredLane(data) \
  (Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms = *(data))
#  define Rte_IrvIWriteRef_BDP_60ms_Runnable_BDP_FilteredLane() \
  (&Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_FSA_60ms_Runnable_BDP_FilteredLane() \
  (&Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_FSA_60ms_Runnable_LSI_LatSigInput() \
  (&Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_FSA_60ms_Runnable_VDP_VehicleState() \
  (&Rte_Irv_LatCtrlFct_VDP_VehicleState_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_FSA_60ms_Runnable_FSA_SystemState(data) \
  (Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_60ms = *(data))
#  define Rte_IrvIWriteRef_FSA_60ms_Runnable_FSA_SystemState() \
  (&Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_60ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_LCT_10ms_Runnable_BDP_FilteredLane() \
  (&Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_LCT_10ms_Runnable_FSA_SystemState() \
  (&Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_LCT_10ms_Runnable_LSI_LatSigInput() \
  (&Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LSI_10ms_Runnable_LSI_LatSigInput(data) \
  (Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_10ms = *(data))
#  define Rte_IrvIWriteRef_LSI_10ms_Runnable_LSI_LatSigInput() \
  (&Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_10ms)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LatCtrlFct_Init_BDP_FilteredLane(data) \
  (Rte_Irv_LatCtrlFct_BDP_FilteredLane_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LatCtrlFct_Init_BDP_FilteredLane() \
  (&Rte_Irv_LatCtrlFct_BDP_FilteredLane_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LatCtrlFct_Init_FSA_SystemState(data) \
  (Rte_Irv_LatCtrlFct_FSA_SystemState_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LatCtrlFct_Init_FSA_SystemState() \
  (&Rte_Irv_LatCtrlFct_FSA_SystemState_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LatCtrlFct_Init_LSI_LatSigInput(data) \
  (Rte_Irv_LatCtrlFct_LSI_LatSigInput_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LatCtrlFct_Init_LSI_LatSigInput() \
  (&Rte_Irv_LatCtrlFct_LSI_LatSigInput_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIWrite_LatCtrlFct_Init_VDP_VehicleState(data) \
  (Rte_Irv_LatCtrlFct_VDP_VehicleState_OsTask_Init_Core0_APP = *(data))
#  define Rte_IrvIWriteRef_LatCtrlFct_Init_VDP_VehicleState() \
  (&Rte_Irv_LatCtrlFct_VDP_VehicleState_OsTask_Init_Core0_APP)
/* PRQA L:L1 */

/* PRQA S 3453 L1 */ /* MD_MSR_FctLikeMacro */
#  define Rte_IrvIRead_VDP_60ms_Runnable_BDP_FilteredLane() \
  (&Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms)
#  define Rte_IrvIWrite_VDP_60ms_Runnable_VDP_VehicleState(data) \
  (Rte_Irv_LatCtrlFct_VDP_VehicleState_MainTask_Core0_60ms = *(data))
#  define Rte_IrvIWriteRef_VDP_60ms_Runnable_VDP_VehicleState() \
  (&Rte_Irv_LatCtrlFct_VDP_VehicleState_MainTask_Core0_60ms)
/* PRQA L:L1 */


# endif /* !defined(RTE_CORE) */


# define LatCtrlFct_START_SEC_CODE
// # include "LatCtrlFct_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_BDP_60ms_Runnable BDP_60ms_Runnable
#  define RTE_RUNNABLE_FSA_60ms_Runnable FSA_60ms_Runnable
#  define RTE_RUNNABLE_LCT_10ms_Runnable LCT_10ms_Runnable
#  define RTE_RUNNABLE_LSI_10ms_Runnable LSI_10ms_Runnable
#  define RTE_RUNNABLE_LatCtrlFct_Init LatCtrlFct_Init
#  define RTE_RUNNABLE_VDP_60ms_Runnable VDP_60ms_Runnable
# endif

FUNC(void, LatCtrlFct_CODE) BDP_60ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LatCtrlFct_CODE) FSA_60ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LatCtrlFct_CODE) LCT_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LatCtrlFct_CODE) LSI_10ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LatCtrlFct_CODE) LatCtrlFct_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, LatCtrlFct_CODE) VDP_60ms_Runnable(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define LatCtrlFct_STOP_SEC_CODE
// # include "LatCtrlFct_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_LATCTRLFCT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
