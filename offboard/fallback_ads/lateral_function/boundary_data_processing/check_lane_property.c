
#include "lateral_function/boundary_data_processing/check_lane_property.h"

#include <math.h>
#include <stdbool.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/common_method.h"
#include "common_math_library/debounce_method.h"
#include "lateral_function/lateral_function_parameter.h"

float CalculateLanePosition(float y0, float heading, float curvature,
                            float curvature_change, float x) {
  return y0 + tanf(heading) * x + 0.5F * curvature * x * x +
         1.0F / 6.0F * curvature_change * x * x * x;
}

float CalculateLaneHeading(float heading, float curvature,
                           float curvature_change, float x) {
  return tanf(heading) + curvature * x + 0.5F * curvature_change * x * x;
}

bool CheckLaneRadius(float left_lane_curvature, float right_lane_curvature) {
  static bool left_lane_radius_valid = false;
  static bool right_lane_radius_valid = false;

  CML_Hysteresis((CML_SafeDivision(1.0F, left_lane_curvature)),
                 GetLatMinLaneRadius(),
                 (GetLatMinLaneRadius() + GetLatMinLaneRadiusHys()),
                 &left_lane_radius_valid);

  CML_Hysteresis((CML_SafeDivision(1.0F, right_lane_curvature)),
                 GetLatMinLaneRadius(),
                 (GetLatMinLaneRadius() + GetLatMinLaneRadiusHys()),
                 &right_lane_radius_valid);

  return (left_lane_radius_valid && right_lane_radius_valid);
}

bool CheckLaneLength(float left_lane_length, float right_lane_length) {
  static bool left_lane_length_valid = false;
  static bool right_lane_length_valid = false;

  CML_Hysteresis(left_lane_length,
                 (GetLatMinLaneLength() - GetLatMinLaneLengthHys()),
                 GetLatMinLaneLength(), &left_lane_length_valid);

  CML_Hysteresis(right_lane_length,
                 (GetLatMinLaneLength() - GetLatMinLaneLengthHys()),
                 GetLatMinLaneLength(), &right_lane_length_valid);

  return (left_lane_length_valid && right_lane_length_valid);
}

bool CheckLaneWidth(float left_lane_y0, float left_lane_heading,
                    float right_lane_y0, float right_lane_heading) {
  static bool lane_width_valid = false;
  float lane_width = fabsf(left_lane_y0 * cosf(left_lane_heading) -
                           right_lane_y0 * cosf(right_lane_heading));
  CML_BilateralHysteresis(lane_width, GetLatMinLaneWidth(),
                          GetLatMaxLaneWidth(), GetLatLaneWidthHys(),
                          &lane_width_valid);
  return lane_width_valid;
}

bool CheckDistanceToLane(float left_lane_y0, float right_lane_y0) {
  static bool dist_to_left_lane_valid = false;
  static bool dist_to_right_lane_valid = false;

  CML_Hysteresis(fabsf(left_lane_y0), GetLatMinDistanceToLane(),
                 (GetLatMinDistanceToLane() + GetLatMinDistanceToLaneHys()),
                 &dist_to_left_lane_valid);

  CML_Hysteresis(fabsf(right_lane_y0), GetLatMinDistanceToLane(),
                 (GetLatMinDistanceToLane() + GetLatMinDistanceToLaneHys()),
                 &dist_to_right_lane_valid);

  return (dist_to_left_lane_valid && dist_to_right_lane_valid &&
          left_lane_y0 > 0.0F && right_lane_y0 < 0.0F);
}

bool CheckLaneInCaptureRange(float left_lane_y0, float left_lane_heading,
                             float right_lane_y0, float right_lane_heading) {
  float center_lane_y0 = 0.5F * (left_lane_y0 + right_lane_y0);
  float center_lane_heading = 0.5F * (left_lane_heading + right_lane_heading);

  // the vehicle is not moving away from the center of the lane
  bool is_away_from_center_lane =
      CML_Sign(center_lane_y0) != CML_Sign(center_lane_heading);

  // check lane heading angle for lane centering
  bool lane_heading_valid = fabsf(center_lane_heading) < GetLatMaxLaneHeading();

  return (is_away_from_center_lane && lane_heading_valid);
}

bool CheckLaneMerge(const CSI_LaneInfo_Struct *lane_info_ptr) {
  // calculate the width of the end position of the lane
  float min_lane_length =
      fminf(lane_info_ptr->CSI_PosXEndLf, lane_info_ptr->CSI_PosXEndRi);

  // calculate left lane position and heading at the end of the lane
  float left_lane_y0 = CalculateLanePosition(
      lane_info_ptr->CSI_PosY0Lf, lane_info_ptr->CSI_HeadingLf,
      lane_info_ptr->CSI_CurvatureLf, lane_info_ptr->CSI_CrvRateLf,
      min_lane_length);
  float left_lane_heading = CalculateLaneHeading(
      lane_info_ptr->CSI_HeadingLf, lane_info_ptr->CSI_CurvatureLf,
      lane_info_ptr->CSI_CrvRateLf, min_lane_length);

  // calculate right lane position and heading at the end of the lane
  float right_lane_y0 = CalculateLanePosition(
      lane_info_ptr->CSI_PosY0Ri, lane_info_ptr->CSI_HeadingRi,
      lane_info_ptr->CSI_CurvatureRi, lane_info_ptr->CSI_CrvRateRi,
      min_lane_length);
  float right_lane_heading = CalculateLaneHeading(
      lane_info_ptr->CSI_HeadingRi, lane_info_ptr->CSI_CurvatureRi,
      lane_info_ptr->CSI_CrvRateRi, min_lane_length);

  // check lane merge by lane width
  float lane_width = left_lane_y0 * cosf(left_lane_heading) -
                     right_lane_y0 * cosf(right_lane_heading);
  bool is_lane_merge = lane_width < GetLatLaneWidthForLaneMerge();

  return is_lane_merge;
}
