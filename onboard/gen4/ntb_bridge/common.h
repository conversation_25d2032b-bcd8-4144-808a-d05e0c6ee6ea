#ifndef ONBOARD_GEN4_NTB_BRIDGE_COMMON_H_
#define ONBOARD_GEN4_NTB_BRIDGE_COMMON_H_

#include <cstdint>
#include <string>
#include <unordered_map>
#include <utility>
#include <vector>

extern "C" {
#include "libntb/ntb_lib.h"
}

#include "av_comm/topics.h"

namespace ntb_bridge {
// Frame diagram:
// <-------------------kPacket------------------->
// +-------------+
// |   ntb_msg   |
// |     ...     |       <--------Payload-------->
// |             |       +---------------+-------+
// |  void* buf -------> | kNtbMsgHeader | kData |
// +-------------+       +---------------+-------+

struct Ntb<PERSON>eader {
  // The timestamp at which the sender starts sending data. (8 bytes)
  int64_t timestamp;
  // The physical timestamp of sensor data, such as image timestamp. (8 bytes)
  int64_t hw_timestamp;
  // To align the publish timestamps of YUV images and video frames, we
  // explicitly pass the publish timestamp of YUV images. (8 bytes)
  int64_t msg_publish_timestamp;
  // The size of transmitted data. (4 bytes)
  int32_t topic_id;
  uint32_t data_size;
  // Add topic sequence to identify if the messages belong to the same topic. (4
  // bytes)
  // int32_t topic_seq;
  // Add Frame sequence to identify the real sequence within the same topic. (4
  // bytes)
  // int32_t frame_seq;
  // int32_t reserved;
};

constexpr int kNtbMsgHeaderSize = sizeof(NtbMsgHeader);

struct TopicMappingMeta {
  int ntb_id;
  ntb_host_id src_host;
  ntb_host_id dst_host;
};

inline const std::vector<std::pair<std::string, TopicMappingMeta>>&
GetTopicMappings() {
  static const std::vector<std::pair<std::string, TopicMappingMeta>>
      topic_mappings = {
          // Orin1 Camera Raw
          {av_comm::topic::kCameraRaw102, {102, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraRaw103, {103, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraRaw104, {104, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraRaw105, {105, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraRaw106, {106, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraRaw107, {107, NTB_ORIN1, NTB_X86}},
          // Orin1 Camera Video
          {av_comm::topic::kCameraVideo102, {112, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraVideo103, {113, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraVideo104, {114, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraVideo105, {115, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraVideo106, {116, NTB_ORIN1, NTB_X86}},
          {av_comm::topic::kCameraVideo107, {117, NTB_ORIN1, NTB_X86}},
          // Orin2 Camera Raw
          {av_comm::topic::kCameraRaw101, {201, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawTrafficLight,
           {202, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawShort1, {203, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawShort2, {204, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawShort3, {205, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawShort4, {206, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4RawInfrared1, {207, NTB_ORIN2, NTB_X86}},
          // Orin2 Camera Video
          {av_comm::topic::kCameraVideo101, {211, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4VideoTrafficLight,
           {212, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4VideoShort1, {213, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4VideoShort2, {214, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4VideoShort3, {215, NTB_ORIN2, NTB_X86}},
          {av_comm::topic::kCameraGen4VideoShort4, {216, NTB_ORIN2, NTB_X86}},
      };
  return topic_mappings;
}

inline int TopicToNtbTopicID(const std::string& topic) {
  static const std::unordered_map<std::string, int> topic_name_to_ntb_topic_id =
      []() {
        const auto& mappings = GetTopicMappings();
        std::unordered_map<std::string, int> ret;
        for (const auto& [topic_name, topic_meta] : mappings) {
          ret[topic_name] = topic_meta.ntb_id;
        }
        return ret;
      }();

  return topic_name_to_ntb_topic_id.at(topic);
}

inline const std::string& NtbTopicIDToTopicName(const int topic_id) {
  static const std::unordered_map<int, std::string> ntb_topic_id_to_topic_name =
      []() {
        const auto& mappings = GetTopicMappings();
        std::unordered_map<int, std::string> reverse_map;
        for (const auto& [topic_name, topic_meta] : mappings) {
          reverse_map[topic_meta.ntb_id] = topic_name;
        }
        return reverse_map;
      }();

  return ntb_topic_id_to_topic_name.at(topic_id);
}

inline ntb_host_id TopicToDestHostID(const std::string& topic) {
  static const std::unordered_map<std::string, ntb_host_id>
      topic_name_to_dest_ntb_host_id = []() {
        const auto& mappings = GetTopicMappings();
        std::unordered_map<std::string, ntb_host_id> ret;
        for (const auto& [topic_name, topic_meta] : mappings) {
          ret[topic_name] = topic_meta.dst_host;
        }
        return ret;
      }();
  return topic_name_to_dest_ntb_host_id.at(topic);
}

}  // namespace ntb_bridge

#endif  // ONBOARD_GEN4_NTB_BRIDGE_COMMON_H_
