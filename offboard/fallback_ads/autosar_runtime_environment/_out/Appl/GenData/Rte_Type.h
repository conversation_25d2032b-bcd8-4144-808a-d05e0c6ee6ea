/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_Type.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Header file containing user defined AUTOSAR types and RTE structures
 *********************************************************************************************************************/

/* PRQA S 0777, 0779 EOF */ /* MD_MSR_Rule5.1, MD_MSR_Rule5.2 */

/* double include prevention */
#ifndef RTE_TYPE_H
# define RTE_TYPE_H

# include "_out/Appl/GenData/Rte.h"
# ifdef RTE_CORE
/**********************************************************************************************************************
 * Type definitions for mode management
 *********************************************************************************************************************/
typedef EventMaskType Rte_EventMaskType;
# endif /* defined(RTE_CORE) */

/* PRQA S 1039 EOF */ /* MD_Rte_1039 */

/**********************************************************************************************************************
 * Data type definitions
 *********************************************************************************************************************/

# define Rte_TypeDef_Float
typedef float32 Float;

# define Rte_TypeDef_UInt16
typedef uint16 UInt16;

# define Rte_TypeDef_UInt32
typedef uint32 UInt32;

# define Rte_TypeDef_AcuControlTimestamp
typedef uint8 AcuControlTimestamp[8];

# define Rte_TypeDef_ControlTimeStamp
typedef uint8 ControlTimeStamp[6];

# define Rte_TypeDef_DataArrayType_uint8_1
typedef uint8 DataArrayType_uint8_1[1];

# define Rte_TypeDef_DataArrayType_uint8_2
typedef uint8 DataArrayType_uint8_2[2];

# define Rte_TypeDef_Dcm_Data10ByteType
typedef uint8 Dcm_Data10ByteType[10];

# define Rte_TypeDef_Dcm_Data13ByteType
typedef uint8 Dcm_Data13ByteType[13];

# define Rte_TypeDef_Dcm_Data17ByteType
typedef uint8 Dcm_Data17ByteType[17];

# define Rte_TypeDef_Dcm_Data1ByteType
typedef uint8 Dcm_Data1ByteType[1];

# define Rte_TypeDef_Dcm_Data2ByteType
typedef uint8 Dcm_Data2ByteType[2];

# define Rte_TypeDef_Dcm_Data4ByteType
typedef uint8 Dcm_Data4ByteType[4];

# define Rte_TypeDef_Dcm_Data5ByteType
typedef uint8 Dcm_Data5ByteType[5];

# define Rte_TypeDef_Dem_MaxDataValueType
typedef uint8 Dem_MaxDataValueType[2];

# define Rte_TypeDef_GnssPoseTimeStamp
typedef uint8 GnssPoseTimeStamp[6];

# define Rte_TypeDef_PoseDebugTimeStamp
typedef uint8 PoseDebugTimeStamp[6];

# define Rte_TypeDef_PoseTimeStamp
typedef uint8 PoseTimeStamp[6];

# define Rte_TypeDef_TrajectoryInfoInitTimeStamp
typedef uint8 TrajectoryInfoInitTimeStamp[8];

# define Rte_TypeDef_BDP_FilteredLane_Struct
typedef struct
{
  Float BDP_PosY0Lf;
  Float BDP_HeadingAngleLf;
  Float BDP_CurvatureLf;
  Float BDP_CurvatureRateLf;
  Float BDP_ValidLengthLf;
  Float BDP_CurvatureRateCntr;
  Float BDP_CurvatureCntr;
  Float BDP_HeadingAngleCntr;
  Float BDP_PosY0Cntr;
  Float BDP_ValidLengthCntr;
  Float BDP_PosY0Ri;
  Float BDP_HeadingAngleRi;
  Float BDP_CurvatureRi;
  Float BDP_CurvatureRateRi;
  Float BDP_ValidLengthRi;
  uint8 VLP_LaneValidState;
  Float VLP_MeasureLaneWidth;
} BDP_FilteredLane_Struct;

# define Rte_TypeDef_CDS_CollisionDetection_Struct
typedef struct
{
  Float CDS_TimeToCollision;
  Float CDS_EgoStopTime;
  Float CDS_ObjectStopTime;
  boolean CDS_EgoStopEnable;
  boolean CDS_ObjectStopEnable;
  boolean CDS_ObjectPositionValid;
  boolean CDS_CollisionEnableByDy;
  float32 CDS_CollisionProbability;
  Float CDS_SafeDistance;
  Float CDS_LongNecAcc;
  Float CDS_TimeToReachStopPoint;
  uint8 CDS_LongNecAccState;
} CDS_CollisionDetection_Struct;

# define Rte_TypeDef_CSI_LaneInfo_Struct
typedef struct
{
  uint8 CSI_CalibrationStatus;
  uint8 CSI_LaneChangeStatus;
  Float CSI_LaneTimeStamp;
  uint8 CSI_MarkerTypeLf;
  uint8 CSI_LaneIDLf;
  uint8 CSI_MeasureTypeLf;
  Float CSI_ExistProbLf;
  Float CSI_PosY0Lf;
  Float CSI_HeadingLf;
  Float CSI_CurvatureLf;
  uint8 CSI_ColorTypeLf;
  Float CSI_CrvRateLf;
  Float CSI_MakerWidthLf;
  Float CSI_PosXStartLf;
  Float CSI_PosYStartStdLf;
  Float CSI_PosXEndLf;
  Float CSI_PosXEndStdLf;
  uint8 CSI_ParseConfLf;
  uint8 CSI_ObstacleFlgLf;
  uint8 CSI_PolyfitRmseLf;
  uint8 CSI_LaneIDRi;
  uint8 CSI_MeasureTypeRi;
  uint8 CSI_MarkerTypeRi;
  Float CSI_ExistProbRi;
  Float CSI_PosY0Ri;
  Float CSI_HeadingRi;
  Float CSI_CurvatureRi;
  uint8 CSI_ColorTypeRi;
  Float CSI_CrvRateRi;
  Float CSI_MakerWidthRi;
  Float CSI_PosXStartRi;
  Float CSI_PosYStartStdRi;
  Float CSI_PosXEndRi;
  Float CSI_PosXEndStdRi;
  uint8 CSI_ParseConfRi;
  uint8 CSI_PolyfitRmseRi;
  uint8 CSI_ObstacleFlgRi;
  uint8 CSI_LaneIDLfAdj;
  uint8 CSI_MeasureTypeLfAdj;
  uint8 CSI_MarkerTypeLfAdj;
  Float CSI_ExistProbLfAdj;
  Float CSI_PosY0LfAdj;
  Float CSI_HeadingLfAdj;
  Float CSI_CurvatureLfAdj;
  Float CSI_CrvRateLfAdj;
  uint8 CSI_ColorTypeLfAdj;
  Float CSI_MakerWidthLfAdj;
  Float CSI_PosXStartLfAdj;
  Float CSI_PosYStartStdLfAdj;
  Float CSI_PosXEndLfAdj;
  Float CSI_PosXEndStdLfAdj;
  uint8 CSI_ObstacleFlgLfAdj;
  uint8 CSI_PolyfitRmseLfAdj;
  uint8 CSI_ParseConfLfAdj;
  uint8 CSI_LaneIDRiAdj;
  uint8 CSI_MeasureTypeRiAdj;
  uint8 CSI_MarkerTypeRiAdj;
  Float CSI_ExistProbRiAdj;
  Float CSI_PosY0RiAdj;
  Float CSI_HeadingAngleRiAdj;
  Float CSI_CurvatureRiAdj;
  Float CSI_CrvRateRiAdj;
  uint8 CSI_ColorTypeRiAdj;
  Float CSI_MakerWidthRiAdj;
  Float CSI_PosXStartRiAdj;
  Float CSI_PosYStartStdRiAdj;
  Float CSI_PosXEndRiAdj;
  Float CSI_PosXEndStdRiAdj;
  uint8 CSI_ParseConfRiAdj;
  uint8 CSI_ObstacleFlgRiAdj;
  uint8 CSI_PolyfitRmseRiAdj;
  uint8 CSI_ParseConfLfRe;
  uint8 CSI_PolyfitRmseLfRe;
  uint8 CSI_ObstacleFlgLfRe;
  uint8 CSI_MeasureTypeLfRe;
  uint8 CSI_MarkerTypeLfRe;
  uint8 CSI_ExistProbLfRe;
  Float CSI_PosY0LfRe;
  Float CSI_HeadingLfRe;
  Float CSI_CurvatureLfRe;
  Float CSI_CrvRateLfRe;
  uint8 CSI_ColorTypeLfRe;
  Float CSI_MakerWidthLfRe;
  Float CSI_PosXStartLfRe;
  Float CSI_PosYStartStdLfRe;
  Float CSI_PosXEndLfRe;
  Float CSI_PosXEndStdLfRe;
  uint8 CSI_ParseConfRiRe;
  uint8 CSI_PolyfitRmseRiRe;
  uint8 CSI_ObstacleFlgRiRe;
  uint8 CSI_MarkerTypeRiRe;
  uint8 CSI_MeasureTypeRiRe;
  Float CSI_ExistProbRiRe;
  Float CSI_PosY0RiRe;
  Float CSI_HeadingAngleRiRe;
  Float CSI_CurvatureRiRe;
  Float CSI_CrvRateRiRe;
  uint8 CSI_ColorTypeRiRe;
  Float CSI_MakerWidthRiRe;
  Float CSI_PosXStartRiRe;
  Float CSI_PosYStartStdRiRe;
  Float CSI_PosXEndRiRe;
  Float CSI_PosXEndStdRiRe;
} CSI_LaneInfo_Struct;

# define Rte_TypeDef_CSI_ObjectInfo_Struct
typedef struct
{
  Float CSI_Ri_AccOBJ_Width;
  Float CSI_Ri_AccOBJ_Vy;
  Float CSI_Ri_AccOBJ_Vx;
  Float CSI_Ri_AccOBJ_ObstacleProb;
  Float CSI_Ri_AccOBJ_HeadingAngle;
  Float CSI_Ri_AccOBJ_ExistProb;
  Float CSI_Ri_AccOBJ_Dy;
  Float CSI_Ri_AccOBJ_Dx;
  uint8 CSI_Ri_AccOBJ_Class;
  uint8 CSI_Ri_AccOBJ_Brakelight_Info;
  Float CSI_Ri_AccOBJ_Ay;
  Float CSI_Ri_AccOBJ_Ax;
  Float CSI_RiFr_AccOBJ_Width;
  Float CSI_RiFr_AccOBJ_Vy;
  Float CSI_RiFr_AccOBJ_Vx;
  Float CSI_RiFr_AccOBJ_ObstacleProb;
  Float CSI_RiFr_AccOBJ_HeadingAngle;
  Float CSI_RiFr_AccOBJ_ExistProb;
  Float CSI_RiFr_AccOBJ_Dy;
  Float CSI_RiFr_AccOBJ_Dx;
  uint8 CSI_RiFr_AccOBJ_Class;
  uint8 CSI_RiFr_AccOBJ_Brakelight_Info;
  Float CSI_RiFr_AccOBJ_Ay;
  Float CSI_RiFr_AccOBJ_Ax;
  Float CSI_Le_AccOBJ_Width;
  Float CSI_Le_AccOBJ_Vy;
  Float CSI_Le_AccOBJ_Vx;
  Float CSI_Le_AccOBJ_ObstacleProb;
  Float CSI_Le_AccOBJ_HeadingAngle;
  Float CSI_Le_AccOBJ_ExistProb;
  Float CSI_Le_AccOBJ_Dy;
  Float CSI_Le_AccOBJ_Dx;
  uint8 CSI_Le_AccOBJ_Class;
  uint8 CSI_Le_AccOBJ_Brakelight_Info;
  Float CSI_Le_AccOBJ_Ay;
  Float CSI_Le_AccOBJ_Ax;
  Float CSI_LeFr_AccOBJ_Width;
  Float CSI_LeFr_AccOBJ_Vy;
  Float CSI_LeFr_AccOBJ_Vx;
  Float CSI_LeFr_AccOBJ_ObstacleProb;
  Float CSI_LeFr_AccOBJ_HeadingAngle;
  Float CSI_LeFr_AccOBJ_ExistProb;
  Float CSI_LeFr_AccOBJ_Dy;
  Float CSI_LeFr_AccOBJ_Dx;
  uint8 CSI_LeFr_AccOBJ_Class;
  uint8 CSI_LeFr_AccOBJ_Brakelight_Info;
  Float CSI_LeFr_AccOBJ_Ay;
  Float CSI_LeFr_AccOBJ_Ax;
  Float CSI_Fr_AccOBJ_Width;
  Float CSI_Fr_AccOBJ_Vy;
  Float CSI_Fr_AccOBJ_Vx;
  Float CSI_Fr_AccOBJ_ObstacleProb;
  Float CSI_Fr_AccOBJ_Length;
  Float CSI_Fr_AccOBJ_Height;
  Float CSI_Fr_AccOBJ_HeadingAngle;
  Float CSI_Fr_AccOBJ_ExistProb;
  Float CSI_Fr_AccOBJ_Dy;
  Float CSI_Fr_AccOBJ_Dx;
  uint8 CSI_Fr_AccOBJ_Class;
  uint8 CSI_Fr_AccOBJ_Brakelight_Info;
  Float CSI_Fr_AccOBJ_Ay;
  Float CSI_Fr_AccOBJ_Ax;
  Float CSI_FrFr_AccOBJ_Width;
  Float CSI_FrFr_AccOBJ_Vy;
  Float CSI_FrFr_AccOBJ_Vx;
  Float CSI_FrFr_AccOBJ_ObstacleProb;
  Float CSI_FrFr_AccOBJ_HeadingAngle;
  Float CSI_FrFr_AccOBJ_ExistProb;
  Float CSI_FrFr_AccOBJ_Dy;
  Float CSI_FrFr_AccOBJ_Dx;
  uint8 CSI_FrFr_AccOBJ_Class;
  uint8 CSI_FrFr_AccOBJ_Brakelight_Info;
  Float CSI_FrFr_AccOBJ_Ay;
  Float CSI_FrFr_AccOBJ_Ax;
} CSI_ObjectInfo_Struct;

# define Rte_TypeDef_CalibratedVehicleParams
typedef struct
{
  float32 wheel_angle_offset;
} CalibratedVehicleParams;

# define Rte_TypeDef_ControlOutput
typedef struct
{
  uint32 timestamp_ms;
  float32 wheel_angle;
  float32 acceleration;
  float32 jerk;
  float32 drive_torque;
  boolean is_horn_on;
  boolean is_hazard_light_on;
} ControlOutput;

# define Rte_TypeDef_EstimationStateDebug
typedef struct
{
  float32 x;
  float32 y;
  float32 yaw;
  float32 vel;
  float32 x_std;
  float32 y_std;
  float32 yaw_std;
  float32 vel_std;
} EstimationStateDebug;

# define Rte_TypeDef_FBS_DebugInfo_Struct
typedef struct
{
  uint8 EAD_McuVersion;
  uint8 EAD_SystemState;
  float32 EAD_AccRequest;
  float32 EAD_JerkRequest;
  float32 EAD_SteeringAngle;
  float32 EAD_LimitSteeringAngle;
  float32 EAD_MaxSteeringAngleRate;
} FBS_DebugInfo_Struct;

# define Rte_TypeDef_FSA_SystemState_Struct
typedef struct
{
  uint16 FSA_SystemStateOut;
} FSA_SystemState_Struct;

# define Rte_TypeDef_FSC_FreeStopControl_Struct
typedef struct
{
  Float FSC_FreeStopAccRequest;
  boolean FSC_LowSpeedCruiseEnable;
  Float FSC_CruiseAccRequest;
  float32 FSC_AccRequestByLaneLength;
} FSC_FreeStopControl_Struct;

# define Rte_TypeDef_LAT_CtrlCmd_Struct
typedef struct
{
  Float BDP_PosY0Lf;
  Float BDP_HeadingAngleLf;
  Float BDP_CurvatureLf;
  Float BDP_CurvatureRateLf;
  Float BDP_ValidLengthLf;
  Float BDP_PosY0Cntr;
  Float BDP_HeadingAngleCntr;
  Float BDP_CurvatureCntr;
  Float BDP_CurvatureRateCntr;
  Float BDP_ValidLengthCntr;
  Float BDP_PosY0Ri;
  Float BDP_HeadingAngleRi;
  Float BDP_CurvatureRi;
  Float BDP_CurvatureRateRi;
  Float BDP_ValidLengthRi;
  uint8 VLP_LaneValidState;
  Float VLP_MeasureLaneWidth;
  Float LAT_FrontWheelSteerAngle;
  uint16 LAT_SystemStateOut;
  Float LCT_ErrorDistY;
  Float LCT_ErrHeadingAngle;
  Float LCT_DistYErrorWeight;
  Float LCT_HeadingErrorWeight;
  uint16 LCT_IterationNum;
  Float LCT_IterationError;
  Float LCT_SteeringAngleByLQR;
  Float LCT_FeedforwardSteeringAngle;
  Float LCT_RawSteeringAngle;
  Float LCT_LimitSteeringAngle;
  Float LCT_MaxSteeringAngleRate;
  Float LCT_MaxSteeringAngle;
} LAT_CtrlCmd_Struct;

# define Rte_TypeDef_LSI_LatSigInput_Struct
typedef struct
{
  boolean LSI_EnableByAcuFd;
  uint8 LSI_AutnmsDrvModSts;
  Float LSI_ActualLgtSpeed;
  Float LSI_ActualLatSpeed;
  Float LSI_ActualLgtAcc;
  Float LSI_ActualLatAcc;
  Float LSI_SteerWheelAngle;
  Float LSI_SteerWheelAngleRate;
  Float LSI_FrontWheelSteerAngle;
  Float LSI_VehYawRate;
  Float LSI_VehYawRateStd;
  uint8 LSI_DriverInterventionState;
  uint8 LSI_TurnSignalState;
  Float LSI_AccPedalPosition;
  uint8 LSI_BuckledUpState;
  boolean LSI_TurnSignalHazard;
  boolean LSI_TurnSignalLeft;
  boolean LSI_TurnSignalRight;
  boolean LSI_DoorOpen;
  boolean LSI_ReadyToStart;
  boolean LSI_CurrentGearReverse;
  boolean LSI_CurrentGearParking;
  boolean LSI_CurrentGearNeutral;
  boolean LSI_MoveBackward;
  boolean LSI_EnableWiper;
  uint8 LSI_WiperState;
  UInt32 LSI_CameraTimeStamp;
  uint8 LSI_LaneChangeState;
  Float LSI_PosY0Lf;
  Float LSI_HeadingLf;
  Float LSI_CurvatureLf;
  Float LSI_CrvRateLf;
  Float LSI_ValidLengthLf;
  Float LSI_LaneQualityLf;
  uint8 LSI_MarkerTypeLf;
  uint8 LSI_ColorTypeLf;
  boolean LSI_AvailableLf;
  Float LSI_PosY0StdLf;
  Float LSI_HeadingStdLf;
  Float LSI_CurvatureStdLf;
  Float LSI_CrvRateStdLf;
  Float LSI_PosY0Ri;
  Float LSI_HeadingRi;
  Float LSI_CurvatureRi;
  Float LSI_CrvRateRi;
  Float LSI_ValidLengthRi;
  Float LSI_LaneQualityRi;
  uint8 LSI_MarkerTypeRi;
  Float LSI_ColorTypeRi;
  boolean LSI_AvailableRi;
  Float LSI_PosY0StdRi;
  Float LSI_HeadingStdRi;
  Float LSI_CurvatureStdRi;
  Float LSI_CrvRateStdRi;
  Float LSI_PosY0LfAdj;
  Float LSI_HeadingLfAdj;
  Float LSI_CurvatureLfAdj;
  Float LSI_CrvRateLfAdj;
  Float LSI_ValidLengthLfAdj;
  Float LSI_LaneQualityLfAdj;
  uint8 LSI_MarkerTypeLfAdj;
  uint8 LSI_ColorTypeLfAdj;
  boolean LSI_AvailableLfAdj;
  Float LSI_PosY0RiAdj;
  Float LSI_HeadingRiAdj;
  Float LSI_CurvatureRiAdj;
  Float LSI_CrvRateRiAdj;
  Float LSI_ValidLengthRiAdj;
  Float LSI_LaneQualityRiAdj;
  uint8 LSI_MarkerTypeRiAdj;
  uint8 LSI_ColorTypeRiAdj;
  boolean LSI_AvailableRiAdj;
} LSI_LatSigInput_Struct;

# define Rte_TypeDef_LocalizationPoseInput
typedef struct
{
  uint32 timestamp_ms;
  float32 x;
  float32 y;
  float32 yaw;
  float32 pitch;
  float32 vel_x;
  float32 vel_y;
  float32 vel_yaw;
  float32 acc_forward;
  float32 acc_right;
  boolean is_unreliable;
} LocalizationPoseInput;

# define Rte_TypeDef_Metrics
typedef struct
{
  float32 error_longitudinal;
  float32 error_velocity;
  float32 error_heading;
  float32 error_lateral;
} Metrics;

# define Rte_TypeDef_OPS_ObjectList_Struct
typedef struct
{
  Float OPS_MainObjectWidth;
  boolean OPS_MainObjectValid;
  Float OPS_MainObjectHeadingAngle;
  uint8 OPS_MainObjectBehavior;
  Float OPS_MainObjectDx;
  Float OPS_MainObjectDy;
  Float OPS_MainObjectVx;
  Float OPS_MainObjectVy;
  Float OPS_MainObjectAx;
  Float OPS_MainObjectAy;
} OPS_ObjectList_Struct;

# define Rte_TypeDef_PIDDebug
typedef struct
{
  float32 pid_total;
  float32 p_component;
  float32 i_component;
  float32 d_component;
} PIDDebug;

# define Rte_TypeDef_SG_ALgtMaxAvl
typedef struct
{
  uint8 ALgtMaxAvlALgtMaxAvl;
  uint8 ALgtMaxAvlALgtMaxAvlLong;
  uint8 ALgtMaxAvlChks;
  uint8 ALgtMaxAvlCntr;
  uint8 ALgtMaxAvlDecelLgtMaxAvl;
} SG_ALgtMaxAvl;

# define Rte_TypeDef_SG_AdFreeDst
typedef struct
{
  uint8 AdFreeDstChks;
  uint8 AdFreeDstCntr;
  uint8 AdFreeDstFreeDstFwd;
  uint8 AdFreeDstFreeDstRvs;
} SG_AdFreeDst;

# define Rte_TypeDef_SG_AdNomALgtReqGroupSafe
typedef struct
{
  uint16 AdNomALgtReqGroupSafeALgtNomReqMax;
  uint16 AdNomALgtReqGroupSafeALgtNomReqMin;
  uint8 AdNomALgtReqGroupSafeChks;
  uint8 AdNomALgtReqGroupSafeCntr;
  uint16 AdNomALgtReqGroupSafeNegLimForJerk;
  uint16 AdNomALgtReqGroupSafePosLimForJerk;
} SG_AdNomALgtReqGroupSafe;

# define Rte_TypeDef_SG_AdNomALgtReqGroupSafe_A
typedef struct
{
  uint16 AdNomALgtReqGroupSafeALgtNomReqMax_A;
  uint16 AdNomALgtReqGroupSafeALgtNomReqMin_A;
  uint8 AdNomALgtReqGroupSafeChks_A;
  uint8 AdNomALgtReqGroupSafeCntr_A;
  uint16 AdNomALgtReqGroupSafeNegLimForJerk_A;
  uint16 AdNomALgtReqGroupSafePosLimForJerk_A;
} SG_AdNomALgtReqGroupSafe_A;

# define Rte_TypeDef_SG_AdPrimALgtLimReqGroupSafe
typedef struct
{
  uint16 AdPrimALgtLimReqGroupSafeALgtMaxReq;
  uint16 AdPrimALgtLimReqGroupSafeALgtMinReq;
  uint8 AdPrimALgtLimReqGroupSafeChks;
  uint8 AdPrimALgtLimReqGroupSafeCntr;
} SG_AdPrimALgtLimReqGroupSafe;

# define Rte_TypeDef_SG_AdPrimALgtLimReqGroupSafe_A
typedef struct
{
  uint16 AdPrimALgtLimReqGroupSafeALgtMaxReq_A;
  uint16 AdPrimALgtLimReqGroupSafeALgtMinReq_A;
  uint8 AdPrimALgtLimReqGroupSafeChks_A;
  uint8 AdPrimALgtLimReqGroupSafeCntr_A;
} SG_AdPrimALgtLimReqGroupSafe_A;

# define Rte_TypeDef_SG_AdPrimPose_A
typedef struct
{
  uint32 AdPrimPoseAX_A;
  uint8 AdPrimPoseAXConf_A;
  uint32 AdPrimPoseAY_A;
  uint8 AdPrimPoseAYConf_A;
  uint32 AdPrimPoseAZ_A;
  uint8 AdPrimPoseAZConf_A;
  uint16 AdPrimPoseChks_A;
  uint8 AdPrimPoseCntr_A;
  uint16 AdPrimPoseDataID_A;
  uint16 AdPrimPosePitch_A;
  uint8 AdPrimPosePitchConf_A;
  uint32 AdPrimPosePitchRate_A;
  uint8 AdPrimPosePitchRateConf_A;
  sint32 AdPrimPosePosX_A;
  uint8 AdPrimPosePosXConf_A;
  sint32 AdPrimPosePosY_A;
  uint8 AdPrimPosePosYConf_A;
  uint16 AdPrimPoseRoll_A;
  uint8 AdPrimPoseRollConf_A;
  uint32 AdPrimPoseRollRate_A;
  uint8 AdPrimPoseRollRateConf_A;
  uint32 AdPrimPoseTiStampNanoSec_A;
  uint32 AdPrimPoseTiStampSec_A;
  uint16 AdPrimPoseVX_A;
  uint8 AdPrimPoseVXConf_A;
  uint16 AdPrimPoseVY_A;
  uint8 AdPrimPoseVYConf_A;
  uint32 AdPrimPoseYaw_A;
  uint8 AdPrimPoseYawConf_A;
  uint32 AdPrimPoseYawRate_A;
  uint8 AdPrimPoseYawRateConf_A;
} SG_AdPrimPose_A;

# define Rte_TypeDef_SG_AdPrimWhlAgReqGroupSafe
typedef struct
{
  uint8 AdPrimWhlAgReqGroupSafeChks;
  uint8 AdPrimWhlAgReqGroupSafeCntr;
  uint16 AdPrimWhlAgReqGroupSafeWhlAgReq;
} SG_AdPrimWhlAgReqGroupSafe;

# define Rte_TypeDef_SG_AdPrimWhlAgReqGroupSafe_A
typedef struct
{
  uint8 AdPrimWhlAgReqGroupSafeChks_A;
  uint8 AdPrimWhlAgReqGroupSafeCntr_A;
  uint16 AdPrimWhlAgReqGroupSafeWhlAgReq_A;
} SG_AdPrimWhlAgReqGroupSafe_A;

# define Rte_TypeDef_SG_AdSecALgtLimReqGroupSafe
typedef struct
{
  uint16 AdSecALgtLimReqGroupSafeALgtMaxReq;
  uint16 AdSecALgtLimReqGroupSafeALgtMinReq;
  uint8 AdSecALgtLimReqGroupSafeChks;
  uint8 AdSecALgtLimReqGroupSafeCntr;
} SG_AdSecALgtLimReqGroupSafe;

# define Rte_TypeDef_SG_AdSecALgtLimReqGroupSafe_A
typedef struct
{
  uint16 AdSecALgtLimReqGroupSafeALgtMaxReq_A;
  uint16 AdSecALgtLimReqGroupSafeALgtMinReq_A;
  uint8 AdSecALgtLimReqGroupSafeChks_A;
  uint8 AdSecALgtLimReqGroupSafeCntr_A;
} SG_AdSecALgtLimReqGroupSafe_A;

# define Rte_TypeDef_SG_AdSecWhlAgRateLimEstimdSafe
typedef struct
{
  uint16 AdSecWhlAgRateLimEstimdSafeAdWhlAgRate1;
  uint8 AdSecWhlAgRateLimEstimdSafeChks;
  uint8 AdSecWhlAgRateLimEstimdSafeCntr;
} SG_AdSecWhlAgRateLimEstimdSafe;

# define Rte_TypeDef_SG_AdSecWhlAgReqGroupSafe
typedef struct
{
  uint8 AdSecWhlAgReqGroupSafeChks;
  uint8 AdSecWhlAgReqGroupSafeCntr;
  uint16 AdSecWhlAgReqGroupSafeWhlAgReq;
} SG_AdSecWhlAgReqGroupSafe;

# define Rte_TypeDef_SG_AdSecWhlAgReqGroupSafe_A
typedef struct
{
  uint8 AdSecWhlAgReqGroupSafeChks_A;
  uint8 AdSecWhlAgReqGroupSafeCntr_A;
  uint16 AdSecWhlAgReqGroupSafeWhlAgReq_A;
} SG_AdSecWhlAgReqGroupSafe_A;

# define Rte_TypeDef_SG_BrkDegraded
typedef struct
{
  uint8 BrkDegradedChks;
  uint8 BrkDegradedCntr;
  uint16 BrkDegradedSts;
} SG_BrkDegraded;

# define Rte_TypeDef_SG_BrkDegradedRdnt
typedef struct
{
  uint8 BrkDegradedRdntChks;
  uint8 BrkDegradedRdntCntr;
  uint16 BrkDegradedRdntSts;
} SG_BrkDegradedRdnt;

# define Rte_TypeDef_SG_BrkFricTqTotAtWhlsAct
typedef struct
{
  uint16 BrkFricTqTotAtWhlsActBrkFricTqTotAtWhlsAct;
  uint8 BrkFricTqTotAtWhlsActBrkFricTqTotAtWhlsActChks;
  uint8 BrkFricTqTotAtWhlsActBrkFricTqTotAtWhlsActCntr;
} SG_BrkFricTqTotAtWhlsAct;

# define Rte_TypeDef_SG_SecAdNomALgtReqGroupSafe
typedef struct
{
  uint16 SecAdNomALgtReqGroupSafeALgtNomReqMax;
  uint16 SecAdNomALgtReqGroupSafeALgtNomReqMin;
  uint8 SecAdNomALgtReqGroupSafeChks;
  uint8 SecAdNomALgtReqGroupSafeCntr;
  uint16 SecAdNomALgtReqGroupSafeNegLimForJerk;
  uint16 SecAdNomALgtReqGroupSafePosLimForJerk;
} SG_SecAdNomALgtReqGroupSafe;

# define Rte_TypeDef_SG_SecAdNomALgtReqGroupSafe_A
typedef struct
{
  uint16 SecAdNomALgtReqGroupSafeALgtNomReqMax_A;
  uint16 SecAdNomALgtReqGroupSafeALgtNomReqMin_A;
  uint8 SecAdNomALgtReqGroupSafeChks_A;
  uint8 SecAdNomALgtReqGroupSafeCntr_A;
  uint16 SecAdNomALgtReqGroupSafeNegLimForJerk_A;
  uint16 SecAdNomALgtReqGroupSafePosLimForJerk_A;
} SG_SecAdNomALgtReqGroupSafe_A;

# define Rte_TypeDef_SG_SecMaxALatEstimdGroup
typedef struct
{
  sint16 SecMaxALatEstimdGroupAcceleration;
  sint16 SecMaxALatEstimdGroupJerk;
} SG_SecMaxALatEstimdGroup;

# define Rte_TypeDef_SG_WhlAgReqFb
typedef struct
{
  uint8 WhlAgReqFbChks;
  uint8 WhlAgReqFbCntr;
  uint16 WhlAgReqFbWhlAgRateReqFeedFwd;
  uint16 WhlAgReqFbWhlAgReq;
} SG_WhlAgReqFb;

# define Rte_TypeDef_SG_WhlAgReqFbRdnt
typedef struct
{
  uint8 WhlAgReqFbRdntChks;
  uint8 WhlAgReqFbRdntCntr;
  uint16 WhlAgReqFbRdntWhlAgRateReqFeedFwd;
  uint16 WhlAgReqFbRdntWhlAgReq;
} SG_WhlAgReqFbRdnt;

# define Rte_TypeDef_StbM_OffsetRecordTableHeadType
typedef struct
{
  uint8 OffsetTimeDomain;
} StbM_OffsetRecordTableHeadType;

# define Rte_TypeDef_StbM_PortIdType
typedef struct
{
  uint64 clockIdentity;
  uint16 portNumber;
} StbM_PortIdType;

# define Rte_TypeDef_StbM_SyncRecordTableHeadType
typedef struct
{
  uint8 SynchronizedTimeDomain;
  uint32 HWfrequency;
  uint32 HWprescaler;
} StbM_SyncRecordTableHeadType;

# define Rte_TypeDef_StbM_TimeStampShortType
typedef struct
{
  uint32 nanoseconds;
  uint32 seconds;
} StbM_TimeStampShortType;

# define Rte_TypeDef_StbM_UserDataType
typedef struct
{
  uint8 userDataLength;
  uint8 userByte0;
  uint8 userByte1;
  uint8 userByte2;
} StbM_UserDataType;

# define Rte_TypeDef_StbM_VirtualLocalTimeType
typedef struct
{
  uint32 nanosecondsLo;
  uint32 nanosecondsHi;
} StbM_VirtualLocalTimeType;

# define Rte_TypeDef_TrajectoryPose
typedef struct
{
  uint32 timestamp_ms;
  float32 x_pos;
  float32 y_pos;
  float32 heading;
  float32 steering;
  float32 speed;
  float32 accel;
  float32 curvature;
} TrajectoryPose;

# define Rte_TypeDef_VDP_VehicleState_Struct
typedef struct
{
  uint8 VDP_DrvStInvalidBtf;
  uint8 VDP_ActiveStCtrlBtf;
  uint8 VDP_SysStNotAvailableBtf;
  uint8 VDP_SystemStateErrorBtf;
  UInt16 VDP_VehStateInvalidBtf;
} VDP_VehicleState_Struct;

# define Rte_TypeDef_VSI_McuCanTimeout_Struct
typedef struct
{
  float32 VSI_VehMid3SsmCounter0Timer;
  float32 VSI_VehMid3SsmCounter1Timer;
  float32 VSI_VehMid3VcuCounter0Timer;
  float32 VSI_VehMid3VcuCounter1Timer;
  float32 VSI_VehMid5SsmCounter0Timer;
  float32 VSI_VehMid5SsmCounter1Timer;
  float32 VSI_VehMid6SsmCounter0Timer;
  float32 VSI_VehMid6SsmCounter1Timer;
  float32 VSI_AcuMid3SsmCounter0Timer;
  float32 VSI_AcuMid3SsmCounter1Timer;
  float32 VSI_AcuMid5SsmCounter0Timer;
  float32 VSI_AcuMid5SsmCounter1Timer;
  float32 VSI_AcuMid6SsmCounter0Timer;
  float32 VSI_AcuMid6SsmCounter1Timer;
  float32 VSI_AcuFbCanTimer;
  boolean VSI_VehMid3SsmCounter0Timeout;
  boolean VSI_VehMid3SsmCounter1Timeout;
  boolean VSI_VehMid3VcuCounter0Timeout;
  boolean VSI_VehMid3VcuCounter1Timeout;
  boolean VSI_VehMid5SsmCounter0Timeout;
  boolean VSI_VehMid5SsmCounter1Timeout;
  boolean VSI_VehMid6SsmCounter0Timeout;
  boolean VSI_VehMid6SsmCounter1Timeout;
  boolean VSI_AcuMid3SsmCounter0Timeout;
  boolean VSI_AcuMid3SsmCounter1Timeout;
  boolean VSI_AcuMid5SsmCounter0Timeout;
  boolean VSI_AcuMid5SsmCounter1Timeout;
  boolean VSI_AcuMid6SsmCounter0Timeout;
  boolean VSI_AcuMid6SsmCounter1Timeout;
  boolean VSI_AcuFbCanTimeout;
} VSI_McuCanTimeout_Struct;

# define Rte_TypeDef_VSI_VehInfoFor1V1R_Struct
typedef struct
{
  Float VSI_PrimWhlAgSpdFrntSafeLe;
  uint8 VSI_PrimWhlAgSpdFrntSafeLeQf;
  Float VSI_PrimWhlAgSpdFrntSafeRi;
  uint8 VSI_PrimWhlAgSpdFrntSafeRiQf;
  Float VSI_PrimWhlAgSpdReSafeLe;
  uint8 VSI_PrimWhlAgSpdReSafeLeQf;
  Float VSI_PrimWhlAgSpdReSafeRi;
  uint8 VSI_PrimWhlAgSpdReSafeRiQf;
  Float VSI_PrimALatDataRawSafeNom;
  uint8 VSI_PrimALatDataRawSafeNomQf;
  Float VSI_YawRate1;
  uint8 VSI_YawRate1Qf1;
  Float VSI_PrimALgtDataRawSafeNom;
  uint8 VSI_PrimALgtDataRawSafeNomQf;
  Float VSI_SteerWhlAgSafe;
  Float VSI_SteerWhlAgSpdSafe;
  uint8 VSI_SteerWhlSnsrQf;
  Float VSI_PrimVehSpdGroupSafeNom;
  uint8 VSI_PrimVehSpdGroupSafeNomQf;
  uint8 VSI_WhlLockStsLockSts;
  uint8 VSI_PrpsnTqDirAct;
} VSI_VehInfoFor1V1R_Struct;

# define Rte_TypeDef_VSI_VehicleInfo_Struct
typedef struct
{
  Float VSI_LongitudinalVelocity;
  Float VSI_LateralVelocity;
  Float VSI_LongitudinalAcceleration;
  Float VSI_LateralAcceleration;
  Float VSI_YawRate;
  Float VSI_FrontWheelSteeringAngle;
  Float VSI_FrontWheelAngularVelocity;
  Float VSI_SteeringWheelAngle;
  Float VSI_SteeringAngularVelocity;
  uint8 VSI_TurnSignalStatus;
  uint8 VSI_MovementDirection;
  boolean VSI_VehicleAutofullReady;
  boolean VSI_FallbackActive;
  uint8 VSI_VehicleMode;
  uint8 VSI_FallbackMode;
  boolean VSI_ForwardCollisionWarning;
  boolean VSI_RearCollisionWarning;
} VSI_VehicleInfo_Struct;

# define Rte_TypeDef_VSP_VehicleSignal_Struct
typedef struct
{
  boolean VSP_VehicleStandstill;
  Float VSP_ThrottlePedalPosition;
  boolean VSP_BrakePedalPressed;
  Float VSP_BrakeMstCylPressure;
  Float VSP_FrontWheelAngle;
  Float VSP_LgtVehSpdFild;
  Float VSP_LatVehSpdFild;
  Float VSP_LgtVehAccFild;
  Float VSP_LatVehAccFild;
  Float VSP_YawRateFild;
  Float VSP_StrWhlAngleFild;
  Float VSP_StrWhlAngleRateFild;
} VSP_VehicleSignal_Struct;

# define Rte_TypeDef_AccRequestAfterRateLimit
typedef uint16 AccRequestAfterRateLimit;

# define Rte_TypeDef_AccRequestByOutOfOdd
typedef uint16 AccRequestByOutOfOdd;

# define Rte_TypeDef_AccRequestBySpeed
typedef uint16 AccRequestBySpeed;

# define Rte_TypeDef_AccRequestForSystemError
typedef uint16 AccRequestForSystemError;

# define Rte_TypeDef_AccrPedlRat
typedef uint16 AccrPedlRat;

# define Rte_TypeDef_AcuFbCanMessageID
typedef uint16 AcuFbCanMessageID;

# define Rte_TypeDef_AcuFbCanTimeout
typedef boolean AcuFbCanTimeout;

# define Rte_TypeDef_AcuFbCanTimer
typedef uint16 AcuFbCanTimer;

# define Rte_TypeDef_AcuFbReserved
typedef uint8 AcuFbReserved;

# define Rte_TypeDef_AcuFbReserved1
typedef uint8 AcuFbReserved1;

# define Rte_TypeDef_AcuFbReserved2
typedef uint8 AcuFbReserved2;

# define Rte_TypeDef_AcuFbReserved3
typedef uint8 AcuFbReserved3;

# define Rte_TypeDef_AcuFbReserved4
typedef sint16 AcuFbReserved4;

# define Rte_TypeDef_AcuFbRollingCounter
typedef uint8 AcuFbRollingCounter;

# define Rte_TypeDef_AcuMid3SsmCounter0MessageID
typedef uint16 AcuMid3SsmCounter0MessageID;

# define Rte_TypeDef_AcuMid3SsmCounter0Timeout
typedef boolean AcuMid3SsmCounter0Timeout;

# define Rte_TypeDef_AcuMid3SsmCounter0Timer
typedef uint16 AcuMid3SsmCounter0Timer;

# define Rte_TypeDef_AcuMid3SsmCounter1MessageID
typedef uint16 AcuMid3SsmCounter1MessageID;

# define Rte_TypeDef_AcuMid3SsmCounter1Timeout
typedef boolean AcuMid3SsmCounter1Timeout;

# define Rte_TypeDef_AcuMid3SsmCounter1Timer
typedef uint16 AcuMid3SsmCounter1Timer;

# define Rte_TypeDef_AcuMid5SsmCounter0MessageID
typedef uint16 AcuMid5SsmCounter0MessageID;

# define Rte_TypeDef_AcuMid5SsmCounter0Timeout
typedef boolean AcuMid5SsmCounter0Timeout;

# define Rte_TypeDef_AcuMid5SsmCounter0Timer
typedef uint16 AcuMid5SsmCounter0Timer;

# define Rte_TypeDef_AcuMid5SsmCounter1MessageID
typedef uint16 AcuMid5SsmCounter1MessageID;

# define Rte_TypeDef_AcuMid5SsmCounter1Timeout
typedef boolean AcuMid5SsmCounter1Timeout;

# define Rte_TypeDef_AcuMid5SsmCounter1Timer
typedef uint16 AcuMid5SsmCounter1Timer;

# define Rte_TypeDef_AcuMid6SsmCounter0MessageID
typedef uint16 AcuMid6SsmCounter0MessageID;

# define Rte_TypeDef_AcuMid6SsmCounter0Timeout
typedef boolean AcuMid6SsmCounter0Timeout;

# define Rte_TypeDef_AcuMid6SsmCounter0Timer
typedef uint16 AcuMid6SsmCounter0Timer;

# define Rte_TypeDef_AcuMid6SsmCounter1MessageID
typedef uint16 AcuMid6SsmCounter1MessageID;

# define Rte_TypeDef_AcuMid6SsmCounter1Timeout
typedef boolean AcuMid6SsmCounter1Timeout;

# define Rte_TypeDef_AcuMid6SsmCounter1Timer
typedef uint16 AcuMid6SsmCounter1Timer;

# define Rte_TypeDef_AcuPoseRollingCounter
typedef uint8 AcuPoseRollingCounter;

# define Rte_TypeDef_AdActvnOkFromVehDyn
typedef boolean AdActvnOkFromVehDyn;

# define Rte_TypeDef_AdSetSpd
typedef uint16 AdSetSpd;

# define Rte_TypeDef_AdsFaultStatus
typedef uint8 AdsFaultStatus;

# define Rte_TypeDef_AdsFaultStatusBackup
typedef uint8 AdsFaultStatusBackup;

# define Rte_TypeDef_AhbcIndcnToAPI
typedef uint8 AhbcIndcnToAPI;

# define Rte_TypeDef_AswSoftwarewareVersion
typedef uint8 AswSoftwarewareVersion;

# define Rte_TypeDef_AsySftyBrkDlyEstimd
typedef uint8 AsySftyBrkDlyEstimd;

# define Rte_TypeDef_AsySftyDecelEnadByVehDyn
typedef boolean AsySftyDecelEnadByVehDyn;

# define Rte_TypeDef_AvoidCollisionEnable
typedef boolean AvoidCollisionEnable;

# define Rte_TypeDef_BattChrgnTiEstimdExt
typedef uint16 BattChrgnTiEstimdExt;

# define Rte_TypeDef_BattIExt
typedef uint16 BattIExt;

# define Rte_TypeDef_BattUExt
typedef uint16 BattUExt;

# define Rte_TypeDef_BootLoaderVersion
typedef uint8 BootLoaderVersion;

# define Rte_TypeDef_BswM_ESH_Mode
typedef uint8 BswM_ESH_Mode;

# define Rte_TypeDef_BswM_ESH_RunRequest
typedef uint8 BswM_ESH_RunRequest;

# define Rte_TypeDef_BswSoftwarewareVersion
typedef uint8 BswSoftwarewareVersion;

# define Rte_TypeDef_CarTiGlb_A
typedef uint32 CarTiGlb_A;

# define Rte_TypeDef_ChrgnTypExt
typedef uint8 ChrgnTypExt;

# define Rte_TypeDef_ChrgnUReqExt
typedef uint8 ChrgnUReqExt;

# define Rte_TypeDef_ChrgrHndlStsExt
typedef uint8 ChrgrHndlStsExt;

# define Rte_TypeDef_CllsnFwdWarnReq
typedef boolean CllsnFwdWarnReq;

# define Rte_TypeDef_CllsnThreat
typedef uint8 CllsnThreat;

# define Rte_TypeDef_CnclReqForCrsCtrl
typedef uint8 CnclReqForCrsCtrl;

# define Rte_TypeDef_ComM_InhibitionStatusType
typedef uint8 ComM_InhibitionStatusType;

# define Rte_TypeDef_ComM_ModeType
typedef uint8 ComM_ModeType;

# define Rte_TypeDef_ComM_UserHandleType
typedef uint16 ComM_UserHandleType;

# define Rte_TypeDef_ControlErrorHeading
typedef sint16 ControlErrorHeading;

# define Rte_TypeDef_ControlErrorLateral
typedef sint8 ControlErrorLateral;

# define Rte_TypeDef_ControlErrorLongitudinal
typedef sint16 ControlErrorLongitudinal;

# define Rte_TypeDef_ControlErrorVelocity
typedef sint16 ControlErrorVelocity;

# define Rte_TypeDef_ControlReserve0
typedef sint16 ControlReserve0;

# define Rte_TypeDef_ControlReserve1
typedef sint16 ControlReserve1;

# define Rte_TypeDef_ControlReserve2
typedef boolean ControlReserve2;

# define Rte_TypeDef_ControlReserve3
typedef sint16 ControlReserve3;

# define Rte_TypeDef_ControlReserve4
typedef sint16 ControlReserve4;

# define Rte_TypeDef_ControlReserve5
typedef sint16 ControlReserve5;

# define Rte_TypeDef_ControlReserve6
typedef sint16 ControlReserve6;

# define Rte_TypeDef_ControlReserve7
typedef sint16 ControlReserve7;

# define Rte_TypeDef_ControlReserve8
typedef sint16 ControlReserve8;

# define Rte_TypeDef_ControlReserve9
typedef sint32 ControlReserve9;

# define Rte_TypeDef_ControlWheelAngleOffset
typedef sint32 ControlWheelAngleOffset;

# define Rte_TypeDef_CooltFlowInDtElecForExt
typedef uint16 CooltFlowInDtElecForExt;

# define Rte_TypeDef_CooltTInDtElecForExt
typedef uint16 CooltTInDtElecForExt;

# define Rte_TypeDef_Dcm_CommunicationModeType
typedef uint8 Dcm_CommunicationModeType;

# define Rte_TypeDef_Dcm_ConfirmationStatusType
typedef uint8 Dcm_ConfirmationStatusType;

# define Rte_TypeDef_Dcm_ControlDtcSettingType
typedef uint8 Dcm_ControlDtcSettingType;

# define Rte_TypeDef_Dcm_DiagnosticSessionControlType
typedef uint8 Dcm_DiagnosticSessionControlType;

# define Rte_TypeDef_Dcm_EcuResetType
typedef uint8 Dcm_EcuResetType;

# define Rte_TypeDef_Dcm_NegativeResponseCodeType
typedef uint8 Dcm_NegativeResponseCodeType;

# define Rte_TypeDef_Dcm_OpStatusType
typedef uint8 Dcm_OpStatusType;

# define Rte_TypeDef_Dcm_ProtocolType
typedef uint8 Dcm_ProtocolType;

# define Rte_TypeDef_Dcm_RequestKindType
typedef uint8 Dcm_RequestKindType;

# define Rte_TypeDef_Dcm_SecLevelType
typedef uint8 Dcm_SecLevelType;

# define Rte_TypeDef_Dcm_SesCtrlType
typedef uint8 Dcm_SesCtrlType;

# define Rte_TypeDef_Dem_DTCFormatType
typedef uint8 Dem_DTCFormatType;

# define Rte_TypeDef_Dem_DTCGroupType
typedef uint32 Dem_DTCGroupType;

# define Rte_TypeDef_Dem_DTCKindType
typedef uint8 Dem_DTCKindType;

# define Rte_TypeDef_Dem_DTCOriginType
typedef uint16 Dem_DTCOriginType;

# define Rte_TypeDef_Dem_DTCSeverityType
typedef uint8 Dem_DTCSeverityType;

# define Rte_TypeDef_Dem_DTCStatusMaskType
typedef uint8 Dem_DTCStatusMaskType;

# define Rte_TypeDef_Dem_DTRControlType
typedef uint8 Dem_DTRControlType;

# define Rte_TypeDef_Dem_DebounceResetStatusType
typedef uint8 Dem_DebounceResetStatusType;

# define Rte_TypeDef_Dem_DebouncingStateType
typedef uint8 Dem_DebouncingStateType;

# define Rte_TypeDef_Dem_EventIdType
typedef uint16 Dem_EventIdType;

# define Rte_TypeDef_Dem_EventStatusType
typedef uint8 Dem_EventStatusType;

# define Rte_TypeDef_Dem_IndicatorStatusType
typedef uint8 Dem_IndicatorStatusType;

# define Rte_TypeDef_Dem_InitMonitorReasonType
typedef uint8 Dem_InitMonitorReasonType;

# define Rte_TypeDef_Dem_IumprDenomCondIdType
typedef uint8 Dem_IumprDenomCondIdType;

# define Rte_TypeDef_Dem_IumprDenomCondStatusType
typedef uint8 Dem_IumprDenomCondStatusType;

# define Rte_TypeDef_Dem_IumprReadinessGroupType
typedef uint8 Dem_IumprReadinessGroupType;

# define Rte_TypeDef_Dem_MonitorStatusType
typedef uint8 Dem_MonitorStatusType;

# define Rte_TypeDef_Dem_OperationCycleStateType
typedef uint8 Dem_OperationCycleStateType;

# define Rte_TypeDef_Dem_RatioIdType
typedef uint16 Dem_RatioIdType;

# define Rte_TypeDef_Dem_UdsStatusByteType
typedef uint8 Dem_UdsStatusByteType;

# define Rte_TypeDef_DoorDrvrMovmtFailNotif
typedef uint8 DoorDrvrMovmtFailNotif;

# define Rte_TypeDef_DoorDrvrReMovmtFailNotif
typedef uint8 DoorDrvrReMovmtFailNotif;

# define Rte_TypeDef_DoorDrvrReSts
typedef uint8 DoorDrvrReSts;

# define Rte_TypeDef_DoorDrvrSts
typedef uint8 DoorDrvrSts;

# define Rte_TypeDef_DoorPassMovmtFailNotif
typedef uint8 DoorPassMovmtFailNotif;

# define Rte_TypeDef_DoorPassReMovmtFailNotif
typedef uint8 DoorPassReMovmtFailNotif;

# define Rte_TypeDef_DoorPassReSts
typedef uint8 DoorPassReSts;

# define Rte_TypeDef_DoorPassSts
typedef uint8 DoorPassSts;

# define Rte_TypeDef_DrvrDecelReq
typedef sint8 DrvrDecelReq;

# define Rte_TypeDef_ESC_ABA_active
typedef boolean ESC_ABA_active;

# define Rte_TypeDef_ESC_ABA_available
typedef boolean ESC_ABA_available;

# define Rte_TypeDef_ESC_ABP_active
typedef boolean ESC_ABP_active;

# define Rte_TypeDef_ESC_ABP_available
typedef boolean ESC_ABP_available;

# define Rte_TypeDef_ESC_ABSActive
typedef boolean ESC_ABSActive;

# define Rte_TypeDef_ESC_AEB_active
typedef boolean ESC_AEB_active;

# define Rte_TypeDef_ESC_AEB_available
typedef boolean ESC_AEB_available;

# define Rte_TypeDef_ESC_AVHStatus
typedef uint8 ESC_AVHStatus;

# define Rte_TypeDef_ESC_AWB_active
typedef boolean ESC_AWB_active;

# define Rte_TypeDef_ESC_AWB_available
typedef boolean ESC_AWB_available;

# define Rte_TypeDef_ESC_BrakePedalSwitchInvalid
typedef boolean ESC_BrakePedalSwitchInvalid;

# define Rte_TypeDef_ESC_BrakePedalSwitchStatus
typedef boolean ESC_BrakePedalSwitchStatus;

# define Rte_TypeDef_ESC_BrakeTempTooHigh
typedef boolean ESC_BrakeTempTooHigh;

# define Rte_TypeDef_ESC_DA_MESSAGE_AliveCounter
typedef uint8 ESC_DA_MESSAGE_AliveCounter;

# define Rte_TypeDef_ESC_DA_MESSAGE_Checksum
typedef uint8 ESC_DA_MESSAGE_Checksum;

# define Rte_TypeDef_ESC_DTC_Active
typedef boolean ESC_DTC_Active;

# define Rte_TypeDef_ESC_DiagExtModSts
typedef boolean ESC_DiagExtModSts;

# define Rte_TypeDef_ESC_EPBStatus
typedef uint8 ESC_EPBStatus;

# define Rte_TypeDef_ESC_ESPActive
typedef boolean ESC_ESPActive;

# define Rte_TypeDef_ESC_ESPFailed
typedef boolean ESC_ESPFailed;

# define Rte_TypeDef_ESC_FLWheelDirection
typedef uint8 ESC_FLWheelDirection;

# define Rte_TypeDef_ESC_FLWheelSpeedInvalid
typedef boolean ESC_FLWheelSpeedInvalid;

# define Rte_TypeDef_ESC_FLWheelSpeedKPH
typedef uint16 ESC_FLWheelSpeedKPH;

# define Rte_TypeDef_ESC_FRWheelDirection
typedef uint8 ESC_FRWheelDirection;

# define Rte_TypeDef_ESC_FRWheelSpeedInvalid
typedef boolean ESC_FRWheelSpeedInvalid;

# define Rte_TypeDef_ESC_FRWheelSpeedKPH
typedef uint16 ESC_FRWheelSpeedKPH;

# define Rte_TypeDef_ESC_FrontWheelSpeedsKPH_AliveCounter
typedef uint8 ESC_FrontWheelSpeedsKPH_AliveCounter;

# define Rte_TypeDef_ESC_FrontWheelSpeedsKPH_Checksum
typedef uint8 ESC_FrontWheelSpeedsKPH_Checksum;

# define Rte_TypeDef_ESC_HHCActive
typedef boolean ESC_HHCActive;

# define Rte_TypeDef_ESC_Mcylinder_Pressure
typedef uint8 ESC_Mcylinder_Pressure;

# define Rte_TypeDef_ESC_Mcylinder_PressureInvalid
typedef boolean ESC_Mcylinder_PressureInvalid;

# define Rte_TypeDef_ESC_NoBrakeForce
typedef boolean ESC_NoBrakeForce;

# define Rte_TypeDef_ESC_PATAResponse
typedef boolean ESC_PATAResponse;

# define Rte_TypeDef_ESC_QDCFRS
typedef boolean ESC_QDCFRS;

# define Rte_TypeDef_ESC_RLWheelDirection
typedef uint8 ESC_RLWheelDirection;

# define Rte_TypeDef_ESC_RLWheelSpeedInvalid
typedef boolean ESC_RLWheelSpeedInvalid;

# define Rte_TypeDef_ESC_RLWheelSpeedKPH
typedef uint16 ESC_RLWheelSpeedKPH;

# define Rte_TypeDef_ESC_RRWheelDirection
typedef uint8 ESC_RRWheelDirection;

# define Rte_TypeDef_ESC_RRWheelSpeedInvalid
typedef boolean ESC_RRWheelSpeedInvalid;

# define Rte_TypeDef_ESC_RRWheelSpeedKPH
typedef uint16 ESC_RRWheelSpeedKPH;

# define Rte_TypeDef_ESC_RearWheelSpeedsKPH_AliveCounter
typedef uint8 ESC_RearWheelSpeedsKPH_AliveCounter;

# define Rte_TypeDef_ESC_RearWheelSpeedsKPH_Checksum
typedef uint8 ESC_RearWheelSpeedsKPH_Checksum;

# define Rte_TypeDef_ESC_Status_AliveCounter
typedef uint8 ESC_Status_AliveCounter;

# define Rte_TypeDef_ESC_Status_Checksum
typedef uint8 ESC_Status_Checksum;

# define Rte_TypeDef_ESC_TCSActive
typedef boolean ESC_TCSActive;

# define Rte_TypeDef_ESC_VehicleSpeed
typedef uint16 ESC_VehicleSpeed;

# define Rte_TypeDef_ESC_VehicleSpeedInvalid
typedef boolean ESC_VehicleSpeedInvalid;

# define Rte_TypeDef_ESC_Vehiclestandstill
typedef uint8 ESC_Vehiclestandstill;

# define Rte_TypeDef_EcuM_BootTargetType
typedef uint8 EcuM_BootTargetType;

# define Rte_TypeDef_EcuM_ModeType
typedef uint8 EcuM_ModeType;

# define Rte_TypeDef_EcuM_ShutdownCauseType
typedef uint8 EcuM_ShutdownCauseType;

# define Rte_TypeDef_EcuM_StateType
typedef uint8 EcuM_StateType;

# define Rte_TypeDef_EcuM_TimeType
typedef uint32 EcuM_TimeType;

# define Rte_TypeDef_EcuM_UserType
typedef uint8 EcuM_UserType;

# define Rte_TypeDef_EgoLaneLightColor
typedef uint8 EgoLaneLightColor;

# define Rte_TypeDef_EgoLaneWidth
typedef uint16 EgoLaneWidth;

# define Rte_TypeDef_EgoStopTime
typedef uint16 EgoStopTime;

# define Rte_TypeDef_EgyAvlChrgTot
typedef uint16 EgyAvlChrgTot;

# define Rte_TypeDef_EgyAvlDchaTot
typedef uint16 EgyAvlDchaTot;

# define Rte_TypeDef_EmergencyBrakeAcc
typedef uint16 EmergencyBrakeAcc;

# define Rte_TypeDef_EngOilLvlSts
typedef uint8 EngOilLvlSts;

# define Rte_TypeDef_EngOilPWarn
typedef boolean EngOilPWarn;

# define Rte_TypeDef_EstimationStateType
typedef uint8 EstimationStateType;

# define Rte_TypeDef_ExtDcDcActvnAllwd
typedef boolean ExtDcDcActvnAllwd;

# define Rte_TypeDef_FC_CIPV_ID
typedef uint8 FC_CIPV_ID;

# define Rte_TypeDef_FC_FrontCameraCalibrationStatus
typedef uint8 FC_FrontCameraCalibrationStatus;

# define Rte_TypeDef_FC_LaneChangeStatus
typedef uint8 FC_LaneChangeStatus;

# define Rte_TypeDef_FC_LineTiStamp
typedef uint32 FC_LineTiStamp;

# define Rte_TypeDef_FC_Line_01_HeadingAngle
typedef uint16 FC_Line_01_HeadingAngle;

# define Rte_TypeDef_FC_Line_01_Id
typedef uint8 FC_Line_01_Id;

# define Rte_TypeDef_FC_Line_01_Type
typedef uint8 FC_Line_01_Type;

# define Rte_TypeDef_FC_Line_01_Width
typedef uint8 FC_Line_01_Width;

# define Rte_TypeDef_FC_Line_01_color
typedef uint8 FC_Line_01_color;

# define Rte_TypeDef_FC_Line_01_dx_End
typedef uint16 FC_Line_01_dx_End;

# define Rte_TypeDef_FC_Line_01_dx_End_std
typedef uint16 FC_Line_01_dx_End_std;

# define Rte_TypeDef_FC_Line_01_dx_Start
typedef uint16 FC_Line_01_dx_Start;

# define Rte_TypeDef_FC_Line_01_dx_Start_std
typedef uint16 FC_Line_01_dx_Start_std;

# define Rte_TypeDef_FC_Line_01_dy
typedef uint16 FC_Line_01_dy;

# define Rte_TypeDef_FC_Line_01_exist_prob
typedef uint8 FC_Line_01_exist_prob;

# define Rte_TypeDef_FC_Line_02_HeadingAngle
typedef uint16 FC_Line_02_HeadingAngle;

# define Rte_TypeDef_FC_Line_02_Id
typedef uint8 FC_Line_02_Id;

# define Rte_TypeDef_FC_Line_02_Type
typedef uint8 FC_Line_02_Type;

# define Rte_TypeDef_FC_Line_02_Width
typedef uint8 FC_Line_02_Width;

# define Rte_TypeDef_FC_Line_02_color
typedef uint8 FC_Line_02_color;

# define Rte_TypeDef_FC_Line_02_dx_End
typedef uint16 FC_Line_02_dx_End;

# define Rte_TypeDef_FC_Line_02_dx_End_std
typedef uint16 FC_Line_02_dx_End_std;

# define Rte_TypeDef_FC_Line_02_dx_Start
typedef uint16 FC_Line_02_dx_Start;

# define Rte_TypeDef_FC_Line_02_dx_Start_std
typedef uint16 FC_Line_02_dx_Start_std;

# define Rte_TypeDef_FC_Line_02_dy
typedef uint16 FC_Line_02_dy;

# define Rte_TypeDef_FC_Line_02_exist_prob
typedef uint8 FC_Line_02_exist_prob;

# define Rte_TypeDef_FC_Line_03_HeadingAngle
typedef uint16 FC_Line_03_HeadingAngle;

# define Rte_TypeDef_FC_Line_03_Id
typedef uint8 FC_Line_03_Id;

# define Rte_TypeDef_FC_Line_03_Type
typedef uint8 FC_Line_03_Type;

# define Rte_TypeDef_FC_Line_03_Width
typedef uint8 FC_Line_03_Width;

# define Rte_TypeDef_FC_Line_03_color
typedef uint8 FC_Line_03_color;

# define Rte_TypeDef_FC_Line_03_dx_End
typedef uint16 FC_Line_03_dx_End;

# define Rte_TypeDef_FC_Line_03_dx_End_std
typedef uint16 FC_Line_03_dx_End_std;

# define Rte_TypeDef_FC_Line_03_dx_Start
typedef uint16 FC_Line_03_dx_Start;

# define Rte_TypeDef_FC_Line_03_dx_Start_std
typedef uint16 FC_Line_03_dx_Start_std;

# define Rte_TypeDef_FC_Line_03_dy
typedef uint16 FC_Line_03_dy;

# define Rte_TypeDef_FC_Line_03_exist_prob
typedef uint8 FC_Line_03_exist_prob;

# define Rte_TypeDef_FC_Line_04_HeadingAngle
typedef uint16 FC_Line_04_HeadingAngle;

# define Rte_TypeDef_FC_Line_04_Id
typedef uint8 FC_Line_04_Id;

# define Rte_TypeDef_FC_Line_04_Type
typedef uint8 FC_Line_04_Type;

# define Rte_TypeDef_FC_Line_04_Width
typedef uint8 FC_Line_04_Width;

# define Rte_TypeDef_FC_Line_04_color
typedef uint8 FC_Line_04_color;

# define Rte_TypeDef_FC_Line_04_dx_End
typedef uint16 FC_Line_04_dx_End;

# define Rte_TypeDef_FC_Line_04_dx_End_std
typedef uint16 FC_Line_04_dx_End_std;

# define Rte_TypeDef_FC_Line_04_dx_Start
typedef uint16 FC_Line_04_dx_Start;

# define Rte_TypeDef_FC_Line_04_dx_Start_std
typedef uint16 FC_Line_04_dx_Start_std;

# define Rte_TypeDef_FC_Line_04_dy
typedef uint16 FC_Line_04_dy;

# define Rte_TypeDef_FC_Line_04_exist_prob
typedef uint8 FC_Line_04_exist_prob;

# define Rte_TypeDef_FC_Line_05_HeadingAngle
typedef uint16 FC_Line_05_HeadingAngle;

# define Rte_TypeDef_FC_Line_05_Type
typedef uint8 FC_Line_05_Type;

# define Rte_TypeDef_FC_Line_05_Width
typedef uint8 FC_Line_05_Width;

# define Rte_TypeDef_FC_Line_05_color
typedef uint8 FC_Line_05_color;

# define Rte_TypeDef_FC_Line_05_dx_End
typedef uint16 FC_Line_05_dx_End;

# define Rte_TypeDef_FC_Line_05_dx_End_std
typedef uint16 FC_Line_05_dx_End_std;

# define Rte_TypeDef_FC_Line_05_dx_Start
typedef uint16 FC_Line_05_dx_Start;

# define Rte_TypeDef_FC_Line_05_dx_Start_std
typedef uint16 FC_Line_05_dx_Start_std;

# define Rte_TypeDef_FC_Line_05_dy
typedef uint16 FC_Line_05_dy;

# define Rte_TypeDef_FC_Line_05_exist_prob
typedef uint8 FC_Line_05_exist_prob;

# define Rte_TypeDef_FC_Line_06_HeadingAngle
typedef uint16 FC_Line_06_HeadingAngle;

# define Rte_TypeDef_FC_Line_06_Type
typedef uint8 FC_Line_06_Type;

# define Rte_TypeDef_FC_Line_06_Width
typedef uint8 FC_Line_06_Width;

# define Rte_TypeDef_FC_Line_06_color
typedef uint8 FC_Line_06_color;

# define Rte_TypeDef_FC_Line_06_dx_End
typedef uint16 FC_Line_06_dx_End;

# define Rte_TypeDef_FC_Line_06_dx_End_std
typedef uint16 FC_Line_06_dx_End_std;

# define Rte_TypeDef_FC_Line_06_dx_Start
typedef uint16 FC_Line_06_dx_Start;

# define Rte_TypeDef_FC_Line_06_dx_Start_std
typedef uint16 FC_Line_06_dx_Start_std;

# define Rte_TypeDef_FC_Line_06_dy
typedef uint16 FC_Line_06_dy;

# define Rte_TypeDef_FC_Line_06_exist_prob
typedef uint8 FC_Line_06_exist_prob;

# define Rte_TypeDef_FC_Obj1_Ax
typedef uint16 FC_Obj1_Ax;

# define Rte_TypeDef_FC_Obj20_Ax
typedef uint16 FC_Obj20_Ax;

# define Rte_TypeDef_FC_Obj20_Ay
typedef uint8 FC_Obj20_Ay;

# define Rte_TypeDef_FC_Obj20_ClassifiedView
typedef uint8 FC_Obj20_ClassifiedView;

# define Rte_TypeDef_FC_Obj20_Dx
typedef uint16 FC_Obj20_Dx;

# define Rte_TypeDef_FC_Obj20_Dx_Vnce
typedef uint16 FC_Obj20_Dx_Vnce;

# define Rte_TypeDef_FC_Obj20_Dy
typedef uint16 FC_Obj20_Dy;

# define Rte_TypeDef_FC_Obj20_Dy_Vnce
typedef uint16 FC_Obj20_Dy_Vnce;

# define Rte_TypeDef_FC_Obj20_ExistProb
typedef uint8 FC_Obj20_ExistProb;

# define Rte_TypeDef_FC_Obj20_HeadingAngle
typedef uint16 FC_Obj20_HeadingAngle;

# define Rte_TypeDef_FC_Obj20_Height
typedef uint16 FC_Obj20_Height;

# define Rte_TypeDef_FC_Obj20_LaneAssignment
typedef uint8 FC_Obj20_LaneAssignment;

# define Rte_TypeDef_FC_Obj20_Length
typedef uint16 FC_Obj20_Length;

# define Rte_TypeDef_FC_Obj20_MotionType
typedef uint8 FC_Obj20_MotionType;

# define Rte_TypeDef_FC_Obj20_Track_Age
typedef uint8 FC_Obj20_Track_Age;

# define Rte_TypeDef_FC_Obj20_Track_ID
typedef uint8 FC_Obj20_Track_ID;

# define Rte_TypeDef_FC_Obj20_Type
typedef uint8 FC_Obj20_Type;

# define Rte_TypeDef_FC_Obj20_Vx
typedef uint16 FC_Obj20_Vx;

# define Rte_TypeDef_FC_Obj20_Vy
typedef uint16 FC_Obj20_Vy;

# define Rte_TypeDef_FC_Obj20_Width
typedef uint16 FC_Obj20_Width;

# define Rte_TypeDef_FC_RollingCounter
typedef uint8 FC_RollingCounter;

# define Rte_TypeDef_FC_obj20_Brakelight_Info
typedef boolean FC_obj20_Brakelight_Info;

# define Rte_TypeDef_FC_obj20_Taillight_Info
typedef uint8 FC_obj20_Taillight_Info;

# define Rte_TypeDef_FRS_Fail
typedef boolean FRS_Fail;

# define Rte_TypeDef_FRS_HostSpeed
typedef uint16 FRS_HostSpeed;

# define Rte_TypeDef_FRS_Host_Yaw
typedef uint16 FRS_Host_Yaw;

# define Rte_TypeDef_FRS_Latency
typedef uint8 FRS_Latency;

# define Rte_TypeDef_FRS_MeasEnabled
typedef boolean FRS_MeasEnabled;

# define Rte_TypeDef_FRS_Msg_AliveCounter
typedef uint8 FRS_Msg_AliveCounter;

# define Rte_TypeDef_FRS_Msg_CheckSum
typedef uint8 FRS_Msg_CheckSum;

# define Rte_TypeDef_FRS_Status_BlkProg
typedef boolean FRS_Status_BlkProg;

# define Rte_TypeDef_FRS_Status_HWErr
typedef boolean FRS_Status_HWErr;

# define Rte_TypeDef_FRS_Status_MisAlign
typedef uint8 FRS_Status_MisAlign;

# define Rte_TypeDef_FRS_TimeStamp
typedef uint16 FRS_TimeStamp;

# define Rte_TypeDef_FTFC_Line_01_MeasureType
typedef uint8 FTFC_Line_01_MeasureType;

# define Rte_TypeDef_FTFC_Line_01_ObstacleFlg
typedef uint8 FTFC_Line_01_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_01_ParseConf
typedef uint8 FTFC_Line_01_ParseConf;

# define Rte_TypeDef_FTFC_Line_01_RMSE
typedef uint8 FTFC_Line_01_RMSE;

# define Rte_TypeDef_FTFC_Line_01_curvature_alte
typedef uint16 FTFC_Line_01_curvature_alte;

# define Rte_TypeDef_FTFC_Line_01_curve
typedef uint16 FTFC_Line_01_curve;

# define Rte_TypeDef_FTFC_Line_02_MeasureType
typedef uint8 FTFC_Line_02_MeasureType;

# define Rte_TypeDef_FTFC_Line_02_ObstacleFlg
typedef uint8 FTFC_Line_02_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_02_ParseConf
typedef uint8 FTFC_Line_02_ParseConf;

# define Rte_TypeDef_FTFC_Line_02_RMSE
typedef uint8 FTFC_Line_02_RMSE;

# define Rte_TypeDef_FTFC_Line_02_curvature_alte
typedef uint16 FTFC_Line_02_curvature_alte;

# define Rte_TypeDef_FTFC_Line_02_curve
typedef uint16 FTFC_Line_02_curve;

# define Rte_TypeDef_FTFC_Line_03_MeasureType
typedef uint8 FTFC_Line_03_MeasureType;

# define Rte_TypeDef_FTFC_Line_03_ObstacleFlg
typedef uint8 FTFC_Line_03_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_03_ParseConf
typedef uint8 FTFC_Line_03_ParseConf;

# define Rte_TypeDef_FTFC_Line_03_RMSE
typedef uint8 FTFC_Line_03_RMSE;

# define Rte_TypeDef_FTFC_Line_03_curvature_alte
typedef uint16 FTFC_Line_03_curvature_alte;

# define Rte_TypeDef_FTFC_Line_03_curve
typedef uint16 FTFC_Line_03_curve;

# define Rte_TypeDef_FTFC_Line_04_MeasureType
typedef uint8 FTFC_Line_04_MeasureType;

# define Rte_TypeDef_FTFC_Line_04_ObstacleFlg
typedef uint8 FTFC_Line_04_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_04_ParseConf
typedef uint8 FTFC_Line_04_ParseConf;

# define Rte_TypeDef_FTFC_Line_04_RMSE
typedef uint8 FTFC_Line_04_RMSE;

# define Rte_TypeDef_FTFC_Line_04_curvature_alte
typedef uint16 FTFC_Line_04_curvature_alte;

# define Rte_TypeDef_FTFC_Line_04_curve
typedef uint16 FTFC_Line_04_curve;

# define Rte_TypeDef_FTFC_Line_05_MeasureType
typedef uint8 FTFC_Line_05_MeasureType;

# define Rte_TypeDef_FTFC_Line_05_ObstacleFlg
typedef uint8 FTFC_Line_05_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_05_ParseConf
typedef uint8 FTFC_Line_05_ParseConf;

# define Rte_TypeDef_FTFC_Line_05_RMSE
typedef uint8 FTFC_Line_05_RMSE;

# define Rte_TypeDef_FTFC_Line_05_curvature_alte
typedef uint16 FTFC_Line_05_curvature_alte;

# define Rte_TypeDef_FTFC_Line_05_curve
typedef uint16 FTFC_Line_05_curve;

# define Rte_TypeDef_FTFC_Line_06_MeasureType
typedef uint8 FTFC_Line_06_MeasureType;

# define Rte_TypeDef_FTFC_Line_06_ObstacleFlg
typedef uint8 FTFC_Line_06_ObstacleFlg;

# define Rte_TypeDef_FTFC_Line_06_ParseConf
typedef uint8 FTFC_Line_06_ParseConf;

# define Rte_TypeDef_FTFC_Line_06_RMSE
typedef uint8 FTFC_Line_06_RMSE;

# define Rte_TypeDef_FTFC_Line_06_curvature_alte
typedef uint16 FTFC_Line_06_curvature_alte;

# define Rte_TypeDef_FTFC_Line_06_curve
typedef uint16 FTFC_Line_06_curve;

# define Rte_TypeDef_FTFC_Obj20_CenterAngle
typedef uint16 FTFC_Obj20_CenterAngle;

# define Rte_TypeDef_FTFC_Obj20_CornerPoint_x
typedef uint16 FTFC_Obj20_CornerPoint_x;

# define Rte_TypeDef_FTFC_Obj20_CornerPoint_y
typedef uint16 FTFC_Obj20_CornerPoint_y;

# define Rte_TypeDef_FTFC_Obj20_DistInLane
typedef uint16 FTFC_Obj20_DistInLane;

# define Rte_TypeDef_FTFC_Obj20_objCutInFlag
typedef uint8 FTFC_Obj20_objCutInFlag;

# define Rte_TypeDef_FTFC_Obj20_objCutInLane
typedef uint8 FTFC_Obj20_objCutInLane;

# define Rte_TypeDef_FallbackDebugInfoReserve1
typedef uint16 FallbackDebugInfoReserve1;

# define Rte_TypeDef_FallbackDebugInfoReserve2
typedef uint16 FallbackDebugInfoReserve2;

# define Rte_TypeDef_FallbackDebugInfoReserve3
typedef uint16 FallbackDebugInfoReserve3;

# define Rte_TypeDef_FallbackDebugInfoReserve4
typedef uint16 FallbackDebugInfoReserve4;

# define Rte_TypeDef_FallbackDebugInfoReserve5
typedef uint8 FallbackDebugInfoReserve5;

# define Rte_TypeDef_FallbackDebugInfoRollingCounter
typedef uint8 FallbackDebugInfoRollingCounter;

# define Rte_TypeDef_FallbackSelfCheckStatus
typedef uint16 FallbackSelfCheckStatus;

# define Rte_TypeDef_FallbackTriggerStatus
typedef uint8 FallbackTriggerStatus;

# define Rte_TypeDef_FbAcuReserved
typedef uint8 FbAcuReserved;

# define Rte_TypeDef_FbAcuRollingCounter
typedef uint8 FbAcuRollingCounter;

# define Rte_TypeDef_FeedforwardsSteerAngle
typedef uint16 FeedforwardsSteerAngle;

# define Rte_TypeDef_FrFr_AccOBJ_Ax
typedef uint16 FrFr_AccOBJ_Ax;

# define Rte_TypeDef_FrFr_AccOBJ_Ay
typedef uint16 FrFr_AccOBJ_Ay;

# define Rte_TypeDef_FrFr_AccOBJ_Brakelight_Info
typedef boolean FrFr_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_FrFr_AccOBJ_Class
typedef uint8 FrFr_AccOBJ_Class;

# define Rte_TypeDef_FrFr_AccOBJ_Dx
typedef uint16 FrFr_AccOBJ_Dx;

# define Rte_TypeDef_FrFr_AccOBJ_Dx_Vnce
typedef uint16 FrFr_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_FrFr_AccOBJ_Dy
typedef uint16 FrFr_AccOBJ_Dy;

# define Rte_TypeDef_FrFr_AccOBJ_Dy_Vnce
typedef uint16 FrFr_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_FrFr_AccOBJ_ExistProb
typedef uint8 FrFr_AccOBJ_ExistProb;

# define Rte_TypeDef_FrFr_AccOBJ_FusionedFC_Track_ID
typedef uint8 FrFr_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_FrFr_AccOBJ_HeadingAngle
typedef uint16 FrFr_AccOBJ_HeadingAngle;

# define Rte_TypeDef_FrFr_AccOBJ_Height
typedef uint16 FrFr_AccOBJ_Height;

# define Rte_TypeDef_FrFr_AccOBJ_Length
typedef uint16 FrFr_AccOBJ_Length;

# define Rte_TypeDef_FrFr_AccOBJ_ObstacleProb
typedef uint8 FrFr_AccOBJ_ObstacleProb;

# define Rte_TypeDef_FrFr_AccOBJ_Taillight_Info
typedef uint8 FrFr_AccOBJ_Taillight_Info;

# define Rte_TypeDef_FrFr_AccOBJ_Track_Age
typedef uint8 FrFr_AccOBJ_Track_Age;

# define Rte_TypeDef_FrFr_AccOBJ_Track_ID
typedef uint8 FrFr_AccOBJ_Track_ID;

# define Rte_TypeDef_FrFr_AccOBJ_Vx
typedef uint16 FrFr_AccOBJ_Vx;

# define Rte_TypeDef_FrFr_AccOBJ_Vx_std
typedef uint16 FrFr_AccOBJ_Vx_std;

# define Rte_TypeDef_FrFr_AccOBJ_Vy
typedef uint16 FrFr_AccOBJ_Vy;

# define Rte_TypeDef_FrFr_AccOBJ_Vy_std
typedef uint16 FrFr_AccOBJ_Vy_std;

# define Rte_TypeDef_FrFr_AccOBJ_Width
typedef uint16 FrFr_AccOBJ_Width;

# define Rte_TypeDef_FrFr_AccOBJ_confi
typedef uint8 FrFr_AccOBJ_confi;

# define Rte_TypeDef_FrFr_AccOBJ_fusion_Sts
typedef uint8 FrFr_AccOBJ_fusion_Sts;

# define Rte_TypeDef_Fr_AccOBJ_Ax
typedef uint16 Fr_AccOBJ_Ax;

# define Rte_TypeDef_Fr_AccOBJ_Ay
typedef uint16 Fr_AccOBJ_Ay;

# define Rte_TypeDef_Fr_AccOBJ_Brakelight_Info
typedef boolean Fr_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_Fr_AccOBJ_Class
typedef uint8 Fr_AccOBJ_Class;

# define Rte_TypeDef_Fr_AccOBJ_Dx
typedef uint16 Fr_AccOBJ_Dx;

# define Rte_TypeDef_Fr_AccOBJ_Dx_Vnce
typedef uint16 Fr_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_Fr_AccOBJ_Dy
typedef uint16 Fr_AccOBJ_Dy;

# define Rte_TypeDef_Fr_AccOBJ_Dy_Vnce
typedef uint16 Fr_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_Fr_AccOBJ_ExistProb
typedef uint8 Fr_AccOBJ_ExistProb;

# define Rte_TypeDef_Fr_AccOBJ_FusionedFC_Track_ID
typedef uint8 Fr_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_Fr_AccOBJ_HeadingAngle
typedef uint16 Fr_AccOBJ_HeadingAngle;

# define Rte_TypeDef_Fr_AccOBJ_Height
typedef uint16 Fr_AccOBJ_Height;

# define Rte_TypeDef_Fr_AccOBJ_Length
typedef uint16 Fr_AccOBJ_Length;

# define Rte_TypeDef_Fr_AccOBJ_ObstacleProb
typedef uint8 Fr_AccOBJ_ObstacleProb;

# define Rte_TypeDef_Fr_AccOBJ_Taillight_Info
typedef uint8 Fr_AccOBJ_Taillight_Info;

# define Rte_TypeDef_Fr_AccOBJ_Track_Age
typedef uint8 Fr_AccOBJ_Track_Age;

# define Rte_TypeDef_Fr_AccOBJ_Track_ID
typedef uint8 Fr_AccOBJ_Track_ID;

# define Rte_TypeDef_Fr_AccOBJ_Vx
typedef uint16 Fr_AccOBJ_Vx;

# define Rte_TypeDef_Fr_AccOBJ_Vx_std
typedef uint16 Fr_AccOBJ_Vx_std;

# define Rte_TypeDef_Fr_AccOBJ_Vy
typedef uint16 Fr_AccOBJ_Vy;

# define Rte_TypeDef_Fr_AccOBJ_Vy_std
typedef uint16 Fr_AccOBJ_Vy_std;

# define Rte_TypeDef_Fr_AccOBJ_Width
typedef uint16 Fr_AccOBJ_Width;

# define Rte_TypeDef_Fr_AccOBJ_confi
typedef uint8 Fr_AccOBJ_confi;

# define Rte_TypeDef_Fr_AccOBJ_fusion_Sts
typedef uint8 Fr_AccOBJ_fusion_Sts;

# define Rte_TypeDef_FrontCameraCalibrationStatus
typedef uint8 FrontCameraCalibrationStatus;

# define Rte_TypeDef_FrontCameraCanMessageID
typedef uint16 FrontCameraCanMessageID;

# define Rte_TypeDef_FrontCameraCanTimeout
typedef boolean FrontCameraCanTimeout;

# define Rte_TypeDef_FrontCameraCanTimer
typedef uint16 FrontCameraCanTimer;

# define Rte_TypeDef_FrontCameraFailureStatus
typedef uint8 FrontCameraFailureStatus;

# define Rte_TypeDef_FrontObjectTTC
typedef uint16 FrontObjectTTC;

# define Rte_TypeDef_FrontObjectValid
typedef boolean FrontObjectValid;

# define Rte_TypeDef_FrontRadarCalibrationStatus
typedef uint8 FrontRadarCalibrationStatus;

# define Rte_TypeDef_FrontRadarCanMessageID
typedef uint16 FrontRadarCanMessageID;

# define Rte_TypeDef_FrontRadarCanTimeout
typedef boolean FrontRadarCanTimeout;

# define Rte_TypeDef_FrontRadarCanTimer
typedef uint16 FrontRadarCanTimer;

# define Rte_TypeDef_FrontRadarFailureStatus
typedef uint8 FrontRadarFailureStatus;

# define Rte_TypeDef_GnssPoseHeading
typedef sint32 GnssPoseHeading;

# define Rte_TypeDef_GnssPoseHeadingType
typedef uint8 GnssPoseHeadingType;

# define Rte_TypeDef_GnssPoseNumSatsTracked
typedef uint8 GnssPoseNumSatsTracked;

# define Rte_TypeDef_GnssPosePosType
typedef uint8 GnssPosePosType;

# define Rte_TypeDef_GnssPosePositionX
typedef sint32 GnssPosePositionX;

# define Rte_TypeDef_GnssPosePositionY
typedef sint32 GnssPosePositionY;

# define Rte_TypeDef_GnssPoseReserve1
typedef sint32 GnssPoseReserve1;

# define Rte_TypeDef_GnssPoseVelocityX
typedef sint32 GnssPoseVelocityX;

# define Rte_TypeDef_GnssPoseVelocityY
typedef sint32 GnssPoseVelocityY;

# define Rte_TypeDef_GnssPoseYaw
typedef sint32 GnssPoseYaw;

# define Rte_TypeDef_GnsssPoseReserve0
typedef uint8 GnsssPoseReserve0;

# define Rte_TypeDef_GradientLimitAccRequest
typedef uint16 GradientLimitAccRequest;

# define Rte_TypeDef_HeadingAngleContribution
typedef uint8 HeadingAngleContribution;

# define Rte_TypeDef_HeadingAngleError
typedef uint16 HeadingAngleError;

# define Rte_TypeDef_HeadingAngleErrorWeight
typedef uint8 HeadingAngleErrorWeight;

# define Rte_TypeDef_HoodSts
typedef uint8 HoodSts;

# define Rte_TypeDef_HornActvIf
typedef uint8 HornActvIf;

# define Rte_TypeDef_HornSwtStsIf
typedef uint8 HornSwtStsIf;

# define Rte_TypeDef_HvSysActvStsExt1
typedef boolean HvSysActvStsExt1;

# define Rte_TypeDef_IDcDcAvlLoSideExt
typedef uint8 IDcDcAvlLoSideExt;

# define Rte_TypeDef_IDcDcAvlMaxLoSideExt
typedef uint8 IDcDcAvlMaxLoSideExt;

# define Rte_TypeDef_IOHWAB_UINT16
typedef uint16 IOHWAB_UINT16;

# define Rte_TypeDef_IOHWAB_UINT8
typedef uint8 IOHWAB_UINT8;

# define Rte_TypeDef_IndcrDisp1WdSts
typedef uint8 IndcrDisp1WdSts;

# define Rte_TypeDef_IndcrTurnSts1WdSts
typedef uint8 IndcrTurnSts1WdSts;

# define Rte_TypeDef_InhbOfAsySftyDecelByVehDyn
typedef boolean InhbOfAsySftyDecelByVehDyn;

# define Rte_TypeDef_IsInTheHighway
typedef boolean IsInTheHighway;

# define Rte_TypeDef_LaneChangeMode
typedef uint8 LaneChangeMode;

# define Rte_TypeDef_LaneValidState
typedef uint8 LaneValidState;

# define Rte_TypeDef_LatPidDistanceDComponent
typedef sint16 LatPidDistanceDComponent;

# define Rte_TypeDef_LatPidDistanceIComponent
typedef sint16 LatPidDistanceIComponent;

# define Rte_TypeDef_LatPidDistancePComponent
typedef sint16 LatPidDistancePComponent;

# define Rte_TypeDef_LatPidDistanceTotal
typedef sint16 LatPidDistanceTotal;

# define Rte_TypeDef_LatPidHeadingDComponent
typedef sint16 LatPidHeadingDComponent;

# define Rte_TypeDef_LatPidHeadingIComponent
typedef sint16 LatPidHeadingIComponent;

# define Rte_TypeDef_LatPidHeadingPComponent
typedef sint16 LatPidHeadingPComponent;

# define Rte_TypeDef_LatPidHeadingTotal
typedef sint16 LatPidHeadingTotal;

# define Rte_TypeDef_LatSteeringFeeback
typedef sint16 LatSteeringFeeback;

# define Rte_TypeDef_LatSteeringFeedforward
typedef sint16 LatSteeringFeedforward;

# define Rte_TypeDef_LateralContribution
typedef uint8 LateralContribution;

# define Rte_TypeDef_LateralDistanceError
typedef uint16 LateralDistanceError;

# define Rte_TypeDef_LateralDistanceErrorWeight
typedef uint8 LateralDistanceErrorWeight;

# define Rte_TypeDef_LateralSystemState
typedef uint8 LateralSystemState;

# define Rte_TypeDef_LeFr_AccOBJ_Ax
typedef uint16 LeFr_AccOBJ_Ax;

# define Rte_TypeDef_LeFr_AccOBJ_Ay
typedef uint16 LeFr_AccOBJ_Ay;

# define Rte_TypeDef_LeFr_AccOBJ_Brakelight_Info
typedef boolean LeFr_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_LeFr_AccOBJ_Class
typedef uint8 LeFr_AccOBJ_Class;

# define Rte_TypeDef_LeFr_AccOBJ_Dx
typedef uint16 LeFr_AccOBJ_Dx;

# define Rte_TypeDef_LeFr_AccOBJ_Dx_Vnce
typedef uint16 LeFr_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_LeFr_AccOBJ_Dy
typedef uint16 LeFr_AccOBJ_Dy;

# define Rte_TypeDef_LeFr_AccOBJ_Dy_Vnce
typedef uint16 LeFr_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_LeFr_AccOBJ_ExistProb
typedef uint8 LeFr_AccOBJ_ExistProb;

# define Rte_TypeDef_LeFr_AccOBJ_FusionedFC_Track_ID
typedef uint8 LeFr_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_LeFr_AccOBJ_HeadingAngle
typedef uint16 LeFr_AccOBJ_HeadingAngle;

# define Rte_TypeDef_LeFr_AccOBJ_Height
typedef uint16 LeFr_AccOBJ_Height;

# define Rte_TypeDef_LeFr_AccOBJ_Length
typedef uint16 LeFr_AccOBJ_Length;

# define Rte_TypeDef_LeFr_AccOBJ_ObstacleProb
typedef uint8 LeFr_AccOBJ_ObstacleProb;

# define Rte_TypeDef_LeFr_AccOBJ_Taillight_Info
typedef uint8 LeFr_AccOBJ_Taillight_Info;

# define Rte_TypeDef_LeFr_AccOBJ_Track_Age
typedef uint8 LeFr_AccOBJ_Track_Age;

# define Rte_TypeDef_LeFr_AccOBJ_Track_ID
typedef uint8 LeFr_AccOBJ_Track_ID;

# define Rte_TypeDef_LeFr_AccOBJ_Vx
typedef uint16 LeFr_AccOBJ_Vx;

# define Rte_TypeDef_LeFr_AccOBJ_Vx_std
typedef uint16 LeFr_AccOBJ_Vx_std;

# define Rte_TypeDef_LeFr_AccOBJ_Vy
typedef uint16 LeFr_AccOBJ_Vy;

# define Rte_TypeDef_LeFr_AccOBJ_Vy_std
typedef uint16 LeFr_AccOBJ_Vy_std;

# define Rte_TypeDef_LeFr_AccOBJ_Width
typedef uint16 LeFr_AccOBJ_Width;

# define Rte_TypeDef_LeFr_AccOBJ_confi
typedef uint8 LeFr_AccOBJ_confi;

# define Rte_TypeDef_LeFr_AccOBJ_fusion_Sts
typedef uint8 LeFr_AccOBJ_fusion_Sts;

# define Rte_TypeDef_Le_AccOBJ_Ax
typedef uint16 Le_AccOBJ_Ax;

# define Rte_TypeDef_Le_AccOBJ_Ay
typedef uint16 Le_AccOBJ_Ay;

# define Rte_TypeDef_Le_AccOBJ_Brakelight_Info
typedef boolean Le_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_Le_AccOBJ_Class
typedef uint8 Le_AccOBJ_Class;

# define Rte_TypeDef_Le_AccOBJ_Dx
typedef uint16 Le_AccOBJ_Dx;

# define Rte_TypeDef_Le_AccOBJ_Dx_Vnce
typedef uint16 Le_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_Le_AccOBJ_Dy
typedef uint16 Le_AccOBJ_Dy;

# define Rte_TypeDef_Le_AccOBJ_Dy_Vnce
typedef uint16 Le_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_Le_AccOBJ_ExistProb
typedef uint8 Le_AccOBJ_ExistProb;

# define Rte_TypeDef_Le_AccOBJ_FusionedFC_Track_ID
typedef uint8 Le_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_Le_AccOBJ_HeadingAngle
typedef uint16 Le_AccOBJ_HeadingAngle;

# define Rte_TypeDef_Le_AccOBJ_Height
typedef uint16 Le_AccOBJ_Height;

# define Rte_TypeDef_Le_AccOBJ_Length
typedef uint16 Le_AccOBJ_Length;

# define Rte_TypeDef_Le_AccOBJ_ObstacleProb
typedef uint8 Le_AccOBJ_ObstacleProb;

# define Rte_TypeDef_Le_AccOBJ_Taillight_Info
typedef uint8 Le_AccOBJ_Taillight_Info;

# define Rte_TypeDef_Le_AccOBJ_Track_Age
typedef uint8 Le_AccOBJ_Track_Age;

# define Rte_TypeDef_Le_AccOBJ_Track_ID
typedef uint8 Le_AccOBJ_Track_ID;

# define Rte_TypeDef_Le_AccOBJ_Vx
typedef uint16 Le_AccOBJ_Vx;

# define Rte_TypeDef_Le_AccOBJ_Vx_std
typedef uint16 Le_AccOBJ_Vx_std;

# define Rte_TypeDef_Le_AccOBJ_Vy
typedef uint16 Le_AccOBJ_Vy;

# define Rte_TypeDef_Le_AccOBJ_Vy_std
typedef uint16 Le_AccOBJ_Vy_std;

# define Rte_TypeDef_Le_AccOBJ_Width
typedef uint16 Le_AccOBJ_Width;

# define Rte_TypeDef_Le_AccOBJ_confi
typedef uint8 Le_AccOBJ_confi;

# define Rte_TypeDef_Le_AccOBJ_fusion_Sts
typedef uint8 Le_AccOBJ_fusion_Sts;

# define Rte_TypeDef_LeftRoadEdgeLatDist
typedef sint16 LeftRoadEdgeLatDist;

# define Rte_TypeDef_LeftRoadEdgeLength
typedef uint16 LeftRoadEdgeLength;

# define Rte_TypeDef_LeftRoadEdgeValid
typedef boolean LeftRoadEdgeValid;

# define Rte_TypeDef_LimitAccRequest
typedef uint16 LimitAccRequest;

# define Rte_TypeDef_LimitSteerAngle
typedef uint16 LimitSteerAngle;

# define Rte_TypeDef_LimitSteerAngleRequest
typedef uint16 LimitSteerAngleRequest;

# define Rte_TypeDef_LongAccRequest
typedef uint16 LongAccRequest;

# define Rte_TypeDef_LongAccelerationFeeback
typedef sint16 LongAccelerationFeeback;

# define Rte_TypeDef_LongAccelerationFeedforward
typedef sint16 LongAccelerationFeedforward;

# define Rte_TypeDef_LongNecAcc
typedef uint16 LongNecAcc;

# define Rte_TypeDef_LongPidDistanceDComponent
typedef sint16 LongPidDistanceDComponent;

# define Rte_TypeDef_LongPidDistanceIComponent
typedef sint16 LongPidDistanceIComponent;

# define Rte_TypeDef_LongPidDistancePComponent
typedef sint16 LongPidDistancePComponent;

# define Rte_TypeDef_LongPidDistanceTotal
typedef sint16 LongPidDistanceTotal;

# define Rte_TypeDef_LongPidSpeedDComponent
typedef sint16 LongPidSpeedDComponent;

# define Rte_TypeDef_LongPidSpeedIComponent
typedef sint16 LongPidSpeedIComponent;

# define Rte_TypeDef_LongPidSpeedPComponent
typedef sint16 LongPidSpeedPComponent;

# define Rte_TypeDef_LongPidSpeedTotal
typedef sint16 LongPidSpeedTotal;

# define Rte_TypeDef_LqrIterationError
typedef uint16 LqrIterationError;

# define Rte_TypeDef_LqrIterationNums
typedef uint16 LqrIterationNums;

# define Rte_TypeDef_MaxSteerAngleRateThreshold
typedef uint8 MaxSteerAngleRateThreshold;

# define Rte_TypeDef_MaxSteerAngleThreshold
typedef uint16 MaxSteerAngleThreshold;

# define Rte_TypeDef_McuPositionType
typedef uint8 McuPositionType;

# define Rte_TypeDef_McuStatus
typedef uint8 McuStatus;

# define Rte_TypeDef_MinAccRate
typedef uint8 MinAccRate;

# define Rte_TypeDef_MotionMode
typedef uint8 MotionMode;

# define Rte_TypeDef_ObjectStopTime
typedef uint16 ObjectStopTime;

# define Rte_TypeDef_PassSeatSts
typedef uint8 PassSeatSts;

# define Rte_TypeDef_PoseAccelerationX
typedef sint16 PoseAccelerationX;

# define Rte_TypeDef_PoseAccelerationY
typedef sint16 PoseAccelerationY;

# define Rte_TypeDef_PoseDebugCanbusState
typedef uint8 PoseDebugCanbusState;

# define Rte_TypeDef_PoseDebugIsStationary
typedef boolean PoseDebugIsStationary;

# define Rte_TypeDef_PoseDebugPositionX
typedef sint32 PoseDebugPositionX;

# define Rte_TypeDef_PoseDebugPositionXStd
typedef uint32 PoseDebugPositionXStd;

# define Rte_TypeDef_PoseDebugPositionY
typedef sint32 PoseDebugPositionY;

# define Rte_TypeDef_PoseDebugPositionYStd
typedef uint32 PoseDebugPositionYStd;

# define Rte_TypeDef_PoseDebugReserved0
typedef uint8 PoseDebugReserved0;

# define Rte_TypeDef_PoseDebugReserved1
typedef sint16 PoseDebugReserved1;

# define Rte_TypeDef_PoseDebugReserved2
typedef sint16 PoseDebugReserved2;

# define Rte_TypeDef_PoseDebugReserved3
typedef sint16 PoseDebugReserved3;

# define Rte_TypeDef_PoseDebugReserved4
typedef sint32 PoseDebugReserved4;

# define Rte_TypeDef_PoseDebugReserved5
typedef sint32 PoseDebugReserved5;

# define Rte_TypeDef_PoseDebugReserved6
typedef sint32 PoseDebugReserved6;

# define Rte_TypeDef_PoseDebugReserved7
typedef sint32 PoseDebugReserved7;

# define Rte_TypeDef_PoseDebugReserved8
typedef sint32 PoseDebugReserved8;

# define Rte_TypeDef_PoseDebugSideSlipAngle
typedef sint32 PoseDebugSideSlipAngle;

# define Rte_TypeDef_PoseDebugSpeed
typedef sint32 PoseDebugSpeed;

# define Rte_TypeDef_PoseDebugSpeedStd
typedef uint32 PoseDebugSpeedStd;

# define Rte_TypeDef_PoseDebugStateType
typedef uint8 PoseDebugStateType;

# define Rte_TypeDef_PoseDebugVehicleState
typedef uint8 PoseDebugVehicleState;

# define Rte_TypeDef_PoseDebugYaw
typedef sint32 PoseDebugYaw;

# define Rte_TypeDef_PoseDebugYawStd
typedef uint32 PoseDebugYawStd;

# define Rte_TypeDef_PosePitch
typedef sint32 PosePitch;

# define Rte_TypeDef_PosePositionX
typedef sint32 PosePositionX;

# define Rte_TypeDef_PosePositionY
typedef sint32 PosePositionY;

# define Rte_TypeDef_PoseStatus
typedef uint8 PoseStatus;

# define Rte_TypeDef_PoseVelocityX
typedef sint32 PoseVelocityX;

# define Rte_TypeDef_PoseVelocityY
typedef sint32 PoseVelocityY;

# define Rte_TypeDef_PoseYaw
typedef sint32 PoseYaw;

# define Rte_TypeDef_PoseYawRate
typedef sint32 PoseYawRate;

# define Rte_TypeDef_PrimALatDataRawSafeNom_C
typedef uint16 PrimALatDataRawSafeNom_C;

# define Rte_TypeDef_PrimALgtDataRawSafeNomQf_C
typedef uint8 PrimALgtDataRawSafeNomQf_C;

# define Rte_TypeDef_PrimALgtDataRawSafeNom_C
typedef uint16 PrimALgtDataRawSafeNom_C;

# define Rte_TypeDef_PrimVehSpdGroupSafeNomQf_C
typedef uint8 PrimVehSpdGroupSafeNomQf_C;

# define Rte_TypeDef_PrimVehSpdGroupSafeNom_C
typedef uint16 PrimVehSpdGroupSafeNom_C;

# define Rte_TypeDef_PrpsnErrIndcnReq
typedef uint8 PrpsnErrIndcnReq;

# define Rte_TypeDef_PrpsnTqDirAct_C
typedef uint8 PrpsnTqDirAct_C;

# define Rte_TypeDef_PtGearAct
typedef uint8 PtGearAct;

# define Rte_TypeDef_PtTqAtAxleAvlFrntMax
typedef sint16 PtTqAtAxleAvlFrntMax;

# define Rte_TypeDef_PtTqAtAxleAvlFrntMaxLong
typedef sint16 PtTqAtAxleAvlFrntMaxLong;

# define Rte_TypeDef_PtTqAtAxleAvlReMax
typedef sint16 PtTqAtAxleAvlReMax;

# define Rte_TypeDef_PtTqAtAxleAvlReMaxLong
typedef sint16 PtTqAtAxleAvlReMaxLong;

# define Rte_TypeDef_RawAccRequest
typedef uint16 RawAccRequest;

# define Rte_TypeDef_RawSteerAngle
typedef uint16 RawSteerAngle;

# define Rte_TypeDef_RearObjectTTC
typedef uint16 RearObjectTTC;

# define Rte_TypeDef_RearObjectValid
typedef boolean RearObjectValid;

# define Rte_TypeDef_ReqResleaseFbControl
typedef uint8 ReqResleaseFbControl;

# define Rte_TypeDef_RiFr_AccOBJ_Ax
typedef uint16 RiFr_AccOBJ_Ax;

# define Rte_TypeDef_RiFr_AccOBJ_Ay
typedef uint16 RiFr_AccOBJ_Ay;

# define Rte_TypeDef_RiFr_AccOBJ_Brakelight_Info
typedef boolean RiFr_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_RiFr_AccOBJ_Class
typedef uint8 RiFr_AccOBJ_Class;

# define Rte_TypeDef_RiFr_AccOBJ_Dx
typedef uint16 RiFr_AccOBJ_Dx;

# define Rte_TypeDef_RiFr_AccOBJ_Dx_Vnce
typedef uint16 RiFr_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_RiFr_AccOBJ_Dy
typedef uint16 RiFr_AccOBJ_Dy;

# define Rte_TypeDef_RiFr_AccOBJ_Dy_Vnce
typedef uint16 RiFr_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_RiFr_AccOBJ_ExistProb
typedef uint8 RiFr_AccOBJ_ExistProb;

# define Rte_TypeDef_RiFr_AccOBJ_FusionedFC_Track_ID
typedef uint8 RiFr_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_RiFr_AccOBJ_HeadingAngle
typedef uint16 RiFr_AccOBJ_HeadingAngle;

# define Rte_TypeDef_RiFr_AccOBJ_Height
typedef uint16 RiFr_AccOBJ_Height;

# define Rte_TypeDef_RiFr_AccOBJ_Length
typedef uint16 RiFr_AccOBJ_Length;

# define Rte_TypeDef_RiFr_AccOBJ_ObstacleProb
typedef uint8 RiFr_AccOBJ_ObstacleProb;

# define Rte_TypeDef_RiFr_AccOBJ_Taillight_Info
typedef uint8 RiFr_AccOBJ_Taillight_Info;

# define Rte_TypeDef_RiFr_AccOBJ_Track_Age
typedef uint8 RiFr_AccOBJ_Track_Age;

# define Rte_TypeDef_RiFr_AccOBJ_Track_ID
typedef uint8 RiFr_AccOBJ_Track_ID;

# define Rte_TypeDef_RiFr_AccOBJ_Vx
typedef uint16 RiFr_AccOBJ_Vx;

# define Rte_TypeDef_RiFr_AccOBJ_Vx_std
typedef uint16 RiFr_AccOBJ_Vx_std;

# define Rte_TypeDef_RiFr_AccOBJ_Vy
typedef uint16 RiFr_AccOBJ_Vy;

# define Rte_TypeDef_RiFr_AccOBJ_Vy_std
typedef uint16 RiFr_AccOBJ_Vy_std;

# define Rte_TypeDef_RiFr_AccOBJ_Width
typedef uint16 RiFr_AccOBJ_Width;

# define Rte_TypeDef_RiFr_AccOBJ_confi
typedef uint8 RiFr_AccOBJ_confi;

# define Rte_TypeDef_RiFr_AccOBJ_fusion_Sts
typedef uint8 RiFr_AccOBJ_fusion_Sts;

# define Rte_TypeDef_Ri_AccOBJ_Ax
typedef uint16 Ri_AccOBJ_Ax;

# define Rte_TypeDef_Ri_AccOBJ_Ay
typedef uint16 Ri_AccOBJ_Ay;

# define Rte_TypeDef_Ri_AccOBJ_Brakelight_Info
typedef boolean Ri_AccOBJ_Brakelight_Info;

# define Rte_TypeDef_Ri_AccOBJ_Class
typedef uint8 Ri_AccOBJ_Class;

# define Rte_TypeDef_Ri_AccOBJ_Dx
typedef uint16 Ri_AccOBJ_Dx;

# define Rte_TypeDef_Ri_AccOBJ_Dx_Vnce
typedef uint16 Ri_AccOBJ_Dx_Vnce;

# define Rte_TypeDef_Ri_AccOBJ_Dy
typedef uint16 Ri_AccOBJ_Dy;

# define Rte_TypeDef_Ri_AccOBJ_Dy_Vnce
typedef uint16 Ri_AccOBJ_Dy_Vnce;

# define Rte_TypeDef_Ri_AccOBJ_ExistProb
typedef uint8 Ri_AccOBJ_ExistProb;

# define Rte_TypeDef_Ri_AccOBJ_FusionedFC_Track_ID
typedef uint8 Ri_AccOBJ_FusionedFC_Track_ID;

# define Rte_TypeDef_Ri_AccOBJ_HeadingAngle
typedef uint16 Ri_AccOBJ_HeadingAngle;

# define Rte_TypeDef_Ri_AccOBJ_Height
typedef uint16 Ri_AccOBJ_Height;

# define Rte_TypeDef_Ri_AccOBJ_Length
typedef uint16 Ri_AccOBJ_Length;

# define Rte_TypeDef_Ri_AccOBJ_ObstacleProb
typedef uint8 Ri_AccOBJ_ObstacleProb;

# define Rte_TypeDef_Ri_AccOBJ_Taillight_Info
typedef uint8 Ri_AccOBJ_Taillight_Info;

# define Rte_TypeDef_Ri_AccOBJ_Track_Age
typedef uint8 Ri_AccOBJ_Track_Age;

# define Rte_TypeDef_Ri_AccOBJ_Track_ID
typedef uint8 Ri_AccOBJ_Track_ID;

# define Rte_TypeDef_Ri_AccOBJ_Vx
typedef uint16 Ri_AccOBJ_Vx;

# define Rte_TypeDef_Ri_AccOBJ_Vx_std
typedef uint16 Ri_AccOBJ_Vx_std;

# define Rte_TypeDef_Ri_AccOBJ_Vy
typedef uint16 Ri_AccOBJ_Vy;

# define Rte_TypeDef_Ri_AccOBJ_Vy_std
typedef uint16 Ri_AccOBJ_Vy_std;

# define Rte_TypeDef_Ri_AccOBJ_Width
typedef uint16 Ri_AccOBJ_Width;

# define Rte_TypeDef_Ri_AccOBJ_confi
typedef uint8 Ri_AccOBJ_confi;

# define Rte_TypeDef_Ri_AccOBJ_fusion_Sts
typedef uint8 Ri_AccOBJ_fusion_Sts;

# define Rte_TypeDef_RightRoadEdgeLatDist
typedef sint16 RightRoadEdgeLatDist;

# define Rte_TypeDef_RightRoadEdgeLength
typedef uint16 RightRoadEdgeLength;

# define Rte_TypeDef_RightRoadEdgeValid
typedef boolean RightRoadEdgeValid;

# define Rte_TypeDef_Rte_DT_EstimationDebug_2
typedef EstimationStateType Rte_DT_EstimationDebug_2;

# define Rte_TypeDef_Rte_DT_GnssPoseInput_5
typedef McuPositionType Rte_DT_GnssPoseInput_5;

# define Rte_TypeDef_Rte_DT_GnssPoseInput_6
typedef McuPositionType Rte_DT_GnssPoseInput_6;

# define Rte_TypeDef_Rte_DT_Intention_0
typedef MotionMode Rte_DT_Intention_0;

# define Rte_TypeDef_Rte_DT_Intention_1
typedef LaneChangeMode Rte_DT_Intention_1;

# define Rte_TypeDef_Rte_DT_SG_ALnchTiDly3_0
typedef uint8 Rte_DT_SG_ALnchTiDly3_0;

# define Rte_TypeDef_Rte_DT_SG_AccrPedlPsd_0
typedef boolean Rte_DT_SG_AccrPedlPsd_0;

# define Rte_TypeDef_Rte_DT_SG_AccrPedlPsd_3
typedef boolean Rte_DT_SG_AccrPedlPsd_3;

# define Rte_TypeDef_Rte_DT_SG_AdDirReq_2
typedef uint8 Rte_DT_SG_AdDirReq_2;

# define Rte_TypeDef_Rte_DT_SG_AdFusedFricEstimn_1
typedef uint8 Rte_DT_SG_AdFusedFricEstimn_1;

# define Rte_TypeDef_Rte_DT_SG_AdPrimSafeStopActvGroupSafe_2
typedef boolean Rte_DT_SG_AdPrimSafeStopActvGroupSafe_2;

# define Rte_TypeDef_Rte_DT_SG_AdPrimSteerStsSafeGroup_0
typedef uint8 Rte_DT_SG_AdPrimSteerStsSafeGroup_0;

# define Rte_TypeDef_Rte_DT_SG_AdPrimSteerStsSafeGroup_1
typedef uint8 Rte_DT_SG_AdPrimSteerStsSafeGroup_1;

# define Rte_TypeDef_Rte_DT_SG_AdPrimWhlAgEstimdGroupSafe_2
typedef uint8 Rte_DT_SG_AdPrimWhlAgEstimdGroupSafe_2;

# define Rte_TypeDef_Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_0
typedef boolean Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_3
typedef boolean Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_3;

# define Rte_TypeDef_Rte_DT_SG_AdSecBrkActvnGroupSafe_0
typedef boolean Rte_DT_SG_AdSecBrkActvnGroupSafe_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecBrkActvnGroupSafe_3
typedef boolean Rte_DT_SG_AdSecBrkActvnGroupSafe_3;

# define Rte_TypeDef_Rte_DT_SG_AdSecPahStsGroupSafe_2
typedef boolean Rte_DT_SG_AdSecPahStsGroupSafe_2;

# define Rte_TypeDef_Rte_DT_SG_AdSecPahStsGroupSafe_3
typedef boolean Rte_DT_SG_AdSecPahStsGroupSafe_3;

# define Rte_TypeDef_Rte_DT_SG_AdSecPahStsGroupSafe_4
typedef boolean Rte_DT_SG_AdSecPahStsGroupSafe_4;

# define Rte_TypeDef_Rte_DT_SG_AdSecSafeStopActvGroupSafe_2
typedef boolean Rte_DT_SG_AdSecSafeStopActvGroupSafe_2;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerActvnGroupSafe_0
typedef boolean Rte_DT_SG_AdSecSteerActvnGroupSafe_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerActvnGroupSafe_1
typedef boolean Rte_DT_SG_AdSecSteerActvnGroupSafe_1;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_0
typedef uint8 Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_3
typedef boolean Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_3;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_4
typedef uint8 Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_4;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_0
typedef uint8 Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_1
typedef uint8 Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_1;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerStsSafeGroup_0
typedef uint8 Rte_DT_SG_AdSecSteerStsSafeGroup_0;

# define Rte_TypeDef_Rte_DT_SG_AdSecSteerStsSafeGroup_1
typedef uint8 Rte_DT_SG_AdSecSteerStsSafeGroup_1;

# define Rte_TypeDef_Rte_DT_SG_AdStandStillReq_2
typedef uint8 Rte_DT_SG_AdStandStillReq_2;

# define Rte_TypeDef_Rte_DT_SG_AdSteerPaddlPsdGroupSafe_2
typedef boolean Rte_DT_SG_AdSteerPaddlPsdGroupSafe_2;

# define Rte_TypeDef_Rte_DT_SG_AdSteerPaddlPsdGroupSafe_3
typedef boolean Rte_DT_SG_AdSteerPaddlPsdGroupSafe_3;

# define Rte_TypeDef_Rte_DT_SG_AdSteerPaddlPsdGroupSafe_4
typedef boolean Rte_DT_SG_AdSteerPaddlPsdGroupSafe_4;

# define Rte_TypeDef_Rte_DT_SG_AdSteerPaddlPsdGroupSafe_5
typedef boolean Rte_DT_SG_AdSteerPaddlPsdGroupSafe_5;

# define Rte_TypeDef_Rte_DT_SG_AdWhlLockReq_2
typedef uint8 Rte_DT_SG_AdWhlLockReq_2;

# define Rte_TypeDef_Rte_DT_SG_AdpLiReqFromAPI_0
typedef boolean Rte_DT_SG_AdpLiReqFromAPI_0;

# define Rte_TypeDef_Rte_DT_SG_AdpLiReqFromAPI_3
typedef boolean Rte_DT_SG_AdpLiReqFromAPI_3;

# define Rte_TypeDef_Rte_DT_SG_AdpLiReqFromAPI_4
typedef boolean Rte_DT_SG_AdpLiReqFromAPI_4;

# define Rte_TypeDef_Rte_DT_SG_AdpLiReqFromAPI_5
typedef boolean Rte_DT_SG_AdpLiReqFromAPI_5;

# define Rte_TypeDef_Rte_DT_SG_AdpLiReqFromAPI_6
typedef boolean Rte_DT_SG_AdpLiReqFromAPI_6;

# define Rte_TypeDef_Rte_DT_SG_AgDataRawSafe_3
typedef uint8 Rte_DT_SG_AgDataRawSafe_3;

# define Rte_TypeDef_Rte_DT_SG_AgDataRawSafe_5
typedef uint8 Rte_DT_SG_AgDataRawSafe_5;

# define Rte_TypeDef_Rte_DT_SG_AlrmSts1_0
typedef uint8 Rte_DT_SG_AlrmSts1_0;

# define Rte_TypeDef_Rte_DT_SG_AlrmSts1_1
typedef uint8 Rte_DT_SG_AlrmSts1_1;

# define Rte_TypeDef_Rte_DT_SG_AlrmSts1_2
typedef boolean Rte_DT_SG_AlrmSts1_2;

# define Rte_TypeDef_Rte_DT_SG_AlrmSts1_3
typedef boolean Rte_DT_SG_AlrmSts1_3;

# define Rte_TypeDef_Rte_DT_SG_AlrmSts1_4
typedef boolean Rte_DT_SG_AlrmSts1_4;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvModMngtExtSafe_0
typedef uint8 Rte_DT_SG_AutnmsDrvModMngtExtSafe_0;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvModMngtExtSafe_3
typedef uint8 Rte_DT_SG_AutnmsDrvModMngtExtSafe_3;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_0
typedef uint8 Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_0;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_1
typedef uint8 Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_1;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_4
typedef uint8 Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_4;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvStReq_0
typedef uint8 Rte_DT_SG_AutnmsDrvStReq_0;

# define Rte_TypeDef_Rte_DT_SG_AutnmsDrvStReq_A_0
typedef uint8 Rte_DT_SG_AutnmsDrvStReq_A_0;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtDrvr_0
typedef boolean Rte_DT_SG_BltLockStAtDrvr_0;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtDrvr_1
typedef boolean Rte_DT_SG_BltLockStAtDrvr_1;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtPass_0
typedef boolean Rte_DT_SG_BltLockStAtPass_0;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtPass_1
typedef boolean Rte_DT_SG_BltLockStAtPass_1;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_0
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_0;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_1
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_1;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_2
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_2;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_0
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_0;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_1
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_1;

# define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_2
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_2;

# define Rte_TypeDef_Rte_DT_SG_BrkPedlPsdSafeGroup_0
typedef boolean Rte_DT_SG_BrkPedlPsdSafeGroup_0;

# define Rte_TypeDef_Rte_DT_SG_BrkPedlPsdSafeGroup_1
typedef boolean Rte_DT_SG_BrkPedlPsdSafeGroup_1;

# define Rte_TypeDef_Rte_DT_SG_BrkPedlPsdSafeGroup_4
typedef uint8 Rte_DT_SG_BrkPedlPsdSafeGroup_4;

# define Rte_TypeDef_Rte_DT_SG_BrkPedlVal_1
typedef uint8 Rte_DT_SG_BrkPedlVal_1;

# define Rte_TypeDef_Rte_DT_SG_BrkTqMinReq_0
typedef uint8 Rte_DT_SG_BrkTqMinReq_0;

# define Rte_TypeDef_Rte_DT_SG_CarModInCrashStsSafe_2
typedef boolean Rte_DT_SG_CarModInCrashStsSafe_2;

# define Rte_TypeDef_Rte_DT_SG_CllsnAidSnvtySeld_0
typedef uint8 Rte_DT_SG_CllsnAidSnvtySeld_0;

# define Rte_TypeDef_Rte_DT_SG_CllsnAidSnvtySeld_1
typedef uint8 Rte_DT_SG_CllsnAidSnvtySeld_1;

# define Rte_TypeDef_Rte_DT_SG_ClstrSts1ForAutnmsDrv_2
typedef uint8 Rte_DT_SG_ClstrSts1ForAutnmsDrv_2;

# define Rte_TypeDef_Rte_DT_SG_ClstrSts2ForAutnmsDrv_2
typedef uint8 Rte_DT_SG_ClstrSts2ForAutnmsDrv_2;

# define Rte_TypeDef_Rte_DT_SG_DoorPassRePosnStsToAPI_0
typedef uint8 Rte_DT_SG_DoorPassRePosnStsToAPI_0;

# define Rte_TypeDef_Rte_DT_SG_DrvrGearShiftManReq_0
typedef boolean Rte_DT_SG_DrvrGearShiftManReq_0;

# define Rte_TypeDef_Rte_DT_SG_DrvrGearShiftManReq_1
typedef boolean Rte_DT_SG_DrvrGearShiftManReq_1;

# define Rte_TypeDef_Rte_DT_SG_DrvrGearShiftManReq_2
typedef boolean Rte_DT_SG_DrvrGearShiftManReq_2;

# define Rte_TypeDef_Rte_DT_SG_DrvrGearShiftManReq_3
typedef boolean Rte_DT_SG_DrvrGearShiftManReq_3;

# define Rte_TypeDef_Rte_DT_SG_DrvrGearShiftManReq_4
typedef boolean Rte_DT_SG_DrvrGearShiftManReq_4;

# define Rte_TypeDef_Rte_DT_SG_DrvrIntvSts_0
typedef boolean Rte_DT_SG_DrvrIntvSts_0;

# define Rte_TypeDef_Rte_DT_SG_DrvrIntvSts_1
typedef boolean Rte_DT_SG_DrvrIntvSts_1;

# define Rte_TypeDef_Rte_DT_SG_DrvrIntvSts_2
typedef boolean Rte_DT_SG_DrvrIntvSts_2;

# define Rte_TypeDef_Rte_DT_SG_DrvrPrsntGroup_0
typedef uint8 Rte_DT_SG_DrvrPrsntGroup_0;

# define Rte_TypeDef_Rte_DT_SG_DrvrPrsntGroup_1
typedef uint8 Rte_DT_SG_DrvrPrsntGroup_1;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_0
typedef boolean Rte_DT_SG_EngFltIndcn_0;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_1
typedef boolean Rte_DT_SG_EngFltIndcn_1;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_10
typedef boolean Rte_DT_SG_EngFltIndcn_10;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_11
typedef boolean Rte_DT_SG_EngFltIndcn_11;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_12
typedef boolean Rte_DT_SG_EngFltIndcn_12;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_13
typedef boolean Rte_DT_SG_EngFltIndcn_13;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_14
typedef boolean Rte_DT_SG_EngFltIndcn_14;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_15
typedef boolean Rte_DT_SG_EngFltIndcn_15;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_16
typedef boolean Rte_DT_SG_EngFltIndcn_16;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_17
typedef boolean Rte_DT_SG_EngFltIndcn_17;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_18
typedef boolean Rte_DT_SG_EngFltIndcn_18;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_19
typedef boolean Rte_DT_SG_EngFltIndcn_19;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_2
typedef boolean Rte_DT_SG_EngFltIndcn_2;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_20
typedef boolean Rte_DT_SG_EngFltIndcn_20;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_21
typedef boolean Rte_DT_SG_EngFltIndcn_21;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_22
typedef boolean Rte_DT_SG_EngFltIndcn_22;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_23
typedef boolean Rte_DT_SG_EngFltIndcn_23;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_24
typedef boolean Rte_DT_SG_EngFltIndcn_24;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_25
typedef boolean Rte_DT_SG_EngFltIndcn_25;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_26
typedef boolean Rte_DT_SG_EngFltIndcn_26;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_3
typedef boolean Rte_DT_SG_EngFltIndcn_3;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_4
typedef boolean Rte_DT_SG_EngFltIndcn_4;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_5
typedef boolean Rte_DT_SG_EngFltIndcn_5;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_6
typedef boolean Rte_DT_SG_EngFltIndcn_6;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_7
typedef boolean Rte_DT_SG_EngFltIndcn_7;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_8
typedef boolean Rte_DT_SG_EngFltIndcn_8;

# define Rte_TypeDef_Rte_DT_SG_EngFltIndcn_9
typedef boolean Rte_DT_SG_EngFltIndcn_9;

# define Rte_TypeDef_Rte_DT_SG_FricEstimnFromVehDynGroup_1
typedef uint8 Rte_DT_SG_FricEstimnFromVehDynGroup_1;

# define Rte_TypeDef_Rte_DT_SG_HmiAutnmsSts_2
typedef uint8 Rte_DT_SG_HmiAutnmsSts_2;

# define Rte_TypeDef_Rte_DT_SG_OvrdDecelByDrvr_0
typedef boolean Rte_DT_SG_OvrdDecelByDrvr_0;

# define Rte_TypeDef_Rte_DT_SG_PrimALatDataRawSafe_4
typedef uint8 Rte_DT_SG_PrimALatDataRawSafe_4;

# define Rte_TypeDef_Rte_DT_SG_PrimALatDataRawSafe_6
typedef uint8 Rte_DT_SG_PrimALatDataRawSafe_6;

# define Rte_TypeDef_Rte_DT_SG_PrimALgtDataRawSafe_4
typedef uint8 Rte_DT_SG_PrimALgtDataRawSafe_4;

# define Rte_TypeDef_Rte_DT_SG_PrimALgtDataRawSafe_6
typedef uint8 Rte_DT_SG_PrimALgtDataRawSafe_6;

# define Rte_TypeDef_Rte_DT_SG_PrimAxleSlipStsAndRelAg_0
typedef uint8 Rte_DT_SG_PrimAxleSlipStsAndRelAg_0;

# define Rte_TypeDef_Rte_DT_SG_PrimAxleSlipStsAndRelAg_1
typedef uint8 Rte_DT_SG_PrimAxleSlipStsAndRelAg_1;

# define Rte_TypeDef_Rte_DT_SG_PrimAxleSlipStsAndRelAg_2
typedef uint8 Rte_DT_SG_PrimAxleSlipStsAndRelAg_2;

# define Rte_TypeDef_Rte_DT_SG_PrimAxleSlipStsAndRelAg_4
typedef uint8 Rte_DT_SG_PrimAxleSlipStsAndRelAg_4;

# define Rte_TypeDef_Rte_DT_SG_PrimAxleSlipStsAndRelAg_6
typedef uint8 Rte_DT_SG_PrimAxleSlipStsAndRelAg_6;

# define Rte_TypeDef_Rte_DT_SG_PrimVLatSafe_4
typedef uint8 Rte_DT_SG_PrimVLatSafe_4;

# define Rte_TypeDef_Rte_DT_SG_PrimVLatSafe_6
typedef uint8 Rte_DT_SG_PrimVLatSafe_6;

# define Rte_TypeDef_Rte_DT_SG_PrimVehMSafe_2
typedef uint8 Rte_DT_SG_PrimVehMSafe_2;

# define Rte_TypeDef_Rte_DT_SG_PrimVehMSafe_3
typedef uint8 Rte_DT_SG_PrimVehMSafe_3;

# define Rte_TypeDef_Rte_DT_SG_PrimVehSpdGroupSafe_4
typedef uint8 Rte_DT_SG_PrimVehSpdGroupSafe_4;

# define Rte_TypeDef_Rte_DT_SG_PrimVehSpdGroupSafe_5
typedef uint8 Rte_DT_SG_PrimVehSpdGroupSafe_5;

# define Rte_TypeDef_Rte_DT_SG_PrimVehSpdGroupSafe_6
typedef uint8 Rte_DT_SG_PrimVehSpdGroupSafe_6;

# define Rte_TypeDef_Rte_DT_SG_PrimVehSpdGroupSafe_8
typedef uint8 Rte_DT_SG_PrimVehSpdGroupSafe_8;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlAgSpdFrntSafe_3
typedef uint8 Rte_DT_SG_PrimWhlAgSpdFrntSafe_3;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlAgSpdFrntSafe_5
typedef uint8 Rte_DT_SG_PrimWhlAgSpdFrntSafe_5;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlAgSpdReSafe_3
typedef uint8 Rte_DT_SG_PrimWhlAgSpdReSafe_3;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlAgSpdReSafe_5
typedef uint8 Rte_DT_SG_PrimWhlAgSpdReSafe_5;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotDirReSafe1_2
typedef uint8 Rte_DT_SG_PrimWhlRotDirReSafe1_2;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotDirReSafe1_3
typedef uint8 Rte_DT_SG_PrimWhlRotDirReSafe1_3;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotDirReSafe1_4
typedef uint8 Rte_DT_SG_PrimWhlRotDirReSafe1_4;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotDirReSafe1_5
typedef uint8 Rte_DT_SG_PrimWhlRotDirReSafe1_5;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotToothCntr_3
typedef uint8 Rte_DT_SG_PrimWhlRotToothCntr_3;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotToothCntr_5
typedef uint8 Rte_DT_SG_PrimWhlRotToothCntr_5;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotToothCntr_7
typedef uint8 Rte_DT_SG_PrimWhlRotToothCntr_7;

# define Rte_TypeDef_Rte_DT_SG_PrimWhlRotToothCntr_9
typedef uint8 Rte_DT_SG_PrimWhlRotToothCntr_9;

# define Rte_TypeDef_Rte_DT_SG_PrimYawRateSafe_4
typedef uint8 Rte_DT_SG_PrimYawRateSafe_4;

# define Rte_TypeDef_Rte_DT_SG_PrimYawRateSafe_6
typedef uint8 Rte_DT_SG_PrimYawRateSafe_6;

# define Rte_TypeDef_Rte_DT_SG_PrpsnTqDirCpby_2
typedef uint8 Rte_DT_SG_PrpsnTqDirCpby_2;

# define Rte_TypeDef_Rte_DT_SG_PrpsnTqDir_0
typedef uint8 Rte_DT_SG_PrpsnTqDir_0;

# define Rte_TypeDef_Rte_DT_SG_RoadLoadNom_3
typedef boolean Rte_DT_SG_RoadLoadNom_3;

# define Rte_TypeDef_Rte_DT_SG_SSMBDegraded_2
typedef uint8 Rte_DT_SG_SSMBDegraded_2;

# define Rte_TypeDef_Rte_DT_SG_SSMDegraded_2
typedef uint8 Rte_DT_SG_SSMDegraded_2;

# define Rte_TypeDef_Rte_DT_SG_SecAdWhlLockReq_2
typedef uint8 Rte_DT_SG_SecAdWhlLockReq_2;

# define Rte_TypeDef_Rte_DT_SG_SecPoseMonSafe_4
typedef uint8 Rte_DT_SG_SecPoseMonSafe_4;

# define Rte_TypeDef_Rte_DT_SG_SecSteerMotTq_1
typedef uint8 Rte_DT_SG_SecSteerMotTq_1;

# define Rte_TypeDef_Rte_DT_SG_SecWhlLockSts_2
typedef uint8 Rte_DT_SG_SecWhlLockSts_2;

# define Rte_TypeDef_Rte_DT_SG_SecWhlLockSts_3
typedef uint8 Rte_DT_SG_SecWhlLockSts_3;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_0
typedef boolean Rte_DT_SG_SnsrClngErrIf_0;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_1
typedef boolean Rte_DT_SG_SnsrClngErrIf_1;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_2
typedef boolean Rte_DT_SG_SnsrClngErrIf_2;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_3
typedef boolean Rte_DT_SG_SnsrClngErrIf_3;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_4
typedef boolean Rte_DT_SG_SnsrClngErrIf_4;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_5
typedef boolean Rte_DT_SG_SnsrClngErrIf_5;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_6
typedef boolean Rte_DT_SG_SnsrClngErrIf_6;

# define Rte_TypeDef_Rte_DT_SG_SnsrClngErrIf_7
typedef boolean Rte_DT_SG_SnsrClngErrIf_7;

# define Rte_TypeDef_Rte_DT_SG_StandStillMgrStsForHldSafe_2
typedef uint8 Rte_DT_SG_StandStillMgrStsForHldSafe_2;

# define Rte_TypeDef_Rte_DT_SG_SteerWhlSnsr_2
typedef uint8 Rte_DT_SG_SteerWhlSnsr_2;

# define Rte_TypeDef_Rte_DT_SG_SteerWhlTqGroup_1
typedef uint8 Rte_DT_SG_SteerWhlTqGroup_1;

# define Rte_TypeDef_Rte_DT_SG_SwtExtrLiFromAPI_2
typedef uint8 Rte_DT_SG_SwtExtrLiFromAPI_2;

# define Rte_TypeDef_Rte_DT_SG_SwtExtrLiFromAPI_3
typedef uint8 Rte_DT_SG_SwtExtrLiFromAPI_3;

# define Rte_TypeDef_Rte_DT_SG_SwtExtrLiToAPI_2
typedef uint8 Rte_DT_SG_SwtExtrLiToAPI_2;

# define Rte_TypeDef_Rte_DT_SG_SwtExtrLiToAPI_3
typedef uint8 Rte_DT_SG_SwtExtrLiToAPI_3;

# define Rte_TypeDef_Rte_DT_SG_SwtIndcrToAPI_0
typedef uint8 Rte_DT_SG_SwtIndcrToAPI_0;

# define Rte_TypeDef_Rte_DT_SG_SwtIndcrToAPI_3
typedef uint8 Rte_DT_SG_SwtIndcrToAPI_3;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnFrntRi_0
typedef uint8 Rte_DT_SG_TirePWarnFrntRi_0;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnFrntRi_1
typedef uint8 Rte_DT_SG_TirePWarnFrntRi_1;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnReLe_0
typedef uint8 Rte_DT_SG_TirePWarnReLe_0;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnReLe_1
typedef uint8 Rte_DT_SG_TirePWarnReLe_1;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnReRi_0
typedef uint8 Rte_DT_SG_TirePWarnReRi_0;

# define Rte_TypeDef_Rte_DT_SG_TirePWarnReRi_1
typedef uint8 Rte_DT_SG_TirePWarnReRi_1;

# define Rte_TypeDef_Rte_DT_SG_VehMGroup_1
typedef uint8 Rte_DT_SG_VehMGroup_1;

# define Rte_TypeDef_Rte_DT_SG_VehMGroup_2
typedef uint8 Rte_DT_SG_VehMGroup_2;

# define Rte_TypeDef_Rte_DT_SG_VehOperStReq_2
typedef uint8 Rte_DT_SG_VehOperStReq_2;

# define Rte_TypeDef_Rte_DT_SG_WhlLockSts_2
typedef uint8 Rte_DT_SG_WhlLockSts_2;

# define Rte_TypeDef_Rte_DT_SG_WhlLockSts_3
typedef uint8 Rte_DT_SG_WhlLockSts_3;

# define Rte_TypeDef_SAS_CalibrationSts
typedef boolean SAS_CalibrationSts;

# define Rte_TypeDef_SAS_FailureSts
typedef boolean SAS_FailureSts;

# define Rte_TypeDef_SAS_Status_AliveCounter
typedef uint8 SAS_Status_AliveCounter;

# define Rte_TypeDef_SAS_Status_Checksum
typedef uint8 SAS_Status_Checksum;

# define Rte_TypeDef_SAS_SteerWheelAngle
typedef sint16 SAS_SteerWheelAngle;

# define Rte_TypeDef_SAS_SteerWheelRotSpd
typedef uint8 SAS_SteerWheelRotSpd;

# define Rte_TypeDef_SafeDistance
typedef uint8 SafeDistance;

# define Rte_TypeDef_Sensor1v1rStatus
typedef uint8 Sensor1v1rStatus;

# define Rte_TypeDef_SrvRqrdForCllsnAid
typedef boolean SrvRqrdForCllsnAid;

# define Rte_TypeDef_StbM_CustomerIdType
typedef uint16 StbM_CustomerIdType;

# define Rte_TypeDef_StbM_MasterConfigType
typedef uint8 StbM_MasterConfigType;

# define Rte_TypeDef_StbM_RateDeviationType
typedef sint16 StbM_RateDeviationType;

# define Rte_TypeDef_StbM_SynchronizedTimeBaseType
typedef uint16 StbM_SynchronizedTimeBaseType;

# define Rte_TypeDef_StbM_TimeBaseNotificationType
typedef uint32 StbM_TimeBaseNotificationType;

# define Rte_TypeDef_StbM_TimeBaseStatusType
typedef uint8 StbM_TimeBaseStatusType;

# define Rte_TypeDef_StbM_TimeDiffType
typedef sint32 StbM_TimeDiffType;

# define Rte_TypeDef_SteerAngle
typedef uint16 SteerAngle;

# define Rte_TypeDef_SteerAngleByLQR
typedef uint16 SteerAngleByLQR;

# define Rte_TypeDef_SteerAngleForSystemError
typedef uint16 SteerAngleForSystemError;

# define Rte_TypeDef_StopLineLeftEdgeX
typedef uint16 StopLineLeftEdgeX;

# define Rte_TypeDef_StopLineLeftEdgeY
typedef sint16 StopLineLeftEdgeY;

# define Rte_TypeDef_StopLineRightEdgeX
typedef uint16 StopLineRightEdgeX;

# define Rte_TypeDef_StopLineRightEdgeY
typedef sint16 StopLineRightEdgeY;

# define Rte_TypeDef_StopLineValid
typedef boolean StopLineValid;

# define Rte_TypeDef_SwtBeamHiToAPI
typedef uint8 SwtBeamHiToAPI;

# define Rte_TypeDef_SwtLiHzrdWarnToAPI
typedef boolean SwtLiHzrdWarnToAPI;

# define Rte_TypeDef_SystemState
typedef uint8 SystemState;

# define Rte_TypeDef_SystemStatusReserved1
typedef uint16 SystemStatusReserved1;

# define Rte_TypeDef_SystemStatusReserved2
typedef uint16 SystemStatusReserved2;

# define Rte_TypeDef_SystemStatusReserved3
typedef uint16 SystemStatusReserved3;

# define Rte_TypeDef_SystemStatusReserved4
typedef uint8 SystemStatusReserved4;

# define Rte_TypeDef_SystemStatusRollingCounter
typedef uint8 SystemStatusRollingCounter;

# define Rte_TypeDef_TankFlapSts
typedef uint8 TankFlapSts;

# define Rte_TypeDef_TimeInMicrosecondsType
typedef uint32 TimeInMicrosecondsType;

# define Rte_TypeDef_TimeToCollison
typedef uint16 TimeToCollison;

# define Rte_TypeDef_TrSts
typedef uint8 TrSts;

# define Rte_TypeDef_TrajectoryCurvature
typedef uint16 TrajectoryCurvature;

# define Rte_TypeDef_TrajectoryCurvatureChange
typedef uint32 TrajectoryCurvatureChange;

# define Rte_TypeDef_TrajectoryHeadingAngle
typedef uint16 TrajectoryHeadingAngle;

# define Rte_TypeDef_TrajectoryInfoHarzardLight
typedef boolean TrajectoryInfoHarzardLight;

# define Rte_TypeDef_TrajectoryInfoHorn
typedef boolean TrajectoryInfoHorn;

# define Rte_TypeDef_TrajectoryInfoLaneChange
typedef uint8 TrajectoryInfoLaneChange;

# define Rte_TypeDef_TrajectoryInfoMotion
typedef uint8 TrajectoryInfoMotion;

# define Rte_TypeDef_TrajectoryInfoPlanningStatus
typedef uint8 TrajectoryInfoPlanningStatus;

# define Rte_TypeDef_TrajectoryInfoReserve0
typedef sint16 TrajectoryInfoReserve0;

# define Rte_TypeDef_TrajectoryInfoReserve1
typedef sint16 TrajectoryInfoReserve1;

# define Rte_TypeDef_TrajectoryInfoReserve2
typedef sint16 TrajectoryInfoReserve2;

# define Rte_TypeDef_TrajectoryInfoReserve3
typedef sint32 TrajectoryInfoReserve3;

# define Rte_TypeDef_TrajectoryInfoReserve4
typedef sint32 TrajectoryInfoReserve4;

# define Rte_TypeDef_TrajectoryInfoReserve5
typedef sint32 TrajectoryInfoReserve5;

# define Rte_TypeDef_TrajectoryInfoReserve6
typedef sint32 TrajectoryInfoReserve6;

# define Rte_TypeDef_TrajectoryInfoRollingCounter
typedef uint8 TrajectoryInfoRollingCounter;

# define Rte_TypeDef_TrajectoryLength
typedef uint16 TrajectoryLength;

# define Rte_TypeDef_TrajectoryPosY0
typedef uint16 TrajectoryPosY0;

# define Rte_TypeDef_TrajectoryPose00Acceleration
typedef sint16 TrajectoryPose00Acceleration;

# define Rte_TypeDef_TrajectoryPose00Curvature
typedef uint16 TrajectoryPose00Curvature;

# define Rte_TypeDef_TrajectoryPose00Heading
typedef sint16 TrajectoryPose00Heading;

# define Rte_TypeDef_TrajectoryPose00PositionX
typedef sint32 TrajectoryPose00PositionX;

# define Rte_TypeDef_TrajectoryPose00PositionY
typedef sint32 TrajectoryPose00PositionY;

# define Rte_TypeDef_TrajectoryPose00Speed
typedef uint16 TrajectoryPose00Speed;

# define Rte_TypeDef_TrajectoryPose00Steering
typedef sint16 TrajectoryPose00Steering;

# define Rte_TypeDef_TrajectoryPose01Acceleration
typedef sint16 TrajectoryPose01Acceleration;

# define Rte_TypeDef_TrajectoryPose01Curvature
typedef uint16 TrajectoryPose01Curvature;

# define Rte_TypeDef_TrajectoryPose01Heading
typedef sint16 TrajectoryPose01Heading;

# define Rte_TypeDef_TrajectoryPose01PositionX
typedef sint32 TrajectoryPose01PositionX;

# define Rte_TypeDef_TrajectoryPose01PositionY
typedef sint32 TrajectoryPose01PositionY;

# define Rte_TypeDef_TrajectoryPose01Speed
typedef uint16 TrajectoryPose01Speed;

# define Rte_TypeDef_TrajectoryPose01Steering
typedef sint16 TrajectoryPose01Steering;

# define Rte_TypeDef_TrajectoryPose02Acceleration
typedef sint16 TrajectoryPose02Acceleration;

# define Rte_TypeDef_TrajectoryPose02Curvature
typedef uint16 TrajectoryPose02Curvature;

# define Rte_TypeDef_TrajectoryPose02Heading
typedef sint16 TrajectoryPose02Heading;

# define Rte_TypeDef_TrajectoryPose02PositionX
typedef sint32 TrajectoryPose02PositionX;

# define Rte_TypeDef_TrajectoryPose02PositionY
typedef sint32 TrajectoryPose02PositionY;

# define Rte_TypeDef_TrajectoryPose02Speed
typedef uint16 TrajectoryPose02Speed;

# define Rte_TypeDef_TrajectoryPose02Steering
typedef sint16 TrajectoryPose02Steering;

# define Rte_TypeDef_TrajectoryPose03Acceleration
typedef sint16 TrajectoryPose03Acceleration;

# define Rte_TypeDef_TrajectoryPose03Curvature
typedef uint16 TrajectoryPose03Curvature;

# define Rte_TypeDef_TrajectoryPose03Heading
typedef sint16 TrajectoryPose03Heading;

# define Rte_TypeDef_TrajectoryPose03PositionX
typedef sint32 TrajectoryPose03PositionX;

# define Rte_TypeDef_TrajectoryPose03PositionY
typedef sint32 TrajectoryPose03PositionY;

# define Rte_TypeDef_TrajectoryPose03Speed
typedef uint16 TrajectoryPose03Speed;

# define Rte_TypeDef_TrajectoryPose03Steering
typedef sint16 TrajectoryPose03Steering;

# define Rte_TypeDef_TrajectoryPose04Acceleration
typedef sint16 TrajectoryPose04Acceleration;

# define Rte_TypeDef_TrajectoryPose04Curvature
typedef uint16 TrajectoryPose04Curvature;

# define Rte_TypeDef_TrajectoryPose04Heading
typedef sint16 TrajectoryPose04Heading;

# define Rte_TypeDef_TrajectoryPose04PositionX
typedef sint32 TrajectoryPose04PositionX;

# define Rte_TypeDef_TrajectoryPose04PositionY
typedef sint32 TrajectoryPose04PositionY;

# define Rte_TypeDef_TrajectoryPose04Speed
typedef uint16 TrajectoryPose04Speed;

# define Rte_TypeDef_TrajectoryPose04Steering
typedef sint16 TrajectoryPose04Steering;

# define Rte_TypeDef_TrajectoryPose05Acceleration
typedef sint16 TrajectoryPose05Acceleration;

# define Rte_TypeDef_TrajectoryPose05Curvature
typedef uint16 TrajectoryPose05Curvature;

# define Rte_TypeDef_TrajectoryPose05Heading
typedef sint16 TrajectoryPose05Heading;

# define Rte_TypeDef_TrajectoryPose05PositionX
typedef sint32 TrajectoryPose05PositionX;

# define Rte_TypeDef_TrajectoryPose05PositionY
typedef sint32 TrajectoryPose05PositionY;

# define Rte_TypeDef_TrajectoryPose05Speed
typedef uint16 TrajectoryPose05Speed;

# define Rte_TypeDef_TrajectoryPose05Steering
typedef sint16 TrajectoryPose05Steering;

# define Rte_TypeDef_TrajectoryPose06Acceleration
typedef sint16 TrajectoryPose06Acceleration;

# define Rte_TypeDef_TrajectoryPose06Curvature
typedef uint16 TrajectoryPose06Curvature;

# define Rte_TypeDef_TrajectoryPose06Heading
typedef sint16 TrajectoryPose06Heading;

# define Rte_TypeDef_TrajectoryPose06PositionX
typedef sint32 TrajectoryPose06PositionX;

# define Rte_TypeDef_TrajectoryPose06PositionY
typedef sint32 TrajectoryPose06PositionY;

# define Rte_TypeDef_TrajectoryPose06Speed
typedef uint16 TrajectoryPose06Speed;

# define Rte_TypeDef_TrajectoryPose06Steering
typedef sint16 TrajectoryPose06Steering;

# define Rte_TypeDef_TrajectoryPose07Acceleration
typedef sint16 TrajectoryPose07Acceleration;

# define Rte_TypeDef_TrajectoryPose07Curvature
typedef uint16 TrajectoryPose07Curvature;

# define Rte_TypeDef_TrajectoryPose07Heading
typedef sint16 TrajectoryPose07Heading;

# define Rte_TypeDef_TrajectoryPose07PositionX
typedef sint32 TrajectoryPose07PositionX;

# define Rte_TypeDef_TrajectoryPose07PositionY
typedef sint32 TrajectoryPose07PositionY;

# define Rte_TypeDef_TrajectoryPose07Speed
typedef uint16 TrajectoryPose07Speed;

# define Rte_TypeDef_TrajectoryPose07Steering
typedef sint16 TrajectoryPose07Steering;

# define Rte_TypeDef_TrajectoryPose08Acceleration
typedef sint16 TrajectoryPose08Acceleration;

# define Rte_TypeDef_TrajectoryPose08Curvature
typedef uint16 TrajectoryPose08Curvature;

# define Rte_TypeDef_TrajectoryPose08Heading
typedef sint16 TrajectoryPose08Heading;

# define Rte_TypeDef_TrajectoryPose08PositionX
typedef sint32 TrajectoryPose08PositionX;

# define Rte_TypeDef_TrajectoryPose08PositionY
typedef sint32 TrajectoryPose08PositionY;

# define Rte_TypeDef_TrajectoryPose08Speed
typedef uint16 TrajectoryPose08Speed;

# define Rte_TypeDef_TrajectoryPose08Steering
typedef sint16 TrajectoryPose08Steering;

# define Rte_TypeDef_TrajectoryPose09Acceleration
typedef sint16 TrajectoryPose09Acceleration;

# define Rte_TypeDef_TrajectoryPose09Curvature
typedef uint16 TrajectoryPose09Curvature;

# define Rte_TypeDef_TrajectoryPose09Heading
typedef sint16 TrajectoryPose09Heading;

# define Rte_TypeDef_TrajectoryPose09PositionX
typedef sint32 TrajectoryPose09PositionX;

# define Rte_TypeDef_TrajectoryPose09PositionY
typedef sint32 TrajectoryPose09PositionY;

# define Rte_TypeDef_TrajectoryPose09Speed
typedef uint16 TrajectoryPose09Speed;

# define Rte_TypeDef_TrajectoryPose09Steering
typedef sint16 TrajectoryPose09Steering;

# define Rte_TypeDef_TrajectoryPose10Acceleration
typedef sint16 TrajectoryPose10Acceleration;

# define Rte_TypeDef_TrajectoryPose10Curvature
typedef uint16 TrajectoryPose10Curvature;

# define Rte_TypeDef_TrajectoryPose10Heading
typedef sint16 TrajectoryPose10Heading;

# define Rte_TypeDef_TrajectoryPose10PositionX
typedef sint32 TrajectoryPose10PositionX;

# define Rte_TypeDef_TrajectoryPose10PositionY
typedef sint32 TrajectoryPose10PositionY;

# define Rte_TypeDef_TrajectoryPose10Speed
typedef uint16 TrajectoryPose10Speed;

# define Rte_TypeDef_TrajectoryPose10Steering
typedef sint16 TrajectoryPose10Steering;

# define Rte_TypeDef_TrajectoryPose11Acceleration
typedef sint16 TrajectoryPose11Acceleration;

# define Rte_TypeDef_TrajectoryPose11Curvature
typedef uint16 TrajectoryPose11Curvature;

# define Rte_TypeDef_TrajectoryPose11Heading
typedef sint16 TrajectoryPose11Heading;

# define Rte_TypeDef_TrajectoryPose11PositionX
typedef sint32 TrajectoryPose11PositionX;

# define Rte_TypeDef_TrajectoryPose11PositionY
typedef sint32 TrajectoryPose11PositionY;

# define Rte_TypeDef_TrajectoryPose11Speed
typedef uint16 TrajectoryPose11Speed;

# define Rte_TypeDef_TrajectoryPose11Steering
typedef sint16 TrajectoryPose11Steering;

# define Rte_TypeDef_TrajectoryPose12Acceleration
typedef sint16 TrajectoryPose12Acceleration;

# define Rte_TypeDef_TrajectoryPose12Curvature
typedef uint16 TrajectoryPose12Curvature;

# define Rte_TypeDef_TrajectoryPose12Heading
typedef sint16 TrajectoryPose12Heading;

# define Rte_TypeDef_TrajectoryPose12PositionX
typedef sint32 TrajectoryPose12PositionX;

# define Rte_TypeDef_TrajectoryPose12PositionY
typedef sint32 TrajectoryPose12PositionY;

# define Rte_TypeDef_TrajectoryPose12Speed
typedef uint16 TrajectoryPose12Speed;

# define Rte_TypeDef_TrajectoryPose12Steering
typedef sint16 TrajectoryPose12Steering;

# define Rte_TypeDef_TrajectoryPose13Acceleration
typedef sint16 TrajectoryPose13Acceleration;

# define Rte_TypeDef_TrajectoryPose13Curvature
typedef uint16 TrajectoryPose13Curvature;

# define Rte_TypeDef_TrajectoryPose13Heading
typedef sint16 TrajectoryPose13Heading;

# define Rte_TypeDef_TrajectoryPose13PositionX
typedef sint32 TrajectoryPose13PositionX;

# define Rte_TypeDef_TrajectoryPose13PositionY
typedef sint32 TrajectoryPose13PositionY;

# define Rte_TypeDef_TrajectoryPose13Speed
typedef uint16 TrajectoryPose13Speed;

# define Rte_TypeDef_TrajectoryPose13Steering
typedef sint16 TrajectoryPose13Steering;

# define Rte_TypeDef_TrajectoryPose14Acceleration
typedef sint16 TrajectoryPose14Acceleration;

# define Rte_TypeDef_TrajectoryPose14Curvature
typedef uint16 TrajectoryPose14Curvature;

# define Rte_TypeDef_TrajectoryPose14Heading
typedef sint16 TrajectoryPose14Heading;

# define Rte_TypeDef_TrajectoryPose14PositionX
typedef sint32 TrajectoryPose14PositionX;

# define Rte_TypeDef_TrajectoryPose14PositionY
typedef sint32 TrajectoryPose14PositionY;

# define Rte_TypeDef_TrajectoryPose14Speed
typedef uint16 TrajectoryPose14Speed;

# define Rte_TypeDef_TrajectoryPose14Steering
typedef sint16 TrajectoryPose14Steering;

# define Rte_TypeDef_TrajectoryPose15Acceleration
typedef sint16 TrajectoryPose15Acceleration;

# define Rte_TypeDef_TrajectoryPose15Curvature
typedef uint16 TrajectoryPose15Curvature;

# define Rte_TypeDef_TrajectoryPose15Heading
typedef sint16 TrajectoryPose15Heading;

# define Rte_TypeDef_TrajectoryPose15PositionX
typedef sint32 TrajectoryPose15PositionX;

# define Rte_TypeDef_TrajectoryPose15PositionY
typedef sint32 TrajectoryPose15PositionY;

# define Rte_TypeDef_TrajectoryPose15Speed
typedef uint16 TrajectoryPose15Speed;

# define Rte_TypeDef_TrajectoryPose15Steering
typedef sint16 TrajectoryPose15Steering;

# define Rte_TypeDef_TrajectoryPose16Acceleration
typedef sint16 TrajectoryPose16Acceleration;

# define Rte_TypeDef_TrajectoryPose16Curvature
typedef uint16 TrajectoryPose16Curvature;

# define Rte_TypeDef_TrajectoryPose16Heading
typedef sint16 TrajectoryPose16Heading;

# define Rte_TypeDef_TrajectoryPose16PositionX
typedef sint32 TrajectoryPose16PositionX;

# define Rte_TypeDef_TrajectoryPose16PositionY
typedef sint32 TrajectoryPose16PositionY;

# define Rte_TypeDef_TrajectoryPose16Speed
typedef uint16 TrajectoryPose16Speed;

# define Rte_TypeDef_TrajectoryPose16Steering
typedef sint16 TrajectoryPose16Steering;

# define Rte_TypeDef_TrajectoryPose17Acceleration
typedef sint16 TrajectoryPose17Acceleration;

# define Rte_TypeDef_TrajectoryPose17Curvature
typedef uint16 TrajectoryPose17Curvature;

# define Rte_TypeDef_TrajectoryPose17Heading
typedef sint16 TrajectoryPose17Heading;

# define Rte_TypeDef_TrajectoryPose17PositionX
typedef sint32 TrajectoryPose17PositionX;

# define Rte_TypeDef_TrajectoryPose17PositionY
typedef sint32 TrajectoryPose17PositionY;

# define Rte_TypeDef_TrajectoryPose17Speed
typedef uint16 TrajectoryPose17Speed;

# define Rte_TypeDef_TrajectoryPose17Steering
typedef sint16 TrajectoryPose17Steering;

# define Rte_TypeDef_TrajectoryPose18Acceleration
typedef sint16 TrajectoryPose18Acceleration;

# define Rte_TypeDef_TrajectoryPose18Curvature
typedef uint16 TrajectoryPose18Curvature;

# define Rte_TypeDef_TrajectoryPose18Heading
typedef sint16 TrajectoryPose18Heading;

# define Rte_TypeDef_TrajectoryPose18PositionX
typedef sint32 TrajectoryPose18PositionX;

# define Rte_TypeDef_TrajectoryPose18PositionY
typedef sint32 TrajectoryPose18PositionY;

# define Rte_TypeDef_TrajectoryPose18Speed
typedef uint16 TrajectoryPose18Speed;

# define Rte_TypeDef_TrajectoryPose18Steering
typedef sint16 TrajectoryPose18Steering;

# define Rte_TypeDef_TrajectoryPose19Acceleration
typedef sint16 TrajectoryPose19Acceleration;

# define Rte_TypeDef_TrajectoryPose19Curvature
typedef uint16 TrajectoryPose19Curvature;

# define Rte_TypeDef_TrajectoryPose19Heading
typedef sint16 TrajectoryPose19Heading;

# define Rte_TypeDef_TrajectoryPose19PositionX
typedef sint32 TrajectoryPose19PositionX;

# define Rte_TypeDef_TrajectoryPose19PositionY
typedef sint32 TrajectoryPose19PositionY;

# define Rte_TypeDef_TrajectoryPose19Speed
typedef uint16 TrajectoryPose19Speed;

# define Rte_TypeDef_TrajectoryPose19Steering
typedef sint16 TrajectoryPose19Steering;

# define Rte_TypeDef_TrajectoryPose20Acceleration
typedef sint16 TrajectoryPose20Acceleration;

# define Rte_TypeDef_TrajectoryPose20Curvature
typedef uint16 TrajectoryPose20Curvature;

# define Rte_TypeDef_TrajectoryPose20Heading
typedef sint16 TrajectoryPose20Heading;

# define Rte_TypeDef_TrajectoryPose20PositionX
typedef sint32 TrajectoryPose20PositionX;

# define Rte_TypeDef_TrajectoryPose20PositionY
typedef sint32 TrajectoryPose20PositionY;

# define Rte_TypeDef_TrajectoryPose20Speed
typedef uint16 TrajectoryPose20Speed;

# define Rte_TypeDef_TrajectoryPose20Steering
typedef sint16 TrajectoryPose20Steering;

# define Rte_TypeDef_TrajectoryPose21Acceleration
typedef sint16 TrajectoryPose21Acceleration;

# define Rte_TypeDef_TrajectoryPose21Curvature
typedef uint16 TrajectoryPose21Curvature;

# define Rte_TypeDef_TrajectoryPose21Heading
typedef sint16 TrajectoryPose21Heading;

# define Rte_TypeDef_TrajectoryPose21PositionX
typedef sint32 TrajectoryPose21PositionX;

# define Rte_TypeDef_TrajectoryPose21PositionY
typedef sint32 TrajectoryPose21PositionY;

# define Rte_TypeDef_TrajectoryPose21Speed
typedef uint16 TrajectoryPose21Speed;

# define Rte_TypeDef_TrajectoryPose21Steering
typedef sint16 TrajectoryPose21Steering;

# define Rte_TypeDef_TrajectoryPose22Acceleration
typedef sint16 TrajectoryPose22Acceleration;

# define Rte_TypeDef_TrajectoryPose22Curvature
typedef uint16 TrajectoryPose22Curvature;

# define Rte_TypeDef_TrajectoryPose22Heading
typedef sint16 TrajectoryPose22Heading;

# define Rte_TypeDef_TrajectoryPose22PositionX
typedef sint32 TrajectoryPose22PositionX;

# define Rte_TypeDef_TrajectoryPose22PositionY
typedef sint32 TrajectoryPose22PositionY;

# define Rte_TypeDef_TrajectoryPose22Speed
typedef uint16 TrajectoryPose22Speed;

# define Rte_TypeDef_TrajectoryPose22Steering
typedef sint16 TrajectoryPose22Steering;

# define Rte_TypeDef_TrajectoryPose23Acceleration
typedef sint16 TrajectoryPose23Acceleration;

# define Rte_TypeDef_TrajectoryPose23Curvature
typedef uint16 TrajectoryPose23Curvature;

# define Rte_TypeDef_TrajectoryPose23Heading
typedef sint16 TrajectoryPose23Heading;

# define Rte_TypeDef_TrajectoryPose23PositionX
typedef sint32 TrajectoryPose23PositionX;

# define Rte_TypeDef_TrajectoryPose23PositionY
typedef sint32 TrajectoryPose23PositionY;

# define Rte_TypeDef_TrajectoryPose23Speed
typedef uint16 TrajectoryPose23Speed;

# define Rte_TypeDef_TrajectoryPose23Steering
typedef sint16 TrajectoryPose23Steering;

# define Rte_TypeDef_TrajectoryPose24Acceleration
typedef sint16 TrajectoryPose24Acceleration;

# define Rte_TypeDef_TrajectoryPose24Curvature
typedef uint16 TrajectoryPose24Curvature;

# define Rte_TypeDef_TrajectoryPose24Heading
typedef sint16 TrajectoryPose24Heading;

# define Rte_TypeDef_TrajectoryPose24PositionX
typedef sint32 TrajectoryPose24PositionX;

# define Rte_TypeDef_TrajectoryPose24PositionY
typedef sint32 TrajectoryPose24PositionY;

# define Rte_TypeDef_TrajectoryPose24Speed
typedef uint16 TrajectoryPose24Speed;

# define Rte_TypeDef_TrajectoryPose24Steering
typedef sint16 TrajectoryPose24Steering;

# define Rte_TypeDef_TrajectoryPose25Acceleration
typedef sint16 TrajectoryPose25Acceleration;

# define Rte_TypeDef_TrajectoryPose25Curvature
typedef uint16 TrajectoryPose25Curvature;

# define Rte_TypeDef_TrajectoryPose25Heading
typedef sint16 TrajectoryPose25Heading;

# define Rte_TypeDef_TrajectoryPose25PositionX
typedef sint32 TrajectoryPose25PositionX;

# define Rte_TypeDef_TrajectoryPose25PositionY
typedef sint32 TrajectoryPose25PositionY;

# define Rte_TypeDef_TrajectoryPose25Speed
typedef uint16 TrajectoryPose25Speed;

# define Rte_TypeDef_TrajectoryPose25Steering
typedef sint16 TrajectoryPose25Steering;

# define Rte_TypeDef_TrajectoryPose26Acceleration
typedef sint16 TrajectoryPose26Acceleration;

# define Rte_TypeDef_TrajectoryPose26Curvature
typedef uint16 TrajectoryPose26Curvature;

# define Rte_TypeDef_TrajectoryPose26Heading
typedef sint16 TrajectoryPose26Heading;

# define Rte_TypeDef_TrajectoryPose26PositionX
typedef sint32 TrajectoryPose26PositionX;

# define Rte_TypeDef_TrajectoryPose26PositionY
typedef sint32 TrajectoryPose26PositionY;

# define Rte_TypeDef_TrajectoryPose26Speed
typedef uint16 TrajectoryPose26Speed;

# define Rte_TypeDef_TrajectoryPose26Steering
typedef sint16 TrajectoryPose26Steering;

# define Rte_TypeDef_TrajectoryPose27Acceleration
typedef sint16 TrajectoryPose27Acceleration;

# define Rte_TypeDef_TrajectoryPose27Curvature
typedef uint16 TrajectoryPose27Curvature;

# define Rte_TypeDef_TrajectoryPose27Heading
typedef sint16 TrajectoryPose27Heading;

# define Rte_TypeDef_TrajectoryPose27PositionX
typedef sint32 TrajectoryPose27PositionX;

# define Rte_TypeDef_TrajectoryPose27PositionY
typedef sint32 TrajectoryPose27PositionY;

# define Rte_TypeDef_TrajectoryPose27Speed
typedef uint16 TrajectoryPose27Speed;

# define Rte_TypeDef_TrajectoryPose27Steering
typedef sint16 TrajectoryPose27Steering;

# define Rte_TypeDef_TrajectoryPose28Acceleration
typedef sint16 TrajectoryPose28Acceleration;

# define Rte_TypeDef_TrajectoryPose28Curvature
typedef uint16 TrajectoryPose28Curvature;

# define Rte_TypeDef_TrajectoryPose28Heading
typedef sint16 TrajectoryPose28Heading;

# define Rte_TypeDef_TrajectoryPose28PositionX
typedef sint32 TrajectoryPose28PositionX;

# define Rte_TypeDef_TrajectoryPose28PositionY
typedef sint32 TrajectoryPose28PositionY;

# define Rte_TypeDef_TrajectoryPose28Speed
typedef uint16 TrajectoryPose28Speed;

# define Rte_TypeDef_TrajectoryPose28Steering
typedef sint16 TrajectoryPose28Steering;

# define Rte_TypeDef_TrajectoryPose29Acceleration
typedef sint16 TrajectoryPose29Acceleration;

# define Rte_TypeDef_TrajectoryPose29Curvature
typedef uint16 TrajectoryPose29Curvature;

# define Rte_TypeDef_TrajectoryPose29Heading
typedef sint16 TrajectoryPose29Heading;

# define Rte_TypeDef_TrajectoryPose29PositionX
typedef sint32 TrajectoryPose29PositionX;

# define Rte_TypeDef_TrajectoryPose29PositionY
typedef sint32 TrajectoryPose29PositionY;

# define Rte_TypeDef_TrajectoryPose29Speed
typedef uint16 TrajectoryPose29Speed;

# define Rte_TypeDef_TrajectoryPose29Steering
typedef sint16 TrajectoryPose29Steering;

# define Rte_TypeDef_TrajectoryPose30Acceleration
typedef sint16 TrajectoryPose30Acceleration;

# define Rte_TypeDef_TrajectoryPose30Curvature
typedef uint16 TrajectoryPose30Curvature;

# define Rte_TypeDef_TrajectoryPose30Heading
typedef sint16 TrajectoryPose30Heading;

# define Rte_TypeDef_TrajectoryPose30PositionX
typedef sint32 TrajectoryPose30PositionX;

# define Rte_TypeDef_TrajectoryPose30PositionY
typedef sint32 TrajectoryPose30PositionY;

# define Rte_TypeDef_TrajectoryPose30Speed
typedef uint16 TrajectoryPose30Speed;

# define Rte_TypeDef_TrajectoryPose30Steering
typedef sint16 TrajectoryPose30Steering;

# define Rte_TypeDef_TrajectoryPose31Acceleration
typedef sint16 TrajectoryPose31Acceleration;

# define Rte_TypeDef_TrajectoryPose31Curvature
typedef uint16 TrajectoryPose31Curvature;

# define Rte_TypeDef_TrajectoryPose31Heading
typedef sint16 TrajectoryPose31Heading;

# define Rte_TypeDef_TrajectoryPose31PositionX
typedef sint32 TrajectoryPose31PositionX;

# define Rte_TypeDef_TrajectoryPose31PositionY
typedef sint32 TrajectoryPose31PositionY;

# define Rte_TypeDef_TrajectoryPose31Speed
typedef uint16 TrajectoryPose31Speed;

# define Rte_TypeDef_TrajectoryPose31Steering
typedef sint16 TrajectoryPose31Steering;

# define Rte_TypeDef_TrajectoryPose32Acceleration
typedef sint16 TrajectoryPose32Acceleration;

# define Rte_TypeDef_TrajectoryPose32Curvature
typedef uint16 TrajectoryPose32Curvature;

# define Rte_TypeDef_TrajectoryPose32Heading
typedef sint16 TrajectoryPose32Heading;

# define Rte_TypeDef_TrajectoryPose32PositionX
typedef sint32 TrajectoryPose32PositionX;

# define Rte_TypeDef_TrajectoryPose32PositionY
typedef sint32 TrajectoryPose32PositionY;

# define Rte_TypeDef_TrajectoryPose32Speed
typedef uint16 TrajectoryPose32Speed;

# define Rte_TypeDef_TrajectoryPose32Steering
typedef sint16 TrajectoryPose32Steering;

# define Rte_TypeDef_TrajectoryPose33Acceleration
typedef sint16 TrajectoryPose33Acceleration;

# define Rte_TypeDef_TrajectoryPose33Curvature
typedef uint16 TrajectoryPose33Curvature;

# define Rte_TypeDef_TrajectoryPose33Heading
typedef sint16 TrajectoryPose33Heading;

# define Rte_TypeDef_TrajectoryPose33PositionX
typedef sint32 TrajectoryPose33PositionX;

# define Rte_TypeDef_TrajectoryPose33PositionY
typedef sint32 TrajectoryPose33PositionY;

# define Rte_TypeDef_TrajectoryPose33Speed
typedef uint16 TrajectoryPose33Speed;

# define Rte_TypeDef_TrajectoryPose33Steering
typedef sint16 TrajectoryPose33Steering;

# define Rte_TypeDef_TrajectoryPose34Acceleration
typedef sint16 TrajectoryPose34Acceleration;

# define Rte_TypeDef_TrajectoryPose34Curvature
typedef uint16 TrajectoryPose34Curvature;

# define Rte_TypeDef_TrajectoryPose34Heading
typedef sint16 TrajectoryPose34Heading;

# define Rte_TypeDef_TrajectoryPose34PositionX
typedef sint32 TrajectoryPose34PositionX;

# define Rte_TypeDef_TrajectoryPose34PositionY
typedef sint32 TrajectoryPose34PositionY;

# define Rte_TypeDef_TrajectoryPose34Speed
typedef uint16 TrajectoryPose34Speed;

# define Rte_TypeDef_TrajectoryPose34Steering
typedef sint16 TrajectoryPose34Steering;

# define Rte_TypeDef_TrajectoryPose35Acceleration
typedef sint16 TrajectoryPose35Acceleration;

# define Rte_TypeDef_TrajectoryPose35Curvature
typedef uint16 TrajectoryPose35Curvature;

# define Rte_TypeDef_TrajectoryPose35Heading
typedef sint16 TrajectoryPose35Heading;

# define Rte_TypeDef_TrajectoryPose35PositionX
typedef sint32 TrajectoryPose35PositionX;

# define Rte_TypeDef_TrajectoryPose35PositionY
typedef sint32 TrajectoryPose35PositionY;

# define Rte_TypeDef_TrajectoryPose35Speed
typedef uint16 TrajectoryPose35Speed;

# define Rte_TypeDef_TrajectoryPose35Steering
typedef sint16 TrajectoryPose35Steering;

# define Rte_TypeDef_TrajectoryPose36Acceleration
typedef sint16 TrajectoryPose36Acceleration;

# define Rte_TypeDef_TrajectoryPose36Curvature
typedef uint16 TrajectoryPose36Curvature;

# define Rte_TypeDef_TrajectoryPose36Heading
typedef sint16 TrajectoryPose36Heading;

# define Rte_TypeDef_TrajectoryPose36PositionX
typedef sint32 TrajectoryPose36PositionX;

# define Rte_TypeDef_TrajectoryPose36PositionY
typedef sint32 TrajectoryPose36PositionY;

# define Rte_TypeDef_TrajectoryPose36Speed
typedef uint16 TrajectoryPose36Speed;

# define Rte_TypeDef_TrajectoryPose36Steering
typedef sint16 TrajectoryPose36Steering;

# define Rte_TypeDef_TrajectoryPose37Acceleration
typedef sint16 TrajectoryPose37Acceleration;

# define Rte_TypeDef_TrajectoryPose37Curvature
typedef uint16 TrajectoryPose37Curvature;

# define Rte_TypeDef_TrajectoryPose37Heading
typedef sint16 TrajectoryPose37Heading;

# define Rte_TypeDef_TrajectoryPose37PositionX
typedef sint32 TrajectoryPose37PositionX;

# define Rte_TypeDef_TrajectoryPose37PositionY
typedef sint32 TrajectoryPose37PositionY;

# define Rte_TypeDef_TrajectoryPose37Speed
typedef uint16 TrajectoryPose37Speed;

# define Rte_TypeDef_TrajectoryPose37Steering
typedef sint16 TrajectoryPose37Steering;

# define Rte_TypeDef_TrajectoryPose38Acceleration
typedef sint16 TrajectoryPose38Acceleration;

# define Rte_TypeDef_TrajectoryPose38Curvature
typedef uint16 TrajectoryPose38Curvature;

# define Rte_TypeDef_TrajectoryPose38Heading
typedef sint16 TrajectoryPose38Heading;

# define Rte_TypeDef_TrajectoryPose38PositionX
typedef sint32 TrajectoryPose38PositionX;

# define Rte_TypeDef_TrajectoryPose38PositionY
typedef sint32 TrajectoryPose38PositionY;

# define Rte_TypeDef_TrajectoryPose38Speed
typedef uint16 TrajectoryPose38Speed;

# define Rte_TypeDef_TrajectoryPose38Steering
typedef sint16 TrajectoryPose38Steering;

# define Rte_TypeDef_TrajectoryPose39Acceleration
typedef sint16 TrajectoryPose39Acceleration;

# define Rte_TypeDef_TrajectoryPose39Curvature
typedef uint16 TrajectoryPose39Curvature;

# define Rte_TypeDef_TrajectoryPose39Heading
typedef sint16 TrajectoryPose39Heading;

# define Rte_TypeDef_TrajectoryPose39PositionX
typedef sint32 TrajectoryPose39PositionX;

# define Rte_TypeDef_TrajectoryPose39PositionY
typedef sint32 TrajectoryPose39PositionY;

# define Rte_TypeDef_TrajectoryPose39Speed
typedef uint16 TrajectoryPose39Speed;

# define Rte_TypeDef_TrajectoryPose39Steering
typedef sint16 TrajectoryPose39Steering;

# define Rte_TypeDef_TrajectoryPose40Acceleration
typedef sint16 TrajectoryPose40Acceleration;

# define Rte_TypeDef_TrajectoryPose40Curvature
typedef uint16 TrajectoryPose40Curvature;

# define Rte_TypeDef_TrajectoryPose40Heading
typedef sint16 TrajectoryPose40Heading;

# define Rte_TypeDef_TrajectoryPose40PositionX
typedef sint32 TrajectoryPose40PositionX;

# define Rte_TypeDef_TrajectoryPose40PositionY
typedef sint32 TrajectoryPose40PositionY;

# define Rte_TypeDef_TrajectoryPose40Speed
typedef uint16 TrajectoryPose40Speed;

# define Rte_TypeDef_TrajectoryPose40Steering
typedef sint16 TrajectoryPose40Steering;

# define Rte_TypeDef_TrajectoryPose41Acceleration
typedef sint16 TrajectoryPose41Acceleration;

# define Rte_TypeDef_TrajectoryPose41Curvature
typedef uint16 TrajectoryPose41Curvature;

# define Rte_TypeDef_TrajectoryPose41Heading
typedef sint16 TrajectoryPose41Heading;

# define Rte_TypeDef_TrajectoryPose41PositionX
typedef sint32 TrajectoryPose41PositionX;

# define Rte_TypeDef_TrajectoryPose41PositionY
typedef sint32 TrajectoryPose41PositionY;

# define Rte_TypeDef_TrajectoryPose41Speed
typedef uint16 TrajectoryPose41Speed;

# define Rte_TypeDef_TrajectoryPose41Steering
typedef sint16 TrajectoryPose41Steering;

# define Rte_TypeDef_TrajectoryPose42Acceleration
typedef sint16 TrajectoryPose42Acceleration;

# define Rte_TypeDef_TrajectoryPose42Curvature
typedef uint16 TrajectoryPose42Curvature;

# define Rte_TypeDef_TrajectoryPose42Heading
typedef sint16 TrajectoryPose42Heading;

# define Rte_TypeDef_TrajectoryPose42PositionX
typedef sint32 TrajectoryPose42PositionX;

# define Rte_TypeDef_TrajectoryPose42PositionY
typedef sint32 TrajectoryPose42PositionY;

# define Rte_TypeDef_TrajectoryPose42Speed
typedef uint16 TrajectoryPose42Speed;

# define Rte_TypeDef_TrajectoryPose42Steering
typedef sint16 TrajectoryPose42Steering;

# define Rte_TypeDef_TrajectoryPose43Acceleration
typedef sint16 TrajectoryPose43Acceleration;

# define Rte_TypeDef_TrajectoryPose43Curvature
typedef uint16 TrajectoryPose43Curvature;

# define Rte_TypeDef_TrajectoryPose43Heading
typedef sint16 TrajectoryPose43Heading;

# define Rte_TypeDef_TrajectoryPose43PositionX
typedef sint32 TrajectoryPose43PositionX;

# define Rte_TypeDef_TrajectoryPose43PositionY
typedef sint32 TrajectoryPose43PositionY;

# define Rte_TypeDef_TrajectoryPose43Speed
typedef uint16 TrajectoryPose43Speed;

# define Rte_TypeDef_TrajectoryPose43Steering
typedef sint16 TrajectoryPose43Steering;

# define Rte_TypeDef_TrajectoryPose44Acceleration
typedef sint16 TrajectoryPose44Acceleration;

# define Rte_TypeDef_TrajectoryPose44Curvature
typedef uint16 TrajectoryPose44Curvature;

# define Rte_TypeDef_TrajectoryPose44Heading
typedef sint16 TrajectoryPose44Heading;

# define Rte_TypeDef_TrajectoryPose44PositionX
typedef sint32 TrajectoryPose44PositionX;

# define Rte_TypeDef_TrajectoryPose44PositionY
typedef sint32 TrajectoryPose44PositionY;

# define Rte_TypeDef_TrajectoryPose44Speed
typedef uint16 TrajectoryPose44Speed;

# define Rte_TypeDef_TrajectoryPose44Steering
typedef sint16 TrajectoryPose44Steering;

# define Rte_TypeDef_TrajectoryPose45Acceleration
typedef sint16 TrajectoryPose45Acceleration;

# define Rte_TypeDef_TrajectoryPose45Curvature
typedef uint16 TrajectoryPose45Curvature;

# define Rte_TypeDef_TrajectoryPose45Heading
typedef sint16 TrajectoryPose45Heading;

# define Rte_TypeDef_TrajectoryPose45PositionX
typedef sint32 TrajectoryPose45PositionX;

# define Rte_TypeDef_TrajectoryPose45PositionY
typedef sint32 TrajectoryPose45PositionY;

# define Rte_TypeDef_TrajectoryPose45Speed
typedef uint16 TrajectoryPose45Speed;

# define Rte_TypeDef_TrajectoryPose45Steering
typedef sint16 TrajectoryPose45Steering;

# define Rte_TypeDef_TrajectoryPose46Acceleration
typedef sint16 TrajectoryPose46Acceleration;

# define Rte_TypeDef_TrajectoryPose46Curvature
typedef uint16 TrajectoryPose46Curvature;

# define Rte_TypeDef_TrajectoryPose46Heading
typedef sint16 TrajectoryPose46Heading;

# define Rte_TypeDef_TrajectoryPose46PositionX
typedef sint32 TrajectoryPose46PositionX;

# define Rte_TypeDef_TrajectoryPose46PositionY
typedef sint32 TrajectoryPose46PositionY;

# define Rte_TypeDef_TrajectoryPose46Speed
typedef uint16 TrajectoryPose46Speed;

# define Rte_TypeDef_TrajectoryPose46Steering
typedef sint16 TrajectoryPose46Steering;

# define Rte_TypeDef_TrajectoryPose47Acceleration
typedef sint16 TrajectoryPose47Acceleration;

# define Rte_TypeDef_TrajectoryPose47Curvature
typedef uint16 TrajectoryPose47Curvature;

# define Rte_TypeDef_TrajectoryPose47Heading
typedef sint16 TrajectoryPose47Heading;

# define Rte_TypeDef_TrajectoryPose47PositionX
typedef sint32 TrajectoryPose47PositionX;

# define Rte_TypeDef_TrajectoryPose47PositionY
typedef sint32 TrajectoryPose47PositionY;

# define Rte_TypeDef_TrajectoryPose47Speed
typedef uint16 TrajectoryPose47Speed;

# define Rte_TypeDef_TrajectoryPose47Steering
typedef sint16 TrajectoryPose47Steering;

# define Rte_TypeDef_TrajectoryPose48Acceleration
typedef sint16 TrajectoryPose48Acceleration;

# define Rte_TypeDef_TrajectoryPose48Curvature
typedef uint16 TrajectoryPose48Curvature;

# define Rte_TypeDef_TrajectoryPose48Heading
typedef sint16 TrajectoryPose48Heading;

# define Rte_TypeDef_TrajectoryPose48PositionX
typedef sint32 TrajectoryPose48PositionX;

# define Rte_TypeDef_TrajectoryPose48PositionY
typedef sint32 TrajectoryPose48PositionY;

# define Rte_TypeDef_TrajectoryPose48Speed
typedef uint16 TrajectoryPose48Speed;

# define Rte_TypeDef_TrajectoryPose48Steering
typedef sint16 TrajectoryPose48Steering;

# define Rte_TypeDef_TrajectoryPose49Acceleration
typedef sint16 TrajectoryPose49Acceleration;

# define Rte_TypeDef_TrajectoryPose49Curvature
typedef uint16 TrajectoryPose49Curvature;

# define Rte_TypeDef_TrajectoryPose49Heading
typedef sint16 TrajectoryPose49Heading;

# define Rte_TypeDef_TrajectoryPose49PositionX
typedef sint32 TrajectoryPose49PositionX;

# define Rte_TypeDef_TrajectoryPose49PositionY
typedef sint32 TrajectoryPose49PositionY;

# define Rte_TypeDef_TrajectoryPose49Speed
typedef uint16 TrajectoryPose49Speed;

# define Rte_TypeDef_TrajectoryPose49Steering
typedef sint16 TrajectoryPose49Steering;

# define Rte_TypeDef_TrajectoryPose50Acceleration
typedef sint16 TrajectoryPose50Acceleration;

# define Rte_TypeDef_TrajectoryPose50Curvature
typedef uint16 TrajectoryPose50Curvature;

# define Rte_TypeDef_TrajectoryPose50Heading
typedef sint16 TrajectoryPose50Heading;

# define Rte_TypeDef_TrajectoryPose50PositionX
typedef sint32 TrajectoryPose50PositionX;

# define Rte_TypeDef_TrajectoryPose50PositionY
typedef sint32 TrajectoryPose50PositionY;

# define Rte_TypeDef_TrajectoryPose50Speed
typedef uint16 TrajectoryPose50Speed;

# define Rte_TypeDef_TrajectoryPose50Steering
typedef sint16 TrajectoryPose50Steering;

# define Rte_TypeDef_TrajectoryPose51Acceleration
typedef sint16 TrajectoryPose51Acceleration;

# define Rte_TypeDef_TrajectoryPose51Curvature
typedef uint16 TrajectoryPose51Curvature;

# define Rte_TypeDef_TrajectoryPose51Heading
typedef sint16 TrajectoryPose51Heading;

# define Rte_TypeDef_TrajectoryPose51PositionX
typedef sint32 TrajectoryPose51PositionX;

# define Rte_TypeDef_TrajectoryPose51PositionY
typedef sint32 TrajectoryPose51PositionY;

# define Rte_TypeDef_TrajectoryPose51Speed
typedef uint16 TrajectoryPose51Speed;

# define Rte_TypeDef_TrajectoryPose51Steering
typedef sint16 TrajectoryPose51Steering;

# define Rte_TypeDef_TrajectoryPose52Acceleration
typedef sint16 TrajectoryPose52Acceleration;

# define Rte_TypeDef_TrajectoryPose52Curvature
typedef uint16 TrajectoryPose52Curvature;

# define Rte_TypeDef_TrajectoryPose52Heading
typedef sint16 TrajectoryPose52Heading;

# define Rte_TypeDef_TrajectoryPose52PositionX
typedef sint32 TrajectoryPose52PositionX;

# define Rte_TypeDef_TrajectoryPose52PositionY
typedef sint32 TrajectoryPose52PositionY;

# define Rte_TypeDef_TrajectoryPose52Speed
typedef uint16 TrajectoryPose52Speed;

# define Rte_TypeDef_TrajectoryPose52Steering
typedef sint16 TrajectoryPose52Steering;

# define Rte_TypeDef_TrajectoryPose53Acceleration
typedef sint16 TrajectoryPose53Acceleration;

# define Rte_TypeDef_TrajectoryPose53Curvature
typedef uint16 TrajectoryPose53Curvature;

# define Rte_TypeDef_TrajectoryPose53Heading
typedef sint16 TrajectoryPose53Heading;

# define Rte_TypeDef_TrajectoryPose53PositionX
typedef sint32 TrajectoryPose53PositionX;

# define Rte_TypeDef_TrajectoryPose53PositionY
typedef sint32 TrajectoryPose53PositionY;

# define Rte_TypeDef_TrajectoryPose53Speed
typedef uint16 TrajectoryPose53Speed;

# define Rte_TypeDef_TrajectoryPose53Steering
typedef sint16 TrajectoryPose53Steering;

# define Rte_TypeDef_TrajectoryPose54Acceleration
typedef sint16 TrajectoryPose54Acceleration;

# define Rte_TypeDef_TrajectoryPose54Curvature
typedef uint16 TrajectoryPose54Curvature;

# define Rte_TypeDef_TrajectoryPose54Heading
typedef sint16 TrajectoryPose54Heading;

# define Rte_TypeDef_TrajectoryPose54PositionX
typedef sint32 TrajectoryPose54PositionX;

# define Rte_TypeDef_TrajectoryPose54PositionY
typedef sint32 TrajectoryPose54PositionY;

# define Rte_TypeDef_TrajectoryPose54Speed
typedef uint16 TrajectoryPose54Speed;

# define Rte_TypeDef_TrajectoryPose54Steering
typedef sint16 TrajectoryPose54Steering;

# define Rte_TypeDef_TrajectoryPose55Acceleration
typedef sint16 TrajectoryPose55Acceleration;

# define Rte_TypeDef_TrajectoryPose55Curvature
typedef uint16 TrajectoryPose55Curvature;

# define Rte_TypeDef_TrajectoryPose55Heading
typedef sint16 TrajectoryPose55Heading;

# define Rte_TypeDef_TrajectoryPose55PositionX
typedef sint32 TrajectoryPose55PositionX;

# define Rte_TypeDef_TrajectoryPose55PositionY
typedef sint32 TrajectoryPose55PositionY;

# define Rte_TypeDef_TrajectoryPose55Speed
typedef uint16 TrajectoryPose55Speed;

# define Rte_TypeDef_TrajectoryPose55Steering
typedef sint16 TrajectoryPose55Steering;

# define Rte_TypeDef_TrajectoryPose56Acceleration
typedef sint16 TrajectoryPose56Acceleration;

# define Rte_TypeDef_TrajectoryPose56Curvature
typedef uint16 TrajectoryPose56Curvature;

# define Rte_TypeDef_TrajectoryPose56Heading
typedef sint16 TrajectoryPose56Heading;

# define Rte_TypeDef_TrajectoryPose56PositionX
typedef sint32 TrajectoryPose56PositionX;

# define Rte_TypeDef_TrajectoryPose56PositionY
typedef sint32 TrajectoryPose56PositionY;

# define Rte_TypeDef_TrajectoryPose56Speed
typedef uint16 TrajectoryPose56Speed;

# define Rte_TypeDef_TrajectoryPose56Steering
typedef sint16 TrajectoryPose56Steering;

# define Rte_TypeDef_TrajectoryPose57Acceleration
typedef sint16 TrajectoryPose57Acceleration;

# define Rte_TypeDef_TrajectoryPose57Curvature
typedef uint16 TrajectoryPose57Curvature;

# define Rte_TypeDef_TrajectoryPose57Heading
typedef sint16 TrajectoryPose57Heading;

# define Rte_TypeDef_TrajectoryPose57PositionX
typedef sint32 TrajectoryPose57PositionX;

# define Rte_TypeDef_TrajectoryPose57PositionY
typedef sint32 TrajectoryPose57PositionY;

# define Rte_TypeDef_TrajectoryPose57Speed
typedef uint16 TrajectoryPose57Speed;

# define Rte_TypeDef_TrajectoryPose57Steering
typedef sint16 TrajectoryPose57Steering;

# define Rte_TypeDef_TrajectoryPose58Acceleration
typedef sint16 TrajectoryPose58Acceleration;

# define Rte_TypeDef_TrajectoryPose58Curvature
typedef uint16 TrajectoryPose58Curvature;

# define Rte_TypeDef_TrajectoryPose58Heading
typedef sint16 TrajectoryPose58Heading;

# define Rte_TypeDef_TrajectoryPose58PositionX
typedef sint32 TrajectoryPose58PositionX;

# define Rte_TypeDef_TrajectoryPose58PositionY
typedef sint32 TrajectoryPose58PositionY;

# define Rte_TypeDef_TrajectoryPose58Speed
typedef uint16 TrajectoryPose58Speed;

# define Rte_TypeDef_TrajectoryPose58Steering
typedef sint16 TrajectoryPose58Steering;

# define Rte_TypeDef_TrajectoryPose59Acceleration
typedef sint16 TrajectoryPose59Acceleration;

# define Rte_TypeDef_TrajectoryPose59Curvature
typedef uint16 TrajectoryPose59Curvature;

# define Rte_TypeDef_TrajectoryPose59Heading
typedef sint16 TrajectoryPose59Heading;

# define Rte_TypeDef_TrajectoryPose59PositionX
typedef sint32 TrajectoryPose59PositionX;

# define Rte_TypeDef_TrajectoryPose59PositionY
typedef sint32 TrajectoryPose59PositionY;

# define Rte_TypeDef_TrajectoryPose59Speed
typedef uint16 TrajectoryPose59Speed;

# define Rte_TypeDef_TrajectoryPose59Steering
typedef sint16 TrajectoryPose59Steering;

# define Rte_TypeDef_TrajectoryPose60Acceleration
typedef sint16 TrajectoryPose60Acceleration;

# define Rte_TypeDef_TrajectoryPose60Curvature
typedef uint16 TrajectoryPose60Curvature;

# define Rte_TypeDef_TrajectoryPose60Heading
typedef sint16 TrajectoryPose60Heading;

# define Rte_TypeDef_TrajectoryPose60PositionX
typedef sint32 TrajectoryPose60PositionX;

# define Rte_TypeDef_TrajectoryPose60PositionY
typedef sint32 TrajectoryPose60PositionY;

# define Rte_TypeDef_TrajectoryPose60Speed
typedef uint16 TrajectoryPose60Speed;

# define Rte_TypeDef_TrajectoryPose60Steering
typedef sint16 TrajectoryPose60Steering;

# define Rte_TypeDef_TrajectoryPose61Acceleration
typedef sint16 TrajectoryPose61Acceleration;

# define Rte_TypeDef_TrajectoryPose61Curvature
typedef uint16 TrajectoryPose61Curvature;

# define Rte_TypeDef_TrajectoryPose61Heading
typedef sint16 TrajectoryPose61Heading;

# define Rte_TypeDef_TrajectoryPose61PositionX
typedef sint32 TrajectoryPose61PositionX;

# define Rte_TypeDef_TrajectoryPose61PositionY
typedef sint32 TrajectoryPose61PositionY;

# define Rte_TypeDef_TrajectoryPose61Speed
typedef uint16 TrajectoryPose61Speed;

# define Rte_TypeDef_TrajectoryPose61Steering
typedef sint16 TrajectoryPose61Steering;

# define Rte_TypeDef_TrajectoryPose62Acceleration
typedef sint16 TrajectoryPose62Acceleration;

# define Rte_TypeDef_TrajectoryPose62Curvature
typedef uint16 TrajectoryPose62Curvature;

# define Rte_TypeDef_TrajectoryPose62Heading
typedef sint16 TrajectoryPose62Heading;

# define Rte_TypeDef_TrajectoryPose62PositionX
typedef sint32 TrajectoryPose62PositionX;

# define Rte_TypeDef_TrajectoryPose62PositionY
typedef sint32 TrajectoryPose62PositionY;

# define Rte_TypeDef_TrajectoryPose62Speed
typedef uint16 TrajectoryPose62Speed;

# define Rte_TypeDef_TrajectoryPose62Steering
typedef sint16 TrajectoryPose62Steering;

# define Rte_TypeDef_TrajectoryPose63Acceleration
typedef sint16 TrajectoryPose63Acceleration;

# define Rte_TypeDef_TrajectoryPose63Curvature
typedef uint16 TrajectoryPose63Curvature;

# define Rte_TypeDef_TrajectoryPose63Heading
typedef sint16 TrajectoryPose63Heading;

# define Rte_TypeDef_TrajectoryPose63PositionX
typedef sint32 TrajectoryPose63PositionX;

# define Rte_TypeDef_TrajectoryPose63PositionY
typedef sint32 TrajectoryPose63PositionY;

# define Rte_TypeDef_TrajectoryPose63Speed
typedef uint16 TrajectoryPose63Speed;

# define Rte_TypeDef_TrajectoryPose63Steering
typedef sint16 TrajectoryPose63Steering;

# define Rte_TypeDef_TrajectoryPose64Acceleration
typedef sint16 TrajectoryPose64Acceleration;

# define Rte_TypeDef_TrajectoryPose64Curvature
typedef uint16 TrajectoryPose64Curvature;

# define Rte_TypeDef_TrajectoryPose64Heading
typedef sint16 TrajectoryPose64Heading;

# define Rte_TypeDef_TrajectoryPose64PositionX
typedef sint32 TrajectoryPose64PositionX;

# define Rte_TypeDef_TrajectoryPose64PositionY
typedef sint32 TrajectoryPose64PositionY;

# define Rte_TypeDef_TrajectoryPose64Speed
typedef uint16 TrajectoryPose64Speed;

# define Rte_TypeDef_TrajectoryPose64Steering
typedef sint16 TrajectoryPose64Steering;

# define Rte_TypeDef_TrajectoryPose65Acceleration
typedef sint16 TrajectoryPose65Acceleration;

# define Rte_TypeDef_TrajectoryPose65Curvature
typedef uint16 TrajectoryPose65Curvature;

# define Rte_TypeDef_TrajectoryPose65Heading
typedef sint16 TrajectoryPose65Heading;

# define Rte_TypeDef_TrajectoryPose65PositionX
typedef sint32 TrajectoryPose65PositionX;

# define Rte_TypeDef_TrajectoryPose65PositionY
typedef sint32 TrajectoryPose65PositionY;

# define Rte_TypeDef_TrajectoryPose65Speed
typedef uint16 TrajectoryPose65Speed;

# define Rte_TypeDef_TrajectoryPose65Steering
typedef sint16 TrajectoryPose65Steering;

# define Rte_TypeDef_TrajectoryPose66Acceleration
typedef sint16 TrajectoryPose66Acceleration;

# define Rte_TypeDef_TrajectoryPose66Curvature
typedef uint16 TrajectoryPose66Curvature;

# define Rte_TypeDef_TrajectoryPose66Heading
typedef sint16 TrajectoryPose66Heading;

# define Rte_TypeDef_TrajectoryPose66PositionX
typedef sint32 TrajectoryPose66PositionX;

# define Rte_TypeDef_TrajectoryPose66PositionY
typedef sint32 TrajectoryPose66PositionY;

# define Rte_TypeDef_TrajectoryPose66Speed
typedef uint16 TrajectoryPose66Speed;

# define Rte_TypeDef_TrajectoryPose66Steering
typedef sint16 TrajectoryPose66Steering;

# define Rte_TypeDef_TrajectoryPose67Acceleration
typedef sint16 TrajectoryPose67Acceleration;

# define Rte_TypeDef_TrajectoryPose67Curvature
typedef uint16 TrajectoryPose67Curvature;

# define Rte_TypeDef_TrajectoryPose67Heading
typedef sint16 TrajectoryPose67Heading;

# define Rte_TypeDef_TrajectoryPose67PositionX
typedef sint32 TrajectoryPose67PositionX;

# define Rte_TypeDef_TrajectoryPose67PositionY
typedef sint32 TrajectoryPose67PositionY;

# define Rte_TypeDef_TrajectoryPose67Speed
typedef uint16 TrajectoryPose67Speed;

# define Rte_TypeDef_TrajectoryPose67Steering
typedef sint16 TrajectoryPose67Steering;

# define Rte_TypeDef_TrajectoryPose68Acceleration
typedef sint16 TrajectoryPose68Acceleration;

# define Rte_TypeDef_TrajectoryPose68Curvature
typedef uint16 TrajectoryPose68Curvature;

# define Rte_TypeDef_TrajectoryPose68Heading
typedef sint16 TrajectoryPose68Heading;

# define Rte_TypeDef_TrajectoryPose68PositionX
typedef sint32 TrajectoryPose68PositionX;

# define Rte_TypeDef_TrajectoryPose68PositionY
typedef sint32 TrajectoryPose68PositionY;

# define Rte_TypeDef_TrajectoryPose68Speed
typedef uint16 TrajectoryPose68Speed;

# define Rte_TypeDef_TrajectoryPose68Steering
typedef sint16 TrajectoryPose68Steering;

# define Rte_TypeDef_TrajectoryPose69Acceleration
typedef sint16 TrajectoryPose69Acceleration;

# define Rte_TypeDef_TrajectoryPose69Curvature
typedef uint16 TrajectoryPose69Curvature;

# define Rte_TypeDef_TrajectoryPose69Heading
typedef sint16 TrajectoryPose69Heading;

# define Rte_TypeDef_TrajectoryPose69PositionX
typedef sint32 TrajectoryPose69PositionX;

# define Rte_TypeDef_TrajectoryPose69PositionY
typedef sint32 TrajectoryPose69PositionY;

# define Rte_TypeDef_TrajectoryPose69Speed
typedef uint16 TrajectoryPose69Speed;

# define Rte_TypeDef_TrajectoryPose69Steering
typedef sint16 TrajectoryPose69Steering;

# define Rte_TypeDef_TrajectoryPose70Acceleration
typedef sint16 TrajectoryPose70Acceleration;

# define Rte_TypeDef_TrajectoryPose70Curvature
typedef uint16 TrajectoryPose70Curvature;

# define Rte_TypeDef_TrajectoryPose70Heading
typedef sint16 TrajectoryPose70Heading;

# define Rte_TypeDef_TrajectoryPose70PositionX
typedef sint32 TrajectoryPose70PositionX;

# define Rte_TypeDef_TrajectoryPose70PositionY
typedef sint32 TrajectoryPose70PositionY;

# define Rte_TypeDef_TrajectoryPose70Speed
typedef uint16 TrajectoryPose70Speed;

# define Rte_TypeDef_TrajectoryPose70Steering
typedef sint16 TrajectoryPose70Steering;

# define Rte_TypeDef_TrajectoryPose71Acceleration
typedef sint16 TrajectoryPose71Acceleration;

# define Rte_TypeDef_TrajectoryPose71Curvature
typedef uint16 TrajectoryPose71Curvature;

# define Rte_TypeDef_TrajectoryPose71Heading
typedef sint16 TrajectoryPose71Heading;

# define Rte_TypeDef_TrajectoryPose71PositionX
typedef sint32 TrajectoryPose71PositionX;

# define Rte_TypeDef_TrajectoryPose71PositionY
typedef sint32 TrajectoryPose71PositionY;

# define Rte_TypeDef_TrajectoryPose71Speed
typedef uint16 TrajectoryPose71Speed;

# define Rte_TypeDef_TrajectoryPose71Steering
typedef sint16 TrajectoryPose71Steering;

# define Rte_TypeDef_TrajectoryPose72Acceleration
typedef sint16 TrajectoryPose72Acceleration;

# define Rte_TypeDef_TrajectoryPose72Curvature
typedef uint16 TrajectoryPose72Curvature;

# define Rte_TypeDef_TrajectoryPose72Heading
typedef sint16 TrajectoryPose72Heading;

# define Rte_TypeDef_TrajectoryPose72PositionX
typedef sint32 TrajectoryPose72PositionX;

# define Rte_TypeDef_TrajectoryPose72PositionY
typedef sint32 TrajectoryPose72PositionY;

# define Rte_TypeDef_TrajectoryPose72Speed
typedef uint16 TrajectoryPose72Speed;

# define Rte_TypeDef_TrajectoryPose72Steering
typedef sint16 TrajectoryPose72Steering;

# define Rte_TypeDef_TrajectoryPose73Acceleration
typedef sint16 TrajectoryPose73Acceleration;

# define Rte_TypeDef_TrajectoryPose73Curvature
typedef uint16 TrajectoryPose73Curvature;

# define Rte_TypeDef_TrajectoryPose73Heading
typedef sint16 TrajectoryPose73Heading;

# define Rte_TypeDef_TrajectoryPose73PositionX
typedef sint32 TrajectoryPose73PositionX;

# define Rte_TypeDef_TrajectoryPose73PositionY
typedef sint32 TrajectoryPose73PositionY;

# define Rte_TypeDef_TrajectoryPose73Speed
typedef uint16 TrajectoryPose73Speed;

# define Rte_TypeDef_TrajectoryPose73Steering
typedef sint16 TrajectoryPose73Steering;

# define Rte_TypeDef_TrajectoryPose74Acceleration
typedef sint16 TrajectoryPose74Acceleration;

# define Rte_TypeDef_TrajectoryPose74Curvature
typedef uint16 TrajectoryPose74Curvature;

# define Rte_TypeDef_TrajectoryPose74Heading
typedef sint16 TrajectoryPose74Heading;

# define Rte_TypeDef_TrajectoryPose74PositionX
typedef sint32 TrajectoryPose74PositionX;

# define Rte_TypeDef_TrajectoryPose74PositionY
typedef sint32 TrajectoryPose74PositionY;

# define Rte_TypeDef_TrajectoryPose74Speed
typedef uint16 TrajectoryPose74Speed;

# define Rte_TypeDef_TrajectoryPose74Steering
typedef sint16 TrajectoryPose74Steering;

# define Rte_TypeDef_TrajectoryPose75Acceleration
typedef sint16 TrajectoryPose75Acceleration;

# define Rte_TypeDef_TrajectoryPose75Curvature
typedef uint16 TrajectoryPose75Curvature;

# define Rte_TypeDef_TrajectoryPose75Heading
typedef sint16 TrajectoryPose75Heading;

# define Rte_TypeDef_TrajectoryPose75PositionX
typedef sint32 TrajectoryPose75PositionX;

# define Rte_TypeDef_TrajectoryPose75PositionY
typedef sint32 TrajectoryPose75PositionY;

# define Rte_TypeDef_TrajectoryPose75Speed
typedef uint16 TrajectoryPose75Speed;

# define Rte_TypeDef_TrajectoryPose75Steering
typedef sint16 TrajectoryPose75Steering;

# define Rte_TypeDef_TrajectoryPose76Acceleration
typedef sint16 TrajectoryPose76Acceleration;

# define Rte_TypeDef_TrajectoryPose76Curvature
typedef uint16 TrajectoryPose76Curvature;

# define Rte_TypeDef_TrajectoryPose76Heading
typedef sint16 TrajectoryPose76Heading;

# define Rte_TypeDef_TrajectoryPose76PositionX
typedef sint32 TrajectoryPose76PositionX;

# define Rte_TypeDef_TrajectoryPose76PositionY
typedef sint32 TrajectoryPose76PositionY;

# define Rte_TypeDef_TrajectoryPose76Speed
typedef uint16 TrajectoryPose76Speed;

# define Rte_TypeDef_TrajectoryPose76Steering
typedef sint16 TrajectoryPose76Steering;

# define Rte_TypeDef_TrajectoryPose77Acceleration
typedef sint16 TrajectoryPose77Acceleration;

# define Rte_TypeDef_TrajectoryPose77Curvature
typedef uint16 TrajectoryPose77Curvature;

# define Rte_TypeDef_TrajectoryPose77Heading
typedef sint16 TrajectoryPose77Heading;

# define Rte_TypeDef_TrajectoryPose77PositionX
typedef sint32 TrajectoryPose77PositionX;

# define Rte_TypeDef_TrajectoryPose77PositionY
typedef sint32 TrajectoryPose77PositionY;

# define Rte_TypeDef_TrajectoryPose77Speed
typedef uint16 TrajectoryPose77Speed;

# define Rte_TypeDef_TrajectoryPose77Steering
typedef sint16 TrajectoryPose77Steering;

# define Rte_TypeDef_TrajectoryPose78Acceleration
typedef sint16 TrajectoryPose78Acceleration;

# define Rte_TypeDef_TrajectoryPose78Curvature
typedef uint16 TrajectoryPose78Curvature;

# define Rte_TypeDef_TrajectoryPose78Heading
typedef sint16 TrajectoryPose78Heading;

# define Rte_TypeDef_TrajectoryPose78PositionX
typedef sint32 TrajectoryPose78PositionX;

# define Rte_TypeDef_TrajectoryPose78PositionY
typedef sint32 TrajectoryPose78PositionY;

# define Rte_TypeDef_TrajectoryPose78Speed
typedef uint16 TrajectoryPose78Speed;

# define Rte_TypeDef_TrajectoryPose78Steering
typedef sint16 TrajectoryPose78Steering;

# define Rte_TypeDef_TrajectoryPose79Acceleration
typedef sint16 TrajectoryPose79Acceleration;

# define Rte_TypeDef_TrajectoryPose79Curvature
typedef uint16 TrajectoryPose79Curvature;

# define Rte_TypeDef_TrajectoryPose79Heading
typedef sint16 TrajectoryPose79Heading;

# define Rte_TypeDef_TrajectoryPose79PositionX
typedef sint32 TrajectoryPose79PositionX;

# define Rte_TypeDef_TrajectoryPose79PositionY
typedef sint32 TrajectoryPose79PositionY;

# define Rte_TypeDef_TrajectoryPose79Speed
typedef uint16 TrajectoryPose79Speed;

# define Rte_TypeDef_TrajectoryPose79Steering
typedef sint16 TrajectoryPose79Steering;

# define Rte_TypeDef_TrajectoryPose80Acceleration
typedef sint16 TrajectoryPose80Acceleration;

# define Rte_TypeDef_TrajectoryPose80Curvature
typedef uint16 TrajectoryPose80Curvature;

# define Rte_TypeDef_TrajectoryPose80Heading
typedef sint16 TrajectoryPose80Heading;

# define Rte_TypeDef_TrajectoryPose80PositionX
typedef sint32 TrajectoryPose80PositionX;

# define Rte_TypeDef_TrajectoryPose80PositionY
typedef sint32 TrajectoryPose80PositionY;

# define Rte_TypeDef_TrajectoryPose80Speed
typedef uint16 TrajectoryPose80Speed;

# define Rte_TypeDef_TrajectoryPose80Steering
typedef sint16 TrajectoryPose80Steering;

# define Rte_TypeDef_TrajectoryPose81Acceleration
typedef sint16 TrajectoryPose81Acceleration;

# define Rte_TypeDef_TrajectoryPose81Curvature
typedef uint16 TrajectoryPose81Curvature;

# define Rte_TypeDef_TrajectoryPose81Heading
typedef sint16 TrajectoryPose81Heading;

# define Rte_TypeDef_TrajectoryPose81PositionX
typedef sint32 TrajectoryPose81PositionX;

# define Rte_TypeDef_TrajectoryPose81PositionY
typedef sint32 TrajectoryPose81PositionY;

# define Rte_TypeDef_TrajectoryPose81Speed
typedef uint16 TrajectoryPose81Speed;

# define Rte_TypeDef_TrajectoryPose81Steering
typedef sint16 TrajectoryPose81Steering;

# define Rte_TypeDef_TrajectoryPose82Acceleration
typedef sint16 TrajectoryPose82Acceleration;

# define Rte_TypeDef_TrajectoryPose82Curvature
typedef uint16 TrajectoryPose82Curvature;

# define Rte_TypeDef_TrajectoryPose82Heading
typedef sint16 TrajectoryPose82Heading;

# define Rte_TypeDef_TrajectoryPose82PositionX
typedef sint32 TrajectoryPose82PositionX;

# define Rte_TypeDef_TrajectoryPose82PositionY
typedef sint32 TrajectoryPose82PositionY;

# define Rte_TypeDef_TrajectoryPose82Speed
typedef uint16 TrajectoryPose82Speed;

# define Rte_TypeDef_TrajectoryPose82Steering
typedef sint16 TrajectoryPose82Steering;

# define Rte_TypeDef_TrajectoryPose83Acceleration
typedef sint16 TrajectoryPose83Acceleration;

# define Rte_TypeDef_TrajectoryPose83Curvature
typedef uint16 TrajectoryPose83Curvature;

# define Rte_TypeDef_TrajectoryPose83Heading
typedef sint16 TrajectoryPose83Heading;

# define Rte_TypeDef_TrajectoryPose83PositionX
typedef sint32 TrajectoryPose83PositionX;

# define Rte_TypeDef_TrajectoryPose83PositionY
typedef sint32 TrajectoryPose83PositionY;

# define Rte_TypeDef_TrajectoryPose83Speed
typedef uint16 TrajectoryPose83Speed;

# define Rte_TypeDef_TrajectoryPose83Steering
typedef sint16 TrajectoryPose83Steering;

# define Rte_TypeDef_TrajectoryPose84Acceleration
typedef sint16 TrajectoryPose84Acceleration;

# define Rte_TypeDef_TrajectoryPose84Curvature
typedef uint16 TrajectoryPose84Curvature;

# define Rte_TypeDef_TrajectoryPose84Heading
typedef sint16 TrajectoryPose84Heading;

# define Rte_TypeDef_TrajectoryPose84PositionX
typedef sint32 TrajectoryPose84PositionX;

# define Rte_TypeDef_TrajectoryPose84PositionY
typedef sint32 TrajectoryPose84PositionY;

# define Rte_TypeDef_TrajectoryPose84Speed
typedef uint16 TrajectoryPose84Speed;

# define Rte_TypeDef_TrajectoryPose84Steering
typedef sint16 TrajectoryPose84Steering;

# define Rte_TypeDef_TrajectoryPose85Acceleration
typedef sint16 TrajectoryPose85Acceleration;

# define Rte_TypeDef_TrajectoryPose85Curvature
typedef uint16 TrajectoryPose85Curvature;

# define Rte_TypeDef_TrajectoryPose85Heading
typedef sint16 TrajectoryPose85Heading;

# define Rte_TypeDef_TrajectoryPose85PositionX
typedef sint32 TrajectoryPose85PositionX;

# define Rte_TypeDef_TrajectoryPose85PositionY
typedef sint32 TrajectoryPose85PositionY;

# define Rte_TypeDef_TrajectoryPose85Speed
typedef uint16 TrajectoryPose85Speed;

# define Rte_TypeDef_TrajectoryPose85Steering
typedef sint16 TrajectoryPose85Steering;

# define Rte_TypeDef_TrajectoryPose86Acceleration
typedef sint16 TrajectoryPose86Acceleration;

# define Rte_TypeDef_TrajectoryPose86Curvature
typedef uint16 TrajectoryPose86Curvature;

# define Rte_TypeDef_TrajectoryPose86Heading
typedef sint16 TrajectoryPose86Heading;

# define Rte_TypeDef_TrajectoryPose86PositionX
typedef sint32 TrajectoryPose86PositionX;

# define Rte_TypeDef_TrajectoryPose86PositionY
typedef sint32 TrajectoryPose86PositionY;

# define Rte_TypeDef_TrajectoryPose86Speed
typedef uint16 TrajectoryPose86Speed;

# define Rte_TypeDef_TrajectoryPose86Steering
typedef sint16 TrajectoryPose86Steering;

# define Rte_TypeDef_TrajectoryPose87Acceleration
typedef sint16 TrajectoryPose87Acceleration;

# define Rte_TypeDef_TrajectoryPose87Curvature
typedef uint16 TrajectoryPose87Curvature;

# define Rte_TypeDef_TrajectoryPose87Heading
typedef sint16 TrajectoryPose87Heading;

# define Rte_TypeDef_TrajectoryPose87PositionX
typedef sint32 TrajectoryPose87PositionX;

# define Rte_TypeDef_TrajectoryPose87PositionY
typedef sint32 TrajectoryPose87PositionY;

# define Rte_TypeDef_TrajectoryPose87Speed
typedef uint16 TrajectoryPose87Speed;

# define Rte_TypeDef_TrajectoryPose87Steering
typedef sint16 TrajectoryPose87Steering;

# define Rte_TypeDef_TrajectoryPose88Acceleration
typedef sint16 TrajectoryPose88Acceleration;

# define Rte_TypeDef_TrajectoryPose88Curvature
typedef uint16 TrajectoryPose88Curvature;

# define Rte_TypeDef_TrajectoryPose88Heading
typedef sint16 TrajectoryPose88Heading;

# define Rte_TypeDef_TrajectoryPose88PositionX
typedef sint32 TrajectoryPose88PositionX;

# define Rte_TypeDef_TrajectoryPose88PositionY
typedef sint32 TrajectoryPose88PositionY;

# define Rte_TypeDef_TrajectoryPose88Speed
typedef uint16 TrajectoryPose88Speed;

# define Rte_TypeDef_TrajectoryPose88Steering
typedef sint16 TrajectoryPose88Steering;

# define Rte_TypeDef_TrajectoryPose89Acceleration
typedef sint16 TrajectoryPose89Acceleration;

# define Rte_TypeDef_TrajectoryPose89Curvature
typedef uint16 TrajectoryPose89Curvature;

# define Rte_TypeDef_TrajectoryPose89Heading
typedef sint16 TrajectoryPose89Heading;

# define Rte_TypeDef_TrajectoryPose89PositionX
typedef sint32 TrajectoryPose89PositionX;

# define Rte_TypeDef_TrajectoryPose89PositionY
typedef sint32 TrajectoryPose89PositionY;

# define Rte_TypeDef_TrajectoryPose89Speed
typedef uint16 TrajectoryPose89Speed;

# define Rte_TypeDef_TrajectoryPose89Steering
typedef sint16 TrajectoryPose89Steering;

# define Rte_TypeDef_TrajectoryPose90Acceleration
typedef sint16 TrajectoryPose90Acceleration;

# define Rte_TypeDef_TrajectoryPose90Curvature
typedef uint16 TrajectoryPose90Curvature;

# define Rte_TypeDef_TrajectoryPose90Heading
typedef sint16 TrajectoryPose90Heading;

# define Rte_TypeDef_TrajectoryPose90PositionX
typedef sint32 TrajectoryPose90PositionX;

# define Rte_TypeDef_TrajectoryPose90PositionY
typedef sint32 TrajectoryPose90PositionY;

# define Rte_TypeDef_TrajectoryPose90Speed
typedef uint16 TrajectoryPose90Speed;

# define Rte_TypeDef_TrajectoryPose90Steering
typedef sint16 TrajectoryPose90Steering;

# define Rte_TypeDef_TrajectoryPose91Acceleration
typedef sint16 TrajectoryPose91Acceleration;

# define Rte_TypeDef_TrajectoryPose91Curvature
typedef uint16 TrajectoryPose91Curvature;

# define Rte_TypeDef_TrajectoryPose91Heading
typedef sint16 TrajectoryPose91Heading;

# define Rte_TypeDef_TrajectoryPose91PositionX
typedef sint32 TrajectoryPose91PositionX;

# define Rte_TypeDef_TrajectoryPose91PositionY
typedef sint32 TrajectoryPose91PositionY;

# define Rte_TypeDef_TrajectoryPose91Speed
typedef uint16 TrajectoryPose91Speed;

# define Rte_TypeDef_TrajectoryPose91Steering
typedef sint16 TrajectoryPose91Steering;

# define Rte_TypeDef_TrajectoryPose92Acceleration
typedef sint16 TrajectoryPose92Acceleration;

# define Rte_TypeDef_TrajectoryPose92Curvature
typedef uint16 TrajectoryPose92Curvature;

# define Rte_TypeDef_TrajectoryPose92Heading
typedef sint16 TrajectoryPose92Heading;

# define Rte_TypeDef_TrajectoryPose92PositionX
typedef sint32 TrajectoryPose92PositionX;

# define Rte_TypeDef_TrajectoryPose92PositionY
typedef sint32 TrajectoryPose92PositionY;

# define Rte_TypeDef_TrajectoryPose92Speed
typedef uint16 TrajectoryPose92Speed;

# define Rte_TypeDef_TrajectoryPose92Steering
typedef sint16 TrajectoryPose92Steering;

# define Rte_TypeDef_TrajectoryPose93Acceleration
typedef sint16 TrajectoryPose93Acceleration;

# define Rte_TypeDef_TrajectoryPose93Curvature
typedef uint16 TrajectoryPose93Curvature;

# define Rte_TypeDef_TrajectoryPose93Heading
typedef sint16 TrajectoryPose93Heading;

# define Rte_TypeDef_TrajectoryPose93PositionX
typedef sint32 TrajectoryPose93PositionX;

# define Rte_TypeDef_TrajectoryPose93PositionY
typedef sint32 TrajectoryPose93PositionY;

# define Rte_TypeDef_TrajectoryPose93Speed
typedef uint16 TrajectoryPose93Speed;

# define Rte_TypeDef_TrajectoryPose93Steering
typedef sint16 TrajectoryPose93Steering;

# define Rte_TypeDef_TrajectoryPose94Acceleration
typedef sint16 TrajectoryPose94Acceleration;

# define Rte_TypeDef_TrajectoryPose94Curvature
typedef uint16 TrajectoryPose94Curvature;

# define Rte_TypeDef_TrajectoryPose94Heading
typedef sint16 TrajectoryPose94Heading;

# define Rte_TypeDef_TrajectoryPose94PositionX
typedef sint32 TrajectoryPose94PositionX;

# define Rte_TypeDef_TrajectoryPose94PositionY
typedef sint32 TrajectoryPose94PositionY;

# define Rte_TypeDef_TrajectoryPose94Speed
typedef uint16 TrajectoryPose94Speed;

# define Rte_TypeDef_TrajectoryPose94Steering
typedef sint16 TrajectoryPose94Steering;

# define Rte_TypeDef_TrajectoryPose95Acceleration
typedef sint16 TrajectoryPose95Acceleration;

# define Rte_TypeDef_TrajectoryPose95Curvature
typedef uint16 TrajectoryPose95Curvature;

# define Rte_TypeDef_TrajectoryPose95Heading
typedef sint16 TrajectoryPose95Heading;

# define Rte_TypeDef_TrajectoryPose95PositionX
typedef sint32 TrajectoryPose95PositionX;

# define Rte_TypeDef_TrajectoryPose95PositionY
typedef sint32 TrajectoryPose95PositionY;

# define Rte_TypeDef_TrajectoryPose95Speed
typedef uint16 TrajectoryPose95Speed;

# define Rte_TypeDef_TrajectoryPose95Steering
typedef sint16 TrajectoryPose95Steering;

# define Rte_TypeDef_TrajectoryPose96Acceleration
typedef sint16 TrajectoryPose96Acceleration;

# define Rte_TypeDef_TrajectoryPose96Curvature
typedef uint16 TrajectoryPose96Curvature;

# define Rte_TypeDef_TrajectoryPose96Heading
typedef sint16 TrajectoryPose96Heading;

# define Rte_TypeDef_TrajectoryPose96PositionX
typedef sint32 TrajectoryPose96PositionX;

# define Rte_TypeDef_TrajectoryPose96PositionY
typedef sint32 TrajectoryPose96PositionY;

# define Rte_TypeDef_TrajectoryPose96Speed
typedef uint16 TrajectoryPose96Speed;

# define Rte_TypeDef_TrajectoryPose96Steering
typedef sint16 TrajectoryPose96Steering;

# define Rte_TypeDef_TrajectoryPose97Acceleration
typedef sint16 TrajectoryPose97Acceleration;

# define Rte_TypeDef_TrajectoryPose97Curvature
typedef uint16 TrajectoryPose97Curvature;

# define Rte_TypeDef_TrajectoryPose97Heading
typedef sint16 TrajectoryPose97Heading;

# define Rte_TypeDef_TrajectoryPose97PositionX
typedef sint32 TrajectoryPose97PositionX;

# define Rte_TypeDef_TrajectoryPose97PositionY
typedef sint32 TrajectoryPose97PositionY;

# define Rte_TypeDef_TrajectoryPose97Speed
typedef uint16 TrajectoryPose97Speed;

# define Rte_TypeDef_TrajectoryPose97Steering
typedef sint16 TrajectoryPose97Steering;

# define Rte_TypeDef_TrajectoryPose98Acceleration
typedef sint16 TrajectoryPose98Acceleration;

# define Rte_TypeDef_TrajectoryPose98Curvature
typedef uint16 TrajectoryPose98Curvature;

# define Rte_TypeDef_TrajectoryPose98Heading
typedef sint16 TrajectoryPose98Heading;

# define Rte_TypeDef_TrajectoryPose98PositionX
typedef sint32 TrajectoryPose98PositionX;

# define Rte_TypeDef_TrajectoryPose98PositionY
typedef sint32 TrajectoryPose98PositionY;

# define Rte_TypeDef_TrajectoryPose98Speed
typedef uint16 TrajectoryPose98Speed;

# define Rte_TypeDef_TrajectoryPose98Steering
typedef sint16 TrajectoryPose98Steering;

# define Rte_TypeDef_TrajectoryPose99Acceleration
typedef sint16 TrajectoryPose99Acceleration;

# define Rte_TypeDef_TrajectoryPose99Curvature
typedef uint16 TrajectoryPose99Curvature;

# define Rte_TypeDef_TrajectoryPose99Heading
typedef sint16 TrajectoryPose99Heading;

# define Rte_TypeDef_TrajectoryPose99PositionX
typedef sint32 TrajectoryPose99PositionX;

# define Rte_TypeDef_TrajectoryPose99PositionY
typedef sint32 TrajectoryPose99PositionY;

# define Rte_TypeDef_TrajectoryPose99Speed
typedef uint16 TrajectoryPose99Speed;

# define Rte_TypeDef_TrajectoryPose99Steering
typedef sint16 TrajectoryPose99Steering;

# define Rte_TypeDef_TrsmFltIndcn
typedef uint8 TrsmFltIndcn;

# define Rte_TypeDef_UDcDcAvlLoSideExt
typedef uint8 UDcDcAvlLoSideExt;

# define Rte_TypeDef_VehControlStatus
typedef uint8 VehControlStatus;

# define Rte_TypeDef_VehManDeactvnReqInProgs1
typedef uint8 VehManDeactvnReqInProgs1;

# define Rte_TypeDef_VehMid3SsmCounter0MessageID
typedef uint16 VehMid3SsmCounter0MessageID;

# define Rte_TypeDef_VehMid3SsmCounter0Timeout
typedef boolean VehMid3SsmCounter0Timeout;

# define Rte_TypeDef_VehMid3SsmCounter0Timer
typedef uint16 VehMid3SsmCounter0Timer;

# define Rte_TypeDef_VehMid3SsmCounter1MessageID
typedef uint16 VehMid3SsmCounter1MessageID;

# define Rte_TypeDef_VehMid3SsmCounter1Timeout
typedef boolean VehMid3SsmCounter1Timeout;

# define Rte_TypeDef_VehMid3SsmCounter1Timer
typedef uint16 VehMid3SsmCounter1Timer;

# define Rte_TypeDef_VehMid3VcuCounter0MessageID
typedef uint16 VehMid3VcuCounter0MessageID;

# define Rte_TypeDef_VehMid3VcuCounter0Timeout
typedef boolean VehMid3VcuCounter0Timeout;

# define Rte_TypeDef_VehMid3VcuCounter0Timer
typedef uint16 VehMid3VcuCounter0Timer;

# define Rte_TypeDef_VehMid3VcuCounter1MessageID
typedef uint16 VehMid3VcuCounter1MessageID;

# define Rte_TypeDef_VehMid3VcuCounter1Timeout
typedef boolean VehMid3VcuCounter1Timeout;

# define Rte_TypeDef_VehMid3VcuCounter1Timer
typedef uint16 VehMid3VcuCounter1Timer;

# define Rte_TypeDef_VehMid5SsmCounter0MessageID
typedef uint16 VehMid5SsmCounter0MessageID;

# define Rte_TypeDef_VehMid5SsmCounter0Timeout
typedef boolean VehMid5SsmCounter0Timeout;

# define Rte_TypeDef_VehMid5SsmCounter0Timer
typedef uint16 VehMid5SsmCounter0Timer;

# define Rte_TypeDef_VehMid5SsmCounter1MessageID
typedef uint16 VehMid5SsmCounter1MessageID;

# define Rte_TypeDef_VehMid5SsmCounter1Timeout
typedef boolean VehMid5SsmCounter1Timeout;

# define Rte_TypeDef_VehMid5SsmCounter1Timer
typedef uint16 VehMid5SsmCounter1Timer;

# define Rte_TypeDef_VehMid6SsmCounter0MessageID
typedef uint16 VehMid6SsmCounter0MessageID;

# define Rte_TypeDef_VehMid6SsmCounter0Timeout
typedef boolean VehMid6SsmCounter0Timeout;

# define Rte_TypeDef_VehMid6SsmCounter0Timer
typedef uint16 VehMid6SsmCounter0Timer;

# define Rte_TypeDef_VehMid6SsmCounter1MessageID
typedef uint16 VehMid6SsmCounter1MessageID;

# define Rte_TypeDef_VehMid6SsmCounter1Timeout
typedef boolean VehMid6SsmCounter1Timeout;

# define Rte_TypeDef_VehMid6SsmCounter1Timer
typedef uint16 VehMid6SsmCounter1Timer;

# define Rte_TypeDef_VehUsgSt
typedef uint8 VehUsgSt;

# define Rte_TypeDef_VehUsgStReq
typedef uint8 VehUsgStReq;

# define Rte_TypeDef_WdgM_CheckpointIdType
typedef uint16 WdgM_CheckpointIdType;

# define Rte_TypeDef_WdgM_GlobalStatusType
typedef uint8 WdgM_GlobalStatusType;

# define Rte_TypeDef_WdgM_LocalStatusType
typedef uint8 WdgM_LocalStatusType;

# define Rte_TypeDef_WdgM_ModeType
typedef uint8 WdgM_ModeType;

# define Rte_TypeDef_WdgM_SupervisedEntityIdType
typedef uint16 WdgM_SupervisedEntityIdType;

# define Rte_TypeDef_WhlLockStsLockSts_C
typedef uint8 WhlLockStsLockSts_C;

# define Rte_TypeDef_WhlMotSysCluSts
typedef boolean WhlMotSysCluSts;

# define Rte_TypeDef_WinWipgAutCmdIf
typedef uint8 WinWipgAutCmdIf;

# define Rte_TypeDef_WipgFrntAutModIf
typedef uint8 WipgFrntAutModIf;

# define Rte_TypeDef_WiprActvIf
typedef boolean WiprActvIf;

# define Rte_TypeDef_WshngCycActvIf
typedef boolean WshngCycActvIf;

# define Rte_TypeDef_WshrFldLvlContnsIf
typedef uint8 WshrFldLvlContnsIf;

# define Rte_TypeDef_YRS1_AliveCounter
typedef uint8 YRS1_AliveCounter;

# define Rte_TypeDef_YRS1_Checksum
typedef uint8 YRS1_Checksum;

# define Rte_TypeDef_YRS2_Checksum
typedef uint8 YRS2_Checksum;

# define Rte_TypeDef_YRS_AliveCounter
typedef uint8 YRS_AliveCounter;

# define Rte_TypeDef_YRS_LateralAcce
typedef uint16 YRS_LateralAcce;

# define Rte_TypeDef_YRS_LateralSensorState
typedef uint8 YRS_LateralSensorState;

# define Rte_TypeDef_YRS_LongitAcce
typedef uint16 YRS_LongitAcce;

# define Rte_TypeDef_YRS_LongitSensorState
typedef uint8 YRS_LongitSensorState;

# define Rte_TypeDef_YRS_YawRate
typedef uint16 YRS_YawRate;

# define Rte_TypeDef_YRS_YawRateSensorState
typedef uint8 YRS_YawRateSensorState;

# define Rte_TypeDef_YawRate1Qf1_C
typedef uint8 YawRate1Qf1_C;

# define Rte_TypeDef_YawRate1_C
typedef sint16 YawRate1_C;

# define Rte_TypeDef_Rte_DT_TrajectoryInput_1
typedef TrajectoryPose Rte_DT_TrajectoryInput_1[100];

# define Rte_TypeDef_AcuControl
typedef struct
{
  uint32 timestamp_ms;
  CalibratedVehicleParams calibrated_vehicle_params;
} AcuControl;

# define Rte_TypeDef_EstimationDebug
typedef struct
{
  uint64 timestamp_ms;
  EstimationStateDebug state;
  Rte_DT_EstimationDebug_2 state_type;
  boolean is_stationary;
  Float sideslip_angle;
  EstimationStateDebug dead_reckoning_state;
} EstimationDebug;

# define Rte_TypeDef_GnssPoseInput
typedef struct
{
  uint32 timestamp_ms;
  float32 x;
  float32 y;
  float32 yaw;
  float32 heading;
  Rte_DT_GnssPoseInput_5 pos_type;
  Rte_DT_GnssPoseInput_6 heading_type;
  uint8 num_sats_tracked;
  float32 velocity_x;
  float32 velocity_y;
} GnssPoseInput;

# define Rte_TypeDef_Intention
typedef struct
{
  Rte_DT_Intention_0 motion;
  Rte_DT_Intention_1 lane_change;
  boolean horn;
  boolean hazard_light;
} Intention;

# define Rte_TypeDef_LGT_CtrlCmd_Struct
typedef struct
{
  uint8 LGT_SystemState;
  Float LGT_AccRequest;
  Float LGT_JerkRequest;
  VSP_VehicleSignal_Struct VSP_VehicleSignal;
  OPS_ObjectList_Struct OPS_ObjectList;
  CDS_CollisionDetection_Struct CDS_CollisionDetection;
  FSC_FreeStopControl_Struct FSC_FreeStopControl;
} LGT_CtrlCmd_Struct;

# define Rte_TypeDef_LateralPIDDebug
typedef struct
{
  PIDDebug pid_distance;
  PIDDebug pid_heading;
  float32 steering_feedforward;
  float32 steering_feedback;
} LateralPIDDebug;

# define Rte_TypeDef_LongitudinalPIDDebug
typedef struct
{
  PIDDebug pid_distance;
  PIDDebug pid_speed;
  float32 feedback_acceleration;
  float32 feedforward_acceleration;
} LongitudinalPIDDebug;

# define Rte_TypeDef_SG_ALnchTiDly3
typedef struct
{
  Rte_DT_SG_ALnchTiDly3_0 ALnchTiDly3Qf;
  uint8 ALnchTiDly3Sec2;
} SG_ALnchTiDly3;

# define Rte_TypeDef_SG_AccrPedlPsd
typedef struct
{
  Rte_DT_SG_AccrPedlPsd_0 AccrPedlPsdAccrPedlPsd;
  uint8 AccrPedlPsdAccrPedlPsdChks;
  uint8 AccrPedlPsdAccrPedlPsdCntr;
  Rte_DT_SG_AccrPedlPsd_3 AccrPedlPsdAccrPedlPsdSts;
} SG_AccrPedlPsd;

# define Rte_TypeDef_SG_AdDirReq
typedef struct
{
  uint8 AdDirReqChks;
  uint8 AdDirReqCntr;
  Rte_DT_SG_AdDirReq_2 AdDirReqDirReq;
} SG_AdDirReq;

# define Rte_TypeDef_SG_AdFusedFricEstimn
typedef struct
{
  uint8 AdFusedFricEstimnFricEstimn;
  Rte_DT_SG_AdFusedFricEstimn_1 AdFusedFricEstimnFricEstimnConf;
} SG_AdFusedFricEstimn;

# define Rte_TypeDef_SG_AdPrimSafeStopActvGroupSafe
typedef struct
{
  uint8 AdPrimSafeStopActvGroupSafeChks;
  uint8 AdPrimSafeStopActvGroupSafeCntr;
  Rte_DT_SG_AdPrimSafeStopActvGroupSafe_2 AdPrimSafeStopActvGroupSafePlatformSafeStopActv;
} SG_AdPrimSafeStopActvGroupSafe;

# define Rte_TypeDef_SG_AdPrimSteerStsSafeGroup
typedef struct
{
  Rte_DT_SG_AdPrimSteerStsSafeGroup_0 AdPrimSteerStsSafeGroupAdSteerPerf;
  Rte_DT_SG_AdPrimSteerStsSafeGroup_1 AdPrimSteerStsSafeGroupAdSteerSts;
  uint8 AdPrimSteerStsSafeGroupChks;
  uint8 AdPrimSteerStsSafeGroupCntr;
} SG_AdPrimSteerStsSafeGroup;

# define Rte_TypeDef_SG_AdPrimWhlAgEstimdGroupSafe
typedef struct
{
  uint8 AdPrimWhlAgEstimdGroupSafeChks;
  uint8 AdPrimWhlAgEstimdGroupSafeCntr;
  Rte_DT_SG_AdPrimWhlAgEstimdGroupSafe_2 AdPrimWhlAgEstimdGroupSafeQf1;
  uint16 AdPrimWhlAgEstimdGroupSafeWhlAg;
  uint16 AdPrimWhlAgEstimdGroupSafeWhlAgRate;
} SG_AdPrimWhlAgEstimdGroupSafe;

# define Rte_TypeDef_SG_AdSecBlindStopMonActvnGroupSaf
typedef struct
{
  Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_0 AdSecBlindStopMonActvnGroupSafActvnReq;
  uint8 AdSecBlindStopMonActvnGroupSafChks;
  uint8 AdSecBlindStopMonActvnGroupSafCntr;
  Rte_DT_SG_AdSecBlindStopMonActvnGroupSaf_3 AdSecBlindStopMonActvnGroupSafDeactvnReq;
} SG_AdSecBlindStopMonActvnGroupSaf;

# define Rte_TypeDef_SG_AdSecBrkActvnGroupSafe
typedef struct
{
  Rte_DT_SG_AdSecBrkActvnGroupSafe_0 AdSecBrkActvnGroupSafeActvnReq;
  uint8 AdSecBrkActvnGroupSafeChks;
  uint8 AdSecBrkActvnGroupSafeCntr;
  Rte_DT_SG_AdSecBrkActvnGroupSafe_3 AdSecBrkActvnGroupSafeDeactvnReq;
} SG_AdSecBrkActvnGroupSafe;

# define Rte_TypeDef_SG_AdSecPahStsGroupSafe
typedef struct
{
  uint8 AdSecPahStsGroupSafeChks;
  uint8 AdSecPahStsGroupSafeCntr;
  Rte_DT_SG_AdSecPahStsGroupSafe_2 AdSecPahStsGroupSafeLatCtrlSupervisedMode;
  Rte_DT_SG_AdSecPahStsGroupSafe_3 AdSecPahStsGroupSafePahOk1;
  Rte_DT_SG_AdSecPahStsGroupSafe_4 AdSecPahStsGroupSafePahOk2;
} SG_AdSecPahStsGroupSafe;

# define Rte_TypeDef_SG_AdSecSafeStopActvGroupSafe
typedef struct
{
  uint8 AdSecSafeStopActvGroupSafeChks;
  uint8 AdSecSafeStopActvGroupSafeCntr;
  Rte_DT_SG_AdSecSafeStopActvGroupSafe_2 AdSecSafeStopActvGroupSafePlatformSafeStopActv;
} SG_AdSecSafeStopActvGroupSafe;

# define Rte_TypeDef_SG_AdSecSteerActvnGroupSafe
typedef struct
{
  Rte_DT_SG_AdSecSteerActvnGroupSafe_0 AdSecSteerActvnGroupSafeAdSteerActvnReq;
  Rte_DT_SG_AdSecSteerActvnGroupSafe_1 AdSecSteerActvnGroupSafeAdSteerDeActvnReq;
  uint8 AdSecSteerActvnGroupSafeChks;
  uint8 AdSecSteerActvnGroupSafeCntr;
} SG_AdSecSteerActvnGroupSafe;

# define Rte_TypeDef_SG_AdSecSteerModStsSafeGroupByGat
typedef struct
{
  Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_0 AdSecSteerModStsSafeGroupByGatAdSteerMod;
  uint8 AdSecSteerModStsSafeGroupByGatChks;
  uint8 AdSecSteerModStsSafeGroupByGatCntr;
  Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_3 AdSecSteerModStsSafeGroupByGatVehOperIntv;
  Rte_DT_SG_AdSecSteerModStsSafeGroupByGat_4 AdSecSteerModStsSafeGroupByGatVehOperModSts1;
} SG_AdSecSteerModStsSafeGroupByGat;

# define Rte_TypeDef_SG_AdSecSteerStsSafeGroup
typedef struct
{
  Rte_DT_SG_AdSecSteerStsSafeGroup_0 AdSecSteerStsSafeGroupAdSteerPerf;
  Rte_DT_SG_AdSecSteerStsSafeGroup_1 AdSecSteerStsSafeGroupAdSteerSts;
  uint8 AdSecSteerStsSafeGroupChks;
  uint8 AdSecSteerStsSafeGroupCntr;
} SG_AdSecSteerStsSafeGroup;

# define Rte_TypeDef_SG_AdSecSteerStsSafeGroupByGatewy
typedef struct
{
  Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_0 AdSecSteerStsSafeGroupByGatewyAdSteerPerf;
  Rte_DT_SG_AdSecSteerStsSafeGroupByGatewy_1 AdSecSteerStsSafeGroupByGatewyAdSteerSts;
  uint8 AdSecSteerStsSafeGroupByGatewyChks;
  uint8 AdSecSteerStsSafeGroupByGatewyCntr;
} SG_AdSecSteerStsSafeGroupByGatewy;

# define Rte_TypeDef_SG_AdStandStillReq
typedef struct
{
  uint8 AdStandStillReqChks;
  uint8 AdStandStillReqCntr;
  Rte_DT_SG_AdStandStillReq_2 AdStandStillReqReq;
} SG_AdStandStillReq;

# define Rte_TypeDef_SG_AdSteerPaddlPsdGroupSafe
typedef struct
{
  uint8 AdSteerPaddlPsdGroupSafeChks;
  uint8 AdSteerPaddlPsdGroupSafeCntr;
  Rte_DT_SG_AdSteerPaddlPsdGroupSafe_2 AdSteerPaddlPsdGroupSafeLe;
  Rte_DT_SG_AdSteerPaddlPsdGroupSafe_3 AdSteerPaddlPsdGroupSafeLeSts;
  Rte_DT_SG_AdSteerPaddlPsdGroupSafe_4 AdSteerPaddlPsdGroupSafeRi;
  Rte_DT_SG_AdSteerPaddlPsdGroupSafe_5 AdSteerPaddlPsdGroupSafeRiSts;
} SG_AdSteerPaddlPsdGroupSafe;

# define Rte_TypeDef_SG_AdWhlLockReq
typedef struct
{
  uint8 AdWhlLockReqChks;
  uint8 AdWhlLockReqCntr;
  Rte_DT_SG_AdWhlLockReq_2 AdWhlLockReqNoReqApplyRel;
} SG_AdWhlLockReq;

# define Rte_TypeDef_SG_AdpLiReqFromAPI
typedef struct
{
  Rte_DT_SG_AdpLiReqFromAPI_0 AdpLiReqFromAPIAhbcActvn;
  uint8 AdpLiReqFromAPIChks;
  uint8 AdpLiReqFromAPICntr;
  Rte_DT_SG_AdpLiReqFromAPI_3 AdpLiReqFromAPIHzrdLiActvnReq;
  Rte_DT_SG_AdpLiReqFromAPI_4 AdpLiReqFromAPIHzrdLiDeactnReq;
  Rte_DT_SG_AdpLiReqFromAPI_5 AdpLiReqFromAPIIncrLiRiReq;
  Rte_DT_SG_AdpLiReqFromAPI_6 AdpLiReqFromAPIIndcrLeReq;
} SG_AdpLiReqFromAPI;

# define Rte_TypeDef_SG_AgDataRawSafe
typedef struct
{
  uint8 AgDataRawSafeChks;
  uint8 AgDataRawSafeCntr;
  sint16 RollRate1;
  Rte_DT_SG_AgDataRawSafe_3 RollRate1Qf1;
  sint16 YawRate1;
  Rte_DT_SG_AgDataRawSafe_5 YawRate1Qf1;
} SG_AgDataRawSafe;

# define Rte_TypeDef_SG_AlrmSts1
typedef struct
{
  Rte_DT_SG_AlrmSts1_0 AlrmSt;
  Rte_DT_SG_AlrmSts1_1 AlrmTrgSrc;
  Rte_DT_SG_AlrmSts1_2 SnsrInclnFailr;
  Rte_DT_SG_AlrmSts1_3 SnsrIntrScanrFailr;
  Rte_DT_SG_AlrmSts1_4 SnsrSoundrBattBackedFailr;
} SG_AlrmSts1;

# define Rte_TypeDef_SG_AutnmsDrvModMngtExtSafe
typedef struct
{
  Rte_DT_SG_AutnmsDrvModMngtExtSafe_0 AutnmsDrvModMngtExtSafeAutnmsDrvModSts1;
  uint8 AutnmsDrvModMngtExtSafeChecksum;
  uint8 AutnmsDrvModMngtExtSafeCounter;
  Rte_DT_SG_AutnmsDrvModMngtExtSafe_3 AutnmsDrvModMngtExtSafeVehOperModSts1;
} SG_AutnmsDrvModMngtExtSafe;

# define Rte_TypeDef_SG_AutnmsDrvModMngtGlbSafe1
typedef struct
{
  Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_0 AutnmsDrvModMngtGlbSafe1AutnmsDrvModSts1;
  Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_1 AutnmsDrvModMngtGlbSafe1AutnmsLowSpdModSts1;
  uint8 AutnmsDrvModMngtGlbSafe1Checksum;
  uint8 AutnmsDrvModMngtGlbSafe1Counter;
  Rte_DT_SG_AutnmsDrvModMngtGlbSafe1_4 AutnmsDrvModMngtGlbSafe1VehOperModSts1;
} SG_AutnmsDrvModMngtGlbSafe1;

# define Rte_TypeDef_SG_AutnmsDrvStReq
typedef struct
{
  Rte_DT_SG_AutnmsDrvStReq_0 AutnmsDrvStReqAutnmsDrvStReq;
  uint8 AutnmsDrvStReqChecksum;
  uint8 AutnmsDrvStReqCounter;
} SG_AutnmsDrvStReq;

# define Rte_TypeDef_SG_AutnmsDrvStReq_A
typedef struct
{
  Rte_DT_SG_AutnmsDrvStReq_A_0 AutnmsDrvStReqAutnmsDrvStReq_A;
  uint8 AutnmsDrvStReqChecksum_A;
  uint8 AutnmsDrvStReqCounter_A;
} SG_AutnmsDrvStReq_A;

# define Rte_TypeDef_SG_BltLockStAtDrvr
typedef struct
{
  Rte_DT_SG_BltLockStAtDrvr_0 BltLockStAtDrvrForBltLockSt1;
  Rte_DT_SG_BltLockStAtDrvr_1 BltLockStAtDrvrForDevErrSts2;
} SG_BltLockStAtDrvr;

# define Rte_TypeDef_SG_BltLockStAtPass
typedef struct
{
  Rte_DT_SG_BltLockStAtPass_0 BltLockStAtPassForBltLockSt1;
  Rte_DT_SG_BltLockStAtPass_1 BltLockStAtPassForBltLockSts;
} SG_BltLockStAtPass;

# define Rte_TypeDef_SG_BltLockStAtRowSecLe
typedef struct
{
  Rte_DT_SG_BltLockStAtRowSecLe_0 BltLockStAtRowSecLeForBltLockEquid;
  Rte_DT_SG_BltLockStAtRowSecLe_1 BltLockStAtRowSecLeForBltLockSt1;
  Rte_DT_SG_BltLockStAtRowSecLe_2 BltLockStAtRowSecLeForBltLockSts;
} SG_BltLockStAtRowSecLe;

# define Rte_TypeDef_SG_BltLockStAtRowSecRi
typedef struct
{
  Rte_DT_SG_BltLockStAtRowSecRi_0 BltLockStAtRowSecRiForBltLockEquid;
  Rte_DT_SG_BltLockStAtRowSecRi_1 BltLockStAtRowSecRiForBltLockSt1;
  Rte_DT_SG_BltLockStAtRowSecRi_2 BltLockStAtRowSecRiForBltLockSts;
} SG_BltLockStAtRowSecRi;

# define Rte_TypeDef_SG_BrkPedlPsdSafeGroup
typedef struct
{
  Rte_DT_SG_BrkPedlPsdSafeGroup_0 BrkPedlNotPsdSafe;
  Rte_DT_SG_BrkPedlPsdSafeGroup_1 BrkPedlPsd;
  uint8 BrkPedlPsdChks;
  uint8 BrkPedlPsdCntr;
  Rte_DT_SG_BrkPedlPsdSafeGroup_4 BrkPedlPsdQf;
} SG_BrkPedlPsdSafeGroup;

# define Rte_TypeDef_SG_BrkPedlVal
typedef struct
{
  uint16 BrkPedlValBrkPedlVal;
  Rte_DT_SG_BrkPedlVal_1 BrkPedlValBrkPedlValQf;
} SG_BrkPedlVal;

# define Rte_TypeDef_SG_BrkTqMinReq
typedef struct
{
  Rte_DT_SG_BrkTqMinReq_0 BrkTqMinReqBrkActrCtrlModForMinTqReq;
  uint16 BrkTqMinReqBrkTqGrdtNegMinReq;
  uint16 BrkTqMinReqBrkTqGrdtPosMinReq;
  uint16 BrkTqMinReqBrkTqMinReq;
  uint8 BrkTqMinReqBrkTqMinReqChks;
  uint8 BrkTqMinReqBrkTqMinReqCntr;
} SG_BrkTqMinReq;

# define Rte_TypeDef_SG_CarModInCrashStsSafe
typedef struct
{
  uint8 CarModInCrashStsSafeChks;
  uint8 CarModInCrashStsSafeCntr;
  Rte_DT_SG_CarModInCrashStsSafe_2 CarModInCrashStsSafeSts;
} SG_CarModInCrashStsSafe;

# define Rte_TypeDef_SG_CllsnAidSnvtySeld
typedef struct
{
  Rte_DT_SG_CllsnAidSnvtySeld_0 CllsnAidSnvtySeldPen;
  Rte_DT_SG_CllsnAidSnvtySeld_1 CllsnAidSnvtySeldSts;
} SG_CllsnAidSnvtySeld;

# define Rte_TypeDef_SG_ClstrSts1ForAutnmsDrv
typedef struct
{
  uint8 ClstrSts1ForAutnmsDrvClstr1Chks;
  uint8 ClstrSts1ForAutnmsDrvClstr1Cnt;
  Rte_DT_SG_ClstrSts1ForAutnmsDrv_2 ClstrSts1ForAutnmsDrvClstr1Sts;
} SG_ClstrSts1ForAutnmsDrv;

# define Rte_TypeDef_SG_ClstrSts2ForAutnmsDrv
typedef struct
{
  uint8 ClstrSts2ForAutnmsDrvClstr2Chks;
  uint8 ClstrSts2ForAutnmsDrvClstr2Cnt;
  Rte_DT_SG_ClstrSts2ForAutnmsDrv_2 ClstrSts2ForAutnmsDrvClstr2Sts;
} SG_ClstrSts2ForAutnmsDrv;

# define Rte_TypeDef_SG_DoorPassRePosnStsToAPI
typedef struct
{
  Rte_DT_SG_DoorPassRePosnStsToAPI_0 DoorPassRePosnStsToAPIPassReDoorPosnSts;
  uint8 DoorPassRePosnStsToAPIPassReDoorPosnStsChks;
  uint8 DoorPassRePosnStsToAPIPassReDoorPosnStsCntr;
} SG_DoorPassRePosnStsToAPI;

# define Rte_TypeDef_SG_DrvrGearShiftManReq
typedef struct
{
  Rte_DT_SG_DrvrGearShiftManReq_0 DrvrGearShiftManReqDwnTipReq;
  Rte_DT_SG_DrvrGearShiftManReq_1 DrvrGearShiftManReqSteerWhlDwnReq;
  Rte_DT_SG_DrvrGearShiftManReq_2 DrvrGearShiftManReqSteerWhlUpReq;
  Rte_DT_SG_DrvrGearShiftManReq_3 DrvrGearShiftManReqTipReq;
  Rte_DT_SG_DrvrGearShiftManReq_4 DrvrGearShiftManReqUpTipReq;
} SG_DrvrGearShiftManReq;

# define Rte_TypeDef_SG_DrvrIntvSts
typedef struct
{
  Rte_DT_SG_DrvrIntvSts_0 DrvrIntvStsDrvrIntvByAccrPedl;
  Rte_DT_SG_DrvrIntvSts_1 DrvrIntvStsDrvrIntvByBrkPedl;
  Rte_DT_SG_DrvrIntvSts_2 DrvrIntvStsDrvrIntvBySteering;
} SG_DrvrIntvSts;

# define Rte_TypeDef_SG_DrvrPrsntGroup
typedef struct
{
  Rte_DT_SG_DrvrPrsntGroup_0 DrvrPrsnt;
  Rte_DT_SG_DrvrPrsntGroup_1 DrvrPrsntQf;
  uint8 DrvrPrsntStsDrvrPrsntChks;
  uint8 DrvrPrsntStsDrvrPrsntCntr;
} SG_DrvrPrsntGroup;

# define Rte_TypeDef_SG_EngFltIndcn
typedef struct
{
  Rte_DT_SG_EngFltIndcn_0 EngFltIndcnEngFltIndcn1;
  Rte_DT_SG_EngFltIndcn_1 EngFltIndcnEngFltIndcn2;
  Rte_DT_SG_EngFltIndcn_2 EngFltIndcnEngFltIndcn3;
  Rte_DT_SG_EngFltIndcn_3 EngFltIndcnEngFltIndcn4;
  Rte_DT_SG_EngFltIndcn_4 EngFltIndcnEngFltIndcn5;
  Rte_DT_SG_EngFltIndcn_5 EngFltIndcnEngFltIndcn6;
  Rte_DT_SG_EngFltIndcn_6 EngFltIndcnEngFltIndcn7;
  Rte_DT_SG_EngFltIndcn_7 EngFltIndcnEngFltIndcn8;
  Rte_DT_SG_EngFltIndcn_8 EngFltIndcnEngFltIndcn9;
  Rte_DT_SG_EngFltIndcn_9 EngFltIndcnEngFltIndcn10;
  Rte_DT_SG_EngFltIndcn_10 EngFltIndcnEngFltIndcn11;
  Rte_DT_SG_EngFltIndcn_11 EngFltIndcnEngFltIndcn12;
  Rte_DT_SG_EngFltIndcn_12 EngFltIndcnEngFltIndcn13;
  Rte_DT_SG_EngFltIndcn_13 EngFltIndcnEngFltIndcn14;
  Rte_DT_SG_EngFltIndcn_14 EngFltIndcnEngFltIndcn15;
  Rte_DT_SG_EngFltIndcn_15 EngFltIndcnEngFltIndcn16;
  Rte_DT_SG_EngFltIndcn_16 EngFltIndcnEngFltIndcn17;
  Rte_DT_SG_EngFltIndcn_17 EngFltIndcnEngFltIndcn18;
  Rte_DT_SG_EngFltIndcn_18 EngFltIndcnEngFltIndcn19;
  Rte_DT_SG_EngFltIndcn_19 EngFltIndcnEngFltIndcn20;
  Rte_DT_SG_EngFltIndcn_20 EngFltIndcnEngFltIndcn21;
  Rte_DT_SG_EngFltIndcn_21 EngFltIndcnEngFltIndcn22;
  Rte_DT_SG_EngFltIndcn_22 EngFltIndcnEngFltIndcn23;
  Rte_DT_SG_EngFltIndcn_23 EngFltIndcnEngFltIndcn24;
  Rte_DT_SG_EngFltIndcn_24 EngFltIndcnEngFltIndcn25;
  Rte_DT_SG_EngFltIndcn_25 EngFltIndcnEngFltIndcn26;
  Rte_DT_SG_EngFltIndcn_26 EngFltIndcnEngFltIndcn27;
} SG_EngFltIndcn;

# define Rte_TypeDef_SG_FricEstimnFromVehDynGroup
typedef struct
{
  uint8 FricEstimnFromVehDyn;
  Rte_DT_SG_FricEstimnFromVehDynGroup_1 FricEstimnFromVehDynQly;
} SG_FricEstimnFromVehDynGroup;

# define Rte_TypeDef_SG_HmiAutnmsSts
typedef struct
{
  uint8 HmiAutnmsStsChecksum;
  uint8 HmiAutnmsStsCounter;
  Rte_DT_SG_HmiAutnmsSts_2 HmiAutnmsStsHmiAutnmsSts;
} SG_HmiAutnmsSts;

# define Rte_TypeDef_SG_OvrdDecelByDrvr
typedef struct
{
  Rte_DT_SG_OvrdDecelByDrvr_0 OvrdDecelByDrvrOvrdDecelByDrvr;
  uint8 OvrdDecelByDrvrOvrdDecelByDrvrChks;
  uint8 OvrdDecelByDrvrOvrdDecelByDrvrCntr;
} SG_OvrdDecelByDrvr;

# define Rte_TypeDef_SG_PrimALatDataRawSafe
typedef struct
{
  uint8 PrimALatDataRawSafeChks;
  uint8 PrimALatDataRawSafeCntr;
  uint16 PrimALatDataRawSafeMax;
  uint16 PrimALatDataRawSafeMin;
  Rte_DT_SG_PrimALatDataRawSafe_4 PrimALatDataRawSafeMinMaxQf;
  uint16 PrimALatDataRawSafeNom;
  Rte_DT_SG_PrimALatDataRawSafe_6 PrimALatDataRawSafeNomQf;
} SG_PrimALatDataRawSafe;

# define Rte_TypeDef_SG_PrimALgtDataRawSafe
typedef struct
{
  uint8 PrimALgtDataRawSafeChks;
  uint8 PrimALgtDataRawSafeCntr;
  uint16 PrimALgtDataRawSafeMax;
  uint16 PrimALgtDataRawSafeMin;
  Rte_DT_SG_PrimALgtDataRawSafe_4 PrimALgtDataRawSafeMinMaxQf;
  uint16 PrimALgtDataRawSafeNom;
  Rte_DT_SG_PrimALgtDataRawSafe_6 PrimALgtDataRawSafeNomQf;
} SG_PrimALgtDataRawSafe;

# define Rte_TypeDef_SG_PrimAxleSlipStsAndRelAg
typedef struct
{
  Rte_DT_SG_PrimAxleSlipStsAndRelAg_0 PrimAxleSlipStsAndRelAgFrntAxle;
  Rte_DT_SG_PrimAxleSlipStsAndRelAg_1 PrimAxleSlipStsAndRelAgIntvSts;
  Rte_DT_SG_PrimAxleSlipStsAndRelAg_2 PrimAxleSlipStsAndRelAgReAxle;
  sint16 PrimAxleSlipStsAndRelAgRelPitch;
  Rte_DT_SG_PrimAxleSlipStsAndRelAg_4 PrimAxleSlipStsAndRelAgRelPitchQf;
  sint16 PrimAxleSlipStsAndRelAgRelRoll;
  Rte_DT_SG_PrimAxleSlipStsAndRelAg_6 PrimAxleSlipStsAndRelAgRelRolQf;
} SG_PrimAxleSlipStsAndRelAg;

# define Rte_TypeDef_SG_PrimVLatSafe
typedef struct
{
  uint8 PrimVLatSafeChks;
  uint8 PrimVLatSafeCntr;
  sint16 PrimVLatSafeMax;
  sint16 PrimVLatSafeMin;
  Rte_DT_SG_PrimVLatSafe_4 PrimVLatSafeMinMaxQf;
  sint16 PrimVLatSafeNom;
  Rte_DT_SG_PrimVLatSafe_6 PrimVLatSafeNomQf;
} SG_PrimVLatSafe;

# define Rte_TypeDef_SG_PrimVehMSafe
typedef struct
{
  uint8 PrimVehMSafeChks;
  uint8 PrimVehMSafeCntr;
  Rte_DT_SG_PrimVehMSafe_2 PrimVehMSafeNomQf;
  Rte_DT_SG_PrimVehMSafe_3 PrimVehMSafeSafeQf;
  uint8 PrimVehMSafeVehMMax;
  uint8 PrimVehMSafeVehMMin;
  uint8 PrimVehMSafeVehMNom;
} SG_PrimVehMSafe;

# define Rte_TypeDef_SG_PrimVehSpdGroupSafe
typedef struct
{
  uint8 PrimVehSpdGroupSafeChks;
  uint8 PrimVehSpdGroupSafeCntr;
  uint16 PrimVehSpdGroupSafeMax;
  uint16 PrimVehSpdGroupSafeMin;
  Rte_DT_SG_PrimVehSpdGroupSafe_4 PrimVehSpdGroupSafeMinMaxQf;
  Rte_DT_SG_PrimVehSpdGroupSafe_5 PrimVehSpdGroupSafeMovDir;
  Rte_DT_SG_PrimVehSpdGroupSafe_6 PrimVehSpdGroupSafeMovDirQf;
  uint16 PrimVehSpdGroupSafeNom;
  Rte_DT_SG_PrimVehSpdGroupSafe_8 PrimVehSpdGroupSafeNomQf;
} SG_PrimVehSpdGroupSafe;

# define Rte_TypeDef_SG_PrimWhlAgSpdFrntSafe
typedef struct
{
  uint8 PrimWhlAgSpdFrntSafeChks;
  uint8 PrimWhlAgSpdFrntSafeCntr;
  sint16 PrimWhlAgSpdFrntSafeLe;
  Rte_DT_SG_PrimWhlAgSpdFrntSafe_3 PrimWhlAgSpdFrntSafeLeQf;
  sint16 PrimWhlAgSpdFrntSafeRi;
  Rte_DT_SG_PrimWhlAgSpdFrntSafe_5 PrimWhlAgSpdFrntSafeRiQf;
} SG_PrimWhlAgSpdFrntSafe;

# define Rte_TypeDef_SG_PrimWhlAgSpdReSafe
typedef struct
{
  uint8 PrimWhlAgSpdReSafeChks;
  uint8 PrimWhlAgSpdReSafeCntr;
  sint16 PrimWhlAgSpdReSafeLe;
  Rte_DT_SG_PrimWhlAgSpdReSafe_3 PrimWhlAgSpdReSafeLeQf;
  sint16 PrimWhlAgSpdReSafeRi;
  Rte_DT_SG_PrimWhlAgSpdReSafe_5 PrimWhlAgSpdReSafeRiQf;
} SG_PrimWhlAgSpdReSafe;

# define Rte_TypeDef_SG_PrimWhlRotDirReSafe1
typedef struct
{
  uint8 PrimWhlRotDirReSafe1Chks;
  uint8 PrimWhlRotDirReSafe1Cntr;
  Rte_DT_SG_PrimWhlRotDirReSafe1_2 PrimWhlRotDirReSafe1Le;
  Rte_DT_SG_PrimWhlRotDirReSafe1_3 PrimWhlRotDirReSafe1LeQf;
  Rte_DT_SG_PrimWhlRotDirReSafe1_4 PrimWhlRotDirReSafe1Ri;
  Rte_DT_SG_PrimWhlRotDirReSafe1_5 PrimWhlRotDirReSafe1RiQf;
} SG_PrimWhlRotDirReSafe1;

# define Rte_TypeDef_SG_PrimWhlRotToothCntr
typedef struct
{
  uint8 PrimWhlRotToothCntrChks;
  uint8 PrimWhlRotToothCntrCntr;
  uint8 PrimWhlRotToothCntrWhlRotToothCntrFrntLe;
  Rte_DT_SG_PrimWhlRotToothCntr_3 PrimWhlRotToothCntrWhlRotToothCntrFrntLeQf;
  uint8 PrimWhlRotToothCntrWhlRotToothCntrFrntRi;
  Rte_DT_SG_PrimWhlRotToothCntr_5 PrimWhlRotToothCntrWhlRotToothCntrFrntRiQf;
  uint8 PrimWhlRotToothCntrWhlRotToothCntrReLe;
  Rte_DT_SG_PrimWhlRotToothCntr_7 PrimWhlRotToothCntrWhlRotToothCntrReLeQf;
  uint8 PrimWhlRotToothCntrWhlRotToothCntrReRi;
  Rte_DT_SG_PrimWhlRotToothCntr_9 PrimWhlRotToothCntrWhlRotToothCntrReRiQf;
} SG_PrimWhlRotToothCntr;

# define Rte_TypeDef_SG_PrimYawRateSafe
typedef struct
{
  uint8 PrimYawRateSafeChks;
  uint8 PrimYawRateSafeCntr;
  sint16 PrimYawRateSafeMax;
  sint16 PrimYawRateSafeMin;
  Rte_DT_SG_PrimYawRateSafe_4 PrimYawRateSafeMinMaxQf;
  sint16 PrimYawRateSafeNom;
  Rte_DT_SG_PrimYawRateSafe_6 PrimYawRateSafeNomQf;
} SG_PrimYawRateSafe;

# define Rte_TypeDef_SG_PrpsnTqDir
typedef struct
{
  Rte_DT_SG_PrpsnTqDir_0 PrpsnTqDirAct;
  uint8 PrpsnTqDirChks;
  uint8 PrpsnTqDirCntr;
} SG_PrpsnTqDir;

# define Rte_TypeDef_SG_PrpsnTqDirCpby
typedef struct
{
  uint8 PrpsnTqDirCpbyChks;
  uint8 PrpsnTqDirCpbyCntr;
  Rte_DT_SG_PrpsnTqDirCpby_2 PrpsnTqDirCpbySts;
} SG_PrpsnTqDirCpby;

# define Rte_TypeDef_SG_RoadLoadNom
typedef struct
{
  uint16 RoadLoadNomCoeff0;
  sint16 RoadLoadNomCoeff1;
  uint16 RoadLoadNomCoeff2;
  Rte_DT_SG_RoadLoadNom_3 RoadLoadNomCoeffSts;
} SG_RoadLoadNom;

# define Rte_TypeDef_SG_SSMBDegraded
typedef struct
{
  uint8 SSMBDegradedchks;
  uint8 SSMBDegradedcntr;
  Rte_DT_SG_SSMBDegraded_2 SSMBDegradedSSMBDegraded;
} SG_SSMBDegraded;

# define Rte_TypeDef_SG_SSMDegraded
typedef struct
{
  uint8 SSMDegradedchks;
  uint8 SSMDegradedcntr;
  Rte_DT_SG_SSMDegraded_2 SSMDegradedssmdegraded;
} SG_SSMDegraded;

# define Rte_TypeDef_SG_SecAdWhlLockReq
typedef struct
{
  uint8 SecAdWhlLockReqChks;
  uint8 SecAdWhlLockReqCntr;
  Rte_DT_SG_SecAdWhlLockReq_2 SecAdWhlLockReqNoReqApplyRel;
} SG_SecAdWhlLockReq;

# define Rte_TypeDef_SG_SecPoseMonSafe
typedef struct
{
  uint8 SecPoseMonSafeChks;
  uint8 SecPoseMonSafeCntr;
  sint16 SecPoseMonSafeLatErr;
  sint16 SecPoseMonSafeLgtErr;
  Rte_DT_SG_SecPoseMonSafe_4 SecPoseMonSafeSafeQf;
  sint8 SecPoseMonSafeYawErr;
} SG_SecPoseMonSafe;

# define Rte_TypeDef_SG_SecSteerMotTq
typedef struct
{
  sint16 SecSteerMotTqSteerMotTq;
  Rte_DT_SG_SecSteerMotTq_1 SecSteerMotTqSteerMotTqQf;
} SG_SecSteerMotTq;

# define Rte_TypeDef_SG_SecWhlLockSts
typedef struct
{
  uint8 SecWhlLockStsChks;
  uint8 SecWhlLockStsCntr;
  Rte_DT_SG_SecWhlLockSts_2 SecWhlLockStsDegradedSts;
  Rte_DT_SG_SecWhlLockSts_3 SecWhlLockStsLockSts;
} SG_SecWhlLockSts;

# define Rte_TypeDef_SG_SnsrClngErrIf
typedef struct
{
  Rte_DT_SG_SnsrClngErrIf_0 SnsrClngErrIfActr1;
  Rte_DT_SG_SnsrClngErrIf_1 SnsrClngErrIfActr2;
  Rte_DT_SG_SnsrClngErrIf_2 SnsrClngErrIfActr3;
  Rte_DT_SG_SnsrClngErrIf_3 SnsrClngErrIfRsvd1;
  Rte_DT_SG_SnsrClngErrIf_4 SnsrClngErrIfRsvd2;
  Rte_DT_SG_SnsrClngErrIf_5 SnsrClngErrIfRsvd3;
  Rte_DT_SG_SnsrClngErrIf_6 SnsrClngErrIfSysClngFailr;
  Rte_DT_SG_SnsrClngErrIf_7 SnsrClngErrIfWshrFldSnsrFailr;
} SG_SnsrClngErrIf;

# define Rte_TypeDef_SG_StandStillMgrStsForHldSafe
typedef struct
{
  uint8 StandStillMgrStsForHldSafeChks;
  uint8 StandStillMgrStsForHldSafeCntr;
  Rte_DT_SG_StandStillMgrStsForHldSafe_2 StandStillMgrStsForHldSafeStandStillSts;
} SG_StandStillMgrStsForHldSafe;

# define Rte_TypeDef_SG_SteerWhlSnsr
typedef struct
{
  sint16 SteerWhlAgSafe;
  sint16 SteerWhlAgSpdSafe;
  Rte_DT_SG_SteerWhlSnsr_2 SteerWhlSnsrQf;
  uint8 SteerWhlSnsrSafeChks;
  uint8 SteerWhlSnsrSafeCntr;
} SG_SteerWhlSnsr;

# define Rte_TypeDef_SG_SteerWhlTqGroup
typedef struct
{
  sint16 SteerWhlTq;
  Rte_DT_SG_SteerWhlTqGroup_1 SteerWhlTqQf;
} SG_SteerWhlTqGroup;

# define Rte_TypeDef_SG_SwtExtrLiFromAPI
typedef struct
{
  uint8 SwtExtrLiFromAPILiExtFctCntr;
  uint8 SwtExtrLiFromAPILiExtFctCrc;
  Rte_DT_SG_SwtExtrLiFromAPI_2 SwtExtrLiFromAPILiExtFctQf;
  Rte_DT_SG_SwtExtrLiFromAPI_3 SwtExtrLiFromAPILiExtFctReq1;
} SG_SwtExtrLiFromAPI;

# define Rte_TypeDef_SG_SwtExtrLiToAPI
typedef struct
{
  uint8 SwtExtrLiToAPILiExtFctCntr;
  uint8 SwtExtrLiToAPILiExtFctCrc;
  Rte_DT_SG_SwtExtrLiToAPI_2 SwtExtrLiToAPILiExtFctQf;
  Rte_DT_SG_SwtExtrLiToAPI_3 SwtExtrLiToAPILiExtFctReq1;
} SG_SwtExtrLiToAPI;

# define Rte_TypeDef_SG_SwtIndcrToAPI
typedef struct
{
  Rte_DT_SG_SwtIndcrToAPI_0 SwtIndcrToAPIIndcrTypExtReq;
  uint8 SwtIndcrToAPIIndcrTypExtReqChks;
  uint8 SwtIndcrToAPIIndcrTypExtReqCntr;
  Rte_DT_SG_SwtIndcrToAPI_3 SwtIndcrToAPIIndcrTypExtReqToUpdQf;
} SG_SwtIndcrToAPI;

# define Rte_TypeDef_SG_TirePWarnFrntRi
typedef struct
{
  Rte_DT_SG_TirePWarnFrntRi_0 TirePWarnFrntRiTirePWarn;
  Rte_DT_SG_TirePWarnFrntRi_1 TirePWarnFrntRiTirePWarnType;
} SG_TirePWarnFrntRi;

# define Rte_TypeDef_SG_TirePWarnReLe
typedef struct
{
  Rte_DT_SG_TirePWarnReLe_0 TirePWarnReLeTirePWarn;
  Rte_DT_SG_TirePWarnReLe_1 TirePWarnReLeTirePWarnType;
} SG_TirePWarnReLe;

# define Rte_TypeDef_SG_TirePWarnReRi
typedef struct
{
  Rte_DT_SG_TirePWarnReRi_0 TirePWarnReRiTirePWarn;
  Rte_DT_SG_TirePWarnReRi_1 TirePWarnReRiTirePWarnType;
} SG_TirePWarnReRi;

# define Rte_TypeDef_SG_VehMGroup
typedef struct
{
  uint16 VehM;
  Rte_DT_SG_VehMGroup_1 VehMNomTrlrM;
  Rte_DT_SG_VehMGroup_2 VehMQly;
} SG_VehMGroup;

# define Rte_TypeDef_SG_VehOperStReq
typedef struct
{
  uint8 VehOperStReqChecksum;
  uint8 VehOperStReqCounter;
  Rte_DT_SG_VehOperStReq_2 VehOperStReqVehOperStReq;
} SG_VehOperStReq;

# define Rte_TypeDef_SG_WhlLockSts
typedef struct
{
  uint8 WhlLockStsChks;
  uint8 WhlLockStsCntr;
  Rte_DT_SG_WhlLockSts_2 WhlLockStsDegradedSts;
  Rte_DT_SG_WhlLockSts_3 WhlLockStsLockSts;
} SG_WhlLockSts;

# define Rte_TypeDef_StbM_EthTimeMasterMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType sourcePortId;
  StbM_VirtualLocalTimeType syncEgressTimestamp;
  StbM_TimeStampShortType preciseOriginTimestamp;
  sint64 correctionField;
} StbM_EthTimeMasterMeasurementType;

# define Rte_TypeDef_StbM_EthTimeSlaveMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType sourcePortId;
  StbM_VirtualLocalTimeType syncIngressTimestamp;
  StbM_TimeStampShortType preciseOriginTimestamp;
  sint64 correctionField;
  uint32 pDelay;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
} StbM_EthTimeSlaveMeasurementType;

# define Rte_TypeDef_StbM_OffsetRecordTableBlockType
typedef struct
{
  uint32 GlbSeconds;
  uint32 GlbNanoSeconds;
  StbM_TimeBaseStatusType TimeBaseStatus;
} StbM_OffsetRecordTableBlockType;

# define Rte_TypeDef_StbM_PdelayInitiatorMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType requestPortId;
  StbM_PortIdType responsePortId;
  StbM_VirtualLocalTimeType requestOriginTimestamp;
  StbM_VirtualLocalTimeType responseReceiptTimestamp;
  StbM_TimeStampShortType requestReceiptTimestamp;
  StbM_TimeStampShortType responseOriginTimestamp;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
  uint32 pdelay;
} StbM_PdelayInitiatorMeasurementType;

# define Rte_TypeDef_StbM_PdelayResponderMeasurementType
typedef struct
{
  uint16 sequenceId;
  StbM_PortIdType requestPortId;
  StbM_PortIdType responsePortId;
  StbM_VirtualLocalTimeType requestReceiptTimestamp;
  StbM_VirtualLocalTimeType responseOriginTimestamp;
  StbM_VirtualLocalTimeType referenceLocalTimestamp;
  StbM_TimeStampShortType referenceGlobalTimestamp;
} StbM_PdelayResponderMeasurementType;

# define Rte_TypeDef_StbM_SyncRecordTableBlockType
typedef struct
{
  uint32 GlbSeconds;
  uint32 GlbNanoSeconds;
  StbM_TimeBaseStatusType TimeBaseStatus;
  uint32 VirtualLocalTimeLow;
  sint16 RateDeviation;
  uint32 LocSeconds;
  uint32 LocNanoSeconds;
  uint32 PathDelay;
} StbM_SyncRecordTableBlockType;

# define Rte_TypeDef_StbM_TimeStampType
typedef struct
{
  StbM_TimeBaseStatusType timeBaseStatus;
  uint32 nanoseconds;
  uint32 seconds;
  uint16 secondsHi;
} StbM_TimeStampType;

# define Rte_TypeDef_TrajectoryInput
typedef struct
{
  uint32 plan_init_timestamp_ms;
  Rte_DT_TrajectoryInput_1 trajectory_pose;
  Intention intention;
} TrajectoryInput;

# define Rte_TypeDef_ControllerDebug
typedef struct
{
  LateralPIDDebug lateral_pid;
  LongitudinalPIDDebug longitudinal_pid;
} ControllerDebug;

# define Rte_TypeDef_ControlDebug
typedef struct
{
  uint64 timestamp_ms;
  Metrics metrics;
  ControllerDebug controller;
} ControlDebug;


# ifndef RTE_SUPPRESS_UNUSED_DATATYPES
/**********************************************************************************************************************
 * Unused Data type definitions
 *********************************************************************************************************************/

#  define Rte_TypeDef_dtRef_VOID
typedef void * dtRef_VOID;

#  define Rte_TypeDef_dtRef_const_VOID
typedef const void * dtRef_const_VOID;

#  define Rte_TypeDef_AhbcIndcnToAPI_A
typedef uint8 AhbcIndcnToAPI_A;

#  define Rte_TypeDef_Dem_OperationCycleIdType
typedef uint8 Dem_OperationCycleIdType;

#  define Rte_TypeDef_DoorDrvrMovmtFailNotif_A
typedef uint8 DoorDrvrMovmtFailNotif_A;

#  define Rte_TypeDef_DoorDrvrReMovmtFailNotif_A
typedef uint8 DoorDrvrReMovmtFailNotif_A;

#  define Rte_TypeDef_DoorPassMovmtFailNotif_A
typedef uint8 DoorPassMovmtFailNotif_A;

#  define Rte_TypeDef_DoorPassReMovmtFailNotif_A
typedef uint8 DoorPassReMovmtFailNotif_A;

#  define Rte_TypeDef_NvM_BlockIdType
typedef uint16 NvM_BlockIdType;

#  define Rte_TypeDef_NvM_RequestResultType
typedef uint8 NvM_RequestResultType;

#  define Rte_TypeDef_NvM_ServiceIdType
typedef uint8 NvM_ServiceIdType;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtDrvr_A_0
typedef boolean Rte_DT_SG_BltLockStAtDrvr_A_0;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtDrvr_A_1
typedef boolean Rte_DT_SG_BltLockStAtDrvr_A_1;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtPass_A_0
typedef boolean Rte_DT_SG_BltLockStAtPass_A_0;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtPass_A_1
typedef boolean Rte_DT_SG_BltLockStAtPass_A_1;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_A_0
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_A_0;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_A_1
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_A_1;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecLe_A_2
typedef boolean Rte_DT_SG_BltLockStAtRowSecLe_A_2;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_A_0
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_A_0;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_A_1
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_A_1;

#  define Rte_TypeDef_Rte_DT_SG_BltLockStAtRowSecRi_A_2
typedef boolean Rte_DT_SG_BltLockStAtRowSecRi_A_2;

#  define Rte_TypeDef_Rte_DT_SG_DoorPassRePosnStsToAPI_A_0
typedef uint8 Rte_DT_SG_DoorPassRePosnStsToAPI_A_0;

#  define Rte_TypeDef_SG_BltLockStAtDrvr_A
typedef struct
{
  Rte_DT_SG_BltLockStAtDrvr_A_0 BltLockStAtDrvrForBltLockSt1_A;
  Rte_DT_SG_BltLockStAtDrvr_A_1 BltLockStAtDrvrForDevErrSts2_A;
} SG_BltLockStAtDrvr_A;

#  define Rte_TypeDef_SG_BltLockStAtPass_A
typedef struct
{
  Rte_DT_SG_BltLockStAtPass_A_0 BltLockStAtPassForBltLockSt1_A;
  Rte_DT_SG_BltLockStAtPass_A_1 BltLockStAtPassForBltLockSts_A;
} SG_BltLockStAtPass_A;

#  define Rte_TypeDef_SG_BltLockStAtRowSecLe_A
typedef struct
{
  Rte_DT_SG_BltLockStAtRowSecLe_A_0 BltLockStAtRowSecLeForBltLockEquid_A;
  Rte_DT_SG_BltLockStAtRowSecLe_A_1 BltLockStAtRowSecLeForBltLockSt1_A;
  Rte_DT_SG_BltLockStAtRowSecLe_A_2 BltLockStAtRowSecLeForBltLockSts_A;
} SG_BltLockStAtRowSecLe_A;

#  define Rte_TypeDef_SG_BltLockStAtRowSecRi_A
typedef struct
{
  Rte_DT_SG_BltLockStAtRowSecRi_A_0 BltLockStAtRowSecRiForBltLockEquid_A;
  Rte_DT_SG_BltLockStAtRowSecRi_A_1 BltLockStAtRowSecRiForBltLockSt1_A;
  Rte_DT_SG_BltLockStAtRowSecRi_A_2 BltLockStAtRowSecRiForBltLockSts_A;
} SG_BltLockStAtRowSecRi_A;

#  define Rte_TypeDef_SG_DoorPassRePosnStsToAPI_A
typedef struct
{
  Rte_DT_SG_DoorPassRePosnStsToAPI_A_0 DoorPassRePosnStsToAPIPassReDoorPosnSts_A;
  uint8 DoorPassRePosnStsToAPIPassReDoorPosnStsChks_A;
  uint8 DoorPassRePosnStsToAPIPassReDoorPosnStsCntr_A;
} SG_DoorPassRePosnStsToAPI_A;

# endif


/**********************************************************************************************************************
 * Constant value definitions
 *********************************************************************************************************************/

# define RTE_START_SEC_CONST_UNSPECIFIED
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONST(AcuControlTimestamp, RTE_CONST) Rte_C_AcuControlTimestamp_0;

extern CONST(GnssPoseTimeStamp, RTE_CONST) Rte_C_GnssPoseTimeStamp_0;

extern CONST(PoseTimeStamp, RTE_CONST) Rte_C_PoseTimeStamp_0;

extern CONST(TrajectoryInfoInitTimeStamp, RTE_CONST) Rte_C_TrajectoryInfoInitTimeStamp_0;

extern CONST(AcuControl, RTE_CONST) Rte_C_AcuControl_0;

extern CONST(BDP_FilteredLane_Struct, RTE_CONST) Rte_C_BDP_FilteredLane_Struct_0;

extern CONST(CDS_CollisionDetection_Struct, RTE_CONST) Rte_C_CDS_CollisionDetection_Struct_0;

extern CONST(CSI_LaneInfo_Struct, RTE_CONST) Rte_C_CSI_LaneInfo_Struct_0;

extern CONST(CSI_ObjectInfo_Struct, RTE_CONST) Rte_C_CSI_ObjectInfo_Struct_0;

extern CONST(ControlDebug, RTE_CONST) Rte_C_ControlDebug_0;

extern CONST(ControlOutput, RTE_CONST) Rte_C_ControlOutput_0;

extern CONST(EstimationDebug, RTE_CONST) Rte_C_EstimationDebug_0;

extern CONST(FBS_DebugInfo_Struct, RTE_CONST) Rte_C_FBS_DebugInfo_Struct_0;

extern CONST(FSA_SystemState_Struct, RTE_CONST) Rte_C_FSA_SystemState_Struct_0;

extern CONST(FSC_FreeStopControl_Struct, RTE_CONST) Rte_C_FSC_FreeStopControl_Struct_0;

extern CONST(GnssPoseInput, RTE_CONST) Rte_C_GnssPoseInput_0;

extern CONST(LAT_CtrlCmd_Struct, RTE_CONST) Rte_C_LAT_CtrlCmd_Struct_0;

extern CONST(LGT_CtrlCmd_Struct, RTE_CONST) Rte_C_LGT_CtrlCmd_Struct_0;

extern CONST(LSI_LatSigInput_Struct, RTE_CONST) Rte_C_LSI_LatSigInput_Struct_0;

extern CONST(LocalizationPoseInput, RTE_CONST) Rte_C_LocalizationPoseInput_0;

extern CONST(OPS_ObjectList_Struct, RTE_CONST) Rte_C_OPS_ObjectList_Struct_0;

extern CONST(SG_ALgtMaxAvl, RTE_CONST) Rte_C_SG_ALgtMaxAvl_0;

extern CONST(SG_ALnchTiDly3, RTE_CONST) Rte_C_SG_ALnchTiDly3_0;

extern CONST(SG_AccrPedlPsd, RTE_CONST) Rte_C_SG_AccrPedlPsd_0;

extern CONST(SG_AdNomALgtReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdNomALgtReqGroupSafe_A_0;

extern CONST(SG_AdPrimALgtLimReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdPrimALgtLimReqGroupSafe_A_0;

extern CONST(SG_AdPrimPose_A, RTE_CONST) Rte_C_SG_AdPrimPose_A_0;

extern CONST(SG_AdPrimSafeStopActvGroupSafe, RTE_CONST) Rte_C_SG_AdPrimSafeStopActvGroupSafe_0;

extern CONST(SG_AdPrimSteerStsSafeGroup, RTE_CONST) Rte_C_SG_AdPrimSteerStsSafeGroup_0;

extern CONST(SG_AdPrimWhlAgEstimdGroupSafe, RTE_CONST) Rte_C_SG_AdPrimWhlAgEstimdGroupSafe_0;

extern CONST(SG_AdPrimWhlAgReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdPrimWhlAgReqGroupSafe_A_0;

extern CONST(SG_AdSecALgtLimReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdSecALgtLimReqGroupSafe_A_0;

extern CONST(SG_AdSecBlindStopMonActvnGroupSaf, RTE_CONST) Rte_C_SG_AdSecBlindStopMonActvnGroupSaf_0;

extern CONST(SG_AdSecBrkActvnGroupSafe, RTE_CONST) Rte_C_SG_AdSecBrkActvnGroupSafe_0;

extern CONST(SG_AdSecPahStsGroupSafe, RTE_CONST) Rte_C_SG_AdSecPahStsGroupSafe_0;

extern CONST(SG_AdSecSafeStopActvGroupSafe, RTE_CONST) Rte_C_SG_AdSecSafeStopActvGroupSafe_0;

extern CONST(SG_AdSecSteerActvnGroupSafe, RTE_CONST) Rte_C_SG_AdSecSteerActvnGroupSafe_0;

extern CONST(SG_AdSecSteerModStsSafeGroupByGat, RTE_CONST) Rte_C_SG_AdSecSteerModStsSafeGroupByGat_0;

extern CONST(SG_AdSecSteerStsSafeGroup, RTE_CONST) Rte_C_SG_AdSecSteerStsSafeGroup_0;

extern CONST(SG_AdSecSteerStsSafeGroupByGatewy, RTE_CONST) Rte_C_SG_AdSecSteerStsSafeGroupByGatewy_0;

extern CONST(SG_AdSecWhlAgRateLimEstimdSafe, RTE_CONST) Rte_C_SG_AdSecWhlAgRateLimEstimdSafe_0;

extern CONST(SG_AdSecWhlAgReqGroupSafe_A, RTE_CONST) Rte_C_SG_AdSecWhlAgReqGroupSafe_A_0;

extern CONST(SG_AdSteerPaddlPsdGroupSafe, RTE_CONST) Rte_C_SG_AdSteerPaddlPsdGroupSafe_0;

extern CONST(SG_AgDataRawSafe, RTE_CONST) Rte_C_SG_AgDataRawSafe_0;

extern CONST(SG_AlrmSts1, RTE_CONST) Rte_C_SG_AlrmSts1_0;

extern CONST(SG_AutnmsDrvModMngtExtSafe, RTE_CONST) Rte_C_SG_AutnmsDrvModMngtExtSafe_0;

extern CONST(SG_AutnmsDrvModMngtGlbSafe1, RTE_CONST) Rte_C_SG_AutnmsDrvModMngtGlbSafe1_0;

extern CONST(SG_AutnmsDrvStReq_A, RTE_CONST) Rte_C_SG_AutnmsDrvStReq_A_0;

extern CONST(SG_BltLockStAtDrvr, RTE_CONST) Rte_C_SG_BltLockStAtDrvr_0;

extern CONST(SG_BltLockStAtPass, RTE_CONST) Rte_C_SG_BltLockStAtPass_0;

extern CONST(SG_BltLockStAtRowSecLe, RTE_CONST) Rte_C_SG_BltLockStAtRowSecLe_0;

extern CONST(SG_BltLockStAtRowSecRi, RTE_CONST) Rte_C_SG_BltLockStAtRowSecRi_0;

extern CONST(SG_BrkDegraded, RTE_CONST) Rte_C_SG_BrkDegraded_0;

extern CONST(SG_BrkDegradedRdnt, RTE_CONST) Rte_C_SG_BrkDegradedRdnt_0;

extern CONST(SG_BrkFricTqTotAtWhlsAct, RTE_CONST) Rte_C_SG_BrkFricTqTotAtWhlsAct_0;

extern CONST(SG_BrkPedlPsdSafeGroup, RTE_CONST) Rte_C_SG_BrkPedlPsdSafeGroup_0;

extern CONST(SG_BrkPedlVal, RTE_CONST) Rte_C_SG_BrkPedlVal_0;

extern CONST(SG_BrkTqMinReq, RTE_CONST) Rte_C_SG_BrkTqMinReq_0;

extern CONST(SG_CarModInCrashStsSafe, RTE_CONST) Rte_C_SG_CarModInCrashStsSafe_0;

extern CONST(SG_CllsnAidSnvtySeld, RTE_CONST) Rte_C_SG_CllsnAidSnvtySeld_0;

extern CONST(SG_ClstrSts1ForAutnmsDrv, RTE_CONST) Rte_C_SG_ClstrSts1ForAutnmsDrv_0;

extern CONST(SG_ClstrSts2ForAutnmsDrv, RTE_CONST) Rte_C_SG_ClstrSts2ForAutnmsDrv_0;

extern CONST(SG_DoorPassRePosnStsToAPI, RTE_CONST) Rte_C_SG_DoorPassRePosnStsToAPI_0;

extern CONST(SG_DrvrGearShiftManReq, RTE_CONST) Rte_C_SG_DrvrGearShiftManReq_0;

extern CONST(SG_DrvrIntvSts, RTE_CONST) Rte_C_SG_DrvrIntvSts_0;

extern CONST(SG_DrvrPrsntGroup, RTE_CONST) Rte_C_SG_DrvrPrsntGroup_0;

extern CONST(SG_EngFltIndcn, RTE_CONST) Rte_C_SG_EngFltIndcn_0;

extern CONST(SG_FricEstimnFromVehDynGroup, RTE_CONST) Rte_C_SG_FricEstimnFromVehDynGroup_0;

extern CONST(SG_OvrdDecelByDrvr, RTE_CONST) Rte_C_SG_OvrdDecelByDrvr_0;

extern CONST(SG_PrimALatDataRawSafe, RTE_CONST) Rte_C_SG_PrimALatDataRawSafe_0;

extern CONST(SG_PrimALgtDataRawSafe, RTE_CONST) Rte_C_SG_PrimALgtDataRawSafe_0;

extern CONST(SG_PrimAxleSlipStsAndRelAg, RTE_CONST) Rte_C_SG_PrimAxleSlipStsAndRelAg_0;

extern CONST(SG_PrimVLatSafe, RTE_CONST) Rte_C_SG_PrimVLatSafe_0;

extern CONST(SG_PrimVehMSafe, RTE_CONST) Rte_C_SG_PrimVehMSafe_0;

extern CONST(SG_PrimVehSpdGroupSafe, RTE_CONST) Rte_C_SG_PrimVehSpdGroupSafe_0;

extern CONST(SG_PrimWhlAgSpdFrntSafe, RTE_CONST) Rte_C_SG_PrimWhlAgSpdFrntSafe_0;

extern CONST(SG_PrimWhlAgSpdReSafe, RTE_CONST) Rte_C_SG_PrimWhlAgSpdReSafe_0;

extern CONST(SG_PrimWhlRotDirReSafe1, RTE_CONST) Rte_C_SG_PrimWhlRotDirReSafe1_0;

extern CONST(SG_PrimWhlRotToothCntr, RTE_CONST) Rte_C_SG_PrimWhlRotToothCntr_0;

extern CONST(SG_PrimYawRateSafe, RTE_CONST) Rte_C_SG_PrimYawRateSafe_0;

extern CONST(SG_PrpsnTqDir, RTE_CONST) Rte_C_SG_PrpsnTqDir_0;

extern CONST(SG_PrpsnTqDirCpby, RTE_CONST) Rte_C_SG_PrpsnTqDirCpby_0;

extern CONST(SG_RoadLoadNom, RTE_CONST) Rte_C_SG_RoadLoadNom_0;

extern CONST(SG_SSMBDegraded, RTE_CONST) Rte_C_SG_SSMBDegraded_0;

extern CONST(SG_SSMDegraded, RTE_CONST) Rte_C_SG_SSMDegraded_0;

extern CONST(SG_SecAdNomALgtReqGroupSafe_A, RTE_CONST) Rte_C_SG_SecAdNomALgtReqGroupSafe_A_0;

extern CONST(SG_SecMaxALatEstimdGroup, RTE_CONST) Rte_C_SG_SecMaxALatEstimdGroup_0;

extern CONST(SG_SecPoseMonSafe, RTE_CONST) Rte_C_SG_SecPoseMonSafe_0;

extern CONST(SG_SecSteerMotTq, RTE_CONST) Rte_C_SG_SecSteerMotTq_0;

extern CONST(SG_SecWhlLockSts, RTE_CONST) Rte_C_SG_SecWhlLockSts_0;

extern CONST(SG_SnsrClngErrIf, RTE_CONST) Rte_C_SG_SnsrClngErrIf_0;

extern CONST(SG_StandStillMgrStsForHldSafe, RTE_CONST) Rte_C_SG_StandStillMgrStsForHldSafe_0;

extern CONST(SG_SteerWhlSnsr, RTE_CONST) Rte_C_SG_SteerWhlSnsr_0;

extern CONST(SG_SteerWhlTqGroup, RTE_CONST) Rte_C_SG_SteerWhlTqGroup_0;

extern CONST(SG_SwtExtrLiToAPI, RTE_CONST) Rte_C_SG_SwtExtrLiToAPI_0;

extern CONST(SG_SwtIndcrToAPI, RTE_CONST) Rte_C_SG_SwtIndcrToAPI_0;

extern CONST(SG_TirePWarnFrntRi, RTE_CONST) Rte_C_SG_TirePWarnFrntRi_0;

extern CONST(SG_TirePWarnReLe, RTE_CONST) Rte_C_SG_TirePWarnReLe_0;

extern CONST(SG_TirePWarnReRi, RTE_CONST) Rte_C_SG_TirePWarnReRi_0;

extern CONST(SG_VehMGroup, RTE_CONST) Rte_C_SG_VehMGroup_0;

extern CONST(SG_WhlAgReqFb, RTE_CONST) Rte_C_SG_WhlAgReqFb_0;

extern CONST(SG_WhlAgReqFbRdnt, RTE_CONST) Rte_C_SG_WhlAgReqFbRdnt_0;

extern CONST(SG_WhlLockSts, RTE_CONST) Rte_C_SG_WhlLockSts_0;

extern CONST(TrajectoryInput, RTE_CONST) Rte_C_TrajectoryInput_0;

extern CONST(VDP_VehicleState_Struct, RTE_CONST) Rte_C_VDP_VehicleState_Struct_0;

extern CONST(VSI_McuCanTimeout_Struct, RTE_CONST) Rte_C_VSI_McuCanTimeout_Struct_0;

extern CONST(VSI_VehInfoFor1V1R_Struct, RTE_CONST) Rte_C_VSI_VehInfoFor1V1R_Struct_0;

extern CONST(VSI_VehicleInfo_Struct, RTE_CONST) Rte_C_VSI_VehicleInfo_Struct_0;

extern CONST(VSP_VehicleSignal_Struct, RTE_CONST) Rte_C_VSP_VehicleSignal_Struct_0;

# define RTE_STOP_SEC_CONST_UNSPECIFIED
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */
# include "_out/Appl/GenData/Rte_DataHandleType.h"


/**********************************************************************************************************************
 * Buffer definitions for implicit access to S/R port elements
 *********************************************************************************************************************/

# define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

typedef struct
{
  Rte_DE_AcuFbReserved Rte_AcuFbAdsStatus_AcuFbReserved;
  Rte_DE_AcuFbReserved1 Rte_AcuFbAdsStatus_AcuFbReserved1;
  Rte_DE_AcuFbReserved2 Rte_AcuFbAdsStatus_AcuFbReserved2;
  Rte_DE_AcuFbReserved3 Rte_AcuFbAdsStatus_AcuFbReserved3;
  Rte_DE_AcuFbReserved4 Rte_AcuFbAdsStatus_AcuFbReserved4;
  Rte_DE_AcuFbRollingCounter Rte_AcuFbAdsStatus_AcuFbRollingCounter;
  Rte_DE_AdsFaultStatus Rte_AcuFbAdsStatus_AdsFaultStatus;
  Rte_DE_AdsFaultStatusBackup Rte_AcuFbAdsStatus_AdsFaultStatusBackup;
  Rte_DE_ReqResleaseFbControl Rte_AcuFbAdsStatus_ReqResleaseFbControl;
  Rte_DE_SG_AdSecSteerStsSafeGroup Rte_SSMBMid6CanFdFr01_SG_AdSecSteerStsSafeGroup;
  Rte_DE_SG_SSMBDegraded Rte_SSMBMid6CanFdFr03_SG_SSMBDegraded;
  Rte_DE_SG_SecPoseMonSafe Rte_SSMBMid6CanFdFr03_SG_SecPoseMonSafe;
  Rte_DE_SG_SecSteerMotTq Rte_SSMBMid6CanFdFr03_SG_SecSteerMotTq;
  Rte_DE_SG_SecWhlLockSts Rte_SSMBMid6CanFdFr03_SG_SecWhlLockSts;
  Rte_DE_SG_BrkDegradedRdnt Rte_SSMBMid6CanFdFr04_SG_BrkDegradedRdnt;
  Rte_DE_SG_AdSecPahStsGroupSafe Rte_SSMBMid6CanFdFr11_SG_AdSecPahStsGroupSafe;
  Rte_DE_SG_AdSecSafeStopActvGroupSafe Rte_SSMBMid6CanFdFr11_SG_AdSecSafeStopActvGroupSafe;
  Rte_DE_SG_AdSecWhlAgRateLimEstimdSafe Rte_SSMBMid6CanFdFr11_SG_AdSecWhlAgRateLimEstimdSafe;
  Rte_DE_SG_SecMaxALatEstimdGroup Rte_SSMBMid6CanFdFr11_SG_SecMaxALatEstimdGroup;
  Rte_DE_SG_WhlAgReqFbRdnt Rte_SSMBMid6CanFdFr11_SG_WhlAgReqFbRdnt;
  Rte_DE_SG_AdSecSteerModStsSafeGroupByGat Rte_SSMMid3CanFr01_SG_AdSecSteerModStsSafeGroupByGat;
  Rte_DE_SG_AdSecSteerStsSafeGroupByGatewy Rte_SSMMid3CanFr01_SG_AdSecSteerStsSafeGroupByGatewy;
  Rte_DE_SG_PrimVehSpdGroupSafe Rte_SSMMid3CanFr07_SG_PrimVehSpdGroupSafe;
  Rte_DE_SG_PrimALgtDataRawSafe Rte_SSMMid3CanFr11_SG_PrimALgtDataRawSafe;
  Rte_DE_SG_AdPrimWhlAgEstimdGroupSafe Rte_SSMMid5CanFdFr03_SG_AdPrimWhlAgEstimdGroupSafe;
  Rte_DE_SG_PrimALatDataRawSafe Rte_SSMMid5CanFdFr03_SG_PrimALatDataRawSafe;
  Rte_DE_SG_PrimAxleSlipStsAndRelAg Rte_SSMMid5CanFdFr03_SG_PrimAxleSlipStsAndRelAg;
  Rte_DE_SG_PrimVLatSafe Rte_SSMMid5CanFdFr03_SG_PrimVLatSafe;
  Rte_DE_SG_PrimWhlRotDirReSafe1 Rte_SSMMid5CanFdFr03_SG_PrimWhlRotDirReSafe1;
  Rte_DE_SG_PrimWhlRotToothCntr Rte_SSMMid5CanFdFr03_SG_PrimWhlRotToothCntr;
  Rte_DE_SG_PrimYawRateSafe Rte_SSMMid5CanFdFr03_SG_PrimYawRateSafe;
  Rte_DE_SG_PrimVehMSafe Rte_SSMMid5CanFdFr04_SG_PrimVehMSafe;
  Rte_DE_SG_PrimWhlAgSpdFrntSafe Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdFrntSafe;
  Rte_DE_SG_PrimWhlAgSpdReSafe Rte_SSMMid5CanFdFr04_SG_PrimWhlAgSpdReSafe;
  Rte_DE_SG_AdPrimSafeStopActvGroupSafe Rte_SSMMid5CanFdFr06_SG_AdPrimSafeStopActvGroupSafe;
  Rte_DE_SG_SSMDegraded Rte_SSMMid5CanFdFr06_SG_SSMDegraded;
  Rte_DE_SG_WhlAgReqFb Rte_SSMMid5CanFdFr06_SG_WhlAgReqFb;
  Rte_DE_SG_AdPrimSteerStsSafeGroup Rte_VCU1Mid3CanFr01_SG_AdPrimSteerStsSafeGroup;
  Rte_DE_SG_CarModInCrashStsSafe Rte_VCU1Mid3CanFr01_SG_CarModInCrashStsSafe;
  Rte_DE_HornActvIf Rte_VCU1Mid3CanFr02_HornActvIf;
  Rte_DE_HornSwtStsIf Rte_VCU1Mid3CanFr02_HornSwtStsIf;
  Rte_DE_SG_BrkTqMinReq Rte_VCU1Mid3CanFr02_SG_BrkTqMinReq;
  Rte_DE_WshrFldLvlContnsIf Rte_VCU1Mid3CanFr02_WshrFldLvlContnsIf;
  Rte_DE_SG_AdSecSteerActvnGroupSafe Rte_VCU1Mid3CanFr03_SG_AdSecSteerActvnGroupSafe;
  Rte_DE_SG_PrpsnTqDir Rte_VCU1Mid3CanFr03_SG_PrpsnTqDir;
  Rte_DE_SG_SteerWhlTqGroup Rte_VCU1Mid3CanFr03_SG_SteerWhlTqGroup;
  Rte_DE_SG_FricEstimnFromVehDynGroup Rte_VCU1Mid3CanFr04_SG_FricEstimnFromVehDynGroup;
  Rte_DE_SG_PrpsnTqDirCpby Rte_VCU1Mid3CanFr04_SG_PrpsnTqDirCpby;
  Rte_DE_SG_SwtExtrLiToAPI Rte_VCU1Mid3CanFr04_SG_SwtExtrLiToAPI;
  Rte_DE_PassSeatSts Rte_VCU1Mid3CanFr05_PassSeatSts;
  Rte_DE_SG_AdSecBlindStopMonActvnGroupSaf Rte_VCU1Mid3CanFr05_SG_AdSecBlindStopMonActvnGroupSaf;
  Rte_DE_SG_DrvrGearShiftManReq Rte_VCU1Mid3CanFr05_SG_DrvrGearShiftManReq;
  Rte_DE_SG_SnsrClngErrIf Rte_VCU1Mid3CanFr05_SG_SnsrClngErrIf;
  Rte_DE_SG_VehMGroup Rte_VCU1Mid3CanFr05_SG_VehMGroup;
  Rte_DE_WinWipgAutCmdIf Rte_VCU1Mid3CanFr05_WinWipgAutCmdIf;
  Rte_DE_WipgFrntAutModIf Rte_VCU1Mid3CanFr05_WipgFrntAutModIf;
  Rte_DE_WiprActvIf Rte_VCU1Mid3CanFr05_WiprActvIf;
  Rte_DE_WshngCycActvIf Rte_VCU1Mid3CanFr05_WshngCycActvIf;
  Rte_DE_SG_AgDataRawSafe Rte_VCU1Mid3CanFr08_SG_AgDataRawSafe;
  Rte_DE_SG_ClstrSts1ForAutnmsDrv Rte_VCU1Mid3CanFr08_SG_ClstrSts1ForAutnmsDrv;
  Rte_DE_SG_AdSteerPaddlPsdGroupSafe Rte_VCU1Mid3CanFr09_SG_AdSteerPaddlPsdGroupSafe;
  Rte_DE_SG_SteerWhlSnsr Rte_VCU1Mid3CanFr09_SG_SteerWhlSnsr;
  Rte_DE_WhlMotSysCluSts Rte_VCU1Mid3CanFr09_WhlMotSysCluSts;
  Rte_DE_AhbcIndcnToAPI Rte_VCU1Mid3CanFr11_AhbcIndcnToAPI;
  Rte_DE_DoorDrvrMovmtFailNotif Rte_VCU1Mid3CanFr11_DoorDrvrMovmtFailNotif;
  Rte_DE_DoorDrvrReMovmtFailNotif Rte_VCU1Mid3CanFr11_DoorDrvrReMovmtFailNotif;
  Rte_DE_DoorPassMovmtFailNotif Rte_VCU1Mid3CanFr11_DoorPassMovmtFailNotif;
  Rte_DE_DoorPassReMovmtFailNotif Rte_VCU1Mid3CanFr11_DoorPassReMovmtFailNotif;
  Rte_DE_SG_BltLockStAtDrvr Rte_VCU1Mid3CanFr11_SG_BltLockStAtDrvr;
  Rte_DE_SG_BltLockStAtPass Rte_VCU1Mid3CanFr11_SG_BltLockStAtPass;
  Rte_DE_SG_BltLockStAtRowSecLe Rte_VCU1Mid3CanFr11_SG_BltLockStAtRowSecLe;
  Rte_DE_SG_BltLockStAtRowSecRi Rte_VCU1Mid3CanFr11_SG_BltLockStAtRowSecRi;
  Rte_DE_SG_DoorPassRePosnStsToAPI Rte_VCU1Mid3CanFr11_SG_DoorPassRePosnStsToAPI;
  Rte_DE_ExtDcDcActvnAllwd Rte_VCU1Mid3CanFr12_ExtDcDcActvnAllwd;
  Rte_DE_PtTqAtAxleAvlFrntMax Rte_VCU1Mid3CanFr12_PtTqAtAxleAvlFrntMax;
  Rte_DE_PtTqAtAxleAvlReMax Rte_VCU1Mid3CanFr12_PtTqAtAxleAvlReMax;
  Rte_DE_SG_BrkPedlVal Rte_VCU1Mid3CanFr12_SG_BrkPedlVal;
  Rte_DE_SG_ClstrSts2ForAutnmsDrv Rte_VCU1Mid3CanFr12_SG_ClstrSts2ForAutnmsDrv;
  Rte_DE_AdActvnOkFromVehDyn Rte_VCU1Mid3CanFr13_AdActvnOkFromVehDyn;
  Rte_DE_PtTqAtAxleAvlReMaxLong Rte_VCU1Mid3CanFr13_PtTqAtAxleAvlReMaxLong;
  Rte_DE_SG_AutnmsDrvModMngtGlbSafe1 Rte_VCU1Mid3CanFr13_SG_AutnmsDrvModMngtGlbSafe1;
  Rte_DE_SG_EngFltIndcn Rte_VCU1Mid3CanFr13_SG_EngFltIndcn;
  Rte_DE_CllsnFwdWarnReq Rte_VCU1Mid3CanFr14_CllsnFwdWarnReq;
  Rte_DE_CllsnThreat Rte_VCU1Mid3CanFr14_CllsnThreat;
  Rte_DE_CnclReqForCrsCtrl Rte_VCU1Mid3CanFr14_CnclReqForCrsCtrl;
  Rte_DE_CooltFlowInDtElecForExt Rte_VCU1Mid3CanFr14_CooltFlowInDtElecForExt;
  Rte_DE_CooltTInDtElecForExt Rte_VCU1Mid3CanFr14_CooltTInDtElecForExt;
  Rte_DE_IndcrDisp1WdSts Rte_VCU1Mid3CanFr14_IndcrDisp1WdSts;
  Rte_DE_IndcrTurnSts1WdSts Rte_VCU1Mid3CanFr14_IndcrTurnSts1WdSts;
  Rte_DE_SG_CllsnAidSnvtySeld Rte_VCU1Mid3CanFr14_SG_CllsnAidSnvtySeld;
  Rte_DE_SrvRqrdForCllsnAid Rte_VCU1Mid3CanFr14_SrvRqrdForCllsnAid;
  Rte_DE_AsySftyDecelEnadByVehDyn Rte_VCU1Mid3CanFr16_AsySftyDecelEnadByVehDyn;
  Rte_DE_EngOilPWarn Rte_VCU1Mid3CanFr16_EngOilPWarn;
  Rte_DE_InhbOfAsySftyDecelByVehDyn Rte_VCU1Mid3CanFr16_InhbOfAsySftyDecelByVehDyn;
  Rte_DE_PrpsnErrIndcnReq Rte_VCU1Mid3CanFr16_PrpsnErrIndcnReq;
  Rte_DE_SG_RoadLoadNom Rte_VCU1Mid3CanFr16_SG_RoadLoadNom;
  Rte_DE_TankFlapSts Rte_VCU1Mid3CanFr16_TankFlapSts;
  Rte_DE_TrsmFltIndcn Rte_VCU1Mid3CanFr16_TrsmFltIndcn;
  Rte_DE_DoorDrvrReSts Rte_VCU1Mid3CanFr17_DoorDrvrReSts;
  Rte_DE_DoorDrvrSts Rte_VCU1Mid3CanFr17_DoorDrvrSts;
  Rte_DE_DoorPassReSts Rte_VCU1Mid3CanFr17_DoorPassReSts;
  Rte_DE_DoorPassSts Rte_VCU1Mid3CanFr17_DoorPassSts;
  Rte_DE_EngOilLvlSts Rte_VCU1Mid3CanFr17_EngOilLvlSts;
  Rte_DE_HoodSts Rte_VCU1Mid3CanFr17_HoodSts;
  Rte_DE_SG_ALnchTiDly3 Rte_VCU1Mid3CanFr17_SG_ALnchTiDly3;
  Rte_DE_SG_AlrmSts1 Rte_VCU1Mid3CanFr17_SG_AlrmSts1;
  Rte_DE_TrSts Rte_VCU1Mid3CanFr17_TrSts;
  Rte_DE_AsySftyBrkDlyEstimd Rte_VCU1Mid3CanFr18_AsySftyBrkDlyEstimd;
  Rte_DE_HvSysActvStsExt1 Rte_VCU1Mid3CanFr18_HvSysActvStsExt1;
  Rte_DE_SG_AccrPedlPsd Rte_VCU1Mid3CanFr18_SG_AccrPedlPsd;
  Rte_DE_SG_BrkPedlPsdSafeGroup Rte_VCU1Mid3CanFr18_SG_BrkPedlPsdSafeGroup;
  Rte_DE_SG_OvrdDecelByDrvr Rte_VCU1Mid3CanFr18_SG_OvrdDecelByDrvr;
  Rte_DE_AccrPedlRat Rte_VCU1Mid3CanFr19_AccrPedlRat;
  Rte_DE_ChrgnUReqExt Rte_VCU1Mid3CanFr19_ChrgnUReqExt;
  Rte_DE_DrvrDecelReq Rte_VCU1Mid3CanFr19_DrvrDecelReq;
  Rte_DE_PtGearAct Rte_VCU1Mid3CanFr19_PtGearAct;
  Rte_DE_SG_TirePWarnFrntRi Rte_VCU1Mid3CanFr19_SG_TirePWarnFrntRi;
  Rte_DE_SG_TirePWarnReLe Rte_VCU1Mid3CanFr19_SG_TirePWarnReLe;
  Rte_DE_SG_TirePWarnReRi Rte_VCU1Mid3CanFr19_SG_TirePWarnReRi;
  Rte_DE_PtTqAtAxleAvlFrntMaxLong Rte_VCU1Mid3CanFr22_PtTqAtAxleAvlFrntMaxLong;
  Rte_DE_SG_AdSecBrkActvnGroupSafe Rte_VCU1Mid3CanFr22_SG_AdSecBrkActvnGroupSafe;
  Rte_DE_SG_BrkFricTqTotAtWhlsAct Rte_VCU1Mid3CanFr22_SG_BrkFricTqTotAtWhlsAct;
  Rte_DE_EgyAvlChrgTot Rte_VCU1Mid3CanFr25_EgyAvlChrgTot;
  Rte_DE_EgyAvlDchaTot Rte_VCU1Mid3CanFr25_EgyAvlDchaTot;
  Rte_DE_SG_DrvrIntvSts Rte_VCU1Mid3CanFr25_SG_DrvrIntvSts;
  Rte_DE_SG_DrvrPrsntGroup Rte_VCU1Mid3CanFr25_SG_DrvrPrsntGroup;
  Rte_DE_BattChrgnTiEstimdExt Rte_VCU1Mid3CanFr29_BattChrgnTiEstimdExt;
  Rte_DE_BattIExt Rte_VCU1Mid3CanFr29_BattIExt;
  Rte_DE_BattUExt Rte_VCU1Mid3CanFr29_BattUExt;
  Rte_DE_ChrgnTypExt Rte_VCU1Mid3CanFr29_ChrgnTypExt;
  Rte_DE_ChrgrHndlStsExt Rte_VCU1Mid3CanFr29_ChrgrHndlStsExt;
  Rte_DE_SG_AutnmsDrvModMngtExtSafe Rte_VCU1Mid3CanFr30_SG_AutnmsDrvModMngtExtSafe;
  Rte_DE_SG_BrkDegraded Rte_VCU1Mid3CanFr30_SG_BrkDegraded;
  Rte_DE_VehManDeactvnReqInProgs1 Rte_VCU1Mid3CanFr30_VehManDeactvnReqInProgs1;
  Rte_DE_VehUsgSt Rte_VCU1Mid3CanFr30_VehUsgSt;
  Rte_DE_SG_StandStillMgrStsForHldSafe Rte_VCU1Mid3CanFr34_SG_StandStillMgrStsForHldSafe;
  Rte_DE_SG_SwtIndcrToAPI Rte_VCU1Mid3CanFr34_SG_SwtIndcrToAPI;
  Rte_DE_SwtBeamHiToAPI Rte_VCU1Mid3CanFr34_SwtBeamHiToAPI;
  Rte_DE_SwtLiHzrdWarnToAPI Rte_VCU1Mid3CanFr34_SwtLiHzrdWarnToAPI;
  Rte_DE_SG_ALgtMaxAvl Rte_VCU1Mid3CanFr36_SG_ALgtMaxAvl;
  Rte_DE_SG_WhlLockSts Rte_VCU1Mid3CanFr36_SG_WhlLockSts;
  Rte_DE_SG_AdSecWhlAgReqGroupSafe_A Rte_VIMBMid6CanFdFr14_ACU_SG_AdSecWhlAgReqGroupSafe_A;
  Rte_DE_SG_AdSecALgtLimReqGroupSafe_A Rte_VIMBMid6CanFdFr28_ACU_SG_AdSecALgtLimReqGroupSafe_A;
  Rte_DE_SG_SecAdNomALgtReqGroupSafe_A Rte_VIMBMid6CanFdFr28_ACU_SG_SecAdNomALgtReqGroupSafe_A;
  Rte_DE_SG_AutnmsDrvStReq_A Rte_VIMMid3CanFr07_ACU_SG_AutnmsDrvStReq_A;
  Rte_DE_SG_AdNomALgtReqGroupSafe_A Rte_VIMMid3CanFr14_ACU_SG_AdNomALgtReqGroupSafe_A;
  Rte_DE_SG_AdPrimALgtLimReqGroupSafe_A Rte_VIMMid3CanFr15_ACU_SG_AdPrimALgtLimReqGroupSafe_A;
  Rte_DE_SG_AdPrimPose_A Rte_VIMMid5CanFdFr02_ACU_SG_AdPrimPose_A;
  Rte_DE_SG_AdPrimWhlAgReqGroupSafe_A Rte_VIMMid5CanFdFr12_ACU_SG_AdPrimWhlAgReqGroupSafe_A;
} Rte_tsRB_VehSigInput_VehSigInput_10ms_Runnable; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_FC_LineTiStamp Rte_FC_191_FC_LineTiStamp;
  Rte_DE_FC_Line_01_HeadingAngle Rte_FC_191_FC_Line_01_HeadingAngle;
  Rte_DE_FC_Line_01_Id Rte_FC_191_FC_Line_01_Id;
  Rte_DE_FC_Line_01_Type Rte_FC_191_FC_Line_01_Type;
  Rte_DE_FC_Line_01_Width Rte_FC_191_FC_Line_01_Width;
  Rte_DE_FC_Line_01_color Rte_FC_191_FC_Line_01_color;
  Rte_DE_FC_Line_01_dx_End Rte_FC_191_FC_Line_01_dx_End;
  Rte_DE_FC_Line_01_dx_End_std Rte_FC_191_FC_Line_01_dx_End_std;
  Rte_DE_FC_Line_01_dx_Start Rte_FC_191_FC_Line_01_dx_Start;
  Rte_DE_FC_Line_01_dx_Start_std Rte_FC_191_FC_Line_01_dx_Start_std;
  Rte_DE_FC_Line_01_dy Rte_FC_191_FC_Line_01_dy;
  Rte_DE_FC_Line_01_exist_prob Rte_FC_191_FC_Line_01_exist_prob;
  Rte_DE_FC_Line_02_HeadingAngle Rte_FC_191_FC_Line_02_HeadingAngle;
  Rte_DE_FC_Line_02_Id Rte_FC_191_FC_Line_02_Id;
  Rte_DE_FC_Line_02_Type Rte_FC_191_FC_Line_02_Type;
  Rte_DE_FC_Line_02_Width Rte_FC_191_FC_Line_02_Width;
  Rte_DE_FC_Line_02_color Rte_FC_191_FC_Line_02_color;
  Rte_DE_FC_Line_02_dx_End Rte_FC_191_FC_Line_02_dx_End;
  Rte_DE_FC_Line_02_dx_End_std Rte_FC_191_FC_Line_02_dx_End_std;
  Rte_DE_FC_Line_02_dx_Start Rte_FC_191_FC_Line_02_dx_Start;
  Rte_DE_FC_Line_02_dx_Start_std Rte_FC_191_FC_Line_02_dx_Start_std;
  Rte_DE_FC_Line_02_dy Rte_FC_191_FC_Line_02_dy;
  Rte_DE_FC_Line_02_exist_prob Rte_FC_191_FC_Line_02_exist_prob;
  Rte_DE_FC_Line_03_HeadingAngle Rte_FC_191_FC_Line_03_HeadingAngle;
  Rte_DE_FC_Line_03_Id Rte_FC_191_FC_Line_03_Id;
  Rte_DE_FC_Line_03_Type Rte_FC_191_FC_Line_03_Type;
  Rte_DE_FC_Line_03_Width Rte_FC_191_FC_Line_03_Width;
  Rte_DE_FC_Line_03_color Rte_FC_191_FC_Line_03_color;
  Rte_DE_FC_Line_03_dx_End Rte_FC_191_FC_Line_03_dx_End;
  Rte_DE_FC_Line_03_dx_End_std Rte_FC_191_FC_Line_03_dx_End_std;
  Rte_DE_FC_Line_03_dx_Start Rte_FC_191_FC_Line_03_dx_Start;
  Rte_DE_FC_Line_03_dx_Start_std Rte_FC_191_FC_Line_03_dx_Start_std;
  Rte_DE_FC_Line_03_dy Rte_FC_191_FC_Line_03_dy;
  Rte_DE_FC_Line_03_exist_prob Rte_FC_191_FC_Line_03_exist_prob;
  Rte_DE_FTFC_Line_01_MeasureType Rte_FC_191_FTFC_Line_01_MeasureType;
  Rte_DE_FTFC_Line_01_ObstacleFlg Rte_FC_191_FTFC_Line_01_ObstacleFlg;
  Rte_DE_FTFC_Line_01_ParseConf Rte_FC_191_FTFC_Line_01_ParseConf;
  Rte_DE_FTFC_Line_01_RMSE Rte_FC_191_FTFC_Line_01_RMSE;
  Rte_DE_FTFC_Line_01_curvature_alte Rte_FC_191_FTFC_Line_01_curvature_alte;
  Rte_DE_FTFC_Line_01_curve Rte_FC_191_FTFC_Line_01_curve;
  Rte_DE_FTFC_Line_02_MeasureType Rte_FC_191_FTFC_Line_02_MeasureType;
  Rte_DE_FTFC_Line_02_ObstacleFlg Rte_FC_191_FTFC_Line_02_ObstacleFlg;
  Rte_DE_FTFC_Line_02_ParseConf Rte_FC_191_FTFC_Line_02_ParseConf;
  Rte_DE_FTFC_Line_02_RMSE Rte_FC_191_FTFC_Line_02_RMSE;
  Rte_DE_FTFC_Line_02_curvature_alte Rte_FC_191_FTFC_Line_02_curvature_alte;
  Rte_DE_FTFC_Line_02_curve Rte_FC_191_FTFC_Line_02_curve;
  Rte_DE_FTFC_Line_03_MeasureType Rte_FC_191_FTFC_Line_03_MeasureType;
  Rte_DE_FTFC_Line_03_curvature_alte Rte_FC_191_FTFC_Line_03_curvature_alte;
  Rte_DE_FTFC_Line_03_curve Rte_FC_191_FTFC_Line_03_curve;
  Rte_DE_FC_Line_04_HeadingAngle Rte_FC_192_FC_Line_04_HeadingAngle;
  Rte_DE_FC_Line_04_Id Rte_FC_192_FC_Line_04_Id;
  Rte_DE_FC_Line_04_Type Rte_FC_192_FC_Line_04_Type;
  Rte_DE_FC_Line_04_Width Rte_FC_192_FC_Line_04_Width;
  Rte_DE_FC_Line_04_color Rte_FC_192_FC_Line_04_color;
  Rte_DE_FC_Line_04_dx_End Rte_FC_192_FC_Line_04_dx_End;
  Rte_DE_FC_Line_04_dx_End_std Rte_FC_192_FC_Line_04_dx_End_std;
  Rte_DE_FC_Line_04_dx_Start Rte_FC_192_FC_Line_04_dx_Start;
  Rte_DE_FC_Line_04_dx_Start_std Rte_FC_192_FC_Line_04_dx_Start_std;
  Rte_DE_FC_Line_04_dy Rte_FC_192_FC_Line_04_dy;
  Rte_DE_FC_Line_04_exist_prob Rte_FC_192_FC_Line_04_exist_prob;
  Rte_DE_FC_Line_05_HeadingAngle Rte_FC_192_FC_Line_05_HeadingAngle;
  Rte_DE_FC_Line_05_Type Rte_FC_192_FC_Line_05_Type;
  Rte_DE_FC_Line_05_Width Rte_FC_192_FC_Line_05_Width;
  Rte_DE_FC_Line_05_color Rte_FC_192_FC_Line_05_color;
  Rte_DE_FC_Line_05_dx_End Rte_FC_192_FC_Line_05_dx_End;
  Rte_DE_FC_Line_05_dx_End_std Rte_FC_192_FC_Line_05_dx_End_std;
  Rte_DE_FC_Line_05_dx_Start Rte_FC_192_FC_Line_05_dx_Start;
  Rte_DE_FC_Line_05_dx_Start_std Rte_FC_192_FC_Line_05_dx_Start_std;
  Rte_DE_FC_Line_05_dy Rte_FC_192_FC_Line_05_dy;
  Rte_DE_FC_Line_05_exist_prob Rte_FC_192_FC_Line_05_exist_prob;
  Rte_DE_FC_Line_06_HeadingAngle Rte_FC_192_FC_Line_06_HeadingAngle;
  Rte_DE_FC_Line_06_Type Rte_FC_192_FC_Line_06_Type;
  Rte_DE_FC_Line_06_Width Rte_FC_192_FC_Line_06_Width;
  Rte_DE_FC_Line_06_color Rte_FC_192_FC_Line_06_color;
  Rte_DE_FC_Line_06_dx_End Rte_FC_192_FC_Line_06_dx_End;
  Rte_DE_FC_Line_06_dx_End_std Rte_FC_192_FC_Line_06_dx_End_std;
  Rte_DE_FC_Line_06_dx_Start Rte_FC_192_FC_Line_06_dx_Start;
  Rte_DE_FC_Line_06_dx_Start_std Rte_FC_192_FC_Line_06_dx_Start_std;
  Rte_DE_FC_Line_06_dy Rte_FC_192_FC_Line_06_dy;
  Rte_DE_FC_Line_06_exist_prob Rte_FC_192_FC_Line_06_exist_prob;
  Rte_DE_FTFC_Line_03_ObstacleFlg Rte_FC_192_FTFC_Line_03_ObstacleFlg;
  Rte_DE_FTFC_Line_03_ParseConf Rte_FC_192_FTFC_Line_03_ParseConf;
  Rte_DE_FTFC_Line_03_RMSE Rte_FC_192_FTFC_Line_03_RMSE;
  Rte_DE_FTFC_Line_04_MeasureType Rte_FC_192_FTFC_Line_04_MeasureType;
  Rte_DE_FTFC_Line_04_ObstacleFlg Rte_FC_192_FTFC_Line_04_ObstacleFlg;
  Rte_DE_FTFC_Line_04_ParseConf Rte_FC_192_FTFC_Line_04_ParseConf;
  Rte_DE_FTFC_Line_04_RMSE Rte_FC_192_FTFC_Line_04_RMSE;
  Rte_DE_FTFC_Line_04_curvature_alte Rte_FC_192_FTFC_Line_04_curvature_alte;
  Rte_DE_FTFC_Line_04_curve Rte_FC_192_FTFC_Line_04_curve;
  Rte_DE_FTFC_Line_05_MeasureType Rte_FC_192_FTFC_Line_05_MeasureType;
  Rte_DE_FTFC_Line_05_ObstacleFlg Rte_FC_192_FTFC_Line_05_ObstacleFlg;
  Rte_DE_FTFC_Line_05_ParseConf Rte_FC_192_FTFC_Line_05_ParseConf;
  Rte_DE_FTFC_Line_05_RMSE Rte_FC_192_FTFC_Line_05_RMSE;
  Rte_DE_FTFC_Line_05_curvature_alte Rte_FC_192_FTFC_Line_05_curvature_alte;
  Rte_DE_FTFC_Line_05_curve Rte_FC_192_FTFC_Line_05_curve;
  Rte_DE_FTFC_Line_06_MeasureType Rte_FC_192_FTFC_Line_06_MeasureType;
  Rte_DE_FTFC_Line_06_ObstacleFlg Rte_FC_192_FTFC_Line_06_ObstacleFlg;
  Rte_DE_FTFC_Line_06_ParseConf Rte_FC_192_FTFC_Line_06_ParseConf;
  Rte_DE_FTFC_Line_06_RMSE Rte_FC_192_FTFC_Line_06_RMSE;
  Rte_DE_FTFC_Line_06_curvature_alte Rte_FC_192_FTFC_Line_06_curvature_alte;
  Rte_DE_FTFC_Line_06_curve Rte_FC_192_FTFC_Line_06_curve;
  Rte_DE_FC_CIPV_ID Rte_FC_19D_FC_CIPV_ID;
  Rte_DE_FC_Obj20_Ax Rte_FC_19D_FC_Obj20_Ax;
  Rte_DE_FC_Obj20_Ay Rte_FC_19D_FC_Obj20_Ay;
  Rte_DE_FC_Obj20_ClassifiedView Rte_FC_19D_FC_Obj20_ClassifiedView;
  Rte_DE_FC_Obj20_Dx Rte_FC_19D_FC_Obj20_Dx;
  Rte_DE_FC_Obj20_Dx_Vnce Rte_FC_19D_FC_Obj20_Dx_Vnce;
  Rte_DE_FC_Obj20_Dy Rte_FC_19D_FC_Obj20_Dy;
  Rte_DE_FC_Obj20_Dy_Vnce Rte_FC_19D_FC_Obj20_Dy_Vnce;
  Rte_DE_FC_Obj20_ExistProb Rte_FC_19D_FC_Obj20_ExistProb;
  Rte_DE_FC_Obj20_HeadingAngle Rte_FC_19D_FC_Obj20_HeadingAngle;
  Rte_DE_FC_Obj20_Height Rte_FC_19D_FC_Obj20_Height;
  Rte_DE_FC_Obj20_LaneAssignment Rte_FC_19D_FC_Obj20_LaneAssignment;
  Rte_DE_FC_Obj20_Length Rte_FC_19D_FC_Obj20_Length;
  Rte_DE_FC_Obj20_MotionType Rte_FC_19D_FC_Obj20_MotionType;
  Rte_DE_FC_Obj20_Track_Age Rte_FC_19D_FC_Obj20_Track_Age;
  Rte_DE_FC_Obj20_Track_ID Rte_FC_19D_FC_Obj20_Track_ID;
  Rte_DE_FC_Obj20_Type Rte_FC_19D_FC_Obj20_Type;
  Rte_DE_FC_Obj20_Vx Rte_FC_19D_FC_Obj20_Vx;
  Rte_DE_FC_Obj20_Vy Rte_FC_19D_FC_Obj20_Vy;
  Rte_DE_FC_Obj20_Width Rte_FC_19D_FC_Obj20_Width;
  Rte_DE_FC_obj20_Brakelight_Info Rte_FC_19D_FC_obj20_Brakelight_Info;
  Rte_DE_FC_obj20_Taillight_Info Rte_FC_19D_FC_obj20_Taillight_Info;
  Rte_DE_FTFC_Obj20_CenterAngle Rte_FC_19D_FTFC_Obj20_CenterAngle;
  Rte_DE_FTFC_Obj20_CornerPoint_x Rte_FC_19D_FTFC_Obj20_CornerPoint_x;
  Rte_DE_FTFC_Obj20_CornerPoint_y Rte_FC_19D_FTFC_Obj20_CornerPoint_y;
  Rte_DE_FTFC_Obj20_DistInLane Rte_FC_19D_FTFC_Obj20_DistInLane;
  Rte_DE_FTFC_Obj20_objCutInFlag Rte_FC_19D_FTFC_Obj20_objCutInFlag;
  Rte_DE_FTFC_Obj20_objCutInLane Rte_FC_19D_FTFC_Obj20_objCutInLane;
  Rte_DE_FrFr_AccOBJ_Class Rte_FC_19D_FrFr_AccOBJ_Class;
  Rte_DE_FrFr_AccOBJ_Width Rte_FC_19D_FrFr_AccOBJ_Width;
  Rte_DE_Fr_AccOBJ_Ax Rte_FC_19D_Fr_AccOBJ_Ax;
  Rte_DE_Fr_AccOBJ_Ay Rte_FC_19D_Fr_AccOBJ_Ay;
  Rte_DE_Fr_AccOBJ_Brakelight_Info Rte_FC_19D_Fr_AccOBJ_Brakelight_Info;
  Rte_DE_Fr_AccOBJ_Class Rte_FC_19D_Fr_AccOBJ_Class;
  Rte_DE_Fr_AccOBJ_Dx Rte_FC_19D_Fr_AccOBJ_Dx;
  Rte_DE_Fr_AccOBJ_Dx_Vnce Rte_FC_19D_Fr_AccOBJ_Dx_Vnce;
  Rte_DE_Fr_AccOBJ_Dy Rte_FC_19D_Fr_AccOBJ_Dy;
  Rte_DE_Fr_AccOBJ_Dy_Vnce Rte_FC_19D_Fr_AccOBJ_Dy_Vnce;
  Rte_DE_Fr_AccOBJ_ExistProb Rte_FC_19D_Fr_AccOBJ_ExistProb;
  Rte_DE_Fr_AccOBJ_FusionedFC_Track_ID Rte_FC_19D_Fr_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_Fr_AccOBJ_HeadingAngle Rte_FC_19D_Fr_AccOBJ_HeadingAngle;
  Rte_DE_Fr_AccOBJ_Height Rte_FC_19D_Fr_AccOBJ_Height;
  Rte_DE_Fr_AccOBJ_Length Rte_FC_19D_Fr_AccOBJ_Length;
  Rte_DE_Fr_AccOBJ_ObstacleProb Rte_FC_19D_Fr_AccOBJ_ObstacleProb;
  Rte_DE_Fr_AccOBJ_Taillight_Info Rte_FC_19D_Fr_AccOBJ_Taillight_Info;
  Rte_DE_Fr_AccOBJ_Track_Age Rte_FC_19D_Fr_AccOBJ_Track_Age;
  Rte_DE_Fr_AccOBJ_Track_ID Rte_FC_19D_Fr_AccOBJ_Track_ID;
  Rte_DE_Fr_AccOBJ_Vx Rte_FC_19D_Fr_AccOBJ_Vx;
  Rte_DE_Fr_AccOBJ_Vx_std Rte_FC_19D_Fr_AccOBJ_Vx_std;
  Rte_DE_Fr_AccOBJ_Vy Rte_FC_19D_Fr_AccOBJ_Vy;
  Rte_DE_Fr_AccOBJ_Vy_std Rte_FC_19D_Fr_AccOBJ_Vy_std;
  Rte_DE_Fr_AccOBJ_Width Rte_FC_19D_Fr_AccOBJ_Width;
  Rte_DE_Fr_AccOBJ_fusion_Sts Rte_FC_19D_Fr_AccOBJ_fusion_Sts;
  Rte_DE_FrFr_AccOBJ_Ax Rte_FC_19E_FrFr_AccOBJ_Ax;
  Rte_DE_FrFr_AccOBJ_Ay Rte_FC_19E_FrFr_AccOBJ_Ay;
  Rte_DE_FrFr_AccOBJ_Brakelight_Info Rte_FC_19E_FrFr_AccOBJ_Brakelight_Info;
  Rte_DE_FrFr_AccOBJ_Dx Rte_FC_19E_FrFr_AccOBJ_Dx;
  Rte_DE_FrFr_AccOBJ_Dx_Vnce Rte_FC_19E_FrFr_AccOBJ_Dx_Vnce;
  Rte_DE_FrFr_AccOBJ_Dy Rte_FC_19E_FrFr_AccOBJ_Dy;
  Rte_DE_FrFr_AccOBJ_Dy_Vnce Rte_FC_19E_FrFr_AccOBJ_Dy_Vnce;
  Rte_DE_FrFr_AccOBJ_ExistProb Rte_FC_19E_FrFr_AccOBJ_ExistProb;
  Rte_DE_FrFr_AccOBJ_FusionedFC_Track_ID Rte_FC_19E_FrFr_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_FrFr_AccOBJ_HeadingAngle Rte_FC_19E_FrFr_AccOBJ_HeadingAngle;
  Rte_DE_FrFr_AccOBJ_Height Rte_FC_19E_FrFr_AccOBJ_Height;
  Rte_DE_FrFr_AccOBJ_Length Rte_FC_19E_FrFr_AccOBJ_Length;
  Rte_DE_FrFr_AccOBJ_ObstacleProb Rte_FC_19E_FrFr_AccOBJ_ObstacleProb;
  Rte_DE_FrFr_AccOBJ_Taillight_Info Rte_FC_19E_FrFr_AccOBJ_Taillight_Info;
  Rte_DE_FrFr_AccOBJ_Track_Age Rte_FC_19E_FrFr_AccOBJ_Track_Age;
  Rte_DE_FrFr_AccOBJ_Track_ID Rte_FC_19E_FrFr_AccOBJ_Track_ID;
  Rte_DE_FrFr_AccOBJ_Vx Rte_FC_19E_FrFr_AccOBJ_Vx;
  Rte_DE_FrFr_AccOBJ_Vx_std Rte_FC_19E_FrFr_AccOBJ_Vx_std;
  Rte_DE_FrFr_AccOBJ_Vy Rte_FC_19E_FrFr_AccOBJ_Vy;
  Rte_DE_FrFr_AccOBJ_Vy_std Rte_FC_19E_FrFr_AccOBJ_Vy_std;
  Rte_DE_FrFr_AccOBJ_fusion_Sts Rte_FC_19E_FrFr_AccOBJ_fusion_Sts;
  Rte_DE_LeFr_AccOBJ_Class Rte_FC_19E_LeFr_AccOBJ_Class;
  Rte_DE_LeFr_AccOBJ_Dy Rte_FC_19E_LeFr_AccOBJ_Dy;
  Rte_DE_LeFr_AccOBJ_Dy_Vnce Rte_FC_19E_LeFr_AccOBJ_Dy_Vnce;
  Rte_DE_LeFr_AccOBJ_HeadingAngle Rte_FC_19E_LeFr_AccOBJ_HeadingAngle;
  Rte_DE_LeFr_AccOBJ_Height Rte_FC_19E_LeFr_AccOBJ_Height;
  Rte_DE_LeFr_AccOBJ_Length Rte_FC_19E_LeFr_AccOBJ_Length;
  Rte_DE_LeFr_AccOBJ_Width Rte_FC_19E_LeFr_AccOBJ_Width;
  Rte_DE_Le_AccOBJ_Ax Rte_FC_19E_Le_AccOBJ_Ax;
  Rte_DE_Le_AccOBJ_Ay Rte_FC_19E_Le_AccOBJ_Ay;
  Rte_DE_Le_AccOBJ_Brakelight_Info Rte_FC_19E_Le_AccOBJ_Brakelight_Info;
  Rte_DE_Le_AccOBJ_Class Rte_FC_19E_Le_AccOBJ_Class;
  Rte_DE_Le_AccOBJ_Dx Rte_FC_19E_Le_AccOBJ_Dx;
  Rte_DE_Le_AccOBJ_Dx_Vnce Rte_FC_19E_Le_AccOBJ_Dx_Vnce;
  Rte_DE_Le_AccOBJ_Dy Rte_FC_19E_Le_AccOBJ_Dy;
  Rte_DE_Le_AccOBJ_Dy_Vnce Rte_FC_19E_Le_AccOBJ_Dy_Vnce;
  Rte_DE_Le_AccOBJ_ExistProb Rte_FC_19E_Le_AccOBJ_ExistProb;
  Rte_DE_Le_AccOBJ_FusionedFC_Track_ID Rte_FC_19E_Le_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_Le_AccOBJ_HeadingAngle Rte_FC_19E_Le_AccOBJ_HeadingAngle;
  Rte_DE_Le_AccOBJ_Height Rte_FC_19E_Le_AccOBJ_Height;
  Rte_DE_Le_AccOBJ_Length Rte_FC_19E_Le_AccOBJ_Length;
  Rte_DE_Le_AccOBJ_ObstacleProb Rte_FC_19E_Le_AccOBJ_ObstacleProb;
  Rte_DE_Le_AccOBJ_Taillight_Info Rte_FC_19E_Le_AccOBJ_Taillight_Info;
  Rte_DE_Le_AccOBJ_Track_Age Rte_FC_19E_Le_AccOBJ_Track_Age;
  Rte_DE_Le_AccOBJ_Track_ID Rte_FC_19E_Le_AccOBJ_Track_ID;
  Rte_DE_Le_AccOBJ_Vx Rte_FC_19E_Le_AccOBJ_Vx;
  Rte_DE_Le_AccOBJ_Vx_std Rte_FC_19E_Le_AccOBJ_Vx_std;
  Rte_DE_Le_AccOBJ_Vy Rte_FC_19E_Le_AccOBJ_Vy;
  Rte_DE_Le_AccOBJ_Vy_std Rte_FC_19E_Le_AccOBJ_Vy_std;
  Rte_DE_Le_AccOBJ_Width Rte_FC_19E_Le_AccOBJ_Width;
  Rte_DE_Le_AccOBJ_fusion_Sts Rte_FC_19E_Le_AccOBJ_fusion_Sts;
  Rte_DE_LeFr_AccOBJ_Ax Rte_FC_19F_LeFr_AccOBJ_Ax;
  Rte_DE_LeFr_AccOBJ_Ay Rte_FC_19F_LeFr_AccOBJ_Ay;
  Rte_DE_LeFr_AccOBJ_Brakelight_Info Rte_FC_19F_LeFr_AccOBJ_Brakelight_Info;
  Rte_DE_LeFr_AccOBJ_Dx Rte_FC_19F_LeFr_AccOBJ_Dx;
  Rte_DE_LeFr_AccOBJ_Dx_Vnce Rte_FC_19F_LeFr_AccOBJ_Dx_Vnce;
  Rte_DE_LeFr_AccOBJ_ExistProb Rte_FC_19F_LeFr_AccOBJ_ExistProb;
  Rte_DE_LeFr_AccOBJ_FusionedFC_Track_ID Rte_FC_19F_LeFr_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_LeFr_AccOBJ_ObstacleProb Rte_FC_19F_LeFr_AccOBJ_ObstacleProb;
  Rte_DE_LeFr_AccOBJ_Taillight_Info Rte_FC_19F_LeFr_AccOBJ_Taillight_Info;
  Rte_DE_LeFr_AccOBJ_Track_Age Rte_FC_19F_LeFr_AccOBJ_Track_Age;
  Rte_DE_LeFr_AccOBJ_Track_ID Rte_FC_19F_LeFr_AccOBJ_Track_ID;
  Rte_DE_LeFr_AccOBJ_Vx Rte_FC_19F_LeFr_AccOBJ_Vx;
  Rte_DE_LeFr_AccOBJ_Vx_std Rte_FC_19F_LeFr_AccOBJ_Vx_std;
  Rte_DE_LeFr_AccOBJ_Vy Rte_FC_19F_LeFr_AccOBJ_Vy;
  Rte_DE_LeFr_AccOBJ_Vy_std Rte_FC_19F_LeFr_AccOBJ_Vy_std;
  Rte_DE_LeFr_AccOBJ_fusion_Sts Rte_FC_19F_LeFr_AccOBJ_fusion_Sts;
  Rte_DE_RiFr_AccOBJ_Class Rte_FC_19F_RiFr_AccOBJ_Class;
  Rte_DE_RiFr_AccOBJ_Dx Rte_FC_19F_RiFr_AccOBJ_Dx;
  Rte_DE_RiFr_AccOBJ_Dx_Vnce Rte_FC_19F_RiFr_AccOBJ_Dx_Vnce;
  Rte_DE_RiFr_AccOBJ_Dy Rte_FC_19F_RiFr_AccOBJ_Dy;
  Rte_DE_RiFr_AccOBJ_Dy_Vnce Rte_FC_19F_RiFr_AccOBJ_Dy_Vnce;
  Rte_DE_RiFr_AccOBJ_HeadingAngle Rte_FC_19F_RiFr_AccOBJ_HeadingAngle;
  Rte_DE_RiFr_AccOBJ_Height Rte_FC_19F_RiFr_AccOBJ_Height;
  Rte_DE_RiFr_AccOBJ_Length Rte_FC_19F_RiFr_AccOBJ_Length;
  Rte_DE_RiFr_AccOBJ_Vy Rte_FC_19F_RiFr_AccOBJ_Vy;
  Rte_DE_RiFr_AccOBJ_Vy_std Rte_FC_19F_RiFr_AccOBJ_Vy_std;
  Rte_DE_RiFr_AccOBJ_Width Rte_FC_19F_RiFr_AccOBJ_Width;
  Rte_DE_Ri_AccOBJ_Ax Rte_FC_19F_Ri_AccOBJ_Ax;
  Rte_DE_Ri_AccOBJ_Ay Rte_FC_19F_Ri_AccOBJ_Ay;
  Rte_DE_Ri_AccOBJ_Brakelight_Info Rte_FC_19F_Ri_AccOBJ_Brakelight_Info;
  Rte_DE_Ri_AccOBJ_Class Rte_FC_19F_Ri_AccOBJ_Class;
  Rte_DE_Ri_AccOBJ_Dx Rte_FC_19F_Ri_AccOBJ_Dx;
  Rte_DE_Ri_AccOBJ_Dx_Vnce Rte_FC_19F_Ri_AccOBJ_Dx_Vnce;
  Rte_DE_Ri_AccOBJ_Dy Rte_FC_19F_Ri_AccOBJ_Dy;
  Rte_DE_Ri_AccOBJ_Dy_Vnce Rte_FC_19F_Ri_AccOBJ_Dy_Vnce;
  Rte_DE_Ri_AccOBJ_ExistProb Rte_FC_19F_Ri_AccOBJ_ExistProb;
  Rte_DE_Ri_AccOBJ_FusionedFC_Track_ID Rte_FC_19F_Ri_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_Ri_AccOBJ_HeadingAngle Rte_FC_19F_Ri_AccOBJ_HeadingAngle;
  Rte_DE_Ri_AccOBJ_Height Rte_FC_19F_Ri_AccOBJ_Height;
  Rte_DE_Ri_AccOBJ_Length Rte_FC_19F_Ri_AccOBJ_Length;
  Rte_DE_Ri_AccOBJ_ObstacleProb Rte_FC_19F_Ri_AccOBJ_ObstacleProb;
  Rte_DE_Ri_AccOBJ_Taillight_Info Rte_FC_19F_Ri_AccOBJ_Taillight_Info;
  Rte_DE_Ri_AccOBJ_Track_Age Rte_FC_19F_Ri_AccOBJ_Track_Age;
  Rte_DE_Ri_AccOBJ_Track_ID Rte_FC_19F_Ri_AccOBJ_Track_ID;
  Rte_DE_Ri_AccOBJ_Vx Rte_FC_19F_Ri_AccOBJ_Vx;
  Rte_DE_Ri_AccOBJ_Vx_std Rte_FC_19F_Ri_AccOBJ_Vx_std;
  Rte_DE_Ri_AccOBJ_Vy Rte_FC_19F_Ri_AccOBJ_Vy;
  Rte_DE_Ri_AccOBJ_Vy_std Rte_FC_19F_Ri_AccOBJ_Vy_std;
  Rte_DE_Ri_AccOBJ_Width Rte_FC_19F_Ri_AccOBJ_Width;
  Rte_DE_Ri_AccOBJ_fusion_Sts Rte_FC_19F_Ri_AccOBJ_fusion_Sts;
  Rte_DE_FrFr_AccOBJ_confi Rte_FC_1A0_FrFr_AccOBJ_confi;
  Rte_DE_Fr_AccOBJ_confi Rte_FC_1A0_Fr_AccOBJ_confi;
  Rte_DE_LeFr_AccOBJ_confi Rte_FC_1A0_LeFr_AccOBJ_confi;
  Rte_DE_Le_AccOBJ_confi Rte_FC_1A0_Le_AccOBJ_confi;
  Rte_DE_RiFr_AccOBJ_Ax Rte_FC_1A0_RiFr_AccOBJ_Ax;
  Rte_DE_RiFr_AccOBJ_Ay Rte_FC_1A0_RiFr_AccOBJ_Ay;
  Rte_DE_RiFr_AccOBJ_Brakelight_Info Rte_FC_1A0_RiFr_AccOBJ_Brakelight_Info;
  Rte_DE_RiFr_AccOBJ_ExistProb Rte_FC_1A0_RiFr_AccOBJ_ExistProb;
  Rte_DE_RiFr_AccOBJ_FusionedFC_Track_ID Rte_FC_1A0_RiFr_AccOBJ_FusionedFC_Track_ID;
  Rte_DE_RiFr_AccOBJ_ObstacleProb Rte_FC_1A0_RiFr_AccOBJ_ObstacleProb;
  Rte_DE_RiFr_AccOBJ_Taillight_Info Rte_FC_1A0_RiFr_AccOBJ_Taillight_Info;
  Rte_DE_RiFr_AccOBJ_Track_Age Rte_FC_1A0_RiFr_AccOBJ_Track_Age;
  Rte_DE_RiFr_AccOBJ_Track_ID Rte_FC_1A0_RiFr_AccOBJ_Track_ID;
  Rte_DE_RiFr_AccOBJ_Vx Rte_FC_1A0_RiFr_AccOBJ_Vx;
  Rte_DE_RiFr_AccOBJ_Vx_std Rte_FC_1A0_RiFr_AccOBJ_Vx_std;
  Rte_DE_RiFr_AccOBJ_confi Rte_FC_1A0_RiFr_AccOBJ_confi;
  Rte_DE_RiFr_AccOBJ_fusion_Sts Rte_FC_1A0_RiFr_AccOBJ_fusion_Sts;
  Rte_DE_Ri_AccOBJ_confi Rte_FC_1A0_Ri_AccOBJ_confi;
  Rte_DE_FC_FrontCameraCalibrationStatus Rte_FC_1A5_FC_FrontCameraCalibrationStatus;
  Rte_DE_FC_LaneChangeStatus Rte_FC_1A5_FC_LaneChangeStatus;
  Rte_DE_FC_RollingCounter Rte_FC_1A5_FC_RollingCounter;
} Rte_tsRB_CameraSigInput_CameraSigInput_10ms_Runnable; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_AcuPoseRollingCounter Rte_AcuPose_AcuPoseRollingCounter;
  Rte_DE_GnssPoseHeading Rte_AcuPose_GnssPoseHeading;
  Rte_DE_GnssPoseHeadingType Rte_AcuPose_GnssPoseHeadingType;
  Rte_DE_GnssPoseNumSatsTracked Rte_AcuPose_GnssPoseNumSatsTracked;
  Rte_DE_GnssPosePosType Rte_AcuPose_GnssPosePosType;
  Rte_DE_GnssPosePositionX Rte_AcuPose_GnssPosePositionX;
  Rte_DE_GnssPosePositionY Rte_AcuPose_GnssPosePositionY;
  Rte_DE_GnssPoseReserve1 Rte_AcuPose_GnssPoseReserve1;
  Rte_DE_GnssPoseTimeStamp Rte_AcuPose_GnssPoseTimeStamp;
  Rte_DE_GnssPoseVelocityX Rte_AcuPose_GnssPoseVelocityX;
  Rte_DE_GnssPoseVelocityY Rte_AcuPose_GnssPoseVelocityY;
  Rte_DE_GnssPoseYaw Rte_AcuPose_GnssPoseYaw;
  Rte_DE_GnsssPoseReserve0 Rte_AcuPose_GnsssPoseReserve0;
  Rte_DE_PoseAccelerationX Rte_AcuPose_PoseAccelerationX;
  Rte_DE_PoseAccelerationY Rte_AcuPose_PoseAccelerationY;
  Rte_DE_PosePitch Rte_AcuPose_PosePitch;
  Rte_DE_PosePositionX Rte_AcuPose_PosePositionX;
  Rte_DE_PosePositionY Rte_AcuPose_PosePositionY;
  Rte_DE_PoseStatus Rte_AcuPose_PoseStatus;
  Rte_DE_PoseTimeStamp Rte_AcuPose_PoseTimeStamp;
  Rte_DE_PoseVelocityX Rte_AcuPose_PoseVelocityX;
  Rte_DE_PoseVelocityY Rte_AcuPose_PoseVelocityY;
  Rte_DE_PoseYaw Rte_AcuPose_PoseYaw;
  Rte_DE_PoseYawRate Rte_AcuPose_PoseYawRate;
} Rte_tsRB_AcuSigInput_PoseInput_10ms; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_uint32 Rte_Timestamp_Timestamp;
  Rte_DE_AcuControl Rte_AcuControl_AcuControl;
  Rte_DE_GnssPoseInput Rte_GnssPose_GnssPose;
  Rte_DE_LocalizationPoseInput Rte_LocalizationPose_LocalizationPose;
  Rte_DE_TrajectoryInput Rte_Trajectory_Trajectory;
  Rte_DE_VSI_VehicleInfo_Struct Rte_VSI_VehicleInfo_VSI_VehicleInfo;
  Rte_DE_ControlDebug Rte_ControlDebug_ControlDebug;
  Rte_DE_EstimationDebug Rte_EstimationDebug_EstimationDebug;
} Rte_tsRB_Control_Control_10ms; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_FRS_Fail Rte_FRS_Status_FRS_Fail;
  Rte_DE_FRS_HostSpeed Rte_FRS_Status_FRS_HostSpeed;
  Rte_DE_FRS_Host_Yaw Rte_FRS_Status_FRS_Host_Yaw;
  Rte_DE_FRS_Latency Rte_FRS_Status_FRS_Latency;
  Rte_DE_FRS_MeasEnabled Rte_FRS_Status_FRS_MeasEnabled;
  Rte_DE_FRS_Msg_AliveCounter Rte_FRS_Status_FRS_Msg_AliveCounter;
  Rte_DE_FRS_Msg_CheckSum Rte_FRS_Status_FRS_Msg_CheckSum;
  Rte_DE_FRS_Status_BlkProg Rte_FRS_Status_FRS_Status_BlkProg;
  Rte_DE_FRS_Status_HWErr Rte_FRS_Status_FRS_Status_HWErr;
  Rte_DE_FRS_Status_MisAlign Rte_FRS_Status_FRS_Status_MisAlign;
  Rte_DE_FRS_TimeStamp Rte_FRS_Status_FRS_TimeStamp;
  Rte_DE_CSI_LaneInfo_Struct Rte_CSI_LaneInfo_CSI_LaneInfo;
  Rte_DE_ControlOutput Rte_ControlCommand_ControlCommand;
  Rte_DE_LAT_CtrlCmd_Struct Rte_LAT_CtrlCmd_LAT_CtrlCmd;
  Rte_DE_LGT_CtrlCmd_Struct Rte_LGT_CtrlCmd_LGT_CtrlCmd;
  Rte_DE_VSI_McuCanTimeout_Struct Rte_VSI_McuCanTimeout_VSI_McuCanTimeout;
  Rte_DE_VSI_VehInfoFor1V1R_Struct Rte_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R;
  Rte_DE_VSI_VehicleInfo_Struct Rte_VSI_VehicleInfo_VSI_VehicleInfo;
  Rte_DE_FBS_DebugInfo_Struct Rte_FBS_DebugInfo_FBS_DebugInfo;
  Rte_DE_ESC_ABA_active Rte_ESC_DA_MESSAGE_ESC_ABA_active;
  Rte_DE_ESC_ABA_available Rte_ESC_DA_MESSAGE_ESC_ABA_available;
  Rte_DE_ESC_ABP_active Rte_ESC_DA_MESSAGE_ESC_ABP_active;
  Rte_DE_ESC_ABP_available Rte_ESC_DA_MESSAGE_ESC_ABP_available;
  Rte_DE_ESC_AEB_active Rte_ESC_DA_MESSAGE_ESC_AEB_active;
  Rte_DE_ESC_AEB_available Rte_ESC_DA_MESSAGE_ESC_AEB_available;
  Rte_DE_ESC_AWB_active Rte_ESC_DA_MESSAGE_ESC_AWB_active;
  Rte_DE_ESC_AWB_available Rte_ESC_DA_MESSAGE_ESC_AWB_available;
  Rte_DE_ESC_BrakeTempTooHigh Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh;
  Rte_DE_ESC_DA_MESSAGE_AliveCounter Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter;
  Rte_DE_ESC_DA_MESSAGE_Checksum Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum;
  Rte_DE_ESC_DTC_Active Rte_ESC_DA_MESSAGE_ESC_DTC_Active;
  Rte_DE_ESC_DiagExtModSts Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts;
  Rte_DE_ESC_NoBrakeForce Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce;
  Rte_DE_ESC_QDCFRS Rte_ESC_DA_MESSAGE_ESC_QDCFRS;
  Rte_DE_ESC_Vehiclestandstill Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill;
  Rte_DE_ESC_FLWheelDirection Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection;
  Rte_DE_ESC_FLWheelSpeedInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid;
  Rte_DE_ESC_FLWheelSpeedKPH Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH;
  Rte_DE_ESC_FRWheelDirection Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection;
  Rte_DE_ESC_FRWheelSpeedInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid;
  Rte_DE_ESC_FRWheelSpeedKPH Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH;
  Rte_DE_ESC_FrontWheelSpeedsKPH_AliveCounter Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter;
  Rte_DE_ESC_FrontWheelSpeedsKPH_Checksum Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum;
  Rte_DE_ESC_Mcylinder_Pressure Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure;
  Rte_DE_ESC_Mcylinder_PressureInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid;
  Rte_DE_ESC_RLWheelDirection Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection;
  Rte_DE_ESC_RLWheelSpeedInvalid Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid;
  Rte_DE_ESC_RLWheelSpeedKPH Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH;
  Rte_DE_ESC_RRWheelDirection Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection;
  Rte_DE_ESC_RRWheelSpeedInvalid Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid;
  Rte_DE_ESC_RRWheelSpeedKPH Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH;
  Rte_DE_ESC_RearWheelSpeedsKPH_AliveCounter Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter;
  Rte_DE_ESC_RearWheelSpeedsKPH_Checksum Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum;
  Rte_DE_ESC_ABSActive Rte_ESC_Status_ESC_ABSActive;
  Rte_DE_ESC_AVHStatus Rte_ESC_Status_ESC_AVHStatus;
  Rte_DE_ESC_BrakePedalSwitchInvalid Rte_ESC_Status_ESC_BrakePedalSwitchInvalid;
  Rte_DE_ESC_BrakePedalSwitchStatus Rte_ESC_Status_ESC_BrakePedalSwitchStatus;
  Rte_DE_ESC_EPBStatus Rte_ESC_Status_ESC_EPBStatus;
  Rte_DE_ESC_ESPActive Rte_ESC_Status_ESC_ESPActive;
  Rte_DE_ESC_ESPFailed Rte_ESC_Status_ESC_ESPFailed;
  Rte_DE_ESC_HHCActive Rte_ESC_Status_ESC_HHCActive;
  Rte_DE_ESC_PATAResponse Rte_ESC_Status_ESC_PATAResponse;
  Rte_DE_ESC_Status_AliveCounter Rte_ESC_Status_ESC_Status_AliveCounter;
  Rte_DE_ESC_Status_Checksum Rte_ESC_Status_ESC_Status_Checksum;
  Rte_DE_ESC_TCSActive Rte_ESC_Status_ESC_TCSActive;
  Rte_DE_ESC_VehicleSpeed Rte_ESC_Status_ESC_VehicleSpeed;
  Rte_DE_ESC_VehicleSpeedInvalid Rte_ESC_Status_ESC_VehicleSpeedInvalid;
  Rte_DE_AccRequestAfterRateLimit Rte_FallbackDebugInfo_AccRequestAfterRateLimit;
  Rte_DE_AccRequestByOutOfOdd Rte_FallbackDebugInfo_AccRequestByOutOfOdd;
  Rte_DE_AccRequestBySpeed Rte_FallbackDebugInfo_AccRequestBySpeed;
  Rte_DE_AccRequestForSystemError Rte_FallbackDebugInfo_AccRequestForSystemError;
  Rte_DE_AvoidCollisionEnable Rte_FallbackDebugInfo_AvoidCollisionEnable;
  Rte_DE_EgoLaneWidth Rte_FallbackDebugInfo_EgoLaneWidth;
  Rte_DE_EgoStopTime Rte_FallbackDebugInfo_EgoStopTime;
  Rte_DE_EmergencyBrakeAcc Rte_FallbackDebugInfo_EmergencyBrakeAcc;
  Rte_DE_FallbackDebugInfoReserve1 Rte_FallbackDebugInfo_FallbackDebugInfoReserve1;
  Rte_DE_FallbackDebugInfoReserve2 Rte_FallbackDebugInfo_FallbackDebugInfoReserve2;
  Rte_DE_FallbackDebugInfoReserve3 Rte_FallbackDebugInfo_FallbackDebugInfoReserve3;
  Rte_DE_FallbackDebugInfoReserve4 Rte_FallbackDebugInfo_FallbackDebugInfoReserve4;
  Rte_DE_FallbackDebugInfoReserve5 Rte_FallbackDebugInfo_FallbackDebugInfoReserve5;
  Rte_DE_FallbackDebugInfoRollingCounter Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter;
  Rte_DE_FallbackTriggerStatus Rte_FallbackDebugInfo_FallbackTriggerStatus;
  Rte_DE_FeedforwardsSteerAngle Rte_FallbackDebugInfo_FeedforwardsSteerAngle;
  Rte_DE_GradientLimitAccRequest Rte_FallbackDebugInfo_GradientLimitAccRequest;
  Rte_DE_HeadingAngleContribution Rte_FallbackDebugInfo_HeadingAngleContribution;
  Rte_DE_HeadingAngleError Rte_FallbackDebugInfo_HeadingAngleError;
  Rte_DE_HeadingAngleErrorWeight Rte_FallbackDebugInfo_HeadingAngleErrorWeight;
  Rte_DE_LaneValidState Rte_FallbackDebugInfo_LaneValidState;
  Rte_DE_LateralContribution Rte_FallbackDebugInfo_LateralContribution;
  Rte_DE_LateralDistanceError Rte_FallbackDebugInfo_LateralDistanceError;
  Rte_DE_LateralDistanceErrorWeight Rte_FallbackDebugInfo_LateralDistanceErrorWeight;
  Rte_DE_LateralSystemState Rte_FallbackDebugInfo_LateralSystemState;
  Rte_DE_LimitAccRequest Rte_FallbackDebugInfo_LimitAccRequest;
  Rte_DE_LimitSteerAngle Rte_FallbackDebugInfo_LimitSteerAngle;
  Rte_DE_LimitSteerAngleRequest Rte_FallbackDebugInfo_LimitSteerAngleRequest;
  Rte_DE_LongAccRequest Rte_FallbackDebugInfo_LongAccRequest;
  Rte_DE_LongNecAcc Rte_FallbackDebugInfo_LongNecAcc;
  Rte_DE_LqrIterationError Rte_FallbackDebugInfo_LqrIterationError;
  Rte_DE_LqrIterationNums Rte_FallbackDebugInfo_LqrIterationNums;
  Rte_DE_MaxSteerAngleRateThreshold Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold;
  Rte_DE_MaxSteerAngleThreshold Rte_FallbackDebugInfo_MaxSteerAngleThreshold;
  Rte_DE_MinAccRate Rte_FallbackDebugInfo_MinAccRate;
  Rte_DE_ObjectStopTime Rte_FallbackDebugInfo_ObjectStopTime;
  Rte_DE_RawAccRequest Rte_FallbackDebugInfo_RawAccRequest;
  Rte_DE_RawSteerAngle Rte_FallbackDebugInfo_RawSteerAngle;
  Rte_DE_SafeDistance Rte_FallbackDebugInfo_SafeDistance;
  Rte_DE_SteerAngle Rte_FallbackDebugInfo_SteerAngle;
  Rte_DE_SteerAngleByLQR Rte_FallbackDebugInfo_SteerAngleByLQR;
  Rte_DE_SteerAngleForSystemError Rte_FallbackDebugInfo_SteerAngleForSystemError;
  Rte_DE_SystemState Rte_FallbackDebugInfo_SystemState;
  Rte_DE_TimeToCollison Rte_FallbackDebugInfo_TimeToCollison;
  Rte_DE_TrajectoryCurvature Rte_FallbackDebugInfo_TrajectoryCurvature;
  Rte_DE_TrajectoryCurvatureChange Rte_FallbackDebugInfo_TrajectoryCurvatureChange;
  Rte_DE_TrajectoryHeadingAngle Rte_FallbackDebugInfo_TrajectoryHeadingAngle;
  Rte_DE_TrajectoryLength Rte_FallbackDebugInfo_TrajectoryLength;
  Rte_DE_TrajectoryPosY0 Rte_FallbackDebugInfo_TrajectoryPosY0;
  Rte_DE_AcuFbCanMessageID Rte_FallbackSystemStatus_AcuFbCanMessageID;
  Rte_DE_AcuFbCanTimeout Rte_FallbackSystemStatus_AcuFbCanTimeout;
  Rte_DE_AcuFbCanTimer Rte_FallbackSystemStatus_AcuFbCanTimer;
  Rte_DE_AcuMid3SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID;
  Rte_DE_AcuMid3SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout;
  Rte_DE_AcuMid3SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer;
  Rte_DE_AcuMid3SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID;
  Rte_DE_AcuMid3SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout;
  Rte_DE_AcuMid3SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer;
  Rte_DE_AcuMid5SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID;
  Rte_DE_AcuMid5SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout;
  Rte_DE_AcuMid5SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer;
  Rte_DE_AcuMid5SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID;
  Rte_DE_AcuMid5SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout;
  Rte_DE_AcuMid5SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer;
  Rte_DE_AcuMid6SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID;
  Rte_DE_AcuMid6SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout;
  Rte_DE_AcuMid6SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer;
  Rte_DE_AcuMid6SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID;
  Rte_DE_AcuMid6SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout;
  Rte_DE_AcuMid6SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer;
  Rte_DE_AswSoftwarewareVersion Rte_FallbackSystemStatus_AswSoftwarewareVersion;
  Rte_DE_BootLoaderVersion Rte_FallbackSystemStatus_BootLoaderVersion;
  Rte_DE_BswSoftwarewareVersion Rte_FallbackSystemStatus_BswSoftwarewareVersion;
  Rte_DE_FrontCameraCalibrationStatus Rte_FallbackSystemStatus_FrontCameraCalibrationStatus;
  Rte_DE_FrontCameraCanMessageID Rte_FallbackSystemStatus_FrontCameraCanMessageID;
  Rte_DE_FrontCameraCanTimeout Rte_FallbackSystemStatus_FrontCameraCanTimeout;
  Rte_DE_FrontCameraCanTimer Rte_FallbackSystemStatus_FrontCameraCanTimer;
  Rte_DE_FrontCameraFailureStatus Rte_FallbackSystemStatus_FrontCameraFailureStatus;
  Rte_DE_FrontRadarCalibrationStatus Rte_FallbackSystemStatus_FrontRadarCalibrationStatus;
  Rte_DE_FrontRadarCanMessageID Rte_FallbackSystemStatus_FrontRadarCanMessageID;
  Rte_DE_FrontRadarCanTimeout Rte_FallbackSystemStatus_FrontRadarCanTimeout;
  Rte_DE_FrontRadarCanTimer Rte_FallbackSystemStatus_FrontRadarCanTimer;
  Rte_DE_FrontRadarFailureStatus Rte_FallbackSystemStatus_FrontRadarFailureStatus;
  Rte_DE_SystemStatusReserved1 Rte_FallbackSystemStatus_SystemStatusReserved1;
  Rte_DE_SystemStatusReserved2 Rte_FallbackSystemStatus_SystemStatusReserved2;
  Rte_DE_SystemStatusReserved3 Rte_FallbackSystemStatus_SystemStatusReserved3;
  Rte_DE_SystemStatusReserved4 Rte_FallbackSystemStatus_SystemStatusReserved4;
  Rte_DE_SystemStatusRollingCounter Rte_FallbackSystemStatus_SystemStatusRollingCounter;
  Rte_DE_VehMid3SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID;
  Rte_DE_VehMid3SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout;
  Rte_DE_VehMid3SsmCounter0Timer Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer;
  Rte_DE_VehMid3SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID;
  Rte_DE_VehMid3SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout;
  Rte_DE_VehMid3SsmCounter1Timer Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer;
  Rte_DE_VehMid3VcuCounter0MessageID Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID;
  Rte_DE_VehMid3VcuCounter0Timeout Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout;
  Rte_DE_VehMid3VcuCounter0Timer Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer;
  Rte_DE_VehMid3VcuCounter1MessageID Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID;
  Rte_DE_VehMid3VcuCounter1Timeout Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout;
  Rte_DE_VehMid3VcuCounter1Timer Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer;
  Rte_DE_VehMid5SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID;
  Rte_DE_VehMid5SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout;
  Rte_DE_VehMid5SsmCounter0Timer Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer;
  Rte_DE_VehMid5SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID;
  Rte_DE_VehMid5SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout;
  Rte_DE_VehMid5SsmCounter1Timer Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer;
  Rte_DE_VehMid6SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID;
  Rte_DE_VehMid6SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout;
  Rte_DE_VehMid6SsmCounter0Timer Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer;
  Rte_DE_VehMid6SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID;
  Rte_DE_VehMid6SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout;
  Rte_DE_VehMid6SsmCounter1Timer Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer;
  Rte_DE_FallbackSelfCheckStatus Rte_FbAcuAvailable_FallbackSelfCheckStatus;
  Rte_DE_FbAcuReserved Rte_FbAcuAvailable_FbAcuReserved;
  Rte_DE_FbAcuRollingCounter Rte_FbAcuAvailable_FbAcuRollingCounter;
  Rte_DE_McuStatus Rte_FbAcuAvailable_McuStatus;
  Rte_DE_Sensor1v1rStatus Rte_FbAcuAvailable_Sensor1v1rStatus;
  Rte_DE_VehControlStatus Rte_FbAcuAvailable_VehControlStatus;
  Rte_DE_SAS_CalibrationSts Rte_SAS_Status_SAS_CalibrationSts;
  Rte_DE_SAS_FailureSts Rte_SAS_Status_SAS_FailureSts;
  Rte_DE_SAS_Status_AliveCounter Rte_SAS_Status_SAS_Status_AliveCounter;
  Rte_DE_SAS_Status_Checksum Rte_SAS_Status_SAS_Status_Checksum;
  Rte_DE_SAS_SteerWheelAngle Rte_SAS_Status_SAS_SteerWheelAngle;
  Rte_DE_SAS_SteerWheelRotSpd Rte_SAS_Status_SAS_SteerWheelRotSpd;
  Rte_DE_PrimVehSpdGroupSafeNomQf_C Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C;
  Rte_DE_PrimVehSpdGroupSafeNom_C Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C;
  Rte_DE_PrimALgtDataRawSafeNomQf_C Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C;
  Rte_DE_PrimALgtDataRawSafeNom_C Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C;
  Rte_DE_PrimALatDataRawSafeNom_C Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C;
  Rte_DE_PrpsnTqDirAct_C Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C;
  Rte_DE_CarTiGlb_A Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A;
  Rte_DE_YawRate1Qf1_C Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C;
  Rte_DE_YawRate1_C Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C;
  Rte_DE_WhlLockStsLockSts_C Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C;
  Rte_DE_SG_AdSecWhlAgReqGroupSafe Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe;
  Rte_DE_SG_AdSecALgtLimReqGroupSafe Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe;
  Rte_DE_SG_SecAdNomALgtReqGroupSafe Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe;
  Rte_DE_SG_SecAdWhlLockReq Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq;
  Rte_DE_IDcDcAvlLoSideExt Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt;
  Rte_DE_IDcDcAvlMaxLoSideExt Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt;
  Rte_DE_SG_HmiAutnmsSts Rte_VIMMid3CanFr04_SG_HmiAutnmsSts;
  Rte_DE_UDcDcAvlLoSideExt Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt;
  Rte_DE_SG_AutnmsDrvStReq Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq;
  Rte_DE_SG_VehOperStReq Rte_VIMMid3CanFr08_SG_VehOperStReq;
  Rte_DE_SG_AdDirReq Rte_VIMMid3CanFr09_SG_AdDirReq;
  Rte_DE_SG_AdStandStillReq Rte_VIMMid3CanFr09_SG_AdStandStillReq;
  Rte_DE_VehUsgStReq Rte_VIMMid3CanFr09_VehUsgStReq;
  Rte_DE_SG_AdFusedFricEstimn Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn;
  Rte_DE_SG_AdpLiReqFromAPI Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI;
  Rte_DE_SG_SwtExtrLiFromAPI Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI;
  Rte_DE_AdSetSpd Rte_VIMMid3CanFr13_AdSetSpd;
  Rte_DE_SG_AdFreeDst Rte_VIMMid3CanFr13_SG_AdFreeDst;
  Rte_DE_SG_AdWhlLockReq Rte_VIMMid3CanFr13_SG_AdWhlLockReq;
  Rte_DE_SG_AdNomALgtReqGroupSafe Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe;
  Rte_DE_SG_AdPrimALgtLimReqGroupSafe Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe;
  Rte_DE_SG_AdPrimWhlAgReqGroupSafe Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe;
  Rte_DE_YRS1_AliveCounter Rte_YRS1_YRS1_AliveCounter;
  Rte_DE_YRS1_Checksum Rte_YRS1_YRS1_Checksum;
  Rte_DE_YRS_LateralAcce Rte_YRS1_YRS_LateralAcce;
  Rte_DE_YRS_LateralSensorState Rte_YRS1_YRS_LateralSensorState;
  Rte_DE_YRS_YawRate Rte_YRS1_YRS_YawRate;
  Rte_DE_YRS_YawRateSensorState Rte_YRS1_YRS_YawRateSensorState;
  Rte_DE_YRS2_Checksum Rte_YRS2_YRS2_Checksum;
  Rte_DE_YRS_AliveCounter Rte_YRS2_YRS_AliveCounter;
  Rte_DE_YRS_LongitAcce Rte_YRS2_YRS_LongitAcce;
  Rte_DE_YRS_LongitSensorState Rte_YRS2_YRS_LongitSensorState;
} Rte_tsRB_FallbackSigOutput_FallbackSigOutput_10ms_Runnable; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

/* PRQA S 0750 L1 */ /* MD_MSR_Union */
typedef union
{
  Rte_tsRB_VehSigInput_VehSigInput_10ms_Runnable Rte_VehSigInput_VehSigInput_10ms_Runnable;
  Rte_tsRB_CameraSigInput_CameraSigInput_10ms_Runnable Rte_CameraSigInput_CameraSigInput_10ms_Runnable;
  Rte_tsRB_AcuSigInput_PoseInput_10ms Rte_AcuSigInput_PoseInput_10ms;
  Rte_tsRB_Control_Control_10ms Rte_Control_Control_10ms;
  Rte_tsRB_FallbackSigOutput_FallbackSigOutput_10ms_Runnable Rte_FallbackSigOutput_FallbackSigOutput_10ms_Runnable;
} Rte_tuRB_MainTask_Core0_10ms;
/* PRQA L:L1 */

typedef struct
{
  Rte_DE_VSI_McuCanTimeout_Struct Rte_I_VehSigInput_VSI_McuCanTimeout_VSI_McuCanTimeout;
  Rte_DE_VSI_VehicleInfo_Struct Rte_I_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo;
  Rte_DE_VSI_VehInfoFor1V1R_Struct Rte_I_VehSigInput_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R;
  Rte_DE_CSI_LaneInfo_Struct Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo;
  Rte_DE_CSI_ObjectInfo_Struct Rte_I_CameraSigInput_CSI_ObjectInfo_CSI_ObjectInfo;
  Rte_DE_GnssPoseInput Rte_I_AcuSigInput_GnssPose_GnssPose;
  Rte_DE_LocalizationPoseInput Rte_I_AcuSigInput_LocalizationPose_LocalizationPose;
  Rte_DE_uint32 Rte_I_AcuSigInput_Timestamp_Timestamp;
  Rte_DE_LGT_CtrlCmd_Struct Rte_I_LgtCtrlFct_LGT_CtrlCmd_LGT_CtrlCmd;
  Rte_DE_LAT_CtrlCmd_Struct Rte_I_LatCtrlFct_LAT_CtrlCmd_LAT_CtrlCmd;
  Rte_DE_ControlOutput Rte_I_Control_ControlCommand_ControlCommand;
} Rte_tsTB_MainTask_Core0_10ms;

typedef struct
{
  Rte_tsTB_MainTask_Core0_10ms Rte_TB;
  Rte_tuRB_MainTask_Core0_10ms Rte_RB;
} Rte_tsMainTask_Core0_10ms;

typedef struct
{
  Rte_DE_AcuControlTimestamp Rte_AcuTrajectoryInfo_ControlTimestamp;
  Rte_DE_ControlWheelAngleOffset Rte_AcuTrajectoryInfo_ControlWheelAngleOffset;
  Rte_DE_EgoLaneLightColor Rte_AcuTrajectoryInfo_EgoLaneLightColor;
  Rte_DE_FrontObjectTTC Rte_AcuTrajectoryInfo_FrontObjectTTC;
  Rte_DE_FrontObjectValid Rte_AcuTrajectoryInfo_FrontObjectValid;
  Rte_DE_IsInTheHighway Rte_AcuTrajectoryInfo_IsInTheHighway;
  Rte_DE_LeftRoadEdgeLatDist Rte_AcuTrajectoryInfo_LeftRoadEdgeLatDist;
  Rte_DE_LeftRoadEdgeLength Rte_AcuTrajectoryInfo_LeftRoadEdgeLength;
  Rte_DE_LeftRoadEdgeValid Rte_AcuTrajectoryInfo_LeftRoadEdgeValid;
  Rte_DE_RearObjectTTC Rte_AcuTrajectoryInfo_RearObjectTTC;
  Rte_DE_RearObjectValid Rte_AcuTrajectoryInfo_RearObjectValid;
  Rte_DE_RightRoadEdgeLatDist Rte_AcuTrajectoryInfo_RightRoadEdgeLatDist;
  Rte_DE_RightRoadEdgeLength Rte_AcuTrajectoryInfo_RightRoadEdgeLength;
  Rte_DE_RightRoadEdgeValid Rte_AcuTrajectoryInfo_RightRoadEdgeValid;
  Rte_DE_StopLineLeftEdgeX Rte_AcuTrajectoryInfo_StopLineLeftEdgeX;
  Rte_DE_StopLineLeftEdgeY Rte_AcuTrajectoryInfo_StopLineLeftEdgeY;
  Rte_DE_StopLineRightEdgeX Rte_AcuTrajectoryInfo_StopLineRightEdgeX;
  Rte_DE_StopLineRightEdgeY Rte_AcuTrajectoryInfo_StopLineRightEdgeY;
  Rte_DE_StopLineValid Rte_AcuTrajectoryInfo_StopLineValid;
  Rte_DE_TrajectoryInfoHarzardLight Rte_AcuTrajectoryInfo_TrajectoryInfoHazardLight;
  Rte_DE_TrajectoryInfoHorn Rte_AcuTrajectoryInfo_TrajectoryInfoHorn;
  Rte_DE_TrajectoryInfoInitTimeStamp Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp;
  Rte_DE_TrajectoryInfoLaneChange Rte_AcuTrajectoryInfo_TrajectoryInfoLaneChange;
  Rte_DE_TrajectoryInfoMotion Rte_AcuTrajectoryInfo_TrajectoryInfoMotion;
  Rte_DE_TrajectoryInfoPlanningStatus Rte_AcuTrajectoryInfo_TrajectoryInfoPlanningStatus;
  Rte_DE_TrajectoryInfoReserve0 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve0;
  Rte_DE_TrajectoryInfoReserve1 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve1;
  Rte_DE_TrajectoryInfoReserve2 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve2;
  Rte_DE_TrajectoryInfoReserve3 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve3;
  Rte_DE_TrajectoryInfoReserve4 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve4;
  Rte_DE_TrajectoryInfoReserve5 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve5;
  Rte_DE_TrajectoryInfoReserve6 Rte_AcuTrajectoryInfo_TrajectoryInfoReserve6;
  Rte_DE_TrajectoryInfoRollingCounter Rte_AcuTrajectoryInfo_TrajectoryInfoRollingCounter;
  Rte_DE_TrajectoryPose00Acceleration Rte_AcuTrajectoryPose00To03_TrajectoryPose00Acceleration;
  Rte_DE_TrajectoryPose00Curvature Rte_AcuTrajectoryPose00To03_TrajectoryPose00Curvature;
  Rte_DE_TrajectoryPose00Heading Rte_AcuTrajectoryPose00To03_TrajectoryPose00Heading;
  Rte_DE_TrajectoryPose00PositionX Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionX;
  Rte_DE_TrajectoryPose00PositionY Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionY;
  Rte_DE_TrajectoryPose00Speed Rte_AcuTrajectoryPose00To03_TrajectoryPose00Speed;
  Rte_DE_TrajectoryPose00Steering Rte_AcuTrajectoryPose00To03_TrajectoryPose00Steering;
  Rte_DE_TrajectoryPose01Acceleration Rte_AcuTrajectoryPose00To03_TrajectoryPose01Acceleration;
  Rte_DE_TrajectoryPose01Curvature Rte_AcuTrajectoryPose00To03_TrajectoryPose01Curvature;
  Rte_DE_TrajectoryPose01Heading Rte_AcuTrajectoryPose00To03_TrajectoryPose01Heading;
  Rte_DE_TrajectoryPose01PositionX Rte_AcuTrajectoryPose00To03_TrajectoryPose01PositionX;
  Rte_DE_TrajectoryPose01PositionY Rte_AcuTrajectoryPose00To03_TrajectoryPose01PositionY;
  Rte_DE_TrajectoryPose01Speed Rte_AcuTrajectoryPose00To03_TrajectoryPose01Speed;
  Rte_DE_TrajectoryPose01Steering Rte_AcuTrajectoryPose00To03_TrajectoryPose01Steering;
  Rte_DE_TrajectoryPose02Acceleration Rte_AcuTrajectoryPose00To03_TrajectoryPose02Acceleration;
  Rte_DE_TrajectoryPose02Curvature Rte_AcuTrajectoryPose00To03_TrajectoryPose02Curvature;
  Rte_DE_TrajectoryPose02Heading Rte_AcuTrajectoryPose00To03_TrajectoryPose02Heading;
  Rte_DE_TrajectoryPose02PositionX Rte_AcuTrajectoryPose00To03_TrajectoryPose02PositionX;
  Rte_DE_TrajectoryPose02PositionY Rte_AcuTrajectoryPose00To03_TrajectoryPose02PositionY;
  Rte_DE_TrajectoryPose02Speed Rte_AcuTrajectoryPose00To03_TrajectoryPose02Speed;
  Rte_DE_TrajectoryPose02Steering Rte_AcuTrajectoryPose00To03_TrajectoryPose02Steering;
  Rte_DE_TrajectoryPose03Acceleration Rte_AcuTrajectoryPose00To03_TrajectoryPose03Acceleration;
  Rte_DE_TrajectoryPose03Curvature Rte_AcuTrajectoryPose00To03_TrajectoryPose03Curvature;
  Rte_DE_TrajectoryPose03Heading Rte_AcuTrajectoryPose00To03_TrajectoryPose03Heading;
  Rte_DE_TrajectoryPose03PositionX Rte_AcuTrajectoryPose00To03_TrajectoryPose03PositionX;
  Rte_DE_TrajectoryPose03PositionY Rte_AcuTrajectoryPose00To03_TrajectoryPose03PositionY;
  Rte_DE_TrajectoryPose03Speed Rte_AcuTrajectoryPose00To03_TrajectoryPose03Speed;
  Rte_DE_TrajectoryPose03Steering Rte_AcuTrajectoryPose00To03_TrajectoryPose03Steering;
  Rte_DE_TrajectoryPose04Acceleration Rte_AcuTrajectoryPose04To07_TrajectoryPose04Acceleration;
  Rte_DE_TrajectoryPose04Curvature Rte_AcuTrajectoryPose04To07_TrajectoryPose04Curvature;
  Rte_DE_TrajectoryPose04Heading Rte_AcuTrajectoryPose04To07_TrajectoryPose04Heading;
  Rte_DE_TrajectoryPose04PositionX Rte_AcuTrajectoryPose04To07_TrajectoryPose04PositionX;
  Rte_DE_TrajectoryPose04PositionY Rte_AcuTrajectoryPose04To07_TrajectoryPose04PositionY;
  Rte_DE_TrajectoryPose04Speed Rte_AcuTrajectoryPose04To07_TrajectoryPose04Speed;
  Rte_DE_TrajectoryPose04Steering Rte_AcuTrajectoryPose04To07_TrajectoryPose04Steering;
  Rte_DE_TrajectoryPose05Acceleration Rte_AcuTrajectoryPose04To07_TrajectoryPose05Acceleration;
  Rte_DE_TrajectoryPose05Curvature Rte_AcuTrajectoryPose04To07_TrajectoryPose05Curvature;
  Rte_DE_TrajectoryPose05Heading Rte_AcuTrajectoryPose04To07_TrajectoryPose05Heading;
  Rte_DE_TrajectoryPose05PositionX Rte_AcuTrajectoryPose04To07_TrajectoryPose05PositionX;
  Rte_DE_TrajectoryPose05PositionY Rte_AcuTrajectoryPose04To07_TrajectoryPose05PositionY;
  Rte_DE_TrajectoryPose05Speed Rte_AcuTrajectoryPose04To07_TrajectoryPose05Speed;
  Rte_DE_TrajectoryPose05Steering Rte_AcuTrajectoryPose04To07_TrajectoryPose05Steering;
  Rte_DE_TrajectoryPose06Acceleration Rte_AcuTrajectoryPose04To07_TrajectoryPose06Acceleration;
  Rte_DE_TrajectoryPose06Curvature Rte_AcuTrajectoryPose04To07_TrajectoryPose06Curvature;
  Rte_DE_TrajectoryPose06Heading Rte_AcuTrajectoryPose04To07_TrajectoryPose06Heading;
  Rte_DE_TrajectoryPose06PositionX Rte_AcuTrajectoryPose04To07_TrajectoryPose06PositionX;
  Rte_DE_TrajectoryPose06PositionY Rte_AcuTrajectoryPose04To07_TrajectoryPose06PositionY;
  Rte_DE_TrajectoryPose06Speed Rte_AcuTrajectoryPose04To07_TrajectoryPose06Speed;
  Rte_DE_TrajectoryPose06Steering Rte_AcuTrajectoryPose04To07_TrajectoryPose06Steering;
  Rte_DE_TrajectoryPose07Acceleration Rte_AcuTrajectoryPose04To07_TrajectoryPose07Acceleration;
  Rte_DE_TrajectoryPose07Curvature Rte_AcuTrajectoryPose04To07_TrajectoryPose07Curvature;
  Rte_DE_TrajectoryPose07Heading Rte_AcuTrajectoryPose04To07_TrajectoryPose07Heading;
  Rte_DE_TrajectoryPose07PositionX Rte_AcuTrajectoryPose04To07_TrajectoryPose07PositionX;
  Rte_DE_TrajectoryPose07PositionY Rte_AcuTrajectoryPose04To07_TrajectoryPose07PositionY;
  Rte_DE_TrajectoryPose07Speed Rte_AcuTrajectoryPose04To07_TrajectoryPose07Speed;
  Rte_DE_TrajectoryPose07Steering Rte_AcuTrajectoryPose04To07_TrajectoryPose07Steering;
  Rte_DE_TrajectoryPose08Acceleration Rte_AcuTrajectoryPose08To11_TrajectoryPose08Acceleration;
  Rte_DE_TrajectoryPose08Curvature Rte_AcuTrajectoryPose08To11_TrajectoryPose08Curvature;
  Rte_DE_TrajectoryPose08Heading Rte_AcuTrajectoryPose08To11_TrajectoryPose08Heading;
  Rte_DE_TrajectoryPose08PositionX Rte_AcuTrajectoryPose08To11_TrajectoryPose08PositionX;
  Rte_DE_TrajectoryPose08PositionY Rte_AcuTrajectoryPose08To11_TrajectoryPose08PositionY;
  Rte_DE_TrajectoryPose08Speed Rte_AcuTrajectoryPose08To11_TrajectoryPose08Speed;
  Rte_DE_TrajectoryPose08Steering Rte_AcuTrajectoryPose08To11_TrajectoryPose08Steering;
  Rte_DE_TrajectoryPose09Acceleration Rte_AcuTrajectoryPose08To11_TrajectoryPose09Acceleration;
  Rte_DE_TrajectoryPose09Curvature Rte_AcuTrajectoryPose08To11_TrajectoryPose09Curvature;
  Rte_DE_TrajectoryPose09Heading Rte_AcuTrajectoryPose08To11_TrajectoryPose09Heading;
  Rte_DE_TrajectoryPose09PositionX Rte_AcuTrajectoryPose08To11_TrajectoryPose09PositionX;
  Rte_DE_TrajectoryPose09PositionY Rte_AcuTrajectoryPose08To11_TrajectoryPose09PositionY;
  Rte_DE_TrajectoryPose09Speed Rte_AcuTrajectoryPose08To11_TrajectoryPose09Speed;
  Rte_DE_TrajectoryPose09Steering Rte_AcuTrajectoryPose08To11_TrajectoryPose09Steering;
  Rte_DE_TrajectoryPose10Acceleration Rte_AcuTrajectoryPose08To11_TrajectoryPose10Acceleration;
  Rte_DE_TrajectoryPose10Curvature Rte_AcuTrajectoryPose08To11_TrajectoryPose10Curvature;
  Rte_DE_TrajectoryPose10Heading Rte_AcuTrajectoryPose08To11_TrajectoryPose10Heading;
  Rte_DE_TrajectoryPose10PositionX Rte_AcuTrajectoryPose08To11_TrajectoryPose10PositionX;
  Rte_DE_TrajectoryPose10PositionY Rte_AcuTrajectoryPose08To11_TrajectoryPose10PositionY;
  Rte_DE_TrajectoryPose10Speed Rte_AcuTrajectoryPose08To11_TrajectoryPose10Speed;
  Rte_DE_TrajectoryPose10Steering Rte_AcuTrajectoryPose08To11_TrajectoryPose10Steering;
  Rte_DE_TrajectoryPose11Acceleration Rte_AcuTrajectoryPose08To11_TrajectoryPose11Acceleration;
  Rte_DE_TrajectoryPose11Curvature Rte_AcuTrajectoryPose08To11_TrajectoryPose11Curvature;
  Rte_DE_TrajectoryPose11Heading Rte_AcuTrajectoryPose08To11_TrajectoryPose11Heading;
  Rte_DE_TrajectoryPose11PositionX Rte_AcuTrajectoryPose08To11_TrajectoryPose11PositionX;
  Rte_DE_TrajectoryPose11PositionY Rte_AcuTrajectoryPose08To11_TrajectoryPose11PositionY;
  Rte_DE_TrajectoryPose11Speed Rte_AcuTrajectoryPose08To11_TrajectoryPose11Speed;
  Rte_DE_TrajectoryPose11Steering Rte_AcuTrajectoryPose08To11_TrajectoryPose11Steering;
  Rte_DE_TrajectoryPose12Acceleration Rte_AcuTrajectoryPose12To15_TrajectoryPose12Acceleration;
  Rte_DE_TrajectoryPose12Curvature Rte_AcuTrajectoryPose12To15_TrajectoryPose12Curvature;
  Rte_DE_TrajectoryPose12Heading Rte_AcuTrajectoryPose12To15_TrajectoryPose12Heading;
  Rte_DE_TrajectoryPose12PositionX Rte_AcuTrajectoryPose12To15_TrajectoryPose12PositionX;
  Rte_DE_TrajectoryPose12PositionY Rte_AcuTrajectoryPose12To15_TrajectoryPose12PositionY;
  Rte_DE_TrajectoryPose12Speed Rte_AcuTrajectoryPose12To15_TrajectoryPose12Speed;
  Rte_DE_TrajectoryPose12Steering Rte_AcuTrajectoryPose12To15_TrajectoryPose12Steering;
  Rte_DE_TrajectoryPose13Acceleration Rte_AcuTrajectoryPose12To15_TrajectoryPose13Acceleration;
  Rte_DE_TrajectoryPose13Curvature Rte_AcuTrajectoryPose12To15_TrajectoryPose13Curvature;
  Rte_DE_TrajectoryPose13Heading Rte_AcuTrajectoryPose12To15_TrajectoryPose13Heading;
  Rte_DE_TrajectoryPose13PositionX Rte_AcuTrajectoryPose12To15_TrajectoryPose13PositionX;
  Rte_DE_TrajectoryPose13PositionY Rte_AcuTrajectoryPose12To15_TrajectoryPose13PositionY;
  Rte_DE_TrajectoryPose13Speed Rte_AcuTrajectoryPose12To15_TrajectoryPose13Speed;
  Rte_DE_TrajectoryPose13Steering Rte_AcuTrajectoryPose12To15_TrajectoryPose13Steering;
  Rte_DE_TrajectoryPose14Acceleration Rte_AcuTrajectoryPose12To15_TrajectoryPose14Acceleration;
  Rte_DE_TrajectoryPose14Curvature Rte_AcuTrajectoryPose12To15_TrajectoryPose14Curvature;
  Rte_DE_TrajectoryPose14Heading Rte_AcuTrajectoryPose12To15_TrajectoryPose14Heading;
  Rte_DE_TrajectoryPose14PositionX Rte_AcuTrajectoryPose12To15_TrajectoryPose14PositionX;
  Rte_DE_TrajectoryPose14PositionY Rte_AcuTrajectoryPose12To15_TrajectoryPose14PositionY;
  Rte_DE_TrajectoryPose14Speed Rte_AcuTrajectoryPose12To15_TrajectoryPose14Speed;
  Rte_DE_TrajectoryPose14Steering Rte_AcuTrajectoryPose12To15_TrajectoryPose14Steering;
  Rte_DE_TrajectoryPose15Acceleration Rte_AcuTrajectoryPose12To15_TrajectoryPose15Acceleration;
  Rte_DE_TrajectoryPose15Curvature Rte_AcuTrajectoryPose12To15_TrajectoryPose15Curvature;
  Rte_DE_TrajectoryPose15Heading Rte_AcuTrajectoryPose12To15_TrajectoryPose15Heading;
  Rte_DE_TrajectoryPose15PositionX Rte_AcuTrajectoryPose12To15_TrajectoryPose15PositionX;
  Rte_DE_TrajectoryPose15PositionY Rte_AcuTrajectoryPose12To15_TrajectoryPose15PositionY;
  Rte_DE_TrajectoryPose15Speed Rte_AcuTrajectoryPose12To15_TrajectoryPose15Speed;
  Rte_DE_TrajectoryPose15Steering Rte_AcuTrajectoryPose12To15_TrajectoryPose15Steering;
  Rte_DE_TrajectoryPose16Acceleration Rte_AcuTrajectoryPose16To19_TrajectoryPose16Acceleration;
  Rte_DE_TrajectoryPose16Curvature Rte_AcuTrajectoryPose16To19_TrajectoryPose16Curvature;
  Rte_DE_TrajectoryPose16Heading Rte_AcuTrajectoryPose16To19_TrajectoryPose16Heading;
  Rte_DE_TrajectoryPose16PositionX Rte_AcuTrajectoryPose16To19_TrajectoryPose16PositionX;
  Rte_DE_TrajectoryPose16PositionY Rte_AcuTrajectoryPose16To19_TrajectoryPose16PositionY;
  Rte_DE_TrajectoryPose16Speed Rte_AcuTrajectoryPose16To19_TrajectoryPose16Speed;
  Rte_DE_TrajectoryPose16Steering Rte_AcuTrajectoryPose16To19_TrajectoryPose16Steering;
  Rte_DE_TrajectoryPose17Acceleration Rte_AcuTrajectoryPose16To19_TrajectoryPose17Acceleration;
  Rte_DE_TrajectoryPose17Curvature Rte_AcuTrajectoryPose16To19_TrajectoryPose17Curvature;
  Rte_DE_TrajectoryPose17Heading Rte_AcuTrajectoryPose16To19_TrajectoryPose17Heading;
  Rte_DE_TrajectoryPose17PositionX Rte_AcuTrajectoryPose16To19_TrajectoryPose17PositionX;
  Rte_DE_TrajectoryPose17PositionY Rte_AcuTrajectoryPose16To19_TrajectoryPose17PositionY;
  Rte_DE_TrajectoryPose17Speed Rte_AcuTrajectoryPose16To19_TrajectoryPose17Speed;
  Rte_DE_TrajectoryPose17Steering Rte_AcuTrajectoryPose16To19_TrajectoryPose17Steering;
  Rte_DE_TrajectoryPose18Acceleration Rte_AcuTrajectoryPose16To19_TrajectoryPose18Acceleration;
  Rte_DE_TrajectoryPose18Curvature Rte_AcuTrajectoryPose16To19_TrajectoryPose18Curvature;
  Rte_DE_TrajectoryPose18Heading Rte_AcuTrajectoryPose16To19_TrajectoryPose18Heading;
  Rte_DE_TrajectoryPose18PositionX Rte_AcuTrajectoryPose16To19_TrajectoryPose18PositionX;
  Rte_DE_TrajectoryPose18PositionY Rte_AcuTrajectoryPose16To19_TrajectoryPose18PositionY;
  Rte_DE_TrajectoryPose18Speed Rte_AcuTrajectoryPose16To19_TrajectoryPose18Speed;
  Rte_DE_TrajectoryPose18Steering Rte_AcuTrajectoryPose16To19_TrajectoryPose18Steering;
  Rte_DE_TrajectoryPose19Acceleration Rte_AcuTrajectoryPose16To19_TrajectoryPose19Acceleration;
  Rte_DE_TrajectoryPose19Curvature Rte_AcuTrajectoryPose16To19_TrajectoryPose19Curvature;
  Rte_DE_TrajectoryPose19Heading Rte_AcuTrajectoryPose16To19_TrajectoryPose19Heading;
  Rte_DE_TrajectoryPose19PositionX Rte_AcuTrajectoryPose16To19_TrajectoryPose19PositionX;
  Rte_DE_TrajectoryPose19PositionY Rte_AcuTrajectoryPose16To19_TrajectoryPose19PositionY;
  Rte_DE_TrajectoryPose19Speed Rte_AcuTrajectoryPose16To19_TrajectoryPose19Speed;
  Rte_DE_TrajectoryPose19Steering Rte_AcuTrajectoryPose16To19_TrajectoryPose19Steering;
  Rte_DE_TrajectoryPose20Acceleration Rte_AcuTrajectoryPose20To23_TrajectoryPose20Acceleration;
  Rte_DE_TrajectoryPose20Curvature Rte_AcuTrajectoryPose20To23_TrajectoryPose20Curvature;
  Rte_DE_TrajectoryPose20Heading Rte_AcuTrajectoryPose20To23_TrajectoryPose20Heading;
  Rte_DE_TrajectoryPose20PositionX Rte_AcuTrajectoryPose20To23_TrajectoryPose20PositionX;
  Rte_DE_TrajectoryPose20PositionY Rte_AcuTrajectoryPose20To23_TrajectoryPose20PositionY;
  Rte_DE_TrajectoryPose20Speed Rte_AcuTrajectoryPose20To23_TrajectoryPose20Speed;
  Rte_DE_TrajectoryPose20Steering Rte_AcuTrajectoryPose20To23_TrajectoryPose20Steering;
  Rte_DE_TrajectoryPose21Acceleration Rte_AcuTrajectoryPose20To23_TrajectoryPose21Acceleration;
  Rte_DE_TrajectoryPose21Curvature Rte_AcuTrajectoryPose20To23_TrajectoryPose21Curvature;
  Rte_DE_TrajectoryPose21Heading Rte_AcuTrajectoryPose20To23_TrajectoryPose21Heading;
  Rte_DE_TrajectoryPose21PositionX Rte_AcuTrajectoryPose20To23_TrajectoryPose21PositionX;
  Rte_DE_TrajectoryPose21PositionY Rte_AcuTrajectoryPose20To23_TrajectoryPose21PositionY;
  Rte_DE_TrajectoryPose21Speed Rte_AcuTrajectoryPose20To23_TrajectoryPose21Speed;
  Rte_DE_TrajectoryPose21Steering Rte_AcuTrajectoryPose20To23_TrajectoryPose21Steering;
  Rte_DE_TrajectoryPose22Acceleration Rte_AcuTrajectoryPose20To23_TrajectoryPose22Acceleration;
  Rte_DE_TrajectoryPose22Curvature Rte_AcuTrajectoryPose20To23_TrajectoryPose22Curvature;
  Rte_DE_TrajectoryPose22Heading Rte_AcuTrajectoryPose20To23_TrajectoryPose22Heading;
  Rte_DE_TrajectoryPose22PositionX Rte_AcuTrajectoryPose20To23_TrajectoryPose22PositionX;
  Rte_DE_TrajectoryPose22PositionY Rte_AcuTrajectoryPose20To23_TrajectoryPose22PositionY;
  Rte_DE_TrajectoryPose22Speed Rte_AcuTrajectoryPose20To23_TrajectoryPose22Speed;
  Rte_DE_TrajectoryPose22Steering Rte_AcuTrajectoryPose20To23_TrajectoryPose22Steering;
  Rte_DE_TrajectoryPose23Acceleration Rte_AcuTrajectoryPose20To23_TrajectoryPose23Acceleration;
  Rte_DE_TrajectoryPose23Curvature Rte_AcuTrajectoryPose20To23_TrajectoryPose23Curvature;
  Rte_DE_TrajectoryPose23Heading Rte_AcuTrajectoryPose20To23_TrajectoryPose23Heading;
  Rte_DE_TrajectoryPose23PositionX Rte_AcuTrajectoryPose20To23_TrajectoryPose23PositionX;
  Rte_DE_TrajectoryPose23PositionY Rte_AcuTrajectoryPose20To23_TrajectoryPose23PositionY;
  Rte_DE_TrajectoryPose23Speed Rte_AcuTrajectoryPose20To23_TrajectoryPose23Speed;
  Rte_DE_TrajectoryPose23Steering Rte_AcuTrajectoryPose20To23_TrajectoryPose23Steering;
  Rte_DE_TrajectoryPose24Acceleration Rte_AcuTrajectoryPose24To27_TrajectoryPose24Acceleration;
  Rte_DE_TrajectoryPose24Curvature Rte_AcuTrajectoryPose24To27_TrajectoryPose24Curvature;
  Rte_DE_TrajectoryPose24Heading Rte_AcuTrajectoryPose24To27_TrajectoryPose24Heading;
  Rte_DE_TrajectoryPose24PositionX Rte_AcuTrajectoryPose24To27_TrajectoryPose24PositionX;
  Rte_DE_TrajectoryPose24PositionY Rte_AcuTrajectoryPose24To27_TrajectoryPose24PositionY;
  Rte_DE_TrajectoryPose24Speed Rte_AcuTrajectoryPose24To27_TrajectoryPose24Speed;
  Rte_DE_TrajectoryPose24Steering Rte_AcuTrajectoryPose24To27_TrajectoryPose24Steering;
  Rte_DE_TrajectoryPose25Acceleration Rte_AcuTrajectoryPose24To27_TrajectoryPose25Acceleration;
  Rte_DE_TrajectoryPose25Curvature Rte_AcuTrajectoryPose24To27_TrajectoryPose25Curvature;
  Rte_DE_TrajectoryPose25Heading Rte_AcuTrajectoryPose24To27_TrajectoryPose25Heading;
  Rte_DE_TrajectoryPose25PositionX Rte_AcuTrajectoryPose24To27_TrajectoryPose25PositionX;
  Rte_DE_TrajectoryPose25PositionY Rte_AcuTrajectoryPose24To27_TrajectoryPose25PositionY;
  Rte_DE_TrajectoryPose25Speed Rte_AcuTrajectoryPose24To27_TrajectoryPose25Speed;
  Rte_DE_TrajectoryPose25Steering Rte_AcuTrajectoryPose24To27_TrajectoryPose25Steering;
  Rte_DE_TrajectoryPose26Acceleration Rte_AcuTrajectoryPose24To27_TrajectoryPose26Acceleration;
  Rte_DE_TrajectoryPose26Curvature Rte_AcuTrajectoryPose24To27_TrajectoryPose26Curvature;
  Rte_DE_TrajectoryPose26Heading Rte_AcuTrajectoryPose24To27_TrajectoryPose26Heading;
  Rte_DE_TrajectoryPose26PositionX Rte_AcuTrajectoryPose24To27_TrajectoryPose26PositionX;
  Rte_DE_TrajectoryPose26PositionY Rte_AcuTrajectoryPose24To27_TrajectoryPose26PositionY;
  Rte_DE_TrajectoryPose26Speed Rte_AcuTrajectoryPose24To27_TrajectoryPose26Speed;
  Rte_DE_TrajectoryPose26Steering Rte_AcuTrajectoryPose24To27_TrajectoryPose26Steering;
  Rte_DE_TrajectoryPose27Acceleration Rte_AcuTrajectoryPose24To27_TrajectoryPose27Acceleration;
  Rte_DE_TrajectoryPose27Curvature Rte_AcuTrajectoryPose24To27_TrajectoryPose27Curvature;
  Rte_DE_TrajectoryPose27Heading Rte_AcuTrajectoryPose24To27_TrajectoryPose27Heading;
  Rte_DE_TrajectoryPose27PositionX Rte_AcuTrajectoryPose24To27_TrajectoryPose27PositionX;
  Rte_DE_TrajectoryPose27PositionY Rte_AcuTrajectoryPose24To27_TrajectoryPose27PositionY;
  Rte_DE_TrajectoryPose27Speed Rte_AcuTrajectoryPose24To27_TrajectoryPose27Speed;
  Rte_DE_TrajectoryPose27Steering Rte_AcuTrajectoryPose24To27_TrajectoryPose27Steering;
  Rte_DE_TrajectoryPose28Acceleration Rte_AcuTrajectoryPose28To31_TrajectoryPose28Acceleration;
  Rte_DE_TrajectoryPose28Curvature Rte_AcuTrajectoryPose28To31_TrajectoryPose28Curvature;
  Rte_DE_TrajectoryPose28Heading Rte_AcuTrajectoryPose28To31_TrajectoryPose28Heading;
  Rte_DE_TrajectoryPose28PositionX Rte_AcuTrajectoryPose28To31_TrajectoryPose28PositionX;
  Rte_DE_TrajectoryPose28PositionY Rte_AcuTrajectoryPose28To31_TrajectoryPose28PositionY;
  Rte_DE_TrajectoryPose28Speed Rte_AcuTrajectoryPose28To31_TrajectoryPose28Speed;
  Rte_DE_TrajectoryPose28Steering Rte_AcuTrajectoryPose28To31_TrajectoryPose28Steering;
  Rte_DE_TrajectoryPose29Acceleration Rte_AcuTrajectoryPose28To31_TrajectoryPose29Acceleration;
  Rte_DE_TrajectoryPose29Curvature Rte_AcuTrajectoryPose28To31_TrajectoryPose29Curvature;
  Rte_DE_TrajectoryPose29Heading Rte_AcuTrajectoryPose28To31_TrajectoryPose29Heading;
  Rte_DE_TrajectoryPose29PositionX Rte_AcuTrajectoryPose28To31_TrajectoryPose29PositionX;
  Rte_DE_TrajectoryPose29PositionY Rte_AcuTrajectoryPose28To31_TrajectoryPose29PositionY;
  Rte_DE_TrajectoryPose29Speed Rte_AcuTrajectoryPose28To31_TrajectoryPose29Speed;
  Rte_DE_TrajectoryPose29Steering Rte_AcuTrajectoryPose28To31_TrajectoryPose29Steering;
  Rte_DE_TrajectoryPose30Acceleration Rte_AcuTrajectoryPose28To31_TrajectoryPose30Acceleration;
  Rte_DE_TrajectoryPose30Curvature Rte_AcuTrajectoryPose28To31_TrajectoryPose30Curvature;
  Rte_DE_TrajectoryPose30Heading Rte_AcuTrajectoryPose28To31_TrajectoryPose30Heading;
  Rte_DE_TrajectoryPose30PositionX Rte_AcuTrajectoryPose28To31_TrajectoryPose30PositionX;
  Rte_DE_TrajectoryPose30PositionY Rte_AcuTrajectoryPose28To31_TrajectoryPose30PositionY;
  Rte_DE_TrajectoryPose30Speed Rte_AcuTrajectoryPose28To31_TrajectoryPose30Speed;
  Rte_DE_TrajectoryPose30Steering Rte_AcuTrajectoryPose28To31_TrajectoryPose30Steering;
  Rte_DE_TrajectoryPose31Acceleration Rte_AcuTrajectoryPose28To31_TrajectoryPose31Acceleration;
  Rte_DE_TrajectoryPose31Curvature Rte_AcuTrajectoryPose28To31_TrajectoryPose31Curvature;
  Rte_DE_TrajectoryPose31Heading Rte_AcuTrajectoryPose28To31_TrajectoryPose31Heading;
  Rte_DE_TrajectoryPose31PositionX Rte_AcuTrajectoryPose28To31_TrajectoryPose31PositionX;
  Rte_DE_TrajectoryPose31PositionY Rte_AcuTrajectoryPose28To31_TrajectoryPose31PositionY;
  Rte_DE_TrajectoryPose31Speed Rte_AcuTrajectoryPose28To31_TrajectoryPose31Speed;
  Rte_DE_TrajectoryPose31Steering Rte_AcuTrajectoryPose28To31_TrajectoryPose31Steering;
  Rte_DE_TrajectoryPose32Acceleration Rte_AcuTrajectoryPose32To35_TrajectoryPose32Acceleration;
  Rte_DE_TrajectoryPose32Curvature Rte_AcuTrajectoryPose32To35_TrajectoryPose32Curvature;
  Rte_DE_TrajectoryPose32Heading Rte_AcuTrajectoryPose32To35_TrajectoryPose32Heading;
  Rte_DE_TrajectoryPose32PositionX Rte_AcuTrajectoryPose32To35_TrajectoryPose32PositionX;
  Rte_DE_TrajectoryPose32PositionY Rte_AcuTrajectoryPose32To35_TrajectoryPose32PositionY;
  Rte_DE_TrajectoryPose32Speed Rte_AcuTrajectoryPose32To35_TrajectoryPose32Speed;
  Rte_DE_TrajectoryPose32Steering Rte_AcuTrajectoryPose32To35_TrajectoryPose32Steering;
  Rte_DE_TrajectoryPose33Acceleration Rte_AcuTrajectoryPose32To35_TrajectoryPose33Acceleration;
  Rte_DE_TrajectoryPose33Curvature Rte_AcuTrajectoryPose32To35_TrajectoryPose33Curvature;
  Rte_DE_TrajectoryPose33Heading Rte_AcuTrajectoryPose32To35_TrajectoryPose33Heading;
  Rte_DE_TrajectoryPose33PositionX Rte_AcuTrajectoryPose32To35_TrajectoryPose33PositionX;
  Rte_DE_TrajectoryPose33PositionY Rte_AcuTrajectoryPose32To35_TrajectoryPose33PositionY;
  Rte_DE_TrajectoryPose33Speed Rte_AcuTrajectoryPose32To35_TrajectoryPose33Speed;
  Rte_DE_TrajectoryPose33Steering Rte_AcuTrajectoryPose32To35_TrajectoryPose33Steering;
  Rte_DE_TrajectoryPose34Acceleration Rte_AcuTrajectoryPose32To35_TrajectoryPose34Acceleration;
  Rte_DE_TrajectoryPose34Curvature Rte_AcuTrajectoryPose32To35_TrajectoryPose34Curvature;
  Rte_DE_TrajectoryPose34Heading Rte_AcuTrajectoryPose32To35_TrajectoryPose34Heading;
  Rte_DE_TrajectoryPose34PositionX Rte_AcuTrajectoryPose32To35_TrajectoryPose34PositionX;
  Rte_DE_TrajectoryPose34PositionY Rte_AcuTrajectoryPose32To35_TrajectoryPose34PositionY;
  Rte_DE_TrajectoryPose34Speed Rte_AcuTrajectoryPose32To35_TrajectoryPose34Speed;
  Rte_DE_TrajectoryPose34Steering Rte_AcuTrajectoryPose32To35_TrajectoryPose34Steering;
  Rte_DE_TrajectoryPose35Acceleration Rte_AcuTrajectoryPose32To35_TrajectoryPose35Acceleration;
  Rte_DE_TrajectoryPose35Curvature Rte_AcuTrajectoryPose32To35_TrajectoryPose35Curvature;
  Rte_DE_TrajectoryPose35Heading Rte_AcuTrajectoryPose32To35_TrajectoryPose35Heading;
  Rte_DE_TrajectoryPose35PositionX Rte_AcuTrajectoryPose32To35_TrajectoryPose35PositionX;
  Rte_DE_TrajectoryPose35PositionY Rte_AcuTrajectoryPose32To35_TrajectoryPose35PositionY;
  Rte_DE_TrajectoryPose35Speed Rte_AcuTrajectoryPose32To35_TrajectoryPose35Speed;
  Rte_DE_TrajectoryPose35Steering Rte_AcuTrajectoryPose32To35_TrajectoryPose35Steering;
  Rte_DE_TrajectoryPose36Acceleration Rte_AcuTrajectoryPose36To39_TrajectoryPose36Acceleration;
  Rte_DE_TrajectoryPose36Curvature Rte_AcuTrajectoryPose36To39_TrajectoryPose36Curvature;
  Rte_DE_TrajectoryPose36Heading Rte_AcuTrajectoryPose36To39_TrajectoryPose36Heading;
  Rte_DE_TrajectoryPose36PositionX Rte_AcuTrajectoryPose36To39_TrajectoryPose36PositionX;
  Rte_DE_TrajectoryPose36PositionY Rte_AcuTrajectoryPose36To39_TrajectoryPose36PositionY;
  Rte_DE_TrajectoryPose36Speed Rte_AcuTrajectoryPose36To39_TrajectoryPose36Speed;
  Rte_DE_TrajectoryPose36Steering Rte_AcuTrajectoryPose36To39_TrajectoryPose36Steering;
  Rte_DE_TrajectoryPose37Acceleration Rte_AcuTrajectoryPose36To39_TrajectoryPose37Acceleration;
  Rte_DE_TrajectoryPose37Curvature Rte_AcuTrajectoryPose36To39_TrajectoryPose37Curvature;
  Rte_DE_TrajectoryPose37Heading Rte_AcuTrajectoryPose36To39_TrajectoryPose37Heading;
  Rte_DE_TrajectoryPose37PositionX Rte_AcuTrajectoryPose36To39_TrajectoryPose37PositionX;
  Rte_DE_TrajectoryPose37PositionY Rte_AcuTrajectoryPose36To39_TrajectoryPose37PositionY;
  Rte_DE_TrajectoryPose37Speed Rte_AcuTrajectoryPose36To39_TrajectoryPose37Speed;
  Rte_DE_TrajectoryPose37Steering Rte_AcuTrajectoryPose36To39_TrajectoryPose37Steering;
  Rte_DE_TrajectoryPose38Acceleration Rte_AcuTrajectoryPose36To39_TrajectoryPose38Acceleration;
  Rte_DE_TrajectoryPose38Curvature Rte_AcuTrajectoryPose36To39_TrajectoryPose38Curvature;
  Rte_DE_TrajectoryPose38Heading Rte_AcuTrajectoryPose36To39_TrajectoryPose38Heading;
  Rte_DE_TrajectoryPose38PositionX Rte_AcuTrajectoryPose36To39_TrajectoryPose38PositionX;
  Rte_DE_TrajectoryPose38PositionY Rte_AcuTrajectoryPose36To39_TrajectoryPose38PositionY;
  Rte_DE_TrajectoryPose38Speed Rte_AcuTrajectoryPose36To39_TrajectoryPose38Speed;
  Rte_DE_TrajectoryPose38Steering Rte_AcuTrajectoryPose36To39_TrajectoryPose38Steering;
  Rte_DE_TrajectoryPose39Acceleration Rte_AcuTrajectoryPose36To39_TrajectoryPose39Acceleration;
  Rte_DE_TrajectoryPose39Curvature Rte_AcuTrajectoryPose36To39_TrajectoryPose39Curvature;
  Rte_DE_TrajectoryPose39Heading Rte_AcuTrajectoryPose36To39_TrajectoryPose39Heading;
  Rte_DE_TrajectoryPose39PositionX Rte_AcuTrajectoryPose36To39_TrajectoryPose39PositionX;
  Rte_DE_TrajectoryPose39PositionY Rte_AcuTrajectoryPose36To39_TrajectoryPose39PositionY;
  Rte_DE_TrajectoryPose39Speed Rte_AcuTrajectoryPose36To39_TrajectoryPose39Speed;
  Rte_DE_TrajectoryPose39Steering Rte_AcuTrajectoryPose36To39_TrajectoryPose39Steering;
  Rte_DE_TrajectoryPose40Acceleration Rte_AcuTrajectoryPose40To43_TrajectoryPose40Acceleration;
  Rte_DE_TrajectoryPose40Curvature Rte_AcuTrajectoryPose40To43_TrajectoryPose40Curvature;
  Rte_DE_TrajectoryPose40Heading Rte_AcuTrajectoryPose40To43_TrajectoryPose40Heading;
  Rte_DE_TrajectoryPose40PositionX Rte_AcuTrajectoryPose40To43_TrajectoryPose40PositionX;
  Rte_DE_TrajectoryPose40PositionY Rte_AcuTrajectoryPose40To43_TrajectoryPose40PositionY;
  Rte_DE_TrajectoryPose40Speed Rte_AcuTrajectoryPose40To43_TrajectoryPose40Speed;
  Rte_DE_TrajectoryPose40Steering Rte_AcuTrajectoryPose40To43_TrajectoryPose40Steering;
  Rte_DE_TrajectoryPose41Acceleration Rte_AcuTrajectoryPose40To43_TrajectoryPose41Acceleration;
  Rte_DE_TrajectoryPose41Curvature Rte_AcuTrajectoryPose40To43_TrajectoryPose41Curvature;
  Rte_DE_TrajectoryPose41Heading Rte_AcuTrajectoryPose40To43_TrajectoryPose41Heading;
  Rte_DE_TrajectoryPose41PositionX Rte_AcuTrajectoryPose40To43_TrajectoryPose41PositionX;
  Rte_DE_TrajectoryPose41PositionY Rte_AcuTrajectoryPose40To43_TrajectoryPose41PositionY;
  Rte_DE_TrajectoryPose41Speed Rte_AcuTrajectoryPose40To43_TrajectoryPose41Speed;
  Rte_DE_TrajectoryPose41Steering Rte_AcuTrajectoryPose40To43_TrajectoryPose41Steering;
  Rte_DE_TrajectoryPose42Acceleration Rte_AcuTrajectoryPose40To43_TrajectoryPose42Acceleration;
  Rte_DE_TrajectoryPose42Curvature Rte_AcuTrajectoryPose40To43_TrajectoryPose42Curvature;
  Rte_DE_TrajectoryPose42Heading Rte_AcuTrajectoryPose40To43_TrajectoryPose42Heading;
  Rte_DE_TrajectoryPose42PositionX Rte_AcuTrajectoryPose40To43_TrajectoryPose42PositionX;
  Rte_DE_TrajectoryPose42PositionY Rte_AcuTrajectoryPose40To43_TrajectoryPose42PositionY;
  Rte_DE_TrajectoryPose42Speed Rte_AcuTrajectoryPose40To43_TrajectoryPose42Speed;
  Rte_DE_TrajectoryPose42Steering Rte_AcuTrajectoryPose40To43_TrajectoryPose42Steering;
  Rte_DE_TrajectoryPose43Acceleration Rte_AcuTrajectoryPose40To43_TrajectoryPose43Acceleration;
  Rte_DE_TrajectoryPose43Curvature Rte_AcuTrajectoryPose40To43_TrajectoryPose43Curvature;
  Rte_DE_TrajectoryPose43Heading Rte_AcuTrajectoryPose40To43_TrajectoryPose43Heading;
  Rte_DE_TrajectoryPose43PositionX Rte_AcuTrajectoryPose40To43_TrajectoryPose43PositionX;
  Rte_DE_TrajectoryPose43PositionY Rte_AcuTrajectoryPose40To43_TrajectoryPose43PositionY;
  Rte_DE_TrajectoryPose43Speed Rte_AcuTrajectoryPose40To43_TrajectoryPose43Speed;
  Rte_DE_TrajectoryPose43Steering Rte_AcuTrajectoryPose40To43_TrajectoryPose43Steering;
  Rte_DE_TrajectoryPose44Acceleration Rte_AcuTrajectoryPose44To47_TrajectoryPose44Acceleration;
  Rte_DE_TrajectoryPose44Curvature Rte_AcuTrajectoryPose44To47_TrajectoryPose44Curvature;
  Rte_DE_TrajectoryPose44Heading Rte_AcuTrajectoryPose44To47_TrajectoryPose44Heading;
  Rte_DE_TrajectoryPose44PositionX Rte_AcuTrajectoryPose44To47_TrajectoryPose44PositionX;
  Rte_DE_TrajectoryPose44PositionY Rte_AcuTrajectoryPose44To47_TrajectoryPose44PositionY;
  Rte_DE_TrajectoryPose44Speed Rte_AcuTrajectoryPose44To47_TrajectoryPose44Speed;
  Rte_DE_TrajectoryPose44Steering Rte_AcuTrajectoryPose44To47_TrajectoryPose44Steering;
  Rte_DE_TrajectoryPose45Acceleration Rte_AcuTrajectoryPose44To47_TrajectoryPose45Acceleration;
  Rte_DE_TrajectoryPose45Curvature Rte_AcuTrajectoryPose44To47_TrajectoryPose45Curvature;
  Rte_DE_TrajectoryPose45Heading Rte_AcuTrajectoryPose44To47_TrajectoryPose45Heading;
  Rte_DE_TrajectoryPose45PositionX Rte_AcuTrajectoryPose44To47_TrajectoryPose45PositionX;
  Rte_DE_TrajectoryPose45PositionY Rte_AcuTrajectoryPose44To47_TrajectoryPose45PositionY;
  Rte_DE_TrajectoryPose45Speed Rte_AcuTrajectoryPose44To47_TrajectoryPose45Speed;
  Rte_DE_TrajectoryPose45Steering Rte_AcuTrajectoryPose44To47_TrajectoryPose45Steering;
  Rte_DE_TrajectoryPose46Acceleration Rte_AcuTrajectoryPose44To47_TrajectoryPose46Acceleration;
  Rte_DE_TrajectoryPose46Curvature Rte_AcuTrajectoryPose44To47_TrajectoryPose46Curvature;
  Rte_DE_TrajectoryPose46Heading Rte_AcuTrajectoryPose44To47_TrajectoryPose46Heading;
  Rte_DE_TrajectoryPose46PositionX Rte_AcuTrajectoryPose44To47_TrajectoryPose46PositionX;
  Rte_DE_TrajectoryPose46PositionY Rte_AcuTrajectoryPose44To47_TrajectoryPose46PositionY;
  Rte_DE_TrajectoryPose46Speed Rte_AcuTrajectoryPose44To47_TrajectoryPose46Speed;
  Rte_DE_TrajectoryPose46Steering Rte_AcuTrajectoryPose44To47_TrajectoryPose46Steering;
  Rte_DE_TrajectoryPose47Acceleration Rte_AcuTrajectoryPose44To47_TrajectoryPose47Acceleration;
  Rte_DE_TrajectoryPose47Curvature Rte_AcuTrajectoryPose44To47_TrajectoryPose47Curvature;
  Rte_DE_TrajectoryPose47Heading Rte_AcuTrajectoryPose44To47_TrajectoryPose47Heading;
  Rte_DE_TrajectoryPose47PositionX Rte_AcuTrajectoryPose44To47_TrajectoryPose47PositionX;
  Rte_DE_TrajectoryPose47PositionY Rte_AcuTrajectoryPose44To47_TrajectoryPose47PositionY;
  Rte_DE_TrajectoryPose47Speed Rte_AcuTrajectoryPose44To47_TrajectoryPose47Speed;
  Rte_DE_TrajectoryPose47Steering Rte_AcuTrajectoryPose44To47_TrajectoryPose47Steering;
  Rte_DE_TrajectoryPose48Acceleration Rte_AcuTrajectoryPose48To51_TrajectoryPose48Acceleration;
  Rte_DE_TrajectoryPose48Curvature Rte_AcuTrajectoryPose48To51_TrajectoryPose48Curvature;
  Rte_DE_TrajectoryPose48Heading Rte_AcuTrajectoryPose48To51_TrajectoryPose48Heading;
  Rte_DE_TrajectoryPose48PositionX Rte_AcuTrajectoryPose48To51_TrajectoryPose48PositionX;
  Rte_DE_TrajectoryPose48PositionY Rte_AcuTrajectoryPose48To51_TrajectoryPose48PositionY;
  Rte_DE_TrajectoryPose48Speed Rte_AcuTrajectoryPose48To51_TrajectoryPose48Speed;
  Rte_DE_TrajectoryPose48Steering Rte_AcuTrajectoryPose48To51_TrajectoryPose48Steering;
  Rte_DE_TrajectoryPose49Acceleration Rte_AcuTrajectoryPose48To51_TrajectoryPose49Acceleration;
  Rte_DE_TrajectoryPose49Curvature Rte_AcuTrajectoryPose48To51_TrajectoryPose49Curvature;
  Rte_DE_TrajectoryPose49Heading Rte_AcuTrajectoryPose48To51_TrajectoryPose49Heading;
  Rte_DE_TrajectoryPose49PositionX Rte_AcuTrajectoryPose48To51_TrajectoryPose49PositionX;
  Rte_DE_TrajectoryPose49PositionY Rte_AcuTrajectoryPose48To51_TrajectoryPose49PositionY;
  Rte_DE_TrajectoryPose49Speed Rte_AcuTrajectoryPose48To51_TrajectoryPose49Speed;
  Rte_DE_TrajectoryPose49Steering Rte_AcuTrajectoryPose48To51_TrajectoryPose49Steering;
  Rte_DE_TrajectoryPose50Acceleration Rte_AcuTrajectoryPose48To51_TrajectoryPose50Acceleration;
  Rte_DE_TrajectoryPose50Curvature Rte_AcuTrajectoryPose48To51_TrajectoryPose50Curvature;
  Rte_DE_TrajectoryPose50Heading Rte_AcuTrajectoryPose48To51_TrajectoryPose50Heading;
  Rte_DE_TrajectoryPose50PositionX Rte_AcuTrajectoryPose48To51_TrajectoryPose50PositionX;
  Rte_DE_TrajectoryPose50PositionY Rte_AcuTrajectoryPose48To51_TrajectoryPose50PositionY;
  Rte_DE_TrajectoryPose50Speed Rte_AcuTrajectoryPose48To51_TrajectoryPose50Speed;
  Rte_DE_TrajectoryPose50Steering Rte_AcuTrajectoryPose48To51_TrajectoryPose50Steering;
  Rte_DE_TrajectoryPose51Acceleration Rte_AcuTrajectoryPose48To51_TrajectoryPose51Acceleration;
  Rte_DE_TrajectoryPose51Curvature Rte_AcuTrajectoryPose48To51_TrajectoryPose51Curvature;
  Rte_DE_TrajectoryPose51Heading Rte_AcuTrajectoryPose48To51_TrajectoryPose51Heading;
  Rte_DE_TrajectoryPose51PositionX Rte_AcuTrajectoryPose48To51_TrajectoryPose51PositionX;
  Rte_DE_TrajectoryPose51PositionY Rte_AcuTrajectoryPose48To51_TrajectoryPose51PositionY;
  Rte_DE_TrajectoryPose51Speed Rte_AcuTrajectoryPose48To51_TrajectoryPose51Speed;
  Rte_DE_TrajectoryPose51Steering Rte_AcuTrajectoryPose48To51_TrajectoryPose51Steering;
  Rte_DE_TrajectoryPose52Acceleration Rte_AcuTrajectoryPose52To55_TrajectoryPose52Acceleration;
  Rte_DE_TrajectoryPose52Curvature Rte_AcuTrajectoryPose52To55_TrajectoryPose52Curvature;
  Rte_DE_TrajectoryPose52Heading Rte_AcuTrajectoryPose52To55_TrajectoryPose52Heading;
  Rte_DE_TrajectoryPose52PositionX Rte_AcuTrajectoryPose52To55_TrajectoryPose52PositionX;
  Rte_DE_TrajectoryPose52PositionY Rte_AcuTrajectoryPose52To55_TrajectoryPose52PositionY;
  Rte_DE_TrajectoryPose52Speed Rte_AcuTrajectoryPose52To55_TrajectoryPose52Speed;
  Rte_DE_TrajectoryPose52Steering Rte_AcuTrajectoryPose52To55_TrajectoryPose52Steering;
  Rte_DE_TrajectoryPose53Acceleration Rte_AcuTrajectoryPose52To55_TrajectoryPose53Acceleration;
  Rte_DE_TrajectoryPose53Curvature Rte_AcuTrajectoryPose52To55_TrajectoryPose53Curvature;
  Rte_DE_TrajectoryPose53Heading Rte_AcuTrajectoryPose52To55_TrajectoryPose53Heading;
  Rte_DE_TrajectoryPose53PositionX Rte_AcuTrajectoryPose52To55_TrajectoryPose53PositionX;
  Rte_DE_TrajectoryPose53PositionY Rte_AcuTrajectoryPose52To55_TrajectoryPose53PositionY;
  Rte_DE_TrajectoryPose53Speed Rte_AcuTrajectoryPose52To55_TrajectoryPose53Speed;
  Rte_DE_TrajectoryPose53Steering Rte_AcuTrajectoryPose52To55_TrajectoryPose53Steering;
  Rte_DE_TrajectoryPose54Acceleration Rte_AcuTrajectoryPose52To55_TrajectoryPose54Acceleration;
  Rte_DE_TrajectoryPose54Curvature Rte_AcuTrajectoryPose52To55_TrajectoryPose54Curvature;
  Rte_DE_TrajectoryPose54Heading Rte_AcuTrajectoryPose52To55_TrajectoryPose54Heading;
  Rte_DE_TrajectoryPose54PositionX Rte_AcuTrajectoryPose52To55_TrajectoryPose54PositionX;
  Rte_DE_TrajectoryPose54PositionY Rte_AcuTrajectoryPose52To55_TrajectoryPose54PositionY;
  Rte_DE_TrajectoryPose54Speed Rte_AcuTrajectoryPose52To55_TrajectoryPose54Speed;
  Rte_DE_TrajectoryPose54Steering Rte_AcuTrajectoryPose52To55_TrajectoryPose54Steering;
  Rte_DE_TrajectoryPose55Acceleration Rte_AcuTrajectoryPose52To55_TrajectoryPose55Acceleration;
  Rte_DE_TrajectoryPose55Curvature Rte_AcuTrajectoryPose52To55_TrajectoryPose55Curvature;
  Rte_DE_TrajectoryPose55Heading Rte_AcuTrajectoryPose52To55_TrajectoryPose55Heading;
  Rte_DE_TrajectoryPose55PositionX Rte_AcuTrajectoryPose52To55_TrajectoryPose55PositionX;
  Rte_DE_TrajectoryPose55PositionY Rte_AcuTrajectoryPose52To55_TrajectoryPose55PositionY;
  Rte_DE_TrajectoryPose55Speed Rte_AcuTrajectoryPose52To55_TrajectoryPose55Speed;
  Rte_DE_TrajectoryPose55Steering Rte_AcuTrajectoryPose52To55_TrajectoryPose55Steering;
  Rte_DE_TrajectoryPose56Acceleration Rte_AcuTrajectoryPose56To59_TrajectoryPose56Acceleration;
  Rte_DE_TrajectoryPose56Curvature Rte_AcuTrajectoryPose56To59_TrajectoryPose56Curvature;
  Rte_DE_TrajectoryPose56Heading Rte_AcuTrajectoryPose56To59_TrajectoryPose56Heading;
  Rte_DE_TrajectoryPose56PositionX Rte_AcuTrajectoryPose56To59_TrajectoryPose56PositionX;
  Rte_DE_TrajectoryPose56PositionY Rte_AcuTrajectoryPose56To59_TrajectoryPose56PositionY;
  Rte_DE_TrajectoryPose56Speed Rte_AcuTrajectoryPose56To59_TrajectoryPose56Speed;
  Rte_DE_TrajectoryPose56Steering Rte_AcuTrajectoryPose56To59_TrajectoryPose56Steering;
  Rte_DE_TrajectoryPose57Acceleration Rte_AcuTrajectoryPose56To59_TrajectoryPose57Acceleration;
  Rte_DE_TrajectoryPose57Curvature Rte_AcuTrajectoryPose56To59_TrajectoryPose57Curvature;
  Rte_DE_TrajectoryPose57Heading Rte_AcuTrajectoryPose56To59_TrajectoryPose57Heading;
  Rte_DE_TrajectoryPose57PositionX Rte_AcuTrajectoryPose56To59_TrajectoryPose57PositionX;
  Rte_DE_TrajectoryPose57PositionY Rte_AcuTrajectoryPose56To59_TrajectoryPose57PositionY;
  Rte_DE_TrajectoryPose57Speed Rte_AcuTrajectoryPose56To59_TrajectoryPose57Speed;
  Rte_DE_TrajectoryPose57Steering Rte_AcuTrajectoryPose56To59_TrajectoryPose57Steering;
  Rte_DE_TrajectoryPose58Acceleration Rte_AcuTrajectoryPose56To59_TrajectoryPose58Acceleration;
  Rte_DE_TrajectoryPose58Curvature Rte_AcuTrajectoryPose56To59_TrajectoryPose58Curvature;
  Rte_DE_TrajectoryPose58Heading Rte_AcuTrajectoryPose56To59_TrajectoryPose58Heading;
  Rte_DE_TrajectoryPose58PositionX Rte_AcuTrajectoryPose56To59_TrajectoryPose58PositionX;
  Rte_DE_TrajectoryPose58PositionY Rte_AcuTrajectoryPose56To59_TrajectoryPose58PositionY;
  Rte_DE_TrajectoryPose58Speed Rte_AcuTrajectoryPose56To59_TrajectoryPose58Speed;
  Rte_DE_TrajectoryPose58Steering Rte_AcuTrajectoryPose56To59_TrajectoryPose58Steering;
  Rte_DE_TrajectoryPose59Acceleration Rte_AcuTrajectoryPose56To59_TrajectoryPose59Acceleration;
  Rte_DE_TrajectoryPose59Curvature Rte_AcuTrajectoryPose56To59_TrajectoryPose59Curvature;
  Rte_DE_TrajectoryPose59Heading Rte_AcuTrajectoryPose56To59_TrajectoryPose59Heading;
  Rte_DE_TrajectoryPose59PositionX Rte_AcuTrajectoryPose56To59_TrajectoryPose59PositionX;
  Rte_DE_TrajectoryPose59PositionY Rte_AcuTrajectoryPose56To59_TrajectoryPose59PositionY;
  Rte_DE_TrajectoryPose59Speed Rte_AcuTrajectoryPose56To59_TrajectoryPose59Speed;
  Rte_DE_TrajectoryPose59Steering Rte_AcuTrajectoryPose56To59_TrajectoryPose59Steering;
  Rte_DE_TrajectoryPose60Acceleration Rte_AcuTrajectoryPose60To63_TrajectoryPose60Acceleration;
  Rte_DE_TrajectoryPose60Curvature Rte_AcuTrajectoryPose60To63_TrajectoryPose60Curvature;
  Rte_DE_TrajectoryPose60Heading Rte_AcuTrajectoryPose60To63_TrajectoryPose60Heading;
  Rte_DE_TrajectoryPose60PositionX Rte_AcuTrajectoryPose60To63_TrajectoryPose60PositionX;
  Rte_DE_TrajectoryPose60PositionY Rte_AcuTrajectoryPose60To63_TrajectoryPose60PositionY;
  Rte_DE_TrajectoryPose60Speed Rte_AcuTrajectoryPose60To63_TrajectoryPose60Speed;
  Rte_DE_TrajectoryPose60Steering Rte_AcuTrajectoryPose60To63_TrajectoryPose60Steering;
  Rte_DE_TrajectoryPose61Acceleration Rte_AcuTrajectoryPose60To63_TrajectoryPose61Acceleration;
  Rte_DE_TrajectoryPose61Curvature Rte_AcuTrajectoryPose60To63_TrajectoryPose61Curvature;
  Rte_DE_TrajectoryPose61Heading Rte_AcuTrajectoryPose60To63_TrajectoryPose61Heading;
  Rte_DE_TrajectoryPose61PositionX Rte_AcuTrajectoryPose60To63_TrajectoryPose61PositionX;
  Rte_DE_TrajectoryPose61PositionY Rte_AcuTrajectoryPose60To63_TrajectoryPose61PositionY;
  Rte_DE_TrajectoryPose61Speed Rte_AcuTrajectoryPose60To63_TrajectoryPose61Speed;
  Rte_DE_TrajectoryPose61Steering Rte_AcuTrajectoryPose60To63_TrajectoryPose61Steering;
  Rte_DE_TrajectoryPose62Acceleration Rte_AcuTrajectoryPose60To63_TrajectoryPose62Acceleration;
  Rte_DE_TrajectoryPose62Curvature Rte_AcuTrajectoryPose60To63_TrajectoryPose62Curvature;
  Rte_DE_TrajectoryPose62Heading Rte_AcuTrajectoryPose60To63_TrajectoryPose62Heading;
  Rte_DE_TrajectoryPose62PositionX Rte_AcuTrajectoryPose60To63_TrajectoryPose62PositionX;
  Rte_DE_TrajectoryPose62PositionY Rte_AcuTrajectoryPose60To63_TrajectoryPose62PositionY;
  Rte_DE_TrajectoryPose62Speed Rte_AcuTrajectoryPose60To63_TrajectoryPose62Speed;
  Rte_DE_TrajectoryPose62Steering Rte_AcuTrajectoryPose60To63_TrajectoryPose62Steering;
  Rte_DE_TrajectoryPose63Acceleration Rte_AcuTrajectoryPose60To63_TrajectoryPose63Acceleration;
  Rte_DE_TrajectoryPose63Curvature Rte_AcuTrajectoryPose60To63_TrajectoryPose63Curvature;
  Rte_DE_TrajectoryPose63Heading Rte_AcuTrajectoryPose60To63_TrajectoryPose63Heading;
  Rte_DE_TrajectoryPose63PositionX Rte_AcuTrajectoryPose60To63_TrajectoryPose63PositionX;
  Rte_DE_TrajectoryPose63PositionY Rte_AcuTrajectoryPose60To63_TrajectoryPose63PositionY;
  Rte_DE_TrajectoryPose63Speed Rte_AcuTrajectoryPose60To63_TrajectoryPose63Speed;
  Rte_DE_TrajectoryPose63Steering Rte_AcuTrajectoryPose60To63_TrajectoryPose63Steering;
  Rte_DE_TrajectoryPose64Acceleration Rte_AcuTrajectoryPose64To67_TrajectoryPose64Acceleration;
  Rte_DE_TrajectoryPose64Curvature Rte_AcuTrajectoryPose64To67_TrajectoryPose64Curvature;
  Rte_DE_TrajectoryPose64Heading Rte_AcuTrajectoryPose64To67_TrajectoryPose64Heading;
  Rte_DE_TrajectoryPose64PositionX Rte_AcuTrajectoryPose64To67_TrajectoryPose64PositionX;
  Rte_DE_TrajectoryPose64PositionY Rte_AcuTrajectoryPose64To67_TrajectoryPose64PositionY;
  Rte_DE_TrajectoryPose64Speed Rte_AcuTrajectoryPose64To67_TrajectoryPose64Speed;
  Rte_DE_TrajectoryPose64Steering Rte_AcuTrajectoryPose64To67_TrajectoryPose64Steering;
  Rte_DE_TrajectoryPose65Acceleration Rte_AcuTrajectoryPose64To67_TrajectoryPose65Acceleration;
  Rte_DE_TrajectoryPose65Curvature Rte_AcuTrajectoryPose64To67_TrajectoryPose65Curvature;
  Rte_DE_TrajectoryPose65Heading Rte_AcuTrajectoryPose64To67_TrajectoryPose65Heading;
  Rte_DE_TrajectoryPose65PositionX Rte_AcuTrajectoryPose64To67_TrajectoryPose65PositionX;
  Rte_DE_TrajectoryPose65PositionY Rte_AcuTrajectoryPose64To67_TrajectoryPose65PositionY;
  Rte_DE_TrajectoryPose65Speed Rte_AcuTrajectoryPose64To67_TrajectoryPose65Speed;
  Rte_DE_TrajectoryPose65Steering Rte_AcuTrajectoryPose64To67_TrajectoryPose65Steering;
  Rte_DE_TrajectoryPose66Acceleration Rte_AcuTrajectoryPose64To67_TrajectoryPose66Acceleration;
  Rte_DE_TrajectoryPose66Curvature Rte_AcuTrajectoryPose64To67_TrajectoryPose66Curvature;
  Rte_DE_TrajectoryPose66Heading Rte_AcuTrajectoryPose64To67_TrajectoryPose66Heading;
  Rte_DE_TrajectoryPose66PositionX Rte_AcuTrajectoryPose64To67_TrajectoryPose66PositionX;
  Rte_DE_TrajectoryPose66PositionY Rte_AcuTrajectoryPose64To67_TrajectoryPose66PositionY;
  Rte_DE_TrajectoryPose66Speed Rte_AcuTrajectoryPose64To67_TrajectoryPose66Speed;
  Rte_DE_TrajectoryPose66Steering Rte_AcuTrajectoryPose64To67_TrajectoryPose66Steering;
  Rte_DE_TrajectoryPose67Acceleration Rte_AcuTrajectoryPose64To67_TrajectoryPose67Acceleration;
  Rte_DE_TrajectoryPose67Curvature Rte_AcuTrajectoryPose64To67_TrajectoryPose67Curvature;
  Rte_DE_TrajectoryPose67Heading Rte_AcuTrajectoryPose64To67_TrajectoryPose67Heading;
  Rte_DE_TrajectoryPose67PositionX Rte_AcuTrajectoryPose64To67_TrajectoryPose67PositionX;
  Rte_DE_TrajectoryPose67PositionY Rte_AcuTrajectoryPose64To67_TrajectoryPose67PositionY;
  Rte_DE_TrajectoryPose67Speed Rte_AcuTrajectoryPose64To67_TrajectoryPose67Speed;
  Rte_DE_TrajectoryPose67Steering Rte_AcuTrajectoryPose64To67_TrajectoryPose67Steering;
  Rte_DE_TrajectoryPose68Acceleration Rte_AcuTrajectoryPose68To71_TrajectoryPose68Acceleration;
  Rte_DE_TrajectoryPose68Curvature Rte_AcuTrajectoryPose68To71_TrajectoryPose68Curvature;
  Rte_DE_TrajectoryPose68Heading Rte_AcuTrajectoryPose68To71_TrajectoryPose68Heading;
  Rte_DE_TrajectoryPose68PositionX Rte_AcuTrajectoryPose68To71_TrajectoryPose68PositionX;
  Rte_DE_TrajectoryPose68PositionY Rte_AcuTrajectoryPose68To71_TrajectoryPose68PositionY;
  Rte_DE_TrajectoryPose68Speed Rte_AcuTrajectoryPose68To71_TrajectoryPose68Speed;
  Rte_DE_TrajectoryPose68Steering Rte_AcuTrajectoryPose68To71_TrajectoryPose68Steering;
  Rte_DE_TrajectoryPose69Acceleration Rte_AcuTrajectoryPose68To71_TrajectoryPose69Acceleration;
  Rte_DE_TrajectoryPose69Curvature Rte_AcuTrajectoryPose68To71_TrajectoryPose69Curvature;
  Rte_DE_TrajectoryPose69Heading Rte_AcuTrajectoryPose68To71_TrajectoryPose69Heading;
  Rte_DE_TrajectoryPose69PositionX Rte_AcuTrajectoryPose68To71_TrajectoryPose69PositionX;
  Rte_DE_TrajectoryPose69PositionY Rte_AcuTrajectoryPose68To71_TrajectoryPose69PositionY;
  Rte_DE_TrajectoryPose69Speed Rte_AcuTrajectoryPose68To71_TrajectoryPose69Speed;
  Rte_DE_TrajectoryPose69Steering Rte_AcuTrajectoryPose68To71_TrajectoryPose69Steering;
  Rte_DE_TrajectoryPose70Acceleration Rte_AcuTrajectoryPose68To71_TrajectoryPose70Acceleration;
  Rte_DE_TrajectoryPose70Curvature Rte_AcuTrajectoryPose68To71_TrajectoryPose70Curvature;
  Rte_DE_TrajectoryPose70Heading Rte_AcuTrajectoryPose68To71_TrajectoryPose70Heading;
  Rte_DE_TrajectoryPose70PositionX Rte_AcuTrajectoryPose68To71_TrajectoryPose70PositionX;
  Rte_DE_TrajectoryPose70PositionY Rte_AcuTrajectoryPose68To71_TrajectoryPose70PositionY;
  Rte_DE_TrajectoryPose70Speed Rte_AcuTrajectoryPose68To71_TrajectoryPose70Speed;
  Rte_DE_TrajectoryPose70Steering Rte_AcuTrajectoryPose68To71_TrajectoryPose70Steering;
  Rte_DE_TrajectoryPose71Acceleration Rte_AcuTrajectoryPose68To71_TrajectoryPose71Acceleration;
  Rte_DE_TrajectoryPose71Curvature Rte_AcuTrajectoryPose68To71_TrajectoryPose71Curvature;
  Rte_DE_TrajectoryPose71Heading Rte_AcuTrajectoryPose68To71_TrajectoryPose71Heading;
  Rte_DE_TrajectoryPose71PositionX Rte_AcuTrajectoryPose68To71_TrajectoryPose71PositionX;
  Rte_DE_TrajectoryPose71PositionY Rte_AcuTrajectoryPose68To71_TrajectoryPose71PositionY;
  Rte_DE_TrajectoryPose71Speed Rte_AcuTrajectoryPose68To71_TrajectoryPose71Speed;
  Rte_DE_TrajectoryPose71Steering Rte_AcuTrajectoryPose68To71_TrajectoryPose71Steering;
  Rte_DE_TrajectoryPose72Acceleration Rte_AcuTrajectoryPose72To75_TrajectoryPose72Acceleration;
  Rte_DE_TrajectoryPose72Curvature Rte_AcuTrajectoryPose72To75_TrajectoryPose72Curvature;
  Rte_DE_TrajectoryPose72Heading Rte_AcuTrajectoryPose72To75_TrajectoryPose72Heading;
  Rte_DE_TrajectoryPose72PositionX Rte_AcuTrajectoryPose72To75_TrajectoryPose72PositionX;
  Rte_DE_TrajectoryPose72PositionY Rte_AcuTrajectoryPose72To75_TrajectoryPose72PositionY;
  Rte_DE_TrajectoryPose72Speed Rte_AcuTrajectoryPose72To75_TrajectoryPose72Speed;
  Rte_DE_TrajectoryPose72Steering Rte_AcuTrajectoryPose72To75_TrajectoryPose72Steering;
  Rte_DE_TrajectoryPose73Acceleration Rte_AcuTrajectoryPose72To75_TrajectoryPose73Acceleration;
  Rte_DE_TrajectoryPose73Curvature Rte_AcuTrajectoryPose72To75_TrajectoryPose73Curvature;
  Rte_DE_TrajectoryPose73Heading Rte_AcuTrajectoryPose72To75_TrajectoryPose73Heading;
  Rte_DE_TrajectoryPose73PositionX Rte_AcuTrajectoryPose72To75_TrajectoryPose73PositionX;
  Rte_DE_TrajectoryPose73PositionY Rte_AcuTrajectoryPose72To75_TrajectoryPose73PositionY;
  Rte_DE_TrajectoryPose73Speed Rte_AcuTrajectoryPose72To75_TrajectoryPose73Speed;
  Rte_DE_TrajectoryPose73Steering Rte_AcuTrajectoryPose72To75_TrajectoryPose73Steering;
  Rte_DE_TrajectoryPose74Acceleration Rte_AcuTrajectoryPose72To75_TrajectoryPose74Acceleration;
  Rte_DE_TrajectoryPose74Curvature Rte_AcuTrajectoryPose72To75_TrajectoryPose74Curvature;
  Rte_DE_TrajectoryPose74Heading Rte_AcuTrajectoryPose72To75_TrajectoryPose74Heading;
  Rte_DE_TrajectoryPose74PositionX Rte_AcuTrajectoryPose72To75_TrajectoryPose74PositionX;
  Rte_DE_TrajectoryPose74PositionY Rte_AcuTrajectoryPose72To75_TrajectoryPose74PositionY;
  Rte_DE_TrajectoryPose74Speed Rte_AcuTrajectoryPose72To75_TrajectoryPose74Speed;
  Rte_DE_TrajectoryPose74Steering Rte_AcuTrajectoryPose72To75_TrajectoryPose74Steering;
  Rte_DE_TrajectoryPose75Acceleration Rte_AcuTrajectoryPose72To75_TrajectoryPose75Acceleration;
  Rte_DE_TrajectoryPose75Curvature Rte_AcuTrajectoryPose72To75_TrajectoryPose75Curvature;
  Rte_DE_TrajectoryPose75Heading Rte_AcuTrajectoryPose72To75_TrajectoryPose75Heading;
  Rte_DE_TrajectoryPose75PositionX Rte_AcuTrajectoryPose72To75_TrajectoryPose75PositionX;
  Rte_DE_TrajectoryPose75PositionY Rte_AcuTrajectoryPose72To75_TrajectoryPose75PositionY;
  Rte_DE_TrajectoryPose75Speed Rte_AcuTrajectoryPose72To75_TrajectoryPose75Speed;
  Rte_DE_TrajectoryPose75Steering Rte_AcuTrajectoryPose72To75_TrajectoryPose75Steering;
  Rte_DE_TrajectoryPose76Acceleration Rte_AcuTrajectoryPose76To79_TrajectoryPose76Acceleration;
  Rte_DE_TrajectoryPose76Curvature Rte_AcuTrajectoryPose76To79_TrajectoryPose76Curvature;
  Rte_DE_TrajectoryPose76Heading Rte_AcuTrajectoryPose76To79_TrajectoryPose76Heading;
  Rte_DE_TrajectoryPose76PositionX Rte_AcuTrajectoryPose76To79_TrajectoryPose76PositionX;
  Rte_DE_TrajectoryPose76PositionY Rte_AcuTrajectoryPose76To79_TrajectoryPose76PositionY;
  Rte_DE_TrajectoryPose76Speed Rte_AcuTrajectoryPose76To79_TrajectoryPose76Speed;
  Rte_DE_TrajectoryPose76Steering Rte_AcuTrajectoryPose76To79_TrajectoryPose76Steering;
  Rte_DE_TrajectoryPose77Acceleration Rte_AcuTrajectoryPose76To79_TrajectoryPose77Acceleration;
  Rte_DE_TrajectoryPose77Curvature Rte_AcuTrajectoryPose76To79_TrajectoryPose77Curvature;
  Rte_DE_TrajectoryPose77Heading Rte_AcuTrajectoryPose76To79_TrajectoryPose77Heading;
  Rte_DE_TrajectoryPose77PositionX Rte_AcuTrajectoryPose76To79_TrajectoryPose77PositionX;
  Rte_DE_TrajectoryPose77PositionY Rte_AcuTrajectoryPose76To79_TrajectoryPose77PositionY;
  Rte_DE_TrajectoryPose77Speed Rte_AcuTrajectoryPose76To79_TrajectoryPose77Speed;
  Rte_DE_TrajectoryPose77Steering Rte_AcuTrajectoryPose76To79_TrajectoryPose77Steering;
  Rte_DE_TrajectoryPose78Acceleration Rte_AcuTrajectoryPose76To79_TrajectoryPose78Acceleration;
  Rte_DE_TrajectoryPose78Curvature Rte_AcuTrajectoryPose76To79_TrajectoryPose78Curvature;
  Rte_DE_TrajectoryPose78Heading Rte_AcuTrajectoryPose76To79_TrajectoryPose78Heading;
  Rte_DE_TrajectoryPose78PositionX Rte_AcuTrajectoryPose76To79_TrajectoryPose78PositionX;
  Rte_DE_TrajectoryPose78PositionY Rte_AcuTrajectoryPose76To79_TrajectoryPose78PositionY;
  Rte_DE_TrajectoryPose78Speed Rte_AcuTrajectoryPose76To79_TrajectoryPose78Speed;
  Rte_DE_TrajectoryPose78Steering Rte_AcuTrajectoryPose76To79_TrajectoryPose78Steering;
  Rte_DE_TrajectoryPose79Acceleration Rte_AcuTrajectoryPose76To79_TrajectoryPose79Acceleration;
  Rte_DE_TrajectoryPose79Curvature Rte_AcuTrajectoryPose76To79_TrajectoryPose79Curvature;
  Rte_DE_TrajectoryPose79Heading Rte_AcuTrajectoryPose76To79_TrajectoryPose79Heading;
  Rte_DE_TrajectoryPose79PositionX Rte_AcuTrajectoryPose76To79_TrajectoryPose79PositionX;
  Rte_DE_TrajectoryPose79PositionY Rte_AcuTrajectoryPose76To79_TrajectoryPose79PositionY;
  Rte_DE_TrajectoryPose79Speed Rte_AcuTrajectoryPose76To79_TrajectoryPose79Speed;
  Rte_DE_TrajectoryPose79Steering Rte_AcuTrajectoryPose76To79_TrajectoryPose79Steering;
  Rte_DE_TrajectoryPose80Acceleration Rte_AcuTrajectoryPose80To83_TrajectoryPose80Acceleration;
  Rte_DE_TrajectoryPose80Curvature Rte_AcuTrajectoryPose80To83_TrajectoryPose80Curvature;
  Rte_DE_TrajectoryPose80Heading Rte_AcuTrajectoryPose80To83_TrajectoryPose80Heading;
  Rte_DE_TrajectoryPose80PositionX Rte_AcuTrajectoryPose80To83_TrajectoryPose80PositionX;
  Rte_DE_TrajectoryPose80PositionY Rte_AcuTrajectoryPose80To83_TrajectoryPose80PositionY;
  Rte_DE_TrajectoryPose80Speed Rte_AcuTrajectoryPose80To83_TrajectoryPose80Speed;
  Rte_DE_TrajectoryPose80Steering Rte_AcuTrajectoryPose80To83_TrajectoryPose80Steering;
  Rte_DE_TrajectoryPose81Acceleration Rte_AcuTrajectoryPose80To83_TrajectoryPose81Acceleration;
  Rte_DE_TrajectoryPose81Curvature Rte_AcuTrajectoryPose80To83_TrajectoryPose81Curvature;
  Rte_DE_TrajectoryPose81Heading Rte_AcuTrajectoryPose80To83_TrajectoryPose81Heading;
  Rte_DE_TrajectoryPose81PositionX Rte_AcuTrajectoryPose80To83_TrajectoryPose81PositionX;
  Rte_DE_TrajectoryPose81PositionY Rte_AcuTrajectoryPose80To83_TrajectoryPose81PositionY;
  Rte_DE_TrajectoryPose81Speed Rte_AcuTrajectoryPose80To83_TrajectoryPose81Speed;
  Rte_DE_TrajectoryPose81Steering Rte_AcuTrajectoryPose80To83_TrajectoryPose81Steering;
  Rte_DE_TrajectoryPose82Acceleration Rte_AcuTrajectoryPose80To83_TrajectoryPose82Acceleration;
  Rte_DE_TrajectoryPose82Curvature Rte_AcuTrajectoryPose80To83_TrajectoryPose82Curvature;
  Rte_DE_TrajectoryPose82Heading Rte_AcuTrajectoryPose80To83_TrajectoryPose82Heading;
  Rte_DE_TrajectoryPose82PositionX Rte_AcuTrajectoryPose80To83_TrajectoryPose82PositionX;
  Rte_DE_TrajectoryPose82PositionY Rte_AcuTrajectoryPose80To83_TrajectoryPose82PositionY;
  Rte_DE_TrajectoryPose82Speed Rte_AcuTrajectoryPose80To83_TrajectoryPose82Speed;
  Rte_DE_TrajectoryPose82Steering Rte_AcuTrajectoryPose80To83_TrajectoryPose82Steering;
  Rte_DE_TrajectoryPose83Acceleration Rte_AcuTrajectoryPose80To83_TrajectoryPose83Acceleration;
  Rte_DE_TrajectoryPose83Curvature Rte_AcuTrajectoryPose80To83_TrajectoryPose83Curvature;
  Rte_DE_TrajectoryPose83Heading Rte_AcuTrajectoryPose80To83_TrajectoryPose83Heading;
  Rte_DE_TrajectoryPose83PositionX Rte_AcuTrajectoryPose80To83_TrajectoryPose83PositionX;
  Rte_DE_TrajectoryPose83PositionY Rte_AcuTrajectoryPose80To83_TrajectoryPose83PositionY;
  Rte_DE_TrajectoryPose83Speed Rte_AcuTrajectoryPose80To83_TrajectoryPose83Speed;
  Rte_DE_TrajectoryPose83Steering Rte_AcuTrajectoryPose80To83_TrajectoryPose83Steering;
  Rte_DE_TrajectoryPose84Acceleration Rte_AcuTrajectoryPose84To87_TrajectoryPose84Acceleration;
  Rte_DE_TrajectoryPose84Curvature Rte_AcuTrajectoryPose84To87_TrajectoryPose84Curvature;
  Rte_DE_TrajectoryPose84Heading Rte_AcuTrajectoryPose84To87_TrajectoryPose84Heading;
  Rte_DE_TrajectoryPose84PositionX Rte_AcuTrajectoryPose84To87_TrajectoryPose84PositionX;
  Rte_DE_TrajectoryPose84PositionY Rte_AcuTrajectoryPose84To87_TrajectoryPose84PositionY;
  Rte_DE_TrajectoryPose84Speed Rte_AcuTrajectoryPose84To87_TrajectoryPose84Speed;
  Rte_DE_TrajectoryPose84Steering Rte_AcuTrajectoryPose84To87_TrajectoryPose84Steering;
  Rte_DE_TrajectoryPose85Acceleration Rte_AcuTrajectoryPose84To87_TrajectoryPose85Acceleration;
  Rte_DE_TrajectoryPose85Curvature Rte_AcuTrajectoryPose84To87_TrajectoryPose85Curvature;
  Rte_DE_TrajectoryPose85Heading Rte_AcuTrajectoryPose84To87_TrajectoryPose85Heading;
  Rte_DE_TrajectoryPose85PositionX Rte_AcuTrajectoryPose84To87_TrajectoryPose85PositionX;
  Rte_DE_TrajectoryPose85PositionY Rte_AcuTrajectoryPose84To87_TrajectoryPose85PositionY;
  Rte_DE_TrajectoryPose85Speed Rte_AcuTrajectoryPose84To87_TrajectoryPose85Speed;
  Rte_DE_TrajectoryPose85Steering Rte_AcuTrajectoryPose84To87_TrajectoryPose85Steering;
  Rte_DE_TrajectoryPose86Acceleration Rte_AcuTrajectoryPose84To87_TrajectoryPose86Acceleration;
  Rte_DE_TrajectoryPose86Curvature Rte_AcuTrajectoryPose84To87_TrajectoryPose86Curvature;
  Rte_DE_TrajectoryPose86Heading Rte_AcuTrajectoryPose84To87_TrajectoryPose86Heading;
  Rte_DE_TrajectoryPose86PositionX Rte_AcuTrajectoryPose84To87_TrajectoryPose86PositionX;
  Rte_DE_TrajectoryPose86PositionY Rte_AcuTrajectoryPose84To87_TrajectoryPose86PositionY;
  Rte_DE_TrajectoryPose86Speed Rte_AcuTrajectoryPose84To87_TrajectoryPose86Speed;
  Rte_DE_TrajectoryPose86Steering Rte_AcuTrajectoryPose84To87_TrajectoryPose86Steering;
  Rte_DE_TrajectoryPose87Acceleration Rte_AcuTrajectoryPose84To87_TrajectoryPose87Acceleration;
  Rte_DE_TrajectoryPose87Curvature Rte_AcuTrajectoryPose84To87_TrajectoryPose87Curvature;
  Rte_DE_TrajectoryPose87Heading Rte_AcuTrajectoryPose84To87_TrajectoryPose87Heading;
  Rte_DE_TrajectoryPose87PositionX Rte_AcuTrajectoryPose84To87_TrajectoryPose87PositionX;
  Rte_DE_TrajectoryPose87PositionY Rte_AcuTrajectoryPose84To87_TrajectoryPose87PositionY;
  Rte_DE_TrajectoryPose87Speed Rte_AcuTrajectoryPose84To87_TrajectoryPose87Speed;
  Rte_DE_TrajectoryPose87Steering Rte_AcuTrajectoryPose84To87_TrajectoryPose87Steering;
  Rte_DE_TrajectoryPose88Acceleration Rte_AcuTrajectoryPose88To91_TrajectoryPose88Acceleration;
  Rte_DE_TrajectoryPose88Curvature Rte_AcuTrajectoryPose88To91_TrajectoryPose88Curvature;
  Rte_DE_TrajectoryPose88Heading Rte_AcuTrajectoryPose88To91_TrajectoryPose88Heading;
  Rte_DE_TrajectoryPose88PositionX Rte_AcuTrajectoryPose88To91_TrajectoryPose88PositionX;
  Rte_DE_TrajectoryPose88PositionY Rte_AcuTrajectoryPose88To91_TrajectoryPose88PositionY;
  Rte_DE_TrajectoryPose88Speed Rte_AcuTrajectoryPose88To91_TrajectoryPose88Speed;
  Rte_DE_TrajectoryPose88Steering Rte_AcuTrajectoryPose88To91_TrajectoryPose88Steering;
  Rte_DE_TrajectoryPose89Acceleration Rte_AcuTrajectoryPose88To91_TrajectoryPose89Acceleration;
  Rte_DE_TrajectoryPose89Curvature Rte_AcuTrajectoryPose88To91_TrajectoryPose89Curvature;
  Rte_DE_TrajectoryPose89Heading Rte_AcuTrajectoryPose88To91_TrajectoryPose89Heading;
  Rte_DE_TrajectoryPose89PositionX Rte_AcuTrajectoryPose88To91_TrajectoryPose89PositionX;
  Rte_DE_TrajectoryPose89PositionY Rte_AcuTrajectoryPose88To91_TrajectoryPose89PositionY;
  Rte_DE_TrajectoryPose89Speed Rte_AcuTrajectoryPose88To91_TrajectoryPose89Speed;
  Rte_DE_TrajectoryPose89Steering Rte_AcuTrajectoryPose88To91_TrajectoryPose89Steering;
  Rte_DE_TrajectoryPose90Acceleration Rte_AcuTrajectoryPose88To91_TrajectoryPose90Acceleration;
  Rte_DE_TrajectoryPose90Curvature Rte_AcuTrajectoryPose88To91_TrajectoryPose90Curvature;
  Rte_DE_TrajectoryPose90Heading Rte_AcuTrajectoryPose88To91_TrajectoryPose90Heading;
  Rte_DE_TrajectoryPose90PositionX Rte_AcuTrajectoryPose88To91_TrajectoryPose90PositionX;
  Rte_DE_TrajectoryPose90PositionY Rte_AcuTrajectoryPose88To91_TrajectoryPose90PositionY;
  Rte_DE_TrajectoryPose90Speed Rte_AcuTrajectoryPose88To91_TrajectoryPose90Speed;
  Rte_DE_TrajectoryPose90Steering Rte_AcuTrajectoryPose88To91_TrajectoryPose90Steering;
  Rte_DE_TrajectoryPose91Acceleration Rte_AcuTrajectoryPose88To91_TrajectoryPose91Acceleration;
  Rte_DE_TrajectoryPose91Curvature Rte_AcuTrajectoryPose88To91_TrajectoryPose91Curvature;
  Rte_DE_TrajectoryPose91Heading Rte_AcuTrajectoryPose88To91_TrajectoryPose91Heading;
  Rte_DE_TrajectoryPose91PositionX Rte_AcuTrajectoryPose88To91_TrajectoryPose91PositionX;
  Rte_DE_TrajectoryPose91PositionY Rte_AcuTrajectoryPose88To91_TrajectoryPose91PositionY;
  Rte_DE_TrajectoryPose91Speed Rte_AcuTrajectoryPose88To91_TrajectoryPose91Speed;
  Rte_DE_TrajectoryPose91Steering Rte_AcuTrajectoryPose88To91_TrajectoryPose91Steering;
  Rte_DE_TrajectoryPose92Acceleration Rte_AcuTrajectoryPose92To95_TrajectoryPose92Acceleration;
  Rte_DE_TrajectoryPose92Curvature Rte_AcuTrajectoryPose92To95_TrajectoryPose92Curvature;
  Rte_DE_TrajectoryPose92Heading Rte_AcuTrajectoryPose92To95_TrajectoryPose92Heading;
  Rte_DE_TrajectoryPose92PositionX Rte_AcuTrajectoryPose92To95_TrajectoryPose92PositionX;
  Rte_DE_TrajectoryPose92PositionY Rte_AcuTrajectoryPose92To95_TrajectoryPose92PositionY;
  Rte_DE_TrajectoryPose92Speed Rte_AcuTrajectoryPose92To95_TrajectoryPose92Speed;
  Rte_DE_TrajectoryPose92Steering Rte_AcuTrajectoryPose92To95_TrajectoryPose92Steering;
  Rte_DE_TrajectoryPose93Acceleration Rte_AcuTrajectoryPose92To95_TrajectoryPose93Acceleration;
  Rte_DE_TrajectoryPose93Curvature Rte_AcuTrajectoryPose92To95_TrajectoryPose93Curvature;
  Rte_DE_TrajectoryPose93Heading Rte_AcuTrajectoryPose92To95_TrajectoryPose93Heading;
  Rte_DE_TrajectoryPose93PositionX Rte_AcuTrajectoryPose92To95_TrajectoryPose93PositionX;
  Rte_DE_TrajectoryPose93PositionY Rte_AcuTrajectoryPose92To95_TrajectoryPose93PositionY;
  Rte_DE_TrajectoryPose93Speed Rte_AcuTrajectoryPose92To95_TrajectoryPose93Speed;
  Rte_DE_TrajectoryPose93Steering Rte_AcuTrajectoryPose92To95_TrajectoryPose93Steering;
  Rte_DE_TrajectoryPose94Acceleration Rte_AcuTrajectoryPose92To95_TrajectoryPose94Acceleration;
  Rte_DE_TrajectoryPose94Curvature Rte_AcuTrajectoryPose92To95_TrajectoryPose94Curvature;
  Rte_DE_TrajectoryPose94Heading Rte_AcuTrajectoryPose92To95_TrajectoryPose94Heading;
  Rte_DE_TrajectoryPose94PositionX Rte_AcuTrajectoryPose92To95_TrajectoryPose94PositionX;
  Rte_DE_TrajectoryPose94PositionY Rte_AcuTrajectoryPose92To95_TrajectoryPose94PositionY;
  Rte_DE_TrajectoryPose94Speed Rte_AcuTrajectoryPose92To95_TrajectoryPose94Speed;
  Rte_DE_TrajectoryPose94Steering Rte_AcuTrajectoryPose92To95_TrajectoryPose94Steering;
  Rte_DE_TrajectoryPose95Acceleration Rte_AcuTrajectoryPose92To95_TrajectoryPose95Acceleration;
  Rte_DE_TrajectoryPose95Curvature Rte_AcuTrajectoryPose92To95_TrajectoryPose95Curvature;
  Rte_DE_TrajectoryPose95Heading Rte_AcuTrajectoryPose92To95_TrajectoryPose95Heading;
  Rte_DE_TrajectoryPose95PositionX Rte_AcuTrajectoryPose92To95_TrajectoryPose95PositionX;
  Rte_DE_TrajectoryPose95PositionY Rte_AcuTrajectoryPose92To95_TrajectoryPose95PositionY;
  Rte_DE_TrajectoryPose95Speed Rte_AcuTrajectoryPose92To95_TrajectoryPose95Speed;
  Rte_DE_TrajectoryPose95Steering Rte_AcuTrajectoryPose92To95_TrajectoryPose95Steering;
  Rte_DE_TrajectoryPose96Acceleration Rte_AcuTrajectoryPose96To99_TrajectoryPose96Acceleration;
  Rte_DE_TrajectoryPose96Curvature Rte_AcuTrajectoryPose96To99_TrajectoryPose96Curvature;
  Rte_DE_TrajectoryPose96Heading Rte_AcuTrajectoryPose96To99_TrajectoryPose96Heading;
  Rte_DE_TrajectoryPose96PositionX Rte_AcuTrajectoryPose96To99_TrajectoryPose96PositionX;
  Rte_DE_TrajectoryPose96PositionY Rte_AcuTrajectoryPose96To99_TrajectoryPose96PositionY;
  Rte_DE_TrajectoryPose96Speed Rte_AcuTrajectoryPose96To99_TrajectoryPose96Speed;
  Rte_DE_TrajectoryPose96Steering Rte_AcuTrajectoryPose96To99_TrajectoryPose96Steering;
  Rte_DE_TrajectoryPose97Acceleration Rte_AcuTrajectoryPose96To99_TrajectoryPose97Acceleration;
  Rte_DE_TrajectoryPose97Curvature Rte_AcuTrajectoryPose96To99_TrajectoryPose97Curvature;
  Rte_DE_TrajectoryPose97Heading Rte_AcuTrajectoryPose96To99_TrajectoryPose97Heading;
  Rte_DE_TrajectoryPose97PositionX Rte_AcuTrajectoryPose96To99_TrajectoryPose97PositionX;
  Rte_DE_TrajectoryPose97PositionY Rte_AcuTrajectoryPose96To99_TrajectoryPose97PositionY;
  Rte_DE_TrajectoryPose97Speed Rte_AcuTrajectoryPose96To99_TrajectoryPose97Speed;
  Rte_DE_TrajectoryPose97Steering Rte_AcuTrajectoryPose96To99_TrajectoryPose97Steering;
  Rte_DE_TrajectoryPose98Acceleration Rte_AcuTrajectoryPose96To99_TrajectoryPose98Acceleration;
  Rte_DE_TrajectoryPose98Curvature Rte_AcuTrajectoryPose96To99_TrajectoryPose98Curvature;
  Rte_DE_TrajectoryPose98Heading Rte_AcuTrajectoryPose96To99_TrajectoryPose98Heading;
  Rte_DE_TrajectoryPose98PositionX Rte_AcuTrajectoryPose96To99_TrajectoryPose98PositionX;
  Rte_DE_TrajectoryPose98PositionY Rte_AcuTrajectoryPose96To99_TrajectoryPose98PositionY;
  Rte_DE_TrajectoryPose98Speed Rte_AcuTrajectoryPose96To99_TrajectoryPose98Speed;
  Rte_DE_TrajectoryPose98Steering Rte_AcuTrajectoryPose96To99_TrajectoryPose98Steering;
  Rte_DE_TrajectoryPose99Acceleration Rte_AcuTrajectoryPose96To99_TrajectoryPose99Acceleration;
  Rte_DE_TrajectoryPose99Curvature Rte_AcuTrajectoryPose96To99_TrajectoryPose99Curvature;
  Rte_DE_TrajectoryPose99Heading Rte_AcuTrajectoryPose96To99_TrajectoryPose99Heading;
  Rte_DE_TrajectoryPose99PositionX Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionX;
  Rte_DE_TrajectoryPose99PositionY Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionY;
  Rte_DE_TrajectoryPose99Speed Rte_AcuTrajectoryPose96To99_TrajectoryPose99Speed;
  Rte_DE_TrajectoryPose99Steering Rte_AcuTrajectoryPose96To99_TrajectoryPose99Steering;
  Rte_DE_AcuControl Rte_AcuControl_AcuControl;
  Rte_DE_TrajectoryInput Rte_Trajectory_Trajectory;
} Rte_tsRB_AcuSigInput_TrajectoryInput_60ms; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_CSI_LaneInfo_Struct Rte_CSI_LaneInfo_CSI_LaneInfo;
  Rte_DE_VSI_VehicleInfo_Struct Rte_VSI_VehicleInfo_VSI_VehicleInfo;
} Rte_tsRB_LatCtrlFct_FSA_60ms_Runnable; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_ControlDebug Rte_ControlDebug_ControlDebug;
  Rte_DE_EstimationDebug Rte_EstimationDebug_EstimationDebug;
  Rte_DE_ControlErrorHeading Rte_FallbackControlDebugInfo_ControlErrorHeading;
  Rte_DE_ControlErrorLateral Rte_FallbackControlDebugInfo_ControlErrorLateral;
  Rte_DE_ControlErrorLongitudinal Rte_FallbackControlDebugInfo_ControlErrorLongitudinal;
  Rte_DE_ControlErrorVelocity Rte_FallbackControlDebugInfo_ControlErrorVelocity;
  Rte_DE_ControlReserve0 Rte_FallbackControlDebugInfo_ControlReserve0;
  Rte_DE_ControlReserve1 Rte_FallbackControlDebugInfo_ControlReserve1;
  Rte_DE_ControlReserve2 Rte_FallbackControlDebugInfo_ControlReserve2;
  Rte_DE_ControlReserve3 Rte_FallbackControlDebugInfo_ControlReserve3;
  Rte_DE_ControlReserve4 Rte_FallbackControlDebugInfo_ControlReserve4;
  Rte_DE_ControlReserve5 Rte_FallbackControlDebugInfo_ControlReserve5;
  Rte_DE_ControlReserve6 Rte_FallbackControlDebugInfo_ControlReserve6;
  Rte_DE_ControlReserve7 Rte_FallbackControlDebugInfo_ControlReserve7;
  Rte_DE_ControlReserve8 Rte_FallbackControlDebugInfo_ControlReserve8;
  Rte_DE_ControlReserve9 Rte_FallbackControlDebugInfo_ControlReserve9;
  Rte_DE_ControlTimeStamp Rte_FallbackControlDebugInfo_ControlTimeStamp;
  Rte_DE_LatPidDistanceDComponent Rte_FallbackControlDebugInfo_LatPidDistanceDComponent;
  Rte_DE_LatPidDistanceIComponent Rte_FallbackControlDebugInfo_LatPidDistanceIComponent;
  Rte_DE_LatPidDistancePComponent Rte_FallbackControlDebugInfo_LatPidDistancePComponent;
  Rte_DE_LatPidDistanceTotal Rte_FallbackControlDebugInfo_LatPidDistanceTotal;
  Rte_DE_LatPidHeadingDComponent Rte_FallbackControlDebugInfo_LatPidHeadingDComponent;
  Rte_DE_LatPidHeadingIComponent Rte_FallbackControlDebugInfo_LatPidHeadingIComponent;
  Rte_DE_LatPidHeadingPComponent Rte_FallbackControlDebugInfo_LatPidHeadingPComponent;
  Rte_DE_LatPidHeadingTotal Rte_FallbackControlDebugInfo_LatPidHeadingTotal;
  Rte_DE_LatSteeringFeeback Rte_FallbackControlDebugInfo_LatSteeringFeeback;
  Rte_DE_LatSteeringFeedforward Rte_FallbackControlDebugInfo_LatSteeringFeedforward;
  Rte_DE_LongAccelerationFeeback Rte_FallbackControlDebugInfo_LongAccelerationFeeback;
  Rte_DE_LongAccelerationFeedforward Rte_FallbackControlDebugInfo_LongAccelerationFeedforward;
  Rte_DE_LongPidDistanceDComponent Rte_FallbackControlDebugInfo_LongPidDistanceDComponent;
  Rte_DE_LongPidDistanceIComponent Rte_FallbackControlDebugInfo_LongPidDistanceIComponent;
  Rte_DE_LongPidDistancePComponent Rte_FallbackControlDebugInfo_LongPidDistancePComponent;
  Rte_DE_LongPidDistanceTotal Rte_FallbackControlDebugInfo_LongPidDistanceTotal;
  Rte_DE_LongPidSpeedDComponent Rte_FallbackControlDebugInfo_LongPidSpeedDComponent;
  Rte_DE_LongPidSpeedIComponent Rte_FallbackControlDebugInfo_LongPidSpeedIComponent;
  Rte_DE_LongPidSpeedPComponent Rte_FallbackControlDebugInfo_LongPidSpeedPComponent;
  Rte_DE_LongPidSpeedTotal Rte_FallbackControlDebugInfo_LongPidSpeedTotal;
  Rte_DE_PoseDebugCanbusState Rte_FallbackPoseDebug_PoseDebugCanbusState;
  Rte_DE_PoseDebugIsStationary Rte_FallbackPoseDebug_PoseDebugIsStationary;
  Rte_DE_PoseDebugPositionX Rte_FallbackPoseDebug_PoseDebugPositionX;
  Rte_DE_PoseDebugPositionXStd Rte_FallbackPoseDebug_PoseDebugPositionXStd;
  Rte_DE_PoseDebugPositionY Rte_FallbackPoseDebug_PoseDebugPositionY;
  Rte_DE_PoseDebugPositionYStd Rte_FallbackPoseDebug_PoseDebugPositionYStd;
  Rte_DE_PoseDebugReserved0 Rte_FallbackPoseDebug_PoseDebugReserved0;
  Rte_DE_PoseDebugReserved1 Rte_FallbackPoseDebug_PoseDebugReserved1;
  Rte_DE_PoseDebugReserved2 Rte_FallbackPoseDebug_PoseDebugReserved2;
  Rte_DE_PoseDebugReserved3 Rte_FallbackPoseDebug_PoseDebugReserved3;
  Rte_DE_PoseDebugReserved4 Rte_FallbackPoseDebug_PoseDebugReserved4;
  Rte_DE_PoseDebugReserved5 Rte_FallbackPoseDebug_PoseDebugReserved5;
  Rte_DE_PoseDebugReserved6 Rte_FallbackPoseDebug_PoseDebugReserved6;
  Rte_DE_PoseDebugReserved7 Rte_FallbackPoseDebug_PoseDebugReserved7;
  Rte_DE_PoseDebugReserved8 Rte_FallbackPoseDebug_PoseDebugReserved8;
  Rte_DE_PoseDebugSideSlipAngle Rte_FallbackPoseDebug_PoseDebugSideSlipAngle;
  Rte_DE_PoseDebugSpeed Rte_FallbackPoseDebug_PoseDebugSpeed;
  Rte_DE_PoseDebugSpeedStd Rte_FallbackPoseDebug_PoseDebugSpeedStd;
  Rte_DE_PoseDebugStateType Rte_FallbackPoseDebug_PoseDebugStateType;
  Rte_DE_PoseDebugTimeStamp Rte_FallbackPoseDebug_PoseDebugTimeStamp;
  Rte_DE_PoseDebugVehicleState Rte_FallbackPoseDebug_PoseDebugVehicleState;
  Rte_DE_PoseDebugYaw Rte_FallbackPoseDebug_PoseDebugYaw;
  Rte_DE_PoseDebugYawStd Rte_FallbackPoseDebug_PoseDebugYawStd;
} Rte_tsRB_Debug_Debug_60ms; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

/* PRQA S 0750 L1 */ /* MD_MSR_Union */
typedef union
{
  Rte_tsRB_AcuSigInput_TrajectoryInput_60ms Rte_AcuSigInput_TrajectoryInput_60ms;
  Rte_tsRB_LatCtrlFct_FSA_60ms_Runnable Rte_LatCtrlFct_FSA_60ms_Runnable;
  Rte_tsRB_Debug_Debug_60ms Rte_Debug_Debug_60ms;
} Rte_tuRB_MainTask_Core0_60ms;
/* PRQA L:L1 */

typedef struct
{
  Rte_DE_CSI_LaneInfo_Struct Rte_I_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo;
} Rte_tsTB_MainTask_Core0_60ms;

typedef struct
{
  Rte_tsTB_MainTask_Core0_60ms Rte_TB;
  Rte_tuRB_MainTask_Core0_60ms Rte_RB;
} Rte_tsMainTask_Core0_60ms;

typedef struct
{
  Rte_DE_VSI_McuCanTimeout_Struct Rte_VSI_McuCanTimeout_VSI_McuCanTimeout;
  Rte_DE_VSI_VehInfoFor1V1R_Struct Rte_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R;
  Rte_DE_VSI_VehicleInfo_Struct Rte_VSI_VehicleInfo_VSI_VehicleInfo;
} Rte_tsRB_VehSigInput_VehSigInput_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_CSI_LaneInfo_Struct Rte_CSI_LaneInfo_CSI_LaneInfo;
  Rte_DE_CSI_ObjectInfo_Struct Rte_CSI_ObjectInfo_CSI_ObjectInfo;
} Rte_tsRB_CameraSigInput_CameraSigInput_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_AcuControl Rte_AcuControl_AcuControl;
  Rte_DE_GnssPoseInput Rte_GnssPose_GnssPose;
  Rte_DE_LocalizationPoseInput Rte_LocalizationPose_LocalizationPose;
  Rte_DE_uint32 Rte_Timestamp_Timestamp;
  Rte_DE_TrajectoryInput Rte_Trajectory_Trajectory;
} Rte_tsRB_AcuSigInput_AcuSigInput_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_LAT_CtrlCmd_Struct Rte_LAT_CtrlCmd_LAT_CtrlCmd;
} Rte_tsRB_LatCtrlFct_LatCtrlFct_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_LGT_CtrlCmd_Struct Rte_LGT_CtrlCmd_LGT_CtrlCmd;
} Rte_tsRB_LgtCtrlFct_LongitudinalControlFunction_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_ControlOutput Rte_ControlCommand_ControlCommand;
  Rte_DE_ControlDebug Rte_ControlDebug_ControlDebug;
  Rte_DE_EstimationDebug Rte_EstimationDebug_EstimationDebug;
} Rte_tsRB_Control_Control_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_ESC_ABA_active Rte_ESC_DA_MESSAGE_ESC_ABA_active;
  Rte_DE_ESC_ABA_available Rte_ESC_DA_MESSAGE_ESC_ABA_available;
  Rte_DE_ESC_ABP_active Rte_ESC_DA_MESSAGE_ESC_ABP_active;
  Rte_DE_ESC_ABP_available Rte_ESC_DA_MESSAGE_ESC_ABP_available;
  Rte_DE_ESC_AEB_active Rte_ESC_DA_MESSAGE_ESC_AEB_active;
  Rte_DE_ESC_AEB_available Rte_ESC_DA_MESSAGE_ESC_AEB_available;
  Rte_DE_ESC_AWB_active Rte_ESC_DA_MESSAGE_ESC_AWB_active;
  Rte_DE_ESC_AWB_available Rte_ESC_DA_MESSAGE_ESC_AWB_available;
  Rte_DE_ESC_BrakeTempTooHigh Rte_ESC_DA_MESSAGE_ESC_BrakeTempTooHigh;
  Rte_DE_ESC_DA_MESSAGE_AliveCounter Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_AliveCounter;
  Rte_DE_ESC_DA_MESSAGE_Checksum Rte_ESC_DA_MESSAGE_ESC_DA_MESSAGE_Checksum;
  Rte_DE_ESC_DTC_Active Rte_ESC_DA_MESSAGE_ESC_DTC_Active;
  Rte_DE_ESC_DiagExtModSts Rte_ESC_DA_MESSAGE_ESC_DiagExtModSts;
  Rte_DE_ESC_NoBrakeForce Rte_ESC_DA_MESSAGE_ESC_NoBrakeForce;
  Rte_DE_ESC_QDCFRS Rte_ESC_DA_MESSAGE_ESC_QDCFRS;
  Rte_DE_ESC_Vehiclestandstill Rte_ESC_DA_MESSAGE_ESC_Vehiclestandstill;
  Rte_DE_ESC_FLWheelDirection Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelDirection;
  Rte_DE_ESC_FLWheelSpeedInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedInvalid;
  Rte_DE_ESC_FLWheelSpeedKPH Rte_ESC_FrontWheelSpeedKPH_ESC_FLWheelSpeedKPH;
  Rte_DE_ESC_FRWheelDirection Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelDirection;
  Rte_DE_ESC_FRWheelSpeedInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedInvalid;
  Rte_DE_ESC_FRWheelSpeedKPH Rte_ESC_FrontWheelSpeedKPH_ESC_FRWheelSpeedKPH;
  Rte_DE_ESC_FrontWheelSpeedsKPH_AliveCounter Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_AliveCounter;
  Rte_DE_ESC_FrontWheelSpeedsKPH_Checksum Rte_ESC_FrontWheelSpeedKPH_ESC_FrontWheelSpeedsKPH_Checksum;
  Rte_DE_ESC_Mcylinder_Pressure Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_Pressure;
  Rte_DE_ESC_Mcylinder_PressureInvalid Rte_ESC_FrontWheelSpeedKPH_ESC_Mcylinder_PressureInvalid;
  Rte_DE_ESC_RLWheelDirection Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelDirection;
  Rte_DE_ESC_RLWheelSpeedInvalid Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedInvalid;
  Rte_DE_ESC_RLWheelSpeedKPH Rte_ESC_RearWheelSpeedKPH_ESC_RLWheelSpeedKPH;
  Rte_DE_ESC_RRWheelDirection Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelDirection;
  Rte_DE_ESC_RRWheelSpeedInvalid Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedInvalid;
  Rte_DE_ESC_RRWheelSpeedKPH Rte_ESC_RearWheelSpeedKPH_ESC_RRWheelSpeedKPH;
  Rte_DE_ESC_RearWheelSpeedsKPH_AliveCounter Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_AliveCounter;
  Rte_DE_ESC_RearWheelSpeedsKPH_Checksum Rte_ESC_RearWheelSpeedKPH_ESC_RearWheelSpeedsKPH_Checksum;
  Rte_DE_ESC_ABSActive Rte_ESC_Status_ESC_ABSActive;
  Rte_DE_ESC_AVHStatus Rte_ESC_Status_ESC_AVHStatus;
  Rte_DE_ESC_BrakePedalSwitchInvalid Rte_ESC_Status_ESC_BrakePedalSwitchInvalid;
  Rte_DE_ESC_BrakePedalSwitchStatus Rte_ESC_Status_ESC_BrakePedalSwitchStatus;
  Rte_DE_ESC_EPBStatus Rte_ESC_Status_ESC_EPBStatus;
  Rte_DE_ESC_ESPActive Rte_ESC_Status_ESC_ESPActive;
  Rte_DE_ESC_ESPFailed Rte_ESC_Status_ESC_ESPFailed;
  Rte_DE_ESC_HHCActive Rte_ESC_Status_ESC_HHCActive;
  Rte_DE_ESC_PATAResponse Rte_ESC_Status_ESC_PATAResponse;
  Rte_DE_ESC_Status_AliveCounter Rte_ESC_Status_ESC_Status_AliveCounter;
  Rte_DE_ESC_Status_Checksum Rte_ESC_Status_ESC_Status_Checksum;
  Rte_DE_ESC_TCSActive Rte_ESC_Status_ESC_TCSActive;
  Rte_DE_ESC_VehicleSpeed Rte_ESC_Status_ESC_VehicleSpeed;
  Rte_DE_ESC_VehicleSpeedInvalid Rte_ESC_Status_ESC_VehicleSpeedInvalid;
  Rte_DE_FBS_DebugInfo_Struct Rte_FBS_DebugInfo_FBS_DebugInfo;
  Rte_DE_AccRequestAfterRateLimit Rte_FallbackDebugInfo_AccRequestAfterRateLimit;
  Rte_DE_AccRequestByOutOfOdd Rte_FallbackDebugInfo_AccRequestByOutOfOdd;
  Rte_DE_AccRequestBySpeed Rte_FallbackDebugInfo_AccRequestBySpeed;
  Rte_DE_AccRequestForSystemError Rte_FallbackDebugInfo_AccRequestForSystemError;
  Rte_DE_AvoidCollisionEnable Rte_FallbackDebugInfo_AvoidCollisionEnable;
  Rte_DE_EgoLaneWidth Rte_FallbackDebugInfo_EgoLaneWidth;
  Rte_DE_EgoStopTime Rte_FallbackDebugInfo_EgoStopTime;
  Rte_DE_EmergencyBrakeAcc Rte_FallbackDebugInfo_EmergencyBrakeAcc;
  Rte_DE_FallbackDebugInfoReserve1 Rte_FallbackDebugInfo_FallbackDebugInfoReserve1;
  Rte_DE_FallbackDebugInfoReserve2 Rte_FallbackDebugInfo_FallbackDebugInfoReserve2;
  Rte_DE_FallbackDebugInfoReserve3 Rte_FallbackDebugInfo_FallbackDebugInfoReserve3;
  Rte_DE_FallbackDebugInfoReserve4 Rte_FallbackDebugInfo_FallbackDebugInfoReserve4;
  Rte_DE_FallbackDebugInfoReserve5 Rte_FallbackDebugInfo_FallbackDebugInfoReserve5;
  Rte_DE_FallbackDebugInfoRollingCounter Rte_FallbackDebugInfo_FallbackDebugInfoRollingCounter;
  Rte_DE_FallbackTriggerStatus Rte_FallbackDebugInfo_FallbackTriggerStatus;
  Rte_DE_FeedforwardsSteerAngle Rte_FallbackDebugInfo_FeedforwardsSteerAngle;
  Rte_DE_GradientLimitAccRequest Rte_FallbackDebugInfo_GradientLimitAccRequest;
  Rte_DE_HeadingAngleContribution Rte_FallbackDebugInfo_HeadingAngleContribution;
  Rte_DE_HeadingAngleError Rte_FallbackDebugInfo_HeadingAngleError;
  Rte_DE_HeadingAngleErrorWeight Rte_FallbackDebugInfo_HeadingAngleErrorWeight;
  Rte_DE_LaneValidState Rte_FallbackDebugInfo_LaneValidState;
  Rte_DE_LateralContribution Rte_FallbackDebugInfo_LateralContribution;
  Rte_DE_LateralDistanceError Rte_FallbackDebugInfo_LateralDistanceError;
  Rte_DE_LateralDistanceErrorWeight Rte_FallbackDebugInfo_LateralDistanceErrorWeight;
  Rte_DE_LateralSystemState Rte_FallbackDebugInfo_LateralSystemState;
  Rte_DE_LimitAccRequest Rte_FallbackDebugInfo_LimitAccRequest;
  Rte_DE_LimitSteerAngle Rte_FallbackDebugInfo_LimitSteerAngle;
  Rte_DE_LimitSteerAngleRequest Rte_FallbackDebugInfo_LimitSteerAngleRequest;
  Rte_DE_LongAccRequest Rte_FallbackDebugInfo_LongAccRequest;
  Rte_DE_LongNecAcc Rte_FallbackDebugInfo_LongNecAcc;
  Rte_DE_LqrIterationError Rte_FallbackDebugInfo_LqrIterationError;
  Rte_DE_LqrIterationNums Rte_FallbackDebugInfo_LqrIterationNums;
  Rte_DE_MaxSteerAngleRateThreshold Rte_FallbackDebugInfo_MaxSteerAngleRateThreshold;
  Rte_DE_MaxSteerAngleThreshold Rte_FallbackDebugInfo_MaxSteerAngleThreshold;
  Rte_DE_MinAccRate Rte_FallbackDebugInfo_MinAccRate;
  Rte_DE_ObjectStopTime Rte_FallbackDebugInfo_ObjectStopTime;
  Rte_DE_RawAccRequest Rte_FallbackDebugInfo_RawAccRequest;
  Rte_DE_RawSteerAngle Rte_FallbackDebugInfo_RawSteerAngle;
  Rte_DE_SafeDistance Rte_FallbackDebugInfo_SafeDistance;
  Rte_DE_SteerAngle Rte_FallbackDebugInfo_SteerAngle;
  Rte_DE_SteerAngleByLQR Rte_FallbackDebugInfo_SteerAngleByLQR;
  Rte_DE_SteerAngleForSystemError Rte_FallbackDebugInfo_SteerAngleForSystemError;
  Rte_DE_SystemState Rte_FallbackDebugInfo_SystemState;
  Rte_DE_TimeToCollison Rte_FallbackDebugInfo_TimeToCollison;
  Rte_DE_TrajectoryCurvature Rte_FallbackDebugInfo_TrajectoryCurvature;
  Rte_DE_TrajectoryCurvatureChange Rte_FallbackDebugInfo_TrajectoryCurvatureChange;
  Rte_DE_TrajectoryHeadingAngle Rte_FallbackDebugInfo_TrajectoryHeadingAngle;
  Rte_DE_TrajectoryLength Rte_FallbackDebugInfo_TrajectoryLength;
  Rte_DE_TrajectoryPosY0 Rte_FallbackDebugInfo_TrajectoryPosY0;
  Rte_DE_AcuFbCanMessageID Rte_FallbackSystemStatus_AcuFbCanMessageID;
  Rte_DE_AcuFbCanTimeout Rte_FallbackSystemStatus_AcuFbCanTimeout;
  Rte_DE_AcuFbCanTimer Rte_FallbackSystemStatus_AcuFbCanTimer;
  Rte_DE_AcuMid3SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid3SsmCounter0MessageID;
  Rte_DE_AcuMid3SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timeout;
  Rte_DE_AcuMid3SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid3SsmCounter0Timer;
  Rte_DE_AcuMid3SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid3SsmCounter1MessageID;
  Rte_DE_AcuMid3SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timeout;
  Rte_DE_AcuMid3SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid3SsmCounter1Timer;
  Rte_DE_AcuMid5SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid5SsmCounter0MessageID;
  Rte_DE_AcuMid5SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timeout;
  Rte_DE_AcuMid5SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid5SsmCounter0Timer;
  Rte_DE_AcuMid5SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid5SsmCounter1MessageID;
  Rte_DE_AcuMid5SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timeout;
  Rte_DE_AcuMid5SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid5SsmCounter1Timer;
  Rte_DE_AcuMid6SsmCounter0MessageID Rte_FallbackSystemStatus_AcuMid6SsmCounter0MessageID;
  Rte_DE_AcuMid6SsmCounter0Timeout Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timeout;
  Rte_DE_AcuMid6SsmCounter0Timer Rte_FallbackSystemStatus_AcuMid6SsmCounter0Timer;
  Rte_DE_AcuMid6SsmCounter1MessageID Rte_FallbackSystemStatus_AcuMid6SsmCounter1MessageID;
  Rte_DE_AcuMid6SsmCounter1Timeout Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timeout;
  Rte_DE_AcuMid6SsmCounter1Timer Rte_FallbackSystemStatus_AcuMid6SsmCounter1Timer;
  Rte_DE_AswSoftwarewareVersion Rte_FallbackSystemStatus_AswSoftwarewareVersion;
  Rte_DE_BootLoaderVersion Rte_FallbackSystemStatus_BootLoaderVersion;
  Rte_DE_BswSoftwarewareVersion Rte_FallbackSystemStatus_BswSoftwarewareVersion;
  Rte_DE_FrontCameraCalibrationStatus Rte_FallbackSystemStatus_FrontCameraCalibrationStatus;
  Rte_DE_FrontCameraCanMessageID Rte_FallbackSystemStatus_FrontCameraCanMessageID;
  Rte_DE_FrontCameraCanTimeout Rte_FallbackSystemStatus_FrontCameraCanTimeout;
  Rte_DE_FrontCameraCanTimer Rte_FallbackSystemStatus_FrontCameraCanTimer;
  Rte_DE_FrontCameraFailureStatus Rte_FallbackSystemStatus_FrontCameraFailureStatus;
  Rte_DE_FrontRadarCalibrationStatus Rte_FallbackSystemStatus_FrontRadarCalibrationStatus;
  Rte_DE_FrontRadarCanMessageID Rte_FallbackSystemStatus_FrontRadarCanMessageID;
  Rte_DE_FrontRadarCanTimeout Rte_FallbackSystemStatus_FrontRadarCanTimeout;
  Rte_DE_FrontRadarCanTimer Rte_FallbackSystemStatus_FrontRadarCanTimer;
  Rte_DE_FrontRadarFailureStatus Rte_FallbackSystemStatus_FrontRadarFailureStatus;
  Rte_DE_SystemStatusReserved1 Rte_FallbackSystemStatus_SystemStatusReserved1;
  Rte_DE_SystemStatusReserved2 Rte_FallbackSystemStatus_SystemStatusReserved2;
  Rte_DE_SystemStatusReserved3 Rte_FallbackSystemStatus_SystemStatusReserved3;
  Rte_DE_SystemStatusReserved4 Rte_FallbackSystemStatus_SystemStatusReserved4;
  Rte_DE_SystemStatusRollingCounter Rte_FallbackSystemStatus_SystemStatusRollingCounter;
  Rte_DE_VehMid3SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid3SsmCounter0MessageID;
  Rte_DE_VehMid3SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid3SsmCounter0Timeout;
  Rte_DE_VehMid3SsmCounter0Timer Rte_FallbackSystemStatus_VehMid3SsmCounter0Timer;
  Rte_DE_VehMid3SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid3SsmCounter1MessageID;
  Rte_DE_VehMid3SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid3SsmCounter1Timeout;
  Rte_DE_VehMid3SsmCounter1Timer Rte_FallbackSystemStatus_VehMid3SsmCounter1Timer;
  Rte_DE_VehMid3VcuCounter0MessageID Rte_FallbackSystemStatus_VehMid3VcuCounter0MessageID;
  Rte_DE_VehMid3VcuCounter0Timeout Rte_FallbackSystemStatus_VehMid3VcuCounter0Timeout;
  Rte_DE_VehMid3VcuCounter0Timer Rte_FallbackSystemStatus_VehMid3VcuCounter0Timer;
  Rte_DE_VehMid3VcuCounter1MessageID Rte_FallbackSystemStatus_VehMid3VcuCounter1MessageID;
  Rte_DE_VehMid3VcuCounter1Timeout Rte_FallbackSystemStatus_VehMid3VcuCounter1Timeout;
  Rte_DE_VehMid3VcuCounter1Timer Rte_FallbackSystemStatus_VehMid3VcuCounter1Timer;
  Rte_DE_VehMid5SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid5SsmCounter0MessageID;
  Rte_DE_VehMid5SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid5SsmCounter0Timeout;
  Rte_DE_VehMid5SsmCounter0Timer Rte_FallbackSystemStatus_VehMid5SsmCounter0Timer;
  Rte_DE_VehMid5SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid5SsmCounter1MessageID;
  Rte_DE_VehMid5SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid5SsmCounter1Timeout;
  Rte_DE_VehMid5SsmCounter1Timer Rte_FallbackSystemStatus_VehMid5SsmCounter1Timer;
  Rte_DE_VehMid6SsmCounter0MessageID Rte_FallbackSystemStatus_VehMid6SsmCounter0MessageID;
  Rte_DE_VehMid6SsmCounter0Timeout Rte_FallbackSystemStatus_VehMid6SsmCounter0Timeout;
  Rte_DE_VehMid6SsmCounter0Timer Rte_FallbackSystemStatus_VehMid6SsmCounter0Timer;
  Rte_DE_VehMid6SsmCounter1MessageID Rte_FallbackSystemStatus_VehMid6SsmCounter1MessageID;
  Rte_DE_VehMid6SsmCounter1Timeout Rte_FallbackSystemStatus_VehMid6SsmCounter1Timeout;
  Rte_DE_VehMid6SsmCounter1Timer Rte_FallbackSystemStatus_VehMid6SsmCounter1Timer;
  Rte_DE_FallbackSelfCheckStatus Rte_FbAcuAvailable_FallbackSelfCheckStatus;
  Rte_DE_FbAcuReserved Rte_FbAcuAvailable_FbAcuReserved;
  Rte_DE_FbAcuRollingCounter Rte_FbAcuAvailable_FbAcuRollingCounter;
  Rte_DE_McuStatus Rte_FbAcuAvailable_McuStatus;
  Rte_DE_Sensor1v1rStatus Rte_FbAcuAvailable_Sensor1v1rStatus;
  Rte_DE_VehControlStatus Rte_FbAcuAvailable_VehControlStatus;
  Rte_DE_SAS_CalibrationSts Rte_SAS_Status_SAS_CalibrationSts;
  Rte_DE_SAS_FailureSts Rte_SAS_Status_SAS_FailureSts;
  Rte_DE_SAS_Status_AliveCounter Rte_SAS_Status_SAS_Status_AliveCounter;
  Rte_DE_SAS_Status_Checksum Rte_SAS_Status_SAS_Status_Checksum;
  Rte_DE_SAS_SteerWheelAngle Rte_SAS_Status_SAS_SteerWheelAngle;
  Rte_DE_SAS_SteerWheelRotSpd Rte_SAS_Status_SAS_SteerWheelRotSpd;
  Rte_DE_PrimVehSpdGroupSafeNomQf_C Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNomQf_C;
  Rte_DE_PrimVehSpdGroupSafeNom_C Rte_SSMMid3CanFr07_1V1R_PrimVehSpdGroupSafeNom_C;
  Rte_DE_PrimALgtDataRawSafeNomQf_C Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNomQf_C;
  Rte_DE_PrimALgtDataRawSafeNom_C Rte_SSMMid3CanFr11_1V1R_PrimALgtDataRawSafeNom_C;
  Rte_DE_PrimALatDataRawSafeNom_C Rte_SSMMid5CanFdFr03_1V1R_PrimALatDataRawSafeNom_C;
  Rte_DE_PrpsnTqDirAct_C Rte_VCU1Mid3CanFr03_1V1R_PrpsnTqDirAct_C;
  Rte_DE_CarTiGlb_A Rte_VCU1Mid3CanFr06_ACU_CarTiGlb_A;
  Rte_DE_YawRate1Qf1_C Rte_VCU1Mid3CanFr08_1V1R_YawRate1Qf1_C;
  Rte_DE_YawRate1_C Rte_VCU1Mid3CanFr08_1V1R_YawRate1_C;
  Rte_DE_WhlLockStsLockSts_C Rte_VCU1Mid3CanFr36_1V1R_WhlLockStsLockSts_C;
  Rte_DE_SG_AdSecWhlAgReqGroupSafe Rte_VIMBMid6CanFdFr14_SG_AdSecWhlAgReqGroupSafe;
  Rte_DE_SG_AdSecALgtLimReqGroupSafe Rte_VIMBMid6CanFdFr28_SG_AdSecALgtLimReqGroupSafe;
  Rte_DE_SG_SecAdNomALgtReqGroupSafe Rte_VIMBMid6CanFdFr28_SG_SecAdNomALgtReqGroupSafe;
  Rte_DE_SG_SecAdWhlLockReq Rte_VIMBMid6CanFdFr29_SG_SecAdWhlLockReq;
  Rte_DE_IDcDcAvlLoSideExt Rte_VIMMid3CanFr04_IDcDcAvlLoSideExt;
  Rte_DE_IDcDcAvlMaxLoSideExt Rte_VIMMid3CanFr04_IDcDcAvlMaxLoSideExt;
  Rte_DE_SG_HmiAutnmsSts Rte_VIMMid3CanFr04_SG_HmiAutnmsSts;
  Rte_DE_UDcDcAvlLoSideExt Rte_VIMMid3CanFr04_UDcDcAvlLoSideExt;
  Rte_DE_SG_AutnmsDrvStReq Rte_VIMMid3CanFr07_SG_AutnmsDrvStReq;
  Rte_DE_SG_VehOperStReq Rte_VIMMid3CanFr08_SG_VehOperStReq;
  Rte_DE_SG_AdDirReq Rte_VIMMid3CanFr09_SG_AdDirReq;
  Rte_DE_SG_AdStandStillReq Rte_VIMMid3CanFr09_SG_AdStandStillReq;
  Rte_DE_VehUsgStReq Rte_VIMMid3CanFr09_VehUsgStReq;
  Rte_DE_SG_AdFusedFricEstimn Rte_VIMMid3CanFr11_SG_AdFusedFricEstimn;
  Rte_DE_SG_AdpLiReqFromAPI Rte_VIMMid3CanFr11_SG_AdpLiReqFromAPI;
  Rte_DE_SG_SwtExtrLiFromAPI Rte_VIMMid3CanFr11_SG_SwtExtrLiFromAPI;
  Rte_DE_AdSetSpd Rte_VIMMid3CanFr13_AdSetSpd;
  Rte_DE_SG_AdFreeDst Rte_VIMMid3CanFr13_SG_AdFreeDst;
  Rte_DE_SG_AdWhlLockReq Rte_VIMMid3CanFr13_SG_AdWhlLockReq;
  Rte_DE_SG_AdNomALgtReqGroupSafe Rte_VIMMid3CanFr14_SG_AdNomALgtReqGroupSafe;
  Rte_DE_SG_AdPrimALgtLimReqGroupSafe Rte_VIMMid3CanFr15_SG_AdPrimALgtLimReqGroupSafe;
  Rte_DE_SG_AdPrimWhlAgReqGroupSafe Rte_VIMMid5CanFdFr12_SG_AdPrimWhlAgReqGroupSafe;
  Rte_DE_YRS1_AliveCounter Rte_YRS1_YRS1_AliveCounter;
  Rte_DE_YRS1_Checksum Rte_YRS1_YRS1_Checksum;
  Rte_DE_YRS_LateralAcce Rte_YRS1_YRS_LateralAcce;
  Rte_DE_YRS_LateralSensorState Rte_YRS1_YRS_LateralSensorState;
  Rte_DE_YRS_YawRate Rte_YRS1_YRS_YawRate;
  Rte_DE_YRS_YawRateSensorState Rte_YRS1_YRS_YawRateSensorState;
  Rte_DE_YRS2_Checksum Rte_YRS2_YRS2_Checksum;
  Rte_DE_YRS_AliveCounter Rte_YRS2_YRS_AliveCounter;
  Rte_DE_YRS_LongitAcce Rte_YRS2_YRS_LongitAcce;
  Rte_DE_YRS_LongitSensorState Rte_YRS2_YRS_LongitSensorState;
} Rte_tsRB_FallbackSigOutput_FallbackSigOutput_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

typedef struct
{
  Rte_DE_ControlErrorHeading Rte_FallbackControlDebugInfo_ControlErrorHeading;
  Rte_DE_ControlErrorLateral Rte_FallbackControlDebugInfo_ControlErrorLateral;
  Rte_DE_ControlErrorLongitudinal Rte_FallbackControlDebugInfo_ControlErrorLongitudinal;
  Rte_DE_ControlErrorVelocity Rte_FallbackControlDebugInfo_ControlErrorVelocity;
  Rte_DE_ControlReserve0 Rte_FallbackControlDebugInfo_ControlReserve0;
  Rte_DE_ControlReserve1 Rte_FallbackControlDebugInfo_ControlReserve1;
  Rte_DE_ControlReserve2 Rte_FallbackControlDebugInfo_ControlReserve2;
  Rte_DE_ControlReserve3 Rte_FallbackControlDebugInfo_ControlReserve3;
  Rte_DE_ControlReserve4 Rte_FallbackControlDebugInfo_ControlReserve4;
  Rte_DE_ControlReserve5 Rte_FallbackControlDebugInfo_ControlReserve5;
  Rte_DE_ControlReserve6 Rte_FallbackControlDebugInfo_ControlReserve6;
  Rte_DE_ControlReserve7 Rte_FallbackControlDebugInfo_ControlReserve7;
  Rte_DE_ControlReserve8 Rte_FallbackControlDebugInfo_ControlReserve8;
  Rte_DE_ControlReserve9 Rte_FallbackControlDebugInfo_ControlReserve9;
  Rte_DE_ControlTimeStamp Rte_FallbackControlDebugInfo_ControlTimeStamp;
  Rte_DE_LatPidDistanceDComponent Rte_FallbackControlDebugInfo_LatPidDistanceDComponent;
  Rte_DE_LatPidDistanceIComponent Rte_FallbackControlDebugInfo_LatPidDistanceIComponent;
  Rte_DE_LatPidDistancePComponent Rte_FallbackControlDebugInfo_LatPidDistancePComponent;
  Rte_DE_LatPidDistanceTotal Rte_FallbackControlDebugInfo_LatPidDistanceTotal;
  Rte_DE_LatPidHeadingDComponent Rte_FallbackControlDebugInfo_LatPidHeadingDComponent;
  Rte_DE_LatPidHeadingIComponent Rte_FallbackControlDebugInfo_LatPidHeadingIComponent;
  Rte_DE_LatPidHeadingPComponent Rte_FallbackControlDebugInfo_LatPidHeadingPComponent;
  Rte_DE_LatPidHeadingTotal Rte_FallbackControlDebugInfo_LatPidHeadingTotal;
  Rte_DE_LatSteeringFeeback Rte_FallbackControlDebugInfo_LatSteeringFeeback;
  Rte_DE_LatSteeringFeedforward Rte_FallbackControlDebugInfo_LatSteeringFeedforward;
  Rte_DE_LongAccelerationFeeback Rte_FallbackControlDebugInfo_LongAccelerationFeeback;
  Rte_DE_LongAccelerationFeedforward Rte_FallbackControlDebugInfo_LongAccelerationFeedforward;
  Rte_DE_LongPidDistanceDComponent Rte_FallbackControlDebugInfo_LongPidDistanceDComponent;
  Rte_DE_LongPidDistanceIComponent Rte_FallbackControlDebugInfo_LongPidDistanceIComponent;
  Rte_DE_LongPidDistancePComponent Rte_FallbackControlDebugInfo_LongPidDistancePComponent;
  Rte_DE_LongPidDistanceTotal Rte_FallbackControlDebugInfo_LongPidDistanceTotal;
  Rte_DE_LongPidSpeedDComponent Rte_FallbackControlDebugInfo_LongPidSpeedDComponent;
  Rte_DE_LongPidSpeedIComponent Rte_FallbackControlDebugInfo_LongPidSpeedIComponent;
  Rte_DE_LongPidSpeedPComponent Rte_FallbackControlDebugInfo_LongPidSpeedPComponent;
  Rte_DE_LongPidSpeedTotal Rte_FallbackControlDebugInfo_LongPidSpeedTotal;
  Rte_DE_PoseDebugCanbusState Rte_FallbackPoseDebug_PoseDebugCanbusState;
  Rte_DE_PoseDebugIsStationary Rte_FallbackPoseDebug_PoseDebugIsStationary;
  Rte_DE_PoseDebugPositionX Rte_FallbackPoseDebug_PoseDebugPositionX;
  Rte_DE_PoseDebugPositionXStd Rte_FallbackPoseDebug_PoseDebugPositionXStd;
  Rte_DE_PoseDebugPositionY Rte_FallbackPoseDebug_PoseDebugPositionY;
  Rte_DE_PoseDebugPositionYStd Rte_FallbackPoseDebug_PoseDebugPositionYStd;
  Rte_DE_PoseDebugReserved0 Rte_FallbackPoseDebug_PoseDebugReserved0;
  Rte_DE_PoseDebugReserved1 Rte_FallbackPoseDebug_PoseDebugReserved1;
  Rte_DE_PoseDebugReserved2 Rte_FallbackPoseDebug_PoseDebugReserved2;
  Rte_DE_PoseDebugReserved3 Rte_FallbackPoseDebug_PoseDebugReserved3;
  Rte_DE_PoseDebugReserved4 Rte_FallbackPoseDebug_PoseDebugReserved4;
  Rte_DE_PoseDebugReserved5 Rte_FallbackPoseDebug_PoseDebugReserved5;
  Rte_DE_PoseDebugReserved6 Rte_FallbackPoseDebug_PoseDebugReserved6;
  Rte_DE_PoseDebugReserved7 Rte_FallbackPoseDebug_PoseDebugReserved7;
  Rte_DE_PoseDebugReserved8 Rte_FallbackPoseDebug_PoseDebugReserved8;
  Rte_DE_PoseDebugSideSlipAngle Rte_FallbackPoseDebug_PoseDebugSideSlipAngle;
  Rte_DE_PoseDebugSpeed Rte_FallbackPoseDebug_PoseDebugSpeed;
  Rte_DE_PoseDebugSpeedStd Rte_FallbackPoseDebug_PoseDebugSpeedStd;
  Rte_DE_PoseDebugStateType Rte_FallbackPoseDebug_PoseDebugStateType;
  Rte_DE_PoseDebugTimeStamp Rte_FallbackPoseDebug_PoseDebugTimeStamp;
  Rte_DE_PoseDebugVehicleState Rte_FallbackPoseDebug_PoseDebugVehicleState;
  Rte_DE_PoseDebugYaw Rte_FallbackPoseDebug_PoseDebugYaw;
  Rte_DE_PoseDebugYawStd Rte_FallbackPoseDebug_PoseDebugYawStd;
} Rte_tsRB_Debug_Debug_Init; /* PRQA S 0779 */ /* MD_MSR_Rule5.2 */

/* PRQA S 0750 L1 */ /* MD_MSR_Union */
typedef union
{
  Rte_tsRB_VehSigInput_VehSigInput_Init Rte_VehSigInput_VehSigInput_Init;
  Rte_tsRB_CameraSigInput_CameraSigInput_Init Rte_CameraSigInput_CameraSigInput_Init;
  Rte_tsRB_AcuSigInput_AcuSigInput_Init Rte_AcuSigInput_AcuSigInput_Init;
  Rte_tsRB_LatCtrlFct_LatCtrlFct_Init Rte_LatCtrlFct_LatCtrlFct_Init;
  Rte_tsRB_LgtCtrlFct_LongitudinalControlFunction_Init Rte_LgtCtrlFct_LongitudinalControlFunction_Init;
  Rte_tsRB_Control_Control_Init Rte_Control_Control_Init;
  Rte_tsRB_FallbackSigOutput_FallbackSigOutput_Init Rte_FallbackSigOutput_FallbackSigOutput_Init;
  Rte_tsRB_Debug_Debug_Init Rte_Debug_Debug_Init;
} Rte_tuRB_OsTask_Init_Core0_APP;
/* PRQA L:L1 */

typedef struct
{
  Rte_tuRB_OsTask_Init_Core0_APP Rte_RB;
} Rte_tsOsTask_Init_Core0_APP;

# define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 *  LOCAL DATA TYPES AND STRUCTURES
 *********************************************************************************************************************/

typedef unsigned int Rte_BitType;
/**********************************************************************************************************************
 * type and extern declarations of RTE internal variables
 *********************************************************************************************************************/

/**********************************************************************************************************************
 * Rte Init State Variable
 *********************************************************************************************************************/

# define RTE_STATE_UNINIT    (0U)
# define RTE_STATE_SCHM_INIT (1U)
# define RTE_STATE_INIT      (2U)

# define RTE_START_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_StartTiming_InitState; /* PRQA S 0850, 3408 */ /* MD_MSR_MacroArgumentEmpty, MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# define RTE_START_SEC_VAR_SystemApplication_OsCore1_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_1; /* PRQA S 3408 */ /* MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore1_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# define RTE_START_SEC_VAR_SystemApplication_OsCore2_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_2; /* PRQA S 3408 */ /* MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore2_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# define RTE_START_SEC_VAR_SystemApplication_OsCore3_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_3; /* PRQA S 3408 */ /* MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore3_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# define RTE_START_SEC_VAR_SystemApplication_OsCore4_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_4; /* PRQA S 3408 */ /* MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore4_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# define RTE_START_SEC_VAR_SystemApplication_OsCore5_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern volatile VAR(uint8, RTE_VAR_ZERO_INIT_NOCACHE) Rte_InitState_5; /* PRQA S 3408 */ /* MD_Rte_3408 */

# define RTE_STOP_SEC_VAR_SystemApplication_OsCore5_NOCACHE_ZERO_INIT_8BIT
# include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef RTE_CORE

/**********************************************************************************************************************
 * Buffers for unqueued S/R
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(AcuControl, RTE_VAR_INIT) Rte_AcuSigInput_AcuControl_AcuControl; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(GnssPoseInput, RTE_VAR_INIT) Rte_AcuSigInput_GnssPose_GnssPose; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(LocalizationPoseInput, RTE_VAR_INIT) Rte_AcuSigInput_LocalizationPose_LocalizationPose; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(uint32, RTE_VAR_INIT) Rte_AcuSigInput_Timestamp_Timestamp; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(TrajectoryInput, RTE_VAR_INIT) Rte_AcuSigInput_Trajectory_Trajectory; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(CSI_LaneInfo_Struct, RTE_VAR_INIT) Rte_CameraSigInput_CSI_LaneInfo_CSI_LaneInfo; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(CSI_ObjectInfo_Struct, RTE_VAR_INIT) Rte_CameraSigInput_CSI_ObjectInfo_CSI_ObjectInfo; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(ControlOutput, RTE_VAR_INIT) Rte_Control_ControlCommand_ControlCommand; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(ControlDebug, RTE_VAR_INIT) Rte_Control_ControlDebug_ControlDebug; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(EstimationDebug, RTE_VAR_INIT) Rte_Control_EstimationDebug_EstimationDebug; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(BswM_ESH_RunRequest, RTE_VAR_INIT) Rte_CpBswAdapt_Request_ESH_RunRequest_0_requestedMode; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(boolean, RTE_VAR_INIT) Rte_CpSysCtrl_RunCmd_RunCmd; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(LAT_CtrlCmd_Struct, RTE_VAR_INIT) Rte_LatCtrlFct_LAT_CtrlCmd_LAT_CtrlCmd; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(LGT_CtrlCmd_Struct, RTE_VAR_INIT) Rte_LgtCtrlFct_LGT_CtrlCmd_LGT_CtrlCmd; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(VSI_McuCanTimeout_Struct, RTE_VAR_INIT) Rte_VehSigInput_VSI_McuCanTimeout_VSI_McuCanTimeout; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(VSI_VehInfoFor1V1R_Struct, RTE_VAR_INIT) Rte_VehSigInput_VSI_VehInfoFor1V1R_VSI_VehInfoFor1V1R; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(VSI_VehicleInfo_Struct, RTE_VAR_INIT) Rte_VehSigInput_VSI_VehicleInfo_VSI_VehicleInfo; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

typedef struct
{
  Rte_BitType Rte_ModeSwitchAck_Dcm_DcmEcuReset_DcmEcuReset_Ack : 1;
} Rte_SystemApplication_OsCore0_AckFlagsType;

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_SystemApplication_OsCore0_AckFlagsType, RTE_VAR_INIT) Rte_SystemApplication_OsCore0_AckFlags;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Buffer for inter-runnable variables
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_MainTask_Core0_60ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(BDP_FilteredLane_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_BDP_FilteredLane; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_MainTask_Core0_60ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(FSA_SystemState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_FSA_SystemState; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_MainTask_Core0_60ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(LSI_LatSigInput_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_LSI_LatSigInput; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(VDP_VehicleState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_VDP_VehicleState_MainTask_Core0_60ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(VDP_VehicleState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_VDP_VehicleState_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(VDP_VehicleState_Struct, RTE_VAR_INIT) Rte_Irv_LatCtrlFct_VDP_VehicleState; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(CDS_CollisionDetection_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(CDS_CollisionDetection_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_CDS_CollisionDetection_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(CDS_CollisionDetection_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_CDS_CollisionDetection; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(FSC_FreeStopControl_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(FSC_FreeStopControl_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_FSC_FreeStopControl_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(FSC_FreeStopControl_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_FSC_FreeStopControl; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(OPS_ObjectList_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_OPS_ObjectList_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(OPS_ObjectList_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_OPS_ObjectList_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(OPS_ObjectList_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_OPS_ObjectList; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */
extern VAR(VSP_VehicleSignal_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_MainTask_Core0_10ms; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(VSP_VehicleSignal_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_VSP_VehicleSignal_OsTask_Init_Core0_APP; /* PRQA S 3408 */ /* MD_Rte_3408 */
extern VAR(VSP_VehicleSignal_Struct, RTE_VAR_INIT) Rte_Irv_LgtCtrlFct_VSP_VehicleSignal; /* PRQA S 3408, 1504 */ /* MD_Rte_3408, MD_MSR_Rule8.7 */

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


/**********************************************************************************************************************
 * RTE internal IOC replacement
 *********************************************************************************************************************/

#  define RTE_START_SEC_VAR_SystemApplication_OsCore1_NOCACHE_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(uint8, RTE_VAR_NOINIT_NOCACHE) Rte_ioc_Rte_CS_ServerQueue_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime_Queue[1];

#  define RTE_STOP_SEC_VAR_SystemApplication_OsCore1_NOCACHE_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_SystemApplication_OsCore1_NOCACHE_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(uint32, RTE_VAR_INIT_NOCACHE) Rte_ioc_Rte_CS_ServerQueue_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime_tail;

#  define RTE_STOP_SEC_VAR_SystemApplication_OsCore1_NOCACHE_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(uint32, RTE_VAR_ZERO_INIT_NOCACHE) Rte_ioc_Rte_CS_ServerQueue_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime_head;

#  define RTE_STOP_SEC_VAR_SystemApplication_OsCore0_NOCACHE_ZERO_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */



/**********************************************************************************************************************
 * Internal C/S connections
 *********************************************************************************************************************/

/* Queue element type definitions for internal C/S connections */

typedef struct
{
  boolean Rte_CallCompleted;
  StbM_TimeStampType timeStampPtr;
  StbM_UserDataType userDataPtr;
  Std_ReturnType Rte_Result;
} Rte_CS_ClientQueueType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime;

typedef struct
{
  uint8 Rte_WaitingTaskCount;
  P2CONST(TaskType, TYPEDEF, RTE_APPL_DATA) Rte_WaitingTaskList;
  P2VAR(Rte_CS_ClientQueueType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime, TYPEDEF, RTE_VAR_NOINIT) Rte_ClientQueue;
} Rte_CS_ClientConfigType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime;

typedef struct
{
  uint8 Rte_ClientId;
} Rte_CS_ServerQueueType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime;

#  define RTE_START_SEC_VAR_NOCACHE_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_CS_ClientQueueType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime, RTE_VAR_NOINIT_NOCACHE) Rte_CS_ClientQueue_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime;

#  define RTE_STOP_SEC_VAR_NOCACHE_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_CONST_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern CONST(TaskType, RTE_CONST) Rte_CS_WaitingTaskList_CpVehCtrl_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime[1];
extern CONST(Rte_CS_ClientConfigType_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime, RTE_CONST) Rte_CS_ClientConfig_StbM_GlobalTime_Slave_StbMSynchronizedTimeBase_EthTsyn_GetCurrentTime[1];

#  define RTE_STOP_SEC_CONST_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */



/**********************************************************************************************************************
 * Data structures for mode management
 *********************************************************************************************************************/


#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(BswM_ESH_Mode, RTE_VAR_INIT) Rte_ModeMachine_BswM_Switch_ESH_ModeSwitch_BswM_MDGP_ESH_Mode;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Dcm_CommunicationModeType, RTE_VAR_INIT) Rte_ModeMachine_Dcm_DcmCommunicationControl_ComMConf_ComMChannel_ComMChannel_DiagCAN1_ACU_DcmCommunicationControl_ComMConf_ComMChannel_ComMChannel_DiagCAN1_ACU;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Dcm_ControlDtcSettingType, RTE_VAR_INIT) Rte_ModeMachine_Dcm_DcmControlDtcSetting_DcmControlDtcSetting;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Dcm_DiagnosticSessionControlType, RTE_VAR_INIT) Rte_ModeMachine_Dcm_DcmDiagnosticSessionControl_DcmDiagnosticSessionControl;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


#  define RTE_START_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Dcm_EcuResetType, RTE_VAR_INIT) Rte_ModeMachine_Dcm_DcmEcuReset_DcmEcuReset;

#  define RTE_STOP_SEC_VAR_INIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif /* defined(RTE_CORE) */

#endif /* RTE_TYPE_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_1039:  MISRA rule: Rule1.2
     Reason:     Same macro and function names are required to meet AUTOSAR spec.
     Risk:       No functional risk. Macro will be undefined before function definition.
     Prevention: Not required.

   MD_Rte_3408:  MISRA rule: Rule8.4
     Reason:     For the purpose of monitoring during calibration or debugging it is necessary to use non-static declarations.
                 This is covered in the MISRA C compliance section of the Rte specification.
     Risk:       No functional risk.
     Prevention: Not required.

*/
