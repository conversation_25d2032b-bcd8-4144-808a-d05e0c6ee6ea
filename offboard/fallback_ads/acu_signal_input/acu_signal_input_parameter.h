#ifndef OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_ACU_SIGNAL_INPUT_PARAMETER_H_
#define OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_ACU_SIGNAL_INPUT_PARAMETER_H_

#ifndef POSE_INPUT_TASK_CYCLE_TIME
#define POSE_INPUT_TASK_CYCLE_TIME (0.01F)
#endif

namespace fallback_adas {
namespace acu_signal_input {

extern bool GetOnlyCheckCounterChange();
extern float GetPoseUpdateOKTimeThreshold();

}  // namespace acu_signal_input
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_ACU_SIGNAL_INPUT_PARAMETER_H_
