#include "planner/speed/reasoning/test/reasoning_test_fixture.h"

#include <algorithm>
#include <limits>
#include <memory>
#include <sstream>
#include <string>
#include <utility>

#include <boost/filesystem.hpp>
#include <map>
#include <rosbag/bag.h>
#include <vector>

#include "av_comm/topics.h"
#include "geometry/model/frenet_axis_aligned_box.h"
#include "geometry/model/point_2d.h"
#include "geometry/model/polygon_with_cache.h"
#include "geometry/model/polyline_curve.h"
#include "math/angle.h"
#include "math/math_util.h"
#include "math/pose_2d.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_external_utils.h"
#include "planner/decoupled_maneuvers/cross_lane/cross_lane_info_manager.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_common.h"
#include "planner/decoupled_maneuvers/lane_change/lane_change_env_analyzer.h"
#include "planner/decoupled_maneuvers/pull_over/destination_initial_decision.h"
#include "planner/decoupled_maneuvers/pull_over/destination_meta_data.h"
#include "planner/decoupled_maneuvers/pull_over/immediate_pull_over_trigger_info.h"
#include "planner/decoupled_maneuvers/pull_over/jump_in_guidance.h"
#include "planner/decoupled_maneuvers/pull_over/pull_over_info.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/requried_lateral_gap.h"
#include "planner/speed/constraint/constraint_creator.h"
#include "planner/speed/overlap/object_proximity_computer.h"
#include "planner/speed/overlap/overlap_computer.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/overlap_computation_policy.h"
#include "planner/speed/reference/reference_generator.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner/world_model/test/world_model_test_utility.h"
#include "planner/world_model/traffic_participant/tracked_object.h"
#include "planner_protos/lane_blockage.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "prediction_protos/agent.pb.h"
#include "prediction_protos/yield_intention.pb.h"
#include "ros_util/shape_shifter_util.h"
#include "voy_protos/construction_zones.pb.h"
#include "voy_protos/point.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_protos/traffic_light.pb.h"

namespace planner {
namespace speed {

// TODO(speed): move this to a shared util file.
namespace {

// Gets the sampled distances by math::kDefaultDeltaS for each curve.
void GetSampledDistancesForLaneSequence(
    const std::vector<const math::geometry::PolylineCurve2d*>& curve_vec,
    const std::vector<double>& accumulated_curve_length_vec,
    std::vector<std::vector<double>>* sampled_distances) {
  (void)curve_vec;
  int curve_index = 0;
  double current_sample_distance = 0;
  const double accumulated_length = accumulated_curve_length_vec.back();
  while (current_sample_distance <
         accumulated_length - math::constants::kEpsilon) {
    // Get the correct curve index by distance.
    if (current_sample_distance >
        accumulated_curve_length_vec.at(curve_index)) {
      curve_index++;
    }
    (*sampled_distances)
        .at(curve_index)
        .push_back(curve_index == 0
                       ? current_sample_distance
                       : current_sample_distance -
                             accumulated_curve_length_vec.at(curve_index - 1));
    current_sample_distance += math::kDefaultArcLengthSamplingDist;
  }
  (*sampled_distances)
      .at(curve_index)
      .push_back(curve_index == 0
                     ? accumulated_length
                     : accumulated_length -
                           accumulated_curve_length_vec.at(curve_index - 1));
}

// Samples the points on a vector of parameter curve.
math::geometry::Polyline2d GetSampledCurveForLaneSequence(
    const std::vector<const math::geometry::PolylineCurve2d*>& curve_vec) {
  // Generate the accumulated length for each curve.
  std::vector<double> accumulated_curve_length_vec;
  accumulated_curve_length_vec.reserve(curve_vec.size());
  // sampled_distances saves the sampled distance for each curve.
  std::vector<std::vector<double>> sampled_distances;
  sampled_distances.reserve(curve_vec.size());
  double accumulated_length = 0.0;
  for (const auto& curve_ptr : curve_vec) {
    accumulated_length += curve_ptr->GetTotalArcLength();
    accumulated_curve_length_vec.push_back(accumulated_length);
    // Init the sampling distance vector for each curve.
    std::vector<double> initial_vec;
    sampled_distances.emplace_back(std::move(initial_vec));
    const int curve_sample_num =
        static_cast<int>(std::floor(curve_ptr->GetTotalArcLength() /
                                    math::kDefaultArcLengthSamplingDist)) +
        1;
    sampled_distances.back().reserve(curve_sample_num);
  }
  GetSampledDistancesForLaneSequence(curve_vec, accumulated_curve_length_vec,
                                     &sampled_distances);

  math::geometry::Polyline2d points;
  const int total_curve_sample_num =
      static_cast<int>(std::floor(accumulated_length /
                                  math::kDefaultArcLengthSamplingDist)) +
      1;
  points.reserve(total_curve_sample_num);

  // Samples the points according sampled distances.
  for (unsigned i = 0; i < curve_vec.size(); i++) {
    for (unsigned idx = 0; idx < sampled_distances[i].size(); idx++) {
      points.emplace_back(curve_vec[i]->GetInterp(sampled_distances[i][idx]));
    }
  }
  return points;
}

std::vector<math::geometry::Point2d> GetSampledCurveBetweenTwoNeighbouringLanes(
    const pnc_map::Lane& source_lane, const pnc_map::Lane& target_lane,
    const double source_portion, const double target_portion) {
  DCHECK_LE(0.0, source_portion);
  DCHECK_GE(1.0, source_portion);

  DCHECK_LE(0.0, target_portion);
  DCHECK_GE(1.0, target_portion);

  const math::geometry::PolylineCurve2d& source_center_line =
      source_lane.center_line();
  const double start_arc_length =
      source_center_line.GetTotalArcLength() * source_portion;
  math::geometry::Point2d start_point =
      source_center_line.GetInterp(start_arc_length);

  const math::geometry::PolylineCurve2d& end_center_line =
      target_lane.center_line();
  const double end_arc_length =
      end_center_line.GetTotalArcLength() * target_portion;
  math::geometry::Point2d end_point = end_center_line.GetInterp(end_arc_length);

  const math::geometry::PolylineCurve2d curve_skeleton{start_point, end_point};
  std::vector<math::geometry::Point2d> bridge_segment_points =
      curve_skeleton.GetSampledPoints(0.0, curve_skeleton.GetTotalArcLength(),
                                      constants::kPathIntervalInMeter,
                                      math::pb::UseExtensionFlag::kForbid);
  return bridge_segment_points;
}

// Samples the points over the source lane before portion point.
std::vector<math::geometry::Point2d> GetSampledCurveFromSourceLane(
    const pnc_map::Lane& lane, const double portion) {
  DCHECK_LE(0.0, portion);
  DCHECK_GE(1.0, portion);

  const math::geometry::PolylineCurve2d& source_lane_center_line =
      lane.center_line();
  DCHECK(!source_lane_center_line.empty());
  const double arc_length =
      source_lane_center_line.GetTotalArcLength() * portion;

  DCHECK_GE(source_lane_center_line.points().size(), 2.0);
  std::vector<math::geometry::Point2d> source_lane_segment_points =
      source_lane_center_line.GetSampledPoints(
          0.0, arc_length, constants::kPathIntervalInMeter,
          math::pb::UseExtensionFlag::kForbid);
  return source_lane_segment_points;
}

// Samples the points over the target lane after portion point.
std::vector<math::geometry::Point2d> GetSampledCurveFromTargetLane(
    const pnc_map::Lane& lane, const double portion) {
  DCHECK_LE(0.0, portion);
  DCHECK_GE(1.0, portion);

  const math::geometry::PolylineCurve2d& target_lane_center_line =
      lane.center_line();
  DCHECK(!target_lane_center_line.empty());
  const double arc_length =
      target_lane_center_line.GetTotalArcLength() * portion;

  DCHECK_GE(target_lane_center_line.points().size(), 2.0);
  std::vector<math::geometry::Point2d> target_lane_segment_points =
      target_lane_center_line.GetSampledPoints(
          arc_length, target_lane_center_line.GetTotalArcLength(),
          constants::kPathIntervalInMeter, math::pb::UseExtensionFlag::kForbid);
  return target_lane_segment_points;
}

void AddTrajectoryPoseToPredictedTrajectory(
    const double x, const double y, const double heading, const double speed,
    const double odom, const double timestamp,
    prediction::pb::PredictedTrajectory& predicted_trajectory) {
  planner::pb::TrajectoryPose pose;
  pose.set_timestamp(static_cast<int64_t>(timestamp));
  pose.set_x_pos(x);
  pose.set_y_pos(y);
  pose.set_z_pos(0.0);
  pose.set_heading(heading);
  pose.set_speed(speed);
  pose.set_odom(odom);
  *(predicted_trajectory.add_traj_poses()) = std::move(pose);
}

void WriteSpecificTopic(
    const std::string& topic_name,
    const boost::shared_ptr<const topic_tools::ShapeShifter>& msg_shape_shifter,
    rosbag::Bag* bag_ptr) {
  DCHECK(bag_ptr != nullptr);
  const uint32_t data_size = msg_shape_shifter->size();
  std::vector<uint8_t> buffer(data_size);
  ros::serialization::OStream o_stream(buffer.data(), buffer.size());
  msg_shape_shifter->write(o_stream);
  ros::serialization::IStream i_stream(buffer.data(), buffer.size());

  // Read timestamp from output stream.
  uint64_t timestamp = 0;
  i_stream.next(timestamp);

  // Put the remaining part back to the stream.
  const std::string md5_sum = msg_shape_shifter->getMD5Sum();
  const std::string data_type = msg_shape_shifter->getDataType();
  const std::string message_definition =
      msg_shape_shifter->getMessageDefinition();
  topic_tools::ShapeShifter new_msg;
  new_msg.morph(md5_sum, data_type, message_definition, "1" /* latching */);
  new_msg.read(i_stream);

  ros::Time ros_time(1.0);
  bag_ptr->write(topic_name, ros_time, new_msg);
}

// Constructs a rectangle contour for the tracked object and populates points
// in counter-clockwise direction from the left-top corner point.
void PopulateObjectContour(const math::Pose2d& pose, double length,
                           double width, voy::TrackedObject& tracked_object) {
  const double cos_op = math::FastCos(pose.yaw().value());
  const double sin_op = math::FastSin(pose.yaw().value());
  const double half_length = length * 0.5;
  const double half_width = width * 0.5;
  const auto set_corner_point = [&pose, &cos_op, &sin_op](const double x,
                                                          const double y,
                                                          voy::Point2d* point) {
    point->set_x(cos_op * x - sin_op * y + pose.x());
    point->set_y(sin_op * x + cos_op * y + pose.y());
  };
  set_corner_point(half_length, half_width, tracked_object.add_contour());
  set_corner_point(-half_length, half_width, tracked_object.add_contour());
  set_corner_point(-half_length, -half_width, tracked_object.add_contour());
  set_corner_point(half_length, -half_width, tracked_object.add_contour());
}

// Densify a given polyline curve with the specified number of points.
math::geometry::PolylineCurve2d GetDensifiedPolylineCurve(
    const math::geometry::PolylineCurve2d& polyline_curve,
    size_t densified_sampling_number) {
  DCHECK_GT(densified_sampling_number, polyline_curve.size());
  std::vector<math::geometry::Point2d> upsampled_points;
  upsampled_points.reserve(densified_sampling_number);
  const double total_arclength = polyline_curve.GetTotalArcLength();
  const double arclength_sampling_resolution =
      total_arclength / (densified_sampling_number - 1);
  for (size_t i = 0UL; i < densified_sampling_number; ++i) {
    upsampled_points.push_back(
        polyline_curve.GetInterp(arclength_sampling_resolution * i,
                                 math::pb::UseExtensionFlag::kForbid));
  }
  math::geometry::PolylineCurve2d res(upsampled_points);
  return res;
}

// Split a polyline curve in two ways and populate them into corresponding
// containers.
void SplitPolylineCurveInTwoWaysByPortion(
    const math::geometry::PolylineCurve2d& polyline_curve,
    double spliting_portion,
    std::vector<math::geometry::PolylineCurve2d>& first_portion_curves,
    std::vector<math::geometry::PolylineCurve2d>& second_portion_curves) {
  std::vector<math::geometry::Point2d> points_before_spliting_point;
  std::vector<math::geometry::Point2d> points_after_spliting_point;
  math::geometry::PolylineCurve2d densified_polyline_curve;
  if (polyline_curve.size() < kMinimumDensifiedPolylineCurvePointNumber) {
    densified_polyline_curve = GetDensifiedPolylineCurve(
        polyline_curve, kMinimumDensifiedPolylineCurvePointNumber);
  }
  const math::geometry::PolylineCurve2d& curve =
      densified_polyline_curve.empty() ? polyline_curve
                                       : densified_polyline_curve;
  const double total_arclength = curve.GetTotalArcLength();
  for (size_t i = 0UL; i < curve.size(); ++i) {
    if (curve.GetArcLengthAtPoint(i) / total_arclength < spliting_portion) {
      points_before_spliting_point.push_back(curve.points()[i]);
    } else {
      points_after_spliting_point.push_back(curve.points()[i]);
    }
  }

  if (points_before_spliting_point.size() >= 2UL) {
    first_portion_curves.push_back(
        math::geometry::PolylineCurve2d(points_before_spliting_point));
  }
  if (points_after_spliting_point.size() >= 2UL) {
    second_portion_curves.push_back(
        math::geometry::PolylineCurve2d(points_after_spliting_point));
  }
}

// Split a polyline curve in three ways and populate them into corresponding
// containers.
// TODO(minhanli): This function is kinda a duplicate to the
// |SplitPolylineCurveInTwoWaysByPortion|, consider develop a general function
// to cover them both.
void SplitPolylineCurveInThreeWaysByPortion(
    const math::geometry::PolylineCurve2d& polyline_curve,
    double first_spliting_portion, double second_spliting_portion,
    std::vector<math::geometry::PolylineCurve2d>& first_portion_curves,
    std::vector<math::geometry::PolylineCurve2d>& second_portion_curves,
    std::vector<math::geometry::PolylineCurve2d>& third_portion_curves) {
  std::vector<math::geometry::Point2d> points_before_first_spliting_point;
  std::vector<math::geometry::Point2d> points_after_second_spliting_point;
  std::vector<math::geometry::Point2d> points_between_two_spliting_point;
  math::geometry::PolylineCurve2d densified_polyline_curve;
  if (polyline_curve.size() < kMinimumDensifiedPolylineCurvePointNumber) {
    densified_polyline_curve = GetDensifiedPolylineCurve(
        polyline_curve, kMinimumDensifiedPolylineCurvePointNumber);
  }
  const math::geometry::PolylineCurve2d& curve =
      densified_polyline_curve.empty() ? polyline_curve
                                       : densified_polyline_curve;
  const double total_arclength = curve.GetTotalArcLength();
  for (size_t i = 0UL; i < curve.size(); ++i) {
    if (curve.GetArcLengthAtPoint(i) / total_arclength <
        first_spliting_portion) {
      points_before_first_spliting_point.push_back(curve.points()[i]);
    } else if (curve.GetArcLengthAtPoint(i) / total_arclength >
               second_spliting_portion) {
      points_after_second_spliting_point.push_back(curve.points()[i]);
    } else {
      points_between_two_spliting_point.push_back(curve.points()[i]);
    }
  }
  if (points_before_first_spliting_point.size() >= 2UL) {
    first_portion_curves.push_back(
        math::geometry::PolylineCurve2d(points_before_first_spliting_point));
  }
  if (points_between_two_spliting_point.size() >= 2UL) {
    second_portion_curves.push_back(
        math::geometry::PolylineCurve2d(points_between_two_spliting_point));
  }
  if (points_after_second_spliting_point.size() >= 2UL) {
    third_portion_curves.push_back(
        math::geometry::PolylineCurve2d(points_after_second_spliting_point));
  }
}

}  // namespace

ReasoningTestFixture::ReasoningTestFixture()
    : previous_iter_seed_(
          *Seed::Access<token::DecoupledManeuverSeed>::MutableMsg(
              SpeedCurrentFrame())) {
  previous_iter_seed_.Clear();
  path_reasoning_seed_.Clear();
  mutable_current_iter_seed_.Clear();
}

void ReasoningTestFixture::SetUpRegionMap(
    double init_map_x, double init_map_y, const std::string& route_name,
    hdmap::test_util::TestData test_map_area) {
  map_test_util_.InitPncMapService(init_map_x, init_map_y, /*z=*/0.0,
                                   route_name, test_map_area);
  ego_pose_.set_x(init_map_x);
  ego_pose_.set_y(init_map_y);
  ego_pose_.set_timestamp(timestamp());

  UpdateWorldModel();

  agent_list_.set_timestamp(timestamp());
}

void ReasoningTestFixture::SetUpRegionMap(
    hdmap::test_util::TestData test_map_area) {
  double init_map_x = 0.0;
  double init_map_y = 0.0;
  std::string route_name;
  switch (test_map_area) {
    case hdmap::test_util::kFremontData: {
      init_map_x = -4498.062135;
      init_map_y = -2587.717336;
      route_name = "fremont_route_1";
      break;
    }
    case hdmap::test_util::kSceneData: {
      LOG(ERROR) << "Wrong API usage, SetUpSceneMap should be used";
      return;
    }
    default: {
      LOG(ERROR) << "Type not supported yet";
      return;
    }
  }

  SetUpRegionMap(init_map_x, init_map_y, route_name, test_map_area);
}

void ReasoningTestFixture::SetUpSceneMap(
    hdmap::test_util::SceneType scene_type) {
  double init_map_x = 0.0;
  double init_map_y = 0.0;
  double init_yaw = 0.0;
  std::string route_name_for_scene;

  switch (scene_type) {
    case hdmap::test_util::SceneType::kForklane: {
      init_map_x = 28559.33119886974;
      init_map_y = 39411.657032079995;
      init_yaw = -0.631;
      route_name_for_scene = "SCENARIO_FORKLANE";
      break;
    }
    case hdmap::test_util::SceneType::kJunction: {
      init_map_x = -8462.57285173732;
      init_map_y = -33333.03701276006;
      init_yaw = -2.86859;
      route_name_for_scene = "SCENARIO_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kTjunction: {
      init_map_x = -11689.2488147174;
      init_map_y = -33687.39985542279;
      init_yaw = 0.0336329;
      route_name_for_scene = "SCENARIO_T_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kNoSignalJunction: {
      init_map_x = 7981.558705776406;
      init_map_y = 16027.32386226952;
      init_yaw = 0.554617;
      route_name_for_scene = "SCENARIO_NO_SIGNAL_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kRoundabout: {
      init_map_x = 48500.07500000985;
      init_map_y = 14156.474268863909;
      init_yaw = 0.0119558;
      route_name_for_scene = "SCENARIO_ROUNDABOUT";
      break;
    }
    case hdmap::test_util::SceneType::kMerge: {
      init_map_x = 28821.68601358193;
      init_map_y = 39039.90699229622;
      init_yaw = 1.86917;
      route_name_for_scene = "SCENARIO_MERGE";
      break;
    }
    case hdmap::test_util::SceneType::kRightStraightMerge: {
      init_map_x = -8464.342784169305;
      init_map_y = -33337.181650610175;
      init_yaw = -2.8683;
      route_name_for_scene = "SCENARIO_RIGHT_STRAIGHT_MERGE";
      break;
    }
    case hdmap::test_util::SceneType::kCrosswalk: {
      init_map_x = -8826.486823851767;
      init_map_y = -33890.86066968739;
      init_yaw = 1.7505;
      route_name_for_scene = "SCENARIO_CROSSWALK";
      break;
    }
    case hdmap::test_util::SceneType::kUturn: {
      init_map_x = 50454.034010021016;
      init_map_y = 14738.047165964264;
      init_yaw = -1.0385;
      route_name_for_scene = "SCENARIO_U_TURN";
      break;
    }
    case hdmap::test_util::SceneType::kBusBulb: {
      init_map_x = 27050.948989064666;
      init_map_y = 39985.45480643492;
      init_yaw = 3.131;
      route_name_for_scene = "SCENARIO_BUS_BULB";
      break;
    }
    case hdmap::test_util::SceneType::kStraightMergeInJunction: {
      init_map_x = 50327.03408838948;
      init_map_y = 14256.703561567701;
      init_yaw = -2.70035;
      route_name_for_scene = "SCENARIO_STRAIGHT_MERGE_IN_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kForkMergeByBikelane: {
      init_map_x = 8368.667021557107;
      init_map_y = 9212.665932114236;
      init_yaw = 1.7375;
      route_name_for_scene = "SCENARIO_FORK_MERGE_BY_BIKELANE";
      break;
    }
    case hdmap::test_util::SceneType::kKeepClearZone: {
      init_map_x = 47169.49307420489;
      init_map_y = 13377.433867456857;
      init_yaw = 0.250255;
      route_name_for_scene = "SCENARIO_KEEP_CLEAR_ZONE";
      break;
    }
    case hdmap::test_util::SceneType::kSideToMainRoad: {
      init_map_x = 58548.75068745075;
      init_map_y = 11476.849312325008;
      init_yaw = -0.807329;
      route_name_for_scene = "SCENARIO_SIDE_TO_MAIN_ROAD";
      break;
    }
    case hdmap::test_util::SceneType::kMergeByBuslane: {
      init_map_x = 55682.81699117937;
      init_map_y = 15043.224825329147;
      init_yaw = -0.154059;
      route_name_for_scene = "SCENARIO_MERGE_BY_BUSLANE";
      break;
    }
    case hdmap::test_util::SceneType::kNoWatchLineJunction: {
      init_map_x = 24749.59198432014;
      init_map_y = -35310.95316855563;
      init_yaw = -1.5454;
      route_name_for_scene = "SCENARIO_NO_WATCHLINE_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kSpecialLightJunction: {
      init_map_x = 50245.85036460869;
      init_map_y = 15621.153697860893;
      init_yaw = -2.36343;
      route_name_for_scene = "SCENARIO_SPECIAL_LIGHT_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kPickUpDropOffZone: {
      init_map_x = 49075.02245340403;
      init_map_y = 13538.5753886858;
      init_yaw = 0.00008;
      route_name_for_scene = "SCENARIO_PICK_UP_DROP_OFF_ZONE";
      break;
    }
    case hdmap::test_util::SceneType::kZipMergeInNoSignalJunction: {
      init_map_x = 7886.463503761508;
      init_map_y = 14756.671045946889;
      init_yaw = 2.12529;
      route_name_for_scene = "SCENARIO_ZIP_MERGE_IN_NO_SIGNAL_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kMergeAfterExclusiveRightTurn: {
      init_map_x = 52708.304028910992;
      init_map_y = 15032.26132318005;
      init_yaw = 2.00482;
      route_name_for_scene = "SCENARIO_MERGE_AFTER_EXCLUSIVE_RIGHT_TURN";
      break;
    }
    case hdmap::test_util::SceneType::kTrafficSignalInNonJunction: {
      init_map_x = 59934.44490580447;
      init_map_y = 31356.098810850643;
      init_yaw = 2.04802;
      route_name_for_scene = "SCENARIO_TRAFFIC_SIGNAL_IN_NON_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kMultiLeftTrafficSignalInJunction: {
      init_map_x = 8436.783791843452;
      init_map_y = 10904.775698883459;
      init_yaw = -0.356158;
      route_name_for_scene = "SCENARIO_MULTI_LEFT_TRAFFIC_SIGNAL_IN_JUNCTION";
      break;
    }
    case hdmap::test_util::SceneType::kHighSpeedTollStation: {
      init_map_x = 1759.1868419720558;
      init_map_y = 7535.087543452159;
      init_yaw = -1.222025;
      route_name_for_scene = "SCENARIO_HIGH_SPEED_TOLL_STATION";
      break;
    }
    default: {
      LOG(ERROR) << "SceneType not handled in ReasoningTestFixture.";
      return;
    }
  }

  map_test_util_.InitPncMapService(init_map_x, init_map_y, /*z=*/0.0,
                                   scene_type, route_name_for_scene);

  ego_pose_.set_x(init_map_x);
  ego_pose_.set_y(init_map_y);
  ego_pose_.set_yaw(init_yaw);
  ego_pose_.set_timestamp(timestamp());

  UpdateWorldModel();

  agent_list_.set_timestamp(timestamp());
}

void ReasoningTestFixture::UpdateWorldModel(
    const std::optional<mrc::pb::MrcRequest>& mrc_request,
    const std::optional<const std::vector<planner::pb::AssistResponse>>&
        assist_responses) {
  std::vector<prediction::pb::Agent> agents;
  agents.reserve(agent_list_.agent_list().size());
  for (const auto& agent : agent_list_.agent_list()) {
    agents.push_back(agent);
  }
  world_model_.emplace(CreateDummyWorldModel(
      PlannerConfigCenter::GetInstance().planner_config().world_model_config(),
      ego_pose_, canbus_, agents, map_test_util_.pnc_map_service(),
      &traffic_lights_, map_test_util_.route_solution(),
      /*trajectory_guider_output=*/nullptr, construction_zones_,
      /*constraint_name=*/"", mrc_request, assist_responses));
  // Update world_context_ accordingly.
  world_context_.emplace(world_model(), motion_mode());
}

void ReasoningTestFixture::ExtractToSpeedWorldModel() {
  DCHECK(world_model_.has_value());
  speed_world_model_ = world_model_->ExtractToSpeedWorldModel();
}

void ReasoningTestFixture::UpdatePreviousSeed() {
  *(mutable_previous_iter_seed().mutable_speed_seed()) =
      std::move(*mutable_current_iter_seed());
  mutable_current_iter_seed()->Clear();
}

const PredictionDecision& ReasoningTestFixture::prediction_decision(
    const ObjectTrajectoryId& id) const {
  const int64_t object_id = id.object_id;
  const int trajectory_id = id.trajectory_id;
  const std::vector<PredictionDecisionMakerOutput>& decision_outputs =
      wrapped_prediction_decisions();
  const auto output_iter =
      std::find_if(decision_outputs.begin(), decision_outputs.end(),
                   [object_id](const PredictionDecisionMakerOutput& output) {
                     return output.reasoning_object().id() == object_id;
                   });
  DCHECK(output_iter != decision_outputs.end())
      << "Object ID " << object_id
      << "not found in wrapped_prediction_decisions.";
  const std::vector<PredictionDecision>& prediction_decisions =
      output_iter->prediction_decisions();
  const auto decision_iter =
      std::find_if(prediction_decisions.begin(), prediction_decisions.end(),
                   [trajectory_id](const PredictionDecision& decision) {
                     return decision.trajectory_id() == trajectory_id;
                   });
  DCHECK(decision_iter != prediction_decisions.end())
      << "Trajectory ID " << trajectory_id
      << "not found in prediction decisions.";
  return *decision_iter;
}

math::Pose2d ReasoningTestFixture::LanePointToPoseWithShift(
    const int64_t lane_id, const double portion, const double arclength_shift,
    const double lateral_shift, const double theta_shift) const {
  DCHECK_LE(0.0, portion);
  DCHECK_GE(1.0, portion);

  const pnc_map::Lane* lane = GetLaneFromId(lane_id);
  const math::geometry::PolylineCurve2d& center_line = lane->center_line();
  const double arc_length =
      center_line.GetTotalArcLength() * portion + arclength_shift;

  const auto [point, deriv] = center_line.GetInterpPointAndDeriv(arc_length);
  const double theta = std::atan2(deriv.y(), deriv.x());

  return math::Pose2d(point.x() - lateral_shift * deriv.y(),
                      point.y() + lateral_shift * deriv.x(),
                      math::NormalizeMinusPiToPi(theta + theta_shift));
}

math::Pose2d ReasoningTestFixture::LanePointToPose(
    const int64_t lane_id, const double portion,
    const double heading_diff_to_lane) const {
  DCHECK_LE(0.0, portion);
  DCHECK_GE(1.0, portion);

  const pnc_map::Lane* lane = GetLaneFromId(lane_id);
  const math::geometry::PolylineCurve2d& center_line = lane->center_line();
  const double arc_length = center_line.GetTotalArcLength() * portion;
  math::geometry::Point2d point = center_line.GetInterp(arc_length);

  return math::Pose2d(
      point.x(), point.y(),
      math::WrapFromMinusPiToPi(center_line.GetInterpTheta(arc_length) +
                                heading_diff_to_lane));
}

math::Pose2d ReasoningTestFixture::FindPoseInCrosswalk(
    const int64_t crosswalk_id, const double portion) const {
  DCHECK_LE(0.0, portion);
  DCHECK_GE(1.0, portion);
  const auto iter = std::find_if(
      trajectory_info().traffic_rules().crosswalks.begin(),
      trajectory_info().traffic_rules().crosswalks.end(),
      [&crosswalk_id](const auto crosswalk_in_lane_seq) {
        return crosswalk_in_lane_seq.crosswalk_ptr == nullptr
                   ? false
                   : crosswalk_in_lane_seq.crosswalk_ptr->id() == crosswalk_id;
      });
  DCHECK(iter != trajectory_info().traffic_rules().crosswalks.end())
      << " Invalid Crosswalk ID. ";
  const math::geometry::PolylineCurve2d* center_line =
      iter->crosswalk_ptr->center_line();

  math::geometry::Point2d point;
  const double arc_length =
      DCHECK_NOTNULL(center_line)->GetTotalArcLength() * portion;
  point = center_line->GetInterp(arc_length);

  return math::Pose2d(
      point.x(), point.y(),
      center_line->GetInterpTheta(center_line->GetTotalArcLength()));
}

math::Pose2d ReasoningTestFixture::FindPoseInExitZone(
    const int64_t zone_id, const double portion) const {
  const auto iter =
      std::find_if(trajectory_info().traffic_rules().zones.begin(),
                   trajectory_info().traffic_rules().zones.end(),
                   [&zone_id](const auto zone_in_lane_seq) {
                     return zone_in_lane_seq.zone_ptr == nullptr
                                ? false
                                : zone_in_lane_seq.zone_ptr->id() == zone_id;
                   });
  DCHECK(iter != trajectory_info().traffic_rules().zones.end())
      << " Invalid Zone ID. ";
  DCHECK(iter->zone_ptr->type() == hdmap::Zone::ROAD_EXIT)
      << " Zone type must be exit. ";
  const math::geometry::PolylineCurve2d* center_line =
      iter->zone_ptr->center_line();

  const double arc_length =
      DCHECK_NOTNULL(center_line)->GetTotalArcLength() * portion;
  math::geometry::Point2d point = center_line->GetInterp(arc_length);

  return math::Pose2d(point.x(), point.y(),
                      center_line->GetInterpTheta(arc_length));
}

void ReasoningTestFixture::SetEgoCanbus(bool is_auto) {
  canbus_.set_mode(is_auto ? voy::AUTO_FULL : voy::MANUAL);

  mutable_world_model().robot_state_ptr_->UpdatePoseCanbus(
      ego_pose_, std::make_shared<const voy::Canbus>(canbus_),
      /*replan_request=*/nullptr,
      /*replan_locator_debug=*/nullptr);
}

void ReasoningTestFixture::SetEgoPose(const int64_t lane_id,
                                      const double portion, const double speed,
                                      const double accel) {
  ego_lane_id_ = lane_id;
  math::Pose2d pose_2d = LanePointToPose(lane_id, portion);
  voy::Pose ego_pose;
  ego_pose.set_x(pose_2d.x());
  ego_pose.set_y(pose_2d.y());
  ego_pose.set_yaw(pose_2d.yaw().value());
  ego_pose.set_vel_x(speed * std::cos(pose_2d.yaw().value()));
  ego_pose.set_vel_y(speed * std::sin(pose_2d.yaw().value()));
  ego_pose.set_acc_forward(accel);
  ego_pose.set_timestamp(timestamp());

  ego_pose_ = ego_pose;

  mutable_world_model().robot_state_ptr_->UpdatePoseCanbus(
      ego_pose_, std::make_shared<const voy::Canbus>(canbus_),
      /*replan_request=*/nullptr,
      /*replan_locator_debug=*/nullptr);
}

void ReasoningTestFixture::SetEgoPose(const double x_pos, const double y_pos,
                                      const double yaw, const double speed,
                                      const double accel) {
  voy::Pose ego_pose;
  ego_pose.set_x(x_pos);
  ego_pose.set_y(y_pos);
  ego_pose.set_yaw(yaw);
  ego_pose.set_vel_x(speed * std::cos(yaw));
  ego_pose.set_vel_y(speed * std::sin(yaw));
  ego_pose.set_acc_forward(accel);
  ego_pose.set_timestamp(timestamp());

  ego_pose_ = ego_pose;

  mutable_world_model().robot_state_ptr_->UpdatePoseCanbus(
      ego_pose_, std::make_shared<const voy::Canbus>(canbus_),
      /*replan_request=*/nullptr,
      /*replan_locator_debug=*/nullptr);
}

void ReasoningTestFixture::SetEgoPoseWoYaw(const double x_pos,
                                           const double y_pos,
                                           const int64_t lane_id,
                                           const double speed) {
  ego_lane_id_ = lane_id;
  const pnc_map::Lane* lane = GetLaneFromId(lane_id);
  const math::geometry::PolylineCurve2d& center_line = lane->center_line();
  const double arc_length =
      center_line
          .GetProximity({x_pos, y_pos}, math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  const double yaw = center_line.GetInterpTheta(arc_length);

  voy::Pose ego_pose;
  ego_pose.set_x(x_pos);
  ego_pose.set_y(y_pos);
  ego_pose.set_yaw(yaw);
  ego_pose.set_vel_x(speed * std::cos(yaw));
  ego_pose.set_vel_y(speed * std::sin(yaw));
  ego_pose.set_acc_forward(0.0);
  ego_pose.set_timestamp(timestamp());

  ego_pose_ = ego_pose;

  mutable_world_model().robot_state_ptr_->UpdatePoseCanbus(
      ego_pose_, std::make_shared<const voy::Canbus>(canbus_),
      /*replan_request=*/nullptr,
      /*replan_locator_debug=*/nullptr);
}

void ReasoningTestFixture::SetEgoTrajectory(
    const planner::pb::Trajectory& trajectory) {
  mutable_world_model().robot_state_ptr_->UpdateTrajectory(trajectory);
}

void ReasoningTestFixture::SetOrderStartTimeInfo(int hour, int min, int sec) {
  mutable_world_model().order_start_time_info_->tm_hour = hour;
  mutable_world_model().order_start_time_info_->tm_min = min;
  mutable_world_model().order_start_time_info_->tm_sec = sec;
}

void ReasoningTestFixture::SetLaneChangeStatus(
    const planner::pb::LaneChangeMode direction,
    const planner::pb::LaneChangeState lane_change_state,
    const double start_arclength, const int64_t source_lane_id,
    const int64_t target_lane_id, bool is_current_homotopy_lane_change,
    const int64_t leading_object_id, const int64_t tailing_object_id) {
  const pnc_map::Lane* source_lane = GetLaneFromId(source_lane_id);
  const pnc_map::Lane* target_lane = GetLaneFromId(target_lane_id);
  LaneChangeInstance lane_change_instance(direction, start_arclength,
                                          source_lane, target_lane);
  lane_change_instance.UpdateSourceAndTargetLaneSequences(
      DrivableLaneReasoner(), {source_lane, target_lane});
  lane_change_instance.SetTargetRegionMarkingSequence(
      std::map<int64_t, ViolableLaneMarkingRange>());

  lane_change_status_.lane_change_instance = lane_change_instance;
  lane_change_status_.direction = direction;
  lane_change_status_.lane_change_state = lane_change_state;
  lane_change_status_.is_current_homotopy_lane_change =
      is_current_homotopy_lane_change;
  lane_change_status_.leading_object_id = leading_object_id;
  lane_change_status_.tailing_object_id = tailing_object_id;
  UpdateCrossLaneInfoManager();
}

void ReasoningTestFixture::SetPullOverStatus(
    pull_over::PullOverStatus&& pull_over_status) {
  pull_over_status_ = std::move(pull_over_status);
  UpdatePullOverInfo();
}

void ReasoningTestFixture::SetPullOverDestination(double portion,
                                                  bool is_pull_over_jump_in) {
  DCHECK(behavior_type_ == planner::pb::DECOUPLED_PULL_OVER)
      << "Please set the behavior type to DECOUPLED_PULL_OVER before setting "
         "the pull over destination.";

  const int64_t lane_id = ego_lane_id_;
  DCHECK_LE(0, lane_id) << "Ego lane not initialized";

  const math::Pose2d pose_2d = LanePointToPose(lane_id, portion);
  const auto pull_over_dest = math::geometry::Point2d(pose_2d.x(), pose_2d.y());

  pull_over::DestinationInitialDecision dest_init_decision;
  dest_init_decision.reference_dest = pull_over_dest;
  dest_init_decision.reference_target_pose = pose_2d;

  // Set dest initial decision.
  destination_.initial_decision = std::move(dest_init_decision);

  // Set jump in guidance.
  // Jump in guidance may be set by other setter function.
  std::optional<pull_over::JumpInGuidance>& jump_in_guidance =
      destination_.jump_in_guidance;
  if (is_pull_over_jump_in) {
    if (!jump_in_guidance.has_value()) {
      // Initialization.
      jump_in_guidance = pull_over::JumpInGuidance();
    }
    jump_in_guidance->set_destination(pull_over_dest);
    jump_in_guidance->set_target_pose(pose_2d);
    jump_in_guidance->set_jump_in_type(
        planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN);
  }

  UpdatePullOverInfo();
}

void ReasoningTestFixture::SetPullOverRearObject(int64_t target_lane_id,
                                                 int64_t rear_object_id) {
  DCHECK(behavior_type_ == planner::pb::DECOUPLED_PULL_OVER)
      << "Please set the behavior type to DECOUPLED_PULL_OVER before setting "
         "the pull over destination rear obstacles.";

  std::vector<pull_over::PullOverObjectInfo> trailing_objects;
  trailing_objects.reserve(1);

  const pnc_map::Lane* target_lane = GetLaneFromId(target_lane_id);
  for (const auto& agent : agent_list_.agent_list()) {
    if (agent.tracked_object().id() == rear_object_id) {
      // Build contour.
      std::vector<math::geometry::Point2d> points;
      points.reserve(agent.tracked_object().contour_size());
      for (const voy::Point2d& pb_pt : agent.tracked_object().contour()) {
        points.emplace_back(pb_pt.x(), pb_pt.y());
      }
      // Build sl box.
      math::geometry::FrenetAxisAlignedBox2d sl_box(target_lane->center_line(),
                                                    points);

      // NOTE: Construct object without planner object pointer.
      trailing_objects.emplace_back(
          TypedObjectId(rear_object_id,
                        planner::pb::ObjectSourceType::kTrackedObject),
          math::geometry::PolygonWithCache2d(points), std::move(sl_box),
          /*planner_object_ptr=*/nullptr, /*construction_zone_ptr=*/nullptr);
      break;
    }
  }

  if (!destination_.jump_in_guidance.has_value()) {
    destination_.jump_in_guidance = pull_over::JumpInGuidance();
  }

  destination_.jump_in_guidance->set_trailing_objects(trailing_objects);

  UpdatePullOverInfo();
}

void ReasoningTestFixture::SetIsForRAUnstuckRequest(
    const bool is_for_ra_unstuck_request) {
  is_for_ra_unstuck_request_ = is_for_ra_unstuck_request;
}

void ReasoningTestFixture::SetIsPullOutJumpOut(
    const bool is_pull_out_jump_out) {
  is_pull_out_jump_out_ = is_pull_out_jump_out;
}

void ReasoningTestFixture::SetIsPullOutTriggered(
    const bool is_pull_out_triggered) {
  mutable_previous_iter_seed()
      .mutable_speed_seed()
      ->mutable_speed_reasoner_seeds()
      ->mutable_pullout_reasoner_seed()
      ->set_is_triggered(is_pull_out_triggered);
  DCHECK(world_context_.has_value());
  world_context_->UpdateIsPullOutTriggered(previous_iter_seed());
}

void ReasoningTestFixture::SetIsLaneSequenceFeasible(
    bool is_lane_sequence_feasible) {
  is_lane_sequence_feasible_ = is_lane_sequence_feasible;
}

void ReasoningTestFixture::SetIsWaypointAssistInvoked(
    const bool is_waypoint_assist_invoked) {
  is_waypoint_assist_invoked_ = is_waypoint_assist_invoked;
}

void ReasoningTestFixture::SetIsWaypointBackupTrajectoryUsed(
    const bool is_waypoint_backup_trajectory_used) {
  is_waypoint_backup_trajectory_used_ = is_waypoint_backup_trajectory_used;
}

void ReasoningTestFixture::SetIsHardBoundaryConstraintRelaxedForPath(
    const bool is_hard_boundary_relaxed_in_path) {
  is_hard_boundary_relaxed_in_path_ = is_hard_boundary_relaxed_in_path;
}

void ReasoningTestFixture::SetMotionMode(
    const planner::pb::MotionMode motion_mode) {
  motion_mode_ = motion_mode;
}

void ReasoningTestFixture::SetBehaviorType(
    planner::pb::BehaviorType behavior_type) {
  behavior_type_ = behavior_type;
}

void ReasoningTestFixture::SetOpenSpaceInfo(
    const open_space::OpenSpaceInfo& open_space_info) {
  open_space_info_ = open_space_info;
}

void ReasoningTestFixture::SetShouldPlannerRespondMRC(
    bool should_planner_respond_mrc) {
  mutable_world_model().should_planner_respond_mrc_ =
      should_planner_respond_mrc;
}

void ReasoningTestFixture::SetReverseDrivingInfo(
    const planner::pb::ReverseDrivingState_Enum reverse_driving_state,
    const double portion) {
  const int64_t lane_id = ego_lane_id_;
  DCHECK_LE(0, lane_id) << "Ego lane not initialized";
  reverse_driving_info_.set_state(reverse_driving_state);
  if (portion != -1) {
    const math::Pose2d pose_2d = LanePointToPose(lane_id, portion);
    auto* stop_point = reverse_driving_info_.mutable_stop_point();
    stop_point->set_x(pose_2d.x());
    stop_point->set_y(pose_2d.y());
  }
}

void ReasoningTestFixture::GenerateUnextendedPath() {
  DCHECK(extended_path_.has_value());

  const math::geometry::Point2d ego_pose_2d(ego_pose_.x(), ego_pose_.y());
  const math::ProximityQueryInfo ego_pose_proximity =
      path().QueryProximity(ego_pose_2d, math::pb::UseExtensionFlag::kForbid);

  DCHECK(ego_pose_proximity.relative_position ==
         math::RelativePosition::kWithIn);
  const auto iter =
      std::lower_bound(path().arc_lengths().begin(), path().arc_lengths().end(),
                       ego_pose_proximity.arc_length);
  DCHECK(iter != path().arc_lengths().end());

  const int ego_pose_index = std::distance(path().arc_lengths().begin(), iter);

  std::vector<math::geometry::Point2d> points;
  points.reserve(path().points().size() - ego_pose_index);
  points.push_back(ego_pose_2d);
  for (unsigned ii = ego_pose_index + 1; ii < path().points().size(); ++ii) {
    points.push_back(path().points()[ii]);
  }

  unextended_path_.emplace(
      adv_geom::Path2dWithJuke(math::geometry::PolylineCurve2d(points)));
}

// TODO(speed): refine this function, it's a little bit tricky now.
// Maybe call PathPlanner to generate it.
void ReasoningTestFixture::CreateLaneFollowPath(bool extend_backward) {
  DCHECK_LE(0, ego_lane_id_) << "Ego lane not initialized";

  maneuver_type_ = planner::pb::ManeuverType::DECOUPLED_FORWARD;
  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);

  // TODO(speed): make this more general and more robust.
  const std::vector<const pnc_map::Lane*> lane_sequence =
      extend_backward
          ? std::vector<
                const pnc_map::Lane*>{current_lane->predecessors().front(),
                                      current_lane,
                                      current_lane->successors().front()}
          : std::vector<const pnc_map::Lane*>{
                current_lane, current_lane->successors().front()};

  std::vector<const math::geometry::PolylineCurve2d*> curve_vec;
  curve_vec.reserve(lane_sequence.size());

  for (const auto& lane_ptr : lane_sequence) {
    curve_vec.push_back(&(lane_ptr->center_line()));
  }

  const math::geometry::Polyline2d points =
      GetSampledCurveForLaneSequence(curve_vec);
  const math::geometry::Polyline2d reverse_points = {points.rbegin(),
                                                     points.rend()};
  extended_path_.emplace(
      adv_geom::Path2dWithJuke(math::geometry::PolylineCurve2d(
          motion_mode() == planner::pb::MotionMode::BACKWARD ? reverse_points
                                                             : points)));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  lane_sequence_result_.lane_sequence_type =
      planner::pb::LaneSequenceCandidate::LANE_FOLLOW;
  lane_sequence_result_.current_lane = current_lane;
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = lane_sequence;

  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  trajectory_info_.emplace(
      unextended_path(), path(), path_evaluation_result(), ego_intention(),
      lane_sequence_iterator_opt_.value(),
      planning_segment_sequence_opt_.value(),
      world_model().traffic_light_detection(), physical_boundaries(),
      *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
      maneuver_type_, behavior_type_, pull_over_info_,
      pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
      lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
      reverse_driving_info(), open_space_info(),
      plan_init_ra_arc_length_on_extended_path(),
      world_model_.value().robot_state(), is_pull_out_jump_out_,
      is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
      lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
      is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
      planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
      mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
      /*intention_plan_debug_in=*/nullptr);
}

void ReasoningTestFixture::CreateLaneChangePath(int64_t source_lane_id,
                                                int64_t target_lane_id) {
  const LaneChangeInstance lc_instance(
      planner::pb::LaneChangeMode::RIGHT_LANE_CHANGE,
      /*start_arclength_on_source_lane=*/0.0, GetLaneFromId(source_lane_id),
      GetLaneFromId(target_lane_id));
  DCHECK_NE(lc_instance.direction(),
            planner::pb::LaneChangeMode::NONE_LANE_CHANGE);

  // point_sequence = source segment + bridge segment + target segment.
  std::vector<math::geometry::Point2d> point_sequence =
      GetSampledCurveFromSourceLane(lc_instance.source_lane(),
                                    /*portion for start arclength=*/0.1);

  // Generate and insert the bridge segment between source lane and target
  // lane into the sequence.
  const std::vector<math::geometry::Point2d> bridge_segment =
      GetSampledCurveBetweenTwoNeighbouringLanes(
          lc_instance.source_lane(), lc_instance.target_lane(),
          /*portion for start arclength=*/0.1,
          /*portion for end arclength=*/0.8);
  point_sequence.insert(point_sequence.end(), bridge_segment.begin() + 1,
                        bridge_segment.end());

  // TODO(Zhaohui): smooth the curve and involve speed profile info to
  // accomodate the testings from speed solver.
  const std::vector<math::geometry::Point2d> target_segment =
      GetSampledCurveFromTargetLane(lc_instance.target_lane(),
                                    /*portion for end arclength=*/0.8);
  point_sequence.insert(point_sequence.end(), target_segment.begin() + 1,
                        target_segment.end());
  extended_path_.emplace(adv_geom::Path2dWithJuke(
      math::geometry::PolylineCurve2d(point_sequence)));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  std::vector<const pnc_map::Lane*> lane_sequence;
  lane_sequence.push_back(&lc_instance.source_lane());
  lane_sequence.push_back(&lc_instance.target_lane());
  lane_sequence_result_.current_lane = &(lc_instance.source_lane());
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = lane_sequence;
  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  trajectory_info_.emplace(
      unextended_path(), path(), path_evaluation_result(), ego_intention(),
      lane_sequence_iterator_opt_.value(),
      planning_segment_sequence_opt_.value(),
      world_model().traffic_light_detection(), physical_boundaries(),
      *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
      maneuver_type_, behavior_type_, pull_over_info_,
      pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
      lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
      reverse_driving_info(), open_space_info(),
      plan_init_ra_arc_length_on_extended_path(),
      world_model_.value().robot_state(), is_pull_out_jump_out_,
      is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
      lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
      is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
      planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
      mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
      /*intention_plan_debug_in=*/nullptr);
}

void ReasoningTestFixture::CreatePathWithLaneSequence(
    const std::vector<int64_t>& lane_sequence_id,
    planner::pb::ManeuverType maneuver_type, bool update_trajectory_info) {
  // Ego lane id must be in the given lane sequence.
  DCHECK(std::find(lane_sequence_id.begin(), lane_sequence_id.end(),
                   ego_lane_id_) != lane_sequence_id.end());

  maneuver_type_ = maneuver_type;

  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);
  const std::vector<const pnc_map::Lane*> lane_sequence =
      GetLaneSequenceFromId(lane_sequence_id);

  std::vector<const math::geometry::PolylineCurve2d*> curve_vec;
  curve_vec.reserve(lane_sequence.size());

  for (const auto& lane_ptr : lane_sequence) {
    curve_vec.push_back(&(lane_ptr->center_line()));
  }

  const math::geometry::Polyline2d points =
      GetSampledCurveForLaneSequence(curve_vec);

  extended_path_.emplace(
      adv_geom::Path2dWithJuke(math::geometry::PolylineCurve2d(points)));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  lane_sequence_result_.current_lane = current_lane;
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = lane_sequence;

  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  if (update_trajectory_info) {
    trajectory_info_.emplace(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(),
        world_model_.value().robot_state(), is_pull_out_jump_out_,
        is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
        lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
        is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }
}

void ReasoningTestFixture::CreateNudgePathWithSineWave(
    const std::vector<int64_t>& lane_sequence_id, int64_t start_nudging_lane_id,
    int64_t end_nudging_lane_id, double start_nudging_lane_portion,
    double end_nudging_lane_portion, double max_lateral_offset_in_meter,
    bool is_nudging_from_right, bool is_nudging_back,
    planner::pb::ManeuverType maneuver_type, bool update_trajectory_info) {
  DCHECK(!lane_sequence_id.empty());
  maneuver_type_ = maneuver_type;
  // Ego lane id must be in the given lane sequence.
  const auto ego_iter =
      std::find(lane_sequence_id.begin(), lane_sequence_id.end(), ego_lane_id_);
  DCHECK(ego_iter != lane_sequence_id.end());
  const int64_t ego_lane_index =
      std::distance(lane_sequence_id.begin(), ego_iter);
  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);
  const math::geometry::Point2d ego_pose_2d(ego_pose_.x(), ego_pose_.y());
  const math::ProximityQueryInfo ego_pose_proximity_on_current_lane =
      current_lane->center_line().GetProximity(
          ego_pose_2d, math::pb::UseExtensionFlag::kForbid);
  // Start nudging point must be no behind of ego current pose.
  const auto start_nudging_iter = std::find(
      lane_sequence_id.begin(), lane_sequence_id.end(), start_nudging_lane_id);
  DCHECK(start_nudging_iter != lane_sequence_id.end());
  const int64_t start_nudging_lane_index =
      std::distance(lane_sequence_id.begin(), start_nudging_iter);
  DCHECK_GE(start_nudging_lane_index, ego_lane_index);
  if (start_nudging_lane_index == ego_lane_index) {
    DCHECK_LE(ego_pose_proximity_on_current_lane.arc_length,
              current_lane->center_line().GetTotalArcLength() *
                  start_nudging_lane_portion);
  }
  DCHECK_GE(start_nudging_lane_portion, 0.0);
  DCHECK_LE(start_nudging_lane_portion, 1.0);
  // End nudging point must be no behind of the start nudging counterpart.
  const auto end_nudging_iter = std::find(
      lane_sequence_id.begin(), lane_sequence_id.end(), end_nudging_lane_id);
  DCHECK(end_nudging_iter != lane_sequence_id.end());
  const int64_t end_nudging_lane_index =
      std::distance(lane_sequence_id.begin(), end_nudging_iter);
  DCHECK_GE(end_nudging_lane_index, start_nudging_lane_index);
  if (end_nudging_lane_index == start_nudging_lane_index) {
    DCHECK_LE(start_nudging_lane_portion, end_nudging_lane_portion);
  }
  DCHECK_GE(end_nudging_lane_portion, 0.0);
  DCHECK_LE(end_nudging_lane_portion, 1.0);
  // This function only supports open nudging path when the point at which
  // nudging ends is the last point of lane sequence.
  if (!is_nudging_back) {
    DCHECK_EQ(end_nudging_lane_id, lane_sequence_id.back());
    DCHECK_EQ(end_nudging_lane_portion, 1.0);
  }
  // Find curves before, along, and after the nudging portion.
  const std::vector<const pnc_map::Lane*> lane_sequence =
      GetLaneSequenceFromId(lane_sequence_id);
  std::vector<math::geometry::PolylineCurve2d> curves_before_nudging;
  std::vector<math::geometry::PolylineCurve2d> curves_after_nudging;
  std::vector<math::geometry::PolylineCurve2d> original_curves_along_nudging;
  double total_arclength_of_original_curves_along_nudging = 0.0;
  for (int64_t lane_index = 0UL;
       lane_index < static_cast<int64_t>(lane_sequence.size()); ++lane_index) {
    const pnc_map::Lane* lane = lane_sequence.at(lane_index);
    const math::geometry::PolylineCurve2d& center_line = lane->center_line();
    if (lane_index < start_nudging_lane_index) {
      curves_before_nudging.push_back(center_line);
    } else if (lane_index > end_nudging_lane_index) {
      curves_after_nudging.push_back(center_line);
    } else {
      if (lane_index == start_nudging_lane_index &&
          start_nudging_lane_index == end_nudging_lane_index) {
        SplitPolylineCurveInThreeWaysByPortion(
            center_line, start_nudging_lane_portion, end_nudging_lane_portion,
            curves_before_nudging, original_curves_along_nudging,
            curves_after_nudging);
      } else if (lane_index == start_nudging_lane_index) {
        SplitPolylineCurveInTwoWaysByPortion(
            center_line, start_nudging_lane_portion, curves_before_nudging,
            original_curves_along_nudging);
      } else if (lane_index == end_nudging_lane_index) {
        SplitPolylineCurveInTwoWaysByPortion(
            center_line, end_nudging_lane_portion,
            original_curves_along_nudging, curves_after_nudging);
      } else {
        original_curves_along_nudging.push_back(center_line);
      }
      total_arclength_of_original_curves_along_nudging +=
          original_curves_along_nudging.back().GetTotalArcLength();
    }
  }
  // Transform original curves along nudging portion into a single nudging curve
  // via sine wave mapping.
  std::vector<math::geometry::Point2d> nudging_points;
  double accumulated_arclength = 0.0;
  for (const math::geometry::PolylineCurve2d& curve :
       original_curves_along_nudging) {
    for (size_t i = 0UL; i < curve.size(); ++i) {
      const double local_arc_length = curve.GetArcLengthAtPoint(i);
      const double global_arclength = accumulated_arclength + local_arc_length;
      const double shift_phase =
          is_nudging_back ? (global_arclength /
                             total_arclength_of_original_curves_along_nudging) *
                                M_PI
                          : (global_arclength /
                             total_arclength_of_original_curves_along_nudging) *
                                M_PI_2;
      const double lateral_shift =
          is_nudging_from_right
              ? -std::sin(shift_phase) * max_lateral_offset_in_meter
              : std::sin(shift_phase) * max_lateral_offset_in_meter;
      const auto [point, deriv] =
          curve.GetInterpPointAndDeriv(local_arc_length);
      nudging_points.emplace_back(point.x() - lateral_shift * deriv.y(),
                                  point.y() + lateral_shift * deriv.x());
    }
    accumulated_arclength += curve.GetTotalArcLength();
  }
  math::geometry::PolylineCurve2d nudging_curve(nudging_points);
  // Concatenate pointers in |curves_before_nudging|, |nudging_curve|, and
  // |curves_after_nudging| in a single vector, then do downsampling.
  std::vector<const math::geometry::PolylineCurve2d*> polyline_curves_ptr;
  std::for_each(curves_before_nudging.begin(), curves_before_nudging.end(),
                [&polyline_curves_ptr](
                    const math::geometry::PolylineCurve2d& polyline_curve) {
                  polyline_curves_ptr.push_back(&polyline_curve);
                });
  polyline_curves_ptr.push_back(&nudging_curve);
  std::for_each(curves_after_nudging.begin(), curves_after_nudging.end(),
                [&polyline_curves_ptr](
                    const math::geometry::PolylineCurve2d& polyline_curve) {
                  polyline_curves_ptr.push_back(&polyline_curve);
                });
  const math::geometry::Polyline2d sampled_points =
      GetSampledCurveForLaneSequence(polyline_curves_ptr);
  // Generate path and associated infos.
  extended_path_.emplace(adv_geom::Path2dWithJuke(
      math::geometry::PolylineCurve2d(sampled_points)));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  lane_sequence_result_.current_lane = current_lane;
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = lane_sequence;
  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  if (update_trajectory_info) {
    trajectory_info_.emplace(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(),
        world_model_.value().robot_state(), is_pull_out_jump_out_,
        is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
        lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
        is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }
}

void ReasoningTestFixture::CreatePathWithLaneSequenceAndUntrimmedLaneSequence(
    const std::vector<int64_t>& lane_sequence_id,
    const std::vector<int64_t>& untrimmed_lane_sequence_id,
    planner::pb::ManeuverType maneuver_type, bool update_trajectory_info) {
  // Ego lane id must be in the given lane sequence.
  DCHECK(std::find(lane_sequence_id.begin(), lane_sequence_id.end(),
                   ego_lane_id_) != lane_sequence_id.end());

  maneuver_type_ = maneuver_type;

  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);
  const std::vector<const pnc_map::Lane*> lane_sequence =
      GetLaneSequenceFromId(lane_sequence_id);
  const std::vector<const pnc_map::Lane*> untrimmed_lane_sequence =
      GetLaneSequenceFromId(untrimmed_lane_sequence_id);

  std::vector<const math::geometry::PolylineCurve2d*> curve_vec;
  curve_vec.reserve(lane_sequence.size());

  for (const auto& lane_ptr : lane_sequence) {
    curve_vec.push_back(&(lane_ptr->center_line()));
  }

  const math::geometry::Polyline2d points =
      GetSampledCurveForLaneSequence(curve_vec);

  extended_path_.emplace(
      adv_geom::Path2dWithJuke(math::geometry::PolylineCurve2d(points)));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  lane_sequence_result_.current_lane = current_lane;
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = untrimmed_lane_sequence;

  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  if (update_trajectory_info) {
    trajectory_info_.emplace(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(),
        world_model_.value().robot_state(), is_pull_out_jump_out_,
        is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
        lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
        is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }
}

void ReasoningTestFixture::CreatePathWithPathCurveAndLaneSequence(
    const math::geometry::PolylineCurve2d& path_curve,
    const std::vector<int64_t>& lane_sequence_id,
    planner::pb::ManeuverType maneuver_type, bool update_trajectory_info) {
  // Ego lane id must be in the given lane sequence.
  DCHECK(std::find(lane_sequence_id.begin(), lane_sequence_id.end(),
                   ego_lane_id_) != lane_sequence_id.end());

  maneuver_type_ = maneuver_type;

  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);
  const std::vector<const pnc_map::Lane*> lane_sequence =
      GetLaneSequenceFromId(lane_sequence_id);
  extended_path_.emplace(adv_geom::Path2dWithJuke(path_curve));
  GenerateUnextendedPath();
  path_evaluation_result_ = path::PathEvaluationResult();

  lane_sequence_result_.current_lane = current_lane;
  lane_sequence_result_.lane_sequence = lane_sequence;
  lane_sequence_result_.untrimmed_lane_sequence = lane_sequence;
  lane_sequence_iterator_opt_.emplace(lane_sequence_result());
  planning_segment_sequence_opt_.emplace();

  DCHECK(world_model_.has_value());
  if (update_trajectory_info) {
    trajectory_info_.emplace(
        unextended_path(), path(), path_evaluation_result(), ego_intention(),
        lane_sequence_iterator_opt_.value(),
        planning_segment_sequence_opt_.value(),
        world_model().traffic_light_detection(), physical_boundaries(),
        *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
        maneuver_type_, behavior_type_, pull_over_info_,
        pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
        lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
        reverse_driving_info(), open_space_info(),
        plan_init_ra_arc_length_on_extended_path(),
        world_model_.value().robot_state(), is_pull_out_jump_out_,
        is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
        lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
        is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
        planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
        mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
        /*intention_plan_debug_in=*/nullptr);
  }
}

voy::ConstructionZone& ReasoningTestFixture::AddConstructionZone(
    int64_t id, const std::vector<math::geometry::Point2d>& contour_points,
    voy::ZoneSource source, voy::SpaceType space_type) {
  DCHECK_GT(contour_points.size(), 2);
  voy::ConstructionZone construction_zone;
  construction_zone.set_id(id);
  construction_zone.set_zone_source(source);
  construction_zone.set_space_type(space_type);
  auto* zone_contour = construction_zone.mutable_zone();
  for (const auto& pt : contour_points) {
    auto* point = zone_contour->add_points();
    point->set_x(pt.x());
    point->set_y(pt.y());
  }
  construction_zones_.push_back(std::move(construction_zone));
  return construction_zones_.back();
}

prediction::pb::Agent& ReasoningTestFixture::AddAgent(
    const voy::perception::ObjectType object_type, const int id,
    const math::Pose2d& pose, const double length, const double width,
    const double velocity, const double height) {
  voy::TrackedObject tracked_object;
  tracked_object.set_object_type(object_type);
  tracked_object.set_id(id);

  tracked_object.set_center_x(pose.x());
  tracked_object.set_center_y(pose.y());
  tracked_object.set_center_z(0.0);

  tracked_object.set_length(length);
  tracked_object.set_width(width);
  tracked_object.set_height(height);
  tracked_object.set_heading(pose.yaw().value());
  tracked_object.set_box_heading(pose.yaw().value());

  PopulateObjectContour(pose, length, width, tracked_object);

  tracked_object.set_velocity(velocity);

  prediction::pb::Agent& added_agent = *(agent_list_.add_agent_list());
  *(added_agent.mutable_tracked_object()) = std::move(tracked_object);
  return added_agent;
}

prediction::pb::Agent& ReasoningTestFixture::AddAgent(
    const voy::perception::ObjectType object_type, const int id,
    const int64_t lane_id, const double portion, const double length,
    const double width, const double velocity, const double height,
    const double heading_diff_to_lane) {
  math::Pose2d pose = LanePointToPose(lane_id, portion, heading_diff_to_lane);
  return AddAgent(object_type, id, pose, length, width, velocity, height);
}

prediction::pb::Agent& ReasoningTestFixture::AddAgentInExitZone(
    const voy::perception::ObjectType object_type, const int id,
    const int64_t zone_id, const double portion, const double length,
    const double width, const double velocity, const double height) {
  math::Pose2d pose = FindPoseInExitZone(zone_id, portion);
  return AddAgent(object_type, id, pose, length, width, velocity, height);
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStationaryPredictedTrajectory(
    const math::Pose2d& pose, double likelihood, int traj_id,
    prediction::pb::Agent& agent) const {
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      *(agent.add_predicted_trajectories());
  added_predicted_trajectory.set_is_multi_output_trajectory(true);
  added_predicted_trajectory.set_is_output_trajectory(true);
  added_predicted_trajectory.set_backup_trajectory_type(
      prediction::pb::BackupTrajectoryType::NOT_BACKUP);
  const int probability_in_ppm = static_cast<int>(likelihood * 1e6);
  added_predicted_trajectory.set_probability_in_ppm(probability_in_ppm);
  added_predicted_trajectory.set_id(traj_id);
  const double heading = pose.yaw().value();

  added_predicted_trajectory.mutable_traj_poses()->Reserve(
      kPredictedTrajectoryStep + 1);
  for (size_t ii = 0; ii < kPredictedTrajectoryStep + 1; ++ii) {
    planner::pb::TrajectoryPose trajectory_pose;
    trajectory_pose.set_timestamp(
        timestamp() + kPredictedTrajectoryTimestampIntervalInMSec * ii);
    trajectory_pose.set_x_pos(pose.x());
    trajectory_pose.set_y_pos(pose.y());
    trajectory_pose.set_z_pos(0.0);
    trajectory_pose.set_heading(heading);
    trajectory_pose.set_speed(0.0);
    trajectory_pose.set_odom(0.0);
    *(added_predicted_trajectory.add_traj_poses()) = std::move(trajectory_pose);
  }

  agent.mutable_stationary_intention()->set_stationary_intention_type(
      prediction::pb::StationaryIntentionType::STATIONARY_NOT_TO_MOVE);
  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStationaryPredictedTrajectory(
    int64_t lane_id, double portion, double likelihood, int traj_id,
    prediction::pb::Agent& agent, double bbox_heading_diff_to_lane) const {
  return AddStationaryPredictedTrajectory(
      LanePointToPose(lane_id, portion, bbox_heading_diff_to_lane), likelihood,
      traj_id, agent);
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStraightPredictedTrajectory(
    const int64_t start_lane_id, const double start_portion,
    const int64_t end_lane_id, const double end_portion,
    const double likelihood, const int traj_id, prediction::pb::Agent& agent,
    double bbox_heading_diff_to_lane) {
  return AddStraightPredictedTrajectory(
      LanePointToPose(start_lane_id, start_portion, bbox_heading_diff_to_lane),
      LanePointToPose(end_lane_id, end_portion, bbox_heading_diff_to_lane),
      likelihood, traj_id, agent);
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStraightPredictedTrajectoryWithConstantUncertainty(
    int64_t start_lane_id, double start_portion, int64_t end_lane_id,
    double end_portion, double likelihood, int traj_id,
    prediction::pb::Agent& agent, double lat_sd, double long_sd,
    double bbox_heading_diff_to_lane) {
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      AddStraightPredictedTrajectory(start_lane_id, start_portion, end_lane_id,
                                     end_portion, likelihood, traj_id, agent,
                                     bbox_heading_diff_to_lane);
  for (auto& pose : *added_predicted_trajectory.mutable_traj_poses()) {
    pose.mutable_uncertainty()->set_lat_sd(lat_sd);
    pose.mutable_uncertainty()->set_long_sd(long_sd);
  }
  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStraightPredictedTrajectoryWithConstantAccel(
    const int64_t start_lane_id, const double start_portion,
    const int64_t end_lane_id, const double end_portion,
    const double likelihood, const int traj_id, prediction::pb::Agent& agent,
    const double accel) {
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      AddStraightPredictedTrajectory(start_lane_id, start_portion, end_lane_id,
                                     end_portion, likelihood, traj_id, agent);

  Limits limits;
  const Profile const_accel_profile = CreateConstantAccelProfileFromTime(
      limits, /*start_time=*/0.0,
      /*start_x=*/added_predicted_trajectory.traj_poses(0).odom(),
      agent.tracked_object().velocity(), accel, kPredictedTrajectoryStep,
      math::Ms2Sec(kPredictedTrajectoryTimestampIntervalInMSec));
  DCHECK_GE(const_accel_profile.size(), 1);

  for (int i = 0; i < added_predicted_trajectory.traj_poses_size(); ++i) {
    planner::pb::TrajectoryPose& pose =
        *added_predicted_trajectory.mutable_traj_poses(i);
    if (i >= static_cast<int>(const_accel_profile.size())) {
      pose.set_speed(const_accel_profile.back().v);
      pose.set_accel(const_accel_profile.back().a);
      continue;
    }

    pose.set_speed(const_accel_profile[i].v);
    pose.set_accel(const_accel_profile[i].a);
  }

  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStartToMovePredictedTrajectory(
    int64_t start_lane_id, double start_portion, int64_t end_lane_id,
    double end_portion, double likelihood, int traj_id,
    prediction::pb::Agent& agent, int start_to_move_idx,
    double bbox_heading_diff_to_lane) {
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      *(agent.add_predicted_trajectories());
  added_predicted_trajectory.set_backup_trajectory_type(
      prediction::pb::BackupTrajectoryType::NOT_BACKUP);
  const int probability_in_ppm = static_cast<int>(likelihood * 1e6);
  added_predicted_trajectory.set_probability_in_ppm(probability_in_ppm);
  added_predicted_trajectory.set_id(traj_id);
  *(added_predicted_trajectory.mutable_yield_intention()) =
      agent.yield_intention();
  added_predicted_trajectory.set_is_multi_output_trajectory(false);
  added_predicted_trajectory.set_is_output_trajectory(false);

  added_predicted_trajectory.mutable_traj_poses()->Reserve(
      kPredictedTrajectoryStep + 1);
  DCHECK_LT(start_to_move_idx, kPredictedTrajectoryStep);
  const math::Pose2d start_pose =
      LanePointToPose(start_lane_id, start_portion, bbox_heading_diff_to_lane);
  const math::Pose2d end_pose =
      LanePointToPose(end_lane_id, end_portion, bbox_heading_diff_to_lane);
  const double diff_x = end_pose.x() - start_pose.x();
  const double diff_y = end_pose.y() - start_pose.y();
  const double heading = std::atan2(diff_y, diff_x);
  // Add the static part.
  for (int i = 0; i < start_to_move_idx; ++i) {
    AddTrajectoryPoseToPredictedTrajectory(
        start_pose.x(), start_pose.y(), heading,
        /*speed=*/0.0, /*odom=*/0.0,
        timestamp() + kPredictedTrajectoryTimestampIntervalInMSec * i,
        added_predicted_trajectory);
  }
  // Add the moving part.
  const int64_t start_timestamp =
      timestamp() +
      kPredictedTrajectoryTimestampIntervalInMSec * start_to_move_idx;
  const int remaining_length = kPredictedTrajectoryStep + 1 - start_to_move_idx;
  AddLinearInterpolatedPosesToPredictedTrajectory(
      start_pose, end_pose, start_timestamp, /*start_odom=*/0.0,
      remaining_length,
      /*including_end_pose=*/true, added_predicted_trajectory);

  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddLinearInterpolatedPosesToPredictedTrajectory(
    const math::Pose2d& start_pose, const math::Pose2d& end_pose,
    const int64_t start_timestamp, const double start_odom,
    const int num_of_poses, const bool including_end_pose,
    prediction::pb::PredictedTrajectory& predicted_trajectory) {
  DCHECK_GE(num_of_poses, 1);

  const int num_of_segments = num_of_poses - (including_end_pose ? 1 : 0);
  const double diff_x = end_pose.x() - start_pose.x();
  const double diff_y = end_pose.y() - start_pose.y();
  const double heading = std::atan2(diff_y, diff_x);
  const double travel_distance = std::hypot(diff_x, diff_y);

  // Handle special case when num_of_poses is 1.
  if (num_of_poses == 1) {
    if (including_end_pose) {
      AddTrajectoryPoseToPredictedTrajectory(
          end_pose.x(), end_pose.y(), heading,
          travel_distance /
              math::Ms2Sec(kPredictedTrajectoryTimestampIntervalInMSec),
          start_odom + travel_distance,
          start_timestamp + kPredictedTrajectoryTimestampIntervalInMSec,
          predicted_trajectory);
    } else {
      AddTrajectoryPoseToPredictedTrajectory(
          start_pose.x(), start_pose.y(), heading,
          travel_distance /
              math::Ms2Sec(kPredictedTrajectoryTimestampIntervalInMSec),
          start_odom, start_timestamp, predicted_trajectory);
    }
    return predicted_trajectory;
  }

  const double x_step = diff_x / num_of_segments;
  const double y_step = diff_y / num_of_segments;
  const double speed =
      travel_distance / num_of_segments /
      math::Ms2Sec(kPredictedTrajectoryTimestampIntervalInMSec);

  for (int ii = 0; ii < num_of_poses; ++ii) {
    AddTrajectoryPoseToPredictedTrajectory(
        start_pose.x() + x_step * ii, start_pose.y() + y_step * ii, heading,
        speed,
        start_odom +
            speed * math::Ms2Sec(kPredictedTrajectoryTimestampIntervalInMSec) *
                ii,
        start_timestamp + kPredictedTrajectoryTimestampIntervalInMSec * ii,
        predicted_trajectory);
  }
  return predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddStraightPredictedTrajectory(
    const math::Pose2d& start_pose, const math::Pose2d& end_pose,
    const double likelihood, const int traj_id, prediction::pb::Agent& agent) {
  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      *(agent.add_predicted_trajectories());
  added_predicted_trajectory.set_is_multi_output_trajectory(true);
  added_predicted_trajectory.set_is_output_trajectory(true);
  added_predicted_trajectory.set_backup_trajectory_type(
      prediction::pb::BackupTrajectoryType::NOT_BACKUP);

  const int probability_in_ppm = static_cast<int>(likelihood * 1e6);
  added_predicted_trajectory.set_probability_in_ppm(probability_in_ppm);
  added_predicted_trajectory.set_id(traj_id);
  *(added_predicted_trajectory.mutable_yield_intention()) =
      agent.yield_intention();

  added_predicted_trajectory.mutable_traj_poses()->Reserve(
      kPredictedTrajectoryStep + 1);
  AddLinearInterpolatedPosesToPredictedTrajectory(
      start_pose, end_pose, timestamp(), /*start_odom=*/0.0,
      kPredictedTrajectoryStep + 1,
      /*including_end_pose=*/true, added_predicted_trajectory);

  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddMultiPointPredictedTrajectory(
    const std::vector<math::Pose2d>& waypoints, const double likelihood,
    const int traj_id, prediction::pb::Agent& agent) {
  DCHECK(!waypoints.empty()) << "waypoints are empty!";
  DCHECK_GT(waypoints.size(), 1)
      << "use AddStationaryPredictedTrajectory instead.";
  DCHECK_LT(waypoints.size(), kPredictedTrajectoryStep)
      << "too many waypoints.";

  std::vector<double> segment_length;
  double total_length = 0.0;
  // poses.size() is guaranteed to be larger than 1 by DCHECK.
  segment_length.reserve(waypoints.size() - 1);
  for (size_t ii = 0; ii + 1 < waypoints.size(); ++ii) {
    segment_length.push_back(
        std::hypot(waypoints[ii + 1].x() - waypoints[ii].x(),
                   waypoints[ii + 1].y() - waypoints[ii].y()));
    total_length += segment_length.back();
  }

  const double step = total_length / kPredictedTrajectoryStep;

  std::vector<int> waypoint_indices(waypoints.size(), 0);
  waypoint_indices.back() = kPredictedTrajectoryStep;
  for (size_t ii = 1; ii + 1 < waypoint_indices.size(); ++ii) {
    waypoint_indices[ii] = static_cast<int>(
        waypoint_indices[ii - 1] + std::round(segment_length[ii - 1] / step));
  }

  return AddMultiPointPredictedTrajectory(waypoints, waypoint_indices,
                                          likelihood, traj_id, agent);
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddMultiPointPredictedTrajectory(
    const std::vector<math::Pose2d>& waypoints,
    const std::vector<int>& waypoint_indices, const double likelihood,
    const int traj_id, prediction::pb::Agent& agent) {
  DCHECK(!waypoints.empty()) << "waypoints are empty!";
  DCHECK_GT(waypoints.size(), 1)
      << "use AddStationaryPredictedTrajectory instead.";
  DCHECK_LT(waypoints.size(), kPredictedTrajectoryStep)
      << "too many waypoints.";

  DCHECK_EQ(waypoints.size(), waypoint_indices.size());
  DCHECK_EQ(0, waypoint_indices.front()) << "first waypoint must be index 0.";
  DCHECK_EQ(kPredictedTrajectoryStep, waypoint_indices.back())
      << "last waypoint must be kPredictedTrajectoryStep.";
  DCHECK(std::is_sorted(waypoint_indices.begin(), waypoint_indices.end()));

  prediction::pb::PredictedTrajectory& added_predicted_trajectory =
      *(agent.add_predicted_trajectories());
  added_predicted_trajectory.set_is_multi_output_trajectory(true);
  added_predicted_trajectory.set_is_output_trajectory(true);
  added_predicted_trajectory.set_backup_trajectory_type(
      prediction::pb::BackupTrajectoryType::NOT_BACKUP);

  const int probability_in_ppm = static_cast<int>(likelihood * 1e6);
  added_predicted_trajectory.set_probability_in_ppm(probability_in_ppm);
  added_predicted_trajectory.set_id(traj_id);
  *(added_predicted_trajectory.mutable_yield_intention()) =
      agent.yield_intention();

  added_predicted_trajectory.mutable_traj_poses()->Reserve(
      kPredictedTrajectoryStep + 1);
  double odom = 0.0;
  int64_t pose_timestamp = timestamp();
  for (size_t ii = 0; ii + 1 < waypoints.size(); ++ii) {
    // Generates a linear segment for each waypoint segment. Note each
    // waypoint is included as the start pose except for the last waypoint.
    const bool including_end_pose = (ii + 2) == waypoints.size();
    int num_of_poses = waypoint_indices[ii + 1] - waypoint_indices[ii] +
                       (including_end_pose ? 1 : 0);
    // WARNING: user should try to avoid num_of_poses to be zero in which case
    // the waypoints will not be included.
    if (num_of_poses == 0) continue;
    AddLinearInterpolatedPosesToPredictedTrajectory(
        waypoints[ii], waypoints[ii + 1], pose_timestamp, odom, num_of_poses,
        including_end_pose, added_predicted_trajectory);
    pose_timestamp +=
        kPredictedTrajectoryTimestampIntervalInMSec * num_of_poses;
    odom += std::hypot(waypoints[ii + 1].x() - waypoints[ii].x(),
                       waypoints[ii + 1].y() - waypoints[ii].y());
  }

  return added_predicted_trajectory;
}

prediction::pb::PredictedTrajectory&
ReasoningTestFixture::AddPredictedTrajectory(prediction::pb::Agent& agent) {
  return *(agent.add_predicted_trajectories());
}

void ReasoningTestFixture::UpdateWorldContext() {
  world_context_.emplace(world_model(), motion_mode());
  pb::AgentReactionTrackerSeed art_seed;
  pb::EgoStateTracker& ego_state =
      (*(art_seed.mutable_ego_state_at_timestamp_map()))[timestamp()];
  ego_state.set_speed(world_context().ego_speed());
  ego_state.set_acceleration(world_context().ego_acceleration());
  DCHECK(world_context_.has_value());
  world_context_->UpdateEgoStateHistory(art_seed);
}

void ReasoningTestFixture::UpdateForLaneChange(
    const double last_lane_change_point_distance) {
  DCHECK_LE(0, ego_lane_id_) << "Ego lane not initialized";

  UpdateWorldModel();
  // TODO(planner): should we directly use world_model().planner_object_map()?
  planner_object_map_ = world_model().planner_object_map();

  // Generate lane change env analyzer.
  planner::pb::LaneSequenceCostFactors lane_sequence_cost_factors;
  planner::pb::LastLaneChangeChancePoint*
      mutable_last_lane_change_chance_point =
          lane_sequence_cost_factors.mutable_last_lane_change_chance_point();
  mutable_last_lane_change_chance_point->set_lane_change_reason(
      planner::pb::LastLaneChangeChancePoint::BLOCKAGE);
  mutable_last_lane_change_chance_point->set_dist_from_ego(
      last_lane_change_point_distance);

  const planner::pb::LaneSequenceCandidate::LaneSequenceType lc_sequence_type =
      planner::pb::LaneSequenceCandidate::LANE_CHANGE;

  LaneChangeInfo lane_change_info;

  lane_change_info.lane_change_instance =
      lane_change_status_.lane_change_instance;
  lane_change_info.lane_change_metadata = CalculateLaneChangeMetadata(
      lane_sequence_result().planning_segment_sequence,
      lane_change_info.lane_change_instance,
      *DCHECK_NOTNULL(lane_sequence_result().current_lane),
      lane_sequence_cost_factors,
      *DCHECK_NOTNULL(world_model().GetLatestJointPncMapService()),
      previous_iter_seed(), world_model().robot_state(), /*debug=*/nullptr);
  std::ostringstream debug_oss;
  DCHECK(world_model_.has_value());
  const pnc_map::Lane* current_lane = GetLaneFromId(ego_lane_id_);
  planner::LaneChangeEnvAnalyzer::PopulateLaneChangeEnvInfo(
      world_model(), mutable_previous_iter_seed(),
      lane_change_status_.lane_change_instance.source_lane_sequence(),
      lane_sequence_cost_factors, lc_sequence_type,
      lane_change_info.lane_change_instance,
      lane_change_info.lane_change_metadata, current_lane,
      lane_change_info.all_lane_change_instances,
      planner::pb::LaneChangeInfoLevel::kCompleteLaneChangeInfo,
      lane_change_info.signal_source_type,
      /*dist_to_destination=*/std::numeric_limits<double>::infinity(),
      /*should_latch_urgency_info=*/false,
      /*has_valid_preview_route=*/true, lane_change_info.lane_change_env_info,
      debug_oss);

  lane_change_info_ = std::move(lane_change_info);

  UpdateCrossLaneInfoManager();

  UpdateWorldContext();

  trajectory_info_.emplace(
      unextended_path(), path(), path_evaluation_result(), ego_intention(),
      lane_sequence_iterator(), planning_segment_sequence(),
      world_model().traffic_light_detection(), physical_boundaries(),
      *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
      maneuver_type_, behavior_type_, pull_over_info_,
      pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
      lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
      reverse_driving_info(), open_space_info(),
      plan_init_ra_arc_length_on_extended_path(), world_model().robot_state(),
      is_pull_out_jump_out_, is_for_ra_unstuck_request_,
      is_lane_sequence_feasible_, lane_sequence_cost_factors_,
      is_waypoint_assist_invoked_, is_waypoint_backup_trajectory_used_,
      is_hard_boundary_relaxed_in_path_,
      planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
      mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
      /*intention_plan_debug_in=*/nullptr);

  UpdateOverlapRegionsAndProximityInfos();
  UpdateWrappedPredictionDecisions();
}

void ReasoningTestFixture::UpdateCrossLaneInfoManager() {
  std::vector<CrossLaneInfo> cross_lane_infos;
  if (lane_change_info_.lane_change_instance.IsDummy()) {
    cross_lane_info_manager_ = CrossLaneInfoManager(cross_lane_infos);
    return;
  }
  cross_lane_infos.emplace_back(GenerateCrossLaneInfoFromLaneChangeInfo(
      planner::pb::BehaviorType::CROSS_LANE, world_model(),
      std::cref(lane_change_info_), previous_iter_seed_, /*debug=*/nullptr));
  cross_lane_info_manager_ = CrossLaneInfoManager(cross_lane_infos);
}

void ReasoningTestFixture::UpdatePullOverInfo() {
  pull_over::PullOverDecisionKey dest_key_for_prep =
      destination_.jump_in_guidance.has_value() ? "kGapByRule_0_"
                                                : "kSimple_0_";
  dest_key_for_prep += std::to_string(destination_.destination_pt().x()) + "_" +
                       std::to_string(destination_.destination_pt().y());
  pull_over_info_ = pull_over::PullOverInfo(
      pull_over::PullOverStatus(pull_over_status_),
      pull_over::ImmediatePullOverTriggerInfo(),
      pull_over::PullOverExternalSignals{},
      lane_selection::LaneSequenceGeometry(),
      /*lane_sequence=*/{}, /*reference_destination=*/std::nullopt,
      /*destinations=*/{destination_}, std::make_optional(dest_key_for_prep),
      /*interested_objects=*/{},
      destination_.jump_in_guidance.has_value()
          ? std::make_optional(pull_over::GapAlignMetaData())
          : std::nullopt,
      pull_over::ProbeDecision(), planner::pb::PullOverDecisionSeed(),
      planner::pb::PullOverDebug());
}

void ReasoningTestFixture::UpdateUrgencyInfo() {
  DCHECK(world_context_.has_value());
  DCHECK(trajectory_info_.has_value());
  urgency_info_.emplace(
      world_context(), trajectory_info(), planner_object_map(), cz_ptr_map(),
      reasoning_inputs(), previous_iter_seed(), GetReasoningConfig(),
      *(mutable_current_iter_seed()->mutable_urgency_info_seed()),
      /*debug=*/nullptr);
}

void ReasoningTestFixture::Update() {
  mutable_world_model().mutable_global_object_manager_ptr()->Update(
      world_model().snapshot_timestamp(), agent_list(),
      planner::pb::EgoOffroadInfoSeed(),
      mutable_world_model()
          .assist_blockage_analyzer()
          .perception_fp_obstacle_tracker(),
      world_model().regional_map().regional_path.distance_to_destination_m,
      world_model().robot_state(), *world_model().pnc_map_service(),
      planner::pb::WorldModelSeed().object_type_tracking_map(),
      world_model()
          .regional_sections_locator()
          .tracked_object_perception_range_lanes(),
      world_model().assist_directive_generator().unstuck_directives(),
      world_model().GetStuckAssistInstruction(), nullptr);

  // TODO(planner): should we directly use world_model().planner_object_map()?
  planner_object_map_ = world_model().planner_object_map();

  const std::vector<planner::LaneSequenceResult> lane_sequence_results = {
      lane_sequence_result()};
  mutable_world_model().SetAssociatedRouteForObjectPredictedTrajectories(
      previous_iter_seed(), lane_sequence_results, &mutable_planning_debug());
  UpdateWorldContext();
  UpdateCrossLaneInfoManager();
  DCHECK(world_model_.has_value());
  DCHECK(lane_sequence_iterator_opt_.has_value());
  DCHECK(planning_segment_sequence_opt_.has_value());
  trajectory_info_.emplace(
      unextended_path(), path(), path_evaluation_result(), ego_intention(),
      lane_sequence_iterator_opt_.value(),
      planning_segment_sequence_opt_.value(),
      world_model().traffic_light_detection(), physical_boundaries(),
      *world_model().GetLatestJointPncMapService(), previous_iter_seed_,
      maneuver_type_, behavior_type_, pull_over_info_,
      pull_over_dest_meta_ptr(), cross_lane_info_manager_, lane_change_info_,
      lane_change_status_, /*xlane_nudge_meta=*/std::nullopt, motion_mode(),
      reverse_driving_info(), open_space_info(),
      plan_init_ra_arc_length_on_extended_path(),
      world_model_.value().robot_state(), is_pull_out_jump_out_,
      is_for_ra_unstuck_request_, is_lane_sequence_feasible_,
      lane_sequence_cost_factors_, is_waypoint_assist_invoked_,
      is_waypoint_backup_trajectory_used_, is_hard_boundary_relaxed_in_path_,
      planner::pb::TrajectoryType::NOMINAL_TRAJECTORY, path_reasoning_seed(),
      mutable_current_trajectory_info_seeds(), /*unique_path_homotopy_id=*/"",
      /*intention_plan_debug_in=*/nullptr);

  UpdateOverlapRegionsAndProximityInfos();
  UpdateWrappedPredictionDecisions();
}

const speed::pb::SpeedGeneratorConfig&
ReasoningTestFixture::GetSpeedGeneratorConfig() const {
  return PlannerConfigCenter::GetInstance()
      .GetDecoupledForwardManeuverConfig()
      .speed_generator_config();
}

const planner::pb::PlannerObjectConfig&
ReasoningTestFixture::GetPlannerObjectConfig() const {
  return PlannerConfigCenter::GetInstance()
      .world_model_config()
      .planner_object_config();
}

const speed::pb::ReasoningConfig& ReasoningTestFixture::GetReasoningConfig()
    const {
  return PlannerConfigCenter::GetInstance()
      .GetDecoupledForwardManeuverConfig()
      .speed_generator_config()
      .reasoning();
}

const speed::pb::VruReasonerConfig& ReasoningTestFixture::GetVruReasonerConfig()
    const {
  return GetReasoningConfig().reasoners().vru_reasoner();
}

const speed::pb::ReasoningInputConfig&
ReasoningTestFixture::GetReasoningInputConfig() const {
  return PlannerConfigCenter::GetInstance()
      .GetDecoupledForwardManeuverConfig()
      .speed_generator_config()
      .reasoning_input();
}

void ReasoningTestFixture::UpdateOverlapRegionsAndProximityInfos() {
  DCHECK(!path().empty());
  // Update required lateral gaps.
  obj_id_to_required_lat_gaps_ =
      ComputePrincipledRequiredLateralGapForPlannerObjects(
          planner_object_map(), PlannerConfigCenter::GetInstance()
                                    .GetDecoupledForwardManeuverConfig()
                                    .required_lateral_gap_config());
  // TODO(ThaiDuongLe): TrackedObject to be deprecated
  // Create TrackedObject class list;
  std::vector<planner::TrackedObject> tracked_object_list;
  tracked_object_list.reserve(agent_list().agent_list().size());
  for (const prediction::pb::Agent& agent : agent_list().agent_list()) {
    std::vector<const pnc_map::Lane*> lanes;

    planner::TrackedObject tracked_object(
        timestamp(), agent, /*max_interested_prediction_timestamp=*/INT64_MAX,
        lanes);
    tracked_object_list.push_back(std::move(tracked_object));
  }
  const int64_t ego_pose_timestamp = timestamp();
  const double plan_init_ra_arc_length =
      plan_init_ra_arc_length_on_extended_path();
  // TODO(ruiwu): If this is too slow, consider using a parallel_for.
  overlap_computer_opt_.emplace(path_for_overlap(), ego_pose_timestamp,
                                /*plan_init_ra_arc_length=*/0.0);

  // TODO(mengze): add support for OverlapComputationPolicy;
  for (const auto& [object_id, predicted_trajectories] :
       object_prediction_map()) {
    const double comfort_required_lateral_gap =
        obj_id_to_required_lat_gaps_.at(object_id)
            .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed();
    for (const auto& agent_traj : predicted_trajectories) {
      overlap_computer_opt_->PreComputePaddedPathData(
          comfort_required_lateral_gap);
      std::vector<speed::pb::OverlapRegion> curr_regions =
          overlap_computer_opt_->ComputeOverlapRegionV2(
              agent_traj, comfort_required_lateral_gap);
      std::move(curr_regions.begin(), curr_regions.end(),
                std::back_inserter(object_overlap_regions_[object_id]));
    }
  }

  const speed::PathForOverlap extended_path_for_overlap(
      path(), GetVehicleParamsForOverlap());
  const speed::ObjectProximityComputer proximity_computer(
      extended_path_for_overlap, path_for_overlap(), plan_init_ra_arc_length);
  for (const auto& [object_id, planner_object] : planner_object_map()) {
    object_proximity_infos_[object_id] =
        proximity_computer.ComputeProximityInfo(
            planner_object.pose(),
            planner_object.tracked_object().acceleration());
  }
  // TODO(speed): populate debug;
}

void ReasoningTestFixture::UpdateWrappedPredictionDecisions() {
  overlap_computation_policy_map_ =
      GetDefaultOverlapComputationPolicy(planner_object_map());
  reasoning_inputs_ = PrepareReasoningInputs(
      world_context(), trajectory_info(), ego_intention(), planner_object_map(),
      overlap_computation_policy_map(), object_prediction_map(),
      /*optional_object_occupancy_param_map=*/std::nullopt,
      object_overlap_regions(), object_proximity_infos(),
      obj_id_to_required_lat_gaps(), previous_iter_seed_,
      /*use_tbb=*/false, GetOrCreateReasoningDebug()->mutable_input_debug());

  UpdateUrgencyInfo();

  PredictionDecisionMaker prediction_decision_maker;
  wrapped_prediction_decisions_ = prediction_decision_maker.MakeDecisions(
      world_context(), trajectory_info(), urgency_info(), reasoning_inputs(),
      pinch_regions_mapper(), GetReasoningConfig(),
      GetOrCreateReasoningDebug());
}

void ReasoningTestFixture::PopulateOverlapComputerByUpdatedInfo(
    speed::OverlapComputer* overlap_computer_ptr) const {
  for (const auto& [object_id, predicted_trajectories] :
       object_prediction_map()) {
    const double comfort_required_lateral_gap =
        obj_id_to_required_lat_gaps_.at(object_id)
            .GetComfortRequiredLateralGapGoingStraightAtZeroSpeed();
    for (const auto& agent_traj : predicted_trajectories) {
      (void)agent_traj;
      overlap_computer_ptr->PreComputePaddedPathData(
          comfort_required_lateral_gap);
    }
  }
}

speed::ReferenceGenerator ReasoningTestFixture::CreateReferenceGenerator()
    const {
  const speed::pb::SpeedGeneratorConfig& speed_config =
      PlannerConfigCenter::GetInstance()
          .GetDecoupledForwardManeuverConfig()
          .speed_generator_config();
  speed::ReferenceGenerator ref_generator(
      speed_config.reference(), world_model().robot_state(), unextended_path(),
      speed_config.for_car_type().limits(),
      trajectory_info().lane_sequence_iterator().lane_sequence(),
      world_model().trajectory_guider_output(),
      /*path_homotopy=*/planner::pb::IntentionResult::IGNORE_ALL,
      /*relax_ego_exceeding_road_speed_limit_factor=*/0.0,
      /*discomfort_to_decay_ego_exceeding_road_speed_limit=*/std::nullopt,
      /*lane_speed_limits=*/{}, motion_mode(),
      /*order_start_time_info=*/world_model().order_start_time_info(),
      ReferenceGeneratorSettings(),
      world_model().pnc_map_service()->hdmap()->config().name());
  ref_generator.RegulateInitialStateAndUpdateImmutableTime(
      speed_config.for_search().dt());
  return ref_generator;
}

speed::pb::ReasoningDebug* ReasoningTestFixture::GetOrCreateReasoningDebug() {
  planner::pb::DecoupledManeuverDebug* maneuver_debug = nullptr;
  planner::pb::BehaviorReasonersDebug* behavior_reasoner_debug =
      mutable_planning_debug().mutable_behavior_reasoner_debug();
  if (!behavior_reasoner_debug->decoupled_maneuvers().empty()) {
    maneuver_debug = behavior_reasoner_debug->mutable_decoupled_maneuvers(0);
    DCHECK(maneuver_debug->type() == maneuver_type_);
  } else {
    maneuver_debug = behavior_reasoner_debug->add_decoupled_maneuvers();
    maneuver_debug->set_type(maneuver_type_);
  }

  planner::pb::LaneSequenceInfoAndIntentPlanDebug* lane_sequence_plan_debug =
      maneuver_debug->single_lane_sequence_plan_debug().empty()
          ? maneuver_debug->add_single_lane_sequence_plan_debug()
          : maneuver_debug->mutable_single_lane_sequence_plan_debug(0);
  planner::pb::IntentionPlanDebug* intention_plan_debug =
      lane_sequence_plan_debug->single_intention_plan_debug().empty()
          ? lane_sequence_plan_debug->add_single_intention_plan_debug()
          : lane_sequence_plan_debug->mutable_single_intention_plan_debug(0);
  speed::pb::SpeedGeneratorDebug* speed_generator_debug =
      intention_plan_debug->mutable_speed_generator_debug();

  return speed_generator_debug->mutable_speed_reasoning_debug();
}

planner::pb::Trajectory ReasoningTestFixture::GenerateTrajectoryFromPath(
    const double speed) const {
  planner::pb::Trajectory trajectory;
  trajectory.set_timestamp(timestamp());
  for (unsigned ix = 0; ix < path().size(); ++ix) {
    const math::geometry::Point2d& point = path().GetPointAtIndex(ix);
    planner::pb::TrajectoryPose pose;
    pose.set_x_pos(point.x());
    pose.set_y_pos(point.y());
    pose.set_z_pos(0.0);
    pose.set_heading(path().GetHeadingAtIndex(ix));
    pose.set_speed(speed);
    pose.set_timestamp(timestamp() +
                       ix * kPredictedTrajectoryTimestampIntervalInMSec);
    *(trajectory.add_poses()) = std::move(pose);
  }

  return trajectory;
}

std::vector<const pnc_map::Lane*> ReasoningTestFixture::GetLaneSequenceFromId(
    const std::vector<int64_t>& lane_sequence_id) const {
  const pnc_map::PncMapService* pnc_map_service =
      world_model().pnc_map_service();
  return pnc_map_service->GetLaneSequence(lane_sequence_id);
}

const pnc_map::Lane* ReasoningTestFixture::GetLaneFromId(
    int64_t lane_id) const {
  return GetLaneSequenceFromId({lane_id}).front();
}

void ReasoningTestFixture::AddAgentSeenEgoTimeToSeed(const ObjectId object_id,
                                                     int64_t timestamp) {
  speed::pb::AgentReactionSeeds* previous_iter_seed =
      mutable_previous_iter_seed().mutable_agent_reaction_seeds();
  const auto iter = previous_iter_seed->agent_info_map().find(object_id);
  // If it is the first time that the agent sees ego, update its first saw ego
  // time.
  if (iter == previous_iter_seed->agent_info_map().end()) {
    (*previous_iter_seed->mutable_agent_info_map())[object_id]
        .mutable_agent_saw_ego_interval()
        ->set_agent_first_saw_ego_timestamp(timestamp);
  }
  (*previous_iter_seed->mutable_agent_info_map())[object_id]
      .mutable_agent_saw_ego_interval()
      ->set_agent_last_saw_ego_timestamp(timestamp);
}

const PredictionDecision& ReasoningTestFixture::GetPredictionDecision(
    const int64_t object_id, const int trajectory_id) const {
  auto decision_output_iter =
      std::find_if(wrapped_prediction_decisions().begin(),
                   wrapped_prediction_decisions().end(),
                   [object_id](const PredictionDecisionMakerOutput& output) {
                     return output.reasoning_object().id() == object_id;
                   });
  DCHECK(decision_output_iter != wrapped_prediction_decisions().end());

  const std::vector<PredictionDecision>& prediction_decisions =
      decision_output_iter->prediction_decisions();
  auto prediction_decision_iter = std::find_if(
      prediction_decisions.begin(), prediction_decisions.end(),
      [trajectory_id](const PredictionDecision& prediction_decision) {
        return prediction_decision.trajectory_id() == trajectory_id;
      });

  DCHECK(prediction_decision_iter != prediction_decisions.end());
  return *prediction_decision_iter;
}

VehicleParamsForOverlap ReasoningTestFixture::GetVehicleParamsForOverlap()
    const {
  const double ego_max_curvature = world_model()
                                       .robot_state()
                                       .juke_integrated_model_with_shape()
                                       .motion_model()
                                       .param()
                                       .limit()
                                       .kappa_limit()
                                       .max_val();
  const vehicle_model::pb::AxleRectangularMeasurement& ego_shape =
      world_model().robot_state().car_model_with_shape().shape_measurement();
  return VehicleParamsForOverlap(ego_shape, ego_max_curvature, motion_mode_);
}

void ReasoningTestFixture::WriteToRosBag(const std::string& bag_name) const {
  rosbag::Bag bag;
  ros::Time::init();
  bag.open(bag_name, rosbag::bagmode::Write);

  {
    // Writes Ego Pose
    const auto proto_msg =
        boost::make_shared<com::ConstPtrMessageAdapter<voy::Pose>>(
            std::make_shared<voy::Pose>(ego_pose()));
    auto msg_shape_shifter = ros_util::ConvertToShapeShifter(proto_msg);
    WriteSpecificTopic(av_comm::topic::kPose, msg_shape_shifter, &bag);
  }

  {
    // Writes Perception
    voy::TrackedObjectList tracked_object_list;
    tracked_object_list.set_timestamp(timestamp());
    *(tracked_object_list.mutable_pose()) = ego_pose();
    for (const prediction::pb::Agent& agent : agent_list().agent_list()) {
      *(tracked_object_list.add_tracked_objects()) = agent.tracked_object();
    }

    const auto proto_msg =
        boost::make_shared<com::ConstPtrMessageAdapter<voy::TrackedObjectList>>(
            std::make_shared<voy::TrackedObjectList>(tracked_object_list));
    auto msg_shape_shifter = ros_util::ConvertToShapeShifter(proto_msg);
    WriteSpecificTopic(av_comm::topic::kTrackedObjectList, msg_shape_shifter,
                       &bag);
  }

  {
    // Writes AgentList
    const auto proto_msg = boost::make_shared<
        com::ConstPtrMessageAdapter<prediction::pb::AgentList>>(
        std::make_shared<prediction::pb::AgentList>(agent_list()));
    auto msg_shape_shifter = ros_util::ConvertToShapeShifter(proto_msg);
    WriteSpecificTopic(av_comm::topic::kPredictedObjectList, msg_shape_shifter,
                       &bag);
  }

  {
    // Writes Trajectory
    const auto proto_msg = boost::make_shared<
        com::ConstPtrMessageAdapter<planner::pb::Trajectory>>(
        std::make_shared<planner::pb::Trajectory>(
            GenerateTrajectoryFromPath()));
    auto msg_shape_shifter = ros_util::ConvertToShapeShifter(proto_msg);
    WriteSpecificTopic(av_comm::topic::kPlanningTrajectory, msg_shape_shifter,
                       &bag);
  }

  {
    // Writes PlanningDebug
    const auto proto_msg = boost::make_shared<
        com::ConstPtrMessageAdapter<planner::pb::PlanningDebug>>(
        std::make_shared<planner::pb::PlanningDebug>(planning_debug()));
    auto msg_shape_shifter = ros_util::ConvertToShapeShifter(proto_msg);
    WriteSpecificTopic(av_comm::topic::kPlanningDebug, msg_shape_shifter, &bag);
  }
  bag.close();
}

void ReasoningTestFixture::AddAgentYieldIntention(
    prediction::pb::Agent& agent,
    ::prediction::pb::YieldIntentionType yield_intention_type,
    double yield_probability) {
  if (yield_intention_type == ::prediction::pb::UNKNOWN_YIELD_INTENTION) {
    DCHECK(yield_probability == 0.0);
  }
  prediction::pb::YieldIntention yield_intention;
  yield_intention.set_yield_probability(yield_probability);
  yield_intention.set_yield_intention_type(yield_intention_type);
  *(agent.mutable_yield_intention()) = std::move(yield_intention);
}

void ReasoningTestFixture::SetWaypointAssistPhase(
    ::planner::pb::AssistManeuverPhase::Enum phase) {
  mutable_world_model().waypoint_assist_tracker_.SetWaypointAssistPhase(phase);
}

void ReasoningTestFixture::SetWaypointAssistEndPoint(
    const voy::Point2d& point) {
  mutable_world_model().waypoint_assist_tracker_.SetWaypointAssistEndPoint(
      point);
}

void ReasoningTestFixture::AddWaypointAssistCommand(
    const planner::pb::WaypointBasedAssistCommand& assist_command,
    const std::vector<int64_t>& lane_ids) {
  planner::pb::AssistResponse assist_response;
  const std::vector<const pnc_map::Lane*>& lane_sequence_temp =
      GetLaneSequenceFromId(lane_ids);
  auto* waypoint_assist_command_ptr =
      assist_response.mutable_waypoint_based_assist_command();
  *waypoint_assist_command_ptr = assist_command;
  mutable_world_model().waypoint_assist_tracker_.UpdateWaypointAssistState(
      world_model().robot_state(), world_model().route_model(),
      world_model().assist_task_scheduler().assist_task_queue(),
      {assist_response}, {assist_response}, lane_sequence_temp,
      *world_model().GetLatestJointPncMapService(),
      world_model().drivable_lane_reasoner(),
      *world_model().global_route_solution(),
      world_model().current_lane_associator(),
      world_model().current_lane_reasoner(),
      planner::pb::DecoupledManeuverSeed(),
      /*road_blockages=*/
      std::map<int64_t, std::vector<RoadBlockageGroup>>(),
      /*has_left_assist_blockage_area=*/true,
      /*is_immediate_pull_over_triggered_by_planning=*/false,
      /*is_current_lanes_continuous=*/std::nullopt,
      /*waypoint_assist_debug=*/nullptr,
      /*lane_sequence_generator_debug=*/nullptr);
}

void ReasoningTestFixture::AddPhysicalBoundary(
    const math::geometry::Polyline2d& boundary_line, math::pb::Side side) {
  std::vector<BoundaryElement>& boundary_elements =
      side == math::pb::Side::kLeft
          ? physical_boundaries_.left_boundary_elements
          : physical_boundaries_.right_boundary_elements;
  boundary_elements.push_back(BoundaryElement());
  BoundaryElement& boundary_element = boundary_elements.back();
  boundary_element.boundary_line =
      math::geometry::PolylineCurve2d(boundary_line);
  boundary_element.boundary_type = path::BoundaryType::kPhysical;
}

voy::TrafficLight& ReasoningTestFixture::AddTrafficLight(
    int64_t timestamp, int64_t sign_id, voy::TrafficLight::Color color,
    voy::TrafficLight::Type type, int32_t countdown_number, bool is_flashing) {
  voy::TrafficLight& traffic_light = *traffic_lights_.add_traffic_lights();
  traffic_light.set_timestamp(timestamp);
  traffic_light.set_signal_id(sign_id);
  traffic_light.set_color(color);
  traffic_light.set_type(type);
  traffic_light.set_countdown_number(countdown_number);
  traffic_light.set_is_flashing(is_flashing);
  return traffic_light;
}

void ReasoningTestFixture::Reset() {
  trajectory_info_ = std::nullopt;
  extended_path_ = std::nullopt;
  unextended_path_ = std::nullopt;
  path_evaluation_result_ = std::nullopt;
  lane_sequence_iterator_opt_ = std::nullopt;
  agent_list_.Clear();
  planner_object_map_.clear();
  mutable_world_model()
      .mutable_global_object_manager_ptr()
      ->mutable_object_prediction_map()
      .clear();
  object_overlap_regions_.clear();
  cz_overlap_regions_.clear();
  object_proximity_infos_.clear();
  obj_id_to_required_lat_gaps_.clear();
  reasoning_inputs_.clear();
  wrapped_prediction_decisions_.clear();
  constraints_.clear();
  ego_intention_.Clear();
  planning_debug_.Clear();
}

ConstraintCreator ReasoningTestFixture::GenerateConstraintCreator() {
  constraints_.reserve(100);
  return ConstraintCreator(rear_axle_to_front_bumper(),
                           rear_axle_to_rear_bumper(), &constraints_);
}

void ReasoningTestFixture::AddLightAssistResponse(
    const planner::pb::AssistResponse& assist_response) {
  planner::pb::WorldModelSeed previous_world_model_seed;

  mutable_world_model().assist_task_scheduler_.UpdateAssistTaskQueue(
      world_model().assist_directive_generator().unstuck_directives(),
      world_model().mrc_request_ptr(),
      std::make_unique<std::vector<planner::pb::AssistResponse>>(
          std::vector<planner::pb::AssistResponse>{assist_response}),
      world_model().last_selected_lane_sequence(), {assist_response},
      world_model().robot_state(),
      *Seed::Access<token::UnstuckSeed>::GetMsg(SpeedLastFinishedFrame()),
      world_model().current_lane_associator(),
      world_model().current_lane_reasoner(),
      *world_model().global_route_solution(),
      /*assist_task_scheduler_debug=*/nullptr);

  mutable_world_model().assist_directive_generator_.UpdateUnstuckInfo(
      world_model().assist_task_scheduler().assist_task_queue(),
      world_model().planner_object_map(), {assist_response},
      world_model().assist_task_scheduler().remote_assist_signal(),
      world_model().robot_state(),
      world_model().current_lane_on_last_selected_lane_sequence(),
      *world_model().GetLatestJointPncMapService(),
      /*remote_speed_limit=*/nullptr,
      *Seed::Access<token::UnstuckSeed>::GetMsg(SpeedLastFinishedFrame()),
      mutable_previous_iter_seed().mutable_light_assist_seed(),
      /*assist_directive_generation_debug=*/nullptr);
}

}  // namespace speed
}  // namespace planner
