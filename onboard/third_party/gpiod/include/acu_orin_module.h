/**
 * @file acu_orin_module.h
 * <AUTHOR>
 * @brief
 * @version 0.1
 * @date 2024-06-17
 *
 * @copyright Copyright (c) 2024
 *
 */

#ifndef __ACU_ORIN_MODULE_H__
#define __ACU_ORIN_MODULE_H__
#include <cstdint>
#include <string>

#include "acu_dv_protocal.hpp"
#include "acu_mt_protocal.hpp"

namespace voyager {
namespace hardware {
#define kAcuOrinIdA (uint8_t)0
#define kAcuOrinIdB (uint8_t)1
#define kAcuOrinIdUnknown (uint8_t)2

class AcuOrinModule {
 public:
  AcuOrinModule() = default;
  virtual ~AcuOrinModule() = default;
  static int get_orin_id(uint8_t *id);

  bool dv_init(void *arg);
  void dv_deinit();
  int get_dv_msg(AcuDvOrinBaseInfoUn &module);
  std::string print_dv_msg(const AcuDvOrinBaseInfoUn &module);
  int test_dv_msg(int repeat = 10, uint16_t ms = 1000);

  bool mt_init(void *arg);
  void mt_deinit();
  int get_mt_msg(AcuMtOrinModuleSt &module);
  std::string print_mt_msg(const AcuMtOrinModuleSt &module);
  int test_mt_msg(int repeat = 10, uint16_t ms = 1000);
 private:
  int get_orin_temp_interface(int32_t *temp, const char *name);
  int get_orin_volt_interface(int32_t *volt, const char *name);
  int get_orin_curr_interface(int32_t *curr, const char *name);

  int get_orin_cpu_temp(int32_t *temp);
  int get_orin_gpu_temp(int32_t *temp);
  int get_orin_soc0_temp(int32_t *temp);
  int get_orin_soc1_temp(int32_t *temp);
  int get_orin_soc2_temp(int32_t *temp);
  int get_orin_tb_temp(int32_t *temp);
  int get_orin_tj_temp(int32_t *temp);
  int get_orin_tdiode_temp(int32_t *temp);

  int get_orin_gpu_soc_volt(int32_t *volt);
  int get_orin_cpu_cv_volt(int32_t *volt);
  int get_orin_sys_5v0_volt(int32_t *volt);
  int get_orin_vdd2_1v8a0_volt(int32_t *volt);

  int get_orin_gpu_soc_curr(int32_t *curr);
  int get_orin_cpu_cv_curr(int32_t *curr);
  int get_orin_sys_5v0_curr(int32_t *curr);
  int get_orin_vdd2_1v8a0_curr(int32_t *curr);

  int get_orin_gpu_load(int32_t *load);
  int get_orin_gpu_freq(int32_t *freq);
};
}  // namespace hardware
}  // namespace voyager

#endif
