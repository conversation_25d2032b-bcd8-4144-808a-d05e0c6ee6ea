#include "planner/speed/constraint/constraint_creator.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <locale>
#include <string>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>
#include <glog/logging.h>

#include "log_utils/log_macros.h"
#include "math/constants.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/constants.h"
#include "planner/path/reasoning/agent_intention/semantic_context/reasoning_info/object_reasoning_info.h"
#include "planner/planning_gflags.h"
#include "planner/speed/constraint/constraint.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/discomforts/discomforts.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/utility/object_id/typed_object_id.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/speed_constraint.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_yielding_fence.pb.h"

namespace planner {
namespace speed {
namespace {

// Creates constraint states that follows constant speed model.
// |longitudinal_speed| can be either positive or negative.
// A positive value indicates an agent moving along, and
// a negative values indicates an oncoming agent.
void AddConstraintStatesFromConstSpeed(
    double start_time, double ra_start_pos, double longitudinal_speed,
    double dt, int num_states, double agent_length,
    double leading_bumper_offset_from_ra, double trailing_bumper_offset_from_ra,
    std::vector<ConstraintState>& mutable_constraint_states) {
  mutable_constraint_states.reserve(num_states);
  const double delta_x = dt * longitudinal_speed;
  double state_time = start_time;
  double state_position = ra_start_pos;
  const double leading_bumper_adjust = leading_bumper_offset_from_ra;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra;
  for (int ix = 0; ix < num_states; ++ix) {
    const double start_x = state_position - leading_bumper_adjust;
    const double end_x = state_position + agent_length + trailing_bumper_adjust;
    mutable_constraint_states.emplace_back(
        state_time, state_time, DiscomfortVarying(start_x),
        DiscomfortVarying(end_x),
        /*strict_start_x=*/start_x, /*strict_end_x=*/end_x, longitudinal_speed,
        /*abs_lat_gap=*/0.0, /*timestamp_index=*/-1);
    state_time += dt;
    state_position += delta_x;
  }
}

// Creates constraint states that follows constant accel model.
// |initial_longitudinal_speed| can be either positive or negative.
// A positive value indicates an agent moving along, and
// a negative values indicates an oncoming agent.
// Meanwhile, |acceleration| is given along the agent's travel direction.
// For both moving-along and oncoming agents, a > 0 means it is accelerating,
// a < 0 means it is decelerating.
void AddConstraintStatesFromConstAccel(
    double start_time, double ra_start_pos, double max_longitudinal_movement,
    double initial_longitudinal_speed, double final_longitudinal_speed,
    double dt, int num_states, double agent_length, double acceleration,
    double leading_bumper_offset_from_ra, double trailing_bumper_offset_from_ra,
    std::vector<ConstraintState>& mutable_constraint_states) {
  // Final and initial longitudinal speed should have the same sign.
  DCHECK_GE(initial_longitudinal_speed * final_longitudinal_speed, 0.0);
  DCHECK_GE(max_longitudinal_movement, 0.0);
  // TODO(zhoujinhao, tienanli): rewrite this function.
  DCHECK_GE(std::abs(initial_longitudinal_speed),
            std::abs(final_longitudinal_speed));
  mutable_constraint_states.reserve(num_states);
  double state_time = start_time;
  double state_position = ra_start_pos;
  double state_speed = initial_longitudinal_speed;
  const double leading_bumper_adjust = leading_bumper_offset_from_ra;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra;
  for (int ix = 0; ix < num_states; ++ix) {
    const double start_x = state_position - leading_bumper_adjust;
    const double end_x = state_position + agent_length + trailing_bumper_adjust;
    mutable_constraint_states.emplace_back(
        state_time, state_time, DiscomfortVarying(start_x),
        DiscomfortVarying(end_x),
        /*strict_start_x=*/start_x, /*strict_end_x=*/end_x, state_speed,
        /*abs_lat_gap=*/0.0, /*timestamp_index=*/-1);

    // Update infomation for the next state.
    state_time += dt;
    // Update the speed depending on whether the agent is driving towards
    // the same or opposite direction with ego. The agent will at most reach
    // zero velocity and would not go backward w.r.t. its original direction.
    state_speed = state_speed >= 0 ? std::max(state_speed + acceleration * dt,
                                              final_longitudinal_speed)
                                   : std::min(state_speed - acceleration * dt,
                                              final_longitudinal_speed);
    // As this is a const accel model, the position of the next timestamp
    // equals dt times the average speed (of this and the next timestamp).
    state_position +=
        0.5 * dt *
        (mutable_constraint_states.back().signed_longitudinal_speed +
         state_speed);
    // Only generates the states within the |max_longitudinal_movement|.
    if (std::abs(state_position - ra_start_pos) > max_longitudinal_movement) {
      break;
    }
  }
}

}  // namespace

GlobalStrategySetter::GlobalStrategySetter(
    GlobalSpeedSolverSettings* global_speed_solver_settings)
    : global_speed_solver_settings_(
          DCHECK_NOTNULL(global_speed_solver_settings)) {}

pb::GlobalSpeedSolverSettings GlobalSpeedSolverSettings::ToProto() const {
  pb::GlobalSpeedSolverSettings global_speed_solver_settings_pb;
  global_speed_solver_settings_pb.set_global_discomfort_for_progress(
      global_discomfort_for_progress);
  global_speed_solver_settings_pb.set_gap_align_max_discomfort(
      gap_align_max_discomfort);
  global_speed_solver_settings_pb
      .set_estimated_half_cross_lane_duration_in_second(
          estimated_half_cross_lane_duration_in_second);
  global_speed_solver_settings_pb.set_global_gap_align_pass_max_active_time(
      global_gap_align_pass_max_active_time);
  global_speed_solver_settings_pb.set_global_gap_align_yield_max_active_time(
      global_gap_align_yield_max_active_time);
  global_speed_solver_settings_pb.set_progress_for_kinematic(
      progress_for_kinematic);
  global_speed_solver_settings_pb.set_globally_allow_emergency_brake(
      globally_allow_emergency_brake);
  global_speed_solver_settings_pb.set_enable_extra_diversity_by_risk_evaluator(
      enable_extra_diversity_by_risk_evaluator);
  *global_speed_solver_settings_pb.mutable_gap_diversity_settings() =
      gap_diversity_settings.ToProto();
  global_speed_solver_settings_pb.set_attraction_for_progress(
      attraction_for_progress);
  global_speed_solver_settings_pb.set_disable_speed_attractor(
      disable_speed_attractor);
  global_speed_solver_settings_pb.set_enable_opt_profile_validation(
      enable_opt_profile_validation);
  return global_speed_solver_settings_pb;
}

void GlobalStrategySetter::set_global_discomfort_for_progress(
    const double discomfort) {
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);
  global_speed_solver_settings_->global_discomfort_for_progress = discomfort;
}

void GlobalStrategySetter::set_gap_align_max_discomfort(
    const double discomfort) {
  DCHECK_LE(Discomforts::kMin, discomfort);
  DCHECK_LE(discomfort, Discomforts::kMax);
  global_speed_solver_settings_->gap_align_max_discomfort = discomfort;
}

void GlobalStrategySetter::set_estimated_half_cross_lane_duration_in_second(
    const double estimated_half_cross_lane_duration_in_second) {
  DCHECK_GE(estimated_half_cross_lane_duration_in_second, 0.0);
  global_speed_solver_settings_->estimated_half_cross_lane_duration_in_second =
      estimated_half_cross_lane_duration_in_second;
}

void GlobalStrategySetter::set_global_gap_align_pass_max_active_time(
    const double time) {
  DCHECK_LE(0.0, time);
  global_speed_solver_settings_->global_gap_align_pass_max_active_time = time;
}

void GlobalStrategySetter::set_global_gap_align_yield_max_active_time(
    const double time) {
  DCHECK_LE(0.0, time);
  global_speed_solver_settings_->global_gap_align_yield_max_active_time = time;
}

void GlobalStrategySetter::set_progress_for_kinematic(
    const bool progress_for_kinematic) {
  global_speed_solver_settings_->progress_for_kinematic =
      progress_for_kinematic;
}

void GlobalStrategySetter::set_globally_allow_emergency_brake(
    bool allow_emergency_brake) {
  global_speed_solver_settings_->globally_allow_emergency_brake =
      allow_emergency_brake;
}

void GlobalStrategySetter::set_enable_extra_diversity_by_risk_evaluator() {
  global_speed_solver_settings_->enable_extra_diversity_by_risk_evaluator =
      true;
}

void GlobalStrategySetter::set_gap_diversity_settings(
    const int faster_gap_num, const int slower_gap_num,
    const double delta_discomfort) {
  DCHECK_LE(faster_gap_num, 1);
  DCHECK_GE(faster_gap_num, 0);
  DCHECK_LE(slower_gap_num, 1);
  DCHECK_GE(slower_gap_num, 0);
  DCHECK_GE(delta_discomfort, 0.0);
  DCHECK_LE(delta_discomfort, Discomforts::kMax);
  global_speed_solver_settings_->gap_diversity_settings
      .extra_gap_amount_faster_than_default_gap = faster_gap_num;
  global_speed_solver_settings_->gap_diversity_settings
      .extra_gap_amount_slower_than_default_gap = slower_gap_num;
  global_speed_solver_settings_->gap_diversity_settings
      .delta_discomfort_for_gap_diversity = delta_discomfort;
}

void GlobalStrategySetter::set_attraction_for_progress() {
  global_speed_solver_settings_->attraction_for_progress = true;
}

void GlobalStrategySetter::set_disable_speed_attractor() {
  global_speed_solver_settings_->disable_speed_attractor = true;
}

void GlobalStrategySetter::set_enable_opt_profile_validation(
    bool enable_opt_profile_validation) {
  global_speed_solver_settings_->enable_opt_profile_validation =
      enable_opt_profile_validation;
}

ConstraintCreator::ConstraintCreator(double leading_bumper_offset_from_ra,
                                     double trailing_bumper_offset_from_ra,
                                     std::vector<Constraint>* constraints)
    : leading_bumper_offset_from_ra_(leading_bumper_offset_from_ra),
      trailing_bumper_offset_from_ra_(trailing_bumper_offset_from_ra),
      constraints_(DCHECK_NOTNULL(constraints)) {}

void ConstraintCreator::CheckUniqueConstraintId(
    const std::string& constraint_id) {
  DCHECK(constraint_ids_.find(constraint_id) == constraint_ids_.end())
      << constraint_id << " is not unique, please update.";
  constraint_ids_.insert(constraint_id);
}

ConstraintMutator ConstraintCreator::AddAvoidRegion(
    const double start_time, const double end_time, const double ra_start_x,
    const double ra_end_x, const pb::FenceType fence_type,
    const pb::ReasonerId reasoner_id, const std::string& constraint_id) {
  DCHECK_LE(start_time, end_time) << constraint_id;
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint =
      constraints_->emplace_back(pb::Constraint::AVOID_REGION, fence_type,
                                 ReasonerId_Name(reasoner_id), constraint_id);
  spin_lock_.clear(std::memory_order_release);
  const double leading_bumper_adjust = leading_bumper_offset_from_ra_;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra_;
  constraint.states.emplace_back(start_time, end_time,
                                 ra_start_x - leading_bumper_adjust,
                                 ra_end_x + trailing_bumper_adjust);
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddStopPoint(
    const double start_time, const double end_time, const double ra_stop_x,
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id) {
  DCHECK_LE(start_time, end_time) << constraint_id;
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint =
      constraints_->emplace_back(pb::Constraint::STOP_POINT, fence_type,
                                 ReasonerId_Name(reasoner_id), constraint_id);
  spin_lock_.clear(std::memory_order_release);
  const double leading_bumper_adjust = leading_bumper_offset_from_ra_;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra_;
  constraint.states.emplace_back(start_time, end_time,
                                 ra_stop_x - leading_bumper_adjust,
                                 ra_stop_x + trailing_bumper_adjust);
  // A stop point can only be yielded to.
  constraint.settings.pass_option = pb::ConstraintSettings::NA;
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddExactStopPoint(
    const double start_time, const double end_time, const double ra_stop_x,
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id) {
  DCHECK_LE(start_time, end_time) << constraint_id;
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint =
      constraints_->emplace_back(pb::Constraint::STOP_POINT, fence_type,
                                 ReasonerId_Name(reasoner_id), constraint_id);
  spin_lock_.clear(std::memory_order_release);
  const double leading_bumper_adjust = leading_bumper_offset_from_ra_;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra_;
  constraint.states.emplace_back(start_time, end_time,
                                 ra_stop_x - leading_bumper_adjust,
                                 ra_stop_x + trailing_bumper_adjust);
  // An exact stop point can only be yielded to.
  constraint.settings.pass_option = pb::ConstraintSettings::NA;
  // An exact stop point is hard, but we will not add min range for it to
  // make its stopping position non discomfort-varying.
  constraint.settings.yield_option = pb::ConstraintSettings::HARD;
  constraint.settings.allow_ignoring_min_range = true;
  // An exact stop point only has a very small stop range.
  constraint.settings.yield_stop_range =
      DiscomfortVarying(kExactStopPointStopRange);
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddNoBlock(
    const double start_time, const double ra_start_x, const double ra_end_x,
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const double no_block_min_speed,
    const double no_block_max_duration) {
  DCHECK_GE(no_block_min_speed, 0.0);
  DCHECK_GE(no_block_max_duration, 0.0);
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint =
      constraints_->emplace_back(pb::Constraint::NO_BLOCK, fence_type,
                                 ReasonerId_Name(reasoner_id), constraint_id);
  spin_lock_.clear(std::memory_order_release);
  const double leading_bumper_adjust = leading_bumper_offset_from_ra_;
  const double trailing_bumper_adjust = trailing_bumper_offset_from_ra_;
  // End time for no-block is always infinity.
  constraint.states.emplace_back(
      start_time, std::numeric_limits<double>::infinity(),
      ra_start_x - leading_bumper_adjust, ra_end_x + trailing_bumper_adjust);

  constraint.settings.no_block_min_speed = no_block_min_speed;
  constraint.settings.no_block_max_duration = no_block_max_duration;
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromConstAccel(
    const double start_time, const double ra_start_pos,
    const double max_longitudinal_movement,
    const double initial_longitudinal_speed,
    const double final_longitudinal_speed, const double dt,
    const int num_states, const double agent_length, const double acceleration,
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const int64_t obj_id) {
  // Any SPEED_OBJECT constraint must be associated with an agent.
  DCHECK_GT(obj_id, 0);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint SPEED_OBJECT with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      ConstraintType::Constraint_Type_SPEED_OBJECT, fence_type,
      ReasonerId_Name(reasoner_id), constraint_id, obj_id);
  spin_lock_.clear(std::memory_order_release);
  // required_lat_gap is inf so all states are considered in speed search.
  constraint.settings.pass_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint.settings.yield_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstAccel(
      start_time, ra_start_pos, max_longitudinal_movement,
      initial_longitudinal_speed, final_longitudinal_speed, dt, num_states,
      agent_length, acceleration, leading_bumper_offset_from_ra_,
      trailing_bumper_offset_from_ra_, mutable_constraint_states);

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddSpeedConstraintWithMaxMovementFromConstAccel(
    double ra_start_pos, double max_longitudinal_movement,
    double initial_longitudinal_speed, double final_longitudinal_speed,
    double agent_length, double acceleration, pb::FenceType fence_type,
    pb::ReasonerId reasoner_id, const std::string& constraint_id,
    int64_t obj_id) {
  return AddSpeedConstraintFromConstAccel(
      /*start_time=*/0.0, ra_start_pos, max_longitudinal_movement,
      initial_longitudinal_speed, final_longitudinal_speed,
      constants::PlannerDt(), constants::PlannerHorizonStateNum(), agent_length,
      acceleration, fence_type, reasoner_id, constraint_id, obj_id);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromConstSpeed(
    const double start_time, const double ra_start_pos,
    const double longitudinal_speed, const double dt, const int num_states,
    const double agent_length, const pb::FenceType fence_type,
    const pb::ReasonerId reasoner_id, const std::string& constraint_id,
    int64_t obj_id) {
  // Any SPEED_OBJECT constraint must be associated with an agent.
  DCHECK_GT(obj_id, 0);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint SPEED_OBJECT with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      ConstraintType::Constraint_Type_SPEED_OBJECT, fence_type,
      ReasonerId_Name(reasoner_id), constraint_id, obj_id);
  spin_lock_.clear(std::memory_order_release);
  // required_lat_gap is inf so all states are considered in speed search.
  constraint.settings.pass_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint.settings.yield_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstSpeed(
      start_time, ra_start_pos, longitudinal_speed, dt, num_states,
      agent_length, leading_bumper_offset_from_ra_,
      trailing_bumper_offset_from_ra_, mutable_constraint_states);

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddHorizonLengthSpeedConstraintFromConstSpeed(
    const double ra_start_pos, const double longitudinal_speed,
    const double agent_length, const pb::FenceType fence_type,
    const pb::ReasonerId reasoner_id, const std::string& constraint_id,
    const int64_t obj_id) {
  return AddSpeedConstraintFromConstSpeed(
      /*start_time=*/0.0, ra_start_pos, longitudinal_speed,
      constants::PlannerDt(), constants::PlannerHorizonStateNum(), agent_length,
      fence_type, reasoner_id, constraint_id, obj_id);
}

ConstraintMutator ConstraintCreator::AddStrictSpeedConstraintFromOverlapRegion(
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    const int64_t obj_id, const std::string& c_traj_id) {
  return AddStrictSpeedConstraintFromOverlapRegionSegment(
      fence_type, reasoner_id, constraint_id, overlap_region, obj_id, c_traj_id,
      /*start_idx=*/0,
      /*end_idx=*/overlap_region.overlap_slices().size());
}

ConstraintMutator
ConstraintCreator::AddStrictSpeedConstraintFromOverlapRegionSegment(
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    const int64_t obj_id, const std::string& c_traj_id, int start_idx,
    int end_idx) {
  // Any SPEED_OBJECT constraint must be associated with an agent.
  DCHECK_GT(obj_id, 0);
  DCHECK(overlap_region.contain_strict_overlap());
  DCHECK_GE(overlap_region.overlap_slices().size(), 1);
  CheckUniqueConstraintId(constraint_id);

  // OverlapRegion segment must be valid.
  DCHECK_LE(0, start_idx);
  DCHECK_LT(start_idx, end_idx);
  DCHECK_LE(end_idx, overlap_region.overlap_slices().size());

  // Create the constraint SPEED_OBJECT with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::SPEED_OBJECT, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, c_traj_id,
      GetOverlapRegionUniqueId(overlap_region));
  spin_lock_.clear(std::memory_order_release);
  constraint.settings.pass_required_lat_gap = DiscomfortVarying{0.0};
  constraint.settings.yield_required_lat_gap = DiscomfortVarying{0.0};

  constraint.states =
      ComputeStrictSpeedConstraintStatesFromOverlapRegionSegment(
          overlap_region, start_idx, end_idx);

  // TODO(rui/shiying): Set other overlap related settings.
  // like overlap_region_type
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromOverlapRegionSegment(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    const DiscomfortVarying& required_lat_gap, int64_t obj_id,
    const std::string& c_traj_id, bool use_discomfort_varying_overlap,
    int start_idx, int end_idx) {
  return AddSpeedConstraintFromOverlapRegionSegment(
      fence_type, reasoner_id, constraint_id, overlap_region,
      /*pass_required_lat_gap=*/required_lat_gap,
      /*yield_required_lat_gap=*/required_lat_gap, obj_id, c_traj_id,
      use_discomfort_varying_overlap, start_idx, end_idx);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromOverlapRegionSegment(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    DiscomfortVarying pass_required_lat_gap,
    DiscomfortVarying yield_required_lat_gap, int64_t obj_id,
    const std::string& c_traj_id, bool use_discomfort_varying_overlap,
    int start_idx, int end_idx) {
  // Any SPEED_OBJECT constraint must be associated with an agent.
  DCHECK_GT(obj_id, 0);
  DCHECK(pass_required_lat_gap.IsNonIncreasing());
  DCHECK_GE(overlap_region.overlap_slices().size(), 1);
  DCHECK_GE(pass_required_lat_gap(Discomforts::kMax), 0);
  DCHECK(yield_required_lat_gap.IsNonIncreasing());
  DCHECK_GE(yield_required_lat_gap(Discomforts::kMax), 0);
  CheckUniqueConstraintId(constraint_id);

  // OverlapRegion segment must be valid.
  DCHECK_LE(0, start_idx);
  DCHECK_LT(start_idx, end_idx);
  DCHECK_LE(end_idx, overlap_region.overlap_slices().size());

  // Create the constraint SPEED_OBJECT with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::SPEED_OBJECT, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, c_traj_id,
      GetOverlapRegionUniqueId(overlap_region));
  spin_lock_.clear(std::memory_order_release);
  constraint.settings.pass_required_lat_gap = pass_required_lat_gap;
  constraint.settings.yield_required_lat_gap = yield_required_lat_gap;

  // Create constraint states from overlap region.
  if (use_discomfort_varying_overlap) {
    DCHECK(overlap_computer_ptr_ != nullptr);

    constraint.states = ComputeReqLatGapVarStatesFromOverlapV2RegionSegment(
        overlap_region, pass_required_lat_gap, yield_required_lat_gap,
        start_idx, end_idx);
    constraint.is_using_rlg_var_states = true;
  } else {
    // Create non varying constraint states. This adds states from padded
    // overlap with required_lat_gap at min discomfort.
    constraint.states = ComputeSpeedConstraintStatesFromOverlapRegionSegment(
        overlap_region, start_idx, end_idx);
  }
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromOverlapRegion(
    const pb::FenceType fence_type, const pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    const DiscomfortVarying& required_lat_gap, int64_t obj_id,
    const std::string& c_traj_id, bool use_discomfort_varying_overlap) {
  return AddSpeedConstraintFromOverlapRegion(
      fence_type, reasoner_id, constraint_id, overlap_region, required_lat_gap,
      required_lat_gap, obj_id, c_traj_id, use_discomfort_varying_overlap);
}

ConstraintMutator ConstraintCreator::AddSpeedConstraintFromOverlapRegion(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    DiscomfortVarying pass_required_lat_gap,
    DiscomfortVarying yield_required_lat_gap, int64_t obj_id,
    const std::string& c_traj_id, bool use_discomfort_varying_overlap) {
  return AddSpeedConstraintFromOverlapRegionSegment(
      fence_type, reasoner_id, constraint_id, overlap_region,
      std::move(pass_required_lat_gap), std::move(yield_required_lat_gap),
      obj_id, c_traj_id, use_discomfort_varying_overlap, /*start_idx=*/0,
      /*end_idx=*/overlap_region.overlap_slices().size());
}

ConstraintMutator
ConstraintCreator::AddYieldStrictSpeedConstraintFromOverlapRegion(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    DiscomfortVarying pass_required_lat_gap, int64_t obj_id,
    const std::string& c_traj_id, const bool allow_zero_pass_rlg,
    const bool use_rlg_varying_overlap) {
  // Any SPEED_OBJECT constraint must be associated with an agent.
  DCHECK_GT(obj_id, 0);
  DCHECK(pass_required_lat_gap.IsNonIncreasing());
  if (!allow_zero_pass_rlg) {
    DCHECK_GT(pass_required_lat_gap(0.0), 0.0);
  }
  DCHECK_GE(overlap_region.overlap_slices().size(), 1);
  CheckUniqueConstraintId(constraint_id);

  // Create the constraint SPEED_OBJECT with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::SPEED_OBJECT, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, c_traj_id,
      GetOverlapRegionUniqueId(overlap_region));
  spin_lock_.clear(std::memory_order_release);
  // partially strict means we have zero required_lat_gap on yield side and
  // non-zero requried_lat_gap on pass side, unless the |allow_zero_pass_rlg| is
  // being explicitly set.
  constraint.settings.pass_required_lat_gap = pass_required_lat_gap;
  constraint.settings.yield_required_lat_gap = DiscomfortVarying{0.0};
  // TODO(speed): we do not need to compute RLG varying overlap for yield side.
  if (use_rlg_varying_overlap) {
    DCHECK(overlap_computer_ptr_ != nullptr);

    constraint.states = ComputeReqLatGapVarStatesFromOverlapV2RegionSegment(
        overlap_region, pass_required_lat_gap,
        /*yield_req_lat_gap=*/DiscomfortVarying{0.0},
        /*start_idx=*/0, /*end_idx=*/overlap_region.overlap_slices_size());
    constraint.is_using_rlg_var_states = true;
  } else {
    constraint.states =
        ComputePartialStrictSpeedConstraintStatesFromOverlapRegion(
            overlap_region);
  }

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddGapAlignSpeedConstraintFromOverlapRegion(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    int64_t obj_id, const std::string& c_traj_id, const GapAlignType type,
    const bool should_consider_in_seed_population) {
  DCHECK_GT(obj_id, 0);
  DCHECK_GE(overlap_region.overlap_slices().size(), 1);
  DCHECK(type != pb::GapAlignType::kNA);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint GAP_ALIGN with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::GAP_ALIGN, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, c_traj_id,
      GetOverlapRegionUniqueId(overlap_region));
  spin_lock_.clear(std::memory_order_release);
  // GAP_ALIGN constraint conceptually has infinity required lateral gap (aka.
  // in solver, we always consider all constraint states for gap align).
  // Users can still explicitly set required lateral gap if needed.
  const DiscomfortVarying required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint.settings.pass_required_lat_gap = required_lat_gap;
  constraint.settings.yield_required_lat_gap = required_lat_gap;
  // All GAP_ALIGN constraints are by default set to have IF_POSSIBLE pass/yield
  // option.
  constraint.settings.pass_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.yield_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.gap_align_source_info.type = type;
  constraint.settings.gap_align_source_info.should_consider_in_seed_population =
      should_consider_in_seed_population;
  // By default, we do not allow pass ignore for gap align constraint.
  constraint.settings.allow_pass_ignore_starting_at_discomfort =
      std::numeric_limits<double>::infinity();
  constraint.states =
      ComputeSpeedConstraintStatesFromOverlapRegion(overlap_region);
  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddGapAlignSpeedConstraintFromConstSpeed(
    double start_time, double ra_start_pos, double longitudinal_speed,
    double dt, int num_states, double agent_length, pb::FenceType fence_type,
    pb::ReasonerId reasoner_id, const std::string& constraint_id,
    int64_t obj_id, const GapAlignType type,
    const bool should_consider_in_seed_population) {
  DCHECK_GT(obj_id, 0);
  DCHECK(type != pb::GapAlignType::kNA);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint GAP_ALIGN with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::GAP_ALIGN, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, /*c_traj_id=*/"const-speed");
  spin_lock_.clear(std::memory_order_release);
  // GAP_ALIGN constraint conceptually has infinity required lateral gap.
  // Users can still explicitly set required lateral gap if needed.
  constraint.settings.pass_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint.settings.yield_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  // All GAP_ALIGN constraints are by default set to have IF_POSSIBLE pass/yield
  // option.
  constraint.settings.pass_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.yield_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.gap_align_source_info.type = type;
  constraint.settings.gap_align_source_info.should_consider_in_seed_population =
      should_consider_in_seed_population;
  // By default, we do not allow pass ignore for gap align constraint.
  constraint.settings.allow_pass_ignore_starting_at_discomfort =
      std::numeric_limits<double>::infinity();

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstSpeed(
      start_time, ra_start_pos, longitudinal_speed, dt, num_states,
      agent_length, leading_bumper_offset_from_ra_,
      trailing_bumper_offset_from_ra_, mutable_constraint_states);

  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddGapAlignSpeedConstraintFromConstAccel(
    double start_time, double ra_start_pos, double initial_longitudinal_speed,
    double dt, int num_states, double agent_length, double acceleration,
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, int64_t obj_id, const GapAlignType type,
    const bool should_consider_in_seed_population) {
  DCHECK_GT(obj_id, 0);
  DCHECK(type != pb::GapAlignType::kNA);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint GAP_ALIGN with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::GAP_ALIGN, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id);
  spin_lock_.clear(std::memory_order_release);
  // GAP_ALIGN constraint conceptually has infinity required lateral gap.
  // Users can still explicitly set required lateral gap if needed.
  constraint.settings.pass_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  constraint.settings.yield_required_lat_gap =
      DiscomfortVarying(std::numeric_limits<double>::infinity());
  // All GAP_ALIGN constraints are by default set to have IF_POSSIBLE pass/yield
  // option.
  constraint.settings.pass_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.yield_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.gap_align_source_info.type = type;
  constraint.settings.gap_align_source_info.should_consider_in_seed_population =
      should_consider_in_seed_population;
  // By default, we do not allow pass ignore for gap align constraint.
  constraint.settings.allow_pass_ignore_starting_at_discomfort =
      std::numeric_limits<double>::infinity();

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstAccel(
      start_time, ra_start_pos,
      /*max_longitudinal_movement=*/std::numeric_limits<double>::infinity(),
      initial_longitudinal_speed,
      /*final_longitudinal_speed=*/0.0, dt, num_states, agent_length,
      acceleration, leading_bumper_offset_from_ra_,
      trailing_bumper_offset_from_ra_, mutable_constraint_states);

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddProximitySpeedConstraintFromOverlapRegion(
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, const pb::OverlapRegion& overlap_region,
    int64_t obj_id, const std::string& c_traj_id) {
  DCHECK_GT(obj_id, 0);
  DCHECK_GE(overlap_region.overlap_slices().size(), 1);
  CheckUniqueConstraintId(constraint_id);
  // Create the constraint with type PROXIMITY_SPEED with proper settings.
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::PROXIMITY_SPEED, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id, c_traj_id,
      GetOverlapRegionUniqueId(overlap_region));
  spin_lock_.clear(std::memory_order_release);

  // Proximity speed constraint has both pass and yield option being NA so will
  // be ignored in speed searcher.
  constraint.settings.pass_option = pb::ConstraintSettings::NA;
  constraint.settings.yield_option = pb::ConstraintSettings::NA;

  constraint.states =
      ComputeSpeedConstraintStatesFromOverlapRegion(overlap_region);

  return ConstraintMutator(&constraint);
}

ConstraintMutator ConstraintCreator::AddProximitySpeedConstraintFromConstAccel(
    double start_time, double ra_start_pos, double max_longitudinal_movement,
    double initial_longitudinal_speed, double final_longitudinal_speed,
    double dt, int num_states, double agent_length, double acceleration,
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, int64_t obj_id) {
  DCHECK_GT(obj_id, 0);
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::PROXIMITY_SPEED, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id);
  spin_lock_.clear(std::memory_order_release);

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstAccel(
      start_time, ra_start_pos, max_longitudinal_movement,
      initial_longitudinal_speed, final_longitudinal_speed, dt, num_states,
      agent_length, acceleration, leading_bumper_offset_from_ra_,
      trailing_bumper_offset_from_ra_, mutable_constraint_states);

  // Proximity speed constraint has both pass and yield option being NA so will
  // be ignored in speed searcher.
  constraint.settings.pass_option = pb::ConstraintSettings::NA;
  constraint.settings.yield_option = pb::ConstraintSettings::NA;

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddHorizonLengthProximitySpeedConstraintFromConstSpeed(
    double ra_start_pos, double longitudinal_speed, double agent_length,
    pb::FenceType fence_type, pb::ReasonerId reasoner_id,
    const std::string& constraint_id, int64_t obj_id) {
  DCHECK_GT(obj_id, 0);
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::PROXIMITY_SPEED, fence_type, ReasonerId_Name(reasoner_id),
      constraint_id, obj_id);
  spin_lock_.clear(std::memory_order_release);

  std::vector<ConstraintState>& mutable_constraint_states = constraint.states;
  AddConstraintStatesFromConstSpeed(
      /*start_time=*/0.0, ra_start_pos, longitudinal_speed,
      constants::PlannerDt(), constants::PlannerHorizonStateNum(), agent_length,
      leading_bumper_offset_from_ra_, trailing_bumper_offset_from_ra_,
      mutable_constraint_states);

  // Proximity speed constraint has both pass and yield option being NA so will
  // be ignored in speed searcher.
  constraint.settings.pass_option = pb::ConstraintSettings::NA;
  constraint.settings.yield_option = pb::ConstraintSettings::NA;

  return ConstraintMutator(&constraint);
}

ConstraintMutator
ConstraintCreator::AddGapAlignSpeedConstraintFromSTObstacleContiguousRegions(
    const path::ObjectReasoningInfo& object_reasoning_info,
    const ::planner::pb::STObstacleContiguousRegions& regions,
    const math::geometry::PolylineCurve2d& reference_path,
    const ::planner::pb::AgentReasonerId& agent_reasoner_id,
    int blocking_sequence_index, bool is_static, bool allow_pass_ignore,
    const GapAlignType type, const bool should_consider_in_seed_population,
    std::optional<std::string> customized_constraint_id) {
  const TypedObjectId object_id = object_reasoning_info.object_id();
  DCHECK_GT(object_id.id, 0);
  DCHECK(type != pb::GapAlignType::kNA);
  const std::string& reasoner_id =
      planner::pb::AgentReasonerId_Name(agent_reasoner_id);
  // set unique constraint, traj and overlap ids for this agent.
  const std::string blocking_state_name =
      planner::pb::AgentSnapshotBlockingState_Name(regions.blocking_state());
  const std::string constraint_id =
      customized_constraint_id
          ? *customized_constraint_id
          : absl::StrCat(object_id.ToString(), "_", blocking_sequence_index,
                         "_", blocking_state_name, "_", reasoner_id);
  CheckUniqueConstraintId(constraint_id);
  while (spin_lock_.test_and_set(std::memory_order_acquire)) continue;
  Constraint& constraint = constraints_->emplace_back(
      pb::Constraint::GAP_ALIGN, pb::FenceType::kFromSTPlanner, reasoner_id,
      constraint_id, object_id.id);
  spin_lock_.clear(std::memory_order_release);
  // All GAP_ALIGN constraints are by default set to have IF_POSSIBLE pass/yield
  // option.
  constraint.settings.pass_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.yield_option = pb::ConstraintSettings::IF_POSSIBLE;
  constraint.settings.gap_align_source_info.type = type;
  constraint.settings.gap_align_source_info.should_consider_in_seed_population =
      should_consider_in_seed_population;
  constraint.settings.allow_pass_ignore_starting_at_discomfort =
      allow_pass_ignore ? Discomforts::kMin
                        : std::numeric_limits<double>::infinity();

  constraint.states =
      ComputeSpeedConstraintStatesFromSTObstacleContiguousRegions(
          object_reasoning_info, regions, reference_path, is_static);
  return ConstraintMutator(&constraint);
}

}  // namespace speed
}  // namespace planner
