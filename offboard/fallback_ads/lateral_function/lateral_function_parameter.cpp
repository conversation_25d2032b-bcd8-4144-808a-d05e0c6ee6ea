#include "lateral_function/lateral_function_parameter.h"
#include "common_math_library/interpolation.h"

// maximum required lane width for lane centering
const volatile float kLatMinLaneWidth = 2.65F;
// maximum required lane width for lane centering
const volatile float kLatMaxLaneWidth = 4.3F;
// hysteresis for lane width check
const volatile float kLatLaneWidthHys = 0.14F;
// minimum distance to lane
const volatile float kLatMinDistanceToLane = 1.0F;
// hysteresis for minimum distance to lane check
const volatile float kLatMinDistanceToLaneHys = 0.1F;
// minimum curvature radius for lane centering
const volatile float kLatMinLaneRadius = 100.0F;
// hysteresis for minimum curvature radius check
const volatile float kLatMinLaneRadiusHys = 150.0F;
// maximum allowed lane heading angle for lane centering
const volatile float kLatMaxLaneHeading = 0.1F;
// minimum required lane length for lane centering
const volatile float kLatMinLaneLength = 15.0F;
// hysteresis for minimum lane length check
const volatile float kLatMinLaneLengthHys = 5.0F;
// max lateral velocity towards the left lane
const volatile float kLatMaxLateralVelocityTowardsLane = 1.0F;
// max time to lane crossing
const volatile float kLatMaxTimeToLaneCrossing = 1.0F;
// lane width threshold for lane merge detection
const volatile float kLatLaneWidthForLaneMerge = 2.0F;
// control cost for lqr controller
const volatile float kLatControlCost = 2.0F;
// tolerance for lqr controller
const volatile float kLatLqrTolerance = 1e-2F;
// maximum number Of iterations for lqr controller
const volatile uint16_t kLatLqrMaxNumIteration = 100;

const volatile CML_Table1D
    tLatGainForQ11StateCost[LAT_GAIN_FOR_Q11_STATE_COST_TABLE_ROWS] = {
        {4.0F, 1.0F},  {8.0F, 0.6F},   {12.0F, 0.2F},
        {20.0F, 0.1F}, {25.0F, 0.05F},
};

const volatile CML_Table1D
    tLatGainForQ33StateCost[LAT_GAIN_FOR_Q33_STATE_COST_TABLE_ROWS] = {
        {4.0F, 1.0F},  {8.0F, 0.6F},   {12.0F, 0.2F},
        {20.0F, 0.1F}, {25.0F, 0.05F},
};

const volatile CML_Table1D tLatMaxWheelAngle[LAT_MAX_WHEEL_ANGLE_TABLE_ROWS] = {
    {0.0F, 35.0F},  {2.0F, 35.0F},  {3.0F, 35.0F},  {8.0F, 35.0F},
    {10.0F, 35.0F}, {20.0F, 25.0F}, {30.0F, 15.6F}, {40.0F, 3.0F},
    {50.0F, 2.0F},  {60.0F, 1.7F},
};

// For velocity < 40 kph, lateral jerk is limited to about 7.0 m/sss;
// For velocity >= 40 kph, lateral jerk is limited to about 9.0 m/sss;
const volatile CML_Table1D
    tLatMaxWheelAngleRate[LAT_MAX_WHEEL_ANGLE_RATE_TABLE_ROWS] = {
        {0.0F, 35.0F},  {2.0F, 35.0F},  {3.0F, 35.0F},  {8.0F, 35.0F},
        {10.0F, 35.0F}, {20.0F, 35.0F}, {30.0F, 17.2F}, {40.0F, 12.4F},
        {50.0F, 7.9F},  {60.0F, 5.5F},
};

const volatile CML_Table1D
    tScaleGainForQCostRatio[SCALE_GAIN_FOR_Q_COST_RATIO_TABLE_ROWS] = {
        {0.0F, 0.025F}, {5.0F, 0.025F},  {15.0F, 0.015F},
        {50.0F, 0.01F}, {100.0F, 0.01F},
};

float GetLatMinLaneWidth() { return kLatMinLaneWidth; }

float GetLatMaxLaneWidth() { return kLatMaxLaneWidth; }

float GetLatLaneWidthHys() { return kLatLaneWidthHys; }

float GetLatMinDistanceToLane() { return kLatMinDistanceToLane; }

float GetLatMinDistanceToLaneHys() { return kLatMinDistanceToLaneHys; }

float GetLatMinLaneRadius() { return kLatMinLaneRadius; }

float GetLatMinLaneRadiusHys() { return kLatMinLaneRadiusHys; }

float GetLatMaxLaneHeading() { return kLatMaxLaneHeading; }

float GetLatMinLaneLength() { return kLatMinLaneLength; }

float GetLatMinLaneLengthHys() { return kLatMinLaneLengthHys; }

float GetLatMaxLateralVelocityTowardsLane() {
  return kLatMaxLateralVelocityTowardsLane;
}

float GetLatMaxTimeToLaneCrossing() { return kLatMaxTimeToLaneCrossing; }

float GetLatLaneWidthForLaneMerge() { return kLatLaneWidthForLaneMerge; }

float GetLatControlCost() { return kLatControlCost; }

float GetLatLqrTolerance() { return kLatLqrTolerance; }

uint16_t GetLatLqrMaxNumIteration() { return kLatLqrMaxNumIteration; }

const CML_Table1D* GetLatGainForQ11StateSpaceTable() {
  return (const CML_Table1D*)tLatGainForQ11StateCost;
}

const CML_Table1D* GetLatGainForQ33StateSpaceTable() {
  return (const CML_Table1D*)tLatGainForQ33StateCost;
}

const CML_Table1D* GetLatMaxWheelAngleTable() {
  return (const CML_Table1D*)tLatMaxWheelAngle;
}

const CML_Table1D* GetLatMaxWheelAngleRateTable() {
  return (const CML_Table1D*)tLatMaxWheelAngleRate;
}

const CML_Table1D* GetScaleGainForQCostRatioTable() {
  return (const CML_Table1D*)tScaleGainForQCostRatio;
}
