#ifndef ONBOARD_REALTIME_UPLOADER_COMMON_TOPIC_INFO_HPP_
#define ONBOARD_REALTIME_UPLOADER_COMMON_TOPIC_INFO_HPP_

#include <string>
#include <unordered_set>

#include <av_comm/onboard_config.h>

#include "realtime_uploader/common/types.hpp"

namespace realtime_uploader {
const std::unordered_set<std::string> kTopicDefaultBox1 = {
    "/health/regulator_node",
    "/health/xavier1_network_monitor",
    "/health/sensing_node",
    "/health/multi_relay",
    "/order_service",
    "/v2x/traffic_light_detect",
    "/vehicle/body_info",
    "/health/ntb_bridge",
    "/converted_raw_gps",
    "/health/network_monitor",
    "/action_factory/can_communication_record",
    "/health/recorder_node",
    "/realtime_map/update_data",
    "/health/xavier1_system_control",
    "/health/order_service_v2_node",
    "/health/ntrip_client_node",
    "/health/side_lidar",
    "/health/xavier2_network_monitor",
    "/health/localization_node",
    "/perception/lidar_object_list",
    "/perception/lidar_detected_list",
    "/system_state",
    "/health/main_lidar",
    "/health/gnss_driver_node",
    "/health/tracking_node",
    "/shape_stream",
    "/realtime_map/traffic_lights",
    "/mapping/map_change_area_list",
    "/health/xavier1_custom_recorder_node",
    "/health/service_restore",
    "/control",
    "/control/seed",
    "/restore_record",
    "/health/lidar",
    "/health/onboard_v2x",
    "/health/infrared_camera",
    "/converted_gps_imu",
    "/health/xavier2_custom_recorder_node",
    "/realtime_map/construction_zone_list",
    "/perception/processed_map_detection_list",
    "/health/canbus_node",
    "/health/laptop_network_monitor",
    "/network_info",
    "/health/gpu_monitor",
    "/health/aurix_driver",
    "/health/storage_monitor",
    "/health/robotaxi_device_control",
    "/health/cpubox1_custom_recorder_node",
    "/health/gmsl_camera_xavier1",
    "/health/system_control",
    "/health/gmsl_camera_xavier2",
    "/perception/lidar_detected_curb_list",
    "/health/control_node",
    "/health/gmsl_infrared_camera",
    "/ads_event",
    "/health/xavier2_system_control",
    "/realtime_map/map_change_area_list",
    "/health/vehicle_node",
    "/vehicle/assist_info"};

const std::unordered_set<std::string> kTopicAutonomyBox1 = {
    "/traffic_light_detect",
    "/routing/route_command",
    "/pose",
    "/obstacle_list",
    "/perception/collision_detection",
    "/route/request",
    "/gateway",
    "/routing/planning_route_state",
    "/routing/state_for_sim",
    "/perception/construction_zone_list",
    "/routing/planner_route_command",
    "/perception/tracked_object_list",
    "/routing/query",
    "/perception/scene_understanding",
    "/routing/route_status",
    "/routing/seed"};

// TODO(Tianyiliu): add topics for gen4
const std::unordered_set<std::string> kTopicLidarBox1 = {
    "/gen3_main_lidar_slice_scan_udp_packet"};

const std::unordered_set<std::string> kTopicDefaultBox2 = {
    "/planning/customer_monitor_visual",
    "/health/cpubox2_system_control",
    "/health/planner_node",
    "/health/routing_node",
    "/health/prediction_node",
    "/health/xavier3_system_control",
    "/health/xavier4_custom_recorder_node",
    "/health/xavier4_system_control",
    "/health/gmsl_camera_xavier3",
    "/health/gmsl_camera_xavier4",
    "/health/can_inspector_node",
    "/health/cpubox2_ntb_bridge",
    "/health/cpubox2_gpu_monitor",
    "/health/conti430_radar_node",
    "/health/inference_server_node",
    "/health/fallback_can_decode_node",
    "/health/fallback_level1_control_node",
    "/health/fallback_planning",
    "/health/fallback_level1",
    "/health/fallback_miira",
    "/health/restore_network_detector",
    "/health/mrc_node",
    "/health/exception_handler_node",
    "/fallback_level1",
    "/fallback_level1_control",
    "/fallback_level1_control/control_debug",
    "/fallback_level1_control/seed",
    "/fallback_level2",
    "/fallback_camera",
    "/fallback_radar",
    "/fallback_chassis_xc90",
    "/fallback_level2/chassis",
    "/fallback_level2/camera",
    "/fallback_level2/radar",
    "/fallback_planning_trajectory",
    "/planning/fault_detector_response",
    "/planning/mrc_immediate_pullover"};

const std::unordered_set<std::string> kTopicAutonomyBox2 = {
    "/prediction/predicted_objects",
    "/planning/trajectory",
    "/planning/planning_lane_sequence",
    "/planning/assist_response",
    "/planning/seed",
    "/planning/snapshot_seed",
    "/trajectory_guider/guide_trajectory",
    "/planning/message_metadata_input",
    "/planning/message_metadata",
    "/planning/pullout_request",
    "/planning/stuck_detection_recall_signal",
    "/planning/remote_warning_signal",
    "/planning/assist_request",
    "/planner/state",
    "/exception_handler/global_init_state",
    "/exception_handler/global_selection_state",
    "/exception_handler/global_selection_seed",
    "/exception_handler/trajectory"};

const std::unordered_set<std::string> kTopicCameraBox1 = {
    "/camera_video_frame_102", "/camera_video_frame_103",
    "/camera_video_frame_107"};

const std::unordered_set<std::string> kTopicDebugBox2 = {
    "/planning/planning_debug"};

const std::unordered_set<std::string> kEmptyTopics = {};

inline std::unordered_set<std::string> GetTopics(
    const onboard_config::AcuType &acu_type, const BagType bag_type) {
  if (acu_type == onboard_config::AcuType::CPUBOX1) {
    if (bag_type == BagType::DEFAULT) {
      return kTopicDefaultBox1;
    }
    if (bag_type == BagType::LIDAR) {
      return kTopicLidarBox1;
    }
    if (bag_type == BagType::AUTONOMY) {
      return kTopicAutonomyBox1;
    }
    if (bag_type == BagType::CAMERA) {
      return kTopicCameraBox1;
    }
    return kEmptyTopics;
  }
  if (acu_type == onboard_config::AcuType::CPUBOX2) {
    if (bag_type == BagType::DEFAULT) {
      return kTopicDefaultBox2;
    }
    if (bag_type == BagType::DEBUG) {
      return kTopicDebugBox2;
    }
    if (bag_type == BagType::AUTONOMY) {
      return kTopicAutonomyBox2;
    }
    return kEmptyTopics;
  }
  if (acu_type == onboard_config::AcuType::GEN4X86) {
    std::unordered_set<std::string> result;
    if (bag_type == BagType::DEFAULT) {
      result.insert(kTopicDefaultBox1.begin(), kTopicDefaultBox1.end());
      result.insert(kTopicDefaultBox2.begin(), kTopicDefaultBox2.end());
    }
    if (bag_type == BagType::CAMERA) {
      result.insert(kTopicCameraBox1.begin(), kTopicCameraBox1.end());
    }
    if (bag_type == BagType::DEBUG) {
      result.insert(kTopicDebugBox2.begin(), kTopicDebugBox2.end());
    }
    if (bag_type == BagType::AUTONOMY) {
      result.insert(kTopicAutonomyBox1.begin(), kTopicAutonomyBox1.end());
      result.insert(kTopicAutonomyBox2.begin(), kTopicAutonomyBox2.end());
    }
    return result;
  }
  return kEmptyTopics;
}

}  // namespace realtime_uploader

#endif  // ONBOARD_REALTIME_UPLOADER_COMMON_TOPIC_INFO_HPP_
