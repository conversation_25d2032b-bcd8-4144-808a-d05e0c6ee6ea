#include "av_comm/node_names.h"
#include "av_comm/onboard_config.h"
#include "gnss_driver_node.h"
#include "node/breakpad_exception_handler.h"
#include "node/log_context.h"
#include "node/node_runner.h"
#include "varch/vnode/detail/fastdds_log/fastdds_log.h"

namespace gnss {
std::string GetNodeName() {
  return av_comm::IsGen4Platform() ? av_comm::node_name::kDeviceGnssOrin1
                                   : av_comm::node_name::kGnssDriver;
}
}  // namespace gnss

int main(int argc, char** argv) {
  google::ParseCommandLineFlags(&argc, &argv, true /* remove_flags */);
  ros::init(argc, argv, gnss::GetNodeName(),
            ros::init_options::NoSigintHandler);
  node::LogContext log_context(ros::this_node::getName().c_str());
  varch::vnode::detail::ddslog::InitFastDDSLog(FLAGS_log_dir,
                                               gnss::GetNodeName());
  node::BreakpadExceptionHandler eh(ros::this_node::getName());

  const bool is_tnu_coexist = av_comm::UseTnuGnssWhenCoexist();
  node::NodeRunner node_runner(/*num_threads=*/1,
                               /*report_health=*/!is_tnu_coexist);
  gnss::GnssDriverNode gnss_driver_node(node_runner.io_service());
  return node_runner.Run().value();
}
