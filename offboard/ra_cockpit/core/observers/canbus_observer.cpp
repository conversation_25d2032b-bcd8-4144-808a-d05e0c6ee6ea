#include "canbus_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "base/now.h"
#include "core/dotting.h"
#include "core/global_state.h"
#include "glog/logging.h"

namespace remote_assist {

CanbusObserver::CanbusObserver(CanbusCallback callback)
    : callback_(std::move(callback)),
      kManualModeSet{voy::VehicleMode::AUTO_FULL,
                     voy::VehicleMode::EMERGENCY,
                     voy::VehicleMode::FALLBACK_ADAS_CONTROL,
                     voy::VehicleMode::FALLBACK_AUTONOMY_CONTROL,
                     voy::VehicleMode::FALLBACK_CONTROL,
                     voy::VehicleMode::REMOTE_CONTROL,
                     voy::VehicleMode::TRANSITION} {}

bool CanbusObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kCanbusMessage);
}

std::shared_ptr<google::protobuf::Message> CanbusObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto canbus_msg = std::make_shared<voy::Canbus>();
  if (!canbus_msg->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error Canbus message format, len = "
               << message.message_data().length();
    return nullptr;
  }

  // check if received msg exceeds kMsgTimeoutMs after binding car
  CheckMsgTimeout();

  SetIsManual(canbus_msg);
  if (callback_) {
    callback_(canbus_msg, data_channel);
  }
  return canbus_msg;
}

void CanbusObserver::CheckMsgTimeout() {
  int64_t driverless_sign_in_time = gGlobalState.GetDriverlessSignInTime();
  int64_t first_receive_timestamp =
      gGlobalState.GetDriverlessFirstMsgReceiveTime();

  if (driverless_sign_in_time == 0) return;

  if (first_receive_timestamp == 0) {
    first_receive_timestamp = base::Now();
    gGlobalState.SetDriverlessFirstMsgReceiveTime(first_receive_timestamp);
  }
  int64_t time_gap = first_receive_timestamp - driverless_sign_in_time;
  if (time_gap > kMsgTimeoutMs) {
    gDotting.DottingKV(
        "ra_waypoint_abnormal", "ra_waypoint_abnormal",
        {{"status", 0},
         {"reason", "msg timeout: " + std::to_string(time_gap)}});
    gGlobalState.SetDriverlessSignInTime(0);
    gGlobalState.SetDriverlessFirstMsgReceiveTime(0);
  }
}

void CanbusObserver::SetIsManual(const std::shared_ptr<voy::Canbus>& canbus) {
  auto mode = canbus->mode();
  gGlobalState.SetIsManual(kManualModeSet.count(mode) == 0);
}

}  // namespace remote_assist
