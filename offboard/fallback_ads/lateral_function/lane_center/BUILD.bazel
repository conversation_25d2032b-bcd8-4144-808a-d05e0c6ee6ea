package(default_visibility = ["//visibility:public"])

cc_library(
    name = "check_lane_center",
    srcs = [
        "check_lane_center.c",
    ],
    hdrs = [
        "check_lane_center.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/lane_center/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:debounce_method",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
        "//offboard/fallback_ads/lateral_function/boundary_data_processing:check_lane_property",
        "//offboard/fallback_ads/lateral_function/lane_center:lane_departure",
    ],
)

cc_library(
    name = "lane_departure",
    srcs = [
        "lane_departure.c",
    ],
    hdrs = [
        "lane_departure.h",
    ],
    copts = [
        "-x",
        "c",
        "-std=c11",
    ],
    include_prefix = "lateral_function/lane_center/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_lateral_function_type",
        "//offboard/fallback_ads/common_math_library:common_method",
        "//offboard/fallback_ads/common_math_library:vehicle_parameters",
        "//offboard/fallback_ads/lateral_function:lateral_function_parameter",
    ],
)
