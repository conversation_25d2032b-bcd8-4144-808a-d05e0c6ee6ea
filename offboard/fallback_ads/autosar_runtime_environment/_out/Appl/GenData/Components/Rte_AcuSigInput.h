/**********************************************************************************************************************
 *  COPYRIGHT
 *  -------------------------------------------------------------------------------------------------------------------
 *  \verbatim
 *
 *                This software is copyright protected and proprietary to Vector Informatik GmbH.
 *                Vector Informatik GmbH grants to you only those rights as set out in the license conditions.
 *                All other rights remain with Vector Informatik GmbH.
 *  \endverbatim
 *  -------------------------------------------------------------------------------------------------------------------
 *  FILE DESCRIPTION
 *  -------------------------------------------------------------------------------------------------------------------
 *             File:  Rte_AcuSigInput.h
 *           Config:  DiDi_FBU.dpa
 *      ECU-Project:  DiDi_FBU
 *
 *        Generator:  MICROSAR RTE Generator Version 4.27.0
 *                    RTE Core Version 1.27.0
 *          License:  CBD2100894
 *
 *      Description:  Application header file for SW-C <AcuSigInput>
 *********************************************************************************************************************/

/* double include prevention */
#ifndef RTE_ACUSIGINPUT_H
# define RTE_ACUSIGINPUT_H

# ifndef RTE_CORE
#  ifdef RTE_APPLICATION_HEADER_FILE
#   error Multiple application header files included.
#  endif
#  define RTE_APPLICATION_HEADER_FILE
#  ifndef RTE_PTR2ARRAYBASETYPE_PASSING
#   define RTE_PTR2ARRAYBASETYPE_PASSING
#  endif
# endif

# ifdef __cplusplus
extern "C"
{
# endif /* __cplusplus */

/* include files */

# include "_out/Appl/GenData/Components/Rte_AcuSigInput_Type.h"
# include "_out/Appl/GenData/Rte_DataHandleType.h"


# ifndef RTE_CORE
/**********************************************************************************************************************
 * Init Values for unqueued S/R communication (primitive types only)
 *********************************************************************************************************************/

#  define Rte_InitValue_AcuPose_AcuPoseRollingCounter (0U)
#  define Rte_InitValue_AcuPose_GnssPoseHeading (0)
#  define Rte_InitValue_AcuPose_GnssPoseHeadingType (0U)
#  define Rte_InitValue_AcuPose_GnssPoseNumSatsTracked (0U)
#  define Rte_InitValue_AcuPose_GnssPosePosType (0U)
#  define Rte_InitValue_AcuPose_GnssPosePositionX (0)
#  define Rte_InitValue_AcuPose_GnssPosePositionY (0)
#  define Rte_InitValue_AcuPose_GnssPoseReserve1 (0)
#  define Rte_InitValue_AcuPose_GnssPoseVelocityX (0)
#  define Rte_InitValue_AcuPose_GnssPoseVelocityY (0)
#  define Rte_InitValue_AcuPose_GnssPoseYaw (0)
#  define Rte_InitValue_AcuPose_GnsssPoseReserve0 (0U)
#  define Rte_InitValue_AcuPose_PoseAccelerationX (0)
#  define Rte_InitValue_AcuPose_PoseAccelerationY (0)
#  define Rte_InitValue_AcuPose_PosePitch (0)
#  define Rte_InitValue_AcuPose_PosePositionX (0)
#  define Rte_InitValue_AcuPose_PosePositionY (0)
#  define Rte_InitValue_AcuPose_PoseStatus (0U)
#  define Rte_InitValue_AcuPose_PoseVelocityX (0)
#  define Rte_InitValue_AcuPose_PoseVelocityY (0)
#  define Rte_InitValue_AcuPose_PoseYaw (0)
#  define Rte_InitValue_AcuPose_PoseYawRate (0)
#  define Rte_InitValue_AcuTrajectoryInfo_ControlWheelAngleOffset (0)
#  define Rte_InitValue_AcuTrajectoryInfo_EgoLaneLightColor (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_FrontObjectTTC (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_FrontObjectValid (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_IsInTheHighway (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_LeftRoadEdgeLatDist (0)
#  define Rte_InitValue_AcuTrajectoryInfo_LeftRoadEdgeLength (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_LeftRoadEdgeValid (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_RearObjectTTC (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_RearObjectValid (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_RightRoadEdgeLatDist (0)
#  define Rte_InitValue_AcuTrajectoryInfo_RightRoadEdgeLength (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_RightRoadEdgeValid (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_StopLineLeftEdgeX (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_StopLineLeftEdgeY (0)
#  define Rte_InitValue_AcuTrajectoryInfo_StopLineRightEdgeX (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_StopLineRightEdgeY (0)
#  define Rte_InitValue_AcuTrajectoryInfo_StopLineValid (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoHazardLight (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoHorn (FALSE)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoLaneChange (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoMotion (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoPlanningStatus (0U)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve0 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve1 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve2 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve3 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve4 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve5 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoReserve6 (0)
#  define Rte_InitValue_AcuTrajectoryInfo_TrajectoryInfoRollingCounter (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose00Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose01Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose02Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose00To03_TrajectoryPose03Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose04Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose05Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose06Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose04To07_TrajectoryPose07Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose08Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose09Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose10Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose08To11_TrajectoryPose11Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose12Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose13Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose14Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose12To15_TrajectoryPose15Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose16Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose17Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose18Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose16To19_TrajectoryPose19Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose20Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose21Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose22Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose20To23_TrajectoryPose23Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose24Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose25Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose26Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose24To27_TrajectoryPose27Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose28Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose29Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose30Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose28To31_TrajectoryPose31Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose32Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose33Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose34Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose32To35_TrajectoryPose35Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose36Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose37Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose38Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose36To39_TrajectoryPose39Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose40Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose41Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose42Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose40To43_TrajectoryPose43Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose44Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose45Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose46Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose44To47_TrajectoryPose47Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose48Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose49Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose50Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose48To51_TrajectoryPose51Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose52Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose53Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose54Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose52To55_TrajectoryPose55Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose56Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose57Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose58Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose56To59_TrajectoryPose59Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose60Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose61Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose62Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose60To63_TrajectoryPose63Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose64Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose65Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose66Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose64To67_TrajectoryPose67Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose68Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose69Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose70Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose68To71_TrajectoryPose71Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose72Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose73Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose74Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose72To75_TrajectoryPose75Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose76Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose77Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose78Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose76To79_TrajectoryPose79Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose80Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose81Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose82Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose80To83_TrajectoryPose83Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose84Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose85Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose86Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose84To87_TrajectoryPose87Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose88Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose89Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose90Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose88To91_TrajectoryPose91Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose92Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose93Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose94Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose92To95_TrajectoryPose95Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose96Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose97Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose98Steering (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99Acceleration (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99Curvature (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99Heading (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99PositionX (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99PositionY (0)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99Speed (0U)
#  define Rte_InitValue_AcuTrajectoryPose96To99_TrajectoryPose99Steering (0)
#  define Rte_InitValue_Timestamp_Timestamp (0U)
# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Buffers for implicit communication
 *********************************************************************************************************************/
#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsOsTask_Init_Core0_APP, RTE_VAR_NOINIT) Rte_OsTask_Init_Core0_APP; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_10ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_10ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

#  define RTE_START_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

extern VAR(Rte_tsMainTask_Core0_60ms, RTE_VAR_NOINIT) Rte_MainTask_Core0_60ms; /* PRQA S 0759 */ /* MD_MSR_Union */

#  define RTE_STOP_SEC_VAR_NOINIT_UNSPECIFIED
#  include "_out/Appl/GenData/Rte_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */


# endif


# ifndef RTE_CORE

/**********************************************************************************************************************
 * Rte_IRead_<r>_<p>_<d>
 * Rte_IStatus_<r>_<p>_<d>
 * Rte_IFeedback_<r>_<p>_<d>
 * Rte_IWrite_<r>_<p>_<d>
 * Rte_IWriteRef_<r>_<p>_<d>
 * Rte_IInvalidate_<r>_<p>_<d>
 *********************************************************************************************************************/


#  define Rte_IWrite_AcuSigInput_Init_AcuControl_AcuControl(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_AcuControl_AcuControl.value = *(data) \
  )


#  define Rte_IWriteRef_AcuSigInput_Init_AcuControl_AcuControl() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_AcuControl_AcuControl.value \
  )


#  define Rte_IWrite_AcuSigInput_Init_GnssPose_GnssPose(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_GnssPose_GnssPose.value = *(data) \
  )


#  define Rte_IWriteRef_AcuSigInput_Init_GnssPose_GnssPose() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_GnssPose_GnssPose.value \
  )


#  define Rte_IWrite_AcuSigInput_Init_LocalizationPose_LocalizationPose(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_LocalizationPose_LocalizationPose.value = *(data) \
  )


#  define Rte_IWriteRef_AcuSigInput_Init_LocalizationPose_LocalizationPose() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_LocalizationPose_LocalizationPose.value \
  )


#  define Rte_IWrite_AcuSigInput_Init_Timestamp_Timestamp(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_Timestamp_Timestamp.value = (data) \
  )


#  define Rte_IWriteRef_AcuSigInput_Init_Timestamp_Timestamp() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_Timestamp_Timestamp.value \
  )


#  define Rte_IWrite_AcuSigInput_Init_Trajectory_Trajectory(data) \
  ( \
    Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_Trajectory_Trajectory.value = *(data) \
  )


#  define Rte_IWriteRef_AcuSigInput_Init_Trajectory_Trajectory() \
  ( \
    &Rte_OsTask_Init_Core0_APP.Rte_RB.Rte_AcuSigInput_AcuSigInput_Init.Rte_Trajectory_Trajectory.value \
  )


#  define Rte_IRead_PoseInput_10ms_AcuPose_AcuPoseRollingCounter() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_AcuPoseRollingCounter.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseHeading() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseHeading.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseHeadingType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseHeadingType.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseNumSatsTracked() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseNumSatsTracked.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePosType() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPosePosType.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePositionX() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPosePositionX.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPosePositionY() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPosePositionY.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseReserve1() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseReserve1.value)


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp() \
  (&((*RteIRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp())[0]))
#  else
#   define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp() \
  RteIRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp()
#  endif
#  define RteIRead_PoseInput_10ms_AcuPose_GnssPoseTimeStamp() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseTimeStamp.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseVelocityX() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseVelocityX.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseVelocityY() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseVelocityY.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnssPoseYaw() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnssPoseYaw.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_GnsssPoseReserve0() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_GnsssPoseReserve0.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseAccelerationX() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseAccelerationX.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseAccelerationY() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseAccelerationY.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PosePitch() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PosePitch.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PosePositionX() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PosePositionX.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PosePositionY() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PosePositionY.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseStatus() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseStatus.value)


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IRead_PoseInput_10ms_AcuPose_PoseTimeStamp() \
  (&((*RteIRead_PoseInput_10ms_AcuPose_PoseTimeStamp())[0]))
#  else
#   define Rte_IRead_PoseInput_10ms_AcuPose_PoseTimeStamp() \
  RteIRead_PoseInput_10ms_AcuPose_PoseTimeStamp()
#  endif
#  define RteIRead_PoseInput_10ms_AcuPose_PoseTimeStamp() \
  (&Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseTimeStamp.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseVelocityX() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseVelocityX.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseVelocityY() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseVelocityY.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseYaw() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseYaw.value)


#  define Rte_IRead_PoseInput_10ms_AcuPose_PoseYawRate() \
  (Rte_MainTask_Core0_10ms.Rte_RB.Rte_AcuSigInput_PoseInput_10ms.Rte_AcuPose_PoseYawRate.value)


#  define Rte_IWrite_PoseInput_10ms_GnssPose_GnssPose(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_GnssPose_GnssPose.value = *(data) \
  )


#  define Rte_IWriteRef_PoseInput_10ms_GnssPose_GnssPose() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_GnssPose_GnssPose.value \
  )


#  define Rte_IWrite_PoseInput_10ms_LocalizationPose_LocalizationPose(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_LocalizationPose_LocalizationPose.value = *(data) \
  )


#  define Rte_IWriteRef_PoseInput_10ms_LocalizationPose_LocalizationPose() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_LocalizationPose_LocalizationPose.value \
  )


#  define Rte_IWrite_PoseInput_10ms_Timestamp_Timestamp(data) \
  ( \
    Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_Timestamp_Timestamp.value = (data) \
  )


#  define Rte_IWriteRef_PoseInput_10ms_Timestamp_Timestamp() \
  ( \
    &Rte_MainTask_Core0_10ms.Rte_TB.Rte_I_AcuSigInput_Timestamp_Timestamp.value \
  )


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp() \
  (&((*RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp())[0]))
#  else
#   define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp() \
  RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp()
#  endif
#  define RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlTimestamp() \
  (&Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_ControlTimestamp.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_ControlWheelAngleOffset() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_ControlWheelAngleOffset.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_EgoLaneLightColor() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_EgoLaneLightColor.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_FrontObjectTTC() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_FrontObjectTTC.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_FrontObjectValid() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_FrontObjectValid.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_IsInTheHighway() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_IsInTheHighway.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_LeftRoadEdgeLatDist() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_LeftRoadEdgeLatDist.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_LeftRoadEdgeLength() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_LeftRoadEdgeLength.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_LeftRoadEdgeValid() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_LeftRoadEdgeValid.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_RearObjectTTC() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_RearObjectTTC.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_RearObjectValid() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_RearObjectValid.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_RightRoadEdgeLatDist() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_RightRoadEdgeLatDist.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_RightRoadEdgeLength() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_RightRoadEdgeLength.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_RightRoadEdgeValid() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_RightRoadEdgeValid.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_StopLineLeftEdgeX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_StopLineLeftEdgeX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_StopLineLeftEdgeY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_StopLineLeftEdgeY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_StopLineRightEdgeX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_StopLineRightEdgeX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_StopLineRightEdgeY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_StopLineRightEdgeY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_StopLineValid() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_StopLineValid.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoHazardLight() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoHazardLight.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoHorn() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoHorn.value)


#  ifndef RTE_PTR2ARRAYTYPE_PASSING
#   define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp() \
  (&((*RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp())[0]))
#  else
#   define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp() \
  RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp()
#  endif
#  define RteIRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp() \
  (&Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoLaneChange() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoLaneChange.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoMotion() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoMotion.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoPlanningStatus() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoPlanningStatus.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve0() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve0.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve1() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve1.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve2() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve2.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve3() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve3.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve4() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve4.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve5() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve5.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoReserve6() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve6.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryInfo_TrajectoryInfoRollingCounter() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryInfo_TrajectoryInfoRollingCounter.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose00Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose01Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose01Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose02Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose02Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose00To03_TrajectoryPose03Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose00To03_TrajectoryPose03Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose04Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose04Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose05Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose05Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose06Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose06Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose04To07_TrajectoryPose07Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose04To07_TrajectoryPose07Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose08Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose08Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose09Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose09Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose10Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose10Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose08To11_TrajectoryPose11Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose08To11_TrajectoryPose11Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose12Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose12Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose13Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose13Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose14Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose14Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose12To15_TrajectoryPose15Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose12To15_TrajectoryPose15Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose16Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose16Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose17Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose17Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose18Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose18Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose16To19_TrajectoryPose19Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose16To19_TrajectoryPose19Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose20Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose20Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose21Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose21Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose22Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose22Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose20To23_TrajectoryPose23Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose20To23_TrajectoryPose23Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose24Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose24Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose25Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose25Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose26Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose26Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose24To27_TrajectoryPose27Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose24To27_TrajectoryPose27Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose28Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose28Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose29Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose29Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose30Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose30Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose28To31_TrajectoryPose31Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose28To31_TrajectoryPose31Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose32Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose32Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose33Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose33Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose34Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose34Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose32To35_TrajectoryPose35Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose32To35_TrajectoryPose35Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose36Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose36Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose37Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose37Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose38Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose38Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose36To39_TrajectoryPose39Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose36To39_TrajectoryPose39Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose40Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose40Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose41Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose41Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose42Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose42Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose40To43_TrajectoryPose43Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose40To43_TrajectoryPose43Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose44Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose44Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose45Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose45Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose46Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose46Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose44To47_TrajectoryPose47Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose44To47_TrajectoryPose47Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose48Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose48Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose49Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose49Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose50Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose50Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose48To51_TrajectoryPose51Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose48To51_TrajectoryPose51Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose52Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose52Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose53Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose53Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose54Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose54Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose52To55_TrajectoryPose55Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose52To55_TrajectoryPose55Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose56Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose56Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose57Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose57Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose58Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose58Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose56To59_TrajectoryPose59Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose56To59_TrajectoryPose59Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose60Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose60Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose61Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose61Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose62Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose62Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose60To63_TrajectoryPose63Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose60To63_TrajectoryPose63Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose64Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose64Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose65Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose65Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose66Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose66Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose64To67_TrajectoryPose67Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose64To67_TrajectoryPose67Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose68Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose68Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose69Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose69Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose70Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose70Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose68To71_TrajectoryPose71Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose68To71_TrajectoryPose71Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose72Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose72Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose73Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose73Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose74Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose74Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose72To75_TrajectoryPose75Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose72To75_TrajectoryPose75Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose76Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose76Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose77Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose77Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose78Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose78Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose76To79_TrajectoryPose79Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose76To79_TrajectoryPose79Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose80Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose80Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose81Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose81Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose82Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose82Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose80To83_TrajectoryPose83Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose80To83_TrajectoryPose83Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose84Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose84Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose85Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose85Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose86Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose86Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose84To87_TrajectoryPose87Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose84To87_TrajectoryPose87Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose88Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose88Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose89Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose89Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose90Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose90Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose88To91_TrajectoryPose91Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose88To91_TrajectoryPose91Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose92Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose92Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose93Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose93Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose94Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose94Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose92To95_TrajectoryPose95Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose92To95_TrajectoryPose95Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose96Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose96Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose97Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose97Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose98Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose98Steering.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99Acceleration() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Acceleration.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99Curvature() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Curvature.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99Heading() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Heading.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99PositionX() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionX.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99PositionY() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionY.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99Speed() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Speed.value)


#  define Rte_IRead_TrajectoryInput_60ms_AcuTrajectoryPose96To99_TrajectoryPose99Steering() \
  (Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Steering.value)


#  define Rte_IWrite_TrajectoryInput_60ms_AcuControl_AcuControl(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuControl_AcuControl.value = *(data) \
  )


#  define Rte_IWriteRef_TrajectoryInput_60ms_AcuControl_AcuControl() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_AcuControl_AcuControl.value \
  )


#  define Rte_IWrite_TrajectoryInput_60ms_Trajectory_Trajectory(data) \
  ( \
    Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_Trajectory_Trajectory.value = *(data) \
  )


#  define Rte_IWriteRef_TrajectoryInput_60ms_Trajectory_Trajectory() \
  ( \
    &Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms.Rte_Trajectory_Trajectory.value \
  )


# endif /* !defined(RTE_CORE) */


# define AcuSigInput_START_SEC_CODE
// # include "AcuSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

/**********************************************************************************************************************
 * Runnable entities
 *********************************************************************************************************************/

# ifndef RTE_CORE
#  define RTE_RUNNABLE_AcuSigInput_Init AcuSigInput_Init
#  define RTE_RUNNABLE_PoseInput_10ms PoseInput_10ms
#  define RTE_RUNNABLE_TrajectoryInput_60ms TrajectoryInput_60ms
# endif

FUNC(void, AcuSigInput_CODE) AcuSigInput_Init(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, AcuSigInput_CODE) PoseInput_10ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */
FUNC(void, AcuSigInput_CODE) TrajectoryInput_60ms(void); /* PRQA S 3451, 0786, 3449 */ /* MD_Rte_3451, MD_Rte_0786, MD_Rte_3449 */

# define AcuSigInput_STOP_SEC_CODE
// # include "AcuSigInput_MemMap.h" /* PRQA S 5087 */ /* MD_MSR_MemMap */

# ifdef __cplusplus
} /* extern "C" */
# endif /* __cplusplus */

#endif /* RTE_ACUSIGINPUT_H */

/**********************************************************************************************************************
 MISRA 2012 violations and justifications
 *********************************************************************************************************************/

/* module specific MISRA deviations:
   MD_Rte_0786:  MISRA rule: Rule5.5
     Reason:     Same macro and idintifier names in first 63 characters are required to meet AUTOSAR spec.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3449:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

   MD_Rte_3451:  MISRA rule: Rule8.5
     Reason:     Schedulable entities are declared by the RTE and also by the BSW modules.
     Risk:       No functional risk.
     Prevention: Not required.

*/
