package(default_visibility = ["//visibility:public"])

cc_library(
    name = "grid_params",
    srcs = [
        "grid_params.cpp",
    ],
    hdrs = [
        "grid_params.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        "//onboard/common/math",
        "//onboard/planner/open_space/constraints:motion_plan_roi",
    ],
)

cc_library(
    name = "grid_util",
    hdrs = [
        "grid_util.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        "//onboard/common/math",
    ],
)

cc_library(
    name = "grid_cell",
    srcs = [
        "grid_cell.cpp",
    ],
    hdrs = [
        "grid_cell.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":grid_util",
        ":open_space_node",
        "//onboard/common/math",
        "//onboard/planner/open_space/constraints:util",
        "@voy-sdk//:gtest",
    ],
)

cc_library(
    name = "grid",
    srcs = [
        "grid.cpp",
    ],
    hdrs = [
        "grid.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":grid_cell",
        ":grid_params",
        "//onboard/common/vehicle_model:utils",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/open_space/constraints:motion_plan_problem",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:glog",
        "@voy-sdk//:tbb",
    ],
)

cc_library(
    name = "hybrid_a_star_search",
    srcs = [
        "hybrid_a_star_search.cpp",
    ],
    hdrs = [
        "hybrid_a_star_search.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":grid",
        ":open_space_costing_engine",
        ":open_space_node",
        ":open_space_path_search_result",
        ":reeds_shepp_path_generator",
        "//onboard/common/math",
        "//onboard/common/vehicle_model",
        "//onboard/common/vehicle_model:utils",
        "//onboard/planner:voy_trace_provider_planner",
        "//onboard/planner/world_model:robot_state",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:glog",
        "@voy-sdk//:tbb",
    ],
)

cc_library(
    name = "open_space_costing_engine",
    srcs = [
        "open_space_costing_engine.cpp",
    ],
    hdrs = [
        "open_space_costing_engine.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":grid",
        ":open_space_node",
        "//onboard/common/math",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "open_space_node",
    hdrs = [
        "open_space_node.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":path_segment",
        "//onboard/common/math",
        "@voy-sdk//:absl-strings",
    ],
)

cc_library(
    name = "open_space_path_search_result",
    hdrs = [
        "open_space_path_search_result.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        "//onboard/planner/behavior/util/lane_sequence_geometry",
        "//onboard/planner/world_model:planner_object",
        "//onboard/planner/world_model:robot_state",
        "//onboard/planner/world_model/construction_zone",
    ],
)

cc_library(
    name = "open_space_path_searcher",
    srcs = [
        "open_space_path_searcher.cpp",
    ],
    hdrs = [
        "open_space_path_searcher.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":grid",
        ":hybrid_a_star_search",
        ":open_space_path_search_result",
        "//protobuf_cpp:protos_cpp",
    ],
)

cc_library(
    name = "path_segment",
    hdrs = [
        "path_segment.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        "//onboard/common/math",
    ],
)

cc_library(
    name = "reeds_shepp_path_generator",
    srcs = [
        "reeds_shepp_path_generator.cpp",
    ],
    hdrs = [
        "reeds_shepp_path_generator.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":open_space_node",
        ":path_segment",
        ":reeds_shepp_path_util",
        "//onboard/common/math",
        "//onboard/common/vehicle_model",
        "//onboard/planner/world_model:robot_state",
        "//protobuf_cpp:protos_cpp",
        "@voy-sdk//:tbb",
    ],
)

cc_library(
    name = "reeds_shepp_path_util",
    srcs = [
        "reeds_shepp_path_util.cpp",
    ],
    hdrs = [
        "reeds_shepp_path_util.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        "//onboard/common/math",
        "@voy-sdk//:absl-strings",
        "@voy-sdk//:glog",
    ],
)

cc_library(
    name = "arc_path_2d",
    srcs = [
        "arc_path_2d.cpp",
    ],
    hdrs = [
        "arc_path_2d.h",
    ],
    include_prefix = "planner/open_space/motion_plan_generator/open_space_path_searcher",
    deps = [
        ":reeds_shepp_path_util",
        "//onboard/common/math",
    ],
)
