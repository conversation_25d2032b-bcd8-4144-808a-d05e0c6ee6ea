#include "planner/assist/assist_stuck_scene_detector.h"

#include <algorithm>
#include <cstddef>
#include <deque>
#include <map>
#include <memory>
#include <optional>
#include <queue>
#include <string>
#include <tuple>
#include <unordered_map>
#include <unordered_set>

#include <glog/logging.h>

#include "base/elapsed_timer.h"
#include "base/now.h"
#include "planner/assist/assist_util.h"
#include "planner/assist/feature_extraction/utils/better_progress_utils.h"
#include "planner/assist/ignore_tracker/waypoint_cz_tracker.h"
#include "planner/assist/stuck_detect_rules/intervention_requirement.h"
#include "planner/assist/util/queuing_util.h"
#include "planner/behavior/util/traffic_rules/traffic_rule_utility.h"
#include "planner/planning_gflags.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/seed/planning_seed_token.h"
#include "planner_protos/hazardous_state.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/remote_assist_config.pb.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_protos/remote_assist_stuck.pb.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace {

// Considering that temp parked car detection may not give all correct results,
// we set a ratio threshold to help judge if ego is mainly yielding temp
// parked cars, used in MaybeFalseNegativeStuckSceneAboutNoblock.
constexpr double KStaticAgentJudgeRatio = 0.7;

// Here we set 300 ms as a default time out threshold, to help track
// constinous signals.
constexpr int KDefaultTimeOutThresholdInMs = 300;

// Queue size for creep signals.
constexpr int KDefaultQueueSizeForCreep = 40;

// The ratio to judge if creep signals suggested a TP stuck and should connect
// RA.
constexpr double KCreepRequestCycleRatio = 0.75;

// The cycle count for fn selection when ego in occlusion_maybe scenario.
constexpr int KStraightLaneFNselectionWaittingCycles = 100;

inline bool IsAVehicleWithParkedSign(const PlannerObject& obj) {
  return obj.is_vehicle() && obj.is_stationary() && obj.is_parked_car();
}

inline bool IsAVehicleWithBrakeLight(const PlannerObject& obj) {
  return obj.is_vehicle() && obj.is_stationary() &&
         obj.HasPerceptionAttribute(
             voy::perception::Attribute::VEHICLE_BRAKE_SIGNAL_ON);
}

inline bool IsAVehicleWithTurnSignal(const PlannerObject& obj) {
  return obj.is_vehicle() && obj.is_slow_moving_vehicle() &&
         (obj.HasPerceptionAttribute(
              voy::perception::Attribute::VEHICLE_LEFT_TURN_SIGNAL_ON) ||
          obj.HasPerceptionAttribute(
              voy::perception::Attribute::VEHICLE_RIGHT_TURN_SIGNAL_ON));
}

inline bool IsAVehicleWithHazardSignal(const PlannerObject& obj) {
  return obj.is_vehicle() && obj.is_stationary() &&
         obj.HasPerceptionAttribute(
             voy::perception::Attribute::VEHICLE_HAZARD_SIGNAL_ON);
}

// Return true if the order status is kHeadingToPickupPoint and the order
// interaction_request is kRequestArrivePickupPoint, which means the ego is
// going to pickup passengers.
inline bool IsHeadingToPickupPoint(const SpeedWorldModel& world_model) {
  if (world_model.latest_snapshot_ptr() == nullptr ||
      world_model.latest_snapshot_ptr()->order_service_ptr == nullptr) {
    return false;
  }
  return world_model.latest_snapshot_ptr()->order_service_ptr->status() ==
             order::pb::kHeadingToPickupPoint &&
         world_model.latest_snapshot_ptr()
                 ->order_service_ptr->interaction_request() ==
             order::pb::kRequestArrivePickupPoint;
}

// Return true if the order status is kHeadingToDropoffPoint and the order
// interaction_request is kRequestArriveDropoffPoint, which means the ego is
// going to dropoff passengers to the dropoff point.
inline bool IsHeadingToDropoffPoint(const SpeedWorldModel& world_model) {
  if (world_model.latest_snapshot_ptr() == nullptr ||
      world_model.latest_snapshot_ptr()->order_service_ptr == nullptr) {
    return false;
  }
  return world_model.latest_snapshot_ptr()->order_service_ptr->status() ==
             order::pb::kHeadingToDropoffPoint &&
         world_model.latest_snapshot_ptr()
                 ->order_service_ptr->interaction_request() ==
             order::pb::kRequestArriveDropoffPoint;
}

// Delete stuck scene request if existed in current assist requests.
void RemoveStuckSceneRequestIfExisted(
    std::vector<pb::AssistRequest>* acceptable_assist_requests) {
  for (auto it = acceptable_assist_requests->begin();
       it != acceptable_assist_requests->end();) {
    if (it->type_case() == pb::AssistRequest::TypeCase::kStuckSceneRequest) {
      it = acceptable_assist_requests->erase(it);
    } else {
      ++it;
    }
  }
}

// Return true if target_lane is not congested.
bool IsTargetLaneAvailable(
    const LaneCongestionReasonerSpeedData& lane_congestion_reasoner,
    const pnc_map::Lane* target_lane) {
  return !lane_congestion_reasoner.IsLaneCongested(target_lane->id());
}

// Extracts dominant constraint from feature extractor.
const pb::SpeedConstraintMeta* ExtractDominantConstraint(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_dominant_constraint()) {
    return nullptr;
  }
  return &(feature->decoupled_forward_feature()
               .speed_feature()
               .dominant_constraint());
}

// Return true if ego is almost in pull over destination.
bool IsInPullOverDestination(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    double pull_over_destination_dist) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_is_pull_over()) {
    return false;
  }
  if (feature->decoupled_forward_feature().speed_feature().is_pull_over() &&
      feature->decoupled_forward_feature()
              .speed_feature()
              .dist_to_pull_over_dest_fence_m() < pull_over_destination_dist) {
    return true;
  }
  return false;
}

// Return true if pull over is completed and pull out is waiting when ego is in
// normal pull over and immediate pull over.
bool IsPullOverCompletedAndWaitingPullOut(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_pull_over_progress()) {
    return false;
  }
  return feature->decoupled_forward_feature().pull_over_progress() ==
         pb::PullOverProgress::kCompleted;
}

// Returns true if any of ego's successor lanes is controlled by red light.
bool IsEgoSuccessorLaneControlledByRedLight(
    const pnc_map::Lane* current_lane,
    const voy::TrafficLights& traffic_lights) {
  // get successor lanes of current lane and check
  // if successor lanes in junction and controlled by red light.
  if (current_lane != nullptr && current_lane->HasSuccessorsInJunction()) {
    for (const pnc_map::Lane* successor_lane : current_lane->successors()) {
      const lane_selection::MatchedTrafficLightInfo& matched_traffic_info =
          lane_selection::GetMatchedTrafficLight(
              successor_lane->turn(), successor_lane->traffic_signals(),
              traffic_lights);
      if (matched_traffic_info.associate_traffic_signal != nullptr &&
          matched_traffic_info.traffic_signal_detection != nullptr &&
          matched_traffic_info.traffic_signal_detection->color() ==
              voy::TrafficLight::RED) {
        return true;
      }
    }
  }
  return false;
}

bool HasTrafficLightConstraint(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_has_tl_fence()) {
    return false;
  }
  return feature->decoupled_forward_feature().speed_feature().has_tl_fence();
}

bool IsEgoWaittingTrafficLight(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pnc_map::Lane* current_lane,
    const voy::TrafficLights& traffic_lights) {
  // case 0: with tl fence.
  const bool has_tl_fence = HasTrafficLightConstraint(feature_extractor);
  // case 1: without tl fence, check successor lanes.
  const bool successor_lane_red_light =
      IsEgoSuccessorLaneControlledByRedLight(current_lane, traffic_lights);
  return has_tl_fence || successor_lane_red_light;
}

// Returns true if any of ego's trajectory candidates in selector is controlled
// by red light.
bool HasTrajectoryCandidateWaitingRedLight(
    const pb::EgoStuckFeatureAboutDecoupledForward& feature,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  for (const pb::EgoStuckFeatureAboutTrajectoryMeta& trajectory_meta_feature :
       feature.selection_trajectory_meta()) {
    if (trajectory_meta_feature.is_selected()) {
      continue;
    }
    if (trajectory_meta_feature.dist_to_tl_fence_for_unselected_candidate_m() <
        stuck_scene_detector_fp_rules_config
            .dist_to_traffic_light_fence_for_complex_scene_in_meter()) {
      return true;
    }
  }
  return false;
}

bool IsComplexFalsePositiveScene(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    bool is_controlled_by_red_light,
    std::optional<double> distance_to_junction) {
  // check end of lane sequence fence.
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (!feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      feature->decoupled_forward_feature()
          .selection_trajectory_meta()
          .empty()) {
    return false;
  }
  const auto& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  const bool has_trajectory_candidate_waiting_red_light =
      HasTrajectoryCandidateWaitingRedLight(
          feature->decoupled_forward_feature(),
          stuck_scene_detector_fp_rules_config);
  const bool is_in_reasonable_distance =
      speed_feature.dist_to_end_of_lane_sequence_fence_m() <
          stuck_scene_detector_fp_rules_config
              .dist_to_end_of_lane_sequence_fence_in_meter() &&
      distance_to_junction.has_value() &&
      distance_to_junction <
          stuck_scene_detector_fp_rules_config.dist_to_junction_in_meter();
  return is_in_reasonable_distance &&
         (is_controlled_by_red_light ||
          has_trajectory_candidate_waiting_red_light);
}

bool IsFalsePositiveStuckSceneAboutTrafficJam(
    const SpeedWorldModel& world_model, const pnc_map::Lane* current_lane,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  // The following logic is for traffic jam and congestion in front(current,
  // left, right) lanes.
  if (current_lane == nullptr) {
    return false;
  }
  int vote_cnt = 0, info_cnt = 0;
  std::vector<int64_t> related_lane_ids;
  const std::map<int64_t, LaneTrafficFlow>& lane_traffic_flow_map =
      world_model.lane_congestion_detector().lane_traffic_flow_map();
  related_lane_ids.push_back(current_lane->id());
  if (current_lane->adjacent_left_lane() != nullptr) {
    related_lane_ids.push_back(current_lane->adjacent_left_lane()->id());
  }
  if (current_lane->adjacent_right_lane() != nullptr) {
    related_lane_ids.push_back(current_lane->adjacent_right_lane()->id());
  }
  if (related_lane_ids.size() == 0) {
    return false;
  }
  for (const auto& lane_id : related_lane_ids) {
    const auto iter = lane_traffic_flow_map.find(lane_id);
    if (iter != lane_traffic_flow_map.end()) {
      info_cnt += 1;
      if (assist_stuck::IsQueuingTrafficFlow(
              iter->second,
              stuck_scene_detector_fp_rules_config.slow_moving_count(),
              stuck_scene_detector_fp_rules_config.congestion_prob())) {
        vote_cnt += 1;
      }
    }
  }
  // return true if len(congestion lanes) > half of near lanes.
  return vote_cnt > info_cnt * 0.5;
}

bool IsFalsePositiveStuckSceneAboutPullOver(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    const PlannerObject* yield_obj, bool is_yielding_object_dominant_constraint,
    int& yield_temp_parked_cycles_when_pullover) {
  // 1. If returned true, ego will:
  // 1.1 Not be in status of HeadingToPickupPoint or HeadingToDropoffPoint. This
  // means ego is in one of status of receiving new order, waiting for
  // passengers, finishing ride, canceling order.
  // 1.2 Be in pull over completed status. This means pull over is completed and
  // pull out is waiting.
  if ((!IsHeadingToPickupPoint(world_model) &&
       !IsHeadingToDropoffPoint(world_model)) ||
      IsPullOverCompletedAndWaitingPullOut(feature_extractor)) {
    rt_event::PostRtEvent<rt_event::planner::AssistStuckFPPullOverAboutOrder>();
    // FLAGS_planning_enable_remote_assist_fp_pull_over_about_order is set to
    // true in master, false in release. So in master, if the order conditions
    // are met, return true. But in release, regardless of whether the condition
    // is met or not, nothing is returned, and the subsequent process continues.
    if (FLAGS_planning_enable_remote_assist_fp_pull_over_about_order) {
      return true;
    }
  }
  // 2. Filter out scenes that are not about pull over.
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_is_pull_over()) {
    return false;
  }
  if (!feature->decoupled_forward_feature().speed_feature().is_pull_over()) {
    return false;
  }
  // 3. For pull over scenes about stationary objects, if there is a parked
  // car,
  // double flash, static roadblock, or a near-stationary object with no
  // intention to start, trigger FP within 100 cycles to avoid temporary
  // stopping, and do not trigger FP after 100 cycles.
  // TODO(fengkaijun): Remove this flag after the logic has been land in
  // release.
  if (yield_obj != nullptr && is_yielding_object_dominant_constraint &&
      (IsAVehicleWithParkedSign(*yield_obj) ||
       yield_obj->is_static_roadblock() ||
       ((yield_obj->is_stationary() || yield_obj->is_primary_stationary()) &&
        !yield_obj->has_stationary_to_move_intention()))) {
    yield_temp_parked_cycles_when_pullover++;
    if (yield_temp_parked_cycles_when_pullover >
        stuck_scene_detector_fp_rules_config
            .yield_temp_parked_cycles_when_pullover()) {
      rt_event::PostRtEvent<
          rt_event::planner::
              AssistStuckFPPullOverAboutStationaryObjectTriggered>();
      // FLAGS_planning_enable_remote_assist_fp_pull_over_about_stationary_object
      // is set to true in master, false in release. So in master, if the
      // cycle count conditions are met, return false. But in release,
      // regardless of whether the condition is met or not, nothing is returned,
      // and the subsequent process continues.
      if (FLAGS_planning_enable_remote_assist_fp_pull_over_about_stationary_object) {
        return false;
      }
    }
  }
  // 4. For other pull over scenes, continue to output true.
  return true;
}

bool IsFalsePositiveStuckSceneAboutStartup(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (feature->has_decoupled_forward_feature() &&
      feature->decoupled_forward_feature().has_speed_feature()) {
    const auto& speed_feature =
        feature->decoupled_forward_feature().speed_feature();
    if (speed_feature.has_avg_v_after_future_five_sec() &&
        speed_feature.avg_v_after_future_five_sec() >=
            stuck_scene_detector_fp_rules_config
                .avg_v_after_future_five_sec_in_mps()) {
      return true;
    }
    if (speed_feature.has_avg_a_within_future_three_sec() &&
        speed_feature.avg_a_within_future_three_sec() >=
            stuck_scene_detector_fp_rules_config
                .avg_a_with_in_future_three_sec_in_mpss()) {
      return true;
    }
  }
  return false;
}

bool IsFalsePositiveStuckSceneAboutHolding(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  // If returned true, ego will not request for assist since it has been
  // held by some confirming process, like pull-out.
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (feature->has_decoupled_forward_feature() &&
      feature->decoupled_forward_feature().has_speed_feature()) {
    const auto& speed_feature =
        feature->decoupled_forward_feature().speed_feature();
    return speed_feature.has_min_dist_to_holding_fences_m() &&
           speed_feature.min_dist_to_holding_fences_m() >= 0 &&
           speed_feature.min_dist_to_holding_fences_m() <=
               stuck_scene_detector_fp_rules_config.dist_to_holding_in_meter();
  }
  return false;
}

bool IsFalsePositiveStuckSceneAboutCrosswalk(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  // If returned true, ego will not request for assist since it has been
  // held by yielding fence of crosswalk.
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_dist_to_crosswalk_fence_m()) {
    return false;
  }
  const auto& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  // Use crosswalk object, crosswalk object_count, fence_dist to judge if it's a
  // FP scene.
  const auto& object_map = world_model.planner_object_map();
  const auto iter = object_map.find(speed_feature.crosswalk_yield_object_id());
  if (iter == object_map.end()) {
    return false;
  }
  // For low crosswalk_yield_object_count.
  // Here we set 5 as the crosswalk_yield_object_count threshold to avoid hit FP
  // in stuck scenarios like 5589184, 5588670 and 5588554.
  if ((iter->second.is_stationary() || iter->second.is_primary_stationary()) &&
      speed_feature.crosswalk_yield_object_count() <=
          stuck_scene_detector_fp_rules_config.crosswalk_yield_object_count()) {
    return false;
  }
  // For high crosswalk_yield_object_count.
  // Here we set crosswalk_yield_cycle_count to 40 cycles to avoid potential
  // late auto-trigger.(Testing ops will request RA manually in about 5s).
  if (static_cast<int>(feature_extractor.n_cycles_in_low_speed()) >
          stuck_scene_detector_fp_rules_config.crosswalk_yield_cycle_count() &&
      iter->second.is_pedestrian() &&
      (iter->second.is_stationary() || iter->second.is_primary_stationary()) &&
      speed_feature.crosswalk_yield_object_count() >
          stuck_scene_detector_fp_rules_config.crosswalk_yield_object_count()) {
    return false;
  }
  return speed_feature.dist_to_crosswalk_fence_m() >= 0 &&
         speed_feature.dist_to_crosswalk_fence_m() <=
             stuck_scene_detector_fp_rules_config.dist_to_cross_walk_in_meter();
}

// Checks if yielding varying traffic flow.
bool IsYieldingDynamicObjectsVarying(
    const std::unordered_map<int64_t, int>& yield_obj_freq_umap,
    const std::deque<int64_t>& yield_obj_queue,
    const pb::StuckSceneDetectorFPRulesConfig& fp_config) {
  const size_t num_yield_objects_threshold =
      static_cast<size_t>(fp_config.num_yield_dynamic_objects_varying());
  if (yield_obj_queue.size() < num_yield_objects_threshold ||
      yield_obj_freq_umap.size() < num_yield_objects_threshold) {
    return false;
  }
  std::unordered_set<int64_t> yielding_objects;
  for (auto iter = yield_obj_queue.rbegin(); iter != yield_obj_queue.rend();
       iter++) {
    int64_t obj_id = *iter;
    if (yielding_objects.count(obj_id)) {
      continue;
    }
    const int* freq = gtl::FindOrNull(yield_obj_freq_umap, obj_id);
    if (freq && *freq > 1 &&
        *freq <= fp_config.cycles_yield_dynamic_objects_varying()) {
      yielding_objects.emplace(obj_id);
      if (yielding_objects.size() >= num_yield_objects_threshold) {
        return true;
      }
    } else {
      break;
    }
  }
  return false;
}

bool IsFalsePositiveStuckSceneAboutYieldDynamicObject(
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    const PlannerObject* yield_obj, const pnc_map::Lane* current_lane,
    const std::unordered_map<int64_t, int>& yield_obj_freq_umap,
    const std::deque<int64_t>& yield_obj_queue, int n_cycles_in_low_speed,
    bool is_yielding_object_dominant_constraint, double dist_to_yield_obj_m,
    int* yield_hold_cycles, int* yield_temp_parked_cycles,
    int* right_lane_yield_cycles, int* total_yielding_cycles) {
  // TODO(Xiaobo): Refactor similar usages in AssistStuckSceneDetector where
  // both provide function returns and modify mutable param values.
  if (yield_obj == nullptr || yield_hold_cycles == nullptr ||
      yield_temp_parked_cycles == nullptr ||
      right_lane_yield_cycles == nullptr || total_yielding_cycles == nullptr ||
      yield_obj->is_blockage_object()) {
    return false;
  }
  // Process yielding other lane slow moving objects.
  if (current_lane != nullptr &&
      (yield_obj->is_slow_moving_vehicle() || yield_obj->is_stationary() ||
       yield_obj->is_primary_stationary()) &&
      !math::geometry::Within(yield_obj->center_2d(), current_lane->border()) &&
      n_cycles_in_low_speed >
          stuck_scene_detector_fp_rules_config
              .slow_moving_obj_in_other_lane_yield_cycles()) {
    *yield_hold_cycles = 0;
    *yield_temp_parked_cycles = 0;
    *right_lane_yield_cycles = 0;
    return false;
  }
  // Process dynamic/static yielding switch scenario to avoid blocking recall.
  const auto max_freq_obj_iter = std::max_element(
      yield_obj_freq_umap.begin(), yield_obj_freq_umap.end(),
      [](const auto& kv1, const auto& kv2) { return kv1.second < kv2.second; });
  if (max_freq_obj_iter != yield_obj_freq_umap.end()) {
    const int64_t yield_obj_id = max_freq_obj_iter->first;
    if (yield_obj->id() == yield_obj_id &&
        (yield_obj->is_stationary() || yield_obj->is_primary_stationary()) &&
        max_freq_obj_iter->second > stuck_scene_detector_fp_rules_config
                                            .mainly_yielding_static_ratio() *
                                        n_cycles_in_low_speed &&
        n_cycles_in_low_speed >
            stuck_scene_detector_fp_rules_config
                .special_scenario_yielding_time_in_cycles()) {
      *yield_hold_cycles = 0;
      *yield_temp_parked_cycles = 0;
      *right_lane_yield_cycles = 0;
      return false;
    }
  }

  // Process yielding static obj in junction\exit_zone to avoid blocking
  // recall.
  if ((yield_obj->is_in_junction() || yield_obj->is_in_exit_zone()) &&
      (yield_obj->is_stationary() || yield_obj->is_primary_stationary()) &&
      is_yielding_object_dominant_constraint &&
      (IsAVehicleWithTurnSignal(*yield_obj) ||
       IsAVehicleWithBrakeLight(*yield_obj) ||
       IsAVehicleWithHazardSignal(*yield_obj) ||
       yield_obj->optional_observed_stationary_duration_ms() >
           stuck_scene_detector_fp_rules_config
               .observed_stationary_duration_in_ms()) &&
      n_cycles_in_low_speed > stuck_scene_detector_fp_rules_config
                                  .special_scenario_yielding_time_in_cycles()) {
    *yield_hold_cycles = 0;
    *yield_temp_parked_cycles = 0;
    *right_lane_yield_cycles = 0;
    return false;
  }
  // Process a common scenario to avoid miss recall:
  // ego on right most lane, stuck by a nearby static vehicle.
  if (current_lane != nullptr &&
      (current_lane->IsRightmostVehicleLane() ||
       current_lane->IsRightmostDrivableLane()) &&
      is_yielding_object_dominant_constraint &&
      (yield_obj->is_stationary() || yield_obj->is_primary_stationary()) &&
      (yield_obj->is_vehicle() || yield_obj->is_truck() ||
       yield_obj->is_large_vehicle() || yield_obj->is_bus())) {
    (*right_lane_yield_cycles)++;
    if (*right_lane_yield_cycles <
        stuck_scene_detector_fp_rules_config.right_lane_yield_cycles()) {
      *yield_hold_cycles = 0;
      *yield_temp_parked_cycles = 0;
      return true;
    }
    return false;
  }
  // Filter temporary stationary scenarios with timeout:
  // 1.taxis/vehicles opens door, waitting passengers to get in/off.
  // 2.vehicle shortly stops in bus_bulb, yielding for a while.
  // 3.yielding object has stm intention(signal by prediction).
  if (!yield_obj->is_parked_car() && !yield_obj->is_static_roadblock() &&
      (yield_obj->is_stationary() || yield_obj->is_primary_stationary()) &&
      (yield_obj->is_open_door_vehicle() || yield_obj->is_in_bus_bulb() ||
       yield_obj->has_stationary_to_move_intention())) {
    (*yield_temp_parked_cycles)++;
    if (*yield_temp_parked_cycles <
        stuck_scene_detector_fp_rules_config.yield_temp_parked_cycles()) {
      return true;
    }
  }

  // If yielding varying traffic flow, this rule should not trigger.
  if (FLAGS_planning_enable_assist_stuck_yield_varying_traffic_flow &&
      IsYieldingDynamicObjectsVarying(yield_obj_freq_umap, yield_obj_queue,
                                      stuck_scene_detector_fp_rules_config)) {
    return false;
  }

  // If return true, ego will not request for assist since it has been
  // held by yielding fence of dynamic object.
  if (!yield_obj->is_stationary() &&
      dist_to_yield_obj_m <=
          stuck_scene_detector_fp_rules_config.dist_to_yield_in_meter() &&
      *total_yielding_cycles <
          stuck_scene_detector_fp_rules_config.total_yielding_cycles()) {
    *yield_hold_cycles = 1;
    return true;
  }

  // When yielding end, delay for a time buffer to let ego start up.
  if (*yield_hold_cycles != 0 &&
      *yield_hold_cycles <
          stuck_scene_detector_fp_rules_config.yield_hold_cycles()) {
    (*yield_hold_cycles)++;
    if (*yield_hold_cycles ==
        stuck_scene_detector_fp_rules_config.yield_hold_cycles()) {
      *yield_hold_cycles = 0;
    }
    return true;
  }
  return false;
}

bool IsFalsePositiveStuckSceneAboutPlannedMaxSpeed(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (!feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature()) {
    return false;
  }
  const auto& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  if (!speed_feature.has_max_v()) {
    return false;
  }
  return speed_feature.max_v() >=
         stuck_scene_detector_fp_rules_config.planned_max_speed_in_mps();
}

// Tests if the ego yielding temp parked cars near road boundary.
// TODO(liangxianghui, fengkaijun): This function considers right side only,
// extend to left side of the road.
bool IsYieldingTempParkedCarsNearRoadBoundary(
    const SpeedWorldModel& world_model, const pnc_map::Lane* current_lane,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig& fp_rules_config,
    const pb::TempParkedCarsNearBoundaryConfig&
        temp_parked_cars_near_boundary_config,
    pb::EgoStuckDebug* ego_stuck_debug) {
  if (!current_lane || current_lane->adjacent_right_lane()) {
    return false;
  }
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (!feature || !feature->has_routing_feature() ||
      !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature()) {
    return false;
  }
  const pb::EgoStuckFeatureAboutDecoupledSpeed& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  // If the adjacent left lane is queuing, the ego has to wait.
  if (current_lane->adjacent_left_lane()) {
    const bool is_congested = speed_feature.is_in_congestion();
    const LaneTrafficFlow* left_traffic_flow = gtl::FindOrNull(
        world_model.lane_congestion_detector().lane_traffic_flow_map(),
        current_lane->adjacent_left_lane()->id());
    const bool is_queuing =
        left_traffic_flow != nullptr &&
        assist_stuck::IsQueuingTrafficFlow(*left_traffic_flow,
                                           fp_rules_config.slow_moving_count(),
                                           fp_rules_config.congestion_prob());
    // Add trace point for the diff.
    if (is_congested != is_queuing) {
      rt_event::PostRtEvent<rt_event::planner::AssistStuckCongestionAPIDiff>();
    }
    if (FLAGS_planning_enable_congestion_api_in_assist_stuck_detector) {
      ego_stuck_debug->set_use_congested_judge(true);
      ego_stuck_debug->set_is_congested(is_congested);
      if (is_congested) {
        return false;
      }
    }
    if (is_queuing) {
      return false;
    }
  }
  const auto& blockage_object_map =
      feature->routing_feature().blockage_object_map();
  return std::count_if(
             speed_feature.leading_object_ids().cbegin(),
             speed_feature.leading_object_ids().cend(),
             [&world_model, &blockage_object_map,
              &temp_parked_cars_near_boundary_config](const int64_t obj_id) {
               // For counting temp parked cars.
               const PlannerObject* obj =
                   gtl::FindOrNull(world_model.planner_object_map(), obj_id);
               if (!obj || obj->l2_distance_to_ego_m() >
                               temp_parked_cars_near_boundary_config
                                   .interesting_dist_in_meter()) {
                 return false;
               }
               if (IsAVehicleWithParkedSign(*obj) ||
                   IsAVehicleWithHazardSignal(*obj)) {
                 return true;
               }
               const pb::BlockageObjectFeature* blockage_obj_ft =
                   gtl::FindOrNull(blockage_object_map, obj_id);
               if (blockage_obj_ft) {
                 if (blockage_obj_ft->left_inlane_clearance() >=
                         temp_parked_cars_near_boundary_config
                             .farther_side_inlane_dist_in_meter() &&
                     blockage_obj_ft->right_clearance_to_hard_boundary() <=
                         temp_parked_cars_near_boundary_config
                             .nearer_side_hard_boundary_dist_in_meter() &&
                     blockage_obj_ft->left_clearance_to_hard_boundary() >=
                         temp_parked_cars_near_boundary_config
                             .farther_side_hard_boundary_dist_in_meter()) {
                   return true;
                 }
               }
               return false;
             }) >=
         temp_parked_cars_near_boundary_config.temp_parked_cars_count();
}

// Returns true if it's a u turn lane.
bool IsUTurn(const pnc_map::Lane* lane) {
  return lane->turn() == hdmap::Lane::U_TURN &&
         lane->type() == hdmap::Lane_LaneType_VIRTUAL;
}

// Returns true if it's a right turn lane.
bool IsRightTurn(const pnc_map::Lane* lane) {
  return lane->turn() == hdmap::Lane::RIGHT;
}

// Returns true if it's false positive stuck about yielding on right or u
// turn.
bool IsFalsePositiveStuckSceneAboutYieldingOnTurn(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    const pnc_map::Lane* current_lane,
    const std::vector<const pnc_map::Lane*>& global_route_lanes,
    const PlannerObject* yield_obj, pb::EgoStuckDebug* ego_stuck_debug) {
  // If 1) yield obj is stationary
  // and 2) yielding a vehicle or cyclist, not a parked car or a car with
  // double flashes and 3) the ego is on right or u turn, then return true for
  // a FP scene. For fixing right turn issues like cn11267871, cn11886053, u
  // turn issues like cn11659917.
  if (current_lane == nullptr) {
    return false;
  }
  if (yield_obj == nullptr || !yield_obj->is_stationary()) {
    return false;
  }
  if (IsAVehicleWithParkedSign(*yield_obj) ||
      IsAVehicleWithHazardSignal(*yield_obj)) {
    return false;
  }
  if (!yield_obj->is_cyclist() && !yield_obj->is_vehicle()) {
    return false;
  }

  // Helper function to check right turn and u-turn logic.
  const auto CheckTurn = [&world_model, &feature_extractor,
                          &stuck_scene_detector_fp_rules_config,
                          &ego_stuck_debug](const pnc_map::Lane* lane) {
    // Check if the lane is a nullptr.
    if (lane == nullptr) {
      return false;
    }

    // Check if the lane is u-turn.
    if (IsUTurn(lane)) {
      return true;
    }

    // Check if the lane is right turn. When in right turn, if ego is
    // yielding temp parked cars near right most road boundary, the
    // FP_OnTurn should not be valid.
    if (IsRightTurn(lane)) {
      if (IsYieldingTempParkedCarsNearRoadBoundary(
              world_model, lane, feature_extractor,
              stuck_scene_detector_fp_rules_config,
              stuck_scene_detector_fp_rules_config.yielding_on_turn_config()
                  .temp_parked_cars_near_boundary_config(),
              ego_stuck_debug)) {
        rt_event::PostRtEvent<
            rt_event::planner::AssistStuckFPOnTurnAboutTempParkedCar>();
        // FLAGS_planning_enable_remote_assist_fp_on_turn_about_temp_parked_car
        // is set to true in master, false in release. So in master, if the
        // right turn and temp parked cars conditions are met, return false.
        // But in release, regardless of whether the condition is met or
        // not, nothing is returned, and the subsequent process continues.
        if (FLAGS_planning_enable_remote_assist_fp_on_turn_about_temp_parked_car) {
          return false;
        }
      }
      return true;
    }

    return false;
  };

  // Return true, if current_lane satisfies the condition of CheckTurn.
  if (CheckTurn(current_lane)) {
    return true;
  }

  // Find the current_lane_iter in global_route_lanes to get next_lane_iter and
  // double check.
  const auto current_lane_iter =
      std::find_if(global_route_lanes.begin(), global_route_lanes.end(),
                   [&current_lane](const pnc_map::Lane* lane) {
                     return lane != nullptr && lane->id() == current_lane->id();
                   });
  if (current_lane_iter == global_route_lanes.end()) {
    return false;
  }

  // Double check if current_lane in global_route_lanes satisfies the condition
  // of CheckTurn.
  if (CheckTurn(*current_lane_iter)) {
    return true;
  }

  // Return true, if next_lane in global_route_lanes satisfies the condition of
  // CheckTurn.
  const auto next_lane_iter = std::next(current_lane_iter);
  if (next_lane_iter != global_route_lanes.end() &&
      *next_lane_iter != nullptr && CheckTurn(*next_lane_iter)) {
    return true;
  }

  return false;
}

bool IsCurrentLaneInQueuing(
    const SpeedWorldModel& world_model, const pnc_map::Lane* current_lane,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    int slow_moving_count,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    pb::EgoStuckDebug* ego_stuck_debug) {
  // Extract from feature.
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature && feature->has_progress_feature()) {
    const pb::EgoStuckFeatureAboutProgress& progress_feature =
        feature->progress_feature();
    // TODO(lxh): Also consider some nearby obstacles here.
    if (progress_feature.ego_stationary_reason() ==
        pb::BlockageObjectFeature::kQueuing) {
      return true;
    }
  }
  // Extract from traffic flow.
  if (FLAGS_planning_enable_assist_stuck_traffic_flow_queuing_reasoning &&
      current_lane != nullptr) {
    const bool is_congested =
        feature && feature->has_decoupled_forward_feature() &&
                !feature->decoupled_forward_feature().has_speed_feature()
            ? feature->decoupled_forward_feature()
                  .speed_feature()
                  .is_in_congestion()
            : false;
    const std::map<int64_t, LaneTrafficFlow>& lane_traffic_flow_map =
        world_model.lane_congestion_detector().lane_traffic_flow_map();
    const auto iter = lane_traffic_flow_map.find(current_lane->id());
    const bool is_queuing =
        iter != lane_traffic_flow_map.end() &&
        assist_stuck::IsQueuingTrafficFlow(
            iter->second, slow_moving_count,
            stuck_scene_detector_fp_rules_config.congestion_prob());
    // Add trace point for the diff.
    if (is_congested != is_queuing) {
      rt_event::PostRtEvent<rt_event::planner::AssistStuckCongestionAPIDiff>();
    }
    if (FLAGS_planning_enable_congestion_api_in_assist_stuck_detector) {
      ego_stuck_debug->set_use_congested_judge(true);
      ego_stuck_debug->set_is_congested(is_congested);
      return is_congested;
    }
    return is_queuing;
  }
  return false;
}

// Tests having better progress for a given lane.
// Returns true, if 1) the lane existed, 2) was not in traffic jam,
// and 3) was connective to planned lane sequence. Means the ego
// could jump out and gain better progress.
bool HasBetterProgress(
    const pnc_map::Lane* lane,
    const std::map<int64_t, LaneTrafficFlow>& lane_traffic_flow_map,
    const std::vector<const pnc_map::Lane*>& following_lanes,
    const pb::StuckSceneDetectorFPRulesConfig& fp_config,
    pb::HasBetterProgressDebug* debug) {
  if (lane == nullptr) {
    return false;
  }
  const std::vector<int64_t>& lane_seq =
      assist_stuck::GetLaneSeqFromSourceToTargets(
          lane, following_lanes, lane_traffic_flow_map, fp_config);
  const bool has_better_progress = !lane_seq.empty();
  if (debug) {
    debug->set_has_better_progress(has_better_progress);
    debug->set_lane_id(lane->id());
    for (int64_t lane_id : lane_seq) {
      debug->add_connective_lane_seq(lane_id);
    }
  }
  return has_better_progress;
}

bool IsFalsePositiveStuckSceneAboutQueuing(
    const SpeedWorldModel& world_model, const pnc_map::Lane* current_lane,
    const std::map<int64_t, LaneTrafficFlow>& lane_traffic_flow_map,
    const std::vector<const pnc_map::Lane*> global_route_lanes,
    const PlannerObject* yielding_object,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    bool enable_stationary_leading_vehicle_in_queuing,
    pb::AssistStuckFPQueuingDebug* debug, pb::EgoStuckDebug* ego_stuck_debug) {
  // TODO(lxh): Consider forward yielding objects, not only the closest one.
  pb::AssistStuckFPQueuingDebug::DecisionReason decision = [&]() {
    // Check if stationary leading vehicle scene, which would be thought of as a
    // queuing scene in forcing recall, when
    // enable_stationary_leading_vehicle_in_queuing is true.
    // TODO(Fengkaijun): Merging force recall and detector's duplicate logic
    // judgment.
    if (enable_stationary_leading_vehicle_in_queuing &&
        planner::assist::IsWaitingInLineForLeadingVehicle(
            world_model, feature_extractor.GetLastFeature())) {
      return pb::AssistStuckFPQueuingDebug::kWaitingInLineForLeadingVehicle;
    }
    if (yielding_object == nullptr) {
      return pb::AssistStuckFPQueuingDebug::kNoYieldingObj;
    }
    if (yielding_object->is_parked_car()) {
      return pb::AssistStuckFPQueuingDebug::kYieldingParkedCar;
    }
    if (current_lane == nullptr) {
      return pb::AssistStuckFPQueuingDebug::kNoCurrentLane;
    }
    if (FLAGS_planning_enable_assist_stuck_checking_for_parked_cars_near_hb &&
        IsYieldingTempParkedCarsNearRoadBoundary(
            world_model, current_lane, feature_extractor,
            stuck_scene_detector_fp_rules_config,
            stuck_scene_detector_fp_rules_config.queuing_config()
                .temp_parked_cars_near_boundary_config(),
            ego_stuck_debug)) {
      return pb::AssistStuckFPQueuingDebug::kYieldingCarsNearBound;
    }
    if (!IsCurrentLaneInQueuing(
            world_model, current_lane, feature_extractor,
            stuck_scene_detector_fp_rules_config.slow_moving_count(),
            stuck_scene_detector_fp_rules_config, ego_stuck_debug)) {
      return pb::AssistStuckFPQueuingDebug::kCurPathNotQueuing;
    }
    // Fetch following lanes after the current lane from global route.
    std::vector<const pnc_map::Lane*> following_lanes;
    const auto current_iter =
        std::find_if(global_route_lanes.begin(), global_route_lanes.end(),
                     [current_lane](const pnc_map::Lane* lane) {
                       return lane && current_lane->id() == lane->id();
                     });
    if (current_iter != global_route_lanes.end()) {
      const size_t n_following_lanes =
          std::min(std::distance(current_iter + 1, global_route_lanes.end()),
                   static_cast<int64_t>(stuck_scene_detector_fp_rules_config
                                            .test_connective_bfs_max_depth()));
      if (n_following_lanes > 0) {
        following_lanes.reserve(n_following_lanes);
        std::copy(current_iter + 1, current_iter + 1 + n_following_lanes,
                  std::back_inserter(following_lanes));
      }
    }
    if (debug) {
      for (const pnc_map::Lane* lane : following_lanes) {
        debug->add_following_lanes(lane->id());
      }
    }
    const std::vector<const pnc_map::Lane*> adjacent_lanes = {
        current_lane->adjacent_left_lane(),
        current_lane->adjacent_right_lane()};
    if (std::any_of(adjacent_lanes.begin(), adjacent_lanes.end(),
                    [&](const pnc_map::Lane* lane) {
                      return HasBetterProgress(
                          lane, lane_traffic_flow_map, following_lanes,
                          stuck_scene_detector_fp_rules_config,
                          debug ? debug->add_adjacent_lane_progress_debug()
                                : nullptr);
                    })) {
      return pb::AssistStuckFPQueuingDebug::kBetterProgress;
    }
    return pb::AssistStuckFPQueuingDebug::kQueuing;
  }();
  if (debug) {
    debug->set_decision(decision);
    if (yielding_object != nullptr) {
      debug->set_yielding_obj_id(yielding_object->id());
    }
    if (current_lane != nullptr) {
      debug->set_current_lane_id(current_lane->id());
    }

    const pb::EgoStuckFeatureOnCycle* feature =
        feature_extractor.GetLastFeature();
    if (feature != nullptr && feature->has_progress_feature()) {
      const pb::EgoStuckFeatureAboutProgress& progress_feature =
          feature->progress_feature();
      debug->mutable_progress_feature()->CopyFrom(progress_feature);
    }
  }
  return decision == pb::AssistStuckFPQueuingDebug::kQueuing ||
         decision ==
             pb::AssistStuckFPQueuingDebug::kWaitingInLineForLeadingVehicle;
}

// Returns true if there maybe some agents behind a large leading obj, and
// then block assist stuck request.
bool IsFalsePositiveStuckSceneAboutOcclusion(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::FPRuleOcclusionConfig& config) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (!feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature()) {
    return false;
  }
  const pb::EgoStuckFeatureAboutDecoupledSpeed& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  return std::any_of(
      speed_feature.leading_object_ids().cbegin(),
      speed_feature.leading_object_ids().cend(),
      [&world_model, &config](const int64_t obj_id) {
        const PlannerObject* obj =
            gtl::FindOrNull(world_model.planner_object_map(), obj_id);
        if (obj == nullptr || !obj->is_vehicle() ||
            IsAVehicleWithParkedSign(*obj) ||
            IsAVehicleWithHazardSignal(*obj)) {
          return false;
        }
        // TODO(liangxianghui): make usage of EvaluateTrafficJamOcclusion()
        // and agent movement in history.
        if (obj->l2_distance_to_ego_m() > config.interesting_dist_in_meter() ||
            obj->height() < config.occlusion_object_height_in_meter()) {
          return false;
        }
        return true;
      });
}

// Returns true if remote_speed_limit has value, which means that an
// intervention is being performed at this moment to slow down the ego. If the
// ego is fully stopped, we do not want to trigger the RA at this point.
bool IsFalsePositiveStuckSceneAboutRemoteSpeedLimit(
    const SpeedWorldModel& world_model) {
  return world_model.assist_directive_generator()
      .remote_speed_limit()
      .has_value();
}

bool MaybeFalseNegativeStuckSceneAboutEOL(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::AssistStuckSceneDetectorConfig&
        assist_stuck_scene_detector_config) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_dist_to_end_of_lane_sequence_fence_m()) {
    return false;
  }

  const bool is_ego_in_pull_over_destination = IsInPullOverDestination(
      feature_extractor,
      assist_stuck_scene_detector_config.stuck_scene_detector_fn_rules_config()
          .dist_to_pull_over_destination_fence_in_meter());
  if (is_ego_in_pull_over_destination) {
    return false;
  }

  const auto& yielding_object_info = feature_extractor.yielding_object_info();

  if (feature->decoupled_forward_feature()
              .speed_feature()
              .dist_to_end_of_lane_sequence_fence_m() < 30.0 &&
      yielding_object_info != std::nullopt) {
    rt_event::PostRtEvent<rt_event::planner::AssistStuckFNForEOLWithYieldObj>();
  }
  // Do not trigger if ego is yielding a static obj.
  if (yielding_object_info != std::nullopt &&
      (yielding_object_info->first->is_stationary() ||
       yielding_object_info->first->is_primary_stationary()) &&
      yielding_object_info->second->is_dominant_constraint()) {
    return false;
  }
  // Judge if ego is stuck by a EOL fence nearly.
  return feature->decoupled_forward_feature()
             .speed_feature()
             .dist_to_end_of_lane_sequence_fence_m() <
         assist_stuck_scene_detector_config
             .stuck_scene_detector_fn_rules_config()
             .dist_to_eol_fence_in_meter();
}

bool MaybeFalseNegativeStuckSceneAboutSelection(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const SpeedWorldModel& world_model,
    const pb::LaneChangeState& selected_lane_change_state,
    const pb::AssistStuckSceneDetectorConfig&
        assist_stuck_scene_detector_config,
    pb::AssistStuckFNSelectionDebug* fn_selection_debug) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_current_diff_progress_in_m() ||
      !feature->decoupled_forward_feature().has_routing_cost_diff() ||
      !feature->decoupled_forward_feature()
           .has_progress_efficiency_cost_diff()) {
    return false;
  }
  // TODO(alexma): Add details for fn selection strategy debug.
  const bool has_other_candidate =
      feature->decoupled_forward_feature().selection_trajectory_meta().size() >
      1;
  fn_selection_debug->set_has_other_candidate(has_other_candidate);
  // Make sure there is a better progress trajectory, with low rerouting risk.
  // Fetch following lanes after the current lane from global route.
  const pnc_map::Lane* current_lane = world_model.GetCurrentLaneForAssist();
  const std::map<int64_t, LaneTrafficFlow>& lane_traffic_flow_map =
      world_model.lane_congestion_detector().lane_traffic_flow_map();
  const LaneCongestionReasonerSpeedData& lane_congestion_reasoner =
      world_model.lane_blockage_detector().lane_congestion_reasoner();
  const bool current_lane_available =
      current_lane != nullptr &&
      IsTargetLaneAvailable(lane_congestion_reasoner, current_lane);
  fn_selection_debug->set_current_lane_available(current_lane_available);
  const bool left_lane_available =
      current_lane != nullptr &&
      current_lane->adjacent_left_lane() != nullptr &&
      IsTargetLaneAvailable(lane_congestion_reasoner,
                            current_lane->adjacent_left_lane());
  fn_selection_debug->set_left_lane_available(left_lane_available);
  const bool right_lane_available =
      current_lane != nullptr &&
      current_lane->adjacent_right_lane() != nullptr &&
      IsTargetLaneAvailable(lane_congestion_reasoner,
                            current_lane->adjacent_right_lane());
  fn_selection_debug->set_right_lane_available(right_lane_available);
  fn_selection_debug->set_lane_change_state(selected_lane_change_state);
  const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
      lane_to_junction_info_map =
          world_model.regional_map().lane_to_junction_info_map;
  std::optional<double> distance_to_junction = std::nullopt;
  if (current_lane != nullptr) {
    const auto iter = lane_to_junction_info_map.find(current_lane->id());
    if (iter != lane_to_junction_info_map.end()) {
      const lane_selection::LaneToNextJunctionInfo& lane_to_junction_info =
          iter->second;
      const RobotStateSnapshot& robot_state_snapshot =
          world_model.robot_state().current_state_snapshot();
      const math::geometry::Point2d ego_center(robot_state_snapshot.x(),
                                               robot_state_snapshot.y());
      const double ego_arc_len =
          current_lane->center_line()
              .GetProximity(ego_center, math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      distance_to_junction =
          lane_to_junction_info.distance_to_junction_m() - ego_arc_len;
    }
  }
  if (distance_to_junction.has_value()) {
    fn_selection_debug->set_distance_to_junction(distance_to_junction.value());
  } else {
    fn_selection_debug->set_distance_to_junction(-1.0);
  }
  const bool has_tl_constraint = HasTrafficLightConstraint(feature_extractor);
  const bool is_ego_in_pull_over_destination = IsInPullOverDestination(
      feature_extractor,
      assist_stuck_scene_detector_config.stuck_scene_detector_fn_rules_config()
          .dist_to_pull_over_destination_fence_in_meter());
  const GlobalRouteSolution* global_route_solution =
      world_model.route_model().global_route_solution();

  const auto& yielding_object_info = feature_extractor.yielding_object_info();

  // Recall if it is a pullout stuck by static object scenario, ego is in the
  // right most lane.
  if (current_lane != nullptr && yielding_object_info != std::nullopt &&
      (current_lane->IsRightmostVehicleLane() ||
       current_lane->IsRightmostDrivableLane()) &&
      (yielding_object_info->first->is_stationary() ||
       yielding_object_info->first->is_primary_stationary()) &&
      yielding_object_info->second->is_dominant_constraint() &&
      distance_to_junction.has_value() &&
      distance_to_junction > assist_stuck_scene_detector_config
                                 .stuck_scene_detector_fp_rules_config()
                                 .dist_to_junction_in_meter()) {
    bool should_recall_by_right_lane_stuck =
        !is_ego_in_pull_over_destination && !has_tl_constraint &&
        feature->decoupled_forward_feature().current_diff_progress_in_m() >
            assist_stuck_scene_detector_config
                .stuck_scene_detector_fn_rules_config()
                .valid_progress_threshold_in_m();
    const bool occlusion_exists = yielding_object_info->first->height() >
                                  assist_stuck_scene_detector_config
                                      .stuck_scene_detector_fp_rules_config()
                                      .occlusion_config()
                                      .occlusion_object_height_in_meter();
    // Apply long time threshold for occlusion_exists scenario.
    if (occlusion_exists) {
      should_recall_by_right_lane_stuck &=
          feature_extractor.n_cycles_in_low_speed() >
          KStraightLaneFNselectionWaittingCycles;
    }
    // Set return && debug info.
    if (should_recall_by_right_lane_stuck) {
      fn_selection_debug->set_fn_scenario(
          pb::AssistStuckFNSelectionDebug::kRightMostLaneStuck);
      return true;
    }
  }
  // Check if ego is stuck by a stationary object in junction.
  const RobotStateSnapshot& robot_state_snapshot =
      world_model.robot_state().current_state_snapshot();
  hdmap::Point ego_pose_center;
  ego_pose_center.set_x(robot_state_snapshot.x());
  ego_pose_center.set_y(robot_state_snapshot.y());
  const bool is_ego_pose_in_junction =
      world_model.pnc_map_service()->IsInJunction(ego_pose_center);
  if (is_ego_pose_in_junction) {
    const bool should_recall_by_junction_stuck =
        yielding_object_info != std::nullopt &&
        (yielding_object_info->first->is_stationary() ||
         yielding_object_info->first->is_primary_stationary()) &&
        yielding_object_info->second->is_dominant_constraint() &&
        !(IsAVehicleWithTurnSignal(*yielding_object_info->first) ||
          IsAVehicleWithBrakeLight(*yielding_object_info->first));
    // Set debug info.
    if (should_recall_by_junction_stuck) {
      fn_selection_debug->set_fn_scenario(
          pb::AssistStuckFNSelectionDebug::kJunctionStuck);
      return true;
    }
  }
  // TODO(alexma,xianghui): Move HasBetterProgress related logic to avoid
  // duplicate calculating. Check if adjacent lanes passable to target lanes.
  if (global_route_solution != nullptr && current_lane != nullptr) {
    const std::vector<const pnc_map::Lane*>& global_route_lanes =
        global_route_solution->global_route_lanes();
    std::vector<const pnc_map::Lane*> following_lanes;
    const auto current_iter =
        std::find_if(global_route_lanes.begin(), global_route_lanes.end(),
                     [current_lane](const pnc_map::Lane* lane) {
                       return lane && current_lane->id() == lane->id();
                     });
    if (current_iter != global_route_lanes.end()) {
      const size_t n_following_lanes = std::min(
          std::distance(current_iter + 1, global_route_lanes.end()),
          static_cast<int64_t>(assist_stuck_scene_detector_config
                                   .stuck_scene_detector_fp_rules_config()
                                   .test_connective_bfs_max_depth()));
      if (n_following_lanes > 0) {
        following_lanes.reserve(n_following_lanes);
        std::copy(current_iter + 1, current_iter + 1 + n_following_lanes,
                  std::back_inserter(following_lanes));
      }
    }
    const std::vector<const pnc_map::Lane*> adjacent_lanes = {
        current_lane->adjacent_left_lane(),
        current_lane->adjacent_right_lane()};
    // Recall if other candidate gives a better progress with low rerouting
    // cost.
    if (!std::any_of(adjacent_lanes.begin(), adjacent_lanes.end(),
                     [&](const pnc_map::Lane* lane) {
                       return HasBetterProgress(
                           lane, lane_traffic_flow_map, following_lanes,
                           assist_stuck_scene_detector_config
                               .stuck_scene_detector_fp_rules_config(),
                           nullptr);
                     })) {
      // Recall if ego is performing lane change and got stuck for a while,
      // while has better progress candidate and not close to junction.
      if (selected_lane_change_state !=
          pb::LaneChangeState::LANE_CHANGE_STATE_NONE) {
        const bool should_recall_by_lc_stuck =
            (current_lane_available || left_lane_available ||
             right_lane_available) &&
            yielding_object_info != std::nullopt &&
            (yielding_object_info->first->is_stationary() ||
             yielding_object_info->first->is_primary_stationary()) &&
            yielding_object_info->second->is_dominant_constraint() &&
            distance_to_junction.has_value() &&
            distance_to_junction > assist_stuck_scene_detector_config
                                       .stuck_scene_detector_fp_rules_config()
                                       .dist_to_junction_in_meter() &&
            feature->decoupled_forward_feature().current_diff_progress_in_m() >
                assist_stuck_scene_detector_config
                    .stuck_scene_detector_fn_rules_config()
                    .valid_progress_threshold_in_m();
        // Set debug info.
        if (should_recall_by_lc_stuck) {
          fn_selection_debug->set_fn_scenario(
              pb::AssistStuckFNSelectionDebug::kLaneChangeStuck);
          return true;
        }
      }
      // Generally decide whether should trigger RA for a balance on reroute &&
      // progress.
      const bool should_recall_by_lane_selection_stuck =
          feature->decoupled_forward_feature().current_diff_progress_in_m() >
              assist_stuck_scene_detector_config
                  .stuck_scene_detector_fn_rules_config()
                  .valid_progress_threshold_in_m() &&
          feature->decoupled_forward_feature().routing_cost_diff() <= 1500 &&
          feature->decoupled_forward_feature()
                  .progress_efficiency_cost_diff() >= 0.5;
      // Set debug info.
      if (should_recall_by_lane_selection_stuck) {
        fn_selection_debug->set_fn_scenario(
            pb::AssistStuckFNSelectionDebug::kBetterProgress);
        return true;
      }
    }
  }
  return false;
}

bool MaybeFalseNegativeStuckSceneAboutNoblock(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const SpeedWorldModel& world_model,
    const pb::AssistStuckSceneDetectorConfig&
        assist_stuck_scene_detector_config) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr || !feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature() ||
      !feature->decoupled_forward_feature()
           .speed_feature()
           .has_dist_to_no_block_fence_m()) {
    return false;
  }
  const bool has_tl_fence = HasTrafficLightConstraint(feature_extractor);
  // TODO(alexma): clear redundant checks.
  const pb::SpeedConstraintMeta* dominant_constraint =
      ExtractDominantConstraint(feature_extractor);
  const bool has_dominant_constraint_about_no_block =
      dominant_constraint &&
      dominant_constraint->fence_type() == speed::pb::FenceType::kNoblock &&
      dominant_constraint->reasoner_id() ==
          speed::pb::ReasonerId_Name(speed::pb::ReasonerId::KEEP_CLEAR);
  const pb::EgoStuckFeatureAboutDecoupledSpeed& speed_feature =
      feature->decoupled_forward_feature().speed_feature();
  const auto& blockage_object_map =
      feature->routing_feature().blockage_object_map();
  const pb::TempParkedCarsNearBoundaryConfig& config =
      assist_stuck_scene_detector_config.stuck_scene_detector_fp_rules_config()
          .queuing_config()
          .temp_parked_cars_near_boundary_config();
  const int static_leading_obj_count = std::count_if(
      speed_feature.leading_object_ids().cbegin(),
      speed_feature.leading_object_ids().cend(),
      [&world_model, &config](const int64_t obj_id) {
        // For counting temp parked cars.
        const PlannerObject* obj =
            gtl::FindOrNull(world_model.planner_object_map(), obj_id);
        return obj &&
               obj->l2_distance_to_ego_m() <=
                   2 * config.interesting_dist_in_meter() &&
               (obj->is_stationary() || obj->is_primary_stationary());
      });
  const bool is_mainly_yielding_temp_parked_cars =
      std::count_if(
          speed_feature.leading_object_ids().cbegin(),
          speed_feature.leading_object_ids().cend(),
          [&world_model, &blockage_object_map, &config](const int64_t obj_id) {
            // For counting temp parked cars.
            const PlannerObject* obj =
                gtl::FindOrNull(world_model.planner_object_map(), obj_id);
            if (!obj ||
                obj->l2_distance_to_ego_m() >
                    2 * config.interesting_dist_in_meter() ||
                !(obj->is_stationary() || obj->is_primary_stationary())) {
              return false;
            }
            if (IsAVehicleWithParkedSign(*obj) ||
                IsAVehicleWithHazardSignal(*obj)) {
              return true;
            }
            const pb::BlockageObjectFeature* blockage_obj_ft =
                gtl::FindOrNull(blockage_object_map, obj_id);
            return blockage_obj_ft &&
                   blockage_obj_ft->left_inlane_clearance() >=
                       config.farther_side_inlane_dist_in_meter() &&
                   blockage_obj_ft->right_clearance_to_hard_boundary() <=
                       config.nearer_side_hard_boundary_dist_in_meter() &&
                   blockage_obj_ft->left_clearance_to_hard_boundary() >=
                       config.farther_side_hard_boundary_dist_in_meter();
          }) >= static_leading_obj_count * KStaticAgentJudgeRatio;
  const pnc_map::Lane* current_lane = world_model.GetCurrentLaneForAssist();
  if (current_lane != nullptr && (current_lane->IsRightmostVehicleLane() ||
                                  current_lane->IsRightmostDrivableLane())) {
    const bool may_select_other_candidate =
        feature->decoupled_forward_feature()
                .selection_trajectory_meta()
                .size() > 1 &&
        feature->decoupled_forward_feature().current_diff_progress_in_m() >
            assist_stuck_scene_detector_config
                .stuck_scene_detector_fn_rules_config()
                .valid_progress_threshold_in_m();
    if (may_select_other_candidate) {
      return false;
    }
  }
  return !has_tl_fence && has_dominant_constraint_about_no_block &&
         ((feature->has_progress_feature() &&
           feature->progress_feature().ego_stationary_reason() ==
               pb::BlockageObjectFeature::kTempParked) ||
          is_mainly_yielding_temp_parked_cars);
}

bool ShouldRequestbyRoutingFixedPoint(const SpeedWorldModel& world_model) {
  return world_model.stuck_avoidance_reasoner_speed_data()
      .stuck_avoidance_parameter()
      .waypoint_assist_request.expect_trigger_waypoint_assist;
}

bool ShouldRequestbyCreepSignals(
    const std::deque<std::pair<int64_t, int>>& creep_signal_queue) {
  return creep_signal_queue.size() == KDefaultQueueSizeForCreep &&
         std::accumulate(
             creep_signal_queue.begin(), creep_signal_queue.end(), 0,
             [](int init, const std::pair<int64_t, int>& elem) {
               return init + elem.second;
             }) >= KDefaultQueueSizeForCreep * KCreepRequestCycleRatio;
}

// Return true if the ego car is close to one side physical boundary.
bool is_close_to_boundary(
    const speed::pb::PhysicalBoundaryProximityInfo& pb_proximity_info,
    const pb::StuckSceneDetectorFNRulesConfig&
        stuck_scene_detector_fn_rules_config) {
  // Copy from NeedDrySteeringForWaypointAssist function in dry steering.
  // TODO(fengkaijun): Unified interfaces for self-triggering and dry
  // steering.
  for (int i = 0; i < pb_proximity_info.ra_arc_length_on_path_size(); ++i) {
    // If the trajectory length exceeds the threshold, stop checking.
    if (pb_proximity_info.ra_arc_length_on_path(i) >
        stuck_scene_detector_fn_rules_config
            .trajectory_length_threshold_for_boundary_in_meter()) {
      break;
    }
    // Returns true if the distance from the boundary is less than the
    // threshold.
    if (pb_proximity_info.abs_distance(i) <
        stuck_scene_detector_fn_rules_config
            .dist_to_physical_boundary_in_meter()) {
      return true;
    }
  }
  return false;
}

// Return true if the ego car trigger the near hard boundary false negative
// rule.
bool IsFalseNegativeStuckSceneAboutNearHardBoundary(
    const pb::StuckSceneDetectorFNRulesConfig&
        stuck_scene_detector_fn_rules_config,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  if (feature == nullptr) {
    return false;
  }
  if (!feature->has_decoupled_forward_feature() ||
      !feature->decoupled_forward_feature().has_speed_feature()) {
    return false;
  }
  // 1. Check if the minimum distance between the ego box in some sample
  // points of trajectory and physical boundary is less than the threshold.
  bool close_to_left_boundary = false, close_to_right_boundary = false;
  if (feature->decoupled_forward_feature()
          .speed_feature()
          .has_physical_boundary_proximity_info()) {
    const auto& pb_proximity_info = feature->decoupled_forward_feature()
                                        .speed_feature()
                                        .physical_boundary_proximity_info();
    if (pb_proximity_info.has_left()) {
      close_to_left_boundary = is_close_to_boundary(
          pb_proximity_info.left(), stuck_scene_detector_fn_rules_config);
    }
    if (pb_proximity_info.has_right()) {
      close_to_left_boundary = is_close_to_boundary(
          pb_proximity_info.right(), stuck_scene_detector_fn_rules_config);
    }
  }
  // 2. Check if the reference speed limits added for physical boundaries is
  // less than the threshold.
  bool stop_by_speed_limit = false;
  const auto& speed_limits_for_boundaries = feature->decoupled_forward_feature()
                                                .speed_feature()
                                                .speed_limits_for_boundaries();
  if (!speed_limits_for_boundaries.empty()) {
    for (const auto& speed_limit_for_boundary : speed_limits_for_boundaries) {
      for (const auto& limit : speed_limit_for_boundary.limits()) {
        if (limit.end_ra_arclength() >
            stuck_scene_detector_fn_rules_config
                .trajectory_length_threshold_for_speed_limit_in_meter()) {
          break;
        }
        if (limit.min_discomfort_speed() <
            stuck_scene_detector_fn_rules_config
                .min_discomfort_speed_threshold()) {
          stop_by_speed_limit = true;
          break;
        }
      }
      if (stop_by_speed_limit) {
        break;
      }
    }
  }
  // 3. Return true if ego is close left or right physical boundary and stops
  // by reference speed limit added for physical boundaries.
  return (close_to_left_boundary || close_to_right_boundary) &&
         stop_by_speed_limit;
}

// Checks signal from routing, that indicates the ego's route is blocked by map
// change areas about traffic light.
bool WillBeStuckByTrafficLightMapChangeArea(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    pb::AssistStuckFNMapChangeAreaDebug* debug) {
  if (!FLAGS_planning_enable_ra_request_for_map_change_area_by_routing) {
    return false;
  }
  const pb::SpeedConstraintMeta* dominant_constraint =
      ExtractDominantConstraint(feature_extractor);
  const bool has_dominant_constraint_about_tl_map_change =
      dominant_constraint &&
      dominant_constraint->fence_type() == speed::pb::FenceType::kMapChange &&
      dominant_constraint->reasoner_id() ==
          speed::pb::ReasonerId_Name(speed::pb::ReasonerId::TRAFFIC_LIGHT);
  const std::vector<std::shared_ptr<const ConstructionZone>>& map_change_areas =
      world_model.map_change_area_handle_param()
          .GetStuckTrafficLightChangedAssociateAreas();
  const bool will_be_stuck_by_tl_map_change_area =
      has_dominant_constraint_about_tl_map_change && !map_change_areas.empty();
  if (debug) {
    if (will_be_stuck_by_tl_map_change_area) {
      if (map_change_areas.front()) {
        debug->set_id(map_change_areas.front()->id);
      }
      debug->set_map_change_attribute(voy::TRAFFIC_LIGHT_CHANGE);
      debug->set_will_be_stuck_by_map_change_area(true);
    }
    debug->set_has_dominant_constraint_about_traffic_light_map_change(
        has_dominant_constraint_about_tl_map_change);
    debug->set_has_map_change_area_in_routing(!map_change_areas.empty());
  }
  return will_be_stuck_by_tl_map_change_area;
}

// Fetches pointer of the CZ which is the dominant constraint in speed, returns
// nullptr if no such CZ.
const ConstructionZone* FetchDominantConstructionZone(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::SpeedConstraintMeta* dominant_constraint =
      ExtractDominantConstraint(feature_extractor);
  if (dominant_constraint == nullptr) {
    return nullptr;
  }
  if (dominant_constraint->fence_type() !=
      speed::pb::FenceType::kConstructionZone) {
    return nullptr;
  }
  const ConstructionZone* const* cz = gtl::FindOrNull(
      world_model.construction_zone_ptr_map(), dominant_constraint->obj_id());
  if (cz && *cz) {
    return *cz;
  }
  return nullptr;
}

// Checks if the ego is stuck by a construction zone.
bool IsEgoStuckByConstructionZone(const ConstructionZone* dominant_cz) {
  return dominant_cz != nullptr;
}

// Updates ignorable CZ to seed, for noticing RA ops these CZ could be ignored
// by waypoint, and latching for avoiding mis-ignore during unstuck.
// TODO(lxh): Move to stuck scene analyzer.
void UpdateIgnorableConstructionZoneToSeed(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    pb::AssistStuckSeed* assist_stuck_seed) {
  const ConstructionZone* dominant_cz =
      FetchDominantConstructionZone(world_model, feature_extractor);
  if (dominant_cz == nullptr) {
    return;
  }
  assist_stuck_seed->set_dominant_cz_id(dominant_cz->id);
  if (assist::IsIgnorableCZ(*dominant_cz) &&
      !std::any_of(assist_stuck_seed->ignoring_construction_zones().begin(),
                   assist_stuck_seed->ignoring_construction_zones().end(),
                   [dominant_cz](const pb::ConstructionZoneMeta& cz_meta) {
                     return cz_meta.id() == dominant_cz->id;
                   })) {
    // Keep update, since contour might change.
    pb::ConstructionZoneMeta* cz_meta =
        assist_stuck_seed->mutable_ignoring_construction_zones()->Add();
    cz_meta->set_timestamp(base::Now());
    cz_meta->set_id(dominant_cz->id);
    cz_meta->mutable_contour()->CopyFrom(
        math::geometry::Convert<voy::Polygon2d>(dominant_cz->contour));
  }
}

// Clears ignorable CZ from seed.
// TODO(lxh): Move to stuck scene analyzer.
void ClearIgnorableConstructionZoneFromSeed(
    pb::AssistStuckSeed* assist_stuck_seed) {
  assist_stuck_seed->clear_ignoring_construction_zones();
  assist_stuck_seed->clear_dominant_cz_id();
}

// Checks if the ego is stuck by a map change construction zone.
bool IsEgoStuckByMapChangeArea(
    const bool will_be_stuck_by_traffic_light_map_change_area,
    const ConstructionZone* dominant_cz,
    pb::AssistStuckFNMapChangeAreaDebug* debug) {
  // TODO(lxh): Move these attributes to config.
  static const std::vector<voy::MapChangeAreaAttribute>& map_change_attrs = {
      voy::TRAFFIC_LIGHT_CHANGE, voy::WAYPOINT_PASSABLE};
  // Try to trigger by routing.
  if (will_be_stuck_by_traffic_light_map_change_area) {
    return true;
  }
  if (dominant_cz == nullptr) {
    return false;
  }
  const bool has_ra_map_change_attr =
      std::any_of(map_change_attrs.begin(), map_change_attrs.end(),
                  [dominant_cz](const voy::MapChangeAreaAttribute attr) {
                    return common::IsMapChangeArea(*dominant_cz, attr);
                  });
  if (!has_ra_map_change_attr) {
    return false;
  }
  debug->set_id(dominant_cz->id);
  if (!dominant_cz->map_change_area_attributes.empty()) {
    debug->set_map_change_attribute(
        dominant_cz->map_change_area_attributes.front());
  }
  return true;
}

// Extracts dominant obstacle which is likely being a perception FP object.
void ExtractDominantObstacle(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::FNRulePerceptionFPConfig& fn_perception_fp_config,
    pb::AssistStuckFNPerceptionFPDebug* perception_fp_info) {
  perception_fp_info->clear_current_dominant_obstacle_meta();
  const pb::SpeedConstraintMeta* dominant_constraint =
      ExtractDominantConstraint(feature_extractor);
  if (dominant_constraint == nullptr) {
    return;
  }
  const PlannerObject* obj = gtl::FindOrNull(world_model.planner_object_map(),
                                             dominant_constraint->obj_id());
  if (obj == nullptr) {
    return;
  }

  const auto& maybe_fp_object_types =
      fn_perception_fp_config.ignorable_object_types();
  const voy::perception::ObjectType object_type = obj->object_type();
  const bool has_maybe_fp_object_type =
      std::any_of(maybe_fp_object_types.cbegin(), maybe_fp_object_types.cend(),
                  [object_type](int maybe_fp_object_type) {
                    return object_type == maybe_fp_object_type;
                  });

  // Early return if the object doesn't have ignorable object type.
  if (!has_maybe_fp_object_type) {
    return;
  }

  const auto& non_ignorable_object_attributes =
      fn_perception_fp_config.non_ignorable_object_attributes();
  const auto& attributes = obj->tracked_object().attributes();
  bool has_non_ignorable_object_attributes = false;
  for (const auto attribute : attributes) {
    has_non_ignorable_object_attributes =
        std::any_of(non_ignorable_object_attributes.cbegin(),
                    non_ignorable_object_attributes.cend(),
                    [attribute](int non_ignorable_object_attribute) {
                      return attribute == non_ignorable_object_attribute;
                    });
  }

  // Early return if the object has non-ignorable attributes.
  if (has_non_ignorable_object_attributes) {
    return;
  }

  pb::ObstacleMeta* obstacle_meta =
      perception_fp_info->mutable_current_dominant_obstacle_meta();
  obstacle_meta->set_timestamp(base::Now());
  obstacle_meta->set_object_id(obj->id());
  obstacle_meta->set_object_type(obj->object_type());
  obstacle_meta->set_height(obj->height());
  obstacle_meta->set_width(obj->width());
  obstacle_meta->set_length(obj->length());
  obstacle_meta->set_heading(obj->heading());
  obstacle_meta->set_speed(obj->speed());
  obstacle_meta->mutable_contour()->CopyFrom(
      math::geometry::Convert<voy::Polygon2d>(obj->contour()));
}

// Checks expected ignorable object, resets if mis-match with the one in planner
// object map.
void CheckExpectedIgnoringObject(const SpeedWorldModel& world_model) {
  if (!Seed::Access<token::AssistStuckSeed_IgnoringObstacleRequest>::HasMsg(
          SpeedLastFinishedFrame())) {
    return;
  }
  const auto expected_obstacle =
      Seed::Access<token::AssistStuckSeed_IgnoringObstacleRequest>::GetMsg(
          SpeedLastFinishedFrame());

  const PlannerObject* planner_object = gtl::FindOrNull(
      world_model.planner_object_map(), expected_obstacle->object_id());
  if (planner_object == nullptr ||
      planner_object->object_type() != expected_obstacle->object_type()) {
    // Reset ignorable object's meta if the object is missing or type flicking.
    Seed::Access<token::AssistStuckSeed_IgnoringObstacleRequest>::Clear(
        SpeedCurrentFrame());
  }
}

bool MaybeFalseNegativeStuckSceneAboutPerceptionFP(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::FNRulePerceptionFPConfig& fn_perception_fp_config,
    pb::AssistStuckFNPerceptionFPDebug* perception_fp_info) {
  if (!FLAGS_planning_enable_ra_for_perception_fp_ignoring) {
    return false;
  }
  ExtractDominantObstacle(world_model, feature_extractor,
                          fn_perception_fp_config, perception_fp_info);
  return perception_fp_info->has_current_dominant_obstacle_meta();
}

// Return true if the ego car trigger the breakdown car false negative
// rule.
bool IsFalseNegativeStuckSceneAboutBreakdownCar(
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor) {
  const pb::EgoStuckFeatureOnCycle* feature =
      feature_extractor.GetLastFeature();
  // 1. Filter out illegal feature variables.
  if (feature == nullptr || !feature->has_routing_feature() ||
      !feature->routing_feature().has_current_lane_traffic_flow()) {
    return false;
  }

  if (feature_extractor.yielding_object_info() == std::nullopt) {
    return false;
  }
  const auto yield_obj = feature_extractor.yielding_object_info()->first;
  // 2. Determine whether the current lane is blocked and whether the next lane
  // is passable.
  const bool current_lane_traffic_flow_slow_moving =
      feature->routing_feature().current_lane_traffic_flow().slow_moving_cnt() >
      0;
  const bool next_lane_traffic_flow_fast_moving =
      feature->routing_feature().has_next_lane_traffic_flow() &&
      (feature->routing_feature().next_lane_traffic_flow().object_cnt() >
       feature->routing_feature().next_lane_traffic_flow().slow_moving_cnt());
  // 3. Determine whether the ego is a breakdown car.
  const bool is_stationary_breakdown_car =
      yield_obj->is_vehicle() && yield_obj->is_stationary() &&
      (yield_obj->is_open_door_vehicle() || yield_obj->is_parked_car() ||
       yield_obj->HasPerceptionAttribute(
           voy::perception::Attribute::VEHICLE_HAZARD_SIGNAL_ON) ||
       yield_obj->HasPerceptionAttribute(
           voy::perception::Attribute::VEHICLE_BRAKE_SIGNAL_ON));
  return is_stationary_breakdown_car && current_lane_traffic_flow_slow_moving &&
         next_lane_traffic_flow_fast_moving;
}

// Arbitrates rules FN / FP decisions.
// TODO(lxh): Move to a util file in stuck_detect_rules.
pb::AssistStuckRules::RuleDecision ArbitrateRules(
    const std::unordered_set<pb::AssistModelProcessReason>& fp_reasons,
    const std::unordered_set<pb::AssistModelProcessReason>& fn_reasons,
    bool should_request_immediately) {
  if (should_request_immediately) {
    return pb::AssistStuckRules::kRequestImmediately;
  }
  size_t num_fp_reasons = fp_reasons.size();
  if (fn_reasons.count(pb::AssistModelProcessReason::FN_SELECTION)) {
    if (FLAGS_planning_disable_assist_stuck_fp_queuing_when_fn_selection &&
        fp_reasons.count(pb::AssistModelProcessReason::FP_QUEUING) &&
        num_fp_reasons > 0) {
      --num_fp_reasons;
    }
    if (fp_reasons.count(pb::AssistModelProcessReason::FP_CROSSWALK) &&
        num_fp_reasons > 0) {
      --num_fp_reasons;
    }
    if (fp_reasons.count(pb::AssistModelProcessReason::FP_OCCLUSION) &&
        num_fp_reasons > 0) {
      --num_fp_reasons;
    }
    if (fp_reasons.count(pb::AssistModelProcessReason::FP_YIELD_ON_TURN) &&
        num_fp_reasons > 0) {
      --num_fp_reasons;
    }
  }
  if (num_fp_reasons > 0) {
    return pb::AssistStuckRules::kAbort;
  }
  if (!fn_reasons.empty()) {
    return pb::AssistStuckRules::kHasActivateFN;
  }
  return pb::AssistStuckRules::kNone;
}

// Decides request type according to reasoning information.
// Since fn rules check on every cycle, the information from fn rule goes with
// high prior.
pb::AssistChannelConnectRequest::RequestType DecideRequestType(
    const pb::AssistStuckRequestTypeMeta& request_type_meta) {
  if (request_type_meta.should_request_for_traffic_light_map_change_area()) {
    return pb::AssistChannelConnectRequest::kCZTrafficLightRequest;
  } else if (request_type_meta.should_request_for_abnormal_traffic_light()) {
    return pb::AssistChannelConnectRequest::kAbnormalTrafficLight;
  }
  return pb::AssistChannelConnectRequest::kStuckModelRequest;
}

pb::AssistRequest GenerateRecallSignal(const pb::AssistRequest& model_request,
                                       const bool is_fn_stuck_scene,
                                       const pb::EgoStuckDebug& debug) {
  pb::AssistRequest request_with_recall_signal;
  request_with_recall_signal.CopyFrom(model_request);
  pb::StuckDetectionRecallSignal* stuck_recall_signal =
      request_with_recall_signal.mutable_stuck_detection_recall_signal();
  stuck_recall_signal->mutable_recall_signal()->CopyFrom(
      model_request.assist_channel_connect_request());
  stuck_recall_signal->set_detected_by_model(
      debug.is_stuck_detected_by_model());
  stuck_recall_signal->set_detected_by_fn_rule(is_fn_stuck_scene);
  return request_with_recall_signal;
}

// Posts RT event when request.
void PostRequestRTEvents(const SpeedWorldModel& world_model,
                         const pb::AssistRequest& assist_request,
                         const pb::EgoStuckDebug& stuck_debug) {
  rt_event::PostRtEvent<rt_event::planner::AssistStuckModelRequest>();
  switch (stuck_debug.assist_model_process_reason()) {
    case pb::AssistModelProcessReason::FN_SELECTION:
      rt_event::PostRtEvent<rt_event::planner::ModelRequestByFNSelection>();
      break;
    case pb::AssistModelProcessReason::FN_NEAR_HARD_BOUNDARY:
      rt_event::PostRtEvent<rt_event::planner::ModelRequestByFNHardBoundary>();
      break;
    case pb::AssistModelProcessReason::FN_RA_CZ:
      if (assist_request.assist_channel_connect_request().request_type() ==
          pb::AssistChannelConnectRequest::kCZTrafficLightRequest) {
        const ConstructionZone* const* cz = gtl::FindOrNull(
            world_model.construction_zone_ptr_map(),
            stuck_debug.rules_debug().fn_map_change_area_debug().id());
        if (cz && *cz) {
          std::stringstream cz_info_stream;
          cz_info_stream << "id=" << (*cz)->id << ",source="
                         << voy::ZoneSource_Name((*cz)->zone_source);
          rt_event::PostRtEvent<
              rt_event::planner::ModelRequestByMapChangeAreaAboutTrafficLight>(
              cz_info_stream.str());
        } else {
          rt_event::PostRtEvent<
              rt_event::planner::
                  ModelRequestByMapChangeAreaAboutTrafficLight>();
        }
      } else {
        rt_event::PostRtEvent<rt_event::planner::ModelRequestByMapChangeArea>();
      }
      break;
    case pb::AssistModelProcessReason::REQUIREMENT_OF_TRAFFIC_LIGHT:
      rt_event::PostRtEvent<
          rt_event::planner::ModelRequestByRARequirementOfTrafficLight>();
      break;
    default:
      break;
  }
  if (stuck_debug.rules_debug()
          .fn_perception_fp_debug()
          .has_current_dominant_obstacle_meta()) {
    rt_event::PostRtEvent<
        rt_event::planner::ModelRequestWithMaybePerceptionFPObstacle>();
  }
}

}  // namespace

void AssistStuckSceneDetector::LoadState(
    const pb::AssistStuckSceneDetectorState& from_pb) {
  stuck_status_timer_.LoadState(from_pb.stuck_status_timer_state());
  if (from_pb.has_held_stuck_scene_request()) {
    held_stuck_scene_request_ = from_pb.held_stuck_scene_request();
  } else {
    held_stuck_scene_request_ = std::nullopt;
  }
  has_model_detect_stuck_scene_ = from_pb.has_model_detect_stuck_scene();
  has_core_planner_detect_stuck_scene_ =
      from_pb.has_core_planner_detect_stuck_scene();
  request_id_ = from_pb.request_id();

  assist_stuck_estimator_.LoadState(from_pb);
  continuous_fn_cycle_cnt_ = from_pb.continuous_fn_cycle_cnt();
  latest_refused_feedback_lasted_cycles_ =
      from_pb.latest_refused_feedback_lasted_cycles();
  model_request_lasted_cycles_ = from_pb.model_request_lasted_cycles();
  traffic_light_scene_reasoner_.LoadState(from_pb);
  yield_hold_cycles_ = from_pb.yield_hold_cycles();
  yield_temp_parked_cycles_ = from_pb.yield_temp_parked_cycles();
  right_lane_yield_cycles_ = from_pb.right_lane_yield_cycles();
  total_yielding_cycles_ = from_pb.total_yielding_cycles();
  yield_temp_parked_cycles_when_pullover_ =
      from_pb.yield_temp_parked_cycles_when_pullover();
  if (from_pb.has_assist_stuck_feature_extractor_state()) {
    ego_stuck_feature_extractor_.LoadState(
        from_pb.assist_stuck_feature_extractor_state());
  }
}

void AssistStuckSceneDetector::DumpState(
    pb::AssistStuckSceneDetectorState& to_pb) const {
  stuck_status_timer_.DumpState(*to_pb.mutable_stuck_status_timer_state());
  if (held_stuck_scene_request_.has_value()) {
    *to_pb.mutable_held_stuck_scene_request() =
        held_stuck_scene_request_.value();
  } else {
    to_pb.clear_held_stuck_scene_request();
  }
  to_pb.set_has_model_detect_stuck_scene(has_model_detect_stuck_scene_);
  to_pb.set_has_core_planner_detect_stuck_scene(
      has_core_planner_detect_stuck_scene_);
  to_pb.set_request_id(request_id_);

  assist_stuck_estimator_.DumpState(to_pb);
  to_pb.set_continuous_fn_cycle_cnt(continuous_fn_cycle_cnt_);
  to_pb.set_latest_refused_feedback_lasted_cycles(
      latest_refused_feedback_lasted_cycles_);
  to_pb.set_model_request_lasted_cycles(model_request_lasted_cycles_);
  traffic_light_scene_reasoner_.DumpState(to_pb);
  to_pb.set_yield_hold_cycles(yield_hold_cycles_);
  to_pb.set_yield_temp_parked_cycles(yield_temp_parked_cycles_);
  to_pb.set_right_lane_yield_cycles(right_lane_yield_cycles_);
  to_pb.set_total_yielding_cycles(total_yielding_cycles_);
  to_pb.set_yield_temp_parked_cycles_when_pullover(
      yield_temp_parked_cycles_when_pullover_);
  ego_stuck_feature_extractor_.DumpState(
      *(to_pb.mutable_assist_stuck_feature_extractor_state()));
}

bool AssistStuckSceneDetector::IsValidStuckDetection(
    bool is_fn_stuck_scene, pb::EgoStuckDebug* ego_stuck_debug) const {
  // 1. Check is valid model detect.
  const bool is_valid_model_predictions =
      assist_stuck_estimator_.CheckIsValidModelPredictions(
          ego_stuck_feature_extractor_, ego_stuck_debug);
  if (is_valid_model_predictions) {
    return true;
  }

  // 2. Check is valid fn detect.
  if (is_fn_stuck_scene) {
    ego_stuck_debug->set_is_stuck_detected_by_model(false);
    rt_event::PostRtEvent<rt_event::planner::ModelRequestByFNRules>();
    return true;
  }
  return false;
}

bool AssistStuckSceneDetector::AlreadyInAssistUnstuckMode(
    const SpeedWorldModel& world_model,
    pb::UnstuckStatus* in_assist_mode) const {
  // Check if in waypoint assist or direction key unstuck mode.
  if (world_model.waypoint_assist_tracker().IsWaypointAssistInvoked()) {
    *in_assist_mode = pb::UnstuckStatus::ALREADY_IN_WAYPOINT_ASSIST;
    return true;
  }
  if (FLAGS_planning_enable_remote_assist_connection) {
    if (world_model.assist_task_scheduler()
            .remote_assist_signal()
            .HasPlannerReceivedTeleopsConnectedResponse()) {
      *in_assist_mode = pb::UnstuckStatus::ALREADY_IN_LIGHT_ASSIST;
      return true;
    }
  } else {
    if (world_model.assist_directive_generator()
            .has_tele_ops_accpeted_remote_assist_req()) {
      *in_assist_mode = pb::UnstuckStatus::ALREADY_IN_LIGHT_ASSIST;
      return true;
    }
  }
  // Check if ego is reversing, for fixing FP issues like cn11023075.
  if (IsReversing()) {
    *in_assist_mode = pb::UnstuckStatus::ALREADY_IN_REVERSING;
    return true;
  }
  return false;
}

// Updates status if it is undefined.
void UpdateUnstuckStatus(const pb::UnstuckStatus new_status,
                         pb::UnstuckStatus* original_status) {
  if (*original_status == pb::UnstuckStatus::UNSTUCK_UNDEFIEND &&
      new_status != pb::UnstuckStatus::UNSTUCK_UNDEFIEND) {
    *original_status = new_status;
  }
}

bool AssistStuckSceneDetector::HasReceivedRefusedFeedback(
    const SpeedWorldModel& world_model) {
  // Find latest refused feedback and reset the cycle counter.
  const auto iter = std::find_if(world_model.assist_responses().cbegin(),
                                 world_model.assist_responses().cend(),
                                 [](const pb::AssistResponse& response) {
                                   return response.type_case() ==
                                          pb::AssistResponse::kAssistFeedback;
                                 });
  if (iter != world_model.assist_responses().cend()) {
    const auto assist_feedback_type =
        iter->assist_feedback().assist_feedback_type();
    if (assist_feedback_type == pb::AssistFeedback::kFpStuckScene ||
        assist_feedback_type == pb::AssistFeedback::kOutOfScope) {
      latest_refused_feedback_lasted_cycles_ = 1;
      return true;
    }
  }

  // Find latest refused crawl feedback and reset the cycle counter.
  const auto iter_crawl = std::find_if(
      world_model.assist_responses().cbegin(),
      world_model.assist_responses().cend(),
      [](const pb::AssistResponse& response) {
        return response.type_case() == pb::AssistResponse::kStuckSceneResponse;
      });
  if (iter_crawl != world_model.assist_responses().cend() &&
      !iter_crawl->stuck_scene_response().should_unstuck()) {
    latest_refused_feedback_lasted_cycles_ = 1;
    return true;
  }

  // Use cycle count since last refused feedback from ops to judge if should
  // make it a valid feedback.
  if (latest_refused_feedback_lasted_cycles_ != 0 &&
      latest_refused_feedback_lasted_cycles_ <
          assist_stuck_scene_detector_config_.stuck_request_config()
              .ops_refused_feedback_cycle_count()) {
    latest_refused_feedback_lasted_cycles_ += 1;
    if (latest_refused_feedback_lasted_cycles_ ==
        assist_stuck_scene_detector_config_.stuck_request_config()
            .ops_refused_feedback_cycle_count()) {
      latest_refused_feedback_lasted_cycles_ = 0;
    }
    return true;
  }
  return false;
}

bool AssistStuckSceneDetector::HasReceivedExitFeedback(
    const SpeedWorldModel& world_model) {
  // Find latest exit feedback and reset the cycle counter.
  const auto iter =
      std::find_if(world_model.assist_responses().cbegin(),
                   world_model.assist_responses().cend(),
                   [](const pb::AssistResponse& response) {
                     return IsExitUnstuckAssistModeCommand(response);
                   });
  return iter != world_model.assist_responses().cend();
}

bool AssistStuckSceneDetector::IsVoluntaryUndetectedStuckScene(
    const pb::UnstuckModeSeed& voluntary_unstuck_mode_seed,
    double ego_current_speed) const {
  (void)ego_current_speed;
  if (voluntary_unstuck_mode_seed.unstuck_modes_size() != 0) {
    return false;
  }

  return stuck_status_timer_.GetElapsedTimeInMs() >
         assist_stuck_scene_detector_config_
             .ego_stuck_voluntary_behavior_config()
             .voluntary_undetected_stuck_scene_time_threshold_in_msec();
}

bool AssistStuckSceneDetector::IsVoluntaryUnstuckFail(
    const pb::StuckSignal& stuck_signal, double ego_current_speed) const {
  return stuck_signal.last_stuck_timestamp() - stuck_signal.start_timestamp() >
             assist_stuck_scene_detector_config_
                 .ego_stuck_voluntary_behavior_config()
                 .voluntary_unstuck_time_threshold_in_msec() &&
         ego_current_speed < assist_stuck_scene_detector_config_
                                 .ego_stuck_voluntary_behavior_config()
                                 .stuck_speed_threshold_in_mps();
}

bool AssistStuckSceneDetector::IsUnderVoluntaryUnstuckMode(
    const pb::UnstuckModeSeed& voluntary_unstuck_mode_seed,
    const pb::StuckSignal& stuck_signal, double ego_current_speed,
    pb::EgoStuckDebug* ego_stuck_debug) const {
  ego_stuck_debug->set_non_voluntary_unstuck_reason(
      pb::NonVoluntaryUnstuckReason::Undefined);
  if (IsVoluntaryUndetectedStuckScene(voluntary_unstuck_mode_seed,
                                      ego_current_speed)) {
    ego_stuck_debug->set_non_voluntary_unstuck_reason(
        pb::NonVoluntaryUnstuckReason::UndetectedStuckScene);
    return false;
  }

  if (IsVoluntaryUnstuckFail(stuck_signal, ego_current_speed)) {
    ego_stuck_debug->set_non_voluntary_unstuck_reason(
        pb::NonVoluntaryUnstuckReason::UnstuckFail);
    return false;
  }

  return true;
}

void AssistStuckSceneDetector::DetectStuckScene(
    const SpeedWorldModel& world_model,
    const ManeuverReturnType& maneuver_return_type,
    const pb::UnstuckModeSeed& voluntary_unstuck_mode_seed,
    const pb::StuckSignal& stuck_signal,
    const pb::LaneChangeState& selected_lane_change_state,
    const std::shared_ptr<const pb::AssistStuckModelOutput>&
        assist_stuck_model_output_sptr,
    assist::AssistModelInput* assist_model_input,
    std::vector<pb::AssistRequest>* acceptable_assist_requests,
    pb::AssistStuckSeed* assist_stuck_seed,
    pb::BehaviorReasonersDebug* behavior_reasoner_debug) {
  pb::UnstuckStatus unstuck_status = DetectStuckSceneProcess(
      world_model, maneuver_return_type, voluntary_unstuck_mode_seed,
      stuck_signal, selected_lane_change_state, assist_stuck_model_output_sptr,
      assist_model_input, acceptable_assist_requests, assist_stuck_seed,
      behavior_reasoner_debug);
  // TODO(lxh): Move this to assist stuck scene analyzer.
  if (unstuck_status == pb::UnstuckStatus::MANUAL_DRIVING ||
      unstuck_status == pb::UnstuckStatus::MRC_REACHED ||
      unstuck_status == pb::UnstuckStatus::NOT_STATIONARY) {
    ClearIgnorableConstructionZoneFromSeed(assist_stuck_seed);
  }
  if (behavior_reasoner_debug) {
    // Marks debug info after all.
    pb::EgoStuckDebug* ego_stuck_debug =
        behavior_reasoner_debug->mutable_ego_stuck_debug();
    // TODO(liangxianghui): Move debug messages about DetectStuckSceneProcess()
    // to here.
    ego_stuck_debug->set_unstuck_status(unstuck_status);
    ego_stuck_debug->set_is_stuck_detected_by_core_planner(
        has_core_planner_detect_stuck_scene_);
    ego_stuck_debug->set_last_model_request_time(latest_model_request_time_);
    ego_stuck_debug->set_last_core_planner_request_time(
        latest_core_planner_request_time_);
    ego_stuck_debug->set_stationary_cycle_count(
        ego_stuck_feature_extractor_.n_cycles_in_low_speed());
    ego_stuck_debug->set_model_request_lasted_cycles(
        model_request_lasted_cycles_);
    ego_stuck_debug->set_latest_refused_feedback_lasted_cycles(
        latest_refused_feedback_lasted_cycles_);
    ego_stuck_debug->set_continuous_fn_cycle_cnt(continuous_fn_cycle_cnt_);
    ego_stuck_debug->mutable_request_type_meta()->CopyFrom(request_type_meta_);
    assist_stuck_estimator_.UpdateStuckLikelihoodQueueDebug(ego_stuck_debug);
  }
}

void AssistStuckSceneDetector::ProcessCreepSignals(
    const pb::RAInterventionRequirements& ra_intervention_requirements,
    std::vector<pb::AssistRequest>* acceptable_assist_requests,
    bool should_request_remote_assist_in_mrc) {
  auto light_assist_seed =
      Seed::Access<token::LightAssistSeed>::MutableMsg(SpeedCurrentFrame())
          .get();
  light_assist_seed->set_is_core_planner_suggest_creep_around_cz(false);
  light_assist_seed->set_is_core_planner_suggest_creep(false);
  // process ra_intervention_requirements to update state in seed.
  if (ra_intervention_requirements.has_planner_creep_around_requirement() &&
      ra_intervention_requirements.planner_creep_around_requirement()
              .ra_requirement_type() ==
          pb::RARequirementType::AskForRAIntervention) {
    if (ra_intervention_requirements.planner_creep_around_requirement()
            .has_core_planner_stuck_scene_special_info() &&
        ra_intervention_requirements.planner_creep_around_requirement()
                .core_planner_stuck_scene_special_info()
                .creep_around_request()
                .creep_around_objects()
                .size() != 0) {
      const auto creep_around_objects =
          ra_intervention_requirements.planner_creep_around_requirement()
              .core_planner_stuck_scene_special_info()
              .creep_around_request()
              .creep_around_objects();
      const auto iter_cz =
          std::find_if(creep_around_objects.begin(), creep_around_objects.end(),
                       [](const auto& t_obj) {
                         return t_obj.object_type() ==
                                pb::ObjectSourceType::kConstructionZone;
                       });
      if (iter_cz != creep_around_objects.end()) {
        light_assist_seed->set_is_core_planner_suggest_creep_around_cz(true);
        light_assist_seed->set_dominant_constraint_id(iter_cz->object_id());
      } else {
        light_assist_seed->set_is_core_planner_suggest_creep(true);
        light_assist_seed->set_dominant_constraint_id(
            creep_around_objects[0].object_id());
      }
    } else {
      light_assist_seed->set_dominant_constraint_id(0);
      LOG(ERROR) << "Core_planner detected stuck_scene, but No "
                    "core_planner_stuck_scene_special_info in "
                    "core_planner_requirement";
    }
  }
  const bool is_core_planner_suggest_creep =
      light_assist_seed->is_core_planner_suggest_creep();
  Seed::Access<token::AssistStuckSeed_IsEgoStuck>::SetValue(
      SpeedCurrentFrame(), is_core_planner_suggest_creep);

  const bool is_forward_crawl_suggest_creep =
      light_assist_seed->is_forward_crawl_available();
  const bool should_pub_model_request_for_creep =
      FLAGS_planning_enable_creep_connect_ra &&
      (light_assist_seed->is_core_planner_suggest_creep_around_cz() ||
       is_forward_crawl_suggest_creep);
  // Add forward creep signal to queue.
  RecordForwardCreepSignals(should_pub_model_request_for_creep,
                            KDefaultTimeOutThresholdInMs,
                            KDefaultQueueSizeForCreep, creep_signal_queue_);

  if (light_assist_seed->is_core_planner_suggest_creep_around_cz() &&
      is_forward_crawl_suggest_creep) {
    rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
        strings::StringPrintf("CZStuckRequestSource: both for object %d.",
                              light_assist_seed->dominant_constraint_id()));
  } else if (light_assist_seed->is_core_planner_suggest_creep_around_cz()) {
    rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
        strings::StringPrintf(
            "CZStuckRequestSource: core_planner for object %d.",
            light_assist_seed->dominant_constraint_id()));
  } else if (is_forward_crawl_suggest_creep) {
    rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
        strings::StringPrintf("CZStuckRequestSource: ra_forward for object %d.",
                              light_assist_seed->dominant_constraint_id()));
  }

  const bool should_pub_assist_channel_request_for_core_planner =
      FLAGS_planning_enable_publish_stuck_scene_request &&
      ra_intervention_requirements.has_planner_creep_around_requirement() &&
      !has_core_planner_detect_stuck_scene_ &&
      !light_assist_seed->is_core_planner_suggest_creep_around_cz();
  if (should_pub_assist_channel_request_for_core_planner &&
      should_request_remote_assist_in_mrc) {
    has_core_planner_detect_stuck_scene_ = true;
    // Update the request id in the first cycle when core planner detects
    // the stuck scene.
    request_id_++;
    pb::AssistRequest core_planner_request =
        GenerateAssistChannelConnectRequest(
            /*request_type=*/pb::AssistChannelConnectRequest::
                kCorePlannerRequest,
            request_id_);
    acceptable_assist_requests->push_back(core_planner_request);
    LOG(INFO) << "GenerateAssistChannelConnectRequest with request_id:"
              << request_id_;
    stuck_status_timer_.RestartTimer();
  }
}

pb::UnstuckStatus AssistStuckSceneDetector::DetectStuckSceneProcess(
    const SpeedWorldModel& world_model,
    const ManeuverReturnType& maneuver_return_type,
    const pb::UnstuckModeSeed& voluntary_unstuck_mode_seed,
    const pb::StuckSignal& stuck_signal,
    const pb::LaneChangeState& selected_lane_change_state,
    const std::shared_ptr<const pb::AssistStuckModelOutput>&
        assist_stuck_model_output_sptr,
    assist::AssistModelInput* assist_model_input,
    std::vector<pb::AssistRequest>* acceptable_assist_requests,
    pb::AssistStuckSeed* assist_stuck_seed,
    pb::BehaviorReasonersDebug* behavior_reasoner_debug) {
  if (behavior_reasoner_debug == nullptr) {
    // Since this stuck detector is based on debug message, rules and model
    // don't work without debug message.
    LOG(ERROR) << "behavior_reasoner_debug is nullptr";
    return pb::UnstuckStatus::NO_DEBUG_MESSAGE;
  }
  auto ego_stuck_debug = behavior_reasoner_debug->mutable_ego_stuck_debug();
  ego_stuck_debug->set_config_tag(
      assist_stuck_scene_detector_config_.config_tag());
  ego_stuck_debug->set_config_desc(
      assist_stuck_scene_detector_config_.config_desc());
  // 0. Extract features and inference must run every cycle.
  ego_stuck_feature_extractor_.ExtractFeatures(
      world_model, maneuver_return_type, assist_stuck_scene_detector_config_,
      assist_model_input ? &(assist_model_input->extract_feature_on_cycle)
                         : nullptr);
  // Infer gbm and dnn model in planning speed vnode, and publish inputs to
  // remote assist model vnode.
  // TODO(fkj): When the ra model vnode is tested, we will migrate the inference
  // of gbm and old dnn model from the planning speed vnode to ra model vnode.
  assist_stuck_estimator_.Infer(
      ego_stuck_feature_extractor_,
      assist_model_input ? &(assist_model_input->old_dnn_features) : nullptr,
      ego_stuck_debug);

  // Update the dnn model output and stuck_likelihood debug from remote
  // assist model vnode. Dump model output of RemoteAssistModelVNode to
  // seed.
  assist_stuck_estimator_.UpdateDNNModelOutput(
      assist_stuck_model_output_sptr, assist_stuck_seed, ego_stuck_debug);

  // Checks expected ignoring obstacle about perception FP.
  CheckExpectedIgnoringObject(world_model);

  // Updates ignorable CZ.
  UpdateIgnorableConstructionZoneToSeed(
      world_model, ego_stuck_feature_extractor_, assist_stuck_seed);

  // Here we set UnstuckStatus as default value, it will be overwritten later
  // if hit following logic.
  ego_stuck_debug->set_assist_model_process_reason(
      pb::AssistModelProcessReason::UNDEFINED_REASON);

  // Since RA FE will pub  FP or out of scope msg after exit msg,
  // then ego may be in manual mode or start to move, or just stay still,
  // we should firstly detect if refused feedback received and
  // set a long duration before next request.
  if (HasReceivedRefusedFeedback(world_model)) {
    ResetStuckDetectorStatus();
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    return pb::UnstuckStatus::OPS_REFUSED_SCENE;
  }

  // early return if pullout confirm in requests.
  if (std::find_if(acceptable_assist_requests->begin(),
                   acceptable_assist_requests->end(),
                   [](const pb::AssistRequest& request) {
                     return request.type_case() ==
                            pb::AssistRequest::TypeCase::kPullOutConfirmRequest;
                   }) != acceptable_assist_requests->end()) {
    LOG(INFO) << " pullout confirm in requests, no need to detect";
    return pb::UnstuckStatus::PULLOUT_IN_REQUEST;
  }
  // 0. If ego is not in autonomous mode or just exited ra, no need to detect
  // stuck.
  if (!world_model.robot_state().IsInAutonomousMode()) {
    ResetStuckDetectorStatus();
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    latest_refused_feedback_lasted_cycles_ = 0;
    stuck_status_timer_.RestartTimer();
    has_pub_backup_ra_request_ = false;
    return pb::UnstuckStatus::MANUAL_DRIVING;
  }
  if (world_model.mrc_request_ptr() &&
      world_model.mrc_request_ptr()->mrm_progress() == mrc::pb::FINISHED) {
    ResetStuckDetectorStatus();
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    latest_refused_feedback_lasted_cycles_ = 0;
    has_pub_backup_ra_request_ = false;
    return pb::UnstuckStatus::MRC_REACHED;
  }

  const bool is_waiting_tl = IsEgoWaittingTrafficLight(
      ego_stuck_feature_extractor_, world_model.GetCurrentLaneForAssist(),
      world_model.traffic_light_detection());
  // Call forcing recall strategy.
  const bool is_forcing_recall_triggered =
      forcing_recall_strategy_.ShouldTrigger(
          world_model, ego_stuck_feature_extractor_, is_waiting_tl,
          has_pub_backup_ra_request_,
          ego_stuck_debug->mutable_rules_debug()
              ->mutable_forcing_recall_debug());

  const pb::RAInterventionRequirements& ra_intervention_requirements =
      assist_stuck_seed->ra_intervention_requirements();

  // 1. If ego is not stationary, no need to detect stuck.
  // Traffic light map change area.
  const bool will_be_stuck_by_traffic_light_map_change_area =
      WillBeStuckByTrafficLightMapChangeArea(
          world_model, ego_stuck_feature_extractor_,
          ego_stuck_debug->mutable_rules_debug()
              ->mutable_fn_map_change_area_debug());
  request_type_meta_.set_should_request_for_traffic_light_map_change_area(
      will_be_stuck_by_traffic_light_map_change_area);
  // Abnormal traffic light.
  const bool should_request_for_abnormal_traffic_light =
      assist_stuck::ShouldIntervene(ra_intervention_requirements,
                                    ego_stuck_debug);
  request_type_meta_.set_should_request_for_abnormal_traffic_light(
      should_request_for_abnormal_traffic_light);
  if (std::abs(world_model.robot_state().current_state_snapshot().speed()) >=
          constants::kDefaultEgoNearStaticSpeedInMps &&
      !will_be_stuck_by_traffic_light_map_change_area &&
      !should_request_for_abnormal_traffic_light) {
    ResetStuckDetectorStatus();
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    latest_refused_feedback_lasted_cycles_ = 0;
    has_pub_backup_ra_request_ = false;
    return pb::UnstuckStatus::NOT_STATIONARY;
  }

  // 2. If ego received exit feedback, hold for a while.
  if (HasReceivedExitFeedback(world_model)) {
    ResetStuckDetectorStatus();
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    latest_refused_feedback_lasted_cycles_ = 0;
    return pb::UnstuckStatus::NORMAL_EXIT;
  }

  // The unstuck status going to be return.
  pb::UnstuckStatus unstuck_status = pb::UnstuckStatus::UNSTUCK_UNDEFIEND;

  // 3. No need to send request if has been in assist mode or planner has
  // received assist rejection from tele-ops, which means ego is not under
  // stuck or the stuck scene is out of RA's scope.
  pb::UnstuckStatus in_assist_mode = pb::UnstuckStatus::UNSTUCK_UNDEFIEND;
  const bool already_in_assist =
      AlreadyInAssistUnstuckMode(world_model, &in_assist_mode);
  if (already_in_assist) {
    UpdateUnstuckStatus(in_assist_mode, &unstuck_status);
  }
  if (already_in_assist &&
      !FLAGS_planner_enable_assist_stuck_detector_working_in_assist_mode) {
    ExitByAlreadyInAssist(acceptable_assist_requests);
    ResetStuckDetectorStatus();
    return unstuck_status;
  }

  if (model_request_lasted_cycles_ != 0 &&
      model_request_lasted_cycles_ <
          assist_stuck_scene_detector_config_.stuck_request_config()
              .model_request_hold_cycle_count()) {
    model_request_lasted_cycles_ += 1;
    if (model_request_lasted_cycles_ ==
        assist_stuck_scene_detector_config_.stuck_request_config()
            .model_request_hold_cycle_count()) {
      model_request_lasted_cycles_ = 0;
      // If time out since last model request was published but still stuck,
      // we reset has_model_detect_stuck_scene_ and prepare to publish a new
      // model request.fix cn11075435, cn11075473.
      has_model_detect_stuck_scene_ = false;
      LOG(INFO) << " time out since last model request, reset detector status";
    }
  }

  // 4. Handle the stuck scene request generated from core planner.
  const bool should_request_remote_assist_in_mrc =
      world_model.ShouldRequestRaUnstuckGivenMrcState();
  // TODO(Jianmo): Remove this once switching to ra intervention requirement
  // pipeline, as this is no longer needed at that point.
  const bool core_planner_detect = held_stuck_scene_request_.has_value();

  if (FLAGS_planning_enable_ra_intervention_requirement_for_creep_around) {
    // Core planner's request comes from |ra_intervention_requirements|.
    if (ra_intervention_requirements.has_planner_creep_around_requirement()) {
      const auto& creep_around_requirement =
          ra_intervention_requirements.planner_creep_around_requirement();
      // Handle core planner creep request.
      switch (creep_around_requirement.ra_requirement_type()) {
        case pb::RARequirementType::ForbidRAIntervention:
          unstuck_status = pb::UnstuckStatus::INTERVENTION_FORBID;
          LOG(ERROR)
              << "INTERVENTION_FORBID from planner_creep_around_requirement";
          break;
        case pb::RARequirementType::AskForRAIntervention:
          latest_core_planner_request_time_ =
              ra_intervention_requirements.planner_creep_around_requirement()
                  .timestamp();
          unstuck_status = pb::UnstuckStatus::CORE_PLANNER_DETECT;
          break;
        default:
          break;
      }
    }

    // Early return if forbid requirements found.
    if (unstuck_status == pb::UnstuckStatus::INTERVENTION_FORBID) {
      return unstuck_status;
    }

    // Collect signals from core planner && forward stuck scene analyzer.
    ProcessCreepSignals(ra_intervention_requirements,
                        acceptable_assist_requests,
                        should_request_remote_assist_in_mrc);
  } else {
    // Following logics should be cleared after new_core_planner_creep_interface
    // polish finished.
    if (has_core_planner_detect_stuck_scene_ && has_pub_stuck_scene_request_) {
      RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    }

    if (core_planner_detect) {
      UpdateUnstuckStatus(pb::UnstuckStatus::CORE_PLANNER_DETECT,
                          &unstuck_status);
    }
    if (core_planner_detect &&
        !FLAGS_planner_enable_assist_stuck_detector_working_in_assist_mode) {
      ExitByCorePlannerDetect(acceptable_assist_requests);
      continuous_fn_cycle_cnt_ = 0;
      return unstuck_status;
    }

    auto iter = std::find_if(
        acceptable_assist_requests->begin(), acceptable_assist_requests->end(),
        [](const pb::AssistRequest& request) {
          return request.type_case() ==
                 pb::AssistRequest::TypeCase::kStuckSceneRequest;
        });

    // For creep around request of cz, use signals from core planner && forward
    // stuck scene analyzer to generate a model request instead of core planner
    // request to connect RA.
    auto light_assist_seed =
        Seed::Access<token::LightAssistSeed>::MutableMsg(SpeedCurrentFrame());
    bool is_core_planner_suggest_creep_around_cz = false;
    light_assist_seed->set_is_core_planner_suggest_creep_around_cz(false);
    if (iter != acceptable_assist_requests->end()) {
      const auto& creep_around_objects = iter->stuck_scene_request()
                                             .creep_around_request()
                                             .creep_around_objects();
      const auto iter_cz =
          std::find_if(creep_around_objects.begin(), creep_around_objects.end(),
                       [](const auto& t_obj) {
                         return t_obj.object_type() ==
                                pb::ObjectSourceType::kConstructionZone;
                       });
      if (iter_cz != creep_around_objects.end()) {
        is_core_planner_suggest_creep_around_cz = true;
        light_assist_seed->set_is_core_planner_suggest_creep_around_cz(true);
        light_assist_seed->set_dominant_constraint_id(iter_cz->object_id());
        RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
      }
    }
    const bool is_forward_crawl_suggest_creep =
        light_assist_seed->is_forward_crawl_available();
    const bool should_pub_model_request_for_creep =
        FLAGS_planning_enable_creep_connect_ra &&
        (is_core_planner_suggest_creep_around_cz ||
         is_forward_crawl_suggest_creep);
    RecordForwardCreepSignals(should_pub_model_request_for_creep,
                              KDefaultTimeOutThresholdInMs,
                              KDefaultQueueSizeForCreep, creep_signal_queue_);

    if (is_core_planner_suggest_creep_around_cz &&
        is_forward_crawl_suggest_creep) {
      rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
          strings::StringPrintf("CZStuckRequestSource: both for object %d.",
                                light_assist_seed->dominant_constraint_id()));
    } else if (is_core_planner_suggest_creep_around_cz) {
      rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
          strings::StringPrintf(
              "CZStuckRequestSource: core_planner for object %d.",
              light_assist_seed->dominant_constraint_id()));
    } else if (is_forward_crawl_suggest_creep) {
      rt_event::PostRtEvent<rt_event::planner::CZStuckRequestSource>(
          strings::StringPrintf(
              "CZStuckRequestSource: ra_forward for object %d.",
              light_assist_seed->dominant_constraint_id()));
    }

    const bool should_pub_assist_channel_request_for_core_planner =
        FLAGS_planning_enable_publish_stuck_scene_request &&
        iter != acceptable_assist_requests->end() &&
        !has_core_planner_detect_stuck_scene_ &&
        !light_assist_seed->is_core_planner_suggest_creep_around_cz() &&
        !core_planner_detect;

    Seed::Access<token::AssistStuckSeed_IsEgoStuck>::SetValue(
        SpeedCurrentFrame(),
        should_pub_assist_channel_request_for_core_planner);

    if (should_pub_assist_channel_request_for_core_planner &&
        should_request_remote_assist_in_mrc) {
      has_core_planner_detect_stuck_scene_ = true;
      // Update the request id in the first cycle when core planner detects
      // the stuck scene.
      request_id_++;
      // Hold core planner request.
      held_stuck_scene_request_ =
          std::make_optional(iter->stuck_scene_request());
      *iter = GenerateAssistChannelConnectRequest(
          /*request_type=*/pb::AssistChannelConnectRequest::kCorePlannerRequest,
          request_id_);
      latest_core_planner_request_time_ = (*iter).timestamp();
      ego_stuck_debug->set_is_stuck_detected_by_core_planner(
          has_core_planner_detect_stuck_scene_);
      LOG(INFO) << "GenerateAssistChannelConnectRequest with request_id:"
                << request_id_;
      stuck_status_timer_.RestartTimer();
    }
  }

  // 5. Stuck model infers whether ego is under stuck with extracted features.
  // 5.1 Check FP and FN rules.
  const pb::AssistStuckRules::RuleDecision rule_decision = CheckRules(
      world_model, selected_lane_change_state, is_forcing_recall_triggered,
      will_be_stuck_by_traffic_light_map_change_area,
      should_request_for_abnormal_traffic_light, ego_stuck_debug);
  ego_stuck_debug->mutable_rules_debug()->set_rule_decision(rule_decision);
  if (rule_decision == pb::AssistStuckRules::kAbort) {
    return pb::UnstuckStatus::MODEL_FP;
  }

  if (rule_decision == pb::AssistStuckRules::kRequestImmediately) {
    latest_refused_feedback_lasted_cycles_ = 0;
  }

  // Update the request id & restart the timer in the first cycle when
  // model detects stuck scene.
  assist_stuck_estimator_.UpdateModelPredictionHistory();

  const bool is_fn_stuck_scene =
      (rule_decision == pb::AssistStuckRules::kStuckDetected ||
       rule_decision == pb::AssistStuckRules::kRequestImmediately);
  const auto& assist_stuck_estimations =
      assist_stuck_estimator_.assist_stuck_estimations();
  const bool use_scenario_dnn_model =
      assist_stuck_scene_detector_config_.assist_stuck_estimator_config()
          .use_scenario_dnn_model();
  const bool is_dnn_stuck_scene =
      FLAGS_planning_enable_remote_assist_model_vnode
          ? (use_scenario_dnn_model
                 ? assist_stuck_estimations
                       .ra_vnode_scenario_dnn_stuck_likelihood.has_value()
                 : assist_stuck_estimations.ra_vnode_dnn_stuck_likelihood
                       .has_value())
          : assist_stuck_estimations.dnn_stuck_likelihood.has_value();
  const bool is_gbm_stuck_scene =
      !FLAGS_planning_enable_remote_assist_model_vnode &&
      assist_stuck_estimations.gbm_stuck_likelihood.has_value();
  if (is_dnn_stuck_scene || is_gbm_stuck_scene || is_fn_stuck_scene) {
    if (IsValidStuckDetection(is_fn_stuck_scene, ego_stuck_debug)) {
      if (!has_model_detect_stuck_scene_) {
        request_id_++;
        has_model_detect_stuck_scene_ = true;
        stuck_status_timer_.RestartTimer();
      }
    } else {
      has_model_detect_stuck_scene_ = false;
    }
  }
  if (!FLAGS_planning_enable_ego_stuck_estimation) {
    return pb::UnstuckStatus::FLAG_DISABLE;
  }

  // 6. Do not publish assist request during core planner voluntary unstuck
  const bool waiting_core_planner =
      (rule_decision != pb::AssistStuckRules::kRequestImmediately) &&
      IsUnderVoluntaryUnstuckMode(
          voluntary_unstuck_mode_seed, stuck_signal,
          world_model.robot_state().current_state_snapshot().speed(),
          ego_stuck_debug);
  if (waiting_core_planner) {
    UpdateUnstuckStatus(pb::UnstuckStatus::WAITTING_CORE_PLANNER_UNSTUCK,
                        &unstuck_status);
  }
  if (waiting_core_planner &&
      !FLAGS_planner_enable_assist_stuck_detector_working_in_assist_mode) {
    return unstuck_status;
  }

  // 7. Publish stuck model request.
  const bool should_pub_assist_channel_request_for_stuck_model =
      model_request_lasted_cycles_ == 0 &&
      (is_fn_stuck_scene ||
       (assist_stuck_scene_detector_config_.assist_stuck_estimator_config()
                .use_dnn_model()
            ? is_dnn_stuck_scene
            : is_gbm_stuck_scene)) &&
      has_model_detect_stuck_scene_;
  if (should_pub_assist_channel_request_for_stuck_model) {
    Seed::Access<token::AssistStuckSeed_IsEgoStuck>::SetValue(
        SpeedCurrentFrame(), should_pub_assist_channel_request_for_stuck_model);
  }

  if (should_pub_assist_channel_request_for_stuck_model &&
      should_request_remote_assist_in_mrc) {
    const pb::AssistChannelConnectRequest::RequestType request_type =
        DecideRequestType(request_type_meta_);
    pb::AssistRequest model_request =
        GenerateAssistChannelConnectRequest(request_type, request_id_);
    tidal_flow_lane_scene_reasoner_.PostReason(
        *assist_stuck_seed, should_pub_assist_channel_request_for_stuck_model,
        model_request, ego_stuck_debug);
    (*request_type_meta_.mutable_last_request_time_on_type())
        [model_request.assist_channel_connect_request().request_type()] =
            model_request.timestamp();
    // Send recall signal.
    pb::AssistRequest recall_signal = GenerateRecallSignal(
        model_request, is_fn_stuck_scene, *ego_stuck_debug);
    acceptable_assist_requests->push_back(recall_signal);
    // If skipped early return, exit here without RA request and marking
    // rt_events.
    if (FLAGS_planner_enable_assist_stuck_detector_working_in_assist_mode &&
        (unstuck_status != pb::UnstuckStatus::UNSTUCK_UNDEFIEND ||
         has_core_planner_detect_stuck_scene_)) {
      if (already_in_assist) {
        ExitByAlreadyInAssist(acceptable_assist_requests);
      } else if (core_planner_detect) {
        ExitByCorePlannerDetect(acceptable_assist_requests);
      }
      return unstuck_status;
    }
    PopulateRequestInfo(world_model, *ego_stuck_debug, assist_stuck_seed,
                        &model_request);
    acceptable_assist_requests->push_back(model_request);
    latest_model_request_time_ = model_request.timestamp();
    model_request_lasted_cycles_ = 1;
    continuous_fn_cycle_cnt_ = 0;
    if (is_fn_stuck_scene) {
      PostRequestRTEvents(world_model, model_request, *ego_stuck_debug);
    }
    return pb::UnstuckStatus::MODEL_REQUEST;
  }
  if (rule_decision == pb::AssistStuckRules::kHasActivateFN) {
    UpdateUnstuckStatus(pb::UnstuckStatus::RULE_FN_DETECT, &unstuck_status);
    return unstuck_status;
  }
  return unstuck_status;
}

pb::AssistStuckRules::RuleDecision AssistStuckSceneDetector::CheckRules(
    const SpeedWorldModel& world_model,
    const pb::LaneChangeState& selected_lane_change_state,
    const bool is_forcing_recall_triggered,
    const bool will_be_stuck_by_traffic_light_map_change_area,
    const bool should_request_for_abnormal_traffic_light,
    pb::EgoStuckDebug* stuck_debug) {
  const auto& yielding_object_info =
      ego_stuck_feature_extractor_.yielding_object_info();
  UpdateYieldObjects(yielding_object_info == std::nullopt
                         ? 0
                         : yielding_object_info->first->id());
  const std::unordered_set<pb::AssistModelProcessReason>& fp_reasons =
      IsFalsePositiveStuckScene(
          world_model, ego_stuck_feature_extractor_, yield_obj_freq_umap_,
          yield_obj_queue_,
          assist_stuck_scene_detector_config_.fp_rules_switch_config(),
          assist_stuck_scene_detector_config_
              .stuck_scene_detector_fp_rules_config(),
          yield_hold_cycles_, yield_temp_parked_cycles_,
          right_lane_yield_cycles_, total_yielding_cycles_,
          yield_temp_parked_cycles_when_pullover_, stuck_debug);
  const std::unordered_set<pb::AssistModelProcessReason>& fn_reasons =
      MaybeFalseNegativeStuckScene(
          world_model, ego_stuck_feature_extractor_, selected_lane_change_state,
          will_be_stuck_by_traffic_light_map_change_area,
          assist_stuck_scene_detector_config_, stuck_debug);
  const bool is_map_change_area_stuck =
      fn_reasons.count(pb::AssistModelProcessReason::FN_RA_CZ);
  const bool is_routing_fixed_point_stuck =
      ShouldRequestbyRoutingFixedPoint(world_model);
  const bool should_request_by_forcing_recall =
      assist_stuck_scene_detector_config_.fn_rules_switch_config()
          .enable_forcing_recall() &&
      is_forcing_recall_triggered;

  // Avoid trigger RA by creep when ego is waitting red Light, in pull over
  // process or trying to move.
  const bool is_forward_creep_suggested_stuck =
      ShouldRequestbyCreepSignals(creep_signal_queue_) &&
      !IsEgoWaittingTrafficLight(ego_stuck_feature_extractor_,
                                 world_model.GetCurrentLaneForAssist(),
                                 world_model.traffic_light_detection()) &&
      !stuck_debug->rules_debug().rule_activation().fp_startup() &&
      !stuck_debug->rules_debug().rule_activation().fp_pullover();

  if (is_routing_fixed_point_stuck) {
    stuck_debug->set_unstuck_status(pb::UnstuckStatus::OUTER_RA_REQUEST);
    stuck_debug->set_assist_model_process_reason(
        pb::AssistModelProcessReason::REQUEST_FROM_ROUTING);
  }
  if (is_forward_creep_suggested_stuck) {
    rt_event::PostRtEvent<rt_event::planner::CZCreepStuckRequested>();
    stuck_debug->set_unstuck_status(pb::UnstuckStatus::OUTER_RA_REQUEST);
    stuck_debug->set_assist_model_process_reason(
        pb::AssistModelProcessReason::REQUEST_FROM_CREEP);
  }
  if (should_request_by_forcing_recall) {
    stuck_debug->set_unstuck_status(pb::UnstuckStatus::OUTER_RA_REQUEST);
    stuck_debug->set_assist_model_process_reason(
        pb::AssistModelProcessReason::FN_FORCING_RECALL);
    has_pub_backup_ra_request_ = true;
  }

  // Arbitrates abortion about the detection pipeline.
  const bool should_request_immediately =
      is_map_change_area_stuck || is_routing_fixed_point_stuck ||
      is_forward_creep_suggested_stuck || should_request_by_forcing_recall ||
      // is_ego_stuck_in_tidal_flow_lane ||
      should_request_for_abnormal_traffic_light;
  pb::AssistStuckRules::RuleDecision decision =
      ArbitrateRules(fp_reasons, fn_reasons, should_request_immediately);
  if (decision == pb::AssistStuckRules::kRequestImmediately) {
    return decision;
  } else if (decision == pb::AssistStuckRules::kAbort) {
    continuous_fn_cycle_cnt_ = std::max(0, continuous_fn_cycle_cnt_ - 1);
  } else if (decision == pb::AssistStuckRules::kHasActivateFN) {
    continuous_fn_cycle_cnt_++;
  }
  // Tests sufficiency of stuck from FN rules.
  const bool is_fn_stuck_scene =
      decision == pb::AssistStuckRules::kHasActivateFN &&
      static_cast<int>(ego_stuck_feature_extractor_.n_cycles_in_low_speed()) >
          assist_stuck_scene_detector_config_
              .stuck_scene_detector_fn_rules_config()
              .trigger_cycle_count() &&
      continuous_fn_cycle_cnt_ >
          0.7 * assist_stuck_scene_detector_config_
                    .stuck_scene_detector_fn_rules_config()
                    .trigger_cycle_count();
  if (is_fn_stuck_scene) {
    decision = pb::AssistStuckRules::kStuckDetected;
  }
  return decision;
}

pb::AssistRequest AssistStuckSceneDetector::GenerateAssistChannelConnectRequest(
    pb::AssistChannelConnectRequest::RequestType request_type,
    int64_t request_id) const {
  pb::AssistRequest request;
  request.set_timestamp(base::Now());
  pb::AssistChannelConnectRequest* assist_channel_connect_request =
      request.mutable_assist_channel_connect_request();
  assist_channel_connect_request->set_request_id(request_id);
  assist_channel_connect_request->set_request_type(request_type);
  return request;
}

void AssistStuckSceneDetector::UpdateYieldObjects(int64_t yield_object_id) {
  // Add yield_object_id to yield_obj_freq_umap_ and yield_obj_queue_.
  if (yield_obj_freq_umap_.find(yield_object_id) !=
      yield_obj_freq_umap_.end()) {
    yield_obj_freq_umap_[yield_object_id]++;
  } else {
    yield_obj_freq_umap_[yield_object_id] = 1;
  }
  yield_obj_queue_.push_back(yield_object_id);
  // Clear old yield obj id from both.
  while (
      static_cast<int>(yield_obj_queue_.size()) >
      assist_stuck_scene_detector_config_.stuck_scene_detector_fp_rules_config()
          .yielding_collection_cycles()) {
    const auto old_yielding_obj_id = yield_obj_queue_.front();
    yield_obj_queue_.pop_front();
    if (yield_obj_freq_umap_.find(old_yielding_obj_id) !=
        yield_obj_freq_umap_.end()) {
      yield_obj_freq_umap_[old_yielding_obj_id]--;
    } else {
      LOG(ERROR) << "[Exception] old_yielding_obj_id in yield_obj_queue_ was "
                    "not in yield_obj_freq_umap";
    }
  }
}

void AssistStuckSceneDetector::PopulateRequestInfo(
    const SpeedWorldModel& world_model, const pb::EgoStuckDebug& stuck_debug,
    pb::AssistStuckSeed* assist_stuck_seed, pb::AssistRequest* request) {
  if (stuck_debug.rules_debug()
          .fn_perception_fp_debug()
          .has_current_dominant_obstacle_meta()) {
    const pb::ObstacleMeta& dominant_obstacle =
        stuck_debug.rules_debug()
            .fn_perception_fp_debug()
            .current_dominant_obstacle_meta();
    request->mutable_assist_channel_connect_request()
        ->mutable_ignoring_obstacle_request()
        ->CopyFrom(dominant_obstacle);
    assist_stuck_seed->mutable_ignoring_obstacle_request()->CopyFrom(
        dominant_obstacle);
  }
  UpdateIgnorableConstructionZoneToSeed(
      world_model, ego_stuck_feature_extractor_, assist_stuck_seed);
  assist_stuck::PopulateRequirementInfo(
      assist_stuck_seed->ra_intervention_requirements(),
      request->mutable_assist_channel_connect_request());
}

void AssistStuckSceneDetector::ResetStuckDetectorStatus() {
  has_model_detect_stuck_scene_ = false;
  model_request_lasted_cycles_ = 0;
  has_core_planner_detect_stuck_scene_ = false;
  has_pub_stuck_scene_request_ = false;
  continuous_fn_cycle_cnt_ = 0;
  traffic_light_scene_reasoner_.Reset();
  yield_hold_cycles_ = 0;
  yield_temp_parked_cycles_ = 0;
  right_lane_yield_cycles_ = 0;
  total_yielding_cycles_ = 0;
  yield_temp_parked_cycles_when_pullover_ = 0;
  yield_obj_freq_umap_.clear();
  yield_obj_queue_.clear();
  forcing_recall_strategy_.Reset();
  stuck_status_timer_.RestartTimer();
}

void AssistStuckSceneDetector::ExitByAlreadyInAssist(
    std::vector<pb::AssistRequest>* acceptable_assist_requests) {
  RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
  stuck_status_timer_.RestartTimer();
}

void AssistStuckSceneDetector::ExitByCorePlannerDetect(
    std::vector<pb::AssistRequest>* acceptable_assist_requests) {
  // held_stuck_scene_request_ should be reset
  // when cancel_previous_stuck_request in stuck_scene_request;
  if (held_stuck_scene_request_->cancel_previous_stuck_request()) {
    LOG(INFO) << "reset held_stuck_scene_request_";
    held_stuck_scene_request_ = std::nullopt;
    has_core_planner_detect_stuck_scene_ = false;
  }
  // if held time out and cancel_previous_stuck_request not in, should
  // publish stuck scene request and reset held_stuck_scene_request_;
  if (held_stuck_scene_request_.has_value() &&
      !held_stuck_scene_request_->cancel_previous_stuck_request() &&
      stuck_status_timer_.GetElapsedTimeInMs() >
          assist_stuck_scene_detector_config_.stuck_request_config()
              .hold_stuck_scene_request_time_threshold_in_msec()) {
    pb::AssistRequest request;
    request.set_timestamp(base::Now());
    *request.mutable_stuck_scene_request() = held_stuck_scene_request_.value();
    // clear stuck_scene_request in acceptable_assist_requests to make sure
    // the downstream got only one request in one cycle.
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
    acceptable_assist_requests->push_back(request);
    held_stuck_scene_request_ = std::nullopt;
    has_pub_stuck_scene_request_ = true;
    LOG(INFO) << "ready to publish stuck_scene_request";
  } else {
    RemoveStuckSceneRequestIfExisted(acceptable_assist_requests);
  }
}

std::unordered_set<pb::AssistModelProcessReason>
AssistStuckSceneDetector::IsFalsePositiveStuckScene(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const std::unordered_map<int64_t, int>& yield_obj_freq_umap,
    const std::deque<int64_t>& yield_obj_queue,
    const voy::planner::AssistStuckFPRulesSwitchConfig& switch_config,
    const pb::StuckSceneDetectorFPRulesConfig&
        stuck_scene_detector_fp_rules_config,
    int& yield_hold_cycles, int& yield_temp_parked_cycles,
    int& right_lane_yield_cycles, int& total_yielding_cycles,
    int& yield_temp_parked_cycles_when_pullover,
    pb::EgoStuckDebug* ego_stuck_debug) {
  const pnc_map::Lane* current_lane = world_model.GetCurrentLaneForAssist();
  const auto& yielding_object_info = feature_extractor.yielding_object_info();
  const PlannerObject* yield_obj = yielding_object_info == std::nullopt
                                       ? nullptr
                                       : yielding_object_info->first;
  const double dist_to_yield_obj_m =
      yielding_object_info == std::nullopt
          ? 0.0
          : yielding_object_info->second->dist_to_yield_fence();
  const bool is_yielding_object_dominant_constraint =
      yielding_object_info == std::nullopt
          ? false
          : yielding_object_info->second->is_dominant_constraint();

  // The rules for FP scenes of assist stuck model. Will block stuck request
  // if returned true.
  // TODO(Xiaobo, liangxianghui): will be deprecated when model update can
  // handle following situations.
  std::unordered_set<pb::AssistModelProcessReason> process_reasons;
  pb::AssistStuckRuleActivation* rule_activation =
      ego_stuck_debug->mutable_rules_debug()->mutable_rule_activation();

  traffic_light_scene_reasoner_.Reason(world_model, process_reasons,
                                       ego_stuck_debug);

  if (IsFalsePositiveStuckSceneAboutYieldDynamicObject(
          stuck_scene_detector_fp_rules_config, yield_obj, current_lane,
          yield_obj_freq_umap, yield_obj_queue,
          feature_extractor.n_cycles_in_low_speed(),
          is_yielding_object_dominant_constraint, dist_to_yield_obj_m,
          &yield_hold_cycles, &yield_temp_parked_cycles,
          &right_lane_yield_cycles, &total_yielding_cycles)) {
    if (switch_config.enable_yield_dynamic_object()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_YIELD_DYNAMIC_OBJECT);
      process_reasons.emplace(
          pb::AssistModelProcessReason::FP_YIELD_DYNAMIC_OBJECT);
    }
    rule_activation->set_fp_yielding(true);
    total_yielding_cycles++;
  }
  if (IsFalsePositiveStuckSceneAboutPullOver(
          world_model, feature_extractor, stuck_scene_detector_fp_rules_config,
          yield_obj, is_yielding_object_dominant_constraint,
          yield_temp_parked_cycles_when_pullover)) {
    if (switch_config.enable_pull_over()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_PULL_OVER);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_PULL_OVER);
      rt_event::PostRtEvent<
          rt_event::planner::AssistStuckFPPullOverTriggered>();
    }
    rule_activation->set_fp_pullover(true);
  }
  if (IsFalsePositiveStuckSceneAboutCrosswalk(
          world_model, feature_extractor,
          stuck_scene_detector_fp_rules_config)) {
    if (switch_config.enable_crosswalk()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_CROSSWALK);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_CROSSWALK);
    }
    rule_activation->set_fp_crosswalk(true);
  }
  if (IsFalsePositiveStuckSceneAboutHolding(
          feature_extractor, stuck_scene_detector_fp_rules_config)) {
    if (switch_config.enable_holding()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_STOP_FENCE);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_STOP_FENCE);
    }
    rule_activation->set_fp_stop_fence(true);
  }
  if (IsFalsePositiveStuckSceneAboutStartup(
          feature_extractor, stuck_scene_detector_fp_rules_config)) {
    if (switch_config.enable_start_up()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_STARTUP);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_STARTUP);
    }
    rule_activation->set_fp_startup(true);
  }
  if (IsFalsePositiveStuckSceneAboutPlannedMaxSpeed(
          feature_extractor, stuck_scene_detector_fp_rules_config)) {
    if (switch_config.enable_planned_max_speed()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_MAXSPD);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_MAXSPD);
    }
    rule_activation->set_fp_max_speed(true);
  }
  if (IsFalsePositiveStuckSceneAboutOcclusion(
          world_model, feature_extractor,
          stuck_scene_detector_fp_rules_config.occlusion_config())) {
    if (switch_config.enable_occlusion()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_OCCLUSION);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_OCCLUSION);
    }
    rule_activation->set_fp_occlusion(true);
  }
  const bool is_controlled_by_red_light =
      IsEgoSuccessorLaneControlledByRedLight(
          current_lane, world_model.traffic_light_detection());

  const std::map<int64_t, lane_selection::LaneToNextJunctionInfo>&
      lane_to_junction_info_map =
          world_model.regional_map().lane_to_junction_info_map;
  std::optional<double> distance_to_junction = std::nullopt;
  if (current_lane != nullptr) {
    const auto iter = lane_to_junction_info_map.find(current_lane->id());
    if (iter != lane_to_junction_info_map.end()) {
      const lane_selection::LaneToNextJunctionInfo& lane_to_junction_info =
          iter->second;
      distance_to_junction =
          lane_to_junction_info.remaining_forward_distance_m();
    }
  }
  if (IsComplexFalsePositiveScene(
          feature_extractor, stuck_scene_detector_fp_rules_config,
          is_controlled_by_red_light, distance_to_junction)) {
    if (switch_config.enable_complex()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_EOL_WITH_RED_TL);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_EOL_WITH_RED_TL);
    }
    rule_activation->set_fp_eol(true);
  }

  const GlobalRouteSolution* global_route_solution =
      world_model.route_model().global_route_solution();
  if (global_route_solution != nullptr) {
    const std::vector<const pnc_map::Lane*>& global_route_lanes =
        global_route_solution->global_route_lanes();
    if (IsFalsePositiveStuckSceneAboutYieldingOnTurn(
            world_model, feature_extractor,
            stuck_scene_detector_fp_rules_config, current_lane,
            global_route_lanes, yield_obj, ego_stuck_debug)) {
      if (switch_config.enable_yielding_on_turn()) {
        ego_stuck_debug->set_assist_model_process_reason(
            pb::AssistModelProcessReason::FP_YIELD_ON_TURN);
        process_reasons.emplace(pb::AssistModelProcessReason::FP_YIELD_ON_TURN);
      }
      rule_activation->set_fp_on_turn(true);
    }
    if (IsFalsePositiveStuckSceneAboutQueuing(
            world_model, current_lane,
            world_model.lane_congestion_detector().lane_traffic_flow_map(),
            global_route_lanes, yield_obj, feature_extractor,
            stuck_scene_detector_fp_rules_config,
            switch_config.enable_stationary_leading_vehicle_in_queuing(),
            ego_stuck_debug->mutable_rules_debug()->mutable_fp_queuing_debug(),
            ego_stuck_debug)) {
      if (switch_config.enable_queuing()) {
        ego_stuck_debug->set_assist_model_process_reason(
            pb::AssistModelProcessReason::FP_QUEUING);
        process_reasons.emplace(pb::AssistModelProcessReason::FP_QUEUING);
      }
      rule_activation->set_fp_queuing(true);
    }
  }
  // The enable_traffic_jam switch is turned off by default.
  if (IsFalsePositiveStuckSceneAboutTrafficJam(
          world_model, current_lane, stuck_scene_detector_fp_rules_config)) {
    if (switch_config.enable_traffic_jam()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_TRAFFIC_JAM);
      process_reasons.emplace(pb::AssistModelProcessReason::FP_TRAFFIC_JAM);
    }
    rule_activation->set_fp_jam(true);
  }
  if (IsFalsePositiveStuckSceneAboutRemoteSpeedLimit(world_model)) {
    if (switch_config.enable_remote_speed_limit()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FP_REMOTE_SPEED_LIMIT);
      process_reasons.emplace(
          pb::AssistModelProcessReason::FP_REMOTE_SPEED_LIMIT);
    }
    rule_activation->set_fp_remote_speed_limit(true);
  }
  return process_reasons;
}

std::unordered_set<pb::AssistModelProcessReason>
AssistStuckSceneDetector::MaybeFalseNegativeStuckScene(
    const SpeedWorldModel& world_model,
    const ego_stuck::EgoStuckFeatureExtractor& feature_extractor,
    const pb::LaneChangeState& selected_lane_change_state,
    const bool will_be_stuck_by_traffic_light_map_change_area,
    const pb::AssistStuckSceneDetectorConfig&
        assist_stuck_scene_detector_config,
    pb::EgoStuckDebug* ego_stuck_debug) {
  const voy::planner::AssistStuckFNRulesSwitchConfig& switch_config =
      assist_stuck_scene_detector_config.fn_rules_switch_config();
  std::unordered_set<pb::AssistModelProcessReason> process_reasons;
  pb::AssistStuckRuleActivation* rule_activation =
      ego_stuck_debug->mutable_rules_debug()->mutable_rule_activation();
  if (MaybeFalseNegativeStuckSceneAboutEOL(
          feature_extractor, assist_stuck_scene_detector_config)) {
    if (switch_config.enable_fn_eol()) {
      rt_event::PostRtEvent<rt_event::planner::AssistStuckFNForEOLTriggered>();
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_EOL);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_EOL);
    }
    rule_activation->set_fn_eol(true);
  }
  if (MaybeFalseNegativeStuckSceneAboutSelection(
          feature_extractor, world_model, selected_lane_change_state,
          assist_stuck_scene_detector_config,
          ego_stuck_debug->mutable_rules_debug()
              ->mutable_fn_selection_debug())) {
    if (FLAGS_planning_enable_fn_selection_in_stuck_detect &&
        switch_config.enable_better_progress_candidate()) {
      rt_event::PostRtEvent<
          rt_event::planner::AssistStuckFNSelectionTriggered>();
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_SELECTION);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_SELECTION);
    }
    rule_activation->set_fn_selection(true);
  }
  if (IsFalseNegativeStuckSceneAboutNearHardBoundary(
          assist_stuck_scene_detector_config
              .stuck_scene_detector_fn_rules_config(),
          feature_extractor)) {
    if (switch_config.enable_near_hard_boundry()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_NEAR_HARD_BOUNDARY);
      process_reasons.emplace(
          pb::AssistModelProcessReason::FN_NEAR_HARD_BOUNDARY);
    }
    rule_activation->set_fn_near_hb(true);
  }
  if (MaybeFalseNegativeStuckSceneAboutPerceptionFP(
          world_model, feature_extractor,
          assist_stuck_scene_detector_config
              .stuck_scene_detector_fn_rules_config()
              .fn_perception_fp_config(),
          ego_stuck_debug->mutable_rules_debug()
              ->mutable_fn_perception_fp_debug())) {
    if (switch_config.enable_perception_fp()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_PERCEPTION_FP);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_PERCEPTION_FP);
    }
    rule_activation->set_fn_perception_fp(true);
  }
  if (IsFalseNegativeStuckSceneAboutBreakdownCar(feature_extractor)) {
    // The switch is turned off by default.
    if (switch_config.enable_breakdown_car()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_BREAKDOWN_CAR);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_BREAKDOWN_CAR);
    }
    rule_activation->set_fn_breakdown_car(true);
    rt_event::PostRtEvent<rt_event::planner::ModelRequestByFNBreakdownCar>();
  }
  const ConstructionZone* dominant_cz =
      FetchDominantConstructionZone(world_model, feature_extractor);
  if (IsEgoStuckByConstructionZone(dominant_cz)) {
    if (switch_config.enable_construction_zone()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_CZ);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_CZ);
    }
    rule_activation->set_fn_construction_zone(true);
  }
  if (IsEgoStuckByMapChangeArea(will_be_stuck_by_traffic_light_map_change_area,
                                dominant_cz,
                                ego_stuck_debug->mutable_rules_debug()
                                    ->mutable_fn_map_change_area_debug())) {
    if (switch_config.enable_map_change_area()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_RA_CZ);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_RA_CZ);
    }
    rule_activation->set_fn_map_change_area(true);
  }
  if (MaybeFalseNegativeStuckSceneAboutNoblock(
          feature_extractor, world_model, assist_stuck_scene_detector_config)) {
    if (switch_config.enable_fn_no_block()) {
      ego_stuck_debug->set_assist_model_process_reason(
          pb::AssistModelProcessReason::FN_NO_BLOCK);
      process_reasons.emplace(pb::AssistModelProcessReason::FN_NO_BLOCK);
    }
    rule_activation->set_fn_no_block(true);
    rt_event::PostRtEvent<rt_event::planner::AssistStuckFNNoBlockTriggered>();
  }

  lane_change_scene_reasoner_.Reason(world_model, process_reasons,
                                     ego_stuck_debug);
  hazard_signal_scene_reasoner_.Reason(world_model, process_reasons,
                                       ego_stuck_debug);

  return process_reasons;
}

}  // namespace planner
