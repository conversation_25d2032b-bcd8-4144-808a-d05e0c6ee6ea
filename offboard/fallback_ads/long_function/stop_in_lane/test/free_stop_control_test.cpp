#include "long_function/stop_in_lane/free_stop_control.h"

#include <gtest/gtest.h>

#include "long_function/long_function_type.h"

TEST(CalculateFreeStopDeceleration, ZeroVelocityTest) {
  float ego_velocity = 0.0F;
  float accel_ref = -1.0F;
  float accel = 0.0F;

  for (int i = 0; i < 1000; ++i) {
    accel = CalculateFreeStopDeceleration(ego_velocity);
  }

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateFreeStopDeceleration, NegativeVelocityTest) {
  float ego_velocity = -5.0F;
  float accel_ref = -1.0F;
  float accel = 0.0F;

  for (int i = 0; i < 1000; ++i) {
    accel = CalculateFreeStopDeceleration(ego_velocity);
  }

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateFreeStopDeceleration, HighVelocityTest) {
  float ego_velocity = 30.0F;
  float accel_ref = -5.0F;
  float accel = 0.0F;

  for (int i = 0; i < 1000; ++i) {
    accel = CalculateFreeStopDeceleration(ego_velocity);
  }

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateFreeStopDeceleration, MiddleVelocityTest) {
  float ego_velocity = 8.25F;
  float accel_ref = -1.98555F;
  float accel = 0.0F;
  for (int i = 0; i < 1000; ++i) {
    accel = CalculateFreeStopDeceleration(ego_velocity);
  }

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateFreeStopDeceleration, LowVelocityTest) {
  float ego_velocity = 1.0F;
  float accel_ref = -1.0F;
  float accel = 0.0F;

  for (int i = 0; i < 1000; ++i) {
    accel = CalculateFreeStopDeceleration(ego_velocity);
  }

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateAccelRequestByLaneLength, NoLaneAndVehicleMovingTest) {
  float ego_velocity = 10.0F;
  float accel_ref = -10.0F;
  float accel = 0.0F;
  LaneInfo left_lane_info = {.lane_id = 0U, .dx_end = 0.0F};
  LaneInfo right_lane_info = {.lane_id = 0U, .dx_end = 0.0F};

  accel = CalculateAccelRequestByLaneLength(ego_velocity, left_lane_info,
                                            right_lane_info);

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateAccelRequestByLaneLength, NoLaneAndVehicleLowVelocityTest) {
  float ego_velocity = 1.0F;
  float accel_ref = -1.0F;
  float accel = 0.0F;
  LaneInfo left_lane_info = {.lane_id = 0U, .dx_end = 0.0F};
  LaneInfo right_lane_info = {.lane_id = 0U, .dx_end = 0.0F};

  accel = CalculateAccelRequestByLaneLength(ego_velocity, left_lane_info,
                                            right_lane_info);

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateAccelRequestByLaneLength, OnlyLeftLaneTest) {
  float ego_velocity = 10.0F;
  float accel_ref = -10.0F;
  float accel = 0.0F;
  LaneInfo left_lane_info = {.lane_id = 1U, .dx_end = 20.0F};
  LaneInfo right_lane_info = {.lane_id = 0U, .dx_end = 0.0F};

  accel = CalculateAccelRequestByLaneLength(ego_velocity, left_lane_info,
                                            right_lane_info);

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateAccelRequestByLaneLength, OnlyRightLaneTest) {
  float ego_velocity = 10.0F;
  float accel_ref = -10.0F;
  float accel = 0.0F;
  LaneInfo left_lane_info = {.lane_id = 0U, .dx_end = 0.0F};
  LaneInfo right_lane_info = {.lane_id = 3U, .dx_end = 20.0F};

  accel = CalculateAccelRequestByLaneLength(ego_velocity, left_lane_info,
                                            right_lane_info);

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}

TEST(CalculateAccelRequestByLaneLength, BothLanesTest) {
  float ego_velocity = 8.25F;
  float accel_ref = -0.889822F;
  float accel = 0.0F;
  LaneInfo left_lane_info = {.lane_id = 1U, .dx_end = 43.75F};
  LaneInfo right_lane_info = {.lane_id = 3U, .dx_end = 43.75F};

  accel = CalculateAccelRequestByLaneLength(ego_velocity, left_lane_info,
                                            right_lane_info);

  ASSERT_NEAR(accel, accel_ref, 1e-4F);
}
