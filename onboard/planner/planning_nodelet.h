#ifndef ONBOARD_PLANNER_PLANNING_NODELET_H_
#define ONBOARD_PLANNER_PLANNING_NODELET_H_

#include <deque>
#include <list>
#include <map>
#include <memory>
#include <numeric>
#include <string>
#include <tuple>
#include <utility>
#include <vector>

#include "av_comm/mode_config.h"
#include "av_comm/topics.h"

#include "base/msg_traits.h"
#include "base/wall_clock_elapsed_timer.h"

#include "planner/behavior/main_planner.h"
#include "planner/behavior/types/behavior_decision.h"
#include "planner/edge_filter.h"

#include "exception_handler/exception_handler_main/trajectory_result.h"
#include "planner/ops_warning/ops_warning.h"

#include "planner/planner_debug_wrapper.h"
#include "planner/planner_health/backup_trajectory_generator.h"
#include "planner/planning_nodelet_utility.h"
#include "planner/planning_result.h"
#include "planner/planning_service.h"
#include "planner/planning_topics.h"

#include "planner/route_replan/route_replan_manager.h"

#include "planner/utility/common/config_io.h"
#include "planner/utility/config_center/planner_config_center.h"
#include "planner/utility/seed/planning_seed.h"
#include "planner/utility/state/planner_state_util.h"

#include "planner/world_model/snapshot/snapshot.h"
#include "planner/world_model/world_model.h"

#include "planner/global_object_manager/global_object_manager.h"
#include "planner_protos/customer_monitor_visual.pb.h"
#include "planner_protos/exception_handler_for_perception.pb.h"
#include "planner_protos/exception_handler_trajectory_result.pb.h"
#include "planner_protos/message_metadata.pb.h"
#include "planner_protos/ops_warning.pb.h"
#include "planner_protos/planner_infra.pb.h"
#include "planner_protos/planning_debug.pb.h"
#include "planner_protos/planning_lane_sequence.pb.h"
#include "planner_protos/planning_seed.pb.h"
#include "planner_protos/remote_assist.pb.h"
#include "planner_protos/state/planner.pb.h"
#include "planner_protos/trajectory_generator_debug.pb.h"
#include "planner_protos/trajectory_guider_output.pb.h"

#include "pnc_map_service/pnc_map_service.h"

#include "routing_protos/planning_route_state.pb.h"
#include "routing_protos/route_command.pb.h"
#include "routing_protos/route_confirmation.pb.h"
#include "routing_protos/route_solution.pb.h"

#include "varch/utils/latency/latency_data_base.h"
#include "varch/vnode/global.h"
#include "varch/vnode/system_call.h"
#include "varch/vnode/vnode.h"

#include "voy_protos/issue_tag_change.pb.h"
#include "voy_protos/system_control.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voystat/stats_planner.h"

namespace planner {
namespace planning_vnode {

namespace vnode = varch::vnode;
using SnapshotMessage = std::shared_ptr<std::unique_ptr<Snapshot>>;
using SnapshotRAMessage =
    std::shared_ptr<std::unique_ptr<std::vector<pb::AssistResponse>>>;
using LatencyDataBase = varch::utils::latency::LatencyDataBase;

struct PathResult {
  PathResult() = default;
  PathResult(DecoupledForwardPathPlannerOutput&& output,
             std::unique_ptr<SpeedWorldModel> data,
             lane_selection::LaneSequenceCandidates&& candidates,
             TurnSignalState&& state,
             ManeuverReturnType&& decoupled_maneuver_return,
             routing::pb::RouteState last_route_acceptance_state)
      : path_planner_output(std::move(output)),
        speed_world_model_data(std::move(data)),
        lane_sequence_candidates(std::move(candidates)),
        turn_signal_state(std::move(state)),
        decoupled_maneuver_return(std::move(decoupled_maneuver_return)),
        last_route_acceptance_state(last_route_acceptance_state) {}

  explicit PathResult(std::unique_ptr<SpeedWorldModel> data,
                      routing::pb::RouteState last_route_acceptance_state)
      : speed_world_model_data(std::move(data)),
        last_route_acceptance_state(last_route_acceptance_state) {}

  explicit PathResult(routing::pb::RouteState last_route_acceptance_state,
                      bool path_aborted = false)
      : last_route_acceptance_state(last_route_acceptance_state),
        path_aborted(path_aborted) {}

  // We use optional to indicate whether path_planner_output exists; if
  // world_model.Update() fails, path_planner_output is absent, and we pass this
  // information to the speed_nodelet to publish a backup trajectory generated
  // by ExceptionHandle.
  std::optional<DecoupledForwardPathPlannerOutput> path_planner_output;
  std::unique_ptr<SpeedWorldModel> speed_world_model_data;
  std::optional<lane_selection::LaneSequenceCandidates>
      lane_sequence_candidates;
  std::optional<TurnSignalState> turn_signal_state;
  std::optional<ManeuverReturnType> decoupled_maneuver_return;
  // The last route acceptance state sent by path planer
  routing::pb::RouteState last_route_acceptance_state =
      routing::pb::RouteState::kUnknownRouteState;
  // Indicates whether the path planning has been aborted due to detected
  // divergence in initial state's steering wheel angle, speed, or acceleration
  // between consecutive frames. When set to true, it triggers a frame drop and
  // aborts the path_node execution at the current frame, awaiting completion of
  // the last speed_node execution.
  bool path_aborted = false;
};

enum class SnapshotStatus { HasSnapshot, NoSnapshot };

struct PathPlanningResult {
  PathPlanningResult() : snapshot_status(SnapshotStatus::NoSnapshot) {}
  PathPlanningResult(PathResult&& result,
                     std::unique_ptr<PlannerDebugWrapper> planner_debug_wrapper,
                     SnapshotStatus status)
      : path_result(std::move(result)),
        planner_debug_wrapper(std::move(planner_debug_wrapper)),
        snapshot_status(status) {}

  PathResult path_result;
  std::unique_ptr<PlannerDebugWrapper> planner_debug_wrapper;
  SnapshotStatus snapshot_status;
};

struct SpeedVNodeParameters : public LatencyDataBase {
  using SPtr = std::shared_ptr<SpeedVNodeParameters>;
  SpeedVNodeParameters(bool snapshot_dropped,                      //
                       int64_t output_message_sequence_number,     //
                       int64_t message_metadata_timestamp,         //
                       int64_t snapshot_pub_time,                  //
                       int64_t lidar_hw_timestamp,                 //
                       int64_t path_planning_start_time,           //
                       int64_t path_planning_pub_speed_time,       //
                       PathPlanningResult&& path_planning_result,  //
                       InputMsgTsMap&& input_timestamps_map,       //
                       bool state_loaded,                          //
                       std::shared_ptr<const pb::PlannerState> planner_state,
                       bool state_dumped,
                       std::unique_ptr<pb::PlannerState> dumped_state,
                       FrameView path_frame_view)
      : snapshot_dropped(snapshot_dropped),
        output_message_sequence_number(output_message_sequence_number),
        message_metadata_timestamp(message_metadata_timestamp),
        snapshot_pub_time(snapshot_pub_time),

        lidar_hw_timestamp(lidar_hw_timestamp),
        path_planning_start_time(path_planning_start_time),
        path_planning_pub_speed_time(path_planning_pub_speed_time),
        path_planning_result(std::move(path_planning_result)),
        input_timestamps_map(std::move(input_timestamps_map)),
        state_loaded(state_loaded),
        loaded_state(std::move(planner_state)),
        state_dumped(state_dumped),
        dumped_state(std::move(dumped_state)),
        path_frame_view(path_frame_view) {}
  [[nodiscard]] int64_t GetLidarHWTimestamp() const override {
    return lidar_hw_timestamp;
  }
  // whether the snapshot was intentionally dropped due to proactive frame drop
  // logic.
  bool snapshot_dropped;
  // the sequence number to use for output. This also serves as a key for
  // versioned data structures
  int64_t output_message_sequence_number;
  // timestamp for this frame
  int64_t message_metadata_timestamp;
  // snapshot message publish time
  int64_t snapshot_pub_time;
  // lidar_hardware_timestamp for this frame
  int64_t lidar_hw_timestamp;
  // when the path planning start, used to calculate total execution
  // time for planning, which affects pruning in selection.
  int64_t path_planning_start_time;

  // when the path planning publish speed parameter, use this arg
  // for statistic path nodelet execute interval for drop msg
  int64_t path_planning_pub_speed_time;

  // result from path planner
  PathPlanningResult path_planning_result;
  // incoming topic timestamps for this frame. Path planner has filled some
  InputMsgTsMap input_timestamps_map;

  // if path planner loaded this state
  bool state_loaded;
  // the state proto that path planner loaded from
  std::shared_ptr<const pb::PlannerState> loaded_state;
  // if path planner dumped its state
  bool state_dumped;
  // the partially dumped state from path planner
  std::unique_ptr<pb::PlannerState> dumped_state;
  // FrameView used by path
  FrameView path_frame_view;
};

// Stores essential information during the planning cycle to determine
// whether the current frame should be dropped based on initial-state
// conditions.
struct PlanningCycleHistory {
  double
      steering_wheel_angle;  // This is the steering wheel angle (方向盘角度).
  double speed;
  double acceleration;
  pb::BehaviorType behavior_type;
  pb::IntentionResult::IntentionHomotopy intention;
};

static inline bool ShouldSubscribePlanningSeedTopic() {
  if (planner::FLAGS_planning_enable_sim_planner_init_state_recovery_mode) {
    CHECK(av_comm::InSimulation());
    if (planner::
            FLAGS_planning_disable_snapshot_seed_in_sim_planner_init_state_recovery_mode ||  // NOLINT
        varch::vnode::GetVArchGlobalData()->IsVirtualSim()) {
      return false;
    }
  } else {
    if (!IsPlanningSeedEnabled()) {
      return false;
    }
  }
  return true;
}

// This class defines the core planning vnode inside PlannerVNode.
// It is triggered per generation of an instance of "Snapshot", and produces
// the core planning output.

// clang-format off
class PlanningPathVNode
    : public PlanningSeedAccess,
      public vnode::VNode<
          vnode::PubMessages<SpeedVNodeParameters::SPtr,        // topic: "/planning/intra/speed_planner_params"
                             routing::pb::PlanningRouteState,   // topic: "/routing/planning_route_state"
                             pb::MessageMetadata,               // topic: "/planning/message_metadata_input",
                             hdmap::EmpiricalInfoRawDataList>,  // topic: "/planning/planning_empirical_raw_data"
          vnode::SubMessages<SnapshotMessage,                   // 0, topic: "/planning/snapshot"
                             pb::PlanningSeed,                  // 1, topic: "/bag/planning/seed"
                             pb::SpeedCompletionSignal,         // 2, topic: "/planning/intra/speed_completion_signal"
                             SnapshotRAMessage,                 // 3, topic: "/planning/snapshot_ra"
                             pb::PlannerState>                  // 4, topic: "/bag/planner/state"
          > {
  // clang-format on
 public:
  PlanningPathVNode();

  bool Init() override {
    if (!ShouldSubscribePlanningSeedTopic()) {
      CancelSubTopicByIndex(1);  // Sub message Index 1 is PlanningSeed.
    }

    if (!IsPlannerStateEnabled()) {
      CancelSubTopicByIndex(4);  // Sub message Index 4 is PlannerState.
    }

    if (av_comm::InSimulation() ||
        planner::FLAGS_planning_path_speed_are_parallel) {
      // disable speed completion as trigger in simulation
      //
      // If path and speed run in parallel, we don't need completion signal for
      // trigger, but still use it to evaluate total latency and also mark seed
      // as completion
      //
      // In simulation, without align-mode, path and speed takes no time to
      // finish one after another. So speed will always finish before next
      // snapshot message which will depend on upstream data.
      //
      // With align-mode, the path/speed will or will not overlap depends on the
      // recurring frame information, so no need to wait for completion signal.
      //

      // on car, if path/speed run in parallel, than don't wait for speed
      // completion
      UnsetSubTriggerByName("speed_completion_signal");
    }

    if (planner::FLAGS_planning_enable_sim_planner_init_state_recovery_mode) {
      if (av_comm::InSimulation()) {
        // (minghao): Seed is used during the warm-up stage in simulation when
        // related flags are enabled. After warm-up completes, there is no need
        // to use it any more, especially as a trigger topic. Since seed could
        // be sometimes missing in road test (too large to be dumped), keeping
        // it as a trigger topic could be somehow harmful when aligned mode is
        // enabled. To be more concrete, even if the required upstream messages
        // are aligned well with the recorded recurring task frame but the
        // trigger topic Seed is missing, such frame won't be eligible to be
        // triggered, and thus will be dropped, which doesn't reproduce the road
        // test execution.
        // So, here, we make seed as a non-trigger topic after warm-up completes

        // (minghao): If aligned mode is enabled, VNodes will be triggered by
        // the recorded recurring frames in simulation. Ideally, tt can be
        // implicitly guaranteed that the seed messages must arrive before the
        // trigger moment, since the seed used in nth cycle is actually
        // published by the (n-1)th cycle. However, the expected seed messages
        // might be missing due to various reasons, which may lead to a total
        // frame loss. We set seed topic as a non-trigger topic to mitigate such
        // potential execution cycle missing in aligned mode, where frame
        // alignment is more crucial than any single seed message lost.
        if (av_comm::InSimAlignedMode()) {
          UnsetSubTriggerByIndex(1);  // Sub message Index 1 is PlanningSeed.
        }

        SetWarmUpCompleteEvent([this]() {
          // Sub message Index 1 is PlanningSeed, which will no longer be
          // trigger message anymore after warm-up is completed
          this->UnsetTriggerBySubIdx(1);
          LOG(INFO) << "PlanningSeed is no longer a trigger topic!";
        });
      }
    }

    return true;
  }

  void Callback(
      std::list<std::shared_ptr<const SnapshotMessage>>& snapshot_list,
      std::list<std::shared_ptr<const pb::PlanningSeed>>& planning_seed_list,
      std::list<std::shared_ptr<const pb::SpeedCompletionSignal>>&
          planning_seed_completion_list,
      std::list<std::shared_ptr<const SnapshotRAMessage>>& snapshot_ra_list,
      std::list<std::shared_ptr<const pb::PlannerState>>& planner_state_list)
      override;

 private:
  // Executes one cycle of planning node, including local routing and
  // planning. Then publishes the result.
  PathPlanningResult ExecutePath(
      std::unique_ptr<const Snapshot> snapshot,
      std::unique_ptr<std::vector<pb::AssistResponse>>
          remote_assist_responses_ptr);

  // Plans for one cycle of planner. Populates planning_debug if it is not
  // nullptr.
  PathResult PathPlan(std::unique_ptr<const Snapshot> snapshot_ptr,
                      std::unique_ptr<std::vector<pb::AssistResponse>>
                          remote_assist_responses_ptr,
                      bool is_proposed_route_solution_rejected,
                      pb::PlanningDebug* planning_debug,
                      pb::BehaviorReasonersDebug* behavior_reasoner_debug);

  // Loads planner state from proto.
  void LoadPlannerState(const pb::PlannerState& proto);

  // Dumps planner state to proto.
  void DumpPlannerState(int64_t snapshot_timestamp, pb::PlannerState& proto);

  // Publishes the route state, including acceptance, rejection of route
  // lanes.
  void PublishRouteState(const routing::pb::RouteStatus& route_status,
                         int64_t current_timestamp,
                         bool is_last_optimal_route_accepted_by_planner,
                         bool is_ego_in_junction,
                         bool* is_proposed_route_solution_rejected);

  // Updates constraint.
  void UpdateRoutingConstraint(const routing::pb::RouteStatus& route_status,
                               bool is_new_route_solution,
                               bool is_proposed_route_solution_rejected);

  // Publishes the route state for replan route, and in some case, update
  // to new route lanes.
  void PublishRouteStateAndUpdateRouteLanesForReplan(
      const routing::pb::RouteStatus& route_status);

  // Publishes the route acceptance.
  void PublishRouteAcceptance(const routing::pb::RouteSolution& route_solution);

  // Publishes the route rejection by giving whether only rejects route lanes.
  void PublishRouteRejection(const routing::pb::RouteSolution& route_solution,
                             bool only_reject_route_lanes);

  // publish the input topic information before the output is generated.
  void PublishInputMessageMetadata(
      int64_t message_metadata_timestamp,
      const InputMsgTsMap& input_publish_timestamps,
      bool drop_current_snapshot_flag);

  // Publishes the empirical raw data.
  void PublishEmpiricalRawData();

  void InvokeSpeedVNode(
      bool snapshot_dropped,                                      //
      int64_t message_metadata_timestamp,                         //
      int64_t snapshot_pub_time,                                  //
      int64_t path_planning_start_time,                           //
      int64_t path_planning_publish_speed_time,                   //
      PathPlanningResult&& result,                                //
      InputMsgTsMap&& input_msg_ts_map,                           //
      bool state_loaded,                                          //
      std::shared_ptr<const pb::PlannerState> loaded_state_sptr,  //
      bool state_dumped,                                          //
      std::unique_ptr<pb::PlannerState> dumped_state_uptr);

  std::tuple<bool,                                     // state loaded
             std::shared_ptr<const pb::PlannerState>,  // loaded state
             bool,                                     // state dumped
             std::unique_ptr<pb::PlannerState>>        // partially dumped state
  LoadAndDumpStates(
      std::list<std::shared_ptr<const pb::PlannerState>>& planner_state_list,
      int64_t snapshot_timestamp, InputMsgTsMap* input_msg_ts_map);

  void ProactiveDropSnapshotPostProcess(
      int64_t message_metadata_timestamp, int64_t snapshot_pub_time,
      const InputMsgTsMap& input_publish_timestamps_ns,
      bool drop_current_snapshot_flag, int64_t path_planning_start_time_ns,
      int64_t path_planning_publish_speed_time_ns,
      const std::string& drop_reason);

  // Returns a non-empty vector of InitStateDivergenceInfo if any initial state
  // shows oscillations in steering wheel angle, speed, or acceleration;
  // otherwise, returns an empty vector.
  std::vector<pb::InitStateDivergenceInfo> GetInitStateDivergenceInfos(
      const PlanInitState& init_state,
      const vehicle_model::JukeIntegratedModelWithAxleRectangularShape&
          model_with_shape);
  // Data members.
  //

  // The planning config center, which provides all the planning related
  // config.
  const PlannerConfigCenter& planner_config_center_;

  std::shared_ptr<const pnc_map::PncMapService> pnc_map_service_;

  std::shared_ptr<routing::EdgeFilterManager> edge_filter_manager_;

  std::shared_ptr<GlobalObjectManager> global_object_manager_;

  // Data used across cycles, which has been stored in state.
  WorldModel world_model_;
  // Data used across cycles, which has been stored in state.
  MainPathPlanner main_path_planner_;

  TurnSignalIndicator turn_signal_indicator_;

  // The current route query timestamp to check if route is changed to send
  // out ack message. Data used across cycles, which has been stored in state.
  int64_t current_route_timestamp_ = 0;
  // The query timestamp of last rejected route.
  int64_t rejected_route_timestamp_ = -1;
  // The query timestamp of last rejected route.
  int64_t accepted_route_timestamp_ = -1;
  // The timestamp of receiving proposed route.
  int64_t first_receive_timestamp_ = -1;
  // A flag to indicate whether need publish route status for routing node.
  // We only need to publish one for new route solution.
  // Data used across cycles, which has been stored in state.
  bool need_publish_route_status_ = false;
  // The route acceptance state for the route solution of last replan query.
  // Data used across cycles, which has been stored in state.
  routing::pb::RouteState last_route_acceptance_state_ =
      routing::pb::RouteState::kPlanningAccepted;

  // The timers to record the planner's execution and cycle duration.
  base::WallClockElapsedTimer cycle_timer_;
  // The timer to track the duration of no trajectory generation.
  base::WallClockElapsedTimer no_traj_timer_;
  base::WallClockElapsedTimer latency_timer_;

  // The sequence number identifying the output message of the planning cycle.
  // Data used across cycles, which has been stored in state.
  int64_t output_message_sequence_number_ = 1;

  // Indicate whether the first planner state is loaded.
  bool first_planner_state_loaded_ = false;

  // Thread pool for pupulating route status debug in async.
  std::unique_ptr<av_comm::ThreadPool> update_route_status_debug_pool_;

  // time when the callback start, used to calculate total planning (include
  // both path and speed) computation latency,
  // in epoch nano seconds.
  int64_t callback_start_time_ns_ = 0;

  av_comm::HistoryBuffer<int64_t, PlanningCycleHistory>
      planning_three_cycle_history_;

  int64_t consecutive_parallel_planning_count_ = 0;
};

// clang-format off
class PlanningSpeedVNode
    : public PlanningSeedAccess,
    public vnode::VNode<
        vnode::PubMessages<
            pb::OpsWarning,                         // topic: "/planning/ops_warning"
            std::shared_ptr<PlannerDebugWrapper>,   // topic: "/planning/intra/planning_debug_wrapper"
            pb::PlanningSeed,                       // topic: "/planning/seed"
            pb::PlannerState,                       // topic: "/planner/state"
            pb::PlanningLaneSequence,               // topic: "/planning/planning_lane_sequence"
            pb::CustomerMonitorVisual,              // topic: "/planning/customer_monitor_visual"
            pb::AssistRequest,                      // topic: "/planning/pullout_request"
            pb::AssistRequest,                      // topic: "/planning/cloud_cell_request"
            pb::AssistRequest,                      // topic: "/planning/stuck_detection_recall_signal"
            pb::AssistRequest,                      // topic: "/planning/assist_request"
            pb::RemoteWarningSignal,                // topic: "/planning/remote_warning_signal"
            // routing::pb::PlanningRouteState,     // topic: "/routing/planning_route_state" ->  pub by path
            routing::pb::RouteCommand,              // topic: "/routing/planner_route_command"
            pb::Trajectory,                         // topic: "/planning/trajectory"
            pb::SelectnetSample,                    // topic: "/planning/selectnet_sample"
            pb::TrajectoryResult,                   // topic: "/planning/trajectory_result"
            pb::MessageMetadata,                    // topic: "/planning/message_metadata"
            pb::Trajectory,                         // topic: "/planning/ml_planner_close_loop_assist_trajectory"
            pb::SpeedCompletionSignal,              // topic: "/planning/intra/speed_completion_signal"
            voy::IssueTagChangeInfo,                // topic: "/issue_tag_change",
            assist::AssistModelInput::SPtr>,        // topic: "/planning/intra/remote_assist_model_input",
        vnode::SubMessages<
            SpeedVNodeParameters::SPtr,              // topic: "/planning/intra/speed_planner_params"
            pb::AssistStuckModelOutput,              // topic: "/planning/intra/remote_assist_model_output"
            pb::PlanningSeed,                        // topic: "/bag/planning/seed"
            pb::ExceptionHandlerForPerceptionDebug,  // topic: "/exception_handler/debug_for_perception"
            ::pb::SystemState,                       // topic: "/system_state"
            ::onboard_metrics::pb::EarlyWarning,     // topic: "/se_early_warning"
            ::voy::perception::CollisionDetection,   // topic: "/perception/collision_detection"
            ::voy::perception::SensorAbnormality,    // topic: "/perception/sensor_abnormal_pub"
            ::voy::perception::SensorAbnormality     // topic: "/perception/camera_abnormal_pub"
            >> {
  // clang-format on
 public:
  PlanningSpeedVNode();

  bool Init() override {
    if (!ShouldSubscribePlanningSeedTopic()) {
      CancelSubTopicByIndex(2);  // Sub message Index 2 is PlanningSeed.
    }
    if (!FLAGS_planning_enable_selectnet_sample_generation) {
      CancelPublisherByName(
          GetPublisherName(PlannerPubTopic::planning_selectnet_sample));
    }
    if (FLAGS_planning_disable_planner_trajectory) {
      CancelPublisherByName(
          GetPublisherName(PlannerPubTopic::planning_trajectory));
    }
    if (FLAGS_planning_enable_mlplanner_trajectory_evaluation) {
      LOG(ERROR)
          << "FLAG planning_enable_mlplanner_trajectory_evaluation is going to "
             "be deprecated. Please use other flags to enable ML mode.";
    }
    if (!FLAGS_planning_enable_remote_assist_model_vnode) {
      CancelPublisherByName(
          GetPublisherName(PlannerPubTopic::remote_assist_model_input));
    }
    return true;
  }

  void Callback(
      std::list<std::shared_ptr<const SpeedVNodeParameters::SPtr>>&
          speed_vnode_parameter_list,
      std::list<std::shared_ptr<const pb::AssistStuckModelOutput>>&
          assist_stuck_model_output_list,
      std::list<std::shared_ptr<const pb::PlanningSeed>>& planning_seed_list,
      std::list<std::shared_ptr<const pb::ExceptionHandlerForPerceptionDebug>>&
          eh_for_perception_debug_list,
      std::list<std::shared_ptr<const ::pb::SystemState>>& system_state_list,
      std::list<std::shared_ptr<const ::onboard_metrics::pb::EarlyWarning>>&
          early_warning_list,
      std::list<std::shared_ptr<const ::voy::perception::CollisionDetection>>&
          collision_detection_list,
      std::list<std::shared_ptr<const ::voy::perception::SensorAbnormality>>&
          sensor_abnormality_list,
      std::list<std::shared_ptr<const ::voy::perception::SensorAbnormality>>&
          camera_abnormality_list) override;

 private:
  void ExecuteSpeed(PathPlanningResult& path_planning_result,
                    int64_t message_metadata_timestamp,
                    int64_t path_planning_start_time,
                    const std::shared_ptr<const pb::AssistStuckModelOutput>&
                        assist_stuck_model_output_sptr,
                    const assist::RemoteWarningInputs& remote_warning_inputs,
                    InputMsgTsMap* input_timestamps_map,
                    bool load_planner_state,
                    const pb::PlannerState* planner_state_in,
                    bool dump_planner_state,
                    pb::PlannerState* planner_state_out);

  PlanningResult SpeedPlan(
      int64_t path_planning_start_time, PathResult& path_result,
      const std::shared_ptr<const pb::AssistStuckModelOutput>&
          assist_stuck_model_output_sptr,
      const assist::RemoteWarningInputs& remote_warning_inputs,
      assist::AssistModelInput* assist_model_input,
      pb::PlanningDebug* planning_debug,
      pb::BehaviorReasonersDebug* behavior_reasoner_debug,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Publishes the planning related results.
  void PublishPlanningResult(
      std::unique_ptr<SpeedWorldModel> speed_world_model_data,
      std::optional<ManeuverReturnType> decoupled_maneuver_return,
      std::shared_ptr<PlannerDebugWrapper> planner_debug_wrapper,
      PlanningResult* planning_result,
      pb::CustomerMonitorVisual* customer_monitor_visual,
      std::shared_ptr<assist::AssistModelInput> assist_model_input);

  void HandleSeed(
      std::list<std::shared_ptr<const pb::PlanningSeed>>& bag_seed_list,
      const FrameView& path_frame_view);

  // Loads planner state from proto.
  void LoadPlannerState(const pb::PlannerState& proto);

  // Dumps planner state to proto.
  void DumpPlannerState(pb::PlannerState& proto);

  // Handles the planning node exception i.e. no planned trajectory.
  // Currently generates backup trajectory or publishes fault code.
  void HandlePlanningNodeletException(
      const RobotState& robot_state, int64_t prediction_timestamp,
      const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
      bool should_planner_respond_mrc, PlanningResult* planning_result,
      pb::CustomerMonitorVisual* customer_monitor_visual);

  // Gets abnormal sequence signal.
  pb::AbnormalSequenceSignal GetAbnormalSequenceSignal(
      const GlobalRouteSolution* global_route_solution,
      bool should_add_route_replan_fail_fault_code);

  // Publishes the message metadata that contains information on the input and
  // output messages of the current planning iteration.
  void PublishFullMessageMetadata(int64_t message_metadata_timestamp,
                                  const InputMsgTsMap& input_publish_timestamps,
                                  bool drop_current_snapshot_flag);

  // Publishes the issue tag change info.
  void PublishPlannerIssueTagChangeInfo(
      const std::optional<voy::IssueTagChangeInfo>& issue_tag_change_info);

  ////////////////////
  // Private data
  //

  void ProactiveDropPathPostProcess(const std::string& drop_reason);

  // The planning config center, which provides all the planning related
  // config. owned by planning_node, shared by both path and speed.
  const PlannerConfigCenter& planner_config_center_;

  /****************************************************************
   * long live service, internal state need to be captured if exists.
   ****************************************************************/
  ops_warning::OpsWarningGenerator ops_warning_generator_;
  CustomerMonitorVisualMsgGenerator customer_monitor_visual_msg_generator_;
  // The manager deciding if and when route replan query should be published.
  RouteReplanManager route_replan_manager_;

  MainSpeedPlanner main_speed_planner_;

  /****************************************************************
   * frame level temp, can be set to default at start of each frame
   ****************************************************************/
  // The timers to record the planner's execution and cycle
  // duration.
  base::WallClockElapsedTimer cycle_timer_;

  // The timer to track the duration of no trajectory generation.
  base::WallClockElapsedTimer no_traj_timer_;
  base::WallClockElapsedTimer latency_timer_;
  // output message sequence number, pass down from path
  int64_t output_message_sequence_number_ = 1;
  // The route command.
  // Data used across cycles, which has been stored in state.
  std::shared_ptr<routing::pb::RouteCommand> route_command_;

  std::unique_ptr<SpeedWorldModel> speed_world_model_;

  // A flag to indicate a route completion command has been published. We only
  // need to send one per route.
  // Data used across cycles, which has been stored in state.
  bool has_published_route_completion_ = false;

  // The current route query timestamp to check if route is changed to send
  // out ack message. Data used across cycles, which has been stored in state.
  int64_t current_route_timestamp_ = 0;

  // A flag to indicate an immediate pull over request has been published. We
  // only need to send in the first cycle in which immediate pull over request
  // is set to true or when planning need replan query and immediate pull over
  // request is true.
  // Data used across cycles, which has been stored in state.
  bool has_published_immediate_pullover_request_ = false;
  // A flag to indicate that planning needs to send a request to routing that
  // trigger immediate pull over.
  // Data used across cycles, which has been stored in state.
  bool need_publish_immediate_pullover_request_ = false;
  // A flag to indicate that planning needs to send a request to routing that
  // cancel immediate pull over.
  // Data used across cycles, which has been stored in state.
  bool need_publish_cancel_immediate_pullover_request_ = false;

  // A flag to indicate whether routing node used to send replan fail signal,
  // and has not been recovered.
  bool has_send_route_replan_signal_ = false;

  // How many times we load the planner state. It's possible to load state
  // every frame depends on the flags and running state (e.g. in warmup)
  int planner_state_loaded_cnt_ = 0;

  // Thread pool for object deconstruction in async.
  std::unique_ptr<av_comm::ThreadPool> async_destruction_pool_;

  // Thread pool for pupulating route status debug in async.
  std::unique_ptr<av_comm::ThreadPool> update_route_status_debug_pool_;

  // Record latest assist stuck model output.
  // 1. When PlanningSpeedVNode subscribes a latest assist stuck model output
  // from RemoteAssistModelVNode, latest_assist_stuck_model_output_uptr_ will be
  // given unique ownership of the subscribed latest outputs.
  // 2. The only way to destroy latest_assist_stuck_model_output_uptr_ is to
  // transfer unique ownership of the latest output to
  // `PlanningSpeedVNode::ExecuteSpeed` function's incoming parameter.
  // 3. If proactive frame drop occurs at planning speed, planner will not
  // execute `PlanningSpeedVNode::ExecuteSpeed`, so this unique ptr will not
  // transfer unique ownership of the latest output, meaning it will keep the
  // latest output until the planner executes `PlanningSpeedVNode::ExecuteSpeed`
  // normally.
  std::unique_ptr<const pb::AssistStuckModelOutput>
      latest_assist_stuck_model_output_uptr_;
};

}  // namespace planning_vnode
}  // namespace planner

#endif  // ONBOARD_PLANNER_PLANNING_NODELET_H_
