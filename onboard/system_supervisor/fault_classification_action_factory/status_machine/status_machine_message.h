#ifndef ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_STATUS_MACHINE_STATUS_MACHINE_MESSAGE_H_
#define ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_STATUS_MACHINE_STATUS_MACHINE_MESSAGE_H_

#include <atomic>
#include <memory>
#include <mutex>  //NOLINT
#include <string>
#include <thread>  //NOLINT
#include <tuple>
#include <unordered_map>
#include <utility>
#include <vector>

#if !defined(ONBOARD_ONLY)
#include "sim_protos/fallback_level2_state.pb.h"
#include "voy_protos/perception_collision.pb.h"
#endif

#include "action_factory_protos/collision_upstream_faults.pb.h"
#include "action_factory_protos/system_supervisor_mrc_triggers.pb.h"
#include "base/now.h"
#include "common.h"
#include "mrc_protos/mrc_request.pb.h"
#include "planner_protos/mrc_immediate_pullover.pb.h"
#include "platform_interface/ss_gflags.h"
#include "restart_protos/restart.pb.h"
#include "varch/vnode/message_util.h"
#include "voy_protos/event_handle.pb.h"
#include "voy_protos/system_control.pb.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_protos/vehicle_ground_assist.pb.h"

namespace system_supervisor::action_factory {
namespace status_machine {
bool IsAnyCameraNeedSelfCleaning(const std::string& node_name,
                                 const pb::Health& health);

namespace message_util = varch::vnode::message_util;
class StatusMachineMessage {
 public:
  StatusMachineMessage();
  explicit StatusMachineMessage(const std::string& car_id);
  ~StatusMachineMessage();
#if defined(ONBOARD_ONLY)
  void CheckReEngageState();
  void ParamCheckWorker();
#endif
  void ParseMessage(const ::pb::SystemState& system_state);
  void ParseGatewayMessage(const ::voy::Canbus& canbus_message);
  void ParseRestoreRecordMessage(
      const ::service_restore::pb::RestoreRecord& restore_message);
  void ParseVehicleBodyInfoMessage(
      const ::voy::VehicleBodyInfo& vehicle_body_info);
  void HandleFallbackl2TriggerMethod(Fallbackl2TriggerMethod trigger_method);
  Fallbackl2TriggerMethod GetTriggerMethodOfFallbackl2() const;
  void SendAdsStateToFallback(bool trigger_fallback_adas);
  void SetCurrentControlWayOfFallbackl2(
      const Fallbackl2TriggerMethod& current_control_way_of_fallbackl2);
  void InitFaultLevelFromJson(
      const ::action_factory::pb::CollisionUpstreamFaults&
          collision_upstream_faults);
#if !defined(ONBOARD_ONLY)
  void UpdateFbControlStateInSim(
      const ::fallback_level2::FbAcuAvailable& message) {
    ::voy::Canbus canbus_message;
    canbus_message.mutable_vehicle_detail_info()->set_fallback_control_status(
        message.fallback_control_status());
    canbus_message.mutable_vehicle_detail_info()->set_fallback_rolling_counter(
        message.fb_acu_rolling_counter());
    canbus_message.mutable_vehicle_detail_info()
        ->set_redundant_fb_control_status(message.fallback_control_status());
    canbus_message.mutable_vehicle_detail_info()
        ->set_redundant_fb_rolling_counter(message.fb_acu_rolling_counter());
    ParseGatewayMessage(canbus_message);
  }

#else
  void ParseVehicleGroundAssistMessage(
      const ::voy::VehicleGroundAssist& vehicle_ground_assist);
  void SetIsEnableReEngage(bool is_enable_re_engage);
  bool GetIsEnableReEngage() const { return is_enable_re_engage_; }
  std::string GetOddType() { return odd_type_; }
  bool GetIsKeepDefense() const { return is_keep_defense_; }
  voy::Canbus::Source GetEngageSource() const {
    return canbus_.engage_source_type();
  }
  void SetOddType(const std::string& odd_type) { odd_type_ = odd_type; }
  void AutoDisEngage();
  bool GetIsDriverlessScene();
#endif
  void ParseFallbackPlanningMessage(const ::planner::pb::Trajectory& message);
  void UpdateCollsionMessageRequired(const pb::SystemState& system_state);
  void ParseMrcRequest(const ::mrc::pb::MrcRequest& message);
  void ParseGroundSupportCommand(const ::voy::VehicleGroundAssist& message);
  void UpdateDelayToRemindOpsTakeoverTimer();
  bool IsEventHandleProcessingCompleted(::pb::EventHandleInfo& event_info);
  StatusBitType GetCurrStateBit() const { return curr_status_; }
  StatusBitType GetPrevStateBit() const { return prev_status_; }
  void UpdateUnmannedMessage();
  void UpdateStateBit(const StatusBitType prev_status,
                      const StatusBitType curr_status) {
    prev_status_ = prev_status;
    curr_status_ = curr_status;
  }
  MrmStatusType GetMrmState() const { return mrc_state_; }

  std::unordered_map<std::string, std::tuple<uint8_t, uint8_t, uint8_t> >
  GetCollisionUpstreamMap() const {
    return collision_upstream_map_;
  }
  void AddItemIntoCollisionUpstreamMap(
      const std::pair<std::string, std::tuple<uint8_t, uint8_t, uint8_t> >
          item) {
    collision_upstream_map_.insert(item);
  }
  ::action_factory::pb::SystemSupervisorMrcTriggers GetMrmTriggerMessage()
      const {
    return mrm_trigger_message_;
  }
  void SetMrmTriggerMessage(
      const ::action_factory::pb::SystemSupervisorMrcTriggers&
          mrc_trigger_message) {
    mrm_trigger_message_ = mrc_trigger_message;
  }

  SelfDrivingStatus GetSelfDrivingStatus();

  bool IsDoorStatusAbnormal() const { return is_door_status_abnormal_; }
  void SetIsDoorStatusAbnormal(const bool is_door_status_abnormal) {
    is_door_status_abnormal_ = is_door_status_abnormal;
  }
  bool IsRecoveryTimeout() const { return is_recovery_timeout_; }
  void SetIsRecoveryTimeout(const bool is_recovery_timeout) {
    is_recovery_timeout_ = is_recovery_timeout;
  }

  // is_ground_support_parking_retention_active_
  bool IsGroundSupportParkingRetentionActive() const {
    return is_ground_support_parking_retention_active_;
  }

  int64_t GetGroundSupportParkingRetentionActiveStartTime() const {
    return ground_support_parking_retention_active_start_time_;
  }

  void SetParkedRetentionAuto() {
    is_parked_retention_auto_ = true;
    is_parked_retention_fallback_autonomy_ = false;
    is_parked_retention_fallback_adas_ = false;
  }

  void SetParkedRetentionFallbackAutonomy() {
    is_parked_retention_auto_ = false;
    is_parked_retention_fallback_autonomy_ = true;
    is_parked_retention_fallback_adas_ = false;
  }

  void SetParkedRetentionFallbackAdas() {
    is_parked_retention_auto_ = false;
    is_parked_retention_fallback_autonomy_ = false;
    is_parked_retention_fallback_adas_ = true;
  }

  bool IsParkedRetentionAuto() { return is_parked_retention_auto_; }

  bool IsParkedRetentionFallbackAutonomy() {
    return is_parked_retention_fallback_autonomy_;
  }

  bool IsParkedRetentionFallbackAdas() {
    return is_parked_retention_fallback_adas_;
  }

  void ActiveParkingRetention(const std::string& source) {
    if (!is_ground_support_parking_retention_active_) {
      ground_support_parking_retention_active_start_time_ = base::Now();
    }
    is_ground_support_parking_retention_active_ = true;
    LOG(INFO) << "Active Parking Retention by " << source;
  }

  void ReleaseParkingRetention(const std::string& source) {
    is_ground_support_parking_retention_active_ = false;
    is_parked_retention_auto_ = false;
    is_parked_retention_fallback_autonomy_ = false;
    is_parked_retention_fallback_adas_ = false;
    ground_support_parking_retention_active_start_time_ = 0;
    LOG(INFO) << "Release Parking Retention by " << source;
  }

  bool IsInMrmProcess() const { return is_in_mrm_process_; }
  void SetIsInMrmProcess(const bool is_in_mrm_process) {
    is_in_mrm_process_ = is_in_mrm_process;
  }

  bool IsInMrmProcessLastFrame() const { return is_in_mrm_process_last_frame_; }
  void SetIsInMrmProcessLastFrame(const bool is_in_mrm_process_last_frame) {
    is_in_mrm_process_last_frame_ = is_in_mrm_process_last_frame;
  }
  void SetSystemStatus(const SystemStatusType system_status) {
    system_status_ = system_status;
  }

  void SetLastSystemStatus(const SystemStatusType system_status) {
    last_system_status_ = system_status;
  }

  bool IsCameraNeedSelfCleaning() const {
    return is_camera_need_self_cleaning_;
  }
  bool IsEnableCameraCleaningFunction() const {
    return is_enable_camera_cleaning_function_;
  }

  // cannot be const because its function needs to be called
  StatusMachineMessage& GetStatusBitMachineMessage() { return *this; }
  SystemStatusType GetSystemStatus() const { return system_status_; }
  SystemStatusType GetLastSystemStatus() const { return last_system_status_; }
  ::voy::VehicleMode GetVehicleMode() const { return vehicle_mode_; }
  ::voy::VehicleMode GetLastVehicleMode() const { return last_vehicle_mode_; }
  bool GetIsHighFrequencyFault() const { return is_high_frequency_fault_; }
  pb::SystemState::Disengage GetIsNeedOpsTakenOver() const {
    return is_need_ops_taken_over_;
  }
  pb::SystemState::Value GetSystemValue() { return system_value_; }
  bool IsDelayToRemindOpsTakeover() const {
    return is_delay_to_remind_ops_takeover_;
  }

  void SetIsNeedOpsTakenOver(
      pb::SystemState::Disengage is_need_ops_taken_over) {
    is_need_ops_taken_over_ = is_need_ops_taken_over;
  }
  void StartDelayRemindOpsTakeoverTimer() {
    delay_remind_ops_takeover_timer_ = base::Now();
  }
  void StartFallbackTakeoverTimer() { fallback_takeover_timer_ = base::Now(); }

  bool IsUptoLimitTime(int64_t timer, int threshold);
  void StopFallbackTakeoverTimer() { fallback_takeover_timer_ = 0; }

  void StopDelayRemindOpsTakeoverTimer() {
    delay_remind_ops_takeover_timer_ = 0;
  }
  void SetModeInSim(const voy::VehicleMode& mode) {
    vehicle_mode_in_sim_ = mode;
    is_modify_mode_in_sim_flag_ = true;
  }
  voy::VehicleMode GetModeInSim();
  int64_t GetDelayRemindOpsTakeoverTimer() const {
    return delay_remind_ops_takeover_timer_;
  }
  // Obtain the time to enter the fallback state
  int64_t GetFallbackTakeoverTimer() const { return fallback_takeover_timer_; }
  // Obtain status whether high-frequency takeover voice has been broadcasted
  bool GetIsHighFrequencyVoiceBroadcasted() const {
    return is_high_frequency_voice_broadcasted_;
  }

  void SetIsHighFrequencyVoiceBroadcasted(
      bool is_high_frequency_voice_broadcasted) {
    is_high_frequency_voice_broadcasted_ = is_high_frequency_voice_broadcasted;
  }
  std::string GetDefaultSsEventId() const { return default_ss_event_id_; }
  EventHandleState GetIsTakeoverSuccess() const { return is_takeover_success_; }
  void SetIsFallbackL2TakingOverBeforeOps(
      const bool is_fallbackl2_taking_over_before_ops) {
    is_fallbackl2_taking_over_before_ops_ =
        is_fallbackl2_taking_over_before_ops;
  }

  bool GetIsFallbackL2TakingOverBeforeOps() const {
    return is_fallbackl2_taking_over_before_ops_;
  }

  int GetModeChangeWaitMs() const {
    if (FLAGS_enable_action_factory_test) {
      return kModeChangeWaitMsForTest;
    } else {
      return kModeChangeWaitMs;
    }
  }

  int GetWaitingAdsFullRecoveryTimeInMs() const {
    if (FLAGS_enable_action_factory_test) {
      return kWaitingAdsFullRecoveryTimeInMsForTest;
    } else {
      return kWaitingAdsFullRecoveryTimeInMs;
    }
  }

  int GetWaitingFallbackAutonomuFullRecoveryTimeInMs() const {
    if (FLAGS_enable_action_factory_test) {
      return kWaitingFallbackAutonomuFullRecoveryTimeInMsForTest;
    } else {
      return kWaitingFallbackAutonomuFullRecoveryTimeInMs;
    }
  }
  bool IsVehicleParkingCompleted() const {
    return is_vehicle_parking_completed_;
  }
  bool IsEnableFallbackAdas() const { return is_enable_fallback_adas_; }
  bool IsEnableFallbackAutonomy() const { return is_enable_fallback_autonomy_; }
  MrmStatusType GetLastMrmState() const { return last_mrc_state_; }
  void SetLastMrmState(const MrmStatusType& last_mrc_state) {
    last_mrc_state_ = last_mrc_state;
  }
  void SetCollisionMessageRequired(
      const pb::CollisionMessageRequired& message) {
    collision_message_required_ = message;
  }
  pb::CollisionMessageRequired GetCollisionMessageRequired() const {
    return collision_message_required_;
  }
  void SetMrmState(const MrmStatusType& mrc_state) { mrc_state_ = mrc_state; }
  uint8_t GetStopValue() const { return stop_value_; }
  uint8_t GetRiskValue() const { return risk_value_; }
  uint8_t GetSystemAvailable() const { return system_available_; }
  void UpdateSystemLevel(const std::string& system_level);

  void SetIsRAEBFault(const bool is_ra_eb_fault) {
    is_ra_eb_fault_ = is_ra_eb_fault;
  }
  bool GetIsRAEBFault() { return is_ra_eb_fault_; }

  void SetVehicleMode(const ::voy::VehicleMode& vehicle_mode) {
    vehicle_mode_ =
        FLAGS_enable_simulation_switch ? vehicle_mode_in_sim_ : vehicle_mode;
  }
  void SetSystemValue(const pb::SystemState::Value& system_value) {
    system_value_ = system_value;
  }
  void SetLastVehicleMode(const ::voy::VehicleMode& vehicle_mode) {
    last_vehicle_mode_ = vehicle_mode;
  }
#if defined(ONBOARD_ONLY)
  void SetIsKeepDefense(const bool is_keep_defense);
#endif

  void SetIsHighFrequencyFault(const bool is_high_frequency_fault) {
    is_high_frequency_fault_ = is_high_frequency_fault;
  }

  // Check if current SR recovery status is CANNOT_RESTART, which requires
  // OPS intervention.
  bool IsNeedOpsRestore() const { return is_need_ops_restore_; }
  // Check current frame data and related status information to determine if SS
  // should auto execute active or release parking retention.
  void CheckIfSsExecParkingRetention(
      const std::shared_ptr<const ::pb::SystemState> system_state);

 private:
  // Check if current frame's SystemState contains faults reported by
  // telemetry_node indicating ground assist network disconnection.
  bool CheckNetworkStatusOfGroundAssist(
      const std::shared_ptr<const ::pb::SystemState> system_state);
  void UpdateFallbackAdasStatus();
  void UpdateMrcStatus();
  void UpdateFallbackl1VehicleParkingStatus(
      const ::planner::pb::Trajectory& message);
  void UpdateFallbackAutonomyStatus();
  void UpdateMrcStatusWhenCollisionOccur();
  void UpdateFallbackAdasStatusWhenCollisionOccur();
  void UpdateFallbackl2VehicleParkingStatus();
  void ResetMessage() {
    SetIsNeedOpsTakenOver(pb::SystemState::NO);
    SetSystemStatus(SystemStatusType::ADS);
    SetIsHighFrequencyFault(false);
  }

  bool IsCommInterruptedWithFallbackAdas(const int32_t rolling_counter,
                                         const int index);

  bool IsCollisionDetectorNodeUnavailable(const std::string& node_name,
                                          const pb::Health& health);

 private:
  struct AcuMcuCommChecker {
    int32_t last_rolling_counter;
    int32_t comm_interrupt_count;
  };

  // 打标相关
  EventHandleState is_takeover_success_{EventHandleState::DEFAULT};
  EventHandleState is_restore_success_{EventHandleState::DEFAULT};
  const std::string default_ss_event_id_;
  std::string ss_event_id_;
  std::string ss_last_event_id_;
  FallbackControlState fallback_autonomy_control_state_{
      FallbackControlState::DEFAULT};
  FallbackControlState fallback_ads_control_state_{
      FallbackControlState::DEFAULT};

  // Specific action judgment
  bool is_fallbackl2_taking_over_before_ops_ = false;
  bool is_high_frequency_voice_broadcasted_ = false;
  bool is_delay_to_remind_ops_takeover_ = false;
  pb::SystemState::Disengage is_need_ops_taken_over_{pb::SystemState::UNKNOWN};

  // Simulation related
  bool is_modify_mode_in_sim_flag_ = false;
  ::voy::VehicleMode vehicle_mode_in_sim_{::voy::VehicleMode::MANUAL};

  // Unmanned related
  bool is_door_opened_ = false;
  bool is_door_status_abnormal_ = false;
  bool is_need_ops_restore_ = false;
  bool is_recovery_timeout_ = false;  // Does it meet the condition of manual
                                      // takeover for recovery timeout
  bool is_ground_support_parking_retention_active_ = false;
  // Has the vehicle come to a complete stop and stopped starting
  bool is_vehicle_parking_completed_ = false;
  bool is_in_mrm_process_ = false;
  bool is_in_mrm_process_last_frame_ = false;
  bool is_in_parking_process_ = false;

  // Related variables for determining vehicle status
  uint64_t ground_support_parking_retention_active_start_time_ = 0;
  uint8_t system_available_ = 0;
  uint8_t risk_value_ = 0;
  uint8_t stop_value_ = 0;
  int32_t fallback_mcu_version_ = 0;
  SystemStatusType system_status_{SystemStatusType::ADS};
  SystemStatusType last_system_status_{SystemStatusType::ADS};
  SystemStatusType init_system_status_{SystemStatusType::ADS};
  ::voy::VehicleMode vehicle_mode_{::voy::VehicleMode::MANUAL};
  ::voy::VehicleMode last_vehicle_mode_{::voy::VehicleMode::MANUAL};
  StatusBitType prev_status_ = StatusBitType::INITIALIZATION;
  StatusBitType curr_status_ = StatusBitType::INITIALIZATION;
  ::pb::SystemState::SafetyValue system_safety_status_{
      ::pb::SystemState::SAFETY};
  ::pb::SystemState::Value system_value_{::pb::SystemState::NOT_READY};

  // Determine the relevant variables of the takeover method
  MrmStatusType last_mrc_state_{MrmStatusType::UNDEFINED};
  MrmStatusType mrc_state_{MrmStatusType::UNDEFINED};
  ::action_factory::pb::SystemSupervisorMrcTriggers mrm_trigger_message_;
  // fallback adas
  bool is_in_fallback_adas_odd_ = false;
  bool is_enable_fallback_adas_ = false;
  bool is_enable_fallback_autonomy_ = false;
  bool is_enable_camera_cleaning_function_ = false;
  bool is_parked_retention_auto_ = false;
  bool is_parked_retention_fallback_autonomy_ = false;
  bool is_parked_retention_fallback_adas_ = false;
  Fallbackl2TriggerMethod trigger_method_of_fallbackl2_ = {
      Fallbackl2TriggerMethod::NO_TRIGGER};
  Fallbackl2TriggerMethod current_control_way_of_fallbackl2_ = {
      Fallbackl2TriggerMethod::NO_TRIGGER};
  std::vector<AcuMcuCommChecker> acu_mcu_comm_checker_;
  AcuMcuCommStatus acu_mcu_comm_status_{AcuMcuCommStatus::COMM_NORMAL};
  ::voy::DetailedInfo::FallBackStatus fallback_control_status_{
      ::voy::DetailedInfo::ADS_CONTROL};

  // Related variables for time recording
  int64_t delay_remind_ops_takeover_timer_ = 0;
  int64_t fallback_takeover_timer_ = 0;

  // Used to determine special events, such as high frequencies and collisions
  bool is_high_frequency_fault_;
  std::unordered_map<std::string, std::tuple<uint8_t, uint8_t, uint8_t> >
      collision_upstream_map_;
  voy::Canbus canbus_;  // Real time update of received gateway values
  bool is_collision_detection_risk_ = false;
  bool is_upstream_risk_ = false;
  bool is_camera_need_self_cleaning_ = false;
  pb::CollisionMessageRequired collision_message_required_;

  bool is_ra_eb_fault_ = true;
#if defined(ONBOARD_ONLY)
  bool is_enable_re_engage_ = false;
  bool is_keep_defense_ = true;
  std::string odd_type_;
  message_util::Publisher action_factory_re_engage_publisher_;
  node::FaultReporter fault_reporter_ =
      node::FaultReporter(av_comm::component::kActionFactory);
  // Add new member variables
  std::thread param_check_thread_;
  std::atomic<bool> is_driverless_scene_{false};
  std::atomic<bool> stop_param_check_{false};
  std::mutex param_check_mutex_;
#endif
};

}  // namespace  status_machine
}  // namespace system_supervisor::action_factory

#endif  // ONBOARD_SYSTEM_SUPERVISOR_FAULT_CLASSIFICATION_ACTION_FACTORY_STATUS_MACHINE_STATUS_MACHINE_MESSAGE_H_
