#include "planner/planning_proactive_drop_message_manager.h"
#include <cstdlib>
#include <numeric>

#include "av_comm/mode_config.h"

#include "base/now.h"
#include "base/wall_clock_elapsed_timer.h"
#include "latency/pipeline_id.h"
namespace planner {
int64_t ProactiveDropMessageManager::GetPathPublishInterval() {
  return path_publish_interval_ms_.load(std::memory_order_acquire);
}

void ProactiveDropMessageManager::SetPathPublishInterval(
    int64_t path_publish_speed_timestamp_ms) {
  int64_t path_publish_interval_ms = kPathIntervalInMs;

  if (!pathvnode_publish_timestamps_.empty()) {
    // Get three recent values and calculate the average.
    path_publish_interval_ms = (path_publish_speed_timestamp_ms -
                                pathvnode_publish_timestamps_.front()) /
                               pathvnode_publish_timestamps_.size();
  }

  path_publish_interval_ms_.store(path_publish_interval_ms,
                                  std::memory_order_relaxed);

  while (pathvnode_publish_timestamps_.size() >= kAverageFrequency) {
    pathvnode_publish_timestamps_.pop_front();
  }
  pathvnode_publish_timestamps_.push_back(path_publish_speed_timestamp_ms);
}

int64_t ProactiveDropMessageManager::GetSnapshotPublishInterval() {
  return snapshot_publish_interval_ms_.load(std::memory_order_acquire);
}

void ProactiveDropMessageManager::SetSnapshotPublishInterval(
    int64_t snapshot_publish_timestamp_ms) {
  int64_t snapshot_publish_interval_ms = kSnapshotIntervalInMs;

  if (!snapshot_vnode_publish_timestamps_.empty()) {
    // Get three recent values and calculate the average.
    snapshot_publish_interval_ms =
        (snapshot_publish_timestamp_ms -
         snapshot_vnode_publish_timestamps_.front()) /
        snapshot_vnode_publish_timestamps_.size();
  }

  snapshot_publish_interval_ms_.store(snapshot_publish_interval_ms,
                                      std::memory_order_relaxed);

  while (snapshot_vnode_publish_timestamps_.size() >= kAverageFrequency) {
    snapshot_vnode_publish_timestamps_.pop_front();
  }
  snapshot_vnode_publish_timestamps_.push_back(snapshot_publish_timestamp_ms);
}

void ProactiveDropMessageManager::SavePathVnodeExecutionTime(
    int64_t path_vnode_execute_time_ms) {
  while (path_vnode_execution_times_.size() >= kAverageFrequency) {
    path_vnode_execution_times_.pop_front();
  }
  path_vnode_execution_times_.push_back(path_vnode_execute_time_ms);
}

bool ProactiveDropMessageManager::CanDropCurrentSnapshot(
    const int64_t latest_snapshot_publish_timestamp_ms) {
  if (av_comm::InSimulation() && av_comm::InSimAlignedMode()) {
    return false;
  }

  // If current snapshot has been delayed for more than 50ms, drop the current
  // frame.
  const int64_t current_snapshot_has_delayed_ms =
      base::Now() - latest_snapshot_publish_timestamp_ms;
  if (CanDropFrameWithConsecutiveGuard(
          latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>()
              .load(),
          current_snapshot_has_delayed_ms >
              kFrameDropSnapshotDelayThresholdInMs)) {
    return true;
  }

  if (planner::
          FLAGS_planning_enable_frame_drop_for_consecutive_snapshot_input_delay) {
    // If the arrival of snapshot(planner input) has been delayed consecutively
    // for more than 2 frames, drop the third path frame.
    SetPathContinuousDelayNumber(current_snapshot_has_delayed_ms);
    if (CanDropFrameWithConsecutiveGuard(
            latency::PipelineID<
                latency::PipelineType::PlannerStage1LidarHWTime>()
                .load(),
            GetPathContinuousDelayNumber() >
                kConsecutiveDelayNumberThreshold)) {
      ClearPathContinuousDelayNumber();
      rt_event::PostRtEvent<
          rt_event::planner::ConsecutiveDelayTriggeredProactiveFrameDrop>(
          "path_drop_frame_for_consecutive_delay");
      return true;
    }
  }

  // Calculate average snapshot publish interval, then update snapshot publish
  // timestamp vector.
  int64_t snapshot_publish_interval = GetSnapshotPublishInterval();
  const int64_t next_snapshot_timestamp =
      latest_snapshot_publish_timestamp_ms + snapshot_publish_interval;
  // Calculate average PlanningNodelet_Tick() execution time.
  int64_t path_vnode_execution_time = 0;
  if (!path_vnode_execution_times_.empty()) {
    path_vnode_execution_time =
        std::accumulate(path_vnode_execution_times_.begin(),
                        path_vnode_execution_times_.end(), 0LL) /
        path_vnode_execution_times_.size();
  }

  // If next snapshot is coming, if we not drop current snapshot, it
  // will cause the next snapshot being passively dropped, so just drop the
  // current frame.
  const int64_t snapshot_timestamp_after_next =
      next_snapshot_timestamp + snapshot_publish_interval;
  const int64_t next_nodelet_start_timestamp =
      base::Now() + path_vnode_execution_time;
  return CanDropFrameWithConsecutiveGuard(
      latency::PipelineID<latency::PipelineType::PlannerStage1LidarHWTime>()
          .load(),
      next_nodelet_start_timestamp > snapshot_timestamp_after_next);
}

void ProactiveDropMessageManager::SaveSpeedVnodeExecutionTime(
    int64_t speed_vnode_execute_time_ms) {
  while (speed_vnode_execution_times_.size() >= kAverageFrequency) {
    speed_vnode_execution_times_.pop_front();
  }
  speed_vnode_execution_times_.push_back(speed_vnode_execute_time_ms);
}

bool ProactiveDropMessageManager::CanDropCurrentPath(
    const int64_t latest_path_publish_timestamp_ms) {
  // Check if the lost frames are recorded in the recurring_frame during the
  // road test, In align mode, a frame will be directly retained or discarded
  // based on this value. Therefore, it will not determine whether the frame is
  // actively lost, but directly use the original frame loss result from the
  // road test.
  if (av_comm::InSimulation() && av_comm::InSimAlignedMode()) {
    return false;
  }
  int64_t path_publish_interval = GetPathPublishInterval();
  // If the latest snapshot arrival time to current path start time has been
  // more than 50ms, we think the next snapshot will arrive in less than
  // 50ms(assume snapshot interval is 100ms), we will drop the current path
  // frame.
  const int64_t current_path_has_delayed_ms =
      base::Now() - latest_path_publish_timestamp_ms;
  if (CanDropFrameWithConsecutiveGuard(
          latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>()
              .load(),
          current_path_has_delayed_ms > kFrameDropPathDelayThresholdInMs)) {
    return true;
  }

  if (planner::
          FLAGS_planning_enable_frame_drop_for_consecutive_path_input_delay) {
    // If the arrival of path input has been delayed consecutively for more than
    // 2 frames, drop the third speed frame.
    SetPathContinuousDelayNumber(current_path_has_delayed_ms);
    if (CanDropFrameWithConsecutiveGuard(
            latency::PipelineID<
                latency::PipelineType::PlannerStage2LidarHWTime>()
                .load(),
            GetPathContinuousDelayNumber() >
                kConsecutiveDelayNumberThreshold)) {
      ClearPathContinuousDelayNumber();
      rt_event::PostRtEvent<
          rt_event::planner::ConsecutiveDelayTriggeredProactiveFrameDrop>(
          "speed_drop_frame_for_consecutive_delay");
      return true;
    }
  }
  const int64_t next_path_timestamp =
      latest_path_publish_timestamp_ms + path_publish_interval;

  // Calculate average SpeedVNode execution time.
  int64_t speedvnode_execution_time = 0;
  if (!speed_vnode_execution_times_.empty()) {
    speedvnode_execution_time =
        std::accumulate(speed_vnode_execution_times_.begin(),
                        speed_vnode_execution_times_.end(), 0LL) /
        speed_vnode_execution_times_.size();
  }

  // If we do not drop current path, it
  // will cause the next path being passively dropped, so just drop the
  // current frame.
  const int64_t path_timestamp_after_next =
      next_path_timestamp + path_publish_interval;
  const int64_t next_nodelet_start_timestamp =
      base::Now() + speedvnode_execution_time;
  return CanDropFrameWithConsecutiveGuard(
      latency::PipelineID<latency::PipelineType::PlannerStage2LidarHWTime>()
          .load(),
      next_nodelet_start_timestamp > path_timestamp_after_next);
}

void ProactiveDropMessageManager::SavePathSpeedVnodeExecutionTime(
    int64_t path_speed_vnode_execute_time_ms) {
  while (path_speed_vnode_execution_times_.size() >= kAverageFrequency) {
    path_speed_vnode_execution_times_.pop_front();
  }
  path_speed_vnode_execution_times_.push_back(path_speed_vnode_execute_time_ms);
}

bool ProactiveDropMessageManager::CanDropCurrentSnapshotNotSplittingVnode(
    int64_t latest_snapshot_publish_timestamp_ms,
    int64_t callback_start_time_ms) {
  if (av_comm::InSimulation() && av_comm::InSimAlignedMode()) {
    return false;
  }

  // Calculate average snapshot publish interval, then update snapshot publish
  // timestamp vector.
  int64_t snapshot_publish_interval = GetSnapshotPublishInterval();

  // If next snapshot will coming in 30ms, drop the current frame.
  const int64_t next_snapshot_timestamp =
      latest_snapshot_publish_timestamp_ms + snapshot_publish_interval;
  const int64_t time_until_next_snapshot =
      next_snapshot_timestamp - callback_start_time_ms;
  if (time_until_next_snapshot > kFrameDropSnapshotThresholdInMs) {
    return false;
  }

  // Calculate average path and speed vnodes execution time.
  int64_t path_speed_vnode_execution_time = 0;
  if (!path_speed_vnode_execution_times_.empty()) {
    path_speed_vnode_execution_time =
        std::accumulate(path_speed_vnode_execution_times_.begin(),
                        path_speed_vnode_execution_times_.end(), 0LL) /
        path_speed_vnode_execution_times_.size();
  }

  // If next snapshot is coming, if we not drop current snapshot, it
  // will cause the next snapshot being passively dropped, so just drop the
  // current frame.
  const int64_t snapshot_timestamp_after_next =
      next_snapshot_timestamp + snapshot_publish_interval;
  const int64_t next_nodelet_start_timestamp =
      callback_start_time_ms + path_speed_vnode_execution_time;
  return next_nodelet_start_timestamp > snapshot_timestamp_after_next;
}

void ProactiveDropMessageManager::SetPathContinuousDelayNumber(
    int8_t delay_time_ms) {
  if (delay_time_ms > kDelayTimeThresholdInMs) {
    consecutive_delay_number_path_++;
  } else {
    consecutive_delay_number_path_ = 0;
  }
}
void ProactiveDropMessageManager::SetSpeedContinuousDelayNumber(
    int8_t delay_time_ms) {
  if (delay_time_ms > kDelayTimeThresholdInMs) {
    consecutive_delay_number_speed_++;
  } else {
    consecutive_delay_number_speed_ = 0;
  }
}

uint64_t ProactiveDropMessageManager::CalculateDelta(uint64_t t1, uint64_t t2) {
  return (t1 >= t2) ? (t1 - t2) : (t2 - t1);
}

void ProactiveDropMessageManager::UpdateDropTimestamps(uint64_t current_hw_ms) {
  last_last_drop_hw_ms_ =
      current_hw_ms > last_drop_hw_ms_ ? last_drop_hw_ms_ : current_hw_ms;
  last_drop_hw_ms_ =
      current_hw_ms > last_drop_hw_ms_ ? current_hw_ms : last_drop_hw_ms_;
}

// There are two reasons for adding a lock here:
// 1. Worried about data competition caused by parallel path and speed
// entering this logic simultaneously.
// 2. In order to ensure the operation updating last_drop_hw_ms_
// and judgment of Delta>=kConsecutiveFrameDropGuardIntervalMs is an atomic
// operation. Under extreme conditions, both path and speed enter this
// function for judgment, while executing the judgment
// delta>=kConsecutiveFrameDropGuardIntervalMs, and simultaneously satisfying
// the condition,This situation will result in continuous frame loss.
bool ProactiveDropMessageManager::CanDropFrameWithConsecutiveGuard(
    uint64_t current_hw_ms, bool precondition_met, bool force_drop) {
  std::lock_guard<std::mutex> lock(frame_drop_state_mutex_);

  // No frame dropping during the code startup phase.
  if (last_drop_hw_ms_ == 0) {
    last_last_drop_hw_ms_ = current_hw_ms;
    last_drop_hw_ms_ = current_hw_ms;
    return false;
  }

  if (!precondition_met) {
    return false;
  }

  if (!force_drop && GetUpstreamFrameDropStatus()) {
    rt_event::PostRtEvent<rt_event::planner::UpstreamFrameDrop>(
        "upstream drop");
    return false;
  }
  const uint64_t current_delta = ProactiveDropMessageManager::CalculateDelta(
      current_hw_ms, last_drop_hw_ms_);
  if (current_delta >= kConsecutiveFrameDropGuardIntervalMs) {
    UpdateDropTimestamps(current_hw_ms);
    return true;
  }
  // Fallback to the point where two consecutive frames
  // can be lost in the case of force_drop is True.
  if (last_drop_hw_ms_ != last_last_drop_hw_ms_ && force_drop) {
    const uint64_t previous_delta = ProactiveDropMessageManager::CalculateDelta(
        last_drop_hw_ms_, last_last_drop_hw_ms_);
    if (previous_delta >= kConsecutiveFrameDropGuardIntervalMs) {
      UpdateDropTimestamps(current_hw_ms);
      rt_event::PostRtEvent<rt_event::planner::ForceProactiveFrameDrop>(
          "force drop");
      return true;
    }
  }
  rt_event::PostRtEvent<rt_event::planner::ForceProactiveFrameDrop>(
      "do not drop reason is Consecutive");
  return false;
}

void ProactiveDropMessageManager::UpdateUpstreamFrameDropStatus(
    uint64_t current_hw_ms) {
  if (last_path_hw_ms_ != 0) {
    const uint64_t delta = current_hw_ms - last_path_hw_ms_;
    upstream_frame_drop_detected_.store(delta >= kUpstreamFrameDropThresholdMs,
                                        std::memory_order_relaxed);
  }
  last_path_hw_ms_ = current_hw_ms;
}

}  // namespace planner
