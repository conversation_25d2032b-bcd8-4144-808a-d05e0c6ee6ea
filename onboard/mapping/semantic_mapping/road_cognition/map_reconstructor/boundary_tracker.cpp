#include "boundary_tracker.h"

#include <algorithm>
#include <iomanip>
#include <limits>
#include <string>
#include <utility>

#include <glog/logging.h>
#include <unsupported/Eigen/Splines>

#include "math/bezier_curve_2d.h"
#include "road_cognition/io/visual_stream_io.h"
#include "road_cognition/utils/config_util.hpp"
#include "road_cognition/utils/geometry_util.hpp"
#include "road_cognition/utils/statistic_util.h"
#include "semantic_mapping_gflags.h"

namespace {
constexpr double kOutlierAngle = 90 * M_PI / 180.0;
constexpr size_t kInitObsTimes{1};
constexpr double kMaxGapTolerance{2.5};  // Unit: m
constexpr double kMinUncertainty{0.5};   // Unit: m
}  // namespace

namespace slam {
namespace semantic_mapping {
namespace road_cognition {

BoundaryTracker::BoundaryTracker(const LaneBoundarySingle& lane_boundary)
    : obs_times_(kInitObsTimes) {
  // Create boundary instance
  boundary_instance_.track_id = lane_boundary.track_id;
  boundary_instance_.type = lane_boundary.type;
  boundary_instance_.index = lane_boundary.index;
  boundary_instance_.confidence = lane_boundary.confidence;
  boundary_instance_.attr = lane_boundary.attr;
  boundary_instance_.obs_times = kInitObsTimes;
  boundary_instance_.direction = lane_boundary.direction;
  boundary_instance_.id = AssignID();

  // Storage single frame
  raw_frames_.emplace(lane_boundary.timestamp,
                      std::make_shared<LaneBoundarySingle>(lane_boundary));
}

void BoundaryTracker::Track(const LaneBoundarySinglePtr& lane_boundary) {
  if (!lane_boundary || lane_boundary->raw_obs_pts.size() <= 1) {
    return;
  }
  DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
      << "Boundary tracker: track id=" << boundary_instance_.track_id
      << ", add new frame size=" << lane_boundary->raw_obs_pts.size()
      << ", frame buffer size=" << raw_frames_.size();

  raw_frames_.emplace(lane_boundary->timestamp, lane_boundary);
  while (raw_frames_.size() >
         ConfigUtil::Instance()->Config().boundary_update_buffer_size()) {
    raw_frames_.erase(raw_frames_.begin());
  }

  ++obs_times_;
  boundary_instance_.confidence =
      std::max(lane_boundary->confidence, boundary_instance_.confidence);
}

std::shared_ptr<ReconBoundary> BoundaryTracker::Update(
    double passed_index, double fixed_index, double update_index,
    bool& is_able_to_remove) {
  if (boundary_instance_.obs_times < obs_times_) {
    if (!boundary_instance_.points.empty() &&
        boundary_instance_.points.rbegin()->first > update_index) {
      DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
          << "TkId=" << boundary_instance_.track_id << " not ready for update.";
      // No need to update geometry.
    } else {
      UpdateGeometry();
    }
  }

  UpdateInstanceStatus(passed_index, fixed_index, update_index);
  is_able_to_remove = boundary_instance_.points.empty() && raw_frames_.empty();

  return GetBoundary();
}

void BoundaryTracker::ClearVisualizePts() {
  if (!FLAGS_enable_road_cognition_visualization) {
    return;
  }
  if (FLAGS_enable_road_cognition_debug) {
    VisualStreamIo::ClearStream(kObsBoundaryStreamName);
  }
  VisualStreamIo::ClearStream(kFixBoundaryStreamName);
}

void BoundaryTracker::UpdateGeometry() {
  Direction direction;
  if (!GetMainDirection(direction)) {
    LOG(WARNING) << "GetMainDirection failed.Track Id="
                 << boundary_instance_.track_id;
    return;
  }
  auto raw_pts = GetObsPointsWithProjIndex(direction);
  auto fix_pts = GetFixPointsWithProjIndex(direction);

  auto raw_pts_clusters = GetRawPointsCluster(raw_pts);
  AddInstancePointsToCluster(fix_pts, raw_pts_clusters);
  auto merged_pts = ClusterPts(raw_pts_clusters);
  RemoveOutlierPoints(merged_pts);

  if (FLAGS_enable_road_cognition_visualization) {
    VisualStreamIo::AppendVisualStream<double, ObsPoint>(kFixBoundaryStreamName,
                                                         fix_pts);
  }

  auto sampled_merged_pts = PreparePointsForFitting(fix_pts, merged_pts);
  std::map<double, Eigen::Vector3d> smoothed_pts;
  BspineFitAndSmoothCurve(sampled_merged_pts, smoothed_pts);

  double ave_latest_end_arc_index{0.0};
  if (!GetLatestAveEndProjIndex(direction, ave_latest_end_arc_index)) {
    LOG(INFO) << "GetLatestAveEndProjIndex failed.Track Id="
              << boundary_instance_.track_id;
    return;
  }
  raw_pts.insert(fix_pts.begin(), fix_pts.end());
  UpdateBoundaryInstance(sampled_merged_pts, raw_pts, smoothed_pts,
                         ave_latest_end_arc_index);
}

bool BoundaryTracker::GetMainDirection(Direction& direction) const {
  if (raw_frames_.empty() && boundary_instance_.points.size() <= 1) {
    return false;
  }

  // calculate from raw frames
  bool find_direction{false};
  double min_raw_index{std::numeric_limits<double>::max()};
  double max_raw_index{std::numeric_limits<double>::lowest()};
  for (const auto& frame : raw_frames_) {
    if (frame.second->raw_obs_pts.size() <= 1) {
      // Skip invalid frame
      continue;
    }
    if (frame.second->raw_obs_pts.begin()->first < min_raw_index) {
      min_raw_index = frame.second->raw_obs_pts.begin()->first;
      direction.start_pt = frame.second->raw_obs_pts.begin()->second.pt;
    }
    if (frame.second->raw_obs_pts.rbegin()->first > max_raw_index) {
      max_raw_index = frame.second->raw_obs_pts.rbegin()->first;
      direction.end_pt = frame.second->raw_obs_pts.rbegin()->second.pt;
    }
    find_direction = true;
  }
  direction.start_index = min_raw_index;
  if (find_direction) {
    return true;
  }

  // when single frames are empty, use boundary instance to cal main direction
  if (!find_direction && boundary_instance_.points.size() > 1) {
    direction.start_pt = boundary_instance_.points.begin()->second.pt;
    direction.end_pt = boundary_instance_.points.rbegin()->second.pt;
    direction.start_index = boundary_instance_.points.begin()->first;
    return true;
  }

  return false;
}

std::map<double, ObsPoint> BoundaryTracker::GetObsPointsWithProjIndex(
    const Direction& direction) const {
  std::map<double, ObsPoint> proj_obs_pts;

  for (auto frame = raw_frames_.rbegin(); frame != raw_frames_.rend();
       frame++) {
    if (frame->second->raw_obs_pts.size() <= 1) {
      // Skip invalid frame
      continue;
    }
    for (const auto& obs_pt : frame->second->raw_obs_pts) {
      double proj_to_start_dist =
          GeometryUtil::PointToSegmentProjPointToStartDistance2D(
              obs_pt.second.pt.head(2), direction.start_pt.head(2),
              direction.end_pt.head(2));

      auto updated_index = direction.start_index + proj_to_start_dist;
      proj_obs_pts.emplace(updated_index, obs_pt.second);
    }
  }
  return proj_obs_pts;
}

std::map<double, ObsPoint> BoundaryTracker::GetFixPointsWithProjIndex(
    const Direction& direction) const {
  if (boundary_instance_.points.size() <= 1) {
    return {};
  }

  std::map<double, ObsPoint> proj_fix_pts;

  for (auto it = boundary_instance_.points.begin();
       it != boundary_instance_.points.end(); it++) {
    double proj_to_start_dist =
        GeometryUtil::PointToSegmentProjPointToStartDistance2D(
            it->second.pt.head(2), direction.start_pt.head(2),
            direction.end_pt.head(2));

    auto updated_index = direction.start_index + proj_to_start_dist;

    DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
        << " Update index FIX: " << it->first << " to " << updated_index
        << ",startIdx=" << boundary_instance_.points.begin()->first
        << ",endIdx=" << boundary_instance_.points.rbegin()->first
        << ",delta=" << proj_to_start_dist << ", pa =" << direction.start_pt.x()
        << "," << direction.start_pt.y() << ";pt=" << it->second.pt.x() << ","
        << it->second.pt.y()
        << ",norm=" << (it->second.pt - direction.start_pt).head(2).norm()
        << ",proj dist=" << proj_to_start_dist;

    proj_fix_pts.emplace(updated_index, it->second);
  }
  return proj_fix_pts;
}

bool BoundaryTracker::GetLatestAveEndProjIndex(
    const Direction& direction, double& ave_latest_end_arc_index) const {
  const int latest_frame_window_size = std::max(
      static_cast<int>(
          ConfigUtil::Instance()->Config().boundary_update_buffer_size() / 2 -
          1),
      1);

  double latest_end_index_sum{0.0};
  int frame_count{0};
  for (auto frame = raw_frames_.rbegin(); frame != raw_frames_.rend();
       frame++) {
    if (frame->second->raw_obs_pts.size() <= 1) {
      // Skip invalid frame
      continue;
    }
    if (frame_count <= latest_frame_window_size) {
      latest_end_index_sum +=
          direction.start_index +
          GeometryUtil::PointToSegmentProjPointToStartDistance2D(
              frame->second->raw_obs_pts.rbegin()->second.pt.head(2),
              direction.start_pt.head(2), direction.end_pt.head(2));
      frame_count++;
    }
  }

  ave_latest_end_arc_index = latest_end_index_sum / frame_count;
  return frame_count != 0;
}

std::map<int, std::vector<std::pair<double, ObsPoint>>>
BoundaryTracker::GetRawPointsCluster(
    const std::map<double, ObsPoint>& raw_obs_pts) const {
  if (raw_obs_pts.empty()) {
    return {};
  }

  std::map<int, std::vector<std::pair<double, ObsPoint>>> cluster_pts;
  std::map<int, Eigen::Vector3d> cluster_pts_sum;
  const double& cluster_arc_len{
      ConfigUtil::Instance()->Config().hb_cluster_arc_length()};

  for (const auto& update_pt : raw_obs_pts) {
    const int idx = static_cast<int>(update_pt.first / cluster_arc_len);
    if (cluster_pts.find(idx) == cluster_pts.end()) {
      cluster_pts[idx] = std::vector<std::pair<double, ObsPoint>>{update_pt};
      cluster_pts_sum[idx] = update_pt.second.pt;
    } else {
      cluster_pts[idx].emplace_back(update_pt);
      cluster_pts_sum[idx] += update_pt.second.pt;
    }
  }

  // remove outlier and large rmse points
  for (auto& [key, cluster] : cluster_pts) {
    std::sort(cluster.begin(), cluster.end(),
              [](const std::pair<double, ObsPoint>& a,
                 const std::pair<double, ObsPoint>& b) {
                return a.second.rmse > b.second.rmse;
              });

    auto&& pt_ave = cluster_pts_sum[key] / cluster.size();
    for (auto pt = cluster.begin(); pt != cluster.end();) {
      if (cluster.size() <= 1) {
        break;
      }
      if ((pt->second.pt - pt_ave).head(2).norm() >
              ConfigUtil::Instance()->Config().hb_cluster_arc_length() ||
          pt->second.rmse > kMinUncertainty) {
        pt = cluster.erase(pt);
        continue;
      }
      pt++;
    }
  }
  return cluster_pts;
}

void BoundaryTracker::AddInstancePointsToCluster(
    const std::map<double, ObsPoint>& fix_pts,
    std::map<int, std::vector<std::pair<double, ObsPoint>>>& raw_pts_clusters) {
  if (fix_pts.empty()) {
    return;
  }

  const double& cluster_arc_len{
      ConfigUtil::Instance()->Config().hb_cluster_arc_length()};

  for (const auto& fixed_pt : fix_pts) {
    if (fixed_pt.second.type != ObsPointType::Fixed &&
        fixed_pt.second.type != ObsPointType::Current) {
      continue;
    }
    const int idx = static_cast<int>(fixed_pt.first / cluster_arc_len);
    if (raw_pts_clusters.find(idx) == raw_pts_clusters.end()) {
      raw_pts_clusters[idx] =
          std::vector<std::pair<double, ObsPoint>>{fixed_pt};
    } else {
      raw_pts_clusters[idx].emplace_back(fixed_pt);
    }
  }
}

std::map<double, ObsPoint> BoundaryTracker::ClusterPts(
    std::map<int, std::vector<std::pair<double, ObsPoint>>>& point_clusters)
    const {
  std::map<double, ObsPoint> clustered_pts;
  for (const auto& [cl_index, cl_pts] : point_clusters) {
    if (cl_pts.empty()) {
      continue;
    }

    // TODO(weiyanchao): Will cause short HB points <= Bspline order required.
    // if (obs_times_ >= ConfigUtil::Instance()->Config().hb_min_cluster_num()
    // &&
    //     cl_pts.size() <
    //     ConfigUtil::Instance()->Config().hb_min_cluster_num()) {
    //   continue;
    // }

    StatisticXd<3> statistic_proc;
    double ave_pt_rmse{0.0};
    double ave_pt_width{0.0};
    ExistenceStatus exist_status{ExistenceStatus::Unknown};

    double ave_index{0.0};
    double min_raw_index{std::numeric_limits<double>::max()};
    double max_raw_index{std::numeric_limits<double>::lowest()};

    double min_obs_angle{std::numeric_limits<double>::max()};
    double max_obs_angle{std::numeric_limits<double>::lowest()};
    for (const auto& pt_pair : cl_pts) {
      double weight = CalWeight(pt_pair.second);
      statistic_proc.PutVar(pt_pair.second.pt, weight);
      ave_pt_rmse += pt_pair.second.rmse;

      min_raw_index = std::min(min_raw_index, pt_pair.second.raw_index);
      max_raw_index = std::max(max_raw_index, pt_pair.second.raw_index);

      min_obs_angle = std::min(min_obs_angle, pt_pair.second.observe_angle);
      max_obs_angle = std::max(max_obs_angle, pt_pair.second.observe_angle);
      ave_index += pt_pair.first;
      ave_pt_width += pt_pair.second.width;
      exist_status =
          pt_pair.second.existence_status == ExistenceStatus::Confirmed
              ? ExistenceStatus::Confirmed
              : exist_status;
    }
    ave_index /= cl_pts.size();
    ave_pt_rmse /= cl_pts.size();
    ave_pt_width /= cl_pts.size();

    if (!clustered_pts.empty() &&
        std::abs(ave_index - clustered_pts.rbegin()->first) >
            kMaxGapTolerance) {
      // Max gap length
      LOG(WARNING) << "BoundaryTracker find gap in track id="
                   << boundary_instance_.track_id
                   << ", cluster index=" << ave_index
                   << " is too far with prev=" << clustered_pts.rbegin()->first
                   << ", will skip.";
      continue;
    }

    const std::optional<Eigen::Vector3d> sigma = statistic_proc.GetSigma();
    const std::optional<Eigen::Vector3d> avg = statistic_proc.GetMean();
    if (!sigma.has_value() || !avg.has_value() || avg->hasNaN()) {
      DLOG(WARNING) << " Cal sigma and avg failed...";
      continue;
    }

    const Eigen::Vector3d& centroid = avg.value();
    const Eigen::Vector3d& std = sigma.value();

    ObsPoint cluster_pt(min_raw_index,
                        cl_pts.size() == 1
                            ? ave_pt_rmse
                            : std::min(std.head(2).norm(), ave_pt_rmse),
                        centroid);
    cluster_pt.existence_status = exist_status;
    cluster_pt.width = ave_pt_width;
    clustered_pts.emplace(ave_index, cluster_pt);
    // Abandon observation information here.

    DLOG_IF(INFO, FLAGS_enable_road_cognition_debug)
        << "TkId=" << boundary_instance_.track_id
        << ", cluster size=" << cl_pts.size()
        << ", raw_ave_rmse=" << ave_pt_rmse << ",sigma=[" << sigma.value().x()
        << "," << sigma.value().y() << "],raw_index=[" << min_raw_index << ","
        << max_raw_index << "],ave index=" << ave_index
        << ", centroid=" << centroid.transpose() << ", angle=["
        << min_obs_angle * 180 / M_PI << "," << max_obs_angle * 180 / M_PI
        << "]";
  }
  return clustered_pts;
}

std::map<double, ObsPoint> BoundaryTracker::PreparePointsForFitting(
    std::map<double, ObsPoint>& fix_pts, std::map<double, ObsPoint>& raw_pts) {
  std::map<double, ObsPoint> all_pts;
  for (auto& fix_pt : fix_pts) {
    if (fix_pt.second.type != ObsPointType::Fixed) {
      break;
    }
    if (all_pts.empty() || !(math::IsApprox(all_pts.rbegin()->second.pt.x(),
                                            fix_pt.second.pt.x()) &&
                             math::IsApprox(all_pts.rbegin()->second.pt.y(),
                                            fix_pt.second.pt.y()))) {
      all_pts.emplace(fix_pt.first, std::move(fix_pt.second));
    }
  }

  const double connect_index{all_pts.empty()
                                 ? std::numeric_limits<double>::lowest()
                                 : all_pts.rbegin()->first};

  size_t extend_points_num{0};
  for (auto& raw_pt : raw_pts) {
    if (raw_pt.first > connect_index &&
        (all_pts.empty() || !(math::IsApprox(all_pts.rbegin()->second.pt.x(),
                                             raw_pt.second.pt.x()) &&
                              math::IsApprox(all_pts.rbegin()->second.pt.y(),
                                             raw_pt.second.pt.y())))) {
      if (!all_pts.empty() &&
          raw_pt.first - all_pts.rbegin()->first > kMaxGapTolerance) {
        break;
      }
      all_pts.emplace(raw_pt.first, std::move(raw_pt.second));
      extend_points_num++;
    }
  }

  if (extend_points_num <= 1) {
    return {};
  }

  return all_pts;
}

std::map<int, std::vector<std::pair<double, ObsPoint>>>
BoundaryTracker::GetPointsClusterWithDbScan(
    const std::map<double, ObsPoint>& update_pts) const {
  auto start_raw_it = update_pts.begin();
  auto end_raw_it = update_pts.end();
  if (std::distance(start_raw_it, end_raw_it) == 0) {
    return {};
  }

  DLOG(INFO) << "UpdateGeometry PointSize = "
             << std::distance(start_raw_it, end_raw_it)
             << ", track_id = " << boundary_instance_.track_id << ", dist =["
             << start_raw_it->first << "," << std::prev(end_raw_it)->first
             << "]"
             << ", last dist="
             << (boundary_instance_.points.empty()
                     ? 0.0
                     : boundary_instance_.points.rbegin()->first);

  int cluster_index = 0;
  std::map<double, int> point_clusters;
  for (auto it = start_raw_it; it != end_raw_it; it++) {
    auto&& index = it->first;
    point_clusters[index] = -1;
  }
  for (auto it = start_raw_it; it != end_raw_it; it++) {
    auto&& index = it->first;
    if (point_clusters[index] != -1) {
      continue;
    }
    std::vector<double> neighbors = GetNeighborPoints(update_pts, index);
    if (neighbors.size() <
        ConfigUtil::Instance()->Config().hb_min_cluster_num()) {
      point_clusters[index] = 0;  // outlier
    } else {
      cluster_index++;
      ExpandCluster(update_pts, index, cluster_index, point_clusters);
    }
  }

  std::map<int, std::vector<std::pair<double, ObsPoint>>> cluster_pts;
  for (auto it = start_raw_it; it != end_raw_it; it++) {
    if (point_clusters.at(it->first) <= 0) {
      continue;
    }
    cluster_pts[point_clusters.at(it->first)].emplace_back(it->first,
                                                           it->second);
  }

  return cluster_pts;
}

std::vector<double> BoundaryTracker::GetNeighborPoints(
    const std::map<double, ObsPoint>& pts, double pt_index) const {
  auto lower_it = pts.lower_bound(
      pt_index - ConfigUtil::Instance()->Config().hb_cluster_arc_length());
  auto upper_it = pts.upper_bound(
      pt_index + ConfigUtil::Instance()->Config().hb_cluster_arc_length());
  std::vector<double> neighbors;
  Eigen::Vector3d center{Eigen::Vector3d::Zero()};
  size_t idx = 0;
  for (auto iter = lower_it; iter != upper_it; iter++) {
    neighbors.emplace_back(iter->first);
    center += iter->second.pt;
    idx++;
  }
  if (neighbors.empty()) {
    return neighbors;
  }
  center /= idx;
  std::vector<double> valid_neighbors;
  for (double& neighbor : neighbors) {
    const auto& obs_pt = pts.at(neighbor);
    if ((obs_pt.pt - center).head(2).norm() <
        1.5 * ConfigUtil::Instance()->Config().hb_cluster_arc_length()) {
      valid_neighbors.emplace_back(neighbor);
    }
  }
  return valid_neighbors;
}

void BoundaryTracker::ExpandCluster(
    const std::map<double, ObsPoint>& sorted_lane_pts, double index,
    const int cluster_index, std::map<double, int>& point_clusters) const {
  point_clusters[index] = cluster_index;
  std::vector<double> neighbors = GetNeighborPoints(sorted_lane_pts, index);
  if (neighbors.size() >=
      ConfigUtil::Instance()->Config().hb_min_cluster_num()) {
    for (double neighbor_index : neighbors) {
      if (point_clusters[neighbor_index] == -1) {
        ExpandCluster(sorted_lane_pts, neighbor_index, cluster_index,
                      point_clusters);
      }
    }
  }
}

std::optional<ObsPoint> BoundaryTracker::FindNearestByGeometry(
    const std::map<double, ObsPoint>& input_pts,
    const Eigen::Vector3d& query_pt) const {
  // input_pts is not empty
  if (input_pts.empty()) {
    return std::nullopt;
  }
  ObsPoint nearest_pt = input_pts.begin()->second;
  double min_dis = std::numeric_limits<double>::max();
  for (const auto& pt : input_pts) {
    double dis = (query_pt - pt.second.pt).norm();
    if (min_dis > dis) {
      min_dis = dis;
      nearest_pt = pt.second;
    }
  }
  return std::make_optional(nearest_pt);
}

ExistenceStatus BoundaryTracker::QueryExistenceByIndex(
    const std::map<double, ObsPoint>& input_pts, double query_index) const {
  if (input_pts.empty()) {
    return ExistenceStatus::Unknown;
  }

  const double query_ext_range{
      ConfigUtil::Instance()->Config().voxel_grid_size() / 2.0};

  size_t unexists_num{0}, exists_num{0};
  auto low = input_pts.lower_bound(query_index - query_ext_range);
  while (low != input_pts.end() && low->first < query_index + query_ext_range) {
    if (low->second.existence_status == ExistenceStatus::Removed) {
      return ExistenceStatus::Removed;
    }
    if (low->second.existence_status == ExistenceStatus::Confirmed) {
      if (low->second.type != ObsPointType::Unknown) {
        return ExistenceStatus::Confirmed;
      }
      exists_num++;
    } else {
      unexists_num++;
    }
    if (exists_num > 1) {
      return ExistenceStatus::Confirmed;
    }
    low++;
  }

  return exists_num > (exists_num + unexists_num) * 0.1
             ? ExistenceStatus::Confirmed
             : ExistenceStatus::Unknown;
}

double BoundaryTracker::CalWeight(const ObsPoint& raw_obs_pt) const {
  constexpr double kDistanceBaseWeight{10.0};

  double x_dist =
      raw_obs_pt.obs_x < ConfigUtil::Instance()->Config().best_observation_x()
          ? 0.0
          : std::fabs(raw_obs_pt.obs_x -
                      ConfigUtil::Instance()->Config().best_observation_x());

  double y_dist = std::fabs(raw_obs_pt.obs_y) -
                  ConfigUtil::Instance()->Config().best_observation_y();

  double dist_weight =
      kDistanceBaseWeight / (1.0 + x_dist / 70.0 + y_dist / 30.0);

  double rmse_weight = std::exp(-raw_obs_pt.rmse * raw_obs_pt.rmse);

  double obs_weight = 1.0 - std::abs(raw_obs_pt.observe_angle / M_PI_2);

  double existence_add_weight =
      TypeUtil::IsHardBoundary(
          static_cast<slam::proto::OnlineMapElementType::Type>(
              boundary_instance_.type)) &&
              raw_obs_pt.existence_status == ExistenceStatus::Confirmed
          ? 1.0
          : 0.0;

  double prev_weight_ratio{1.0};
  switch (raw_obs_pt.type) {
    case ObsPointType::Fixed:
      prev_weight_ratio = 1e6;
      dist_weight = kDistanceBaseWeight;
      break;
    case ObsPointType::Current:
      prev_weight_ratio = 2.5;
      dist_weight = kDistanceBaseWeight;
      break;
    default:
      prev_weight_ratio = 1.0;
      break;
  }

  LOG_IF_EVERY_N(INFO, 1e3, FLAGS_enable_road_cognition_debug)
      << "raw_index=" << raw_obs_pt.raw_index << ", raw_x=" << raw_obs_pt.obs_x
      << ", raw_y=" << raw_obs_pt.obs_y << ", dist_weight=" << dist_weight
      << ", rmse=" << raw_obs_pt.rmse << ", rmse_weight=" << rmse_weight
      << ", raw_angle=" << raw_obs_pt.observe_angle
      << ", obs_weight=" << obs_weight
      << ", prev_weight_ratio=" << prev_weight_ratio;

  return prev_weight_ratio * dist_weight *
         (1.0 + rmse_weight * obs_weight + existence_add_weight);
}

double BoundaryTracker::CalConfidence(const Eigen::Vector3d& sigma,
                                      double pt_rmse, size_t obs_times) const {
  constexpr double kConfidenceSigma2 = 0.15 * 0.15;
  constexpr double kMaxConfidenceofObsTimes = 0.2;
  constexpr double kConfidenceGrowthRate = kMaxConfidenceofObsTimes / 10;
  constexpr double kMaxConfidence = 100;

  double base_weight =
      std::exp(-((sigma.x() * sigma.x()) + (sigma.y() * sigma.y())) /
               (2 * kConfidenceSigma2));

  double raw_weight = std::exp(-pt_rmse * pt_rmse / kConfidenceSigma2);

  double obs_conf =
      std::min(kMaxConfidenceofObsTimes, kConfidenceGrowthRate * obs_times);
  double confidence = std::min(
      kMaxConfidence, (obs_conf + std::max(base_weight, raw_weight)) * 100);
  return confidence;
}

void BoundaryTracker::RemoveOutlierPoints(
    std::map<double, ObsPoint>& input_points) const {
  if (input_points.size() < 3) {  // Angle check requires at least 3 points.
    return;
  }

  std::vector<double> indices_to_remove;
  auto it_pt_left = input_points.begin();
  auto it_pt_mid = std::next(it_pt_left, 1);
  auto it_pt_right = std::next(it_pt_mid, 1);
  size_t continue_outlier_num = 0;
  while (it_pt_right != input_points.end()) {
    const auto p1 = it_pt_left->second.pt.head(2);
    const auto p2 = it_pt_mid->second.pt.head(2);
    const auto p3 = it_pt_right->second.pt.head(2);

    // angle check
    auto angle_d =
        GeometryUtil::AngleBetweenTwoVectors<double>(p2 - p1, p3 - p2, false);
    continue_outlier_num =
        angle_d > kOutlierAngle ? continue_outlier_num + 1 : 0;
    if (continue_outlier_num >= 1) {
      indices_to_remove.emplace_back(it_pt_mid->first);
    }

    it_pt_left++;
    it_pt_mid++;
    it_pt_right++;
  }

  const size_t size_before_remove{input_points.size()};
  for (const auto& index : indices_to_remove) {
    input_points.erase(index);
    DLOG(WARNING) << "Track Id=" << boundary_instance_.track_id
                  << " remove outlier pt=" << index
                  << ", remove size=" << indices_to_remove.size() << "/"
                  << size_before_remove;
  }
}

void BoundaryTracker::BspineFitAndSmoothCurve(
    const std::map<double, ObsPoint>& input_points,
    std::map<double, Eigen::Vector3d>& output_points) const {
  if (input_points.size() <
      ConfigUtil::Instance()->Config().curve_fit_order() + 1) {
    DLOG(WARNING) << "SmoothCurve failed. Point size <= 1.";
    for (const auto& pt : input_points) {
      output_points.emplace(pt.first, pt.second.pt);
    }
    return;
  }

  const double downsample_step =
      TypeUtil::IsHardBoundary(
          static_cast<slam::proto::OnlineMapElementType::Type>(
              boundary_instance_.type))
          ? ConfigUtil::Instance()->Config().curve_fit_sample_arc_length()
          : 2 * ConfigUtil::Instance()->Config().curve_fit_sample_arc_length();

  auto segments = SegmentAndDownSample(
      input_points, ConfigUtil::Instance()->Config().curve_fit_order(),
      downsample_step,
      ConfigUtil::Instance()->Config().curve_fit_max_segment_arc_lenth());

  const double output_arc_sample_length{
      ConfigUtil::Instance()->Config().hb_output_arc_step()};
  for (size_t i = 0; i < segments.size(); i++) {
    auto&& seg = segments[i];

    bool smooth_success{true};
    const double& min_arc{seg.begin()->first};
    const double& max_arc{seg.rbegin()->first};

    if (seg.size() >= ConfigUtil::Instance()->Config().curve_fit_order() + 1) {
      Eigen::MatrixXd data = GetDataMatrix(seg);
      Eigen::Spline<double, 3> spline_fitter(
          Eigen::SplineFitting<Eigen::Spline3d>::Interpolate(
              data, ConfigUtil::Instance()->Config().curve_fit_order()));
      for (double arc_len = min_arc; arc_len < max_arc;
           arc_len += output_arc_sample_length) {
        Eigen::Vector3d pt =
            spline_fitter((arc_len - min_arc) / (max_arc - min_arc));
        if (pt.hasNaN()) {
          smooth_success = false;
          break;
        }
        if (output_points.empty() ||
            !(math::IsApprox(output_points.rbegin()->second.x(), pt.x()) &&
              math::IsApprox(output_points.rbegin()->second.y(), pt.y()))) {
          output_points.emplace(arc_len, pt);
        }
      }
      if (i == segments.size() - 1) {
        Eigen::Vector3d end_pt = spline_fitter(1.0);
        if (!end_pt.hasNaN() &&
            (output_points.empty() ||
             !(math::IsApprox(output_points.rbegin()->second.x(), end_pt.x()) &&
               math::IsApprox(output_points.rbegin()->second.y(),
                              end_pt.y())))) {
          output_points.emplace(max_arc, end_pt);
        }
      }
    }

    if (!smooth_success ||
        seg.size() < ConfigUtil::Instance()->Config().curve_fit_order() + 1) {
      DLOG(WARNING) << "Smooth Curve failed: id=" << boundary_instance_.track_id
                    << ",size=" << seg.size() << ", dist=[" << min_arc << ","
                    << max_arc << "], will use original points.";
      for (const auto& pt : seg) {
        if (output_points.empty() ||
            !(math::IsApprox(output_points.rbegin()->second.x(),
                             pt.second.x()) &&
              math::IsApprox(output_points.rbegin()->second.y(),
                             pt.second.y()))) {
          output_points.emplace(pt.first, pt.second);
        }
      }
    }
  }
}

std::vector<std::vector<std::pair<double, Eigen::Vector3d>>>
BoundaryTracker ::SegmentAndDownSample(
    const std::map<double, ObsPoint>& input_points, size_t order,
    double downsample_step, double segment_arc_len) const {
  if (input_points.size() < order + 1) {
    std::vector<std::pair<double, Eigen::Vector3d>> segment;
    segment.reserve(input_points.size());
    for (const auto& it : input_points) {
      segment.emplace_back(it.first, it.second.pt);
    }
    return {segment};
  }

  const double arc_len{input_points.rbegin()->first -
                       input_points.begin()->first};
  if ((order + 1) * downsample_step > arc_len) {
    downsample_step =
        std::max(ConfigUtil::Instance()->Config().hb_output_arc_step(),
                 arc_len / (order + 1));
    DLOG(WARNING) << "Track id=" << boundary_instance_.track_id
                  << ", length is too short, downsample step changed="
                  << downsample_step << ", arc_len=" << arc_len;
  }

  std::vector<std::vector<std::pair<double, Eigen::Vector3d>>> data_segments;
  std::vector<std::pair<double, Eigen::Vector3d>> current_segment;
  for (auto it = input_points.begin(); it != input_points.end(); ++it) {
    if (current_segment.empty() ||
        (it->first - current_segment.back().first > downsample_step) ||
        std::next(it) == input_points.end()) {
      current_segment.emplace_back(it->first, it->second.pt);
      if (current_segment.size() >= order + 1 &&
          current_segment.back().first - current_segment.front().first >
              segment_arc_len) {
        data_segments.push_back(current_segment);
        current_segment = {current_segment.back()};
      }
    }
  }

  if (!current_segment.empty()) {
    if (current_segment.size() >= order + 1) {
      data_segments.push_back(current_segment);
    } else if (!data_segments.empty()) {
      auto& last_segment = data_segments.back();
      for (size_t i = 1; i < current_segment.size(); ++i) {
        last_segment.push_back(current_segment[i]);
      }
    }
  }
  return data_segments;
}

Eigen::MatrixXd BoundaryTracker::GetDataMatrix(
    const std::vector<std::pair<double, Eigen::Vector3d>>& points) const {
  Eigen::MatrixXd data(3, points.size());
  int i = 0;
  for (const auto& pt : points) {
    data(0, i) = pt.second.x();
    data(1, i) = pt.second.y();
    data(2, i) = pt.second.z();
    i++;
  }

  return data;
}

void BoundaryTracker::UpdateBoundaryInstance(
    const std::map<double, ObsPoint>& sampled_merged_pts,
    const std::map<double, ObsPoint>& all_original_pts,
    const std::map<double, Eigen::Vector3d>& smoothed_pts,
    double ave_latest_end_arc_index) {
  if (sampled_merged_pts.empty() || smoothed_pts.empty()) {
    return;
  }
  double max_raw_index{std::numeric_limits<double>::lowest()};
  double min_raw_index{std::numeric_limits<double>::max()};
  std::for_each(
      sampled_merged_pts.begin(), sampled_merged_pts.end(),
      [&max_raw_index, &min_raw_index](const std::pair<double, ObsPoint>& it) {
        max_raw_index = std::max(max_raw_index, it.second.raw_index);
        min_raw_index = std::min(min_raw_index, it.second.raw_index);
      });
  Eigen::VectorXd sample_raw_indexes = Eigen::VectorXd::LinSpaced(
      smoothed_pts.size(), min_raw_index, max_raw_index);

  size_t i = 0;
  std::map<double, ObsPoint> updated_pts;

  DLOG_IF(INFO, (!smoothed_pts.empty() && FLAGS_enable_road_cognition_debug))
      << "TkId=" << boundary_instance_.track_id << ", smoothed arc range=["
      << smoothed_pts.begin()->first << "," << smoothed_pts.rbegin()->first
      << "]"
      << ", ave_latest_end_arc_index=" << ave_latest_end_arc_index
      << ", raw index range=[" << min_raw_index << "," << max_raw_index << "]";

  for (const auto& pt : smoothed_pts) {
    const auto& sample_raw_index = sample_raw_indexes[i++];
    if (pt.first > ave_latest_end_arc_index) {
      continue;
    }
    std::optional<ObsPoint> nearest_pt =
        FindNearestByGeometry(sampled_merged_pts, pt.second);
    if (!nearest_pt) {
      continue;
    }
    nearest_pt->pt = pt.second;
    nearest_pt->existence_status =
        QueryExistenceByIndex(all_original_pts, pt.first);
    nearest_pt->raw_index = sample_raw_index;
    updated_pts.emplace(sample_raw_index, *nearest_pt);
  }
  if (updated_pts.size() <= 1) {
    DLOG_IF(WARNING, FLAGS_enable_road_cognition_debug)
        << "After curve fitting, points size <=1. Will not update "
           "boundary's fixed points.";
    return;
  }
  boundary_instance_.points.swap(updated_pts);
  boundary_instance_.obs_times = obs_times_;
}

void BoundaryTracker::UpdateInstanceStatus(double passed_index,
                                           double fixed_index,
                                           double update_index) {
  // Update Boundary instance status
  for (auto it = boundary_instance_.points.begin();
       it != boundary_instance_.points.end();) {
    if (it->first < passed_index) {
      it = boundary_instance_.points.erase(it);
      continue;
    }

    if (it->first < fixed_index) {
      it->second.type = ObsPointType::Fixed;
    } else if (it->first > update_index) {
      it->second.type = ObsPointType::Extend;
    } else {
      it->second.type = ObsPointType::Current;
    }
    it++;
  }

  // Update raw frames buffer
  for (auto it = raw_frames_.begin(); it != raw_frames_.end();) {
    if (it->second->raw_obs_pts.empty() ||
        it->second->raw_obs_pts.rbegin()->first < passed_index) {
      it = raw_frames_.erase(it);
      continue;
    }
    it++;
  }
}

std::shared_ptr<ReconBoundary> BoundaryTracker::GetBoundary() const {
  ReconBoundary output_boundary = boundary_instance_;
  return std::make_shared<ReconBoundary>(output_boundary);
}

}  // namespace road_cognition
}  // namespace semantic_mapping
}  // namespace slam
