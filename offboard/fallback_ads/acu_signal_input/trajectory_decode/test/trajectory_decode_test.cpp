#include "acu_signal_input/trajectory_decode/trajectory_decode.h"

#include <gtest/gtest.h>

#include <algorithm>
#include <climits>
#include <cmath>
#include <cstdint>
#include <cstring>

#include "_out/Appl/GenData/Components/Rte_AcuSigInput.h"
#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/can_util.h"

extern Rte_tsMainTask_Core0_60ms Rte_MainTask_Core0_60ms;

namespace fallback_adas {
namespace acu_signal_input {

TEST(TrajectoryDecode, TimestampTest) {
  auto& raw_trajectory_input =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms;

  // ref_plan_init_timestamp_ms = 0x19618d955d8.
  uint64_t ref_plan_init_timestamp_ms = 1744173618648;
  uint8_t timestamp_bytes[8] = {0};
  common::Uint64ToBytes(ref_plan_init_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  std::memcpy(raw_trajectory_input
                  .Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp.value,
              timestamp_bytes, sizeof(timestamp_bytes));

  // Create a TrajectoryInput object
  TrajectoryInput trajectory_input = {0};
  TrajectoryDecode(&trajectory_input);

  // Verify the timestamp
  EXPECT_EQ(static_cast<uint32_t>(trajectory_input.plan_init_timestamp_ms),
            static_cast<uint32_t>(ref_plan_init_timestamp_ms % 1000000000L));
}

TEST(TrajectoryDecode, TrajectoryDecodeTest) {
  auto& raw_trajectory_input =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms;

  for (int32_t i = -3; i <= 3; ++i) {
    auto ref_value = static_cast<float>(i);

    // pose 0
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionX
        .value = ref_value / 0.001F;
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00PositionY
        .value = ref_value / 0.001F;
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Heading
        .value = ref_value / 2e-4F;
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Steering
        .value = ref_value / 4e-4F;
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Speed
        .value = std::abs(ref_value) / 0.01F;
    raw_trajectory_input
        .Rte_AcuTrajectoryPose00To03_TrajectoryPose00Acceleration.value =
        ref_value / 0.005F;

    // curvature
    float ref_curvature = std::clamp(std::abs(ref_value) / 7.5F, 0.0F, 0.4F);
    raw_trajectory_input.Rte_AcuTrajectoryPose00To03_TrajectoryPose00Curvature
        .value = ref_curvature / 1e-4F;

    // pose 99
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionX
        .value = ref_value / 0.001F;
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99PositionY
        .value = ref_value / 0.001F;
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Heading
        .value = ref_value / 2e-4F;
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Steering
        .value = ref_value / 4e-4F;
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Speed
        .value = std::abs(ref_value) / 0.01F;
    raw_trajectory_input
        .Rte_AcuTrajectoryPose96To99_TrajectoryPose99Acceleration.value =
        ref_value / 0.005F;
    raw_trajectory_input.Rte_AcuTrajectoryPose96To99_TrajectoryPose99Curvature
        .value = ref_curvature / 1e-4F;

    TrajectoryInput trajectory_input;
    TrajectoryDecode(&trajectory_input);

    EXPECT_NEAR(trajectory_input.trajectory_pose[0].x_pos, ref_value, 0.001F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].y_pos, ref_value, 0.001F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].heading, ref_value, 2e-4F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].steering, ref_value, 4e-4F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].speed, std::abs(ref_value),
                0.01F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].accel, ref_value, 0.005F);
    EXPECT_NEAR(trajectory_input.trajectory_pose[0].curvature, ref_curvature,
                1e-4F);
  }
}

TEST(GenerateTrajectoryPoseTimestamp, GenerateTrajectoryPoseTimestampTest) {
  auto& raw_trajectory_input =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms;

  uint64_t ref_plan_init_timestamp_ms = 0;
  uint8_t timestamp_bytes[8] = {0};
  TrajectoryInput trajectory_input = {0};

  // Case 1: verify the timestamp, replan_start_idx = 0.
  ref_plan_init_timestamp_ms = 0;
  raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve0.value = 0;
  common::Uint64ToBytes(ref_plan_init_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  memcpy(raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp
             .value,
         timestamp_bytes, sizeof(timestamp_bytes));
  TrajectoryDecode(&trajectory_input);
  const auto& trajectory_pose = trajectory_input.trajectory_pose;
  for (size_t i = 0; i < 100; ++i) {
    EXPECT_EQ(trajectory_pose[i].timestamp_ms, i * 100);
  }

  // Case 2: verify the timestamp, replan_start_idx = 5.
  ref_plan_init_timestamp_ms = 693248338;
  raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve0.value = 5;
  common::Uint64ToBytes(ref_plan_init_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  memcpy(raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp
             .value,
         timestamp_bytes, sizeof(timestamp_bytes));
  TrajectoryDecode(&trajectory_input);
  EXPECT_EQ(trajectory_pose[0].timestamp_ms, 693247838);
  EXPECT_EQ(trajectory_pose[1].timestamp_ms, 693247938);
  EXPECT_EQ(trajectory_pose[2].timestamp_ms, 693248038);
  EXPECT_EQ(trajectory_pose[3].timestamp_ms, 693248138);
  EXPECT_EQ(trajectory_pose[4].timestamp_ms, 693248238);
  EXPECT_EQ(trajectory_pose[5].timestamp_ms, 693248338);
  EXPECT_EQ(trajectory_pose[6].timestamp_ms, 693248438);
  EXPECT_EQ(trajectory_pose[95].timestamp_ms, 693257338);

  // Case 2: verify the timestamp, replan_start_idx = 21.
  ref_plan_init_timestamp_ms = 29338;
  common::Uint64ToBytes(ref_plan_init_timestamp_ms, timestamp_bytes,
                        sizeof(timestamp_bytes), true);
  memcpy(raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoInitTimeStamp
             .value,
         timestamp_bytes, sizeof(timestamp_bytes));
  raw_trajectory_input.Rte_AcuTrajectoryInfo_TrajectoryInfoReserve0.value = 21;
  TrajectoryDecode(&trajectory_input);
  EXPECT_EQ(trajectory_pose[0].timestamp_ms, 27238);
  EXPECT_EQ(trajectory_pose[20].timestamp_ms, 29238);
  EXPECT_EQ(trajectory_pose[21].timestamp_ms, 29338);
  EXPECT_EQ(trajectory_pose[22].timestamp_ms, 29438);
  EXPECT_EQ(trajectory_pose[99].timestamp_ms, 37138);
}

TEST(AcuControlDecode, AcuControlDecodeTest) {
  auto& raw_trajectory_input =
      Rte_MainTask_Core0_60ms.Rte_RB.Rte_AcuSigInput_TrajectoryInput_60ms;

  for (int32_t i = -8; i <= 8; ++i) {
    auto ref_value = static_cast<float>(i);

    // wheel angle offset
    raw_trajectory_input.Rte_AcuTrajectoryInfo_ControlWheelAngleOffset.value =
        ref_value / 1e-6F;

    AcuControl acu_control;
    AcuControlDecode(&acu_control);

    EXPECT_NEAR(acu_control.calibrated_vehicle_params.wheel_angle_offset,
                ref_value, 1e-6F);
  }
}

}  // namespace acu_signal_input
}  // namespace fallback_adas
