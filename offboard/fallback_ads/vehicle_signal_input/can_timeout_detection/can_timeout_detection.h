
#ifndef OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_TIMEOUT_DETECTION_CAN_TIMEOUT_DETECTION_H_
#define OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_TIMEOUT_DETECTION_CAN_TIMEOUT_DETECTION_H_

#ifdef __cplusplus
extern "C" {
#endif

#include <stdbool.h>
#include <stdint.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "vehicle_signal_input/vehicle_signal_input_type.h"

void CanCounterMapping(const VehicleAndAcuCanSignal *can_signal_ptr,
                       uint8 mcu_can_counter[], int size);

void GetMcuCanTimeoutResult(const float can_timeout_timer_array[],
                            const bool can_timeout_enable_array[], int size,
                            VSI_McuCanTimeout_Struct *mcu_can_timeout_ptr);

extern bool CheckMainCanTimeout(uint8_t main_can_counter);

extern bool CheckRedundantCanTimeout(uint8_t redundant_can_counter);

extern void CheckMcuCanTimeout(const VehicleAndAcuCanSignal *can_signal,
                               VSI_McuCanTimeout_Struct *mcu_can_timeout_ptr);

#ifdef __cplusplus
}
#endif

#endif  // OFFBOARD_FALLBACK_ADS_VEHICLE_SIGNAL_INPUT_CAN_TIMEOUT_DETECTION_CAN_TIMEOUT_DETECTION_H_
