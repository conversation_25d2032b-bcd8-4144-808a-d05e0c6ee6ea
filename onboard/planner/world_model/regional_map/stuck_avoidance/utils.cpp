#include "planner/world_model/regional_map/stuck_avoidance/utils.h"

#include <algorithm>
#include <iterator>
#include <memory>
#include <optional>
#include <set>
#include <string>
#include <utility>

#include "math/bezier_curve_2d.h"
#include "math/range.h"
#include "planner/constants.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_rule_utility.h"
#include "planner/utility/common/common_utility.h"
#include "planner/utility/common/lane_search_util.h"
#include "planner/world_model/drivable_lane_reasoner/drivable_lane_reasoner.h"
#include "planner/world_model/lane_blockage/lane_blockage.h"
#include "planner/world_model/regional_map/common_utility.h"
#include "planner/world_model/regional_map/stuck_avoidance/debug_util.h"
#include "planner/world_model/regional_map/stuck_avoidance/stuck_avoidance_param.h"
#include "pnc_map_service/map_elements/lane.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace lane_selection {
namespace {
// The minimum length allow to trim stuck segment.
constexpr double kMinDistanceRemainAllowToTrim = 0.2;
// Max distance to relax lane marking around object foward direction.
constexpr double kMaxForwardDistanceAroundObjectToRelaxLaneChange = 15.0;
// Max distance to relax lane marking around object backward direction.
constexpr double kMaxBackwardDistanceAroundObjectToRelaxLaneChange = 15.0;
// The maximum stuck segment to store.
constexpr size_t kMaxStuckLaneSegmentsSize = 500;
// The minimum height of a vehicle that may cause occlusion.
constexpr double kMinHeightToCauseOcclusionInMeter = 1.85;
// The minimum length of a vehicle that may cause occlusion.
constexpr double kMinLengthToCauseOcclusionInMeter = 8.0;
// Maximum layer to search right turn only road.
constexpr int kMaxLayerForRightTurnOnlyRoad = 3;
// Default threshold to check if the hard boundary is close enough to the
// corresponding soft boundary.
constexpr double kHardBoundaryToSoftBoundaryDistanceThresholdInMeter = 2.0;

// Gets the range in a lane in which there are no blocked lane marking.
std::vector<math::Range1d> GetRangesWithoutBlockedLaneMarking(
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocked_lane_markings,
    const pnc_map::Lane* lane, const math::pb::Range& arc_length_range_m,
    bool to_left) {
  if (!common::IsValidArcLengthRange(arc_length_range_m.start_pos(),
                                     arc_length_range_m.end_pos())) {
    return {};
  }
  std::vector<math::Range1d> raw_relax_ranges{math::Range1d(
      arc_length_range_m.start_pos(), arc_length_range_m.end_pos())};
  if (lane == nullptr) {
    return raw_relax_ranges;
  }
  const pnc_map::LaneMarking* target_marking =
      to_left ? lane->left_marking() : lane->right_marking();
  if (target_marking == nullptr) {
    return raw_relax_ranges;
  }
  // [Note]: the |arc_length_range_m| is on lane's center line, in some cases,
  // the length of lane marking and lane are not same.
  const double projected_start_pos =
      std::max(arc_length_range_m.start_pos() / lane->length() *
                   target_marking->line().GetTotalArcLength(),
               arc_length_range_m.start_pos());
  const double projected_end_pos =
      std::max(arc_length_range_m.end_pos() / lane->length() *
                   target_marking->line().GetTotalArcLength(),
               arc_length_range_m.end_pos());
  raw_relax_ranges = {math::Range1d(projected_start_pos, projected_end_pos)};
  const auto iter = blocked_lane_markings.find(target_marking->id());
  if (iter == blocked_lane_markings.end() || iter->second.empty()) {
    return raw_relax_ranges;
  }
  const auto& blocked_lane_marking_segments = iter->second;
  std::vector<math::Range1d> blocked_marking_ranges;
  blocked_marking_ranges.reserve(blocked_lane_marking_segments.size());
  for (const auto& segment : blocked_lane_marking_segments) {
    if (!math::IsValidRange(segment.arc_length_range_m)) {
      continue;
    }
    blocked_marking_ranges.emplace_back(segment.arc_length_range_m);
  }
  return math::SubtractRanges(std::vector<math::Range1d>(raw_relax_ranges),
                              math::MergeRange(blocked_marking_ranges));
}

// Gets the range whose lane marking on the target side is safety to be relaxed
// in a lane.
std::vector<math::Range1d> GetValidRelaxedLaneMarkingRanges(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const common::LaneSegment& lane_segment,
    const std::set<int64_t>& violable_lane_marking_ids,
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocked_lane_marking_segments,
    bool allow_cross_road_lane_change, bool to_left) {
  const pnc_map::Lane* neighbor_lane = to_left
                                           ? lane_segment.lane->left_lane()
                                           : lane_segment.lane->right_lane();
  // TODO(songjingru): Use road neighbor lanes afterwards.
  const bool has_lane_change_neighbor_lane =
      allow_cross_road_lane_change
          ? true
          : neighbor_lane != nullptr &&
                drivable_lane_reasoner.IsLaneRobotDrivable(
                    neighbor_lane, /*check_local_hold=*/true,
                    /*check_edge_filter=*/false);
  if (!has_lane_change_neighbor_lane) {
    return {};
  }
  const pnc_map::LaneMarking* lane_marking =
      to_left ? lane_segment.lane->left_marking()
              : lane_segment.lane->right_marking();
  if (lane_marking == nullptr) {
    return {};
  }
  if (violable_lane_marking_ids.find(lane_marking->id()) !=
      violable_lane_marking_ids.end()) {
    return {};
  }
  if (IsValidLaneMarkingRangeForLaneChange(
          lane_marking, lane_segment.arc_length_range_m, to_left,
          /*use_raw_attributes=*/true)) {
    return {};
  }
  // For cross road lane change, we will check hard boundary while add lane
  // change edge.
  if (!allow_cross_road_lane_change &&
      HasHardBoundaryInSegment(joint_pnc_map_service, lane_segment, to_left)) {
    return {};
  }
  return GetRangesWithoutBlockedLaneMarking(
      blocked_lane_marking_segments, lane_segment.lane,
      lane_segment.arc_length_range_m, to_left);
}

}  // namespace

std::optional<double> GetTrimmedSegmentEndPos(
    const common::LaneSegment& segment,
    const pnc_map::LaneMarking* lane_marking,
    const std::vector<hdmap::util::RelaxLaneMarkingSegments>&
        relax_lane_marking_segments) {
  if (segment.lane == nullptr || lane_marking == nullptr) {
    return std::nullopt;
  }
  for (const auto& relax_segment : relax_lane_marking_segments) {
    if (relax_segment.lane_marking_ptr->id() != lane_marking->id()) {
      continue;
    }
    for (const auto& range : relax_segment.relaxed_ranges_in_arc_length) {
      const double projected_end_pos =
          std::max(range.end_pos / lane_marking->line().GetTotalArcLength() *
                       segment.lane->length(),
                   range.end_pos);
      if (segment.arc_length_range_m.end_pos() >= projected_end_pos) {
        return projected_end_pos;
      }
    }
  }
  return std::nullopt;
}

void TrimStuckLaneSegmentByRelaxLaneMarkingParamEndPos(
    hdmap::util::RelaxLaneMarkingParameter& relax_lane_marking_param,
    std::vector<common::LaneSegment>* stuck_lane_segments) {
  const auto relax_lane_marking_segments =
      relax_lane_marking_param.GetRelaxLaneMarkingSegments();
  if (stuck_lane_segments == nullptr || relax_lane_marking_segments.empty()) {
    return;
  }
  std::vector<common::LaneSegment> trimmed_segments;
  trimmed_segments.reserve(stuck_lane_segments->size());

  for (const auto& segment : *stuck_lane_segments) {
    if (segment.lane == nullptr) {
      continue;
    }
    // Initialize variables for segment trimming
    bool trimmed = false;
    double start_pos = segment.arc_length_range_m.start_pos();
    const double segment_original_end = segment.arc_length_range_m.end_pos();
    std::optional<double> left_end_pos;
    std::optional<double> right_end_pos;
    const pnc_map::LaneMarking* left_marking = segment.lane->left_marking();
    if (left_marking != nullptr) {
      // Find matching relaxed marking segments and get valid end position,if
      // segment_original_end > range.end_pos we assume segment should be
      // trimmed
      left_end_pos = GetTrimmedSegmentEndPos(segment, left_marking,
                                             relax_lane_marking_segments);
    }
    const pnc_map::LaneMarking* right_marking = segment.lane->right_marking();
    if (right_marking != nullptr) {
      right_end_pos = GetTrimmedSegmentEndPos(segment, right_marking,
                                              relax_lane_marking_segments);
    }
    // Calculate final end position considering both markings
    double selected_end_pos = segment_original_end;
    // Only update end_pos if it would not create an invalid range
    if (left_end_pos.has_value() && right_end_pos.has_value()) {
      double potential_end =
          std::max(left_end_pos.value(), right_end_pos.value());
      if (potential_end - start_pos > kMinDistanceRemainAllowToTrim) {
        selected_end_pos = potential_end;
        trimmed = true;
      } else {
        trimmed = false;
      }
    } else if (left_end_pos.has_value()) {
      if (left_end_pos.value() - start_pos > kMinDistanceRemainAllowToTrim) {
        selected_end_pos = left_end_pos.value();
        trimmed = true;
      } else {
        trimmed = false;
      }
    } else if (right_end_pos.has_value()) {
      if (right_end_pos.value() - start_pos > kMinDistanceRemainAllowToTrim) {
        selected_end_pos = right_end_pos.value();
        trimmed = true;
      } else {
        trimmed = false;
      }
    }
    // Create new segment with trimmed or original length
    if (trimmed) {
      trimmed_segments.emplace_back(segment.lane,
                                    segment.arc_length_range_m.start_pos(),
                                    selected_end_pos);
    } else {
      trimmed_segments.push_back(segment);
    }
  }
  // Replace original segments with trimmed segments
  *stuck_lane_segments = std::move(trimmed_segments);
}
// Get segment from max_backward_length_around_object to
// max_forward_length_around_object around object.
void GetLaneSegmentsAroundObject(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<common::LaneSegment>& segments_at_object,
    double max_backward_length_around_object,
    double max_forward_length_around_object,
    std::vector<common::LaneSegment>* lane_segments_around_object) {
  if (!lane_segments_around_object) {
    return;
  }
  lane_segments_around_object->clear();
  if (segments_at_object.empty()) {
    return;
  }
  const common::LaneSegment& last_lane_segment = segments_at_object.back();
  const common::LaneSegment& first_lane_segment = segments_at_object.front();

  // Get the lane segments before the object.
  if (first_lane_segment.lane) {
    *lane_segments_around_object = BuildPathFromLaneWithMaxLength(
        drivable_lane_reasoner, first_lane_segment.lane,
        first_lane_segment.arc_length_range_m.start_pos(),
        max_backward_length_around_object,
        /*need_reverse_path=*/true,
        /*allow_undrivable_lane=*/false,
        /*allowed_search_sections=*/std::nullopt,
        /*IsLeafNode=*/[](const PathSearchNode& node) {
          return node.current_lane_segment().lane->predecessors().empty();
        });
  }

  // Get the lane segments at the object.
  std::copy(segments_at_object.begin(), segments_at_object.end(),
            std::back_inserter(*lane_segments_around_object));

  // Get the lane segments after the object.
  if (last_lane_segment.lane) {
    auto result = BuildPathFromLaneWithMaxLength(
        drivable_lane_reasoner, last_lane_segment.lane,
        last_lane_segment.arc_length_range_m.end_pos(),
        max_forward_length_around_object,
        /*need_reverse_path=*/false,
        /*allow_undrivable_lane=*/false,
        /*allowed_search_sections=*/std::nullopt,
        /*IsLeafNode=*/[](const PathSearchNode& node) {
          return node.current_lane_segment().lane->successors().empty();
        });
    if (!result.empty()) {
      std::move(result.begin(), result.end(),
                std::back_inserter(*lane_segments_around_object));
    }
  }
}

// Relax lane marking max_forward_length_around_object and
// max_backward_length_around_object around objects.
void UpdateRelaxLaneMarkingsAroundObjects(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocked_lane_marking_segments,
    bool allow_cross_road_lane_change,
    const std::set<StuckObject>& stuck_objects,
    hdmap::util::RelaxLaneMarkingParameter* relax_lane_marking_param) {
  if (stuck_objects.empty() || relax_lane_marking_param == nullptr) {
    return;
  }
  for (const auto& stuck_object : stuck_objects) {
    if (stuck_object.lane_segments.empty()) {
      continue;
    }

    std::vector<common::LaneSegment> lane_segments_around_object;
    GetLaneSegmentsAroundObject(
        drivable_lane_reasoner, stuck_object.lane_segments,
        kMaxBackwardDistanceAroundObjectToRelaxLaneChange,
        kMaxForwardDistanceAroundObjectToRelaxLaneChange,
        &lane_segments_around_object);

    for (const auto& lane_segment : lane_segments_around_object) {
      const pnc_map::Lane* lane = lane_segment.lane;
      if (lane == nullptr) {
        continue;
      }

      // Check left marking
      const std::vector<math::Range1d> valid_segments_to_left =
          GetValidRelaxedLaneMarkingRanges(
              joint_pnc_map_service, drivable_lane_reasoner, lane_segment,
              /*violable_lane_marking_ids=*/{}, blocked_lane_marking_segments,
              allow_cross_road_lane_change,
              /*to_left=*/true);
      if (!valid_segments_to_left.empty()) {
        const pnc_map::LaneMarking* lane_marking =
            lane_segment.lane->left_marking();
        if (lane_marking == nullptr) {
          continue;
        }
        const hdmap::LaneMarking& hdmap_lane_marking = lane_marking->proto();
        const hdmap::util::RelaxLaneMarkingSegments relax_lane_marking_segments(
            std::make_shared<const hdmap::LaneMarking>(hdmap_lane_marking),
            hdmap::RelaxLaneMarkingDirection::kRelaxToBoth,
            {valid_segments_to_left});
        relax_lane_marking_param->AddRelaxLaneMarkingSegments(
            relax_lane_marking_segments);
      }

      // Check right marking
      const std::vector<math::Range1d> valid_segments_to_right =
          GetValidRelaxedLaneMarkingRanges(
              joint_pnc_map_service, drivable_lane_reasoner, lane_segment,
              /*violable_lane_marking_ids=*/{}, blocked_lane_marking_segments,
              allow_cross_road_lane_change,
              /*to_left=*/
              false);
      if (!valid_segments_to_right.empty()) {
        const pnc_map::LaneMarking* lane_marking =
            lane_segment.lane->right_marking();
        if (lane_marking == nullptr) {
          continue;
        }
        const hdmap::LaneMarking& hdmap_lane_marking = lane_marking->proto();
        const hdmap::util::RelaxLaneMarkingSegments relax_lane_marking_segments(
            std::make_shared<const hdmap::LaneMarking>(hdmap_lane_marking),
            hdmap::RelaxLaneMarkingDirection::kRelaxToBoth,
            {valid_segments_to_right});
        relax_lane_marking_param->AddRelaxLaneMarkingSegments(
            relax_lane_marking_segments);
      }
    }
  }
}

void UpdateStuckLaneSegmentsToMakeSectionPreferenceValid(
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    const std::optional<size_t>& current_lane_on_global_idx,
    const std::optional<size_t>& first_enter_lane_along_global_idx,
    std::vector<common::LaneSegment>* stuck_lane_segments) {
  if (global_lane_sequence.empty() || !current_lane_on_global_idx.has_value() ||
      !first_enter_lane_along_global_idx.has_value() ||
      (first_enter_lane_along_global_idx.value() + 1) >=
          global_lane_sequence.size() ||
      stuck_lane_segments == nullptr) {
    return;
  }
  if (!stuck_lane_segments->empty()) {
    stuck_lane_segments->clear();
  }
  for (size_t idx = current_lane_on_global_idx.value();
       idx <= (first_enter_lane_along_global_idx.value() + 1); ++idx) {
    const pnc_map::Lane* lane = global_lane_sequence.at(idx);
    if (lane == nullptr) {
      continue;
    }
    stuck_lane_segments->emplace_back(lane, 0.0, lane->length());
  }
}

pb::UnstuckMonitorEventInfo CreateMonitorEventInfo(
    const RelaxLaneSequenceScenarioParameter& scenario_param,
    const int mission_id) {
  pb::UnstuckMonitorEventInfo event_info;

  event_info.set_scenario_type(
      pb::RelaxLaneSequenceScenarioType_Name(scenario_param.scenario_type));
  event_info.set_timestamp_ms(scenario_param.start_stuck_timestamp_ms);
  event_info.set_reason(pb::StuckAvoidanceParameter_TerminateReason_Name(
      scenario_param.terminate_reason));
  event_info.set_id(strings::StringPrintf(
      "CarID_%s_StartTime_%ld_MissionID_%d",
      av_comm::CarId::Get().str().c_str(),
      scenario_param.start_stuck_timestamp_ms, mission_id));
  return event_info;
}

pb::UnstuckMonitorEventInfo CreateMonitorEventInfo(
    const StuckAvoidanceParameter& stuck_param, const int mission_id) {
  pb::UnstuckMonitorEventInfo event_info;

  // Handle multiple scenario types
  if (stuck_param.scenario_types.empty()) {
    event_info.set_scenario_type(pb::RelaxLaneSequenceScenarioType_Name(
        pb::RelaxLaneSequenceScenarioType::kInvalidScenario));
    return event_info;
  } else {
    event_info.set_scenario_type(pb::RelaxLaneSequenceScenarioType_Name(
        *stuck_param.scenario_types.begin()));
  }
  event_info.set_timestamp_ms(stuck_param.start_stuck_timestamp_ms);
  event_info.set_reason(pb::StuckAvoidanceParameter_TerminateReason_Name(
      stuck_param.terminate_reason));
  event_info.set_id(strings::StringPrintf("CarID_%s_StartTime_%ld_MissionID_%d",
                                          av_comm::CarId::Get().str().c_str(),
                                          stuck_param.start_stuck_timestamp_ms,
                                          mission_id));

  return event_info;
}

void PublishMonitorEvent(const pb::UnstuckMonitorEventInfo& event_info,
                         const std::string& stage) {
  std::string event_info_json;
  google::protobuf::util::MessageToJsonString(event_info, &event_info_json);

  routing::utility::AddRtEventAsJson<rt_event::planner::RouteUnstuck>(
      "event_type", stage, "key_id", std::to_string(event_info.timestamp_ms()),
      "event_detail", event_info_json);
}

// Gets search lane on specific direction.
std::vector<const pnc_map::Lane*> GetSearchLanesOnDirection(
    const pnc_map::Lane* lane, pb::SearchDirection search_direction) {
  if (!lane) {
    return {};
  }
  switch (search_direction) {
    case pb::SearchDirection::kSearchInLane:
      return {lane};
    case pb::SearchDirection::kSearchLeft:
      return {lane->left_lane()};
    case pb::SearchDirection::kSearchRight:
      return {lane->right_lane()};
    case pb::SearchDirection::kSearchInSection:
      return lane->section()->lanes();
    default:
      return {};
  }
  return {};
}

// Gets the first object in the |search_lane|.
const ObjectBlockingInfo* GetFirstTargetObjectInSearchLane(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const pnc_map::Lane& search_lane, const double range_start_arc_length,
    const double range_end_arc_length,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object)) {
  if (lane_blockages.empty() || IsTargetObject == nullptr) {
    return nullptr;
  }
  const auto& lane_blockage_it = lane_blockages.find(search_lane.id());
  if (lane_blockage_it == lane_blockages.end()) {
    // There are no objects in this lane, we just accumulate the range
    // length and continue to check the next lane.
    return nullptr;
  }
  // There are blockages in the lane, find the first target object info.
  double first_object_end_arc_length = std::numeric_limits<double>::max();
  const ObjectBlockingInfo* first_target_object = nullptr;
  for (const auto& object : lane_blockage_it->second.blocking_objects) {
    const double object_start_arc_length =
        object.along_lane_start_percentage() * search_lane.length();
    const double object_end_arc_length =
        object.along_lane_end_percentage() * search_lane.length();
    const bool object_in_valid_range =
        (range_start_arc_length <= object_start_arc_length &&
         object_start_arc_length <= range_end_arc_length) ||
        (range_start_arc_length <= object_end_arc_length &&
         object_end_arc_length <= range_end_arc_length);
    const bool should_ignore =
        (ignore_object.has_value() && ignore_object.value() != nullptr &&
         object.object_id() == ignore_object.value()->object_id());
    if (object_in_valid_range && !should_ignore && IsTargetObject(object) &&
        object_end_arc_length < first_object_end_arc_length) {
      first_target_object = &object;
      first_object_end_arc_length = object_end_arc_length;
    }
  }
  return first_target_object;
}

std::optional<StartPointToObjectInfo>
FindFirstTargetObjectInfoAlongLaneSequence(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object),
    pb::SearchDirection search_direction) {
  if (lane_sequence.empty() || !IsTargetObject || max_length_to_find < 0.0 ||
      math::NearZero(max_length_to_find)) {
    return std::nullopt;
  }
  double accumulate_distance = 0.0;
  bool meet_start_lane = false;
  std::vector<common::LaneSegment> accumulate_lane_segments;
  accumulate_lane_segments.reserve(lane_sequence.size());
  const pnc_map::Lane* last_lane = nullptr;
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (!lane) {
      return std::nullopt;
    }
    if (!meet_start_lane) {
      if (lane->id() != start_lane.id()) {
        continue;
      }
      meet_start_lane = true;
    }
    const double remaining_distance =
        std::max(max_length_to_find - accumulate_distance, 0.0);
    if (math::NearZero(remaining_distance)) {
      // Do not find another target object in the range who starts from the
      // start point and have length of |max_length_to_find|.
      return std::nullopt;
    }
    if (stop_at_first_lane_change && last_lane &&
        !last_lane->IsSuccessor(*lane)) {
      // If we meet a lane change instance, it indicates that it is not
      // blocked before.
      return std::nullopt;
    }
    last_lane = lane;
    const double range_start_arc_length =
        lane->id() == start_lane.id() ? start_arc_length : 0.0;
    const double range_end_arc_length =
        std::min(range_start_arc_length + remaining_distance, lane->length());

    const std::vector<const pnc_map::Lane*> search_lanes =
        GetSearchLanesOnDirection(lane, search_direction);
    double first_object_end_arc_length = std::numeric_limits<double>::max();
    const ObjectBlockingInfo* first_target_object = nullptr;
    for (const pnc_map::Lane* search_lane : search_lanes) {
      if (search_lane == nullptr ||
          !drivable_lane_reasoner.IsLaneRobotDrivable(search_lane)) {
        continue;
      }
      const ObjectBlockingInfo* first_target_object_in_search_lane =
          GetFirstTargetObjectInSearchLane(
              lane_blockages, *search_lane, range_start_arc_length,
              range_end_arc_length, ignore_object, IsTargetObject);
      if (first_target_object_in_search_lane != nullptr) {
        const double first_object_end_arc_length_in_search_lane =
            first_target_object_in_search_lane->along_lane_end_percentage() *
            lane->length();
        if (first_object_end_arc_length_in_search_lane <
            first_object_end_arc_length) {
          first_target_object = first_target_object_in_search_lane;
          first_object_end_arc_length =
              first_object_end_arc_length_in_search_lane;
        }
      }
    }

    if (first_target_object != nullptr) {
      // Meet first target object after start point, return it.
      const double first_object_start_arc_length =
          first_target_object->along_lane_start_percentage() * lane->length();
      const double first_object_end_arc_length =
          first_target_object->along_lane_end_percentage() * lane->length();
      accumulate_distance +=
          std::max(0.0, first_object_start_arc_length - range_start_arc_length);
      accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                            first_object_end_arc_length);
      return StartPointToObjectInfo(first_target_object, lane,
                                    accumulate_distance,
                                    accumulate_lane_segments);
    }
    accumulate_distance += (range_end_arc_length - range_start_arc_length);
    accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                          range_end_arc_length);
  }
  return std::nullopt;
}

// TODO(zhanshushi): Merge logic with
// FindFirstTargetObjectInfoAlongLaneSequence.
std::vector<StartPointToObjectInfo> FindTargetsObjectInfoAlongLaneSequence(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object),
    pb::SearchDirection search_direction) {
  if (lane_sequence.empty() || !IsTargetObject || max_length_to_find < 0.0 ||
      math::NearZero(max_length_to_find)) {
    return {};
  }
  double accumulate_distance = 0.0;
  bool meet_start_lane = false;
  std::vector<common::LaneSegment> accumulate_lane_segments;
  accumulate_lane_segments.reserve(lane_sequence.size());
  std::vector<StartPointToObjectInfo> accumulated_objects;
  const pnc_map::Lane* last_lane = nullptr;
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (!lane) {
      return {};
    }
    if (!meet_start_lane) {
      if (lane->id() != start_lane.id()) {
        continue;
      }
      meet_start_lane = true;
    }
    const double remaining_distance =
        std::max(max_length_to_find - accumulate_distance, 0.0);
    if (math::NearZero(remaining_distance)) {
      // Do not find another target object in the range who starts from the
      // start point and have length of |max_length_to_find|.
      return accumulated_objects;
    }
    if (stop_at_first_lane_change && last_lane &&
        !last_lane->IsSuccessor(*lane)) {
      // If we meet a lane change instance, it indicates that it is not
      // blocked before.
      return accumulated_objects;
    }
    last_lane = lane;
    const double range_start_arc_length =
        lane->id() == start_lane.id() ? start_arc_length : 0.0;
    const double range_end_arc_length =
        std::min(range_start_arc_length + remaining_distance, lane->length());

    const std::vector<const pnc_map::Lane*> search_lanes =
        GetSearchLanesOnDirection(lane, search_direction);
    if (search_lanes.size() != 1) {
      return accumulated_objects;
    }
    const pnc_map::Lane* search_lane = search_lanes.front();
    if (search_lane == nullptr ||
        !drivable_lane_reasoner.IsLaneRobotDrivable(search_lane)) {
      return accumulated_objects;
    }

    const auto& lane_blockage_it = lane_blockages.find(search_lane->id());
    if (lane_blockage_it == lane_blockages.end()) {
      // There are no objects in this lane, we just accumulate the range
      // length and continue to check the next lane.
      accumulate_distance += (range_end_arc_length - range_start_arc_length);
      accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                            range_end_arc_length);
    } else {
      // If there are blockages in the lane, find the first target object info.
      for (const auto& object : lane_blockage_it->second.blocking_objects) {
        const double object_start_arc_length =
            object.along_lane_start_percentage() * lane->length();
        const double object_end_arc_length =
            object.along_lane_end_percentage() * lane->length();
        const bool object_in_valid_range =
            !(object_end_arc_length < range_start_arc_length ||
              object_start_arc_length > range_end_arc_length);
        const bool should_ignore =
            (ignore_object.has_value() && ignore_object.value() != nullptr &&
             object.object_id() == ignore_object.value()->object_id());
        if (object_in_valid_range && !should_ignore && IsTargetObject(object)) {
          const double dist_to_object_start_from_range_start =
              std::max(0.0, object_start_arc_length - range_start_arc_length);
          std::vector<common::LaneSegment> accumulate_lane_segments_to_object(
              accumulate_lane_segments.begin(), accumulate_lane_segments.end());
          accumulate_lane_segments_to_object.reserve(
              accumulate_lane_segments.size() + 1);
          accumulate_lane_segments_to_object.emplace_back(
              lane, range_start_arc_length, object_end_arc_length);
          accumulated_objects.emplace_back(
              &object, search_lane,
              accumulate_distance + dist_to_object_start_from_range_start,
              accumulate_lane_segments_to_object, lane_sequence);
        }
      }
      // In this lane, we can not find a target object, we just accumulate
      // the distance.
      accumulate_distance += (range_end_arc_length - range_start_arc_length);
      accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                            range_end_arc_length);
    }
  }
  return accumulated_objects;
}

std::optional<StartPointToObjectInfo>
FindFirstTargetObjectInfoAlongLaneSequenceByReverseSearching(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<const ObjectBlockingInfo*>& ignore_object,
    const pnc_map::Lane& start_lane, double search_start_arc_length,
    double max_length_to_find,
    bool (*IsTargetObject)(const ObjectBlockingInfo& object)) {
  if (lane_sequence.empty() || !IsTargetObject || max_length_to_find < 0.0 ||
      math::NearZero(max_length_to_find)) {
    return std::nullopt;
  }
  double accumulate_distance = 0.0;
  bool meet_start_lane = false;
  std::vector<common::LaneSegment> accumulate_lane_segments;
  accumulate_lane_segments.reserve(lane_sequence.size());
  const pnc_map::Lane* last_lane = nullptr;
  for (auto it = lane_sequence.rbegin(); it < lane_sequence.rend(); ++it) {
    const pnc_map::Lane* lane = *it;
    if (!lane) {
      return std::nullopt;
    }
    if (!meet_start_lane) {
      if (lane->id() != start_lane.id()) {
        continue;
      }
      meet_start_lane = true;
    }
    const double remaining_distance =
        std::max(max_length_to_find - accumulate_distance, 0.0);
    if (math::NearZero(remaining_distance)) {
      // Do not find another target object in the range who starts from the
      // start point and have length of |max_length_to_find|.
      return std::nullopt;
    }
    if (last_lane && !lane->IsSuccessor(*last_lane)) {
      // If we meet a lane change instance, it indicates that it is not
      // blocked before.
      return std::nullopt;
    }
    last_lane = lane;
    const double range_end_arc_length = lane->id() == start_lane.id()
                                            ? search_start_arc_length
                                            : lane->length();
    const double range_start_arc_length =
        std::max(range_end_arc_length - remaining_distance, 0.0);

    const auto& lane_blockage_it = lane_blockages.find(lane->id());
    if (lane_blockage_it == lane_blockages.end()) {
      // There are no objects in this lane, we just accumulate the range
      // length and continue to check the next lane.
      accumulate_distance += (range_end_arc_length - range_start_arc_length);
      accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                            range_end_arc_length);
    } else {
      // If there are blockages in the lane, find the first target object info.
      double first_object_end_arc_length = -1.0;
      const ObjectBlockingInfo* first_target_object = nullptr;
      for (const auto& object : lane_blockage_it->second.blocking_objects) {
        const double object_end_arc_length =
            object.along_lane_end_percentage() * lane->length();
        const bool object_in_valid_range =
            range_start_arc_length <= object_end_arc_length &&
            object_end_arc_length <= range_end_arc_length;
        const bool should_ignore =
            (ignore_object.has_value() && ignore_object.value() != nullptr &&
             object.object_id() == ignore_object.value()->object_id());
        if (object_in_valid_range && !should_ignore && IsTargetObject(object) &&
            object_end_arc_length > first_object_end_arc_length) {
          first_target_object = &object;
          first_object_end_arc_length = object_end_arc_length;
        }
      }
      if (first_target_object) {
        // Meet first target object after start point, return it.
        accumulate_distance +=
            (range_end_arc_length - first_object_end_arc_length);
        const double first_object_start_arc_length =
            first_target_object->along_lane_start_percentage() * lane->length();
        accumulate_lane_segments.emplace_back(
            lane, first_object_start_arc_length, range_end_arc_length);
        return StartPointToObjectInfo(first_target_object, lane,
                                      accumulate_distance,
                                      accumulate_lane_segments);
      }
      // In this lane, we can not find a target object, we just accumulate
      // the distance.
      accumulate_distance += (range_end_arc_length - range_start_arc_length);
      accumulate_lane_segments.emplace_back(lane, range_start_arc_length,
                                            range_end_arc_length);
    }
  }
  return std::nullopt;
}

const lane_candidate::CurrentLaneCandidate*
GetFirstCurrentLaneCandidateOnSequence(
    const std::vector<const pnc_map::Lane*>& last_selected_lane_sequence,
    const std::vector<lane_candidate::CurrentLaneCandidate>&
        current_lane_candidates) {
  if (last_selected_lane_sequence.empty()) {
    return nullptr;
  }

  for (const pnc_map::Lane* lane : last_selected_lane_sequence) {
    const auto& candidate_it = std::find_if(
        current_lane_candidates.begin(), current_lane_candidates.end(),
        [lane](const lane_candidate::CurrentLaneCandidate& candidate) {
          return lane != nullptr && candidate.lane()->id() == lane->id();
        });
    if (candidate_it != current_lane_candidates.end()) {
      return &(*candidate_it);
    }
  }
  return nullptr;
}

// Gets the optimal lane follow sequence candidate from last seed.
const pb::LaneSequenceCandidate*
GetOptimalLaneFollowSequenceCandidateFromLastSeed(
    const pb::LaneSequenceCandidates& last_candidates) {
  for (const auto& candidate : last_candidates.lane_sequence_candidates()) {
    if (candidate.lane_sequence_type() ==
        pb::LaneSequenceCandidate_LaneSequenceType_LANE_FOLLOW) {
      return &candidate;
    }
  }
  return nullptr;
}

// Gets the optimal lane change sequence candidate from last seed.
const pb::LaneSequenceCandidate*
GetOptimalLaneChangeSequenceCandidateFromLastSeed(
    const pb::LaneSequenceCandidates& last_candidates) {
  for (const auto& candidate : last_candidates.lane_sequence_candidates()) {
    if (candidate.lane_sequence_type() ==
        pb::LaneSequenceCandidate_LaneSequenceType_LANE_CHANGE) {
      return &candidate;
    }
  }
  return nullptr;
}

bool IsObjectStationary(const PlannerObject& planner_object) {
  // TODO(Shushi): Is just check speed OK here?
  return planner_object.speed() <= constants::kDefaultAgentNearStaticSpeedInMps;
}

bool IsValidStationaryVehicle(const ObjectBlockingInfo& object) {
  return object.IsVehicle() &&
         IsObjectStationary(*DCHECK_NOTNULL(object.planner_object())) &&
         object.IsBlockingObjectForRouting(
             /*exclude_soft_block_able_to_nudge=*/false);
}

bool IsLaneSegmentBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                          int64_t lane_id,
                          const std::optional<double>& start_arc_percent,
                          const std::optional<double>& end_arc_percent,
                          const std::optional<int64_t>& object_id,
                          const std::optional<pb::BlockingType>& object_type) {
  if (lane_blockages.empty()) {
    return false;
  }
  const auto lane_blockage_it = lane_blockages.find(lane_id);
  return (lane_blockage_it != lane_blockages.end() &&
          std::any_of(lane_blockage_it->second.blocking_objects.begin(),
                      lane_blockage_it->second.blocking_objects.end(),
                      [&start_arc_percent, &end_arc_percent, &object_id,
                       &object_type](const ObjectBlockingInfo& object) {
                        return (!start_arc_percent.has_value() ||
                                object.along_lane_end_percentage() >=
                                    *start_arc_percent) &&
                               (!end_arc_percent.has_value() ||
                                object.along_lane_start_percentage() <
                                    *end_arc_percent) &&
                               (!object_id.has_value() ||
                                object.object_id() == *object_id) &&
                               (!object_type.has_value() ||
                                object.type() == *object_type) &&
                               ShouldConsiderBlockageAsReal(object);
                      }));
}

bool IsLaneBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                   int64_t lane_id, const std::optional<int64_t>& object_id,
                   const std::optional<pb::BlockingType>& object_type) {
  return IsLaneSegmentBlocked(
      lane_blockages, lane_id, /*start_arc_percent=*/std::nullopt,
      /*end_arc_percent=*/std::nullopt, object_id, object_type);
}

bool IsLanesBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                    const std::vector<int64_t>& lane_ids,
                    const std::optional<int64_t>& object_id,
                    const std::optional<pb::BlockingType>& object_type) {
  return std::any_of(
      lane_ids.begin(), lane_ids.end(),
      [&lane_blockages, &object_id, &object_type](int64_t lane_id) {
        return IsLaneBlocked(lane_blockages, lane_id, object_id, object_type);
      });
}

bool IsLanesBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                    const std::vector<common::LaneSegment>& lane_segments,
                    const std::optional<int64_t>& object_id,
                    const std::optional<pb::BlockingType>& object_type) {
  return std::any_of(lane_segments.begin(), lane_segments.end(),
                     [&lane_blockages, &object_id,
                      &object_type](const common::LaneSegment& lane_segment) {
                       return lane_segment.lane != nullptr &&
                              IsLaneBlocked(lane_blockages,
                                            lane_segment.lane->id(), object_id,
                                            object_type);
                     });
}

// Returns true if we need to consider the blockage as a real one.
bool ShouldConsiderBlockageAsReal(const ObjectBlockingInfo& object) {
  if (!object.ShouldConsiderAsHardBlockingForRouting() &&
      !object.ShouldConsiderAsSoftBlockingForRouting()) {
    return false;
  }
  // Don't stuck avoidance if it's map change area.
  if (object.IsConstructionZone(voy::SpaceType::MAP_CHANGE_AREA)) {
    return false;
  }
  return true;
}

bool VehicleMayCauseOcclusion(const ObjectBlockingInfo& current_object) {
  // TODO(songjingru): refine the logic.
  const PlannerObject& planner_object =
      *DCHECK_NOTNULL(current_object.planner_object());
  return current_object.IsVehicle() &&
         (planner_object.height() > kMinHeightToCauseOcclusionInMeter ||
          planner_object.length() > kMinLengthToCauseOcclusionInMeter);
}

std::optional<double> GetDistanceToJunctionAlongLaneSequence(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lf_sequence_from_current_lane,
    const routing::pb::RouteLane& destination_lane,
    const pnc_map::Lane& start_lane, double start_percentage,
    bool stop_if_start_in_junction, double max_find_length) {
  if (lf_sequence_from_current_lane.empty()) {
    // The input is invalid, return nullopt.
    return std::nullopt;
  }
  const auto& start_lane_it =
      std::find_if(lf_sequence_from_current_lane.begin(),
                   lf_sequence_from_current_lane.end(),
                   [&start_lane](const pnc_map::Lane* lane) {
                     return lane != nullptr && lane->id() == start_lane.id();
                   });
  if (start_lane_it == lf_sequence_from_current_lane.end()) {
    // The start lane is not in the lane sequence, we do not need to search and
    // return nullopt.
    return std::nullopt;
  }
  double accumulate_dist = 0.0;
  double remaining_dist = max_find_length - accumulate_dist;
  const pnc_map::Lane* last_lane = nullptr;
  for (auto lane_it = start_lane_it;
       lane_it != lf_sequence_from_current_lane.end(); ++lane_it) {
    const pnc_map::Lane* lane = *lane_it;
    if (lane == nullptr) {
      return std::nullopt;
    }
    if (last_lane && !last_lane->IsSuccessor(*lane)) {
      // Meet a lane change instance, return nullopt.
      return std::nullopt;
    }
    // If lane is last lane of lane sequence and in destination section,
    // consider it as a destination. In this scenario, we can know that there
    // is no junction between ego and destination.
    if (lane_it == lf_sequence_from_current_lane.end() - 1 &&
        lane->section()->id() == destination_lane.section_id()) {
      return std::numeric_limits<double>::max();
    }
    const pnc_map::Section& section = *lane->section();
    if (lane_selection::IsKeyRouteSectionMayLeadToQueue(joint_pnc_map_service,
                                                        section) ||
        std::any_of(
            section.lanes().begin(), section.lanes().end(),
            [&joint_pnc_map_service](const pnc_map::Lane* lane) {
              return lane != nullptr && lane->type() == hdmap::Lane::VIRTUAL &&
                     !joint_pnc_map_service.GetTrafficSignals(*lane).empty();
            })) {
      // Scenario 1: Find the first junction, return the accumulated distance.
      // [Note]: All the section with traffic signal should be considered in
      // stuck avoidance.
      // TODO(zhanshushi): Check why the section of lane 148275 is a fork
      // section instead of a u-turn.
      if (stop_if_start_in_junction || lane->id() != start_lane.id()) {
        return accumulate_dist;
      }
    }
    const double range_start_arc = lane->id() == start_lane.id()
                                       ? start_lane.length() * start_percentage
                                       : 0.0;
    const double range_length =
        std::min(lane->length() - range_start_arc, remaining_dist);
    accumulate_dist += range_length;
    remaining_dist = std::max(max_find_length - accumulate_dist, 0.0);
    if (math::NearZero(remaining_dist)) {
      // Scenario 2: Do not find a junction in the max length range, we return
      // double max. In this scenario, we can know that the distance to junction
      // is bigger than |max_find_length| at least. So we return a double max to
      // distinguish with the scenario that we just find a junction at the point
      // whose distance to start point is |max_find_length|.
      return std::numeric_limits<double>::max();
    }
    last_lane = lane;
  }
  // Scenario 3: Do not find a junction in the whole lane sequence, we return
  // std::nullopt. In this scenario, the lane sequence is shorted than
  // |max_find_length| and we do not find a junction in the lane sequence. We
  // can not decide whether there is a junction in the next range, so return
  // std::nullopt.
  return std::nullopt;
}

std::vector<common::LaneSegment> ConstructLaneSegmentsFromLFSequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane* start_lane, double start_arc_length) {
  const auto& start_lane_it =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [start_lane](const pnc_map::Lane* lane) {
                     return lane != nullptr && lane->id() == start_lane->id();
                   });
  if (start_lane_it == lane_sequence.end()) {
    // The current lane is not in the lane sequence, we do not need to search
    // and return.
    return {};
  }
  std::vector<common::LaneSegment> lane_segments;
  lane_segments.reserve(lane_sequence.size());
  for (auto lane_it = start_lane_it; lane_it != lane_sequence.end();
       ++lane_it) {
    const pnc_map::Lane* lane = *lane_it;
    if (!lane) {
      return lane_segments;
    }
    const double start_pos =
        lane->id() == start_lane->id() ? start_arc_length : 0.0;
    lane_segments.emplace_back(lane, start_pos, lane->length());
  }
  return lane_segments;
}

// Returns true if the vehicle is still stationary. If it is not certain, a
// nullopt is expected.
std::optional<bool> IsTheStuckVehicleStillStationary(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::optional<int64_t>& object_id,
    const std::optional<int64_t>& object_lane_id,
    pb::BlockingType stuck_object_type) {
  if ((stuck_object_type != pb::BlockingType::kParked &&
       stuck_object_type != pb::BlockingType::kSlowMovingVehicle) ||
      !object_id.has_value() || !object_lane_id.has_value()) {
    return std::nullopt;
  }
  if (lane_blockages.empty()) {
    return false;
  }
  const auto lane_info_it = lane_blockages.find(object_lane_id.value());
  if (lane_info_it == lane_blockages.end()) {
    return false;
  }
  const auto& objects = lane_info_it->second.blocking_objects;
  const auto object_it =
      std::find_if(objects.begin(), objects.end(),
                   [&object_id](const ObjectBlockingInfo& object) {
                     return object.object_id() == object_id.value();
                   });
  if (object_it == objects.end()) {
    // The target vehicle do not exists, maybe is has move away.
    return false;
  }
  return object_it->IsVehicle() &&
         IsObjectStationary(*DCHECK_NOTNULL(object_it->planner_object()));
}

// Returns true if the blockage which caused stuck has disappeared.
bool HasStuckObjectDisappeared(
    const StuckObject& stuck_object,
    const std::map<int64_t, LaneBlockage>& lane_blockages) {
  switch (stuck_object.object_type) {
    case pb::BlockingType::kInvalid:
#pragma GCC diagnostic push
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
    case pb::BlockingType::kMaybeSlowMovingVehicle:
#pragma GCC diagnostic pop
    case pb::BlockingType::kSlowMovingPedOrCyclist:
    case pb::BlockingType::kRaBlockage:
    case pb::BlockingType::kLongtermBlockage:
    case pb::BlockingType::kBusInMotion:
    case pb::BlockingType::kBlockingPedestrian:
      return false;
    case pb::BlockingType::kSlowMovingVehicle:
    case pb::BlockingType::kTempParked:
    case pb::BlockingType::kParked: {
      // If the object cause end of lane sequence problem is a vehicle, we can
      // terminate the stuck avoidance when the vehicle start to move.
      return std::any_of(
          stuck_object.lane_segments.begin(), stuck_object.lane_segments.end(),
          [&lane_blockages,
           &stuck_object](const common::LaneSegment& lane_segment) {
            if (lane_segment.lane == nullptr) {
              return false;
            }
            const std::optional<bool> is_stationary =
                IsTheStuckVehicleStillStationary(
                    lane_blockages, stuck_object.object_id,
                    lane_segment.lane->id(), stuck_object.object_type);
            return is_stationary.has_value() && !is_stationary.value();
          });
    }
    case pb::BlockingType::kBackground:
    case pb::BlockingType::kTrafficCone: {
      // We check whether the object which cause stuck is still there and still
      // a blockage.
      return stuck_object.lane_segments.empty() ||
             !IsLanesBlocked(lane_blockages, stuck_object.lane_segments,
                             stuck_object.object_id, stuck_object.object_type);
    }
    case pb::BlockingType::kOccupancy:
    case pb::BlockingType::kConstructionZone:
    case pb::BlockingType::kMapChangeArea: {
      // For the construction zone is not a real object and it is generated by
      // geometry computing, and it has not been tracked in tracking module, the
      // object ID is not reliable, so we need to check whether the target lane
      // is still blocked by construction zone. If there is still a
      // construction zone, the lane will be blocked by the new one.
      return stuck_object.lane_segments.empty() ||
             !IsLanesBlocked(lane_blockages, stuck_object.lane_segments,
                             stuck_object.object_id, stuck_object.object_type);
    }
    default:
      return false;
  }
}

bool HasStuckObjectsDisappeared(
    const std::set<StuckObject>& stuck_objects,
    const std::map<int64_t, LaneBlockage>& lane_blockages) {
  return std::all_of(stuck_objects.begin(), stuck_objects.end(),
                     [&lane_blockages](const StuckObject& stuck_object) {
                       return HasStuckObjectDisappeared(stuck_object,
                                                        lane_blockages);
                     });
}

void MergeOverlappingSegments(
    std::vector<common::LaneSegment>* stuck_lane_segments) {
  if (stuck_lane_segments->empty()) {
    LOG(ERROR) << "No segments to merge.";
    return;
  }

  // Sort the segments by lane id and start position
  std::sort(stuck_lane_segments->begin(), stuck_lane_segments->end(),
            [](const common::LaneSegment& a, const common::LaneSegment& b) {
              if (a.lane->id() == b.lane->id()) {
                return a.arc_length_range_m.start_pos() <
                       b.arc_length_range_m.start_pos();
              }
              return a.lane->id() < b.lane->id();
            });

  std::vector<common::LaneSegment> merged_segments;
  merged_segments.reserve(stuck_lane_segments->size());

  size_t current_segment_index = 0;
  for (size_t i = 1; i < stuck_lane_segments->size(); ++i) {
    const common::LaneSegment& current_segment =
        stuck_lane_segments->at(current_segment_index);
    const common::LaneSegment& next_segment = stuck_lane_segments->at(i);

    if (current_segment.lane->id() == next_segment.lane->id() &&
        current_segment.arc_length_range_m.end_pos() >=
            next_segment.arc_length_range_m.start_pos()) {
      // Merge the segments
      stuck_lane_segments->at(current_segment_index)
          .arc_length_range_m.set_end_pos(
              std::max(current_segment.arc_length_range_m.end_pos(),
                       next_segment.arc_length_range_m.end_pos()));
    } else {
      // Add the current segment to the merged list and move to the next segment
      merged_segments.push_back(stuck_lane_segments->at(current_segment_index));
      current_segment_index = i;
    }
  }
  // Add the last segment
  merged_segments.push_back(stuck_lane_segments->at(current_segment_index));

  // Replace the original segments with the merged segments
  *stuck_lane_segments = std::move(merged_segments);
}

void UpdateSegmentsForStagedUnstuck(
    const DrivableLaneReasoner& drivable_lane_reasoner,
    const std::vector<common::LaneSegment>& raw_lane_segments,
    const bool need_to_lane_change_back,
    const hdmap::RelaxDirection avoid_direction,
    std::vector<common::LaneSegment>* stuck_lane_segments,
    std::vector<DirectedRelaxLaneSegments>*
        staged_relax_lane_segments_sequence) {
  const bool need_left = (avoid_direction == hdmap::RelaxDirection::kToLeft ||
                          avoid_direction == hdmap::RelaxDirection::kToBoth);
  const bool need_right = (avoid_direction == hdmap::RelaxDirection::kToRight ||
                           avoid_direction == hdmap::RelaxDirection::kToBoth);
  if (!need_left && !need_right) {
    return;
  }
  staged_relax_lane_segments_sequence->clear();
  staged_relax_lane_segments_sequence->emplace_back(raw_lane_segments,
                                                    avoid_direction);
  size_t lane_segments_size = raw_lane_segments.size();
  if (need_to_lane_change_back) {
    std::vector<common::LaneSegment> relax_lane_segments_back_to_right;
    relax_lane_segments_back_to_right.reserve(raw_lane_segments.size());
    std::vector<common::LaneSegment> relax_lane_segments_back_to_left;
    relax_lane_segments_back_to_left.reserve(raw_lane_segments.size());
    for (const auto& lane_segment : raw_lane_segments) {
      if (need_left && lane_segment.lane->left_lane() != nullptr &&
          drivable_lane_reasoner.IsLaneRobotDrivable(
              lane_segment.lane->left_lane(), /*check_local_hold=*/true,
              /*check_edge_filter=*/false)) {
        relax_lane_segments_back_to_right.emplace_back(
            lane_segment.lane->left_lane(),
            lane_segment.arc_length_range_m.start_pos(),
            lane_segment.lane->left_lane()->length());
        lane_segments_size++;
      }
      if (need_right && lane_segment.lane->right_lane() != nullptr &&
          drivable_lane_reasoner.IsLaneRobotDrivable(
              lane_segment.lane->right_lane(), /*check_local_hold=*/true,
              /*check_edge_filter=*/false)) {
        relax_lane_segments_back_to_left.emplace_back(
            lane_segment.lane->right_lane(),
            lane_segment.arc_length_range_m.start_pos(),
            lane_segment.lane->right_lane()->length());
        lane_segments_size++;
      }
    }
    if (!relax_lane_segments_back_to_left.empty()) {
      staged_relax_lane_segments_sequence->emplace_back(
          relax_lane_segments_back_to_left, hdmap::RelaxDirection::kToLeft);
    }
    if (!relax_lane_segments_back_to_right.empty()) {
      staged_relax_lane_segments_sequence->emplace_back(
          relax_lane_segments_back_to_right, hdmap::RelaxDirection::kToRight);
    }
  }
  stuck_lane_segments->clear();
  stuck_lane_segments->reserve(lane_segments_size);
  size_t current_size = 0;
  for (const auto& staged_directed_relax_lane_segment :
       *staged_relax_lane_segments_sequence) {
    for (const auto& lane_segment :
         staged_directed_relax_lane_segment.lane_segments) {
      if (current_size >= kMaxStuckLaneSegmentsSize) {
        LOG(ERROR) << "Reached maximum stuck lane segments size limit of "
                   << kMaxStuckLaneSegmentsSize
                   << ", truncating remaining segments";
        break;  // Exit both loops once limit is reached
      }
      stuck_lane_segments->emplace_back(lane_segment);
      current_size++;
    }
  }
  MergeOverlappingSegments(stuck_lane_segments);
}

bool IsEgoInLaneSegmentSequence(
    const RobotStateSnapshot& robot_state_snapshot,
    const std::vector<common::LaneSegment>& stuck_lane_segments,
    bool check_section_range) {
  // Store lane ids that have been checked for intersection.
  std::set<int64_t> visited_lane_ids;
  for (const common::LaneSegment& segment : stuck_lane_segments) {
    if (segment.lane == nullptr) {
      continue;
    }
    if (visited_lane_ids.find(segment.lane->id()) != visited_lane_ids.end()) {
      continue;
    }
    const double ego_front_arc_length =
        segment.lane->GetArclength(robot_state_snapshot.front_axle_position());
    const double ego_rear_arc_length =
        segment.lane->GetArclength(robot_state_snapshot.rear_axle_position());
    const double segment_start = segment.arc_length_range_m.start_pos();
    const double segment_end = segment.arc_length_range_m.end_pos();
    if (!math::IsInRange(ego_front_arc_length, segment_start, segment_end) &&
        !math::IsInRange(ego_rear_arc_length, segment_start, segment_end)) {
      // The arc length of ego is not in the range of lane segment, ego should
      // not in the segment.
      continue;
    }
    visited_lane_ids.insert(segment.lane->id());
    const auto& border = check_section_range ? segment.lane->section()->border()
                                             : segment.lane->border();
    if (math::geometry::Intersects(border,
                                   robot_state_snapshot.bounding_box())) {
      // Ego intersect with the segment which means ego is in the lane
      // segment.
      return true;
    }
  }
  return false;
}

bool IsLaneSegmentBlocked(const std::map<int64_t, LaneBlockage>& lane_blockages,
                          const common::LaneSegment& lane_segment) {
  const pnc_map::Lane* lane = lane_segment.lane;
  return lane != nullptr &&
         IsLaneSegmentBlocked(
             lane_blockages, lane->id(),
             /*start_arc_percent*/ lane_segment.arc_length_range_m.start_pos() /
                 lane->length(),
             /*end_arc_percent=*/lane_segment.arc_length_range_m.end_pos() /
                 lane->length(),
             /*object_id=*/std::nullopt, /*object_type=*/std::nullopt);
}

bool IsLaneSegmentsBlocked(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::vector<common::LaneSegment>& lane_segments) {
  return !lane_blockages.empty() && !lane_segments.empty() &&
         std::any_of(
             lane_segments.begin(), lane_segments.end(),
             [&lane_blockages](const common::LaneSegment& lane_segment) {
               return IsLaneSegmentBlocked(lane_blockages, lane_segment);
             });
}

bool IsObjectsAssociatedLaneSegmentsBlocked(
    const std::map<int64_t, LaneBlockage>& lane_blockages,
    const std::set<StuckObject>& objects) {
  return std::any_of(objects.begin(), objects.end(),
                     [&lane_blockages](const StuckObject& object) {
                       return IsLaneSegmentsBlocked(lane_blockages,
                                                    object.lane_segments);
                     });
}

void ConvertObjectInfosIntoStuckObjects(
    const std::vector<const ObjectBlockingInfo*>& object_infos,
    std::set<StuckObject>* stuck_objects) {
  if (object_infos.empty() || stuck_objects == nullptr) {
    return;
  }
  for (const auto* object_info : object_infos) {
    if (object_info == nullptr || object_info->lane() == nullptr) {
      continue;
    }
#pragma GCC diagnostic push
    // voy::SpaceType::OCCUPANCY is deprecated
#pragma GCC diagnostic ignored "-Wdeprecated-declarations"
    const bool is_construction_zone_from_occupancy =
        object_info != nullptr &&
        object_info->type() == pb::kConstructionZone &&
        DCHECK_NOTNULL(object_info->construction_zone())->space_type ==
            voy::SpaceType::OCCUPANCY;
    std::vector<common::LaneSegment> lane_segments;
    lane_segments.reserve(1);
    lane_segments.emplace_back(object_info->lane(),
                               object_info->along_lane_start_percentage() *
                                   object_info->lane()->length(),
                               object_info->along_lane_end_percentage() *
                                   object_info->lane()->length());
    stuck_objects->insert(StuckObject(object_info->object_id(),
                                      object_info->type(), lane_segments,
                                      is_construction_zone_from_occupancy));
  }
}

void GetObjectsAssociateLaneSegments(
    const std::set<StuckObject>& stuck_objects,
    std::vector<common::LaneSegment>* lane_segments_contains_objects) {
  if (stuck_objects.empty() || lane_segments_contains_objects == nullptr) {
    return;
  }
  lane_segments_contains_objects->reserve(stuck_objects.size() * 2);
  for (const auto& object : stuck_objects) {
    for (const auto& segment : object.lane_segments) {
      if (segment.lane == nullptr) {
        continue;
      }
      lane_segments_contains_objects->emplace_back(
          segment.lane, segment.arc_length_range_m.start_pos(),
          segment.arc_length_range_m.end_pos());
    }
  }
}

bool IsLaneSequencePassObjectAssociateLanes(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::set<StuckObject>& stuck_objects) {
  std::vector<common::LaneSegment> lane_segments_contains_objects;
  GetObjectsAssociateLaneSegments(stuck_objects,
                                  &lane_segments_contains_objects);
  const auto IsLaneSegmentAssociatedLane =
      [&lane_segments_contains_objects](const pnc_map::Lane* lane) {
        return lane != nullptr &&
               std::any_of(lane_segments_contains_objects.begin(),
                           lane_segments_contains_objects.end(),
                           [&lane](const common::LaneSegment& lane_segment) {
                             return lane_segment.lane != nullptr &&
                                    lane->id() == lane_segment.lane->id();
                           });
      };
  return std::any_of(lane_sequence.begin(), lane_sequence.end(),
                     IsLaneSegmentAssociatedLane);
}

bool IsLaneSequencePassObjectAssociateLaneSegments(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const std::set<StuckObject>& stuck_objects, int64_t start_lane_id,
    double start_arc_length) {
  if (start_lane_id <= 0) {
    return false;
  }
  std::vector<common::LaneSegment> lane_segments_contains_objects;
  GetObjectsAssociateLaneSegments(stuck_objects,
                                  &lane_segments_contains_objects);
  bool meet_start_lane = false;
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (lane == nullptr) {
      return false;
    }
    if (!meet_start_lane) {
      if (lane->id() != start_lane_id) {
        continue;
      }
      meet_start_lane = true;
    }
    const math::Range1d search_range(
        lane->id() == start_lane_id ? start_arc_length : 0.0, lane->length());
    if (std::any_of(
            lane_segments_contains_objects.begin(),
            lane_segments_contains_objects.end(),
            [&lane, &search_range](const common::LaneSegment& lane_segment) {
              return lane_segment.lane->id() == lane->id() &&
                     lane_segment.arc_length_range_m.start_pos() <=
                         search_range.end_pos &&
                     lane_segment.arc_length_range_m.end_pos() >=
                         search_range.start_pos;
            })) {
      return true;
    }
  }
  return false;
}

bool IsValidLaneMarkingRangeForLaneChange(
    const pnc_map::LaneMarking* lane_marking,
    const math::pb::Range& arc_length_range_m, bool to_left,
    bool use_raw_attributes) {
  if (lane_marking->lane_marking_segments().empty()) {
    return true;
  }
  const hdmap::util::ViolateLaneMarkingSingleton&
      violate_lane_marking_singleton =
          hdmap::util::ViolateLaneMarkingSingleton::Get();
  for (const pnc_map::LaneMarkingSegment& segment :
       lane_marking->lane_marking_segments()) {
    bool is_ranges_overlapping =
        arc_length_range_m.start_pos() < segment.arclength_range.end_pos &&
        arc_length_range_m.end_pos() > segment.arclength_range.start_pos;
    if (is_ranges_overlapping &&
        (use_raw_attributes
             ? !hdmap::util::IsLaneChangeableAttributeType(segment.type,
                                                           to_left)
             : !violate_lane_marking_singleton
                    .IsLaneChangeableAttributeTypeInArcLengthRange(
                        segment.type, lane_marking->id(), to_left,
                        /*arc_length_start=*/
                        std::max(arc_length_range_m.start_pos(),
                                 segment.arclength_range.start_pos),
                        /*arc_length_end=*/
                        std::min(arc_length_range_m.end_pos(),
                                 segment.arclength_range.end_pos)))) {
      return false;
    }
  }
  return true;
}

void ReasonSectionPreferenceParamAlongGlobalRoute(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    size_t first_enter_lane_along_global_idx,
    SectionPreferenceParamForUnstuck* section_preference_param) {
  if (global_lane_sequence.empty() ||
      (first_enter_lane_along_global_idx + 1) >= global_lane_sequence.size() ||
      section_preference_param == nullptr) {
    return;
  }
  const pnc_map::Lane* enter_lane =
      global_lane_sequence.at(first_enter_lane_along_global_idx);
  const pnc_map::Lane* junction_lane =
      global_lane_sequence.at(first_enter_lane_along_global_idx + 1);
  if (enter_lane == nullptr || enter_lane->section() == nullptr ||
      junction_lane == nullptr || junction_lane->section() == nullptr) {
    return;
  }
  if (lane_selection::GetKeyRouteSectionType(joint_pnc_map_service,
                                             *junction_lane->section()) ==
          pb::KeyRouteSectionType::kNotKeySection &&
      !IsEnterLaneForRightTurnOnlyRoad(global_lane_sequence,
                                       first_enter_lane_along_global_idx)) {
    return;
  }
  section_preference_param->preferred_section_sequence.reserve(2);
  section_preference_param->preferred_section_sequence.push_back(
      enter_lane->section());
  section_preference_param->preferred_section_sequence.push_back(
      junction_lane->section());
  section_preference_param->junction_section_idx =
      section_preference_param->preferred_section_sequence.size() - 1;
  section_preference_param->junction_turn_type = junction_lane->turn();
}

bool HasTrafficLightAssociatedLane(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lanes) {
  for (const pnc_map::Lane* lane : lanes) {
    if (!joint_pnc_map_service.GetTrafficSignals(*DCHECK_NOTNULL(lane))
             .empty()) {
      return true;
    }
  }
  return false;
}

bool IsLaneControlledByTrafficLight(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pnc_map::Lane& lane, const voy::TrafficLights& traffic_lights,
    double waiting_duration) {
  const speed::traffic_rules::MatchedTrafficLightInfo matched_result =
      speed::traffic_rules::GetMatchedTrafficLight(joint_pnc_map_service, lane,
                                                   traffic_lights);
  if (matched_result.traffic_signal_detection == nullptr) {
    return false;
  }

  return matched_result.traffic_signal_detection->color() ==
             voy::TrafficLight::RED ||
         matched_result.traffic_signal_detection->color() ==
             voy::TrafficLight::UNKNOWN_COLOR ||
         matched_result.traffic_signal_detection->is_occluded() ||
         matched_result.traffic_signal_detection->duration() < waiting_duration;
}

bool IsWaitingForTrafficLight(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane& start_lane, const voy::TrafficLights& traffic_lights,
    double waiting_duration) {
  const auto& start_lane_it =
      std::find_if(lane_sequence.begin(), lane_sequence.end(),
                   [&start_lane](const pnc_map::Lane* lane) {
                     return lane != nullptr && lane->id() == start_lane.id();
                   });
  DCHECK(start_lane_it != lane_sequence.end());
  if (start_lane_it == lane_sequence.end()) {
    return false;
  }
  const pnc_map::Lane* last_lane = nullptr;
  const pnc_map::Lane* lane_before_tl = nullptr;
  for (auto lane_it = start_lane_it; lane_it != lane_sequence.end();
       ++lane_it) {
    const pnc_map::Lane* lane = *lane_it;
    if (lane == nullptr) {
      break;
    }
    if (last_lane && !last_lane->IsSuccessor(*lane)) {
      // Meet a lane change instance, break.
      break;
    }
    if (lane_selection::IsKeyRouteSectionMayLeadToQueue(joint_pnc_map_service,
                                                        *lane->section())) {
      break;
    }
    if (HasTrafficLightAssociatedLane(joint_pnc_map_service,
                                      lane->successors())) {
      lane_before_tl = lane;
      break;
    }
    last_lane = lane;
  }
  if (lane_before_tl == nullptr) {
    return false;
  }
  const auto& successor_lanes = lane_before_tl->successors();
  // Return true if any successor lanes are controlled by the RED traffic light.
  return std::any_of(successor_lanes.begin(), successor_lanes.end(),
                     [&tls = traffic_lights, &joint_pnc_map_service,
                      &waiting_duration](const pnc_map::Lane* lane) {
                       DCHECK(lane != nullptr);
                       return IsLaneControlledByTrafficLight(
                           joint_pnc_map_service, *lane, tls, waiting_duration);
                     });
}

std::vector<common::LaneSegment> ConstructLaneSegmentsFromLaneSequence(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pnc_map::Lane& start_lane, double start_arc_length,
    double max_length_to_find, bool stop_at_first_lane_change) {
  if (lane_sequence.empty() || max_length_to_find <= 0.0) {
    return {};
  }
  std::vector<common::LaneSegment> lane_segments;
  double accumulate_distance = 0.0;
  bool meet_start_lane = false;
  const pnc_map::Lane* last_lane = nullptr;
  for (const pnc_map::Lane* lane : lane_sequence) {
    if (lane == nullptr) {
      LOG(ERROR) << "lane is nullptr";
      return {};
    }
    if (!meet_start_lane) {
      if (lane->id() != start_lane.id()) {
        continue;
      }
      meet_start_lane = true;
    }
    const double remaining_distance =
        std::max(max_length_to_find - accumulate_distance, 0.0);
    if (math::NearZero(remaining_distance)) {
      return lane_segments;
    }
    // Only consider the lane segments before first lane change.
    if (stop_at_first_lane_change && last_lane != nullptr &&
        !last_lane->IsSuccessor(*lane)) {
      return lane_segments;
    }
    last_lane = lane;
    const double range_start_arc_length =
        lane->id() == start_lane.id() ? start_arc_length : 0.0;
    const double range_end_arc_length =
        std::min(range_start_arc_length + remaining_distance, lane->length());
    accumulate_distance += (range_end_arc_length - range_start_arc_length);
    lane_segments.emplace_back(lane, range_start_arc_length,
                               range_end_arc_length);
  }
  return lane_segments;
}

// Returns true if the |start_idx| in |global_lane_sequence| is the enter
// lane for right turn only road.
bool IsEnterLaneForRightTurnOnlyRoad(
    const std::vector<const pnc_map::Lane*>& global_lane_sequence,
    size_t start_idx) {
  if (global_lane_sequence.empty()) {
    return false;
  }
  auto end_idx = global_lane_sequence.size() - 1;
  if (start_idx >= end_idx) {
    return false;
  }
  end_idx = std::min(end_idx, start_idx + kMaxLayerForRightTurnOnlyRoad);
  for (size_t lane_idx = start_idx + 1; lane_idx <= end_idx; ++lane_idx) {
    const pnc_map::Lane* curr_lane = global_lane_sequence.at(lane_idx);
    if (!curr_lane || !curr_lane->section() ||
        curr_lane->section()->successors().empty() ||
        curr_lane->section()->successors().size() > 1) {
      return false;
    }
    if (curr_lane->section()->road()->side_road_type() ==
        hdmap::Road::EXCLUSIVE_RIGHT_TURN) {
      return true;
    }
  }
  return false;
}

bool IsRightTurnOnlyRoad(const pnc_map::Road& road) {
  const pnc_map::Road* search_road = &road;
  for (size_t idx = 0; idx < kMaxLayerForRightTurnOnlyRoad; ++idx) {
    if (search_road == nullptr || search_road->successors().size() != 1) {
      return false;
    }
    if (search_road->side_road_type() == hdmap::Road::EXCLUSIVE_RIGHT_TURN) {
      return true;
    }
    search_road = search_road->successors().front();
  }
  return false;
}

hdmap::Polyline CreatePolylineProto(
    const std::vector<math::geometry::Point2d>& points) {
  hdmap::Polyline polyline;
  double length = 0.0;
  const math::geometry::Point2d* prev_point = nullptr;
  for (const auto& p : points) {
    hdmap::Point* point = polyline.add_points();
    point->set_x(p.x());
    point->set_y(p.y());
    if (prev_point != nullptr) {
      length += math::geometry::Distance(prev_point, p);
    }
    prev_point = &p;
  }

  polyline.set_length(length);
  return polyline;
}

hdmap::Polygon CreatePolygonProto(
    const std::vector<std::vector<math::geometry::Point2d>>& rings) {
  hdmap::Polygon polygon;
  for (const auto& ring : rings) {
    *polygon.add_loops() = CreatePolylineProto(ring);
  }
  return polygon;
}

std::vector<math::geometry::Point2d> GetReferencePointsForRegularTurn(
    const math::geometry::PolylineCurve2d& source_line,
    const math::geometry::PolylineCurve2d& via_line) {
  if (source_line.size() < 2 || via_line.size() < 2) {
    return {};
  }
  const math::geometry::Point2d& enter_vector_start_point =
      *(source_line.points().end() - 2);
  const math::geometry::Point2d& enter_vector_end_point =
      source_line.points().back();
  const math::geometry::Point2d& exit_vector_start_point =
      *(via_line.points().end() - 2);
  const math::geometry::Point2d& exit_vector_end_point =
      via_line.points().back();
  const std::optional<math::geometry::Point2d> intersection_point =
      math::geometry::LineIntersection<std::optional<math::geometry::Point2d>>(
          enter_vector_start_point, enter_vector_end_point,
          exit_vector_end_point, exit_vector_start_point);
  if (!intersection_point.has_value()) {
    return {};
  }
  const math::BezierCurve2d bezierCurve2d(/*control_points=*/{
      enter_vector_end_point, *intersection_point, exit_vector_end_point});
  return bezierCurve2d.GetSkeleton(kSkeletonSampleResolutionInMeter);
}

std::vector<math::geometry::Point2d> GetReferencePointsInJunction(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction, const pnc_map::Lane& exit_lane) {
  std::vector<math::geometry::Point2d> reference_points;
  // We assume section in junction just has one kind of turn type.
  switch (via_lane_in_junction.turn()) {
    case hdmap::Lane::LEFT:
    case hdmap::Lane::RIGHT: {
      // If the section is left or right turn section, get sampled points based
      // on bezier curve.
      reference_points = GetReferencePointsForRegularTurn(
          relax_enter_lane.center_line(), via_lane_in_junction.center_line());
      break;
    }
    case hdmap::Lane::STRAIGHT: {
      // If the section' turn type is straight, construct a straight line and
      // sample points based on it.
      const math::geometry::PolylineCurve2d curve(
          {relax_enter_lane.center_line().points().back(),
           via_lane_in_junction.center_line().points().back()});
      reference_points = curve.GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/curve.GetTotalArcLength(),
          /*interval=*/kSkeletonSampleResolutionInMeter,
          math::pb::UseExtensionFlag::kForbid);
      break;
    }
    case hdmap::Lane::U_TURN: {
      // const pnc_map::Lane* successor = successors_of_via_lane.front();
      // For 3-part uturn, we reason the reference points between
      // |relax_enter_lane| and the regular uturn, based on bezier curve.
      if (exit_lane.type() == hdmap::Lane::REGULAR &&
          exit_lane.turn() == hdmap::Lane::U_TURN) {
        reference_points = GetReferencePointsForRegularTurn(
            relax_enter_lane.center_line(), via_lane_in_junction.center_line());
      } else {
        // For 1-part uturn, we can't handle it well, so we just ignore it.
        break;
      }
      break;
    }
    default:
      break;
  }
  return reference_points;
}

std::vector<math::geometry::Point2d> GetLaneMarkingPointsInJunction(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction, const pnc_map::Lane& exit_lane,
    bool to_left) {
  std::vector<math::geometry::Point2d> reference_points;
  const pnc_map::LaneMarking* enter_lane_marking =
      to_left ? relax_enter_lane.left_marking()
              : relax_enter_lane.right_marking();
  const pnc_map::LaneMarking* via_lane_marking =
      to_left ? via_lane_in_junction.left_marking()
              : via_lane_in_junction.right_marking();
  // We assume section in junction just has one kind of turn type.
  switch (via_lane_in_junction.turn()) {
    case hdmap::Lane::LEFT:
    case hdmap::Lane::RIGHT: {
      // If the section is left or right turn section, get sampled points based
      // on bezier curve.
      reference_points = GetReferencePointsForRegularTurn(
          enter_lane_marking->line(), via_lane_marking->line());
      break;
    }
    case hdmap::Lane::STRAIGHT: {
      // If the section' turn type is straight, construct a straight line and
      // sample points based on it.
      const math::geometry::PolylineCurve2d curve(
          {enter_lane_marking->line().points().back(),
           via_lane_marking->line().points().back()});
      reference_points = curve.GetSampledPoints(
          /*start_pos=*/0.0, /*end_pos=*/curve.GetTotalArcLength(),
          /*interval=*/kSkeletonSampleResolutionInMeter,
          math::pb::UseExtensionFlag::kForbid);
      break;
    }
    case hdmap::Lane::U_TURN: {
      // For 3-part uturn, we reason the reference points between
      // |relax_enter_lane| and the regular uturn, based on bezier curve.
      if (exit_lane.type() == hdmap::Lane::REGULAR &&
          exit_lane.turn() == hdmap::Lane::U_TURN) {
        reference_points = GetReferencePointsForRegularTurn(
            enter_lane_marking->line(), via_lane_marking->line());
      } else {
        // For 1-part uturn, we can't handle it well, so we just ignore it.
        break;
      }
      break;
    }
    default:
      break;
  }
  return reference_points;
}

std::optional<math::pb::Side> GetRelativeSideOfRelaxDirection(
    const pnc_map::Lane& relax_enter_lane,
    const pnc_map::Lane& via_lane_in_junction) {
  bool meet_relax_enter_lane = false;
  for (const pnc_map::Lane* lane : relax_enter_lane.section()->lanes()) {
    if (lane == nullptr) {
      continue;
    }
    if (lane->id() == relax_enter_lane.id()) {
      meet_relax_enter_lane = true;
      continue;
    }
    if (lane->successors().empty()) {
      continue;
    }
    for (const pnc_map::Lane* successor : lane->successors()) {
      // Meet the enter lane for |via_lane_in_junction|.
      if (successor != nullptr &&
          successor->id() == via_lane_in_junction.id()) {
        return meet_relax_enter_lane ? math::pb::Side::kLeft
                                     : math::pb::Side::kRight;
      }
    }
  }
  return std::nullopt;
}

std::optional<double> GetStartArclengthOnLaneWithHardBoundary(
    const pnc_map::Lane& lane, bool check_left_side) {
  const auto& lane_hard_boundary_infos = check_left_side
                                             ? lane.left_hard_boundary_info()
                                             : lane.right_hard_boundary_info();
  std::optional<double> start_arclength_with_hb = std::nullopt;
  for (const auto& hb_info : lane_hard_boundary_infos) {
    if (!IsValidRange(hb_info.range_on_center_line) ||
        hb_info.dist_to_lane_marking >=
            kHardBoundaryToSoftBoundaryDistanceThresholdInMeter) {
      continue;
    }
    const double start_pos_on_center_line =
        std::max(0.0, hb_info.range_on_center_line.start_pos);
    if (!start_arclength_with_hb.has_value() ||
        start_pos_on_center_line < start_arclength_with_hb.value()) {
      start_arclength_with_hb = start_pos_on_center_line;
    }
  }
  return start_arclength_with_hb;
}
}  // namespace lane_selection
}  // namespace planner
