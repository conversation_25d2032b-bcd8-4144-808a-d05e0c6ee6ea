#include "planner/path/reasoning/agent_intention/trajectory_reasoner/trajectory_reasoner.h"

#include <memory>

#include <google/protobuf/text_format.h>
#include <gtest/gtest.h>

#include "adv_geom/util.h"
#include "planner/behavior/directive_generator/lateral_clearance_corridor_directive_generator.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/agent_intention_state.h"
#include "planner/path/reasoning/agent_intention/drivable_lane_corridor_generator/drivable_lane_corridor_generator_utility.h"
#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/trajectory_reasoning_info_util.h"
#include "planner/path/reasoning/agent_intention/reasoner/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/semantic_context/test/test_utility.h"
#include "planner/path/reasoning/agent_intention/test_utils/path_reasoning_test_utils.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/speed/solver/searcher/simple_profile_searcher.h"
#include "planner/speed/test_util/test_util.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "planner_protos/agent_inlane_state.pb.h"
#include "planner_protos/agent_intention.pb.h"

namespace planner {

namespace {

constexpr int kNumPredictedPoses = 20;
constexpr int kTrajectoryId = 1;

lane_selection::LaneSequenceGeometry GetLaneSequenceGeomtry() {
  lane_selection::LaneSequenceGeometry lane_sequence_geometry;
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  lane_sequence_geometry.nominal_path =
      lane_selection::NominalPath(nominal_path_curve);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  lane_sequence_geometry.left_lane_boundary = left_boundary_curve;
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  lane_sequence_geometry.right_lane_boundary = right_boundary_curve;
  return lane_sequence_geometry;
}

void SetUpObjectOccupancyStates(
    const EgoInLaneParams& ego_params,
    std::vector<prediction::pb::Agent>& agent_proto_list_,
    std::vector<PlannerObject>& planner_object_list,
    std::vector<prediction::pb::PredictedTrajectory>&
        predicted_trajectory_protos,
    std::vector<std::vector<PredictedTrajectoryWrapper>>&
        predicted_trajectory_wrappers,
    ObjectOccupancyStateMap& object_occupancy_state_map) {
  // Lane structure.
  const auto lane_sequence_geometry = GetLaneSequenceGeomtry();
  prediction::pb::Agent static_behind_agent;
  auto& static_behind_object = *static_behind_agent.mutable_tracked_object();
  static_behind_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  static_behind_object.set_id(1);
  // The agent is behind the ego by 2 meters.
  static_behind_object.set_center_x(3.0);
  static_behind_object.set_center_y(-0.75);
  static_behind_object.set_length(4.0);
  static_behind_object.set_width(2);
  static_behind_object.set_height(1.2);
  static_behind_object.set_velocity(0.0);

  prediction::pb::Agent dynamic_behind_agent;
  auto& dynamic_behind_object = *dynamic_behind_agent.mutable_tracked_object();
  dynamic_behind_object.CopyFrom(static_behind_object);
  dynamic_behind_object.set_id(2);
  dynamic_behind_object.set_center_y(0.0);
  dynamic_behind_object.set_velocity(4.0);
  prediction::pb::Agent dynamic_fast_behind_agent;
  auto& dynamic_fast_behind_object =
      *dynamic_fast_behind_agent.mutable_tracked_object();
  dynamic_fast_behind_object.CopyFrom(static_behind_object);
  dynamic_fast_behind_object.set_id(3);
  dynamic_fast_behind_object.set_velocity(10.0);
  agent_proto_list_ = {static_behind_agent, dynamic_behind_agent,
                       dynamic_fast_behind_agent};
  planner_object_list.reserve(agent_proto_list_.size());
  predicted_trajectory_wrappers.reserve(agent_proto_list_.size());
  predicted_trajectory_protos.reserve(agent_proto_list_.size());
  for (size_t i = 0; i < /*num_of_agents=*/agent_proto_list_.size(); ++i) {
    PlannerObject planner_object(
        agent_proto_list_[i], /*object_timestamp=*/0,
        /*pose_at_plan_init_ts=*/
        TrafficParticipantPose(0.0, agent_proto_list_[i].tracked_object()));
    planner_object.set_is_primary_stationary(
        agent_proto_list_[i].tracked_object().velocity() == 0);
    planner_object_list.push_back(planner_object);
    std::vector<double> headings(kNumPredictedPoses, M_PI / 6);
    predicted_trajectory_protos.push_back(
        path::test::GeneratePredictedTrajectory(
            agent_proto_list_[i].tracked_object(), kTrajectoryId,
            /*initial_timestamp=*/0.0, headings));
    predicted_trajectory_wrappers.push_back({PredictedTrajectoryWrapper(
        agent_proto_list_[i].tracked_object(), predicted_trajectory_protos[i],
        /*is_primary_trajectory=*/true)});
    ObjectOccupancyState object_occupancy_state = GenerateObjectOccupancyState(
        planner_object_list[i], /*object_information=*/{},
        predicted_trajectory_wrappers[i], lane_sequence_geometry.nominal_path,
        lane_sequence_geometry.left_lane_boundary,
        lane_sequence_geometry.right_lane_boundary, ego_params,
        pb::MotionMode::FORWARD);
    object_occupancy_state_map.emplace(
        planner_object_list[i].id(),
        std::make_unique<ObjectOccupancyState>(object_occupancy_state));
  }
}

DrivableSpaceCorridor GenerateDrivableSpaceCorridor() {
  const auto lane_sequence_geometry = GetLaneSequenceGeomtry();
  DrivableSpaceCorridor corridor;
  corridor.driving_corridor.left_boundary =
      lane_sequence_geometry.left_lane_boundary;
  corridor.driving_corridor.right_boundary =
      lane_sequence_geometry.right_lane_boundary;
  const math::Range1d planning_horizon_range(
      0.0, lane_sequence_geometry.nominal_path.GetTotalArcLength());
  LateralClearanceCorridorDirectiveGenerator corridor_generator(
      kDrivableCorridorSamplingResolutionInMeter, kMaxLateralHorizonInMeter);
  corridor.driving_lateral_corridor = corridor_generator.GenerateCorridor(
      lane_sequence_geometry.nominal_path, planning_horizon_range,
      {corridor.driving_corridor.left_boundary},
      {corridor.driving_corridor.right_boundary});
  return corridor;
}

class TrajectoryReasonerTest : public ::testing::Test {
 protected:
  TrajectoryReasonerTest() : planner_objects_history_(20) {}

  void SetUp() override {
    // Set the agent.
    AgentInLaneMetadata meta_data;
    meta_data.features[pb::AgentFeature::IS_STATIONARY] = false;
    meta_data.agent_type = voy::perception::ObjectType::VEHICLE;

    AgentSnapshotBlockingInfo blocking_info;
    blocking_info.is_fully_behind_ego = false;

    const std::vector<math::geometry::Point2d> points = {
        {7.5, 0.5}, {12.5, 0.5}, {12.5, 1}, {7.5, 1}, {7.5, 0.5}};
    const math::geometry::PolygonWithCache2d polygon_bbox(std::move(points),
                                                          /*correct=*/true);
    const math::geometry::Point3d center(10.0, 0.75, 0);
    AgentSnapshotPose pose(center, /*length=*/5.0, /*width=*/0.5,
                           /*height=*/0.0, /*heading=*/0.0, polygon_bbox,
                           /*should_use_contour=*/false);
    // Agent is ahead of the ego.
    AgentSnapshotInLaneParam in_lane_param;
    in_lane_param.pose = pose;
    in_lane_param.cross_track_encroachment_m = 0.0;
    in_lane_param.start_arclength_m = 7.5;
    in_lane_param.end_arclength_m = 12.5;

    AgentSnapshotInLaneState tracked_state;
    tracked_state.inlane_param = in_lane_param;
    tracked_state.blocking_info = blocking_info;

    AgentTrajectoryInLaneStates trajectory_states;

    agent_.agent_metadata = meta_data;
    agent_.tracked_state = tracked_state;
    trajectory_states.trajectory_id = kTrajectoryId;
    agent_.predicted_trajectories.insert(
        {trajectory_states.trajectory_id, trajectory_states});
    agent_.primary_trajectory_id = kTrajectoryId;

    ego_param_.arclength_m = 10.0;
    ego_param_.rear_axle_to_rear_bumper_m = 1.0;
    ego_param_.rear_axle_to_front_bumper_m = 2.0;

    reference_line_ = math::geometry::PolylineCurve2d(
        {{0.0, 0.0}, {5.0, 0.0}, {10.0, 0.0}, {20.0, 0.0}, {100.0, 0.0}});

    const int num_step = 80;
    const speed::Limits limits(
        speed::LimitRange(/*brake_a.min=*/-2.0, /*brake_a.max=*/2.0),
        speed::LimitRange(/*accel_a.min=*/-2.0, /*accel_a.max=*/2.0),
        speed::LimitRange(/*brake_j.min=*/-3.0, /*brake_j.max=*/2.5),
        speed::LimitRange(/*accel_j.min=*/-2.0, /*accel_j.max=*/2.0),
        /*max_v=*/33.33, /*max_brake2accel_j=*/2.5);
    // Initialize lower and upper bound speed.
    lower_bound_speed_ = speed::CreateConstantSpeedProfileFromTime(
        /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, num_step, /*dt=*/0.1);
    upper_bound_speed_ = speed::CreateConstantAccelProfileFromTime(
        limits, /*t0=*/0.0, /*x0=*/0.0, /*v0=*/2.0, /*a0=*/1.0, num_step,
        /*dt=*/0.1);

    speed::GenerateBrakeProfile(/*brake_first_ix=*/0, num_step, limits,
                                /*dt=*/0.1, &lower_bound_speed_);
    emergency_brake_speed_ = lower_bound_speed_;

    google::protobuf::TextFormat::ParseFromString(
        R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
    )",
        agent_proto_.mutable_tracked_object());
    const int64_t timestamp = 0;
    const TrafficParticipantPose pose_at_plan_init_ts(
        timestamp, agent_proto_.tracked_object());
    planner_object_ = std::make_unique<PlannerObject>(agent_proto_, timestamp,
                                                      pose_at_plan_init_ts);

    ego_bounding_box_ = math::geometry::OrientedBox2d(
        /*x=*/10.0, /*y=*/0.0, /*length=*/5.0, /*width=*/0.5, /*heading=*/0.0);
    SetUpObjectOccupancyStates(
        ego_param_, agent_proto_list_, planner_object_list_,
        predicted_trajectory_protos_, predicted_trajectory_wrappers_,
        object_occupancy_state_map_);
    auto destruction_pool = std::make_unique<av_comm::ThreadPool>();
    auto planner_object_map_ptr =
        std::make_shared<std::unordered_map<ObjectId, PlannerObject>>();
    planner_object_map_ptr->emplace(planner_object_->id(), *planner_object_);
    planner_objects_history_.Update(planner_object_map_ptr, destruction_pool);

    planning_horizon_range_ = math::Range1d(0.0, 100.0);
    object_sl_boundary_ = AgentSLBoundary(in_lane_param);
    drivable_space_corridor_ = GenerateDrivableSpaceCorridor();
  }

  prediction::pb::Agent agent_proto_;
  std::unique_ptr<PlannerObject> planner_object_;
  PlannerObjectsHistory planner_objects_history_;
  std::vector<PredictedTrajectoryWrapper> predictions_;
  tbb::concurrent_unordered_map<int64_t,
                                std::vector<PredictedTrajectoryWrapper>>
      object_predictions_;
  AgentInLaneStates agent_;
  math::geometry::OrientedBox2d ego_bounding_box_;
  RequiredLateralGap required_lat_gap_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.4,
                         /*comfort_required_lateral_gap=*/0.8);
  RequiredLateralGap required_lat_gap_for_pass_state_ =
      RequiredLateralGap(/*critical_required_lateral_gap=*/0.2,
                         /*comfort_required_lateral_gap=*/0.4);
  EgoInLaneParams ego_param_;
  path::ScenarioIdentifierResult scenario_identify_result_;
  pb::TrafficRuleReasoningInfoDebug traffic_rule_reasoning_info_;
  speed::Profile emergency_brake_speed_;
  speed::Profile lower_bound_speed_;
  speed::Profile upper_bound_speed_;
  int agent_snapshot_size_ = 20;
  math::Range1d planning_horizon_range_;

  std::vector<prediction::pb::Agent> agent_proto_list_;
  std::vector<PlannerObject> planner_object_list_;
  std::vector<prediction::pb::PredictedTrajectory> predicted_trajectory_protos_;
  std::vector<std::vector<PredictedTrajectoryWrapper>>
      predicted_trajectory_wrappers_;
  ObjectOccupancyStateMap object_occupancy_state_map_;
  math::geometry::PolylineCurve2d reference_line_;
  AgentSLBoundary object_sl_boundary_;
  DrivableSpaceCorridor drivable_space_corridor_;
  std::optional<planner::pb::YieldIntentionData> updated_yield_data_ =
      std::nullopt;
  pb::ObstacleSTBufferConfig st_buffer_config_;
  pb::PathReasonerSeed last_path_reasoner_seed_;
  pb::PathReasonerSeedPerAgent path_reasoner_seed_per_agent_;
};

}  // namespace

// The object is behind the ego and is far away from the ego. For all these
// cases, no matter the blocking status, the reactions are all IGNORE.
TEST_F(TrajectoryReasonerTest, StaticBehindTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      path::ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      reference_line_,
      reference_line_,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  TrajectoryReasoner reasoner(ego_context_info,
                              /*generate_intent_for_dynamic=*/false);
  agent_.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = true;
  planner_object_->set_is_primary_stationary(true);
  agent_.tracked_state.inlane_param.start_arclength_m = -17;
  agent_.tracked_state.inlane_param.end_arclength_m = -15;
  // Case 1: Soft blocking object.
  LateralBlockingStateMetaData softblocking_object;
  softblocking_object.trajectory_id = kTrajectoryId;
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      softblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, true);
  // Test results from object occupancy states.
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[1], planner_objects_history_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[1], required_lat_gap_,
      required_lat_gap_for_pass_state_, softblocking_object,
      object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, true);

  // Case 2: Hard blocking object.
  LateralBlockingStateMetaData hardblocking_object;
  hardblocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  hardblocking_object.trajectory_id = kTrajectoryId;
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      hardblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, true);
  // Test results from object occupancy states.
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[1], planner_objects_history_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[1], required_lat_gap_,
      required_lat_gap_for_pass_state_, hardblocking_object,
      object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, true);

  // Case 3: Non-blocking objects.
  LateralBlockingStateMetaData nonblocking_object;
  nonblocking_object.global_blocking_state = pb::TrajectoryState::NON_BLOCKING;
  nonblocking_object.trajectory_id = kTrajectoryId;
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, true);
  // Test results from object occupancy states.
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[1], planner_objects_history_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[1], required_lat_gap_,
      required_lat_gap_for_pass_state_, nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, true);
}

// The agent is behind the ego and it is slower than the ego.
TEST_F(TrajectoryReasonerTest, DynamicBehindTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      path::ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      reference_line_,
      reference_line_,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  TrajectoryReasoner reasoner(ego_context_info,
                              /*generate_intent_for_dynamic=*/true);
  agent_.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = false;
  agent_.tracked_state.blocking_info.is_fully_behind_ego = true;
  agent_.tracked_state.inlane_param.start_arclength_m = -17;
  agent_.tracked_state.inlane_param.end_arclength_m = -15;
  // The ego is faster than the agent.
  ego_param_.speed_mps = 10.0;
  agent_.tracked_state.inlane_param.along_track_speed_mps = 9.0;
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 9.0
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);
  // Case 1: Soft blocking object.
  LateralBlockingStateMetaData softblocking_object;
  softblocking_object.global_blocking_state =
      pb::TrajectoryState::SOFT_BLOCKING;
  softblocking_object.trajectory_id = kTrajectoryId;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  softblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      softblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, false);
  // Test results from object occupancy states.
  softblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[2], planner_objects_history_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[2], required_lat_gap_,
      required_lat_gap_for_pass_state_, softblocking_object,
      object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, false);

  // Hard blocking object.
  LateralBlockingStateMetaData hardblocking_object;
  hardblocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  hardblocking_object.trajectory_id = kTrajectoryId;
  info.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
  hardblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      hardblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, false);
  // Test results from object occupancy states.
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[2], planner_objects_history_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[2], required_lat_gap_,
      required_lat_gap_for_pass_state_, hardblocking_object,
      object_sl_boundary_, /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, false);

  // Non-blocking object.
  LateralBlockingStateMetaData nonblocking_object;
  nonblocking_object.global_blocking_state = pb::TrajectoryState::NON_BLOCKING;
  nonblocking_object.trajectory_id = kTrajectoryId;
  info.set_blocking_state(pb::LateralBlockingState::NON_BLOCKING);
  nonblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, false);
  // Test results from object occupancy states.
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[2], planner_objects_history_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[2], required_lat_gap_,
      required_lat_gap_for_pass_state_, nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, false);
}

// The agent is behind the ego, mostly in lane and it is faster than the ego.
TEST_F(TrajectoryReasonerTest, DynamicFastBehindTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      path::ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      reference_line_,
      reference_line_,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  TrajectoryReasoner reasoner(ego_context_info,
                              /*generate_intent_for_dynamic=*/true);
  agent_.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = false;
  agent_.tracked_state.blocking_info.is_fully_behind_ego = true;
  agent_.tracked_state.inlane_param.start_arclength_m = -2;
  agent_.tracked_state.inlane_param.end_arclength_m = 0;
  agent_.tracked_state.inlane_param.cross_track_encroachment_m = 0.5;
  // The ego is slower than the agent.
  ego_param_.speed_mps = 3.0;
  agent_.tracked_state.inlane_param.along_track_speed_mps = 9.0;
  agent_.agent_metadata.prediction_agent_rank = 1;
  prediction::pb::Agent agent_proto;
  google::protobuf::TextFormat::ParseFromString(
      R"(
        object_type: VEHICLE
        center_x: 10.0
        center_y: 0.75
        center_z: 0.0
        width: 0.5
        length: 10.0
        height: 0.0
        velocity: 9.0
    )",
      agent_proto.mutable_tracked_object());
  const int64_t timestamp = 0;
  const TrafficParticipantPose pose_at_plan_init_ts(
      timestamp, agent_proto.tracked_object());
  auto planner_object = std::make_unique<PlannerObject>(agent_proto, timestamp,
                                                        pose_at_plan_init_ts);

  // Soft blocking object.
  LateralBlockingStateMetaData softblocking_object;
  softblocking_object.global_blocking_state =
      pb::TrajectoryState::SOFT_BLOCKING;
  softblocking_object.trajectory_id = kTrajectoryId;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::SOFT_BLOCKING);
  softblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(20, info);
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      softblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, false);
  // Test results from object occupancy states.
  softblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[3], planner_objects_history_,
      required_lat_gap_, softblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[3], required_lat_gap_,
      required_lat_gap_for_pass_state_, softblocking_object,
      object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(softblocking_result.is_static, false);

  // Hard blocking object.
  LateralBlockingStateMetaData hardblocking_object;
  hardblocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  hardblocking_object.trajectory_id = kTrajectoryId;
  info.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
  hardblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      hardblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, false);
  // Test results from object occupancy states.
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[3], planner_objects_history_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[3], required_lat_gap_,
      required_lat_gap_for_pass_state_, hardblocking_object,
      object_sl_boundary_, /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(hardblocking_result.is_static, false);

  // Non-blocking object.
  LateralBlockingStateMetaData nonblocking_object;
  nonblocking_object.global_blocking_state = pb::TrajectoryState::NON_BLOCKING;
  nonblocking_object.trajectory_id = kTrajectoryId;
  info.set_blocking_state(pb::LateralBlockingState::NON_BLOCKING);
  nonblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(agent_snapshot_size_, info);
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object, planner_objects_history_, object_predictions_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, false);
  // Test results from object occupancy states.
  nonblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      *object_occupancy_state_map_[3], planner_objects_history_,
      required_lat_gap_, nonblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      *object_occupancy_state_map_[3], required_lat_gap_,
      required_lat_gap_for_pass_state_, nonblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::IGNORE);
  EXPECT_EQ(nonblocking_result.is_static, false);
}

// The agent is a ped that has a cutin prediction.
TEST_F(TrajectoryReasonerTest, DISABLED_CutinPedTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      path::ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      reference_line_,
      reference_line_,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  TrajectoryReasoner reasoner(ego_context_info,
                              /*generate_intent_for_dynamic=*/true);
  agent_.agent_metadata.features[pb::AgentFeature::IS_STATIONARY] = false;
  // is_beside_ego
  agent_.tracked_state.inlane_param.start_arclength_m = 10;
  agent_.tracked_state.inlane_param.end_arclength_m = 11;
  agent_.tracked_state.inlane_param.cross_track_encroachment_m = 0.5;
  // The ego is slower than the agent.
  ego_param_.speed_mps = 3.0;
  agent_.tracked_state.inlane_param.along_track_speed_mps = 0;
  agent_.agent_metadata.agent_type = voy::perception::ObjectType::PED;
  agent_.predicted_trajectories[0].trajectory_metadata.intention_type =
      pb::AgentTrajectoryIntentionType::CUT_IN;

  LateralBlockingStateMetaData hardblocking_object;
  hardblocking_object.tracked_blocking_info.set_blocking_state(
      pb::LateralBlockingState::SOFT_BLOCKING);
  hardblocking_object.trajectory_id = kTrajectoryId;
  hardblocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  pb::LateralBlockingInfo info;
  info.set_is_beyond_intention_range(false);
  info.set_blocking_state(pb::LateralBlockingState::HARD_BLOCKING);
  hardblocking_object.trajectory_blocking_infos =
      std::vector<pb::LateralBlockingInfo>(20, info);
  hardblocking_object.reasoning_info = GenerateAgentReasoningInfo(
      agent_, *planner_object_, planner_objects_history_, object_predictions_,
      required_lat_gap_, hardblocking_object, /*ls_tl_infos=*/{},
      emergency_brake_speed_, lower_bound_speed_, upper_bound_speed_,
      ego_param_, ego_bounding_box_, planning_horizon_range_,
      object_sl_boundary_,
      /*was_nudge_intention=*/false, /*is_xlane_nudge_object=*/false,
      /*should_trigger_unstuck_for_unknown_objects=*/false, reference_line_,
      /*pull_over_destination_meta_ptr=*/nullptr,
      /*lane_change_execution_info=*/std::nullopt, /*lane_change_info=*/{},
      /*lane_marking_traffic_rule=*/nullptr,
      planner::lateral_clearance::LateralClearanceData{},
      /*current_lane=*/test::GenerateLane(GetLaneSequenceGeomtry()),
      /*is_ego_near_merge=*/false,
      /*is_pull_over_gap_align_trailing_agent=*/false,
      /*behavior_type=*/pb::BehaviorType::LANE_KEEP);
  auto hard_blocking_result = reasoner.GetTrajectoryIntentionMetaData(
      agent_, required_lat_gap_, required_lat_gap_for_pass_state_,
      hardblocking_object, object_sl_boundary_,
      /*lane_change_execution_info=*/std::nullopt,
      /*should_ignore_by_upstream=*/false, updated_yield_data_,
      path_reasoner_seed_per_agent_);
  EXPECT_EQ(hard_blocking_result.intention_type, pb::TrajectoryState::MIXED);
  EXPECT_EQ(hard_blocking_result.is_static, true);
}

// The decision for construction zone is based on LateralBlockingStateMetaData.
TEST_F(TrajectoryReasonerTest, ConstructionZoneTest) {
  path::EgoContextInfo ego_context_info{
      ego_param_,
      scenario_identify_result_,
      traffic_rule_reasoning_info_,
      LaneChangeInfo{},
      drivable_space_corridor_,
      /*lane_keep_behavior_type=*/pb::LK_NA,
      /*behavior_type=*/pb::LANE_KEEP,
      /*nudge_motion_checker=*/nullptr,
      path::ActiveLatGapAdjustMetaData{},
      lateral_clearance::LateralClearanceData{},
      planner::pb::PullOverJumpInType::INVALID_TYPE,
      reference_line_,
      reference_line_,
      path::test::GetSpeedLowerBound(),
      path::test::GetSpeedUpperBound(),
      /*reusable_last_speed_profile=*/std::nullopt,
      st_buffer_config_,
      last_path_reasoner_seed_,
      /*pullover_status_meta=*/std::nullopt};
  TrajectoryReasoner reasoner(ego_context_info,
                              /*generate_intent_for_dynamic=*/false);
  ConstructionZoneInLaneState cz_state;
  cz_state.blocking_state = pb::ConstructionZoneBlockingState::SOFT_BLOCKING;
  cz_state.construction_zone_contour = math::geometry::PolygonWithCache2d(
      {{7.5, 0.5}, {12.5, 0.5}, {12.5, 1}, {7.5, 1}, {7.5, 0.5}},
      /*correct=*/true);
  AgentSLBoundary cz_sl_boundary(/*start_s_in=*/7.5, /*end_s_in=*/12.5,
                                 /*start_l_in=*/0.5, /*end_l_in=*/1.0);

  LateralBlockingStateMetaData softblocking_object;
  softblocking_object.global_blocking_state =
      pb::TrajectoryState::SOFT_BLOCKING;
  auto softblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      cz_state, required_lat_gap_, required_lat_gap_for_pass_state_,
      softblocking_object, cz_sl_boundary, path_reasoner_seed_per_agent_);
  EXPECT_EQ(softblocking_result.intention_type, pb::TrajectoryState::MIXED);
  EXPECT_EQ(softblocking_result.is_static, true);

  cz_state.blocking_state = pb::ConstructionZoneBlockingState::HARD_BLOCKING;
  LateralBlockingStateMetaData hardblocking_object;
  hardblocking_object.global_blocking_state =
      pb::TrajectoryState::HARD_BLOCKING;
  auto hardblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      cz_state, required_lat_gap_, required_lat_gap_for_pass_state_,
      hardblocking_object, cz_sl_boundary, path_reasoner_seed_per_agent_);
  EXPECT_EQ(hardblocking_result.intention_type, pb::TrajectoryState::MIXED);
  EXPECT_EQ(hardblocking_result.is_static, true);

  cz_state.blocking_state = pb::ConstructionZoneBlockingState::NOT_BLOCKING;
  LateralBlockingStateMetaData nonblocking_object;
  nonblocking_object.global_blocking_state = pb::TrajectoryState::NON_BLOCKING;
  auto nonblocking_result = reasoner.GetTrajectoryIntentionMetaData(
      cz_state, required_lat_gap_, required_lat_gap_for_pass_state_,
      nonblocking_object, cz_sl_boundary, path_reasoner_seed_per_agent_);
  EXPECT_EQ(nonblocking_result.intention_type, pb::TrajectoryState::MIXED);
  EXPECT_EQ(nonblocking_result.is_static, true);
}

}  // namespace planner
