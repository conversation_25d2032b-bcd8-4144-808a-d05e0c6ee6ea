// Unit test to verify the analytically calculated gradients.

#include "trajectory_test_fixture.h"

#include <unordered_set>

#include "numerical_differentiation/numerical_differentiation.h"
#include "numerical_differentiation/numerical_differentiation_options.h"

namespace planner {

namespace {

constexpr double kErrorTolerance = 1e-6;
constexpr int X_DIM = TrajectorySolver::X_DIM;
constexpr int U_DIM = TrajectorySolver::U_DIM;
constexpr int DIM = X_DIM + U_DIM;

}  // namespace

using Hamiltonian = TrajectorySolver::Hamiltonian;
using Feature = control_util::CostingUtil<X_DIM, U_DIM>::Feature;
using CostJacobian = control_util::CostingUtil<X_DIM, U_DIM>::CostJacobian;
using PbCostingTerm = planner::pb::TrajectoryCostingTerm;
using PbFeatureType = TrajectoryFormula::PbFeatureType;

// Stores the inputs and corresponding function outputs
// during numerical differentiation iterations. Useful for
// debugging.
using InputOutputPair = std::pair<math::EVector<DIM>, double>;

// Stores the gradients and the corresponding input-output pairs
// while calculating the gradients.
struct NumericalGradientsInfo {
  std::optional<CostJacobian> jacobian = std::nullopt;
  std::vector<InputOutputPair> iteration_io_pair;
};

// Stores all the information to verify and debug gradients.
struct GradientVerificationAndDebugInfo {
  Feature feature;
  CostJacobian cost_feature_jacobian;
  Hamiltonian hamiltonian = Hamiltonian::Zero();
  PbFeatureType feature_type;

  std::vector<NumericalGradientsInfo> feature_numerical_gradients_forward;
  NumericalGradientsInfo cost_feature_numerical_gradients_forward;

  std::vector<NumericalGradientsInfo> feature_numerical_gradients_central;
  NumericalGradientsInfo cost_feature_numerical_gradients_central;

  std::vector<NumericalGradientsInfo> feature_numerical_gradients_ridders;
  NumericalGradientsInfo cost_feature_numerical_gradients_ridders;
};

class GradientTestFixture : public TrajectoryDdpSolverTestFixture {
 public:
  GradientTestFixture() : TrajectoryDdpSolverTestFixture() {}

  GradientVerificationAndDebugInfo ComputeGradients(
      int step, int total_step_size, double delta, const State& x,
      const Control& u, const TrajectoryFormula::Primitives& primitives,
      const PbCostingTerm& costing_term) {
    GradientVerificationAndDebugInfo gradients_debug_info;
    bool is_terminal = (step == total_step_size);
    gradients_debug_info.feature_type = costing_term.feature_type();
    const auto stepwise_primitives =
        formula_->GetStepwisePrimitives(step, is_terminal, x, u, primitives);
    // Calculate the analytical gradient for feature and cost feature.
    control_util::GetFeature<control_util::DdpProblemType::kTrajectory>(
        step, total_step_size, costing_term, feature_param_, x, u,
        stepwise_primitives, primitives, &gradients_debug_info.feature);
    control_util::CostingUtil<X_DIM, U_DIM>::CostFeature(
        costing_term.costing_spec(), delta, &gradients_debug_info.hamiltonian,
        &gradients_debug_info.feature);
    gradients_debug_info.cost_feature_jacobian =
        gradients_debug_info.hamiltonian.template bottomLeftCorner<DIM, 1>();

    // Calculate the numerical gradients for feature and cost feature.
    ComputeGradientsInternal<math::NumericalDifferentiationMode::kForward>(
        step, total_step_size, delta, x, u, primitives, costing_term,
        &gradients_debug_info.feature,
        gradients_debug_info.feature_numerical_gradients_forward,
        gradients_debug_info.cost_feature_numerical_gradients_forward);

    ComputeGradientsInternal<math::NumericalDifferentiationMode::kCentral>(
        step, total_step_size, delta, x, u, primitives, costing_term,
        &gradients_debug_info.feature,
        gradients_debug_info.feature_numerical_gradients_central,
        gradients_debug_info.cost_feature_numerical_gradients_central);

    ComputeGradientsInternal<math::NumericalDifferentiationMode::kRidders>(
        step, total_step_size, delta, x, u, primitives, costing_term,
        &gradients_debug_info.feature,
        gradients_debug_info.feature_numerical_gradients_ridders,
        gradients_debug_info.cost_feature_numerical_gradients_ridders);
    return gradients_debug_info;
  }

 private:
  template <math::NumericalDifferentiationMode mode>
  void ComputeGradientsInternal(
      int step, int total_step_size, double delta, const State& x,
      const Control& u, const TrajectoryFormula::Primitives& primitives,
      const PbCostingTerm& costing_term, Feature* feature,
      std::vector<NumericalGradientsInfo>& feature_gradients,  // NOLINT
      NumericalGradientsInfo& cost_feature_gradients) {        // NOLINT
    NumericalGradientsInfo gradients;
    feature_gradients.resize(feature->components.size());
    const math::EVector<DIM> x_and_u =
        (math::EVector<DIM>() << x, u).finished();
    math::NumericalDifferentiationOptions options;
    bool is_terminal = (step == total_step_size);
    for (size_t component_idx = 0; component_idx < feature->components.size();
         ++component_idx) {
      // Lambda that takes in the input as state and control vector
      // and calculates the feature value.
      auto feature_function =
          [&](const math::EVector<DIM>& input) -> std::optional<double> {
        Feature test_feature;
        const auto current_stepwise_primitives =
            formula_->GetStepwisePrimitives(
                step, is_terminal, input.template head<StateIndex::X_DIM>(),
                input.template tail<ControlIndex::U_DIM>(), primitives);
        control_util::GetFeature<control_util::DdpProblemType::kTrajectory>(
            step, total_step_size, costing_term, feature_param_,
            input.template head<StateIndex::X_DIM>(),
            input.template tail<ControlIndex::U_DIM>(),
            current_stepwise_primitives, primitives, &test_feature);
        if (test_feature.components.size() != feature->components.size()) {
          return {};
        }
        // Store the input-output pairs
        feature_gradients[component_idx].iteration_io_pair.push_back(
            {input, test_feature.components[component_idx].val});
        return test_feature.components[component_idx].val;
      };
      feature_gradients[component_idx].jacobian =
          math::ComputeNumericalDiffGradient<mode, DIM>(feature_function,
                                                        x_and_u, options);
    }

    // Lambda that takes in the input as state and control vector
    // and calculates the cost feature value.
    auto cost_feature_function =
        [&](const math::EVector<DIM>& input) -> std::optional<double> {
      Feature test_feature;
      const auto current_stepwise_primitives = formula_->GetStepwisePrimitives(
          step, is_terminal, input.template head<StateIndex::X_DIM>(),
          input.template tail<ControlIndex::U_DIM>(), primitives);
      control_util::GetFeature<control_util::DdpProblemType::kTrajectory>(
          step, total_step_size, costing_term, feature_param_,
          input.template head<StateIndex::X_DIM>(),
          input.template tail<ControlIndex::U_DIM>(),
          current_stepwise_primitives, primitives, &test_feature);
      control_util::CostingUtil<X_DIM, U_DIM>::CostFeature(
          costing_term.costing_spec(), delta, nullptr, &test_feature);
      // stor the input-output pair
      cost_feature_gradients.iteration_io_pair.push_back(
          {input, test_feature.cost});
      return test_feature.cost;
    };
    cost_feature_gradients.jacobian =
        math::ComputeNumericalDiffGradient<mode, DIM>(cost_feature_function,
                                                      x_and_u, options);
  }
};

// Checks the analytical gradients with the numerical gradients
// computed using ridders' method.
void IsGradientEqualUsingRidders(
    const std::vector<GradientVerificationAndDebugInfo>& gradients_debug_info,
    const std::unordered_set<PbFeatureType>& features_to_ignore) {
  for (size_t i = 0; i < gradients_debug_info.size(); ++i) {
    // Ignore the features that have if conditions.
    if (features_to_ignore.find(gradients_debug_info[i].feature_type) !=
        features_to_ignore.end()) {
      continue;
    }
    const auto& current_numerical_feature_gradient =
        gradients_debug_info[i].feature_numerical_gradients_ridders;
    const auto& current_feature = gradients_debug_info[i].feature;
    const auto& current_cost_feature_jacobian =
        gradients_debug_info[i].cost_feature_jacobian;
    const auto& current_numerical_cost_feature_gradient =
        gradients_debug_info[i].cost_feature_numerical_gradients_ridders;
    for (size_t j = 0; j < current_numerical_feature_gradient.size(); ++j) {
      EXPECT_TRUE(current_numerical_feature_gradient[j].jacobian)
          << "Empty at Feature: "
          << static_cast<int>(gradients_debug_info[i].feature_type)
          << ", FeatureLet: " << j;
      for (int k = 0; k < DIM; ++k) {
        EXPECT_NEAR(current_numerical_feature_gradient[j].jacobian.value()(k),
                    current_feature.components[j].jacob(k), kErrorTolerance)
            << "Discrepancy at "
            << "Feature: "
            << static_cast<int>(gradients_debug_info[i].feature_type)
            << ", FeatureLet:" << j << ", Dimension:" << k << std::endl;
      }
    }
    EXPECT_TRUE(current_numerical_cost_feature_gradient.jacobian)
        << "Cost Feature Jacobian empty at Feature: "
        << static_cast<int>(gradients_debug_info[i].feature_type) << std::endl;
    for (int k = 0; k < DIM; ++k) {
      EXPECT_NEAR(current_numerical_cost_feature_gradient.jacobian.value()(k),
                  current_cost_feature_jacobian(k), kErrorTolerance)
          << "Discrepancy in cost feature at "
          << "Feature: "
          << static_cast<int>(gradients_debug_info[i].feature_type)
          << ", Dimension:" << k << std::endl;
    }
  }
}

TEST_F(GradientTestFixture, NominalPathGradientVerification) {
  const auto primitives = GetNominalMockPrimitives();
  // Set sequence specification.
  SequenceSpecification sequence_spec;
  sequence_spec.x_init = State::Zero();
  sequence_spec.x_init[StateIndex::X_POS] = 2.0;
  sequence_spec.x_init[StateIndex::Y_POS] = 0.0;
  sequence_spec.x_init[StateIndex::SPEED] = 5.0;
  sequence_spec.x_init[StateIndex::THETA] = math::Degree2Radian(90);

  sequence_spec.state_num = state_num_;
  sequence_spec.control_num = control_num_;
  sequence_spec.delta = dt_s_;

  // Set initial control sequence to be all zeros.
  ControlSequence us_init(control_num_, Control::Zero());
  StateSequence x_init =
      motion_model_with_shape_->motion_model().EvaluateControlSequence(
          sequence_spec, us_init);

  std::vector<GradientVerificationAndDebugInfo> gradients_debug_info;

  const int step = 2;
  const int total_step_size = 5;

  for (const auto& costing_term : formula_->costing_terms()) {
    gradients_debug_info.push_back(ComputeGradients(
        step, total_step_size, sequence_spec.delta, x_init[step], us_init[step],
        primitives, costing_term));
  }

  // TODO(nihar): Remove this ignore list once features with if conditions
  // are modified.
  std::unordered_set<PbFeatureType> features_with_if_conditionals(
      {PbFeatureType::EFFORT_ACCELERATION, PbFeatureType::EFFORT_DECELERATION,
       PbFeatureType::EFFORT_POSITIVE_JERK,
       PbFeatureType::EFFORT_NEGATIVE_JERK});
  IsGradientEqualUsingRidders(gradients_debug_info,
                              features_with_if_conditionals);
}

TEST_F(GradientTestFixture, NominalPathTrackingWithStopLine) {
  const auto primitives = GetStopLineMockPrimitives();
  // Set sequence specification.
  SequenceSpecification sequence_spec;
  sequence_spec.x_init = State::Zero();
  sequence_spec.x_init[StateIndex::X_POS] = 2.0;
  sequence_spec.x_init[StateIndex::Y_POS] = 0.0;
  sequence_spec.x_init[StateIndex::SPEED] = 5.0;
  sequence_spec.x_init[StateIndex::THETA] = math::Degree2Radian(90);

  sequence_spec.state_num = state_num_;
  sequence_spec.control_num = control_num_;
  sequence_spec.delta = dt_s_;

  // Set initial control sequence to be all zeros.
  ControlSequence us_init(control_num_, Control::Zero());
  StateSequence x_init =
      motion_model_with_shape_->motion_model().EvaluateControlSequence(
          sequence_spec, us_init);

  std::vector<GradientVerificationAndDebugInfo> gradients_debug_info;

  const int step = 2;
  const int total_step_size = 5;

  for (const auto& costing_term : formula_->costing_terms()) {
    gradients_debug_info.push_back(ComputeGradients(
        step, total_step_size, sequence_spec.delta, x_init[step], us_init[step],
        primitives, costing_term));
  }

  // TODO(nihar): Remove this ignore list once features with if conditions
  // are modified.
  std::unordered_set<PbFeatureType> features_with_if_conditionals(
      {PbFeatureType::EFFORT_ACCELERATION, PbFeatureType::EFFORT_DECELERATION,
       PbFeatureType::EFFORT_POSITIVE_JERK,
       PbFeatureType::EFFORT_NEGATIVE_JERK});
  IsGradientEqualUsingRidders(gradients_debug_info,
                              features_with_if_conditionals);
}

TEST_F(GradientTestFixture, NominalPathTrackingWithACC) {
  const auto primitives = GetACCMockPrimitives();
  // Set sequence specification.
  SequenceSpecification sequence_spec;
  sequence_spec.x_init = State::Zero();
  sequence_spec.x_init[StateIndex::X_POS] = 2.0;
  sequence_spec.x_init[StateIndex::Y_POS] = 0.0;
  sequence_spec.x_init[StateIndex::SPEED] = 10.0;
  sequence_spec.x_init[StateIndex::THETA] = math::Degree2Radian(90);

  sequence_spec.state_num = state_num_;
  sequence_spec.control_num = control_num_;
  sequence_spec.delta = dt_s_;

  // Set initial control sequence to be all zeros.
  ControlSequence us_init(control_num_, Control::Zero());
  StateSequence x_init =
      motion_model_with_shape_->motion_model().EvaluateControlSequence(
          sequence_spec, us_init);

  std::vector<GradientVerificationAndDebugInfo> gradients_debug_info;

  const int step = 2;
  const int total_step_size = 5;

  for (const auto& costing_term : formula_->costing_terms()) {
    gradients_debug_info.push_back(ComputeGradients(
        step, total_step_size, sequence_spec.delta, x_init[step], us_init[step],
        primitives, costing_term));
  }

  // TODO(nihar): Remove this ignore list once features with if conditions
  // are modified.
  std::unordered_set<PbFeatureType> features_with_if_conditionals(
      {PbFeatureType::EFFORT_ACCELERATION, PbFeatureType::EFFORT_DECELERATION,
       PbFeatureType::EFFORT_POSITIVE_JERK, PbFeatureType::EFFORT_NEGATIVE_JERK,
       PbFeatureType::PRIMITIVE_SOFT_SPEED_ZONE});
  IsGradientEqualUsingRidders(gradients_debug_info,
                              features_with_if_conditionals);
}

}  // namespace planner
