#include "planner/path/reasoning/agent_intention/lateral_blocking_state_generator/trajectory_reasoning_info_util.h"

#include <algorithm>
#include <cstdint>
#include <limits>
#include <memory>
#include <optional>
#include <set>
#include <string>
#include <unordered_map>
#include <utility>

#include "geometry/algorithms/arithmetic.h"
#include "geometry/algorithms/length.h"
#include "hdmap_protos/lane.pb.h"
#include "log_utils/log_macros.h"
#include "log_utils/map_macros.h"
#include "math/constants.h"
#include "math/curve2d.h"
#include "math/math_util.h"
#include "math/piecewise_linear_function.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/behavior/util/agent_state/agent_in_lane_state.h"
#include "planner/behavior/util/construction_zone/construction_zone_inlane_state.h"
#include "planner/behavior/util/traffic_rules/traffic_light_info.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/agent_grouping_utility.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_param.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state.h"
#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_stamped_snapshot_info.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "planner/path/reasoning/agent_intention/semantic_context/frame_analyzer/frame_analyzer_util.h"
#include "planner/speed/agent_reaction/reaction_utils.h"
#include "planner/speed/profile/profile.h"
#include "planner/speed/profile/profile_util.h"
#include "planner/utility/physics/motion_model_2d.h"
#include "planner/world_model/planner_object/planner_object.h"
#include "planner/world_model/planner_object/planner_objects_history.h"
#include "planner/world_model/traffic_participant/traffic_participant_pose.h"
#include "rt_event/rt_event.h"
#include "voy_protos/trajectory.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace {

// The relative angle threshold for a along agent determination(45 degree).
constexpr double kMaxRelativeAlongHeadingInRad = M_PI_4;
// The relative angle threshold for a oncoming agent determination(135 degree).
constexpr double kMaxRelativeOnComingHeadingInRad = M_PI_4 * 3;
// The max effective spatial range in meter for the st_region of dynamic agent.
constexpr double kMaxValidSpatialRangeInMeter = 30.0;
// The min absolute cross speed threshold for a crossing agent determination.
constexpr double kMinAbsoluteCrossSpeedForEncroachingSnapshotInMps = 0.3;
// The relative angle threshold for a crossing agent determination(20 degree).
constexpr double kMaxRelativeCrossingHeadingInRad = M_PI / 9.0;
// The heading diff threshold for a reversing agent (180 degree).
constexpr double kReversingAgentHeadingDiffThresholdInRad = M_PI * 8 / 9;
// The minimum heading diff threshold for a potentially reversing agent (60
// degrees).
constexpr double kMinReversingAgentCautiousHeadingDiffThresholdInRad =
    M_PI / 3.0;
// The maximum heading diff threshold for a potentially reversing agent (120
// degrees).
constexpr double kMaxReversingAgentCautiousHeadingDiffThresholdInRad =
    2.0 * M_PI / 3.0;
// The minimum distance to ignore static agents behind the ego.
constexpr double kStaticAgentIgnoreDistanceInMeter = 7.5;
// The minimum ttc to ignore agents behind the ego in lane follow.
constexpr double kBehindAgentIgnoreTtcForLaneFollowInSec = 3.0;
// In general, we ignore oncoming agents if they don't have longitudinal
// overlap with Ego within this time horizon.
constexpr double KOncomingAgentMaxConsiderationHorizonInSec = 5.0;
// The ignorable minimum lateral distance between agent and ego.
constexpr double kBesideAgentIgnoreLateralDistanceInMeter = 5.0;
// The longitudinal extend buffer for agent should be considered for
// intention generation when it is close to extreme braking with lower bound
// speed profile.
constexpr double kLongitudinalExtendBufferInMeter = 1.5;
// The longest distance to take unknown objects into nudge considerations.
constexpr double kFarthestUnknownObjectsForUnstuckInM = 20.0;
// The relative heading threshold for treating it as along nominal path;
constexpr double kAlongNominalPathHeadingDiffThreshold = M_PI / 2.0 / 9.0;
// The ratio threshold to identify historical reversing agent.
constexpr double kRatioThresholdToConsiderReversingInHistory = 0.2;
// The max duration in history we want to considered to get some recent
// kinematic infos.
constexpr double kMaxHistoryDurationConsiderationInSec = 0.5;  // in s.
// The default longitudinal length of traffic light waiting range before the
// traffic light. The non-blockage vehicle or cyclist within this range would
// be considered as waiting for traffic light.
constexpr double kDefaultLongitudinalTLWaitingDistInMeter = 30.0;
// When an agent increases its speed from smaller than the threshold (for some
// frames agent can see Ego) to larger than the threshold or switches its motion
// state from reversing to forward and has a speed larger than the threshold, we
// think the agent has lost its patience.
constexpr double kLosePatienceAgentSpeedThreshold = 0.1;  // in m/s.
// The minimum proportion of the frames where agent can see the Ego to assign it
// with a yield intention.
constexpr double kMinAgentSeeEgoRatioToShowYieldIntention = 0.3;
// The speed threshold below which will we treat an agent as stationary.
constexpr double kStationarySpeedThresholdMps = 0.15;
// The minimum heading misalignment between agent and the corresponding
// reference line direction to show a toward reference line intention.
constexpr double kMinTowardReferenceLineAngle = math::Degree2Radian(5.0);
// The maximum required time duration to start up when the traffic light turns
// to green.
constexpr int64_t kMaxStartUpRequiredTimeInMSec = 5000;  // 5s
// The max duration in history we want to consider to get historical average
// velocity of the agent.
constexpr int64_t kDefaultHistoricalDurationForAvgVelocityInMSec = 3000;
// When agent tracked pose TTC is smaller than this, we will ignore other
// ES checks and trigger ES.
constexpr double kMaxTtcToTriggerEsInSeconds = 0.5;

// Returns the longitudinal arclength range for the agent's interested part of
// its predicted trajectory.
math::Range1d GetAgentArclengthRangeWithinTimeHorizon(
    const AgentInLaneStates& agent_state, int predicted_trajectory_id,
    int64_t max_considered_timestamp, bool is_stationary) {
  math::Range1d agent_arclength_range(
      agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
      agent_state.tracked_state.inlane_param.full_body_end_arclength_m);
  if (is_stationary || agent_state.predicted_trajectories.empty()) {
    return agent_arclength_range;
  }
  // Update agent arc length range with its prediction trajectory.
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  for (const auto& predicted_state : predicted_states) {
    // Only consider part of prediction.
    if (predicted_state.timestamp > max_considered_timestamp) {
      break;
    }
    math::UpdateMin(
        predicted_state.inlane_state.inlane_param.full_body_start_arclength_m,
        agent_arclength_range.start_pos);
    math::UpdateMax(
        predicted_state.inlane_state.inlane_param.full_body_end_arclength_m,
        agent_arclength_range.end_pos);
  }
  return agent_arclength_range;
}

math::Range1d GetAgentArclengthRangeWithinTimeHorizon(
    const ObjectOccupancyState& object_occupancy_state,
    int predicted_trajectory_id, int64_t max_considered_timestamp,
    bool is_stationary) {
  math::Range1d agent_arclength_range(
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_start_arclength_m,
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_end_arclength_m);
  if (is_stationary ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return agent_arclength_range;
  }
  // Update agent arc length range with its prediction trajectory.
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  for (const auto& predicted_state : predicted_states) {
    // Only consider part of prediction.
    if (predicted_state.timestamp() > max_considered_timestamp) {
      break;
    }
    math::UpdateMin(
        predicted_state.object_occupancy_param().full_body_start_arclength_m,
        agent_arclength_range.start_pos);
    math::UpdateMax(
        predicted_state.object_occupancy_param().full_body_end_arclength_m,
        agent_arclength_range.end_pos);
  }
  return agent_arclength_range;
}

// Returns the buffered longitudinal arclength range for ego.
math::Range1d GetEgoBufferedLongitudinalArclengthRange(
    const EgoInLaneParams& ego_param,
    const speed::Profile& lower_bound_speed_profile) {
  const double ego_start_s =
      ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m;
  const double ego_end_s =
      ego_param.arclength_m + ego_param.rear_axle_to_front_bumper_m;

  const double rear_buffer = ego_param.rear_axle_to_rear_bumper_m;
  double front_buffer = ego_param.rear_axle_to_rear_bumper_m +
                        ego_param.rear_axle_to_front_bumper_m;
  if (!lower_bound_speed_profile.empty()) {
    math::UpdateMax(lower_bound_speed_profile.back().x - ego_param.arclength_m +
                        kLongitudinalExtendBufferInMeter,
                    front_buffer);
  }
  return math::Range1d(ego_start_s - rear_buffer, ego_end_s + front_buffer);
}

// Return true if the agent is waiting for the traffic light.
bool IsAgentWaitingForTrafficLight(
    const PlannerObject& planner_object,
    const std::vector<lane_selection::TrafficLightInfoReference>& ls_tl_infos,
    const math::Range1d& longitudinal_range) {
  // Respect the blockage signal and not consider them as TL-waiting.
  if (planner_object.is_blockage_object() || !planner_object.is_stationary()) {
    return false;
  }
  // Only consider vehicles and cyclists.
  if (!(planner_object.is_vehicle() || planner_object.is_cyclist())) {
    return false;
  }
  // Skip invalid objects on the lane sequence.
  if (!math::IsValidRange(longitudinal_range)) {
    return false;
  }

  // Return true if the agent is in the traffic light waiting area, and its
  // brake signal is on if it's a vehicle. Otherwise, return false.
  for (const auto& tl_info_ref : ls_tl_infos) {
    const lane_selection::TrafficLightInfo& tl_info = tl_info_ref.get();
    const bool has_turn_to_green_for_long_time =
        tl_info.color() == voy::TrafficLight::GREEN &&
        tl_info.GetColorDuration() > kMaxStartUpRequiredTimeInMSec;
    const bool is_flashing_yellow =
        tl_info.color() == voy::TrafficLight::YELLOW && tl_info.is_flashing();
    // If the traffic light has turn to green for a long time, or the it's
    // long-term flashing yellow, we assume the agent is not waiting for this
    // traffic light. Skip it.
    if (has_turn_to_green_for_long_time || is_flashing_yellow) {
      continue;
    }

    // Otherwise, the agent might be waiting for traffic light if it overlaps
    // with the pre-defined wait area.
    const math::Range1d tl_wait_area(
        tl_info.start_arclength() - kDefaultLongitudinalTLWaitingDistInMeter,
        tl_info.start_arclength());
    if (math::AreRangesOverlapping(tl_wait_area, longitudinal_range) &&
        (planner_object.HasPerceptionAttribute(
             voy::perception::Attribute::VEHICLE_BRAKE_SIGNAL_ON) ||
         planner_object.is_cyclist())) {
      return true;
    }
  }

  return false;
}

inline bool IsLowConfidenceObject(const PlannerObject& planner_object) {
  return planner_object.is_sustained() || planner_object.is_early_published();
}

inline bool IsPhysicalLaneMarking(
    double arclength_m, math::pb::Side side,
    const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule) {
  if (lane_marking_traffic_rule == nullptr) {
    return false;
  }
  const auto lane_marking_type =
      lane_marking_traffic_rule->GetLaneMarkingType(arclength_m, side);
  return lane_marking_type != hdmap::LaneMarking::Attribute::UNKNOWN_TYPE &&
         lane_marking_type != hdmap::LaneMarking::Attribute::VIRTUAL;
}

bool IsAgentTowardReferenceLine(const math::pb::Side& center_line_side,
                                double relative_heading_rad) {
  bool is_agent_toward_reference_line =
      (center_line_side == math::pb::kLeft &&
       math::NormalizeMinusPiToPi(relative_heading_rad) <
           -kMinTowardReferenceLineAngle) ||
      (center_line_side == math::pb::kRight &&
       math::NormalizeMinusPiToPi(relative_heading_rad) >
           kMinTowardReferenceLineAngle);
  return is_agent_toward_reference_line;
}

// Returns true if the agent is a vehicle near current lane heading towards
// reference path in slow speed.
bool IsSlowCutInVehicle(const PlannerObject& planner_object,
                        const pb::TrajectoryReasoningInfo& reasoning_info,
                        const double max_lane_encroachment_m) {
  if (!planner_object.is_vehicle()) {
    return false;
  }
  // TODO(sixian): Consider change to front bumper.
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }

  constexpr double kMaxSpeedForSlowCutInAgent = 2.0;
  if (planner_object.speed() > kMaxSpeedForSlowCutInAgent) {
    return false;
  }

  if (reasoning_info.is_stationary() &&
      !reasoning_info.contains_stm_prediction()) {
    return false;
  }

  constexpr double kMaxAgentConsiderableLateralDistToLaneBoundary = 3.0;
  if (max_lane_encroachment_m <
      -kMaxAgentConsiderableLateralDistToLaneBoundary) {
    // Ignore vehicles that are 3 meters away from the current lane's lane
    // marking.
    return false;
  }

  if (!reasoning_info.is_agent_toward_reference_line()) {
    return false;
  }

  return true;
}

// Returns true if a queried obj id is pull over jump in trailing object.
bool IsPullOverJumpInDestTrailingObstacle(
    const pull_over::DestinationMetaData& destination_meta, bool is_cz,
    ObjectId obj_id) {
  if (!destination_meta.jump_in_guidance.has_value()) {
    return false;
  }
  const std::vector<pull_over::PullOverObjectInfo>& trailing_objs =
      destination_meta.jump_in_guidance->trailing_objects();
  return std::any_of(
      trailing_objs.begin(), trailing_objs.end(),
      [is_cz, obj_id](const pull_over::PullOverObjectInfo& obj_info) {
        return obj_info.typed_object_id().is_construction_zone() == is_cz &&
               obj_info.typed_object_id().id == obj_id;
      });
}

bool WillTtcTriggerEs(const PlannerObject& planner_object,
                      const double tracked_pose_ttc_in_seconds) {
  // When vehicle TTC is very small, should consider ES.
  // TODO(mingshenchen): We may also want to apply to VRU in the future.
  if (planner_object.is_vehicle() &&
      tracked_pose_ttc_in_seconds < kMaxTtcToTriggerEsInSeconds) {
    return true;
  }

  return false;
}

bool WillTrackedPoseCauseEmergencySwerve(
    const PlannerObject& planner_object, const EgoInLaneParams& ego_param,
    const double tracked_pose_ttc_in_seconds,
    const AgentSnapshotInLaneState& agent_inlane_state,
    const RequiredLateralGap& required_lat_gap) {
  constexpr double kMinEgoSpeedForEmergencySwerveMps = 5.0;
  if (ego_param.speed_mps < kMinEgoSpeedForEmergencySwerveMps) {
    return false;
  }

  if (WillTtcTriggerEs(planner_object, tracked_pose_ttc_in_seconds)) {
    return true;
  }

  // Return false when there is enough lateral distance.
  math::Range1d fullbody_lateral_range = math::Range1d(
      agent_inlane_state.inlane_param.full_body_start_lateral_distance_m,
      agent_inlane_state.inlane_param.full_body_end_lateral_distance_m);
  if (path::GetAgentLateralTrackedSideToEgo(
          ego_param, fullbody_lateral_range,
          /*extended_buffer=*/
          required_lat_gap.critical_required_lateral_gap) != math::pb::kOn) {
    return false;
  }

  double lateral_overlap_distance = 0;
  double longitudinal_distance = 0;
  path::ComputeOverlapDistanceForLateralAndLongitudinal(
      ego_param, agent_inlane_state, required_lat_gap, lateral_overlap_distance,
      longitudinal_distance);
  // We consider critical lateral gap in longitudinal_distance calculation, so
  // it can be negative, which means the distance is smaller than critical RLG.
  if (longitudinal_distance < 0) {
    return true;
  }
  // Return false when Ego can just brake to avoid collision.
  constexpr double kSpeedEmergencyBrakeDecelarationInMpss = 10.0;
  const double min_brake_distance =
      ego_param.speed_mps * ego_param.speed_mps /
      (2.0 * kSpeedEmergencyBrakeDecelarationInMpss);
  if (longitudinal_distance > min_brake_distance) {
    return false;
  }

  // When Ego can not just brake to avoid collision, Ego should swerve to avoid
  // collision, returns true when lateral acceleration is larger than threshold.
  if (path::ComputeLateralAcceleration(
          ego_param.speed_mps, ego_param.wheel_base, lateral_overlap_distance,
          longitudinal_distance) >
      path::kMaxComfortableLateralAccelerationForVehAndCycInMpss) {
    return true;
  }
  return false;
}

bool WillTrackedPoseCauseEmergencySwerve(
    const PlannerObject& planner_object, const EgoInLaneParams& ego_param,
    const double tracked_pose_ttc_in_seconds,
    const ObjectOccupancyParam& agent_occupancy_param,
    const RequiredLateralGap& required_lat_gap) {
  if (WillTtcTriggerEs(planner_object, tracked_pose_ttc_in_seconds)) {
    return true;
  }

  // Return false when there is enough lateral distance.
  math::Range1d fullbody_lateral_range =
      math::Range1d(agent_occupancy_param.full_body_start_lateral_distance_m,
                    agent_occupancy_param.full_body_end_lateral_distance_m);
  if (path::GetAgentLateralTrackedSideToEgo(
          ego_param, fullbody_lateral_range,
          /*extended_buffer=*/
          required_lat_gap.critical_required_lateral_gap) != math::pb::kOn) {
    return false;
  }

  double lateral_overlap_distance = 0;
  double longitudinal_distance = 0;
  path::ComputeOverlapDistanceForLateralAndLongitudinal(
      ego_param, agent_occupancy_param, required_lat_gap,
      lateral_overlap_distance, longitudinal_distance);
  // We consider critical lateral gap in longitudinal_distance calculation, so
  // it can be negative, which means the distance is smaller than critical RLG.
  if (longitudinal_distance < 0) {
    return true;
  }
  // Return false when Ego can just brake to avoid collision.
  constexpr double kSpeedEmergencyBrakeDecelarationInMpss = 10.0;
  const double min_brake_distance =
      ego_param.speed_mps * ego_param.speed_mps /
      (2.0 * kSpeedEmergencyBrakeDecelarationInMpss);
  if (longitudinal_distance > min_brake_distance) {
    return false;
  }

  // When Ego can not just brake to avoid collision, Ego should swerve to avoid
  // collision, returns true when lateral acceleration is larger than threshold.
  if (path::ComputeLateralAcceleration(
          ego_param.speed_mps, ego_param.wheel_base, lateral_overlap_distance,
          longitudinal_distance) >
      path::kMaxComfortableLateralAccelerationForVehAndCycInMpss) {
    return true;
  }
  return false;
}

// Do a quick filter to improve efficiency. These conditions are necessary but
// not sufficient conditions that Ego will ES for an agent.
bool ShouldConsiderEmergencySwerveForAgent(
    const PlannerObject& planner_object,
    const bool is_agent_in_front_of_front_bumper,
    const double tracked_pose_ttc_in_seconds,
    const bool is_xlane_nudge_object) {
  // We don't want ES to interact with xlane nudge for now.
  if (is_xlane_nudge_object) {
    return false;
  }

  if (WillTtcTriggerEs(planner_object, tracked_pose_ttc_in_seconds)) {
    return true;
  }

  if (!is_agent_in_front_of_front_bumper) {
    return false;
  }

  // Consider all ped and traffic cone.
  if (planner_object.is_pedestrian() || planner_object.is_traffic_cone()) {
    return true;
  }

  // For cyc and vehicle, only consider slow agents to reduce FP.
  if (planner_object.is_cyclist() || planner_object.is_vehicle()) {
    constexpr double kMaxCycVehicleSpeedForEsMps = 2.0;
    return planner_object.speed() < kMaxCycVehicleSpeedForEsMps;
  }

  return false;
}

std::pair<double, double> ComputeAbsAndSignedAverageCurvature(
    const PredictedTrajectoryWrapper& predicted_trajectory) {
  static constexpr double kQueryTime = 4.0;
  const auto [path_polyline, profile] =
      physics::FromTrajectoryPosesToPathAndProfile(
          predicted_trajectory.poses().size(),
          [&](size_t i) { return predicted_trajectory.poses().at(i); },
          /*simplify=*/true);
  if (profile.empty() || path_polyline.empty()) {
    return std::make_pair(0.0, 0.0);
  }
  const double s_end =
      std::min(speed::FinsStateSAtTime(profile, kQueryTime + profile.front().t),
               profile.back().x);
  const math::Curve2d path_curve(path_polyline.points(),
                                 math::pb::Interpolation1dType::kCSpline,
                                 /*check_intersect=*/false);
  return path_curve.GetAbsAndSignedAverageCurvatureInRange(0.0, s_end);
}

}  // namespace

// Note: maybe we should not consider RLG, but use 0.0 instead.
bool IsInEgoLaneSnapshot(const RequiredLateralGap& required_lat_gap,
                         double agent_encroachment_m) {
  return agent_encroachment_m > -required_lat_gap.critical_required_lateral_gap;
}

bool IsOncomingSnapshot(const double relative_heading_rad) {
  return std::fabs(relative_heading_rad) > kMaxRelativeOnComingHeadingInRad;
}

bool IsSameDirectionSnapshot(const double relative_heading_rad) {
  return std::fabs(relative_heading_rad) < kMaxRelativeAlongHeadingInRad;
}

bool IsDrivingForwardSnapshot(const double relative_heading_rad) {
  return (std::fabs(relative_heading_rad) > 0.0) &&
         (std::fabs(relative_heading_rad) < M_PI_2);
}

bool IsAbnormalInlaneParamSnapshot(const double start_arclength_m,
                                   const double end_arclength_m) {
  return end_arclength_m - start_arclength_m > kMaxValidSpatialRangeInMeter;
}

bool HasPersistentMotion(const std::optional<PlannerObjectRecentKinematicInfo>&
                             recent_kinematic_info,
                         const PlannerObjectsHistory& planner_objects_history,
                         const PlannerObject& planner_object_current_ts) {
  static constexpr size_t kNumMostRecentSnapshotsToConsider = 5;
  const bool was_heading_consistent =
      planner_objects_history.WasHeadingConsistent(
          planner_object_current_ts, kNumMostRecentSnapshotsToConsider,
          math::Sec2Ms(kMaxHistoryDurationConsiderationInSec));
  bool has_similar_kinematic_info = true;
  static constexpr double kMaxAllowedSpeedDiffInMps = 0.8;
  static constexpr double kMaxAllowedAccelDiffInMpss = 2.0;

  // The number of frames that the planner_object was detected by Perception.
  size_t detected_frames = 0;
  if (recent_kinematic_info.has_value()) {
    // Iterate from the back of the planner_object_history to find the latest
    // timestamp when this object occurs. Compare the extrapolated kinematics
    // (acceleration/jerk) with the current values. If the difference is with
    // the tolerance, we assume this object has consistent kinematics.
    for (auto planner_object_map_it =
             planner_objects_history.history().crbegin();
         planner_object_map_it != planner_objects_history.history().crend();
         ++planner_object_map_it) {
      const std::shared_ptr<const std::unordered_map<int64_t, PlannerObject>>&
          planner_object_map_ptr = *planner_object_map_it;
      if (planner_object_map_ptr == nullptr) continue;
      if (planner_object_map_ptr->find(planner_object_current_ts.id()) ==
          planner_object_map_ptr->end()) {
        continue;
      }
      ++detected_frames;
      const PlannerObject& past_planner_object =
          planner_object_map_ptr->at(planner_object_current_ts.id());
      const double time_in_sec =
          math::Ms2Sec((planner_object_current_ts.timestamp() -
                        past_planner_object.timestamp()));
      if (time_in_sec > kMaxHistoryDurationConsiderationInSec) {
        // If the object was detected less than 4 frames, we do not have enough
        // information to detect whether its dynamics is persistent. Thus, early
        // return false.
        if (detected_frames < kNumMostRecentSnapshotsToConsider) {
          has_similar_kinematic_info = false;
        }
        break;
      }
      const double speed_diff = std::max(
          std::abs(planner_object_current_ts.speed() -
                   (past_planner_object.speed() +
                    recent_kinematic_info->average_accel_mpss * time_in_sec)),
          std::abs(past_planner_object.speed() -
                   recent_kinematic_info->average_speed_mps));
      const double accel_diff = std::max(
          std::abs(planner_object_current_ts.acceleration() -
                   (past_planner_object.acceleration() +
                    recent_kinematic_info->average_jerk_mpsss * time_in_sec)),
          std::abs(past_planner_object.acceleration() -
                   recent_kinematic_info->average_accel_mpss));
      if (speed_diff >= kMaxAllowedSpeedDiffInMps ||
          accel_diff >= kMaxAllowedAccelDiffInMpss) {
        has_similar_kinematic_info = false;
        break;
      }
    }
  }
  return was_heading_consistent && has_similar_kinematic_info;
}

bool IsCrossingSnapshot(const AgentSnapshotInLaneParam& agent_inlane_param) {
  // no encroachment.
  if (agent_inlane_param.cross_track_encroachment_m < 0.0) {
    return false;
  }

  // The heading is not potentially crossing.
  const double relative_heading =
      std::abs(agent_inlane_param.relative_heading_rad);
  if (relative_heading < kMaxRelativeCrossingHeadingInRad ||
      relative_heading > M_PI - kMaxRelativeCrossingHeadingInRad) {
    return false;
  }

  const double cross_speed_mps = agent_inlane_param.cross_track_speed_mps;
  return std::fabs(cross_speed_mps) >
         kMinAbsoluteCrossSpeedForEncroachingSnapshotInMps;
}

bool IsEmergencyAvoidanceSnapshot(
    const EgoInLaneParams& ego_param,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const double agent_start_arclength_m, const double agent_end_arclength_m,
    const double time_stamp, const bool is_stationary) {
  const double agent_start_s = agent_start_arclength_m -
                               ego_param.rear_axle_to_front_bumper_m -
                               kLongitudinalExtendBufferInMeter;
  const double agent_end_s = agent_end_arclength_m +
                             ego_param.rear_axle_to_rear_bumper_m +
                             kLongitudinalExtendBufferInMeter;
  double ego_start_s = ego_param.arclength_m;
  double ego_end_s = ego_param.arclength_m;
  speed::State ego_state;
  int unused_time_ix = -1;
  // Update the ego range with speed profile.
  if (speed::GetStateAtTime(emergency_brake_speed_profile, time_stamp,
                            &ego_state, unused_time_ix)) {
    is_stationary ? math::UpdateMax(ego_state.x, ego_end_s)
                  : math::UpdateMax(ego_state.x, ego_start_s);
  }
  if (speed::GetStateAtTime(lower_bound_speed_profile, time_stamp, &ego_state,
                            unused_time_ix)) {
    is_stationary ? math::UpdateMax(ego_state.x, ego_end_s)
                  : math::UpdateMax(ego_state.x, ego_end_s);
  }

  // The two values are very similar with calculation error.
  if (ego_start_s > ego_end_s) {
    std::swap(ego_start_s, ego_end_s);
  }
  // Return true if two intervals overlap.
  return std::max(agent_start_s, ego_start_s) <
         std::min(agent_end_s, ego_end_s) + math::constants::kEpsilon;
}

// Returns true if the snapshot is hard blocking.
bool IsHardBlockingSnapshot(const EgoInLaneParams& ego_param,
                            const AgentSnapshotInLaneParam& agent_inlane_param,
                            const RequiredLateralGap& required_lat_gap) {
  // Returns clearance based hard blocking state.
  if (agent_inlane_param.cross_track_encroachment_m >
          math::constants::kEpsilon &&
      agent_inlane_param.left_inlane_clearance_m <
          ego_param.width_m + required_lat_gap.critical_required_lateral_gap &&
      agent_inlane_param.right_inlane_clearance_m <
          ego_param.width_m + required_lat_gap.critical_required_lateral_gap) {
    return true;
  }
  // Returns hard blocking if the agent invade the center lane.
  if (agent_inlane_param.center_line_side == math::pb::kOn) {
    return true;
  }

  // Returns hard blocking if the agent is fully in lane.
  if (agent_inlane_param.is_fully_in_lane) {
    return true;
  }

  return false;
}

bool IsHardBlockingSnapshot(const EgoInLaneParams& ego_param,
                            const ObjectOccupancyParam& object_occupancy_param,
                            const RequiredLateralGap& required_lat_gap) {
  // Returns clearance based hard blocking state.
  if (object_occupancy_param.cross_track_encroachment_m >
          math::constants::kEpsilon &&
      object_occupancy_param.left_boundary_clearance_m <
          ego_param.width_m + required_lat_gap.critical_required_lateral_gap &&
      object_occupancy_param.right_boundary_clearance_m <
          ego_param.width_m + required_lat_gap.critical_required_lateral_gap) {
    return true;
  }
  // Returns hard blocking if the agent invade the center lane.
  if (object_occupancy_param.center_line_side == math::pb::kOn) {
    return true;
  }

  // Returns hard blocking if the agent is fully in lane.
  if (object_occupancy_param.is_fully_within_corridor) {
    return true;
  }

  return false;
}
/****************************************/
/*   Agent behind ego util function     */
/****************************************/
// Return true if the static agent is far behind enough.
bool IsSafeToIgnore(const EgoInLaneParams& ego_param,
                    pb::BehaviorType behavior_type,
                    double snapshot_end_arclength_m, bool is_stationary) {
  const double distance_to_ego_rear_in_meter =
      (ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m) -
      snapshot_end_arclength_m;

  if (behavior_type != pb::REVERSE &&
      distance_to_ego_rear_in_meter > kStaticAgentIgnoreDistanceInMeter &&
      is_stationary) {
    return true;
  }

  return false;
}

bool ShouldIgnoreAgentBehind(
    const EgoInLaneParams& ego_param,
    const AgentInLaneStates& agent_inlane_states,
    const pb::BehaviorType behavior_type,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // We should not ignore the agent if it is not fully behind the ego car.
  if (!agent_inlane_states.tracked_state.blocking_info.is_fully_behind_ego) {
    return false;
  }
  const AgentSnapshotInLaneParam& tracked_inlane_param =
      agent_inlane_states.tracked_state.inlane_param;

  const double safe_lon_dist = CalculateSafeLongitudinalDistance(
      ego_param.along_track_speed_mps,
      tracked_inlane_param.along_track_speed_mps,
      /*is_front_of_ego=*/false, planner::path::RssConfig());
  const double distance_to_rear_object = ego_param.arclength_m -
                                         ego_param.rear_axle_to_rear_bumper_m -
                                         tracked_inlane_param.end_arclength_m;
  DCHECK_GE(distance_to_rear_object, 0.0);

  if (behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    // Ignore agents that is farway or located on left side of the ego lane.
    return tracked_inlane_param.center_line_side == math::pb::kLeft ||
           tracked_inlane_param.center_line_clearance_m >
               kBesideAgentIgnoreLateralDistanceInMeter ||
           distance_to_rear_object > safe_lon_dist;
  }

  // TODO(wanghao): Maybe ignore agents behind ego on the left or right
  // lane more clearly based on the direction of nudge.
  // TODO(wanghao): Fix the issue of GetAgentLateralTrackedSideToEgo function
  // being called multiple times in fixit.
  if (behavior_type == pb::LANE_KEEP) {
    // Should ignore agent is fully in lane and not on one side of ego.
    if (tracked_inlane_param.is_fully_in_lane &&
        path::GetAgentLateralTrackedSideToEgo(
            ego_param,
            math::Range1d(
                tracked_inlane_param.full_body_start_lateral_distance_m,
                tracked_inlane_param.full_body_end_lateral_distance_m),
            /*extended_buffer=*/0.0) == math::pb::kOn) {
      return true;
    }
    // Ignore agents is farway or won't encroach in a period.
    return distance_to_rear_object > safe_lon_dist;
  }

  return false;
}

bool ShouldIgnoreAgentBehind(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyParam& object_occupancy_param,
    const bool is_fully_behind_ego, const pb::BehaviorType behavior_type,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // We should not ignore the agent if it is not fully behind the ego car.
  if (!is_fully_behind_ego) {
    return false;
  }

  const double safe_lon_dist = CalculateSafeLongitudinalDistance(
      ego_param.along_track_speed_mps,
      object_occupancy_param.along_track_speed_mps,
      /*is_front_of_ego=*/false, planner::path::RssConfig());
  const double distance_to_rear_object =
      ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m -
      object_occupancy_param.snapshot_end_arclength_m;
  DCHECK_GE(distance_to_rear_object, 0.0);

  if (behavior_type == pb::DECOUPLED_PULL_OVER &&
      pullover_jump_in_type ==
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    // Ignore agents that is farway or located on left side of the ego lane.
    return object_occupancy_param.center_line_side == math::pb::kLeft ||
           object_occupancy_param.center_line_clearance_m >
               kBesideAgentIgnoreLateralDistanceInMeter ||
           distance_to_rear_object > safe_lon_dist;
  }

  // TODO(wanghao): Maybe ignore agents behind ego on the left or right
  // lane more clearly based on the direction of nudge.
  if (behavior_type == pb::LANE_KEEP) {
    // Should ignore agent is fully in lane and not on one side of ego.
    if (object_occupancy_param.is_fully_within_corridor &&
        path::GetAgentLateralTrackedSideToEgo(
            ego_param,
            math::Range1d(
                object_occupancy_param.full_body_start_lateral_distance_m,
                object_occupancy_param.full_body_end_lateral_distance_m),
            /*extended_buffer=*/0.0) == math::pb::kOn) {
      return true;
    }
    // Ignore agents is farway or won't encroach in a period.
    return distance_to_rear_object > safe_lon_dist;
  }

  return false;
}

bool IsFarAwayBehindAgent(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const bool is_stationary, const pb::BehaviorType behavior_type,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // Ignore the static agent that is far behind.
  if (IsSafeToIgnore(ego_param, behavior_type,
                     agent_state.tracked_state.inlane_param.end_arclength_m,
                     is_stationary)) {
    return true;
  }

  // Include the rest static agents.
  if (is_stationary) {
    return false;
  }

  // Ignore the agent is mostly in lane and ignorable relative side
  // based on behavior_type.
  return ShouldIgnoreAgentBehind(ego_param, agent_state, behavior_type,
                                 pullover_jump_in_type);
}

bool IsFarAwayBehindAgent(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyParam& object_occupancy_param,
    const bool is_fully_behind_ego, const bool is_stationary,
    const pb::BehaviorType behavior_type,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // Ignore the static agent that is far behind.
  if (IsSafeToIgnore(ego_param, behavior_type,
                     object_occupancy_param.snapshot_end_arclength_m,
                     is_stationary)) {
    return true;
  }

  // Include the rest static agents.
  if (is_stationary) {
    return false;
  }

  // Ignore the agent is mostly in lane and ignorable relative side
  // based on behavior_type.
  return ShouldIgnoreAgentBehind(ego_param, object_occupancy_param,
                                 is_fully_behind_ego, behavior_type,
                                 pullover_jump_in_type);
}

bool IsFullyBehindRearBumper(const EgoInLaneParams& ego_param,
                             const math::Range1d& object_arclength_range) {
  // When ego in starting position of the first lane of lane sequence, ego
  // arclength is near 0, then ego rear bumper arclength is negative number.
  // All agents fully behind ego arclength is 0 due to we use kForbid flag
  // in “GetProximity“ API to generate agent inlane param, so we can identify
  // the agents fully behind ego based on the condition that both
  // start_arclenght and end_arclength are equal to 0.
  if (math::NearZero(object_arclength_range.start_pos) &&
      math::NearZero(object_arclength_range.end_pos)) {
    return true;
  }
  return ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m >
         object_arclength_range.end_pos;
}

bool IsFullyBehindFrontBumper(const EgoInLaneParams& ego_param,
                              const math::Range1d& object_arclength_range) {
  // When ego in starting position of the first lane of lane sequence, ego
  // arclength is near 0, then ego front bumper arclength is negative number.
  // All agents fully behind ego arclength is 0 due to we use kForbid flag
  // in “GetProximity“ API to generate agent inlane param, so we can identify
  // the agents fully behind ego based on the condition that both
  // start_arclenght and end_arclength are equal to 0.
  if (math::NearZero(object_arclength_range.start_pos) &&
      math::NearZero(object_arclength_range.end_pos)) {
    return true;
  }
  return ego_param.arclength_m + ego_param.rear_axle_to_front_bumper_m >
         object_arclength_range.end_pos;
}

bool IsFullyInFrontOfFrontBumper(const EgoInLaneParams& ego_param,
                                 double object_start_arclength_m) {
  return ego_param.arclength_m + ego_param.rear_axle_to_front_bumper_m <
         object_start_arclength_m;
}

bool IsAgentWithinComfort(const RequiredLateralGap& required_lat_gap,
                          const pb::TrajectoryReasoningInfo& reasoning_info) {
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  return reasoning_info.plan_init_state_distance() <=
         required_lat_gap.comfort_required_lateral_gap;
}

// Returns true if agent is not fully behind rear bumper and within critical
// required lateral gap to ego.
bool IsAgentWithinCritical(const RequiredLateralGap& required_lat_gap,
                           const pb::TrajectoryReasoningInfo& reasoning_info) {
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  return reasoning_info.plan_init_state_distance() <=
         required_lat_gap.critical_required_lateral_gap;
}

inline bool IsLeftTurnOncomingAgent(
    const PredictedTrajectoryWrapper& trajectory,
    const double relative_heading_rad) {
  // The agent must in the oncoming heading range. (-PI - buffer, -PI/2 +
  // buffer).
  const double normalized_relative_heading_rad =
      math::NormalizeMinusPiToPi(relative_heading_rad);
  if (normalized_relative_heading_rad >
          -M_PI / 2.0 + kMinTowardReferenceLineAngle &&
      normalized_relative_heading_rad < M_PI - kMinTowardReferenceLineAngle) {
    return false;
  }

  // If the agent is on the left lane.
  if (trajectory.associated_route_opt().has_value()) {
    std::vector<int> section_ids;
    std::size_t left_turn_lane_amount = 0;
    for (const auto& route : *trajectory.associated_route_opt()) {
      if (route.lane_ptr != nullptr && route.lane_ptr->IsInJunction()) {
        section_ids.push_back(route.lane_ptr->section()->id());
        if (route.lane_ptr->turn() == hdmap::Lane::LEFT) {
          ++left_turn_lane_amount;
        }
      }
    }

    // We must have at least one left lane and there is no other type lane in
    // the junction, also all the left lanes are in the same section.
    if (left_turn_lane_amount != 0 &&
        left_turn_lane_amount == section_ids.size() &&
        std::all_of(section_ids.begin(), section_ids.end(),
                    [&](const int section_id) {
                      return section_id == section_ids[0];
                    })) {
      return true;
    }
  }

  // If the agent is from a lane that has left/uturn lane successors.
  if (trajectory.latest_isolated_lane_opt().has_value()) {
    const pnc_map::Lane* latest_isolated_lane =
        trajectory.latest_isolated_lane_opt()->lane_ptr;
    if (latest_isolated_lane != nullptr &&
        !latest_isolated_lane->successors().empty() &&
        std::all_of(latest_isolated_lane->successors().begin(),
                    latest_isolated_lane->successors().end(),
                    [](const auto* lane_ptr) {
                      return lane_ptr->turn() == hdmap::Lane::LEFT ||
                             lane_ptr->turn() == hdmap::Lane::U_TURN;
                    })) {
      return true;
    }
  }

  return false;
}

bool IsObjectFollowAssociatedLane(const PlannerObject& planner_object) {
  constexpr double kMinAbsHeadingMisalignment =
      math::Degree2Radian(15);  // in redian
  for (const auto* lane : planner_object.associated_lanes()) {
    int64_t lane_id = lane->id();
    const auto it =
        planner_object.center_arclength_on_associated_lane_ids().find(lane_id);
    if (it == planner_object.center_arclength_on_associated_lane_ids().end()) {
      continue;
    }
    const math::geometry::Point2d lane_deriv =
        lane->center_line().GetInterpDeriv(it->second);
    if (math::geometry::LengthSq(lane_deriv) < math::constants::kEpsilonForSq) {
      continue;
    }
    const double lane_heading =
        math::geometry::FromVector2DToHeading(lane_deriv);
    if (std::abs(math::NormalizeMinusPiToPi(lane_heading -
                                            planner_object.heading())) <
        kMinAbsHeadingMisalignment) {
      return true;
    }
  }
  return false;
}

bool IsIgnorableUnknownAgent(
    const AgentInLaneStates& agent_state,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const bool has_persistent_motion,
    const bool should_trigger_unstuck_for_unknown_objects) {
  if (agent_state.agent_metadata.agent_type !=
      voy::perception::ObjectType::UNKNOWN) {
    return false;
  }

  // When this flag is on, don't ignore any static unknown agent.
  if (FLAGS_planning_enable_path_reaction_to_all_unknown &&
      reasoning_info.is_stationary()) {
    return false;
  }

  const auto& attributes = agent_state.agent_metadata.attributes;

  // Don't ignore occupancy cluster output by tracking.
  if (reasoning_info.is_stationary() &&
      std::any_of(attributes.begin(), attributes.end(),
                  [](const voy::perception::Attribute attribute) {
                    return attribute == voy::perception::CLUSTER_ONLY_TRACK ||
                           attribute == voy::perception::CLUSTER_MAJOR_TRACK;
                  })) {
    return false;
  }
  if (std::any_of(attributes.begin(), attributes.end(),
                  [](const voy::perception::Attribute attribute) {
                    return attribute == voy::perception::IS_VEGETATION;
                  })) {
    return false;
  }
  if (std::any_of(attributes.begin(), attributes.end(),
                  [](const voy::perception::Attribute attribute) {
                    return attribute ==
                           voy::perception::CYC_WITHOUT_PERSON_ATTR;
                  }) &&
      reasoning_info.plan_init_state_distance() <
          constants::kCycWithoutPersonThresholdInMeter) {
    return false;
  }
  // Don't ignore animals if if it has persistent motion.
  if (has_persistent_motion &&
      std::any_of(attributes.begin(), attributes.end(),
                  [](const voy::perception::Attribute attribute) {
                    return attribute == voy::perception::IS_ANIMAL;
                  })) {
    rt_event::PostRtEvent<rt_event::planner::NotIgnoreAnimalsInPath>(
        reasoning_info.is_crossing() ? "crossing" : "non-crossing");
    return false;
  }
  if (should_trigger_unstuck_for_unknown_objects &&
      reasoning_info.is_stationary() &&
      reasoning_info.plan_init_state_distance() <
          kFarthestUnknownObjectsForUnstuckInM) {
    return false;
  }
  return true;
}

bool IsIgnorableUnknownAgent(
    const ObjectOccupancyState& agent_state,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const bool has_persistent_motion,
    const bool should_trigger_unstuck_for_unknown_objects) {
  if (agent_state.object_type() != voy::perception::ObjectType::UNKNOWN) {
    return false;
  }

  // When this flag is on, any unknown agent is not ignorable.
  if (FLAGS_planning_enable_path_reaction_to_all_unknown &&
      reasoning_info.is_stationary()) {
    return false;
  }

  // Don't ignore stationary occupancy cluster output by tracking.
  if (reasoning_info.is_stationary() &&
      agent_state.planner_object().is_occupancy_cluster()) {
    return false;
  }

  if (agent_state.planner_object().is_cyc_no_person() &&
      reasoning_info.plan_init_state_distance() <
          constants::kCycWithoutPersonThresholdInMeter) {
    return false;
  }
  if (agent_state.planner_object().is_vegetation()) {
    return false;
  }
  // Don't ignore animals.
  if (has_persistent_motion && agent_state.planner_object().is_animal()) {
    rt_event::PostRtEvent<rt_event::planner::NotIgnoreAnimalsInPath>(
        reasoning_info.is_crossing() ? "crossing" : "non-crossing");
    return false;
  }
  if (should_trigger_unstuck_for_unknown_objects &&
      reasoning_info.is_stationary() &&
      reasoning_info.plan_init_state_distance() <
          kFarthestUnknownObjectsForUnstuckInM) {
    return false;
  }
  return true;
}

bool IsCrossingTrajectory(const AgentInLaneStates& agent_state,
                          const LateralBlockingStateMetaData& blocking_data,
                          bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary || agent_state.predicted_trajectories.empty()) {
    return false;
  }
  // Use AgentTrajectoryIntentionType::CROSS.
  const AgentTrajectoryInLaneStates& predicted_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             blocking_data.trajectory_id);
  if (predicted_trajectory.trajectory_metadata.intention_type !=
      pb::AgentTrajectoryIntentionType::CROSS) {
    return false;
  }

  if (predicted_trajectory.predicted_states.empty()) {
    return false;
  }

  // We will treat agent as crossing if any primary prediction snapshot is
  // oncoming, mainly to quickly recall oncoming&crossing agents. But don't
  // apply to ped as there are many FP (example
  // https://drive.google.com/file/d/1F3HdlPxfSDfsvMo7I8j2oc100GdHOElu/view?usp=sharing).
  if (agent_state.agent_metadata.agent_type !=
          voy::perception::ObjectType::PED &&
      std::any_of(
          predicted_trajectory.predicted_states.begin(),
          predicted_trajectory.predicted_states.end(),
          [](const auto& predicted_state) {
            return IsOncomingSnapshot(
                predicted_state.inlane_state.inlane_param.relative_heading_rad);
          })) {
    return true;
  }

  // Check any hard blocking snapshot is not oncoming or same direction.
  DCHECK_EQ(predicted_trajectory.predicted_states.size(),
            blocking_data.trajectory_blocking_infos.size());

  for (size_t idx = 0; idx < predicted_trajectory.predicted_states.size();
       ++idx) {
    const pb::LateralBlockingInfo& blocking_info =
        blocking_data.trajectory_blocking_infos[idx];
    const StampedAgentSnapshotInLaneState& predicted_state =
        predicted_trajectory.predicted_states[idx];
    if (blocking_info.blocking_state() !=
        pb::LateralBlockingState::HARD_BLOCKING) {
      continue;
    }
    if (!IsOncomingSnapshot(
            predicted_state.inlane_state.inlane_param.relative_heading_rad) &&
        !IsSameDirectionSnapshot(
            predicted_state.inlane_state.inlane_param.relative_heading_rad)) {
      return true;
    }
  }

  return false;
}

bool IsCrossingTrajectory(const ObjectOccupancyState& object_occupancy_state,
                          const LateralBlockingStateMetaData& blocking_data,
                          bool is_crossing_object, bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  // Use is_crossing_object.
  if (!is_crossing_object) {
    return false;
  }
  const ObjectTrajectoryStates& predicted_trajectory = FIND_OR_DIE_WITH_PRINT(
      object_occupancy_state.predicted_trajectory_occupancy_states(),
      blocking_data.trajectory_id);
  if (predicted_trajectory.predicted_states().empty()) {
    return false;
  }

  // Return true if has any oncoming snapshot for cross agent.
  if (object_occupancy_state.object_type() !=
          voy::perception::ObjectType::PED &&
      std::any_of(
          predicted_trajectory.predicted_states().begin(),
          predicted_trajectory.predicted_states().end(),
          [](const auto& predicted_state) {
            return IsOncomingSnapshot(
                predicted_state.object_occupancy_param().relative_heading_rad);
          })) {
    return true;
  }

  // Check any hard blocking snapshot is not oncoming or same direction.
  DCHECK_EQ(predicted_trajectory.predicted_states().size(),
            blocking_data.trajectory_blocking_infos.size());

  for (size_t idx = 0; idx < predicted_trajectory.predicted_states().size();
       ++idx) {
    const pb::LateralBlockingInfo& blocking_info =
        blocking_data.trajectory_blocking_infos[idx];
    const ObjectStampedSnapshotInfo& predicted_state =
        predicted_trajectory.predicted_states()[idx];
    if (blocking_info.blocking_state() !=
        pb::LateralBlockingState::HARD_BLOCKING) {
      continue;
    }
    if (!IsOncomingSnapshot(
            predicted_state.object_occupancy_param().relative_heading_rad) &&
        !IsSameDirectionSnapshot(
            predicted_state.object_occupancy_param().relative_heading_rad)) {
      return true;
    }
  }

  return false;
}

bool IsOncomingAheadOfEgoFrontBumper(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const RequiredLateralGap& required_lat_gap, int predicted_trajectory_id,
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      agent_state.predicted_trajectories.empty()) {
    return false;
  }
  // Only consider the agents in front of ego front bumper.
  if (agent_state.tracked_state.inlane_param.full_body_end_arclength_m <
      ego_param.front_bumper_arclength) {
    return false;
  }

  // Check if all snapshots located on one side of the ego lane in front of
  // ego front bumper are oncoming.
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  const bool fisrt_predict_snapshot_is_in_ego_lane = IsInEgoLaneSnapshot(
      required_lat_gap,
      predicted_states.size()
          ? predicted_states.front()
                .inlane_state.inlane_param.cross_track_encroachment_m
          : agent_state.tracked_state.inlane_param.cross_track_encroachment_m);
  const double ego_distance_driven =
      ego_param.along_track_speed_mps * ego_param.along_track_speed_mps /
      (2 * path::RssConfig().longitudinal_brake_max);
  for (const auto& predicted_state : predicted_states) {
    const bool is_oncoming_snapshot = IsOncomingSnapshot(
        predicted_state.inlane_state.inlane_param.relative_heading_rad);
    // Return false if there is any snapshot is not oncoming.
    if (!is_oncoming_snapshot) {
      return false;
    }
    // Skip if the snapshot is behind ego front bumper.
    if (predicted_state.inlane_state.inlane_param.full_body_end_arclength_m <
        ego_param.front_bumper_arclength + ego_distance_driven) {
      break;
    }
    // The considered agents include those that crossing agent(crossing left and
    // right side of ego lane), those that nomal oncoming, and those that
    // crossing one side of ego lane.
    const bool current_snapshot_is_in_ego_lane = IsInEgoLaneSnapshot(
        required_lat_gap,
        predicted_state.inlane_state.inlane_param.cross_track_encroachment_m);
    // 1. Check if all snapshots in ego lane are crossing.
    //                  o--<-|__| agent
    // ————————————————/———————————————
    //                /
    // |__|--> ego   /
    // —————————————/——————————————————
    //             ⌄   <- prediction
    if (reasoning_info.is_crossing() && current_snapshot_is_in_ego_lane) {
      continue;
    }
    // 2. Check for agents who have not crossed the lane or have crossed one
    // side of the lane. For agents crossed one side of the lane, E.g.
    // agent1 enters the lane with an oncoming snapshot, or
    // agent2 steps out the lane and all snapshots in the lane is oncoming,
    // we consider them to be oncoming agents;
    //
    //        <-------o-----|__| agent1
    // ——————————————/——————————————————
    //              /  <- prediction
    //             ⌄
    //        <-------o-----|__| agent2
    // |__|--> ego   /
    // —————————————/——————————————————
    //             ⌄   <- prediction
    if (current_snapshot_is_in_ego_lane !=
        fisrt_predict_snapshot_is_in_ego_lane) {
      return true;
    }
  }
  // All snapshots in front of ego is oncoming. retunr true.
  return true;
}

bool IsOncomingAheadOfEgoFrontBumper(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lat_gap, int predicted_trajectory_id,
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  // Only consider the agents in front of ego front bumper.
  if (object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .full_body_end_arclength_m < ego_param.front_bumper_arclength) {
    return false;
  }

  // Check if all snapshots located on one side of the ego lane in front of
  // ego front bumper are oncoming.
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  const bool first_predict_snapshot_is_in_ego_lane =
      IsInEgoLaneSnapshot(required_lat_gap, predicted_states.front()
                                                .object_occupancy_param()
                                                .cross_track_encroachment_m);
  const double ego_distance_driven =
      ego_param.along_track_speed_mps * ego_param.along_track_speed_mps /
      (2 * path::RssConfig().longitudinal_brake_max);
  for (const auto& predicted_state : predicted_states) {
    const bool is_oncoming_snapshot = IsOncomingSnapshot(
        predicted_state.object_occupancy_param().relative_heading_rad);
    // Return false if there is any snapshot is not oncoming.
    if (!is_oncoming_snapshot) {
      return false;
    }
    // Skip if the snapshot is behind ego front bumper.
    if (predicted_state.object_occupancy_param().full_body_end_arclength_m <
        ego_param.front_bumper_arclength + ego_distance_driven) {
      break;
    }
    // The considered agents include those that crossing agent(crossing left and
    // right side of ego lane), those that nomal oncoming, and those that
    // crossing one side of ego lane.
    const bool current_snapshot_is_in_ego_lane = IsInEgoLaneSnapshot(
        required_lat_gap,
        predicted_state.object_occupancy_param().cross_track_encroachment_m);
    // 1. Check that all snapshots in ego lane of crossing.
    //                  o--<-|__| agent
    // ————————————————/———————————————
    //                /
    // |__|--> ego   /
    // —————————————/——————————————————
    //             ⌄   <- prediction
    if (reasoning_info.is_crossing() && current_snapshot_is_in_ego_lane) {
      continue;
    }
    // 2. Check for agents who have not crossed the lane or have crossed one
    // side of the lane. For agents crossed one side of the lane, E.g.
    // agent1 enters the lane with an oncoming snapshot, or
    // agent2 steps out the lane and all snapshots in the lane is oncoming,
    // we consider them to be oncoming agents;
    //
    //        <-------o-----|__| agent1
    // ——————————————/——————————————————
    //              /  <- prediction
    //             ⌄
    //        <-------o-----|__| agent2
    // |__|--> ego   /
    // —————————————/——————————————————
    //             ⌄   <- prediction
    if (current_snapshot_is_in_ego_lane !=
        first_predict_snapshot_is_in_ego_lane) {
      return true;
    }
  }
  // All snapshots in front of ego is oncoming, return true.
  return true;
}

bool IsOncomingAgent(const AgentInLaneStates& agent_state,
                     const RequiredLateralGap& /* required_lat_gap */,
                     const pb::TrajectoryReasoningInfo& reasoning_info,
                     int predicted_trajectory_id) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const AgentTrajectoryInLaneStates& predicted_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id);
  if (predicted_trajectory.predicted_states.empty()) {
    return false;
  }

  // Switch side trajectory connot be regard as oncoming.
  if (predicted_trajectory.trajectory_metadata.intention_type ==
      pb::AgentTrajectoryIntentionType::CROSS) {
    return false;
  }

  return reasoning_info.is_oncoming_ahead_of_ego_front_bumper();
}

bool IsHeadingAlongNominalPath(const double relative_heading_rad) {
  const double abs_relative_heading_rad = std::abs(relative_heading_rad);
  return abs_relative_heading_rad < kAlongNominalPathHeadingDiffThreshold ||
         (M_PI - abs_relative_heading_rad <
          kAlongNominalPathHeadingDiffThreshold);
}

bool IsOncomingAgent(const ObjectOccupancyState& object_occupancy_state,
                     const RequiredLateralGap& /* required_lat_gap */,
                     const pb::TrajectoryReasoningInfo& reasoning_info,
                     bool is_crossing_object, int predicted_trajectory_id) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  // Switch side trajectory cannot be regard as oncoming.
  if (is_crossing_object) {
    return false;
  }

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }

  return reasoning_info.is_oncoming_ahead_of_ego_front_bumper();
}

bool IsSameDirectionAgent(const AgentInLaneStates& agent_state,
                          int predicted_trajectory_id, bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary || agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }
  return std::all_of(
      predicted_states.begin(), predicted_states.end(),
      [](const auto& predicted_state) {
        return IsSameDirectionSnapshot(
            predicted_state.inlane_state.inlane_param.relative_heading_rad);
      });
}

bool IsSameDirectionAgent(const ObjectOccupancyState& object_occupancy_state,
                          int predicted_trajectory_id, bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }
  return std::all_of(
      predicted_states.begin(), predicted_states.end(),
      [](const auto& predicted_state) {
        return IsSameDirectionSnapshot(
            predicted_state.object_occupancy_param().relative_heading_rad);
      });
}

bool IsDrivingForwardOnEgoLane(const AgentInLaneStates& agent_state,
                               const RequiredLateralGap& required_lat_gap,
                               int predicted_trajectory_id,
                               bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary || agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }
  bool has_encroached_snapshot = false;
  for (const auto& predicted_state : predicted_states) {
    if (!IsInEgoLaneSnapshot(required_lat_gap,
                             predicted_state.inlane_state.inlane_param
                                 .cross_track_encroachment_m)) {
      continue;
    }
    has_encroached_snapshot = true;
    if (!IsDrivingForwardSnapshot(
            predicted_state.inlane_state.inlane_param.relative_heading_rad)) {
      return false;
    }
  }
  return has_encroached_snapshot;
}

bool IsDrivingForwardOnEgoLane(
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lat_gap, int predicted_trajectory_id,
    bool is_stationary) {
  // No need to consider static agents.
  if (is_stationary) {
    return IsInEgoLaneSnapshot(required_lat_gap,
                               object_occupancy_state.current_snapshot_info()
                                   .object_occupancy_param()
                                   .cross_track_encroachment_m);
  }
  if (object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }
  bool has_encroached_snapshot = false;
  for (const auto& predicted_state : predicted_states) {
    if (!IsInEgoLaneSnapshot(required_lat_gap,
                             predicted_state.object_occupancy_param()
                                 .cross_track_encroachment_m)) {
      continue;
    }
    has_encroached_snapshot = true;
    if (!IsDrivingForwardSnapshot(
            predicted_state.object_occupancy_param().relative_heading_rad)) {
      return false;
    }
  }
  return has_encroached_snapshot;
}

bool IsInCurrentLaneAgent(const AgentInLaneStates& agent_state,
                          const RequiredLateralGap& required_lat_gap,
                          int predicted_trajectory_id, bool is_stationary) {
  // Only consider the tracked state for static agents.
  if (is_stationary) {
    return IsInEgoLaneSnapshot(
        required_lat_gap,
        agent_state.tracked_state.inlane_param.cross_track_encroachment_m);
  }
  if (agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }
  return std::any_of(predicted_states.begin(), predicted_states.end(),
                     [&required_lat_gap](const auto& predicted_state) {
                       return IsInEgoLaneSnapshot(
                           required_lat_gap,
                           predicted_state.inlane_state.inlane_param
                               .cross_track_encroachment_m);
                     });
}

bool IsInCurrentLaneAgent(const ObjectOccupancyState& object_occupancy_state,
                          const RequiredLateralGap& required_lat_gap,
                          int predicted_trajectory_id, bool is_stationary) {
  // Only consider the tracked state for static agents.
  if (is_stationary) {
    return IsInEgoLaneSnapshot(required_lat_gap,
                               object_occupancy_state.current_snapshot_info()
                                   .object_occupancy_param()
                                   .cross_track_encroachment_m);
  }
  if (object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }
  return std::any_of(predicted_states.begin(), predicted_states.end(),
                     [&required_lat_gap](const auto& predicted_state) {
                       return IsInEgoLaneSnapshot(
                           required_lat_gap,
                           predicted_state.object_occupancy_param()
                               .cross_track_encroachment_m);
                     });
}

bool IsEmergencyAvoidanceAgent(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    int predicted_trajectory_id, bool is_stationary,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  // Not consider rear agent.
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  // Only consider the tracked state for static agents.
  const int64_t max_considered_timestamp =
      ego_param.plan_start_timestamp_ms +
      math::Sec2Ms(kBehindAgentIgnoreTtcForLaneFollowInSec);
  if (is_stationary) {
    return IsEmergencyAvoidanceSnapshot(
        ego_param, emergency_brake_speed_profile, lower_bound_speed_profile,
        agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
        agent_state.tracked_state.inlane_param.full_body_end_arclength_m,
        math::Ms2Sec(max_considered_timestamp), is_stationary);
  }

  if (agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  for (const auto& predicted_state : predicted_states) {
    // Only consider part of prediction.
    if (predicted_state.timestamp > max_considered_timestamp) {
      break;
    }
    if (IsEmergencyAvoidanceSnapshot(
            ego_param, emergency_brake_speed_profile, lower_bound_speed_profile,
            predicted_state.inlane_state.inlane_param
                .full_body_start_arclength_m,
            predicted_state.inlane_state.inlane_param.full_body_end_arclength_m,
            math::Ms2Sec(predicted_state.timestamp), is_stationary)) {
      return true;
    }
  }
  return false;
}

bool IsEmergencyAvoidanceAgent(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    int predicted_trajectory_id, bool is_stationary,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const pb::TrajectoryReasoningInfo& reasoning_info) {
  // Only consider the tracked state for static agents.
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  const int64_t max_considered_timestamp =
      ego_param.plan_start_timestamp_ms +
      math::Sec2Ms(kBehindAgentIgnoreTtcForLaneFollowInSec);
  if (is_stationary) {
    return IsEmergencyAvoidanceSnapshot(
        ego_param, emergency_brake_speed_profile, lower_bound_speed_profile,
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_start_arclength_m,
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_end_arclength_m,
        math::Ms2Sec(max_considered_timestamp), is_stationary);
  }

  if (object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  for (const auto& predicted_state : predicted_states) {
    // Only consider part of prediction.
    if (predicted_state.timestamp() > max_considered_timestamp) {
      break;
    }
    if (IsEmergencyAvoidanceSnapshot(
            ego_param, emergency_brake_speed_profile, lower_bound_speed_profile,
            predicted_state.object_occupancy_param()
                .full_body_start_arclength_m,
            predicted_state.object_occupancy_param().full_body_end_arclength_m,
            math::Ms2Sec(predicted_state.timestamp()), is_stationary)) {
      return true;
    }
  }
  return false;
}

bool IsEmergencyAvoidanceCz(const EgoInLaneParams& ego_param,
                            const ConstructionZoneInLaneState& cz_inlane_state,
                            const speed::Profile& emergency_brake_speed_profile,
                            const speed::Profile& lower_bound_speed_profile,
                            const pb::TrajectoryReasoningInfo& reasoning_info) {
  // Not consider rear agent.
  if (reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  // Only consider the tracked state for static agents.
  const int64_t max_considered_timestamp =
      ego_param.plan_start_timestamp_ms +
      math::Sec2Ms(kBehindAgentIgnoreTtcForLaneFollowInSec);
  return IsEmergencyAvoidanceSnapshot(
      ego_param, emergency_brake_speed_profile, lower_bound_speed_profile,
      cz_inlane_state.start_arclength_m, cz_inlane_state.end_arclength_m,
      math::Ms2Sec(max_considered_timestamp), /*is_stationary=*/true);
}

bool IsAbnormalInlaneParamAgent(const AgentInLaneStates& agent_state,
                                int predicted_trajectory_id,
                                bool is_stationary) {
  // Only consider the tracked state for static agents.
  if (is_stationary) {
    return IsAbnormalInlaneParamSnapshot(
        agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
        agent_state.tracked_state.inlane_param.full_body_end_arclength_m);
  }

  if (agent_state.predicted_trajectories.empty()) {
    return false;
  }
  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }
  return std::any_of(predicted_states.begin(), predicted_states.end(),
                     [](const auto& predicted_state) {
                       return IsAbnormalInlaneParamSnapshot(
                           predicted_state.inlane_state.inlane_param
                               .full_body_start_arclength_m,
                           predicted_state.inlane_state.inlane_param
                               .full_body_end_arclength_m);
                     });
}

bool IsAbnormalObjectOccupancyParamAgent(
    const ObjectOccupancyState& object_occupancy_state,
    int predicted_trajectory_id, bool is_stationary) {
  // Only consider the tracked state for static agents.
  if (is_stationary) {
    return IsAbnormalInlaneParamSnapshot(
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_start_arclength_m,
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .full_body_end_arclength_m);
  }

  if (object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }
  return std::any_of(
      predicted_states.begin(), predicted_states.end(),
      [](const auto& predicted_state) {
        return IsAbnormalInlaneParamSnapshot(
            predicted_state.object_occupancy_param()
                .full_body_start_arclength_m,
            predicted_state.object_occupancy_param().full_body_end_arclength_m);
      });
}

bool IsLateralBesideEgo(const EgoInLaneParams& ego_param,
                        double full_body_start_lateral_distance_m,
                        double full_body_end_lateral_distance_m) {
  const double agent_center_lateral_distance =
      0.5 *
      (full_body_start_lateral_distance_m + full_body_end_lateral_distance_m);
  const math::Range1d ego_lateral_range =
      path::GetEgoLateralDistanceRange(ego_param);
  return agent_center_lateral_distance > ego_lateral_range.end_pos ||
         agent_center_lateral_distance < ego_lateral_range.start_pos;
}

bool IsBesideEgo(const EgoInLaneParams& ego_param,
                 double full_body_start_arclength_m,
                 double full_body_end_arclength_m,
                 double full_body_start_lateral_distance_m,
                 double full_body_end_lateral_distance_m,
                 const speed::Profile& lower_bound_speed_profile) {
  const math::Range1d agent_arclength_range(full_body_start_arclength_m,
                                            full_body_end_arclength_m);
  const math::Range1d ego_arclength_range =
      GetEgoBufferedLongitudinalArclengthRange(ego_param,
                                               lower_bound_speed_profile);
  const bool has_lon_overlap =
      math::IsValidRange(agent_arclength_range) &&
      math::AreRangesOverlapping(ego_arclength_range, agent_arclength_range);

  return has_lon_overlap &&
         IsLateralBesideEgo(ego_param, full_body_start_lateral_distance_m,
                            full_body_end_lateral_distance_m);
}

math::pb::Side GetAgentTrackedSideToEgo(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const speed::Profile& lower_bound_speed_profile,
    const int predicted_trajectory_id, const bool is_stationary,
    const bool is_oncoming) {
  const double time_horizon_seconds =
      is_oncoming ? KOncomingAgentMaxConsiderationHorizonInSec
                  : kBehindAgentIgnoreTtcForLaneFollowInSec;

  const math::Range1d agent_arclength_range =
      GetAgentArclengthRangeWithinTimeHorizon(
          agent_state, predicted_trajectory_id,
          /*max_timestamp=*/ego_param.plan_start_timestamp_ms +
              math::Sec2Ms(time_horizon_seconds),
          is_stationary);
  const math::Range1d ego_buffered_arclength_range =
      GetEgoBufferedLongitudinalArclengthRange(ego_param,
                                               lower_bound_speed_profile);

  const bool has_lon_overlap =
      math::IsValidRange(agent_arclength_range) &&
      math::AreRangesOverlapping(ego_buffered_arclength_range,
                                 agent_arclength_range);
  if (!has_lon_overlap) {
    return math::pb::kOn;
  }

  return path::GetAgentLateralTrackedSideToEgo(
      ego_param,
      math::Range1d(agent_state.tracked_state.inlane_param
                        .full_body_start_lateral_distance_m,
                    agent_state.tracked_state.inlane_param
                        .full_body_end_lateral_distance_m),
      /*extended_buffer=*/0.0);
}

math::pb::Side GetAgentTrackedSideToEgo(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const speed::Profile& lower_bound_speed_profile,
    const int predicted_trajectory_id, const bool is_stationary,
    const bool is_oncoming) {
  const double time_horizon_seconds =
      is_oncoming ? KOncomingAgentMaxConsiderationHorizonInSec
                  : kBehindAgentIgnoreTtcForLaneFollowInSec;

  const math::Range1d agent_arclength_range =
      GetAgentArclengthRangeWithinTimeHorizon(
          object_occupancy_state, predicted_trajectory_id,
          /*max_timestamp=*/ego_param.plan_start_timestamp_ms +
              math::Sec2Ms(time_horizon_seconds),
          is_stationary);
  const math::Range1d ego_buffered_arclength_range =
      GetEgoBufferedLongitudinalArclengthRange(ego_param,
                                               lower_bound_speed_profile);

  const bool has_lon_overlap =
      math::IsValidRange(agent_arclength_range) &&
      math::AreRangesOverlapping(ego_buffered_arclength_range,
                                 agent_arclength_range);
  if (!has_lon_overlap) {
    return math::pb::kOn;
  }

  return path::GetAgentLateralTrackedSideToEgo(
      ego_param,
      math::Range1d(object_occupancy_state.current_snapshot_info()
                        .object_occupancy_param()
                        .full_body_start_lateral_distance_m,
                    object_occupancy_state.current_snapshot_info()
                        .object_occupancy_param()
                        .full_body_end_lateral_distance_m),
      /*extended_buffer=*/0.0);
}

math::pb::Side GetCzTrackedSideToEgo(
    const EgoInLaneParams& ego_param,
    const ConstructionZoneInLaneState& cz_inlane_state,
    const speed::Profile& lower_bound_speed_profile) {
  const math::Range1d agent_arclength_range = math::Range1d(
      cz_inlane_state.start_arclength_m, cz_inlane_state.end_arclength_m);
  const math::Range1d ego_buffered_arclength_range =
      GetEgoBufferedLongitudinalArclengthRange(ego_param,
                                               lower_bound_speed_profile);
  const bool has_lon_overlap =
      math::IsValidRange(agent_arclength_range) &&
      math::AreRangesOverlapping(ego_buffered_arclength_range,
                                 agent_arclength_range);
  if (!has_lon_overlap) {
    return math::pb::kOn;
  }

  return path::GetAgentLateralTrackedSideToEgo(
      ego_param,
      math::Range1d(cz_inlane_state.start_lateral_distance_m,
                    cz_inlane_state.end_lateral_distance_m),
      /*extended_buffer=*/0.0);
}

// Get oriented side to ego center line based on ego heading.
math::pb::Side GetOrientedSideToEgoCenterline(
    const math::geometry::PolygonWithCache2d& agent_contour,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const double extend_front_buffer) {
  // Construct extended polyline started bounding box center point.
  const double extend_length =
      extend_front_buffer + 0.5 * ego_bounding_box.length();
  const double extend_x = ego_bounding_box.center().x() +
                          extend_length * std::cos(ego_bounding_box.heading());
  const double extend_y = ego_bounding_box.center().y() +
                          extend_length * std::sin(ego_bounding_box.heading());
  math::geometry::PolylineCurve2d ego_box_center_line =
      math::geometry::PolylineCurve2d(
          {ego_bounding_box.center(), {extend_x, extend_y}});
  // If agent box intersect with ego box, return kOn;
  const std::vector<math::geometry::Point2d> intersected_points =
      math::geometry::Intersection<std::vector<math::geometry::Point2d>>(
          ego_box_center_line, agent_contour);
  if (!intersected_points.empty()) {
    return math::pb::kOn;
  }
  // Compute the side relative to the ego_box_center_line at any point of agent
  // box.
  math::ProximityQueryInfo proximity_info = ego_box_center_line.GetProximity(
      agent_contour.points().front(), math::pb::UseExtensionFlag::kForbid);

  return proximity_info.signed_dist > 0 ? math::pb::kLeft : math::pb::kRight;
}

// Get oriented SLBoundar to ego center point.
// refer to: https://blog.csdn.net/m0_71905144/article/details/132206373
pb::TrajectoryReasoningInfo::OrientedType GetAgentTrackedOrientedTypeToEge(
    const math::geometry::PolygonWithCache2d& agent_contour,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const double req_lat_gap = 0.0) {
  // project polygon to (0, 0) -> direction to find l_min, l_max, s_min, s_max
  double start_s = std::numeric_limits<double>::max();
  double end_s = std::numeric_limits<double>::lowest();
  double start_l = std::numeric_limits<double>::max();
  double end_l = std::numeric_limits<double>::lowest();
  const double ego_half_length = 0.5 * ego_bounding_box.length();
  const double ego_half_width = 0.5 * ego_bounding_box.width();
  for (const auto& point : agent_contour.points()) {
    const double s = (point.x() - ego_bounding_box.center().x()) *
                         ego_bounding_box.cos_heading() +
                     (point.y() - ego_bounding_box.center().y()) *
                         ego_bounding_box.sin_heading();
    const double l = (point.y() - ego_bounding_box.center().y()) *
                         ego_bounding_box.cos_heading() -
                     (point.x() - ego_bounding_box.center().x()) *
                         ego_bounding_box.sin_heading();
    start_s = std::min(start_s, s - req_lat_gap - ego_half_length);
    end_s = std::max(end_s, s + req_lat_gap + ego_half_length);
    start_l = std::min(start_l, l - req_lat_gap - ego_half_width);
    end_l = std::max(end_l, l + req_lat_gap + ego_half_width);
  }
  DCHECK_LT(start_s, end_s);
  DCHECK_LT(start_l, end_l);
  if (start_s > 0.0 && math::IsInRange(0.0, start_l, end_l)) {
    return pb::TrajectoryReasoningInfo::FACING_FORWARD;
  } else if (end_s < 0.0 && math::IsInRange(0.0, start_l, end_l)) {
    return pb::TrajectoryReasoningInfo::FACING_BACKWARD;
  } else if (start_l > 0.0 && math::IsInRange(0.0, start_s, end_s)) {
    return pb::TrajectoryReasoningInfo::FACING_LEFT;
  } else if (end_l < 0.0 && math::IsInRange(0.0, start_s, end_s)) {
    return pb::TrajectoryReasoningInfo::FACING_RIGHT;
  } else if (start_s > 0.0 && start_l > 0.0) {
    return pb::TrajectoryReasoningInfo::LEFT_FRONT;
  } else if (start_s > 0.0 && end_l < 0.0) {
    return pb::TrajectoryReasoningInfo::RIGHT_FRONT;
  } else if (end_s < 0.0 && start_l > 0.0) {
    return pb::TrajectoryReasoningInfo::LEFT_REAR;
  } else if (end_s < 0.0 && end_l < 0.0) {
    return pb::TrajectoryReasoningInfo::RIGHT_REAR;
  }
  return pb::TrajectoryReasoningInfo::UNKNOWN;
}

bool IsCutinTrajectory(const AgentInLaneStates& agent_state,
                       const RequiredLateralGap& required_lat_gap,
                       const LateralBlockingStateMetaData& blocking_data,
                       const pb::TrajectoryReasoningInfo& reasoning_info) {
  if (!reasoning_info.is_agent_tracking_possible_cut_in()) {
    return false;
  }
  // No need consider the crossing and oncoming agents.
  if (reasoning_info.is_crossing() || reasoning_info.is_oncoming()) {
    return false;
  }

  // If no hard blocking snapshot, return false.
  if (blocking_data.global_blocking_state !=
      pb::TrajectoryState::HARD_BLOCKING) {
    return false;
  }

  // If tracked blocking state is soft blocking and in ego lane, return true.
  if (IsInEgoLaneSnapshot(
          required_lat_gap,
          agent_state.tracked_state.inlane_param.cross_track_encroachment_m) &&
      path::GetEffectiveLateralBlockingState(
          blocking_data.tracked_blocking_info) ==
          pb::LateralBlockingState::SOFT_BLOCKING) {
    return true;
  }

  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             blocking_data.trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }

  DCHECK_EQ(predicted_states.size(),
            blocking_data.trajectory_blocking_infos.size());
  // begin->end to find the smallest start_pos in ego lane.
  const auto start_iter = std::find_if(
      predicted_states.begin(), predicted_states.end(),
      [&required_lat_gap](const auto& predicted_state) {
        return IsInEgoLaneSnapshot(required_lat_gap,
                                   predicted_state.inlane_state.inlane_param
                                       .cross_track_encroachment_m);
      });
  // Returns false if the first ego lane snapshot is HARD_BLOCKING.
  if (start_iter == predicted_states.end() ||
      blocking_data
              .trajectory_blocking_infos[start_iter - predicted_states.begin()]
              .blocking_state() == pb::LateralBlockingState::HARD_BLOCKING) {
    return false;
  }

  return true;
}

bool IsCutinTrajectory(const ObjectOccupancyState& object_occupancy_state,
                       const RequiredLateralGap& required_lat_gap,
                       const LateralBlockingStateMetaData& blocking_data,
                       const pb::TrajectoryReasoningInfo& reasoning_info) {
  if (!reasoning_info.is_agent_tracking_possible_cut_in()) {
    return false;
  }
  // No need consider the crossing and oncoming agents.
  if (reasoning_info.is_crossing() || reasoning_info.is_oncoming()) {
    return false;
  }

  // If no hard blocking snapshot, return false.
  if (blocking_data.global_blocking_state !=
      pb::TrajectoryState::HARD_BLOCKING) {
    return false;
  }

  // If tracked blocking state is soft blocking and in ego lane, return true.
  if (IsInEgoLaneSnapshot(required_lat_gap,
                          object_occupancy_state.current_snapshot_info()
                              .object_occupancy_param()
                              .cross_track_encroachment_m) &&
      path::GetEffectiveLateralBlockingState(
          blocking_data.tracked_blocking_info) ==
          pb::LateralBlockingState::SOFT_BLOCKING) {
    return true;
  }
  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          blocking_data.trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }

  DCHECK_EQ(predicted_states.size(),
            blocking_data.trajectory_blocking_infos.size());
  // begin->end to find the smallest start_pos in ego lane.
  const auto start_iter = std::find_if(
      predicted_states.begin(), predicted_states.end(),
      [&required_lat_gap](const auto& predicted_state) {
        return IsInEgoLaneSnapshot(required_lat_gap,
                                   predicted_state.object_occupancy_param()
                                       .cross_track_encroachment_m);
      });
  // Returns false if the first ego lane snapshot is HARD_BLOCKING.
  if (start_iter == predicted_states.end() ||
      blocking_data
              .trajectory_blocking_infos[start_iter - predicted_states.begin()]
              .blocking_state() == pb::LateralBlockingState::HARD_BLOCKING) {
    return false;
  }

  return true;
}

bool IsAgentTrackingPossibleCutin(
    const AgentInLaneStates& agent_state,
    const LateralBlockingStateMetaData& blocking_data,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const pb::BehaviorType behavior_type) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      agent_state.predicted_trajectories.empty()) {
    return false;
  }
  // Will not consider a behind rear bumper agent for cut-in in Lane Keep.
  if (behavior_type == pb::BehaviorType::LANE_KEEP &&
      reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }
  // if the agent is not beside of ego vehicle, return false.
  if (!reasoning_info.is_beside_ego()) {
    return false;
  }
  // If the traked blocking state is hard return
  // false.
  if (path::GetEffectiveLateralBlockingState(
          blocking_data.tracked_blocking_info) ==
      pb::LateralBlockingState::HARD_BLOCKING) {
    return false;
  }
  return true;
}

bool IsAgentTrackingPossibleCutin(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_data,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const pb::BehaviorType behavior_type) {
  // No need to consider static agents.
  if (reasoning_info.is_stationary() ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }
  // Will not consider a behind rear bumper agent for cut-in in Lane Keep.
  if (behavior_type == pb::BehaviorType::LANE_KEEP &&
      reasoning_info.is_agent_behind_rear_bumper()) {
    return false;
  }

  // if the agent is not beside of ego vehicle, return false.
  if (!reasoning_info.is_beside_ego()) {
    return false;
  }
  // If the traked blocking state is hard return
  // false.
  if (path::GetEffectiveLateralBlockingState(
          blocking_data.tracked_blocking_info) ==
      pb::LateralBlockingState::HARD_BLOCKING) {
    return false;
  }
  return true;
}

bool IsLeadAndMergeTrajectory(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const RequiredLateralGap& required_lat_gap,
    const LateralBlockingStateMetaData& blocking_data,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const bool is_ego_near_merge,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // Cut-in will be treated as merge agent.
  if (reasoning_info.is_cut_in()) {
    return true;
  }

  // No need consider the static agent.
  if (reasoning_info.is_stationary() ||
      agent_state.predicted_trajectories.empty()) {
    return false;
  }

  // No need consider the crossing and oncoming agents.
  if (reasoning_info.is_crossing() || reasoning_info.is_oncoming()) {
    return false;
  }

  // if the agent is not in front of ego vehicle, return false except for
  // cross lane jump in pullover and in merge situation. In such behavior, since
  // Ego has intentional lateral movement to the right side so the side agent
  // will also have merge interaction with Ego even if they are not cut in.
  if (!reasoning_info.is_agent_in_front_of_front_bumper() &&
      pullover_jump_in_type !=
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN &&
      !is_ego_near_merge) {
    return false;
  }

  // If no hard blocking snapshot, return false except for cross lane jump in
  // pullover. In such pullover behavior, since Ego has intentional lateral
  // movement to the right side and the lane geometry is significantly larger
  // than usual, we shouldn't filter out only based on hard blocking state.
  if (blocking_data.global_blocking_state !=
          pb::TrajectoryState::HARD_BLOCKING &&
      pullover_jump_in_type !=
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    return false;
  }

  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             blocking_data.trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }

  if (std::any_of(
          predicted_states.begin(), predicted_states.end(),
          [](const auto& predicted_state) {
            return IsOncomingSnapshot(
                predicted_state.inlane_state.inlane_param.relative_heading_rad);
          })) {
    // If agent has oncoming snapshot, it is not lead and merge.
    return false;
  }

  if (pullover_jump_in_type ==
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    // Cross lane jump in pullover behavior is special because Ego will
    // intentionally pull to the right side, hence will have a merge behavior
    // with agent from right side which is driving straight forward.
    if (reasoning_info.is_agent_behind_rear_bumper()) {
      // Don't consider behind agent as lead and merge in pullover.
      return false;
    }
    const auto& agent_inlane_param = agent_state.tracked_state.inlane_param;
    const double agent_relative_lateral_distance_to_line_center =
        agent_inlane_param.center_line_side == math::pb::kRight
            ? -agent_inlane_param.center_line_clearance_m
            : agent_inlane_param.center_line_clearance_m;

    const bool is_agent_right_side_to_ego =
        (agent_relative_lateral_distance_to_line_center <
         ego_param.relative_lateral_distance_to_line_center);
    if (is_agent_right_side_to_ego) {
      // If the agent is at the right side and not behind Ego, consider it
      // as a lead and merge trajectory.
      return true;
    }
  }

  bool has_hard_blocking_snapshot =
      std::any_of(predicted_states.begin(), predicted_states.end(),
                  [&ego_param, &required_lat_gap](const auto& predicted_state) {
                    return IsHardBlockingSnapshot(
                        ego_param, predicted_state.inlane_state.inlane_param,
                        required_lat_gap);
                  });
  return has_hard_blocking_snapshot;
}

bool IsLeadAndMergeTrajectory(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lat_gap,
    const LateralBlockingStateMetaData& blocking_data,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const bool is_ego_near_merge,
    const planner::pb::PullOverJumpInType pullover_jump_in_type) {
  // Cut-in will be treated as merge agent.
  if (reasoning_info.is_cut_in()) {
    return true;
  }

  // No need consider the static agent.
  if (reasoning_info.is_stationary() ||
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()) {
    return false;
  }

  // No need consider the crossing and oncoming agents.
  if (reasoning_info.is_crossing() || reasoning_info.is_oncoming()) {
    return false;
  }

  // if the agent is not in front of ego vehicle, return false except for
  // cross lane jump in pullover and in merge situation. In such behavior, since
  // Ego has intentional lateral movement to the right side so the side agent
  // will also have merge interaction with Ego even if they are not cut in.
  if (!reasoning_info.is_agent_in_front_of_front_bumper() &&
      pullover_jump_in_type !=
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN &&
      !is_ego_near_merge) {
    return false;
  }

  // If no hard blocking snapshot, return false except for cross lane jump in
  // pullover. In such pullover behavior, since Ego has intentional lateral
  // movement to the right side and the lane geometry is significantly larger
  // than usual, we shouldn't filter out only based on hard blocking state.
  if (blocking_data.global_blocking_state !=
          pb::TrajectoryState::HARD_BLOCKING &&
      pullover_jump_in_type !=
          planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    return false;
  }

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          blocking_data.trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }

  if (std::any_of(
          predicted_states.begin(), predicted_states.end(),
          [](const auto& predicted_state) {
            return IsOncomingSnapshot(
                predicted_state.object_occupancy_param().relative_heading_rad);
          })) {
    // If agent has oncoming snapshot, it is not lead and merge.
    return false;
  }

  if (pullover_jump_in_type ==
      planner::pb::PullOverJumpInType::CROSS_LANE_JUMP_IN) {
    // Cross lane jump in pullover behavior is special because Ego will
    // intentionally pull to the right side, hence will have a merge behavior
    // with agent from right side which is driving straight forward.
    if (reasoning_info.is_agent_behind_rear_bumper()) {
      // Don't consider behind agent as lead and merge in pullover.
      return false;
    }
    const auto& object_occupancy_param =
        object_occupancy_state.current_snapshot_info().object_occupancy_param();
    const double agent_relative_lateral_distance_to_line_center =
        object_occupancy_param.center_line_side == math::pb::kRight
            ? -object_occupancy_param.center_line_clearance_m
            : object_occupancy_param.center_line_clearance_m;

    const bool is_agent_right_side_to_ego =
        (agent_relative_lateral_distance_to_line_center <
         ego_param.relative_lateral_distance_to_line_center);
    if (is_agent_right_side_to_ego) {
      // If the agent is at the right side and not behind Ego, consider it
      // as a lead and merge trajectory.
      return true;
    }
  }

  bool has_hard_blocking_snapshot =
      std::any_of(predicted_states.begin(), predicted_states.end(),
                  [&ego_param, &required_lat_gap](const auto& predicted_state) {
                    return IsHardBlockingSnapshot(
                        ego_param, predicted_state.object_occupancy_param(),
                        required_lat_gap);
                  });

  return has_hard_blocking_snapshot;
}

bool IsCyclistOvertaking(const EgoInLaneParams& ego_param,
                         const AgentInLaneStates& agent_state,
                         const RequiredLateralGap& required_lat_gap,
                         const PlannerObject& planner_object,
                         const pb::TrajectoryReasoningInfo& reasoning_info,
                         const int predicted_trajectory_id) {
  // Currently only processing CYCLIST. In the future, path reasoning
  // will be expanded to more types after motivation cases are available.
  if (agent_state.agent_metadata.agent_type !=
      voy::perception::ObjectType::CYCLIST) {
    return false;
  }

  // Returns false if agent is overtaked by ego.
  if (planner_object.is_overtaken()) {
    return false;
  }

  // Return true if the agent overtaking ego from behind ego to beside ego.
  if (planner_object.is_overtaking()) {
    return true;
  }

  // Ignores cyclists that are kOn side to ego.
  const math::pb::Side neg_buffered_tracked_side =
      path::GetAgentLateralTrackedSideToEgo(
          ego_param,
          math::Range1d(agent_state.tracked_state.inlane_param
                            .full_body_start_lateral_distance_m,
                        agent_state.tracked_state.inlane_param
                            .full_body_end_lateral_distance_m),
          /*extended_buffer=*/-0.2);
  if (neg_buffered_tracked_side == math::pb::kOn) {
    return false;
  }

  // Returns true if the ego undergoes an emergency braking and the cyclist
  // behind it cannot come to a comfortable stop while being relatively
  // close laterally.
  const auto& agent_inlane_param = agent_state.tracked_state.inlane_param;
  if (reasoning_info.is_agent_behind_rear_bumper() &&
      agent_inlane_param.along_track_speed_mps > ego_param.speed_mps) {
    // Agent is not enough to brake stop with comfortable deceleration.
    constexpr double kComfortableDecelerationForCyclistInMss = 3.0;
    constexpr double kEmergencyBrakeDecelerationForVehicleInMss = 5.0;
    const double lon_gap_dis_in_m =
        ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m -
        agent_inlane_param.full_body_end_arclength_m;
    const bool can_cyclist_stop_for_ego_brake =
        agent_inlane_param.along_track_speed_mps *
            agent_inlane_param.along_track_speed_mps /
            (2 * kComfortableDecelerationForCyclistInMss) <=
        ego_param.speed_mps * ego_param.speed_mps /
                (2 * kEmergencyBrakeDecelerationForVehicleInMss) +
            lon_gap_dis_in_m;
    if (can_cyclist_stop_for_ego_brake) {
      return false;
    }

    // Lateral distance is small when the agent catches up with the ego.
    // Here, it is assumed that the ego is traveling along the reference
    // path and the lateral range does not change.
    const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
        FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                               predicted_trajectory_id)
            .predicted_states;
    DCHECK(!predicted_states.empty());

    // Locates the snapshot where the agent catches up with the ego.
    const int64_t timestamp_of_encounter =
        ego_param.plan_start_timestamp_ms +
        math::Sec2Ms(
            lon_gap_dis_in_m /
            (agent_inlane_param.along_track_speed_mps - ego_param.speed_mps));

    const auto& iter = std::lower_bound(
        predicted_states.begin(), predicted_states.end(),
        timestamp_of_encounter,
        [](const StampedAgentSnapshotInLaneState& predicted_state,
           int64_t timestamp) {
          return predicted_state.timestamp < timestamp;
        });
    if (iter == predicted_states.end()) {
      return false;
    }

    // Based on the relative side to the ego, determine if the snapshot where
    // the agent catches up with the ego is too close in distance.
    const math::Range1d ego_lateral_range =
        path::GetEgoLateralDistanceRange(ego_param);
    const math::pb::Side snapshot_side_to_ego =
        path::GetAgentLateralTrackedSideToEgo(
            ego_param,
            math::Range1d(iter->inlane_state.inlane_param
                              .full_body_start_lateral_distance_m,
                          iter->inlane_state.inlane_param
                              .full_body_end_lateral_distance_m),
            /*extended_buffer=*/0.0);
    switch (snapshot_side_to_ego) {
      case math::pb::kLeft:
        DCHECK_GT(
            iter->inlane_state.inlane_param.full_body_start_lateral_distance_m,
            ego_lateral_range.end_pos);
        return iter->inlane_state.inlane_param
                       .full_body_start_lateral_distance_m -
                   ego_lateral_range.end_pos <
               required_lat_gap.comfort_required_lateral_gap;
      case math::pb::kRight:
        DCHECK_LT(
            iter->inlane_state.inlane_param.full_body_end_lateral_distance_m,
            ego_lateral_range.start_pos);
        return ego_lateral_range.start_pos -
                   iter->inlane_state.inlane_param
                       .full_body_end_lateral_distance_m <
               required_lat_gap.comfort_required_lateral_gap;
      case math::pb::kOn:
        // In real-world scenarios, overtaking cyclists will not collide with
        // the ego vehicle; the term 'kOn' here indicates that the predicted
        // trajectory of the overtaking cyclist is in close proximity to the ego
        // vehicle.
        return true;
      default:
        break;
    }
  }

  // Returns true if agent overtakes the ego front bumper within comfort gap
  // distance. Note: this approach ensures that the decision-making process does
  // not experience sudden changes in the vicinity of the ego vehicle.
  return reasoning_info.is_agent_within_comfort() &&
         agent_inlane_param.along_track_speed_mps > ego_param.speed_mps;
}

bool IsLaneChangeTargetRegionAgent(
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const int64_t agent_id) {
  if (!lane_change_execution_info.has_value()) {
    return false;
  }
  const std::vector<int64_t>& lane_change_agent_ids =
      lane_change_execution_info->lane_change_target_lane_agent_ids;
  return std::any_of(lane_change_agent_ids.begin(), lane_change_agent_ids.end(),
                     [&agent_id](const auto& id) { return id == agent_id; });
}

bool IsLaneChangeIgnoreAgentInPostReason(
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const int64_t agent_id) {
  if (!lane_change_execution_info.has_value()) {
    return false;
  }
  const std::vector<int64_t>& lane_change_ignore_agent_ids =
      lane_change_execution_info->lane_change_ignore_ids_in_post_reason;
  return std::any_of(lane_change_ignore_agent_ids.begin(),
                     lane_change_ignore_agent_ids.end(),
                     [&agent_id](const auto& id) { return id == agent_id; });
}

bool IsInterestedAgentDuringLaneChangeOrLaneChangeAbort(
    const bool is_lane_change_target_lane_agent, pb::BehaviorType behavior_type,
    const bool is_source_lane_agent_during_lane_change_abort) {
  return (is_lane_change_target_lane_agent &&
          behavior_type == pb::CROSS_LANE) ||
         is_source_lane_agent_during_lane_change_abort;
}

// Check if the nudging behavior for the agent is disabled  during lane change.
bool IsLaneChangeIgnoreAgent(
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const int64_t agent_id) {
  if (!lane_change_execution_info.has_value()) {
    return false;
  }
  if (IsLaneChangeIgnoreAgentInPostReason(lane_change_execution_info,
                                          agent_id)) {
    return true;
  }

  const std::vector<int64_t>& lane_change_ignore_ids_before_path_reason =
      lane_change_execution_info->lane_change_ignore_ids_before_path_reason;
  return std::any_of(lane_change_ignore_ids_before_path_reason.begin(),
                     lane_change_ignore_ids_before_path_reason.end(),
                     [&agent_id](const auto& id) { return id == agent_id; });
}

bool IsLaneChangeCrawlInterestedTargetRegionAgent(
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const int64_t agent_id, const bool is_parked_car,
    const bool has_start_to_move_trajectory, const double agent_speed) {
  if (!lane_change_execution_info.has_value() ||
      !lane_change_execution_info->is_lane_change_crawl || is_parked_car ||
      has_start_to_move_trajectory) {
    return false;
  }
  constexpr double kStaticAgentMaxSpeedInMps = 0.5;
  if (agent_speed > kStaticAgentMaxSpeedInMps) {
    return false;
  }
  return IsLaneChangeTargetRegionAgent(lane_change_execution_info, agent_id);
}

// We consider last iteration's LC state, and we also check lane keep behavior
// type for source lane agent.
bool IsSourceRegionAgentDuringLaneChangeAbort(
    const LaneChangeInfo& lane_change_info, const int64_t agent_id,
    pb::BehaviorType behavior_type) {
  if (behavior_type != pb::LANE_KEEP) {
    return false;
  }
  if (lane_change_info.last_selected_lane_change_state !=
          pb::LaneChangeState::LANE_CHANGE_STATE_IN_PROGRESS &&
      lane_change_info.last_selected_lane_change_state !=
          pb::LaneChangeState::LANE_CHANGE_STATE_ABORT) {
    return false;
  }
  const LaneChangeObjectInfoList* source_region_object_info_list =
      GetSourceRegionObjectInfoListPtr(lane_change_info);
  const std::vector<int64_t>& lane_change_source_lane_agent_ids =
      (source_region_object_info_list != nullptr)
          ? source_region_object_info_list->ids()
          : std::vector<int64_t>{};
  return std::any_of(lane_change_source_lane_agent_ids.begin(),
                     lane_change_source_lane_agent_ids.end(),
                     [&agent_id](const auto& id) { return id == agent_id; });
}

// When the ego deviates from the center line by more than 0.3m, we shouldn't
// ignore the agent in the source lane.
bool ShouldIgnoreSourceAgentDuringLaneChangeAbort(
    const EgoInLaneParams& ego_param, const LaneChangeInfo& lane_change_info,
    const int64_t agent_id, pb::BehaviorType behavior_type) {
  if (!IsSourceRegionAgentDuringLaneChangeAbort(lane_change_info, agent_id,
                                                behavior_type)) {
    return true;
  }
  constexpr double kMaxLateralDistanceToLineCenterForConsiderTailAgentInMeter =
      0.3;
  return std::abs(ego_param.relative_lateral_distance_to_line_center) <
         kMaxLateralDistanceToLineCenterForConsiderTailAgentInMeter;
}

bool IsCutAheadAgentDuringLaneChange(const LaneChangeInfo& lane_change_info,
                                     const int64_t agent_id,
                                     const bool is_agent_behind_rear_bumper,
                                     pb::BehaviorType behavior_type) {
  if (is_agent_behind_rear_bumper) {
    return false;
  }
  if (behavior_type != pb::CROSS_LANE) {
    return false;
  }
  const std::unordered_map<
      int64_t, std::unordered_map<std::string, pb::XRegionMotionInfo>>&
      object_xregion_motion_info_map =
          lane_change_info.lane_change_env_info.object_xregion_motion_info_map;
  std::unordered_map<int64_t,
                     std::unordered_map<std::string, pb::XRegionMotionInfo>>::
      const_iterator iter = object_xregion_motion_info_map.find(agent_id);
  if (iter == object_xregion_motion_info_map.end()) {
    return false;
  }
  const std::unordered_map<std::string, pb::XRegionMotionInfo>& cut_ahead_info =
      iter->second;

  if (cut_ahead_info.find(pb::XRegionMotionType_Name(
          planner::pb::XRegionMotionType::SOURCE_NEIGHBOR_TO_SOURCE)) !=
      cut_ahead_info.end()) {
    return true;
  }
  if (cut_ahead_info.find(pb::XRegionMotionType_Name(
          planner::pb::XRegionMotionType::SOURCE_TO_TARGET)) !=
      cut_ahead_info.end()) {
    return true;
  }
  return false;
}

bool IsCyclistOvertaking(const EgoInLaneParams& ego_param,
                         const ObjectOccupancyState& object_occupancy_state,
                         const RequiredLateralGap& required_lat_gap,
                         const pb::TrajectoryReasoningInfo& reasoning_info,
                         const int predicted_trajectory_id) {
  // Currently only processing CYCLIST. In the future, path reasoning
  // will be expanded to more types after motivation cases are available.
  if (object_occupancy_state.object_type() !=
      voy::perception::ObjectType::CYCLIST) {
    return false;
  }

  // Returns false if agent is overtaked by ego.
  if (object_occupancy_state.planner_object().is_overtaken()) {
    return false;
  }

  // Return true if the agent overtaking ego from behind ego to beside ego.
  if (object_occupancy_state.planner_object().is_overtaking()) {
    return true;
  }

  // Ignores cyclists that are kOn side to ego.
  const math::pb::Side neg_buffered_tracked_side =
      path::GetAgentLateralTrackedSideToEgo(
          ego_param,
          math::Range1d(object_occupancy_state.current_snapshot_info()
                            .object_occupancy_param()
                            .full_body_start_lateral_distance_m,
                        object_occupancy_state.current_snapshot_info()
                            .object_occupancy_param()
                            .full_body_end_lateral_distance_m),
          /*extended_buffer=*/-0.2);
  if (neg_buffered_tracked_side == math::pb::kOn) {
    return false;
  }

  // Returns true if the ego undergoes an emergency braking and the cyclist
  // behind it cannot come to a comfortable stop while being relatively
  // close laterally.
  const auto& object_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();
  if (reasoning_info.is_agent_behind_rear_bumper() &&
      object_occupancy_param.along_track_speed_mps > ego_param.speed_mps) {
    // Agent is not enough to brake stop with comfortable deceleration.
    constexpr double kComfortableDecelerationForCyclistInMss = 3.0;
    constexpr double kEmergencyBrakeDecelerationForVehicleInMss = 5.0;
    const double lon_gap_dis_in_m =
        ego_param.arclength_m - ego_param.rear_axle_to_rear_bumper_m -
        object_occupancy_param.full_body_end_arclength_m;
    const bool can_cyclist_stop_for_ego_brake =
        object_occupancy_param.along_track_speed_mps *
            object_occupancy_param.along_track_speed_mps /
            (2 * kComfortableDecelerationForCyclistInMss) <=
        ego_param.speed_mps * ego_param.speed_mps /
                (2 * kEmergencyBrakeDecelerationForVehicleInMss) +
            lon_gap_dis_in_m;
    if (can_cyclist_stop_for_ego_brake) {
      return false;
    }

    // Lateral distance is small when the agent catches up with the ego.
    // Here, it is assumed that the ego is traveling along the reference
    // path and the lateral range does not change.
    const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
        FIND_OR_DIE_WITH_PRINT(
            object_occupancy_state.predicted_trajectory_occupancy_states(),
            predicted_trajectory_id)
            .predicted_states();
    DCHECK(!predicted_states.empty());

    // Locates the snapshot where the agent catches up with the ego.
    const int64_t timestamp_of_encounter =
        ego_param.plan_start_timestamp_ms +
        math::Sec2Ms(lon_gap_dis_in_m /
                     (object_occupancy_param.along_track_speed_mps -
                      ego_param.speed_mps));

    const auto& iter =
        std::lower_bound(predicted_states.begin(), predicted_states.end(),
                         timestamp_of_encounter,
                         [](const ObjectStampedSnapshotInfo& predicted_state,
                            int64_t timestamp) {
                           return predicted_state.timestamp() < timestamp;
                         });
    if (iter == predicted_states.end()) {
      return false;
    }

    // Based on the relative side to the ego, determine if the snapshot where
    // the agent catches up with the ego is too close in distance.
    const math::Range1d ego_lateral_range =
        path::GetEgoLateralDistanceRange(ego_param);
    const math::pb::Side snapshot_side_to_ego =
        path::GetAgentLateralTrackedSideToEgo(
            ego_param,
            math::Range1d(iter->object_occupancy_param()
                              .full_body_start_lateral_distance_m,
                          iter->object_occupancy_param()
                              .full_body_end_lateral_distance_m),
            /*extended_buffer=*/0.0);
    switch (snapshot_side_to_ego) {
      case math::pb::kLeft:
        DCHECK_GT(
            iter->object_occupancy_param().full_body_start_lateral_distance_m,
            ego_lateral_range.end_pos);
        return iter->object_occupancy_param()
                       .full_body_start_lateral_distance_m -
                   ego_lateral_range.end_pos <
               required_lat_gap.comfort_required_lateral_gap;
        break;
      case math::pb::kRight:
        DCHECK_LT(
            iter->object_occupancy_param().full_body_end_lateral_distance_m,
            ego_lateral_range.start_pos);
        return ego_lateral_range.start_pos -
                   iter->object_occupancy_param()
                       .full_body_end_lateral_distance_m <
               required_lat_gap.comfort_required_lateral_gap;
      case math::pb::kOn:
        // In real-world scenarios, overtaking cyclists will not collide with
        // the ego vehicle; the term 'kOn' here indicates that the predicted
        // trajectory of the overtaking cyclist is in close proximity to the ego
        // vehicle.
        return true;
      default:
        break;
    }
  }

  // Returns true if agent overtakes the ego front bumper within comfort gap
  // distance. Note: this approach ensures that the decision-making process does
  // not experience sudden changes in the vicinity of the ego vehicle.
  return reasoning_info.is_agent_within_comfort() &&
         object_occupancy_param.along_track_speed_mps > ego_param.speed_mps;
}

// Determines if a vehicle is potentially reversing based on current prediction
// and history. Returns true if the vehicle is potentially reversing, including:
// - K-turning (three-point turn or similar maneuver)
// - Complete reversing (fully reversing without forward movement or steering)
// - Continuous backward movement (steadily moving backward)
// Returns false if the vehicle is not reversing or there is insufficient
// information to determine.

bool IsPotentiallyReversingVehicle(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const PlannerObject& planner_object,
    const PlannerObjectsHistory& planner_objects_history,
    const double relative_lane_heading) {
  // Only consider reversing vehicles.
  if (!planner_object.is_vehicle()) {
    return false;
  }

  // Check if the vehicle is reversing based on predictor output.
  if (reasoning_info.is_vehicle_with_complete_reversing_trajectory() ||
      reasoning_info.is_vehicle_with_kturn_trajectory()) {
    return true;
  }

  // Check if the vehicle is reversing based on motion heading.
  if ((reasoning_info.is_oncoming() || reasoning_info.is_crossing()) &&
      reasoning_info.is_motion_heading_opposite_to_box_heading()) {
    return true;
  }

  // Check if the vehicle is reversing based on historical information.
  if (!planner_objects_history.history().empty()) {
    const ObjectId object_id = planner_object.id();
    int count = std::count_if(
        planner_objects_history.history().begin(),
        planner_objects_history.history().end(),
        [&](const auto& history_info_map) {
          auto it = history_info_map->find(object_id);
          if (it == history_info_map->end()) {
            return false;
          }
          return std::abs(math::AngleDiff(it->second.box_heading(),
                                          it->second.heading())) >
                     kReversingAgentHeadingDiffThresholdInRad &&
                 !it->second.is_stationary();
        });
    if (static_cast<double>(count) >
        planner_objects_history.history().size() *
            kRatioThresholdToConsiderReversingInHistory) {
      return true;
    }
  }

  // Check if the vehicle is reversing based on proximity information.
  if (!planner_object.is_in_junction() && reasoning_info.is_in_current_lane() &&
      planner_object.is_stationary()) {
    auto box_relative_heading =
        reasoning_info.is_motion_heading_opposite_to_box_heading()
            ? math::NormalizeMinusPiToPi(relative_lane_heading + M_PI)
            : relative_lane_heading;
    // If the agent is on the left side of the lane and heading towards the left
    // with a large deviation ([pi/3, 2pi/3]), or on the right side of the lane
    // and heading towards the right with a large deviation ([-2pi/3, -pi/3]),
    // it is potentially reversing.
    if ((box_relative_heading >
             kMinReversingAgentCautiousHeadingDiffThresholdInRad &&
         box_relative_heading <
             kMaxReversingAgentCautiousHeadingDiffThresholdInRad &&
         reasoning_info.oriented_side_to_ego_centerline() == math::pb::kLeft) ||
        (box_relative_heading <
             -kMinReversingAgentCautiousHeadingDiffThresholdInRad &&
         box_relative_heading >
             -kMaxReversingAgentCautiousHeadingDiffThresholdInRad &&
         reasoning_info.oriented_side_to_ego_centerline() ==
             math::pb::kRight)) {
      return true;
    }
  }

  return false;
}

// Returns true if the agent has a large relative lane heading and is inside a
// buffer within the current lane.
bool HasLargeRelativeLaneHeading(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const double relative_heading_rad,
    const double cross_track_encroachment_m) {
  // Check if the agent is not oncoming and has a relative lane heading greater
  // than 45 degrees (pi/4 radians) but less than 180 degrees (pi radians).
  // Also, check if the agent is inside a buffer within the current lane, which
  // is defined as having a cross track encroachment greater than -1.5 meters
  // (negative value indicates being out of the lane).
  return !reasoning_info.is_oncoming() &&
         std::fabs(relative_heading_rad) > M_PI_4 &&
         std::fabs(relative_heading_rad) < M_PI &&
         cross_track_encroachment_m > -1.5;
}

// Check if the agent trajectory may cause harsh swerve of steering wheel.
bool IsPotentialHarshSwerveTrajectory(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const RequiredLateralGap& required_lat_gap,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    int predicted_trajectory_id) {
  // Ignore static agent.
  if (reasoning_info.is_stationary()) {
    return false;
  }

  // Ignore agent trajectory is not in current lane.
  if (!reasoning_info.is_in_current_lane()) {
    return false;
  }

  // Mainly process oncoming, crossing and cutin agent.
  if (!(reasoning_info.is_oncoming() || reasoning_info.is_crossing() ||
        reasoning_info.is_cut_in())) {
    return false;
  }

  const std::vector<StampedAgentSnapshotInLaneState>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories,
                             predicted_trajectory_id)
          .predicted_states;
  if (predicted_states.empty()) {
    return false;
  }

  // Checks all effective blocking sequences.
  for (const auto& blocking_sequence : blocking_sequences) {
    // Skips the non blocking sequence.
    if (blocking_sequence.blocking_state() ==
        pb::LateralBlockingState::NON_BLOCKING) {
      continue;
    }

    DCHECK_LT(blocking_sequence.range().start_index(),
              blocking_sequence.range().end_index());
    DCHECK_LT(blocking_sequence.range().end_index(), predicted_states.size());

    for (int index = blocking_sequence.range().start_index();
         index < blocking_sequence.range().end_index(); ++index) {
      // Skips if there is not lateral overlap between snapshot and ego positon.
      math::Range1d fullbody_lateral_range = math::Range1d(
          predicted_states[index]
              .inlane_state.inlane_param.full_body_start_lateral_distance_m,
          predicted_states[index]
              .inlane_state.inlane_param.full_body_end_lateral_distance_m);
      if (path::GetAgentLateralTrackedSideToEgo(
              ego_param, fullbody_lateral_range,
              /*extended_buffer=*/
              required_lat_gap.critical_required_lateral_gap) !=
          math::pb::kOn) {
        continue;
      }
      // Estimates whether the agent in front of ego vehicle(eg: oncoming,
      // crossing, cutin) or cutin agent on the side of ego vehicle could lead
      // harsh swerve by using approximate lateral accelaration.
      if (!reasoning_info.is_agent_behind_rear_bumper()) {
        // Computes lateral and longitudinal overlaped distance between the
        // snapshot and ego current position.
        double lateral_overlap_distance = 0;
        double longitudinal_distance = 0;
        path::ComputeOverlapDistanceForLateralAndLongitudinal(
            ego_param, predicted_states[index].inlane_state, required_lat_gap,
            lateral_overlap_distance, longitudinal_distance);
        // Return true if agent snapshot encroach ego vehicle.
        if (longitudinal_distance < 0) {
          return true;
        }

        // Return true if the lateral accelaration larger than the threshold.
        if (path::ComputeLateralAcceleration(
                ego_param.speed_mps, ego_param.wheel_base,
                lateral_overlap_distance, longitudinal_distance) >
            path::kMaxComfortableLateralAccelerationForVehAndCycInMpss) {
          return true;
        }
      } else {
        // Estimates whether the agent fully behind ego vehicle(eg: cut in
        // behind) could lead harsh swerve by determining the overlap between
        // the ego position and the snapshot.
        if (ego_param.ego_range.start_pos <
            predicted_states[index]
                .inlane_state.inlane_param.full_body_end_arclength_m) {
          return true;
        }
      }
    }
  }
  return false;
}

// Check if the agent trajectory may cause harsh swerve of steering wheel.
bool IsPotentialHarshSwerveTrajectory(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& object_occupancy_state,
    const RequiredLateralGap& required_lat_gap,
    const std::vector<pb::BlockingSequence>& blocking_sequences,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    int predicted_trajectory_id) {
  // Ignore static agent.
  if (reasoning_info.is_stationary()) {
    return false;
  }

  // Ignore agent trajectory is not in current lane.
  if (!reasoning_info.is_in_current_lane()) {
    return false;
  }

  // Mainly process oncoming, crossing and cutin agent.
  if (!(reasoning_info.is_oncoming() || reasoning_info.is_crossing() ||
        reasoning_info.is_cut_in())) {
    return false;
  }

  const std::vector<ObjectStampedSnapshotInfo>& predicted_states =
      FIND_OR_DIE_WITH_PRINT(
          object_occupancy_state.predicted_trajectory_occupancy_states(),
          predicted_trajectory_id)
          .predicted_states();
  if (predicted_states.empty()) {
    return false;
  }

  // Prediction trajectory overlap with ego position.
  for (const auto& blocking_sequence : blocking_sequences) {
    // Skips the non blocking sequence.
    if (blocking_sequence.blocking_state() ==
        pb::LateralBlockingState::NON_BLOCKING) {
      continue;
    }

    DCHECK_LT(blocking_sequence.range().start_index(),
              blocking_sequence.range().end_index());
    DCHECK_LT(blocking_sequence.range().end_index(), predicted_states.size());

    for (int index = blocking_sequence.range().start_index();
         index < blocking_sequence.range().end_index(); ++index) {
      // Skips if there is not lateral overlap between snapshot and ego positon.
      math::Range1d fullbody_lateral_range =
          math::Range1d(predicted_states[index]
                            .object_occupancy_param()
                            .full_body_start_lateral_distance_m,
                        predicted_states[index]
                            .object_occupancy_param()
                            .full_body_end_lateral_distance_m);
      if (path::GetAgentLateralTrackedSideToEgo(
              ego_param, fullbody_lateral_range,
              /*extended_buffer=*/
              required_lat_gap.critical_required_lateral_gap) !=
          math::pb::kOn) {
        continue;
      }
      // Estimates whether the agent in front or on the side of ego vehicle
      // could lead harsh swerve by using approximate lateral accelaration.
      if (!reasoning_info.is_agent_behind_rear_bumper()) {
        // Computes lateral and longitudinal overlaped distance between the
        // snapshot and ego current position.
        double lateral_overlap_distance = 0;
        double longitudinal_distance = 0;
        path::ComputeOverlapDistanceForLateralAndLongitudinal(
            ego_param, predicted_states[index].object_occupancy_param(),
            required_lat_gap, lateral_overlap_distance, longitudinal_distance);
        // Return true if agent snapshot encroach ego vehicle.
        if (longitudinal_distance < 0) {
          return true;
        }

        // Return true if the lateral accelaration larger than the threshold.
        if (path::ComputeLateralAcceleration(
                ego_param.speed_mps, ego_param.wheel_base,
                lateral_overlap_distance, longitudinal_distance) >
            path::kMaxComfortableLateralAccelerationForVehAndCycInMpss) {
          return true;
        }
      } else {
        // Estimates whether the agent behind ego vehicle(eg: cut in behind)
        // could lead harsh swerve by determining the overlap between the ego
        // position and the snapshot.
        if (ego_param.ego_range.start_pos < predicted_states[index]
                                                .object_occupancy_param()
                                                .full_body_end_arclength_m) {
          return true;
        }
      }
    }
  }
  return false;
}

bool IsRiskyBusInBusBulb(const PlannerObject& planner_object,
                         const double agent_end_arclength_m,
                         const pnc_map::Lane& current_lane,
                         const double ego_arclength_m) {
  constexpr double kBusSpeedThresholdToNotAddRepulsionInMps = 5.0;
  const std::vector<const pnc_map::Lane*>& object_lanes =
      planner_object.associated_lanes();
  if ((!planner_object.is_bus_near_bus_bulb() &&
       !planner_object.is_bus_queuing_near_bus_bulb()) ||
      object_lanes.empty() || object_lanes[0] == nullptr) {
    return false;
  }

  if (planner_object.speed() > kBusSpeedThresholdToNotAddRepulsionInMps) {
    // If bus is already in fast enough, no need to be extra cautious since Bus
    // might not be pulling over for the bus bulb, or the bus already finished
    // pull out and is driving normally.
    return false;
  }

  const pnc_map::Road* object_road =
      DCHECK_NOTNULL(object_lanes[0]->section())->road();
  const pnc_map::Road* current_road =
      DCHECK_NOTNULL(current_lane.section())->road();

  if (DCHECK_NOTNULL(object_road)->id() != DCHECK_NOTNULL(current_road)->id()) {
    // If the bus is not in the same road as Ego, don't consider it.
    return false;
  }

  if (agent_end_arclength_m < ego_arclength_m) {
    // Agent is behind Ego, no need to add repulsion.
    return false;
  }

  if (planner_object.is_bus_queuing_near_bus_bulb()) {
    // Trigger RT event for targeting risky bus queuing near bus bulb.
    rt_event::PostRtEvent<
        rt_event::planner::TargetRiskyBusQueuingNearBusBulb>();
  }

  return true;
}

// Detects whether the agent is moving from one side of the lane to the
// other but has not yet fully crossed the lane centerline.
bool IsSemiCrossingWithOncomingMovementTrend(
    const EgoInLaneParams& ego_param, const AgentInLaneStates& agent_state,
    const pb::TrajectoryReasoningInfo& reasoning_info, int trajectory_id) {
  // Currently, only the uncertainty of semi-crossing cyclists is observed to
  // be relatively high, so only cyclist trajectories are considered.
  if (agent_state.agent_metadata.agent_type !=
      voy::perception::ObjectType::CYCLIST) {
    return false;
  }

  // Returns false if agent trajectory is fully crossing ego center line.
  const AgentTrajectoryInLaneStates& predicted_trajectory =
      FIND_OR_DIE_WITH_PRINT(agent_state.predicted_trajectories, trajectory_id);
  if (predicted_trajectory.predicted_states.empty() ||
      predicted_trajectory.trajectory_metadata.intention_type ==
          pb::AgentTrajectoryIntentionType::CROSS) {
    return false;
  }

  // Returns false if agent is beside ego. When the oncoming agent is close
  // longitudinally and on beside of ego vehicle, it is reasonable to assume
  // that the cyclist will not recklessly cross.
  if (reasoning_info.is_beside_ego()) {
    return false;
  }

  // Returns false if agent cross speed is too small.
  const auto& tracked_param = agent_state.tracked_state.inlane_param;
  if (std::fabs(tracked_param.cross_track_speed_mps) <
      kMinAbsoluteCrossSpeedForEncroachingSnapshotInMps) {
    return false;
  }

  // Returns false if there is no lane clearance changes in front of the ego.
  const bool is_current_left_side_inlane =
      tracked_param.left_inlane_clearance_m <
      tracked_param.right_inlane_clearance_m + math::constants::kEpsilon;
  const bool has_side_change_inlane_clearance = std::any_of(
      predicted_trajectory.predicted_states.begin(),
      predicted_trajectory.predicted_states.end(),
      [&ego_param, is_current_left_side_inlane](
          const StampedAgentSnapshotInLaneState& state) {
        const bool is_right_side_inlane =
            state.inlane_state.inlane_param.left_inlane_clearance_m >
            state.inlane_state.inlane_param.right_inlane_clearance_m +
                math::constants::kEpsilon;
        const bool has_change_inlane_clearance =
            (is_current_left_side_inlane && is_right_side_inlane) ||
            (!is_current_left_side_inlane && !is_right_side_inlane);
        return has_change_inlane_clearance &&
               state.inlane_state.inlane_param.full_body_start_arclength_m >
                   ego_param.front_bumper_arclength;
      });
  if (!has_side_change_inlane_clearance) {
    return false;
  }

  const bool is_oncoming_toward_left_side =
      tracked_param.relative_heading_rad > M_PI * 0.5 &&
      tracked_param.relative_heading_rad <
          M_PI - kAlongNominalPathHeadingDiffThreshold;
  const bool is_oncoming_toward_right_side =
      tracked_param.relative_heading_rad < -M_PI * 0.5 &&
      tracked_param.relative_heading_rad >
          -M_PI + kAlongNominalPathHeadingDiffThreshold;
  // Returns true if the agent is moving from one side to the other side of the
  // lane.
  if ((is_current_left_side_inlane && is_oncoming_toward_right_side) ||
      (!is_current_left_side_inlane && is_oncoming_toward_left_side)) {
    return true;
  }

  return false;
}

// Detects whether the agent is moving from one side of the lane to the
// other but has not yet fully crossed the lane centerline.
bool IsSemiCrossingWithOncomingMovementTrend(
    const EgoInLaneParams& ego_param,
    const ObjectOccupancyState& occupancy_state,
    const pb::TrajectoryReasoningInfo& reasoning_info, int trajectory_id,
    const bool is_crossing_object) {
  // Currently, only the uncertainty of semi-crossing cyclists is observed to be
  // relatively high, so only cyclist trajectories are considered.
  if (occupancy_state.object_type() != voy::perception::ObjectType::CYCLIST) {
    return false;
  }

  // Returns false if agent trajectory is fully crossing ego center line.
  const ObjectTrajectoryStates& predicted_trajectory = FIND_OR_DIE_WITH_PRINT(
      occupancy_state.predicted_trajectory_occupancy_states(), trajectory_id);
  if (predicted_trajectory.predicted_states().empty() || is_crossing_object) {
    return false;
  }

  // Returns false if agent is beside ego. When the oncoming agent is close
  // longitudinally and on beside of ego vehicle, it is reasonable to assume
  // that the cyclist will not recklessly cross.
  if (reasoning_info.is_beside_ego()) {
    return false;
  }

  // Returns false if agent cross speed is too small.
  const auto& occupancy_param =
      occupancy_state.current_snapshot_info().object_occupancy_param();
  if (std::fabs(occupancy_param.cross_track_speed_mps) <
      kMinAbsoluteCrossSpeedForEncroachingSnapshotInMps) {
    return false;
  }

  // Returns false if there is no hard blocking snapshot in front of the ego.
  const bool is_current_left_side_inlane =
      occupancy_param.left_boundary_clearance_m <
      occupancy_param.right_boundary_clearance_m + math::constants::kEpsilon;
  const bool has_side_change_inlane_clearance = std::any_of(
      predicted_trajectory.predicted_states().begin(),
      predicted_trajectory.predicted_states().end(),
      [&ego_param,
       is_current_left_side_inlane](const ObjectStampedSnapshotInfo& state) {
        const bool is_right_side_inlane =
            state.object_occupancy_param().left_boundary_clearance_m >
            state.object_occupancy_param().right_boundary_clearance_m +
                math::constants::kEpsilon;
        const bool has_change_inlane_clearance =
            (is_current_left_side_inlane && is_right_side_inlane) ||
            (!is_current_left_side_inlane && !is_right_side_inlane);
        return has_change_inlane_clearance &&
               state.object_occupancy_param().full_body_start_arclength_m >
                   ego_param.front_bumper_arclength;
      });
  if (!has_side_change_inlane_clearance) {
    return false;
  }

  const bool is_oncoming_toward_left_side =
      occupancy_param.relative_heading_rad >
          kAlongNominalPathHeadingDiffThreshold &&
      occupancy_param.relative_heading_rad <
          M_PI - kAlongNominalPathHeadingDiffThreshold;
  const bool is_oncoming_toward_right_side =
      occupancy_param.relative_heading_rad <
          -kAlongNominalPathHeadingDiffThreshold &&
      occupancy_param.relative_heading_rad >
          -M_PI + kAlongNominalPathHeadingDiffThreshold;
  // Returns true if the agent is moving from one side to the other side of the
  // lane.
  if ((is_current_left_side_inlane && is_oncoming_toward_right_side) ||
      (!is_current_left_side_inlane && is_oncoming_toward_left_side)) {
    return true;
  }

  return false;
}

// Returns the time for agent and ego to meet along the reference path, assuming
// ego keeps the current accel and speed while agent keeps the current speed and
// the average accel from its history.
double GetAlongRefPathTtcInSeconds(
    const EgoInLaneParams& ego_param,
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const double agent_along_track_speed,
    const double agent_full_body_start_arclength,
    const double agent_full_body_end_arclength) {
  const double agent_min_arclength =
      std::min(agent_full_body_start_arclength, agent_full_body_end_arclength);
  const double agent_max_arclength =
      std::max(agent_full_body_start_arclength, agent_full_body_end_arclength);
  if (math::AreRangesOverlapping<double>(
          {ego_param.ego_range.start_pos - math::constants::kEpsilon,
           ego_param.ego_range.end_pos + math::constants::kEpsilon},
          {agent_min_arclength - math::constants::kEpsilon,
           agent_max_arclength + math::constants::kEpsilon})) {
    // Already overlapped.
    return 0.0;
  }
  if (!reasoning_info.has_recent_average_cross_track_accel() ||
      !reasoning_info.has_recent_average_accel()) {
    // No recent agent kinematic info.
    return std::numeric_limits<double>::infinity();
  }
  const double along_track_dist =
      agent_min_arclength >= ego_param.ego_range.end_pos
          ? agent_min_arclength - ego_param.ego_range.end_pos
          : agent_max_arclength - ego_param.ego_range.start_pos;
  // If along_track_dist > 0.0, then ego pursuits the agent, otherwise, agent
  // pursuits ego.
  const bool is_agent_leading = along_track_dist > 0.0;
  const double abs_agent_along_track_accel =
      std::sqrt(math::Sqr(reasoning_info.recent_average_accel()) -
                math::Sqr(reasoning_info.recent_average_cross_track_accel()));
  const double agent_along_track_accel =
      reasoning_info.recent_average_accel() > 0.0
          ? abs_agent_along_track_accel
          : -abs_agent_along_track_accel;
  const double ego_along_track_accel =
      ego_param.speed_mps > math::constants::kEpsilon
          ? ego_param.along_track_speed_mps * ego_param.accleration_mpss /
                ego_param.speed_mps
          : 0.0;
  double time_to_catch =
      is_agent_leading
          ? physics::GetMinTimeToCatch(
                ego_along_track_accel, ego_param.along_track_speed_mps,
                agent_along_track_accel, agent_along_track_speed,
                along_track_dist)
          : -physics::GetMinTimeToCatch(
                agent_along_track_accel, agent_along_track_speed,
                ego_along_track_accel, ego_param.along_track_speed_mps,
                -along_track_dist);
  return time_to_catch;
}

pb::TrajectoryReasoningInfo GenerateAgentReasoningInfo(
    const AgentInLaneStates& agent_state, const PlannerObject& planner_object,
    const PlannerObjectsHistory& planner_objects_history,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>& object_predictions,
    const RequiredLateralGap& required_lat_gap,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const std::vector<lane_selection::TrafficLightInfoReference>& ls_tl_infos,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile,
    const EgoInLaneParams& ego_param,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const math::Range1d& planning_horizon_range,
    const AgentSLBoundary& agent_sl_boundary, bool was_nudge_intention,
    const bool is_xlane_nudge_object,
    bool should_trigger_unstuck_for_unknown_objects,
    const math::geometry::PolylineCurve2d& reference_line,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const LaneChangeInfo& lane_change_info,
    const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
    const planner::lateral_clearance::LateralClearanceData&
        lateral_clearance_data,
    const pnc_map::Lane& current_lane, bool is_ego_near_merge,
    bool is_pull_over_gap_align_trailing_agent,
    pb::BehaviorType behavior_type) {
  pb::TrajectoryReasoningInfo reasoning_info;
  reasoning_info.set_is_xlane_nudge_object(is_xlane_nudge_object);
  reasoning_info.set_is_pull_over_gap_align_trailing_agent(
      is_pull_over_gap_align_trailing_agent);
  planner::pb::PullOverJumpInType pullover_jump_in_type =
      planner::pb::PullOverJumpInType::INVALID_TYPE;
  if (pull_over_destination_meta_ptr &&
      pull_over_destination_meta_ptr->jump_in_guidance.has_value()) {
    pullover_jump_in_type =
        pull_over_destination_meta_ptr->jump_in_guidance->jump_in_type();
  }

  // For static/dynamic agent.
  reasoning_info.set_is_stationary(
      planner_object.is_primary_stationary() ||
      (planner_object.is_vehicle() &&
       planner_object.speed() < kStationarySpeedThresholdMps));
  reasoning_info.set_was_nudge_intention(was_nudge_intention);
  reasoning_info.set_is_far_away_behind(
      ShouldIgnoreSourceAgentDuringLaneChangeAbort(
          ego_param, lane_change_info, agent_state.object_id, behavior_type) &&
      IsFarAwayBehindAgent(ego_param, agent_state,
                           reasoning_info.is_stationary(), behavior_type,
                           pullover_jump_in_type));
  reasoning_info.set_plan_init_state_distance(math::geometry::Distance(
      ego_bounding_box,
      agent_state.tracked_state.inlane_param.pose.contour().polygon()));
  reasoning_info.set_is_pull_over_rear_obstacle(
      pull_over_destination_meta_ptr != nullptr &&
      IsPullOverJumpInDestTrailingObstacle(*pull_over_destination_meta_ptr,
                                           /*is_cz=*/false,
                                           planner_object.id()));
  // For dynamic agent.
  reasoning_info.set_is_agent_behind_rear_bumper(IsFullyBehindRearBumper(
      ego_param,
      math::Range1d{
          agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
          agent_state.tracked_state.inlane_param.full_body_end_arclength_m}));
  reasoning_info.set_is_agent_behind_front_bumper(IsFullyBehindFrontBumper(
      ego_param,
      math::Range1d{
          agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
          agent_state.tracked_state.inlane_param.full_body_end_arclength_m}));
  reasoning_info.set_is_agent_in_front_of_front_bumper(
      IsFullyInFrontOfFrontBumper(
          ego_param,
          agent_state.tracked_state.inlane_param.full_body_start_arclength_m));

  if (planner_object.ttc_from_ego_to_pose_at_plan_init_ts().has_value()) {
    reasoning_info.set_tracked_pose_ttc_in_seconds(
        *planner_object.ttc_from_ego_to_pose_at_plan_init_ts());
  } else {
    reasoning_info.set_tracked_pose_ttc_in_seconds(10e4);
  }

  if (ShouldConsiderEmergencySwerveForAgent(
          planner_object, reasoning_info.is_agent_in_front_of_front_bumper(),
          reasoning_info.tracked_pose_ttc_in_seconds(),
          reasoning_info.is_xlane_nudge_object())) {
    reasoning_info.set_tracked_pose_can_cause_emergency_swerve(
        WillTrackedPoseCauseEmergencySwerve(
            planner_object, ego_param,
            reasoning_info.tracked_pose_ttc_in_seconds(),
            agent_state.tracked_state, required_lat_gap));
  } else {
    reasoning_info.set_tracked_pose_can_cause_emergency_swerve(false);
  }

  reasoning_info.set_is_ignorable_behind(
      ShouldIgnoreSourceAgentDuringLaneChangeAbort(
          ego_param, lane_change_info, agent_state.object_id, behavior_type) &&
      reasoning_info.is_agent_behind_rear_bumper() &&
      (path::GetAgentLateralTrackedSideToEgo(
           ego_param,
           math::Range1d(agent_state.tracked_state.inlane_param
                             .full_body_start_lateral_distance_m,
                         agent_state.tracked_state.inlane_param
                             .full_body_end_lateral_distance_m),
           /*extended_buffer=*/0.0) == math::pb::kOn));
  if (reasoning_info.is_stationary()) {
    // Only considers stationary object.
    if (reasoning_info.is_agent_behind_rear_bumper() ||
        planning_horizon_range.end_pos - math::constants::kEpsilon <=
            agent_sl_boundary.start_s) {
      reasoning_info.set_is_stationary_out_of_range_object(true);
    }
  }
  reasoning_info.set_is_beside_ego(IsBesideEgo(
      ego_param,
      agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
      agent_state.tracked_state.inlane_param.full_body_end_arclength_m,
      agent_state.tracked_state.inlane_param.full_body_start_lateral_distance_m,
      agent_state.tracked_state.inlane_param.full_body_end_lateral_distance_m,
      lower_bound_speed_profile));
  reasoning_info.set_contain_emergency_brake_state(IsEmergencyAvoidanceAgent(
      ego_param, agent_state, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary(), emergency_brake_speed_profile,
      lower_bound_speed_profile, reasoning_info));
  reasoning_info.set_oriented_side_to_ego_centerline(
      GetOrientedSideToEgoCenterline(
          agent_state.tracked_state.inlane_param.pose.contour(),
          ego_bounding_box, upper_bound_speed_profile.back().x));
  reasoning_info.set_tracked_oriented_type_to_ego(
      GetAgentTrackedOrientedTypeToEge(
          agent_state.tracked_state.inlane_param.pose.contour(),
          ego_bounding_box));
  reasoning_info.set_is_same_direction(
      IsSameDirectionAgent(agent_state, blocking_state_meta_data.trajectory_id,
                           reasoning_info.is_stationary()));
  reasoning_info.set_is_in_current_lane(IsInCurrentLaneAgent(
      agent_state, required_lat_gap, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary()));
  reasoning_info.set_contain_abnomal_inlane_state(IsAbnormalInlaneParamAgent(
      agent_state, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary()));
  reasoning_info.set_is_motion_heading_opposite_to_box_heading(
      std::abs(math::AngleDiff(planner_object.box_heading(),
                               planner_object.heading())) >
      kReversingAgentHeadingDiffThresholdInRad);
  reasoning_info.set_is_crossing(IsCrossingTrajectory(
      agent_state, blocking_state_meta_data, reasoning_info.is_stationary()));
  reasoning_info.set_is_cyclist_semi_crossing(
      IsSemiCrossingWithOncomingMovementTrend(
          ego_param, agent_state, reasoning_info,
          blocking_state_meta_data.trajectory_id));
  reasoning_info.set_is_oncoming_ahead_of_ego_front_bumper(
      IsOncomingAheadOfEgoFrontBumper(ego_param, agent_state, required_lat_gap,
                                      blocking_state_meta_data.trajectory_id,
                                      reasoning_info));
  if (std::optional<int64_t> prediction_agent_rank =
          agent_state.agent_metadata.prediction_agent_rank;
      prediction_agent_rank.has_value()) {
    reasoning_info.set_prediction_agent_rank(*prediction_agent_rank);
  } else {
    reasoning_info.set_prediction_agent_rank(-1);
  }
  reasoning_info.set_is_agent_within_comfort(
      IsAgentWithinComfort(required_lat_gap, reasoning_info));
  reasoning_info.set_is_agent_within_critical(
      IsAgentWithinCritical(required_lat_gap, reasoning_info));
  reasoning_info.set_is_vehicle_current_heading_along_nominal_path(
      planner_object.is_vehicle() &&
      IsHeadingAlongNominalPath(
          agent_state.tracked_state.inlane_param.relative_heading_rad));
  reasoning_info.set_is_oncoming(
      IsOncomingAgent(agent_state, required_lat_gap, reasoning_info,
                      blocking_state_meta_data.trajectory_id));
  reasoning_info.set_tracked_side_to_ego(GetAgentTrackedSideToEgo(
      ego_param, agent_state, lower_bound_speed_profile,
      blocking_state_meta_data.trajectory_id, reasoning_info.is_stationary(),
      reasoning_info.is_oncoming()));
  reasoning_info.set_is_agent_tracking_possible_cut_in(
      IsAgentTrackingPossibleCutin(agent_state, blocking_state_meta_data,
                                   reasoning_info, behavior_type));
  reasoning_info.set_is_lane_change_target_lane_agent(
      IsLaneChangeTargetRegionAgent(lane_change_execution_info,
                                    agent_state.object_id));
  reasoning_info.set_is_cut_in(IsCutinTrajectory(
      agent_state, required_lat_gap, blocking_state_meta_data, reasoning_info));
  reasoning_info.set_is_lead_and_merge(IsLeadAndMergeTrajectory(
      ego_param, agent_state, required_lat_gap, blocking_state_meta_data,
      reasoning_info, is_ego_near_merge, pullover_jump_in_type));
  const bool is_static_large_vehicle_in_bus_bulb =
      (reasoning_info.is_stationary() && planner_object.is_in_bus_bulb() &&
       planner_object.is_large_vehicle());
  reasoning_info.set_is_static_large_vehicle_in_bus_bulb(
      is_static_large_vehicle_in_bus_bulb);
  reasoning_info.set_is_cyclist_overtaking(IsCyclistOvertaking(
      ego_param, agent_state, required_lat_gap, planner_object, reasoning_info,
      blocking_state_meta_data.trajectory_id));
  reasoning_info.set_is_large_vehicle(planner_object.is_large_vehicle());
  math::Range1d dist_to_center_lane_range =
      agent_state.tracked_state.inlane_param.dist_to_center_line_range;
  bool agent_has_open_door_direction =
      planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_RIGHT) ||
      planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_RIGHT);
  bool is_agent_open_door_vehicle =
      agent_has_open_door_direction
          ? planner::common::ShouldConsiderOpenDoorSignal(
                planner_object,
                /* Signed lateral gap is approximated with Ego current lateral
                   position and (approximated) agent lateral center position */
                (dist_to_center_lane_range.start_pos +
                 dist_to_center_lane_range.end_pos) /
                        2 -
                    ego_param.relative_lateral_distance_to_line_center,
                agent_state.tracked_state.inlane_param.relative_heading_rad)
          : planner_object.is_open_door_vehicle();
  reasoning_info.set_is_open_door_vehicle(is_agent_open_door_vehicle);
  reasoning_info.set_last_stationary_timestamp(
      GetLastStationaryTimestamp(planner_object, planner_objects_history));
  std::optional<PlannerObjectRecentKinematicInfo> recent_kinematic_info =
      planner_objects_history.GetRecentKinematicInfo(
          planner_object, math::Sec2Ms(kMaxHistoryDurationConsiderationInSec),
          reference_line);
  if (recent_kinematic_info.has_value()) {
    reasoning_info.set_recent_average_accel(
        recent_kinematic_info->average_accel_mpss);
    reasoning_info.set_recent_average_jerk(
        recent_kinematic_info->average_jerk_mpsss);
    reasoning_info.set_recent_average_cross_track_accel(
        recent_kinematic_info->average_cross_track_accel_mpss);
    reasoning_info.set_recent_average_cross_track_jerk(
        recent_kinematic_info->average_cross_track_jerk_mpsss);
    reasoning_info.set_recent_average_lat_accel(
        recent_kinematic_info->average_lat_accel_mpss);
  }
  const bool has_persistent_motion = HasPersistentMotion(
      recent_kinematic_info, planner_objects_history, planner_object);
  reasoning_info.set_is_ignorable_unknown(IsIgnorableUnknownAgent(
      agent_state, reasoning_info, has_persistent_motion,
      should_trigger_unstuck_for_unknown_objects));
  reasoning_info.set_is_potential_harsh_swerve_trajectory(
      IsPotentialHarshSwerveTrajectory(
          ego_param, agent_state, required_lat_gap,
          blocking_state_meta_data.blocking_sequences, reasoning_info,
          blocking_state_meta_data.trajectory_id));
  reasoning_info.set_is_vehicle_with_complete_reversing_trajectory(
      planner_object.has_complete_reverse_trajectory() &&
      planner_object.is_vehicle());
  reasoning_info.set_is_lane_change_ignore_agent_in_post_reason(
      IsLaneChangeIgnoreAgentInPostReason(lane_change_execution_info,
                                          agent_state.object_id));
  reasoning_info.set_is_lane_change_ignore_agent(IsLaneChangeIgnoreAgent(
      lane_change_execution_info, agent_state.object_id));
  reasoning_info.set_is_lane_change_crawl_interested_agent(
      IsLaneChangeCrawlInterestedTargetRegionAgent(
          lane_change_execution_info, agent_state.object_id,
          planner_object.is_parked_car(),
          agent_state.agent_metadata.has_start_to_move_trajectory(),
          agent_state.tracked_state.inlane_param.speed_mps));
  reasoning_info.set_is_source_lane_agent_during_lane_change_abort(
      IsSourceRegionAgentDuringLaneChangeAbort(
          lane_change_info, agent_state.object_id, behavior_type));
  reasoning_info.set_is_interested_agent_during_lane_change_or_abort(
      IsInterestedAgentDuringLaneChangeOrLaneChangeAbort(
          reasoning_info.is_lane_change_target_lane_agent(), behavior_type,
          reasoning_info.is_source_lane_agent_during_lane_change_abort()));
  reasoning_info.set_is_cut_ahead_agent_during_lane_change(
      IsCutAheadAgentDuringLaneChange(
          lane_change_info, agent_state.object_id,
          reasoning_info.is_agent_behind_rear_bumper(), behavior_type));
  reasoning_info.set_is_drivable_fod(planner_object.is_object_drivable());
  reasoning_info.set_is_better_not_drive_fod(
      planner_object.is_better_not_drive());
  reasoning_info.set_is_pose_follow_associated_lane(
      IsObjectFollowAssociatedLane(planner_object));
  reasoning_info.set_has_agent_lost_patience(
      HasAgentLostPatience(ego_bounding_box, planner_object,
                           planner_objects_history, reasoning_info));
  reasoning_info.set_is_agent_toward_reference_line(IsAgentTowardReferenceLine(
      agent_state.tracked_state.inlane_param.center_line_side,
      agent_state.tracked_state.inlane_param.relative_heading_rad));
  reasoning_info.set_is_waiting_for_tl(IsAgentWaitingForTrafficLight(
      planner_object, ls_tl_infos,
      math::Range1d(agent_state.tracked_state.inlane_param.start_arclength_m,
                    agent_state.tracked_state.inlane_param.end_arclength_m)));
  reasoning_info.set_is_low_confidence_object(
      IsLowConfidenceObject(planner_object));
  reasoning_info.set_is_sustained_object(planner_object.is_sustained());
  reasoning_info.set_is_vehicle_with_kturn_trajectory(
      planner_object.has_kturn_trajectory() && planner_object.is_vehicle());
  reasoning_info.set_is_vehicle_with_large_relative_lane_heading(
      planner_object.is_vehicle() &&
      HasLargeRelativeLaneHeading(
          reasoning_info,
          agent_state.tracked_state.inlane_param.relative_heading_rad,
          agent_state.tracked_state.inlane_param.cross_track_encroachment_m));
  reasoning_info.set_is_potentially_reversing(IsPotentiallyReversingVehicle(
      reasoning_info, planner_object, planner_objects_history,
      agent_state.tracked_state.inlane_param.relative_heading_rad));
  // NOTE(Reasoning): Filter FP STM signal by parked_car attribute.
  reasoning_info.set_contains_stm_prediction(
      planner_object.stationary_intention().stationary_intention_type() ==
          prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE &&
      !planner_object.is_parked_car());
  reasoning_info.set_is_agent_in_junction(planner_object.is_in_junction());
  reasoning_info.set_is_agent_on_physical_lane_marking(
      (math::NearZero(
           agent_state.tracked_state.inlane_param.left_inlane_clearance_m) &&
       IsPhysicalLaneMarking(
           agent_state.tracked_state.inlane_param.full_body_end_arclength_m,
           math::pb::kLeft, lane_marking_traffic_rule)) ||
      (math::NearZero(
           agent_state.tracked_state.inlane_param.right_inlane_clearance_m) &&
       IsPhysicalLaneMarking(
           agent_state.tracked_state.inlane_param.full_body_end_arclength_m,
           math::pb::kRight, lane_marking_traffic_rule)));
  reasoning_info.set_cross_track_speed_mps(
      agent_state.tracked_state.inlane_param.cross_track_speed_mps);
  reasoning_info.set_is_child_ped(planner_object.is_child_ped());
  reasoning_info.set_is_policeman(planner_object.is_policeman());

  const auto prediction_trajectories_iter =
      object_predictions.find(planner_object.id());
  if (prediction_trajectories_iter == object_predictions.end()) {
    reasoning_info.set_is_low_efficiency_agent(false);
    reasoning_info.set_is_left_turn_oncoming_vehicle(false);
    reasoning_info.set_average_abs_curvature(0.0);
    reasoning_info.set_average_signed_curvature(0.0);
  } else {
    const auto traj_iter = std::find_if(
        prediction_trajectories_iter->second.begin(),
        prediction_trajectories_iter->second.end(),
        [&blocking_state_meta_data](const auto& traj) {
          return traj.id() == blocking_state_meta_data.trajectory_id;
        });
    DCHECK(traj_iter != prediction_trajectories_iter->second.end());
    reasoning_info.set_is_low_efficiency_agent(IsLowEfficiencyAgent(
        planner_object, planner_objects_history, *traj_iter,
        reasoning_info.is_agent_behind_rear_bumper()));
    reasoning_info.set_is_left_turn_oncoming_vehicle(
        planner_object.is_vehicle() &&
        IsLeftTurnOncomingAgent(
            *traj_iter,
            agent_state.tracked_state.inlane_param.relative_heading_rad));
    auto [abs_average_curvature, signed_average_curvature] =
        ComputeAbsAndSignedAverageCurvature(*traj_iter);
    reasoning_info.set_average_abs_curvature(abs_average_curvature);
    reasoning_info.set_average_signed_curvature(signed_average_curvature);
  }

  reasoning_info.set_is_slow_cut_in_vehicle(IsSlowCutInVehicle(
      planner_object, reasoning_info,
      agent_state.tracked_state.inlane_param.cross_track_encroachment_m));
  reasoning_info.set_is_driving_forward_on_ego_lane(IsDrivingForwardOnEgoLane(
      agent_state, required_lat_gap, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary()));
  const auto agent_primary_traj_iter = agent_state.predicted_trajectories.find(
      agent_state.primary_trajectory_id);
  DCHECK(agent_primary_traj_iter != agent_state.predicted_trajectories.end());
  reasoning_info.set_is_primarilly_out_of_physical_boundary(
      path::IsAgentPrimarilyOutOfPhysicalBoundary(
          lateral_clearance_data,
          agent_primary_traj_iter->second.predicted_states,
          planner_object.HasPerceptionAttribute(
              voy::perception::IS_IRREGULAR_SHAPE),
          reference_line));
  reasoning_info.set_is_risky_bus_in_bus_bulb(IsRiskyBusInBusBulb(
      planner_object,
      agent_state.tracked_state.inlane_param.full_body_end_arclength_m,
      current_lane, ego_param.arclength_m));
  reasoning_info.set_along_reference_path_ttc_in_seconds(
      GetAlongRefPathTtcInSeconds(
          ego_param, reasoning_info,
          agent_state.tracked_state.inlane_param.along_track_speed_mps,
          agent_state.tracked_state.inlane_param.full_body_start_arclength_m,
          agent_state.tracked_state.inlane_param.full_body_end_arclength_m));

  if (planner_object.contour_lat_compensation_m().has_value()) {
    reasoning_info.set_irregular_shape_lateral_gap(
        planner_object.contour_lat_compensation_m().value());
  }
  reasoning_info.set_is_blockage_object(planner_object.is_blockage_object());

  return reasoning_info;
}

pb::TrajectoryReasoningInfo GenerateAgentReasoningInfo(
    const ObjectOccupancyState& object_occupancy_state,
    const PlannerObjectsHistory& planner_objects_history,
    const RequiredLateralGap& required_lat_gap,
    const LateralBlockingStateMetaData& blocking_state_meta_data,
    const std::vector<lane_selection::TrafficLightInfoReference>& ls_tl_infos,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile,
    const EgoInLaneParams& ego_param,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const math::Range1d& planning_horizon_range,
    const AgentSLBoundary& agent_sl_boundary, bool was_nudge_intention,
    const bool is_xlane_nudge_object,
    bool should_trigger_unstuck_for_unknown_objects,
    const math::geometry::PolylineCurve2d& reference_line,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const std::optional<LaneChangeExecutionInfo>& lane_change_execution_info,
    const LaneChangeInfo& lane_change_info,
    const lane_selection::LaneMarkingTrafficRule* lane_marking_traffic_rule,
    const planner::lateral_clearance::LateralClearanceData&
        lateral_clearance_data,
    const pnc_map::Lane& current_lane, bool is_ego_near_merge,
    bool is_pull_over_gap_align_trailing_agent,
    pb::BehaviorType behavior_type) {
  pb::TrajectoryReasoningInfo reasoning_info;
  reasoning_info.set_is_xlane_nudge_object(is_xlane_nudge_object);
  reasoning_info.set_is_pull_over_gap_align_trailing_agent(
      is_pull_over_gap_align_trailing_agent);
  planner::pb::PullOverJumpInType pullover_jump_in_type =
      planner::pb::PullOverJumpInType::INVALID_TYPE;
  if (pull_over_destination_meta_ptr &&
      pull_over_destination_meta_ptr->jump_in_guidance.has_value()) {
    pullover_jump_in_type =
        pull_over_destination_meta_ptr->jump_in_guidance->jump_in_type();
  }
  const auto& plan_init_state_occupancy_param =
      object_occupancy_state.current_snapshot_info().object_occupancy_param();

  const auto& planner_object = object_occupancy_state.planner_object();
  // For static/dynamic agent.
  reasoning_info.set_is_stationary(
      object_occupancy_state.is_primary_stationary() ||
      (planner_object.is_vehicle() &&
       planner_object.speed() < kStationarySpeedThresholdMps));
  reasoning_info.set_was_nudge_intention(was_nudge_intention);
  reasoning_info.set_is_far_away_behind(
      ShouldIgnoreSourceAgentDuringLaneChangeAbort(
          ego_param, lane_change_info, object_occupancy_state.object_id(),
          behavior_type) &&
      IsFarAwayBehindAgent(
          ego_param, plan_init_state_occupancy_param,
          object_occupancy_state.current_snapshot_info().is_fully_behind_ego(),
          reasoning_info.is_stationary(), behavior_type,
          pullover_jump_in_type));
  reasoning_info.set_plan_init_state_distance(
      planner_object.l2_distance_to_ego_m());
  reasoning_info.set_is_pull_over_rear_obstacle(
      pull_over_destination_meta_ptr != nullptr &&
      IsPullOverJumpInDestTrailingObstacle(*pull_over_destination_meta_ptr,
                                           /*is_cz=*/false,
                                           planner_object.id()));

  // For dynamic agent.
  reasoning_info.set_is_agent_behind_rear_bumper(IsFullyBehindRearBumper(
      ego_param,
      math::Range1d{
          plan_init_state_occupancy_param.full_body_start_arclength_m,
          plan_init_state_occupancy_param.full_body_end_arclength_m}));
  reasoning_info.set_is_agent_behind_front_bumper(IsFullyBehindFrontBumper(
      ego_param,
      math::Range1d{
          plan_init_state_occupancy_param.full_body_start_arclength_m,
          plan_init_state_occupancy_param.full_body_end_arclength_m}));
  reasoning_info.set_is_agent_in_front_of_front_bumper(
      IsFullyInFrontOfFrontBumper(
          ego_param,
          plan_init_state_occupancy_param.full_body_start_arclength_m));

  if (planner_object.ttc_from_ego_to_pose_at_plan_init_ts().has_value()) {
    reasoning_info.set_tracked_pose_ttc_in_seconds(
        *planner_object.ttc_from_ego_to_pose_at_plan_init_ts());
  } else {
    reasoning_info.set_tracked_pose_ttc_in_seconds(10e4);
  }

  if (ShouldConsiderEmergencySwerveForAgent(
          planner_object, reasoning_info.is_agent_in_front_of_front_bumper(),
          reasoning_info.tracked_pose_ttc_in_seconds(),
          reasoning_info.is_xlane_nudge_object())) {
    reasoning_info.set_tracked_pose_can_cause_emergency_swerve(
        WillTrackedPoseCauseEmergencySwerve(
            planner_object, ego_param,
            reasoning_info.tracked_pose_ttc_in_seconds(),
            object_occupancy_state.current_snapshot_info()
                .object_occupancy_param(),
            required_lat_gap));
  } else {
    reasoning_info.set_tracked_pose_can_cause_emergency_swerve(false);
  }

  reasoning_info.set_is_ignorable_behind(
      ShouldIgnoreSourceAgentDuringLaneChangeAbort(
          ego_param, lane_change_info, object_occupancy_state.object_id(),
          behavior_type) &&
      reasoning_info.is_agent_behind_rear_bumper() &&
      (path::GetAgentLateralTrackedSideToEgo(
           ego_param,
           math::Range1d(plan_init_state_occupancy_param
                             .full_body_start_lateral_distance_m,
                         plan_init_state_occupancy_param
                             .full_body_end_lateral_distance_m),
           /*extended_buffer=*/0.0) == math::pb::kOn));
  if (reasoning_info.is_stationary()) {
    // Only considers stationary object.
    if (reasoning_info.is_agent_behind_rear_bumper() ||
        planning_horizon_range.end_pos - math::constants::kEpsilon <=
            agent_sl_boundary.start_s) {
      reasoning_info.set_is_stationary_out_of_range_object(true);
    }
  }
  reasoning_info.set_is_beside_ego(IsBesideEgo(
      ego_param, plan_init_state_occupancy_param.full_body_start_arclength_m,
      plan_init_state_occupancy_param.full_body_end_arclength_m,
      plan_init_state_occupancy_param.full_body_start_lateral_distance_m,
      plan_init_state_occupancy_param.full_body_end_lateral_distance_m,
      lower_bound_speed_profile));
  reasoning_info.set_contain_emergency_brake_state(IsEmergencyAvoidanceAgent(
      ego_param, object_occupancy_state, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary(), emergency_brake_speed_profile,
      lower_bound_speed_profile, reasoning_info));
  reasoning_info.set_oriented_side_to_ego_centerline(
      GetOrientedSideToEgoCenterline(object_occupancy_state.pose().contour(),
                                     ego_bounding_box,
                                     upper_bound_speed_profile.back().x));
  reasoning_info.set_tracked_oriented_type_to_ego(
      GetAgentTrackedOrientedTypeToEge(object_occupancy_state.pose().contour(),
                                       ego_bounding_box));
  reasoning_info.set_is_motion_heading_opposite_to_box_heading(
      std::abs(math::AngleDiff(planner_object.box_heading(),
                               planner_object.heading())) >
      kReversingAgentHeadingDiffThresholdInRad);
  reasoning_info.set_is_same_direction(IsSameDirectionAgent(
      object_occupancy_state, blocking_state_meta_data.trajectory_id,
      reasoning_info.is_stationary()));
  reasoning_info.set_is_in_current_lane(IsInCurrentLaneAgent(
      object_occupancy_state, required_lat_gap,
      blocking_state_meta_data.trajectory_id, reasoning_info.is_stationary()));
  reasoning_info.set_contain_abnomal_inlane_state(
      IsAbnormalObjectOccupancyParamAgent(
          object_occupancy_state, blocking_state_meta_data.trajectory_id,
          reasoning_info.is_stationary()));
  DCHECK(object_occupancy_state.predicted_trajectory_occupancy_states().find(
             blocking_state_meta_data.trajectory_id) !=
         object_occupancy_state.predicted_trajectory_occupancy_states().end());
  bool is_crossing_object =
      object_occupancy_state.predicted_trajectory_occupancy_states().empty()
          ? false
          : object_occupancy_state.predicted_trajectory_occupancy_states()
                .at(blocking_state_meta_data.trajectory_id)
                .is_crossing_prediction();
  reasoning_info.set_is_crossing(
      IsCrossingTrajectory(object_occupancy_state, blocking_state_meta_data,
                           is_crossing_object, reasoning_info.is_stationary()));
  reasoning_info.set_is_cyclist_semi_crossing(
      IsSemiCrossingWithOncomingMovementTrend(
          ego_param, object_occupancy_state, reasoning_info,
          blocking_state_meta_data.trajectory_id, is_crossing_object));
  reasoning_info.set_is_oncoming_ahead_of_ego_front_bumper(
      IsOncomingAheadOfEgoFrontBumper(
          ego_param, object_occupancy_state, required_lat_gap,
          blocking_state_meta_data.trajectory_id, reasoning_info));
  if (std::optional<int64_t> prediction_agent_rank =
          planner_object.prediction_agent_rank();
      prediction_agent_rank.has_value()) {
    reasoning_info.set_prediction_agent_rank(*prediction_agent_rank);
  } else {
    reasoning_info.set_prediction_agent_rank(-1);
  }
  reasoning_info.set_is_agent_within_comfort(
      IsAgentWithinComfort(required_lat_gap, reasoning_info));
  reasoning_info.set_is_agent_within_critical(
      IsAgentWithinCritical(required_lat_gap, reasoning_info));
  reasoning_info.set_is_vehicle_current_heading_along_nominal_path(
      planner_object.is_vehicle() &&
      IsHeadingAlongNominalPath(
          plan_init_state_occupancy_param.relative_heading_rad));
  reasoning_info.set_is_oncoming(IsOncomingAgent(
      object_occupancy_state, required_lat_gap, reasoning_info,
      is_crossing_object, blocking_state_meta_data.trajectory_id));
  reasoning_info.set_tracked_side_to_ego(GetAgentTrackedSideToEgo(
      ego_param, object_occupancy_state, lower_bound_speed_profile,
      blocking_state_meta_data.trajectory_id, reasoning_info.is_stationary(),
      reasoning_info.is_oncoming()));
  reasoning_info.set_is_agent_tracking_possible_cut_in(
      IsAgentTrackingPossibleCutin(object_occupancy_state,
                                   blocking_state_meta_data, reasoning_info,
                                   behavior_type));
  reasoning_info.set_is_lane_change_target_lane_agent(
      IsLaneChangeTargetRegionAgent(lane_change_execution_info,
                                    object_occupancy_state.object_id()));
  reasoning_info.set_is_cut_in(
      IsCutinTrajectory(object_occupancy_state, required_lat_gap,
                        blocking_state_meta_data, reasoning_info));
  reasoning_info.set_is_lead_and_merge(IsLeadAndMergeTrajectory(
      ego_param, object_occupancy_state, required_lat_gap,
      blocking_state_meta_data, reasoning_info, is_ego_near_merge,
      pullover_jump_in_type));
  const bool is_static_large_vehicle_in_bus_bulb =
      (reasoning_info.is_stationary() && planner_object.is_in_bus_bulb() &&
       planner_object.is_large_vehicle());
  reasoning_info.set_is_static_large_vehicle_in_bus_bulb(
      is_static_large_vehicle_in_bus_bulb);
  reasoning_info.set_is_cyclist_overtaking(IsCyclistOvertaking(
      ego_param, object_occupancy_state, required_lat_gap, reasoning_info,
      blocking_state_meta_data.trajectory_id));
  reasoning_info.set_is_large_vehicle(
      object_occupancy_state.planner_object().is_large_vehicle());
  math::Range1d dist_to_center_lane_range =
      object_occupancy_state.current_snapshot_info()
          .object_occupancy_param()
          .dist_to_center_line_range;
  bool agent_has_open_door_direction =
      planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_RIGHT) ||
      planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_RIGHT);
  bool is_agent_open_door_vehicle =
      agent_has_open_door_direction
          ? planner::common::ShouldConsiderOpenDoorSignal(
                planner_object,
                /* Signed lateral gap is approximated with Ego current lateral
                   position and (approximated) agent lateral center position */
                (dist_to_center_lane_range.start_pos +
                 dist_to_center_lane_range.end_pos) /
                        2 -
                    ego_param.relative_lateral_distance_to_line_center,
                object_occupancy_state.current_snapshot_info()
                    .object_occupancy_param()
                    .relative_heading_rad)
          : planner_object.is_open_door_vehicle();
  reasoning_info.set_is_open_door_vehicle(is_agent_open_door_vehicle);
  reasoning_info.set_last_stationary_timestamp(
      GetLastStationaryTimestamp(planner_object, planner_objects_history));
  std::optional<PlannerObjectRecentKinematicInfo> recent_kinematic_info =
      planner_objects_history.GetRecentKinematicInfo(
          planner_object, math::Sec2Ms(kMaxHistoryDurationConsiderationInSec),
          reference_line);
  if (recent_kinematic_info.has_value()) {
    reasoning_info.set_recent_average_accel(
        recent_kinematic_info->average_accel_mpss);
    reasoning_info.set_recent_average_jerk(
        recent_kinematic_info->average_jerk_mpsss);
    reasoning_info.set_recent_average_cross_track_accel(
        recent_kinematic_info->average_cross_track_accel_mpss);
    reasoning_info.set_recent_average_cross_track_jerk(
        recent_kinematic_info->average_cross_track_jerk_mpsss);
    reasoning_info.set_recent_average_lat_accel(
        recent_kinematic_info->average_lat_accel_mpss);
  }
  const bool has_persistent_motion = HasPersistentMotion(
      recent_kinematic_info, planner_objects_history, planner_object);
  reasoning_info.set_is_ignorable_unknown(IsIgnorableUnknownAgent(
      object_occupancy_state, reasoning_info, has_persistent_motion,
      should_trigger_unstuck_for_unknown_objects));
  reasoning_info.set_is_potential_harsh_swerve_trajectory(
      IsPotentialHarshSwerveTrajectory(
          ego_param, object_occupancy_state, required_lat_gap,
          blocking_state_meta_data.blocking_sequences, reasoning_info,
          blocking_state_meta_data.trajectory_id));
  reasoning_info.set_is_vehicle_with_complete_reversing_trajectory(
      planner_object.has_complete_reverse_trajectory() &&
      planner_object.is_vehicle());
  reasoning_info.set_is_lane_change_ignore_agent_in_post_reason(
      IsLaneChangeIgnoreAgentInPostReason(lane_change_execution_info,
                                          object_occupancy_state.object_id()));
  reasoning_info.set_is_lane_change_ignore_agent(IsLaneChangeIgnoreAgent(
      lane_change_execution_info, object_occupancy_state.object_id()));
  reasoning_info.set_is_lane_change_crawl_interested_agent(
      IsLaneChangeCrawlInterestedTargetRegionAgent(
          lane_change_execution_info, object_occupancy_state.object_id(),
          planner_object.is_parked_car(),
          object_occupancy_state.has_start_to_move_trajectory(),
          planner_object.speed()));
  reasoning_info.set_is_source_lane_agent_during_lane_change_abort(
      IsSourceRegionAgentDuringLaneChangeAbort(
          lane_change_info, object_occupancy_state.object_id(), behavior_type));
  reasoning_info.set_is_interested_agent_during_lane_change_or_abort(
      IsInterestedAgentDuringLaneChangeOrLaneChangeAbort(
          reasoning_info.is_lane_change_target_lane_agent(), behavior_type,
          reasoning_info.is_source_lane_agent_during_lane_change_abort()));
  reasoning_info.set_is_cut_ahead_agent_during_lane_change(
      IsCutAheadAgentDuringLaneChange(
          lane_change_info, object_occupancy_state.object_id(),
          reasoning_info.is_agent_behind_rear_bumper(), behavior_type));
  reasoning_info.set_is_drivable_fod(planner_object.is_object_drivable());
  reasoning_info.set_is_better_not_drive_fod(
      planner_object.is_better_not_drive());
  reasoning_info.set_is_pose_follow_associated_lane(
      IsObjectFollowAssociatedLane(planner_object));
  reasoning_info.set_has_agent_lost_patience(
      HasAgentLostPatience(ego_bounding_box, planner_object,
                           planner_objects_history, reasoning_info));
  reasoning_info.set_is_agent_toward_reference_line(
      IsAgentTowardReferenceLine(object_occupancy_state.current_snapshot_info()
                                     .object_occupancy_param()
                                     .center_line_side,
                                 object_occupancy_state.current_snapshot_info()
                                     .object_occupancy_param()
                                     .relative_heading_rad));
  reasoning_info.set_is_waiting_for_tl(IsAgentWaitingForTrafficLight(
      planner_object, ls_tl_infos,
      math::Range1d(plan_init_state_occupancy_param.snapshot_start_arclength_m,
                    plan_init_state_occupancy_param.snapshot_end_arclength_m)));
  reasoning_info.set_is_low_confidence_object(
      IsLowConfidenceObject(planner_object));
  reasoning_info.set_is_sustained_object(planner_object.is_sustained());
  reasoning_info.set_is_vehicle_with_kturn_trajectory(
      planner_object.has_kturn_trajectory() && planner_object.is_vehicle());
  reasoning_info.set_is_vehicle_with_large_relative_lane_heading(
      planner_object.is_vehicle() &&
      HasLargeRelativeLaneHeading(
          reasoning_info, plan_init_state_occupancy_param.relative_heading_rad,
          plan_init_state_occupancy_param.cross_track_encroachment_m));
  reasoning_info.set_is_potentially_reversing(IsPotentiallyReversingVehicle(
      reasoning_info, planner_object, planner_objects_history,
      plan_init_state_occupancy_param.relative_heading_rad));
  // NOTE(Reasoning): Filter FP STM signal by parked_car attribute.
  reasoning_info.set_contains_stm_prediction(
      planner_object.stationary_intention().stationary_intention_type() ==
          prediction::pb::StationaryIntentionType::STATIONARY_TO_MOVE &&
      !planner_object.is_parked_car());
  reasoning_info.set_is_agent_in_junction(planner_object.is_in_junction());
  reasoning_info.set_is_agent_on_physical_lane_marking(
      (math::NearZero(
           plan_init_state_occupancy_param.left_boundary_clearance_m) &&
       IsPhysicalLaneMarking(
           plan_init_state_occupancy_param.full_body_end_arclength_m,
           math::pb::kLeft, lane_marking_traffic_rule)) ||
      (math::NearZero(
           plan_init_state_occupancy_param.right_boundary_clearance_m) &&
       IsPhysicalLaneMarking(
           plan_init_state_occupancy_param.full_body_end_arclength_m,
           math::pb::kRight, lane_marking_traffic_rule)));
  reasoning_info.set_cross_track_speed_mps(
      plan_init_state_occupancy_param.cross_track_speed_mps);
  reasoning_info.set_is_child_ped(planner_object.is_child_ped());
  reasoning_info.set_is_policeman(planner_object.is_policeman());

  const auto& traj_occupancy_states =
      object_occupancy_state.predicted_trajectory_occupancy_states();
  const auto traj_state_iter =
      traj_occupancy_states.find(blocking_state_meta_data.trajectory_id);
  if (traj_state_iter == traj_occupancy_states.end()) {
    reasoning_info.set_is_low_efficiency_agent(false);
    reasoning_info.set_is_left_turn_oncoming_vehicle(false);
    reasoning_info.set_average_abs_curvature(0.0);
    reasoning_info.set_average_signed_curvature(0.0);
  } else {
    reasoning_info.set_is_low_efficiency_agent(
        IsLowEfficiencyAgent(planner_object, planner_objects_history,
                             traj_state_iter->second.predicted_trajectory(),
                             reasoning_info.is_agent_behind_rear_bumper()));
    reasoning_info.set_is_left_turn_oncoming_vehicle(
        planner_object.is_vehicle() &&
        IsLeftTurnOncomingAgent(traj_state_iter->second.predicted_trajectory(),
                                object_occupancy_state.current_snapshot_info()
                                    .object_occupancy_param()
                                    .relative_heading_rad));
    auto [abs_average_curvature, signed_average_curvature] =
        ComputeAbsAndSignedAverageCurvature(
            traj_state_iter->second.predicted_trajectory());
    reasoning_info.set_average_abs_curvature(abs_average_curvature);
    reasoning_info.set_average_signed_curvature(signed_average_curvature);
  }
  const auto primary_traj_state_iter = traj_occupancy_states.find(
      object_occupancy_state.primary_trajectory_id());
  if (primary_traj_state_iter == traj_occupancy_states.end()) {
    reasoning_info.set_is_primarilly_out_of_physical_boundary(false);
  } else {
    reasoning_info.set_is_primarilly_out_of_physical_boundary(
        path::IsAgentPrimarilyOutOfPhysicalBoundary(
            lateral_clearance_data,
            primary_traj_state_iter->second.predicted_states(),
            planner_object.HasPerceptionAttribute(
                voy::perception::IS_IRREGULAR_SHAPE),
            reference_line));
  }

  reasoning_info.set_is_slow_cut_in_vehicle(IsSlowCutInVehicle(
      planner_object, reasoning_info,
      plan_init_state_occupancy_param.cross_track_encroachment_m));
  reasoning_info.set_is_driving_forward_on_ego_lane(IsDrivingForwardOnEgoLane(
      object_occupancy_state, required_lat_gap,
      blocking_state_meta_data.trajectory_id, reasoning_info.is_stationary()));
  reasoning_info.set_is_risky_bus_in_bus_bulb(
      IsRiskyBusInBusBulb(planner_object,
                          object_occupancy_state.current_snapshot_info()
                              .object_occupancy_param()
                              .full_body_end_arclength_m,
                          current_lane, ego_param.arclength_m));
  reasoning_info.set_along_reference_path_ttc_in_seconds(
      GetAlongRefPathTtcInSeconds(ego_param, reasoning_info,
                                  object_occupancy_state.current_snapshot_info()
                                      .object_occupancy_param()
                                      .along_track_speed_mps,
                                  object_occupancy_state.current_snapshot_info()
                                      .object_occupancy_param()
                                      .full_body_start_arclength_m,
                                  object_occupancy_state.current_snapshot_info()
                                      .object_occupancy_param()
                                      .full_body_end_arclength_m));
  if (object_occupancy_state.planner_object()
          .contour_lat_compensation_m()
          .has_value()) {
    reasoning_info.set_irregular_shape_lateral_gap(
        object_occupancy_state.planner_object()
            .contour_lat_compensation_m()
            .value());
  }
  reasoning_info.set_is_blockage_object(planner_object.is_blockage_object());

  return reasoning_info;
}

pb::TrajectoryReasoningInfo GenerateConstructionZoneReasoningInfo(
    const ConstructionZoneInLaneState& cz_inlane_state,
    const RequiredLateralGap& required_lat_gap,
    const EgoInLaneParams& ego_param,
    const math::geometry::OrientedBox2d& ego_bounding_box,
    const math::Range1d& planning_horizon_range,
    const AgentSLBoundary& cz_sl_boundary,
    const pull_over::DestinationMetaData* pull_over_destination_meta_ptr,
    const speed::Profile& emergency_brake_speed_profile,
    const speed::Profile& lower_bound_speed_profile,
    const speed::Profile& upper_bound_speed_profile, bool was_nudge_intention,
    pb::BehaviorType behavior_type) {
  pb::TrajectoryReasoningInfo reasoning_info;
  // For static/dynamic agent.
  reasoning_info.set_is_stationary(true);
  reasoning_info.set_is_far_away_behind(
      IsSafeToIgnore(ego_param, behavior_type, cz_inlane_state.end_arclength_m,
                     /*is_stationary=*/true));
  reasoning_info.set_contain_abnomal_inlane_state(IsAbnormalInlaneParamSnapshot(
      cz_inlane_state.start_arclength_m, cz_inlane_state.end_arclength_m));
  reasoning_info.set_was_nudge_intention(was_nudge_intention);
  reasoning_info.set_is_agent_behind_rear_bumper(IsFullyBehindRearBumper(
      ego_param, math::Range1d{cz_inlane_state.start_arclength_m,
                               cz_inlane_state.end_arclength_m}));
  reasoning_info.set_is_agent_behind_front_bumper(IsFullyBehindFrontBumper(
      ego_param, math::Range1d{cz_inlane_state.start_arclength_m,
                               cz_inlane_state.end_arclength_m}));
  reasoning_info.set_is_agent_in_front_of_front_bumper(
      IsFullyInFrontOfFrontBumper(ego_param,
                                  cz_inlane_state.start_arclength_m));
  reasoning_info.set_is_ignorable_behind(
      reasoning_info.is_agent_behind_rear_bumper() &&
      (path::GetAgentLateralTrackedSideToEgo(
           ego_param,
           math::Range1d(cz_inlane_state.start_lateral_distance_m,
                         cz_inlane_state.end_lateral_distance_m),
           /*extended_buffer=*/0.0) == math::pb::kOn));
  if (planning_horizon_range.start_pos + math::constants::kEpsilon >=
          cz_sl_boundary.end_s ||
      planning_horizon_range.end_pos - math::constants::kEpsilon <=
          cz_sl_boundary.start_s) {
    reasoning_info.set_is_stationary_out_of_range_object(true);
  }
  reasoning_info.set_plan_init_state_distance(math::geometry::Distance(
      ego_bounding_box, cz_inlane_state.construction_zone_contour));
  reasoning_info.set_contain_emergency_brake_state(IsEmergencyAvoidanceCz(
      ego_param, cz_inlane_state, emergency_brake_speed_profile,
      lower_bound_speed_profile, reasoning_info));

  reasoning_info.set_is_in_current_lane(
      IsInEgoLaneSnapshot(required_lat_gap, cz_inlane_state.encroachment_m));
  reasoning_info.set_is_agent_within_critical(
      IsAgentWithinCritical(required_lat_gap, reasoning_info));
  reasoning_info.set_is_agent_within_comfort(
      IsAgentWithinComfort(required_lat_gap, reasoning_info));

  reasoning_info.set_is_pull_over_rear_obstacle(
      pull_over_destination_meta_ptr != nullptr &&
      IsPullOverJumpInDestTrailingObstacle(*pull_over_destination_meta_ptr,
                                           /*is_cz=*/true, cz_inlane_state.id));
  reasoning_info.set_is_lane_change_target_lane_agent(false);
  reasoning_info.set_is_lane_change_ignore_agent_in_post_reason(false);
  reasoning_info.set_is_lane_change_ignore_agent(false);
  reasoning_info.set_is_lane_change_crawl_interested_agent(false);
  reasoning_info.set_is_source_lane_agent_during_lane_change_abort(false);
  reasoning_info.set_is_interested_agent_during_lane_change_or_abort(false);
  reasoning_info.set_is_cut_ahead_agent_during_lane_change(false);
  reasoning_info.set_tracked_side_to_ego(GetCzTrackedSideToEgo(
      ego_param, cz_inlane_state, lower_bound_speed_profile));
  reasoning_info.set_oriented_side_to_ego_centerline(
      GetOrientedSideToEgoCenterline(cz_inlane_state.construction_zone_contour,
                                     ego_bounding_box,
                                     upper_bound_speed_profile.back().x));
  reasoning_info.set_tracked_oriented_type_to_ego(
      GetAgentTrackedOrientedTypeToEge(
          cz_inlane_state.construction_zone_contour, ego_bounding_box));

  // Below are attributes that doesn't apply to CZ. We overwrite to false just
  // to be safe.
  reasoning_info.set_is_cyclist_overtaking(false);
  reasoning_info.set_is_ignorable_unknown(false);
  reasoning_info.set_is_same_direction(false);
  reasoning_info.set_is_oncoming(false);
  reasoning_info.set_is_crossing(false);
  reasoning_info.set_is_oncoming_ahead_of_ego_front_bumper(false);
  reasoning_info.set_is_lane_change_target_lane_agent(false);
  reasoning_info.set_is_cut_in(false);
  reasoning_info.set_is_lead_and_merge(false);
  reasoning_info.set_is_waiting_for_tl(false);
  reasoning_info.set_prediction_agent_rank(-1);
  reasoning_info.set_is_static_large_vehicle_in_bus_bulb(false);
  reasoning_info.set_is_large_vehicle(false);
  reasoning_info.set_is_open_door_vehicle(false);
  reasoning_info.set_is_motion_heading_opposite_to_box_heading(false);
  reasoning_info.set_is_potential_harsh_swerve_trajectory(false);
  reasoning_info.set_is_vehicle_current_heading_along_nominal_path(false);
  reasoning_info.set_is_vehicle_with_complete_reversing_trajectory(false);
  reasoning_info.set_is_lane_change_ignore_agent_in_post_reason(false);
  reasoning_info.set_is_lane_change_ignore_agent(false);
  reasoning_info.set_is_lane_change_crawl_interested_agent(false);
  reasoning_info.set_is_source_lane_agent_during_lane_change_abort(false);
  reasoning_info.set_is_interested_agent_during_lane_change_or_abort(false);
  reasoning_info.set_is_cut_ahead_agent_during_lane_change(false);
  reasoning_info.set_is_drivable_fod(false);
  reasoning_info.set_is_better_not_drive_fod(false);
  reasoning_info.set_has_agent_lost_patience(false);
  reasoning_info.set_is_low_confidence_object(false);
  reasoning_info.set_is_vehicle_with_large_relative_lane_heading(false);
  reasoning_info.set_is_potentially_reversing(false);
  reasoning_info.set_contains_stm_prediction(false);
  reasoning_info.set_is_agent_in_junction(false);
  reasoning_info.set_is_agent_on_physical_lane_marking(false);
  reasoning_info.set_is_low_efficiency_agent(false);
  reasoning_info.set_is_left_turn_oncoming_vehicle(false);
  // Set CZ TTC as a random large value.
  reasoning_info.set_tracked_pose_ttc_in_seconds(10e4);
  reasoning_info.set_along_reference_path_ttc_in_seconds(
      std::numeric_limits<double>::infinity());
  reasoning_info.set_average_abs_curvature(0.0);
  reasoning_info.set_average_signed_curvature(0.0);
  reasoning_info.set_is_blockage_object(true);
  return reasoning_info;
}

int64 GetLastStationaryTimestamp(
    const PlannerObject& planner_object,
    const PlannerObjectsHistory& planner_objects_history) {
  const ObjectId object_id = planner_object.id();
  int64 result_timestamp = std::numeric_limits<int64_t>::min();
  for (const auto& history_info_map : planner_objects_history.history()) {
    const auto object_history_iter = history_info_map->find(object_id);
    if (object_history_iter == history_info_map->end()) {
      continue;
    }
    if (object_history_iter->second.is_stationary()) {
      math::UpdateMax(object_history_iter->second.timestamp(),
                      result_timestamp);
    }
  }
  return result_timestamp;
}

// Returns true, if we found agent has yielded to Ego for some time but
// doesn't want to yield to Ego anymore. To check whether the agent has shown a
// yield intention, we focus on the following two conditions:
// 1. if agent has finished reverse, we treat agent as having shown yield
// intention;
// 2. if agent has been seeing ego for more than 30% of the time, and it is at
// low speed < 0.5, we think it is has shown yield intention.
// If 1 or 2 is true, and current agent speed is positive, and it is not
// reversing, we think it is going to pass us right now, so we treat it as
// has_agent_lost_patience.
bool HasAgentLostPatience(const math::geometry::OrientedBox2d& ego_bounding_box,
                          const PlannerObject& planner_object,
                          const PlannerObjectsHistory& planner_objects_history,
                          const pb::TrajectoryReasoningInfo& reasoning_info) {
  if (!reasoning_info.was_nudge_intention()) {
    return false;
  }
  bool agent_has_shown_a_yield_intention = false;
  int can_agent_see_ego_frame_count = 0;
  int total_frame_count = 0;
  for (const auto& history_map : planner_objects_history.history()) {
    const auto it = history_map->find(planner_object.id());
    if (it == history_map->end()) {
      continue;
    }
    const auto& history = it->second;
    // TODO(landryli): consider k-turn signal here.
    if (history.has_complete_reverse_trajectory()) {
      agent_has_shown_a_yield_intention = true;
      break;
    }
    total_frame_count = total_frame_count + 1;
    if (speed::CanAgentSeeEgo(planner_object.tracked_object(),
                              ego_bounding_box.CornerPoints()) &&
        planner_object.speed() < kLosePatienceAgentSpeedThreshold) {
      can_agent_see_ego_frame_count += 1;
    }
  }
  if (!agent_has_shown_a_yield_intention && total_frame_count > 0 &&
      can_agent_see_ego_frame_count >
          total_frame_count * kMinAgentSeeEgoRatioToShowYieldIntention) {
    agent_has_shown_a_yield_intention = true;
  }
  if (!agent_has_shown_a_yield_intention) {
    return false;
  }
  return planner_object.speed() > kLosePatienceAgentSpeedThreshold &&
         !planner_object.has_complete_reverse_trajectory();
}

bool IsLowEfficiencyAgent(const PlannerObject& planner_object,
                          const PlannerObjectsHistory& planner_objects_history,
                          const PredictedTrajectoryWrapper& trajectory,
                          bool is_fully_behind_agent) {
  constexpr double kMinEfficiencySpeedForEgoInMps = 7.0;
  constexpr double kLowEfficiencySpeedRatio = 0.6;
  // Currently, only dynamic agent can talk about driving 'efficiency'.
  if (planner_object.is_stationary() || is_fully_behind_agent) {
    return false;
  }

  const std::optional<double> avg_historical_velocity =
      planner_objects_history.GetRecentAverageVelocity(
          planner_object, kDefaultHistoricalDurationForAvgVelocityInMSec);
  const double avg_future_velocity = trajectory.GetTrajectoryAverageSpeed();
  // Use associated route to check if the agents' velocity is much lower than
  // the lane's speed limit.
  const std::optional<std::vector<route_association::MapElementAndPoseInfo>>&
      associated_route_opt = trajectory.associated_route_opt();
  double min_max_speed_limit_mps = std::numeric_limits<double>::max();
  if (associated_route_opt.has_value()) {
    for (const auto& map_element_info : associated_route_opt.value()) {
      if (map_element_info.lane_ptr == nullptr) {
        continue;
      }

      math::geometry::Point2d query_point =
          map_element_info.lane_ptr->center_line().GetStartPoint();
      if (map_element_info.sampled_pose_index_in_trajectory_opt.has_value() &&
          map_element_info.sampled_pose_index_in_trajectory_opt.value() <
              trajectory.size()) {
        const planner::pb::TrajectoryPose& associate_pose = trajectory.pose(
            map_element_info.sampled_pose_index_in_trajectory_opt.value());
        query_point = math::geometry::Point2d(associate_pose.x_pos(),
                                              associate_pose.y_pos());
      }
      const double max_speed_limit =
          map_element_info.lane_ptr->GetMaxSpeedLimit(query_point);
      math::UpdateMin(max_speed_limit, min_max_speed_limit_mps);
    }
  }

  // Try to find speed limit based on the associated lanes.
  if (min_max_speed_limit_mps == std::numeric_limits<double>::max()) {
    for (const auto* lane : planner_object.associated_lanes()) {
      const double max_speed_limit =
          lane->GetMaxSpeedLimit(planner_object.center_2d());
      math::UpdateMin(max_speed_limit, min_max_speed_limit_mps);
    }
  }

  // Use default low-efficiency speed threshold if there is still no valid
  // speed limit.
  min_max_speed_limit_mps =
      min_max_speed_limit_mps == std::numeric_limits<double>::max()
          ? kMinEfficiencySpeedForEgoInMps
          : min_max_speed_limit_mps * kLowEfficiencySpeedRatio;
  return avg_future_velocity < min_max_speed_limit_mps &&
         avg_historical_velocity.value_or(0.0) < min_max_speed_limit_mps;
}

}  // namespace planner
