#include "planner/path/reasoning/agent_intention/reasoner/side_agent_path_reasoner/side_agent_path_reasoner.h"

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include "log_utils/map_macros.h"
#include "planner/path/reasoning/agent_intention/reasoner/reasoner_util.h"
#include "trace/trace.h"
#include "voy_trace/trace_planner.h"

namespace planner {
namespace path {

namespace {

constexpr int64 kMaxDurationToConsiderPrediction = 2000;  // in ms
constexpr double kComfortAbsoluteBrakeInMpss = 2.0;
constexpr double kDefensiveLatGapIncrementDuringLaneChangeInMeter = 0.2;

bool ShouldConsiderNeighbourLaneAgentWhenInlaneNudge(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const path::ActiveLatGapAdjustMetaData& active_lat_gap_adjust_meta,
    const double agent_along_track_speed_mps, std::string* debug_str) {
  if (active_lat_gap_adjust_meta.intended_lat_move_direction !=
      math::pb::Side::kOn) {
    // If <PERSON><PERSON> has active lateral movement, don't consider agent on the other
    // side.
    if (reasoning_info.tracked_side_to_ego() !=
        active_lat_gap_adjust_meta.intended_lat_move_direction) {
      absl::StrAppend(debug_str,
                      "INLANE_NUDGE: ego active lateral move, no consider the "
                      "other side agent; ");
      return false;
    }
  } else {
    // If Ego does not have active lateral movement, don't consider agent that's
    // behind rear bumper and is slower.
    if (reasoning_info.is_agent_behind_rear_bumper() &&
        active_lat_gap_adjust_meta.ego_speed_mps >
            agent_along_track_speed_mps) {
      absl::StrAppend(debug_str,
                      "INLANE_NUDGE: not consider behind slow agent; ");
      return false;
    }
  }

  // By default, we should consider neighbor lane agent.
  return true;
}

// Returns true if the agent is neighbour lane agent and need to consider in st
// planner with the results of scenario identifier.
bool ShouldConsiderNeighbourLaneAgent(
    const pb::TrajectoryReasoningInfo& reasoning_info,
    const path::ActiveLatGapAdjustMetaData& active_lat_gap_adjust_meta,
    const path::ScenarioIdentifierResult& scenario_identify_result,
    const double agent_along_track_speed_mps, std::string* debug_str) {
  switch (scenario_identify_result.scenario_type) {
    case pb::ScenarioRecognition::AVOID_NUDGE: {
      DCHECK(scenario_identify_result.avoid_nudge_scene.has_value());
      if (debug_str) {
        absl::StrAppend(debug_str, "AVOID_NUDGE: no consider side agent; ");
      }
      return false;
    }
    case pb::ScenarioRecognition::XLANE_NUDGE: {
      DCHECK(scenario_identify_result.xlane_nudge_scene.has_value());
      // TODO(tianxi): maybe consider to override the decision for the agent on
      // the neighbor lane.
      if (debug_str) {
        absl::StrAppend(debug_str, "XLANE_NUDGE: no consider side agent; ");
      }
      return false;
    }
    case pb::ScenarioRecognition::INLANE_NUDGE: {
      return ShouldConsiderNeighbourLaneAgentWhenInlaneNudge(
          reasoning_info, active_lat_gap_adjust_meta,
          agent_along_track_speed_mps, debug_str);
    }
    default:
      DCHECK(false) << "Unsupported scenario identify type.";
      absl::StrAppend(debug_str, "No consider in unsupported scenario; ");
      return false;
  }
}
}  // namespace

bool SideAgentPathReasoner::PostReason(
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  DCHECK(!blocking_state_data.reasoning_info.is_in_current_lane());
  intention_result.reasoner_id = reasoner_id_;
  intention_result.is_overtaken = false;

  if (behavior_type_ == pb::CROSS_LANE) {
    if (active_lat_gap_adjust_meta_.intended_lat_move_direction !=
        math::pb::Side::kOn) {
      intention_result.required_lat_gap.comfort_required_lateral_gap +=
          kDefensiveLatGapIncrementDuringLaneChangeInMeter;
      absl::StrAppend(&intention_result.debug_str,
                      "ego is changing lane, increase comfort lat gap; ");
    }
  } else {
    AdjustLateralGapBasedOnLateralMoveDirection(
        lane_change_info_, blocking_state_data.object_id,
        intention_result.lateral_decision, active_lat_gap_adjust_meta_,
        agent_inlane_state.tracked_state.inlane_param.speed_mps,
        blocking_state_data.reasoning_info
            .is_interested_agent_during_lane_change_or_abort(),
        intention_result.required_lat_gap, &intention_result.debug_str);
  }
  // Adjust the lane boundary imutability.
  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_start_arclength_m,
                       agent_inlane_state.tracked_state.inlane_param
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  // Apply post reasoner for side agent with scenario_identify_result.
  if (behavior_type_ != pb::CROSS_LANE &&
      !ShouldConsiderNeighbourLaneAgent(
          blocking_state_data.reasoning_info, active_lat_gap_adjust_meta_,
          scenario_identify_result_,
          agent_inlane_state.tracked_state.inlane_param.along_track_speed_mps,
          &(intention_result.debug_str))) {
    // Add soft nudge for normal scene.
    intention_result.required_lat_gap.critical_required_lateral_gap = 0.2;
    absl::StrAppend(&intention_result.debug_str, "set cri_rlg = 0.2; ");
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  } else if (intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE) {
    // Add normal nudge constraint when lateral decision is nudge.
    AddMultipleNominalNudgeConstraintOnPrediction(
        agent_inlane_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  DCHECK_EQ(intention_result.lateral_decision, pb::SnapshotIntention::IGNORE);
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          agent_inlane_state.tracked_state.inlane_param.center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());
  if (blocking_state_data.reasoning_info.is_large_vehicle()) {
    // Increases comfort req lat gap for large vehicle.
    intention_result.required_lat_gap.comfort_required_lateral_gap = 1.2;
    absl::StrAppend(&intention_result.debug_str,
                    "large_veh increase com_rlg 1.2; ");
  }
  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                    blocking_state_data.reasoning_info))) {
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_deci ignore treat as static; ");
    AddCurrentPoseNominalConstraint(agent_inlane_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return true;
  }

  AddMultipleNominalNudgeConstraintOnPrediction(
      agent_inlane_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

bool SideAgentPathReasoner::PostReason(
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const TrajectoryIntentionMetaData& /* trajectory_intention_data */,
    IntentionResultMetaData& intention_result) const {
  DCHECK(!intention_result.is_static);
  DCHECK(!blocking_state_data.reasoning_info.is_in_current_lane());
  intention_result.reasoner_id = reasoner_id_;
  intention_result.is_overtaken = false;

  if (behavior_type_ == pb::CROSS_LANE) {
    if (active_lat_gap_adjust_meta_.intended_lat_move_direction !=
        math::pb::Side::kOn) {
      intention_result.required_lat_gap.comfort_required_lateral_gap +=
          kDefensiveLatGapIncrementDuringLaneChangeInMeter;
      absl::StrAppend(&intention_result.debug_str,
                      "ego is changing lane, increase comfort lat gap; ");
    }
  } else {
    AdjustLateralGapBasedOnLateralMoveDirection(
        lane_change_info_, blocking_state_data.object_id,
        intention_result.lateral_decision, active_lat_gap_adjust_meta_,
        object_occupancy_state.current_snapshot_info()
            .object_occupancy_param()
            .speed_mps,
        blocking_state_data.reasoning_info
            .is_interested_agent_during_lane_change_or_abort(),
        intention_result.required_lat_gap, &intention_result.debug_str);
  }

  if (intention_result.lateral_decision != pb::SnapshotIntention::IGNORE &&
      ShouldStayInLane(blocking_state_data,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_start_arclength_m,
                       object_occupancy_state.current_snapshot_info()
                           .object_occupancy_param()
                           .full_body_end_arclength_m)) {
    intention_result.solid_line_lane_boundary_decision =
        LaneBoundaryDecision::kStayInLane;
  }

  // Apply post reasoner for side agent with scenario_identify_result.
  if (behavior_type_ != pb::CROSS_LANE &&
      !ShouldConsiderNeighbourLaneAgent(
          blocking_state_data.reasoning_info, active_lat_gap_adjust_meta_,
          scenario_identify_result_,
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .along_track_speed_mps,
          &(intention_result.debug_str))) {
    intention_result.required_lat_gap.critical_required_lateral_gap = 0.2;
    absl::StrAppend(&intention_result.debug_str, "set cri_rlg = 0.2; ");
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  } else if (intention_result.lateral_decision !=
             pb::SnapshotIntention::IGNORE) {
    AddMultipleNominalNudgeConstraintOnPrediction(
        object_occupancy_state, intention_result.lateral_decision,
        intention_result.nudge_index_ranges, intention_result.required_lat_gap,
        intention_result);
    return true;
  }

  DCHECK_EQ(intention_result.lateral_decision, pb::SnapshotIntention::IGNORE);
  const pb::SnapshotIntention::PassState tracked_pass_decision =
      GetPassStateWithAgentTrackedState(
          object_occupancy_state.current_snapshot_info()
              .object_occupancy_param()
              .center_line_side,
          blocking_state_data.reasoning_info.tracked_side_to_ego());
  if (blocking_state_data.reasoning_info.is_large_vehicle()) {
    // Increases comfort req lat gap for large vehicle.
    intention_result.required_lat_gap.comfort_required_lateral_gap = 1.2;
    absl::StrAppend(&intention_result.debug_str,
                    "large_veh increase com_rlg 1.2; ");
  }
  if (tracked_pass_decision != pb::SnapshotIntention::IGNORE &&
      (ShouldTreatAgentAsStationary(blocking_state_data.tracked_blocking_info,
                                    blocking_state_data.reasoning_info))) {
    intention_result.is_overtaken = false;
    intention_result.is_static = true;
    intention_result.lateral_decision = tracked_pass_decision;
    absl::StrAppend(&intention_result.debug_str,
                    "lat_deci ignore treat as static; ");
    AddCurrentPoseNominalConstraint(object_occupancy_state,
                                    intention_result.lateral_decision,
                                    intention_result);
    return true;
  }

  AddMultipleNominalNudgeConstraintOnPrediction(
      object_occupancy_state, intention_result.lateral_decision,
      intention_result.nudge_index_ranges, intention_result.required_lat_gap,
      intention_result);
  return true;
}

std::unique_ptr<RepulsionMetaData> SideAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const AgentInLaneStates& agent_inlane_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::unordered_map<int, LateralBlockingStateMetaData>&
    /* trajectory_blocking_state_data_map */,
    const std::optional<pb::Path>& /* reusable_last_path */,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap,
    const std::optional<
        LaneChangeExecutionInfo>& /* lane_change_execution_info */,
    std::string& debug_str) const {
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_SideAgentPathReasoner_PreRepulsionReason);
  // Setup repulsion meta data for side agent with predicted trajectories.
  speed::Profile profile_upper_bound, profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  const auto& agent_traj_states =
      FIND_OR_DIE_WITH_PRINT(agent_inlane_state.predicted_trajectories,
                             agent_inlane_state.primary_trajectory_id);
  const int64_t first_timestamp =
      agent_traj_states.predicted_states.front().timestamp;
  for (const auto& state : agent_traj_states.predicted_states) {
    const auto& inlane_param = state.inlane_state.inlane_param;
    if (CanEgoBrakeToYieldSnapshot(
            ego_param_, inlane_param, kComfortAbsoluteBrakeInMpss,
            blocking_state_data.reasoning_info.is_oncoming(),
            static_cast<double>(state.timestamp - first_timestamp) * 1e-3)) {
      continue;
    }
    const auto& pose = inlane_param.pose;
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    agent_center_path_points.emplace_back(pose.center_2d());
    profile_upper_bound.emplace_back(math::Ms2Sec(state.timestamp),
                                     state.odom_m,
                                     state.inlane_state.inlane_param.speed_mps,
                                     /*a_in=*/0.0, /*j_in=*/0.0);
    if (state.timestamp - first_timestamp > kMaxDurationToConsiderPrediction) {
      break;
    }
  }
  profile_lower_bound = profile_upper_bound;

  // Simplifying path points to avoid duplication.
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(
          std::move(agent_center_path_points),
          /*max_distance=*/math::constants::kDefaultSimplifyThreshold,
          /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    LOG(INFO)
        << " SideRepulsion "
        << " Add repulsion fail: agent center path point < 2! The agent id is "
        << agent_inlane_state.object_id;
    debug_str += "agent_center_path invalid";
    return nullptr;
  }
  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    LOG(INFO) << " SideRepulsion "
              << " Add repulsion fail: agent center path length < 0.2m! The "
                 "agent id is "
              << agent_inlane_state.object_id;
    debug_str += "agent_center_path too short";
    return nullptr;
  }
  const double tracked_heading =
      agent_inlane_state.tracked_state.inlane_param.pose.heading();
  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kEither,
      agent_inlane_state.tracked_state.inlane_param.pose.contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), tracked_heading,
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(agent_inlane_state.object_id,
                            pb::ObjectSourceType::kTrackedObject)),
      agent_inlane_state.primary_trajectory_id,
      /*path_risk_mitigation_mode_in=*/false);
}

std::unique_ptr<RepulsionMetaData> SideAgentPathReasoner::PreRepulsionReason(
    const RobotStateSnapshot& /* robot_state_snapshot */,
    const ObjectOccupancyState& object_occupancy_state,
    const LateralBlockingStateMetaData& blocking_state_data,
    const std::optional<pb::Path>& /* reusable_last_path */,
    const std::optional<speed::Profile>& /* reusable_last_speed_profile */,
    double repulsion_required_lat_gap, std::string& debug_str) const {
  TRACE_EVENT_SCOPE(planner,
                    RunPathReasoning_SideAgentPathReasoner_PreRepulsionReason);
  // Setup repulsion meta data for side agent with predicted trajectories.
  speed::Profile profile_upper_bound, profile_lower_bound;
  math::geometry::Polyline2d agent_center_path_points;
  const auto& agent_traj_states = FIND_OR_DIE_WITH_PRINT(
      object_occupancy_state.predicted_trajectory_occupancy_states(),
      object_occupancy_state.primary_trajectory_id());
  const int64_t first_timestamp =
      agent_traj_states.predicted_states().front().timestamp();
  for (const auto& state : agent_traj_states.predicted_states()) {
    const auto& inlane_param = state.object_occupancy_param();
    if (CanEgoBrakeToYieldSnapshot(
            ego_param_, inlane_param, kComfortAbsoluteBrakeInMpss,
            blocking_state_data.reasoning_info.is_oncoming(),
            static_cast<double>(state.timestamp() - first_timestamp) * 1e-3)) {
      continue;
    }
    const auto& pose = object_occupancy_state.pose();
    if (!agent_center_path_points.empty() &&
        math::NearZero(math::geometry::Distance(
            pose.center_2d(), agent_center_path_points.back()))) {
      continue;
    }
    DCHECK(state.odom_m().has_value());
    agent_center_path_points.emplace_back(pose.center_2d());
    profile_upper_bound.emplace_back(
        /*t_in=*/math::Ms2Sec(state.timestamp()),
        /*s=*/state.odom_m().value(),
        /*v=*/inlane_param.speed_mps,
        /*a_in=*/0.0, /*j_in=*/0.0);
    if (state.timestamp() - first_timestamp >
        kMaxDurationToConsiderPrediction) {
      break;
    }
  }
  profile_lower_bound = profile_upper_bound;

  // Simplifying path points to avoid duplication.
  math::geometry::Polyline2d simplified_agent_center_path_points =
      math::geometry::Simplify(
          std::move(agent_center_path_points),
          /*max_distance=*/math::constants::kDefaultSimplifyThreshold,
          /*assert_no_self_intersection=*/false);
  if (simplified_agent_center_path_points.size() < 2) {
    LOG(INFO)
        << " SideRepulsion "
        << " Add repulsion fail: agent center path point < 2! The agent id is "
        << object_occupancy_state.object_id();
    debug_str += "agent_center_path invalid";
    return nullptr;
  }
  math::geometry::PolylineCurve2d simplified_agent_center_path(
      std::move(simplified_agent_center_path_points));
  if (simplified_agent_center_path.GetTotalArcLength() <
      kMinLengthToAddRepulsion) {
    LOG(INFO) << " SideRepulsion "
              << " Add repulsion fail: agent center path length < 0.2m! The "
                 "agent id is "
              << object_occupancy_state.object_id();
    debug_str += "agent_center_path too short";
    return nullptr;
  }
  const double tracked_heading = object_occupancy_state.pose().heading();
  return std::make_unique<RepulsionMetaData>(
      RepulsionMetaData::RepulsionDirection::kEither,
      object_occupancy_state.pose().contour().polygon(),
      std::move(simplified_agent_center_path), std::move(profile_lower_bound),
      std::move(profile_upper_bound), tracked_heading,
      /*add_only_overtaking=*/true, /*ignore_if_nudge_failed_in=*/false,
      /*add_only_not_overtaking_in=*/false, /*ignore_if_nudge_yield_in=*/false,
      path::BoundaryStrength::kModerate,
      /*strength_ratio_in=*/std::nullopt, repulsion_required_lat_gap,
      reasoner_id_,
      ToProto(TypedObjectId(object_occupancy_state.object_id(),
                            pb::ObjectSourceType::kTrackedObject)),
      object_occupancy_state.primary_trajectory_id(),
      /*path_risk_mitigation_mode_in=*/false);
}
}  // namespace path
}  // namespace planner
