#include "planner/speed/reasoning/reasoner/cautious_driving_reasoner/cautious_driving_reasoner.h"

#include <algorithm>
#include <cmath>
#include <limits>
#include <optional>
#include <string>
#include <unordered_set>
#include <utility>
#include <vector>

#include <absl/strings/str_cat.h>

#include "exception_handler/exception_handler_gflags.h"
#include "exception_handler/exception_handler_main/trajectory_result.h"
#include "exception_handler/exception_handler_main/util/environment_util.h"
#include "hdmap_protos/geometry.pb.h"
#include "hdmap_protos/lane.pb.h"
#include "math/constants.h"
#include "math/interpolation.h"
#include "math/math_util.h"
#include "math/range.h"
#include "math/unit_conversion.h"
#include "planner/decoupled_maneuvers/required_lateral_gap/control_error.h"
#include "planner/path/reasoning//agent_intention/agent_intention_constants.h"
#include "planner/planning_gflags.h"
#include "planner/speed/discomforts/discomfort_varying.h"
#include "planner/speed/overlap/overlap_lib_util.h"
#include "planner/speed/reasoning/agent_policy.h"
#include "planner/speed/reasoning/prediction_decision.h"
#include "planner/speed/reasoning/prediction_decision_maker.h"
#include "planner/speed/reasoning/reasoner/cautious_driving_reasoner/cautious_driving_reasoner_util.h"
#include "planner/speed/reasoning/reasoning_util.h"
#include "planner/speed/reasoning_input/reasoning_object.h"
#include "planner/speed/reasoning_input/traffic_rules/crosswalk_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/lane_info_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/speed_limit_traffic_rule.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_light_info.h"
#include "planner/speed/reasoning_input/trajectory_info.h"
#include "planner/speed/reasoning_input/world_context.h"
#include "planner/speed/reference/cautious_driving_limiter.h"
#include "planner/world_model/snapshot/robot_state.h"
#include "planner_protos/behavior_common_type.pb.h"
#include "planner_protos/overlap.pb.h"
#include "planner_protos/prediction_decision.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "planner_protos/speed_reasoner.pb.h"
#include "planner_protos/speed_reasoning_debug.pb.h"
#include "planner_protos/speed_seed.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "prediction/maneuver_proposal/reference_line/reference_line_util.h"
#include "rt_event/rt_event.h"
#include "voy_protos/math.pb.h"
#include "voy_protos/tracked_objects.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {
namespace speed {
namespace reasoner {
namespace {

constexpr double kEgoSpeedLimitForCreepingUnderCriticalLateralGapInMps =
    math::KmphToMps(5.0);
constexpr double kEgoSpeedLimitInExtremeNarrowPassageInMps =
    math::KmphToMps(6.0);
constexpr double kEgoSpeedLimitInIntermediateNarrowPassageInMps =
    math::KmphToMps(10.0);
constexpr double kExtremeNarrowPassageClearanceThresholdInMeter = 0.6;
constexpr double kIntermediateNarrowPassageClearanceThresholdInMeter = 0.8;
constexpr int kFakeCutInPredictedTrajectoryID = 888;
constexpr double kEntryEgoPathTimeForFakeCutInPredictedTrajectoryInSec = 1.5;
constexpr double
    kMaxFutureDistToLaneBoundaryForLargeVehicleDynamicProximityInMeter = 0.1;
constexpr double
    kMinFutureDistToLaneBoundaryForLargeVehicleDynamicProximityInMeter = -0.3;
constexpr double kMaxSpeedLessThanLargeVehicleForDynamicProximityInMps = 1.5;
constexpr double kMinRemainingSpeedRatioForDynamicProximity = 0.85;
constexpr double kMaxTotalLaneArcLengthForBeingConsideredShortInMeter = 30.0;
constexpr int64_t kRemoveStaleAgentTimeThresholdInMs = 1000;  // 1s
constexpr double kMinAccelForRemoteSpeedLimiterInMpss = -5.0;
constexpr double kMaxSpeedForRemoteSpeedLimiterInMps = 16.67;
constexpr double kMaintainSpeedAccelForRemoteSpeedLimiterInMpss = -2.0;
constexpr double kAlmostGapAlignedLongitudinalDistThresholdInMeter = 1.0;
constexpr double kAlmostGapAlignedMaxYieldActiveTimeInSec = 3.0;
constexpr double kMaxDistToConsiderAgentCloseToJunctionInMeter = 35.0;
constexpr double kMaxDistanceToAddCautiousForNewDetectAgentInMeter = 1.5;
constexpr double kMinSpeedToConsiderVRUMovingCloseInMps = 0.3;
constexpr double kMinLongitudinalGapForNonreactiveVRUCautiousInMeters = 50.0;
// Parameters for avoid squeeze cautious driving.
constexpr double
    kAgentAheadOfFrontBumperThresholdForAvoidSqueezeCautiousInMeter = 2.5;
constexpr double
    kAgentDistanceToLaneBoundaryThresholdForAvoidSqueezeCautiousInMeter = 0.8;
constexpr double kTimeHorizonForEstimatingAgentMotionInSec = 2.0;
constexpr double kMinLaneEncroachmentThresholdForAvoidSqueezeInMeter = 0.7;
constexpr double kRequiredLateralGapForMildAvoidSqueezeInMeter = 0.6;
constexpr double kRequiredLateralGapForHarshAvoidSqueezeInMeter = 0.2;
constexpr double kExtraYieldDistanceFromAgentHeadInMeter = 2.0;
constexpr double kProjectedHeadingDiffRatioThreshold = 2.0;
constexpr double kAvoidSqueezeRelHeadingThresholdInRad = 0.035;  // 2.0 Deg

// Returns true if any debug for scenes or predicted trajectories are added.
bool IsAgentDebugUpdated(
    const pb::CautiousDrivingAgentDebug& cautious_driving_agent_debug) {
  return cautious_driving_agent_debug.debug_for_scenes_size() > 0 ||
         cautious_driving_agent_debug.predicted_trajectory_debugs_size() > 0;
}

// Filters pedestrians which needed to add reference limit at high agent density
// scenarios. Returns true if we need to consider.
bool ShouldConsiderAgentAtHighAgentDensity(
    const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config) {
  if (!reasoning_object.is_pedestrian()) return false;

  // Make some filtering on the object.
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const pb::LongitudinalRange& proximity_ra_arclength =
      object_proximity_info.projected_ra_arc_length();

  if (object_proximity_info.out_of_path_range() ||
      !math::IsValidRange(math::Range(proximity_ra_arclength.start(),
                                      proximity_ra_arclength.end()))) {
    return false;
  }

  if (reasoning_object.is_fully_behind()) {
    // If the agent is behind ego, we will ignore it.
    return false;
  }

  // Returns true if the agent is very close to ego path or is close and
  // approaching ego path.
  return object_proximity_info.abs_lateral_gap() <
         config.max_lateral_gap_for_ped_at_high_agent_density_in_meter();
}

// Returns a pointer of CautiousDrivingPredictedTrajectoryDebug corresponding
// to the |object_trajectory_id|. If there is none, a new one is added and
// returned. Otherwise, the existing one is returned.
// Note: This function should alway return a non-null pointer.
pb::CautiousDrivingPredictedTrajectoryDebug* AddOrFindPredictedTrajectoryDebug(
    const ObjectTrajectoryId& object_trajectory_id,
    pb::CautiousDrivingAgentDebug& agent_debug) {
  DCHECK_EQ(object_trajectory_id.object_id, agent_debug.object_id());
  // Find an existing one and return it.
  if (agent_debug.predicted_trajectory_debugs_size() != 0) {
    auto last_predicted_trajectory_debug_iter =
        agent_debug.mutable_predicted_trajectory_debugs()->rbegin();
    if (last_predicted_trajectory_debug_iter->trajectory_id() ==
        object_trajectory_id.trajectory_id) {
      return &(*last_predicted_trajectory_debug_iter);
    }
  }

  // There is none. We add one and it is returned.
  pb::CautiousDrivingPredictedTrajectoryDebug* predicted_trajectory_debug =
      agent_debug.add_predicted_trajectory_debugs();
  predicted_trajectory_debug->set_trajectory_id(
      object_trajectory_id.trajectory_id);

  return predicted_trajectory_debug;
}

void UpdateCautiousDrivingReasonerSeed(
    const pb::CautiousDrivingReasonerSeed& previous_iter_seed,
    pb::CautiousDrivingReasonerSeed& current_iter_mutable_seed,
    int64_t curr_timestamp) {
  const auto& prev_obj_id_to_ego_speed_record =
      previous_iter_seed.obj_id_to_ego_speed_record();
  auto& curr_obj_id_to_ego_speed_record =
      *current_iter_mutable_seed.mutable_obj_id_to_ego_speed_record();
  for (const auto& [obj_id, ego_speed_tracking] :
       prev_obj_id_to_ego_speed_record) {
    if (curr_timestamp - ego_speed_tracking.latest_cautious_timestamp() <
        kRemoveStaleAgentTimeThresholdInMs) {
      curr_obj_id_to_ego_speed_record[obj_id] = ego_speed_tracking;
    }
  }
}

// Returns the strength according to the minimum lateral gap of OverlapRegion.
// The lateral strength estimates the potential collision risk according to
// lateral gap. For example, the smaller lateral gap，the greater strength for
// cut-in scenario.
double ComputeLateralStrength(const AgentPolicy& agent_policy,
                              const pb::CautiousDrivingReasonerConfig& config) {
  const double min_abs_lateral_gap =
      GetOverlapRegionMinAbsLateralGap(agent_policy.OverlapRegion());

  // When the predicted trajectory encroaches Ego's path lightly, Ego should
  // react lightly. Otherwise, Ego react heavily. The critical required lateral
  // gap is a switch point.
  const double comfort_required_lateral_gap =
      agent_policy.reasoning_object().ComfortRequiredLateralGap();
  const double critical_required_lateral_gap =
      agent_policy.reasoning_object().CriticalRequiredLateralGap();
  if (min_abs_lateral_gap > critical_required_lateral_gap) {
    DCHECK_LE(min_abs_lateral_gap,
              comfort_required_lateral_gap + math::constants::kEpsilon)
        << agent_policy.OverlapRegion().DebugString();
    return math::GetLinearInterpolatedY(
        /*x1=*/critical_required_lateral_gap,
        /*x2=*/comfort_required_lateral_gap,
        /*y1=*/config.critical_required_lateral_gap_cautious_strength(),
        /*y2=*/0.0,
        /*x_eval=*/min_abs_lateral_gap);
  }
  return math::GetLinearInterpolatedY(
      /*x1=*/0.0, /*x2=*/critical_required_lateral_gap,
      /*y1=*/1.0,
      /*y2=*/config.critical_required_lateral_gap_cautious_strength(),
      /*x_eval=*/min_abs_lateral_gap);
}

// Returns the cautious strength that is used to adjust the speed given by the
// CautiousDrivingReasoner.
double ComputeCautiousStrength(const AgentPolicy& agent_policy,
                               const pb::CautiousDrivingReasonerConfig& config,
                               std::string& debug_string) {
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  if (overlap_region.motion_type() == pb::OVERLAP_MOTION_CROSSING) {
    // For crossing overlaps, the minimum lateral gaps usually are zero and the
    // lateral strength is not useful.
    absl::StrAppendFormat(&debug_string, "policy_strength:%.2f.",
                          agent_policy.strength());
    return agent_policy.strength();
  }

  DCHECK(  // NOLINT, FP when checking macro DCHECK
      overlap_region.motion_type() == pb::OVERLAP_MOTION_SAME_DIRECTION ||
      overlap_region.motion_type() == pb::OVERLAP_MOTION_ONCOMING)
      << pb::OverlapMotionType_Name(overlap_region.motion_type());
  // Compute cautious strength based on lateral gap for same-direction and
  // oncoming overlaps.
  const double lateral_strength = ComputeLateralStrength(agent_policy, config);
  const pb::CautiousDrivingReason cautious_driving_reason =
      agent_policy.cautious_driving_reason();
  if (cautious_driving_reason == pb::CautiousDrivingReason::ONCOMING_CYCLIST) {
    // TODO(hezhihang): Consider more factors to compute speed limit.
    absl::StrAppendFormat(&debug_string,
                          "policy_strength:%.2f,lat_strength:%.2f.",
                          agent_policy.strength(), lateral_strength);
    return agent_policy.strength() * lateral_strength;
  }

  const auto& first_overlap_slice =
      agent_policy.OverlapRegion().overlap_slices().at(0);
  // We use overlap's timestamp to determine the cautious strength. We assign
  // weaker cautious strength for future timestamps due to lower prediction
  // quality over time.
  const double relative_time_strength = math::GetLinearInterpolatedY(
      /*x1=*/0.0, /*x2=*/constants::kTrajectoryHorizonInSec,
      /*y1=*/1.0,
      /*y2=*/0.0,
      /*x_eval=*/first_overlap_slice.relative_time_in_sec());

  // Take these strength factors into consideration.
  absl::StrAppendFormat(
      &debug_string,
      "policy_strength:%.2f,lat_strength:%.2f,time_strength:%.2f.",
      agent_policy.strength(), lateral_strength, relative_time_strength);

  return agent_policy.strength() * lateral_strength * relative_time_strength;
}

// Returns the speed zone arclength range. The arclength starts from the Ego's
// rear axle.
math::Range1d ComputeSpeedZoneArclengthRange(
    const pb::OverlapRegion& overlap_region,
    bool should_use_strict_overlap = false) {
  DCHECK_NE(overlap_region.motion_type(), pb::OVERLAP_MOTION_UNKNOWN);

  if (should_use_strict_overlap) {
    return {GetStrictOverlapStart(overlap_region),
            GetStrictOverlapEnd(overlap_region)};
  }

  return {GetPaddedOverlapStart(overlap_region),
          GetPaddedOverlapEnd(overlap_region)};
}

// Returns the speed limit range. The start value is the value at the min
// discomfort. The end value is the value at the max discomfort. The end value
// is set to infinity, because we do not want to change the reference at the max
// discomfort.
math::Range1d ComputeSpeedLimitRange(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    const math::Range1d& speed_zone_range,
    const pb::OverlapRegion& /* overlap_region */, double strength,
    const pb::CautiousDrivingReasonerConfig& config) {
  DCHECK_GE(strength, 0.0);
  DCHECK_LE(strength, 1.0);
  // When reverse driving ego should keep for cautious driving. Thus, the range
  // is set to [inf, inf] in order to indicate that the reference should not be
  // updated.
  if (trajectory_info.motion_mode() == planner::pb::MotionMode::BACKWARD) {
    return {std::numeric_limits<double>::infinity(),
            std::numeric_limits<double>::infinity()};
  }

  const double ego_max_speed =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(speed_zone_range);
  // The lane's (lane 58025, etc) speed limit is lower than the minimum speed
  // that Ego should keep for cautious driving. Thus, the range is set to [inf,
  // inf] in order to indicate that the reference should not be updated.
  if (ego_max_speed <= config.should_stopping_scene_ego_min_speed_in_mps() ||
      math::IsApprox(config.should_stopping_scene_ego_min_speed_in_mps(),
                     ego_max_speed)) {
    return {std::numeric_limits<double>::infinity(),
            std::numeric_limits<double>::infinity()};
  }

  if (agent_policy.reasoning_object().IsUnknownObject()) {
    const double speed_percentage = math::LinearInterpolate(
        1.0, config.lowest_ego_max_speed_percentage_for_unknown_object(),
        strength);
    return {ego_max_speed * speed_percentage, ego_max_speed};
  }

  return {math::LinearInterpolate(
              ego_max_speed,
              config.should_stopping_scene_ego_min_speed_in_mps(), strength),
          ego_max_speed};
}

struct ClampedLinearSpeedCurveConfig {
  double max_slow_down_lat_gap_to_lane_boundary;
  double min_slow_down_lat_gap_to_lane_boundary;
  double speed_lower_bound;
  double speed_upper_bound;
};

// The lat-gap-related speed curve is defined by the speed_curve_config. The
// speed curve is a clamped linear function, Given the abs_lat_gap, this
// function help computes the desired speed limit.
double ComputeDesiredSpeedLimit(
    const ClampedLinearSpeedCurveConfig& speed_curve_config,
    double abs_lat_gap) {
  DCHECK_LE(abs_lat_gap,
            speed_curve_config.max_slow_down_lat_gap_to_lane_boundary);
  return math::GetLinearInterpolatedY(
      /*x1=*/speed_curve_config.min_slow_down_lat_gap_to_lane_boundary,
      /*x2=*/speed_curve_config.max_slow_down_lat_gap_to_lane_boundary,
      /*y1=*/speed_curve_config.speed_lower_bound,
      /*y2=*/speed_curve_config.speed_upper_bound,
      /*x_eval=*/abs_lat_gap);
}

// Returns true if an VRU (cyclist or pedestrian) is approaching Ego path
// without any overlapping predicted trajectory. The conditions in this function
// want to capture all features that the target scene has.
bool IsVruApproachingEgoPathWithoutOverlappingPredictedTrajectory(
    const ReasoningObject& reasoning_object, bool has_made_prediction_decision,
    double ego_front_bumper_ra_arclength,
    const pb::DynamicProximityAgentConfig& dynamic_proximity_config) {
  // If there is prediction decision, there is overlap region. The predicted
  // trajectory has been filtered out during preparing input of reasoning.
  if (!reasoning_object.is_pedestrian_or_cyclist() ||
      reasoning_object.IsStationary() || has_made_prediction_decision) {
    return false;
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  // In order to reduce the number of unnecessary reduction, filter out VRUs
  // whose lateral gap is large or lateral speed is small. These VRUs have less
  // risk.
  if (object_proximity_info.abs_lateral_gap() >
          dynamic_proximity_config
              .max_lateral_gap_for_dynamic_proximity_in_meter() ||
      std::abs(object_proximity_info.signed_lateral_speed()) <
          dynamic_proximity_config
              .min_lateral_speed_for_dynamic_proximity_in_mps()) {
    return false;
  }

  // The VRU should be entering Ego path not exiting.
  if (object_proximity_info.signed_lateral_gap() *
          object_proximity_info.signed_lateral_speed() >=
      0) {
    return false;
  }

  // Ignore VRU who wants to cut-in behind Ego.
  if (object_proximity_info.projected_ra_arc_length().end() <
      ego_front_bumper_ra_arclength) {
    return false;
  }

  return true;
}

// Return true is the agent if laterally close to ego path's lane boundaries,
// driving toward ego and is likely to overtake or be overtaken by ego.
// The conditions in this function want to capture all features that the target
// scene has.
bool ShouldConsiderCloseToLaneBoundaryLimits(
    const WorldContext& /* world_context */,
    const ReasoningObject& reasoning_object, bool has_made_prediction_decision,
    double ego_speed,
    const pb::DynamicProximityAgentConfig& /* dynamic_proximity_config */,
    pb::CautiousDrivingDebugForScene::DynamicProximitySceneRecognition&
        scene_recogination) {
  // If there is a prediction decision, then we will depend on speed
  // constraints. This dynamic cautious for agent close to lane boundaries only
  // should take effects when there is no cut-in prediction recall.
  if (has_made_prediction_decision) {
    scene_recogination = pb::CautiousDrivingDebugForScene::HAS_MADE_DECISION;
    return false;
  }
  if (!reasoning_object.is_laterally_encroach_ego_lane_boundary()) {
    scene_recogination =
        pb::CautiousDrivingDebugForScene::WILL_NOT_LATERALLY_ENCROACH;
    return false;
  }
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  // It is weird to brake for behind agents, only when it is ahead of ego front
  // bumper should we take actions.
  if (reasoning_object.IsBehindEgoLeadingBumper()) {
    scene_recogination = pb::CautiousDrivingDebugForScene::BEHIND_EGO;
    return false;
  }
  // No need to add cautious for those fully ahead and faster than ego agents.
  if (reasoning_object.IsFullyAhead() &&
      proximity_info.signed_longitudinal_speed() >= ego_speed) {
    scene_recogination = pb::CautiousDrivingDebugForScene::FASTER_THAN_EGO;
    return false;
  }
  // TODO(speed): As we currently add proximity constraint for the close lane
  // boundary cautious driving instead of static speed limit, there is no need
  // to further check the spatial-temporal conflicts in reasoning level as
  // follows. Remove lines below when refactoring for small vehicles is done.
  if (reasoning_object.IsLargeVehicle()) return true;
  // The longitudinal gap between ego and agent is decreasing, check the
  // distance to see whether we need to slow down.
  if (reasoning_object.IsFullyAhead() &&
      proximity_info.signed_longitudinal_speed() < ego_speed) {
    // These two parameters are only used here to control the size of the
    // attention pool to avoid FP decelerations. We only want to add cautious to
    // the agents which are likely to be longitudinally close to ego.
    constexpr double kCheckAheadTime = 5.;
    constexpr double kCheckAheadDistance = 5.;
    const double relative_signed_longitudinal_speed =
        proximity_info.signed_longitudinal_speed() - ego_speed;
    DCHECK_LT(relative_signed_longitudinal_speed, 0.);
    const double future_relative_start =
        proximity_info.projected_ra_arc_length().start() +
        kCheckAheadTime * relative_signed_longitudinal_speed;
    if (future_relative_start > kCheckAheadDistance) {
      scene_recogination =
          pb::CautiousDrivingDebugForScene::WILL_NOT_LONGITUDINALLY_CLOSE;
      return false;
    }
  }

  return true;
}

// Returns the speed limit range for dynamic proximity.
math::Range1d ComputeDynamicProximitySpeedLimitRange(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::DynamicProximityAgentConfig& dynamic_proximity_config) {
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  const double ego_max_speed = trajectory_info.GetEgoMaxSpeedInArclengthRange(
      proximity_info.projected_ra_arc_length());
  const double min_speed = std::min(
      dynamic_proximity_config.min_speed_for_dynamic_proximity_in_mps(),
      ego_max_speed);
  const double min_discomfort_speed_limit = math::GetLinearInterpolatedY(
      /*x1=*/reasoning_object.ComfortRequiredLateralGap(),
      /*x2=*/
      dynamic_proximity_config.max_lateral_gap_for_dynamic_proximity_in_meter(),
      /*y1=*/min_speed, /*y2=*/ego_max_speed, proximity_info.abs_lateral_gap());

  return {min_discomfort_speed_limit, ego_max_speed};
}

// Appends debug information related to different cautious scenes to
// CautiousDrivingAgentDebug. |debug_string| can be whatever useful
// information for debugging you think. |scene| indicates this debug
// information is added by which cautious scene, like occlusion, static
// proximity. This function should be called firstly. Then, if we have tried
// to update the reference profile limit, we should call the following
// function |PopulateCautiousDrivingLimiterParametersDebug| to populate the
// parameters used to update reference limit. NOTE: The returned reference
// should be only used as input of function
// |PopulateCautiousDrivingLimiterParametersDebug| to populate related debug
// info.
pb::CautiousDrivingDebugForScene& AppendCautiousDrivingDebugForScene(
    std::string&& debug_string,
    pb::CautiousDrivingDebugForScene::AgentCautiousScene scene,
    pb::CautiousDrivingAgentDebug& cautious_driving_agent_debug) {
  pb::CautiousDrivingDebugForScene* debug_for_scene =
      cautious_driving_agent_debug.add_debug_for_scenes();
  debug_for_scene->set_cautious_scene(scene);
  debug_for_scene->set_debug_str(std::move(debug_string));
  return *debug_for_scene;
}

// Populates debug info of one OverlapRegion of agent's predicted trajectory.
void PopulatePredictedTrajectoryDebug(
    const ObjectTrajectoryId& object_trajectory_id,
    const math::Range1d& speed_limit_range,
    const math::Range1d& speed_zone_arclength_range, double strength,
    double min_accel, bool is_reference_profile_updated, int overlap_region_id,
    pb::CautiousDrivingReason cautious_driving_reason,
    std::string&& debug_string, pb::CautiousDrivingAgentDebug& agent_debug) {
  pb::CautiousDrivingPredictedTrajectoryDebug* predicted_trajectory_debug_ptr =
      AddOrFindPredictedTrajectoryDebug(object_trajectory_id, agent_debug);
  DCHECK(predicted_trajectory_debug_ptr != nullptr);
  pb::CautiousDrivingOverlapDebug* overlap_debug_ptr =
      predicted_trajectory_debug_ptr->add_overlaps();

  overlap_debug_ptr->set_overlap_region_id(overlap_region_id);
  overlap_debug_ptr->set_strength(strength);
  overlap_debug_ptr->set_cautious_driving_reason(cautious_driving_reason);
  overlap_debug_ptr->set_debug_str(std::move(debug_string));
  reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
      speed_limit_range.start_pos, speed_limit_range.end_pos,
      speed_zone_arclength_range.start_pos, speed_zone_arclength_range.end_pos,
      min_accel, is_reference_profile_updated,
      *(overlap_debug_ptr->mutable_limiter_parameters()));
}

// Populates some debug information related to agent itself used in
// CautiousDrivingReasoner.
[[maybe_unused]] void PopulateCautiousDrivingAgentDebug(
    const ReasoningObject& reasoning_object,
    pb::CautiousDrivingAgentDebug& cautious_driving_agent_debug) {
  cautious_driving_agent_debug.set_object_id(reasoning_object.id());
}

// Gets all predicted trajectories of interest for gap align of turning large
// vehicles.
std::vector<const planner::PredictedTrajectoryWrapper*>
GetPredictedTrajectoriesForTurningLargeVehicleGapAlign(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const pb::PredictionDecisionMakerConfig& pdm_config) {
  std::vector<const planner::PredictedTrajectoryWrapper*>
      predicted_trajectories_for_gap_align;
  predicted_trajectories_for_gap_align.reserve(
      object_prediction_map.at(reasoning_object.id()).size());
  for (const PredictionDecision& prediction_decision : prediction_decisions) {
    if (prediction_decision.agent_trajectory_info()
            .is_large_vehicle_turning_along_with_ego() &&
        prediction_decision.Nominal()) {
      predicted_trajectories_for_gap_align.push_back(
          &prediction_decision.agent_trajectory_info().predicted_trajectory());
    }
  }

  const double min_likelihood_for_nominal =
      pdm_config.vehicles().min_likelihood_for_nominal();
  const bool has_no_nominal_decisions =
      std::none_of(prediction_decisions.begin(), prediction_decisions.end(),
                   [](const PredictionDecision& prediction_decision) {
                     return prediction_decision.Nominal();
                   });
  // In case there is no nominal decision being made for the object, we do
  // additional check if it is under the targeted scope and add gap align as
  // needed.
  if ((prediction_decisions.empty() || has_no_nominal_decisions) &&
      reasoning_object.IsLargeVehicle()) {
    const std::vector<planner::PredictedTrajectoryWrapper>&
        predicted_trajectories =
            object_prediction_map.at(reasoning_object.id());
    for (const planner::PredictedTrajectoryWrapper& predicted_trajectory :
         predicted_trajectories) {
      // Only considering high-likelihood or primary trajectories.
      if (!predicted_trajectory.is_primary_trajectory() &&
          predicted_trajectory.Likelihood() < min_likelihood_for_nominal) {
        continue;
      }
      if (reasoning_util::IsEgoAboutToTurnAlongWithLargeVehicleInJunction(
              world_context, trajectory_info, reasoning_object,
              predicted_trajectory)) {
        predicted_trajectories_for_gap_align.push_back(&predicted_trajectory);
      }
    }
  }
  return predicted_trajectories_for_gap_align;
}

// Computes overlap regions for gap align of turning large vehicles.
std::vector<speed::pb::OverlapRegion>
ComputeOverlapRegionsForTurningLargeVehicleGapAlign(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const pb::CautiousDrivingReasonerConfig& config,
    const pb::PredictionDecisionMakerConfig& pdm_config,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  std::vector<const planner::PredictedTrajectoryWrapper*>
      predicted_trajectories_for_gap_align =
          GetPredictedTrajectoriesForTurningLargeVehicleGapAlign(
              world_context, trajectory_info, reasoning_object,
              prediction_decisions, object_prediction_map, pdm_config);

  OverlapComputer overlap_computer(
      trajectory_info.unextended_path_for_overlap(),
      world_context.ego_pose_timestamp());
  overlap_computer.PreComputePaddedPathData(
      config.turning_large_vehicle_gap_align().gap_align_required_lat_gap());

  std::vector<speed::pb::OverlapRegion> overlap_regions_for_gap_align;
  overlap_regions_for_gap_align.reserve(
      predicted_trajectories_for_gap_align.size());

  for (const planner::PredictedTrajectoryWrapper* predicted_trajectory :
       predicted_trajectories_for_gap_align) {
    std::vector<speed::pb::OverlapRegion>
        overlap_regions_for_single_trajectory =
            overlap_computer.ComputeOverlapRegionV2(
                *predicted_trajectory, config.turning_large_vehicle_gap_align()
                                           .gap_align_required_lat_gap());
    overlap_regions_for_gap_align.insert(
        overlap_regions_for_gap_align.end(),
        std::make_move_iterator(overlap_regions_for_single_trajectory.begin()),
        std::make_move_iterator(overlap_regions_for_single_trajectory.end()));
  }

  if (!predicted_trajectories_for_gap_align.empty() &&
      overlap_regions_for_gap_align.empty()) {
    if (cautious_driving_agent_debug != nullptr) {
      cautious_driving_agent_debug->set_object_id(reasoning_object.id());
      [[maybe_unused]] pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              /*debug_string=*/
              "No overlaps",
              pb::CautiousDrivingDebugForScene::
                  TURNING_ALONG_WITH_LARGE_VEHICLE,
              *cautious_driving_agent_debug);
    }
  }

  return overlap_regions_for_gap_align;
}

// Adds a gap align constraint for turning large vehicles.
bool AddGapAlignConstraintForLargeVehicleTurningOverlap(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const speed::pb::OverlapRegion& overlap_region,
    const traffic_rules::LaneInfoInLaneSequence& turning_lane_info,
    const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr) {
  if (overlap_region.overlap_slices().empty()) {
    return false;
  }
  const std::string constraint_id = absl::StrCat(
      GetOverlapRegionUniqueId(overlap_region), "-gap_align-lv-turn");
  ObjectTrajectoryId obj_traj_id(reasoning_object.id(),
                                 overlap_region.trajectory_id());
  ConstraintMutator mutator =
      constraint_creator_ptr->AddGapAlignSpeedConstraintFromOverlapRegion(
          pb::FenceType::kLead, pb::ReasonerId::CAUTIOUS_DRIVING, constraint_id,
          overlap_region, reasoning_object.id(), obj_traj_id.ToString(),
          /*type=*/pb::GapAlignType::kOthers,
          /*should_consider_in_seed_population=*/false);

  const bool is_short_turn_lane =
      turning_lane_info.lane->length() <
      kMaxTotalLaneArcLengthForBeingConsideredShortInMeter;
  // For short turn lanes, we set the three quarters point as the targeted
  // place to finish gap align to increase the chance of gap align success.
  // Otherwise, we set it to the mid point instead.
  const double target_gap_align_finish_point_ra_arclength =
      is_short_turn_lane ? 0.25 * (turning_lane_info.start_ra_arclength +
                                   3.0 * turning_lane_info.end_ra_arclength)
                         : 0.5 * (turning_lane_info.start_ra_arclength +
                                  turning_lane_info.end_ra_arclength);

  // Find the first overlap slice whose start pos is behind the target gap
  // align finish point. The relative time of this slice is supposed to be the
  // moment when the ego has to finish the yielding gap align.
  const auto yield_slice_iter =
      std::find_if(overlap_region.overlap_slices().begin(),
                   overlap_region.overlap_slices().end(),
                   [&world_context, target_gap_align_finish_point_ra_arclength](
                       const pb::OverlapSlice& slice) {
                     return GetPaddedOverlapStart(slice) +
                                world_context.ra_to_leading_bumper() >=
                            target_gap_align_finish_point_ra_arclength;
                   });

  // Find the first overlap slice whose end pos is behind the target gap
  // align finish point. The relative time of this slice is supposed to be the
  // moment when the ego has to finish the passing gap align.
  const auto pass_slice_iter =
      std::find_if(overlap_region.overlap_slices().begin(),
                   overlap_region.overlap_slices().end(),
                   [&world_context, target_gap_align_finish_point_ra_arclength](
                       const pb::OverlapSlice& slice) {
                     return (GetPaddedOverlapEnd(slice) +
                             world_context.ra_to_trailing_bumper_shift()) >=
                            target_gap_align_finish_point_ra_arclength;
                   });
  // When there is no overlap slice behind the target finish point to
  // pass/yield, using the default pass/yield max active time. If the
  // pass/yield slice relative time is greater than zero (indicating the agent
  // has not pass the finish point yet), using the pass/yield slice's relative
  // time, otherwise using a zeros unless ego is almost gap aligned (i.e,
  // ignoring the gap align if it's far away from being achieved or maintaining
  // the gap aligned position, but we hold the max yield active time at 3.0sec
  // as long as ego is almost gap aligned to compensate for abrupt arc-length
  // changes due to turning).
  if (yield_slice_iter == overlap_region.overlap_slices().end()) {
    mutator.set_gap_align_yield_max_active_time(
        config.turning_large_vehicle_gap_align()
            .default_yield_max_active_time());
  } else if (yield_slice_iter->relative_time_in_sec() > 0.0) {
    mutator.set_gap_align_yield_max_active_time(
        yield_slice_iter->relative_time_in_sec());
  } else {
    const double overlap_start_ra_arclength =
        speed::GetPaddedOverlapStart(*overlap_region.overlap_slices().begin()) +
        world_context.ra_to_leading_bumper();
    const bool is_almost_gap_aligned =
        (overlap_start_ra_arclength +
         kAlmostGapAlignedLongitudinalDistThresholdInMeter) >=
        (world_context.ra_to_leading_bumper() +
         config.turning_large_vehicle_gap_align().yield_extra_distance());
    mutator.set_gap_align_yield_max_active_time(
        is_almost_gap_aligned ? kAlmostGapAlignedMaxYieldActiveTimeInSec : 0.0);
  }

  if (pass_slice_iter == overlap_region.overlap_slices().end()) {
    mutator.set_gap_align_pass_max_active_time(
        config.turning_large_vehicle_gap_align()
            .default_pass_max_active_time());
  } else if (pass_slice_iter->relative_time_in_sec() > 0.0) {
    mutator.set_gap_align_pass_max_active_time(
        pass_slice_iter->relative_time_in_sec());
  } else {
    mutator.set_gap_align_pass_max_active_time(0.0);
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double agent_longitudinal_speed =
      object_proximity_info.signed_longitudinal_speed();
  const double max_ego_speed = trajectory_info.GetEgoMaxSpeedInArclengthRange(
      GetPaddedOverlap(overlap_region));
  mutator.set_pass_if_possible_with_max_speed_accel_jerk(
      max_ego_speed, config.turning_large_vehicle_gap_align().max_pass_jerk());
  const double yield_min_v = std::max(
      config.turning_large_vehicle_gap_align().yield_min_v_lower_bound_in_mps(),
      agent_longitudinal_speed - config.turning_large_vehicle_gap_align()
                                     .max_speed_slower_than_agent_in_mps());

  mutator.set_pass_extra_distance(
      DiscomfortVarying(
          config.turning_large_vehicle_gap_align().pass_extra_distance()),
      /*allow_negative_value=*/true);
  mutator.set_yield_extra_distance(
      DiscomfortVarying(
          config.turning_large_vehicle_gap_align().yield_extra_distance()),
      /*allow_negative_value=*/true);

  // Do not yield to agent behind to avoid over-yielding.
  if (reasoning_object.IsBehindEgoFrontAxle()) {
    mutator.set_no_yield();
  } else {
    mutator.set_yield_if_possible_with_min_speed_accel(
        yield_min_v, config.turning_large_vehicle_gap_align().yield_min_a());
  }
  // When there are multiple BPs belonging to one agent (most are long BPs but
  // with one short BP), it is likely to happen that the whole gap align search
  // problem fails just because of no pass solution being found for the one
  // short BP (which cannot yield as it is too slow and leading vehicles block
  // pass solution space). To avoid such gap align search failure, we need set
  // allow_pass_ignore_starting_at_discomfort to a valid value.
  // TODO(minhanli): To increase the chance of passing at higher discomfort, we
  // generally would like to set the |allow_pass_ignore_starting_at_discomfort|
  // at the specified discomfort, e.g., 0.25.
  mutator.set_allow_pass_ignore(true);
  mutator.set_if_possible_below_discomfort(
      config.turning_large_vehicle_gap_align().gap_align_max_discomfort() +
      math::constants::kEpsilon);
  return true;
}

math::Range1d CalculateReferenceLimitForNonreactivePedStrictOverlap(
    const TrajectoryInfo& trajectory_info,
    const speed::pb::OverlapRegion& overlap_region,
    const pb::CautiousDrivingReasonerConfig& config) {
  const speed::pb::OverlapSlice& nearest_strict_overlap_slice =
      overlap_region.overlap_slices(
          reasoning_util::GetNearestStrictOverlapSliceIdx(overlap_region));

  // TODO(reasoning): clean up the usage of the strict overlap slice.
  const double road_speed_limit =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(GetStrictOverlap(
          nearest_strict_overlap_slice, /*use_original=*/true));

  const double ego_strict_speed_limit_lb = std::min(
      config.low_discomfort_collision_speed_for_nonreactive_ped_in_mps(),
      road_speed_limit);

  const double ego_strict_speed_limit_ub = std::min(
      config.high_discomfort_collision_speed_for_nonreactive_ped_in_mps(),
      road_speed_limit);

  DCHECK_LE(ego_strict_speed_limit_lb, ego_strict_speed_limit_ub);

  return {ego_strict_speed_limit_lb, ego_strict_speed_limit_ub};
}

math::Range1d CalculateReferenceLimitForNonreactiveVehicleStrictOverlap(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const speed::pb::OverlapSlice& nearest_strict_overlap_slice,
    const pb::CautiousDrivingReasonerConfig& config) {
  const double road_speed_limit =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(
          GetStrictOverlap(nearest_strict_overlap_slice));

  const double abs_lateral_gap =
      reasoning_object.object_proximity_info().abs_lateral_gap();

  double query_lateral_gap = abs_lateral_gap;
  double query_speed = nearest_strict_overlap_slice.signed_lateral_speed();

  // This is a short term solution for cn9259924.
  // TODO(mengze): revise the logic in the future.
  if (reasoning_object.IsObviouslyAccelerating()) {
    // Here we add cautious driving strategy based estimated future pose in 1.5s
    // assuming const accel model. This time is selected to have enough slowing
    // down for the motivated case given the speed limited is discounted heavily
    // based on lateral gaps.
    const double t_delay = 1.5;  // s.
    const double lateral_speed = std::abs(
        reasoning_object.object_proximity_info().signed_lateral_speed());
    const double lateral_accel = std::abs(
        reasoning_object.object_proximity_info().signed_lateral_acceleration());
    query_lateral_gap = abs_lateral_gap - lateral_speed * t_delay -
                        0.5 * lateral_accel * t_delay * t_delay;
    query_speed = lateral_speed + lateral_accel * t_delay;
  }

  if (query_lateral_gap >=
      config.lateral_gap_no_slowdown_limit_nonreactive_vehicle_in_meter()) {
    return {road_speed_limit, road_speed_limit};
  }
  // For T-bone like collision, we consider agent lateral speed.
  const double lateral_speed_sqr = query_speed * query_speed;
  const double comfort_collision_speed_sqr =
      config.comfort_nonreactive_collision_speed_sqr();
  const double critical_collision_speed_sqr =
      config.critical_nonreactive_collision_speed_sqr();
  DCHECK_LE(comfort_collision_speed_sqr, critical_collision_speed_sqr);
  double comfort_ego_speed_at_collision = 0.0;
  double critical_ego_speed_at_collision = 0.0;
  // If the agent lateral speed is larger than the allowed collision speed
  // bound, it indicates that the agent is laterally fast, and ego is expected
  // to significantly slow down to avoid high risk collision.
  if (lateral_speed_sqr < comfort_collision_speed_sqr) {
    comfort_ego_speed_at_collision =
        std::sqrt(comfort_collision_speed_sqr - lateral_speed_sqr);
  }
  if (lateral_speed_sqr < critical_collision_speed_sqr) {
    critical_ego_speed_at_collision =
        std::sqrt(critical_collision_speed_sqr - lateral_speed_sqr);
  }

  DCHECK_LE(comfort_ego_speed_at_collision, critical_ego_speed_at_collision);

  const double critical_required_lateral_gap =
      reasoning_object.CriticalRequiredLateralGap();

  const double comfort_slow_down_speed = math::GetLinearInterpolatedY(
      /*x1=*/critical_required_lateral_gap,
      /*x2=*/
      config.lateral_gap_no_slowdown_limit_nonreactive_vehicle_in_meter(),
      /*y1=*/comfort_ego_speed_at_collision,
      /*y2=*/road_speed_limit,
      /*x_eval=*/query_lateral_gap);

  const double critical_slow_down_speed = math::GetLinearInterpolatedY(
      /*x1=*/critical_required_lateral_gap,
      /*x2=*/
      config.lateral_gap_no_slowdown_limit_nonreactive_vehicle_in_meter(),
      /*y1=*/critical_ego_speed_at_collision,
      /*y2=*/road_speed_limit,
      /*x_eval=*/query_lateral_gap);

  DCHECK_LE(comfort_slow_down_speed, critical_slow_down_speed);

  return {comfort_slow_down_speed, critical_slow_down_speed};
}

struct RangeWithId {
  math::Range1d range;
  ObjectId id;
};

// Merge overlapping RangeWithId and outputs a vector of disjoint RangeWithId in
// ascending order of their starting positions. Each output RangeWithId will
// have the first of its merged ranges' id.
// Note that this function may modify the input ranges.
std::vector<RangeWithId> MergeRangeWithID(std::vector<RangeWithId>& ranges) {
  DCHECK_GT(ranges.size(), 0);
  std::sort(ranges.begin(), ranges.end(),
            [](const RangeWithId& range1, const RangeWithId& range2) {
              return range1.range < range2.range;
            });

  std::vector<RangeWithId> result_ranges;

  result_ranges.push_back(ranges.front());
  for (unsigned i = 1; i < ranges.size(); ++i) {
    if (math::AreRangesOverlapping(result_ranges.back().range,
                                   ranges[i].range)) {
      result_ranges.back().range.end_pos =
          std::max(result_ranges.back().range.end_pos, ranges[i].range.end_pos);
    } else {
      // No merge, append the range.
      result_ranges.push_back(ranges[i]);
    }
  }
  return result_ranges;
}

// This is a helper function to find the responding road speed limit at
// ra_arc_length_on_path, we use this method rather than
// TrajectoryInfo::GetEgoMaxSpeedAtArclength due to the runtime concern, as we
// need to find the road limit for every path point.
std::pair<size_t, double> FindRoadSpeedLimit(
    const std::vector<traffic_rules::LaneSpeedLimit>& road_speed_limits,
    size_t curr_road_limit_idx, double ra_arc_length_on_path) {
  DCHECK_LT(curr_road_limit_idx, road_speed_limits.size());
  // The speed limit is effective on the range
  // [road_speed_limits.at(curr_road_limit_idx).ra_arclength,
  // road_speed_limits.at(curr_road_limit_idx + 1).ra_arclength], we want to use
  // this code block to find the curr_road_limit_idx so that the arc_length
  // range covers the query ra_arc_length_on_path.
  while (curr_road_limit_idx + 1 < road_speed_limits.size() &&
         road_speed_limits.at(curr_road_limit_idx + 1).ra_arclength <=
             ra_arc_length_on_path) {
    curr_road_limit_idx++;
  }
  const double curr_road_limit =
      std::min(road_speed_limits.at(curr_road_limit_idx).max_speed,
               traffic_rules::GetEgoMaxSpeedDependOnCar());
  return std::make_pair(curr_road_limit_idx, curr_road_limit);
}

// Computes speed limit of cautious driving limiter based on the type of
// crosswalk proximity VRU cautious.
double ComputeCrosswalkProximityVRUCautiousSpeedLimit(
    const CrosswalkProximityVRUCautiousType crosswalk_cautious_type) {
  // According to the product doc,
  // https://cooper.didichuxing.com/docs2/document/2201531640255, Ego should
  // slow down as passing through crosswalks.
  constexpr double kCrosswalkSpeedLimitInMps = math::KmphToMps(35.0);
  constexpr double kCrosswalkSpeedLimitForVRUMovingOnCrosswalkInMps =
      math::KmphToMps(25.0);
  DCHECK(crosswalk_cautious_type != CrosswalkProximityVRUCautiousType::kNA);

  return crosswalk_cautious_type == CrosswalkProximityVRUCautiousType::kHigh
             ? kCrosswalkSpeedLimitForVRUMovingOnCrosswalkInMps
             : kCrosswalkSpeedLimitInMps;
}

// Computes acceleration of cautious driving limiter based on the type of
// crosswalk proximity VRU cautious.
double ComputeCrosswalkProximityVRUCautiousAcceleration(
    const CrosswalkProximityVRUCautiousType crosswalk_cautious_type) {
  constexpr double kAccelerationForVRUOutCrosswalkInMpss = -1.0;
  constexpr double kAccelerationForVRUMovingOnCrosswalkInMpss = -2.0;

  DCHECK(crosswalk_cautious_type != CrosswalkProximityVRUCautiousType::kNA);

  return crosswalk_cautious_type == CrosswalkProximityVRUCautiousType::kHigh
             ? kAccelerationForVRUMovingOnCrosswalkInMpss
             : kAccelerationForVRUOutCrosswalkInMpss;
}

// Compute arc-length range of cautious driving limiter, which sets the
// arc-length range before crosswalk while not on the crosswalk.
math::Range1d ComputeCrosswalkProximityVRUCautiousRange(
    const traffic_rules::CrosswalkInLaneSequence& crosswalk,
    const double ego_front_bumper_ra_arclength) {
  constexpr double kCautiousLongitudinalRangBeforeCrosswalkInMeter = 5.0;
  const double offset_crosswalk_start_ra_arclength = std::max(
      0.0, crosswalk.yield_zone_infos.front().ra_arclength_range.start_pos -
               ego_front_bumper_ra_arclength);

  return {std::max(0.0, offset_crosswalk_start_ra_arclength -
                            kCautiousLongitudinalRangBeforeCrosswalkInMeter),
          offset_crosswalk_start_ra_arclength};
}

// Adds reference limits based on crosswalks when ther are VRUs around
// crosswalks. Returns true if the reference is updated.
bool AddReferenceLimitForProximityVRUAtCrosswalk(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const TrajectoryInfo& trajectory_info,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* debug_ptr) {
  const double ego_front_bumper_ra_arclength =
      world_context.ra_to_leading_bumper();
  const double ego_half_width = world_context.robot_state().GetWidth() * 0.5;
  bool reference_limit_added = false;
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk :
       trajectory_info.traffic_rules().crosswalks) {
    pb::CrosswalkCautiousDrivingDebug* crosswalk_debug =
        debug_ptr != nullptr ? debug_ptr->add_crosswalks() : nullptr;
    if (crosswalk_debug != nullptr) {
      crosswalk_debug->set_crosswalk_id(crosswalk.crosswalk_ptr->id());
      crosswalk_debug->set_scene(
          pb::CrosswalkCautiousDrivingDebug::PROXIMITY_VRU);
    }

    std::string* debug_str_ptr = crosswalk_debug != nullptr
                                     ? crosswalk_debug->mutable_debug_str()
                                     : nullptr;
    const CrosswalkProximityVRUCautiousType crosswalk_cautious_type =
        ComputeCrosswalkProximityVRUCautiousType(wrapped_prediction_decisions,
                                                 crosswalk, ego_half_width,
                                                 debug_str_ptr);
    if (crosswalk_cautious_type == CrosswalkProximityVRUCautiousType::kNA) {
      continue;
    }

    const double crosswalk_cautious_speed_limit =
        ComputeCrosswalkProximityVRUCautiousSpeedLimit(crosswalk_cautious_type);
    const double crosswalk_cautious_acceleration =
        ComputeCrosswalkProximityVRUCautiousAcceleration(
            crosswalk_cautious_type);
    const math::Range1d crosswalk_cautious_range =
        ComputeCrosswalkProximityVRUCautiousRange(
            crosswalk, ego_front_bumper_ra_arclength);
    const bool reference_limit_added_for_this_crosswalk =
        reference_limiter_ptr->AddReferenceLimit(
            crosswalk_cautious_speed_limit, crosswalk_cautious_speed_limit,
            crosswalk_cautious_range.start_pos,
            crosswalk_cautious_range.end_pos, crosswalk_cautious_acceleration,
            /*object_id=*/-1);
    reference_limit_added |= reference_limit_added_for_this_crosswalk;

    if (crosswalk_debug != nullptr) {
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          crosswalk_cautious_speed_limit, crosswalk_cautious_speed_limit,
          crosswalk_cautious_range.start_pos, crosswalk_cautious_range.end_pos,
          crosswalk_cautious_acceleration,
          reference_limit_added_for_this_crosswalk,
          *(crosswalk_debug->mutable_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

// Adds reference limit in order to prevent cyclist at crosswalk from starting
// to move. True is returned if reference limit is added.
bool AddReferenceLimitForStartingToMoveCyclistAtCrosswalk(
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    const double ego_front_bumper_ra_arclength,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* debug_ptr) {
  constexpr double kCrosswalkSpeedLimitForStartingToMoveAgentInMps =
      2.78;  // 10 km/h

  bool reference_limit_added = false;
  for (const traffic_rules::CrosswalkInLaneSequence& crosswalk :
       trajectory_info.traffic_rules().crosswalks) {
    pb::CrosswalkCautiousDrivingDebug* crosswalk_debug_ptr = nullptr;
    if (debug_ptr != nullptr) {
      crosswalk_debug_ptr = debug_ptr->add_crosswalks();
      crosswalk_debug_ptr->set_crosswalk_id(crosswalk.crosswalk_ptr->id());
      crosswalk_debug_ptr->set_scene(
          pb::CrosswalkCautiousDrivingDebug::STARTING_TO_MOVE_CYCLIST);
    }

    std::string debug_str;
    const traffic_rules::LaneInfoInLaneSequence* right_turn_lane_info_ptr =
        GetEgoRightTurnLaneInfoIntersectingWithCrosswalk(
            crosswalk, trajectory_info.traffic_rules().lane_sequence_lanes);
    if (!ShouldAddReferenceLimitForStartingToMoveCyclistAroundCrosswalk(
            crosswalk, right_turn_lane_info_ptr, wrapped_prediction_decisions,
            &debug_str)) {
      if (crosswalk_debug_ptr != nullptr) {
        crosswalk_debug_ptr->set_debug_str(std::move(debug_str));
      }
      continue;
    }

    DCHECK(right_turn_lane_info_ptr != nullptr);
    const double cautious_start_ra_arclength =
        right_turn_lane_info_ptr->start_ra_arclength -
        ego_front_bumper_ra_arclength;
    const double cautious_end_ra_arclength =
        right_turn_lane_info_ptr->end_ra_arclength -
        ego_front_bumper_ra_arclength;
    const bool reference_limit_added_for_this_crosswalk =
        reference_limiter_ptr->AddReferenceLimit(
            kCrosswalkSpeedLimitForStartingToMoveAgentInMps,
            kCrosswalkSpeedLimitForStartingToMoveAgentInMps,
            cautious_start_ra_arclength, cautious_end_ra_arclength,
            config.min_acceleration_for_risk(), /*object_id=*/-1);
    reference_limit_added |= reference_limit_added_for_this_crosswalk;

    if (crosswalk_debug_ptr != nullptr) {
      crosswalk_debug_ptr->set_debug_str(std::move(debug_str));
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          kCrosswalkSpeedLimitForStartingToMoveAgentInMps,
          kCrosswalkSpeedLimitForStartingToMoveAgentInMps,
          cautious_start_ra_arclength, cautious_end_ra_arclength,
          config.min_acceleration_for_risk(),
          reference_limit_added_for_this_crosswalk,
          *(crosswalk_debug_ptr->mutable_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

prediction::pb::PredictedTrajectory
FakeCutInPredictedTrajectoryForProximityAgent(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object, std::string* /* debug_string */) {
  const math::geometry::PolylineCurve2d& ego_lane_reference_line =
      trajectory_info.path().polyline_curve();
  const math::geometry::Point2d& agent_center_pose =
      reasoning_object.planner_object().pose_at_plan_init_ts().center_2d();
  // We will first use the current state of the agent to deduce the cut-in
  // entry position. The entry position would be invalid if the agent's heading
  // closely aligns with ego lane sequence.
  constexpr double kRaySegmentDistInMeters = 30;
  const math::geometry::Point2d shift_pose(
      agent_center_pose.x() +
          math::FastCos(reasoning_object.planner_object().heading()) *
              kRaySegmentDistInMeters,
      agent_center_pose.y() +
          math::FastSin(reasoning_object.planner_object().heading()) *
              kRaySegmentDistInMeters);
  const math::geometry::Polyline2d ray_segment = {agent_center_pose,
                                                  shift_pose};
  std::vector<math::geometry::Point2d> result;
  math::geometry::Intersection(ego_lane_reference_line.polyline(), ray_segment,
                               result);
  // We need to specify the entry position to control the generation of the
  // cut-in trajectory.
  glm::dvec2 agent_entry_position;
  if (result.empty()) {
    // If there is no intersection point, we would assume that the agent will
    // cut into ego path with a fixed time threshold.
    const math::geometry::Point2d point = ego_lane_reference_line.GetInterp(
        std::min(trajectory_info.plan_init_ra_arc_length_on_extended_path() +
                     reasoning_object.object_proximity_info()
                         .projected_ra_arc_length()
                         .start() +
                     reasoning_object.speed() *
                         kEntryEgoPathTimeForFakeCutInPredictedTrajectoryInSec,
                 ego_lane_reference_line.GetTotalArcLength()));
    agent_entry_position = glm::dvec2(point.x(), point.y());
  } else {
    agent_entry_position = glm::dvec2(result.front().x(), result.front().y());
  }
  // Fake one cut-in trajectory with prediction utility function.
  const math::geometry::PolylineCurve2d fake_cut_in_trajectory =
      prediction::utility::RefineReferenceLineForLaneChange(
          ego_lane_reference_line,
          glm::dvec2(agent_center_pose.x(), agent_center_pose.y()),
          agent_entry_position)
          .curve;
  // Convert |PolylineCurve2d| to |PredictedTrajectory| with const speed.
  prediction::pb::PredictedTrajectory predicted_trajectory_proto;
  predicted_trajectory_proto.set_id(kFakeCutInPredictedTrajectoryID);
  predicted_trajectory_proto.set_is_multi_output_trajectory(true);
  predicted_trajectory_proto.mutable_traj_poses()->Reserve(
      prediction::constants::kPredictionHorizonInPoses);
  const double fake_cut_in_trajectory_length =
      fake_cut_in_trajectory.GetTotalArcLength();
  for (int pose_index = 0;
       pose_index < prediction::constants::kPredictionHorizonInPoses;
       ++pose_index) {
    const double odom = reasoning_object.speed() * pose_index *
                        prediction::constants::kPoseIntervalInSec;
    if (odom > fake_cut_in_trajectory_length) {
      break;
    }

    const double heading = fake_cut_in_trajectory.GetInterpTheta(odom);
    const math::geometry::Point2d point =
        fake_cut_in_trajectory.GetInterp(odom);
    planner::pb::TrajectoryPose* trajectory_pose =
        predicted_trajectory_proto.add_traj_poses();
    trajectory_pose->set_x_pos(point.x());
    trajectory_pose->set_y_pos(point.y());
    trajectory_pose->set_speed(reasoning_object.speed());
    trajectory_pose->set_accel(0);
    trajectory_pose->set_heading(heading);
    trajectory_pose->set_odom(odom);
    trajectory_pose->set_timestamp(
        reasoning_object.planner_object().timestamp() +
        pose_index * prediction::constants::kPoseIntervalInMs);
  }

  return predicted_trajectory_proto;
}

void AddProximitySpeedConstraintForOverlapRegion(
    const ReasoningObject& reasoning_object,
    const pb::OverlapRegion& overlap_region, pb::ReasonerId reasoner_id,
    double required_lateral_gap, double max_speed, double min_req_discomfort,
    ConstraintCreator* constraint_creator_ptr) {
  const std::string constraint_id = absl::StrCat(
      GetOverlapRegionUniqueId(overlap_region), "-proximity-speed");
  const std::string pred_traj_id =
      GetBpUniqueId(overlap_region.object_id(), overlap_region.trajectory_id());

  ConstraintMutator mutator =
      constraint_creator_ptr->AddProximitySpeedConstraintFromOverlapRegion(
          pb::FenceType::kProximity, reasoner_id, constraint_id, overlap_region,
          reasoning_object.id(), pred_traj_id);
  mutator.set_proximity_speed_params(required_lateral_gap, max_speed,
                                     min_req_discomfort);
}

double ComputeMinRequiredDiscomfortToApplyNonreactiveCautious(
    const ReasoningObject& reasoning_object, const int64_t curr_timestamp) {
  constexpr int kIgnoreStaleRecordTimeThresholdInMs = 300;
  constexpr double kMinTGapToApplyNonreactiveCautiousAtHighDiscomfort = 1.2;
  constexpr double kMinRequiredDiscomfortToApplyNonreactiveCautiousVehicle =
      0.5;
  const std::optional<ARTObjectInfo>& art_object_info =
      reasoning_object.art_object_info();
  if (art_object_info.has_value() &&
      art_object_info.value().latest_t_gap.has_value() &&
      (art_object_info.value().latest_t_gap.value() >
           kMinTGapToApplyNonreactiveCautiousAtHighDiscomfort &&
       curr_timestamp - art_object_info.value().latest_constraint_timestamp <
           kIgnoreStaleRecordTimeThresholdInMs)) {
    return kMinRequiredDiscomfortToApplyNonreactiveCautiousVehicle;
  }
  return Discomforts::kMid;
}

// Returns true if the agent has any overlap lies in the range of traffic light
// controlled lane and we should be cautious and slow down. More specifically,
// we consider the overlaps assigned to certain reasoners like cross agent
// reasoner.
bool HasAgentPolicyWithinTLControlRange(
    const PredictionDecisionMakerOutput& output,
    const math::Range1d& tl_control_range) {
  // The reasoners we are interested for adding cautious for traffic light.
  const std::unordered_set<pb::ReasonerId> interested_reasoner = {
      pb::ReasonerId::CROSS_AGENT, pb::ReasonerId::VRU,
      pb::ReasonerId::JAYWALKER, pb::ReasonerId::ONCOMING_AGENT};

  // Find the interested agent at overlap level.
  const auto has_any_interested_overlap =
      [&tl_control_range, &interested_reasoner](
          const PredictionDecision& prediction_decision) -> bool {
    return std::any_of(
        prediction_decision.agent_policies().begin(),
        prediction_decision.agent_policies().end(),
        [&tl_control_range,
         &interested_reasoner](const AgentPolicy& agent_policy) {
          const pb::OverlapRegion& overlap_region =
              agent_policy.OverlapRegion();
          const bool overlap_with_given_range =
              std::min(tl_control_range.end_pos,
                       GetPaddedOverlapEnd(overlap_region)) >
              std::max(tl_control_range.start_pos,
                       GetPaddedOverlapStart(overlap_region));
          const bool is_interested_overlap =
              interested_reasoner.find(agent_policy.reasoner_in_charge()) !=
              interested_reasoner.end();
          return overlap_with_given_range && is_interested_overlap;
        });
  };

  // Find the interested agent.
  return std::any_of(output.prediction_decisions().begin(),
                     output.prediction_decisions().end(),
                     [has_any_interested_overlap](
                         const PredictionDecision& prediction_decision) {
                       return !prediction_decision.Ignore() &&
                              has_any_interested_overlap(prediction_decision);
                     });
}

double ComputeComfortSpeedLimitForNewDetectAgent(
    const ReasoningObject& reasoning_object) {
  const double critical_required_lateral_gap =
      reasoning_object.CriticalRequiredLateralGap();
  const double abs_lateral_gap =
      reasoning_object.object_proximity_info().abs_lateral_gap();

  // From the maximum lateral gap to the critical lateral gap, the speed limit
  // will linearly interpolate from 20 km/h to 10 km/h.
  constexpr double kMaxSpeedLimitForNewDetectAgentInMps = math::KmphToMps(20.0);
  constexpr double kMinSpeedLimitForNewDetectAgentInMps = math::KmphToMps(10.0);

  return math::GetLinearInterpolatedY(
      /*x1=*/critical_required_lateral_gap,
      /*x2=*/kMaxDistanceToAddCautiousForNewDetectAgentInMeter,
      /*y1=*/kMinSpeedLimitForNewDetectAgentInMps,
      /*y2=*/kMaxSpeedLimitForNewDetectAgentInMps,
      /*x_eval=*/abs_lateral_gap);
}

// Returns true if the agent goes through any lane sections before
// entering the target ego lane as ego does.
bool IsAgentGoingThroughSameSectionBeforeTargetLane(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pnc_map::Lane* target_ego_lane,
    const LaneSequenceIterator& ego_lane_sequence_iterator) {
  if (target_ego_lane == nullptr) {
    return false;
  }
  if (reasoning_object.agent_occupied_map_element_infos_in_seed_ptr() ==
          nullptr ||
      !reasoning_object.agent_occupied_map_element_infos_in_seed_ptr()
           ->has_latest_occupied_isolated_lane()) {
    return false;
  }
  std::vector<const pnc_map::Lane*> agent_latest_regular_lane_vec =
      world_context.joint_pnc_map_service()->GetLaneSequence(
          {reasoning_object.agent_occupied_map_element_infos_in_seed_ptr()
               ->latest_occupied_isolated_lane()
               .element_id()});
  if (agent_latest_regular_lane_vec.empty()) {
    DLOG(WARNING) << " Lane ID not exist:"
                  << reasoning_object
                         .agent_occupied_map_element_infos_in_seed_ptr()
                         ->latest_occupied_isolated_lane()
                         .element_id();
    return false;
  }
  const pnc_map::Lane* agent_latest_regular_lane =
      agent_latest_regular_lane_vec.front();
  bool is_from_the_same_preceding_section = false;
  bool is_or_after_current_lane = false;
  // Iterate through ego's lane sequence until the target lane to check if
  // the agent is coming from any same lane sections as that of ego.
  for (const pnc_map::Lane* lane_in_ego_lane_sequence :
       ego_lane_sequence_iterator.lane_sequence()) {
    if (!is_or_after_current_lane &&
        !ego_lane_sequence_iterator.IsOrAfterCurrentLane(
            *lane_in_ego_lane_sequence)) {
      is_or_after_current_lane = true;
      continue;
    }
    // Do not search lanes after the target ego lane, including the target lane.
    if (lane_in_ego_lane_sequence->id() == target_ego_lane->id()) {
      break;
    }
    if (lane_in_ego_lane_sequence->section()->id() ==
        agent_latest_regular_lane->section()->id()) {
      is_from_the_same_preceding_section = true;
      break;
    }
  }
  return is_from_the_same_preceding_section;
}

// Adds proximity speed constraint for low likelihood predicted trajectories.
// True is returned if proximity speed constraint is added.
bool AddProximitySpeedConstraintForLowLikelihoodPredictedTrajectory(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  DCHECK(agent_policy.cautious_driving_reason() ==
         pb::CautiousDrivingReason::LOW_LIKELIHOOD_TRAJECTORY);

  std::string debug_string;
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  // TODO(hezhihang): Polish the following logic based on object type, context
  // and potential collision risk and strength.
  // The cautious speed is heavily dependent on |strength|. If |strength| is 0,
  // we ignore the cautious speed.
  const double strength =
      ComputeCautiousStrength(agent_policy, config, debug_string);
  if (strength < config.min_strength_for_cautious()) {
    if (agent_debug_ptr != nullptr) {
      absl::StrAppendFormat(&debug_string, "ignore for low strength:%.2f",
                            strength);
      PopulatePredictedTrajectoryDebug(
          agent_policy.agent_trajectory_info().id(), {0.0, 0.0}, {0.0, 0.0},
          strength, /*min_accel=*/0.0, /*is_reference_profile_updated=*/false,
          overlap_region.region_id(), agent_policy.cautious_driving_reason(),
          std::move(debug_string), *agent_debug_ptr);
    }

    return false;
  }

  math::Range1d speed_zone_ra_arclength_range =
      ComputeSpeedZoneArclengthRange(overlap_region);
  math::Range1d speed_limit_range = ComputeSpeedLimitRange(
      trajectory_info, agent_policy, speed_zone_ra_arclength_range,
      overlap_region, strength, config);

  // TODO(waylon): To reduce unnecessary speed limits for low-likelihood
  // cut-in trajectories that are similar to the primary trajectory, the
  // minimum speed limit should be kept consistent with the
  // LeadAndMergeAgentReasoner reasoner. Adjust the speed limit when overlap
  // aggregation is deployed later.
  const double agent_speed = reasoning_object.speed();
  const double min_comfort_speed = std::max(
      {config.cut_in_agent().min_speed_limit_bound_in_mps(),
       agent_speed * config.cut_in_agent().min_cautious_ratio_per_agent(),
       speed_limit_range.start_pos});

  // Add proximity speed constraint for the low-likelihood predicted
  // trajectory.
  const DiscomfortVarying& agent_required_lateral_gap =
      reasoning_object.required_lateral_gap();
  AddProximitySpeedConstraintForOverlapRegion(
      reasoning_object, overlap_region, agent_policy.reasoner_in_charge(),
      agent_required_lateral_gap(Discomforts::kMax), min_comfort_speed,
      /*min_req_discomfort=*/0.0, constraint_creator_ptr);

  if (agent_debug_ptr != nullptr) {
    debug_string =
        "proximity constraint added, see constraint setting for actual debug";
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(), speed_limit_range,
        speed_zone_ra_arclength_range, strength,
        /*min_accel=*/0.0,
        /*is_reference_profile_updated=*/false, overlap_region.region_id(),
        agent_policy.cautious_driving_reason(), std::move(debug_string),
        *agent_debug_ptr);
  }
  rt_event::PostRtEvent<rt_event::planner::LowLikelihoodCautious>(
      std::to_string(reasoning_object.id()));

  return true;
}

// Returns true if reference profile is updated by the speed limit added for the
// oncoming cyclicst.
bool AddReferenceLimitForOncomingCyclist(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  DCHECK(agent_policy.cautious_driving_reason() ==
         pb::CautiousDrivingReason::ONCOMING_CYCLIST);
  constexpr double kSpeedLimitForOncomingCyclistsAtLowestDiscomfortInMps =
      math::KmphToMps(30.0);

  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();

  // Compute the arc-length range on where the speed limit should be added. For
  // the OverlapRegion whose motion type is ONCOMING, it is generally very long
  // and Ego cannot collide with the agent on most part of OverlapRegion. Thus,
  // we should shrink the length of speed zone.
  // The following function estimates a position where Ego starts to meet with
  // the oncoming cyclist.
  const double cautious_start_arclength =
      std::max(reasoning_util::ComputeYieldPositionForOncomingAgent(
                   reasoning_object, world_context.ra_to_leading_bumper(),
                   world_context.ego_speed()),
               0.0);
  // We use the sum of Ego length and the oncoming cyclist's length to estimate
  // the range where Ego and the cyclist pass side by side.
  math::Range1d speed_zone_ra_arclength_range(
      cautious_start_arclength, cautious_start_arclength +
                                    world_context.robot_state().GetLength() +
                                    reasoning_object.length());

  // Compute the speed limit value range from the lowest discomfort level to the
  // highest discomfort level.
  const double ego_max_speed =
      std::max(trajectory_info.GetEgoMaxSpeedInArclengthRange(
                   speed_zone_ra_arclength_range),
               kSpeedLimitForOncomingCyclistsAtLowestDiscomfortInMps);
  const double speed_limit = math::GetLinearInterpolatedY(
      /*x1=*/reasoning_object.ComfortRequiredLateralGap(),
      /*x2=*/config.max_lateral_gap_for_oncoming_cyclist_in_meter(),
      /*y1=*/kSpeedLimitForOncomingCyclistsAtLowestDiscomfortInMps,
      /*y2=*/ego_max_speed,
      /*x_eval=*/reasoning_object.object_proximity_info().abs_lateral_gap());
  const math::Range1d speed_limit_range(speed_limit, speed_limit);

  // Compute min acceleration of cautious.
  const double min_acceleration_for_cautious =
      config.min_acceleration_for_comfort();

  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          speed_limit_range, speed_zone_ra_arclength_range,
          min_acceleration_for_cautious, reasoning_object.id());

  if (agent_debug_ptr != nullptr) {
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(), speed_limit_range,
        speed_zone_ra_arclength_range, /*strength*/ 1.0,
        min_acceleration_for_cautious, is_reference_profile_updated,
        overlap_region.region_id(), agent_policy.cautious_driving_reason(),
        /*debug_string=*/"", *agent_debug_ptr);
  }

  return is_reference_profile_updated;
}

// Returns true if the agent could cut-in Ego. Currently, there are two
// scenarios:
// 1) The agent is approaching Ego path laterally and it will encroach Ego path
//    in a short time.
// 2) The cyclist has large angular velocity but its velocity is not very large.
bool IsRiskyCutInAgent(
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    std::string* debug_str) {
  // Only consider vehicle or cyclists that are ahead Ego and laterally
  // approaching Ego within certain heading and speed.
  constexpr double kMinRelativeHeadingRightInRad = 0.15;  // 8.6 degrees
  constexpr double kMaxRelativeHeadingRightInRad = 1.0;   // 57.3 degrees
  constexpr double kMinRelativeHeadingLeftInRad = 0.15;   // 8.6 degrees
  constexpr double kMaxRelativeHeadingLeftInRad = 1.2;    // 68.7 degrees
  constexpr double kMaxRelativeHeadingLeftForUTurnAgentInRad =
      2.4;  // 137.5 degrees
  constexpr double kMinLateralSpeedInMps = 0.5;
  constexpr double kMaxLateralTTCFromRight = 2.0;
  constexpr double kMaxLateralTTCShortFromLeft = 2.5;
  constexpr double kMaxLateralTTCShortFromLeftSquare = 6.25;
  constexpr double kMaxLateralTTCFromLeft = 4.0;
  constexpr double kMaxLateralTTCFromLeftSquare = 16.0;
  constexpr double kMaxRelativeHeadingForSuddenlyCutinInRad =
      math::Degree2Radian(75.0);
  constexpr double kMaxOmegaInRadianPerSecond = math::Degree2Radian(5.0);
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (!reasoning_object.is_vehicle_or_cyclist() ||
      reasoning_object.IsStationary() || !reasoning_object.IsFullyAhead() ||
      reasoning_object.IsOnEgoPath()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Not target agent; ");
    }
    return false;
  }

  if ((object_proximity_info.signed_lateral_speed() *
           object_proximity_info.signed_lateral_gap() <
       0.0) &&
      std::abs(object_proximity_info.signed_lateral_speed()) >
          kMinLateralSpeedInMps) {
    if (object_proximity_info.signed_lateral_gap() <
        0.0) {  // agent on right side
      if ((object_proximity_info.abs_lateral_gap() <
           std::abs(object_proximity_info.signed_lateral_speed()) *
               kMaxLateralTTCFromRight) &&
          math::IsInRange(
              std::abs(object_proximity_info.relative_motion_heading()),
              kMinRelativeHeadingRightInRad, kMaxRelativeHeadingRightInRad)) {
        if (debug_str != nullptr) {
          absl::StrAppend(debug_str,
                          "Laterally approaching quickly from right; ");
        }
        return true;
      }
    } else {  // agent on left side, most likely an Uturn agent
      // calculate lateral displacement based on constant acceleration at TTC
      double lat_displacement_with_current_accel =
          std::abs(object_proximity_info.signed_lateral_speed()) *
              kMaxLateralTTCFromLeft -
          0.5 * object_proximity_info.signed_lateral_acceleration() *
              kMaxLateralTTCFromLeftSquare;
      double lat_displacement_with_current_accel_short =
          std::abs(object_proximity_info.signed_lateral_speed()) *
              kMaxLateralTTCShortFromLeft -
          0.5 * object_proximity_info.signed_lateral_acceleration() *
              kMaxLateralTTCShortFromLeftSquare;
      double agent_current_omega =
          reasoning_object.planner_object().tracked_object().omega();
      bool is_potentially_an_uturn_agent = std::any_of(
          prediction_decisions.begin(), prediction_decisions.end(),
          [](const PredictionDecision& prediction_decision) {
            const auto& agent_trajectory_info =
                prediction_decision.agent_trajectory_info();
            return reasoning_util::IsUturnTrajectory(agent_trajectory_info);
          });

      const double max_relative_heading_for_left_agent =
          is_potentially_an_uturn_agent
              ? kMaxRelativeHeadingLeftForUTurnAgentInRad
              : kMaxRelativeHeadingLeftInRad;
      if ((object_proximity_info.abs_lateral_gap() <
           lat_displacement_with_current_accel) &&
          (agent_current_omega > 0) &&
          math::IsInRange(
              std::abs(object_proximity_info.relative_motion_heading()),
              kMinRelativeHeadingLeftInRad,
              max_relative_heading_for_left_agent)) {
        rt_event::PostRtEvent<rt_event::planner::RiskyCutInAgentFromLeft>();
        if (object_proximity_info.abs_lateral_gap() >
            lat_displacement_with_current_accel_short) {
          rt_event::PostRtEvent<
              rt_event::planner::RiskyCutInAgentFromLeftWithTTCFrom2pt5sto4s>();
        }
        if (debug_str != nullptr) {
          absl::StrAppend(debug_str,
                          "Laterally approaching quickly from left; ");
        }
        return true;
      }
    }
  }

  const double cyclist_current_omega =
      reasoning_object.planner_object().tracked_object().omega();
  if (reasoning_object.is_cyclist() &&
      std::abs(object_proximity_info.relative_motion_heading()) <
          kMaxRelativeHeadingForSuddenlyCutinInRad &&
      ((object_proximity_info.signed_lateral_gap() > 0.0 &&
        cyclist_current_omega < -kMaxOmegaInRadianPerSecond) ||
       (object_proximity_info.signed_lateral_gap() < 0.0 &&
        cyclist_current_omega > kMaxOmegaInRadianPerSecond))) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Cyclist with large angular velocity; ");
    }
    return true;
  }

  if (debug_str != nullptr) {
    absl::StrAppend(debug_str, "Not target scene; ");
  }
  return false;
}

// Returns a pair of results of the risky squeezing agent identification
// based on its current tracking info. The first tuple represents if the agent
// is risky squeezing ego and the second tuple is its relative heading w.r.t.
// ego if it is risky.
// NOTE: This util is applicable to lane keep only.
std::pair<bool, double> ComputeRiskySqueezingAgentIdentificationResult(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info, std::string* debug_str) {
  if (!reasoning_object.planner_object().is_vehicle()) {
    return {false, 0.0};
  }
  // Only consider the case under lane keep maneuver.
  if (!trajectory_info.IsLaneKeep()) {
    absl::StrAppend(debug_str, "Not LK; ");
    return {false, 0.0};
  }
  const pb::ObjectProximityInfo& proximity =
      reasoning_object.object_proximity_info();
  if (reasoning_object.IsOnEgoPath() || proximity.out_of_path_range()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Out Path Scope; ");
    }
    return {false, 0.0};
  }
  if (proximity.projected_ra_arc_length().end() <
      world_context.ra_to_leading_bumper() +
          kAgentAheadOfFrontBumperThresholdForAvoidSqueezeCautiousInMeter) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Not ahead; ");
    }
    return {false, 0.0};
  }
  if (reasoning_object.IsStationary() ||
      reasoning_object.is_slow_moving_vehicle()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Slow moving; ");
    }
    return {false, 0.0};
  }
  if (reasoning_object.object_occupancy_param_ptr() == nullptr) {
    return {false, 0.0};
  }
  if (reasoning_object.object_occupancy_param_ptr()->max_lane_encroachment_m <
      -kAgentDistanceToLaneBoundaryThresholdForAvoidSqueezeCautiousInMeter) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Not close to lane; ");
    }
    return {false, 0.0};
  }
  // For the projected heading diff value, we take the mean of both projection
  // results if their absolute diff ratio is less than two to recall more
  // squeeze tendency, otherwise prefer using proximity info over the occupancy
  // info (the latter relies more on the lane labeling).
  const double projected_heading_diff =
      std::abs(
          reasoning_object.object_occupancy_param_ptr()->relative_heading_rad) >
              kProjectedHeadingDiffRatioThreshold *
                  std::abs(proximity.relative_motion_heading())
          ? proximity.relative_motion_heading()
          : 0.5 * (reasoning_object.object_occupancy_param_ptr()
                       ->relative_heading_rad +
                   proximity.relative_motion_heading());
  // When turning, the relative heading w.r.t. the turning virtual lane may not
  // be meaningful, neither is the proximity relative heading, e.g., cn17957459.
  // Thus, using the current pose relative heading instead.
  const std::optional<traffic_rules::LaneInfoInLaneSequence> lane_info_opt =
      speed::traffic_rules::GetLaneInfoInLaneSequenceAtRaArcLength(
          trajectory_info.traffic_rules().lane_sequence_lanes,
          0.5 * (proximity.projected_ra_arc_length().start() +
                 proximity.projected_ra_arc_length().end()));
  const bool is_agent_in_junction_while_ego_turning =
      lane_info_opt.has_value() && lane_info_opt->lane->IsInJunction() &&
      lane_info_opt->lane->turn() != hdmap::Lane::Turn::Lane_Turn_STRAIGHT;
  const double current_pose_heading_diff = math::AngleDiff(
      reasoning_object.planner_object().heading(), world_context.heading());

  // The direction of projected heading diff, regardless of with
  // respect to what it gets computed (e.g., object_occupancy or
  // object_proximity), is possible to be problemetic. The reason is either due
  // to the unhuman-like virtual lane or not fully analyzing the relationship
  // between the whole ego path and the estimated agent trajectory in the
  // considered time horizon (aka. proximity is a compressed information). To
  // mitigate the problem when two projection yields different directions, we
  // count on the current pose heading diff to get the heading direction and
  // using the absolute value of proxmity heading.

  // NOTE: In long term, we need to analyze the whole path and the
  // estimated agent trajectory (e.g., fake overlap regions) to determine if
  // there is a potential sequeeze taking place instead of the relative heading.
  const bool is_same_projected_heading_sign =
      math::SignNum(reasoning_object.object_occupancy_param_ptr()
                        ->relative_heading_rad) ==
      math::SignNum(proximity.relative_motion_heading());
  if (!is_same_projected_heading_sign && debug_str != nullptr) {
    absl::StrAppend(debug_str, "Undertermined heading sign; ");
  }
  const double rel_heading =
      is_agent_in_junction_while_ego_turning ? current_pose_heading_diff
      : is_same_projected_heading_sign
          ? projected_heading_diff
          : math::SignNum(current_pose_heading_diff) *
                std::abs(proximity.relative_motion_heading());
  if (std::abs(rel_heading) > M_PI_4) {
    if (debug_str != nullptr) {
      absl::StrAppendFormat(
          debug_str, "rel_heading:%.2f, Out of heading scope;", rel_heading);
    }
    return {false, 0.0};
  }

  const double rel_lat_speed =
      math::FastSin(rel_heading) * reasoning_object.speed();
  if (debug_str != nullptr) {
    absl::StrAppendFormat(debug_str, "rel_heading:%.2f, rel_speed:%.2f;",
                          rel_heading, rel_lat_speed);
  }
  if (rel_lat_speed *
          reasoning_object.object_proximity_info().signed_lateral_gap() >
      0.0) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "Currently moving away; ");
    }
    return {false, 0.0};
  }

  const double agent_lat_displacement =
      rel_lat_speed * kTimeHorizonForEstimatingAgentMotionInSec;
  if (debug_str != nullptr) {
    absl::StrAppendFormat(debug_str, "agent_lat_displacement:%.2f;",
                          agent_lat_displacement);
  }
  // Consider an agent risky squeeze if (1) already encroached ego lane over 0.7
  // m; or (2) approaching ego path with lat gap less than 0.6 m in the next 2
  // sec with lane encroachment.
  const bool is_risky_squeezing_agent =
      (reasoning_object.object_occupancy_param_ptr()->max_lane_encroachment_m >
       kMinLaneEncroachmentThresholdForAvoidSqueezeInMeter) ||
      (std::abs(agent_lat_displacement) +
               kRequiredLateralGapForMildAvoidSqueezeInMeter >
           proximity.abs_lateral_gap() &&
       (reasoning_object.object_occupancy_param_ptr()->max_lane_encroachment_m >
            0.0 ||
        agent_lat_displacement >
            std::abs(reasoning_object.object_occupancy_param_ptr()
                         ->max_lane_encroachment_m)) &&
       std::abs(rel_heading) > kAvoidSqueezeRelHeadingThresholdInRad);
  return {is_risky_squeezing_agent, rel_heading};
}

// Returns true if Ego is overtaking a vehicle or cyclist with a small
// lateral gap.
bool IsCloseVehicleOrCyclistOvertakenByEgo(
    const PredictionDecisionMakerOutput& agent_pdm_output,
    const double max_lateral_gap) {
  const ReasoningObject& reasoning_object = agent_pdm_output.reasoning_object();
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (!reasoning_object.is_vehicle_or_cyclist()) {
    return false;
  }

  if (object_proximity_info.abs_lateral_gap() > max_lateral_gap ||
      math::NearZero(object_proximity_info.abs_lateral_gap()) ||
      !reasoning_object.IsFullyAhead()) {
    return false;
  }

  if (object_proximity_info.signed_longitudinal_speed() < 0.0) {
    return false;
  }

  if (std::abs(object_proximity_info.relative_motion_heading()) > M_PI_4) {
    return false;
  }

  if (std::none_of(agent_pdm_output.prediction_decisions().begin(),
                   agent_pdm_output.prediction_decisions().end(),
                   [](const PredictionDecision& prediction_decision) {
                     return prediction_decision.agent_trajectory_info()
                         .is_overtaken_by_ego();
                   })) {
    return false;
  }

  return true;
}

// Adds proximity speed constraint for vehicles or cyclists overtaken by Ego to
// avoid passing them with a high speed. Returns true if a const speed proximity
// speed constraint is added.
bool AddProximitySpeedConstraintForAgentsOvertakenByEgo(
    const PredictionDecisionMakerOutput& agent_pdm_output,
    ConstraintCreator* constraint_creator_ptr) {
  constexpr double kMaxLateralGapForCyclistInMeter = 1.0;
  constexpr double kMaxLateralGapForVehicleInMeter = 0.8;
  constexpr double kMaxRelativeSpeedInMps = 5.0;
  constexpr double kMinRelativeSpeedInMps = 1.0;
  const ReasoningObject& reasoning_object = agent_pdm_output.reasoning_object();
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  const double max_lateral_gap = reasoning_object.is_vehicle()
                                     ? kMaxLateralGapForVehicleInMeter
                                     : kMaxLateralGapForCyclistInMeter;

  if (!IsCloseVehicleOrCyclistOvertakenByEgo(agent_pdm_output,
                                             max_lateral_gap)) {
    return false;
  }

  ConstraintMutator constraint_mutator =
      constraint_creator_ptr
          ->AddHorizonLengthProximitySpeedConstraintFromConstSpeed(
              /*ra_start_pos=*/object_proximity_info.projected_ra_arc_length()
                  .start(),
              /*longitudinal_speed=*/
              object_proximity_info.signed_longitudinal_speed(),
              /*agent_length=*/reasoning_object.length(),
              /*fence_type=*/pb::FenceType::kProximity,
              /*reasoner_id=*/pb::ReasonerId::CAUTIOUS_DRIVING,
              absl::StrFormat("overtaking_obj_%ld", reasoning_object.id()),
              reasoning_object.id());
  // TODO(hezhihang): Use nonlinear interpolation to calculate the relative
  // speed.
  constraint_mutator.set_proximity_relative_speed_params(
      max_lateral_gap,
      math::GetLinearInterpolatedY(0.0, max_lateral_gap, kMinRelativeSpeedInMps,
                                   kMaxRelativeSpeedInMps,
                                   object_proximity_info.abs_lateral_gap()),
      /*min_req_discomfort=*/0.0, /*min_a=*/-2.0, /*min_j=*/-2.0);
  return true;
}

// Calculate TTC for both ego and agent with constant speed model to check
// whether ego and agent under collision risk requiring non-reactive caution.
bool DoesVRUHavePotentialConflictWithEgo(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    std::string* debug_str) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double agent_abs_lateral_speed =
      std::abs(object_proximity_info.signed_lateral_speed());
  // Do not add cautious driving reference limit if the VRU is lateral
  // moving away or lateral slow movement.
  if (reasoning_object.is_laterally_moving_away() ||
      agent_abs_lateral_speed < kMinSpeedToConsiderVRUMovingCloseInMps) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: not lat move close;");
    }
    return false;
  }

  // Calculate the arrival times for both the agent and the ego vehicle
  // using a constant speed model.
  if (math::NearZero(world_context.ego_speed() -
                     object_proximity_info.signed_longitudinal_speed())) {
    return false;
  }
  // We add a lateral gap to define the conflict window.
  constexpr double kRequiredLateralGapForCycInMeter = 0.0;
  constexpr double kRequiredLateralGapForPedInMeter = 2.0;
  const double kRequiredLateralGapForVRUInMeter =
      reasoning_object.is_cyclist() ? kRequiredLateralGapForCycInMeter
                                    : kRequiredLateralGapForPedInMeter;
  double agent_arrive_time = (object_proximity_info.abs_lateral_gap() -
                              kRequiredLateralGapForVRUInMeter) /
                             agent_abs_lateral_speed;
  double agent_leave_time = (object_proximity_info.abs_lateral_gap() +
                             kRequiredLateralGapForVRUInMeter) /
                            agent_abs_lateral_speed;

  double ego_arrive_time =
      object_proximity_info.projected_ra_arc_length().start() /
      (world_context.ego_speed() -
       object_proximity_info.signed_longitudinal_speed());

  // If the agent arrive at the collision point after the ego plus with a time
  // buffer, then we considered this agent arrive late and will cut behind the
  // ego, we will not apply any cautious to it. We will use a larger buffer for
  // pedestrian to keep safe.
  constexpr double kTGapEgoPassTimeBufferForVRUInSec = 1.0;
  if (agent_arrive_time > ego_arrive_time + kTGapEgoPassTimeBufferForVRUInSec) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: arrive late, cut behind;");
    }
    return false;
  }

  // If the ego arrives at the interaction finish point after the agent plus
  // with a large "yield extra time" buffer, then we consider this agent is
  // fully passed, we will not apply any cautious to it.
  constexpr double kTGapEgoYieldTimeBufferForCycInSec = 1.0;
  if (agent_leave_time + kTGapEgoYieldTimeBufferForCycInSec < ego_arrive_time) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "no NRcautious: arrive far earlier, fully pass;");
    }
    return false;
  }

  // If the agent cannot reach the interaction point even under a high assumed
  // lateral acceleration, we consider it will cut behind and skip apply
  // cautious.
  constexpr double kAssumedAgentMaxAccelMpss = 2.0;
  const double agent_max_reachable_lateral_dist =
      agent_abs_lateral_speed * ego_arrive_time +
      0.5 * kAssumedAgentMaxAccelMpss * ego_arrive_time * ego_arrive_time;
  if (agent_max_reachable_lateral_dist <
      object_proximity_info.abs_lateral_gap()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str,
                      "no NRcautious: arrive late even with large accel;");
    }
    return false;
  }

  if (debug_str != nullptr) {
    absl::StrAppend(debug_str, "NRcautious added: have potential interaction;");
  }
  return true;
}

// Return true if cautious policy needs to be added for nonreactive VRUs.
bool ShouldConsiderCautiousForNonreactiveVRU(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config, std::string* debug_str) {
  if (reasoning_object.out_of_extended_path_range()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: out of range;");
    }
    return false;
  }

  // Exclude VRUs that are behind the ego vehicle's front axle.
  if (reasoning_object.IsBehindEgoFrontAxle()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: behind front axle;");
    }
    return false;
  }

  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  // Do not add cautious driving reference limit to the lateral faraway VRUs.
  // Note: Currently, both cyclist and pedestrian non-reactive cautious
  // configurations share the same max lateral gap. To reduce latency, we use
  // the cyclist config here. If the values diverge in the future, this logic
  // should be updated.
  if (object_proximity_info.abs_lateral_gap() >
      config.cyc_nonreactive_cautious()
          .max_lateral_gap_for_cautious_in_meter()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: lat faraway;");
    }
    return false;
  }

  // Do not add cautious driving reference limit if agent on ego's path as ego
  // will yield to them and no cautious is needed.
  if (reasoning_object.IsOnEgoPath()) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: on ego path;");
    }
    return false;
  }

  // Do not add cautious driving reference limit to the longitudinal faraway
  // VRUs.
  if (object_proximity_info.projected_ra_arc_length().start() >
      kMinLongitudinalGapForNonreactiveVRUCautiousInMeters) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: longi far away vru;");
    }
    return false;
  }

  // Currently, only cross-interaction scenarios are supported.
  // TODO(Frank): Further refine this scope to reduce FP non reactive cautious.
  constexpr double kInteractionAngleLowerBoundInRad = math::Degree2Radian(30.0);
  constexpr double kInteractionAngleUpperBoundInRad =
      math::Degree2Radian(150.0);
  if (!math::IsInRange(
          std::abs(object_proximity_info.relative_motion_heading()),
          kInteractionAngleLowerBoundInRad, kInteractionAngleUpperBoundInRad)) {
    if (debug_str != nullptr) {
      absl::StrAppend(debug_str, "no NRcautious: not cross interaction angle;");
    }
    return false;
  }

  return DoesVRUHavePotentialConflictWithEgo(world_context, reasoning_object,
                                             debug_str);
}

const math::Range1d ComputeSpeedZoneArclengthRangeForNonReactiveVRU(
    const WorldContext& world_context,
    const ReasoningObject& reasoning_object) {
  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  double agent_arrive_time =
      std::abs(object_proximity_info.signed_lateral_gap()) /
      std::abs(object_proximity_info.signed_lateral_speed());

  const double projected_arc_length =
      object_proximity_info.projected_ra_arc_length().start() -
      world_context.ra_to_leading_bumper();
  const double cautious_start_arclength = std::max(
      projected_arc_length +
          agent_arrive_time * object_proximity_info.signed_longitudinal_speed(),
      0.0);

  return {cautious_start_arclength,
          cautious_start_arclength + world_context.robot_state().GetLength() +
              reasoning_object.length()};
}

math::Range1d CalculateReferenceLimitForNonreactiveVRU(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config) {
  // Get speed limit based on lateral distance and lateral speed.
  constexpr double kRiskyTTCLowerBoundInSec = 1.0;
  constexpr double kRiskyTTCUpperBoundInSec = 5.0;

  double speed_limit_lb = std::numeric_limits<double>::infinity();
  double speed_limit_ub = std::numeric_limits<double>::infinity();

  // Higher limits are used when ego going straight to avoid unnecessary
  // slowdowns at higher speeds. Lower limits are used during turns, as ego
  // will naturally moves slower and can react more flexibly. The lower bound
  // is interpolated based on the agent’s crossing time (TTC).
  // TODO(Frank): Refactor ego turning behavior using a non-acceleration-based
  // interface instead of applying a speed limit。
  if (reasoning_object.is_cyclist()) {
    if (trajectory_info.IsGoingStraight()) {
      speed_limit_lb = config.cyc_nonreactive_cautious()
                           .min_speed_limit_for_cautious_in_mps();  // 25km/h
      speed_limit_ub = config.cyc_nonreactive_cautious()
                           .max_speed_limit_for_cautious_in_mps();  // 35km/h
    } else {
      speed_limit_lb =
          config
              .low_discomfort_collision_speed_for_nonreactive_ped_in_mps();  // 15km/h
      speed_limit_ub =
          config
              .high_discomfort_collision_speed_for_nonreactive_ped_in_mps();  // 30km/h
    }
  } else {
    DCHECK(reasoning_object.is_pedestrian());
    if (trajectory_info.IsGoingStraight()) {
      speed_limit_lb = config.ped_nonreactive_cautious()
                           .min_speed_limit_for_cautious_in_mps();  // 20km/h
      speed_limit_ub = config.ped_nonreactive_cautious()
                           .max_speed_limit_for_cautious_in_mps();  // 30km/h
    } else {
      constexpr double kMinSpeedLimitForPedInMps = math::KmphToMps(15.0);
      constexpr double kMaxSpeedLimitForPedInMps = math::KmphToMps(25.0);
      speed_limit_lb = kMinSpeedLimitForPedInMps;  // 15km/h
      speed_limit_ub = kMaxSpeedLimitForPedInMps;  // 25km/h
    }
  }

  const speed::pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  double agent_arrive_time =
      std::abs(object_proximity_info.signed_lateral_gap()) /
      std::abs(object_proximity_info.signed_lateral_speed());

  const double speed_limit_based_on_TTC = math::GetLinearInterpolatedY(
      /*x1=*/kRiskyTTCLowerBoundInSec,
      /*x2=*/kRiskyTTCUpperBoundInSec,
      /*y1=*/speed_limit_lb,
      /*y2=*/speed_limit_ub,
      /*x_eval=*/agent_arrive_time);

  return {speed_limit_based_on_TTC, speed_limit_ub};
}

// Returns true if the agent is close to ego and moving fast laterally,
// indicating a high-risk situation that ego may require stronger braking.
bool IsHighRiskFastAndCloseAgent(
    const pb::ObjectProximityInfo& object_proximity_info,
    double lateral_speed_threshold, double lateral_gap_threshold) {
  return std::abs(object_proximity_info.signed_lateral_speed()) >
             lateral_speed_threshold &&
         std::abs(object_proximity_info.signed_lateral_gap()) <
             lateral_gap_threshold;
}

}  // namespace

double CautiousDrivingReasoner::CalculateComfortSpeedLimitBasedOnLateralGap(
    const ReasoningObject& reasoning_object, double min_speed_limit,
    double max_speed_limit, double min_lateral_gap_for_cautious,
    double max_lateral_gap_for_cautious) const {
  const double abs_lateral_gap =
      reasoning_object.object_proximity_info().abs_lateral_gap();

  // If abs_lateral_gap is smaller than critical_required_lateral_gap, we rely
  // on constraints to yield to the agent.
  if (abs_lateral_gap <= reasoning_object.CriticalRequiredLateralGap() ||
      abs_lateral_gap > max_lateral_gap_for_cautious) {
    return std::numeric_limits<double>::infinity();
  }

  return math::GetLinearInterpolatedY(
      /*x1=*/min_lateral_gap_for_cautious,
      /*x2=*/max_lateral_gap_for_cautious,
      /*y1=*/min_speed_limit,
      /*y2=*/max_speed_limit,
      /*x_eval=*/reasoning_object.object_proximity_info().abs_lateral_gap());
}

std::optional<pb::CautiousDrivingSpeedLimit>
CautiousDrivingReasoner::ComputeSpeedLimitFromPrincipledLateralGap(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info) {
  // If the lateral gap is small enough that principled lateral gap thinks
  // it's necessary to add a speed limit for safety, use this non-discomfort
  // varying speed limit.
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  const double curvature = trajectory_info.GetEgoCurvature(
      object_proximity_info.projected_ra_arc_length().start());
  const std::optional<double> critical_speed_limit =
      reasoning_object.principled_required_lateral_gap().GetCriticalSpeedLimit(
          curvature, object_proximity_info.abs_lateral_gap());
  if (critical_speed_limit.has_value()) {
    pb::CautiousDrivingSpeedLimit speed_limit;
    speed_limit.set_min_speed(critical_speed_limit.value());
    speed_limit.set_max_speed(critical_speed_limit.value());
    return {speed_limit};
  }

  return std::nullopt;
}

pb::CautiousDrivingSpeedLimit
CautiousDrivingReasoner::ComputeSpeedLimitBasedOnObjectType(
    const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config, double ego_max_speed,
    std::string* debug_string) const {
  pb::CautiousDrivingSpeedLimit speed_limit;
  double comfort_speed_limit = std::numeric_limits<double>::infinity();
  double critical_speed_limit = ego_max_speed;
  if (reasoning_object.is_pedestrian_or_cyclist()) {
    if (debug_string != nullptr) {
      absl::StrAppend(debug_string, "VRU");
    }
    comfort_speed_limit = CalculateComfortSpeedLimitBasedOnLateralGap(
        reasoning_object,
        config.vru_static_cautious().min_speed_limit_for_cautious_in_mps(),
        config.vru_static_cautious().max_speed_limit_for_cautious_in_mps(),
        config.vru_static_cautious().min_lateral_gap_for_cautious_in_meter(),
        config.vru_static_cautious().max_lateral_gap_for_cautious_in_meter());
    critical_speed_limit = comfort_speed_limit;
  } else if (reasoning_object.is_animal()) {
    comfort_speed_limit = CalculateComfortSpeedLimitBasedOnLateralGap(
        reasoning_object,
        config.animal_cautious().min_speed_limit_for_cautious_in_mps(),
        config.animal_cautious().max_speed_limit_for_cautious_in_mps(),
        config.animal_cautious().min_lateral_gap_for_cautious_in_meter(),
        config.animal_cautious().max_lateral_gap_for_cautious_in_meter());
    critical_speed_limit = comfort_speed_limit;
  } else if (reasoning_util::IsPullOverTailingAgent(trajectory_info,
                                                    reasoning_object) &&
             reasoning_object.is_vehicle()) {
    // The tailing vehicle on behind of pull over gap is likely start moving
    // suddenly and leading to under yield issues. Therefore, we apply a
    // stricter cautious limit to it based on static proximity to mitigate this
    // risk.
    if (debug_string != nullptr) {
      absl::StrAppend(debug_string, "PO tail agent");
    }
    const double min_speed_for_po_tail_agent_critical_required_lat_gap =
        config
            .min_speed_for_po_tail_agent_critical_required_lateral_gap_in_mps();
    comfort_speed_limit = CalculateComfortSpeedLimitBasedOnLateralGap(
        reasoning_object, min_speed_for_po_tail_agent_critical_required_lat_gap,
        std::max(
            config.max_speed_limit_ratio_for_po_tail_agent() * ego_max_speed,
            min_speed_for_po_tail_agent_critical_required_lat_gap),
        reasoning_object.CriticalRequiredLateralGap(),
        config.lateral_gap_no_slow_down_in_meter());
  } else {
    if (debug_string != nullptr) {
      absl::StrAppend(debug_string, "normal agent");
    }
    comfort_speed_limit = CalculateComfortSpeedLimitBasedOnLateralGap(
        reasoning_object,
        config.min_speed_for_critical_required_lateral_gap_in_mps(),
        ego_max_speed, reasoning_object.CriticalRequiredLateralGap(),
        config.lateral_gap_no_slow_down_in_meter());
  }
  if (std::isinf(comfort_speed_limit) || comfort_speed_limit >= ego_max_speed) {
    // no speed limit is needed
    speed_limit.set_min_speed(std::numeric_limits<double>::infinity());
    speed_limit.set_max_speed(std::numeric_limits<double>::infinity());
  } else {
    speed_limit.set_min_speed(comfort_speed_limit);
    speed_limit.set_max_speed(critical_speed_limit);
  }
  return speed_limit;
}

// Product guidance:
// https://cooper.didichuxing.com/knowledge/share/page/mlO8TYs54oym#
// d >= 0.00018 v ^ 2 + 0.02025 v + 0.25
// To address the issues in this sheet:
// https://cooper.didichuxing.com/docs2/sheet/2201907232072,
// We tune this quadratic function to d >= 0.00018 v ^ 2 + 0.01534 v + 0.25 to
// reduce UB.
// The difference can be found in:
// https://drive.google.com/file/d/1m9xl_GJsDdBgHPSvqGABBdOnfTd84cgg/view?usp=sharing
// We define the speed curve for boundary proximity as a piecewise
// function.
// 1. when lat_gap > 0.2, we use the quadratic function
// to compute the speed limit. And we use a min_speed_for_progress (1m/s) to
// clamp the speed limit.
// 2. when lat_gap <= 0.20, we set the speed limit as 1m/s.
// https://docs.google.com/document/d/1DlMhTSOmsoXlcpfYbrJNjzj2ef2BD32FFL6lyswWCRk/edit?usp=sharing
// This function returns the speed limit at discomfort 0.
double CautiousDrivingReasoner::ComputeDesiredSpeedLimitForBoundaryProximity(
    const double lat_gap, const double min_speed_for_progress) {
  // configs preparation
  const double quad_coeff = 0.00018;        // s ^ 2 / m
  const double liner_coeff = 0.01534;       // s
  const double min_lat_gap_for_quad = 0.2;  // m
  if (lat_gap > min_lat_gap_for_quad) {
    // When the lat_gap is larger than min_lat_gap_for_quad, we try to ensure
    // progress by clamping the speed limit. As mentioned above: d >= 0.00018 v
    // ^ 2 + 0.01534 v + 0.25 so v <= the root of 0.00018 v ^ 2 + 0.01534 v +
    // 0.25 - d
    const double desired_speed_at_zero_discomfort =
        (-liner_coeff +
         std::sqrt(liner_coeff * liner_coeff -
                   4 * quad_coeff * (min_lat_gap_for_quad - lat_gap))) /
        (2 * quad_coeff);
    return std::max(desired_speed_at_zero_discomfort, min_speed_for_progress);
  } else {
    // We want to hold speed limit to min_speed_for_progress 1m/s when lat gap <
    // 0.2m
    return min_speed_for_progress;
  }
}

bool CautiousDrivingReasoner::AddReferenceLimitForObservationCautious(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    const pb::CautiousDrivingReasonerConfig& config,
    pb::CautiousDrivingReasonerSeed& current_iter_mutable_seed,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  std::string debug_str;
  const pb::ObservationCautiousConfig& observation_cautious_config =
      config.observation_cautious();
  pb::CautiousDrivingReason cautious_reason = GetObservationCautiousType(
      trajectory_info, reasoning_object, prediction_decisions,
      observation_cautious_config, world_context.ego_speed(), &debug_str);
  if (cautious_reason == pb::CautiousDrivingReason::NA) {
    return false;
  }

  // Here we stored the ego speed when we decide to speed down.
  UpdateEgoCautiousStartSpeed(world_context, current_iter_mutable_seed,
                              reasoning_object.id());

  const math::Range1d ego_strict_speed_limit_range =
      ComputeObservationCautiousSpeedLimitRange(
          current_iter_mutable_seed, observation_cautious_config,
          reasoning_object.id(), cautious_reason);

  const math::Range1d speed_zone_ra_arclength_range =
      ComputeObservationCautiousArcLengthRange(
          reasoning_object.object_proximity_info(),
          observation_cautious_config);

  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          ego_strict_speed_limit_range, speed_zone_ra_arclength_range,
          config.min_acceleration_for_risk(), reasoning_object.id());
  if (agent_debug_ptr != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/
            std::move(debug_str),
            pb::CautiousDrivingDebugForScene::UNCERTAIN_RISKY_AGENT,
            *agent_debug_ptr);

    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        ego_strict_speed_limit_range.start_pos,
        ego_strict_speed_limit_range.end_pos,
        speed_zone_ra_arclength_range.start_pos,
        speed_zone_ra_arclength_range.end_pos,
        config.min_acceleration_for_risk(), is_reference_profile_updated,
        *(debug_for_scene.mutable_limiter_parameters()));
  }
  if (is_reference_profile_updated &&
      ego_strict_speed_limit_range.start_pos < world_context.ego_speed()) {
    // Add rt event when the cautious is added and the speed limit is lower than
    // ego's current speed, which means this cautious causes ego to brake.
    switch (cautious_reason) {
      case pb::CautiousDrivingReason::POTENTIAL_CUT_IN_AGENT_FROM_PROXIMITY:
        rt_event::PostRtEvent<
            rt_event::planner::UncertainRiskyCautiousFromProximity>();
        break;
      case pb::CautiousDrivingReason::U_TURN_AGENT_WITH_HIGH_RISK:
      case pb::CautiousDrivingReason::U_TURN_AGENT_WITH_LOW_RISK:
        rt_event::PostRtEvent<
            rt_event::planner::UncertainRiskyCautiousFromUTurn>();
        break;
      case pb::CautiousDrivingReason::POTENTIAL_CUT_IN_AGENT_FROM_ELA:
        rt_event::PostRtEvent<
            rt_event::planner::UncertainRiskyCautiousFromELA>();
        break;
      default:
        // TODO(junying): add rt events for other agents
        break;
    }
    if (cautious_reason != pb::CautiousDrivingReason::NA &&
        reasoning_object.is_cyclist()) {
      // Currently we have proximity based UNCERTAIN_RISKY_CAUTIOUS or
      // U_TURN_AGENT_WITH_LOW_RISK for CYC
      rt_event::PostRtEvent<rt_event::planner::UncertainRiskyCautiousForCyc>();
    }
  }

  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::
    AddProximitySpeedConstraintForNonreactiveCrossVehicle(
        const WorldContext& world_context,
        const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
        const pb::CautiousDrivingReasonerConfig& config,
        ConstraintCreator* constraint_creator_ptr,
        pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  // Add cautious strategy for vehicles.
  if (!agent_policy.reasoning_object().is_vehicle()) {
    return false;
  }

  const speed::pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  DCHECK(overlap_region.contain_strict_overlap());

  const speed::pb::OverlapSlice& nearest_strict_overlap_slice =
      overlap_region.overlap_slices(
          reasoning_util::GetNearestStrictOverlapSliceIdx(overlap_region));

  const math::Range1d ego_strict_speed_limit_range =
      CalculateReferenceLimitForNonreactiveVehicleStrictOverlap(
          agent_policy.reasoning_object(), trajectory_info,
          nearest_strict_overlap_slice, config);

  const double min_req_discomfort =
      ComputeMinRequiredDiscomfortToApplyNonreactiveCautious(
          agent_policy.reasoning_object(), world_context.ego_pose_timestamp());
  // For the parameter, we use the critical required lateral gap as the
  // mat_lat_gap and the critical slow down speed as the max_speed.
  AddProximitySpeedConstraintForOverlapRegion(
      agent_policy.reasoning_object(), agent_policy.OverlapRegion(),
      agent_policy.reasoner_in_charge(),
      /*required_lateral_gap=*/
      agent_policy.reasoning_object().ComfortRequiredLateralGap(),
      /*max_speed=*/ego_strict_speed_limit_range.end_pos,
      /*min_req_discomfort=*/
      min_req_discomfort, constraint_creator_ptr);

  if (agent_debug_ptr != nullptr) {
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(), ego_strict_speed_limit_range,
        {GetPaddedOverlapStart(agent_policy.OverlapRegion()),
         GetPaddedOverlapEnd(agent_policy.OverlapRegion())},
        /*strength=*/1.0, config.min_acceleration_for_risk(),
        /*is_reference_profile_updated=*/false, overlap_region.region_id(),
        agent_policy.cautious_driving_reason(),
        /*debug_string=*/
        absl::StrCat("proximity constraint added. min_req_discomfort: ",
                     min_req_discomfort),
        *agent_debug_ptr);
  }
  return true;
}

bool CautiousDrivingReasoner::AddReferenceLimitForNonreactivePed(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  const speed::pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();

  DCHECK(overlap_region.contain_strict_overlap());

  const math::Range1d ego_strict_speed_limit_range =
      CalculateReferenceLimitForNonreactivePedStrictOverlap(
          trajectory_info, overlap_region, config);
  const math::Range1d speed_zone_ra_arclength_range =
      ComputeSpeedZoneArclengthRange(overlap_region,
                                     /*should_use_strict_overlap=*/true);
  // For speed limit, the start value is the value at the min
  // discomfort, and the end value is the value at the max discomfort.
  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          ego_strict_speed_limit_range, speed_zone_ra_arclength_range,
          config.min_acceleration_for_risk(),
          agent_policy.reasoning_object().id());

  if (agent_debug_ptr != nullptr) {
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(), ego_strict_speed_limit_range,
        speed_zone_ra_arclength_range, /*strength=*/1.0,
        config.min_acceleration_for_risk(), is_reference_profile_updated,
        overlap_region.region_id(), agent_policy.cautious_driving_reason(),
        /*debug_string=*/"", *agent_debug_ptr);
  }

  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitToNonReactiveVRU(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  // Currently we only consider cyclist for the non-reactive approaching
  // cautious.
  if (!reasoning_object.is_pedestrian_or_cyclist()) {
    return false;
  }

  std::string debug_string;
  pb::CautiousDrivingDebugForScene::DynamicProximitySceneRecognition
      scene_recogination =
          reasoning_object.is_cyclist()
              ? pb::CautiousDrivingDebugForScene::NON_REACTIVE_CYC
              : pb::CautiousDrivingDebugForScene::NON_REACTIVE_PED;
  // Make some filtering on the object.
  if (!ShouldConsiderCautiousForNonreactiveVRU(world_context, reasoning_object,
                                               config, &debug_string)) {
    if (cautious_driving_agent_debug != nullptr) {
      pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              /*debug_string=*/std::move(debug_string),
              pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
              *cautious_driving_agent_debug);
      debug_for_scene.set_dynamic_proximity_scene(scene_recogination);
    }
    return false;
  }

  const math::Range1d speed_zone_ra_arclength_range =
      ComputeSpeedZoneArclengthRangeForNonReactiveVRU(world_context,
                                                      reasoning_object);

  const math::Range1d speed_limit = CalculateReferenceLimitForNonreactiveVRU(
      trajectory_info, reasoning_object, config);

  constexpr double kMinAccelForExtremelyRiskyAgentInMpss = -3.0;
  constexpr double kCycLateralSpeedToBrakeHarderInMps = 3.0;
  constexpr double kPedLateralSpeedToBrakeHarderInMps = 2.0;
  constexpr double kLateralGapToBrakeHarderInMeters = 5.0;
  // For high risky agents that are very close and approaching quickly from the
  // side, apply stronger deceleration.
  const double non_reactive_cautious_accel =
      IsHighRiskFastAndCloseAgent(reasoning_object.object_proximity_info(),
                                  reasoning_object.is_cyclist()
                                      ? kCycLateralSpeedToBrakeHarderInMps
                                      : kPedLateralSpeedToBrakeHarderInMps,
                                  kLateralGapToBrakeHarderInMeters)
          ? kMinAccelForExtremelyRiskyAgentInMpss
          : config.min_acceleration_for_risk();

  // For speed limit, the start value is the value at the min
  // discomfort, and the end value is the value at the max discomfort.
  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          speed_limit, speed_zone_ra_arclength_range,
          non_reactive_cautious_accel, reasoning_object.id());

  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/std::move(debug_string),
            pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
            *cautious_driving_agent_debug);
    debug_for_scene.set_dynamic_proximity_scene(scene_recogination);
    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        speed_limit.start_pos, speed_limit.end_pos,
        speed_zone_ra_arclength_range.start_pos,
        speed_zone_ra_arclength_range.end_pos, non_reactive_cautious_accel,
        is_reference_profile_updated,
        *(debug_for_scene.mutable_limiter_parameters()));
  }

  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitForRoundaboutAgent(
    const TrajectoryInfo& trajectory_info, const AgentPolicy& agent_policy,
    double ego_speed, const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) const {
  const speed::pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  if (!overlap_region.contain_strict_overlap()) {
    return false;
  }
  const pb::RoundaboutAgentConfig& roundabout_config =
      config.roundabout_agent();
  const pb::ObjectProximityInfo& object_proximity_info =
      agent_policy.reasoning_object().object_proximity_info();

  std::string debug_string;
  math::Range1d speed_zone_ra_arclength_range;
  pb::CautiousDrivingSpeedLimit ego_speed_limit_range;

  if (agent_policy.agent_trajectory_info()
          .is_agent_exiting_roundabout_from_inner_lane()) {
    // Add speed limit zone ranging from the start pos of agent's current ra
    // arclength projection minus offset to the start pos of the strict overlap.
    const double start_pos = std::max(
        0.0, object_proximity_info.projected_ra_arc_length().start() -
                 roundabout_config.speed_limit_range_start_offset_in_meter());
    // TODO(nihar): Make sure the bumper is correct for the
    const auto& vehicle_params =
        trajectory_info.unextended_path_for_overlap().vehicle_params();
    const double leading_bumper_offset_from_ra =
        vehicle_params.leading_bumper_offset;
    const double end_pos =
        GetStrictOverlapStart(overlap_region) + leading_bumper_offset_from_ra;

    DCHECK(overlap_region.motion_type() != pb::OVERLAP_MOTION_ONCOMING);
    // As agents exiting roundabout from inner lane are supposed to be not yet
    // or just touch the ego path, therefore the proximity start pos should be
    // no greater than the strict over lap start pos.
    DCHECK_LE(start_pos, end_pos)
        << "Proximity start: "
        << object_proximity_info.projected_ra_arc_length().start()
        << " Strict overlap start: " << GetStrictOverlapStart(overlap_region)
        << ".\n";
    // No need to add speed limiter if the start pos equals the end pos (i.e.,
    // at the start moment of overlapping) or the start pos is far ahead.
    if (start_pos == end_pos ||
        start_pos >=
            roundabout_config.speed_limit_range_end_upper_bound_in_meter()) {
      return false;
    }

    speed_zone_ra_arclength_range.start_pos = start_pos;
    speed_zone_ra_arclength_range.end_pos = std::min(
        end_pos,
        roundabout_config.speed_limit_range_end_upper_bound_in_meter());

    const double road_speed_limit =
        trajectory_info.GetEgoMaxSpeedInArclengthRange(
            object_proximity_info.projected_ra_arc_length());

    // Compute a lower bound for min speed limit to avoid over-slowdown.
    const double min_speed_limit_lower_bound = std::min(
        road_speed_limit *
            roundabout_config.min_speed_limit_lower_bound_ratio_road_limit(),
        agent_policy.reasoning_object().planner_object().speed() *
            roundabout_config.min_speed_limit_lower_bound_ratio_agent_speed());
    const double min_speed =
        std::max(ego_speed * roundabout_config.min_speed_limit_ratio(),
                 min_speed_limit_lower_bound);

    const double max_speed = CalculateComfortSpeedLimitBasedOnLateralGap(
        agent_policy.reasoning_object(), min_speed, road_speed_limit,
        agent_policy.reasoning_object().CriticalRequiredLateralGap(),
        roundabout_config.lateral_gap_no_slowdown_limit_in_meter());

    ego_speed_limit_range.set_max_speed(max_speed);
    ego_speed_limit_range.set_min_speed(min_speed);

    debug_string = "ROUNDABOUT_AGENT:inner";
  }

  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          ego_speed_limit_range, speed_zone_ra_arclength_range,
          config.min_acceleration_for_risk(),
          agent_policy.reasoning_object().id());

  pb::CautiousDrivingDebugForScene& debug_for_scene =
      AppendCautiousDrivingDebugForScene(
          /*debug_string=*/
          std::move(debug_string),
          pb::CautiousDrivingDebugForScene::ROUNDABOUT_AGENT, *agent_debug_ptr);

  reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
      ego_speed_limit_range.min_speed(), ego_speed_limit_range.max_speed(),
      speed_zone_ra_arclength_range.start_pos,
      speed_zone_ra_arclength_range.end_pos, config.min_acceleration_for_risk(),
      is_reference_profile_updated,
      *(debug_for_scene.mutable_limiter_parameters()));
  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitForDrivingThroughFoD(
    const AgentPolicy& agent_policy,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  DCHECK(agent_policy.cautious_driving_reason() ==
         pb::CautiousDrivingReason::DRIVING_THROUGH_FOD);
  DCHECK(agent_policy.reasoning_object().is_fod_preferable_to_drive_through());

  const speed::pb::OverlapRegion& overlap_region = agent_policy.OverlapRegion();
  // Early return if ego path has no strict overlaps with the FoD.
  if (!overlap_region.contain_strict_overlap()) return false;

  // According to the product doc, Ego should slow down when drive through the
  // FoD. https://cooper.didichuxing.com/docs/sheet/2200214783458.
  constexpr double kDrivingThroughFoDSpeedLimitInMps = 8.333;  // 30 km/h
  const math::Range1d speed_limit_range = {kDrivingThroughFoDSpeedLimitInMps,
                                           kDrivingThroughFoDSpeedLimitInMps};

  const math::Range1d speed_zone_ra_arclength_range =
      ComputeSpeedZoneArclengthRange(overlap_region,
                                     /*should_use_strict_overlap=*/true);

  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(
          speed_limit_range, speed_zone_ra_arclength_range,
          config.min_acceleration_for_risk(),
          agent_policy.reasoning_object().id());

  if (agent_debug_ptr != nullptr) {
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(), speed_limit_range,
        speed_zone_ra_arclength_range, /*strength=*/1.0,
        config.min_acceleration_for_risk(), is_reference_profile_updated,
        overlap_region.region_id(), agent_policy.cautious_driving_reason(),
        /*debug_string=*/"", *agent_debug_ptr);
  }

  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitForPolicy(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy, double ego_speed,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  if (agent_policy.reasoner_in_charge() != pb::ReasonerId::CAUTIOUS_DRIVING) {
    return false;
  }

  const pb::CautiousDrivingReason cautious_driving_reason =
      agent_policy.cautious_driving_reason();

  switch (cautious_driving_reason) {
    case pb::CautiousDrivingReason::NONREACTIVE_AGENT:
      if (agent_policy.reasoning_object().is_pedestrian()) {
        return AddReferenceLimitForNonreactivePed(trajectory_info, agent_policy,
                                                  config, reference_limiter_ptr,
                                                  agent_debug_ptr);
      } else {
        return AddProximitySpeedConstraintForNonreactiveCrossVehicle(
            world_context, trajectory_info, agent_policy, config,
            constraint_creator_ptr, agent_debug_ptr);
      }
    case pb::CautiousDrivingReason::ROUNDABOUT_AGENT:
      return AddReferenceLimitForRoundaboutAgent(
          trajectory_info, agent_policy, ego_speed, config,
          reference_limiter_ptr, agent_debug_ptr);
    case pb::CautiousDrivingReason::UNSTUCK_WITH_LAT_GAP_LESS_THAN_CRITICAL:
      return AddReferenceLimitToUnstuckWithLateralGapLessThanCritical(
          world_context, agent_policy.reasoning_object(), trajectory_info,
          config, reference_limiter_ptr, agent_debug_ptr);
    case pb::CautiousDrivingReason::DRIVING_THROUGH_FOD:
      return AddReferenceLimitForDrivingThroughFoD(
          agent_policy, config, reference_limiter_ptr, agent_debug_ptr);
    case pb::CautiousDrivingReason::LOW_LIKELIHOOD_TRAJECTORY:
      return AddProximitySpeedConstraintForLowLikelihoodPredictedTrajectory(
          trajectory_info, agent_policy, config, constraint_creator_ptr,
          agent_debug_ptr);
    case pb::CautiousDrivingReason::ONCOMING_CYCLIST:
      return AddReferenceLimitForOncomingCyclist(
          world_context, trajectory_info, agent_policy, config,
          reference_limiter_ptr, agent_debug_ptr);
    case pb::CautiousDrivingReason::SLOW_CUT_IN_AGENT:
    case pb::CautiousDrivingReason::CAUTIOUS_ONLY_TO_STM_AGENT:
      return AddReferenceLimitForSlowCutInAgent(
          world_context, trajectory_info, agent_policy,
          config.slow_cut_in_agent(), reference_limiter_ptr, agent_debug_ptr);
    default:
      DCHECK(false) << pb::CautiousDrivingReason_Name(cautious_driving_reason)
                    << " is NOT handled by CautiousDrivingReasoner";
      return false;
  }
}

bool CautiousDrivingReasoner::AddReferenceLimitForBoundaryProximity(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr,
    CautiousDrivingLimiter* reference_limiter_ptr,
    planner::pb::EgoStuckFeatureAboutDecoupledSpeed*
        ego_stuck_feature_about_speed,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  // The cause we use a low-level speed limit searching method rather than using
  // GetEgoMaxSpeedInArclengthRange directly is the runtime concern. We need to
  // get the responding road limit for each path point.
  const std::vector<traffic_rules::LaneSpeedLimit>& ego_speed_limits =
      trajectory_info.traffic_rules().speed_limits;

  bool reference_limit_added = false;
  for (const math::pb::Side side :
       {math::pb::Side::kLeft, math::pb::Side::kRight}) {
    // Assist stuck feature extraction.
    pb::CautiousDrivingBoundaryDebug* cautious_driving_boundary_debug =
        ego_stuck_feature_about_speed->add_speed_limits_for_boundaries();
    if (cautious_driving_boundary_debug) {
      cautious_driving_boundary_debug->set_side(side);
    }

    const speed::pb::PhysicalBoundaryProximityInfo& pb_proximity_info =
        side == math::pb::Side::kLeft
            ? trajectory_info.closest_physical_boundary_proximity_info().left()
            : trajectory_info.closest_physical_boundary_proximity_info()
                  .right();
    DCHECK_EQ(pb_proximity_info.ra_arc_length_on_path_size(),
              pb_proximity_info.abs_distance_size());
    // this idx is a pointer pointing to the responding road limit
    // check the comments of FindRoadSpeedLimit for more details
    size_t curr_road_limit_idx = 0;
    // for each arc_length, check the responding distance between path to the
    // physical boundary to see if the reference limit is needed
    for (int i = 0; i < pb_proximity_info.ra_arc_length_on_path_size(); ++i) {
      // find the road speed limit at current ra_arc_length_on_path
      const double ra_arc_length_on_path =
          pb_proximity_info.ra_arc_length_on_path(i);
      double curr_road_limit = std::numeric_limits<double>::infinity();
      std::tie(curr_road_limit_idx, curr_road_limit) = FindRoadSpeedLimit(
          ego_speed_limits, curr_road_limit_idx, ra_arc_length_on_path);
      const double abs_distance = pb_proximity_info.abs_distance(i);
      if (abs_distance <
          config.lateral_gap_to_stop_for_boundary_proximity_in_meter()) {
        ConstraintMutator mutator = constraint_creator_ptr->AddStopPoint(
            /*start_time=*/0.0,
            /*end_time=*/constants::kTrajectoryHorizonInSec,
            /*ra_stop_x=*/
            ra_arc_length_on_path + world_context.ra_to_leading_bumper(),
            /*fence_type=*/side == math::pb::Side::kLeft
                ? pb::FenceType::kLeftSideHardRoadBoundary
                : pb::FenceType::kRightSideHardRoadBoundary,
            pb::ReasonerId::CAUTIOUS_DRIVING,
            absl::StrCat("hardboundary-",
                         side == math::pb::Side::kLeft ? "left" : "right"));
        mutator.set_allow_emergency_brake();
        break;
      }

      // We don't consider the physical boundary when it is far away to reduce
      // the computation.
      if (abs_distance <
          config.lateral_gap_no_slow_down_for_boundary_proximity_in_meter()) {
        double desired_speed = ComputeDesiredSpeedLimitForBoundaryProximity(
            abs_distance,
            config.min_speed_along_physical_boundary_for_progress());
        if (desired_speed >= curr_road_limit) {
          continue;
        }
        const bool is_reference_profile_updated =
            reference_limiter_ptr->AddBoundaryReferenceLimit(
                desired_speed, desired_speed, pb_proximity_info.path_index(i),
                config.min_acceleration_for_risk(), side);
        reference_limit_added |= is_reference_profile_updated;

        if (cautious_driving_boundary_debug != nullptr &&
            is_reference_profile_updated) {
          reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
              desired_speed, curr_road_limit,
              pb_proximity_info.ra_arc_length_on_path(i),
              pb_proximity_info.ra_arc_length_on_path(i),
              config.min_acceleration_for_risk(), is_reference_profile_updated,
              *(cautious_driving_boundary_debug->add_limits()));
        }
      }
    }
  }
  // debug info preparation
  if (cautious_driving_reasoner_debug) {
    cautious_driving_reasoner_debug->mutable_boundaries()->CopyFrom(
        ego_stuck_feature_about_speed->speed_limits_for_boundaries());
  }
  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForPrediction(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const PredictionDecision& prediction_decision,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  bool reference_profile_updated = false;
  if (prediction_decision.Ignore()) {
    DCHECK(prediction_decision.agent_policies().empty());
    return false;
  }

  for (const AgentPolicy& agent_policy : prediction_decision.agent_policies()) {
    reference_profile_updated |= AddReferenceLimitForPolicy(
        world_context, trajectory_info, agent_policy, world_context.ego_speed(),
        config, reference_limiter_ptr, constraint_creator_ptr, agent_debug_ptr);
  }
  return reference_profile_updated;
}

bool CautiousDrivingReasoner::IsObjectInterferingInLane(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object) {
  const math::geometry::PolygonWithCache2d& object_contour =
      reasoning_object.planner_object().contour();
  for (auto lane_iter = trajectory_info.lane_sequence_iterator().current_lane();
       lane_iter != trajectory_info.lane_sequence_iterator().end();
       ++lane_iter) {
    const math::geometry::PolygonWithCache2d& lane_border =
        DCHECK_NOTNULL(*lane_iter)->border();
    if (math::geometry::Intersects(lane_border, object_contour)) {
      return true;
    }
  }
  return false;
}

bool CautiousDrivingReasoner::IsObjectCausingOcclusion(
    const WorldContext& /* world_context */,
    const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config) {
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  // Ignore if the agent is outside the physical boundary.
  if (reasoning_object.is_outside_physical_boundary()) {
    return false;
  }

  // Ignore if the agent is laterally far away from ego.
  if (object_proximity_info.abs_lateral_gap() >=
      config.max_lateral_gap_to_consider_occlusion_in_meter()) {
    return false;
  }

  // Ignore if the agent has been left behind ego.
  if (reasoning_object.is_fully_behind()) {
    return false;
  }

  // A nearby, large, stationary vehicle will cause occlusion.
  if (reasoning_object.IsStationary() &&
      reasoning_object.IsVehicleLikelyToCauseOcclusion()) {
    return true;
  }

  return false;
}

bool CautiousDrivingReasoner::AddCautiousSpeedLimitForNewDetectObject(
    const ReasoningObject& reasoning_object,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  std::string debug_string;
  std::string* debug_string_ptr =
      (cautious_driving_agent_debug != nullptr &&
       (reasoning_object.is_pedestrian() || reasoning_object.is_cyclist()))
          ? &debug_string
          : nullptr;
  // Make some filtering on the object.
  if (!ShouldConsiderAddingCautiousSpeedLimitForNewDetectObject(
          reasoning_object, kMaxDistanceToAddCautiousForNewDetectAgentInMeter,
          debug_string_ptr)) {
    if (debug_string_ptr != nullptr) {
      AppendCautiousDrivingDebugForScene(
          /*debug_string=*/std::move(debug_string),
          pb::CautiousDrivingDebugForScene::NEW_DETECT_AGENT,
          *cautious_driving_agent_debug);
    }
    return false;
  }

  const std::string const_speed_constraint_id = absl::StrFormat(
      "new-detect-obj-%i-const-speed-constraint", reasoning_object.id());
  ConstraintMutator mutator =
      constraint_creator_ptr->AddHorizonLengthSpeedConstraintFromConstSpeed(
          reasoning_object.object_proximity_info()
              .projected_ra_arc_length()
              .start(),
          reasoning_object.object_proximity_info().signed_longitudinal_speed(),
          reasoning_object.length(), pb::FenceType::kProximity, id(),
          const_speed_constraint_id, reasoning_object.id());

  // For limit the tracking agent, we will set a strong cautious that brake
  // acceleration to -4 m/s^2 and brake to a low speed to 5 m/s.
  constexpr double kMaxDecelerationForLimitTrackObjectInMpss = -4.0;
  mutator.set_soft_yield_with_min_speed_accel(
      ComputeComfortSpeedLimitForNewDetectAgent(reasoning_object),
      kMaxDecelerationForLimitTrackObjectInMpss);

  absl::StrAppend(&debug_string, "const speed constraint added;");
  AppendCautiousDrivingDebugForScene(
      /*debug_string=*/std::move(debug_string),
      pb::CautiousDrivingDebugForScene::NEW_DETECT_AGENT,
      *cautious_driving_agent_debug);

  return true;
}

bool CautiousDrivingReasoner::AddReferenceLimitForObjectProximity(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    ReasoningPolicies& reasoning_policies,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  // The length of VRUs are small, so their arc-length range of cautious driving
  // limit is small. This causes that Ego cannot reduce to the expected speed.
  constexpr double kCautiousDrivingLimitArclengthRangeBufferForVRUInMeters =
      5.0;
  bool reference_limit_added = false;
  // Make some filtering on the object.
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (!ShouldConsiderObjectForProximity(world_context, reasoning_object,
                                        config)) {
    return false;
  }

  std::string debug_string;
  // First check if the object is so close that we need to add strong
  // speed limit for safety.
  std::optional<pb::CautiousDrivingSpeedLimit> critical_speed_limit =
      ComputeSpeedLimitFromPrincipledLateralGap(reasoning_object,
                                                trajectory_info);
  pb::CautiousDrivingSpeedLimit cautious_speed_limit;
  const double ego_max_speed_limit =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(
          object_proximity_info.projected_ra_arc_length());
  const pb::CautiousDrivingSpeedLimit rule_based_cautious_speed_limit =
      ComputeSpeedLimitBasedOnObjectType(reasoning_object, trajectory_info,
                                         config, ego_max_speed_limit,
                                         &debug_string);

  // Calculate max/min speed limit.
  if (critical_speed_limit.has_value()) {
    cautious_speed_limit = critical_speed_limit.value();
    absl::StrAppend(&debug_string, "close PLG");
  } else {
    DCHECK(  // NOLINT, FP when checking macro DCHECK
        reasoning_object.IsStationary() ||
        reasoning_object.object_type() == voy::perception::ObjectType::PED ||
        reasoning_object.is_animal());
    cautious_speed_limit = rule_based_cautious_speed_limit;

    absl::StrAppend(&debug_string, "add rule-based limit.");
  }

  // TODO(yanlong): Fix the bug when lateral gap is less than critical
  // threshold.
  if (math::NearZero(cautious_speed_limit.max_speed()) ||
      std::isinf(cautious_speed_limit.min_speed())) {
    return false;
  }

  const double min_accel = critical_speed_limit.has_value()
                               ? config.min_acceleration_for_safety()
                               : config.min_acceleration_for_risk();

  double speed_zone_range_front_buffer_in_meter = 0.0;
  if (reasoning_object.is_pedestrian_or_cyclist() ||
      reasoning_object.is_animal()) {
    // Enlarge the range of cautious driving limit, so that Ego can reach the
    // expected speed.
    speed_zone_range_front_buffer_in_meter =
        kCautiousDrivingLimitArclengthRangeBufferForVRUInMeters;
  }

  math::Range1d speed_zone_ra_arclength_range = {
      std::max(0.0, object_proximity_info.projected_ra_arc_length().start() -
                        world_context.ra_to_leading_bumper() -
                        speed_zone_range_front_buffer_in_meter),
      object_proximity_info.projected_ra_arc_length().end() -
          world_context.ra_to_leading_bumper()};

  // Adds the cautious speed limit to reasoning policies. Note that currently we
  // do not know the actual speed yet and set 0.0 as place holder.
  reasoning_policies.static_proximity_cautious_reasoning_policies.emplace(
      std::make_pair(
          reasoning_object.id(),
          StaticProximityCautiousReasoningPolicy(
              cautious_speed_limit, speed_zone_ra_arclength_range, min_accel,
              /*actual_speed=*/math::Range1d{-1.0, -1.0},
              /*policy_id=*/
              absl::StrCat(reasoning_object.id(), "-static-proximity"))));

  reference_limit_added = reference_limiter_ptr->AddReferenceLimit(
      cautious_speed_limit, speed_zone_ra_arclength_range, min_accel,
      reasoning_object.id());

  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/std::move(debug_string),
            pb::CautiousDrivingDebugForScene::STATIC_PROXIMITY,
            *cautious_driving_agent_debug);
    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        cautious_speed_limit.min_speed(), cautious_speed_limit.max_speed(),
        speed_zone_ra_arclength_range.start_pos,
        speed_zone_ra_arclength_range.end_pos, min_accel, reference_limit_added,
        *(debug_for_scene.mutable_limiter_parameters()));
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForOcclusion(
    const WorldContext& world_context, const ReasoningObject& reasoning_object,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  bool reference_limit_added = false;

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  if (!IsObjectCausingOcclusion(world_context, reasoning_object, config)) {
    return false;
  }

  // Whether the occlusion object interferes in ego lane.
  const bool is_object_interfere_in_lane =
      IsObjectInterferingInLane(trajectory_info, reasoning_object);
  double min_speed_for_occlusion =
      is_object_interfere_in_lane
          ? config.min_speed_for_occlusion_in_mps()
          : std::max(config.min_speed_for_occlusion_in_mps(),
                     world_context.ego_speed() *
                         config.cautious_speed_limit_ratio());

  // Calculate max/min speed.
  pb::CautiousDrivingSpeedLimit cautious_speed_limit;
  const double comfort_speed_limit =
      CalculateComfortSpeedLimitBasedOnLateralGap(
          reasoning_object, min_speed_for_occlusion,
          trajectory_info.lane_sequence_iterator()
              .current_lane()[0]
              ->speed_limits()[0]
              .limit_max(),
          reasoning_object.CriticalRequiredLateralGap(),
          config.lateral_gap_occlusion_no_slow_down_in_meter());
  // TODO(pengxu): to keep noop, we still use the 0.8 here, need to revisit this
  // logic and set a more reasonable and suitable parameter.
  cautious_speed_limit.set_max_speed(comfort_speed_limit);
  cautious_speed_limit.set_min_speed(comfort_speed_limit * 0.8);

  if (std::isinf(cautious_speed_limit.max_speed())) {
    return false;
  }

  const math::Range1d speed_zone_ra_arclength_range = {
      object_proximity_info.projected_ra_arc_length().start(),
      object_proximity_info.projected_ra_arc_length().end()};

  reference_limit_added = reference_limiter_ptr->AddReferenceLimit(
      cautious_speed_limit, speed_zone_ra_arclength_range,
      config.min_acceleration_for_risk(), reasoning_object.id());

  if (cautious_driving_agent_debug != nullptr) {
    std::string debug_string =
        is_object_interfere_in_lane
            ? "OCCLUSION: Object interfering in ego lane."
            : "OCCLUSION: Object completely in side lane.";
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/std::move(debug_string),
            pb::CautiousDrivingDebugForScene::OCCLUSION,
            *cautious_driving_agent_debug);
    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        cautious_speed_limit.min_speed(), cautious_speed_limit.max_speed(),
        object_proximity_info.projected_ra_arc_length().start(),
        object_proximity_info.projected_ra_arc_length().end(),
        config.min_acceleration_for_risk(), reference_limit_added,
        *(debug_for_scene.mutable_limiter_parameters()));
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForMapChangeRegion(
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  pb::ScenarioBasedCautiousDrivingDebug* scenario_debug =
      cautious_driving_reasoner_debug != nullptr
          ? cautious_driving_reasoner_debug->add_scenarios()
          : nullptr;
  bool reference_limit_added = false;
  for (const traffic_rules::LaneInfoInLaneSequence& lane_info :
       trajectory_info.traffic_rules().lane_sequence_lanes) {
    if (lane_info.lane == nullptr || !lane_info.lane->is_temp_lane()) {
      continue;
    }
    const bool limit_added = reference_limiter_ptr->AddScenarioReferenceLimit(
        config.min_speed_for_map_change_region_in_mps(),
        config.max_speed_for_map_change_region_in_mps(),
        lane_info.start_ra_arclength, lane_info.end_ra_arclength,
        config.min_acceleration_for_comfort(),
        speed::pb::CautiousSpeedLimiterScenarioType::MAP_CHANGE_REGION);
    reference_limit_added |= limit_added;
    // Populate the debug info.
    if (scenario_debug != nullptr) {
      scenario_debug->set_scenario_type(
          pb::ScenarioBasedCautiousDrivingDebug::MAP_CHANGE_REGION);
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          config.min_speed_for_map_change_region_in_mps(),
          config.max_speed_for_map_change_region_in_mps(),
          lane_info.start_ra_arclength, lane_info.end_ra_arclength,
          config.min_acceleration_for_comfort(), limit_added,
          *(scenario_debug->add_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForAgentCloseToLaneBoundary(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object, bool has_made_prediction_decision,
    double ego_speed, const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  pb::CautiousDrivingDebugForScene::DynamicProximitySceneRecognition
      scene_recogination = pb::CautiousDrivingDebugForScene::NA;
  if (!ShouldConsiderCloseToLaneBoundaryLimits(
          world_context, reasoning_object, has_made_prediction_decision,
          ego_speed, config.dynamic_proximity_agent(), scene_recogination)) {
    // To reduce debug size, we would only output verbose debug info in
    // simulation mode and limited road test scenarios.
    const bool should_add_debug_info =
        av_comm::InSimulation() ||
        reasoning_object.is_laterally_encroach_ego_lane_boundary();
    if (cautious_driving_agent_debug != nullptr && should_add_debug_info) {
      pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              /*debug_string=*/"",
              pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
              *cautious_driving_agent_debug);
      debug_for_scene.set_dynamic_proximity_scene(scene_recogination);
    }
    return false;
  }

  std::string debug_string = "Close To Lane Boundary:";
  // 1. Compute the desired speed limit.
  // 1.a Compute the agent to lane boundary distance.
  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  math::pb::Side lane_boundary_side = proximity_info.signed_lateral_gap() > 0.
                                          ? math::pb::Side::kLeft
                                          : math::pb::Side::kRight;
  const double object_lane_boundary_dist =
      reasoning_util::ComputeAgentToLaneBoundaryMinDistance(
          trajectory_info.lane_boundary_pair(),
          reasoning_object.planner_object().contour(), lane_boundary_side);
  // TODO(junyingshen): This logic is kind of simple and some trick corner cases
  // may happen(turn cases). Need to refine this logic later.
  const double signed_object_lateral_moving_distance_in_lookahead_window =
      config.dynamic_proximity_agent()
          .agent_close_to_lane_boundary_look_ahead_time_in_sec() *
      proximity_info.signed_lateral_speed();
  const double future_object_lane_boundary_dist =
      object_lane_boundary_dist -
      std::abs(signed_object_lateral_moving_distance_in_lookahead_window);
  const double future_abs_lateral_gap =
      std::abs(proximity_info.signed_lateral_gap() +
               signed_object_lateral_moving_distance_in_lookahead_window);
  // Due to the size of large vehicles, they are easily getting close to the
  // lane boundary even when they are going straight, so we need a filter of
  // current distance to the lane boundary. In addition, we also need to exclude
  // agents whose future lateral gap to ego is still greater than comfort gap to
  // reduce FP.
  const bool should_be_cautious_for_large_vehicle =
      future_object_lane_boundary_dist <=
          kMaxFutureDistToLaneBoundaryForLargeVehicleDynamicProximityInMeter &&
      object_lane_boundary_dist <=
          config.dynamic_proximity_agent()
              .lateral_gap_close_to_lane_boundary_no_slow_down_in_meter() &&
      future_abs_lateral_gap <= reasoning_object.ComfortRequiredLateralGap();
  // When the distance is more than the max_slow_down_lat_gap_to_lane_boundary,
  // there is no need to add speed limits.
  const bool will_not_be_close_to_lane =
      future_object_lane_boundary_dist >
          config.dynamic_proximity_agent()
              .lateral_gap_close_to_lane_boundary_no_slow_down_in_meter() ||
      (reasoning_object.IsLargeVehicle() &&
       !should_be_cautious_for_large_vehicle);
  if (will_not_be_close_to_lane) {
    if (cautious_driving_agent_debug != nullptr) {
      pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              absl::StrFormat("Curr dist: %.2f, future lane dist: %.2f, future "
                              "lat gap: %.2f, signed lateral accel: %.2f.",
                              object_lane_boundary_dist,
                              future_object_lane_boundary_dist,
                              future_abs_lateral_gap,
                              proximity_info.signed_lateral_acceleration()),
              pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
              *cautious_driving_agent_debug);
      debug_for_scene.set_dynamic_proximity_scene(
          pb::CautiousDrivingDebugForScene::WILL_NOT_CLOSE_LANE_BOUNDARY);
    }
    return false;
  }

  // 1.b The desired speed limit depends on the distance.
  // TODO(junyingshen): Fine tune these parameters, consider use some speed
  // relative to agent speed rather than the constant value as the speed lower
  // bound.
  const pb::LongitudinalRange& proximity_ra_arclength =
      proximity_info.projected_ra_arc_length();
  const double road_speed_limit =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(proximity_ra_arclength);
  ClampedLinearSpeedCurveConfig speed_curve_config{};

  if (reasoning_object.IsLargeVehicle()) {
    speed_curve_config.max_slow_down_lat_gap_to_lane_boundary =
        kMaxFutureDistToLaneBoundaryForLargeVehicleDynamicProximityInMeter;
    speed_curve_config.min_slow_down_lat_gap_to_lane_boundary =
        kMinFutureDistToLaneBoundaryForLargeVehicleDynamicProximityInMeter;

    speed_curve_config.speed_lower_bound =
        std::max(proximity_info.signed_longitudinal_speed() -
                     kMaxSpeedLessThanLargeVehicleForDynamicProximityInMps,
                 kMinRemainingSpeedRatioForDynamicProximity * ego_speed);
    // Agent speed could be faster than ego, to ensure it won't trigger DCHECK
    // failure in ComputeDesiredSpeedLimit(), we take the maximum of ego and
    // agent speeds.
    speed_curve_config.speed_upper_bound =
        std::max(proximity_info.signed_longitudinal_speed(), ego_speed);
  } else {
    speed_curve_config.max_slow_down_lat_gap_to_lane_boundary =
        config.dynamic_proximity_agent()
            .lateral_gap_close_to_lane_boundary_no_slow_down_in_meter();
    speed_curve_config.min_slow_down_lat_gap_to_lane_boundary = 0.;
    speed_curve_config.speed_lower_bound = std::min(
        road_speed_limit,
        std::max(proximity_info.signed_longitudinal_speed() -
                     kMaxSpeedLessThanLargeVehicleForDynamicProximityInMps,
                 kMinRemainingSpeedRatioForDynamicProximity * ego_speed));
    speed_curve_config.speed_upper_bound = road_speed_limit;
  }
  const double desired_speed_limit_at_zero_discomfort =
      ComputeDesiredSpeedLimit(
          speed_curve_config,
          reasoning_object.IsLargeVehicle()
              ? future_object_lane_boundary_dist
              : std::max(0., future_object_lane_boundary_dist));

  // 2. Fake one cut-in predicted trajectory and add proximity speed constraint.
  const prediction::pb::PredictedTrajectory predicted_trajectory_proto =
      FakeCutInPredictedTrajectoryForProximityAgent(
          trajectory_info, reasoning_object, &debug_string);
  const DiscomfortVarying& agent_required_lateral_gap =
      reasoning_object.required_lateral_gap();
  double comfort_required_lateral_gap =
      agent_required_lateral_gap(Discomforts::kMin);
  // TODO(waylon): Add constraint states directly from fake BP when speed solver
  // is prepared.
  OverlapComputer overlap_computer(
      trajectory_info.unextended_path_for_overlap(),
      world_context.ego_pose_timestamp());
  overlap_computer.PreComputePaddedPathData(comfort_required_lateral_gap);
  std::vector<speed::pb::OverlapRegion> fake_overlap_regions =
      overlap_computer.ComputeOverlapRegionV2(
          PredictedTrajectoryWrapper(
              reasoning_object.planner_object().tracked_object(),
              predicted_trajectory_proto,
              /*is_primary_trajectory=*/false),
          comfort_required_lateral_gap);
  if (fake_overlap_regions.empty()) {
    LOG(ERROR) << " object id is " << reasoning_object.id() << " "
               << boost::geometry::wkt(
                      trajectory_info.path().polyline_curve().polyline())
               << "/n" << debug_string;
    return false;
  }

  const speed::pb::OverlapRegion& first_overlap_region =
      fake_overlap_regions.front();
  // Add proximity speed constraint for the fake overlap region.
  AddProximitySpeedConstraintForOverlapRegion(
      reasoning_object, first_overlap_region, pb::ReasonerId::CAUTIOUS_DRIVING,
      comfort_required_lateral_gap, desired_speed_limit_at_zero_discomfort,
      /*min_req_discomfort=*/0.0, constraint_creator_ptr);
  rt_event::PostRtEvent<rt_event::planner::DynamicProximityScene>(
      std::to_string(reasoning_object.id()));

  if (cautious_driving_agent_debug != nullptr) {
    absl::StrAppendFormat(
        &debug_string,
        "Curr dist: %.2f, future lane boundary dist: %.2f, future gap: %.2f",
        object_lane_boundary_dist, future_object_lane_boundary_dist,
        future_abs_lateral_gap);
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/
            std::move(debug_string),
            pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
            *cautious_driving_agent_debug);
    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        speed_curve_config.speed_lower_bound,
        speed_curve_config.speed_upper_bound,
        GetPaddedOverlapStart(first_overlap_region),
        GetPaddedOverlapEnd(first_overlap_region),
        config.min_acceleration_for_risk(),
        /*is_reference_profile_updated=*/false,
        *(debug_for_scene.mutable_limiter_parameters()));
  }
  return true;
}

bool CautiousDrivingReasoner::AddConstAccelSoftConstraintForRiskyCutInAgent(
    const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  std::string* debug_str = nullptr;
  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            "[Risky Cut-in] ",
            pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
            *cautious_driving_agent_debug);
    debug_for_scene.set_dynamic_proximity_scene(
        pb::CautiousDrivingDebugForScene::RISKY_CUT_IN);
    debug_str = debug_for_scene.mutable_debug_str();
  }

  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  if (trajectory_info.IsGoingStraight() &&
      IsRiskyCutInAgent(reasoning_object, prediction_decisions, debug_str)) {
    // Here we use const accel and motion heading model to add the constraint.
    // The constraint start at the position when the lateral gap is
    // kRequiredLateralGap.
    constexpr double kRequiredLateralGap = 2.0;
    constexpr double kMinSpeed = math::KmphToMps(20.0);
    const double ra_start_pos =
        object_proximity_info.projected_ra_arc_length().start() +
        std::max(object_proximity_info.abs_lateral_gap() - kRequiredLateralGap,
                 0.0) *
            math::FastCos(
                std::abs(object_proximity_info.relative_motion_heading())) /
            math::FastSin(
                std::abs(object_proximity_info.relative_motion_heading()));
    const double start_time =
        std::max(object_proximity_info.abs_lateral_gap() - kRequiredLateralGap,
                 0.0) /
        std::abs(object_proximity_info.signed_lateral_speed());

    const std::string constraint_id =
        absl::StrCat("obj-", reasoning_object.id(), "-risky-cut-in-soft");
    ConstraintMutator mutator =
        constraint_creator_ptr->AddSpeedConstraintFromConstAccel(
            /*start_time=*/start_time, ra_start_pos,
            /*max_longitudinal_movement=*/
            std::numeric_limits<double>::infinity(),
            /*initial_longitudinal_speed=*/
            object_proximity_info.signed_longitudinal_speed(),
            /*final_longitudinal_speed=*/0.0, constants::PlannerDt(),
            constants::PlannerHorizonStateNum(), reasoning_object.length(),
            /*acceleration=*/
            object_proximity_info.signed_longitudinal_acceleration(),
            pb::FenceType::kLead, id(), constraint_id, reasoning_object.id());
    // For the risky cut in agent, we will brake harder if it is extremely
    // risky.
    constexpr double kMinAccelForExtremelyRiskyAgentInMpss = -3.0;
    constexpr double kLateralSpeedToBrakeHarderInMps = 1.8;
    constexpr double kLateralGapToBrakeHarderInMeters = 2.0;
    const double min_accel_for_soft_cut_in =
        IsHighRiskFastAndCloseAgent(object_proximity_info,
                                    kLateralSpeedToBrakeHarderInMps,
                                    kLateralGapToBrakeHarderInMeters)
            ? kMinAccelForExtremelyRiskyAgentInMpss
            : config.min_acceleration_for_risk();
    mutator.set_soft_yield_with_min_speed_accel(kMinSpeed,
                                                min_accel_for_soft_cut_in);
    // This constraint is only for cautious. Set soft-pass to avoid it being
    // considered in SCR.
    mutator.set_soft_pass();
    mutator.set_yield_headway(DiscomfortVarying(0.0));
    mutator.set_yield_stop_range(DiscomfortVarying(0.0));
    rt_event::PostRtEvent<rt_event::planner::ConstSpeedSoftCutIn>(
        std::to_string(reasoning_object.id()));

    return true;
  }

  return false;
}

bool CautiousDrivingReasoner::AddConstAccelSoftConstraintForSqueezingAgent(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const pb::CautiousDrivingReasonerConfig& config,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  if (!FLAGS_planning_enable_tracking_based_cautious_driving_to_avoid_squeeze) {
    return false;
  }
  std::string* debug_str = nullptr;
  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            "[Risky-Squeeze] ",
            pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
            *cautious_driving_agent_debug);
    debug_for_scene.set_dynamic_proximity_scene(
        pb::CautiousDrivingDebugForScene::AVOID_SQUEEZE);
    debug_str = debug_for_scene.mutable_debug_str();
  }

  const auto [is_risky_squeezing_agent, rel_heading] =
      ComputeRiskySqueezingAgentIdentificationResult(
          world_context, reasoning_object, trajectory_info, debug_str);
  if (!is_risky_squeezing_agent) {
    absl::StrAppend(debug_str, "Not risky squeezing; ");
    return false;
  }
  const double cotan_rel_heading = math::FastCos(std::abs(rel_heading)) /
                                   math::FastSin(std::abs(rel_heading));
  const double rel_lat_speed =
      math::FastSin(rel_heading) * reasoning_object.speed();
  if (math::NearZero(rel_lat_speed, 1e-3) || std::isnan(cotan_rel_heading)) {
    absl::StrAppend(debug_str, "NAN; ");
    return false;
  }
  const pb::ObjectProximityInfo& proximity =
      reasoning_object.object_proximity_info();
  // Adds a mild soft constrint with -2.0 m/ss min_a for an estimated agent
  // squeeze trajectory that approaches ego path with lat gap less than 0.6 m.
  // Ego will make as much effort as possible up to -2.0 m/ss to yield to the
  // agent rear bumper.
  {
    const double ra_start_pos =
        proximity.projected_ra_arc_length().start() +
        std::max(proximity.abs_lateral_gap() -
                     kRequiredLateralGapForMildAvoidSqueezeInMeter,
                 0.0) *
            cotan_rel_heading;
    const double start_time =
        std::max(proximity.abs_lateral_gap() -
                     kRequiredLateralGapForMildAvoidSqueezeInMeter,
                 0.0) /
        std::abs(rel_lat_speed);
    if (start_time < constants::kTrajectoryHorizonInSec) {
      const std::string mild_constraint_id = absl::StrCat(
          "obj-", reasoning_object.id(), "-crossing-lane-squeeze-mild-soft");
      ConstraintMutator mild_mutator =
          constraint_creator_ptr->AddSpeedConstraintFromConstAccel(
              /*start_time=*/start_time, ra_start_pos,
              /*max_longitudinal_movement=*/
              std::numeric_limits<double>::infinity(),
              /*initial_longitudinal_speed=*/
              proximity.signed_longitudinal_speed(),
              /*final_longitudinal_speed=*/0.0, constants::PlannerDt(),
              /*num_states=*/
              static_cast<int>(kTimeHorizonForEstimatingAgentMotionInSec /
                               constants::PlannerDt()),
              reasoning_object.length(),
              /*acceleration=*/
              proximity.signed_longitudinal_acceleration(),
              pb::FenceType::kTrackingBasedCautious, id(), mild_constraint_id,
              reasoning_object.id());
      mild_mutator.set_soft_yield_with_min_a(
          config.min_acceleration_for_risk());
      mild_mutator.set_soft_pass();
      mild_mutator.set_yield_headway(DiscomfortVarying(0.0));
      mild_mutator.set_yield_stop_range(DiscomfortVarying(0.0));
    }
  }
  // Adds a mild soft constrint with -4.0 m/ss min_a for an estimated agent
  // squeeze trajectory that approaches ego path with lat gap less than 0.2 m.
  {
    const double ra_start_pos =
        proximity.projected_ra_arc_length().start() +
        std::max(proximity.abs_lateral_gap() -
                     kRequiredLateralGapForHarshAvoidSqueezeInMeter,
                 0.0) *
            cotan_rel_heading;
    const double start_time =
        std::max(proximity.abs_lateral_gap() -
                     kRequiredLateralGapForHarshAvoidSqueezeInMeter,
                 0.0) /
        std::abs(rel_lat_speed);
    if (start_time < constants::kTrajectoryHorizonInSec) {
      const std::string harsh_constraint_id = absl::StrCat(
          "obj-", reasoning_object.id(), "-crossing-lane-squeeze-harsh-soft");
      ConstraintMutator harsh_mutator =
          constraint_creator_ptr->AddSpeedConstraintFromConstAccel(
              /*start_time=*/start_time, ra_start_pos,
              /*max_longitudinal_movement=*/
              std::numeric_limits<double>::infinity(),
              /*initial_longitudinal_speed=*/
              proximity.signed_longitudinal_speed(),
              /*final_longitudinal_speed=*/0.0, constants::PlannerDt(),
              /*num_states=*/
              static_cast<int>(kTimeHorizonForEstimatingAgentMotionInSec /
                               constants::PlannerDt()),
              reasoning_object.length(),
              /*acceleration=*/
              proximity.signed_longitudinal_acceleration(),
              pb::FenceType::kTrackingBasedCautious, id(), harsh_constraint_id,
              reasoning_object.id());
      harsh_mutator.set_soft_yield_with_min_a(
          config.min_acceleration_for_safety());
      harsh_mutator.set_soft_pass();
      harsh_mutator.set_yield_headway(DiscomfortVarying(0.0));
      harsh_mutator.set_yield_stop_range(DiscomfortVarying(0.0));
      // Because the start position (i.e., the yield side) of each state in the
      // constraint is computed by the agent proximity start pos, it represents
      // the agent rear bumper. However, we do not want to yield to the rear
      // bumper with the effort up to -4.0 m/ss. To balance safety and comfort,
      // we can apply a negative yield buffer to just make ego yield an extra
      // distance w.r.t. the agent front bumper.
      const double yield_buffer = proximity.projected_ra_arc_length().start() -
                                  proximity.projected_ra_arc_length().end() +
                                  kExtraYieldDistanceFromAgentHeadInMeter;
      harsh_mutator.set_yield_extra_distance(DiscomfortVarying(yield_buffer),
                                             /*allow_negative_value=*/true);
    }
  }
  return true;
}

bool CautiousDrivingReasoner::AddReferenceLimitForApproachingVRU(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object, bool has_made_prediction_decision,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  bool reference_limit_added = false;

  reference_limit_added |= AddReferenceLimitToNonReactiveVRU(
      world_context, trajectory_info, reasoning_object, config,
      reference_limiter_ptr, cautious_driving_agent_debug);

  if (!IsVruApproachingEgoPathWithoutOverlappingPredictedTrajectory(
          reasoning_object, has_made_prediction_decision,
          world_context.ra_to_leading_bumper(),
          config.dynamic_proximity_agent())) {
    return reference_limit_added;
  }

  const pb::ObjectProximityInfo& proximity_info =
      reasoning_object.object_proximity_info();
  const pb::LongitudinalRange& proximity_ra_arclength =
      proximity_info.projected_ra_arc_length();
  // The agent's projection on the path exceeds the range of the path, so it is
  // far away Ego current position and we can ignore it safely. Meanwhile, we do
  // not have enough information to compute the required parameters.
  if (proximity_info.out_of_path_range() ||
      !math::IsValidRange(math::Range(proximity_ra_arclength.start(),
                                      proximity_ra_arclength.end()))) {
    return reference_limit_added;
  }

  const math::Range1d speed_limit_range =
      ComputeDynamicProximitySpeedLimitRange(reasoning_object, trajectory_info,
                                             config.dynamic_proximity_agent());

  const math::Range1d speed_zone_ra_arclength_range = {
      proximity_ra_arclength.start(), proximity_ra_arclength.end()};
  reference_limit_added = reference_limiter_ptr->AddReferenceLimit(
      speed_limit_range, speed_zone_ra_arclength_range,
      config.min_acceleration_for_risk(), reasoning_object.id());

  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/
            "DYNAMIC_PROXIMITY: VRU approaching Ego path, no overlaps.",
            pb::CautiousDrivingDebugForScene::DYNAMIC_PROXIMITY,
            *cautious_driving_agent_debug);
    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        speed_limit_range.start_pos, speed_limit_range.end_pos,
        proximity_ra_arclength.start(), proximity_ra_arclength.end(),
        config.min_acceleration_for_risk(), reference_limit_added,
        *(debug_for_scene.mutable_limiter_parameters()));
  }
  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForDynamicProximity(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const std::vector<PredictionDecision>& prediction_decisions,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  const bool has_made_prediction_decision = !prediction_decisions.empty();
  bool reference_limit_added = false;

  reference_limit_added |= AddReferenceLimitForApproachingVRU(
      world_context, trajectory_info, reasoning_object,
      has_made_prediction_decision, config, reference_limiter_ptr,
      cautious_driving_agent_debug);
  reference_limit_added |= AddReferenceLimitForAgentCloseToLaneBoundary(
      world_context, trajectory_info, reasoning_object,
      has_made_prediction_decision, world_context.ego_speed(), config,
      constraint_creator_ptr, cautious_driving_agent_debug);
  reference_limit_added |= AddConstAccelSoftConstraintForRiskyCutInAgent(
      trajectory_info, reasoning_object, prediction_decisions, config,
      constraint_creator_ptr, cautious_driving_agent_debug);
  reference_limit_added |= AddConstAccelSoftConstraintForSqueezingAgent(
      world_context, trajectory_info, reasoning_object, config,
      constraint_creator_ptr, cautious_driving_agent_debug);

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForPedestrianAtHighAgentDensity(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  bool reference_limit_added = false;

  if (!world_context.is_high_agent_density()) {
    return reference_limit_added;
  }

  std::vector<RangeWithId> speed_zone_ra_arclength_range_with_ids;
  for (const PredictionDecisionMakerOutput& output :
       wrapped_prediction_decisions) {
    const ReasoningObject& reasoning_object = output.reasoning_object();
    if (!ShouldConsiderAgentAtHighAgentDensity(reasoning_object, config)) {
      continue;
    }

    const pb::ObjectProximityInfo& proximity_info =
        reasoning_object.object_proximity_info();
    const pb::LongitudinalRange& proximity_ra_arclength =
        proximity_info.projected_ra_arc_length();

    // If the front bumper already passes the object, do not slow down anymore.
    if (proximity_ra_arclength.end() < world_context.ra_to_leading_bumper()) {
      continue;
    }

    const math::Range1d range{proximity_ra_arclength.start(),
                              proximity_ra_arclength.end()};
    speed_zone_ra_arclength_range_with_ids.push_back(
        {range, reasoning_object.id()});
  }

  if (speed_zone_ra_arclength_range_with_ids.empty()) {
    return reference_limit_added;
  }

  // Merge the overlapping proximity arclength ranges into disjoint ranges.
  const std::vector<RangeWithId> merged_speed_zone_ra_arclength_range_with_ids =
      MergeRangeWithID(speed_zone_ra_arclength_range_with_ids);

  // The speed limit is not discomfort varying.
  const math::Range1d speed_limit_range = {
      config.max_speed_for_ped_at_high_agent_density_in_mps(),
      config.max_speed_for_ped_at_high_agent_density_in_mps()};

  for (const RangeWithId& range_with_id :
       merged_speed_zone_ra_arclength_range_with_ids) {
    const math::Range1d& speed_zone_ra_arclength_range = range_with_id.range;
    const ObjectId& object_id = range_with_id.id;
    reference_limit_added |= reference_limiter_ptr->AddReferenceLimit(
        speed_limit_range, speed_zone_ra_arclength_range,
        config.min_acceleration_for_risk(), object_id);

    if (cautious_driving_reasoner_debug != nullptr) {
      pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug =
          cautious_driving_reasoner_debug->add_agents();
      cautious_driving_agent_debug->set_object_id(object_id);
      pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              /*debug_string=*/
              "HIGH_AGENT_DENSITY",
              pb::CautiousDrivingDebugForScene::HIGH_AGENT_DENSITY,
              *cautious_driving_agent_debug);
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          speed_limit_range.start_pos, speed_limit_range.end_pos,
          speed_zone_ra_arclength_range.start_pos,
          speed_zone_ra_arclength_range.end_pos,
          config.min_acceleration_for_risk(), reference_limit_added,
          *(debug_for_scene.mutable_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForJunctions(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    const pb::TargetSpeedLimitPatch& speed_limit_patch,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  bool reference_limit_added = false;
  for (const traffic_rules::JunctionInLaneSequence& junction :
       trajectory_info.traffic_rules().lane_sequence_junctions) {
    const math::Range1d& ra_arclength_range_m = junction.ra_arclength_range_m;
    if (ra_arclength_range_m.end_pos < 0) {
      // we don't need to consider the junctions behind us.
      continue;
    }
    if (ra_arclength_range_m.start_pos >= ra_arclength_range_m.end_pos) {
      // per our observation, sometimes the start & end of the range are not
      // sorted, the reason is that ego is making an uturn. As ego has kinematic
      // limits during uturn so we can drop the junction speed limits.
      if (junction.ego_lane_in_junction_ptr == nullptr ||
          junction.ego_lane_in_junction_ptr->turn() != hdmap::Lane::U_TURN) {
        rt_event::PostRtEvent<
            rt_event::planner::JunctionArcLengthRangeUnsorted>();
      }
      continue;
    }

    constexpr double kMaxDistToJunctionForKeepingCurrentSpeedInMeter = 15;
    const double junction_lane_center_arclength =
        (ra_arclength_range_m.start_pos + ra_arclength_range_m.end_pos) * 0.5;
    if (trajectory_info.IsGoingStraight(junction_lane_center_arclength) &&
        ra_arclength_range_m.start_pos <
            kMaxDistToJunctionForKeepingCurrentSpeedInMeter &&
        ra_arclength_range_m.end_pos > 0) {
      // If ego is near the junction, Ego will prioritize maintaining its
      // current speed while passing through the junction.
      std::vector<hdmap::Limit> lane_speed_limits{
          junction.ego_lane_in_junction_ptr->speed_limits().begin(),
          junction.ego_lane_in_junction_ptr->speed_limits().end()};

      traffic_rules::UpdateLaneSpeedLimitsFromPatchedSpeedLimits(
          junction.ego_lane_in_junction_ptr, speed_limit_patch,
          world_context.pnc_map_service()->map_region(), &lane_speed_limits);
      const double road_max_speed =
          std::max_element(lane_speed_limits.begin(), lane_speed_limits.end(),
                           [](const hdmap::Limit lhs, const hdmap::Limit rhs) {
                             return lhs.limit_max() < rhs.limit_max();
                           })
              ->limit_max();

      // Check whether junction lane is controlled by traffic light.
      const auto matched_traffic_light = std::find_if(
          trajectory_info.traffic_rules().traffic_lights.begin(),
          trajectory_info.traffic_rules().traffic_lights.end(),
          [junction_lane_id = junction.ego_lane_in_junction_ptr->id()](
              const traffic_rules::TrafficLightInfo& traffic_light_info) {
            return traffic_light_info.controlled_lane().id() ==
                   junction_lane_id;
          });
      const bool is_controlled_by_traffic_light =
          matched_traffic_light !=
              trajectory_info.traffic_rules().traffic_lights.end() &&
          !matched_traffic_light->is_broken() &&
          !matched_traffic_light->is_unreliable_broken();

      // These param are suggested by product team, reference:
      // https://cooper.didichuxing.com/docs2/document/2203628478064
      constexpr double
          kMinSpeedLimitForEgoWhenPassingJunctionWithoutTrafficLight = 30.0;
      constexpr double kMinSpeedLimitForEgoWhenPassingJunctionWithTrafficLight =
          35.0;
      double min_speed_limit_for_ego_when_passing_junction =
          kMinSpeedLimitForEgoWhenPassingJunctionWithoutTrafficLight;
      if (is_controlled_by_traffic_light) {
        min_speed_limit_for_ego_when_passing_junction =
            kMinSpeedLimitForEgoWhenPassingJunctionWithTrafficLight;
      }

      const double ego_comfort_speed_limit = std::min(
          road_max_speed,
          std::max(
              world_context.ego_speed(),
              math::KmphToMps(min_speed_limit_for_ego_when_passing_junction)));
      reference_limit_added |= reference_limiter_ptr->AddReferenceLimit(
          {ego_comfort_speed_limit, road_max_speed}, ra_arclength_range_m,
          config.min_acceleration_for_comfort(),
          /*object_id=*/-junction.ego_lane_in_junction_ptr->id());
      if (reference_limit_added) {
        rt_event::PostRtEvent<
            rt_event::planner::JunctionCautiousWithEgoSpeed>();
      }

      // Populate the debug info.
      if (cautious_driving_reasoner_debug != nullptr) {
        pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug =
            cautious_driving_reasoner_debug->add_agents();
        cautious_driving_agent_debug->set_object_id(
            -junction.ego_lane_in_junction_ptr->id());
        pb::CautiousDrivingDebugForScene& debug_for_scene =
            AppendCautiousDrivingDebugForScene(
                /*debug_string=*/
                "CONST_SPEED_PASSING_JUNCTION",
                pb::CautiousDrivingDebugForScene::CONST_SPEED_PASSING_JUNCTION,
                *cautious_driving_agent_debug);
        reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
            ego_comfort_speed_limit, road_max_speed,
            ra_arclength_range_m.start_pos, ra_arclength_range_m.end_pos,
            config.min_acceleration_for_comfort(), reference_limit_added,
            *(debug_for_scene.mutable_limiter_parameters()));
      }
    }

    if (ra_arclength_range_m.start_pos < ra_arclength_range_m.end_pos) {
      reference_limit_added |= reference_limiter_ptr->AddReferenceLimit(
          {config.max_speed_for_junctions_in_mps(),
           config.max_speed_for_junctions_in_mps()},
          ra_arclength_range_m, config.min_acceleration_for_comfort(),
          /*object_id=*/-1);
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForVRUAtCrosswalk(
    const WorldContext& world_context,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* debug_ptr) {
  bool reference_limit_added = false;

  reference_limit_added |= AddReferenceLimitForProximityVRUAtCrosswalk(
      world_context, wrapped_prediction_decisions, trajectory_info,
      reference_limiter_ptr, debug_ptr);

  reference_limit_added |= AddReferenceLimitForStartingToMoveCyclistAtCrosswalk(
      wrapped_prediction_decisions, trajectory_info, config,
      world_context.ra_to_leading_bumper(), reference_limiter_ptr, debug_ptr);

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForTrafficLight(
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* debug_ptr) {
  bool reference_limit_added = false;
  const pb::TrafficLightCautiousConfig& tl_cautious_config =
      config.traffic_light_cautious();
  for (const traffic_rules::TrafficLightInfo& upcoming_tl_info :
       trajectory_info.traffic_rules().traffic_lights) {
    if (upcoming_tl_info.state() !=
            planner::pb::TrafficLightState::kYellowFlashingLight &&
        !upcoming_tl_info.is_broken()) {
      // Only add reference speed limit for yellow flashing traffic light and
      // broken traffic light.
      continue;
    }

    const int64_t signal_id = upcoming_tl_info.associate_traffic_light().id();
    const math::Range1d& controlled_lane_ra_arclength_range =
        upcoming_tl_info.controlled_lane_ra_arclength_range();
    const double ego_max_speed = trajectory_info.GetEgoMaxSpeedInArclengthRange(
        controlled_lane_ra_arclength_range);

    // Loop over the PDM outputs to see if we should be more cautious for the
    // traffic light.
    const bool has_interested_agent_for_cautious =
        std::any_of(wrapped_prediction_decisions.begin(),
                    wrapped_prediction_decisions.end(),
                    [&controlled_lane_ra_arclength_range](
                        const PredictionDecisionMakerOutput& output) {
                      return HasAgentPolicyWithinTLControlRange(
                          output, controlled_lane_ra_arclength_range);
                    });

    const double critical_speed_limit =
        ego_max_speed * tl_cautious_config.max_speed_limit_ratio();
    const double comfort_speed_limit =
        has_interested_agent_for_cautious
            ? ego_max_speed * tl_cautious_config.min_speed_limit_ratio()
            : critical_speed_limit;

    const bool is_reference_profile_updated_for_tl =
        reference_limiter_ptr->AddReferenceLimit(
            comfort_speed_limit, critical_speed_limit,
            controlled_lane_ra_arclength_range.start_pos < 0.0
                ? 0.0
                : controlled_lane_ra_arclength_range.start_pos,
            controlled_lane_ra_arclength_range.end_pos,
            config.min_acceleration_for_comfort(), signal_id);

    reference_limit_added |= is_reference_profile_updated_for_tl;

    if (debug_ptr != nullptr) {
      pb::TrafficLightReferenceSpeedLimiterDebug* tl_cautious_debug =
          debug_ptr->add_traffic_lights();
      tl_cautious_debug->set_signal_id(signal_id);
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          comfort_speed_limit, critical_speed_limit,
          std::max(0.0, controlled_lane_ra_arclength_range.start_pos),
          controlled_lane_ra_arclength_range.end_pos,
          config.min_acceleration_for_comfort(),
          is_reference_profile_updated_for_tl,
          *(tl_cautious_debug->mutable_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForExceptionHandler(
    const TrajectoryInfo& trajectory_info,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* debug_ptr) {
  bool reference_limit_added = false;
  const std::vector<
      ::exception_handler::exception_handler_main::DegrationSpeedLimiter>&
      speed_limiter_list = ::exception_handler::exception_handler_main::
                               TrajectoryResultInstance::GetInstance()
                                   ->degradation_handler()
                                   .speed_limiter_list();
  const math::Range1d arclength_range = {
      0.0, trajectory_info.unextended_path_for_overlap().GetTotalArcLength()};

  for (const auto& speed_limiter : speed_limiter_list) {
    reference_limit_added |= reference_limiter_ptr->AddReferenceLimit(
        speed_limiter.speed_range, arclength_range, speed_limiter.min_accel,
        /*object_id=*/-1);
    if (debug_ptr != nullptr) {
      pb::ExceptionhandlerSpeedLimiterDebug*
          exception_handler_speed_limiter_debug =
              debug_ptr->add_exception_handler();
      exception_handler_speed_limiter_debug->set_min_speed(
          speed_limiter.speed_range.start_pos);
      exception_handler_speed_limiter_debug->set_max_speed(
          speed_limiter.speed_range.end_pos);
      exception_handler_speed_limiter_debug->set_min_accl(
          speed_limiter.min_accel);
      exception_handler_speed_limiter_debug->set_degradation_handler_type(
          speed_limiter.degradation_handler_type);
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForSlowCutInAgent(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const AgentPolicy& agent_policy,
    const pb::SlowCutInAgentConfig& slow_cut_in_agent_config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingAgentDebug* agent_debug_ptr) {
  bool reference_limit_added = false;

  const ReasoningObject& reasoning_object = agent_policy.reasoning_object();
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  // If the front bumper already passes the object, do not slow down anymore.
  if (object_proximity_info.projected_ra_arc_length().end() <
      world_context.ra_to_leading_bumper()) {
    return false;
  }

  const double abs_lateral_gap = object_proximity_info.abs_lateral_gap();
  const double min_lateral_gap_no_slow_down_in_meter =
      slow_cut_in_agent_config.lateral_gap_no_slow_down_in_meter();
  const double critical_required_lateral_gap =
      reasoning_object.CriticalRequiredLateralGap();
  // If |abs_lateral_gap| is larger than
  // |min_lateral_gap_no_slow_down_in_meter|, we will NOT add any speed limit to
  // the agent. If |abs_lateral_gap| is smaller than
  // |critical_required_lateral_gap|, we rely on constraints to yield to the
  // agent and will NOT add any speed limit to the agent.
  if (abs_lateral_gap <= critical_required_lateral_gap ||
      abs_lateral_gap > min_lateral_gap_no_slow_down_in_meter) {
    return false;
  }

  // Calculate comfort speed limit based on lateral gap, constrained by
  // piecewise linear function.
  const double ego_max_speed_limit =
      trajectory_info.GetEgoMaxSpeedInArclengthRange(
          object_proximity_info.projected_ra_arc_length());
  const math::PiecewiseLinearFunction comfort_speed_limit_based_on_lat_gap(
      {reasoning_object.CriticalRequiredLateralGap(),
       slow_cut_in_agent_config.medial_lateral_gap(),
       min_lateral_gap_no_slow_down_in_meter},
      {
          slow_cut_in_agent_config
              .min_speed_for_critical_required_lateral_gap_in_mps(),
          std::min(slow_cut_in_agent_config.medial_speed_limit(),
                   ego_max_speed_limit),
          ego_max_speed_limit,
      });
  const double comfort_speed_limit =
      comfort_speed_limit_based_on_lat_gap(abs_lateral_gap);

  pb::CautiousDrivingSpeedLimit cautious_speed_limit;
  cautious_speed_limit.set_min_speed(comfort_speed_limit);
  if (agent_policy.agent_trajectory_info()
          .is_agent_extra_cautious_for_slow_cut_in_in_front()) {
    // TODO(waylon): Suppress acceleration when discomfort increases. Use accel
    // limiter when speed solver has prepared.
    cautious_speed_limit.set_max_speed(comfort_speed_limit);
  } else {
    cautious_speed_limit.set_max_speed(ego_max_speed_limit);
  }

  const math::Range1d speed_zone_ra_arclength_range = {
      std::max(0.0, object_proximity_info.projected_ra_arc_length().start() -
                        world_context.ra_to_leading_bumper()),
      object_proximity_info.projected_ra_arc_length().end() -
          world_context.ra_to_trailing_bumper_shift()};

  reference_limit_added = reference_limiter_ptr->AddReferenceLimit(
      cautious_speed_limit, speed_zone_ra_arclength_range,
      slow_cut_in_agent_config.min_acceleration(), reasoning_object.id());

  if (reference_limit_added) {
    rt_event::PostRtEvent<rt_event::planner::LowSpeedCutInAgentCautious>(

        trajectory_info.unique_path_homotopy_id() + "_" +
        std::to_string(reasoning_object.id()));
  }

  if (agent_debug_ptr != nullptr) {
    std::string debug_string;
    absl::StrAppendFormat(&debug_string, "abs lat gap:%.2f,comfort_speed:%.2f;",
                          abs_lateral_gap, comfort_speed_limit);
    PopulatePredictedTrajectoryDebug(
        agent_policy.agent_trajectory_info().id(),
        {cautious_speed_limit.min_speed(), cautious_speed_limit.max_speed()},
        speed_zone_ra_arclength_range, /*strength=*/1.0,
        slow_cut_in_agent_config.min_acceleration(), reference_limit_added,
        agent_policy.OverlapRegion().region_id(),
        agent_policy.cautious_driving_reason(), std::move(debug_string),
        *agent_debug_ptr);
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddGapAlignForLargeVehicleTurningAlongEgo(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const ReasoningObject& reasoning_object,
    const traffic_rules::LaneInfoInLaneSequence& turning_lane_info,
    const std::vector<PredictionDecision>& prediction_decisions,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const pb::CautiousDrivingReasonerConfig& config,
    const pb::PredictionDecisionMakerConfig& pdm_config,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  bool gap_align_added = false;
  if (!reasoning_object.IsLargeVehicle()) {
    return gap_align_added;
  }

  const std::vector<speed::pb::OverlapRegion>
      overlap_regions_for_turning_large_vehicle_gap_align =
          ComputeOverlapRegionsForTurningLargeVehicleGapAlign(
              world_context, trajectory_info, reasoning_object,
              prediction_decisions, object_prediction_map, config, pdm_config,
              cautious_driving_agent_debug);
  if (overlap_regions_for_turning_large_vehicle_gap_align.empty()) {
    return gap_align_added;
  }

  for (const speed::pb::OverlapRegion& overlap_region :
       overlap_regions_for_turning_large_vehicle_gap_align) {
    gap_align_added |= AddGapAlignConstraintForLargeVehicleTurningOverlap(
        world_context, trajectory_info, reasoning_object, overlap_region,
        turning_lane_info, config, constraint_creator_ptr);
  }
  return gap_align_added;
}

bool CautiousDrivingReasoner::
    AddProximitySpeedConstraintForLargeVehicleCausingOcclusion(
        const WorldContext& world_context,
        const ReasoningObject& reasoning_object,
        const traffic_rules::LaneInfoInLaneSequence& ego_lane_info,
        const LaneSequenceIterator& lane_sequence_iterator,
        const pb::CautiousDrivingReasonerConfig& config,
        ConstraintCreator* constraint_creator_ptr,
        pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  if (!reasoning_object.IsVehicleLikelyToCauseOcclusion()) {
    return false;
  }
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  if (object_proximity_info.abs_lateral_gap() >
          config.max_lateral_gap_to_consider_occlusion_in_meter() ||
      object_proximity_info.abs_lateral_gap() == 0.0 ||
      object_proximity_info.projected_ra_arc_length().end() <
          world_context.ra_to_leading_bumper()) {
    return false;
  }
  // We currently only consider the scenario when ego goes straight.
  if (ego_lane_info.lane_turn_type != hdmap::Lane_Turn_STRAIGHT) {
    return false;
  }

  // We currently only consider the scenario where the agent it is close the the
  // lane of interest and goes through the same section before the lane of
  // interest as ego does (to exclude agents turning from other sections which
  // could be very slow anyway regardless of occlusion, applying occlusion
  // cautious to them may cause unnecessary braking).
  const bool is_agent_close_to_lane =
      reasoning_object.object_proximity_info().projected_ra_arc_length().end() +
          kMaxDistToConsiderAgentCloseToJunctionInMeter >
      ego_lane_info.start_ra_arclength;
  if (!is_agent_close_to_lane ||
      !IsAgentGoingThroughSameSectionBeforeTargetLane(
          world_context, reasoning_object, ego_lane_info.lane,
          lane_sequence_iterator)) {
    return false;
  }

  const std::string constraint_id = absl::StrCat(
      "obj-", reasoning_object.id(), "-lv-occlusion-proximity-speed");
  const double ra_start_pos =
      std::max(0.0, object_proximity_info.projected_ra_arc_length().start());
  // Apply the proximity speed constraint up to the end of the lane.
  const double max_longitudinal_movement =
      std::max(0.0, ego_lane_info.end_ra_arclength - ra_start_pos);

  // As the large vehicle may not have overlap with ego path, to save
  // computation effort, we create the proximity speed constraint from the const
  // acceleration model rather than overlap regions.
  ConstraintMutator mutator =
      constraint_creator_ptr->AddProximitySpeedConstraintFromConstAccel(
          /*start_time=*/0.0, ra_start_pos, max_longitudinal_movement,
          /*initial_longitudinal_speed=*/
          object_proximity_info.signed_longitudinal_speed(),
          /*final_longitudinal_speed=*/0.0, constants::PlannerDt(),
          constants::PlannerHorizonStateNum(), reasoning_object.length(),
          /*acceleration=*/
          object_proximity_info.signed_longitudinal_acceleration(),
          pb::FenceType::kProximity, id(), constraint_id,
          reasoning_object.id());

  mutator.set_proximity_relative_speed_params(
      /*max_lat_gap=*/reasoning_object.ComfortRequiredLateralGap(),
      config.large_vehicle_occlusion_cautious().pass_relative_speed_in_mps(),
      /*min_req_discomfort=*/0.0,
      /*min_a=*/-std::numeric_limits<double>::infinity(),
      /*min_j=*/-std::numeric_limits<double>::infinity(), /*abs_speed_bound=*/
      {config.large_vehicle_occlusion_cautious().min_abs_pass_speed_in_mps(),
       std::numeric_limits<double>::infinity()});

  if (cautious_driving_agent_debug != nullptr) {
    cautious_driving_agent_debug->set_object_id(reasoning_object.id());
    AppendCautiousDrivingDebugForScene(
        /*debug_string=*/"OCCLUSION: LV. ",
        pb::CautiousDrivingDebugForScene::OCCLUSION,
        *cautious_driving_agent_debug);
  }
  return true;
}

bool CautiousDrivingReasoner::AddProximitySpeedConstraintForBusNearBusBulb(
    const ReasoningObject& reasoning_object,
    ConstraintCreator* constraint_creator_ptr,
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();
  constexpr double kLatDistThresholdToAddContraintInMeter = 1.4;
  if (object_proximity_info.abs_lateral_gap() >
          kLatDistThresholdToAddContraintInMeter &&
      math::NearZero(object_proximity_info.signed_lateral_speed())) {
    if (cautious_driving_agent_debug != nullptr) {
      cautious_driving_agent_debug->set_object_id(reasoning_object.id());
      AppendCautiousDrivingDebugForScene(
          /*debug_string=*/"Not risky stationary. ",
          pb::CautiousDrivingDebugForScene::BUS_NEAR_BUS_BULB,
          *cautious_driving_agent_debug);
    }
    return false;
  }
  // As the large vehicle may not have overlap with ego path, to save
  // computation effort, we create the proximity speed constraint from
  // the const acceleration model rather than overlap regions.

  // Estimate when and where to start adding the proximity speed constraint
  // based on the proximity info. If the agent is laterally accelrating up,
  // using const accel model to estimate.
  constexpr double kLatAccelBeingConsideredThresholdInMpss = 0.01;
  const double lat_gap_to_travel = object_proximity_info.abs_lateral_gap() -
                                   kLatDistThresholdToAddContraintInMeter;
  const double start_time =
      lat_gap_to_travel <= 0.0 ? 0.0
      : object_proximity_info.signed_lateral_acceleration() >
              kLatAccelBeingConsideredThresholdInMpss
          ? (std::sqrt(math::Sqr(object_proximity_info.signed_lateral_speed()) +
                       2 * object_proximity_info.signed_lateral_acceleration() *
                           lat_gap_to_travel) -
             object_proximity_info.signed_lateral_speed()) /
                object_proximity_info.signed_lateral_acceleration()
          : lat_gap_to_travel /
                std::abs(object_proximity_info.signed_lateral_speed());
  if (start_time > constants::kTrajectoryHorizonInSec) {
    if (cautious_driving_agent_debug != nullptr) {
      cautious_driving_agent_debug->set_object_id(reasoning_object.id());
      AppendCautiousDrivingDebugForScene(
          /*debug_string=*/"Not risky slow moving. ",
          pb::CautiousDrivingDebugForScene::BUS_NEAR_BUS_BULB,
          *cautious_driving_agent_debug);
    }
    return false;
  }
  const double ra_start_pos =
      std::max(0.0, object_proximity_info.projected_ra_arc_length().start()) +
      start_time * std::abs(object_proximity_info.signed_longitudinal_speed());
  const std::string constraint_id =
      absl::StrCat("obj-", reasoning_object.id(), "-bus-bulb-proximity-speed");
  ConstraintMutator mutator =
      constraint_creator_ptr->AddProximitySpeedConstraintFromConstAccel(
          start_time, ra_start_pos,
          /*max_longitudinal_movement=*/
          std::numeric_limits<double>::infinity(),
          /*initial_longitudinal_speed=*/
          object_proximity_info.signed_longitudinal_speed(),
          /*final_longitudinal_speed=*/0.0, constants::PlannerDt(),
          constants::PlannerHorizonStateNum(), reasoning_object.length(),
          /*acceleration=*/
          object_proximity_info.signed_longitudinal_acceleration(),
          pb::FenceType::kProximity, id(), constraint_id,
          reasoning_object.id());
  // Compute the abs speed bound by linear interpolation based on the
  // agent estimated lat gap at the look ahead time into the future.
  constexpr double kMinSpeedBoundInMps = math::KmphToMps(35.0);
  constexpr double kMaxSpeedBoundInMps = math::KmphToMps(45.0);
  constexpr double kLookAheadHorizonInSec = 3.0;
  const double lat_accel_compensenation_movement =
      object_proximity_info.signed_lateral_acceleration() >
              kLatAccelBeingConsideredThresholdInMpss
          ? 0.5 * kLookAheadHorizonInSec * kLookAheadHorizonInSec *
                object_proximity_info.signed_lateral_acceleration()
          : 0.0;
  const double abs_lat_gap_into_look_ahead_future = std::max(
      0.0, object_proximity_info.abs_lateral_gap() -
               kLookAheadHorizonInSec *
                   std::abs(object_proximity_info.signed_lateral_speed()) -
               lat_accel_compensenation_movement);
  const double max_speed = math::GetLinearInterpolatedY(
      reasoning_object.CriticalRequiredLateralGap(),
      kLatDistThresholdToAddContraintInMeter, kMinSpeedBoundInMps,
      kMaxSpeedBoundInMps, abs_lat_gap_into_look_ahead_future,
      /*allow_extension=*/false);
  constexpr double kMinAInMpss = -1.5;
  mutator.set_proximity_speed_params(
      /*max_lat_gap=*/reasoning_object.ComfortRequiredLateralGap(), max_speed,
      /*min_req_discomfort=*/0.0,
      /*min_a=*/kMinAInMpss);
  if (cautious_driving_agent_debug != nullptr) {
    cautious_driving_agent_debug->set_object_id(reasoning_object.id());
    std::string debug_string;
    absl::StrAppendFormat(&debug_string, "start time:%.2f, start pos:%.2f",
                          start_time, ra_start_pos);
    AppendCautiousDrivingDebugForScene(
        /*debug_string=*/std::move(debug_string),
        pb::CautiousDrivingDebugForScene::BUS_NEAR_BUS_BULB,
        *cautious_driving_agent_debug);
  }
  return true;
}

bool CautiousDrivingReasoner::
    AddReferenceLimitToUnstuckWithLateralGapLessThanCritical(
        const WorldContext& world_context,
        const ReasoningObject& reasoning_object,
        const TrajectoryInfo& trajectory_info,
        const pb::CautiousDrivingReasonerConfig& config,
        CautiousDrivingLimiter* reference_limiter_ptr,
        pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug) {
  DCHECK(reasoning_object.plan_init_pose_overlap_slice_ptr() != nullptr);
  const double curvature =
      trajectory_info.GetEgoCurvature(GetPaddedOverlapStart(
          *reasoning_object.plan_init_pose_overlap_slice_ptr()));
  if (std::abs(reasoning_object.plan_init_pose_overlap_slice_ptr()
                   ->signed_lateral_gap()) >=
      reasoning_object.GetPrincipledCriticalRequiredLateralGap(curvature)) {
    return false;
  }
  const pb::ObjectProximityInfo& object_proximity_info =
      reasoning_object.object_proximity_info();

  pb::CautiousDrivingSpeedLimit speed_limit;
  math::Range1d speed_zone_ra_arclength_range;

  speed_limit.set_min_speed(
      kEgoSpeedLimitForCreepingUnderCriticalLateralGapInMps);
  speed_limit.set_max_speed(
      kEgoSpeedLimitForCreepingUnderCriticalLateralGapInMps);
  speed_zone_ra_arclength_range.start_pos =
      std::max(0.0, object_proximity_info.projected_ra_arc_length().start() -
                        world_context.ra_to_leading_bumper());
  speed_zone_ra_arclength_range.end_pos =
      std::max(object_proximity_info.projected_ra_arc_length().end(),
               speed_zone_ra_arclength_range.start_pos +
                   world_context.robot_state().GetLength());
  const double min_acc = config.min_acceleration_for_risk();
  const bool is_reference_profile_updated =
      reference_limiter_ptr->AddReferenceLimit(speed_limit,
                                               speed_zone_ra_arclength_range,
                                               min_acc, reasoning_object.id());
  // Populate the debug info.
  if (cautious_driving_agent_debug != nullptr) {
    pb::CautiousDrivingDebugForScene& debug_for_scene =
        AppendCautiousDrivingDebugForScene(
            /*debug_string=*/"",
            pb::CautiousDrivingDebugForScene::
                UNSTUCK_WITH_LAT_GAP_LESS_THAN_CRITICAL,
            *cautious_driving_agent_debug);

    reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
        speed_limit.min_speed(), speed_limit.max_speed(),
        speed_zone_ra_arclength_range.start_pos,
        speed_zone_ra_arclength_range.end_pos, min_acc,
        is_reference_profile_updated,
        *(debug_for_scene.mutable_limiter_parameters()));
  }
  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitForNarrowPassage(
    const WorldContext& world_context,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  bool is_reference_profile_updated = false;
  pb::ScenarioBasedCautiousDrivingDebug* scenario_debug =
      cautious_driving_reasoner_debug != nullptr
          ? cautious_driving_reasoner_debug->add_scenarios()
          : nullptr;
  for (size_t index = 0UL; index < world_context.narrow_passage_infos().size();
       ++index) {
    const pb::NarrowPassageInfo& narrow_passage_info =
        world_context.narrow_passage_infos()[index];
    // Do not apply speed limit if the clearance is greater than 0.8m;
    if (narrow_passage_info.min_bilateral_clearance_sum() >
        kIntermediateNarrowPassageClearanceThresholdInMeter) {
      continue;
    }
    const double speed_limit = math::GetLinearInterpolatedY(
        /*x1=*/kExtremeNarrowPassageClearanceThresholdInMeter,
        /*x2=*/kIntermediateNarrowPassageClearanceThresholdInMeter,
        /*y1=*/kEgoSpeedLimitInExtremeNarrowPassageInMps,
        /*y2=*/kEgoSpeedLimitInIntermediateNarrowPassageInMps,
        /*x_eval=*/
        narrow_passage_info.min_bilateral_clearance_sum());
    const double start_pos =
        std::max(0.0, narrow_passage_info.ego_moving_distance_range().start());
    const double end_pos =
        std::max(narrow_passage_info.ego_moving_distance_range().end(),
                 start_pos + world_context.robot_state().GetLength());

    const double min_acc = config.min_acceleration_for_risk();
    is_reference_profile_updated =
        reference_limiter_ptr->AddScenarioReferenceLimit(
            speed_limit, speed_limit, start_pos, end_pos, min_acc,
            speed::pb::CautiousSpeedLimiterScenarioType::NARROW_PASSAGE);
    // Populate the debug info.
    if (scenario_debug != nullptr) {
      scenario_debug->set_scenario_type(
          pb::ScenarioBasedCautiousDrivingDebug::NARROW_PASSAGE);
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          speed_limit, speed_limit, start_pos, end_pos, min_acc,
          is_reference_profile_updated,
          *(scenario_debug->add_limiter_parameters()));
    }
  }
  return is_reference_profile_updated;
}

bool CautiousDrivingReasoner::AddReferenceLimitForExitZoneNearby(
    const TrajectoryInfo& trajectory_info,
    const pb::CautiousDrivingReasonerConfig& config,
    CautiousDrivingLimiter* reference_limiter_ptr,
    pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug) {
  bool reference_limit_added = false;

  // TODO(junying, waylon): In Hdmap, the multi-layer areas in expressway are
  // temporarily expressed as road exit zones, making them indistinguishable
  // from regular road exit zones. For any expressway exit zones, we will return
  // directly.
  if (trajectory_info.IsOnExpressway()) {
    return reference_limit_added;
  }

  if (trajectory_info.IsReverseDriving()) {
    // We use the min(road_speed_limit, max_speed_limit_for_exit_zone) as the
    // speed limit. We compute the road_speed_limit from traffic_rules, but
    // traffic_rules only have a default 1.39m/s(5km/h) for reversing, see
    // GetReverseDrivingSpeedLimits for more details. So the road_speed_limits
    // from traffic_rules when reversing is not the real road speed limits.
    // Since we won't go fast while reversing, there is no need to add an
    // exit-zone-road speed limit.
    return false;
  }
  // Check if the lane sequence passes by a road exit zone.
  auto lane_iter = std::find_if(
      trajectory_info.lane_sequence_iterator().current_lane(),
      trajectory_info.lane_sequence_iterator().end(), [](auto& lane_iter) {
        return lane_iter->IsRightmostVehicleLane() &&
               std::any_of(lane_iter->zones().begin(), lane_iter->zones().end(),
                           [](auto& zone) {
                             return zone.zone->type() == hdmap::Zone::ROAD_EXIT;
                           });
      });
  // If we could not find a lane which associates a road exit zone, it means
  // the lane sequence does not pass by a road exit zone. We would early return
  // in such case.
  if (lane_iter == trajectory_info.lane_sequence_iterator().end()) {
    return false;
  }

  // Get the first road exit zone ahead of ego's current position.
  const traffic_rules::ZoneInLaneSequence* first_exit_zone =
      traffic_rules::GetFirstRoadExitZoneAhead(
          trajectory_info.traffic_rules().zones);
  if (first_exit_zone != nullptr) {
    // As designed we want to complete the deceleration some distance before
    // reaching the target exit zone. For example, the distance can be 10 m.
    const math::Range1d arclength_range(
        {first_exit_zone->start_ra_arclength -
             config.cautious_for_exit_zone_nearby_arclength_offset(),
         first_exit_zone->start_ra_arclength});
    const double road_speed_limit =
        trajectory_info.GetEgoMaxSpeedInArclengthRange(arclength_range);
    const double min_speed_limit = std::min(
        config.min_speed_for_exit_zone_nearby_in_mps(), road_speed_limit);
    reference_limit_added |= reference_limiter_ptr->AddReferenceLimit(
        {min_speed_limit, road_speed_limit}, arclength_range,
        config.min_acceleration_for_comfort(),
        /*object_id=*/-first_exit_zone->zone_ptr->id());

    // Populate the debug info.
    if (cautious_driving_reasoner_debug != nullptr) {
      pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug =
          cautious_driving_reasoner_debug->add_agents();
      cautious_driving_agent_debug->set_object_id(
          -first_exit_zone->zone_ptr->id());
      pb::CautiousDrivingDebugForScene& debug_for_scene =
          AppendCautiousDrivingDebugForScene(
              /*debug_string=*/
              "EXIT_ZONE_NEARBY",
              pb::CautiousDrivingDebugForScene::EXIT_ZONE_NEARBY,
              *cautious_driving_agent_debug);
      reasoning_util::PopulateCautiousDrivingLimiterParametersDebug(
          min_speed_limit, road_speed_limit, arclength_range.start_pos,
          arclength_range.end_pos, config.min_acceleration_for_comfort(),
          reference_limit_added,
          *(debug_for_scene.mutable_limiter_parameters()));
    }
  }

  return reference_limit_added;
}

bool CautiousDrivingReasoner::AddReferenceLimitForRemoteSpeedLimiter(
    const WorldContext& world_context,
    ConstraintCreator* constraint_creator_ptr) {
  if (!world_context.remote_speed_limit().has_value()) {
    return false;
  }

  ConstraintMutator mutator =
      DCHECK_NOTNULL(constraint_creator_ptr)
          ->AddStopPoint(
              /*start_time=*/0,
              /*end_time=*/constants::kTrajectoryHorizonInSec,
              /*ra_stop_x=*/0.0, pb::FenceType::kRemoteSpeedLimit, id(),
              "REMOTE_SPEED_LIMITER");

  const double accel_from_remote_speed_limit =
      math::NearZero(world_context.remote_speed_limit()->speed())
          ? math::Clamp(world_context.remote_speed_limit()->accel(),
                        kMinAccelForRemoteSpeedLimiterInMpss, 0.0)
          : kMaintainSpeedAccelForRemoteSpeedLimiterInMpss;
  const double speed_from_remote_speed_limit =
      math::Clamp(world_context.remote_speed_limit()->speed(), 0.0,
                  kMaxSpeedForRemoteSpeedLimiterInMps);

  mutator.set_soft_yield_with_min_speed_accel(speed_from_remote_speed_limit,
                                              accel_from_remote_speed_limit);
  mutator.set_no_pass();

  rt_event::PostRtEvent<rt_event::planner::RemoteSpeedLimiterActivated>(
      world_context.remote_speed_limit()->DebugString());

  return true;
}

bool CautiousDrivingReasoner::Reason(
    const WorldContext& world_context, const TrajectoryInfo& trajectory_info,
    const std::vector<PredictionDecisionMakerOutput>&
        wrapped_prediction_decisions,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<planner::PredictedTrajectoryWrapper>>&
        object_prediction_map,
    const pb::CautiousDrivingReasonerConfig& config,
    const pb::PredictionDecisionMakerConfig& pdm_config,
    const pb::TargetSpeedLimitPatch& speed_limit_patch,
    const pb::CautiousDrivingReasonerSeed& previous_iter_seed,
    pb::CautiousDrivingReasonerSeed& current_iter_mutable_seed,
    ReasoningPolicies& reasoning_policies,
    CautiousDrivingLimiter* reference_limiter_ptr,
    ConstraintCreator* constraint_creator_ptr,
    GlobalStrategySetter* global_strategy_setter,
    planner::pb::EgoStuckFeatureAboutDecoupledSpeed*
        ego_stuck_feature_about_speed,
    pb::ReasoningDebug* reasoners_debug) {
  bool reference_profile_updated = false;
  pb::CautiousDrivingReasonerDebug* cautious_driving_reasoner_debug =
      (reasoners_debug != nullptr) ? reasoners_debug->mutable_reasoner_debug()
                                         ->mutable_cautious_driving_reasoner()
                                   : nullptr;

  UpdateCautiousDrivingReasonerSeed(previous_iter_seed,
                                    current_iter_mutable_seed,
                                    world_context.ego_pose_timestamp());

  const traffic_rules::JunctionInLaneSequence* first_junction_ahead_ptr =
      GetFirstJunctionAhead(
          trajectory_info.traffic_rules().lane_sequence_junctions);
  const auto turning_lane_info_iter = std::find_if(
      trajectory_info.traffic_rules().lane_sequence_lanes.begin(),
      trajectory_info.traffic_rules().lane_sequence_lanes.end(),
      [first_junction_ahead_ptr](
          const traffic_rules::LaneInfoInLaneSequence& lane_info) {
        return first_junction_ahead_ptr != nullptr &&
               lane_info.lane->id() ==
                   first_junction_ahead_ptr->ego_lane_in_junction_ptr->id();
      });

  const traffic_rules::CrosswalkInLaneSequence* first_crosswalk_ahead_ptr =
      GetFirstCrosswalkAheadEgoPath(trajectory_info.traffic_rules().crosswalks);
  const auto first_lane_intersecting_crosswalk_info_iter =
      first_crosswalk_ahead_ptr == nullptr
          ? trajectory_info.traffic_rules().lane_sequence_lanes.end()
          : std::find_if(
                trajectory_info.traffic_rules().lane_sequence_lanes.begin(),
                trajectory_info.traffic_rules().lane_sequence_lanes.end(),
                [first_crosswalk_ahead_ptr](
                    const traffic_rules::LaneInfoInLaneSequence& lane_info) {
                  return !first_crosswalk_ahead_ptr
                              ->lanes_intersecting_crosswalk.empty() &&
                         first_crosswalk_ahead_ptr->lanes_intersecting_crosswalk
                                 .front()
                                 ->id() == lane_info.lane->id();
                });

  for (const PredictionDecisionMakerOutput& output :
       wrapped_prediction_decisions) {
    bool constraint_added_for_agent = false;
    pb::CautiousDrivingAgentDebug local_agent_debug;
    pb::CautiousDrivingAgentDebug* cautious_driving_agent_debug =
        cautious_driving_reasoner_debug == nullptr ? nullptr
                                                   : &local_agent_debug;
    const ReasoningObject& reasoning_object = output.reasoning_object();
    // TODO(speed): integrate low confident object from perception.
    if (reasoning_object.is_low_confidence_object()) {
      continue;
    }
    if (cautious_driving_agent_debug != nullptr) {
      cautious_driving_agent_debug->set_object_id(reasoning_object.id());
    }

    // Add Const Speed Constraint for new detected object.
    constraint_added_for_agent |= AddCautiousSpeedLimitForNewDetectObject(
        output.reasoning_object(), constraint_creator_ptr,
        cautious_driving_agent_debug);

    // Add reference limit for the given object based on its proximity info.
    constraint_added_for_agent |= AddReferenceLimitForObjectProximity(
        world_context, output.reasoning_object(), trajectory_info, config,
        reasoning_policies, reference_limiter_ptr,
        cautious_driving_agent_debug);

    // Add reference limit for objects which cause occlusions.
    constraint_added_for_agent |= AddReferenceLimitForOcclusion(
        world_context, output.reasoning_object(), trajectory_info, config,
        reference_limiter_ptr, cautious_driving_agent_debug);

    // Add reference limit for dynamic agents approaching Ego based on their
    // proximity info, including VRUs who are approaching Ego and vehicles
    // across ego lane boundaries without any overlapping predicted trajectory.
    constraint_added_for_agent |= AddReferenceLimitForDynamicProximity(
        world_context, trajectory_info, output.reasoning_object(),
        output.prediction_decisions(), config, reference_limiter_ptr,
        constraint_creator_ptr, cautious_driving_agent_debug);

    // Add const speed proximity speed constraint based on agents' current
    // longitudinal speed for vehicles or cyclists overtaken by Ego to avoid
    // passing them with high speed.
    constraint_added_for_agent |=
        AddProximitySpeedConstraintForAgentsOvertakenByEgo(
            output, constraint_creator_ptr);

    // TODO(minhanli): There will be a DCHECK failure when there exist other
    // tree-search gap aligns. So we exclude lane change preparation scenarios
    // in which tree-search gap aligns are being used for now. Remove this
    // limitation when solver's logic for prioritizing various types of gap
    // aligns is ready.
    if (turning_lane_info_iter !=
            trajectory_info.traffic_rules().lane_sequence_lanes.end() &&
        !trajectory_info.should_run_lane_change_gap_align() &&
        !trajectory_info.pull_over_info().gap_align_meta_data().has_value()) {
      // Add gap align constraint for large vehicles turning along with ego.
      const bool gap_align_constraint_added_for_agent =
          AddGapAlignForLargeVehicleTurningAlongEgo(
              world_context, trajectory_info, reasoning_object,
              *turning_lane_info_iter, output.prediction_decisions(),
              object_prediction_map, config, pdm_config, constraint_creator_ptr,
              cautious_driving_agent_debug);
      // Solver will take the min value of global and per-constraint max active
      // time in gap align search problem. The default value of global max
      // active time is 5.0. To avoid the per-constraint max active time being
      // overwritten by the global value, we manually set a large value for
      // global max active time if there is any large turning gap align
      // constraint being added.
      if (gap_align_constraint_added_for_agent) {
        global_strategy_setter->set_global_gap_align_pass_max_active_time(
            planner::constants::kTrajectoryHorizonInSec);
        global_strategy_setter->set_global_gap_align_yield_max_active_time(
            planner::constants::kTrajectoryHorizonInSec);
      }
      constraint_added_for_agent |= gap_align_constraint_added_for_agent;
    }

    if (turning_lane_info_iter !=
            trajectory_info.traffic_rules().lane_sequence_lanes.end() ||
        first_lane_intersecting_crosswalk_info_iter !=
            trajectory_info.traffic_rules().lane_sequence_lanes.end()) {
      // Check if there exist both junction and crosswalk ahead.
      const bool exist_both_junction_and_xwalk_ahead =
          turning_lane_info_iter !=
              trajectory_info.traffic_rules().lane_sequence_lanes.end() &&
          first_lane_intersecting_crosswalk_info_iter !=
              trajectory_info.traffic_rules().lane_sequence_lanes.end();
      auto occlusion_lane_info_iter = turning_lane_info_iter;
      if (occlusion_lane_info_iter ==
          trajectory_info.traffic_rules().lane_sequence_lanes.end()) {
        occlusion_lane_info_iter = first_lane_intersecting_crosswalk_info_iter;
      } else if (exist_both_junction_and_xwalk_ahead) {
        // Take the closest lane as the place where the occlusion may occur.
        occlusion_lane_info_iter =
            (first_lane_intersecting_crosswalk_info_iter->start_ra_arclength <
             turning_lane_info_iter->start_ra_arclength)
                ? first_lane_intersecting_crosswalk_info_iter
                : turning_lane_info_iter;
      }
      constraint_added_for_agent |=
          AddProximitySpeedConstraintForLargeVehicleCausingOcclusion(
              world_context, reasoning_object, *occlusion_lane_info_iter,
              trajectory_info.lane_sequence_iterator(), config,
              constraint_creator_ptr, cautious_driving_agent_debug);
    }

    if (reasoning_util::ShouldBeCautiousForBusNearBusBulb(reasoning_object,
                                                          trajectory_info)) {
      constraint_added_for_agent |=
          AddProximitySpeedConstraintForBusNearBusBulb(
              reasoning_object, constraint_creator_ptr,
              cautious_driving_agent_debug);
    }

    constraint_added_for_agent |= AddReferenceLimitForObservationCautious(
        world_context, trajectory_info, output.reasoning_object(),
        output.prediction_decisions(), config, current_iter_mutable_seed,
        reference_limiter_ptr, cautious_driving_agent_debug);

    for (const PredictionDecision& prediction_decision :
         output.prediction_decisions()) {
      // Add reference limit for predicted trajectory.
      constraint_added_for_agent |= AddReferenceLimitForPrediction(
          world_context, trajectory_info, prediction_decision, config,
          reference_limiter_ptr, constraint_creator_ptr,
          cautious_driving_agent_debug);
    }

    reference_profile_updated |= constraint_added_for_agent;
    // Output debug only if constraint is added or any scene/predicted
    // trajectory debug is added.
    if (cautious_driving_agent_debug != nullptr &&
        (constraint_added_for_agent ||
         IsAgentDebugUpdated(local_agent_debug))) {
      *cautious_driving_reasoner_debug->add_agents() =
          std::move(*cautious_driving_agent_debug);
    }
  }

  // Add aggregated reference limits for pedestrians at high agent density.
  reference_profile_updated |= AddReferenceLimitForPedestrianAtHighAgentDensity(
      world_context, wrapped_prediction_decisions, config,
      reference_limiter_ptr, cautious_driving_reasoner_debug);

  // physical boundary
  reference_profile_updated |= AddReferenceLimitForBoundaryProximity(
      world_context, trajectory_info, config, constraint_creator_ptr,
      reference_limiter_ptr, ego_stuck_feature_about_speed,
      cautious_driving_reasoner_debug);

  if (FLAGS_planning_enable_junction_speed_limits) {
    reference_profile_updated |= AddReferenceLimitForJunctions(
        world_context, trajectory_info, config, speed_limit_patch,
        reference_limiter_ptr, cautious_driving_reasoner_debug);
  } else {
    DCHECK_EQ(world_context.pnc_map_service()->hdmap()->config().name(),
              "suzhou_close_test");
  }

  // Be cautious when driving nearby a road exit zone, more specifically, in the
  // right most lane.
  reference_profile_updated |= AddReferenceLimitForExitZoneNearby(
      trajectory_info, config, reference_limiter_ptr,
      cautious_driving_reasoner_debug);

  // Add cautious driving limits for narrow passages.
  reference_profile_updated |= AddReferenceLimitForNarrowPassage(
      world_context, config, reference_limiter_ptr,
      cautious_driving_reasoner_debug);

  // Add cautious driving limits for map change regions.
  reference_profile_updated |= AddReferenceLimitForMapChangeRegion(
      trajectory_info, config, reference_limiter_ptr,
      cautious_driving_reasoner_debug);

  // Add cautious driving limits for crosswalks at where the traffic lights are
  // not green. When Ego is at the most right or left lane and VRUs around
  // crosswalks are close to the lane, Ego should reduce speed according to
  // traffic rules.
  reference_profile_updated |= AddReferenceLimitForVRUAtCrosswalk(
      world_context, wrapped_prediction_decisions, trajectory_info, config,
      reference_limiter_ptr, cautious_driving_reasoner_debug);

  // Add cautious driving limits for traffic lights, including yellow flashing
  // light and broken light.
  reference_profile_updated |= AddReferenceLimitForTrafficLight(
      wrapped_prediction_decisions, trajectory_info, config,
      reference_limiter_ptr, cautious_driving_reasoner_debug);

  // Get whether ego on expressway.
  voy::Pose ego_pose;
  const auto& snap_shot = world_context.robot_state().current_state_snapshot();
  ego_pose.set_x(snap_shot.x());
  ego_pose.set_y(snap_shot.y());
  ego_pose.set_z(snap_shot.z());
  ego_pose.set_yaw(snap_shot.heading());
  // TODO(EH): Consider replace this with standard Map interface once map ready.
  const bool is_ego_on_expressway =
      exception_handler::exception_handler_main::IsOnExpressway(
          ego_pose, world_context.pnc_map_service());
  // Add cautious driving limits for exception handler.
  if (FLAGS_planning_enable_exception_handler_cautious_driving &&
      (!is_ego_on_expressway ||
       exception_handler::FLAGS_exception_handler_enable_eh_on_highway)) {
    reference_profile_updated |= AddReferenceLimitForExceptionHandler(
        trajectory_info, reference_limiter_ptr,
        cautious_driving_reasoner_debug);
  }

  // Add cautious driving limits for remote assist.
  if (FLAGS_planning_enable_speed_cautious_driving_reasoner_remote_speed_limit) {  // NOLINT
    reference_profile_updated |= AddReferenceLimitForRemoteSpeedLimiter(
        world_context, constraint_creator_ptr);
  }

  return reference_profile_updated;
}

}  // namespace reasoner
}  // namespace speed
}  // namespace planner
