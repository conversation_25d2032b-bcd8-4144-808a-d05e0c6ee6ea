#include "planner/utility/common/common_utility.h"

#include <cmath>
#include <limits>
#include <unordered_set>
#include <vector>

#include "tbb/concurrent_unordered_map.h"

#include "av_comm/mode_config.h"
#include "base/now.h"
#include "geometry/model/point_2d.h"
#include "hdmap/lib/hdmap.h"
#include "hdmap/lib/hdmap_common.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/planning_gflags.h"
#include "planner/world_model/construction_zone/construction_zone.h"
#if !defined(BUILD_PYBIND_IN_CENTOS7)
#include "planner/world_model/planner_object/planner_object.h"
#endif
#include "planner_protos/common.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/pnc_object.h"
#include "types/canbus_mode.h"
#include "varch/base/thread.h"
#include "voy_protos/construction_zones.pb.h"
#include "voy_protos/point.pb.h"
#include "voy_protos/polygon.pb.h"
#include "voy_protos/pose.pb.h"
#include "voy_rt_event/rt_event_planner.h"

namespace planner {

namespace common {
// The distance threshold from end of lane sequence to ego front_bumper, used in
// IsEgoAtEndOfLaneSequence.
constexpr double kThresholdNearEndOfLaneSequenceInMeter = 15.0;
// The distance threshold if ego current lane is short, used in
// IsEgoInShortLaneSequence.
constexpr double kThresholdShortLaneLengthInMeter = 30.0;

void ConstructPBLaneSegment(int64_t lane_id, double start_arc_length,
                            double end_arc_length,
                            pb::LaneSegment* lane_segment) {
  if (!lane_segment) {
    return;
  }
  lane_segment->Clear();
  lane_segment->set_lane_id(lane_id);
  lane_segment->mutable_arc_length_range_m()->set_start_pos(start_arc_length);
  lane_segment->mutable_arc_length_range_m()->set_end_pos(end_arc_length);
}

void ConvertLaneSegmentToPB(const LaneSegment& lane_segment,
                            pb::LaneSegment* pb_lane_segment) {
  ConstructPBLaneSegment(
      lane_segment.lane->id(), lane_segment.arc_length_range_m.start_pos(),
      lane_segment.arc_length_range_m.end_pos(), pb_lane_segment);
}

math::pb::Range ConvertToMathPbRange(const double start_pos,
                                     const double end_pos) {
  math::pb::Range range_pb;
  range_pb.set_start_pos(start_pos);
  range_pb.set_end_pos(end_pos);
  return range_pb;
}

void ConvertLaneSegmentsToPB(const std::vector<LaneSegment>& lane_segments,
                             std::vector<pb::LaneSegment>* pb_lane_segments) {
  if (pb_lane_segments == nullptr) {
    return;
  }
  pb_lane_segments->reserve(lane_segments.size());
  for (const auto& segment : lane_segments) {
    pb::LaneSegment pb_lane_segment;
    common::ConstructPBLaneSegment(
        segment.lane->id(), segment.arc_length_range_m.start_pos(),
        segment.arc_length_range_m.end_pos(), &pb_lane_segment);
    pb_lane_segments->push_back(std::move(pb_lane_segment));
  }
}

void ConvertLaneSegmentsToPB(
    const std::vector<LaneSegment>& lane_segments,
    ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>*
        pb_lane_segments) {
  if (pb_lane_segments == nullptr) {
    return;
  }
  pb_lane_segments->Reserve(lane_segments.size());
  for (const auto& segment : lane_segments) {
    common::ConstructPBLaneSegment(
        segment.lane->id(), segment.arc_length_range_m.start_pos(),
        segment.arc_length_range_m.end_pos(), pb_lane_segments->Add());
  }
}

LaneSegment ConvertPBToLaneSegment(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const pb::LaneSegment& pb_lane_segment) {
  const pnc_map::Lane* lane =
      joint_pnc_map_service.GetLaneSequence({pb_lane_segment.lane_id()})
          .front();
  return LaneSegment(lane, pb_lane_segment.arc_length_range_m().start_pos(),
                     pb_lane_segment.arc_length_range_m().end_pos());
}

void ConvertPBToLaneSegments(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>&
        lane_segments,
    std::vector<LaneSegment>* lane_segments_out) {
  if (lane_segments.empty() || lane_segments_out == nullptr) {
    return;
  }
  std::vector<int64_t> lane_ids;
  lane_ids.reserve(lane_segments.size());
  for (const auto& segment : lane_segments) {
    lane_ids.push_back(segment.lane_id());
  }
  const std::vector<const pnc_map::Lane*> lane_sequence =
      joint_pnc_map_service.GetLaneSequence(lane_ids);
  lane_segments_out->reserve(lane_segments.size());
  for (int lane_idx = 0; lane_idx < lane_segments.size(); ++lane_idx) {
    const pnc_map::Lane* lane = lane_sequence.at(lane_idx);
    if (lane == nullptr) {
      return;
    }
    const pb::LaneSegment& segment = lane_segments.at(lane_idx);
    lane_segments_out->emplace_back(lane,
                                    segment.arc_length_range_m().start_pos(),
                                    segment.arc_length_range_m().end_pos());
  }
}

void ConvertPBToLaneSegments(
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const std::vector<pb::LaneSegment>& lane_segments,
    std::vector<LaneSegment>* lane_segments_out) {
  if (lane_segments.empty() || lane_segments_out == nullptr) {
    return;
  }
  std::vector<int64_t> lane_ids;
  lane_ids.reserve(lane_segments.size());
  for (const auto& segment : lane_segments) {
    lane_ids.push_back(segment.lane_id());
  }
  const std::vector<const pnc_map::Lane*> lane_sequence =
      joint_pnc_map_service.GetLaneSequence(lane_ids);
  lane_segments_out->reserve(lane_segments.size());
  for (size_t lane_idx = 0; lane_idx < lane_segments.size(); ++lane_idx) {
    const pnc_map::Lane* lane = lane_sequence.at(lane_idx);
    if (lane == nullptr) {
      return;
    }
    const pb::LaneSegment& segment = lane_segments.at(lane_idx);
    lane_segments_out->emplace_back(lane,
                                    segment.arc_length_range_m().start_pos(),
                                    segment.arc_length_range_m().end_pos());
  }
}

::google::protobuf::RepeatedPtrField<hdmap::LaneSegment>
ConvertToHdmapLaneSegments(
    const ::google::protobuf::RepeatedPtrField<::planner::pb::LaneSegment>&
        lane_segments) {
  google::protobuf::RepeatedPtrField<hdmap::LaneSegment> new_lane_segments;
  new_lane_segments.Reserve(lane_segments.size());
  for (const auto& lane_segment : lane_segments) {
    auto* new_lane_segment = new_lane_segments.Add();
    new_lane_segment->set_lane_id(lane_segment.lane_id());
    new_lane_segment->mutable_arc_length_range_m()->CopyFrom(
        common::ConvertToMathPbRange(
            lane_segment.arc_length_range_m().start_pos(),
            lane_segment.arc_length_range_m().end_pos()));
  }
  return new_lane_segments;
}

// NOTE: Not all fields are assigned.
voy::Pose ConvertTrajectoryPoseToImuPose(
    const pb::TrajectoryPose& trajectory_pose) {
  voy::Pose pose;
  pose.set_timestamp(trajectory_pose.timestamp());

  pose.set_x(trajectory_pose.x_pos());
  pose.set_y(trajectory_pose.y_pos());
  pose.set_z(trajectory_pose.z_pos());

  pose.set_yaw(trajectory_pose.heading());

  pose.set_vel_x(trajectory_pose.speed() * std::cos(trajectory_pose.heading()));
  pose.set_vel_y(trajectory_pose.speed() * std::sin(trajectory_pose.heading()));
  return pose;
}

voy::TrackedObject GetTrackedObjectWithPredictedPose(
    const voy::TrackedObject& object, const pb::TrajectoryPose& new_pose) {
  voy::TrackedObject new_object(object);
  new_object.set_center_x(new_pose.x_pos());
  new_object.set_center_y(new_pose.y_pos());
  new_object.set_center_z(new_pose.z_pos());
  new_object.set_velocity(new_pose.speed());
  new_object.set_heading(new_pose.heading());
  new_object.set_box_heading(new_pose.heading());
  new_object.set_omega(new_pose.steering_rate());
  new_object.set_acceleration(new_pose.accel());
  // Set bounding box as contour, as we do not have predicted contour info yet
  new_object.clear_contour();
  const math::geometry::OrientedBox2d bounding_box =
      math::geometry::OrientedBox2d(new_object.center_x(),
                                    new_object.center_y(), new_object.length(),
                                    new_object.width(), new_object.heading());
  for (const math::geometry::Point2d p : bounding_box.CornerPoints()) {
    voy::Point2d* contour_point = new_object.mutable_contour()->Add();
    contour_point->set_x(p.x());
    contour_point->set_y(p.y());
  }
  return new_object;
}

void ConstructPBLaneMarkingSegment(
    int64_t lane_marking_id, double start_arc_length, double end_arc_length,
    const pnc_map::Lane* left_lane, const pnc_map::Lane* right_lane,
    pb::LaneMarkingSegment* lane_marking_segment) {
  if (!lane_marking_segment) {
    return;
  }
  lane_marking_segment->Clear();
  lane_marking_segment->set_lane_marking_id(lane_marking_id);
  lane_marking_segment->mutable_arc_length_range_m()->set_start_pos(
      start_arc_length);
  lane_marking_segment->mutable_arc_length_range_m()->set_end_pos(
      end_arc_length);
  lane_marking_segment->set_left_lane_id(left_lane ? left_lane->id()
                                                   : hdmap::kInvalidId);
  lane_marking_segment->set_right_lane_id(right_lane ? right_lane->id()
                                                     : hdmap::kInvalidId);
}

bool IsRangesOverlap(const math::pb::Range& lhs, const math::pb::Range& rhs) {
  return !(rhs.end_pos() < lhs.start_pos() || rhs.start_pos() > lhs.end_pos());
}

bool HasBlockedLaneMarkingInLaneSegment(
    const ::google::protobuf::RepeatedPtrField<
        ::planner::pb::LaneMarkingSegment>& blocked_marking_segments,
    const pnc_map::Lane* lane, const math::pb::Range& arc_length_range_m,
    bool to_left) {
  if (blocked_marking_segments.empty()) {
    return false;
  }
  if (lane == nullptr) {
    return true;
  }
  const pnc_map::LaneMarking* target_marking =
      to_left ? lane->left_marking() : lane->right_marking();
  if (!target_marking) {
    return true;
  }
  const bool is_target_marking_blocked = std::any_of(
      blocked_marking_segments.begin(), blocked_marking_segments.end(),
      [&target_marking, arc_length_range_m](
          const pb::LaneMarkingSegment& blocked_lane_marking_segment) {
        return blocked_lane_marking_segment.lane_marking_id() ==
                   target_marking->id() &&
               IsRangesOverlap(
                   arc_length_range_m,
                   blocked_lane_marking_segment.arc_length_range_m());
      });
  return is_target_marking_blocked;
}

bool IsRangesOverlap(const math::pb::Range& lhs, const math::Range1d& rhs) {
  return !(rhs.end_pos < lhs.start_pos() || rhs.start_pos > lhs.end_pos());
}

#if !defined(BUILD_PYBIND_IN_CENTOS7)
bool HasBlockedLaneMarkingInLaneSegment(
    const std::map<int64_t, std::vector<LaneMarkingBlockage>>&
        blocked_lane_marking_segments,
    const pnc_map::Lane* lane, const math::pb::Range& arc_length_range_m,
    bool to_left) {
  if (blocked_lane_marking_segments.empty()) {
    return false;
  }
  if (lane == nullptr) {
    return true;
  }
  const pnc_map::LaneMarking* target_marking =
      to_left ? lane->left_marking() : lane->right_marking();
  if (target_marking == nullptr) {
    return true;
  }
  const auto iter = blocked_lane_marking_segments.find(target_marking->id());
  if (iter == blocked_lane_marking_segments.end()) {
    return false;
  }
  const bool is_target_marking_blocked = std::any_of(
      iter->second.begin(), iter->second.end(),
      [&arc_length_range_m](const LaneMarkingBlockage& lane_marking_blockage) {
        return IsRangesOverlap(arc_length_range_m,
                               lane_marking_blockage.arc_length_range_m);
      });
  return is_target_marking_blocked;
}

bool ShouldConsiderOpenDoorSignal(const planner::PlannerObject& planner_object,
                                  double signed_lateral_gap,
                                  double relative_box_heading) {
  if (!planner_object.HasPerceptionAttribute(
          voy::perception::VEHICLE_DOOR_OPEN)) {
    return false;
  }

  DCHECK(!math::NearZero(signed_lateral_gap));
  // NOTE: Synced with perception, |DOOR_OPEN_RIGHT| and |DOOR_OPEN_LEFT| are
  // determined relative to agent's bbox heading.
  if (!planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_LEFT) &&
      !planner_object.HasPerceptionAttribute(
          voy::perception::DOOR_OPEN_RIGHT)) {
    // Return true directly to guarantee recall if perception does not provide
    // info about the side of open door.
    return true;
  }

  if (planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_LEFT) &&
      planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_RIGHT)) {
    // Return true directly if perception has output both side of open door.
    return true;
  }

  // Return false if the following conditions are met, indicating that open door
  // side is not facing Ego path: 1) same-direction agent on Ego path right has
  // a valid DOOR_OPEN_RIGHT signal, 2) same-direction agent on Ego path left
  // has a valid DOOR_OPEN_LEFT signal, 3) oncoming-direction agent on Ego path
  // right has a valid DOOR_OPEN_LEFT signal, 4) oncoming-direction agent on Ego
  // path left has a valid DOOR_OPEN_RIGHT signa.
  // If none of these conditions are met, return true.
  return !(
      (std::abs(relative_box_heading) <= M_PI_4 && signed_lateral_gap < 0.0 &&
       planner_object.HasPerceptionAttribute(
           voy::perception::DOOR_OPEN_RIGHT)) ||
      (std::abs(relative_box_heading) <= M_PI_4 && signed_lateral_gap > 0.0 &&
       planner_object.HasPerceptionAttribute(
           voy::perception::DOOR_OPEN_LEFT)) ||
      (std::abs(relative_box_heading) >= 3 * M_PI_4 &&
       signed_lateral_gap > 0.0 &&
       planner_object.HasPerceptionAttribute(
           voy::perception::DOOR_OPEN_RIGHT)) ||
      (std::abs(relative_box_heading) >= 3 * M_PI_4 &&
       signed_lateral_gap < 0.0 &&
       planner_object.HasPerceptionAttribute(voy::perception::DOOR_OPEN_LEFT)));
}

const PredictedTrajectoryWrapper* FindClosestLeadAgentTrajectory(
    const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& lane_sequence_result,
    const math::geometry::PolylineCurve2d& nominal_path,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const tbb::concurrent_unordered_map<
        ObjectId, std::vector<PredictedTrajectoryWrapper>>&
        object_prediction_map) {
  std::unordered_set<ObjectId> lane_ids_set;
  for (const auto* lane : lane_sequence_result) {
    lane_ids_set.insert(lane->id());
  }

  // Take all agents as the sanme lane agents if their latest_isolated_lane_opt
  // match any lane in the ego route.
  std::unordered_map<ObjectId, const PredictedTrajectoryWrapper*>
      lane_matched_agents;
  for (const auto& [object_id, predicted_trajectories] :
       object_prediction_map) {
    // Only consider vehicle objects.
    auto it = planner_object_map.find(object_id);
    DCHECK(it != planner_object_map.end());
    if (!it->second.is_vehicle() && !it->second.is_cyclist() &&
        !it->second.is_tricycle()) {
      continue;
    }
    for (const auto& predicted_trajectory : predicted_trajectories) {
      // Only take the primary trajectory.
      if (!predicted_trajectory.is_primary_trajectory()) {
        continue;
      }
      // Skip predicted_trajectory if it has not generated the
      // latest_isolated_lane_opt.
      if (!predicted_trajectory.latest_isolated_lane_opt().has_value()) {
        continue;
      }
      const pnc_map::Lane* latest_isolated_lane =
          predicted_trajectory.latest_isolated_lane_opt()->lane_ptr;
      if (latest_isolated_lane != nullptr &&
          lane_ids_set.count(latest_isolated_lane->id())) {
        lane_matched_agents[object_id] = &predicted_trajectory;
        break;
      }
    }
  }

  // Find the closest lead agent.
  double closest_distance = std::numeric_limits<double>::max();
  const PredictedTrajectoryWrapper* closest_lead_agent_trajectory = nullptr;
  for (const auto& [object_id, predicted_trajectory] : lane_matched_agents) {
    DCHECK(predicted_trajectory != nullptr);
    // Skip predicted_trajectory if it has no trajectory.
    if (predicted_trajectory->empty()) {
      continue;
    }
    const double agent_arc_length =
        nominal_path
            .GetProximity({predicted_trajectory->pose(0).x_pos(),
                           predicted_trajectory->pose(0).y_pos()},
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    const double ego_arc_length =
        nominal_path
            .GetProximity({ego_pose.x(), ego_pose.y()},
                          math::pb::UseExtensionFlag::kForbid)
            .arc_length;
    // Skip the same lane agents that are behind of ego.
    if (agent_arc_length < ego_arc_length) {
      continue;
    }
    auto it = planner_object_map.find(object_id);
    DCHECK(it != planner_object_map.end());
    const double distance_to_ego = it->second.l2_distance_to_ego_m();
    // Select the closest lead agent.
    if (distance_to_ego < closest_distance) {
      closest_distance = distance_to_ego;
      closest_lead_agent_trajectory = predicted_trajectory;
    }
  }

  return closest_lead_agent_trajectory;
}
#endif

bool HasBlockedLaneMarkingInSegmentSequence(
    const ::google::protobuf::RepeatedPtrField<
        ::planner::pb::LaneMarkingSegment>& blocked_marking_segments,
    const std::vector<LaneSegment>& segment_sequence, bool to_left) {
  if (blocked_marking_segments.empty() || segment_sequence.empty()) {
    return false;
  }
  for (const auto& lane_segment : segment_sequence) {
    if (HasBlockedLaneMarkingInLaneSegment(
            blocked_marking_segments, lane_segment.lane,
            lane_segment.arc_length_range_m, to_left)) {
      return true;
    }
  }
  return false;
}

bool IsValidArcLengthRange(double start_pos, double end_pos) {
  if (start_pos >= 0.0 && end_pos >= 0.0 && end_pos > start_pos) {
    return true;
  }
  return false;
}

bool IsValidArcLengthRange(const math::Range1d& arc_length_range_m) {
  return IsValidArcLengthRange(arc_length_range_m.start_pos,
                               arc_length_range_m.end_pos);
}

bool IsSubRange(const math::pb::Range& first_range,
                const math::pb::Range& second_range) {
  return first_range.start_pos() <= second_range.start_pos() &&
         first_range.end_pos() >= second_range.end_pos();
}

bool IsMapChangeArea(
    const ConstructionZone& cz,
    const std::optional<voy::MapChangeAreaAttribute>& attr_need) {
  return cz.space_type == voy::SpaceType::MAP_CHANGE_AREA &&
         (!attr_need.has_value() ||
          std::any_of(cz.map_change_area_attributes.begin(),
                      cz.map_change_area_attributes.end(),
                      [&attr_need](voy::MapChangeAreaAttribute attribute) {
                        return attribute == *attr_need;
                      }));
}

bool IsMapChangeArea(
    const voy::ConstructionZone& cz,
    const std::optional<voy::MapChangeAreaAttribute>& attr_need) {
  return cz.space_type() == voy::SpaceType::MAP_CHANGE_AREA &&
         (!attr_need.has_value() ||
          std::any_of(cz.map_change_area_attributes().begin(),
                      cz.map_change_area_attributes().end(),
                      [&attr_need](int attribute) {
                        return attribute == static_cast<int>(*attr_need);
                      }));
}

void ConvertConstructionZoneToPB(
    const ConstructionZone& construction_zone,
    voy::ConstructionZone* mutable_construction_zone_proto) {
  if (!mutable_construction_zone_proto) {
    return;
  }
  mutable_construction_zone_proto->set_zone_source(
      construction_zone.zone_source);
  mutable_construction_zone_proto->set_id(construction_zone.id);
  mutable_construction_zone_proto->mutable_zone()->CopyFrom(
      math::geometry::Convert<voy::Polygon2d>(construction_zone.contour));
  for (const pnc_map::Lane* lane : construction_zone.associated_lanes) {
    if (lane == nullptr) {
      continue;
    }
    mutable_construction_zone_proto->add_lane_ids(lane->id());
  }
  mutable_construction_zone_proto->set_dist(construction_zone.dist_to_ego);
  mutable_construction_zone_proto->set_space_type(construction_zone.space_type);
  mutable_construction_zone_proto->set_original_rtm_id(
      construction_zone.original_rtm_id);
  for (const voy::MapChangeAreaAttribute attr :
       construction_zone.map_change_area_attributes) {
    mutable_construction_zone_proto->add_map_change_area_attributes(attr);
  }
}

ConstructionZone ConvertPBToConstructionZone(
    const voy::ConstructionZone& construction_zone_proto) {
  return ConstructionZone(/*timestamp=*/base::Now(), construction_zone_proto,
                          /*perception_range_lanes=*/{});
}

size_t CPUCoresNumberOfPlanner() {
  static size_t cpu_number = varch::base::GetProcessAffinityCpuNumber();
  return cpu_number;
}

namespace local_state_view {
bool IsLocallyInAutonomyControlMode(const voy::VehicleMode& canbus_mode) {
  return av_comm::local_view::IsLocallyAutoMode(
      canbus_mode,
      /*aligned_with_IsAutonomyControlMode=*/
      !planner::FLAGS_planning_enable_sim_planner_init_state_recovery_mode);
}
}  // namespace local_state_view

bool IsEgoNearEndOfLaneSequence(
    const math::geometry::PolylineCurve2d& nominal_path_lane_sequence,
    const math::geometry::Point2d& front_bumper_position) {
  const double ego_arclength =
      nominal_path_lane_sequence
          .GetProximity(math::geometry::Point2d(front_bumper_position.x(),
                                                front_bumper_position.y()),
                        math::pb::UseExtensionFlag::kForbid)
          .arc_length;

  const double end_arclength = nominal_path_lane_sequence.GetTotalArcLength();

  if (end_arclength - ego_arclength < kThresholdNearEndOfLaneSequenceInMeter) {
    // Add cloud buried point
    rt_event::PostRtEvent<rt_event::planner::EgoNearEndOfLaneSequence>();
    return true;
  }

  return false;
}

bool IsEgoInShortLaneSequence(
    const math::geometry::PolylineCurve2d& nominal_path_lane_sequence) {
  return nominal_path_lane_sequence.GetTotalArcLength() <
         kThresholdShortLaneLengthInMeter;
}

bool IsEgoInTheOppositeRoad(
    const math::geometry::Point2d& rear_axle_position,
    const std::vector<const pnc_map::Lane*>& current_lanes_for_regional_path) {
  std::unordered_set<int64_t> checked_opposite_road_ids;
  // Check if ego is on the oppsite road.
  for (const auto* regional_path_lane : current_lanes_for_regional_path) {
    // Get opposite road
    if (!regional_path_lane || !regional_path_lane->section() ||
        !regional_path_lane->section()->road() ||
        !regional_path_lane->section()->road()->opposite_road()) {
      continue;
    }
    const auto& opposite_road =
        regional_path_lane->section()->road()->opposite_road();
    if (checked_opposite_road_ids.find(opposite_road->id()) !=
        checked_opposite_road_ids.end()) {
      continue;
    }
    checked_opposite_road_ids.insert(opposite_road->id());

    // Ego is on the opposite lane.
    if (math::geometry::Within(rear_axle_position, opposite_road->border())) {
      rt_event::PostRtEvent<
          rt_event::planner::WaypointAssistEgoInTheOppositeRoad>();
      return true;
    }
  }
  return false;
}

bool WillEgoBorrowOppositeRoad(
    const math::geometry::PolylineCurve2d& extended_path,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  std::unordered_set<int64_t> drivable_road_ids;
  for (const auto& lane : lane_sequence) {
    drivable_road_ids.insert(lane->section()->road()->id());
  }

  for (const auto& lane : lane_sequence) {
    // Skip if the opposite road is empty or drivable.
    if (lane->section()->road()->opposite_road() == nullptr) {
      continue;
    }
    const auto& opposite_road = lane->section()->road()->opposite_road();
    const int64_t opposite_road_id = opposite_road->id();
    if (drivable_road_ids.find(opposite_road_id) != drivable_road_ids.end()) {
      continue;
    }

    // extended_path has any point in the opposite road.
    if (std::any_of(extended_path.points().begin(),
                    extended_path.points().end(),
                    [opposite_road](const auto& trajectory_point) {
                      return math::geometry::Within(trajectory_point,
                                                    opposite_road->border());
                    })) {
      return true;
    }
  }
  return false;
}

math::geometry::Point2d ComputeCentroid(
    const math::geometry::PolygonWithCache2d& polygon) {
  math::geometry::Point2d polygon_centroid(0, 0);
  boost::geometry::centroid(polygon.points(), polygon_centroid);
  return polygon_centroid;
}

math::geometry::PolylineCurve2d ConnectLanesCenterLines(
    const std::vector<const pnc_map::Lane*>& lanes) {
  math::geometry::PolylineCurve2d connected_line;
  for (const pnc_map::Lane* lane : lanes) {
    if (lane == nullptr) {
      continue;
    }
    connected_line.AppendCurveSegment(lane->center_line());
  }
  return connected_line;
}

}  // namespace common
}  // namespace planner
