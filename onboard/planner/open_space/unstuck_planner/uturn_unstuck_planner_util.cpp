#include "planner/open_space/unstuck_planner/uturn_unstuck_planner_util.h"

#include <algorithm>
#include <limits>
#include <unordered_set>
#include <utility>

#include "geometry/algorithms/curvature.h"
#include "geometry/algorithms/intersects.h"
#include "hdmap/lib/point_util.h"
#include "planner/behavior/util/lane_sequence_geometry/lane_sequence_geometry_utility.h"
#include "planner/speed/constraint/constraint_util.h"
#include "rt_event/rt_event.h"
#include "trace/trace.h"
#include "voy_rt_event/rt_event_planner.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace unstuck {

namespace {

// The heading change threshold used to justify whether this u-turn lane is the
// lane that before the entrance of the actual u-turn lane.
constexpr double kUTurnLaneHeadingChangeThresholdInRad = M_PI / 6.0;
// The distance threshold to check dominant constraint as u-turn blockage. The
// distance is from EGO's center of rear axis to the closest point of the object
// along the path.
constexpr double kMaxDistanceToBlockageInMeter = 7.0;
// The padding width of extended path. The extended path is used to check if
// dominant constraint object overlap with ego's path. This value estimated
// from speed extend path which used to check overlap.
constexpr double kLateralDistBufferToCheckObjectOnPathInMeter = 1.0;
// The padding width of extended path for checking if physical boundary overlap
// with ego's path.
constexpr double kLateralDistBufferToCheckPhysicalBoundaryOnPathInMeter = 0.3;
// The max arc length of path we used to determine if dominant constraint
// object overlap with ego's path.
constexpr double kMaxArcLengthToCheckObjectOnPathInMeter = 10.0;
// The step size of path that we used its oriented box to check overlap with
// dominant constraint object.
constexpr double kStepToCheckObjectOnPathInMeter = 2.0;
// The distance buffer of uturn lane extension to check if ego is close to the
// predecessor uturn lane.
constexpr double kDistanceBufferToCheckEgoCloseToUTurnLaneInMeter = 0.0;
// The length extension buffer for EGO's box when check collision.
constexpr double kEgoBoxLengthBufferInMeter = 0.5;
// The width extension buffer for EGO's box when check collision.
constexpr double kEgoBoxWidthBufferinMeter = 0.2;

// Trace all connecting u-turn lanes towards prodecessor or successor. Note that
// the current lane is not included.
std::vector<const pnc_map::Lane*> TraceConnectingUTurnLanes(
    const pnc_map::Lane* current_lane, bool is_for_predecessor) {
  std::vector<const pnc_map::Lane*> connecting_uturn_lanes;
  while (current_lane != nullptr) {
    const auto& next_lanes = is_for_predecessor ? current_lane->predecessors()
                                                : current_lane->successors();
    current_lane = nullptr;
    for (const auto* next_lane : next_lanes) {
      if (!IsStrictUTurnLane(next_lane)) {
        continue;
      }
      // Found the first u-turn one, ignore all other lanes.
      connecting_uturn_lanes.push_back(next_lane);
      current_lane = next_lane;
      break;
    }
  }
  if (is_for_predecessor) {
    std::reverse(connecting_uturn_lanes.begin(), connecting_uturn_lanes.end());
  }
  return connecting_uturn_lanes;
}

// Checks if geometry overlap with ego's path.
template <typename Geometry>
bool IsOverlapWithPath(const speed::TrajectoryInfo& trajectory_info,
                       const Geometry& geometry, double arc_length_to_check,
                       double lateral_dist_buffer) {
  const speed::PathForOverlap& path_for_overlap =
      trajectory_info.unextended_path_for_overlap();
  const std::vector<math::geometry::OrientedBox2d> path_boxes =
      path_for_overlap.ConstructPathOrientedBoxes(lateral_dist_buffer);
  DCHECK_EQ(path_for_overlap.densified_arc_lengths().size(), path_boxes.size())
      << "path_for_overlap.densified_arc_lengths() size should align with "
         "path_boxes size.";
  // Reduce the resolution of each step to decrease computation time.
  for (size_t index = 0;
       index < path_for_overlap.densified_center_points().size();
       index +=
       static_cast<size_t>(kStepToCheckObjectOnPathInMeter /
                           speed::kEgoPathPointDenseSamplingSpacingInMeter)) {
    // Trim the path to decrease computation time.
    if (path_for_overlap.densified_arc_lengths()[index] > arc_length_to_check) {
      break;
    }
    if (math::geometry::Intersects(path_boxes[index], geometry)) {
      return true;
    }
  }
  return false;
}

// Checks if right hard boundary is UTurn Blockage. Only right boundary has the
// potential to become a blockage during ego's Uturn.
// Return nullopt when ego's path not overlap with hard boundary, which means
// hard boudary is not Uturn blockage.
std::optional<UTurnUnstuckBlockageInfo> CheckHardBoundaryIsUTurnBlockage(
    const speed::TrajectoryInfo& trajectory_info,
    const std::vector<math::geometry::PolylineCurve2d>&
        right_hard_boundary_lines,
    double arc_length_to_check) {
  for (const math::geometry::PolylineCurve2d& right_hard_boundary_line :
       right_hard_boundary_lines) {
    if (IsOverlapWithPath(
            trajectory_info, right_hard_boundary_line, arc_length_to_check,
            kLateralDistBufferToCheckPhysicalBoundaryOnPathInMeter)) {
      return UTurnUnstuckBlockageInfo(
          pb::UTurnUnstuckBlockage::kPhysicalBoundary,
          /*physical_boundary_polyline=*/right_hard_boundary_line,
          pb::UTurnUnstuckBlockageDebug::kIsUTurnUnstuckBlockage);
    }
  }
  return std::nullopt;
}

}  // namespace

bool IsStrictUTurnLane(const pnc_map::Lane* lane) {
  if (lane->turn() != hdmap::Lane::U_TURN) {
    return false;
  }
  if (lane->type() == hdmap::Lane::REGULAR) {
    const math::geometry::PolylineCurve2d& center_line = lane->center_line();
    const double start_heading = center_line.GetInterpTheta(0.0);
    const double end_heading =
        center_line.GetInterpTheta(center_line.GetTotalArcLength());
    if (std::abs(end_heading - start_heading) <
        kUTurnLaneHeadingChangeThresholdInRad) {
      // This U-turn lane is regular and has little heading check. It is highly
      // likely the lane before the u-turn entrance.
      return false;
    }
  }
  return true;
}

std::vector<const pnc_map::Lane*> RetrieveEntireUTurnLaneSequenceByRouting(
    const pnc_map::Lane* current_lane,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  if (current_lane->turn() != hdmap::Lane::U_TURN) {
    // Lane for retrieving lane sequence is not uturn lane.
    return {};
  }
  std::vector<const pnc_map::Lane*> all_uturn_lanes;
  const size_t size = lane_sequence.size();
  for (size_t i = 0; i < size; ++i) {
    if (lane_sequence.at(i) == current_lane) {
      // Found the current lane in lane sequence.
      size_t left_idx = i;
      size_t right_idx = i;
      while (left_idx > 0 &&
             IsStrictUTurnLane(lane_sequence.at(left_idx - 1))) {
        --left_idx;
      }
      while (right_idx + 1 < size &&
             IsStrictUTurnLane(lane_sequence.at(right_idx + 1))) {
        ++right_idx;
      }
      for (size_t j = left_idx; j <= right_idx; ++j) {
        all_uturn_lanes.push_back(lane_sequence.at(j));
      }
      break;
    }
  }
  return all_uturn_lanes;
}

// Returns the Uturn lane preceding the current lane from the lane sequence.
// Returns nullptr if there is no predecessor lane or the predecessor is not a
// Uturn lane.
const pnc_map::Lane* GetPredecessorUturnLane(
    const pnc_map::Lane* current_lane,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  for (size_t i = 0; i < lane_sequence.size(); ++i) {
    if (lane_sequence.at(i) == current_lane && i > 0 &&
        IsStrictUTurnLane(lane_sequence.at(i - 1))) {
      return lane_sequence.at(i - 1);
    }
  }
  return nullptr;
}

bool IsUTurnOnlyLaneSequence(
    const std::vector<const pnc_map::Lane*>& uturn_lane_sequence) {
  const pnc_map::Lane* u_turn_start_lane = uturn_lane_sequence.front();
  const std::vector<const pnc_map::Lane*> predecessors =
      u_turn_start_lane->predecessors();
  DCHECK(!predecessors.empty());
  if (predecessors.size() > 1) {
    // NOTE(Jiakai): The u-turn start lane is a merge lane, which may have
    // uncertain interaction for k-turn. Skip it for now.
    return false;
  }
  const pnc_map::Lane* u_turn_predecessor_lane = predecessors.at(0);
  if (u_turn_predecessor_lane->turn() != hdmap::Lane::U_TURN ||
      u_turn_predecessor_lane->type() != hdmap::Lane::REGULAR) {
    return false;
  }
  return true;
}

bool HasConflictingDynamicBlockage(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const std::vector<pnc_map::ConflictingLane>& conflicting_lanes,
    pb::UTurnUnstuckTriggerDebug* uturn_unstuck_trigger_debug) {
  DCHECK(uturn_unstuck_trigger_debug != nullptr);
  std::unordered_set<int64_t> conflicting_lane_ids;
  for (const pnc_map::ConflictingLane& conflicting_lane : conflicting_lanes) {
    conflicting_lane_ids.insert(conflicting_lane.lane()->id());
  }

  bool has_conflicting_blockage = false;
  for (const auto& [object_id, planner_object] : planner_object_map) {
    if (!planner_object.is_vehicle()) {
      // Only consider vehicle as conflicting blockage.
      continue;
    }
    if (planner_object.is_stationary() ||
        planner_object.is_slow_moving_vehicle()) {
      // Only consider fast dynamic object as conflicting blockage.
      continue;
    }
    for (const pnc_map::Lane* lane : planner_object.associated_lanes()) {
      if (conflicting_lane_ids.find(lane->id()) != conflicting_lane_ids.end()) {
        // The object is associated to the a conflicting lane.
        // NOTE(Jiakai): No early return here to record all blockage objects for
        // debugging.
        has_conflicting_blockage |= true;
        uturn_unstuck_trigger_debug->mutable_conflicting_blockage_object_id()
            ->Add(object_id);
        break;
      }
    }
  }

  return has_conflicting_blockage;
}

bool IsUTurnBlockageObject(
    const PlannerObject& object,
    const lane_selection::LaneSequenceGeometry& lane_sequence_geometry,
    const speed::TrajectoryInfo& trajectory_info,
    const StuckSignalMetaData& stuck_signal_meta,
    pb::UTurnUnstuckBlockageDebug::NonUTurnUnstuckBlockageReason*
        non_uturn_unstuck_blockage_reason) {
  // TODO(Jiakai): Refactor the data flow here and use the information computed
  // in reasoning object of speed pipeline. IsPotentialUTurnBlockage().
  if (!object.is_stationary()) {
    // Only consider stationary object as blockage.
    *non_uturn_unstuck_blockage_reason =
        pb::UTurnUnstuckBlockageDebug::kNotStatic;
    return false;
  }
  const math::pb::Side side =
      lane_sequence_geometry.nominal_path.GetSide(object.center_2d());
  if (side != math::pb::Side::kRight) {
    // Only consider right-side object as blockage.
    *non_uturn_unstuck_blockage_reason =
        pb::UTurnUnstuckBlockageDebug::kNotOnRightSide;
    return false;
  }

  if (object.is_blockage_object()) {
    // Blockage object is always considered.
    return true;
  }

  // Checks if ego is determined as waiting in traffic by stuck signal.
  if (stuck_signal_meta.optional_is_ego_waiting_in_traffic.has_value() &&
      stuck_signal_meta.optional_is_ego_waiting_in_traffic.value() &&
      stuck_signal_meta.optional_stuck_object_id.has_value() &&
      object.id() == stuck_signal_meta.optional_stuck_object_id.value().id) {
    *non_uturn_unstuck_blockage_reason =
        pb::UTurnUnstuckBlockageDebug::kIsEgoWaitingInTraffic;
    return false;
  }

  // Check if object overlap with extended path. Do NOT consider objects whose
  // contours are not overlap with ego's path within a short lookahead horizon.
  const bool is_overlap_with_path =
      IsOverlapWithPath(trajectory_info, object.contour(),
                        kMaxArcLengthToCheckObjectOnPathInMeter,
                        kLateralDistBufferToCheckObjectOnPathInMeter);
  if (!is_overlap_with_path) {
    *non_uturn_unstuck_blockage_reason =
        pb::UTurnUnstuckBlockageDebug::kNotOverlapWithPath;
  }

  return is_overlap_with_path;
}

math::geometry::OrientedBox2d GenerateOrientedBoxByPathState(
    const open_space::PathState& path_state,
    const vehicle_model::CarModelWithAxleRectangularShape&
        car_model_with_shape) {
  const double length = car_model_with_shape.shape_measurement().length();
  const double width = car_model_with_shape.shape_measurement().width();
  const double geometry_center_to_rear_axle =
      car_model_with_shape.shape_measurement().geometry_center_to_rear_axle();

  const double box_center_x =
      path_state.position.x() +
      geometry_center_to_rear_axle * math::FastCos(path_state.heading);
  const double box_center_y =
      path_state.position.y() +
      geometry_center_to_rear_axle * math::FastSin(path_state.heading);
  return math::geometry::OrientedBox2d(
      {box_center_x, box_center_y}, length + kEgoBoxLengthBufferInMeter,
      width + kEgoBoxWidthBufferinMeter, path_state.heading);
}

bool AreAllBlockagesRemoved(
    const pb::UTurnUnstuckTriggerSeed& uturn_unstuck_trigger_seed,
    const SpeedWorldModel& world_model) {
  if (!uturn_unstuck_trigger_seed.has_trimmed_stuck_path()) {
    return false;
  }
  const math::geometry::PolylineCurve2d trimmed_stuck_path(
      uturn_unstuck_trigger_seed.trimmed_stuck_path());
  static constexpr double kRightSideExtraBufferInMeter = 4.0;
  const double ego_half_width = 0.5 * world_model.robot_state()
                                          .car_model_with_shape()
                                          .shape_measurement()
                                          .width();
  const math::geometry::PolygonWithCache2d estimated_stuck_path_corridor =
      math::geometry::Buffer(trimmed_stuck_path.polyline(), ego_half_width,
                             ego_half_width + kRightSideExtraBufferInMeter);
  for (const auto& pb_blockage :
       uturn_unstuck_trigger_seed.unstuck_unstuck_blockage()) {
    const auto& blockage_type = pb_blockage.blockage_type();
    if (blockage_type == pb::UTurnUnstuckBlockage::kPhysicalBoundary ||
        blockage_type == pb::UTurnUnstuckBlockage::kConstructionZone) {
      // Blockage of physical boundary or construction zone is never considered
      // to be removed.
      return false;
    }
    const auto planner_object_iter = world_model.planner_object_map().find(
        TypedObjectId(pb_blockage.blockage_object_id()).id);
    if (planner_object_iter == world_model.planner_object_map().end()) {
      // The blockage is not found in the planner object map of the current
      // frame. The object may be occluded or id jumps. Do not consider such
      // objects as removed.
      return false;
    }
    // Check whether there is overlap between the estimated trimmed path
    // corridor and object contour.
    if (math::geometry::Intersects(estimated_stuck_path_corridor.polygon(),
                                   planner_object_iter->second.contour())) {
      return false;
    }
  }
  // All blockages are removed from the original stuck path.
  return true;
}

bool GenerateUTurnUnstuckBlockageInfo(
    const SpeedWorldModel& world_model,
    const selection::TrajectoryMetaData& selected_trajectory_meta,
    bool should_ignore_hard_boundary,
    std::vector<UTurnUnstuckBlockageInfo>*
        potential_uturn_unstuck_blockage_info) {
  const auto& speed_result = selected_trajectory_meta.speed_result;
  const double ra_to_rear_bumper = world_model.robot_state()
                                       .car_model_with_shape()
                                       .shape_measurement()
                                       .rear_bumper_to_rear_axle();
  const double ra_to_front_bumper = world_model.robot_state()
                                        .car_model_with_shape()
                                        .shape_measurement()
                                        .length() -
                                    ra_to_rear_bumper;
  // NOTE(Jiakai): When the overlap_v2 is enabled, the start_x of constraint
  // state represents the distance from the stop line to front bumper rather
  // than the rear axis.
  const double front_bumper_adjust = ra_to_front_bumper;
  bool has_uturn_unstuck_blockage = false;

  // NOTE(Chester): Consider all constraints within a certain horizon as
  // potential uturn unstuck blockage.
  const std::vector<speed::Constraint> constraints = speed_result.constraints();
  for (size_t idx = 0; idx < constraints.size(); ++idx) {
    if (constraints[idx].states.front().start_x(
            speed_result.selected_discomfort()) >
        kMaxDistanceToBlockageInMeter - front_bumper_adjust) {
      // Ignore constraint out of the horizon.
      continue;
    }

    const speed::pb::FenceType fence_type = constraints[idx].fence_type;
    const bool is_yielding_to_persistent_object =
        fence_type == speed::pb::kLeftSideHardRoadBoundary ||
        fence_type == speed::pb::kRightSideHardRoadBoundary;
    if (is_yielding_to_persistent_object) {
      // TODO(Chester): Save the physical boundary polyline to blockage info.
      potential_uturn_unstuck_blockage_info->emplace_back(
          pb::UTurnUnstuckBlockage::kPhysicalBoundary,
          /*physical_boundary_polyline=*/std::nullopt,
          pb::UTurnUnstuckBlockageDebug::kIsUTurnUnstuckBlockage);
      has_uturn_unstuck_blockage = true;
      continue;
    }

    if (constraints[idx].type == speed::pb::Constraint::STOP_POINT ||
        constraints[idx].type == speed::pb::Constraint::NO_BLOCK ||
        constraints[idx].fence_type == speed::pb::kCrossWalkVRU ||
        speed_result.decisions()[idx] != speed::pb::YIELD) {
      // Skip non eligble constraint types and decisions.
      continue;
    }

    const TypedObjectId typed_blocking_object_id(
        constraints[idx].obj_id, constraints[idx].IsConstructionZone()
                                     ? pb::ObjectSourceType::kConstructionZone
                                     : pb::ObjectSourceType::kTrackedObject);
    if (typed_blocking_object_id.is_tracked_object()) {
      const auto& planner_object_map = world_model.planner_object_map();
      const auto planner_object_iter =
          planner_object_map.find(typed_blocking_object_id.id);
      if (planner_object_iter == planner_object_map.end()) {
        continue;
      }
      const PlannerObject& planner_object = planner_object_iter->second;
      // Only blockage object and stationary out-of-lane objects are considered
      // as u-turn blockage.
      pb::UTurnUnstuckBlockageDebug::NonUTurnUnstuckBlockageReason
          non_uturn_unstuck_blockage_reason =
              pb::UTurnUnstuckBlockageDebug::kIsUTurnUnstuckBlockage;
      has_uturn_unstuck_blockage |= IsUTurnBlockageObject(
          planner_object, selected_trajectory_meta.lane_sequence_geometry(),
          *selected_trajectory_meta.trajectory_info,
          selected_trajectory_meta.stuck_signal_meta,
          &non_uturn_unstuck_blockage_reason);

      // Skip adding non static constraint to potential blockage to aviod
      // saving excessive unnecessary dynamic constraint-related information
      // in debug.
      if (non_uturn_unstuck_blockage_reason !=
          pb::UTurnUnstuckBlockageDebug::kNotStatic) {
        potential_uturn_unstuck_blockage_info->emplace_back(
            pb::UTurnUnstuckBlockage::kAgent, typed_blocking_object_id,
            planner_object.contour(), non_uturn_unstuck_blockage_reason);
      }

    } else if (typed_blocking_object_id.is_construction_zone()) {
      // Uturn blockage is construction zone.
      const auto& construction_zone_ptr_map =
          world_model.construction_zone_ptr_map();
      const auto cz_iter =
          construction_zone_ptr_map.find(typed_blocking_object_id.id);
      if (cz_iter == construction_zone_ptr_map.end() ||
          cz_iter->second->IsMapLabeledCz()) {
        // Skips map labeled construction zone.
        continue;
      }
      potential_uturn_unstuck_blockage_info->emplace_back(
          pb::UTurnUnstuckBlockage::kConstructionZone, typed_blocking_object_id,
          cz_iter->second->contour,
          pb::UTurnUnstuckBlockageDebug::kIsUTurnUnstuckBlockage);
      has_uturn_unstuck_blockage = true;
    } else {
      DCHECK(false) << "Invalid typed object id";
      continue;
    }
  }

  if (!has_uturn_unstuck_blockage && !should_ignore_hard_boundary) {
    // If there is no valid constraint in range determined as UTurn Blockage,
    // check if ego's path ovelap with right hard boundary.
    std::optional<UTurnUnstuckBlockageInfo> blockage_info =
        CheckHardBoundaryIsUTurnBlockage(
            *selected_trajectory_meta.trajectory_info,
            selected_trajectory_meta.lane_sequence_geometry()
                .right_hard_boundary_lines,
            kMaxDistanceToBlockageInMeter);
    if (blockage_info.has_value()) {
      potential_uturn_unstuck_blockage_info->push_back(blockage_info.value());
      has_uturn_unstuck_blockage = true;
    }
  }

  return has_uturn_unstuck_blockage;
}

std::optional<UTurnUnstuckLaneInfo> GenerateUTurnUnstuckLaneInfo(
    const selection::TrajectoryMetaData& selected_trajectory_meta,
    const RobotStateSnapshot& robot_state_snapshot) {
  // Construct the u-turn lane sequence.
  const pnc_map::Lane* current_lane_from_routing =
      selected_trajectory_meta.current_lane();

  if (current_lane_from_routing == nullptr) {
    // No current lane found from routing.
    return std::nullopt;
  }

  std::vector<const pnc_map::Lane*> uturn_lane_sequence;
  if (IsStrictUTurnLane(current_lane_from_routing)) {
    uturn_lane_sequence = RetrieveEntireUTurnLaneSequenceByRouting(
        current_lane_from_routing, selected_trajectory_meta.lane_sequence());
  } else {
    const pnc_map::Lane* predecessor_uturn_lane = GetPredecessorUturnLane(
        current_lane_from_routing, selected_trajectory_meta.lane_sequence());
    if (predecessor_uturn_lane) {
      const math::geometry::PolylineCurve2d& uturn_lane_center_line =
          predecessor_uturn_lane->center_line();
      const double ego_arclength =
          uturn_lane_center_line
              .GetProximity(robot_state_snapshot.rear_axle_position(),
                            math::pb::UseExtensionFlag::kAllow)
              .arc_length;
      if (ego_arclength - uturn_lane_center_line.GetTotalArcLength() <
          kDistanceBufferToCheckEgoCloseToUTurnLaneInMeter) {
        // EGO is close to the predecessor uturn lane.
        uturn_lane_sequence = RetrieveEntireUTurnLaneSequenceByRouting(
            predecessor_uturn_lane, selected_trajectory_meta.lane_sequence());
      } else {
        // EGO is not close to the predecessor uturn lane.
        return std::nullopt;
      }
    } else {
      // Predecessor lane is not uturn lane.
      return std::nullopt;
    }
  }

  if (uturn_lane_sequence.empty()) {
    // No uturn lane sequence retrieved.
    return std::nullopt;
  }

  // Set related info.
  UTurnUnstuckLaneInfo lane_info;
  const pnc_map::Lane* uturn_start_lane = uturn_lane_sequence.front();
  const std::vector<const pnc_map::Lane*> predecessors =
      uturn_start_lane->predecessors();
  DCHECK(!predecessors.empty());
  // NOTE(Jiakai): Use the first predecessor, which should not really matter.
  lane_info.uturn_predecessor_lane = predecessors.at(0);
  lane_info.is_uturn_only_lane_sequence =
      lane_info.uturn_predecessor_lane->turn() == hdmap::Lane::U_TURN &&
      lane_info.uturn_predecessor_lane->type() == hdmap::Lane::REGULAR;

  const pnc_map::Lane* uturn_end_lane = uturn_lane_sequence.back();
  const std::vector<const pnc_map::Lane*> successors =
      uturn_end_lane->successors();
  DCHECK(!successors.empty());
  lane_info.uturn_successor_lane = successors.at(0);

  // Construct center line curve.
  lane_info.uturn_lane_center_line = lane_selection::GetLaneSequenceCurve(
      uturn_lane_sequence, lane_selection::LaneCurveType::kCenterLine,
      /*should_use_prior_path=*/false,
      /*need_center_line_smooth=*/false);

  // Compute max curvature.
  lane_info.max_curvature =
      math::geometry::GetCurvatureInfo(lane_info.uturn_lane_center_line)
          .max_absolute_curvature;

  lane_info.uturn_lane_sequence = std::move(uturn_lane_sequence);
  return lane_info;
}

std::optional<UTurnUnstuckLaneInfo> GenerateUTurnUnstuckLaneInfo(
    const pb::UTurnUnstuckTriggerSeed& uturn_unstuck_seed,
    const SpeedWorldModel& world_model) {
  if (!uturn_unstuck_seed.has_last_trigger_lane_id()) {
    return std::nullopt;
  }
  const int64_t last_trigger_lane_id =
      uturn_unstuck_seed.last_trigger_lane_id();

  const std::vector<const pnc_map::Lane*> queried_lane_vec =
      world_model.joint_pnc_map_service()->GetLaneSequence(
          {last_trigger_lane_id});
  DCHECK_EQ(queried_lane_vec.size(), 1);
  if (queried_lane_vec.empty()) {
    return std::nullopt;
  }

  const pnc_map::Lane* uturn_end_lane = queried_lane_vec[0];
  std::vector<const pnc_map::Lane*> uturn_lane_sequence =
      TraceConnectingUTurnLanes(uturn_end_lane, /*is_for_predecessor=*/true);
  uturn_lane_sequence.push_back(uturn_end_lane);

  // Set related info.
  UTurnUnstuckLaneInfo lane_info;
  const pnc_map::Lane* uturn_start_lane = uturn_lane_sequence.front();
  const std::vector<const pnc_map::Lane*> predecessors =
      uturn_start_lane->predecessors();
  DCHECK(!predecessors.empty());
  // NOTE(Jiakai): Use the first predecessor, which should not really matter.
  lane_info.uturn_predecessor_lane = predecessors.at(0);
  lane_info.is_uturn_only_lane_sequence =
      lane_info.uturn_predecessor_lane->turn() == hdmap::Lane::U_TURN &&
      lane_info.uturn_predecessor_lane->type() == hdmap::Lane::REGULAR;

  const std::vector<const pnc_map::Lane*> successors =
      uturn_end_lane->successors();
  DCHECK(!successors.empty());
  lane_info.uturn_successor_lane = successors.at(0);

  // Construct center line curve.
  lane_info.uturn_lane_center_line = lane_selection::GetLaneSequenceCurve(
      uturn_lane_sequence, lane_selection::LaneCurveType::kCenterLine,
      /*should_use_prior_path=*/false,
      /*need_center_line_smooth=*/false);

  // Compute max curvature.
  lane_info.max_curvature =
      math::geometry::GetCurvatureInfo(lane_info.uturn_lane_center_line)
          .max_absolute_curvature;

  lane_info.uturn_lane_sequence = std::move(uturn_lane_sequence);
  return lane_info;
}

}  // namespace unstuck

}  // namespace planner
