load("//bazel:defs.bzl", "voy_cc_test")

package(default_visibility = ["//visibility:public"])

voy_cc_test(
    name = "open_space_grid_cell_test",
    srcs = ["open_space_grid_cell_test.cpp"],
    deps = [
        "//onboard/common/math",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:grid_cell",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:glog",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "open_space_grid_test",
    srcs = ["open_space_grid_test.cpp"],
    deps = [
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/open_space/constraints:motion_plan_problem_manager",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:grid",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:glog",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "reeds_shepp_path_generator_test",
    srcs = ["reeds_shepp_path_generator_test.cpp"],
    deps = [
        "//offboard/third_party/matplotlibcpp",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:reeds_shepp_path_generator",
        "//onboard/planner/path/reasoning/agent_intention/agent_intention_searcher/test:agent_intention_searcher_test_utility",
        "//onboard/planner/world_model:robot_state",
        "//protobuf_cpp:protos_cpp",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "open_space_path_searcher_test",
    srcs = ["open_space_path_searcher_test.cpp"],
    deps = [
        "//offboard/third_party/matplotlibcpp",
        "//onboard/common/base:voy_base",
        "//onboard/common/math",
        "//onboard/common/voy_protos:proto_util",
        "//onboard/planner/behavior/util/lane_sequence/test:utility_lane_sequence_test_utility",
        "//onboard/planner/behavior/util/lane_sequence_geometry",
        "//onboard/planner/open_space/constraints:motion_plan_problem_manager",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher",
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:open_space_path_search_result",
        "//onboard/planner/world_model:planner_object",
        "//onboard/planner/world_model:robot_state",
        "//protobuf_cpp:protos_cpp",
        "@com_google_protobuf//:protobuf",
        "@voy-sdk//:gtest",
    ],
)

voy_cc_test(
    name = "arc_path_2d_test",
    srcs = ["arc_path_2d_test.cpp"],
    deps = [
        "//onboard/planner/open_space/motion_plan_generator/open_space_path_searcher:arc_path_2d",
        "@voy-sdk//:gtest",
    ],
)
