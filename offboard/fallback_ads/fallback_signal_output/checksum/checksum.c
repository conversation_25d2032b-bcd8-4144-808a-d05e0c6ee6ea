#include "fallback_signal_output/checksum/checksum.h"
#include <math.h>
#include <stdio.h>
uint8 crc_cal(uint8 *buf) {
  uint8 crc = 0xff;
  uint8 poly = 0x2f;
  uint8 j, i;
  for (i = 0; i < 7; i++) {
    crc ^= buf[i];
    for (j = 0; j < 8; j++) {
      if (crc & 0x80)
        crc = (crc << 1) ^ poly;
      else
        crc <<= 1;
    }
  }
  return ~crc;
}
uint8 crc_cal_YRS(uint8 *buf) {
  uint8 crc = 0xff;
  uint8 poly = 0x2f;
  uint8 j, i;
  for (i = 0; i < 8; i++) {
    if (0 == i) {
      continue;
    }
    crc ^= buf[i];
    for (j = 0; j < 8; j++) {
      if (crc & 0x80)
        crc = (crc << 1) ^ poly;
      else
        crc <<= 1;
    }
  }
  return ~crc;
}
uint8 crc_cal_SAS_Status(uint8 *buf) {
  uint8 crc = 0xff;
  uint8 poly = 0x2f;
  uint8 j, i;
  for (i = 0; i < 8; i++) {
    if (5 == i) {
      continue;
    }

    crc ^= buf[i];
    for (j = 0; j < 8; j++) {
      if (crc & 0x80)
        crc = (crc << 1) ^ poly;
      else
        crc <<= 1;
    }
  }
  return ~crc;
}
