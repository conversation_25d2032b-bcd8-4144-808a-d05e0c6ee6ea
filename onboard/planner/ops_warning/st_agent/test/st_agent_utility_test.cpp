#include "planner/ops_warning/st_agent/st_agent_utility.h"

#include <vector>

#include <gtest/gtest.h>

#include "geometry/model/point_2d.h"
#include "geometry/model/polyline_curve.h"
#include "planner/constants.h"
#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/ops_warning/st_agent/test/st_agent_test_util.h"
#include "prediction/common/definition/prediction_constants.h"

namespace planner {
namespace ops_warning {
namespace {

constexpr double kVehicleWidthInMeter = 1.864;
constexpr double kVehicleLengthInMeter = 4.925;
constexpr double kVehicleLVelocity = 10.0;

}  // namespace

TEST(STAgentUtilityTest, TransformSTAgentParam) {
  ::prediction::pb::Agent agent_proto;
  voy::TrackedObject& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  tracked_object.set_width(kVehicleWidthInMeter);
  tracked_object.set_length(kVehicleLengthInMeter);
  tracked_object.set_heading(0.0);

  ::prediction::pb::PredictedTrajectory& proto_traj =
      *agent_proto.add_predicted_trajectories();
  proto_traj.set_id(1);
  proto_traj.set_is_output_trajectory(true);
  proto_traj.set_probability_in_ppm(
      prediction::constants::kOutputTrajectoryWithoutBackupProbability);
  double init_x = -10.0;
  const auto resolution_s =
      math::Ms2Sec(constants::kTrajectoryPoseResolutionInMSec);
  double init_t = 0.0;
  while (init_t < 6.0) {
    pb::TrajectoryPose& proto_traj_pose = *proto_traj.add_traj_poses();
    proto_traj_pose.set_timestamp(math::Sec2Ms(init_t) + 0);
    proto_traj_pose.set_x_pos(init_x);
    proto_traj_pose.set_y_pos(0.0);
    proto_traj_pose.set_heading(0.0);
    proto_traj_pose.set_speed(kVehicleLVelocity);
    proto_traj_pose.set_accel(0.0);
    init_x += kVehicleLVelocity * resolution_s;
    init_t += resolution_s;
  }

  PredictedTrajectoryWrapper trajectory(agent_proto,
                                        agent_proto.predicted_trajectories(0));

  STAgentParam param = TransformSTAgentParam(trajectory);

  EXPECT_DOUBLE_EQ(kVehicleWidthInMeter, param.width);
  EXPECT_DOUBLE_EQ(kVehicleLengthInMeter, param.length);
  const PredictedPose& first_predicted_pose =
      trajectory.predicted_poses(/*use_legacy_logic=*/true).front();
  const pb::TrajectoryPose& first_pose = *param.trajectory.poses().begin();
  EXPECT_DOUBLE_EQ(first_predicted_pose.center().x(), first_pose.x_pos());
  EXPECT_DOUBLE_EQ(first_predicted_pose.center().y(), first_pose.y_pos());
  EXPECT_DOUBLE_EQ(first_predicted_pose.speed(), first_pose.speed());

  const PredictedPose& back_predicted_pose =
      trajectory.predicted_poses(/*use_legacy_logic=*/true).back();
  const pb::TrajectoryPose& back_pose = *param.trajectory.poses().rbegin();
  EXPECT_DOUBLE_EQ(back_predicted_pose.center().x(), back_pose.x_pos());
  EXPECT_DOUBLE_EQ(back_predicted_pose.center().y(), back_pose.y_pos());
  EXPECT_DOUBLE_EQ(back_predicted_pose.speed(), back_pose.speed());
}

TEST(STAgentUtilityTest, GetSampledOdomsByConstAcc) {
  // Test const speed.
  const std::vector<OdomSampleInfo> const_speed_info = {{1100, 0.0}};
  std::vector<double> result =
      GetSampledOdomsByConstAcc(/*speed=*/10.0, const_speed_info,
                                /*resolution_ms=*/100, /*odom_offset=*/0);
  EXPECT_EQ(12, result.size());
  EXPECT_DOUBLE_EQ(11, result.back());

  // Test odom offset.
  result = GetSampledOdomsByConstAcc(/*speed=*/10.0, const_speed_info,
                                     /*resolution_ms=*/100, /*odom_offset=*/10);
  EXPECT_DOUBLE_EQ(10, result.front());
  EXPECT_DOUBLE_EQ(21, result.back());

  // Test zero resolution.
  EXPECT_DEATH(
      GetSampledOdomsByConstAcc(/*speed=*/10.0, const_speed_info,
                                /*resolution_ms=*/0, /*odom_offset=*/0),
      "");

  // Test const acceleration.
  const std::vector<OdomSampleInfo> const_acc_info = {{1100, 1.0}};
  result = GetSampledOdomsByConstAcc(/*speed=*/10.0, const_acc_info,
                                     /*resolution_ms=*/100, /*odom_offset=*/0);
  EXPECT_EQ(12, result.size());
  EXPECT_DOUBLE_EQ(11.605, result.back());

  // Test const deceleration.
  const std::vector<OdomSampleInfo> const_dec_info = {{1100, -1.0}};
  result = GetSampledOdomsByConstAcc(/*speed=*/10.0, const_dec_info,
                                     /*resolution_ms=*/100, /*odom_offset=*/0);
  EXPECT_EQ(12, result.size());
  EXPECT_DOUBLE_EQ(10.395, result.back());

  // Test const large deceleration.
  const std::vector<OdomSampleInfo> const_large_dec_info = {{1100, -20.0}};
  result = GetSampledOdomsByConstAcc(/*speed=*/10.0, const_large_dec_info,
                                     /*resolution_ms=*/100, /*odom_offset=*/0);
  EXPECT_EQ(12, result.size());
  EXPECT_DOUBLE_EQ(2.5, result.back());
}

TEST(STAgentUtilityTest, InterpolatePosesByOdom) {
  pb::Trajectory trajectory = ConstructTrajectory(/*v=*/10.0);
  std::vector<double> sampled_odoms = {0.0};

  // Transform trajectory pose to 2d points.
  std::vector<math::geometry::Point2d> trajectory_points;
  trajectory_points.reserve(trajectory.poses().size());
  std::transform(trajectory.poses().begin(), trajectory.poses().end(),
                 std::back_inserter(trajectory_points),
                 [](const pb::TrajectoryPose& pose) -> math::geometry::Point2d {
                   return math::geometry::Point2d(pose.x_pos(), pose.y_pos());
                 });
  // Creates curves with trajectory points.
  const math::geometry::PolylineCurve2d trajectory_curve(
      std::move(trajectory_points));

  // Samples trajectory pose according sampled odom.
  std::vector<pb::TrajectoryPose> result =
      InterpolatePosesByOdom(trajectory_curve, sampled_odoms);
  EXPECT_DOUBLE_EQ(0.0, result.front().x_pos());

  sampled_odoms = {50.0};
  result = InterpolatePosesByOdom(trajectory_curve, sampled_odoms);
  EXPECT_NEAR(50.0, result.front().x_pos(), 1e-6);

  sampled_odoms = {0.1, 0.3};
  result = InterpolatePosesByOdom(trajectory_curve, sampled_odoms);
  EXPECT_DOUBLE_EQ(0.1, result.front().x_pos());
  EXPECT_DOUBLE_EQ(0.3, result.back().x_pos());
}

}  // namespace ops_warning
}  // namespace planner
