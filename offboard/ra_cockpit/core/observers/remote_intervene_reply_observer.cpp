#include "remote_intervene_reply_observer.h"

#include <utility>

#include "av_comm/topics.h"
#include "glog/logging.h"

namespace remote_assist {

RemoteInterveneReplyObserver::RemoteInterveneReplyObserver(
    RemoteInterveneReplyCallback callback)
    : remote_intervene_reply_callback_(std::move(callback)) {}

bool RemoteInterveneReplyObserver::IsInterestedIn(
    const teleassist::pb::TeleassistRequest& message) {
  return (message.message_type() == kRemoteInterveneReply);
}

std::shared_ptr<google::protobuf::Message>
RemoteInterveneReplyObserver::OnMessage(
    const teleassist::pb::TeleassistRequest& message,
    DataChannel data_channel) {
  auto remote_intervene_reply =
      std::make_shared<teleassist::pb::RemoteInterveneReply>();
  if (!remote_intervene_reply->ParseFromString(message.message_data())) {
    LOG(ERROR) << "Received error RemoteInterveneReply message format, len = "
               << message.message_data().length();
    return nullptr;
  }
  if (remote_intervene_reply_callback_) {
    remote_intervene_reply_callback_(remote_intervene_reply, data_channel);
  }
  return remote_intervene_reply;
}

}  // namespace remote_assist
