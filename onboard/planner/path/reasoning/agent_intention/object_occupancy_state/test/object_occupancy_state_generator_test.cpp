#include "planner/path/reasoning/agent_intention/object_occupancy_state/object_occupancy_state_generator.h"

#include <memory>

#include <gtest/gtest.h>

#include "planner/decoupled_maneuvers/predicted_trajectory_wrapper/predicted_trajectory_wrapper.h"
#include "planner/path/reasoning/agent_intention/agent_intention_searcher/test/test_utility.h"
#include "prediction/concept/trajectory/predicted_trajectory.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

namespace {
constexpr int64_t kInitialTimestamp = 1000;  // ms.
const int kMockNumPoses = 80;

prediction::pb::PredictedTrajectory MockPredictedTrajectory(
    double initial_speed, double speed_jump, double acc,
    const std::vector<double>& headings) {
  prediction::PredictedTrajectory predicted_trajectory;
  std::vector<planner::pb::TrajectoryPose> poses(kMockNumPoses);

  double x = 0, y = 0, v = initial_speed + speed_jump;
  poses[0].set_timestamp(kInitialTimestamp);
  poses[0].set_x_pos(x);
  poses[0].set_y_pos(y);
  poses[0].set_speed(initial_speed);
  poses[0].set_heading(headings[0]);
  poses[0].set_odom(0.0);

  for (size_t i = 1; i < kMockNumPoses; i++) {
    x += v * std::cos(headings[i]) * prediction::constants::kPoseIntervalInSec;
    y += v * std::sin(headings[i]) * prediction::constants::kPoseIntervalInSec;
    poses[i].set_timestamp(kInitialTimestamp +
                           i * prediction::constants::kPoseIntervalInMs);
    poses[i].set_x_pos(x);
    poses[i].set_y_pos(y);
    poses[i].set_speed(v);
    poses[i].set_heading(headings[i]);
    poses[i].set_odom(poses[i - 1].odom() +
                      v * prediction::constants::kPoseIntervalInSec);
    v += acc * prediction::constants::kPoseIntervalInSec;
  }
  predicted_trajectory.set_id(/*id=*/1);
  predicted_trajectory.SetTrajectoryPoses(std::move(poses));
  predicted_trajectory.set_score(1.0);
  predicted_trajectory.set_trajectory_generator_type(
      prediction::pb::TrajectoryGeneratorType::JOINT_DL_GENERATOR);
  return predicted_trajectory.ToProto();
}

class ObjectOccupancyStateGeneratorTest : public ::testing::Test {
 protected:
  void SetUp() override {
    auto& tracked_object = *agent_proto_.mutable_tracked_object();
    tracked_object.set_object_type(voy::perception::ObjectType::VEHICLE);
    tracked_object.set_id(1);
    tracked_object.set_center_x(5.0);
    tracked_object.set_length(4.2);
    tracked_object.set_width(1.9);
    tracked_object.set_height(1.2);
    tracked_object.set_velocity(10.0);
    const int64_t timestamp = 20;
    const double initial_speed = 5.0;
    const double heading = M_PI / 4.0;
    const double acc = 4.0;
    // Dummy pose at plan init ts.
    const TrafficParticipantPose pose_at_plan_init_ts_(
        timestamp, agent_proto_.tracked_object());
    planner_object_ = std::make_unique<PlannerObject>(agent_proto_, timestamp,
                                                      pose_at_plan_init_ts_);
    const std::vector<double> headings(kMockNumPoses, heading);
    primary_predicted_trajectory_proto_ = MockPredictedTrajectory(
        initial_speed, /*speed_jump=*/0.0, acc, headings);
    secondary_predicted_trajectory_proto_ = primary_predicted_trajectory_proto_;
    secondary_predicted_trajectory_proto_.set_id(2);
    primary_predicted_single_trajectory_wrapper_ =
        std::make_unique<PredictedTrajectoryWrapper>(
            voy::TrackedObject(), primary_predicted_trajectory_proto_,
            /*is_primary_trajectory=*/true);
    secondary_predicted_single_trajectory_wrapper_ =
        std::make_unique<PredictedTrajectoryWrapper>(
            voy::TrackedObject(), secondary_predicted_trajectory_proto_,
            /*is_primary_trajectory=*/true);
  }

  prediction::pb::Agent agent_proto_;
  const int64_t timestamp = 0;
  // Dummy pose at plan init ts.
  const TrafficParticipantPose pose_at_plan_init_ts_;
  std::unique_ptr<PlannerObject> planner_object_;
  std::unique_ptr<PredictedTrajectoryWrapper>
      primary_predicted_single_trajectory_wrapper_;
  std::unique_ptr<PredictedTrajectoryWrapper>
      secondary_predicted_single_trajectory_wrapper_;
  EgoInLaneParams ego_params_;
  prediction::pb::PredictedTrajectory primary_predicted_trajectory_proto_;
  prediction::pb::PredictedTrajectory secondary_predicted_trajectory_proto_;
};
}  // namespace

TEST_F(ObjectOccupancyStateGeneratorTest, SingleObjectBasicTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);

  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrapper;
  predicted_trajectory_wrapper.push_back(
      *primary_predicted_single_trajectory_wrapper_);

  ObjectOccupancyState object_occupancy_state = GenerateObjectOccupancyState(
      *planner_object_, /*object_information=*/{}, predicted_trajectory_wrapper,
      nominal_path_curve, left_boundary_curve, right_boundary_curve,
      ego_params_, pb::MotionMode::FORWARD);
  EXPECT_EQ(object_occupancy_state.object_id(), 1);
  EXPECT_EQ(object_occupancy_state.planner_object().speed(), 10.0);
  EXPECT_EQ(object_occupancy_state.predicted_trajectory_occupancy_states()
                .at(object_occupancy_state.primary_trajectory_id())
                .predicted_states()
                .size(),
            80);
  EXPECT_GT(object_occupancy_state.current_snapshot_info()
                .object_occupancy_param()
                .full_body_end_arclength_m,
            0);
}

TEST_F(ObjectOccupancyStateGeneratorTest,
       ObjectOccupancyStateMapGeneratorTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);

  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrapper;
  predicted_trajectory_wrapper.push_back(
      *primary_predicted_single_trajectory_wrapper_);
  object_prediction_map.emplace(planner_object_->id(),
                                predicted_trajectory_wrapper);
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  planner_object_map.emplace(planner_object_->id(), *planner_object_);
  auto plan_init_object_info_map = GeneratePlanInitTsObjectInfoMap(
      planner_object_map, ego_params_, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, pb::MotionMode::FORWARD);
  std::unordered_map<TypedObjectId, ObjectInformation>
      interested_planner_object_map;
  ObjectOccupancyStateMap object_occupancy_state_map =
      GenerateObjectOccupancyStateMap(
          planner_object_map, object_prediction_map, nominal_path_curve,
          left_boundary_curve, right_boundary_curve, ego_params_,
          interested_planner_object_map, std::move(plan_init_object_info_map),
          pb::MotionMode::FORWARD);
  EXPECT_EQ(object_occupancy_state_map.size(), 1);
  EXPECT_GT(object_occupancy_state_map[planner_object_->id()]
                ->current_snapshot_info()
                .object_occupancy_param()
                .full_body_end_arclength_m,
            0);
}

TEST_F(ObjectOccupancyStateGeneratorTest, StaticAgentInLaneStateMatchingTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  DrivingCorridor lane_corridor;
  lane_corridor.right_boundary = right_boundary_curve;
  lane_corridor.left_boundary = left_boundary_curve;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      test::ConstructLaneSequenceGeometry(nominal_path_curve, lane_corridor);
  AgentInLaneStatesMap agent_state_map;
  math::geometry::Polygon2d agent_contour(
      planner_object_->pose().contour().points(), false);
  // Construct static agent inlane states.
  test::ConstructStaticAgentInLaneState(lane_sequence_geometry,
                                        planner_object_->id(), agent_contour,
                                        agent_state_map);

  // Construct object occupancy state map.
  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrapper;
  predicted_trajectory_wrapper.push_back(
      *primary_predicted_single_trajectory_wrapper_);
  // Construct a tracked_object that matches the one in the AgentInLaneState
  // test function.
  prediction::pb::Agent agent_proto;
  auto& tracked_object = *agent_proto.mutable_tracked_object();
  tracked_object.set_id(planner_object_->id());
  tracked_object.add_attributes(voy::perception::HAS_EXACT_SHAPE);
  math::geometry::Convert(agent_contour, *tracked_object.mutable_contour());
  // Construct the dummy planner object.
  const PlannerObject dummy_planner_object(
      agent_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, tracked_object));

  object_prediction_map.emplace(dummy_planner_object.id(),
                                predicted_trajectory_wrapper);
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  planner_object_map.emplace(dummy_planner_object.id(), dummy_planner_object);
  auto plan_init_object_info_map = GeneratePlanInitTsObjectInfoMap(
      planner_object_map, ego_params_, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, pb::MotionMode::FORWARD);
  std::unordered_map<TypedObjectId, ObjectInformation>
      interested_planner_object_map;
  ObjectOccupancyStateMap object_occupancy_state_map =
      GenerateObjectOccupancyStateMap(
          planner_object_map, object_prediction_map, nominal_path_curve,
          left_boundary_curve, right_boundary_curve, ego_params_,
          interested_planner_object_map, std::move(plan_init_object_info_map),
          pb::MotionMode::FORWARD);
  std::vector<int64_t> object_ids{dummy_planner_object.id()};

  // Compare the parameters in two structures.
  EXPECT_TRUE(test::IsAgentInLaneStateMapEquivalentToObjectOccupancyStateMap(
      agent_state_map, object_occupancy_state_map, object_ids));
}

TEST_F(ObjectOccupancyStateGeneratorTest, DynamicAgentInLaneStateMatchingTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);
  DrivingCorridor lane_corridor;
  lane_corridor.right_boundary = right_boundary_curve;
  lane_corridor.left_boundary = left_boundary_curve;
  lane_selection::LaneSequenceGeometry lane_sequence_geometry =
      test::ConstructLaneSequenceGeometry(nominal_path_curve, lane_corridor);
  AgentInLaneStatesMap agent_state_map;
  // Construct dynamic agent inlane states.
  test::ConstructDynamicAgentInLaneState(
      lane_sequence_geometry, /*object_id=*/1, /*x0=*/1.0, /*y0=*/1.0,
      planner_object_->width(), /*speed=*/1.0, agent_state_map);

  // Construct object occupancy state map.
  tbb::concurrent_unordered_map<ObjectId,
                                std::vector<PredictedTrajectoryWrapper>>
      object_prediction_map;
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  prediction::pb::Agent agent_proto;
  agent_proto.mutable_tracked_object()->CopyFrom(
      test::ConstructTrackedObjectFromDimension(
          /*object_id=*/1, /*x0=*/1.0, /*y0=*/1.0, planner_object_->width(),
          /*length=*/2.0, /*height=*/2.0, /*velocity=*/1.0, /*heading=*/0.0));
  prediction::pb::PredictedTrajectory proto_traj;
  const auto predicted_trajectory_wrapper =
      test::ConstructPredictedTrajectoryWrapperForTest(
          agent_proto.tracked_object(), proto_traj);
  predicted_trajectory_wrappers.push_back(
      std::move(predicted_trajectory_wrapper));
  const PlannerObject dummy_planner_object(
      agent_proto, /*timestamp=*/1,
      TrafficParticipantPose(/*timestamp=*/1, agent_proto.tracked_object()));
  object_prediction_map.emplace(dummy_planner_object.id(),
                                predicted_trajectory_wrappers);
  std::unordered_map<ObjectId, PlannerObject> planner_object_map;
  planner_object_map.emplace(dummy_planner_object.id(), dummy_planner_object);
  auto plan_init_object_info_map = GeneratePlanInitTsObjectInfoMap(
      planner_object_map, ego_params_, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, pb::MotionMode::FORWARD);
  std::unordered_map<TypedObjectId, ObjectInformation>
      interested_planner_object_map;
  ObjectOccupancyStateMap object_occupancy_state_map =
      GenerateObjectOccupancyStateMap(
          planner_object_map, object_prediction_map, nominal_path_curve,
          left_boundary_curve, right_boundary_curve, ego_params_,
          interested_planner_object_map, std::move(plan_init_object_info_map),
          pb::MotionMode::FORWARD);
  std::vector<int64_t> object_ids{dummy_planner_object.id()};
  // Compare the parameters in two structures.
  EXPECT_TRUE(test::IsAgentInLaneStateMapEquivalentToObjectOccupancyStateMap(
      agent_state_map, object_occupancy_state_map, object_ids));
  // Compare the predicted trajectories.
  const auto& agent_trajectory_inlane_states =
      agent_state_map[dummy_planner_object.id()]->predicted_trajectories;
  const auto& predicted_trajectory_occupancy_states =
      object_occupancy_state_map[dummy_planner_object.id()]
          ->predicted_trajectory_occupancy_states();
  EXPECT_EQ(agent_trajectory_inlane_states.size(),
            predicted_trajectory_occupancy_states.size());
  for (const auto& [id, predicted_trajectory_occupancy_state] :
       predicted_trajectory_occupancy_states) {
    EXPECT_EQ(
        predicted_trajectory_occupancy_states.at(id).predicted_states().size(),
        agent_trajectory_inlane_states.at(id).predicted_states.size());
    for (size_t j = 0;
         j <
         predicted_trajectory_occupancy_states.at(id).predicted_states().size();
         ++j) {
      EXPECT_TRUE(test::IsAgentInLaneParamEquivalentToObjectOccupancyParam(
          agent_trajectory_inlane_states.at(id)
              .predicted_states[j]
              .inlane_state.inlane_param,
          predicted_trajectory_occupancy_states.at(id)
              .predicted_states()[j]
              .object_occupancy_param()));
      EXPECT_EQ(
          agent_trajectory_inlane_states.at(id).predicted_states[j].timestamp,
          predicted_trajectory_occupancy_states.at(id)
              .predicted_states()[j]
              .timestamp());
    }
  }
}

TEST_F(ObjectOccupancyStateGeneratorTest, EgoParamRelatedFieldsTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 6.0}, {10.0, 6.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);

  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  predicted_trajectory_wrappers.push_back(
      *primary_predicted_single_trajectory_wrapper_);

  ObjectOccupancyState object_occupancy_state = GenerateObjectOccupancyState(
      *(planner_object_.get()), /*object_information=*/{},
      predicted_trajectory_wrappers, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, ego_params_, pb::MotionMode::FORWARD);
  EXPECT_FALSE(
      object_occupancy_state.current_snapshot_info().is_fully_behind_ego());
  EXPECT_TRUE(object_occupancy_state.predicted_trajectory_occupancy_states()
                  .at(object_occupancy_state.primary_trajectory_id())
                  .is_crossing_prediction());

  EgoInLaneParams ego_params;
  ego_params.arclength_m = 10;
  ObjectOccupancyState object_occupancy_state_2 = GenerateObjectOccupancyState(
      *(planner_object_.get()), /*object_information=*/{},
      predicted_trajectory_wrappers, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, ego_params, pb::MotionMode::FORWARD);
  EXPECT_TRUE(
      object_occupancy_state_2.current_snapshot_info().is_fully_behind_ego());
}

TEST_F(ObjectOccupancyStateGeneratorTest, PrimaryPredictionTest) {
  const std::vector<math::geometry::Point2d> nominal_path_points = {
      {0.0, 0.0}, {10.0, 0.0}};
  const math::geometry::PolylineCurve2d nominal_path_curve(nominal_path_points);
  const math::Range1d driving_arclength_range(0.0, 10.0);
  const std::vector<math::geometry::Point2d> left_boundary_points = {
      {0.0, 3.0}, {10.0, 3.0}};
  const math::geometry::PolylineCurve2d left_boundary_curve(
      left_boundary_points);
  const std::vector<math::geometry::Point2d> right_boundary_points = {
      {0.0, -3.0}, {10.0, -3.0}};
  const math::geometry::PolylineCurve2d right_boundary_curve(
      right_boundary_points);

  // For current use, only consider the primary prediction, so the predicted
  // trajectory size should be one.
  std::vector<PredictedTrajectoryWrapper> predicted_trajectory_wrappers;
  predicted_trajectory_wrappers.push_back(
      *primary_predicted_single_trajectory_wrapper_);
  predicted_trajectory_wrappers.push_back(
      *secondary_predicted_single_trajectory_wrapper_);
  ObjectOccupancyState object_occupancy_state = GenerateObjectOccupancyState(
      *planner_object_, /*object_information=*/{},
      predicted_trajectory_wrappers, nominal_path_curve, left_boundary_curve,
      right_boundary_curve, ego_params_, pb::MotionMode::FORWARD);
  EXPECT_EQ(
      object_occupancy_state.predicted_trajectory_occupancy_states().size(), 2);
  EXPECT_EQ(object_occupancy_state.predicted_trajectory_occupancy_states()
                .at(object_occupancy_state.primary_trajectory_id())
                .trajectory_id(),
            1);
}
}  // namespace planner
