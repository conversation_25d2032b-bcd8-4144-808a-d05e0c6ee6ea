#ifndef ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_COMMON_DATA_TYPES_HPP_
#define ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_COMMON_DATA_TYPES_HPP_
#include <cstdint>
#include <list>
#include <map>
#include <memory>
#include <utility>
#include <vector>

#include <Eigen/Core>
#include <Eigen/Dense>

#include "slam_protos/online_map_tracking.pb.h"

namespace slam {
namespace semantic_mapping {
namespace road_cognition {

enum class ExistenceStatus : uint8_t {
  Unknown = 1,
  Confirmed = 2,
  Removed = 3,
  Virtual = 4,  // Not used yet
};

enum class ObsPointType : uint8_t {
  Unknown = 0,
  Fixed = 1,
  Current = 2,
  Extend = 3,
};

enum class ElementDirection {
  Unknown = 0,
  Forward = 1,
  Backward = 2,
  Left = 3,
  Right = 4,
};

struct ObsPoint {
  ObsPoint() = default;
  ObsPoint(double raw_index, double rmse, Eigen::Vector3d pt_w)
      : raw_index(raw_index), rmse(rmse), pt(std::move(pt_w)) {}
  ObsPoint(double raw_index, double x, double y, double rmse,
           double observe_angle, double width, int64_t timestamp,
           Eigen::Vector3d pt_w)
      : raw_index(raw_index),
        obs_x(x),
        obs_y(y),
        rmse(rmse),
        width(width),
        observe_angle(observe_angle),
        timestamp(timestamp),
        pt(std::move(pt_w)) {}

  double raw_index{0.0};  // Raw index of observed point.

  double obs_x{0.0};                         // observation dist of x.
  double obs_y{0.0};                         // observation dist of y.
  double rmse{0.0};                          // rmse of observed point.
  double width{0.0};                         // width of fence, unit: m
  double observe_angle{0.0};                 // observation angle, unit: rad
  int64_t timestamp{0};                      // timestamp unit:ms
  ObsPointType type{ObsPointType::Unknown};  // Observation type.

  Eigen::Vector3d pt{Eigen::Vector3d::Zero()};  // geometry

  ExistenceStatus existence_status{
      ExistenceStatus::Unknown};  // Only for raw obs points

  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};
using ObsPointPtr = std::shared_ptr<ObsPoint>;

struct LaneBoundarySingle {
  std::map<double, ObsPoint> raw_obs_pts;  // <index, ObsPoint>

  int64_t timestamp{0};

  int type{0};  // OnlineMapElementType::Type

  int64_t track_id{0};  // OnlineMapElementType::track_id

  uint64_t id{0};  // AssignID

  double index{0.0};  // Index of start point.

  double confidence{0.0};  // Property: confidence

  std::vector<std::pair<int, double>>
      attr;  // <MapElementAttribute::Attr,MapElementAttribute::Confidence>

  ElementDirection direction{ElementDirection::Forward};

  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};
using LaneBoundarySinglePtr = std::shared_ptr<LaneBoundarySingle>;

struct ReconBoundary {
  std::map<double, ObsPoint> points;  // <index, ObsPoint>

  // The property of element
  int64_t track_id{0};  // The original track id from online map

  uint64_t id{0};  // The id of map

  int type{0};  // OnlineMapElementType::Type

  double index{0.0};  // The distance infomation

  double confidence{0.0};  // The average confidence of merged boundary

  std::vector<std::pair<int, double>>
      attr;  // <MapElementAttribute::Attr,MapElementAttribute::Confidence>

  size_t obs_times{0};

  ElementDirection direction{ElementDirection::Forward};

  EIGEN_MAKE_ALIGNED_OPERATOR_NEW
};
using ReconBoundaryPtr = std::shared_ptr<ReconBoundary>;

struct TypeUtil {
 private:
  TypeUtil() = default;

 public:
  static inline bool IsHardBoundary(
      const slam::proto::OnlineMapElementType::Type& type) {
    return type == slam::proto::OnlineMapElementType::CURB ||
           type == slam::proto::OnlineMapElementType::FENCE;
  }

  static inline bool IsPolylineType(
      const slam::proto::OnlineMapElementType::Type& type) {
    return type == slam::proto::OnlineMapElementType::CURB ||
           type == slam::proto::OnlineMapElementType::FENCE ||
           type == slam::proto::OnlineMapElementType::LANE_CENTER_LINE ||
           type == slam::proto::OnlineMapElementType::LANE_MARKING;
  }

  static inline bool IsFence(
      const slam::proto::OnlineMapElementType::Type& type) {
    return type == slam::proto::OnlineMapElementType::FENCE;
  }
};
}  // namespace road_cognition
}  // namespace semantic_mapping
}  // namespace slam
#endif  // ONBOARD_MAPPING_SEMANTIC_MAPPING_ROAD_COGNITION_COMMON_DATA_TYPES_HPP_
