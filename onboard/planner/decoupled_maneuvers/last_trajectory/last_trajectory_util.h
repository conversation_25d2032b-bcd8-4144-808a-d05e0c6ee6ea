#ifndef ONBOARD_PLANNER_DECOUPLED_MANEUVERS_LAST_TRAJECTORY_LAST_TRAJECTORY_UTIL_H_
#define ONBOARD_PLANNER_DECOUPLED_MANEUVERS_LAST_TRAJECTORY_LAST_TRAJECTORY_UTIL_H_

#include <vector>

#include "planner/behavior/util/lane_sequence/decoupled_lane_sequence_info.h"
#include "planner/speed/profile/profile.h"
#include "planner/world_model/world_model.h"
#include "planner_protos/behavior_reasoner_seed.pb.h"
#include "planner_protos/speed_generator_config.pb.h"
#include "voy_protos/trajectory.pb.h"

namespace planner {

// Returns true if we can reuse the last path.
// prerequisite: no replan in init state, same Maneuver, same LaneSequence.
bool CanReuseLastPath(
    const WorldModel& world_model,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const std::vector<const pnc_map::Lane*>& curr_lane_sequence,
    pb::BehaviorType behavior_type);

// Truncates last path to make its first pose starting from proximal point of
// init state and returns the path if exists. If this init state diverges too
// much from last path, return nullopt.
std::optional<pb::Path> TruncateLastPath(
    const pb::Path& last_path,
    const RobotStateSnapshot& plan_init_state_snapshot,
    bool is_last_iter_lane_change);

// Generates warm start speed profile from last trajectory, which starts from
// the current iteration's planning initial state time.
std::optional<speed::Profile> GenerateWarmStartSpeedProfile(
    const RobotState& robot_state, pb::ManeuverType last_selected_maneuver_type,
    const pb::DecoupledManeuverSeed& previous_iter_seed,
    const speed::pb::SpeedGeneratorConfig& speed_config,
    bool check_speed_accel_error = true);

}  // namespace planner

#endif  // ONBOARD_PLANNER_DECOUPLED_MANEUVERS_LAST_TRAJECTORY_LAST_TRAJECTORY_UTIL_H_
