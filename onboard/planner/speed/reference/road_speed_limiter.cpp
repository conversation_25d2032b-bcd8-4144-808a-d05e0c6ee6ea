#include "planner/speed/reference/road_speed_limiter.h"

#include <glog/logging.h>

#include <algorithm>
#include <string>
#include <utility>
#include <vector>

#include "hdmap/lib/point_util.h"
#include "hdmap_protos/lane.pb.h"
#include "log_utils/log_macros.h"
#include "math/unit_conversion.h"
#include "perception/common/map_util.h"
#include "planner/planning_gflags.h"
#include "planner/speed/reasoning_input/traffic_rules/speed_limit_traffic_rule.h"
#include "planner/speed/reference/reference_profile.h"
#include "planner/utility/common/config_io.h"
#include "planner_protos/road_speed_limiter_patch.pb.h"
#include "planner_protos/speed_limiters.pb.h"
#include "pnc_map_service/map_elements/lane.h"
#include "pnc_map_service/map_elements/road.h"
#include "pnc_map_service/map_elements/section.h"

namespace planner {
namespace speed {

namespace {
// Finds the closest point on the nominal path from the track point on a given
// 'speed_limit' then returns the closest point's arclength.
double GetsClosestArcLengthFromSpeedLimitOnNominalPath(
    const hdmap::Limit& speed_limit, const adv_geom::Path2dWithJuke& path) {
  const hdmap::Point& track_point = speed_limit.track().point();
  return path.GetProjectionArcLength({track_point.x(), track_point.y()},
                                     math::pb::UseExtensionFlag::kForbid);
}

// Transform from LaneSpeedLimit Struct to hdmap::Limit proto.
std::optional<hdmap::Limit> GetSpeedLimitAtArcLengthFromLaneSpeedLimit(
    const traffic_rules::LaneSpeedLimit& speed_limit,
    const adv_geom::Path2dWithJuke& path) {
  if (path.GetTotalArcLength() <= speed_limit.ra_arclength) {
    return std::nullopt;
  }
  math::geometry::Point2d track_point =
      path.GetInterpPoint(std::max(speed_limit.ra_arclength, 0.0));
  hdmap::Limit speed_limit_at_arc_length;
  *(speed_limit_at_arc_length.mutable_track()->mutable_point()) =
      hdmap::point_util::BuildHdmapPoint(track_point.x(), track_point.y());
  speed_limit_at_arc_length.set_limit_max(speed_limit.max_speed);
  speed_limit_at_arc_length.set_limit_min(0);
  return speed_limit_at_arc_length;
}

}  // namespace

RoadSpeedLimiter::RoadSpeedLimiter(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pb::RoadSpeedLimiterConfig& config,
    double relax_ego_exceeding_road_speed_limit_factor,
    std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit,
    std::vector<traffic_rules::LaneSpeedLimit> traffic_rule_speed_limits,
    planner::pb::MotionMode motion_mode, const std::tm* order_start_time_info,
    ReferenceProfile* reference_profile_ptr, const std::string& current_region)
    : lane_sequence_(lane_sequence),
      relax_ego_exceeding_road_speed_limit_factor_(
          relax_ego_exceeding_road_speed_limit_factor),
      discomfort_to_decay_ego_exceeding_road_speed_limit_(
          std::move(discomfort_to_decay_ego_exceeding_road_speed_limit)),
      reference_profile_ptr_(DCHECK_NOTNULL(reference_profile_ptr)),
      traffic_rule_speed_limits_(std::move(traffic_rule_speed_limits)),
      motion_mode_(motion_mode),
      order_start_time_info_(order_start_time_info),
      config_(config),
      current_region_(current_region) {}

RoadSpeedLimiter::RoadSpeedLimiter(
    const std::vector<const pnc_map::Lane*>& lane_sequence,
    const pb::RoadSpeedLimiterConfig& config,
    double relax_ego_exceeding_road_speed_limit_factor,
    std::optional<double> discomfort_to_decay_ego_exceeding_road_speed_limit,
    ReferenceProfile* reference_profile_ptr, const std::string& current_region)
    : lane_sequence_(lane_sequence),
      relax_ego_exceeding_road_speed_limit_factor_(
          relax_ego_exceeding_road_speed_limit_factor),
      discomfort_to_decay_ego_exceeding_road_speed_limit_(
          std::move(discomfort_to_decay_ego_exceeding_road_speed_limit)),
      reference_profile_ptr_(DCHECK_NOTNULL(reference_profile_ptr)),
      config_(config),
      current_region_(current_region) {}

double RoadSpeedLimiter::ComputeSpeedLimitBasedOnOperationTime(
    const std::tm& operation_time, const pb::RoadSpeedLimiterConfig& config) {
  const bool driving_at_daytime =
      (config.daytime_start_time_in_hours() <= operation_time.tm_hour) &&
      (operation_time.tm_hour < config.daytime_end_time_in_hours());
  return driving_at_daytime
             ? config.speed_limit_for_driving_at_daytime_in_mps()
             : config.speed_limit_for_driving_outside_daytime_in_mps();
}

void RoadSpeedLimiter::Update() {
  DCHECK(!lane_sequence_.empty());
  std::vector<hdmap::Limit> speed_limits = {};
  double ego_max_speed = traffic_rules::GetEgoMaxSpeedDependOnCar();
  // We use fixed route mode in US road test which will publish undefined order
  // infomation and we only test in daytime in US. Do not adjust speed limit
  // based on order start time in US.
  if (FLAGS_planning_enable_daytime_speed_limit &&
      !FLAGS_planning_enable_driving_on_freeway &&
      order_start_time_info_ != nullptr &&
      av_comm::CarId::Get().region() != av_comm::CarRegion::kUS) {
    math::UpdateMin(
        ComputeSpeedLimitBasedOnOperationTime(*order_start_time_info_, config_),
        ego_max_speed);
  }
  road_speed_limits_debug_.set_ego_max_speed(ego_max_speed);

  // The path starting from ego position.
  const adv_geom::Path2dWithJuke& path = reference_profile_ptr_->path();
  if (!traffic_rule_speed_limits_.empty() &&
      motion_mode_ == planner::pb::MotionMode::BACKWARD) {
    for (const auto& traffic_rule_speed_limit : traffic_rule_speed_limits_) {
      // Apply traffic_rule speed limits.
      const auto hdmap_speed_limit = GetSpeedLimitAtArcLengthFromLaneSpeedLimit(
          traffic_rule_speed_limit, path);
      if (!hdmap_speed_limit.has_value()) {
        break;
      }
      speed_limits.insert(speed_limits.end(), hdmap_speed_limit.value());
    }
  } else {
    for (const pnc_map::Lane* lane_ptr : lane_sequence_) {
      // Apply road speed limits.
      std::vector<hdmap::Limit> lane_speed_limits{
          lane_ptr->speed_limits().begin(), lane_ptr->speed_limits().end()};
      // Update speed limits if lane is in patched speed limits config.
      traffic_rules::UpdateLaneSpeedLimitsFromPatchedSpeedLimits(
          lane_ptr, config_.target_speed_limit_patch(), current_region_,
          &lane_speed_limits);
      // Update speed limits if lane is ramp type. Refer to
      // https://cooper.didichuxing.com/docs2/document/2204260303355.
      if (lane_ptr->section()->road()->side_road_type() == hdmap::Road::RAMP) {
        DCHECK(lane_ptr->section()->road()->road_class() ==
               hdmap::Road::EXPRESSWAY)
            << " lane id is " << lane_ptr->id()
            << "This is NOT mandatory, but it serves as a reminder for the "
               "moderator to conduct sufficient testing.";

        for (hdmap::Limit& speed_limit : lane_speed_limits) {
          const double adjusted_target_speed_limit = std::max(
              ego_max_speed, static_cast<double>(speed_limit.limit_max()));
          DCHECK_GE(adjusted_target_speed_limit, speed_limit.limit_min());
          speed_limit.set_limit_max(adjusted_target_speed_limit);
        }
      }

      speed_limits.insert(speed_limits.end(),
                          std::make_move_iterator(lane_speed_limits.begin()),
                          std::make_move_iterator(lane_speed_limits.end()));

      // Add debug info.
      const auto& speed_limits_from_map_debug =
          road_speed_limits_debug_.add_speed_limits_from_map();
      speed_limits_from_map_debug->set_lane_id(lane_ptr->id());
      for (const hdmap::Limit& limit : lane_ptr->speed_limits()) {
        speed_limits_from_map_debug->add_limits(limit.limit_max());
      }
    }
  }

  // This is used to record the beginning and end of the added speed limit
  // segment.
  double min_ra_arclength = 0;
  double min_ra_arclength_speed = 0;
  double max_ra_arclength = 0;
  double max_ra_arclength_speed = 0;
  road_speed_limits_debug_.mutable_speed_limits_for_arclength()
      ->mutable_speed_limits()
      ->Reserve(speed_limits.size());
  for (size_t idx = 0; idx < speed_limits.size(); idx++) {
    const hdmap::Limit& speed_limit = speed_limits[idx];
    // The first speed limit should be added from the location of ego, this also
    // solves the problem of projection errors.
    const double start_ra_arclength =
        (idx == 0 ? 0.0
                  : GetsClosestArcLengthFromSpeedLimitOnNominalPath(speed_limit,
                                                                    path));
    const double end_ra_arclength =
        idx + 1 < speed_limits.size()
            ? GetsClosestArcLengthFromSpeedLimitOnNominalPath(
                  speed_limits.at(idx + 1), path)
            : path.GetTotalArcLength();
    // We only care about the speed limit segment that can be correctly
    // projected.
    if (end_ra_arclength > start_ra_arclength) {
      if (idx == 0) {
        min_ra_arclength = start_ra_arclength;
      } else {
        min_ra_arclength = std::min(min_ra_arclength, start_ra_arclength);
      }
      const double limit_max_speed = (std::min(
          static_cast<double>(speed_limit.limit_max()), ego_max_speed));
      DCHECK_LT(limit_max_speed, ego_max_speed + math::constants::kEpsilon);
      if (math::IsApprox(min_ra_arclength, start_ra_arclength)) {
        min_ra_arclength_speed = limit_max_speed;
      }
      max_ra_arclength = std::max(max_ra_arclength, end_ra_arclength);
      if (math::IsApprox(max_ra_arclength, end_ra_arclength)) {
        max_ra_arclength_speed = limit_max_speed;
      }
      const auto& speed_limit_at_arclength_debug =
          road_speed_limits_debug_.mutable_speed_limits_for_arclength()
              ->add_speed_limits();
      // TODO(nihar): now we only support settings each discomfort levels' speed
      // limit exactly, as road speed limit only allowing setting zero
      // discomfort and max discomfort speed, then do linear interpolation in
      // between. we need to update logic in UpdateSpeedBoundAtDiscomfortIx to
      // support randomly discomfort varying speed limit.
      if (FLAGS_planning_enable_relax_speed_road_limiter &&
          discomfort_to_decay_ego_exceeding_road_speed_limit_.has_value()) {
        // Discomfort 0.0,ego don't exceed 50km/h,discomfort 0.25,ego will
        // exceed 1.1*road_speed_limit and <60km/h,discomfort >=0.5,ego will
        // exceed 1.2*road_speed_limit and <60km/h.
        ComputeAndSetDiscomfortVaryingRoadSpeedLimit(
            static_cast<double>(speed_limit.limit_max()),
            reference_profile_ptr_->init_state_speed(), ego_max_speed,
            discomfort_to_decay_ego_exceeding_road_speed_limit_.value(),
            start_ra_arclength, end_ra_arclength,
            speed_limit_at_arclength_debug);
        speed_limit_at_arclength_debug->set_debug_str("decay exceed limit;");
      } else if (FLAGS_planning_enable_relax_speed_road_limiter &&
                 (relax_ego_exceeding_road_speed_limit_factor_ >
                      math::constants::kEpsilon &&
                  reference_profile_ptr_->init_state_speed() >
                      limit_max_speed)) {
        const double adjust_max_speed =
            limit_max_speed +
            (reference_profile_ptr_->init_state_speed() - limit_max_speed) *
                relax_ego_exceeding_road_speed_limit_factor_;
        SetZeroAndMaxDiscomfortRoadSpeedLimit(
            adjust_max_speed, start_ra_arclength, end_ra_arclength,
            speed_limit_at_arclength_debug);
        speed_limit_at_arclength_debug->set_debug_str("exceed road limit;");
      } else {
        SetZeroAndMaxDiscomfortRoadSpeedLimit(
            limit_max_speed, start_ra_arclength, end_ra_arclength,
            speed_limit_at_arclength_debug);
        speed_limit_at_arclength_debug->set_debug_str("normal;");
      }
    }
  }
  // If we have not added speed limit from 0 to min_ra_arclength.
  if (!math::NearZero(min_ra_arclength)) {
    const auto& speed_limit_at_arclength_debug =
        road_speed_limits_debug_.mutable_speed_limits_for_arclength()
            ->add_speed_limits();
    SetZeroAndMaxDiscomfortRoadSpeedLimit(min_ra_arclength_speed, 0,
                                          min_ra_arclength,
                                          speed_limit_at_arclength_debug);
    speed_limit_at_arclength_debug->set_debug_str(
        "not add speed limit at begin segment;");
  }
  // If we have not added speed limit from max_ra_arclength to end of path.
  if (!math::IsApprox(max_ra_arclength, path.GetTotalArcLength())) {
    const auto& speed_limit_at_arclength_debug =
        road_speed_limits_debug_.mutable_speed_limits_for_arclength()
            ->add_speed_limits();
    SetZeroAndMaxDiscomfortRoadSpeedLimit(
        max_ra_arclength_speed, max_ra_arclength, path.GetTotalArcLength(),
        speed_limit_at_arclength_debug);
    speed_limit_at_arclength_debug->set_debug_str(
        "not add speed limit at end segment;");
  }
  // If we have not added speed limit for the whole path.
  if (math::IsApprox(min_ra_arclength, max_ra_arclength)) {
    const auto& speed_limit_at_arclength_debug =
        road_speed_limits_debug_.mutable_speed_limits_for_arclength()
            ->add_speed_limits();
    const auto whole_path_speed =
        std::min(config_.default_road_speed_limit_in_mps(), ego_max_speed);
    SetZeroAndMaxDiscomfortRoadSpeedLimit(whole_path_speed, 0,
                                          path.GetTotalArcLength(),
                                          speed_limit_at_arclength_debug);
    speed_limit_at_arclength_debug->set_debug_str(
        "not add speed limit at whole path;");
    road_speed_limits_debug_.set_is_default_speed_limit(true);
    road_speed_limits_debug_.set_default_speed_limit(whole_path_speed);
  }
  // Set fixed location speed limit patch
  SetFixedSpeedLimitPatchForOcclusion(path, lane_sequence_);

  updated_ = true;
}

void RoadSpeedLimiter::SetMaxDiscomfortRoadSpeedLimit(double speed,
                                                      double start_ra_arclength,
                                                      double end_ra_arclength) {
  reference_profile_ptr_->UpdateMaxDiscomfortV(
      speed, start_ra_arclength, end_ra_arclength,
      config_.road_limiter_min_a_mpss());
}

void RoadSpeedLimiter::SetZeroDiscomfortRoadSpeedLimit(
    double speed, double start_ra_arclength, double end_ra_arclength) {
  reference_profile_ptr_->UpdateMinAndMidDiscomfortV(
      speed, start_ra_arclength, end_ra_arclength,
      config_.road_limiter_min_a_mpss());
}

void RoadSpeedLimiter::SetRoadSpeedLimitForEntirePath(const double speed) {
  auto* const speed_limit_at_arclength_debug =
      road_speed_limits_debug_.mutable_speed_limits_for_arclength()
          ->add_speed_limits();
  SetZeroAndMaxDiscomfortRoadSpeedLimit(
      speed, 0, reference_profile_ptr_->path().GetTotalArcLength(),
      speed_limit_at_arclength_debug);
  updated_ = true;
}

void RoadSpeedLimiter::SetZeroAndMaxDiscomfortRoadSpeedLimit(
    double speed, double start_ra_arclength, double end_ra_arclength,
    pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug) {
  reference_profile_ptr_->UpdateVAtAllDiscomfort(
      speed, start_ra_arclength, end_ra_arclength,
      config_.road_limiter_min_a_mpss());
  if (speed_limit_at_arclength_debug != nullptr) {
    speed_limit_at_arclength_debug->set_start_arclength_m(start_ra_arclength);
    speed_limit_at_arclength_debug->set_end_arclength_m(end_ra_arclength);
    speed_limit_at_arclength_debug->mutable_speed_limit()->set_min_discomfort_v(
        speed);
    speed_limit_at_arclength_debug->mutable_speed_limit()->set_max_discomfort_v(
        speed);
  }
}

void RoadSpeedLimiter::SetDiscomfortVaryingRoadSpeedLimit(
    const std::vector<double>& discomforts,
    const std::vector<double>& discomfort_varying_speed_limit,
    double start_ra_arclength, double end_ra_arclength,
    pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug) {
  DCHECK_EQ(discomforts.size(), discomfort_varying_speed_limit.size());
  DiscomfortVarying speed_limits_for_discomforts(
      discomforts, discomfort_varying_speed_limit);
  DCHECK(speed_limits_for_discomforts.IsNonDecreasing());
  DCHECK_LE(discomfort_varying_speed_limit.size(), Discomforts::kLevels);
  for (size_t index = 0; index < discomfort_varying_speed_limit.size();
       index++) {
    reference_profile_ptr_->UpdateVAtDiscomfort(
        discomforts[index], discomfort_varying_speed_limit[index],
        start_ra_arclength, end_ra_arclength,
        config_.road_limiter_min_a_mpss());
  }
  if (speed_limit_at_arclength_debug != nullptr) {
    speed_limit_at_arclength_debug->set_start_arclength_m(start_ra_arclength);
    speed_limit_at_arclength_debug->set_end_arclength_m(end_ra_arclength);
    speed_limit_at_arclength_debug->mutable_speed_limit()->set_min_discomfort_v(
        discomfort_varying_speed_limit.front());
    speed_limit_at_arclength_debug->mutable_speed_limit()->set_max_discomfort_v(
        discomfort_varying_speed_limit.back());
  }
}

void RoadSpeedLimiter::ComputeAndSetDiscomfortVaryingRoadSpeedLimit(
    const double max_road_speed_limit, const double init_state_speed,
    const double ego_max_speed,
    const double previous_discomfort_to_decay_relaxation,
    const double start_ra_arclength, const double end_ra_arclength,
    pb::SpeedLimitForArcLength* speed_limit_at_arclength_debug) {
  DiscomfortVarying discomfort_varying_speed_limit_relaxation(
      {{0.0, 1.0}, {0.25, 1.1}, {0.5, 1.2}, {1.0, 1.2}});
  const double max_discomfort_speed_limit =
      max_road_speed_limit * discomfort_varying_speed_limit_relaxation(1.0);

  // Compute the discomfort varying road speed limit w.r.t. the relaxation
  // factor and the previous discomfort.
  const std::vector<double> discomforts = {-1.0, 0.0, 0.25, 0.5, 0.75, 1.0};
  DCHECK(!discomforts.empty());
  std::vector<double> discomfort_varying_speed_limit(discomforts.size());
  double last_discomfort_speed_limit = std::numeric_limits<double>::infinity();
  for (int index = discomforts.size() - 1; index >= 0; index--) {
    const double discomfort = discomforts[index];
    double current_discomfort_max_speed_limit =
        max_road_speed_limit *
        discomfort_varying_speed_limit_relaxation(discomfort);
    if (discomfort < math::constants::kEpsilon) {
      // To keep the consistency with the previous settings, we restrict the
      // speed limit at discomfort 0 to be less than ego's max speed.
      math::UpdateMin(ego_max_speed, current_discomfort_max_speed_limit);
    }

    // TODO(pengfei): See if we can remove this condition.
    if (discomfort <
        previous_discomfort_to_decay_relaxation + math::constants::kEpsilon) {
      // Compute the speed limit considering the decay factor. The part where
      // ego's init speed exceeds the speed limit will be decayed to zero in
      // around 40 cycles.
      current_discomfort_max_speed_limit = std::min(
          current_discomfort_max_speed_limit +
              std::max(init_state_speed - current_discomfort_max_speed_limit,
                       0.0) *
                  relax_ego_exceeding_road_speed_limit_factor_,
          max_discomfort_speed_limit);
    }
    // Guarantee that the final speed limit at the current discomfort is not
    // greater than the one at the higher discomfort and the max speed for lane
    // change (i.e. 60kph).
    current_discomfort_max_speed_limit = std::min(
        {current_discomfort_max_speed_limit, last_discomfort_speed_limit,
         FLAGS_planning_enable_driving_on_freeway
             ? ego_max_speed
             : config_.max_road_speed_for_lane_change_in_mps()});
    last_discomfort_speed_limit = current_discomfort_max_speed_limit;

    discomfort_varying_speed_limit[index] = current_discomfort_max_speed_limit;
  }

  SetDiscomfortVaryingRoadSpeedLimit(
      discomforts, discomfort_varying_speed_limit, start_ra_arclength,
      end_ra_arclength, speed_limit_at_arclength_debug);
}

void RoadSpeedLimiter::SetFixedSpeedLimitPatchForOcclusion(
    const adv_geom::Path2dWithJuke& path,
    const std::vector<const pnc_map::Lane*>& lane_sequence) {
  // Loop over the lane sequence and find the target lanes.

  for (const pnc_map::Lane* lane : lane_sequence) {
    // 2024-2025 occlusion-related speed limit patch:
    // https://cooper.didichuxing.com/docs2/sheet/2203275557565?sheetId=q2ycv
    //
    // The following are based on map version:
    //      beijing_yizhuang-22101982-v37.1 for patch 1-5
    //      guangzhou-v47.1-653 for patch 6-7

    // Patch 1 - cn13392591
    if (lane->id() == 194468) {
      const double lane_end_arclength =
          path.GetProjectionArcLength(lane->center_line().GetEndPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_end_arclength - 20.0, 0.0);
      const double speed_limit_range_end_arclength = lane_end_arclength;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        SetZeroAndMaxDiscomfortRoadSpeedLimit(
            math::KmphToMps(40.0), speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits());
      }
    }

    // Patch 2 - cn12994425
    // Update(zhihaoruan):
    //
    // [4/28/2025] - scenario cn12994425 traffic light sequence has changed.
    //  When ego has green light, ego will no longer interact with
    //  right-crossing traffic (verified by: chengyongfeng). The speed limit
    //  patch should be safe to remove.

    // Patch 3 - cn11825409
    if (lane->id() == 162832 || lane->id() == 162831) {
      const double lane_start_arclength =
          path.GetProjectionArcLength(lane->center_line().GetStartPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_start_arclength + 20.0, 0.0);
      const double speed_limit_range_end_arclength =
          lane_start_arclength + 40.0;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        SetZeroAndMaxDiscomfortRoadSpeedLimit(
            math::KmphToMps(30.0), speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits());
      }
    }

    // Patch 4 - cn11462161
    if (lane->id() == 193434) {
      const double lane_end_arclength =
          path.GetProjectionArcLength(lane->center_line().GetEndPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_end_arclength - 15.0, 0.0);
      const double speed_limit_range_end_arclength = lane_end_arclength;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        SetZeroAndMaxDiscomfortRoadSpeedLimit(
            math::KmphToMps(30.0), speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits());
      }
    }

    // Patch 5 - cn12293075
    if (lane->id() == 195942) {
      const double lane_end_arclength =
          path.GetProjectionArcLength(lane->center_line().GetEndPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_end_arclength - 10.0, 0.0);
      const double speed_limit_range_end_arclength = lane_end_arclength;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        const double speed_limit = math::KmphToMps(15.0);
        reference_profile_ptr_->UpdateVAtAllDiscomfort(
            speed_limit, speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            /*min_accel=*/-2.0);
        auto* speed_limit_at_arclength_debug =
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits();
        speed_limit_at_arclength_debug->set_start_arclength_m(
            speed_limit_range_start_arclength);
        speed_limit_at_arclength_debug->set_end_arclength_m(
            speed_limit_range_end_arclength);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_min_discomfort_v(speed_limit);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_max_discomfort_v(speed_limit);
      }
    }

    // Patch 6 - cn18769231
    if (lane->id() == 190792 || lane->id() == 190794) {
      const double lane_end_arclength =
          path.GetProjectionArcLength(lane->center_line().GetEndPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_end_arclength - 10.0, 0.0);
      const double speed_limit_range_end_arclength = lane_end_arclength;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        const double speed_limit = math::KmphToMps(20.0);
        reference_profile_ptr_->UpdateVAtAllDiscomfort(
            speed_limit, speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            /*min_accel=*/-2.0);
        auto* speed_limit_at_arclength_debug =
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits();
        speed_limit_at_arclength_debug->set_start_arclength_m(
            speed_limit_range_start_arclength);
        speed_limit_at_arclength_debug->set_end_arclength_m(
            speed_limit_range_end_arclength);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_min_discomfort_v(speed_limit);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_max_discomfort_v(speed_limit);
      }
    }

    // Patch 7 - cn18916751
    if (lane->id() == 166472 || lane->id() == 166473) {
      const double lane_end_arclength =
          path.GetProjectionArcLength(lane->center_line().GetEndPoint(),
                                      math::pb::UseExtensionFlag::kAllow);
      const double speed_limit_range_start_arclength =
          std::max(lane_end_arclength - 10.0, 0.0);
      const double speed_limit_range_end_arclength = lane_end_arclength;
      if (speed_limit_range_end_arclength > speed_limit_range_start_arclength) {
        const double speed_limit = math::KmphToMps(25.0);
        reference_profile_ptr_->UpdateVAtAllDiscomfort(
            speed_limit, speed_limit_range_start_arclength,
            speed_limit_range_end_arclength,
            /*min_accel=*/-2.0);
        auto* speed_limit_at_arclength_debug =
            road_speed_limits_debug_.mutable_speed_limits_for_arclength()
                ->add_speed_limits();
        speed_limit_at_arclength_debug->set_start_arclength_m(
            speed_limit_range_start_arclength);
        speed_limit_at_arclength_debug->set_end_arclength_m(
            speed_limit_range_end_arclength);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_min_discomfort_v(speed_limit);
        speed_limit_at_arclength_debug->mutable_speed_limit()
            ->set_max_discomfort_v(speed_limit);
      }
    }
  }
}

}  // namespace speed
}  // namespace planner
