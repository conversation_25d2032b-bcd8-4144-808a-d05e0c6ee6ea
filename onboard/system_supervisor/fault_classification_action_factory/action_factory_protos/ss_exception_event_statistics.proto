syntax = "proto3";
package action_factory.pb;

import "voy_protos/canbus.proto";
import "voy_protos/fault_level_manifest.proto";
import "voy_protos/system_control.proto";

// Next id: 21
message SsExceptionEventStatistics {
  enum FaultCause {
    NORMALLY = 0;
    SS_RESTORE = 1;
  }

  enum SsExceptionEventType {
    UNKNOWN = 0;
    FAULT_APPEARED = 1;
    FAULT_DISAPPEARED = 2;
  }

  // date + sequence.
  string sequence_counter = 1;
  // car_id + timestamp + [node, component, component_instance, code]
  string fault_event_id = 2;
  // car_id + timestamp + system_level
  string vehicle_exception_id = 3;
  // Indicates the create timestamp of fault_event_id.
  int64 fault_event_time = 4;
  string car_id = 5;
  voy.VehicleMode vehicle_mode = 6;
  string fault_level = 7;
  fault_detector.pb.FaultExceptionType fault_type = 8;
  // Indicates the fault level of the system.
  string system_level = 9;
  // Indicates the collection of all current fault types
  repeated fault_detector.pb.FaultExceptionType system_fault_type = 10;
  string acu_version = 11;
  // Node name + Component + Component instance + Fault code.
  string fault_unit = 12;
  FaultCause fault_cause = 13;
  int64 fault_appear_time = 14;
  // Indicates the timestamp when triggers system_level alterations.
  int64 af_dispose_time = 15;
  bool is_major_fault = 16;
  .pb.SsVehicleParkingType.Type vehicle_parking_type = 17;
  int64 fault_disappear_time = 18;
  SsExceptionEventType event_type = 19;
  string trip_id = 20;
}
