#ifndef OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TIME_SYNC_TIME_SYNC_H_
#define OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TIME_SYNC_TIME_SYNC_H_

#include <cstdint>

namespace fallback_adas {
namespace acu_signal_input {

// The MCU has not synchronized with ACU in terms of time, but the algorithm
// in the MCU needs to accurately obtain the current timestamp.
// A simple approach was taken. <PERSON><PERSON> obtains the latest timestamp of the
// current ACU and sends it to the MCU through a 10ms cycle message. then
// considering the communication delay, compensating for 10ms is the external
// timestamp of the MCU. At the same time, the MCU will accumulate this
// timestamp internally as the internal timestamp. Ref doc:
// https://cooper.didichuxing.com/docs2/document/2204441670104.
extern uint32_t GetCurrentTimestamp(uint8_t acu_pose_counter,
                                    uint32_t pose_timestamp_ms,
                                    uint32_t can_timestamp_ms);

}  // namespace acu_signal_input
}  // namespace fallback_adas

#endif  // OFFBOARD_FALLBACK_ADS_ACU_SIGNAL_INPUT_TIME_SYNC_TIME_SYNC_H_
