#include "planner/utility/customer_monitor/customer_monitor.h"

#include <algorithm>
#include <optional>
#include <string>

#include "base/optional_value_accessor.h"
#include "geometry/algorithms/append.h"
#include "planner/behavior/directive_generator/lateral_clearance_corridor_directive_generator.h"
#include "planner/behavior/types/behavior_decision.h"
#include "planner/constants.h"
#include "planner/speed/reasoning_input/traffic_rules/traffic_rule_utility.h"
#include "stop_cell_protos/stop_cell_data.pb.h"

namespace planner {

namespace {

// Filter distance for drawing road boundary in meters.
constexpr double kRoadBoundaryDrawingFilterInMeter = 0.25;
// Distance threshold to determine cut in warning in meters.
constexpr double kCutInWarningDistanceThresholdInMeter = 45.0;
// Distance threshold to determine vehicle cut in warning in meters.
constexpr double kVehicleCutInWarningDistanceInMeter = 35.0;
// Angle threshold to determine cut in warning in radian.
constexpr double kCutInWarningAngleThresholdInRad = 2.0 * M_PI / 3.0;
// Distance threshold from agent to overlap lane in meters.
constexpr double kCutInAgentToOverlapLaneThresholdInMeter = 1.0;
// The number to check free space cyclist cut in.
constexpr int64_t kCutInCyclistCheckNum = 50;
// The resolution to check free space cyclist cut in in milliseconds.
constexpr int64_t kCutInCyclistResolutionInMSec = 100;
// Distance threshold to determine right turn warning cyclist in meters.
constexpr double kRightTurnWarningCyclistThresholdInMeter = 15.0;
// Distance threshold to determine right turn warning pedestrian in meters.
constexpr double kRightTurnWarningPedThresholdInMeter = 7.5;
// Speed threshold to determine right turn warning in mps.
constexpr double kRightTurnWarningSpeedThresholdInMps = 0.5;
// Angle threshold to determine right turn warning in rad.
constexpr double kRightTurnWarningAngleThresholdInRad = M_PI / 3.0;
// Distance threshold to determine potential collision warning in meters.
constexpr double kPotentialCollisionDistanceInMeter = 0.5;
// Angle to determine direction of potential collision warning in radian.
constexpr double kPotentialCollisionAngleInRad = M_PI / 4.0;
// Distance threshold to determine cyclist flicker warning in meters.
constexpr double kCyclistWarningNeededDistanceInMeter = 10;
// Distance threshold to determine cyclist voice warning in meters.
constexpr double kCyclistVoiceWarningNeededDistanceInMeter = 10;
// Distance threshold to determine cyclist voice warning in meters.
constexpr double kCyclistVoiceWarningNeededSpeedDiffInMps = 5 / 3.6;
// Distance threshold to near median strip zone in meters.
constexpr double kNearMedianStripThresholdInMeter = 3.0;
// Distance threshold to determine right turn warning in meters.
constexpr double kMedianStripAgentThresholdInMeter = 50.0;
// Distance threshold to determine traffic light warning in meters.
constexpr double kTrafficLightWarningThresholdInMeter = 60.0;
// Speed threshold of ego completely stop in mps.
constexpr double kCompletelyStopSpeedThresholdInMps = 0.2;
// Trajectory odom threshold of ego completely stop in meters.
constexpr double kEgoStopTrajOdomThresholdInMps = 2.0;
// Distance threshold to determine yield agent warning in meters.
constexpr double kYieldAgentWarningThresholdInMeter = 15.0;
// Speed diff threshold to determine acc yield agent warning in mps.
constexpr double kYieldAccAgentWarningSpeedDiffInMps = 3.0;
// Distance from ego to PDZ to determine median strip warning in meters.
constexpr double kPDZDistanceThresholdInMeter = 0.5;
// The max count for publishing PullOutSuccess progress state signal.
constexpr int kPullOutSuccessSignalMaxCount = 10;
// The offset of drawing text in meters.
constexpr double kTextDrawingOffsetInMeter = 1.0;
// The start index of trajectory poses to be evaluated.
constexpr int kEvaluateTrajectoryPosesStartIndex = 5;
// The end index of trajectory poses to be evaluated.
constexpr int kEvaluateTrajectoryPosesEndIndex = 15;
// The lateral threshold of nudge-warning.
constexpr double kMinNudgeWarningDistInMeter = 0.6;
// The longitude threshold of nudge-warning.
constexpr double kMaxNudgeJudgeArcLengthThresholdInMeter = 15;
// Minimum number of cycles that can judge nudge.
constexpr int kMinNudgeJudgeCycleNum = 3;
// The max count for publishing cyclist overtake warning signal.
constexpr int kCyclistVoiceWarningSignalMaxCount = 3;
// The distance small enough to be considered to have reached the destination.
// (Considering the distance less than 1m will seem as flicker)
constexpr double kSmallDistanceDiffInMeter = 1;
// Maximum tolerance time for reverse driving to stationary in incomplete state.
constexpr int64_t kMaxToleranceReverseDrivingStopTimeInMSec = 15000;
// The minimum roads number of junction to give route guidance warning.
constexpr int kMinRoadNumInRouteGuidanceWaringJunction = 5;
// The lane id used for invalid route guidance warning.
constexpr int kInvalidRouteGuidanceWaringLaneId = -1;
// The ego speed threshold of potential nudge collision in mps.
constexpr double kPotentialNudgeCollisionEgoSpeedThresholdInMps = 3.0;
// The lateral distance threshold from ego vehicle to nominal path of potential
// nudge collision in meters.
constexpr double kPotentialNudgeCollisionLateralDisThresholdInMeter = 0.5;
// The ego steer threshold of potential nudge collision in rads.
constexpr double kPotentialNudgeCollisionEgoSteerThresholdInRad = 0.02;
// The dilation width buffer of the bounding box for checking potential
// nudge collision.
constexpr double kBoundingBoxDilationWidthBufferInMeter = 0.7;
// Distance threshold to determine the agent is far away from ego vehicle. Use
// ComparableDistance to reduce computation cost.
constexpr double kAgentFarAwayDistanceInSqrMeter = 100.0;
// The buffer of the offroad distance in u-turn.
constexpr double kOffroadInUTurnDistBufferInMeter = 30.0;
// The ray casting range offset for generation of road corridor in meters.
constexpr double kRayCastingRangeOffsetInMeter = 5.0;
// The default planning horizontal length in meters.
constexpr double kDefaultPlanningHorizonInMeter = 40.0;
// The ray cast resolution for visualization, which can be set lower to save
// compute time.
constexpr double kRayCastingResolutionForVisualizationInMeter = 0.5;
// The distance that ego crosses destination, within which planning is allowed
// to try immediate pull over.
constexpr double kMaxDistanceToTryImmediatePullOverInMeter = 500.0;
// If duration exceeds this threshold, HMI will pop up a window with the
// option to cancel immediate pull over.
constexpr int64_t kTimeThresholdToPopUpCancelImmediatePullOverInMSec = 180000;
// If duration exceeds this threshold, customer-triggered immediate pull
// over will be judged as failure.
constexpr int64_t kMaxTimeThresholdToTryImmediatePullOverInMSec = 300000;
// If mileage exceeds this threshold, HMI will pop up a window with
// the option to cancel immediate pull over.
constexpr double kDistanceThresholdToPopUpCancelImmediatePullOverInMeter =
    500.0;
// If mileage exceeds this threshold, customer-triggered immediate
// pull over will be judged as failure.
constexpr double kMaxDistanceThresholdToTryImmediatePullOverInMeter = 1000.0;
// Speed threshold of ego stop for pullover in mps.
constexpr double kStopSpeedForPulloverThresholdInMps = 0.2;
// The distance threshold from end of lane sequence to ego front_bumper, used in
// IsEgoAtEndOfLaneSequence.
constexpr double kThresholdNearEndOfLaneSequenceInMeter = 15.0;
// When dist_to_destination is within this threshold, we prefer to trigger the
// regular pull over.
constexpr double kThresholdDistToDestToExecuteRegularPullOver = 120.0;

// Updates the stop line describe by input parameters.
void UpdateSpecificStopLine(
    double stop_arclength_m, pb::StopLineType::Enum stop_line_type,
    voy::perception::ObjectType stop_object_type, bool is_prepared,
    const WorldModel& world_model, const math::Curve2d& nominal_curve,
    const math::geometry::PolylineCurve2d& right_lane_boundary,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  const double wheel_base = world_model.robot_state()
                                .car_model_with_shape()
                                .param()
                                .measurement()
                                .wheel_base();
  pb::StopLineDecision* stop_line_decision =
      customer_monitor_visual->add_stop_fences();
  stop_line_decision->set_arclength_m(stop_arclength_m);
  stop_line_decision->set_type(stop_line_type);
  stop_line_decision->set_is_prepared(is_prepared);
  const double target_curvature =
      nominal_curve.GetInterpSignedCurvature(stop_arclength_m);
  const double target_steering_rad =
      vehicle_model::ComputeSteeringFromCurvature(target_curvature, wheel_base);
  stop_line_decision->set_target_steering_rad(target_steering_rad);
  auto pose = stop_line_decision->mutable_pose();
  pose->set_x(nominal_curve.GetInterpX(stop_arclength_m));
  pose->set_y(nominal_curve.GetInterpY(stop_arclength_m));
  pose->set_yaw(nominal_curve.GetInterpTheta(stop_arclength_m));
  stop_line_decision->set_object_type(stop_object_type);
  const double x =
      nominal_curve.GetInterpX(stop_arclength_m - kTextDrawingOffsetInMeter);
  const double y =
      nominal_curve.GetInterpY(stop_arclength_m - kTextDrawingOffsetInMeter);
  const double arc_length =
      right_lane_boundary
          .GetProximity({x, y}, math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  auto text_pose = stop_line_decision->mutable_text_pose();
  const auto text_pt = right_lane_boundary.GetInterp(arc_length);
  text_pose->set_x(text_pt.x());
  text_pose->set_y(text_pt.y());
  text_pose->set_yaw(right_lane_boundary.GetInterpTheta(arc_length));
}

// Adds an acc fence.
void AddAccFence(const AccLine& acc_line,
                 const math::Curve2d& smoothed_nominal_curve,
                 pb::CustomerMonitorVisual* customer_monitor_visual) {
  pb::AccFence* acc_fence = customer_monitor_visual->add_acc_fences();
  const double acc_safety_arclength_m = acc_line.acc_safety_arclength_m;
  acc_fence->set_arclength_m(acc_safety_arclength_m);
  auto pose = acc_fence->mutable_pose();
  pose->set_x(smoothed_nominal_curve.GetInterpX(acc_safety_arclength_m));
  pose->set_y(smoothed_nominal_curve.GetInterpY(acc_safety_arclength_m));
  pose->set_yaw(smoothed_nominal_curve.GetInterpTheta(acc_safety_arclength_m));
  acc_fence->set_object_type(acc_line.object_type);
  acc_fence->set_object_id(acc_line.object_id);
  acc_fence->set_along_track_speed_mps(acc_line.along_track_speed_mps);
}

// Updates the acc fences.
[[maybe_unused]] void UpdateAccFences(
    const math::Curve2d& smoothed_nominal_curve,
    const AccDirective& acc_directive, int64_t plan_start_time,
    double ego_arclength_m,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  customer_monitor_visual->mutable_acc_fences()->Reserve(
      acc_directive.static_acc.size() +
      acc_directive.contiguous_acc_sequences.size());
  for (const auto& acc_line : acc_directive.static_acc) {
    if (acc_line.acc_safety_arclength_m < ego_arclength_m) {
      continue;
    }
    AddAccFence(acc_line, smoothed_nominal_curve, customer_monitor_visual);
  }

  for (const auto& contiguous_acc_sequence :
       acc_directive.contiguous_acc_sequences) {
    // Search the first ACC line that is later than the plan start time.
    const auto acc_iter = std::find_if(
        contiguous_acc_sequence.begin(), contiguous_acc_sequence.end(),
        [plan_start_time](const StampedAccLine& stamped_acc_line) {
          return stamped_acc_line.timestamp > plan_start_time;
        });

    // Skip if there is no one match.
    if (acc_iter == contiguous_acc_sequence.end() ||
        acc_iter == contiguous_acc_sequence.begin()) {
      continue;
    }

    if (acc_iter->acc_line.acc_safety_arclength_m < ego_arclength_m) {
      continue;
    }

    AddAccFence(acc_iter->acc_line, smoothed_nominal_curve,
                customer_monitor_visual);
  }
}

// Adds a warning agent to proto.
void AddWarningAgent(const PlannerObject& planner_object,
                     double distance_to_ego_m,
                     pb::WarningAgent* warning_agent) {
  warning_agent->set_id(planner_object.id());
  warning_agent->set_object_type(planner_object.object_type());
  warning_agent->set_distance_to_ego_m(distance_to_ego_m);
  warning_agent->set_speed_mps(planner_object.pose().speed());
  warning_agent->set_heading_rad(planner_object.pose().heading());
}

// Adds a warning cyclist agent to proto (Derived for cyclist warning).
void AddWarningCyclistAgent(const PlannerObject& planner_object,
                            double distance_to_ego_m,
                            pb::FlickerDirection flicker_direction,
                            pb::VoiceWarningType voice_warning_type,
                            pb::WarningCyclistAgent* warning_moto) {
  pb::WarningAgent warning_agent;
  warning_agent.set_id(planner_object.id());
  warning_agent.set_object_type(planner_object.object_type());
  warning_agent.set_distance_to_ego_m(distance_to_ego_m);
  warning_agent.set_speed_mps(planner_object.pose().speed());
  warning_agent.set_heading_rad(planner_object.pose().heading());
  warning_moto->mutable_general_agent_info()->CopyFrom(warning_agent);
  warning_moto->set_direction(flicker_direction);
  warning_moto->set_voice_warning_type(voice_warning_type);
}

// Calculates the distance from object to ego car.
double DistanceFromObjectToEgo(const PlannerObject& planner_object,
                               const voy::Pose& ego_pose) {
  const math::geometry::Point2d ego_pose_pt(ego_pose.x(), ego_pose.y());
  const math::geometry::Point2d object_pose_pt(
      planner_object.pose().center().x(), planner_object.pose().center().y());
  return math::geometry::Distance(object_pose_pt, ego_pose_pt);
}

// Calculates the direction from ego car to object.
// The type of ego_pose can be voy::Pose or RobotState.
template <typename T>
std::optional<double> DirectionFromEgoToObject(
    const PlannerObject& planner_object, const T& ego_pose) {
  const double delta_x = planner_object.pose().center().x() - ego_pose.x();
  const double delta_y = planner_object.pose().center().y() - ego_pose.y();
  if (math::NearZero(delta_x) && math::NearZero(delta_y)) {
    return std::nullopt;
  }
  return std::atan2(delta_y, delta_x);
}

// Adds cyclists which cut in ego car.
void AddCutInCyclists(const PlannerObject& planner_object,
                      const voy::Pose& ego_pose,
                      const math::geometry::PolylineCurve2d& nominal_path,
                      double distance_to_ego_m,
                      pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Add free space cyclist whose predicted trajectory conflicts with ego
  // nominal path.
  const auto& predicted_trajectory = planner_object.primary_trajectory();

  if (predicted_trajectory.predicted_trajectory_proto()
          .maneuver()
          .intention_type() ==
      ::prediction::pb::Maneuver::IntentionType::
          Maneuver_IntentionType_FREE_SPACE_INTENTION) {
    for (int64_t i = 0; i < kCutInCyclistCheckNum; ++i) {
      const std::optional<math::geometry::OrientedBox2d> interpolated_box =
          predicted_trajectory.GetInterpolatedBoundingBox(
              ego_pose.timestamp() + kCutInCyclistResolutionInMSec * i,
              /*use_legacy_logic=*/true);
      if (interpolated_box &&
          math::geometry::Intersects(*interpolated_box, nominal_path)) {
        AddWarningAgent(planner_object, distance_to_ego_m,
                        customer_monitor_visual->add_cut_in_agents());
        return;
      }
    }
  }
}

// Adds vehicles which cut in ego car.
void AddCutInVehicles(
    const PlannerObject& planner_object, const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& ego_lane_sequence,
    double distance_to_ego_m,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Skip if the vehicle is far from ego.
  if (distance_to_ego_m > kVehicleCutInWarningDistanceInMeter) {
    return;
  }
  // Skip if the vehicle is in completely different direction.
  const double delta_heading = math::WrapFromMinusPiToPi(
      planner_object.pose().heading() - ego_pose.yaw());
  if (fabs(delta_heading) > M_PI_2) {
    return;
  }
  // Add vehicles predicted to change lane and target lanes overlap with ego
  // lane sequence.
  const auto& intention_type = planner_object.primary_trajectory()
                                   .predicted_trajectory_proto()
                                   .maneuver()
                                   .intention_type();

  const auto& target_lane_ids =
      planner_object.primary_trajectory().maneuver().target_lane_ids();

  if (intention_type == ::prediction::pb::Maneuver::IntentionType::
                            Maneuver_IntentionType_CHANGE_LANE) {
    if (ego_lane_sequence.empty() || target_lane_ids.empty()) {
      return;
    }
    for (auto i = 1; i < target_lane_ids.size(); ++i) {
      const auto overlap_lane_it = std::find_if(
          ego_lane_sequence.begin(), ego_lane_sequence.end(),
          [target_lane_id = target_lane_ids[i]](const auto& ego_lane) {
            return ego_lane && ego_lane->id() == target_lane_id;
          });
      if (overlap_lane_it != ego_lane_sequence.end()) {
        const double agent_to_overlap_lane =
            (*overlap_lane_it)
                ->center_line()
                .GetProximity(
                    math::geometry::Point2d(planner_object.pose().center().x(),
                                            planner_object.pose().center().y()),
                    math::pb::UseExtensionFlag::kForbid)
                .dist;
        const double ego_to_overlap_lane =
            (*overlap_lane_it)
                ->center_line()
                .GetProximity(
                    math::geometry::Point2d(ego_pose.x(), ego_pose.y()),
                    math::pb::UseExtensionFlag::kForbid)
                .dist;
        if (agent_to_overlap_lane > kCutInAgentToOverlapLaneThresholdInMeter &&
            ego_to_overlap_lane < kCutInWarningDistanceThresholdInMeter) {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          customer_monitor_visual->add_cut_in_agents());
          return;
        }
      }
    }
  }
}

// Updates the agents which will cut in ego car.
void UpdateCutInAgents(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& ego_lane_sequence,
    const math::geometry::PolylineCurve2d& nominal_path,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  for (const auto& [_, planner_object] : planner_object_map) {
    // Skip if the agent is far from ego.
    const double distance_to_ego_m =
        DistanceFromObjectToEgo(planner_object, ego_pose);
    if (distance_to_ego_m > kCutInWarningDistanceThresholdInMeter) {
      continue;
    }
    // Skip if the agent is behind ego.
    const std::optional<double> direction_to_ego =
        DirectionFromEgoToObject(planner_object, ego_pose);
    if (!direction_to_ego || std::fabs(math::WrapFromMinusPiToPi(
                                 *direction_to_ego - ego_pose.yaw())) >
                                 kCutInWarningAngleThresholdInRad) {
      continue;
    }
    // Add agent which is predicted to cut in.
    // Currently, it is only used for pedestrian in the jaywalk scenario.

    const auto& intention_type = planner_object.primary_trajectory()
                                     .predicted_trajectory_proto()
                                     .maneuver()
                                     .intention_type();

    if (intention_type == ::prediction::pb::Maneuver::IntentionType::
                              Maneuver_IntentionType_CUT_IN) {
      AddWarningAgent(planner_object, distance_to_ego_m,
                      customer_monitor_visual->add_cut_in_agents());
      continue;
    }

    if (planner_object.object_type() == voy::perception::ObjectType::CYCLIST) {
      AddCutInCyclists(planner_object, ego_pose, nominal_path,
                       distance_to_ego_m, customer_monitor_visual);
    } else if (planner_object.object_type() ==
               voy::perception::ObjectType::VEHICLE) {
      AddCutInVehicles(planner_object, ego_pose, ego_lane_sequence,
                       distance_to_ego_m, customer_monitor_visual);
    }
  }
}

// Updates the agents which should be warned when ego car turns right.
void UpdateRightTurnWarningAgents(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (current_lanes.empty() ||
      current_lanes.front()->turn() != hdmap::Lane::Turn::Lane_Turn_RIGHT ||
      current_lanes.front()->type() !=
          hdmap::Lane::LaneType::Lane_LaneType_VIRTUAL) {
    return;
  }
  for (const auto& [_, planner_object] : planner_object_map) {
    // Skip if the agent is not pedestrian or cyclist.
    if (planner_object.object_type() != voy::perception::ObjectType::PED &&
        planner_object.object_type() != voy::perception::ObjectType::CYCLIST) {
      continue;
    }
    // Skip if the agent is stationary or moving very slow.
    if (planner_object.pose().speed() < kRightTurnWarningSpeedThresholdInMps) {
      continue;
    }
    // Skip if the agent is far from ego.
    const double distance_to_ego_m =
        DistanceFromObjectToEgo(planner_object, ego_pose);
    if ((planner_object.object_type() == voy::perception::ObjectType::CYCLIST &&
         distance_to_ego_m > kRightTurnWarningCyclistThresholdInMeter) ||
        (planner_object.object_type() == voy::perception::ObjectType::PED &&
         distance_to_ego_m > kRightTurnWarningPedThresholdInMeter)) {
      continue;
    }
    // Skip if the agent is not on the right side.
    if (current_lanes.front()
            ->center_line()
            .GetProximity(
                math::geometry::Point2d(planner_object.pose().center().x(),
                                        planner_object.pose().center().y()),
                math::pb::UseExtensionFlag::kAllow)
            .side != math::pb::kRight) {
      continue;
    }
    // Decide warning direction.
    const std::optional<double> direction_to_ego =
        DirectionFromEgoToObject(planner_object, ego_pose);
    if (!direction_to_ego) {
      continue;
    }
    const double delta_theta =
        math::WrapFromMinusPiToPi(*direction_to_ego - ego_pose.yaw());
    // Skip if the agent is in front of ego car.
    if (std::fabs(delta_theta) < kRightTurnWarningAngleThresholdInRad) {
      continue;
    }
    // Skip if the agent is completely different direction.
    const double delta_heading = math::WrapFromMinusPiToPi(
        planner_object.pose().heading() - ego_pose.yaw());
    if (delta_heading > (M_PI - kRightTurnWarningAngleThresholdInRad) ||
        delta_heading < -kRightTurnWarningAngleThresholdInRad) {
      continue;
    }
    AddWarningAgent(planner_object, distance_to_ego_m,
                    customer_monitor_visual->add_right_turn_warning_agents());
  }
}

// Updates the agents with potential collision with ego car.
void UpdatePotentialCollisionAgents(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const RobotState& robot_state,
    pb::PotentialCollisionAgents* potential_collision_agents) {
  const int64_t timestamp = robot_state.current_state_snapshot().timestamp();
  const math::geometry::OrientedBox2d& bounding_box =
      robot_state.current_state_snapshot().bounding_box();
  for (const auto& [_, planner_object] : planner_object_map) {
    if (planner_object
            .predicted_trajectories(/*use_filtered_trajectories=*/true)
            .empty()) {
      continue;
    }

    const std::optional<math::geometry::OrientedBox2d> interpolated_box =
        planner_object.primary_trajectory().GetInterpolatedBoundingBox(
            timestamp, /*use_legacy_logic=*/true);
    if (interpolated_box) {
      const double distance_to_ego_m =
          math::geometry::Distance(bounding_box, *interpolated_box);
      if (distance_to_ego_m <= kPotentialCollisionDistanceInMeter) {
        // Calculate the direction of potential collision.
        //   x    Front     x
        //    x     ^     x
        //     x    |    x
        //      x+--+--+x
        //       |  |  |
        //       |x   x|
        //       | x x |
        //  Left |  x  | Right
        //       | x x |
        //       +-----+
        //       x     x
        //      x       x
        //     x         x
        //    x   Back    x
        const std::optional<double> direction_to_ego = DirectionFromEgoToObject(
            planner_object, robot_state.current_state_snapshot());
        if (!direction_to_ego) {
          continue;
        }
        const double delta_theta = math::WrapFromMinusPiToPi(
            *direction_to_ego - robot_state.current_state_snapshot().heading());
        if (std::fabs(delta_theta) < kPotentialCollisionAngleInRad) {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          potential_collision_agents
                              ->add_front_potential_collision_agents());
        } else if (std::fabs(delta_theta) >
                   M_PI - kPotentialCollisionAngleInRad) {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          potential_collision_agents
                              ->add_back_potential_collision_agents());
        } else if (delta_theta > 0) {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          potential_collision_agents
                              ->add_left_potential_collision_agents());
        } else {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          potential_collision_agents
                              ->add_right_potential_collision_agents());
        }
      }
    }
  }
}

// Gets CyclistDirFromEgo (rear_profile, left_rear or right_rear)
// Defined in https://cooper.didichuxing.com/docs/document/2199581068214 2.2.a.2
// For detail, see https://cooper.didichuxing.com/docs/document/2199613092558
CyclistDirFromEgo GetDirFromEgo(const PlannerObject& planner_object,
                                const RobotStateSnapshot& ego_pose) {
  const double expand_theta = M_PI * 0.1;
  const math::geometry::OrientedBox2d ego_box = ego_pose.bounding_box();

  const double delta_x_from_rear_bumper_left =
      planner_object.pose().center().x() - ego_pose.rear_bumper_position().x() +
      (0.5 * ego_box.width() * std::cos(ego_pose.heading() + M_PI_2));
  const double delta_y_from_rear_bumper_left =
      planner_object.pose().center().y() - ego_pose.rear_bumper_position().y() +
      (0.5 * ego_box.width() * std::cos(ego_pose.heading() + M_PI_2));
  const double delta_x_from_rear_bumper_right =
      planner_object.pose().center().x() - ego_pose.rear_bumper_position().x() +
      (0.5 * ego_box.width() * std::cos(ego_pose.heading() - M_PI_2));
  const double delta_y_from_rear_bumper_right =
      planner_object.pose().center().y() - ego_pose.rear_bumper_position().y() +
      (0.5 * ego_box.width() * std::cos(ego_pose.heading() - M_PI_2));

  const double dir_from_rear_bumper_left =
      std::atan2(delta_y_from_rear_bumper_left, delta_x_from_rear_bumper_left);
  const double dir_from_rear_bumper_right = std::atan2(
      delta_y_from_rear_bumper_right, delta_x_from_rear_bumper_right);
  const double dir_from_rear_bumper_left_relative =
      math::WrapFromMinusPiToPi(dir_from_rear_bumper_left - ego_pose.heading());
  const double dir_from_rear_bumper_right_relative = math::WrapFromMinusPiToPi(
      dir_from_rear_bumper_right - ego_pose.heading());

  if (dir_from_rear_bumper_left_relative < M_PI - expand_theta &&
      dir_from_rear_bumper_left_relative > M_PI_2) {
    return CyclistDirFromEgo::LEFT_TO_EGO_REAR_PROFILE;
  } else if (dir_from_rear_bumper_right_relative > -M_PI + expand_theta &&
             dir_from_rear_bumper_right_relative < -M_PI_2) {
    return CyclistDirFromEgo::RIGHT_TO_EGO_REAR_PROFILE;
  } else if (dir_from_rear_bumper_right_relative < -M_PI + expand_theta ||
             dir_from_rear_bumper_left_relative > M_PI - expand_theta ||
             (dir_from_rear_bumper_right_relative > M_PI_2 &&
              dir_from_rear_bumper_left_relative < -M_PI_2)) {
    return CyclistDirFromEgo::IN_EGO_REAR_PROFILE;
  }
  return CyclistDirFromEgo::MOTOR_DIR_TO_EGO_OTHERS;
}

// Updates the agents which are in median strip.
void UpdateMedianStripWarningAgents(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const voy::Pose& ego_pose,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (current_lanes.empty() || !current_lanes.front()) {
    return;
  }
  const auto& current_section = current_lanes.front()->section();
  const auto& current_zones = current_section->road()->zone_infos();
  // Skip if ego car is in PDZ.
  const math::geometry::Point2d ego_pose_pt(ego_pose.x(), ego_pose.y());
  const auto pdz = std::find_if(
      current_zones.begin(), current_zones.end(), [](const auto& zone) {
        return zone.zone->type() ==
                   hdmap::Zone_ZoneType::Zone_ZoneType_PICKUP_DROPOFF ||
               zone.zone->type() ==
                   hdmap::Zone_ZoneType::Zone_ZoneType_BUS_BULB;
      });
  if (pdz != current_zones.end() &&
      math::geometry::Distance(pdz->zone->border(), ego_pose_pt) <
          kPDZDistanceThresholdInMeter) {
    return;
  }
  for (const auto& zone : current_zones) {
    const auto& zone_border = zone.zone->border();
    if (zone.zone->type() == hdmap::Zone_ZoneType::Zone_ZoneType_MEDIAN_STRIP &&
        math::geometry::Distance(zone_border, ego_pose_pt) <
            kNearMedianStripThresholdInMeter) {
      for (const auto& [_, planner_object] : planner_object_map) {
        if (planner_object.object_type() != voy::perception::ObjectType::PED) {
          continue;
        }
        if (!math::geometry::Intersects(zone_border,
                                        planner_object.pose().contour())) {
          continue;
        }
        const double distance_to_ego_m =
            DistanceFromObjectToEgo(planner_object, ego_pose);
        if (distance_to_ego_m > kMedianStripAgentThresholdInMeter) {
          continue;
        }
        // Skip the agent in bike lane.
        const auto bike_lane = std::find_if(
            current_section->lanes().begin(), current_section->lanes().end(),
            [](const auto& lane) {
              return lane->type() == hdmap::Lane::LaneType::Lane_LaneType_BIKE;
            });
        if (bike_lane != current_section->lanes().end() &&
            math::geometry::Intersects((*bike_lane)->border(),
                                       planner_object.pose().contour())) {
          continue;
        }
        const std::optional<double> direction_to_ego =
            DirectionFromEgoToObject(planner_object, ego_pose);
        if (direction_to_ego && cos(math::WrapFromMinusPiToPi(
                                    *direction_to_ego - ego_pose.yaw())) > 0) {
          AddWarningAgent(planner_object, distance_to_ego_m,
                          customer_monitor_visual->add_median_strip_agents());
        }
      }
    }
  }
}

void UpdatePullOverPullOutProgressState(
    const pb::ManeuverType& selected_maneuver_type,
    const speed::pb::PulloutReasonerSeed& pull_out_reasoner_seed,
    bool has_reached_destination,
    pb::CustomerMonitorVisual* customer_monitor_visual,
    pb::PullOverPullOutProgressState*
        pull_over_pull_out_progress_state_last_cycle,
    int* pull_out_success_cycle_count, bool* has_sent_pull_out_success) {
  if (selected_maneuver_type == pb::ManeuverType::PULL_OVER) {
    customer_monitor_visual->set_pullover_pullout_progress_state(
        has_reached_destination
            ? pb::PullOverPullOutProgressState::PULL_OVER_SUCCESS
            : pb::PullOverPullOutProgressState::PULL_OVER_TRIGGERED);
  } else if (pull_out_reasoner_seed.is_triggered()) {
    customer_monitor_visual->set_pullover_pullout_progress_state(
        pull_out_reasoner_seed.is_ready_to_go()
            ? pb::PullOverPullOutProgressState::PULL_OUT_READY_TO_GO
            : pb::PullOverPullOutProgressState::PULL_OUT_TRIGGERED);
    // Reset has_sent_pull_out_success_ if a new pull out is triggered.
    *has_sent_pull_out_success = false;
  } else if ((pull_out_reasoner_seed
                  .has_pull_out_finished_for_current_route() &&
              !(*has_sent_pull_out_success)) ||
             (*pull_over_pull_out_progress_state_last_cycle ==
                  pb::PullOverPullOutProgressState::PULL_OUT_SUCCESS &&
              *pull_out_success_cycle_count < kPullOutSuccessSignalMaxCount)) {
    // Publish at most kPullOutSuccessSignalMaxCount iterations of
    // PullOutSuccess Signals if no interruptions occurs.
    customer_monitor_visual->set_pullover_pullout_progress_state(
        pb::PullOverPullOutProgressState::PULL_OUT_SUCCESS);
    (*pull_out_success_cycle_count)++;
    *has_sent_pull_out_success = true;
  } else {
    customer_monitor_visual->set_pullover_pullout_progress_state(
        pb::PullOverPullOutProgressState::NOT_IN_PULLOVER_PULLOUT);
  }

  // Update PullOverPullOutProgressState related variables.
  if (customer_monitor_visual->pullover_pullout_progress_state() !=
      pb::PullOverPullOutProgressState::PULL_OUT_SUCCESS) {
    *pull_out_success_cycle_count = 0;
  }
  *pull_over_pull_out_progress_state_last_cycle =
      customer_monitor_visual->pullover_pullout_progress_state();
}

// Updates customer monitor visualization of selected maneuver.
void UpdateManeuverVisual(const ManeuverCustomerMonitorVisual& maneuver_visual,
                          pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Update the decision of the traffic participants.
  for (const auto& acc_agent_id : maneuver_visual.acc_agent_ids) {
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->add_acc_agent_ids(acc_agent_id);
  }
  for (const auto& evade_agent_id : maneuver_visual.evade_agent_ids) {
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->add_evade_agent_ids(evade_agent_id);
  }
  for (const auto& nudge_agent_id : maneuver_visual.nudge_agent_ids) {
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->add_nudge_agent_ids(nudge_agent_id);
  }
  customer_monitor_visual->mutable_traffic_participants()->Reserve(
      maneuver_visual.traffic_participants.size());
  for (const auto& traffic_participant : maneuver_visual.traffic_participants) {
    auto agent = customer_monitor_visual->add_traffic_participants();
    *agent = traffic_participant;
  }

  // Update the distance related to junction.
  if (maneuver_visual.junction_entrance_distance_m) {
    customer_monitor_visual->mutable_junction_entrance_distance_m()->set_value(
        *(maneuver_visual.junction_entrance_distance_m));
  }
  if (maneuver_visual.junction_exit_distance_m) {
    customer_monitor_visual->mutable_junction_exit_distance_m()->set_value(
        *(maneuver_visual.junction_exit_distance_m));
  }

  // Update the traffic light in lane sequence.
  *customer_monitor_visual->mutable_traffic_light_in_lane_sequence() =
      maneuver_visual.traffic_light_in_lane_sequence;

  // Update turning information.
  customer_monitor_visual->mutable_turning_information()->set_turn_mode(
      maneuver_visual.turn_mode);
  const std::optional<double>& distance_to_next_turn_m =
      maneuver_visual.distance_to_next_turn_m;
  if (distance_to_next_turn_m) {
    customer_monitor_visual->mutable_turning_information()
        ->mutable_distance_to_next_turn_m()
        ->set_value(*distance_to_next_turn_m);
  }
  customer_monitor_visual->set_ego_arclength_m(maneuver_visual.ego_arclength_m);

  customer_monitor_visual->set_current_lane_speed_limit_mps(
      maneuver_visual.current_lane_speed_limit_mps);
}

// Updates road boundary.
void UpdateRoadBoundary(
    const std::vector<double>& lateral_clearances,
    const LateralClearanceCorridorDirective& road_corridor,
    const math::Curve2d& nominal_curve,
    ::google::protobuf::RepeatedPtrField<::voy::Point2d>* road_boundary) {
  for (size_t i = 0; i < lateral_clearances.size(); ++i) {
    const double lateral_clearance = lateral_clearances[i];
    if (std::abs(lateral_clearance) > road_corridor.max_abs_lateral_clearance -
                                          kRoadBoundaryDrawingFilterInMeter) {
      continue;
    }
    const double arclength =
        road_corridor.reference_range.start_pos + i * road_corridor.resolution;
    auto road_boundary_pt = road_boundary->Add();
    road_boundary_pt->set_x(nominal_curve.GetInterpX(arclength) -
                            lateral_clearance *
                                nominal_curve.GetInterpDerivY(arclength));
    road_boundary_pt->set_y(nominal_curve.GetInterpY(arclength) +
                            lateral_clearance *
                                nominal_curve.GetInterpDerivX(arclength));
  }
}

// Updates lane geometry visual and drivable space
// for waypoint assist under decoupled arch.
// Using input_path from speed reasoning and
// left/right boundaries from path searcher debug.
void UpdateLaneGeometryVisualForRemoteAssist(
    const pb::DecoupledManeuverDebug& decoupled_manuver_debug,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  customer_monitor_visual->mutable_drivable_space_left_boundary()->Clear();
  customer_monitor_visual->mutable_drivable_space_right_boundary()->Clear();
  // find the selected identifier for the trajectory.
  std::string selected_label;
  const auto& traj_meta_debugs =
      decoupled_manuver_debug.trajectory_selection_debug()
          .trajectory_meta_debugs();
  for (const auto& traj_meta : traj_meta_debugs) {
    if (traj_meta.is_selected()) {
      selected_label = traj_meta.label();
      break;
    }
  }
  if (selected_label.empty()) {
    LOG(ERROR) << "selected_label not found:" << selected_label;
    return;
  }
  // find the selected identifier with speed_generator_debug.
  for (const auto& single_lane_sequence_plan_debug :
       decoupled_manuver_debug.single_lane_sequence_plan_debug()) {
    for (const auto& single_intention_debug :
         single_lane_sequence_plan_debug.single_intention_plan_debug()) {
      if (single_intention_debug.candidate_identifier() == selected_label) {
        // set input_path.stitching_path_boundary to customer monitor visual.
        customer_monitor_visual->mutable_left_lane_boundary()->CopyFrom(
            single_intention_debug.speed_generator_debug()
                .input_path_strict_boundary()
                .left_boundary()
                .points());
        customer_monitor_visual->mutable_right_lane_boundary()->CopyFrom(
            single_intention_debug.speed_generator_debug()
                .input_path_strict_boundary()
                .right_boundary()
                .points());
        // set path_searcher_input_debug.hardboundaries to customer monitor
        // visual.
        const auto& left_hard_boundaries =
            single_intention_debug.path_planner_debug()
                .path_searcher_debug()
                .path_searcher_input_debug()
                .left_hard_boundaries();
        for (const auto& left_hard_boundary : left_hard_boundaries) {
          for (const auto& segment : left_hard_boundary.segments()) {
            for (const auto& pt : segment.points()) {
              auto left_hard_boundary_pt =
                  customer_monitor_visual
                      ->mutable_drivable_space_left_boundary()
                      ->Add();
              left_hard_boundary_pt->set_x(pt.x());
              left_hard_boundary_pt->set_y(pt.y());
            }
          }
        }
        const auto& right_hard_boundaries =
            single_intention_debug.path_planner_debug()
                .path_searcher_debug()
                .path_searcher_input_debug()
                .right_hard_boundaries();
        for (const auto& right_hard_boundary : right_hard_boundaries) {
          for (const auto& segment : right_hard_boundary.segments()) {
            for (const auto& pt : segment.points()) {
              auto right_hard_boundary_pt =
                  customer_monitor_visual
                      ->mutable_drivable_space_right_boundary()
                      ->Add();
              right_hard_boundary_pt->set_x(pt.x());
              right_hard_boundary_pt->set_y(pt.y());
            }
          }
        }
        break;
      }
    }
  }
}

// Updates lane geometry visual.
void UpdateLaneGeometryVisual(
    const RobotState& robot_state, const LaneGeometryDirective& lane_geometry,
    double planing_horizon,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Update left/right lane boundary.
  customer_monitor_visual->mutable_left_lane_boundary()->Reserve(
      lane_geometry.left_lane_boundary->points().size());
  customer_monitor_visual->mutable_right_lane_boundary()->Reserve(
      lane_geometry.right_lane_boundary->points().size());
  for (const auto& pt : lane_geometry.left_lane_boundary->points()) {
    auto left_lane_boundary_pt =
        customer_monitor_visual->add_left_lane_boundary();
    left_lane_boundary_pt->set_x(pt.x());
    left_lane_boundary_pt->set_y(pt.y());
  }
  for (const auto& pt : lane_geometry.right_lane_boundary->points()) {
    auto right_lane_boundary_pt =
        customer_monitor_visual->add_right_lane_boundary();
    right_lane_boundary_pt->set_x(pt.x());
    right_lane_boundary_pt->set_y(pt.y());
  }

  // Translate the lane geometry's hard boundary lines to the lateral clearance
  // based descrete points, and extract the nearest points if there are
  // multi-hard boundary lines. This is for keep the same to the legacy
  // maneuver.
  // TODO(hongda): Redefine the hard boundary plot with product team and
  // operation team. Or directly get the descrete hard boundary points from the
  // decoupled maneuver.
  LateralClearanceCorridorDirectiveGenerator road_corridor_generator(
      kRayCastingResolutionForVisualizationInMeter,
      constants::kDefaultMaxRayCastingSearchDistanceInMeter);

  std::vector<math::geometry::PolylineCurve2d> left_hard_boundary_lines;
  std::vector<math::geometry::PolylineCurve2d> right_hard_boundary_lines;
  left_hard_boundary_lines.reserve(
      lane_geometry.left_hard_boundary_lines.size());
  right_hard_boundary_lines.reserve(
      lane_geometry.right_hard_boundary_lines.size());
  for (const auto& left_hard_boundary_line :
       lane_geometry.left_hard_boundary_lines) {
    left_hard_boundary_lines.emplace_back(left_hard_boundary_line);
  }
  for (const auto& right_hard_boundary_line :
       lane_geometry.right_hard_boundary_lines) {
    right_hard_boundary_lines.emplace_back(right_hard_boundary_line);
  }

  const double ego_arclength =
      lane_geometry.nominal_path
          ->GetProximity({robot_state.current_state_snapshot().x(),
                          robot_state.current_state_snapshot().y()},
                         math::pb::UseExtensionFlag::kForbid)
          .arc_length;
  const double start_arclength =
      std::max(ego_arclength - kRayCastingRangeOffsetInMeter, 0.0);
  const double end_arclength =
      std::min(ego_arclength + planing_horizon + kRayCastingRangeOffsetInMeter,
               lane_geometry.nominal_path->GetTotalArcLength());

  // Update left/right road boundary.
  math::Range1d planning_horizon_range(start_arclength, end_arclength);
  const auto road_corridor = road_corridor_generator.GenerateCorridor(
      *lane_geometry.nominal_path, planning_horizon_range,
      left_hard_boundary_lines, right_hard_boundary_lines);

  const math::Curve2d nominal_curve(*lane_geometry.nominal_path,
                                    math::pb::Interpolation1dType::kLinear);

  UpdateRoadBoundary(road_corridor.left_lateral_clearances, road_corridor,
                     nominal_curve,
                     customer_monitor_visual->mutable_left_road_boundary());
  UpdateRoadBoundary(road_corridor.right_lateral_clearances, road_corridor,
                     nominal_curve,
                     customer_monitor_visual->mutable_right_road_boundary());
}

// Add traffic participant.
[[maybe_unused]] void AddTrafficParticipant(
    const AgentInLaneStates& st_agent, pb::AgentSnapshotDecision decision,
    std::vector<pb::TrafficParticipant>* traffic_participants) {
  pb::TrafficParticipant traffic_participant;
  traffic_participant.set_agent_id(st_agent.object_id);
  traffic_participant.set_decision(decision);
  *traffic_participant.mutable_pose() =
      st_agent.tracked_state.inlane_param.pose.ToProto();
  traffic_participants->emplace_back(traffic_participant);
}

// Returns the traffic light attention corresponding to the traffic light stop
// line and the color.
pb::EgoIntensionAttention GetTrafficLightAttention(
    const voy::TrafficLight& traffic_light, bool has_traffic_light_stop_line) {
  switch (traffic_light.color()) {
    case voy::TrafficLight_Color::TrafficLight_Color_RED:
      return has_traffic_light_stop_line
                 ? pb::EgoIntensionAttention::WAIT_RED_LIGHT
                 : pb::EgoIntensionAttention::PASS_RED_LIGHT;
    case voy::TrafficLight_Color::TrafficLight_Color_YELLOW:
      return has_traffic_light_stop_line
                 ? pb::EgoIntensionAttention::WAIT_YELLOW_LIGHT
                 : pb::EgoIntensionAttention::PASS_YELLOW_LIGHT;
    case voy::TrafficLight_Color::TrafficLight_Color_GREEN:
      if (traffic_light.is_flashing()) {
        return has_traffic_light_stop_line
                   ? pb::EgoIntensionAttention::WAIT_GREEN_FLASHING_LIGHT
                   : pb::EgoIntensionAttention::PASS_GREEN_FLASHING_LIGHT;
      }
      return pb::EgoIntensionAttention::PASS_GREEN_LIGHT;
    case voy::TrafficLight_Color::TrafficLight_Color_UNKNOWN_COLOR:
      return has_traffic_light_stop_line
                 ? pb::EgoIntensionAttention::WAIT_UNKNOWN_LIGHT
                 : pb::EgoIntensionAttention::PASS_UNKNOWN_LIGHT;
    default:
      DCHECK(false) << "Unknown traffic light color type.";
  }
  return pb::EgoIntensionAttention::WAIT_RED_LIGHT;
}

// Updates the yield attention information.
void UpdateYieldAttentionInfo(
    const std::vector<StopLine>& stop_directive, double ego_speed,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  const auto nearest_stop_fence =
      std::min_element(stop_directive.begin(), stop_directive.end(),
                       [](const auto& lhs, const auto& rhs) {
                         return lhs.stop_arclength_m < rhs.stop_arclength_m;
                       });
  const auto& acc_fences = customer_monitor_visual->acc_fences();
  const auto nearest_acc_fence =
      std::min_element(acc_fences.begin(), acc_fences.end(),
                       [](const auto& lhs, const auto& rhs) {
                         return lhs.arclength_m() < rhs.arclength_m();
                       });
  const double ego_arclength_m = customer_monitor_visual->ego_arclength_m();
  if (nearest_stop_fence != stop_directive.end() &&
      (nearest_acc_fence == acc_fences.end() ||
       nearest_acc_fence->arclength_m() >
           nearest_stop_fence->stop_arclength_m)) {
    // Skip if the stop line is not yield or crosswalk type.
    if (nearest_stop_fence->type !=
            pb::StopLineType::Enum::StopLineType_Enum_YIELD &&
        nearest_stop_fence->type !=
            pb::StopLineType::Enum::StopLineType_Enum_CROSSWALK) {
      return;
    }

    // Skip if the stop line is far from the ego vehicle.
    if (nearest_stop_fence->stop_arclength_m - ego_arclength_m >
        kYieldAgentWarningThresholdInMeter) {
      return;
    }

    // Generate yield attention information from the nearest stop fence.
    pb::YieldAttentionInfo* yield_info =
        customer_monitor_visual->add_yield_attention_infos();
    yield_info->set_type(nearest_stop_fence->type);
    if (nearest_stop_fence->type == pb::StopLineType::CROSSWALK) {
      const CrosswalkReasonerStopLineMetadata* metadata =
          boost::get<CrosswalkReasonerStopLineMetadata>(
              &base::CheckAndGetValue(nearest_stop_fence->metadata));
      yield_info->set_object_id(DCHECK_NOTNULL(metadata)->agent_id());
    } else if (nearest_stop_fence->type == pb::StopLineType::YIELD) {
      const YieldReasonerStopLineMetadata* metadata =
          boost::get<YieldReasonerStopLineMetadata>(
              &base::CheckAndGetValue(nearest_stop_fence->metadata));
      yield_info->set_object_id(DCHECK_NOTNULL(metadata)->yielding_object_id());
    }
    yield_info->set_object_type(nearest_stop_fence->object_type);
    yield_info->set_arclength_distance_m(nearest_stop_fence->stop_arclength_m -
                                         ego_arclength_m);
  } else if (nearest_acc_fence != acc_fences.end() &&
             (nearest_stop_fence == stop_directive.end() ||
              nearest_acc_fence->arclength_m() <
                  nearest_stop_fence->stop_arclength_m)) {
    // Skip static acc agent.
    if (math::NearZero(nearest_acc_fence->along_track_speed_mps())) {
      return;
    }

    // Skip if the along track speed is not negative and the ego speed is not
    // needed to slow down a lot to acc the front agent.
    if (nearest_acc_fence->along_track_speed_mps() >
            -math::constants::kEpsilon &&
        (ego_speed - nearest_acc_fence->along_track_speed_mps()) <
            kYieldAccAgentWarningSpeedDiffInMps) {
      return;
    }

    // Skip if the acc fence is far from the ego vehicle.
    if (nearest_acc_fence->arclength_m() - ego_arclength_m >
        kYieldAgentWarningThresholdInMeter) {
      return;
    }

    // Generate yield attention information from the nearest acc fence.
    pb::YieldAttentionInfo* yield_info =
        customer_monitor_visual->add_yield_attention_infos();
    yield_info->set_type(pb::StopLineType::Enum::StopLineType_Enum_YIELD);
    yield_info->set_object_id(nearest_acc_fence->object_id());
    yield_info->set_object_type(nearest_acc_fence->object_type());
    yield_info->set_arclength_distance_m(nearest_acc_fence->arclength_m() -
                                         ego_arclength_m);
  }
}

// Returns the optional guidance turn mode. Nullopt means the turn is not needed
// to be warned.
std::optional<pb::TurnMode> GetRouteGuidanceTurnMode(
    const pnc_map::Lane& lane) {
  // U turn in junction and not in junction are both needed to be warned.
  if (lane.turn() == hdmap::Lane::Turn::Lane_Turn_U_TURN) {
    return pb::TurnMode::U_TURN;
  }

  if (!lane.IsInJunction()) {
    return std::nullopt;
  }

  // Filter the straight turn in small junctions.
  const pnc_map::Junction* junction = lane.section()->road()->junction();
  if (!junction) {
    return std::nullopt;
  }
  if (junction->roads().size() < kMinRoadNumInRouteGuidanceWaringJunction &&
      lane.turn() == hdmap::Lane::Turn::Lane_Turn_STRAIGHT) {
    return std::nullopt;
  }

  switch (lane.turn()) {
    case hdmap::Lane::Turn::Lane_Turn_LEFT:
      return pb::TurnMode::LEFT_TURN;
    case hdmap::Lane::Turn::Lane_Turn_RIGHT:
      return pb::TurnMode::RIGHT_TURN;
    case hdmap::Lane::Turn::Lane_Turn_STRAIGHT:
      return pb::TurnMode::STRAIGHT;
    default:
      // Other types of turns are not needed to be warned.
      return std::nullopt;
  }

  return std::nullopt;
}

// Updates the route guidance for guiding the ops to drive the route provided by
// routing node. It is based on the routing regional path information instead of
// the planner behavior decision..
void UpdateRouteGuidance(const std::vector<const pnc_map::Lane*>& route_lanes,
                         const math::geometry::Point2d& front_bumper_position,
                         pb::RouteGuidance* route_guidance) {
  if (route_lanes.empty()) {
    return;
  }

  const pnc_map::Lane* last_lane = route_lanes.front();
  // If the ego vehicle is on the lane with route guidance warning, do not
  // output the next route guidance to prevent misleading by returning an
  // invalid lane id.
  if (!last_lane || GetRouteGuidanceTurnMode(*last_lane)) {
    route_guidance->set_lane_id(kInvalidRouteGuidanceWaringLaneId);
    return;
  }

  double accumulated_distance =
      last_lane->length() -
      last_lane->center_line()
          .GetProximity(front_bumper_position,
                        math::pb::UseExtensionFlag::kAllow)
          .arc_length;
  for (size_t i = 1; i < route_lanes.size(); ++i) {
    const auto turn_mode = GetRouteGuidanceTurnMode(*route_lanes[i]);
    if (turn_mode) {
      route_guidance->set_turn_mode(*turn_mode);
      route_guidance->set_distance_to_next_turn_m(accumulated_distance);
      route_guidance->set_lane_id(route_lanes[i]->id());
      return;
    }

    // Check successor lane to avoid repeated calculation of distance.
    if (last_lane->IsSuccessor(*route_lanes[i])) {
      accumulated_distance += route_lanes[i]->length();
    }
    last_lane = route_lanes[i];
  }

  // Return a invalid lane id if no turn needed to be warned is found.
  route_guidance->set_lane_id(kInvalidRouteGuidanceWaringLaneId);
  route_guidance->set_distance_to_next_turn_m(accumulated_distance);
}

// Checks whether the traffic light attentions should be output.
bool ShouldOutputTrafficLightAttentions(
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const math::geometry::PolylineCurve2d& nominal_path,
    const pb::CustomerMonitorVisual& customer_monitor_visual) {
  if (!customer_monitor_visual.has_traffic_light_in_lane_sequence() ||
      current_lanes.empty()) {
    return false;
  }

  const pnc_map::Lane* current_lane = current_lanes.front();

  // Early special check for left turn waiting area. Output the traffic light
  // attentions if the ego vehicle is behind the watch line.
  if (current_lane->turn() == hdmap::Lane::Turn::Lane_Turn_LEFT &&
      current_lane->IsInJunction() && current_lane->watch_line()) {
    const double stop_line_arclength =
        lane_selection::GetStopLineOrWatchLineArclength(
            base::CheckAndGetValue(current_lane->watch_line()), nominal_path);
    if (stop_line_arclength > customer_monitor_visual.ego_arclength_m()) {
      return true;
    }
    return false;
  }

  // Skip if the vehicle has driven in junction.
  if (current_lane->IsInJunction()) {
    return false;
  }

  // Early special check for u-turn not in junction.
  const auto& turning_information =
      customer_monitor_visual.turning_information();
  if (turning_information.turn_mode() == pb::TurnMode::U_TURN &&
      turning_information.has_distance_to_next_turn_m() &&
      turning_information.distance_to_next_turn_m().value() <
          kTrafficLightWarningThresholdInMeter) {
    return true;
  }

  // Skip if the vehicle is not drive close to the entrance of junction.
  if (!customer_monitor_visual.has_junction_entrance_distance_m() ||
      customer_monitor_visual.junction_entrance_distance_m().value() >
          kTrafficLightWarningThresholdInMeter ||
      customer_monitor_visual.junction_entrance_distance_m().value() < 0) {
    return false;
  }

  return true;
}

// Checks whether the ego vehicle is out of the roads of hdmap.
bool IsEgoOutOfRoad(const math::geometry::OrientedBox2d& ego_bounding_box,
                    const std::vector<const pnc_map::Lane*>& current_lanes) {
  // The ego vehicle is out of road if no near lanes are found.
  if (current_lanes.empty()) {
    return true;
  }

  for (const pnc_map::Lane* lane : current_lanes) {
    const pnc_map::Road* road = lane->section()->road();
    if (math::geometry::Intersects(ego_bounding_box, road->border())) {
      return false;
    }

    if (road->junction() && math::geometry::Intersects(
                                ego_bounding_box, road->junction()->border())) {
      return false;
    }

    if (road->opposite_road() &&
        math::geometry::Intersects(ego_bounding_box,
                                   road->opposite_road()->border())) {
      return false;
    }
  }

  // The ego vehicle is out of road if it is not intersected with nearby roads
  // and junctions.
  return true;
}

// Checks whether the bounding box is collided with dynamic agents.
bool IsBoundingBoxCollidedWithDynamicAgents(
    const math::geometry::OrientedBox2d& bounding_box,
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map) {
  for (const auto& [_, planner_object] : planner_object_map) {
    // Skip static agents.
    if (planner_object.pose().speed() <
        planner::constants::kDefaultAgentNearStaticSpeedInMps) {
      continue;
    }

    // Skip the agents which are far away from the ego vehicle.
    if (math::geometry::ComparableDistance(bounding_box.center(),
                                           planner_object.pose().center_2d()) >
        kAgentFarAwayDistanceInSqrMeter) {
      continue;
    }

    if (math::geometry::Intersects(bounding_box,
                                   planner_object.pose().contour())) {
      return true;
    }
  }

  return false;
}

// Checks whether there is potential collision during nudging dynamic agents.
bool CheckPotentialNudgeCollision(
    const SpeedWorldModel& world_model,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const math::geometry::PolylineCurve2d& nominal_path,
    pb::ManeuverType maneuver_type) {
  if (maneuver_type != pb::ManeuverType::LANE_FOLLOW) {
    return false;
  }

  const RobotStateSnapshot& curr_snapshot =
      world_model.robot_state().current_state_snapshot();
  if (curr_snapshot.speed() < kPotentialNudgeCollisionEgoSpeedThresholdInMps) {
    return false;
  }

  if (current_lanes.empty()) {
    return false;
  }
  const pnc_map::Lane* current_lane = current_lanes.front();
  if (current_lane->IsInJunction() ||
      current_lane->turn() == hdmap::Lane::Turn::Lane_Turn_U_TURN) {
    return false;
  }

  // Check whether the ego vehicle is nudging and whether it steers to the lane
  // center.
  const auto front_bumper_proximity =
      nominal_path.GetProximity(curr_snapshot.front_bumper_position(),
                                math::pb::UseExtensionFlag::kAllow);
  const auto rear_bumper_proximity = nominal_path.GetProximity(
      curr_snapshot.rear_bumper_position(), math::pb::UseExtensionFlag::kAllow);
  if (front_bumper_proximity.dist <
          kPotentialNudgeCollisionLateralDisThresholdInMeter ||
      rear_bumper_proximity.dist <
          kPotentialNudgeCollisionLateralDisThresholdInMeter ||
      front_bumper_proximity.side != rear_bumper_proximity.side) {
    return false;
  }
  if (front_bumper_proximity.side == math::pb::kLeft &&
      curr_snapshot.steering() >
          -kPotentialNudgeCollisionEgoSteerThresholdInRad) {
    return false;
  }
  if (front_bumper_proximity.side == math::pb::kRight &&
      curr_snapshot.steering() <
          kPotentialNudgeCollisionEgoSteerThresholdInRad) {
    return false;
  }

  // Construct a bounding box from the front half part of ego vehicle to nominal
  // path and check whether it is collided with agents.
  const auto& shape_measurement =
      world_model.robot_state().car_model_with_shape().shape_measurement();
  const double geometry_center_to_rear_axle =
      shape_measurement.geometry_center_to_rear_axle();
  const auto& rear_axle_position = curr_snapshot.rear_axle_position();
  const double heading = curr_snapshot.heading();
  const double half_length = shape_measurement.length() / 2;
  const double geometry_center_x =
      rear_axle_position.x() +
      (cos(heading) * (geometry_center_to_rear_axle + half_length / 2)) +
      (kBoundingBoxDilationWidthBufferInMeter * sin(heading) *
       (front_bumper_proximity.side == math::pb::kLeft ? 1.0 : -1.0));
  const double geometry_center_y =
      rear_axle_position.y() +
      (sin(heading) * (geometry_center_to_rear_axle + half_length / 2)) +
      (kBoundingBoxDilationWidthBufferInMeter * cos(heading) *
       (front_bumper_proximity.side == math::pb::kLeft ? -1.0 : 1.0));
  const math::geometry::OrientedBox2d bounding_box(
      geometry_center_x, geometry_center_y, half_length,
      shape_measurement.width() + (kBoundingBoxDilationWidthBufferInMeter * 2),
      heading);
  if (!IsBoundingBoxCollidedWithDynamicAgents(
          bounding_box, world_model.planner_object_map())) {
    return false;
  }

  // There is potential nudge collision if the conditions above are all passed.
  return true;
}

void UpdateStopCellsInfo(const SpeedWorldModel& world_model,
                         pb::CustomerMonitorVisual* customer_monitor_visual) {
  customer_monitor_visual->mutable_stop_cells_info()->Clear();
  if (world_model.pickup_dropoff_zone_infos().empty()) {
    return;
  }
  for (const auto& raw_stop_cell :
       world_model.pickup_dropoff_zone_infos()[0].raw_stop_cells) {
    auto* stop_cell = customer_monitor_visual->add_stop_cells_info();
    stop_cell->set_is_parking_allowed(raw_stop_cell.parking_info().type() ==
                                      hdmap::CellParkingInfo_Type_ALLOW);
    for (const auto& loop : raw_stop_cell.border().loops()) {
      for (const auto& raw_pt : loop.points()) {
        voy::Point2d* pt = stop_cell->add_contour();
        pt->set_x(raw_pt.x());
        pt->set_y(raw_pt.y());
      }
    }
  }
}

// Updates the information of PDZ for visualization in pull over.
void UpdatePickupDropoffZoneVisualInfo(
    const SpeedWorldModel& world_model,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (world_model.pickup_dropoff_zone_infos().empty() ||
      !world_model.pickup_dropoff_zone_infos().front().border.has_value()) {
    return;
  }

  // Add border of pdz.
  const auto& pdz_info = world_model.pickup_dropoff_zone_infos().front();
  for (const auto& point : pdz_info.border.value()) {
    voy::Point2d* p_border = customer_monitor_visual->mutable_pdz_info()
                                 ->mutable_border()
                                 ->add_points();
    p_border->set_x(point.x());
    p_border->set_y(point.y());
  }

  // Add center point of pdz.
  const math::geometry::PolylineCurve2d& nominal_path = pdz_info.nominal_path;
  math::geometry::Point2d center_point =
      nominal_path.GetInterp(0.5 * nominal_path.GetTotalArcLength());
  const double center_point_yaw =
      nominal_path.GetInterpTheta(0.5 * nominal_path.GetTotalArcLength());

  if (pdz_info.left_boundary.has_value()) {
    // Adjust PDZ nominal path to the midpoint between the left and right
    // boundaries of PDZ.
    const auto left_boundary_proximity = pdz_info.left_boundary->GetProximity(
        center_point, math::pb::UseExtensionFlag::kAllow);
    const auto right_boundary_proximity = pdz_info.right_boundary.GetProximity(
        center_point, math::pb::UseExtensionFlag::kAllow);
    center_point = {
        (left_boundary_proximity.x + right_boundary_proximity.x) / 2.0,
        (left_boundary_proximity.y + right_boundary_proximity.y) / 2.0};
  }

  math::pb::Pose2d* p_center =
      customer_monitor_visual->mutable_pdz_info()->mutable_center_pose();
  p_center->set_x(center_point.x());
  p_center->set_y(center_point.y());
  p_center->set_yaw(center_point_yaw);
}

// Updates the cell risk info. Cell risk info represents the risk level of
// traffic law violations and the time limit to pullover in target cell.
// Estimated destination cell risk info represents the max cell risk info near
// destination, while current cell risk info represents the cell risk info of
// current ego position.
void UpdateCellRiskInfo(const SpeedWorldModel& world_model,
                        pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Update the estimated cell risk info near destination.
  if (!world_model.pickup_dropoff_zone_infos().empty()) {
    const auto& pdz_info = world_model.pickup_dropoff_zone_infos().front();
    if (pdz_info.estimated_destination_cell_risk_info.has_value()) {
      *customer_monitor_visual->mutable_estimated_destination_cell_risk_info() =
          pdz_info.estimated_destination_cell_risk_info.value();
    }
  }

  // Update cell risk info of current ego position if ego is static.
  const auto& current_snapshot =
      world_model.robot_state().current_state_snapshot();
  if (current_snapshot.speed() < kStopSpeedForPulloverThresholdInMps) {
    // Update current cell risk info when ego is static.
    hdmap::StopCellRiskQueryInfo stop_cell_risk_query_info;
    stop_cell_risk_query_info.mutable_ego_pose()->set_x(current_snapshot.x());
    stop_cell_risk_query_info.mutable_ego_pose()->set_y(current_snapshot.y());
    stop_cell_risk_query_info.set_timestamp_s(world_model.snapshot_timestamp() /
                                              1000);
    *customer_monitor_visual->mutable_current_cell_risk_info() =
        world_model.pnc_map_service()
            ->hdmap()
            ->QueryEgoStopCellRiskLevel(stop_cell_risk_query_info,
                                        hdmap::StopCellQueryInfo())
            .cell_risk_info();
  }
}

void UpdatePulloverForwardRetryInfo(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    pb::PullOverForwardRetryInfo* pullover_forward_retry_info) {
  *pullover_forward_retry_info->mutable_immediate_pullover_info() =
      world_model.immediate_pull_over_state_manager().immediate_pullover_info();
  if (behavior_decision.pull_over_status_info.has_value() &&
      behavior_decision.pull_over_status_info->pull_over_dest.has_value()) {
    pullover_forward_retry_info->mutable_pullover_destination()->set_x(
        behavior_decision.pull_over_status_info->pull_over_dest->x());
    pullover_forward_retry_info->mutable_pullover_destination()->set_y(
        behavior_decision.pull_over_status_info->pull_over_dest->y());
  }

  if ((mrc_request_ptr != nullptr &&
       mrc_request_ptr->mrm_progress() != mrc::pb::MrcProgress::NONE) ||
      (pullover_forward_retry_info->immediate_pullover_info()
               .immediate_pullover_source() !=
           routing::pb::ImmediatePullOverSource::kNoneSource &&
       pullover_forward_retry_info->immediate_pullover_info()
               .immediate_pullover_source() !=
           routing::pb::ImmediatePullOverSource::kPlanning)) {
    pullover_forward_retry_info->set_is_non_destination_pullover(true);
  }

  // When ego is far away from destination and immediate pull over jumps from
  // kTriggered to kNotTriggered, set cancel_pullover_forward_retry to true.
  const bool is_immediate_pullover_cancelled =
      !behavior_decision.should_trigger_immediate_pull_over &&
      world_model.immediate_pull_over_state_manager().IsTriggeredByPlanning();
  if (!is_immediate_pullover_cancelled) {
    pullover_forward_retry_info->set_cancel_pullover_forward_retry(false);
    return;
  }

  const auto& current_state_snapshot =
      world_model.robot_state().current_state_snapshot();
  const math::geometry::Point2d ego_position(current_state_snapshot.x(),
                                             current_state_snapshot.y());
  const auto& destination =
      world_model.global_route_solution()->route_solution().destination();
  const math::geometry::Point2d dest_point(destination.position().x(),
                                           destination.position().y());
  const bool is_ego_far_away_from_destination =
      (world_model.immediate_pull_over_state_manager().accumulated_distance() >
       kMaxDistanceToTryImmediatePullOverInMeter +
           world_model.immediate_pull_over_state_manager()
               .initial_dist_to_destination());
  pullover_forward_retry_info->set_cancel_pullover_forward_retry(
      is_ego_far_away_from_destination);
}

void UpdateTimeAndMileageStatusForCustomerTriggeredImmediatePullOver(
    const SpeedWorldModel& world_model,
    pb::CustomerTriggeredImmediatePullOverInfo*
        customer_triggered_immediate_pullover_info) {
  const double duration_in_ms = world_model.snapshot_timestamp() -
                                world_model.immediate_pull_over_state_manager()
                                    .immediate_pullover_triggered_timestamp();
  const double mileage_in_meter =
      world_model.immediate_pull_over_state_manager().accumulated_distance();
  if (world_model.immediate_pull_over_state_manager()
              .immediate_pullover_state() ==
          routing::pb::ImmediatePullOverState::kSuccess ||
      (duration_in_ms < kTimeThresholdToPopUpCancelImmediatePullOverInMSec &&
       mileage_in_meter <
           kDistanceThresholdToPopUpCancelImmediatePullOverInMeter)) {
    customer_triggered_immediate_pullover_info->set_time_and_mileage_status(
        pb::TimeAndMileageStatus::kWithinExpectedRange);
  } else if (duration_in_ms > kMaxTimeThresholdToTryImmediatePullOverInMSec ||
             mileage_in_meter >
                 kMaxDistanceThresholdToTryImmediatePullOverInMeter) {
    customer_triggered_immediate_pullover_info->set_time_and_mileage_status(
        pb::TimeAndMileageStatus::kOutOfExpectedRange);
  } else {
    customer_triggered_immediate_pullover_info->set_time_and_mileage_status(
        pb::TimeAndMileageStatus::kEnterPopupRange);
  }
}

// Updates lane sequence related info to customer monitor.
void UpdateLaneSequenceInfo(
    const RegionalMap& regional_map,
    pb::LaneSequenceRelatedInfo* lane_sequence_related_info) {
  lane_sequence_related_info->set_distance_to_next_junction(
      regional_map.regional_path_next_info.distance_to_next_junction);
  std::vector<const pnc_map::Lane*> path_lanes =
      regional_map.regional_path.lanes;
  const bool is_first_lane_in_junction =
      (path_lanes.empty() || path_lanes[0] == nullptr)
          ? false
          : path_lanes[0]->IsInJunction();
  lane_sequence_related_info->set_is_ego_in_junction(is_first_lane_in_junction);
}

// Convert mrc request to mrc info, which is used by hmi.
void ConvertMrcRequestToMrcInfo(
    bool is_inlane_degrade_allowed, bool should_planner_respond_mrc,
    const std::shared_ptr<const mrc::pb::MrcRequest>& mrc_request_ptr,
    pb::MRCInfo* mrc_info) {
  if (!should_planner_respond_mrc || mrc_request_ptr == nullptr) {
    return;
  }

  // Set is_inlane_degrade_allowed for mrc to degrade if necessary.
  mrc_info->set_is_inlane_degrade_allowed(is_inlane_degrade_allowed);

  // Extract mrm type.
  switch (mrc_request_ptr->mrm_type()) {
    case mrc::pb::MRM_HARBOR:
      mrc_info->set_mrm_type(pb::MRMType::kMRM1);
      break;
    case mrc::pb::MRM_PULLRIGHT:
      mrc_info->set_mrm_type(pb::MRMType::kMRM2);
      break;
    case mrc::pb::MRM_INLANE:
    case mrc::pb::MRM_URGENCY:
      mrc_info->set_mrm_type(pb::MRMType::kMRM3);
      break;
    default:
      break;
  }

  // Extract mrc status.
  switch (mrc_request_ptr->mrm_progress()) {
    case mrc::pb::ONGOING:
      mrc_info->set_mrc_status(pb::MRCStatus::kEnterMRCInProgress);
      break;
    case mrc::pb::FINISHED:
      mrc_info->set_mrc_status(pb::MRCStatus::kEnterMRCSuccess);
      break;
    case mrc::pb::FAILED:
      mrc_info->set_mrc_status(pb::MRCStatus::kEnterMRCFailed);
    default:
      break;
  }

  // Extract mrc source.
  switch (mrc_request_ptr->mrc_reason()) {
    case mrc::pb::kRAImmediateFullStop:
    case mrc::pb::kRAImmediatePullover:
    case mrc::pb::kRemoteAssist:
      mrc_info->set_mrc_source(pb::MRCInfo::kRemoteAssist);
      break;
    case mrc::pb::kSystemSupervisor:
      mrc_info->set_mrc_source(pb::MRCInfo::kSystemSupervisor);
      break;
    case mrc::pb::kCanbusImmediateFullStop:
      mrc_info->set_mrc_source(pb::MRCInfo::kCanbus);
      break;
    default:
      break;
  }
}

void UpdateRemoteInterveneInfo(
    const pb::DecoupledManeuverDebug& decoupled_manuver_debug,
    const pb::Trajectory& trajectory,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  // Find the selected identifier for the trajectory.
  const auto& traj_meta_debugs =
      decoupled_manuver_debug.trajectory_selection_debug()
          .trajectory_meta_debugs();
  const auto selected_trajectory_meta_debug =
      std::find_if(traj_meta_debugs.begin(), traj_meta_debugs.end(),
                   [](const planner::pb::TrajectoryMetaDebug& traj_meta_debug) {
                     return traj_meta_debug.is_selected();
                   });
  if (selected_trajectory_meta_debug == traj_meta_debugs.end()) {
    LOG(ERROR) << "selected_label not found.";
    return;
  }
  const std::string selected_label = selected_trajectory_meta_debug->label();

  // Find the selected identifier with speed_generator_debug.
  for (const auto& single_lane_sequence_plan_debug :
       decoupled_manuver_debug.single_lane_sequence_plan_debug()) {
    for (const auto& single_intention_debug :
         single_lane_sequence_plan_debug.single_intention_plan_debug()) {
      const std::string candidate_identifier =
          single_intention_debug.candidate_identifier();
      if (candidate_identifier.empty()) {
        continue;
      }
      DCHECK(!candidate_identifier.empty() && !selected_label.empty());

      // After speed diversity is enabled, candidate_identifier and
      // selected_label is
      // ${behavior_type}${geo_guidance_idx}_${intention}${intention_idx}_
      // ${speed_discomfort_ix}, we need to delete the last one character.
      if (candidate_identifier.substr(0, candidate_identifier.length() - 1) ==
          selected_label.substr(0, selected_label.length() - 1)) {
        DCHECK(!single_intention_debug.speed_generator_debug()
                    .ego_intent_signals()
                    .empty());
        // Every lane_sequence/intention plan_debugs must have updated the
        // hazard_light_reasoner_debug in reasoner_debug.
        const auto hazard_light_state =
            single_intention_debug.speed_generator_debug()
                .ego_intent_signals(0)
                .hazard_light_request_debug()
                .triggered_hazard_light_scene();
        customer_monitor_visual->mutable_remote_intervene_info()
            ->set_hazard_light_state(
                hazard_light_state == planner::pb::HazardLightScene::NA
                    ? speed::pb::HazardLightReasonerDebug::UNDEFINED
                    : (hazard_light_state ==
                               planner::pb::HazardLightScene::REMOTE_ASSIST
                           ? speed::pb::HazardLightReasonerDebug::ON_BY_RA
                           : speed::pb::HazardLightReasonerDebug::
                                 ON_BY_PLANNER));
        customer_monitor_visual->mutable_remote_intervene_info()
            ->set_is_honking(trajectory.intention().horn());

        return;
      }
    }
  }
}

}  // namespace

void GenerateCustomerMonitorVisualForManeuver(
    const lane_selection::DecoupledLaneSequenceInfo& lane_sequence_info,
    const speed::TrajectoryInfo& trajectory_info,
    const pb::ManeuverType& maneuver_type,
    const pnc_map::JointPncMapService& joint_pnc_map_service,
    const voy::TrafficLights& traffic_light_detection,
    ManeuverCustomerMonitorVisual* maneuver_visual) {
  DCHECK(maneuver_visual);
  maneuver_visual->maneuver_type = maneuver_type;
  const double ego_arclength_m =
      lane_sequence_info.robot_in_lane_param().arclength_m;
  maneuver_visual->ego_arclength_m = ego_arclength_m;

  DCHECK(
      !lane_sequence_info.traffic_rules().speed_limit_in_lane_vector().empty());
  maneuver_visual->current_lane_speed_limit_mps =
      lane_sequence_info.traffic_rules()
          .speed_limit_in_lane_vector()
          .front()
          .max_speed;

  for (const auto& junction : lane_sequence_info.traffic_rules().junctions()) {
    if (!junction.has_traffic_light) {
      continue;
    }
    maneuver_visual->junction_entrance_distance_m =
        junction.start_arclength_m - ego_arclength_m;
    maneuver_visual->junction_exit_distance_m =
        junction.end_arclength_m - ego_arclength_m;
    break;
  }

  for (const auto& junction :
       trajectory_info.traffic_rules().lane_sequence_junctions) {
    if (speed::traffic_rules::ComputeEgoLaneInJunctionTrafficLight(
            junction, trajectory_info.traffic_rules().traffic_lights) ==
        nullptr) {
      continue;
    }
    maneuver_visual->junction_entrance_distance_m =
        junction.ra_arclength_range_m.start_pos;
    maneuver_visual->junction_exit_distance_m =
        junction.ra_arclength_range_m.end_pos;
    break;
  }

  if (!trajectory_info.traffic_rules().traffic_lights.empty()) {
    const voy::TrafficLight* traffic_light_in_lane_sequence =
        trajectory_info.traffic_rules()
            .traffic_lights.front()
            .traffic_light_detected();
    if (traffic_light_in_lane_sequence != nullptr) {
      maneuver_visual->traffic_light_in_lane_sequence =
          *traffic_light_in_lane_sequence;
    }
  } else if (lane_sequence_info.lane_sequence_length() - ego_arclength_m <
             kThresholdNearEndOfLaneSequenceInMeter) {
    const auto& successor_lanes =
        (*trajectory_info.lane_sequence_iterator().current_lane())
            ->successors();
    if (!successor_lanes.empty()) {
      const speed::traffic_rules::MatchedTrafficLightInfo&
          matched_traffic_info = speed::traffic_rules::GetMatchedTrafficLight(
              joint_pnc_map_service, *successor_lanes.front(),
              traffic_light_detection);
      if (matched_traffic_info.traffic_signal_detection != nullptr) {
        maneuver_visual->traffic_light_in_lane_sequence =
            *matched_traffic_info.traffic_signal_detection;
      }
    }
  }

  const auto& turns_infos = lane_sequence_info.traffic_rules().turns_infos();
  for (const auto& turn_info : turns_infos) {
    if (turn_info.turn_direction == pb::TurnMode::STRAIGHT ||
        !turn_info.turn_lane ||
        turn_info.turn_lane->turn() ==
            hdmap::Lane::Turn::Lane_Turn_UNKNOWN_TURN ||
        turn_info.turn_lane->turn() == hdmap::Lane::Turn::Lane_Turn_STRAIGHT) {
      continue;
    }
    // There are some U turns not in junction but need to be warned.
    if (!turn_info.turn_lane->IsInJunction() &&
        turn_info.turn_direction != pb::TurnMode::U_TURN) {
      continue;
    }
    const double distance_to_next_turn_m =
        turn_info.turn_start_arclength - ego_arclength_m;
    if (distance_to_next_turn_m < 0) {
      continue;
    }
    maneuver_visual->turn_mode = turn_info.turn_direction;
    maneuver_visual->distance_to_next_turn_m = distance_to_next_turn_m;
    break;
  }
}

void CustomerMonitorVisualMsgGenerator::UpdateTidalFlowLaneVisualizationInfo(
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  const auto ra_intervention_requirements =
      Seed::Access<token::AssistStuckSeed_RAInterventionRequirements>::GetMsg(
          SpeedCurrentFrame());
  if (!ra_intervention_requirements->has_tidal_flow_lane_requirement()) {
    customer_monitor_visual->clear_tidal_flow_lane_requirement_special_info();
    return;
  }

  const auto& tidal_flow_lane_requirement =
      ra_intervention_requirements->tidal_flow_lane_requirement();
  DCHECK(tidal_flow_lane_requirement.special_info_case() ==
         pb::RAInterventionRequirement::kTidalFlowLaneRequirementSpecialInfo);
  *customer_monitor_visual->mutable_tidal_flow_lane_requirement_special_info() =
      tidal_flow_lane_requirement.tidal_flow_lane_requirement_special_info();
}

void CustomerMonitorVisualMsgGenerator::
    UpdateCustomerTriggeredImmediatePullOverInfo(
        const SpeedWorldModel& world_model,
        const BehaviorDecision& behavior_decision,
        pb::CustomerTriggeredImmediatePullOverInfo*
            customer_triggered_immediate_pullover_info) {
  // Early return if pull over status has not value or immediate pull over is
  // not triggered by dhmi/phmi.
  if (!behavior_decision.pull_over_status_info.has_value() ||
      !world_model.immediate_pull_over_state_manager()
           .IsTriggeredByOrderService()) {
    return;
  }

  // Set the termination info when immediate pull over is terminated by planner.
  const double distance_to_destination =
      world_model.regional_map().regional_path.distance_to_destination_m;
  if (behavior_decision.pull_over_status_info->immediate_pullover_destination
          .is_terminated() &&
      distance_to_destination < kThresholdDistToDestToExecuteRegularPullOver) {
    customer_triggered_immediate_pullover_info->mutable_termination_info()
        ->set_is_terminated(true);
    customer_triggered_immediate_pullover_info->mutable_termination_info()
        ->set_reason(
            pb::ImmediatePullOverTerminationInfo::kCloseToRoutingDestination);
  }

  // Early return if planner don't response to the immediate pull over.
  if (!world_model.ShouldResponseToTriggeredImmediatePullOver()) {
    have_searched_immediate_pullover_point_ = false;
    return;
  }

  // Update time_and_mileage_status.
  UpdateTimeAndMileageStatusForCustomerTriggeredImmediatePullOver(
      world_model, customer_triggered_immediate_pullover_info);

  // Update the flag of has_searched_immediate_pullover_point and
  // is_re_searching_immediate_pullover_point.
  const auto& optional_immediate_pull_over_point =
      world_model.immediate_pull_over_point();
  // Indicates planner has searched an immediate pull over point.
  if (optional_immediate_pull_over_point.has_value() &&
      world_model.immediate_pullover_state() !=
          routing::pb::ImmediatePullOverState::kSuccess) {
    customer_triggered_immediate_pullover_info
        ->set_has_searched_immediate_pullover_point(true);
    have_searched_immediate_pullover_point_ = true;
  }

  // Indicates planner has searched an immediate pull over point but has not
  // execute pull over on the pdz  where this point located. Now planner is
  // searching a new immediate pull over point.
  if (!optional_immediate_pull_over_point.has_value() &&
      have_searched_immediate_pullover_point_) {
    customer_triggered_immediate_pullover_info
        ->set_is_re_searching_immediate_pullover_point(true);
  }
}

void CustomerMonitorVisualMsgGenerator::SendReverseDrivingInfo(
    pb::ReverseDrivingAsker trigger_source,
    pb::CopilotReverseDrivingState state,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (trigger_source == pb::ReverseDrivingAsker::PLANNER_U_TURN_UNSTUCK) {
    customer_monitor_visual->mutable_copilot_reverse_info()->set_trigger_source(
        pb::ReverseDrivingAsker::PLANNER_PULLOUT);
  } else {
    customer_monitor_visual->mutable_copilot_reverse_info()->set_trigger_source(
        trigger_source);
  }
  customer_monitor_visual->mutable_copilot_reverse_info()->set_state(state);
  customer_monitor_visual->mutable_copilot_reverse_info()
      ->set_copilot_assist_id(copilot_assist_id_);
}

void CustomerMonitorVisualMsgGenerator::UpdateReverseDrivingInfo(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const math::geometry::PolylineCurve2d& nominal_path,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  switch (behavior_decision.reverse_driving_info.state()) {
    case planner::pb::ReverseDrivingState::kPrepare: {
      unfinished_time_ms_ = 0;
      SendReverseDrivingInfo(
          behavior_decision.reverse_driving_info.trigger_source(),
          pb::CopilotReverseDrivingState::STANDBY, customer_monitor_visual);
      return;
    }
    case planner::pb::ReverseDrivingState::kIdle: {
      unfinished_time_ms_ = 0;
      SendReverseDrivingInfo(
          behavior_decision.reverse_driving_info.trigger_source(),
          pb::CopilotReverseDrivingState::NONE, customer_monitor_visual);
      return;
    }
    case planner::pb::ReverseDrivingState::kAction: {
      bool has_reached_destination = false;
      const double rear_bumper_arc_length_m =
          nominal_path
              .GetProximity(world_model.robot_state()
                                .current_state_snapshot()
                                .rear_bumper_position(),
                            math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      const double stop_point_arclength_m =
          nominal_path
              .GetProximity(
                  math::geometry::Point2(
                      behavior_decision.reverse_driving_info.stop_point().x(),
                      behavior_decision.reverse_driving_info.stop_point().y()),
                  math::pb::UseExtensionFlag::kForbid)
              .arc_length;
      if (stop_point_arclength_m - rear_bumper_arc_length_m <
          kSmallDistanceDiffInMeter) {
        has_reached_destination = true;
      }

      // Continuous cumulative time when unfinished and stationary.
      const bool is_stationary = world_model.robot_state().IsStationary();
      if (is_stationary && !has_reached_destination) {
        unfinished_time_ms_ += constants::kPlanningCycleTimeInMSec;
        // Check if fail because time-out.
        if (unfinished_time_ms_ > kMaxToleranceReverseDrivingStopTimeInMSec) {
          SendReverseDrivingInfo(
              behavior_decision.reverse_driving_info.trigger_source(),
              pb::CopilotReverseDrivingState::FAIL_TIMEOUT,
              customer_monitor_visual);
          return;
        }
      } else {
        unfinished_time_ms_ = 0;
      }

      // Check if success.
      if (has_reached_destination) {
        SendReverseDrivingInfo(
            behavior_decision.reverse_driving_info.trigger_source(),
            pb::CopilotReverseDrivingState::SUCCESS, customer_monitor_visual);
        return;
      }
      SendReverseDrivingInfo(
          behavior_decision.reverse_driving_info.trigger_source(),
          pb::CopilotReverseDrivingState::ACTION, customer_monitor_visual);
      return;
    }
    case planner::pb::ReverseDrivingState::kStoppingForExit: {
      SendReverseDrivingInfo(
          behavior_decision.reverse_driving_info.trigger_source(),
          pb::CopilotReverseDrivingState::STOPPING, customer_monitor_visual);
      return;
    }
    default:
      LOG(ERROR) << "Unknown ReverseDrivingState type.";
  }
}

// Return a rectangle by inputs.
//                      heading
//                      ^
//                      |
//   start_point(1) --> 1---width---2
//                      |           |
//                    length        |
//                      |           |
//                      4-----------3
voy::Polygon2d GetRectangleByParas(const math::geometry::Point2d& start_point,
                                   double heading, double width,
                                   double length) {
  voy::Polygon2d rectangle;
  rectangle.mutable_points()->Reserve(4);
  voy::Point2d* left_top = rectangle.add_points();
  left_top->set_x(start_point.x());
  left_top->set_y(start_point.y());
  voy::Point2d* right_top = rectangle.add_points();
  right_top->set_x(left_top->x() + width * std::cos(heading - M_PI_2)),
      right_top->set_y(left_top->y() + width * std::sin(heading - M_PI_2));
  voy::Point2d* right_bottom = rectangle.add_points();
  right_bottom->set_x(right_top->x() + length * std::cos(heading - M_PI)),
      right_bottom->set_y(right_top->y() + length * std::sin(heading - M_PI));
  voy::Point2d* left_bottom = rectangle.add_points();
  left_bottom->set_x(left_top->x() + length * std::cos(heading - M_PI)),
      left_bottom->set_y(left_top->y() + length * std::sin(heading - M_PI));
  return rectangle;
}

math::geometry::Polygon2d GetGeometryPolygon2dFromVoy(
    const voy::Polygon2d& origin_polygon) {
  math::geometry::Polygon2d polygon;
  polygon.reserve(origin_polygon.points().size());
  for (const auto& point : origin_polygon.points()) {
    math::geometry::Append(polygon,
                           math::geometry::Point2d(point.x(), point.y()));
  }
  return polygon;
}

void SetCurrentAreaDangerousLevelForOncomingAgent(
    const pb::RiskAreaMaps risk_area_maps, const PlannerObject& planner_object,
    const math::geometry::OrientedBox2d object_box,
    pb::OncomingAgent* oncoming_agent) {
  // Check if object box has intersected with dangerous area.
  for (const auto& risk_area_map : risk_area_maps.risk_area_map()) {
    if (planner_object.object_type() == risk_area_map.type()) {
      if (math::geometry::Intersects(
              object_box,
              GetGeometryPolygon2dFromVoy(risk_area_map.high_risk_area()))) {
        oncoming_agent->set_current_area_dangerous_level(
            pb::AreaDangerousLevel::HIGH_RISK_LEVEL);
      } else if (math::geometry::Intersects(
                     object_box, GetGeometryPolygon2dFromVoy(
                                     risk_area_map.medium_risk_area()))) {
        oncoming_agent->set_current_area_dangerous_level(
            pb::AreaDangerousLevel::MEDIUM_RISK_LEVEL);
      }
    }
  }
}

void CustomerMonitorVisualMsgGenerator::
    SetFutureAreaDangerousLevelForOncomingAgent(
        const pb::RiskAreaMaps risk_area_maps,
        const PlannerObject& planner_object, const RobotStateSnapshot& ego_pose,
        pb::OncomingAgent* oncoming_agent) {
  int32_t agent_prediction_trajectory_point_number = 0;
  if (planner_object.object_type() == voy::perception::ObjectType::PED ||
      planner_object.object_type() == voy::perception::ObjectType::CYCLIST) {
    agent_prediction_trajectory_point_number =
        customer_monitor_config_.vru_predicted_traj_points_num();
  } else if (planner_object.object_type() ==
             voy::perception::ObjectType::VEHICLE) {
    agent_prediction_trajectory_point_number =
        customer_monitor_config_.vehicle_predicted_traj_points_num();
  }

  // Check if object boxes on prediction trajectory points have intersected
  // with dangerous area.
  for (int32_t i = 0; i < agent_prediction_trajectory_point_number; ++i) {
    const std::optional<math::geometry::OrientedBox2d>
        prediction_trajectory_box =
            planner_object.primary_trajectory().GetInterpolatedBoundingBox(
                ego_pose.timestamp() +
                    (constants::kPlanningCycleTimeInMSec * i),
                /*use_legacy_logic=*/true);
    if (!prediction_trajectory_box) {
      break;
    }
    if (oncoming_agent->future_area_dangerous_level() ==
        pb::AreaDangerousLevel::HIGH_RISK_LEVEL) {
      break;
    }
    for (const auto& risk_area_map : risk_area_maps.risk_area_map()) {
      if (planner_object.object_type() == risk_area_map.type()) {
        if (math::geometry::Intersects(
                *prediction_trajectory_box,
                GetGeometryPolygon2dFromVoy(risk_area_map.high_risk_area()))) {
          oncoming_agent->set_future_area_dangerous_level(
              pb::AreaDangerousLevel::HIGH_RISK_LEVEL);
          break;
        } else if (math::geometry::Intersects(
                       *prediction_trajectory_box,
                       GetGeometryPolygon2dFromVoy(
                           risk_area_map.medium_risk_area()))) {
          oncoming_agent->set_future_area_dangerous_level(
              pb::AreaDangerousLevel::MEDIUM_RISK_LEVEL);
        }
      }
    }
  }
}

// Updates dangerous area maps for oncoming agent from rear when pullover.
void CustomerMonitorVisualMsgGenerator::UpdateRiskAreaMaps(
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const RobotStateSnapshot& ego_pose, pb::RiskAreaMaps* risk_area_maps) {
  const double area_start_point_x_offset =
      0.5 * ego_pose.bounding_box().width() *
          std::cos(ego_pose.heading() - M_PI_2) +
      customer_monitor_config_.risk_area_lateral_offset_gen2_m() *
          std::cos(ego_pose.heading() - M_PI);
  const double area_start_point_y_offset =
      0.5 * ego_pose.bounding_box().width() *
          std::sin(ego_pose.heading() - M_PI_2) +
      customer_monitor_config_.risk_area_lateral_offset_gen2_m() *
          std::sin(ego_pose.heading() - M_PI);
  const math::geometry::Point2d start_point = {
      ego_pose.bounding_box().center().x() + area_start_point_x_offset,
      ego_pose.bounding_box().center().y() + area_start_point_y_offset};

  pb::RiskAreaMap* ped_risk_area_map = risk_area_maps->add_risk_area_map();
  ped_risk_area_map->set_type(voy::perception::ObjectType::PED);
  *ped_risk_area_map->mutable_high_risk_area() = GetRectangleByParas(
      start_point, ego_pose.heading(),
      customer_monitor_config_.ped_risk_area_width_m(),
      customer_monitor_config_.ped_high_risk_area_length_m());
  *ped_risk_area_map->mutable_medium_risk_area() = GetRectangleByParas(
      start_point, ego_pose.heading(),
      customer_monitor_config_.ped_risk_area_width_m(),
      customer_monitor_config_.ped_medium_risk_area_length_m());
  pb::RiskAreaMap* cyclist_risk_area_map = risk_area_maps->add_risk_area_map();
  cyclist_risk_area_map->set_type(voy::perception::ObjectType::CYCLIST);
  *cyclist_risk_area_map->mutable_high_risk_area() = GetRectangleByParas(
      start_point, ego_pose.heading(),
      customer_monitor_config_.cyclist_risk_area_width_m(),
      customer_monitor_config_.cyclist_high_risk_area_length_m());
  *cyclist_risk_area_map->mutable_medium_risk_area() = GetRectangleByParas(
      start_point, ego_pose.heading(),
      customer_monitor_config_.cyclist_risk_area_width_m(),
      customer_monitor_config_.cyclist_medium_risk_area_length_m());

  if (!current_lanes.empty() &&
      !current_lanes.front()->IsRightmostVehicleLane()) {
    pb::RiskAreaMap* vehicle_risk_area_map =
        risk_area_maps->add_risk_area_map();
    vehicle_risk_area_map->set_type(voy::perception::ObjectType::VEHICLE);
    *vehicle_risk_area_map->mutable_high_risk_area() = GetRectangleByParas(
        start_point, ego_pose.heading(),
        customer_monitor_config_.vehicle_risk_area_width_m(),
        customer_monitor_config_.vehicle_high_risk_area_length_m());
    *vehicle_risk_area_map->mutable_medium_risk_area() = GetRectangleByParas(
        start_point, ego_pose.heading(),
        customer_monitor_config_.vehicle_risk_area_width_m(),
        customer_monitor_config_.vehicle_medium_risk_area_length_m());
  }
}

// Update whether neeed warn the oncoming agent.
void CustomerMonitorVisualMsgGenerator::UpdateWhetherOncomingAgent(
    const SpeedWorldModel& world_model) {
  const auto oncoming_agent_warning_iter =
      std::find_if(world_model.assist_responses().cbegin(),
                   world_model.assist_responses().cend(),
                   [](const pb::AssistResponse& response) {
                     return response.type_case() ==
                            pb::AssistResponse::kOncomingAgentWarningResponse;
                   });

  if (oncoming_agent_warning_iter != world_model.assist_responses().cend()) {
    if (oncoming_agent_warning_iter->oncoming_agent_warning_response()
            .start_req()) {
      is_oncoming_agent_warn_needed_ = true;
    } else if (oncoming_agent_warning_iter->oncoming_agent_warning_response()
                   .stop_req()) {
      is_oncoming_agent_warn_needed_ = false;
    }
  }
}

// Updates oncoming agent from rear when pullover.
void CustomerMonitorVisualMsgGenerator::
    UpdateOncomingAgentWhenReachedDestination(
        const SpeedWorldModel& world_model,
        const std::vector<const pnc_map::Lane*>& current_lanes,
        pb::CustomerMonitorVisual* customer_monitor_visual) {
  // 1. Update whether neeed warn the oncoming agent.
  UpdateWhetherOncomingAgent(world_model);
  if (!is_oncoming_agent_warn_needed_) {
    return;
  }

  // 2. Update dangerous area maps for the oncoming agent.
  pb::RiskAreaMaps risk_area_maps;
  UpdateRiskAreaMaps(current_lanes,
                     world_model.robot_state().current_state_snapshot(),
                     &risk_area_maps);
  // 3. Check all the tracked objects, Update oncoming_agent message.
  customer_monitor_visual->mutable_oncoming_agents_info()->Reserve(
      world_model.planner_object_map().size());
  for (const auto& [_, planner_object] : world_model.planner_object_map()) {
    if (planner_object
            .predicted_trajectories(/*use_filtered_trajectories=*/true)
            .empty()) {
      break;
    }
    pb::OncomingAgent* oncoming_agent =
        customer_monitor_visual->add_oncoming_agents_info();
    oncoming_agent->mutable_agent()->set_id(planner_object.id());
    oncoming_agent->mutable_agent()->set_object_type(
        planner_object.object_type());
    oncoming_agent->mutable_agent()->set_speed_mps(
        planner_object.pose().speed());
    oncoming_agent->mutable_agent()->set_heading_rad(
        planner_object.pose().heading());

    const std::optional<math::geometry::OrientedBox2d> object_box =
        planner_object.primary_trajectory().GetInterpolatedBoundingBox(
            world_model.robot_state().current_state_snapshot().timestamp(),
            /*use_legacy_logic=*/true);
    if (!object_box) {
      break;
    }
    // Heading diff < pi/2.
    if (abs(math::WrapFromMinusPiToPi(
            object_box->heading() -
            world_model.robot_state().current_state_snapshot().heading())) <
        M_PI_2) {
      oncoming_agent->set_is_same_direction(true);
    } else {
      oncoming_agent->set_is_same_direction(false);
    }
    SetCurrentAreaDangerousLevelForOncomingAgent(risk_area_maps, planner_object,
                                                 *object_box, oncoming_agent);
    SetFutureAreaDangerousLevelForOncomingAgent(
        risk_area_maps, planner_object,
        world_model.robot_state().current_state_snapshot(), oncoming_agent);
  }
}

// Updates whether the ego is inside the uturn.
// The status will be latched for a while after leaving uturn as the lane info
// may be lost inside uturn.
bool CustomerMonitorVisualMsgGenerator::IsEgoInsideUTurn(
    const RobotStateSnapshot& ego_pose,
    const std::vector<const pnc_map::Lane*>& selected_lane_sequence,
    double ego_arclength) {
  offroad_distance_in_meter_ +=
      ego_pose.speed() * constants::kPlanningCycleTimeInMSec / 1000;
  if (offroad_uturn_total_distance_in_meter_.has_value() &&
      offroad_distance_in_meter_ <
          offroad_uturn_total_distance_in_meter_.value()) {
    return true;
  }

  offroad_distance_in_meter_ = 0.0;
  offroad_uturn_total_distance_in_meter_ = std::nullopt;

  double dist_to_u_turn_end = 0;
  for (const pnc_map::Lane* lane : selected_lane_sequence) {
    dist_to_u_turn_end += lane->center_line().GetTotalArcLength();
    if (lane->turn() == hdmap::Lane::Turn::Lane_Turn_U_TURN &&
        dist_to_u_turn_end > ego_arclength) {
      offroad_uturn_total_distance_in_meter_ =
          std::make_optional(dist_to_u_turn_end - ego_arclength +
                             kOffroadInUTurnDistBufferInMeter);
      break;
    }
  }

  return false;
}

// Determines if the cyclist needs a voice warning.
// And updates cyclist_voice_warning_hists_last_cycle BTW.
pb::VoiceWarningType
CustomerMonitorVisualMsgGenerator::GetCyclistVoiceWarningType(
    const PlannerObject& planner_object, const RobotStateSnapshot& ego_pose,
    std::vector<std::pair<int64_t, CyclistVoiceWarningHist>>*
        cyclist_voice_warning_hists) {
  const math::geometry::Point2d ego_pose_pt(ego_pose.x(), ego_pose.y());
  const math::geometry::Point2d object_pose_pt(
      planner_object.pose().center().x(), planner_object.pose().center().y());
  const double distance = math::geometry::Distance(ego_pose_pt, object_pose_pt);
  if (distance < kCyclistVoiceWarningNeededDistanceInMeter) {
    CyclistDirFromEgo direction_to_ego =
        GetDirFromEgo(planner_object, ego_pose);
    CyclistVoiceWarningHist voice_warning_hist = {
        direction_to_ego, 0, pb::VoiceWarningType::NO_VOICE};
    CyclistVoiceWarningHist voice_warning_hist_last_cycle = {
        CyclistDirFromEgo::MOTOR_DIR_TO_EGO_OTHERS, 0,
        pb::VoiceWarningType::NO_VOICE};

    // get cache of this obj.
    for (const auto& cyclist_dir : cyclist_voice_warning_hists_last_cycle_) {
      if (planner_object.id() == cyclist_dir.first) {
        voice_warning_hist_last_cycle = cyclist_dir.second;
      }
    }

    const bool is_speed_enough =
        planner_object.pose().speed() - ego_pose.speed() >
        kCyclistVoiceWarningNeededSpeedDiffInMps;
    if (direction_to_ego == CyclistDirFromEgo::LEFT_TO_EGO_REAR_PROFILE &&
        voice_warning_hist_last_cycle.dir_from_ego ==
            CyclistDirFromEgo::IN_EGO_REAR_PROFILE &&
        is_speed_enough) {
      voice_warning_hist.count = kCyclistVoiceWarningSignalMaxCount;
      voice_warning_hist.type = pb::VoiceWarningType::LEFT_OVERTAKE;
      cyclist_voice_warning_hists->push_back(
          std::make_pair(planner_object.id(), voice_warning_hist));
      return pb::VoiceWarningType::LEFT_OVERTAKE;
    } else if (direction_to_ego ==
                   CyclistDirFromEgo::RIGHT_TO_EGO_REAR_PROFILE &&
               voice_warning_hist_last_cycle.dir_from_ego ==
                   CyclistDirFromEgo::IN_EGO_REAR_PROFILE &&
               is_speed_enough) {
      voice_warning_hist.count = kCyclistVoiceWarningSignalMaxCount;
      voice_warning_hist.type = pb::VoiceWarningType::RIGHT_OVERTAKE;
      cyclist_voice_warning_hists->push_back(
          std::make_pair(planner_object.id(), voice_warning_hist));
      return pb::VoiceWarningType::RIGHT_OVERTAKE;
    }
    if (voice_warning_hist_last_cycle.type != pb::VoiceWarningType::NO_VOICE &&
        voice_warning_hist_last_cycle.count > 0) {
      voice_warning_hist.count = voice_warning_hist_last_cycle.count - 1;
      voice_warning_hist.type = voice_warning_hist_last_cycle.type;
      cyclist_voice_warning_hists->push_back(
          std::make_pair(planner_object.id(), voice_warning_hist));
      return voice_warning_hist_last_cycle.type;
    }
    voice_warning_hist.count = 0;
    voice_warning_hist.type = pb::VoiceWarningType::NO_VOICE;
    cyclist_voice_warning_hists->push_back(
        std::make_pair(planner_object.id(), voice_warning_hist));
  }
  return pb::VoiceWarningType::NO_VOICE;
}
// Updates the cyclist or bicycle agents(collectively referred to here
// as cyclist) which should be warning for driver HMI.
// Cause need cached variable, put the func into class.
void CustomerMonitorVisualMsgGenerator::UpdateWarningNeededCyclistAgents(
    const std::unordered_map<ObjectId, PlannerObject>& planner_object_map,
    const RobotState& robot_state,
    pb::WarningCyclistInfo* warning_cyclist_info) {
  const int64_t timestamp = robot_state.current_state_snapshot().timestamp();
  const math::geometry::OrientedBox2d& bounding_box =
      robot_state.current_state_snapshot().bounding_box();
  // for cache
  std::vector<std::pair<int64_t, CyclistVoiceWarningHist>>
      cyclist_voice_warning_hists;
  for (const auto& [_, planner_object] : planner_object_map) {
    if (planner_object
            .predicted_trajectories(/*use_filtered_trajectories=*/true)
            .empty()) {
      continue;
    }
    if (planner_object.object_type() != voy::perception::ObjectType::CYCLIST) {
      continue;
    }

    const std::optional<math::geometry::OrientedBox2d> interpolated_box =
        planner_object.primary_trajectory().GetInterpolatedBoundingBox(
            timestamp, /*use_legacy_logic=*/true);
    if (interpolated_box) {
      const pb::VoiceWarningType voice_warning_type =
          GetCyclistVoiceWarningType(planner_object,
                                     robot_state.current_state_snapshot(),
                                     &cyclist_voice_warning_hists);
      const double distance_to_ego_m =
          math::geometry::Distance(bounding_box, *interpolated_box);
      pb::FlickerDirection flicker_direction =
          pb::FlickerDirection::DIR_UNKNOWN;
      if (distance_to_ego_m <= kCyclistWarningNeededDistanceInMeter) {
        const std::optional<double> direction_to_ego = DirectionFromEgoToObject(
            planner_object, robot_state.current_state_snapshot());
        if (!direction_to_ego) {
          continue;
        }
        const double delta_theta = math::WrapFromMinusPiToPi(
            *direction_to_ego - robot_state.current_state_snapshot().heading());
        if (delta_theta < -M_PI / 2) {
          flicker_direction = pb::FlickerDirection::BOTTOM_RIGHT;
        } else if (delta_theta < 0) {
          flicker_direction = pb::FlickerDirection::TOP_RIGHT;
        } else if (delta_theta < M_PI / 2) {
          flicker_direction = pb::FlickerDirection::TOP_LEFT;
        } else {
          flicker_direction = pb::FlickerDirection::BOTTOM_LEFT;
        }
      }
      AddWarningCyclistAgent(
          planner_object, distance_to_ego_m, flicker_direction,
          voice_warning_type,
          warning_cyclist_info->add_warning_cyclist_agents());
    }
  }
  // Cache.
  cyclist_voice_warning_hists_last_cycle_ = cyclist_voice_warning_hists;
}

void CustomerMonitorVisualMsgGenerator::UpdateTrafficLightAttentions(
    const std::optional<speed::pb::FenceList>& decoupled_yielding_fence_list,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    const math::geometry::PolylineCurve2d& nominal_path,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (!decoupled_yielding_fence_list.has_value()) {
    return;
  }
  if (ShouldOutputTrafficLightAttentions(current_lanes, nominal_path,
                                         *customer_monitor_visual)) {
    const bool has_traffic_light_stop_line = std::any_of(
        decoupled_yielding_fence_list->fence_list().begin(),
        decoupled_yielding_fence_list->fence_list().end(),
        [](const auto& yield_fence) {
          return yield_fence.type() == speed::pb::FenceType::kTrafficLight;
        });
    customer_monitor_visual->add_ego_intension_attentions(
        GetTrafficLightAttention(
            customer_monitor_visual->traffic_light_in_lane_sequence(),
            has_traffic_light_stop_line));
  }
}

void CustomerMonitorVisualMsgGenerator::UpdateBrakeStopOverLineAttention(
    bool will_cross_the_stop_line,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  customer_monitor_visual->set_will_cross_the_stop_line(
      will_cross_the_stop_line);
}

void CustomerMonitorVisualMsgGenerator::UpdateYieldAndPassAttentions(
    const std::vector<StopLine>& stop_directive,
    const pb::Trajectory& trajectory, double ego_speed,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  UpdateYieldAttentionInfo(stop_directive, ego_speed, customer_monitor_visual);

  const math::Range<int> evaluate_range(
      trajectory.replan_start_idx() + kEvaluateTrajectoryPosesStartIndex,
      std::min(trajectory.replan_start_idx() + kEvaluateTrajectoryPosesEndIndex,
               trajectory.poses().size()));
  if (!customer_monitor_visual->yield_attention_infos().empty()) {
    const bool is_decelerating = std::all_of(
        trajectory.poses().begin() + evaluate_range.start_pos,
        trajectory.poses().begin() + evaluate_range.end_pos,
        [yield_accel = customer_monitor_config_.yield_agent_accel_mpss()](
            const auto& pose) { return pose.accel() < yield_accel; });
    // Output yield agent attentions if there is yield agent information and the
    // ego vehicle is decelerating. Preserve the informations for next pass
    // agent warning.
    if (is_decelerating) {
      customer_monitor_visual->add_ego_intension_attentions(
          ::planner::pb::EgoIntensionAttention::YIELD_AGENT);
      has_yield_warning_last_cycle_ = true;
      last_yield_attention_info_ =
          *customer_monitor_visual->yield_attention_infos().begin();
    }
  } else {
    // Update the counter to output pass agent warning for some times.
    if (has_yield_warning_last_cycle_) {
      pass_agent_warning_count_ =
          customer_monitor_config_.pass_agent_warning_count_max();
      has_yield_warning_last_cycle_ = false;
    }
    if (pass_agent_warning_count_ > 0) {
      --pass_agent_warning_count_;
      const bool is_accelerating = std::all_of(
          trajectory.poses().begin() + evaluate_range.start_pos,
          trajectory.poses().begin() + evaluate_range.end_pos,
          [pass_accel = customer_monitor_config_.pass_agent_accel_mpss()](
              const auto& pose) { return pose.accel() > pass_accel; });
      // Output pass agent attentions if there is yield agent information in the
      // last cycles and the ego vehicle is accelerating.
      if (is_accelerating) {
        customer_monitor_visual->add_ego_intension_attentions(
            ::planner::pb::EgoIntensionAttention::PASS_AGENT);
        // Use last yield agent info for pass agent warning to show which agent
        // is passed.
        pb::YieldAttentionInfo* yield_info =
            customer_monitor_visual->add_yield_attention_infos();
        *yield_info = last_yield_attention_info_;
      }
    }
  }
}

void CustomerMonitorVisualMsgGenerator::UpdateNudgeAttentions(
    const math::geometry::PolylineCurve2d& nominal_path,
    const pb::Trajectory& trajectory,
    const std::vector<const pnc_map::Lane*>& current_lanes,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  if (customer_monitor_visual->selected_maneuver_type() !=
      pb::ManeuverType::LANE_FOLLOW) {
    nudge_warning_trust_time_ms_ = 0;
    return;
  }

  if (current_lanes.empty() ||
      current_lanes.front()->turn() != hdmap::Lane::Turn::Lane_Turn_STRAIGHT) {
    nudge_warning_trust_time_ms_ = 0;
    return;
  }

  int left_nudge_trajectory_pose_number = 0;
  int right_nudge_trajectory_pose_number = 0;
  if (nudge_warning_trust_time_ms_ >= 2000) {
    for (const auto& traj_pose : trajectory.poses()) {
      const math::ProximityQueryInfo proximity = nominal_path.GetProximity(
          math::geometry::Point2d(traj_pose.x_pos(), traj_pose.y_pos()),
          math::pb::UseExtensionFlag::kForbid);
      if (proximity.arc_length < customer_monitor_visual->ego_arclength_m()) {
        continue;
      }
      if (proximity.arc_length - customer_monitor_visual->ego_arclength_m() >
          kMaxNudgeJudgeArcLengthThresholdInMeter) {
        break;
      }

      if (proximity.dist > kMinNudgeWarningDistInMeter) {
        if (proximity.side == math::pb::kLeft) {
          left_nudge_trajectory_pose_number += 1;
          if (left_nudge_trajectory_pose_number >= kMinNudgeJudgeCycleNum) {
            customer_monitor_visual->add_ego_intension_attentions(
                ::planner::pb::EgoIntensionAttention::LEFT_NUDGE_AGENT);
            // keeping add_ego_intension_attentions LEFT_NUDGE_AGENT until
            // left_nudge_trajectory_pose_number == 0.
            cache_left_nudge_agent_ = true;
            return;
          }
        } else if (proximity.side == math::pb::kRight) {
          right_nudge_trajectory_pose_number += 1;
          if (right_nudge_trajectory_pose_number >= kMinNudgeJudgeCycleNum) {
            customer_monitor_visual->add_ego_intension_attentions(
                ::planner::pb::EgoIntensionAttention::RIGHT_NUDGE_AGENT);
            // keeping add_ego_intension_attentions RIGHT_NUDGE_AGENT until
            // right_nudge_trajectory_pose_number == 0.
            cache_right_nudge_agent_ = true;
            return;
          }
        }
      }
    }

    if (left_nudge_trajectory_pose_number == 0) {
      cache_left_nudge_agent_ = false;
    }

    if (right_nudge_trajectory_pose_number == 0) {
      cache_right_nudge_agent_ = false;
    }

    if (cache_left_nudge_agent_) {
      customer_monitor_visual->add_ego_intension_attentions(
          ::planner::pb::EgoIntensionAttention::LEFT_NUDGE_AGENT);
      return;
    }

    if (cache_right_nudge_agent_) {
      customer_monitor_visual->add_ego_intension_attentions(
          ::planner::pb::EgoIntensionAttention::RIGHT_NUDGE_AGENT);
      return;
    }
  } else {
    nudge_warning_trust_time_ms_ += constants::kPlanningCycleTimeInMSec;
  }
  return;
}

void CustomerMonitorVisualMsgGenerator::UpdateWarningInformation(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const TrajectoryGenerationResult& trajectory_generation_result,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  const auto& nominal_path =
      *behavior_decision.lane_geometry_directive->nominal_path;
  const bool is_ego_stop =
      hypot(world_model.pose().vel_x(), world_model.pose().vel_y()) <
          kCompletelyStopSpeedThresholdInMps &&
      (trajectory_generation_result.trajectory->poses().empty() ||
       trajectory_generation_result.trajectory
               ->poses()
                   [trajectory_generation_result.trajectory->poses().size() - 1]
               .odom() < kEgoStopTrajOdomThresholdInMps);
  if (!is_ego_stop) {
    // Update the agents which will cut in ego car.
    UpdateCutInAgents(world_model.planner_object_map(), world_model.pose(),
                      behavior_decision.lane_sequence.selected_lane_sequence,
                      nominal_path, customer_monitor_visual);

    // Update the agents with potential collision with ego car.
    UpdatePotentialCollisionAgents(
        world_model.planner_object_map(), world_model.robot_state(),
        customer_monitor_visual->mutable_potential_collision_agents());

    // Updates the cyclist or bicycle agents(collectively referred to here
    // as cyclist) which should be warning for driver HMI.
    UpdateWarningNeededCyclistAgents(
        world_model.planner_object_map(), world_model.robot_state(),
        customer_monitor_visual->mutable_warning_needed_cyclist_agents());

    // Update the agents which are in median strip.
    if (behavior_decision.maneuver_type != pb::ManeuverType::PULL_OUT &&
        behavior_decision.maneuver_type != pb::ManeuverType::PULL_OVER) {
      UpdateMedianStripWarningAgents(
          world_model.planner_object_map(), world_model.pose(),
          behavior_decision.current_lanes, customer_monitor_visual);
    }

    // Update the brake-stopping over the line attentions.
    UpdateBrakeStopOverLineAttention(behavior_decision.will_cross_the_stop_line,
                                     customer_monitor_visual);

    // Updates the nudge attentions.
    UpdateNudgeAttentions(
        nominal_path, *trajectory_generation_result.trajectory,
        behavior_decision.current_lanes, customer_monitor_visual);

    // Updates the traffic light attentions.
    UpdateTrafficLightAttentions(
        behavior_decision.decoupled_yielding_fence_list,
        behavior_decision.current_lanes, nominal_path, customer_monitor_visual);
  }

  // Updates oncoming agent from rear when pullover.
  UpdateOncomingAgentWhenReachedDestination(
      world_model, behavior_decision.current_lanes, customer_monitor_visual);

  // Update the agents which should be warned when ego car turns right.
  UpdateRightTurnWarningAgents(
      world_model.planner_object_map(), world_model.pose(),
      behavior_decision.current_lanes, customer_monitor_visual);

  // Update the route guidance.
  UpdateRouteGuidance(world_model.regional_map().regional_path.lanes,
                      world_model.robot_state()
                          .current_state_snapshot()
                          .front_bumper_position(),
                      customer_monitor_visual->mutable_route_guidance());

  // Update the remaining lane sequence arclength.
  customer_monitor_visual->set_remaining_lane_sequence_arclength_m(
      nominal_path.GetTotalArcLength() -
      customer_monitor_visual->ego_arclength_m());

  // Check whether the ego vehicle is out of the roads of hdmap.
  customer_monitor_visual->set_is_ego_out_of_road(
      !IsEgoInsideUTurn(world_model.robot_state().current_state_snapshot(),
                        behavior_decision.lane_sequence.selected_lane_sequence,
                        customer_monitor_visual->ego_arclength_m()) &&
      IsEgoOutOfRoad(
          world_model.robot_state().current_state_snapshot().bounding_box(),
          behavior_decision.current_lanes));

  // Check whether there is potential collision during nudging dynamic agents.
  if (CheckPotentialNudgeCollision(world_model, behavior_decision.current_lanes,
                                   nominal_path,
                                   behavior_decision.maneuver_type)) {
    customer_monitor_visual->add_ego_intension_attentions(
        ::planner::pb::EgoIntensionAttention::POTENTIAL_NUDGE_COLLISION);
  }
}

// Updates the stop lines.
void CustomerMonitorVisualMsgGenerator::UpdateStopLines(
    const math::Curve2d& nominal_curve,
    const math::geometry::PolylineCurve2d& right_lane_boundary,
    const std::vector<StopLine>& stop_directive, const WorldModel& world_model,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  [[maybe_unused]] const double wheel_base = world_model.robot_state()
                                                 .car_model_with_shape()
                                                 .param()
                                                 .measurement()
                                                 .wheel_base();
  customer_monitor_visual->mutable_stop_fences()->Reserve(
      stop_directive.size());
  double rear_bumper_arclength_m = 0;
  const auto iter =
      std::find_if(world_model.assist_responses().cbegin(),
                   world_model.assist_responses().cend(),
                   [](const pb::AssistResponse& response) {
                     return response.type_case() ==
                            pb::AssistResponse::kReverseDrivingResponse;
                   });
  if (iter != world_model.assist_responses().cend() &&
      iter->reverse_driving_response().signal_type() ==
          pb::ReverseDrivingSignalType::kPrepareReverseDriving) {
    is_preparing_reverse_driving_ = true;
    copilot_assist_id_ = iter->reverse_driving_response().copilot_assist_id();
    for (const auto& stop_line : stop_directive) {
      if (stop_line.type == pb::StopLineType::Enum::
                                StopLineType_Enum_REVERSE_DRIVING_DESTINATION) {
        rear_bumper_arclength_m = stop_line.stop_arclength_m;
      }
    }
    copilot_reverse_driving_distance_in_meter_ =
        rear_bumper_arclength_m -
        iter->reverse_driving_response().distance().value();
  }

  if (iter != world_model.assist_responses().cend() &&
      (iter->reverse_driving_response().signal_type() ==
           pb::ReverseDrivingSignalType::kEnterReverseDriving ||
       iter->reverse_driving_response().signal_type() ==
           pb::ReverseDrivingSignalType::kExitReverseDriving)) {
    is_preparing_reverse_driving_ = false;
    copilot_reverse_driving_distance_in_meter_ = 0;
  }

  if (is_preparing_reverse_driving_) {
    customer_monitor_visual->mutable_stop_fences()->Reserve(
        stop_directive.size() + 1);
    UpdateSpecificStopLine(
        copilot_reverse_driving_distance_in_meter_,
        pb::StopLineType::Enum::StopLineType_Enum_REVERSE_DRIVING_DESTINATION,
        voy::perception::ObjectType::UNKNOWN, /*is_prepared=*/true, world_model,
        nominal_curve, right_lane_boundary, customer_monitor_visual);
  }

  for (const auto& stop_line : stop_directive) {
    UpdateSpecificStopLine(stop_line.stop_arclength_m, stop_line.type,
                           stop_line.object_type, /*is_prepared=*/false,
                           world_model, nominal_curve, right_lane_boundary,
                           customer_monitor_visual);
  }
}

void CustomerMonitorVisualMsgGenerator::UpdateCustomerMonitorVisual(
    const SpeedWorldModel& world_model,
    const BehaviorDecision& behavior_decision,
    const TrajectoryGenerationResult& trajectory_generation_result,
    const ManeuverCustomerMonitorVisual& maneuver_visual,
    const pb::PlanningDebug* planning_debug,
    pb::CustomerMonitorVisual* customer_monitor_visual) {
  TRACE_EVENT_SCOPE(planner, UpdateCustomerMonitorVisual);
  // Update the timestamp and ego pose of current planning loop.
  *customer_monitor_visual->mutable_ego_pose() = world_model.pose();
  customer_monitor_visual->set_timestamp(world_model.pose().timestamp());
  customer_monitor_visual->set_current_turn_mode(behavior_decision.turn_mode);

  // Update visual information about lane geometry.
  // Note(hongda): Directly return if the lane geometry nominal path is nullptr.
  // The following visualization logic is based on the nominal_path calculation.
  if (behavior_decision.lane_geometry_directive == nullptr ||
      behavior_decision.lane_geometry_directive->nominal_path == nullptr) {
    return;
  }
  const auto& lane_geometry = *(behavior_decision.lane_geometry_directive);
  const bool is_decoupled_path_valid =
      behavior_decision.decoupled_path.has_value() &&
      (behavior_decision.decoupled_path->poses_size() > 1);
  const double planning_horizon =
      is_decoupled_path_valid
          ? behavior_decision.decoupled_path->poses().rbegin()->odom() -
                behavior_decision.decoupled_path->poses().begin()->odom()
          : kDefaultPlanningHorizonInMeter;
  UpdateLaneGeometryVisual(world_model.robot_state(), lane_geometry,
                           planning_horizon, customer_monitor_visual);

  // Update selected maneuver type.
  DCHECK(behavior_decision.decoupled_corresponding_maneuver.has_value());
  customer_monitor_visual->set_selected_maneuver_type(
      behavior_decision.decoupled_corresponding_maneuver.value());

  // Update RA directive visual info.
  customer_monitor_visual->mutable_ra_directive_visual_info()
      ->set_direction_key_command_type(
          world_model.assist_directive_generator().GetDirectionType());

  // Set light assist availability.
  customer_monitor_visual->mutable_light_assist_availability()->CopyFrom(
      world_model.assist_availability_analyzer().light_assist_availability());

  if (planning_debug) {
    const auto& decoupled_maneuver_debug =
        planning_debug->behavior_reasoner_debug().decoupled_maneuvers(0);

    // set waypoint_availability and reason_waypoint_assist_not_available.
    const auto& waypoint_assist_debug =
        planning_debug->world_model_debug().waypoint_assist_debug();
    customer_monitor_visual->set_waypoint_availability(
        waypoint_assist_debug.waypoint_availability());
    customer_monitor_visual->set_reason_waypoint_assist_not_available(
        waypoint_assist_debug.reason_waypoint_assist_not_available());

    if (waypoint_assist_debug.waypoint_availability() ==
            pb::WaypointAvailability::NOT_AVAILABLE &&
        waypoint_assist_debug.reason_waypoint_assist_not_available() ==
            pb::ReasonWaypointAssistNotAvailable::NOT_SET) {
      LOG(ERROR) << "Unexpected waypoint_availability with "
                    "reason_waypoint_assist_not_available";
    }

    // set waypoint assist visual info in decoupled arch.
    if (world_model.waypoint_assist_tracker().IsWaypointAssistInvoked()) {
      // Update waypoint assist visual info.
      const auto waypoint_assist_phase =
          world_model.waypoint_assist_tracker().waypoint_assist_phase();
      customer_monitor_visual->mutable_waypoint_assist_visual_info()
          ->set_waypoint_assist_phase(waypoint_assist_phase);
      // Update waypoint assist drawable area for visualization.
      if (waypoint_assist_phase == pb::AssistManeuverPhase::kWaitingPoints ||
          waypoint_assist_phase == pb::AssistManeuverPhase::kPreparingPath) {
        // Update waypoint assist drawable area for visualization.
        for (const auto& lane :
             world_model.waypoint_assist_tracker().drawable_lane_sequence()) {
          customer_monitor_visual->mutable_waypoint_assist_visual_info()
              ->add_lane_ids(lane->id());
        }
      }
      // Update waypoint assist path generation status and failure type.
      if (waypoint_assist_phase == pb::AssistManeuverPhase::kWaitingPoints ||
          waypoint_assist_phase == pb::AssistManeuverPhase::kPreparingPath ||
          waypoint_assist_phase == pb::AssistManeuverPhase::kWaitingConfirm) {
        customer_monitor_visual->mutable_waypoint_assist_visual_info()
            ->set_path_failure_type(waypoint_assist_debug.path_failure_type());
        customer_monitor_visual->mutable_waypoint_assist_visual_info()
            ->set_assist_path_generation_status(
                waypoint_assist_debug.path_generation_status());
        if (world_model.waypoint_assist_tracker()
                .is_waypoint_triggered_by_routing()) {
          customer_monitor_visual->mutable_waypoint_assist_visual_info()
              ->set_waypoint_assist_source(
                  pb::WaypointAssistVisualInfo::ROUTING);
        } else {
          customer_monitor_visual->mutable_waypoint_assist_visual_info()
              ->set_waypoint_assist_source(
                  pb::WaypointAssistVisualInfo::TELEOPS);
        }
      }
      // Update waypoint assist path for visualization.
      if (waypoint_assist_phase == pb::AssistManeuverPhase::kWaitingConfirm ||
          waypoint_assist_phase == pb::AssistManeuverPhase::kFollowingPath ||
          waypoint_assist_phase == pb::AssistManeuverPhase::kExitReasoning) {
        UpdateLaneGeometryVisualForRemoteAssist(decoupled_maneuver_debug,
                                                customer_monitor_visual);
        customer_monitor_visual->mutable_waypoint_assist_visual_info()
            ->set_has_arrived_waypoint_assist_path_end(
                world_model.waypoint_assist_tracker()
                    .has_arrived_at_waypoint_assist_path_end());
      }
    }

    if (world_model.assist_availability_analyzer()
            .light_assist_availability()
            .is_forward_crawl_available() ==
        pb::LightAssistAvailability::IsAvailable::AVAILABLE) {
      UpdateLaneGeometryVisualForRemoteAssist(decoupled_maneuver_debug,
                                              customer_monitor_visual);
    }

    for (const auto& assist_task :
         world_model.assist_task_scheduler().assist_task_queue()) {
      if (!assist_task) {
        continue;
      }
      auto* remote_assist_task =
          customer_monitor_visual->add_remote_assist_task();
      assist_task->UpdateRemoteAssistTask(remote_assist_task);
    }
  }

  // Update assist stuck detected obstacle meta.
  const bool is_mrc_reached =
      world_model.mrc_request_ptr() &&
      world_model.mrc_request_ptr()->mrm_progress() == mrc::pb::FINISHED;
  if (!is_mrc_reached) {
    if (Seed::Access<token::AssistStuckSeed_IgnoringObstacleRequest>::HasMsg(
            SpeedCurrentFrame())) {
      customer_monitor_visual->mutable_ra_ignorable_dominant_obstacle()
          ->CopyFrom(
              *Seed::Access<token::AssistStuckSeed_IgnoringObstacleRequest>::
                  GetMsg(SpeedCurrentFrame()));
    }
  }

  customer_monitor_visual->clear_ignoring_construction_zones();
  {
    const auto ignored_construction_zones =
        Seed::Access<token::AssistStuckSeed_IgnoredConstructionZones>::GetMsg(
            SpeedCurrentFrame());
    if (!ignored_construction_zones->empty()) {
      customer_monitor_visual->mutable_ignoring_construction_zones()->CopyFrom(
          *ignored_construction_zones);
    } else {
      const auto ignoring_construction_zones =
          Seed::Access<token::AssistStuckSeed_IgnoringConstructionZones>::
              GetMsg(SpeedCurrentFrame());
      if (!ignoring_construction_zones->empty()) {
        customer_monitor_visual->mutable_ignoring_construction_zones()
            ->CopyFrom(*ignoring_construction_zones);
      }
    }
  }

  UpdateReverseDrivingInfo(world_model, behavior_decision,
                           *lane_geometry.nominal_path,
                           customer_monitor_visual);

  // Update decoupled path if it exists.
  if (behavior_decision.decoupled_path.has_value()) {
    *customer_monitor_visual->mutable_decoupled_path() =
        behavior_decision.decoupled_path.value();
  }

  // Update decoupled yielding fences if they exist.
  if (behavior_decision.decoupled_yielding_fence_list.has_value()) {
    *customer_monitor_visual->mutable_decoupled_yielding_fence_list() =
        behavior_decision.decoupled_yielding_fence_list.value();
  }

  UpdateManeuverVisual(maneuver_visual, customer_monitor_visual);

  const auto speed_seed =
      Seed::Access<token::SpeedSeed>::GetMsg(SpeedCurrentFrame());
  // Update the PullOver and PullOut progress state.
  UpdatePullOverPullOutProgressState(
      behavior_decision.decoupled_corresponding_maneuver.value(),
      speed_seed->speed_reasoner_seeds().pullout_reasoner_seed(),
      behavior_decision.has_reached_destination, customer_monitor_visual,
      &pull_over_pull_out_progress_state_last_cycle_,
      &pull_out_success_cycle_count_, &has_sent_pull_out_success_);

  // Update the information of warnings.
  UpdateWarningInformation(world_model, behavior_decision,
                           trajectory_generation_result,
                           customer_monitor_visual);

  // Update decoupled traffic participants result.
  if (behavior_decision.decoupled_lf_trajectory_result.has_value()) {
    const std::vector<pb::TrafficParticipant>& traffic_participants =
        behavior_decision.decoupled_traffic_participants;
    customer_monitor_visual->mutable_traffic_participants()->Clear();
    customer_monitor_visual->mutable_traffic_participants()->Reserve(
        traffic_participants.size());
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->mutable_acc_agent_ids()
        ->Clear();
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->mutable_nudge_agent_ids()
        ->Clear();
    customer_monitor_visual->mutable_traffic_participant_decisions()
        ->mutable_evade_agent_ids()
        ->Clear();
    std::for_each(
        traffic_participants.begin(), traffic_participants.end(),
        [&customer_monitor_visual](const auto& traffic_participant) {
          *customer_monitor_visual->add_traffic_participants() =
              traffic_participant;
          if (traffic_participant.decision() ==
              pb::AgentSnapshotDecision::ACC) {
            customer_monitor_visual->mutable_traffic_participant_decisions()
                ->add_acc_agent_ids(traffic_participant.agent_id());
          } else if (traffic_participant.decision() ==
                     pb::AgentSnapshotDecision::NUDGE) {
            customer_monitor_visual->mutable_traffic_participant_decisions()
                ->add_nudge_agent_ids(traffic_participant.agent_id());
          }
        });
  }
  // Update stop cell info.
  UpdateStopCellsInfo(world_model, customer_monitor_visual);

  // Update pdz info.
  UpdatePickupDropoffZoneVisualInfo(world_model, customer_monitor_visual);

  // Update cell risk info.
  UpdateCellRiskInfo(world_model, customer_monitor_visual);

  // Update mrc info.
  bool is_inlane_degrade_allowed = speed_seed->speed_reasoner_seeds()
                                       .mrc_stop_reasoner_seed()
                                       .is_inlane_degrade_allowed();
  ConvertMrcRequestToMrcInfo(is_inlane_degrade_allowed,
                             world_model.should_planner_respond_mrc(),
                             world_model.mrc_request_ptr(),
                             customer_monitor_visual->mutable_mrc_info());

  // Update pullover forward retry info.
  UpdatePulloverForwardRetryInfo(
      world_model, behavior_decision, world_model.mrc_request_ptr(),
      customer_monitor_visual->mutable_pullover_forward_retry_info());

  // Update has reached mrc.
  customer_monitor_visual->set_has_reached_mrc(
      behavior_decision.has_ego_entered_mrc);

  // Update customer-triggered immediate pull over info.
  UpdateCustomerTriggeredImmediatePullOverInfo(
      world_model, behavior_decision,
      customer_monitor_visual
          ->mutable_customer_triggered_immediate_pullover_info());

  // Update tidal flow lane related visualization info.
  UpdateTidalFlowLaneVisualizationInfo(customer_monitor_visual);

  // Update mrc request.
  if (world_model.mrc_request_ptr() != nullptr) {
    *customer_monitor_visual->mutable_mrc_request() =
        *world_model.mrc_request_ptr();
  }

  UpdateLaneSequenceInfo(
      world_model.regional_map(),
      customer_monitor_visual->mutable_lane_sequence_related_info());

  // Update remote intervene info.
  if (planning_debug) {
    const auto& decoupled_maneuver_debug =
        planning_debug->behavior_reasoner_debug().decoupled_maneuvers(0);
    UpdateRemoteInterveneInfo(decoupled_maneuver_debug,
                              *trajectory_generation_result.trajectory,
                              customer_monitor_visual);
  }

  // Update lane change abort response info.
  const auto current_maneuver_seed =
      Seed::Access<token::DecoupledManeuverSeed>::GetMsg(SpeedCurrentFrame());
  customer_monitor_visual->set_lane_change_state(
      current_maneuver_seed->selected_lane_change_state());
  const auto lane_change_info_seed =
      Seed::Access<token::LaneChangeInfoSeed>::GetMsg(SpeedCurrentFrame());
  *customer_monitor_visual->mutable_mlc_state() =
      lane_change_info_seed->mlc_state();

  // Update the lane change target region curves.
  const pb::LaneChangeInstance& lane_change_instance_pb =
      lane_change_info_seed->lane_change_instance();
  *customer_monitor_visual->mutable_lane_change_target_region_nominal_path() =
      lane_change_instance_pb.target_region_nominal_path();
  *customer_monitor_visual->mutable_lane_change_target_region_left_boundary() =
      lane_change_instance_pb.target_region_left_boundary();
  *customer_monitor_visual->mutable_lane_change_target_region_right_boundary() =
      lane_change_instance_pb.target_region_right_boundary();

  // Update remote speed limit info.
  if (world_model.assist_directive_generator()
          .remote_speed_limit()
          .has_value()) {
    customer_monitor_visual->mutable_remote_speed_limit()->CopyFrom(
        world_model.assist_directive_generator().remote_speed_limit().value());
  }

  // Update remote warning signal.
  if (behavior_decision.remote_warning_detection) {
    customer_monitor_visual->mutable_remote_warning()->CopyFrom(
        behavior_decision.remote_warning_detection->remote_warning_visual());
  }

  // Update RA ignoring traffic lights.
  customer_monitor_visual->set_is_ra_ignoring_traffic_lights(
      world_model.should_ignore_map_change_traffic_lights());
}

}  // namespace planner
