#ifndef ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_TRAJECTORY_COSTING_H_
#define ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_TRAJECTORY_COSTING_H_

#include "control_util/costing_base.h"
#include "planner/utility/primitive/primitive_definition/trajectory_primitive.h"
#include "planner_protos/trajectory_costing.pb.h"

namespace control_util {

// Trait for TrajectoryFormula specialization.
template <>
struct FormulaTraits<DdpProblemType::kTrajectory> {
  using StateControlSpecs = vehicle_model::CarModel;

  using PbState = vehicle_model::pb::CarState;
  using PbControl = vehicle_model::pb::CarControl;

  using PbFeatureType = planner::pb::TrajectoryFeatureType;
  using PbWeightAdjustSpec = control_util::pb::CostingWeightAdjustSpec;
  using PbFeatureParameter = planner::pb::TrajectoryFeatureParameter;
  using PbCostingTerm = planner::pb::TrajectoryCostingTerm;
  using Primitives = planner::primitive::TrajectoryPrimitives;
  struct StepwisePrimitives {
    vehicle_model::VehicleRepresentation ego_representation;
  };

  using PbSolution = planner::pb::TrajectorySolution;
  using PbWarmstarterDebug = planner::pb::TrajectoryWarmstarterDebug;
  using PbSolverDebug = planner::pb::TrajectorySolverDebug;
  using PbSolverInterationDebug = planner::pb::TrajectorySolverIteration;
  using PbWarmstartType = planner::pb::TrajectoryWarmstartType;

  static constexpr int FEATURE_DIM =
      planner::pb::TrajectoryFeatureType_ARRAYSIZE;
};
}  // namespace control_util

namespace planner {

using TrajectoryFormula =
    control_util::Formula<control_util::DdpProblemType::kTrajectory>;

// TrajectoryFeatureParameter includes parameters defined through motion/shape
// model that are not included in the top level costing .conf files since they
// are vehicle-specific. This helper function merges feature parameter from
// .conf file with motion model to create the full TrajectoryFeatureParameter
// object.
planner::pb::TrajectoryFeatureParameter MergeWithMotionModelParams(
    planner::pb::TrajectoryFeatureParameter feature_param,
    const vehicle_model::CarModel::PbMotionModelParam& param,
    const vehicle_model::CarModelWithAxleRectangularShape::PbShapeMeasurement&
        shape_measurement);

}  // namespace planner

namespace control_util {

// Computes the feature given type.
template <>
void GetFeature<DdpProblemType::kTrajectory>(
    int step, int total_step_size,
    const typename planner::TrajectoryFormula::PbCostingTerm& costing_term,
    const typename planner::TrajectoryFormula::PbFeatureParameter&
        feature_param,
    const typename planner::TrajectoryFormula::State& x,
    const typename planner::TrajectoryFormula::Control& u,
    const typename planner::TrajectoryFormula::StepwisePrimitives&
        stepwise_primitives,
    const typename planner::TrajectoryFormula::Primitives& primitives,
    typename planner::TrajectoryFormula::Feature* feature);

template <>
planner::TrajectoryFormula::StepwisePrimitives
planner::TrajectoryFormula::GetStepwisePrimitives(
    int step, bool is_terminal, const State& x, const Control& u,
    const Primitives& primitives) const;

}  // namespace control_util

#endif  // ONBOARD_PLANNER_TRAJECTORY_OPTIMIZER_TRAJECTORY_COSTING_H_
