package(default_visibility = ["//visibility:public"])

cc_library(
    name = "debug_main",
    srcs = [
        "debug_main.cpp",
    ],
    hdrs = [
        "debug_main.h",
    ],
    copts = [
        "-x",
        "c++",
        "-std=c++14",
        "-Wno-missing-field-initializers",
    ],
    include_prefix = "debug/",
    deps = [
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData:rte_type",
        "//offboard/fallback_ads/autosar_runtime_environment/_out/Appl/GenData/Components:rte_debug",
        "//offboard/fallback_ads/debug/control_debug_encode",
        "//offboard/fallback_ads/debug/estimation_debug_encode",
    ],
)
