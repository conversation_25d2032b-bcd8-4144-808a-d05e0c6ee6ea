#include "long_function/stop_in_lane/time_to_collision.h"

#include <gtest/gtest.h>

#include "common_math_library/vehicle_parameters.h"

// 20250310, more test cases need to be added
TEST(CalculateTimeToCollision, NoCollisionRiskTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 9.66F;
  float ego_ax = -1.68F;
  float obj_dx = 45.4375F;
  float obj_vx = 16.8125F;
  float obj_ax = -0.09375F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 40.0F);
}

TEST(CalculateTimeToCollision, ObjectStandstillTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 10.0F;
  float ego_ax = 0.0F;
  float obj_dx = 25.0F + ego_dx;
  float obj_vx = 0.0F;
  float obj_ax = 0.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 2.5F);
}

TEST(CalculateTimeToCollision, EgoFasterThanObjectTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 20.0F;
  float ego_ax = 0.0F;
  float obj_dx = 50.0F + ego_dx;
  float obj_vx = 10.0F;
  float obj_ax = 0.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 5.0F);
}

TEST(CalculateTimeToCollision, EgoSlowerThanObjectTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 10.0F;
  float ego_ax = 0.0F;
  float obj_dx = 50.0F + ego_dx;
  float obj_vx = 20.0F;
  float obj_ax = 0.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 40.0F);
}

TEST(CalculateTimeToCollision, BothVehiclesAcceleratingTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 10.0F;
  float ego_ax = 2.0F;
  float obj_dx = 50.0F + ego_dx;
  float obj_vx = 5.0F;
  float obj_ax = 1.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_NEAR(time_to_collision, 6.18F, 1e-2F);
}

TEST(CalculateTimeToCollision, BothDeceleratingAndNoCollisionTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 10.0F;
  float ego_ax = -2.0F;
  float obj_dx = 50.0F + ego_dx;
  float obj_vx = 5.0F;
  float obj_ax = -1.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 40.0F);
}

TEST(CalculateTimeToCollision, BothDeceleratingAndObjectFirstStop) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 15.0F;
  float ego_ax = -2.0F;
  float obj_dx = 40.0F + ego_dx;
  float obj_vx = 5.0F;
  float obj_ax = -1.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_NEAR(time_to_collision, 5.56F, 1e-2F);
}

TEST(CalculateTimeToCollision, ObjectFasterThanEgoTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 10.0F;
  float ego_ax = 0.0F;
  float obj_dx = 30.0F + ego_dx;
  float obj_vx = 20.0F;
  float obj_ax = 0.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 40.0F);  // Assuming -1.0F indicates no collision
}

TEST(CalculateTimeToCollision, EgoStandstillWithOncomingObjectTest) {
  // setup
  float ego_dx = GetVehicleRearAxleToFrontBumperDistance();
  float ego_vx = 0.0F;
  float ego_ax = 0.0F;
  float obj_dx = 25.0F + ego_dx;
  float obj_vx = -10.0F;
  float obj_ax = 0.0F;

  // execute
  float time_to_collision =
      CalculateTimeToCollision(ego_dx, ego_vx, ego_ax, obj_dx, obj_vx, obj_ax);

  // verify
  ASSERT_EQ(time_to_collision, 2.5F);
}
