load(
    "//bazel:defs.bzl",
    "install",
)

package(default_visibility = ["//visibility:public"])

cc_library(
    name = "onboard_monitor_add_data",
    srcs = [
        "src/add_data.cpp",
    ],
    hdrs = [
        "include/add_data.h",
    ],
    includes = [
        "include",
        "onboard/common/voy_protos",
    ],
    deps = [
        "//onboard/common/av_comm:voy_common_common",
    ],
)

cc_library(
    name = "onboard_monitor",
    srcs = [
        "src/icmp_measure.cpp",
        "src/onboard_monitor.cpp",
        "src/shell_util.cpp",
        "src/timer_manager.cpp",
        "src/timesync.cpp",
    ] + select({
        "@//bazel/platforms:is_ubuntu22_arm64": [
            "src/pcie_handler.cpp",
        ],
        "//conditions:default": [
            "src/mqtt_client.cpp",
            "src/mqtt_handler.cpp",
        ],
    }),
    hdrs = [
        "include/icmp_measure.h",
        "include/onboard_monitor.h",
        "include/shell_util.h",
        "include/thread_safe_queue.h",
        "include/timer_manager.h",
        "include/timesync.h",
    ] + select({
        "@//bazel/platforms:is_ubuntu22_arm64": [
            "include/pcie_handler.h",
        ],
        "//conditions:default": [
            "include/mqtt_client.h",
            "include/mqtt_handler.h",
        ],
    }),
    copts = select({
        "@//bazel/platforms:is_ubuntu22_arm64": ["-DU22_ARM64"],
        "//conditions:default": [],
    }),
    includes = [
        "include",
        "onboard/common/voy_protos",
    ],
    linkopts = [
    ] + select({
        "@//bazel/platforms:is_ubuntu22_arm64": [
            "-lgpiod",
        ],
        "//conditions:default": [
            "-lmosquitto",
        ],
    }),
    deps = [
        "//onboard/common/av_comm:voy_common_common",
        "//onboard/common/base:voy_base",
        "//onboard/common/node",
        "//onboard/sensors/onboard_monitor:onboard_monitor_add_data",
        "//onboard/third_party/json",
        "//onboard/third_party/strings",
        "//protobuf_cpp:grpc_protos_cpp",
        "@system//:collectdclient",
        "@voy-sdk//:boost",
        "@voy-sdk//:gflags",
        "@voy-sdk//:glog",
        "@voy-sdk//:tbb",
    ] + select({
        "@//bazel/platforms:is_ubuntu22_arm64": [
            "//onboard/third_party/gpiod",
        ],
        "//conditions:default": [],
    }),
)

cc_binary(
    name = "onboard_monitor_node",
    srcs = ["src/main.cpp"],
    linkopts = [
        "-Wl,--no-as-needed,-llttng-ust-tracepoint",
        "-Wl,--disable-new-dtags,-rpath,/opt/voyager/lib",
    ],
    deps = [":onboard_monitor"],
)

install(
    name = "install_targets",
    srcs = [":onboard_monitor_node"],
    dest = "lib/onboard_monitor",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": [],
        "@//bazel/platforms:is_ubuntu22_amd64": [],
        "@//bazel/platforms:is_ubuntu22_arm64": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)

install(
    name = "install_config",
    srcs = ["onboard/sensors/onboard_monitor/config/"],
    dest = "etc/onboard_monitor",
    target_compatible_with = select({
        "@//bazel/platforms:is_ubuntu18_amd64": [],
        "@//bazel/platforms:is_ubuntu22_amd64": [],
        "@//bazel/platforms:is_ubuntu22_arm64": [],
        "//conditions:default": ["@platforms//:incompatible"],
    }),
)
