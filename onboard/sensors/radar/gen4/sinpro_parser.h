#ifndef ONBOARD_SENSORS_RADAR_GEN4_SINPRO_PARSER_H_
#define ONBOARD_SENSORS_RADAR_GEN4_SINPRO_PARSER_H_

#include <memory>
#include <string>
#include <vector>

#include "radar_parser_base.h"
#include "voy_protos/canbus.pb.h"
#include "voy_protos/radar.pb.h"

namespace radar {

// Pre-computed CRC32 table for packet to Sinpro,
// Use polynomial 0x4811DB7 and reflection polynomial 0xEDB88120.
constexpr std::array<uint32_t, 256> kSinproRxCrc32Table = []() constexpr {
  std::array<uint32_t, 256> crc_table{};
  constexpr uint32_t polynomial = 0xEDB88120;
  for (uint32_t i = 0; i < 256; ++i) {
    uint32_t c = i;
    for (int j = 0; j < 8; ++j) {
      if (c & 1)
        c = polynomial ^ (c >> 1);
      else
        c >>= 1;
    }
    crc_table[i] = c;
  }
  return crc_table;
}();

class SinproParser : public RadarParser {
 public:
  explicit SinproParser(const std::string& facing_direction);
  DISALLOW_COPY(SinproParser);

  void ProcessReceive(const PacketPtr& pkt_buf_ptr) override;

  PacketPtr Canbus2VehicleInfo(const voy::Canbus& canbus);
  void InitExtrinsics();
  PacketPtr MakeExtrinsics();
  PacketPtr MakeFrameSync(int target_time_us);
  PacketPtr MakeReboot();
  bool IsRadarStateWorking() const { return is_radar_state_working_; }

 private:
  void ProcessPointCloud(const PacketPtr& pkt_buf_ptr,
                         std::vector<uint8_t>::const_iterator& it,
                         const uint16_t seq);
  void ProcessSystem(const PacketPtr& pkt_buf_ptr,
                     std::vector<uint8_t>::const_iterator& it);
  bool NewFrame(const uint64_t frame_cnt);
  void OnScanCycleComplete();
  void HardwareMonitorInfo(const RadarTxSystem* system);

  static uint32_t CalculateCRC32(const uint8_t* data, size_t length) {
    uint32_t crc = 0xFFFFFFFF;
    for (size_t i = 0; i < length; ++i) {
      crc = (crc >> 8) ^ kSinproRxCrc32Table[(crc ^ data[i]) & 0xFF];
    }
    return ~crc;
  }

  template <typename T>
  static void AddRadarRxHeader(T* msg, RadarRxType Type) {
    msg->header.ver = kSinproHeaderVersion;
    msg->header.rver = kSinproHeaderVersionReverse;
    msg->header.type = static_cast<uint16_t>(Type);
    msg->header.length = sizeof(T) - sizeof(RadarRxHeader);

    msg->header.crc =
        CalculateCRC32(reinterpret_cast<const uint8_t*>(msg) + sizeof(uint32_t),
                       sizeof(T) - sizeof(uint32_t));
  }

  double CalculateSteeringAngleSpeed(double current_angle,
                                     uint64_t current_timestamp) {
    // Handling divide 0.
    if (last_canbus_timestamp_ == 0 ||
        current_timestamp == last_canbus_timestamp_) {
      last_steering_angle_ = current_angle;
      last_canbus_timestamp_ = current_timestamp;
      return 0.0f;
    }
    double steering_angle_speed = (current_angle - last_steering_angle_) /
                                  (current_timestamp - last_canbus_timestamp_);

    last_steering_angle_ = current_angle;
    last_canbus_timestamp_ = current_timestamp;
    return steering_angle_speed;
  }

  std::shared_ptr<pb::RadarScan> radar_scan_;
  RadarRxExtrinsics extrinsics_;
  uint64_t scan_frame_cnt_ = -1;
  int pointcloud_num_ = -1;

  uint32_t canbus_counter_ = 0;
  uint64_t last_canbus_timestamp_ = 0;
  float last_steering_angle_ = 0;

  bool is_this_frame_pub_ = false;
};

}  // namespace radar

#endif  // ONBOARD_SENSORS_RADAR_GEN4_SINPRO_PARSER_H_
