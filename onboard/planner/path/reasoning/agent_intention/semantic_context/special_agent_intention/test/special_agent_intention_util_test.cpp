#include "planner/path/reasoning/agent_intention/semantic_context/special_agent_intention/special_agent_intention_util.h"

#include <gtest/gtest.h>

namespace planner {
namespace {

TEST(SpecialAgentIntentionUtilsTest,
     ShouldTriggerUnstuckForUnknownObjectsTest) {
  // Setup.
  prediction::pb::Agent dummy_unknown_agent_proto;
  auto& dummy_unknown_object =
      *dummy_unknown_agent_proto.mutable_tracked_object();
  dummy_unknown_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  TrafficParticipantPose traffic_participant_pose(/*timestamp=*/101,
                                                  dummy_unknown_object);
  PlannerObject planner_object(dummy_unknown_agent_proto, /*timestamp=*/101,
                               traffic_participant_pose);
  planner_object.set_l2_distance_to_ego_m(10);
  planner_object.set_is_primary_stationary(true);
  std::unordered_map<int64_t, PlannerObject> planner_object_map;
  planner_object_map.emplace(1, planner_object);

  // In the first scenario, the ego was stuck, but the unstuck seed is not yet
  // generated.
  pb::SelectionSeed selection_seed_stuck;
  pb::StuckSignal stuck_signal;
  stuck_signal.set_last_stuck_timestamp(100);
  stuck_signal.set_reason(pb::StuckSignal::kBlockageObject);
  stuck_signal.set_stuck_score(1.0);
  selection_seed_stuck.mutable_stuck_signal()->Swap(&stuck_signal);
  pb::SingleHomotopyPathSeed path_seed;
  EXPECT_TRUE(ShouldTriggerUnstuckForUnknownObjects(
      planner_object_map, selection_seed_stuck, path_seed,
      /*curr_timestamp=*/6000,
      /*curr_ego_speed=*/1.0));
  // In the second scenario, the ego was not stuck but is in the middle of the
  // unstuck.
  pb::SelectionSeed selection_seed_not_stuck;
  pb::StuckSignal stuck_signal_2;
  stuck_signal_2.set_last_stuck_timestamp(100);
  stuck_signal_2.set_reason(pb::StuckSignal::kNoStuck);
  stuck_signal_2.set_stuck_score(0.0);
  selection_seed_stuck.mutable_stuck_signal()->Swap(&stuck_signal_2);
  pb::UnstuckModeSeed unstuck_mode_seed;
  unstuck_mode_seed.add_unstuck_modes(pb::UnstuckMode::UNSTUCK_UNKNOWN_OBJECTS);
  path_seed.mutable_unstuck_mode_seed()->Swap(&unstuck_mode_seed);
  EXPECT_TRUE(ShouldTriggerUnstuckForUnknownObjects(
      planner_object_map, selection_seed_not_stuck, path_seed,
      /*curr_timestamp=*/1000,
      /*curr_ego_speed=*/1.0));
  // In the third scenario, the ego was not stuck and is not in the unstuck
  // process.
  EXPECT_FALSE(ShouldTriggerUnstuckForUnknownObjects(
      planner_object_map, selection_seed_not_stuck, path_seed,
      /*curr_timestamp=*/6000,
      /*curr_ego_speed=*/1.0));
  // In the fourth scenario, the ego is too fast so we do not want to trigger
  // unstuck.
  EXPECT_FALSE(ShouldTriggerUnstuckForUnknownObjects(
      planner_object_map, selection_seed_not_stuck, path_seed,
      /*curr_timestamp=*/6000,
      /*curr_ego_speed=*/10.0));
}

TEST(SpecialAgentIntentionUtilsTest,
     OnlyTriggerUnstuckIfUnknownObjectsExistTest) {
  // Setup.

  prediction::pb::Agent dummy_regular_agent_proto;
  auto& dummy_regular_object =
      *dummy_regular_agent_proto.mutable_tracked_object();
  dummy_regular_object.set_object_type(voy::perception::ObjectType::VEHICLE);
  TrafficParticipantPose traffic_participant_pose_1(/*timestamp=*/101,
                                                    dummy_regular_object);
  PlannerObject regular_planner_object(
      dummy_regular_agent_proto, /*timestamp=*/101, traffic_participant_pose_1);
  regular_planner_object.set_l2_distance_to_ego_m(10);
  // Add another unknown object but it is far away from the ego.
  prediction::pb::Agent dummy_unknown_agent_proto;
  auto& dummy_unknown_object =
      *dummy_unknown_agent_proto.mutable_tracked_object();
  dummy_unknown_object.set_object_type(voy::perception::ObjectType::UNKNOWN);
  TrafficParticipantPose traffic_participant_pose_2(/*timestamp=*/101,
                                                    dummy_unknown_object);
  PlannerObject unknown_planner_object(
      dummy_unknown_agent_proto, /*timestamp=*/101, traffic_participant_pose_2);
  unknown_planner_object.set_l2_distance_to_ego_m(30);
  unknown_planner_object.set_is_primary_stationary(true);
  std::unordered_map<int64_t, PlannerObject> planner_object_map;
  planner_object_map.emplace(1, regular_planner_object);
  planner_object_map.emplace(2, unknown_planner_object);

  pb::SelectionSeed selection_seed_stuck;
  pb::StuckSignal stuck_signal;
  stuck_signal.set_last_stuck_timestamp(100);
  stuck_signal.set_reason(pb::StuckSignal::kBlockageObject);
  stuck_signal.set_stuck_score(1.0);
  selection_seed_stuck.mutable_stuck_signal()->Swap(&stuck_signal);
  pb::SingleHomotopyPathSeed path_seed;

  // In this scenario, there is no unknown agent with a small distance to the
  // ego.
  EXPECT_FALSE(ShouldTriggerUnstuckForUnknownObjects(
      planner_object_map, selection_seed_stuck, path_seed,
      /*curr_timestamp=*/1000,
      /*curr_ego_speed=*/1.0));
}
}  // namespace
}  // namespace planner
