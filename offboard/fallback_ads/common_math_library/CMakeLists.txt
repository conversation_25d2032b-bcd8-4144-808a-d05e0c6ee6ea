###########
## Build ##
###########
# clangd 
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)

add_library(common_library
  can_util.cpp
  common_method.c
  debounce_method.c
  interpolation.c
  linear_quadratic_regulator.c
  look1_iflf_binlcapw.c
  matrix.c
  poly_solve.c
  sort.c
  vehicle_parameters.c
)

#############
## Install ##
#############
install(TARGETS common_library
  EXCLUDE_FROM_ALL
  EXPORT fb_l2_node
)
install(DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
  EXCLUDE_FROM_ALL
  DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}
  FILES_MATCHING # install only matched files
  PATTERN "*.h" # select header files
)
