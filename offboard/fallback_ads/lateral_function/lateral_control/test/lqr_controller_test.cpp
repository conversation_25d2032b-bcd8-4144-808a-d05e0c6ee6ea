#include "lateral_function/lateral_control/lqr_controller.h"

#include <gtest/gtest.h>

#include "_out/Appl/GenData/Rte_Type.h"
#include "common_math_library/matrix.h"
#include "lateral_function/lateral_function_type.h"

TEST(UpdateSystemMatrix, UpdateSystemMatrixTest) {
  float ego_velocity = 2.45F;
  CML_MatrixCreate(matrix_ad_ptr, 4, 4);
  UpdateSystemMatrix(ego_velocity, matrix_ad_ptr);

  // reference matrix ad
  CML_MatrixCreate(matrix_ad_ref_ptr, 4, 4);
  CML_MatrixSetAll(matrix_ad_ref_ptr, 4, 4, 0.0F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 0, 0, 1.0F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 0, 1, 0.007977F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 0, 2, 0.004957F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 0, 3, 1.977e-5F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 1, 1, 0.5953F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 1, 2, 0.9915F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 1, 3, 0.003954F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 2, 2, 1.0F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 2, 3, 0.007977F);
  CML_MatrixSetElement(matrix_ad_ref_ptr, 3, 3, 0.5953F);

  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 4; ++col) {
      // TODO(huxiaorong): Fix the test case.
      // ASSERT_NEAR(CML_MatrixGetElement(matrix_ad_ptr, row, col),
      //             CML_MatrixGetElement(matrix_ad_ref_ptr, row, col), 1e-4F);
    }
  }
}

TEST(UpdateControlMatrix, UpdateControlMatrixTest) {
  CML_MatrixCreate(matrix_bd_ptr, 4, 1);
  UpdateControlMatrix(matrix_bd_ptr);

  // reference matrix bd
  CML_MatrixCreate(matrix_bd_ref_ptr, 4, 1);
  CML_MatrixSetZero(matrix_bd_ref_ptr);
  CML_MatrixSetElement(matrix_bd_ref_ptr, 1, 0, 0.6215F);
  CML_MatrixSetElement(matrix_bd_ref_ptr, 3, 0, 0.4165F);

  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 1; ++col) {
      // TODO(huxiaorong): Fix the test case.
      // ASSERT_NEAR(CML_MatrixGetElement(matrix_bd_ptr, row, col),
      //             CML_MatrixGetElement(matrix_bd_ref_ptr, row, col), 1e-4F);
    }
  }
}

TEST(ComputeLateralErrors, ComputeLateralErrorsTest) {
  float center_lane_y0 = 0.586F;
  float center_lane_heading = -0.0448F;
  LateralError lateral_error =
      ComputeLateralErrors(center_lane_y0, center_lane_heading);
  ASSERT_NEAR(lateral_error.distance_error, -0.586F, 1e-4F);
  ASSERT_NEAR(lateral_error.heading_error, 0.0448F, 1e-4F);
  // ASSERT_NEAR(lateral_error.distance_error_rate, -58.6F, 1e-2F);
  // ASSERT_NEAR(lateral_error.heading_error_rate, 4.48F, 1e-2F);

  lateral_error = ComputeLateralErrors(center_lane_y0, center_lane_heading);
  ASSERT_NEAR(lateral_error.distance_error, -0.586F, 1e-4F);
  ASSERT_NEAR(lateral_error.heading_error, 0.0448F, 1e-4F);
  ASSERT_NEAR(lateral_error.distance_error_rate, 0.0, 1e-2F);
  ASSERT_NEAR(lateral_error.heading_error_rate, 0.0F, 1e-2F);
}

TEST(UpdateStateMatrix, UpdateStateMatrixTest) {
  LateralError lateral_error = {-0.586F, -58.6F, 0.0448F, 4.48F};
  CML_MatrixCreate(matrix_state_ptr, 4, 1);
  UpdateStateMatrix(lateral_error, matrix_state_ptr);

  // reference matrix state
  CML_MatrixCreate(matrix_state_ref_ptr, 4, 1);
  CML_MatrixSetZero(matrix_state_ref_ptr);
  CML_MatrixSetElement(matrix_state_ref_ptr, 0, 0, -0.586F);
  CML_MatrixSetElement(matrix_state_ref_ptr, 1, 0, -58.6F);
  CML_MatrixSetElement(matrix_state_ref_ptr, 2, 0, 0.0448F);
  CML_MatrixSetElement(matrix_state_ref_ptr, 3, 0, 4.48F);

  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 1; ++col) {
      ASSERT_NEAR(CML_MatrixGetElement(matrix_state_ptr, row, col),
                  CML_MatrixGetElement(matrix_state_ref_ptr, row, col), 1e-4F);
    }
  }
}

TEST(ComputeStateCostMatrix, ComputeStateCostMatrixTest) {
  float ego_velocity = 10.0F;
  CML_MatrixCreate(matrix_q_ptr, 4, 4);
  ComputeStateCostMatrix(ego_velocity, 1.0, matrix_q_ptr);

  // reference matrix q
  CML_MatrixCreate(matrix_q_ref_ptr, 4, 4);
  CML_MatrixSetZero(matrix_q_ref_ptr);
  CML_MatrixSetElement(matrix_q_ref_ptr, 0, 0, 0.01F);
  CML_MatrixSetElement(matrix_q_ref_ptr, 2, 2, 0.4F);

  for (uint8_t row = 0; row < 4; ++row) {
    for (uint8_t col = 0; col < 4; ++col) {
      // TODO(huxiaorong): Fix the test case
      // ASSERT_NEAR(CML_MatrixGetElement(matrix_q_ptr, row, col),
      //             CML_MatrixGetElement(matrix_q_ref_ptr, row, col), 1e-4F);
    }
  }
}

TEST(ComputeFeedback, ComputeFeedbackTest) {
  CML_MatrixCreate(matrix_k_ptr, 1, 4);
  CML_MatrixSetZero(matrix_k_ptr);
  CML_MatrixSetElement(matrix_k_ptr, 0, 0, 0.02411F);
  CML_MatrixSetElement(matrix_k_ptr, 0, 1, 0.0004736F);
  CML_MatrixSetElement(matrix_k_ptr, 0, 2, 0.3922F);
  CML_MatrixSetElement(matrix_k_ptr, 0, 3, 0.007663F);

  CML_MatrixCreate(matrix_state_ptr, 4, 1);
  CML_MatrixSetZero(matrix_state_ptr);
  CML_MatrixSetElement(matrix_state_ptr, 0, 0, -0.5859F);
  CML_MatrixSetElement(matrix_state_ptr, 1, 0, 0.0F);
  CML_MatrixSetElement(matrix_state_ptr, 2, 0, 0.04483F);
  CML_MatrixSetElement(matrix_state_ptr, 3, 0, 0.0F);

  float steer_wheel_angle = ComputeFeedback(matrix_k_ptr, matrix_state_ptr);
  ASSERT_NEAR(steer_wheel_angle, -0.00345F, 1e-4F);
}

TEST(ComputeFeedForward, ComputeFeedForwardTest) {
  float k3 = 0.392F;
  float ego_velocity = 2.45F;
  float ref_curvature = 0.00061F;
  float steer_wheel_angle = ComputeFeedForward(k3, ego_velocity, ref_curvature);
  ASSERT_NEAR(steer_wheel_angle, 0.00148F, 1e-4F);
}

TEST(LimitSteeringAngle, WithinLimitsTest) {
  float ego_velocity = 10.0F;
  uint16_t system_state = 0;
  float front_wheel_steer_angle = 0.024F;
  float front_wheel_steer_angle_limit = 0.0F;
  float max_steer_angle = 0.0F;
  float max_steer_angle_rate = 0.0F;

  for (int i = 0; i < 1000; ++i) {
    front_wheel_steer_angle_limit =
        LimitSteeringAngle(ego_velocity, system_state, front_wheel_steer_angle,
                           &max_steer_angle, &max_steer_angle_rate);
  }

  ASSERT_EQ(front_wheel_steer_angle_limit, front_wheel_steer_angle);
}

TEST(CheckLqrController, CheckLqrControllerTest) {
  VSI_VehicleInfo_Struct vehicle_info = {};
  BDP_FilteredLane_Struct center_lane = {};
  FSA_SystemState_Struct system_state = {};

  // case1: vehicle is stationary, camera does not detect the lane markings
  float steer_wheel_angle = 0.0F;
  LqrControllerDebug lqr_debug = {};
  for (int i = 0; i < 1000; ++i) {
    steer_wheel_angle =
        LqrController(&vehicle_info, &center_lane, &system_state, &lqr_debug);
  }
  ASSERT_NEAR(steer_wheel_angle, 0.0F, 1e-4F);

  // case2: vehicle is moving, camera does not detect the lane markings
  vehicle_info.VSI_LongitudinalVelocity = 10.0F;
  for (int i = 0; i < 1000; ++i) {
    steer_wheel_angle =
        LqrController(&vehicle_info, &center_lane, &system_state, &lqr_debug);
  }
  ASSERT_NEAR(steer_wheel_angle, 0.0F, 1e-4F);

  // case3: vehicle stationary, camera detects the lane markings
  vehicle_info.VSI_LongitudinalVelocity = 2.45F;
  center_lane.BDP_PosY0Cntr = 0.586F;
  center_lane.BDP_HeadingAngleCntr = -0.0448F;
  center_lane.BDP_CurvatureCntr = 0.00061F;
  for (int i = 0; i < 1000; ++i) {
    steer_wheel_angle =
        LqrController(&vehicle_info, &center_lane, &system_state, &lqr_debug);
  }
  // ASSERT_NEAR(steer_wheel_angle, -1.81F, 1e-4F);

  // case4: vehicle moving, camera detects the lane markings
  vehicle_info.VSI_LongitudinalVelocity = 10.0F;
  center_lane.BDP_PosY0Cntr = 0.5F;
  center_lane.BDP_HeadingAngleCntr = -0.0F;
  center_lane.BDP_CurvatureCntr = 0.0F;
  for (int i = 0; i < 1000; ++i) {
    steer_wheel_angle =
        LqrController(&vehicle_info, &center_lane, &system_state, &lqr_debug);
  }
  // TODO(huxiaorong): Fix the test case.
  // ASSERT_NEAR(steer_wheel_angle, 0.01F, 1e-3F);
}
