#include "planner/open_space/motion_plan_generator/open_space_path_searcher/hybrid_a_star_search.h"

#include <algorithm>
#include <limits>
#include <numeric>
#include <optional>
#include <queue>
#include <utility>
#include <vector>

#include <glog/logging.h>
#include <tbb/parallel_for.h>

#include "math/math_util.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_node.h"
#include "planner/open_space/motion_plan_generator/open_space_path_searcher/open_space_path_search_result.h"
#include "planner/world_model/snapshot/robot_state_snapshot.h"
#include "trace/trace.h"
#include "vehicle_model/motion_model_with_shape.h"
#include "vehicle_model/utils.h"
#include "voy_trace/trace_planner.h"

namespace planner {

namespace open_space {

// Estimated num path states for the searched path.
constexpr int kEstimatedSearchedPathStateSize = 100;

// Estimated num motion segments in the searched result.
constexpr int kEstimatedMotionSegmentsInSearchResult = 10;

namespace {

// Partitions the searched path into several motion segments according to their
// motion mode (forward / backward).
std::vector<MotionSegment> PartitionPathToMotionSegments(
    PathSegment&& searched_path) {
  std::vector<MotionSegment> motion_segments;
  motion_segments.reserve(kEstimatedMotionSegmentsInSearchResult);
  DCHECK(!searched_path.empty());
  PathState* last_state_ptr = nullptr;
  for (PathState& state : searched_path.mutable_path_states()) {
    if (last_state_ptr == nullptr ||
        state.is_forward != last_state_ptr->is_forward) {
      motion_segments.emplace_back();
    }
    motion_segments.back().path.Append(std::move(state));
    last_state_ptr = &state;
  }
  return motion_segments;
}

// Calculates the steering angle sampling division for the next nodes.
double CalculateSteeringAngleSamplingDivision(double max_steering_angle,
                                              double num_next_node_samples) {
  return 2.0 * max_steering_angle /
         (static_cast<double>(num_next_node_samples) * 0.5 - 1.0);
}

// Returns true if the init state of the RS path disagree with the preset motion
// mode, e.g one is forward and another is backward. The RS path is loaded from
// the final node candidate.
bool ShouldRejectRSPByPresetMotionMode(
    const OpenSpaceNode& current_node,
    const OpenSpaceNode& final_node_candidate, const Grid& grid) {
  if (!current_node.parent_key.IsNullKey()) {
    // If the current node has valid parent key, it means the node is not start
    // node, then there's no need to align the initial motion mode of the RS
    // path with the preset value.
    return false;
  }
  const std::optional<MotionModeGuidance>& start_pose_motion_mode_guidance =
      base::CheckAndGetValue(grid.motion_plan_problem().start_pose_info)
          .motion_mode_guidance;
  if (!start_pose_motion_mode_guidance.has_value()) {
    return false;
  }
  // TODO(Liwen): Consume guidance_level in MotionModeGuidance.
  if (start_pose_motion_mode_guidance->guidance_motion_mode ==
          planner::pb::MotionMode::FORWARD &&
      !final_node_candidate.path_from_parent.front_state().is_forward) {
    return true;
  }
  if (start_pose_motion_mode_guidance->guidance_motion_mode ==
          planner::pb::MotionMode::BACKWARD &&
      final_node_candidate.path_from_parent.front_state().is_forward) {
    return true;
  }
  return false;
}

}  // namespace

std::optional<OpenSpacePathSearchResult> HybridAStarSearch::Search() {
  TRACE_EVENT_SCOPE(planner, OpenSpacePathSearcher_Search);
  // Prepare start and end node(state)s.
  const OpenSpaceNode& start_node = PrepareStartNode();
  const MotionPlanProblem& motion_plan_problem = grid_.motion_plan_problem();
  // TODO(Liwen, Zixuan): Adapt search to consume multiple target poses and
  // remove DCHECK below.
  DCHECK_EQ(motion_plan_problem.target_pose_infos.size(), 1)
      << "Open space search currently only supports a single target pose!";
  const math::Pose2d& target_pose =
      motion_plan_problem.target_pose_infos.front().target_pose();
  const PathState target_state(target_pose.position(),
                               target_pose.yaw().value());
  if (!ValidateNode(start_node) || !ValidateNode(OpenSpaceNode(target_state))) {
    LOG(ERROR) << "The start / final node is not valid (has collision or not "
                  "in grid bound), early return."
               << "start_node: "
               << "in area: "
               << grid_.IsWithinGridArea(start_node.state.position)
               << " collision: " << HasCollision(start_node.state)
               << " final_node: "
               << "in area: " << grid_.IsWithinGridArea(target_state.position)
               << " collision: " << HasCollision(target_state);
    return std::nullopt;
  }

  // Initialize priority queue for cost ranking.
  std::priority_queue<OpenSpaceNodePQInfo, std::vector<OpenSpaceNodePQInfo>> pq;
  pq.emplace(start_node.key, start_node.costing_info.total_cost());

  int num_of_feasible_solution_found = 0;
  std::optional<OpenSpaceNode> final_node;
  while (!pq.empty() &&
         num_of_feasible_solution_found <
             config_.max_num_of_feasible_path_solutions_to_find()) {
    // Get the current node from pq.
    const OpenSpaceNodePQInfo& pq_info = pq.top();
    OpenSpaceNode* current_node_ptr =
        grid_.GetMutableOpenSpaceNode(pq_info.key);
    if (current_node_ptr == nullptr) {
      LOG(ERROR) << "Cannot find current node, while loop failed.";
      continue;
    }
    OpenSpaceNode& current_node = *current_node_ptr;
    DCHECK(pq_info.total_cost == current_node.costing_info.total_cost() ||
           current_node.is_closed)
        << "Current Node: " << current_node << " pq_info: " << pq_info.key
        << " " << pq_info.total_cost;
    pq.pop();

    // Close the current node.
    if (current_node.is_closed) {
      continue;
    }
    current_node.is_closed = true;

    // Generate the curve to reach target state with analytic expansion.
    OpenSpaceNode final_node_candidate(target_state);
    if (ExecuteAnalyticExpansion(current_node, &final_node_candidate)) {
      if (!final_node.has_value() ||
          final_node->costing_info.path_cost() >
              final_node_candidate.costing_info.path_cost()) {
        if (!ShouldRejectRSPByPresetMotionMode(current_node,
                                               final_node_candidate, grid_)) {
          // RSP might search from start node to final node, so here we filter
          // those candidates that do not match the preset mode.
          final_node = final_node_candidate;
        }
      }
      ++num_of_feasible_solution_found;
    }

    // Explore the next nodes generated from current node control space
    // sampling.
    Explore(current_node, &pq);
  }

  if (!final_node.has_value()) {
    LOG(ERROR) << "Failed to find final node, Skip.";
    return std::nullopt;
  }

  return RetrievePathSearchResult(start_node, &final_node.value());
}

void HybridAStarSearch::Explore(
    const OpenSpaceNode& current_node,
    std::priority_queue<OpenSpaceNodePQInfo, std::vector<OpenSpaceNodePQInfo>>*
        pq) const {
  // Prepare control space samples for next nodes generation.
  const std::vector<ControlSpaceSample> control_space_samples =
      GenerateControlSpaceSamples(current_node);
  std::vector<OpenSpaceNodePQInfo> next_pq_infos(control_space_samples.size());
  std::vector<OpenSpaceNode> next_nodes(control_space_samples.size());

  tbb::parallel_for(
      0, static_cast<int>(control_space_samples.size()),
      [this, &current_node, &control_space_samples, &next_pq_infos,
       &next_nodes](int next_node_idx) {
        std::optional<OpenSpaceNode> next_node = GenerateNextNode(
            current_node, control_space_samples[next_node_idx]);
        if (!next_node.has_value()) {
          return;
        }
        const OpenSpaceNode* possible_conflict_node_ptr =
            grid_.GetOpenSpaceNode(next_node->key);
        if (possible_conflict_node_ptr != nullptr &&
            possible_conflict_node_ptr->is_closed) {
          return;
        }

        costing_engine_.CalculateCurveCost(current_node, next_node.value(),
                                           grid_, &next_node->costing_info);
        if (possible_conflict_node_ptr != nullptr &&
            possible_conflict_node_ptr->costing_info.total_cost() <=
                next_node->costing_info.total_cost()) {
          return;
        }

        OpenSpaceNodePQInfo& next_pq_info = next_pq_infos[next_node_idx];
        DCHECK(next_pq_info.key.IsNullKey());
        next_pq_info.key = next_node->key;
        next_pq_info.total_cost = next_node->costing_info.total_cost();
        next_nodes[next_node_idx] = std::move(next_node.value());
      });

  // Push updated pq_infos to lattice search pq and populate debugs.
  for (size_t idx = 0; idx < control_space_samples.size(); ++idx) {
    // Skip nodes that are not updated.
    if (next_pq_infos[idx].key.IsNullKey()) {
      continue;
    }
    // Ensure that data in pq_infos agree with node cost data.
    DCHECK(math::IsApprox(next_pq_infos[idx].total_cost,
                          next_nodes[idx].costing_info.total_cost()));
    pq->push(std::move(next_pq_infos[idx]));

    // Emplace next node to grid.
    grid_.EmplaceOpenSpaceNode(std::move(next_nodes[idx]));
  }
}

std::vector<ControlSpaceSample> HybridAStarSearch::GenerateControlSpaceSamples(
    const OpenSpaceNode& curr_node) const {
  const vehicle_model::CarModelWithAxleRectangularShape& car_model_with_shape =
      grid_.motion_plan_problem().car_model_with_shape();
  const double max_steering_angle =
      car_model_with_shape.param().limit().steering_limit().max_val();
  const size_t num_next_node_samples = config_.num_next_node_samples();
  const double steering_angle_sampling_division_per_next_node =
      CalculateSteeringAngleSamplingDivision(max_steering_angle,
                                             num_next_node_samples);
  const double path_segment_sampling_step_length =
      config_.path_segment_sampling_step_in_meters();

  const std::optional<MotionModeGuidance>& start_pose_motion_mode_guidance =
      base::CheckAndGetValue(grid_.motion_plan_problem().start_pose_info)
          .motion_mode_guidance;
  const bool is_preset_init_motion_mode_forward =
      start_pose_motion_mode_guidance.has_value() &&
      start_pose_motion_mode_guidance->guidance_motion_mode ==
          planner::pb::MotionMode::FORWARD;

  std::vector<ControlSpaceSample> control_space_samples;
  control_space_samples.reserve(num_next_node_samples);
  for (size_t next_node_idx = 0; next_node_idx < num_next_node_samples;
       ++next_node_idx) {
    double steering_angle = 0.0;
    // The sign of the variable represents the forward/backward motion.
    double signed_sampling_step_length = 0.0;
    const bool is_forward =
        next_node_idx < static_cast<double>(num_next_node_samples) * 0.5;
    if (curr_node.parent_key.IsNullKey() &&
        (is_preset_init_motion_mode_forward != is_forward)) {
      // Skip the sampling candidates that disagree with the preset init
      // motion mode when expanding start node.
      continue;
    }

    if (is_forward) {
      steering_angle =
          -max_steering_angle + steering_angle_sampling_division_per_next_node *
                                    static_cast<double>(next_node_idx);
      signed_sampling_step_length = path_segment_sampling_step_length;
    } else {
      size_t index = next_node_idx - num_next_node_samples / 2;
      steering_angle =
          -max_steering_angle + steering_angle_sampling_division_per_next_node *
                                    static_cast<double>(index);
      signed_sampling_step_length = -path_segment_sampling_step_length;
    }
    control_space_samples.emplace_back(steering_angle,
                                       signed_sampling_step_length);
  }
  return control_space_samples;
}

std::optional<OpenSpaceNode> HybridAStarSearch::GenerateNextNode(
    const OpenSpaceNode& current_node,
    const ControlSpaceSample& control_space_sample) const {
  const vehicle_model::CarModelWithAxleRectangularShape& car_model_with_shape =
      grid_.motion_plan_problem().car_model_with_shape();
  const double max_steering_angle =
      car_model_with_shape.param().limit().steering_limit().max_val();
  const double wheel_base =
      car_model_with_shape.param().measurement().wheel_base();
  const pb::OpenSpaceGridConfig& grid_config = grid_.config();
  const size_t num_next_node_samples = config_.num_next_node_samples();
  const double path_segment_sampling_step_length =
      config_.path_segment_sampling_step_in_meters();

  // The expected single step arc length is required to gurantee that next
  // states in one step exploration will not reach the same open space grid cell
  // (x,y,heading) with each other, or conflict with current node.
  // For the purpose, two threshold values are considered: (1) the single step
  // arc length should not shorter than the diagonal of a grid cell to make sure
  // the target state locates at grid cell different from current state; (2) the
  // single step arc length should be large enough to make sure that target
  // states are with heading diff larger than the heading resolution, larger the
  // steering angle, larger the heading diff with the same arc length, so just
  // calculate the arc length threshold with the smallest steering angle (so
  // here use the `steering_angle_sampling_division_per_next_node`).
  const double steering_angle_sampling_division_per_next_node =
      CalculateSteeringAngleSamplingDivision(max_steering_angle,
                                             num_next_node_samples);
  const double turning_radius_with_min_steering =
      wheel_base / std::tan(steering_angle_sampling_division_per_next_node);
  const double expected_single_step_arc_length =
      std::max(grid_config.heading_slot_resolution_in_rad() *
                   turning_radius_with_min_steering,
               std::sqrt(2.0) * grid_config.xy_grid_resolution_in_meters());
  const int expected_num_steps_in_path_segment =
      static_cast<int>(std::ceil(expected_single_step_arc_length /
                                 path_segment_sampling_step_length)) +
      1;

  // Take motion primitive to generate a curve driving the car to a different
  // grid cell.
  PathSegment path_segment_from_parent;
  double arc_length = 0.0;
  path_segment_from_parent.Append(
      PathState(current_node.state.position, current_node.state.heading,
                control_space_sample.steering_angle,
                control_space_sample.signed_sampling_step_length > 0.0),
      arc_length);
  for (int i = 0; i < expected_num_steps_in_path_segment; ++i) {
    const PathState& last_state = path_segment_from_parent.back_state();
    const double next_heading =
        last_state.heading + control_space_sample.signed_sampling_step_length /
                                 wheel_base *
                                 std::tan(control_space_sample.steering_angle);

    // Use the average heading of current and next state to make the movement
    // vector direction estimation closer to the ground truth.
    const double average_heading = (last_state.heading + next_heading) * 0.5;
    const math::geometry::Point2d last_to_next_vector =
        math::geometry::Multiply(
            math::geometry::Point2d(math::FastCos(average_heading),
                                    math::FastSin(average_heading)),
            control_space_sample.signed_sampling_step_length);
    const math::geometry::Point2d next_state_position =
        math::geometry::Add(last_state.position, last_to_next_vector);
    path_segment_from_parent.Append(
        PathState(next_state_position, math::NormalizeMinusPiToPi(next_heading),
                  control_space_sample.steering_angle,
                  control_space_sample.signed_sampling_step_length > 0.0),
        arc_length);
    arc_length += std::fabs(control_space_sample.signed_sampling_step_length);
    // Ignore the next node previously if it runs outside of XY boundary or
    // has collision.
    if (!grid_.IsWithinGridArea(
            path_segment_from_parent.back_state().position) ||
        HasCollision(path_segment_from_parent.back_state())) {
      return std::nullopt;
    }
  }
  // Calculate node key.
  const PathState& last_state = path_segment_from_parent.back_state();
  const OpenSpaceNodeKey next_node_key =
      grid_.GetOpenSpaceNodeKeyFromPathState(last_state);
  if (next_node_key.IsNullKey()) {
    return std::nullopt;
  }
  return OpenSpaceNode(last_state, next_node_key, current_node.key,
                       std::move(path_segment_from_parent));
}

bool HybridAStarSearch::ExecuteAnalyticExpansion(
    const OpenSpaceNode& current_node, OpenSpaceNode* final_node) {
  std::optional<PathSegment> path_from_current_to_final =
      rs_path_generator_.GenerateBestRSPathSegment(
          current_node.state, final_node->state,
          grid_.motion_plan_problem().car_model_with_shape(),
          &(final_node->costing_info));

  if (!path_from_current_to_final.has_value()) {
    return false;
  }

  // Update final node info.
  final_node->path_from_parent = std::move(path_from_current_to_final.value());

  if (!ValidateNode(*final_node)) {
    // The generated RS path has collision or out of grid bound, cannot be used.
    return false;
  }

  final_node->parent_key = current_node.key;
  // Update the final node path cost with final node cost computed in RS curve
  // selection.
  final_node->costing_info.set_path_cost(
      current_node.costing_info.path_cost() +
      final_node->costing_info.ComputeNodeCost());
  return true;
}

std::optional<OpenSpacePathSearchResult>
HybridAStarSearch::RetrievePathSearchResult(const OpenSpaceNode& start_node,
                                            OpenSpaceNode* final_node) const {
  if (final_node == nullptr || start_node.key.IsNullKey() ||
      final_node->key == start_node.key) {
    LOG(ERROR) << "Start / end node is null, or start and end node at the same "
                  "place, search failed.";

    return std::nullopt;
  }

  // Collect the searched path.
  std::optional<PathSegment> searched_path =
      RetrieveSearchedPath(start_node, final_node);

  if (!searched_path.has_value() || searched_path.value().empty()) {
    LOG(ERROR) << "Search path collection failed";
    return std::nullopt;
  }

  // Partition the last path segment for the searched path.
  std::vector<MotionSegment> motion_segments =
      PartitionPathToMotionSegments(std::move(searched_path.value()));

  return OpenSpacePathSearchResult(std::move(motion_segments));
}

std::optional<PathSegment> HybridAStarSearch::RetrieveSearchedPath(
    const OpenSpaceNode& start_node, OpenSpaceNode* final_node) const {
  PathSegment searched_path;
  // Use heuristic distance to estimate the searched path state size.
  searched_path.Reserve(kEstimatedSearchedPathStateSize);
  OpenSpaceNode* current_node_ptr = final_node;
  DCHECK(current_node_ptr != nullptr);
  while (!current_node_ptr->parent_key.IsNullKey()) {
    PathSegment& path_from_parent = current_node_ptr->path_from_parent;
    OpenSpaceNode* parent_node_ptr =
        grid_.GetMutableOpenSpaceNode(current_node_ptr->parent_key);
    if (parent_node_ptr == nullptr) {
      LOG(ERROR) << "Parent not found, retrieve path failed";
      return std::nullopt;
    }
    // For the states that motion mode changed, we populate two nodes to the
    // searched path, the two nodes have the same position but different
    // motion mode / steering. This helps the path partition in later stage.
    const int tail_idx =
        parent_node_ptr->key == start_node.key ? 0
        : (parent_node_ptr->path_from_parent.back_state().is_forward ==
           current_node_ptr->path_from_parent.front_state().is_forward)
            ? 1
            : 0;
    for (int idx = path_from_parent.size() - 1; idx >= tail_idx; --idx) {
      searched_path.Append(
          std::move(path_from_parent.mutable_path_states()[idx]));
    }
    current_node_ptr = parent_node_ptr;
  }
  if (current_node_ptr->key != start_node.key) {
    LOG(ERROR) << "The retrieved end node is not the start node, Failed to "
                  "retrieve path.";
    return std::nullopt;
  }
  if (searched_path.empty()) {
    LOG(ERROR) << "The result path is empty, failed to retrieve path.";
    return std::nullopt;
  }

  searched_path.Reverse();
  return std::move(searched_path);
}

const OpenSpaceNode& HybridAStarSearch::PrepareStartNode() const {
  const RobotStateSnapshot& curr_snapshot =
      grid_.motion_plan_problem().current_state_snapshot();
  const std::optional<MotionModeGuidance>& start_pose_motion_mode_guidance =
      base::CheckAndGetValue(grid_.motion_plan_problem().start_pose_info)
          .motion_mode_guidance;
  const bool is_preset_init_motion_mode_backward =
      start_pose_motion_mode_guidance.has_value() &&
      start_pose_motion_mode_guidance->guidance_motion_mode ==
          planner::pb::MotionMode::BACKWARD;
  // The ego initial motion mode is set based on the preset init motion mode to
  // make sure ego prefer the init motion mode during search.
  const PathState start_state(
      curr_snapshot.rear_axle_position(),
      math::NormalizeMinusPiToPi(curr_snapshot.heading()),
      curr_snapshot.steering(),
      /*is_forward_in=*/!is_preset_init_motion_mode_backward);

  OpenSpaceNode search_start_node(
      start_state,
      /*node_key=*/grid_.GetOpenSpaceNodeKeyFromPathState(start_state),
      /*parent_key=*/OpenSpaceNodeKey(), /*path_from_parent=*/PathSegment());
  search_start_node.costing_info.set_path_cost(0.0);

  return *DCHECK_NOTNULL(
      grid_.EmplaceOpenSpaceNode(std::move(search_start_node)));
}

bool HybridAStarSearch::ValidateNode(const OpenSpaceNode& node) const {
  // For the search start node, only check the node state.
  if (node.path_from_parent.empty()) {
    if (!grid_.IsWithinGridArea(node.state.position) ||
        HasCollision(node.state)) {
      return false;
    }
    return true;
  }

  // Check if the node position or node traversed path segment out of search
  // boundary or has collision, skip the parent state in the front of path
  // segment as it is guranteed within grid area and collision-free.
  for (size_t idx = node.path_from_parent.size() - 1; idx > 0; --idx) {
    const PathState& state = node.path_from_parent.path_states()[idx];
    if (!grid_.IsWithinGridArea(state.position) || HasCollision(state)) {
      return false;
    }
  }

  return true;
}

bool HybridAStarSearch::HasCollision(const PathState& state) const {
  const vehicle_model::CarModelWithAxleRectangularShape& car_model_with_shape =
      grid_.motion_plan_problem().car_model_with_shape();
  const double disk_radius =
      vehicle_model::GetDiskRadius(car_model_with_shape.shape_measurement(),
                                   vehicle_model::kDefaultEgoDiskNumber);
  const auto [disks, cached_shape_data] =
      vehicle_model::GetCoveringDiskAndCachedShapeInfo(
          car_model_with_shape.shape_measurement(), state.position.x(),
          state.position.y(), state.heading, disk_radius,
          vehicle_model::kDefaultEgoDiskNumber);
  for (const vehicle_model::DiskInfo& disk_info : disks) {
    const math::geometry::Point2d& disk_center = disk_info.disk.center();
    if (!grid_.IsWithinGridArea(disk_center)) {
      return true;
    }
    if (!grid_
             .GetDominantOccupancyClearanceInfo(disk_center,
                                                disk_info.disk.radius())
             .is_drivable) {
      return true;
    }
  }
  return false;
}

}  // namespace open_space

}  // namespace planner
