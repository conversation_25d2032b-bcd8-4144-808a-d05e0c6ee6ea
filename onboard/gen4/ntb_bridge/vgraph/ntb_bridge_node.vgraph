# proto-file: varch/protos/vnode/vgraph_conf.proto
# proto-message: VForestConfig

vgraph {
  mode: kReality
  scheduler {
    worker_num: 25
    group: [
      {
        name: "ntb_bridge_group"
        workers: "0"
      },
      {
        name: "publish_group"
        workers: "0-24"
      }
    ]
  }

  vnode: [
    {
      library: "${VOY_LIB_DIR}/libntb_bridge_vnode.so"
      clazz: "NtbBridge"
      name: "ntb_bridge_vnode"
      sub {
        mode: kCallOnce,
      }
      pub: [
        {
          name: "raw_camera_telescopic_102"
          topic: "/raw_camera_telescopic_102"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_103"
          topic: "/raw_camera_telescopic_103"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_104"
          topic: "/raw_camera_telescopic_104"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_105"
          topic: "/raw_camera_telescopic_105"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_106"
          topic: "/raw_camera_telescopic_106"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_107"
          topic: "/raw_camera_telescopic_107"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_102"
          topic: "/camera_video_frame_102"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_103"
          topic: "/camera_video_frame_103"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_104"
          topic: "/camera_video_frame_104"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_105"
          topic: "/camera_video_frame_105"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_106"
          topic: "/camera_video_frame_106"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_107"
          topic: "/camera_video_frame_107"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_101"
          topic: "/raw_camera_telescopic_101"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_telescopic_tl"
          topic: "/raw_camera_telescopic_tl"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_gen4_telescopic_short_1"
          topic: "/raw_camera_gen4_telescopic_short_1"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_gen4_telescopic_short_2"
          topic: "/raw_camera_gen4_telescopic_short_2"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_gen4_telescopic_short_3"
          topic: "/raw_camera_gen4_telescopic_short_3"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_gen4_telescopic_short_4"
          topic: "/raw_camera_gen4_telescopic_short_4"
          transport_option: { type: kUDP }
        },
        {
          name: "raw_camera_gen4_infrared_1"
          topic: "/raw_camera_gen4_infrared_1"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_101"
          topic: "/camera_video_frame_101"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_video_frame_tl"
          topic: "/camera_video_frame_tl"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_gen4_video_frame_short_1"
          topic: "/camera_gen4_video_frame_short_1"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_gen4_video_frame_short_2"
          topic: "/camera_gen4_video_frame_short_2"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_gen4_video_frame_short_3"
          topic: "/camera_gen4_video_frame_short_3"
          transport_option: { type: kUDP }
        },
        {
          name: "camera_gen4_video_frame_short_4"
          topic: "/camera_gen4_video_frame_short_4"
          transport_option: { type: kUDP }
        }
      ]

      group: "ntb_bridge_group"
      prio: 11

      disable_recurring_frame: true
    }
  ]
}
