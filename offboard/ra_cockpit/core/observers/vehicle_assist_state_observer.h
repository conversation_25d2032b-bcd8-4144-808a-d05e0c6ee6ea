#ifndef OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ASSIST_STATE_OBSERVER_H_
#define OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ASSIST_STATE_OBSERVER_H_

#include <functional>
#include <memory>

#include "../data_observer.h"
#include "teleassist_protos/teleassist.pb.h"

namespace remote_assist {

class VehicleAssistStateObserver : public IDataObserver {
 public:
  using VehicleAssistStateCallback = std::function<void(
      std::shared_ptr<teleassist::pb::VehicleAssistState>, DataChannel)>;

  explicit VehicleAssistStateObserver(VehicleAssistStateCallback callback);

  bool IsInterestedIn(
      const teleassist::pb::TeleassistRequest& message) override;

  std::shared_ptr<google::protobuf::Message> OnMessage(
      const teleassist::pb::TeleassistRequest& message,
      DataChannel data_channel) override;

 private:
  VehicleAssistStateCallback vehicle_assist_state_callback_;
};

}  // namespace remote_assist

#endif  // OFFBOARD_RA_COCKPIT_CORE_OBSERVERS_VEHICLE_ASSIST_STATE_OBSERVER_H_
